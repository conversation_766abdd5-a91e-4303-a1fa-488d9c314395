﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@using Newtonsoft.Json;
@using ContinuityPatrol.Domain.ViewModels.WorkflowCategoryModel
@using ContinuityPatrol.Shared.Services.Helper;

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/color_pallete.css" rel="stylesheet" />
<link href="~/css/workflowconfiguration.css" rel="stylesheet" />
<link href="~/lib/jquery.steps/jquery.steps.css" rel="stylesheet" />
<link href="~/css/wizard.css" rel="stylesheet" />
<link href="~/css/builder.css" rel="stylesheet" />
<link href="~/lib/formeo/formeo.min.css" rel="stylesheet" />

<div class="page-content">
    <div class="row g-2">
        <div class="col-12 mt-2">
            <div class="header">
                <h6 class="page_title"><i class="cp-action-builder"></i><span>Action Builder</span></h6>
                <div class="action_catagory_toggle">
                    <ul class="mb-0 list-group list-group-horizontal gap-2">
                        <li class="d-flex align-items-center"><span class="badge common-bg circle me-1 p-2"><span class="hidden"></span></span>Common</li>
                        <li class="d-flex align-items-center"><span class="badge droperation-bg circle me-1 p-2"><span class="hidden"></span></span>DR Operation</li>
                        <li class="d-flex align-items-center"><span class="badge monitoring-bg circle me-1 p-2"><span class="hidden"></span></span>Monitoring</li>
                        <li class="d-flex align-items-center"><span class="badge resiliency-ready-bg circle me-1 p-2"><span class="hidden"></span></span>Resiliency Ready</li>
                    </ul>
                </div>
                <div class="action_catagory_toggle">
                    <div class="d-flex gap-3 align-items-baseline">
                        <button type="button" id="exportAction" class="btn btn-primary btn-sm rounded-2 d-none">Export</button>
                        <div class="d-none" id="d_selectall">
                            <div class="form-check d-flex align-items-center gap-1">
                                <label class="form-check-label">
                                    Select All
                                </label>
                                <input class="form-check" id="selectAllExport" type="checkbox" value="" checked>
                            </div>
                        </div>
                        <div class="input-group " style="width:13rem; ">
                            <select class="form-control  select2-icon" id="selectionType" data-live-search="true" name="icon">
                                <option data-icon="cp-action-builder" value="all">All</option>
                                <option data-icon="cp-dr-readiness" value="Readyness">Resiliency Readiness</option>
                                <option data-icon="cp-Monitor" value="Monitoring">Monitoring</option>
                                <option data-icon="cp-dr-calendar" value="Operation">DR Operation</option>
                                <option data-icon="cp-summary" value="Common">Common</option>
                            </select>
                        </div>
                        <div class="input-group me-2 w-auto ">
                            <input type="search" id="search-inp" class="form-control search-inp-type bg-transparent" placeholder="Search" />
                            <span class="input-group-text pe-0"><i class="cp-search"></i></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-xxl-2">
            <div class="card Card_Design_None mb-0">
                <div class="p-2 pt-1 card-header d-flex justify-content-between align-items-center">
                    <div class="input-group border-light-subtle">
                        <input type="text" id="search-inpcategory" class="form-control search-inp-category ps-0" type="search" placeholder="Search Action Type" />
                        <div class="input-group-text p-0"><i class="cp-search" title="Search"></i></div>
                        <div class="input-group-text px-2">
                            <span role="button" id="collapse"><i title="Collapse" class="cp-circle-rightarrow fs-5 text-primary"></i></span>
                        </div>
                        <div class="input-group-text p-0">
                            <div class="dropdown dropend">
                                <span role="button" data-bs-toggle="dropdown" class="categoryplus" aria-expanded="false" data-bs-auto-close="outside">
                                    <i class="cp-circle-plus fs-5 text-primary" title="Add Category"></i>
                                </span>
                                <div class="dropdown-menu py-0" data-bs-dismiss="modal" style="width:18rem; border:1px solid #e9e9e9 !important;">
                                    <h6 class="dropdown-header px-2">Add Category</h6>
                                    <form class="p-2 pt-0">
                                        
                                        <div class="form-group mb-2">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="cp-name"></i></span>
                                                <input type="text" id="categoryName" onkeyup="categoryInputvalidation(this)" onkeydown="categoryInputvalidation(this)" class="form-control me-2" maxlength="100" placeholder="Enter Category Name" />
                                                <span class="input-group-text" title="Select Category Icon" role="button" data-bs-toggle="collapse" id="collapseExampleImage" href="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
                                                    <i class="cp-images" id="imageSelected"></i>
                                                </span>

                                            </div>
                                            <span id="Name-error" style="color:red"></span>
                                        </div>
                                        <div class="collapse" id="collapseExample">
                                            <div class="form-label">Category Icon</div>
                                            <div class="Category_Icon" style="height: calc(71vh - 150px);overflow: auto;">
                                                <table class="table table-bordered mb-1">
                                                    <tbody>
                                                        <tr>
                                                            <td><i title="Cloud" class="cp-cloud custom-cursor-on-hover" cursorshover="true"></i></td>
                                                            <td><i title="Amazon" class="cp-amazon custom-cursor-on-hover"></i></td>
                                                            <td><i title="Softlayer" class="cp-java-soft-layers custom-cursor-on-hover"></i></td>
                                                            <td><i title="Database" class="cp-data custom-cursor-on-hover"></i></td>
                                                            <td><i title="MSSQL" class="cp-mssql custom-cursor-on-hover"></i></td>
                                                            <td><i title="MYSQL" class="cp-mysql custom-cursor-on-hover" cursorshover="true"></i></td>
                                                            <td><i title="Oracle" class="cp-oracle custom-cursor-on-hover"></i></td>
                                                        </tr>
                                                        <tr>
                                                            <td><i title="Postgres" class="cp-postgres custom-cursor-on-hover" cursorshover="true"></i></td>
                                                            <td><i title="IBM" class="cp-IBM custom-cursor-on-hover" cursorshover="true"></i></td>
                                                            <td><i title="Files" class="cp-folder-file custom-cursor-on-hover"></i></td>
                                                            <td><i title="Hypervisor" class="cp-workflow-execution custom-cursor-on-hover"></i></td>
                                                            <td><i title="Windows" class="cp-windows custom-cursor-on-hover" cursorshover="true"></i></td>
                                                            <td><i title="Exchange" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
                                                            <td><i title="Network" class="cp-network custom-cursor-on-hover" cursorshover="true"></i></td>
                                                        </tr>
                                                        <tr>
                                                            <td><i title="Goldengate" class="cp-goldengate custom-cursor-on-hover" cursorshover="true"></i></td>
                                                            <td><i title="Firewall" class="cp-firewall custom-cursor-on-hover" cursorshover="true"></i></td>
                                                            <td><i title="Router" class="cp-router custom-cursor-on-hover" cursorshover="true"></i></td>
                                                            <td><i title="Switch" class="cp-switch custom-cursor-on-hover"></i></td>
                                                            <td><i title="OS" class="cp-os-type custom-cursor-on-hover"></i></td>
                                                            <td><i title="Linux" class="cp-linux" cursorshover="true"></i></td>
                                                            <td><i title="HP" class="cp-hp custom-cursor-on-hover"></i></td>
                                                        </tr>
                                                        <tr>
                                                            <td><i title="Replication" class="cp-replication-rotate custom-cursor-on-hover"></i></td>
                                                            <td><i title="rsync" class="cp-rsync custom-cursor-on-hover"></i></td>
                                                            <td><i title="Veeam" class="cp-Veeam custom-cursor-on-hover"></i></td>
                                                            <td><i title="Storage" class="cp-stand-storage custom-cursor-on-hover"></i></td>
                                                            <td><i title="hds" class="cp-hds custom-cursor-on-hover"></i></td>
                                                            <td><i title="Netapp" class="cp-netapp" cursorshover="true"></i></td>
                                                            <td><i title="systemmanagementtool" class="cp-system-management-tool" cursorshover="true"></i></td>
                                                        </tr>

                                                        <tr>
                                                            <td><i title="Microsoft" class="cp-microsoft custom-cursor-on-hover"></i></td>
                                                            <td><i title="sun-ilom" class="cp-sun-ilom custom-cursor-on-hover"></i></td>
                                                            <td><i title="veritas cluster" class="cp-veritas-cluster custom-cursor-on-hover"></i></td>
                                                            <td><i title="virtualization" class="cp-virtualization_new custom-cursor-on-hover"></i></td>
                                                            <td><i title="Aix" class="cp-aix custom-cursor-on-hover" cursorshover="true"></i></td>
                                                            <td><i title="solaris" class="cp-oracle-solaris custom-cursor-on-hover" cursorshover="true"></i></td>
                                                            <td><i title="vmware" class="cp-vmware custom-cursor-on-hover"></i></td>
                                                        </tr>
                                                        <tr>
                                                            <td><i title="Web" class="cp-web custom-cursor-on-hover"></i></td>
                                                            <td><i title="power-cli" class="cp-power-cli custom-cursor-on-hover"></i></td>
                                                            <td><i title="Workflow" class="cp-workflow custom-cursor-on-hover"></i></td>
                                                            <td><i title="EMC" class="cp-EMC custom-cursor-on-hover"></i></td>
                                                            <td><i title="oracle-ops" class="cp-oracle-ops custom-cursor-on-hover" cursorshover="true"></i></td>
                                                            <td><i title="H3PAR" class="cp-H3PAR custom-cursor-on-hover"></i></td>
                                                            <td><i title="Double Take" class="cp-double-take custom-cursor-on-hover"></i></td>
                                                        </tr>
                                                        <tr>
                                                            <td><i title="Infoblox" class="cp-soft-layer custom-cursor-on-hover"></i>        </td>
                                                            <td><i title="Solution" class="cp-solution custom-cursor-on-hover"></i></td>
                                                            <td><i title="Token" class="cp-token custom-cursor-on-hover"></i></td>
                                                            <td><i title="General" class="cp-general custom-cursor-on-hover"></i></td>
                                                            <td><i title="Create-Logger" class="cp-create-Logger custom-cursor-on-hover"></i></td>
                                                            <td><i title="Conditional" class="cp-conditional custom-cursor-on-hover"></i></td>
                                                            <td><i title="Loop" class="cp-loop custom-cursor-on-hover"></i></td>
                                                        </tr>
                                                        <tr>
                                                            <td><i title="System" class="cp-system custom-cursor-on-hover"></i></td>
                                                            <td><i title="Delay" class="cp-delay custom-cursor-on-hover"></i></td>
                                                            <td><i title="If" class="cp-if custom-cursor-on-hover"></i></td>
                                                            <td><i title="circle-switch" class="cp-circle-switch custom-cursor-on-hover"></i></td>
                                                            <td><i title="data-source" class="cp-data-source custom-cursor-on-hover"></i></td>
                                                            <td><i title="Rule" class="cp-rule custom-cursor-on-hover"></i></td>
                                                            <td><i title="Wait" class="cp-wait custom-cursor-on-hover"></i></td>
                                                        </tr>
                                                        <tr>
                                                            <td><i title="Common" class="cp-common custom-cursor-on-hover"></i></td>
                                                            <td><i title="Security" class="cp-security custom-cursor-on-hover"></i></td>
                                                            <td><i title="Powershell" class="cp-powershell custom-cursor-on-hover"></i></td>
                                                            <td><i title="Conversion" class="cp-conversion custom-cursor-on-hover"></i></td>
                                                            <td><i title="Script" class="cp-script custom-cursor-on-hover"></i></td>
                                                            <td><i title="SSH" class="cp-SSH custom-cursor-on-hover"></i></td>
                                                            <td><i title="Connect " class="cp-connect custom-cursor-on-hover"></i></td>
                                                        </tr>
                                                        <tr>
                                                            <td><i title="Error-handing " class="cp-error-handing custom-cursor-on-hover"></i></td>
                                                            <td><i title="Stringutility" class="cp-stringutility custom-cursor-on-hover"></i></td>
                                                            <td><i title="Wmi" class="cp-wmi custom-cursor-on-hover"></i></td>
                                                            <td><i title="Log Files" class="cp-log-file-name custom-cursor-on-hover"></i></td>
                                                            <td><i title="Configure Settings" class="cp-configure-settings custom-cursor-on-hover"></i></td>
                                                            <td><i title="prsite" class="cp-prsite custom-cursor-on-hover"></i></td>
                                                            <td><i title="Mongo DB" class="cp-mongo-db"></i></td>
                                                        </tr>
                                                        <tr>
                                                            <td><i title="Nutanix" class="cp-nutanix custom-cursor-on-hover"></i></td>
                                                            <td><i title="IBM-AIX" class="cp-IBM-AIX custom-cursor-on-hover"></i></td>
                                                            <td><i title="Mainframe" class="cp-server-cloud custom-cursor-on-hover"></i></td>
                                                            <td><i title="EBDR" class="cp-ebdr custom-cursor-on-hover"></i></td>  
                                                        </tr>
                                                      
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="collapse multi-collapse" id="multiCollapseExample1">
                                            <table class="table mb-1">
                                                <tbody id="colorTable">
                                                    <tr>
                                                        <td>
                                                            <input type="radio" name="color" id="red" value="red" />
                                                            <label for="red"><span class="red dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="green" />
                                                            <label for="green"><span class="green dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="yellow" />
                                                            <label for="yellow"><span class="yellow dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="olive" />
                                                            <label for="olive"><span class="olive dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="orange" />
                                                            <label for="orange"><span class="orange dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="teal" />
                                                            <label for="teal"><span class="teal dynamicColor"></span></label>
                                                        </td>

                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <input type="radio" name="color" id="blue" />
                                                            <label for="blue"><span class="blue dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="violet" />
                                                            <label for="violet"><span class="violet dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="purple" />
                                                            <label for="purple"><span class="purple dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="pink" />
                                                            <label for="pink"><span class="pink dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="darkblue" />
                                                            <label for="darkblue"><span class="darkblue dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="pgreen" />
                                                            <label for="pgreen"><span class="pgreen dynamicColor"></span></label>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <input type="radio" name="color" id="skyblue" />
                                                            <label for="skyblue"><span class="skyblue dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="darkred" />
                                                            <label for="darkred"><span class="darkred dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="darkpink" />
                                                            <label for="darkpink"><span class="darkpink dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="green2" />
                                                            <label for="green2"><span class="green2 dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="jupitar" />
                                                            <label for="jupitar"><span class="jupitar dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="mustrad" />
                                                            <label for="mustrad"><span class="mustrad dynamicColor"></span></label>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <input type="radio" name="color" id="melon" />
                                                            <label for="melon"><span class="melon dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="darkgrey" />
                                                            <label for="darkgrey"><span class="darkgrey dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="violet_lite" />
                                                            <label for="violet_lite"><span class="violet_lite dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="black" />
                                                            <label for="black"><span class="black dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="chacolate" />
                                                            <label for="chacolate"><span class="chacolate dynamicColor"></span></label>
                                                        </td>
                                                        <td>
                                                            <input type="radio" name="color" id="pasigreen" />
                                                            <label for="pasigreen"><span class="pasigreen dynamicColor"></span></label>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="text-end mt-4">
                                            <button type="submit" class="btn btn-sm btn-secondary" id="CancelCategory">Cancel</button>
                                            <button type="submit" class="btn btn-sm btn-primary" id="CreateCategory">Save</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        @* <div class="input-group-text px-2"><span role="button"><i title="Collapse" class="cp-import fs-5 text-primary"></i></span></div> *@
                    </div>
                </div>
                <div class="card-body pt-0 ActionBuilder-Tree mb-2 p-2">
            
                    <div id="workflowList">
                        @foreach (var workflow in Model)
                        {
                            var properties = JsonConvert.DeserializeObject<dynamic>(@workflow.Properties);
                            <details class="ms-0">
                                <summary id="@workflow.Id" class="categorysummary d-flex px-2" parentId="">
                                    <div class="d-flex align-items-center justify-content-between w-100 searchData">
                                        <div class="d-flex gap-1 align-items-center w-100"><i class="@properties.icon"></i><div class="text-truncate" style="width:200px;">@workflow.Name</div></div>
                                        <div class="dropdown">
                                            <i class="cp-horizontal-dots float-end" data-bs-toggle="dropdown" title="More" onclick="WorkflowCategoryList()" aria-expanded="@properties.expanded" role="button"></i>
                                            <ul class="dropdown-menu">
                                                <li id="@workflow.Id" nodeId="@properties.nodeId" parentId="@properties.nodeId" name="@workflow.Name" class="AddSubCategory" title="Add Sub Category" type="Add"><a class="dropdown-item" href="#"><i class="cp-add me-2"></i>Add Sub-Category</a></li>
                                                <li id="@workflow.Id" nodeId="@properties.nodeId" icon="@properties.icon" color="@properties.color" parentId="@properties.nodeId" name="@workflow.Name" class="EditCategory" title="Edit Category" type="Update"><a class="dropdown-item" href="#"><i class="cp-edit me-2"></i>Edit Category</a></li>
                                                <li class="delete-button deleteCategoryList" data-WorkflowCategory-id="@workflow.Id" data-WorkflowCategory-name="@workflow.Name" data-bs-backdrop="static" data-bs-toggle="modal" data-bs-target="#DeleteModal"><a class="dropdown-item" href="#"><i class="cp-Delete me-2"></i>Delete Category</a></li>

                                            </ul>
                                        </div>
                                    </div>
                                </summary>
                                @foreach (var propertiesData in @properties.children)
                                {
                                 
                                    <details>
                                        <summary id="@workflow.Id" class="categorysummary d-flex px-2" parentId="@propertiesData.parentId">
                                            <div class="d-flex align-items-center justify-content-between w-100 searchData">
                                                <div class="d-flex gap-1 align-items-center w-100">
                                                    <i class="@propertiesData.icon"></i>
                                                    <div class="text-truncate" style="width:200px;">@propertiesData.title</div>
                                                </div>
                                                <div class="dropdown">
                                                    <i class="cp-horizontal-dots float-end" data-bs-toggle="dropdown" onclick="WorkflowCategoryList()" aria-expanded="true" role="button"></i>
                                                    <ul class="dropdown-menu">
                                                        <li id="@workflow.Id" nodeId="@propertiesData.nodeId" parentId="@propertiesData.parentId" class="AddchidSubCategory" name="@workflow.Name" title="Add Sub Category" type="Add"><a class="dropdown-item" href="#"><i class="cp-add me-2"></i>Add Sub-Category</a></li>
                                                        <li id="@workflow.Id" nodeId="@propertiesData.nodeId" parentId="@propertiesData.parentId" icon="@propertiesData.icon" parentName="@workflow.Name" name="@propertiesData.title" class="EditSubCategory" title="Edit Sub Category" type="Update"><a class="dropdown-item" href="#"><i class="cp-edit me-2"></i>Edit Sub-Category</a></li>
                                                        <li class="DeleteSubCategory" id="@workflow.Id" nodeId="@propertiesData.nodeId" parentId="@propertiesData.parentId" data-WorkflowCategory-id="@propertiesData.id" parentName="@workflow.Name" data-WorkflowCategory-name="@propertiesData.title"><a class="dropdown-item" href="#"><i class="cp-Delete me-2"></i>Delete Sub-Category</a></li>
                                                        <li class="importCategoryActionNode" importName="category" parentId="@propertiesData.parentId" referenceId="@workflow.Id" nodeId="@propertiesData.nodeId"><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#ImportActionModal"><i class="cp-import me-2"></i>Import</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </summary>
                                        @foreach (var childPropertiesData in @propertiesData.children)
                                        {
                                            <div id="@childPropertiesData.id" class="categorysummary" parentId="@childPropertiesData.parentId" style="margin-left:18px;">
                                                <div id="@childPropertiesData.id" nodeId="@childPropertiesData.nodeId" class="d-flex justify-content-between align-items-center searchData" parentId="@childPropertiesData.parentId">
                                                    <span role="button" id="@childPropertiesData.id" nodeId="@childPropertiesData.nodeId" parentId="@childPropertiesData.parentId" class="categoryactionlist"><i class="@childPropertiesData.icon me-0 "></i>@childPropertiesData.title</span>
                                                    <div class="dropdown me-2">
                                                        <i class="cp-horizontal-dots float-end" data-bs-toggle="dropdown" onclick="WorkflowCategoryList()" aria-expanded="true" role="button"></i>
                                                        <ul class="dropdown-menu">
                                                            <li id="@workflow.Id" nodeId="@childPropertiesData.nodeId" parentId="@childPropertiesData.parentId" class="AddAction" data-bs-toggle="modal" data-bs-target="#ActionbuilderconfigModal"><a class="dropdown-item" href="#"><i class="cp-add me-2"></i>Add Action</a></li>
                                                            <li id="@workflow.Id" nodeId="@childPropertiesData.nodeId" parentId="@childPropertiesData.parentId" parentName="@workflow.Name" icon="@childPropertiesData.icon" name="@childPropertiesData.title" class="EditchidSubCategory" title="Edit Sub Category" type="Update"><a class="dropdown-item" href="#"><i class="cp-edit me-2"></i>Edit Sub-Category</a></li>
                                                            <li id="@workflow.Id" nodeId="@childPropertiesData.nodeId" parentId="@childPropertiesData.parentId" data-WorkflowCategory-id="@childPropertiesData.id" parentName="@workflow.Name" class="DeleteSubChildCategory" data-WorkflowCategory-name="@childPropertiesData.title"><a class="dropdown-item" href="#"><i class="cp-Delete me-2"></i>Delete Sub-Category</a></li>
                                                            <li referenceId="@workflow.Id" parentNodeId="@childPropertiesData.parentId" nodeId="@childPropertiesData.nodeId" class="exportCategoryAction" parentId="@properties.nodeId" categoryIcon="@childPropertiesData.icon" mainParentName="@workflow.Name" parentName="@propertiesData.title" name="@childPropertiesData.title"><a class="dropdown-item" href="#"><i class="cp-export me-2"></i>Export Category Action</a></li>
                                                            <li onclick="selectExportActionListView(this)" referenceId="@workflow.Id" nodeId="@childPropertiesData.nodeId" parentId="@childPropertiesData.parentId" mainParentName="@workflow.Name" parentName="@propertiesData.title" name="@childPropertiesData.title"><a class="dropdown-item" href="#"><i class="cp-export me-2"></i>Select Export Action</a></li>
                                                            <li class="importActionNode" nodeId="@childPropertiesData.nodeId" importName="action"><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#ImportActionModal"><i class="cp-import me-2"></i>Import</a></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                        }
                                    </details>
                                }
                            </details>
                        }
                    </div>
                    <div id="workflowListNoData"></div>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card Card_Design_None mb-0">
                <div class="card-body p-2 my-2" style=" height: calc(100vh - 133px);overflow-y: auto;">
                    <div class="row row-cols-2 row-cols-lg-4 row-cols-xl-5 row-cols-xxl-6 actionTypelist g-2">
                    </div>
                    <div id="actionTypeListImage"></div>
                </div>
            </div>
        </div>
    </div>
</div>




<!-- category Modal -->
<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="AddCategory" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-action-builder"></i><span id="TitleHeaderCategory">Add Sub-Category</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                   
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-workflow-configuration"></i></span>
                        <input class="form-control me-2" type="text" id="CategoryNameInput" onkeyup="subcategoryInputvalidation(this)" onkeydown="subcategoryInputvalidation(this)" maxlength="100" placeholder="Enter Category Name" />
                        <span class="input-group-text" data-bs-toggle="collapse" id="collapseIconImage" href="#collapseIcon" aria-expanded="false" aria-controls="collapseIcon">
                            <i class="imageSubSelected cp-images" id="imageSubSelected"></i>
                        </span>
                     
                    </div>
                    <span id="namecategory-error" style="color:red"></span>
                </div>
                <div class="collapse mb-2" id="collapseIcon">
                    <div class="form-label">Category Icon</div>
                    <div class="Category_Icon">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <td><i title="Cloud" class="cp-cloud custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Amazon" class="cp-amazon custom-cursor-on-hover"></i></td>
                                    <td><i title="Softlayer" class="cp-java-soft-layers custom-cursor-on-hover"></i></td>
                                    <td><i title="Database" class="cp-data custom-cursor-on-hover"></i></td>
                                    <td><i title="MSSQL" class="cp-mssql custom-cursor-on-hover"></i></td>
                                    <td><i title="MYSQL" class="cp-mysql custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Oracle" class="cp-oracle custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Postgres" class="cp-postgres custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="IBM" class="cp-IBM custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Files" class="cp-folder-file custom-cursor-on-hover"></i></td>
                                    <td><i title="Hypervisor" class="cp-workflow-execution custom-cursor-on-hover"></i></td>
                                    <td><i title="Windows" class="cp-windows custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Exchange" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Network" class="cp-network custom-cursor-on-hover" cursorshover="true"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Goldengate" class="cp-goldengate custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Infoblox" class="cp-infoblox custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Router" class="cp-router custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Switch" class="cp-switch custom-cursor-on-hover"></i></td>
                                    <td><i title="OS" class="cp-os-type custom-cursor-on-hover"></i></td>
                                    <td><i title="Linux" class="cp-linux" cursorshover="true"></i></td>
                                    <td><i title="HP" class="cp-hp custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Replication" class="cp-replication-rotate custom-cursor-on-hover"></i></td>
                                    <td><i title="rsync" class="cp-rsync custom-cursor-on-hover"></i></td>
                                    <td><i title="Veeam" class="cp-Veeam custom-cursor-on-hover"></i></td>
                                    <td><i title="Storage" class="cp-stand-storage custom-cursor-on-hover"></i></td>
                                    <td><i title="hds" class="cp-hds custom-cursor-on-hover"></i></td>
                                    <td><i title="Netapp" class="cp-netapp" cursorshover="true"></i></td>
                                    <td><i title="systemmanagementtool" class="cp-system-management-tool" cursorshover="true"></i></td>
                                </tr>

                                <tr>
                                    <td><i title="Microsoft" class="cp-microsoft custom-cursor-on-hover"></i></td>
                                    <td><i title="sun-ilom" class="cp-sun-ilom custom-cursor-on-hover"></i></td>
                                    <td><i title="veritas cluster" class="cp-veritas-cluster custom-cursor-on-hover"></i></td>
                                    <td><i title="virtualization" class="cp-virtualization custom-cursor-on-hover"></i></td>
                                    <td><i title="Aix" class="cp-aix custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="solaris" class="cp-oracle-solaris custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="vmware" class="cp-vmware custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Web" class="cp-web custom-cursor-on-hover"></i></td>
                                    <td><i title="power-cli" class="cp-power-cli custom-cursor-on-hover"></i></td>
                                    <td><i title="Workflow" class="cp-workflow custom-cursor-on-hover"></i></td>
                                    <td><i title="Exchange" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="oracle-ops" class="cp-oracle-ops custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Firewall" class="cp-firewall custom-cursor-on-hover"></i></td>
                                    <td><i title="EMC" class="cp-EMC custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Infoblox" class="cp-soft-layer custom-cursor-on-hover"></i>        </td>
                                    <td><i title="Solution" class="cp-solution custom-cursor-on-hover"></i></td>
                                    <td><i title="Token" class="cp-token custom-cursor-on-hover"></i></td>
                                    <td><i title="General" class="cp-general custom-cursor-on-hover"></i></td>
                                    <td><i title="Create-Logger" class="cp-create-Logger custom-cursor-on-hover"></i></td>
                                    <td><i title="Conditional" class="cp-conditional custom-cursor-on-hover"></i></td>
                                    <td><i title="Loop" class="cp-loop custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="System" class="cp-system custom-cursor-on-hover"></i></td>
                                    <td><i title="Delay" class="cp-delay custom-cursor-on-hover"></i></td>
                                    <td><i title="If" class="cp-if custom-cursor-on-hover"></i></td>
                                    <td><i title="circle-switch" class="cp-circle-switch custom-cursor-on-hover"></i></td>
                                    <td><i title="data-source" class="cp-data-source custom-cursor-on-hover"></i></td>
                                    <td><i title="Rule" class="cp-rule custom-cursor-on-hover"></i></td>
                                    <td><i title="Wait" class="cp-wait custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Common" class="cp-common custom-cursor-on-hover"></i></td>
                                    <td><i title="Security" class="cp-security custom-cursor-on-hover"></i></td>
                                    <td><i title="Powershell" class="cp-powershell custom-cursor-on-hover"></i></td>
                                    <td><i title="Conversion" class="cp-conversion custom-cursor-on-hover"></i></td>
                                    <td><i title="Script" class="cp-script custom-cursor-on-hover"></i></td>
                                    <td><i title="SSH" class="cp-SSH custom-cursor-on-hover"></i></td>
                                    <td><i title="Connect " class="cp-connect custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Error-handing " class="cp-error-handing custom-cursor-on-hover"></i></td>
                                    <td><i title="Stringutility" class="cp-stringutility custom-cursor-on-hover"></i></td>
                                    <td><i title="Wmi" class="cp-wmi custom-cursor-on-hover"></i></td>
                                    <td><i title="Log Files" class="cp-log-file-name custom-cursor-on-hover"></i></td>
                                    <td><i title="Configure Settings" class="cp-configure-settings custom-cursor-on-hover"></i></td>
                                    <td><i title="prsite" class="cp-prsite custom-cursor-on-hover"></i></td>
                                    <td><i title="Mongo DB" class="cp-mongo-db"></i></td>

                                </tr>
                                <tr>
                                    <td><i title="Nutanix" class="cp-nutanix custom-cursor-on-hover"></i></td>
                                    <td><i title="IBM-AIX" class="cp-IBM-AIX custom-cursor-on-hover"></i></td>
                                    <td><i title="Mainframe" class="cp-fal-server custom-cursor-on-hover"></i></td>
                                    <td><i title="EBDR" class="cp-ebdr custom-cursor-on-hover"></i></td>
                                    <td><i title="Double Take" class="cp-double-take custom-cursor-on-hover"></i></td>
                                    <td><i title="H3PAR" class="cp-H3PAR custom-cursor-on-hover"></i></td>
                                    

                                </tr>
                               
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="collapse multi-collapse" id="multiCollapseIcon">

                    <table class="table table-bordered">
                        <tbody id="editColorTable">
                        </tbody>
                    </table>

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-sm btn-primary" id="TitleFooterCategory">Restore</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="exampleModalToggle" aria-hidden="true" aria-labelledby="exampleModalToggle" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="exampleModalToggleLabel">Modal 1</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Show a second modal and hide this one with the button below.
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" data-bs-target="#exampleModalToggle2" data-bs-toggle="modal">Open second modal</button>
            </div>
        </div>
    </div>
</div>
<!-- command Action Modal -->


<!--Compare Version Modal -->
<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="CompareVersion" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-action-builder"></i><span>Compare Action Builder</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body" >
                <div class="card-group gap-2">
                    <div class="card Card_Design_None">
                        <div class="card-header header">
                            <span>Primary Version</span>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    Select Primary Version
                                </button>
                                <ul class="dropdown-menu">
                                    
                                </ul>
                            </div>
                        </div>
                        <pre class="card-body mb-0 CompareAction-Scroll previousJson" id="previousJson" style="background:black;color:white;">

                            
                        </pre>
                    </div>
                    <div class="card Card_Design_None">
                        <div class="card-header header">
                            <span>Alternative Version 6.0.1</span>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    Select Alternative Version
                                </button>
                                <ul class="dropdown-menu">
                                    
                                </ul>
                            </div>
                        </div>
                        <pre class="card-body  mb-0 CompareAction-Scroll currentJson" id="currentJson" style="background:black;color:white;">
                            
                        </pre>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Restore</button>
            </div>
        </div>
    </div>
</div>

<!-- SaveAs Modal -->
<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="SaveAs" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-action-builder"></i><span>Save As Action</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <div class="form-label">Action Name</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-save"></i></span>
                        <input class="form-control" type="text" onkeyup="saveAsInputvalidation(this)" onkeydown="saveAsInputvalidation(this)" maxlength="100" id="saveAsActionName" placeholder="Enter Action Name" />
                    </div>
                    <span id="actionName-error"></span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" id="restore" class="btn btn-primary">Save As</button>
            </div>
        </div>
    </div>
</div>

<!--Modal Action builder config Modal Start-->
<div class="modal fade" data-bs-backdrop="static" id="ActionbuilderconfigModal" tabindex="-1" aria-labelledby="configureModalLabel" aria-hidden="false">
    <div class="modal-dialog modal-fullscreen modal-dialog-centered wizard-content ">
        <form id="actionRole" class="modal-content">
            <div class="modal-header position-absolute w-100" style="z-index:1;">
                <h6 class="page_title">
                    <i class="cp-action-builder"></i><span>Action Builder Configuration </span>
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body pb-0 p-2" style="max-height: fit-content;">
                <div action="#" class="tab-wizard wizard-circle wizard clearfix example-form ActionWizard">
                    <h6>
                        <span class="step">
                            <i class="cp-administrator"></i>
                        </span>
                        <span class="step_title">
                            Get Input
                        </span>
                    </h6>
                    <h6>
                        <span class="step">
                            <i class="cp-list-prsite1"></i>
                        </span>
                        <span class="step_title">
                            Execute Actions
                        </span>
                    </h6>
                    <h6>
                        <span class="step">
                            <i class="cp-user-hcard"></i>
                        </span>
                        <span class="step_title">
                            Summary
                        </span>
                    </h6>
                    <section>
                        <div class="row row-cols-5">
                            <div class="col">
                                <div class="form-group">
                                    <div class="form-label">Name</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-name"></i></span>
                                        <input class="form-control" id="formBuilderInput" onkeyup="actionInputvalidation(this)" onkeydown="actionInputvalidation(this)" maxlength="100" type="text" placeholder="Enter Action Name" />

                                    </div>
                                    <span id="formbuildname-error" style="color:red"></span>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <div class="form-label">Action Type</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-text"></i></span>
                                        <select class="form-select-modal" id="formBuilderType" title="Some placeholder text...">
                                           
                                            <option data-icon="bi-database" color="common-bg" value="Common">Common</option>
                                            <option data-icon="bi-database" color="resiliency-ready-bg" value="Readyness">Resiliency Readiness</option>
                                            <option data-icon="bi-database" color="monitoring-bg" value="Monitoring">Monitoring</option>
                                            <option data-icon="bi-database" color="droperation-bg" value="Operation">DR Operation</option>

                                        </select>
                                    </div>
                                    <span id="formbuildtype-error" style="color:red"></span>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <div class="form-label">Description <span class="text-light">( Optional )</span></div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-description"></i></span>
                                        <input class="form-control" id="description" placeholder="Enter Description" />
                                    </div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <div class="form-label">Times,Wait <span class="text-light">( Optional )</span></div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-time"></i></span>
                                        <input type="number" class="form-control" min="0" id="timeWait" onkeypress="restrict(e)" onkeyup="restrict(e)" placeholder="Times,Wait" />
                                    </div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <div class="form-label">MS Between Tries,Then <span class="text-light">( Optional )</span></div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-time"></i></span>
                                        <input type="number" class="form-control" min="0" id="msBetweenTries" onkeypress="restrict(e)" onkeyup="restrict(e)" placeholder="MS Between Tries,Then" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="actionForms"></div>
                    </section>
                    <section>
                        <div class="row m-0 g-2">
                            <div class="col-auto mt-0" id="datain" style="width:17%;">
                                <div class="card Card_Design_None mb-0">
                                    <div class="card-header header p-0">
                                        <span class="card-title">Execution</span>
                                        <span role="button" onclick="addCategoryScript('category')" class="cp-circle-plus fs-5 text-primary"></span>
                                    </div>
                                    <div class="card-body p-0">
                                        <div class="input-group">
                                            <input type="search" id="search-inp" oninput="ActionSearchWorkflowTree(this)" value="" class="form-control" placeholder="Search" />
                                            <span class="input-group-text p-0"><i class="cp-search"></i></span>
                                        </div>
                                        <div id="Workflow-ActionTree" class="Workflow-Tree pe-2" style="height: calc(100vh - 182px);">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col mt-0">
                                <div contenteditable="true" class="bd-code-snippet border bg-dark logic-container highlight p-2" id="droppable" oncopy="OnBoardCopy()" onpaste="paste(this,event)" ondrop="drop(event)" ondragover="allowDrop(event)">
                                    <div id="parentAction">
                                    </div>
                                </div>
                                <div class="row d-none">
                                    <div class="col">
                                        <h6 class="d-flex">Test Action</h6>
                                        <div class="row row-cols-2">
                                            <div class="col">
                                                <div class="form-group">
                                                    <div class="form-label">Result Variable</div>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="cp-add"></i></span>
                                                        <select class="form-select-modal" title="Some placeholder text...">
                                                            <option value="One">One</option>
                                                            <option value="Two">Two</option>
                                                            <option value="Three">Three</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <h6 class="d-flex">Log Viewer</h6>
                                        <small><span class="text-secondary">2022-09-20 09:55:13</span> 562 INFO  - PSSH: RSession created <span class="text-success">sucessfully...</span>************<br><span class="text-secondary">2022-09-20 09:55:13</span> logs-- 2022-09-20 09:55:14,427 INFO  - 10.14.53.2Postgres Service is running 2022-09-20 09:55:14,427 INFO  - Database Info Retrieved... 2022-09-20 09:55:14,427 INFO  - Checking Version... 2022-09-20 09:55:14,427 INFO  - Attempting SSH Connection to: *********** 2022-09-20 09:55:16,009 INFO  - Connected to: *********** 2022-09-20 09:55:16,009 INFO  - SUDO SU SubAuthentication Type serverIP: *********** 2022-09-20 09:55:16,009 INFO  - serverIP: *********** command: sudo su - postgres 2022-09-20 09:55:16,258 INFO  - SqlServer:***********,DataBase:Avery_Production<span class="text-danger">failed..</span><br>Login failed for user 'cpadmin'.</small>
                                    </div>

                                </div>
                            </div>
                            <div class="col-3 propertiWindow mt-0">
                                <div class="card Card_Design_None mb-0">
                                    <div class="card-header px-0 pt-0">
                                        <div id="properticsTitle" class=""></div>
                                    </div>
                                    <div class="card-body p-0 pe-1 " style="height:calc(100vh - 199px); overflow-y:auto;">
                                        <div id="properticsHtml"></div>
                                    </div>
                                    <div class="card-footer Profile-Select text-end px-0">
                                        <span class="spinner spinner-border spinner-border-sm" role="status" aria-hidden="true" style="display: none;"></span>
                                        <i class="cp-Delete Skipped_Paused mx-1" id="delete-action" onclick="deletePropertice(this)" role="button"></i>
                                        <i id="btnDescriptionChange" class="cp-check Success_Running" role="button"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section>
                        <h6>Action Summary</h6>
                        <table class="table table-hover">
                            <tbody>
                                <tr>
                                    <th>Action Name</th>
                                    <td id="actionNameSummary">NA</td>

                                </tr>
                                <tr>
                                    <th>Action Type</th>
                                    <td id="actionTypeSummary">NA</td>

                                </tr>
                               
                                <tr>
                                    <th>Description</th>
                                    <td id="descriptionSummary">NA</td>

                                </tr>
                            </tbody>
                        </table>
                    </section>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between align-items-end">
                <small class="text-secondary">
                    <i class="cp-note me-1"></i>Note: All fields are mandatory except optional
                </small>
                <div class="gap-2 d-flex me-5">
                    <span> <i class="cp-copy d-none copyContent" role="button" style="font-size: 2.5em;"></i> </span>
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                  
                    <a class="btn btn-primary prev_btn btn-sm" href="javascript:void(0)" role="menuitem"
                       onclick="form.steps('previous')">Previous</a>
                    <a class="btn btn-primary next_btn btn-sm" href="javascript:void(0)" role="menuitem">Next</a>
                    <a class="btn btn-primary finish_btn btn-sm" href="javascript:void(0)" role="menuitem"
                       onclick="form.steps('finish')">Save</a>
                </div>
            </div>
        </form>
    </div>

</div>
<!--Notification-->
<div class='Notification'>
    <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' aria-atomic='true'>
        <div class='d-flex'>
            <div class='toast-body'>
                <span id="alertClass" class='{ClassName}-toast'>
                    <i class='cp-check toast_icon iconClass'></i>
                </span>
                <span id="message">

                </span>
            </div>
            <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
        </div>
    </div>
</div>

<div class="modal fade" tabindex="-1" id="ImportActionModal" aria-labelledby="configureModalLabel" data-bs-backdrop="static" aria-modal="true" role="dialog">

    <div class="modal-dialog modal-dialog-scrollabel modal-dialog-centered">

        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title"><i class="cp-import"></i><span>Import Action</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <form id="example-form">
                    <div class="form-group">
                        <div class="form-label">Import Action</div>
                        <div class="input-group">
                            <input type="file" class="form-control" name="importAction" id="importAction" accept=".json" placeholder="Import Action">
                        </div>
                        <span id="importAction-error"></span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelImport" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="loadImportAction">Import</button>
            </div>
        </div>
    </div>

</div>

<!--Action Config Delete-->

<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="DeleteActionModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="~/img/isomatric/delete.png" alt="Delete Img" />
            </div>
            <div class="modal-body text-center pt-0">
                <h5 class="fw-semibold">Are you sure?</h5>
                <p>You want to delete <span class="font-weight-bolder text-primary" id="deleteData"></span>?</p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="actionDeleteButton">Yes</button>
            </div>
        </div>
    </div>
</div>


<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" model="new WorkflowCategoryViewModel()" />
</div>

<!-- TestAction Modal -->
<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="CreateActionModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-action-builder"></i><span id="actionHeaderCategory">Add Action</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                   
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-action-name"></i></span>
                        <input class="form-control actionNameInput" maxlength="100" type="text" id="actionNameInput" placeholder="Enter Action Name" />
                        <span class="input-group-text" data-bs-toggle="collapse" id="collapseIconImage" href="#collapseIcon" aria-expanded="false" aria-controls="collapseIcon">
                            
                        </span>

                    </div>
                    <span id="actionNameCategory-error" style="color:red"></span>
                </div>
                <div class="collapse mb-2" id="collapseIcon">
                    <div class="form-label">Category Icon</div>
                    <div class="Category_Icon">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <td><i title="Cloud" class="cp-cloud custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Amazon" class="cp-amazon custom-cursor-on-hover"></i></td>
                                    <td><i title="Softlayer" class="cp-java-soft-layers custom-cursor-on-hover"></i></td>
                                    <td><i title="Database" class="cp-data custom-cursor-on-hover"></i></td>
                                    <td><i title="MSSQL" class="cp-mssql custom-cursor-on-hover"></i></td>
                                    <td><i title="MYSQL" class="cp-mysql custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Oracle" class="cp-oracle custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Postgres" class="cp-postgres custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="IBM" class="cp-IBM custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Files" class="cp-folder-file custom-cursor-on-hover"></i></td>
                                    <td><i title="Hypervisor" class="cp-workflow-execution custom-cursor-on-hover"></i></td>
                                    <td><i title="Windows" class="cp-windows custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Mailingsystem" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Network" class="cp-network custom-cursor-on-hover" cursorshover="true"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Goldengate" class="cp-goldengate custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Infoblox" class="cp-infoblox custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Router" class="cp-router custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Switch" class="cp-switch custom-cursor-on-hover"></i></td>
                                    <td><i title="OS" class="cp-os-type custom-cursor-on-hover"></i></td>
                                    <td><i title="Linux" class="cp-linux" cursorshover="true"></i></td>
                                    <td><i title="HP" class="cp-hp custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Replication" class="cp-replication-rotate custom-cursor-on-hover"></i></td>
                                    <td><i title="rsync" class="cp-rsync custom-cursor-on-hover"></i></td>
                                    <td><i title="Veeam" class="cp-Veeam custom-cursor-on-hover"></i></td>
                                    <td><i title="Storage" class="cp-stand-storage custom-cursor-on-hover"></i></td>
                                    <td><i title="hds" class="cp-hds custom-cursor-on-hover"></i></td>
                                    <td><i title="Netapp" class="cp-netapp" cursorshover="true"></i></td>
                                    <td><i title="systemmanagementtool" class="cp-system-management-tool" cursorshover="true"></i></td>
                                </tr>

                                <tr>
                                    <td><i title="Oracle Ops Center" class="cp-microsoft custom-cursor-on-hover"></i></td>
                                    <td><i title="sun-ilom" class="cp-sun-ilom custom-cursor-on-hover"></i></td>
                                    <td><i title="veritas cluster" class="cp-veritas-cluster custom-cursor-on-hover"></i></td>
                                    <td><i title="virtualization" class="cp-virtualization custom-cursor-on-hover"></i></td>
                                    <td><i title="Aix" class="cp-aix custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="solaris" class="cp-oracle-solaris custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="vmware" class="cp-vmware custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="web" class="cp-web custom-cursor-on-hover"></i></td>
                                    <td><i title="power-cli" class="cp-power-cli custom-cursor-on-hover"></i></td>
                                    <td><i title="Workflow" class="cp-workflow custom-cursor-on-hover"></i></td>
                                    <td><i title="Replication" class="cp-replication-rotate custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Web" class="cp-web"></i> </td>
                                    <td><i title="Exchange" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Sunllom" class="cp-oracle-ops custom-cursor-on-hover" cursorshover="true"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Infoblox" class="cp-soft-layer custom-cursor-on-hover"></i>        </td>
                                    <td><i title="Solution" class="cp-solution custom-cursor-on-hover"></i></td>
                                    <td><i title="Token" class="cp-token custom-cursor-on-hover"></i></td>
                                    <td><i title="General" class="cp-general custom-cursor-on-hover"></i></td>
                                    <td><i title="Create-Logger" class="cp-create-Logger custom-cursor-on-hover"></i></td>
                                    <td><i title="Conditional" class="cp-conditional custom-cursor-on-hover"></i></td>
                                    <td><i title="Loop" class="cp-loop custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="System" class="cp-system custom-cursor-on-hover"></i></td>
                                    <td><i title="Delay" class="cp-delay custom-cursor-on-hover"></i></td>
                                    <td><i title="If" class="cp-if custom-cursor-on-hover"></i></td>
                                    <td><i title="circle-switch" class="cp-circle-switch custom-cursor-on-hover"></i></td>
                                    <td><i title="data-source" class="cp-data-source custom-cursor-on-hover"></i></td>
                                    <td><i title="Rule" class="cp-rule custom-cursor-on-hover"></i></td>
                                    <td><i title="Wait" class="cp-wait custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Common" class="cp-common custom-cursor-on-hover"></i></td>
                                    <td><i title="Security" class="cp-security custom-cursor-on-hover"></i></td>
                                    <td><i title="Powershell" class="cp-powershell custom-cursor-on-hover"></i></td>
                                    <td><i title="Conversion" class="cp-conversion custom-cursor-on-hover"></i></td>
                                    <td><i title="Script" class="cp-script custom-cursor-on-hover"></i></td>
                                    <td><i title="SSH" class="cp-SSH custom-cursor-on-hover"></i></td>
                                    <td><i title="Connect " class="cp-connect custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Error-handing " class="cp-error-handing custom-cursor-on-hover"></i></td>
                                    <td><i title="Stringutility" class="cp-stringutility custom-cursor-on-hover"></i></td>
                                    <td><i title="Wmi" class="cp-wmi custom-cursor-on-hover"></i></td>
                                    <td><i title="Log Files" class="cp-log-file-name custom-cursor-on-hover"></i></td>
                                    <td><i title="Configure Settings" class="cp-configure-settings custom-cursor-on-hover"></i></td>
                                    <td><i title="prsite" class="cp-prsite custom-cursor-on-hover"></i></td>
                                    <td><i title="Mongo DB" class="cp-mongo-db"></i></td>

                                </tr>
                                <tr>
                                    <td><i title="Nutanix" class="cp-nutanix custom-cursor-on-hover"></i></td>
                                    <td><i title="IBM-AIX" class="cp-IBM-AIX custom-cursor-on-hover"></i></td>
                                    <td><i title="Mainframe" class="cp-fal-server custom-cursor-on-hover"></i></td>
                                    <td><i title="EBDR" class="cp-ebdr custom-cursor-on-hover"></i></td>
                                    <td><i title="Double Take" class="cp-double-take custom-cursor-on-hover"></i></td>
                                    <td><i title="H3PAR" class="cp-H3PAR custom-cursor-on-hover"></i></td>
                                    <td><i title="EMC" class="cp-EMC custom-cursor-on-hover"></i></td>

                                </tr>
                                <tr>
                                    <td>
                                        <i title="Firewall" class="cp-firewall custom-cursor-on-hover"></i>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="collapse multi-collapse" id="multiCollapseIcon">

                    <table class="table table-bordered">
                        <tbody id="editColorTable">
                        </tbody>
                    </table>

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-sm btn-primary actionFooterCategory">Save</button>
            </div>
        </div>
    </div>
</div>



<!-- commendAction Modal -->
<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="CommendActionModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-action-builder"></i><span id="actionHeaderCategory">Add Action</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                   
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-workflow-configuration"></i></span>
                        <input class="form-control fieldNameInput" type="text" id="fieldNameInput" placeholder="Enter Field Name" />


                    </div>
                    <span id="fieldNameInputCategory-error" style="color:red"></span>
                </div>

            </div>
            <div class="modal-body">
                <div class="form-group">
                    
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-log-file-name"></i></span>
                        <input class="form-control"  type="text" id="commendInput" placeholder="Enter Comment" />


                    </div>
                    <span id="Commend-error" style="color:red"></span>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-sm btn-primary actionCommendCategory" status="addChildSubComment" id="actionCommendCategory">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Action builder lock -->
<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="LockModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel ">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-lock"></i><span id="tittleName">Lock Action</span></h6>
                <button type="button" title="Close" class="btn-close"  data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">

                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label" title="Secret Key">
                                Secret Key
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-key"></i></span>
                                <input class="form-control" id="password" type="password" placeholder="Enter Secret Key" />
                            </div>
                            <span id="passwordlog-error"></span>
                        </div>
                    </div>
                    <input class="form-control d-none" id="userName" value="@WebHelper.UserSession.LoginName" type="text" />

                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"></small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" cursorshover="true">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm saveLock">Verify</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- LogView Modal -->
<div class="modal fade" id="LogViewModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form class="modal-content">
            <div class="modal-header">
                <h6 class="page_title">
                    <i class="cp-action-builder"></i><span>Log View</span>
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <small>
                    <span class="text-secondary">2022-09-20 09:55:13</span> 562 INFO  - PSSH: RSession created
                    <span class="text-success">sucessfully...</span>************<br>
                    <span class="text-secondary">2022-09-20 09:55:13</span> logs-- 2022-09-20 09:55:14,427 INFO  - 10.14.53.2Postgres Service is running 2022-09-20 09:55:14,427 INFO  - Database Info Retrieved... 2022-09-20 09:55:14,427 INFO  - Checking Version... 2022-09-20 09:55:14,427 INFO  - Attempting SSH Connection to: *********** 2022-09-20 09:55:16,009 INFO  - Connected to: *********** 2022-09-20 09:55:16,009 INFO  - SUDO SU SubAuthentication Type serverIP: *********** 2022-09-20 09:55:16,009 INFO  - serverIP: *********** command: sudo su - postgres 2022-09-20 09:55:16,258 INFO  - SqlServer:***********,DataBase:Avery_Production<span class="text-danger">failed..</span><br>Login failed for user 'cpadmin'.
                </small>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" data-bs-target="#ActionbuilderconfigModal" data-bs-toggle="modal">Back to Configurtion </button>
            </div>
        </form>
    </div>
</div>
<!-- Import Modal -->
<div class="modal fade" id="importmodal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form class="modal-content">
            <div class="modal-header">
                <h6 class="page_title">
                    <i class="cp-import "></i><span>Import</span>
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th><input class="form-check" id="selectAllImport" type="checkbox" checked /></th>
                            <th>Action Type</th>
                            <th>Action Name</th>
                            <th>Version</th>
                        </tr>
                    </thead>
                    <tbody id="actionDataTable">
                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="save_import">Save</button>
            </div>
        </form>
    </div>
</div>

<div class="modal fade" id="DeleteModalAction" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">

            <div class="modal-header p-0">
                <img class="delete-img" src="~/img/isomatric/delete.png" alt="Delete Img" />
            </div>
            <div class="modal-body text-center pt-0">
                <h5 class="fw-bold">Are you sure?</h5>
                <p>You want to delete the data?</p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" id="cancelButton" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="submit" id="confirmButton" class="btn btn-primary btn-sm">Yes</button>
            </div>
        </div>
    </div>
</div>


<!--Modal Action builder config Modal End-->
@section Scripts
{
    <partial name="_ValidationScriptsPartial" />
}

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
    async function validate_name(data) {
        if (data.value == "") {
            $("#fieldNameInputCategory-error").text("Enter field name")
                .addClass('field-validation-error')
            return false;
        } else {
            $("#fieldNameInputCategory-error").text("")
                .removeClass('field-validation-error')
            return true;
        }
    }
   async function validate_Commend(data) {
        if (data.value == "") {
            $("#Commend-error").text("Enter field name")
                .addClass('field-validation-error')
            return false;
        } else {
            $("#Commend-error").text("")
                .removeClass('field-validation-error')
            return true;
        }
    }
 async function  restrict(data){
  await ["e", "E", "+", "-", "."].includes(event.key) && event.preventDefault()
  }
</script>
<script src="~/lib/bootstrap-select/js/select2.min.js"></script>
<script src="~/js/admin/actionbuilder/workflowcategory.js"></script>
<script src="~/js/admin/actionbuilder/workflowaction.js"></script>
<script src="~/js/wizard.js"></script>
<script src="~/js/ExecuteDropZone.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.js"></script>
@* <script src="~/lib/jqueryui/jquery-ui.min.js"></script> *@
<script src="~/lib/jQuery-formBuilder/form-builder.min.js"></script>
<script src="~/lib/formeo/formeo.min.js"></script>





