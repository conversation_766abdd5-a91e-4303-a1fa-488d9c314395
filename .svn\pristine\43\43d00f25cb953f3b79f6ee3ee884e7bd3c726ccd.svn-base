﻿using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Application.Features.BusinessFunction.Events.PaginatedView;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class OperationalFunctionController : BaseController
{
    private readonly ILogger<OperationalFunctionController> _logger;
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public OperationalFunctionController(ILogger<OperationalFunctionController> logger, IDataProvider dataProvider, IPublisher publisher, IMapper mapper)
    {
        _logger = logger;
        _publisher = publisher;
        _dataProvider = dataProvider;
        _mapper = mapper;
    }


    [AntiXss]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in OperationalFunction");

        await _publisher.Publish(new BusinessFunctionPaginatedEvent());

        var businessServiceView = await _dataProvider.BusinessService.GetBusinessServiceNames();

       // var businessFunctionView = await _dataProvider.BusinessFunction.GetBusinessFunctionPaginatedList(new GetBusinessFunctionPaginatedListQuery());

        var businessFunctionModel = new BusinessFunctionModels
        {
            BusinessServiceNames = businessServiceView
            //  PaginatedBusinessFunction = businessFunctionView
        };

        return View(businessFunctionModel);

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Configuration.CreateAndEdit)]
    public async Task<IActionResult> CreateOrUpdate(BusinessFunctionModels businessFunctionModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in OperationalFunction");
        var businessFunctionId = Request.Form["id"].ToString();
        try
        {

           
            BaseResponse result;
            if (businessFunctionId.IsNullOrWhiteSpace())
            {
                var businessFunctionCommand = _mapper.Map<CreateBusinessFunctionCommand>(businessFunctionModel);

                 result = await _dataProvider.BusinessFunction.CreateAsync(businessFunctionCommand);

                _logger.LogDebug($"Creating OperationalFunction '{businessFunctionCommand.Name}'");

                //TempData.NotifySuccess(result.Message);
            }
            else
            {
                var businessFunctionCommand = _mapper.Map<UpdateBusinessFunctionCommand>(businessFunctionModel);

                 result = await _dataProvider.BusinessFunction.UpdateAsync(businessFunctionCommand);

                _logger.LogDebug($"Updating OperationalFunction '{businessFunctionCommand.Name}'");

                //TempData.NotifySuccess(result.Message);
            }

            _logger.LogDebug("CreateOrUpdate operation completed successfully in OperationalFunction, returning view.");

            return Json(new { Success = true, data = result });
        }
        //catch (ValidationException ex)
        //{
        //    _logger.LogError($"Validation error on operational function page: {ex.ValidationErrors.FirstOrDefault()}");

        //    TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

        //    return RedirectToAction("List");
        //}
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on operational function page while processing the request for create or update.", ex);

            //TempData.NotifyWarning(ex.Message);

            return ex.GetJsonException();
        }

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in OperationalFunction");

        try
        {
            var businessFunction = await _dataProvider.BusinessFunction.DeleteAsync(id);

            _logger.LogDebug("Successfully deleted record in OperationalFunction");

            TempData.NotifySuccess(businessFunction.Message);

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            TempData.NotifyWarning(ex.GetMessage());

            _logger.Exception("An error occurred while deleting record on OperationalFunction.", ex);

            return RedirectToAction("List");
        }
    }


    [HttpGet]
    public async Task<bool> IsBusinessFunctionNameExist(string businessFunctionName, string? id)
    {
        _logger.LogDebug("Entering IsBusinessFunctionNameExist method in OperationalFunction");

        try
        {
            var nameExist = await _dataProvider.BusinessFunction.IsBusinessFunctionNameExist(businessFunctionName, id);

            _logger.LogDebug("Returning result for IsBusinessFunctionNameExist on OperationalFunction");

            return nameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on operational function while checking if operational function  name exists for : {businessFunctionName}.", ex);

            return false;
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetPaginationList(GetBusinessFunctionPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPaginationList method in OperationalFunction");

        try
        {
            _logger.LogDebug("Successfully retrieved operational function paginated list on OperationalFunction page");

            return Json(await _dataProvider.BusinessFunction.GetBusinessFunctionPaginatedList(query));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on operational function page while processing the pagination request.", ex);

            return ex.GetJsonException();
        }

    }

}