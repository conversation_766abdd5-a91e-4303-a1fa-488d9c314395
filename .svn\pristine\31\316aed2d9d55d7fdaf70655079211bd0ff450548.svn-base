﻿namespace ContinuityPatrol.Application.Features.RsyncJob.Commands.Create;

public class CreateRsyncJobCommand : IRequest<CreateRsyncJobResponse>
{
    public string ReplicationId { get; set; }
    public string ReplicationName { get; set; }
    public string ReplicationTypeId { get; set; }
    public string ReplicationType { get; set; }
    public string SiteId { get; set; }
    public string SiteName { get; set; }
    public string Properties { get; set; }
    public string JobProperties { get; set; }
    public string ScheduleProperties { get; set; }
    public string SourceDirectory { get; set; }
    public string DestinationDirectory { get; set; }
    public string ModeType { get; set; }
    public string RsyncOptionId { get; set; }
    public string LastSuccessfullReplTime { get; set; }
}