using ContinuityPatrol.Application.Features.DynamicDashboardMap.Events.Create;

namespace ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Create;

public class
    CreateDynamicDashboardMapCommandHandler : IRequestHandler<CreateDynamicDashboardMapCommand,
        CreateDynamicDashboardMapResponse>
{
    private readonly IDynamicDashboardMapRepository _dynamicDashboardMapRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IUserRepository _userRepository;

    public CreateDynamicDashboardMapCommandHandler(IMapper mapper,
        IDynamicDashboardMapRepository dynamicDashboardMapRepository, IPublisher publisher,
        IUserRepository userRepository)
    {
        _mapper = mapper;
        _publisher = publisher;
        _userRepository = userRepository;
        _dynamicDashboardMapRepository = dynamicDashboardMapRepository;
    }

    public async Task<CreateDynamicDashboardMapResponse> Handle(CreateDynamicDashboardMapCommand request,
        CancellationToken cancellationToken)
    {
        var dynamicDashboardMap = _mapper.Map<Domain.Entities.DynamicDashboardMap>(request);

        dynamicDashboardMap = await _dynamicDashboardMapRepository.AddAsync(dynamicDashboardMap);

        if (request.Type.Trim().ToLower().Equals("user"))
        {
            var user = await _userRepository.GetByReferenceIdAsync(dynamicDashboardMap.UserId);

            user.IsDefaultDashboard = request.IsDefault;

            await _userRepository.UpdateAsync(user);
        }
        else if (request.Type.Trim().ToLower().Equals("role"))
        {
            var userRoles = await _userRepository.GetUsersByRoleId(dynamicDashboardMap.RoleId);

            userRoles.ForEach(x => x.IsDefaultDashboard = request.IsDefault);

            await _userRepository.UpdateRangeAsync(userRoles);
        }

        var response = new CreateDynamicDashboardMapResponse
        {
            Message = Message.Create(nameof(Domain.Entities.DynamicDashboardMap), dynamicDashboardMap.UserName),

            Id = dynamicDashboardMap.ReferenceId
        };

        await _publisher.Publish(new DynamicDashboardMapCreatedEvent { Name = dynamicDashboardMap.UserName },
            cancellationToken);

        return response;
    }
}