﻿using ContinuityPatrol.Application.Features.WorkflowPrediction.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowPrediction.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetActionId;
using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetNextPossibleId;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IWorkflowPredictionService
{
    Task<List<WorkflowPredictionListVm>> GetWorkflowPrediction();

    Task<BaseResponse> CreateWorkflowPrediction( CreateWorkflowPredictionCommand createWorkflowPredictionCommand);

    Task<BaseResponse> UpdateWorkflowPrediction( UpdateWorkflowPredictionCommand updateWorkflowPredictionCommand);

    Task<BaseResponse> DeleteWorkflowPrediction(string id);
    Task<WorkflowPredictionResult> GetWorkflowPredictionListByActionId(string actionId,string previousActionId);

    Task<List<WorkflowPredictionListByNextPossibleIdVm>> GetWorkflowPredictionListByNextPossibleId(string nextPossibleId);
}