using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DynamicDashboardWidgetRepositoryTests : IClassFixture<DynamicDashboardWidgetFixture>
{
    private readonly DynamicDashboardWidgetFixture _dynamicDashboardWidgetFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DynamicDashboardWidgetRepository _repository;
    private readonly DynamicDashboardWidgetRepository _repositoryNotParent;

    public DynamicDashboardWidgetRepositoryTests(DynamicDashboardWidgetFixture dynamicDashboardWidgetFixture)
    {
        _dynamicDashboardWidgetFixture = dynamicDashboardWidgetFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DynamicDashboardWidgetRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new DynamicDashboardWidgetRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var dynamicDashboardWidget = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetDto;

        // Act
        var result = await _repository.AddAsync(dynamicDashboardWidget);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dynamicDashboardWidget.Name, result.Name);
        Assert.Equal(dynamicDashboardWidget.Properties, result.Properties);
        Assert.Single(_dbContext.DynamicDashboardWidgets);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var dynamicDashboardWidget = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetDto;
        await _repository.AddAsync(dynamicDashboardWidget);

        dynamicDashboardWidget.Name = "UpdatedWidgetName";

        // Act
        var result = await _repository.UpdateAsync(dynamicDashboardWidget);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedWidgetName", result.Name);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var dynamicDashboardWidget = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetDto;
        await _repository.AddAsync(dynamicDashboardWidget);

        // Act
        var result = await _repository.DeleteAsync(dynamicDashboardWidget);

        // Assert
        Assert.Equal(dynamicDashboardWidget.Name, result.Name);
        Assert.Empty(_dbContext.DynamicDashboardWidgets);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var dynamicDashboardWidget = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetDto;

        var addedEntity = await _repository.AddAsync(dynamicDashboardWidget);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
 
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var dynamicDashboardWidget = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetDto;
        var addedEntity = await _repositoryNotParent.AddAsync(dynamicDashboardWidget);

        // Act
        var result = await _repositoryNotParent.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dynamicDashboardWidget = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetDto;
        await _repository.AddAsync(dynamicDashboardWidget);

        // Act
        var result = await _repository.GetByReferenceIdAsync(dynamicDashboardWidget.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dynamicDashboardWidget.ReferenceId, result.ReferenceId);
        Assert.Equal(dynamicDashboardWidget.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var dynamicDashboardWidgets = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetList;
        await _repository.AddRangeAsync(dynamicDashboardWidgets);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dynamicDashboardWidgets.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var dynamicDashboardWidgets = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetList;
        await _repositoryNotParent.AddRangeAsync(dynamicDashboardWidgets);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

   

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var dynamicDashboardWidget = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetDto;
        dynamicDashboardWidget.Name = "ExistingWidgetName";
        await _repository.AddAsync(dynamicDashboardWidget);

        // Act
        var result = await _repository.IsNameExist("ExistingWidgetName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Act
        var result = await _repository.IsNameExist("NonExistentWidgetName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var dynamicDashboardWidget = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetDto;

        dynamicDashboardWidget.Name = "SameWidgetName";
           
        await _repository.AddAsync(dynamicDashboardWidget);

        // Act
        var result = await _repository.IsNameExist("SameWidgetName", dynamicDashboardWidget.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntity()
    {
        // Arrange
        var dynamicDashboardWidget1 = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetList;

        dynamicDashboardWidget1[0].Name = "ExistingWidgetName";
          
        await _repository.AddRangeAsync(dynamicDashboardWidget1);

        // Act
        var result = await _repository.IsNameExist("ExistingWidgetName", dynamicDashboardWidget1[0].ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var dynamicDashboardWidgets = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetList;

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(dynamicDashboardWidgets);
        var initialCount = dynamicDashboardWidgets.Count;
        
        var toUpdate = dynamicDashboardWidgets.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedWidgetName");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = dynamicDashboardWidgets.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.Name == "UpdatedWidgetName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
