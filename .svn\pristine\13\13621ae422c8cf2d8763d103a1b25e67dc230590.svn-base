﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()

<link href="~/css/dashboard.css" rel="stylesheet" />
<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title"><i class="cp-monitoring"></i><span>RoboCopy Detail Monitoring :</span>
            <span id="infraName"></span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time :"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-0 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow"></i>Back</a>
        </div>
       
    </div>
    <div id="noDataimg" class="monitor_pages">
        <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row g-2 mt-0">
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Robocopy Detailed Monitoring</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Robocopy Details</th>
                                    <th class="text-primary">Source Server</th>
                                    <th >Target Server</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-bold"><i class="text-secondary cp-ip-address me-1"></i>IP Address</td>
                                    <td class="text-truncate" id="PR_Server_IpAddress"></td>
                                    <td class="text-truncate" id="DR_Server_IpAddress"></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-secondary cp-globe-www me-1"></i>Hostname</td>
                                    <td class="text-truncate" id="PR_Server_HostName"></td>
                                    <td class="text-truncate" id="DR_Server_HostName"></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-secondary cp-job-management me-1"></i>No. Of Jobs</td>
                                    <td class="text-truncate" id="PR_Server_Job"></td>
                                    <td class="text-truncate" id="DR_Server_Job"></td>
                                    
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center pt-0">
                        @* <img src="~/img/isomatric/solutiondiagram.svg" height="159px;" /> *@
                        <div id="Solution_Diagram" style="width:100%; height:100%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-2">
            <div class="col d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Robocopy Monitoring Status</div>
                    <div class="card-body pt-0 p-2" id="jobtable">
                      
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 d-grid">
            <div class="card Card_Design_None mb-2 h-100" id="mssqlserver">
                <div class="card-header card-title" title="Service/Process/Workflow">Service/Process/Workflow</div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0 noDataimg" style="table-layout:fixed" id="tableCluster">
                        <thead class="align-middle">
                            <tr>
                                <th rowspan="2">Service / Process / Workflow Name</th>
                                <th colspan="2" class="text-center">Server IP/HostName</th>
                            </tr>
                            <tr>
                                <th id="prIp"></th>
                                <th id="drIp"></th>
                            </tr>
                        </thead>
                        <tbody id="mssqlserverbody">
                        </tbody>

                    </table>
                </div>
            </div>
        </div>
       @*  <div class="row">
            <div class="col">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Robocopy Monitoring Status</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Job : 2</th>
                                    <th></th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-bold"><i class="text-secondary cp-folder-file me-1"></i>Drive / Directory for Copy</td>
                                    <td>\\172.16.30.139\c$\Runbook</td>
                                    <td>C;\Runbook</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-secondary cp-select me-1"></i>Selected Options</td>
                                    <td>/S /E /Z /COPYALL /MIR /R:5 /W:10</td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-secondary cp-table-clock me-1"></i>Started Date, Time</td>
                                    <td>Thursday. September 30,2021 5:10:32 PM</td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-secondary cp-table-clock me-1"></i>Archive Mode</td>
                                    <td>Thursday. September 30,2021 5:10:32 PM</td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="p-0">
                                        <table class="table mb-0">
                                            <thead>
                                                <tr>
                                                    <td></td>
                                                    <th>Total</th>
                                                    <th>Copied</th>
                                                    <th>Skipped</th>
                                                    <th>Mismatch</th>
                                                    <th>Failed</th>
                                                    <th>Extras</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><i class="text-secondary cp-file-c me-1"></i>Dirs</td>
                                                    <td>1</td>
                                                    <td>1</td>
                                                    <td>0</td>
                                                    <td>0</td>
                                                    <td>0</td>
                                                    <td>0</td>
                                                </tr>
                                                <tr>
                                                    <td><i class="text-secondary cp-control-file-type me-1"></i>Files</td>
                                                    <td>2</td>
                                                    <td>2</td>
                                                    <td>0</td>
                                                    <td>0</td>
                                                    <td>0</td>
                                                    <td>0</td>
                                                </tr>
                                                <tr>
                                                    <td><i class="text-secondary cp-freeze-time me-1"></i>Bytes</td>
                                                    <td>74.5 K</td>
                                                    <td>74.5 K</td>
                                                    <td>0</td>
                                                    <td>0</td>
                                                    <td>0</td>
                                                    <td>0</td>
                                                </tr>
                                                <tr>
                                                    <td class="border-bottom-0"><i class="text-secondary cp-apply-finish-time me-1"></i>Times</td>
                                                    <td class="border-bottom-0">0:00:00</td>
                                                    <td class="border-bottom-0">0:00:00</td>
                                                    <td class="border-bottom-0"></td>
                                                    <td class="border-bottom-0"></td>
                                                    <td class="border-bottom-0">0:00:00</td>
                                                    <td class="border-bottom-0">0:00:00</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-speed-meter me-1"></i>Speed(Bytes/Min)</td>
                                    <td>5085866 Bytes/Sec</td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-speed-meter me-1"></i>Speed(MegaBytes/Min)</td>
                                    <td>291.015 MegaBytes/min.</td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div> *@
    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/js/monitoring/MonitoringSolutionDiagram.js"></script>
<script src="~/js/monitoring/MonitoringRobocopy.js"></script>

