﻿using ContinuityPatrol.Application.Features.RsyncJob.Commands.Create;
using ContinuityPatrol.Application.Features.RsyncJob.Commands.Update;
using ContinuityPatrol.Application.Features.RsyncJob.Queries.GetPaginated;
using ContinuityPatrol.Domain.ViewModels.RsyncJobModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class RsyncJobService : BaseClient, IRsyncJobService
{

    public RsyncJobService(IConfiguration configuration, IAppCache appCache, ILogger<RsyncJobService> logger) : base(configuration, appCache, logger)
    {
    }
    public async  Task<BaseResponse> CreateRsyncJob(CreateRsyncJobCommand createRsyncJobCommand)
    {
        var request = new RestRequest("api/v6/rsyncjobs", Method.Post);

        request.AddJsonBody(createRsyncJobCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteRsyncJob(string id)
    {
        var request = new RestRequest($"api/v6/rsyncjob/{id}", Method.Delete);

        return await Get<BaseResponse>(request);
    }

    public async  Task<PaginatedResult<RsyncJobListVm>> GetPaginatedRsyncJobs(GetRsyncJobPaginatedQuery query)
    {

        var request = new RestRequest($"api/v6/rsyncjob/paginated-list/{query}", Method.Get);

        return await Get<PaginatedResult<RsyncJobListVm>>(request);
    }

    public async Task<RsyncJobDetailVm> GetRsyncJobById(string id)
    {
        var request = new RestRequest($"api/v6/Rsyncjob/{id}", Method.Get);

        return await Get<RsyncJobDetailVm>(request);
    }

    public async Task<List<RsyncJobListVm>> GetRsyncJobs()
    {
        var request = new RestRequest("api/v6/rsyncjob", Method.Get);

        return await Get<List<RsyncJobListVm>>(request);
    }

    public async Task<BaseResponse> UpdateRsyncJob(UpdateRsyncJobCommand updateRsyncJobCommand)
    {
        var request = new RestRequest("api/v6/Rsyncjob", Method.Put);

        request.AddJsonBody(updateRsyncJobCommand);

        return await Put<BaseResponse>(request);
    }
}
