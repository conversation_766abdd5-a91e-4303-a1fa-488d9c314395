using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowExecutionTempFixture : IDisposable
{
    public List<WorkflowExecutionTemp> WorkflowExecutionTempPaginationList { get; set; }
    public List<WorkflowExecutionTemp> WorkflowExecutionTempList { get; set; }
    public WorkflowExecutionTemp WorkflowExecutionTempDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowExecutionTempFixture()
    {
        var fixture = new Fixture();

        WorkflowExecutionTempList = fixture.Create<List<WorkflowExecutionTemp>>();

        WorkflowExecutionTempPaginationList = fixture.CreateMany<WorkflowExecutionTemp>(20).ToList();

        WorkflowExecutionTempDto = fixture.Create<WorkflowExecutionTemp>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
