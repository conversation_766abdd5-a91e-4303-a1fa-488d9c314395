﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}


<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title">
            <i class="cp-monitoring"></i><span> ORACLE RAC  Min DETAIL MONITORING:  <span id="infraName"></span></span>
        </h6>
        <form class="d-flex align-items-center jusify-content-between" >
            <div class="d-flex align-items-center">
                <span title="Last Monitored Time"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
                @* <a class="btn btn-sm btn-primary ms-2 px-2 py-0 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow"></i>Back</a> *@
            </div>
            <div class="input-group" style="width:200px">
                <select id="clusterDetails" class="form-select" aria-label="Default select example">
                    <option disabled>Select Node Name</option>
                </select>
            </div>
        </form>
    </div>
    <div id="noDataimg" class="monitor_pages">
        <div class="row g-2">
            <div class="col-4 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title" title="Database Details">Database Details</div>
                    <div class="card-body pt-0 p-2" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 270px);">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead class="position-sticky top-0">
                                <tr>
                                    <th title="Database Details">Database Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Name"> <span><i class="text-secondary cp-database me-1 fs-6"></i>Database Name</span> </td>
                                    <td class="text-truncate">Qarel</td>
                                    <td>Qarel</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Unique Name">
                                        <span> <i class="text-secondary cp-database-type me-1 fs-6"></i>Database Unique Name</span>

                                    </td>
                                    <td class="text-truncate">Qarel_STB</td>
                                    <td class="text-truncate">Qarel_STB</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Role"><span><i class="text-secondary cp-database-role me-1 fs-6"></i>Database Role</span></td>
                                    <td class="text-truncate">Primary</td>
                                    <td class="text-truncate">
                                        Physical Sta...
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Open Mode">
                                        <span><i class="text-secondary cp-dr me-1 fs-6"></i>Open Mode</span>
                                    </td>
                                    <td class="text-truncate">
                                        Read Weite
                                    </td>
                                    <td class="text-truncate">Mounted</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Created Time">
                                        <span>
                                            <i class="text-secondary cp-apply-finish-time
                                                me-1 fs-6"></i>Database Created Time
                                        </span>

                                    </td>
                                    <td class="text-truncate">
                                        10/03/2019
                                        01:21:56
                                    </td>
                                    <td class="text-truncate">
                                        10/03/2019
                                        01:21:56
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Control File Type">
                                        <span>  <i class="text-secondary cp-control-file-type me-1 fs-6"></i>Control File Type</span>

                                    </td>
                                    <td class="text-truncate"><i class="cp-file-edit me-1"></i>Current</td>
                                    <td class="text-truncate"><i class="cp-file-edit me-1"></i>Current</td>
                                   
                                </tr>

                                <tr>
                                    <td class="fw-semibold text-truncate" title="Flashback_ON">
                                        <span><i class="text-secondary cp-warning me-1 fs-6"></i>Flashback_ON</span>

                                    </td>
                                    <td class="text-truncate">No</td>
                                    <td class="text-truncate">No</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Version">
                                        <span>
                                            <i class="text-secondary cp-datas
                                                me-1 fs-6"></i>Database Version
                                        </span>

                                    </td>
                                    <td class="text-truncate">
                                        12.1.0.2.0
                                    </td>
                                    <td class="text-truncate">
                                        12.1.0.2.0
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Incarnation">
                                        <span> <i class="text-secondary cp-database me-1 fs-6"></i>Database Incarnation</span>

                                    </td>
                                    <td class="text-truncate">2</td>
                                    <td class="text-truncate">2</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Resetlogs Change">
                                        <span>   <i class="text-secondary cp-refresh me-1 fs-6"></i>Resetlogs Change</span>

                                    </td>
                                    <td class="text-truncate">1594143</td>
                                    <td class="text-truncate">1594143</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Resetlogs Mode">
                                        <span> <i class="text-secondary cp-reset-log-change me-1 fs-6"></i>Resetlogs Mode</span>

                                    </td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1 fs-6"></i>Not Allowed</td>
                                    <td class="text-truncate"><i class="text-warning cp-warning me-1 fs-6"></i>Required</td>
                                   
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Archive Mode">
                                        <span> <i class="text-secondary cp-refresh me-1 fs-6"></i>Archive Mode</span>

                                    </td>
                                    <td class="text-truncate">Archivelog</td>
                                    <td class="text-truncate">Archivelog</td>
                                </tr>
                                  <tr>
                                <td class="fw-semibold text-truncate" title="DB Size(in MB)">
                                <span>  <i class="text-secondary cp-storage-name me-1 fs-6"></i>DB Size(in MB)</span>

                                </td>
                                    <td class="text-truncate">2665</td>
                                    <td class="text-truncate">2665</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DB Create File Dest">
                                        <span>     <i class="text-secondary cp-db-create-online me-1 fs-6"></i>DB Create File Dest</span>

                                    </td>
                                    <td class="text-truncate">+Data</td>
                                    <td class="text-truncate">+Data</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DB File Name Convert">
                                        <span><i class="text-secondary cp-database me-1 fs-6"></i>DB File Name Convert</span>

                                    </td>
                                    <td class="text-truncate">+Data, +Data</td>
                                    <td class="text-truncate">+Data, +Data</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DB Create Online">
                                        <span> <i class="text-secondary cp-db-create-online me-1 fs-6"></i>DB Create Online Log Dest   </span>

                                    </td>
                                    <td class="text-truncate"> <i class="text-danger cp-disable me-1"></i>NA</td>
                                    <td class="text-truncate"> <i class="text-danger cp-disable me-1"></i>NA</td>
                                  
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Log File Name Convert">
                                        <span> <i class="text-secondary cp-log-file-name me-1 fs-6"></i>Log File Name Convert</span>

                                    </td>
                                    <td class="text-truncate">+Data, +Data</td>
                                    <td class="text-truncate">+Data, +Data</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DB Recovery File Dest">
                                        <span>   <i class="text-secondary cp-roate-settings me-1 fs-6"></i>DB Recovery File Dest</span>

                                    </td>
                                    <td class="text-truncate">
                                        +Arch
                                    </td class="text-truncate">
                                    <td class="text-truncate">+Arch</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DB Recovery File Dest Size">
                                        <span> <i class="text-secondary cp-roate-settings me-1 fs-6"></i>DB Recovery File Dest Size</span>

                                    </td>
                                    <td class="text-truncate">
                                        2048
                                    </td>
                                    <td class="text-truncate">
                                        2048
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Flashback Retention Target">
                                        <span> <i class="text-secondary cp-warning me-1 fs-6"></i>Flashback Retention Target</span>

                                    </td>
                                    <td class="text-truncate">
                                        1440
                                    </td>
                                    <td class="text-truncate">
                                        1440
                                    </td>
                                </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
            </div>

            <div class="col-4 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title" title="Replication Monitoring">Replication Monitoring</div>
                    <div class="card-body pt-0 p-2" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 270px);">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead class="position-sticky top-0">
                                <tr>
                                    <th title="Replication Details">Replication Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Active DG Enabled">
                                        <span> <i class="text-secondary cp-active-dg-enable me-1 fs-6"></i>Active DG Enabled</span>

                                    </td>
                                    @*<td class="text-truncate"><span id="PR_Active_DG_Enabled"></span></td>*@
                                   <td class="text-truncate"> <i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DG_Broker Status"><span><i class="text-secondary cp-files me-1 fs-6"></i>DG_Broker Status</span></td>
                                <td class="text-truncate"> <i class="text-danger cp-disable me-1"></i>Disable</td>
                               <td class="text-truncate"> <i class="text-danger cp-disable me-1"></i>Disable</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Dataguard_Status">
                                        <span> <i class="text-secondary cp-dataguard-status me-1 fs-6"></i>Dataguard_Status</span>

                                    </td>
                                    <td class="text-truncate">Running</td>
                                    <td class="text-truncate">Valid</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Recovery_Status"><span><i class="text-secondary cp-time me-1 fs-6"></i>Recovery_Status</span></td>
                                    @*<td class="text-truncate"><span id="PR_Recovery_Status"></span></td>*@
                                    <td class="text-truncate">Wait_For_Log</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Switchover Status"><span><i class="text-secondary cp-files me-1 fs-6"></i>Switchover Status</span></td>
                                    <td class="text-truncate">To Standby</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>Not Allowed</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Log_Archive_Config">
                                        <span> <i class="text-secondary cp-configure-settings me-1 fs-6"></i>Log_Archive_Config</span>

                                    </td>
                                    <td class="text-truncate">DG_Config=</td>
                                    <td class="text-truncate">DG_Config=</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Force Logging"><span><i class="text-secondary cp-left-right me-1 fs-6"></i>Force Logging</span></td>
                                    <td class="text-truncate"> <i class="text-danger cp-disable me-1"></i>NO</td>
                                    <td class="text-truncate"> <i class="text-danger cp-disable me-1"></i>NO</td>
                                   
                                </tr>
                            
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Archive Dest Location"><span><i class="text-secondary cp-location me-1 fs-6"></i>Archive Dest Location</span></td>
                                    <td class="text-truncate">+Arch</td>
                                    <td class="text-truncate">Use_DB_R</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Protection Mode"><span><i class="text-secondary cp-protection-mode me-1 fs-6"></i>Protection Mode</span></td>
                                    <td class="text-truncate">Maximum
Performance</td>
                                    <td class="text-truncate">Maximum
Performance</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Transmit Mode"><span><i class="text-secondary cp-left-right me-1 fs-6"></i>Transmit Mode</span></td>
                                    <td class="text-truncate">Parallelsync</td>
                                    <td class="text-truncate">Parallelsync</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Recovery Mode"><span><i class="text-secondary cp-time me-1 fs-6"></i>Recovery Mode</span></td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                    <td class="text-truncate">Managed</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Affirm"><span><i class="text-secondary cp-files me-1 fs-6"></i>Affirm</span></td>
                                      <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NO</td>
                                      <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NO</td>
                         
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Archiver"><i class="text-secondary cp-files me-1 fs-6"></i>Archiver</td>
                                    <td class="text-truncate">LGER</td>
                                    <td class="text-truncate">LGER</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Archivelog Compression">
                                        <span> <i class="text-secondary cp-archive-mode me-1 fs-6"></i>Archivelog Compression</span>

                                    </td>
                                    <td class="text-truncate">Not Enable</td>
                                    <td class="text-truncate">Not Enable</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Delay Mins"><span><i class="text-secondary cp-folder-server me-1 fs-6"></i>Delay Mins</span></td>
                                    <td class="text-truncate">0</td>
                                    <td class="text-truncate">0</td>
                                </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
            </div>
            <div class="col-4 d-grid ">

                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram<span id="selctedNodeNameSolution"></span></div>
                    <div class="card-body text-center d-flex align-items-center justify-content-center" id="Solution_Diagram">
                        @* <div id="SolutionDiagramRAC" style="width:100%; height:100%;"></div> *@
                    </div>
                </div>

                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title" style="font-size:15px" title="Database Size">Database Size</div>
                    <div class="card-body d-flex pt-0 align-items-center gap-4 justify-content-center">
                        <div>
                            <i class="cp-database-sizes text-light" style="font-size: 5.9rem;"></i>
                        </div>
                        <div class="d-grid  border-start border-3">
                            <div class="text-primary ms-2 fw-semibold" title="Primary">Primary</div>
                            <span class="text-secondary mb-1 ms-2" title="Database Size">Database Size</span>
                            <h6 class="mb-0 fw-bold ms-2" >3861.125 MB</h6>
                        </div>
                        @*<div class="w-50" id="DatabaseSize"></div>*@
                        <div>
                            <div class="d-grid">
                                <div class="ms-2 fw-semibold" title="DR">DR</div>
                                <span class="text-secondary mb-1 ms-2" title="Database Size">Database Size</span>
                                <h6 class="mb-0 fw-bold ms-2" >234.67 MB</h6>
                            </div>
                        </div>
                    </div>
                </div>



                <div class="d-flex gap-2">
                    <div class="card Card_Design_None w-50 mb-0">
                        <div class="card-body">
                            <i class="cp-log-sequence text-primary fs-4 me-2 bg-body-secondary p-1 rounded-2" title="Log Sequence"></i><span title="Log Sequence" class="fw-semibold">Log Sequence</span>
                            <div class=" mt-3">
                                <div class="w-50 d-grid mb-0">
                                    <small class="text-primary fs-7 mb-1 fw-semibold" title="Primary">Primary</small>
                                    <h6 class="mb-0  fs-7" >983</h6>
                                </div>
                                <div class="w-50 d-grid">
                                    <small class=" fs-7 mb-1 fw-semibold" title="DR">DR</small>
                                    <h6 class="mb-0 fs-7 d-inline-block">1412</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card Card_Design_None w-50 mb-2">
                        <div class="card-body">
                            <i class="cp-current-scn text-primary fs-4 me-2 bg-body-secondary p-1 rounded-2" title="Current SCN"></i><span title="Current SCN" class="fw-semibold">Current SCN</span>
                            <div class=" mt-3">
                                <div class="w-50 d-grid mb-2">

                                    <small class="text-primary fs-7 mb-1 fw-semibold" title="Primary">Primary</small>
                                    <h6 class="mb-0  fs-7" >83324</h6>

                                </div>
                                <div class="w-50 d-grid">
                                    <small class=" fs-7 mb-1 fw-semibold" title="DR">DR</small>
                                    <h6 class="mb-0 fs-7" >73032</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
            <!-- <div class="row mt-3"> -->
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-0" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 310px);">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span title="Cluster Details">
                            Cluster Details <span id="selctedNodeNameCluster"></span>
                        </span>

                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" id="tableCluster" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Cluster Details">Cluster Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Cluster Name"><span><i class="text-secondary cp-cluster-database me-1 fs-6"></i>Cluster Name</span></td>
                                    <td class="text-truncate">ora scan d</td>
                                    <td class="text-truncate">ora scan d</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Clusterware Active Version">
                                        <span><i class="text-secondary cp-data me-1 fs-6"></i>Clusterware Active Version</span>

                                    </td>
                                    <td class="text-truncate">12.1.0.2.0</td>
                                    <td class="text-truncate">12.1.0.2.0</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="OHAS Status"><span><i class="text-secondary cp-ohas-status me-1 fs-6"></i>OHAS Status</span></td>
                                    <td class="text-truncate">Oracle High Availability Services is Online</td>
                                    <td class="text-truncate">Oracle High Availability Services is Online</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="CRS Status"><span><i class="text-secondary cp-cluster-database me-1 fs-6"></i>CRS Status</span></td>
                                   
                                    <td class="text-truncate">Cluster Ready Services is online</td>
                                    <td class="text-truncate">Cluster Ready Services is online</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="CSS Status"><span><i class="text-secondary cp-cluster-database me-1 fs-6"></i>CSS Status</span></td>
                              <td class="text-truncate">Cluster Synchronization Services is Online</td>
                                    <td class="text-truncate">Cluster Synchronization Services is Online</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="EVM Status"><span><i class="text-secondary cp-ohas-status me-1 fs-6"></i>EVM Status</span></td>
                                    <td class="text-truncate">Event Manager is Online</td>
                                    <td class="text-truncate">Event Manager is Online</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Cluster Listener">
                                        <span> <i class="text-secondary cp-cluster-database me-1 fs-6"></i>Cluster Listener</span>

                                    </td>
                                    <td class="text-truncate">
                                        <p class="text-truncate">Listener LISTENER is enabled</p>
                                        <p class="text-truncate">Listener LISTENER is running on node(s):ora rac 3,ora rac 4</p>
                                    </td> 
                                    <td class="text-truncate">
                                        <p class="text-truncate">Listener LISTENER is enabled</p>
                                        <p class="text-truncate">Listener LISTENER is running on node(s):ora rac 3,ora rac 4</p>
                                    </td>
                                  
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Scan Listener Status">
                                        <span> <i class="text-secondary cp-cluster-database me-1 fs-6"></i>Scan Listener Status</span>

                                    </td>
                                      <td class="text-truncate">
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN1 is Enabled</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN1 is running on node ora rac 3</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN2 is Enabled</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN2 is running on node ora rac 4</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN3 is Enabled</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN3 is running on node ora rac 4</p>
                                    </td>
                                     <td class="text-truncate">
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN1 is Enabled</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN1 is running on node ora rac 3</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN2 is Enabled</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN2 is running on node ora rac 4</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN3 is Enabled</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN3 is running on node ora rac 4</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Scan Status">
                                        <span> <i class="text-secondary cp-cluster-database me-1 fs-6"></i>Scan Status</span>

                                    </td>
                                    <td class="text-truncate">
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN1 is Enabled</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN1 is running on node ora rac 3</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN2 is Enabled</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN2 is running on node ora rac 4</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN3 is Enabled</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN3 is running on node ora rac 4</p>
                                    </td>
                                     <td class="text-truncate">
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN1 is Enabled</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN1 is running on node ora rac 3</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN2 is Enabled</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN2 is running on node ora rac 4</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN3 is Enabled</p>
                                        <p class="text-truncate">SCAN Listener LISTENER_SCAN3 is running on node ora rac 4</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title" title="Pluggable Databases">Pluggable Databases</div>
                    <div class="card-body pt-0">
                        <div class="NoData text-center p-2 d-grid justify-content-center">
                            <img src="/img/isomatric/nodatalag.svg" class="mx-auto" />
                            <span class="text-danger">
                                Pluggable Databases
                                not configured.
                            </span>
                        </div>
                    </div>
                </div>
            </div>


            <div class="col-4 d-grid ">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span title="Instance Details">
                            Instance Details <span ></span>
                        </span>
                        @* <form>
                        <select id="instanceDetails" class="form-select" aria-label="Default select example">
                        <option disabled >Select Node Name</option>
                        </select>
                        </form>*@
                    </div>
                    <div class="card-body pt-0 p-2" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 275px);">
                        <table class="table mb-0" id="tableInstance" style="table-layout:fixed">
                            <thead class="position-sticky top-0">
                                <tr>
                                    <th title="Instance Details">Instance Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Instance Name"><span><i class="text-secondary cp-instance-name me-1 fs-6"></i>Instance Name</span></td>
                                    <td class="text-truncate">Qarel1</td>
                                    <td class="text-truncate">Qarel1</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Instance ID"><span><i class="text-secondary cp-instance-id me-1 fs-6"></i>Instance ID</span></td>
                                    <td class="text-truncate">1</td>
                                    <td class="text-truncate">1</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Instance Startup Time">
                                        <span>  <i class="text-secondary cp-table-clock me-1 fs-6"></i>Instance Startup Time</span>

                                    </td>
                                    <td class="text-truncate">
                                        30/04/2019
13:10:46
                                    </td>
                                    <td class="text-truncate">
                                      30/04/2019
13:10:46
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Open Mode"><span><i class="text-secondary cp-platform-name me-1 fs-6"></i>Open Mode</span></td>
                                    <td class="text-truncate">Read Write</td>
                                    <td class="text-truncate">Mounted</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Cluster Database">
                                        <span>  <i class="text-secondary cp-cluster-database me-1 fs-6"></i>Cluster Database</span>

                                    </td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1 fs-6"></i>NO</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1 fs-6"></i>NO</td>

                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Control File Name">
                                        <span><i class="text-secondary cp-control-file-type me-1 fs-6"></i>Control File Name</span>

                                    </td>
                                    <td class="text-truncate">
                                     +Data/
Qarel_STB
                                    </td>
                                    <td class="text-truncate">+Data/
Qarel_STB</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Parameter File"><span><i class="text-secondary cp-parameter-file me-1 fs-6"></i>Parameter File</span></td>
                                   <td class="text-truncate">
                                     +Data/
Qarel_STB
                                    </td>
                                    <td class="text-truncate">+Data/
Qarel_STB</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Platform Name"><span><i class="text-secondary cp-platform-name me-1 fs-6"></i>Platform Name</span></td>
                                    <td class="text-truncate">Linux x86 64bit</td>
                                    <td class="text-truncate">Linux x86 64bit</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="row">
                    <div class="col-12 d-grid ">
                        <div class="card Card_Design_None mb-2">
                            <div class="card-header card-title d-flex align-items-center justify-content-between">
                                <span title="Multi Tenancy">Multi Tenancy</span>
                            </div>
                            <div class="card-body pt-0 p-2">
                                <table class="table mb-0" style="table-layout:fixed">
                                    <thead>
                                        <tr>
                                            <th title="Multi Tenancy">Multi Tenancy</th>
                                            <th class="text-primary" title="Primary">Primary</th>
                                            <th class="" title="DR">DR</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="fw-semibold text-truncate" title="CDB"><i class="text-secondary cp-control-file-type me-1 fs-6"></i>CDB</td>
                                            <td class="text-truncate"><i class="text-danger cp-disable me-1 fs-6"></i>NO</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1 fs-6"></i>NO</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold text-truncate" title="Containers"><i class="text-secondary cp-dr me-1 fs-6"></i>Containers</td>
                                            <td class="text-truncate">Qarel</td>
                                            <td class="text-truncate">Qarel</td>
                                        </tr>
                                       

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 d-grid">
                        <div class="card Card_Design_None mb-0">
                            <div class="card-header card-title d-flex align-items-center justify-content-between">
                                <span title="TNS Service Details">
                                    TNS Service Details
                                </span>

                            </div>
                            <div class="card-body pt-0 p-2">
                                <table class="table mb-0" style="table-layout:fixed">
                                    <thead>
                                        <tr>
                                            <th title="TNS Service Details">TNS Service Details</th>
                                            <th class="text-primary" title="Primary">Primary</th>
                                            <th class="" title="DR">DR</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="fw-semibold text-truncate" title="Service"><i class="text-secondary cp-service me-1 fs-6"></i>Service</td>
                                            <td class="fw-semibold text-truncate" >Qarel_STB</td>
                                            <td class="fw-semibold text-truncate" >Qarel_STB</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
             <div class="col-4 d-grid">
            <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title d-flex align-items-center justify-content-between mb-2">
                        <span class="text-truncate d-inline-block" title="Archive Log Generation Hourly Last 24Hrs(Size)">
                            Archive Log Generation
                            Hourly Last 24Hrs(Size)
                        </span>

                    </div>
                    <div class="card-body pt-0">
                        <div class="d-flex" style="width:100%; height:250px;"></div>
                    </div>
                </div>
            </div>

            <!-- </div> -->

            <div class="col-12 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title" title="ASM Details">ASM Details </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th title="ASM">ASM</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Services">Services</td>
                                    <td class="text-truncate">
                                        <table class="table mb-0"  style="table-layout:fixed">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th title="Name">Name</th>
                                                    <th title="State">State</th>
                                                    <th title="Type">Type</th>
                                                    <th title="Total MB">Total MB</th>
                                                    <th title="Fee MB">Free MB</th>
                                                    <th title="Used(%)">Used(%)</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td class="text-truncate">1</td>
                                                    <td class="text-truncate">Data</td>
                                                    <td class="text-truncate">Mounted</td>
                                                    <td class="text-truncate">Extern</td>
                                                    <td class="text-truncate">15357</td>
                                                    <td class="text-truncate">4845</td>
                                                    <td class="text-truncate">31.549307</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-truncate">2</td>
                                                    <td class="text-truncate">Data</td>
                                                    <td class="text-truncate">Mounted</td>
                                                    <td class="text-truncate">Extern</td>
                                                    <td class="text-truncate">15357</td>
                                                    <td class="text-truncate">4845</td>
                                                    <td class="text-truncate">31.549307</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td class="text-truncate">
                                        <table class="table mb-0"  style="table-layout:fixed">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th title="Name">Name</th>
                                                    <th title="State">State</th>
                                                    <th title="Type">Type</th>
                                                    <th title="Total MB">Total MB</th>
                                                    <th title="Fee MB">Free MB</th>
                                                    <th title="Used(%)">Used(%)</th>
                                                </tr>
                                            </thead>
                                            <tbody >
                                                <tr>
                                                    <td class="text-truncate">1</td>
                                                    <td class="text-truncate">Data</td>
                                                    <td class="text-truncate">Mounted</td>
                                                    <td class="text-truncate">Extern</td>
                                                    <td class="text-truncate">15357</td>
                                                    <td class="text-truncate">4845</td>
                                                    <td class="text-truncate">31.549307</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-truncate">2</td>
                                                    <td class="text-truncate">Data</td>
                                                    <td class="text-truncate">Mounted</td>
                                                    <td class="text-truncate">Extern</td>
                                                    <td class="text-truncate">15357</td>
                                                    <td class="text-truncate">4845</td>
                                                    <td class="text-truncate">31.549307</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

       
        </div>
      

    </div>
</div>