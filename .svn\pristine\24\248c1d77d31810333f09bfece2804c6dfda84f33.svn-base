﻿namespace ContinuityPatrol.Application.Features.BusinessFunction.Commands.Update;

public class UpdateBusinessFunctionCommandValidator : AbstractValidator<UpdateBusinessFunctionCommand>
{
    private readonly IBusinessFunctionRepository _businessFunctionRepository;

    public UpdateBusinessFunctionCommandValidator(IBusinessFunctionRepository businessFunctionRepository)
    {
        _businessFunctionRepository = businessFunctionRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.Description)
            .MaximumLength(250).WithMessage("Operational Function {PropertyName} Maximum 250 characters.")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Operational Function {PropertyName} contains invalid characters.")
            .When(p => p.Description.IsNotNullOrWhiteSpace());


        RuleFor(p => p.CriticalityLevel)
            .NotEmpty().WithMessage("Select {PropertyName}.");
        //.Matches(@"^[A-Za-z]*$").WithMessage("Only alphabets are allowed.")
        //.NotNull();

        RuleFor(p => p.BusinessServiceName)
            .NotEmpty().WithMessage("Select Operational Service.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s\-]?)([a-zA-Z\d]+[_\s\-])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid Operational Service.");


        RuleFor(p => p.ConfiguredRTO)
            .NotEmpty().WithMessage("RTO is required.")
            .NotNull()
            .Matches(@"^[\d]{0,5}$").WithMessage("Please enter number only.");


        RuleFor(p => p.ConfiguredRPO)
            .NotEmpty().WithMessage("RPO is required.")
            .NotNull()
            .Matches("^[0-9]{0,5}$").WithMessage("Please enter number only.");

        RuleFor(p => p.ConfiguredMAO)
            .NotEmpty().WithMessage("MAO is required.")
            .NotNull()
            .Matches("^[0-9]{0,5}$").WithMessage("Please enter number only.");

        RuleFor(e => e)
            .MustAsync(BusinessFunctionNameUnique)
            .WithMessage("A same name already exists.");
        RuleFor(e => e)
            .MustAsync(CheckRpoThreshold)
            .WithMessage("The ConfiguredRPO is less than the RPOThreshold.");

        RuleFor(p => p)
            .NotNull()
            .MustAsync(IsValidGUID).WithMessage("Invalid id");
    }

    private async Task<bool> BusinessFunctionNameUnique(UpdateBusinessFunctionCommand e, CancellationToken token)
    {
        return !await _businessFunctionRepository.IsBusinessFunctionNameExist(e.Name, e.Id);
    }

    private Task<bool> CheckRpoThreshold(UpdateBusinessFunctionCommand e, CancellationToken token)
    {
        if (!long.TryParse(e.ConfiguredRPO, out var configuredRPO))
            throw new FormatException("ConfiguredRPO is not a valid number.");

        if (!long.TryParse(e.RPOThreshold, out var rpoThreshold))
            throw new FormatException("RPOThreshold is not a valid number.");
        return Task.FromResult(e.ConfiguredRPO.ToInt64() >= e.RPOThreshold.ToInt64());
    }

    private Task<bool> IsValidGUID(UpdateBusinessFunctionCommand p, CancellationToken token)
    {
        Guard.Against.InvalidGuidOrEmpty(p.BusinessServiceId, "operational service id");
        Guard.Against.InvalidGuidOrEmpty(p.Id, "operational function id");
        return Task.FromResult(true);
    }
}