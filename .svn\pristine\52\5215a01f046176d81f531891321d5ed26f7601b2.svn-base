using ContinuityPatrol.Application.Features.CyberSnaps.Commands.Create;
using ContinuityPatrol.Application.Features.CyberSnaps.Commands.Update;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetPowerMax;
using ContinuityPatrol.Domain.ViewModels.CyberSnapsModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ICyberSnapsService
{
    Task<List<CyberSnapsListVm>> GetCyberSnapsList();
    Task<BaseResponse> CreateAsync(CreateCyberSnapsCommand createCyberSnapsCommand);
    Task<BaseResponse> UpdateAsync(UpdateCyberSnapsCommand updateCyberSnapsCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<CyberSnapsDetailVm> GetByReferenceId(string id);

    Task<List<CyberSnapsListVm>> GetCyberSnapsByStorageGroupNameAndLinkedStatus(string storageGroupName,
        string linkedStatus, string snapshotName);
    Task<List<PowerMaxDetailVm>> GetPowerMaxMonitorStatus(string name, bool isSnap);
    #region NameExist
    Task<bool> IsCyberSnapsNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<CyberSnapsListVm>> GetPaginatedCyberSnapss(GetCyberSnapsPaginatedListQuery query);
    #endregion
}
