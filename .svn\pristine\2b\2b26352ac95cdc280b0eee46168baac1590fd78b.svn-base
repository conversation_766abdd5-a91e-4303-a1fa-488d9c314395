﻿using ContinuityPatrol.Application.Features.User.Events.UserUnLock;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;

namespace ContinuityPatrol.Application.Features.User.Commands.UserUnLock;

public class UserUnLockCommandHandler : IRequestHandler<UserUnLockCommand, UserUnLockResponse>
{
    private readonly IConfiguration _config;
    private readonly IEmailService _emailService;
    private readonly IGlobalSettingRepository _globalSettingRepository;
    private readonly ILogger<UserUnLockCommandHandler> _logger;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly ISmtpConfigurationRepository _smtpConfigurationRepository;
    private readonly IUserInfoRepository _userInfoRepository;
    private readonly IUserLoginRepository _userLoginRepository;
    private readonly IUserRepository _userRepository;

    public UserUnLockCommandHandler(IMapper mapper, IEmailService emailService, IPublisher publisher,
        IUserRepository userRepository, IUserLoginRepository userLoginRepository,
        ISmtpConfigurationRepository smtpConfigurationRepository, IConfiguration configuration,
        IUserInfoRepository userInfoRepository, ILogger<UserUnLockCommandHandler> logger,
        IGlobalSettingRepository globalSettingRepository)
    {
        _mapper = mapper;
        _userRepository = userRepository;
        _userLoginRepository = userLoginRepository;
        _smtpConfigurationRepository = smtpConfigurationRepository;
        _emailService = emailService;
        _publisher = publisher;
        _config = configuration;
        _userInfoRepository = userInfoRepository;
        _logger = logger;
        _globalSettingRepository = globalSettingRepository;
    }

    public async Task<UserUnLockResponse> Handle(UserUnLockCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _userRepository.GetByReferenceIdAsync(request.UserId);

        var globalSetting = await _globalSettingRepository.GlobalSettingBySettingKey("Email Notification");

        if (globalSetting is null || globalSetting.GlobalSettingValue.Equals("false"))
        {
            _logger.LogWarning("The email notification feature is not enabled in the global settings.");

            throw new InvalidException("The email notification feature is not enabled in the global settings.");
        }

        var smtpConfiguration = (await _smtpConfigurationRepository.ListAllAsync()).LastOrDefault();

        if (smtpConfiguration is null) throw new InvalidException("Please configure smtp.");

        Guard.Against.NullOrDeactive(eventToUpdate, nameof(Domain.Entities.User),
            new NotFoundException(nameof(Domain.Entities.User), request.UserId));

        var userLogInDtl = await _userLoginRepository.GetUserLoginByUserId(request.UserId);

        Guard.Against.NullOrDeactive(userLogInDtl, nameof(Domain.Entities.UserLogin),
            new NotFoundException(nameof(Domain.Entities.UserLogin), request.UserId));

        eventToUpdate.IsLock = false;

        userLogInDtl.InvalidLoginAttempt = 0;

        await _userRepository.UpdateAsync(eventToUpdate);

        await _userLoginRepository.UpdateAsync(userLogInDtl);

        var version = _config.GetValue<string>("CP:Version");

        var userInfo = await _userInfoRepository.GetUserInfoByUserIdAsync(request.UserId);

        var body = EmailTemplateHelper.GetAccountUnlockedEmailBody(eventToUpdate, version);

        var imageNames = new List<string>
        {
            "abstract.png",
            "cp_logo.png",
            "unlocked_image.png"
        };

        var htmlView = HtmlEmailBuilder.BuildHtmlView(body, imageNames, "UserUnLock");

        var emailDto = _mapper.Map<EmailDto>(smtpConfiguration);
        emailDto.To = userInfo.Email;
        emailDto.Subject = "User Account Unlocked.";
        emailDto.HtmlBody = htmlView;

        await _emailService.SendEmail(emailDto);

        var response = new UserUnLockResponse
        {
            UserId = eventToUpdate.ReferenceId,
            Message = $"User '{eventToUpdate.LoginName}' unlocked successfully!."
        };

        await _publisher.Publish(new UserUserUnLockEvent { UserName = eventToUpdate.LoginName }, cancellationToken);

        return response;
    }
}