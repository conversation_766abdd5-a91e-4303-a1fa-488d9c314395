﻿namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class SmtpConfigurationController : BaseController
{
    private readonly ILogger<SmtpConfigurationController> _logger;

    public SmtpConfigurationController(ILogger<SmtpConfigurationController> logger)
    {
        _logger = logger;
    }

    public IActionResult List()
    {
        _logger.LogDebug("Entering List method in SmtpConfiguration");

        return View();
    }
}