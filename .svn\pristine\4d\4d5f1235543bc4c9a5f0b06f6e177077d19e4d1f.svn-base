﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller;

public class AlertMasterControllerShould
{
    private readonly AlertMasterController _controller;

    public AlertMasterControllerShould()
    {
        _controller = new AlertMasterController();
    }

    [Fact]
    public void List_ReturnsViewResult()
    {
        var result = _controller.List();
            
        Assert.NotNull(result);
        Assert.IsType<ViewResult>(result);
            
    }
}