﻿using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.MSSQLAlwaysOnMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.MSSQLAlwaysOnMonitorLogs.Queries;

public class GetMssqlAlwaysOnMonitorLogsPaginatedListQueryHandlerTests : IClassFixture<MssqlAlwaysOnMonitorLogsFixture>
{
    private readonly GetMSSQLAlwaysOnMonitorLogsPaginatedListQueryHandler _handler;

    private readonly Mock<IMssqlAlwaysOnMonitorLogsRepository> _mockMssqlAlwaysOnMonitorLogsRepository;

    public GetMssqlAlwaysOnMonitorLogsPaginatedListQueryHandlerTests(MssqlAlwaysOnMonitorLogsFixture mssqlAlwaysOnMonitorLogsFixture)
    {
        var mssqlAlwaysOnMonitorLogsNewFixture = mssqlAlwaysOnMonitorLogsFixture;

        mssqlAlwaysOnMonitorLogsNewFixture.MssqlAlwaysOnMonitorLogs[0].WorkflowName = "WF_ActionName";
        mssqlAlwaysOnMonitorLogsNewFixture.MssqlAlwaysOnMonitorLogs[0].InfraObjectName = "Infra_Base";
        mssqlAlwaysOnMonitorLogsNewFixture.MssqlAlwaysOnMonitorLogs[0].Type = "MSSQL";
        mssqlAlwaysOnMonitorLogsNewFixture.MssqlAlwaysOnMonitorLogs[0].Properties = "{\"Name\": \"operator\", \"password\": \"Common@123\"}";

        mssqlAlwaysOnMonitorLogsNewFixture.MssqlAlwaysOnMonitorLogs[1].WorkflowName = "WF_Name";
        mssqlAlwaysOnMonitorLogsNewFixture.MssqlAlwaysOnMonitorLogs[1].InfraObjectName = "Infra_Test";
        mssqlAlwaysOnMonitorLogsNewFixture.MssqlAlwaysOnMonitorLogs[1].Type = "MYSQL";
        mssqlAlwaysOnMonitorLogsNewFixture.MssqlAlwaysOnMonitorLogs[1].Properties = "{\"Name\": \"admin\", \"password\": \"Admin@123\"}";



        _mockMssqlAlwaysOnMonitorLogsRepository = MssqlAlwaysOnMonitorLogsRepositoryMocks.GetPaginatedMssqlAlwaysOnMonitorLogsRepository(mssqlAlwaysOnMonitorLogsNewFixture.MssqlAlwaysOnMonitorLogs);

        _handler = new GetMSSQLAlwaysOnMonitorLogsPaginatedListQueryHandler(_mockMssqlAlwaysOnMonitorLogsRepository.Object, mssqlAlwaysOnMonitorLogsNewFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetMSSQLAlwaysOnMonitorLogsPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Replication_Master" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<MssqlAlwaysOnMonitorLogsListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedTeamResources_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetMSSQLAlwaysOnMonitorLogsPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Infra_Base" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<MssqlAlwaysOnMonitorLogsListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<MssqlAlwaysOnMonitorLogsListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].WorkflowName.ShouldBe("WF_ActionName");

        result.Data[0].InfraObjectName.ShouldBe("Infra_Base");

        result.Data[0].Type.ShouldBe("MSSQL");

        result.Data[0].Properties.ShouldBe("{\"Name\": \"operator\", \"password\": \"Common@123\"}");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetMSSQLAlwaysOnMonitorLogsPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<MssqlAlwaysOnMonitorLogsListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_TeamResources_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetMSSQLAlwaysOnMonitorLogsPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "workflowname=WF_ActionName;infraobjectname=Infra_Base;type=MSSQL;properties={\"Name\": \"operator\", \"password\": \"Common@123\"}" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<MssqlAlwaysOnMonitorLogsListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].WorkflowName.ShouldBe("WF_ActionName");

        result.Data[0].InfraObjectName.ShouldBe("Infra_Base");

        result.Data[0].Type.ShouldBe("MSSQL");

        result.Data[0].Properties.ShouldBe("{\"Name\": \"operator\", \"password\": \"Common@123\"}");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetMSSQLAlwaysOnMonitorLogsPaginatedListQuery(), CancellationToken.None);

        _mockMssqlAlwaysOnMonitorLogsRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}