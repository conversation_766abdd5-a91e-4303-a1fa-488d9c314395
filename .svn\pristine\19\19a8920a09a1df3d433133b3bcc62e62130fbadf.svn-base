﻿using ContinuityPatrol.Application.Features.Database.Commands.Create;
using ContinuityPatrol.Application.Features.Database.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Database.Commands.Update;
using ContinuityPatrol.Application.Features.Database.Events.Create;
using ContinuityPatrol.Application.Features.Database.Events.Delete;
using ContinuityPatrol.Application.Features.Database.Events.InfraSummaryEvents.Create;
using ContinuityPatrol.Application.Features.Database.Events.InfraSummaryEvents.Delete;
using ContinuityPatrol.Application.Features.Database.Events.InfraSummaryEvents.Update;
using ContinuityPatrol.Application.Features.Database.Events.LicenseInfoEvents.Create;
using ContinuityPatrol.Application.Features.Database.Events.LicenseInfoEvents.Delete;
using ContinuityPatrol.Application.Features.Database.Events.LicenseInfoEvents.Update;
using ContinuityPatrol.Application.Features.Database.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Database.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class DatabaseFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<Database> Databases { get; set; }
    public Server Server { get; set; }
    public List<LicenseManager> LicenseManagers { get; set; }

    public CreateDatabaseCommand CreateDatabaseCommand { get; set; }
    public UpdateDatabaseCommand UpdateDatabaseCommand { get; set; }
    public SaveAsDatabaseCommand SaveAsDatabaseCommand { get; set; }

    public DatabaseCreatedEvent DatabaseCreatedEvent { get; set; }
    public DatabaseDeletedEvent DatabaseDeletedEvent { get; set; }
    public DatabaseUpdatedEvent DatabaseUpdatedEvent { get; set; }
    public DatabasePaginatedEvent DatabasePaginatedEvent { get; set; }

    public DatabaseInfraSummaryCreatedEvent DatabaseInfraSummaryCreatedEvent { get; set; }
    public DatabaseInfraSummaryDeletedEvent DatabaseInfraSummaryDeletedEvent { get; set; }
    public DatabaseInfraSummaryUpdatedEvent DatabaseInfraSummaryUpdatedEvent { get; set; }

    public DatabaseLicenseInfoCreatedEvent DatabaseLicenseInfoCreatedEvent { get; set; }
    public DatabaseLicenseInfoDeletedEvent DatabaseLicenseInfoDeletedEvent { get; set; }
    public DatabaseLicenseInfoUpdatedEvent DatabaseLicenseInfoUpdatedEvent { get; set; }

    public DatabaseFixture()
    {
        Databases = AutoDatabaseFixture.Create<List<Database>>();
        Server = AutoDatabaseFixture.Create<Server>();
        LicenseManagers = AutoDatabaseFixture.Create<List<LicenseManager>>();

        CreateDatabaseCommand = AutoDatabaseFixture.Create<CreateDatabaseCommand>();
        UpdateDatabaseCommand = AutoDatabaseFixture.Create<UpdateDatabaseCommand>();
        SaveAsDatabaseCommand = AutoDatabaseFixture.Create<SaveAsDatabaseCommand>();

        DatabaseCreatedEvent = AutoDatabaseFixture.Create<DatabaseCreatedEvent>();
        DatabaseDeletedEvent = AutoDatabaseFixture.Create<DatabaseDeletedEvent>();
        DatabaseUpdatedEvent = AutoDatabaseFixture.Create<DatabaseUpdatedEvent>();
        DatabasePaginatedEvent = AutoDatabaseFixture.Create<DatabasePaginatedEvent>();

        DatabaseInfraSummaryCreatedEvent = AutoDatabaseFixture.Create<DatabaseInfraSummaryCreatedEvent>();
        DatabaseInfraSummaryDeletedEvent = AutoDatabaseFixture.Create<DatabaseInfraSummaryDeletedEvent>();
        DatabaseInfraSummaryUpdatedEvent = AutoDatabaseFixture.Create<DatabaseInfraSummaryUpdatedEvent>();

        DatabaseLicenseInfoCreatedEvent = AutoDatabaseFixture.Create<DatabaseLicenseInfoCreatedEvent>();
        DatabaseLicenseInfoDeletedEvent = AutoDatabaseFixture.Create<DatabaseLicenseInfoDeletedEvent>();
        DatabaseLicenseInfoUpdatedEvent = AutoDatabaseFixture.Create<DatabaseLicenseInfoUpdatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<DatabaseProfile>();
        });

        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoDatabaseFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateDatabaseCommand>(p => p.Name, 10));
            fixture.Customize<CreateDatabaseCommand>(c => c.With(b => b.ServerId, 0.ToString()));
            fixture.Customize<CreateDatabaseCommand>(c => c.With(b => b.Properties, "{\"Name\": \"admin\", \"password\": \"Admin@123\"}"));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateDatabaseCommand>(p => p.Name, 10));
            fixture.Customize<UpdateDatabaseCommand>(c => c.With(b => b.Id, 0.ToString()));
            fixture.Customize<UpdateDatabaseCommand>(c => c.With(b => b.Properties, "{\"Name\": \"admin\", \"password\": \"Admin@123\"}"));
            fixture.Customize<Database>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<DatabaseCreatedEvent>(p => p.DatabaseName, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<DatabaseDeletedEvent>(p => p.DatabaseName, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<DatabaseUpdatedEvent>(p => p.DatabaseName, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<DatabasePaginatedEvent>(p => p.DatabaseName, 10));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}