﻿function clearSelectAndTriggerChange(selectElementId, element) {
    $("#" + selectElementId).val('').trigger('change');
    $('#' + element).text('').removeClass('field-validation-error');
}

const loadDynamicSites = async () => {

    $('#infraDynamicBody')?.children('tr')?.slice(2, -2)?.remove();

    let getReplicationName = $('#ddlReplicationTypeNameId option:selected')?.text();
    let isSrmFound = getReplicationName && ReplicationtypeName?.toLowerCase()?.replace(/ /g, "")?.includes('srmvmware');
    let isRedisFound = getReplicationName && getReplicationName?.toLowerCase()?.replace(/ /g, "")?.includes('redis')
    let oracleFound = getOracleFound();
    let mssqlFound = getMssqlFound();

    let html = '';
    for (let i = 0; i < siteProperties?.length; i++) {

        if (siteProperties[i]?.category?.toLowerCase() === 'dr') {

            $('#DRCol, #dr_summary_header').show();
            $('#DRCol td:first').text(siteProperties[i]?.name);
            $('#DRSelectDatabaseNamesError, #DRSelectServerNamesError').text('').removeClass('field-validation-error');

            if (isSrmFound) {
                $('#drSrm').show();
                SetSrmServerType(activeType, 'dresxiappserver', 'dr');
            }

            if (oracleFound || mssqlFound) {
                $('#DRServerBody, #DRDatabaseBody').addClass('d-none')
                $('#DRMultipleServerBody, #DRMultipleDatabaseBody').removeClass('d-none')
            } else {
                $('#DRServerBody, #DRDatabaseBody').removeClass('d-none')
                $('#DRMultipleServerBody, #DRMultipleDatabaseBody').addClass('d-none')
            }

            if (mssqlFound) $('#PRReplication,#DRReplication,#tablereplication').addClass('d-none')
            else $('#PRReplication,#DRReplication,#tablereplication').removeClass('d-none')

        } else if (siteProperties[i]?.category?.toLowerCase() === 'primary') {
            $('#Prtable td:first').text(siteProperties[i]?.name);
        } else {
            let dynamicId = siteProperties[i]?.id;
            let dynamicName = siteProperties[i]?.name;

            html += '<tr class="align-middle">';
            html += `<td>${dynamicName}</td>`;

            // server append
            html += `<td><div class="form-group"><div class="input-group singleServer"><span class="input-group-text"><i class="cp-server"></i></span>`;
            html += `<select id='${dynamicId}ServerName' class="form-select-modal dynamic_infra dynamic_select" data-type="server" data-dynamicName='${dynamicId}' data-siteType='${dynamicName}' data-placeholder="Select Server">`;

            html += '<option value="" hidden selected>Select Server</option>'
            html += await SetDynamicServerType("Configuration/InfraObject/GetServerRoleTypeAndServerType", 'dynamic', `${dynamicId}`, 'server', '', `${isSrmFound ? 'dresxiserver' : 'DRDBServer'}`, isSrmFound);

            html += `</select></div>`;

            html += `<div class="input-group multipleServer d-none" id='${dynamicId}MultipleServerBody'><span class="input-group-text"><i class="cp-server"></i></span>`;
            html += `<select class="form-select-modal dynamic_infra dynamic_select" id='${dynamicId}MultipleServer' data-type="multipleserver" data-dynamicName='${dynamicId}' data-siteType='${dynamicName}' data-placeholder="Select Server" multiple>`;
            html += `<option value="" hidden selected>Select Server</option>`

            html += await SetDynamicServerType("Configuration/InfraObject/GetServerRoleTypeAndServerType", 'dynamic', `${dynamicId}`, 'multipleserver');

            html += `</select></div><span id="${dynamicId}ServerNameError"></span></div></td>`;

            // srm server data
            html += `<td id='${dynamicId}Srm' class='d-none dynamicSrm'><div class="form-group"><div class="input-group"><span class="input-group-text"><i class="cp-server"></i></span>`;
            html += `<select id='${dynamicId}SrmServer' class="form-select-modal dynamic_infra srmServer" data-type="server" name='SRMServer' data-dynamicName='${dynamicId}' data-siteType='${dynamicName}' data-placeholder="Select Server">`;

            html += '<option value="" hidden selected>Select Server</option>'
            html += await SetDynamicServerType("Configuration/InfraObject/GetServerRoleTypeAndServerType", 'dynamic', `${dynamicId}`, 'server', '', 'dresxiappserver', isSrmFound);

            html += `</select></div>`;
            html += `<span id="${dynamicId}SrmServerError"></span></div></td>`;

            // database append
            html += `<td class='dynamicDatabase'><div class="form-group"><div class="input-group singleDatabase"><span class="input-group-text"><i class="cp-database"></i></span>`;
            html += `<select id='${dynamicId}DatabaseName' class="form-select-modal dynamic_infra dynamic_select dynamic_database" data-type="database" data-dynamicName='${dynamicId}' data-siteType='${dynamicName}' data-placeholder="Select Database">`;
            html += '<option value="" hidden selected>Select Database</option>'
            html += `</select></div>`;

            html += `<div class="input-group multipleDatabase d-none" id='${dynamicId}MultipleDatabaseBody'><span class="input-group-text"><i class="cp-server"></i></span>`;
            html += `<select class="form-select-modal dynamic_infra dynamic_select" id='${dynamicId}MultipleDatabase' data-type="multipledatabase" data-dynamicName='${dynamicId}' data-siteType='${dynamicName}' data-placeholder="Select Database" multiple>`;
            html += `<option value="" hidden selected>Select Database</option>`

            html += `</select></div><span id="${dynamicId}DatabaseNameError"></span></div></td>`;

            // replication append
            html += `<td class='dynamicReplication'><div class="form-group"><div class="input-group" style="width: 350px !important;"><span class="input-group-text"><i class="cp-replication-on"></i></span>`;
            html += `<select id='${dynamicId}ReplicationName' class="form-select-modal dynamic_infra dynamic_select dynamic_replication" data-type="replication" data-dynamicName='${dynamicId}' data-siteType='${dynamicName}' data-placeholder="Select Replication">`;

            html += '<option value="" hidden selected>Select Replication</option>'
            html += await getReplicationList("Configuration/InfraObject/GetReplicationList", 'replication');

            html += `</select></div>`;
            html += `<span id="${dynamicId}ReplicationNameError"></span></div></td>`;
        }
    }

    let findDrIndex = siteProperties?.length && siteProperties?.findIndex((site) => site?.category?.toLowerCase() === 'dr')

    if (findDrIndex === -1) {
        if (serverProperties?.hasOwnProperty('DR')) delete serverProperties['DR'];
        if (databaseProperties?.hasOwnProperty('DR')) delete databaseProperties['DR'];
        if (replicationProperties?.hasOwnProperty('DR')) delete replicationProperties['DR'];
        if (serverProperties?.hasOwnProperty('SRMServer')) {
            serverProperties['SRMServer'] = serverProperties?.SRMServer && serverProperties?.SRMServer?.length && serverProperties?.SRMServer?.filter((item) => item?.type !== 'DR')
        }

        $('#serverProperties').val(JSON.stringify(serverProperties))
        $('#databaseProperties').val(JSON.stringify(databaseProperties))
        $('#replicationProperties').val(JSON.stringify(replicationProperties))

        $("#DRCol,#drSrm, #dr_summary_header").hide();
        clearSelectAndTriggerChange("DRSelectServerNames", 'DRSelectServerNamesError');
        clearSelectAndTriggerChange("DRMultipleServer", 'DRSelectServerNamesError');
        clearSelectAndTriggerChange("DRMultipleDatabase", 'DRSelectDatabaseNamesError');
        clearSelectAndTriggerChange("DRSelectDatabase", 'DRSelectDatabaseNamesError');
        clearSelectAndTriggerChange("SelectReplicationNames", 'DRSelectReplicationNamesError');
    }

    $('#infraDynamicBody tr:nth-child(2)').after(html);

    if ($('#Activetype').val() === "1" || $('#Activetype').val() === "3") {
        $(".dynamicDatabase").hide();
        $('.dynamic_database').val("");
    } else {
        $(".dynamicDatabase").show();
    }

    if ((ReplicationCategory?.toLowerCase()?.replace(/ /g, "") === "application-noreplication") || (ReplicationCategory?.toLowerCase()?.replace(/ /g, "") === "database-noreplication")
        || isRedisFound) {
        $(".dynamicReplication").hide();
        $('.dynamic_replication').val("");
    } else {
        $(".dynamicReplication").show();
    }

    if (!isSrmFound) {
        $('#tableServer').text('Server')
        $('.dynamicSrm').addClass('d-none');
    } else {
        $('#tableServer').text('EXSI Server')
        $('.dynamicSrm').removeClass('d-none');
        $('.dynamicReplication').hide();
        $('.dynamic_replication').val("");
    }

    if (oracleFound) {
        $('.singleServer, .singleDatabase').addClass('d-none');
        $('.multipleServer, .multipleDatabase').removeClass('d-none');
    }

    Object.keys(serverProperties)?.length && Object.keys(serverProperties).map((server) => {
        if (typeof serverProperties[server] == 'object' && serverProperties[server]?.type === 'customserver' && server !== 'clusters') {
            if (!$(`#${serverProperties[server]?.siteId}ServerName`).length) delete serverProperties[server]
            else if (!$(`#${serverProperties[server]?.siteId}MultipleServer`)?.length) delete serverProperties[server]
        }

        if (server?.toLowerCase() === 'srmserver') {
            serverProperties?.SRMServer?.length && serverProperties?.SRMServer?.forEach((srm, index) => {
                if (srm?.type !== 'PR' && srm?.type !== 'DR' && !$(`#${srm?.type}SrmServer`).length) serverProperties?.SRMServer?.splice(index, 1)
            })
        }
    })

    Object.keys(databaseProperties)?.length && Object.keys(databaseProperties).map((database) => {
        if (databaseProperties[database]?.type === 'customdatabase') {

            if (!$(`#${databaseProperties[database]?.siteId}DatabaseName`).length) delete databaseProperties[database]
            else if (!$(`#${databaseProperties[database]?.siteId}MultipleDatabase`).length) delete databaseProperties[database]
        }
    })

    Object.keys(replicationProperties)?.length && Object.keys(replicationProperties).map((replication) => {
        if (replicationProperties[replication]?.type === 'customreplication' && !$(`#${replicationProperties[replication]?.siteId}ReplicationName`)?.length) delete replicationProperties[replication]
    })

    setTimeout(() => {

        $('.dynamic_infra').selectize({
            normalize: true,
            openOnFocus: false,
            create: false,
            createOnBlur: true,
            closeAfterSelect: true,
            width: '100%',
            score: function (search) {
                let score = this.getScoreFunction(search);
                return function (item) {
                    return score(item) + (item?.text?.toLowerCase()?.indexOf(search?.toLowerCase()) + 1) * 1000;
                };
            },

            onDropdownOpen: function ($dropdown) {
                let currrentDrop = $dropdown?.parent()?.siblings('select')[0]?.selectize
                $('.selectize-dropdown').each(function () {
                    let selectizeInstance = $(this).parent().siblings('select')[0]?.selectize;
                    if (selectizeInstance && selectizeInstance?.isOpen && selectizeInstance !== currrentDrop) {
                        selectizeInstance.close();
                    }
                });
            },

            onItemAdd: function (value, $item) {
                let selectize = this;
                let selectedValues = selectize?.items;
                setTimeout(function () {
                    selectize?.setValue(selectedValues, true);
                }, 0);
            }
        });

        $('.dynamic_infra').css('width', '100%');

        if (isEdit) {

            Object.keys(serverProperties)?.length && Object.keys(serverProperties).forEach((server) => {
                if (serverProperties[server] && serverProperties[server]?.type == 'customserver') {
                    if (oracleFound && $(`#${serverProperties[server]?.siteId}MultipleServer`)?.length) {

                        multipleServerDetails[server] = serverProperties[server]?.id?.split(',') || [];

                        multipleServerDetails[server]?.length && multipleServerDetails[server].forEach((data) => {
                            SetDynamicDatabaseType("Configuration/InfraObject/GetDatabase", 'dynamic', serverProperties[server]?.siteId, 'multipleDatabase', data)
                        })

                        $(`#${serverProperties[server]?.siteId}MultipleServer`)[0]?.selectize?.setValue(multipleServerDetails[server]);

                    } else {
                        if ($(`#${serverProperties[server]?.siteId}ServerName`)?.length) $(`#${serverProperties[server]?.siteId}ServerName`)[0]?.selectize?.setValue(serverProperties[server]?.id);
                    }
                } else if (server?.toLowerCase() == 'srmserver') {

                    serverProperties?.SRMServer?.length && serverProperties?.SRMServer.forEach((srm) => {
                        if (srm?.type !== 'PR' && srm?.type !== 'DR') {
                            if ($(`#${srm?.type}SrmServer`)[0]) $(`#${srm?.type}SrmServer`)[0]?.selectize?.setValue(srm?.id);
                        }
                    })
                }
            })

            Object.keys(replicationProperties)?.length && Object.keys(replicationProperties).forEach((replication) => {
                if (replicationProperties[replication] && $(`#${replicationProperties[replication]?.siteId}ReplicationName`)?.length && replicationProperties[replication]?.type) {
                    $(`#${replicationProperties[replication]?.siteId}ReplicationName`)[0]?.selectize?.setValue(replicationProperties[replication]?.id);
                }
            })
        }

        form.steps('next');
    }, 200);
}

$(function () { 

    $("#Next").on('click', commonDebounce(async function () {

        let currentLi = $('.steps ul li.current')
        let getReplicationName = $('#ddlReplicationTypeNameId option:selected').text();
        let isSrmFound = getReplicationName && ReplicationtypeName?.toLowerCase()?.replace(/ /g, "")?.includes('srmvmware')
        let isOracleFound = getOracleFound();
        let ismssqlFound = getMssqlFound()
        let isClusterFound = getReplicationName && clusterReplicationData && clusterReplicationData?.some((rep) => (ReplicationtypeName?.toLowerCase()?.replace(/ /g, "")?.includes(rep)))

        const activeType = $("#Activetype").val();
        const pairInfraChecked = $('#PairId').prop('checked');
        const associateChecked = $('#InfraId').prop('checked');

        if (currentLi?.hasClass('first')) {
            const validations = await Promise.all([
                validateName($("#textName").val(), $('#infraNameId').val()),
                validateDescription($("#Description").val(), "Between 3 to 250 characters", $('#DescriptionError')),
                validateDropDown($("#infraBusinessServiceId").val(), ' Select operational service ', 'BusinessServiceError'),
                validateDropDown($("#ddlbusinessFunctionId").val(), ' Select  operational function', 'BusinessFunctionError'),
                $("#ReplicationTypeInput").is(":visible") ? validateDropDown($("#ddlReplicationTypeNameId").val(), ' Select replication type', 'SelectReplicationError') : true,
                validateDropDown(siteProperties, ' Select site type', 'siteTypeError'),
                validateDropDown($("#SelectReplicationType").val(), "Select replication category", "SelectReplicationTypeError"),
                activeType === "2" ? validateDropDown($("#SelectDatabaseType").val(), "Select database type ", "DatbaseTypeError") : true,
                validateDropDown(activeType, ' Select activity type', "SelectActiveTypeError"),
                pairInfraChecked ? await validateDropDown($("#SelectPairInfra").val(), ' Select pair infraobject', "SelectPairInfraError") : true,
                associateChecked ? validateDropDown($("#SelectAssociate").val(), ' Select associate infraobject', "SelectAssociateError") : true
            ]);

            const isAllValid = validations.every(Boolean);

            if (isAllValid) {
                loadDynamicSites();
                dynamicErrorElements.forEach((id) => $(id)?.text('')?.removeClass('field-validation-error'));

                if (!isSrmFound) {
                    $('#tablesrm, #prSrm, #drSrm, #nearSrm').hide();
                } else {
                    $("#NearReplication,#NearReplication, #DRReplication, #PRReplication,#tablereplication").hide();
                    $('#SelectReplicationNames').val("");
                }

                if (!isClusterFound && !isSrmFound) $('#clusterCol, #selectedClusterCol, #clusterProperties').hide();

                if (isOracleFound) {
                    $('#PRServerBody, #PRDatabaseBody').addClass('d-none')
                    $('#PRMultipleServerBody, #PRMultipleDatabaseBody').removeClass('d-none')
                }else {
                    $('#PRServerBody, #PRDatabaseBody').removeClass('d-none')
                    $('#PRMultipleServerBody, #PRMultipleDatabaseBody').addClass('d-none')
                }

                if (isOracleFound || ismssqlFound) {
                    $('#DRServerBody, #DRDatabaseBody').addClass('d-none')
                    $('#DRMultipleServerBody, #DRMultipleDatabaseBody').removeClass('d-none')
                }else {
                   $('#DRServerBody, #DRDatabaseBody').removeClass('d-none')
                    $('#DRMultipleServerBody, #DRMultipleDatabaseBody').addClass('d-none')
                }

                if (ismssqlFound) $('#PRReplication,#DRReplication,#tablereplication').addClass('d-none')
                else $('#PRReplication,#DRReplication,#tablereplication').removeClass('d-none')

                populateSummary();
            }
        } else {

            const selectedServer = $(".PRServerName").val() || $("#PRMultipleServer").val();
            const selectedDatabase = $(".PRDatabaseName").val() || $("#PRMultipleDatabase").val();
            const selectedDRServer = $(".DRServerName").val() || $("#DRMultipleServer").val();
            const selectedDRDatabase = $(".DRDatabaseName").val() || $("#DRMultipleDatabase").val();

            if (getReplicationName === "Native Replication-Oracle-Rac" && $("#DRCol").is(":visible")) {
                approverLists(selectedServer, selectedDRServer)
            }

            const validations = [
                validateDropDown(selectedServer, ' Select server ', 'SelectServerNameError'),
                $('#PRReplication').is(':visible') ? validateDropDown($("#PRSelectReplicationName").val(), ' Select replication ', 'SelectReplicationNameError') : true,
                $('#prdatabase').is(':visible') ? validateDropDown(selectedDatabase, ' Select database', 'PRSelectDatabaseError') : true,
                $('#DRCol').is(':visible') ? [
                    $('#DRReplication').is(':visible') ? validateDropDown($("#SelectReplicationNames").val(), ' Select replication ', 'DRSelectReplicationNamesError') : true,
                    $('#drdatabase').is(':visible') ? validateDropDown(selectedDRDatabase, 'Select database', 'DRSelectDatabaseNamesError') : true,
                    validateDropDown(selectedDRServer, ' Select server ', 'DRSelectServerNamesError')
                ] : true,
                isSrmFound ? [
                    $("#prSrm").is(":visible") ? validateDropDown($("#prSrmServer").val(), ' Select server', 'prSrmServerError') : true,
                    $("#drSrm").is(":visible") ? validateDropDown($("#drSrmServer").val(), ' Select server', 'drSrmServerError') : true
                ] : true,
                (isClusterFound || isSrmFound) ? [
                    $("#clusterProperties").is(":visible") ? validateDropDown($("#clusterType").val(), ' Select server', 'clusterTypeError') : true,
                    $("#veritas-child").is(":visible") ? validateDropDown($("#clusterPR").val(), ' Select server', 'clusterPRError') : true,
                    $("#HACMP-child").is(":visible") ? validateDropDown($("#clusterDR").val(), ' Select server', 'clusterDRError') : true
                ] : true,
                checkDynamicDataValidation()
            ].flat();

            const isAllValid = validations.every(Boolean);

            if (isAllValid) {

                if (getReplicationName === "Native Replication-Oracle-Rac" && $("#DRCol").is(":visible")) {
                    $('#NewModel').modal('show');
                    $('#CreateModal').modal('hide');
                    modelSave();
                }

                $('#summary_header').children('th').slice(3).remove();
                $('#serverTbody > tr, #srmServerTbody > tr, #databaseTbody > tr, #replicationTbody > tr').each(function () {
                    $(this).children('td').slice(3).remove();
                });

                let findOtherSites = siteProperties?.length && siteProperties?.filter((site) => site?.category?.toLowerCase() !== 'primary')

                if (findOtherSites?.length) $('#DREnable').val(true)
                else $('#DREnable').val(false)

                siteProperties?.length && siteProperties?.forEach((site) => {

                    if (site?.category?.toLowerCase() === 'primary') {
                        $('#pr_summary_header').html(site?.name)
                    } else if (site?.category?.toLowerCase() === 'dr') {
                        $('#dr_summary_header').html(site?.name)
                    } else {
                        let getSiteName = site?.name?.trim();

                        $('#summary_header').append(`<th>${site?.name}</th>`)
                        $('#serverTbody').append(`<td>${serverProperties[getSiteName]?.name || 'NA'}</td>`)
                        $('#databaseTbody').append(`<td>${databaseProperties[getSiteName]?.name || 'NA'}</td>`)
                        $('#replicationTbody').append(`<td>${replicationProperties[getSiteName]?.name || 'NA'}</td>`)
                    }
                })

                if (serverProperties?.hasOwnProperty('SRMServer')) {
                    serverProperties?.SRMServer?.length && serverProperties?.SRMServer?.forEach((srm) => {
                        if (srm?.type !== 'PR' && srm?.type !== 'DR') $('#srmServerTbody').append(`<td>${srm?.name || 'NA'}</td>`)
                    })
                }

                $("#PRReplicationSum").text($("#PRSelectReplicationName").val() || 'NA');

                const selectedserverValues = ($("#PRServerBody").is(':visible') ? $(".PRServerName").val() : $('#PRMultipleServer').val()) || 'NA';
                $("#PRServerSum").text(Array.isArray(selectedserverValues) ? selectedserverValues.join(', ') : selectedserverValues);

                const selectedDBValues = ($("#PRDatabaseBody").is(':visible') ? $(".PRDatabaseName").val() : $('#PRMultipleDatabase').val()) || 'NA';
                $("#PRDataBaseSum").text(Array.isArray(selectedDBValues) ? selectedDBValues.join(', ') : selectedDBValues);

                const selectedDRserverValues = ($("#DRServerBody").is(':visible') ? $(".DRServerName").val() : $('#DRMultipleServer').val()) || 'NA';
                $("#DRServerSum").text(Array.isArray(selectedDRserverValues) ? selectedDRserverValues.join(', ') : selectedDRserverValues);

                const selectedDRDBValues = ($("#DRDatabaseBody").is(':visible') ? $(".DRDatabaseName").val() : $('#DRMultipleDatabase').val()) || 'NA';
                $("#DRDataBaseSum").text(Array.isArray(selectedDRDBValues) ? selectedDRDBValues.join(', ') : selectedDRDBValues);

                if ($("#DRReplication").is(':visible')) {
                    $('#DRReplicationSum').show()
                    $("#DRReplicationSum").text($("#SelectReplicationNames").val() || 'NA');
                } else $('#DRReplicationSum').hide()

                if (!$('#tabledatabase').is(':visible')) $('#databaseTbody').hide();
                else $('#databaseTbody').show();

                if (!$('#tablereplication').is(':visible')) $('#replicationTbody').hide();
                else $('#replicationTbody').show();

                if (isSrmFound) {

                    serverProperties?.SRMServer && serverProperties['SRMServer']?.length && serverProperties['SRMServer'].forEach((srm) => {
                        if (srm?.type == 'PR') $('#PRSrmServerSum').show().text(srm?.name ?? 'NA')
                        else if (srm?.type == 'DR') $('#DRSrmServerSum').show().text(srm?.name ?? 'NA')
                    })

                    $('#srmServerTbody').show();
                } else {
                    $('#srmServerTbody').hide();
                }

                if (isClusterFound && $('#cluster').prop('checked')) {

                    serverProperties?.clusters && serverProperties['clusters']?.length && serverProperties['clusters'].forEach((server) => {
                        if (server?.type == 'PR') $('#PRClusterSum').show().text(server?.name ?? 'NA')
                        else if (server?.type == 'DR') $('#DRClusterSum').show().text(server?.name ?? 'NA')
                    })

                    $('#clusterServerTbody').show();
                } else {
                    $('#clusterServerTbody').hide();
                }

                $('#siteProperties').val(JSON.stringify(siteProperties))
                $('#serverProperties').val(JSON.stringify(serverProperties))
                $('#databaseProperties').val(JSON.stringify(databaseProperties))
                $('#replicationProperties').val(JSON.stringify(replicationProperties))
                form.steps('next');
            }
        }
    }, 700));

    //InfraObject Name
    $('#textName').on('keyup', commonDebounce(async function () {
        let infraId = $('#infraNameId')?.val();
        const value = $(this)?.val();
        let sanitizedValue = value?.replace(/\s{2,}/g, ' ')
        $(this).val(sanitizedValue);
        await validateName(sanitizedValue, infraId, IsNameExist);
    }, 500));

    $('#Description').on('keyup keypress', async function (event) {
        const value = $(this)?.val();
        let sanitizedValue = value?.replace(/^\s+/, '')?.replace(/\s{2,}/g, ' ')
        $(this)?.val(sanitizedValue);

        if (exceptThisSymbols?.includes(event?.key)) event?.preventDefault();
        await validateDescription(value, "Should not allow more than 250 characters", $('#DescriptionError'));
    });

    //BusinessService
    $('#infraBusinessServiceId').on('change', function () {
        const value = $(this)?.val();
        let id = $(this)?.children(":selected")?.attr("id");
        let getSiteProperties = $(this)?.children(":selected")?.attr('siteproperties');
        $('#BusinessServiceId').val(id);
        validateDropDown(value, ' Select operational service', 'BusinessServiceError');

        if (id) {
            $('#siteTypeError').text('').removeClass('field-validation-error');
            $('#siteType').empty();
            SetBusinessFunction(id);
            if (getSiteProperties) getSiteDetailsByOperationService(getSiteProperties)
        }

    });

    // --- Active Type --->
    $('#Activetype').on('change', async function () {
        const value = $(this)?.val();
        activeType = $('#Activetype option:selected')?.text();
        $("#ActiveTypeName")?.val(activeType);
        $("#DataTypeCol").hide();

        getPRAndDRServerType(activeType, 'production')
        getPRAndDRServerType(activeType, 'DR', 'DRDBServer')
        activityTypeEnable(value)

        Activitydata = value
        if (value === "1" || value === "3") $("#prdatabase, #drdatabase, #tabledatabase").hide();
        else $("#prdatabase, #drdatabase, #tabledatabase").show();

        serverProperties = {};
        databaseProperties = {};
        replicationProperties = {};
        multipleServerDetails = { multiplePRServer: [], multipleDRServer: [] };
        multipleDatabaseDetails = { multiplePRDatabase: [], multipleDRDatabase: [] };

        SetReplicationMaster(activeType)
        dynamicElements.forEach((id) => $(id).val("").trigger('change'));
        dynamicErrorElements.forEach((id) => $(id).text('').removeClass('field-validation-error'));

        validateDropDown(value, ' Select activity type', 'SelectActiveTypeError');
    })
    function activityTypeEnable(value) {

        if (value == '2') {
            $("#DataTypeCol").show();
            GetdabaseNames()
        } else {
            $('#DatabaseId, #databaseText, #SelectDatabaseType').val('');
            $('#SelectDatabaseType').empty().append('<option value=""> Select Database Type </option>');
            $("#DataTypeCol").hide();
        }
    }

    // --- DatabaseType --->
    $("#SelectDatabaseType").on('change', function () {
        const value = $(this)?.val();
        const databaseText = $('#SelectDatabaseType option:selected')?.text();
        database = databaseText;
        databaseType = value;
        $('#databaseText').val(databaseText)
        $('#DatabaseId').val(value)
        $('#ddlReplicationTypeNameId').empty().append('<option value=""> Select Replication Type </option>');
        $('#SelectReplicationType').append('<option value="" selected > Select Replication Category </option>');
        validateDropDown(value, "Select Database type", "DatbaseTypeError");
    });

    // ---- ReplicationType --->
    $('#ddlReplicationTypeNameId').on('change', function () {
        const value = $(this)?.val();
        $('#ReplicationTypeId')?.val(value);

        ReplicationtypeName = $('#ddlReplicationTypeNameId option:selected')?.text();
        let clusterFound = clusterReplicationData && clusterReplicationData.some((rep) => (ReplicationtypeName?.toLowerCase().replace(/ /g, "").includes(rep)))
        let isOracleFound = getOracleFound();
        let ismssqlFound = getMssqlFound()

        if (ReplicationtypeName && ReplicationtypeName?.toLowerCase()?.replace(/ /g, "")?.includes('srmvmware')) {
            getPRAndDRServerType(activeType, 'production', 'presxiserver', false, true)
            getPRAndDRServerType(activeType, 'DR', 'dresxiserver', false, true)
            SetSrmServerType(activeType, 'presxiappserver', 'production')
            $('#tablesrm, #prSrm, #clusterCol').show();
            $('#cluster').prop('checked', false);
            $('#clusterType, #clusterPR, #clusterDR').val('').trigger('change');
            $('#selectedClusterCol, #clusterProperties,#DRReplication, #PRReplication,#tablereplication').hide()
            $('#SelectReplicationNames').val("");
            if ($('#DREnable').prop('checked')) $('#drSrm').show();

        } else if (ReplicationtypeName?.toLowerCase()?.replace(/ /g, "")?.includes('redis')) {
            $("#DRReplication, #PRReplication,#tablereplication").hide();
            $('#SelectReplicationNames').val("");
        } else if (ReplicationtypeName && clusterFound) {
            $('#clusterCol').show();
            $('#cluster').prop('checked', false);
            $('#selectedClusterCol, #clusterProperties').hide();
        } else {
            $('#tablesrm, #prSrm, #drSrm, #clusterCol, #selectedClusterCol, #clusterProperties').hide();
        }

        if (isOracleFound) {
            $('.PRDatabaseName, .DRDatabaseName').empty();
            $('#PRServerBody, #PRDatabaseBody, #DRServerBody, #DRDatabaseBody').addClass('d-none')
            $('#PRMultipleServerBody, #PRMultipleDatabaseBody, #DRMultipleServerBody, #DRMultipleDatabaseBody').removeClass('d-none')
        } else {
            $('#PRServerBody, #PRDatabaseBody, #DRServerBody, #DRDatabaseBody').removeClass('d-none')
            $('#PRMultipleServerBody, #PRMultipleDatabaseBody, #DRMultipleServerBody, #DRMultipleDatabaseBody').addClass('d-none')
        }

        if (ismssqlFound) {
            $('.PRDatabaseName, .DRDatabaseName').empty();
            $(' #DRServerBody, #DRDatabaseBody,#PRReplication,#DRReplication,#tablereplication').addClass('d-none')
            $('#DRMultipleServerBody, #DRMultipleDatabaseBody').removeClass('d-none')
        } else {
            $('#DRServerBody, #DRDatabaseBody,#PRReplication,#DRReplication,#tablereplication').removeClass('d-none')
            $('#DRMultipleServerBody, #DRMultipleDatabaseBody').addClass('d-none')
        }

        $('#ReplicationTypeName').val(ReplicationtypeName)

        serverProperties = {};
        databaseProperties = {};
        replicationProperties = {};

        multipleServerDetails = { multiplePRServer: [], multipleDRServer: [] };
        multipleDatabaseDetails = { multiplePRDatabase: [], multipleDRDatabase: [] };

        dynamicElements.forEach((id) => $(id)?.val("")?.trigger('change'));
        dynamicErrorElements.forEach((id) => $(id)?.text('')?.removeClass('field-validation-error'));
        validateDropDown(value, ' select replication type', 'SelectReplicationError');
    });

    // --- Replication Category --->
    $('#SelectReplicationType').on('change', async function () {

        const value = $(this)?.val();
        ReplicationCategory = $('#SelectReplicationType option:selected')?.text();
        replicationType = $(this)?.children(":selected")?.attr("value");
        $('#infraReplicationTypeId')?.val(replicationType)
        $('#ReplicationCategorytext')?.val(ReplicationCategory)

        if ((ReplicationCategory === "Application - No Replication") || (ReplicationCategory === "Database - No Replication")) {
            $("#DRReplication, #PRReplication, #tablereplication").hide();
            $('#SelectReplicationNames, #PRSelectReplicationName').val("");
        } else {
            $("#DRReplication, #PRReplication, #tablereplication").show();
            $('#SelectReplicationNameError, #DRSelectReplicationNamesError').text('').removeClass('field-validation-error');
        }

        dynamicElements.forEach((id) => $(id)?.val("")?.trigger('change'));
        dynamicErrorElements.forEach((id) => $(id)?.text('')?.removeClass('field-validation-error'));

        SetReplicationMapping();
        validateDropDown(value, "Select replication category", "SelectReplicationTypeError");
    })

    // --- Pairinfra or Associate --->

    $(document).on('change', '.pair', function (e) {
        const value = $(this).val();
        const isChecked = e?.target?.checked;

        const toggleSection = (selector, show) => $(selector).toggle(show);
        const clearAndReset = (selectId, errorId) => {
            clearSelectAndTriggerChange(selectId, errorId);
            $(`#${selectId}`).val('');
        };

        if (value === "IsPair") {
            $('#pairValue').val(isChecked);
            toggleSection("#pairinfra", isChecked);

            if (isChecked) {
                clearAndReset("SelectPairInfra", "SelectPairInfraError");
                clearAndReset("PairInfraId", "SelectPairInfraError");
                $('#AssociateValue').val(false);
                $('#InfraId').prop('checked', false);
                toggleSection("#associate", false);
            }
        } else if (value === "IsAssociate") {
            $('#AssociateValue').val(isChecked);
            toggleSection("#associate", isChecked);

            if (isChecked) {
                clearAndReset("SelectAssociate", "SelectAssociateError");
                clearAndReset("AssociateId", "SelectAssociateError");
                $('#pairValue').val(false);
                $('#PairId').prop('checked', false);
                toggleSection("#pairinfra", false);
            }
        }
    });

    $('#drift').on('change', function (e) {
        if (e?.target?.checked) $('#driftValue')?.val(true)
        else $('#driftValue')?.val(false)
    })

    // --- Priority --->
    $('input[name="Priority"]').on('change', function () {
        let selectedValue = $('input[name="Priority"]:checked')?.val();
        $('#PrioritySum')?.text(selectedValue);
    });

    // --- PRServer --->
    $('#SelectServerName, #PRMultipleServer').on('change', function () {
        const value = $(this)?.val();
        let isMssqlAG = getMssqlFound()

        if ($(this)?.attr('id') === 'PRMultipleServer') {
            let getIds = []

            $('#PRMultipleServer option:selected').each(function () {
                let id = $(this)?.attr('prSId');

                if (id) {
                    getIds.push(id)

                    if (!multipleServerDetails?.multiplePRServer?.includes(id)) {
                        multipleServerDetails?.multiplePRServer?.push(id)
                        validateDropDown(id, ' select PRServer', 'SelectServerNameError');
                        if ($("#Activetype").val() !== '1') SetServerID(id, 'production','multiplePR');
                    }
                }
            });

            if (multipleServerDetails?.multiplePRServer?.length) {
                appendDynamicProperties(getIds?.join(','), 'PR', value.join(','), serverProperties, 'PRDBServer');
            }

        } else {
            let obj = {}
            let prServer = $('#SelectServerName option:selected').text();
            let id = $('#SelectServerName option:selected').attr('prSId');

            if (isMssqlAG) obj['currentPR'] = true

            appendDynamicProperties(id, 'PR', prServer, serverProperties, 'PRDBServer', '', isMssqlAG ? obj : '')
            validateDropDown(value, ' select PRServer', 'SelectServerNameError');

            if ($("#Activetype").val() !== '1') SetServerID(id, 'production');
        }
    });

    $('#PRMultipleServer, #DRMultipleServer').on('select2:unselecting select2:unselect', function (e) {
        const $el = $(this);
        const isPR = $el.attr('id') === 'PRMultipleServer';

        const element = e?.params?.args?.data?.element || e?.params?.data?.element;
        const attrName = isPR ? 'prsid' : 'drsid';
        const value = element?.getAttribute(attrName);
        const dbKey = isPR ? 'multiplePRServer' : 'multipleDRServer';
        const dbSelector = isPR ? '#PRMultipleDatabase' : '#DRMultipleDatabase';
        const dbAttr = isPR ? 'prServerId' : 'drServerId';

        if (value) {
            const index = multipleServerDetails[dbKey]?.indexOf(value);
            if (index !== -1) multipleServerDetails[dbKey].splice(index, 1);
            $(`${dbSelector} option[${dbAttr}="${value}"]`).remove();
        }

        $el.select2('close');
    });

    // --- DRServer --->
    $('#DRSelectServerNames, #DRMultipleServer').on('change', function () {
        const value = $(this)?.val();
        let isMssqlAG = getMssqlFound()

        if ($(this).attr('id') === 'DRMultipleServer') {
            let obj = {}
            let getIds = [];

            if (isMssqlAG) obj["currentPRDetails"] = []

            $('#DRMultipleServer option:selected').each(function () {
                let id = $(this)?.attr('drSId');

                if (id) {
                    getIds.push(id)

                    if (obj?.currentPRDetails) obj?.currentPRDetails?.push({ id, name: $(this).text(), currentPR: false })
                    if (!multipleServerDetails?.multipleDRServer?.includes(id)) {

                        multipleServerDetails?.multipleDRServer?.push(id)

                        validateDropDown(id, ' select Server Name', 'DRSelectServerNamesError');
                        if ($("#Activetype").val() !== '1') SetServerID(id, 'DR', 'multipleDR');
                    }
                }
            });

            if (multipleServerDetails?.multipleDRServer?.length) {
                appendDynamicProperties(getIds?.join(','), 'DR', value?.join(','), serverProperties, 'DRDBServer', '', obj);
            }

        } else {

            let id = $('#DRSelectServerNames option:selected').attr('drSId');;

            if (id) {
                appendDynamicProperties(id, 'DR', value, serverProperties, 'DRDBServer')

                validateDropDown(value, ' select Server Name', 'DRSelectServerNamesError');
                if ($("#Activetype").val() !== '1') SetServerID(id, 'DR');
            }
        }
    });

    // --- PR Database --->
    $('#PRSelectDatabase, #PRMultipleDatabase').on('change', function () {

        const value = $(this)?.val();

        if ($(this).attr('id') === 'PRMultipleDatabase') {
            let prType = []
            multipleDatabaseDetails['multiplePRDatabase'] = [];

            $('#PRMultipleDatabase option:selected').each(function () {
                let id = $(this)?.attr('prDId');
                let type = $(this)?.data('type');

                if (!prType?.includes(type)) prType.push(type);

                if (!multipleDatabaseDetails?.multiplePRDatabase?.includes(id) && id) {

                    multipleDatabaseDetails?.multiplePRDatabase.push(id);

                    validateDropDown(id, ' select database name', 'PRSelectDatabaseError');
                }
            });

            if (multipleDatabaseDetails?.multiplePRDatabase?.length) {
                appendDynamicProperties(multipleDatabaseDetails?.multiplePRDatabase?.join(','), 'PR', value?.join(','), databaseProperties, prType?.join(','))
            }

        } else {

            const value = $('#PRSelectDatabase option:selected').text();
            let id = $('#PRSelectDatabase option:selected').attr('prDId');
            let type = $('#PRSelectDatabase option:selected').data('type');

            if (id) {
                appendDynamicProperties(id, 'PR', value, databaseProperties, type)
                validateDropDown(value, ' select database name', 'PRSelectDatabaseError');
            }
        }
    })

    // --- DR Database --->
    $('#DRSelectDatabase, #DRMultipleDatabase').on('change', function () {

        const value = $(this)?.val();

        if ($(this).attr('id') === 'DRMultipleDatabase') {
            let drType = []
            multipleDatabaseDetails['multipleDRDatabase'] = [];

            $('#DRMultipleDatabase option:selected').each(function () {
                let id = $(this)?.attr('drDId');
                let type = $(this)?.data('type');

                if (!drType.includes(type)) drType?.push(type);

                if (!multipleDatabaseDetails?.multipleDRDatabase?.includes(id) && id) {

                    multipleDatabaseDetails?.multipleDRDatabase?.push(id);

                    validateDropDown(id, 'select database name', 'DRSelectDatabaseNamesError');
                }
            });

            if (multipleDatabaseDetails?.multipleDRDatabase?.length) {
                appendDynamicProperties(multipleDatabaseDetails?.multipleDRDatabase?.join(','), 'DR', value.join(','), databaseProperties, drType?.join(','))
            }

        } else {

            const value = $('#DRSelectDatabase option:selected').text();
            let id = $('#DRSelectDatabase option:selected').attr('drDId');
            let type = $('#DRSelectDatabase option:selected').data('type');

            if (id) {
                appendDynamicProperties(id, 'DR', value, databaseProperties, type)
                validateDropDown(value, 'select database name', 'DRSelectDatabaseNamesError');
            }
        }
    })

    $('#PRMultipleDatabase, #DRMultipleDatabase').on('select2:unselecting select2:unselect', function (e) {
        const $el = $(this);
        const isPR = $el.attr('id') === 'PRMultipleDatabase';

        const element = e?.params?.args?.data?.element || e?.params?.data?.element;
        const selectedText = e?.params?.args?.data?.text || e?.params?.data?.text;

        const attrName = isPR ? 'prdid' : 'drdid';
        const value = element?.getAttribute(attrName);
        const selectedArray = $el.val() || [];
        const dbKey = isPR ? 'multiplePRDatabase' : 'multipleDRDatabase';

        if (value) {
            const index = multipleDatabaseDetails[dbKey]?.indexOf(value);
            if (index !== -1) multipleDatabaseDetails[dbKey].splice(index, 1);
        }

        const textIndex = selectedArray.indexOf(selectedText?.trim());
        if (textIndex !== -1) selectedArray.splice(textIndex, 1);

        $el.select2('close');
    });

    // --- Business Function --->
    $('#ddlbusinessFunctionId').on('change', function () {
        const value = $('#ddlbusinessFunctionId option:selected').text();
        const id = $(this)?.val();
        $('#BusinessFunctionVal').val(value)
        $('#BusinessFunctionId').val(id)
        validateDropDown(value, ' select Database Name', 'BusinessFunctionError');
    })

    // ---  PR Replication --->
    $('#PRSelectReplicationName').on('change', function () {
        const value = $('#PRSelectReplicationName option:selected').text();
        const id = $(this)?.children(":selected")?.attr('prRId');
        $('#PRReplicationName').val(id)

        appendDynamicProperties(id, 'PR', value, replicationProperties)
        validateDropDown(value, 'select database name', 'SelectReplicationNameError');
    })

    // ---  DR Replication --->
    $('#SelectReplicationNames').on('change', function () {
        const value = $('#SelectReplicationNames option:selected').text();
        const id = $(this)?.children(":selected")?.attr('drRId');
        $('#drReplicationNameId').val(id)

        appendDynamicProperties(id, 'DR', value, replicationProperties)
        validateDropDown(value, 'select replication category', 'DRSelectReplicationNamesError');
    })

    $('#siteType').on('change', function () {
        let selectedOptions = $(this)?.find('option:selected');
        siteProperties = []

        selectedOptions?.length && selectedOptions.each(async function () {
            let getId = $(this)?.val();
            let getText = $(this)?.data('sitetypename');
            let category = $(this)?.data('category');

            let duplicateFound = siteProperties?.length && siteProperties?.some(data => data?.id === getId)

            if (!duplicateFound) {
                let obj = { id: getId, name: getText, category: category }
                siteProperties.push(obj)
            }
        });
    })

    $('#siteType').on('select2:unselecting select2:unselect', function (e) {

        let value = e?.params?.args ? e?.params?.args?.data?.element?.dataset?.category : e?.params?.data?.element?.dataset?.category;
        if (value?.toLowerCase() === "primary") e?.preventDefault();
    });

    $('#siteType').on('select2:open', function () {

        const searchField = document?.querySelector('.select2-search__field');
        if (searchField) searchField.remove();
    });

    /*Srm servers onchange*/
    $(document).on('change', '.srmServer', async function (e) {
        const inputName = e?.target?.name
        const inputType = $(this)?.attr('type');

        if (inputType == 'checkbox' && inputName == 'cluster') {
            $('#clusterProperties').toggle(e?.target?.checked);
            serverProperties["isCluster"] = e?.target?.checked;

            if (!e?.target?.checked) {
                $('#clusterType').val('').trigger('change');
                $('#selectedClusterCol').hide();
                return;
            }
        } else if (inputName == "cluster") {

            if (e?.target?.value == "veritas") {
                getVeritasHacmpList("veritas")
                $(`#${e?.target?.value}-child`).show();
                $('#HACMP-child').hide();
            } else {
                getVeritasHacmpList("hacmp")
                $(`#${e?.target?.value}-child`).show();
                $('#veritas-child').show();
            }

            serverProperties["clusterType"] = e?.target?.value
            serverProperties["clusters"] = []
            $('#clusterPR, #clusterDR').val('').trigger('change');
            $('#clusterPRError, #clusterDRError').removeClass('field-validation-error').text('')
            $('#selectedClusterCol').show();

        } else if (inputName == "clusters") {

            let clusterChildId = $(this)?.attr('id')
            let clusterName = $(this)?.children(":selected")?.text();
            let clusterType = $(this)?.children(":selected")?.attr('clustertype')

            if ($('#clusterType')?.val() == 'HACMP') {
                if (clusterChildId?.includes('clusterPR')) {
                    $('#clusterDR option[value="' + e?.target?.value + '"]').prop('disabled', true);
                    $('#clusterDR option').not('[value="' + e?.target?.value + '"]').prop('disabled', false);

                } else if (clusterChildId?.includes('clusterDR')) {
                    $('#clusterPR option[value="' + e?.target?.value + '"]').prop('disabled', true);
                    $('#clusterPR option').not('[value="' + e?.target?.value + '"]').prop('disabled', false);
                }
            }
            appendDynamicProperties(e.target.value, inputName, clusterName, serverProperties, clusterType)

        } else {
            let srmName = $(this)?.find(":selected")?.text()
            let srmType = $(this)?.children(":selected")?.attr('srmtype')
            let id = $(this)?.data('dynamicname');
            if (srmName) appendDynamicProperties(e?.target?.value, inputName, srmName, serverProperties, srmType ? srmType : id)
        }

        if (e?.target?.value) validateDropDown(e?.target?.value, ' select server ', $(this)?.attr('id') + 'Error');
    })

    $(document).on('change', '.dynamic_select', async function (e) {

        let value = $(this)?.val();
        let selectTagType = $(this)?.data('type')
        let selectTagDynamicId = $(this)?.data('dynamicname');
        let selectTagDynamicType = $(this)?.data('sitetype');
        let id = $(this)?.attr('id');

        let selectedText = $(this)?.find('option:selected')?.map(function () {
            return $(this).text();
        }).get().join(', ');

        if (selectTagType == 'multipleserver' && id) {

            if (!multipleServerDetails.hasOwnProperty(selectTagDynamicId)) multipleServerDetails[selectTagDynamicId] = []

            const selectedOptions = $(`#${id} option:selected`);
            const getIds = []
            const removedOptions = multipleServerDetails[selectTagDynamicId]?.filter(optionId => !selectedOptions.map((_, el) => $(el).val())?.get()?.includes(optionId));

            selectedOptions.each(async function () {
                let optionId = $(this)?.val();

                if (optionId) {

                    getIds?.push(optionId);

                    if (multipleServerDetails[selectTagDynamicId] && !multipleServerDetails[selectTagDynamicId]?.includes(optionId)) {
                        multipleServerDetails[selectTagDynamicId].push(optionId)

                        validateDropDown(optionId, 'select server', `${selectTagDynamicId}ServerNameError`);
                        if ($("#Activetype").val() !== '1') await SetDynamicDatabaseType("Configuration/InfraObject/GetDatabase", 'dynamic', selectTagDynamicId, 'multipleDatabase', optionId)
                    }
                }
            });

            if (removedOptions?.length) {

                removedOptions.forEach(removedOptionId => {
                    multipleServerDetails[selectTagDynamicId] = multipleServerDetails[selectTagDynamicId]?.filter(optionId => optionId !== removedOptionId);
                    let selectize = $(`#${selectTagDynamicId}MultipleDatabase`)[0]?.selectize;

                    for (const [value, data] of serverDataMap.entries()) {
                        if (data?.serverId === removedOptionId) {
                            selectize.removeOption(value);

                            $(`#${selectTagDynamicId}MultipleDatabase option[value='${value}']`).remove();
                            serverDataMap.delete(value);
                        }
                    }
                });
            }

            if (multipleServerDetails[selectTagDynamicId] && multipleServerDetails[selectTagDynamicId]?.length) {
                appendDynamicProperties(getIds?.join(','), selectTagDynamicId, selectedText, serverProperties, 'customserver', selectTagDynamicType?.trim());
            }

        } else if (selectTagType == 'multipledatabase' && id) {

            if (!multipleDatabaseDetails?.hasOwnProperty(selectTagDynamicId)) multipleDatabaseDetails[selectTagDynamicId] = [];
            else multipleDatabaseDetails[selectTagDynamicId] = []

            const selectedOptions = $(`#${id} option:selected`);
            selectedOptions.each(function () {
                let optionId = $(this).val();

                if (multipleDatabaseDetails[selectTagDynamicId] && !multipleDatabaseDetails[selectTagDynamicId]?.includes(optionId) && optionId) {

                    multipleDatabaseDetails[selectTagDynamicId].push(optionId)

                    validateDropDown(optionId, 'select database', `${selectTagDynamicId}DatabaseNameError`);
                }
            });

            multipleDatabaseDetails[selectTagDynamicId] = multipleDatabaseDetails[selectTagDynamicId]?.filter(optionId => selectedOptions.map((_, el) => $(el).val())?.get()?.includes(optionId));

            if (multipleDatabaseDetails[selectTagDynamicId] && multipleDatabaseDetails[selectTagDynamicId].length) {
                appendDynamicProperties(multipleDatabaseDetails[selectTagDynamicId].join(','), selectTagDynamicId, selectedText, databaseProperties, 'customdatabase', selectTagDynamicType?.trim());
            }

        } else if (selectTagType == 'server') {

            await SetDynamicDatabaseType("Configuration/InfraObject/GetDatabase", 'dynamic', selectTagDynamicId, 'database', value)
            appendDynamicProperties(value, selectTagDynamicId, selectedText, serverProperties, 'customserver', selectTagDynamicType?.trim())
            selectTagDynamicId = `${selectTagDynamicId}ServerName`;

        } else if (selectTagType == 'database') {

            appendDynamicProperties(value, selectTagDynamicId, selectedText, databaseProperties, 'customdatabase', selectTagDynamicType?.trim())
            selectTagDynamicId = `${selectTagDynamicId}DatabaseName`;

        } else {
            appendDynamicProperties(value, selectTagDynamicId, selectedText, replicationProperties, 'customreplication', selectTagDynamicType?.trim())
            selectTagDynamicId = `${selectTagDynamicId}ReplicationName`;
        }

        if (value) validateDropDown(value, `Select ${selectTagType}`, `${selectTagDynamicId}Error`);
    });

    const appendDynamicProperties = (value, inputName, addValue, dynamicProperties, serverType = '', siteType = '', mssqlAGDetails = {}) => {

        if (value) {

            let obj = { id: value, name: addValue };
            if (serverType) obj['type'] = serverType

            if (siteType) obj['siteId'] = inputName

            if (Object?.keys(mssqlAGDetails)?.length) obj = { ...obj, ...mssqlAGDetails }

            if (inputName?.toLowerCase() == 'srmserver' || inputName?.toLowerCase() == 'clusters') {
                let propertyName = inputName?.toLowerCase() == 'srmserver' ? 'SRMServer' : 'clusters'

                dynamicProperties[propertyName] = dynamicProperties[propertyName] || [];

                const existingIndex = dynamicProperties[propertyName]?.findIndex(prop => prop?.type === serverType);

                if (existingIndex !== -1) dynamicProperties[propertyName][existingIndex] = obj;
                else dynamicProperties[propertyName].push(obj);

            } else {
                if (siteType) {

                    Object.keys(dynamicProperties)?.length && Object.keys(dynamicProperties).forEach((key) => {
                        if (dynamicProperties[key]?.siteId && dynamicProperties[key]?.siteId === inputName) {
                            delete dynamicProperties[key];
                        }
                    });

                    dynamicProperties[siteType] = obj;
                } else {
                    dynamicProperties[inputName] = obj;
                }
            }
        }
    }

    // ---Select Associate --->
    $('#SelectAssociate').on('change', function () {

        const value = $('#SelectAssociate option:selected')?.text();
        const id = $(this)?.children(":selected")?.attr('Acid');
        $('#AssociateId').val(id)
        validateDropDown(value, 'Select associate ', 'SelectAssociateError');
    })

    // --- Select PairInfra --->
    $('#SelectPairInfra').on('change', function () {
        const value = $('#SelectPairInfra option:selected').text();
        const id = $(this)?.children(":selected")?.attr('infraid');
        $('#PairInfraId').val(id)
        validateDropDown(value, 'Select pairInfra', 'SelectPairInfraError');
    })


    $("#Modelcancel, #Modelcls").on('click', function () {
        $('#NewModel').modal('hide');
        $('#CreateModal').modal('show');
        form.steps('previous');
    });

    $('#addUserApproval').on('change', '.dataVal', function () {
        const value = $(this)?.val();
        const prValue = $(this)?.data('pr');
        let getCurrentTrIndex = $(this)?.closest('tr')?.index()

        if (selectedNodeValues[getCurrentTrIndex]) {
            let obj = { [prValue]: value }
            selectedNodeValues.splice(getCurrentTrIndex, 1, obj);
        } else {
            const newSelection = {};
            newSelection[prValue] = value;
            selectedNodeValues.push(newSelection);
        }
        const errorElementId = $(this)?.closest('.form-group')?.find('.error-message')
        validateDropDown(value, 'Select database', errorElementId);
    });

    function modelSave() {

        $("#ModelSave").on('click', async function () {

            const allValid = validateAllFields();
            if (allValid) {
                $('.dataVal')?.trigger('change');
                let removedElements = selectedNodeValues?.splice(0, NodeValuesLength);
                setTimeout(() => {
                    let nodeData = JSON.stringify(removedElements);
                    $("#NodePropertiesData").val(nodeData);
                    $('#NewModel').modal('hide');
                    $('#CreateModal').modal('show');
                }, 200)

            }
        });
    }
    function validateAllFields() {
        let allValid = true;

        $('.dataVal').each(function () {
            const value = $(this)?.val();
            const errorElementId = $(this)?.closest('.form-group')?.find('.error-message')

            if (!validateDropDown(value, 'Select database', errorElementId)) allValid = false;
        });

        return allValid;
    }

    const checkDynamicDataValidation = () => {
        let formIsValid = true;

        $('.dynamic_infra').each(function () {
            let value = $(this)?.val();
            let getId = $(this)?.attr('id');
            let getName = $(this)?.attr('name')
            let getDynamicName = $(this)?.data('dynamicname');
            let selectTagType = $(this)?.data('type')
            let isValueEmpty = Array.isArray(value) ? !value?.length : !value;

            if (isValueEmpty && getId && $(`#${getId}`).is('select') && !$(this)?.parent()?.hasClass('d-none')) {
                if ($(`#${getId}`)?.closest('td')?.is(':visible')) {
                    formIsValid = false;

                    if (getName && getName?.toLowerCase() === 'srmserver') $(`#${getDynamicName}SrmServerError`).text('Select server').addClass('field-validation-error')
                    else if (selectTagType === 'server' || selectTagType === 'multipleserver') $(`#${getDynamicName}ServerNameError`).text('Select server').addClass('field-validation-error')
                    else if (selectTagType === 'database' || selectTagType === 'multipledatabase') $(`#${getDynamicName}DatabaseNameError`).text('Select database').addClass('field-validation-error')
                    else $(`#${getId}Error`).text('Select replication').addClass('field-validation-error')
                }
            }
        });

        return formIsValid;
    }

    async function validateDescription(value, errorMessage) {
        const errorElement = $('#DescriptionError');

        if (value.includes('<')) {
            errorElement.text('Special characters not allowed').addClass('field-validation-error');
            return false;
        } else if (value.length > 250) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }

    function validateDropDown(value, errorMessage, errorElement) {
        const isInvalid = Array.isArray(value) ? !value?.length : !value;

        const $element = typeof errorElement === 'string'
            ? $(`#${errorElement}`) : $(errorElement); 

        if (isInvalid) {
            $element.text(errorMessage).addClass('field-validation-error');
            return false;
        }

        $element.text('').removeClass('field-validation-error');
        return true;
    }

    async function validateName(value, id = null) {
        if (!value) {
            $('#NameError').text('Enter infraobject name').addClass('field-validation-error');
            return false;
        } else if (value.includes("<")) {
            $('#NameError').text('Special characters not allowed').addClass('field-validation-error');
            return false;
        }
        let url = RootUrl + infraObjectURL?.nameExistUrl;
        let data = { infraObjectName: value, id: id };

        const validationResults = [
            await ShouldNotBeginWithDotAndHyphen(value),
            await ShouldNotConsecutiveDotAndHyphen(value),
            await SpecialCharValidateCustom(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithSpace(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsNameExist(url, data, OnError)
        ];
        return await CommonValidation($('#NameError'), validationResults);
    }

    async function IsNameExist(url, data, errorFunc) {
        return !data?.infraObjectName?.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
    }

})