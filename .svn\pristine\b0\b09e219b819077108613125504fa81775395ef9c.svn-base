using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class PluginManagerHistoryFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string LoginName => "TestPluginManagerHistory";
    public static string PluginManagerId => "PLUGIN_MANAGER_123";
    public static string PluginManagerName => "TestPluginManager";
    public static string Version => "1.0.0";
    public static string UpdaterId => "UPDATER_123";

    public List<PluginManagerHistory> PluginManagerHistoryPaginationList { get; set; }
    public List<PluginManagerHistory> PluginManagerHistoryList { get; set; }
    public PluginManagerHistory PluginManagerHistoryDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public PluginManagerHistoryFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<PluginManagerHistory>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.CompanyId, () => CompanyId)
            .With(x => x.LoginName, () => LoginName + "_" + _fixture.Create<string>())
            .With(x => x.PluginManagerId, () => PluginManagerId)
            .With(x => x.PluginManagerName, () => PluginManagerName + "_" + _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.Version, () => Version)
            .With(x => x.UpdaterId, () => UpdaterId)
            .With(x => x.Description, () => _fixture.Create<string>())
            .With(x => x.Comments, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow));

        PluginManagerHistoryPaginationList = _fixture.CreateMany<PluginManagerHistory>(20).ToList();
        PluginManagerHistoryList = _fixture.CreateMany<PluginManagerHistory>(5).ToList();
        PluginManagerHistoryDto = _fixture.Create<PluginManagerHistory>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public PluginManagerHistory CreatePluginManagerHistoryWithProperties(
        string companyId = null,
        string loginName = null,
        string pluginManagerId = null,
        string pluginManagerName = null,
        string version = null,
        string updaterId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<PluginManagerHistory>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.CompanyId, companyId ?? CompanyId)
            .With(x => x.LoginName, loginName ?? LoginName + "_" + _fixture.Create<string>())
            .With(x => x.PluginManagerId, pluginManagerId ?? PluginManagerId)
            .With(x => x.PluginManagerName, pluginManagerName ?? PluginManagerName + "_" + _fixture.Create<string>())
            .With(x => x.Version, version ?? Version)
            .With(x => x.UpdaterId, updaterId ?? UpdaterId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public PluginManagerHistory CreatePluginManagerHistoryWithSpecificLoginName(string loginName)
    {
        return CreatePluginManagerHistoryWithProperties(loginName: loginName);
    }

    public PluginManagerHistory CreatePluginManagerHistoryWithSpecificPluginManagerId(string pluginManagerId)
    {
        return CreatePluginManagerHistoryWithProperties(pluginManagerId: pluginManagerId);
    }

    public PluginManagerHistory CreatePluginManagerHistoryWithSpecificCompanyId(string companyId)
    {
        return CreatePluginManagerHistoryWithProperties(companyId: companyId);
    }

    public List<PluginManagerHistory> CreateMultiplePluginManagerHistoriesWithSameLoginName(string loginName, int count)
    {
        var histories = new List<PluginManagerHistory>();
        for (int i = 0; i < count; i++)
        {
            histories.Add(CreatePluginManagerHistoryWithProperties(loginName: loginName));
        }
        return histories;
    }

    public List<PluginManagerHistory> CreatePluginManagerHistoriesWithSamePluginManagerId(string pluginManagerId, int count)
    {
        var histories = new List<PluginManagerHistory>();
        for (int i = 0; i < count; i++)
        {
            histories.Add(CreatePluginManagerHistoryWithProperties(pluginManagerId: pluginManagerId));
        }
        return histories;
    }

    public List<PluginManagerHistory> CreatePluginManagerHistoriesWithSameCompanyId(string companyId, int count)
    {
        var histories = new List<PluginManagerHistory>();
        for (int i = 0; i < count; i++)
        {
            histories.Add(CreatePluginManagerHistoryWithProperties(companyId: companyId));
        }
        return histories;
    }

    public PluginManagerHistory CreateInactivePluginManagerHistory()
    {
        return CreatePluginManagerHistoryWithProperties(isActive: false);
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
