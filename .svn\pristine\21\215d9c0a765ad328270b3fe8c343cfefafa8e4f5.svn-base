using ContinuityPatrol.Application.Features.RsyncOption.Events.Create;

namespace ContinuityPatrol.Application.Features.RsyncOption.Commands.Create;

public class CreateRsyncOptionCommandHandler : IRequestHandler<CreateRsyncOptionCommand, CreateRsyncOptionResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IRsyncOptionRepository _rsyncOptionRepository;

    public CreateRsyncOptionCommandHandler(IMapper mapper, IRsyncOptionRepository rsyncOptionRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _rsyncOptionRepository = rsyncOptionRepository;
    }

    public async Task<CreateRsyncOptionResponse> Handle(CreateRsyncOptionCommand request,
        CancellationToken cancellationToken)
    {
        var rsyncOption = _mapper.Map<Domain.Entities.RsyncOption>(request);

        rsyncOption = await _rsyncOptionRepository.AddAsync(rsyncOption);

        var response = new CreateRsyncOptionResponse
        {
            Message = Message.Create("Rsync Options", rsyncOption.Name),

            Id = rsyncOption.ReferenceId
        };

        await _publisher.Publish(new RsyncOptionCreatedEvent { Name = rsyncOption.Name }, cancellationToken);

        return response;
    }
}