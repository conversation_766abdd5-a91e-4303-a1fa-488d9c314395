﻿using ContinuityPatrol.Application.Features.User.Events.ResetPassword;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;

namespace ContinuityPatrol.Application.Features.User.Commands.ResetPassword;

public class ResetPasswordCommandHandler : IRequestHandler<ResetPasswordCommand, ResetPasswordResponse>
{
    private readonly IEmailService _emailService;
    private readonly IGlobalSettingRepository _globalSettingRepository;
    private readonly ILogger<ResetPasswordCommandHandler> _logger;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly ISmtpConfigurationRepository _smtpConfigurationRepository;
    private readonly IUserLoginRepository _userLoginRepository;
    private readonly IUserRepository _userRepository;

    public ResetPasswordCommandHandler(IMapper mapper, IEmailService emailService, IUserRepository userRepository,
        IUserLoginRepository userLoginRepository, IPublisher publisher,
        ISmtpConfigurationRepository smtpConfigurationRepository, ILogger<ResetPasswordCommandHandler> logger,
        IGlobalSettingRepository globalSettingRepository)
    {
        _mapper = mapper;
        _userRepository = userRepository;
        _userLoginRepository = userLoginRepository;
        _emailService = emailService;
        _publisher = publisher;
        _smtpConfigurationRepository = smtpConfigurationRepository;
        _logger = logger;
        _globalSettingRepository = globalSettingRepository;
    }

    public async Task<ResetPasswordResponse> Handle(ResetPasswordCommand request, CancellationToken cancellationToken)
    {
        var userDtl = await _userRepository.GetByReferenceIdAsync(request.UserId);

        var globalSetting = await _globalSettingRepository.GlobalSettingBySettingKey("Email Notification");

        if (globalSetting is null || globalSetting.GlobalSettingValue.Equals("false"))
        {
            _logger.LogWarning("The email notification feature is not enabled in the global settings.");

            throw new InvalidException("The email notification feature is not enabled in the global settings.");
        }

        var smtpConfiguration = (await _smtpConfigurationRepository.ListAllAsync()).LastOrDefault();

        if (smtpConfiguration is null) throw new InvalidException("please configure smtp.");

        Guard.Against.NullOrDeactive(userDtl, nameof(Domain.Entities.User),
            new NotFoundException(nameof(Domain.Entities.User), request.UserId));

        var userLogInDtl = await _userLoginRepository.GetUserLoginByUserId(request.UserId);

        Guard.Against.NullOrDeactive(userLogInDtl, nameof(Domain.Entities.UserLogin),
            new NotFoundException(nameof(Domain.Entities.UserLogin), request.UserId));

        if (!string.Equals(userDtl.LoginName, request.LoginName, StringComparison.OrdinalIgnoreCase))
            throw new InvalidPasswordException("The user is not valid.");

        //userDtl.LoginPassword = SecurityHelper.Encrypt(request.Password);

        userDtl.LoginPassword = request.Password;

        userDtl.IsLock = false;

        userDtl.IsReset = userDtl.LoginType.Trim().ToLower() == "ad" ? false : true;

        userLogInDtl.InvalidLoginAttempt = 0;

        await _userRepository.UpdateAsync(userDtl);

        await _userLoginRepository.UpdateAsync(userLogInDtl);

        if (!userDtl.LoginType.Trim().ToLower().Equals("ad"))
        {
            var emailDto = _mapper.Map<EmailDto>(smtpConfiguration);
            emailDto.To = request.Email;
            emailDto.Subject = "Password Reset Request";
            emailDto.Body = $"Dear {request.LoginName},\n\nNew Password : {request.NewPassword}";

            await _emailService.SendEmail(emailDto);
        }

        var response = new ResetPasswordResponse
        {
            Message = userDtl.LoginType.Trim().ToLower().Equals("ad")
                ? "User unlocked successfully!."
                : "Email sent successfully!.",
            UserId = request.LoginName
        };

        await _publisher.Publish(new ResetPasswordUpdatedEvent { UserName = userDtl.LoginName }, cancellationToken);

        return response;
    }
}