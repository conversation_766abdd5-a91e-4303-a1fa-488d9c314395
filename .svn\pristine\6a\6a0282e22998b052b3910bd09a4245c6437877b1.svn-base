﻿using AutoFixture;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Create;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Update;
using ContinuityPatrol.Application.Features.BiaRules.Events.PaginatedView;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetBusinessServiceDiagramDetail;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByBusinessServiceId;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectDetailById;
using ContinuityPatrol.Domain.ViewModels.BiaRulesModel;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using Microsoft.AspNetCore.Http;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class BIARulesControllerShould
    {
        private BiaRulesController _controller;
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<ILogger<BiaRulesController>> _mockLogger = new();

        public BIARulesControllerShould()
        {
            Initialize();
        }
        internal void Initialize()
        {
            _controller = new BiaRulesController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockMapper.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }
        [Fact]
        public async Task List_ReturnsViewResult()
        {

            var result = await _controller.List();
            var viewResult = Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public async Task List1_ReturnsJsonResult_WithData()
        {
            
            var mockData = new List<BiaRulesListVm>();
            _mockDataProvider.Setup(dp => dp.BiaRule.GetBiaImpactList()).ReturnsAsync(mockData);
            
            var result = await _controller.GetBiaImpactList();

            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task CreateOrUpdate_CreatesBiaImpact_WhenIdIsNull()
        {

            var impactViewModel = new AutoFixture.Fixture().Create<BiaRulesViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateBiaRulesCommand ();
            var response = new BaseResponse { Success = true };

            _mockMapper.Setup(m => m.Map<CreateBiaRulesCommand>(impactViewModel)).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.BiaRule.CreateAsync(createCommand)).ReturnsAsync(response);

            
            var result = await _controller.CreateOrUpdate(impactViewModel);

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesBiaImpact_WhenIdIsNotNull()
        {

            var impactViewModel = new AutoFixture.Fixture().Create<BiaRulesViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateBiaRulesCommand ();
            var response = new BaseResponse { Success = true };
            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            };
            _controller.Request.Form = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues> { { "id", "123" } });

            _mockMapper.Setup(m => m.Map<UpdateBiaRulesCommand>(impactViewModel)).Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.BiaRule.UpdateAsync(updateCommand)).ReturnsAsync(response);

            
            var result = await _controller.CreateOrUpdate(impactViewModel);

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task Delete_data_ReturnsJsonResult_WithSuccess()
        {
            
            var id = "123";
            var response = new BaseResponse { Success = true };
            _mockDataProvider.Setup(dp => dp.BiaRule.DeleteAsync(id)).ReturnsAsync(response);

            
            var result = await _controller.Delete(id);

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetBusinessServiceList_ReturnsJsonResult_WithData()
        {
            
            var businessServiceList = new List<BusinessServiceListVm>();
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceList()).ReturnsAsync(businessServiceList);

            
            var result = await _controller.GetBusinessServiceList();

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetBusinessFunctionList_ReturnsJsonResult_WithData()
        {
            
            var query = new GetBusinessFunctionPaginatedListQuery { 
                 PageNumber = 1,
                 PageSize = 10,
                 SearchString = string.Empty,
            };
            var paginatedList = new PaginatedResult<BusinessFunctionListVm>();
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionPaginatedList(query)).ReturnsAsync(paginatedList);

            
            var result = await _controller.GetBusinessFunctionList();

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetInfraObjectByBusinessServiceId_ReturnsJsonResult_WithData()
        {
            
            var businessServiceId = "123";
            var infraObjects = new List<GetInfraObjectByBusinessServiceIdVm>();
            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectByBusinessServiceId(businessServiceId)).ReturnsAsync(infraObjects);

            
            var result = await _controller.GetInfraObjectByBusinessServiceId(businessServiceId);

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetInfraObjectById_ReturnsJsonResult_WithData()
        {

            var infraObjectId = "123";
            var infraObjectDetails = new GetInfraObjectDetailByIdVm();
            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectDetailsById(infraObjectId)).ReturnsAsync(infraObjectDetails);


            var result = await _controller.GetInfraObjectById(infraObjectId);


            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":true", json);
        }

        // Additional test cases for better coverage

        [Fact]
        public async Task GetBiaImpactList_ReturnsJsonException_WhenExceptionThrown()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.BiaRule.GetBiaImpactList())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetBiaImpactList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task CreateOrUpdate_ReturnsRedirectToAction_WhenValidationExceptionThrown()
        {
            // Arrange
            var impactViewModel = new AutoFixture.Fixture().Create<BiaRulesViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var validationResult = new FluentValidation.Results.ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Property", "Validation error"));
            var validationException = new ValidationException(validationResult);

            _mockMapper.Setup(m => m.Map<CreateBiaRulesCommand>(impactViewModel))
                .Returns(new CreateBiaRulesCommand());
            _mockDataProvider.Setup(dp => dp.BiaRule.CreateAsync(It.IsAny<CreateBiaRulesCommand>()))
                .ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(impactViewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_ReturnsJsonException_WhenGeneralExceptionThrown()
        {
            // Arrange
            var impactViewModel = new AutoFixture.Fixture().Create<BiaRulesViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            _mockMapper.Setup(m => m.Map<CreateBiaRulesCommand>(impactViewModel))
                .Returns(new CreateBiaRulesCommand());
            _mockDataProvider.Setup(dp => dp.BiaRule.CreateAsync(It.IsAny<CreateBiaRulesCommand>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(impactViewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetBiaRulesByentityIdAndType_ReturnsJsonResult_WithData()
        {
            // Arrange
            var entityId = "123";
            var type = "TestType";
            var mockData = new BiaRulesListVm();
            _mockDataProvider.Setup(dp => dp.BiaRule.GetBiaRulesByEntityIdAndType(entityId, type))
                .ReturnsAsync(mockData);

            // Act
            var result = await _controller.GetBiaRulesByentityIdAndType(entityId, type);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetBiaRulesByentityIdAndType_ReturnsJsonException_WhenExceptionThrown()
        {
            // Arrange
            var entityId = "123";
            var type = "TestType";
            _mockDataProvider.Setup(dp => dp.BiaRule.GetBiaRulesByEntityIdAndType(entityId, type))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetBiaRulesByentityIdAndType(entityId, type);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task Delete_ReturnsJsonException_WhenExceptionThrown()
        {
            // Arrange
            var id = "123";
            _mockDataProvider.Setup(dp => dp.BiaRule.DeleteAsync(id))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetBusinessServiceList_ReturnsJsonException_WhenExceptionThrown()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceList())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetBusinessServiceList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetBusinessFunctionList_ReturnsJsonException_WhenExceptionThrown()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionList())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetBusinessFunctionList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetInfraObjectByBusinessServiceId_ReturnsEmptyJson_WhenBusinessServiceIdIsNullOrWhiteSpace()
        {
            // Arrange
            var businessServiceId = "";

            // Act
            var result = await _controller.GetInfraObjectByBusinessServiceId(businessServiceId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        [Fact]
        public async Task GetInfraObjectByBusinessServiceId_ReturnsJsonWithErrorMessage_WhenExceptionThrown()
        {
            // Arrange
            var businessServiceId = "123";
            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectByBusinessServiceId(businessServiceId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetInfraObjectByBusinessServiceId(businessServiceId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"success\":false", json);
        }

        [Fact]
        public async Task GetInfraObjectById_ReturnsEmptyJson_WhenInfraObjectIdIsNullOrWhiteSpace()
        {
            // Arrange
            var infraObjectId = "";

            // Act
            var result = await _controller.GetInfraObjectById(infraObjectId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        [Fact]
        public async Task GetInfraObjectById_ReturnsJsonWithErrorMessage_WhenExceptionThrown()
        {
            // Arrange
            var infraObjectId = "123";
            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectDetailsById(infraObjectId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetInfraObjectById(infraObjectId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"success\":false", json);
        }

        [Fact]
        public async Task GetBIABusinessServiceTreeViewListByBusinessServiceId_ReturnsEmptyJson_WhenBusinessServiceIdIsNullOrWhiteSpace()
        {
            // Arrange
            var businessServiceId = "";

            // Act
            var result = await _controller.GetBIABusinessServiceTreeViewListByBusinessServiceId(businessServiceId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        [Fact]
        public async Task GetBIABusinessServiceTreeViewListByBusinessServiceId_ReturnsJsonResult_WithData()
        {
            // Arrange
            var businessServiceId = "123";
            var mockData = new GetBusinessServiceDiagramDetailVm();
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceDiagramByBusinessServiceId(businessServiceId))
                .ReturnsAsync(mockData);

            // Act
            var result = await _controller.GetBIABusinessServiceTreeViewListByBusinessServiceId(businessServiceId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetBIABusinessServiceTreeViewListByBusinessServiceId_ReturnsJsonWithErrorMessage_WhenExceptionThrown()
        {
            // Arrange
            var businessServiceId = "123";
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceDiagramByBusinessServiceId(businessServiceId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetBIABusinessServiceTreeViewListByBusinessServiceId(businessServiceId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"success\":false", json);
        }

        [Fact]
        public async Task List_PublishesBiaRulesPaginatedEvent_AndReturnsView()
        {
            // Act
            var result = await _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<BiaRulesPaginatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetInfraObjectByBusinessServiceId_ReturnsJsonResult_WithNullBusinessServiceId()
        {
            // Arrange
            string businessServiceId = null;

            // Act
            var result = await _controller.GetInfraObjectByBusinessServiceId(businessServiceId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        [Fact]
        public async Task GetInfraObjectById_ReturnsJsonResult_WithNullInfraObjectId()
        {
            // Arrange
            string infraObjectId = null;

            // Act
            var result = await _controller.GetInfraObjectById(infraObjectId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        [Fact]
        public async Task GetBIABusinessServiceTreeViewListByBusinessServiceId_ReturnsJsonResult_WithNullBusinessServiceId()
        {
            // Arrange
            string businessServiceId = null;

            // Act
            var result = await _controller.GetBIABusinessServiceTreeViewListByBusinessServiceId(businessServiceId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        [Fact]
        public async Task GetInfraObjectByBusinessServiceId_ReturnsJsonResult_WithWhitespaceBusinessServiceId()
        {
            // Arrange
            var businessServiceId = "   ";

            // Act
            var result = await _controller.GetInfraObjectByBusinessServiceId(businessServiceId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        [Fact]
        public async Task GetInfraObjectById_ReturnsJsonResult_WithWhitespaceInfraObjectId()
        {
            // Arrange
            var infraObjectId = "   ";

            // Act
            var result = await _controller.GetInfraObjectById(infraObjectId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        [Fact]
        public async Task GetBIABusinessServiceTreeViewListByBusinessServiceId_ReturnsJsonResult_WithWhitespaceBusinessServiceId()
        {
            // Arrange
            var businessServiceId = "   ";

            // Act
            var result = await _controller.GetBIABusinessServiceTreeViewListByBusinessServiceId(businessServiceId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }
    }
}
