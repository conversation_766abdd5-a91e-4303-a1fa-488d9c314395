using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class TableInformationFixture : IDisposable
{
    public List<TableInformation> TableInformationPaginationList { get; set; }
    public List<TableInformation> TableInformationList { get; set; }
    public TableInformation TableInformationDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public TableInformationFixture()
    {
        var fixture = new Fixture();

        TableInformationList = fixture.Create<List<TableInformation>>();

        TableInformationPaginationList = fixture.CreateMany<TableInformation>(20).ToList();

        TableInformationDto = fixture.Create<TableInformation>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
