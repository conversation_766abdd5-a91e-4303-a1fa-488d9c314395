﻿using ContinuityPatrol.Application.Features.RoboCopy.Commands.Create;
using ContinuityPatrol.Application.Features.RoboCopy.Commands.Update;
using ContinuityPatrol.Application.Features.RoboCopyJob.Commands.Create;
using ContinuityPatrol.Application.Features.RoboCopyJob.Queries.GetList;
using ContinuityPatrol.Application.Features.RoboCopyJob.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RoboCopyJobModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ContinuityPatrol.Application.Features.RoboCopyJob.Commands.update;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class RoboCopyJobService : BaseClient, IRoboCopyJobService
{
    public RoboCopyJobService(IConfiguration configuration, IAppCache appCache, ILogger<RoboCopyJobService> logger) : base(configuration, appCache, logger)
    { 
    }
    public async Task<BaseResponse> CreateRoboCopyJob(CreateRoboCopyJobCommand createRoboCopyJobCommand)
    {
        var request = new RestRequest("api/v6/robocopyjobs", Method.Post);

        request.AddJsonBody(createRoboCopyJobCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteRoboCopyJob(string id)
    {
        var request = new RestRequest($"api/v6/robocopyjob/{id}", Method.Delete);

        return await Get<BaseResponse>(request);
    }

    public async  Task<PaginatedResult<RoboCopyJobListVm>> GetPaginatedRoboCopyJobs(GetRoboCopyJobPaginatedQuery query)
    {
        var request = new RestRequest($"api/v6/robocopyjob/paginated-list/{query}", Method.Get);

        return await Get<PaginatedResult<RoboCopyJobListVm>>(request);
    }

    public async Task<RoboCopyJobDetailVm> GetRoboCopyJobById(string id)
    {
        var request = new RestRequest($"api/v6/robocopyjob/{id}", Method.Get);

        return await Get<RoboCopyJobDetailVm>(request);
    }

    public async Task<List<RoboCopyJobListVm>> GetRoboCopyJobs()
    {
        var request = new RestRequest("api/v6/robocopyjob", Method.Get);

        return await Get<List<RoboCopyJobListVm>>(request);
    }

    public async Task<BaseResponse> UpdateRoboCopyJob(UpdateRoboCopyJobCommand updateRoboCopyJobCommand)
    {
        var request = new RestRequest("api/v6/robocopyjob", Method.Put);

        request.AddJsonBody(updateRoboCopyJobCommand);

        return await Put<BaseResponse>(request);
    }
}
