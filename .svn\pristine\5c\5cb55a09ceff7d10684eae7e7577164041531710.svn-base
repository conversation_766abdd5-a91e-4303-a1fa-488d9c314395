﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/dashboard.css" rel="stylesheet" />

<div class="page-content pageblur">
    <div class="col-12">
        <h6 class="page_title mb-2">
            <i class="cp-dc-mapping"></i><span>Resiliency Mapping</span>
        </h6>
    </div>
    <div class="row g-2 mt-0">
        <div class="col-12 col-lg-7 col-xl-8 col-xxl-9 mt-0">
            <div class="card Card_Design_None position-relative mb-0 border">
                <div class="card-header header position-absolute pb-0 w-100" style="z-index:1;">
                    <span class="card-title"></span>
                    <div class="d-flex text-light gap-3 fs-5 fw-semibold">
                        <div>
                            <span class="text-primary siteCount"></span>
                            <small class="text-dark">Sites</small>
                        </div><span class="fw-light">|</span>
                        <div>
                            <span class="text-primary businessServiceCount"></span>
                            <small class="text-dark">Business Service</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-2">
                    <div id="DCMap-Chart"></div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between gap-2 position-absolute w-100 bottom-0">
                    <div class="row row-cols-5 g-3 w-100 cardSiteDetails">
                         @* <div class="col-auto d-flex gap-2 align-items-start">
                            <div class="card shadow mb-2">
                                <div class="card-body p-2 d-flex align-items-start gap-2">
                                    <img class="mt-1" src="~/img/input_icons/service_available.svg" width="15" />
                                    <div>
                                        <span class="fs-6 fw-semibold">PR Site</span>
                                        <div class="">
                                            <small class="text-light">Dubai</small>
                                            <span class="text-danger small px-1 rounded-pill ms-1" style="background-color:#f8d7da59;">5<i class="cp-dashboard-down fs-8 fw-semibold ms-1"></i></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-auto d-flex gap-2 align-items-start">
                            <div class="card shadow mb-2">
                                <div class="card-body p-2 d-flex align-items-start gap-2">
                                    <img class="mt-1" src="~/img/input_icons/business_functions.svg" width="15" />
                                    <div>
                                        <span class="fs-6 fw-semibold">DR Site</span>
                                        <div class="">
                                            <small class="text-light">Mumbai</small>
                                            <span class="text-danger small px-1 rounded-pill ms-1" style="background-color:#f8d7da59;">5<i class="cp-dashboard-down fs-8 fw-semibold ms-1"></i></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-auto d-flex gap-2 align-items-start">
                            <div class="card shadow mb-2">
                                <div class="card-body p-2 d-flex align-items-start gap-2">
                                    <img class="mt-1" src="~/img/input_icons/dr_ready.svg" width="15" />
                                    <div>
                                        <span class="fs-6 fw-semibold">Near DR</span>
                                        <div class="">
                                            <small class="text-light">Chennai</small>
                                            <span class="text-danger small px-1 rounded-pill ms-1" style="background-color:#f8d7da59;">5<i class="cp-dashboard-down fs-8 fw-semibold ms-1"></i></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-auto d-flex gap-2 align-items-start">
                            <div class="card shadow mb-2">
                                <div class="card-body p-2 d-flex align-items-start gap-2">
                                    <img class="mt-1" src="~/img/input_icons/alerts.svg" width="15" />
                                    <div>
                                        <span class="fs-6 fw-semibold">PR Site</span>
                                        <div class="">
                                            <small class="text-light">Mumbai</small>
                                            <span class="text-danger small px-1 rounded-pill ms-1" style="background-color:#f8d7da59;">5<i class="cp-dashboard-down fs-8 fw-semibold ms-1"></i></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-auto d-flex gap-2 align-items-start">
                            <div class="card shadow mb-2">
                                <div class="card-body p-2 d-flex align-items-start gap-2">
                                    <img class="mt-1" src="~/img/input_icons/incidents.svg" width="15" />
                                    <div>
                                        <span class="fs-6 fw-semibold">PR Site</span>
                                        <div class="">
                                            <small class="text-light">Chennai</small>
                                            <span class="text-danger small px-1 rounded-pill ms-1" style="background-color:#f8d7da59;">5<i class="cp-dashboard-down fs-8 fw-semibold ms-1"></i></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> *@
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-lg-5 col-xl-4 col-xxl-3 mt-0">
            <div class="card Card_Design_None border mb-0">
                <div class="card-header input-group p-4">
                    <span class="card-title">Operational Service Availability</span>
                    <input type="search" id="search-inp-businessCategory" class="form-control search-inp-type" placeholder="Search" />
                </div>
                <div id="businessService" class="card-body p-2 pt-3 mb-2" style="height:calc(100vh - 365px); overflow:auto;">
                    <div class="accordion accordion-flush DC-Accordion" id="accordionExample">
                    </div>
                </div>
            </div>
        </div>


        <div class="col-12 col-md-4 col-xl-5">
            <div class="card Card_Design_None mb-0 h-100">
                <div class="card-header p-2">
                    <span class="card-title">Operational Service Topology</span>
                </div>
                <div class="card-body pt-0" style="height:calc(50vh - 170px); overflow:auto;overflow-x:hidden">
                        <div class="row g-2 align-items-center" id="DCMapping"></div>
                </div>
                @* <div class="card-footer pt-0 d-flex gap-2 align-items-center justify-content-between">
                    <span><i class="cp-single-dot text-warning me-1 fs-9"></i>PR</span>
                    <span><i class="cp-single-dot text-success me-1 fs-9"></i>DR</span>
                    <span><i class="cp-single-dot text-info me-1 fs-9"></i>Near DR</span>
                    <span>|</span>
                    <span><i class="cp-single-dot me-1 fs-9" style="color:var(--bs-green);"></i>Replication inprogress</span>
                    <span><i class="cp-single-dot me-1 fs-9" style="color:var(--bs-yellow);"></i>SLA Breached</span>
                    <span><i class="cp-single-dot me-1 fs-9" style="color:var(--bs-red);"></i>Replication Failure</span>
                </div> *@
            </div>
        </div>
        <div class="col-12 col-md-4 col-xl-3">
            <div class="card Card_Design_None mb-0">
                <div class="card-header p-2">
                    <span class="card-title">Event Summary</span>
                </div>
                <div class="card-body pt-0" style="height: 173px; overflow:auto;">
                    <div class="User_Profile_Timeline DC-Map-Timeline">
                        @* <div class="user-profile-timeline-container">
                            <ul class="tl">
                                <li>
                                    <div class="item-icon"></div>
                                    <div class="item-text">
                                        <div class="item-title w-100">
                                            <div class="d-flex align-items-start gap-2">
                                                <i class="text-danger cp-database mt-1"></i>
                                                <div class="d-grid gap-1 w-100">
                                                    <div class="d-flex justify-content-between">
                                                        <span class="list-title">IO_DataSync_WinApp</span>
                                                        <span class="text-danger">Database Down</span>
                                                    </div>
                                                    <small class="text-light fs-8">09 01 2024 &nbsp; 04:27:05 pm</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="item-icon"></div>
                                    <div class="item-text">
                                        <div class="item-title w-100">
                                            <div class="d-flex align-items-start gap-2">
                                                <i class="text-success cp-database mt-1"></i>
                                                <div class="d-grid gap-1 w-100">
                                                    <div class="d-flex justify-content-between">
                                                        <span class="list-title">IO_DataSync_WinApp</span>
                                                        <span class="text-success">Database Up</span>
                                                    </div>
                                                    <small class="text-light fs-8">09 01 2024 &nbsp; 04:27:05 pm</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="item-icon"></div>
                                    <div class="item-text">
                                        <div class="item-title w-100">
                                            <div class="d-flex align-items-start gap-2">
                                                <i class="text-danger cp-database mt-1"></i>
                                                <div class="d-grid gap-1 w-100">
                                                    <div class="d-flex justify-content-between">
                                                        <span class="list-title">IO_DataSync_WinApp</span>
                                                        <span class="text-danger">Database Down</span>
                                                    </div>
                                                    <small class="text-light fs-8">09 01 2024 &nbsp; 04:27:05 pm</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div> *@
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-4 col-xl-4">
            <div class="card Card_Design_None border mb-0 h-100">
                <div class="card-header p-2 pb-1">
                    <span class="card-title">Operational Heat Map</span>
                </div>
                <div class="card-body pt-0 p-2">
                    <div class="row row-cols-3 g-3 align-items-center h-100">
                        <div class="col">
                            <div class="d-flex align-items-center">
                                <img class="me-1" src="~/img/charts_img/datacenter/application.svg" height="35" />
                                @* <span class="circle me-2 p-2" style="background-color:var(--bs-gray-200)"><i class="cp-application text-primary fs-5 fw-semibold"></i> </span> *@
                                <span>
                                    <span class="d-flex align-items-center applicationdone">
                                        <i class="" id="applicationIcon"></i>
                                        <span class="fs-6 fw-semibold text-danger applicationDownCount impactDetails" type="Application" style="cursor: pointer;"></span><small class="mx-1" id="applicationdata">/</small><small class="text-light applicationTotalCount"></small><br />
                                    </span>
                                    <span>Application</span>
                                </span>
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-flex align-items-center">
                                <img class="me-1" src="~/img/charts_img/datacenter/database.svg" height="35" />
                                @* <span class="circle me-2 p-2" style="background-color:var(--bs-gray-200)"><i class="cp-database text-primary fs-5 fw-semibold"></i> </span> *@

                                <span>
                                    <span class="d-flex align-items-center databasedone">
                                        <i class="" id="databaseIcon"></i>
                                        <span class="fs-6 fw-semibold text-danger databaseDownCount impactDetails" type="Database" style="cursor: pointer;"></span><small class="mx-1" id="databasedata">/</small><small class="text-light databaseTotalCount"></small><br />
                                    </span>
                                    <span>Database</span>
                                </span>
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-flex align-items-center">
                                     <img class="me-1" src="~/img/charts_img/datacenter/Storage.svg" height="35" />
                                @* <span class="circle me-2 p-2" style="background-color:var(--bs-gray-200)"><i class="cp-storage text-primary fs-5 fw-semibold"></i> </span> *@
                                <span>
                                    <span class="d-flex align-items-center storagedone">
                                        <i class="" id="storageIcon"></i>
                                        <span class="fs-6 fw-semibold text-danger storageDownCount impactDetails" type="Storage" style="cursor: pointer;"></span><small class="mx-1" id="storagedata">/</small><small class="text-light storageTotalCount"></small><br />
                                    </span>
                                    <span>Storage</span>
                                </span>
                                
                                
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-flex align-items-center">
                                  <img class="me-1" src="~/img/charts_img/datacenter/Replication.svg" height="35" />
                                @* <span class="circle me-2 p-2" style="background-color:var(--bs-gray-200)"><i class="cp-replication-on text-primary fs-5 fw-semibold"></i> </span> *@
                                <span>
                                     <span class="d-flex align-items-center replicationdone">
                                    <i class="" id="replicationIcon"></i>
                                    <span class="fs-6 fw-semibold replicationDownCount impactDetails" type="Replication" style="cursor: pointer;"></span><small class="mx-1" id="replicationdata">/</small><small class="text-light replicationTotalCount"></small><br />
                                    </span>
                                    <span>Replication</span>
                                </span>
                            </div>
                        </div>
                        <div class="col">
                            <div>
                                <div class="d-flex align-items-center">
                                    <img class="me-1" src="~/img/charts_img/datacenter/Network.svg" height="35" />
                                    @* <span class="circle me-2 p-2" style="background-color:var(--bs-gray-200)"><i class="cp-flow text-primary fs-5 fw-semibold"></i> </span> *@
                                    <span>
                                        <span class="d-flex align-items-center networkdone">
                                        <i class="" id="networkIcon"></i>
                                        <span class="fs-6 fw-semibold networkDownCount impactDetails" type="Network" style="cursor: pointer;"></span><small class="mx-1" id="networkdata">/</small><small class="text-light networkTotalCount"></small><br />
                                       </span>
                                        <span>Network</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div>
                                <div class="d-flex align-items-center">
                                    <img class="me-1" src="~/img/charts_img/datacenter/server.svg" height="35" />
                                    @* <span class="circle me-2 p-2" style="background-color:var(--bs-gray-200)"><i class="cp-book text-primary fs-5 fw-semibold"></i> </span> *@
                                    <span>
                                         <span class="d-flex align-items-center serverdone">
                                        <i class="" id="serverIcon"></i>
                                        <span class="fs-6 fw-semibold serverDownCount impactDetails" type="Server" style="cursor: pointer;"></span>
                                        <small class="mx-1" id="serverdata">/</small><small class="text-light serverTotalCount"></small><br />
                                        </span>
                                        <span>Server</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Serves Heatmap Modal -->
<div class="modal fade" id="impactModalModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollabel" style="min-height:400px;width:1300px">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-service-heatmap-database"></i><span class="page_titleheatmap">Database Heatmap Details</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="min-height: 400px;">
                <table id="HeatmapTable" class="table table-hover datatable" style="table-layout: fixed;">
                   
                </table>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="impactTopologyModalLabel" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered modal-dialog-scrollabel">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-service-heatmap-database"></i><span>Service Topology</span>&nbsp;&nbsp;<span id="TypeHeatmap"></span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="min-height: 300px;">
                <table id="HeatmapTable" class="table table-hover datatable">
                    <thead>
                        <tr>
                            <th>InfraObjects</th>
                            <th>Site</th>
                        </tr>
                    </thead>
                    <tbody id="Tobologybody">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>


<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/maps.js"></script>
<script src="~/lib/amcharts4/worldIndiaLow.js"></script>
<script src="~/lib/amcharts4/pt.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/js/DCMapping/dcMapping.js"></script>
<script src="~/js/dashboard-charts/dcmap-chart.js"></script>

