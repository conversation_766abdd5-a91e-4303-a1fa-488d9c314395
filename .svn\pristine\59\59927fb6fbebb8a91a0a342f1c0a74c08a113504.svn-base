﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowAction.Events.ImportWorkflowAction;

public class ImportWorkflowActionEventHandler : INotificationHandler<ImportWorkflowActionEvent>
{
    private readonly ILogger<ImportWorkflowActionEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ImportWorkflowActionEventHandler(ILoggedInUserService userService,
        ILogger<ImportWorkflowActionEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ImportWorkflowActionEvent importedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Import} {Modules.WorkflowAction}",
            Entity = Modules.WorkflowAction.ToString(),
            ActivityType = ActivityType.Import.ToString(),
            ActivityDetails = "WorkflowActions Imported successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("WorkflowActions Imported successfully.");
    }
}