﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;

namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowProfileInfoRepository : BaseRepository<WorkflowProfileInfo>, IWorkflowProfileInfoRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IWorkflowRepository _workflowRepository;

    public WorkflowProfileInfoRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService, IWorkflowRepository workflowRepository) :
        base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
        _workflowRepository = workflowRepository;
    }

    public override async Task<IReadOnlyList<WorkflowProfileInfo>> ListAllAsync()
    {
        var workflowProfiles = base.QueryAll(workflow =>
            workflow.CompanyId.Equals(_loggedInUserService.CompanyId));

        var workflowProfileDto = MapWorkflowProfileInfo(workflowProfiles);

        return _loggedInUserService.IsAllInfra
            ? await workflowProfileDto.ToListAsync()
            : AssignedInfraObjects(workflowProfileDto);
    }



    public async Task<IReadOnlyList<WorkflowProfileInfo>> ConfiguredProfileInfo()
    {
        var workflowProfiles = base.QueryAll(workflow =>
            workflow.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(wfp => new WorkflowProfileInfo
             {
                 Id = wfp.Id,
                 ReferenceId = wfp.ReferenceId,
                 ProfileId = wfp.ProfileId,
                 ProfileName = wfp.ProfileName,
                 InfraObjectId = wfp.InfraObjectId,
                 InfraObjectName = wfp.InfraObjectName,
             });
        return _loggedInUserService.IsAllInfra
           ? await workflowProfiles.ToListAsync()
           : AssignedInfraObjects(workflowProfiles);

    }
    public override Task<WorkflowProfileInfo> GetByReferenceIdAsync(string id)
    {
        var workflowProfiles = base.GetByReferenceId(id,
            workflow => workflow.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                           workflow.ReferenceId.Equals(id));

        var workflowProfileDto = MapWorkflowProfileInfo(workflowProfiles);

        return Task.FromResult(workflowProfileDto.SingleOrDefault());
    }

    public async Task<List<WorkflowProfileInfo>> GetWorkflowProfileInfoNames()
    {
        var workflowPermission = await _workflowRepository.GetWorkflowPermissions("profile");

       // var workflowProfiles = base.ListAllAsync(x => x.CompanyId.Equals(_loggedInUserService.CompanyId));

        var profileInfos = _loggedInUserService.IsAllInfra
            ? base.QueryAll(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new WorkflowProfileInfo
            {
                ReferenceId = x.ReferenceId,
                ProfileName = x.ProfileName,
                ProfileId = x.ProfileId,
                BusinessServiceId = x.BusinessServiceId
            }).ToList()
            : AssignedInfraObjects(MapWorkflowProfileInfo(base.QueryAll(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))))
                .Select(x => new WorkflowProfileInfo
                {
                    ReferenceId = x.ReferenceId,
                    ProfileName = x.ProfileName,
                    ProfileId = x.ProfileId,
                    BusinessServiceId = x.BusinessServiceId
                }).ToList();


        //if (_loggedInUserService.IsAllInfra)
        //{
        //    var profileInfos = workflowProfiles.Select(x => new WorkflowProfileInfo { ReferenceId = x.ReferenceId, ProfileName = x.ProfileName, ProfileId = x.ProfileId, BusinessServiceId = x.BusinessServiceId }).ToList();

        //    return workflowPermission.Count > 0
        //        ? profileInfos.Concat(await base.FindByFilter(x => workflowPermission.Contains(x.ReferenceId))).DistinctBy(x => x.ReferenceId).ToList()
        //        : profileInfos;
        //}

        //var workflowProfileDto = MapWorkflowProfileInfo(workflowProfiles);

        //var profile = AssignedInfraObjects(workflowProfileDto).ToList();

        //var profileInfo = profile.Select(x => new WorkflowProfileInfo { ReferenceId = x.ReferenceId, ProfileName = x.ProfileName, ProfileId = x.ProfileId, BusinessServiceId = x.BusinessServiceId }).ToList();

        return workflowPermission.Count > 0
            ? profileInfos.Concat(await base.FindByFilter(x => workflowPermission.Contains(x.ReferenceId))).DistinctBy(x => x.ReferenceId).ToList()
            : profileInfos;

        //var profile = _loggedInUserService.IsAllInfra
        //? await workflowProfileDto.ToListAsync()
        //: AssignedInfraObjects(workflowProfileDto).ToList();

        //var profileInfo = profile.Select(x => new WorkflowProfileInfo { ReferenceId = x.ReferenceId, ProfileName = x.ProfileName, ProfileId = x.ProfileId, BusinessServiceId=x.BusinessServiceId }).ToList();

        //return workflowPermission.Count > 0
        //    ? profileInfo.Concat(await base.FindByFilter(x => workflowPermission.Contains(x.ReferenceId))).DistinctBy(x => x.ReferenceId).ToList()
        //    : profileInfo;
    }

    public override IQueryable<WorkflowProfileInfo> GetPaginatedQuery()
    {
        var workflowProfiles = base.QueryAll(workflow =>
           workflow.CompanyId.Equals(_loggedInUserService.CompanyId));

        var workflowDto = MapWorkflowProfileInfo(workflowProfiles);

        return _loggedInUserService.IsAllInfra
            ? workflowDto.AsNoTracking().OrderByDescending(x => x.Id)
            : PaginatedAssignedInfraObjects(workflowDto).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public async Task<WorkflowProfileInfo> GetWorkflowProfileInfoByProfileId(string profileId)
    {
        var workflowProfiles = _loggedInUserService.IsParent
            ? base.FilterBy(workflow => workflow.ProfileId.Equals(profileId))
            : base.FilterBy(workflow => workflow.ProfileId.Equals(profileId) && workflow.CompanyId.Equals(_loggedInUserService.CompanyId));

        var workflowDto = MapWorkflowProfileInfo(workflowProfiles);

        return _loggedInUserService.IsAllInfra
            ? await workflowDto.FirstOrDefaultAsync()
            : GetInfraObjectById(workflowDto.FirstOrDefault());
    }

    //No need to check assigned infraObjects
    public Task<WorkflowProfileInfo> GetWorkflowProfileFilterByProfileId(string profileId)
    {
        var workflowProfiles = _loggedInUserService.IsParent
             ? base.FilterBy(workflow => workflow.ProfileId.Equals(profileId))
             : base.FilterBy(workflow => workflow.ProfileId.Equals(profileId) && workflow.CompanyId.Equals(_loggedInUserService.CompanyId));

        var workflowDto = MapWorkflowProfileInfo(workflowProfiles);

        return Task.FromResult(workflowDto.FirstOrDefault());
    }

    public async Task<List<WorkflowProfileInfo>> GetWorkflowProfileInfoByProfileIdAsync(string profileId)
    {
        var workflowProfiles = _loggedInUserService.IsParent
            ? base.FilterBy(workflow => workflow.ProfileId.Equals(profileId))
            : base.FilterBy(workflow => workflow.ProfileId.Equals(profileId) && workflow.CompanyId.Equals(_loggedInUserService.CompanyId));

        var workflowProfileDto = MapWorkflowProfileInfo(workflowProfiles);

        return _loggedInUserService.IsAllInfra
            ? await workflowProfileDto.ToListAsync()
            : AssignedInfraObjects(workflowProfileDto).ToList();
    }

    public async Task<List<WorkflowProfileInfo>> GetWorkflowProfileInfoByProfileIds(List<string> profileIds)
    {
        var workflowProfiles = _loggedInUserService.IsParent
            ? base.FilterBy(workflow => profileIds.Contains(workflow.ProfileId))
            : base.FilterBy(workflow => profileIds.Contains(workflow.ProfileId) && workflow.CompanyId.Equals(_loggedInUserService.CompanyId));

        var workflowProfileDto = MapWorkflowProfileInfo(workflowProfiles);

        return _loggedInUserService.IsAllInfra
            ? await workflowProfileDto.ToListAsync()
            : AssignedInfraObjects(workflowProfileDto).ToList();
    }

    public async Task<WorkflowProfileInfo> GetProfileIdAttachByWorkflowId(string workflowId)
    {
        var workflowProfiles = _loggedInUserService.IsParent
            ? base.FilterBy(workflow => workflow.WorkflowId.Equals(workflowId))
            : base.FilterBy(workflow => workflow.WorkflowId.Equals(workflowId) && workflow.CompanyId.Equals(_loggedInUserService.CompanyId));

        var workflowProfileDto = MapWorkflowProfileInfo(workflowProfiles);

        return _loggedInUserService.IsAllInfra
            ? await workflowProfileDto.FirstOrDefaultAsync()
            : GetInfraObjectById(workflowProfileDto.FirstOrDefault());
    }

    public async Task<List<WorkflowProfileInfo>> GetProfileIdAttachByInfraObjectId(string infraObjectId)
    {
        var workflowProfiles = _loggedInUserService.IsParent
            ? base.FilterBy(workflow => workflow.InfraObjectId.Equals(infraObjectId))
            : base.FilterBy(workflow => workflow.InfraObjectId.Equals(infraObjectId) && workflow.CompanyId.Equals(_loggedInUserService.CompanyId));

        var workflowProfileDto = MapWorkflowProfileInfo(workflowProfiles);

        return _loggedInUserService.IsAllInfra
            ? await workflowProfileDto.ToListAsync()
            : AssignedInfraObjects(workflowProfileDto).ToList();
    }
    public async Task<List<WorkflowProfileInfo>> GetWorkflowProfileByInfraId(string infraId)
    {
        var workflowProfiles = _loggedInUserService.IsParent
            ? base.FilterBy(workflow => workflow.InfraObjectId.Equals(infraId))
            : base.FilterBy(workflow => workflow.InfraObjectId.Equals(infraId) && workflow.CompanyId.Equals(_loggedInUserService.CompanyId));

        var workflowProfileDto = MapWorkflowProfileInfo(workflowProfiles);

        return _loggedInUserService.IsAllInfra
            ? await workflowProfileDto.ToListAsync()
            : AssignedInfraObjects(workflowProfileDto).ToList();
    }

    public async Task<List<WorkflowProfileInfo>> GetWorkflowProfileByWorkflowId(string workflowId)
    {
        var workflowProfiles = _loggedInUserService.IsParent
            ? base.FilterBy(workflow => workflow.WorkflowId.Equals(workflowId))
            : base.FilterBy(workflow => workflow.WorkflowId.Equals(workflowId) && workflow.CompanyId.Equals(_loggedInUserService.CompanyId));

        var workflowProfileDto = MapWorkflowProfileInfo(workflowProfiles);

        return _loggedInUserService.IsAllInfra
            ? await workflowProfileDto.ToListAsync()
            : AssignedInfraObjects(workflowProfileDto).ToList();
    }

    public Task<bool> IsWorkflowIdUnique(string workflowId)
    {
        var matches = _dbContext.WorkflowProfileInfos.Active().Any(e => e.WorkflowId.Equals(workflowId));

        return Task.FromResult(matches);
    }

    public Task<bool> IsWorkflowProfileInfoNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.WorkflowProfileInfos.Any(e => e.ProfileName.Equals(name)))
            : Task.FromResult(
                _dbContext.WorkflowProfileInfos.Where(e => e.ProfileName.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsWorkflowNameAndProfileNameUnique(string workflowId, string profileId)
    {
        var matches =
            _dbContext.WorkflowProfileInfos.Any(e => e.WorkflowId.Equals(workflowId) && e.ProfileId.Equals(profileId));

        return Task.FromResult(matches);
    }


    private IReadOnlyList<WorkflowProfileInfo> AssignedInfraObjects(IQueryable<WorkflowProfileInfo> infraObjects)
    {
        var infraObjectList = new List<WorkflowProfileInfo>();

        var assignedBusinessFunctions = new List<AssignedBusinessFunctions>();

        if (AssignedEntity.AssignedBusinessServices.Count > 0)
            foreach (var assignedBusinessServices in AssignedEntity.AssignedBusinessServices)
                assignedBusinessFunctions.AddRange(assignedBusinessServices.AssignedBusinessFunctions);

        var assignedBusinessInfraObjects = new List<AssignedInfraObjects>();

        if (assignedBusinessFunctions.Count > 0)
            foreach (var assignedBusinessFunction in assignedBusinessFunctions)
                assignedBusinessInfraObjects.AddRange(assignedBusinessFunction.AssignedInfraObjects);

        foreach (var infraObject in infraObjects)
            if (assignedBusinessInfraObjects.Count > 0)
                infraObjectList.AddRange(from assignedInfraObject in assignedBusinessInfraObjects
                                         where infraObject.InfraObjectId == assignedInfraObject.Id
                                         select infraObject);
        return infraObjectList;
    }
    private IQueryable<WorkflowProfileInfo> PaginatedAssignedInfraObjects(IQueryable<WorkflowProfileInfo> infraObjects)
    {
        if (AssignedEntity != null)
        {
            var assignedInfraIds = AssignedEntity!.AssignedBusinessServices.SelectMany(x => x.AssignedBusinessFunctions.SelectMany(y => y.AssignedInfraObjects.Select(z => z.Id))).ToList();
            return infraObjects.Where(s => assignedInfraIds.Contains(s.InfraObjectId));
        }

        return infraObjects;

    }
    private WorkflowProfileInfo GetInfraObjectById(WorkflowProfileInfo infraObject)
    {
        var services = AssignedEntity.AssignedBusinessServices
            .SelectMany(assignedBusinessService => assignedBusinessService.AssignedBusinessFunctions)
            .SelectMany(assignedBusinessFunction => assignedBusinessFunction.AssignedInfraObjects)
            .Where(assignedInfraObjects => infraObject?.InfraObjectId == assignedInfraObjects.Id)
            .Select(_ => infraObject).SingleOrDefault();

        return services;
    }

    private IQueryable<WorkflowProfileInfo> MapWorkflowProfileInfo(IQueryable<WorkflowProfileInfo> workflowProfile)
    {
        var mappedWorkflowProfileInfo = workflowProfile.Select(data => new
        {
            WorkflowProfileInfo = data,
            WorkflowProfile = _dbContext.WorkflowProfiles.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.ProfileId)),
            InfraObject = _dbContext.InfraObjects.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.InfraObjectId)),
            BusinessFunction = _dbContext.BusinessFunctions.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.BusinessFunctionId)),
            BusinessService = _dbContext.BusinessServices.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.BusinessServiceId)),
            Workflow = _dbContext.WorkFlows.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.WorkflowId))
        });

        var workflowProfileInfoQuery = mappedWorkflowProfileInfo.Select(result => new WorkflowProfileInfo
        {
            Id = result.WorkflowProfileInfo.Id,
            ReferenceId = result.WorkflowProfileInfo.ReferenceId,
            CompanyId = result.WorkflowProfileInfo.CompanyId,
            ProfileId = result.WorkflowProfile.ReferenceId,
            ProfileName = result.WorkflowProfile.Name,
            InfraObjectId = result.InfraObject.ReferenceId,
            InfraObjectName = result.InfraObject.Name,
            BusinessFunctionId = result.BusinessFunction.ReferenceId,
            BusinessFunctionName = result.BusinessFunction.Name,
            BusinessServiceId = result.BusinessService.ReferenceId,
            BusinessServiceName = result.BusinessService.Name,
            WorkflowId = result.Workflow.ReferenceId,
            WorkflowName = result.Workflow.Name,
            CurrentActionId = result.WorkflowProfileInfo.CurrentActionId,
            CurrentActionName = result.WorkflowProfileInfo.CurrentActionName,
            Message = result.WorkflowProfileInfo.Message,
            ProgressStatus = result.WorkflowProfileInfo.ProgressStatus,
            ConditionalOperation = result.WorkflowProfileInfo.ConditionalOperation,
            WorkflowType = result.WorkflowProfileInfo.WorkflowType,
            ActionMode = result.WorkflowProfileInfo.ActionMode,
            Status = result.WorkflowProfileInfo.Status,
            IsLock = result.WorkflowProfileInfo.IsLock,
            IsPublish = result.WorkflowProfileInfo.IsPublish,
            IsActive = result.WorkflowProfileInfo.IsActive,
            CreatedBy = result.WorkflowProfileInfo.CreatedBy,
            CreatedDate = result.WorkflowProfileInfo.CreatedDate,
            LastModifiedBy = result.WorkflowProfileInfo.LastModifiedBy,
            LastModifiedDate = result.WorkflowProfileInfo.LastModifiedDate,
            Index = result.WorkflowProfileInfo.Index
        });

        return workflowProfileInfoQuery;
    }

    public async Task<WorkflowProfileInfo> GetWorkflowProfileInfoByWorkflowIdAndInfraObjectId(string workflowId, string infraObjectId)
    {
        var profileInfo = await _dbContext.WorkflowProfileInfos.Where(x => x.InfraObjectId.Equals(infraObjectId) && x.WorkflowId.Equals(workflowId)).FirstOrDefaultAsync();

        return profileInfo;
    }
}