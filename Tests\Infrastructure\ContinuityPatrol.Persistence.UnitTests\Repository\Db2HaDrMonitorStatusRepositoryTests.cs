using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class Db2HaDrMonitorStatusRepositoryTests : IClassFixture<Db2HaDrMonitorStatusFixture>
{
    private readonly Db2HaDrMonitorStatusFixture _db2HaDrMonitorStatusFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly Db2HaDrMonitorStatusRepository _repository;

    public Db2HaDrMonitorStatusRepositoryTests(Db2HaDrMonitorStatusFixture db2HaDrMonitorStatusFixture)
    {
        _db2HaDrMonitorStatusFixture = db2HaDrMonitorStatusFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new Db2HaDrMonitorStatusRepository(_dbContext);
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var db2HaDrMonitorStatus = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusDto;

        // Act
        var result = await _repository.AddAsync(db2HaDrMonitorStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(db2HaDrMonitorStatus.Type, result.Type);
        Assert.Equal(db2HaDrMonitorStatus.InfraObjectId, result.InfraObjectId);
        Assert.Single(_dbContext.Db2HaDrMonitorStatus);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var db2HaDrMonitorStatus = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusDto;
        await _repository.AddAsync(db2HaDrMonitorStatus);

        db2HaDrMonitorStatus.Type = "UpdatedType";

        // Act
        var result = await _repository.UpdateAsync(db2HaDrMonitorStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedType", result.Type);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var db2HaDrMonitorStatus = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusDto;
        await _repository.AddAsync(db2HaDrMonitorStatus);

        // Act
        var result = await _repository.DeleteAsync(db2HaDrMonitorStatus);

        // Assert
        Assert.Equal(db2HaDrMonitorStatus.Type, result.Type);
        Assert.Empty(_dbContext.Db2HaDrMonitorStatus);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var db2HaDrMonitorStatus = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusDto;
        var addedEntity = await _repository.AddAsync(db2HaDrMonitorStatus);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Type, result.Type);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var db2HaDrMonitorStatus = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusDto;
        await _repository.AddAsync(db2HaDrMonitorStatus);

        // Act
        var result = await _repository.GetByReferenceIdAsync(db2HaDrMonitorStatus.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(db2HaDrMonitorStatus.ReferenceId, result.ReferenceId);
        Assert.Equal(db2HaDrMonitorStatus.Type, result.Type);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var db2HaDrMonitorStatuses = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusList;
        await _repository.AddRangeAsync(db2HaDrMonitorStatuses);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(db2HaDrMonitorStatuses.Count, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldNotReturnInactiveEntities()
    {
        // Arrange
        var db2HaDrMonitorStatuses = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusList;
        db2HaDrMonitorStatuses.First().IsActive = false; // Make one inactive
         _dbContext.Db2HaDrMonitorStatus.AddRange(db2HaDrMonitorStatuses);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(db2HaDrMonitorStatuses.Count - 1, result.Count); // Should exclude the inactive one
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnEntitiesWithMatchingType()
    {
        // Arrange
        var db2HaDrMonitorStatuses = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusList;
        await _repository.AddRangeAsync(db2HaDrMonitorStatuses);

        // Act
        var result = await _repository.GetDetailByType(Db2HaDrMonitorStatusFixture.Type);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(Db2HaDrMonitorStatusFixture.Type, x.Type));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var db2HaDrMonitorStatuses = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusList;
        await _repository.AddRangeAsync(db2HaDrMonitorStatuses);

        // Act
        var result = await _repository.GetDetailByType("non-existent-type");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldNotReturnInactiveEntities()
    {
        // Arrange
        var db2HaDrMonitorStatuses = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusList;
        db2HaDrMonitorStatuses.First().IsActive = false; // Make one inactive
        _dbContext.Db2HaDrMonitorStatus.AddRange(db2HaDrMonitorStatuses);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType(Db2HaDrMonitorStatusFixture.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(db2HaDrMonitorStatuses.Count - 1, result.Count); // Should exclude the inactive one
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region GetDb2HaDrMonitorStatusByInfraObjectId Tests

    [Fact]
    public async Task GetDb2HaDrMonitorStatusByInfraObjectId_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var db2HaDrMonitorStatus = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusDto;

        await _repository.AddAsync(db2HaDrMonitorStatus);

        // Act
        var result = await _repository.GetDb2HaDrMonitorStatusByInfraObjectId(db2HaDrMonitorStatus.InfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Db2HaDrMonitorStatusFixture.InfraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetDb2HaDrMonitorStatusByInfraObjectId_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var db2HaDrMonitorStatuses = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusList;
        await _repository.AddRangeAsync(db2HaDrMonitorStatuses);

        // Act
        var result = await _repository.GetDb2HaDrMonitorStatusByInfraObjectId("74dca453-04ae-4771-b017-0afdb37b5e33");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetDb2HaDrMonitorStatusByInfraObjectId_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => 
            _repository.GetDb2HaDrMonitorStatusByInfraObjectId("invalid-guid"));
    }

    [Fact]
    public async Task GetDb2HaDrMonitorStatusByInfraObjectId_ShouldReturnCorrectEntity_WhenMultipleExist()
    {
        // Arrange
        var db2HaDrMonitorStatuses = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusList;

        db2HaDrMonitorStatuses.First().InfraObjectId = db2HaDrMonitorStatuses[0].InfraObjectId;
        await _repository.AddRangeAsync(db2HaDrMonitorStatuses);

        // Act
        var result = await _repository.GetDb2HaDrMonitorStatusByInfraObjectId(db2HaDrMonitorStatuses[0].InfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(db2HaDrMonitorStatuses[0].InfraObjectId, result.InfraObjectId);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var db2HaDrMonitorStatuses = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusList;

        // Act
        var result = await _repository.AddRangeAsync(db2HaDrMonitorStatuses);

        // Assert
        Assert.Equal(db2HaDrMonitorStatuses.Count, result.Count());
        Assert.Equal(db2HaDrMonitorStatuses.Count, _dbContext.Db2HaDrMonitorStatus.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var db2HaDrMonitorStatuses = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusList;
        await _repository.AddRangeAsync(db2HaDrMonitorStatuses);

        // Act
        var result = await _repository.RemoveRangeAsync(db2HaDrMonitorStatuses);

        // Assert
        Assert.Equal(db2HaDrMonitorStatuses.Count, result.Count());
        Assert.Empty(_dbContext.Db2HaDrMonitorStatus);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var db2HaDrMonitorStatuses = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusList.Take(5).ToList();
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(db2HaDrMonitorStatuses);
        var initialCount = db2HaDrMonitorStatuses.Count;
        
        var toUpdate = db2HaDrMonitorStatuses.Take(2).ToList();
        toUpdate.ForEach(x => x.Type = "UpdatedType");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = db2HaDrMonitorStatuses.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.Type == "UpdatedType").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
