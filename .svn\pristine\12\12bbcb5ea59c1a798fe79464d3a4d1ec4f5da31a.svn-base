﻿using ContinuityPatrol.Application.Features.DRCalendar.Commands.Update;
using ContinuityPatrol.Application.Features.TeamMaster.Commands.Create;
using ContinuityPatrol.Application.Features.TeamMaster.Commands.Update;
using ContinuityPatrol.Application.Features.TeamMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.TeamMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.TeamMasterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
using Microsoft.AspNetCore.Http;
using Serilog.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.Api.Impl.Configuration
{
    public class TeamMasterService : BaseClient, ITeamMasterService
    {
        public TeamMasterService(IConfiguration config, IAppCache cache, ILogger<TeamMasterService> logger)
        : base(config, cache, logger)
        {
        }

       public async Task<BaseResponse>  CreateAsync(CreateTeamMasterCommand team)
        {
            var request = new RestRequest("api/v6/TeamMasterService", Method.Post);

            request.AddJsonBody(team);

            return await Post<BaseResponse>(request);
        }


        public async Task<BaseResponse>  UpdateAsync(UpdateTeamMasterCommand team)
        {
            var request = new RestRequest("api/v6/TeamMasterService", Method.Put);

            request.AddJsonBody(team);

            return await Put<BaseResponse>(request);
        }
        public async Task<BaseResponse>  DeleteAsync(string TeamId)
        {
            var request = new RestRequest($"api/v6/TeamMasterService/{TeamId}", Method.Delete);

            return await Delete<BaseResponse>(request);
        }

        Task<List<TeamMasterDetailVm>> ITeamMasterService.GetAllTeamNames()
        {
            throw new NotImplementedException();
        }

        Task<PaginatedResult<TeamMasterListVm>> ITeamMasterService.GetTeamConfigurationList(GetTeamMasterPaginatedListQuery query)
        {
            throw new NotImplementedException();
        }

        Task<TeamMasterDetailVm> ITeamMasterService.GetTeamNameById(string id)
        {
            throw new NotImplementedException();
        }

        Task<TeamMasterDetailVm> ITeamMasterService.GetTeamNameByName(string name)
        {
            throw new NotImplementedException();
        }

        Task<bool> ITeamMasterService.IsTeamNameAlreadyExist(string name, string id)
        {
            throw new NotImplementedException();
        }

        Task<bool> ITeamMasterService.IsTeamNameExist(string name)
        {
            throw new NotImplementedException();
        }

       
    }
}
