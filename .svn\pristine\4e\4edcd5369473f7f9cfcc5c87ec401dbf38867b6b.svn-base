﻿using ContinuityPatrol.Application.Features.Form.Commands.Create;
using ContinuityPatrol.Application.Features.Form.Commands.Import;
using ContinuityPatrol.Application.Features.Form.Commands.Lock;
using ContinuityPatrol.Application.Features.Form.Commands.Publish;
using ContinuityPatrol.Application.Features.Form.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Form.Commands.Update;
using ContinuityPatrol.Application.Features.Form.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Form.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Form.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.FormModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IFormService
{
    Task<List<FormNameVm>> GetFormNames();
    Task<List<FormListVm>> GetFormList();
    Task<BaseResponse> CreateAsync(CreateFormCommand createFormCommand);
    Task<BaseResponse> SaveAsForm(SaveAsFormCommand saveAsFormCommand);
    Task<BaseResponse> UpdateAsync(UpdateFormCommand updateFormCommand);
    Task<BaseResponse> DeleteAsync(string formId);
    Task<FormDetailVm> GetByReferenceId(string formId);
    Task<bool> IsFormNameExist(string name, string id);
    Task<List<FormTypeVm>> GetFormByType(string type);
    Task<BaseResponse> Publish(UpdateFormPublishCommand updateFormPublishCommand);
    Task<PaginatedResult<FormListVm>> GetPaginatedForms(GetFormPaginatedListQuery query);
    Task<BaseResponse> UpdateFormLock(UpdateFormLockCommand formLock);
    Task<ImportFormResponse> ImportForms(ImportFormCommand command);
}