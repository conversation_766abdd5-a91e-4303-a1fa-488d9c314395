﻿@model ContinuityPatrol.Domain.ViewModels.ServerModel.ServerViewModel

<!--Modal Create-->
<div class="modal-dialog modal-lg modal-dialog-scrollable modal-dialog-centered Organization_modal wizard-sticky-header">
    <div class="modal-content">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-server"></i><span>Server Configuration</span></h6>
            <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body py-0">
            @Html.AntiForgeryToken()
            <div class="wizard-content" id="serverWizard">
                <form id="server-form" class="tab-wizard wizard-circle wizard clearfix mb-2 example-form">
                    @* asp-controller="Server" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data" *@
                    <input asp-for="Properties" type="hidden" id="Props" class="form-control" />
                    <input id="serverLogo" type="hidden" asp-for="Logo" />
                    <input id="serverId" type="hidden" asp-for="Id" />
                    <input id="serverStatus" type="hidden" asp-for="Status" />
                    <input id="serverFormVersion" type="hidden" asp-for="FormVersion" />
                    <input id="serverAttach" type="hidden" asp-for="IsAttached" />
                    <h6>
                        <span class="step">
                            <i class="cp-control-file-type"></i>
                        </span>
                        <span class="step_title">
                            Server Details
                        </span>
                    </h6>
                    <section>
                        <div>
                            <div class="form-group">
                                <div class="form-label">Name</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-name"></i></span>
                                    <input asp-for="Name" maxlength="100" id="ServerName" type="text" class="form-control"
                                           placeholder="Enter Server Name" autocomplete="off" />
                                </div>
                                <span asp-validation-for="Name" id="Name-error"></span>
                            </div>
                            <div class="form-group">
                                <div class="form-label">Site Name</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-web"></i></span>
                                    <select asp-for="SiteId"
                                            data-placeholder="Select Site Name"
                                            id="siteNames"
                                            class="form-select-modal"
                                            aria-label="Default select example"
                                            data-live-search="true"
                                            required>
                                        <option value=""></option>
                                    </select>
                                    <input type="hidden" asp-for="SiteName" id="names" />
                                </div>
                                <span asp-validation-for="SiteId" id="SiteName-error"></span>
                            </div>
                            <div class="form-group">
                                <div class="form-label">Operational Service</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-business-service"></i></span>
                                    <select asp-for="BusinessServiceId"
                                            data-placeholder="Select Operational Service"
                                            class="form-select-modal"
                                            aria-label="Default select example"
                                            data-live-search="true"
                                            id="businessServiceID"
                                            required>
                                        <option value=""></option>
                                    </select>
                                    <input type="hidden" asp-for="BusinessServiceName" id="businessServiceName" />
                                </div>
                                <span asp-validation-for="BusinessServiceId" id="BusinessServiceIdError"></span>
                            </div>
                        </div>
                        <div class="row row-cols-2">
                            <div class="col">
                                <div class="form-group">
                                    <div class="form-label">Server Role</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-server-role"></i></span>
                                        <select asp-for="RoleType" id="serverRole"
                                                data-placeholder="Select Server Role"
                                                aria-label="Default select example"
                                                data-live-search="true"
                                                class="form-select-modal" required>
                                            <option value=""></option>
                                        </select>
                                    </div>
                                    <span asp-validation-for="RoleType" id="RoleType-error"></span>
                                </div>
                                <input type="hidden" asp-for="RoleTypeId" id="server_RoleTypeId" />
                            </div>
                            <div class="col">
                                <div class="form-group ">
                                    <div class="form-label">Server Type</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-production-server-name"></i></span>
                                        <select asp-for="ServerType"
                                                data-placeholder="Select Server Type"
                                                data-live-search="true"
                                                id="serverType"
                                                class="form-select-modal"
                                                required>
                                            <option value=""></option>
                                        </select>
                                    </div>
                                    <span asp-validation-for="ServerType" id="ServerType-error"></span>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" asp-for="ServerTypeId" id="server_ServerTypeId" />
                        <div class=" row row-cols-2">
                            <div class="col">
                                <div class="form-group">
                                    <div class="form-label">OS Type</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-os-type" id="osTypeIcon"></i></span>
                                        <select asp-for="OSTypeId"
                                                data-placeholder="Select OS Type"
                                                data-live-search="true"
                                                id="osType"
                                                class="form-select-modal"
                                                required>
                                        </select>
                                        <input asp-for="OSType" type="hidden" id="ostypeid" />
                                    </div>
                                    <span asp-validation-for="OSType" id="OS-error"></span>
                                </div>
                            </div>
                            <div class="col">
                                <div class="col">
                                    <div class="form-group">
                                        <div class="form-label">Version</div>
                                        <div class="input-group">
                                            <span class=" input-group-text"><i class="cp-version"></i></span>
                                            <select asp-for="Version"
                                                    data-placeholder="Select Version"
                                                    data-live-search="true"
                                                    id="version"
                                                    class="form-select-modal infraComponentsVersion"
                                                    required>
                                            </select>
                                        </div>
                                        <span asp-validation-for="Version" id="Version-error"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-label"> License Key</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-license-key"></i></span>
                                <select asp-for="LicenseKey"
                                        data-placeholder="Select License Key"
                                        data-live-search="true"
                                        id="license"
                                        class="form-select-modal"></select>
                            </div>
                            <span asp-validation-for="LicenseKey" id="Licensekey-error"></span>
                            <div class="d-flex justify-content-between">
                                <small class="text-secondary" id="information">                                   
                                </small>
                            </div>                          
                        </div>
                        <input asp-for="LicenseId" type="hidden" id="server_LicenseId" />
                    </section>
                    <h6>
                        <span class="step">
                            <i class="cp-os-type" id="serverTypeTitleIcon"></i>
                        </span>
                        <span class="step_title" id="serverTypeName">
                            Server
                        </span>
                    </h6>
                    <section>
                        <div id="formRenderingArea" class="row">
                        </div>
                    </section>
                </form>
            </div>
        </div>
        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary">
                <i class="cp-note me-1"></i>Note: All fields are mandatory
                except optional
            </small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm btn-cancel" data-bs-dismiss="modal">Cancel</button>
                <a class="btn btn-primary prev_btn btn-sm" href="javascript:void(0)" role="menuitem"
                   onclick="form.steps('previous')">Previous</a>
                <a class="btn btn-primary next_btn btn-sm" id="nextButton" href="javascript:void(0)" role="menuitem">Next</a>
                <a class="btn btn-primary finish_btn btn-sm" id="saveButton" role="menuitem"
                   onclick="form.steps('finish') ">Save</a>
            </div>
        </div>
    </div>
</div>

@section Scripts
{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}
