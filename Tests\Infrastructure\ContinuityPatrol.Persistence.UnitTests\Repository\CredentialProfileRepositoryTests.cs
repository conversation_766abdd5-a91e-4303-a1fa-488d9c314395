using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class CredentialProfileRepositoryTests : IClassFixture<CredentialProfileFixture>
{
    private readonly CredentialProfileFixture _credentialProfileFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CredentialProfileRepository _repository;

    public CredentialProfileRepositoryTests(CredentialProfileFixture credentialProfileFixture)
    {
        _credentialProfileFixture = credentialProfileFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CredentialProfileRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;

        // Act
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(credentialProfile.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(credentialProfile.Name, result.Name);
        Assert.Equal(credentialProfile.CredentialType, result.CredentialType);
        Assert.Single(_dbContext.CredentialProfiles);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
        await _dbContext.SaveChangesAsync();

        credentialProfile.Name = "UpdatedName";
        credentialProfile.CredentialType = "UpdatedType";

        // Act
        _dbContext.CredentialProfiles.Update(credentialProfile);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(credentialProfile.ReferenceId);

        // Assert
        Assert.Equal("UpdatedName", result.Name);
        Assert.Equal("UpdatedType", result.CredentialType);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
        await _dbContext.SaveChangesAsync();

        // Act
        credentialProfile.IsActive = false;

        _dbContext.CredentialProfiles.Update(credentialProfile);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
        await _dbContext.SaveChangesAsync();

        var results = await _repository.ListAllAsync();
        // Act
        var result = await _repository.GetByIdAsync(credentialProfile.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(credentialProfile.Id, result.Id);
        Assert.Equal(credentialProfile.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
   
        await _dbContext.SaveChangesAsync();

        var results = await _repository.ListAllAsync();
        // Act
        var result = await _repository.GetByReferenceIdAsync(credentialProfile.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(credentialProfile.ReferenceId, result.ReferenceId);
        Assert.Equal(credentialProfile.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;
        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(credentialProfiles.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetType Tests

    [Fact]
    public async Task GetType_ShouldReturnCredentialProfilesOfSpecificType()
    {
        // Arrange
        var credentialType = "SSH";
        var credentialProfiles = new List<CredentialProfile>
        {
            new CredentialProfile 
            { 
                Name = "Profile1",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile 
            { 
                Name = "Profile2",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile 
            { 
                Name = "Profile3",
                CredentialType = "FTP",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repository.GetType(credentialType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(credentialType, x.CredentialType));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetType_ShouldReturnEmpty_WhenNoMatchingType()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;
        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repository.GetType("NON_EXISTENT_TYPE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetCredentialProfileNames Tests

    [Fact]
    public async Task GetCredentialProfileNames_ShouldReturnNamesAndReferenceIds()
    {
        // Arrange
        var credentialProfiles = new List<CredentialProfile>
        {
            new CredentialProfile 
            { 
                Name = "Profile1",
                CredentialType = "SSH",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile 
            { 
                Name = "Profile2",
                CredentialType = "FTP",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repository.GetCredentialProfileNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.NotNull(x.Name));
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
    }

    [Fact]
    public async Task GetCredentialProfileNames_ShouldReturnEmpty_WhenNoCredentialProfiles()
    {
        // Act
        var result = await _repository.GetCredentialProfileNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsCredentialProfileNameExist Tests

    [Fact]
    public async Task IsCredentialProfileNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        credentialProfile.Name = "ExistingName";
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.IsCredentialProfileNameExist("ExistingName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsCredentialProfileNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;
        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repository.IsCredentialProfileNameExist("NonExistentName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsCredentialProfileNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        credentialProfile.Name = "SameName";
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
        
        await _dbContext.SaveChangesAsync();

        var results = await _repository.ListAllAsync();

        // Act
        var result = await _repository.IsCredentialProfileNameExist("SameName", credentialProfile.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsCredentialProfileNameUnique Tests

    [Fact]
    public async Task IsCredentialProfileNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var credentialProfile = _credentialProfileFixture.CredentialProfileDto;
        credentialProfile.Name = "UniqueName";
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.IsCredentialProfileNameUnique("UniqueName");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsCredentialProfileNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;
        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = await _repository.IsCredentialProfileNameUnique("NonExistentName");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetCredentialProfileByType Tests

    [Fact]
    public async Task GetCredentialProfileByType_ShouldReturnProfilesOfSpecificType()
    {
        // Arrange
        var credentialType = "SSH";
        var credentialProfiles = new List<CredentialProfile>
        {
            new CredentialProfile
            {
                Name = "Profile1",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile
            {
                Name = "Profile2",
                CredentialType = credentialType,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile
            {
                Name = "Profile3",
                CredentialType = "FTP",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = _repository.GetCredentialProfileByType(credentialType);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(2, resultList.Count);
        Assert.All(resultList, x => Assert.Equal(credentialType, x.CredentialType));
        Assert.All(resultList, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetCredentialProfileByType_ShouldReturnEmpty_WhenNoMatchingType()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList;
        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var result = _repository.GetCredentialProfileByType("NON_EXISTENT_TYPE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.ToList());
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var credentoialProfile = _credentialProfileFixture.CredentialProfileList;
        var credentialProfile1 = credentoialProfile[0];
        var credentialProfile2 = credentoialProfile[1];

        credentialProfile2.Name = "DifferentName";

        // Act
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile1);
        await _dbContext.CredentialProfiles.AddAsync(credentialProfile2);
        await _dbContext.SaveChangesAsync();

        var results = await _repository.ListAllAsync();


        // Assert
        Assert.Equal(2, results.Count);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.CredentialProfiles.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var credentialProfiles = _credentialProfileFixture.CredentialProfileList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(credentialProfiles);
        var initialCount = credentialProfiles.Count;

        var toUpdate = credentialProfiles.Take(2).ToList();
        toUpdate.ForEach(x => x.CredentialType = "Updated");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = credentialProfiles.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.CredentialType == "Updated").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleCredentialTypeFiltering()
    {
        // Arrange
        var credentialProfiles = new List<CredentialProfile>
        {
            new CredentialProfile
            {
                Name = "Profile1",
                CredentialType = "SSH",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile
            {
                Name = "Profile2",
                CredentialType = "FTP",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            },
            new CredentialProfile
            {
                Name = "Profile3",
                CredentialType = "SSH",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = CredentialProfileFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(credentialProfiles);

        // Act
        var sshProfiles = await _repository.GetType("SSH");
        var ftpProfiles = await _repository.GetType("FTP");

        // Assert
        Assert.Equal(2, sshProfiles.Count);
        Assert.Single(ftpProfiles);
        Assert.All(sshProfiles, x => Assert.Equal("SSH", x.CredentialType));
        Assert.All(ftpProfiles, x => Assert.Equal("FTP", x.CredentialType));
    }

    #endregion
}
