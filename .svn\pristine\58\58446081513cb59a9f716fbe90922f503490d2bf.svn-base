using ContinuityPatrol.Application.Features.InfraMaster.Events.Delete;

namespace ContinuityPatrol.Application.Features.InfraMaster.Commands.Delete;

public class DeleteInfraMasterCommandHandler : IRequestHandler<DeleteInfraMasterCommand, DeleteInfraMasterResponse>
{
    private readonly IInfraMasterRepository _infraMasterRepository;
    private readonly IPublisher _publisher;

    public DeleteInfraMasterCommandHandler(IInfraMasterRepository infraMasterRepository, IPublisher publisher)
    {
        _infraMasterRepository = infraMasterRepository;

        _publisher = publisher;
    }

    public async Task<DeleteInfraMasterResponse> Handle(DeleteInfraMasterCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _infraMasterRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.InfraMaster),
            new NotFoundException(nameof(Domain.Entities.InfraMaster), request.Id));

        eventToDelete.IsActive = false;

        await _infraMasterRepository.UpdateAsync(eventToDelete);

        var response = new DeleteInfraMasterResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.InfraMaster), eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new InfraMasterDeletedEvent { Name = eventToDelete.Name }, cancellationToken);

        return response;
    }
}