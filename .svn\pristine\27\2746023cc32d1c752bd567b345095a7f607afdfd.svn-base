﻿using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Workflow.Commands.SaveAs;

public class SaveAsWorkflowCommandValidator : AbstractValidator<SaveAsWorkflowCommand>
{
    private readonly IWorkflowRepository _workflowRepository;

    public SaveAsWorkflowCommandValidator(IWorkflowRepository workflowRepository)
    {
        _workflowRepository = workflowRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required")
            //.Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .Matches(@"^([a-zA-Z]+(?:[_\s-]?))([a-zA-Z\d]+(?:[_\s-]?))*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Length(3, 200).WithMessage("{PropertyName} should contain between 3 to 200 characters");

        RuleFor(p => p.Properties)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Must(IsValidJson).WithMessage("{PropertyName} must be a valid JSON string.");

        RuleFor(e => e)
            .MustAsync(WorkflowNameUnique)
            .WithMessage("A same name already exists.");

        RuleFor(y => y)
            .NotNull()
            .MustAsync(VerifyGuid)
            .WithMessage("Id is invalid");
    }

    private Task<bool> VerifyGuid(SaveAsWorkflowCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.WorkflowId, "Workflow Id");

        return Task.FromResult(true);
    }

    private async Task<bool> WorkflowNameUnique(SaveAsWorkflowCommand e, CancellationToken token)
    {
        return !await _workflowRepository.IsWorkflowNameUnique(e.Name);
    }

    private bool IsValidJson(string properties)
    {
        if (string.IsNullOrWhiteSpace(properties))
            return false;

        properties = properties.Trim();
        if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
            (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
            try
            {
                var obj = JsonConvert.DeserializeObject(properties);
                return obj != null;
            }
            catch (JsonException)
            {
                return false;
            }

        return false;
    }
}