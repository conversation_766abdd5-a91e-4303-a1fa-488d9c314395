﻿namespace ContinuityPatrol.Web.Middlewares;

public class CheckRefererHeaderMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<CheckRefererHeaderMiddleware> _logger;

    public CheckRefererHeaderMiddleware(RequestDelegate next, ILogger<CheckRefererHeaderMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task Invoke(HttpContext context)
    {
        if (context.Request.Headers.ContainsKey("Referer"))
        {
            await _next(context);
        }
        else
        {
            var path = context.Request.Path.Value!;

            if (VerifyValidPath(path))
            {
                await _next(context);
            }
            else
            {
                _logger.LogWarning("RefererHeaderMiddleware : Validation path Failed. Redirect to Login");

                context.Response.Redirect("/Account/Logout");
            }
        }
    }

    private static bool VerifyValidPath(string path)
    {
        return path.Contains("login", StringComparison.OrdinalIgnoreCase) ||
               path.Contains("prelogin", StringComparison.OrdinalIgnoreCase) ||
               path.Contains("basic", StringComparison.OrdinalIgnoreCase) ||
               path.Contains("canvas", StringComparison.OrdinalIgnoreCase) ||
               ContainsSingleSlash(path) ||
               path.Contains("logout", StringComparison.OrdinalIgnoreCase) ||
               path.Contains("hub", StringComparison.OrdinalIgnoreCase) ||
               path.Contains("api/", StringComparison.OrdinalIgnoreCase) ||
               path.Contains("health", StringComparison.OrdinalIgnoreCase);
    }

    private static bool ContainsSingleSlash(string path)
    {
        return path.Length == 1 && path[0] == '/';
    }
}

public static class CheckRefererHeaderMiddlewareExtension
{
    public static IApplicationBuilder UseRefererHeaderMiddleware(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<CheckRefererHeaderMiddleware>();
    }
}
