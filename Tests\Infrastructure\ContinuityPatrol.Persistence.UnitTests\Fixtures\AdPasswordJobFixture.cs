using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class AdPasswordJobFixture : IDisposable
{
    public List<AdPasswordJob> AdPasswordJobPaginationList { get; set; }
    public List<AdPasswordJob> AdPasswordJobList { get; set; }
    public AdPasswordJob AdPasswordJobDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public AdPasswordJobFixture()
    {
        var fixture = new Fixture();

        AdPasswordJobList = fixture.Create<List<AdPasswordJob>>();

        AdPasswordJobPaginationList = fixture.CreateMany<AdPasswordJob>(20).ToList();

        AdPasswordJobPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AdPasswordJobPaginationList.ForEach(x => x.IsActive = true);

        AdPasswordJobList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AdPasswordJobList.ForEach(x => x.IsActive = true);

        AdPasswordJobDto = fixture.Create<AdPasswordJob>();
        AdPasswordJobDto.ReferenceId = Guid.NewGuid().ToString();
        AdPasswordJobDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
