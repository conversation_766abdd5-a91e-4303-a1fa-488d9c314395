using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.WorkflowDrCalenderModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetPaginatedList;

public class GetWorkflowDrCalenderPaginatedListQueryHandler : IRequestHandler<GetWorkflowDrCalenderPaginatedListQuery, PaginatedResult<WorkflowDrCalenderListVm>>
{
    private readonly IWorkflowDrCalenderRepository _workflowDrCalenderRepository;
    private readonly IMapper _mapper;

    public GetWorkflowDrCalenderPaginatedListQueryHandler(IMapper mapper, IWorkflowDrCalenderRepository workflowDrCalenderRepository)
    {
        _mapper = mapper;
        _workflowDrCalenderRepository = workflowDrCalenderRepository;
    }

    public async Task<PaginatedResult<WorkflowDrCalenderListVm>> Handle(GetWorkflowDrCalenderPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _workflowDrCalenderRepository.GetPaginatedQuery();

        var productFilterSpec = new WorkflowDrCalenderFilterSpecification(request.SearchString);

        var workflowDrCalenderList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<WorkflowDrCalenderListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return workflowDrCalenderList;
    }
}
