using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DriftResourceSummary.Commands.Create;
using ContinuityPatrol.Application.Features.DriftResourceSummary.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftResourceSummary.Commands.Update;
using ContinuityPatrol.Application.Features.DriftResourceSummary.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftResourceSummary.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.DriftResourceSummaryModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DriftResourceSummaryControllerTests : IClassFixture<DriftResourceSummaryFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DriftResourceSummarysController _controller;
    private readonly DriftResourceSummaryFixture _driftResourceSummaryFixture;

    public DriftResourceSummaryControllerTests(DriftResourceSummaryFixture driftResourceSummaryFixture)
    {
        _driftResourceSummaryFixture = driftResourceSummaryFixture;

        var testBuilder = new ControllerTestBuilder<DriftResourceSummarysController>();
        _controller = testBuilder.CreateController(
            _ => new DriftResourceSummarysController(),
            out _mediatorMock);
    }

    #region GetDriftResourceSummaries Tests

    [Fact]
    public async Task GetDriftResourceSummaries_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedList = _driftResourceSummaryFixture.DriftResourceSummaryListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftResourceSummaryListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDriftResourceSummarys();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftResourceSummaryListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item => Assert.NotNull(item.EntityName));
        Assert.All(returnedList, item => Assert.True(item.TotalCount > 0));
        Assert.All(returnedList, item => Assert.NotNull(item.Type));
    }

    [Fact]
    public async Task GetDriftResourceSummaries_WithEmptyResult_ReturnsOkWithEmptyList()
    {
        // Arrange
        var emptyList = new List<DriftResourceSummaryListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftResourceSummaryListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDriftResourceSummarys();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftResourceSummaryListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDriftResourceSummaries_WithException_ThrowsException()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftResourceSummaryListQuery>(), default))
            .ThrowsAsync(new Exception("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _controller.GetDriftResourceSummarys());
    }

    #endregion

    #region CreateDriftResourceSummary Tests

    [Fact]
    public async Task CreateDriftResourceSummary_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftResourceSummaryFixture.CreateDriftResourceSummaryCommand;
        var expectedResponse = _driftResourceSummaryFixture.CreateDriftResourceSummaryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftResourceSummary(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftResourceSummaryResponse>(createdResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
        Assert.NotNull(command.EntityName);
        Assert.True(command.TotalCount > 0);
        Assert.True(command.ConflictCount >= 0);
        Assert.True(command.IsConflict);
    }

    [Fact]
    public async Task CreateDriftResourceSummary_WithNullCommand_ThrowsArgumentNullException()
    {
        // Arrange
        CreateDriftResourceSummaryCommand nullCommand = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.CreateDriftResourceSummary(nullCommand));
    }

    [Fact]
    public async Task CreateDriftResourceSummary_WithInvalidInfraObjectId_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftResourceSummaryFixture.CreateDriftResourceSummaryCommand;
        command.InfraObjectId = "invalid-guid"; // Invalid GUID

        var failureResponse = new CreateDriftResourceSummaryResponse
        {
            Success = false,
            Message = "Invalid InfraObjectId format"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.CreateDriftResourceSummary(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftResourceSummaryResponse>(createdResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("Invalid", returnedResponse.Message);
    }

    #endregion

    #region UpdateDriftResourceSummary Tests

    [Fact]
    public async Task UpdateDriftResourceSummary_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _driftResourceSummaryFixture.UpdateDriftResourceSummaryCommand;
        var expectedResponse = _driftResourceSummaryFixture.UpdateDriftResourceSummaryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftResourceSummary(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftResourceSummaryResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
        Assert.True(Guid.TryParse(returnedResponse.Id, out _));
        Assert.NotNull(command.EntityName);
        Assert.True(command.TotalCount > 0);
        Assert.True(command.ConflictCount >= 0);
    }

    [Fact]
    public async Task UpdateDriftResourceSummary_WithNullCommand_ThrowsArgumentNullException()
    {
        // Arrange
        UpdateDriftResourceSummaryCommand nullCommand = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.UpdateDriftResourceSummary(nullCommand));
    }

    [Fact]
    public async Task UpdateDriftResourceSummary_WithInvalidId_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftResourceSummaryFixture.UpdateDriftResourceSummaryCommand;
        command.Id = "invalid-guid";

        var failureResponse = new UpdateDriftResourceSummaryResponse
        {
            Success = false,
            Message = "Invalid ID format"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.UpdateDriftResourceSummary(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftResourceSummaryResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("Invalid", returnedResponse.Message);
    }

    #endregion

    #region GetDriftResourceSummaryById Tests

    [Fact]
    public async Task GetDriftResourceSummaryById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _driftResourceSummaryFixture.DriftResourceSummaryDetailVm;
        expectedDetail.Id = id;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftResourceSummaryDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftResourceSummaryById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftResourceSummaryDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.Id);
        Assert.NotNull(returnedDetail.EntityName);
        Assert.True(returnedDetail.TotalCount > 0);
        Assert.True(returnedDetail.ConflictCount >= 0);
        Assert.True(returnedDetail.IsConflict);
    }

    [Fact]
    public async Task GetDriftResourceSummaryById_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDriftResourceSummaryById(invalidId));
    }

    [Fact]
    public async Task GetDriftResourceSummaryById_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetDriftResourceSummaryById(nullId));
    }

    #endregion

    #region DeleteDriftResourceSummary Tests

    [Fact]
    public async Task DeleteDriftResourceSummary_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftResourceSummaryFixture.DeleteDriftResourceSummaryResponse;
        expectedResponse.IsActive=false;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDriftResourceSummaryCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftResourceSummary(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftResourceSummaryResponse>(okResult.Value);
        Assert.Equal(false, returnedResponse.IsActive);
        Assert.True(returnedResponse.Success);
        Assert.Contains("deleted successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task DeleteDriftResourceSummary_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDriftResourceSummary(invalidId));
    }

    [Fact]
    public async Task DeleteDriftResourceSummary_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteDriftResourceSummary(nullId));
    }

    #endregion

    #region Additional Comprehensive Test Cases

    [Fact]
    public async Task CreateDriftResourceSummary_WithHighConflictCount_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftResourceSummaryFixture.CreateDriftResourceSummaryCommand;
        command.EntityName = "Critical Enterprise Database";
        command.TotalCount = 1000;
        command.ConflictCount = 150;
        command.NonConflictCount = 850;
        command.IsConflict = true;
        var expectedResponse = _driftResourceSummaryFixture.CreateDriftResourceSummaryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftResourceSummary(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftResourceSummaryResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Critical Enterprise Database", command.EntityName);
        Assert.Equal(1000, command.TotalCount);
        Assert.Equal(150, command.ConflictCount);
        Assert.True(command.IsConflict);
    }

    [Fact]
    public async Task UpdateDriftResourceSummary_WithConflictResolution_ReturnsOkResult()
    {
        // Arrange
        var command = _driftResourceSummaryFixture.UpdateDriftResourceSummaryCommand;
        command.ConflictCount = 0; // Conflicts resolved
        command.NonConflictCount = 500;
        command.IsConflict = false;
        command.EntityName = "Resolved Enterprise Resource";
        var expectedResponse = _driftResourceSummaryFixture.UpdateDriftResourceSummaryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftResourceSummary(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftResourceSummaryResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal(0, command.ConflictCount);
        Assert.False(command.IsConflict);
        Assert.Equal("Resolved Enterprise Resource", command.EntityName);
    }

    [Fact]
    public async Task GetDriftResourceSummaryById_WithDetailedMetrics_ReturnsCompleteData()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _driftResourceSummaryFixture.DriftResourceSummaryDetailVm;
        expectedDetail.Id = id; // Set the ID to match the test parameter
        expectedDetail.EntityName = "Enterprise Infrastructure Summary";
        expectedDetail.TotalCount = 2000;
        expectedDetail.ConflictCount = 200;
        expectedDetail.NonConflictCount = 1800;
        expectedDetail.IsConflict = true;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftResourceSummaryDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftResourceSummaryById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftResourceSummaryDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.Id);
        Assert.Equal("Enterprise Infrastructure Summary", returnedDetail.EntityName);
        Assert.Equal(2000, returnedDetail.TotalCount);
        Assert.Equal(200, returnedDetail.ConflictCount);
        Assert.Equal(1800, returnedDetail.NonConflictCount);

        // Verify conflict percentage calculation
        var conflictPercentage = (double)returnedDetail.ConflictCount / returnedDetail.TotalCount * 100;
        Assert.Equal(10.0, conflictPercentage);
    }

    [Fact]
    public async Task GetDriftResourceSummarys_WithMultipleEntityTypes_ReturnsVariedSummaries()
    {
        // Arrange
        var expectedList = _driftResourceSummaryFixture.DriftResourceSummaryListVm;
        for (int i = 0; i < expectedList.Count; i++)
        {
            expectedList[i].EntityName = i % 3 == 0 ? "Database Server" :
                          i % 3 == 1 ? "Web Server" : "Application Server";
            expectedList[i].TotalCount = (i + 1) * 100;
            expectedList[i].ConflictCount = (i + 1) * 10;
            expectedList[i].NonConflictCount = expectedList[i].TotalCount - expectedList[i].ConflictCount;
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftResourceSummaryListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDriftResourceSummarys();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftResourceSummaryListVm>>(okResult.Value);
        Assert.NotNull(returnedList);
        Assert.Contains(returnedList, item => item.EntityName.Contains("Database"));
        Assert.Contains(returnedList, item => item.EntityName.Contains("Web"));
        Assert.Contains(returnedList, item => item.EntityName.Contains("Application"));
        Assert.All(returnedList, item => Assert.True(item.TotalCount > 0));
    }

    [Fact]
    public async Task CreateDriftResourceSummary_WithZeroConflicts_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftResourceSummaryFixture.CreateDriftResourceSummaryCommand;
        command.EntityName = "Compliant Enterprise System";
        command.TotalCount = 500;
        command.ConflictCount = 0;
        command.NonConflictCount = 500;
        command.IsConflict = false;
        var expectedResponse = _driftResourceSummaryFixture.CreateDriftResourceSummaryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftResourceSummary(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftResourceSummaryResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Compliant Enterprise System", command.EntityName);
        Assert.Equal(0, command.ConflictCount);
        Assert.False(command.IsConflict);
        Assert.Equal(command.TotalCount, command.NonConflictCount);
    }

    [Fact]
    public async Task UpdateDriftResourceSummary_WithIncrementalConflicts_ReturnsOkResult()
    {
        // Arrange
        var command = _driftResourceSummaryFixture.UpdateDriftResourceSummaryCommand;
        command.TotalCount = 1000;
        command.ConflictCount = 50; // Incremental increase
        command.NonConflictCount = 950;
        command.IsConflict = true;
        command.EntityName = "Monitored Enterprise Infrastructure";
        var expectedResponse = _driftResourceSummaryFixture.UpdateDriftResourceSummaryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftResourceSummary(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftResourceSummaryResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal(50, command.ConflictCount);
        Assert.True(command.IsConflict);
        Assert.Equal(1000, command.TotalCount);

        // Verify count consistency
        Assert.Equal(command.TotalCount, command.ConflictCount + command.NonConflictCount);
    }

    [Fact]
    public async Task DeleteDriftResourceSummary_WithCascadingDeletion_ReturnsSuccessResponse()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftResourceSummaryFixture.DeleteDriftResourceSummaryResponse;
        expectedResponse.Message = "DriftResourceSummary and all related monitoring data deleted successfully";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDriftResourceSummaryCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftResourceSummary(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftResourceSummaryResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("related monitoring data", returnedResponse.Message);
        Assert.False(returnedResponse.IsActive);
    }

    [Fact]
    public async Task GetDriftResourceSummarys_WithHighVolumeData_ReturnsOptimizedResults()
    {
        // Arrange
        var expectedList = _driftResourceSummaryFixture.DriftResourceSummaryListVm;
        // Simulate high volume data
        for (int i = 0; i < expectedList.Count; i++)
        {
            expectedList[i].TotalCount = 10000 + (i * 1000);
            expectedList[i].ConflictCount = expectedList[i].TotalCount / 10; // 10% conflicts
            expectedList[i].NonConflictCount = expectedList[i].TotalCount - expectedList[i].ConflictCount;
            expectedList[i].EntityName = $"High Volume Enterprise System {i + 1}";
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftResourceSummaryListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDriftResourceSummarys();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftResourceSummaryListVm>>(okResult.Value);
        Assert.NotNull(returnedList);
        Assert.All(returnedList, item => Assert.True(item.TotalCount >= 10000));
        Assert.All(returnedList, item => Assert.Contains("High Volume", item.EntityName));
        Assert.All(returnedList, item => Assert.True(item.ConflictCount <= item.TotalCount / 10 + 1)); // Allow for rounding
    }

    [Fact]
    public async Task CreateDriftResourceSummary_WithComplexInfraObjectId_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftResourceSummaryFixture.CreateDriftResourceSummaryCommand;
        command.InfraObjectId = Guid.NewGuid().ToString();
        command.EntityName = "Complex Enterprise Infrastructure Object";
        command.TotalCount = 750;
        command.ConflictCount = 75;
        command.NonConflictCount = 675;
        var expectedResponse = _driftResourceSummaryFixture.CreateDriftResourceSummaryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftResourceSummary(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftResourceSummaryResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.True(Guid.TryParse(command.InfraObjectId, out _));
        Assert.Equal("Complex Enterprise Infrastructure Object", command.EntityName);
        Assert.Equal(750, command.TotalCount);
    }

    [Fact]
    public async Task GetDriftResourceSummarys_WithFilteringByConflictStatus_ReturnsFilteredResults()
    {
        // Arrange
        var expectedList = _driftResourceSummaryFixture.DriftResourceSummaryListVm;
        expectedList.ForEach(x =>
        {
            x.IsConflict = true;
            x.ConflictCount = 25;
            x.EntityName = "Conflicted Enterprise Resource";
        });

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftResourceSummaryListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDriftResourceSummarys();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftResourceSummaryListVm>>(okResult.Value);
        Assert.All(returnedList, item => Assert.True(item.IsConflict));
        Assert.All(returnedList, item => Assert.True(item.ConflictCount > 0));
        Assert.All(returnedList, item => Assert.Contains("Conflicted", item.EntityName));
    }

    [Fact]
    public async Task UpdateDriftResourceSummary_WithPerformanceOptimization_ReturnsOkResult()
    {
        // Arrange
        var command = _driftResourceSummaryFixture.UpdateDriftResourceSummaryCommand;
        command.EntityName = "Performance Optimized Enterprise Resource";
        command.TotalCount = 5000;
        command.ConflictCount = 100;
        command.NonConflictCount = 4900;
        command.IsConflict = true;
        var expectedResponse = _driftResourceSummaryFixture.UpdateDriftResourceSummaryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftResourceSummary(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftResourceSummaryResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("Performance Optimized", command.EntityName);

        // Verify low conflict ratio (2%)
        var conflictRatio = (double)command.ConflictCount / command.TotalCount;
        Assert.True(conflictRatio <= 0.02);
    }

    [Fact]
    public async Task GetDriftResourceSummaryById_WithNonExistentId_ThrowsException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftResourceSummaryDetailQuery>(q => q.Id == nonExistentId), default))
            .ThrowsAsync(new Exception("DriftResourceSummary not found"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _controller.GetDriftResourceSummaryById(nonExistentId));
    }

    [Fact]
    public async Task CreateDriftResourceSummary_WithBusinessCriticalEntity_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftResourceSummaryFixture.CreateDriftResourceSummaryCommand;
        command.EntityName = "Business Critical Enterprise Database Cluster";
        command.TotalCount = 2500;
        command.ConflictCount = 25; // 1% conflict rate for critical systems
        command.NonConflictCount = 2475;
        command.IsConflict = true;
        var expectedResponse = _driftResourceSummaryFixture.CreateDriftResourceSummaryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftResourceSummary(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftResourceSummaryResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("Business Critical", command.EntityName);

        // Verify very low conflict rate for critical systems
        var conflictRate = (double)command.ConflictCount / command.TotalCount * 100;
        Assert.True(conflictRate <= 1.0);
    }

    #endregion
}
