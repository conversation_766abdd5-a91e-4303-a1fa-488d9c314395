﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ContinuityPatrol.Application.Contracts.Persistence
{
    public interface IDataSyncMonitorLogsRepository : IRepository<DataSyncMonitorLog>
    {
        Task<List<DataSyncMonitorLog>> GetByInfraObjectId(string infraObjectId, string startDate, string endDate);
    }
}
