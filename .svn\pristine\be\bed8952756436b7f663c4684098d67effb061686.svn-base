using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MssqlNativeLogShippingMonitorLogFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "MssqlNativeLogShippingMonitor";

    public List<MssqlNativeLogShippingMonitorLog> MssqlNativeLogShippingMonitorLogPaginationList { get; set; }
    public List<MssqlNativeLogShippingMonitorLog> MssqlNativeLogShippingMonitorLogList { get; set; }
    public MssqlNativeLogShippingMonitorLog MssqlNativeLogShippingMonitorLogDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public MssqlNativeLogShippingMonitorLogFixture()
    {
        _fixture = new Fixture();
        
        // Configure AutoFixture to generate valid data
        _fixture.Customize<MssqlNativeLogShippingMonitorLog>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
          );

        MssqlNativeLogShippingMonitorLogPaginationList = _fixture.CreateMany<MssqlNativeLogShippingMonitorLog>(20).ToList();
        MssqlNativeLogShippingMonitorLogList = _fixture.CreateMany<MssqlNativeLogShippingMonitorLog>(5).ToList();
        MssqlNativeLogShippingMonitorLogDto = _fixture.Create<MssqlNativeLogShippingMonitorLog>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public MssqlNativeLogShippingMonitorLog CreateMssqlNativeLogShippingMonitorLogWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<MssqlNativeLogShippingMonitorLog>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public MssqlNativeLogShippingMonitorLog CreateMssqlNativeLogShippingMonitorLogWithWhitespace()
    {
        return CreateMssqlNativeLogShippingMonitorLogWithProperties(type: "  MssqlNativeLogShippingMonitor  ");
    }

    public MssqlNativeLogShippingMonitorLog CreateMssqlNativeLogShippingMonitorLogWithLongType(int length)
    {
        var longType = new string('A', length);
        return CreateMssqlNativeLogShippingMonitorLogWithProperties(type: longType);
    }

    public MssqlNativeLogShippingMonitorLog CreateMssqlNativeLogShippingMonitorLogWithSpecificInfraObjectId(string infraObjectId)
    {
        return CreateMssqlNativeLogShippingMonitorLogWithProperties(infraObjectId: infraObjectId);
    }

    public List<MssqlNativeLogShippingMonitorLog> CreateMultipleMssqlNativeLogShippingMonitorLogWithSameType(string type, int count)
    {
        var logs = new List<MssqlNativeLogShippingMonitorLog>();
        for (int i = 0; i < count; i++)
        {
            logs.Add(CreateMssqlNativeLogShippingMonitorLogWithProperties(type: type, isActive: true));
        }
        return logs;
    }

    public List<MssqlNativeLogShippingMonitorLog> CreateMssqlNativeLogShippingMonitorLogWithMixedActiveStatus(string type)
    {
        return new List<MssqlNativeLogShippingMonitorLog>
        {
            CreateMssqlNativeLogShippingMonitorLogWithProperties(type: type, isActive: true),
            CreateMssqlNativeLogShippingMonitorLogWithProperties(type: type, isActive: false),
            CreateMssqlNativeLogShippingMonitorLogWithProperties(type: type, isActive: true)
        };
    }

    public List<MssqlNativeLogShippingMonitorLog> CreateMssqlNativeLogShippingMonitorLogWithDateRange(string infraObjectId, DateTime startDate, DateTime endDate, int count)
    {
        var logs = new List<MssqlNativeLogShippingMonitorLog>();
        var dateRange = (endDate - startDate).TotalDays;
        
        for (int i = 0; i < count; i++)
        {
            var randomDate = startDate.AddDays(Random.Shared.NextDouble() * dateRange);
            logs.Add(CreateMssqlNativeLogShippingMonitorLogWithProperties(
                infraObjectId: infraObjectId,
                isActive: true,
                createdDate: randomDate));
        }
        return logs;
    }

    public List<MssqlNativeLogShippingMonitorLog> CreateMssqlNativeLogShippingMonitorLogOutsideDateRange(string infraObjectId, DateTime startDate, DateTime endDate)
    {
        return new List<MssqlNativeLogShippingMonitorLog>
        {
            CreateMssqlNativeLogShippingMonitorLogWithProperties(
                infraObjectId: infraObjectId,
                isActive: true,
                createdDate: startDate.AddDays(-5)), // Before range
            CreateMssqlNativeLogShippingMonitorLogWithProperties(
                infraObjectId: infraObjectId,
                isActive: true,
                createdDate: endDate.AddDays(5)) // After range
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonTypes = { "MssqlNativeLogShippingMonitor", "LogShipping", "MSSQL", "NativeLogShipping" };
        public static readonly string[] CommonInfraObjectIds = { "INFRA_001", "INFRA_002", "INFRA_003" };
        public static readonly string[] CommonWorkflowIds = { "WF_001", "WF_002", "WF_003" };
        public static readonly string[] SpecialCharacterTypes = { "Type@#$%", "Type with spaces", "Type_with_underscores", "Type-with-dashes" };
    }
}
