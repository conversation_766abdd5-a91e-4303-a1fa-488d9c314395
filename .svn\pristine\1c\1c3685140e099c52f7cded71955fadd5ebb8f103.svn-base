﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@using ContinuityPatrol.Domain.ViewModels.AdPasswordJobModel;
@using ContinuityPatrol.Shared.Services.Helper
@model ContinuityPatrol.Domain.ViewModels.AdPasswordJobModel.AdPasswordJobViewModel
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/switch_slide_toggle.css" rel="stylesheet" />
<div class="page-content">
    <div class="card Card_Design_None">

        <div class="">
            <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                <div class="d-flex align-items-center">
                    <h6 class="page_title" title="AD Password Expire">
                        <i class="cp-BIA-cost"></i>
                        <span>AD Password Expire</span>
                    </h6>
                    <ul class="ms-2 nav nav-pills" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active adConfigureTabs" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">User List</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link adConfigureTabs" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">Schedule List</button>
                        </li>
                    </ul>
                </div>
                <form class="d-flex align-items-center">
                    <div class="input-group me-2 w-auto">
                        <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                        <div class="input-group-text">
                            <div class="dropdown">
                                <span data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="cp-filter"></i>
                                </span>
                                <ul class="dropdown-menu filter-dropdown">
                                    <li>
                                        <h6 class="dropdown-header">Filter Search</h6>
                                    </li>
                                    <li class="dropdown-item">
                                        <div>
                                            <input class="form-check-input" type="checkbox" value="" id="Name">
                                            <label class="form-check-label" for="Name">
                                                Name
                                            </label>
                                        </div>
                                    </li>

                                </ul>
                            </div>
                        </div>
                    </div>

                    <button type="button" class="btn btn-primary btn-sm me-1 d-none" data-bs-toggle="modal" id='shedulerCreate' title="schedule" data-bs-target="#scheduleModal"><i class="cp-time"></i></button>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" id="createADPasswordExpiry" data-bs-target="#CreateModal"><i class="cp-add me-1"></i>Create</button>
                </form>
            </div>
        </div>
        <div class="card-body pt-0">
            <div class="tab-content" id="pills-tabContent">
                <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab" tabindex="0">
                    <table class="table table-hover dataTable" id="tblADExpiryUser" style="width:100%">
                        <thead>
                            <tr>
                                <th>Sr.No</th>
                                <th>Username</th>
                                <th>Domain Server</th>
                                <th>Server Info</th>
                                <th>E-Mail</th>
                                <th>Notification Days</th>
                                <th class="Action-th">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                <div class="tab-pane fades" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab" tabindex="0">
                    <table id="AdPassword" class="table table-hover dataTable" style="width:100%">
                        <thead>
                            <tr>
                                <th>Sr.No</th>
                                <th>Server Name</th>
                                <th>Schedule Time</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!--Modal Config ADPasswordExpire-->
    <div class="modal fade" id="CreateModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <partial name="ConfigurationUser" />
    </div>

    <!--Modal Delete ADPasswordExpire-->
    <div class="modal fade" data-bs-backdrop="static" id="DeleteADPasswordModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <partial name="Delete" />
    </div>

    <div class="modal fade" id="scheduleModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <form class="modal-content" id="CreateAdPassword" asp-area="Manage" autocomplete="off" asp-controller="ADPasswordExpire" asp-action="AdJobCreateOrAdJobUpdate" method="post" enctype="multipart/form-data">
                <div class="modal-header">
                    <h6 class="page_title" title="Perform Quantitative Financial Impact"><i class="cp-BIA-cost"></i><span> AD Password Expire Configuration</span></h6>
                    <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="min-height:25rem ">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <div class="form-label" title=" Domain Server"> Domain Server</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-site-names"></i></span>
                                    <select class="form-select-modal mb-0" asp-for="DomainServerId" id="DomainServer" data-placeholder="Select  Domain Server">
                                    </select>
                                </div>
                                <input asp-for="Id" id="Id" type="hidden" class="form-control" />
                                <input asp-for="State" id="StateVal" type="hidden" class="form-control" />
                                <input asp-for="DomainServer" id="domainId" type="hidden" class="form-control" />
                                <input asp-for="CronExpression" id="textCronExpression" type="hidden" class="form-control" />
                                <input asp-for="ScheduleType" id="textScheduleType" type="hidden" class="form-control" />
                                <input asp-for="ScheduleTime" id="txtCronViewList" type="hidden" class="form-control" />
                                <span asp-validation-for="DomainServer" id="domainName-error"></span>
                            </div>
                        </div>

                        <div class="col-12">

                            <div class="row mt-2 w-100 year">
                                <div>
                                    <div class="mb-3">
                                        <div class="form-label">Scheduler</div>
                                        <div>
                                            <nav class="mb-3">
                                                <div class="nav nav-tabs navChange" id="nav-tab" role="tablist">

                                                    <button class="nav-link active " id="nav-Minutes-tab" data-bs-toggle="tab"
                                                            data-bs-target="#nav-Minutes" type="button" role="tab" name="minutes"
                                                            aria-controls="nav-Minutes" aria-selected="true">
                                                        Minutes
                                                    </button>
                                                    <button class="nav-link" id="nav-Hourly-tab" data-bs-toggle="tab"
                                                            data-bs-target="#nav-Hourly" type="button" role="tab" name="hourly"
                                                            aria-controls="nav-Hourly" aria-selected="false">
                                                        Hourly
                                                    </button>
                                                    <button class="nav-link" id="nav-Daily-tab" data-bs-toggle="tab"
                                                            data-bs-target="#nav-Daily" type="button" role="tab" name="daily"
                                                            aria-controls="nav-Daily" aria-selected="false">
                                                        Daily
                                                    </button>
                                                    <button class="nav-link" id="nav-Weekly-tab" data-bs-toggle="tab"
                                                            data-bs-target="#nav-Weekly" type="button" role="tab" name="weekly"
                                                            aria-controls="nav-Weekly" aria-selected="false">
                                                        Weekly
                                                    </button>
                                                    <button class="nav-link" id="nav-Monthly-tab" data-bs-toggle="tab"
                                                            data-bs-target="#nav-Monthly" type="button" role="tab" name="monthly"
                                                            aria-controls="nav-Monthly" aria-selected="false">
                                                        Monthly
                                                    </button>
                                                </div>
                                            </nav>
                                            <div class="tab-content" id="nav-tabContent">

                                                <div class="tab-pane fade active show" id="nav-Minutes" role="tabpanel" aria-labelledby="nav-home-tab" tabindex="0">
                                                    <div class="row">

                                                        <div class="col-6">
                                                            <div class="form-group">
                                                                <div class="form-label">Every</div>
                                                                <div class="form-label">Minutes</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text" id="EveryMint">
                                                                        <i class="cp-apply-finish-time"></i>
                                                                    </span>
                                                                    @Html.TextBox("txtMins", null, new { id = "txtMins", type = "number", min = "1", max = "60", @class = "form-control", @placeholder = "Minute(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                    <span class="input-group-text form-label mb-0 text-secondary">
                                                                        Mins
                                                                    </span>
                                                                </div>
                                                                <span asp-validation-for="ScheduleTime" id="CronMin-error"></span>
                                                            </div>

                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tab-pane fade" id="nav-Hourly" role="tabpanel" aria-labelledby="nav-Hourly-tab" tabindex="0">
                                                    <div class="row">
                                                        <div class="form-label">Every</div>
                                                        <div class="col-xl-6">
                                                            <div class="form-group">
                                                                <div class="form-label">Hours</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text" id="Everyhour">
                                                                        <i class="cp-apply-finish-time"></i>
                                                                    </span>
                                                                    @Html.TextBox("txtHours", null, new { id = "txtHours", type = "number", min = "1", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                    <span class="input-group-text form-label mb-0 text-secondary">Hours</span>
                                                                </div>
                                                                <span asp-validation-for="ScheduleTime" id="CronHourly-error"></span>
                                                            </div>
                                                        </div>
                                                        <div class="col-xl-6">
                                                            <div class="mb-3 form-group">
                                                                <div class="form-label">Minutes</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text" id="EveryhourMint">
                                                                        <i class="cp-apply-finish-time"></i>
                                                                    </span>
                                                                    @Html.TextBox("txtMinutes", null, new { id = "txtMinutes", type = "number", min = "1", max = "59", @class = "form-control", @placeholder = "Min(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                    <span class="input-group-text form-label mb-0 text-secondary">Mins</span>
                                                                </div>
                                                                <span asp-validation-for="ScheduleTime" id="CronHourMin-error"></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tab-pane fade" id="nav-Daily" role="tabpanel" aria-labelledby="nav-Daily-tab" tabindex="0">
                                                    <div class="row align-items-center">
                                                        <div class="col">
                                                            <div class="form-group flex-fill ">
                                                                <label class="animation-label form-label">
                                                                    Select Day
                                                                    Type
                                                                </label>
                                                                <div class="" style="margin-top: 8px;">
                                                                    <div class="form-check form-check-inline">
                                                                        <input name="daysevery1" aria-label="Every Day" type="radio" id="defaultCheck-everyday" class="form-check-input custom-cursor-default-hover" value="everyday" cursorshover="true">
                                                                        <label for="defaultCheck-everyday" class="form-check-label custom-cursor-default-hover" cursorshover="true">Every Day</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline">
                                                                        <input name="daysevery1" aria-label="Every Week Day" type="radio" id="defaultCheck-MON-FRI" class="form-check-input custom-cursor-default-hover" value="Mon-Fri">
                                                                        <label for="defaultCheck-MON-FRI" class="form-check-label custom-cursor-default-hover" cursorshover="true">Every Week Day</label>
                                                                    </div>
                                                                </div>
                                                                <span asp-validation-for="ScheduleTime" id="Crondaysevery-error"></span>
                                                            </div>
                                                        </div>
                                                        <div class="col">
                                                            <div class="form-group">
                                                                <div class="form-label">Hours</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text" id="DayHour">
                                                                        <i class="cp-apply-finish-time"></i>
                                                                    </span>
                                                                    @Html.TextBox("everyHours", null, new { id = "everyHours", type = "number", min = "1", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                </div>
                                                                <span asp-validation-for="ScheduleTime" id="CroneveryHour-error"></span>
                                                            </div>
                                                        </div>
                                                        <div class="col">
                                                            <div class="form-group">
                                                                <div class="form-label">Minutes</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text" id="DayMint">
                                                                        <i class="cp-apply-finish-time"></i>
                                                                    </span>
                                                                    @Html.TextBox("everyMinutes", null, new { id = "everyMinutes", type = "number", min = "1", max = "59", @class = "form-control", @placeholder = "Min(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                </div>
                                                                <span asp-validation-for="ScheduleTime" id="CroneveryMin-error"></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tab-pane fade" id="nav-Weekly" role="tabpanel" aria-labelledby="nav-Weekly-tab" tabindex="0">
                                                    <div class="form-group">
                                                        <label class="form-label custom-cursor-default-hover">Select Days</label>
                                                        <div class="bg-transparent input-group">
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Monday" type="checkbox" id="defaultCheck-1" class="form-check-input defaultCheck" value="Mon"><label for="defaultCheck-1" class="form-check-label custom-cursor-default-hover">Monday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Tuesday" type="checkbox" id="defaultCheck-2" class="form-check-input defaultCheck" value="Tue"><label for="defaultCheck-2" class="form-check-label">Tuesday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Wednesday" type="checkbox" id="defaultCheck-3" class="form-check-input defaultCheck" value="Wed"><label for="defaultCheck-3" class="form-check-label" cursorshover="true">Wednesday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Thursday" type="checkbox" id="defaultCheck-4" class="form-check-input defaultCheck" value="Thu"><label for="defaultCheck-4" class="form-check-label custom-cursor-default-hover" cursorshover="true">Thursday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Friday" type="checkbox" id="defaultCheck-5" class="form-check-input defaultCheck" value="Fri" cursorshover="true"><label for="defaultCheck-5" class="form-check-label">Friday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Saturday" type="checkbox" id="defaultCheck-6" class="form-check-input defaultCheck" value="Sat"><label for="defaultCheck-6" class="form-check-label">Saturday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Sunday" type="checkbox" id="defaultCheck-0" class="form-check-input defaultCheck" value="Sun"><label for="defaultCheck-0" class="form-check-label">Sunday</label>
                                                            </div>
                                                        </div>
                                                        <span asp-validation-for="ScheduleTime" id="CronDay-error"></span>
                                                    </div>
                                                    <div class="row row-cols-2 mt-2">
                                                        <div class="col">
                                                            <div class="form-group">
                                                                <div class="form-label">Starts at</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text"><i class="cp-apply-finish-time"></i></span>
                                                                    <input autocomplete="off" class="form-control" id="ddlHours" max="23" min="1" name="ddlHours" ondragstart="return false" ondrop="return false" onkeydown="return true" onpaste="return false" placeholder="Hour(s)" type="time" cursorshover="true">
                                                                </div>
                                                                <span asp-validation-for="ScheduleTime" id="CronddlHour-error"></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tab-pane fade" id="nav-Monthly" role="tabpanel" aria-labelledby="nav-Monthly-tab" tabindex="0">
                                                    <div class="row row-cols-2 mt-2">
                                                        <div class="col">
                                                            <div class="mb-3 form-group">
                                                                <div class="form-label">Select Month and year</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text">
                                                                        <i class="cp-calendar"></i>
                                                                    </span>
                                                                    <input name="month" autocomplete="off" type="month" id="lblMonth" class="form-control custom-cursor-default-hover" cursorshover="true" min="2024-02" max="2101-02">
                                                                </div>
                                                                <span id="CronMonth-error"></span>
                                                            </div>
                                                        </div>
                                                        <div class="col-12">
                                                            <div class="mb-1 form-group text-justify " style="display: inline-table;">
                                                                <div class="form-label">Days</div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox1" value="1">
                                                                    <label class="form-check-label" for="inlineCheckbox1">1</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input  " type="checkbox" id="inlineCheckbox2" value="2">
                                                                    <label class="form-check-label" for="inlineCheckbox2">2</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox3" value="3">
                                                                    <label class="form-check-label" for="inlineCheckbox3">3</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox4" value="4">
                                                                    <label class="form-check-label" for="inlineCheckbox4">4</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox5" value="5">
                                                                    <label class="form-check-label" for="inlineCheckbox5">5</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox6" value="6">
                                                                    <label class="form-check-label" for="inlineCheckbox6">6</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox7" value="7">
                                                                    <label class="form-check-label" for="inlineCheckbox7">7</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox8" value="8">
                                                                    <label class="form-check-label" for="inlineCheckbox8">8</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox9" value="9">
                                                                    <label class="form-check-label" for="inlineCheckbox9">9</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox10" value="10">
                                                                    <label class="form-check-label" for="inlineCheckbox10">10</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox11" value="11">
                                                                    <label class="form-check-label" for="inlineCheckbox11">11</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox12" value="12">
                                                                    <label class="form-check-label" for="inlineCheckbox12">12</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox13" value="13">
                                                                    <label class="form-check-label" for="inlineCheckbox13">13</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox14" value="14">
                                                                    <label class="form-check-label" for="inlineCheckbox14">14</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input" type="checkbox" id="inlineCheckbox15" value="15">
                                                                    <label class="form-check-label" for="inlineCheckbox15">15</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox16" value="16">
                                                                    <label class="form-check-label" for="inlineCheckbox16">16</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox17" value="17">
                                                                    <label class="form-check-label" for="inlineCheckbox17">17</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox18" value="18">
                                                                    <label class="form-check-label" for="inlineCheckbox18">18</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox19" value="19">
                                                                    <label class="form-check-label" for="inlineCheckbox19">19</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox20" value="20">
                                                                    <label class="form-check-label" for="inlineCheckbox20">20</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox21" value="21">
                                                                    <label class="form-check-label" for="inlineCheckbox21">21</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input" type="checkbox" id="inlineCheckbox22" value="22">
                                                                    <label class="form-check-label" for="inlineCheckbox22">22</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox23" value="23">
                                                                    <label class="form-check-label" for="inlineCheckbox23">23</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox24" value="24">
                                                                    <label class="form-check-label" for="inlineCheckbox24">24</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox25" value="25">
                                                                    <label class="form-check-label" for="inlineCheckbox25">25</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox26" value="26">
                                                                    <label class="form-check-label" for="inlineCheckbox26">26</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox27" value="27">
                                                                    <label class="form-check-label" for="inlineCheckbox27">27</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox28" value="28">
                                                                    <label class="form-check-label" for="inlineCheckbox28">28</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox29" value="29">
                                                                    <label class="form-check-label" for="inlineCheckbox29">29</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox30" value="30">
                                                                    <label class="form-check-label" for="inlineCheckbox30">30</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Days" class="form-check-input monthday" type="checkbox" id="inlineCheckbox31" value="31">
                                                                    <label class="form-check-label" for="inlineCheckbox31">31</label>
                                                                </div>
                                                                <div class="form-group">
                                                                    <div class="form-group"> <span asp-validation-for="ScheduleTime" id="CronMonthlyDay-error"></span></div>
                                                                </div>
                                                            </div>

                                                        </div>
                                                        <div class="col">
                                                            <div class="mb-3 form-group">
                                                                <div class="form-label">Hours</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text">
                                                                        <i class="cp-apply-finish-time"></i>
                                                                    </span>
                                                                    @Html.TextBox("txtHourss", null, new { id = "txtHourss", type = "number", min = "1", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                    <span class="input-group-text form-label mb-0 text-secondary">Hours</span>
                                                                </div>
                                                                <span asp-validation-for="ScheduleTime" id="CronMonthHrs-error"></span>
                                                            </div>
                                                        </div>
                                                        <div class="col">
                                                            <div class="mb-3 form-group">
                                                                <div class="form-label">Minutes</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text">
                                                                        <i class="cp-apply-finish-time"></i>
                                                                    </span>
                                                                    @Html.TextBox("txtMinss", null, new { id = "txtMinss", type = "number", min = "1", max = "59", @class = "form-control", @placeholder = "Min(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                    <span class="input-group-text form-label mb-0 text-secondary">Mins</span>
                                                                </div>
                                                                <span asp-validation-for="ScheduleTime" id="CronMonthMins-error"></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label class="form-label custom-cursor-default-hover" cursorshover="true">State</label>
                                <div class="row row-cols-4">
                                    <div class="col-auto col">
                                        <div class="form-check">
                                            <input name="state" type="radio" id="textStateActive" class="form-check-input" value="Active"><label for="textStateActive" class="form-check-label" cursorshover="true">Active</label>
                                        </div>
                                    </div>
                                    <div class="col-auto col">
                                        <div class="form-check">
                                            <input name="state" type="radio" id="textStateInactive" class="form-check-input" value="Inactive" cursorshover="true"><label for="textStateInactive" class="form-check-label" cursorshover="true">Inactive</label>
                                        </div>
                                    </div>
                                </div>
                                <span id="state-error" class="field-validation-valid" data-valmsg-for="State" data-valmsg-replace="true"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" title="Cancel" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" id="btnSave" title="Save">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

</div>

<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="AdDelete" />
</div>
<div id="AdpasswordCreate" data-create-permission="@WebHelper.CurrentSession.Permissions.Manage.CreateAndEdit" aria-hidden="true"></div>
<div id="AdpasswordDelete" data-delete-permission="@WebHelper.CurrentSession.Permissions.Manage.Delete" aria-hidden="true"></div>


@* <script src="~/js/slide_toggle.js"></script> *@
<script src="~/js/aduserlist.js"></script>
<script src="~/js/AdPassword.js"></script>