using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ComponentSaveAllFixture : IDisposable
{

    public List<ComponentSaveAll> ComponentSaveAllPaginationList { get; set; }
    public List<ComponentSaveAll> ComponentSaveAllList { get; set; }
    public ComponentSaveAll ComponentSaveAllDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public ComponentSaveAllFixture()
    {
        var fixture = new Fixture();

        ComponentSaveAllList = fixture.Create<List<ComponentSaveAll>>();

        ComponentSaveAllPaginationList = fixture.CreateMany<ComponentSaveAll>(20).ToList();
        
        ComponentSaveAllDto = fixture.Create<ComponentSaveAll>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
