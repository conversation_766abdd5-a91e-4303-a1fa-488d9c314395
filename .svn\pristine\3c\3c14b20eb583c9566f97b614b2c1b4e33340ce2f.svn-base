﻿using ContinuityPatrol.Application.Features.WorkflowHistory.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowHistory.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetNames;
using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetWorkflowHistoryByWorkflowId;
using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetWorkflowHistoryByWorkflowIdAndVersion;
using ContinuityPatrol.Domain.ViewModels.WorkflowHistoryModel;

namespace ContinuityPatrol.Application.Mappings;

public class WorkflowHistoryProfile : Profile
{
    public WorkflowHistoryProfile()
    {
        CreateMap<WorkflowHistory, CreateWorkflowHistoryCommand>().ReverseMap();
        CreateMap<UpdateWorkflowHistoryCommand, WorkflowHistory>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<WorkflowHistory, WorkflowHistoryListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowHistory, WorkflowHistoryDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowHistory, WorkflowHistoryNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowHistory, WorkflowHistoryByWorkflowIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowHistory, WorkflowHistoryByWorkflowIdAndVersionVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}