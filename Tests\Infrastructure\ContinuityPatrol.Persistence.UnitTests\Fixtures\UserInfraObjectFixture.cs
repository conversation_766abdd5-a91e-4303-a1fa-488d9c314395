using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class UserInfraObjectFixture : IDisposable
{
    public List<UserInfraObject> UserInfraObjectPaginationList { get; set; }
    public List<UserInfraObject> UserInfraObjectList { get; set; }
    public UserInfraObject UserInfraObjectDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public UserInfraObjectFixture()
    {
        var fixture = new Fixture();

        UserInfraObjectList = fixture.Create<List<UserInfraObject>>();

        UserInfraObjectPaginationList = fixture.CreateMany<UserInfraObject>(20).ToList();

        UserInfraObjectDto = fixture.Create<UserInfraObject>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
