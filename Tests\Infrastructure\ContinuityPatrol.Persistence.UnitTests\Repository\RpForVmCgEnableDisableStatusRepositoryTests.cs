using ContinuityPatrol.Application.Features.CGExecutionReport.Queries.GetCgExecutionReportPaginatedList;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class RpForVmCgEnableDisableStatusRepositoryTests : IClassFixture<RpForVmCgEnableDisableStatusFixture>
{
    private readonly RpForVmCgEnableDisableStatusFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly RpForVmCgEnableDisableStatusRepository _repository;

    public RpForVmCgEnableDisableStatusRepositoryTests(RpForVmCgEnableDisableStatusFixture fixture)
    {
        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new RpForVmCgEnableDisableStatusRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region GetLastRpForVmCgEnableDisableS Tests

    [Fact]
    public async Task GetLastRpForVmCgEnableDisableS_ShouldReturnLatestRecord_WhenRecordsExist()
    {
        // Arrange
        var status1 = _fixture.RpForVmCgEnableDisableStatusDto;
        status1.Id = 1;
        status1.CreatedDate = DateTime.UtcNow.AddDays(-2);

        var status2 = _fixture.CreateRpForVmCgEnableDisableStatusWithWorkflowOperationId("WORKFLOW_2");
        status2.Id = 2;
        status2.CreatedDate = DateTime.UtcNow.AddDays(-1);

        var status3 = _fixture.CreateRpForVmCgEnableDisableStatusWithWorkflowOperationId("WORKFLOW_3");
        status3.Id = 3;
        status3.CreatedDate = DateTime.UtcNow;

        _dbContext.RPForVMCGEnableDisableStatuses.AddRange(status1, status2, status3);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetLastRpForVmCgEnableDisableS();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Id); // Should return the record with highest Id
    }

    [Fact]
    public async Task GetLastRpForVmCgEnableDisableS_ShouldReturnNull_WhenNoRecordsExist()
    {
        // Act
        var result = await _repository.GetLastRpForVmCgEnableDisableS();

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region CalculateCompletionPercentageAsync Tests

    [Fact]
    public async Task CalculateCompletionPercentageAsync_ShouldReturnCorrectPercentage_WhenRecordsExist()
    {
        // Arrange
        var workflowOperationId = "WORKFLOW_OP_TEST";
        var statuses = _fixture.CreateMultipleRpForVmCgEnableDisableStatusWithSameWorkflowOperationId(
            workflowOperationId, 3, 5); // 3 completed out of 5 total = 60%

        _dbContext.RPForVMCGEnableDisableStatuses.AddRange(statuses);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.CalculateCompletionPercentageAsync(workflowOperationId);

        // Assert
        Assert.Equal(60.0, result);
    }

    [Fact]
    public async Task CalculateCompletionPercentageAsync_ShouldReturn100Percent_WhenAllCompleted()
    {
        // Arrange
        var workflowOperationId = "WORKFLOW_OP_ALL_COMPLETED";
        var statuses = _fixture.CreateMultipleRpForVmCgEnableDisableStatusWithSameWorkflowOperationId(
            workflowOperationId, 4, 4); // 4 completed out of 4 total = 100%

        _dbContext.RPForVMCGEnableDisableStatuses.AddRange(statuses);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.CalculateCompletionPercentageAsync(workflowOperationId);

        // Assert
        Assert.Equal(100.0, result);
    }

    [Fact]
    public async Task CalculateCompletionPercentageAsync_ShouldReturn0Percent_WhenNoneCompleted()
    {
        // Arrange
        var workflowOperationId = "WORKFLOW_OP_NONE_COMPLETED";
        var statuses = _fixture.CreateMultipleRpForVmCgEnableDisableStatusWithSameWorkflowOperationId(
            workflowOperationId, 0, 3); // 0 completed out of 3 total = 0%

        _dbContext.RPForVMCGEnableDisableStatuses.AddRange(statuses);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.CalculateCompletionPercentageAsync(workflowOperationId);

        // Assert
        Assert.Equal(0.0, result);
    }

    [Fact]
    public async Task CalculateCompletionPercentageAsync_ShouldReturn0_WhenNoRecordsExist()
    {
        // Act
        var result = await _repository.CalculateCompletionPercentageAsync("NON_EXISTENT_WORKFLOW");

        // Assert
        Assert.Equal(0.0, result);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task CalculateCompletionPercentageAsync_ShouldReturn0_WhenWorkflowOperationIdIsNullOrEmpty(string workflowOperationId)
    {
        // Act
        var result = await _repository.CalculateCompletionPercentageAsync(workflowOperationId);

        // Assert
        Assert.Equal(0.0, result);
    }

    #endregion

    #region GetCGExecutionByWorkflowOperationId Tests

    [Fact]
    public async Task GetCGExecutionByWorkflowOperationId_ShouldReturnMatchingRecords_WhenRecordsExist()
    {
        // Arrange
        var workflowOperationId = "WORKFLOW_OP_TEST";
        var matchingStatuses = new List<RpForVmCgEnableDisableStatus>
        {
            _fixture.CreateRpForVmCgEnableDisableStatusWithWorkflowOperationId(workflowOperationId),
            _fixture.CreateRpForVmCgEnableDisableStatusWithWorkflowOperationId(workflowOperationId)
        };
        var nonMatchingStatus = _fixture.CreateRpForVmCgEnableDisableStatusWithWorkflowOperationId("DIFFERENT_WORKFLOW");

        _dbContext.RPForVMCGEnableDisableStatuses.AddRange(matchingStatuses);
        _dbContext.RPForVMCGEnableDisableStatuses.Add(nonMatchingStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetCGExecutionByWorkflowOperationId(workflowOperationId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, status => Assert.Equal(workflowOperationId, status.WorkflowOperationId));
    }

    [Fact]
    public async Task GetCGExecutionByWorkflowOperationId_ShouldReturnEmptyList_WhenNoMatchingRecords()
    {
        // Arrange
        var status = _fixture.CreateRpForVmCgEnableDisableStatusWithWorkflowOperationId("DIFFERENT_WORKFLOW");
        _dbContext.RPForVMCGEnableDisableStatuses.Add(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetCGExecutionByWorkflowOperationId("NON_EXISTENT_WORKFLOW");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetCGExecutionByWorkflowOperationId_ShouldReturnOnlyActiveRecords()
    {
        // Arrange
        var workflowOperationId = "WORKFLOW_OP_TEST";
        var activeStatus = _fixture.CreateRpForVmCgEnableDisableStatusWithWorkflowOperationId(workflowOperationId);
        activeStatus.IsActive = true;

        var inactiveStatus = _fixture.CreateRpForVmCgEnableDisableStatusWithWorkflowOperationId(workflowOperationId);
        inactiveStatus.IsActive = false;

        _dbContext.RPForVMCGEnableDisableStatuses.AddRange(activeStatus, inactiveStatus);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetCGExecutionByWorkflowOperationId(workflowOperationId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsActive);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task GetCGExecutionByWorkflowOperationId_ShouldReturnEmptyList_WhenWorkflowOperationIdIsNullOrEmpty(string workflowOperationId)
    {
        // Act
        var result = await _repository.GetCGExecutionByWorkflowOperationId(workflowOperationId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetCgExecutionPaginatedList Tests

    [Fact]
    public async Task GetCgExecutionPaginatedList_ShouldReturnPaginatedResult_WhenRecordsExist()
    {
        // Arrange
        var jobId1 = "JOB_001";
        var jobId2 = "JOB_002";
        var workflowOp1 = "WORKFLOW_OP_001";
        var workflowOp2 = "WORKFLOW_OP_002";

        var statuses = new List<RpForVmCgEnableDisableStatus>
        {
            _fixture.CreateRpForVmCgEnableDisableStatusWithJobId(jobId1, workflowOp1, "completed"),
            _fixture.CreateRpForVmCgEnableDisableStatusWithJobId(jobId1, workflowOp1, "completed"),
            _fixture.CreateRpForVmCgEnableDisableStatusWithJobId(jobId2, workflowOp2, "in-progress")
        };

        _dbContext.RPForVMCGEnableDisableStatuses.AddRange(statuses);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetCgExecutionPaginatedList(null, null, null, 1, 10);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.NotEmpty(result.Data);
        Assert.True(result.TotalCount > 0);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(10, result.PageSize);
    }

    [Fact]
    public async Task GetCgExecutionPaginatedList_ShouldFilterByType_WhenTypeProvided()
    {
        // Arrange
        var enableStatus = _fixture.CreateRpForVmCgEnableDisableStatusWithType("Enable");
        var disableStatus = _fixture.CreateRpForVmCgEnableDisableStatusWithType("Disable");

        _dbContext.RPForVMCGEnableDisableStatuses.AddRange(enableStatus, disableStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetCgExecutionPaginatedList("Enable", null, null, 1, 10);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        // The result should be filtered by type, but the exact count depends on the grouping logic
        Assert.True(result.TotalCount >= 0);
    }

    [Fact]
    public async Task GetCgExecutionPaginatedList_ShouldFilterByDateRange_WhenDatesProvided()
    {
        // Arrange
        var startDate = DateTime.UtcNow.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");

        var statusInRange = _fixture.CreateRpForVmCgEnableDisableStatusWithType("Enable",
            DateTime.UtcNow.AddDays(-3), DateTime.UtcNow.AddDays(-2));
        var statusOutOfRange = _fixture.CreateRpForVmCgEnableDisableStatusWithType("Enable",
            DateTime.UtcNow.AddDays(-10), DateTime.UtcNow.AddDays(-9));

        _dbContext.RPForVMCGEnableDisableStatuses.AddRange(statusInRange, statusOutOfRange);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetCgExecutionPaginatedList(null, startDate, endDate, 1, 10);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        // The result should be filtered by date range
        Assert.True(result.TotalCount >= 0);
    }

    [Fact]
    public async Task GetCgExecutionPaginatedList_ShouldHandlePagination_WhenMultiplePages()
    {
        // Arrange
        var statuses = new List<RpForVmCgEnableDisableStatus>();
        for (int i = 1; i <= 15; i++)
        {
            statuses.Add(_fixture.CreateRpForVmCgEnableDisableStatusWithJobId($"JOB_{i:D3}", $"WORKFLOW_OP_{i:D3}"));
        }

        _dbContext.RPForVMCGEnableDisableStatuses.AddRange(statuses);
        await _dbContext.SaveChangesAsync();

        // Act - Get first page with page size 5
        var result = await _repository.GetCgExecutionPaginatedList(null, null, null, 1, 5);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(5, result.PageSize);
        Assert.True(result.Data.Count <= 5);
    }

    [Fact]
    public async Task GetCgExecutionPaginatedList_ShouldReturnEmptyResult_WhenNoRecordsExist()
    {
        // Act
        var result = await _repository.GetCgExecutionPaginatedList(null, null, null, 1, 10);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Empty(result.Data);
        Assert.Equal(0, result.TotalCount);
    }

    [Fact]
    public async Task GetCgExecutionPaginatedList_ShouldHandleInvalidPageParameters()
    {
        // Arrange
        var status = _fixture.CreateRpForVmCgEnableDisableStatusWithJobId("JOB_001");
        _dbContext.RPForVMCGEnableDisableStatuses.Add(status);
        await _dbContext.SaveChangesAsync();

        // Act - Test with invalid page parameters
        var result = await _repository.GetCgExecutionPaginatedList(null, null, null, 0, 0);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(1, result.CurrentPage); // Should default to 1
        Assert.Equal(10, result.PageSize); // Should default to 10
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    public async Task GetCgExecutionPaginatedList_ShouldTreatEmptyTypeAsNull(string emptyType)
    {
        // Arrange
        var status = _fixture.CreateRpForVmCgEnableDisableStatusWithType("Enable");
        _dbContext.RPForVMCGEnableDisableStatuses.Add(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetCgExecutionPaginatedList(emptyType, null, null, 1, 10);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        // Should not filter by type when empty string is provided
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddRpForVmCgEnableDisableStatus_WhenValidEntity()
    {
        // Arrange
        var status = _fixture.RpForVmCgEnableDisableStatusDto;
        status.WorkflowOperationId = "TEST_WORKFLOW_OP";
        status.WorkflowName = "Test Workflow";
        status.CGName = "Test CG";
        status.Status = "Completed";
        status.Type = "Enable";

        // Act
        var result = await _repository.AddAsync(status);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(status.WorkflowOperationId, result.WorkflowOperationId);
        Assert.Equal(status.WorkflowName, result.WorkflowName);
        Assert.Equal(status.CGName, result.CGName);
        Assert.Equal(status.Status, result.Status);
        Assert.Equal(status.Type, result.Type);
        Assert.Single(_dbContext.RPForVMCGEnableDisableStatuses);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var status = _fixture.RpForVmCgEnableDisableStatusDto;
        _dbContext.RPForVMCGEnableDisableStatuses.Add(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(status.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(status.Id, result.Id);
        Assert.Equal(status.WorkflowOperationId, result.WorkflowOperationId);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity_WhenExists()
    {
        // Arrange
        var status = _fixture.RpForVmCgEnableDisableStatusDto;
        _dbContext.RPForVMCGEnableDisableStatuses.Add(status);
        await _dbContext.SaveChangesAsync();

        var updatedStatus = "Updated Status";
        status.Status = updatedStatus;

        // Act
        var result = await _repository.UpdateAsync(status);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(updatedStatus, result.Status);
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity_WhenExists()
    {
        // Arrange
        var status = _fixture.RpForVmCgEnableDisableStatusDto;
        _dbContext.RPForVMCGEnableDisableStatuses.Add(status);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(status);

        // Assert
        var deletedEntity = await _repository.GetByIdAsync(status.Id);
        Assert.Null(deletedEntity);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.RPForVMCGEnableDisableStatuses.RemoveRange(_dbContext.RPForVMCGEnableDisableStatuses);
        await _dbContext.SaveChangesAsync();
    }
}
