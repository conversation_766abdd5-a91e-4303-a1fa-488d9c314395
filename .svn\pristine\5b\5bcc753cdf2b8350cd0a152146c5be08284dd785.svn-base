﻿using ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLDBMirroingStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class SqlDbMirroringMonitorStatusProfile : Profile
{
    public SqlDbMirroringMonitorStatusProfile()
    {
        CreateMap<CreateSQLDBMirroringStatusCommand, MSSQLDBMirroringStatus>().ReverseMap();
        CreateMap<GetMSSQLDbMonitorStatusPaginatedListQuery, PaginatedResult<MSSQLDBMirroringStatusVm>>();
        CreateMap<MSSQLDBMirroringStatus, MSSQLDBMirroringStatusVm>().ReverseMap();
        CreateMap<SQLDBMirroringMonitorStatusDetailByTypeQuery, List<SQLDBMirroringMonitorStatusDetailByTypeVm>>();
    }
}