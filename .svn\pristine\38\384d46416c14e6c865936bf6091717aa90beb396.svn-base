﻿using ContinuityPatrol.Application.Features.TableAccess.Commands.Update;
using ContinuityPatrol.Application.Features.TableAccess.Event.PaginatedView;
using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class TableAccessController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly ILogger<TableAccessController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;

    public TableAccessController(IPublisher publisher, IDataProvider provider, IMapper mapper, ILogger<TableAccessController> logger)
    {
        _publisher = publisher;
        _dataProvider = provider;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in TableAccess");

        await _publisher.Publish(new TableAccessPaginatedEvent());

        var tableAccesses = await _dataProvider.TableAccess.GetAllTableAccesses();

        var tableAccessList = new TableAccessModel
        {
            PaginatedTableAccess = tableAccesses
        };

        return View(tableAccessList);
    }

    public async Task<IActionResult> GetTableNamesBySchemaName(string schemaName)
    {
        _logger.LogDebug("Entering List method in TableAccess");

        if (schemaName.IsNullOrWhiteSpace())
        {
            _logger.LogDebug("SchemaName is not valid format to retrieve the tableNames in TableAccess.");
            return Json(new { Success = false, Message = "Table Name Id is not valid format", ErrorCode = 0 });
        }

        try
        {
            var tableNames = await _dataProvider.TableAccess.GetTableNamesBySchemaName(schemaName);
            _logger.LogDebug($"Successfully retrieved tableNames by schemaName '{schemaName}'");
            return Json(new { Success = true, data = tableNames });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on table access page while retrieving the tableNames by schemaName '{schemaName}'.", ex);
            return ex.GetJsonException();
        }
    }

    //public async Task<List<TableAccessListVm>> GetTableAccessList()
    //{
    //    var tableAccesses = await _dataProvider.TableAccess.GetAllTableAccesses();

    //    return tableAccesses;
    //}
    public async Task<IActionResult> GetTableAccessList()
    {
        _logger.LogDebug("Entering GetTableAccessList method in TableAccess");

        try
        {
            var tableAccesses = await _dataProvider.TableAccess.GetAllTableAccesses();
            _logger.LogDebug("Successfully retrieved table access list in TableAccess.");
            return Json(tableAccesses);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on table access page while retrieving tableAccess list.", ex);
            return Json("");
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate([FromBody] TableAccessModel tableAccessModel)
    {
        _logger.LogDebug("Entering List method in TableAccess");

        try
        {
            var updateTableAccessCommand = new UpdateTableAccessCommand();
            var tableAccessCommand = _mapper.Map<List<UpdateTableAccess>>(tableAccessModel.PaginatedTableAccess);
            updateTableAccessCommand.UpdateTableAccess = tableAccessCommand;
            var result = await _dataProvider.TableAccess.UpdateAsync(updateTableAccessCommand);
            _logger.LogDebug($"Updating table access in TableAccess page");
            return Json(new { success = true, data = result });

        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on table access page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return ex.GetJsonException();
        }

        catch (Exception ex)
        {
            _logger.Exception("An error occurred on table access page while processing the request for create or update.", ex);
            TempData.NotifyWarning(ex.Message);
            return ex.GetJsonException();
        }
    }
    public async Task<IActionResult> TableList()
    {
        _logger.LogDebug("Entering List method in TableListAccess");

        await _publisher.Publish(new TableAccessPaginatedEvent());

        var tableAccesses = await _dataProvider.TableAccess.GetAllTableAccesses();

        var tableAccessList = new TableAccessModel
        {
            PaginatedTableAccess = tableAccesses
        };

        return Json(tableAccessList);
    }

    //private IActionResult RouteToPostView(BaseResponse result)
    //{
    //    TempData.Set(result.Success
    //        ? new NotificationMessage(NotificationType.Success, result.Message)
    //        : new NotificationMessage(NotificationType.Error, result.Message));

    //    return RedirectToAction("List", "TableAccess", new { Area = "Admin" });
    //}

}