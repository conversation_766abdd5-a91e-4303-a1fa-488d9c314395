﻿using ContinuityPatrol.Application.Features.AlertMaster.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertMaster.Commands;

public class DeleteAlertMasterTests : IClassFixture<AlertMasterFixture>
{
    private readonly AlertMasterFixture _alertMasterFixture;
    private readonly Mock<IAlertMasterRepository> _mockAlertMasterRepository;
    private readonly Mock<IPublisher> _mockPubilsh;
    private readonly DeleteAlertMasterCommandHandler _handler;

    public DeleteAlertMasterTests(AlertMasterFixture alertMasterFixture)
    {
        _alertMasterFixture = alertMasterFixture;

        _mockPubilsh = new Mock<IPublisher>();

        //var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        _mockAlertMasterRepository = AlertMasterRepositoryMocks.DeleteAlertMasterRepository(_alertMasterFixture.AlertMasters);

        _handler = new DeleteAlertMasterCommandHandler(_mockAlertMasterRepository.Object, _mockPubilsh.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_AlertMasterDeleted()
    {
        var result = await _handler.Handle(new DeleteAlertMasterCommand { AlertId = _alertMasterFixture.AlertMasters[0].ReferenceId }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteAlertMasterResponse_When_AlertMasterDeleted()
    {
        var result = await _handler.Handle(new DeleteAlertMasterCommand { AlertId = _alertMasterFixture.AlertMasters[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteAlertMasterResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_AlertMasterDeleted()
    {
        await _handler.Handle(new DeleteAlertMasterCommand { AlertId = _alertMasterFixture.AlertMasters[0].ReferenceId }, CancellationToken.None);

        var accessManager = await _mockAlertMasterRepository.Object.GetByReferenceIdAsync(_alertMasterFixture.AlertMasters[0].ReferenceId);

        accessManager.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidAlertMasterId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteAlertMasterCommand { AlertId = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteAlertMasterCommand { AlertId = _alertMasterFixture.AlertMasters[0].ReferenceId }, CancellationToken.None);

        _mockAlertMasterRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockAlertMasterRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.AlertMaster>()), Times.Once);
    }
}