﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowPermission.Events.Update;

public class WorkflowPermissionUpdatedEventHandler : INotificationHandler<WorkflowPermissionUpdatedEvent>
{
    private readonly ILogger<WorkflowPermissionUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowPermissionUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<WorkflowPermissionUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowPermissionUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update}{Modules.WorkflowPermission}",
            Entity = Modules.WorkflowPermission.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"WorkflowPermission '{updatedEvent.AccessProperties}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"WorkflowPermission '{updatedEvent.AccessProperties}' updated successfully.");
    }
}