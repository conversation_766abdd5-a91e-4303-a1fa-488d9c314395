﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Events.Create;
using ContinuityPatrol.Infrastructure.Contract;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperationGroup.Commands
{
    public class CreateWorkflowOperationGroupTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly Mock<IWorkflowOperationRepository> _mockWorkflowOperationRepository;
        private readonly Mock<IWorkflowOperationGroupRepository> _mockWorkflowOperationGroupRepository;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly Mock<ILoadBalancerRepository> _mockLoadBalancerRepository;
        private readonly Mock<IJobScheduler> _mockJobScheduler;
        private readonly Mock<ILogger<CreateWorkflowOperationGroupCommandHandler>> _mockLogger;
        private readonly CreateWorkflowOperationGroupCommandHandler _handler;

        public CreateWorkflowOperationGroupTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockUserService = new Mock<ILoggedInUserService>();
            _mockWorkflowOperationRepository = new Mock<IWorkflowOperationRepository>();
            _mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();
            _mockPublisher = new Mock<IPublisher>();
            _mockLoadBalancerRepository = new Mock<ILoadBalancerRepository>();
            _mockJobScheduler = new Mock<IJobScheduler>();
            _mockLogger = new Mock<ILogger<CreateWorkflowOperationGroupCommandHandler>>();

            _handler = new CreateWorkflowOperationGroupCommandHandler(
                _mockMapper.Object,
                _mockUserService.Object,
                _mockWorkflowOperationRepository.Object,
                _mockWorkflowOperationGroupRepository.Object,
                _mockPublisher.Object,
                _mockLoadBalancerRepository.Object,
                _mockJobScheduler.Object,
                _mockLogger.Object
            );
        }

        [Fact]
        public async Task Handle_ReturnsCorrectResponse_WhenCommandIsHandled()
        {
            var command = new CreateWorkflowOperationGroupCommand
            {
                CreateWorkflowOperationGroupListCommands = new List<CreateWorkflowOperationGroupListCommand>
                {
                    new CreateWorkflowOperationGroupListCommand
                    {
                        WorkflowId = "workflow-id-1",
                        WorkflowName = "Test Workflow",
                        ActionMode = "Test Mode",
                        InfraObjectId = "infra-id-1",
                        InfraObjectName = "Infra Test"
                    }
                }
            };
            var nodeConfig = new Domain.Entities.LoadBalancer
            {
                ConnectionType = "http",
                IPAddress = "127.0.0.1",
                Port = 8080,
                TypeCategory = "Load Balancer"
            };

            var workflowOperation = new Domain.Entities.WorkflowOperation { ReferenceId = "operation-id-1" };
            var workflowOperationGroups = new List<Domain.Entities.WorkflowOperationGroup>
            {
                new Domain.Entities.WorkflowOperationGroup { WorkflowOperationId = "operation-id-1" }
            };
            _mockLoadBalancerRepository
                .Setup(repo => repo.GetNodeConfigurationByTypeAndTypeCategory("ALL", "Load Balancer"))
                .ReturnsAsync(nodeConfig);

            _mockUserService.Setup(user => user.CompanyId).Returns("test-company-id");
            _mockUserService.Setup(user => user.LoginName).Returns("test-login");

            _mockMapper
                .Setup(mapper => mapper.Map<List<Domain.Entities.WorkflowOperationGroup>>(command.CreateWorkflowOperationGroupListCommands))
                .Returns(workflowOperationGroups);
            _mockMapper
                .Setup(mapper => mapper.Map<Domain.Entities.WorkflowOperation>(command))
                .Returns(workflowOperation);

            _mockWorkflowOperationRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.WorkflowOperation>()))
                .ReturnsAsync(workflowOperation);
            _mockWorkflowOperationGroupRepository
                .Setup(repo => repo.AddRange(It.IsAny<List<Domain.Entities.WorkflowOperationGroup>>()))
                .ReturnsAsync(workflowOperationGroups);
            _mockPublisher
                .Setup(pub => pub.Publish(It.IsAny<WorkflowOperationGroupCreatedEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);
            _mockJobScheduler
                .Setup(client => client.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
                .Returns(Task.CompletedTask);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.Equal("Profile Initiated Successfully.", result.Message);
            Assert.Equal("operation-id-1", result.WorkflowOperationId);

            _mockWorkflowOperationRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.WorkflowOperation>()), Times.Once);
            _mockWorkflowOperationGroupRepository.Verify(repo => repo.AddRange(It.IsAny<List<Domain.Entities.WorkflowOperationGroup>>()), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<WorkflowOperationGroupCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockJobScheduler.Verify(client => client.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Once);
        }
    }
}
