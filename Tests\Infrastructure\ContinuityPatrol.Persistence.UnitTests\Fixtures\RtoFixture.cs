using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class RtoFixture : IDisposable
{
    public List<Rto> RtoPaginationList { get; set; }
    public List<Rto> RtoList { get; set; }
    public Rto RtoDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public RtoFixture()
    {
        var fixture = new Fixture();

        RtoList = fixture.Create<List<Rto>>();

        RtoPaginationList = fixture.CreateMany<Rto>(20).ToList();

        RtoDto = fixture.Create<Rto>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
