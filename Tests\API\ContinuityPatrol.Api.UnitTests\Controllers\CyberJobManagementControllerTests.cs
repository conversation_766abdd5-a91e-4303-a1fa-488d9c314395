using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.Create;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.Update;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.UpdateJobState;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.UpdateJobStatus;
using ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetJobStatus;
using ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberJobManagementModel;
using ContinuityPatrol.Domain.ViewModels.CyberJobManagementLogsModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class CyberJobManagementControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly CyberJobManagementsController _controller;
    private readonly CyberJobManagementFixture _cyberJobManagementFixture;

    public CyberJobManagementControllerTests()
    {
        _cyberJobManagementFixture = new CyberJobManagementFixture();

        var testBuilder = new ControllerTestBuilder<CyberJobManagementsController>();
        _controller = testBuilder.CreateController(
            _ => new CyberJobManagementsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetCyberJobManagements_ReturnsExpectedList()
    {
        // Arrange
        var expectedCyberJobManagements = new List<CyberJobManagementListVm>
        {
            _cyberJobManagementFixture.CyberJobManagementListVm,
            _cyberJobManagementFixture.CyberJobManagementListVm,
            _cyberJobManagementFixture.CyberJobManagementListVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberJobManagementListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCyberJobManagements);

        // Act
        var result = await _controller.GetCyberJobManagements();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberJobManagements = Assert.IsAssignableFrom<List<CyberJobManagementListVm>>(okResult.Value);
        Assert.Equal(3, cyberJobManagements.Count);
    }

    [Fact]
    public async Task GetCyberJobManagementById_ReturnsExpectedDetail()
    {
        // Arrange
        var cyberJobManagementId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobManagementDetailQuery>(q => q.Id == cyberJobManagementId), default))
            .ReturnsAsync(_cyberJobManagementFixture.CyberJobManagementDetailVm);

        // Act
        var result = await _controller.GetCyberJobManagementById(cyberJobManagementId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberJobManagement = Assert.IsType<CyberJobManagementDetailVm>(okResult.Value);
        Assert.Equal(_cyberJobManagementFixture.CyberJobManagementDetailVm.Name, cyberJobManagement.Name);
    }

    [Fact]
    public async Task GetPaginatedCyberJobManagements_ReturnsExpectedResults()
    {
        // Arrange
        var query = new GetCyberJobManagementPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = new List<CyberJobManagementListVm>
        {
            _cyberJobManagementFixture.CyberJobManagementListVm,
            _cyberJobManagementFixture.CyberJobManagementListVm
        };
        var expectedResults = PaginatedResult<CyberJobManagementListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobManagementPaginatedListQuery>(q => 
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberJobManagements(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberJobManagementListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task CreateCyberJobManagement_ReturnsCreatedAtAction()
    {
        // Arrange
        var command = _cyberJobManagementFixture.CreateCyberJobManagementCommand;
        var expectedMessage = "CyberJobManagement has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberJobManagementResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberJobManagement(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberJobManagementResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberJobManagement_ReturnsOk()
    {
        // Arrange
        var command = _cyberJobManagementFixture.UpdateCyberJobManagementCommand;
        var expectedMessage = "CyberJobManagement has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberJobManagementResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberJobManagement(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberJobManagementResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteCyberJobManagement_ReturnsOk()
    {
        // Arrange
        var cyberJobManagementId = Guid.NewGuid().ToString();
        var expectedMessage = "CyberJobManagement has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCyberJobManagementCommand>(c => c.Id == cyberJobManagementId), default))
            .ReturnsAsync(new DeleteCyberJobManagementResponse
            {
                Message = expectedMessage,
                IsActive = false
            });

        // Act
        var result = await _controller.DeleteCyberJobManagement(cyberJobManagementId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteCyberJobManagementResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public async Task UpdateCyberJobManagementState_ReturnsOk()
    {
        // Arrange
        var command = _cyberJobManagementFixture.UpdateCyberJobManagementStateCommand;
        var expectedMessage = "CyberJobManagement states have been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberJobManagementStateResponse
            {
                Message = expectedMessage
            });

        // Act
        var result = await _controller.UpdateState(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberJobManagementStateResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberJobManagementStatus_ReturnsOk()
    {
        // Arrange
        var command = _cyberJobManagementFixture.UpdateCyberJobManagementStatusCommand;
        var expectedMessage = "CyberJobManagement status has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberJobManagementStatusResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberJobManagementStatusResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetCyberJobManagementStatus_ReturnsExpectedStatus()
    {
        // Arrange
        var expectedStatus = new List<CyberJobManagementStatusVm>
        {
            _cyberJobManagementFixture.CyberJobManagementStatusVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberJobManagementStatusQuery>(), default))
            .ReturnsAsync(expectedStatus);

        // Act
        var result = await _controller.GetCyberJobManagementStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var statusList = Assert.IsType<List<CyberJobManagementStatusVm>>(okResult.Value);
        Assert.Single(statusList);
    }

    [Fact]
    public async Task IsCyberJobManagementNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var jobName = "Existing Job Management";
        var jobId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobManagementNameUniqueQuery>(q => 
                q.Name == jobName && q.Id == jobId), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsCyberJobManagementNameExist(jobName, jobId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.True(exists);
    }

    [Fact]
    public async Task IsCyberJobManagementNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var jobName = "Unique Job Management Name";
        var jobId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobManagementNameUniqueQuery>(q => 
                q.Name == jobName && q.Id == jobId), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsCyberJobManagementNameExist(jobName, jobId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.False(exists);
    }

    [Fact]
    public async Task GetCyberJobManagements_HandlesEmptyList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberJobManagementListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<CyberJobManagementListVm>());

        // Act
        var result = await _controller.GetCyberJobManagements();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberJobManagements = Assert.IsAssignableFrom<List<CyberJobManagementListVm>>(okResult.Value);
        Assert.Empty(cyberJobManagements);
    }

    [Fact]
    public async Task GetCyberJobManagementById_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetCyberJobManagementById(invalidId));
    }

    [Fact]
    public async Task GetCyberJobManagementById_HandlesNotFound()
    {
        // Arrange
        var cyberJobManagementId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobManagementDetailQuery>(q => q.Id == cyberJobManagementId), default))
            .ThrowsAsync(new NotFoundException("CyberJobManagement", cyberJobManagementId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetCyberJobManagementById(cyberJobManagementId));
    }

    [Fact]
    public async Task DeleteCyberJobManagement_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteCyberJobManagement(invalidId));
    }

    [Fact]
    public async Task CreateCyberJobManagement_HandlesComplexScheduledJob()
    {
        // Arrange
        var command = _cyberJobManagementFixture.CreateCyberJobManagementCommand;

        var expectedMessage = "Complex CyberJobManagement created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberJobManagementResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberJobManagement(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberJobManagementResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Complex", response.Message);
    }

    [Fact]
    public async Task UpdateCyberJobManagementState_HandlesBatchStateUpdate()
    {
        // Arrange
        var command = _cyberJobManagementFixture.UpdateCyberJobManagementStateCommand;

        var expectedMessage = "Batch state update completed for 3 jobs.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberJobManagementStateResponse
            {
                Message = expectedMessage,
               
            });

        // Act
        var result = await _controller.UpdateState(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberJobManagementStateResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
       
    }

    [Fact]
    public async Task GetPaginatedCyberJobManagements_HandlesAdvancedFiltering()
    {
        // Arrange
        var query = new GetCyberJobManagementPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 25,
            SearchString = "Backup",
           
        };

        var expectedData = new List<CyberJobManagementListVm>
        {
            new CyberJobManagementListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Database Backup Job",
                AirgapId = Guid.NewGuid().ToString(),
                AirgapName = "Enterprise Backup AirGap",
                WorkflowId = Guid.NewGuid().ToString(),
                WorkflowName = "Database Backup Workflow",
                SolutionId = Guid.NewGuid().ToString(),
                SolutionName = "Enterprise Backup Solution",
                IsSchedule = 1,
                ScheduleType = 2,
                ScheduleTime = "02:00:00",
                CronExpression = "0 0 2 * * ? *",
                Status = "Running",
                State = "In Progress",
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Backup Node 01",
                ExceptionMessage = null,
                LastExecutedTime = DateTime.Now.AddHours(-1).ToString("yyyy-MM-dd HH:mm:ss")
            }
        };
        var expectedResults = PaginatedResult<CyberJobManagementListVm>.Success(expectedData, 1, 1, 25);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobManagementPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber &&
                q.PageSize == query.PageSize &&
                q.SearchString == query.SearchString), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberJobManagements(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberJobManagementListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);

        var job = paginatedResult.Data.First();
        Assert.Contains("Backup", job.Name);
        Assert.Equal("Running", job.Status);
        Assert.Equal("In Progress", job.State);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(25, paginatedResult.PageSize);
    }

    [Fact]
    public async Task UpdateCyberJobManagementStatus_HandlesJobCompletion()
    {
        // Arrange
        var command = _cyberJobManagementFixture.UpdateCyberJobManagementStatusCommand;

        var expectedMessage = "Job status updated to Completed.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberJobManagementStatusResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberJobManagementStatusResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal("Completed", command.Status);
    }

    [Fact]
    public async Task GetCyberJobManagementStatus_HandlesDetailedStatusBreakdown()
    {
        // Arrange
        var expectedStatus = new List<CyberJobManagementStatusVm>
        {
            new CyberJobManagementStatusVm { Status = "Running", Count = 15 },
            new CyberJobManagementStatusVm { Status = "Failed", Count = 3 },
            new CyberJobManagementStatusVm { Status = "Completed", Count = 25 }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberJobManagementStatusQuery>(), default))
            .ReturnsAsync(expectedStatus);

        // Act
        var result = await _controller.GetCyberJobManagementStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var statusList = Assert.IsType<List<CyberJobManagementStatusVm>>(okResult.Value);
        Assert.Equal(3, statusList.Count);
        Assert.Equal(15, statusList.First(s => s.Status == "Running").Count);
        Assert.Equal(3, statusList.First(s => s.Status == "Failed").Count);
        Assert.Equal(25, statusList.First(s => s.Status == "Completed").Count);
    }

    [Fact]
    public async Task CreateCyberJobManagement_HandlesMonitoringJob()
    {
        // Arrange
        var command = _cyberJobManagementFixture.CreateCyberJobManagementCommand;

        var expectedMessage = "Monitoring CyberJobManagement created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberJobManagementResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberJobManagement(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberJobManagementResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Monitoring", response.Message);
    }

    [Fact]
    public async Task GetCyberJobManagementLogs_ReturnsExpectedLogs()
    {
        // Arrange
        var expectedLogs = new List<CyberJobManagementLogsListVm>
        {
            new CyberJobManagementLogsListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Backup Job Log",
                AirgapId = Guid.NewGuid().ToString(),
                AirgapName = "Enterprise AirGap System",
                WorkflowId = Guid.NewGuid().ToString(),
                WorkflowName = "Daily Backup Workflow",
                SolutionId = Guid.NewGuid().ToString(),
                SolutionName = "Enterprise Backup Solution",
                IsSchedule = 1,
                ScheduleType = 2,
                ScheduleTime = "02:00:00",
                CronExpression = "0 0 2 * * ? *",
                Status = "Completed",
                State = "Success",
                Mode = "Automatic",
                JobId = Guid.NewGuid().ToString(),
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Enterprise Node 01",
                ExceptionMessage = null,
                LastExecutedTime = DateTime.Now.AddHours(-6).ToString("yyyy-MM-dd HH:mm:ss")
            },
            new CyberJobManagementLogsListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Replication Job Log",
                AirgapId = Guid.NewGuid().ToString(),
                AirgapName = "Enterprise AirGap System",
                WorkflowId = Guid.NewGuid().ToString(),
                WorkflowName = "Cross-Site Replication Workflow",
                SolutionId = Guid.NewGuid().ToString(),
                SolutionName = "Enterprise Replication Solution",
                IsSchedule = 1,
                ScheduleType = 3,
                ScheduleTime = "04:00:00",
                CronExpression = "0 0 4 * * ? *",
                Status = "Running",
                State = "In Progress",
                Mode = "Automatic",
                JobId = Guid.NewGuid().ToString(),
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Enterprise Node 02",
                ExceptionMessage = null,
                LastExecutedTime = DateTime.Now.AddHours(-2).ToString("yyyy-MM-dd HH:mm:ss")
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberJobManagementLogsListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedLogs);

        // Act
        var result = await _controller.GetCyberJobManagementLogs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var logs = Assert.IsAssignableFrom<List<CyberJobManagementLogsListVm>>(okResult.Value);
        Assert.Equal(2, logs.Count);
        Assert.Contains(logs, log => log.Status == "Completed");
        Assert.Contains(logs, log => log.Status == "Running");
        Assert.All(logs, log => Assert.NotNull(log.WorkflowName));
    }

    [Fact]
    public async Task GetPaginatedCyberJobManagementLogs_ReturnsExpectedResults()
    {
        // Arrange
        var query = new GetCyberJobManagementLogsPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 15,
            SearchString = "Backup",
            SortColumn = "LastExecutedTime",
            SortOrder = "DESC"
        };

        var expectedData = new List<CyberJobManagementLogsListVm>
        {
            new CyberJobManagementLogsListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Database Backup Job Log",
                AirgapId = Guid.NewGuid().ToString(),
                AirgapName = "Enterprise Database AirGap",
                WorkflowId = Guid.NewGuid().ToString(),
                WorkflowName = "Database Backup Workflow",
                SolutionId = Guid.NewGuid().ToString(),
                SolutionName = "Enterprise Database Backup Solution",
                IsSchedule = 1,
                ScheduleType = 1,
                ScheduleTime = "01:30:00",
                CronExpression = "0 30 1 * * ? *",
                Status = "Completed",
                State = "Success",
                Mode = "Automatic",
                JobId = Guid.NewGuid().ToString(),
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Database Node 01",
                ExceptionMessage = null,
                LastExecutedTime = DateTime.Now.AddHours(-1).ToString("yyyy-MM-dd HH:mm:ss")
            }
        };
        var expectedResults = PaginatedResult<CyberJobManagementLogsListVm>.Success(expectedData, 1, 1, 15);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobManagementLogsPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber &&
                q.PageSize == query.PageSize &&
                q.SearchString == query.SearchString &&
                q.SortColumn == query.SortColumn &&
                q.SortOrder == query.SortOrder), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberJobManagementLogs(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberJobManagementLogsListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);
        Assert.Contains("Backup", paginatedResult.Data.First().Name);
        Assert.Equal("Completed", paginatedResult.Data.First().Status);
        Assert.Equal("DESC", query.SortOrder);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(15, paginatedResult.PageSize);
    }

    [Fact]
    public async Task GetCyberJobManagementLogs_HandlesEmptyLogs()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberJobManagementLogsListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<CyberJobManagementLogsListVm>());

        // Act
        var result = await _controller.GetCyberJobManagementLogs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var logs = Assert.IsAssignableFrom<List<CyberJobManagementLogsListVm>>(okResult.Value);
        Assert.Empty(logs);
    }

    [Fact]
    public async Task GetPaginatedCyberJobManagementLogs_HandlesFailedJobs()
    {
        // Arrange
        var query = new GetCyberJobManagementLogsPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Failed"
        };

        var expectedData = new List<CyberJobManagementLogsListVm>
        {
            new CyberJobManagementLogsListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Failed Enterprise Backup Job",
                AirgapId = Guid.NewGuid().ToString(),
                AirgapName = "Enterprise AirGap System",
                WorkflowId = Guid.NewGuid().ToString(),
                WorkflowName = "Failed Backup Workflow",
                SolutionId = Guid.NewGuid().ToString(),
                SolutionName = "Enterprise Backup Solution",
                IsSchedule = 1,
                ScheduleType = 2,
                ScheduleTime = "03:00:00",
                CronExpression = "0 0 3 * * ? *",
                Status = "Failed",
                State = "Error",
                Mode = "Automatic",
                JobId = Guid.NewGuid().ToString(),
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Enterprise Node 03",
                ExceptionMessage = "Disk space insufficient for backup operation. Available: 50GB, Required: 200GB",
                LastExecutedTime = DateTime.Now.AddHours(-3).ToString("yyyy-MM-dd HH:mm:ss")
            }
        };
        var expectedResults = PaginatedResult<CyberJobManagementLogsListVm>.Success(expectedData, 1, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobManagementLogsPaginatedListQuery>(q =>
                q.SearchString == query.SearchString), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberJobManagementLogs(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberJobManagementLogsListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);
        var failedJob = paginatedResult.Data.First();
        Assert.Equal("Failed", failedJob.Status);
        Assert.Equal("Error", failedJob.State);
        Assert.Contains("Disk space insufficient", failedJob.ExceptionMessage);
    }
}
