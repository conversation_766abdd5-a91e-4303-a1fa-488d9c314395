﻿using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Application.Features.LogViewer.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.LogViewer.Commands.Create;
using ContinuityPatrol.Application.Features.LogViewer.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.LogViewerModel;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class ServerLogController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IMapper _mapper;
    private readonly IDataProvider _provider;
    private readonly ILogger<ServerLogController> _logger;

    public ServerLogController(IPublisher publisher, IMapper mapper, IDataProvider provider, ILogger<ServerLogController> logger)
    {
        _publisher = publisher;
        _mapper = mapper;
        _provider = provider;
        _logger = logger;
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Configuration.CreateAndEdit)]
    public async Task<IActionResult> CreateOrUpdate(LogViewerViewModel serverLog)
    {
        try
        {
            // Validate required fields
            if (string.IsNullOrWhiteSpace(serverLog.Name) ||
                string.IsNullOrWhiteSpace(serverLog.UserName) ||
                string.IsNullOrWhiteSpace(serverLog.Password) ||
                string.IsNullOrWhiteSpace(serverLog.FolderPath))
            {
                _logger.LogError("Invalid or empty value inputs are not accepted.");
                TempData.NotifyError("Invalid or empty value inputs are not accepted.");
                return RedirectToAction("List");
            }

            _logger.LogInformation("Entering the ServerLog CreateOrUpdate method.");

            // Encrypt password before saving
            serverLog.Password = SecurityHelper.Encrypt(serverLog.Password);

            // Check if updating an existing record
            var serverLogId = Request.Form["id"].ToString();
            if (string.IsNullOrWhiteSpace(serverLogId))
            {
                // Create new ServerLog
                var createServerLog = _mapper.Map<CreateLogViewerCommand>(serverLog);
                var response = await _provider.LogViewer.CreateAsync(createServerLog);
                TempData.NotifySuccess(response.Message);
            }
            else
            {
                // Update existing ServerLog
                var updateServerLog = _mapper.Map<UpdateLogViewerCommand>(serverLog);
                var updateResponse = await _provider.LogViewer.UpdateAsync(updateServerLog);
                TempData.NotifySuccess(updateResponse.Message);
            }
            _logger.LogInformation("End the ServerLog CreateOrUpdate method.");
            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error occurred in ServerLog CreateOrUpdate method: {ex}");
            TempData.NotifyError("An unexpected error occurred. Please try again.");
            return RedirectToAction("List");
        }
    }

    [AntiXss]
    public IActionResult List()
    {
        return View();
    }
    [HttpGet]
    public async Task<JsonResult> GetPagination(GetLogViewerPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in ServerLog");
        try
        {
            var paginationList = await _provider.LogViewer.GetPaginatedLogViewerList(query);
            paginationList.Data.ForEach(x => x.Password = SecurityHelper.Decrypt(x.Password) ?? x.Password);
            _logger.LogDebug("Successfully retrieved pagination list for ServerLog");
            return Json(new { Success = true, data = paginationList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on report ServerLog page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }

    }
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in Server Log");
        try
        {
            _logger.LogDebug($"Deleting Server Log Details by Id '{id}'");

            var result = await _provider.LogViewer.DeleteAsync(id);

            TempData.NotifySuccess(result.Message);

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred while deleting record on Server Log.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    [HttpGet]
    public async Task<bool> IsServerLogNameExist(string name, string? id)
    {
        _logger.LogDebug("Entering IsServerLogNameExist method in ServerLog");

        try
        {
            var nameExist = await _provider.LogViewer.IsLogViewerNameUnique(name, id);

            _logger.LogDebug("Returning result for IsLogViewerNameUnique on ServerLog");

            return nameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on ServerLog page while checking if ServerLog name exists for : {name}.", ex);
            return false;
        }
    }
}