﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Company.Events.Create;

public class CompanyCreatedEventHandler : INotificationHandler<CompanyCreatedEvent>
{
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ILogger<CompanyCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;

    public CompanyCreatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<CompanyCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _loggedInUserService = loggedInUserService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(CompanyCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        if (!_loggedInUserService.LoginName.Equals("Anonymous"))
        {
            var userActivity = new Domain.Entities.UserActivity
            {
                UserId = _loggedInUserService.UserId,
                LoginName = _loggedInUserService.LoginName,
                CompanyId = _loggedInUserService.CompanyId,
                RequestUrl = _loggedInUserService.RequestedUrl,
                HostAddress = _loggedInUserService.IpAddress,
                Action = $"{ActivityType.Create} {Modules.Company}",
                Entity = Modules.Company.ToString(),
                ActivityType = ActivityType.Create.ToString(),
                ActivityDetails = $"Company '{createdEvent.CompanyName}' created successfully.",
                CreatedBy = _loggedInUserService.UserId.IsNullOrEmpty()
                    ? Guid.NewGuid().ToString()
                    : _loggedInUserService.UserId,
                LastModifiedBy = _loggedInUserService.UserId.IsNullOrEmpty()
                    ? Guid.NewGuid().ToString()
                    : _loggedInUserService.UserId
            };

            await _userActivityRepository.AddAsync(userActivity);
        }

        _logger.LogInformation($"Company '{createdEvent.CompanyName}' created successfully.");
    }
}