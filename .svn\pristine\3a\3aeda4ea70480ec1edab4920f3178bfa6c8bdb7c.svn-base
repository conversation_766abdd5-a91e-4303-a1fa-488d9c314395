using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class DriftCategoryMasterFilterSpecification : Specification<DriftCategoryMaster>
{
    public DriftCategoryMasterFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.CategoryName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("categoryname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.CategoryName.Contains(stringItem.Replace("categoryname=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p => p.CategoryName.Contains(searchString);
            }
        }
    }
}