﻿function monitorTypeOracleRac(value, infraObjectName, moniterType, parsedData) {
    
    let monitor = value?.monitorServiceDetails
    
    const getDRDetails = (data, value, obj = null) => {
        let tdHtml = '';
        
        data.length && data.forEach((item) => {
            item.DrModels.forEach((a) => {
                let dataoracle = value == 'Replication_Mode' ? obj : a.MonitoringModel
                let iconClass = getIconClass(value, dataoracle);

                let tableData = '';

                if (value == 'Replication_Mode') {
                    tableData = obj ? obj : 'NA'
                } else {
                    tableData = obj && dataoracle[obj] ? dataoracle[obj][value] : dataoracle[value];
                }

                tdHtml += `<td class="text-truncate"><i class="${iconClass} me-1"></i>${tableData}</td>`;
            });
        });

        return tdHtml;
    }

    const getIconClass = (value, monitoringData) => {

        let iconClass = '';

        if (value == 'ServerName') {
            iconClass = 'cp-stand-server text-primary'

        } else if (value === 'Server_IpAddress' || value === 'Server_HostName') {
            let text = monitoringData?.ServerDetails?.Server_Status?.toLowerCase()
            iconClass = text === 'down' ? "cp-down-linearrow me-1 text-danger" : text === 'pending' ? 'cp-pending me-1 text-warning' : text === 'up' ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";

        } else if (value === 'Database_Sid') {

            iconClass = monitoringData?.ServerDetails?.Database_Sid ? 'cp-database me-1 text-primary' : "cp-disable me-1 text-danger";

        } else if (value === 'Log_sequence') {

            iconClass = monitoringData?.ServerDetails?.Log_sequence?.includes('NA') ? "cp-disable me-1 text-danger" : monitoringData?.ServerDetails?.Log_sequence ? "text-primary me-1 cp-fal-server" : "cp-disable me-1 text-danger";

        } else if (value === 'Replication_Mode') {
            iconClass = monitoringData ? "cp-replication-on me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'Services') {
            iconClass = monitoringData?.DatabaseMonitoring?.Services?.includes('NA') ? "cp-disable me-1 text-danger" : monitoringData?.DatabaseMonitoring?.Services ? "cp-service me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'Protection_mode') {
            iconClass = monitoringData?.ReplicationMonitoring?.Protection_mode?.includes('NA') ? "cp-disable me-1 text-danger" : monitoringData?.ReplicationMonitoring?.Protection_mode ? "cp-protection-mode me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'Dataguard_status') {
            iconClass = monitoringData?.ReplicationMonitoring?.Dataguard_status?.includes('NA') ? "cp-disable me-1 text-danger" : monitoringData?.ReplicationMonitoring?.Dataguard_status ? "text-primary cp-dataguard-status me-1" : "cp-disable me-1 text-danger";

        }

        return iconClass;
    }

    const getDynamicHeader = (MonitoringOracleRacModel) => {

        let dynamicHeader = '';
        let selectedNodeName = $('#nodeName').val();
        let selectedNodeData = MonitoringOracleRacModel?.length && MonitoringOracleRacModel.find(obj => `${obj.Node}` === selectedNodeName);
        
        if (selectedNodeData && Object.keys(selectedNodeData)?.length) {
            selectedNodeData?.DrModels && selectedNodeData?.DrModels.length && selectedNodeData?.DrModels.map((data) => {
                dynamicHeader += `<th>${data?.Type}</th>`
            })
        }

        return dynamicHeader;
    }

    let prserverdata = "";
    let prdatabasedata = "";
    let pripaddress = "";
    let prdatabase = "";
    let prlogsequence = "";
    let prlogsequencedata = "";
    let prservicename;
    let prprotection;
    let prdataguard;
    let application;
    let workflow;
    let prreplicatype;
    let prreplicamode;
    let prService;
    let prGuard;
    let prMode;
    

    if (moniterType === "OracleRac") {
        let repType = value?.replicationType ? 'cp-replication-type me-1 text-primary' : 'cp-disable me-1 text-danger'
        let rep = value?.replicationType !== null && value?.replicationType !== "" ? value?.replicationType : 'NA'
        let ipOrHostName;
        let prdata = parsedData?.MonitoringOracleRacModel;
        prdata.forEach((item) => {
           
            application = value?.monitorServiceDetails[0]?.isServiceUpdate?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.isServiceUpdate?.toLowerCase()?.includes("running") ? "text-success cp-reload cp-animate" : "text-danger cp-fail-back";
            workflow = value?.monitorServiceDetails[0]?.workflowName?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.workflowName ? "cp-log-archive-config me-1 text-primary" : "cp-disable me-1 text-danger";
            prreplicatype = value?.replicationType?.includes("NA") ? "cp-disable me-1 text-danger" : value?.replicationType ? "cp-replication-type me-1 text-primary" : "cp-disable me-1 text-danger";
            prreplicamode = parsedData?.PR_Replication_Mode?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData.PR_Replication_Mode ? "cp-replication-on me-1 text-primary" : "cp-disable me-1 text-danger";

            prservicename = item?.PrModel?.PrMonitoringModel.PrDatabaseMonitoring?.PR_Services?.includes("NA") ? "cp-disable me-1 text-danger" : item.PrModel?.PrMonitoringModel.PrDatabaseMonitoring?.PR_Services ? "cp-service me-1 text-primary" : "cp-disable me-1 text-danger";
          
            prprotection = item?.PrModel?.PrMonitoringModel?.PrReplicationMonitoring?.PR_Protection_mode?.includes("NA") ? "cp-disable me-1 text-danger" : item.PrModel?.PrMonitoringModel?.PrReplicationMonitoring?.PR_Protection_mode ? "cp-protection-mode me-1 text-primary" : "cp-disable me-1 text-danger";
          
            prdataguard = item?.PrModel?.PrMonitoringModel?.PrReplicationMonitoring?.PR_Dataguard_status?.includes("NA") ? "cp-disable me-1 text-danger" : item.PrModel?.PrMonitoringModel?.PrReplicationMonitoring?.PR_Dataguard_status ? "cp-dataguard-status me-1 text-primary" : "cp-disable me-1 text-danger";
         
            prService = item.PrModel?.PrMonitoringModel?.PrDatabaseMonitoring?.PR_Services || "NA"
            prMode = item.PrModel?.PrMonitoringModel?.PrReplicationMonitoring?.PR_Protection_mode || "NA"

            prGuard = item.PrModel?.PrMonitoringModel?.PrReplicationMonitoring?.PR_Dataguard_status || "NA"
        });

        let selectNode = $('<select id="nodeName"  class="form-select w-100" aria-label="Default select example"><option disabled >Select Node Name</option></select>');
        $('#nodeRelationCont').append(selectNode);

        prdata?.forEach((d, index) => {

            selectNode.append(`<option >${d.Node}</option>`);

            if (index === 0) {

                const oracleload = prdata.filter(a => a.PRNodeName === d.PRNodeName);
                prserverdata = (oracleload[0].PrModel?.PrMonitoringModel.PrServerDetails.PR_ServerName !== null ? oracleload[0].PrModel?.PrMonitoringModel.PrServerDetails.PR_ServerName : "NA");
            
                pripaddressdata = (oracleload[0].PrModel?.PrMonitoringModel.PrServerDetails.PR_Server_IpAddress !== null ? oracleload[0].PrModel?.PrMonitoringModel.PrServerDetails.PR_Server_IpAddress : "NA")

                pripaddress = oracleload[0]?.PrModel?.PrMonitoringModel?.PrServerDetails.PR_Server_Status 
                pripaddress = pripaddress?.toLowerCase() === 'down' ? "cp-down-linearrow me-1 text-danger" : pripaddress?.toLowerCase() === 'pending' ? 'cp-pending me-1 text-warning' : pripaddress?.toLowerCase() === 'up' ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";

                prdatabasedata = (oracleload[0].PrModel?.PrMonitoringModel.PrServerDetails.PR_Database_Sid !== null ? oracleload[0].PrModel?.PrMonitoringModel.PrServerDetails.PR_Database_Sid : "NA")
    
                prlogsequencedata = (oracleload[0].PrModel?.PrMonitoringModel.PrServerDetails.PR_Log_sequence !== null ? oracleload[0].PrModel?.PrMonitoringModel.PrServerDetails.PR_Log_sequence : "NA")
              
            }

        });

        selectNode.on('change', function () {

            let selectedNodeName = $(this).val();
            const oraclechange = prdata.filter(t => t.Node === selectedNodeName);

            if (oraclechange && oraclechange?.length) {
                prserverdata = (oraclechange[0].PrModel?.PrMonitoringModel?.PrServerDetails.PR_ServerName !== null ? oraclechange[0].PrModel?.PrMonitoringModel?.PrServerDetails.PR_ServerName : "NA")
                var iconClass = "cp-stand-server me-1 text-primary";
                $("#prserver").empty().append('<i class="' + iconClass + '"></i> ' + prserverdata)
                 pripaddressdata = oraclechange[0]?.PrModel?.PrMonitoringModel?.PrServerDetails?.Pr_ConnectViaHostName.toLowerCase() === "true" ? oraclechange[0]?.PrModel?.PrMonitoringModel?.PrServerDetails?.PR_Server_HostName : oraclechange[0]?.PrModel?.PrMonitoringModel?.PrServerDetails?.PR_Server_IpAddress
                //pripaddressdata = (oraclechange[0]?.PrModel?.PrMonitoringModel?.PrServerDetails.PR_Server_IpAddress !== null && oraclechange[0]?.PrModel?.PrMonitoringModel?.PrServerDetails.PR_Server_IpAddress !== "" ? oraclechange[0].PrModel?.PrMonitoringModel?.PrServerDetails.PR_Server_IpAddress : "NA")
                pripaddress = oraclechange[0]?.PrModel?.PrMonitoringModel?.PrServerDetails.PR_Server_Status ? oraclechange[0]?.PrModel?.PrMonitoringModel?.PrServerDetails.PR_Server_Status : 'itView_PRClass'

                prdatabase = oraclechange[0]?.PrModel?.PrMonitoringModel?.PrModel?.PrMonitoringModel?.PrServerDetails.PR_Database_Sid?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-database me-1 text-primary"
                prdatabasedata = (oraclechange[0].PrModel?.PrMonitoringModel?.PrServerDetails.PR_Database_Sid !== null ? oraclechange[0]?.PrModel?.PrMonitoringModel?.PrServerDetails.PR_Database_Sid : "NA")
                $("#prdatabase").empty().append('<i class="' + prdatabase + '"></i> ' + prdatabasedata)

                prlogsequence = oraclechange[0].PrModel?.PrMonitoringModel?.PrServerDetails.PR_Log_sequence?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-log-archive-config me-1 text-primary"
                prlogsequencedata = (oraclechange[0].PrModel?.PrMonitoringModel?.PrServerDetails.PR_Log_sequence !== null ? oraclechange[0].PrModel?.PrMonitoringModel?.PrServerDetails.PR_Log_sequence : "NA")
                $("#prlog").empty().append('<i class="' + prlogsequence + '"></i> ' + prlogsequencedata)

                let getInfraData = appendTableData(oraclechange)
                $("#infraobjectalldata").html(getInfraData);
            }

        });
       

        prdatabase = prdata[0].PrModel?.PrMonitoringModel?.PrServerDetails.PR_Database_Sid?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-database me-1 text-primary"

        prlogsequence = prdata[0].PrModel?.PrMonitoringModel?.PrServerDetails.PR_Log_sequence?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-log-archive-config me-1 text-primary"
        // drlogsequence = prdata[0].ServerDetails.DrServerDetails.DR_Log_sequence?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-log-archive-config me-1 text-primary"

        function appendTableData(data = ''){

            //$("#infraobjectalldata").empty();

            let selectedNodeName = $('#nodeName').val();

         
           
            let filteredData = parsedData?.MonitoringOracleRacModel?.length
                && parsedData?.MonitoringOracleRacModel.filter((nodeData) => nodeData?.Node === selectedNodeName);

           
            let drdata = filteredData.flatMap((item) =>
                item?.DrModels.map((a) => a?.MonitoringModel?.ServerDetails?.connectViaHostName)
            );

            
            drdata.forEach((ip, index) => {
             
                let isHostName = ip?.toLowerCase() === "true";  

              
                 value = isHostName ? 'Server_HostName' : 'Server_IpAddress';

             
                 ipOrHostName = isHostName
                    ? getDRDetails(data ? data : filteredData, 'Server_HostName', 'ServerDetails')
                    : getDRDetails(data ? data : filteredData, 'Server_IpAddress', 'ServerDetails');

              
            });
           
            let infraobjectdata =
                '<div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">' +
                ' <table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
                '<thead style="position: sticky;top: 0px;">' +
                '<tr>' +
                '<th>Component Monitor</th>' +
                ' <th>Production Server</th>' +
                `${getDynamicHeader(data ? data : filteredData)}` +
                '</tr>' +
                '</thead>' +
                '<tbody style="">' +
                '<tr>' +
                '<td>' + 'Server Name' + '</td>' +
                '<td id="prserver">' + '<i class="cp-stand-server me-1 text-primary"></i>' + prserverdata + '</td>' +
                `${getDRDetails(data ? data : filteredData, 'ServerName', 'ServerDetails')}` +

                '</tr>' +
                '<tr>' +
                '<td>' + 'IP Address/Host Name' + '</td>' +
                '<td id="praddress">' + '<i class="' + pripaddress + '"></i>' + (pripaddressdata !== "" && pripaddressdata !== null ? pripaddressdata : "NA") + '</td>' +
                `${ipOrHostName}` +

                '</tr>' +
                '<tr>' +
                '<td>' + 'Database SID' + '</td>' +
                '<td id="prdatabase">' + '<i class="' + prdatabase + '"></i>' + (prdatabasedata !== "" && prdatabasedata !== null ? prdatabasedata : "NA") + '</td>' +
                `${getDRDetails(data ? data : filteredData, 'Database_Sid', 'ServerDetails')}` +
                '</tr>' +
                '<tr>' +
                '<td>' + 'Last Generated Archive Log' + '</td>' +
                '<td id="prlog">' + '<i class="' + prlogsequence + '"></i>' + prlogsequencedata + '</td>' +
                '<td>' + '--' +
                '</tr>' +
                '<tr>' +
                '<td>' + 'Last Applied Archive Log' + '</td>' +
                '<td>' + '--' +
                `${getDRDetails(data ? data : filteredData, 'Log_sequence', 'ServerDetails')}` +
                '</tr>';
           
            infraobjectdata += generateMonitorServiceDetailsRow(workflow, application, monitor);

            infraobjectdata += '</tbody>' +
                '</table>' +
                '</div>' +
                '<div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">' +
                '<table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
                '<thead style="position: sticky;top: 0px;z-index: 1;">' +
                '<tr>' +
                '<th>Replication Monitor</th>' +
                '<th>Production Server</th>' +
                `${getDynamicHeader(data ? data : filteredData)}` +
                '</tr>' +
                '</thead>' +
                '<tbody style="">' +
                '<tr>' +
                '<td>' + "Replication Type" + '</td>' +
                '<td>' + '<i class="' + repType + '"></i>' + rep + '</td>' +

                '</tr>' +
                '<tr>' +
                '<td>' + "Replication Mode" + '</td>' +
                '<td>' + '<i class="' + prreplicamode + '"></i>' + (parsedData?.PR_Replication_Mode !== null && parsedData?.PR_Replication_Mode !== "" ? parsedData?.PR_Replication_Mode : "NA") + '</td>' +
                `${getDRDetails(data ? data : filteredData, 'Replication_Mode', parsedData?.DR_Replication_Mode ? parsedData?.DR_Replication_Mode : "")}` +
                '</tr>' +
                '<tr>' +
                '<td>' + "Service Name" + '</td>' +
                '<td>' + '<i class="' + prservicename + '"></i>' + (prService) + '</td>' +
                `${getDRDetails(data ? data : filteredData, 'Services', 'DatabaseMonitoring')}` +
                '</tr>' +
                '<tr>' +
                '<td>' + "Protection Mode" + '</td>' +
                '<td>' + '<i class="' + prprotection + '"></i>' + (prMode) + '</td>' +
                `${getDRDetails(data ? data : filteredData, 'Protection_mode', 'ReplicationMonitoring')}` +
                '</tr>' +
                '<tr>' +
                '<td>' + "DataGuard Status" + '</td>' +
                '<td>' + '<i class="' + prdataguard + '"></i>' + (prGuard) + '</td>' +
                `${getDRDetails(data ? data : filteredData, 'Dataguard_status', 'ReplicationMonitoring')}` +
                '</tr>' +
                '</tbody>' +
                '</table>' +
                '</div>';

            return infraobjectdata;
        }

        setTimeout(() => {

            let getInfraData = appendTableData();
            $("#infraobjectalldata").html(getInfraData);

        }, 200);

    }
}
