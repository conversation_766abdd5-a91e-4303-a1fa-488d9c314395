﻿using ContinuityPatrol.Application.Features.UserGroup.Commands.Create;
using ContinuityPatrol.Application.Features.UserGroup.Commands.Update;
using ContinuityPatrol.Application.Features.UserGroup.Queries.GetAssignedUserGroups;
using ContinuityPatrol.Application.Features.UserGroup.Queries.GetList;
using ContinuityPatrol.Application.Features.UserGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.UserGroupModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class UserGroupService : BaseClient, IUserGroupService
{
    public UserGroupService(IConfiguration config, IAppCache cache, ILogger<CompanyService> logger)
        : base(config, cache, logger)
    {

    }

    public async Task<BaseResponse> CreateAsync(CreateUserGroupCommand createUserGroup)
    {
        var request = new RestRequest("api/v6/usergroup", Method.Post);

        request.AddJsonBody(createUserGroup);

        ClearCache("GetCompanyNamesOnLogin");

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateUserGroupCommand updateUserGroup)
    {
        var request = new RestRequest("api/v6/usergroup", Method.Put);

        request.AddJsonBody(updateUserGroup);

        ClearCache("GetCompanyNamesOnLogin");

        return await Put<BaseResponse>(request);
    }

    public async Task<PaginatedResult<UserGroupListVm>> GetPaginatedUserGroups(GetUserGroupPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/usergroup");

        request.AddQueryParameter("PageNumber", query.PageNumber.ToString());
        request.AddQueryParameter("PageSize", query.PageSize.ToString());

        return await Get<PaginatedResult<UserGroupListVm>>(request);
    }

    public async Task<bool> IsGroupNameExist(string groupName, string id)
    {
        var request = new RestRequest($"api/v6/usergroup/name-exist?GroupName={groupName}");

        return await Get<bool>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string groupId)
    {
        var request = new RestRequest($"api/v6/user_group/{groupId}", Method.Delete);

        ClearCache("GetCompanyNamesOnLogin");

        return await Delete<BaseResponse>(request);
    }

    public Task<List<UserGroupListVm>> GetAllUserGroups()
    {
        throw new NotImplementedException();
    }

    public Task<UsersWithUserGroup> GetAllUserWithGroups(GetAllUserWithGroupListQuery query)
    {
        throw new NotImplementedException();
    }

    public Task<UserGroupListVm> GetByReferenceId(string userGroupId)
    {
        throw new NotImplementedException();
    }

    public async Task<List<UserGroupListVm>> GetUserGroupList()
    {
        var request = new RestRequest($"api/v6/usergroup");

        return await Get<List<UserGroupListVm>>(request);
    }
}