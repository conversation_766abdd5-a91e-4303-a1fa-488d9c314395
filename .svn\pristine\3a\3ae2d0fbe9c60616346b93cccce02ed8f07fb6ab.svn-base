﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowAction.Events.Update;

public class WorkflowActionUpdatedEventHandler : INotificationHandler<WorkflowActionUpdatedEvent>
{
    private readonly ILogger<WorkflowActionUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowActionUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<WorkflowActionUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowActionUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.WorkflowAction}",
            Entity = Modules.WorkflowAction.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"WorkflowAction '{updatedEvent.ActionName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"WorkflowAction '{updatedEvent.ActionName}' updated successfully.");
    }
}