﻿using ContinuityPatrol.Application.Features.FormType.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FormType.Queries;

public class GetFormTypeNameUniqueQueryHandlerTests : IClassFixture<FormTypeFixture>
{
    private readonly FormTypeFixture _formTypeFixture;

    private Mock<IFormTypeRepository> _mockFormTypeRepository;

    private readonly GetFormTypeNameUniqueQueryHandler _handler;

    public GetFormTypeNameUniqueQueryHandlerTests(FormTypeFixture formTypeFixture)
    {
        _formTypeFixture = formTypeFixture;

        _mockFormTypeRepository = FormTypeRepositoryMocks.GetFormTypeNameUniqueRepository(_formTypeFixture.FormTypes);

        _handler = new GetFormTypeNameUniqueQueryHandler(_mockFormTypeRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_FormTypeName_Exist()
    {
        _formTypeFixture.FormTypes[0].FormTypeName = "PR_Site";

        var result = await _handler.Handle(new GetFormTypeNameUniqueQuery { FormTypeName = _formTypeFixture.FormTypes[0].FormTypeName, FormTypeId = _formTypeFixture.FormTypes[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_FormTypeNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetFormTypeNameUniqueQuery { FormTypeName = "DR_Site", FormTypeId = _formTypeFixture.FormTypes[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsFormTypeNameExist_OneTime()
    {
        var handler = new GetFormTypeNameUniqueQueryHandler(_mockFormTypeRepository.Object);

        await handler.Handle(new GetFormTypeNameUniqueQuery { FormTypeId = _formTypeFixture.FormTypes[0].ReferenceId, FormTypeName = _formTypeFixture.FormTypes[0].FormTypeName }, CancellationToken.None);

        _mockFormTypeRepository.Verify(x => x.IsFormTypeNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_False_FormTypeName_NotMatch()
    {
        var result = await _handler.Handle(new GetFormTypeNameUniqueQuery { FormTypeName = "DR_Pro", FormTypeId = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockFormTypeRepository = FormTypeRepositoryMocks.GetFormTypeEmptyRepository();

        var result = await _handler.Handle(new GetFormTypeNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}