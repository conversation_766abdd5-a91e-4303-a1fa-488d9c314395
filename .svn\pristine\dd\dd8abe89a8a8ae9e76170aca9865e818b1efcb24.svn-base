﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.Workflow.Queries.GetPaginatedList;

public class
    GetWorkflowPaginatedListQueryHandler : IRequestHandler<GetWorkflowPaginatedListQuery,
        PaginatedResult<WorkflowListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowRepository _workflowRepository;

    public GetWorkflowPaginatedListQueryHandler(IMapper mapper, IWorkflowRepository workflowRepository)
    {
        _mapper = mapper;
        _workflowRepository = workflowRepository;
    }

    public async Task<PaginatedResult<WorkflowListVm>> Handle(GetWorkflowPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _workflowRepository.GetPaginatedQuery();

        var productFilterSpec = new WorkflowFilterSpecification(request.SearchString);

        var workflowList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<WorkflowListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return workflowList;
    }
}