﻿namespace ContinuityPatrol.Application.Features.OracleMonitorStatus.Events.Create;

public class OracleMonitorStatusCreatedEventHandler : INotificationHandler<OracleMonitorStatusCreatedEvent>
{
    private readonly ILogger<OracleMonitorStatusCreatedEventHandler> _logger;
    private readonly IOracleMonitorStatusRepository _oracleMonitorStatusRepository;

    public OracleMonitorStatusCreatedEventHandler(ILogger<OracleMonitorStatusCreatedEventHandler> logger,
        IOracleMonitorStatusRepository oracleMonitorStatusRepository)
    {
        _logger = logger;
        _oracleMonitorStatusRepository = oracleMonitorStatusRepository;
    }

    public async Task Handle(OracleMonitorStatusCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var oracleMonitorStatus = new Domain.Entities.OracleMonitorStatus
        {
            Type = createdEvent.Type,
            InfraObjectId = createdEvent.InfraObjectId,
            InfraObjectName = createdEvent.InfraObjectName,
            WorkflowId = createdEvent.WorkflowId,
            WorkflowName = createdEvent.WorkflowName,
            Properties = createdEvent.Properties,
            ConfiguredRPO = createdEvent.ConfiguredRPO,
            DataLagValue = createdEvent.DataLagValue
        };

        await _oracleMonitorStatusRepository.AddAsync(oracleMonitorStatus);

        _logger.LogInformation($"OracleMonitorStatus '{createdEvent.InfraObjectName}' created successfully.");
    }
}