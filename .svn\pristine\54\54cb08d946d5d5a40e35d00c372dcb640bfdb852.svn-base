﻿namespace ContinuityPatrol.Application.Features.DataSetColumns.Queries.GetNames;

public class GetDataSetColumnsNameQueryHandler : IRequestHandler<GetDataSetColumnsNameQuery, List<DataSetColumnsNameVm>>
{
    private readonly IDataSetColumnsRepository _dataSetColumnsRepository;
    private readonly IMapper _mapper;

    public GetDataSetColumnsNameQueryHandler(IMapper mapper, IDataSetColumnsRepository dataSetColumnsRepository)
    {
        _mapper = mapper;
        _dataSetColumnsRepository = dataSetColumnsRepository;
    }

    public async Task<List<DataSetColumnsNameVm>> Handle(GetDataSetColumnsNameQuery request,
        CancellationToken cancellationToken)
    {
        var dataSetColumns = (await _dataSetColumnsRepository.ListAllAsync()).ToList();

        var dataSetColumnsDto = _mapper.Map<List<DataSetColumnsNameVm>>(dataSetColumns);

        return dataSetColumnsDto;
    }
}