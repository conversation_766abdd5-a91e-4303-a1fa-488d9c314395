﻿using ContinuityPatrol.Application.Features.HeatMapLog.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.HeatMapLog.Commands;

public class DeleteHeatMapLogTests : IClassFixture<HeatMapLogFixture>
{
    private readonly HeatMapLogFixture _heatMapLogFixture;

    private readonly Mock<IHeatMapLogRepository> _heatMapLogRepositoryMock;

    private readonly DeleteHeatMapLogCommandHandler _handler;

    public DeleteHeatMapLogTests(HeatMapLogFixture heatMapLogFixture)
    {
        _heatMapLogFixture = heatMapLogFixture;

        _heatMapLogRepositoryMock = HeatMapLogRepositoryMocks.DeleteHeatMapLogRepository(_heatMapLogFixture.HeatMapLogs);
        
        _handler = new DeleteHeatMapLogCommandHandler(_heatMapLogRepositoryMock.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_HeatMapLogDeleted()
    {
        var result = await _handler.Handle(new DeleteHeatMapLogCommand { Id = _heatMapLogFixture.HeatMapLogs[0].ReferenceId }, CancellationToken.None);
        
        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteHeatMapLogResponse_When_HeatMapLogDeleted()
    {
        var result = await _handler.Handle(new DeleteHeatMapLogCommand { Id = _heatMapLogFixture.HeatMapLogs[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteHeatMapLogResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_HeatMapLogDeleted()
    {
        await _handler.Handle(new DeleteHeatMapLogCommand { Id = _heatMapLogFixture.HeatMapLogs[0].ReferenceId }, CancellationToken.None);

        var heatMapLog = await _heatMapLogRepositoryMock.Object.GetByReferenceIdAsync(_heatMapLogFixture.HeatMapLogs[0].ReferenceId);

        heatMapLog.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidHeatMapLogId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteHeatMapLogCommand { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteHeatMapLogCommand { Id = _heatMapLogFixture.HeatMapLogs[0].ReferenceId }, CancellationToken.None);

        _heatMapLogRepositoryMock.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _heatMapLogRepositoryMock.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.HeatMapLog>()), Times.Once);
    }
}