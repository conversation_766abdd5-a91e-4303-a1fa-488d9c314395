﻿using ContinuityPatrol.Application.Features.ImpactAvailability.Commands.Create;
using ContinuityPatrol.Application.Features.ImpactAvailability.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class ImpactAvailabilityFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<ImpactAvailability> ImpactAvailabilities { get; set; }

    public CreateImpactAvailabilityCommand CreateImpactAvailabilityCommand { get; set; }

    public UpdateImpactAvailabilityCommand UpdateImpactAvailabilityCommand { get; set; }

    public ImpactAvailabilityFixture()
    {
        ImpactAvailabilities = AutoImpactAvailabilityFixture.Create<List<ImpactAvailability>>();

        CreateImpactAvailabilityCommand = AutoImpactAvailabilityFixture.Create<CreateImpactAvailabilityCommand>();

        UpdateImpactAvailabilityCommand = AutoImpactAvailabilityFixture.Create<UpdateImpactAvailabilityCommand>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ImpactAvailabilityProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoImpactAvailabilityFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateImpactAvailabilityCommand>(p => p.BusinessServiceName, 10));
            fixture.Customize<CreateImpactAvailabilityCommand>(c => c.With(b => b.BusinessServiceId, 0.ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateImpactAvailabilityCommand>(p => p.BusinessServiceName, 10));
            fixture.Customize<UpdateImpactAvailabilityCommand>(c => c.With(b => b.Id, 0.ToString));
            fixture.Customize<AccessManager>(c => c.With(b => b.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}