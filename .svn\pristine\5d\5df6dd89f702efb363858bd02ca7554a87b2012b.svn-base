﻿namespace ContinuityPatrol.Application.Features.AlertInformation.Commands.Update;

public class UpdateAlertInformationCommand : IRequest<UpdateAlertInformationResponse>
{
    public string Id { get; set; }
    public string Type { get; set; }
    public string Severity { get; set; }
    public string Code { get; set; }
    public int AlertFrequency { get; set; }

    public override string ToString()
    {
        return $"Type: {Type}; Id:{Id};";
    }
}