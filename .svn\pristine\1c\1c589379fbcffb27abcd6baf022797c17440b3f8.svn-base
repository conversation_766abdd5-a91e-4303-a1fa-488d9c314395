﻿using ContinuityPatrol.Application.Features.MSSQLMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLMonitorLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IMssqlMonitorLogsService
{
    Task<BaseResponse> CreateAsync(CreateMSSQLMonitorLogCommand createMssqlMonitorLogCommand);
    Task<List<MSSQLMonitorLogsListVm>> GetAllMSSQLMonitorLogs();
    Task<MSSQLMonitorLogsDetailVm> GetByReferenceId(string id);
    Task<List<MSSQLMonitorLogsDetailByTypeVm>> GetMSSQLMonitorLogsByType(string type);
    Task<PaginatedResult<MSSQLMonitorLogsListVm>> GetPaginatedMSSQLMonitorLogs(GetMSSQLMonitorLogsPaginatedListQuery query);
}