using AutoFixture;
using ContinuityPatrol.Application.Features.CredentialProfile.Commands.Create;
using ContinuityPatrol.Application.Features.CredentialProfile.Commands.Update;
using ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.CredentialProfileModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class CredentialProfilesFixture : IDisposable
{
    public CreateCredentialProfileCommand CreateCredentialProfileCommand { get; set; }
    public UpdateCredentialProfileCommand UpdateCredentialProfileCommand { get; set; }
    public List<CredentialProfileListVm> CredentialProfileListVm { get; set; }
    public CredentialProfileDetailVm CredentialProfileDetailVm { get; set; }

    public CredentialProfilesFixture()
    {
        CreateCredentialProfileCommand = AutoCredentialProfilesFixture.Create<CreateCredentialProfileCommand>();
        UpdateCredentialProfileCommand = AutoCredentialProfilesFixture.Create<UpdateCredentialProfileCommand>();
        CredentialProfileListVm = AutoCredentialProfilesFixture.CreateMany<CredentialProfileListVm>(3).ToList();
        CredentialProfileDetailVm = AutoCredentialProfilesFixture.Create<CredentialProfileDetailVm>();
    }

    public Fixture AutoCredentialProfilesFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customize<CreateCredentialProfileCommand>(c => c
                .With(b => b.CompanyId, Guid.NewGuid().ToString)
                .With(b => b.Name, "Enterprise Database Admin Profile")
                .With(b => b.CredentialType, "Database")
                .With(b => b.Properties, "{\"username\":\"dbadmin\",\"password\":\"encrypted_password_hash\",\"server\":\"db-server-01\",\"port\":1433,\"database\":\"master\",\"connectionTimeout\":30}"));

            fixture.Customize<UpdateCredentialProfileCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.Name, "Updated Enterprise Database Admin Profile")
                .With(b => b.CredentialType, "Database")
                .With(b => b.Properties, "{\"username\":\"dbadmin_updated\",\"password\":\"updated_encrypted_password_hash\",\"server\":\"db-server-02\",\"port\":1433,\"database\":\"master\",\"connectionTimeout\":60}"));

            fixture.Customize<CredentialProfileListVm>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.Name, () => $"CredentialProfile-{fixture.Create<string>().Substring(0, 8)}")
                .With(b => b.CredentialType, () => fixture.Create<string>().Substring(0, 10))
                .With(b => b.Properties,
                    () => $"{{\"type\":\"{fixture.Create<string>().Substring(0, 5)}\",\"config\":\"encrypted\"}}")
                .With(b => b.CompanyId, Guid.NewGuid().ToString));
                

            fixture.Customize<CredentialProfileDetailVm>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.Name, "Enterprise SSH Admin Profile")
                .With(b => b.CredentialType, "SSH")
                .With(b => b.Properties, "{\"username\":\"sysadmin\",\"privateKey\":\"encrypted_private_key\",\"publicKey\":\"ssh_public_key\",\"passphrase\":\"encrypted_passphrase\",\"port\":22}")
                .With(b => b.CompanyId, Guid.NewGuid().ToString));


            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
