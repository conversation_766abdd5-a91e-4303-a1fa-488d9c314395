using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class CyberAlertFixture : IDisposable
{
    public const string CompanyId = "550e8400-e29b-41d4-a716-446655440000";
    public const string ParentCompanyId = "550e8400-e29b-41d4-a716-446655440001";
    public const string ComponentId = "ALERT_COMP_001";
    public const string SiteId = "ALERT_SITE_001";

    public List<CyberAlert> CyberAlertPaginationList { get; set; }
    public List<CyberAlert> CyberAlertList { get; set; }
    public CyberAlert CyberAlertDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public CyberAlertFixture()
    {
        var fixture = new Fixture();

        CyberAlertList = fixture.Create<List<CyberAlert>>();

        CyberAlertPaginationList = fixture.CreateMany<CyberAlert>(20).ToList();

        CyberAlertDto = fixture.Create<CyberAlert>();
       
        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
