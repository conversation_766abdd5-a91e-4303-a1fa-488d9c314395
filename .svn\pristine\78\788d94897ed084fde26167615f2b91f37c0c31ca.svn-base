using ContinuityPatrol.Application.Features.BackUp.Commands.Create;
using ContinuityPatrol.Application.Features.BackUp.Commands.Delete;
using ContinuityPatrol.Application.Features.BackUp.Commands.Execute;
using ContinuityPatrol.Application.Features.BackUp.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class BackUpFixture : IDisposable
{
    public List<BackUp> BackUps { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public List<BackUpLog> BackUpLogs { get; set; }
    public CreateBackUpCommand CreateBackUpCommand { get; set; }
    public UpdateBackUpCommand UpdateBackUpCommand { get; set; }
    public DeleteBackUpCommand DeleteBackUpCommand { get; set; }
    public BackUpExecuteCommand BackUpExecuteCommand { get; set; }
    public IMapper Mapper { get; set; }

    public BackUpFixture()
    {
        BackUps = new List<BackUp>
        {
            new BackUp
            {
                ReferenceId = Guid.NewGuid().ToString(),
                HostName = "TestServer01",
                DatabaseName = "TestDatabase",
                UserName = "TestUser",
                Password = "TestPassword123",
                IsLocalServer = true,
                IsBackUpServer = false,
                BackUpPath = @"C:\Backups\TestDatabase.bak",
                BackUpType = "Full",
                CronExpression = "0 0 2 * * ?",
                ScheduleType = "Daily",
                ScheduleTime = "02:00",
                Properties = "{\"compression\":\"true\",\"encryption\":\"false\",\"retention\":\"30\"}",
                KeepBackUpLast = "30",
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Node01",
                IsActive = true
            }
        };

        BackUps = AutoBackUpFixture.Create<List<BackUp>>();
        UserActivities = AutoBackUpFixture.Create<List<UserActivity>>();
        BackUpLogs = AutoBackUpFixture.Create<List<BackUpLog>>();
        CreateBackUpCommand = AutoBackUpFixture.Create<CreateBackUpCommand>();
        UpdateBackUpCommand = AutoBackUpFixture.Create<UpdateBackUpCommand>();
        DeleteBackUpCommand = AutoBackUpFixture.Create<DeleteBackUpCommand>();
        BackUpExecuteCommand = AutoBackUpFixture.Create<BackUpExecuteCommand>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<BackUpProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoBackUpFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateBackUpCommand>(p => p.HostName, 100));
            fixture.Customize<CreateBackUpCommand>(c => c.With(b => b.HostName, "TestServer01"));
            fixture.Customize<CreateBackUpCommand>(c => c.With(b => b.DatabaseName, "TestDatabase"));
            fixture.Customize<CreateBackUpCommand>(c => c.With(b => b.UserName, "TestUser"));
            fixture.Customize<CreateBackUpCommand>(c => c.With(b => b.Password, "TestPassword123"));
            fixture.Customize<CreateBackUpCommand>(c => c.With(b => b.IsLocalServer, true));
            fixture.Customize<CreateBackUpCommand>(c => c.With(b => b.IsBackUpServer, false));
            fixture.Customize<CreateBackUpCommand>(c => c.With(b => b.BackUpPath, @"C:\Backups\TestDatabase.bak"));
            fixture.Customize<CreateBackUpCommand>(c => c.With(b => b.BackUpType, "Full"));
            fixture.Customize<CreateBackUpCommand>(c => c.With(b => b.CronExpression, "0 0 2 * * ?"));
            fixture.Customize<CreateBackUpCommand>(c => c.With(b => b.ScheduleType, "Daily"));
            fixture.Customize<CreateBackUpCommand>(c => c.With(b => b.ScheduleTime, "02:00"));
            fixture.Customize<CreateBackUpCommand>(c => c.With(b => b.KeepBackUpLast, "30"));
            fixture.Customize<CreateBackUpCommand>(c => c.With(b => b.NodeId, Guid.NewGuid().ToString()));
            fixture.Customize<CreateBackUpCommand>(c => c.With(b => b.NodeName, "Node01"));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateBackUpCommand>(p => p.HostName, 100));
            fixture.Customize<UpdateBackUpCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateBackUpCommand>(c => c.With(b => b.HostName, "UpdatedServer01"));
            fixture.Customize<UpdateBackUpCommand>(c => c.With(b => b.DatabaseName, "UpdatedDatabase"));
            fixture.Customize<UpdateBackUpCommand>(c => c.With(b => b.UserName, "UpdatedUser"));
            fixture.Customize<UpdateBackUpCommand>(c => c.With(b => b.Password, "UpdatedPassword123"));
            fixture.Customize<UpdateBackUpCommand>(c => c.With(b => b.IsLocalServer, false));
            fixture.Customize<UpdateBackUpCommand>(c => c.With(b => b.IsBackUpServer, true));
            fixture.Customize<UpdateBackUpCommand>(c => c.With(b => b.BackUpPath, @"D:\Backups\UpdatedDatabase.bak"));
            fixture.Customize<UpdateBackUpCommand>(c => c.With(b => b.BackUpType, "Differential"));
            fixture.Customize<UpdateBackUpCommand>(c => c.With(b => b.CronExpression, "0 0 3 * * ?"));
            fixture.Customize<UpdateBackUpCommand>(c => c.With(b => b.ScheduleType, "Weekly"));
            fixture.Customize<UpdateBackUpCommand>(c => c.With(b => b.ScheduleTime, "03:00"));
            fixture.Customize<UpdateBackUpCommand>(c => c.With(b => b.KeepBackUpLast, "60"));
            fixture.Customize<UpdateBackUpCommand>(c => c.With(b => b.NodeId, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateBackUpCommand>(c => c.With(b => b.NodeName, "UpdatedNode01"));

            fixture.Customize<DeleteBackUpCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString()));
            fixture.Customize<BackUpExecuteCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<BackUp>(c => c.With(b => b.ReferenceId, Guid.NewGuid().ToString()));
            fixture.Customize<BackUp>(c => c.With(b => b.IsActive, true));
            fixture.Customize<BackUp>(c => c.With(b => b.HostName, "TestServer01"));
            fixture.Customize<BackUp>(c => c.With(b => b.DatabaseName, "TestDatabase"));
            fixture.Customize<BackUp>(c => c.With(b => b.UserName, "TestUser"));
            fixture.Customize<BackUp>(c => c.With(b => b.Password, "TestPassword123"));
            fixture.Customize<BackUp>(c => c.With(b => b.IsLocalServer, true));
            fixture.Customize<BackUp>(c => c.With(b => b.IsBackUpServer, false));
            fixture.Customize<BackUp>(c => c.With(b => b.BackUpPath, @"C:\Backups\TestDatabase.bak"));
            fixture.Customize<BackUp>(c => c.With(b => b.BackUpType, "Full"));
            fixture.Customize<BackUp>(c => c.With(b => b.CronExpression, "0 0 2 * * ?"));
            fixture.Customize<BackUp>(c => c.With(b => b.ScheduleType, "Daily"));
            fixture.Customize<BackUp>(c => c.With(b => b.ScheduleTime, "02:00"));
            fixture.Customize<BackUp>(c => c.With(b => b.Properties, "{\"compression\":\"true\",\"encryption\":\"false\",\"retention\":\"30\"}"));
            fixture.Customize<BackUp>(c => c.With(b => b.KeepBackUpLast, "30"));
            fixture.Customize<BackUp>(c => c.With(b => b.NodeId, Guid.NewGuid().ToString()));
            fixture.Customize<BackUp>(c => c.With(b => b.NodeName, "Node01"));

            fixture.Customize<BackUpLog>(c => c.With(b => b.ReferenceId, Guid.NewGuid().ToString()));
            fixture.Customize<BackUpLog>(c => c.With(b => b.IsActive, true));
            fixture.Customize<BackUpLog>(c => c.With(b => b.HostName, "TestServer01"));
            fixture.Customize<BackUpLog>(c => c.With(b => b.DatabaseName, "TestDatabase"));
            fixture.Customize<BackUpLog>(c => c.With(b => b.UserName, "TestUser"));
            fixture.Customize<BackUpLog>(c => c.With(b => b.IsLocalServer, true));
            fixture.Customize<BackUpLog>(c => c.With(b => b.IsBackUpServer, false));
            fixture.Customize<BackUpLog>(c => c.With(b => b.BackUpPath, @"C:\Backups\TestDatabase.bak"));
            fixture.Customize<BackUpLog>(c => c.With(b => b.Type, "Full"));
            fixture.Customize<BackUpLog>(c => c.With(b => b.Status, "Completed"));
            fixture.Customize<BackUpLog>(c => c.With(b => b.Properties, "{\"size\":\"1024MB\",\"duration\":\"15min\"}"));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}
