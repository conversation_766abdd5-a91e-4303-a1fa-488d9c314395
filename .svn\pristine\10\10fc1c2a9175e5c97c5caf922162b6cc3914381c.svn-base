﻿var createPermission = $("#ManageExCreate").data("create-permission").toLowerCase();
var deletePermission = $("#ManageExDelete").data("delete-permission").toLowerCase();
const nameExistUrl = 'Manage/EscalationMatrix/IsEscalationMatrixNameExist';

let eMatrixURL = {
    getPagination: "/Manage/EscalationMatrix/GetPagination",
    nameExistUrl: "Manage/EscalationMatrix/IsEscalationMatrixNameExist",
    CreateOrUpdate: "Manage/EscalationMatrix/CreateOrUpdate",
    Delete: "Manage/EscalationMatrix/Delete",
    Name: "Manage/EscalationMatrix/IsEscalationMatrixNameExist"
}

if (createPermission == 'false') {
    $("#creatematrix").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
   
}
let globallevelData = [];

$(function () {

    let selectedValues = [];

    var dataTable = $('#tblEmatrix').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-right-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
            }
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        processing: true,
        serverSide: true,
        filter: true,
        ajax: {
            type: "GET",
            url: "/Manage/EscalationMatrix/GetPagination",
            dataType: "json",
            data: function (d) {
                console.log("Ajax data function:", d);
                d.PageNumber = Math.ceil(d.start / d.length) + 1;
                d.pageSize = d.length;
                d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                selectedValues.length = 0;
            },
            dataSrc: function (json) {
                console.log("Ajax dataSrc function:", json);
                json.recordsTotal = json?.totalCount || 0;
                json.recordsFiltered = json?.filteredCount || json?.totalCount || 0;

                if (!json.data || json.data.length === 0) {
                    $(".pagination-column").addClass("disabled");
                } else {
                    $(".pagination-column").removeClass("disabled");
                }

                return json?.data || [];
            }
        },

        columns: [
            {
                data: null,
                name: "Sr. No.",
                autoWidth: true,
                render: function (data, type, row, meta) {
                    if (type === 'display') {
                        var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
                        return (page * meta.settings._iDisplayLength) + meta.row + 1;
                    }
                    return data;
                }
            },
            {
                data: "escMatName",
                name: "Matrix Code &amp Name",
                autoWidth: true,
                render: function (data, type, row) {
                    if (type === 'display') {
                        return `${row.escMatCode || ''} ${row.escMatName || ''}`;
                    }
                    return data;
                }
            },
            {
                data: "ownerid",
                name: "OwnerName",
                autoWidth: true,
                render: function (data, type, row) {
                    if (type === 'display') {
                        const owner = row.ownerName || '';
                        return `${owner}`;
                    }
                    return data;
                }
            },
            {
                data: null,
                name: "Matrix Used",
                autoWidth: true,
                render: function (data, type, row) {
                    if (type === 'display') {
                        let names = [];
                        let rawDesc = row.properties || '';
                        rawDesc = rawDesc.trim();
                        const firstChar = rawDesc[0];
                        const lastChar = rawDesc[rawDesc.length - 1];
                        const quoteChars = ["'", '"', "`"];

                        if (quoteChars.includes(firstChar) && quoteChars.includes(lastChar)) {
                            rawDesc = rawDesc.slice(1, -1);
                        }
                        if (rawDesc.startsWith('[') && rawDesc.endsWith(']')) {
                            const levels = JSON.parse(rawDesc);
                            levels.forEach(level => {
                                if (Array.isArray(level.users)) {
                                    level.users.forEach(userGroup => {
                                        if (Array.isArray(userGroup.list)) {
                                            userGroup.list.forEach(user => {
                                                if (user.name) {
                                                    names.push(user.name);
                                                }
                                            });
                                        }
                                    });
                                }
                            });
                        }
                        const fullList = names.join(', ');
                        const displayList = names.slice(0, 2).join(', ') + (names.length > 2 ? '...' : '');
                        return `<span title="${fullList}">${displayList}</span>`;
                    }
                    return data;
                }
            },

            {
                data: "createdDate",
                name: "Matrix CreateDetails",
                autoWidth: true,
                render: function (data, type, row) {
                    if (type === 'display') {
                        const created = row.createdDate || '';                        
                        return `${created}`;
                    }
                    return data;
                }
            },
            {
                data: "createdDate",
                name: "MatrixUpdate Details",
                autoWidth: true,
                render: function (data, type, row) {
                    if (type === 'display') {                        
                        const modified = row.lastModifiedDate || '';
                        return `${modified}`;
                    }
                    return data;
                }
            },
            
            {
                render: function (data, type, row) {
                    const isParent = row.isParent;
                    const escalationData = JSON.stringify(row).replace(/"/g, '&quot;'); 

                    if (createPermission === 'true' && deletePermission === "true") {
                        return `<div class="d-flex align-items-center gap-2">                                 
                                <span role="button" title="Edit" class="edit-button" data-escalation="${escalationData}">
                                    <i class="cp-edit"></i>
                                </span>
                                ${isParent ? `
                                    <span title="Delete disabled" class="delete-button disabled" style="opacity:0.5;">
                                        <i class="cp-Delete"></i>
                                    </span>` :
                                `
                                    <span role="button" title="Delete" class="delete-button" data-escalation-id="${row.id}" data-escalation-name="${row.escMatCode}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                        <i class="cp-Delete"></i>
                                    </span>`}
                            </div>`;
                    } else if (createPermission === 'true') {
                        return `<div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="edit-button" data-escalation="${escalationData}">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete disabled" class="icon-disabled">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                    } else if (deletePermission === "true") {
                        return `<div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit disabled" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="delete-button" data-escalation-id="${row.id}" data-escalation-name="${row.escMatName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                    } else {
                        return `<div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit disabled" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete disabled" class="icon-disabled">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                    }
                },
                orderable: false
            }
        ],
        order: [[0, 'asc']],
        columnDefs: [
            {
                targets: 0,
                type: 'num'
            }
        ],
        drawCallback: function (settings) {
            const randomColor = () => {
                return "#" + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0').toUpperCase();
            };

            document.querySelectorAll(".companyname").forEach((name) => {
                name.style.backgroundColor = randomColor();
            });
        }
    });

    //Events   
    //Levels
    $('#operationalService').select2();  
    
    $("#previousClick").on("click", function () {
        const nextBtn = document.getElementById("nextFunction");
        nextBtn.removeAttribute('data-bs-toggle', 'pill');
        nextBtn.removeAttribute('data-bs-target', '#pills-Teams');        
        const btn = document.getElementById("previousClick");
        btn.setAttribute('data-bs-toggle', 'pill');
        btn.setAttribute('data-bs-target', '#pills-Level');
        var tabTrigger = new bootstrap.Tab(btn);
        tabTrigger.show();
        $('#previousClick').closest("li").hide();
        $("#nextFunction").closest("li").show();
        $('#saveLevel').closest("li").hide();
    });

    $("#unanimousSave").click(function () {
        $('#unanimousId').hide();
    });

    $("#addName").click(function () {
        $('#unanimousId').show();
    });

    $("#saveLevel").click(function () {
        const isUserChecked = $('#userListContainer .userCheckList:checked').length > 0;
        const isGroupChecked = $('#userGroupListContainer .userCheckList:checked').length > 0;

        if (!isUserChecked && !isGroupChecked) {
            notificationAlert("warning","Please select at least one User or User Group");
            return false;
        }
        $("#end").show();
        $("#start").show();
        $("#escImage").addClass("d-none");
        const editingLevelId = $(this).data('editing-levelid');

        let userList = [];
        let userGroupList = [];
        $('#userListContainer .userCheckList:checked').each((idx, obj) => {
            userList.push({ id: obj.id, name: obj.name });
        });
        $('#userGroupListContainer .userCheckList:checked').each((idx, obj) => {
            userGroupList.push({ id: obj.id, name: obj.name });
        });

        const obj = {
            id: editingLevelId || getRandomLevelId('level'),
            name: $('#escalationLevelName').val(),
            description: $('#escalationLevelDescriptin').val(),
            Duration: {
                type: $('#escalationTimeZone').val(),
                value: $('#escalationLevelTime').val()
            },
            OpearationServiceName: $("#operationalService option:selected").text(),
            OpearationServiceId: $("#operationalService option:selected").val(),
            users: [{ type: "individual", list: userList }, { type: "group", list: userGroupList }],
            unanimous: [{
                Name: $("#useraddName").val(),
                PhoneNumber: $("#phoneNumber").val(),
                Email: $("#emailId").val()
            }]
        };

        if (editingLevelId) {  
            const index = globallevelData.findIndex(x => x.id === editingLevelId);
            if (index !== -1) {
                globallevelData[index] = obj;
            }
        } else {           
            globallevelData.push(obj);
        }
        $(".Escalation_Timeline ul").empty();
        globallevelData.forEach((item, index) => {
            const html = appendMatrix(item, index + 1);
            $(".Escalation_Timeline ul").prepend(html);
        });        
       $(this).removeData('editing-levelid');
        const btn = document.getElementById("previousClick");
        btn.setAttribute('data-bs-toggle', 'pill');
        btn.setAttribute('data-bs-target', '#pills-Level');
        var tabTrigger = new bootstrap.Tab(btn);
        tabTrigger.show();
        $('#previousClick').closest("li").hide();
        $("#nextFunction").closest("li").show();
        $('#saveLevel').closest("li").hide();
        const nextBtn = document.getElementById("nextFunction");
        nextBtn.removeAttribute('data-bs-toggle', 'pill');
        nextBtn.removeAttribute('data-bs-target', '#pills-Teams');
        $("#saveLevel").closest("li").hide();
        $("#useraddName, #phoneNumber, #emailId").val('');
        clearFields();        
    });

    $("#savebtnClick").click(async function () {
        data = JSON.stringify(globallevelData);
        const formData = {            
            Properties:JSON.stringify(globallevelData),
            EscMatDesc: "Test",             
            EscMatCode: "ESC-MAT-O1",        
            OwnerId: "aert3fg-tryu-ytjg6788fdssdjg-fdg",
            OwnerName: "Perpetuuiti",
            __RequestVerificationToken: gettoken()
        };
            const response = await $.ajax({
                url: `${RootUrl}${eMatrixURL.CreateOrUpdate}`,
                data: formData,
                type: 'POST'
            });

            if (response?.success && response?.data) {               
                notificationAlert('success', response.data.message);
            } else {
                errorNotification(response);
            }        
    });

    $('#escalationLevelName').on('keyup', async function () {        
            const value = $(this).val();
           await validatemscName(value);
    });

    $('#escalationLevelTime').on('keyup', async function () {
        const value = $(this).val();
        await validaDateTime(value);
    });

    $('#CreateModal').on('shown.bs.modal', function () {
        $('#operationalService').select2();
    });
   
    $('#operationalService').change(async function () {
        const selectedValue = $(this).val();  
        await validaDateOperationalService(selectedValue);
    });   

    $("#nextFunction").on("click", async function () {
        const levelName = $('#escalationLevelName').val();

        const levelTime = $("#escalationLevelTime").val();
        const levelOpService = $("#operationalService option:selected").text();
        const isNameValid = await validatemscName(levelName);
        const isTimeValid = await validaDateTime(levelTime);
        const isOpServiceValid = await validaDateOperationalService(levelOpService);
        const btn = document.getElementById("nextFunction");
        if (isNameValid && isTimeValid && isOpServiceValid) {           
            btn.setAttribute('data-bs-toggle', 'pill');
            btn.setAttribute('data-bs-target', '#pills-Teams');
            const tabTrigger = new bootstrap.Tab(btn);
            tabTrigger.show();
            $("#previousClick").closest("li").show();
            $("#saveLevel").closest("li").show();
            $("#nextFunction").closest("li").hide();
        } 
    });
    
    $(document).on('click', '.btnMatleveCon', function () {
        let id = $(this)[0].id;
        sessionStorage.setItem('teamResourceId', id);
        var companyData = $(this).data('escalation');
        $('#textgroupId').val(companyData.id);

        // $('#levelModal').modal('show');
        Escmatlevdis(id);

    });

    $(document).on('keyup', '#search-inp', function () {
        $('input[type="checkbox"]').each(function () {
            if ($(this).is(':checked')) {
                var checkboxValue = $(this).val();
                var inputValue = $('#search-inp').val();
                selectedValues.push(checkboxValue + inputValue);
            }
        });
        dataTable.search($(this).val()).draw();
    });

    $('#tablebody').on('click', '.edit-button', function () {        
        var companyData = $(this).data('escalation');
        populateModalFields(companyData);
        $('#savebtnClick').text('Update')
        $('#savebtnClick').attr('title', 'Update')
        $('#CreateModal').modal('show');        
        $("#end").show();
        $("#start").show();
        individual();
        groupUser();
        $("#escImage").addClass("d-none");
        clearFields();
        $("#previousClick").click();
    });

    $('#Escalation_Time').on('click', '.levelEdit', function () {        
        let levelId = $(this).attr('data-levelid');
        $("#saveLevel").data('editing-levelid', levelId);
        $("#escImage").addClass("d-none");
        $("#enNameEerror, #escHourError, #operationError").text('').removeClass('field-validation-error');
        let filterlevelData = globallevelData.filter((filterData) => filterData.id === levelId);
        if (filterlevelData.length > 0) {
            let filterData = filterlevelData[0];
            $('#escalationLevelName').val(filterData.name);
            $('#escalationLevelDescriptin').val(filterData.description);
            $('#escalationLevelTime').val(filterData.Duration.value);
            setDropdownValue('#escalationTimeZone', filterData.Duration.type);
            setDropdownValue('#operationalService', filterData.OpearationServiceName);
            $('#userListContainer').empty();
            $('#userGroupListContainer').empty();
            individual(filterData);
            groupUser(filterData);
            $("#saveLevel").text("Update");
            $('#useraddName').val(filterData.unanimous[0].Name);
            $('#emailId').val(filterData.unanimous[0].Email);
            $('#phoneNumber').val(filterData.unanimous[0].PhoneNumber);
        }
    });
    
    $('#Escalation_Time').on('click', '.levelDelete', function () {
        const levelId = $(this).closest('li').attr('id'); 
        let levelName = $(this).data('levelname');
        $("#deleteLevelData").text(levelName);
        $('#confirmDeleteLevelBtn').data('level-id', levelId);
        $('#DeleteModalLevel').modal('show');
    });

    $('#confirmDeleteLevelBtn').click(function () {       
        const levelId = $(this).data('level-id');
        globallevelData = globallevelData.filter(item => item.id !== levelId);
        $(".Escalation_Timeline ul").empty();
        globallevelData.forEach((item, index) => {
            const html = appendMatrix(item, index + 1);
            $(".Escalation_Timeline ul").prepend(html);
        });
        $('#DeleteModalLevel').modal('hide');
    });

    //LevelCreate
    $("#levelCreate").on("click", function () {
        globallevelData.length = 0;
        $("#CreateModal").modal("show")
        $("#escImage").removeClass("d-none");
        clearFields();
        individual();
        groupUser();
        $("#end").hide();
        $("#start").hide();
        $("#saveLevel").text("Insert");
        $(".Escalation_Timeline ul").empty();
        $("#previousClick").click();
    })
    $("#clearLevel").on("click", function () {
        clearFields();         
        const btn = document.getElementById("previousClick");
        btn.setAttribute('data-bs-toggle', 'pill');
        btn.setAttribute('data-bs-target', '#pills-Level');
        var tabTrigger = new bootstrap.Tab(btn);
        tabTrigger.show();
        $('#previousClick').closest("li").hide();
        $("#nextFunction").closest("li").show();
        $('#saveLevel').closest("li").hide();
        const nextBtn = document.getElementById("nextFunction");
        nextBtn.removeAttribute('data-bs-toggle', 'pill');
        nextBtn.removeAttribute('data-bs-target', '#pills-Teams');  
    });

    //Listpage Search
    $(document).on('input', '.EscalMatSearch', function () {
        let value = $(this).val().toLowerCase();

        $('#tblesclevUser').children().last().children().each(function () {
            $(this).toggle($(this).find('.EM_UserName').text().toLowerCase().indexOf(value) > -1);
        })
    });

    $(document).on('input', '.EscalTeamSearch', function () {
        let value = $(this).val().toLowerCase();

        $('#tblesclevTeam').children().last().children().each(function () {
            $(this).toggle($(this).find('.EM_TeamName').text().toLowerCase().indexOf(value) > -1);
        })
    });
    //Search
    $('#txtSearch').on('keydown input', function () {
        const term = $(this).val().toLowerCase();
        function filterList(containerId, rowSelector, textSelector, emptyMsg) {
            const $container = $(containerId);
            let count = 0;

            $(`${containerId} ${rowSelector}`).each(function () {
                const text = $(this).find(textSelector).text().toLowerCase();
                $(this).toggle(text.includes(term));
                if (text.includes(term)) count++;
            });

            $(`${containerId} .no-data-message`).remove();
            if (count === 0) {
                const colspan = $(`${containerId} table tr:first td`).length || 4;
                $(`${containerId} table tbody`).append(`<tr class="no-data-message"><td colspan="${colspan}" class="text-center text-muted">${emptyMsg}</td></tr>`);
            }
        }
        filterList('#userListContainer', 'tr', 'td:nth-child(2) span:last', 'No data found');
        filterList('#userGroupListContainer', 'tr', 'label.form-check-label', 'No data found');
    });

    //delete
    let deleteEscId = null;
    $('#tablebody').on('click', '.delete-button', function () {
        deleteEscId = $(this).data('escalation-id');
        const escName = $(this).data('escalation-name');

        $('#textDeleteId').val(deleteEscId);
        $('#deleteData').text(escName);
        $('#DeleteModal').modal('show');
    });

    $('#confirmDeleteBtn').on('click', function () {       
        $.ajax({
            url: RootUrl + eMatrixURL.Delete,
            type: "POST",
            dataType: "json",
            data: { id: deleteEscId },
            success: function (result) {
                if (result?.success) {
                    notificationAlert("success", result.data);                   
                    $(`[data-escalation-id="${deleteEscId}"]`).closest('tr').remove();
                } else {
                    errorNotification(result);
                }
            },            
            complete: function () {
                $('#DeleteModal').modal('hide');
                deleteEscId = null;
            }
        });
    });

    ///Functions

    const getRandomLevelId = (value) => {
        return value + "_" + Math.floor(Math.random() * 11110 * Math.random() * 103180)
    }  
    let escMatCounter = 1;

    const appendMatrix = (data, index) => {
        let borderColor = ['success', 'warning', 'primary', 'danger'][index % 4];

        let html = `
            <li class="li" id="${data.id}">
                <div class="Escalation_Timeline_Card card border-${borderColor}">
                    <div class="d-flex align-items-center">
                        <span class="Timeline_Card_Level bg-${borderColor} badge bg-${borderColor}">
                            Level ${index}
                        </span>
                        <div class="d-grid ms-3">
                            <h6 class="mb-1 text-truncate" title="${data.description}">
                                ${data.name}
                            </h6>
                            <span class="d-none">
                                <img class="rounded-circle" src="" width="20" height="20">
                            </span>
                        </div>
                        <div class="d-flex ms-auto">
                            <span class="text-primary me-2 text-truncate">
                                <i class="cp-apply-finish-time"></i>
                                &nbsp;${data.Duration.value} ${data.Duration.type}
                            </span>
                           <i class="cp-edit fs-5 me-2 levelEdit" data-levelId='${data.id}'></i>
                            <i class="cp-Delete fs-5 me-2 levelDelete" data-levelName='${data.name}'></i>
                        </div>
                    </div>
                </div>
            </li>`;

        return html
    }
    function OperationalServiceList() {

    $("#operationalService").select2();

    $.ajax({
        type: "GET",
        url: "/Manage/EscalationMatrix/GetOperationalService",
        dataType: "json",
        success: function (data) {
            let operationalService = $('#operationalService')
            data.forEach((item, index) => {
                if (index == 0) {
                    operationalService.append('<option value=""></option>')
                }
                operationalService.append('<option value="' + item.id + '">' + item.name + '</option>')
            })

        }

    });
}
    function individual(filterData = null) {
    const userColorPallete = [
        '#00CCFF', '#0066FF', '#CC3399', '#FF9900', '#99CC00', '#3399FF', '#993399',
        '#339966', '#993333', '#009900', '#000099', '#666633', '#FF3300', '#6600CC', '#C65F00', '#009974',
        '#BEBE00', '#CC0000', '#9100C5', '#020057', '#949C3C', '#00CB82', '#418E8A', '#0099CC', '#3D50FF'
    ];

    $.ajax({
        type: "GET",
        url: "/Manage/EscalationMatrix/GetUserNames",
        dataType: "json",
        success: function (data) {
            let $container = $('#userListContainer');
            $container.empty();

            let $table = $('<table class="table-borderless table"><tbody></tbody></table>');

            $.each(data, function (index, item) {
                if (!item || !item.userInfo) return;

                let user = item.userInfo;

                let userName = user.userName || 'Unknown';
                let userEmail = user.email || 'NA';
                let userId = user.userId || `user_${index}`;
                let displayName = userName.length > 21 ? userName.substring(0, 21) + '...' : userName;

                // Get initials
                const nameSplit = userName.trim().split(' ');
                const initials = nameSplit.length > 1
                    ? (nameSplit[0][0] + nameSplit[1][0]).toUpperCase()
                    : nameSplit[0][0].toUpperCase();

                // Color from palette
                const colorIndex = index % userColorPallete.length;
                const bgColor = userColorPallete[colorIndex];

                const avatarStyle = `
                    display:inline-flex;
                    align-items:center;
                    justify-content:center;
                    width:23px;
                    height:23px;
                    border-radius:50%;
                    background-color:${bgColor};
                    color:#fff;
                    font-weight:bold;
                    font-size:12px;
                    text-transform:uppercase;
                    margin-right:5px;
                    margin-left: -18px;
                `;

                let isChecked = false;
                if (filterData) {
                    const selectedUsers = filterData?.users?.find(u => u.type === 'individual')?.list || [];
                    isChecked = selectedUsers.some(u => u.id === userId);
                }

                let $row = $(`
                    <tr>
                        <td>
                            <div class="mt-0 align-middle form-check">
                                <input type="checkbox"
                                    class="form-check-input userCheckList"
                                    id="${userId}" name="${displayName}"
                                    ${isChecked ? 'checked' : ''}>
                            </div>
                        </td>
                        <td>
                            <span id="username" title="${initials}" style="${avatarStyle}">${initials}</span>
                            <span title="${userName}">${displayName}</span>
                        </td>
                        <td>${userEmail}</td>
                        <td>
                            <div>
                                <input type="checkbox" name="toggle_${userId}" id="SMSNotificationtoggle_${userId}">
                                <label for="SMSNotificationtoggle_${userId}"></label>
                            </div>
                        </td>
                    </tr>
                `);

                $table.find('tbody').append($row);
            });

            $container.append($table);
        }
    });
}
    function groupUser(filterData = null) {
    const userColorPallete = [
        '#00CCFF', '#0066FF', '#CC3399', '#FF9900', '#99CC00', '#3399FF', '#993399',
        '#339966', '#993333', '#009900', '#000099', '#666633', '#FF3300', '#6600CC',
        '#C65F00', '#009974', '#BEBE00', '#CC0000', '#9100C5', '#020057', '#949C3C',
        '#00CB82', '#418E8A', '#0099CC', '#3D50FF'
    ];

    $.ajax({
        type: "GET",
        url: "/Manage/EscalationMatrix/GetUserGroup",
        dataType: "json",
        success: function (data) {
            let $container = $('#userGroupListContainer');
            $container.empty();

            let $table = $('<table class="table-borderless table"><tbody></tbody></table>');

            $.each(data, function (index, user) {
                let userGroupName = user.groupName || 'Unnamed Group';
                let userEmail = user.email || 'NA';
                let userGroupId = user.id;

                let displayName = userGroupName.length > 21
                    ? userGroupName.substring(0, 21) + '...'
                    : userGroupName;
                const nameSplit = userGroupName.trim().split(' ');
                const initials = nameSplit.length > 1
                    ? (nameSplit[0][0] + nameSplit[1][0]).toUpperCase()
                    : nameSplit[0][0].toUpperCase();
                const colorIndex = index % userColorPallete.length;
                const bgColor = userColorPallete[colorIndex];

                const avatarStyle = `
                    display:inline-flex;
                    align-items:center;
                    justify-content:center;
                    width:23px;
                    height:23px;
                    border-radius:50%;
                    background-color:${bgColor};
                    color:#fff;
                    font-weight:bold;
                    font-size:12px;
                    text-transform:uppercase;
                    margin-right:5px;
                    margin-left:5px;
                `;

                let isChecked = false;
                if (filterData) {
                    const selectedGroups = filterData?.users?.find(u => u.type === 'group')?.list || [];
                    isChecked = selectedGroups.some(g => g.id === userGroupId);
                }

                let $row = $(`
                    <tr>
                        <td>
                            <div class="mt-0 align-middle form-check">
                                <input type="checkbox"
                                    class="form-check-input userCheckList"
                                    data-user-id="${userGroupId}"
                                    id="${userGroupId}"
                                    name="${displayName}"
                                    ${isChecked ? 'checked' : ''}>
                                <span style="${avatarStyle}" title="${initials}">${initials}</span>
                                <label class="form-check-label" title="${userGroupName}">${displayName}</label>
                            </div>
                        </td>                    
                        <td>${userEmail}</td>
                        <td>
                            <div>
                                <input type="checkbox"
                                    name="toggle_${userGroupId}"
                                    id="SMSNotificationtoggle_${userGroupId}">
                                <label for="SMSNotificationtoggle_${userGroupId}"></label>
                            </div>
                        </td>
                    </tr>
                `);

                $table.find('tbody').append($row);
            });

            $container.append($table);
        },
        error: function () {
            $('#userGroupListContainer').html('<p class="text-danger">Failed to load user groups.</p>');
        }
    });
    }
    function setDropdownValue(selector, value) {
        const $dropdown = $(selector);
        if ($dropdown.find(`option[value="${value}"]`).length === 0) {
            $dropdown.append(new Option(value, value));
        }
        $dropdown.val(value);
    }
    function populateModalFields(escData) {
    
        let levels;
        if (Array.isArray(escData.properties)) {
            levels = escData.properties;
    } else {
        try {            
            let cleanedJson = escData.properties;
            if (typeof cleanedJson === 'string' && cleanedJson.startsWith("'") && cleanedJson.endsWith("'")) {
                cleanedJson = cleanedJson.slice(1, -1);
            }
            levels = JSON.parse(cleanedJson);
        } catch (e) {
            console.error("Failed to parse escMatDesc:", e);
            levels = [];
        }
    }
    globallevelData = levels;
    $(".Escalation_Timeline ul").empty();
    const appendMatrix1 = (data, index) => {
        const borderColor = ['success', 'warning', 'primary', 'danger'][index % 4];
        return `
        <li class="li" id="${data.id}">
            <div class="Escalation_Timeline_Card card border-${borderColor}">
                <div class="d-flex align-items-center">
                    <span class="Timeline_Card_Level bg-${borderColor} badge bg-${borderColor}">
                        Level ${index}
                    </span>
                    <div class="d-grid ms-3">
                        <h6 class="mb-1 text-truncate" title="${data.description}">
                            ${data.name}
                        </h6>
                        <span class="d-none">
                            <img class="rounded-circle" src="" width="20" height="20">
                        </span>
                    </div>
                    <div class="d-flex ms-auto">
                        <span class="text-primary me-2 text-truncate">
                            <i class="cp-apply-finish-time"></i>
                            &nbsp;${data.Duration.value} ${data.Duration.type}
                        </span>
                        <i class="cp-edit fs-5 me-2 levelEdit" data-levelId='${data.id}'></i>
                        <i class="cp-Delete fs-5 me-2 levelDelete" data-levelName='${data.name}'></i>
                    </div>
                </div>
            </div>
        </li>`;
    };
    levels.forEach((level, index) => {
        const getDiagramHtml = appendMatrix1(level, index + 1);
        $(".Escalation_Timeline ul").prepend(getDiagramHtml);
    });
    OperationalServiceList();
    }

    function clearFields() {
        $("#escalationLevelName, #escalationLevelDescriptin, #escalationLevelTime").val('');
        $("#enNameEerror, #escHourError, #operationError").text('').removeClass('field-validation-error');
        $("#operationalService").empty();
        $('#userGroupListContainer .userCheckList:checked').prop('checked', false);
        $('#userListContainer .userCheckList:checked').prop('checked', false);
        OperationalServiceList();
    }

///Valaidation Function

    async function validatemscName(value, url) {
        const errorElement = $('#enNameEerror');

        if (!value) {
            errorElement.text('Enter Level Name')
                .addClass('field-validation-error');
            return false;
        }
        var url = RootUrl + eMatrixURL.Name;
        var data = {};
        data.TeamName = value;


        const validationResults = [
            await SpecialCharValidate(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithSpace(value),
            await OnlyNumericsValidate(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsTeamNameExist(url, data, OnError),           
        ];

        return await CommonValidation(errorElement, validationResults);
    }

    async function validaDateTime(value) {
        const errorElement = $('#escHourError');

        if (!value) {
            errorElement.text('Select Escalation Time')
                .addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true
        }
    }

    async function validaDateOperationalService() {
        const errorElement = $('#operationError');
        const e = document.getElementById("operationalService");
        if (!e) {
            errorElement.text('Operational Service element not found').addClass('field-validation-error');
            return false;
        }
        const selectedIndex = e.selectedIndex;
        if (selectedIndex === -1 || !e.options || !e.options[selectedIndex]) {
            errorElement.text('Select Operational Service Name').addClass('field-validation-error');
            return false;
        }
        const text = e.options[selectedIndex].text.trim();
        if (text === "") {
            errorElement.text('Select Operational Service Name').addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
 

    async function IsTeamNameExist(url, data, errorFunc) {
        const teamName = data.TeamName?.trim();
        if (!teamName) return true;

        const editingLevelId = $("#saveLevel").data('editing-levelid');

        const existsLocally = globallevelData.some(item =>
            item.name?.trim().toLowerCase() === teamName.toLowerCase() &&
            item.id !== editingLevelId
        );

        if (existsLocally) {
            return "Level Name already exists";
        }

        const existsOnServer = await GetAsync(url, data, errorFunc);
        return existsOnServer ? "Level Name already exists" : true;
    }


});


//$("#saveLevel").click(async function () {
//    const id = $('#textgrouplelId').val();
//    const levelName = $('#escalationLevelName').val();
//    const levelDesc = $('#escalationLevelDescriptin').val();
//    const levelTime = $("#escalationLevelTime").val();
//    const eseTimeUnit = $("#escalationTimeZone").val();
//    const opService = $("#operationalService option:Selected").text();
//    let selectedUserIds = [];
//    $('#userListContainer input[type="checkbox"][data-user-id]:checked').each(function () {
//        const userId = $(this).data('user-id');
//        if (userId) {
//            selectedUserIds.push(userId);
//        }
//    });
//    const selectedUserIdsString = selectedUserIds.join(',');
//    let selectedGroupIds = [];
//    $('#userGroupListContainer input[type="checkbox"][data-user-id]:checked').each(function () {
//        const groupId = $(this).data('user-id');
//        if (groupId) {
//            selectedGroupIds.push(groupId);
//        }
//    });
//    const selectedGroupIdsString = selectedGroupIds.join(',');
//    await $.ajax({
//        url: RootUrl + "Manage/EscalationMatrix/CreateOrUpdateEscLevel",
//        type: "POST",
//        dataType: "json",
//        data: {
//            'EscalationMatrixLevelViewModel.EscLevName': levelName,
//            'EscalationMatrixLevelViewModel.EscLevDescription': levelDesc,
//            'EscalationMatrixLevelViewModel.EscalationTime': levelTime,
//            'EscalationMatrixLevelViewModel.EscMatLevelResourceID': selectedUserIdsString,
//            'EscalationMatrixLevelViewModel.EscMatLevelTeamID': selectedGroupIdsString,
//            'EscalationMatrixLevelViewModel.EscalationTimeUnit': eseTimeUnit,
//            'EscalationMatrixLevelViewModel.EscMatrixID': escId,
//            'EscalationMatrixLevelViewModel.Id': id,
//            __RequestVerificationToken: gettoken()
//        },
//        success: function (result) {
//            if (result?.success) {
//                notificationAlert("success", result.data);
//                $('#LevelWizradModal').modal('hide');
//            } else {
//                errorNotification(result);
//            }
//        },
//    });
//    //$('#CreateForm').submit();

//});
//function individual(filterData = null) {
//    $.ajax({
//        type: "GET",
//        url: "/Manage/EscalationMatrix/GetUserNames",
//        dataType: "json",
//        success: function (data) {
//            let $container = $('#userListContainer');
//            $container.empty();

//            let $table = $('<table class="table-borderless table"><tbody></tbody></table>');

//            $.each(data, function (index, item) {
//                if (!item || !item.userInfo) return;

//                let user = item.userInfo;

//                let userImage = user.userimage || '/img/input_Icons/user-3.jpg';
//                let userName = user.userName || 'Unknown';
//                let userEmail = user.email || 'NA';
//                let userId = user.userId || `user_${index}`;
//                let displayName = userName.length > 21 ? userName.substring(0, 21) + '...' : userName;
//                let isChecked = false;
//                if (filterData) {
//                    const selectedUsers = filterData?.users?.find(u => u.type === 'individual')?.list || [];
//                    isChecked = selectedUsers.some(u => u.id === userId);
//                }

//                let $row = $(`
//                    <tr>
//                        <td>
//                            <div class="mt-0 align-middle form-check">
//                                <input type="checkbox"
//                                    class="form-check-input userCheckList"
//                                    id="${userId}" name="${displayName}"
//                                    ${isChecked ? 'checked' : ''}>
//                            </div>
//                        </td>
//                        <td>
//                            <img class="rounded-circle me-2"
//                                alt="userimg"
//                                src="${userImage}"
//                                width="30" height="30"
//                                title="User">
//                            <span title="${userName}">${displayName}</span>
//                        </td>
//                        <td>${userEmail}</td>
//                        <td>
//                            <div>
//                                <input type="checkbox" name="toggle_${userId}" id="SMSNotificationtoggle_${userId}">
//                                <label for="SMSNotificationtoggle_${userId}"></label>
//                            </div>
//                        </td>
//                    </tr>
//                `);

//                $table.find('tbody').append($row);
//            });

//            $container.append($table);
//        }
//    });
//}

//function groupUser(filterData = null) {
//    $.ajax({
//        type: "GET",
//        url: "/Manage/EscalationMatrix/GetUserGroup",
//        dataType: "json",
//        success: function (data) {
//            let $container = $('#userGroupListContainer');
//            $container.empty();

//            let $table = $('<table class="table-borderless table"><tbody></tbody></table>');

//            $.each(data, function (index, user) {
//                let userImage = user.userImage || '/img/input_Icons/user-3.jpg';
//                let userGroupName = user.groupName || 'Unnamed Group';
//                let userEmail = user.email || 'NA';
//                let userGroupId = user.id;

//                let displayName = userGroupName.length > 21
//                    ? userGroupName.substring(0, 21) + '...'
//                    : userGroupName;
//                let isChecked = false;
//                if (filterData) {
//                    const selectedGroups = filterData?.users?.find(u => u.type === 'group')?.list || [];
//                    isChecked = selectedGroups.some(g => g.id === userGroupId);
//                }

//                let $row = $(`
//                    <tr>
//                        <td>
//                            <div class="mt-0 align-middle form-check">
//                                <input type="checkbox"
//                                    class="form-check-input userCheckList"
//                                    data-user-id="${userGroupId}"
//                                    id="${userGroupId}"
//                                    name="${displayName}"
//                                    ${isChecked ? 'checked' : ''}>
//                                <img class="rounded-circle me-2"
//                                    alt="userimg"
//                                    src="${userImage}"
//                                    width="30" height="30">
//                                <label class="form-check-label" title="${userGroupName}">${displayName}</label>
//                            </div>
//                        </td>
//                        <td>${userEmail}</td>
//                        <td>
//                            <div>
//                                <input type="checkbox"
//                                    name="toggle_${userGroupId}"
//                                    id="SMSNotificationtoggle_${userGroupId}">
//                                <label for="SMSNotificationtoggle_${userGroupId}"></label>
//                            </div>
//                        </td>
//                    </tr>
//                `);

//                $table.find('tbody').append($row);
//            });

//            $container.append($table);
//        },
//        error: function () {
//            $('#userGroupListContainer').html('<p class="text-danger">Failed to load user groups.</p>');
//        }
//    });
//}