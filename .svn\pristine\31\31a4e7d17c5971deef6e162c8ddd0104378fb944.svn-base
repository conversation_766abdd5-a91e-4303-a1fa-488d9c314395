﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowCategoryViewRepository : BaseRepository<WorkflowCategoryView>, IWorkflowCategoryViewRepository
{

    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public WorkflowCategoryViewRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }


    public async Task<List<WorkflowCategoryView>> GetAllWorkflowCategoriesAsync()
    {
        var wfCategoryList = await _dbContext.WorkflowCategoryViews.ToListAsync();
        return wfCategoryList;


    }
}