﻿//using ContinuityPatrol.Application.Features.TableAccess.Commands.Update;

//namespace ContinuityPatrol.Core.UnitTests.Domains.TableAccess.Validators;

//public class UpdateTableAccessValidatorTests
//{
//    private readonly Mock<ITableAccessRepository> _mockTableAccessRepository;

//    public UpdateTableAccessValidatorTests()
//    {
//        var tableAccesses = new Fixture().Create<List<Domain.Entities.TableAccess>>();

//        _mockTableAccessRepository = TableAccessRepositoryMocks.UpdateTableAccessRepository(tableAccesses);
//    }

//    //TableName

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_TableName_InTableAccess_WithEmpty(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_TableName_InTableAccess_IsNull(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = null;

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameNotNullRequired, validateResult.Errors[1].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_TableName_InTableAccess_MinimumRange(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "DC";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameRangeRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_TableName_InTableAccess_MaximumRange(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXVUTSRQPONMLKJ";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameRangeRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_Valid_TableName_InTableAccess(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "  Accenture  ";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_Valid_TableName_InTableAccess_With_DoubleSpace_InFront(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "  Accenture";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_Valid_TableName_InTableAccess_With_DoubleSpace_InBack(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "Accenture  ";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_Valid_TableName_InTableAccess_With_TripleSpace_InBetween(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "CTS   India";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_Valid_TableName_InTableAccess_With_SpecialCharacters_InFront(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "*&^%CTS India";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_Valid_TableName_InTableAccess_With_SpecialCharacters_InBetween(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "CTS%$%&India";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_Valid_TableName_InTableAccess_With_SpecialCharacters_Only(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "&&^%$$#%&*";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_Valid_TableName_InTableAccess_With_UnderScore_InFront(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "_CTS";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_Valid_TableName_InTableAccess_With_UnderScore_InFront_AndBack(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "_CTS_";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_Valid_TableName_InTableAccess_With_Numbers_InFront(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "789CTS";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_Valid_TableName_InTableAccess_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "_789CTS_";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_Valid_TableName_InTableAccess_With_UnderScore_InFront_AndNumbers_InBack(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "_CTS877";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoTableAccessData]
//    public async Task Verify_Update_Valid_TableName_InTableAccess_With_Numbers_Only(UpdateTableAccessCommand updateTableAccessCommand)
//    {
//        var validator = new UpdateTableAccessCommandValidator(_mockTableAccessRepository.Object);

//        updateTableAccessCommand.TableName = "74525984268";

//        var validateResult = await validator.ValidateAsync(updateTableAccessCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.TableAccess.TableAccessTableNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }
//}