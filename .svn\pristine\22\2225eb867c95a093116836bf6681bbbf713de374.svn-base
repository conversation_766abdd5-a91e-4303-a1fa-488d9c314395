using ContinuityPatrol.Application.Features.PageBuilder.Commands.Create;
using ContinuityPatrol.Application.Features.PageBuilder.Commands.Delete;
using ContinuityPatrol.Application.Features.PageBuilder.Commands.Update;
using ContinuityPatrol.Application.Features.PageBuilder.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PageBuilder.Queries.GetList;
using ContinuityPatrol.Application.Features.PageBuilder.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.PageBuilder.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PageBuilderModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class PageBuilderService : BaseService, IPageBuilderService
{
    public PageBuilderService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<PageBuilderListVm>> GetPageBuilderList()
    {
        Logger.LogDebug("Get All PageBuilders");

        return await Mediator.Send(new GetPageBuilderListQuery());
    }

    public async Task<PageBuilderDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "PageBuilder Id");

        Logger.LogDebug($"Get PageBuilder Detail by Id '{id}'");

        return await Mediator.Send(new GetPageBuilderDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreatePageBuilderCommand createPageBuilderCommand)
    {
        Logger.LogDebug($"Create PageBuilder '{createPageBuilderCommand}'");

        return await Mediator.Send(createPageBuilderCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdatePageBuilderCommand updatePageBuilderCommand)
    {
        Logger.LogDebug($"Update PageBuilder '{updatePageBuilderCommand}'");

        return await Mediator.Send(updatePageBuilderCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "PageBuilder Id");

        Logger.LogDebug($"Delete PageBuilder Details by Id '{id}'");

        return await Mediator.Send(new DeletePageBuilderCommand { Id = id });
    }

    #region NameExist

    public async Task<bool> IsPageBuilderNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "PageBuilder Name");

        Logger.LogDebug($"Check Name Exists Detail by PageBuilder Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetPageBuilderNameUniqueQuery { Name = name, Id = id });
    }

    #endregion

    #region Paginated

    public async Task<PaginatedResult<PageBuilderListVm>> GetPaginatedPageBuilders(
        GetPageBuilderPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in PageBuilder Paginated List");

        return await Mediator.Send(query);
    }

    #endregion
}