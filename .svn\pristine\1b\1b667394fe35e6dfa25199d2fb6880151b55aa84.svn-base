﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionTypeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.WorkflowActionType.Queries.GetPaginatedList;

public class GetWorkflowActionTypePaginatedListQueryHandler :IRequestHandler<GetWorkflowActionTypePaginatedListQuery , PaginatedResult<WorkflowActionTypeListVm>>
{
    private readonly IMapper _mapper;

    private readonly IWorkflowActionTypeRepository _workflowActionTypeRepository;
    public GetWorkflowActionTypePaginatedListQueryHandler(IMapper mapper,IWorkflowActionTypeRepository workflowActionTypeRepository)
    {
        _mapper = mapper;
        _workflowActionTypeRepository = workflowActionTypeRepository;
    }

    public async Task<PaginatedResult<WorkflowActionTypeListVm>> Handle(GetWorkflowActionTypePaginatedListQuery request,
       CancellationToken cancellationToken)
    {
        var specification = new WorkflowActionTypeFilterSpecification(request.SearchString);

        var queryable =await _workflowActionTypeRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,specification, request.SortColumn, request.SortOrder);

        var actionType = _mapper.Map<PaginatedResult<WorkflowActionTypeListVm>>(queryable);

        return actionType;
    }
}
