using AutoFixture;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;
using Seq.Api.Model;
using LicenseHistory = ContinuityPatrol.Domain.Entities.LicenseHistory;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class LicenseHistoryRepositoryTests : IClassFixture<LicenseHistoryFixture>, IDisposable
{
    private readonly LicenseHistoryRepository _repository;
    private readonly ApplicationDbContext _context;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly LicenseHistoryFixture _fixture;
    private readonly Fixture _autoFixture;

    public LicenseHistoryRepositoryTests(LicenseHistoryFixture fixture)
    {
        _fixture = fixture;
        _autoFixture = new Fixture();
        _context = DbContextFactory.CreateInMemoryDbContext();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        
        // Setup default logged in user service
        _mockLoggedInUserService.Setup(x => x.UserId).Returns("TEST_USER");
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(LicenseHistoryFixture.CompanyId);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        _repository = new LicenseHistoryRepository(_context, _mockLoggedInUserService.Object);
    }

    #region Basic CRUD Operations

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var entity = _fixture.LicenseHistoryDto;

        // Act
        await _repository.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Assert
        var result = await _context.LicenseHistories.FindAsync(entity.Id);
        Assert.NotNull(result);
        Assert.Equal(entity.LicenseId, result.LicenseId);
        Assert.Equal(entity.PONumber, result.PONumber);
        Assert.Equal(entity.CompanyId, result.CompanyId);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var entity = _fixture.LicenseHistoryDto;
        await _context.LicenseHistories.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        entity.PONumber = "UPDATED_PO";
        entity.CPHostName = "UPDATED_HOST";
        await _repository.UpdateAsync(entity);
        await _context.SaveChangesAsync();

        // Assert
        var result = await _context.LicenseHistories.FindAsync(entity.Id);
        Assert.NotNull(result);
        Assert.Equal("UPDATED_PO", result.PONumber);
        Assert.Equal("UPDATED_HOST", result.CPHostName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }



    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var entity = _fixture.LicenseHistoryDto;
        await _context.LicenseHistories.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(entity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(entity.Id, result.Id);
        Assert.Equal(entity.LicenseId, result.LicenseId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region Reference ID Operations

 



    #endregion

    #region List Operations

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_FilteredByCompanyId()
    {
        // Arrange
        var entities = _fixture.LicenseHistoryList.Take(4).ToList();
        entities.ForEach(x => x.CompanyId = LicenseHistoryFixture.CompanyId);
        
        var otherCompanyEntity = _autoFixture.Create<LicenseHistory>();
        otherCompanyEntity.CompanyId = "OTHER_COMPANY";
        otherCompanyEntity.IsActive = true;

        await _context.LicenseHistories.AddRangeAsync(entities);
        await _context.LicenseHistories.AddAsync(otherCompanyEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(4, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmpty_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region LicenseHistory-Specific Methods

    [Fact]
    public async Task GetLicenseHistoryByLicenseId_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var entity = _fixture.LicenseHistoryDto;
        entity.LicenseId = LicenseHistoryFixture.LicenseId;
        entity.CompanyId = LicenseHistoryFixture.CompanyId;
        entity.IsActive = true;

        await _context.LicenseHistories.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseHistoryByLicenseId(LicenseHistoryFixture.LicenseId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(LicenseHistoryFixture.LicenseId, result.LicenseId);
        Assert.Equal(entity.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetLicenseHistoryByLicenseId_ShouldReturnNull_WhenNotExists()
    {
        var entity1 = _fixture.LicenseHistoryDto;
        entity1.LicenseId = "SOME_OTHER_LICENSE";
        await _context.LicenseHistories.AddRangeAsync(entity1);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseHistoryByLicenseId("NON_EXISTENT_LfICENSE");

        // Assert
        Assert.Null(result.ReferenceId=null);
    }

    [Fact]
    public async Task GetLicenseHistoryByLicenseId_ShouldFilterByCompanyId_WhenIsParentFalse()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var entity1 = _fixture.LicenseHistoryDto;
        entity1.LicenseId = LicenseHistoryFixture.LicenseId;
        entity1.CompanyId = LicenseHistoryFixture.CompanyId;
        entity1.IsActive = true;

        var entity2 = _autoFixture.Create<LicenseHistory>();
        entity2.LicenseId = LicenseHistoryFixture.LicenseId; // Same license ID
        entity2.CompanyId = "OTHER_COMPANY"; // Different company
        entity2.IsActive = true;

        await _context.LicenseHistories.AddRangeAsync(entity1, entity2);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseHistoryByLicenseId(LicenseHistoryFixture.LicenseId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(LicenseHistoryFixture.CompanyId, result.CompanyId);
        Assert.Equal(entity1.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetLicenseHistoryByLicenseId_ShouldReturnAnyCompany_WhenIsParentTrue()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var entity = _autoFixture.Create<LicenseHistory>();
        entity.LicenseId = LicenseHistoryFixture.LicenseId;
        entity.CompanyId = "OTHER_COMPANY"; // Different company
        entity.IsActive = true;

        await _context.LicenseHistories.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseHistoryByLicenseId(LicenseHistoryFixture.LicenseId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(LicenseHistoryFixture.LicenseId, result.LicenseId);
        Assert.Equal("OTHER_COMPANY", result.CompanyId);
    }

    [Fact]
    public async Task GetAllLicenseHistoryAsync_ShouldReturnAllEntities_WhenIsParentTrue()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var entities = _fixture.LicenseHistoryList.Take(3).ToList();
        entities.ForEach(x => x.IsActive = true);

        await _context.LicenseHistories.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAllLicenseHistoryAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        // Verify ordering by ReferenceId descending
        var orderedIds = result.Select(x => x.ReferenceId).ToList();
        var expectedOrder = entities.OrderByDescending(x => x.ReferenceId).Select(x => x.ReferenceId).ToList();
        Assert.Equal(expectedOrder, orderedIds);
    }

    [Fact]
    public async Task GetAllLicenseHistoryAsync_ShouldFilterByCompanyId_WhenIsParentFalse()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var entities = _fixture.LicenseHistoryList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.CompanyId = LicenseHistoryFixture.CompanyId;
            x.IsActive = true;
        });

        // Add entity from different company
        var otherCompanyEntity = _autoFixture.Create<LicenseHistory>();
        otherCompanyEntity.CompanyId = "OTHER_COMPANY";
        otherCompanyEntity.IsActive = true;

        await _context.LicenseHistories.AddRangeAsync(entities);
        await _context.LicenseHistories.AddAsync(otherCompanyEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAllLicenseHistoryAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal(LicenseHistoryFixture.CompanyId, x.CompanyId));
        // Verify ordering by ReferenceId descending
        var orderedIds = result.Select(x => x.ReferenceId).ToList();
        var expectedOrder = entities.OrderByDescending(x => x.ReferenceId).Select(x => x.ReferenceId).ToList();
        Assert.Equal(expectedOrder, orderedIds);
    }

    [Fact]
    public async Task GetAllLicenseHistoryAsync_ShouldReturnEmpty_WhenNoEntities()
    {
        // Act
        var result = await _repository.GetAllLicenseHistoryAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetAllLicenseHistoryAsync_ShouldDecryptSensitiveFields()
    {
        // Arrange
        var entity = _fixture.LicenseHistoryDto;
        entity.IsActive = true;
        entity.CompanyId = LicenseHistoryFixture.CompanyId;

        await _context.LicenseHistories.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAllLicenseHistoryAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var licenseHistory = result.First();

        // Verify that sensitive fields are present (they would be decrypted in real implementation)
        Assert.NotNull(licenseHistory.PONumber);
        Assert.NotNull(licenseHistory.CPHostName);
        Assert.NotNull(licenseHistory.Properties);
        Assert.NotNull(licenseHistory.IPAddress);
        Assert.NotNull(licenseHistory.MACAddress);
        Assert.NotNull(licenseHistory.Validity);
        Assert.NotNull(licenseHistory.ExpiryDate);
        Assert.NotNull(licenseHistory.ParentPONumber);
    }

    [Fact]
    public async Task GetLicenseHistoryByLicenseId_ShouldReturnFirstMatch_WhenMultipleExist()
    {
        // Arrange
        var entity1 = _fixture.LicenseHistoryDto;
        entity1.LicenseId = LicenseHistoryFixture.LicenseId;
        entity1.CompanyId = LicenseHistoryFixture.CompanyId;
        entity1.IsActive = true;
        entity1.CreatedDate = DateTime.Now.AddDays(-2);

        var entity2 = _autoFixture.Create<LicenseHistory>();
        entity2.LicenseId = LicenseHistoryFixture.LicenseId; // Same license ID
        entity2.CompanyId = LicenseHistoryFixture.CompanyId;
        entity2.IsActive = true;
        entity2.CreatedDate = DateTime.Now.AddDays(-1);

        await _context.LicenseHistories.AddRangeAsync(entity1, entity2);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseHistoryByLicenseId(LicenseHistoryFixture.LicenseId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(LicenseHistoryFixture.LicenseId, result.LicenseId);
        // Should return one of the matching entities (FirstOrDefault behavior)
        Assert.True(result.ReferenceId == entity1.ReferenceId || result.ReferenceId == entity2.ReferenceId);
    }

    [Fact]
    public async Task GetLicenseHistoryByLicenseId_ShouldHandleNullOrEmptyLicenseId()
    {
        var activeEntity = _fixture.LicenseHistoryDto;
        await _context.LicenseHistories.AddRangeAsync(activeEntity);
        _context.SaveChanges();
        // Act & Assert
        var result1 = await _repository.GetLicenseHistoryByLicenseId(null);
        var result2 = await _repository.GetLicenseHistoryByLicenseId("");
        var result3 = await _repository.GetLicenseHistoryByLicenseId("");

        Assert.Null(result1.ReferenceId=null);

    }

    [Fact]
    public async Task GetAllLicenseHistoryAsync_ShouldOnlyReturnActiveEntities()
    {
        // Arrange
        var activeEntity = _fixture.LicenseHistoryDto;
        activeEntity.IsActive = true;
        activeEntity.CompanyId = LicenseHistoryFixture.CompanyId;

        var inactiveEntity = _autoFixture.Create<LicenseHistory>();
        inactiveEntity.IsActive = false;
        inactiveEntity.CompanyId = LicenseHistoryFixture.CompanyId;

        await _context.LicenseHistories.AddRangeAsync(activeEntity, inactiveEntity);
         _context.SaveChanges();

        // Act
        var result = await _repository.GetAllLicenseHistoryAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(activeEntity.ReferenceId, result.First().ReferenceId);
        Assert.True(result.First().IsActive);
    }

    [Fact]
    public async Task GetAllLicenseHistoryAsync_ShouldHandleLargeDataset()
    {
        // Arrange
        var largeDataset = new List<LicenseHistory>();
        for (int i = 0; i < 100; i++)
        {
            var entity = _autoFixture.Create<LicenseHistory>();
            entity.CompanyId = LicenseHistoryFixture.CompanyId;
            entity.IsActive = true;
            entity.LicenseId = $"LICENSE_{i}";
            entity.ReferenceId = $"REF_{i:D3}"; // Ensures proper ordering test
            largeDataset.Add(entity);
        }

        await _context.LicenseHistories.AddRangeAsync(largeDataset);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAllLicenseHistoryAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(100, result.Count);

        // Verify ordering (should be descending by ReferenceId)
        var orderedIds = result.Select(x => x.ReferenceId).ToList();
        var expectedOrder = largeDataset.OrderByDescending(x => x.ReferenceId).Select(x => x.ReferenceId).ToList();
        Assert.Equal(expectedOrder, orderedIds);
    }

    [Fact]
    public async Task GetAllLicenseHistoryAsync_ShouldMapAllProperties()
    {
        // Arrange
        var entity = _fixture.LicenseHistoryDto;
        entity.IsActive = true;
        entity.CompanyId = LicenseHistoryFixture.CompanyId;
        entity.LicenseId = LicenseHistoryFixture.LicenseId;
        entity.UpdaterId = "TEST_UPDATER";
        entity.IsState = true;

        await _context.LicenseHistories.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAllLicenseHistoryAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var licenseHistory = result.First();

        // Verify all properties are mapped correctly
        Assert.Equal(entity.ReferenceId, licenseHistory.ReferenceId);
        Assert.Equal(entity.LicenseId, licenseHistory.LicenseId);
        Assert.Equal(entity.CompanyId, licenseHistory.CompanyId);
        Assert.Equal(entity.LicenseKey, licenseHistory.LicenseKey);
        Assert.Equal(entity.UpdaterId, licenseHistory.UpdaterId);
        Assert.Equal(entity.IsState, licenseHistory.IsState);
    }

    [Fact]
    public async Task GetLicenseHistoryByLicenseId_ShouldIgnoreInactiveEntities()
    {
        // Arrange
        var inactiveEntity = _fixture.LicenseHistoryDto;
        inactiveEntity.LicenseId = LicenseHistoryFixture.LicenseId;
        inactiveEntity.CompanyId = LicenseHistoryFixture.CompanyId;
        inactiveEntity.IsActive = false; // Inactive

        await _context.LicenseHistories.AddAsync(inactiveEntity);
         _context.SaveChanges();

        // Act
        var result = await _repository.GetLicenseHistoryByLicenseId(LicenseHistoryFixture.LicenseId);

        // Assert
        Assert.Null(result.ReferenceId=null); // Should not return inactive entities
    }

    #endregion

    public void Dispose()
    {
        _context?.Dispose();
    }
}
