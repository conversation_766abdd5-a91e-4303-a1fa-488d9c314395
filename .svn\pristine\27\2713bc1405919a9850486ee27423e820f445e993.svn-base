﻿$(function () {

    var createPermission = $("#adminCreate").data("create-permission").toLowerCase();
    if (createPermission == 'false') {
        $(".btnSaveFunction").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
    }

    $(".nav-link").on("click", function () {
        localStorage.setItem("id", $(".nav .nav-link.active").attr("id"))
        let activeSkey = $("#v-pills-tab .nav-link.active").text().trim()
        if (activeSkey !== 'Report') {
            $("#v-pills-home").hide();
        } else {
            $("#v-pills-home").show()
        }
    });

    $(".btnSaveFunction").on("click", async function () {
        localStorage.setItem("id", $(".nav .nav-link.active").attr("id"))
        let type = $(this).data('type')  
        let typeWithoutSpaces = type.replace(/\s+/g, '');
        let loginId = $("input[data-loginid]").attr("data-loginid");       
        let sKey = $("#txt" + typeWithoutSpaces + "SKey").val() || type; 
        let id = $("#txt" + typeWithoutSpaces + "Id").val();
        let sValue 
        if (type === "Password Policy") {
            let passwordPolicy = {
                minSValue: $('#txtMinPwdPlcy').val(),
                minUpSValue: $('#txtMinUpperPwdPlcy').val(),
                minNumSValue: $('#txtMinNumPwdPlcy').val(),
                maxSValue: $('#txtMaxPwdPlcy').val(),
                minLowSValue: $('#txtMinLowerPwdPlcy').val(),
                minSpclSValue: $('#txtMinSpclPwdPlcy').val(), 
            };
            sValue = JSON.stringify(passwordPolicy);
        }
        else if (type === "Log") {
            let log = {
                UI: $("#txtLog1SValue").val(),
                Loadbalancer: $('#txtLog4SValue').val(),
                Monitor: $("#txtLog2SValue").val(),
                Workflow: $("#txtLog3SValue").val(),
                ResiliencyReady: $('#txtLog5SValue').val()
            };
            sValue = JSON.stringify(log)
        }
        else {
            sValue = $("#txt" + typeWithoutSpaces + "SValue").val();
        }
        let sValueInput = $("#txt" + typeWithoutSpaces + "SValue");
        if (sValueInput.length) {
            sValueInput.val(sValue);
        } else {
            $("#Create" + typeWithoutSpaces + "Form").append(
                `<input type="hidden" id="txt${typeWithoutSpaces}SValue" name="SValue" value='${sValue}'>`
            );
        }
        $("#txt" + typeWithoutSpaces + "SKey").val(sKey);        
        $("#txt" + typeWithoutSpaces + "LoginUser").val(loginId);
        $("#txt" + typeWithoutSpaces + "Id").val(id);
        //console.log("Saving " + type + " Data:", { sKey, id, loginId, sValue });
        let isValid = await validateSValue(type, sValue);
        if (sKey && loginId && sValue && isValid) {
            let form = $('#Create' + typeWithoutSpaces);
            form.trigger('submit')
        }
        return false;
    })
    $(document).on("change", "input[type=radio]", function () {
        let selectedValue = $(this).val();
        $("#txtADUserSKey").val("AD Configuration");
        $("#txtADUserSValue").val(selectedValue);
        let loginId = $('#txtADUserLoginUser').data("loginid");
        $('#txtADUserLoginUser').val(loginId);
        $("#CreateADUserForm").trigger("submit");
    });
    $(document).on("change", "#chk-activity", function () {
        $("#txtActivitySKey").val("Load Balancer");
        let loginId = $('#txtActivityLoginUser').data("loginid");
        $('#txtActivityLoginUser').val(loginId);
        $('#chk-activity').is(':checked');
        $('#txtActivityId').val();
        $('#CreateLoadBalancerForm').trigger('submit');
    })
    
    //Validations
    async function validateSValue(type, sValue) {
        if (type === 'Report') {
            return await validateReportSvalue(sValue)
        } else if (type === 'Password Age') {
            return await validatePasswordAgeSvalue(sValue)
        } else if (type === 'Log') {
            let log1Value = $("#txtLog1SValue").val();
            let log2Value = $("#txtLog2SValue").val();
            let log3Value = $("#txtLog3SValue").val();
            let log4Value = $("#txtLog4SValue").val();
            let log5Value = $("#txtLog5SValue").val();

            let log1Valid = await validateLog1Svalue(log1Value);
            let log2Valid = await validateLog2Svalue(log2Value);
            let log3Valid = await validateLog3Svalue(log3Value);
            let log4Valid = await validateLog4Svalue(log4Value);
            let log5Valid = await validateLog5Svalue(log5Value);

            return log1Valid && log2Valid && log3Valid && log4Valid && log5Valid;
        } else if (type === 'Password Policy') {
            let minValue = $("#txtMinPwdPlcy").val();
            let minUpperValue = $("#txtMinUpperPwdPlcy").val();
            let minNumValue = $("#txtMinNumPwdPlcy").val();
            let maxValue = $("#txtMaxPwdPlcy").val();
            let minLowerValue = $("#txtMinLowerPwdPlcy").val();
            let minSpclValue = $("#txtMinSpclPwdPlcy").val();

            let minValid = await validateMinLengthSvalue(minValue);
            let minUpperValid = await validateMinUpperLengthSvalue(minUpperValue);
            let minNumValid = await validateMinNumericLengthSvalue(minNumValue);
            let maxValid = await validateMaxLengthSvalue(maxValue);
            let minLowerValid = await validateMinLowerSvalue(minLowerValue);
            let minSpclValid = await validateMinSpecialSvalue(minSpclValue);

            return minValid && minUpperValid && minNumValid && maxValid && minLowerValid && minSpclValid;
        }

        return true;
    }
    const exceptThisSymbols = ["e", "E", "+", "-", "."];
    $(document).on('keyup keypress paste', async function (event) {
        
        let input = $(event.target);
        let value
        let type = input.data('type');
        if (type === "Report") {
            value = sanitizeInput(event, input);
            await validateReportSvalue(value);
        }
        if (type === 'Password Age') {
            
            restrictSymbols(event)
            value = sanitizeInput(event, input);
            await validatePasswordAgeSvalue(value);
        }
        if (type === 'Log') {
            value = sanitizeInput(event, input);
            switch (input.attr('id')) {
                case "txtLog1SValue":
                    await validateLog1Svalue(value);
                    break;
                case "txtLog2SValue":
                    await validateLog2Svalue(value);
                    break;
                case "txtLog3SValue":
                    await validateLog3Svalue(value);
                    break;
                case "txtLog4SValue":
                    await validateLog4Svalue(value);
                    break;
                case "txtLog5SValue":
                    await validateLog5Svalue(value);
                    break;
            }
        }
        if (type === 'Password Policy') {
            restrictSymbols(event)
            validatePasswordPolicy();
            value = sanitizeInput(event, input);
            switch (input.attr('id')) {
                case "txtMinPwdPlcy":
                    await validateMinLengthSvalue(value);
                    break;
                case "txtMinUpperPwdPlcy":
                    await validateMinUpperLengthSvalue(value);
                    break;
                case "txtMinNumPwdPlcy":
                    await validateMinNumericLengthSvalue(value);
                    break;
                case "txtMaxPwdPlcy":
                    await validateMaxLengthSvalue(value);
                    break;
                case "txtMinLowerPwdPlcy":
                    await validateMinLowerSvalue(value);
                    break;
                case "txtMinSpclPwdPlcy":
                    await validateMinSpecialSvalue(value);
                    break;
            }
        }
    })
    function validatePasswordPolicy() {
        let total = Number($('#txtMinSpclPwdPlcy').val()) +
            Number($('#txtMinLowerPwdPlcy').val()) +
            Number($('#txtMinUpperPwdPlcy').val()) +
            Number($('#txtMinNumPwdPlcy').val());

        let maxAllowed = Number($('#txtMaxPwdPlcy').val());

        if (total >= maxAllowed) {
            const errorElements = ['#MinLower-error', '#MaxLength-error', '#MinNumber-error', '#MinUpper-error', '#MinLength-error', '#MinSpecialChar-error']
            errorElements.forEach(element => {
                $(element).text('').removeClass('field-validation-error');
        });
        }
    }
    $("#txtMinUpperPwdPlcy, #txtMinNumPwdPlcy, #txtMinLowerPwdPlcy, #txtMinSpclPwdPlcy, #txtMaxPwdPlcy")
        .on("keyup", function () {
            validatePasswordPolicy();
        });
    
    function restrictSymbols(event) {
        let input = $(event.target);
        let type = input.data('type');

        const restrictedTypes = ["Password Age", "Password Policy"];

        if (restrictedTypes.includes(type)) {
            if (!/[+0-9-]/.test(event.key) || exceptThisSymbols.includes(event.key) || event.type === 'paste') {
                event.preventDefault();
            }
        }
    }
    function sanitizeInput(event, input) {
        if (event.keyCode === 32) {
            event.preventDefault();
        }
        let value = input.val();
        if (value) {
            input.val(value.replace(/  +/g, " "));
        }
        return value;
    }
    //Report Validation
    async function validateReportSvalue(value) {
        const errorElement = $('#Report-error');

        if (!value) {
            errorElement.text('Enter report path').addClass('field-validation-error')
            return false;
        }
        const validationResults = [
            await InvalidPathRegex(value)
        ]

        return CommonValidation(errorElement, validationResults);
    }
    //Password Age Validation
    async function validatePasswordAgeSvalue(value) {
        const errorElement = $('#PasswordAge-error');

        if (!value) {
            errorElement.text('Enter password age')
                .addClass('field-validation-error')
            return false;
        }
        const validationResults = [
            await zeroValue(value)
        ];

        return await CommonValidation(errorElement, validationResults);
    }
    //Log Validation
    async function validateLog5Svalue(value) {
        const errorElement = $('#Log5-error');
        if (!value) {
            errorElement.text('Enter resiliency readiness service')
                .addClass('field-validation-error')
            return false;
        }
        const validationResults = [
            await InvalidPathRegex(value),
        ];
        return await CommonValidation(errorElement, validationResults);
    }
    async function validateLog4Svalue(value) {
        const errorElement = $('#Log4-error');
        if (!value) {
            errorElement.text('Enter load balancer')
                .addClass('field-validation-error')
            return false;
        }
        const validationResults = [
            await InvalidPathRegex(value),
        ];
        return await CommonValidation(errorElement, validationResults);
    }
    async function validateLog3Svalue(value) {
        const errorElement = $('#Log3-error');
        if (!value) {
            errorElement.text('Enter workflow service')
                .addClass('field-validation-error')
            return false;
        }
        const validationResults = [
            await InvalidPathRegex(value),
        ];
        return await CommonValidation(errorElement, validationResults);
    }

    async function validateLog2Svalue(value) {
        const errorElement = $('#Log2-error');
        if (!value) {
            errorElement.text('Enter monitor service')
                .addClass('field-validation-error')
            return false;
        }
        const validationResults = [

            await InvalidPathRegex(value),
        ];
        return await CommonValidation(errorElement, validationResults);
    }
    async function validateLog1Svalue(value) {
        const errorElement = $('#Log1-error');
        if (!value) {
            errorElement.text('Enter log path')
                .addClass('field-validation-error')
            return false;
        }
        const validationResults = [

            await InvalidPathRegex(value),
        ];
        return await CommonValidation(errorElement, validationResults);
    }
    //Password Policy Validation
    let maxValue = 0;
    let totalValue = 0;
    async function validateMinSpecialSvalue(value) {
        const errorElement = $('#MinSpecialChar-error');
        maxValue = $('#txtMaxPwdPlcy').val();
        totalValue = Number($('#txtMinSpclPwdPlcy').val()) + Number($('#txtMinLowerPwdPlcy').val()) + Number($('#txtMinUpperPwdPlcy').val()) + Number($('#txtMinNumPwdPlcy').val())

        if (!value) {
            errorElement.text('Enter minimum special characters')
                .addClass('field-validation-error')
            return false;
        }

        if (totalValue > maxValue) {
            errorElement.text("Should not exceed maximum password length")
                .addClass('field-validation-error')
            return false;
        }
        const validationResults = [
            await zeroValue(value)
        ];
        return await CommonValidation(errorElement, validationResults);
    }
    async function validateMinLowerSvalue
        (value) {
        const errorElement = $('#MinLower-error');
        maxValue = $('#txtMaxPwdPlcy').val();
        totalValue = Number($('#txtMinNumPwdPlcy').val()) + Number($('#txtMinUpperPwdPlcy').val()) + Number($('#txtMinSpclPwdPlcy').val()) + Number($('#txtMinNumPwdPlcy').val())
        if (!value) {
            errorElement.text('Enter minimum lowercase characters')
                .addClass('field-validation-error')
            return false;
        }

        if (totalValue > maxValue) {
            errorElement.text("Should not exceed maximum password length")
                .addClass('field-validation-error')
            return false;
        }
        const validationResults = [
            await zeroValue(value)
        ];
        return await CommonValidation(errorElement, validationResults);
    }

    async function validateMaxLengthSvalue(value) {
        const errorElement = $('#MaxLength-error');
        if (!value) {
            errorElement.text('Enter maximum password length')
                .addClass('field-validation-error')
            return false;
        }
        const minPwd = parseInt($('#txtMinPwdPlcy').val())
        if (minPwd >= parseInt(value)) {
            errorElement.text('Should be more than minimum password length')
                .addClass('field-validation-error')
            return false;
        } if (Number(value) > 19) {
            errorElement.text("Enter value less than 20");
            errorElement.addClass('field-validation-error');
            return false;
        }
        const validationResults = [
            await zeroValue(value)
        ];
        return await CommonValidation(errorElement, validationResults);
    }
    async function validateMinNumericLengthSvalue(value) {
        const errorElement = $('#MinNumber-error');
        maxValue = $('#txtMaxPwdPlcy').val();
        totalValue = Number($('#txtMinNumPwdPlcy').val()) + Number($('#txtMinLowerPwdPlcy').val()) + Number($('#txtMinSpclPwdPlcy').val()) + Number($('#txtMinUpperPwdPlcy').val());

        if (!value) {
            errorElement.text('Enter minimum numeric characters')
                .addClass('field-validation-error')
            return false;
        }

        if (totalValue > maxValue) {
            errorElement.text("Should not exceed maximum password length")
                .addClass('field-validation-error')
            return false;
        }
        const validationResults = [
            await zeroValue(value)
        ];
        return await CommonValidation(errorElement, validationResults);
    }

    async function validateMinUpperLengthSvalue(value) {
        maxValue = $('#txtMaxPwdPlcy').val();
        totalValue = Number($('#txtMinUpperPwdPlcy').val()) + Number($('#txtMinLowerPwdPlcy').val()) + Number($('#txtMinSpclPwdPlcy').val()) + Number($('#txtMinNumPwdPlcy').val())
        const errorElement = $('#MinUpper-error');
        if (!value) {
            errorElement.text('Enter minimum uppercase characters')
                .addClass('field-validation-error')
            return false;
        }

        if (totalValue > maxValue) {
            errorElement.text("Should not exceed maximum password length")
                .addClass('field-validation-error')
            return false;
        }
        const validationResults = [
            await zeroValue(value)
        ];
        return await CommonValidation(errorElement, validationResults);
    }
    async function validateMinLengthSvalue(value) {
        const errorElement = $('#MinLength-error');
        if (!value) {
            errorElement.text('Enter minimum password length')
                .addClass('field-validation-error')
            return false;
        } if (Number(value) < 8) {
            errorElement.text("Enter value minimum 8");
            errorElement.addClass('field-validation-error');
            return false;
        }
        const validationResults = [
            await zeroValue(value)
        ];
        return await CommonValidation(errorElement, validationResults);
    }
})