﻿using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLAlwaysOnMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Manage;

public class MssqlAlwaysOnMonitorStatusService : BaseService, IMssqlAlwaysOnMonitorStatusService
{
    public MssqlAlwaysOnMonitorStatusService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(
        CreateMSSQLAlwaysOnMonitorStatusCommand createMssqlAlwaysOnMonitorStatusCommand)
    {
        Logger.LogDebug(
            $"Create MSSQLAlwaysOnMonitorStatus '{createMssqlAlwaysOnMonitorStatusCommand.InfraObjectName}'");

        return await Mediator.Send(createMssqlAlwaysOnMonitorStatusCommand);
    }

    public async Task<List<MSSQLAlwaysOnMonitorStatusListVm>> GetAllMSSQLAlwaysOnMonitorStatus()
    {
        Logger.LogDebug("Get All MSSQLAlwaysOnMonitorStatus");

        return await Mediator.Send(new GetMSSQLAlwaysOnMonitorStatusListQuery());
    }

    public async Task<MSSQLAlwaysOnMonitorStatusDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "MSSQLAlwaysOnMonitorStatusById");

        Logger.LogDebug($"Get MSSQLAlwaysOnMonitorStatus Detail By Id '{id}' ");

        return await Mediator.Send(new GetMSSQLAlwaysOnMonitorStatusDetailQuery { Id = id });
    }

    public async Task<List<MSSQLAlwaysOnMonitorStatusDetailByTypeVm>> GetMSSQLAlwaysOnMonitorStatusByType(string type)
    {
        Guard.Against.InvalidGuidOrEmpty(type, "MSSQLAlwaysOnMonitorStatus Type");

        Logger.LogDebug($"Get MSSQLAlwaysOnMonitorStatus Detail by Type '{type}'");

        return await Mediator.Send(new GetMSSQLAlwaysOnMonitorStatusDetailByTypeQuery { Type = type });
    }

    public async Task<PaginatedResult<MSSQLAlwaysOnMonitorStatusListVm>> GetPaginatedMSSQLAlwaysOnMonitorStatus(
        GetMSSQLAlwaysOnMonitorStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in MSSQLAlwaysOnMonitorStatus Paginated List");

        return await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllMSSQLAlwaysOnMonitorStatusCacheKey,
            () => Mediator.Send(query));
    }
}