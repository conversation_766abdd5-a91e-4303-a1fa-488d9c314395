const siteLocationURL = {
    SiteLocationExistUrl :'Configuration/SiteLocation/IsSiteLocationExist',
    SiteLocationPaginatedUrl : "/Configuration/SiteLocation/GetPaginated"
}
let createPermission = $("#configurationCreate").data("create-permission")?.toLowerCase();
let deletePermission = $("#configurationDelete").data("delete-permission")?.toLowerCase();
let SiteLocation = '';
let exceptThisSymbol = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "_", "+", "=", "{", "}", "[", "]", "|", ":", ";", "'", "\"", "<", ">", "?", "/", "\\"];
let regex = new RegExp('[' + exceptThisSymbol.join('\\') + ']|\\.(?=.*\\.)');

if (createPermission == 'false') {
    $("#create").removeClass('#create').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
}

$(function () {

    let selectedValues = [];
    let dataTable = $('#siteLocationTable').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }
                , infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": siteLocationURL.SiteLocationPaginatedUrl,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "city" : sortIndex === 2 ? "country" : sortIndex === 3 ? "lat" :
                        sortIndex === 4 ? "lng" : sortIndex === 5 ? "status" : "";
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {

                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data;
                }
            },
            "columnDefs": [
                {
                    "targets": [1,2],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    orderable: false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }                    
                        return data;
                    },
                    orderable: false
                },

                {
                    "data": "city", "name": "City Name", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<td><span title="${row.city}" > ${row.city|| 'NA'}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "country", "name": "Country Name", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<td><span title="${row.country}"> ${row.country||"NA"}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "lat", "name": "Latitude", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<td><span  > ${row.lat||'NA'}</span></td>`
                        }
                        return data;
                    }
                }, {
                    "data": "lng", "name": "Longitude", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<td><span  > ${row.lng||'NA'}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {

                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                 <span role="button" title="Edit" class="${row.isDelete === false ? 'icon-disabled' : 'edit-button'}" ${row.isDelete !== false ? `data-location='${JSON.stringify(row)}'` : ''}>
                                <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="${row.isDelete === false ? 'icon-disabled' : 'delete-button'}" ${row.isDelete !== false ? `data-location-id="${row.id}" data-location-name="${row.city}" data-bs-toggle="modal" data-bs-target="#DeleteModal"` : ''}>
                                <i class="cp-Delete"></i>
                                </span>
                            </div>`;                          
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                 <span role="button" title="Edit" class="${row.isDelete === false ? 'icon-disabled' : 'edit-button'}" ${row.isDelete !== false ? `data-location='${JSON.stringify(row)}'` : ''}>
                                <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="icon-disabled ">
                                <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                 <span role="button" title="Edit" class="icon-disabled">
                                <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="${row.isDelete === false ? 'icon-disabled' : 'delete-button'}" ${row.isDelete !== false ? `data-location-id="${row.id}" data-location-name="${row.city}" data-bs-toggle="modal" data-bs-target="#DeleteModal"` : ''}>
                                <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                        }
                        else {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                 <span role="button" title="Edit" class="icon-disabled">
                                <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="icon-disabled">
                                <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                        }
                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });

    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }

        const NameCheckbox = $("#Name");
        const CountryNameCheckbox = $("#CountryName");

        const inputValue = $('#search-inp').val();
        if (NameCheckbox.is(':checked')) {
            selectedValues.push(NameCheckbox.val() + inputValue);
        }
        if (CountryNameCheckbox.is(':checked')) {
            selectedValues.push(CountryNameCheckbox.val() + inputValue);
        }

        dataTable.ajax.reload(function (json) {
            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    },500));
})


//  Update
$('#siteLocationTable').on('click', '.edit-button',function () {      
        SiteLocation = $(this).data("location");     
        $('#CreateModal').modal('show');
        siteLocationEdit(SiteLocation);
        $('#SaveFunction').text("Update");
    });

//  Delete
$('#siteLocationTable').on('click', '.delete-button', function () {
    const siteLocationId = $(this).data("location-id");
    const siteLocationName = $(this).data("location-name")
    $("#deleteData").attr("title", siteLocationName);
    $('#textDeleteId').val(siteLocationId);
    $('#deleteData').text(siteLocationName);
});


//Clear data
$('#create').on('click', function () {    
    let errorElements = ['#CityName-error', '#cityAsciiName-error', '#CountryName-error', '#Latitude-error', '#Longitude-error'];
    clearInputFields('CreateForm', errorElements);
    $('#SaveFunction').text('Save'); 
});

$('#cityName').on('keyup', async function () {
    const value = $(this).val();
    let siteLocationId = $('#cityNameId').val()
    const sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
  
    await validateLocation(value, siteLocationId, IsNameExist);
});

$('#countryName').on('keydown input', function () {
    const value = $(this).val();
    const sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    var errorElementPath = $('#CountryName-error');
    validateCountryInput(value, 'Enter country name ', errorElementPath);
});

$('#latitudeID').on('input', function () {
    const regex = /^-?\d*\.?\d*$/;
    const value = $(this).val();
   if (regex.test(value)) {
        $(this).val(value);
    } else {
        $(this).val(value.slice(0, -1));
    }

    validateDropDown($(this).val(), 'Enter city latitude', 'Latitude-error');
});

$('#longitudeId').on('input', function () {
    const regex = /^-?\d*\.?\d*$/;
    const value = $(this).val();
    if (regex.test(value)) {
        $(this).val(value);
    } else {
        $(this).val(value.slice(0, -1));
    }

    validatelongDropDown($(this).val(), 'Enter city longitude ', 'Longitude-error');
});

$("#SaveFunction").on('click', async function () {
    let form = $('#CreateForm');
    let cityName = $("#cityName").val();
    let cityId = $("#cityNameId").val();
    $('#cityAsciiName').val(cityName)
    let countryName = $("#countryName").val();
    let latitude = $("#latitudeID").val();
    let longitude = $("#longitudeId").val();   
    let errorcountryElement = $("#CountryName-error");
    let iscityName = await validateLocation(cityName, cityId, IsNameExist);
    let iscountryName = await validateCountryInput(countryName, ' Enter country name', errorcountryElement)
    let islatitude =  validateDropDown(latitude, ' Enter city latitude', 'Latitude-error')
    let islongitude =  validatelongDropDown(longitude, ' Enter city longitude', 'Longitude-error')
    let SiteLocationSanitizeArray = ['cityName', 'cityNameId', 'countryName', 'cityAsciiName', 'latitudeID','longitudeId']
    sanitizeContainer(SiteLocationSanitizeArray)
    setTimeout(() => {
        if (iscityName && iscountryName && islatitude && islongitude) {
            form.trigger('submit');
        }
    }, 200) 
   
});

async function validateLocation(value, id = null) {

    const errorElement = $('#CityName-error');

    if (!value) {      
        errorElement.text('Enter city name')
            .addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    let url = RootUrl + siteLocationURL.SiteLocationExistUrl;
    let data = {};
    data.name = value;
    data.id = id;

    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithSpace(value),
        await ShouldNotBeginWithUnderScore(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await ShouldNotEndWithSpace(value),
        await ShouldNotAllowMultipleSpace(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsNameExist(url, data, OnError)
    ];

    return await CommonValidation(errorElement, validationResults);
}

async function IsNameExist(url, data, errorFunc) {
    return !data.name.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}

async function validateCountryInput(value, errorMsg, errorElement) {

    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    }   
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithSpace(value),
        await ShouldNotBeginWithUnderScore(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await ShouldNotEndWithSpace(value),
        await ShouldNotAllowMultipleSpace(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await secondChar(value),
        await minMaxlength(value)
        
    ];
    return await CommonValidation(errorElement, validationResults);
}

async function validateInput(value, errorMsg, errorElement) {

    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    }
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithSpace(value),
        await ShouldNotBeginWithUnderScore(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await ShouldNotEndWithSpace(value),
        await ShouldNotAllowMultipleSpace(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await secondChar(value),
     
    ];

    return await CommonValidation(errorElement, validationResults);

}

function validateDropDown(value, errorMessage, errorElement) {
    let result = value.split('').every(d => d === '0') ? false : true;

    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    }
    if (regex.test(value) || result == false) {
        $('#' + errorElement).text('Invalid latitude').addClass('field-validation-error');
        return false;
    }
    else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
}
function validatelongDropDown(value, errorMessage, errorElement) {
    let result = value.split('').every(d => d === '0') ? false : true;
    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    }
    if (regex.test(value) || result == false) {
        $('#' + errorElement).text('Invalid longitude').addClass('field-validation-error');
        return false;
    }
    else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
}
function siteLocationEdit(SiteLocation) {    
    $('#cityNameId').val(SiteLocation.id);
    $('#cityName').val(SiteLocation.city);
    $('#cityAsciiName').val(SiteLocation.cityAscii);
    $('#countryName').val(SiteLocation.country);
    $('#latitudeID').val(SiteLocation.lat);
    $('#longitudeId').val(SiteLocation.lng);
    let errorElement = ['#CityName-error', '#cityAsciiName-error', '#CountryName-error', '#Latitude-error', '#Longitude-error'];
   
    errorElement.forEach(element => {
        $(element).text('').removeClass('field-validation-error')
    });
}