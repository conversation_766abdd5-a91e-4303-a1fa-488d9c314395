using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.CyberComponent.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponent.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberComponent.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetBySiteId;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetInfrastructureSummary;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberComponentModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;


namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class CyberComponentControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly CyberComponentsController _controller;
    private readonly CyberComponentFixture _cyberComponentFixture;

    public CyberComponentControllerTests()
    {
        _cyberComponentFixture = new CyberComponentFixture();

        var testBuilder = new ControllerTestBuilder<CyberComponentsController>();
        _controller = testBuilder.CreateController(
            _ => new CyberComponentsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetCyberComponents_ReturnsExpectedList()
    {
        // Arrange
        var expectedCyberComponents = new List<CyberComponentListVm>
        {
            _cyberComponentFixture.CyberComponentListVm,
            _cyberComponentFixture.CyberComponentListVm,
            _cyberComponentFixture.CyberComponentListVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberComponentListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCyberComponents);

        // Act
        var result = await _controller.GetCyberComponents();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberComponents = Assert.IsAssignableFrom<List<CyberComponentListVm>>(okResult.Value);
        Assert.Equal(3, cyberComponents.Count);
    }

    [Fact]
    public async Task GetCyberComponentById_ReturnsExpectedDetail()
    {
        // Arrange
        var cyberComponentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentDetailQuery>(q => q.Id == cyberComponentId), default))
            .ReturnsAsync(_cyberComponentFixture.CyberComponentDetailVm);

        // Act
        var result = await _controller.GetCyberComponentById(cyberComponentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberComponent = Assert.IsType<CyberComponentDetailVm>(okResult.Value);
        Assert.Equal(_cyberComponentFixture.CyberComponentDetailVm.Name, cyberComponent.Name);
    }

    [Fact]
    public async Task GetPaginatedCyberComponents_ReturnsExpectedResults()
    {
        // Arrange
        var query = new GetCyberComponentPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = new List<CyberComponentListVm>
        {
            _cyberComponentFixture.CyberComponentListVm,
            _cyberComponentFixture.CyberComponentListVm
        };
        var expectedResults = PaginatedResult<CyberComponentListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentPaginatedListQuery>(q => 
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberComponents(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberComponentListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task CreateCyberComponent_ReturnsCreatedAtAction()
    {
        // Arrange
        var command = _cyberComponentFixture.CreateCyberComponentCommand;
        var expectedMessage = "CyberComponent has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberComponentResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberComponent(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberComponentResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberComponent_ReturnsOk()
    {
        // Arrange
        var command = _cyberComponentFixture.UpdateCyberComponentCommand;
        var expectedMessage = "CyberComponent has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberComponentResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberComponent(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberComponentResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteCyberComponent_ReturnsOk()
    {
        // Arrange
        var cyberComponentId = Guid.NewGuid().ToString();
        var expectedMessage = "CyberComponent has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCyberComponentCommand>(c => c.Id == cyberComponentId), default))
            .ReturnsAsync(new DeleteCyberComponentResponse
            {
                Message = expectedMessage,
                IsActive = false
            });

        // Act
        var result = await _controller.DeleteCyberComponent(cyberComponentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteCyberComponentResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public async Task GetCyberComponentBySiteId_ReturnsExpectedComponents()
    {
        // Arrange
        var siteId = Guid.NewGuid().ToString();
        var expectedComponents = new List<CyberComponentBySiteIdVm>
        {
            _cyberComponentFixture.CyberComponentBySiteIdVm,
            _cyberComponentFixture.CyberComponentBySiteIdVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentBySiteIdQuery>(q => q.SiteId == siteId), default))
            .ReturnsAsync(expectedComponents);

        // Act
        var result = await _controller.GetCyberComponentBySiteId(siteId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var components = Assert.IsAssignableFrom<List<CyberComponentBySiteIdVm>>(okResult.Value);
        Assert.Equal(2, components.Count);
    }

    [Fact]
    public async Task GetInfrastructureSummary_ReturnsExpectedSummary()
    {
        // Arrange
        var expectedSummary = new List<GetInfrastructureSummaryVm>
        {
            _cyberComponentFixture.GetInfrastructureSummaryVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetInfrastructureSummaryQuery>(), default))
            .ReturnsAsync(expectedSummary);

        // Act
        var result = await _controller.GetInfrastructureSummary();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var summary = Assert.IsAssignableFrom<List<GetInfrastructureSummaryVm>>(okResult.Value);
        Assert.Single(summary);
        Assert.Equal("Database Servers", summary.First().EntityType);
        Assert.Equal(25, summary.First().Count);
    }

    [Fact]
    public async Task IsCyberComponentNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var componentName = "Existing Component";
        var componentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentNameUniqueQuery>(q => 
                q.Name == componentName && q.Id == componentId), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsCyberComponentNameExist(componentName, componentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.True(exists);
    }

    [Fact]
    public async Task IsCyberComponentNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var componentName = "Unique Component Name";
        var componentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentNameUniqueQuery>(q => 
                q.Name == componentName && q.Id == componentId), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsCyberComponentNameExist(componentName, componentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.False(exists);
    }

    [Fact]
    public async Task GetCyberComponents_HandlesEmptyList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberComponentListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<CyberComponentListVm>());

        // Act
        var result = await _controller.GetCyberComponents();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberComponents = Assert.IsAssignableFrom<List<CyberComponentListVm>>(okResult.Value);
        Assert.Empty(cyberComponents);
    }

    [Fact]
    public async Task GetCyberComponentById_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.GetCyberComponentById(invalidId));
    }

    [Fact]
    public async Task GetCyberComponentById_HandlesNotFound()
    {
        // Arrange
        var cyberComponentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentDetailQuery>(q => q.Id == cyberComponentId), default))
            .ThrowsAsync(new NotFoundException("CyberComponent", cyberComponentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetCyberComponentById(cyberComponentId));
    }

    [Fact]
    public async Task DeleteCyberComponent_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteCyberComponent(invalidId));
    }

    [Fact]
    public async Task CreateCyberComponent_HandlesEnterpriseInfrastructureComponent()
    {
        // Arrange
        var command = new CreateCyberComponentCommand
        {
            Name = "Enterprise Mission-Critical Infrastructure Component",
            Properties = "{\"enterpriseInfrastructure\":{\"tier\":\"tier0\",\"criticality\":\"mission_critical\",\"availability\":\"99.99%\",\"specifications\":{\"cpu\":\"256 cores\",\"memory\":\"2TB\",\"storage\":\"50TB NVMe SSD RAID 10\",\"network\":\"100Gbps redundant\"},\"software\":{\"os\":\"Red Hat Enterprise Linux 8.5\",\"virtualization\":\"VMware vSphere 7.0\",\"clustering\":\"VMware vSAN\",\"backup\":\"Veeam Backup & Replication\",\"monitoring\":\"VMware vRealize Operations\"},\"security\":{\"encryption\":\"FIPS 140-2 Level 3\",\"authentication\":\"Multi-factor with smart cards\",\"network_security\":\"Micro-segmentation with NSX\",\"compliance\":[\"SOX\",\"PCI-DSS\",\"FISMA\",\"GDPR\",\"HIPAA\"]},\"disaster_recovery\":{\"rto\":\"15 minutes\",\"rpo\":\"0 minutes\",\"dr_site\":\"secondary_datacenter\",\"replication\":\"synchronous\",\"testing\":\"monthly\"},\"maintenance\":{\"patching\":\"automated with rollback\",\"updates\":\"staged deployment\",\"monitoring\":\"24x7 NOC\",\"support\":\"enterprise premium\"}}}",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "Enterprise Tier-0 Data Center",
            Type = "Mission-Critical Infrastructure",
            ServerTypeId = Guid.NewGuid().ToString(),
            ServerType = "Enterprise Physical",
            Logo = "enterprise_mission_critical.png",
            Status = "Active",
            Description = "Mission-critical enterprise infrastructure component supporting core business operations with highest availability, security, and compliance requirements"
        };

        var expectedMessage = "Enterprise Infrastructure CyberComponent created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberComponentResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberComponent(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberComponentResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Enterprise Infrastructure", response.Message);
    }

    [Fact]
    public async Task UpdateCyberComponent_HandlesPerformanceOptimization()
    {
        // Arrange
        var command = new UpdateCyberComponentCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Performance-Optimized Enterprise Database Server",
            Properties = "{\"performanceOptimization\":{\"baseline\":{\"cpu_utilization\":\"75%\",\"memory_usage\":\"80%\",\"disk_iops\":\"50000\",\"network_throughput\":\"5Gbps\"},\"optimizations\":[{\"type\":\"cpu_optimization\",\"implementation\":\"Intel Turbo Boost 3.0\",\"improvement\":\"25% performance increase\"},{\"type\":\"memory_optimization\",\"implementation\":\"Intel Optane DC Persistent Memory\",\"improvement\":\"40% faster data access\"},{\"type\":\"storage_optimization\",\"implementation\":\"NVMe SSD with Intel QLC 3D NAND\",\"improvement\":\"300% IOPS increase\"},{\"type\":\"network_optimization\",\"implementation\":\"Intel Ethernet 800 Series\",\"improvement\":\"50% throughput increase\"}],\"results\":{\"cpu_utilization\":\"60%\",\"memory_usage\":\"65%\",\"disk_iops\":\"150000\",\"network_throughput\":\"7.5Gbps\"},\"monitoring\":{\"tools\":[\"Intel VTune Profiler\",\"Oracle Enterprise Manager\",\"Grafana\"],\"metrics\":[\"real_time_performance\",\"resource_utilization\",\"bottleneck_analysis\"],\"alerting\":\"predictive_analytics\"}}}",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "Enterprise Performance Center",
            Type = "High-Performance Database Server",
            ServerTypeId = Guid.NewGuid().ToString(),
            ServerType = "Optimized Physical",
            Logo = "performance_optimized_server.png",
            Status = "Active",
            Description = "Performance-optimized enterprise database server with advanced hardware acceleration and intelligent monitoring for maximum throughput and efficiency"
        };

        var expectedMessage = "Performance optimization completed successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberComponentResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberComponent(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberComponentResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Performance optimization", response.Message);
    }

    [Fact]
    public async Task GetPaginatedCyberComponents_HandlesAdvancedFiltering()
    {
        // Arrange
        var query = new GetCyberComponentPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 20,
            SearchString = "Database",
            SortColumn = "Type",
            SortOrder = "DESC"
        };

        var expectedData = new List<CyberComponentListVm>
        {
            new CyberComponentListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Database Server 01",
                Type = "Database Server",
                ServerType = "Physical",
                Status = "Active",
                SiteName = "Primary Data Center"
            },
            new CyberComponentListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Database Server 02",
                Type = "Database Server",
                ServerType = "Virtual",
                Status = "Active",
                SiteName = "Secondary Data Center"
            }
        };
        var expectedResults = PaginatedResult<CyberComponentListVm>.Success(expectedData, 2, 1, 20);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber &&
                q.PageSize == query.PageSize &&
                q.SearchString == query.SearchString &&
                q.SortColumn == query.SortColumn &&
                q.SortOrder == query.SortOrder), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberComponents(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberComponentListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, component => Assert.Contains("Database", component.Name));
        Assert.Equal("DESC", query.SortOrder);
    }

    [Fact]
    public async Task GetCyberComponentBySiteId_HandlesMultipleSiteComponents()
    {
        // Arrange
        var siteId = Guid.NewGuid().ToString();
        var expectedComponents = new List<CyberComponentBySiteIdVm>
        {
            new CyberComponentBySiteIdVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Site Database Server",
                Type = "Database Server",
                ServerType = "Physical",
                Status = "Active",
                SiteId = siteId,
                SiteName = "Enterprise Primary Site"
            },
            new CyberComponentBySiteIdVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Site Application Server",
                Type = "Application Server",
                ServerType = "Virtual",
                Status = "Active",
                SiteId = siteId,
                SiteName = "Enterprise Primary Site"
            },
            new CyberComponentBySiteIdVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Site Web Server",
                Type = "Web Server",
                ServerType = "Virtual",
                Status = "Active",
                SiteId = siteId,
                SiteName = "Enterprise Primary Site"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentBySiteIdQuery>(q => q.SiteId == siteId), default))
            .ReturnsAsync(expectedComponents);

        // Act
        var result = await _controller.GetCyberComponentBySiteId(siteId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var components = Assert.IsAssignableFrom<List<CyberComponentBySiteIdVm>>(okResult.Value);
        Assert.Equal(3, components.Count);
        Assert.All(components, c => Assert.Equal(siteId, c.SiteId));
        Assert.Contains(components, c => c.Type == "Database Server");
        Assert.Contains(components, c => c.Type == "Application Server");
        Assert.Contains(components, c => c.Type == "Web Server");
    }

    [Fact]
    public async Task GetInfrastructureSummary_HandlesComprehensiveInfrastructure()
    {
        // Arrange
        var expectedSummary = new List<GetInfrastructureSummaryVm>
        {
            new GetInfrastructureSummaryVm
            {
                EntityType = "Database Servers",
                Count = 50,
                ServerInfrastructureSummaryVms = new List<ServerInfrastructureSummaryVm>
                {
                    new ServerInfrastructureSummaryVm
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = "Enterprise DB Cluster 01",
                        IpAddress = "**********",
                        HostName = "entdbcluster01.enterprise.com",
                        SiteId = Guid.NewGuid().ToString(),
                        SiteName = "Enterprise Primary DC",
                        Status = "Active"
                    }
                }
            },
            new GetInfrastructureSummaryVm
            {
                EntityType = "Application Servers",
                Count = 75,
                ServerInfrastructureSummaryVms = new List<ServerInfrastructureSummaryVm>
                {
                    new ServerInfrastructureSummaryVm
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = "Enterprise App Cluster 01",
                        IpAddress = "**********",
                        HostName = "entappcluster01.enterprise.com",
                        SiteId = Guid.NewGuid().ToString(),
                        SiteName = "Enterprise Primary DC",
                        Status = "Active"
                    }
                }
            },
            new GetInfrastructureSummaryVm
            {
                EntityType = "Web Servers",
                Count = 25,
                ServerInfrastructureSummaryVms = new List<ServerInfrastructureSummaryVm>
                {
                    new ServerInfrastructureSummaryVm
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = "Enterprise Web Farm 01",
                        IpAddress = "**********",
                        HostName = "entwebfarm01.enterprise.com",
                        SiteId = Guid.NewGuid().ToString(),
                        SiteName = "Enterprise Primary DC",
                        Status = "Active"
                    }
                }
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetInfrastructureSummaryQuery>(), default))
            .ReturnsAsync(expectedSummary);

        // Act
        var result = await _controller.GetInfrastructureSummary();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var summary = Assert.IsAssignableFrom<List<GetInfrastructureSummaryVm>>(okResult.Value);
        Assert.Equal(3, summary.Count);
        Assert.Equal(150, summary.Sum(s => s.Count)); // Total of 50 + 75 + 25
        Assert.Contains(summary, s => s.EntityType == "Database Servers");
        Assert.Contains(summary, s => s.EntityType == "Application Servers");
        Assert.Contains(summary, s => s.EntityType == "Web Servers");
    }

    [Fact]
    public async Task CreateCyberComponent_HandlesCloudNativeComponent()
    {
        // Arrange
        var command = new CreateCyberComponentCommand
        {
            Name = "Enterprise Cloud-Native Microservices Platform",
            Properties = "{\"cloudNative\":{\"platform\":\"Kubernetes\",\"orchestration\":\"Red Hat OpenShift 4.8\",\"containerRuntime\":\"CRI-O\",\"servicesMesh\":\"Istio 1.11\",\"microservices\":[{\"name\":\"user-service\",\"replicas\":5,\"resources\":{\"cpu\":\"500m\",\"memory\":\"1Gi\"},\"storage\":\"persistent-volume\"},{\"name\":\"order-service\",\"replicas\":3,\"resources\":{\"cpu\":\"1000m\",\"memory\":\"2Gi\"},\"storage\":\"persistent-volume\"},{\"name\":\"payment-service\",\"replicas\":7,\"resources\":{\"cpu\":\"750m\",\"memory\":\"1.5Gi\"},\"storage\":\"persistent-volume\"}],\"infrastructure\":{\"nodes\":12,\"nodeType\":\"c5.4xlarge\",\"storage\":\"Amazon EBS gp3\",\"networking\":\"Amazon VPC with private subnets\"},\"security\":{\"rbac\":\"enabled\",\"networkPolicies\":\"strict\",\"podSecurityPolicies\":\"enforced\",\"secretsManagement\":\"HashiCorp Vault\"},\"monitoring\":{\"metrics\":\"Prometheus + Grafana\",\"logging\":\"Fluentd + Elasticsearch + Kibana\",\"tracing\":\"Jaeger\",\"alerting\":\"AlertManager\"},\"cicd\":{\"pipeline\":\"Jenkins X\",\"gitOps\":\"ArgoCD\",\"registry\":\"Harbor\",\"scanning\":\"Twistlock\"}}}",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "Enterprise Cloud Platform",
            Type = "Cloud-Native Platform",
            ServerTypeId = Guid.NewGuid().ToString(),
            ServerType = "Kubernetes Cluster",
            Logo = "cloud_native_platform.png",
            Status = "Active",
            Description = "Enterprise cloud-native microservices platform built on Kubernetes with comprehensive DevSecOps pipeline, monitoring, and security controls"
        };

        var expectedMessage = "Cloud-Native CyberComponent deployed successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberComponentResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberComponent(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberComponentResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Cloud-Native", response.Message);
    }

    [Fact]
    public async Task UpdateCyberComponent_HandlesSecurityHardening()
    {
        // Arrange
        var command = new UpdateCyberComponentCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Security-Hardened Enterprise Server",
            Properties = "{\"securityHardening\":{\"baseline\":{\"vulnerabilities\":\"medium\",\"compliance_score\":\"75%\",\"security_controls\":\"basic\"},\"hardening_measures\":[{\"category\":\"access_control\",\"implementation\":\"Multi-factor authentication with FIDO2\",\"impact\":\"99.9% reduction in unauthorized access\"},{\"category\":\"network_security\",\"implementation\":\"Zero-trust micro-segmentation\",\"impact\":\"100% lateral movement prevention\"},{\"category\":\"data_protection\",\"implementation\":\"AES-256 encryption at rest and in transit\",\"impact\":\"FIPS 140-2 Level 3 compliance\"},{\"category\":\"endpoint_protection\",\"implementation\":\"CrowdStrike Falcon with behavioral analysis\",\"impact\":\"99.8% malware detection rate\"},{\"category\":\"vulnerability_management\",\"implementation\":\"Automated scanning with Rapid7 Nexpose\",\"impact\":\"24-hour vulnerability remediation SLA\"}],\"results\":{\"vulnerabilities\":\"none\",\"compliance_score\":\"98%\",\"security_controls\":\"enterprise_grade\"},\"certifications\":[\"Common Criteria EAL4+\",\"FIPS 140-2 Level 3\",\"ISO 27001\",\"SOC 2 Type II\"],\"monitoring\":{\"siem\":\"Splunk Enterprise Security\",\"ueba\":\"Exabeam Advanced Analytics\",\"soar\":\"Phantom Security Orchestration\",\"threat_intelligence\":\"Recorded Future\"}}}",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "Enterprise Secure Zone",
            Type = "Hardened Enterprise Server",
            ServerTypeId = Guid.NewGuid().ToString(),
            ServerType = "Security-Hardened Physical",
            Logo = "security_hardened_server.png",
            Status = "Active",
            Description = "Security-hardened enterprise server with advanced threat protection, zero-trust architecture, and comprehensive compliance controls"
        };

        var expectedMessage = "Security hardening completed successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberComponentResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberComponent(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberComponentResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Security hardening", response.Message);
    }

    [Fact]
    public async Task GetCyberComponentById_HandlesComplexComponentDetails()
    {
        // Arrange
        var componentId = Guid.NewGuid().ToString();
        var complexComponent = new CyberComponentDetailVm
        {
            Id = componentId,
            Name = "Enterprise Hybrid Multi-Cloud Infrastructure Component",
            Properties = "{\"hybridMultiCloud\":{\"providers\":[{\"name\":\"AWS\",\"region\":\"us-east-1\",\"services\":[\"EC2\",\"RDS\",\"S3\",\"Lambda\"]},{\"name\":\"Azure\",\"region\":\"East US\",\"services\":[\"Virtual Machines\",\"SQL Database\",\"Blob Storage\",\"Functions\"]},{\"name\":\"Google Cloud\",\"region\":\"us-central1\",\"services\":[\"Compute Engine\",\"Cloud SQL\",\"Cloud Storage\",\"Cloud Functions\"]}],\"connectivity\":{\"vpn\":\"site-to-site IPSec\",\"directConnect\":[\"AWS Direct Connect\",\"Azure ExpressRoute\",\"Google Cloud Interconnect\"],\"bandwidth\":\"10Gbps per connection\"},\"workloadDistribution\":{\"primary\":\"AWS (60%)\",\"secondary\":\"Azure (30%)\",\"tertiary\":\"Google Cloud (10%)\"},\"dataStrategy\":{\"replication\":\"cross-cloud\",\"backup\":\"multi-cloud\",\"archival\":\"cost-optimized\"},\"management\":{\"orchestration\":\"Terraform Enterprise\",\"monitoring\":\"Datadog\",\"security\":\"Prisma Cloud\",\"cost_optimization\":\"CloudHealth\"}}}",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "Enterprise Hybrid Cloud Hub",
            Type = "Hybrid Multi-Cloud Infrastructure",
            ServerTypeId = Guid.NewGuid().ToString(),
            ServerType = "Cloud-Native Hybrid",
            Logo = "hybrid_multicloud_infrastructure.png",
            Description = "Enterprise hybrid multi-cloud infrastructure component spanning AWS, Azure, and Google Cloud with unified management, monitoring, and security controls"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentDetailQuery>(q => q.Id == componentId), default))
            .ReturnsAsync(complexComponent);

        // Act
        var result = await _controller.GetCyberComponentById(componentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var component = Assert.IsType<CyberComponentDetailVm>(okResult.Value);
        Assert.Equal("Enterprise Hybrid Multi-Cloud Infrastructure Component", component.Name);
        Assert.Contains("hybridMultiCloud", component.Properties);
        Assert.Contains("AWS", component.Properties);
        Assert.Contains("Azure", component.Properties);
        Assert.Contains("Google Cloud", component.Properties);
    }

    [Fact]
    public async Task GetPaginatedCyberComponents_HandlesLargeDataset()
    {
        // Arrange
        var query = new GetCyberComponentPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 50,
            SearchString = "",
            SortColumn = "Name",
            SortOrder = "ASC"
        };

        var largeDataset = new List<CyberComponentListVm>();
        for (int i = 0; i < 1000; i++)
        {
            largeDataset.Add(new CyberComponentListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = $"Enterprise Component {i + 1:D4}",
                Type = i % 4 == 0 ? "Database Server" : i % 3 == 0 ? "Application Server" : i % 2 == 0 ? "Web Server" : "Cache Server",
                ServerType = i % 2 == 0 ? "Physical" : "Virtual",
                Status = i % 10 == 0 ? "Maintenance" : "Active",
                SiteName = $"Enterprise Site {(i % 5) + 1}"
            });
        }

        var paginatedData = largeDataset.Take(50).ToList();
        var expectedResults = PaginatedResult<CyberComponentListVm>.Success(paginatedData, 1000, 1, 50);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberComponents(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberComponentListVm>>(okResult.Value);
        Assert.Equal(50, paginatedResult.Data.Count);
        Assert.Equal(1000, paginatedResult.TotalCount);
        Assert.Equal(20, paginatedResult.TotalPages); // 1000 / 50
        Assert.Contains(paginatedResult.Data, c => c.Type == "Database Server");
        Assert.Contains(paginatedResult.Data, c => c.ServerType == "Physical");
        Assert.Contains(paginatedResult.Data, c => c.Status == "Active");
    }

    [Fact]
    public async Task IsCyberComponentNameExist_HandlesSpecialCharacters()
    {
        // Arrange
        var componentName = "Enterprise-Component_v2.1.0 (Production) [Critical]";
        var componentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentNameUniqueQuery>(q =>
                q.Name == componentName && q.Id == componentId), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsCyberComponentNameExist(componentName, componentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.False(exists);
    }

    [Fact]
    public async Task GetCyberComponentBySiteId_HandlesEmptySite()
    {
        // Arrange
        var siteId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentBySiteIdQuery>(q => q.SiteId == siteId), default))
            .ReturnsAsync(new List<CyberComponentBySiteIdVm>());

        // Act
        var result = await _controller.GetCyberComponentBySiteId(siteId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var components = Assert.IsAssignableFrom<List<CyberComponentBySiteIdVm>>(okResult.Value);
        Assert.Empty(components);
    }

    //[Fact]
    //public async Task CreateCyberComponent_ValidatesRequiredFields()
    //{
    //    // Arrange
    //    var command = new CreateCyberComponentCommand
    //    {
    //        Name = "", // Empty required field
    //        Properties = "{}",
    //        SiteId = Guid.NewGuid().ToString(),
    //        Type = "Test Server"
    //    };

    //    _mediatorMock
    //        .Setup(m => m.Send(command, default))
    //        .ThrowsAsync(new ValidationException("Name is required"));

    //    // Act & Assert
    //    await Assert.ThrowsAsync<ValidationException>(() => _controller.CreateCyberComponent(command));
    //}

    [Fact]
    public async Task DeleteCyberComponent_HandlesComponentInUse()
    {
        // Arrange
        var componentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCyberComponentCommand>(c => c.Id == componentId), default))
            .ThrowsAsync(new EntityAssociatedException("The component is currently in use."));

        // Act & Assert
        await Assert.ThrowsAsync<EntityAssociatedException>(() => _controller.DeleteCyberComponent(componentId));
    }

    [Fact]
    public async Task CreateCyberComponent_HandlesEdgeComputingComponent()
    {
        // Arrange
        var command = new CreateCyberComponentCommand
        {
            Name = "Enterprise Edge Computing Infrastructure",
            Properties = "{\"edgeComputing\":{\"deployment\":\"distributed\",\"locations\":[{\"site\":\"retail_store_001\",\"devices\":[\"NVIDIA Jetson AGX Xavier\",\"Intel NUC\"],\"connectivity\":\"5G + Fiber\"},{\"site\":\"manufacturing_plant_001\",\"devices\":[\"Dell Edge Gateway 3000\",\"HPE Edgeline EL300\"],\"connectivity\":\"Industrial Ethernet\"},{\"site\":\"remote_office_001\",\"devices\":[\"Cisco Catalyst IE3400\",\"Aruba AP-515\"],\"connectivity\":\"Satellite + 4G LTE\"}],\"workloads\":[{\"type\":\"ai_inference\",\"framework\":\"TensorFlow Lite\",\"models\":[\"object_detection\",\"predictive_maintenance\"]},{\"type\":\"data_processing\",\"framework\":\"Apache Kafka\",\"functions\":[\"stream_processing\",\"real_time_analytics\"]},{\"type\":\"iot_gateway\",\"protocols\":[\"MQTT\",\"CoAP\",\"LoRaWAN\"],\"devices\":\"10000+\"}],\"management\":{\"orchestration\":\"AWS IoT Greengrass\",\"monitoring\":\"Azure IoT Central\",\"security\":\"Zscaler Private Access\",\"updates\":\"over_the_air\"}}}",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "Enterprise Edge Network",
            Type = "Edge Computing Infrastructure",
            ServerTypeId = Guid.NewGuid().ToString(),
            ServerType = "Edge Device",
            Logo = "edge_computing_infrastructure.png",
            Status = "Active",
            Description = "Enterprise edge computing infrastructure with distributed AI inference, IoT gateway capabilities, and centralized management across multiple edge locations"
        };

        var expectedMessage = "Edge Computing CyberComponent deployed successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberComponentResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberComponent(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberComponentResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Edge Computing", response.Message);
    }

    [Fact]
    public async Task UpdateCyberComponent_HandlesDisasterRecoveryConfiguration()
    {
        // Arrange
        var command = new UpdateCyberComponentCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Enterprise Disaster Recovery Infrastructure",
            Properties = "{\"disasterRecovery\":{\"strategy\":\"active_passive\",\"primary_site\":{\"location\":\"New York Data Center\",\"capacity\":\"100%\",\"components\":[\"database_cluster\",\"application_servers\",\"storage_array\"]},\"dr_site\":{\"location\":\"Chicago Data Center\",\"capacity\":\"100%\",\"components\":[\"standby_database\",\"warm_standby_servers\",\"replicated_storage\"]},\"replication\":{\"database\":{\"method\":\"synchronous\",\"technology\":\"Oracle Data Guard\",\"lag\":\"<1 second\"},\"storage\":{\"method\":\"asynchronous\",\"technology\":\"NetApp SnapMirror\",\"lag\":\"<5 minutes\"},\"applications\":{\"method\":\"blue_green\",\"technology\":\"Docker Swarm\",\"switchover\":\"<2 minutes\"}},\"testing\":{\"frequency\":\"monthly\",\"type\":\"full_failover\",\"duration\":\"4 hours\",\"success_criteria\":{\"rto\":\"<15 minutes\",\"rpo\":\"<5 minutes\",\"data_integrity\":\"100%\"}},\"automation\":{\"failover_trigger\":\"automated_with_approval\",\"health_checks\":\"continuous\",\"rollback\":\"one_click\",\"notification\":\"multi_channel\"}}}",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "Enterprise DR Infrastructure",
            Type = "Disaster Recovery Infrastructure",
            ServerTypeId = Guid.NewGuid().ToString(),
            ServerType = "DR Physical",
            Logo = "disaster_recovery_infrastructure.png",
            Status = "Active",
            Description = "Enterprise disaster recovery infrastructure with automated failover, continuous replication, and comprehensive testing procedures to ensure business continuity"
        };

        var expectedMessage = "Disaster Recovery configuration updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberComponentResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberComponent(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberComponentResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Disaster Recovery", response.Message);
    }

    [Fact]
    public async Task GetInfrastructureSummary_HandlesEmptyInfrastructure()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetInfrastructureSummaryQuery>(), default))
            .ReturnsAsync(new List<GetInfrastructureSummaryVm>());

        // Act
        var result = await _controller.GetInfrastructureSummary();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var summary = Assert.IsAssignableFrom<List<GetInfrastructureSummaryVm>>(okResult.Value);
        Assert.Empty(summary);
    }

    [Fact]
    public async Task GetPaginatedCyberComponents_HandlesZeroResults()
    {
        // Arrange
        var query = new GetCyberComponentPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "NonExistentComponent"
        };

        var expectedResults = PaginatedResult<CyberComponentListVm>.Success(new List<CyberComponentListVm>(), 0, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentPaginatedListQuery>(q =>
                q.SearchString == query.SearchString), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberComponents(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberComponentListVm>>(okResult.Value);
        Assert.Empty(paginatedResult.Data);
        Assert.Equal(0, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task CreateCyberComponent_HandlesQuantumComputingComponent()
    {
        // Arrange
        var command = new CreateCyberComponentCommand
        {
            Name = "Enterprise Quantum Computing Research Platform",
            Properties = "{\"quantumComputing\":{\"platform\":\"IBM Quantum Network\",\"processor\":\"IBM Eagle 127-qubit\",\"topology\":\"heavy-hex\",\"connectivity\":\"all-to-all\",\"applications\":[{\"name\":\"cryptographic_research\",\"algorithms\":[\"Shor's algorithm\",\"Grover's algorithm\"],\"use_cases\":[\"RSA factorization\",\"database search optimization\"]},{\"name\":\"optimization_problems\",\"algorithms\":[\"QAOA\",\"VQE\"],\"use_cases\":[\"portfolio optimization\",\"supply chain optimization\"]},{\"name\":\"machine_learning\",\"algorithms\":[\"Quantum SVM\",\"Quantum Neural Networks\"],\"use_cases\":[\"pattern recognition\",\"drug discovery\"]}],\"infrastructure\":{\"cooling\":\"dilution refrigerator\",\"temperature\":\"15 millikelvin\",\"isolation\":\"electromagnetic shielding\",\"control_electronics\":\"room temperature\"},\"access\":{\"interface\":\"Qiskit SDK\",\"programming_languages\":[\"Python\",\"Q#\",\"Cirq\"],\"cloud_access\":\"IBM Quantum Experience\",\"on_premise\":\"hybrid_deployment\"}}}",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "Enterprise Quantum Research Lab",
            Type = "Quantum Computing Platform",
            ServerTypeId = Guid.NewGuid().ToString(),
            ServerType = "Quantum Processor",
            Logo = "quantum_computing_platform.png",
            Status = "Active",
            Description = "Enterprise quantum computing research platform for advanced cryptographic research, optimization problems, and quantum machine learning applications"
        };

        var expectedMessage = "Quantum Computing CyberComponent initialized successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberComponentResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberComponent(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberComponentResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Quantum Computing", response.Message);
    }

    [Fact]
    public async Task GetCyberComponentBySiteId_ThrowsException_WhenInvalidSiteId()
    {
        // Arrange
        var invalidSiteId = "invalid-site-id";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentBySiteIdQuery>(q => q.SiteId == invalidSiteId), default))
            .ThrowsAsync(new InvalidArgumentException("Input 'site Id' is not valid format."));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetCyberComponentBySiteId(invalidSiteId));
    }

    [Fact]
    public async Task UpdateCyberComponent_HandlesVersionUpgrade()
    {
        // Arrange
        var command = new UpdateCyberComponentCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Enterprise Component v3.0",
            Properties = "{\"versionUpgrade\":{\"from_version\":\"2.5.1\",\"to_version\":\"3.0.0\",\"changes\":[{\"type\":\"feature\",\"description\":\"AI-powered auto-scaling\"},{\"type\":\"security\",\"description\":\"Zero-trust architecture\"},{\"type\":\"performance\",\"description\":\"50% faster processing\"}],\"migration\":{\"strategy\":\"blue_green\",\"rollback_plan\":\"automated\",\"testing\":\"comprehensive\"}}}",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "Enterprise Upgrade Site",
            Type = "Upgraded Enterprise Server",
            ServerTypeId = Guid.NewGuid().ToString(),
            ServerType = "Next-Gen Physical",
            Logo = "upgraded_enterprise_server.png",
            Status = "Active",
            Description = "Enterprise component upgraded to version 3.0 with enhanced AI capabilities, security improvements, and performance optimizations"
        };

        var expectedMessage = "Version upgrade completed successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberComponentResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberComponent(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberComponentResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Version upgrade", response.Message);
    }
}
