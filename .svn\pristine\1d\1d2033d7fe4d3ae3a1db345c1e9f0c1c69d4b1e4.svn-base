using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.SendEmail;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowDrCalenderModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IWorkflowDrCalenderService
{
    Task<List<WorkflowDrCalenderListVm>> GetWorkflowDrCalenderList();
    Task<BaseResponse> CreateAsync(CreateWorkflowDrCalenderCommand createWorkflowDrCalenderCommand);
    Task<BaseResponse> UpdateAsync(UpdateWorkflowDrCalenderCommand updateWorkflowDrCalenderCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<WorkflowDrCalenderDetailVm> GetByReferenceId(string id);
    Task<BaseResponse> SendEmail(WorkflowDrCalenderSendEmailCommand workflowDrCalenderSendEmailCommand);
    #region NameExist
 Task<bool> IsWorkflowDrCalenderNameExist(string name, string? id);
   #endregion
    #region Paginated
 Task<PaginatedResult<WorkflowDrCalenderListVm>> GetPaginatedWorkflowDrCalenders(GetWorkflowDrCalenderPaginatedListQuery query);
    #endregion
}
