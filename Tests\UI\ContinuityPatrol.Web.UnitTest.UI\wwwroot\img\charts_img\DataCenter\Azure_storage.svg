<svg width="84" height="83" viewBox="0 0 84 83" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_5377_602)">
<mask id="mask0_5377_602" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="84" height="83">
<path d="M83.25 0H0.25V83H83.25V0Z" fill="white"/>
</mask>
<g mask="url(#mask0_5377_602)">
<g filter="url(#filter0_d_5377_602)">
<path d="M41.8027 71.3789C58.3713 71.3789 71.8027 57.9474 71.8027 41.3789C71.8027 24.8104 58.3713 11.3789 41.8027 11.3789C25.2342 11.3789 11.8027 24.8104 11.8027 41.3789C11.8027 57.9474 25.2342 71.3789 41.8027 71.3789Z" fill="white"/>
<g clip-path="url(#clip1_5377_602)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M50.1845 29.166H34.815L27.1703 42.4993L34.815 55.8327H50.1845L57.8296 42.4993L50.1845 29.166ZM50.2643 48.4873C50.2643 49.605 49.3087 50.563 48.1937 50.563H36.8859C35.7709 50.563 34.8154 49.605 34.8154 48.4873V36.5913C34.8154 35.4737 35.7709 34.5153 36.8859 34.5153H45.2474H46.84H47.0791L50.2646 37.709V39.3857V48.4877L50.2643 48.4873ZM41.3451 44.655C41.3451 44.4953 41.2654 44.4157 41.2654 44.3357C41.2654 44.2557 41.1857 44.176 41.1063 44.0963C41.0269 44.0167 41.0266 44.0163 40.9469 44.0163H40.7081C40.5487 44.0163 40.4689 44.0163 40.3896 44.0963C40.3102 44.1763 40.2301 44.256 40.1508 44.4157C40.0714 44.5753 40.0711 44.735 40.0711 44.8947V45.6133V46.4117C40.0711 46.6513 40.1508 46.8107 40.2305 46.8907C40.3102 47.0503 40.3899 47.1303 40.4693 47.1303C40.549 47.2103 40.6287 47.2103 40.7878 47.2103C40.8675 47.2103 40.9472 47.2103 41.0266 47.1303C41.0684 47.127 41.1076 47.1091 41.1369 47.0797C41.1662 47.0504 41.1836 47.0116 41.186 46.9707C41.2657 46.8907 41.2657 46.811 41.3451 46.7313C41.3451 46.6513 41.4248 46.5717 41.4248 46.412C41.4248 46.3323 41.4248 46.1723 41.5042 46.0127V45.5337V44.975C41.3451 44.895 41.3451 44.8153 41.3451 44.6557V44.655ZM44.9289 38.4273C44.9289 38.2677 44.8491 38.188 44.8491 38.108C44.8491 38.028 44.7694 37.9483 44.6897 37.8683C44.61 37.7883 44.61 37.7883 44.5303 37.7883H44.2915C44.1324 37.7883 44.0527 37.7883 43.973 37.8683C43.8933 37.9483 43.8135 38.028 43.7338 38.1877C43.6541 38.3473 43.6545 38.507 43.6545 38.6667V39.3853V40.1837C43.6545 40.4233 43.7338 40.583 43.8135 40.6627C43.8933 40.8223 43.973 40.9023 44.0523 40.9023C44.1321 40.9823 44.2114 40.9823 44.3709 40.9823C44.4506 40.9823 44.5299 40.9823 44.6097 40.9023C44.6515 40.899 44.6906 40.8811 44.7199 40.8517C44.7492 40.8224 44.7667 40.7836 44.7691 40.7427C44.8488 40.6627 44.8488 40.583 44.9285 40.503C44.9285 40.4233 45.0082 40.3433 45.0082 40.1837C45.0082 40.104 45.0082 39.9443 45.0879 39.7843V39.3053V38.7463C45.0082 38.7463 45.0082 38.587 44.9289 38.4273ZM46.5214 35.633H45.2474H36.8859C36.3286 35.633 35.9303 36.0323 35.9303 36.591V48.4873C35.9303 49.0463 36.3286 49.4453 36.8859 49.4453H48.1941C48.4495 49.4388 48.6922 49.3351 48.8706 49.1562C49.0491 48.9773 49.1492 48.7373 49.1496 48.4873V38.2677H46.6015V35.633H46.5214ZM39.434 38.0283V37.9487V37.8687L39.5137 37.7887L40.4693 37.15H40.549H40.6287H40.7084H40.8679H41.1067H41.2657H41.3455V37.23V40.9827H42.0622H42.1419L42.2213 41.0627C42.301 41.1423 42.2213 41.1423 42.2213 41.1423V41.302V41.4617V41.5413C42.2213 41.5413 42.2213 41.6213 42.1419 41.6213H42.0622H39.5935H39.5137L39.434 41.5413V41.4617V41.302V41.1423V41.0627C39.434 41.0627 39.434 40.9827 39.5137 40.9827H39.5935H40.4693V38.0287L39.7525 38.428C39.6728 38.428 39.6728 38.5077 39.5935 38.5077H39.5137C39.5137 38.5077 39.5137 38.428 39.434 38.428V38.1887V38.029V38.0283ZM42.2213 46.5713C42.1419 46.8907 42.0622 47.1303 41.9028 47.29C41.7434 47.5293 41.5843 47.6893 41.4248 47.769C41.186 47.8487 40.9469 47.9287 40.6284 47.9287C40.3099 47.9287 40.0711 47.8487 39.8319 47.769C39.7181 47.7338 39.615 47.6716 39.5316 47.5881C39.4483 47.5045 39.3872 47.4022 39.354 47.29C39.2743 47.0507 39.1152 46.811 39.1152 46.5713C39.0355 46.252 39.0355 45.9327 39.0355 45.6133C39.0355 45.214 39.0355 44.8947 39.1152 44.6553C39.1949 44.336 39.2743 44.0963 39.4337 43.9367C39.5931 43.777 39.7522 43.5373 39.9116 43.4577C40.1504 43.3777 40.3896 43.298 40.7081 43.298C41.0266 43.298 41.2654 43.3777 41.5042 43.4577C41.618 43.4929 41.7211 43.5551 41.8045 43.6386C41.8879 43.7222 41.9489 43.8245 41.9822 43.9367C42.0619 44.1763 42.221 44.4157 42.221 44.6553C42.3007 44.9747 42.3007 45.294 42.3007 45.6133C42.3007 46.0127 42.301 46.332 42.2213 46.5713ZM45.805 47.689V47.769C45.805 47.769 45.805 47.8487 45.7253 47.8487H45.6456H43.1768H43.0971L43.0174 47.769V47.689V47.5293V47.3697V47.29C43.0174 47.29 43.0174 47.2103 43.0971 47.2103H43.1768H44.053V44.2563L43.3363 44.6557C43.2566 44.6557 43.2566 44.7353 43.1768 44.7353H43.0971C43.0971 44.7353 43.0971 44.6557 43.0174 44.6557V44.4163V44.2567V44.177V44.097L43.0971 44.017L44.0527 43.3783H44.1324H44.2121H44.2918H44.4513H44.6901H44.8495H44.9292V43.4583V47.211H45.6459H45.7257L45.8054 47.2907V47.3703V47.53V47.6897L45.805 47.689ZM45.8847 40.3437C45.805 40.663 45.7253 40.9027 45.5662 41.0623C45.4068 41.3017 45.2477 41.4617 45.0883 41.5413C44.8495 41.6213 44.6103 41.701 44.2918 41.701C43.9733 41.701 43.7345 41.621 43.4954 41.5413C43.3816 41.5061 43.2784 41.444 43.1951 41.3604C43.1117 41.2769 43.0507 41.1745 43.0174 41.0623C42.9377 40.9027 42.7786 40.5833 42.7786 40.3437C42.7786 40.104 42.6989 39.705 42.6989 39.3857C42.6989 38.9863 42.6989 38.667 42.7786 38.4277C42.8583 38.1083 42.938 37.8687 43.0971 37.709C43.2566 37.4697 43.4156 37.3097 43.5751 37.23C43.8139 37.15 44.053 37.0703 44.3715 37.0703C44.6901 37.0703 44.9289 37.1503 45.168 37.23C45.2818 37.2652 45.3849 37.3274 45.4683 37.4109C45.5517 37.4945 45.6127 37.5969 45.6459 37.709C45.7257 37.9483 45.8847 38.188 45.8847 38.4277C45.9645 38.747 45.9645 39.0663 45.9645 39.3857C45.9645 39.785 45.8847 40.1043 45.8847 40.3437Z" fill="#0078D7"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_5377_602" x="0.595155" y="0.171327" width="82.4152" height="82.4152" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5.60379"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5377_602"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5377_602" result="shape"/>
</filter>
<clipPath id="clip0_5377_602">
<rect width="84" height="83" fill="white"/>
</clipPath>
<clipPath id="clip1_5377_602">
<rect width="31" height="27" fill="white" transform="translate(27 29)"/>
</clipPath>
</defs>
</svg>
