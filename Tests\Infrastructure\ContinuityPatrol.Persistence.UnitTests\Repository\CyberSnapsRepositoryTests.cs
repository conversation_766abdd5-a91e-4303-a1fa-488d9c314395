using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ProtoBuf.WellKnownTypes;
using static Quartz.Logging.OperationName;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class CyberSnapsRepositoryTests : IClassFixture<CyberSnapsFixture>
{
    private readonly CyberSnapsFixture _cyberSnapsFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CyberSnapsRepository _repository;

    public CyberSnapsRepositoryTests(CyberSnapsFixture cyberSnapsFixture)
    {
        _cyberSnapsFixture = cyberSnapsFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CyberSnapsRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var cyberSnap = _cyberSnapsFixture.CyberSnapsDto;
        cyberSnap.Name = "ExistingSnapName";
        await _dbContext.CyberSnaps.AddAsync(cyberSnap);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.IsNameExist("ExistingSnapName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var snaps = _cyberSnapsFixture.CyberSnapsList;
        await _repository.AddRangeAsync(snaps);

        // Act
        var result = await _repository.IsNameExist("NonExistentSnapName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var cyberSnap = _cyberSnapsFixture.CyberSnapsDto;
        cyberSnap.Name = "SameSnapName";
        await _dbContext.CyberSnaps.AddAsync(cyberSnap);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = !await _repository.IsNameExist("SameSnapName", cyberSnap.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetCyberSnapsBySnapTagName Tests

    [Fact]
    public async Task GetCyberSnapsBySnapTagName_ShouldReturnSnapsForSpecificTag()
    {
        // Arrange
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = baseDate.ToString("yyyy-MM-dd");
        var tagName = "SPECIFIC_TAG";

        var snaps = _cyberSnapsFixture.CyberSnapsList;

        snaps[0].TimeStamp = $"Mon {DateTime.Now.ToString("MMM dd HH:mm:ss yyyy")}";
        snaps[0].CreatedDate = baseDate.AddDays(-3);
        snaps[0].Tag = tagName;
        snaps[1].Tag = tagName;
        snaps[0].IsActive = true;
        snaps[1].IsActive = true;
        snaps[1].TimeStamp = $"Tue {DateTime.Now.ToString("MMM dd HH:mm:ss yyyy")}";
        snaps[1].CreatedDate = baseDate.AddDays(-1); 
        snaps[2].TimeStamp = $"Wed {DateTime.Now.ToString("MMM dd HH:mm:ss yyyy")}";
        snaps[2].CreatedDate = baseDate.AddDays(-2);

        _dbContext.CyberSnaps.AddRange(snaps);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetCyberSnapsBySnapTagName(tagName, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(tagName, x.Tag));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= DateTime.Parse(startDate)));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date <= DateTime.Parse(endDate)));
    }

    [Fact]
    public async Task GetCyberSnapsBySnapTagName_ShouldReturnAllSnapsWhenTagIsAll()
    {
        // Arrange
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = baseDate.ToString("yyyy-MM-dd");

        var snaps = _cyberSnapsFixture.CyberSnapsList;

        snaps.ForEach(x => x.IsActive = true);
        snaps[0].CreatedDate = baseDate.AddDays(-3);
        snaps[1].CreatedDate = baseDate.AddDays(-1);
        snaps[2].CreatedDate = baseDate.AddDays(-10);

        _dbContext.CyberSnaps.AddRange(snaps);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetCyberSnapsBySnapTagName("all", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= DateTime.Parse(startDate)));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date <= DateTime.Parse(endDate)));
    }

    #endregion

    #region GetCyberSnapsListByDate Tests

    [Fact]
    public async Task GetCyberSnapsListByDate_ShouldReturnSnapsInDateRange()
    {
        // Arrange
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = baseDate.ToString("yyyy-MM-dd");

        var snaps = _cyberSnapsFixture.CyberSnapsList;

        snaps[0].CreatedDate = baseDate.AddDays(-3);
        snaps[1].CreatedDate = baseDate.AddDays(-15);
        snaps[2].CreatedDate = baseDate.AddDays(-15);
        snaps[0].IsActive= true;
        snaps[1].IsActive = true;

        _dbContext.CyberSnaps.AddRange(snaps);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetCyberSnapsListByDate(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Only one snap within date range
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= DateTime.Parse(startDate)));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date <= DateTime.Parse(endDate)));
    }

    #endregion

  
}
