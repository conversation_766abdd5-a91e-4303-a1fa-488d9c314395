﻿using ContinuityPatrol.Application.Features.Node.Events.Update;

namespace ContinuityPatrol.Application.Features.Node.Commands.Update;

public class UpdateNodeCommandHandler : IRequestHandler<UpdateNodeCommand, UpdateNodeResponse>
{
    private readonly IMapper _mapper;
    private readonly INodeRepository _nodeRepository;
    private readonly IPublisher _publisher;

    public UpdateNodeCommandHandler(IMapper mapper, INodeRepository nodeRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _nodeRepository = nodeRepository;
        _publisher = publisher;
    }

    public async Task<UpdateNodeResponse> Handle(UpdateNodeCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _nodeRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Node), request.Id);
        _mapper.Map(request, eventToUpdate, typeof(Domain.Entities.Node), typeof(Domain.Entities.Node));

        await _nodeRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateNodeResponse
        {
            Message = Message.Update(nameof(Domain.Entities.Node), eventToUpdate.Name),

            NodeId = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new NodeUpdatedEvent { NodeName = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}