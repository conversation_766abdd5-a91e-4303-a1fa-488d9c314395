﻿using ContinuityPatrol.Application.Features.Server.Commands.Create;
using ContinuityPatrol.Application.Features.Server.Commands.Update;
using ContinuityPatrol.Application.Features.Server.Events.Create;
using ContinuityPatrol.Application.Features.Server.Events.Delete;
using ContinuityPatrol.Application.Features.Server.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Server.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class ServerFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<Server> Servers { get; set; }

    public List<LicenseManager> LicenseManagers { get; set; }

    public CreateServerCommand CreateServerCommand { get; set; }

    public UpdateServerCommand UpdateServerCommand { get; set; }

    public ServerCreatedEvent ServerCreatedEvent { get; set; }
    public ServerDeletedEvent ServerDeletedEvent { get; set; }
    public ServerUpdatedEvent ServerUpdatedEvent { get; set; }
    public ServerPaginatedEvent ServerPaginatedEvent { get; set; }

    public ServerFixture()
    {
        Servers = AutoServerFixture.Create<List<Server>>();

        LicenseManagers = AutoServerFixture.Create<List<LicenseManager>>();

        CreateServerCommand = AutoServerFixture.Create<CreateServerCommand>();

        UpdateServerCommand = AutoServerFixture.Create<UpdateServerCommand>();

        ServerCreatedEvent = AutoServerFixture.Create<ServerCreatedEvent>();

        ServerDeletedEvent = AutoServerFixture.Create<ServerDeletedEvent>();

        ServerUpdatedEvent = AutoServerFixture.Create<ServerUpdatedEvent>();

        ServerPaginatedEvent = AutoServerFixture.Create<ServerPaginatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ServerProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoServerFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateServerCommand>(p => p.Name, 10));
            fixture.Customize<CreateServerCommand>(c => c.With(b => b.SiteId, 0.ToString));
            fixture.Customize<CreateServerCommand>(c => c.With(b => b.Properties, "{\"Name\": \"admin\", \"password\": \"Admin@123\"}"));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateServerCommand>(p => p.Name, 10));
            fixture.Customize<UpdateServerCommand>(c => c.With(b => b.Id, 0.ToString));
            fixture.Customize<UpdateServerCommand>(c => c.With(b => b.Properties, "{\"Name\": \"admin\", \"password\": \"Admin@123\"}"));
            fixture.Customize<Server>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ServerCreatedEvent>(p => p.ServerName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ServerDeletedEvent>(p => p.ServerName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ServerUpdatedEvent>(p => p.ServerName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ServerPaginatedEvent>(p => p.ServerName, 10));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}