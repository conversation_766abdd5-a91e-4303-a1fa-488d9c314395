using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using System.Threading.Tasks;
using Xunit;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowActionResultRepositoryTests : IClassFixture<WorkflowActionResultFixture>
    {
        private readonly WorkflowActionResultFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowActionResultRepository _repositoryParent;
        private readonly WorkflowActionResultRepository _repositoryIsNotParent;

        public WorkflowActionResultRepositoryTests(WorkflowActionResultFixture fixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _repositoryParent = new WorkflowActionResultRepository(_dbContext,  DbContextFactory.GetMockUserService());
            _repositoryIsNotParent = new WorkflowActionResultRepository(_dbContext,  DbContextFactory.GetMockLoggedInUserIsNotParent());
        }

        [Fact]
        public async Task ListAllAsync_ReturnsAll_WhenIsParent()
        {
       
            await _dbContext.WorkflowActionResults.AddRangeAsync(_fixture.WorkflowActionResultList);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.ListAllAsync();

            Assert.Equal(_fixture.WorkflowActionResultList.Count, result.Count);
        }

        [Fact]
        public async Task ListAllAsync_ReturnsFiltered_WhenNotParent()
        {
            var entity = _fixture.WorkflowActionResultDto;
            entity.CompanyId = "ChHILD_COMPANY_123";
           await _dbContext.WorkflowActionResults.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryIsNotParent.ListAllAsync();

            Assert.All(result, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
        }

        [Fact]
        public async Task GetByReferenceIdAsync_ReturnsEntity_WhenIsParent()
        {
            
            var entity = _fixture.WorkflowActionResultDto;
          
            await _dbContext.WorkflowActionResults.AddAsync(entity);
           await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.GetByReferenceIdAsync(entity.ReferenceId);

            Assert.NotNull(result);
            Assert.Equal(entity.ReferenceId, result.ReferenceId);
        }

        [Fact]
        public async Task GetByReferenceIdAsync_ReturnsEntity_WhenNotParent()
        {
           
            var entity = _fixture.WorkflowActionResultDto;
            entity.CompanyId = "ChHILD_COMPANY_123";
            await  _dbContext.WorkflowActionResults.AddAsync(entity);
           await  _dbContext.SaveChangesAsync();

            var result = await _repositoryIsNotParent.GetByReferenceIdAsync(entity.ReferenceId);

            Assert.NotNull(result);
            Assert.Equal(entity.ReferenceId, result.ReferenceId);
        }

        [Fact]
        public async Task GetWorkflowActionResultByGroupId_ReturnsResults()
        {
            var entity = _fixture.WorkflowActionResultDto;
           await _dbContext.WorkflowActionResults.AddAsync(entity);
           await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.GetWorkflowActionResultByGroupId(entity.WorkflowOperationGroupId);

            Assert.All(result, x => Assert.Equal(entity.WorkflowOperationGroupId, x.WorkflowOperationGroupId));
        }

        [Fact]
        public async Task GetByOperationGroupIdAndStepId_ReturnsResult()
        {
            var entity = _fixture.WorkflowActionResultDto;
      
            entity.StepId = "baac749f-fb84-4498-9677-3ef8d995e8d4";
            await _dbContext.WorkflowActionResults.AddAsync(entity);
 await  _dbContext.SaveChangesAsync();;

            var result = await _repositoryParent.GetByOperationGroupIdAndStepId(entity.WorkflowOperationGroupId, "baac749f-fb84-4498-9677-3ef8d995e8d4");

            Assert.NotNull(result);
            Assert.Equal(entity.WorkflowOperationGroupId, result.WorkflowOperationGroupId);
            Assert.Equal("baac749f-fb84-4498-9677-3ef8d995e8d4", result.StepId);
        }

        [Fact]
        public async Task GetWorkflowActionResultNames_ReturnsNames_WhenNotParent()
        {
           
            var entity = _fixture.WorkflowActionResultDto;
            entity.CompanyId = "ChHILD_COMPANY_123";
            entity.WorkflowActionName = "A1";
            await  _dbContext.WorkflowActionResults.AddAsync(entity);
           await  _dbContext.SaveChangesAsync();

            var result = await _repositoryIsNotParent.GetWorkflowActionResultNames();

            Assert.All(result, x => Assert.Equal("A1", x.WorkflowActionName));
        }

        [Fact]
        public async Task GetWorkflowActionResultByWorkflowOperationGroupId_ReturnsResults()
        {
           
            var entity = _fixture.WorkflowActionResultDto;
            await  _dbContext.WorkflowActionResults.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.GetWorkflowActionResultByWorkflowOperationGroupId(entity.WorkflowOperationGroupId);

            Assert.All(result, x => Assert.Equal(entity.WorkflowOperationGroupId, x.WorkflowOperationGroupId));
        }

        [Fact]
        public async Task GetWorkflowActionResultByWorkflowOperationId_ReturnsResults()
        {
            var entity = _fixture.WorkflowActionResultDto;
            await  _dbContext.WorkflowActionResults.AddAsync(entity);
           await  _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.GetWorkflowActionResultByWorkflowOperationId(entity.WorkflowOperationGroupId);

            Assert.All(result, x => Assert.Equal(entity.WorkflowOperationGroupId, x.WorkflowOperationId));
        }

        [Fact]
        public async Task GetWorkflowActionResultByWorkflowOperationIds_ReturnsResults()
        {
            var entity = _fixture.WorkflowActionResultDto;
           
            await  _dbContext.WorkflowActionResults.AddAsync(entity);
await  _dbContext.SaveChangesAsync();;

            var result = await _repositoryParent.GetWorkflowActionResultByWorkflowOperationIds(new List<string> { entity.WorkflowOperationId });

            Assert.All(result, x => Assert.Equal(entity.WorkflowOperationId, x.WorkflowOperationId));
        }

        [Fact]
        public async Task GetByWorkflowOperationIds_ReturnsResults()
        {
            var entity = _fixture.WorkflowActionResultDto;
            await  _dbContext.WorkflowActionResults.AddAsync(entity);
           await  _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.GetByWorkflowOperationIds(new List<string> { entity.WorkflowOperationId });

            Assert.All(result, x => Assert.Equal(entity.WorkflowOperationId, x.WorkflowOperationId));
        }

        [Fact]
        public async Task GetPaginatedQuery_ReturnsActiveResultsOrdered()
        {
            
           await _dbContext.WorkflowActionResults.AddRangeAsync(_fixture.WorkflowActionResultPaginationList);
       
            var result =  _repositoryParent.GetPaginatedQuery().ToList();

            Assert.All(result, x => Assert.True(x.IsActive));
            Assert.True(result.SequenceEqual(result.OrderByDescending(x => x.Id)));
        }

        [Fact]
        public async Task IsWorkflowActionResultNameExist_ReturnsTrue_WhenNameExists_AndIdIsInvalidGuid()
        {
            var entity = _fixture.WorkflowActionResultDto;
            entity.WorkflowActionName = "TestName";
           await  _dbContext.WorkflowActionResults.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.IsWorkflowActionResultNameExist("TestName", "invalid-guid");

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowActionResultNameExist_ReturnsFalse_WhenNameDoesNotExist_AndIdIsInvalidGuid()
        {
            var result = await _repositoryParent.IsWorkflowActionResultNameExist("NonExistent", "invalid-guid");

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowActionResultNameExist_ReturnsExpected_WhenIdIsValidGuid()
        {
            var id = Guid.NewGuid().ToString();
            var entity = _fixture.WorkflowActionResultDto;
            entity.ReferenceId = id;
            entity.WorkflowActionName = "UniqueName";
            await _dbContext.WorkflowActionResults.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.IsWorkflowActionResultNameExist("UniqueName", id);

            Assert.False(result);
        }

        [Fact]
        public async Task IsDependWorkflowDetails_ReturnsTrue_WhenStatusMatches()
        {
            var entity = _fixture.WorkflowActionResultDto;
            entity.WorkflowOperationId = "O4";
            entity.WorkflowOperationGroupId = "G4";
            entity.StepId = "S4";
            entity.Status = "Success";
            await  _dbContext.WorkflowActionResults.AddAsync(entity);
           await  _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.IsDependWorkflowDetails("O4", "G4", "S4");

            Assert.True(result);
        }

        [Fact]
        public async Task IsDependWorkflowDetails_ReturnsFalse_WhenNoMatch()
        {
            var result = await _repositoryParent.IsDependWorkflowDetails("O5", "G5", "S5");

            Assert.False(result);
        }

        [Fact]
        public async Task GetByInfraObjectAndActionId_ReturnsResult_WhenIsParent()
        {
           
            var entity = _fixture.WorkflowActionResultDto;
            entity.InfraObjectId = "I1";
            entity.ActionId = "A1";
            await  _dbContext.WorkflowActionResults.AddAsync(entity);
           await  _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.GetByInfraObjectAndActionId("I1", "A1");

            Assert.NotNull(result);
            Assert.Equal("I1", result.InfraObjectId);
            Assert.Equal("A1", result.ActionId);
        }

        [Fact]
        public async Task GetWorkflowActionResultByWorkflowOperationIdAndGroupId_ReturnsResults()
        {
            var entity = _fixture.WorkflowActionResultDto;
            entity.WorkflowOperationId = "O6";
            entity.WorkflowOperationGroupId = "G6";
            await  _dbContext.WorkflowActionResults.AddAsync(entity);
           await  _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.GetWorkflowActionResultByWorkflowOperationIdAndGroupId("O6", "G6");

            Assert.All(result, x => Assert.Equal("O6", x.WorkflowOperationId));
            Assert.All(result, x => Assert.Equal("G6", x.WorkflowOperationGroupId));
        }
    }
}