﻿using ContinuityPatrol.Application.Features.LicenseInfo.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.LicenseInfo.Commands;

public class UpdateLicenseInfoTests : IClassFixture<LicenseInfoFixture>
{
    private readonly LicenseInfoFixture _licenseInfoFixture;

    private readonly Mock<ILicenseInfoRepository> _mockLicenseInfoRepository;

    private readonly UpdateLicenseInfoCommandHandler _handler;

    public UpdateLicenseInfoTests(LicenseInfoFixture licenseInfoFixture)
    {
        _licenseInfoFixture = licenseInfoFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockLicenseInfoRepository = new Mock<ILicenseInfoRepository>();

        _mockLicenseInfoRepository = LicenseInfoRepositoryMocks.UpdateLicenseInfoRepository(_licenseInfoFixture.LicenseInfos);

        _handler = new UpdateLicenseInfoCommandHandler(_mockLicenseInfoRepository.Object, _licenseInfoFixture.Mapper, mockPublisher.Object);

        _licenseInfoFixture.LicenseInfos[0].EntityId = "c5e4d02-dcf9-4634-8c4c-a2cf0e1b1c26";


    }

    [Fact]
    public async Task Handle_ValidLicenseInfo_UpdateToLicenseInfosRepo()
    {
        _licenseInfoFixture.UpdateLicenseInfoCommand.EntityId = _licenseInfoFixture.LicenseInfos[0].EntityId;

        var result = await _handler.Handle(_licenseInfoFixture.UpdateLicenseInfoCommand, CancellationToken.None);

        var licenseInfo =  _mockLicenseInfoRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_licenseInfoFixture.UpdateLicenseInfoCommand.EntityName, licenseInfo.Result.EntityName);
    }

    [Fact]
    public async Task Handle_Return_ValidLicenseInfoResponse_WhenUpdate_LicenseInfo()
    {
        _licenseInfoFixture.UpdateLicenseInfoCommand.EntityId = _licenseInfoFixture.LicenseInfos[0].EntityId;

        var result = await _handler.Handle(_licenseInfoFixture.UpdateLicenseInfoCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateLicenseInfoResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidLicenseInfoId()
    {
        _licenseInfoFixture.UpdateLicenseInfoCommand.EntityId = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_licenseInfoFixture.UpdateLicenseInfoCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateEntityIdAsyncMethod_OnlyOnce()
    {
        _licenseInfoFixture.UpdateLicenseInfoCommand.EntityId = _licenseInfoFixture.LicenseInfos[0].EntityId;

        await _handler.Handle(_licenseInfoFixture.UpdateLicenseInfoCommand, CancellationToken.None);

        _mockLicenseInfoRepository.Verify(x => x.GetByEntityId(It.IsAny<string>()), Times.Once);

        _mockLicenseInfoRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.LicenseInfo>()), Times.Once);
    }
}