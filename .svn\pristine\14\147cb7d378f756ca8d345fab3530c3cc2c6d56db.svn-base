﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatus;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperationGroup.Queries;

public class GetWorkflowOperationGroupRunningStatusQueryHandlerTests : IClassFixture<WorkflowOperationGroupFixture>, IClassFixture<WorkflowOperationFixture>, IClassFixture<WorkflowActionResultFixture>
{
    private readonly WorkflowOperationGroupFixture _workflowOperationGroupFixture;

    private readonly WorkflowOperationFixture _workflowOperationFixture;

    private Mock<IWorkflowOperationGroupRepository> _mockWorkflowOperationGroupRepository;

    private readonly Mock<IWorkflowOperationRepository> _mockWorkflowOperationRepository;

    private readonly Mock<IWorkflowActionResultRepository> _mockWorkflowActionResultRepository;

    private readonly Mock<IInfraObjectRepository> _mockInfraObjectRepository;

    private readonly Mock<ILoadBalancerRepository> _mockLoadBalancerRepository;

    private readonly GetWorkflowOperationGroupRunningStatusQueryHandler _handler;

    public GetWorkflowOperationGroupRunningStatusQueryHandlerTests(WorkflowOperationGroupFixture workflowOperationGroupFixture, WorkflowOperationFixture workflowOperationFixture, WorkflowActionResultFixture workflowActionResultFixture)
    {
        _workflowOperationGroupFixture = workflowOperationGroupFixture;

        _workflowOperationFixture = workflowOperationFixture;

        _mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();

        _mockWorkflowOperationRepository = new Mock<IWorkflowOperationRepository>();

        _mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();

        _mockInfraObjectRepository = new Mock<IInfraObjectRepository>();

        _mockLoadBalancerRepository = new Mock<ILoadBalancerRepository>();

        _mockWorkflowOperationGroupRepository = WorkflowOperationGroupRepositoryMocks.GetWorkflowOperationGroupByWorkflowOperationId(_workflowOperationGroupFixture.WorkflowOperationGroups);

        _mockWorkflowOperationRepository = WorkflowOperationRepositoryMocks.GetWorkflowOperationGroupRunningStatus(_workflowOperationFixture.WorkflowOperations);

        _mockWorkflowActionResultRepository = WorkflowActionResultRepositoryMocks.GetWorkflowActionResultFromOperationGroupIdRepository(workflowActionResultFixture.WorkflowActionResults);

        _mockWorkflowActionResultRepository = WorkflowActionResultRepositoryMocks.GetWorkflowActionResultByWorkflowOperationId(workflowActionResultFixture.WorkflowActionResults);

        _handler = new GetWorkflowOperationGroupRunningStatusQueryHandler(_workflowOperationGroupFixture.Mapper, _mockWorkflowOperationGroupRepository.Object, _mockWorkflowOperationRepository.Object, _mockWorkflowActionResultRepository.Object, _mockInfraObjectRepository.Object, _mockLoadBalancerRepository.Object);

        _workflowOperationFixture.WorkflowOperations[0].Status = "running";
        _workflowOperationFixture.WorkflowOperations[1].Status = "running";
        _workflowOperationFixture.WorkflowOperations[2].Status = "running";
    }

    [Fact]
    public async Task Handle_ReturnWorkflowOperationGroupRunningStatus_When_ValidWorkflowOperationGroup()
    {
        _workflowOperationFixture.WorkflowOperations[0].ReferenceId = _workflowOperationGroupFixture.WorkflowOperationGroups[0].ReferenceId;

        var result = await _handler.Handle(new GetWorkflowOperationGroupRunningStatusQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowOperationGroupRunningStatusVm>>();
        result[0].Id.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].ReferenceId);
        result[0].ProfileId.ShouldBe(_workflowOperationFixture.WorkflowOperations[0].ProfileId);
        result[0].ProfileName.ShouldBe(_workflowOperationFixture.WorkflowOperations[0].ProfileName);
    }
    
    [Fact]
    public async Task Handle_Call_GetAllMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowOperationGroupRunningStatusQuery(), CancellationToken.None);

        _mockWorkflowOperationGroupRepository.Verify(x => x.GetWorkflowOperationGroupByWorkflowOperationId(It.IsAny<string>()), Times.Exactly(3));
    }

    //[Fact]
    //public async Task Handle_ThrowNotFoundException_When_Invalid_WorkflowOperationGroupRunningStatus()
    //{
    //    _mockWorkflowOperationGroupRepository = WorkflowOperationGroupRepositoryMocks.GetWorkflowOperationGroupEmptyRepository();

    //    var handler = new GetWorkflowOperationGroupRunningStatusQueryHandler(_workflowOperationGroupFixture.Mapper, _mockWorkflowOperationGroupRepository.Object, _mockWorkflowOperationRepository.Object, _mockWorkflowActionResultRepository.Object);

    //    await Assert.ThrowsAsync<ArgumentNullException>(() => handler.Handle(new GetWorkflowOperationGroupRunningStatusQuery(), CancellationToken.None));
    //}

}
