﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller
{
    public class HP3ParwithPostgressFullDBControllerShould
    {
        private readonly HP3ParwithPostgressFullDBController _controller;

        public HP3ParwithPostgressFullDBControllerShould()
        {
            
            _controller = new HP3ParwithPostgressFullDBController();
        }

        [Fact]
        public void List_ReturnsViewResult()
        {
            
            var result = _controller.List();

            
            var viewResult = Assert.IsType<ViewResult>(result);
            
        }
    }
}
