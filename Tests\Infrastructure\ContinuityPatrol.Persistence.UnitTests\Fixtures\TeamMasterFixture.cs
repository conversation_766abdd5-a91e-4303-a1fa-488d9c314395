using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class TeamMasterFixture : IDisposable
{
    public List<TeamMaster> TeamMasterPaginationList { get; set; }
    public List<TeamMaster> TeamMasterList { get; set; }
    public TeamMaster TeamMasterDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public TeamMasterFixture()
    {
        var fixture = new Fixture();

        TeamMasterList = fixture.Create<List<TeamMaster>>();

        TeamMasterPaginationList = fixture.CreateMany<TeamMaster>(20).ToList();

        TeamMasterDto = fixture.Create<TeamMaster>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public TeamMaster CreateTeamMaster(
        string groupName = "Default Team",
        string description = "Default Description",
        bool isActive = true,
        bool isDelete = false)
    {
        return new TeamMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GroupName = groupName,
            Description = description,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<TeamMaster> CreateMultipleTeamMasters(int count, string companyId = "COMPANY_123")
    {
        var teams = new List<TeamMaster>();
        for (int i = 1; i <= count; i++)
        {
            teams.Add(CreateTeamMaster(
                groupName: $"Team {i}",
                description: companyId
            ));
        }
        return teams;
    }

    public TeamMaster CreateTeamMasterWithSpecificId(string referenceId, string groupName = "Test Team")
    {
        return new TeamMaster
        {
            ReferenceId = referenceId,
            GroupName = groupName,
            Description = "Test Description",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public TeamMaster CreateTeamMasterForCompany(string companyId, string groupName = null)
    {
        return CreateTeamMaster(
            groupName: groupName ?? $"Team for {companyId}",
            description: companyId
        );
    }

    public List<TeamMaster> CreateTeamMastersForCompanies(List<string> companyIds)
    {
        var teams = new List<TeamMaster>();
        foreach (var companyId in companyIds)
        {
            teams.Add(CreateTeamMasterForCompany(companyId));
        }
        return teams;
    }

    public List<TeamMaster> CreateTeamMastersWithStatus(int activeCount, int inactiveCount, string companyId = "COMPANY_123")
    {
        var teams = new List<TeamMaster>();

        for (int i = 1; i <= activeCount; i++)
        {
            teams.Add(CreateTeamMaster(
                groupName: $"Active Team {i}",
                description: companyId,
                isActive: true
            ));
        }

        for (int i = 1; i <= inactiveCount; i++)
        {
            teams.Add(CreateTeamMaster(
                groupName: $"Inactive Team {i}",
                description: companyId,
                isActive: false
            ));
        }

        return teams;
    }

    public TeamMaster CreateDevelopmentTeam(string companyId = "COMPANY_123")
    {
        return CreateTeamMaster(
            groupName: "Development Team",
            description: companyId
        );
    }

    public TeamMaster CreateQATeam(string companyId = "COMPANY_123")
    {
        return CreateTeamMaster(
            groupName: "QA Team",
            description: companyId
        );
    }

    public TeamMaster CreateDevOpsTeam(string companyId = "COMPANY_123")
    {
        return CreateTeamMaster(
            groupName: "DevOps Team",
            description: companyId
        );
    }

    public List<TeamMaster> CreateStandardTeams(string companyId = "COMPANY_123")
    {
        return new List<TeamMaster>
        {
            CreateDevelopmentTeam(companyId),
            CreateQATeam(companyId),
            CreateDevOpsTeam(companyId)
        };
    }

    public TeamMaster CreateMinimalTeamMaster()
    {
        return new TeamMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GroupName = "Minimal Team",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };
    }

    public TeamMaster CreateTeamMasterForTesting(
        string testName,
        string groupName = null,
        string description = null)
    {
        return CreateTeamMaster(
            groupName: groupName ?? $"Test Team for {testName}",
            description: description ?? $"Description for {testName}"
        );
    }

    public List<TeamMaster> CreateTeamMastersForOrdering()
    {
        return new List<TeamMaster>
        {
            CreateTeamMaster(groupName: "Team Z", description: "COMPANY_123"),
            CreateTeamMaster(groupName: "Team A", description: "COMPANY_123"),
            CreateTeamMaster(groupName: "Team M", description: "COMPANY_123"),
            CreateTeamMaster(groupName: "Team B", description: "COMPANY_123")
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
