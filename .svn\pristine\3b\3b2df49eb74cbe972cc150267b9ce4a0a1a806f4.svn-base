using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberComponentGroupModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Cyber;

public class CyberComponentGroupService : BaseClient, ICyberComponentGroupService
{
    public CyberComponentGroupService(IConfiguration config, IAppCache cache, ILogger<CyberComponentGroupService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<CyberComponentGroupListVm>> GetCyberComponentGroupList()
    {
        var request = new RestRequest("api/v6/cybercomponentgroups");

        return await GetFromCache<List<CyberComponentGroupListVm>>(request, "GetCyberComponentGroupList");
    }

    public async Task<BaseResponse> CreateAsync(CreateCyberComponentGroupCommand createCyberComponentGroupCommand)
    {
        var request = new RestRequest("api/v6/cybercomponentgroups", Method.Post);

        request.AddJsonBody(createCyberComponentGroupCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateCyberComponentGroupCommand updateCyberComponentGroupCommand)
    {
        var request = new RestRequest("api/v6/cybercomponentgroups", Method.Put);

        request.AddJsonBody(updateCyberComponentGroupCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/cybercomponentgroups/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<CyberComponentGroupDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/cybercomponentgroups/{id}");

        return await Get<CyberComponentGroupDetailVm>(request);
    }
    #region NameExist
    public async Task<bool> IsCyberComponentGroupNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/cybercomponentgroups/name-exist?cybercomponentgroupName={name}&id={id}");

        return await Get<bool>(request);
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<CyberComponentGroupListVm>> GetPaginatedCyberComponentGroups(GetCyberComponentGroupPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/cybercomponentgroups/paginated-list");

        return await Get<PaginatedResult<CyberComponentGroupListVm>>(request);
    }
    #endregion
}
