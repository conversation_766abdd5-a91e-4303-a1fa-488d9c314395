using AutoMapper;
using ContinuityPatrol.Application.Contracts.Job;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Infrastructure.Identity;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperationGroup.Commands;

public class UpdateBulkImportOperationGroupTests : IClassFixture<BulkImportOperationGroupFixture>
{
    private readonly BulkImportOperationGroupFixture _bulkImportOperationGroupFixture;
    private readonly Mock<IBulkImportOperationGroupRepository> _mockBulkImportOperationGroupRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IPublisher> _publisher;
    private readonly Mock<IQuartzJobScheduler> _client;
    private readonly Mock<ILoggedInUserService> _loggedInUserService;
    private readonly UpdateBulkImportOperationGroupCommandHandler _handler;

    public UpdateBulkImportOperationGroupTests(BulkImportOperationGroupFixture bulkImportOperationGroupFixture)
    {
        _bulkImportOperationGroupFixture = bulkImportOperationGroupFixture;

        _mockBulkImportOperationGroupRepository = BulkImportOperationGroupRepositoryMocks.CreateUpdateBulkImportOperationGroupRepository(_bulkImportOperationGroupFixture.BulkImportOperationGroups);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map(It.IsAny<object>(), It.IsAny<object>(), It.IsAny<Type>(), It.IsAny<Type>()))
            .Callback<object, object, Type, Type>((source, destination, srcType, destType) =>
            {
                if (source is UpdateBulkImportOperationGroupCommand cmd && destination is Domain.Entities.BulkImportOperationGroup entity)
                {
                    entity.BulkImportOperationId = cmd.BulkImportOperationId;
                    entity.CompanyId = cmd.CompanyId;
                    entity.Properties = cmd.Properties;
                    entity.Status = cmd.Status;
                    entity.ProgressStatus = cmd.ProgressStatus;
                    entity.ErrorMessage = cmd.ErrorMessage;
                    entity.ConditionalOperation = cmd.ConditionalOperation;
                    entity.NodeId = cmd.NodeId;
                    entity.InfraObjectName = cmd.InfraObjectName;
                }
            });

        // Setup default repository behaviors
        //_mockBulkImportOperationGroupRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
        //    .Returns(Task.CompletedTask);

        _publisher = new Mock<IPublisher>();

        _handler = new UpdateBulkImportOperationGroupCommandHandler(
            _mockMapper.Object, _client.Object,
            _mockBulkImportOperationGroupRepository.Object, _publisher!.Object,   _loggedInUserService.Object );

        
    }

    [Fact]
    public async Task Handle_Return_UpdateBulkImportOperationGroupResponse_When_BulkImportOperationGroupUpdated()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var command = new UpdateBulkImportOperationGroupCommand 
        { 
            Id = existingGroup.ReferenceId,
            InfraObjectName = "UpdatedInfraObject"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(UpdateBulkImportOperationGroupResponse));
        result.Id.ShouldBe(existingGroup.ReferenceId);
        result.Message.ShouldContain("BulkImportOperationGroup");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var command = new UpdateBulkImportOperationGroupCommand { Id = existingGroup.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_UpdateAsync_OnlyOnce()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var command = new UpdateBulkImportOperationGroupCommand { Id = existingGroup.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var command = new UpdateBulkImportOperationGroupCommand { Id = existingGroup.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map(It.IsAny<object>(), It.IsAny<object>(),
            It.IsAny<Type>(), It.IsAny<Type>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportOperationGroupNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new UpdateBulkImportOperationGroupCommand { Id = nonExistentId };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportOperationGroup)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_UpdateEntityProperties_When_CommandMapped()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var command = new UpdateBulkImportOperationGroupCommand 
        { 
            Id = existingGroup.ReferenceId,
            BulkImportOperationId = "UpdatedOperationId",
            CompanyId = "UpdatedCompanyId",
            Properties = "{\"updated\":\"value\"}",
            Status = "Completed",
            ProgressStatus = "5/5",
            ErrorMessage = "No errors",
            ConditionalOperation = 2,
            NodeId = "UpdatedNode",
            InfraObjectName = "UpdatedInfraObject"
        };

        Domain.Entities.BulkImportOperationGroup capturedEntity = null;
        //_mockBulkImportOperationGroupRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
        //    .Callback<Domain.Entities.BulkImportOperationGroup>(entity => capturedEntity = entity)
        //    .Returns(_mockBulkImportOperationGroupRepository.BulkImportOperationGroups.First());

        _mockBulkImportOperationGroupRepository
      .Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
      .Callback<Domain.Entities.BulkImportOperationGroup>(entity => capturedEntity = entity)
      .ReturnsAsync((Domain.Entities.BulkImportOperationGroup entity) => entity);
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEntity.ShouldNotBeNull();
        capturedEntity.BulkImportOperationId.ShouldBe("UpdatedOperationId");
        capturedEntity.CompanyId.ShouldBe("UpdatedCompanyId");
        capturedEntity.Properties.ShouldBe("{\"updated\":\"value\"}");
        capturedEntity.Status.ShouldBe("Completed");
        capturedEntity.ProgressStatus.ShouldBe("5/5");
        capturedEntity.ErrorMessage.ShouldBe("No errors");
        capturedEntity.ConditionalOperation.ShouldBe(2);
        capturedEntity.NodeId.ShouldBe("UpdatedNode");
        capturedEntity.InfraObjectName.ShouldBe("UpdatedInfraObject");
    }

    [Fact]
    public async Task Handle_CreateResponseWithCorrectMessage_When_BulkImportOperationGroupUpdated()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        existingGroup.InfraObjectName = "TestInfraObject";
        var command = new UpdateBulkImportOperationGroupCommand 
        { 
            Id = existingGroup.ReferenceId,
            InfraObjectName = "TestInfraObject"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldContain("BulkImportOperationGroup");
        result.Message.ShouldContain("TestInfraObject");
        result.Id.ShouldBe(existingGroup.ReferenceId);
    }

    [Fact]
    public async Task Handle_CallRepositoryWithCorrectId_When_QueryExecuted()
    {
        // Arrange
        var testId = Guid.NewGuid().ToString();
        var command = new UpdateBulkImportOperationGroupCommand { Id = testId };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetByReferenceIdAsync(testId))
            .ReturnsAsync(_bulkImportOperationGroupFixture.BulkImportOperationGroups.First());

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.GetByReferenceIdAsync(testId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_UpdateSuccessful()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var command = new UpdateBulkImportOperationGroupCommand { Id = existingGroup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<UpdateBulkImportOperationGroupResponse>();
        result.GetType().ShouldBe(typeof(UpdateBulkImportOperationGroupResponse));
    }

    [Fact]
    public async Task Handle_UpdateProgressStatus_When_CommandProvided()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var command = new UpdateBulkImportOperationGroupCommand 
        { 
            Id = existingGroup.ReferenceId,
            ProgressStatus = "3/10",
            Status = "In Progress"
        };

        Domain.Entities.BulkImportOperationGroup capturedEntity = null;
        _mockBulkImportOperationGroupRepository
     .Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
     .Callback<Domain.Entities.BulkImportOperationGroup>(entity => capturedEntity = entity)
     .ReturnsAsync((Domain.Entities.BulkImportOperationGroup entity) => entity); // Works for Task<T>


        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEntity.ProgressStatus.ShouldBe("3/10");
        capturedEntity.Status.ShouldBe("In Progress");
    }
}
