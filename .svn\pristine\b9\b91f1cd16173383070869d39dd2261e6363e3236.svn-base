using ContinuityPatrol.Domain.ViewModels.AboutCpModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class AboutCpFixture
{
    public List<GetAboutCpListVm> AboutCpListVm { get; }
    public GetAboutCpListVm AboutCpDetailVm { get; }

    public AboutCpFixture()
    {
        var fixture = new Fixture();

        // Create sample AboutCP data with realistic values
        AboutCpListVm = new List<GetAboutCpListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = "6.0.0",
                LicenseType = "Enterprise",
                ProductId = Guid.NewGuid().ToString(),
                ProductActivatedDate = DateTime.Now.AddDays(-90),
                RemainingDays = "275",
                AboutProduct = "Continuity Patrol™, part of Perpetuuiti's Resiliency Automation platform, empowers organizations to swiftly recover from disruptions or outages, boasting the industry's lowest Recovery Point Objectives (RPOs) and fastest Recovery Time Objectives (RTOs). This effectively minimizes data loss and downtime, ensuring seamless operational continuity."
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = "6.0.1",
                LicenseType = "Standard",
                ProductId = Guid.NewGuid().ToString(),
                ProductActivatedDate = DateTime.Now.AddDays(-180),
                RemainingDays = "185",
                AboutProduct = "Continuity Patrol™ Standard Edition provides essential disaster recovery capabilities with automated failover and recovery processes. Designed for mid-sized organizations requiring reliable business continuity solutions."
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = "6.0.2",
                LicenseType = "Professional",
                ProductId = Guid.NewGuid().ToString(),
                ProductActivatedDate = DateTime.Now.AddDays(-45),
                RemainingDays = "320",
                AboutProduct = "Continuity Patrol™ Professional Edition offers advanced monitoring, reporting, and automation features for comprehensive disaster recovery management. Includes enhanced security and compliance capabilities."
            }
        };

        // Create a single detailed AboutCP item
        AboutCpDetailVm = new GetAboutCpListVm
        {
            Id = Guid.NewGuid().ToString(),
            ProductVersion = "6.0.0",
            LicenseType = "Enterprise",
            ProductId = Guid.NewGuid().ToString(),
            ProductActivatedDate = DateTime.Now.AddDays(-30),
            RemainingDays = "335",
            AboutProduct = "Continuity Patrol™, part of Perpetuuiti's Resiliency Automation platform, empowers organizations to swiftly recover from disruptions or outages, boasting the industry's lowest Recovery Point Objectives (RPOs) and fastest Recovery Time Objectives (RTOs). This effectively minimizes data loss and downtime, ensuring seamless operational continuity. The Enterprise edition includes advanced features such as multi-site replication, automated disaster recovery orchestration, comprehensive monitoring dashboards, and 24/7 support services."
        };
    }
}
