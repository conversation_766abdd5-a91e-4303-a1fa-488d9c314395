using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class MenuBuilderFilterSpecification : Specification<MenuBuilder>
{
    public MenuBuilderFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.Name != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)          
                	if (stringItem.Contains("name=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "",StringComparison.OrdinalIgnoreCase)));
					else if (stringItem.Contains("properties=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Properties.Contains(stringItem.Replace("properties=", "",StringComparison.OrdinalIgnoreCase)));
					else if (stringItem.Contains("state=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.State.Contains(stringItem.Replace("state=", "",StringComparison.OrdinalIgnoreCase)));
                  
            }
            else
            {
                Criteria = p =>p.Name.Contains(searchString)||p.Properties.Contains(searchString)||p.State.Contains(searchString);
            }
        }
    }
}
