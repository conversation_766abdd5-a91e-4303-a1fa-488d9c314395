﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.LoadBalancer.Events.Delete;

public class LoadBalancerDeletedEventHandler : INotificationHandler<LoadBalancerDeletedEvent>
{
    private readonly ILogger<LoadBalancerDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public LoadBalancerDeletedEventHandler(ILoggedInUserService userService,
        ILogger<LoadBalancerDeletedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(LoadBalancerDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.LoadBalancer}",
            Entity = Modules.LoadBalancer.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"{deletedEvent.TypeCategory} ' {deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"{deletedEvent.TypeCategory} '{deletedEvent.Name}' deleted successfully.");
    }
}