﻿namespace ContinuityPatrol.Web.Areas.ITAutomation.Controllers;

[Area("ITAutomation")]
public class WorkFlowHistoryController : BaseController
{
    private readonly ILogger<WorkFlowHistoryController> _logger;

    public WorkFlowHistoryController(ILogger<WorkFlowHistoryController> logger)
    {
        _logger = logger;
    }

    public IActionResult List()
    {
        _logger.LogDebug("Entering List method in WorkFlow History");

        return View();
    }
}