﻿namespace ContinuityPatrol.Domain.Entities;

public class LicenseHistory : AuditableEntity
{
    [Column("ComplianceId")] public string LicenseId { get; set; }

    [Column("ContractCode", TypeName = "NCLOB")] public string PONumber { get; set; }

    public string CompanyId { get; set; }

    public string CompanyName { get; set; }

    [Column("AccessPointName", TypeName = "NCLOB")] public string CPHostName { get; set; }

    [Column("Specifications", TypeName = "NCLOB")] public string Properties { get; set; }

    [Column("AccessPoint", TypeName = "NCLOB")] public string IPAddress { get; set; }

    [Column("AccessHardware", TypeName = "NCLOB")] public string MACAddress { get; set; }

    [Column("Permit", TypeName = "NCLOB")] public string LicenseKey { get; set; }

    public bool IsParent { get; set; }

    [Column(TypeName = "NCLOB")] public string ParentId { get; set; }

    [Column("Applicability", TypeName = "NCLOB")] public string Validity { get; set; }

    [Column("Withdrawal", TypeName = "NCLOB")] public string ExpiryDate { get; set; }

    [Column("ParentContractCode", TypeName = "NCLOB")] public string ParentPONumber { get; set; }

    public string UpdaterId { get; set; }
    public bool IsState { get; set; }
}