﻿using ContinuityPatrol.Application.Features.Report.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.ReportModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.Report.Queries;

public class GetReportPaginatedListQueryHandlerTests : IClassFixture<ReportFixture>
{
    private readonly GetReportPaginatedListQueryHandler _handler;

    private readonly Mock<IReportRepository> _mockReportRepository;

    public GetReportPaginatedListQueryHandlerTests(ReportFixture reportFixture)
    {
        var reportNewFixture = reportFixture;

        reportNewFixture.Reports[0].Name = "Summary_Report";
        reportNewFixture.Reports[0].Description = "Testing";
        reportNewFixture.Reports[0].Design = "DR_Drill";
        reportNewFixture.Reports[0].DataSet = "Events";
        reportNewFixture.Reports[0].FilterColumn = "Row";
        reportNewFixture.Reports[0].HeaderColumn = "Report";

        reportNewFixture.Reports[1].Name = "Dependency_Report";
        reportNewFixture.Reports[1].Description = "Basic";
        reportNewFixture.Reports[1].Design = "PR_Drill";
        reportNewFixture.Reports[1].DataSet = "Result";
        reportNewFixture.Reports[1].FilterColumn = "Column";
        reportNewFixture.Reports[1].HeaderColumn = "Path";

        _mockReportRepository = ReportRepositoryMocks.GetPaginatedReportRepository(reportNewFixture.Reports);

        _handler = new GetReportPaginatedListQueryHandler(reportNewFixture.Mapper, _mockReportRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetReportPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Vas" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ReportListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedReports_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetReportPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Test" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ReportListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<ReportListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("Summary_Report");

        result.Data[0].Description.ShouldBe("Testing");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetReportPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ReportListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_Reports_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetReportPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=Dependency_Report;description=Basic;design=PR_Drill;dataSet=Result;filterColumn=Column;headerColumn=Path" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ReportListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("Dependency_Report");

        result.Data[0].HeaderColumn.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Description.ShouldBe("Basic");

        result.Data[0].Design.ShouldBe("PR_Drill");

        result.Data[0].DataSet.ShouldBe("Result");

        result.Data[0].FilterColumn.ShouldBe("Column");

        result.Data[0].HeaderColumn.ShouldBe("Path");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetReportPaginatedListQuery(), CancellationToken.None);

        _mockReportRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}