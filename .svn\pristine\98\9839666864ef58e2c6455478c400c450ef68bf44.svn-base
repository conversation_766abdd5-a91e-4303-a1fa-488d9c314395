﻿@model ContinuityPatrol.Domain.ViewModels.UserModel.ChangePasswordModels.ChangePasswordViewModel
@using ContinuityPatrol.Shared.Services.Helper
@using Microsoft.Extensions.Configuration
@inject IConfiguration Configuration
@{
    ViewData["Title"] = "ChangePassword";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}
<link href="~/css/password_stregnth_meter.css" rel="stylesheet" />
@Html.AntiForgeryToken()
<body class="align-items-center d-grid vh-100">
    <div class="container-fluid h-100">
        <div class="h-100 row text-dark d-flex align-items-center justify-content-center">
            <div class="col-xl-6 col-md-6 order-md-2 order-lg-2 d-flex align-items-center justify-content-center">
                <div style="width:90%">
                    <div class="card login_card">
                        <div class="card-header" title="CP logo">
                            <img src="~/img/logo/cplogo.svg" alt="Logo" />
                        </div>
                        <form id="CreateForm" asp-controller="Account" asp-action="ChangePassword" method="post" enctype="multipart/form-data">
                            <div class="card-body">
                                <h4 class="text-primary fw-semibold">Change Password!</h4>
                                <div class="row">
                                    <div class="col-11">
                                        <div class="form-group">
                                            <label class="form-label">Current Password</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <img src="~/img/input_Icons/password-icon.svg" />
                                                </span>
                                                <input type="hidden" id="LoginName" asp-for="LoginName" data-loginnames="@WebHelper.UserSession.LoginName">
                                                <input type="hidden" id="loginId" asp-for="UserId" data-loginid="@WebHelper.UserSession.LoggedUserId">

                                                <input type="password" class="form-control" id="CurrentPassword" asp-for="OldPassword" maxlength="30" autocomplete="off" placeholder="Enter Current Password">
                                                <span class="input-group-text eye-change toggle-password"><i class="cp-password-visible fs-6"></i></span>
                                            </div>
                                            <span asp-validation-for="OldPassword" id="OldPassword-error"></span>
                                        </div>
                                    </div>
                                    <div class="col"></div>
                                </div>
                                <div class="row">
                                    <div class="col-11">
                                        <div class="form-group">
                                            <label class="form-label">New Password</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <img src="~/img/input_Icons/password-icon.svg" />
                                                </span>
                                                <input type="password" class="form-control" id="changeNewPassword" asp-for="NewPassword" maxlength="30" autocomplete="off" placeholder="Enter New Password">
                                                <span class="input-group-text eye-change toggle-password"><i class="cp-password-visible fs-6"></i></span>
                                            </div>
                                            <span asp-validation-for="NewPassword" id="NewPassword-error"></span>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div id="mycPass_strength_wrap" class="mt-4" style="top:43px;left:0">
                                            <div id="passwordDescription">Password not entered</div>
                                            <div id="passwordStrength" class="strength0"></div>
                                            <div id="pswd_info">
                                                <strong>Strong Password Tips:</strong>
                                                <ul>
                                                    <li class="invalid" id="length"></li>
                                                    <li class="invalid" id="pnum"></li>
                                                    <li class="invalid" id="capital"></li>
                                                    <li class="invalid" id="spchar"></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-11">
                                        <div class="form-group">
                                            <label class="form-label">Confirm Password</label>
                                            <div class="input-group">
                                                <span class="input-group-text ">
                                                    <img src="~/img/input_Icons/password-icon.svg" />
                                                </span>
                                                <input type="password" class="form-control" id="changeConfirmPassword" asp-for="ConfirmPassword" maxlength="30" autocomplete="off" placeholder="Enter Confirm Password">
                                            </div>
                                            <span asp-validation-for="ConfirmPassword" id="ConfirmPassword-error"></span>
                                            <span id="confirm_password_msg"></span>
                                        </div>
                                    </div>
                                    <div class="col"></div>
                                </div>
                            </div>
                            <div class="card-footer ">
                                <div class="row">
                                    <div class="col-11 text-end">
                                        <button type="button" class="btn btn-secondary px-5 me-2" id="reset_value">Reset</button>
                                        <button type="button" class="btn btn-primary px-5" id="SaveFunction">Change</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-xl-6 col-md-6 ">
                <div class="text-center">
                    @* <img src="~/img/isomatric/change-password-illustration.svg" alt="Login_ISO" class="w-100" /> *@


                    <svg width="539" height="401" fill="none"><g clip-path="url(#a)"><mask id="b" width="539" height="401" x="0" y="0" maskUnits="userSpaceOnUse" style="mask-type:luminance"><path fill="#fff" d="M538.641 0h-538v401h538z" /></mask><g mask="url(#b)"><mask id="c" width="539" height="401" x="0" y="0" maskUnits="userSpaceOnUse" style="mask-type:luminance"><path fill="#fff" d="M538.641 0h-538v401h538z" /></mask><g mask="url(#c)"><path fill="#EBEBEB" d="M538.641 352.352h-538v.216h538zM484.732 369.68h-23.349v.215h23.349zM371.536 364.195h-36.692v.216h36.692zM475.265 359.781h-47.882v.216h47.882zM186.788 365.164h-11.944v.215h11.944zM165.273 365.164h-41.32v.215h41.32zM101.782 360.641H71.977v.215h29.805z" /><path fill="#E6E6E6" d="M144.234 302.209h16.355V238.82h-16.355z" /><path fill="#F0F0F0" d="M99.938 302.244h54.23l8.178-74.26h-54.231z" /><path fill="#E0E0E0" d="M94.016 302.244h56.06l8.177-74.26h-56.059z" /><path fill="#fff" d="m145.99 296.427 6.994-62.529h-46.699l-6.887 62.529z" /><path fill="#E0E0E0" d="m136.408 281.255.323-2.583c1.722-15.712-8.07-29.703-20.767-29.703l-.322 2.583c-1.722 15.713 8.07 29.703 20.766 29.703M63.68 373.857h48.205v-71.568H63.68z" /><path fill="#F0F0F0" d="M68.514 373.807h-4.842v-13.776h9.9z" /><path fill="#E0E0E0" d="M198.867 373.857h48.205v-71.568h-48.205z" /><path fill="#E0E0E0" d="M63.64 365.385h139.45v-63.174H63.64z" /><path fill="#F0F0F0" d="M198.276 373.807h4.842v-13.776h-9.899z" /><path fill="#FBFBFB" d="M67.406 331.64h131.919v-25.507H67.406z" /><path fill="#E0E0E0" d="M124.573 322.795h17.646c7.64 0 14.957-3.121 20.229-8.717h-58.104a27.76 27.76 0 0 0 20.229 8.717" /><path fill="#FBFBFB" d="M67.406 361.24h131.919v-25.506H67.406z" /><path fill="#E0E0E0" d="M124.573 352.397h17.646c7.64 0 14.957-3.121 20.229-8.717h-58.104a27.76 27.76 0 0 0 20.229 8.717" /><path fill="#E6E6E6" d="M194.294 299.336h1.291c0-2.583 0-9.04.108-23.999.215-27.013.645-28.735.645-28.735l-3.012 12.484c-1.83 13.883.538 27.766.968 40.25" /><path fill="#E6E6E6" d="M191.82 257.256c-4.304-5.058-13.45-6.565-22.703-7.856-3.659-.538 9.684 7.856 16.14 13.883 5.057 4.628 8.285 5.919 9.684 10.547.645-5.812.538-12.161-3.121-16.574" /><path fill="#E6E6E6" d="M197.522 266.295c4.304-5.058 13.45-6.565 22.704-7.856 3.658-.538-9.684 7.856-16.14 13.883-5.058 4.628-8.286 5.919-9.684 10.547-.754-5.812-.646-12.161 3.12-16.574M195.694 245.417c3.228-6.134 11.298-11.838 20.336-13.99 3.551-.861-8.177 11.192-12.696 19.049-3.444 6.026-8.07 13.13-8.824 18.295-1.183-10.116-2.044-17.327 1.184-23.354" /><path fill="#F0F0F0" d="M195.26 302.243c-3.766 0-6.994-1.938-7.209-4.413l-.645-4.197h15.602l-.646 4.197c-.108 2.475-3.228 4.413-7.102 4.413" /><path fill="#F0F0F0" d="M204.082 295.353h-17.646l-.538-4.736h18.722z" /><path fill="#FAFAFA" d="M366.236 267.585h-30.128a5.787 5.787 0 0 1-5.811-5.812V247.89a5.787 5.787 0 0 1 5.811-5.812h30.128a5.787 5.787 0 0 1 5.81 5.812v13.883a5.787 5.787 0 0 1-5.81 5.812" /><path fill="#E6E6E6" d="M370.11 242.832h-37.768a1.93 1.93 0 0 1-1.936-1.937v-1.614c0-1.076.86-1.937 1.936-1.937h37.768a1.93 1.93 0 0 1 1.937 1.937v1.614a1.93 1.93 0 0 1-1.937 1.937" /><path fill="#F0F0F0" d="M330.945 295.891s.538 17.973 21.412 17.973 21.412-17.973 21.412-17.973zM378.609 295.891s.538 17.973 21.412 17.973c20.875 0 21.413-17.973 21.413-17.973z" /><path fill="#E0E0E0" d="M411.107 267.585H369.79a7.945 7.945 0 0 1-7.962-7.965v-21.093a7.946 7.946 0 0 1 7.962-7.965h41.317a7.947 7.947 0 0 1 7.964 7.965v21.093a7.947 7.947 0 0 1-7.964 7.965" /><path fill="#E0E0E0" d="M416.491 231.534h-51.863a2.664 2.664 0 0 1-2.69-2.691v-2.152a2.664 2.664 0 0 1 2.69-2.691h51.863a2.665 2.665 0 0 1 2.69 2.691v2.152c-.108 1.507-1.291 2.691-2.69 2.691" /><path fill="#E6E6E6" d="M315.273 373.865h5.38v-88.896h-5.38z" /><path fill="#E0E0E0" d="M471.789 267.586h-29.375v17.327h29.375z" /><path fill="#F5F5F5" d="M296.484 284.937h146.013v-17.328H296.484z" /><path fill="#E0E0E0" d="M471.789 313.867h-29.375v17.327h29.375z" /><path fill="#F5F5F5" d="M296.484 331.101h146.013v-17.328H296.484z" /><path fill="#E0E0E0" d="M461.242 373.865h5.38v-88.896h-5.38z" /><path fill="#F5F5F5" d="M437.062 373.865h5.381v-88.896h-5.381zM296.547 373.865h5.38v-88.896h-5.38zM269.645 401.002c115.226 0 208.636-5.444 208.636-12.161s-93.41-12.161-208.636-12.161c-115.227 0-208.637 5.445-208.637 12.161 0 6.717 93.41 12.161 208.637 12.161" /><path fill="#407BFF" d="M280.83 334.171H143.533c-4.519 0-8.393-3.659-8.608-8.179L122.658 87.609c-.215-4.52 3.228-8.18 7.748-8.18h137.297c4.519 0 8.393 3.66 8.608 8.18L288.578 326.1c.322 4.412-3.228 8.071-7.748 8.071" /><path fill="#fff" d="M280.83 334.171H143.533c-4.519 0-8.393-3.659-8.608-8.179L122.658 87.609c-.215-4.52 3.228-8.18 7.748-8.18h137.297c4.519 0 8.393 3.66 8.608 8.18L288.578 326.1c.322 4.412-3.228 8.071-7.748 8.071" opacity=".8" /><path fill="#A1BDFC" d="M286.749 319.1H155.477c-4.304 0-8.07-3.551-8.285-7.856L135.463 83.192c-.215-4.304 3.121-7.856 7.425-7.856H274.16c4.304 0 8.07 3.552 8.285 7.856l11.728 228.052c.216 4.305-3.12 7.856-7.424 7.856" /><path fill="#739EFD" d="M252.424 302.85h-64.345c-3.121 0-5.811-2.583-5.918-5.704l-.431-7.426c-.107-3.121 2.26-5.704 5.38-5.704h64.345c3.121 0 5.811 2.583 5.918 5.704l.431 7.426c.107 3.121-2.26 5.704-5.38 5.704" /><path fill="#407BFF" d="M286.747 313.827H155.475c-1.506 0-2.905-1.292-3.013-2.906L140.734 82.977c0-.86.323-1.399.538-1.722.323-.323.753-.646 1.614-.646h131.272c1.506 0 2.905 1.292 3.013 2.906l11.836 227.944c0 .861-.323 1.399-.538 1.722-.431.323-.861.646-1.722.646M142.886 80.825c-.753 0-1.184.322-1.399.538-.215.215-.538.753-.538 1.506l11.728 228.052c.108 1.399 1.399 2.583 2.798 2.583h131.272c.753 0 1.184-.323 1.399-.539.215-.215.538-.753.538-1.506L276.955 83.515c-.107-1.399-1.398-2.583-2.797-2.583H142.886z" /><path fill="#407BFF" d="M240.88 163.712c14.93-15.325 13.957-40.493-2.173-56.215s-41.311-16.043-56.241-.718-13.958 40.493 2.173 56.215c16.131 15.721 41.31 16.043 56.241.718" /><path fill="#fff" d="M243.815 135.281c.322 5.596-.861 10.762-3.121 15.282-4.949 10.009-15.171 16.789-27.438 16.789s-23.241-6.78-29.159-16.789c-2.69-4.52-4.412-9.794-4.627-15.282-.969-17.758 12.697-32.179 30.451-32.179s32.925 14.421 33.894 32.179" /><path fill="#407BFF" d="M240.692 150.563c-4.95 10.009-15.172 16.789-27.438 16.789-12.267 0-23.242-6.78-29.16-16.789 7.855-5.381 17.324-8.61 27.869-8.61s20.336 3.229 28.729 8.61M221.617 134.457c5.392-5.534 5.035-14.627-.795-20.31-5.831-5.683-14.929-5.804-20.32-.27-5.392 5.534-5.036 14.627.795 20.31s14.929 5.804 20.32.27" /><path fill="#fff" d="M267.922 230.419H165.164c-1.83 0-3.444-1.506-3.444-3.336l-.753-15.067c-.107-1.83 1.291-3.336 3.121-3.336h102.758c1.829 0 3.443 1.506 3.443 3.336l.753 15.067c.108 1.83-1.291 3.336-3.12 3.336" /><path fill="#000" d="M236.503 200.284h-42.932c-1.829 0-3.443-1.507-3.443-3.336l-.216-3.229c-.107-1.829 1.292-3.336 3.121-3.336h42.932c1.829 0 3.443 1.507 3.443 3.336l.216 3.229c.107 1.937-1.292 3.336-3.121 3.336" opacity=".3" /><path fill="#fff" d="M269.858 266.794H167.1c-1.829 0-3.443-1.506-3.443-3.336l-.753-15.067c-.108-1.83 1.291-3.336 3.12-3.336h102.758c1.829 0 3.443 1.506 3.443 3.336l.753 15.067c.108 1.83-1.291 3.336-3.12 3.336" /><path fill="#263238" d="M178.182 219.55a3.454 3.454 0 0 1-3.444 3.659c-1.936 0-3.767-1.614-3.874-3.659a3.453 3.453 0 0 1 3.444-3.659c1.937 0 3.766 1.614 3.874 3.659M189.478 219.55a3.454 3.454 0 0 1-3.444 3.659c-2.044 0-3.766-1.614-3.873-3.659a3.45 3.45 0 0 1 3.443-3.659c1.937 0 3.766 1.614 3.874 3.659M200.774 219.55a3.45 3.45 0 0 1-3.443 3.659c-2.044 0-3.766-1.614-3.873-3.659a3.45 3.45 0 0 1 3.443-3.659c1.937 0 3.766 1.614 3.873 3.659M212.08 219.547a3.453 3.453 0 0 1-3.443 3.659c-1.937 0-3.766-1.615-3.874-3.659a3.454 3.454 0 0 1 3.443-3.659c2.045-.108 3.874 1.614 3.874 3.659M180.119 255.925a3.45 3.45 0 0 1-3.443 3.659c-2.044 0-3.766-1.614-3.874-3.659a3.454 3.454 0 0 1 3.444-3.659c1.936 0 3.766 1.614 3.873 3.659M191.416 255.925a3.45 3.45 0 0 1-3.443 3.659c-2.045 0-3.766-1.614-3.874-3.659a3.454 3.454 0 0 1 3.443-3.659c1.937 0 3.766 1.614 3.874 3.659M202.713 255.925a3.454 3.454 0 0 1-3.443 3.659c-2.045 0-3.766-1.614-3.874-3.659a3.45 3.45 0 0 1 3.443-3.659c2.045 0 3.766 1.614 3.874 3.659M214.017 255.925a3.454 3.454 0 0 1-3.444 3.659c-2.044 0-3.766-1.614-3.873-3.659a3.45 3.45 0 0 1 3.443-3.659c2.044 0 3.766 1.614 3.874 3.659M225.313 255.925a3.45 3.45 0 0 1-3.443 3.659c-1.937 0-3.766-1.614-3.873-3.659a3.45 3.45 0 0 1 3.443-3.659c1.937 0 3.766 1.614 3.873 3.659M236.61 255.925a3.45 3.45 0 0 1-3.443 3.659c-2.044 0-3.766-1.614-3.873-3.659a3.45 3.45 0 0 1 3.443-3.659c1.937 0 3.766 1.614 3.873 3.659" /><path fill="#E58A7B" d="M358.952 130.977c.323.43.43.645.538.861l.43.753c.323.538.538 1.076.861 1.614.538 1.076 1.076 2.153 1.506 3.229.969 2.152 1.722 4.305 2.368 6.565a72.4 72.4 0 0 1 2.582 13.453l.108.861v1.076c0 .753 0 1.507-.108 2.26-.215 1.399-.538 2.798-1.076 3.982-.968 2.475-2.26 4.52-3.766 6.457-2.905 3.767-6.241 6.78-10.007 9.471l-3.013-3.444c2.69-3.121 5.273-6.457 7.102-9.901.968-1.722 1.722-3.444 2.152-5.058q.323-1.292.323-2.26v-1.507l-.108-.754c-.753-4.089-1.614-7.964-2.905-11.838-.646-1.937-1.399-3.767-2.152-5.596-.43-.969-.861-1.83-1.291-2.691l-.646-1.291-.323-.646-.322-.431z" /><path fill="#E58A7B" d="m348.945 181.667 3.873 8.179 3.121-6.995s-2.69-6.027-6.456-5.489zM355.617 190.271l3.121-5.058-2.798-2.26-3.12 6.995z" /><path fill="#407BFF" d="M338.075 380.227c.86 0 1.721-.215 2.367-.323.107 0 .107-.107.107-.215 0-.107 0-.215-.107-.215-.323-.215-3.013-1.937-4.089-1.507-.215.108-.323.215-.43.646-.108.43 0 .861.322 1.184.538.323 1.184.43 1.83.43m1.829-.645c-1.614.322-2.798.215-3.228-.108-.215-.215-.215-.431-.215-.753 0-.216.107-.216.215-.323.538-.323 2.044.538 3.228 1.184" /><path fill="#407BFF" d="M340.441 379.902c.215-.108.215-.107.215-.214 0-.109 0-2.692-.968-3.553-.216-.215-.538-.323-.861-.323-.538.109-.646.431-.753.646-.216.969 1.398 2.906 2.367 3.444m-1.507-3.659c.216 0 .323.107.431.215.538.538.753 1.938.861 2.799-.861-.645-1.83-2.153-1.722-2.799 0-.108 0-.215.43-.215q-.16 0 0 0M327.961 375.812c0-.108.108-.108 0-.215 0-.108-.107-.108-.215-.108-.43 0-4.304-.215-5.165.753v.108c-.107.215-.215.43-.107.646.107.43.43.645.753.753 1.291.323 3.443-1.076 4.734-1.937m-5.057.646c.43-.538 2.582-.646 4.196-.646-1.721 1.184-3.012 1.722-3.766 1.507-.215-.108-.43-.216-.538-.538 0-.108 0-.216.108-.323" /><path fill="#407BFF" d="M327.961 375.814c.108-.107.108-.215 0-.215 0-.108-1.291-2.26-2.69-2.583-.43-.108-.861-.108-1.183.108-.538.322-.538.645-.431.861.323.968 3.013 1.829 4.197 1.829zm-3.873-2.152s.107-.108.215-.108c.323-.215.538-.215.861-.108.968.216 1.829 1.4 2.152 2.045-1.292-.107-3.228-.968-3.336-1.506 0-.108 0-.216.108-.323" /><path fill="#E58A7B" d="M339.798 99.445c-1.076 5.381-3.444 17.005.322 20.556 0 0-1.506 5.381-11.405 5.381-10.975 0-5.273-5.381-5.273-5.381 5.918-1.399 5.811-5.811 4.735-10.009z" /><path fill="#263238" d="M321.612 86.851h.215c.215-.108.323-.323.215-.538-1.076-2.152-3.012-2.368-3.12-2.368-.215 0-.43.108-.43.323s.107.43.322.43c.108 0 1.614.216 2.475 1.938 0 .108.108.215.323.215" /><path fill="#E1AD8A" d="M318.276 91.477s-1.614 2.905-3.12 4.304c.86.97 2.474.754 2.474.754z" /><path fill="#263238" d="M319.135 90.296c-.107.753-.538 1.291-.968 1.183-.431-.107-.753-.645-.646-1.399.108-.753.538-1.291.969-1.183s.753.753.645 1.399" /><path fill="#263238" d="m318.598 89.003-1.614-.753s.753 1.507 1.614.753" /><path fill="#E58A7B" d="m337.432 373.772-8.716 2.045-5.38-20.233 8.824-2.045zM350.875 379.69h-8.93l.645-20.878h9.039z" /><path fill="#263238" d="M341.196 378.617h10.115c.323 0 .645.216.753.646l1.184 7.964c.107.861-.646 1.614-1.399 1.614-3.551-.107-5.273-.215-9.684-.215-2.69 0-6.779.323-10.545.323-3.658 0-3.981-3.767-2.367-4.09 7.101-1.506 8.177-3.659 10.545-5.596.322-.43.86-.646 1.398-.646" /><path fill="#263238" d="m327.853 374.419 9.146-4.197c.323-.108.753 0 .861.215l4.412 6.78c.43.754.107 1.722-.646 2.045-3.228 1.399-4.842 1.937-8.931 3.767-2.475 1.184-7.532 3.767-10.975 5.381-3.336 1.507-5.165-1.722-3.874-2.69 5.811-4.305 7.425-7.426 8.716-10.225.43-.43.861-.861 1.291-1.076" /><path fill="#000" d="m323.336 355.584 2.906 10.87 8.608-2.476-2.69-10.439zM351.634 358.812h-9.038l-.323 10.762h9.038z" opacity=".2" /><path fill="#287AFF" d="M355.72 122.041c3.766.861 7.749 14.314 7.749 14.314l-10.546 11.3s-8.823-12.7-6.993-17.327c1.721-4.843 5.271-9.256 9.79-8.287" /><path fill="#000" d="M349.694 130.651c-1.937-1.722-3.121-1.183-4.089.108-.861 3.982 4.196 12.377 6.348 15.605.969-3.766 2.045-11.838-2.259-15.713" opacity=".4" /><path fill="#287AFF" d="M308.271 122.369s-4.304 1.507 4.304 54.457h36.584c-.646-14.852-.646-24.107 6.456-54.672 0 0-7.64-1.722-15.495-2.045-6.133-.323-11.19-.538-16.57 0-7.209.538-15.279 2.26-15.279 2.26" /><path fill="#000" d="M307.945 142.603q.323 3.875.968 8.718c3.013-2.691 5.488-10.332 4.519-14.852-2.044 1.614-3.981 3.982-5.487 6.134" opacity=".4" /><path fill="#E58A7B" d="M311.821 134.633a180 180 0 0 1-6.672 14.637c-2.474 4.735-5.057 9.47-8.608 13.991l-.645.861-.323.43-.43.431a12 12 0 0 1-2.152 1.937c-1.507 1.076-3.013 1.722-4.52 2.26a31.5 31.5 0 0 1-8.608 1.507c-5.595.215-10.975-.323-16.355-1.4l.646-4.52c2.475 0 5.057 0 7.532-.215 2.475-.108 4.949-.43 7.424-.861 2.367-.43 4.735-.969 6.564-1.829.968-.431 1.721-.969 2.367-1.4.323-.215.538-.538.753-.753 0 0 .108-.215.215-.323l.216-.323.538-.753c2.69-3.874 4.949-8.502 7.209-13.022 2.152-4.628 4.304-9.363 6.241-13.991z" /><path fill="#287AFF" d="M308.273 122.367c-4.519 1.076-10.007 13.345-10.007 13.345l9.254 13.668s12.374-15.605 10.867-20.341c-1.399-4.95-4.627-8.071-10.114-6.672" /><path fill="#E58A7B" d="m266.734 165.309-7.747-3.551-.753 7.749s6.456 1.937 9.684 0zM254.362 160.898l-.753 5.919 4.627 2.583.753-7.749zM343.673 90.831c-1.614 8.825-2.152 12.592-7.209 16.574-7.532 6.027-17.969 2.045-19.045-6.995-.968-8.18 1.937-21.202 10.975-23.677 9.039-2.476 16.893 5.273 15.279 14.098" /><path fill="#263238" d="M324.413 92.658c-.538 4.52-.646 16.143 9.146 16.466 12.051.431 15.71-9.901 14.634-13.99 6.24-5.167 7.209-13.884.107-20.664s-22.596-3.014-26.9 3.551c-5.487 8.287 3.013 14.637 3.013 14.637" /><path fill="#287AFF" d="M336.464 77.167c-.538-4.735-.108-7.964 4.304-7.964 4.411 0 10.437 1.614 12.696 6.135 2.26 4.52 2.368 11.623-3.766 10.87-6.24-.646-12.481-3.337-13.234-9.04" /><path fill="#263238" d="M343.344 76.091c-1.076-5.596 3.982-21.524 16.248-22.6 5.703-.43 8.285 5.596 5.38 6.672 5.38-.107 8.823 6.996 3.766 8.072 3.981 2.583 3.013 7.749-2.69 10.332-4.196 1.937-21.305 5.273-22.704-2.476" /><path fill="#263238" d="M356.368 82.118c-3.981 0-8.07-.538-10.652-2.26-1.507-1.076-2.368-2.368-2.583-4.09 0-.107.108-.323.215-.323.108 0 .323.108.323.216.108 1.506.861 2.798 2.26 3.659 4.842 3.229 16.14 2.152 19.691 1.076 3.012-.969 5.057-2.906 5.487-5.166.323-1.614-.323-3.228-1.829-4.305-.108-.107-.108-.215 0-.43.108-.108.215-.108.43 0 1.614 1.291 2.368 3.013 2.045 4.843-.431 2.368-2.583 4.52-5.811 5.596-1.829.646-5.595 1.184-9.576 1.184" /><path fill="#E58A7B" d="M321.614 91.482a6.83 6.83 0 0 0-1.184 4.843c.323 2.26 2.583 2.368 4.197.969 1.399-1.292 3.013-4.09 1.829-5.92s-3.551-1.614-4.842.108" /><path fill="#1A3166" d="M322.258 176.719s5.273 61.883 10.653 96.752c4.411 28.197 7.854 93.954 7.854 93.954h12.267s4.411-63.82 2.152-91.694c-5.595-70.492 6.886-76.196-6.026-99.012z" /><path fill="#000" d="M337.754 200.07c-4.95 5.059-8.716 11.085-11.621 17.543 1.399 13.883 3.121 29.381 4.95 42.833 5.595-14.098 10.329-35.838 6.671-60.376" opacity=".4" /><path fill="#1A3166" d="M313.65 176.281s-11.298 67.587-13.127 98.044c4.411 28.197 22.703 87.712 22.703 87.712l11.191-4.09s-6.133-64.896-12.912-82.653c15.171-51.013 26.254-63.82 23.026-98.582-7.747 0-30.881-.431-30.881-.431" /><path fill="#263238" d="m321.717 365.053 15.064-4.089-.86-6.026-16.14 4.304zM339.044 367.744h15.711l.753-5.489-16.57-.646z" /><path fill="#407BFF" d="m349.694 174.567 1.614 3.229c.107.215-.215.538-.646.538h-38.413c-.323 0-.646-.216-.646-.323l-.322-3.229c0-.215.215-.43.645-.43h37.015c.43 0 .753.107.753.215" /><path fill="#000" d="m349.694 174.567 1.614 3.229c.107.215-.215.538-.646.538h-38.413c-.323 0-.646-.216-.646-.323l-.322-3.229c0-.215.215-.43.645-.43h37.015c.43 0 .753.107.753.215" opacity=".5" /><path fill="#407BFF" d="M344.744 178.759h.968c.216 0 .323-.108.323-.215l-.43-4.305c0-.108-.215-.216-.431-.216h-.968c-.215 0-.323.108-.323.216l.431 4.305c.107.107.215.215.43.215M331.402 147.333c-.215 0-.43-.216-.538-.431-3.551-13.022-10.76-23.677-14.311-25.291-.43-.215-.753-.215-.861-.108-.322.108-.538 0-.753-.322-.107-.323 0-.539.323-.754.538-.215 1.076-.215 1.829.108 3.766 1.722 11.083 12.269 14.741 25.614 2.69 0 6.672-.323 7.64-.431 1.184-1.506 10.222-12.699 6.886-25.076-.107-.322.108-.538.431-.645.323-.108.538.107.645.43 3.766 13.776-7.101 26.152-7.209 26.26s-.215.215-.323.215c-.107 0-5.487.431-8.5.431" /><path fill="#407BFF" d="M349.26 154.978c-5.918-10.978-8.285-11.731-9.361-15.283 0 0 .968-2.69 3.335-5.381-.753-.215-2.259-.538-3.228-.646.108-.43.215-.968.323-1.399-.753-.215-2.475 0-3.228.108v-1.076s-3.013-.646-4.196-.215c.107.538.215.968.322 1.506-1.076.215-2.69.538-3.55.754 2.259 2.583 3.12 5.273 3.335 6.026-3.12 5.382-15.494 40.359 4.519 43.049 20.122 2.476 17.539-16.466 11.729-27.443" /><path fill="#A1BDFC" d="M349.26 154.978c-5.918-10.978-8.285-11.731-9.361-15.283 0 0 .968-2.69 3.335-5.381-.753-.215-2.259-.538-3.228-.646.108-.43.215-.968.323-1.399-.753-.215-2.475 0-3.228.108v-1.076s-3.013-.646-4.196-.215c.107.538.215.968.322 1.506-1.076.215-2.69.538-3.55.754 2.259 2.583 3.12 5.273 3.335 6.026-3.12 5.382-15.494 40.359 4.519 43.049 20.122 2.476 17.539-16.466 11.729-27.443" opacity=".4" /><path fill="#407BFF" d="M332.366 138.296c-.108.215-.108 1.291-.108 1.722 0 .43 1.076.646 2.26.753 1.183.108 6.241.323 6.025-.215-.107-.538.216-1.292.216-1.83 0-.43-2.045-.538-3.659-.43-1.614.107-4.411-.646-4.734 0M320.644 178.759h.969c.215 0 .322-.108.322-.215l-.43-4.305c0-.108-.215-.216-.43-.216h-.861c-.215 0-.323.108-.323.216l.43 4.305c0 .107.216.215.323.215" /><path fill="#287AFF" d="m358.156 34.102 3.504 11.372 3.505-11.372z" /><path fill="#287AFF" d="M390.441 37.609h-57.667c-4.991 0-9.133-4.039-9.133-9.14V16.14c0-4.995 4.036-9.14 9.133-9.14h57.667c4.991 0 9.133 4.039 9.133 9.14V28.47c0 4.995-4.036 9.14-9.133 9.14" /><path fill="#fff" d="m341.591 22.309 2.017 1.169-.531.956-2.017-1.169v2.232h-.956v-2.232l-2.018 1.17-.531-.957 2.018-1.17-2.018-1.062.531-.957 2.018 1.17v-2.232h.956v2.231l2.017-1.169.531.957zM348.598 22.309l2.018 1.169-.531.956-2.019-1.169v2.232h-.954v-2.232l-2.018 1.17-.532-.957 2.019-1.17-2.019-1.062.532-.957 2.018 1.17v-2.232h.954v2.231l2.019-1.169.531.957zM355.605 22.309l2.018 1.169-.531.956-2.018-1.169v2.232h-.956v-2.232l-2.017 1.17-.531-.957 2.017-1.17-2.017-1.062.531-.957 2.017 1.17v-2.232h.956v2.231l2.018-1.169.531.957zM362.723 22.309l2.018 1.169-.531.956-2.019-1.169v2.232h-.954v-2.232l-2.018 1.17-.531-.957 2.018-1.17-2.018-1.062.531-.957 2.018 1.17v-2.232h.954v2.231l2.019-1.169.531.957zM369.73 22.309l2.018 1.169-.531.956-2.018-1.169v2.232h-.956v-2.232l-2.017 1.17-.531-.957 2.017-1.17-2.017-1.062.531-.957 2.017 1.17v-2.232h.956v2.231l2.018-1.169.531.957zM376.739 22.309l2.018 1.169-.531.956-2.018-1.169v2.232h-.956v-2.232l-2.017 1.17-.532-.957 2.018-1.17-2.018-1.062.532-.957 2.017 1.17v-2.232h.956v2.231l2.018-1.169.531.957zM383.855 22.309l2.018 1.169-.531.956-2.018-1.169v2.232h-.956v-2.232l-2.017 1.17-.531-.957 2.017-1.17-2.017-1.062.531-.957 2.017 1.17v-2.232h.956v2.231l2.018-1.169.531.957z" /><path fill="#263238" d="M123.818 19.974c5.827-10.138 18.737-13.637 28.867-7.895l-.897 1.525 6.007-1.615-1.615-6.01-.896 1.525c-12.64-7.267-28.866-2.871-36.128 9.868a26.555 26.555 0 0 0 3.048 30.682l4.034-3.498c-6.006-6.819-6.902-16.687-2.42-24.582M162.367 13.336l-4.034 3.409c5.737 6.818 6.634 16.507 2.241 24.223-5.827 10.138-18.736 13.636-28.866 7.895l.896-1.525-6.096 1.704 1.614 6.011.896-1.525c12.64 7.267 28.867 2.87 36.128-9.869 5.558-9.6 4.393-21.8-2.779-30.323" /><path fill="#E0E0E0" d="M123.818 19.974c5.827-10.138 18.737-13.637 28.867-7.895l-.897 1.525 6.007-1.615-1.615-6.01-.896 1.525c-12.64-7.267-28.866-2.871-36.128 9.868a26.555 26.555 0 0 0 3.048 30.682l4.034-3.498c-6.006-6.819-6.902-16.687-2.42-24.582M162.367 13.336l-4.034 3.409c5.737 6.818 6.634 16.507 2.241 24.223-5.827 10.138-18.736 13.636-28.866 7.895l.896-1.525-6.096 1.704 1.614 6.011.896-1.525c12.64 7.267 28.867 2.87 36.128-9.869 5.558-9.6 4.393-21.8-2.779-30.323" /><path fill="#263238" d="M149.913 28.687h-2.461V21.11c0-2.878-2.346-5.265-5.261-5.265-2.877 0-5.261 2.349-5.261 5.265v7.576h-2.461V21.11c0-4.242 3.445-7.727 7.722-7.727 4.239 0 7.722 3.447 7.722 7.727z" /><path fill="#fff" d="M149.913 28.687h-2.461V21.11c0-2.878-2.346-5.265-5.261-5.265-2.877 0-5.261 2.349-5.261 5.265v7.576h-2.461V21.11c0-4.242 3.445-7.727 7.722-7.727 4.239 0 7.722 3.447 7.722 7.727z" opacity=".3" /><path fill="#287AFF" d="M152.756 26.752v12.425c0 .795-.644 1.44-1.439 1.44h-18.206a1.44 1.44 0 0 1-1.439-1.44V26.752c0-.796.644-1.44 1.439-1.44h18.206c.228 0 .493.076.682.19.454.227.757.72.757 1.25" /><path fill="#5DB4F7" d="M152.741 25.462V39.07c0 .89-.667 1.61-1.491 1.61h-18.872c-.235 0-.47-.084-.706-.169v-13.65c0-.89.667-1.611 1.491-1.611h18.872c.235 0 .51.085.706.212" opacity=".2" /><path fill="#000" d="M144.92 31.414c0-1.516-1.211-2.766-2.726-2.766s-2.725 1.25-2.725 2.766c0 1.174.757 2.197 1.779 2.576l-.113 3.295h2.157l-.34-3.22a2.77 2.77 0 0 0 1.968-2.651" opacity=".5" /></g></g><path fill="#E6E6E6" d="M472.11 388.047H51.275c-3.297-.004-6.457-.813-8.786-2.252-2.33-1.439-3.637-3.387-3.637-5.42V15.672c0-2.032 1.308-3.982 3.637-5.42C44.818 8.813 47.978 8.004 51.275 8H472.11c3.3 0 6.466.808 8.801 2.247 2.332 1.439 3.644 3.39 3.644 5.425v364.703c0 1.007-.323 2.004-.948 2.936-.626.931-1.541 1.776-2.696 2.489-1.158.712-2.529 1.277-4.039 1.662-1.51.386-3.127.585-4.762.585M51.275 8.336c-3.153.003-6.174.778-8.401 2.153s-3.478 3.24-3.478 5.183v364.703c0 1.943 1.251 3.808 3.478 5.182 2.227 1.376 5.248 2.15 8.4 2.154H472.11c3.154-.004 6.177-.778 8.408-2.153s3.485-3.239 3.492-5.183V15.672c-.007-1.945-1.262-3.808-3.492-5.183s-5.254-2.15-8.408-2.153z" /></g><defs><clipPath id="a"><path fill="#fff" d="M.64 0h538v401H.64z" /></clipPath></defs></svg>

                </div>
                <div class="text-center">
                    <h4>Compliance Management & Operational Resilience</h4>
                    <h6 class="text-secondary fw-normal">Simple workflows to perform actions & failovers in few clicks.</h6>
                </div>
            </div>
            <div class="text-center fixed-bottom mb-2">
                @{
                    var version = Configuration.GetValue<string>("CP:Version");
                    var isCOE = Configuration.GetValue<string>("Release:isCOE");
                }
                @if (@isCOE != null)
                {
                    <small>Continuity Patrol Version <span>@version</span> | <span class="fw-bold">CoE Release</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
                }
                else
                {
                    <small>Continuity Patrol Version <span>@version</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
                }
            </div>
        </div>
    </div>
    <script>
        let version = sessionStorage.getItem('cpVersion')
        $('.cpVersionData').text(version)
    </script>
    <script type="text/javascript">
        var RootUrl = '@Url.Content("~/")';
    </script>

	<script src="~/js/common/loginpasswordchange.js"></script>
    <script src="~/js/admin/usermanagement/users/passwordpolicy.js"></script>
</body>