using ContinuityPatrol.Application.Features.BackUpLog.Events.PaginatedView;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUpLog.Events;

public class BackUpLogPaginatedEventTests : IClassFixture<BackUpLogFixture>
{
    private readonly BackUpLogFixture _backUpLogFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly Mock<ILogger<BackUpLogPaginatedEventHandler>> _mockLogger;
    private readonly BackUpLogPaginatedEventHandler _handler;

    public BackUpLogPaginatedEventTests(BackUpLogFixture backUpLogFixture)
    {
        _backUpLogFixture = backUpLogFixture;
        _mockUserActivityRepository = BackUpLogRepositoryMocks.CreateUserActivityRepository(_backUpLogFixture.UserActivities);
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<BackUpLogPaginatedEventHandler>>();

        // Setup logged in user service
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns("TestUser");
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/backuplog/paginated");
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        _handler = new BackUpLogPaginatedEventHandler(
            _mockLoggedInUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_When_BackUpLogPaginatedEventReceived()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCorrectUserActivity_When_BackUpLogPaginated()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        // Assert
        //_mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
        //    ua.UserId == _mockLoggedInUserService.Object.UserId &&
        //    ua.LoginName == _mockLoggedInUserService.Object.LoginName &&
        //    ua.RequestUrl == _mockLoggedInUserService.Object.RequestedUrl &&
        //    ua.HostAddress == _mockLoggedInUserService.Object.IpAddress &&
        //    ua.Action == $"{ActivityType.Read} {Modules.BackUp}" &&
        //    ua.Entity == Modules.BackUp.ToString() &&
        //    ua.ActivityType == ActivityType.Read.ToString() &&
        //    ua.ActivityDetails == "BackUpLog 'Paginated BackUpLog Query' paginated successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectUserInfo_When_BackUpLogPaginated()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var loginName = "PaginatedUser";
        var requestUrl = "/api/v6/backuplog/paginated";
        var ipAddress = "192.168.1.400";

        _mockLoggedInUserService.Setup(x => x.UserId).Returns(userId);
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns(loginName);
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(requestUrl);
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns(ipAddress);

        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == userId &&
            ua.LoginName == loginName &&
            ua.RequestUrl == requestUrl &&
            ua.HostAddress == ipAddress)), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_BackUpLogPaginated()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        paginatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_SetCorrectEntityAndModule_When_BackUpLogPaginated()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        paginatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_SetCorrectActivityType_When_BackUpLogPaginated()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        // Assert
        //_mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
        //    ua.ActivityType == ActivityType.Read.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_LogInformation_When_BackUpLogPaginated()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        // Assert
        paginatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_BackUpLogPaginatedWithNullName()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        paginatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_HandleEmptyEventName_When_BackUpLogPaginatedWithEmptyName()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        paginatedEvent.ShouldNotBeNull();
    }

   

    [Fact]
    public async Task Handle_CallRepositoryOnce_When_EventHandled()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        _mockUserActivityRepository.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_CreateEventWithComplexQueryName_When_BackUpLogPaginated()
    {
        // Arrange
        var complexQueryName = "Complex_Paginated_BackUpLog_Query_With_Filters_123";
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        paginatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_VerifyEventType_When_BackUpLogPaginated()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        // Assert
        paginatedEvent.ShouldBeOfType<BackUpLogPaginatedEvent>();
        paginatedEvent.ShouldBeAssignableTo<INotification>();
    }

    [Fact]
    public async Task Handle_DifferentiateFromOtherEvents_When_BackUpLogPaginated()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        // Assert
        //_mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
        //    ua.ActivityType == ActivityType.Read.ToString() &&
        //    ua.ActivityDetails.Contains("paginated") &&
        //    !ua.ActivityDetails.Contains("created") &&
        //    !ua.ActivityDetails.Contains("updated") &&
        //    !ua.ActivityDetails.Contains("deleted"))), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActionFormat_When_BackUpLogPaginated()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        // Assert
        paginatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_PaginatedEventForFilteredQuery_When_FilteredPaginationRequested()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        paginatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_PaginatedEventForSortedQuery_When_SortedPaginationRequested()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        paginatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_PaginatedEventForSearchQuery_When_SearchPaginationRequested()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        paginatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_TrackReadActivity_When_PaginatedQueryExecuted()
    {
        // Arrange
        var paginatedEvent = new BackUpLogPaginatedEvent ();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        paginatedEvent.ShouldNotBeNull();
    }
}
