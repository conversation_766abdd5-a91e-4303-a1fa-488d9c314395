﻿@model ContinuityPatrol.Domain.ViewModels.GroupPolicyModel.GroupPolicyViewModel 

<div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
        <div class="modal-content">
            <div class="modal-header">
            <h6 class="page_title"><i class="cp-standby-file"></i><span>Group Node Policy Configuration</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                 @Html.AntiForgeryToken()
            <form id="Create" asp-controller="GroupNodePolicy" asp-action="CreateOrUpdate" method="post" class="tab-wizard wizard-circle wizard clearfix">
                    <div class="mb-3">
                        <div class="form-group">
                            <div class="form-label">Name</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-name"></i></span>
                                <input asp-for="GroupName" id="name" type="text" class="form-control" placeholder="Enter Group Node Policy Name" maxlength="100" autofocus autocomplete="off" />
                            </div>
                            <span asp-validation-for="GroupName" id="Name-error"></span>
                        </div>
                    </div>

                     <div class="mb-3 form-group">
                                <div class="form-label">Service Type</div>
                                <div class="input-group">
                        <span class="input-group-text"><i class="cp-activity-type"></i></span>
                             <select class=" form-select-modal" data-live-search="true" data-placeholder="Select Service Type" id="cpNodeType">
                            <option value="MonitorService">Monitor Service</option>
                            <option value="WorkflowService">Workflow Service</option>
                            <option value="ResiliencyReadyService">Resiliency Ready Service</option>
                                    </select>
                                </div>
                    <span  id="Type-error"></span>
                            </div>

                    <div class="mb-3 form-group">
                        <div class="form-label">CP Nodes</div>
                        <div class="input-group">
                        <span class="input-group-text">
                            <i class="cp-network"></i></span>
                             <select  id="nodeName" class="form-select-modal" multiple="multiple" data-placeholder="Select CP Nodes" aria-label="Default select example" data-live-search="true" required>
                            
                            </select>                           
                        </div>
                        <span  id="Node-error"></span>
                    </div>
                     <input asp-for="Properties" id="nodeId" type="hidden" class="form-control"/>
                    <input asp-for="Id" type="hidden" id="groupPolicyId" class="form-control" />
                <input asp-for="Type" type="hidden" id="nodeTypeId" class="form-control" />
                </form>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button id="save" class="btn btn-primary btn-sm" >Save</button>
                </div>
            </div>
        </div>
    </div>
