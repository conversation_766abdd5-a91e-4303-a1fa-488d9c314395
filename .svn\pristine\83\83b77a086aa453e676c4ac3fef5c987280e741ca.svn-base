﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.InfraObject.Events.Delete;

public class InfraObjectDeletedEventHandler : INotificationHandler<InfraObjectDeletedEvent>
{
    private readonly ILogger<InfraObjectDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;


    public InfraObjectDeletedEventHandler(ILoggedInUserService userService,
        ILogger<InfraObjectDeletedEventHandler> logger, IUserActivityRepository userActivityRepository )
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
     
    }

    public async Task Handle(InfraObjectDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress ?? "::1",
            Entity = Modules.InfraObject.ToString(),
            Action = $"{ActivityType.Delete} {Modules.InfraObject}",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $" InfraObject '{deletedEvent.InfraObjectName}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"InfraObject '{deletedEvent.InfraObjectName}' deleted successfully.");
    }
}