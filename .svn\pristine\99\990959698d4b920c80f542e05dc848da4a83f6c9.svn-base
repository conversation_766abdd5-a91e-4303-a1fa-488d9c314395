﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.MSSQLMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetPaginatedList;

public class GetMSSQLMonitorStatusPaginatedListQueryHandler : IRequestHandler<GetMSSQLMonitorStatusPaginatedListQuery,
    PaginatedResult<MSSQLMonitorStatusListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMssqlMonitorStatusRepository _mssqlMonitorStatusRepository;

    public GetMSSQLMonitorStatusPaginatedListQueryHandler(IMssqlMonitorStatusRepository mssqlMonitorStatusRepository,
        IMapper mapper)
    {
        _mssqlMonitorStatusRepository = mssqlMonitorStatusRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<MSSQLMonitorStatusListVm>> Handle(GetMSSQLMonitorStatusPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _mssqlMonitorStatusRepository.GetPaginatedQuery();

        var productFilterSpec = new MsSqlMonitorStatusFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<MSSQLMonitorStatusListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}