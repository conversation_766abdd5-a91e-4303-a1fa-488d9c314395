﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Report.Event.Update;

public class ReportUpdatedEventHandler : INotificationHandler<ReportUpdatedEvent>
{
    private readonly ILogger<ReportUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ReportUpdatedEventHandler(ILoggedInUserService userService, ILogger<ReportUpdatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ReportUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.Report}",
            Entity = Modules.Report.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Report '{updatedEvent.ReportName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Report '{updatedEvent.ReportName}' updated successfully.");
    }
}