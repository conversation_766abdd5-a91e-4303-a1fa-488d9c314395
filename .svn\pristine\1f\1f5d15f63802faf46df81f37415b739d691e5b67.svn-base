﻿namespace ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetLastDrillDetails;

public class LastDrillDetailQueryHandler : IRequestHandler<LastDrillDetailQuery, LastDrillDetailVm>
{
    private readonly IWorkflowOperationRepository _workflowOperationRepository;
    private readonly IMapper _mapper;
    public LastDrillDetailQueryHandler(IWorkflowOperationRepository workflowOperationRepository, IMapper mapper)
    {
        _workflowOperationRepository = workflowOperationRepository;
    }
    public async Task<LastDrillDetailVm> Handle(LastDrillDetailQuery request, CancellationToken cancellationToken)
    {
        var workflowOperation = await _workflowOperationRepository.GetLastWorkflowOperation();

        var lastDrillDetails = new LastDrillDetailVm
        {
            ProfileName = workflowOperation.ProfileName,
            Status = workflowOperation.Status,
            LastExecutionTime = workflowOperation.EndTime
        };

        TimeSpan duration = workflowOperation.EndTime - workflowOperation.StartTime;

        if (duration.TotalDays >= 1)
            lastDrillDetails.Duration = $"{(int)duration.TotalDays}d {duration.Hours:D2}:{duration.Minutes:D2}:{duration.Seconds:D2}";

        else
            lastDrillDetails.Duration = $"{duration.Hours:D2}:{duration.Minutes:D2}:{duration.Seconds:D2}";


        return lastDrillDetails;
    }
}
