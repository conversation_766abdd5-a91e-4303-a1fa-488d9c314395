﻿namespace ContinuityPatrol.Application.Features.RiskMitigation.Commands.Delete;

public class
    DeleteRiskMitigationCommandHandler : IRequestHandler<DeleteRiskMitigationCommand, DeleteRiskMitigationResponse>
{
    private readonly IRiskMitigationRepository _riskMitigationRepository;

    public DeleteRiskMitigationCommandHandler(IRiskMitigationRepository riskMitigationRepository)
    {
        _riskMitigationRepository = riskMitigationRepository;
    }

    public async Task<DeleteRiskMitigationResponse> Handle(DeleteRiskMitigationCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _riskMitigationRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.RiskMitigation),
            new NotFoundException(nameof(Domain.Entities.RiskMitigation), request.Id));

        eventToDelete.IsActive = false;

        await _riskMitigationRepository.UpdateAsync(eventToDelete);

        var response = new DeleteRiskMitigationResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.RiskMitigation), eventToDelete.InfraObjectName),

            IsActive = eventToDelete.IsActive
        };

        return response;
    }
}