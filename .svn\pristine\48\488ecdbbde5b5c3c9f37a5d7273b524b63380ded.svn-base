namespace ContinuityPatrol.Application.Features.BiaRules.Commands.Create;

public class CreateBiaRulesCommand : IRequest<CreateBiaRulesResponse>
{
    public string Description { get; set; }
    public string Type { get; set; }
    public string EntityId { get; set; }
    public string Properties { get; set; }
    public string EffectiveDateFrom { get; set; }
    public string EffectiveDateTo { get; set; }
    public bool IsEffective { get; set; }
    public string RuleCode { get; set; }
}