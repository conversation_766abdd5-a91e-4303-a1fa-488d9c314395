using ContinuityPatrol.Application.Features.IncidentManagement.Commands.Create;
using ContinuityPatrol.Application.Features.IncidentManagement.Commands.Update;
using ContinuityPatrol.Application.Features.IncidentManagement.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.IncidentManagementModel;

namespace ContinuityPatrol.Application.Mappings;

public class IncidentManagementProfile : Profile
{
    public IncidentManagementProfile()
    {
        CreateMap<IncidentManagement, IncidentManagementListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<IncidentManagement, IncidentManagementDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<IncidentManagement, CreateIncidentManagementCommand>().ReverseMap();
        CreateMap<IncidentManagement, IncidentManagementViewModel>().ReverseMap();

        CreateMap<CreateIncidentManagementCommand, IncidentManagementViewModel>().ReverseMap();
        CreateMap<UpdateIncidentManagementCommand, IncidentManagementViewModel>().ReverseMap();

        CreateMap<UpdateIncidentManagementCommand, IncidentManagement>().ForMember(x => x.Id, y => y.Ignore());
    }
}