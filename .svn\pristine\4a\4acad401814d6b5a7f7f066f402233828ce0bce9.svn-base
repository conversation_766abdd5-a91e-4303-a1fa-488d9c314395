﻿namespace ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Create;

public class CreatePageSolutionMappingCommand : IRequest<CreatePageSolutionMappingResponse>
{
    public string Name { get; set; }
    public string PageBuilderId { get; set; }
    public string PageBuilderName { get; set; }
    public string MonitorType { get; set; }
    public int Type { get; set; }
    public string TypeName { get; set; }
    public string SubTypeId { get; set; }
    public string SubType { get; set; }
    public string ReplicationTypeId { get; set; }
    public string ReplicationTypeName { get; set; }
    public string ReplicationCategoryTypeId { get; set; }
    public string ReplicationCategoryType { get; set; }
}