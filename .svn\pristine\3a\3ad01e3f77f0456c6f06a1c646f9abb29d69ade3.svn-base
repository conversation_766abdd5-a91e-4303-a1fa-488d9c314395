﻿using Moq;
using Microsoft.Extensions.Logging;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Application.Features.Setting.Commands.Create;
using ContinuityPatrol.Application.Features.Setting.Commands.Update;
using ContinuityPatrol.Application.Features.Setting.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SettingModel;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Provider;
using MediatR;
using AutoFixture;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Fakes;
using Microsoft.AspNetCore.Http;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class SettingsControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<ILogger<SettingsController>> _mockLogger = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private  SettingsController _controller;

        public SettingsControllerShould()
        {

            Initialize();
        }
        internal void Initialize()
        {
            _controller = new SettingsController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockLoggedInUserService.Object,
                _mockMapper.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_ReturnsView()
        {
            var result = await _controller.List() as ViewResult;

            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetList_ReturnsJsonResultWithSettings()
        {
            var _getSettingPaginatedListQuery = new AutoFixture.Fixture().Create<GetSettingPaginatedListQuery>();
            _mockDataProvider.Setup(p=>p.Setting.GetSettingPaginatedList(_getSettingPaginatedListQuery)).ReturnsAsync(It.IsAny<PaginatedResult<SettingListVm>>);
            
            // Act
            var result = await _controller.GetList() as JsonResult;
            var data = result?.Value as List<SettingModel>;

            // Assert
            Assert.IsType<JsonResult>(result);
            
        }

        [Fact]
        public async Task CreateOrUpdate_CreatesSettingSuccessfully()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<SettingModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateSettingCommand ();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockLoggedInUserService.Setup(s => s.UserId).Returns("user-id");
            _mockMapper.Setup(m => m.Map<CreateSettingCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.Setting.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result.ActionName);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesSettingSuccessfully()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<SettingModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateSettingCommand ();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockLoggedInUserService.Setup(s => s.UserId).Returns("user-id");
            _mockMapper.Setup(m => m.Map<UpdateSettingCommand>(model)).Returns(updateCommand);
            _mockDataProvider.Setup(p => p.Setting.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result.ActionName);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesGeneralException()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<SettingModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            _mockLoggedInUserService.Setup(s => s.UserId).Returns("user-id");
            _mockMapper.Setup(m => m.Map<CreateSettingCommand>(model)).Returns(new CreateSettingCommand());
            _mockDataProvider.Setup(p => p.Setting.CreateAsync(It.IsAny<CreateSettingCommand>()))
                .ThrowsAsync(new Exception("General error"));

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result.ActionName);
            Assert.Equal("List", result.ActionName);
           
        }

        [Fact]
        public void SmtpConfiguration_ReturnsView()
        {
            // Act
            var result = _controller.SmtpConfiguration() as ViewResult;

            // Assert
            Assert.NotNull(result);
        }
    }
}
