{"version": 3, "file": "purify.cjs.js", "sources": ["../src/utils.js", "../src/tags.js", "../src/attrs.js", "../src/regexp.js", "../src/purify.js"], "sourcesContent": ["const {\n  entries,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n} = Object;\n\nlet { freeze, seal, create } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst objectHasOwnProperty = unapply(Object.prototype.hasOwnProperty);\n\nconst regExpTest = unapply(RegExp.prototype.test);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\n/**\n * Creates a new function that calls the given function with a specified thisArg and arguments.\n *\n * @param {Function} func - The function to be wrapped and called.\n * @returns {Function} A new function that calls the given function with a specified thisArg and arguments.\n */\nfunction unapply(func) {\n  return (thisArg, ...args) => apply(func, thisArg, args);\n}\n\n/**\n * Creates a new function that constructs an instance of the given constructor function with the provided arguments.\n *\n * @param {Function} func - The constructor function to be wrapped and called.\n * @returns {Function} A new function that constructs an instance of the given constructor function with the provided arguments.\n */\nfunction unconstruct(func) {\n  return (...args) => construct(func, args);\n}\n\n/**\n * Add properties to a lookup table\n *\n * @param {Object} set - The set to which elements will be added.\n * @param {Array} array - The array containing elements to be added to the set.\n * @param {Function} transformCaseFunc - An optional function to transform the case of each element before adding to the set.\n * @returns {Object} The modified set with added elements.\n */\nfunction addToSet(set, array, transformCaseFunc = stringToLowerCase) {\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/**\n * Clean up an array to harden against CSPP\n *\n * @param {Array} array - The array to be cleaned.\n * @returns {Array} The cleaned version of the array\n */\nfunction cleanArray(array) {\n  for (let index = 0; index < array.length; index++) {\n    const isPropertyExist = objectHasOwnProperty(array, index);\n\n    if (!isPropertyExist) {\n      array[index] = null;\n    }\n  }\n\n  return array;\n}\n\n/**\n * Shallow clone an object\n *\n * @param {Object} object - The object to be cloned.\n * @returns {Object} A new object that copies the original.\n */\nfunction clone(object) {\n  const newObject = create(null);\n\n  for (const [property, value] of entries(object)) {\n    const isPropertyExist = objectHasOwnProperty(object, property);\n\n    if (isPropertyExist) {\n      if (Array.isArray(value)) {\n        newObject[property] = cleanArray(value);\n      } else if (\n        value &&\n        typeof value === 'object' &&\n        value.constructor === Object\n      ) {\n        newObject[property] = clone(value);\n      } else {\n        newObject[property] = value;\n      }\n    }\n  }\n\n  return newObject;\n}\n\n/**\n * This method automatically checks if the prop is function or getter and behaves accordingly.\n *\n * @param {Object} object - The object to look up the getter function in its prototype chain.\n * @param {String} prop - The property name for which to find the getter function.\n * @returns {Function} The getter function found in the prototype chain or a fallback function.\n */\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue() {\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  // Object\n  entries,\n  freeze,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n  isFrozen,\n  setPrototypeOf,\n  seal,\n  clone,\n  create,\n  objectHasOwnProperty,\n  // RegExp\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n  // Other\n  lookupGetter,\n  addToSet,\n  // Reflect\n  unapply,\n  unconstruct,\n};\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n]);\n\n// SVG\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n]);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n]);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nexport const svgDisallowed = freeze([\n  'animate',\n  'color-profile',\n  'cursor',\n  'discard',\n  'font-face',\n  'font-face-format',\n  'font-face-name',\n  'font-face-src',\n  'font-face-uri',\n  'foreignobject',\n  'hatch',\n  'hatchpath',\n  'mesh',\n  'meshgradient',\n  'meshpatch',\n  'meshrow',\n  'missing-glyph',\n  'script',\n  'set',\n  'solidcolor',\n  'unknown',\n  'use',\n]);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n  'mprescripts',\n]);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nexport const mathMlDisallowed = freeze([\n  'maction',\n  'maligngroup',\n  'malignmark',\n  'mlongdiv',\n  'mscarries',\n  'mscarry',\n  'msgroup',\n  'mstack',\n  'msline',\n  'msrow',\n  'semantics',\n  'annotation',\n  'annotation-xml',\n  'mprescripts',\n  'none',\n]);\n\nexport const text = freeze(['#text']);\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'nonce',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'popover',\n  'popovertarget',\n  'popovertargetaction',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'wrap',\n  'xmlns',\n  'slot',\n]);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'targetx',\n  'targety',\n  'transform',\n  'transform-origin',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n]);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n]);\n", "import { seal } from './utils.js';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nexport const TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nexport const DOCTYPE_NAME = seal(/^html$/i);\nexport const CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n", "import * as TAGS from './tags.js';\nimport * as ATTRS from './attrs.js';\nimport * as EXPRESSIONS from './regexp.js';\nimport {\n  addToSet,\n  clone,\n  entries,\n  freeze,\n  arrayForEach,\n  arrayPop,\n  arrayPush,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  typeErrorCreate,\n  lookupGetter,\n  create,\n  objectHasOwnProperty,\n} from './utils.js';\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\nconst NODE_TYPE = {\n  element: 1,\n  attribute: 2,\n  text: 3,\n  cdataSection: 4,\n  entityReference: 5, // Deprecated\n  entityNode: 6, // Deprecated\n  progressingInstruction: 7,\n  comment: 8,\n  document: 9,\n  documentType: 10,\n  documentFragment: 11,\n  notation: 12, // Deprecated\n};\n\nconst getGlobal = function () {\n  return typeof window === 'undefined' ? null : window;\n};\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {HTMLScriptElement} purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n * @return {TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported or creating the policy failed).\n */\nconst _createTrustedTypesPolicy = function (trustedTypes, purifyHostElement) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n    suffix = purifyHostElement.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nfunction createDOMPurify(window = getGlobal()) {\n  const DOMPurify = (root) => createDOMPurify(root);\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  DOMPurify.version = VERSION;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  DOMPurify.removed = [];\n\n  if (\n    !window ||\n    !window.document ||\n    window.document.nodeType !== NODE_TYPE.document\n  ) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  let { document } = window;\n\n  const originalDocument = document;\n  const currentScript = originalDocument.currentScript;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  const ElementPrototype = Element.prototype;\n\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const remove = lookupGetter(ElementPrototype, 'remove');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  let trustedTypesPolicy;\n  let emptyHTML = '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let hooks = {};\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    typeof entries === 'function' &&\n    typeof getParentNode === 'function' &&\n    implementation &&\n    implementation.createHTMLDocument !== undefined;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /*\n   * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(\n    create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false,\n      },\n    })\n  );\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'noscript',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'role',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet(\n    {},\n    [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE],\n    stringToString\n  );\n\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE = null;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc = null;\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function (testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg = {}) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n\n    PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1\n        ? DEFAULT_PARSER_MEDIA_TYPE\n        : cfg.PARSER_MEDIA_TYPE;\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc =\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml'\n        ? stringToString\n        : stringToLowerCase;\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS = objectHasOwnProperty(cfg, 'ALLOWED_TAGS')\n      ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc)\n      : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR = objectHasOwnProperty(cfg, 'ALLOWED_ATTR')\n      ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc)\n      : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES = objectHasOwnProperty(cfg, 'ALLOWED_NAMESPACES')\n      ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString)\n      : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES = objectHasOwnProperty(cfg, 'ADD_URI_SAFE_ATTR')\n      ? addToSet(\n          clone(DEFAULT_URI_SAFE_ATTRIBUTES), // eslint-disable-line indent\n          cfg.ADD_URI_SAFE_ATTR, // eslint-disable-line indent\n          transformCaseFunc // eslint-disable-line indent\n        ) // eslint-disable-line indent\n      : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS = objectHasOwnProperty(cfg, 'ADD_DATA_URI_TAGS')\n      ? addToSet(\n          clone(DEFAULT_DATA_URI_TAGS), // eslint-disable-line indent\n          cfg.ADD_DATA_URI_TAGS, // eslint-disable-line indent\n          transformCaseFunc // eslint-disable-line indent\n        ) // eslint-disable-line indent\n      : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS = objectHasOwnProperty(cfg, 'FORBID_CONTENTS')\n      ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc)\n      : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS = objectHasOwnProperty(cfg, 'FORBID_TAGS')\n      ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc)\n      : {};\n    FORBID_ATTR = objectHasOwnProperty(cfg, 'FORBID_ATTR')\n      ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc)\n      : {};\n    USE_PROFILES = objectHasOwnProperty(cfg, 'USE_PROFILES')\n      ? cfg.USE_PROFILES\n      : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || EXPRESSIONS.IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements ===\n        'boolean'\n    ) {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements =\n        cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, TAGS.text);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    if (cfg.TRUSTED_TYPES_POLICY) {\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.'\n        );\n      }\n\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.'\n        );\n      }\n\n      // Overwrite existing TrustedTypes policy.\n      trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY;\n\n      // Sign local variables required by `sanitize`.\n      emptyHTML = trustedTypesPolicy.createHTML('');\n    } else {\n      // Uninitialized policy, attempt to initialize the internal dompurify policy.\n      if (trustedTypesPolicy === undefined) {\n        trustedTypesPolicy = _createTrustedTypesPolicy(\n          trustedTypes,\n          currentScript\n        );\n      }\n\n      // If creating the internal policy succeeded sign internal variables.\n      if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      }\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  const MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n    'mi',\n    'mo',\n    'mn',\n    'ms',\n    'mtext',\n  ]);\n\n  const HTML_INTEGRATION_POINTS = addToSet({}, [\n    'foreignobject',\n    'annotation-xml',\n  ]);\n\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n    'title',\n    'style',\n    'font',\n    'a',\n    'script',\n  ]);\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, [\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.svgDisallowed,\n  ]);\n  const ALL_MATHML_TAGS = addToSet({}, [\n    ...TAGS.mathMl,\n    ...TAGS.mathMlDisallowed,\n  ]);\n\n  /**\n   * @param  {Element} element a DOM element whose namespace is being checked\n   * @returns {boolean} Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function (element) {\n    let parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template',\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return (\n          tagName === 'svg' &&\n          (parentTagName === 'annotation-xml' ||\n            MATHML_TEXT_INTEGRATION_POINTS[parentTagName])\n        );\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (\n        parent.namespaceURI === SVG_NAMESPACE &&\n        !HTML_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      if (\n        parent.namespaceURI === MATHML_NAMESPACE &&\n        !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return (\n        !ALL_MATHML_TAGS[tagName] &&\n        (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName])\n      );\n    }\n\n    // For XHTML and XML documents that support custom namespaces\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      ALLOWED_NAMESPACES[element.namespaceURI]\n    ) {\n      return true;\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n  const _forceRemove = function (node) {\n    arrayPush(DOMPurify.removed, { element: node });\n\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      getParentNode(node).removeChild(node);\n    } catch (_) {\n      remove(node);\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n  const _removeAttribute = function (name, node) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: node.getAttributeNode(name),\n        from: node,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: node,\n      });\n    }\n\n    node.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\"\" attributes\n    if (name === 'is' && !ALLOWED_ATTR[name]) {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(node);\n        } catch (_) {}\n      } else {\n        try {\n          node.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty) {\n    /* Create a HTML document */\n    let doc = null;\n    let leadingWhitespace = null;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      NAMESPACE === HTML_NAMESPACE\n    ) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty =\n        '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' +\n        dirty +\n        '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT\n          ? emptyHTML\n          : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(\n        doc,\n        WHOLE_DOCUMENT ? 'html' : 'body'\n      )[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * Creates a NodeIterator object that you can use to traverse filtered lists of nodes or elements in a document.\n   *\n   * @param  {Node} root The root element or node to start traversing on.\n   * @return {NodeIterator} The created NodeIterator\n   */\n  const _createNodeIterator = function (root) {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT |\n        NodeFilter.SHOW_COMMENT |\n        NodeFilter.SHOW_TEXT |\n        NodeFilter.SHOW_PROCESSING_INSTRUCTION |\n        NodeFilter.SHOW_CDATA_SECTION,\n      null\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n  const _isClobbered = function (elm) {\n    return (\n      elm instanceof HTMLFormElement &&\n      (typeof elm.nodeName !== 'string' ||\n        typeof elm.textContent !== 'string' ||\n        typeof elm.removeChild !== 'function' ||\n        !(elm.attributes instanceof NamedNodeMap) ||\n        typeof elm.removeAttribute !== 'function' ||\n        typeof elm.setAttribute !== 'function' ||\n        typeof elm.namespaceURI !== 'string' ||\n        typeof elm.insertBefore !== 'function' ||\n        typeof elm.hasChildNodes !== 'function')\n    );\n  };\n\n  /**\n   * Checks whether the given object is a DOM node.\n   *\n   * @param  {Node} object object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n  const _isNode = function (object) {\n    return typeof Node === 'function' && object instanceof Node;\n  };\n\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n  const _executeHook = function (entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n\n    arrayForEach(hooks[entryPoint], (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function (currentNode) {\n    let content = null;\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeElements', currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (\n      currentNode.hasChildNodes() &&\n      !_isNode(currentNode.firstElementChild) &&\n      regExpTest(/<[/\\w]/g, currentNode.innerHTML) &&\n      regExpTest(/<[/\\w]/g, currentNode.textContent)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any occurrence of processing instructions */\n    if (currentNode.nodeType === NODE_TYPE.progressingInstruction) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any kind of possibly harmful comments */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.nodeType === NODE_TYPE.comment &&\n      regExpTest(/<[/\\w]/g, currentNode.data)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _isBasicCustomElement(tagName)) {\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n          regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)\n        ) {\n          return false;\n        }\n\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)\n        ) {\n          return false;\n        }\n      }\n\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if (\n      (tagName === 'noscript' ||\n        tagName === 'noembed' ||\n        tagName === 'noframes') &&\n      regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === NODE_TYPE.text) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        content = stringReplace(content, expr, ' ');\n      });\n\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeElements', currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (\n      ALLOW_DATA_ATTR &&\n      !FORBID_ATTR[lcName] &&\n      regExpTest(DATA_ATTR, lcName)\n    ) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        (_isBasicCustomElement(lcTag) &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag))) &&\n          ((CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName)) ||\n            (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)))) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        (lcName === 'is' &&\n          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))))\n      ) {\n        // If user has supplied a regexp or function in CUSTOM_ELEMENT_HANDLING.tagNameCheck, we need to also allow derived custom elements using the same tagName test.\n        // Additionally, we need to allow attributes passing the CUSTOM_ELEMENT_HANDLING.attributeNameCheck user has configured, as custom elements can define these at their own discretion.\n      } else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n    } else if (value) {\n      return false;\n    } else {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    }\n\n    return true;\n  };\n\n  /**\n   * _isBasicCustomElement\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   *\n   * @param {string} tagName name of the tag of the node to sanitize\n   * @returns {boolean} Returns true if the tag name meets the basic criteria for a custom element, otherwise false.\n   */\n  const _isBasicCustomElement = function (tagName) {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n  const _sanitizeAttributes = function (currentNode) {\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n    const { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n    };\n    let l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      const attr = attributes[l];\n      const { name, namespaceURI, value: attrValue } = attr;\n      const lcName = transformCaseFunc(name);\n\n      let value = name === 'value' ? attrValue : stringTrim(attrValue);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n      value = hookEvent.attrValue;\n\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Remove attribute */\n      _removeAttribute(name, currentNode);\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n          value = stringReplace(value, expr, ' ');\n        });\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n\n      /* Handle attributes that require Trusted Types */\n      if (\n        trustedTypesPolicy &&\n        typeof trustedTypes === 'object' &&\n        typeof trustedTypes.getAttributeType === 'function'\n      ) {\n        if (namespaceURI) {\n          /* Namespaces are not yet supported, see https://bugs.chromium.org/p/chromium/issues/detail?id=1305293 */\n        } else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML': {\n              value = trustedTypesPolicy.createHTML(value);\n              break;\n            }\n\n            case 'TrustedScriptURL': {\n              value = trustedTypesPolicy.createScriptURL(value);\n              break;\n            }\n\n            default: {\n              break;\n            }\n          }\n        }\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        if (_isClobbered(currentNode)) {\n          _forceRemove(currentNode);\n        } else {\n          arrayPop(DOMPurify.removed);\n        }\n      } catch (_) {}\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment) {\n    let shadowNode = null;\n    const shadowIterator = _createNodeIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(shadowNode)) {\n        continue;\n      }\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(shadowNode);\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} cfg object\n   */\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg = {}) {\n    let body = null;\n    let importedNode = null;\n    let currentNode = null;\n    let returnNode = null;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n\n    /* Return dirty HTML if DOMPurify cannot run */\n    if (!DOMPurify.isSupported) {\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if (dirty.nodeName) {\n        const tagName = transformCaseFunc(dirty.nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate(\n            'root node is forbidden and cannot be sanitized in-place'\n          );\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (\n        importedNode.nodeType === NODE_TYPE.element &&\n        importedNode.nodeName === 'BODY'\n      ) {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createNodeIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Sanitize tags and elements */\n      if (_sanitizeElements(currentNode)) {\n        continue;\n      }\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n\n      /* Check attributes, sanitize if necessary */\n      _sanitizeAttributes(currentNode);\n    }\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmode) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Serialize doctype if allowed */\n    if (\n      WHOLE_DOCUMENT &&\n      ALLOWED_TAGS['!doctype'] &&\n      body.ownerDocument &&\n      body.ownerDocument.doctype &&\n      body.ownerDocument.doctype.name &&\n      regExpTest(EXPRESSIONS.DOCTYPE_NAME, body.ownerDocument.doctype.name)\n    ) {\n      serializedHTML =\n        '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        serializedHTML = stringReplace(serializedHTML, expr, ' ');\n      });\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n  DOMPurify.setConfig = function (cfg = {}) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {String} tag Tag name of containing element.\n   * @param  {String} attr Attribute name.\n   * @param  {String} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   * @return {Function} removed(popped) hook\n   */\n  DOMPurify.removeHook = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      return arrayPop(hooks[entryPoint]);\n    }\n  };\n\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n  DOMPurify.removeHooks = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   */\n  DOMPurify.removeAllHooks = function () {\n    hooks = {};\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n"], "names": ["entries", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "Object", "freeze", "seal", "create", "apply", "construct", "Reflect", "x", "fun", "thisValue", "args", "Func", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayPop", "pop", "arrayPush", "push", "stringToLowerCase", "String", "toLowerCase", "stringToString", "toString", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "objectHasOwnProperty", "hasOwnProperty", "regExpTest", "RegExp", "test", "typeErrorCreate", "unconstruct", "TypeError", "func", "thisArg", "_len", "arguments", "length", "_key", "_len2", "_key2", "addToSet", "set", "array", "transformCaseFunc", "undefined", "l", "element", "lcElement", "cleanArray", "index", "isPropertyExist", "clone", "object", "newObject", "property", "value", "isArray", "constructor", "lookupGetter", "prop", "desc", "get", "fallback<PERSON><PERSON><PERSON>", "html", "svg", "svgFilters", "svgDisallowed", "mathMl", "mathMlDisallowed", "text", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "CUSTOM_ELEMENT", "NODE_TYPE", "attribute", "cdataSection", "entityReference", "entityNode", "progressingInstruction", "comment", "document", "documentType", "documentFragment", "notation", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "purifyHostElement", "createPolicy", "suffix", "ATTR_NAME", "hasAttribute", "getAttribute", "policyName", "createHTML", "createScriptURL", "scriptUrl", "_", "console", "warn", "createDOMPurify", "DOMPurify", "root", "version", "VERSION", "removed", "nodeType", "isSupported", "originalDocument", "currentScript", "DocumentFragment", "HTMLTemplateElement", "Node", "Element", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "remove", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "hooks", "createHTMLDocument", "EXPRESSIONS", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "SAFE_FOR_XML", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "Function", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "TRUSTED_TYPES_POLICY", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "Boolean", "_forceRemove", "node", "<PERSON><PERSON><PERSON><PERSON>", "_removeAttribute", "name", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createNodeIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "SHOW_PROCESSING_INSTRUCTION", "SHOW_CDATA_SECTION", "_isClobbered", "elm", "nodeName", "textContent", "attributes", "hasChildNodes", "_isNode", "_executeHook", "entryPoint", "currentNode", "data", "hook", "_sanitizeElements", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_isBasicCustomElement", "parentNode", "childCount", "i", "child<PERSON>lone", "__removalCount", "expr", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "attr", "forceKeepAttr", "getAttributeType", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "returnNode", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmode", "serializedHTML", "outerHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"], "mappings": ";;;;AAAA,MAAM;EACJA,OAAO;EACPC,cAAc;EACdC,QAAQ;EACRC,cAAc;AACdC,EAAAA,wBAAAA;AACF,CAAC,GAAGC,MAAM,CAAA;AAEV,IAAI;EAAEC,MAAM;EAAEC,IAAI;AAAEC,EAAAA,MAAAA;AAAO,CAAC,GAAGH,MAAM,CAAC;AACtC,IAAI;EAAEI,KAAK;AAAEC,EAAAA,SAAAA;AAAU,CAAC,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAAA;AAEpE,IAAI,CAACL,MAAM,EAAE;AACXA,EAAAA,MAAM,GAAG,SAAAA,MAAUM,CAAAA,CAAC,EAAE;AACpB,IAAA,OAAOA,CAAC,CAAA;GACT,CAAA;AACH,CAAA;AAEA,IAAI,CAACL,IAAI,EAAE;AACTA,EAAAA,IAAI,GAAG,SAAAA,IAAUK,CAAAA,CAAC,EAAE;AAClB,IAAA,OAAOA,CAAC,CAAA;GACT,CAAA;AACH,CAAA;AAEA,IAAI,CAACH,KAAK,EAAE;EACVA,KAAK,GAAG,SAAAA,KAAUI,CAAAA,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAE;AACtC,IAAA,OAAOF,GAAG,CAACJ,KAAK,CAACK,SAAS,EAAEC,IAAI,CAAC,CAAA;GAClC,CAAA;AACH,CAAA;AAEA,IAAI,CAACL,SAAS,EAAE;AACdA,EAAAA,SAAS,GAAG,SAAAA,SAAAA,CAAUM,IAAI,EAAED,IAAI,EAAE;AAChC,IAAA,OAAO,IAAIC,IAAI,CAAC,GAAGD,IAAI,CAAC,CAAA;GACzB,CAAA;AACH,CAAA;AAEA,MAAME,YAAY,GAAGC,OAAO,CAACC,KAAK,CAACC,SAAS,CAACC,OAAO,CAAC,CAAA;AAErD,MAAMC,QAAQ,GAAGJ,OAAO,CAACC,KAAK,CAACC,SAAS,CAACG,GAAG,CAAC,CAAA;AAC7C,MAAMC,SAAS,GAAGN,OAAO,CAACC,KAAK,CAACC,SAAS,CAACK,IAAI,CAAC,CAAA;AAG/C,MAAMC,iBAAiB,GAAGR,OAAO,CAACS,MAAM,CAACP,SAAS,CAACQ,WAAW,CAAC,CAAA;AAC/D,MAAMC,cAAc,GAAGX,OAAO,CAACS,MAAM,CAACP,SAAS,CAACU,QAAQ,CAAC,CAAA;AACzD,MAAMC,WAAW,GAAGb,OAAO,CAACS,MAAM,CAACP,SAAS,CAACY,KAAK,CAAC,CAAA;AACnD,MAAMC,aAAa,GAAGf,OAAO,CAACS,MAAM,CAACP,SAAS,CAACc,OAAO,CAAC,CAAA;AACvD,MAAMC,aAAa,GAAGjB,OAAO,CAACS,MAAM,CAACP,SAAS,CAACgB,OAAO,CAAC,CAAA;AACvD,MAAMC,UAAU,GAAGnB,OAAO,CAACS,MAAM,CAACP,SAAS,CAACkB,IAAI,CAAC,CAAA;AAEjD,MAAMC,oBAAoB,GAAGrB,OAAO,CAACb,MAAM,CAACe,SAAS,CAACoB,cAAc,CAAC,CAAA;AAErE,MAAMC,UAAU,GAAGvB,OAAO,CAACwB,MAAM,CAACtB,SAAS,CAACuB,IAAI,CAAC,CAAA;AAEjD,MAAMC,eAAe,GAAGC,WAAW,CAACC,SAAS,CAAC,CAAA;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA,SAAS5B,OAAOA,CAAC6B,IAAI,EAAE;AACrB,EAAA,OAAO,UAACC,OAAO,EAAA;IAAA,KAAAC,IAAAA,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAKpC,IAAI,OAAAI,KAAA,CAAA8B,IAAA,GAAAA,CAAAA,GAAAA,IAAA,WAAAG,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA,EAAA,EAAA;AAAJrC,MAAAA,IAAI,CAAAqC,IAAA,GAAAF,CAAAA,CAAAA,GAAAA,SAAA,CAAAE,IAAA,CAAA,CAAA;AAAA,KAAA;AAAA,IAAA,OAAK3C,KAAK,CAACsC,IAAI,EAAEC,OAAO,EAAEjC,IAAI,CAAC,CAAA;AAAA,GAAA,CAAA;AACzD,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8B,WAAWA,CAACE,IAAI,EAAE;EACzB,OAAO,YAAA;AAAA,IAAA,KAAA,IAAAM,KAAA,GAAAH,SAAA,CAAAC,MAAA,EAAIpC,IAAI,GAAAI,IAAAA,KAAA,CAAAkC,KAAA,GAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;AAAJvC,MAAAA,IAAI,CAAAuC,KAAA,CAAAJ,GAAAA,SAAA,CAAAI,KAAA,CAAA,CAAA;AAAA,KAAA;AAAA,IAAA,OAAK5C,SAAS,CAACqC,IAAI,EAAEhC,IAAI,CAAC,CAAA;AAAA,GAAA,CAAA;AAC3C,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwC,QAAQA,CAACC,GAAG,EAAEC,KAAK,EAAyC;AAAA,EAAA,IAAvCC,iBAAiB,GAAAR,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAS,SAAA,GAAAT,SAAA,CAAA,CAAA,CAAA,GAAGxB,iBAAiB,CAAA;AACjE,EAAA,IAAIzB,cAAc,EAAE;AAClB;AACA;AACA;AACAA,IAAAA,cAAc,CAACuD,GAAG,EAAE,IAAI,CAAC,CAAA;AAC3B,GAAA;AAEA,EAAA,IAAII,CAAC,GAAGH,KAAK,CAACN,MAAM,CAAA;EACpB,OAAOS,CAAC,EAAE,EAAE;AACV,IAAA,IAAIC,OAAO,GAAGJ,KAAK,CAACG,CAAC,CAAC,CAAA;AACtB,IAAA,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;AAC/B,MAAA,MAAMC,SAAS,GAAGJ,iBAAiB,CAACG,OAAO,CAAC,CAAA;MAC5C,IAAIC,SAAS,KAAKD,OAAO,EAAE;AACzB;AACA,QAAA,IAAI,CAAC3D,QAAQ,CAACuD,KAAK,CAAC,EAAE;AACpBA,UAAAA,KAAK,CAACG,CAAC,CAAC,GAAGE,SAAS,CAAA;AACtB,SAAA;AAEAD,QAAAA,OAAO,GAAGC,SAAS,CAAA;AACrB,OAAA;AACF,KAAA;AAEAN,IAAAA,GAAG,CAACK,OAAO,CAAC,GAAG,IAAI,CAAA;AACrB,GAAA;AAEA,EAAA,OAAOL,GAAG,CAAA;AACZ,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,UAAUA,CAACN,KAAK,EAAE;AACzB,EAAA,KAAK,IAAIO,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGP,KAAK,CAACN,MAAM,EAAEa,KAAK,EAAE,EAAE;AACjD,IAAA,MAAMC,eAAe,GAAG1B,oBAAoB,CAACkB,KAAK,EAAEO,KAAK,CAAC,CAAA;IAE1D,IAAI,CAACC,eAAe,EAAE;AACpBR,MAAAA,KAAK,CAACO,KAAK,CAAC,GAAG,IAAI,CAAA;AACrB,KAAA;AACF,GAAA;AAEA,EAAA,OAAOP,KAAK,CAAA;AACd,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,KAAKA,CAACC,MAAM,EAAE;AACrB,EAAA,MAAMC,SAAS,GAAG5D,MAAM,CAAC,IAAI,CAAC,CAAA;EAE9B,KAAK,MAAM,CAAC6D,QAAQ,EAAEC,KAAK,CAAC,IAAItE,OAAO,CAACmE,MAAM,CAAC,EAAE;AAC/C,IAAA,MAAMF,eAAe,GAAG1B,oBAAoB,CAAC4B,MAAM,EAAEE,QAAQ,CAAC,CAAA;AAE9D,IAAA,IAAIJ,eAAe,EAAE;AACnB,MAAA,IAAI9C,KAAK,CAACoD,OAAO,CAACD,KAAK,CAAC,EAAE;AACxBF,QAAAA,SAAS,CAACC,QAAQ,CAAC,GAAGN,UAAU,CAACO,KAAK,CAAC,CAAA;AACzC,OAAC,MAAM,IACLA,KAAK,IACL,OAAOA,KAAK,KAAK,QAAQ,IACzBA,KAAK,CAACE,WAAW,KAAKnE,MAAM,EAC5B;AACA+D,QAAAA,SAAS,CAACC,QAAQ,CAAC,GAAGH,KAAK,CAACI,KAAK,CAAC,CAAA;AACpC,OAAC,MAAM;AACLF,QAAAA,SAAS,CAACC,QAAQ,CAAC,GAAGC,KAAK,CAAA;AAC7B,OAAA;AACF,KAAA;AACF,GAAA;AAEA,EAAA,OAAOF,SAAS,CAAA;AAClB,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,YAAYA,CAACN,MAAM,EAAEO,IAAI,EAAE;EAClC,OAAOP,MAAM,KAAK,IAAI,EAAE;AACtB,IAAA,MAAMQ,IAAI,GAAGvE,wBAAwB,CAAC+D,MAAM,EAAEO,IAAI,CAAC,CAAA;AAEnD,IAAA,IAAIC,IAAI,EAAE;MACR,IAAIA,IAAI,CAACC,GAAG,EAAE;AACZ,QAAA,OAAO1D,OAAO,CAACyD,IAAI,CAACC,GAAG,CAAC,CAAA;AAC1B,OAAA;AAEA,MAAA,IAAI,OAAOD,IAAI,CAACL,KAAK,KAAK,UAAU,EAAE;AACpC,QAAA,OAAOpD,OAAO,CAACyD,IAAI,CAACL,KAAK,CAAC,CAAA;AAC5B,OAAA;AACF,KAAA;AAEAH,IAAAA,MAAM,GAAGhE,cAAc,CAACgE,MAAM,CAAC,CAAA;AACjC,GAAA;EAEA,SAASU,aAAaA,GAAG;AACvB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEA,EAAA,OAAOA,aAAa,CAAA;AACtB;;AC1LO,MAAMC,MAAI,GAAGxE,MAAM,CAAC,CACzB,GAAG,EACH,MAAM,EACN,SAAS,EACT,SAAS,EACT,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACP,GAAG,EACH,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP,YAAY,EACZ,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,MAAM,EACN,MAAM,EACN,KAAK,EACL,UAAU,EACV,SAAS,EACT,MAAM,EACN,UAAU,EACV,IAAI,EACJ,WAAW,EACX,KAAK,EACL,SAAS,EACT,KAAK,EACL,QAAQ,EACR,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,GAAG,EACH,KAAK,EACL,OAAO,EACP,KAAK,EACL,KAAK,EACL,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,KAAK,EACL,MAAM,EACN,SAAS,EACT,MAAM,EACN,UAAU,EACV,OAAO,EACP,KAAK,EACL,MAAM,EACN,IAAI,EACJ,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,GAAG,EACH,SAAS,EACT,KAAK,EACL,UAAU,EACV,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,GAAG,EACH,MAAM,EACN,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,KAAK,EACL,SAAS,EACT,KAAK,EACL,OAAO,EACP,OAAO,EACP,IAAI,EACJ,UAAU,EACV,UAAU,EACV,OAAO,EACP,IAAI,EACJ,OAAO,EACP,MAAM,EACN,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,KAAK,EACL,OAAO,EACP,KAAK,CACN,CAAC,CAAA;;AAEF;AACO,MAAMyE,KAAG,GAAGzE,MAAM,CAAC,CACxB,KAAK,EACL,GAAG,EACH,UAAU,EACV,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,EACf,kBAAkB,EAClB,QAAQ,EACR,UAAU,EACV,MAAM,EACN,MAAM,EACN,SAAS,EACT,QAAQ,EACR,MAAM,EACN,GAAG,EACH,OAAO,EACP,UAAU,EACV,OAAO,EACP,OAAO,EACP,MAAM,EACN,gBAAgB,EAChB,QAAQ,EACR,MAAM,EACN,UAAU,EACV,OAAO,EACP,MAAM,EACN,SAAS,EACT,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,MAAM,EACN,MAAM,EACN,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,CACR,CAAC,CAAA;AAEK,MAAM0E,UAAU,GAAG1E,MAAM,CAAC,CAC/B,SAAS,EACT,eAAe,EACf,qBAAqB,EACrB,aAAa,EACb,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,gBAAgB,EAChB,cAAc,EACd,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,gBAAgB,EAChB,SAAS,EACT,SAAS,EACT,aAAa,EACb,cAAc,EACd,UAAU,EACV,cAAc,EACd,oBAAoB,EACpB,aAAa,EACb,QAAQ,EACR,cAAc,CACf,CAAC,CAAA;;AAEF;AACA;AACA;AACA;AACO,MAAM2E,aAAa,GAAG3E,MAAM,CAAC,CAClC,SAAS,EACT,eAAe,EACf,QAAQ,EACR,SAAS,EACT,WAAW,EACX,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,eAAe,EACf,OAAO,EACP,WAAW,EACX,MAAM,EACN,cAAc,EACd,WAAW,EACX,SAAS,EACT,eAAe,EACf,QAAQ,EACR,KAAK,EACL,YAAY,EACZ,SAAS,EACT,KAAK,CACN,CAAC,CAAA;AAEK,MAAM4E,QAAM,GAAG5E,MAAM,CAAC,CAC3B,MAAM,EACN,UAAU,EACV,QAAQ,EACR,SAAS,EACT,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,eAAe,EACf,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,EACP,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,MAAM,EACN,MAAM,EACN,SAAS,EACT,QAAQ,EACR,KAAK,EACL,OAAO,EACP,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,aAAa,CACd,CAAC,CAAA;;AAEF;AACA;AACO,MAAM6E,gBAAgB,GAAG7E,MAAM,CAAC,CACrC,SAAS,EACT,aAAa,EACb,YAAY,EACZ,UAAU,EACV,WAAW,EACX,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,WAAW,EACX,YAAY,EACZ,gBAAgB,EAChB,aAAa,EACb,MAAM,CACP,CAAC,CAAA;AAEK,MAAM8E,IAAI,GAAG9E,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;;ACrR9B,MAAMwE,IAAI,GAAGxE,MAAM,CAAC,CACzB,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,KAAK,EACL,gBAAgB,EAChB,cAAc,EACd,sBAAsB,EACtB,UAAU,EACV,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,SAAS,EACT,aAAa,EACb,aAAa,EACb,SAAS,EACT,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,SAAS,EACT,UAAU,EACV,cAAc,EACd,QAAQ,EACR,aAAa,EACb,UAAU,EACV,UAAU,EACV,SAAS,EACT,KAAK,EACL,UAAU,EACV,yBAAyB,EACzB,uBAAuB,EACvB,UAAU,EACV,WAAW,EACX,SAAS,EACT,cAAc,EACd,MAAM,EACN,KAAK,EACL,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,UAAU,EACV,IAAI,EACJ,WAAW,EACX,WAAW,EACX,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,MAAM,EACN,SAAS,EACT,MAAM,EACN,KAAK,EACL,KAAK,EACL,WAAW,EACX,OAAO,EACP,QAAQ,EACR,KAAK,EACL,WAAW,EACX,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,SAAS,EACT,SAAS,EACT,aAAa,EACb,aAAa,EACb,SAAS,EACT,eAAe,EACf,qBAAqB,EACrB,QAAQ,EACR,SAAS,EACT,SAAS,EACT,YAAY,EACZ,UAAU,EACV,KAAK,EACL,UAAU,EACV,KAAK,EACL,UAAU,EACV,MAAM,EACN,MAAM,EACN,SAAS,EACT,YAAY,EACZ,OAAO,EACP,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,SAAS,EACT,OAAO,EACP,KAAK,EACL,QAAQ,EACR,MAAM,EACN,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,EACP,WAAW,EACX,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,CACP,CAAC,CAAA;AAEK,MAAMyE,GAAG,GAAGzE,MAAM,CAAC,CACxB,eAAe,EACf,YAAY,EACZ,UAAU,EACV,oBAAoB,EACpB,QAAQ,EACR,eAAe,EACf,eAAe,EACf,SAAS,EACT,eAAe,EACf,gBAAgB,EAChB,OAAO,EACP,MAAM,EACN,IAAI,EACJ,OAAO,EACP,MAAM,EACN,eAAe,EACf,WAAW,EACX,WAAW,EACX,OAAO,EACP,qBAAqB,EACrB,6BAA6B,EAC7B,eAAe,EACf,iBAAiB,EACjB,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,iBAAiB,EACjB,WAAW,EACX,SAAS,EACT,SAAS,EACT,KAAK,EACL,UAAU,EACV,WAAW,EACX,KAAK,EACL,MAAM,EACN,cAAc,EACd,WAAW,EACX,QAAQ,EACR,aAAa,EACb,aAAa,EACb,eAAe,EACf,aAAa,EACb,WAAW,EACX,kBAAkB,EAClB,cAAc,EACd,YAAY,EACZ,cAAc,EACd,aAAa,EACb,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,YAAY,EACZ,UAAU,EACV,eAAe,EACf,mBAAmB,EACnB,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,iBAAiB,EACjB,IAAI,EACJ,KAAK,EACL,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,WAAW,EACX,YAAY,EACZ,UAAU,EACV,MAAM,EACN,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,kBAAkB,EAClB,gBAAgB,EAChB,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,cAAc,EACd,aAAa,EACb,aAAa,EACb,kBAAkB,EAClB,WAAW,EACX,KAAK,EACL,MAAM,EACN,OAAO,EACP,QAAQ,EACR,MAAM,EACN,KAAK,EACL,MAAM,EACN,YAAY,EACZ,QAAQ,EACR,UAAU,EACV,SAAS,EACT,OAAO,EACP,QAAQ,EACR,aAAa,EACb,QAAQ,EACR,UAAU,EACV,aAAa,EACb,MAAM,EACN,YAAY,EACZ,qBAAqB,EACrB,kBAAkB,EAClB,cAAc,EACd,QAAQ,EACR,eAAe,EACf,qBAAqB,EACrB,gBAAgB,EAChB,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,MAAM,EACN,MAAM,EACN,aAAa,EACb,WAAW,EACX,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,MAAM,EACN,iBAAiB,EACjB,kBAAkB,EAClB,kBAAkB,EAClB,cAAc,EACd,aAAa,EACb,cAAc,EACd,aAAa,EACb,YAAY,EACZ,cAAc,EACd,kBAAkB,EAClB,mBAAmB,EACnB,gBAAgB,EAChB,iBAAiB,EACjB,mBAAmB,EACnB,gBAAgB,EAChB,QAAQ,EACR,cAAc,EACd,OAAO,EACP,cAAc,EACd,gBAAgB,EAChB,UAAU,EACV,SAAS,EACT,SAAS,EACT,WAAW,EACX,kBAAkB,EAClB,aAAa,EACb,iBAAiB,EACjB,gBAAgB,EAChB,YAAY,EACZ,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,eAAe,EACf,eAAe,EACf,OAAO,EACP,cAAc,EACd,MAAM,EACN,cAAc,EACd,kBAAkB,EAClB,kBAAkB,EAClB,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,YAAY,CACb,CAAC,CAAA;AAEK,MAAM4E,MAAM,GAAG5E,MAAM,CAAC,CAC3B,QAAQ,EACR,aAAa,EACb,OAAO,EACP,UAAU,EACV,OAAO,EACP,cAAc,EACd,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,KAAK,EACL,SAAS,EACT,cAAc,EACd,UAAU,EACV,OAAO,EACP,OAAO,EACP,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,eAAe,EACf,QAAQ,EACR,QAAQ,EACR,gBAAgB,EAChB,WAAW,EACX,UAAU,EACV,aAAa,EACb,SAAS,EACT,SAAS,EACT,eAAe,EACf,UAAU,EACV,UAAU,EACV,MAAM,EACN,UAAU,EACV,UAAU,EACV,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,aAAa,EACb,eAAe,EACf,sBAAsB,EACtB,WAAW,EACX,WAAW,EACX,YAAY,EACZ,UAAU,EACV,gBAAgB,EAChB,gBAAgB,EAChB,WAAW,EACX,SAAS,EACT,OAAO,EACP,OAAO,CACR,CAAC,CAAA;AAEK,MAAM+E,GAAG,GAAG/E,MAAM,CAAC,CACxB,YAAY,EACZ,QAAQ,EACR,aAAa,EACb,WAAW,EACX,aAAa,CACd,CAAC;;AC3WF;AACO,MAAMgF,aAAa,GAAG/E,IAAI,CAAC,2BAA2B,CAAC,CAAC;AACxD,MAAMgF,QAAQ,GAAGhF,IAAI,CAAC,uBAAuB,CAAC,CAAA;AAC9C,MAAMiF,WAAW,GAAGjF,IAAI,CAAC,eAAe,CAAC,CAAA;AACzC,MAAMkF,SAAS,GAAGlF,IAAI,CAAC,4BAA4B,CAAC,CAAC;AACrD,MAAMmF,SAAS,GAAGnF,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACzC,MAAMoF,cAAc,GAAGpF,IAAI,CAChC,2FAA2F;AAC7F,CAAC,CAAA;AACM,MAAMqF,iBAAiB,GAAGrF,IAAI,CAAC,uBAAuB,CAAC,CAAA;AACvD,MAAMsF,eAAe,GAAGtF,IAAI,CACjC,6DAA6D;AAC/D,CAAC,CAAA;AACM,MAAMuF,YAAY,GAAGvF,IAAI,CAAC,SAAS,CAAC,CAAA;AACpC,MAAMwF,cAAc,GAAGxF,IAAI,CAAC,0BAA0B,CAAC;;;;;;;;;;;;;;;;ACQ9D;AACA,MAAMyF,SAAS,GAAG;AAChBnC,EAAAA,OAAO,EAAE,CAAC;AACVoC,EAAAA,SAAS,EAAE,CAAC;AACZb,EAAAA,IAAI,EAAE,CAAC;AACPc,EAAAA,YAAY,EAAE,CAAC;AACfC,EAAAA,eAAe,EAAE,CAAC;AAAE;AACpBC,EAAAA,UAAU,EAAE,CAAC;AAAE;AACfC,EAAAA,sBAAsB,EAAE,CAAC;AACzBC,EAAAA,OAAO,EAAE,CAAC;AACVC,EAAAA,QAAQ,EAAE,CAAC;AACXC,EAAAA,YAAY,EAAE,EAAE;AAChBC,EAAAA,gBAAgB,EAAE,EAAE;EACpBC,QAAQ,EAAE,EAAE;AACd,CAAC,CAAA;AAED,MAAMC,SAAS,GAAG,SAAZA,SAASA,GAAe;AAC5B,EAAA,OAAO,OAAOC,MAAM,KAAK,WAAW,GAAG,IAAI,GAAGA,MAAM,CAAA;AACtD,CAAC,CAAA;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAaC,YAAY,EAAEC,iBAAiB,EAAE;EAC3E,IACE,OAAOD,YAAY,KAAK,QAAQ,IAChC,OAAOA,YAAY,CAACE,YAAY,KAAK,UAAU,EAC/C;AACA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACA;AACA;EACA,IAAIC,MAAM,GAAG,IAAI,CAAA;EACjB,MAAMC,SAAS,GAAG,uBAAuB,CAAA;EACzC,IAAIH,iBAAiB,IAAIA,iBAAiB,CAACI,YAAY,CAACD,SAAS,CAAC,EAAE;AAClED,IAAAA,MAAM,GAAGF,iBAAiB,CAACK,YAAY,CAACF,SAAS,CAAC,CAAA;AACpD,GAAA;EAEA,MAAMG,UAAU,GAAG,WAAW,IAAIJ,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE,CAAC,CAAA;EAE7D,IAAI;AACF,IAAA,OAAOH,YAAY,CAACE,YAAY,CAACK,UAAU,EAAE;MAC3CC,UAAUA,CAACxC,IAAI,EAAE;AACf,QAAA,OAAOA,IAAI,CAAA;OACZ;MACDyC,eAAeA,CAACC,SAAS,EAAE;AACzB,QAAA,OAAOA,SAAS,CAAA;AAClB,OAAA;AACF,KAAC,CAAC,CAAA;GACH,CAAC,OAAOC,CAAC,EAAE;AACV;AACA;AACA;IACAC,OAAO,CAACC,IAAI,CACV,sBAAsB,GAAGN,UAAU,GAAG,wBACxC,CAAC,CAAA;AACD,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACF,CAAC,CAAA;AAED,SAASO,eAAeA,GAAuB;AAAA,EAAA,IAAtBhB,MAAM,GAAA1D,SAAA,CAAAC,MAAA,GAAAD,CAAAA,IAAAA,SAAA,CAAAS,CAAAA,CAAAA,KAAAA,SAAA,GAAAT,SAAA,CAAGyD,CAAAA,CAAAA,GAAAA,SAAS,EAAE,CAAA;AAC3C,EAAA,MAAMkB,SAAS,GAAIC,IAAI,IAAKF,eAAe,CAACE,IAAI,CAAC,CAAA;;AAEjD;AACF;AACA;AACA;EACED,SAAS,CAACE,OAAO,GAAGC,OAAO,CAAA;;AAE3B;AACF;AACA;AACA;EACEH,SAAS,CAACI,OAAO,GAAG,EAAE,CAAA;AAEtB,EAAA,IACE,CAACrB,MAAM,IACP,CAACA,MAAM,CAACL,QAAQ,IAChBK,MAAM,CAACL,QAAQ,CAAC2B,QAAQ,KAAKlC,SAAS,CAACO,QAAQ,EAC/C;AACA;AACA;IACAsB,SAAS,CAACM,WAAW,GAAG,KAAK,CAAA;AAE7B,IAAA,OAAON,SAAS,CAAA;AAClB,GAAA;EAEA,IAAI;AAAEtB,IAAAA,QAAAA;AAAS,GAAC,GAAGK,MAAM,CAAA;EAEzB,MAAMwB,gBAAgB,GAAG7B,QAAQ,CAAA;AACjC,EAAA,MAAM8B,aAAa,GAAGD,gBAAgB,CAACC,aAAa,CAAA;EACpD,MAAM;IACJC,gBAAgB;IAChBC,mBAAmB;IACnBC,IAAI;IACJC,OAAO;IACPC,UAAU;AACVC,IAAAA,YAAY,GAAG/B,MAAM,CAAC+B,YAAY,IAAI/B,MAAM,CAACgC,eAAe;IAC5DC,eAAe;IACfC,SAAS;AACThC,IAAAA,YAAAA;AACF,GAAC,GAAGF,MAAM,CAAA;AAEV,EAAA,MAAMmC,gBAAgB,GAAGN,OAAO,CAACrH,SAAS,CAAA;AAE1C,EAAA,MAAM4H,SAAS,GAAGvE,YAAY,CAACsE,gBAAgB,EAAE,WAAW,CAAC,CAAA;AAC7D,EAAA,MAAME,MAAM,GAAGxE,YAAY,CAACsE,gBAAgB,EAAE,QAAQ,CAAC,CAAA;AACvD,EAAA,MAAMG,cAAc,GAAGzE,YAAY,CAACsE,gBAAgB,EAAE,aAAa,CAAC,CAAA;AACpE,EAAA,MAAMI,aAAa,GAAG1E,YAAY,CAACsE,gBAAgB,EAAE,YAAY,CAAC,CAAA;AAClE,EAAA,MAAMK,aAAa,GAAG3E,YAAY,CAACsE,gBAAgB,EAAE,YAAY,CAAC,CAAA;;AAElE;AACA;AACA;AACA;AACA;AACA;AACA,EAAA,IAAI,OAAOR,mBAAmB,KAAK,UAAU,EAAE;AAC7C,IAAA,MAAMc,QAAQ,GAAG9C,QAAQ,CAAC+C,aAAa,CAAC,UAAU,CAAC,CAAA;IACnD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACE,OAAO,CAACC,aAAa,EAAE;AACtDjD,MAAAA,QAAQ,GAAG8C,QAAQ,CAACE,OAAO,CAACC,aAAa,CAAA;AAC3C,KAAA;AACF,GAAA;AAEA,EAAA,IAAIC,kBAAkB,CAAA;EACtB,IAAIC,SAAS,GAAG,EAAE,CAAA;EAElB,MAAM;IACJC,cAAc;IACdC,kBAAkB;IAClBC,sBAAsB;AACtBC,IAAAA,oBAAAA;AACF,GAAC,GAAGvD,QAAQ,CAAA;EACZ,MAAM;AAAEwD,IAAAA,UAAAA;AAAW,GAAC,GAAG3B,gBAAgB,CAAA;EAEvC,IAAI4B,KAAK,GAAG,EAAE,CAAA;;AAEd;AACF;AACA;AACEnC,EAAAA,SAAS,CAACM,WAAW,GACnB,OAAOnI,OAAO,KAAK,UAAU,IAC7B,OAAOoJ,aAAa,KAAK,UAAU,IACnCO,cAAc,IACdA,cAAc,CAACM,kBAAkB,KAAKtG,SAAS,CAAA;EAEjD,MAAM;IACJ2B,aAAa;IACbC,QAAQ;IACRC,WAAW;IACXC,SAAS;IACTC,SAAS;IACTE,iBAAiB;IACjBC,eAAe;AACfE,IAAAA,cAAAA;AACF,GAAC,GAAGmE,WAAW,CAAA;EAEf,IAAI;AAAEvE,oBAAAA,gBAAAA;AAAe,GAAC,GAAGuE,WAAW,CAAA;;AAEpC;AACF;AACA;AACA;;AAEE;EACA,IAAIC,YAAY,GAAG,IAAI,CAAA;AACvB,EAAA,MAAMC,oBAAoB,GAAG7G,QAAQ,CAAC,EAAE,EAAE,CACxC,GAAG8G,MAAS,EACZ,GAAGA,KAAQ,EACX,GAAGA,UAAe,EAClB,GAAGA,QAAW,EACd,GAAGA,IAAS,CACb,CAAC,CAAA;;AAEF;EACA,IAAIC,YAAY,GAAG,IAAI,CAAA;AACvB,EAAA,MAAMC,oBAAoB,GAAGhH,QAAQ,CAAC,EAAE,EAAE,CACxC,GAAGiH,IAAU,EACb,GAAGA,GAAS,EACZ,GAAGA,MAAY,EACf,GAAGA,GAAS,CACb,CAAC,CAAA;;AAEF;AACF;AACA;AACA;AACA;AACA;EACE,IAAIC,uBAAuB,GAAGpK,MAAM,CAACE,IAAI,CACvCC,MAAM,CAAC,IAAI,EAAE;AACXkK,IAAAA,YAAY,EAAE;AACZC,MAAAA,QAAQ,EAAE,IAAI;AACdC,MAAAA,YAAY,EAAE,KAAK;AACnBC,MAAAA,UAAU,EAAE,IAAI;AAChBvG,MAAAA,KAAK,EAAE,IAAA;KACR;AACDwG,IAAAA,kBAAkB,EAAE;AAClBH,MAAAA,QAAQ,EAAE,IAAI;AACdC,MAAAA,YAAY,EAAE,KAAK;AACnBC,MAAAA,UAAU,EAAE,IAAI;AAChBvG,MAAAA,KAAK,EAAE,IAAA;KACR;AACDyG,IAAAA,8BAA8B,EAAE;AAC9BJ,MAAAA,QAAQ,EAAE,IAAI;AACdC,MAAAA,YAAY,EAAE,KAAK;AACnBC,MAAAA,UAAU,EAAE,IAAI;AAChBvG,MAAAA,KAAK,EAAE,KAAA;AACT,KAAA;AACF,GAAC,CACH,CAAC,CAAA;;AAED;EACA,IAAI0G,WAAW,GAAG,IAAI,CAAA;;AAEtB;EACA,IAAIC,WAAW,GAAG,IAAI,CAAA;;AAEtB;EACA,IAAIC,eAAe,GAAG,IAAI,CAAA;;AAE1B;EACA,IAAIC,eAAe,GAAG,IAAI,CAAA;;AAE1B;EACA,IAAIC,uBAAuB,GAAG,KAAK,CAAA;;AAEnC;AACF;EACE,IAAIC,wBAAwB,GAAG,IAAI,CAAA;;AAEnC;AACF;AACA;EACE,IAAIC,kBAAkB,GAAG,KAAK,CAAA;;AAE9B;AACF;AACA;EACE,IAAIC,YAAY,GAAG,IAAI,CAAA;;AAEvB;EACA,IAAIC,cAAc,GAAG,KAAK,CAAA;;AAE1B;EACA,IAAIC,UAAU,GAAG,KAAK,CAAA;;AAEtB;AACF;EACE,IAAIC,UAAU,GAAG,KAAK,CAAA;;AAEtB;AACF;AACA;AACA;EACE,IAAIC,UAAU,GAAG,KAAK,CAAA;;AAEtB;AACF;EACE,IAAIC,mBAAmB,GAAG,KAAK,CAAA;;AAE/B;AACF;EACE,IAAIC,mBAAmB,GAAG,KAAK,CAAA;;AAE/B;AACF;AACA;EACE,IAAIC,YAAY,GAAG,IAAI,CAAA;;AAEvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIC,oBAAoB,GAAG,KAAK,CAAA;EAChC,MAAMC,2BAA2B,GAAG,eAAe,CAAA;;AAEnD;EACA,IAAIC,YAAY,GAAG,IAAI,CAAA;;AAEvB;AACF;EACE,IAAIC,QAAQ,GAAG,KAAK,CAAA;;AAEpB;EACA,IAAIC,YAAY,GAAG,EAAE,CAAA;;AAErB;EACA,IAAIC,eAAe,GAAG,IAAI,CAAA;EAC1B,MAAMC,uBAAuB,GAAG9I,QAAQ,CAAC,EAAE,EAAE,CAC3C,gBAAgB,EAChB,OAAO,EACP,UAAU,EACV,MAAM,EACN,eAAe,EACf,MAAM,EACN,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,SAAS,EACT,UAAU,EACV,UAAU,EACV,WAAW,EACX,QAAQ,EACR,OAAO,EACP,KAAK,EACL,UAAU,EACV,OAAO,EACP,OAAO,EACP,OAAO,EACP,KAAK,CACN,CAAC,CAAA;;AAEF;EACA,IAAI+I,aAAa,GAAG,IAAI,CAAA;EACxB,MAAMC,qBAAqB,GAAGhJ,QAAQ,CAAC,EAAE,EAAE,CACzC,OAAO,EACP,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,EACP,OAAO,CACR,CAAC,CAAA;;AAEF;EACA,IAAIiJ,mBAAmB,GAAG,IAAI,CAAA;AAC9B,EAAA,MAAMC,2BAA2B,GAAGlJ,QAAQ,CAAC,EAAE,EAAE,CAC/C,KAAK,EACL,OAAO,EACP,KAAK,EACL,IAAI,EACJ,OAAO,EACP,MAAM,EACN,SAAS,EACT,aAAa,EACb,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACR,CAAC,CAAA;EAEF,MAAMmJ,gBAAgB,GAAG,oCAAoC,CAAA;EAC7D,MAAMC,aAAa,GAAG,4BAA4B,CAAA;EAClD,MAAMC,cAAc,GAAG,8BAA8B,CAAA;AACrD;EACA,IAAIC,SAAS,GAAGD,cAAc,CAAA;EAC9B,IAAIE,cAAc,GAAG,KAAK,CAAA;;AAE1B;EACA,IAAIC,kBAAkB,GAAG,IAAI,CAAA;AAC7B,EAAA,MAAMC,0BAA0B,GAAGzJ,QAAQ,CACzC,EAAE,EACF,CAACmJ,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,CAAC,EACjD/K,cACF,CAAC,CAAA;;AAED;EACA,IAAIoL,iBAAiB,GAAG,IAAI,CAAA;AAC5B,EAAA,MAAMC,4BAA4B,GAAG,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAA;EAC3E,MAAMC,yBAAyB,GAAG,WAAW,CAAA;EAC7C,IAAIzJ,iBAAiB,GAAG,IAAI,CAAA;;AAE5B;EACA,IAAI0J,MAAM,GAAG,IAAI,CAAA;;AAEjB;AACA;;AAEA,EAAA,MAAMC,WAAW,GAAG9G,QAAQ,CAAC+C,aAAa,CAAC,MAAM,CAAC,CAAA;AAElD,EAAA,MAAMgE,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaC,SAAS,EAAE;AAC7C,IAAA,OAAOA,SAAS,YAAY7K,MAAM,IAAI6K,SAAS,YAAYC,QAAQ,CAAA;GACpE,CAAA;;AAED;AACF;AACA;AACA;AACA;AACE;AACA,EAAA,MAAMC,YAAY,GAAG,SAAfA,YAAYA,GAAuB;AAAA,IAAA,IAAVC,GAAG,GAAAxK,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAS,SAAA,GAAAT,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;AACrC,IAAA,IAAIkK,MAAM,IAAIA,MAAM,KAAKM,GAAG,EAAE;AAC5B,MAAA,OAAA;AACF,KAAA;;AAEA;AACA,IAAA,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACnCA,GAAG,GAAG,EAAE,CAAA;AACV,KAAA;;AAEA;AACAA,IAAAA,GAAG,GAAGxJ,KAAK,CAACwJ,GAAG,CAAC,CAAA;IAEhBT,iBAAiB;AACf;AACAC,IAAAA,4BAA4B,CAAC9K,OAAO,CAACsL,GAAG,CAACT,iBAAiB,CAAC,KAAK,CAAC,CAAC,GAC9DE,yBAAyB,GACzBO,GAAG,CAACT,iBAAiB,CAAA;;AAE3B;AACAvJ,IAAAA,iBAAiB,GACfuJ,iBAAiB,KAAK,uBAAuB,GACzCpL,cAAc,GACdH,iBAAiB,CAAA;;AAEvB;IACAyI,YAAY,GAAG5H,oBAAoB,CAACmL,GAAG,EAAE,cAAc,CAAC,GACpDnK,QAAQ,CAAC,EAAE,EAAEmK,GAAG,CAACvD,YAAY,EAAEzG,iBAAiB,CAAC,GACjD0G,oBAAoB,CAAA;IACxBE,YAAY,GAAG/H,oBAAoB,CAACmL,GAAG,EAAE,cAAc,CAAC,GACpDnK,QAAQ,CAAC,EAAE,EAAEmK,GAAG,CAACpD,YAAY,EAAE5G,iBAAiB,CAAC,GACjD6G,oBAAoB,CAAA;IACxBwC,kBAAkB,GAAGxK,oBAAoB,CAACmL,GAAG,EAAE,oBAAoB,CAAC,GAChEnK,QAAQ,CAAC,EAAE,EAAEmK,GAAG,CAACX,kBAAkB,EAAElL,cAAc,CAAC,GACpDmL,0BAA0B,CAAA;AAC9BR,IAAAA,mBAAmB,GAAGjK,oBAAoB,CAACmL,GAAG,EAAE,mBAAmB,CAAC,GAChEnK,QAAQ,CACNW,KAAK,CAACuI,2BAA2B,CAAC;AAAE;AACpCiB,IAAAA,GAAG,CAACC,iBAAiB;AAAE;AACvBjK,IAAAA,iBAAiB;AACnB,KAAC;AAAC,MACF+I,2BAA2B,CAAA;AAC/BH,IAAAA,aAAa,GAAG/J,oBAAoB,CAACmL,GAAG,EAAE,mBAAmB,CAAC,GAC1DnK,QAAQ,CACNW,KAAK,CAACqI,qBAAqB,CAAC;AAAE;AAC9BmB,IAAAA,GAAG,CAACE,iBAAiB;AAAE;AACvBlK,IAAAA,iBAAiB;AACnB,KAAC;AAAC,MACF6I,qBAAqB,CAAA;IACzBH,eAAe,GAAG7J,oBAAoB,CAACmL,GAAG,EAAE,iBAAiB,CAAC,GAC1DnK,QAAQ,CAAC,EAAE,EAAEmK,GAAG,CAACtB,eAAe,EAAE1I,iBAAiB,CAAC,GACpD2I,uBAAuB,CAAA;IAC3BrB,WAAW,GAAGzI,oBAAoB,CAACmL,GAAG,EAAE,aAAa,CAAC,GAClDnK,QAAQ,CAAC,EAAE,EAAEmK,GAAG,CAAC1C,WAAW,EAAEtH,iBAAiB,CAAC,GAChD,EAAE,CAAA;IACNuH,WAAW,GAAG1I,oBAAoB,CAACmL,GAAG,EAAE,aAAa,CAAC,GAClDnK,QAAQ,CAAC,EAAE,EAAEmK,GAAG,CAACzC,WAAW,EAAEvH,iBAAiB,CAAC,GAChD,EAAE,CAAA;AACNyI,IAAAA,YAAY,GAAG5J,oBAAoB,CAACmL,GAAG,EAAE,cAAc,CAAC,GACpDA,GAAG,CAACvB,YAAY,GAChB,KAAK,CAAA;AACTjB,IAAAA,eAAe,GAAGwC,GAAG,CAACxC,eAAe,KAAK,KAAK,CAAC;AAChDC,IAAAA,eAAe,GAAGuC,GAAG,CAACvC,eAAe,KAAK,KAAK,CAAC;AAChDC,IAAAA,uBAAuB,GAAGsC,GAAG,CAACtC,uBAAuB,IAAI,KAAK,CAAC;AAC/DC,IAAAA,wBAAwB,GAAGqC,GAAG,CAACrC,wBAAwB,KAAK,KAAK,CAAC;AAClEC,IAAAA,kBAAkB,GAAGoC,GAAG,CAACpC,kBAAkB,IAAI,KAAK,CAAC;AACrDC,IAAAA,YAAY,GAAGmC,GAAG,CAACnC,YAAY,KAAK,KAAK,CAAC;AAC1CC,IAAAA,cAAc,GAAGkC,GAAG,CAAClC,cAAc,IAAI,KAAK,CAAC;AAC7CG,IAAAA,UAAU,GAAG+B,GAAG,CAAC/B,UAAU,IAAI,KAAK,CAAC;AACrCC,IAAAA,mBAAmB,GAAG8B,GAAG,CAAC9B,mBAAmB,IAAI,KAAK,CAAC;AACvDC,IAAAA,mBAAmB,GAAG6B,GAAG,CAAC7B,mBAAmB,IAAI,KAAK,CAAC;AACvDH,IAAAA,UAAU,GAAGgC,GAAG,CAAChC,UAAU,IAAI,KAAK,CAAC;AACrCI,IAAAA,YAAY,GAAG4B,GAAG,CAAC5B,YAAY,KAAK,KAAK,CAAC;AAC1CC,IAAAA,oBAAoB,GAAG2B,GAAG,CAAC3B,oBAAoB,IAAI,KAAK,CAAC;AACzDE,IAAAA,YAAY,GAAGyB,GAAG,CAACzB,YAAY,KAAK,KAAK,CAAC;AAC1CC,IAAAA,QAAQ,GAAGwB,GAAG,CAACxB,QAAQ,IAAI,KAAK,CAAC;AACjCvG,IAAAA,gBAAc,GAAG+H,GAAG,CAACG,kBAAkB,IAAI3D,cAA0B,CAAA;AACrE2C,IAAAA,SAAS,GAAGa,GAAG,CAACb,SAAS,IAAID,cAAc,CAAA;AAC3CnC,IAAAA,uBAAuB,GAAGiD,GAAG,CAACjD,uBAAuB,IAAI,EAAE,CAAA;AAC3D,IAAA,IACEiD,GAAG,CAACjD,uBAAuB,IAC3B6C,iBAAiB,CAACI,GAAG,CAACjD,uBAAuB,CAACC,YAAY,CAAC,EAC3D;AACAD,MAAAA,uBAAuB,CAACC,YAAY,GAClCgD,GAAG,CAACjD,uBAAuB,CAACC,YAAY,CAAA;AAC5C,KAAA;AAEA,IAAA,IACEgD,GAAG,CAACjD,uBAAuB,IAC3B6C,iBAAiB,CAACI,GAAG,CAACjD,uBAAuB,CAACK,kBAAkB,CAAC,EACjE;AACAL,MAAAA,uBAAuB,CAACK,kBAAkB,GACxC4C,GAAG,CAACjD,uBAAuB,CAACK,kBAAkB,CAAA;AAClD,KAAA;AAEA,IAAA,IACE4C,GAAG,CAACjD,uBAAuB,IAC3B,OAAOiD,GAAG,CAACjD,uBAAuB,CAACM,8BAA8B,KAC/D,SAAS,EACX;AACAN,MAAAA,uBAAuB,CAACM,8BAA8B,GACpD2C,GAAG,CAACjD,uBAAuB,CAACM,8BAA8B,CAAA;AAC9D,KAAA;AAEA,IAAA,IAAIO,kBAAkB,EAAE;AACtBH,MAAAA,eAAe,GAAG,KAAK,CAAA;AACzB,KAAA;AAEA,IAAA,IAAIS,mBAAmB,EAAE;AACvBD,MAAAA,UAAU,GAAG,IAAI,CAAA;AACnB,KAAA;;AAEA;AACA,IAAA,IAAIQ,YAAY,EAAE;MAChBhC,YAAY,GAAG5G,QAAQ,CAAC,EAAE,EAAE8G,IAAS,CAAC,CAAA;AACtCC,MAAAA,YAAY,GAAG,EAAE,CAAA;AACjB,MAAA,IAAI6B,YAAY,CAACrH,IAAI,KAAK,IAAI,EAAE;AAC9BvB,QAAAA,QAAQ,CAAC4G,YAAY,EAAEE,MAAS,CAAC,CAAA;AACjC9G,QAAAA,QAAQ,CAAC+G,YAAY,EAAEE,IAAU,CAAC,CAAA;AACpC,OAAA;AAEA,MAAA,IAAI2B,YAAY,CAACpH,GAAG,KAAK,IAAI,EAAE;AAC7BxB,QAAAA,QAAQ,CAAC4G,YAAY,EAAEE,KAAQ,CAAC,CAAA;AAChC9G,QAAAA,QAAQ,CAAC+G,YAAY,EAAEE,GAAS,CAAC,CAAA;AACjCjH,QAAAA,QAAQ,CAAC+G,YAAY,EAAEE,GAAS,CAAC,CAAA;AACnC,OAAA;AAEA,MAAA,IAAI2B,YAAY,CAACnH,UAAU,KAAK,IAAI,EAAE;AACpCzB,QAAAA,QAAQ,CAAC4G,YAAY,EAAEE,UAAe,CAAC,CAAA;AACvC9G,QAAAA,QAAQ,CAAC+G,YAAY,EAAEE,GAAS,CAAC,CAAA;AACjCjH,QAAAA,QAAQ,CAAC+G,YAAY,EAAEE,GAAS,CAAC,CAAA;AACnC,OAAA;AAEA,MAAA,IAAI2B,YAAY,CAACjH,MAAM,KAAK,IAAI,EAAE;AAChC3B,QAAAA,QAAQ,CAAC4G,YAAY,EAAEE,QAAW,CAAC,CAAA;AACnC9G,QAAAA,QAAQ,CAAC+G,YAAY,EAAEE,MAAY,CAAC,CAAA;AACpCjH,QAAAA,QAAQ,CAAC+G,YAAY,EAAEE,GAAS,CAAC,CAAA;AACnC,OAAA;AACF,KAAA;;AAEA;IACA,IAAIkD,GAAG,CAACI,QAAQ,EAAE;MAChB,IAAI3D,YAAY,KAAKC,oBAAoB,EAAE;AACzCD,QAAAA,YAAY,GAAGjG,KAAK,CAACiG,YAAY,CAAC,CAAA;AACpC,OAAA;MAEA5G,QAAQ,CAAC4G,YAAY,EAAEuD,GAAG,CAACI,QAAQ,EAAEpK,iBAAiB,CAAC,CAAA;AACzD,KAAA;IAEA,IAAIgK,GAAG,CAACK,QAAQ,EAAE;MAChB,IAAIzD,YAAY,KAAKC,oBAAoB,EAAE;AACzCD,QAAAA,YAAY,GAAGpG,KAAK,CAACoG,YAAY,CAAC,CAAA;AACpC,OAAA;MAEA/G,QAAQ,CAAC+G,YAAY,EAAEoD,GAAG,CAACK,QAAQ,EAAErK,iBAAiB,CAAC,CAAA;AACzD,KAAA;IAEA,IAAIgK,GAAG,CAACC,iBAAiB,EAAE;MACzBpK,QAAQ,CAACiJ,mBAAmB,EAAEkB,GAAG,CAACC,iBAAiB,EAAEjK,iBAAiB,CAAC,CAAA;AACzE,KAAA;IAEA,IAAIgK,GAAG,CAACtB,eAAe,EAAE;MACvB,IAAIA,eAAe,KAAKC,uBAAuB,EAAE;AAC/CD,QAAAA,eAAe,GAAGlI,KAAK,CAACkI,eAAe,CAAC,CAAA;AAC1C,OAAA;MAEA7I,QAAQ,CAAC6I,eAAe,EAAEsB,GAAG,CAACtB,eAAe,EAAE1I,iBAAiB,CAAC,CAAA;AACnE,KAAA;;AAEA;AACA,IAAA,IAAIuI,YAAY,EAAE;AAChB9B,MAAAA,YAAY,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;AAC9B,KAAA;;AAEA;AACA,IAAA,IAAIqB,cAAc,EAAE;MAClBjI,QAAQ,CAAC4G,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAA;AAClD,KAAA;;AAEA;IACA,IAAIA,YAAY,CAAC6D,KAAK,EAAE;AACtBzK,MAAAA,QAAQ,CAAC4G,YAAY,EAAE,CAAC,OAAO,CAAC,CAAC,CAAA;MACjC,OAAOa,WAAW,CAACiD,KAAK,CAAA;AAC1B,KAAA;IAEA,IAAIP,GAAG,CAACQ,oBAAoB,EAAE;MAC5B,IAAI,OAAOR,GAAG,CAACQ,oBAAoB,CAAC5G,UAAU,KAAK,UAAU,EAAE;QAC7D,MAAM1E,eAAe,CACnB,6EACF,CAAC,CAAA;AACH,OAAA;MAEA,IAAI,OAAO8K,GAAG,CAACQ,oBAAoB,CAAC3G,eAAe,KAAK,UAAU,EAAE;QAClE,MAAM3E,eAAe,CACnB,kFACF,CAAC,CAAA;AACH,OAAA;;AAEA;MACA6G,kBAAkB,GAAGiE,GAAG,CAACQ,oBAAoB,CAAA;;AAE7C;AACAxE,MAAAA,SAAS,GAAGD,kBAAkB,CAACnC,UAAU,CAAC,EAAE,CAAC,CAAA;AAC/C,KAAC,MAAM;AACL;MACA,IAAImC,kBAAkB,KAAK9F,SAAS,EAAE;AACpC8F,QAAAA,kBAAkB,GAAG5C,yBAAyB,CAC5CC,YAAY,EACZuB,aACF,CAAC,CAAA;AACH,OAAA;;AAEA;MACA,IAAIoB,kBAAkB,KAAK,IAAI,IAAI,OAAOC,SAAS,KAAK,QAAQ,EAAE;AAChEA,QAAAA,SAAS,GAAGD,kBAAkB,CAACnC,UAAU,CAAC,EAAE,CAAC,CAAA;AAC/C,OAAA;AACF,KAAA;;AAEA;AACA;AACA,IAAA,IAAIhH,MAAM,EAAE;MACVA,MAAM,CAACoN,GAAG,CAAC,CAAA;AACb,KAAA;AAEAN,IAAAA,MAAM,GAAGM,GAAG,CAAA;GACb,CAAA;AAED,EAAA,MAAMS,8BAA8B,GAAG5K,QAAQ,CAAC,EAAE,EAAE,CAClD,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,OAAO,CACR,CAAC,CAAA;AAEF,EAAA,MAAM6K,uBAAuB,GAAG7K,QAAQ,CAAC,EAAE,EAAE,CAC3C,eAAe,EACf,gBAAgB,CACjB,CAAC,CAAA;;AAEF;AACA;AACA;AACA;AACA,EAAA,MAAM8K,4BAA4B,GAAG9K,QAAQ,CAAC,EAAE,EAAE,CAChD,OAAO,EACP,OAAO,EACP,MAAM,EACN,GAAG,EACH,QAAQ,CACT,CAAC,CAAA;;AAEF;AACF;AACA;EACE,MAAM+K,YAAY,GAAG/K,QAAQ,CAAC,EAAE,EAAE,CAChC,GAAG8G,KAAQ,EACX,GAAGA,UAAe,EAClB,GAAGA,aAAkB,CACtB,CAAC,CAAA;AACF,EAAA,MAAMkE,eAAe,GAAGhL,QAAQ,CAAC,EAAE,EAAE,CACnC,GAAG8G,QAAW,EACd,GAAGA,gBAAqB,CACzB,CAAC,CAAA;;AAEF;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,MAAMmE,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAa3K,OAAO,EAAE;AAC9C,IAAA,IAAI4K,MAAM,GAAGrF,aAAa,CAACvF,OAAO,CAAC,CAAA;;AAEnC;AACA;AACA,IAAA,IAAI,CAAC4K,MAAM,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;AAC9BD,MAAAA,MAAM,GAAG;AACPE,QAAAA,YAAY,EAAE9B,SAAS;AACvB6B,QAAAA,OAAO,EAAE,UAAA;OACV,CAAA;AACH,KAAA;AAEA,IAAA,MAAMA,OAAO,GAAGhN,iBAAiB,CAACmC,OAAO,CAAC6K,OAAO,CAAC,CAAA;AAClD,IAAA,MAAME,aAAa,GAAGlN,iBAAiB,CAAC+M,MAAM,CAACC,OAAO,CAAC,CAAA;AAEvD,IAAA,IAAI,CAAC3B,kBAAkB,CAAClJ,OAAO,CAAC8K,YAAY,CAAC,EAAE;AAC7C,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;AAEA,IAAA,IAAI9K,OAAO,CAAC8K,YAAY,KAAKhC,aAAa,EAAE;AAC1C;AACA;AACA;AACA,MAAA,IAAI8B,MAAM,CAACE,YAAY,KAAK/B,cAAc,EAAE;QAC1C,OAAO8B,OAAO,KAAK,KAAK,CAAA;AAC1B,OAAA;;AAEA;AACA;AACA;AACA,MAAA,IAAID,MAAM,CAACE,YAAY,KAAKjC,gBAAgB,EAAE;AAC5C,QAAA,OACEgC,OAAO,KAAK,KAAK,KAChBE,aAAa,KAAK,gBAAgB,IACjCT,8BAA8B,CAACS,aAAa,CAAC,CAAC,CAAA;AAEpD,OAAA;;AAEA;AACA;AACA,MAAA,OAAOC,OAAO,CAACP,YAAY,CAACI,OAAO,CAAC,CAAC,CAAA;AACvC,KAAA;AAEA,IAAA,IAAI7K,OAAO,CAAC8K,YAAY,KAAKjC,gBAAgB,EAAE;AAC7C;AACA;AACA;AACA,MAAA,IAAI+B,MAAM,CAACE,YAAY,KAAK/B,cAAc,EAAE;QAC1C,OAAO8B,OAAO,KAAK,MAAM,CAAA;AAC3B,OAAA;;AAEA;AACA;AACA,MAAA,IAAID,MAAM,CAACE,YAAY,KAAKhC,aAAa,EAAE;AACzC,QAAA,OAAO+B,OAAO,KAAK,MAAM,IAAIN,uBAAuB,CAACQ,aAAa,CAAC,CAAA;AACrE,OAAA;;AAEA;AACA;AACA,MAAA,OAAOC,OAAO,CAACN,eAAe,CAACG,OAAO,CAAC,CAAC,CAAA;AAC1C,KAAA;AAEA,IAAA,IAAI7K,OAAO,CAAC8K,YAAY,KAAK/B,cAAc,EAAE;AAC3C;AACA;AACA;MACA,IACE6B,MAAM,CAACE,YAAY,KAAKhC,aAAa,IACrC,CAACyB,uBAAuB,CAACQ,aAAa,CAAC,EACvC;AACA,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;MAEA,IACEH,MAAM,CAACE,YAAY,KAAKjC,gBAAgB,IACxC,CAACyB,8BAA8B,CAACS,aAAa,CAAC,EAC9C;AACA,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;;AAEA;AACA;AACA,MAAA,OACE,CAACL,eAAe,CAACG,OAAO,CAAC,KACxBL,4BAA4B,CAACK,OAAO,CAAC,IAAI,CAACJ,YAAY,CAACI,OAAO,CAAC,CAAC,CAAA;AAErE,KAAA;;AAEA;IACA,IACEzB,iBAAiB,KAAK,uBAAuB,IAC7CF,kBAAkB,CAAClJ,OAAO,CAAC8K,YAAY,CAAC,EACxC;AACA,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;AACA;AACA;AACA;AACA,IAAA,OAAO,KAAK,CAAA;GACb,CAAA;;AAED;AACF;AACA;AACA;AACA;AACE,EAAA,MAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAaC,IAAI,EAAE;AACnCvN,IAAAA,SAAS,CAACqG,SAAS,CAACI,OAAO,EAAE;AAAEpE,MAAAA,OAAO,EAAEkL,IAAAA;AAAK,KAAC,CAAC,CAAA;IAE/C,IAAI;AACF;AACA3F,MAAAA,aAAa,CAAC2F,IAAI,CAAC,CAACC,WAAW,CAACD,IAAI,CAAC,CAAA;KACtC,CAAC,OAAOtH,CAAC,EAAE;MACVwB,MAAM,CAAC8F,IAAI,CAAC,CAAA;AACd,KAAA;GACD,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAME,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAaC,IAAI,EAAEH,IAAI,EAAE;IAC7C,IAAI;AACFvN,MAAAA,SAAS,CAACqG,SAAS,CAACI,OAAO,EAAE;AAC3BhC,QAAAA,SAAS,EAAE8I,IAAI,CAACI,gBAAgB,CAACD,IAAI,CAAC;AACtCE,QAAAA,IAAI,EAAEL,IAAAA;AACR,OAAC,CAAC,CAAA;KACH,CAAC,OAAOtH,CAAC,EAAE;AACVjG,MAAAA,SAAS,CAACqG,SAAS,CAACI,OAAO,EAAE;AAC3BhC,QAAAA,SAAS,EAAE,IAAI;AACfmJ,QAAAA,IAAI,EAAEL,IAAAA;AACR,OAAC,CAAC,CAAA;AACJ,KAAA;AAEAA,IAAAA,IAAI,CAACM,eAAe,CAACH,IAAI,CAAC,CAAA;;AAE1B;IACA,IAAIA,IAAI,KAAK,IAAI,IAAI,CAAC5E,YAAY,CAAC4E,IAAI,CAAC,EAAE;MACxC,IAAIvD,UAAU,IAAIC,mBAAmB,EAAE;QACrC,IAAI;UACFkD,YAAY,CAACC,IAAI,CAAC,CAAA;AACpB,SAAC,CAAC,OAAOtH,CAAC,EAAE,EAAC;AACf,OAAC,MAAM;QACL,IAAI;AACFsH,UAAAA,IAAI,CAACO,YAAY,CAACJ,IAAI,EAAE,EAAE,CAAC,CAAA;AAC7B,SAAC,CAAC,OAAOzH,CAAC,EAAE,EAAC;AACf,OAAA;AACF,KAAA;GACD,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,MAAM8H,aAAa,GAAG,SAAhBA,aAAaA,CAAaC,KAAK,EAAE;AACrC;IACA,IAAIC,GAAG,GAAG,IAAI,CAAA;IACd,IAAIC,iBAAiB,GAAG,IAAI,CAAA;AAE5B,IAAA,IAAIhE,UAAU,EAAE;MACd8D,KAAK,GAAG,mBAAmB,GAAGA,KAAK,CAAA;AACrC,KAAC,MAAM;AACL;AACA,MAAA,MAAMG,OAAO,GAAG5N,WAAW,CAACyN,KAAK,EAAE,aAAa,CAAC,CAAA;AACjDE,MAAAA,iBAAiB,GAAGC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,CAAA;AAC3C,KAAA;AAEA,IAAA,IACE1C,iBAAiB,KAAK,uBAAuB,IAC7CJ,SAAS,KAAKD,cAAc,EAC5B;AACA;AACA4C,MAAAA,KAAK,GACH,gEAAgE,GAChEA,KAAK,GACL,gBAAgB,CAAA;AACpB,KAAA;IAEA,MAAMI,YAAY,GAAGnG,kBAAkB,GACnCA,kBAAkB,CAACnC,UAAU,CAACkI,KAAK,CAAC,GACpCA,KAAK,CAAA;AACT;AACJ;AACA;AACA;IACI,IAAI3C,SAAS,KAAKD,cAAc,EAAE;MAChC,IAAI;QACF6C,GAAG,GAAG,IAAI3G,SAAS,EAAE,CAAC+G,eAAe,CAACD,YAAY,EAAE3C,iBAAiB,CAAC,CAAA;AACxE,OAAC,CAAC,OAAOxF,CAAC,EAAE,EAAC;AACf,KAAA;;AAEA;AACA,IAAA,IAAI,CAACgI,GAAG,IAAI,CAACA,GAAG,CAACK,eAAe,EAAE;MAChCL,GAAG,GAAG9F,cAAc,CAACoG,cAAc,CAAClD,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;MAChE,IAAI;QACF4C,GAAG,CAACK,eAAe,CAACE,SAAS,GAAGlD,cAAc,GAC1CpD,SAAS,GACTkG,YAAY,CAAA;OACjB,CAAC,OAAOnI,CAAC,EAAE;AACV;AAAA,OAAA;AAEJ,KAAA;IAEA,MAAMwI,IAAI,GAAGR,GAAG,CAACQ,IAAI,IAAIR,GAAG,CAACK,eAAe,CAAA;IAE5C,IAAIN,KAAK,IAAIE,iBAAiB,EAAE;AAC9BO,MAAAA,IAAI,CAACC,YAAY,CACf3J,QAAQ,CAAC4J,cAAc,CAACT,iBAAiB,CAAC,EAC1CO,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,IAAI,IACxB,CAAC,CAAA;AACH,KAAA;;AAEA;IACA,IAAIvD,SAAS,KAAKD,cAAc,EAAE;AAChC,MAAA,OAAO9C,oBAAoB,CAACuG,IAAI,CAC9BZ,GAAG,EACHjE,cAAc,GAAG,MAAM,GAAG,MAC5B,CAAC,CAAC,CAAC,CAAC,CAAA;AACN,KAAA;AAEA,IAAA,OAAOA,cAAc,GAAGiE,GAAG,CAACK,eAAe,GAAGG,IAAI,CAAA;GACnD,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,MAAMK,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAaxI,IAAI,EAAE;IAC1C,OAAO8B,kBAAkB,CAACyG,IAAI,CAC5BvI,IAAI,CAAC0B,aAAa,IAAI1B,IAAI,EAC1BA,IAAI;AACJ;IACAY,UAAU,CAAC6H,YAAY,GACrB7H,UAAU,CAAC8H,YAAY,GACvB9H,UAAU,CAAC+H,SAAS,GACpB/H,UAAU,CAACgI,2BAA2B,GACtChI,UAAU,CAACiI,kBAAkB,EAC/B,IACF,CAAC,CAAA;GACF,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,MAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAaC,GAAG,EAAE;AAClC,IAAA,OACEA,GAAG,YAAYhI,eAAe,KAC7B,OAAOgI,GAAG,CAACC,QAAQ,KAAK,QAAQ,IAC/B,OAAOD,GAAG,CAACE,WAAW,KAAK,QAAQ,IACnC,OAAOF,GAAG,CAAC7B,WAAW,KAAK,UAAU,IACrC,EAAE6B,GAAG,CAACG,UAAU,YAAYrI,YAAY,CAAC,IACzC,OAAOkI,GAAG,CAACxB,eAAe,KAAK,UAAU,IACzC,OAAOwB,GAAG,CAACvB,YAAY,KAAK,UAAU,IACtC,OAAOuB,GAAG,CAAClC,YAAY,KAAK,QAAQ,IACpC,OAAOkC,GAAG,CAACX,YAAY,KAAK,UAAU,IACtC,OAAOW,GAAG,CAACI,aAAa,KAAK,UAAU,CAAC,CAAA;GAE7C,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,MAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAa/M,MAAM,EAAE;AAChC,IAAA,OAAO,OAAOqE,IAAI,KAAK,UAAU,IAAIrE,MAAM,YAAYqE,IAAI,CAAA;GAC5D,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAM2I,YAAY,GAAG,SAAfA,YAAYA,CAAaC,UAAU,EAAEC,WAAW,EAAEC,IAAI,EAAE;AAC5D,IAAA,IAAI,CAACtH,KAAK,CAACoH,UAAU,CAAC,EAAE;AACtB,MAAA,OAAA;AACF,KAAA;AAEAnQ,IAAAA,YAAY,CAAC+I,KAAK,CAACoH,UAAU,CAAC,EAAGG,IAAI,IAAK;MACxCA,IAAI,CAAClB,IAAI,CAACxI,SAAS,EAAEwJ,WAAW,EAAEC,IAAI,EAAElE,MAAM,CAAC,CAAA;AACjD,KAAC,CAAC,CAAA;GACH,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,MAAMoE,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaH,WAAW,EAAE;IAC/C,IAAI9H,OAAO,GAAG,IAAI,CAAA;;AAElB;AACA4H,IAAAA,YAAY,CAAC,wBAAwB,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;;AAEzD;AACA,IAAA,IAAIT,YAAY,CAACS,WAAW,CAAC,EAAE;MAC7BvC,YAAY,CAACuC,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;AACA,IAAA,MAAM3C,OAAO,GAAGhL,iBAAiB,CAAC2N,WAAW,CAACP,QAAQ,CAAC,CAAA;;AAEvD;AACAK,IAAAA,YAAY,CAAC,qBAAqB,EAAEE,WAAW,EAAE;MAC/C3C,OAAO;AACP+C,MAAAA,WAAW,EAAEtH,YAAAA;AACf,KAAC,CAAC,CAAA;;AAEF;AACA,IAAA,IACEkH,WAAW,CAACJ,aAAa,EAAE,IAC3B,CAACC,OAAO,CAACG,WAAW,CAACK,iBAAiB,CAAC,IACvCjP,UAAU,CAAC,SAAS,EAAE4O,WAAW,CAACrB,SAAS,CAAC,IAC5CvN,UAAU,CAAC,SAAS,EAAE4O,WAAW,CAACN,WAAW,CAAC,EAC9C;MACAjC,YAAY,CAACuC,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;AACA,IAAA,IAAIA,WAAW,CAACnJ,QAAQ,KAAKlC,SAAS,CAACK,sBAAsB,EAAE;MAC7DyI,YAAY,CAACuC,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;AACA,IAAA,IACE9F,YAAY,IACZ8F,WAAW,CAACnJ,QAAQ,KAAKlC,SAAS,CAACM,OAAO,IAC1C7D,UAAU,CAAC,SAAS,EAAE4O,WAAW,CAACC,IAAI,CAAC,EACvC;MACAxC,YAAY,CAACuC,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;IACA,IAAI,CAAClH,YAAY,CAACuE,OAAO,CAAC,IAAI1D,WAAW,CAAC0D,OAAO,CAAC,EAAE;AAClD;MACA,IAAI,CAAC1D,WAAW,CAAC0D,OAAO,CAAC,IAAIiD,qBAAqB,CAACjD,OAAO,CAAC,EAAE;AAC3D,QAAA,IACEjE,uBAAuB,CAACC,YAAY,YAAYhI,MAAM,IACtDD,UAAU,CAACgI,uBAAuB,CAACC,YAAY,EAAEgE,OAAO,CAAC,EACzD;AACA,UAAA,OAAO,KAAK,CAAA;AACd,SAAA;AAEA,QAAA,IACEjE,uBAAuB,CAACC,YAAY,YAAY8C,QAAQ,IACxD/C,uBAAuB,CAACC,YAAY,CAACgE,OAAO,CAAC,EAC7C;AACA,UAAA,OAAO,KAAK,CAAA;AACd,SAAA;AACF,OAAA;;AAEA;AACA,MAAA,IAAIzC,YAAY,IAAI,CAACG,eAAe,CAACsC,OAAO,CAAC,EAAE;QAC7C,MAAMkD,UAAU,GAAGxI,aAAa,CAACiI,WAAW,CAAC,IAAIA,WAAW,CAACO,UAAU,CAAA;QACvE,MAAMxB,UAAU,GAAGjH,aAAa,CAACkI,WAAW,CAAC,IAAIA,WAAW,CAACjB,UAAU,CAAA;QAEvE,IAAIA,UAAU,IAAIwB,UAAU,EAAE;AAC5B,UAAA,MAAMC,UAAU,GAAGzB,UAAU,CAACjN,MAAM,CAAA;AAEpC,UAAA,KAAK,IAAI2O,CAAC,GAAGD,UAAU,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;YACxC,MAAMC,UAAU,GAAG/I,SAAS,CAACoH,UAAU,CAAC0B,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;YACjDC,UAAU,CAACC,cAAc,GAAG,CAACX,WAAW,CAACW,cAAc,IAAI,CAAC,IAAI,CAAC,CAAA;YACjEJ,UAAU,CAAC1B,YAAY,CAAC6B,UAAU,EAAE7I,cAAc,CAACmI,WAAW,CAAC,CAAC,CAAA;AAClE,WAAA;AACF,SAAA;AACF,OAAA;MAEAvC,YAAY,CAACuC,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;IACA,IAAIA,WAAW,YAAY5I,OAAO,IAAI,CAAC+F,oBAAoB,CAAC6C,WAAW,CAAC,EAAE;MACxEvC,YAAY,CAACuC,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;IACA,IACE,CAAC3C,OAAO,KAAK,UAAU,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,UAAU,KACxBjM,UAAU,CAAC,6BAA6B,EAAE4O,WAAW,CAACrB,SAAS,CAAC,EAChE;MACAlB,YAAY,CAACuC,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;IACA,IAAI/F,kBAAkB,IAAI+F,WAAW,CAACnJ,QAAQ,KAAKlC,SAAS,CAACZ,IAAI,EAAE;AACjE;MACAmE,OAAO,GAAG8H,WAAW,CAACN,WAAW,CAAA;MAEjC9P,YAAY,CAAC,CAACqE,aAAa,EAAEC,QAAQ,EAAEC,WAAW,CAAC,EAAGyM,IAAI,IAAK;QAC7D1I,OAAO,GAAGtH,aAAa,CAACsH,OAAO,EAAE0I,IAAI,EAAE,GAAG,CAAC,CAAA;AAC7C,OAAC,CAAC,CAAA;AAEF,MAAA,IAAIZ,WAAW,CAACN,WAAW,KAAKxH,OAAO,EAAE;AACvC/H,QAAAA,SAAS,CAACqG,SAAS,CAACI,OAAO,EAAE;AAAEpE,UAAAA,OAAO,EAAEwN,WAAW,CAACrI,SAAS,EAAC;AAAE,SAAC,CAAC,CAAA;QAClEqI,WAAW,CAACN,WAAW,GAAGxH,OAAO,CAAA;AACnC,OAAA;AACF,KAAA;;AAEA;AACA4H,IAAAA,YAAY,CAAC,uBAAuB,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;AAExD,IAAA,OAAO,KAAK,CAAA;GACb,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACE;EACA,MAAMa,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaC,KAAK,EAAEC,MAAM,EAAE9N,KAAK,EAAE;AACxD;AACA,IAAA,IACEwH,YAAY,KACXsG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,KACrC9N,KAAK,IAAIiC,QAAQ,IAAIjC,KAAK,IAAI+I,WAAW,CAAC,EAC3C;AACA,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;;AAEA;AACJ;AACA;AACA;AACI,IAAA,IACElC,eAAe,IACf,CAACF,WAAW,CAACmH,MAAM,CAAC,IACpB3P,UAAU,CAACgD,SAAS,EAAE2M,MAAM,CAAC,EAC7B,CAED,MAAM,IAAIlH,eAAe,IAAIzI,UAAU,CAACiD,SAAS,EAAE0M,MAAM,CAAC,EAAE,CAG5D,MAAM,IAAI,CAAC9H,YAAY,CAAC8H,MAAM,CAAC,IAAInH,WAAW,CAACmH,MAAM,CAAC,EAAE;AACvD,MAAA;AACE;AACA;AACA;AACCT,MAAAA,qBAAqB,CAACQ,KAAK,CAAC,KACzB1H,uBAAuB,CAACC,YAAY,YAAYhI,MAAM,IACtDD,UAAU,CAACgI,uBAAuB,CAACC,YAAY,EAAEyH,KAAK,CAAC,IACtD1H,uBAAuB,CAACC,YAAY,YAAY8C,QAAQ,IACvD/C,uBAAuB,CAACC,YAAY,CAACyH,KAAK,CAAE,CAAC,KAC/C1H,uBAAuB,CAACK,kBAAkB,YAAYpI,MAAM,IAC5DD,UAAU,CAACgI,uBAAuB,CAACK,kBAAkB,EAAEsH,MAAM,CAAC,IAC7D3H,uBAAuB,CAACK,kBAAkB,YAAY0C,QAAQ,IAC7D/C,uBAAuB,CAACK,kBAAkB,CAACsH,MAAM,CAAE,CAAC;AAC1D;AACA;AACCA,MAAAA,MAAM,KAAK,IAAI,IACd3H,uBAAuB,CAACM,8BAA8B,KACpDN,uBAAuB,CAACC,YAAY,YAAYhI,MAAM,IACtDD,UAAU,CAACgI,uBAAuB,CAACC,YAAY,EAAEpG,KAAK,CAAC,IACtDmG,uBAAuB,CAACC,YAAY,YAAY8C,QAAQ,IACvD/C,uBAAuB,CAACC,YAAY,CAACpG,KAAK,CAAE,CAAE,EACpD,CAGD,MAAM;AACL,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;AACA;AACF,KAAC,MAAM,IAAIkI,mBAAmB,CAAC4F,MAAM,CAAC,EAAE,CAIvC,MAAM,IACL3P,UAAU,CAACkD,gBAAc,EAAE1D,aAAa,CAACqC,KAAK,EAAEuB,eAAe,EAAE,EAAE,CAAC,CAAC,EACrE,CAID,MAAM,IACL,CAACuM,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,MAAM,KACjED,KAAK,KAAK,QAAQ,IAClBhQ,aAAa,CAACmC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IACnCgI,aAAa,CAAC6F,KAAK,CAAC,EACpB,CAKD,MAAM,IACL/G,uBAAuB,IACvB,CAAC3I,UAAU,CAACmD,iBAAiB,EAAE3D,aAAa,CAACqC,KAAK,EAAEuB,eAAe,EAAE,EAAE,CAAC,CAAC,EACzE,CAGD,MAAM,IAAIvB,KAAK,EAAE;AAChB,MAAA,OAAO,KAAK,CAAA;AACd,KAAC,MAAM,CAEL;AAGF,IAAA,OAAO,IAAI,CAAA;GACZ,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,MAAMqN,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAajD,OAAO,EAAE;IAC/C,OAAOA,OAAO,KAAK,gBAAgB,IAAI3M,WAAW,CAAC2M,OAAO,EAAE3I,cAAc,CAAC,CAAA;GAC5E,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,MAAMsM,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAahB,WAAW,EAAE;AACjD;AACAF,IAAAA,YAAY,CAAC,0BAA0B,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;IAE3D,MAAM;AAAEL,MAAAA,UAAAA;AAAW,KAAC,GAAGK,WAAW,CAAA;;AAElC;IACA,IAAI,CAACL,UAAU,EAAE;AACf,MAAA,OAAA;AACF,KAAA;AAEA,IAAA,MAAMsB,SAAS,GAAG;AAChBC,MAAAA,QAAQ,EAAE,EAAE;AACZC,MAAAA,SAAS,EAAE,EAAE;AACbC,MAAAA,QAAQ,EAAE,IAAI;AACdC,MAAAA,iBAAiB,EAAEpI,YAAAA;KACpB,CAAA;AACD,IAAA,IAAI1G,CAAC,GAAGoN,UAAU,CAAC7N,MAAM,CAAA;;AAEzB;IACA,OAAOS,CAAC,EAAE,EAAE;AACV,MAAA,MAAM+O,IAAI,GAAG3B,UAAU,CAACpN,CAAC,CAAC,CAAA;MAC1B,MAAM;QAAEsL,IAAI;QAAEP,YAAY;AAAErK,QAAAA,KAAK,EAAEkO,SAAAA;AAAU,OAAC,GAAGG,IAAI,CAAA;AACrD,MAAA,MAAMP,MAAM,GAAG1O,iBAAiB,CAACwL,IAAI,CAAC,CAAA;MAEtC,IAAI5K,KAAK,GAAG4K,IAAI,KAAK,OAAO,GAAGsD,SAAS,GAAGnQ,UAAU,CAACmQ,SAAS,CAAC,CAAA;;AAEhE;MACAF,SAAS,CAACC,QAAQ,GAAGH,MAAM,CAAA;MAC3BE,SAAS,CAACE,SAAS,GAAGlO,KAAK,CAAA;MAC3BgO,SAAS,CAACG,QAAQ,GAAG,IAAI,CAAA;AACzBH,MAAAA,SAAS,CAACM,aAAa,GAAGjP,SAAS,CAAC;AACpCwN,MAAAA,YAAY,CAAC,uBAAuB,EAAEE,WAAW,EAAEiB,SAAS,CAAC,CAAA;MAC7DhO,KAAK,GAAGgO,SAAS,CAACE,SAAS,CAAA;;AAE3B;MACA,IAAIjH,YAAY,IAAI9I,UAAU,CAAC,+BAA+B,EAAE6B,KAAK,CAAC,EAAE;AACtE2K,QAAAA,gBAAgB,CAACC,IAAI,EAAEmC,WAAW,CAAC,CAAA;AACnC,QAAA,SAAA;AACF,OAAA;;AAEA;MACA,IAAIiB,SAAS,CAACM,aAAa,EAAE;AAC3B,QAAA,SAAA;AACF,OAAA;;AAEA;AACA3D,MAAAA,gBAAgB,CAACC,IAAI,EAAEmC,WAAW,CAAC,CAAA;;AAEnC;AACA,MAAA,IAAI,CAACiB,SAAS,CAACG,QAAQ,EAAE;AACvB,QAAA,SAAA;AACF,OAAA;;AAEA;MACA,IAAI,CAACpH,wBAAwB,IAAI5I,UAAU,CAAC,MAAM,EAAE6B,KAAK,CAAC,EAAE;AAC1D2K,QAAAA,gBAAgB,CAACC,IAAI,EAAEmC,WAAW,CAAC,CAAA;AACnC,QAAA,SAAA;AACF,OAAA;;AAEA;AACA,MAAA,IAAI/F,kBAAkB,EAAE;QACtBrK,YAAY,CAAC,CAACqE,aAAa,EAAEC,QAAQ,EAAEC,WAAW,CAAC,EAAGyM,IAAI,IAAK;UAC7D3N,KAAK,GAAGrC,aAAa,CAACqC,KAAK,EAAE2N,IAAI,EAAE,GAAG,CAAC,CAAA;AACzC,SAAC,CAAC,CAAA;AACJ,OAAA;;AAEA;AACA,MAAA,MAAME,KAAK,GAAGzO,iBAAiB,CAAC2N,WAAW,CAACP,QAAQ,CAAC,CAAA;MACrD,IAAI,CAACoB,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAE9N,KAAK,CAAC,EAAE;AAC5C,QAAA,SAAA;AACF,OAAA;;AAEA;AACN;AACA;MACM,IAAIyH,oBAAoB,KAAKqG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,EAAE;AAClE;AACAnD,QAAAA,gBAAgB,CAACC,IAAI,EAAEmC,WAAW,CAAC,CAAA;;AAEnC;QACA/M,KAAK,GAAG0H,2BAA2B,GAAG1H,KAAK,CAAA;AAC7C,OAAA;;AAEA;AACA,MAAA,IACEmF,kBAAkB,IAClB,OAAO3C,YAAY,KAAK,QAAQ,IAChC,OAAOA,YAAY,CAAC+L,gBAAgB,KAAK,UAAU,EACnD;AACA,QAAA,IAAIlE,YAAY,EAAE,CAEjB,MAAM;AACL,UAAA,QAAQ7H,YAAY,CAAC+L,gBAAgB,CAACV,KAAK,EAAEC,MAAM,CAAC;AAClD,YAAA,KAAK,aAAa;AAAE,cAAA;AAClB9N,gBAAAA,KAAK,GAAGmF,kBAAkB,CAACnC,UAAU,CAAChD,KAAK,CAAC,CAAA;AAC5C,gBAAA,MAAA;AACF,eAAA;AAEA,YAAA,KAAK,kBAAkB;AAAE,cAAA;AACvBA,gBAAAA,KAAK,GAAGmF,kBAAkB,CAAClC,eAAe,CAACjD,KAAK,CAAC,CAAA;AACjD,gBAAA,MAAA;AACF,eAAA;AAKF,WAAA;AACF,SAAA;AACF,OAAA;;AAEA;MACA,IAAI;AACF,QAAA,IAAIqK,YAAY,EAAE;UAChB0C,WAAW,CAACyB,cAAc,CAACnE,YAAY,EAAEO,IAAI,EAAE5K,KAAK,CAAC,CAAA;AACvD,SAAC,MAAM;AACL;AACA+M,UAAAA,WAAW,CAAC/B,YAAY,CAACJ,IAAI,EAAE5K,KAAK,CAAC,CAAA;AACvC,SAAA;AAEA,QAAA,IAAIsM,YAAY,CAACS,WAAW,CAAC,EAAE;UAC7BvC,YAAY,CAACuC,WAAW,CAAC,CAAA;AAC3B,SAAC,MAAM;AACL/P,UAAAA,QAAQ,CAACuG,SAAS,CAACI,OAAO,CAAC,CAAA;AAC7B,SAAA;AACF,OAAC,CAAC,OAAOR,CAAC,EAAE,EAAC;AACf,KAAA;;AAEA;AACA0J,IAAAA,YAAY,CAAC,yBAAyB,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;GAC3D,CAAA;;AAED;AACF;AACA;AACA;AACA;AACE,EAAA,MAAM0B,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAaC,QAAQ,EAAE;IAC7C,IAAIC,UAAU,GAAG,IAAI,CAAA;AACrB,IAAA,MAAMC,cAAc,GAAG5C,mBAAmB,CAAC0C,QAAQ,CAAC,CAAA;;AAEpD;AACA7B,IAAAA,YAAY,CAAC,yBAAyB,EAAE6B,QAAQ,EAAE,IAAI,CAAC,CAAA;AAEvD,IAAA,OAAQC,UAAU,GAAGC,cAAc,CAACC,QAAQ,EAAE,EAAG;AAC/C;AACAhC,MAAAA,YAAY,CAAC,wBAAwB,EAAE8B,UAAU,EAAE,IAAI,CAAC,CAAA;;AAExD;AACA,MAAA,IAAIzB,iBAAiB,CAACyB,UAAU,CAAC,EAAE;AACjC,QAAA,SAAA;AACF,OAAA;;AAEA;AACA,MAAA,IAAIA,UAAU,CAAC1J,OAAO,YAAYjB,gBAAgB,EAAE;AAClDyK,QAAAA,kBAAkB,CAACE,UAAU,CAAC1J,OAAO,CAAC,CAAA;AACxC,OAAA;;AAEA;MACA8I,mBAAmB,CAACY,UAAU,CAAC,CAAA;AACjC,KAAA;;AAEA;AACA9B,IAAAA,YAAY,CAAC,wBAAwB,EAAE6B,QAAQ,EAAE,IAAI,CAAC,CAAA;GACvD,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACE;AACAnL,EAAAA,SAAS,CAACuL,QAAQ,GAAG,UAAU5D,KAAK,EAAY;AAAA,IAAA,IAAV9B,GAAG,GAAAxK,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAS,SAAA,GAAAT,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;IAC5C,IAAI+M,IAAI,GAAG,IAAI,CAAA;IACf,IAAIoD,YAAY,GAAG,IAAI,CAAA;IACvB,IAAIhC,WAAW,GAAG,IAAI,CAAA;IACtB,IAAIiC,UAAU,GAAG,IAAI,CAAA;AACrB;AACJ;AACA;IACIxG,cAAc,GAAG,CAAC0C,KAAK,CAAA;AACvB,IAAA,IAAI1C,cAAc,EAAE;AAClB0C,MAAAA,KAAK,GAAG,OAAO,CAAA;AACjB,KAAA;;AAEA;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAAC0B,OAAO,CAAC1B,KAAK,CAAC,EAAE;AAChD,MAAA,IAAI,OAAOA,KAAK,CAAC1N,QAAQ,KAAK,UAAU,EAAE;AACxC0N,QAAAA,KAAK,GAAGA,KAAK,CAAC1N,QAAQ,EAAE,CAAA;AACxB,QAAA,IAAI,OAAO0N,KAAK,KAAK,QAAQ,EAAE;UAC7B,MAAM5M,eAAe,CAAC,iCAAiC,CAAC,CAAA;AAC1D,SAAA;AACF,OAAC,MAAM;QACL,MAAMA,eAAe,CAAC,4BAA4B,CAAC,CAAA;AACrD,OAAA;AACF,KAAA;;AAEA;AACA,IAAA,IAAI,CAACiF,SAAS,CAACM,WAAW,EAAE;AAC1B,MAAA,OAAOqH,KAAK,CAAA;AACd,KAAA;;AAEA;IACA,IAAI,CAAC/D,UAAU,EAAE;MACfgC,YAAY,CAACC,GAAG,CAAC,CAAA;AACnB,KAAA;;AAEA;IACA7F,SAAS,CAACI,OAAO,GAAG,EAAE,CAAA;;AAEtB;AACA,IAAA,IAAI,OAAOuH,KAAK,KAAK,QAAQ,EAAE;AAC7BtD,MAAAA,QAAQ,GAAG,KAAK,CAAA;AAClB,KAAA;AAEA,IAAA,IAAIA,QAAQ,EAAE;AACZ;MACA,IAAIsD,KAAK,CAACsB,QAAQ,EAAE;AAClB,QAAA,MAAMpC,OAAO,GAAGhL,iBAAiB,CAAC8L,KAAK,CAACsB,QAAQ,CAAC,CAAA;QACjD,IAAI,CAAC3G,YAAY,CAACuE,OAAO,CAAC,IAAI1D,WAAW,CAAC0D,OAAO,CAAC,EAAE;UAClD,MAAM9L,eAAe,CACnB,yDACF,CAAC,CAAA;AACH,SAAA;AACF,OAAA;AACF,KAAC,MAAM,IAAI4M,KAAK,YAAYhH,IAAI,EAAE;AAChC;AACN;AACMyH,MAAAA,IAAI,GAAGV,aAAa,CAAC,SAAS,CAAC,CAAA;MAC/B8D,YAAY,GAAGpD,IAAI,CAACzG,aAAa,CAACO,UAAU,CAACyF,KAAK,EAAE,IAAI,CAAC,CAAA;AACzD,MAAA,IACE6D,YAAY,CAACnL,QAAQ,KAAKlC,SAAS,CAACnC,OAAO,IAC3CwP,YAAY,CAACvC,QAAQ,KAAK,MAAM,EAChC;AACA;AACAb,QAAAA,IAAI,GAAGoD,YAAY,CAAA;AACrB,OAAC,MAAM,IAAIA,YAAY,CAACvC,QAAQ,KAAK,MAAM,EAAE;AAC3Cb,QAAAA,IAAI,GAAGoD,YAAY,CAAA;AACrB,OAAC,MAAM;AACL;AACApD,QAAAA,IAAI,CAACsD,WAAW,CAACF,YAAY,CAAC,CAAA;AAChC,OAAA;AACF,KAAC,MAAM;AACL;AACA,MAAA,IACE,CAAC1H,UAAU,IACX,CAACL,kBAAkB,IACnB,CAACE,cAAc;AACf;MACAgE,KAAK,CAACpN,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EACzB;QACA,OAAOqH,kBAAkB,IAAIoC,mBAAmB,GAC5CpC,kBAAkB,CAACnC,UAAU,CAACkI,KAAK,CAAC,GACpCA,KAAK,CAAA;AACX,OAAA;;AAEA;AACAS,MAAAA,IAAI,GAAGV,aAAa,CAACC,KAAK,CAAC,CAAA;;AAE3B;MACA,IAAI,CAACS,IAAI,EAAE;QACT,OAAOtE,UAAU,GAAG,IAAI,GAAGE,mBAAmB,GAAGnC,SAAS,GAAG,EAAE,CAAA;AACjE,OAAA;AACF,KAAA;;AAEA;IACA,IAAIuG,IAAI,IAAIvE,UAAU,EAAE;AACtBoD,MAAAA,YAAY,CAACmB,IAAI,CAACuD,UAAU,CAAC,CAAA;AAC/B,KAAA;;AAEA;IACA,MAAMC,YAAY,GAAGnD,mBAAmB,CAACpE,QAAQ,GAAGsD,KAAK,GAAGS,IAAI,CAAC,CAAA;;AAEjE;AACA,IAAA,OAAQoB,WAAW,GAAGoC,YAAY,CAACN,QAAQ,EAAE,EAAG;AAC9C;AACA,MAAA,IAAI3B,iBAAiB,CAACH,WAAW,CAAC,EAAE;AAClC,QAAA,SAAA;AACF,OAAA;;AAEA;AACA,MAAA,IAAIA,WAAW,CAAC9H,OAAO,YAAYjB,gBAAgB,EAAE;AACnDyK,QAAAA,kBAAkB,CAAC1B,WAAW,CAAC9H,OAAO,CAAC,CAAA;AACzC,OAAA;;AAEA;MACA8I,mBAAmB,CAAChB,WAAW,CAAC,CAAA;AAClC,KAAA;;AAEA;AACA,IAAA,IAAInF,QAAQ,EAAE;AACZ,MAAA,OAAOsD,KAAK,CAAA;AACd,KAAA;;AAEA;AACA,IAAA,IAAI7D,UAAU,EAAE;AACd,MAAA,IAAIC,mBAAmB,EAAE;QACvB0H,UAAU,GAAGzJ,sBAAsB,CAACwG,IAAI,CAACJ,IAAI,CAACzG,aAAa,CAAC,CAAA;QAE5D,OAAOyG,IAAI,CAACuD,UAAU,EAAE;AACtB;AACAF,UAAAA,UAAU,CAACC,WAAW,CAACtD,IAAI,CAACuD,UAAU,CAAC,CAAA;AACzC,SAAA;AACF,OAAC,MAAM;AACLF,QAAAA,UAAU,GAAGrD,IAAI,CAAA;AACnB,OAAA;AAEA,MAAA,IAAI3F,YAAY,CAACoJ,UAAU,IAAIpJ,YAAY,CAACqJ,cAAc,EAAE;AAC1D;AACR;AACA;AACA;AACA;AACA;AACA;QACQL,UAAU,GAAGvJ,UAAU,CAACsG,IAAI,CAACjI,gBAAgB,EAAEkL,UAAU,EAAE,IAAI,CAAC,CAAA;AAClE,OAAA;AAEA,MAAA,OAAOA,UAAU,CAAA;AACnB,KAAA;IAEA,IAAIM,cAAc,GAAGpI,cAAc,GAAGyE,IAAI,CAAC4D,SAAS,GAAG5D,IAAI,CAACD,SAAS,CAAA;;AAErE;AACA,IAAA,IACExE,cAAc,IACdrB,YAAY,CAAC,UAAU,CAAC,IACxB8F,IAAI,CAACzG,aAAa,IAClByG,IAAI,CAACzG,aAAa,CAACsK,OAAO,IAC1B7D,IAAI,CAACzG,aAAa,CAACsK,OAAO,CAAC5E,IAAI,IAC/BzM,UAAU,CAACyH,YAAwB,EAAE+F,IAAI,CAACzG,aAAa,CAACsK,OAAO,CAAC5E,IAAI,CAAC,EACrE;AACA0E,MAAAA,cAAc,GACZ,YAAY,GAAG3D,IAAI,CAACzG,aAAa,CAACsK,OAAO,CAAC5E,IAAI,GAAG,KAAK,GAAG0E,cAAc,CAAA;AAC3E,KAAA;;AAEA;AACA,IAAA,IAAItI,kBAAkB,EAAE;MACtBrK,YAAY,CAAC,CAACqE,aAAa,EAAEC,QAAQ,EAAEC,WAAW,CAAC,EAAGyM,IAAI,IAAK;QAC7D2B,cAAc,GAAG3R,aAAa,CAAC2R,cAAc,EAAE3B,IAAI,EAAE,GAAG,CAAC,CAAA;AAC3D,OAAC,CAAC,CAAA;AACJ,KAAA;IAEA,OAAOxI,kBAAkB,IAAIoC,mBAAmB,GAC5CpC,kBAAkB,CAACnC,UAAU,CAACsM,cAAc,CAAC,GAC7CA,cAAc,CAAA;GACnB,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;EACE/L,SAAS,CAACkM,SAAS,GAAG,YAAoB;AAAA,IAAA,IAAVrG,GAAG,GAAAxK,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAS,SAAA,GAAAT,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;IACtCuK,YAAY,CAACC,GAAG,CAAC,CAAA;AACjBjC,IAAAA,UAAU,GAAG,IAAI,CAAA;GAClB,CAAA;;AAED;AACF;AACA;AACA;AACA;EACE5D,SAAS,CAACmM,WAAW,GAAG,YAAY;AAClC5G,IAAAA,MAAM,GAAG,IAAI,CAAA;AACb3B,IAAAA,UAAU,GAAG,KAAK,CAAA;GACnB,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE5D,SAAS,CAACoM,gBAAgB,GAAG,UAAUC,GAAG,EAAEvB,IAAI,EAAErO,KAAK,EAAE;AACvD;IACA,IAAI,CAAC8I,MAAM,EAAE;MACXK,YAAY,CAAC,EAAE,CAAC,CAAA;AAClB,KAAA;AAEA,IAAA,MAAM0E,KAAK,GAAGzO,iBAAiB,CAACwQ,GAAG,CAAC,CAAA;AACpC,IAAA,MAAM9B,MAAM,GAAG1O,iBAAiB,CAACiP,IAAI,CAAC,CAAA;AACtC,IAAA,OAAOT,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAE9N,KAAK,CAAC,CAAA;GAC/C,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACEuD,EAAAA,SAAS,CAACsM,OAAO,GAAG,UAAU/C,UAAU,EAAEgD,YAAY,EAAE;AACtD,IAAA,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;AACtC,MAAA,OAAA;AACF,KAAA;IAEApK,KAAK,CAACoH,UAAU,CAAC,GAAGpH,KAAK,CAACoH,UAAU,CAAC,IAAI,EAAE,CAAA;AAC3C5P,IAAAA,SAAS,CAACwI,KAAK,CAACoH,UAAU,CAAC,EAAEgD,YAAY,CAAC,CAAA;GAC3C,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACEvM,EAAAA,SAAS,CAACwM,UAAU,GAAG,UAAUjD,UAAU,EAAE;AAC3C,IAAA,IAAIpH,KAAK,CAACoH,UAAU,CAAC,EAAE;AACrB,MAAA,OAAO9P,QAAQ,CAAC0I,KAAK,CAACoH,UAAU,CAAC,CAAC,CAAA;AACpC,KAAA;GACD,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACEvJ,EAAAA,SAAS,CAACyM,WAAW,GAAG,UAAUlD,UAAU,EAAE;AAC5C,IAAA,IAAIpH,KAAK,CAACoH,UAAU,CAAC,EAAE;AACrBpH,MAAAA,KAAK,CAACoH,UAAU,CAAC,GAAG,EAAE,CAAA;AACxB,KAAA;GACD,CAAA;;AAED;AACF;AACA;AACA;EACEvJ,SAAS,CAAC0M,cAAc,GAAG,YAAY;IACrCvK,KAAK,GAAG,EAAE,CAAA;GACX,CAAA;AAED,EAAA,OAAOnC,SAAS,CAAA;AAClB,CAAA;AAEA,aAAeD,eAAe,EAAE;;;;"}