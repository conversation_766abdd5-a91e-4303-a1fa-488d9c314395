using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class BusinessFunctionFixture : IDisposable
{
    public List<BusinessFunction> BusinessFunctionPaginationList { get; set; }
    public List<BusinessFunction> BusinessFunctionList { get; set; }
    public BusinessFunction BusinessFunctionDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public BusinessFunctionFixture()
    {
        var fixture = new Fixture();

        BusinessFunctionList = fixture.Create<List<BusinessFunction>>();

        BusinessFunctionPaginationList = fixture.CreateMany<BusinessFunction>(20).ToList();

        BusinessFunctionPaginationList.ForEach(x => x.CompanyId = CompanyId);

        BusinessFunctionList.ForEach(x => x.CompanyId = CompanyId);

        BusinessFunctionDto = fixture.Create<BusinessFunction>();

        BusinessFunctionDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
