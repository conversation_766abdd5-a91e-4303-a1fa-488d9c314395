﻿//namespace ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Delete;

//public class
//    DeleteHeatMapStatusCommandHandler : IRequestHandler<DeleteHeatMapStatusCommand, DeleteHeatMapStatusResponse>
//{
//    private readonly IHeatMapStatusRepository _heatMapStatusRepository;

//    public DeleteHeatMapStatusCommandHandler(IHeatMapStatusRepository heatMapStatusRepository)
//    {
//        _heatMapStatusRepository = heatMapStatusRepository;
//    }

//    public async Task<DeleteHeatMapStatusResponse> Handle(DeleteHeatMapStatusCommand request,
//        CancellationToken cancellationToken)
//    {
//        var eventToDelete = await _heatMapStatusRepository.GetByReferenceIdAsync(request.Id);

//        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.HeatMapStatus),
//            new NotFoundException(nameof(Domain.Entities.HeatMapStatus), request.Id));

//        eventToDelete.IsActive = false;

//        await _heatMapStatusRepository.UpdateAsync(eventToDelete);

//        var response = new DeleteHeatMapStatusResponse
//        {
//            Message = Message.Delete(nameof(Domain.Entities.HeatMapStatus), eventToDelete.InfraObjectName),

//            IsActive = eventToDelete.IsActive
//        };

//        return response;
//    }
//}