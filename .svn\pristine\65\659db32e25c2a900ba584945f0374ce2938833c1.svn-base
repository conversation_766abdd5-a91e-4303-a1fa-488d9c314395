using ContinuityPatrol.Domain.ViewModels.UserModel;

namespace ContinuityPatrol.Domain.ViewModels.ApprovalMatrixUsersModel;

public class ApprovalMatrixUsersViewModel
{
    public string Id { get; set; }
	public string UserName { get; set; }
	public string Email { get; set; }
	public string MobileNumber { get; set; }
    public string UserGroupProperties { get; set; }
    public string BusinessServiceProperties { get; set; }
	public string UserType { get; set; }
	public string AcceptType { get; set; }
	public bool IsLink { get; set; }
   
    public List<UserNameVm>UserNames { get; set; }

}
