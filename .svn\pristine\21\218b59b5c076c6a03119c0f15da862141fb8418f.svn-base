﻿using ContinuityPatrol.Application.Features.LicenseInfo.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.LicenseInfo.Commands;

public class DeleteLicenseInfoTests : IClassFixture<LicenseInfoFixture>
{
    private readonly LicenseInfoFixture _licenseInfoFixture;

    private readonly Mock<ILicenseInfoRepository> _mockLicenseInfoRepository;

    private readonly DeleteLicenseInfoCommandHandler _handler;

    public DeleteLicenseInfoTests(LicenseInfoFixture licenseInfoFixture)
    {
        _licenseInfoFixture = licenseInfoFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockLicenseInfoRepository = new Mock<ILicenseInfoRepository>();

        _mockLicenseInfoRepository = LicenseInfoRepositoryMocks.DeleteLicenseInfoRepository(_licenseInfoFixture.LicenseInfos);

        _handler = new DeleteLicenseInfoCommandHandler(_mockLicenseInfoRepository.Object, mockPublisher.Object);

        _licenseInfoFixture.LicenseInfos[0].EntityId = "c2142470-caba-47fd-a090-eefc2d25011e";

    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_LicenseInfoDeleted()
    {
        var result = await _handler.Handle(new DeleteLicenseInfoCommand { EntityId = _licenseInfoFixture.LicenseInfos[0].ReferenceId }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_SuccessfulLicenseInfoResponse_When_LicenseInfoDeleted()
    {
        var result = await _handler.Handle(new DeleteLicenseInfoCommand { EntityId = _licenseInfoFixture.LicenseInfos[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteLicenseInfoResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidLicenseInfoId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteLicenseInfoCommand { EntityId = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Return_IsActive_False_When_DeleteReferenceIdAsync_LicenseInfo()
    {
        await _handler.Handle(new DeleteLicenseInfoCommand { EntityId = _licenseInfoFixture.LicenseInfos[0].ReferenceId }, CancellationToken.None);

        var licenseInfo = await _mockLicenseInfoRepository.Object.GetByEntityId(_licenseInfoFixture.LicenseInfos[0].ReferenceId);

        licenseInfo.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteLicenseInfoCommand { EntityId = _licenseInfoFixture.LicenseInfos[0].ReferenceId }, CancellationToken.None);

        _mockLicenseInfoRepository.Verify(x => x.GetByEntityId(It.IsAny<string>()), Times.Once);

        _mockLicenseInfoRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.LicenseInfo>()), Times.Once);
    }
}