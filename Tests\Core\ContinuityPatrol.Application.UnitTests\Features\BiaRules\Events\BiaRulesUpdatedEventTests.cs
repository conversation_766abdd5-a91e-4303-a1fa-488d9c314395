using ContinuityPatrol.Application.Features.BiaRules.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.BiaRules.Events;

public class BiaRulesUpdatedEventTests : IClassFixture<BiaRulesFixture>
{
    private readonly BiaRulesFixture _biaRulesFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly Mock<ILogger<BiaRulesUpdatedEventHandler>> _mockLogger;
    private readonly BiaRulesUpdatedEventHandler _handler;

    public BiaRulesUpdatedEventTests(BiaRulesFixture biaRulesFixture)
    {
        _biaRulesFixture = biaRulesFixture;
        _mockUserActivityRepository = BiaRulesRepositoryMocks.CreateUserActivityRepository(_biaRulesFixture.UserActivities);
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<BiaRulesUpdatedEventHandler>>();

        // Setup logged in user service
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns("TestUser");
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/biarules");
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        _handler = new BiaRulesUpdatedEventHandler(
            _mockLoggedInUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_When_BiaRulesUpdatedEventReceived()
    {
        // Arrange
        var updatedEvent = new BiaRulesUpdatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCorrectUserActivity_When_RTOBiaRulesUpdated()
    {
        // Arrange
        var updatedEvent = new BiaRulesUpdatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == _mockLoggedInUserService.Object.UserId &&
            ua.LoginName == _mockLoggedInUserService.Object.LoginName &&
            ua.RequestUrl == _mockLoggedInUserService.Object.RequestedUrl &&
            ua.HostAddress == _mockLoggedInUserService.Object.IpAddress &&
            ua.Action == $"{ActivityType.Update} {Modules.BiaRules}" &&
            ua.Entity == Modules.BiaRules.ToString() &&
            ua.ActivityType == ActivityType.Update.ToString() &&
            ua.ActivityDetails == "BiaRules 'RTO' updated successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCorrectUserActivity_When_RPOBiaRulesUpdated()
    {
        // Arrange
        var updatedEvent = new BiaRulesUpdatedEvent { Name = "RPO" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BiaRules 'RPO' updated successfully." &&
            ua.Action == $"{ActivityType.Update} {Modules.BiaRules}" &&
            ua.ActivityType == ActivityType.Update.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectUserInfo_When_BiaRulesUpdated()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var loginName = "UpdateTestUser";
        var requestUrl = "/api/v6/biarules/update";
        var ipAddress = "*************";

        _mockLoggedInUserService.Setup(x => x.UserId).Returns(userId);
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns(loginName);
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(requestUrl);
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns(ipAddress);

        var updatedEvent = new BiaRulesUpdatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == userId &&
            ua.LoginName == loginName &&
            ua.RequestUrl == requestUrl &&
            ua.HostAddress == ipAddress)), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_BiaRulesUpdated()
    {
        // Arrange
        var updatedEvent = new BiaRulesUpdatedEvent { Name = "Custom Updated Rule" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BiaRules 'Custom Updated Rule' updated successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectEntityAndModule_When_BiaRulesUpdated()
    {
        // Arrange
        var updatedEvent = new BiaRulesUpdatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.Entity == Modules.BiaRules.ToString() &&
            ua.Action.Contains(Modules.BiaRules.ToString()))), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityType_When_BiaRulesUpdated()
    {
        // Arrange
        var updatedEvent = new BiaRulesUpdatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityType == ActivityType.Update.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_LogInformation_When_BiaRulesUpdated()
    {
        // Arrange
        var updatedEvent = new BiaRulesUpdatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        //_mockLogger.Verify(
        //    x => x.Log(
        //        LogLevel.Information,
        //        It.IsAny<EventId>(),
        //        It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("BiaRules 'RTO' updated successfully.")),
        //        It.IsAny<Exception>(),
        //        It.IsAny<Func<It.IsAnyType, Exception, string>>()),
        //    Times.Once);
    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_BiaRulesUpdatedWithNullName()
    {
        // Arrange
        var updatedEvent = new BiaRulesUpdatedEvent { Name = null };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BiaRules '' updated successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleEmptyEventName_When_BiaRulesUpdatedWithEmptyName()
    {
        // Arrange
        var updatedEvent = new BiaRulesUpdatedEvent { Name = string.Empty };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BiaRules '' updated successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleCancellation_When_CancellationRequested()
    {
        // Arrange
        var updatedEvent = new BiaRulesUpdatedEvent { Name = "RTO" };
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        // Act & Assert
        await Should.ThrowAsync<OperationCanceledException>(async () =>
            await _handler.Handle(updatedEvent, cancellationTokenSource.Token));
    }

    [Fact]
    public async Task Handle_CallRepositoryOnce_When_EventHandled()
    {
        // Arrange
        var updatedEvent = new BiaRulesUpdatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        _mockUserActivityRepository.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_CreateEventWithComplexRuleName_When_BiaRulesUpdated()
    {
        // Arrange
        var complexRuleName = "Updated Complex RPO Rule with Special Characters & Numbers 456";
        var updatedEvent = new BiaRulesUpdatedEvent { Name = complexRuleName };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == $"BiaRules '{complexRuleName}' updated successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_VerifyEventType_When_BiaRulesUpdated()
    {
        // Arrange
        var updatedEvent = new BiaRulesUpdatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        updatedEvent.ShouldBeOfType<BiaRulesUpdatedEvent>();
        updatedEvent.ShouldBeAssignableTo<INotification>();
    }

    [Fact]
    public async Task Handle_DifferentiateFromCreateEvent_When_BiaRulesUpdated()
    {
        // Arrange
        var updatedEvent = new BiaRulesUpdatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityType == ActivityType.Update.ToString() &&
            ua.ActivityDetails.Contains("updated") &&
            !ua.ActivityDetails.Contains("created"))), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActionFormat_When_BiaRulesUpdated()
    {
        // Arrange
        var updatedEvent = new BiaRulesUpdatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.Action == $"{ActivityType.Update} {Modules.BiaRules}")), Times.Once);
    }
}
