using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.Create;
using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.Update;
using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.UpdateConditionActionId;
using ContinuityPatrol.Application.Features.CGExecutionReport.Queries.GetCgExecutionReportPaginatedList;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class CGExecutionReportFixture : IDisposable
{
    public CreateCGExecutionCommand CreateCGExecutionCommand { get; set; }
    public UpdateCGExecutionCommand UpdateCGExecutionCommand { get; set; }
    public ResiliencyReadinessWorkflowScheduleLogCommand ResiliencyReadinessWorkflowScheduleLogCommand { get; set; }
    public GetCgExecutionPaginatedListQuery GetCgExecutionPaginatedListQuery { get; set; }

    public CGExecutionReportFixture()
    {
        CreateCGExecutionCommand = AutoCGExecutionReportFixture.Create<CreateCGExecutionCommand>();
        UpdateCGExecutionCommand = AutoCGExecutionReportFixture.Create<UpdateCGExecutionCommand>();
        ResiliencyReadinessWorkflowScheduleLogCommand = AutoCGExecutionReportFixture.Create<ResiliencyReadinessWorkflowScheduleLogCommand>();
        GetCgExecutionPaginatedListQuery = AutoCGExecutionReportFixture.Create<GetCgExecutionPaginatedListQuery>();
    }

    public Fixture AutoCGExecutionReportFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customize<CreateCGExecutionCommand>(c => c.With(b => b.WorkflowOperationId, Guid.NewGuid().ToString));
            fixture.Customize<CreateCGExecutionCommand>(c => c.With(b => b.CGName, "Test-CG-01"));
            fixture.Customize<CreateCGExecutionCommand>(c => c.With(b => b.WorkflowName, "Test Workflow"));

            fixture.Customize<UpdateCGExecutionCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<UpdateCGExecutionCommand>(c => c.With(b => b.WorkflowOperationId, Guid.NewGuid().ToString));
            fixture.Customize<UpdateCGExecutionCommand>(c => c.With(b => b.CGName, "Updated-CG-01"));

            fixture.Customize<ResiliencyReadinessWorkflowScheduleLogCommand>(c => c.With(b => b.WorkflowOperationId, Guid.NewGuid().ToString));
            fixture.Customize<ResiliencyReadinessWorkflowScheduleLogCommand>(c => c.With(b => b.ConditionActionId, 1));

            fixture.Customize<GetCgExecutionPaginatedListQuery>(c => c.With(b => b.PageNumber, 1));
            fixture.Customize<GetCgExecutionPaginatedListQuery>(c => c.With(b => b.PageSize, 10));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
