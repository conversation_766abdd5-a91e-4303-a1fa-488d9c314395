﻿using ContinuityPatrol.Application.Features.Alert.Queries.GetLastAlertId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Tests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Alert.Queries;

public class GetLastAlertDetailQueryHandlerTests : IClassFixture<AlertFixture>
{
    private readonly AlertFixture _alertFixture;

    private readonly GetLastAlertDetailQueryHandler _handler;

    public GetLastAlertDetailQueryHandlerTests(AlertFixture alertFixture)
    {
        _alertFixture = alertFixture;

        var mockUserLoginRepository = UserLoginRepositoryMocks.GetUserLoginByUserIdRepository(_alertFixture.UserLogin);

        var mockLoggedInUserService = LoggedInUserServiceRepositoryMocks.GetLoggedInUserServiceRepository();

        var mockAlertRepository = AlertRepositoryMocks.GetLastAlertDetailRepository(_alertFixture.Alerts);
        
        _handler = new GetLastAlertDetailQueryHandler(mockAlertRepository.Object, mockUserLoginRepository.Object, mockLoggedInUserService.Object);

        _alertFixture.Alerts[0].ReferenceId = "f3cb9577-1c77-4329-ae30-0bd6200a088a";
        _alertFixture.UserLogin[0].UserId = _alertFixture.Alerts[0].ReferenceId;
        //_alertFixture.UserLogin[0].LastAlertId = _alertFixture.Alerts[0].ReferenceId;
    }

    [Fact]
    public async Task Handle_Return_AlertDetails_When_ValidAlertByUserLastAlertId()
    {
        var result = await _handler.Handle(new GetLastAlertDetailQuery (), CancellationToken.None);
        
        result.ShouldBeOfType<GetLastAlertDetailVm>();

        result.UserId.ShouldBe(_alertFixture.UserLogin[0].UserId);
        result.UserName.ShouldBe("Tester");
        result.AlertId.ShouldBe(_alertFixture.UserLogin[0].LastAlertId);
        result.AlertCount.ShouldBe(0);
    }

}