using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class TeamResourceRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly TeamResourceRepository _repository;
    private readonly TeamResourceFixture _fixture;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public TeamResourceRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = databaseFixture.DbContext;
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();

        _repository = new TeamResourceRepository(_dbContext, _mockLoggedInUserService.Object);
        _fixture = new TeamResourceFixture();

        // Setup default mock behavior
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
    }

    #region GetTeamMasterIdByTeamResource Tests

    [Fact]
    public async Task GetTeamMasterIdByTeamResource_ShouldReturnTeamResources_WhenTeamMasterIdExists()
    {
        // Arrange
        await ClearDatabase();

        var teamMasterId = Guid.NewGuid().ToString();
        var resource1 = _fixture.CreateTeamResource(
            teamMasterId: teamMasterId,
            teamMasterName: "Development Team",
            resourceName: "John Doe",
            resourceId: "USER_001",
            email: "<EMAIL>",
            phone: "************"
        );
        var resource2 = _fixture.CreateTeamResource(
            teamMasterId: teamMasterId,
            teamMasterName: "Development Team",
            resourceName: "Jane Smith",
            resourceId: "USER_002",
            email: "<EMAIL>",
            phone: "************"
        );
        var resource3 = _fixture.CreateTeamResource(
            teamMasterId: Guid.NewGuid().ToString(),
            teamMasterName: "QA Team",
            resourceName: "Bob Wilson",
            resourceId: "USER_003",
            email: "<EMAIL>",
            phone: "************"
        );

        await _repository.AddAsync(resource1);
        await _repository.AddAsync(resource2);
        await _repository.AddAsync(resource3);

        // Act
        var result = await _repository.GetTeamMasterIdByTeamResource(teamMasterId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.Equal(teamMasterId, r.TeamMasterId));
        Assert.Contains(result, r => r.ResourceName == "John Doe");
        Assert.Contains(result, r => r.ResourceName == "Jane Smith");
        Assert.DoesNotContain(result, r => r.ResourceName == "Bob Wilson");
    }

    [Fact]
    public async Task GetTeamMasterIdByTeamResource_ShouldReturnEmpty_WhenTeamMasterIdNotFound()
    {
        // Arrange
        await ClearDatabase();

        var resource = _fixture.CreateTeamResource(teamMasterId: Guid.NewGuid().ToString());
        await _repository.AddAsync(resource);

        // Act
        var result = await _repository.GetTeamMasterIdByTeamResource(Guid.NewGuid().ToString());

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetTeamMasterIdByTeamResource_ShouldOnlyReturnActiveResources()
    {
        // Arrange
        await ClearDatabase();

        var teamMasterId = Guid.NewGuid().ToString();
        var activeResource = _fixture.CreateTeamResource(
            teamMasterId: teamMasterId,
            resourceName: "Active Resource",
            isActive: true
        );
        var inactiveResource = _fixture.CreateTeamResource(
            teamMasterId: teamMasterId,
            resourceName: "Inactive Resource",
            isActive: false
        );
        await _dbContext.TeamResources.AddRangeAsync(activeResource, inactiveResource);
         _dbContext.SaveChanges();


        // Act
        var result = await _repository.GetTeamMasterIdByTeamResource(teamMasterId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Active Resource", result[0].ResourceName);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetTeamMasterIdByTeamResource_ShouldReturnAllProperties()
    {
        // Arrange
        await ClearDatabase();

        var teamMasterId = Guid.NewGuid().ToString();
        var resource = _fixture.CreateTeamResource(
            teamMasterId: teamMasterId,
            teamMasterName: "Complete Team",
            resourceName: "Complete Resource",
            resourceId: "COMPLETE_USER",
            email: "<EMAIL>",
            phone: "************"
        );

        await _repository.AddAsync(resource);

        // Act
        var result = await _repository.GetTeamMasterIdByTeamResource(teamMasterId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var resultResource = result[0];

        Assert.Equal(teamMasterId, resultResource.TeamMasterId);
        Assert.Equal("Complete Team", resultResource.TeamMasterName);
        Assert.Equal("Complete Resource", resultResource.ResourceName);
        Assert.Equal("COMPLETE_USER", resultResource.ResourceId);
        Assert.Equal("<EMAIL>", resultResource.Email);
        Assert.Equal("************", resultResource.Phone);
        Assert.NotNull(resultResource.ReferenceId);
        Assert.True(resultResource.Id > 0);
    }

    [Fact]
    public async Task GetTeamMasterIdByTeamResource_ShouldHandleNullValues()
    {
        // Arrange
        await ClearDatabase();

        var teamMasterId = Guid.NewGuid().ToString();
        var resource = _fixture.CreateTeamResource(
            teamMasterId: teamMasterId,
            teamMasterName: "Test Team",
            resourceName: "Test Resource",
            resourceId: null,
            email: null,
            phone: null
        );

        await _repository.AddAsync(resource);

        // Act
        var result = await _repository.GetTeamMasterIdByTeamResource(teamMasterId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var resultResource = result[0];

        Assert.Equal(teamMasterId, resultResource.TeamMasterId);
        Assert.Equal("Test Team", resultResource.TeamMasterName);
        Assert.Equal("Test Resource", resultResource.ResourceName);
        Assert.Null(resultResource.ResourceId);
        Assert.Null(resultResource.Email);
        Assert.Null(resultResource.Phone);
    }

//    #endregion

    #region GetTeamResourceNames Tests

    [Fact]
    public async Task GetTeamResourceNames_ShouldReturnProjectedTeamResourceNames_WhenIsParent()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var resource1 = _fixture.CreateTeamResource(teamMasterName: "Team Alpha");
        var resource2 = _fixture.CreateTeamResource(teamMasterName: "Team Beta");
        var inactiveResource = _fixture.CreateTeamResource(teamMasterName: "Inactive Team", isActive: false);

        await _dbContext.TeamResources.AddRangeAsync(resource1, resource2, inactiveResource);
        _dbContext.SaveChanges();


        // Act
        var result = await _repository.GetTeamResourceNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.True(r.IsActive));
        Assert.Contains(result, r => r.TeamMasterName == "Team Alpha");
        Assert.Contains(result, r => r.TeamMasterName == "Team Beta");
        Assert.DoesNotContain(result, r => r.TeamMasterName == "Inactive Team");

        // Verify only projected properties are set
        Assert.All(result, r => Assert.NotNull(r.ReferenceId));
        Assert.All(result, r => Assert.NotNull(r.TeamMasterName));
    }

    [Fact]
    public async Task GetTeamResourceNames_ShouldReturnFilteredByCompany_WhenNotParent()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");

        var companyResource = _fixture.CreateTeamResource(teamMasterName: "COMPANY_123");
        var otherCompanyResource = _fixture.CreateTeamResource(teamMasterName: "OTHER_COMPANY");

        await _repository.AddAsync(companyResource);
        await _repository.AddAsync(otherCompanyResource);

        // Act
        var result = await _repository.GetTeamResourceNames();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("COMPANY_123", result[0].TeamMasterName);
    }
    [Fact]
    public async Task GetTeamResourceNames_ShouldReturnEmptyList_WhenNoActiveResources()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var inactiveResource = _fixture.CreateTeamResource(isActive: false);
        await _dbContext.TeamResources.AddRangeAsync( inactiveResource);
        _dbContext.SaveChanges();


        // Act
        var result = await _repository.GetTeamResourceNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetTeamResourceNames_ShouldOrderByTeamMasterName()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var resourceZ = _fixture.CreateTeamResource(teamMasterName: "Z Team");
        var resourceA = _fixture.CreateTeamResource(teamMasterName: "A Team");
        var resourceM = _fixture.CreateTeamResource(teamMasterName: "M Team");

        await _repository.AddAsync(resourceZ);
        await _repository.AddAsync(resourceA);
        await _repository.AddAsync(resourceM);

        // Act
        var result = await _repository.GetTeamResourceNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.Equal("A Team", result[0].TeamMasterName);
        Assert.Equal("M Team", result[1].TeamMasterName);
        Assert.Equal("Z Team", result[2].TeamMasterName);
    }

    #endregion

    #region IsTeamResourceNameExist Tests

    [Fact]
    public async Task IsTeamResourceNameExist_ShouldReturnTrue_WhenResourceNameExists_WithoutId()
    {
        // Arrange
        await ClearDatabase();

        var resource = _fixture.CreateTeamResource(resourceName: "John Doe");
        await _repository.AddAsync(resource);

        // Act
        var result = await _repository.IsTeamResourceNameExist("John Doe", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTeamResourceNameExist_ShouldReturnFalse_WhenResourceNameDoesNotExist_WithoutId()
    {
        // Arrange
        await ClearDatabase();

        var resource = _fixture.CreateTeamResource(resourceName: "John Doe");
        await _repository.AddAsync(resource);

        // Act
        var result = await _repository.IsTeamResourceNameExist("Jane Smith", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTeamResourceNameExist_ShouldReturnTrue_WhenResourceNameExists_WithValidId()
    {
        // Arrange
        await ClearDatabase();

        var resource1 = _fixture.CreateTeamResource(resourceName: "John Doe");
        var resource2 = _fixture.CreateTeamResource(resourceName: "John Doe");
        await _repository.AddAsync(resource1);
        await _repository.AddAsync(resource2);

        // Act
        var result = await _repository.IsTeamResourceNameExist("John Doe", resource1.ReferenceId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTeamResourceNameExist_ShouldReturnFalse_WhenResourceNameDoesNotExist_WithValidId()
    {
        // Arrange
        await ClearDatabase();

        var resource = _fixture.CreateTeamResource(resourceName: "John Doe");
        await _repository.AddAsync(resource);

        // Act
        var result = await _repository.IsTeamResourceNameExist("Jane Smith", resource.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTeamResourceNameExist_ShouldHandleNullAndEmptyValues()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        var result1 = await _repository.IsTeamResourceNameExist(null, "invalid-guid");
        Assert.False(result1);

        var result2 = await _repository.IsTeamResourceNameExist("", "invalid-guid");
        Assert.False(result2);

        var result3 = await _repository.IsTeamResourceNameExist("John Doe", null);
        Assert.False(result3);

        var result4 = await _repository.IsTeamResourceNameExist("John Doe", "");
        Assert.False(result4);
    }

    #endregion

    #region IsTeamResourceNameUnique Tests

    [Fact]
    public async Task IsTeamResourceNameUnique_ShouldReturnTrue_WhenTeamMasterNameExists()
    {
        // Arrange
        await ClearDatabase();

        var resource = _fixture.CreateTeamResource(teamMasterName: "Development Team");
        await _repository.AddAsync(resource);

        // Act
        var result = await _repository.IsTeamResourceNameUnique("Development Team");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTeamResourceNameUnique_ShouldReturnFalse_WhenTeamMasterNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var resource = _fixture.CreateTeamResource(teamMasterName: "Development Team");
        await _repository.AddAsync(resource);

        // Act
        var result = await _repository.IsTeamResourceNameUnique("QA Team");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTeamResourceNameUnique_ShouldReturnTrue_WhenMultipleResourcesWithSameTeamMasterName()
    {
        // Arrange
        await ClearDatabase();

        var resource1 = _fixture.CreateTeamResource(teamMasterName: "Development Team");
        var resource2 = _fixture.CreateTeamResource(teamMasterName: "Development Team");
        await _repository.AddAsync(resource1);
        await _repository.AddAsync(resource2);

        // Act
        var result = await _repository.IsTeamResourceNameUnique("Development Team");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTeamResourceNameUnique_ShouldReturnFalse_WhenEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsTeamResourceNameUnique("Any Team");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTeamResourceNameUnique_ShouldHandleNullAndEmptyValues()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        var result1 = await _repository.IsTeamResourceNameUnique(null);
        Assert.False(result1);

        var result2 = await _repository.IsTeamResourceNameUnique("");
        Assert.False(result2);
    }

    [Fact]
    public async Task IsTeamResourceNameUnique_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();

        var resource = _fixture.CreateTeamResource(teamMasterName: "Development Team");
        await _repository.AddAsync(resource);

        // Act
        var result1 = await _repository.IsTeamResourceNameUnique("Development Team");
        var result2 = await _repository.IsTeamResourceNameUnique("development team");
        var result3 = await _repository.IsTeamResourceNameUnique("DEVELOPMENT TEAM");

        // Assert
        Assert.True(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    #endregion

    #region Integration Tests

    [Fact]
    public async Task TeamResourceRepository_ShouldHandleComplexScenarios()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var teamMasterId1 = Guid.NewGuid().ToString();
        var teamMasterId2 = Guid.NewGuid().ToString();

        var resources = new List<TeamResource>
        {
            _fixture.CreateTeamResource(
                teamMasterId: teamMasterId1,
                teamMasterName: "Development Team",
                resourceName: "Developer 1"
            ),
            _fixture.CreateTeamResource(
                teamMasterId: teamMasterId1,
                teamMasterName: "Development Team",
                resourceName: "Developer 2"
            ),
            _fixture.CreateTeamResource(
                teamMasterId: teamMasterId2,
                teamMasterName: "QA Team",
                resourceName: "QA Engineer 1"
            )
        };

    
            await _dbContext.TeamResources.AddRangeAsync(resources);
            _dbContext.SaveChanges();
        
            // Act & Assert
            var devTeamResources = await _repository.GetTeamMasterIdByTeamResource(teamMasterId1);
        Assert.Equal(2, devTeamResources.Count);
        Assert.All(devTeamResources, r => Assert.Equal("Development Team", r.TeamMasterName));

        var qaTeamResources = await _repository.GetTeamMasterIdByTeamResource(teamMasterId2);
        Assert.Single(qaTeamResources);
        Assert.Equal("QA Team", qaTeamResources[0].TeamMasterName);

        var teamResourceNames = await _repository.GetTeamResourceNames();
        Assert.Equal(3, teamResourceNames.Count); // Only unique team master names

        var isDevTeamUnique = await _repository.IsTeamResourceNameUnique("Development Team");
        Assert.True(isDevTeamUnique);

        var isNonExistentTeamUnique = await _repository.IsTeamResourceNameUnique("Non-existent Team");
        Assert.False(isNonExistentTeamUnique);

        var isResourceNameExist = await _repository.IsTeamResourceNameExist("Developer 1", "invalid-guid");
        Assert.True(isResourceNameExist);

        var isNonExistentResourceNameExist = await _repository.IsTeamResourceNameExist("Non-existent Resource", "invalid-guid");
        Assert.False(isNonExistentResourceNameExist);
    }

    #endregion

    #region Helper Methods

    private async Task ClearDatabase()
    {
        var existingEntities = _dbContext.TeamResources.ToList();
        _dbContext.TeamResources.RemoveRange(existingEntities);
        await _dbContext.SaveChangesAsync();
    }

    #endregion
    #endregion
}