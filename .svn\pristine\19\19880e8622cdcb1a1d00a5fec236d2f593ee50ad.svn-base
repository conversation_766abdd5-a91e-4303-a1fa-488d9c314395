﻿using ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MYSQLMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Manage;

public class MysqlMonitorLogsService : BaseClient, IMysqlMonitorLogsService
{
    public MysqlMonitorLogsService(IConfiguration config, IAppCache cache, ILogger<MysqlMonitorLogsService> logger)
        : base(config, cache, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateMYSQLMonitorLogCommand createMysqlMonitorLogCommand)
    {
        var request = new RestRequest("api/v6/mysqlmonitorlogs", Method.Post);

        request.AddJsonBody(createMysqlMonitorLogCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<List<MYSQLMonitorLogsListVm>> GetAllMYSQLMonitorLogs()
    {
        var request = new RestRequest("api/v6/mysqlmonitorlogs");

        return await GetFromCache<List<MYSQLMonitorLogsListVm>>(request, "GetAllMYSQLMonitorLogs");
    }

    public async Task<MYSQLMonitorLogsDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/mysqlmonitorlogs/{id}");

        return await Get<MYSQLMonitorLogsDetailVm>(request);
    }

    public async Task<List<MYSQLMonitorLogsDetailByTypeVm>> GetMYSQLMonitorLogsByType(string type)
    {
        var request = new RestRequest($"api/v6/mysqlmonitorlogs/type?type={type}");

        return await Get<List<MYSQLMonitorLogsDetailByTypeVm>>(request);
    }

    public async Task<PaginatedResult<MYSQLMonitorLogsListVm>> GetPaginatedMYSQLMonitorLogs(GetMYSQLMonitorLogsPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/mysqlmonitorlogs/paginated-list");

        return await Get<PaginatedResult<MYSQLMonitorLogsListVm>>(request);
    }
}