﻿using ContinuityPatrol.Application.Features.InfraObjectScheduler.Events.UpdateStatus;

namespace ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.UpdateStatus;

public class UpdateInfraObjectSchedulerStatusCommandHandler : IRequestHandler<UpdateInfraObjectSchedulerStatusCommand,
    UpdateInfraObjectSchedulerStatusResponse>
{
    private readonly IInfraObjectSchedulerRepository _infraObjectSchedulerRepository;
    private readonly IPublisher _publisher;

    public UpdateInfraObjectSchedulerStatusCommandHandler(
        IInfraObjectSchedulerRepository infraObjectSchedulerRepository, IPublisher publisher)
    {
        _infraObjectSchedulerRepository = infraObjectSchedulerRepository;
        _publisher = publisher;
    }

    public async Task<UpdateInfraObjectSchedulerStatusResponse> Handle(UpdateInfraObjectSchedulerStatusCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _infraObjectSchedulerRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(eventToUpdate, nameof(Domain.Entities.InfraObjectScheduler),
            new NotFoundException(nameof(Domain.Entities.InfraObjectScheduler), request.Id));

        eventToUpdate.Status = request.Status;

        await _infraObjectSchedulerRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateInfraObjectSchedulerStatusResponse
        {
            Message = Message.Update($"Manage Resiliency Readiness status {request.Status}",
                eventToUpdate.InfraObjectName),

            Id = eventToUpdate.ReferenceId
        };
        await _publisher.Publish(
            new InfraObjectSchedulerUpdatedStatusEvent
            {
                InfraObjectName = eventToUpdate.InfraObjectName, Status = eventToUpdate.Status, IsStatusChanged = true
            }, cancellationToken);

        return response;
    }
}