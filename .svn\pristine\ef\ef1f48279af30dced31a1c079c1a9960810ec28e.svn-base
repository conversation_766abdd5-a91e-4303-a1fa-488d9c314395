using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class ApprovalMatrixUsersRepository : BaseRepository<ApprovalMatrixUsers>, IApprovalMatrixUsersRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public ApprovalMatrixUsersRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<ApprovalMatrixUsers> GetByUserIdAsync(string userId)
    {
        return await Entities
            .FirstOrDefaultAsync(e => e.UserId == userId);
    }


    public async Task<bool> IsNameExist(string name, string id)
    {
        if (!id.IsValidGuid())
        {
            return await Entities.AnyAsync(e => e.UserName == name);
        }

        var matchingUsers = await Entities
            .Where(e => e.UserName == name)
            .ToListAsync();

        return matchingUsers.Unique(id);
    }
}
