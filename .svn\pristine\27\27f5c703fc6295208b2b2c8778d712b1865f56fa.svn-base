﻿using ContinuityPatrol.Application.Features.WorkflowPrediction.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowPrediction.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetActionId;
using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetNextPossibleId;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Orchestration;

public class WorkflowPredictionService : BaseClient, IWorkflowPredictionService
{
    public WorkflowPredictionService(IConfiguration config, IAppCache cache, ILogger<WorkflowPredictionService> logger)
        : base(config, cache, logger)
    {

    }

    public async Task<List<WorkflowPredictionListVm>> GetWorkflowPrediction()
    {
        var request = new RestRequest("api/v6/workflowprediction");

        return await GetFromCache<List<WorkflowPredictionListVm>>(request, "GetWorkflowPrediction");
    }

    public async Task<BaseResponse> CreateWorkflowPrediction(CreateWorkflowPredictionCommand createWorkflowPredictionCommand)
    {
        var request = new RestRequest("api/v6/workflowprediction", Method.Post);

        request.AddJsonBody(createWorkflowPredictionCommand);

        ClearCache("GetWorkflowOperationGroupList");

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateWorkflowPrediction(UpdateWorkflowPredictionCommand updateWorkflowPredictionCommand)
    {
        var request = new RestRequest("api/v6/workflowprediction", Method.Put);

        request.AddJsonBody(updateWorkflowPredictionCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteWorkflowPrediction(string id)
    {
        var request = new RestRequest($"api/v6/workflowprediction/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<WorkflowPredictionResult> GetWorkflowPredictionListByActionId(string actionId,string? previousActionId)
    {
        var request = new RestRequest($"api/v6/workflowprediction/actionid?actionId={actionId}&previousActionId={previousActionId}");

        return await Get<WorkflowPredictionResult>(request);
    }

    public async Task<List<WorkflowPredictionListByNextPossibleIdVm>> GetWorkflowPredictionListByNextPossibleId(string nextPossibleId)
    {
        var request = new RestRequest($"api/v6/workflowprediction/nextpossibleid?nextPossibleId={nextPossibleId}");

        return await Get<List<WorkflowPredictionListByNextPossibleIdVm>>(request);
    }
}