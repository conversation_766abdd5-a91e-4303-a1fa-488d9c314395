﻿//Update
async function getDetails() {
        let data = await fetchData();
        // Filter data for Report        
        const reportData = data?.filter(report => report?.sKey === "Report")                
            if (reportData?.length > 0) {
                let report = reportData[0];
                $('#txtReportSKey').val(report?.sKey);
                $('#txtReportSValue').val(report?.sValue);
                $('#txtReportLoginUser').val(report?.loginUserId);
                $('#txtReportId').val(report?.id);
                $('#btnSaveReport').text('Update');
            } else {
                $('#btnSaveReport').text('Save');
            }
       
            // Filter data for Password Age
            const passwordAgeData = data?.filter(age => age?.sKey === "Password Age");

            if (passwordAgeData?.length > 0) {
                let pwdAge = passwordAgeData[0];
                $('#txtPasswordAgeSKey').val(pwdAge?.sKey);
                $('#txtPasswordAgeSValue').val(pwdAge?.sValue);
                $('#txtPwdAgeLoginUser').val(pwdAge?.loginUserId);
                $('#txtPasswordAgeId').val(pwdAge?.id);
                $('#btnSavePwdAge').text('Update');
            } else {
                $('#btnSavePwdAge').text('Save');
            }
            // Filter data for Log
            const logData = data?.filter(log => log?.sKey === "Log");

    if (logData?.length > 0) {
        let log = logData[0];
        if (log?.sValue && typeof log?.sValue === "string" && log.sValue.trim() !== "") {
            let logValue = JSON?.parse(log?.sValue)
            $("#txtLog1SValue").val(logValue?.UI);
            $("#txtLog2SValue").val(logValue?.Monitor);
            $("#txtLog3SValue").val(logValue?.Workflow);
            $("#txtLog4SValue").val(logValue?.Loadbalancer);
            $("#txtLog5SValue").val(logValue?.ResiliencyReady);
            $('#txtLogSKey').val(log?.sKey)
            $('#txtLogLoginUser').val(log?.loginUserId)
            $('#txtLogId').val(log?.id)
            $('#btnSaveLog').text('Update');
        } else {
            $('#btnSaveLog').text('Save');
        }
    }
            // Filter data for Password Policy
            const passwordPolicy = data?.filter(policy => policy?.sKey === "Password Policy");
            
        if (passwordPolicy?.length > 0) {
        const pwdPlcy = passwordPolicy[0];
            if (pwdPlcy?.sValue && typeof pwdPlcy?.sValue === "string" && pwdPlcy?.sValue.trim() !== "") {
            let parseSvalue = JSON?.parse(pwdPlcy?.sValue);
            $('#txtPasswordPolicySKey').val(pwdPlcy?.sKey);
            $('#txtMinPwdPlcy').val(parseSvalue?.minSValue)
            $('#txtMinUpperPwdPlcy').val(parseSvalue?.minUpSValue)
            $('#txtMinNumPwdPlcy').val(parseSvalue?.minNumSValue)
            $('#txtMaxPwdPlcy').val(parseSvalue?.maxSValue)
            $('#txtMinLowerPwdPlcy').val(parseSvalue?.minLowSValue)
            $('#txtMinSpclPwdPlcy').val(parseSvalue?.minSpclSValue);
            $('#txtPwdAgeLoginUser').val(pwdPlcy?.loginUserId);
            $('#txtPasswordPolicyId').val(pwdPlcy?.id);
            $('#btnSavePwdPolicy').text('Update');

        } else {
            $('#btnSavePwdPolicy').text('Save');
        }
    }
            // Filter data for AD
            const AD = data?.filter(Ad => Ad?.sKey === "AD Configuration")
            if (AD?.length) {

                const ADValue = AD[0]
                var Adtype = ADValue?.sValue;
                $('input[name="SValue"][value="' + Adtype + '"]').prop("checked", true);
                $('#txtADUserSKey').val(ADValue?.sKey)
                $('#txtADUserLoginUser').val(ADValue?.loginUserId)
                $('#txtADUserId').val(ADValue?.id)

            }

            // Filter data for Load Balancer
            const Activity = data?.filter(active => active?.sKey === "Load Balancer")
            if (Activity?.length) {
                const ActivityValue = Activity[0]
                $('#chk-activity').prop('checked', ActivityValue?.sValue === 'true');
                $('#txtActivitySKey').val(ActivityValue?.sKey)
                $('#txtActivityLoginUser').val(ActivityValue?.loginUserId)
                $('#txtActivityId').val(ActivityValue?.id)
            }
        
}
getDetails()

//Clear ErrorMessages and Values
$('#v-pills-tab').on('click', function () {
    clearErrorMessages('#Report-error');
    clearErrorMessages('#Log5-error');
    clearErrorMessages('#Log4-error');
    clearErrorMessages('#Log3-error');
    clearErrorMessages('#Log2-error');
    clearErrorMessages('#Log1-error');
    clearErrorMessages('#PasswordAge-error');
    clearErrorMessages('#MinLower-error');
    clearErrorMessages('#MaxLength-error');
    clearErrorMessages('#MinNumber-error');
    clearErrorMessages('#MinUpper-error');
    clearErrorMessages('#MinLength-error');
    clearErrorMessages('#MinSpecialChar-error');
    $('#txtMinPwdPlcy').val('');
    $('#txtMinUpperPwdPlcy').val('');
    $('#txtMinNumPwdPlcy').val('');
    $('#txtMaxPwdPlcy').val('');
    $('#txtMinLowerPwdPlcy').val('');
    $('#txtMinSpclPwdPlcy').val('');
    $('#txtPwdAgeSValue').val('');
    $("#txtReportSValue").val('')
    $("#txtLog1SValue").val('');
    $("#txtLog2SValue").val('');
    $("#txtLog3SValue").val('');
    $("#txtLog4SValue").val('');
    $("#txtLog5SValue").val('');
    getDetails()
})
function clearErrorMessages(errorSelector) {
    $(errorSelector).text('').removeClass('field-validation-error');
}
