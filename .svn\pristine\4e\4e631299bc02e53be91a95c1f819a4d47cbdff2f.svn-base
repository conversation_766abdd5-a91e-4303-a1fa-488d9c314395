﻿using ContinuityPatrol.Application.Features.DRCalendar.Commands.Create;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Update;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDrCalendarDrillEvents;
using ContinuityPatrol.Domain.ViewModels.DRCalender;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class DrCalenderProfile : Profile
{
    public DrCalenderProfile()
    {
        CreateMap<DrCalenderActivity, CreateDrCalenderCommand>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<CreateDrCalenderCommand, DrCalenderActivityViewModel>().ReverseMap();
        CreateMap<UpdateDrCalenderCommand, DrCalenderActivityViewModel>().ReverseMap();

        CreateMap<UpdateDrCalenderCommand, DrCalenderActivity>()
            .ForMember(x => x.Id, y => y.Ignore());

        CreateMap<CreateDrCalenderCommand, DrCalenderActivityListVm>().ReverseMap();
        CreateMap<UpdateDrCalenderCommand, DrCalenderActivityListVm>().ReverseMap();
        CreateMap<DrCalenderActivity, DrCalenderActivityListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        
        CreateMap<DrCalenderActivity, GetDrCalendarDrillEventsVm>().ReverseMap()
            .ForMember(dest => dest.ScheduledStartDate,
                opt => opt.MapFrom(src => Convert.ToString(src.ScheduledStartDate)))
            .ForMember(dest => dest.ScheduledEndDate,
                opt => opt.MapFrom(src => Convert.ToString(src.ScheduledEndDate)));

        CreateMap<PaginatedResult<DrCalenderActivity>, PaginatedResult<DrCalenderActivityListVm>>()
           .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));

    }
}