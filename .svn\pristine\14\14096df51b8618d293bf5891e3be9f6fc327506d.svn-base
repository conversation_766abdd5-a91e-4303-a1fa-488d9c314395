using ContinuityPatrol.Domain.ViewModels.RtoModel;

namespace ContinuityPatrol.Application.Features.Rto.Queries.GetList;

public class GetRtoListQueryHandler : IRequestHandler<GetRtoListQuery, List<RtoListVm>>
{
    private readonly IMapper _mapper;
    private readonly IRtoRepository _rtoRepository;

    public GetRtoListQueryHandler(IMapper mapper, IRtoRepository rtoRepository)
    {
        _mapper = mapper;
        _rtoRepository = rtoRepository;
    }

    public async Task<List<RtoListVm>> Handle(GetRtoListQuery request, CancellationToken cancellationToken)
    {
        var rtos = await _rtoRepository.ListAllAsync();

        if (rtos.Count <= 0) return new List<RtoListVm>();

        return _mapper.Map<List<RtoListVm>>(rtos);
    }
}