﻿using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.GroupPolicy.Queries;

public class GetGroupPolicyDetailQueryHandlerTests : IClassFixture<GroupPolicyFixture>
{
    private readonly GroupPolicyFixture _groupPolicyFixture;

    private readonly Mock<IGroupPolicyRepository> _mockGroupPolicyRepository;

    private readonly GetGroupPolicyDetailQueryHandler _handler;

    public GetGroupPolicyDetailQueryHandlerTests(GroupPolicyFixture groupPolicyFixture)
    {
        _groupPolicyFixture = groupPolicyFixture;

        _mockGroupPolicyRepository = GroupPolicyRepositoryMocks.GetGroupPolicyRepository(_groupPolicyFixture.GroupPolicies);

        _handler = new GetGroupPolicyDetailQueryHandler(_mockGroupPolicyRepository.Object, _groupPolicyFixture.Mapper);

    }


    [Fact]
    public async Task Handle_Return_GroupPolicyDetails_When_ValidGroupPolicyId()
    {
        var result = await _handler.Handle(new GetGroupPolicyDetailQuery { Id = _groupPolicyFixture.GroupPolicies[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<GetGroupPolicyDetailVm>();

        result.Id.ShouldBe(_groupPolicyFixture.GroupPolicies[0].ReferenceId);
        result.GroupName.ShouldBe(_groupPolicyFixture.GroupPolicies[0].GroupName);
        result.Properties.ShouldBe(_groupPolicyFixture.GroupPolicies[0].Properties);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidGroupPolicyId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetGroupPolicyDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetGroupPolicyDetailQuery { Id = _groupPolicyFixture.GroupPolicies[0].ReferenceId }, CancellationToken.None);

        _mockGroupPolicyRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}