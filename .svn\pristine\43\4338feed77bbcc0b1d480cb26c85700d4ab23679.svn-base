﻿using System.ComponentModel.DataAnnotations.Schema;

namespace ContinuityPatrol.Application.Features.Setting.Commands.Update;

public class UpdateSettingCommand : IRequest<UpdateSettingResponse>
{
    public string Id { get; set; }

    public string SKey { get; set; }

    [Column(TypeName = "NCLOB")] public string SValue { get; set; }

    public string LoginUserId { get; set; }

    public override string ToString()
    {
        return $"Skey: {SKey}; Id: {Id};";
    }
}