﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetRunQuery;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;
using MySqlConnector;
using Npgsql;
using System.Data;
using DataSet = ContinuityPatrol.Domain.Entities.DataSet;

namespace ContinuityPatrol.Persistence.Repositories;

public class DataSetRepository : BaseRepository<DataSet>, IDataSetRepository
{
    private readonly IConfiguration _configuration;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ApplicationDbContext _dbContext;

    public DataSetRepository(ApplicationDbContext dbContext, IConfiguration config, ILoggedInUserService loggedInUserService) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _configuration = config;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<DataSet>> ListAllAsync()
    {
        var datasets =await  RequiredDataSetFields( base.ListAllAsync(dataset =>
            dataset.CompanyId.Equals(_loggedInUserService.CompanyId))).ToListAsync();

        return datasets;
    }
    public override async  Task<PaginatedResult<DataSet>> PaginatedListAllAsync(int pageNumber,int pageSize, Specification<DataSet> specification, string sortColumn, string sortOrder)
    {
        return await RequiredDataSetFields(_loggedInUserService.IsParent
            ?Entities.Specify(specification).DescOrderById()
            :Entities.Specify(specification).Where(dataset =>
             dataset.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()).ToSortedPaginatedListAsync(pageNumber,pageSize,sortColumn,sortOrder);
    }
    public override IQueryable<DataSet> PaginatedListAllAsync()
    {
       return base.ListAllAsync(dataset =>
            dataset.CompanyId.Equals(_loggedInUserService.CompanyId))
            .AsNoTracking().OrderByDescending(x => x.Id);        
    }
    public  Task<DataSet> GetDataSetById(string id)
    {
        var datasets = RequiredDataSetFields(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.ReferenceId.Equals(id))
            : base.FilterBy(x => x.ReferenceId.Equals(id) && _loggedInUserService.CompanyId.Equals(x.CompanyId)));
       
        return  Task.FromResult(datasets.FirstOrDefault());
    }

    public Task<List<DataSet>> GetDataSetNames()
    {
        var datasets = base
           .ListAllAsync(data => data.CompanyId.Equals(_loggedInUserService.CompanyId))
           .Select(x => new DataSet { ReferenceId = x.ReferenceId, DataSetName = x.DataSetName })
           .OrderBy(x => x.DataSetName);

        return  datasets.ToListAsync();       
    }

    public Task<bool> IsDataSetNameUnique(string name)
    {
        var matches = _dbContext.DataSets.Any(e => e.DataSetName.Equals(name));

        return Task.FromResult(matches);
    }

    public Task<DataSetRunQueryVm> GetTableJson(string query)
    {
        var dbProvider = _configuration.GetSection("ConnectionStrings").GetSection("DBProvider").Value;

        var connectionString = _configuration.GetConnectionString("Default");

        using var connection = CreateConnection(CryptographyHelper.Decrypt(dbProvider), CryptographyHelper.Decrypt(connectionString));

        connection.Open();

        var command = connection.CreateCommand();
        command.CommandText = query;

        var dt = new DataTable();

        using (var reader = command.ExecuteReader())
        {
            dt.Load(reader);
        }

        var dataSetTableVm = JsonConvert.SerializeObject(dt);
        return Task.FromResult(new DataSetRunQueryVm { TableValue = dataSetTableVm });
    }

    public Task<bool> IsDataSetNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.DataSets.Any(x => x.DataSetName.Equals(name)))
            : Task.FromResult(_dbContext.DataSets.Where(x => x.DataSetName.Equals(name)).ToList().Unique(id));
    }
    private IDbConnection CreateConnection(string dbProvider, string connectionString)
    {
        return dbProvider.ToLower() switch
        {
            "mssql" => new SqlConnection(connectionString),
            "mysql" => new MySqlConnection(connectionString),
            "oracle" => new OracleConnection(connectionString),
            "npgsql" => new NpgsqlConnection(connectionString),
            _ => throw new ArgumentException($"Unsupported database provider: {dbProvider}")
        };
    }

    public  async Task<List<DataSet>> GetDataSetByTableAccessId(string tableAccessId)
    {
        return await _dbContext.DataSets.Active().Where(x => x.TableAccessId.Equals(tableAccessId)).ToListAsync();
    }

    private IQueryable<DataSet> RequiredDataSetFields (IQueryable<DataSet> datasets)
    {
        return datasets.Select(x => new DataSet
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            DataSetName = x.DataSetName,
            CompanyId = x.CompanyId,
            Description = x.Description,
            TableAccessId = x.TableAccessId,
            StoredQuery = x.StoredQuery,
            PrimaryTableName = x.PrimaryTableName,
            PrimaryTablePKColumn = x.PrimaryTablePKColumn,
            QueryType = x.QueryType,
            StoredProcedureName = x.StoredProcedureName
        });
    }
}