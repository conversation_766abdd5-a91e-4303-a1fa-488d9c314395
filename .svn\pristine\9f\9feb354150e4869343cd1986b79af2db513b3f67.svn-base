﻿<!--Modal Create-->
<div class="modal-dialog modal-dialog-scrollabel modal-dialog-centered modal-sm Organization_modal">
    <div class="modal-content">
        <div class="modal-header">
            <h6 class="page_title">
                <i class="cp-user_role"></i> <span> User Role Configuration</span>
            </h6>
            <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="wizard-content">
                <form id="CreateForm">
                    <section>
                        <div class="form-group">
                            <div class="form-label">Role Name</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-name"></i></span>
                                <input type="text" id="cpRoleName" maxLength="100" class="form-control" value="" placeholder="Enter Role Name" autocomplete="off" />
                                
                                <span class="input-group-text p-0 ps-1" data-bs-toggle="collapse" id="multiCollapseExample1Color" href="#multiCollapseExample1" role="button" aria-expanded="false" aria-controls="multiCollapseExample1">
                                    <i class="cp-colour-picker" title="Select Color"></i>
                                </span>
                            </div>
                            <span id="nameError"></span></>
                        </div>
                    </section>
                    <div class="collapse multi-collapse pt-3" id="multiCollapseExample1">
                        <table class="table table-bordered">
                            <tbody id="colorTable">
                                <tr>
                                    <td>
                                        <input type="radio" name="color" id="red" value="red" />
                                        <label for="red"><span class="red dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="green" />
                                        <label for="green"><span class="green dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="yellow" />
                                        <label for="yellow"><span class="yellow dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="olive" />
                                        <label for="olive"><span class="olive dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="orange" />
                                        <label for="orange"><span class="orange dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="teal" />
                                        <label for="teal"><span class="teal dynamicColor"></span></label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="radio" name="color" id="blue" />
                                        <label for="blue"><span class="blue dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="violet" />
                                        <label for="violet"><span class="violet dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="purple" />
                                        <label for="purple"><span class="purple dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="pink" />
                                        <label for="pink"><span class="pink dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="darkblue" />
                                        <label for="darkblue"><span class="darkblue dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="pgreen" />
                                        <label for="pgreen"><span class="pgreen dynamicColor"></span></label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="radio" name="color" id="skyblue" />
                                        <label for="skyblue"><span class="skyblue dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="darkred" />
                                        <label for="darkred"><span class="darkred dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="darkpink" />
                                        <label for="darkpink"><span class="darkpink dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="green2" />
                                        <label for="green2"><span class="green2 dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="jupitar" />
                                        <label for="jupitar"><span class="jupitar dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="mustrad" />
                                        <label for="mustrad"><span class="mustrad dynamicColor"></span></label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="radio" name="color" id="melon" />
                                        <label for="melon"><span class="melon dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="darkgrey" />
                                        <label for="darkgrey"><span class="darkgrey dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="violet_lite" />
                                        <label for="violet_lite"><span class="violet_lite dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="black" />
                                        <label for="black"><span class="black dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="chacolate" />
                                        <label for="chacolate"><span class="chacolate dynamicColor"></span></label>
                                    </td>
                                    <td>
                                        <input type="radio" name="color" id="pasigreen" />
                                        <label for="pasigreen"><span class="pasigreen dynamicColor"></span></label>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
        </div>
        <div class="modal-footer d-flex justify-content-end">
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm" id="btnURSave">Save</button>
            </div>
        </div>
    </div>
</div>
@section Scripts
{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}
