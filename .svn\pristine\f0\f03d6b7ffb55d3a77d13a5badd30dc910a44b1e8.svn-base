using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowProfileFixture : IDisposable
{
    public List<WorkflowProfile> WorkflowProfilePaginationList { get; set; }
    public List<WorkflowProfile> WorkflowProfileList { get; set; }
    public WorkflowProfile WorkflowProfileDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowProfileFixture()
    {
        var fixture = new Fixture();

        WorkflowProfileList = fixture.Create<List<WorkflowProfile>>();

        WorkflowProfilePaginationList = fixture.CreateMany<WorkflowProfile>(20).ToList();

        WorkflowProfilePaginationList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowProfileList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowProfileDto = fixture.Create<WorkflowProfile>();

        WorkflowProfileDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
