using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class OracleMonitorStatusRepositoryTests : IClassFixture<OracleMonitorStatusFixture>
{
    private readonly OracleMonitorStatusFixture _oracleMonitorStatusFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly OracleMonitorStatusRepository _repository;

    public OracleMonitorStatusRepositoryTests(OracleMonitorStatusFixture oracleMonitorStatusFixture)
    {
        _oracleMonitorStatusFixture = oracleMonitorStatusFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new OracleMonitorStatusRepository(_dbContext);
    }

    private async Task ClearDatabase()
    {
        _dbContext.OracleMonitorStatuses.RemoveRange(_dbContext.OracleMonitorStatuses);
        await _dbContext.SaveChangesAsync();
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region AddAsync Tests

   

    #endregion

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnStatuses_WhenTypeExists()
    {
        // Arrange
        await ClearDatabase();
        var testType = "TestOracleType";
        var statuses = _oracleMonitorStatusFixture.CreateMultipleOracleMonitorStatusWithSameType(testType, 3);
        
        await _dbContext.OracleMonitorStatuses.AddRangeAsync(statuses);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, status => Assert.Equal(testType, status.Type));
        Assert.All(result, status => Assert.True(status.IsActive));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentType = "NonExistentType";

        // Act
        var result = await _repository.GetDetailByType(nonExistentType);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldOnlyReturnActiveStatuses()
    {
        // Arrange
        await ClearDatabase();
        var testType = "ActiveTestType";
        var activeStatus = _oracleMonitorStatusFixture.CreateOracleMonitorStatusWithProperties(type: testType, isActive: true);
        var inactiveStatus = _oracleMonitorStatusFixture.CreateOracleMonitorStatusWithProperties(type: testType, isActive: false);

        await _dbContext.OracleMonitorStatuses.AddRangeAsync(new[] { activeStatus, inactiveStatus });
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsActive);
        Assert.Equal(activeStatus.ReferenceId, result.First().ReferenceId);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleWhitespace()
    {
        // Arrange
        await ClearDatabase();
        var typeWithWhitespace = "  Oracle  ";
        var status = _oracleMonitorStatusFixture.CreateOracleMonitorStatusWithWhitespace();
        
        await _dbContext.OracleMonitorStatuses.AddAsync(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(typeWithWhitespace);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(typeWithWhitespace, result.First().Type);
    }

    #endregion

    #region GetOracleMonitorStatusByInfraObjectIdAsync Tests

    [Fact]
    public async Task GetOracleMonitorStatusByInfraObjectIdAsync_ShouldReturnStatus_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = Guid.NewGuid().ToString();
        var status = _oracleMonitorStatusFixture.CreateOracleMonitorStatusWithProperties(infraObjectId: infraObjectId);
        
        await _dbContext.OracleMonitorStatuses.AddAsync(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetOracleMonitorStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal(status.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetOracleMonitorStatusByInfraObjectIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentInfraObjectId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetOracleMonitorStatusByInfraObjectIdAsync(nonExistentInfraObjectId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetOracleMonitorStatusByInfraObjectIdAsync_ShouldThrow_WhenInfraObjectIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ContinuityPatrol.Shared.Core.Exceptions.InvalidArgumentException>(() =>
            _repository.GetOracleMonitorStatusByInfraObjectIdAsync("invalid-guid"));
    }

    [Fact]
    public async Task GetOracleMonitorStatusByInfraObjectIdAsync_ShouldThrow_WhenInfraObjectIdIsEmpty()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ContinuityPatrol.Shared.Core.Exceptions.InvalidArgumentException>(() =>
            _repository.GetOracleMonitorStatusByInfraObjectIdAsync(string.Empty));
    }



    [Fact]
    public async Task GetOracleMonitorStatusByInfraObjectIdAsync_ShouldReturnFirstMatch_WhenMultipleExist()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = Guid.NewGuid().ToString();
        var status1 = _oracleMonitorStatusFixture.CreateOracleMonitorStatusWithProperties(infraObjectId: infraObjectId);
        var status2 = _oracleMonitorStatusFixture.CreateOracleMonitorStatusWithProperties(infraObjectId: infraObjectId);
        
        await _dbContext.OracleMonitorStatuses.AddRangeAsync(new[] { status1, status2 });
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetOracleMonitorStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Should return one of the matching records
        Assert.True(result.ReferenceId == status1.ReferenceId || result.ReferenceId == status2.ReferenceId);
    }

    #endregion

 
}
