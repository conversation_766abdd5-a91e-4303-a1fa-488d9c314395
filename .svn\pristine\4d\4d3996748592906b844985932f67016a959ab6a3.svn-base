﻿namespace ContinuityPatrol.Domain.Views;

public class WorkflowCategoryView : BaseEntity
{
    #region Old Code Model
    //public string Name { get; set; }
    //public string NodeId { get; set; }
    //public string NodeName { get; set; }
    //public string ParentId { get; set; }
    //public string ParentTitle { get; set; }
    //public int Level { get; set; }
    //public string ActionName { get; set; }
    //public string ActionId { get; set; }
    //public string Color { get; set; }
    //public string Icon { get; set; }
#endregion
    public string Name { get; set; }

    [Column(TypeName = "NCLOB")]
    public string UpdatedJson { get; set; }

}