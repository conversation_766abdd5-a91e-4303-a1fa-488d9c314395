﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories
{
    public class OneViewRiskMitigationCyberSecurityViewRepository: IOneViewRiskMitigationCyberSecurityViewRepository
    {
        private readonly ApplicationDbContext _dbContext;
       
        public OneViewRiskMitigationCyberSecurityViewRepository(ApplicationDbContext dbContext,ILoggedInUserService loggedInUser)
        {
            _dbContext = dbContext;
            
        }

        public  async Task<List<OneViewRiskMitigationCyberSecurityView>> ListAllAsync()
        {
            return await _dbContext.OneViewRiskMitigationCyberSecurityViews.ToListAsync();
        }
    }
}
