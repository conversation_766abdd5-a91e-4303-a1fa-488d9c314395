﻿namespace ContinuityPatrol.Application.Features.Template.Queries.GetByReplicationTypeIdAndType;

public class GetByReplicationTypeIdAndTypeQueryHandler : IRequestHandler<GetByReplicationTypeIdAndTypeQuery,
    List<GetByReplicationTypeIdAndTypeVm>>
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly IMapper _mapper;
    private readonly IServerViewRepository _serverViewRepository;
    private readonly IServerRepository _serverRepository;
    private readonly ITemplateRepository _templateRepository;

    public GetByReplicationTypeIdAndTypeQueryHandler(IMapper mapper, ITemplateRepository templateRepository, IServerRepository serverRepository,
        IServerViewRepository serverViewRepository, IDatabaseRepository databaseRepository)
    {
        _mapper = mapper;
        _templateRepository = templateRepository;
        _serverViewRepository = serverViewRepository;
        _serverRepository = serverRepository;
        _databaseRepository = databaseRepository;
    }

    public async Task<List<GetByReplicationTypeIdAndTypeVm>> Handle(GetByReplicationTypeIdAndTypeQuery request,
        CancellationToken cancellationToken)
    {
        var template =
            await _templateRepository.GetTemplateByReplicationTypeIdAndActionType(request.ReplicationTypeId,
                request.ActionType);

        Guard.Against.NullOrDeactive(template, nameof(Domain.Entities.Template),
            new NotFoundException(nameof(Domain.Entities.Template), request.ReplicationTypeId));

        if (template.Type.IsNotNullOrWhiteSpace() && !request.TemplateType.Trim().ToLower().Equals("database"))
        {
            if (request.EntityType.Trim().ToLower().Equals("server"))
            {
                var server = (await _serverViewRepository.ListAllAsync())
                    .ToList()
                    .Where(x => x.RoleType.IsNotNullOrWhiteSpace() && !x.RoleType.Trim().ToLower().Equals("database"))
                    .ToList();

                return server.Count <= 0
                    ? new List<GetByReplicationTypeIdAndTypeVm>()
                    : _mapper.Map<List<GetByReplicationTypeIdAndTypeVm>>(server);
            }
        }
        else
        {
            if (request.EntityType.Trim().ToLower().Equals("server"))
            {
                var listServerId = new List<string>();

                var serverVm = new List<Domain.Entities.Server>();

                var database = _databaseRepository.GetDatabaseByType(template.SubTypeId);

                var servers = await _serverViewRepository.GetTypeName(request.Type);

                foreach (var databaseDto in servers.Select(server =>
                             database.Where(x => x.ServerId.Equals(server.ReferenceId))))
                    listServerId.AddRange(databaseDto.Select(x => x.ServerId));

                foreach (var id in listServerId)
                {
                    var server = await _serverRepository.GetByReferenceIdAsync(id);

                    serverVm.Add(server);
                }

                return serverVm.Count <= 0
                    ? new List<GetByReplicationTypeIdAndTypeVm>()
                    : _mapper.Map<List<GetByReplicationTypeIdAndTypeVm>>(serverVm);
            }

            //if(request.EntityType.ToLower().Trim().Equals("database"))
            //{
            //    var database =  _databaseRepository.GetDatabaseByType(template.SubTypeId);

            //    var databaseDto = request.Type.ToLower().Trim().Equals("prdb")
            //        ? database.Where(x=>x.Type.Equals("PRDB"))
            //        : request.Type.ToLower().Trim().Equals("drdb")
            //            ? database.Where(x=>x.Type.Equals("DRDB"))
            //            : database.Where(x=>x.Type.Equals("NearDRDB"));

            //    return databaseDto.ToList().Count <= 0
            //        ? new List<GetByReplicationTypeIdAndTypeVm>()
            //        : _mapper.Map<List<GetByReplicationTypeIdAndTypeVm>>(databaseDto);
            //}
        }

        return new List<GetByReplicationTypeIdAndTypeVm>();
    }
}