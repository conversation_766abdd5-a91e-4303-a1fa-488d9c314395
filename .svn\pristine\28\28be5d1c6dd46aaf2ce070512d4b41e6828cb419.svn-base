﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberAirGap.Events.Paginated;

public class CyberAirGapPaginatedEventHandler : INotificationHandler<CyberAirGapPaginatedEvent>
{
    private readonly ILogger<CyberAirGapPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberAirGapPaginatedEventHandler(ILogger<CyberAirGapPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(CyberAirGapPaginatedEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.View} {Modules.CyberAirGap}",
            Entity = Modules.CyberAirGap.ToString(),
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Cyber Resiliency Airgap viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Cyber Resiliency Airgap viewed");
    }
}