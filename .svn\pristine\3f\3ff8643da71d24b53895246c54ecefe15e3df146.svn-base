﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Shared.Tests.Mocks;

public class LicenseManagerModelRepositoryMocks
{
    public static Mock<ILicenseManagerRepository> GetLicenseManagerPoNumberRepository(List<LicenseManager> licenseManagers)
    {
        var licenseManagerRepository = new Mock<ILicenseManagerRepository>();

        licenseManagerRepository.Setup(repo => repo.GetLicenseDetailByPoNumber(It.IsAny<string>())).ReturnsAsync((string i) => licenseManagers.SingleOrDefault(x => x.PoNumber == i));

        return licenseManagerRepository;
    }
}