﻿const approverURL = {
    nameExist: "Manage/Approval/IsApprovalMatrixUsersNameExist",
    getPaginatedlist: "/Manage/Approval/GetPaginatedlist",
    getUserByID: "Manage/Approval/GetUserByID",
    create: "Manage/Approval/Create",
    getUsersList: "Manage/Approval/GetApprovalMatrixUsersList",
    getBusinessServiceList: "Manage/Approval/GetBusinessServiceList",
    delete: "Manage/Approval/Delete",
    getApproverList: "Manage/Approval/ApprovalMatrixUsersList", 
    CreateGroup: "Manage/Approval/CreateGroup", 
    UpdateGroup: "Manage/Approval/UpdateGroup"
}
let userGroupProperties = [];
let approverButtonDisable = false;
let newApproverData = "";
let addedApproverData = [];
let dataTable = "";
let userApproverDataTable = "";
let userGroupApproverDataTable = "";
let selectedValues = {
    Individual: [],
    Group: []
};
let groupId = "";
let noDataFount = `<div class="d-flex flex-column align-items-center justify-content-center NoDataContainer mt-5">
                       <img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">
                       <span>No matching records found</span>
                   </div>`;
const errorElements = ['#UserName-error', '#Email-error', '#Mobile-error', '#BusinessService-error', "#MobilePre-error"];
let approverdata = {
    ApprovalMatrixUsers: []
}
let countrycode;

let createPermission = $("#ConfigurationCreate").data("create-permission").toLowerCase();
let deletePermission = $("#ConfigurationDelete").data("delete-permission").toLowerCase();

if (createPermission == 'false') {
    $("#approverCreateButton").removeClass('#approverCreateButton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
}

$('#pills-tab button[data-bs-toggle="pill"]').on('shown.bs.tab', function (e) {
    const targetId = $(e.target).attr('id');

    if (targetId === 'pills-approver-tab') {
        $('#approverCreateButton,#userSearchFilter').show();
        $('#approverGroupCreateButton,#groupSearchFilter').hide();
  
    } else if (targetId === 'pills-approvergroup-tab') {
        $('#approverCreateButton,#userSearchFilter').hide();
        $('#approverGroupCreateButton,#groupSearchFilter').show();

    }
});

$(function () {

    approverPreventSpecialKeys('#userSearch');
    $("#pills-approver-tab").on("click", userLists);
    $("#pills-approvergroup-tab").on("click", userGroupLists);
    $("#pills-approver-tab").click();
    $('#userSearch').on('input', commonDebounce(function () {
        const inputValue = $('#userSearch').val().trim();
        selectedValues.Individual = [];

        ['#userNameListApprover', '#typeListApprover','deligatesToListApprover'].forEach(selector => {
            const checkbox = $(selector);
            if (checkbox.is(':checked')) {
                selectedValues.Individual.push(checkbox.val() + inputValue);
            }
        });    
        userApproverDataTable.ajax.reload(json => {
            const message = inputValue.length === 0
                ? (json?.data?.data?.length === 0 ? 'No Data Found' : '')
                : (json?.recordsFiltered === 0 ? 'No matching records found' : '');
            if (message) $('.dataTables_empty').text(message);
        });
    }, 500));
    $('#groupSearch').on('input', commonDebounce(function (e) {
        const inputValue = $('#groupSearch').val().trim();
        selectedValues.Group = [];

        ['#groupNameListApprover', '#approversListApprover'].forEach(selector => {
            const checkbox = $(selector);
            if (checkbox.is(':checked')) {
                selectedValues.Group.push(checkbox.val() + inputValue);
            }
        });

        userGroupApproverDataTable.ajax.reload(json => {
            const message = inputValue.length === 0
                ? (json?.data?.data?.length === 0 ? 'No Data Found' : '')
                : (json?.recordsFiltered === 0 ? 'No matching records found' : '');
            if (message) $('.dataTables_empty').text(message);
        });
      
    }, 500));



    $(document).on("click", ".ADAddUser", commonDebounce(async function () {
        let $this = $(this);
        let className = $this.attr("class");
        let dataID = $this.attr("data-id");
        let dataName = $this.attr("data-name").replaceAll(" ", "").toLowerCase();

        if (className.includes("btn-primary")) {
            $this.removeClass("btn-primary").addClass("btn-danger").text("Remove");
            await $.ajax({
                type: "GET",
                url: RootUrl + approverURL.getUserByID,
                data: { id: dataID },
                dataType: "json",
                success: function (response) {
                    if (response?.success) {
                        let result = response?.data;
                        let approval = {
                            "Id": "",
                            "UserId": result?.id,
                            "UserName": result?.loginName,
                            "Email": result?.userInfo.email || "NA",
                            "MobileNumber": result?.userInfo.mobile || "NA",
                            "UserGroupProperties": "NA",
                            "BusinessServiceProperties": "NA",
                            "UserType": "CP-User",
                            "AcceptType": "NA",
                            "IsLink": true,
                            "Type":"Individual"
                        }
                        approverdata.ApprovalMatrixUsers.push(approval);
                    }
                }
            });
        }
        else {
            $this.removeClass("btn-danger").addClass("btn-primary").text("Add");
            if (approverdata.ApprovalMatrixUsers.length) {
                approverdata.ApprovalMatrixUsers = approverdata.ApprovalMatrixUsers.filter(user => user.UserName.replaceAll(" ", "").toLowerCase() !== dataName);
            }
        }
    }));

    $("#profile-tab").on("click", function () {
        $("#AMUserType").val("Anonymous");
        $("#searchElement").addClass("d-none")
    });

    $("#SaveFunction").on('click', async function () {
        if ($("#AMUserType").val() === "CP-User") {
            if (approverdata.ApprovalMatrixUsers.length && !approverButtonDisable) {
                approverButtonDisable = true;
                await $.ajax({
                    type: 'Post',
                    url: RootUrl + approverURL.create,
                    dataType: "json",
                    data: approverdata,
                    headers: {
                        'RequestVerificationToken': await gettoken()
                    },
                    success: function (result) {
                        if (result?.success) {
                            let response = result?.data;
                            approverdata.ApprovalMatrixUsers = [];
                            notificationAlert("success", response?.message);
                            $("#adduserModal").modal("hide");
                            dataTableCreateAndUpdate($("#SaveFunction"), userApproverDataTable);
                        } else {
                            errorNotification(result);
                        }
                        approverButtonDisable = false;
                    },
                });
            }
        }
        else {
            let name = $("#UserName").val();
            let eMail = $("#mail").val();
            let businessService = $("#selectBusinessService").val();
            let countryCode = $('#mobilepre').val();
            let mobileNumber = $("#mobilenum").val();
            let mobileNumberWithCountryCode = countryCode + ' ' + mobileNumber;
            let isChecked = $("input[name='flexCheckDefault']").prop("checked") ? "true" : "false";
            let SiteSanitizeArray = ['UserName', 'mail', 'mobilenum', 'mobilepre,selectBusinessService'];

            const isName = await validateUserName(name, $('#userNameId').val(), $('#UserName-error'));
            const isEmail = await validateEmail(eMail, 'Enter email', $('#Email-error'));
            const isCountryCode = validateMobilePre(countryCode)
            const isMobileNumber = validateMobile(mobileNumber, 'Enter mobile number', $('#Mobile-error'));
            const isBusinessService = validateDropDown(businessService, 'Select operational service', $('#BusinessService-error'));

            sanitizeContainer(SiteSanitizeArray);

            $("#AMMobileNumber").val(mobileNumberWithCountryCode);
            $("#AMEMail").val(eMail);
            $("#AMUsername").val(name);
            $("#AMIsLink").val(isChecked);

            if (isName && isEmail && isMobileNumber && isBusinessService && isCountryCode && !approverButtonDisable) {
                approverButtonDisable = true;
                let anonymous = {
                    "Id": $("#userNameId").val(),
                    "MobileNumber": mobileNumberWithCountryCode,
                    "Email": eMail,
                    "UserName": name,
                    "IsLink": isChecked,
                    "UserType": $("#AMUserType").val(),
                    "AcceptType": $("#AMAccessType").val(),
                    "BusinessServiceProperties": businessService,
                    "UserGroupProperties": "NA",
                    "Type": "Individual"
                }
                approverdata.ApprovalMatrixUsers.push(anonymous);
                let createOrUpdate = $("#userNameId").val() ? "Update" : "Create";
                let createOrUpdateData = createOrUpdate === "Update" ? anonymous : approverdata;
                await $.ajax({
                    type: 'Post',
                    url: RootUrl + `Manage/Approval/${createOrUpdate}`,
                    dataType: "json",
                    data: createOrUpdateData,
                    headers: {
                        'RequestVerificationToken': await gettoken()
                    },
                    success: function (result) {
                        if (result?.success) {
                            let response = result?.data;
                            approverdata.ApprovalMatrixUsers = [];
                            notificationAlert("success", response?.message);
                            $("#adduserModal").modal("hide");
                            dataTableCreateAndUpdate($("#SaveFunction"), userApproverDataTable);
                        } else {
                            errorNotification(result);
                        }
                        approverButtonDisable = false;
                    },
                });
            }
        }
    });

    // Edit
  
    $('#ApproverTableUser').on('click', '.edit-button', function () {
        updateSiteType($(this).data('site'));
        $('#SaveFunction').text('Update');
        $('#adduserModal').modal('show');
        $('#home-tab-pane, #home-tab').removeClass('active').addClass('disabled');
    });

    //Delete


    $('#ApproverTableGroupUser').on('click', '.userGroupDelete', function () {
        const ApprovalName = $(this).data('usergroup');
        $("#deleteData").attr("title", ApprovalName);
        $('#deleteData').text(ApprovalName);
        $('#textDeleteId').val($(this).data('usergroupid'));
    });


    $("#confirmDeleteButton").on("click", async function () {
        const form = $('#approvalDelete')[0];
        const formData = new FormData(form);
   
        if (!approverButtonDisable) {
            approverButtonDisable = true;
            const response = await $.ajax({
                type: "DELETE",
                url: RootUrl + approverURL.delete,
                headers: {
                    'RequestVerificationToken': await gettoken()
                },
                data: formData,
                contentType: false,
                processData: false,
            });
            $("#DeleteModal").modal("hide");

            if (response?.success) {
                notificationAlert("success", response?.data?.message);
                setTimeout(() => {
                    dataTableDelete(dataTable);
                }, 2000)
            } else {
                errorNotification(response);
            }
            approverButtonDisable = false;
        }
    });

    $('#approverCreateButton').on('click', function () {
        $('#UnaddedTitle').addClass('disabled');
        $('#home-tab').removeClass('disabled');
      
     /*   $('#addserApprovalTag').hide();*/
       // $('.dataTable').removeClass('row-cols-2').addClass('row-cols-1');
        GetBusinessServiceList()
        clearInputFields('CreateForm', errorElements);
        $('#SaveFunction').text('Save');
        $("#AMUserType").val('CP-User');
        $("#home-tab").trigger("click");
        addCPUser();
        /*noDataApprover($('#addserApproval'));*/
    });

    $('#UserName').on('input', function () {
        siteId = $('#textSiteTypeId').val();
        validateUserName($(this).val(), $('#userNameId').val(), $('#UserName-error'));
    });

    $('#mobilenum').on('keypress keyup', function (event) {
        const value = $(this).val();

        if (value !== undefined) {
            $(this).val(value.replace(/  +/g, " "));
        }
        if (!/^[0-9]+$/.test(event.key)) {
            event?.preventDefault();
        }
        validateMobile(value);
    });

    $('#mobilepre').on('change', function (event) {
        validateMobilePre($(this).val());
    });

    $('#mail').on('keyup', async function () {
        let value = $(this).val();
        $(this).val(value.replace(/\s{2,}/g, ' '));
        await validateEmail(value, $('#textLoginId').val());
    });

    $('#selectBusinessService').on('change', function (event) {
        validateDropDown($(this).val(), "Select operational service", $('#BusinessService-error'));
    });

    $("#home-tab").on("click", function () {
        clearInputFields('CreateForm', errorElements);
        $("#AMUserType").val('CP-User');
        $("#searchElement").removeClass("d-none");
    });

    $("#approverSearchInput").on("keyup input", function (e) {
        let value = $(this).val();
        const $addserApproval = $("#addserApproval");

        if (!value) {
            $(".AddedList").hide()
            approvarlists(newApproverData);
            addedApproverData.forEach(function (data, index) {
                addNewApprover($addserApproval, data?.id, data?.loginName, data?.roleName, index);
            });
        }
        if (e.type === "keyup") {
            searchedApproverData();
        }
    });

    $("#searchApprovar").on("click", function () {
        searchedApproverData();
    });
    addCPUser();
});


//usergroup
$("#approverGroupCreateButton").on("click", async function () {
 
    approverdata = {
        ApprovalMatrixUsers: []
    }
    groupId = "";
    $('#SaveUserGroup').text('Save');
    $("#approverGroupName").val("");
    $("#approverList-error,#grpName-error").text("").removeClass('field-validation-error');
    $("#approverList").val("").empty();
    await getApproversData();
    $("#adduserGroupModal").modal("show");
});

$('#approverList').on('change',async function () {
    userGroupProperties = [];

    $('#approverList option:selected').each(function () {
        const Id = $(this).val();
        const Name = $(this).text();

        userGroupProperties.push({ Id, Name });
    });
    await validateApprovers($(this).val(), $('#approverList-error'));
});

$('#ApproverTableGroupUser').on('click', '.userGroupEdit', async function () {
    approverdata = {
        ApprovalMatrixUsers: []
    }
    let groupData = $(this).data('usergroup');
    $("#approverGroupName").val(groupData.userName);
    const parsedData = JSON.parse(groupData.userGroupProperties);
    const idArray = parsedData.userGroupProperties.map(u => u.Id);
    groupId = groupData.id;
    await getApproversData().then(() => {
        $('#approverList').val(idArray).trigger('change');
    })
    $('#SaveUserGroup').text('Update');
    $('#adduserGroupModal').modal('show');
});


$('#ApproverTableUser').on('click', '.delete-button', function () {
    const ApprovalName = $(this).data('site-name');
    $("#deleteData").attr("title", ApprovalName);
    $('#deleteData').text(ApprovalName);
    $('#textDeleteId').val($(this).data('site-id'));
});

$('#approverGroupName').on('input', function () {

    validateGroupName($(this).val(), groupId, $('#grpName-error'));
});
$('#mobilepre').on('change', function (event) {
    validateMobilePre($(this).val());
});

$("#SaveUserGroup").on('click', async function () {
    
    let GroupName = $("#approverGroupName").val(); 
    let Approvers = $('#approverList').val()
    const isName = await validateGroupName(GroupName, groupId, $('#grpName-error'));
         const isApprovers = await validateApprovers(Approvers, $('#approverList-error'));

  
        let url = "";
    if (isName && isApprovers) {
            let GroupData = {
                "Id": groupId,
                "UserName": GroupName,
                "AcceptType": "NA",
                "Email": "NA",
                "MobileNumber": "NA",
                "UserType": "NA",
                "BusinessServiceProperties": "NA",
                "UserGroupProperties": JSON.stringify({ userGroupProperties }),
                "Type": "Group"
        }
        if (groupId) {
            url = approverURL.UpdateGroup
            approverdata = GroupData;
        } else {
            url = approverURL.CreateGroup
            approverdata.ApprovalMatrixUsers.push(GroupData);
        }
       
            await $.ajax({
                type: 'Post',
                url: RootUrl + url,
                dataType: "json",
                data: approverdata,
                headers: {
                    'RequestVerificationToken': await gettoken()
                },
                success: function (result) {
                    if (result?.success) {
                        notificationAlert("success", result?.data?.message);
                        $("#adduserGroupModal").modal("hide");
                        dataTableCreateAndUpdate($("#SaveUserGroup"), userGroupApproverDataTable);
                    } else {
                        errorNotification(result);
                    }
                  
                },
            });
        }
    
});



$(document).on("click", ".ADAddUser", function () {
    const addserApproval = $("#addserApproval");
    const $this = $(this);
    const userId = $this.data("id");
    const userName = $this.data("name");
    const userRole = $this.data("role");
    const index = $this.data("index");
    let addedData = {
        id: userId,
        loginName: userName,
        roleName: userRole
    };

    if (!addedApproverData.some(item => item.id === addedData.id)) {
        addedApproverData.push(addedData);
    }
    $(".addedUsersList").remove();
    addNewApprover(addserApproval, userId, userName, userRole, index);
});

$(document).on("click", ".removeUser", function () {
    const $addserApproval = $("#addserApproval");
    const $addUserApproval = $("#addUserApproval");
    const $this = $(this);
    const userId = $this.data("id");
    const userName = $this.closest('div').find('.fw-semibold').text();
    const userRole = $this.closest('div').find('.badge').text();
    const index = $(`#user-${userId}`).data("index");
    addedApproverData = addedApproverData.filter(user => user.id !== userId);
    $(".usersList").remove();
    removeNewApprover($addserApproval, $addUserApproval, userId, userName, userRole, index);
});
