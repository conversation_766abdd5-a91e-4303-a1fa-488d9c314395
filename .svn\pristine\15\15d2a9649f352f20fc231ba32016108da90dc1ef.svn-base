﻿namespace ContinuityPatrol.Application.Features.InfraObjectInfo.Commands.Create;

public class
    CreateInfraObjectInfoCommandHandler : IRequestHandler<CreateInfraObjectInfoCommand, CreateInfraObjectInfoResponse>
{
    private readonly IInfraObjectInfoRepository _infraObjectInfoRepository;
    private readonly IMapper _mapper;

    public CreateInfraObjectInfoCommandHandler(IMapper mapper, IInfraObjectInfoRepository infraObjectInfoRepository)
    {
        _mapper = mapper;
        _infraObjectInfoRepository = infraObjectInfoRepository;
    }

    public async Task<CreateInfraObjectInfoResponse> Handle(CreateInfraObjectInfoCommand request,
        CancellationToken cancellationToken)
    {
        var infraObjectInfo = _mapper.Map<Domain.Entities.InfraObjectInfo>(request);

        infraObjectInfo = await _infraObjectInfoRepository.AddAsync(infraObjectInfo);

        var response = new CreateInfraObjectInfoResponse
        {
            Message = Message.Create(nameof(Domain.Entities.InfraObjectInfo), infraObjectInfo.InfraObjectName),

            Id = infraObjectInfo.ReferenceId
        };

        return response;
    }
}