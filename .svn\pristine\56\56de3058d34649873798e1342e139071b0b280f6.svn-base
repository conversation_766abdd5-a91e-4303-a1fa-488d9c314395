﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Workflow.Events.Delete;

public class WorkflowDeletedEventHandler : INotificationHandler<WorkflowDeletedEvent>
{
    private readonly ILogger<WorkflowDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowDeletedEventHandler(ILoggedInUserService userService, ILogger<WorkflowDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Workflow '{deletedEvent.WorkflowName}' Deleted successfully.");

        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress ?? "::1",
            Action = $"{ActivityType.Delete} {Modules.Workflow}",
            Entity = Modules.Workflow.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"Workflow '{deletedEvent.WorkflowName}' Deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);
    }
}