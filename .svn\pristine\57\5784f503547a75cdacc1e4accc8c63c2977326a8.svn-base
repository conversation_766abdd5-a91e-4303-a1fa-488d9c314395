using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.BackUp.Events.Update;

public class BackUpUpdatedEventHandler : INotificationHandler<BackUpUpdatedEvent>
{
    private readonly ILogger<BackUpUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public BackUpUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<BackUpUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(BackUpUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} BackUp",
            Entity = "BackUp",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"BackUp '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"BackUp '{updatedEvent.Name}' updated successfully.");
    }
}