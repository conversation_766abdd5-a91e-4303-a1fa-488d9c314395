﻿using ContinuityPatrol.Application.Features.Workflow.Events.Update;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Infrastructure.Hubs;

namespace ContinuityPatrol.Application.Features.Workflow.Commands.Update;

public class UpdateWorkflowCommandHandler : IRequestHandler<UpdateWorkflowCommand, UpdateWorkflowResponse>
{
    private readonly IHubContext<NotificationHub> _hubContext;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IVersionManager _versionManager;
    private readonly IWorkflowHistoryRepository _workflowHistoryRepository;
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IWorkflowExecutionTempRepository _workflowExecutionTempRepository;
    private readonly IWorkflowTempRepository _workflowTempRepository;


    public UpdateWorkflowCommandHandler(I<PERSON><PERSON>per mapper, IWorkflowRepository workflowRepository,
        IWorkflowHistoryRepository workflowHistoryRepository, ILoggedInUserService userService, IPublisher publisher,
        IVersionManager versionManager,
        IHubContext<NotificationHub> hubContext, IWorkflowExecutionTempRepository workflowExecutionTempRepository, IWorkflowTempRepository workflowTempRepository)
    {
        _mapper = mapper;
        _workflowRepository = workflowRepository;
        _workflowHistoryRepository = workflowHistoryRepository;
        _loggedInUserService = userService;
        _publisher = publisher;
        _versionManager = versionManager;
        _hubContext = hubContext;
        _workflowExecutionTempRepository = workflowExecutionTempRepository;
        _workflowTempRepository = workflowTempRepository;
    }

    public async Task<UpdateWorkflowResponse> Handle(UpdateWorkflowCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _workflowRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Workflow), request.Id);

        var version = await _versionManager.GetUpdateVersion(eventToUpdate.Version);

        _mapper.Map(request, eventToUpdate, typeof(UpdateWorkflowCommand), typeof(Domain.Entities.Workflow));

        eventToUpdate.Version = version;

        await _workflowTempRepository.AddAsync(new WorkflowTemp
        {
            WorkflowId = eventToUpdate.ReferenceId,
            Name = eventToUpdate.Name,
            Properties = eventToUpdate.Properties,
            Version = eventToUpdate.Version
        });



        await _workflowRepository.UpdateAsync(eventToUpdate);

        var eventToUpdateTemp = await _workflowExecutionTempRepository.GetByWorkflowIdAsync(request.Id);

        if (eventToUpdateTemp != null)
        {
            eventToUpdateTemp.Properties = eventToUpdate.Properties;

            await _workflowExecutionTempRepository.UpdateAsync(eventToUpdateTemp);
        }

        var workflowHistory = new Domain.Entities.WorkflowHistory
        {
            LoginName = _loggedInUserService.LoginName,
            CompanyId = _loggedInUserService.CompanyId,
            WorkflowId = eventToUpdate.ReferenceId,
            WorkflowName = eventToUpdate.Name,
            Version = eventToUpdate.Version,
            Properties = eventToUpdate.Properties,
            UpdaterId = _loggedInUserService.UserId,
            Comments = request.Comments
        };
        await _workflowHistoryRepository.AddAsync(workflowHistory);

        var response = new UpdateWorkflowResponse
        {
            Message = Message.Update(nameof(Domain.Entities.Workflow), eventToUpdate.Name),

            Id = eventToUpdate.ReferenceId,

            Version = eventToUpdate.Version
        };

        await _hubContext.Clients.All.SendAsync("notification", new
        {
            Message = $"Workflow '{eventToUpdate.Name} updated' by '{_loggedInUserService.LoginName}.'",
            UserName = _loggedInUserService.LoginName
        }, cancellationToken);

        // await _publisher.Publish(new WorkflowExecutionCreatedEvent { WorkflowId = request.Id }, cancellationToken);

        await _publisher.Publish(new WorkflowUpdatedEvent { WorkflowName = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}