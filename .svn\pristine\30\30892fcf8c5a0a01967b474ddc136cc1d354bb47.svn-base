﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class AlertInformationFilterSpecification : Specification<AlertInformation>
{
    public AlertInformationFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("type=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Type.Contains(stringItem.Replace("type=", "", StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("severity=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Severity.Contains(stringItem.Replace("severity=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("code=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Code.Contains(stringItem.Replace("code=", "", StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.Type.Contains(searchString) || p.Severity.Contains(searchString) || p.Code.Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.Type != null;
        }
    }
}