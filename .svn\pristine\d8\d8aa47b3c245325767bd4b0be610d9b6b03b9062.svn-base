﻿using ContinuityPatrol.Application.Features.Database.Events.UpdateBulkPassword;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Database.Commands.UpdateBulkPassword;

public class
    UpdateDatabasePasswordCommandHandler : IRequestHandler<UpdateDatabasePasswordCommand,
        UpdateDatabasePasswordResponse>
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly IPublisher _publisher;
    private readonly ILogger<UpdateDatabasePasswordCommandHandler> _logger;

    public UpdateDatabasePasswordCommandHandler(IDatabaseRepository databaseRepository, IPublisher publisher, ILogger<UpdateDatabasePasswordCommandHandler> logger)
    {
        _databaseRepository = databaseRepository;
        _publisher = publisher;
        _logger = logger;
    }

    public async Task<UpdateDatabasePasswordResponse> Handle(UpdateDatabasePasswordCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            var databaseIds = request.PasswordList
                .Where(x => x.Id.IsNotNullOrWhiteSpace())
                .Select(x => x.Id)
                .ToList();

            var eventToUpdate = await _databaseRepository.GetByDatabaseIdsAsync(databaseIds);

            if (eventToUpdate.Count > 0)
            {
                eventToUpdate = eventToUpdate.Select(database =>
                {
                    var jsonProperties = JsonConvert.DeserializeObject<dynamic>(database.Properties);

                    jsonProperties = GetJsonProperties.ReplacePasswords(jsonProperties, request.Password);

                    var modifiedJson = jsonProperties.ToString();
                    var sanitizedJson = modifiedJson.Replace("\r\n", "");

                    database.Properties = sanitizedJson;

                    database.ModeType = "Pending";

                    return database;
                }).ToList();

                await _databaseRepository.UpdateRangeAsync(eventToUpdate);


                foreach (var database in request.PasswordList)
                {
                    await _publisher.Publish(
                        new DatabaseBulkPasswordUpdatedEvent
                        {
                            DatabaseName = database.Name,
                            UserName = database.UserName,
                            ActivityType = ActivityType.Update.ToString()
                        }, cancellationToken);
                }
            }

            return new UpdateDatabasePasswordResponse
            {
                Message = $"{eventToUpdate.Count} Database password updated successfully."
            };
        }
        catch (Exception ex)
        {
            _logger.Exception("Error in updating database password", ex);

            throw new InvalidException($"Error in updating database password, {ex.GetMessage()}");
        }
    }
}