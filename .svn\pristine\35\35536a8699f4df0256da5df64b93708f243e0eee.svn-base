﻿namespace ContinuityPatrol.Application.Features.Replication.Commands.Update;

public class UpdateReplicationCommand : IRequest<UpdateReplicationResponse>
{
    public string Id { get; set; }

    public string Name { get; set; }

    public string Type { get; set; }

    public string TypeId { get; set; }

    public string SiteId { get; set; }
    public string Logo { get; set; }

    public string SiteName { get; set; }

    public string Properties { get; set; }

    public string LicenseId { get; set; }

    public string LicenseKey { get; set; }

    public string BusinessServiceId { get; set; }

    public string BusinessServiceName { get; set; }
    public string FormVersion { get; set; }

    public override string ToString()
    {
        return $"Name: {Name}; Id:{Id};";
    }
}