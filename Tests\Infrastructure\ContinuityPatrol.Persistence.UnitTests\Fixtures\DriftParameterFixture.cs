using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DriftParameterFixture : IDisposable
{
    public List<DriftParameter> DriftParameterPaginationList { get; set; }
    public List<DriftParameter> DriftParameterList { get; set; }
    public DriftParameter DriftParameterDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string DriftCategoryId = "DC_123";
    public const string DriftImpactTypeId = "DIT_123";

    public ApplicationDbContext DbContext { get; private set; }

    public DriftParameterFixture()
    {
        var fixture = new Fixture();

        DriftParameterList = fixture.Create<List<DriftParameter>>();

        DriftParameterPaginationList = fixture.CreateMany<DriftParameter>(20).ToList();

        DriftParameterPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftParameterPaginationList.ForEach(x => x.IsActive = true);
        DriftParameterPaginationList.ForEach(x => x.DriftCategoryId = DriftCategoryId);
        DriftParameterPaginationList.ForEach(x => x.DriftImpactTypeId = DriftImpactTypeId);

        DriftParameterList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftParameterList.ForEach(x => x.IsActive = true);
        DriftParameterList.ForEach(x => x.DriftCategoryId = DriftCategoryId);
        DriftParameterList.ForEach(x => x.DriftImpactTypeId = DriftImpactTypeId);

        DriftParameterDto = fixture.Create<DriftParameter>();
        DriftParameterDto.ReferenceId = Guid.NewGuid().ToString();
        DriftParameterDto.IsActive = true;
        DriftParameterDto.DriftCategoryId = DriftCategoryId;
        DriftParameterDto.DriftImpactTypeId = DriftImpactTypeId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
