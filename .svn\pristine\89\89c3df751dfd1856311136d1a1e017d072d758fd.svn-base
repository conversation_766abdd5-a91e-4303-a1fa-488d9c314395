using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.CredentialProfile.Commands.Create;
using ContinuityPatrol.Application.Features.CredentialProfile.Commands.Delete;
using ContinuityPatrol.Application.Features.CredentialProfile.Commands.Update;
using ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetList;
using ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetNames;
using ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.CredentialProfileModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class CredentialProfilesControllerTests : IClassFixture<CredentialProfilesFixture>
{
    private readonly CredentialProfilesFixture _credentialProfilesFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly CredentialProfilesController _controller;

    public CredentialProfilesControllerTests(CredentialProfilesFixture credentialProfilesFixture)
    {
        _credentialProfilesFixture = credentialProfilesFixture;

        var testBuilder = new ControllerTestBuilder<CredentialProfilesController>();
        _controller = testBuilder.CreateController(
            _ => new CredentialProfilesController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetCredentialProfiles_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCredentialProfileListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_credentialProfilesFixture.CredentialProfileListVm);

        // Act
        var result = await _controller.GetCredentialProfiles();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var credentialProfiles = Assert.IsAssignableFrom<List<CredentialProfileListVm>>(okResult.Value);
        Assert.Equal(3, credentialProfiles.Count);
    }

    [Fact]
    public async Task GetCredentialProfileById_ReturnsExpectedDetail()
    {
        // Arrange
        var credentialProfileId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCredentialProfileDetailQuery>(q => q.Id == credentialProfileId), default))
            .ReturnsAsync(_credentialProfilesFixture.CredentialProfileDetailVm);

        // Act
        var result = await _controller.GetCredentialProfileById(credentialProfileId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var credentialProfile = Assert.IsType<CredentialProfileDetailVm>(okResult.Value);
        Assert.NotNull(credentialProfile);
    }

    [Fact]
    public async Task CreateCredentialProfile_Returns201Created()
    {
        // Arrange
        var command = _credentialProfilesFixture.CreateCredentialProfileCommand;
        var expectedMessage = $"CredentialProfile '{command.Name}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCredentialProfileResponse
            {
                Message = expectedMessage,
                CredentialProfileId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCredentialProfile(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCredentialProfileResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCredentialProfile_ReturnsOk()
    {
        // Arrange
        var command = _credentialProfilesFixture.UpdateCredentialProfileCommand;
        var expectedMessage = $"CredentialProfile '{command.Name}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCredentialProfileResponse
            {
                Message = expectedMessage,
                CredentialProfileId = command.Id
            });

        // Act
        var result = await _controller.UpdateCredentialProfile(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCredentialProfileResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteCredentialProfile_ReturnsOk()
    {
        // Arrange
        var credentialProfileId = Guid.NewGuid().ToString();
        var expectedMessage = "CredentialProfile 'Test Profile' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCredentialProfileCommand>(c => c.Id == credentialProfileId), default))
            .ReturnsAsync(new DeleteCredentialProfileResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteCredentialProfile(credentialProfileId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteCredentialProfileResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task GetCredentialProfileById_HandlesInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetCredentialProfileById("invalid-guid"));
    }

    [Fact]
    public async Task CreateCredentialProfile_ValidatesName()
    {
        // Arrange
        var command = new CreateCredentialProfileCommand
        {
            CompanyId = Guid.NewGuid().ToString(),
            Name = "", // Empty name should cause validation error
            CredentialType = "Database",
            Properties = "{\"username\":\"test\"}"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("Name is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateCredentialProfile(command));
    }

    [Fact]
    public async Task UpdateCredentialProfile_ValidatesCredentialProfileExists()
    {
        // Arrange
        var command = new UpdateCredentialProfileCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Updated Profile",
            CredentialType = "SSH",
            Properties = "{\"username\":\"updated\"}"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("CredentialProfile not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateCredentialProfile(command));
    }

    [Fact]
    public async Task CreateCredentialProfile_HandlesComplexCredentialProperties()
    {
        // Arrange
        var command = new CreateCredentialProfileCommand
        {
            CompanyId = Guid.NewGuid().ToString(),
            Name = "Enterprise Multi-Factor Authentication Profile",
            CredentialType = "MFA",
            Properties = "{\"username\":\"enterprise_admin\",\"password\":\"encrypted_complex_password\",\"mfaEnabled\":true,\"totpSecret\":\"encrypted_totp_secret\",\"backupCodes\":[\"code1\",\"code2\",\"code3\"],\"certificatePath\":\"/certs/enterprise.pem\",\"keyPath\":\"/keys/enterprise.key\",\"sessionTimeout\":3600,\"maxRetries\":3}"
        };

        var expectedMessage = $"CredentialProfile '{command.Name}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCredentialProfileResponse
            {
                Message = expectedMessage,
                CredentialProfileId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCredentialProfile(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCredentialProfileResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCredentialProfile_HandlesCredentialTypeChange()
    {
        // Arrange
        var command = new UpdateCredentialProfileCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Updated Enterprise API Key Profile",
            CredentialType = "API_KEY",
            Properties = "{\"apiKey\":\"encrypted_api_key\",\"apiSecret\":\"encrypted_api_secret\",\"endpoint\":\"https://api.enterprise.com\",\"version\":\"v2\",\"rateLimit\":1000,\"timeout\":30000}"
        };

        var expectedMessage = $"CredentialProfile '{command.Name}' has been updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCredentialProfileResponse
            {
                Message = expectedMessage,
                CredentialProfileId = command.Id
            });

        // Act
        var result = await _controller.UpdateCredentialProfile(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCredentialProfileResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.CredentialProfileId);
    }

    [Fact]
    public async Task CreateCredentialProfile_ValidatesPropertiesFormat()
    {
        // Arrange
        var command = new CreateCredentialProfileCommand
        {
            CompanyId = Guid.NewGuid().ToString(),
            Name = "Test Profile",
            CredentialType = "Database",
            Properties = "invalid-json-format" // Invalid JSON should cause validation error
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("Properties must be valid JSON format"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateCredentialProfile(command));
    }

    [Fact]
    public async Task CreateCredentialProfile_ValidatesCredentialType()
    {
        // Arrange
        var command = new CreateCredentialProfileCommand
        {
            CompanyId = Guid.NewGuid().ToString(),
            Name = "Test Profile",
            CredentialType = "", // Empty credential type should cause validation error
            Properties = "{\"username\":\"test\"}"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("CredentialType is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateCredentialProfile(command));
    }

    [Fact]
    public async Task GetCredentialProfileByType_ReturnsExpectedList()
    {
        // Arrange
        var credentialType = "Database";
        var expectedProfiles = new List<CredentialProfileTypeVm>
        {
            new CredentialProfileTypeVm { Id = Guid.NewGuid().ToString(), Name = "SQL Server Profile", CredentialType = "Database" },
            new CredentialProfileTypeVm { Id = Guid.NewGuid().ToString(), Name = "Oracle Profile", CredentialType = "Database" }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCredentialProfileTypeQuery>(q => q.CredentialType == credentialType), default))
            .ReturnsAsync(expectedProfiles);

        // Act
        var result = await _controller.GetCredentialProfileByType(credentialType);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var profiles = Assert.IsType<List<CredentialProfileTypeVm>>(okResult.Value);
        Assert.Equal(2, profiles.Count);
        Assert.All(profiles, p => Assert.Equal("Database", p.CredentialType));
    }

    [Fact]
    public async Task GetCredentialProfileNames_ReturnsExpectedList()
    {
        // Arrange
        var expectedNames = new List<CredentialProfileNameVm>
        {
            new CredentialProfileNameVm { Id = Guid.NewGuid().ToString(), Name = "Production DB Profile" },
            new CredentialProfileNameVm { Id = Guid.NewGuid().ToString(), Name = "Staging API Profile" }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCredentialProfileNameQuery>(), default))
            .ReturnsAsync(expectedNames);

        // Act
        var result = await _controller.GetCredentialProfileNames();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var names = Assert.IsType<List<CredentialProfileNameVm>>(okResult.Value);
        Assert.Equal(2, names.Count);
    }

    [Fact]
    public async Task GetPaginatedCredentialProfiles_ReturnsExpectedResults()
    {
        // Arrange
        var query = new GetCredentialProfilePaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Database"
        };

        var expectedData = _credentialProfilesFixture.CredentialProfileListVm.Take(2).ToList();
        var expectedResults = PaginatedResult<CredentialProfileListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCredentialProfilePaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCredentialProfiles(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CredentialProfileListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task IsCredentialProfileNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var profileName = "Production Database Profile";
        var profileId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCredentialProfileNameUniqueQuery>(q =>
                q.CredentialProfileName == profileName && q.CredentialProfileId == profileId), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsCredentialProfileNameExist(profileName, profileId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.True(exists);
    }

    [Fact]
    public async Task IsCredentialProfileNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var profileName = "Unique Profile Name";
        var profileId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCredentialProfileNameUniqueQuery>(q =>
                q.CredentialProfileName == profileName && q.CredentialProfileId == profileId), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsCredentialProfileNameExist(profileName, profileId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.False(exists);
    }
}
