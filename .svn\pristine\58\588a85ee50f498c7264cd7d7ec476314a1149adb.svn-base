﻿namespace ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateDefault;

public class UpdateLoadBalancerDefaultCommandHandler : IRequestHandler<UpdateLoadBalancerDefaultCommand, UpdateLoadBalancerDefaultResponse>
{
    private readonly ILoadBalancerRepository _loadBalancerRepository;

    public UpdateLoadBalancerDefaultCommandHandler(ILoadBalancerRepository loadBalancerRepository)
    {
        _loadBalancerRepository = loadBalancerRepository;
    }

    public async Task<UpdateLoadBalancerDefaultResponse> Handle(UpdateLoadBalancerDefaultCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _loadBalancerRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.LoadBalancer), request.Name);

        eventToUpdate.IsDefault = request.IsDefault;

        await _loadBalancerRepository.UpdateAsync(eventToUpdate);

        var loadBalancers = await _loadBalancerRepository.GetLoadBalancerByType(eventToUpdate.ReferenceId, eventToUpdate.Type);

        loadBalancers.ForEach(x => x.IsDefault = false);

        await _loadBalancerRepository.UpdateRangeAsync(loadBalancers);

        var response = new UpdateLoadBalancerDefaultResponse
        {
            Message = $"{eventToUpdate.TypeCategory} {eventToUpdate.Name} has been set as default successfully.",
            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}