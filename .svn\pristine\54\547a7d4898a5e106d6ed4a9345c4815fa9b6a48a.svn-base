using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class RpForVmCGMonitorLogsRepositoryTests : IClassFixture<RpForVmCGMonitorLogsFixture>
{
    private readonly RpForVmCGMonitorLogsFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly RpForVmCGMonitorLogsRepository _repository;

    public RpForVmCGMonitorLogsRepositoryTests(RpForVmCGMonitorLogsFixture fixture)
    {
        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new RpForVmCGMonitorLogsRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddRpForVmCGMonitorLogs_WhenValidEntity()
    {
        // Arrange
        var monitorLog = _fixture.RpForVmCGMonitorLogsDto;
        monitorLog.InfraObjectId = "INFRA_TEST_001";
        monitorLog.ConsistencyGroupId = "CG_TEST_001";
        monitorLog.ConsistencyGroupName = "Test Consistency Group";
        monitorLog.State = "Active";
        monitorLog.TransferStatus = "Completed";
        monitorLog.ActivityType = "Replication";
        monitorLog.ActivityStatus = "Success";
        monitorLog.AvailabilityStatus = "Available";
        monitorLog.ProtectedSize = "500GB";
        monitorLog.DataLag = "2 minutes";

        // Act
        var result = await _repository.AddAsync(monitorLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorLog.InfraObjectId, result.InfraObjectId);
        Assert.Equal(monitorLog.ConsistencyGroupId, result.ConsistencyGroupId);
        Assert.Equal(monitorLog.ConsistencyGroupName, result.ConsistencyGroupName);
        Assert.Equal(monitorLog.State, result.State);
        Assert.Equal(monitorLog.TransferStatus, result.TransferStatus);
        Assert.Equal(monitorLog.ActivityType, result.ActivityType);
        Assert.Equal(monitorLog.ActivityStatus, result.ActivityStatus);
        Assert.Equal(monitorLog.AvailabilityStatus, result.AvailabilityStatus);
        Assert.Equal(monitorLog.ProtectedSize, result.ProtectedSize);
        Assert.Equal(monitorLog.DataLag, result.DataLag);
        Assert.Single(_dbContext.RpForVmCGMonitorLogs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var monitorLog = _fixture.RpForVmCGMonitorLogsDto;
        _dbContext.RpForVmCGMonitorLogs.Add(monitorLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(monitorLog.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorLog.Id, result.Id);
        Assert.Equal(monitorLog.InfraObjectId, result.InfraObjectId);
        Assert.Equal(monitorLog.ConsistencyGroupId, result.ConsistencyGroupId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var monitorLog = _fixture.RpForVmCGMonitorLogsDto;
        _dbContext.RpForVmCGMonitorLogs.Add(monitorLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(monitorLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorLog.ReferenceId, result.ReferenceId);
        Assert.Equal(monitorLog.InfraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var monitorLogs = _fixture.RpForVmCGMonitorLogsList;
        _dbContext.RpForVmCGMonitorLogs.AddRange(monitorLogs);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorLogs.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntitiesExist()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity_WhenExists()
    {
        // Arrange
        var monitorLog = _fixture.RpForVmCGMonitorLogsDto;
        _dbContext.RpForVmCGMonitorLogs.Add(monitorLog);
        await _dbContext.SaveChangesAsync();

        var updatedState = "Updated State";
        var updatedTransferStatus = "Updated Transfer Status";
        monitorLog.State = updatedState;
        monitorLog.TransferStatus = updatedTransferStatus;

        // Act
        var result = await _repository.UpdateAsync(monitorLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(updatedState, result.State);
        Assert.Equal(updatedTransferStatus, result.TransferStatus);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity_WhenExists()
    {
        // Arrange
        var monitorLog = _fixture.RpForVmCGMonitorLogsDto;
        _dbContext.RpForVmCGMonitorLogs.Add(monitorLog);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(monitorLog);

        // Assert
        var deletedEntity = await _repository.GetByIdAsync(monitorLog.Id);
        Assert.Null(deletedEntity);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultipleEntities()
    {
        // Arrange
        var monitorLogs = new List<RpForVmCGMonitorLogs>
        {
            _fixture.CreateRpForVmCGMonitorLogsWithInfraObjectId("INFRA_001"),
            _fixture.CreateRpForVmCGMonitorLogsWithInfraObjectId("INFRA_002"),
            _fixture.CreateRpForVmCGMonitorLogsWithInfraObjectId("INFRA_003")
        };

        // Act
        var result = await _repository.AddRangeAsync(monitorLogs);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count());

        var savedEntities = await _repository.ListAllAsync();
        Assert.Equal(3, savedEntities.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    [Fact]
    public async Task Repository_ShouldHandleComplexProperties()
    {
        // Arrange
        var monitorLog = _fixture.CreateRpForVmCGMonitorLogsWithProperties(
            infraObjectId: "COMPLEX_INFRA_001",
            consistencyGroupId: "COMPLEX_CG_001",
            state: "Complex State",
            transferStatus: "Complex Transfer Status",
            activityStatus: "Complex Activity Status",
            availabilityStatus: "Complex Availability Status",
            isAlertSent: true,
            lastSnapShotTime: DateTime.UtcNow.AddDays(-1)
        );

        monitorLog.CGProperties = "{\"property1\":\"value1\",\"property2\":\"value2\"}";
        monitorLog.SnapProperties = "{\"snapProperty1\":\"snapValue1\",\"snapProperty2\":\"snapValue2\"}";

        // Act
        var result = await _repository.AddAsync(monitorLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorLog.CGProperties, result.CGProperties);
        Assert.Equal(monitorLog.SnapProperties, result.SnapProperties);
        Assert.Equal(monitorLog.IsAlertSent, result.IsAlertSent);
        Assert.Equal(monitorLog.LastSnapShotTime.Date, result.LastSnapShotTime.Date);
    }

    [Fact]
    public async Task Repository_ShouldHandleEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        var allLogs = await _repository.ListAllAsync();
        Assert.Empty(allLogs);

        var nonExistentLog = await _repository.GetByIdAsync(999);
        Assert.Null(nonExistentLog);

        var nonExistentByReference = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());
        Assert.Null(nonExistentByReference);
    }

    [Theory]
    [InlineData("Active")]
    [InlineData("Inactive")]
    [InlineData("Suspended")]
    [InlineData("Error")]
    public async Task Repository_ShouldHandleDifferentStates(string state)
    {
        // Arrange
        var monitorLog = _fixture.CreateRpForVmCGMonitorLogsWithProperties(state: state);

        // Act
        var result = await _repository.AddAsync(monitorLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(state, result.State);
    }

    [Theory]
    [InlineData("Available")]
    [InlineData("Unavailable")]
    [InlineData("Degraded")]
    [InlineData("Unknown")]
    public async Task Repository_ShouldHandleDifferentAvailabilityStatuses(string availabilityStatus)
    {
        // Arrange
        var monitorLog = _fixture.CreateRpForVmCGMonitorLogsWithProperties(availabilityStatus: availabilityStatus);

        // Act
        var result = await _repository.AddAsync(monitorLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(availabilityStatus, result.AvailabilityStatus);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.RpForVmCGMonitorLogs.RemoveRange(_dbContext.RpForVmCGMonitorLogs);
        await _dbContext.SaveChangesAsync();
    }
}
