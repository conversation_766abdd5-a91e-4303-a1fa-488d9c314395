﻿namespace ContinuityPatrol.Shared.Core.Enums;

public enum Modules
{
    Company,
    AccessManager,
    CredentialProfile,
    Site,
    SiteType,
    Setting,
    OperationalService,
    OperationalFunction,
    Server,
    ComponentType,
    Database,
    Replication,
    InfraObject,
    InfraObjectScheduler,
    MonitoringJob,
    Node,
    Alert,
    Report,
    TeamMaster,
    TeamResource,
    User,
    UserRole,
    UserActivity,
    UserLogin,
    UserLogout,
    Orchestration,
    SingleSignOn,
    Dashboard,
    Form,
    FormType,
    FormTypeCategory,
    FormHistory,
    WorkflowAction,
    WorkflowCategory,
    SolutionHistory,
    WorkflowInfraObject,
    OrchestrationProfile,
    Workflow,
    WorkflowOperation,
    WorkflowOperationGroup,
    WorkflowActionType,
    WorkflowHistory,
    WorkflowProfile,
    WorkflowPermission,
    WorkflowProfileInfo,
    DataSet,
    ManageAlert,
    DataSetColumns,
    TableAccess,
    Password,
    MonitorService,
    PluginManager,
    PluginManagerHistory,
    WorkflowActionResult,
    Template,
    LicenseInfo,
    LoadBalancer,
    LicenseManager,
    BaseLicense,
    DerivedLicense,
    SmtpConfiguration,
    SmsConfiguration,
    ApprovalMatrix,
    DRCalendar,
    ServerType,
    ServerSubType,
    EscalationMatrix,
    EscalationMatrixLevel,
    ReportSchedule,
    Incident,
    RoboCopyJob,
    NotificationManager,
    Archive,
    BackUp,
    GlobalSetting,
    GroupPolicy,
    VeritasCluster,
    InfraReplicationMapping,
    ReplicationJob,
    DriftJob,
    DriftProfile,
    DriftParameter,
    CyberComponent,
    CyberComponentGroup,
    CyberJobManagement,
    CyberSnaps,
    CyberAirGap,
    CyberAlert,
    CyberResiliencyMapping,
    BiaRules,
    UserGroup,
    ServerLog,
    CGExecution
}