﻿let selectedValues = [];

const siteLocationURL = {
    siteLocationExistUrl: "Configuration/SiteLocation/IsSiteLocationExist",
    siteLocationPaginatedUrl: "/Configuration/SiteLocation/GetPaginated",
    siteLocationCreateUrl: "Configuration/SiteLocation/CreateOrUpdate",
    siteLocationDeleteUrl: "Configuration/SiteLocation/Delete"
}



let permission = {
    create: $("#siteLocConfigCreate")?.data("create-permission")?.toLowerCase(),
    delete: $("#siteLocConfigDelete")?.data("delete-permission")?.toLowerCase()
}

if (permission.create == 'false') {
    $("#siteLocCreate").addClass('btn-disabled').css('pointer-events', 'none');
}

let dataTable = $('#siteLocationTable').DataTable(
    {
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
            }
            , infoFiltered: ""
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        "processing": true,
        "serverSide": true,
        "filter": true,
        "order": [],
        "ajax": {
            "type": "GET",
            "url": siteLocationURL.siteLocationPaginatedUrl,
            "dataType": "json",
            "data": function (d) {
                let sortIndex = d?.order[0]?.column || '';
                let sortValue = sortIndex === 1 ? "city" : sortIndex === 2 ? "country" : sortIndex === 3 ? "lat" :
                    sortIndex === 4 ? "lng" : sortIndex === 5 ? "status" : "";
                let orderValue = d?.order[0]?.dir || 'asc';
                d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                d.pageSize = d?.length;
                d.searchString = selectedValues?.length === 0 ? $('#siteLocSearch')?.val() : selectedValues?.join(';');
                d.sortColumn = sortValue;
                d.SortOrder = orderValue;
                selectedValues.length = 0;
            },
            "dataSrc": function (json) {
                json.recordsTotal = json?.data?.totalPages;
                json.recordsFiltered = json?.data?.totalCount;
                if (json?.success && Array.isArray(json?.data?.data) && json?.data?.data?.length) {
                    $(".pagination-column").removeClass("disabled");
                    return json?.data?.data;
                } else {
                    $(".pagination-column").addClass("disabled");
                    if (!json?.success && json?.message) {
                        notificationAlert('warning', json?.message);
                    }
                    return [];
                }
            }
        },
        "columnDefs": [
            {
                "targets": [1, 2],
                "className": "truncate"
            }
        ],
        "columns": [
            {
                "data": null,
                "name": "Sr. No.",
                "autoWidth": true,
                orderable: false,
                "render": function (data, type, row, meta) {
                    if (type === 'display') {
                        return meta?.row + 1;
                    }
                    return data;
                },
                orderable: false
            },
            {
                "data": "city", "name": "City Name", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return `<td><span title="${row?.city || 'NA'}" > ${row?.city || 'NA'}</span></td>`
                    }
                    return data;
                }
            },
            {
                "data": "country", "name": "Country Name", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return `<td><span title="${row?.country || 'NA'}"> ${row?.country || "NA"}</span></td>`
                    }
                    return data;
                }
            },
            {
                "data": "lat", "name": "Latitude", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return `<td><span  > ${row?.lat || 'NA'}</span></td>`
                    }
                    return data;
                }
            },
            {
                "data": "lng", "name": "Longitude", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return `<td><span  > ${row?.lng || 'NA'}</span></td>`
                    }
                    return data;
                }
            },
            {
                "render": function (data, type, row) {
                    return `<div class="d-flex align-items-center gap-2">${permission.create === 'true' && row?.isDelete !== false ? `<span role="button" title="Edit" class="btnSiteEdit" data-location='${row ? btoa(JSON.stringify(row || "NA")) : 'NA'}'><i class="cp-edit"></i></span>` : `<span role="button" title="Edit" class="icon-disabled"><i class="cp-edit"></i></span>`}
                        ${permission.delete === 'true' && row?.isDelete !== false ? `<span role="button" title="Delete" class="btnSiteDelete"data-location-id="${row?.id || 'NA'}"data-location-name="${row?.city || 'NA'}"><i class="cp-Delete"></i></span>` : `<span role="button" title="Delete" class="icon-disabled"> <i class="cp-Delete"></i></span>`} </div>`;
                },
                "orderable": false
            }
        ],
        "rowCallback": function (row, data, index) {
            let api = this.api();
            let startIndex = api?.context[0]?._iDisplayStart;
            let counter = startIndex + index + 1;
            $('td:eq(0)', row).html(counter);
        },
        initComplete: function () {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
        }
    }
);

$('#siteLocSearch').on('keydown input', function (e) {
    if (e.key === '=' || e.key === 'Enter' || (e.shiftKey && e.key == '<')) {
        e.preventDefault();
        return false;
    }
    handleSearchDebounced();
});

const handleSearchDebounced = commonDebounce(function () {

    const NameCheckbox = $("#siteLocCityFilter");
    const CountryNameCheckbox = $("#siteLocCountryFilter");
    const inputValue = $('#siteLocSearch').val();

    if (NameCheckbox.is(':checked')) {
        selectedValues.push(NameCheckbox?.val() + inputValue);
    }
    if (CountryNameCheckbox.is(':checked')) {
        selectedValues.push(CountryNameCheckbox?.val() + inputValue);
    }
    dataTable.ajax.reload(function (json) {
        if (json?.recordsFiltered === 0) {
            $('.dataTables_empty').text('No matching records found');
        }
    })
}, 500);

// Populate Fields on edit
function populateSiteLocation(SiteLocation) {
    $('#cityNameError, #countryNameError, #latitudeError, #longitudeError').text('').removeClass('field-validation-error')
    $('#cityName').val(SiteLocation?.city).attr('cityNameId', SiteLocation?.id);
    $('#countryName').val(SiteLocation?.country);
    $('#latitudeID').val(SiteLocation?.lat);
    $('#longitudeId').val(SiteLocation?.lng);
}

async function IsNameExist(url, data) {
    return !data?.name?.trim() ? true : (await getAysncWithHandler(url, data)) ? "Name already exists" : true;
}

// City Name Country name Validate func
async function validateLocationOrCountry(value, errorElement, errordisplay, checkIsNameExist, id = null) {

    if (!value) {
        errorElement.text(errordisplay).addClass('field-validation-error');
        return false;
    } else {
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed').addClass('field-validation-error');
            return false;
        }
        const validationResults = [
            SpecialCharValidate(value), ShouldNotBeginWithSpace(value), ShouldNotBeginWithUnderScore(value),
            OnlyNumericsValidate(value), ShouldNotBeginWithNumber(value), ShouldNotBeginWithDotAndHyphen(value),
            ShouldNotConsecutiveDotAndHyphen(value), ShouldNotEndWithSpace(value), ShouldNotAllowMultipleSpace(value),
            SpaceWithUnderScore(value), ShouldNotEndWithUnderScore(value), MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value), secondChar(value), minMaxlength(value)
        ];
        if (checkIsNameExist) {
            const url = RootUrl + siteLocationURL.siteLocationExistUrl;
            const data = { name: value, id: id };
            validationResults.push(await IsNameExist(url, data));
        }
        return CommonValidation(errorElement, validationResults);
    }
}

// Latitude Longitute validate func
function validateDropDown(value, errorMessage, errorElement, errortext) {

    let exceptThisSymbol = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "_", "+", "=", "{", "}", "[", "]", "|", ":", ";", "'", "\"", "<", ">", ",", "?", "/", "\\"];
    let regex = new RegExp('[' + exceptThisSymbol.join('\\') + ']|\\.(?=.*\\.)');
    let result = value.split('').every(d => d === '0') ? false : true;
    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    }
    if (regex.test(value) || result == false) {
        $('#' + errorElement).text(errortext).addClass('field-validation-error');
        return false;
    }
    if (value === '.' || value === '-' || value === '-.' || value.startsWith('.') || value.includes('-.') || value.includes('.-') || value.endsWith('.') || value.endsWith('-')) {
        $('#' + errorElement).text(errortext).addClass('field-validation-error');
        return false;
    }
    else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
}

// Clear data on create
$('#siteLocCreate').on('click', function () {
    $('#cityName').val("").attr("cityNameId", "").text("");
    $('#countryName, #latitudeID, #longitudeId').val("").text("");
    $('#cityNameError, #countryNameError, #latitudeError, #longitudeError').text('').removeClass('field-validation-error');
    $('#siteLocSaveBtn').text('Save');
    $('#siteLocCreateModal').modal('show');
});

// Save and Update on click
$("#siteLocSaveBtn").on('click', commonDebounce(async function () {
    let cityName = $("#cityName").val();
    let cityId = $('#cityName').attr('cityNameId');
    let countryName = $("#countryName").val();
    let iscityName = await validateLocationOrCountry(cityName, $('#cityNameError'), 'Enter city name', true, cityId);
    let iscountryName = await validateLocationOrCountry(countryName, $("#countryNameError"), ' Enter country name', false, null);
    let islatitude = validateDropDown($("#latitudeID").val(), ' Enter city latitude', 'latitudeError', 'Invalid latitude');
    let islongitude = validateDropDown($("#longitudeId").val(), ' Enter city longitude', 'longitudeError', 'Invalid longitude');

    if (iscityName && iscountryName && islatitude && islongitude) {

        sanitizeContainer(['cityName', 'countryName', 'latitudeID', 'longitudeId']);
        let savedata = {
            id: cityId, city: cityName, CityAscii: cityName, lat: $("#latitudeID").val(), lng: $("#longitudeId").val(), country: countryName, Iso2: "", Iso3: "", isdelete: "", __RequestVerificationToken: gettoken()
        }

        await $.ajax({
            url: RootUrl + siteLocationURL.siteLocationCreateUrl,
            type: "POST",
            dataType: "json",
            data: savedata,
            success: function (response) {
                if (response && response?.success && response?.data?.message) {
                    $('#siteLocCreateModal').modal('hide');
                    notificationAlert("success", response?.data?.message);
                    setTimeout(() => {
                        dataTable.ajax.reload()
                    }, 1000);
                } else {
                    errorNotification(response);
                }
            }
        });
    }
}, 800));

//  Update btn on click
$('#siteLocationTable').on('click', '.btnSiteEdit', function () {
    let SiteLocation = JSON.parse(atob($(this).data("location")));
    if (SiteLocation) {
        populateSiteLocation(SiteLocation);
        $('#siteLocSaveBtn').text("Update");
        $('#siteLocCreateModal').modal('show');
    }
});

//  Delete btn on click
$('#siteLocationTable').on('click', '.btnSiteDelete', function () {
    const siteLocationId = $(this).data("location-id");
    const siteLocationName = $(this).data("location-name")
    if (siteLocationId) {
        $("#siteLocDeleteId").attr("title", siteLocationName).val(siteLocationId).text(siteLocationName);
        $('#siteLocDeleteModal').modal('show');
    }
});

// Confirm Delete on Click
$("#siteLocDeleteButton").on('click', async function () {
    const deleteid = $('#siteLocDeleteId').val();
    if (deleteid) {
        await $.ajax({
            url: RootUrl + siteLocationURL.siteLocationDeleteUrl,
            type: "DELETE",
            dataType: "json",
            data: { id: deleteid },
            success: function (response) {
                if (response && response?.success && response?.data?.message) {
                    $('#siteLocDeleteModal').modal('hide');
                    notificationAlert("success", response?.data?.message);
                    setTimeout(() => {
                        dataTable.ajax.reload()
                    }, 1000);
                } else {
                    $('#siteLocDeleteModal').modal('hide');
                    errorNotification(response);
                }
            }
        });
    }
});

// City name and country name validation
$('#cityName,#countryName').on('input', commonDebounce(async function () {
    const sanitizedValue = $(this).val()?.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    if (this.id === 'cityName') {
        let siteLocationId = $('#cityName').attr('cityNameId');
        await validateLocationOrCountry(sanitizedValue, $('#cityNameError'), 'Enter city name', true, siteLocationId);
    } else {
        await validateLocationOrCountry(sanitizedValue, $('#countryNameError'), 'Enter country name', false, null);
    }
}, 400));

// Latitude and Longitude validation
$('#latitudeID,#longitudeId').on('input', function () {
    const regex = /^-?\d*\.?\d*$/;
    const value = $(this).val();
    if (regex.test(value)) {
        $(this).val(value);
    } else {
        $(this).val(value?.slice(0, -1));
    }
    if (this.id === 'latitudeID') {
        validateDropDown($(this).val(), 'Enter city latitude', 'latitudeError', 'Invalid latitude');
    } else {
        validateDropDown($(this).val(), 'Enter city longitude', 'longitudeError', 'Invalid longitude');
    }
});
