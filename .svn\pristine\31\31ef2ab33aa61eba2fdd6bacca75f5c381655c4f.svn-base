using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SchedulerWorkflowActionResultsFixture : IDisposable
{
    public List<SchedulerWorkflowActionResults> SchedulerWorkflowActionResultsPaginationList { get; set; }
    public List<SchedulerWorkflowActionResults> SchedulerWorkflowActionResultsList { get; set; }
    public SchedulerWorkflowActionResults SchedulerWorkflowActionResultsDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SchedulerWorkflowActionResultsFixture()
    {
        var fixture = new Fixture();

        SchedulerWorkflowActionResultsList = fixture.Create<List<SchedulerWorkflowActionResults>>();

        SchedulerWorkflowActionResultsPaginationList = fixture.CreateMany<SchedulerWorkflowActionResults>(20).ToList();

        SchedulerWorkflowActionResultsDto = fixture.Create<SchedulerWorkflowActionResults>();


        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
