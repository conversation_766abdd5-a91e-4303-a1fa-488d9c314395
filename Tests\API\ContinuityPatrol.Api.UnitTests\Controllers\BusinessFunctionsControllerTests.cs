﻿using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Delete;
using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetBusinessFunctionByBusinessServiceId;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetList;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetListByBusinessServiceId;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetNames;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class BusinessFunctionsControllerTests : IClassFixture<BusinessFunctionFixture>
{
    private readonly BusinessFunctionFixture _fixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly BusinessFunctionsController _controller;

    public BusinessFunctionsControllerTests(BusinessFunctionFixture fixture)
    {
        _fixture = fixture;

        var testBuilder = new ControllerTestBuilder<BusinessFunctionsController>();
        _controller = testBuilder.CreateController(
            _ => new BusinessFunctionsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetBusinessFunctions_ReturnsExpectedList()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.BusinessFunctionListVm);

        var result = await _controller.GetBusinessFunctions();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var businessFunctions = Assert.IsAssignableFrom<List<BusinessFunctionListVm>>(okResult.Value);
        Assert.Equal(_fixture.BusinessFunctionListVm.Count, businessFunctions.Count);
    }

    [Fact]
    public async Task GetBusinessFunctions_ReturnsEmptyList_WhenNoBusinessFunctionsExist()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionListQuery>(), default))
            .ReturnsAsync(new List<BusinessFunctionListVm>());

        var result = await _controller.GetBusinessFunctions();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<BusinessFunctionListVm>)okResult.Value!));
    }

    [Fact]
    public async Task GetBusinessFunctionById_ReturnsBusinessFunction_WhenIdIsValid()
    {
        var businessFunctionId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessFunctionDetailQuery>(q => q.Id == businessFunctionId), default))
            .ReturnsAsync(_fixture.BusinessFunctionDetailVm);

        var result = await _controller.GetBusinessFunctionById(businessFunctionId);

        Assert.IsType<OkObjectResult>(result.Result);
    }

    [Fact]
    public async Task GetBusinessFunctionById_Throws_WhenIdIsInvalid()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetBusinessFunctionById("invalid-guid"));
    }

    [Fact]
    public async Task CreateBusinessFunction_Throws_WhenNameExists()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateBusinessFunctionCommand>(), default))
            .ThrowsAsync(new InvalidOperationException("Name exists"));

        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.CreateBusinessFunction(_fixture.CreateBusinessFunctionCommand));
    }

    [Fact]
    public async Task CreateBusinessFunction_Returns201Created()
    {
        var command = _fixture.CreateBusinessFunctionCommand;
        var expectedMessage = $"BusinessFunction '{command.Name}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBusinessFunctionResponse
            {
                Message = expectedMessage,
                BusinessFunctionId = "new-guid"
            });

        var result = await _controller.CreateBusinessFunction(command);

        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBusinessFunctionResponse>(createdAtActionResult.Value);

        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBusinessFunction_ReturnsOk()
    {
        var expectedMessage = $"BusinessFunction '{_fixture.UpdateBusinessFunctionCommand.Name}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateBusinessFunctionCommand>(), default))
            .ReturnsAsync(new UpdateBusinessFunctionResponse
            {
                Message = expectedMessage,
                BusinessFunctionId = _fixture.UpdateBusinessFunctionCommand.Id
            });

        var result = await _controller.UpdateBusinessFunction(_fixture.UpdateBusinessFunctionCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBusinessFunctionResponse>(okResult.Value);

        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteBusinessFunction_ReturnsOk()
    {
        var expectedMessage = "BusinessFunction 'TestBusinessFunction' has been deleted successfully!.";
        var businessFunctionId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBusinessFunctionCommand>(c => c.Id == businessFunctionId), default))
            .ReturnsAsync(new DeleteBusinessFunctionResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        var result = await _controller.DeleteBusinessFunction(businessFunctionId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBusinessFunctionResponse>(okResult.Value);

        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteBusinessFunction_Throws_WhenIdIsInvalid()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteBusinessFunction("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedBusinessFunctions_ReturnsExpectedPaginatedList()
    {
        var query = new GetBusinessFunctionPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _fixture.BusinessFunctionListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<BusinessFunctionListVm>.Success(
                data: expectedData,
                count: expectedData.Count,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        var result = await _controller.GetPaginatedBusinessFunctions(query);

        var actionResult = Assert.IsType<ActionResult<List<BusinessFunctionListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<BusinessFunctionListVm>>(okResult.Value);

        Assert.Equal(expectedData.Count, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(expectedData.Count, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task GetBusinessFunctionNames_ReturnsExpectedList()
    {
        _controller.Cache.Remove("all-businessfunctions-name");

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<BusinessFunctionNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.BusinessFunctionNameVm);

        var result = await _controller.GetBusinessFunctionNames();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var businessFunctionNames = Assert.IsAssignableFrom<List<BusinessFunctionNameVm>>(okResult.Value);
        Assert.Equal(_fixture.BusinessFunctionNameVm.Count, businessFunctionNames.Count);
    }

    [Fact]
    public async Task GetBusinessFunctionNames_ReturnsEmptyList_WhenNoBusinessFunctionNamesExist()
    {
        _controller.Cache.Remove("all-businessfunctions-name");

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<BusinessFunctionNameQuery>(), default))
            .ReturnsAsync(new List<BusinessFunctionNameVm>());

        var result = await _controller.GetBusinessFunctionNames();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<BusinessFunctionNameVm>)okResult.Value!));
    }

    [Fact]
    public async Task IsBusinessFunctionNameExist_ReturnsTrue_WhenNameExists()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        var result = await _controller.IsBusinessFunctionNameExist("ExistingBusinessFunction", null);

        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsBusinessFunctionNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetBusinessFunctionNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        var result = await _controller.IsBusinessFunctionNameExist("NewBusinessFunction", null);

        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsBusinessFunctionNameExist_ThrowsInvalidArgumentException_WhenNameIsInvalid()
    {
        var businessFunctionName = "";

        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsBusinessFunctionNameExist(businessFunctionName, null));
    }

    [Fact]
    public async Task GetBusinessFunctionListByBusinessServiceId_ReturnsBusinessFunctionList_WhenBusinessServiceIdIsValid()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var expectedBusinessFunctions = new List<BusinessFunctionListByBusinessServiceIdVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessServiceName = "Test Business Service",
                Name = "Test Business Function 1",
                Description = "Test Description 1",
                CriticalityLevel = "High",
                ConfiguredRPO = "4 Hours",
                ConfiguredRTO = "2 Hours",
                ConfiguredMAO = "1 Hour",
                RPOThreshold = "6 Hours"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessServiceName = "Test Business Service",
                Name = "Test Business Function 2",
                Description = "Test Description 2",
                CriticalityLevel = "Medium",
                ConfiguredRPO = "8 Hours",
                ConfiguredRTO = "4 Hours",
                ConfiguredMAO = "2 Hours",
                RPOThreshold = "12 Hours"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessFunctionListByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(expectedBusinessFunctions);

        // Act
        var result = await _controller.GetBusinessFunctionListByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var businessFunctions = Assert.IsType<List<BusinessFunctionListByBusinessServiceIdVm>>(okResult.Value);
        Assert.Equal(2, businessFunctions.Count);
        Assert.Equal("Test Business Function 1", businessFunctions[0].Name);
        Assert.Equal("Test Business Function 2", businessFunctions[1].Name);
        Assert.Equal(businessServiceId, businessFunctions[0].BusinessServiceId);
        Assert.Equal(businessServiceId, businessFunctions[1].BusinessServiceId);
    }

    [Fact]
    public async Task GetBusinessFunctionListByBusinessServiceId_ThrowsInvalidArgumentException_WhenBusinessServiceIdIsInvalid()
    {
        // Arrange
        var invalidBusinessServiceId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetBusinessFunctionListByBusinessServiceId(invalidBusinessServiceId));
    }

    [Fact]
    public async Task GetBusinessFunctionListByBusinessServiceId_ReturnsEmptyList_WhenNoBusinessFunctionsExist()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessFunctionListByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(new List<BusinessFunctionListByBusinessServiceIdVm>());

        // Act
        var result = await _controller.GetBusinessFunctionListByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var businessFunctions = Assert.IsType<List<BusinessFunctionListByBusinessServiceIdVm>>(okResult.Value);
        Assert.Empty(businessFunctions);
    }

    [Fact]
    public async Task GetBusinessFunctionNamesByBusinessServiceId_ReturnsBusinessFunctionNames_WhenBusinessServiceIdIsValid()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var expectedBusinessFunctionNames = new List<GetBusinessFunctionNameByBusinessServiceIdVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Test Business Function 1",
                BusinessServiceId = businessServiceId
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Test Business Function 2",
                BusinessServiceId = businessServiceId
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessFunctionNameByBusinessServiceIdQuery>(q => q.Id == businessServiceId), default))
            .ReturnsAsync(expectedBusinessFunctionNames);

        // Act
        var result = await _controller.GetBusinessFunctionNamesByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var businessFunctionNames = Assert.IsType<List<GetBusinessFunctionNameByBusinessServiceIdVm>>(okResult.Value);
        Assert.Equal(2, businessFunctionNames.Count);
        Assert.Equal("Test Business Function 1", businessFunctionNames[0].Name);
        Assert.Equal("Test Business Function 2", businessFunctionNames[1].Name);
        Assert.Equal(businessServiceId, businessFunctionNames[0].BusinessServiceId);
        Assert.Equal(businessServiceId, businessFunctionNames[1].BusinessServiceId);
    }

    [Fact]
    public async Task GetBusinessFunctionNamesByBusinessServiceId_ThrowsInvalidArgumentException_WhenBusinessServiceIdIsInvalid()
    {
        // Arrange
        var invalidBusinessServiceId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetBusinessFunctionNamesByBusinessServiceId(invalidBusinessServiceId));
    }

    [Fact]
    public async Task GetBusinessFunctionNamesByBusinessServiceId_ReturnsEmptyList_WhenNoBusinessFunctionNamesExist()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessFunctionNameByBusinessServiceIdQuery>(q => q.Id == businessServiceId), default))
            .ReturnsAsync(new List<GetBusinessFunctionNameByBusinessServiceIdVm>());

        // Act
        var result = await _controller.GetBusinessFunctionNamesByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var businessFunctionNames = Assert.IsType<List<GetBusinessFunctionNameByBusinessServiceIdVm>>(okResult.Value);
        Assert.Empty(businessFunctionNames);
    }

    [Fact]
    public async Task GetPaginatedBusinessFunctions_ReturnsEmptyResult_WhenNoDataExists()
    {
        // Arrange
        var query = new GetBusinessFunctionPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<BusinessFunctionListVm>.Success(
                data: new List<BusinessFunctionListVm>(),
                count: 0,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        // Act
        var result = await _controller.GetPaginatedBusinessFunctions(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<BusinessFunctionListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<BusinessFunctionListVm>>(okResult.Value);

        Assert.Empty(paginatedResult.Data);
        Assert.Equal(0, paginatedResult.TotalCount);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task CreateBusinessFunction_ReturnsCreatedResponse_WithValidCommand()
    {
        // Arrange
        var command = _fixture.CreateBusinessFunctionCommand;
        var expectedResponse = new CreateBusinessFunctionResponse
        {
            BusinessFunctionId = Guid.NewGuid().ToString(),
            Message = $"BusinessFunction '{command.Name}' has been created successfully!."
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateBusinessFunctionCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateBusinessFunction(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBusinessFunctionResponse>(createdAtActionResult.Value);

        Assert.NotNull(response.BusinessFunctionId);
        Assert.Contains("created successfully", response.Message);
    }

    [Fact]
    public async Task UpdateBusinessFunction_ReturnsUpdatedResponse_WithValidCommand()
    {
        // Arrange
        var command = _fixture.UpdateBusinessFunctionCommand;
        var expectedResponse = new UpdateBusinessFunctionResponse
        {
            BusinessFunctionId = command.Id,
            Message = $"BusinessFunction '{command.Name}' has been updated successfully!."
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateBusinessFunctionCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateBusinessFunction(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBusinessFunctionResponse>(okResult.Value);

        Assert.Equal(command.Id, response.BusinessFunctionId);
        Assert.Contains("updated successfully", response.Message);
    }

    [Fact]
    public async Task UpdateBusinessFunction_Throws_WhenBusinessFunctionNotFound()
    {
        // Arrange
        var command = _fixture.UpdateBusinessFunctionCommand;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateBusinessFunctionCommand>(), default))
            .ThrowsAsync(new NotFoundException("BusinessFunction", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdateBusinessFunction(command));
    }

    [Fact]
    public async Task DeleteBusinessFunction_Throws_WhenBusinessFunctionNotFound()
    {
        // Arrange
        var businessFunctionId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBusinessFunctionCommand>(c => c.Id == businessFunctionId), default))
            .ThrowsAsync(new NotFoundException("BusinessFunction", businessFunctionId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.DeleteBusinessFunction(businessFunctionId));
    }

    [Fact]
    public async Task GetBusinessFunctionById_Throws_WhenBusinessFunctionNotFound()
    {
        // Arrange
        var businessFunctionId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessFunctionDetailQuery>(q => q.Id == businessFunctionId), default))
            .ThrowsAsync(new NotFoundException("BusinessFunction", businessFunctionId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetBusinessFunctionById(businessFunctionId));
    }

    [Fact]
    public async Task IsBusinessFunctionNameExist_IncludesIdInQuery_WhenProvided()
    {
        // Arrange
        var businessFunctionName = "TestBusinessFunction";
        var businessFunctionId = Guid.NewGuid().ToString();
        string? capturedId = null;
        string? capturedName = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionNameUniqueQuery>(), default))
            .Callback<IRequest<bool>, CancellationToken>((request, _) =>
            {
                if (request is GetBusinessFunctionNameUniqueQuery query)
                {
                    capturedId = query.BusinessFunctionId;
                    capturedName = query.BusinessFunctionName;
                }
            })
            .ReturnsAsync(false);

        // Act
        await _controller.IsBusinessFunctionNameExist(businessFunctionName, businessFunctionId);

        // Assert
        Assert.Equal(businessFunctionId, capturedId);
        Assert.Equal(businessFunctionName, capturedName);
    }

    [Fact]
    public async Task IsBusinessFunctionNameExist_ExcludesIdFromQuery_WhenNotProvided()
    {
        // Arrange
        var businessFunctionName = "TestBusinessFunction";
        string? capturedId = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionNameUniqueQuery>(), default))
            .Callback<IRequest<bool>, CancellationToken>((request, _) =>
            {
                if (request is GetBusinessFunctionNameUniqueQuery query)
                {
                    capturedId = query.BusinessFunctionId;
                }
            })
            .ReturnsAsync(false);

        // Act
        await _controller.IsBusinessFunctionNameExist(businessFunctionName, null);

        // Assert
        Assert.Null(capturedId);
    }

    [Fact]
    public async Task GetPaginatedBusinessFunctions_HandlesSearchString_Correctly()
    {
        // Arrange
        var query = new GetBusinessFunctionPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "TestSearch"
        };

        var expectedData = _fixture.BusinessFunctionListVm.Take(1).ToList();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessFunctionPaginatedListQuery>(q => q.SearchString == "TestSearch"), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<BusinessFunctionListVm>.Success(
                data: expectedData,
                count: expectedData.Count,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        // Act
        var result = await _controller.GetPaginatedBusinessFunctions(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<BusinessFunctionListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<BusinessFunctionListVm>>(okResult.Value);

        Assert.Single(paginatedResult.Data);
        Assert.Equal(1, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task GetPaginatedBusinessFunctions_HandlesDifferentPageSizes()
    {
        // Arrange
        var query = new GetBusinessFunctionPaginatedListQuery
        {
            PageNumber = 2,
            PageSize = 5
        };

        var expectedData = _fixture.BusinessFunctionListVm.Take(2).ToList();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<BusinessFunctionListVm>.Success(
                data: expectedData,
                count: expectedData.Count,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        // Act
        var result = await _controller.GetPaginatedBusinessFunctions(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<BusinessFunctionListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<BusinessFunctionListVm>>(okResult.Value);

        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal(2, paginatedResult.CurrentPage);
        Assert.Equal(5, paginatedResult.PageSize);
    }

    [Fact]
    public async Task CreateBusinessFunction_ValidatesCommand_Properties()
    {
        // Arrange
        var command = new CreateBusinessFunctionCommand
        {
            Name = "Test Business Function",
            Description = "Test Description",
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Test Business Service",
            CriticalityLevel = "High",
            ConfiguredRPO = "4 Hours",
            ConfiguredRTO = "2 Hours",
            ConfiguredMAO = "1 Hour"
        };

        var expectedResponse = new CreateBusinessFunctionResponse
        {
            BusinessFunctionId = Guid.NewGuid().ToString(),
            Message = $"BusinessFunction '{command.Name}' has been created successfully!."
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<CreateBusinessFunctionCommand>(c =>
                c.Name == command.Name &&
                c.Description == command.Description &&
                c.BusinessServiceId == command.BusinessServiceId &&
                c.CriticalityLevel == command.CriticalityLevel), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateBusinessFunction(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBusinessFunctionResponse>(createdAtActionResult.Value);

        Assert.NotNull(response.BusinessFunctionId);
        Assert.Contains("created successfully", response.Message);
    }

    [Fact]
    public async Task UpdateBusinessFunction_ValidatesCommand_Properties()
    {
        // Arrange
        var command = new UpdateBusinessFunctionCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Updated Business Function",
            Description = "Updated Description",
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Updated Business Service",
            CriticalityLevel = "Medium",
            ConfiguredRPO = "8 Hours",
            ConfiguredRTO = "4 Hours",
            ConfiguredMAO = "2 Hours"
        };

        var expectedResponse = new UpdateBusinessFunctionResponse
        {
            BusinessFunctionId = command.Id,
            Message = $"BusinessFunction '{command.Name}' has been updated successfully!."
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<UpdateBusinessFunctionCommand>(c =>
                c.Id == command.Id &&
                c.Name == command.Name &&
                c.Description == command.Description &&
                c.CriticalityLevel == command.CriticalityLevel), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateBusinessFunction(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBusinessFunctionResponse>(okResult.Value);

        Assert.Equal(command.Id, response.BusinessFunctionId);
        Assert.Contains("updated successfully", response.Message);
    }

    [Fact]
    public async Task GetBusinessFunctions_CallsCorrectQuery()
    {
        // Arrange
        var queryExecuted = false;

        // Setup mediator to track when it's called by cache factory
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<BusinessFunctionListVm>());

        // Act
        await _controller.GetBusinessFunctions();

        // Assert
        Assert.True(queryExecuted);
    }

    [Fact]
    public async Task GetBusinessFunctionNames_CallsCorrectQuery()
    {
        // Arrange
        var queryExecuted = false;

        // Setup mediator to track when it's called by cache factory
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<BusinessFunctionNameQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<BusinessFunctionNameVm>());

        // Act
        await _controller.GetBusinessFunctionNames();

        // Assert
        Assert.True(queryExecuted);
    }

    [Fact]
    public async Task IsBusinessFunctionNameExist_ThrowsInvalidArgumentException_WhenNameIsWhitespace()
    {
        // Arrange
        var businessFunctionName = "   ";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsBusinessFunctionNameExist(businessFunctionName, null));
    }

    [Fact]
    public async Task IsBusinessFunctionNameExist_ThrowsArgumentNullException_WhenNameIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _controller.IsBusinessFunctionNameExist(null!, null));
    }

    [Fact]
    public async Task CreateBusinessFunction_Throws_WhenDuplicateNameExists()
    {
        // Arrange
        var command = _fixture.CreateBusinessFunctionCommand;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateBusinessFunctionCommand>(), default))
            .ThrowsAsync(new InvalidOperationException($"BusinessFunction with name '{command.Name}' already exists"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.CreateBusinessFunction(command));

        Assert.Contains("already exists", exception.Message);
    }

    [Fact]
    public async Task UpdateBusinessFunction_Throws_WhenDuplicateNameExists()
    {
        // Arrange
        var command = _fixture.UpdateBusinessFunctionCommand;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateBusinessFunctionCommand>(), default))
            .ThrowsAsync(new InvalidOperationException($"BusinessFunction with name '{command.Name}' already exists"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.UpdateBusinessFunction(command));

        Assert.Contains("already exists", exception.Message);
    }

    [Fact]
    public async Task GetPaginatedBusinessFunctions_HandlesLargePageSize()
    {
        // Arrange
        var query = new GetBusinessFunctionPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 1000
        };

        var expectedData = _fixture.BusinessFunctionListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<BusinessFunctionListVm>.Success(
                data: expectedData,
                count: expectedData.Count,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        // Act
        var result = await _controller.GetPaginatedBusinessFunctions(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<BusinessFunctionListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<BusinessFunctionListVm>>(okResult.Value);

        Assert.Equal(expectedData.Count, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(1000, paginatedResult.PageSize);
        Assert.Equal(expectedData.Count, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task GetPaginatedBusinessFunctions_HandlesZeroPageSize()
    {
        // Arrange
        var query = new GetBusinessFunctionPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 0
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<BusinessFunctionListVm>.Success(
                data: new List<BusinessFunctionListVm>(),
                count: 0,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        // Act
        var result = await _controller.GetPaginatedBusinessFunctions(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<BusinessFunctionListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<BusinessFunctionListVm>>(okResult.Value);

        Assert.Empty(paginatedResult.Data);
        Assert.Equal(0, paginatedResult.PageSize);
    }

    [Fact]
    public async Task GetBusinessFunctionById_ReturnsCorrectBusinessFunction_WithAllProperties()
    {
        // Arrange
        var businessFunctionId = Guid.NewGuid().ToString();
        var expectedBusinessFunction = new BusinessFunctionDetailVm
        {
            Id = businessFunctionId,
            Name = "Test Business Function",
            Description = "Test Description",
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Test Business Service",
            CriticalityLevel = "High",
            ConfiguredRPO = "4 Hours",
            ConfiguredRTO = "2 Hours",
            ConfiguredMAO = "1 Hour"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessFunctionDetailQuery>(q => q.Id == businessFunctionId), default))
            .ReturnsAsync(expectedBusinessFunction);

        // Act
        var result = await _controller.GetBusinessFunctionById(businessFunctionId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var businessFunction = Assert.IsType<BusinessFunctionDetailVm>(okResult.Value);

        Assert.Equal(expectedBusinessFunction.Id, businessFunction.Id);
        Assert.Equal(expectedBusinessFunction.Name, businessFunction.Name);
        Assert.Equal(expectedBusinessFunction.Description, businessFunction.Description);
        Assert.Equal(expectedBusinessFunction.BusinessServiceId, businessFunction.BusinessServiceId);
        Assert.Equal(expectedBusinessFunction.BusinessServiceName, businessFunction.BusinessServiceName);
        Assert.Equal(expectedBusinessFunction.CriticalityLevel, businessFunction.CriticalityLevel);
        Assert.Equal(expectedBusinessFunction.ConfiguredRPO, businessFunction.ConfiguredRPO);
        Assert.Equal(expectedBusinessFunction.ConfiguredRTO, businessFunction.ConfiguredRTO);
        Assert.Equal(expectedBusinessFunction.ConfiguredMAO, businessFunction.ConfiguredMAO);
    }

    [Fact]
    public async Task DeleteBusinessFunction_ReturnsCorrectResponse_WithIsActiveFalse()
    {
        // Arrange
        var businessFunctionId = Guid.NewGuid().ToString();
        var expectedResponse = new DeleteBusinessFunctionResponse
        {
            IsActive = false,
            Message = "BusinessFunction has been deleted successfully!."
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBusinessFunctionCommand>(c => c.Id == businessFunctionId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteBusinessFunction(businessFunctionId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBusinessFunctionResponse>(okResult.Value);

        Assert.False(response.IsActive);
        Assert.Contains("deleted successfully", response.Message);
    }

    [Fact]
    public async Task GetBusinessFunctionNames_ReturnsBusinessFunctionNames()
    {
        // Arrange
        // Clear the cache to ensure fresh data
        _controller.Cache.Remove("all-businessfunctions-name");

        // Reset any previous mock setups to avoid interference
        _mediatorMock.Reset();

        // Create a fresh list to avoid any reference issues
        var expectedBusinessFunctionNames = new List<BusinessFunctionNameVm>
        {
            new() { Id = "1", Name = "Function 1" },
            new() { Id = "2", Name = "Function 2" },
            new() { Id = "3", Name = "Function 3" }
        };

        // Setup mediator to return the expected data when called by cache factory
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<BusinessFunctionNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedBusinessFunctionNames);

        // Act
        var result = await _controller.GetBusinessFunctionNames();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var businessFunctionNames = Assert.IsAssignableFrom<List<BusinessFunctionNameVm>>(okResult.Value);

        Assert.NotNull(businessFunctionNames);
        Assert.Equal(3, businessFunctionNames.Count);
        Assert.Equal("Function 1", businessFunctionNames[0].Name);
        Assert.Equal("Function 2", businessFunctionNames[1].Name);
        Assert.Equal("Function 3", businessFunctionNames[2].Name);
    }

    [Fact]
    public async Task GetBusinessFunctionListByBusinessServiceId_ValidatesBusinessServiceId()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        string? capturedId = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionListByBusinessServiceIdQuery>(), default))
            .Callback<IRequest<List<BusinessFunctionListByBusinessServiceIdVm>>, CancellationToken>((request, _) =>
            {
                if (request is GetBusinessFunctionListByBusinessServiceIdQuery query)
                {
                    capturedId = query.BusinessServiceId;
                }
            })
            .ReturnsAsync(new List<BusinessFunctionListByBusinessServiceIdVm>());

        // Act
        await _controller.GetBusinessFunctionListByBusinessServiceId(businessServiceId);

        // Assert
        Assert.Equal(businessServiceId, capturedId);
    }

    [Fact]
    public async Task GetBusinessFunctionNamesByBusinessServiceId_ValidatesBusinessServiceId()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        string? capturedId = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionNameByBusinessServiceIdQuery>(), default))
            .Callback<IRequest<List<GetBusinessFunctionNameByBusinessServiceIdVm>>, CancellationToken>((request, _) =>
            {
                if (request is GetBusinessFunctionNameByBusinessServiceIdQuery query)
                {
                    capturedId = query.Id;
                }
            })
            .ReturnsAsync(new List<GetBusinessFunctionNameByBusinessServiceIdVm>());

        // Act
        await _controller.GetBusinessFunctionNamesByBusinessServiceId(businessServiceId);

        // Assert
        Assert.Equal(businessServiceId, capturedId);
    }
}