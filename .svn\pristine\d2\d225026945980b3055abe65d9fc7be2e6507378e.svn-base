using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class UserGroupFixture : IDisposable
{
    public List<UserGroup> UserGroupPaginationList { get; set; }
    public List<UserGroup> UserGroupList { get; set; }
    public UserGroup UserGroupDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public UserGroupFixture()
    {
        var fixture = new Fixture();

        UserGroupList = fixture.Create<List<UserGroup>>();

        UserGroupPaginationList = fixture.CreateMany<UserGroup>(20).ToList();

        UserGroupDto = fixture.Create<UserGroup>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
