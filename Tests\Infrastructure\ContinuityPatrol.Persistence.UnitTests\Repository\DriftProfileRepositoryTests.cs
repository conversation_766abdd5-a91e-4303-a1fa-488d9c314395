using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DriftProfileRepositoryTests : IClassFixture<DriftProfileFixture>
{
    private readonly DriftProfileFixture _driftProfileFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DriftProfileRepository _repository;
    private readonly DriftProfileRepository _repositoryNotParent;

    public DriftProfileRepositoryTests(DriftProfileFixture driftProfileFixture)
    {
        _driftProfileFixture = driftProfileFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DriftProfileRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new DriftProfileRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var driftProfile = _driftProfileFixture.DriftProfileDto;

        // Act
        var result = await _repository.AddAsync(driftProfile);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftProfile.Name, result.Name);
        Assert.Single(_dbContext.DriftProfiles);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var driftProfile = _driftProfileFixture.DriftProfileDto;
        await _repository.AddAsync(driftProfile);

        driftProfile.Name = "UpdatedDriftProfileName";

        // Act
        var result = await _repository.UpdateAsync(driftProfile);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedDriftProfileName", result.Name);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var driftProfile = _driftProfileFixture.DriftProfileDto;
        await _repository.AddAsync(driftProfile);

        // Act
        var result = await _repository.DeleteAsync(driftProfile);

        // Assert
        Assert.Equal(driftProfile.Name, result.Name);
        Assert.Empty(_dbContext.DriftProfiles);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var driftProfile = _driftProfileFixture.DriftProfileDto;
        var addedEntity = await _repository.AddAsync(driftProfile);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var driftProfile = _driftProfileFixture.DriftProfileDto;
        var addedEntity = await _repositoryNotParent.AddAsync(driftProfile);

        // Act
        var result = await _repositoryNotParent.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var driftProfile = _driftProfileFixture.DriftProfileDto;
        await _repository.AddAsync(driftProfile);

        // Act
        var result = await _repository.GetByReferenceIdAsync(driftProfile.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftProfile.ReferenceId, result.ReferenceId);
        Assert.Equal(driftProfile.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var driftProfiles = _driftProfileFixture.DriftProfileList;
        await _repository.AddRangeAsync(driftProfiles);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftProfiles.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var driftProfiles = _driftProfileFixture.DriftProfileList;
        await _repositoryNotParent.AddRangeAsync(driftProfiles);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var driftProfile = _driftProfileFixture.DriftProfileDto;
        await _repository.AddAsync(driftProfile);

        // Act
        var result = await _repository.IsNameExist(driftProfile.Name, "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Act
        var result = await _repository.IsNameExist("NonExistentDriftProfileName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var driftProfile = _driftProfileFixture.DriftProfileDto;
        await _repository.AddAsync(driftProfile);

        // Act
        var result = await _repository.IsNameExist(driftProfile.Name, driftProfile.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntity()
    {
        // Arrange
        var driftProfile1 = new DriftProfile
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingDriftProfileName",
            IsActive = true
        };
        await _repository.AddAsync(driftProfile1);

        var driftProfile2 = new DriftProfile
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "DifferentDriftProfileName",
            IsActive = true
        };
        await _repository.AddAsync(driftProfile2);

        // Act
        var result = await _repository.IsNameExist("ExistingDriftProfileName", driftProfile2.ReferenceId);

        // Assert
        Assert.True(result);
    }

    #endregion

    #region GetByReferenceIdsAsync Tests

    [Fact]
    public async Task GetByReferenceIdsAsync_ShouldReturnEntitiesWithMatchingIds()
    {
        // Arrange
        var driftProfiles = new List<DriftProfile>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "DriftProfile1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "DriftProfile2", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "DriftProfile3", IsActive = true }
        };
        await _repository.AddRangeAsync(driftProfiles);

        var ids = driftProfiles.Take(2).Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repository.GetByReferenceIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains(x.ReferenceId, ids));
    }

    [Fact]
    public async Task GetByReferenceIdsAsync_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var driftProfiles = new List<DriftProfile>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "DriftProfile1", IsActive = true }
        };
        await _repository.AddRangeAsync(driftProfiles);

        var ids = new List<string> { "non-existent-id-1", "non-existent-id-2" };

        // Act
        var result = await _repository.GetByReferenceIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByReferenceIdsAsync_ShouldReturnEmptyList_WhenIdsListIsEmpty()
    {
        // Arrange
        var driftProfiles = new List<DriftProfile>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "DriftProfile1", IsActive = true }
        };
        await _repository.AddRangeAsync(driftProfiles);

        var ids = new List<string>();

        // Act
        var result = await _repository.GetByReferenceIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByReferenceIdsAsync_ShouldHandlePartialMatches()
    {
        // Arrange
        var driftProfiles = new List<DriftProfile>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "DriftProfile1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "DriftProfile2", IsActive = true }
        };
        await _repository.AddRangeAsync(driftProfiles);

        var ids = new List<string> 
        { 
            driftProfiles.First().ReferenceId, 
            "non-existent-id" 
        };

        // Act
        var result = await _repository.GetByReferenceIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(driftProfiles.First().ReferenceId, result.First().ReferenceId);
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var driftProfiles = new List<DriftProfile>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "DriftProfile1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "DriftProfile2", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "DriftProfile3", IsActive = true }
        };
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(driftProfiles);
        var initialCount = driftProfiles.Count;
        
        var toUpdate = driftProfiles.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedDriftProfileName");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = driftProfiles.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.Name == "UpdatedDriftProfileName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
