using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class CyberAirGapStatusRepositoryTests : IClassFixture<CyberAirGapStatusFixture>
{
    private readonly CyberAirGapStatusFixture _cyberAirGapStatusFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CyberAirGapStatusRepository _repository;

    public CyberAirGapStatusRepositoryTests(CyberAirGapStatusFixture cyberAirGapStatusFixture)
    {
        _cyberAirGapStatusFixture = cyberAirGapStatusFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CyberAirGapStatusRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region GetAirGapList Tests

    [Fact]
    public async Task GetAirGapList_ShouldReturnStatusInDateRange()
    {
        // Arrange
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = baseDate.ToString("yyyy-MM-dd");

        var statuses = new List<CyberAirGapStatus>
        {
            new CyberAirGapStatus 
            { 
                AirGapId = "AIRGAP_001",
                AirGapName = "AirGap1",
                Status = "Open",
                CreatedDate = baseDate.AddDays(-3),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGapStatus 
            { 
                AirGapId = "AIRGAP_002",
                AirGapName = "AirGap2",
                Status = "Close",
                CreatedDate = baseDate.AddDays(-1),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGapStatus 
            { 
                AirGapId = "AIRGAP_003",
                AirGapName = "AirGap3",
                Status = "Open",
              
                CreatedDate = baseDate.AddDays(-15), // Outside date range
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        _dbContext.CyberAirGapStatus.AddRange(statuses);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetAirGapList(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= DateTime.Parse(startDate)));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date <= DateTime.Parse(endDate)));
    }

    [Fact]
    public async Task GetAirGapList_ShouldReturnEmpty_WhenNoStatusInDateRange()
    {
        // Arrange
        var statuses = _cyberAirGapStatusFixture.CyberAirGapStatusList;
        await _repository.AddRangeAsync(statuses);

        var futureStartDate = DateTime.Now.AddDays(10).ToString("yyyy-MM-dd");
        var futureEndDate = DateTime.Now.AddDays(15).ToString("yyyy-MM-dd");

        // Act
        var result = await _repository.GetAirGapList(futureStartDate, futureEndDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var cyberAirGapStatus = _cyberAirGapStatusFixture.CyberAirGapStatusDto;
        cyberAirGapStatus.AirGapName = "ExistingName";

        await _dbContext.CyberAirGapStatus.AddAsync(cyberAirGapStatus);
        await _dbContext.SaveChangesAsync();

      
        // Act
        var result = await _repository.IsNameExist("ExistingName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var statuses = _cyberAirGapStatusFixture.CyberAirGapStatusList;
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.IsNameExist("NonExistentName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var cyberAirGapStatus = _cyberAirGapStatusFixture.CyberAirGapStatusDto;
        cyberAirGapStatus.AirGapName = "SameName";
        await _dbContext.CyberAirGapStatus.AddAsync(cyberAirGapStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("SameName", cyberAirGapStatus.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion


    #region Infrastructure Assignment Tests

    [Fact]
    public async Task Repository_ShouldHandleAirGapStatusAssignments()
    {
        // Arrange
        var statuses = new List<CyberAirGapStatus>
        {
            new CyberAirGapStatus 
            { 
                AirGapId = "360ca7d6-6903-435a-9ce3-e9f867facd19",
                AirGapName = "DatabaseAirGapStatus",
                Status = "Open",
                CreatedDate = DateTime.Now.AddDays(-1),
                ReferenceId = Guid.NewGuid().ToString(),

                IsActive = true
            },
            new CyberAirGapStatus 
            { 
                AirGapId = "360ca7d6-6903-435a-9ce3-e9f867facd14",
                AirGapName = "WebAirGapStatus",
                Status = "Open",
                CreatedDate = DateTime.Now.AddDays(-1),
                ReferenceId = Guid.NewGuid().ToString(),

                IsActive = true
            },
            new CyberAirGapStatus 
            { 
                AirGapId = "360ca7d6-6903-435a-9ce3-e9f867facd15",
                AirGapName = "ApplicationAirGapStatus",
                Status = "close",
                CreatedDate = DateTime.Now.AddDays(-1),
                ReferenceId = Guid.NewGuid().ToString(),

                IsActive = true
            }
        };

        await _repository.AddRangeAsync(statuses);

        // Act
        var byAirGapdtl = await _repository.FindByFilterAsync(x => x.AirGapId.Contains("360ca7d6-6903-435a-9ce3-e9f867facd14"));
        var airgapstatuses = await _repository.FindByFilterAsync(x => x.AirGapName.Contains("DatabaseAirGapStatus"));
      

        // Assert
        Assert.Single(byAirGapdtl);
        Assert.Single(airgapstatuses);
       
        Assert.Equal("Open", byAirGapdtl.First().Status);
        Assert.Equal("Open", airgapstatuses.First().Status);
      
        Assert.True(byAirGapdtl.First().IsActive);
        Assert.True(airgapstatuses.First().IsActive);
       
    }

    #endregion

    #region Status Code and Status Tests

    [Fact]
    public async Task Repository_ShouldFilterByStatusCode()
    {
        // Arrange
        var statuses = new List<CyberAirGapStatus>
        {
            new CyberAirGapStatus 
            { 
                AirGapId = "AIRGAP_001",
                AirGapName = "AirGap1",
                Status = "Opens",
                CreatedDate = DateTime.Now,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGapStatus 
            { 
                AirGapId = "AIRGAP_002",
                AirGapName = "AirGap2",
                Status = "Open",
                CreatedDate = DateTime.Now,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGapStatus 
            { 
                AirGapId = "AIRGAP_003",
                AirGapName = "AirGap3",
                Status = "Close",
                CreatedDate = DateTime.Now,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(statuses);

        // Act

        var openStatus = await _repository.FindByFilterAsync(x => x.Status == "Open");
        var closetatuses = await _repository.FindByFilterAsync(x => x.Status == "Close");

        // Assert
        Assert.Single(openStatus);
        Assert.Single(closetatuses);
  
        Assert.Equal("Close", closetatuses.First().Status);
        Assert.Equal("Open", openStatus.First().Status);
    }

    #endregion
}
