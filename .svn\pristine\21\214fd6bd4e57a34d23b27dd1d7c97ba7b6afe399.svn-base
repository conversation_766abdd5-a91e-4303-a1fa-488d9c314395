using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Delete;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetList;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AdPasswordJobModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class AdPasswordJobService : BaseService,IAdPasswordJobService
{
    public AdPasswordJobService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<AdPasswordJobListVm>> GetAdPasswordJobList()
    {
        Logger.LogInformation("Get All AdPasswordJobs");

        return await Mediator.Send(new GetAdPasswordJobListQuery());
    }

    public async Task<AdPasswordJobDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "AdPasswordJob Id");

        Logger.LogInformation($"Get AdPasswordJob Detail by Id '{id}'");

        return await Mediator.Send(new GetAdPasswordJobDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateAdPasswordJobCommand createAdPasswordJobCommand)
    {
        Logger.LogInformation($"Create AdPasswordJob '{createAdPasswordJobCommand}'");

        return await Mediator.Send(createAdPasswordJobCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateAdPasswordJobCommand updateAdPasswordJobCommand)
    {
        Logger.LogInformation($"Update AdPasswordJob '{updateAdPasswordJobCommand}'");

        return await Mediator.Send(updateAdPasswordJobCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "AdPasswordJob Id");

        Logger.LogInformation($"Delete AdPasswordJob Details by Id '{id}'");

        return await Mediator.Send(new DeleteAdPasswordJobCommand { Id = id });
    }
     #region NameExist
 public async Task<bool> IsAdPasswordJobNameExist(string name, string? id)
 {
     Guard.Against.NullOrWhiteSpace(name, "AdPasswordJob Name");

     Logger.LogInformation($"Check Name Exists Detail by AdPasswordJob Name '{name}' and Id '{id}'");

     return await Mediator.Send(new GetAdPasswordJobNameUniqueQuery { Name = name, Id = id });
 }
    #endregion

     #region Paginated
public async Task<PaginatedResult<AdPasswordJobListVm>> GetPaginatedAdPasswordJobs(GetAdPasswordJobPaginatedListQuery query)
{
    Logger.LogInformation("Get Searching Details in AdPasswordJob Paginated List");

    return await Mediator.Send(query);
}
     #endregion
}
