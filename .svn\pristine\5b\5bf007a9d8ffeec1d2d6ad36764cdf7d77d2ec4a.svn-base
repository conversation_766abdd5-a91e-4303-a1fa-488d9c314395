﻿using ContinuityPatrol.Application.Features.RpForVmMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.RpForVmMonitorStatus.Queries.GetSymmetrix;

namespace ContinuityPatrol.Shared.Services.Contract;

public  interface IRpForVmMonitorStatusService
{
    Task<RpForVmMonitorStatusDetailByInfraObjectIdAndGroupNameVm> GetRpForVmMonitorStatusDetailByInfraObjectIdAndConsistencyGroupName(string infraObjectId, string consistancyGroupName);

    Task<SymmetrixListVm> GetSymmetrix(GetSymmetrixListQuery query);
}
