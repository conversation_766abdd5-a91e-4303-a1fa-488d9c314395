﻿
function monitorTypeOdg(value, infraObjectName, moniterType, parsedData) {    
    /* var drStatus = value?.drServerStatus*/
    
    let monitor = value?.monitorServiceDetails;

    const getDRDetails = (data, value, obj = null) => {

        let tdHtml = '';
        data.forEach((item, i) => {
            let iconClass = getIconClass(value, item);
            let tableData = obj ? item?.MonitoringModel[obj][value] : item?.MonitoringModel[value];

            tdHtml += `<td class="text-truncate"><i class="${iconClass} me-1"></i>${tableData || 'NA'}</td>`
        })
        return tdHtml
    }
    const getIconClass = (value, monitoringData) => {
        let iconClass = '';        
        if (value == 'Server_Name') {
            iconClass = 'cp-stand-server text-primary'

        } else if (value === 'Server_IpAddress' || value == 'Server_HostName') {
            let text = monitoringData?.MonitoringModel?.Server_Status?.toLowerCase()
            iconClass = text === 'down' ? "cp-down-linearrow me-1 text-danger" : text === 'pending' ? 'cp-pending me-1 text-warning' : text === 'up' ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";

        } else if (value === 'Database_Sid') {

            iconClass = monitoringData?.MonitoringModel?.Database_Sid ? 'cp-database me-1 text-primary' : "cp-disable me-1 text-danger";

        } else if (value === 'Replication_Mode') {
            iconClass = monitoringData?.MonitoringModel.Replication_Mode?.includes('NA') ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.Replication_Mode ? "cp-replication-on me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'Log_sequence') {
            iconClass = monitoringData?.MonitoringModel?.Log_sequence?.includes('NA') ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.Log_sequence ? "cp-connected me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'Services') {
            iconClass = monitoringData?.MonitoringModel?.Services?.includes('NA') ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.Services ? "cp-service me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'Protection_mode') {
            iconClass = monitoringData?.MonitoringModel?.Protection_mode?.includes('NA') ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.Protection_mode ? "cp-protection-mode me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'Dataguard_status') {
            iconClass = monitoringData?.MonitoringModel?.Dataguard_status?.includes('NA') ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.Dataguard_status ? "cp-dataguard-status me-1 text-primary" : "cp-disable me-1 text-danger";

        }
     
        return iconClass;
    }
    const getDynamicHeader = (OracleDataGuardModels) => {

        let dynamicHeader = '';

        OracleDataGuardModels?.length && OracleDataGuardModels?.map((data) => {
            dynamicHeader += `<th>${data?.Type}</th>`
        })

        return dynamicHeader;
    }
    if (moniterType === "Oracle") {
        let ipOrHostName;
        let repType = value?.replicationType ? 'cp-replication-type me-1 text-primary' : 'cp-disable me-1 text-danger'
        let rep = value?.replicationType !== null && value?.replicationType !== "" ? value?.replicationType : 'NA'
        let replicationstatus = value.drOperationStatus
        let pripaddress = value?.prServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : value?.prServerStatus?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : value?.prServerStatus?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";
        let dripaddress = value?.drServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : value?.drServerStatus?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : value?.drServerStatus?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";
        let prdatabase = parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Database_Sid ? "cp-database me-1 text-primary" : "cp-disable me-1 text-danger";
     
        let prgenerated = parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Log_sequence ? "cp-log-archive-config me-1 text-primary" : "cp-disable me-1 text-danger";
        let application = value?.monitorServiceDetails[0]?.isServiceUpdate?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.isServiceUpdate?.toLowerCase()?.includes("running") ? "text-success cp-reload cp-animate" : "text-danger cp-fail-back";
        let workflow = value?.monitorServiceDetails[0]?.workflowName?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.workflowName ? "cp-log-archive-config me-1 text-primary" : "cp-disable me-1 text-danger";
        
        let prreplicatype = value?.replicationType?.includes("NA") ? "cp-disable me-1 text-danger" : value?.replicationType ? "cp-replication-type me-1 text-primary" : "cp-disable me-1 text-danger";
        let prreplicamode = parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Replication_Mode?.includes('NA') ? "cp-replication-on me-1 text-primary" : parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Replication_Mode ? "cp-replication-on me-1 text-primary" : "cp-disable me-1 text-danger";

        let prservicename = parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Service_Name?.includes('NA')? "cp-disable me-1 text-danger" : parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Service_Name ? "cp-service me-1 text-primary" : "cp-disable me-1 text-danger";

        let prprotection = parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Protection_mode?.includes('NA') ? "cp-disable me-1 text-danger" : parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Protection_mode ? "cp-protection-mode me-1 text-primary" : "cp-disable me-1 text-danger";

        let prdataguard = parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Dataguard_status?.includes('NA') ? "cp-disable me-1 text-danger" : parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Dataguard_status ? "cp-dataguard-status me-1 text-primary" : "cp-disable me-1 text-danger";
        let ipprdata = parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.Pr_ConnectViaHostName.toLowerCase() === "true" ? parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Server_HostName : parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Server_IpAddress
        let drdata = parsedData?.OracleDataGuardModels.map((ip) => ip?.MonitoringModel?.connectViaHostName);
        parsedData?.OracleDataGuardModels.forEach((ip, index) => {

            let isHostName = drdata[index]?.toLowerCase() === "true";
            value = isHostName ? 'Server_HostName' : 'Server_IpAddress';
            ipOrHostName = isHostName
                ? getDRDetails(parsedData?.OracleDataGuardModels, 'Server_HostName')
                : getDRDetails(parsedData?.OracleDataGuardModels, 'Server_IpAddress');
        });

        let infraobjectdata =
            '<div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">' +
            ' <table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
            '<thead style="position: sticky;top: 0px;">' +
            '<tr>' +
            '<th>Component Monitor</th>' +
            ' <th>Production Server</th>' +
            `${getDynamicHeader(parsedData?.OracleDataGuardModels)}` +
            '</tr>' +
            '</thead>' +
            '<tbody style="" id="monitorwork">' +
            '<tr>' +
            '<td>' + 'Server Name' + '</td>' +
            '<td>' + '<i class="cp-stand-server me-1 text-primary"></i>' + (parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Server_Name !== undefined && parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Server_Name !== null && parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Server_Name !== "" ? parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Server_Name : "NA") + '</td>' +
            `${getDRDetails(parsedData?.OracleDataGuardModels, 'Server_Name')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + 'IP Address/Host Name' + '</td>' +
            '<td>' + '<i class="' + pripaddress + '"></i>' + (ipprdata || "NA") + '</td>' +
            `${ipOrHostName}` +
            '</tr>' +
            '<tr>' +
            '<td>' + 'Database Name' + '</td>' +
            '<td>' + '<i class="' + prdatabase + '"></i>' + (parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Database_Sid !== null && parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Database_Sid !== "" ? parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Database_Sid : "NA") + '</td>' +
            `${getDRDetails(parsedData?.OracleDataGuardModels, 'Database_Sid')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + 'Last Generated Archive Log' + '</td>' +
            '<td>' + '<i class="' + prgenerated + '"></i>' + (parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Log_sequence !== null && parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Log_sequence !== "" ? parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Log_sequence : "NA") + '</td>' +
            '<td>' + '--' +
            '</tr>' +
            '<tr>' +
            '<td>' + 'Last Applied Archive Log' + '</td>' +
            '<td>' + '--' +
            `${getDRDetails(parsedData?.OracleDataGuardModels, 'Log_sequence')}` +
            '</tr>';
       
        infraobjectdata += generateMonitorServiceDetailsRow(workflow, application, monitor);

        infraobjectdata += '</tbody>' +
            '</table>' +
            '</div>' +

            '<div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
            '<thead style="position: sticky;top: 0px;z-index: 1;">' +
            '<tr>' +
            '<th>Replication Monitor</th>' +
            '<th>Production Server</th>' +
            `${getDynamicHeader(parsedData?.OracleDataGuardModels)}` +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td>' + 'Replication Type' + '</td>' +
            '<td>' + '<i class="' + repType + '"></i>' + rep + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + "Replication Mode" + '</td>' +
            '<td>' + '<i class="' + prreplicamode + '"></i>' + (parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Replication_Mode !== null && parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Replication_Mode !== "" ? parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Replication_Mode : "NA") + '</td>' +
            `${getDRDetails(parsedData?.OracleDataGuardModels, 'Replication_Mode')}` + 
            '</tr>' +
            '<tr>' +

            '<td>' + 'Service Name' + '</td>' +
            '<td>' + '<i class="' + prservicename + '"></i>' + (parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Services !== null && parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Services !== "" ? parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Services : "NA") + '</td>' +
            `${getDRDetails(parsedData?.OracleDataGuardModels, 'Services')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + 'Protection Mode' + '</td>' +
            '<td>' + '<i class="' + prprotection + '"></i>' + (parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Protection_mode !== null && parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Protection_mode !== "" ? parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Protection_mode : "NA") + '</td>' +
            `${getDRDetails(parsedData?.OracleDataGuardModels, 'Protection_mode')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + 'DataGuard Status' + '</td>' +
            '<td>' + '<i class="' + prdataguard + '"></i>' + (parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Dataguard_status !== null && parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Dataguard_status !== "" ? parsedData?.PrOracleDataGuardModel?.PrMonitoringModel?.PR_Dataguard_status : "NA") + '</td>' +
            `${getDRDetails(parsedData?.OracleDataGuardModels, 'Dataguard_status')}` +
            '</tr>' +
            '</tbody>' +
            '</table>' +
            '</div>';


        setTimeout(() => {
            $("#infraobjectalldata").html(infraobjectdata);
        }, 200)


    }
}