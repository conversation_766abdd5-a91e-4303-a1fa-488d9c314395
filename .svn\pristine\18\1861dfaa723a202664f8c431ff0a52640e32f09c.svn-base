﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()
<link href="~/lib/jquery.steps/jquery.steps.css" rel="stylesheet" />
<link href="~/css/wizard.css" rel="stylesheet" />
<link href="~/css/workflowconfiguration.css" rel="stylesheet" />

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-component-list"></i><span>Component List</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="name=" id="cyberComponentNameSearch">
                                        <label class="form-check-label" for="Name">
                                            Component Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="site=" id="SiteNameSearch">
                                        <label class="form-check-label" for="site">
                                            Site
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="serverType=" id="ServerRoleNameSearch">
                                        <label class="form-check-label" for="serverType">
                                            Server Role
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <button type="button" class="btn btn-primary btn-sm " id="btnCyberComponentCreate"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">

            <table class="datatable table table-hover dataTable no-footer" id="tblCyberComponents" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Component Name</th>
                        <th>Site</th>
                        <th>Server Role</th>
                        <th class="Action-th">Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>


<div class="modal fade" id="cyberComponentCreateModal" tabindex="-1" aria-labelledby="CreateModal" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-lg  modal-dialog-scrollable modal-dialog-centered ">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title">
                    <i class="cp-component-list"></i><span>
                        Component Configuration
                    </span>
                </h6>
                <button type="button" class="btn-close overall_cancel" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="componentID" />
                <div class="form-group">
                    <label class="form-label">Site Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-web"></i></span>
                        <select class="form-select-modal" data-placeholder="Select Site Name" id="cyberSiteCont">
                        </select>
                    </div>
                    <span id="componentZoneError"></span>
                </div>
                <div class="form-group">
                    <label class="form-label">Component Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-name"></i></span>
                        <input type="text" maxlength="100" class="form-control" placeholder="Enter Component Name" autocomplete="off" id="componentName" />
                        <span class="input-group-text changeComponentIcon" data-bs-toggle="collapse" href="#collapseIcon" aria-expanded="false" title="Select Icon" aria-controls="collapseIcon"><i role="button" class="imageSubSelected cp-images" id="imageSubSelected"></i></span>
                    </div>
                    <span id="componentNameError"></span>
                </div>
                <div class="collapse mb-2" id="collapseIcon">
                    <div class="form-label">Component Icon</div>
                    <div class="Category_Icon">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <td class="componentIconData"><img src="~/img/component_icons/application.svg" width="30" height="30" /></td>
                                    <td class="componentIconData"><img src="~/img/component_icons/database.svg" width="30" height="30" /></td>
                                    <td class="componentIconData"><img src="~/img/component_icons/cp.svg" width="30" height="30" /></td>
                                    <td class="componentIconData"><img src="~/img/component_icons/esxi_server_icon.svg" width="30" height="30" /></td>
                                    <td class="componentIconData"><img src="~/img/component_icons/storage.svg" width="30" height="30" /></td>
                                    <td class="componentIconData"><img src="~/img/component_icons/vSphere.svg" width="30" height="30" /></td>
                                    <td class="componentIconData"><img src="~/img/component_icons/Switche.svg" width="30" height="30" /></td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Server Role</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-server-role"></i></span>
                        <select class="form-select-modal" data-placeholder="Select Server Role" id="componetServerRole">
                        </select>
                    </div>
                    <span id="componentServerRoleError"></span>
                </div>
                <div class="form-group">
                    <label class="form-label">Servers</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-server"></i></span>
                        <select class="form-select-modal" data-placeholder="Select Servers" id="componentServerList" multiple>
                        </select>
                    </div>
                    <span id="componentServerListError"></span>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm btn-cancel" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm" id="btnSaveCyberComponent">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="cyberComponentDeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <form class="w-100">
            <div class="modal-content">

                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body   text-center pt-0">
                    <h4>Are you sure?</h4>
                    <p>You want to delete <span class="font-weight-bolder text-primary" id="deleteData"></span> data?</p>
                    <input id="cyberComponentId" name="id" class="form-control d-none" />
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="button" class="btn btn-primary btn-sm" id="btnDeleteCyberComponent">Yes</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script src="~/js/wizard.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.js"></script>
<script src="~/js/CyberResiliency/Configuration/Component/Components.js">    </script>
@section Scripts
{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}