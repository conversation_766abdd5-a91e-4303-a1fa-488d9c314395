﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using System.Linq.Expressions;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class AlertMasterRepositoryTests : IClassFixture<AlertMasterFixture>
{
    private readonly AlertMasterFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly Mock<ILoggedInUserService> _userServiceMock;
    private readonly AlertMasterRepository _repository;

    public AlertMasterRepositoryTests(AlertMasterFixture fixture)
    {
        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _userServiceMock = new Mock<ILoggedInUserService>();
        _userServiceMock.Setup(x => x.CompanyId).Returns("COMPANY_1");
        _userServiceMock.Setup(x => x.IsParent).Returns(true);
        _repository = new AlertMasterRepository(_dbContext, _userServiceMock.Object);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsAll_WhenIsParent()
    {
        var alerts = _fixture.AlertMasterList;
        foreach (var alert in alerts) alert.IsActive = true;
        await _dbContext.AlertMasters.AddRangeAsync(alerts);
        await _dbContext.SaveChangesAsync();

        _userServiceMock.Setup(x => x.IsParent).Returns(true);

        var result = await _repository.ListAllAsync();

        Assert.NotNull(result);
        Assert.Equal(alerts.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsFiltered_WhenNotParent()
    {
        var alerts = _fixture.AlertMasterList;
        foreach (var alert in alerts)
        {
            alert.IsActive = true;
            alert.AlertId = "020041";
        }
        await _dbContext.AlertMasters.AddRangeAsync(alerts);
        await _dbContext.SaveChangesAsync();

        _userServiceMock.Setup(x => x.IsParent).Returns(false);

        var result = await _repository.ListAllAsync();

        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal("020041", x.AlertId));
    }

    [Fact]
    public async Task GetAlertMasterByAlertId_ReturnsList_WhenExists()
    {
        var alert = _fixture.AlertMasterDto;
        alert.AlertId = "020041";
        alert.IsActive = true;
        _dbContext.AlertMasters.Add(alert);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetAlertMasterByAlertId("020041");

        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("020041", result[0].AlertId);
    }

    [Fact]
    public async Task GetAlertMasterByAlertId_ReturnsEmpty_WhenNotExists()
    {
        var result = await _repository.GetAlertMasterByAlertId("NON_EXISTENT");
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetAlertMasterByAlertName_ReturnsList_WhenExists()
    {
        var alert = _fixture.AlertMasterDto;
        alert.AlertName = "TestAlert";
        alert.IsActive = true;
        _dbContext.AlertMasters.Add(alert);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetAlertMasterByAlertName("TestAlert");

        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("TestAlert", result[0].AlertName);
    }

    [Fact]
    public async Task GetAlertMasterByAlertName_ReturnsEmpty_WhenNotExists()
    {
        var result = await _repository.GetAlertMasterByAlertName("NON_EXISTENT");
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetAlertMasterNames_ReturnsNames_WhenIsParent()
    {
        var alerts = _fixture.AlertMasterList;
        foreach (var alert in alerts) alert.IsActive = true;
        await _dbContext.AlertMasters.AddRangeAsync(alerts);
        await _dbContext.SaveChangesAsync();

        _userServiceMock.Setup(x => x.IsParent).Returns(true);

        var result = await _repository.GetAlertMasterNames();

        Assert.NotNull(result);
        Assert.Equal(alerts.Count, result.Count);
        Assert.All(result, x => Assert.False(string.IsNullOrEmpty(x.AlertMessage)));
    }

    [Fact]
    public async Task GetAlertMasterNames_ReturnsFilteredNames_WhenNotParent()
    {
        var alerts = _fixture.AlertMasterList;
        foreach (var alert in alerts)
        {
            alert.IsActive = true;
            alert.AlertId = "020041";
        }
        await _dbContext.AlertMasters.AddRangeAsync(alerts);
        await _dbContext.SaveChangesAsync();

        _userServiceMock.Setup(x => x.IsParent).Returns(false);

        var result = await _repository.GetAlertMasterNames();

        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal("020041", x.AlertId));
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsEntity_WhenIsParent()
    {
        var alert = _fixture.AlertMasterDto;
        alert.ReferenceId = "228f7412-bbaa-4a5e-a65d-ffa79dafa5d3";
        alert.IsActive = true;
        _dbContext.AlertMasters.Add(alert);
        await _dbContext.SaveChangesAsync();

        _userServiceMock.Setup(x => x.IsParent).Returns(true);

        var result = await _repository.GetByReferenceIdAsync("228f7412-bbaa-4a5e-a65d-ffa79dafa5d3");

        Assert.NotNull(result);
        Assert.Equal("228f7412-bbaa-4a5e-a65d-ffa79dafa5d3", result.ReferenceId);
    }
    [Fact]
    public async Task GetByReferenceIdAsync_Throw_InvalidArgument_Exception()
    {
        var alert = _fixture.AlertMasterDto;
        alert.ReferenceId = "REF_1";
        alert.IsActive = true;
        _dbContext.AlertMasters.Add(alert);
        await _dbContext.SaveChangesAsync();

        _userServiceMock.Setup(x => x.IsParent).Returns(true);

        await Assert.ThrowsAsync<InvalidArgumentException>(()=>_repository.GetByReferenceIdAsync("REF_1"));
  
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsEntity_WhenNotParentAndCompanyMatches()
    {
        var alert = _fixture.AlertMasterDto;
        alert.ReferenceId = "228f7412-bbaa-4a5e-a65d-ffa79dafa5d3";
        alert.AlertId = "COMPANY_1";
        alert.IsActive = true;
        _dbContext.AlertMasters.Add(alert);
        await _dbContext.SaveChangesAsync();

        _userServiceMock.Setup(x => x.IsParent).Returns(false);

        var result = await _repository.GetByReferenceIdAsync("228f7412-bbaa-4a5e-a65d-ffa79dafa5d3");

        Assert.NotNull(result);
        Assert.Equal("228f7412-bbaa-4a5e-a65d-ffa79dafa5d3", result.ReferenceId);
        Assert.Equal("COMPANY_1", result.AlertId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenNotParentAndCompanyDoesNotMatch()
    {
        var alert = _fixture.AlertMasterDto;
        alert.ReferenceId = "228f7412-bbaa-4a5e-a65d-ffa79dafa5d3";
        alert.AlertId = "OTHER_COMPANY";
        alert.IsActive = true;
        _dbContext.AlertMasters.Add(alert);
        await _dbContext.SaveChangesAsync();

        _userServiceMock.Setup(x => x.IsParent).Returns(false);

        var result = await _repository.GetByReferenceIdAsync("228f7412-bbaa-4a5e-a65d-ffa79dafa5d3");

        Assert.Null(result);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsPaginatedResult()
    {
        var alerts = _fixture.AlertMasterList;
        foreach (var alert in alerts) alert.IsActive = true;
        await _dbContext.AlertMasters.AddRangeAsync(alerts);
        await _dbContext.SaveChangesAsync();

        Specification<AlertMaster>? spec =null;

        Expression<Func<AlertMaster, bool>> expr = x => x.IsActive;

        var result = await _repository.PaginatedListAllAsync(1, 5, spec, expr, "Id", "asc");

        Assert.NotNull(result);
        Assert.True(result.Data.Count <= 5);
        Assert.True(result.Data.All(x => x.IsActive));
    }

    [Fact]
    public void PaginatedListAllAsync_IQueryable_ReturnsAll_WhenIsParent()
    {
        var alerts = _fixture.AlertMasterList;
        foreach (var alert in alerts) alert.IsActive = true;
        _dbContext.AlertMasters.AddRangeAsync(alerts);
        _dbContext.SaveChanges();

        _userServiceMock.Setup(x => x.IsParent).Returns(true);

        var result = _repository.GetPaginatedQuery().ToList();

        Assert.NotNull(result);
        Assert.Equal(alerts.Count, result.Count);
    }

    [Fact]
    public void PaginatedListAllAsync_IQueryable_ReturnsFiltered_WhenNotParent()
    {
        var alerts = _fixture.AlertMasterList;
        foreach (var alert in alerts)
        {
            alert.IsActive = true;
            alert.AlertId = "COMPANY_1";
        }
        _dbContext.AlertMasters.AddRangeAsync(alerts);
        _dbContext.SaveChanges();

        _userServiceMock.Setup(x => x.IsParent).Returns(false);

        var result = _repository.GetPaginatedQuery().ToList();

        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal("COMPANY_1", x.AlertId));
    }

    [Fact]
    public void GetPaginatedByAlertName_ReturnsFiltered()
    {
        var alert = _fixture.AlertMasterDto;
        alert.AlertName = "ALERT_NAME";
        alert.IsActive = true;
        _dbContext.AlertMasters.Add(alert);
        _dbContext.SaveChanges();

        var result = _repository.GetPaginatedByAlertName("ALERT_NAME").ToList();

        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("ALERT_NAME", result[0].AlertName);
    }

    [Fact]
    public void GetPaginatedByAlertPriority_ReturnsFiltered()
    {
        var alert = _fixture.AlertMasterDto;
        alert.AlertPriority = "HIGH";
        alert.IsActive = true;
        _dbContext.AlertMasters.Add(alert);
        _dbContext.SaveChanges();

        var result = _repository.GetPaginatedByAlertPriority("HIGH").ToList();

        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("HIGH", result[0].AlertPriority);
    }

    [Fact]
    public void GetPaginatedByAlertNameAndPriority_ReturnsFiltered()
    {
        var alert = _fixture.AlertMasterDto;
        alert.AlertName = "ALERT_NAME";
        alert.AlertPriority = "HIGH";
        alert.IsActive = true;
        _dbContext.AlertMasters.Add(alert);
        _dbContext.SaveChanges();

        var result = _repository.GetPaginatedByAlertNameAndPriority("ALERT_NAME", "HIGH").ToList();

        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("ALERT_NAME", result[0].AlertName);
        Assert.Equal("HIGH", result[0].AlertPriority);
    }

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists()
    {
        var alert = _fixture.AlertMasterDto;
        alert.AlertName = "EXISTING_NAME";
        alert.IsActive = true;
        _dbContext.AlertMasters.Add(alert);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsNameExist("EXISTING_NAME", "");

        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        var result = await _repository.IsNameExist("NON_EXISTENT_NAME", "");
        Assert.False(result);
    }

    [Fact]
    public async Task IsAlertIdExist_ReturnsTrue_WhenAlertIdExists()
    {
        var alert = _fixture.AlertMasterDto;
        alert.AlertId = "100002";
        alert.IsActive = true;
        _dbContext.AlertMasters.Add(alert);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsAlertIdExist("100002");

        Assert.True(result);
    }

    [Fact]
    public async Task IsAlertIdExist_ReturnsFalse_WhenAlertIdDoesNotExist()
    {
        var result = await _repository.IsAlertIdExist("000002");
        Assert.False(result);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoActiveEntities()
    {
        var alerts = _fixture.AlertMasterList;
        
        foreach (var alert in alerts)
        {
            alert.IsActive = false;
        }

        await _dbContext.AlertMasters.AddRangeAsync(alerts);

        _dbContext.SaveChanges(); 

        var result = await _repository.ListAllAsync();

        Assert.NotNull(result);
        Assert.Empty(result);
    }
 

    [Fact]
    public async Task GetAlertMasterByAlertId_ReturnEmptyRows()
    {
        var result = await _repository.GetAlertMasterByAlertId(null);
        Assert.Empty(result);
    }


   

    [Fact]
    public async Task GetByReferenceIdAsync_Throws_WhenReferenceIdIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.GetByReferenceIdAsync(null));
    }

    [Fact]
    public void GetPaginatedByAlertName_ReturnsEmpty_WhenNoMatch()
    {
        var result = _repository.GetPaginatedByAlertName("NO_SUCH_NAME").ToList();
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public void GetPaginatedByAlertPriority_ReturnsEmpty_WhenNoMatch()
    {
        var result = _repository.GetPaginatedByAlertPriority("NO_SUCH_PRIORITY").ToList();
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public void GetPaginatedByAlertNameAndPriority_ReturnsEmpty_WhenNoMatch()
    {
        var result = _repository.GetPaginatedByAlertNameAndPriority("NO_SUCH_NAME", "NO_SUCH_PRIORITY").ToList();
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenIdIsValidGuidAndNoMatch()
    {
        var result = await _repository.IsNameExist("NO_SUCH_NAME", Guid.NewGuid().ToString());
        Assert.False(result);
    }

    [Fact]
    public async Task IsAlertIdExist_ReturnsFalse_WhenIdIsValidGuid()
    {
        var result = await _repository.IsAlertIdExist(Guid.NewGuid().ToString());
        Assert.False(result);
    }

   

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsEmpty_WhenNoActiveEntities()
    {
        var alerts = _fixture.AlertMasterList;
        foreach (var alert in alerts) alert.IsActive = false;
        await _dbContext.AlertMasters.AddRangeAsync(alerts);
         _dbContext.SaveChanges();

        Specification<AlertMaster>? spec=null;
            Expression<Func<AlertMaster, bool>> expr = x => x.IsActive;
        var result = await _repository.PaginatedListAllAsync(1, 5, spec, expr, "Id", "asc");

        Assert.NotNull(result);
        Assert.Empty(result.Data);
        Assert.Equal(0, result.TotalCount);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsCorrectCount_ForGivenPageNumberAndPageSize()
    {

        var alerts = _fixture.AlertMasterPaginationList;
        foreach (var alert in alerts)
            alert.IsActive = true;
        await _dbContext.AlertMasters.AddRangeAsync(alerts);
        await _dbContext.SaveChangesAsync();

        var pageSize = 5;
        var pageNumber = 2; 

        Specification<AlertMaster>? spec = null;
        Expression<Func<AlertMaster, bool>> expr = x => x.IsActive;

        var result = await _repository.PaginatedListAllAsync(
            pageNumber,
            pageSize,
            spec,
            expr,
            sortColumn: "Id",
            sortOrder: "asc"
        );

        // Assert
        Assert.NotNull(result);
        Assert.Equal(alerts.Count, result.TotalCount);
        Assert.Equal(pageSize, result.Data.Count); 
        var expectedFirstId = alerts.OrderBy(x => x.Id).Skip(pageSize).First().Id;
        Assert.Equal(expectedFirstId, result.Data.First().Id);
    }

    [Fact]
    public async Task PaginatedListAllAsync_WithSearchString_ReturnsFilteredCount()
    {
        // Arrange
        var alerts = _fixture.AlertMasterPaginationList;
        foreach (var alert in alerts)
            alert.IsActive = true;
        alerts[0].AlertName = "SpecialSearch";
        await _dbContext.AlertMasters.AddRangeAsync(alerts);
        await _dbContext.SaveChangesAsync();

        var searchString = "SpecialSearch";
        var spec = new AlertMasterFilterSpecification(searchString);
        Expression<Func<AlertMaster, bool>> expr = x => x.IsActive;

        // Act
        var result = await _repository.PaginatedListAllAsync(
            pageNumber: 1,
            PageSize: 10,
            specification: spec,
            expression: expr,
            sortColumn: "Id",
            sortOrder: "asc"
        );

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.TotalCount);
        Assert.Single(result.Data);
        Assert.Equal("SpecialSearch", result.Data.First().AlertName);
    }

    [Fact]
    public async Task PaginatedListAllAsync_WithNullSearchString_ReturnsAllData()
    {
        // Arrange
        var alerts = _fixture.AlertMasterPaginationList;
        foreach (var alert in alerts)
            alert.IsActive = true;
        await _dbContext.AlertMasters.AddRangeAsync(alerts);
        await _dbContext.SaveChangesAsync();

        string? searchString = null;
        var spec = new AlertMasterFilterSpecification(searchString);
        Expression<Func<AlertMaster, bool>> expr = x => x.IsActive;

        // Act
        var result = await _repository.PaginatedListAllAsync(
            pageNumber: 1,
            PageSize: 20,
            specification: spec,
            expression: expr,
            sortColumn: "Id",
            sortOrder: "asc"
        );

        // Assert
        Assert.NotNull(result);
        Assert.Equal(alerts.Count, result.TotalCount);
        Assert.Equal(alerts.Count, result.Data.Count);
    }

}
