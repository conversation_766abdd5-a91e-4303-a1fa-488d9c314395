﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.AlertMasterModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using System.Linq.Expressions;

namespace ContinuityPatrol.Application.Features.AlertMaster.Queries.GetPaginatedList;

public class GetAlertMasterPaginatedListQueryHandler : IRequestHandler<GetAlertMasterPaginatedListQuery,
    PaginatedResult<AlertMasterListVm>>
{
    private readonly IAlertMasterRepository _alertMasterRepository;
    private readonly IMapper _mapper;

    public GetAlertMasterPaginatedListQueryHandler(IAlertMasterRepository alertMasterRepository, IMapper mapper)
    {
        _alertMasterRepository = alertMasterRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<AlertMasterListVm>> Handle(GetAlertMasterPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        //var productFilterSpec = new AlertMasterFilterSpecification(request.SearchString);
        //IQueryable<Domain.Entities.AlertMaster> queryable;

        //if (request.AlertName.IsNotNullOrWhiteSpace() && request.AlertPriority.IsNotNullOrWhiteSpace())
        //    queryable = _alertMasterRepository.GetPaginatedByAlertNameAndPriority(request.AlertName,
        //        request.AlertPriority);
        //else if (request.AlertName.IsNotNullOrWhiteSpace())
        //    queryable = _alertMasterRepository.GetPaginatedByAlertName(request.AlertName);
        //else if (request.AlertPriority.IsNotNullOrWhiteSpace())
        //    queryable = _alertMasterRepository.GetPaginatedByAlertPriority(request.AlertPriority);
        //else
        //    queryable = _alertMasterRepository.GetPaginatedQuery();


        //var reportsList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<AlertMasterListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return reportsList;

        var productFilterSpec = new AlertMasterFilterSpecification(request.SearchString);

        Expression<Func<Domain.Entities.AlertMaster, bool>> expression = BuildExpression(request);

        var  queryable=await _alertMasterRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, expression, request.SortColumn, request.SortOrder);

        var reportsList = _mapper.Map<PaginatedResult<AlertMasterListVm>>(queryable);

        return reportsList;
    }
    private static  Expression<Func<Domain.Entities.AlertMaster, bool>> BuildExpression(GetAlertMasterPaginatedListQuery request)
    {
        Expression<Func<Domain.Entities.AlertMaster, bool>> expression = x=>true;

        if (request.AlertName.IsNotNullOrWhiteSpace())
        {
            expression = expression.And(x => x.AlertName.Equals(request.AlertName));
        }

        if (request.AlertPriority.IsNotNullOrWhiteSpace())
        {
            expression = expression.And(x => x.AlertPriority.Equals(request.AlertPriority));
        }

        return expression;
    }
}