﻿using ContinuityPatrol.Application.Constants;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;

namespace ContinuityPatrol.Application.Features.Company.Queries.GetList;

public class GetCompanyListQueryHandler : IRequestHandler<GetCompanyListQuery, List<CompanyListVm>>
{
    private readonly ICompanyRepository _companyRepository;
    private readonly IMapper _mapper;

    public GetCompanyListQueryHandler(IMapper mapper, ICompanyRepository companyRepository)
    {
        _mapper = mapper;
        _companyRepository = companyRepository;
    }

    public async Task<List<CompanyListVm>> Handle(GetCompanyListQuery request, CancellationToken cancellationToken)
    {
        var allCompanies = await _companyRepository.ListAllAsync();

        if (allCompanies.Count <= 0)
            return new List<CompanyListVm>();

        // Create a dictionary for faster parent lookups
        var companyLookup = allCompanies.ToDictionary(c => c.ReferenceId, c => c.DisplayName);

        // Map all companies at once
        var companyVms = _mapper.Map<List<CompanyListVm>>(allCompanies);

        // Process parent names in a single pass
        foreach (var companyVm in companyVms)
        {
            if (companyVm.IsParent)
            {
                companyVm.ParentName = "NA";
            }
            else
            {
                Guard.Against.InvalidGuidOrEmpty(companyVm.ParentId, nameof(companyVm.ParentId),
                    ErrorMessage.Company.ParentCompanyIdCannotBeEmpty);

                companyVm.ParentName = companyLookup.TryGetValue(companyVm.ParentId, out var parentName)
                    ? parentName
                    : string.Empty;
            }
        }

        return companyVms;
    }
}