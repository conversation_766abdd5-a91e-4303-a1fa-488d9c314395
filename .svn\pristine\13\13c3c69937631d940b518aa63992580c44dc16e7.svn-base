﻿using ContinuityPatrol.Application.Features.InfraSummary.Commands.Create;
using ContinuityPatrol.Application.Features.InfraSummary.Commands.Delete;
using ContinuityPatrol.Application.Features.InfraSummary.Commands.Update;
using ContinuityPatrol.Application.Features.InfraSummary.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.InfraSummaryModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class InfraSummaryController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<InfraSummaryListVm>>> GetInfraSummaries()
    {
        Logger.LogDebug("Get All GetInfraSummaries");

        return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllDRReadyLogsCacheKey + LoggedInUserService.CompanyId, () => Mediator.Send(new GetInfraSummaryListQuery()), CacheExpiry));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Dashboard.Create)]
    public async Task<ActionResult<CreateInfraSummaryResponse>> CreateInfraSummary(
        [FromBody] CreateInfraSummaryCommand createInfraSummaryCommand)
    {
        Logger.LogDebug($"Create InfraSummary '{createInfraSummaryCommand.EntityName}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateInfraSummary), await Mediator.Send(createInfraSummaryCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Dashboard.Edit)]
    public async Task<ActionResult<UpdateInfraSummaryResponse>> UpdateInfraSummary(
        [FromBody] UpdateInfraSummaryCommand updateInfraSummaryCommand)
    {
        Logger.LogDebug($"Update InfraSummary '{updateInfraSummaryCommand.EntityName}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateInfraSummaryCommand));
    }

    [HttpDelete("{type}")]
    [Authorize(Policy = Permissions.Dashboard.Delete)]
    public async Task<ActionResult<DeleteInfraSummaryResponse>> DeleteInfraSummary(string type)
    {
        Logger.LogDebug($"Delete InfraSummary Details by Id '{type}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteInfraSummaryCommand { Type = type }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllDRReadyLogsCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}