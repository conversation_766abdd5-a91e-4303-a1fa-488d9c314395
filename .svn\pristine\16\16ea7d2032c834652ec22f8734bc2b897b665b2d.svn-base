﻿using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Update;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetNames;
using ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class InfraObjectSchedulerProfile : Profile
{
    public InfraObjectSchedulerProfile()
    {
        CreateMap<InfraObjectScheduler, CreateInfraObjectSchedulerCommand>().ReverseMap();
        CreateMap<CreateInfraObjectSchedulerCommand, InfraObjectSchedulerViewModel>().ReverseMap();
        CreateMap<UpdateInfraObjectSchedulerCommand, InfraObjectSchedulerViewModel>().ReverseMap();
        CreateMap<UpdateInfraObjectSchedulerCommand, InfraObjectScheduler>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<InfraObjectScheduler, InfraObjectSchedulerDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraObjectScheduler, InfraObjectSchedulerListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraObjectScheduler, InfraObjectSchedulerNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<PaginatedResult<InfraObjectScheduler>, PaginatedResult<InfraObjectSchedulerListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));

        CreateMap<InfraObjectSchedulerLogs, InfraObjectSchedulerLogsListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<PaginatedResult<InfraObjectSchedulerLogs>, PaginatedResult<InfraObjectSchedulerLogsListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}