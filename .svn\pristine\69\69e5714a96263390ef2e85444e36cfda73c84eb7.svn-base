﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class AlertMasterRepository : BaseRepository<AlertMaster>, IAlertMasterRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public AlertMasterRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override Task<IReadOnlyList<AlertMaster>> ListAllAsync()
    {
        return _loggedInUserService.IsParent
            ? base.ListAllAsync()
            : FindByFilter(alertMaster => alertMaster.AlertId.Equals(_loggedInUserService.CompanyId));
    }

    public Task<List<AlertMaster>> GetAlertMasterByAlertId(string alertId)
    {
        var matches = _dbContext.AlertMasters
            .Where(e => e.AlertId.Equals(alertId) && e.IsActive).ToList();

        return Task.FromResult(matches);
    }

    public Task<List<AlertMaster>> GetAlertMasterByAlertName(string alertName)
    {
        var match = _dbContext.AlertMasters
            .Active().Where(e => e.AlertName.Equals(alertName)).ToList();

        return Task.FromResult(match);
    }

    public Task<List<AlertMaster>> GetAlertMasterNames()
    {
        if (!_loggedInUserService.IsParent)
            return _dbContext.AlertMasters.Active()
                .Where(x => x.AlertId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new AlertMaster { ReferenceId = x.ReferenceId, AlertMessage = x.AlertMessage })
                .OrderBy(x => x.AlertMessage)
                .ToListAsync();
        return _dbContext.AlertMasters
            .Active()
            .Select(x => new AlertMaster { ReferenceId = x.ReferenceId, AlertMessage = x.AlertMessage })
            .OrderBy(x => x.AlertMessage)
            .ToListAsync();
    }

    public override Task<AlertMaster> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilter(alertMaster =>
                    alertMaster.ReferenceId.Equals(id) && alertMaster.AlertId.Equals(_loggedInUserService.CompanyId))
                .Result
                .SingleOrDefault());
    }

    public async Task<PaginatedResult<AlertMaster>> PaginatedListAllAsync(int pageNumber,int PageSize,Specification<AlertMaster> specification,Expression<Func<AlertMaster,bool>> expression,string sortColumn,string sortOrder)
    {
        return await Entities.Specify(specification).Where(expression).DescOrderById().ToSortedPaginatedListAsync(pageNumber, PageSize, sortColumn, sortOrder);

    }
    public override IQueryable<AlertMaster> GetPaginatedQuery()
    {
        if (_loggedInUserService.IsParent)
            return Entities.Where(x => x.IsActive)
                .AsNoTracking()
                .OrderByDescending(x => x.Id);

        return Entities.Where(x => x.IsActive && x.AlertId.Equals(_loggedInUserService.CompanyId))
            .AsNoTracking()
            .OrderByDescending(x => x.Id);
    }

    public IQueryable<AlertMaster> GetPaginatedByAlertName(string alertname)
    {
        var alertMaster = _dbContext.AlertMasters.Active().AsNoTracking()
            .Where(x => x.AlertName.Equals(alertname)).OrderByDescending(x => x.Id);

        return alertMaster;
    }

    public IQueryable<AlertMaster> GetPaginatedByAlertPriority(string alertPriority)
    {
        var alertMaster = _dbContext.AlertMasters.Active().AsNoTracking()
            .Where(x => x.AlertPriority.Equals(alertPriority)).OrderByDescending(x => x.Id);

        return alertMaster;
    }

    public IQueryable<AlertMaster> GetPaginatedByAlertNameAndPriority(string alertName, string alertPriority)
    {
        var alertMaster = _dbContext.AlertMasters.Active().AsNoTracking()
            .Where(x => x.AlertName.Equals(alertName) && x.AlertPriority.Equals(alertPriority))
            .OrderByDescending(x => x.Id);

        return alertMaster;
    }

    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.AlertName.Equals(name))
            : Entities.Where(e => e.AlertName.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsAlertIdExist(string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.AlertId.Equals(id))
            :false);

    }
}