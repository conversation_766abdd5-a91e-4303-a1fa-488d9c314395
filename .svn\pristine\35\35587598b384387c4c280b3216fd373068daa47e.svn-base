namespace ContinuityPatrol.Application.Features.BackUp.Commands.Update;

public class UpdateBackUpCommand : IRequest<UpdateBackUpResponse>
{
    public string Id { get; set; }
    public string HostName { get; set; }
    public string DatabaseName { get; set; }
    public string UserName { get; set; }
    public string Password { get; set; }
    public bool IsLocalServer { get; set; }
    public bool IsBackUpServer { get; set; }
    public string BackUpPath { get; set; }
    public string BackUpType { get; set; }
    public string CronExpression { get; set; }
    public string ScheduleType { get; set; }
    public string ScheduleTime { get; set; }
    public string Properties { get; set; }
    public string KeepBackUpLast { get; set; }
    public string NodeId { get; set; }
    public string NodeName { get; set; }
}