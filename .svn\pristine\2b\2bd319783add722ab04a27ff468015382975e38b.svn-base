﻿using ContinuityPatrol.Application.Features.SolutionHistory.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.SolutionHistory.Events;

public class SolutionHistoryUpdatedEventHandlerTests : IClassFixture<SolutionHistoryFixture>
{
    private readonly SolutionHistoryFixture _solutionHistoryFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly SolutionHistoryUpdatedEventHandler _handler;

    public SolutionHistoryUpdatedEventHandlerTests(SolutionHistoryFixture solutionHistoryFixture)
    {
        _solutionHistoryFixture = solutionHistoryFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockSolutionHistoryEventLogger = new Mock<ILogger<SolutionHistoryUpdatedEventHandler>>();

        _mockUserActivityRepository = SolutionHistoryRepositoryMocks.CreateSolutionHistoryEventRepository(_solutionHistoryFixture.UserActivities);

        _handler = new SolutionHistoryUpdatedEventHandler(mockLoggedInUserService.Object, mockSolutionHistoryEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateSolutionHistoryEventUpdated()
    {
        _solutionHistoryFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_solutionHistoryFixture.SolutionHistoryUpdatedEvent, CancellationToken.None);

        result.Equals(_solutionHistoryFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_solutionHistoryFixture.SolutionHistoryUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_UpdateSolutionHistoryEventUpdated()
    {
        _solutionHistoryFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_solutionHistoryFixture.SolutionHistoryUpdatedEvent, CancellationToken.None);

        result.Equals(_solutionHistoryFixture.UserActivities[0].Id);

        result.Equals(_solutionHistoryFixture.SolutionHistoryUpdatedEvent.ActionName);

        await Task.CompletedTask;
    }
}