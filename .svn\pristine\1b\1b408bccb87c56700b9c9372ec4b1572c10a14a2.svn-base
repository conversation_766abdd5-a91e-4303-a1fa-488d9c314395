﻿namespace ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetFormTypeCategoryByName;

public class
    GetFormTypeCategoryByNameQueryHandler : IRequestHandler<GetFormTypeCategoryByNameQuery,
        List<FormTypeCategoryByNameVm>>
{
    private readonly IFormTypeCategoryRepository _formTypeCategoryRepository;
    private readonly IMapper _mapper;

    public GetFormTypeCategoryByNameQueryHandler(IFormTypeCategoryRepository formTypeCategoryRepository, IMapper mapper)
    {
        _formTypeCategoryRepository = formTypeCategoryRepository;
        _mapper = mapper;
    }

    public async Task<List<FormTypeCategoryByNameVm>> Handle(GetFormTypeCategoryByNameQuery request,
        CancellationToken cancellationToken)
    {
        var formTypeCategory =
            await _formTypeCategoryRepository.GetFormTypeCategoryByName(request.Name);

        return formTypeCategory.Count != 0
            ? _mapper.Map<List<FormTypeCategoryByNameVm>>(formTypeCategory)
            : new List<FormTypeCategoryByNameVm>();
    }
}