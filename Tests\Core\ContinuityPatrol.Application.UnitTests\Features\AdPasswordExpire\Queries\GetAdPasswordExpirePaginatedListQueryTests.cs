using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.AdPasswordExpireModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordExpire.Queries;

public class GetAdPasswordExpirePaginatedListQueryTests : IClassFixture<AdPasswordExpireFixture>
{
    private readonly AdPasswordExpireFixture _adPasswordExpireFixture;
    private readonly Mock<IAdPasswordExpireRepository> _mockAdPasswordExpireRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetAdPasswordExpirePaginatedListQueryHandler _handler;

    public GetAdPasswordExpirePaginatedListQueryTests(AdPasswordExpireFixture adPasswordExpireFixture)
    {
        _adPasswordExpireFixture = adPasswordExpireFixture;

        _mockAdPasswordExpireRepository = AdPasswordExpireRepositoryMocks.CreateQueryAdPasswordExpireRepository(_adPasswordExpireFixture.AdPasswordExpires);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        //_mockMapper.Setup(m => m.Map<PaginatedResult<AdPasswordExpireListVm>>(It.IsAny<PaginatedResult<Domain.Entities.AdPasswordExpire>>()))
        //    .Returns((PaginatedResult<Domain.Entities.AdPasswordExpire> paginatedEntities) => 
        //        new PaginatedResult<AdPasswordExpireListVm>(
        //            paginatedEntities.Data.Select(entity => new AdPasswordExpireListVm
        //            {
        //                Id = entity.ReferenceId,
        //                DomainServerId = entity.DomainServerId,
        //                DomainServer = entity.DomainServer,
        //                UserName = entity.UserName,
        //                Email = entity.Email,
        //                ServerList = entity.ServerList,
        //                NotificationDays = entity.NotificationDays,
        //                IsPassword = entity.IsPassword
        //            }).ToList(),
        //            paginatedEntities.TotalCount,
        //            paginatedEntities.CurrentPage,
        //            paginatedEntities.PageSize));

        _handler = new GetAdPasswordExpirePaginatedListQueryHandler(
            _mockMapper.Object,
            _mockAdPasswordExpireRepository.Object);
    }

   

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsync_OnlyOnce()
    {
        // Arrange
        var query = new GetAdPasswordExpirePaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        //_mockAdPasswordExpireRepository.Verify(x => x.PaginatedListAllAsync(
        //    It.IsAny<int>(), It.IsAny<int>(), It.IsAny<object>(), It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var query = new GetAdPasswordExpirePaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<PaginatedResult<AdPasswordExpireListVm>>(
            It.IsAny<PaginatedResult<Domain.Entities.AdPasswordExpire>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_PassCorrectParameters_When_CallingRepository()
    {
        // Arrange
        var query = new GetAdPasswordExpirePaginatedListQuery
        {
            PageNumber = 2,
            PageSize = 5,
            SearchString = "test",
            SortColumn = "UserName",
            SortOrder = "asc"
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        //_mockAdPasswordExpireRepository.Verify(x => x.PaginatedListAllAsync(
        //    2, 5, It.IsAny<object>(), "UserName", "asc"), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectPaginationInfo_When_QueryExecuted()
    {
        // Arrange
        var query = new GetAdPasswordExpirePaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 2
        };

        query.PageSize.ShouldBe(2);
       
    }

    [Fact]
    public async Task Handle_MapEntitiesToViewModels_WithCorrectProperties()
    {
        // Arrange
        var testExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        testExpire.DomainServerId = "DS001";
        testExpire.DomainServer = "TestDomain.com";
        testExpire.UserName = "TestUser";
        testExpire.Email = "<EMAIL>";

        var query = new GetAdPasswordExpirePaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldBeNull();
    }

   
    [Fact]
    public async Task Handle_HandleDifferentPageSizes_When_QueryExecuted()
    {
        // Arrange
        var query = new GetAdPasswordExpirePaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 1
        };

        query.PageSize.ShouldBe(1);
        
    }

    [Fact]
    public async Task Handle_CreateFilterSpecification_When_SearchStringProvided()
    {
        // Arrange
        var query = new GetAdPasswordExpirePaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "TestSearch"
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        //_mockAdPasswordExpireRepository.Verify(x => x.PaginatedListAllAsync(
        //    1, 10, It.IsNotNull<object>(), It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }   
   
}
