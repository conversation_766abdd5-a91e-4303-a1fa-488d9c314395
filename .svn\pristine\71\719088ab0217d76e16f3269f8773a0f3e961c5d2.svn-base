using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class FormTypeFixture : IDisposable
{
    public List<FormType> FormTypePaginationList { get; set; }
    public List<FormType> FormTypeList { get; set; }
    public FormType FormTypeDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public FormTypeFixture()
    {
        var fixture = new Fixture();

        FormTypeList = fixture.Create<List<FormType>>();

        FormTypePaginationList = fixture.CreateMany<FormType>(20).ToList();

        FormTypeDto = fixture.Create<FormType>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
