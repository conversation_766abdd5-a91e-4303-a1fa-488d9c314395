﻿using ContinuityPatrol.Application.Features.LicenseHistory.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.LicenseHistory.Queries;

public class GetLicenseHistoryDetailQueryHandlerTests : IClassFixture<LicenseHistoryFixture>
{
    private readonly LicenseHistoryFixture _licenseHistoryFixture;

    private readonly Mock<ILicenseHistoryRepository> _mockLicenseHistoryRepository;

    private readonly GetLicenseHistoryDetailQueryHandler _handler;

    public GetLicenseHistoryDetailQueryHandlerTests(LicenseHistoryFixture licenseHistoryFixture)
    {
        _licenseHistoryFixture = licenseHistoryFixture;

        _mockLicenseHistoryRepository = LicenseHistoryRepositoryMocks.GetLicenseHistoryRepository(_licenseHistoryFixture.LicenseHistories);

        _handler = new GetLicenseHistoryDetailQueryHandler(_mockLicenseHistoryRepository.Object, _licenseHistoryFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_LicenseHistoryDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetLicenseHistoryDetailQuery { LicenseId = _licenseHistoryFixture.LicenseHistories[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<LicenseHistoryDetailVm>();

        result.Id.ShouldBe(_licenseHistoryFixture.LicenseHistories[0].ReferenceId);
        result.PONumber.ShouldBe(_licenseHistoryFixture.LicenseHistories[0].PONumber);
        result.CompanyId.ShouldBe(_licenseHistoryFixture.LicenseHistories[0].CompanyId);
        result.CPHostName.ShouldBe(_licenseHistoryFixture.LicenseHistories[0].CPHostName);
        result.IPAddress.ShouldBe(_licenseHistoryFixture.LicenseHistories[0].IPAddress);
        result.MACAddress.ShouldBe(_licenseHistoryFixture.LicenseHistories[0].MACAddress);
        result.LicenseKey.ShouldBe(_licenseHistoryFixture.LicenseHistories[0].LicenseKey);
        result.Validity.ShouldBe(_licenseHistoryFixture.LicenseHistories[0].Validity);
        result.ExpiryDate.ShouldBe(_licenseHistoryFixture.LicenseHistories[0].ExpiryDate);
        result.UpdaterId.ShouldBe(_licenseHistoryFixture.LicenseHistories[0].UpdaterId);

    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidLicenseHistoryId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetLicenseHistoryDetailQuery { LicenseId = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("Not Found");
    }

    [Fact]
    public async Task Handle_Call_GetByIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetLicenseHistoryDetailQuery { LicenseId = _licenseHistoryFixture.LicenseHistories[0].ReferenceId }, CancellationToken.None);

        _mockLicenseHistoryRepository.Verify(x => x.GetLicenseHistoryByLicenseId(It.IsAny<string>()), Times.Once);
    }
}