﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class AccessManagerRepository : BaseRepository<AccessManager>, IAccessManagerRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public AccessManagerRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext,loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public override async Task<IReadOnlyList<AccessManager>> ListAllAsync()
    {
        var accessManagers = base.QueryAll(accessManagers => accessManagers.CompanyId.Equals(_loggedInUserService.CompanyId));
        return await SelectToAccessManagers(accessManagers.AsQueryable()).ToListAsync();
    }

    public override async Task<AccessManager> GetByReferenceIdAsync(string id)
    {
        var accessManagers = SelectToAccessManagers(base.GetByReferenceId(id,
            accessManagers => accessManagers.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                           accessManagers.ReferenceId.Equals(id)));

        var accessManager = MapAccessManager(accessManagers);

        return await accessManager.FirstOrDefaultAsync();
    }

    public async Task<List<AccessManager>> GetAccessManagerRoles()
    {
        var accessManagers = base.QueryAll(accessManagers => accessManagers.CompanyId.Equals(_loggedInUserService.CompanyId));
        return await MapAccessManager(accessManagers.AsQueryable()).ToListAsync();

    }
    public override async Task<PaginatedResult<AccessManager>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<AccessManager> productFilterSpec, string sortColumn, string sortOrder)
    {
        var query = IsParent
            ? Entities.Specify(productFilterSpec).DescOrderById()
            : Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById();

        return await MapAccessManager(query).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public async Task<bool> IsAccessManagerRoleUnique(string role)
    {
        return await _dbContext.AccessManagers.AnyAsync(e => e.RoleName.Equals(role));
    }

    public async Task<bool> IsAccessManagerRoleExist(string role, string id)
    {
        if (!id.IsValidGuid())
        {
            return await _dbContext.AccessManagers.AnyAsync(e => e.RoleName.Equals(role));
        }

        var entities = await _dbContext.AccessManagers.Where(e => e.RoleName.Equals(role)).ToListAsync();
        return entities.Unique(id);
    }

    public async Task<AccessManager> GetAccessManagerByRoleId(string roleId)
    {
       return await MapAccessManager(_dbContext.AccessManagers
            .AsNoTracking()
            .Active())
            .FirstOrDefaultAsync(e => e.RoleId == roleId);
    }

    public override async Task<AccessManager> GetByIdAsync(int id)
    {
        if (_loggedInUserService.IsParent)
        {
            return await base.GetByIdAsync(id);
        }

        var accessManagers = await FindByFilterAsync(accessManager =>
            accessManager.Id.Equals(id) && accessManager.CompanyId.Equals(_loggedInUserService.CompanyId));

        return accessManagers.SingleOrDefault();
    }
    public async Task<List<AccessManager>> GetAccessManagersByRoleIds(List<string> roleIds)
    {
        if (roleIds == null || !roleIds.Any())
        {
            return new List<AccessManager>();
        }

        return await SelectToAccessManagers(_dbContext.AccessManagers
            .Where(am => roleIds.Contains(am.RoleId)))
            .ToListAsync();
    }


    private IQueryable<AccessManager> MapAccessManager(IQueryable<AccessManager> accessManagers)
    {
        return accessManagers
        .Select(access => new
        {
            accessManagers = access,
            UserRole = _dbContext.UserRoles.Active().AsNoTracking().FirstOrDefault(bs => bs.ReferenceId.Equals(access.RoleId))

        }).Select(result => new AccessManager
        {
            Id = result.accessManagers.Id,
            ReferenceId = result.accessManagers.ReferenceId,
            RoleId = result.UserRole.ReferenceId,
            RoleName = result.UserRole.Role,
            Properties = result.accessManagers.Properties,
            CompanyId = result.accessManagers.CompanyId,
            ProfileProperties = result.accessManagers.ProfileProperties,
            IsActive = result.accessManagers.IsActive,
            CreatedBy = result.accessManagers.CreatedBy,
            CreatedDate = result.accessManagers.CreatedDate,
            LastModifiedBy = result.accessManagers.LastModifiedBy,
            LastModifiedDate = result.accessManagers.LastModifiedDate
        });
    }

    private IQueryable<AccessManager> SelectToAccessManagers(IQueryable<AccessManager> query)
    {
        return query.Select(access =>new AccessManager
        {
            Id = access.Id,
            ReferenceId = access.ReferenceId,
            RoleId = access.RoleId,
            RoleName = access.RoleName,
            Properties = access.Properties,
            CompanyId = access.CompanyId,
            ProfileProperties = access.ProfileProperties,
        });
    }
}