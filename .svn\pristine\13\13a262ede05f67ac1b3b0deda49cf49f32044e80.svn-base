﻿using ContinuityPatrol.Application.Features.User.Queries.GetDomainUser;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.User.Queries
{
    public class GetDomainUserListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<IUserRepository> _userRepositoryMock;
        private readonly GetDomainUserListQueryHandler _handler;

        public GetDomainUserListQueryHandlerTests()
        {
            _mapperMock = new Mock<IMapper>();
            _userRepositoryMock = new Mock<IUserRepository>();
            _handler = new GetDomainUserListQueryHandler(_mapperMock.Object, _userRepositoryMock.Object);
        }

        [Fact]
        public async Task Handle_Should_ReturnDomainUsers_When_DomainUserNameIsProvided()
        {
            var query = new GetDomainUserListQuery
            {
                Name = "example.com",
                DomainUserName = "johndoe"
            };
            var expectedUsers = new List<string> { "johndoe", "johnsmith" };

            _userRepositoryMock
                .Setup(repo => repo.GetDomainUsersByUserName(query.Name, query.DomainUserName))
                .ReturnsAsync(expectedUsers);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.Equal(expectedUsers, result);
            _userRepositoryMock.Verify(repo => repo.GetDomainUsersByUserName(query.Name, query.DomainUserName), Times.Once);
            _userRepositoryMock.Verify(repo => repo.GetDomainUsers(It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task Handle_Should_ReturnDomainUsers_When_DomainUserNameIsNotProvided()
        {
            var query = new GetDomainUserListQuery
            {
                Name = "example.com",
                DomainUserName = null
            };
            var expectedUsers = new List<string> { "johndoe", "janedoe" };

            _userRepositoryMock
                .Setup(repo => repo.GetDomainUsers(query.Name))
                .ReturnsAsync(expectedUsers);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.Equal(expectedUsers, result);
            _userRepositoryMock.Verify(repo => repo.GetDomainUsers(query.Name), Times.Once);
            _userRepositoryMock.Verify(repo => repo.GetDomainUsersByUserName(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task Handle_Should_ThrowNotFoundException_When_NoUsersFound()
        {
            var query = new GetDomainUserListQuery
            {
                Name = "example.com",
                DomainUserName = "nonexistentuser"
            };
            var emptyUsers = new List<string>();

            _userRepositoryMock
                .Setup(repo => repo.GetDomainUsersByUserName(query.Name, query.DomainUserName))
                .ReturnsAsync(emptyUsers);

            var exception = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, CancellationToken.None));

            Assert.Contains(nameof(Domain.Entities.User), exception.Message);

            _userRepositoryMock.Verify(repo => repo.GetDomainUsersByUserName(query.Name, query.DomainUserName), Times.Once);
        }

        [Theory]
        [InlineData("johndoe", true)] // DomainUserName is provided
        [InlineData(null, false)]     // DomainUserName is not provided
        public async Task Handle_Should_CallCorrectRepositoryMethod_BasedOnDomainUserName(string domainUserName, bool isGetByUserNameCalled)
        {
            var query = new GetDomainUserListQuery
            {
                Name = "example.com",
                DomainUserName = domainUserName
            };
            var expectedUsers = new List<string> { "johndoe", "janedoe" };

            if (isGetByUserNameCalled)
            {
                _userRepositoryMock
                    .Setup(repo => repo.GetDomainUsersByUserName(query.Name, query.DomainUserName))
                    .ReturnsAsync(expectedUsers);
            }
            else
            {
                _userRepositoryMock
                    .Setup(repo => repo.GetDomainUsers(query.Name))
                    .ReturnsAsync(expectedUsers);
            }

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.Equal(expectedUsers, result);

            if (isGetByUserNameCalled)
            {
                _userRepositoryMock.Verify(repo => repo.GetDomainUsersByUserName(query.Name, query.DomainUserName), Times.Once);
                _userRepositoryMock.Verify(repo => repo.GetDomainUsers(It.IsAny<string>()), Times.Never);
            }
            else
            {
                _userRepositoryMock.Verify(repo => repo.GetDomainUsers(query.Name), Times.Once);
                _userRepositoryMock.Verify(repo => repo.GetDomainUsersByUserName(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
            }
        }

        [Fact]
        public async Task Handle_Should_ThrowException_When_RepositoryThrows()
        {
            var query = new GetDomainUserListQuery
            {
                Name = "example.com",
                DomainUserName = "johndoe"
            };

            _userRepositoryMock
                .Setup(repo => repo.GetDomainUsersByUserName(query.Name, query.DomainUserName))
                .ThrowsAsync(new Exception("Database error"));

            await Assert.ThrowsAsync<Exception>(() => _handler.Handle(query, CancellationToken.None));

            _userRepositoryMock.Verify(repo => repo.GetDomainUsersByUserName(query.Name, query.DomainUserName), Times.Once);
            _userRepositoryMock.Verify(repo => repo.GetDomainUsers(It.IsAny<string>()), Times.Never);
        }
    }
}
