{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"PCPL/1.0.0": {"dependencies": {"AutoMapper": "12.0.1", "Newtonsoft.Json": "13.0.3", "Serilog": "2.12.0", "Serilog.AspNetCore": "6.0.1", "Serilog.Enrichers.ClientInfo": "1.2.0", "Serilog.Extensions.Logging": "3.1.0", "Serilog.Settings.Configuration": "3.4.0", "Serilog.Sinks.File": "5.0.0", "System.Management": "6.0.0", "System.Runtime": "4.3.1", "Bcms.Common": "1.0.0.0", "Bcms.DataAccess": "1.0.0.0", "Bcms.ExceptionHandler": "1.0.0.0", "Bcms.Helper": "1.0.0.0", "BouncyCastle.Crypto": "1.7.4114.6375", "Jscape.Ssh": "2.7.0.0", "PGlobalMirror": "1.0.0.0", "PSoftLayer": "1.0.0.0", "Rebex.Common": "5.0.7450.0", "Rebex.Networking": "5.0.7450.0", "Rebex.Sftp": "5.0.7450.0", "Rebex.SshShell": "5.0.7450.0", "Rebex.Terminal": "5.0.7450.0", "System.Management.Automation": "*******", "System.Windows.Forms": "6.0.2.0"}, "runtime": {"PCPL.dll": {}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "1*******", "fileVersion": "12.0.1.0"}}}, "Microsoft.AspNetCore.Http/2.2.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Extensions.Configuration/2.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.8"}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.8": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Configuration.Binder/2.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.0.0"}}, "Microsoft.Extensions.DependencyInjection/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {}, "Microsoft.Extensions.DependencyModel/3.0.0": {"dependencies": {"System.Text.Json": "4.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.19.46305"}}}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.8": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/3.1.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "3.1.8", "Microsoft.Extensions.Logging.Abstractions": "5.0.0"}}, "Microsoft.Extensions.Logging/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {}, "Microsoft.Extensions.ObjectPool/2.2.0": {}, "Microsoft.Extensions.Options/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.Primitives/5.0.0": {}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0", "System.Buffers": "4.5.0"}}, "Microsoft.NETCore.Platforms/1.1.1": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}}, "Serilog/2.12.0": {"runtime": {"lib/net6.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Serilog.AspNetCore/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.0", "Microsoft.Extensions.Logging": "5.0.0", "Serilog": "2.12.0", "Serilog.Extensions.Hosting": "5.0.1", "Serilog.Formatting.Compact": "1.1.0", "Serilog.Settings.Configuration": "3.4.0", "Serilog.Sinks.Console": "4.0.1", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net5.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.ClientInfo/1.2.0": {"dependencies": {"Microsoft.AspNetCore.Http": "2.2.2", "Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.1/Serilog.Enrichers.ClientInfo.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Serilog.Extensions.Hosting/5.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Hosting.Abstractions": "3.1.8", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Serilog": "2.12.0", "Serilog.Extensions.Logging": "3.1.0"}, "runtime": {"lib/netstandard2.1/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/3.1.0": {"dependencies": {"Microsoft.Extensions.Logging": "5.0.0", "Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/1.1.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/3.4.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "2.0.0", "Microsoft.Extensions.DependencyModel": "3.0.0", "Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/4.0.1": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "System.Buffers/4.5.0": {}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Management/6.0.0": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/System.Management.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Text.Encodings.Web/4.5.0": {}, "System.Text.Json/4.6.0": {}, "Bcms.Common/1.0.0.0": {"runtime": {"Bcms.Common.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Bcms.DataAccess/1.0.0.0": {"runtime": {"Bcms.DataAccess.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Bcms.ExceptionHandler/1.0.0.0": {"runtime": {"Bcms.ExceptionHandler.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Bcms.Helper/1.0.0.0": {"runtime": {"Bcms.Helper.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "BouncyCastle.Crypto/1.7.4114.6375": {"runtime": {"BouncyCastle.Crypto.dll": {"assemblyVersion": "1.7.4114.6375", "fileVersion": "1.7.4114.6375"}}}, "Jscape.Ssh/2.7.0.0": {"runtime": {"Jscape.Ssh.dll": {"assemblyVersion": "2.7.0.0", "fileVersion": "2.7.0.0"}}}, "PGlobalMirror/1.0.0.0": {"runtime": {"PGlobalMirror.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "PSoftLayer/1.0.0.0": {"runtime": {"PSoftLayer.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Rebex.Common/5.0.7450.0": {"runtime": {"Rebex.Common.dll": {"assemblyVersion": "5.0.7450.0", "fileVersion": "5.0.7450.0"}}}, "Rebex.Networking/5.0.7450.0": {"runtime": {"Rebex.Networking.dll": {"assemblyVersion": "5.0.7450.0", "fileVersion": "5.0.7450.0"}}}, "Rebex.Sftp/5.0.7450.0": {"runtime": {"Rebex.Sftp.dll": {"assemblyVersion": "5.0.7450.0", "fileVersion": "5.0.7450.0"}}}, "Rebex.SshShell/5.0.7450.0": {"runtime": {"Rebex.SshShell.dll": {"assemblyVersion": "5.0.7450.0", "fileVersion": "5.0.7450.0"}}}, "Rebex.Terminal/5.0.7450.0": {"runtime": {"Rebex.Terminal.dll": {"assemblyVersion": "5.0.7450.0", "fileVersion": "5.0.7450.0"}}}, "System.Management.Automation/*******": {"runtime": {"System.Management.Automation.dll": {"assemblyVersion": "*******", "fileVersion": "6.2.9200.16384"}}}, "System.Windows.Forms/6.0.2.0": {"runtime": {"System.Windows.Forms.dll": {"assemblyVersion": "6.0.2.0", "fileVersion": "6.0.2123.36406"}}}, "Microsoft.Practices.EnterpriseLibrary.Data/*******": {"runtime": {"Microsoft.Practices.EnterpriseLibrary.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "log4net/1.2.9.0": {"runtime": {"log4net.dll": {"assemblyVersion": "1.2.9.0", "fileVersion": "1.2.9.0"}}}, "Microsoft.Practices.EnterpriseLibrary.Common/*******": {"runtime": {"Microsoft.Practices.EnterpriseLibrary.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Practices.ObjectBuilder/1.0.51206.0": {"runtime": {"Microsoft.Practices.ObjectBuilder.dll": {"assemblyVersion": "1.0.51206.0", "fileVersion": "1.0.51206.0"}}}}}, "libraries": {"PCPL/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-BAibpoItxI5puk7YJbIGj95arZueM8B8M5xT1fXBn3hb3L2G3ucrZcYXv1gXdaroLbntUs8qeV8iuBrpjQsrKw==", "path": "microsoft.aspnetcore.http/2.2.2", "hashPath": "microsoft.aspnetcore.http.2.2.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SsI4RqI8EH00+cYO96tbftlh87sNUv1eeyuBU1XZdQkG0RrHAOjWgl7P0FoLeTSMXJpOnfweeOWj2d1/5H3FxA==", "path": "microsoft.extensions.configuration/2.0.0", "hashPath": "microsoft.extensions.configuration.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-0qbNyxGpuNP/fuQ3FLHesm1Vn/83qYcAgVsi1UQCQN1peY4YH1uiizOh4xbYkQyxiVMD/c/zhiYYv94G0DXSSA==", "path": "microsoft.extensions.configuration.abstractions/3.1.8", "hashPath": "microsoft.extensions.configuration.abstractions.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IznHHzGUtrdpuQqIUdmzF6TYPcsYHONhHh3o9dGp39sX/9Zfmt476UnhvU0UhXgJnXXAikt/MpN6AuSLCCMdEQ==", "path": "microsoft.extensions.configuration.binder/2.0.0", "hashPath": "microsoft.extensions.configuration.binder.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rc2kb/p3Ze6cP6rhFC3PJRdWGbLvSHZc0ev7YlyeU6FmHciDMLrhoVoTUEzKPhN5ZjFgKF1Cf5fOz8mCMIkvpA==", "path": "microsoft.extensions.dependencyinjection/5.0.0", "hashPath": "microsoft.extensions.dependencyinjection.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ORj7Zh81gC69TyvmcUm9tSzytcy8AVousi+IVRAI8nLieQjOFryRusSFh7+aLk16FN9pQNqJAiMd7BTKINK0kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/5.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Iaectmzg9Dc4ZbKX/FurrRjgO/I8rTumL5UU+Uube6vZuGetcnXoIgTA94RthFWePhdMVm8MMhVFJZdbzMsdyQ==", "path": "microsoft.extensions.dependencymodel/3.0.0", "hashPath": "microsoft.extensions.dependencymodel.3.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-U7ffyzrPfRDH5K3h/mBpqJVoHbppw1kc1KyHZcZeDR7b1A0FRaqMSiizGpN9IGwWs9BuN7oXIKFyviuSGBjHtQ==", "path": "microsoft.extensions.fileproviders.abstractions/3.1.8", "hashPath": "microsoft.extensions.fileproviders.abstractions.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/3.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-7ZJUKwPipkDvuv2KJPZ3r01wp2AWNMiYH+61i0dL89F7QICknjKpWgLKLpTSUYFgl77S3b4264I6i4HzDdrb2A==", "path": "microsoft.extensions.hosting.abstractions/3.1.8", "hashPath": "microsoft.extensions.hosting.abstractions.3.1.8.nupkg.sha512"}, "Microsoft.Extensions.Logging/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MgOwK6tPzB6YNH21wssJcw/2MKwee8b2gI7SllYfn6rvTpIrVvVS5HAjSU2vqSku1fwqRvWP0MdIi14qjd93Aw==", "path": "microsoft.extensions.logging/5.0.0", "hashPath": "microsoft.extensions.logging.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NxP6ahFcBnnSfwNBi2KH2Oz8Xl5Sm2krjId/jRR3I7teFphwiUoUeZPwTNA21EX+5PtjqmyAvKaOeBXcJjcH/w==", "path": "microsoft.extensions.logging.abstractions/5.0.0", "hashPath": "microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CBvR92TCJ5uBIdd9/HzDSrxYak+0W/3+yxrNg8Qm6Bmrkh5L+nu6m3WeazQehcZ5q1/6dDA7J5YdQjim0165zg==", "path": "microsoft.extensions.options/5.0.0", "hashPath": "microsoft.extensions.options.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "path": "microsoft.extensions.primitives/5.0.0", "hashPath": "microsoft.extensions.primitives.5.0.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "path": "microsoft.netcore.platforms/1.1.1", "hashPath": "microsoft.netcore.platforms.1.1.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Serilog/2.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-xaiJLIdu6rYMKfQMYUZgTy8YK7SMZjB4Yk50C/u//Z4OsvxkUfSPJy4nknfvwAC34yr13q7kcyh4grbwhSxyZg==", "path": "serilog/2.12.0", "hashPath": "serilog.2.12.0.nupkg.sha512"}, "Serilog.AspNetCore/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5XW90k62V7G9I0D/j9Iz+NyRBB6/SnoFpHUPeLnV40gONV2vs2A/ewWi91QVjQmyHBfzFeqIrkvE/DJMZ0alTg==", "path": "serilog.aspnetcore/6.0.1", "hashPath": "serilog.aspnetcore.6.0.1.nupkg.sha512"}, "Serilog.Enrichers.ClientInfo/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZJx2eJQKX6+GxjZM9Y++7DPunR7Nizk9Vdq+BqMs/YPfW3Sv+qDm3PVC88srywJMDKfRaecHFWktBjw5F2pmoQ==", "path": "serilog.enrichers.clientinfo/1.2.0", "hashPath": "serilog.enrichers.clientinfo.1.2.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-o0VUyt3npAqOJaZ6CiWLFeLYs3CYJwfcAqaUqprzsmj7qYIvorcn8cZLVR8AQX6vzX7gee2bD0sQeA17iO2/Aw==", "path": "serilog.extensions.hosting/5.0.1", "hashPath": "serilog.extensions.hosting.5.0.1.nupkg.sha512"}, "Serilog.Extensions.Logging/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-IWfem7wfrFbB3iw1OikqPFNPEzfayvDuN4WP7Ue1AVFskalMByeWk3QbtUXQR34SBkv1EbZ3AySHda/ErDgpcg==", "path": "serilog.extensions.logging/3.1.0", "hashPath": "serilog.extensions.logging.3.1.0.nupkg.sha512"}, "Serilog.Formatting.Compact/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-pNroKVjo+rDqlxNG5PXkRLpfSCuDOBY0ri6jp9PLe505ljqwhwZz8ospy2vWhQlFu5GkIesh3FcDs4n7sWZODA==", "path": "serilog.formatting.compact/1.1.0", "hashPath": "serilog.formatting.compact.1.1.0.nupkg.sha512"}, "Serilog.Settings.Configuration/3.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-ULloXSiapTb3zOWodC0G4WRDQzA5RjMEfZNZzOZpH8kC3t/lrISLblklIpKC44CX0sMDF40MnJwTIQ3pFQFs4g==", "path": "serilog.settings.configuration/3.4.0", "hashPath": "serilog.settings.configuration.3.4.0.nupkg.sha512"}, "Serilog.Sinks.Console/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-apLOvSJQLlIbKlbx+Y2UDHSP05kJsV7mou+fvJoRGs/iR+jC22r8cuFVMjjfVxz/AD4B2UCltFhE1naRLXwKNw==", "path": "serilog.sinks.console/4.0.1", "hashPath": "serilog.sinks.console.4.0.1.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Management/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-sHsESYMmPDhQuOC66h6AEOs/XowzKsbT9srMbX71TCXP58hkpn1BqBjdmKj1+DCA/WlBETX1K5WjQHwmV0Txrg==", "path": "system.management/6.0.0", "hashPath": "system.management.6.0.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Text.Encodings.Web/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xg4G4Indi4dqP1iuAiMSwpiWS54ZghzR644OtsRCm/m/lBMG8dUBhLVN7hLm8NNrNTR+iGbshCPTwrvxZPlm4g==", "path": "system.text.encodings.web/4.5.0", "hashPath": "system.text.encodings.web.4.5.0.nupkg.sha512"}, "System.Text.Json/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-4F8Xe+JIkVoDJ8hDAZ7HqLkjctN/6WItJIzQaifBwClC7wmoLSda/Sv2i6i1kycqDb3hWF4JCVbpAweyOKHEUA==", "path": "system.text.json/4.6.0", "hashPath": "system.text.json.4.6.0.nupkg.sha512"}, "Bcms.Common/1.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bcms.DataAccess/1.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bcms.ExceptionHandler/1.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Bcms.Helper/1.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "BouncyCastle.Crypto/1.7.4114.6375": {"type": "reference", "serviceable": false, "sha512": ""}, "Jscape.Ssh/2.7.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "PGlobalMirror/1.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "PSoftLayer/1.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Rebex.Common/5.0.7450.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Rebex.Networking/5.0.7450.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Rebex.Sftp/5.0.7450.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Rebex.SshShell/5.0.7450.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Rebex.Terminal/5.0.7450.0": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Management.Automation/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Windows.Forms/6.0.2.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Practices.EnterpriseLibrary.Data/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "log4net/1.2.9.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Practices.EnterpriseLibrary.Common/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Practices.ObjectBuilder/1.0.51206.0": {"type": "reference", "serviceable": false, "sha512": ""}}}