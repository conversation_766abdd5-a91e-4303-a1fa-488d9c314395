﻿using ContinuityPatrol.Application.Features.GroupPolicy.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.GroupPolicy.Commands;

public class UpdateGroupPolicyTests : IClassFixture<GroupPolicyFixture>
{
    private readonly GroupPolicyFixture _groupPolicyFixture;
    private readonly Mock<IGroupPolicyRepository> _mockGroupPolicyRepository;
    private readonly Mock<IPublisher> _publisher;
    private readonly UpdateGroupPolicyCommandHandler _handler;

    public UpdateGroupPolicyTests(GroupPolicyFixture groupPolicyFixture)
    {
        _groupPolicyFixture = groupPolicyFixture;

        _publisher = new Mock<IPublisher>();

        _mockGroupPolicyRepository = GroupPolicyRepositoryMocks.UpdateGroupPolicyRepository(_groupPolicyFixture.GroupPolicies);

        _handler = new UpdateGroupPolicyCommandHandler(_mockGroupPolicyRepository.Object, _groupPolicyFixture.Mapper, _publisher.Object);
    }

    [Fact]
    public async Task Handle_ValidGroupPolicy_UpdateToGroupPoliciesRepo()
    {
        _groupPolicyFixture.UpdateGroupPolicyCommand.Id = _groupPolicyFixture.GroupPolicies[0].ReferenceId;

        var result = await _handler.Handle(_groupPolicyFixture.UpdateGroupPolicyCommand, CancellationToken.None);

        var groupPolicy = await _mockGroupPolicyRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_groupPolicyFixture.UpdateGroupPolicyCommand.GroupName, groupPolicy.GroupName);
    }

    [Fact]
    public async Task Handle_Return_UpdateGroupPolicyResponse_When_GroupPolicyUpdated()
    {
        _groupPolicyFixture.UpdateGroupPolicyCommand.Id = _groupPolicyFixture.GroupPolicies[0].ReferenceId;

        var result = await _handler.Handle(_groupPolicyFixture.UpdateGroupPolicyCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateGroupPolicyResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_groupPolicyFixture.UpdateGroupPolicyCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidGroupPolicyId()
    {
        _groupPolicyFixture.UpdateGroupPolicyCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_groupPolicyFixture.UpdateGroupPolicyCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _groupPolicyFixture.UpdateGroupPolicyCommand.Id = _groupPolicyFixture.GroupPolicies[0].ReferenceId;

        await _handler.Handle(_groupPolicyFixture.UpdateGroupPolicyCommand, CancellationToken.None);

        _mockGroupPolicyRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockGroupPolicyRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.GroupPolicy>()), Times.Once);
    }
}