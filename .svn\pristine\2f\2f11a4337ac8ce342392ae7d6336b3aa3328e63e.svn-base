using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.SendEmail;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowDrCalenderModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class WorkflowDrCalendersController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<WorkflowDrCalenderListVm>>> GetWorkflowDrCalenders()
    {
        Logger.LogDebug("Get All WorkflowDrCalenders");

        return Ok(await Mediator.Send(new GetWorkflowDrCalenderListQuery()));
    }


    [HttpPost("sendEemail")]
    [Authorize(Policy = Permissions.Orchestration.Create)]
    public async Task<ActionResult<WorkflowDrCalenderDetailVm>> SendEmail(WorkflowDrCalenderSendEmailCommand workflowDrCalenderSendEmailCommand)
    {

        Logger.LogDebug(" Workflow DrCalender Send Email");

       return Ok(await Mediator.Send(workflowDrCalenderSendEmailCommand));
            
    }
        #region Paginated
        [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Orchestration.View)]
 public async Task<ActionResult<PaginatedResult<WorkflowDrCalenderListVm>>> GetPaginatedWorkflowDrCalenders([FromQuery] GetWorkflowDrCalenderPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in WorkflowDrCalender Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Orchestration.Create)]
    public async Task<ActionResult<CreateWorkflowDrCalenderResponse>> CreateWorkflowDrCalender([FromBody] CreateWorkflowDrCalenderCommand createWorkflowDrCalenderCommand)
    {
        Logger.LogDebug($"Create WorkflowDrCalender '{createWorkflowDrCalenderCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateWorkflowDrCalender), await Mediator.Send(createWorkflowDrCalenderCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Orchestration.Edit)]
    public async Task<ActionResult<UpdateWorkflowDrCalenderResponse>> UpdateWorkflowDrCalender([FromBody] UpdateWorkflowDrCalenderCommand updateWorkflowDrCalenderCommand)
    {
        Logger.LogDebug($"Update WorkflowDrCalender '{updateWorkflowDrCalenderCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateWorkflowDrCalenderCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Orchestration.Delete)]
    public async Task<ActionResult<DeleteWorkflowDrCalenderResponse>> DeleteWorkflowDrCalender(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "WorkflowDrCalender Id");

        Logger.LogDebug($"Delete WorkflowDrCalender Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteWorkflowDrCalenderCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsWorkflowDrCalenderNameExist(string workflowDrCalenderName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(workflowDrCalenderName, "WorkflowDrCalender Name");

     Logger.LogDebug($"Check Name Exists Detail by WorkflowDrCalender Name '{workflowDrCalenderName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetWorkflowDrCalenderNameUniqueQuery { Name = workflowDrCalenderName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


