﻿@using ContinuityPatrol.Domain.ViewModels.ManageWorkflow;
@model ContinuityPatrol.Domain.ViewModels.ManageWorkflow.ManageWorkflowModel
@* @model dynamic
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
} *@

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-circle-workflow"></i><span>Manage Workflow List</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown"  title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="" id="workflowName">
                                        <label class="form-check-label" for="workflowName">
                                            Workflow Name
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                @* <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cp-add me-1"></i>Create</button> *@
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="tblmanageworkflow" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Workflow Name</th>
                        <th>InfraObject Name</th>
                        <th>Created By</th>
                        <th>Created Date</th>
                        <th class="Action-th">Four&nbsp;Eye&nbsp;Status</th>
                    </tr>
                </thead>
                <tbody>
                    @{
                        int i = 1;
                    }
                    @foreach (var workflow in Model.Allworkflowdetails)
                    {
                        <tr>
                            <td>
                                <span>@i</span>
                            </td>
                            <td>
                                <span class="Avatar_Logo">
                                    <img class="customer_logo" src="@workflow.workflowName" title="" />
                                </span>
                                @workflow.workflowName
                            </td>
                            <td>@workflow.InfraObjectName</td>
                            @if(@workflow.CreatedBy==null)
                            {
                                <td>NA</td>
                            }
                            else
                            {
                                <td>@workflow.CreatedBy</td>
                            }

                            <td>@workflow.CreatedDate</td>
                            <td class="Action-th">
                                @*  <div class="dropdown">
                            <i class="cp-horizontal-dots me-2" role="button" data-bs-toggle="dropdown"></i>
                            <ul class="dropdown-menu">
                            <li>
                            <a role="button" class="dropdown-item edit-button" data-company='@Json.Serialize(workflow)'>
                            <i class="cp-edit me-2"></i>Enable
                            </a>
                            </li>
                            <li>
                            <a role="button" class="dropdown-item delete-button" data-company-id="@workflow.Id" data-company-name="@workflow.workflowName" data-bs-toggle="modal">
                            <i class="cp-Delete me-2"></i>Disable
                            </a>
                            </li>
                            </ul>
                            </div> *@
                                <div class="form-switch">
                                    @* <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckChecked" > *@
                                    @Html.CheckBox(workflow.Id, (workflow.IsFourEye == "1" ? true : false) , new { @class = "form-check-input", role="switch", @id = workflow.Id, @onchange="onStatusChanged(this)" })
                                    <label class="form-check-label" for=@workflow.Id></label>
                                </div>
                            </td>
                        </tr>
                        i++;
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<!--Modal Create-->
@* <div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Configuration" model="new ManageWorkflowModel()" />
</div>  *@

<!--Modal Delete-->
@*  <div class="modal fade" id="DisableModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="DisableModal" model="new ManageWorkflowModel()" />
</div>  *@

@section Scripts
    {
    <partial name="_ValidationScriptsPartial" />
}

<script type="text/javascript">
    // var RootUrl = '@Url.Content("~/")';

    function  onStatusChanged(val) {

        var ischecked = val.checked == true ? 1 : 0;

        $.ajax({

            type: "POST",
            url: "/Manage/ManageWorkflow/UpdateStatus",
            data: { status: val.checked, Id: val.id },
            success: function (data) {
                if (data) {
                    alert("Status updated successfully");
                }
                else {
                    alert("Status not updated");
                }
            },
            error: function (data) {
                alert("Error occured!!" + data)
            }


        });

    }



</script>

<script src="~/js/manageworkflow.js"></script>
