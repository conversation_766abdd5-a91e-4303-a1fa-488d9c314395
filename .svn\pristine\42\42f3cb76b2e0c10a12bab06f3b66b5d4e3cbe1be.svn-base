using ContinuityPatrol.Application.Features.BackUp.Events.Update;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.BackUp.Commands.Update;

public class UpdateBackUpCommandHandler : IRequestHandler<UpdateBackUpCommand, UpdateBackUpResponse>
{
    private readonly IBackUpLogRepository _backUpLogRepository;
    private readonly IBackUpRepository _backUpRepository;
    private readonly IJobScheduler _client;
    private readonly IMapper _mapper;
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;
    private readonly IPublisher _publisher;
    private readonly IWindowsService _windowsService;

    public UpdateBackUpCommandHandler(IMapper mapper, IBackUpRepository backUpRepository, IPublisher publisher,
        ILoadBalancerRepository nodeConfigurationRepository, IWindowsService windowsService, IBackUpLogRepository backUpLogRepository, IJobScheduler client)
    {
        _mapper = mapper;
        _backUpRepository = backUpRepository;
        _publisher = publisher;
        _nodeConfigurationRepository = nodeConfigurationRepository;
        _windowsService = windowsService;
        _backUpLogRepository = backUpLogRepository;
        _client = client;
    }

    public async Task<UpdateBackUpResponse> Handle(UpdateBackUpCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _backUpRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.BackUp), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateBackUpCommand), typeof(Domain.Entities.BackUp));

        await _backUpRepository.UpdateAsync(eventToUpdate);


        var nodeConfig =
            await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory("ALL",ServiceType.LoadBalancer.ToString())
            ?? await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(), ServiceType.LoadBalancer.ToString());

        if (nodeConfig is null) throw new InvalidException("LoadBalancer not configured!.");
        var baseUrl = $"{nodeConfig.ConnectionType}://{nodeConfig.IPAddress}:{nodeConfig.Port}";

        var monitorUrl = UrlHelper.GenerateMonitorCheckUrl(nodeConfig.TypeCategory, baseUrl);

        var monitorResponse = await _windowsService.CheckWindowsService(monitorUrl);

        if (!monitorResponse.Success) throw new WindowServiceException(monitorResponse.InActiveNodes, ServiceType.Monitor.ToString(), monitorResponse.Message);
       
        var backUpLog = new Domain.Entities.BackUpLog
        {
            BackUpPath = eventToUpdate.BackUpPath,
            HostName = eventToUpdate.HostName,
            DatabaseName = eventToUpdate.DatabaseName,
            UserName = eventToUpdate.UserName,
            IsLocalServer = eventToUpdate.IsLocalServer,
            IsBackUpServer = eventToUpdate.IsBackUpServer,
            Type = eventToUpdate.BackUpType,
            Status = "Pending",
            Properties= eventToUpdate.Properties
        };

        backUpLog = await _backUpLogRepository.AddAsync(backUpLog);

        await _publisher.Publish(new BackUpUpdatedEvent { Name = eventToUpdate.DatabaseName }, cancellationToken);

        var url = UrlHelper.GenerateBackUptUrl(nodeConfig.TypeCategory, baseUrl, backUpLog.ReferenceId); 

        await _client.ScheduleJob(backUpLog.ReferenceId, new Dictionary<string, string> { ["url"] = url });

        var response = new UpdateBackUpResponse
        {
            Message = Message.Update(nameof(Domain.Entities.BackUp), eventToUpdate.DatabaseName),

            Id = eventToUpdate.ReferenceId
        };

       

        return response;
    }
}