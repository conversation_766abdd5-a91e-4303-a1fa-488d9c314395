using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MSSQLAlwaysOnMonitorLogsFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "MSSQLAlwaysOn";

    public List<MSSQLAlwaysOnMonitorLogs> MSSQLAlwaysOnMonitorLogsPaginationList { get; set; }
    public List<MSSQLAlwaysOnMonitorLogs> MSSQLAlwaysOnMonitorLogsList { get; set; }
    public MSSQLAlwaysOnMonitorLogs MSSQLAlwaysOnMonitorLogsDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public MSSQLAlwaysOnMonitorLogsFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<MSSQLAlwaysOnMonitorLogs>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow));

        MSSQLAlwaysOnMonitorLogsPaginationList = _fixture.CreateMany<MSSQLAlwaysOnMonitorLogs>(20).ToList();
        MSSQLAlwaysOnMonitorLogsList = _fixture.CreateMany<MSSQLAlwaysOnMonitorLogs>(5).ToList();
        MSSQLAlwaysOnMonitorLogsDto = _fixture.Create<MSSQLAlwaysOnMonitorLogs>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public MSSQLAlwaysOnMonitorLogs CreateMSSQLAlwaysOnMonitorLogsWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<MSSQLAlwaysOnMonitorLogs>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
       
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public MSSQLAlwaysOnMonitorLogs CreateMSSQLAlwaysOnMonitorLogsWithWhitespace()
    {
        return CreateMSSQLAlwaysOnMonitorLogsWithProperties(type: "  MSSQLAlwaysOn  ");
    }

    public MSSQLAlwaysOnMonitorLogs CreateMSSQLAlwaysOnMonitorLogsWithLongType(int length)
    {
        var longType = new string('A', length);
        return CreateMSSQLAlwaysOnMonitorLogsWithProperties(type: longType);
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonTypes = { "MSSQLAlwaysOn", "AlwaysOn", "MSSQL", "SQLServer" };
        public static readonly string[] CommonInfraObjectIds = { "INFRA_001", "INFRA_002", "INFRA_003" };
        public static readonly string[] CommonWorkflowIds = { "WF_001", "WF_002", "WF_003" };
    }
}
