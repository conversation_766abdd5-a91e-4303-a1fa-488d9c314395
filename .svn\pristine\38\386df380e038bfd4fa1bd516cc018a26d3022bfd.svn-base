﻿//Workflow Analytics

namespace ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetWorkflowAnalytics;

public class GetWorkflowAnalyticsQueryHandler : IRequestHandler<GetWorkflowAnalyticsQuery, GetWorkflowAnalyticsDetailVm>
{
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;
    private readonly IWorkflowRepository _workflowRepository;

    public GetWorkflowAnalyticsQueryHandler(IWorkflowOperationGroupRepository workflowOperationGroupRepository,
        IWorkflowRepository workflowRepository)
    {
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
        _workflowRepository = workflowRepository;
    }

    public async Task<GetWorkflowAnalyticsDetailVm> Handle(GetWorkflowAnalyticsQuery request,
        CancellationToken cancellationToken)
    {
        //var workflowList = await _workflowRepository.ListAllAsync();

        //var workflowOperationGroupList = await _workflowOperationGroupRepository.ListAllAsync();

        //var operationalReadinessAnalyticsVm = new GetWorkflowAnalyticsDetailVm
        //{
        //    TotalConfigured = workflowList.Count
        //};

        //var data = workflowList.ForEach(x => 
        //{
        //    var workflowOperationGroup =
        //        workflowOperationGroupList.Where(wo => wo.WorkflowId.Equals(x.ReferenceId)).ToList();
        //    if (workflowOperationGroup.Count > 0)
        //    {
        //        operationalReadinessAnalyticsVm.WorkFlowSuccessCount +=
        //            workflowOperationGroup.Count(w => w.Status.ToLower() == "completed");
        //        operationalReadinessAnalyticsVm.WorkFlowErrorCount += workflowOperationGroup.Count(w =>
        //            w.Status.ToLower().Equals("abort") || w.Status.ToLower().Equals("aborted"));
        //    }
        //});
        //operationalReadinessAnalyticsVm.ExecutedWorkFlowCount = operationalReadinessAnalyticsVm.WorkFlowSuccessCount +
        //                                                        operationalReadinessAnalyticsVm.WorkFlowErrorCount;
        //return operationalReadinessAnalyticsVm;
        //*******
     

        var workflowList = await _workflowRepository.ListAllAsyncForDashboardView();

        var workflowOperationGroupList = await _workflowOperationGroupRepository.ListAllAsyncForDashBoardView();

        var operationalReadinessAnalyticsVm = new GetWorkflowAnalyticsDetailVm
        {
            TotalConfigured = workflowList.Count
        };

        var workflowOperationGroupsByReferenceId = workflowOperationGroupList
            .GroupBy(wo => wo.WorkflowId)
            .ToDictionary(g => g.Key, g => g.ToList());


        foreach (var workflow in workflowList)
        {
            if (workflowOperationGroupsByReferenceId.TryGetValue(workflow.ReferenceId, out var workflowOperationGroup))
            {
                operationalReadinessAnalyticsVm.WorkFlowSuccessCount +=
                    workflowOperationGroup.Count(w => string.Equals(w.Status, "completed", StringComparison.OrdinalIgnoreCase));
                operationalReadinessAnalyticsVm.WorkFlowErrorCount +=
                    workflowOperationGroup.Count(w =>
                        string.Equals(w.Status, "abort", StringComparison.OrdinalIgnoreCase) ||
                        string.Equals(w.Status, "aborted", StringComparison.OrdinalIgnoreCase));
            }
        }

        operationalReadinessAnalyticsVm.ExecutedWorkFlowCount =
            operationalReadinessAnalyticsVm.WorkFlowSuccessCount + operationalReadinessAnalyticsVm.WorkFlowErrorCount;

        return operationalReadinessAnalyticsVm;
    }
}