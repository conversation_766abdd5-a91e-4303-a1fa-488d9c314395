﻿let mId = sessionStorage.getItem("monitorId")
let monitortype = 'OracleFullDBEmcSrdf';
let infraObjectId = sessionStorage.getItem("infraobjectId");

setTimeout(() => { oracleFulldbEmcsrdfmonitorstatus(mId, monitortype) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)

async function oracleFulldbEmcsrdfmonitorstatus(id, type) {
    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noData);
    }
}
let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">';
let asmNoDataimg = '<img src="/img/isomatric/nodatalag.svg" class="mx-auto"> <br><span class="text-danger">No data available</span>';
let noData = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'

$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})
function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}
function propertiesData(value) {
    if (!value) {
        $("#noDataimg").css('text-align', 'center').html(noData);
        return
    } else {
        let data = JSON.parse(value?.properties)
        //let data = global
        console.log(data, 'emcdata')
        oracleReplicationDetails(data?.ReplicationMonitoring)
        let customSite = data?.OracleFullDBEmcSrdfModels?.length > 1;
        if (customSite) {
            $("#Sitediv").show();
        } else {
            $("#Sitediv").hide();
        }

        $(".siteContainer").empty();
        data?.OracleFullDBEmcSrdfModels?.forEach((a, index) => {
            let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
            $(".siteContainer").append(selectTab);
        });
        if (data?.OracleFullDBEmcSrdfModels?.length > 0) {
            $("#siteName0 .nav-link").addClass("active");
            displaySiteData(data?.OracleFullDBEmcSrdfModels[0]);
        }
        let defaultSite = data?.OracleFullDBEmcSrdfModels?.find(d => d?.Type === 'DR') || data?.OracleFullDBEmcSrdfModels[0]; 
        if (defaultSite) {
            displaySiteData(defaultSite);
        }
        $(document).on('click', '.siteListChange', function () {
            $(".siteListChange .nav-link").removeClass("active");
            $(this).find(".nav-link").addClass("active");
            let siteId = $(this)[0]?.id
            let getSiteName = $(`#${siteId} .siteName`).text()

            let MonitoringModel = data?.OracleFullDBEmcSrdfModels?.find(d => d?.Type === getSiteName);
            if (MonitoringModel) {
                displaySiteData(MonitoringModel, getSiteName);
            }
            bindMonitoringServices(globalMSSQLServerData, getSiteName);
        });
        function displaySiteData(siteData) {
            
            let obj = {};
            $('.dynamicSite-header').text(siteData?.Type).attr('title', siteData?.Type);

            for (let key in siteData?.MonitoringModel) {
                obj[`DR_` + key] = siteData?.MonitoringModel[key];
            }
            if (obj['DR_Dbsize']) {
                obj['DR_Dbsize'] += " MB";
            }
            let MonitoringModelOracleEmcsrdf = [
                "DR_Database_Sid", "DR_Unique_Name", "DR_Database_role", "DR_Openmode", "DR_Database_createdtime", "DR_Control_filetype", "DR_Currentscn", "DR_Flashback_on", "DR_Database_Version", "DR_Database_incarnation", "DR_DB_Reset_logschange", "DR_Reset_logsmode", "DR_Dbsize", "DR_Db_create_file_dest", "DR_Db_file_name_convert", "DR_Db_create_online_log_dest1", "DR_Log_file_name_convert", "DR_Db_recovery_file_dest", "DR_Db_recovery_file_dest_size", "DR_Db_flashback_retention_target", "DR_InstanceName", "DR_InstanceId", "DR_InstanceStartUpTime", "DR_OpenMode", "DR_IsClusterDatabase", "DR_Control_filename", "DR_Parameterfile", "DR_Platform_name", "DR_CDB", "DR_Containers", "DR_Pdbs", "DR_TNSServiceName", "DR_Archive_mode"
            ]
            if (Object.keys(obj).length > 0) {
                bindProperties(obj, MonitoringModelOracleEmcsrdf, value);
            }
        }
        let dbDetail = data?.PrOracleFullDBEmcSrdfModel?.PrMonitoringModel;
        const dbDetailsProp = [
            "PR_Database_Sid", "PR_Unique_Name", "PR_Database_role", "PR_Openmode", "PR_Database_createdtime", "PR_Control_filetype", "PR_Currentscn", "PR_Flashback_on", "PR_Database_Version", "PR_Database_incarnation", "PR_DB_Reset_logschange", "PR_Reset_logsmode", "PR_Dbsize", "PR_Db_create_file_dest", "PR_Db_file_name_convert", "PR_Db_create_online_log_dest1", "PR_Log_file_name_convert", "PR_Db_recovery_file_dest", "PR_Db_recovery_file_dest_size", "PR_Db_flashback_retention_target", "PR_InstanceName", "PR_InstanceId", "PR_InstanceStartUpTime", "PR_OpenMode", "PR_IsClusterDatabase", "PR_Control_filename", "PR_Parameterfile", "PR_Platform_name", "PR_CDB", "PR_Containers", "PR_Pdbs", "PR_TNSServiceName", "PR_Archive_mode"
        ]
        var prDbsizeValue = checkAndReplace(dbDetail?.PR_Dbsize);
        //var drDbsizeValue = checkAndReplace(data?.DR_Dbsize);

        if (prDbsizeValue === 'NA') {
            $('#PR_Dbsize').text(prDbsizeValue).attr('title', prDbsizeValue);
        } else if (prDbsizeValue?.includes("MB")) {
            $('#PR_Dbsize').text(prDbsizeValue).attr('title', prDbsizeValue);
        }
        else {
            $('#PR_Dbsize').text(prDbsizeValue + " MB").attr('title', prDbsizeValue + " MB");
        }

        displayASM(data?.PrOracleFullDBEmcSrdfModel?.PrMonitoringModel?.PR_Asm_Details, '#asmDataPR', '#asmPRData');
        displayASM(data?.OracleFullDBEmcSrdfModels[0]?.
            MonitoringModel?.Asm_Details, '#asmDataDR', '#asmDRData');

        bindProperties(dbDetail, dbDetailsProp);
    }
}
function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined || value === 'NA') ? 'NA' : value;
}
$('#asmContainer').hide()
function displayASM(val, target, element) {
    if (!val) {
        $('#asmContainer').hide()
        $(element).css('text-align', 'center').html(asmNoDataimg)
        return
    } else {
        let data = val.replace(/,(?=\s*[\]}])/, '');       
        let asmVal = JSON?.parse(data);              
        if (asmVal?.length > 0) {
            $('#asmContainer').show()
            const asmRows = asmVal.map((list, i) => `<tr><td>${i + 1}</td><td>${list.NAME}</td><td>${list.STATE}</td><td>${list.TYPE}</td><td>${list.TOTAL_MB}</td><td>${list.FREE_MB}</td><td>${list['USED(%)']}</td></tr>`).join('');
            $(target).append(asmRows);
        }
        else {
        $('#asmContainer').hide()
        $(element)
            .css('text-align', 'center')
            .html(asmNoDataimg);
    }
    }
}
function setPropData(data, propSets) {
    propSets?.forEach(properties => {
        bindProperties(data, properties);
    });
}
let iconClass
function bindProperties(data, properties, value) {
    properties?.forEach(property => {
        const values = data[property];
        const displayedValue = (value !== undefined || value !== '') ? checkAndReplace(values) : 'NA';
        const iconHtml = getIconClass(displayedValue, property, data, value);
        const mergeValue = `${iconHtml}${displayedValue}`;
        $(`#${property}`).html(mergeValue).attr('title', displayedValue);
    });
}
function oracleReplicationDetails(data) {    
    $('#oracleReplication').empty();

    const rows = [
        {
            label: "Replication Type",
            key: "ReplicationType",
            icon: "cp-replication-type"
        },
        {
            label: "Device Group Name",
            key: "DeviceGroupName",
            icon: "cp-group"
        },
        {
            label: "Disk Group Type",
            key: "DiskGroupType",
            icon: "cp-disk-controller"
        },
        {
            label: "Disk Group Symmetrix Id",
            key: "DiskGroupSymmetrixId",
            icon: "cp-degrade-disk"
        },
        {
            label: "Remote Group Symmetrix Id",
            key: "RemoteGroupSymmetrixId",
            icon: "cp-remote-login"
        },
        {
            label: "RDF (RA) Group Number",
            key: "RDFGroupNumber",
            icon: "cp-RDFG"
        },
        {
            label: "Device State",
            key: "DeviceState",
            icon: "cp-state"
        },
        {
            label: "Pending Tracks",
            key: "PendingTask",
            icon: "cp-pending"
        },
        {
            label: "Lag(R2 Behind R1)",
            key: "Lag",
            icon: "cp-time"
        }
    ];

    let repRow = ``;

    rows?.forEach(row => {
        const value = data?.[row.key] ?? 'NA';
        const iconClass = value !== 'NA'
            ? `${row.icon} me-1 fs-6 text-primary`
            : `cp-disable me-1 fs-6 text-danger`;

        repRow += `
            <tr>
                <td class="fw-semibold text-truncate"><i class="text-secondary ${row.icon} me-1"></i>${row.label}</td>
                <td class="text-truncate"><i class="${iconClass}"></i>${value}</td>
            </tr>
        `;
    });

    $('#oracleReplication').append(repRow);
}

function getIconClass(displayedValue, property, data) {
    let prdbFile = data?.PR_Db_file_name_convert ? "text-primary cp-generate me-1 fs-6" : "text-danger cp-disable"
    let drdbFile = data?.DR_Db_file_name_convert ? "text-primary cp-generate me-1 fs-6" : "text-danger cp-disable"
    let prdestSize = data?.PR_Db_recovery_file_dest_size ? "text-primary cp-roate-settings me-1 fs-6" : "text-danger cp-disable"
    let drdestSize = data?.DR_Db_recovery_file_dest_size ? "text-primary cp-roate-settings me-1 fs-6" : "text-danger cp-disable"
    let prlog = data?.PR_Log_file_name_convert ? "text-primary cp-log-file-name me-1 fs-6" : "text-danger cp-disable"
    let drlog = data?.DR_Log_file_name_convert ? "text-primary cp-log-file-name me-1 fs-6" : "text-danger cp-disable"
    let prFal = data?.PR_Fal_server ? "text-primary cp-fal-server me-1 me-1 fs-6" : "text-danger cp-disable"
    let drFal = data?.DR_Fal_server ? "text-primary cp-fal-server me-1 me-1 fs-6" : "text-danger cp-disable"
    let prFalClient = data?.PR_Fal_client ? "text-primary cp-fal-client me-1 me-1 fs-6" : "text-danger cp-disable"
    let drFalClient = data?.DR_Fal_client ? "text-primary cp-fal-client me-1 me-1 fs-6" : "text-danger cp-disable"
    let prunique = data?.PR_Unique_Name ? "text-primary cp-database-unique-name me-1 fs-6" : "text-danger cp-disable"
    let drunique = data?.DR_Unique_Name ? "text-primary cp-database-unique-name me-1 fs-6" : "text-danger cp-disable"
    let prTime = data?.PR_Database_createdtime ? "text-primary cp-time me-1 fs-6" : "text-danger cp-disable"
    let drTime = data?.DR_Database_createdtime ? "text-primary cp-time me-1 fs-6" : "text-danger cp-disable"
    let prdbVersion = data?.PR_Database_version ? "text-primary cp-version me-1 fs-6" : "text-danger cp-disable"
    let drdbVersion = data?.DR_Database_version ? "text-primary cp-version me-1 fs-6" : "text-danger cp-disable"
    let prArchieve = data?.PR_Archive_mode ? "text-success cp-archive-mode me-1 fs-6" : "text-danger cp-disable"
    let drArchieve = data?.DR_Archive_mode ? "text-success cp-archive-mode me-1 fs-6" : "text-danger cp-disable"
    let prInstance = data?.PR_InstanceName ? "text-primary cp-instance-name me-1 fs-6" : "text-danger cp-disable"
    let drInstance = data?.DR_InstanceName ? "text-primary cp-instance-name me-1 fs-6" : "text-danger cp-disable"
    let prInstanceId = data?.PR_InstanceId ? "text-primary cp-instance-id me-1 fs-6" : "text-danger cp-disable"
    let drInstanceId = data?.DR_InstanceId ? "text-primary cp-instance-id me-1 fs-6" : "text-danger cp-disable"
    let prInstancestart = data?.PR_InstanceStartUpTime ? "text-primary cp-timer-meter me-1 fs-6" : "text-danger cp-disable"
    let drInstancestart = data?.DR_InstanceStartUpTime ? "text-primary cp-timer-meter me-1 fs-6" : "text-danger cp-disable"
    let prDBlog = data?.PR_DB_Reset_logschange ? "text-primary cp-reset-log-change me-1 fs-6" : "text-danger cp-disable"
    let drDBlog = data?.DR_DB_Reset_logschange ? "text-primary cp-reset-log-change me-1 fs-6" : "text-danger cp-disable"
    let prFile = data?.PR_Parameterfile ? "text-primary cp-parameter-file me-1 fs-6" : "text-danger cp-disable"
    let drFile = data?.DR_Parameterfile ? "text-primary cp-parameter-file me-1 fs-6" : "text-danger cp-disable"
    let prPlatform = data?.PR_Platform_name ? "text-primary cp-platform-name me-1 fs-6" : "text-danger cp-disable"
    let drPlatform = data?.DR_Platform_name ? "text-primary cp-platform-name me-1 fs-6" : "text-danger cp-disable"
    let prDatabase = data?.PR_Database_Sid ? "text-primary cp-database me-1 fs-6" : "text-danger cp-disable"
    let drDatabase = data?.DR_Database_Sid ? "text-primary cp-database me-1 fs-6" : "text-danger cp-disable"

    const iconMapping = {
        'PR_Db_file_name_convert': prdbFile,
        'DR_Db_file_name_convert': drdbFile,
        'PR_Db_recovery_file_dest_size': prdestSize,
        'DR_Db_recovery_file_dest_size': drdestSize,
        'PR_Log_file_name_convert': prlog,
        'DR_Log_file_name_convert': drlog,
        'PR_Fal_server': prFal,
        'DR_Fal_server': drFal,
        'PR_Fal_client': prFalClient,
        'DR_Fal_client': drFalClient,
        'PR_Unique_Name': prunique,
        'DR_Unique_Name': drunique,
        'PR_Database_createdtime': prTime,
        'DR_Database_createdtime': drTime,
        'PR_Database_Version': prdbVersion,
        'DR_Database_Version': drdbVersion,
        'PR_Archive_mode': prArchieve,
        'DR_Archive_mode': drArchieve,
        'PR_InstanceName': prInstance,
        'DR_InstanceName': drInstance,
        'PR_InstanceId': prInstanceId,
        'DR_InstanceId': drInstanceId,
        'PR_InstanceStartUpTime': prInstancestart,
        'DR_InstanceStartUpTime': drInstancestart,
        'PR_DB_Reset_logschange': prDBlog,
        'DR_DB_Reset_logschange': drDBlog,
        'PR_Parameterfile': prFile,
        'DR_Parameterfile': drFile,
        'PR_Platform_name': prPlatform,
        'DR_Platform_name': drPlatform,
        'PR_Database_Sid': prDatabase,
        'DR_Database_Sid': drDatabase
    }
    iconClass = iconMapping[property] || '';
    switch (displayedValue?.toLowerCase()) {
        case 'not allowed':
        case 'no':
        case 'na':
            iconClass = 'text-danger cp-disable';
            break;
        case 'manual':
            iconClass = 'text-warning cp-settings';
            break;
        case 'healthy':
            iconClass = 'text-success cp-health-success';
            break;
        case 'nothealthy':
        case 'not_healthy':
        case 'unhealthy':
            iconClass = 'text-danger cp-health-error';
            break;
        case 'online':
            iconClass = 'text-success cp-online';
            break;
        case 'offline':
            iconClass = 'text-danger cp-offline';
            break;
        case 'primary':
            iconClass = 'text-primary cp-list-prsite';
            break;
        case 'secondary':
            iconClass = 'text-info cp-dr';
            break;
        case 'physical standby':
            iconClass = 'text-info cp-physical-drsite';
            break;
        case 'connected':
        case 'connect':
            iconClass = 'text-success cp-connected';
            break;
        case 'disconnected':
        case 'disconnect':
            iconClass = 'text-danger cp-disconnected';
            break;
        case 'synchronous_commit':
        case 'synchronized':
        case 'synchronizing':
        case 'sync':
            iconClass = 'text-success cp-refresh';
            break;
        case 'asynchronous_commit':
        case 'notsynchronizing ':
        case 'notsynchronized':
        case 'not':
        case 'not synchronized':
        case 'not synchronizing':
        case 'asynchronizing':
        case 'asynchronized':
        case 'async':
            iconClass = 'text-danger cp-refresh';
            break;
        case 'pending':
            iconClass = 'text-warning cp-pending';
            break;
        case 'running':
        case 'run':
            iconClass = 'text-success cp-reload cp-animate';
            break;
        case 'error':
            iconClass = 'text-danger cp-fail-back';
            break;
        case 'stopped':
        case 'stop':
            iconClass = 'text-danger cp-Stopped';
            break;
        case 'standby':
        case 'to standby':
        case 'mounted':
            iconClass = 'text-warning cp-control-file-type';
            break;
        case 'enabled':
        case 'enable':
            iconClass = 'text-success cp-enables';
            break;
        case 'disabled':
        case 'disable':
            iconClass = 'text-danger cp-disables';
            break;
        case 'true':
        case 'yes':
            iconClass = 'text-success cp-agree';
            break;
        case 'valid':
            iconClass = 'text-success cp-success';
            break;
        case 'false':
        case 'defer':
        case 'deferred':
            iconClass = 'text-danger cp-error';
            break;
        case 'pause':
        case 'paused':
            iconClass = 'text-warning cp-circle-pause';
            break;
        case 'required':
        case 'require':
            iconClass = 'text-warning cp-warning';
            break;
        case 'on':
            iconClass = 'text-success cp-end';
            break;
        case 'off':
            iconClass = 'text-danger cp-end';
            break;
        case 'current':
        case 'read write':
            iconClass = 'text-success cp-file-edits';
            break;
        default:
            break;
    }
    return iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
}