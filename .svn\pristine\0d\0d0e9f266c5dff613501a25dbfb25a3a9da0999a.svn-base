﻿using ContinuityPatrol.Application.Features.WorkflowPermission.Events.PaginatedView;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowPermission.Events
{
    public class PaginatedWorkflowPermissionEventTests
    {
        private readonly Mock<ILogger<WorkflowPermissionPaginatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly WorkflowPermissionPaginatedEventHandler _handler;

        public PaginatedWorkflowPermissionEventTests()
        {
            _mockLogger = new Mock<ILogger<WorkflowPermissionPaginatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _handler = new WorkflowPermissionPaginatedEventHandler(
                _mockLogger.Object,
                _mockUserActivityRepository.Object,
                _mockUserService.Object
            );
        }

        [Fact]
        public async Task Handle_LogsInformation_WhenEventHandled()
        {
            var notification = new WorkflowPermissionPaginatedEvent();

            var userId = "test-user-id";
            var loginName = "test-login-name";
            var companyId = "test-company-id";
            var requestedUrl = "test-request-url";
            var ipAddress = "127.0.0.1";

            _mockUserService.Setup(x => x.UserId).Returns(userId);
            _mockUserService.Setup(x => x.LoginName).Returns(loginName);
            _mockUserService.Setup(x => x.CompanyId).Returns(companyId);
            _mockUserService.Setup(x => x.RequestedUrl).Returns(requestedUrl);
            _mockUserService.Setup(x => x.IpAddress).Returns(ipAddress);

            var handler = new WorkflowPermissionPaginatedEventHandler(
                _mockLogger.Object,
                _mockUserActivityRepository.Object,
                _mockUserService.Object
            );

            await handler.Handle(notification, CancellationToken.None);

            _mockLogger.Verify(
                logger => logger.LogInformation(It.IsAny<string>()),
                Times.Once,
                "LogInformation was not called as expected"
            );

            _mockLogger.Verify(
                logger => logger.LogInformation(It.Is<string>(msg => msg.Contains("User Privileges viewed"))),
                Times.Once,
                "The log message did not contain the expected text"
            );
        }

        [Fact]
        public async Task Handle_CreatesUserActivity_WhenEventHandled()
        {
            var notification = new WorkflowPermissionPaginatedEvent();

            _mockUserService.Setup(s => s.UserId).Returns("test-user-id");
            _mockUserService.Setup(s => s.LoginName).Returns("test-login");
            _mockUserService.Setup(s => s.CompanyId).Returns("test-company-id");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://test-url.com");
            _mockUserService.Setup(s => s.IpAddress).Returns("127.0.0.1");

            await _handler.Handle(notification, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "test-user-id" &&
                activity.LoginName == "test-login" &&
                activity.CompanyId == "test-company-id" &&
                activity.RequestUrl == "http://test-url.com" &&
                activity.HostAddress == "127.0.0.1" &&
                activity.Entity == Modules.WorkflowPermission.ToString() &&
                activity.Action == $"{ActivityType.View} {Modules.WorkflowPermission}" &&
                activity.ActivityType == ActivityType.View.ToString() &&
                activity.ActivityDetails == "User Privileges viewed"
            )), Times.Once);
        }
    }
}