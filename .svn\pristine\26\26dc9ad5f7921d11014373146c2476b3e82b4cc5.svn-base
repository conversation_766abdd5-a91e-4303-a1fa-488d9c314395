﻿using ContinuityPatrol.Application.Features.Site.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Site.Commands;

public class UpdateSiteTests : IClassFixture<SiteFixture>
{
    private readonly SiteFixture _siteFixture;

    private readonly Mock<ISiteRepository> _mockSiteRepository;

    private readonly UpdateSiteCommandHandler _handler;

    public UpdateSiteTests(SiteFixture siteFixture)
    {
        _siteFixture = siteFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockSiteRepository = SiteRepositoryMocks.UpdateSiteRepository(_siteFixture.Sites);

        _handler = new UpdateSiteCommandHandler(_siteFixture.Mapper, _mockSiteRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidSite_UpdateReferenceIdAsync_ToSitesRepo()
    {
        _siteFixture.UpdateSiteCommand.Id = _siteFixture.Sites[0].ReferenceId;

        var result = await _handler.Handle(_siteFixture.UpdateSiteCommand, CancellationToken.None);

        var site = await _mockSiteRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_siteFixture.UpdateSiteCommand.Name, site.Name);
    }

    [Fact]
    public async Task Handle_Return_ValidSiteResponse_WhenUpdate_Site()
    {
        _siteFixture.UpdateSiteCommand.Id = _siteFixture.Sites[0].ReferenceId;

        var result = await _handler.Handle(_siteFixture.UpdateSiteCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateSiteResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_siteFixture.UpdateSiteCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidSiteId()
    {
        _siteFixture.UpdateSiteCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_siteFixture.UpdateSiteCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        _siteFixture.UpdateSiteCommand.Id = _siteFixture.Sites[0].ReferenceId;

        await _handler.Handle(_siteFixture.UpdateSiteCommand, CancellationToken.None);

        _mockSiteRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockSiteRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.Site>()), Times.Once);
    }
}