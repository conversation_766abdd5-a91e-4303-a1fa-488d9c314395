﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.AlertReceiverModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetPaginatedList;

public class GetAlertReceiverPaginatedListQueryHandler : IRequestHandler<GetAlertReceiverPaginatedListQuery,
    PaginatedResult<AlertReceiverListVm>>
{
    private readonly IAlertReceiverRepository _alertReceiverRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public GetAlertReceiverPaginatedListQueryHandler(IMapper mapper, IPublisher publisher,
        IAlertReceiverRepository alertReceiverRepository)
    {
        _mapper = mapper;
        _publisher = publisher;
        _alertReceiverRepository = alertReceiverRepository;
    }

    public async Task<PaginatedResult<AlertReceiverListVm>> Handle(GetAlertReceiverPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new AlertReceiverFilterSpecification(request.SearchString);

        var queryable =await _alertReceiverRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var alertReceiversList = _mapper.Map<PaginatedResult<AlertReceiverListVm>>(queryable);

        return alertReceiversList;


        //var queryable = _alertReceiverRepository.PaginatedListAllAsync();

        //var productFilterSpec = new AlertReceiverFilterSpecification(request.SearchString);

        //var alertReceiversList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<AlertReceiverListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        ////await _publisher.Publish(new AlertReceiverPaginatedEvent(), cancellationToken);

        //return alertReceiversList;
    }
}