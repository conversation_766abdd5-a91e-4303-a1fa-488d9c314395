﻿using ContinuityPatrol.Application.Features.SiteType.Events.PaginatedView;

namespace ContinuityPatrol.Application.UnitTests.Features.SiteType.Events
{
    public class SiteTypePaginatedEventTests
    {
        private readonly Mock<ILogger<SiteTypePaginatedEventHandler>> _loggerMock;
        private readonly Mock<IUserActivityRepository> _userActivityRepositoryMock;
        private readonly Mock<ILoggedInUserService> _userServiceMock;
        private readonly SiteTypePaginatedEventHandler _handler;

        public SiteTypePaginatedEventTests()
        {
            _loggerMock = new Mock<ILogger<SiteTypePaginatedEventHandler>>();
            _userActivityRepositoryMock = new Mock<IUserActivityRepository>();
            _userServiceMock = new Mock<ILoggedInUserService>();

            _handler = new SiteTypePaginatedEventHandler(
                _userServiceMock.Object,
                _loggerMock.Object,
                _userActivityRepositoryMock.Object);
        }

        [Fact]
        public async Task Handle_ShouldLogAndAddUserActivity_WhenEventIsHandled()
        {
            var paginatedEvent = new SiteTypePaginatedEvent();
            var cancellationToken = CancellationToken.None;

            _userServiceMock.Setup(service => service.UserId).Returns("12345");
            _userServiceMock.Setup(service => service.LoginName).Returns("TestLogin");
            _userServiceMock.Setup(service => service.RequestedUrl).Returns("http://test.com");
            _userServiceMock.Setup(service => service.IpAddress).Returns("127.0.0.1");
            _userServiceMock.Setup(service => service.CompanyId).Returns("67890");

            await _handler.Handle(paginatedEvent, cancellationToken);

            _userActivityRepositoryMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "12345" &&
                activity.LoginName == "TestLogin" &&
                activity.RequestUrl == "http://test.com" &&
                activity.HostAddress == "127.0.0.1" &&
                activity.CompanyId == "67890" &&
                activity.Entity == "SiteType" &&
                activity.Action == "View SiteType" &&
                activity.ActivityType == "View" &&
                activity.ActivityDetails == "Site Type viewed"
            )), Times.Once);

            _loggerMock.Verify(logger => logger.LogInformation("Site Type viewed"), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldNotThrowException_WhenHandledSuccessfully()
        {
            var paginatedEvent = new SiteTypePaginatedEvent();
            var cancellationToken = CancellationToken.None;

            _userServiceMock.Setup(service => service.UserId).Returns("12345");

            await _handler.Handle(paginatedEvent, cancellationToken);

            _userActivityRepositoryMock.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);

            _loggerMock.Verify(logger => logger.LogInformation(It.IsAny<string>()), Times.Once);
        }
    }
}
