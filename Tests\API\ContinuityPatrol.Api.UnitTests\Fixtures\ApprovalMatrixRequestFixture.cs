using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Withdraw;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetByRequestId;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixRequestModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class ApprovalMatrixRequestFixture
{
    public List<ApprovalMatrixRequestListVm> ApprovalMatrixRequestListVm { get; }
    public List<ApprovalMatrixByRequestIdVm> ApprovalMatrixByRequestIdVm { get; }
    public ApprovalMatrixRequestDetailVm ApprovalMatrixRequestDetailVm { get; }
    public CreateApprovalMatrixRequestCommand CreateApprovalMatrixRequestCommand { get; }
    public UpdateApprovalMatrixRequestCommand UpdateApprovalMatrixRequestCommand { get; }
    public WithdrawApprovalMatrixRequestCommand WithdrawApprovalMatrixRequestCommand { get; }

    public ApprovalMatrixRequestFixture()
    {
        var fixture = new Fixture();

        // Create sample ApprovalMatrixRequest list data
        ApprovalMatrixRequestListVm = new List<ApprovalMatrixRequestListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                RequestId = "REQ_001",
                ApprovalMatrixId = Guid.NewGuid().ToString(),
                ProcessName = "Budget Approval Request",
                Description = "Annual budget approval for IT department requiring CFO sign-off",
                UserName = "it.manager",
                Status = "Pending",
                Approvers = "finance.manager,cfo",
                StartDateTime = DateTime.Now.AddHours(-2),
                EndDateTime = DateTime.Now.AddDays(7),
                IsRequest = true
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                RequestId = "REQ_002",
                ApprovalMatrixId = Guid.NewGuid().ToString(),
                ProcessName = "Security Access Request",
                Description = "Privileged access request for production systems maintenance",
                UserName = "system.admin",
                Status = "Approved",
                Approvers = "security.manager,ciso",
                StartDateTime = DateTime.Now.AddHours(-6),
                EndDateTime = DateTime.Now.AddDays(1),
                IsRequest = true
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                RequestId = "REQ_003",
                ApprovalMatrixId = Guid.NewGuid().ToString(),
                ProcessName = "Employee Termination Process",
                Description = "Employee termination process requiring HR director approval",
                UserName = "hr.specialist",
                Status = "In Review",
                Approvers = "hr.manager,hr.director",
                StartDateTime = DateTime.Now.AddHours(-4),
                EndDateTime = DateTime.Now.AddDays(3),
                IsRequest = true
            }
        };

        // Create sample ApprovalMatrixByRequestId data
        ApprovalMatrixByRequestIdVm = new List<ApprovalMatrixByRequestIdVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                RequestId = "REQ_001",
                ProcessName = "Budget Approval Request",
                Description = "Annual budget approval for IT department",
                UserName = "it.manager",
                Status = "Pending",
                ApproverName = "finance.manager",
                Message = "Initial review pending",
                LastModifiedDate = DateTime.Now.AddHours(-2)
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                RequestId = "REQ_001",
                ProcessName = "Budget Approval Request",
                Description = "Annual budget approval for IT department",
                UserName = "it.manager",
                Status = "Pending",
                ApproverName = "cfo",
                Message = "Awaiting first level approval",
                LastModifiedDate = DateTime.Now.AddHours(-1)
            }
        };

        // Create detailed ApprovalMatrixRequest data
        ApprovalMatrixRequestDetailVm = new ApprovalMatrixRequestDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            RequestId = "REQ_DETAIL_001",
            ApprovalMatrixId = Guid.NewGuid().ToString(),
            ProcessName = "Emergency Change Request",
            Description = "Emergency change to production systems for critical bug fix",
            UserName = "emergency.responder",
            Status = "Urgent",
            Approvers = "change.manager,operations.director",
            StartDateTime = DateTime.Now.AddMinutes(-30),
            EndDateTime = DateTime.Now.AddHours(4),
            IsRequest = true
        };

        // Create command for creating ApprovalMatrixRequest
        CreateApprovalMatrixRequestCommand = new CreateApprovalMatrixRequestCommand
        {
            ProcessName = "New Process Request",
            Description = "New business process requiring approval workflow",
            UserName = "process.initiator",
            Status = "Draft",
            Approvers = "department.head,senior.manager",
            StartDateTime = DateTime.Now,
            EndDateTime = DateTime.Now.AddDays(7),
            IsRequest = true
        };

        // Create command for updating ApprovalMatrixRequest
        UpdateApprovalMatrixRequestCommand = new UpdateApprovalMatrixRequestCommand
        {
            Id = Guid.NewGuid().ToString(),
            ProcessName = "Updated Process Request",
            Description = "Updated business process with revised requirements",
            UserName = "process.owner",
            Status = "Under Review",
            Approvers = "updated.manager,senior.director",
            StartDateTime = DateTime.Now,
            EndDateTime = DateTime.Now.AddDays(5),
            IsRequest = true
        };

        // Create command for withdrawing ApprovalMatrixRequest
        WithdrawApprovalMatrixRequestCommand = new WithdrawApprovalMatrixRequestCommand
        {
            Id = Guid.NewGuid().ToString(),
            RequestId = "REQ_WITHDRAW_001",
            Status = "Withdraw"
        };
    }
}
