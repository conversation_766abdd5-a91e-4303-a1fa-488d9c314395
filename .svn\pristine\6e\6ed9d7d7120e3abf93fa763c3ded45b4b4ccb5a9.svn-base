using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Create;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Update;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftImpactTypeMasterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IDriftImpactTypeMasterService
{
    Task<List<DriftImpactTypeMasterListVm>> GetDriftImpactTypeMasterList();
    Task<BaseResponse> CreateAsync(CreateDriftImpactTypeMasterCommand createDriftImpactTypeMasterCommand);
    Task<BaseResponse> UpdateAsync(UpdateDriftImpactTypeMasterCommand updateDriftImpactTypeMasterCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<DriftImpactTypeMasterDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsDriftImpactTypeMasterNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<DriftImpactTypeMasterListVm>> GetPaginatedDriftImpactTypeMasters(GetDriftImpactTypeMasterPaginatedListQuery query);
    #endregion
}
