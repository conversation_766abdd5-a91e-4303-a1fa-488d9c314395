﻿namespace ContinuityPatrol.Application.Models;

public class AuthenticationResponse
{
    public string CompanyId { get; set; }
    public string CompanyName { get; set; }
    public string UserName { get; set; }
    public string UserId { get; set; }
    public string ParentCompanyId { get; set; }
    public string Token { get; set; }
    public string Permissions { get; set; }
    public string TwoFactorAuthentication { get; set; }
    public bool IsReset { get; set; }
    public string RefreshToken { get; set; }
    public string RoleId { get; set; }
    public string RoleName { get; set; }
    public bool IsParent { get; set; }
    public bool IsAuthorized { get; set; }
    public bool IsAllInfra { get; set; }
    public int Expires { get; set; }
    public string AuthenticationType { get; set; }
    public bool IsLicenseValidity { get; set; }
    public bool LicenseEmpty { get; set; }
    public string AssignedInfras { get; set; }
    public bool IsDefaultDashboard { get; set; }
    public string Url { get; set; }
    public DateTime? LastPasswordChanged { get; set; }
}