using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordExpire.Queries;

public class GetAdPasswordExpireNameUniqueQueryTests : IClassFixture<AdPasswordExpireFixture>
{
    private readonly AdPasswordExpireFixture _adPasswordExpireFixture;
    private readonly Mock<IAdPasswordExpireRepository> _mockAdPasswordExpireRepository;
    private readonly GetAdPasswordExpireNameUniqueQueryHandler _handler;

    public GetAdPasswordExpireNameUniqueQueryTests(AdPasswordExpireFixture adPasswordExpireFixture)
    {
        _adPasswordExpireFixture = adPasswordExpireFixture;

        _mockAdPasswordExpireRepository = AdPasswordExpireRepositoryMocks.CreateQueryAdPasswordExpireRepository(_adPasswordExpireFixture.AdPasswordExpires);
        
        _handler = new GetAdPasswordExpireNameUniqueQueryHandler(_mockAdPasswordExpireRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_When_NameExists_And_NoIdProvided()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var query = new GetAdPasswordExpireNameUniqueQuery 
        { 
            Name = existingExpire.UserName,
            Id = null
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_When_NameDoesNotExist_And_NoIdProvided()
    {
        // Arrange
        var query = new GetAdPasswordExpireNameUniqueQuery 
        { 
            Name = "NonExistentUserName",
            Id = null
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_False_When_NameExistsForSameId()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var query = new GetAdPasswordExpireNameUniqueQuery 
        { 
            Name = existingExpire.UserName,
            Id = existingExpire.ReferenceId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_True_When_NameExistsForDifferentId()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var differentId = Guid.NewGuid().ToString();
        var query = new GetAdPasswordExpireNameUniqueQuery 
        { 
            Name = existingExpire.UserName,
            Id = differentId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Call_IsNameExist_OnlyOnce()
    {
        // Arrange
        var query = new GetAdPasswordExpireNameUniqueQuery 
        { 
            Name = "TestName",
            Id = "TestId"
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockAdPasswordExpireRepository.Verify(x => x.IsNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_PassCorrectParameters_When_CallingRepository()
    {
        // Arrange
        var testName = "TestUserName";
        var testId = "TestId";
        var query = new GetAdPasswordExpireNameUniqueQuery 
        { 
            Name = testName,
            Id = testId
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockAdPasswordExpireRepository.Verify(x => x.IsNameExist(testName, testId), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_RepositoryResult_When_QueryExecuted()
    {
        // Arrange
        var query = new GetAdPasswordExpireNameUniqueQuery 
        { 
            Name = "TestName",
            Id = "TestId"
        };

        _mockAdPasswordExpireRepository.Setup(x => x.IsNameExist("TestName", "TestId"))
            .ReturnsAsync(true);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_HandleEmptyName_When_QueryExecuted()
    {
        // Arrange
        var query = new GetAdPasswordExpireNameUniqueQuery 
        { 
            Name = "",
            Id = "TestId"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
        _mockAdPasswordExpireRepository.Verify(x => x.IsNameExist("", "TestId"), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleNullId_When_QueryExecuted()
    {
        // Arrange
        var query = new GetAdPasswordExpireNameUniqueQuery 
        { 
            Name = "TestName",
            Id = null
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockAdPasswordExpireRepository.Verify(x => x.IsNameExist("TestName", null), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleEmptyId_When_QueryExecuted()
    {
        // Arrange
        var query = new GetAdPasswordExpireNameUniqueQuery 
        { 
            Name = "TestName",
            Id = ""
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockAdPasswordExpireRepository.Verify(x => x.IsNameExist("TestName", ""), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnBooleanType_When_QueryExecuted()
    {
        // Arrange
        var query = new GetAdPasswordExpireNameUniqueQuery 
        { 
            Name = "TestName",
            Id = "TestId"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<bool>();
    }
}
