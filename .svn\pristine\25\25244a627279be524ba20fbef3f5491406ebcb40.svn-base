using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Moq;
using System.Data;
using System.Data.Common;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class SVCMssqlMonitorLogRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly SVCMssqlMonitorLogRepository _repository;
    private readonly SVCMssqlMonitorLogFixture _fixture;
    private readonly Mock<IConfiguration> _mockConfiguration;

    public SVCMssqlMonitorLogRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = databaseFixture.DbContext;
        _mockConfiguration = new Mock<IConfiguration>();
        
        _repository = new SVCMssqlMonitorLogRepository(_dbContext, _mockConfiguration.Object);
        _fixture = new SVCMssqlMonitorLogFixture();

        // Setup default mock configuration
        _mockConfiguration.Setup(x => x.GetConnectionString("Default")).Returns("encrypted_connection_string");
        _mockConfiguration.Setup(x => x.GetConnectionString("DBProvider")).Returns("encrypted_provider");
    }

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnMatchingLogs_WhenTypeExists()
    {
        // Arrange
        await ClearDatabase();

        var log1 = _fixture.CreateSVCMssqlMonitorLog(
            type: "MSSQL_REPLICATION",
            infraObjectId: "INFRA_001",
            infraObjectName: "Test MSSQL 1",
            workflowId: "WF_001",
            workflowName: "MSSQL Workflow 1",
            isActive: true
        );
        var log2 = _fixture.CreateSVCMssqlMonitorLog(
            type: "MSSQL_REPLICATION",
            infraObjectId: "INFRA_002",
            infraObjectName: "Test MSSQL 2",
            workflowId: "WF_002",
            workflowName: "MSSQL Workflow 2",
            isActive: true
        );
        var log3 = _fixture.CreateSVCMssqlMonitorLog(
            type: "MSSQL_BACKUP",
            infraObjectId: "INFRA_003",
            infraObjectName: "Test MSSQL 3",
            workflowId: "WF_003",
            workflowName: "MSSQL Workflow 3",
            isActive: true
        );

        await _repository.AddAsync(log1);
        await _repository.AddAsync(log2);
        await _repository.AddAsync(log3);

        // Act
        var result = await _repository.GetDetailByType("MSSQL_REPLICATION");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, log => Assert.Equal("MSSQL_REPLICATION", log.Type));
        Assert.Contains(result, log => log.InfraObjectName == "Test MSSQL 1");
        Assert.Contains(result, log => log.InfraObjectName == "Test MSSQL 2");
        Assert.DoesNotContain(result, log => log.Type == "MSSQL_BACKUP");
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var log = _fixture.CreateSVCMssqlMonitorLog(type: "MSSQL_REPLICATION", isActive: true);
        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetDetailByType("NONEXISTENT_TYPE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldOnlyReturnActiveLogs()
    {
        // Arrange
        await ClearDatabase();

        var activeLog = _fixture.CreateSVCMssqlMonitorLog(
            type: "MSSQL_REPLICATION",
            infraObjectName: "Active MSSQL",
            isActive: true
        );
        var inactiveLog = _fixture.CreateSVCMssqlMonitorLog(
            type: "MSSQL_REPLICATION",
            infraObjectName: "Inactive MSSQL",
            isActive: false
        );

        await _repository.AddAsync(activeLog);
        await _repository.AddAsync(inactiveLog);

        // Act
        var result = await _repository.GetDetailByType("MSSQL_REPLICATION");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Active MSSQL", result[0].InfraObjectName);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnAllProperties()
    {
        // Arrange
        await ClearDatabase();

        var log = _fixture.CreateSVCMssqlMonitorLog(
            type: "MSSQL_REPLICATION",
            infraObjectId: "INFRA_001",
            infraObjectName: "Test MSSQL Object",
            workflowId: "WF_001",
            workflowName: "Test Workflow",
            properties: "{\"rpo\": \"15\", \"status\": \"running\"}",
            configuredRPO: "15",
            dataLagValue: "5",
            isActive: true
        );

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetDetailByType("MSSQL_REPLICATION");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var resultLog = result[0];
        
        Assert.Equal("MSSQL_REPLICATION", resultLog.Type);
        Assert.Equal("INFRA_001", resultLog.InfraObjectId);
        Assert.Equal("Test MSSQL Object", resultLog.InfraObjectName);
        Assert.Equal("WF_001", resultLog.WorkflowId);
        Assert.Equal("Test Workflow", resultLog.WorkflowName);
        Assert.Equal("{\"rpo\": \"15\", \"status\": \"running\"}", resultLog.Properties);
        Assert.Equal("15", resultLog.ConfiguredRPO);
        Assert.Equal("5", resultLog.DataLagValue);
        Assert.True(resultLog.IsActive);
        Assert.NotNull(resultLog.ReferenceId);
        Assert.True(resultLog.Id > 0);
    }

    [Fact]
    public async Task GetDetailByType_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();

        var log = _fixture.CreateSVCMssqlMonitorLog(type: "MSSQL_REPLICATION", isActive: true);
        await _repository.AddAsync(log);

        // Act
        var result1 = await _repository.GetDetailByType("MSSQL_REPLICATION");
        var result2 = await _repository.GetDetailByType("mssql_replication");
        var result3 = await _repository.GetDetailByType("MSSQL_Replication");

        // Assert
        Assert.NotNull(result1);
        Assert.Single(result1);
        
        Assert.NotNull(result2);
        Assert.Empty(result2);
        
        Assert.NotNull(result3);
        Assert.Empty(result3);
    }

    #endregion

    #region GetByInfraObjectId Tests

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnLogsWithinDateRange_WhenNoBackupTable()
    {
        // Arrange
        await ClearDatabase();

        var startDate = "2024-01-01";
        var endDate = "2024-01-31";
        var infraObjectId = "INFRA_001";

        var log1 = _fixture.CreateSVCMssqlMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "Test MSSQL 1",
            createdDate: new DateTime(2024, 1, 15),
            lastModifiedDate: new DateTime(2024, 1, 15),
            isActive: true
        );
        var log2 = _fixture.CreateSVCMssqlMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "Test MSSQL 2",
            createdDate: new DateTime(2024, 1, 20),
            lastModifiedDate: new DateTime(2024, 1, 20),
            isActive: true
        );
        var log3 = _fixture.CreateSVCMssqlMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "Test MSSQL 3",
            createdDate: new DateTime(2024, 2, 5), // Outside date range
            lastModifiedDate: new DateTime(2024, 2, 5),
            isActive: true
        );
        var log4 = _fixture.CreateSVCMssqlMonitorLog(
            infraObjectId: "INFRA_002", // Different infra object
            infraObjectName: "Different MSSQL",
            createdDate: new DateTime(2024, 1, 10),
            lastModifiedDate: new DateTime(2024, 1, 10),
            isActive: true
        );

        await _repository.AddAsync(log1);
        await _repository.AddAsync(log2);
        await _repository.AddAsync(log3);
        await _repository.AddAsync(log4);

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, log => Assert.Equal(infraObjectId, log.InfraObjectId));
        Assert.Contains(result, log => log.InfraObjectName == "Test MSSQL 1");
        Assert.Contains(result, log => log.InfraObjectName == "Test MSSQL 2");
        Assert.DoesNotContain(result, log => log.InfraObjectName == "Test MSSQL 3");
        Assert.DoesNotContain(result, log => log.InfraObjectName == "Different MSSQL");
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmpty_WhenNoLogsInDateRange()
    {
        // Arrange
        await ClearDatabase();

        var startDate = "2024-01-01";
        var endDate = "2024-01-31";
        var infraObjectId = "INFRA_001";

        var log = _fixture.CreateSVCMssqlMonitorLog(
            infraObjectId: infraObjectId,
            createdDate: new DateTime(2024, 2, 15), // Outside date range
            lastModifiedDate: new DateTime(2024, 2, 15),
            isActive: true
        );

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmpty_WhenInfraObjectIdNotFound()
    {
        // Arrange
        await ClearDatabase();

        var startDate = "2024-01-01";
        var endDate = "2024-01-31";

        var log = _fixture.CreateSVCMssqlMonitorLog(
            infraObjectId: "INFRA_001",
            createdDate: new DateTime(2024, 1, 15),
            lastModifiedDate: new DateTime(2024, 1, 15),
            isActive: true
        );

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetByInfraObjectId("NONEXISTENT_INFRA", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldOnlyReturnActiveLogs()
    {
        // Arrange
        await ClearDatabase();

        var startDate = "2024-01-01";
        var endDate = "2024-01-31";
        var infraObjectId = "INFRA_001";

        var activeLog = _fixture.CreateSVCMssqlMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "Active MSSQL",
            createdDate: new DateTime(2024, 1, 15),
            lastModifiedDate: new DateTime(2024, 1, 15),
            isActive: true
        );
        var inactiveLog = _fixture.CreateSVCMssqlMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "Inactive MSSQL",
            createdDate: new DateTime(2024, 1, 20),
            lastModifiedDate: new DateTime(2024, 1, 20),
            isActive: false
        );

        await _repository.AddAsync(activeLog);
        await _repository.AddAsync(inactiveLog);

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Active MSSQL", result[0].InfraObjectName);
        Assert.True(result[0].IsActive);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.SvcMssqlMonitorLogs.RemoveRange(_dbContext.SvcMssqlMonitorLogs);
        await _dbContext.SaveChangesAsync();
    }
}
