﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.SVCGMMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.SVCGMMonitoringStatus.Queries.GetPaginatedList;

public class SVCGMMonitorStatusPaginatedListQueryHandler : IRequestHandler<SVCGMMonitorStatusPaginatedListQuery,
    PaginatedResult<SVCGMMonitorStatusListVm>>
{
    private readonly IMapper _mapper;
    private readonly ISVCGMMonitorStatusRepository _svcGmMonitorStatusRepository;

    public SVCGMMonitorStatusPaginatedListQueryHandler(ISVCGMMonitorStatusRepository svcGmMonitorStatusRepository,
        IMapper mapper)
    {
        _svcGmMonitorStatusRepository = svcGmMonitorStatusRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<SVCGMMonitorStatusListVm>> Handle(SVCGMMonitorStatusPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var query = _svcGmMonitorStatusRepository.GetPaginatedQuery();

        var productFilterSpec = new SvcgmMonitorStatusFilterSpecification(request.SearchString);

        var entities = await query
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<SVCGMMonitorStatusListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return entities;
    }
}