﻿namespace ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Commands.Update;

public class
    UpdateInfraObjectSchedulerWorkflowDetailCommand : IRequest<UpdateInfraObjectSchedulerWorkflowDetailResponse>
{
    public string Id { get; set; }

    public string InfraObjectId { get; set; }

    public string InfraObjectName { get; set; }

    public string WorkflowId { get; set; }

    public string WorkflowName { get; set; }

    public string CurrentActionId { get; set; }

    public string CurrentActionName { get; set; }

    public int ScheduleId { get; set; }

    public string ScheduleType { get; set; }

    public string Status { get; set; }

    public string Type { get; set; }

    public DateTime StartTime { get; set; }

    public DateTime EndTime { get; set; }

    public bool IsTileScheduler { get; set; }

    public string Message { get; set; }

    public override string ToString()
    {
        return $"InfraObject Name: {InfraObjectName}; Id:{Id};";
    }
}