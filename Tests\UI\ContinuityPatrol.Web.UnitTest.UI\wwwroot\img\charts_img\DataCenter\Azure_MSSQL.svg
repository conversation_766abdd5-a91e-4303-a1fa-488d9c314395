<svg width="85" height="83" viewBox="0 0 85 83" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_5377_539)">
<mask id="mask0_5377_539" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="84" height="83">
<path d="M83.4985 0H0.498535V83H83.4985V0Z" fill="white"/>
</mask>
<g mask="url(#mask0_5377_539)">
<g filter="url(#filter0_d_5377_539)">
<path d="M42.0513 71.3789C58.6199 71.3789 72.0513 57.9474 72.0513 41.3789C72.0513 24.8104 58.6199 11.3789 42.0513 11.3789C25.4828 11.3789 12.0513 24.8104 12.0513 41.3789C12.0513 57.9474 25.4828 71.3789 42.0513 71.3789Z" fill="white"/>
<path d="M56.4707 53.226C56.4711 54.2585 56.0974 55.2809 55.3709 56.2348C54.6445 57.1887 53.5794 58.0555 52.2367 58.7857C50.8939 59.5158 49.2998 60.095 47.5452 60.4902C45.7907 60.8853 43.9101 61.0887 42.011 61.0887C40.1119 61.0887 38.2313 60.8853 36.4768 60.4902C34.7222 60.095 33.1281 59.5158 31.7853 58.7857C30.4426 58.0555 29.3775 57.1887 28.6511 56.2348C27.9246 55.2809 27.5509 54.2585 27.5513 53.226C27.5509 52.1935 27.9246 51.1711 28.6511 50.2172C29.3775 49.2633 30.4426 48.3965 31.7853 47.6663C33.1281 46.9362 34.7222 46.357 36.4768 45.9618C38.2313 45.5667 40.1119 45.3633 42.011 45.3633C43.9101 45.3633 45.7907 45.5667 47.5452 45.9618C49.2998 46.357 50.8939 46.9362 52.2367 47.6663C53.5794 48.3965 54.6445 49.2633 55.3709 50.2172C56.0974 51.1711 56.4711 52.1935 56.4707 53.226Z" fill="url(#paint0_radial_5377_539)"/>
<g clip-path="url(#clip1_5377_539)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M30.205 30.6562V54.3429C30.205 56.8024 35.7099 58.7963 42.5 58.7963V30.6562H30.205Z" fill="#0072C6"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.3315 58.7955H42.5C49.2901 58.7955 54.7951 56.8024 54.7951 54.3429V30.6562H42.3315V58.7959V58.7955Z" fill="#0072C6"/>
<path opacity="0.15" fill-rule="evenodd" clip-rule="evenodd" d="M42.3315 58.7955H42.5C49.2901 58.7955 54.7951 56.8024 54.7951 54.3429V30.6562H42.3315V58.7959V58.7955Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M54.7951 30.6557C54.7951 33.1148 49.2902 35.1082 42.5 35.1082C35.7099 35.1082 30.205 33.1148 30.205 30.6557C30.205 28.1966 35.7099 26.2031 42.5 26.2031C49.2902 26.2031 54.7951 28.1966 54.7951 30.6557Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M52.2811 30.3988C52.2811 32.0223 47.902 33.3366 42.5 33.3366C37.0979 33.3366 32.718 32.0223 32.718 30.3988C32.718 28.7752 37.0979 27.4609 42.5 27.4609C47.902 27.4609 52.2811 28.776 52.2811 30.3988Z" fill="#7FBA00"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M50.2324 32.1957C51.5127 31.6991 52.2824 31.0774 52.2824 30.4015C52.282 28.778 47.9029 27.4629 42.5 27.4629C37.0972 27.4629 32.7189 28.778 32.7189 30.4015C32.7189 31.077 33.4881 31.6991 34.7689 32.1957C36.557 31.5015 39.3517 31.0538 42.5004 31.0538C45.6492 31.0538 48.4431 31.5015 50.2324 32.1957Z" fill="#B8D432"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M38.5951 47.0969C38.5951 47.833 38.3283 48.403 37.7938 48.8067C37.2594 49.2105 36.5225 49.413 35.5811 49.413C34.7803 49.413 34.1111 49.2687 33.5733 48.9799V47.248C34.1938 47.7748 34.8774 48.0384 35.6242 48.0384C35.9848 48.0384 36.2639 47.9663 36.4606 47.8216C36.6574 47.677 36.7557 47.4863 36.7557 47.2476C36.7557 47.0133 36.6611 46.8096 36.4717 46.6365C36.2824 46.4633 35.8971 46.2397 35.3164 45.9655C34.1328 45.4106 33.5414 44.652 33.5414 43.6934C33.5414 42.9975 33.7992 42.4394 34.3156 42.0189C34.832 41.5985 35.5168 41.3887 36.3713 41.3887C37.1291 41.3887 37.7561 41.4885 38.2549 41.6865V43.3043C37.732 42.9433 37.1365 42.7633 36.4693 42.7633C36.134 42.7633 35.8688 42.8342 35.6742 42.9763C35.5816 43.0399 35.5065 43.1253 35.4555 43.225C35.4046 43.3246 35.3794 43.4354 35.3824 43.5471C35.3824 43.7924 35.4606 43.9936 35.618 44.1505C35.7754 44.3073 36.0967 44.5013 36.5836 44.7323C37.309 45.0753 37.8254 45.4228 38.1344 45.7764C38.4434 46.1301 38.5951 46.5709 38.5951 47.0969ZM46.9434 45.3442C46.9434 46.2568 46.7356 47.0492 46.3209 47.7194C45.9061 48.3896 45.3217 48.8605 44.568 49.1314L46.8184 51.2145H44.5463L42.9393 49.4126C42.2504 49.3877 41.6287 49.2052 41.075 48.8662C40.5213 48.5273 40.0934 48.0628 39.7926 47.4729C39.4918 46.883 39.3406 46.2254 39.3406 45.5006C39.3406 44.6964 39.5037 43.9794 39.8299 43.3499C40.1561 42.7205 40.6152 42.2361 41.207 41.8971C41.7988 41.5582 42.4762 41.3887 43.2418 41.3887C43.9553 41.3887 44.5951 41.5529 45.1598 41.8812C45.7246 42.2096 46.1623 42.6761 46.4746 43.2823C46.7869 43.8885 46.9434 44.5762 46.9434 45.3442ZM45.1041 45.442C45.1041 44.6553 44.9328 44.0344 44.5897 43.5777C44.2467 43.121 43.7783 42.8928 43.1828 42.8928C42.5766 42.8928 42.0918 43.1218 41.727 43.5793C41.3623 44.0368 41.1807 44.6471 41.1807 45.4082C41.1807 46.1659 41.359 46.7705 41.716 47.2232C42.0729 47.6758 42.5471 47.9019 43.1389 47.9019C43.7418 47.9019 44.2193 47.6827 44.5729 47.2447C44.9266 46.8068 45.1041 46.2063 45.1041 45.442ZM52.8471 49.2773H48.2262V41.519H49.9738V47.8599H52.8467L52.8471 49.2773Z" fill="white"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_5377_539" x="0.84369" y="0.171327" width="82.4152" height="82.4152" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5.60379"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5377_539"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5377_539" result="shape"/>
</filter>
<radialGradient id="paint0_radial_5377_539" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(42.011 53.226) scale(14.4597 7.86102)">
<stop/>
<stop offset="1" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_5377_539">
<rect width="84" height="83" fill="white" transform="translate(0.248535)"/>
</clipPath>
<clipPath id="clip1_5377_539">
<rect width="25" height="33" fill="white" transform="translate(30 26)"/>
</clipPath>
</defs>
</svg>
