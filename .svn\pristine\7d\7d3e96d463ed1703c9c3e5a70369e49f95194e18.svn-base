using ContinuityPatrol.Application.Features.BulkImport.Commands.RollBack;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImport.Commands;

public class RollBackBulkImportTests : IClassFixture<BulkImportFixture>
{
    private readonly BulkImportFixture _bulkImportFixture;
    private readonly Mock<IBulkImportOperationGroupRepository> _mockBulkImportOperationGroupRepository;
    private readonly Mock<IBulkImportActionResultRepository> _mockBulkImportActionResultRepository;
    private readonly Mock<BulkImportHelperService> _mockBulkImportHelperService;
    private readonly RollBackBulkImportCommandHandler _handler;

    public RollBackBulkImportTests(BulkImportFixture bulkImportFixture)
    {
        _bulkImportFixture = bulkImportFixture;

        _mockBulkImportOperationGroupRepository = BulkImportRepositoryMocks.CreateBulkImportOperationGroupRepository(_bulkImportFixture.BulkImportOperationGroups);
        _mockBulkImportActionResultRepository = BulkImportRepositoryMocks.CreateBulkImportActionResultRepository(_bulkImportFixture.BulkImportActionResults);
      //  _mockBulkImportHelperService = BulkImportRepositoryMocks.CreateBulkImportHelperService();

        _handler = new RollBackBulkImportCommandHandler(
            _mockBulkImportOperationGroupRepository.Object,
            _mockBulkImportActionResultRepository.Object,
            _mockBulkImportHelperService.Object
            );
    }

    [Fact]
    public async Task Handle_Return_RollBackBulkImportResponse_When_RollBackExecuted()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 1; // Set to create operation for rollback
        var command = new RollBackBulkImportCommand { GroupId = existingGroup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(RollBackBulkImportResponse));
        result.BulkImportOperationGroupId.ShouldBe(existingGroup.ReferenceId);
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        var command = new RollBackBulkImportCommand { GroupId = existingGroup.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportOperationGroupNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new RollBackBulkImportCommand { GroupId = nonExistentId };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportOperationGroup)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportOperationGroupIsInactive()
    {
        // Arrange
        var inactiveGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        inactiveGroup.IsActive = false;
        var command = new RollBackBulkImportCommand { GroupId = inactiveGroup.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_RollBackCreateOperation_When_ConditionalOperationIs1()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 1; // Create operation
        var command = new RollBackBulkImportCommand { GroupId = existingGroup.ReferenceId };

        // Setup action results for the group
        var actionResults = new List<Domain.Entities.BulkImportOperationGroup>
        {
            new Domain.Entities.BulkImportOperationGroup
            {
                BulkImportOperationId = existingGroup.BulkImportOperationId
            }
        };

        //_mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
        //    existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
        //    .ReturnsAsync(actionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportActionResultRepository.Verify(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId), Times.Once);
    }

    [Fact]
    public async Task Handle_CallDeleteServer_When_RollBackCreateOperationAndServerExists()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 1; // Create operation - rollback means delete
        var command = new RollBackBulkImportCommand { GroupId = existingGroup.ReferenceId };

        var serverActionResults = new List<Domain.Entities.BulkImportOperationGroup>
        {
            new Domain.Entities.BulkImportOperationGroup
            {
                BulkImportOperationId = existingGroup.BulkImportOperationId
            }
        };

        //_mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
        //    existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
        //    .ReturnsAsync(serverActionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportHelperService.Verify(x => x.DeleteServer(
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CallDeleteDatabase_When_RollBackCreateOperationAndDatabaseExists()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 1; // Create operation - rollback means delete
        var command = new RollBackBulkImportCommand { GroupId = existingGroup.ReferenceId };

        var databaseActionResults = new List<Domain.Entities.BulkImportOperationGroup>
        {
            new Domain.Entities.BulkImportOperationGroup
            { 
                BulkImportOperationId = existingGroup.BulkImportOperationId,
                ReferenceId = existingGroup.ReferenceId,
            }
        };

        //_mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
        //    existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
        //    .ReturnsAsync(databaseActionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportHelperService.Verify(x => x.DeleteDatabase(
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CallDeleteReplication_When_RollBackCreateOperationAndReplicationExists()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 1; // Create operation - rollback means delete
        var command = new RollBackBulkImportCommand { GroupId = existingGroup.ReferenceId };

        var replicationActionResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult 
            { 
                BulkImportOperationId = existingGroup.BulkImportOperationId,
                BulkImportOperationGroupId = existingGroup.ReferenceId,
                EntityType = "Replication"
            }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
            .ReturnsAsync(replicationActionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportHelperService.Verify(x => x.DeleteReplication(
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CallDeleteInfraObject_When_RollBackCreateOperationAndInfraObjectExists()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 1; // Create operation - rollback means delete
        var command = new RollBackBulkImportCommand { GroupId = existingGroup.ReferenceId };

        var infraObjectActionResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult 
            { 
                BulkImportOperationId = existingGroup.BulkImportOperationId,
                BulkImportOperationGroupId = existingGroup.ReferenceId,
                EntityType = "InfraObject"
            }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
            .ReturnsAsync(infraObjectActionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportHelperService.Verify(x => x.DeleteInfraObject(
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_OperationSuccessful()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        var command = new RollBackBulkImportCommand { GroupId = existingGroup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<RollBackBulkImportResponse>();
        result.GetType().ShouldBe(typeof(RollBackBulkImportResponse));
    }

    [Fact]
    public async Task Handle_CallRepositoryWithCorrectId_When_QueryExecuted()
    {
        // Arrange
        var testId = Guid.NewGuid().ToString();
        var command = new RollBackBulkImportCommand { GroupId = testId };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetByReferenceIdAsync(testId))
            .ReturnsAsync(_bulkImportFixture.BulkImportOperationGroups.First());

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.GetByReferenceIdAsync(testId), Times.Once);
    }

    [Fact]
    public async Task Handle_FilterActionResultsByEntityType_When_ProcessingRollBack()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 1; // Create operation
        var command = new RollBackBulkImportCommand { GroupId = existingGroup.ReferenceId };

        var mixedActionResults = new List<Domain.Entities.BulkImportActionResult>
        {
           
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
            .ReturnsAsync(mixedActionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportHelperService.Verify(x => x.DeleteServer(
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);

        _mockBulkImportHelperService.Verify(x => x.DeleteDatabase(
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_UseGuardAgainstNullOrDeactive_When_GroupValidation()
    {
        // Arrange
        var inactiveGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        inactiveGroup.IsActive = false;
        var command = new RollBackBulkImportCommand { GroupId = inactiveGroup.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ProcessRollBackLogic_When_ConditionalOperationIs1()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 1; // Create operation needs rollback (delete)
        var command = new RollBackBulkImportCommand { GroupId = existingGroup.ReferenceId };

        var actionResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult 
            { 
                BulkImportOperationId = existingGroup.BulkImportOperationId,
                BulkImportOperationGroupId = existingGroup.ReferenceId,
                EntityType = "Server"
            }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
            .ReturnsAsync(actionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        // Verify that rollback logic is executed (delete operations for create rollback)
        _mockBulkImportActionResultRepository.Verify(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId), Times.Once);
    }

    [Fact]
    public async Task Handle_ProcessRollBackLogic_When_ConditionalOperationIs2()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 2; // Delete operation needs rollback (create)
        var command = new RollBackBulkImportCommand { GroupId = existingGroup.ReferenceId };

        var actionResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult
            {
                BulkImportOperationId = existingGroup.BulkImportOperationId,
                BulkImportOperationGroupId = existingGroup.ReferenceId,
                EntityType = "Server"
            }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
            .ReturnsAsync(actionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        // Verify that rollback logic is executed
        _mockBulkImportActionResultRepository.Verify(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId), Times.Once);
    }

    [Fact]
    public async Task Handle_ProcessMultipleEntityTypesRollback_When_MixedActionResults()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 1; // Create operation - rollback means delete
        var command = new RollBackBulkImportCommand { GroupId = existingGroup.ReferenceId };

        var mixedActionResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult { EntityType = "Server", EntityId = "server1" },
            new Domain.Entities.BulkImportActionResult { EntityType = "Database", EntityId = "db1" },
            new Domain.Entities.BulkImportActionResult { EntityType = "Replication", EntityId = "rep1" },
            new Domain.Entities.BulkImportActionResult { EntityType = "InfraObject", EntityId = "infra1" }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
            .ReturnsAsync(mixedActionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportHelperService.Verify(x => x.DeleteServer(It.IsAny<string >(), It.IsAny<CancellationToken>()), Times.Once);
        _mockBulkImportHelperService.Verify(x => x.DeleteDatabase(It.IsAny<string >(), It.IsAny<CancellationToken>()), Times.Once);
        _mockBulkImportHelperService.Verify(x => x.DeleteReplication(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockBulkImportHelperService.Verify(x => x.DeleteInfraObject(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_PerformInverseOperation_When_RollbackRequested()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 1; // Create operation - rollback should delete
        var command = new RollBackBulkImportCommand { GroupId = existingGroup.ReferenceId };

        var actionResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult { EntityType = "Server" }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
            .ReturnsAsync(actionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        // For create operations (ConditionalOperation = 1), rollback should perform delete operations
        _mockBulkImportHelperService.Verify(x => x.DeleteServer(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleEmptyActionResultsRollback_When_NoResultsFound()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 1;
        var command = new RollBackBulkImportCommand { GroupId = existingGroup.ReferenceId };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
            .ReturnsAsync(new List<Domain.Entities.BulkImportActionResult>());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.BulkImportOperationGroupId.ShouldBe(existingGroup.ReferenceId);
        _mockBulkImportHelperService.Verify(x => x.DeleteServer(It.IsAny<string >(), It.IsAny<CancellationToken>()), Times.Never);
        _mockBulkImportHelperService.Verify(x => x.DeleteDatabase(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ValidateConditionalOperationLogic_When_DifferentOperationTypes()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        var command = new RollBackBulkImportCommand { GroupId = existingGroup.ReferenceId };

        var actionResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult { EntityType = "Server" }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
            .ReturnsAsync(actionResults);

        // Test ConditionalOperation = 1 (Create -> Rollback = Delete)
        existingGroup.ConditionalOperation = 1;
        await _handler.Handle(command, CancellationToken.None);

        // Test ConditionalOperation = 2 (Delete -> Rollback = Create)
        existingGroup.ConditionalOperation = 2;
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportActionResultRepository.Verify(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId), Times.Exactly(2));
    }
}
