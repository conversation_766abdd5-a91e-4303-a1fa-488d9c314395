﻿using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessServiceEvaluation.Commands;

public class UpdateBusinessServiceEvaluationTests : IClassFixture<BusinessServiceEvaluationFixture>
{
    private readonly BusinessServiceEvaluationFixture _businessServiceEvaluationFixture;
    private readonly Mock<IBusinessServiceEvaluationRepository> _mockBusinessServiceEvaluationRepository;
    private readonly UpdateBusinessServiceEvaluationCommandHandler _handler;

    public UpdateBusinessServiceEvaluationTests(BusinessServiceEvaluationFixture businessServiceEvaluationFixture)
    {
        _businessServiceEvaluationFixture = businessServiceEvaluationFixture;

        _mockBusinessServiceEvaluationRepository = BusinessServiceEvaluationRepositoryMocks.UpdateBusinessServiceEvaluationRepository(_businessServiceEvaluationFixture.BusinessServiceEvaluations);

        _handler = new UpdateBusinessServiceEvaluationCommandHandler(_businessServiceEvaluationFixture.Mapper, _mockBusinessServiceEvaluationRepository.Object);
    }

    [Fact]
    public async Task Handle_ValidBusinessServiceEvaluation_UpdateToBusinessServiceEvaluationsRepo()
    {
        _businessServiceEvaluationFixture.UpdateBusinessServiceEvaluationCommand.Id = _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId;

        var result = await _handler.Handle(_businessServiceEvaluationFixture.UpdateBusinessServiceEvaluationCommand, CancellationToken.None);

        var businessServiceEvaluation = await _mockBusinessServiceEvaluationRepository.Object.GetByReferenceIdAsync(result.BusinessServiceEvaluationId);

        Assert.Equal(_businessServiceEvaluationFixture.UpdateBusinessServiceEvaluationCommand.BusinessServiceName, businessServiceEvaluation.BusinessServiceName);
    }

    [Fact]
    public async Task Handle_Return_UpdateBusinessServiceEvaluationResponse_When_BusinessServiceEvaluationUpdated()
    {
        _businessServiceEvaluationFixture.UpdateBusinessServiceEvaluationCommand.Id = _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId;

        var result = await _handler.Handle(_businessServiceEvaluationFixture.UpdateBusinessServiceEvaluationCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateBusinessServiceEvaluationResponse));

        result.BusinessServiceEvaluationId.ShouldBeGreaterThan(0.ToString());

        result.BusinessServiceEvaluationId.ShouldBe(_businessServiceEvaluationFixture.UpdateBusinessServiceEvaluationCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidBusinessServiceEvaluationId()
    {
        _businessServiceEvaluationFixture.UpdateBusinessServiceEvaluationCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_businessServiceEvaluationFixture.UpdateBusinessServiceEvaluationCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _businessServiceEvaluationFixture.UpdateBusinessServiceEvaluationCommand.Id = _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId;

        await _handler.Handle(_businessServiceEvaluationFixture.UpdateBusinessServiceEvaluationCommand, CancellationToken.None);

        _mockBusinessServiceEvaluationRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockBusinessServiceEvaluationRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BusinessServiceEvaluation>()), Times.Once);
    }
}