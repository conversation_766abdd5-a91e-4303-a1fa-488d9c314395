using ContinuityPatrol.Application.Features.BackUp.Commands.Create;
using ContinuityPatrol.Application.Features.BackUp.Commands.Execute;
using ContinuityPatrol.Application.Features.BackUp.Commands.Update;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetByConfig;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BackUpModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IBackUpService
{
    Task<List<BackUpListVm>> GetBackUpList();
    Task<BaseResponse> CreateAsync(CreateBackUpCommand createBackUpCommand);
    Task<BaseResponse> UpdateAsync(UpdateBackUpCommand updateBackUpCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<BackUpDetailVm> GetByReferenceId(string id);
    Task<GetByConfigDetailVm> GetBackUpByConfig();
    Task<BaseResponse> ExecuteBackUp(BackUpExecuteCommand backUpExecuteCommand);
    Task<bool> IsBackUpNameExist(string name, string id);
    Task<PaginatedResult<BackUpListVm>> GetPaginatedBackUps(GetBackUpPaginatedListQuery query);
}
