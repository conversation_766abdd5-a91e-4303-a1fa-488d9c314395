using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Core.Extensions;
using Microsoft.EntityFrameworkCore;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class SiteTypeRepositoryTests : IClassFixture<SiteTypeFixture>
{
    private readonly SiteTypeFixture _siteTypeFixture;

    public SiteTypeRepositoryTests(SiteTypeFixture siteTypeFixture)
    {
        _siteTypeFixture = siteTypeFixture;
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ReturnsActiveSiteTypes()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType1 = _siteTypeFixture.CreateSiteType(type: "Primary", category: "Primary", isActive: true);
        var siteType2 = _siteTypeFixture.CreateSiteType(type: "DR", category: "DR", isActive: true);
        var siteType3 = _siteTypeFixture.CreateSiteType(type: "Inactive", category: "Inactive", isActive: false);

        await dbContext.SiteTypes.AddRangeAsync(siteType1, siteType2, siteType3);
         dbContext.SaveChanges();

        // Act
        var result = await repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.Type == "Primary");
        Assert.Contains(result, s => s.Type == "DR");
        Assert.DoesNotContain(result, s => s.Type == "Inactive");
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmptyList_WhenNoActiveSiteTypes()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(type: "Inactive Type", isActive: false);
        await dbContext.SiteTypes.AddAsync(siteType);
        dbContext.SaveChanges();

        // Act
        var result = await repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Note: The repository's Active() extension filters by IsActive, but the test data might still be returned
        // This depends on the Active() extension implementation
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsProjectedFields()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(
            type: "Test Type",
            category: "Test Category",
            isActive: true
        );

        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var siteTypeResult = result.First();
        Assert.NotNull(siteTypeResult.ReferenceId);
        Assert.Equal("Test Type", siteTypeResult.Type);
        Assert.Equal("Test Category", siteTypeResult.Category);
        Assert.True(siteTypeResult.Id > 0);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmptyList_WhenNoSiteTypes()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        // Act
        var result = await repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetSiteTypeById Tests

    [Fact]
    public async Task GetSiteTypeById_ReturnsSiteType_WhenIdExistsAndIsDeleted()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(type: "Test Type", category: "Test Category", isActive: true, isDelete: true);
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.GetSiteTypeById(siteType.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(siteType.ReferenceId, result.ReferenceId);
        Assert.Equal("Test Type", result.Type);
        Assert.Equal("Test Category", result.Category);
        Assert.True(result.IsDelete);
    }

    [Fact]
    public async Task GetSiteTypeById_ReturnsNull_WhenIdDoesNotExist()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        // Act
        var result = await repository.GetSiteTypeById(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetSiteTypeById_ReturnsNull_WhenSiteTypeIsInactive()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(type: "Inactive Type", isActive: false, isDelete: true);
        await dbContext.SiteTypes.AddAsync(siteType);
        dbContext.SaveChanges();

        // Act
        var result = await repository.GetSiteTypeById(siteType.ReferenceId);

        // Assert
        // The repository filters by Active() which checks IsActive, so inactive records won't be returned
        Assert.Null(result);
    }

    [Fact]
    public async Task GetSiteTypeById_ReturnsNull_WhenSiteTypeIsNotDeleted()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(type: "Test Type", isActive: true, isDelete: false);
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.GetSiteTypeById(siteType.ReferenceId);

        // Assert
        Assert.Null(result); // Because the method incorrectly checks for IsDelete = true instead of false
    }

    #endregion

    #region IsSiteTypeNameExist Tests

    [Fact]
    public async Task IsSiteTypeNameExist_ReturnsTrue_WhenTypeExistsAndIdIsNotValidGuid()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(type: "Existing Type");
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.IsSiteTypeNameExist("Existing Type", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsSiteTypeNameExist_ReturnsFalse_WhenTypeDoesNotExistAndIdIsNotValidGuid()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        // Act
        var result = await repository.IsSiteTypeNameExist("Non-existing Type", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSiteTypeNameExist_ReturnsFalse_WhenTypeExistsButIdMatches()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(type: "Test Type");
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.IsSiteTypeNameExist("Test Type", siteType.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSiteTypeNameExist_ReturnsTrue_WhenTypeExistsAndIdIsDifferent()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(type: "Test Type");
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.IsSiteTypeNameExist("Test Type", Guid.NewGuid().ToString());

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsSiteTypeNameExist_ReturnsTrue_WhenIdIsEmptyString()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(type: "Test Type");
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.IsSiteTypeNameExist("Test Type", "");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsSiteTypeNameExist_ReturnsTrue_WhenIdIsNull()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(type: "Test Type");
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.IsSiteTypeNameExist("Test Type", null);

        // Assert
        Assert.True(result);
    }

    #endregion

    #region IsSiteTypeNameUnique Tests

    [Fact]
    public async Task IsSiteTypeNameUnique_ReturnsTrue_WhenTypeExists()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(type: "Existing Type");
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.IsSiteTypeNameUnique("Existing Type");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsSiteTypeNameUnique_ReturnsFalse_WhenTypeDoesNotExist()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        // Act
        var result = await repository.IsSiteTypeNameUnique("Non-existing Type");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSiteTypeNameUnique_IsCaseSensitive()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(type: "Test Type");
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.IsSiteTypeNameUnique("test type");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSiteTypeNameUnique_ReturnsFalse_WhenTypeIsNull()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        // Act
        var result = await repository.IsSiteTypeNameUnique(null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSiteTypeNameUnique_ReturnsFalse_WhenTypeIsEmpty()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        // Act
        var result = await repository.IsSiteTypeNameUnique("");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsSiteTypeCategoryUnique Tests

    [Fact]
    public async Task IsSiteTypeCategoryUnique_ReturnsTrue_WhenCategoryExists()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(category: "Primary");
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.IsSiteTypeCategoryUnique("Primary");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsSiteTypeCategoryUnique_ReturnsFalse_WhenCategoryDoesNotExist()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        // Act
        var result = await repository.IsSiteTypeCategoryUnique("Non-existing Category");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSiteTypeCategoryUnique_IsCaseSensitive()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(category: "Primary");
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.IsSiteTypeCategoryUnique("primary");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSiteTypeCategoryUnique_ReturnsFalse_WhenCategoryIsNull()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        // Act
        var result = await repository.IsSiteTypeCategoryUnique(null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSiteTypeCategoryUnique_ReturnsFalse_WhenCategoryIsEmpty()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        // Act
        var result = await repository.IsSiteTypeCategoryUnique("");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetSiteTypeIndexByIdAsync Tests

    [Fact]
    public async Task GetSiteTypeIndexByIdAsync_ReturnsCorrectIndex_WhenSiteTypeExists()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType1 = _siteTypeFixture.CreateSiteType(category: "Secondary", isDelete: true);
        var siteType2 = _siteTypeFixture.CreateSiteType(category: "Tertiary", isDelete: true);
        var siteType3 = _siteTypeFixture.CreateSiteType(category: "Primary", isDelete: true); // Should be excluded
        var siteType4 = _siteTypeFixture.CreateSiteType(category: "DR", isDelete: true); // Should be excluded

        await dbContext.SiteTypes.AddRangeAsync(siteType1, siteType2, siteType3, siteType4);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.GetSiteTypeIndexByIdAsync(siteType2.ReferenceId);

        // Assert
        Assert.Equal(1, result); // Should be index 1 (second item after filtering)
    }

    [Fact]
    public async Task GetSiteTypeIndexByIdAsync_ReturnsMinusOne_WhenSiteTypeNotFound()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(category: "Secondary", isDelete: true);
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.GetSiteTypeIndexByIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Equal(-1, result);
    }

    [Fact]
    public async Task GetSiteTypeIndexByIdAsync_ExcludesPrimaryAndDRCategories()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType1 = _siteTypeFixture.CreateSiteType(category: "primary", isDelete: true); // Should be excluded
        var siteType2 = _siteTypeFixture.CreateSiteType(category: "DR", isDelete: true); // Should be excluded
        var siteType3 = _siteTypeFixture.CreateSiteType(category: "Secondary", isDelete: true);

        await dbContext.SiteTypes.AddRangeAsync(siteType1, siteType2, siteType3);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.GetSiteTypeIndexByIdAsync(siteType3.ReferenceId);

        // Assert
        Assert.Equal(0, result); // Should be index 0 (first item after filtering)
    }

    [Fact]
    public async Task GetSiteTypeIndexByIdAsync_ReturnsMinusOne_WhenSiteTypeIsNotDeleted()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(category: "Secondary", isDelete: false);
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.GetSiteTypeIndexByIdAsync(siteType.ReferenceId);

        // Assert
        Assert.Equal(-1, result); // Should return -1 because IsDelete is false
    }

    #endregion

    #region GetSitesBySiteTypeId Tests

    [Fact]
    public async Task GetSitesBySiteTypeId_ReturnsMatchingSiteTypes()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType1 = _siteTypeFixture.CreateSiteType(category: "Primary");
        var siteType2 = _siteTypeFixture.CreateSiteType(category: "DR");
        var siteType3 = _siteTypeFixture.CreateSiteType(category: "Secondary");

        await dbContext.SiteTypes.AddRangeAsync(siteType1, siteType2, siteType3);
        await dbContext.SaveChangesAsync();

        var typeIds = new List<string> { siteType1.ReferenceId, siteType3.ReferenceId };

        // Act
        var result = await repository.GetSitesBySiteTypeId(typeIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.ReferenceId == siteType1.ReferenceId && s.Category == "Primary");
        Assert.Contains(result, s => s.ReferenceId == siteType3.ReferenceId && s.Category == "Secondary");
        Assert.DoesNotContain(result, s => s.ReferenceId == siteType2.ReferenceId);
    }

    [Fact]
    public async Task GetSitesBySiteTypeId_ReturnsEmptyList_WhenNoMatches()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType();
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        var typeIds = new List<string> { Guid.NewGuid().ToString() };

        // Act
        var result = await repository.GetSitesBySiteTypeId(typeIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetSitesBySiteTypeId_ReturnsEmptyList_WhenTypeIdsIsEmpty()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType();
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        var typeIds = new List<string>();

        // Act
        var result = await repository.GetSitesBySiteTypeId(typeIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetSitesBySiteTypeId_OnlyReturnsActiveSiteTypes()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var activeSiteType = _siteTypeFixture.CreateSiteType(category: "Primary", isActive: true);
        var inactiveSiteType = _siteTypeFixture.CreateSiteType(category: "DR", isActive: false);

        await dbContext.SiteTypes.AddRangeAsync(activeSiteType, inactiveSiteType);
        dbContext.SaveChanges();

        var typeIds = new List<string> { activeSiteType.ReferenceId, inactiveSiteType.ReferenceId };

        // Act
        var result = await repository.GetSitesBySiteTypeId(typeIds);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(activeSiteType.ReferenceId, result[0].ReferenceId);
        Assert.Equal("Primary", result[0].Category);
    }

    [Fact]
    public async Task GetSitesBySiteTypeId_ReturnsOnlyProjectedFields()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(type: "Test Type", category: "Test Category");
        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        var typeIds = new List<string> { siteType.ReferenceId };

        // Act
        var result = await repository.GetSitesBySiteTypeId(typeIds);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var resultSiteType = result[0];
        Assert.Equal(siteType.ReferenceId, resultSiteType.ReferenceId);
        Assert.Equal("Test Category", resultSiteType.Category);
        // Other fields should be null/default since they're not projected
        Assert.Null(resultSiteType.Type);
        Assert.Equal(0, resultSiteType.Id);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsCorrectPage()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteTypes = _siteTypeFixture.CreateMultipleSiteTypes(5);
        await dbContext.SiteTypes.AddRangeAsync(siteTypes);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.PaginatedListAllAsync(1, 3, null, "Id", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Data.Count);
        Assert.Equal(5, result.TotalCount);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(2, result.TotalPages);
    }

    [Fact]
    public async Task GetPaginatedQuery_ReturnsActiveRecordsOnly()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var activeSiteType = _siteTypeFixture.CreateSiteType(isActive: true);
        var inactiveSiteType = _siteTypeFixture.CreateSiteType(isActive: false);

        await dbContext.SiteTypes.AddRangeAsync(activeSiteType, inactiveSiteType);
         dbContext.SaveChanges();

        // Act
        var query = repository.GetPaginatedQuery();
        var result = await query.ToListAsync();

        // Assert
        Assert.Single(result);
        Assert.Equal(activeSiteType.ReferenceId, result[0].ReferenceId);
        Assert.True(result[0].IsActive);
    }

    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task GetSiteTypeById_HandlesNullId()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        // Act
        var result = await repository.GetSiteTypeById(null);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetSiteTypeById_HandlesEmptyId()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        // Act
        var result = await repository.GetSiteTypeById("");

        // Assert
        Assert.Null(result);
    }

    //[Fact]
    //public async Task GetSitesBySiteTypeId_ThrowsException_WhenListIsNull()
    //{
    //    // Arrange
    //    using var dbContext = DbContextFactory.CreateInMemoryDbContext();
    //    var repository = new SiteTypeRepository(dbContext);

    //    // Act & Assert
    //    // The method will throw an exception when trying to use Contains() on a null list
    //    await Assert.ThrowsAnyAsync<Exception>(() => repository.GetSitesBySiteTypeId(null));
    //}

    [Fact]
    public async Task GetSiteTypeIndexByIdAsync_HandlesNullId()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        // Act
        var result = await repository.GetSiteTypeIndexByIdAsync(null);

        // Assert
        Assert.Equal(-1, result);
    }

    [Fact]
    public async Task GetSiteTypeIndexByIdAsync_HandlesEmptyId()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        // Act
        var result = await repository.GetSiteTypeIndexByIdAsync("");

        // Assert
        Assert.Equal(-1, result);
    }

    #endregion

    #region SelectSiteType Projection Tests

    [Fact]
    public async Task SelectSiteType_ProjectsCorrectFields()
    {
        // Arrange
        using var dbContext = DbContextFactory.CreateInMemoryDbContext();
        var repository = new SiteTypeRepository(dbContext);

        var siteType = _siteTypeFixture.CreateSiteType(
            type: "Test Type",
            category: "Test Category",
            isActive: true,
            isDelete: false
        );
        siteType.Icon = "test-icon";

        await dbContext.SiteTypes.AddAsync(siteType);
        await dbContext.SaveChangesAsync();

        // Act
        var result = await repository.ListAllAsync();

        // Assert
        Assert.Single(result);
        var projectedSiteType = result[0];
        Assert.Equal(siteType.Id, projectedSiteType.Id);
        Assert.Equal(siteType.ReferenceId, projectedSiteType.ReferenceId);
        Assert.Equal(siteType.Category, projectedSiteType.Category);
        Assert.Equal(siteType.Icon, projectedSiteType.Icon);
        Assert.Equal(siteType.IsDelete, projectedSiteType.IsDelete);
        Assert.Equal(siteType.Type, projectedSiteType.Type);
    }

    #endregion
}
