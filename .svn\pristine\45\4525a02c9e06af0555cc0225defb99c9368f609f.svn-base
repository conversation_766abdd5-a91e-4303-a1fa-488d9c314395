﻿using ContinuityPatrol.Domain.ViewModels.StateMonitorLogModel;

namespace ContinuityPatrol.Application.Features.StateMonitorLog.Queries.GetList;

public class
    GetStateMonitorLogListQueryHandler : IRequestHandler<GetStateMonitorLogListQuery, List<StateMonitorLogListVm>>
{
    private readonly IMapper _mapper;
    private readonly IStateMonitorLogRepository _stateMonitorLogRepository;

    public GetStateMonitorLogListQueryHandler(IStateMonitorLogRepository stateMonitorLogRepository, IMapper mapper)
    {
        _stateMonitorLogRepository = stateMonitorLogRepository;
        _mapper = mapper;
    }

    public async Task<List<StateMonitorLogListVm>> Handle(GetStateMonitorLogListQuery request,
        CancellationToken cancellationToken)
    {
        var stateMonitorList = await _stateMonitorLogRepository.ListAllAsync();

        return stateMonitorList.Count != 0
            ? _mapper.Map<List<StateMonitorLogListVm>>(stateMonitorList)
            : new List<StateMonitorLogListVm>();
    }
}