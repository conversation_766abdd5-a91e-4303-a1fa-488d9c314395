﻿using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Update;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.UpdateState;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.InfraObjectSchedulerLogPagination;
using ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;


namespace ContinuityPatrol.Shared.Services.Contract;

public interface IDrReadyService
{
    Task<BaseResponse> CreateAsync(CreateInfraObjectSchedulerCommand createInfraObjectSchedulerCommand);
    Task<BaseResponse> UpdateAsync(UpdateInfraObjectSchedulerCommand updateInfraObjectSchedulerCommand);
    Task<BaseResponse> UpdateInfraObjectSchedulerStatus(UpdateInfraObjectSchedulerStatusCommand updateInfraObjectSchedulerStatusCommand);
    Task<BaseResponse> UpdateInfraObjectSchedulerState(UpdateInfraObjectSchedulerStateCommand updateInfraObjectSchedulerStateCommand);
    Task<BaseResponse> DeleteAsync(string drReadyId);
    Task<PaginatedResult<InfraObjectSchedulerListVm>> GetDrReadyPaginatedList(GetInfraObjectSchedulerPaginatedListQuery query);
    Task<PaginatedResult<InfraObjectSchedulerLogsListVm>> GetPaginatedInfraObjectSchedulerLogs(GetInfraObjectSchedulerLogsPaginationQuery query);
}