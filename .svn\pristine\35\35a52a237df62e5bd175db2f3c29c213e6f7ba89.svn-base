﻿using ContinuityPatrol.Shared.Core.Behaviors;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Settings;
using ContinuityPatrol.Shared.Infrastructure.Identity;
using ContinuityPatrol.Shared.Infrastructure.VersionManager;
using RemoveVersionFromParameterFilter = ContinuityPatrol.Api.Swagger.Filters.RemoveVersionFromParameterFilter;
using ReplaceVersionWithExactValueInPathFilter = ContinuityPatrol.Api.Swagger.Filters.ReplaceVersionWithExactValueInPathFilter;
using SwaggerExcludeFilter = ContinuityPatrol.Api.Swagger.Filters.SwaggerExcludeFilter;

namespace ContinuityPatrol.Api.Extension;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddShardInfrastructure(this IServiceCollection services, IConfiguration config,
        string title, string version)
    {
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

        services.Configure<CacheSettings>(config.GetSection(nameof(CacheSettings)));
        services.Configure<JwtSettings>(config.GetSection(nameof(JwtSettings)));

        services.AddScoped<ILoggedInUserService, LoggedInUserService>();
        services.AddTransient<ITokenManager, TokenManager>();
        services.AddScoped<IVersionManager, CreateVersion>();
        services.AddControllers();
        services.AddEndpointsApiExplorer();
        services.AddApiVersioning(o =>
        {
            o.AssumeDefaultVersionWhenUnspecified = true;
            o.DefaultApiVersion = new ApiVersion(6, 0);
        });

        services.AddSwaggerDocumentation(title, version);


        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));

        services.Add(ServiceDescriptor.Singleton<IDistributedCache, RedisCache>());

        services.AddLazyCache(_ =>
        {
            var cache = new CachingService(CachingService.DefaultCacheProvider)
            {
                DefaultCachePolicy = { DefaultCacheDurationSeconds = 36000 }
            };
            return cache;
        });

        services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(o =>
            {
                o.RequireHttpsMetadata = false;
                o.SaveToken = true;
                o.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero,
                    ValidIssuer = config["JwtSettings:Issuer"],
                    ValidAudience = config["JwtSettings:Audience"],
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(config["JwtSettings:Key"]))
                };

                o.Events = new JwtBearerEvents
                {
                    OnAuthenticationFailed = context =>
                    {
                        if (context.Exception.GetType() == typeof(SecurityTokenExpiredException))
                        {
                            context.Response.Headers.Add("Token-Expired", "true");
                            return Task.CompletedTask;
                        }

                        context.NoResult();
                        context.Response.StatusCode = 500;
                        context.Response.ContentType = "text/plain";
                        return context.Response.WriteAsync(context.Exception.ToString());
                    },
                    OnChallenge = context =>
                    {
                        context.HandleResponse();
                        context.Response.StatusCode = 401;
                        context.Response.ContentType = "application/json";
                        var result = JsonConvert.SerializeObject("401 Not authorized");
                        return context.Response.WriteAsync(result);
                    },
                    OnForbidden = context =>
                    {
                        context.Response.StatusCode = 403;
                        context.Response.ContentType = "application/json";
                        var result = JsonConvert.SerializeObject("403 Not authorized");
                        return context.Response.WriteAsync(result);
                    }
                };
            });

        services.AddScoped<IAuthorizationHandler, PermissionAuthorizationHandler>();

        services.AddAuthorization(options =>
        {
            Permissions.All().ForEach(permission =>
            {
                options.AddPolicy(permission,
                    builder => { builder.AddRequirements(new PermissionRequirement(permission)); });
            });
            // The rest omitted for brevity.
        });
        services.Configure<ForwardedHeadersOptions>(options =>
        {
            options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
        });
        services.Configure<RouteOptions>(options => options.LowercaseUrls = true);

        return services;
    }

    public static IServiceCollection AddSwaggerDocumentation(this IServiceCollection services, string title,
        string version)
    {
        return services.AddSwaggerGen(options =>
        {
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
                if (!assembly.IsDynamic)
                {
                    var xmlFile = $"{assembly.GetName().Name}.xml";
                    var xmlPath = Path.Combine(baseDirectory, xmlFile);
                    if (File.Exists(xmlPath)) options.IncludeXmlComments(xmlPath);
                }

            options.AddSwaggerDocs(title, version);

            options.OperationFilter<RemoveVersionFromParameterFilter>();
            options.DocumentFilter<ReplaceVersionWithExactValueInPathFilter>();
            options.OperationFilter<SwaggerExcludeFilter>();
            options.DocInclusionPredicate((ver, desc) =>
            {
                if (!desc.TryGetMethodInfo(out var methodInfo)) return false;

                var versions = methodInfo
                    .DeclaringType?
                    .GetCustomAttributes(true)
                    .OfType<ApiVersionAttribute>()
                    .SelectMany(attr => attr.Versions);

                var maps = methodInfo
                    .GetCustomAttributes(true)
                    .OfType<MapToApiVersionAttribute>()
                    .SelectMany(attr => attr.Versions)
                    .ToList();

                return versions?.Any(v => $"v{v}" == ver) == true
                       && (!maps.Any() || maps.Any(v => $"v{v}" == ver));
            });

            options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer",
                BearerFormat = "JWT",
                Description = "Input your Bearer token in this format - Bearer {your token here} to access this API"
            });
            options.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        },
                        Scheme = "Bearer",
                        Name = "Bearer",
                        In = ParameterLocation.Header
                    },
                    new List<string>()
                }
            });
            options.MapType<TimeSpan>(() => new OpenApiSchema
            {
                Type = "string",
                Nullable = true,
                Pattern = @"^([0-9]{1}|(?:0[0-9]|1[0-9]|2[0-3])+):([0-5]?[0-9])(?::([0-5]?[0-9])(?:.(\d{1,9}))?)?$",
                Example = new OpenApiString("02:00:00")
            });
        });
    }

    private static void AddSwaggerDocs(this SwaggerGenOptions options, string title, string version)
    {
        options.SwaggerDoc($"{version}", new OpenApiInfo
        {
            Version = version,
            Title = title,
            License = new OpenApiLicense
            {
                Name = "MIT License",
                Url = new Uri("https://opensource.org/licenses/MIT")
            }
        });
    }

    public static string[]? GetCorsOrigins(this IConfiguration configuration)
    {
        return configuration.GetValue<string>("App:CorsOrigins")
                ?.Split(",", StringSplitOptions.RemoveEmptyEntries)
                .ToArray();
         
    }

    public static string? GetDefaultPolicy(this IConfiguration configuration)
    {
        return configuration.GetValue<string>("Policies:Default");
    }
}