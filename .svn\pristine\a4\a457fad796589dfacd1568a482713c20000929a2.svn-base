﻿@{
    ViewData["Title"] = "Log Directory";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<style>
    .selected-file {
        color: blue; /* Change this color to blue or any other color you prefer */
    }
</style>
<link href="~/css/treeview.css" rel="stylesheet" />
<div class="page-content px-3">
    <div>
        <div class="d-flex align-items-center justify-content-between mb-2">
            <h6 class="page_title d-flex align-items-center">
                <i class="cp-BIA-rules"></i>
                <span>Server Log Directory</span>
            </h6>

            <div class="d-flex align-items-center">
                <!-- Server Dropdown for selecting server -->
                <label for="serverDropdown" class="me-2 fw-bold">Select Server:</label>
                <select id="serverDropdown" class="form-select me-3">
                    <option value="primaryServer">Primary Server</option>
                </select>

                <button id="logDownload" class="btn btn-primary btn-sm" disabled>Download</button>
            </div>
        </div>

        <div class="row g-2">
            <div class="col-4">
                <div class="card Card_Design_None">
                    <div class="card-body" style="height:calc(100vh - 113px); overflow:auto">
                        <b id="logDirectoryTitle">Log Directory</b>
                        <div id="folderTree" class="tree-menu">
                            <!-- Folder tree structure will be dynamically generated -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-8">
                <div class="card Card_Design_None">
                    <div class="card-body" style="height:calc(100vh - 113px); overflow:auto">
                        <div id="connectionError" class="d-none align-content-center justify-content-center h-100 pt-5">
                            <img src="~/img/isomatric/ServerLogTestConnection.svg" alt="Error" width="600" height="350" />
                        </div>
                        <div id="logContent" class="d-none" style=" white-space: pre-wrap">
                            <!-- Log content will be dynamically loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="~/js/report-charts/logreport.js"></script>

@* <!-- Server Dropdown for selecting server -->
<select id="serverDropdown" class="form-select">
    <option value="primaryServer">Primary Server</option>
    <option value="secondaryServer">Secondary Server</option>
</select> *@
@* <script>
    $(function () {
        // Initially show only the first sublist (if it exists), hide others
        $('ul.tree ul').hide();
        $('ul.tree ul:first').show();  // Only show the first sublist

        $('.tree li > ul').each(function (i) {
            var $subUl = $(this);
            var $parentLi = $subUl.parent('li');
            var $toggleIcon = i === 0
                ? '<i class="js-toggle-icon"><span class="cp-circle-minus"></span></i>'
                : '<i class="js-toggle-icon"><span class="cp-circle-plus"></span></i>';

            // Set initial folder icon
            var $folderIcon = i === 0
                ? '<i class="cp-folder-open me-1"></i>'
                : '<i class="cp-folder-close me-1"></i>';

            $parentLi.addClass('has-children');

            // Prepend the folder icon and toggle icon to the parent <li> and set click handler
            $parentLi.prepend($folderIcon).prepend($toggleIcon).find('.js-toggle-icon').on('click', function () {
                var $thisIcon = $(this);
                var $iconSpan = $thisIcon.find('span');
                var $folderIconSpan = $parentLi.find('i.cp-folder-open, i.cp-folder-close');

                // Toggle the folder icon and open/close the sublist
                if ($iconSpan.hasClass('cp-circle-minus')) {
                    // If the icon is a minus, switch to plus, close the folder and change to closed icon
                    $iconSpan.removeClass('cp-circle-minus').addClass('cp-circle-plus');
                    $folderIconSpan.removeClass('cp-folder-open').addClass('cp-folder-close'); // Change to closed icon
                    $subUl.stop(true, true).slideUp('fast'); // Slide up (close)
                } else {
                    // If the icon is a plus, switch to minus, open the folder and change to open icon
                    $iconSpan.removeClass('cp-circle-plus').addClass('cp-circle-minus');
                    $folderIconSpan.removeClass('cp-folder-close').addClass('cp-folder-open'); // Change to open icon
                    $subUl.stop(true, true).slideDown('fast'); // Slide down (open)
                }
            });
        });
    });


</script> *@