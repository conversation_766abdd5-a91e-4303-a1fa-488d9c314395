﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.ApprovalMatrixRequestModel.ApprovalMatrixRequestViewModel
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

@* <link href="~/lib/dr-calendar/datepicker/bootstrap-datetimepicker.min.css" rel="stylesheet" /> *@
<link href="~/css/WorkflowConfiguration.css" rel="stylesheet" />
<style>
    .Profile-img-list img {
        width: 20px;
        height: 20px;
    }

    .Profile-img-list li {
        border: 2px solid #fff;
        margin-left: -6px;
        box-shadow: 0 .125rem .25rem rgb(0 0 0 / 10%) !important;
    }

    .dropContainer .draggableContainer {
        width: fit-content;
        background: #fff;
    }

    .dropContainer {
        display: flex;
        justify-content: flex-start;
        flex-direction: column;
        align-items: center;
        gap: 0px;
    }

    .approvel-matrix-drag .workflowActions {
        width: auto;
    }

    .approvel-matrix-drag .ui-sortable-handle {
        margin-top: 10px;
    }

    .start {
        background-color: #30b027;
        width: 48px;
        color: white;
        padding: 3px;
        display: inline-block;
        border-radius: 5px
    }

    .end {
        background-color: #fcba03;
        width: 48px;
        padding: 3px;
        color: white;
        display: inline-block;
        border-radius: 5px
    }
    /* .dropContainer .draggableContainer::before{
                content: "\ea74";
                font-family:'cp-fonts';
            } */
    .offcanvas {
        z-index: 999999 !important;
    }
</style>
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="">
            <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                <h6 class="page_title">
                    <i class="cp-approval-matrix"></i>
                    <span>Template List</span>
                </h6>
                <form class="d-flex">
                    <div class="input-group me-2 w-auto">
                        <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                        <div class="input-group-text">
                            <div class="dropdown">
                                <span data-bs-toggle="dropdown" title="Filter">
                                    <i class="cp-filter"></i>
                                </span>
                                <ul class="dropdown-menu filter-dropdown">
                                    <li>
                                        <h6 class="dropdown-header">Filter Search</h6>
                                    </li>
                                    <li class="dropdown-item">
                                        <div>
                                            <input class="form-check-input" type="checkbox" value="" id="Name">
                                            <label class="form-check-label" for="Name">
                                                Template Name
                                            </label>
                                        </div>
                                    </li>

                                </ul>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-primary btn-approvalmattemplate-Create" data-bs-toggle="modal"
                            data-bs-target="#CreateModal" id="Create">
                        <i class="cp-add me-1"></i>Create
                    </button>
                </form>
            </div>
        </div>
        <div class="card-body pt-0">
            <table id="templateList" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Template Name</th>
                        <th>Operational Service</th>
                        <th>Operational Function</th>
                        @*<th>ID</th>
                        <th>Process Name</th>
                        <th>Status</th>
                        <th>Approvers</th>
                        <th>Created By</th>*@
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>

    <!--offcanvas-->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasExample" aria-labelledby="offcanvasExampleLabel">
        <div class="offcanvas-header">
            <h6 class="offcanvas-title page_title" id="offcanvasExampleLabel">Process Configuration</h6>
            <div class="d-flex align-items-center">
                <span role="button" id="processDelete" class="ms-3 me-2"><i class="cp-Delete"></i></span>
                <button type="button" class="btn-close" id="closeOffcanvas" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
        </div>
        <div class="offcanvas-body">
            <div class="row">
                <div class="col-xl-12">
                    <div class="firstWizard">

                        <div class="mb-2 form-group">
                            <div class="form-label">Process Name</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-bag"></i></span>
                                <input type="text"
                                       autocomplete="off"
                                       class="form-control"
                                       id="approvalProcessName"
                                       placeholder="Enter Process Name" />
                            </div>
                            <span id="approvalProcessNameError"></span>
                        </div>

                        <div class="mb-2">
                            <div class="mb-3 form-group">
                                <div class="form-label">Description<small class="text-secondary">( Optional )</small></div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-description"></i></span>
                                    <input type="text" autocomplete="off" id="textDescription" class="form-control" maxlength="500" placeholder="Description" />
                                </div>
                                <span id="ActivityDetails-error"></span>
                            </div>
                        </div>

                        <div class="d-flex flex-column" id="approverContainer">
                            @* <div class="fw-semibold">User</div> *@
                            @* <div class="form-group d-flex"> *@
                            @* <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input restore-check" id="userList" name="restore" checked />
                            <label class='form-check-label'>User</label>
                            </div>

                            <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input restore-check" id="userGroupList" name="restore" />
                            <label class='form-check-label'>User Group</label>
                            </div> *@
                            @* </div> *@
                            <div class="mb-2 form-group">
                                <div class="form-label" id="dynamicLabel">User</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-bag"></i></span>
                                    <select id="userNameList" class="form-select-offcanvas w-100" data-placeholder="Select User"
                                            aria-label="Default select example" multiple>
                                    </select>
                                </div>
                                <span id="UserNameError"></span>
                            </div>
                        </div>

                        <div class="mb-2 mt-3" id="SLAApproverContainer">
                            <div class="">
                                SLA
                            </div>
                            <div class="d-flex align-items-center gap-3 ms-1">
                                <div class="form-group w-100">
                                    <div class="input-group">
                                        <input type="number" id="inputDuration" class="form-control w-50" placeholder="Enter Duration" />
                                    </div>
                                    <span id="enterDurationError"></span>
                                </div>
                                <div class="form-group w-100">
                                    <div class="input-group">
                                        <select class="form-select-offcanvas w-50" id="selectDuration" data-placeholder="Select Duration">
                                            <option value=""></option>
                                            <option value="minutes">Minutes</option>
                                            <option value="hours">Hours</option>
                                            <option value="days">Days</option>
                                            <option value="weeks">Weeks</option>
                                            <option value="months">Months</option>
                                        </select>
                                    </div>
                                    <span id="selectDurationError"></span>
                                </div>
                            </div>
                        </div>                       
                    </div>

                    <div class="secondWizard d-none">
                        <div id="ruleSetContainer">
                            <div class="mb-3 fw-semibold">Rule Set</div>
                            <div class="mb-3 ms-2">
                                @*  <div class="mb-3 form-group">
                                <div class="form-label">
                                <small class="text-secondary">
                                Rule Set<span role="button" class="ms-2 text-primary" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
                                <i class="cp-circle-plus"></i>
                                </span>

                                </small>
                                </div>
                                </div> *@
                                <div>
                                    <div class="mb-3">
                                        <div class="form-label">Approval</div>
                                        <div class="d-flex align-items-center gap-3">
                                            <div class="form-group w-50">
                                                <div class="input-group">
                                                    @* <span class="input-group-text form-label">If</span> *@
                                                    <select class="form-select-offcanvas" id="ApOne" data-placeholder="Select Option">
                                                        <option></option>
                                                        <option>All</option>
                                                        <option>Majority</option>
                                                        <option>Any</option>
                                                        <option>Custom</option>
                                                    </select>
                                                </div>
                                                <span id="ApOneError"></span>
                                            </div>
                                            <div class="form-group w-50">
                                                <div class="input-group">
                                                    @* <span class="input-group-text form-label">At</span> *@
                                                    <input type="number" id="ApTwo" class="form-control" placeholder="Enter Approvar" />
                                                </div>
                                                <span id="ApTwoError"></span>
                                            </div>
                                            <div class="form-group w-100">
                                                @* <div class="input-group"> *@
                                                approval out of <span id="ApThree">0</span> approver(s)
                                                @* <span class="input-group-text form-label">Approval</span> *@
                                                @* <select class="form-select-offcanvas" id="ApThree">
                                                <option></option>
                                                <option>1</option>
                                                <option>2</option>
                                                </select> *@
                                                @* </div>
                                                <span id="ApThree-error"></span> *@
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="form-label">Reject</div>
                                        <div class="d-flex align-items-center gap-3">
                                            <div class="form-group w-50">
                                                <div class="input-group">
                                                    @* <span class="input-group-text form-label">If</span> *@
                                                    <select class="form-select-offcanvas" id="RjOne" data-placeholder="Select Option">
                                                        <option></option>
                                                        <option>At Least One</option>
                                                        <option>All</option>
                                                        <option>Majority</option>
                                                         <option>Custom</option> 
                                                    </select>
                                                </div>
                                                <span id="RjOneError"></span>
                                            </div>
                                            <div class="form-group w-50">
                                                <div class="input-group">
                                                    @* <span class="input-group-text form-label">At</span> *@
                                                    <input type="number" id="RjTwo" class="form-control" placeholder="Enter Approvar" />
                                                </div>
                                                <span id="RjTwoError"></span>
                                            </div>
                                            <div class="form-group w-100">
                                                @* <div class="input-group"> *@
                                                rejects out of <span id="RjThree">0</span> approver(s)
                                                @* <span class="input-group-text form-label">Approval</span> *@
                                                @* <select class="form-select-offcanvas" id="ApThree">
                                                <option></option>
                                                <option>1</option>
                                                <option>2</option>
                                                </select> *@
                                                @* </div>
                                                <span id="ApThree-error"></span> *@
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="approvalNotificationContainer">
                            @* <div class="fw-semibold">Notification</div> *@
                            <div class="mb-3">
                                <div class="form-label fw-semibold">Notification Type</div>
                                <div class="p-2 ms-2">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex flex-column align-items-center">                                           
                                            <span class="p-3 shadow-sm rounded"> 
                                                <input type="radio" checked class="btn-check pair" name="typecategory" id="emailIcon">
                                                <label class="site_type btn border-secondary-subtle" for="emailIcon">
                                                    <i class="cp-email align-middle text-primary fw-semibold fs-5"></i>
                                                </label>
                                             </span> 
                                             <span class="mt-2">Email</span>
                                        </div>
                                        <div class="d-flex flex-column align-items-center">
                                            <span class="p-3 shadow-sm rounded"> 
                                                <input type="checkbox" class="btn-check pair" name="messagecategory" id="messageIcon">
                                                <label class="site_type btn border-secondary-subtle" for="messageIcon">
                                                    <i class="cp-message-alert align-middle text-primary fw-semibold fs-5"></i>
                                                </label>                                                
                                             </span> <span class="mt-2">SMS</span>
                                        </div>
                                        <div class="d-flex flex-column align-items-center">
                                            <span class="p-3 shadow-sm rounded"> 
                                                <input type="checkbox" class="btn-check pair" name="applicationcategory" id="applicationIcon">
                                                <label class="site_type btn border-secondary-subtle" for="applicationIcon">
                                                    <i class="cp-alerts align-middle text-primary fw-semibold fs-5"></i> 
                                                </label>                                                            
                                            </span> <span class="mt-2">Application</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>                   

                </div>
            </div>
        </div>
        <div class="modal-footer d-flex justify-content-end">
            @*  <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small> *@
            <div class="gap-2 d-flex mb-3">              
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="offcanvas" aria-label="Close" cursorshover="true">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm d-none" id="previousButton">Previous</button>
                <button type="button" class="btn btn-primary btn-sm" id="nextButton">Next</button>
                <button type="button" class="btn btn-primary btn-sm d-none" id="saveProcessName">Add</button>
            </div>
        </div>
    </div>

    <!--Modal Create-->
    <div class="modal fade" data-bs-backdrop="static" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-xl ">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title">
                        <i class="cp-approval-matrix"></i>
                        <span>Template Configuration</span>
                    </h6>
                    <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    @*   <button class="btn btn-primary" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasExample" aria-controls="offcanvasExample">
                    Button with data-bs-target
                    </button> *@
                    <div class="row g-3 mt-0 position-relative">
                        <div class="col-md-12 mt-1">
                            <div class="card Card_Design_None">
                                <div class="card-header header">
                                    <h6 class="page_title">
                                        <i class="cp-approval-matrix"></i><span id="templateTitle">Untitled Template</span>
                                    </h6>

                                    <button type="button" class="btn btn-sm btn-primary" style="visibility: hidden;" id="saveApprovalTemplate">Save</button>
                                </div>
                                <div class="card-body position-relative p-1 pt-0">
                                    <div class="d-flex gap-2 flex-column position-absolute" style="top: 10px; right: 15px;">
                                        @* <div>
                                        <button class="btn btn-light btn-sm" title="Edit"><i class="cp-edit"></i></button>
                                        </div> *@
                                        @* <div>
                                            <button class="btn btn-light btn-sm" title="Lock"><i class="cp-lock"></i></button>
                                        </div> *@
                                        <div>
                                            <button data-bs-toggle="offcanvas" href="#offcanvasExample" role="button"
                                                    aria-controls="offcanvasExample" class="btn btn-light btn-sm" id="addTemplate"
                                                    title="Add">
                                                <i class="cp-circle-plus"></i>
                                            </button>
                                        </div>
                                        @*  <div>
                                            <button class="btn btn-light btn-sm" id="addTransition" disabled title="Add Transition"><i class="cp-add-new"></i></button>
                                        </div> *@
                                        <div>
                                            <button class="btn btn-light btn-sm" id="endTemplate" title="End"><i class="cp-end"></i></button>
                                        </div>
                                    </div>
                                    <div class="row m-1 rounded border border-light-subtle w-100" style="background-image: radial-gradient(#ebebeb 1px, #fff 1px) !important;background-size: 15px 15px;">

                                        <div class="col-11 text-center">
                                            <div class="mt-2">
                                                @* <img title="Start" src="/img/input_Icons/Start.svg" width="30" height="30" draggable="false" loading="lazy" alt="Start image"> *@
                                            </div>
                                            <div class="text-center dropContainer position-relative" id="approvalContainer" style="height: calc(100vh - 235px); overflow-y: auto;">
                                            </div>
                                        </div>

                                    </div>
                                    @*  <div class="col-9 text-center dropContainer" style="height: calc(100vh - 130px); overflow-y: auto;">
                                    <div id="drop-area">
                                    </div>
                                    </div>
                                    <div style="position: absolute;top: 3%;left: 60%;" id="startElement">
                                    <img title="Start" src="/img/input_Icons/Start.svg" width="30" height="30"  loading="lazy" alt="Start image">
                                    </div> *@
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="col-md-3 mt-1 d-none">
                        @*  <div class="card Card_Design_None">
                        <div class="card-header card-title">Matrix Template / Untitled</div>
                        <div class="card-body" style="height:calc(100vh - 155px);overflow:auto">
                        <div class="form-group">
                        <div class="form-label">Name</div>
                        <div class="input-group">
                        <span class="input-group-text">
                        <i class="cp-name"></i>
                        </span>
                        <input type="text" class="form-control" id="txttemplatename" placeholder="Enter the title of the template" autocomplete="off" required />
                        </div>
                        </div>
                        <div class="form-group">
                        <div class="form-label">Description</div>
                        <div class="input-group">
                        <span class="input-group-text">
                        <i class="cp-name"></i>
                        </span>
                        <input type="text" id="temdescription" class="form-control" placeholder="Give detail description of the template" required />
                        </div>
                        </div>
                        <div class="form-group">
                        <div class="form-label">SLA Time Report</div>
                        <div class="input-group">
                        <span class="input-group-text small">Set the timeline in hours</span>
                        <input class="form-control" type="datetime-local" name="sdate" placeholder="Date And Time" required />
                        @*  <input type="time" class="form-control" placeholder="Give detail description of the template" required />
                        <input type="time" class="form-control" placeholder="Give detail description of the template" required /> *@
                        @*  </div>
                        </div>
                        <div class="form-group">
                        <label class="form-label custom-cursor-default-hover"
                        for="Notification Type">Notification Type</label>
                        <div class="d-flex gap-4 flex-wrap mt-2">
                        <div title="Application">
                        <input class="btn-check" type="checkbox" name="options" value="Application" id="option1" autocomplete="off" checked>
                        <label class="site_type btn border-secondary-subtle custom-cursor-on-hover" for="option1" cursorshover="true"> <i class="cp-application fs-5 custom-cursor-on-hover" cursorshover="true"></i> </label>
                        <div class="text-center mt-2 d-block text-truncate" style="max-width:70px">Application</div>
                        </div>
                        <div title="Email">
                        <input class="btn-check" type="checkbox" name="options" value="Email" id="option2" autocomplete="off">
                        <label class="site_type btn border-secondary-subtle custom-cursor-on-hover" for="option2" cursorshover="true"> <i class="cp-email fs-5 "></i> </label>
                        <div class="text-center mt-2 d-block text-truncate" style="max-width:70px">Email</div>
                        </div>
                        <div title="SMS">
                        <input class="btn-check" type="checkbox" name="options" value="SMS" id="option3" autocomplete="off">
                        <label class="site_type btn border-secondary-subtle custom-cursor-on-hover" for="option3" cursorshover="true"> <i class="cp-sms-gateway fs-5 custom-cursor-on-hover " cursorshover="true"></i> </label>
                        <div class="text-center mt-2 d-block text-truncate" style="max-width:70px">SMS</div>
                        </div>
                        </div>
                        <input type="hidden" id="txtnotificationType" />
                        </div>
                        <div class="form-group">
                        <div class="input-group border-bottom-0 justify-content-between">
                        <div class="input-group-text">
                        <input class="form-check-input mt-0" type="checkbox" value="" aria-label="Checkbox for following text input">
                        <div class="card-title fw-normal">Select Users</div>
                        </div>
                        <div class="input-group-text">
                        <ul class="Profile-img-list" id="ProfileImageList">
                        <li title="Facebook"><img src="../../img/profile-img/Folkman.png"></li>
                        <li title="Facebook"><img src="../../img/profile-img/Folkman.png"></li>
                        <li title="Facebook"><img src="../../img/profile-img/Folkman.png"></li>
                        <li title="Facebook"><img src="../../img/profile-img/Folkman.png"></li>
                        </ul>
                        <small class="ms-2">+ 5 Members</small>
                        </div>
                        </div>
                        </div>
                        <div class="form-group">
                        <div class="form-label">Rule</div>
                        <div class="input-group">
                        <span class="input-group-text"><i class="cp-rule"></i></span>
                        <select class="form-select" id="Rule" title="Some placeholder text...">
                        <option value="Any One">Any One</option>
                        <option value="Any Two">Any Two</option>
                        </select>
                        </div>
                        </div>
                        </div>
                        <div class="card-footer text-end">
                        <button class="btn btn-sm btn-secondary">Reset</button>
                        <button class="btn btn-sm btn-primary">Save</button>
                        </div>
                        </div>  *@
                    </div>
                </div>
            </div>
            @* <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary"><i class="bi bi-info-circle me-1"></i>Note: All fields are mandatory except Optional</small>
            <div class="gap-2 d-flex">
            <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#teamModal">Save</button>
            </div>
            </div> *@
        </div>
    </div>
</div>

<!-- Modal -->
@* <div class="modal fade" id="processModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5>Add Process Name</h5>
            </div>
            <div class="modal-body pt-2">
                <div class="form-group">
                    <div class="form-label">Process Name</div>
                    <div class="input-group">
                        <input type="text"
                               class="form-control"
                               id="approvalProcessName"
                               placeholder="Enter Process Name" />
                    </div>
                    <span id="approvalProcessNameError"></span>
                </div>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="saveProcessName">Save</button>
            </div>
        </div>
    </div>
</div> *@

<!--Save Modal -->
<div class="modal fade" id="saveprocessModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5>Template Configuration</h5>
                @* <img class="w-100" src="~/img/isomatric/confirmation.svg" alt="Duplicate Actions" /> *@
            </div>
            <div class="modal-body pt-2">
                <div class="form-group">
                    <div class="form-label">Template Name</div>
                    <div class="input-group">
                        <input type="text"
                               class="form-control"
                               id="approvalTemplateName"
                               autocomplete="off"
                               placeholder="Enter Template Name" />
                    </div>
                    <span id="approvalTemplateNameError"></span>
                </div>

                 <div class="form-group mt-2">
                     <div class="form-label">Operational Service</div>
                     <div class="input-group">
                          <span class="input-group-text"><i class="cp-business-service"></i></span>
                          <select class="form-select-modal" id="selectOperationalService" data-placeholder="Select Operational Service">
                          </select>
                     </div>
                     <span id="operationalServiceError"></span>
                 </div>

                  <div class="form-group mt-2">
                     <div class="form-label">Operational Function</div>
                     <div class="input-group">
                          <span class="input-group-text"><i class="cp-business-function"></i></span>
                          <select class="form-select-modal" id="selectOperationalFunction" data-placeholder="Select Operational Function">
                          </select>
                     </div>
                     <span id="operationalFunctionError"></span>
                 </div>
            </div>           

            <input type="hidden" id="templateID" />
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="saveTemplateName">Save</button>
            </div>
        </div>
    </div>
</div>

<!--Add Transition-->
@* <div class="modal fade" id="addTransitionModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5>Add Transition</h5>
            </div>
            <div class="modal-body pt-2">
                <div class="form-group">
                    <div class="form-label">From Name</div>
                    <div class="input-group">
                        <input type="text"
                               class="form-control"
                               id="templateFromName"
                               autocomplete="off"
                               placeholder="Enter From Name" />
                    </div>
                    <span id="templateFromNameError"></span>
                </div>

                <div class="form-group">
                    <div class="form-label">To Name</div>
                    <div class="input-group">
                        <input type="text"
                               class="form-control"
                               id="templateToName"
                               autocomplete="off"
                               placeholder="Enter To Name" />
                    </div>
                    <span id="templateToNameError"></span>
                </div>

                <div class="form-group">
                    <div class="form-label">Name</div>
                    <div class="input-group">
                        <input type="text"
                               class="form-control"
                               id="transitionName"
                               autocomplete="off"
                               placeholder="Enter Transition Name" />
                    </div>
                    <span id="transitionNameError"></span>
                </div>

                <div class="form-group">
                    <div class="form-label">Colour</div>
                    <div class="input-group">
                        <input type="text"
                               class="form-control"
                               id="transitionColour"
                               autocomplete="off"
                               placeholder="Enter Colour Code" />
                    </div>
                    <span id="transitionColourError"></span>
                </div>
            </div>
            <input type="hidden" id="templateID" />
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-primary btn-sm" id="validateTransition">Add</button>
            </div>
        </div>
    </div>
</div>
 *@

<!--Modal Delete-->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="Delete" />
</div>

@* Duplicate Actions *@
<div class="modal fade" id="DuplicateActionsModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <img class="w-100" src="~/img/isomatric/confirmation.svg" alt="Duplicate Actions" />
            </div>
            <div class="modal-body text-center pt-5">
                <h5 class="fw-bold">Confirmation</h5>
                <h6 class="fw-bold">Are you sure?</h6>Do you want to change your credentials? <p>Once the credential was updated, couldn't retrieve the password.</p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-target="#CreateModal" data-bs-toggle="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="duplicateConfirmation">Yes</button>
            </div>
        </div>
    </div>
</div>

<div id="AdminCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.CreateAndEdit" aria-hidden="true"></div>
<div id="AdminDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.Delete" aria-hidden="true"></div>

<!-- Modal -->
<div class="modal fade" id="BaselineModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header justify-content-end">
                @*   <h6 class="page_title"><span>Process Name</span></h6> *@
                <button type="button" class="btn-close ms-2 cancel" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <div class="form-label">Process Name</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-name"></i></span>
                        <input type="text" id="cpRoleName" maxlength="100" class="form-control" value="" placeholder="Enter Process Name" autocomplete="off">


                    </div>
                    <span id="Name-error" class="field-validation-error">Enter Process Name</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary cancel" data-bs-dismiss="modal">Cancel</button>

                <button type="button" class="btn btn-primary BaseSnapSave">Save</button>
            </div>
        </div>
    </div>
</div>

<script>
    $('.offcanvas ').on('shown.bs.offcanvas', function (e) {
        $(this).find('.form-select-offcanvas').select2({
            dropdownParent: $(this).find('.offcanvas-body')
        });
    })
</script>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<script src="~/js/manage/approvalmatrix/template.js"></script>
<script src="~/js/manage/approvalmatrix/templatefunctions.js"></script>

@* <script src="~/js/DRCalender/Scripts/moment.min.js"></script>
<script src="~/lib/dr-calendar/datepicker/bootstrap-datetimepicker.min.js"></script> *@
