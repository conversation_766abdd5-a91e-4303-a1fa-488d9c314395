﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class ImpactAvailabilityRepositoryMocks
{
    public static Mock<IImpactAvailabilityRepository> CreateImpactAvailabilityRepository(List<ImpactAvailability> impactAvailabilities)
    {
        var createImpactAvailabilityRepository = new Mock<IImpactAvailabilityRepository>();

        createImpactAvailabilityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(impactAvailabilities);

        createImpactAvailabilityRepository.Setup(repo => repo.AddAsync(It.IsAny<ImpactAvailability>())).ReturnsAsync(
            (ImpactAvailability impactAvailability) =>
            {
                impactAvailability.Id = new Fixture().Create<int>();

                impactAvailability.ReferenceId = new Fixture().Create<Guid>().ToString();

                impactAvailabilities.Add(impactAvailability);

                return impactAvailability;
            });

        return createImpactAvailabilityRepository;
    }

    public static Mock<IImpactAvailabilityRepository> UpdateImpactAvailabilityRepository(List<ImpactAvailability> impactAvailabilities)
    {
        var updateImpactAvailabilityRepository = new Mock<IImpactAvailabilityRepository>();

        updateImpactAvailabilityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(impactAvailabilities);

        updateImpactAvailabilityRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => impactAvailabilities.SingleOrDefault(x => x.ReferenceId == i));

        updateImpactAvailabilityRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ImpactAvailability>())).ReturnsAsync((ImpactAvailability impactAvailability) =>
        {
            var index = impactAvailabilities.FindIndex(item => item.ReferenceId == impactAvailability.ReferenceId);

            impactAvailabilities[index] = impactAvailability;

            return impactAvailability;
        });

        return updateImpactAvailabilityRepository;
    }

    public static Mock<IImpactAvailabilityRepository> DeleteImpactAvailabilityRepository(List<ImpactAvailability> impactAvailabilities)
    {
        var deleteImpactAvailabilityRepository = new Mock<IImpactAvailabilityRepository>();

        deleteImpactAvailabilityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(impactAvailabilities);

        deleteImpactAvailabilityRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => impactAvailabilities.SingleOrDefault(x => x.ReferenceId == i));

        deleteImpactAvailabilityRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ImpactAvailability>())).ReturnsAsync((ImpactAvailability impactAvailability) =>
        {
            var index = impactAvailabilities.FindIndex(item => item.ReferenceId == impactAvailability.ReferenceId);

            impactAvailability.IsActive = false;

            impactAvailabilities[index] = impactAvailability;

            return impactAvailability;
        });

        return deleteImpactAvailabilityRepository;
    }

    public static Mock<IImpactAvailabilityRepository> GetImpactAvailabilityRepository(List<ImpactAvailability> impactAvailabilities)
    {
        var impactAvailabilityRepository = new Mock<IImpactAvailabilityRepository>();

        impactAvailabilityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(impactAvailabilities);

        impactAvailabilityRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => impactAvailabilities.SingleOrDefault(x => x.ReferenceId == i));

        return impactAvailabilityRepository;
    }

    public static Mock<IImpactAvailabilityRepository> GetImpactAvailabilityEmptyRepository()
    {
        var impactAvailabilityEmptyRepository = new Mock<IImpactAvailabilityRepository>();

        impactAvailabilityEmptyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<ImpactAvailability>());

        return impactAvailabilityEmptyRepository;
    }

    public static Mock<IImpactAvailabilityRepository> GetPaginatedImpactAvailabilityRepository(List<ImpactAvailability> impactAvailabilities)
    {
        var paginatedImpactAvailabilityRepository = new Mock<IImpactAvailabilityRepository>();

        var queryableImpactAvailability = impactAvailabilities.BuildMock();

        paginatedImpactAvailabilityRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableImpactAvailability);

        return paginatedImpactAvailabilityRepository;
    }
}