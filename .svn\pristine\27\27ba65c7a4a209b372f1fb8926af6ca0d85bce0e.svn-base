namespace ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Create;

public class CreateDriftImpactTypeMasterCommandValidator : AbstractValidator<CreateDriftImpactTypeMasterCommand>
{
    private readonly IDriftImpactTypeMasterRepository _driftImpactTypeMasterRepository;

    public CreateDriftImpactTypeMasterCommandValidator(IDriftImpactTypeMasterRepository driftImpactTypeMasterRepository)
    {
        _driftImpactTypeMasterRepository = driftImpactTypeMasterRepository;
    }
}