using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FiaImpactType.Events.Delete;

public class FiaImpactTypeDeletedEventHandler : INotificationHandler<FiaImpactTypeDeletedEvent>
{
    private readonly ILogger<FiaImpactTypeDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FiaImpactTypeDeletedEventHandler(ILoggedInUserService userService,
        ILogger<FiaImpactTypeDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(FiaImpactTypeDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} FiaImpactType",
            Entity = "FiaImpactType",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"FiaImpactType '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"FiaImpactType '{deletedEvent.Name}' deleted successfully.");
    }
}