using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Queries.GetDb2HaDrMonitorStatusByInfraObjectId;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.Db2HaDrMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class Db2HaDrMonitorStatusFixture : IDisposable
{
    public CreateDb2HaDrMonitorStatusCommand CreateDb2HaDrMonitorStatusCommand { get; }
    public CreateDb2HaDrMonitorStatusResponse CreateDb2HaDrMonitorStatusResponse { get; }
    public UpdateDb2HaDrMonitorStatusCommand UpdateDb2HaDrMonitorStatusCommand { get; }
    public UpdateDb2HaDrMonitorStatusResponse UpdateDb2HaDrMonitorStatusResponse { get; }
    public Db2HaDrMonitorStatusDetailQuery Db2HaDrMonitorStatusDetailQuery { get; }
    public Db2HaDrMonitorStatusDetailVm Db2HaDrMonitorStatusDetailVm { get; }
    public Db2HaDrMonitorStatusListQuery Db2HaDrMonitorStatusListQuery { get; }
    public List<Db2HaDrMonitorStatusListVm> Db2HaDrMonitorStatusListVm { get; }
    public GetDb2HaDrMonitorStatusPaginatedListQuery GetDb2HaDrMonitorStatusPaginatedListQuery { get; }
    public PaginatedResult<Db2HaDrMonitorStatusListVm> Db2HaDrMonitorStatusPaginatedResult { get; }
    public GetDb2HaDrMonitorStatusDetailByTypeQuery GetDb2HaDrMonitorStatusDetailByTypeQuery { get; }
    public Db2HaDrMonitorStatusDetailByTypeVm Db2HaDrMonitorStatusDetailByTypeVm { get; }
    public GetDb2HaDrMonitorStatusByInfraObjectIdQuery GetDb2HaDrMonitorStatusByInfraObjectIdQuery { get; }

    public Db2HaDrMonitorStatusFixture()
    {
        var fixture = new Fixture();

        // Configure fixture for enterprise scenarios
        fixture.Customize<CreateDb2HaDrMonitorStatusCommand>(c => c
            .With(x => x.Type, "Enterprise DB2 HADR Status")
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectName, "Enterprise DB2 Status Database")
            .With(x => x.WorkflowId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, "Enterprise HADR Status Workflow")
            .With(x => x.ConfiguredRPO, "10")
            .With(x => x.DataLagValue, "3")
            .With(x => x.Threshold, "20")
            .With(x => x.Properties, @"{
                ""statusMonitoring"": ""enabled"",
                ""alertLevel"": ""high"",
                ""replicationHealth"": ""optimal"",
                ""performanceMetrics"": ""enabled"",
                ""automaticFailover"": true
            }"));

        fixture.Customize<CreateDb2HaDrMonitorStatusResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise DB2 HADR Monitor Status created successfully!")
            .With(x => x.Success, true));

        fixture.Customize<UpdateDb2HaDrMonitorStatusCommand>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Type, "Enterprise DB2 HADR Status Update")
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectName, "Enterprise DB2 Status Update Database")
            .With(x => x.WorkflowId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, "Enterprise HADR Status Update Workflow")
            .With(x => x.ConfiguredRPO, "8")
            .With(x => x.DataLagValue, "2")
            .With(x => x.Threshold, "15")
            .With(x => x.Properties, @"{
                ""statusUpdate"": ""enabled"",
                ""alertLevel"": ""critical"",
                ""replicationHealth"": ""excellent"",
                ""performanceMetrics"": ""enhanced""
            }"));

        fixture.Customize<UpdateDb2HaDrMonitorStatusResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise DB2 HADR Monitor Status updated successfully!")
            .With(x => x.Success, true));

        fixture.Customize<Db2HaDrMonitorStatusDetailVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Type, "Enterprise DB2 HADR Status Detail")
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectName, "Enterprise DB2 Status Detail Database")
            .With(x => x.WorkflowId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, "Enterprise HADR Status Detail Workflow")
            .With(x => x.ConfiguredRPO, "12")
            .With(x => x.DataLagValue, "4")
            );

        fixture.Customize<Db2HaDrMonitorStatusListVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Type, "Enterprise DB2 HADR Status List")
            .With(x => x.InfraObjectName, "Enterprise DB2 Status List Database")
            .With(x => x.WorkflowName, "Enterprise HADR Status List Workflow")
            .With(x => x.ConfiguredRPO, "15")
            .With(x => x.DataLagValue, "5"));

        fixture.Customize<Db2HaDrMonitorStatusDetailByTypeVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Type, "Enterprise DB2 HADR Status Type")
            .With(x => x.InfraObjectName, "Enterprise DB2 Status Type Database")
            .With(x => x.WorkflowName, "Enterprise HADR Status Type Workflow")
            .With(x => x.ConfiguredRPO, "18")
            .With(x => x.DataLagValue, "6"));

        fixture.Customize<GetDb2HaDrMonitorStatusPaginatedListQuery>(c => c
            .With(x => x.SearchString, "Enterprise Status")
            .With(x => x.PageNumber, 1)
            .With(x => x.PageSize, 10)
            .With(x => x.SortColumn, "InfraObjectName")
            .With(x => x.SortOrder, "ASC"));

        // Create instances
        CreateDb2HaDrMonitorStatusCommand = fixture.Create<CreateDb2HaDrMonitorStatusCommand>();
        CreateDb2HaDrMonitorStatusResponse = fixture.Create<CreateDb2HaDrMonitorStatusResponse>();
        
        UpdateDb2HaDrMonitorStatusCommand = fixture.Create<UpdateDb2HaDrMonitorStatusCommand>();
        UpdateDb2HaDrMonitorStatusResponse = fixture.Create<UpdateDb2HaDrMonitorStatusResponse>();
        
        Db2HaDrMonitorStatusDetailQuery = new Db2HaDrMonitorStatusDetailQuery { Id = Guid.NewGuid().ToString() };
        Db2HaDrMonitorStatusDetailVm = fixture.Create<Db2HaDrMonitorStatusDetailVm>();
        
        Db2HaDrMonitorStatusListQuery = new Db2HaDrMonitorStatusListQuery();
        Db2HaDrMonitorStatusListVm = fixture.CreateMany<Db2HaDrMonitorStatusListVm>(4).ToList();
        
        GetDb2HaDrMonitorStatusPaginatedListQuery = fixture.Create<GetDb2HaDrMonitorStatusPaginatedListQuery>();
        Db2HaDrMonitorStatusPaginatedResult = new PaginatedResult<Db2HaDrMonitorStatusListVm>
        {
            Data = fixture.CreateMany<Db2HaDrMonitorStatusListVm>(6).ToList(),
            TotalCount = 6,
            PageSize = 10,
            Succeeded = true
        };
        
        GetDb2HaDrMonitorStatusDetailByTypeQuery = new GetDb2HaDrMonitorStatusDetailByTypeQuery { Type = "Enterprise DB2 HADR Status" };
        Db2HaDrMonitorStatusDetailByTypeVm = fixture.Create<Db2HaDrMonitorStatusDetailByTypeVm>();
        
        GetDb2HaDrMonitorStatusByInfraObjectIdQuery = new GetDb2HaDrMonitorStatusByInfraObjectIdQuery { InfraObjectId = Guid.NewGuid().ToString() };
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
