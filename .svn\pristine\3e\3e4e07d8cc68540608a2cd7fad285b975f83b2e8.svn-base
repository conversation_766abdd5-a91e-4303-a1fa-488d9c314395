using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Events.Create;

namespace ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Create;

public class CreateDriftImpactTypeMasterCommandHandler : IRequestHandler<CreateDriftImpactTypeMasterCommand,
    CreateDriftImpactTypeMasterResponse>
{
    private readonly IDriftImpactTypeMasterRepository _driftImpactTypeMasterRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateDriftImpactTypeMasterCommandHandler(IMapper mapper,
        IDriftImpactTypeMasterRepository driftImpactTypeMasterRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _driftImpactTypeMasterRepository = driftImpactTypeMasterRepository;
    }

    public async Task<CreateDriftImpactTypeMasterResponse> Handle(CreateDriftImpactTypeMasterCommand request,
        CancellationToken cancellationToken)
    {
        var driftImpacttypeMaster = _mapper.Map<Domain.Entities.DriftImpactTypeMaster>(request);

        driftImpacttypeMaster = await _driftImpactTypeMasterRepository.AddAsync(driftImpacttypeMaster);

        var response = new CreateDriftImpactTypeMasterResponse
        {
            Message = Message.Create(nameof(Domain.Entities.DriftImpactTypeMaster), driftImpacttypeMaster.ImpactType),

            Id = driftImpacttypeMaster.ReferenceId
        };

        await _publisher.Publish(new DriftImpactTypeMasterCreatedEvent { Name = driftImpacttypeMaster.ImpactType },
            cancellationToken);

        return response;
    }
}