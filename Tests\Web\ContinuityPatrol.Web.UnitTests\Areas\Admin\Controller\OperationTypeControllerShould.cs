﻿using AutoFixture;
using ContinuityPatrol.Application.Features.WorkflowActionType.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowActionType.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowActionType.Events.PaginatedEvent;
using ContinuityPatrol.Application.Features.WorkflowActionType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;
using Microsoft.Extensions.Primitives;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class OperationTypeControllerShould
    {
        private readonly Mock<ILogger<OperationTypeController>> _mockLogger = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IPublisher> _mockPublisher = new();
        private OperationTypeController _controller;
        private readonly Fixture _fixture;

        public OperationTypeControllerShould()
        {
            _fixture = new Fixture();
            Initialize();
        }

        internal void Initialize()
        {
            _controller = new OperationTypeController(
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockPublisher.Object,
                _mockMapper.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new OperationTypeController(
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockPublisher.Object,
                _mockMapper.Object
            );

            // Assert
            Assert.NotNull(controller);
        }

        [Fact]
        public async Task List_ShouldReturnViewResult()
        {
            // Arrange
            _mockPublisher
                .Setup(p => p.Publish(It.IsAny<WorkflowActionTypePaginatedEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.List();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public async Task List_ShouldPublishPaginatedEvent()
        {
            // Arrange
            _mockPublisher
                .Setup(p => p.Publish(It.IsAny<WorkflowActionTypePaginatedEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // Act
            await _controller.List();

            // Assert
            _mockPublisher.Verify(
                p => p.Publish(It.IsAny<WorkflowActionTypePaginatedEvent>(), It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_WithEmptyId_ShouldCreateNewWorkflowActionType()
        {
            // Arrange
            var viewModel = _fixture.Create<WorkflowActionTypeViewModel>();
            var dic = new Dictionary<string, StringValues> { { "id", "" } };
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = _fixture.Create<CreateWorkflowActionTypeCommand>();
            var response = new BaseResponse { Success = true, Message = "Created Successfully" };

            _mockMapper.Setup(m => m.Map<CreateWorkflowActionTypeCommand>(viewModel))
                .Returns(command);

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.CreateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonData = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", jsonData);
            Assert.Contains("\"data\":", jsonData);
            Assert.True(command.IsDelete); // Verify IsDelete is set to true
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidId_ShouldUpdateWorkflowActionType()
        {
            // Arrange
            var viewModel = _fixture.Create<WorkflowActionTypeViewModel>();
            var dic = new Dictionary<string, StringValues> { { "id", "123" } };
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = _fixture.Create<UpdateWorkflowActionTypeCommand>();
            var response = new BaseResponse { Success = true, Message = "Updated Successfully" };

            _mockMapper.Setup(m => m.Map<UpdateWorkflowActionTypeCommand>(viewModel))
                .Returns(command);

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.UpdateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonData = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", jsonData);
            Assert.Contains("\"data\":", jsonData);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidationException_ShouldReturnJsonException()
        {
            // Arrange
            var viewModel = _fixture.Create<WorkflowActionTypeViewModel>();
            var dic = new Dictionary<string, StringValues> { { "id", "" } };
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = _fixture.Create<CreateWorkflowActionTypeCommand>();
            var validationResult = new FluentValidation.Results.ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Property", "Validation error occurred"));
            var validationException = new ContinuityPatrol.Shared.Core.Exceptions.ValidationException(validationResult);

            _mockMapper.Setup(m => m.Map<CreateWorkflowActionTypeCommand>(viewModel))
                .Returns(command);

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.CreateAsync(command))
                .ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task CreateOrUpdate_WithGeneralException_ShouldReturnJsonException()
        {
            // Arrange
            var viewModel = _fixture.Create<WorkflowActionTypeViewModel>();
            var dic = new Dictionary<string, StringValues> { { "id", "" } };
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = _fixture.Create<CreateWorkflowActionTypeCommand>();
            var generalException = new Exception("General error occurred");

            _mockMapper.Setup(m => m.Map<CreateWorkflowActionTypeCommand>(viewModel))
                .Returns(command);

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.CreateAsync(command))
                .ThrowsAsync(generalException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task CreateOrUpdate_WithUpdateValidationException_ShouldReturnJsonException()
        {
            // Arrange
            var viewModel = _fixture.Create<WorkflowActionTypeViewModel>();
            var dic = new Dictionary<string, StringValues> { { "id", "123" } };
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = _fixture.Create<UpdateWorkflowActionTypeCommand>();
            var validationResult = new FluentValidation.Results.ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Property", "Update validation error"));
            var validationException = new ContinuityPatrol.Shared.Core.Exceptions.ValidationException(validationResult);

            _mockMapper.Setup(m => m.Map<UpdateWorkflowActionTypeCommand>(viewModel))
                .Returns(command);

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.UpdateAsync(command))
                .ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task CreateOrUpdate_WithUpdateGeneralException_ShouldReturnJsonException()
        {
            // Arrange
            var viewModel = _fixture.Create<WorkflowActionTypeViewModel>();
            var dic = new Dictionary<string, StringValues> { { "id", "123" } };
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = _fixture.Create<UpdateWorkflowActionTypeCommand>();
            var generalException = new Exception("Update general error");

            _mockMapper.Setup(m => m.Map<UpdateWorkflowActionTypeCommand>(viewModel))
                .Returns(command);

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.UpdateAsync(command))
                .ThrowsAsync(generalException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task GetPagination_ShouldReturnJsonResult()
        {
            // Arrange
            var query = _fixture.Create<GetWorkflowActionTypePaginatedListQuery>();
            var expectedData = _fixture.Create<PaginatedResult<WorkflowActionTypeListVm>>();

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.GetWorkflowActionTypePaginatedList(query))
                .ReturnsAsync(expectedData);

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
            Assert.Equal(expectedData, result.Value);
        }

        [Fact]
        public async Task GetPagination_WithException_ShouldReturnJsonException()
        {
            // Arrange
            var query = _fixture.Create<GetWorkflowActionTypePaginatedListQuery>();
            var exception = new Exception("Pagination error occurred");

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.GetWorkflowActionTypePaginatedList(query))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task Delete_ShouldReturnSuccessJsonResult()
        {
            // Arrange
            var id = "test-id-123";
            var expectedData = _fixture.Create<BaseResponse>();

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.DeleteAsync(id))
                .ReturnsAsync(expectedData);

            // Act
            var result = await _controller.Delete(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonData = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", jsonData);
            Assert.Contains("\"data\":", jsonData);
        }

        [Fact]
        public async Task Delete_WithException_ShouldReturnJsonException()
        {
            // Arrange
            var id = "test-id-123";
            var exception = new Exception("Delete error occurred");

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.DeleteAsync(id))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task IsWorkflowActionTypeExist_ShouldReturnTrue()
        {
            // Arrange
            var name = "test-name";
            var id = "test-id";

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.IsWorkflowActionTypeExist(name, id))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.IsWorkflowActionTypeExist(name, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowActionTypeExist_ShouldReturnFalse()
        {
            // Arrange
            var name = "test-name";
            var id = "test-id";

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.IsWorkflowActionTypeExist(name, id))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.IsWorkflowActionTypeExist(name, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowActionTypeExist_WithException_ShouldReturnFalse()
        {
            // Arrange
            var name = "test-name";
            var id = "test-id";
            var exception = new Exception("Check existence error");

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.IsWorkflowActionTypeExist(name, id))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.IsWorkflowActionTypeExist(name, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void Controller_ShouldHaveAreaAttribute()
        {
            // Arrange & Act
            var areaAttribute = typeof(OperationTypeController)
                .GetCustomAttributes(typeof(AreaAttribute), false)
                .FirstOrDefault() as AreaAttribute;

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void CreateOrUpdate_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var method = typeof(OperationTypeController).GetMethod("CreateOrUpdate");

            // Act
            var httpPostAttribute = method.GetCustomAttributes(typeof(HttpPostAttribute), false).Any();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).Any();
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).Any();

            // Assert
            Assert.True(httpPostAttribute);
            Assert.True(validateAntiForgeryTokenAttribute);
            Assert.True(antiXssAttribute);
        }

        [Fact]
        public void GetPagination_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(OperationTypeController).GetMethod("GetPagination");

            // Act
            var httpGetAttribute = method.GetCustomAttributes(typeof(HttpGetAttribute), false).Any();

            // Assert
            Assert.True(httpGetAttribute);
        }

        [Fact]
        public async Task CreateOrUpdate_WithNullId_ShouldCreateNewWorkflowActionType()
        {
            // Arrange
            var viewModel = _fixture.Create<WorkflowActionTypeViewModel>();
            var dic = new Dictionary<string, StringValues> { { "id", StringValues.Empty } };
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = _fixture.Create<CreateWorkflowActionTypeCommand>();
            var response = new BaseResponse { Success = true, Message = "Created Successfully" };

            _mockMapper.Setup(m => m.Map<CreateWorkflowActionTypeCommand>(viewModel))
                .Returns(command);

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.CreateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonData = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", jsonData);
        }

        [Fact]
        public async Task CreateOrUpdate_WithWhitespaceId_ShouldCreateNewWorkflowActionType()
        {
            // Arrange
            var viewModel = _fixture.Create<WorkflowActionTypeViewModel>();
            var dic = new Dictionary<string, StringValues> { { "id", "   " } };
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = _fixture.Create<CreateWorkflowActionTypeCommand>();
            var response = new BaseResponse { Success = true, Message = "Created Successfully" };

            _mockMapper.Setup(m => m.Map<CreateWorkflowActionTypeCommand>(viewModel))
                .Returns(command);

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.CreateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonData = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", jsonData);
        }

        [Fact]
        public void Controller_ShouldInheritFromController()
        {
            // Act
            var controller = new OperationTypeController(
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockPublisher.Object,
                _mockMapper.Object
            );

            // Assert
            Assert.NotNull(controller);
            Assert.IsAssignableFrom<Microsoft.AspNetCore.Mvc.Controller>(controller);
        }

        [Fact]
        public async Task List_ShouldCallPublisher()
        {
            // Arrange
            _mockPublisher
                .Setup(p => p.Publish(It.IsAny<WorkflowActionTypePaginatedEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.List();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<WorkflowActionTypePaginatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldCallDataProvider()
        {
            // Arrange
            var viewModel = _fixture.Create<WorkflowActionTypeViewModel>();
            var dic = new Dictionary<string, StringValues> { { "id", "" } };
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = _fixture.Create<CreateWorkflowActionTypeCommand>();
            var response = new BaseResponse { Success = true, Message = "Created Successfully" };

            _mockMapper.Setup(m => m.Map<CreateWorkflowActionTypeCommand>(viewModel))
                .Returns(command);

            _mockDataProvider.Setup(dp => dp.WorkflowActionTypes.CreateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
            _mockDataProvider.Verify(dp => dp.WorkflowActionTypes.CreateAsync(command), Times.Once);
        }
    }
}
