﻿using AutoMapper;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class NodeConfigurationControllerTests
    {
        private readonly Mock<IPublisher> _publisher=new();
        private readonly Mock<IMapper> _mapper =new ();
        private readonly Mock<IDataProvider> _dataProvider = new();
        private readonly Mock<ILogger<LoadBalancerController>> _logger = new();
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService = new();
        [Fact]
        public void List_ReturnsViewResult()
        {
            var controller = new LoadBalancerController(_publisher.Object,_mapper.Object,_dataProvider.Object,_logger.Object, _mockLoggedInUserService.Object);

            var result = controller.List();

            
            Assert.NotNull(result); 
        }
    }
}

