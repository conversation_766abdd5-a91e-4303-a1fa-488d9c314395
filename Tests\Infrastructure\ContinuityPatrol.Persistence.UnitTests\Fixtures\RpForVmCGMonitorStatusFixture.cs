using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class RpForVmCGMonitorStatusFixture : IDisposable
{
    public List<RpForVmCGMonitorStatus> RpForVmCGMonitorStatusPaginationList { get; set; }
    public List<RpForVmCGMonitorStatus> RpForVmCGMonitorStatusList { get; set; }
    public RpForVmCGMonitorStatus RpForVmCGMonitorStatusDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string InfraObjectId = "INFRA_OBJ_123";
    public const string ConsistencyGroupId = "CG_123";
    public const string ConsistencyGroupName = "Test CG";
    public const string UserId = "USER_123";

    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public RpForVmCGMonitorStatusFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<RpForVmCGMonitorStatus>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.ConsistencyGroupId, () => ConsistencyGroupId)
            .With(x => x.ConsistencyGroupName, () => ConsistencyGroupName)
            .With(x => x.CGProperties, () => _fixture.Create<string>())
            .With(x => x.State, () => "Active")
            .With(x => x.TransferStatus, () => "Completed")
            .With(x => x.ActivityType, () => "Replication")
            .With(x => x.ActivityStatus, () => "Success")
            .With(x => x.LastSnapShotTime, () => DateTime.UtcNow.AddHours(-1).ToString())
            .With(x => x.DataLag, () => "5 minutes")
            .With(x => x.SnapProperties, () => _fixture.Create<string>())
            .With(x => x.AvailabilityStatus, () => "Available")
            .With(x => x.ProtectedSize, () => "100GB")
            .With(x => x.IsAlertSend, () => false)
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
        );

        RpForVmCGMonitorStatusList = _fixture.CreateMany<RpForVmCGMonitorStatus>(5).ToList();
        RpForVmCGMonitorStatusPaginationList = _fixture.CreateMany<RpForVmCGMonitorStatus>(20).ToList();
        RpForVmCGMonitorStatusDto = _fixture.Create<RpForVmCGMonitorStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public RpForVmCGMonitorStatus CreateRpForVmCGMonitorStatusWithInfraObjectId(string infraObjectId)
    {
        return _fixture.Build<RpForVmCGMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectId, infraObjectId)
            .With(x => x.ConsistencyGroupId, ConsistencyGroupId)
            .With(x => x.ConsistencyGroupName, ConsistencyGroupName)
            .With(x => x.CGProperties, _fixture.Create<string>())
            .With(x => x.State, "Active")
            .With(x => x.TransferStatus, "Completed")
            .With(x => x.ActivityType, "Replication")
            .With(x => x.ActivityStatus, "Success")
            .With(x => x.LastSnapShotTime, DateTime.UtcNow.AddHours(-1).ToString())
            .With(x => x.DataLag, "5 minutes")
            .With(x => x.SnapProperties, _fixture.Create<string>())
            .With(x => x.AvailabilityStatus, "Available")
            .With(x => x.ProtectedSize, "100GB")
            .With(x => x.IsAlertSend, false)
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RpForVmCGMonitorStatus CreateRpForVmCGMonitorStatusWithAvailabilityStatus(string infraObjectId, string availabilityStatus)
    {
        return _fixture.Build<RpForVmCGMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectId, infraObjectId)
            .With(x => x.ConsistencyGroupId, ConsistencyGroupId)
            .With(x => x.ConsistencyGroupName, ConsistencyGroupName)
            .With(x => x.CGProperties, _fixture.Create<string>())
            .With(x => x.State, "Active")
            .With(x => x.TransferStatus, "Completed")
            .With(x => x.ActivityType, "Replication")
            .With(x => x.ActivityStatus, "Success")
            .With(x => x.LastSnapShotTime, DateTime.UtcNow.AddHours(-1).ToString())
            .With(x => x.DataLag, "5 minutes")
            .With(x => x.SnapProperties, _fixture.Create<string>())
            .With(x => x.AvailabilityStatus, availabilityStatus)
            .With(x => x.ProtectedSize, "100GB")
            .With(x => x.IsAlertSend, false)
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RpForVmCGMonitorStatus CreateRpForVmCGMonitorStatusWithProperties(
        string infraObjectId = null,
        string consistencyGroupId = null,
        string state = null,
        string transferStatus = null,
        string activityStatus = null,
        string availabilityStatus = null,
        bool? isAlertSend = null,
        string lastSnapShotTime = null)
    {
        return _fixture.Build<RpForVmCGMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.ConsistencyGroupId, consistencyGroupId ?? ConsistencyGroupId)
            .With(x => x.ConsistencyGroupName, ConsistencyGroupName)
            .With(x => x.CGProperties, _fixture.Create<string>())
            .With(x => x.State, state ?? "Active")
            .With(x => x.TransferStatus, transferStatus ?? "Completed")
            .With(x => x.ActivityType, "Replication")
            .With(x => x.ActivityStatus, activityStatus ?? "Success")
            .With(x => x.LastSnapShotTime, lastSnapShotTime ?? DateTime.UtcNow.AddHours(-1).ToString())
            .With(x => x.DataLag, "5 minutes")
            .With(x => x.SnapProperties, _fixture.Create<string>())
            .With(x => x.AvailabilityStatus, availabilityStatus ?? "Available")
            .With(x => x.ProtectedSize, "100GB")
            .With(x => x.IsAlertSend, isAlertSend ?? false)
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public List<RpForVmCGMonitorStatus> CreateMultipleRpForVmCGMonitorStatusWithSameInfraObjectId(
        string infraObjectId, Dictionary<string, int> availabilityStatusCounts)
    {
        var statuses = new List<RpForVmCGMonitorStatus>();
        
        foreach (var statusCount in availabilityStatusCounts)
        {
            for (int i = 0; i < statusCount.Value; i++)
            {
                statuses.Add(CreateRpForVmCGMonitorStatusWithAvailabilityStatus(infraObjectId, statusCount.Key));
            }
        }
        
        return statuses;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonStates = { "Active", "Inactive", "Suspended", "Error" };
        public static readonly string[] CommonTransferStatuses = { "Completed", "In Progress", "Failed", "Pending" };
        public static readonly string[] CommonActivityStatuses = { "Success", "Failed", "Warning", "In Progress" };
        public static readonly string[] CommonAvailabilityStatuses = { "Available", "Unavailable", "Degraded", "Unknown" };
        public static readonly string[] CommonActivityTypes = { "Replication", "Backup", "Restore", "Sync" };
        
        public static readonly Dictionary<string, int> SampleAvailabilityStatusCounts = new()
        {
            { "Available", 5 },
            { "Unavailable", 2 },
            { "Degraded", 3 },
            { "Unknown", 1 }
        };
    }
}
