﻿//var createPermission = $("#configurationCreate").data("create-permission")?.toLowerCase();
//var deletePermission = $("#configurationDelete").data("delete-permission")?.toLowerCase();
//let getserver = '';
//(function () {
//    let sessionData = sessionStorage.getItem('serverDataFromITView');
//    console.log(sessionData, 'sessionData')
//    if (sessionData !== undefined && sessionData !== null && sessionData !== '') {
//        getserver = sessionData
//    }
//})();
//(function () {
//    var selectedValues = [];
//    var dataTable = $('#datatablelist').DataTable(
//        {
//            language: {
//                decimal: ",",
//                paginate: {
//                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
//                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
//                }
//            },
//            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
//            scrollY: true,
//            deferRender: true,
//            scroller: true,
//            "processing": true,
//            "serverSide": false,
//            "filter": true,
//            "Sortable": true,
//            "order": [],
//            fixedColumns: {
//                left: 1,
//                right: 1
//            },
//            "ajax": {
//                "type": "GET",
//                "url": "/Configuration/Server/GetPagination",
//                "dataType": "json",
//                "data": function (d) {
//                    var selectedType = $('#search-in-type').val();
//                    if (selectedType === "All") {
//                        if (getserver?.length > 0) {
//                            //sessionStorage.removeItem('serverDataFromITView', getserver);
//                            //sessionStorage.removeItem('serverDataFromITView');
//                            selectedType = getserver

//                        } else {
//                            selectedType = "";
//                        }
//                    }
//                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
//                    d.pageSize = d.length;
//                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
//                    d.OSTypeId = selectedType;
//                    selectedValues.length = 0;
//                },
//                "dataSrc": function (json) {
//                    if (json?.success) {
//                        json.recordsTotal = json.data.totalPages;
//                        json.recordsFiltered = json.data.totalCount;
//                        if (json?.data?.data?.length === 0) {
//                            $(".pagination-column").addClass("disabled")
//                        }
//                        else {
//                            $(".pagination-column").removeClass("disabled")
//                        }
//                        return json?.data?.data;
//                    }
//                    else {
//                        errorNotification(json)
//                    }
//                }
//            },
//            "columnDefs": [
//                {
//                    "targets": [0, 1, 2],
//                    "className": "truncate"
//                }
//            ],
//            "columns": [
//                {
//                    "data": null,
//                    "name": "Sr. No.",
//                    "autoWidth": true,
//                    "orderable": false,
//                    "render": function (data, type, row, meta) {
//                        if (type === 'display') {
//                            return meta.row + 1;
//                        }
//                        return data;
//                    },
//                    "orderable": false
//                },
//                {
//                    "data": "name", "name": "Server Name", "autoWidth": true,
//                    "render": function (data, type, row) {
//                        if (type === 'display') {
//                            return '<span title="' + data + '">' + data + '</span>';
//                        }
//                        return data;
//                    }
//                },
//                {
//                    "data": "serverType", "name": "Server Type", "autoWidth": true,
//                    "render": function (data, type, row) {
//                        var iconClass = '';
//                        if (row?.serverType?.toLocaleLowerCase()?.includes("dr") && !row?.serverType?.toLocaleLowerCase()?.includes("near")) {
//                            iconClass = "cp-virtual-drsite me-1"
//                        }
//                        else if (row?.serverType?.toLocaleLowerCase()?.includes("pr")) {
//                            iconClass = "cp-virtual-prsite me-1"
//                        }
//                        else {
//                            iconClass = "cp-virtual-neardrsite me-1"
//                        }

//                        if (type === 'display') {
//                            return `<span title="${row?.serverType}"> <i class="${iconClass}"></i> ${row?.serverType} </span>`;
//                        } else {
//                            return data;
//                        }
//                    }
//                },
//                {
//                    "data": "properties", "name": "IP Address", "autoWidth": true,
//                    "render": function (data, type, row) {
//                        var ip = JSON.parse(data)?.IpAddress || JSON.parse(data)?.IPAddress || "NA";
//                        if (type === 'display') {
//                            return '<span title="' + ip + '">' + ip + '</span>';
//                        }
//                        return data;
//                    }
//                },
//                {
//                    "data": "properties", "name": "Host Name", "autoWidth": true,
//                    "render": function (data, type, row) {
//                        var hostName = JSON.parse(data)?.HostName ? JSON.parse(data)?.HostName : "NA"
//                        if (type === 'display') {
//                            return '<span title="' + hostName + '">' + hostName + '</span>';
//                        }
//                        return data;
//                    }
//                },
//                {
//                    "data": "osType", "name": "OS Type", "autoWidth": true,
//                    "render": function (data, type, row) {
//                        var iconClass = '';
//                        if (type == 'display' && forOsTypeName !== "") {
//                            const filter = forOsTypeName.filter(list => list.properties.name === data).map(filteredItem => filteredItem.properties.name);
//                            if (filter) {
//                                if (filter[0]?.toLocaleLowerCase()?.includes("windows")) {
//                                    iconClass = "cp-windows me-1"
//                                }
//                                else if (filter[0]?.toLocaleLowerCase()?.includes("linux")) {
//                                    iconClass = "cp-linux me-1"
//                                }
//                                else {
//                                    iconClass = "cp-os-type me-1"
//                                }
//                            }
//                            return `<span title="${data ? data : "NA"}"> <i class="${iconClass}"></i> ${data ? data : "NA"} </span>`;
//                        } else {
//                            return data
//                        }
//                    }
//                },
//                {
//                    "data": "status",
//                    "name": "status",
//                    "autoWidth": true,
//                    render: function (data, type, row) {
//                        if (type === 'display') {
//                            const iconClass = data ? (data.toLowerCase() === "up" ? "text-success" : data.toLowerCase() === "pending" ? "text-warning" :
//                                "text-danger") : "text-warning";
//                            const tooltipText = data ? (data.toLowerCase() === "up" ? "Up" :
//                                data.toLowerCase() === "pending" ? "Pending" :
//                                    "Down") : "Pending";
//                            return `
//                            <span title="${tooltipText}">
//                            <i class="text-primary ${data ? (data.toLowerCase() === "pending" ? `cp-pending` : ` cp-${data.toLowerCase()}-linearrow`) : 'cp-pending'}  me-1 ${iconClass}"></i>${tooltipText}</span>`;
//                        }
//                        return data;
//                    },
//                },
//                {
//                    "orderable": false,
//                    "render": function (data, type, row) {
//                        const encryptedRow = encrypt(row);
//                        if (createPermission === 'true' && deletePermission === "true") {
//                            return `
//                        <div class="d-flex align-items-center gap-2">
//                                   <span role="button" >
//                                     <i class="cp-test-connection" id='TestConnection' title="Test Connection" data-server-id="${row.id}"></i>
//                                   </span>
//                                            <span role="button" title="Edit"  class="edit-button" data-server='${encryptedRow}'>
//                                                <i class="cp-edit"></i>
//                                            </span>
//                                            <span role="button" title="Delete"  class="delete-button" data-server-id="${row.id}" data-server-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
//                                                <i class="cp-Delete"></i>
//                                            </span>
                                  
//                        </div>`;
//                        }
//                        else if (createPermission === 'true' && deletePermission === "false") {
//                            return `
//                        <div class="d-flex align-items-center gap-2">
//                          <span role="button" >
//                        <i class="cp-test-connection" id='TestConnection' title="Test Connection" data-server-id="${row.id}"></i>
//                                   </span>
//                                            <span role="button" title="Edit"  class="edit-button" data-server='${encryptedRow}'>
//                                                <i class="cp-edit"></i>
//                                            </span>
//                                            <span role="button" title="Delete"  class="icon-disabled">
//                                                <i class="cp-Delete"></i>
//                                            </span>
                                  
//                        </div>`;
//                        }
//                        else if (createPermission === 'false' && deletePermission === "true") {
//                            return `
//                        <div class="d-flex align-items-center gap-2">
//                          <span role="button" >
//                        <i class="cp-test-connection" id='TestConnection' title="Test Connection" data-server-id="${row.id}"></i>
//                                   </span>
//                                            <span role="button" title="Edit"  class="icon-disabled">
//                                                <i class="cp-edit"></i>
//                                            </span>
//                                            <span role="button" title="Delete"  class="delete-button" data-server-id="${row.id}" data-server-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
//                                                <i class="cp-Delete"></i>
//                                            </span>
                                  
//                        </div>`;
//                        }
//                        else {
//                            return `
//                            <div class="d-flex align-items-center gap-2">
//                              <span role="button" >
//                        <i class="cp-test-connection" id='TestConnection' title="Test Connection" data-server-id="${row.id}"></i>
//                                   </span>
//                                <span role="button" title="Edit" class="icon-disabled">
//                                    <i class="cp-edit"></i>
//                                </span>
//                                <span role="button" title="Delete" class="icon-disabled">
//                                    <i class="cp-Delete"></i>
//                                </span>

//                            </div>`;
//                        }
//                    },
//                    "orderable": false
//                }
//            ],
//            "rowCallback": function (row, data, index) {
//                var api = this.api();
//                var startIndex = api.context[0]._iDisplayStart; // Get the start index of displayed data
//                var counter = startIndex + index + 1; // Calculate the serial number based on start index and index
//                $('td:eq(0)', row).html(counter); // Update the cell in the first column with the serial number
//            },
//            initComplete: function () {
//                $('.paginate_button.page-item.previous').attr('title', 'Previous');
//                $('.paginate_button.page-item.next').attr('title', 'Next');
//                /* $('th.sorting').off();*/
//                /* $("th.sorting").css('cursor', 'default');*/
//                //$("th.sorting_asc").css('cursor', 'default');
//                //$("th.sorting_desc").css('cursor', 'default');
//            },
//            "drawCallback": function (settings) {

//            }
//        }
//    );


//    const clearSessionData = () => {
//        setTimeout(() => {
//            getserver = '';
//            sessionStorage.removeItem('serverDataFromITView', getserver);
//            sessionStorage.removeItem('serverDataFromITView');
//        })
//    }
//    clearSessionData()
//    let getreplication = '';
//    (function () {
//        let sessionData = sessionStorage.getItem('replicationFromITView')
//        if (sessionData !== undefined && sessionData !== null && sessionData !== '') {
//            getreplication = sessionData
//        }
//    })();

//    if (!$.fn.dataTable.isDataTable('#datatablelist')) {
//        $("#createBtn").toggleClass('btn-disabled', createPermission === 'false').css('pointer-events', createPermission === 'false' ? 'none' : '');
//        var selectedValues = [];
//        var dataTable = $('#datatablelist').DataTable(
//            {
//                language: {
//                    paginate: {
//                        next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
//                        previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
//                    }
//                },
//                dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
//                scrollY: true,
//                deferRender: true,
//                scroller: true,
//                "processing": true,
//                "serverSide": false,
//                "filter": true,
//                "order": [],
//                "ajax": {

//                    "type": "GET",
//                    "url": "/Configuration/Replication/GetPagination",
//                    "dataType": "json",

//                    "data": function (d) {

//                        var selectedType = $('#search-in-type').val();
//                        if (selectedType === "all") {
//                            if (getreplication.length > 0) {
//                                selectedType = getreplication
//                            } else {
//                                selectedType = "";
//                            }
//                        }

//                        d.PageNumber = Math.ceil(d.start / d.length) + 1;
//                        d.pageSize = d.length;
//                        d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
//                        d.TypeId = selectedType;
//                        selectedValues.length = 0;
//                    },
//                    "dataSrc": function (json) {
//                        json.recordsTotal = json.totalPages;
//                        json.recordsFiltered = json.totalCount;
//                        if (json.data.length === 0) {
//                            $(".pagination-column").addClass("disabled")
//                        }
//                        else {
//                            $(".pagination-column").removeClass("disabled")
//                        }
//                        return json.data;
//                    }
//                },

//                "columns": [
//                    {
//                        "data": null,
//                        "name": "Sr. No.",
//                        "autoWidth": true,
//                        "orderable": false,
//                        "render": function (data, type, row, meta) {
//                            if (type === 'display') {
//                                return meta.row + 1;
//                            }
//                            return data;
//                        },
//                    },
//                    {
//                        "data": "name", "name": "Name", "autoWidth": true,
//                        "render": function (data, type, row) {
//                            if (type === 'display') {
//                                return '<span title="' + data + '">' + data + '</span>';
//                            }
//                            return data;
//                        }
//                    },
//                    {
//                        "data": "type", "type": "Type", "autoWidth": true,
//                        "render": function (data, type, row) {
//                            if (type === 'display') {
//                                var iconList = JSON.parse(row.properties);
//                                return '<span title="' + data + '">' + '<i class="' + (iconList.icon === "null" || iconList.icon === "undefined" || iconList.icon === null || iconList.icon === undefined || iconList.icon == "d-flex" ? "cp-images" : iconList.icon) + '"></i>' + ' ' + data + '</span>';
//                            }
//                            return data;
//                        }
//                    },
//                    {
//                        "data": "siteName", "name": "Site", "autoWidth": true,
//                        "render": function (data, type, row) {
//                            if (type === 'display') {
//                                return '<span title="' + data + '">' + data + '</span>';
//                            }
//                            return data;
//                        }
//                    },
//                    {
//                        "orderable": false,
//                        "width": '100px',
//                        "render": function (data, type, row) {
//                            if (createPermission === 'true' && deletePermission === "true") {
//                                return `
//                        <div class="d-flex align-items-center gap-2">
//                                            <span role="button" title="Edit"  class=" edit-button" data-replication='${JSON.stringify(row)}'>
//                                                <i class="cp-edit"></i>
//                                            </span>
//                                            <span role="button" title="Delete"  class="delete-button" data-replication-id="${row.id}" data-replication-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
//                                                <i class="cp-Delete"></i>
//                                            </span>
                                  
//                        </div>`;
//                            }
//                            else if (createPermission === 'true' && deletePermission === "false") {
//                                return `
//                        <div class="d-flex align-items-center gap-2">
//                                            <span role="button" title="Edit"  class=" edit-button" data-replication='${JSON.stringify(row)}'>
//                                                <i class="cp-edit"></i>
//                                            </span>
//                                            <span role="button" title="Delete"  class="icon-disabled">
//                                                <i class="cp-Delete"></i>
//                                            </span>
                                  
//                        </div>`;
//                            }
//                            else if (createPermission === 'false' && deletePermission === "true") {
//                                return `
//                        <div class="d-flex align-items-center gap-2">
//                                            <span role="button" title="Edit"  class="icon-disabled">
//                                                <i class="cp-edit"></i>
//                                            </span>
//                                            <span role="button" title="Delete"  class="delete-button" data-replication-id="${row.id}" data-replication-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
//                                                <i class="cp-Delete"></i>
//                                            </span>
                                  
//                        </div>`;
//                            }
//                            else {
//                                return `
//                            <div class="d-flex align-items-center gap-2">
//                                <span role="button" title="Edit" class="icon-disabled">
//                                    <i class="cp-edit"></i>
//                                </span>
//                                <span role="button" title="Delete" class="icon-disabled">
//                                    <i class="cp-Delete"></i>
//                                </span>

//                            </div>`;
//                            }

//                        },
//                        "orderable": false

//                    },
//                ],
//                "rowCallback": function (row, data, index) {
//                    var api = this.api();
//                    var startIndex = api.context[0]._iDisplayStart; // Get the start index of displayed data
//                    var counter = startIndex + index + 1; // Calculate the serial number based on start index and index
//                    $('td:eq(0)', row).html(counter); // Update the cell in the first column with the serial number
//                },
//                initComplete: function () {
//                    $('.paginate_button.page-item.previous').attr('title', 'Previous');
//                    $('.paginate_button.page-item.next').attr('title', 'Next');
//                }
//            });
//        dataTable.on('draw.dt', function () {
//            $('.paginate_button.page-item.previous').attr('title', 'Previous');
//            $('.paginate_button.page-item.next').attr('title', 'Next');
//        });
//        $('#search-in-type').on('change', function () {
//            dataTable.ajax.reload();
//        });

//        $('#search-inp').on('keydown input', function (e) {
//            if (e.key === '=' || e.key === 'Enter') {
//                e.preventDefault();
//                return false;
//            } else {
//                const nameCheckbox = $("#Name");
//                const siteCheckbox = $("#Site");
//                const inputValue = $('#search-inp').val();
//                if (nameCheckbox.is(':checked')) {
//                    selectedValues.push(nameCheckbox.val() + inputValue);
//                }
//                if (siteCheckbox.is(':checked')) {
//                    selectedValues.push(siteCheckbox.val() + inputValue);
//                }
//                dataTable.ajax.reload(function (json) {
//                    if (json.recordsFiltered === 0) {
//                        $('.dataTables_empty').text('No matching records Found');
//                    }
//                })
//            }
//        })
//    }
//    const clearSessionReplica = () => {
//        setTimeout(() => {
//            getreplication = '';
//            sessionStorage.removeItem('replicationFromITView', getreplication);
//            sessionStorage.removeItem('replicationFromITView');
//        })
//    }
//    clearSessionReplica();
//    let getdatabase = '';

//    (function () {
//        let sessionData = sessionStorage.getItem('databaseFromITView')
//        if (sessionData !== undefined && sessionData !== null && sessionData !== '') {

//            getdatabase = sessionData
//        }
//    })();
//    if (!$.fn.dataTable.isDataTable('#databaseList')) {

//        var selectedValues = [];
//        var dataTable = $('#databaseList').DataTable(

//            {
//                language: {
//                    decimal: ",",
//                    paginate: {
//                        next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next" ></i>',
//                        previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous" ></i>'
//                    }
//                },
//                dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
//                scrollY: true,
//                deferRender: true,
//                scroller: true,
//                "processing": true,
//                "serverSide": false,
//                "filter": true,
//                "Sortable": true,
//                "order": [],
//                fixedColumns: {
//                    left: 1,
//                    right: 1
//                },
//                "ajax": {
//                    "type": "GET",
//                    "url": "/Configuration/Database/GetPagination",
//                    "dataType": "json",
//                    "data": function (d) {
//                        var selectedType = $('#selectType').val();
//                        if (selectedType === "all") {

//                            if (getdatabase?.length > 0) {
//                                selectedType = getdatabase
//                            } else {
//                                selectedType = "";
//                            }
//                        }
//                        else {

//                            if (getdatabase?.length > 0) {
//                                selectedType = getdatabase
//                            }

//                        }
//                        d.PageNumber = Math.ceil(d.start / d.length) + 1;
//                        d.pageSize = d.length;
//                        d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
//                        d.DatabaseTypeId = selectedType;
//                        selectedValues.length = 0;
//                    },
//                    "dataSrc": function (json) {
//                        if (json.success) {
//                            if (json?.data?.data?.length === 0) {
//                                $(".pagination-column").addClass("disabled")
//                            }
//                            else {
//                                $(".pagination-column").removeClass("disabled")
//                            }
//                            json.recordsTotal = json.data.totalPages;
//                            json.recordsFiltered = json.data.totalCount;
//                            return json?.data?.data;
//                        }
//                        else {
//                            errorNotification(json)
//                        }
//                    },
//                },
//                "columns": [
//                    {
//                        "data": null,
//                        "name": "Sr. No.",
//                        "autoWidth": true,
//                        "orderable": false,
//                        "render": function (data, type, row, meta) {
//                            if (type === 'display') {
//                                return meta.row + 1;
//                            }
//                            return data;
//                        }
//                    },
//                    {
//                        "data": "name", "name": "name", "autoWidth": true,
//                        "render": function (data, type, row) {
//                            if (type === 'display') {
//                                return '<span title="' + data + '">' + data + '</span>';
//                            }
//                            return data;
//                        }
//                    },
//                    {
//                        "data": "databaseType", "name": "name", "autoWidth": true,
//                        "render": function (data, type, row) {
//                            if (type === 'display') {
//                                var iconList = JSON.parse(row.properties);
//                                if ($('#selectType option:selected').text()?.toLowerCase()?.includes("oracle")) {
//                                    $("#dynamicHeader").empty();
//                                    $("#dynamicHeader").html("Oracle SID");
//                                    if (iconList?.OracleSID) {
//                                        return '<span title="' + iconList?.OracleSID + '">' + iconList?.OracleSID + '</span>';
//                                    } else {
//                                        return '<span title="' + "-" + '">' + "-" + '</span>';
//                                    }
//                                } else if ($('#selectType option:selected').text().toLowerCase() === "mssql") {
//                                    $("#dynamicHeader").empty();
//                                    $("#dynamicHeader").html("Database SID");
//                                    if (iconList?.DatabaseSID) {
//                                        return '<span title="' + iconList?.DatabaseSID + '">' + iconList?.DatabaseSID + '</span>';
//                                    } else {
//                                        return '<span title="' + "-" + '">' + "-" + '</span>';
//                                    }
//                                } else {
//                                    $("#dynamicHeader").empty();
//                                    $("#dynamicHeader").html("Database Name");
//                                    if (iconList?.DatabaseName) {
//                                        return '<span title="' + iconList?.DatabaseName + '">' + iconList?.DatabaseName + '</span>';
//                                    } else if (iconList?.DatabaseSID) {
//                                        return '<span title="' + iconList?.DatabaseSID + '">' + iconList?.DatabaseSID + '</span>';
//                                    } else if (iconList?.Database) {
//                                        return '<span title="' + iconList?.Database + '">' + iconList?.Database + '</span>';
//                                    } else {
//                                        return '<span title="' + "-" + '">' + "-" + '</span>';
//                                    }
//                                }
//                            } else {
//                                return data;
//                            }
//                        }
//                    },
//                    {
//                        "data": "type", "name": "type", "autoWidth": true,
//                        "render": function (data, type, row) {
//                            if (type === 'display') {
//                                return '<span title="' + data + '">' + data + '</span>';
//                            }
//                            return data;
//                        }
//                    },
//                    {
//                        "data": "properties", "name": "version", "autowidth": true,
//                        "render": function (data, type, row) {
//                            let versionarr = data;
//                            if (versionarr) {
//                                let cleanedVersion = JSON.parse(versionarr)
//                                let versiondata = cleanedVersion.version;
//                                if (versiondata) {
//                                    versiondata = versiondata;
//                                }
//                                else {
//                                    versiondata = "-";
//                                }
//                                if (type === 'display') {
//                                    return `<span title="${versiondata}">${versiondata}</span>`;
//                                }
//                                return versionarr;
//                            }
//                        }
//                    },
//                    {
//                        "data": "properties", "name": "port", "autowidth": true,
//                        "render": function (data, type, row) {
//                            let portData = data;
//                            if (portData) {
//                                let cleanedVersion = JSON.parse(portData)
//                                let port = cleanedVersion.Port;
//                                if (type === 'display') {
//                                    if ($('#selectType option:selected').text().toLowerCase() === "mssql") {
//                                        $("#dynamicHeaderTwo").empty();
//                                        $("#dynamicHeaderTwo").html("Authentication Mode");
//                                        if (cleanedVersion?.AuthenticationMode) {
//                                            return '<span title="' + cleanedVersion?.AuthenticationMode + '">' + cleanedVersion?.AuthenticationMode + '</span>';
//                                        } else {
//                                            return '<span title="' + "-" + '">' + "-" + '</span>';
//                                        }
//                                    } else {
//                                        $("#dynamicHeaderTwo").empty();
//                                        $("#dynamicHeaderTwo").html("Port Number");
//                                        return `<span title="${port ? port : '-'}">${port ? port : '-'}</span>`;
//                                    }
//                                }
//                                return portData;
//                            }
//                        }
//                    },
//                    {
//                        "data": "modeType",
//                        "name": "modeType",
//                        "autoWidth": true,
//                        render: function (data, type, row) {
//                            if (type === 'display') {
//                                const iconClass = data ? (data.toLowerCase() === "up" ? "text-success" : data.toLowerCase() === "pending" ? "text-warning" :
//                                    "text-danger") : "text-warning";
//                                const tooltipText = data ? (data.toLowerCase() === "up" ? "Up" :
//                                    data.toLowerCase() === "pending" ? "Pending" :
//                                        "Down") : "Pending";
//                                return `
//                            <span title="${tooltipText}">
//                            <i class="text-primary ${data ? (data.toLowerCase() === "pending" ? `cp-pending` : ` cp-${data.toLowerCase()}-linearrow`) : 'cp-pending'}  me-1  ${iconClass}"></i>${tooltipText}</span>`;
//                            }
//                            return data;
//                        },
//                    },
//                    {
//                        "orderable": false,
//                        "render": function (data, type, row) {
//                            const encryptedRow = encrypt(row);
//                            if (createPermission === 'true' && deletePermission === "true") {
//                                return `
//                                <div class="d-flex align-items-center gap-2">
//                                    <span role="button" title="Test connection" class="">
//                                        <i class="cp-test-connection" data-dbid="${row.id}"></i>
//                                    </span>
//                                    <span role="button" title="Edit" class="database-edit-button" data-database='${encryptedRow}'>
//                                        <i class="cp-edit "></i>
//                                    </span>
//                                    <span role="button" title="Delete" class="database-delete-button" data-bs-toggle="modal" 
//                                        data-dbname="${row.name}" data-dbid="${row.id}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
//                                        <i class="cp-Delete "></i>
//                                    </span>  
//                                </div>`;
//                            }
//                            else if (createPermission === 'true' && deletePermission === "false") {
//                                return `
//                                <div class="d-flex align-items-center  gap-2">
//                                    <span role="button" title="Edit" class="database-edit-button" aria-disabled="true" data-database='${encryptedRow}'>
//                                    <i class="cp-edit"></i>
//                                    </span> 
//                                    <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
//                                    </span>   
//                                    <span role="button" title="Test connection" class="icon-disabled">
//                                        <i class="cp-test-connection" data-dbid="${row.id}"></i>
//                                    </span>
//                                </div>`;
//                            }
//                            else if (createPermission === 'false' && deletePermission === "true") {
//                                return `
//                                <div class="d-flex align-items-center  gap-2">
//                                    <span role="button" title="Edit" class="icon-disabled">
//                                        <i class="cp-edit"></i>
//                                    </span>  
//                                    <span role="button" title="Delete" class="database-delete-button" data-dbid="${row.id}" data-dbname="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
//                                    </span>   
//                                    <span role="button" title="Test connection" class="icon-disabled">
//                                        <i class="cp-test-connection" data-dbid="${row.id}"></i>
//                                    </span>  
//                                 </div>`;
//                            }
//                            else {
//                                return `
//                                <div class="d-flex align-items-center  gap-2">
//                                    <span role="button" title="Edit" class="icon-disabled">
//                                       <i class="cp-edit"></i>
//                                    </span>  
//                                    <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
//                                    </span>  
//                                </div>`;
//                            }
//                        }
//                    }
//                ],
//                "rowCallback": function (row, data, index) {
//                    var api = this.api();
//                    var startIndex = api.context[0]._iDisplayStart; // Get the start index of displayed data
//                    var counter = startIndex + index + 1; // Calculate the serial number based on start index and index
//                    $('td:eq(0)', row).html(counter); // Update the cell in the first column with the serial number
//                },
//                initComplete: function () {
//                    $('.paginate_button.page-item.previous').attr('title', 'Previous');
//                    $('.paginate_button.page-item.next').attr('title', 'Next');
//                },
//                "drawCallback": function (settings) {
//                }
//            });

//        dataTable.on('draw.dt', function () {
//            $('.paginate_button.page-item.previous').attr('title', 'Previous');
//            $('.paginate_button.page-item.next').attr('title', 'Next');
//        });

//        $('#search-inp').on('keydown input', function (e) {
//            if (e.key === '=' || e.key === 'Enter') {
//                e.preventDefault();
//                return false;
//            } else {
//                const nameCheckbox = $("#Name");
//                const companyNameCheckbox = $("#CompanyName");
//                const inputValue = $('#search-inp').val();
//                if (nameCheckbox.is(':checked')) {
//                    selectedValues.push(nameCheckbox.val() + inputValue);
//                }
//                if (companyNameCheckbox.is(':checked')) {
//                    selectedValues.push(companyNameCheckbox.val() + inputValue);
//                }
//                dataTable.ajax.reload(function (json) {
//                    if (json.recordsFiltered === 0) {
//                        $('.dataTables_empty').text('No matching records Found');
//                    }
//                })
//            }
//        })

//        $('#selectType').on('change', function () {
//            dataTable.ajax.reload();
//        });


//        $('.form-select-sm').select2({
//            "language": {
//                "noResults": function () {
//                    return "No Results Found";
//                }
//            },
//        });
//        $(document).on('click', '.cp-test-connection', function () {
//            let dataBaseId = $(this).data('dbid');
//            $.ajax({
//                type: "POST",
//                url: RootUrl + 'Configuration/Database/DatabaseTestConnection',
//                data: { databaseId: dataBaseId },
//                datatype: "json",
//                traditional: true,
//                success: function (result) {
//                    if (result.success) {
//                        dataTable.ajax.reload();
//                        notificationAlert("success", result?.data?.message)
//                    }
//                    else {
//                        errorNotification(result)
//                    }
//                }
//            })
//        });
//    }
//    const clearSession = () => {
//        setTimeout(() => {
//            getdatabase = '';
//            sessionStorage.removeItem('databaseFromITView', getdatabase);
//            sessionStorage.removeItem('databaseFromITView');
//        })
//    }
//    clearSession();
//})