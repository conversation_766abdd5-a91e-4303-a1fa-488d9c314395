using ContinuityPatrol.Application.Features.BackUpLog.Commands.Create;
using ContinuityPatrol.Application.Features.BackUpLog.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUpLog.Commands;

public class CreateBackUpLogTests : IClassFixture<BackUpLogFixture>
{
    private readonly BackUpLogFixture _backUpLogFixture;
    private readonly Mock<IBackUpLogRepository> _mockBackUpLogRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly CreateBackUpLogCommandHandler _handler;

    public CreateBackUpLogTests(BackUpLogFixture backUpLogFixture)
    {
        _backUpLogFixture = backUpLogFixture;
        _mockBackUpLogRepository = BackUpLogRepositoryMocks.CreateBackUpLogRepository(_backUpLogFixture.BackUpLogs);
        _mockPublisher = new Mock<IPublisher>();

        _mockPublisher = new Mock<IPublisher>();
        _handler = new CreateBackUpLogCommandHandler(
            _backUpLogFixture.Mapper,
            _mockBackUpLogRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_CreateBackUpLog_When_ValidCommand()
    {
        // Arrange
        var command = new CreateBackUpLogCommand
        {
            HostName = "TestServer01",
            DatabaseName = "TestDatabase",
            UserName = "TestUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\TestDatabase.bak",
            Type = "Full",
            Status = "Completed"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
      //  result.Message.ShouldBe("BackUpLog created successfully");
        result.Id.ShouldNotBeNullOrEmpty();

        _mockBackUpLogRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.BackUpLog>()), Times.Once);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<BackUpLogCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateBackUpLog_When_FullBackupType()
    {
        // Arrange
        var command = new CreateBackUpLogCommand
        {
            HostName = "ProductionServer",
            DatabaseName = "ProductionDB",
            UserName = "BackupUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\ProductionDB_Full.bak",
            Type = "Full",
            Status = "Completed"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
       
        result.Id.ShouldNotBeNullOrEmpty();

        _mockBackUpLogRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.BackUpLog>(b => 
            b.Type == "Full" && 
            b.DatabaseName == "ProductionDB" &&
            b.HostName == "ProductionServer")), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateBackUpLog_When_DifferentialBackupType()
    {
        // Arrange
        var command = new CreateBackUpLogCommand
        {
            HostName = "TestServer02",
            DatabaseName = "TestDB2",
            UserName = "DiffUser",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = @"D:\Backups\TestDB2_Diff.bak",
            Type = "Differential",
            Status = "In Progress"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();

        _mockBackUpLogRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.BackUpLog>(b => 
            b.Type == "Differential" && 
            b.IsBackUpServer == true &&
            b.IsLocalServer == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateBackUpLog_When_TransactionLogBackupType()
    {
        // Arrange
        var command = new CreateBackUpLogCommand
        {
            HostName = "LogServer",
            DatabaseName = "TransactionDB",
            UserName = "LogUser",
            IsLocalServer = true,
            IsBackUpServer = true,
            BackUpPath = @"E:\Logs\TransactionDB_Log.trn",
            Type = "Transaction Log",
            Status = "Completed"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();

        _mockBackUpLogRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.BackUpLog>(b => 
            b.Type == "Transaction Log" && 
            b.BackUpPath.EndsWith(".trn"))), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateBackUpLog_When_FailedStatus()
    {
        // Arrange
        var command = new CreateBackUpLogCommand
        {
            HostName = "FailedServer",
            DatabaseName = "FailedDB",
            UserName = "FailUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\FailedDB.bak",
            Type = "Full",
            Status = "Failed"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();

        _mockBackUpLogRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.BackUpLog>(b => 
            b.Status == "Failed" && 
            b.DatabaseName == "FailedDB")), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateBackUpLog_When_RemoteBackupServer()
    {
        // Arrange
        var command = new CreateBackUpLogCommand
        {
            HostName = "RemoteServer",
            DatabaseName = "RemoteDB",
            UserName = "RemoteUser",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = @"\\RemoteServer\Backups\RemoteDB.bak",
            Type = "Full",
            Status = "Completed"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();

        _mockBackUpLogRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.BackUpLog>(b => 
            b.IsLocalServer == false && 
            b.IsBackUpServer == true &&
            b.BackUpPath.StartsWith(@"\\"))), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateBackUpLog_When_InProgressStatus()
    {
        // Arrange
        var command = new CreateBackUpLogCommand
        {
            HostName = "ProgressServer",
            DatabaseName = "ProgressDB",
            UserName = "ProgressUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\ProgressDB.bak",
            Type = "Differential",
            Status = "In Progress"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();

        _mockBackUpLogRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.BackUpLog>(b => 
            b.Status == "In Progress" && 
            b.Type == "Differential")), Times.Once);
    }

    [Fact]
    public async Task Handle_PublishEvent_When_BackUpLogCreated()
    {
        // Arrange
        var command = _backUpLogFixture.CreateBackUpLogCommand;

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockPublisher.Verify(x => x.Publish(
            It.Is<BackUpLogCreatedEvent>(e => e.Name == command.DatabaseName), 
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_MapCorrectly_When_ValidCommand()
    {
        // Arrange
        var command = new CreateBackUpLogCommand
        {
            HostName = "MappingTestServer",
            DatabaseName = "MappingTestDB",
            UserName = "MappingUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\MappingTestDB.bak",
            Type = "Full",
            Status = "Completed"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBackUpLogRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.BackUpLog>(b =>
            b.HostName == command.HostName &&
            b.DatabaseName == command.DatabaseName &&
            b.UserName == command.UserName &&
            b.IsLocalServer == command.IsLocalServer &&
            b.IsBackUpServer == command.IsBackUpServer &&
            b.BackUpPath == command.BackUpPath &&
            b.Type == command.Type &&
            b.Status == command.Status)), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponse_When_BackUpLogCreated()
    {
        // Arrange
        var command = new CreateBackUpLogCommand
        {
            HostName = "ResponseTestServer",
            DatabaseName = "ResponseTestDB",
            UserName = "ResponseUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\ResponseTestDB.bak",
            Type = "Full",
            Status = "Completed"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<CreateBackUpLogResponse>();
        result.Id.ShouldNotBeNullOrEmpty();
        Guid.TryParse(result.Id, out _).ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_CreateBackUpLogWithProperties_When_PropertiesProvided()
    {
        // Arrange
        var command = new CreateBackUpLogCommand
        {
            HostName = "PropertiesServer",
            DatabaseName = "PropertiesDB",
            UserName = "PropertiesUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\PropertiesDB.bak",
            Type = "Full",
            Status = "Completed"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();

        _mockBackUpLogRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.BackUpLog>(b => 
            b.DatabaseName == "PropertiesDB")), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateBackUpLogWithLongPath_When_LongPathProvided()
    {
        // Arrange
        var longPath = @"C:\Very\Long\Path\To\Backup\Directory\With\Many\Subdirectories\TestDatabase_Full_Backup.bak";
        var command = new CreateBackUpLogCommand
        {
            HostName = "LongPathServer",
            DatabaseName = "LongPathDB",
            UserName = "LongPathUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = longPath,
            Type = "Full",
            Status = "Completed"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();

        _mockBackUpLogRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.BackUpLog>(b => 
            b.BackUpPath == longPath)), Times.Once);
    }
}
