﻿using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetDetailByInfraObjectandEntityId;

public class GetHeatMapStatusByInfraObjectandEntityIdQueryHandler : IRequestHandler<
    GetHeatMapStatusByInfraObjectandEntityIdQuery, HeatMapStatusByInfraObjectandEntityIdVm>
{
    private readonly IHeatMapStatusViewRepository _heatMapStatusViewRepository;
    private readonly IMapper _mapper;

    public GetHeatMapStatusByInfraObjectandEntityIdQueryHandler(IHeatMapStatusViewRepository heatMapStatusViewRepository,
        IMapper mapper)
    {
        _heatMapStatusViewRepository = heatMapStatusViewRepository;
        _mapper = mapper;
    }

    public async Task<HeatMapStatusByInfraObjectandEntityIdVm> Handle(
        GetHeatMapStatusByInfraObjectandEntityIdQuery request, CancellationToken cancellationToken)
    {
        var heatMapStatus =
            await _heatMapStatusViewRepository.GetHeatMapDetailByInfraObjectAndEntityId(request.InfraObjectId,
                request.EntityId);

        Guard.Against.NullOrDeactive(heatMapStatus, nameof(HeatMapStatusView),
            new NotFoundException(nameof(HeatMapStatusView), request.EntityId));

        var heatMapStatusDetailDto = _mapper.Map<HeatMapStatusByInfraObjectandEntityIdVm>(heatMapStatus);

        return heatMapStatusDetailDto ??
               throw new NotFoundException(nameof(HeatMapStatusView), request.EntityId);
    }
}