﻿

//$(document).on('click', '.workflowClick', WFExeEventDebounce(async function (e) {
//    if (!$(this).hasClass("Active-Card")) {
//        $('#timeline_view, #timeline_WorkflowName').empty();
//        $('#parentWETimeLoader').removeClass('d-none')

//        $("#wrapper2").show()
//        $(".autoscroll").hide()
//        $("#wrapper2").css('text-align', 'center').html(image);
//        $('.Profile-Select li').removeClass("Active-Card");

//        logGroupId = '';
//        profileId = $(this).attr('profileid')
//        workflowId = $(this).attr('id')
//        workflowName = $(this).attr('workflowName')
//        workflowProfileName = $(this).attr("profilename") + " " + $(this).attr("infraname") + " " + $(this).attr('workflowName')

//        if (workflowId) {
//            $(`#${workflowId}`).addClass("Active-Card");
//        }

//        // let clickStatus = $(this).find('.btnCustomWorkflow').hasClass("active");
//        // let customSelectAll = clickStatus ? '<div class="form-check allSelect" > <input class="form-check-input timelineCheckboxAll" type="checkbox" value="" id="flexCheckDefault" checked="true"><label class="form-check-label" for="flexCheckDefault">Select All</label></div>' : ''

//        $('.workflowClick').each(function () {
//            if (!$(this).hasClass('Active-Card')) {

//                let pId = $(this).attr('profileid');
//                $(this).find('.btnCustomWorkflow').removeClass("active");
//                $(this).find(".checkStatus").prop('checked', false)
//                $(`#flush-${pId} .btnStart`).attr('disabled', false)

//            }
//        });

//        let data = {
//            id: workflowId,
//            workflowName: workflowName
//        }

//        //let workflowData = {
//        //    workflowId: $(this).attr("id"),

//        //}

//        //let getWorkflowDetail = await GetAsync(RootUrl + 'ITAutomation/WorkflowExecution/GetWorkflowById', workflowData, "", OnError)

//        //if (getWorkflowDetail?.success == false) {
//        //   // notificationAlert('warning', getWorkflowDetail?.message?.value?.message)
//        //    //$('#alertClass').removeClass("success-toast").addClass("warning-toast")
//        //    //$(".iconClass").removeClass("cp-check").addClass("cp-exclamation")
//        //    //$('#message').text()
//        //    //$('#mytoastrdata').toast({ delay: 3000 });
//        //    //$('#mytoastrdata').toast('show');
//        //    return false;
//        //}

//        //workflowDataDetails = JSON.parse(getWorkflowDetail?.properties)

//        //if (!clickStatus && checkService?.success) {
//        //    $(this).find(".checkStatus").prop('checked', false)
//        //    $(`#flush-${profileId} .btnStart`).attr('disabled', false)
//        //} else {
//        //    $(`#flush-${profileId} .btnStart`).attr('disabled', true)
//        //}

//        await defaultTimeLineView(data, false)
//    }
//}, 800));


const defaultTimeLineView = async (data, customStatus) => {
    $('#timeline_view').empty();
    $('#parentWETimeLoader').removeClass('d-none');
    let isWorkflowCustom = $(`#${data?.id}`).attr('isCustom')
    isWorkflowCustom = isWorkflowCustom === 'true' ? true : false;
    // let getIsCustom = liElement?.attr('custom')

    if (isWorkflowCustom) {
        $('#createCustomExecution').text('Update')
        $('#discardTimeline').removeClass('d-none')
    } else {
        $('#createCustomExecution').text('Save')
        $('#discardTimeline').addClass('d-none')
    }

    await $.ajax({
        type: "GET",
        url: RootUrl + 'ITAutomation/WorkflowExecution/TimeLineView',
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result && result?.length) {
                if (customStatus && isWorkflowCustom) {
                    $('#timeline_view').prepend(`<div class="form-check allSelect"><input class="form-check-input timelineCheckboxAll" type="checkbox" id="flexCheckDefault" ${!isWorkflowCustom ? 'checked' : ''}><label class="form-check-label" for="flexCheckDefault">Select All</label></div>`)
                }

                //   $('#timeline_view').empty().append(customSelectAll)
                let timeLineLength = result.length
                for (let i = 0; i < timeLineLength; i++) {
                    let index = i + 1
                    let objectProperties = JSON.stringify(result[i])
                    let isActionCustom = result[i]?.isCustom

                    let checkBoxCont = customStatus ? `<input type="checkbox" class="form-check-input timelineCheckbox" id="${getRandomId('checkBox')}" ${isWorkflowCustom && isActionCustom ? 'checked' : ''} />` : '';
                    let groupCheckBoxCont = customStatus ? `<input type="checkbox" class="form-check-input timelineGroupCheckbox m-0" id="${result[i]?.groupId}" name="groupActionCheck" />` : '';

                    let actionType = result[i].type

                    let backGroundColor = actionType ? actionType.toLowerCase() === 'common' ? '#0e97ff' : actionType.toLowerCase() === 'operation' ? '#f30' : actionType.toLowerCase() === 'monitoring' ? '#009' : actionType.toLowerCase() === 'monitoring' ? '#f90' : '#08a200' : '#08a200';

                    if (result[i].isParallel && !result[i].isGroup) {
                        let timeLineList = `<a href="#" class="list-group-item list-group-item-action p-2 timeLineList" aria-current="true" id="${result[i]?.stepId}" style="${!isActionCustom && !customStatus && isWorkflowCustom ? 'opacity: 0.5; pointer-events: none;' : ''}">
                            <div class= "d-flex justify-content-between gap-2 text-truncate align-items-start">
                            <div class="d-flex align-items-center checkBoxContainer">${checkBoxCont}<i class="${result[i].icon || 'cp-time-line-action'} workflowIcon workflowTextClass circle fs-7" style='color:white;background:${backGroundColor}'></i></div>
                            <div class="w-100 text-truncate d-grid" ><div class="d-flex">
                            <span class="fw-bold timeLineActineName text-truncate fs-8 text-muted" title="${result[i]?.workflowActionName}" style="width: 220px">${index}. ${result[i]?.workflowActionName}</span>
                            </div>
                            </div><small class="text-danger timeLineErrorMessage" title="${result[i]?.message}">${result[i]?.message}</small>
                            <div/><div class= "text-center d-flex"><span class="fs-8 cp-Parallel text-primary me-1 mt-1" cursorshover="true"></span>
                            <span class= "cp-${!isActionCustom && !customStatus && isWorkflowCustom ? 'pending text-secondary' : 'pending text-warning'} fs-6 image"></span ></div></div></div></div></a>`

                        $('#timeline_view').append(timeLineList)

                    } else if (result[i].isGroup || (result[i].isGroup && result[i].isParallel)) {
                        const existingAccordion = $('#' + result[i].groupId);
                        if (existingAccordion.length > 0) {
                            let timelineList = `<a href="#" class="list-group-item list-group-item-action p-2 timeLineList" aria-current="true" id="${result[i]?.stepId}" style="${!isActionCustom && !customStatus && isWorkflowCustom ? 'opacity: 0.5; pointer-events: none;' : ''}">
                                <div class= "d-flex justify-content-between gap-2 text-truncate align-items-start" >
                                <div class="d-flex align-items-center checkBoxContainer">${checkBoxCont} 
                                <i class="${result[i].icon || 'cp-time-line-action'} workflowIcon workflowTextClass circle fs-7" style='color:white;background:${backGroundColor}'></i></div><div class="w-100 text-truncate">
                                <span class="fw-bold timeLineActineName text-truncate fs-8 text-muted" title="${result[i]?.workflowActionName}" data-propertices='${objectProperties}' style="${result[i]?.isParallel ? 'width: 220px;' : ''}">${index}. ${result[i]?.workflowActionName}</span>
                                <div class="d-flex align-items-center gap-3 mt-1 text-secondary text-truncate">
                                
                                </div><small class="text-danger timeLineErrorMessage" title="${result[i]?.message}" >${result[i]?.message}</small></div><div div class="text-center">`;
                            timelineList += result[i]?.isParallel ? `<span class="fs-8 me-1 cp-Parallel text-primary" cursorshover="true"></span>` : '';
                            timelineList += `<span class= "cp-${!isActionCustom && !customStatus && isWorkflowCustom ? 'pending text-secondary' : 'pending text-warning'} fs-6 image" ></span></div></div></div></a>`;

                            existingAccordion.find('.accordion-body').append(timelineList);
                        } else {
                            let timeLineList = `<div class="accordion border rounded-2 Workflow-Digram-Accordion timeline_accordion timelineGroupContainer" id="${result[i]?.groupId}"><div class="accordion-item"><div class="accordion-header">
                                <button class="accordion-button btnGroup p-2" type="button">
                                <div class="flex-fill d-flex align-items-center gap-2 groupCheckBoxContainer">${groupCheckBoxCont} 
                                <i class="cp-group fs-7" style="background-color:var(--bs-pink) "></i>
                                <span id="groupNameText">${result[i]?.groupName}</span></div><div class="w-auto">
                                <i class="cp-circle-downarrow p-0 text-dark collapsed btnAccordion" data-bs-toggle="collapse" aria-expanded="false" data-bs-target="#accordion_${result[i]?.stepId}"></i></div></button>
                                </div><div class="collapse accordian show" aria-labelledby="headingTwo" id="accordion_${result[i]?.stepId}"> <div class="accordion-body pt-0 p-2">
                                <a href="#" class="list-group-item list-group-item-action p-2 timeLineList" aria-current="true" id="${result[i]?.stepId}" style="${!isActionCustom && !customStatus && isWorkflowCustom ? 'opacity: 0.5; pointer-events: none;' : ''}">
                                <div class= "d-flex justify-content-between gap-2 text-truncate align-items-start" ><div class="d-flex align-items-center checkBoxContainer">
                                ${checkBoxCont} 
                                <i class="${result[i].icon || 'cp-time-line-action'} workflowIcon workflowTextClass circle fs-7" style='color:white;background:${backGroundColor}'></i></div><div class="w-100 text-truncate">
                                <span class="fw-bold timeLineActineName text-truncate fs-8 text-muted" title="${result[i]?.workflowActionName}">${index}. ${result[i]?.workflowActionName}</span>
                                                              
                                <div class="d-flex align-items-center gap-3 mt-1 text-secondary text-truncate">
                                </div>
                                <small class="text-danger timeLineErrorMessage" title="${result[i]?.message}">${result[i]?.message}</small>
                                </div><div class="text-center">
                                ${result[i]?.isParallel ? '<span class="text-primary fs-8 me-1 cp-Parallel" cursorshover="true"></span>' : ''}
                                <span class= "cp-${!isActionCustom && !customStatus && isWorkflowCustom ? 'pending text-secondary' : 'pending text-warning'} fs-6 image"></span >                               
                                </div></div></div></a></div></div>`
                            $('#timeline_view').append(timeLineList)
                        }
                    } else {
                        let timeLineList = `<a href="#" class="list-group-item list-group-item-action p-2 timeLineList" aria-current="true" id="${result[i]?.stepId}" style="${!isActionCustom && !customStatus && isWorkflowCustom ? 'opacity: 0.5; pointer-events: none;' : ''}">
                            <div class= "d-flex justify-content-between gap-2 text-truncate align-items-start">
                            <div class="d-flex align-items-center checkBoxContainer">${checkBoxCont}
                            <i class="${result[i].icon || 'cp-time-line-action'} workflowIcon workflowTextClass circle fs-7" style='color:white;background:${backGroundColor}'></i></div><div class="w-100 text-truncate">
                            <span class="fw-bold timeLineActineName text-truncate fs-8 text-muted" title="${result[i]?.workflowActionName}" data-properties='${objectProperties}'>${index}. ${result[i]?.workflowActionName}</span><br />
                            <div class="d-flex align-items-center gap-3 mt-1 text-secondary text-truncate">
                           </div>
                            <small class="text-danger timeLineErrorMessage" title="${result[i]?.message}">${result[i]?.message}</small>
                            </div><div div class="text-center"><small class="me-2 d-none"><i class="cp-time-2 fs-8 me-1"></i>2025-02-11 15:35:21</small><span class="cp-${!isActionCustom && !customStatus && isWorkflowCustom ? 'pending text-secondary' : 'pending text-warning'} fs-6 image"></span></div></div></div><a>`
                        $('#timeline_view').append(timeLineList)
                    }
                }
            } else {
                errorNotification(result);
                $("#timeline_view").append(noDataImages.timeLine);
            }
            $('#parentWETimeLoader').addClass('d-none')
        }
    })

    if (customStatus) {
        $('#createCustomCont').removeClass('d-none');
    }

    setTimeout(() => {
        if ($('#timeline_view .timelineGroupContainer').length) {
            if ($('.timelineGroupContainer .timelineCheckbox:checked').length === $('.timelineGroupContainer .timelineCheckbox').length) {
                $('.timelineGroupContainer .timelineGroupCheckbox').prop('checked', true)
            } else {
                $('.timelineGroupContainer .timelineGroupCheckbox').prop('checked', false)
            }
        }
    },200)
}


$(document).on('click', '.groupCheckParent', WFExeEventDebounce(async function (e) {
    let getId = $(this).data('id');
    $(`#${getId} .timelineCheckbox`).prop('checked', e.target.checked)
    if ($(".timelineCheckbox").length == $(".timelineCheckbox:checked").length) {
        $('.timelineCheckboxAll').prop("checked", true);
        $('#createCustomCont').removeClass("d-none")
    } else if (e.target.checked) {
        $('#createCustomCont').removeClass("d-none")
    } else {
        $('.timelineCheckboxAll').prop("checked", false);
        !($(".timelineCheckbox:checked").length) && $('#createCustomCont').addClass("d-none")
    }
}, 800))

$("#cancelCustomExecution").on('click', WFExeEventDebounce(() => {
    let workflowId = $('.workflowListContainer.Active-Card').attr('id')
    let workflowName = $(`#${workflowId}`).attr('workflowName')
    $('#timeline_view .allSelect').remove()
    $('.checkBoxContainer .timelineCheckbox').remove();
    $('#createCustomCont').addClass('d-none');
    $('.btnCustomWorkflow').removeClass('active')
    $(`#${workflowId}`).parents('.profileContainer').find('.btnStart').prop('disabled', false)
    let data = {
        id: workflowId,
        workflowName: workflowName
    }
    defaultTimeLineView(data, false)
}, 800))

const setNodeNameToTimeline = (workflowName, nodeName, nodeId) => {
    $('#timeline_WorkflowName').empty().append('<div class="card-header fw-semibold">Timeline View</div>');
    $('#timeline_WorkflowName').append(`<div class="card-header  TimelineWorkflowName"><span title = "${workflowName || 'NA'}"  class= "text-truncate" style = "max-width:185px;display:block"> ${workflowName || 'NA'} </span>
    <span id='${timeLineData.workflowGroupId}-node' class="text-truncate text-primary d-block ${nodeName && nodeName?.toLowerCase() !== 'null' ? '' : 'd-none'}"  style="max-width: 185px;font-size:9px;"><i class="cp-network align-middle me-1 fs-9" title="Current Node"></i><span title="${nodeName || 'NA'}"> - ${nodeName || 'NA'}</span></span>
    </div>`);
}

const TimeLineView = async (timeLineData, workflowName, nodeName) => {

    $('#timeline_view').empty()
    $('#parentWETimeLoader').removeClass('d-none');

    await $.ajax({
        type: "GET",
        url: RootUrl + 'ITAutomation/WorkflowExecution/TimeLineView',
        data: timeLineData,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result && result.length) {
                let isCustom = result.filter((d) => d.isCustom).length
                if (result[0].workflowOperationGroupId) {
                    if (isCustom !== 0) {
                        $(`#${result[0].workflowOperationGroupId}.byPassedCount`).parent().removeClass('d-none')
                    } else {
                        $(`#${result[0].workflowOperationGroupId}.byPassedCount`).parent().addClass('d-none')
                    }
                }
                let workflowId = result[0]?.workflowOperationGroupId && $(`.runningWorkflowContainer[operationGroupId=${result[0]?.workflowOperationGroupId}]`).attr('id')
                let resultLength = result.length
                for (let i = 0; i < resultLength; i++) {
                    let index = i + 1;
                    let startDate = result[i]?.startTime.split("-")
                    let endDate = result[i]?.endTime.split("-")
                    let splitStartTime = startDate[0] == "0001" ? '' : result[i]?.startTime?.replace('T', ' ')?.split('.')[0];
                    let splitEndTime = endDate[0] == "0001" ? '' : result[i]?.endTime?.replace('T', ' ')?.split('.')[0];

                    const status = result[i]?.status?.toLowerCase();
                    const isParallel = result[i]?.isParallel && !result[i]?.isGroup;
                    const isGroupOrParallel = result[i]?.isGroup || (result[i]?.isGroup && result[i]?.isParallel);

                    if (status === 'wait') {                       
                        $(`#${workflowId}-wait`).removeClass('d-none').attr('timeline-data', JSON.stringify(result[i]))
                    }

                    const appendTimeLine = (statusClass, alertClass, animateClass = '') => {
                        if (isParallel) {
                            appendParallelTimeLine(result[i], index, splitStartTime, splitEndTime, isCustom, statusClass, alertClass, animateClass);
                        } else if (isGroupOrParallel) {
                            appendGroupTimeLine(result[i], index, splitStartTime, splitEndTime, isCustom, statusClass, alertClass, animateClass);
                        } else {
                            appendSequenceTimeLine(result[i], index, splitStartTime, splitEndTime, isCustom, statusClass, alertClass, animateClass);
                        }
                    };

                    switch (status) {
                        case "error":
                            appendTimeLine('error', 'danger');
                            $('#btnFindTimelineError').removeClass('d-none')
                            break;
                        case "pending":
                            appendTimeLine('pending', 'warning');
                            break;
                        case "skip":
                            appendTimeLine('skipped', 'info');
                            break;
                        case "bypassed":
                            appendTimeLine('by-pass', '', '#458B82');
                            break;
                        case "running":
                            appendTimeLine('reload', 'primary', 'cp-animate');
                            break;
                        case "wait":
                            appendTimeLine('time', 'primary');
                            break;
                        case "pause":
                        case "paused":
                            appendTimeLine('circle-pause', 'secondary');
                            break;
                        case "aborted":
                            appendTimeLine('aborted', 'primary');
                            break;
                        default:
                            appendTimeLine('success', 'success');
                    }

                }

                let parallelLength = $("#timeline_view .cp-Parallel").length
                let timelineLength = $("#timeline_view .cp-Parallel").parent().parent().find('.cp-error').length

                if (timelineLength > 1 && parallelLength >= 1) {
                    $(`#${workflowId} .parallelErrorModal`).removeClass('d-none')
                    $(`#${workflowId}`).find('.btnNext, .btnRetry, .btnReload, .btnPauseResume').attr('disabled', true).removeClass('btn-outline-primary').addClass('btn-outline-secondary').css('color', 'var(--bs-secondary)')
                } else if (timelineLength == 1) {
                    $(`#${workflowId} .parallelErrorModal`).addClass('d-none')
                    $(`#${workflowId}`).find('.btnNext, .btnRetry, .btnReload, .btnPauseResume').attr('disabled', false).removeClass('btn-outline-secondary').addClass('btn-outline-primary').css('color', 'var(--bs-primary)')
                }

                setTimeout(() => {
                    if ($('#individualCountCont').hasClass('open')) {
                        updateTimelineCounts()
                    } 
                }, 400)
            } else {
                errorNotification(result?.message);
            }

            $('#parentWETimeLoader').addClass('d-none');
        }
    })
}

const appendParallelTimeLine = (j, index, splitStartTime, splitEndTime, isCustom, text, classMessage, reloadClass = '') => {
    let customStatus = !j.isCustom && isCustom
    let backgroundColor = j?.type ? j?.type.toLowerCase() === 'common' ? '#0e97ff' : j?.type.toLowerCase() === 'operation' ? '#f30' : j?.type.toLowerCase() === 'monitoring' ? '#009' : j?.type.toLowerCase() === 'monitoring' ? '#f90' : '#08a200' : '#08a200';

    let parallelList = `<a href="#" class="list-group-item list-group-item-action p-2 timeLineList" aria-current="true" id="${j.stepId}"  style="${customStatus ? 'opacity: 0.5; pointer-events: none;' : ''}">
        <div class=" d-flex justify-content-between text-truncate gap-2 align-items-start">
        <div class="d-flex align-items-center checkBoxContainer"><i class="${j.icon || 'cp-time-line-action'} workflowIcon workflowTextClass circle fs-7" style='color:white;background:${backgroundColor}'></i></div>
        <div class="w-100 text-truncate d-grid" id="TimeLine"><div class="d-flex">
        <span class="fw-bold timeLineActineName text-truncate fs-8 text-muted" data-stepId="${j?.stepId}" title="${j?.workflowActionName}" style="max-width: 220px">${index}. ${j.workflowActionName}
        </span>`

    parallelList += `</div><div class="d-flex align-items-center gap-3 mt-1 text-secondary text-truncate justify-content-between">
        <small class="timeLineStartTime" startTime="${j.startTime}" id="${j?.stepId}">${splitStartTime.length ? `<i class="cp-time fs-8 me-1"></i>` : ''}${splitStartTime}</small><small class="ms-2 timeLineEndTime text-truncate ${text === 'success' || text === 'skipped' || text === 'aborted' ? 'd-block' : 'd-none'}" endTime="${j.endTime}" id="${j?.stepId}">${splitEndTime.length ? `<i class="cp-time-2 fs-8 me-1"></i>` : ''}${splitEndTime}</small></div>
        <small title="${j?.status?.toLowerCase() === 'error' ? j.message || '' : ''}" class="text-danger timeLineErrorMessage">${j?.status?.toLowerCase() === 'error' ? j.message || '' : ''}</small>
        </div><span class="fs-8 cp-Parallel text-primary me-1 mt-1" cursorshover="true"></span>`

    parallelList += classMessage ? `<i class="cp-${text} text-${classMessage} ${reloadClass} fs-6 image" title="${j.status}" id="${j.stepId}" data-actionId="${j?.actionId}"></i></div > `
        : ` <i class="cp-${text} fs-6 image" title="${j.status}" style="color:${reloadClass};" id="${j.stepId}" data-actionId="${j?.actionId}"></i></div ></div></div></div></a>`;



    $('#timeline_view').append(parallelList)

    //if (text === 'reload' || text === 'error' || text === 'success') {
    //    $('#' + j.stepId)[0].scrollIntoView()
    //}
}

const appendSequenceTimeLine = (j, index, splitStartTime, splitEndTime, isCustom, text, classMessage, reloadClass = '') => {
    let customStatus = !j.isCustom && isCustom
    let collopseId = getRandomId('collapse');
    let backgroundColor = j?.type ? j?.type.toLowerCase() === 'common' ? '#0e97ff' : j?.type.toLowerCase() === 'operation' ? '#f30' : j?.type.toLowerCase() === 'monitoring' ? '#009' : j?.type.toLowerCase() === 'monitoring' ? '#f90' : '#08a200' : '#08a200';

    let seqTimeLineList = `<a  class="list-group-item list-group-item-action p-2 timeLineList"  id="${j.stepId}" style="${customStatus ? 'opacity: 0.5; pointer-events: none;' : ''}">
        <div class=" d-flex justify-content-between text-truncate gap-2 align-items-start" role="button" data-bs-toggle="collapse" href='${collopseId}' data-bs-target="#${collopseId}" aria-expanded="false" aria-controls="${collopseId}">
        <div class="d-flex align-items-center checkBoxContainer"><i class="${j.icon || 'cp-time-line-action'} workflowIcon workflowTextClass circle fs-7" style='color:white;background:${backgroundColor}'></i>
        </div><div class="w-100 text-truncate" id="TimeLine">
        <span class="fw-bold timeLineActineName text-truncate fs-8 text-muted" title="${j?.workflowActionName}">${index}. ${j.workflowActionName}</span><br/>
        <div class="d-flex align-items-center gap-3 mt-1 text-secondary text-truncate justify-content-between collapse" id="${collopseId}">
        <small class="timeLineStartTime" startTime="${j.startTime}">${splitStartTime.length ? `<i class="cp-time fs-8 me-1"></i>` : ''}${splitStartTime}</small><small class="ms-2 timeLineEndTime text-truncate ${text === 'success' || text === 'skipped' || text === 'aborted' ? 'd-block' : 'd-none'}">${splitEndTime.length ? `<i class="cp-time-2 fs-8 me-1"></i>` : ''}${splitEndTime}</small>
        </div><small title="${j?.status?.toLowerCase() === 'error' ? j.message || '' : ''}" class="text-danger timeLineErrorMessage">${j?.status?.toLowerCase() === 'error' ? j.message || '' : ''}</small></div>`

    seqTimeLineList += classMessage ? `<div class="text-center"><i class="cp-${text} text-${customStatus ? 'secondary' : classMessage} ${reloadClass} fs-6 image"  title="${j.status}" data-actionId="${j?.actionId}"></i></div>`
        : `<div class="text-center"><i class="cp-${text} fs-6 image" title="${j.status}" style="color:${reloadClass}" data-actionId="${j?.actionId}"></i></div>`

    seqTimeLineList += `</div></a>`;

    $('#timeline_view').append(seqTimeLineList)


    //if (text === 'reload' || text === 'error' || text === 'success') {
    //    $('#' + j.stepId)[0].scrollIntoView()
    //}
}
//const appendSequenceTimeLine = (j, index, splitStartTime, splitEndTime, isCustom, text, classMessage, reloadClass = '') => {
//    let customStatus = !j.isCustom && isCustom
//    let collopseId = getRandomId('collapse');

//    let seqTimeLineList = `<a  class="list-group-item list-group-item-action p-2 timeLineList" role="button" data-bs-toggle="collapse" href='${collopseId}' data-bs-target="#${collopseId}" aria-expanded="false" aria-controls="collapseExample" id="${j.stepId}" style="${customStatus ? 'opacity: 0.5; pointer-events: none;' : ''}">
//        <div class=" d-flex justify-content-between text-truncate gap-2 align-items-start">
//        <div class="d-flex align-items-center checkBoxContainer"><i class="${j.icon || 'cp-time-line-action'} pt-1 fs-6 ${customStatus ? 'text-secondary' : 'text-primary'}"></i>
//        </div><div class="w-100 text-truncate" id="TimeLine">
//        <span class="fw-bold timeLineActineName text-truncate fs-8 text-muted" title="${j?.workflowActionName}">${index}. ${j.workflowActionName}</span><br/>
//        <div class="d-flex align-items-center gap-3 mt-1 text-secondary text-truncate justify-content-between collapse" id="${collopseId}">
//        <small class="timeLineStartTime" startTime="${j.startTime}">${splitStartTime.length ? `<i class="cp-time fs-8 me-1"></i>` : ''}${splitStartTime}</small><small class="ms-2 timeLineEndTime text-truncate ${text === 'success' || text === 'skip' || text === 'aborted' ? 'd-block' : 'd-none'}">${splitEndTime.length ? `<i class="cp-time-2 fs-8 me-1"></i>` : ''}${splitEndTime}</small>
//        </div><small title="${j.message || 'NA'}" class="text-danger timeLineErrorMessage">${j.message || ''}</small></div>`

//    seqTimeLineList += classMessage ? `<div class="text-center"><i class="cp-${text} text-${customStatus ? 'secondary' : classMessage} ${reloadClass} fs-6 image"  title="${j.status}" data-actionId="${j?.actionId}"></i></div>`
//        : `<div class="text-center"><i class="cp-${text} fs-6 image" title="${j.status}" style="color:${reloadClass}" data-actionId="${j?.actionId}"></i></div>`

//    seqTimeLineList += `</div></a>`;

//    $('#timeline_view').append(seqTimeLineList)


//    //if (text === 'reload' || text === 'error' || text === 'success') {
//    //    $('#' + j.stepId)[0].scrollIntoView()
//    //}
//}

const appendGroupTimeLine = (j, index, splitStartTime, splitEndTime, isCustom, text, classMessage, reloadClass = '') => {
    const existingAccordion = $('#' + j.groupId);
    let customStatus = !j.isCustom && isCustom
    let backgroundColor = j?.type ? j?.type.toLowerCase() === 'common' ? '#0e97ff' : j?.type.toLowerCase() === 'operation' ? '#f30' : j?.type.toLowerCase() === 'monitoring' ? '#009' : j?.type.toLowerCase() === 'monitoring' ? '#f90' : '#08a200' : '#08a200';
    if (existingAccordion.length > 0) {


        let existingTimeLineList = `<a href="#" class="list-group-item list-group-item-action p-2 timeLineList" data-bs-toggle="collapse" href="#collapseExample" role="button" aria-expanded="false" aria-controls="collapseExample" id="${j.stepId}"  style="${customStatus ? 'opacity: 0.5; pointer-events: none;' : ''}">
              <div class=" d-flex text-truncate gap-2 align-items-start"><div class="d-flex align-items-center checkBoxContainer">
              <i class="${j.icon || 'cp-time-line-action'} workflowIcon" style='color:white;background:${backgroundColor}'></i></div><div class="w-100 text-truncate" id="TimeLine">
              <span class="fw-bold timeLineActineName text-truncate fs-8 text-muted" data-stepId="${j?.stepId}" title="${j?.workflowActionName}" id="${j.stepId}">${index}. ${j.workflowActionName}</span>
              ${!j.isParallel ? '<br />' : ''}
              <div class="d-flex align-items-center gap-3 mt-1 text-secondary text-truncate justify-content-between collapse" id="collapseExample">
              <small class="timeLineStartTime">${splitStartTime.length ? `<i class="cp-time fs-8 me-1"></i>` : ''}${splitStartTime}</small><small class="ms-2 timeLineEndTime text-truncate ${text === 'success' || text === 'skipped' || text === 'aborted' ? 'd-block' : 'd-none'}" title="${splitEndTime}">${splitEndTime.length ? `<i class="cp-time-2 fs-8 me-1"></i>` : ''}${splitEndTime}</small></div>
              <small title="${j?.status?.toLowerCase() === 'error' ? j.message || '' : ''}" class="text-${classMessage} timeLineErrorMessage">${j?.status?.toLowerCase() === 'error' ? j.message || '' : ''}</small></div>
              ${j.isParallel ? '<div class= "d-flex align-items-center ms-auto" >' : ''}
              ${j.isParallel ? '<span class="text-primary fs-8 me-1 cp-Parallel" cursorshover="true"></span>' : ''}`

        existingTimeLineList += classMessage ? `<div class="text-center"><i class="cp-${text} text-${classMessage} ${reloadClass} fs-6 image" title="${j.status}" data-actionId="${j?.actionId}"></i></div>`
            : `<div class="text-center"><i class="cp-${text} fs-6 image" title="${j.status}" style="color:${reloadClass};" data-actionId="${j?.actionId}"></i></div>`

        existingTimeLineList += `${j.isParallel ? '</div>' : ''}</div></a>`

        existingAccordion.find('.accordion-body').append(existingTimeLineList)

    } else {

        let groupTimeLineList = `<div class="accordion border rounded-2 Workflow-Digram-Accordion timelineGroupContainer" id="${j.groupId}">
            <div class="accordion-item"><div class="accordion-header"><button class="accordion-button btnGroup p-2" type="button"><div class="flex-fill d-flex align-items-center gap-2 groupCheckBoxContainer">
            <i class="cp-group ms-1 fs-7" style="background-color:var(--bs-pink) "></i><span id="groupNameText">${j.groupName}</span></div><div class="w-auto">
            <i class="cp-circle-downarrow p-0 text-dark collapsed btnAccordion" data-bs-toggle="collapse" aria-expanded="false" data-bs-target="#accordion_${j.stepId}"></i></div></button>
            </div><div class="collapse accordian show" aria-labelledby="headingTwo" id="accordion_${j.stepId}"> <div class="accordion-body pt-0 p-2">
            <a href="#" class="list-group-item list-group-item-action p-2 timeLineList" aria-current="true" id="${j.stepId}"  style="${customStatus ? 'opacity: 0.5; pointer-events: none;' : ''}">
            <div class=" d-flex text-truncate gap-2 align-items-start"><div class="d-flex align-items-center checkBoxContainer">
            <i class="${j.icon || 'cp-time-line-action'} workflowIcon" style='color:white;background:${backgroundColor}'></i></div><div class="w-100 text-truncate" id="TimeLine">
            <span class="fw-bold timeLineActineName text-truncate fs-8 text-muted"  title="${j?.workflowActionName}">${index}. ${j.workflowActionName}</span>
            ${!j.isParallel ? '<br />' : ''}
            <div class="d-flex align-items-center gap-3 mt-1 text-secondary text-truncate justify-content-between">
            <small class="timeLineStartTime">${splitStartTime.length ? `<i class="cp-time fs-8 me-1"></i>` : ''}${splitStartTime}</small><small class="ms-1 timeLineEndTime text-truncate ${text === 'success' || text === 'skipped' || text === 'aborted' ? 'd-block' : 'd-none'}" title="${splitEndTime}">${splitEndTime.length ? `<i class="cp-time-2 fs-8 me-1"></i>` : ''}${splitEndTime}</small></div>
            <small title="${j?.status?.toLowerCase() === 'error' ? j.message || '' : ''}" class="text-${classMessage} timeLineErrorMessage">${j?.status?.toLowerCase() === 'error' ? j.message || '' : ''}</small></div>
            ${j.isParallel ? '<div class= "d-flex align-items-center ms-auto" >' : ''}
            ${j.isParallel ? '<span class="text-primary fs-8 me-1 mt-1 cp-Parallel" cursorshover="true"></span>' : ''}`

        groupTimeLineList += classMessage ? `<div class="text-center"><i class="cp-${text} text-${classMessage} ${reloadClass} fs-6 image ${j.status == "Running" || j.status == "Aborted" ? 'me-2 cp-animate' : ''}" title="${j.status}" data-actionId="${j?.actionId}"></i></div>`
            : `<div class="text-center"><i class="cp-${text} fs-6 image ${j.status == "Running" || j.status == "Aborted" ? ' cp-animate' : ''}" title="${j.status}" style="color:${reloadClass};" id="${j.stepId}" data-actionId="${j?.actionId}"></i></div>`

        groupTimeLineList += `${j.isParallel ? '</div>' : ''}</div></a></div></div>`;

        $('#timeline_view').append(groupTimeLineList)
    }
}

$('#btnExpandTimeline').on('click', function () {
    if ($('#logViewerContainer').is(':visible')) {
        $('#logViewerContainer').addClass('d-none')
        $('#timeline_view').css('height', 'calc(100vh - 130px')
        $(this).html('<i class="cp-back me-1"></i>')
    } else {
        $('#logViewerContainer').removeClass('d-none')
        $('#timeline_view').css('height', 'calc(100vh - 385px')
        $(this).html('<i class="cp-open me-1"></i>')
    }
   
})

let errorIndex = 0
$('#btnFindTimelineError').on('click', function () {
    let getStepId = $('.image.cp-error.text-danger').eq(errorIndex).parents('.timeLineList').attr('id')

    if (errorIndex === $('.image.cp-error.text-danger').length - 1) {
        errorIndex = 0
    } else {
        errorIndex++
    }   
    $(`#${getStepId}`)[0]?.scrollIntoView()
})

$('#btnToggleTimeline').on('click', function () {
    $('#individualCountCont').toggleClass('open');
    if ($('#individualCountCont').width() === 0) {
        $('#btnToggleTimeline').empty().append('<i class="cp-rignt-arrow fs-7"></i>')
    } else {
        updateTimelineCounts()
        setTimeout(() => {
            $('#btnToggleTimeline').empty().append('<i class="cp-left-arrow fs-7"></i>')
        },300)
        
    }  
})




