﻿namespace ContinuityPatrol.Application.Features.OracleMonitorLogs.Queries.GetDetail;

public class
    GetOracleMonitorLogsDetailQueryHandler : IRequestHandler<GetOracleMonitorLogsDetailQuery, OracleMonitorLogsDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IOracleMonitorLogsRepository _oracleMonitorLogsRepository;

    public GetOracleMonitorLogsDetailQueryHandler(IOracleMonitorLogsRepository oracleMonitorLogsRepository,
        IMapper mapper)
    {
        _oracleMonitorLogsRepository = oracleMonitorLogsRepository;
        _mapper = mapper;
    }

    public async Task<OracleMonitorLogsDetailVm> Handle(GetOracleMonitorLogsDetailQuery request,
        CancellationToken cancellationToken)
    {
        var oracleMonitorLogs = await _oracleMonitorLogsRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(oracleMonitorLogs, nameof(OracleMonitorLogs),
            new NotFoundException(nameof(OracleMonitorLogs), request.Id));

        var oracleMonitorLogsDetail = _mapper.Map<OracleMonitorLogsDetailVm>(oracleMonitorLogs);

        return oracleMonitorLogsDetail ?? throw new NotFoundException(nameof(OracleMonitorLogs), request.Id);
    }
}