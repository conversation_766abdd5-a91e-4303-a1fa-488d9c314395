﻿namespace ContinuityPatrol.Shared.Infrastructure.Middlewares;

public class SerilogMiddleware
{
    private const string MessageTemplate =
        "HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms";

    private static readonly ILogger Log = Serilog.Log.ForContext<SerilogMiddleware>();

    private static readonly HashSet<string> HeaderWhitelist = new() { "Content-Type", "Content-Length", "User-Agent" };

    private readonly RequestDelegate _next;

    public SerilogMiddleware(RequestDelegate next)
    {
        _next = next ?? throw new ArgumentNullException(nameof(next));
    }

    // ReSharper disable once UnusedMember.Global
    public async Task Invoke(HttpContext httpContext)
    {
        if (httpContext == null) throw new ArgumentNullException(nameof(httpContext));

        var start = Stopwatch.GetTimestamp();
        try
        {
            if (httpContext.User.Identity != null)
                LogContext.PushProperty("UserName", httpContext.User.Identity.Name ?? "Anonymous");

            await _next(httpContext);

            var elapsedMs = GetElapsedMilliseconds(start, Stopwatch.GetTimestamp());

            var statusCode = httpContext.Response?.StatusCode;
            var level = statusCode > 499 ? LogEventLevel.Error : LogEventLevel.Debug;

            var log = level == LogEventLevel.Error ? LogForErrorContext(httpContext) : Log;

            log.Write(level, MessageTemplate, httpContext.Request.Method, GetPath(httpContext), statusCode, elapsedMs);
        }
        // Never caught, because `LogException()` returns false.
        catch (Exception ex) when (LogException(httpContext, GetElapsedMilliseconds(start, Stopwatch.GetTimestamp()),
                                       ex))
        {
        }
    }

    private static bool LogException(HttpContext httpContext, double elapsedMs, Exception ex)
    {
        var statusCode = httpContext.Response?.StatusCode;

        LogForErrorContext(httpContext)
            .Error(
                $"HTTP {httpContext.Request.Method} {GetPath(httpContext)} responded {statusCode} in {elapsedMs:0.0000} ms");

        return false;
    }

    private static ILogger LogForErrorContext(HttpContext httpContext)
    {
        var request = httpContext.Request;

        var loggedHeaders = request.Headers
            .Where(h => HeaderWhitelist.Contains(h.Key))
            .ToDictionary(h => h.Key, h => h.Value.ToString());

        var result = Log
            .ForContext("RequestHeaders", loggedHeaders, true)
            .ForContext("RequestHost", request.Host)
            .ForContext("RequestProtocol", request.Protocol);

        return result;
    }

    private static double GetElapsedMilliseconds(long start, long stop)
    {
        return (stop - start) * 1000 / (double)Stopwatch.Frequency;
    }

    private static string GetPath(HttpContext httpContext)
    {
        return httpContext.Features.Get<IHttpRequestFeature>()?.RawTarget ?? httpContext.Request.Path.ToString();
    }
}