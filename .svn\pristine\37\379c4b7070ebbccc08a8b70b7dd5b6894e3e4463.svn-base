﻿using ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Commands.Update;
using ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerWorkflowDetailModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class InfraObjectSchedulerWorkflowDetailProfile : Profile
{
    public InfraObjectSchedulerWorkflowDetailProfile()
    {
        CreateMap<InfraObjectSchedulerWorkflowDetail, CreateInfraObjectSchedulerWorkflowDetailCommand>().ReverseMap();
        CreateMap<UpdateInfraObjectSchedulerWorkflowDetailCommand, InfraObjectSchedulerWorkflowDetail>()
            .ForMember(x => x.Id, y => y.Ignore());

        CreateMap<InfraObjectSchedulerWorkflowDetail, InfraObjectSchedulerWorkflowDetailDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraObjectSchedulerWorkflowDetail, InfraObjectSchedulerWorkflowDetailListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<PaginatedResult<InfraObjectSchedulerWorkflowDetail>,PaginatedResult<InfraObjectSchedulerWorkflowDetailListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}