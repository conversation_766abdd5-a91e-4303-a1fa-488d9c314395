using ContinuityPatrol.Application.Features.DriftResourceSummary.Commands.Create;
using ContinuityPatrol.Application.Features.DriftResourceSummary.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftResourceSummary.Commands.Update;
using ContinuityPatrol.Application.Features.DriftResourceSummary.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftResourceSummary.Queries.GetList;
//using ContinuityPatrol.Application.Features.DriftResourceSummary.Queries.GetNameUnique;
//using ContinuityPatrol.Application.Features.DriftResourceSummary.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftResourceSummaryModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class DriftResourceSummarysController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<List<DriftResourceSummaryListVm>>> GetDriftResourceSummarys()
    {
        Logger.LogDebug("Get All DriftResourceSummarys");

        return Ok(await Mediator.Send(new GetDriftResourceSummaryListQuery()));
    }

    [HttpGet("{id}", Name = "GetDriftResourceSummary")]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<DriftResourceSummaryDetailVm>> GetDriftResourceSummaryById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DriftResourceSummary Id");

        Logger.LogDebug($"Get DriftResourceSummary Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetDriftResourceSummaryDetailQuery { Id = id }));
    }
    #region Paginated
    // [Route("paginated-list"), HttpGet]
    // [Authorize(Policy = Permissions.Drift.View)]
    // public async Task<ActionResult<PaginatedResult<DriftResourceSummaryListVm>>> GetPaginatedDriftResourceSummarys([FromQuery] GetDriftResourceSummaryPaginatedListQuery query)
    // {
    //     Logger.LogDebug("Get Searching Details in DriftResourceSummary Paginated List");
    //
    //     return Ok(await Mediator.Send(query));
    // }
    #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Drift.Create)]
    public async Task<ActionResult<CreateDriftResourceSummaryResponse>> CreateDriftResourceSummary([FromBody] CreateDriftResourceSummaryCommand createDriftResourceSummaryCommand)
    {
        Logger.LogDebug($"Create DriftResourceSummary '{createDriftResourceSummaryCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateDriftResourceSummary), await Mediator.Send(createDriftResourceSummaryCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Drift.Edit)]
    public async Task<ActionResult<UpdateDriftResourceSummaryResponse>> UpdateDriftResourceSummary([FromBody] UpdateDriftResourceSummaryCommand updateDriftResourceSummaryCommand)
    {
        Logger.LogDebug($"Update DriftResourceSummary '{updateDriftResourceSummaryCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateDriftResourceSummaryCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Drift.Delete)]
    public async Task<ActionResult<DeleteDriftResourceSummaryResponse>> DeleteDriftResourceSummary(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DriftResourceSummary Id");

        Logger.LogDebug($"Delete DriftResourceSummary Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteDriftResourceSummaryCommand { Id = id }));
    }

    #region NameExist

    // [Route("name-exist"), HttpGet]
    // public async Task<ActionResult> IsDriftResourceSummaryNameExist(string driftResourceSummaryName, string? id)
    // {
    //     Guard.Against.NullOrWhiteSpace(driftResourceSummaryName, "DriftResourceSummary Name");
    //
    //     Logger.LogDebug($"Check Name Exists Detail by DriftResourceSummary Name '{driftResourceSummaryName}' and Id '{id}'");
    //
    //     return Ok(await Mediator.Send(new GetDriftResourceSummaryNameUniqueQuery { Name = driftResourceSummaryName, Id = id }));
    // }
    #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


