namespace ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Create;

public class CreateBulkImportOperationGroupCommand : IRequest<CreateBulkImportOperationGroupResponse>
{
    public string BulkImportOperationId { get; set; }
    public string CompanyId { get; set; }
    public string Properties { get; set; }
    public string Status { get; set; }
    public string ProgressStatus { get; set; }
    public string ErrorMessage { get; set; }
    public int ConditionalOperation { get; set; }
    public string NodeId { get; set; }
    public string InfraObjectName { get; set; }
}