using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class EscalationMatrixLevelFixture : IDisposable
{
    public List<EscalationMatrixLevel> EscalationMatrixLevelPaginationList { get; set; }
    public List<EscalationMatrixLevel> EscalationMatrixLevelList { get; set; }
    public EscalationMatrixLevel EscalationMatrixLevelDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public EscalationMatrixLevelFixture()
    {
        var fixture = new Fixture();

        EscalationMatrixLevelList = fixture.Create<List<EscalationMatrixLevel>>();

        EscalationMatrixLevelPaginationList = fixture.CreateMany<EscalationMatrixLevel>(20).ToList();

        EscalationMatrixLevelPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        EscalationMatrixLevelPaginationList.ForEach(x => x.IsActive = true);

        EscalationMatrixLevelList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        EscalationMatrixLevelList.ForEach(x => x.IsActive = true);

        EscalationMatrixLevelDto = fixture.Create<EscalationMatrixLevel>();
        EscalationMatrixLevelDto.ReferenceId = Guid.NewGuid().ToString();
        EscalationMatrixLevelDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
