﻿using ContinuityPatrol.Application.Features.VeritasCluster.Commands.Update;
using ContinuityPatrol.Application.Features.VeritasCluster.Events.Update;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.VeritasCluster.Commands
{
    public class UpdateVeritasClusterTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IVeritasClusterRepository> _mockVeritasClusterRepository;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly UpdateVeritasClusterCommandHandler _handler;

        public UpdateVeritasClusterTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockVeritasClusterRepository = new Mock<IVeritasClusterRepository>();
            _mockPublisher = new Mock<IPublisher>();
            _handler = new UpdateVeritasClusterCommandHandler(_mockMapper.Object, _mockVeritasClusterRepository.Object, _mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_Updates_VeritasCluster_Successfully()
        {
            var clusterId = Guid.NewGuid().ToString();
            var clusterName = "Updated Cluster";
            var command = new UpdateVeritasClusterCommand { Id = clusterId, ClusterName = clusterName };
            var existingCluster = new Domain.Entities.VeritasCluster
            {
                ReferenceId = clusterId,
                ClusterName = "Old Cluster"
            };

            _mockVeritasClusterRepository
                .Setup(repo => repo.GetByReferenceIdAsync(clusterId))
                .ReturnsAsync(existingCluster);

            _mockMapper
                .Setup(mapper => mapper.Map(command, existingCluster, typeof(UpdateVeritasClusterCommand), typeof(Domain.Entities.VeritasCluster)))
                .Callback<UpdateVeritasClusterCommand, Domain.Entities.VeritasCluster, Type, Type>((src, dest, t1, t2) =>
                {
                    dest.ClusterName = src.ClusterName;
                });

            _mockVeritasClusterRepository
                .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.VeritasCluster>()))
                .ReturnsAsync(existingCluster);

            _mockPublisher
                .Setup(publisher => publisher.Publish(It.IsAny<VeritasClusterUpdatedEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            var response = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(response);
            Assert.Equal(clusterId, response.Id);
            Assert.Equal("Veritas Cluster updated successfully: Updated Cluster", response.Message);

            _mockVeritasClusterRepository.Verify(repo => repo.GetByReferenceIdAsync(clusterId), Times.Once);
            _mockVeritasClusterRepository.Verify(repo => repo.UpdateAsync(It.Is<Domain.Entities.VeritasCluster>(c => c.ClusterName == clusterName)), Times.Once);
            _mockPublisher.Verify(publisher => publisher.Publish(It.Is<VeritasClusterUpdatedEvent>(e => e.Name == clusterName), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Throws_NotFoundException_When_Cluster_Does_Not_Exist()
        {
            var clusterId = Guid.NewGuid().ToString();
            var command = new UpdateVeritasClusterCommand { Id = clusterId };

            _mockVeritasClusterRepository
                .Setup(repo => repo.GetByReferenceIdAsync(clusterId))
                .ReturnsAsync((Domain.Entities.VeritasCluster)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));

            _mockVeritasClusterRepository.Verify(repo => repo.GetByReferenceIdAsync(clusterId), Times.Once);
            _mockVeritasClusterRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.VeritasCluster>()), Times.Never);
            _mockPublisher.Verify(publisher => publisher.Publish(It.IsAny<VeritasClusterUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }
    }
}
