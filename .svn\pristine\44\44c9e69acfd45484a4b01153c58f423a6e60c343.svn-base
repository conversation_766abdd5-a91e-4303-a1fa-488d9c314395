﻿using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetWorkflowInfraObjectByWorkflowId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowInfraObject.Queries;

public class GetWorkflowInfraObjectByWorkflowIdQueryHandlerTests : IClassFixture<WorkflowInfraObjectFixture>
{
    private readonly WorkflowInfraObjectFixture _workflowInfraObjectFixture;

    private Mock<IWorkflowInfraObjectRepository> _mockWorkflowInfraObjectRepository;

    private readonly GetWorkflowInfraObjectByWorkflowIdQueryHandler _handler;

    public GetWorkflowInfraObjectByWorkflowIdQueryHandlerTests(WorkflowInfraObjectFixture workflowInfraObjectFixture)
    {
        _workflowInfraObjectFixture = workflowInfraObjectFixture;

        _mockWorkflowInfraObjectRepository = WorkflowInfraObjectRepositoryMocks.GetWorkflowInfraObjectByWorkflowIdRepository(_workflowInfraObjectFixture.WorkflowInfraObjects);

        _handler = new GetWorkflowInfraObjectByWorkflowIdQueryHandler(_workflowInfraObjectFixture.Mapper, _mockWorkflowInfraObjectRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnWorkflowInfraObject_When_ValidWorkflowId()
    {
        var result = await _handler.Handle(new GetWorkflowInfraObjectByWorkflowIdQuery { InfraObjectId = _workflowInfraObjectFixture.WorkflowInfraObjects[0].InfraObjectId, WorkflowId = _workflowInfraObjectFixture.WorkflowInfraObjects[0].WorkflowId }, CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowInfraObjectByWorkflowIdVm>>();

        result.Count.ShouldBe(1);

        result[0].Id.ShouldBe(_workflowInfraObjectFixture.WorkflowInfraObjects[0].ReferenceId);
        result[0].InfraObjectId.ShouldBe(_workflowInfraObjectFixture.WorkflowInfraObjects[0].InfraObjectId);
        result[0].WorkflowId.ShouldBe(_workflowInfraObjectFixture.WorkflowInfraObjects[0].WorkflowId);
        result[0].ActionType.ShouldBe(_workflowInfraObjectFixture.WorkflowInfraObjects[0].ActionType);
        result[0].TotalRTO.ShouldBe(_workflowInfraObjectFixture.WorkflowInfraObjects[0].TotalRTO);
        result[0].WorkflowVersion.ShouldBe(_workflowInfraObjectFixture.WorkflowInfraObjects[0].WorkflowVersion);
        result[0].IsAttach.ShouldBe(_workflowInfraObjectFixture.WorkflowInfraObjects[0].IsAttach);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockWorkflowInfraObjectRepository = WorkflowInfraObjectRepositoryMocks.GetWorkflowInfraObjectEmptyRepository();

        var handler = new GetWorkflowInfraObjectByWorkflowIdQueryHandler(_workflowInfraObjectFixture.Mapper, _mockWorkflowInfraObjectRepository.Object);

        var result = await handler.Handle(new GetWorkflowInfraObjectByWorkflowIdQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetWorkflowInfraObjectNameByWorkflowIdMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowInfraObjectByWorkflowIdQuery { InfraObjectId = _workflowInfraObjectFixture.WorkflowInfraObjects[0].WorkflowId }, CancellationToken.None);

        _mockWorkflowInfraObjectRepository.Verify(x => x.GetWorkflowInfraObjectFromWorkflowId(It.IsAny<string>(),It.IsAny<string>()), Times.Once);
    }
}