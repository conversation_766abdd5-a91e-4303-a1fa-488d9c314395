﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class LoadBalancerRepository : BaseRepository<LoadBalancer>, ILoadBalancerRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public LoadBalancerRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<LoadBalancer>> ListAllAsync()
    {
        var loadBalancers =await FilterRequiredField(Entities.AsNoTracking().DescOrderById()).ToListAsync();

        return loadBalancers;
        //var loadBalancers =await FilterRequiredField(base.ListAllAsync(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))).ToListAsync();

        //return  loadBalancers;

    }
    public override async Task<PaginatedResult<LoadBalancer>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<LoadBalancer> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await FilterRequiredField(Entities.Specify(productFilterSpec).DescOrderById()).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);

        //return await FilterRequiredField(_loggedInUserService.IsParent
        //    ? Entities.Specify(productFilterSpec).DescOrderById()
        //    : Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()).ToPaginatedListAsync(pageNumber, pageSize);
    }
    public override IQueryable<LoadBalancer> GetPaginatedQuery()
    {
        var loadBalancers = Entities.AsNoTracking().OrderByDescending(x => x.Id);

        return loadBalancers;
        //var loadBalancers = base.ListAllAsync(x => x.CompanyId.Equals(_loggedInUserService.CompanyId));

        //return loadBalancers.AsNoTracking().OrderByDescending(x => x.Id);
    }

    public override async Task<LoadBalancer> GetByReferenceIdAsync(string id)
    {
        var loadBalancers = base.GetByReferenceId(id,
            node => node.ReferenceId.Equals(id));

        return await loadBalancers.FirstOrDefaultAsync();
        //var loadBalancers = base.GetByReferenceIdAsync(id,
        //    node => node.CompanyId.Equals(_loggedInUserService.CompanyId) && node.ReferenceId.Equals(id));

        //return await loadBalancers.FirstOrDefaultAsync();
    }

    public async Task<List<LoadBalancer>> GetLoadBalancerByType(string id,string type)
    {
        return await base.FilterBy(x => x.Type.Equals(type) && x.ReferenceId != id).ToListAsync();
    }

    public async Task<List<LoadBalancer>> GetNodeNameByIdAsync(List<string> id)
    {
        return await base.FilterBy(x => id.Contains(x.ReferenceId))
            .Select(x=> new LoadBalancer
            {
                ReferenceId = x.ReferenceId,
                Name = x.Name
            }).ToListAsync();
    }

    public async Task<bool> IsNodeConfigurationNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? await Entities.AsNoTracking().AnyAsync(e => e.Name.Equals(name))
            : await Entities.AsNoTracking().AnyAsync(e => e.Name.Equals(name) && e.ReferenceId != id);
    }


    public Task<bool> IsNodeConfigurationIpAddressAndPortExist(string ipAddress, int port,string id)
    {
        var result = !id.IsValidGuid()
            ? Entities.Any(e =>e.IsActive && e.IPAddress.Equals(ipAddress) && e.Port==port)
            : Entities.Where(e => e.IsActive && e.IPAddress.Equals(ipAddress) && e.Port==port).ToList().Unique(id);
        return Task.FromResult(result);
    }

    public Task<bool> IsNodeConfigurationNameUnique(string name)
    {
        var matches = _dbContext.NodeConfigurations.Any(e => e.Name.Equals(name));

        return Task.FromResult(matches);
    }

    public async Task<(List<string> ActiveNodes, List<string> InActiveNodes)> GetActiveNodeAndInActiveNodeByType(string type)
    {
        var nodesQuery =await base.FilterBy(x => x.TypeCategory.ToLower().Equals("cp node") && x.Type.ToLower().Replace(" ", "").Equals(type.ToLower().Replace(" ", ""))).ToListAsync();

        //var nodesQuery = _loggedInUserService.IsParent
        //    ? await base.FilterBy(x => x.TypeCategory.ToLower().Equals("cp node") && x.Type.ToLower().Replace(" ", "").Equals(type.ToLower().Replace(" ", ""))).ToListAsync()
        //        :await base.FilterBy(x => x.TypeCategory.ToLower().Equals("cp node") && x.Type.ToLower().Replace(" ", "").Equals(type.ToLower().Replace(" ", "")) && x.CompanyId.Equals(_loggedInUserService.CompanyId)).ToListAsync();

        var activeNodes = nodesQuery
            .Where(x => x.HealthStatus.Equals("active", StringComparison.OrdinalIgnoreCase))
            .Select(x => x.Name).ToList();
            

        var inactiveNodes =  nodesQuery
            .Where(x => !x.HealthStatus.Equals("active", StringComparison.OrdinalIgnoreCase))
            .Select(x => x.Name)
            .ToList();

        return (activeNodes, inactiveNodes);
    }



    public async Task<LoadBalancer> GetNodeConfigurationByTypeAndTypeCategory(string type, string typeCategory)
    {
        return await base.FilterBy(x => x.Type.Equals(type) && x.TypeCategory.Equals(typeCategory) && x.IsNodeStatus)
         .Select(x => new LoadBalancer { ReferenceId = x.ReferenceId, Type = x.Type, TypeCategory = x.TypeCategory, ConnectionType = x.ConnectionType, IPAddress = x.IPAddress, Port = x.Port })
         .FirstOrDefaultAsync();

        //return _loggedInUserService.IsParent
        //     ? await base.FilterBy(x => x.Type.Equals(type) && x.TypeCategory.Equals(typeCategory) && x.IsNodeStatus)
        //         .Select(x => new LoadBalancer { ReferenceId = x.ReferenceId ,Type  = x.Type, TypeCategory = x.TypeCategory, ConnectionType =x.ConnectionType, IPAddress =x.IPAddress, Port =x.Port })
        //         .FirstOrDefaultAsync()
        //     : await base.FilterBy(x => x.Type.Equals(type) && x.TypeCategory.Equals(typeCategory) && x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.IsNodeStatus)
        //         .Select(x => new LoadBalancer { ReferenceId = x.ReferenceId, Type = x.Type, TypeCategory = x.TypeCategory, ConnectionType = x.ConnectionType, IPAddress = x.IPAddress, Port = x.Port })
        //         .FirstOrDefaultAsync();

    }

    public async Task<List<LoadBalancer>> GetNodeConfigurationListById(List<string> ids)
    {
        var result = base.FilterBy(x => ids.Contains(x.ReferenceId));

        return await result.ToListAsync();
        //var result = _loggedInUserService.IsParent
        //    ? base.FilterBy(x => x.ReferenceId.Equals(nodeConfigurationId))
        //    : base.FilterBy(x => x.ReferenceId.Equals(nodeConfigurationId) && x.CompanyId.Equals(_loggedInUserService.CompanyId));

        //return await result.ToListAsync();
    }

    public async Task<List<LoadBalancer>> GetLoadBalancerType(string type)
    {
        var query = type.IsNotNullOrWhiteSpace()
          ? base.FilterBy(x => x.Type.ToLower().Replace(" ", "") == type.ToLower().Replace(" ", ""))
          : _dbContext.NodeConfigurations.Select(x => new LoadBalancer { Type = x.Type }).Distinct().AsNoTracking();

        return await query.ToListAsync();
       
    }
    public async Task<List<LoadBalancer>> GetNamesByType(string type)
    {
        var loadBalancer = await base.FilterBy(x => x.Type.Equals(type))
            .Select(x => new LoadBalancer
            {
                Name = x.Name,
                ReferenceId = x.ReferenceId,
                Type=x.Type
            }).ToListAsync();

        return loadBalancer;
    }
    private IQueryable<LoadBalancer>FilterRequiredField(IQueryable<LoadBalancer> loadBalancers)
    {
        return loadBalancers.Select(x => new LoadBalancer
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            Name = x.Name,
            Type = x.Type,
            TypeCategory = x.TypeCategory,
            ConnectionType = x.ConnectionType,
            IPAddress = x.IPAddress,
            HostName=x.HostName,
            Port = x.Port,
            HealthStatus = x.HealthStatus,
            CompanyId = x.CompanyId,
            IsConnection = x.IsConnection,
            IsDefault   = x.IsDefault,
            IsNodeStatus=x.IsNodeStatus
        });

    }
    public async Task<bool> IsNodeInUse(string id)
    {
        var status = new[] { "running", "pending", "error" };

        var response = await _dbContext.MonitorServices.Active().AsNoTracking().AnyAsync(x => x.NodeId.Equals(id) && status.Contains(x.Status.ToLower()))
        || await _dbContext.Jobs.Active().AsNoTracking().AnyAsync(x => x.NodeId.Equals(id) && status.Contains(x.Status.ToLower()))
            || await _dbContext.ReplicationJobs.Active().AsNoTracking().AnyAsync(x => x.NodeId.Equals(id) && status.Contains(x.Status.ToLower()))
            || await _dbContext.InfraObjectSchedulers.Active().AsNoTracking().AnyAsync(x => x.NodeId.Equals(id) && status.Contains(x.Status.ToLower()))
            || await _dbContext.WorkflowOperationGroups.Active().AsNoTracking().AnyAsync(x => x.NodeId.Equals(id) && status.Contains(x.Status.ToLower()));

           return response;
    }

    
}
