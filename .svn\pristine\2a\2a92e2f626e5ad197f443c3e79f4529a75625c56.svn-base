﻿namespace ContinuityPatrol.Shared.Core.Domain;

public enum NotificationType
{
    Success,
    Info,
    Warning,
    Error,
    Unauthorised
}
public class NotificationMessage
{
    public NotificationType NotificationType { get; set; }

    public string Message { get; set; }

    public NotificationMessage(NotificationType type, string message)
    {
        NotificationType = type;
        Message = message;
    }

}

public class MultiLoginSessionMessage
{
    public string UserName { get; set; }

    public MultiLoginSessionMessage(string userName)
    {
        UserName = userName;

    }
}
