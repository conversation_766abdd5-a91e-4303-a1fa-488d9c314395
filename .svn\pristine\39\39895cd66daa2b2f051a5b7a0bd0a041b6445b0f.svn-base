﻿using ContinuityPatrol.Application.Features.DataSet.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.DataSet.Queries;

public class GetDataSetNameQueryHandlerTests : IClassFixture<DataSetFixture>
{
    private readonly DataSetFixture _dataSetFixture;

    private Mock<IDataSetRepository> _mockDataSetRepository;

    private readonly GetDataSetNameQueryHandler _handler;

    public GetDataSetNameQueryHandlerTests(DataSetFixture dataSetFixture)
    {
        _dataSetFixture = dataSetFixture;

        _mockDataSetRepository = DataSetRepositoryMocks.GetDataSetNamesRepository(_dataSetFixture.DataSets);

        _handler = new GetDataSetNameQueryHandler(_dataSetFixture.Mapper, _mockDataSetRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_DataSetsName()
    {
        var result = await _handler.Handle(new GetDataSetNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DataSetNameVm>>();

        result[0].Id.ShouldBe(_dataSetFixture.DataSets[0].ReferenceId);
        result[0].DataSetName.ShouldBe(_dataSetFixture.DataSets[0].DataSetName);
    }

    [Fact]
    public async Task Handle_Return_Active_DataSetNamesCount()
    {
        var result = await _handler.Handle(new GetDataSetNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DataSetNameVm>>();
        result.Count.ShouldBe(_dataSetFixture.DataSets.Count);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockDataSetRepository = DataSetRepositoryMocks.GetDataSetEmptyRepository();

        var handler = new GetDataSetNameQueryHandler(_dataSetFixture.Mapper, _mockDataSetRepository.Object);

        var result = await handler.Handle(new GetDataSetNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetDataSetNameQuery(), CancellationToken.None);

        _mockDataSetRepository.Verify(x => x.GetDataSetNames(), Times.Once);
    }
}