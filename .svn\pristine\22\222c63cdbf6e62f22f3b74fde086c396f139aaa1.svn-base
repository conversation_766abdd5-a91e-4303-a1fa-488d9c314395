﻿using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.InfraObject.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObject.Commands.Update;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Domain.ViewModels.ReplicationModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Domain.ViewModels.NodeModel;
using ContinuityPatrol.Application.Features.InfraObject.Events.PaginatedView;
using Newtonsoft.Json;
using System.Threading;


namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class InfraObjectController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IMapper _mapper;
    private readonly ILogger<InfraObjectController> _logger;
    private readonly IDataProvider _dataProvider;

    public InfraObjectController(IPublisher publisher, IMapper mapper, ILogger<InfraObjectController> logger, IDataProvider provider)
    {
        _publisher = publisher;
        _mapper = mapper;
        _logger = logger;
        _dataProvider = provider;
    }

    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in InfraObject");

        await _publisher.Publish(new InfraObjectPaginatedEvent());
        var list = await _dataProvider.InfraObject.GetInfraObjectList();
        var businessServiceList = await _dataProvider.BusinessService.GetBusinessServiceNames();
        var replicationServiceList = await _dataProvider.Replication.GetReplicationList();
        var databaseServiceList = await _dataProvider.Database.GetDatabaseList();
        var businessFunctionList = await _dataProvider.BusinessFunction.GetBusinessFunctionPaginatedList(new GetBusinessFunctionPaginatedListQuery());

        var databaseServiceName = databaseServiceList.Select(x => new DatabaseListVm
        {
            Id = x.Id,
            Name = x.Name,
            DatabaseType = x.DatabaseType
        }).ToList();       

        var replicationServiceNames = replicationServiceList.Select(x => new ReplicationListVm
        {
            Id = x.Id,
            Name = x.Name,
            Type = x.Type

        }).ToList();

        var infraViewModel = new InfraObjectViewModel
        {
            BusinessServices = businessServiceList,
            Database = databaseServiceName,          
            ReplicationNames = replicationServiceNames,
            InfraObjects = list
        };

        return View(infraViewModel);
    }


    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Configuration.CreateAndEdit)]
    public async Task<IActionResult> SaveOrUpdate(InfraObjectViewModel infraObject)
    {
        _logger.LogDebug("Entering SaveOrUpdate method in InfraObject");

        var infraId = Request.Form["Id"].ToString();

        try
        {
            if (infraId.IsNullOrWhiteSpace())
            {
                var createInfraObject = _mapper.Map<CreateInfraObjectCommand>(infraObject);

                var result = await _dataProvider.InfraObject.CreateAsync(createInfraObject);

                _logger.LogDebug($"Creating InfraObject '{createInfraObject.Name}'");

                TempData.NotifySuccess(result.Message);
            }
            else
            {
                var updateInfraObject = _mapper.Map<UpdateInfraObjectCommand>(infraObject);

                var result = await _dataProvider.InfraObject.UpdateAsync(updateInfraObject);

                _logger.LogDebug($"Updating InfraObject '{updateInfraObject.Name}'");

                TempData.NotifySuccess(result.Message);
            }

            _logger.LogDebug("SaveOrUpdate operation completed successfully in InfraObject, returning view.");

            return RedirectToAction("List");
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on infraObject page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on infraObject page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.Message);

            return RedirectToAction("List");
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetBusinessFunctions(string id)
    {
        _logger.LogDebug("Entering GetBusinessFunctions method in InfraObject");

        if (id.IsNullOrWhiteSpace())
        {
            _logger.LogDebug("Id is not valid format in GetBusinessFunctions method on infraObject page");

            return Json(new { Success = false, Message = "Id is not valid format", ErrorCode = 0 });
        }
        else
        {
            try
            {
                var businessFunctionNames = await _dataProvider.BusinessFunction.GetBusinessFunctionNamesByBusinessServiceId(id);

                _logger.LogDebug("Successfully retrieved business function names by businessServiceId on infraObject");

                return Json(new { Success = true, data = businessFunctionNames });
            }
            catch (Exception ex)
            {
                _logger.Exception("An error occurred on infraObject page while retrieving the businessFunction names by businessServiceId.", ex);

                return ex.GetJsonException();
            }
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetInfraObjectNames()
    {
        _logger.LogDebug("Entering GetInfraObjectNames method in InfraObject");

        try
        {
            var infraObjectNames = await _dataProvider.InfraObject.GetInfraObjectNames();

            _logger.LogDebug("Successfully retrieved infraObject names on InfraObject");

            return Json(new { Success = true, data = infraObjectNames });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on infraObject page while retrieving infraObject names.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetReplicationMasterByInfraMasterName(string infraMasterName)
    {
        _logger.LogDebug("Entering GetReplicationMasterByInfraMasterName method in InfraObject");

        if (infraMasterName.IsNullOrWhiteSpace())
        {
            _logger.LogDebug("InfraObject Replication Master name is invalid format on infraObject in GetReplicationMasterByInfraMasterName method");

            return Json(new { success = false, data = " InfraObject Replication Master is invalid format" });
        }

        try
        {
            var replicationMasterNames = await _dataProvider.ReplicationMaster.GetReplicationMasterByInfraMasterName(infraMasterName);

            _logger.LogDebug($"Successfully retrieved replication master by infraMaster name:{infraMasterName} on infraObject page");

            return Json(new { Success = true, data = replicationMasterNames });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on infraObject page while retrieving replication master by infraMaster name.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetServerRoleTypeAndServerType(string? roleType, string? serverType)
    {
        _logger.LogDebug("Entering GetServerRoleTypeAndServerType method in InfraObject");

        try
        {
            var serverRole = await _dataProvider.Server.GetByRoleTypeAndServerType(roleType, serverType);

            _logger.LogDebug($"Successfully retrieved server details by server roleType & serverType on InfraObject");

            return Json(new { Success = true, data = serverRole });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on infraObject page while retrieving the server by roleType or serverType.", ex);

            return ex.GetJsonException();
        }

    }


    [HttpGet]
    public async Task<JsonResult> GetDatabase(string id)
    {
        _logger.LogDebug("Entering GetDatabase method in InfraObject");

        if (id.IsNullOrWhiteSpace())
        {
            _logger.LogDebug("Id is not valid format in GetDatabase method on infraObject page");

            return Json(new { Success = false, Message = "Id is not valid format", ErrorCode = 0 });
        }
        else
        {
            try
            {
                var databaseList = await _dataProvider.Database.GetByServerId(id);

                _logger.LogDebug("Successfully retrieved database details by id");

                return Json(new { Success = true, data = databaseList });
            }
            catch (Exception ex)
            {
                _logger.Exception("An error occurred on infraObject page while retrieving the database details.", ex);

                return ex.GetJsonException();
            }
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetReplicationList()
    {
        _logger.LogDebug("Entering GetReplicationList method in InfraObject");

        try
        {
            var replicationList = await _dataProvider.Replication.GetReplicationList();

            _logger.LogDebug("Successfully retrieved replication list on infraObject page");

            return Json(new { Success = true, data = replicationList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on infraObject page while retrieving replication list.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetInfraObjectByBusinessServiceId(string businessServiceId)
    {
        _logger.LogDebug("Entering GetInfraObjectByBusinessServiceId method in InfraObject");
       
        if (businessServiceId.IsNullOrWhiteSpace())
        {
            _logger.LogDebug("Id is not valid format in GetInfraObjectByBusinessServiceId method on infraObject page");
            return Json(new { Success = false, data = "" });
        }
        else
        {
            try
            {
                var infraObjects = await _dataProvider.InfraObject.GetInfraObjectByBusinessServiceId(businessServiceId);
                _logger.LogDebug($"Successfully retrieved infraObject by businessServiceId {businessServiceId}");
                return Json(new { Success = true, data = infraObjects });
            }
            catch (Exception ex)
            {
                _logger.Exception("An error occurred on monitoring service page while deleting the record.", ex);
                return ex.GetJsonException();
            }
        }
    }
    public async Task<IActionResult> GetServersByInfraObjectId(string infraObjectId)
    {
        _logger.LogDebug("Entering GetServersByInfraObjectId method in InfraObject");

        try
        {
            var Servers = await _dataProvider.InfraObject.GetInfraObjectById(infraObjectId);

            //if (infraObject == null)
            //{
            //    _logger.LogDebug($"No InfraObject found for Id: {infraObjectId}");

            //    return Json(new { Success = false, data = {});
            //}

            //var deserialize = JsonConvert.DeserializeObject(infraObject.ServerProperties);

            //var servers = new list<selectlistitem>
            //{
            //    new() { value = deserialize.prserverid, text = deserialize.prservername },
            //    //new() { value = infraobject.drserverid, text = infraobject.drservername },
            //    //new() { value = infraobject.neardrserverid, text = infraobject.neardrservername }
            //};

            _logger.LogDebug($"Successfully retrieved servers for InfraObject Id: {infraObjectId}");

            return Json(new { Success = true, data = Servers });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on infraObject page while retrieving servers by infraObjectId.", ex);

            return ex.GetJsonException();
        }
    }


    [HttpGet]
    public async Task<bool> IsInfraObjectNameExist(string infraObjectName, string id)
    {
        _logger.LogDebug("Entering IsInfraObjectNameExist method in InfraObject");

        try
        {
            var nameExist = await _dataProvider.InfraObject.IsInfraObjectNameExist(infraObjectName, id);

            _logger.LogDebug("Returning result for IsInfraObjectNameExist on infraObject");

            return nameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on infraObject while checking if infraObject name exists for : {infraObjectName}.", ex);

            return false;
        }
    }

    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in InfraObject");

        try
        {
            var infra = await _dataProvider.InfraObject.DeleteAsync(id);

            _logger.LogDebug("Successfully deleted record in infraObject");

            TempData.NotifySuccess(infra.Message);

            return RedirectToAction("List");

        }
        catch (Exception ex)
        {
            TempData.NotifyWarning(ex.GetMessage());

            _logger.Exception("An error occurred while deleting record on infraObject.", ex);

            return RedirectToAction("List");
        }

    }

    [HttpGet]
    public async Task<JsonResult> GetPagination(GetInfraObjectPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in InfraObject");

        try
        {
            _logger.LogDebug("Successfully retrieved infraObject paginated list on infraObject page");

            return Json(await _dataProvider.InfraObject.GetPaginatedInfraObjects(query));
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on infraObject page while processing the pagination request. {ex.GetJsonException()}");

            return ex.GetJsonException();
        }
    }
    public async Task<JsonResult> GetDatabaseListByName()
    {
        _logger.LogDebug("Entering GetDatabaseListByName method in InfraObject");

        try
        {
            var result = await _dataProvider.ComponentType.GetComponentTypeListByName("Database");

            _logger.LogDebug("Successfully retrieved database names on infraObject page");

            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on infraObject while retrieving database names.", ex);

            return ex.GetJsonException();
        }

    }

    [HttpGet]
    public async Task<JsonResult> GetTypeByDatabaseIdAndReplicationMasterId(string? databaseId, string replicationMasterId, string type)
    {
        _logger.LogDebug("Entering GetTypeByDatabaseIdAndReplicationMasterId method in InfraObject");

        try
        {
            var replicationNames = await _dataProvider.InfraReplicationMapping.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type);

            _logger.LogDebug("Successfully retrieved serverType by databaseId & replicationMasterId & type in InfraObject");

            return Json(new { Success = true, data = replicationNames });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on infraObject page while retrieving serverType by id & type.", ex);

            return ex.GetJsonException();
        }

    }

    [HttpGet]
    public async Task<IActionResult> SitePropertiesByBusinessService(string businessServiceId)
    {
        _logger.LogDebug("Entering SitePropertiesByBusinessService method in InfraObject");

        if (businessServiceId.IsNullOrWhiteSpace())
        {
            _logger.LogDebug("Id is not valid format in SitePropertiesByBusinessService method on infraObject page");

            return Json(new { Success = false, Message = "Id is not valid format", ErrorCode = 0 });
        }
        else
        {
            try
            {
                var siteDetails = await _dataProvider.DashboardView.GetSitePropertiesByBusinessServiceId(businessServiceId);

                _logger.LogDebug("Successfully retrieved site properties on InfraObject page");

                return Json(new { Success = true, data = siteDetails });
            }
            catch (Exception ex)
            {
                _logger.Exception("An error occurred on infraObject page while retrieving site properties.", ex);

                return ex.GetJsonException();
            }
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetSiteTypeDetails(string siteId)
    {
        _logger.LogDebug("Entering GetSiteTypeDetails method in InfraObject");

        if (siteId.IsNullOrWhiteSpace())
        {
            _logger.LogDebug("Id is not valid format in GetSiteTypeDetails method on infraObject page");

            return Json(new { Success = false, Message = "Id is not valid format", ErrorCode = 0 });
        }

        try
        {
            var siteList = await _dataProvider.Site.GetSiteById(siteId);

            _logger.LogDebug("Successfully retrieved site type by siteId on InfraObject page");

            return Json(new { Success = true, data = siteList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on infraObject page while retrieving siteType by siteId.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetVeritasClusters()
    {
        _logger.LogDebug("Entering GetVeritasClusters method in InfraObject");

        try
        {
            var veritasList = await _dataProvider.VeritasCluster.GetVeritasClusterList();

            _logger.LogDebug("Successfully retrieved veritasCluster list on InfraObject page");

            return Json(new { Success = true, data = veritasList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on infraObject page while retrieving veritasCluster list.", ex);

            return ex.GetJsonException();
        }

    }

    [HttpGet]
    public async Task<JsonResult> GetHACMPClusters()
    {
        _logger.LogDebug("Entering GetHACMPClusters method in InfraObject");

        try
        {
            var hacmpVm = await _dataProvider.HacmpCluster.GetHacmpClusterList();

            _logger.LogDebug("Successfully retrieved hacmpCluster list on InfraObject page");

            return Json(new { Success = true, data = hacmpVm });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on infraObject page while retrieving hacmpCluster list.", ex);

            return ex.GetJsonException();
        }
    }
}