﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.BusinessService.Events.Create;

public class BusinessServiceCreatedEventHandler : INotificationHandler<BusinessServiceCreatedEvent>
{
    private readonly ILogger<BusinessServiceCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public BusinessServiceCreatedEventHandler(ILoggedInUserService userService,
        ILogger<BusinessServiceCreatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(BusinessServiceCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.OperationalService.ToString(),
            Action = $"{ActivityType.Create} {Modules.OperationalService}",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"OperationalService '{createdEvent.BusinessServiceName}' created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"OperationalService '{createdEvent.BusinessServiceName}' created successfully.");
    }
}