﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.InfraObject.Events.PaginatedView;

public class InfraObjectPaginatedEventHandler : INotificationHandler<InfraObjectPaginatedEvent>
{
    private readonly ILogger<InfraObjectPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public InfraObjectPaginatedEventHandler(ILoggedInUserService userService,
        ILogger<InfraObjectPaginatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(InfraObjectPaginatedEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.InfraObject.ToString(),
            Action = $"{ActivityType.View} {Modules.InfraObject}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = " InfraObject viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("InfraObject viewed");
    }
}