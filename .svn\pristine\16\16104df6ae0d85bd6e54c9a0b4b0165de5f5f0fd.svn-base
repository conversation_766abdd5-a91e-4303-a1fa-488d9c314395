﻿using ContinuityPatrol.Application.Features.SmsConfiguration.Events.Update;

namespace ContinuityPatrol.Application.Features.SmsConfiguration.Commands.Update;

public class
    UpdateSmsConfigurationCommandHandler : IRequestHandler<UpdateSmsConfigurationCommand,
        UpdateSmsConfigurationResponse>
{
    private readonly IMapper _mapper;
    private readonly ISmsConfigurationRepository _smsConfigurationRepository;
    private readonly IPublisher _publisher;

    public UpdateSmsConfigurationCommandHandler(IMapper mapper, ISmsConfigurationRepository smsConfigurationRepository,IPublisher publisher)
    {
        _mapper = mapper;
        _smsConfigurationRepository = smsConfigurationRepository;
        _publisher = publisher;
    }

    public async Task<UpdateSmsConfigurationResponse> Handle(UpdateSmsConfigurationCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _smsConfigurationRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.SmsConfiguration), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateSmsConfigurationCommand),
            typeof(Domain.Entities.SmsConfiguration));

        await _smsConfigurationRepository.UpdateAsync(eventToUpdate);

        await _publisher.Publish(new SmsConfigurationUpdatedEvent { UserName = eventToUpdate.UserName }, cancellationToken);

        var response = new UpdateSmsConfigurationResponse
        {
            Message = Message.Update("SMS Configuration", eventToUpdate.UserName),

            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}