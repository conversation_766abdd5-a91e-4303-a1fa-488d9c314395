﻿using AutoFixture;
using ContinuityPatrol.Application.Features.TeamMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.TeamResource.Commands.Create;
using ContinuityPatrol.Application.Features.TeamResource.Events.PaginatedView;
using ContinuityPatrol.Application.Features.TeamResource.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.User.Queries.GetDetail;
using ContinuityPatrol.Application.Features.User.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.UserInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.SmtpConfigurationModel;
using ContinuityPatrol.Domain.ViewModels.TeamResourceModel;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Mocks;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;


namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class TeamResourceControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IEmailService> _mockEmailService = new();
        private readonly Mock<Microsoft.Extensions.Logging.ILogger<TeamResourceController>> _mocklogger = new();
        private TeamResourceController _controller;



        public TeamResourceControllerShould()
        {

            _controller = new TeamResourceController(
                _mockPublisher.Object,
                _mockMapper.Object,
                _mockDataProvider.Object,
				_mocklogger.Object
                //_mockEmailService.Object
            );



            _controller.ControllerContext.HttpContext = new DefaultHttpContext();

        }

        [Fact]
        public async Task List_ReturnsViewResult_WithTeamResourceViewModel()
        {
            var team = new TeamMasterDetailVm
            {
                GroupName = "Team Name"
            };
            var teamId = "team123";
            var teamName = "Team Name";
            var userlist = new PaginatedResult<UserViewListVm>();


            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
            _controller.HttpContext.Session.SetString("GroupName", teamName);
            _controller.HttpContext.Session.SetString("TeamMasterId", teamId);

            _mockDataProvider.Setup(dp => dp.TeamMasterService.GetTeamNameById(teamId))
                .ReturnsAsync(team);
            _mockDataProvider.Setup(dp => dp.User.GetUserPaginatedList(It.IsAny<GetUserPaginatedListQuery>()))
                .ReturnsAsync(userlist);


            var result = await _controller.List(teamId) as ViewResult;
            var model = result?.Model as TeamResourceViewModel;


            Assert.NotNull(result);
            Assert.NotNull(model);
            // Assert.Equal(userlist, model.PaginatedUserList); // Commented out as PaginatedUserList is commented in production
            Assert.Equal(teamName, _controller.ViewBag.TeamMasterName);
        }

        [Fact]
        public async Task _UserListCheckedd_CreatesTeamResource()
        {


            var teamId = "team123";
            var teamName = "Team Name";
            var checkUserlist = "resourceId+resourceName+resourceEmail+resourcePhone";

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);

            _controller.HttpContext.Session.SetString("TeamMasterId", teamId);
            _controller.HttpContext.Session.SetString("GroupName", teamName);

            _mockMapper.Setup(m => m.Map<CreateTeamResourceCommand>(It.IsAny<TeamResource>()))
                .Returns(new CreateTeamResourceCommand());


            _mockDataProvider.Setup(dp => dp.TeamResourceService.CreateAsync(It.IsAny<CreateTeamResourceCommand>()))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Resource Created Successfully" });


            var result = await _controller.UserListChecked(checkUserlist);


            Assert.Equal(teamId, result);
            _mockDataProvider.Verify(dp => dp.TeamResourceService.CreateAsync(It.IsAny<CreateTeamResourceCommand>()), Times.Once);


        }

        [Fact]
        public async Task Delete_ReturnsRedirectToActionResult()
        {

            var teamId = "team123";


            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);

            _controller.HttpContext.Session.SetString("TeamMasterId", teamId);

            _mockDataProvider.Setup(dp => dp.TeamResourceService.DeleteAsync(teamId))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "deleted" });


            var result = await _controller.Delete(teamId) as RedirectToActionResult;


            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            Assert.Equal("TeamResource", result.ControllerName);
            Assert.Equal(teamId, result.RouteValues["Id"]);
        }

        [Fact]
        public async Task SendMail_SendsEmail_ReturnsJsonResult()
        {

            var emailId = "<EMAIL>";
            var userDetails = new UserDetailVm
            {
                UserInfo = new UserInfoDetailVm
                {
                    Email = "<EMAIL>"
                }
            };

            var smtpDetails = new SmtpConfigurationListVm { SmtpHost = "host", Port = "25", EnableSSL = true };

            _mockDataProvider.Setup(dp => dp.User.GetByReferenceId(It.IsAny<string>()))
                .ReturnsAsync(userDetails);
            _mockDataProvider.Setup(dp => dp.SmtpConfiguration.GetSmtpConfigurationList())
                .ReturnsAsync(smtpDetails);
            _mockEmailService.Setup(es => es.SendEmail(It.IsAny<EmailDto>()))
                .ReturnsAsync(true);

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var userSessions = new List<UserSession> { new AutoFixture.Fixture().Create<UserSession>() };
            WebHelper.CurrentSession.Set("SESSION", userSessions);



            var result = await _controller.SendMail(emailId) as JsonResult;


            Assert.NotNull(result);
            Assert.Equal("Success", result.Value);
        }

        [Fact]
        public async Task SendSms_ReturnsJsonResult()
        {

            var phoneNumber = "**********";


            var result = await _controller.SendSms(phoneNumber) as JsonResult;


            Assert.NotNull(result);
            Assert.Equal("Success", result.Value);
        }

        // Additional test cases for 100% coverage

        [Fact]
        public async Task List_WithNullOrEmptyId_ReturnsViewWithoutTeamData()
        {
            // Arrange
            var userlist = new PaginatedResult<UserViewListVm>();

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);

            _mockDataProvider.Setup(dp => dp.User.GetUserPaginatedList(It.IsAny<GetUserPaginatedListQuery>()))
                .ReturnsAsync(userlist);

            // Act
            var result = await _controller.List(null) as ViewResult;
            var model = result?.Model as TeamResourceViewModel;

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(model);
            _mockDataProvider.Verify(dp => dp.TeamMasterService.GetTeamNameById(It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task List_WithEmptyStringId_ReturnsViewWithoutTeamData()
        {
            // Arrange
            var userlist = new PaginatedResult<UserViewListVm>();

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);

            _mockDataProvider.Setup(dp => dp.User.GetUserPaginatedList(It.IsAny<GetUserPaginatedListQuery>()))
                .ReturnsAsync(userlist);

            // Act
            var result = await _controller.List("") as ViewResult;
            var model = result?.Model as TeamResourceViewModel;

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(model);
            _mockDataProvider.Verify(dp => dp.TeamMasterService.GetTeamNameById(It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task List_PublishesTeamResourcePaginatedEvent()
        {
            // Arrange
            var userlist = new PaginatedResult<UserViewListVm>();

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);

            _mockDataProvider.Setup(dp => dp.User.GetUserPaginatedList(It.IsAny<GetUserPaginatedListQuery>()))
                .ReturnsAsync(userlist);

            // Act
            var result = await _controller.List(null);

            // Assert
            _mockPublisher.Verify(p => p.Publish(It.IsAny<TeamResourcePaginatedEvent>(), default), Times.Once);
        }

        [Fact]
        public async Task UserListChecked_WithValidUserList_ReturnsTeamId()
        {
            // Arrange
            var teamId = "team123";
            var teamName = "Team Name";
            var checkUserlist = "<EMAIL>+**********"; // Valid format: id+name+email+phone

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);

            _controller.HttpContext.Session.SetString("TeamMasterId", teamId);
            _controller.HttpContext.Session.SetString("GroupName", teamName);

            // Setup TeamResourceService mock
            var mockTeamResourceService = new Mock<ITeamResourceService>();
            mockTeamResourceService.Setup(s => s.CreateAsync(It.IsAny<CreateTeamResourceCommand>()))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Resource Created Successfully" });
            _mockDataProvider.Setup(dp => dp.TeamResourceService).Returns(mockTeamResourceService.Object);

            // Act
            var result = await _controller.UserListChecked(checkUserlist);

            // Assert
            Assert.Equal(teamId, result);
            mockTeamResourceService.Verify(s => s.CreateAsync(It.IsAny<CreateTeamResourceCommand>()), Times.Once);
        }

        [Fact]
        public async Task UserListChecked_WithValidUserListAndEmptyEmail_ReturnsTeamId()
        {
            // Arrange
            var teamId = "team123";
            var teamName = "Team Name";
            var checkUserlist = "resourceId+resourceName++**********"; // Valid format with empty email

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);

            _controller.HttpContext.Session.SetString("TeamMasterId", teamId);
            _controller.HttpContext.Session.SetString("GroupName", teamName);

            // Setup TeamResourceService mock
            var mockTeamResourceService = new Mock<ITeamResourceService>();
            mockTeamResourceService.Setup(s => s.CreateAsync(It.IsAny<CreateTeamResourceCommand>()))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Resource Created Successfully" });
            _mockDataProvider.Setup(dp => dp.TeamResourceService).Returns(mockTeamResourceService.Object);

            // Act
            var result = await _controller.UserListChecked(checkUserlist);

            // Assert
            Assert.Equal(teamId, result);
            mockTeamResourceService.Verify(s => s.CreateAsync(It.IsAny<CreateTeamResourceCommand>()), Times.Once);
        }

        [Fact]
        public async Task UserListChecked_WithNullEmailAndPhone_SetsNAValues()
        {
            // Arrange
            var teamId = "team123";
            var teamName = "Team Name";
            var checkUserlist = "resourceId+resourceName++"; // Empty email and phone

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);

            _controller.HttpContext.Session.SetString("TeamMasterId", teamId);
            _controller.HttpContext.Session.SetString("GroupName", teamName);

            TeamResource? capturedTeamResource = null;
            _mockMapper.Setup(m => m.Map<CreateTeamResourceCommand>(It.IsAny<object>()))
                .Callback<object>(obj => capturedTeamResource = obj as TeamResource)
                .Returns(new CreateTeamResourceCommand());

            // Setup TeamResourceService mock
            var mockTeamResourceService = new Mock<ITeamResourceService>();
            mockTeamResourceService.Setup(s => s.CreateAsync(It.IsAny<CreateTeamResourceCommand>()))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Resource Created Successfully" });
            _mockDataProvider.Setup(dp => dp.TeamResourceService).Returns(mockTeamResourceService.Object);

            // Act
            var result = await _controller.UserListChecked(checkUserlist);

            // Assert
            Assert.Equal(teamId, result);
            Assert.NotNull(capturedTeamResource);
            Assert.Equal("NA", capturedTeamResource.Email);
            Assert.Equal("NA", capturedTeamResource.Phone);
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonResult()
        {
            // Arrange
            var teamName = "Team Name";
            var query = new GetTeamResourcePaginatedListQuery();
            var teamMemberList = new List<TeamResourceListVm>
            {
                new TeamResourceListVm { ResourceName = "User1", Email = "<EMAIL>" }
            };

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);

            _controller.HttpContext.Session.SetString("GroupName", teamName);

            _mockDataProvider.Setup(dp => dp.TeamResourceService.GetTeamMemberListAll(teamName))
                .Returns(Task.FromResult(new PaginatedResult<TeamResourceListVm> { Data = teamMemberList }));

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var paginatedResult = Assert.IsType<PaginatedResult<TeamResourceListVm>>(result.Value);
            Assert.Equal(teamMemberList, paginatedResult.Data);
        }

        [Fact]
        public async Task GetPagination_WithNullTeamName_ReturnsJsonResult()
        {
            // Arrange
            var query = new GetTeamResourcePaginatedListQuery();
            var teamMemberList = new List<TeamResourceListVm>();

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);

            // No team name set in session

            _mockDataProvider.Setup(dp => dp.TeamResourceService.GetTeamMemberListAll(null))
                .Returns(Task.FromResult(new PaginatedResult<TeamResourceListVm> { Data = teamMemberList }));

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var paginatedResult = Assert.IsType<PaginatedResult<TeamResourceListVm>>(result.Value);
            Assert.Equal(teamMemberList, paginatedResult.Data);
        }

        [Fact]
        public async Task Delete_WithException_ThrowsException()
        {
            // Arrange
            var teamId = "team123";
            var resourceId = "resource123";

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);

            _controller.HttpContext.Session.SetString("TeamMasterId", teamId);

            _mockDataProvider.Setup(dp => dp.TeamResourceService.DeleteAsync(resourceId))
                .ThrowsAsync(new Exception("Delete failed"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _controller.Delete(resourceId));
        }

        [Fact]
        public async Task SendMail_WithException_ThrowsException()
        {
            // Arrange
            var emailId = "<EMAIL>";

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var userSessions = new List<UserSession> { new AutoFixture.Fixture().Create<UserSession>() };
            WebHelper.CurrentSession.Set("SESSION", userSessions);

            _mockDataProvider.Setup(dp => dp.User.GetByReferenceId(It.IsAny<string>()))
                .ThrowsAsync(new Exception("User service failed"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _controller.SendMail(emailId));
        }

        [Fact]
        public async Task UserListChecked_WithException_ThrowsException()
        {
            // Arrange
            var teamId = "team123";
            var teamName = "Team Name";
            var checkUserlist = "resourceId+resourceName+resourceEmail+resourcePhone";

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);

            _controller.HttpContext.Session.SetString("TeamMasterId", teamId);
            _controller.HttpContext.Session.SetString("GroupName", teamName);

            _mockMapper.Setup(m => m.Map<CreateTeamResourceCommand>(It.IsAny<TeamResource>()))
                .Returns(new CreateTeamResourceCommand());

            _mockDataProvider.Setup(dp => dp.TeamResourceService.CreateAsync(It.IsAny<CreateTeamResourceCommand>()))
                .ThrowsAsync(new Exception("Create failed"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _controller.UserListChecked(checkUserlist));
        }

        [Fact]
        public async Task List_WithException_ThrowsException()
        {
            // Arrange
            var teamId = "team123";

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);

            _mockDataProvider.Setup(dp => dp.TeamMasterService.GetTeamNameById(teamId))
                .ThrowsAsync(new Exception("Team service failed"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _controller.List(teamId));
        }

        [Fact]
        public async Task GetPagination_WithException_ThrowsException()
        {
            // Arrange
            var teamName = "Team Name";
            var query = new GetTeamResourcePaginatedListQuery();

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);

            _controller.HttpContext.Session.SetString("GroupName", teamName);

            _mockDataProvider.Setup(dp => dp.TeamResourceService.GetTeamMemberListAll(teamName))
                .ThrowsAsync(new Exception("Pagination service failed"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _controller.GetPagination(query));
        }

        [Fact]
        public async Task SendSms_WithException_DoesNotThrow()
        {
            // Arrange
            var phoneNumber = "**********";

            // Act
            var result = await _controller.SendSms(phoneNumber) as JsonResult;

            // Assert - SendSms is a simple method that just returns success, no exceptions expected
            Assert.NotNull(result);
            Assert.Equal("Success", result.Value);
        }

        [Fact]
        public async Task UserListChecked_WithValidEmailAndPhone_SetsCorrectValues()
        {
            // Arrange
            var teamId = "team123";
            var teamName = "Team Name";
            var checkUserlist = "<EMAIL>+**********";

            _controller.ControllerContext = new ControllerContextMocks().Default();

            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);

            _controller.HttpContext.Session.SetString("TeamMasterId", teamId);
            _controller.HttpContext.Session.SetString("GroupName", teamName);

            TeamResource? capturedTeamResource = null;
            _mockMapper.Setup(m => m.Map<CreateTeamResourceCommand>(It.IsAny<object>()))
                .Callback<object>(obj => capturedTeamResource = obj as TeamResource)
                .Returns(new CreateTeamResourceCommand());

            // Setup TeamResourceService mock
            var mockTeamResourceService = new Mock<ITeamResourceService>();
            mockTeamResourceService.Setup(s => s.CreateAsync(It.IsAny<CreateTeamResourceCommand>()))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Resource Created Successfully" });
            _mockDataProvider.Setup(dp => dp.TeamResourceService).Returns(mockTeamResourceService.Object);

            // Act
            var result = await _controller.UserListChecked(checkUserlist);

            // Assert
            Assert.Equal(teamId, result);
            Assert.NotNull(capturedTeamResource);
            Assert.Equal("<EMAIL>", capturedTeamResource.Email);
            Assert.Equal("**********", capturedTeamResource.Phone);
            Assert.Equal("resourceId", capturedTeamResource.ResourceId);
            Assert.Equal("resourceName", capturedTeamResource.ResourceName);
            Assert.Equal(teamName, capturedTeamResource.TeamMasterName);
            Assert.Equal(teamId, capturedTeamResource.TeamMasterId);
        }
    }
}
