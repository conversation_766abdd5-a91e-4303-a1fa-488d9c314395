﻿@using ContinuityPatrol.Shared.Services.Helper;
@model ContinuityPatrol.Domain.ViewModels.SiteTypeModel.SiteTypeListModel
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title" ><i class="cp-web"></i><span>Site Type </span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="siteTypeSearch" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown"  title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown" >
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="name=" id="stNameFilter">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="type=" id="stypeFilter">
                                        <label class="form-check-label" for="Type">
                                            Type
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm" id="siteTypeCreateButton"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="siteTypeTable" class="table table-hover dataTable" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Name</th>
                        <th>Type</th>                       
                        <th class="Action-th " style="width:7%">Action</th>
                    </tr>
                </thead>
                <tbody>                    
                </tbody>
            </table>
        </div>
    </div>
</div>
<div id="siteTypeConfigCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true"></div>
<div id="siteTypeConfigDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true"></div>

<!--Modal create-->
<div class="modal fade" id="siteTypeCreateModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Configuration" />
</div>
<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="siteTypeDeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Configuration//Sites/Site Type/siteType.js"></script>