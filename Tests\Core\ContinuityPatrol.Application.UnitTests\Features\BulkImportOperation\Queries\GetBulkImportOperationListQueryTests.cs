using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationModel;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperation.Queries;

public class GetBulkImportOperationListQueryTests : IClassFixture<BulkImportOperationFixture>
{
    private readonly BulkImportOperationFixture _bulkImportOperationFixture;
    private readonly Mock<IBulkImportOperationRepository> _mockBulkImportOperationRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetBulkImportOperationListQueryHandler _handler;

    public GetBulkImportOperationListQueryTests(BulkImportOperationFixture bulkImportOperationFixture)
    {
        _bulkImportOperationFixture = bulkImportOperationFixture;

        _mockBulkImportOperationRepository = BulkImportOperationRepositoryMocks.CreateQueryBulkImportOperationRepository(_bulkImportOperationFixture.BulkImportOperations);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<List<BulkImportOperationListVm>>(It.IsAny<List<Domain.Entities.BulkImportOperation>>()))
            .Returns((List<Domain.Entities.BulkImportOperation> entities) => entities.Select(entity => new BulkImportOperationListVm
            {
                Id = entity.ReferenceId,
                CompanyId = entity.CompanyId,
                UserName = entity.UserName,
                Description = entity.Description,
                Status = entity.Status,
                StartTime = entity.StartTime,
                EndTime = entity.EndTime
            }).ToList());

        _handler = new GetBulkImportOperationListQueryHandler(
            _mockMapper.Object,
            _mockBulkImportOperationRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_BulkImportOperationListVm_When_BulkImportOperationsExist()
    {
        // Arrange
        var query = new GetBulkImportOperationListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportOperationListVm>));
        result.Count.ShouldBeGreaterThan(0);
        result.First().Id.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_Call_ListAllAsync_OnlyOnce()
    {
        // Arrange
        var query = new GetBulkImportOperationListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var query = new GetBulkImportOperationListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<List<BulkImportOperationListVm>>(It.IsAny<List<Domain.Entities.BulkImportOperation>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoBulkImportOperationsExist()
    {
        // Arrange
        var query = new GetBulkImportOperationListQuery();
        _mockBulkImportOperationRepository.Setup(x => x.ListAllAsync())
            .ReturnsAsync(new List<Domain.Entities.BulkImportOperation>());

        _mockMapper.Setup(m => m.Map<List<BulkImportOperationListVm>>(It.IsAny<List<Domain.Entities.BulkImportOperation>>()))
            .Returns(new List<BulkImportOperationListVm>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportOperationListVm>));
        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_MapEntitiesToViewModels_WithCorrectProperties()
    {
        // Arrange
        var testOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        testOperation.CompanyId = "TestCompanyId";
        testOperation.UserName = "TestUser";
        testOperation.Description = "Test bulk import operation";
        testOperation.Status = "Pending";
        testOperation.StartTime = DateTime.Now.AddHours(-1);
        testOperation.EndTime = DateTime.Now;

        var query = new GetBulkImportOperationListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBeGreaterThan(0);
        
        var firstItem = result.First();
        firstItem.Id.ShouldBe(testOperation.ReferenceId);
        firstItem.CompanyId.ShouldBe("TestCompanyId");
        firstItem.UserName.ShouldBe("TestUser");
        firstItem.Description.ShouldBe("Test bulk import operation");
        firstItem.Status.ShouldBe("Pending");
        firstItem.StartTime.ShouldBe(testOperation.StartTime);
        firstItem.EndTime.ShouldBe(testOperation.EndTime);
    }

    [Fact]
    public async Task Handle_ReturnCorrectListType_When_MappingSuccessful()
    {
        // Arrange
        var query = new GetBulkImportOperationListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<List<BulkImportOperationListVm>>();
        result.GetType().ShouldBe(typeof(List<BulkImportOperationListVm>));
    }

    [Fact]
    public async Task Handle_ReturnAllActiveItems_When_RepositoryHasData()
    {
        // Arrange
        var query = new GetBulkImportOperationListQuery();
        var expectedCount = _bulkImportOperationFixture.BulkImportOperations.Count;

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(expectedCount);
    }

    [Fact]
    public async Task Handle_NotCallMapper_When_NoDataExists()
    {
        // Arrange
        var query = new GetBulkImportOperationListQuery();
        _mockBulkImportOperationRepository.Setup(x => x.ListAllAsync())
            .ReturnsAsync(new List<Domain.Entities.BulkImportOperation>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(0);
        _mockMapper.Verify(x => x.Map<List<BulkImportOperationListVm>>(It.IsAny<List<Domain.Entities.BulkImportOperation>>()), Times.Never);
    }

    [Fact]
    public async Task Handle_MapStatusAndTimes_WithCorrectValues()
    {
        // Arrange
        var testOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        testOperation.Status = "Running";
        testOperation.StartTime = DateTime.Now.AddHours(-2);
        testOperation.EndTime = DateTime.Now;

        var query = new GetBulkImportOperationListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        var firstItem = result.First();
        firstItem.Status.ShouldBe("Running");
        firstItem.StartTime.ShouldBe(testOperation.StartTime);
        firstItem.EndTime.ShouldBe(testOperation.EndTime);
    }

    [Fact]
    public async Task Handle_MapUserAndCompanyInfo_WithCorrectValues()
    {
        // Arrange
        var testOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        testOperation.CompanyId = "Company123";
        testOperation.UserName = "ProductionUser";
        testOperation.Description = "Production bulk import";

        var query = new GetBulkImportOperationListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        var firstItem = result.First();
        firstItem.CompanyId.ShouldBe("Company123");
        firstItem.UserName.ShouldBe("ProductionUser");
        firstItem.Description.ShouldBe("Production bulk import");
    }

    [Fact]
    public async Task Handle_ReturnEmptyListDirectly_When_CountIsZero()
    {
        // Arrange
        var query = new GetBulkImportOperationListQuery();
        _mockBulkImportOperationRepository.Setup(x => x.ListAllAsync())
            .ReturnsAsync(new List<Domain.Entities.BulkImportOperation>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportOperationListVm>));
        result.Count.ShouldBe(0);
        result.ShouldBeEmpty();
    }

    [Fact]
    public async Task Handle_HandleMultipleOperations_When_RepositoryHasMultipleItems()
    {
        // Arrange
        var query = new GetBulkImportOperationListQuery();
        var multipleOperations = new List<Domain.Entities.BulkImportOperation>
        {
            new Domain.Entities.BulkImportOperation { ReferenceId = "1", UserName = "User1", Status = "Pending" },
            new Domain.Entities.BulkImportOperation { ReferenceId = "2", UserName = "User2", Status = "Running" },
            new Domain.Entities.BulkImportOperation { ReferenceId = "3", UserName = "User3", Status = "Completed" }
        };

        _mockBulkImportOperationRepository.Setup(x => x.ListAllAsync())
            .ReturnsAsync(multipleOperations);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(3);
        result.Select(x => x.Status).ShouldContain("Pending");
        result.Select(x => x.Status).ShouldContain("Running");
        result.Select(x => x.Status).ShouldContain("Completed");
    }
}
