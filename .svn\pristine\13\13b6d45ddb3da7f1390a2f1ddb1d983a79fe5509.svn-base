﻿using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.PostgresMonitorLogsModel;

namespace ContinuityPatrol.Application.UnitTests.Features.PostgresMonitorLogs.Queries
{
    public class GetPostgresMonitorLogsListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPostgresMonitorLogsRepository> _mockPostgresMonitorLogsRepository;
        private readonly GetPostgresMonitorLogsListQueryHandler _handler;

        public GetPostgresMonitorLogsListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockPostgresMonitorLogsRepository = new Mock<IPostgresMonitorLogsRepository>();
            _handler = new GetPostgresMonitorLogsListQueryHandler(_mockPostgresMonitorLogsRepository.Object, _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedLogsList_WhenLogsExist()
        {
            var query = new GetPostgresMonitorLogsListQuery();

            var logsFromRepo = new List<Domain.Entities.PostgresMonitorLogs>
            {
                new Domain.Entities.PostgresMonitorLogs { ReferenceId = Guid.NewGuid().ToString(), Type = "Active" },
                new Domain.Entities.PostgresMonitorLogs { ReferenceId = Guid.NewGuid().ToString(), Type = "InActive" }
            };

            var expectedVmList = new List<PostgresMonitorLogsListVm>
            {
                new PostgresMonitorLogsListVm { Id = Guid.NewGuid().ToString(), ConfiguredRPO = "55" },
                new PostgresMonitorLogsListVm { Id = Guid.NewGuid().ToString(), ConfiguredRPO = "75" }
            };
            _mockPostgresMonitorLogsRepository.Setup(r => r.ListAllAsync())
                .ReturnsAsync(logsFromRepo);

            _mockMapper.Setup(m => m.Map<List<PostgresMonitorLogsListVm>>(logsFromRepo))
                .Returns(expectedVmList);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("Log1", result[0].Id);
            Assert.Equal("Detail1", result[0].Type);

            _mockPostgresMonitorLogsRepository.Verify(r => r.ListAllAsync(), Times.Once);
            _mockMapper.Verify(m => m.Map<List<PostgresMonitorLogsListVm>>(logsFromRepo), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoLogsExist()
        {
            var query = new GetPostgresMonitorLogsListQuery();

            _mockPostgresMonitorLogsRepository.Setup(r => r.ListAllAsync())
                .ReturnsAsync(new List<Domain.Entities.PostgresMonitorLogs>());

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);

            _mockPostgresMonitorLogsRepository.Verify(r => r.ListAllAsync(), Times.Once);
            _mockMapper.Verify(m => m.Map<List<PostgresMonitorLogsListVm>>(It.IsAny<List<Domain.Entities.PostgresMonitorLogs>>()), Times.Never);
        }
    }
}
