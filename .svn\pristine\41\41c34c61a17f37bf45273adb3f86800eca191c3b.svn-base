﻿namespace ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator;

public class CreateBulkImportValidatorCommandResponse
{
    public List<CreateBulkImportValidatorCommandResponseDetail> ValidatorResponse { get; set; } = new();
}

public class CreateBulkImportValidatorCommandResponseDetail
{
    public string InfraObjectName { get; set; }
    public List<ValidationDetail> ServerCommand { get; set; } = new();
    public List<ValidationDetail> DatabaseCommand { get; set; } = new();
    public List<ValidationDetail> ReplicationCommand { get; set; } = new();
    public List<ValidationDetail> InfraObjectCommand { get; set; } = new();
    public bool IsSwitchOver { get; set; }
    public string SwitchOverTemplate { get; set; }
    public bool IsFailOver { get; set; }
    public string FailOverTemplate { get; set; }
    public bool IsSwitchBack { get; set; }
    public string SwitchBackTemplate { get; set; }
    public bool IsFailBack { get; set; }
    public string FailBackTemplate { get; set; }
}

public class ValidationDetail
{
    public string PropertyName { get; set; }
    public string Name { get; set; }
    public string Exception { get; set; }
}