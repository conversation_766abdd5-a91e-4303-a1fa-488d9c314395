using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DrReadyFixture : IDisposable
{
    public List<DrReady> DrReadyPaginationList { get; set; }
    public List<DrReady> DrReadyList { get; set; }
    public DrReady DrReadyDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string BusinessServiceId = "BS_123";

    public ApplicationDbContext DbContext { get; private set; }

    public DrReadyFixture()
    {
        var fixture = new Fixture();

        DrReadyList = fixture.Create<List<DrReady>>();

        DrReadyPaginationList = fixture.CreateMany<DrReady>(20).ToList();

        DrReadyPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DrReadyPaginationList.ForEach(x => x.IsActive = true);
        DrReadyPaginationList.ForEach(x => x.BusinessServiceId = BusinessServiceId);

        DrReadyList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DrReadyList.ForEach(x => x.IsActive = true);
        DrReadyList.ForEach(x => x.BusinessServiceId = BusinessServiceId);

        DrReadyDto = fixture.Create<DrReady>();
        DrReadyDto.ReferenceId = Guid.NewGuid().ToString();
        DrReadyDto.IsActive = true;
        DrReadyDto.BusinessServiceId = BusinessServiceId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
