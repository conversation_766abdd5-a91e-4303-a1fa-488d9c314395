﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Replication.Events.Update;

public class ReplicationUpdatedEventHandler : INotificationHandler<ReplicationUpdatedEvent>
{
    private readonly ILogger<ReplicationUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ReplicationUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<ReplicationUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ReplicationUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress ?? "::1",
            Entity = Modules.Replication.ToString(),
            Action = $"{ActivityType.Update} {Modules.Replication}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Replication '{updatedEvent.ReplicationName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Replication '{updatedEvent.ReplicationName}' updated successfully.");
    }
}