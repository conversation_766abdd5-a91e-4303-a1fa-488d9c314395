﻿using ContinuityPatrol.Application.Features.ReportSchedule.Commands.Create;
using ContinuityPatrol.Application.Features.ReportSchedule.Commands.Delete;
using ContinuityPatrol.Application.Features.ReportSchedule.Commands.Update;
using ContinuityPatrol.Application.Features.ReportSchedule.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ReportSchedule.Queries.GetScheculeExecutionDetails;
using ContinuityPatrol.Application.Features.ReportSchedule.Queries.GetNames;
using ContinuityPatrol.Application.Features.ReportSchedule.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ReportSchedule.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ReportScheduleModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Report;

public class ReportScheduleService : BaseService, IReportScheduleService
{
    public ReportScheduleService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateReportScheduleCommand createReportSchedule)
    {
        Logger.LogDebug($"Create Site '{createReportSchedule.ReportName}'");

        return await Mediator.Send(createReportSchedule);
    }

    public async Task<BaseResponse> DeleteAsync(string reportId)
    {
        Guard.Against.InvalidGuidOrEmpty(reportId, "Report Id");

        Logger.LogDebug($"Delete Report Schedule Details by Id '{reportId}'");

        return await Mediator.Send(new DeleteReportScheduleCommand { Id = reportId });
    }

    public async Task<PaginatedResult<ReportScheduleListVm>> GetPaginatedReportScheduleList(
        GetReportSchedulePaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Report Schedule Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<ReportScheduleDetailVm> GetReportScheduleById(string reportId)
    {
        Guard.Against.InvalidGuidOrEmpty(reportId, "Report Schedule Id");

        Logger.LogDebug($"Get Report Schedule Detail by Id '{reportId}'");

        return await Mediator.Send(new GetReportScheduleDetailQuery { Id = reportId });
    }

    public async Task<List<GetReportScheduleNameVm>> GetReportScheduleNames()
    {
        Logger.LogDebug("Get All Report Schedule Names");

        return await Mediator.Send(new GetReportScheduleNameQuery());
    }

    public async Task<bool> IsReportScheduleNameExist(string reportName, string id)
    {
        Guard.Against.NullOrWhiteSpace(reportName, "Report Schedule Name");

        Logger.LogDebug($"Check Name Exists Detail by Report Schedule Name '{reportName}' and Id '{id}'");

        return await Mediator.Send(new GetReportScheduleNameUniqueQuery { ReportName = reportName, ReportId = id });
    }

    public async Task<BaseResponse> UpdateAsync(UpdateReportScheduleCommand updateReportSchedule)
    {
        Logger.LogDebug($"Update Site '{updateReportSchedule.ReportName}'");

        return await Mediator.Send(updateReportSchedule);
    }
    public async Task<List<ReportScheduleExecutionDetailVm>> GetReportScheduleExecutionById(string reportId)
    {
        Guard.Against.InvalidGuidOrEmpty(reportId, "Report Schedule Execution Id");

        Logger.LogDebug($"Get Report Schedule Execution Details by ReportId '{reportId}'");

        return await Mediator.Send(new GetReportScheduleExecutionDetailQuery { Id = reportId });
    }
}
