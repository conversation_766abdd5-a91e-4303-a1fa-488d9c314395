﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Server.Events.Create;

public class ServerCreatedEventHandler : INotificationHandler<ServerCreatedEvent>
{
    private readonly ILogger<ServerCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ServerCreatedEventHandler(ILoggedInUserService userService, ILogger<ServerCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ServerCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress ?? "::1",
            Entity = Modules.Server.ToString(),
            Action = $"{ActivityType.Create} {Modules.Server}",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"Server '{createdEvent.ServerName}' created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Server '{createdEvent.ServerName}' created successfully.");
    }
}