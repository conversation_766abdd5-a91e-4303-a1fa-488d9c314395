using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class FiaImpactCategoryRepositoryTests : IClassFixture<FiaImpactCategoryFixture>, IDisposable
{
    private readonly FiaImpactCategoryFixture _fiaImpactCategoryFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly FiaImpactCategoryRepository _repository;

    public FiaImpactCategoryRepositoryTests(FiaImpactCategoryFixture fiaImpactCategoryFixture)
    {
        _fiaImpactCategoryFixture = fiaImpactCategoryFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _repository = new FiaImpactCategoryRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithoutId()
    {
        // Arrange
        var categoryName = "Financial Impact";
        _fiaImpactCategoryFixture.FiaImpactCategoryDto.Name = categoryName;

        await _dbContext.FiaImpactCategory.AddAsync(_fiaImpactCategoryFixture.FiaImpactCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(categoryName, null);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameDoesNotExist_WithoutId()
    {
        // Arrange
        var nonExistentName = "Non Existent Category";

        // Act
        var result = await _repository.IsNameExist(nonExistentName, null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameExists_WithSameId()
    {
        // Arrange
        var existingId = Guid.NewGuid().ToString();
        var categoryName = "Operational Impact";

        _fiaImpactCategoryFixture.FiaImpactCategoryDto.ReferenceId = existingId;
        _fiaImpactCategoryFixture.FiaImpactCategoryDto.Name = categoryName;

        await _dbContext.FiaImpactCategory.AddAsync(_fiaImpactCategoryFixture.FiaImpactCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(categoryName, existingId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithDifferentId()
    {
        // Arrange
        var differentId = Guid.NewGuid().ToString();
        var categoryName = "Reputational Impact";

        _fiaImpactCategoryFixture.FiaImpactCategoryDto.Name = categoryName;

        await _dbContext.FiaImpactCategory.AddAsync(_fiaImpactCategoryFixture.FiaImpactCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(categoryName, differentId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithInvalidGuid()
    {
        // Arrange
        var invalidId = "not-a-valid-guid";
        var categoryName = "Legal Impact";

        _fiaImpactCategoryFixture.FiaImpactCategoryDto.Name = categoryName;

        await _dbContext.FiaImpactCategory.AddAsync(_fiaImpactCategoryFixture.FiaImpactCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(categoryName, invalidId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_IsCaseSensitive()
    {
        // Arrange
        var categoryName = "Environmental Impact";
        _fiaImpactCategoryFixture.FiaImpactCategoryDto.Name = categoryName;

        await _dbContext.FiaImpactCategory.AddAsync(_fiaImpactCategoryFixture.FiaImpactCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("ENVIRONMENTAL IMPACT", null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_HandlesEmptyString()
    {
        // Act
        var result = await _repository.IsNameExist("", null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_HandlesEmptyId()
    {
        // Arrange
        var categoryName = "Test Category";
        _fiaImpactCategoryFixture.FiaImpactCategoryDto.Name = categoryName;

        await _dbContext.FiaImpactCategory.AddAsync(_fiaImpactCategoryFixture.FiaImpactCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(categoryName, "");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_IncludesInactiveRecords()
    {
        // Arrange
        var categoryName = "Inactive Category";
        _fiaImpactCategoryFixture.FiaImpactCategoryDto.Name = categoryName;

        await _dbContext.FiaImpactCategory.AddAsync(_fiaImpactCategoryFixture.FiaImpactCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Manually set as inactive after saving
        _fiaImpactCategoryFixture.FiaImpactCategoryDto.IsActive = false;
        _dbContext.FiaImpactCategory.Update(_fiaImpactCategoryFixture.FiaImpactCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(categoryName, null);

        // Assert
        Assert.True(result); // Should still find inactive records
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddFiaImpactCategory_WhenValidCategory()
    {
        // Arrange
        var category = _fiaImpactCategoryFixture.FiaImpactCategoryDto;
        category.Name = "Test Impact Category";
        category.Description = "Test Description";

        // Act
        var result = await _repository.AddAsync(category);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(category.Name, result.Name);
        Assert.Equal(category.Description, result.Description);
        Assert.Single(_dbContext.FiaImpactCategory);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenCategoryIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsCategory_WhenExists()
    {
        // Arrange
        _fiaImpactCategoryFixture.FiaImpactCategoryDto.Id = 1;
        await _dbContext.FiaImpactCategory.AddAsync(_fiaImpactCategoryFixture.FiaImpactCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_fiaImpactCategoryFixture.FiaImpactCategoryDto.Id, result.Id);
        Assert.Equal(_fiaImpactCategoryFixture.FiaImpactCategoryDto.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsCategory_WhenExists()
    {
        // Arrange
        var referenceId = Guid.NewGuid().ToString();
        _fiaImpactCategoryFixture.FiaImpactCategoryDto.ReferenceId = referenceId;

        await _dbContext.FiaImpactCategory.AddAsync(_fiaImpactCategoryFixture.FiaImpactCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        var nonExistentReferenceId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentReferenceId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateCategory_WhenValidCategory()
    {
        // Arrange
        _dbContext.FiaImpactCategory.Add(_fiaImpactCategoryFixture.FiaImpactCategoryDto);
        await _dbContext.SaveChangesAsync();

        _fiaImpactCategoryFixture.FiaImpactCategoryDto.Name = "Updated Category Name";
        _fiaImpactCategoryFixture.FiaImpactCategoryDto.Description = "Updated Description";

        // Act
        var result = await _repository.UpdateAsync(_fiaImpactCategoryFixture.FiaImpactCategoryDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Category Name", result.Name);
        Assert.Equal("Updated Description", result.Description);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenCategoryIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task ListAllAsync_ReturnsAllActiveCategories_WhenCategoriesExist()
    {
        // Arrange
        await _dbContext.FiaImpactCategory.AddRangeAsync(_fiaImpactCategoryFixture.FiaImpactCategoryList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion
}
