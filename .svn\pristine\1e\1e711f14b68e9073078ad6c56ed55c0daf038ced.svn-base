using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class PageSolutionMappingFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string PageSolutionMappingName => "TestPageSolutionMapping";
    public static string PageBuilderId => "PAGE_BUILDER_123";
    public static string ReplicationTypeId => "REPLICATION_TYPE_123";
    public static string ReplicationCategoryTypeId => "REPLICATION_CATEGORY_TYPE_123";

    public List<PageSolutionMapping> PageSolutionMappingPaginationList { get; set; }
    public List<PageSolutionMapping> PageSolutionMappingList { get; set; }
    public PageSolutionMapping PageSolutionMappingDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public PageSolutionMappingFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<PageSolutionMapping>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Name, () => PageSolutionMappingName + "_" + _fixture.Create<string>())
            .With(x => x.PageBuilderId, () => PageBuilderId)
            .With(x => x.PageBuilderName, () => _fixture.Create<string>())
            .With(x => x.MonitorType, () => _fixture.Create<string>())
            .With(x => x.Type, () => _fixture.Create<int>())
            .With(x => x.TypeName, () => _fixture.Create<string>())
            .With(x => x.SubTypeId, () => _fixture.Create<string>())
            .With(x => x.SubType, () => _fixture.Create<string>())
            .With(x => x.ReplicationTypeId, () => ReplicationTypeId)
            .With(x => x.ReplicationTypeName, () => _fixture.Create<string>())
            .With(x => x.ReplicationCategoryTypeId, () => ReplicationCategoryTypeId)
            .With(x => x.ReplicationCategoryType, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow));

        PageSolutionMappingPaginationList = _fixture.CreateMany<PageSolutionMapping>(20).ToList();
        PageSolutionMappingList = _fixture.CreateMany<PageSolutionMapping>(5).ToList();
        PageSolutionMappingDto = _fixture.Create<PageSolutionMapping>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public PageSolutionMapping CreatePageSolutionMappingWithProperties(
        string name = null,
        string pageBuilderId = null,
        string replicationTypeId = null,
        string replicationCategoryTypeId = null,
        int? type = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<PageSolutionMapping>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Name, name ?? PageSolutionMappingName + "_" + _fixture.Create<string>())
            .With(x => x.PageBuilderId, pageBuilderId ?? PageBuilderId)
            .With(x => x.ReplicationTypeId, replicationTypeId ?? ReplicationTypeId)
            .With(x => x.ReplicationCategoryTypeId, replicationCategoryTypeId ?? ReplicationCategoryTypeId)
            .With(x => x.Type, type ?? _fixture.Create<int>())
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public PageSolutionMapping CreatePageSolutionMappingWithSpecificName(string name)
    {
        return CreatePageSolutionMappingWithProperties(name: name);
    }

    public PageSolutionMapping CreatePageSolutionMappingWithSpecificReplicationCategoryTypeId(string replicationCategoryTypeId)
    {
        return CreatePageSolutionMappingWithProperties(replicationCategoryTypeId: replicationCategoryTypeId);
    }

    public PageSolutionMapping CreatePageSolutionMappingWithSpecificReplicationTypeId(string replicationTypeId)
    {
        return CreatePageSolutionMappingWithProperties(replicationTypeId: replicationTypeId);
    }

    public List<PageSolutionMapping> CreateMultiplePageSolutionMappingsWithSameName(string name, int count)
    {
        var mappings = new List<PageSolutionMapping>();
        for (int i = 0; i < count; i++)
        {
            mappings.Add(CreatePageSolutionMappingWithProperties(name: name));
        }
        return mappings;
    }

    public List<PageSolutionMapping> CreatePageSolutionMappingsWithSameReplicationCategoryTypeId(string replicationCategoryTypeId, int count)
    {
        var mappings = new List<PageSolutionMapping>();
        for (int i = 0; i < count; i++)
        {
            mappings.Add(CreatePageSolutionMappingWithProperties(replicationCategoryTypeId: replicationCategoryTypeId));
        }
        return mappings;
    }

    public List<PageSolutionMapping> CreatePageSolutionMappingsWithSameReplicationTypeId(string replicationTypeId, int count)
    {
        var mappings = new List<PageSolutionMapping>();
        for (int i = 0; i < count; i++)
        {
            mappings.Add(CreatePageSolutionMappingWithProperties(replicationTypeId: replicationTypeId));
        }
        return mappings;
    }

    public PageSolutionMapping CreateInactivePageSolutionMapping()
    {
        return CreatePageSolutionMappingWithProperties(isActive: false);
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
