using ContinuityPatrol.Application.Features.Archive.Commands.Create;
using ContinuityPatrol.Application.Features.Archive.Commands.Update;
using ContinuityPatrol.Application.Features.Archive.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixRequestModel;
using ContinuityPatrol.Domain.ViewModels.ArchiveModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class ArchiveProfile : Profile
{
    public ArchiveProfile()
    {
        CreateMap<Archive, ArchiveListVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<Archive, ArchiveDetailVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<Archive, CreateArchiveCommand>().ReverseMap();
        CreateMap<Archive, ArchiveViewModel>().ReverseMap();

        CreateMap<CreateArchiveCommand, ArchiveViewModel>().ReverseMap();
        CreateMap<UpdateArchiveCommand, ArchiveViewModel>().ReverseMap();

        CreateMap<UpdateArchiveCommand, Archive>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<PaginatedResult<Archive>, PaginatedResult<ArchiveListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}