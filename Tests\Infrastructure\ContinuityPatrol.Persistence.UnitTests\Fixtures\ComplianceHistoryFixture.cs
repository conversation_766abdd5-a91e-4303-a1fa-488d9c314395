using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ComplianceHistoryFixture : IDisposable
{
    public const string CompanyId = "550e8400-e29b-41d4-a716-446655440000";
    public const string ParentCompanyId = "550e8400-e29b-41d4-a716-446655440001";
    public const string InfraObjectId = "INFRA_001";

    public List<ComplianceHistory> ComplianceHistoryPaginationList { get; set; }
    public List<ComplianceHistory> ComplianceHistoryList { get; set; }
    public ComplianceHistory ComplianceHistoryDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public ComplianceHistoryFixture()
    {
        var fixture = new Fixture();

        ComplianceHistoryList = fixture.Create<List<ComplianceHistory>>();
        ComplianceHistoryList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());

        ComplianceHistoryPaginationList = fixture.CreateMany<ComplianceHistory>(20).ToList();
        ComplianceHistoryPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
  
        ComplianceHistoryDto = fixture.Create<ComplianceHistory>();
        ComplianceHistoryDto.ReferenceId = Guid.NewGuid().ToString();
        
        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
