﻿
const userManageURL = {
    createOrUpdate: "Admin/User/CreateOrUpdate", 
    getPagination: "/Admin/User/GetPagination",
    nameExist: "Admin/User/IsLoginNameExist",
    userRole: "/Admin/User/GetUserRoleList",
    domainGroups: "Admin/User/GetDomainGroups",
    infraObjectList: "Admin/User/GetAllInfraObjectList",
    domainUsers: "Admin/User/GetDomainUsers",
    domains:"Admin/User/GetDomains"
};

const generateRandomString = (length) => Array.from({ length }, () => String.fromCharCode(65 + Math.floor(Math.random() * 26) + (Math.random() < 0.5 ? 32 : 0))).join('');
let userRole = $('#userRole').text().trim();
let userRoleValue = $('#userRoleValue').text().trim();
let loggedInUserId = $('#loggedInUserId').text().trim();
let userData = {}
let isCreate = false, isLoginName = false;
let JSONDataForClickEditButton = "", editedLoginID = "", dataTable = "", jsonData = "";
let loginNameEdit = 0;

$(document).ready(function () {
    var createPermission = $("#AdminCreate").data("create-permission").toLowerCase();
    var deletePermission = $("#AdminDelete").data("delete-permission").toLowerCase();
    $('#Label_error').Text = "";
    $('#searchStringDomain,#usernameDomain').hide();
    $("#selectAll").prop("disabled", false);
    btnCrudEnable('resetOk')
    btnCrudEnable('confirmDeleteButton')    
    if (createPermission == "false") {
        $("#btnCreate").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
    }  
    var selectedValues = [];
    dataTable = $('#userList').DataTable(
        {
            language: {
                decimal: ",",
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "Sortable": true,
            "order": [],
            fixedColumns: {
                left: 1,
                right: 1
            },
            //retrieve: true,
            "ajax": {
                "type": "GET",
                "url": userManageURL.getPagination,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = (d?.order && d.order[0]) ? d.order[0].column : ''; //
                    let sortValue = sortIndex === 1 ? "loginName" : sortIndex === 2 ? "companyName" : sortIndex === 3 ? "loginType" :
                        sortIndex === 4 ? "roleName" : sortIndex === 5 ? "email" : sortIndex === 6 ? "loginDate" : sortIndex === 7 ? "status" : "";
                    "";
                    let orderValue = (d?.order && d.order[0]) ? d.order[0].dir : 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data;
                }
            },
            "error": function (xhr, status, error) {
                if (error.status === 401) {
                    window.location.assign('/Account/Logout')
                }
            },
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    }
                },
                {
                    "data": "loginName", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            var loginName = row.loginName;
                            var nameSplit = loginName.split(/[ _]+/);
                            var initials = nameSplit.length > 1 ?
                                nameSplit[0].trim().substring(0, 1) + nameSplit[1].trim().substring(0, 1).toUpperCase() :
                                nameSplit[0].trim().substring(0, 1).toUpperCase();
                            return `
                               <span class="Avatar_Logo position-relative">
                                    <span class="position-absolute translate-middle p-1 ${row.isLoggedIn
                                    ? "bg-success"
                                    : "bg-secondary"
                                } border border-2 border-light rounded-circle" style="right: 8px; top: 4px !important;" 
                                      title="${row.isLoggedIn ? "Logged In" : "Not Logged In"}">
                                      <span class="visually-hidden">New alerts</span>
                                    </span>
                                <span class="Icon" id="username" title="${initials}">${initials}</span></span>
                                <span style="max-width:80px" class="text-truncate" title="${loginName}">${loginName}</span>`;
                        } else {
                            return data;
                        }
                    }
                },
                {
                    "data": "companyName", "name": "Company Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span class="align-middle" title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "loginType", "name": "Authentication", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span class="align-middle" title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "roleName", "name": "Role Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            if (row.roleLogo === null || row.roleLogo === '' || row.roleLogo === 'NA') {
                                return `<span class="badge text-bg-primary align-middle" title="${data || 'NA'}" style="text-align: center;">${data || 'NA'}</span>`;
                            }
                            else {
                                return `<span class="badge align-middle" title="${data || 'NA'}" style="background: ${row.roleLogo}; text-align: center;">${data || 'NA'}</span>`;
                            }
                        }
                        return data;
                    }
                },
                {
                    "data": "email", "name": "Email", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span class="mt-1" title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                //{
                // "data": "mobile", "name": "Mobile", "autoWidth": true,
                // "render": function (data, type, row) {
                //    let mobile = data ? data : "NA"
                //    if (type === 'display') {
                //    return '<span class="mt-1" title="' + mobile + '">' + mobile + '</span>';
                // }
                //  return data;
                // }
                //  },
                //  {
                // "data": null,
                // "name": "Communication Type",
                // "autoWidth": true,
                //render: function (data, type, row) {
                // var mobile = row?.mobile;
                // if (mobile != null) {
                //  return `
                // <div class=" d-flex  align-items-end gap-2">
                //   <span>
                //      <i class="cp-email  mt-1" title="Email"></i >
                //  </span >
                //  <span>
                //      <i class="cp-sms-gateway d-flex mt-1" title="SMS" ></i >
                // </span >
                //  </div>`
                // }
                // else {
                //     return `
                //         <span>
                //            <i class="cp-email d-flex mt-1" title="Email"></i >
                //        </span > `;
                // }
                // }
                //},
                {
                    "data": "loginDate", "name": "Login Data", "autoWidth": true,
                    "render": function (data, type, row) {
                        let login_date = data ? data : "NA"
                        if (type === 'display') {
                            return '<span class="mt-1" title="' + login_date + '">' + login_date + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": null,
                    "name": "State",
                    "autoWidth": true,
                    "orderable": false,
                    render: function (data, type, row) {

                        return ` 
                                <span> 
                                   <i class="cp-active-inactive text-${row.isLock ? 'danger' : 'success'}" title="${row.isLock ? 'In-Active' : 'Active'}"></i >
                                </span > `;

                    }
                },
                {
                    "orderable": false,
                    "render": function (data, type, row) {
                        if (userRoleValue === "SuperAdmin") {
                            if (row.roleName === "SuperAdmin") {
                                return `
                                <div class="d-flex align-items-center gap-2">
                                           
                                                    <span role="button" title="Edit" class="edit-button"  data-user='${JSON.stringify(row)}'>
                                                        <i class="cp-edit"></i>
                                                    </span>
                                           
                                                    <span role="button" title="Delete" class="icon-disabled">
                                                        <i class="cp-Delete"></i>
                                                    </span>
                                           
                                                   <span role="button" title=${row.isLock ? "UnLock" : "Lock"} ${row.isLock ? 'class="reset-button" data-bs-toggle="modal" data-bs-target="#ResetModal" data-user-id="' + row.id + '" data-user-name="' + row.loginName + '" data-user-email="' + row?.userInfo?.email + '"' : 'class = "reset-button icon-disabled"'}>
                                                        <i class=${row.isLock ? "cp-open-lock" : "cp-lock"} ></i>
                                                    </span>

                                </div>`;
                            }
                            else {
                                return `
                                <div class="d-flex align-items-center gap-2">
                                            
                                                    <span role="button" title="Edit" class="edit-button" data-user='${JSON.stringify(row)}'>
                                                        <i class="cp-edit"></i>
                                                    </span>
                                           
                                                    <span role="button" title="Delete" class="delete-button" data-user-id="${row.id}" data-user-name="${row.loginName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                        <i class="cp-Delete"></i>
                                                    </span>
                                                    <span role="button" title=${row.isLock ? "UnLock" : "Lock"}  ${row.isLock ? 'class="reset-button" data-bs-toggle="modal" data-bs-target="#ResetModal" data-user-id="' + row.id + '" data-user-name="' + row.loginName + '" data-user-email="' + row?.userInfo?.email + '"' : 'class = "reset-button icon-disabled"'}>
                                                        <i class=${row.isLock ? "cp-open-lock" : "cp-lock"} ></i>
                                                    </span>
                                </div>`;
                            }
                        }
                        else if (userRoleValue === "SiteAdmin") {
                            if (row.roleName === "SiteAdmin") {
                                return `
                                <div class="d-flex align-items-center gap-2">
                                           
                                                    <span role="button" title="Edit" class="edit-button"  data-user='${JSON.stringify(row)}'>
                                                        <i class="cp-edit"></i>
                                                    </span>
                                           
                                                    <span role="button" title="Delete" class="icon-disabled">
                                                        <i class="cp-Delete"></i>
                                                    </span>
                                           
                                                   <span role="button" title=${row.isLock ? "UnLock" : "Lock"} ${row.isLock ? 'class="reset-button" data-bs-toggle="modal" data-bs-target="#ResetModal" data-user-id="' + row.id + '" data-user-name="' + row.loginName + '" data-user-email="' + row?.userInfo?.email + '"' : 'class = "reset-button icon-disabled"'}>
                                                        <i class=${row.isLock ? "cp-open-lock" : "cp-lock"} ></i>
                                                    </span>

                                </div>`;
                            } else {
                                return `
                                <div class="d-flex align-items-center gap-2">
                                            
                                                    <span role="button" title="Edit" class="edit-button" data-user='${JSON.stringify(row)}'>
                                                        <i class="cp-edit"></i>
                                                    </span>
                                           
                                                    <span role="button" title="Delete" class="delete-button " data-user-id="${row.id}" data-user-name="${row.loginName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                        <i class="cp-Delete"></i>
                                                    </span>
                                             
                                                 <span role="button" title=${row.isLock ? "UnLock" : "Lock"} ${row.isLock ? 'class="reset-button" data-bs-toggle="modal" data-bs-target="#ResetModal" data-user-id="' + row.id + '" data-user-name="' + row.loginName + '" data-user-email="' + row?.userInfo?.email + '"' : 'class = "reset-button icon-disabled"'}>
                                                        <i class=${row.isLock ? "cp-open-lock" : "cp-lock"} ></i>
                                                    </span>
                                </div>`;

                            }

                        }
                        else if (userRoleValue === "Administrator") {
                            if (row.roleName === "SuperAdmin") {
                                return `
                                    <div class="d-flex align-items-center gap-2">
                                            
                                                        <span role="button" title="Edit" class="icon-disabled">
                                                            <i class="cp-edit"></i>
                                                        </span>
                                          
                                                        <span role="button" title="Delete" class="icon-disabled" >
                                                            <i class="cp-Delete"></i>
                                                        </span>
                                                         <span role="button" title=${row.isLock ? "UnLock" : "Lock"}  ${row.isLock ? 'class="reset-button" data-bs-toggle="modal" data-bs-target="#ResetModal" data-user-id="' + row.id + '" data-user-name="' + row.loginName + '" data-user-email="' + row?.userInfo?.email + '"' : 'class = "reset-button icon-disabled"'}>
                                                        <i class=${row.isLock ? "cp-open-lock" : "cp-lock"} ></i>
                                                    </span>
                                            
                                    </div>`;
                            }
                            else if (row.roleName === "Administrator") {
                                return `
                                    <div class="d-flex align-items-center gap-2">
                                            
                                                        <span role="button" title="Edit" class="edit-button "  data-user='${JSON.stringify(row)}'>
                                                            <i class="cp-edit"></i>
                                                        </span>
                                          
                                                        <span role="button" title="Delete" class="icon-disabled">
                                                            <i class="cp-Delete"></i>
                                                        </span>
                                                        <span role="button" title=${row.isLock ? "UnLock" : "Lock"}  ${row.isLock ? 'class="reset-button" data-bs-toggle="modal" data-bs-target="#ResetModal" data-user-id="' + row.id + '" data-user-name="' + row.loginName + '" data-user-email="' + row?.userInfo?.email + '"' : 'class = "reset-button icon-disabled"'}>
                                                        <i class=${row.isLock ? "cp-open-lock" : "cp-lock"} ></i>
                                                    </span>
                                            
                                    </div>`;
                            }
                            else {
                                return `
                                <div class="d-flex align-items-center gap-2">
                                            
                                                    <span role="button" title="Edit" class="edit-button" data-user='${JSON.stringify(row)}'>
                                                        <i class="cp-edit"></i>
                                                    </span>
                                            
                                                    <span role="button" title="Delete" class="delete-button" data-user-id="${row.id}" data-user-name="${row.loginName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                        <i class="cp-Delete"></i>
                                                    </span>
                                                   <span role="button" title=${row.isLock ? "UnLock" : "Lock"}  ${row.isLock ? 'class="reset-button" data-bs-toggle="modal" data-bs-target="#ResetModal" data-user-id="' + row.id + '" data-user-name="' + row.loginName + '" data-user-email="' + row?.userInfo?.email + '"' : 'class = "reset-button icon-disabled"'}>
                                                        <i class=${row.isLock ? "cp-open-lock" : "cp-lock"} ></i>
                                                    </span>
                                            
                                </div>`;
                            }
                        }
                        else {
                            if (createPermission == "true" && deletePermission == "true") {
                                return `
                                <div class="d-flex align-items-center gap-2">
                                            
                                                    <span role="button" title="Edit" class="edit-button" data-user='${JSON.stringify(row)}'>
                                                        <i class="cp-edit"></i>
                                                    </span>
                                           
                                                    <span role="button" title="Delete" class="delete-button" data-user-id="${row.id}" data-user-name="${row.loginName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                        <i class="cp-Delete"></i>
                                                    </span>         
                                                      <span role="button" title=${row.isLock ? "UnLock" : "Lock"}  ${row.isLock ? 'class="reset-button" data-bs-toggle="modal" data-bs-target="#ResetModal" data-user-id="' + row.id + '" data-user-name="' + row.loginName + '" data-user-email="' + row?.userInfo?.email + '"' : 'class = "reset-button icon-disabled"'}>
                                                        <i class=${row.isLock ? "cp-open-lock" : "cp-lock"} ></i>
                                                    </span>
                                </div>`;
                            }
                            else if (createPermission == "false" && deletePermission == "true") {
                                return `
                                <div class="d-flex align-items-center gap-2">
                                            
                                                    <span role="button" title="Edit" class="icon-disabled">
                                                        <i class="cp-edit"></i>
                                                    </span>
                                           
                                                    <span role="button" title="Delete" class="delete-button" data-user-id="${row.id}" data-user-name="${row.loginName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                        <i class="cp-Delete"></i>
                                                    </span>         
                                                      <span role="button" title=${row.isLock ? "UnLock" : "Lock"}  ${row.isLock ? 'class="reset-button" data-bs-toggle="modal" data-bs-target="#ResetModal" data-user-id="' + row.id + '" data-user-name="' + row.loginName + '" data-user-email="' + row?.userInfo?.email + '"' : 'class = "reset-button icon-disabled"'}>
                                                        <i class=${row.isLock ? "cp-open-lock" : "cp-lock"} ></i>
                                                    </span>
                                </div>`;
                            }
                            else if (createPermission == "true" && deletePermission == "false") {
                                return `
                                <div class="d-flex align-items-center gap-2">
                                            
                                                    <span role="button" title="Edit" class="edit-button" data-user='${JSON.stringify(row)}'>
                                                        <i class="cp-edit"></i>
                                                    </span>
                                           
                                                    <span role="button" title="Delete" class="icon-disabled">
                                                        <i class="cp-Delete"></i>
                                                    </span>         
                                                      <span role="button" title=${row.isLock ? "UnLock" : "Lock"}  ${row.isLock ? 'class="reset-button" data-bs-toggle="modal" data-bs-target="#ResetModal" data-user-id="' + row.id + '" data-user-name="' + row.loginName + '" data-user-email="' + row?.userInfo?.email + '"' : 'class = "reset-button icon-disabled"'}>
                                                        <i class=${row.isLock ? "cp-open-lock" : "cp-lock"} ></i>
                                                    </span>
                                </div>`;
                            }
                            else if (createPermission == "false" && deletePermission == "false") {
                                return `
                                <div class="d-flex align-items-center gap-2">
                                            
                                                    <span role="button" title="Edit" class="icon-disabled">
                                                        <i class="cp-edit"></i>
                                                    </span>
                                           
                                                    <span role="button" title="Delete" class="icon-disabled">
                                                        <i class="cp-Delete"></i>
                                                    </span>         
                                                      <span role="button" title=${row.isLock ? "UnLock" : "Lock"}  ${row.isLock ? 'class="reset-button" data-bs-toggle="modal" data-bs-target="#ResetModal" data-user-id="' + row.id + '" data-user-name="' + row.loginName + '" data-user-email="' + row?.userInfo?.email + '"' : 'class = "reset-button icon-disabled"'}>
                                                        <i class=${row.isLock ? "cp-open-lock" : "cp-lock"} ></i>
                                                    </span>
                                </div>`;
                            }
                        }
                    }
                }
            ],

            "columnDefs": [
                {
                    "targets": [1, 2, 5, 6],
                    "className": "truncate"
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            },

            "createdRow": function (row) {
                $(row).find(".truncate").each(function () {
                    $(this).attr("title", this.innerText);
                });
            },
            "drawCallback": function (settings) {

                //const randomColor = () => {
                //    return "#" + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0').toUpperCase();
                //}
                //const namelist = document.querySelectorAll("#username");
                //namelist.forEach((name) => {
                //    name.style.backgroundColor = randomColor();
                //})

                var table = $('#userList').DataTable();
                var pageInfo = table?.page?.info();

                const userColorPallete = ['#00CCFF', '#0066FF', '#CC3399', '#FF9900', '#99CC00', '#3399FF', '#993399',
                    '#339966', '#993333', '#009900', '#000099', '#666633', '#FF3300', '#6600CC', '#C65F00', '#009974',
                    '#BEBE00', '#CC0000', '#9100C5', '#020057', '#949C3C', '#00CB82', '#418E8A', '#0099CC', '#3D50FF'
                ]

                const namelist = document.querySelectorAll("#username");
                let startIndex = pageInfo?.start;

                namelist.forEach((name, index) => {

                    let colorIndex = (startIndex + index) % userColorPallete.length;

                    if (userColorPallete[colorIndex]) {
                        name.style.backgroundColor = userColorPallete[colorIndex];
                        colorIndex++;
                    }

                });
            }

        });
   
    $('#search-inp').attr('autocomplete', 'off');

    $(document).on('input', '#search-inp', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        $('.filterSearch').each(function () {

            if ($(this).is(':checked')) {
                var checkboxValue = $(this).val();
                var inputValue = $('#search-inp').val();
                selectedValues.push(checkboxValue + inputValue);
            }
        });

        dataTable.ajax.reload(function (json) {

            if (json?.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    }, 500));

    var value = $('#ddlLoginType').val();
    ValidateLoginType(value);

    $(document).on('change', '#ddlLoginType', async function () {
        const value = $(this).val()
        const trimValue = value.toLowerCase().replace(" ", "");
        if (value === "AD") {
            $("#textLoginName").prop('readonly', true);
            $('#LoginPassword-error').text('').removeClass('field-validation-error');
            $('#ConfirmPassword-error').text('').removeClass('field-validation-error');
            let tempPassword = await EncryptPassword('Use your AD password', "#" + 'encriptedPassword');
            $('#encriptedPassword').val(tempPassword);
            $('#ddlDomain').val('').empty();
            $('#ADGroupName').hide();        
            $("#ADIndividual").prop('checked', true).trigger("click");
        
        }
        else {
            $("#textLoginName").prop('readonly', false);
            $("#textLoginName").prop('disabled', false);
            $("#AdDomain-error").text("").removeClass("field-validation-error");
            $('#encriptedPassword').val("");
            $('#AdUser-error').text("").removeClass('field-validation-error');
        }
        ValidateLoginType($(this).val());
        $("#loginType").val(value);
        $("#textLoginName").val('')
        //$('#LoginName-error, #AdDomain-error').removeClass('field-validation-error').text('')
        await validateDropDown(value, 'Select authentication type', $('#LoginType-error'));
    });

    $(document).on('change', '#userADGroupName', async function () {
        const value = $(this).val()
        await validateDropDown(value, 'Select group', $('#Adgroup-error'));
       
    });

    $(document).on('change', '#ddlCompanyName', async function () {
        const value = $(this).val();
        const companyId = $("#ddlCompanyName option:selected").attr('id');
        const data = { id: companyId };

        try {
            let resultdb = await GetAsync(RootUrl + 'Admin/User/GetCompanyById', data, OnError);
            let response = await $.ajax({
                type: "GET",
                url: userManageURL.userRole,
                dataType: "json"
            });

            if (response?.success) {
                roledata(response.data, resultdb?.isParent);
            } else {
                errorNotification(response);
            }
            $('#textCompanyId').val(companyId);
            let errorElement = $('#Company-error');
            validateDropDown(value, 'Select company name', errorElement);

        } catch (error) {
            console.error("Error occurred:", error);
        }
    });

    $(document).on('change', '#ddlUpdateCompanyName', function () {
        var companyId = $("#ddlUpdateCompanyName option:selected").attr('id');
        $('#textUpdateCompanyId').val(companyId);
    });

    $(document).on('change', '#ddlRoleName', function () {
        const value = $("#ddlRoleName :selected").text();        
        var errorElement = $('#Role-error');
        var roleId = $("#ddlRoleName option:selected").data('roleid');

        $('#textRoleId').val(roleId);
        validateDropDown(value, 'Select role', errorElement);

    });


    $(document).on('change', '#ddlUpdateRoleName', function () {
        var roleId = $("#ddlUpdateRoleName option:selected").attr('id');
        $('#textUpdateRoleId').val(roleId);
    });

    $(document).on('change', '#ddlDomain', function () {
        const value = $(this).val();
        var errorElement = $('#AdDomain-error');
        validateDropDown(value, 'Select domain', errorElement);
        if (value !== 'Select domain' && value !== undefined && value !== '') {    
            if (document.getElementById("ADIndividual").checked) {
                $('#searchStringDomain,#usernameDomain').show();
                
            } else if (document.getElementById("ADGroup").checked) {                             
                $('#searchStringDomain,#usernameDomain').hide();
               
                $('#ADGroupName').show();                
            }                  
        } else {
            $('#ddlDomainUser').empty();
        }

    });

    $(document).on('click', '#btnSearchUserDomain', function () {
        let value2 = $('#SearchUserDomain').val()
        let value = $('#ddlDomain').val()
        if (value) SetDomainUser(value, value2);
    });
    $(document).on('click', '#btnSearchGroupDomain', function () {
        let value2 = $('#SearchDomainGroup').val()
        let value = $('#ddlDomain').val()
        if (value) getGroupByDomain(value, value2);
    });
 
    $(document).on('click', '#ADIndividual', commonDebounce(async function () {
        $('#ddlDomain,#ddlRoleName,#SearchUserDomain,#ddlDomainUser,#textLoginName').val('').empty();
        $('#ADGroupName,#usernameDomain,#searchStringDomain').hide();
        // $('#ADLoginName,#ad_individual').show();
        $('#ad_individual').show();
        $('#ddlCompanyName').val('');
       
    }));

    $(document).on('click', '#ADGroup', function () {
        $('#ddlDomain,#ddlRoleName,#userADGroupName,#textLoginName').val('').empty();
        //$('#ad_individual,#ADLoginName').hide();
        $('#ad_individual').hide();      
        $('#ddlCompanyName').val(''); 

    })

    $(document).on('change', '#SearchUserDomain', function () {
        const value = $(this).val();
        var errorElement = $('#AdUser-error');
        validateDropDown(value, 'Enter search', errorElement);
    })
    $(document).on('change', '#SearchDomainGroup', function () {
        const value = $(this).val();
        var errorElement = $('#AdGroup-error');
        validateDropDown(value, 'Enter search', errorElement);
    })

    $(document).on('change', '#ddlDomainUser', function () {
        var value = $('#ddlDomain').val() + "\\" + $('#ddlDomainUser').val();
        var errorElement = $('#AdUser-error');
        $('#textLoginName').val(value);
        $('#LoginName-error').text('').removeClass('field-validation-error');
        validateDropDown($(this).val(), 'Select username', errorElement);

    });
    $(document).on('change', '#ddlDomainGroup', function () {
        var value = $('#ddlDomain').val() + "\\" + $('#ddlDomainGroup').val();
        var errorElement = $('#AdGroup-error');

        $('#textLoginName').val(value);
        $('#LoginName-error').text('').removeClass('field-validation-error');
        validateDropDown($(this).val(), 'Select username', errorElement);

    });

    $(document).on('keypress input', '#mobilenum', async function (event) {
        const value = $(this).val();
        if (value !== undefined) {
            $(this).val(value.replace(/  +/g, " "));
        }
        if (!/^[0-9]+$/.test(event.key)) {
            event?.preventDefault();
        }
        await validateMobile(value);
    });

    $.getJSON("/json/CountryDailCode.json", function (data) {
        setTimeout(() => {
            data.countrycode.forEach(function (value) {
                $('#mobilepre').append('<option value="' + value.dial_code + '">' + value.dial_code + '</option>');
            });
        }, 500);
    }).fail(function () {
        console.error("Failed to load JSON data.");
    });


    //setTimeout(() => {
    //    countrycode.forEach(function (value, index) {
    //        $('#mobilepre').append('<option  value="' + value.dial_code + '">' + value.dial_code + '</option>')
    //    });
    //}, 500)

    $(document).on('change', '#mobilepre', async function (event) {
        const value = $(this).val();
        await validateMobilePre(value);
    });
    //Update
    $(document).on('click', '#userList .edit-button', async function () {
        loginNameEdit = 0;
        JSONDataForClickEditButton = "";
        // await treeListView();
        $('#chk-LDAP').prop("checked", false);
        btnCrudEnable('SaveFunction');
        $("#example-form-t-0").trigger("click");
        $('#password-group').hide();
        userData = $(this).data('user');
        isCreate = false;
        populateModalFields(userData);
        var type = $("#ddlLoginType").val();
        var type_error = $('#LoginType-error');
        var isTypenamee = validateDropDown(type, 'Select authentication type ', type_error);
        $('#SaveFunction').text('Update');
        $('#CreateModal').modal('show');
        //editedRoleName = userData.roleName;
        //editedLoginName = userData.loginName;
        editedLoginID = userData.id;
        var val = $("#ddlRoleName :selected").text();
        if (editedLoginID.trim() === loggedInUserId) {
            $("#chk-workflowtype").prop("disabled", true)
        } else {
            $("#chk-workflowtype").prop("disabled", false)
        }

        /*var userRole = $('#userRole').text().trim();*/
        if (userRoleValue === "SuperAdmin" && val === "SuperAdmin") {
            //SelectAllTreeView(true);
            $(".selecttree, #selectAll, #textLoginName, #ddlCompanyName").prop('disabled', true);
            $('#ddlRoleName').prop('disabled', false);          
        }
        else if (userRoleValue.toLowerCase() === "siteadmin" && val === "SuperAdmin") {
            SelectAllTreeView(true);
            $(".selecttree, #selectAll, #ddlCompanyName, #textLoginName").prop('disabled', true);
            $('#ddlRoleName').prop('disabled', false);
        }
        else if (userRoleValue === "SuperAdmin" && val === "Administrator") {
            $(".selecttree, #selectAll, #ddlRoleName").prop("disabled", false);         
            $('#ddlCompanyName,#textLoginName').prop('disabled', true);
        }
        else if (userRoleValue === "Administrator" && val === "SuperAdmin") {
            $(".selecttree, #selectAll, #ddlRoleName, #textLoginName, #ddlCompanyName").prop('disabled', true);
        }
        else if (userRoleValue === "Administrator" && val === "Administrator") {          
            $('.selecttree,#ddlRoleName,#selectAll').prop('disabled', false);     
            $('#ddlCompanyName,#textLoginName').prop('disabled', true);
        }
        else if (val == "SuperAdmin") {
            $(".selecttree, #selectAll, #ddlCompanyName, #textLoginName").prop('disabled', true);
            $('#ddlRoleName').prop('disabled', false);
        }
        else {
            $(".selecttree, #selectAll, #ddlRoleName").prop("disabled", false);         
            $('#textLoginName,#ddlCompanyName').prop('disabled', true);;
        }
    });

    //delete
    $(document).on('click', '#userList .delete-button', function () {    
            var userId = $(this).data('user-id');
            var userName = $(this).data('user-name');
            $('#deleteData').text(userName);
            $('#textDeleteId').val(userId);
    });

        //Reset
        $(document).on('click', '#userList .reset-button', async function () {
            var resetUserId = $(this).data('user-id');
            var resetUserName = $(this).data('user-name');
            var resetUserEmail = $(this).data('user-email');
            const randomString = generateRandomString(5);
            $('#newPassword').val(randomString);
            await EncryptPassword(resetUserName, randomString, '#password');
            $('#resetId').val(resetUserId);
            $('#resetName').val(resetUserName);
            $('#resetData').text(resetUserName);
            $('#resetEmail').val(resetUserEmail);
        })

        //Validation
        $(document).on('input', '#textLoginName', function (event) {
            if (!isCreate) {
                $(this).prop('disabled', true);
                event.preventDefault();
                return;
            }
        });

        $(document).on('input', '#textLoginName', commonDebounce(async function () {
            if (!isCreate) {
                $(this).prop('disabled', true);
                return;
            }
            let hasReadOnly = $(this).prop('readonly');

            var loginId = $('#textLoginId').val();
            const value = $(this).val();

            var sanitizedValue = value.replace(/\s{2,}/g, ' ').trim();
            $(this).val(sanitizedValue);

            if (!hasReadOnly) {
                await validateLoginName(sanitizedValue, loginId, userManageURL.nameExist);
            }

            isLoginName = true;

            $("#userPassword, #userConfirmPassword").val("");

            if ($('#LoginPassword-error').hasClass('field-validation-error') && !value.length) {
                $('#LoginPassword-error').text('Enter password')
            }

            if ($('#ConfirmPassword-error').hasClass('field-validation-error') && !value.length) {
                $('#ConfirmPassword-error').text('Enter confirm password')
            }

        }, 500));

    $(document).on('click', '.eye-change', async function () {
        let input = $(this).prev();
        var icon = $(this).find("i");

        if (input.attr("type") === "password") {
            showPassword(input, icon);
            let encryptedPassword = $('#encriptedPassword').val();

            if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
                let afterLoginName = await onfocusPassword(encryptedPassword);
                input.val(afterLoginName);

            }

        } else {
            hidePassword(input, icon);
            let value = input.val().replace(/\s+/g, '');
            blurpassword(input.attr("id"), value);
        }
    });

    $(document).on('input', '#userPassword', function () {
        let value = this.value.replace(/\s+/g, '');
        inputpassword(this.id, value);
        $('#userConfirmPassword').val('');
    });

    $(document).on('input', '#userConfirmPassword', function () {
        let value = this.value.replace(/\s+/g, '');   
        inputConfirmpassword(this.id, value);
    });

    $(document).on('blur', '#userConfirmPassword, #userPassword', function () {
        let value = this.value.replace(/\s+/g, '');
        blurpassword(this.id, value);
    });

    $(document).on('focus', '#userConfirmPassword', function () {
        focusconfirmpassword(this.id);
    });

    $(document).on('focus', '#userPassword', function () {
        focuspassword(this.id);
    });         

        $(document).on('input', '#textUserName', async function () {
            var userId = $('#textLoginId').val();
            const value = $(this).val();
            var sanitizedValue = value.replace(/\s{2,}/g, ' ');
            $(this).val(sanitizedValue);
            await validateUserName(value, userId);
        });

        $(document).on('input', '#textEmail', async function () {
            var email = $('#textLoginId').val();
            const value = $(this).val();
            var sanitizedValue = value.replace(/\s{2,}/g, ' ');
            $(this).val(sanitizedValue);
            await validateEmail(value, email);
        });

        $(document).on('change', '#dashboardMode', function (e) {
            if (e.target.checked) {
                $('#dashboardModeValue').val(e.target.checked)
                $('#dashboardInputContainer').removeClass('d-none')
            } else {
                $('#dashboardInputContainer').addClass('d-none')
                $('#dashboardModeValue').val(e.target.checked)
                $('#dashboardList').val('').trigger('change')
            }
        });

        $(document).on('change', '#dashboardList', function (e) {
            if (e.target.value) $('#dashboard-error').text('').removeClass('field-validation-error')
        })

        $(document).on('input', '#textSessionTimeout', async function () {
            let session = $('#textLoginId').val();
            let value = $(this).val();

            value = value.replace(/[^0-9]/g, '');

            if (value === '0' || value.startsWith('0')) {
                value = '';
            }

            if (value.length > 5) {
                value = value.slice(0, 5);
            }

            $(this).val(value);
            await validateSessionTimeout(value, session);
        });

        $(document).on('click', '#NextFunction', commonDebounce(async function (e) {
            ValidateAll(true);
        }, 700));

        $(document).on('click', '#previousFunction', function () {
            if (loginNameEdit == 1) {
                $('#textLoginName,#ddlCompanyName').prop('disabled', false);              
            }
            else {
                $('#textLoginName,#ddlCompanyName').prop('disabled', true);               
            }

            let errorElement = ['#LoginName-error', '#LoginPassword-error', '#ConfirmPassword-error', '#Company-error', '#UserName-error', '#Role-error', '#Email-error', '#AlertMode-error', '#SessionTimeout-error', '#MobilePre-error', '#Mobile-error', '#treeList-error', '#dashboard-error'];
            errorElement.forEach(element => {
                $(element).text('').removeClass('field-validation-error');
            });

        });

        $(document).on('click', '#SaveFunction', commonDebounce(async function (e) {
            var res = ($('#ddlLoginType').val().toLowerCase() == "ad")
                ? await EncryptPassword(generateRandomString(5))
                : $('#userPassword').val();
            $('#userPassword').val(res);
            ValidateAll(false);
        }, 800));

        $(document).on('click', '#confirmDeleteButton', function () {
            btnCrudDiasable('confirmDeleteButton');
        })

        $(document).on('click', '#btnCancle', function () {
            $("#treeview").empty();
        })

        $(document).on('change', '#chk-LDAP', function () {
            if ($(this).is(':checked')) {
                $('#mobileInputContainer').show();
            } else {
                $('#mobileInputContainer').hide();
                $('#mobilepre,#mobilenum').val('');      
                $('#mobilepre').val('').trigger('change')
                $('#Mobile-error,#MobilePre-error').text('').removeClass('field-validation-error');              
                //$('#textAlertMode').val('');
                //$('#AlertMode-error').text('').removeClass('field-validation-error')
            }
        });

        $(document).on('click', '#btnCreate', function () {
            loginNameEdit = 1;
            userData = {};
            isCreate = true;
            const errorElement = ['#LoginName-error', '#LoginPassword-error', '#ConfirmPassword-error', '#Company-error', '#UserName-error', '#Role-error', '#Email-error', '#AlertMode-error', '#SessionTimeout-error', '#AdDomain-error', '#AdUser-error', '#LoginType-error', '#MobilePre-error', '#Mobile-error', '#treeList-error', '#dashboard-error'];

            clearInputFields('example-form', errorElement);
            btnCrudEnable('SaveFunction')
            $("#example-form-t-0").trigger("click");
            $('#ddlRoleName').empty().val('');
            $('#password-group, #login_hide').show();
            $('#ad_hide, #mobileInputContainer').hide();
            $('#dashboardInputContainer').addClass('d-none')
            $('#ddlCompanyName, #mobilenum, #mobilepre, #dashboardList').val('');
            $('#isMobile').prop("checked", false);                  
            $("#textLoginName").prop('readonly', false);
            // $('#ddlRoleName, #ddlLoginType').prop('disabled', false);
            $('#ddlRoleName, #ddlLoginType,#textLoginName,#ddlCompanyName').prop('disabled', false);          
            $('#SaveFunction').text('Save');
            $("#CreateModal").modal('show')
        });

        $(document).on('change', '#isMobile', function () {
            if (this.checked) {
                $('#mobileInputContainer, #mobpre, #mobilenum, #mobilepre').show();
            }
            else {
                $("#mobileInputContainer, #mobpre, #mobilenum, #mobilepre").hide();             
                $('#mobilepre,#mobilenum').val('');        
                $('#MobilePre-error,#Mobile-error').text('').removeClass('field-validation-error');
            }
        });

        $(document).on('click', '#resetOk', function () {
            btnCrudDiasable('resetOk');
        });

        const treeview = $('#treeview');

        $(document).on('change', '.selecttree', function () {
            let check = this.checked;
            let id = $(this).attr('businessid');
            let funcId = $(this).attr('functionId');
            ///  let funcId = $(this).attr('functionId');
            let infraId = $(this).attr('infraId');
            JsonTreeView(check, id, funcId, infraId);
        });

        $(document).on('click', '.Workflow-Tree details > summary', function (e) {
            e.stopPropagation();

            let $details = $(this).parent();
            let isOpen = $details.attr('open') !== undefined;
            let selectInput = $details.find('input');

            if (selectInput && selectInput.length) {
                let getInputId = selectInput[0].getAttribute('id');

                if (getInputId === 'selectAll') {
                    isOpen ? SelectAllTreeViewExpended(false) : SelectAllTreeViewExpended(true);
                }
            }
        });

        // Select and Unselect All in Tree View
        $(document).on('change', '#selectAll', function () {
            let check = $('#selectAll').prop('checked');
            check ? SelectAllTreeView(true) : SelectAllTreeView(false);

            if (check === true) {
                $("#txtinfraObjectAllFlag").val("true");
                $("#selectAll").css("background-color", "");

                $('#treeList-error').text('').removeClass('field-validation-error');
            } else {

                $('#treeList-error').text('Select at least one infraobject').addClass('field-validation-error');
            }

        });

        $(document).on('change', '#treeview', function () {
            const anyChecked = $('.selecttree:checked').length > 0;

            if (anyChecked) {
                $('#treeList-error').text('').removeClass('field-validation-error');
            } else {
                $('#treeList-error').text('Select at least one infraobject').addClass('field-validation-error');
            }
        });

        $(document).on('change', '#dashboardMode', function (e) {
            if (e.target.checked) {
                $("#dashboard-error").text("").removeClass("field-validation-error");
            }

        });

        $(document).on('change', '#textProperties', function () {
            let s = this.checked;
            let id = $(this).attr('businessid');
            let funcId = $(this).attr('functionId');
            let infraId = $(this).attr('infraId');

            // Update userData.userInfraObject
            if (!userData.userInfraObject) {
                userData.userInfraObject = {};
            }
            if (!userData.userInfraObject[id]) {
                userData.userInfraObject[id] = {};
            }
            if (!userData.userInfraObject[id][funcId]) {
                userData.userInfraObject[id][funcId] = {};
            }
            userData.userInfraObject[id][funcId][infraId] = s;

            // Update the hidden input field
            $('#textProperties').val(JSON.stringify(userData.userInfraObject.properties));
        });

    $(document).on('click', '#btnGetDomainExe', function () {
        SetDomain();    
    });


        // checkbox tree view js
        $(document).on('click', '.plus', function () {
            $(this).toggleClass("minus").siblings("ul").toggle();
        })

        $(document).on('click', 'input[type=checkbox]', function () {
            $(this).siblings("ul").find("input[type=checkbox]").prop('checked', $(this).prop('checked'));
        })

        $(document).on('change', 'input[type=checkbox]', function () {
            var sp = $(this).attr("id");
            if ((sp && sp.trim() !== "")) {
                if (sp.substring(0, 4) === "c_io") {
                    var ff = $(this).parents("ul[id^=bf_l]").attr("id");
                    if ($('#' + ff + ' > li input[type=checkbox]:checked').length == $('#' + ff + ' > li input[type=checkbox]').length) {
                        $('#' + ff).siblings("input[type=checkbox]").prop('checked', true);
                        check_fst_lvl(ff);
                    }
                    else {
                        $('#' + ff).siblings("input[type=checkbox]").prop('checked', false);
                        check_fst_lvl(ff);
                    }
                }

                if (sp.substring(0, 4) === "c_bf") {
                    var ss = $(this).parents("ul[id^=bs_l]").attr("id");
                    if ($('#' + ss + ' > li input[type=checkbox]:checked').length == $('#' + ss + ' > li input[type=checkbox]').length) {
                        $('#' + ss).siblings("input[type=checkbox]").prop('checked', true);
                        check_fst_lvl(ss);
                    }
                    else {
                        $('#' + ss).siblings("input[type=checkbox]").prop('checked', false);
                        check_fst_lvl(ss);
                    }
                }
            }
       
        });

        $(document).on('click', '#btnClick', function () {
            $('#second_modal').modal('show');
            $('#second_modal').on('show.bs.modal', function () {
                $('#CreateModal').css('z-index', 1039);
                $('.modal-backdrop').show();
            });

            $('#second_modal').on('hidden.bs.modal', function () {
                $('#CreateModal').css('z-index', 1041);
            });
        });

        $(document).on('click', '#btnClose', function () {
            $('#second_modal').modal('hide');
            $('.modal-backdrop').hide();
        });

    async function getGroupByDomain(value, value2) {

        var url = RootUrl + userManageURL.domainGroups;
        var data = {};
        data.domainName = value;
        data.domainUserName = value2
        var result = await GetAsync(url, data, OnError);
        $('#ddlDomainGroup').empty();
        $('#ddlDomainGroup').append('<option value="">Select Group</option>');
        if (result.length > 0) {
            for (var index = 0; index <= result.length; index++) {
                if (result[index] != undefined) {
                    $('#ddlDomainGroup').append('<option value="' + result[index] + '">' + result[index] + '</option>');
                }

            }
        }

    }

    function roledata(data, isParent) {
        //var optionIdToRemove = "754483ba-4a02-4e7e-93cf-315d47c6822e";
        $('#ddlRoleName').empty().val(null);
        if (userRoleValue.toLowerCase() === 'siteadmin') {
            $('#ddlRoleName').append($('<option>', {
                value: '',
                text: 'Select Role'
            }));
            $.each(data, function (index, item) {
                //if (item?.id == optionIdToRemove) {
                $('#ddlRoleName').append($('<option>', {
                    "data-roleId": item?.id,
                    value: item?.id,
                    text: item?.role
                }));
                //}
            });

            if (Object.keys(userData).length) {
                if (userData?.roleName && userData?.roleName?.toLowerCase() == "siteadmin") {
                    $('#ddlRoleName').append($('<option>', {
                        value: 'siteadmin',
                        text: 'SiteAdmin'
                    }));
                    $('#ddlRoleName').prop('disabled', true);
                    //$('#ddlRoleName').val(userData?.id).trigger('change');
                    $('#ddlRoleName').val("siteadmin").trigger('change');
                } else {
                    $('#ddlRoleName').prop('disabled', false);
                    $('#ddlRoleName').val(userData?.role).trigger('change');
                }
            }
        }
        else {
            if (userRoleValue.toLowerCase() === 'superadmin') {
                $('#ddlRoleName').append($('<option>', {
                    value: '',
                    text: 'Select Role'
                }));
                $.each(data, function (index, item) {
                    if (isParent) { //|| item?.id != optionIdToRemove
                        $('#ddlRoleName').append($('<option>', {
                            "data-roleId": item?.id,
                            value: item?.id,
                            text: item?.role
                        }));
                    }
                    else if (item?.role !== 'SuperAdmin') {
                        $('#ddlRoleName').append($('<option>', {
                            "data-roleId": item?.id,
                            value: item?.id,
                            text: item?.role
                        }));
                    }
                });
                if (Object.keys(userData).length) {
                    $('#ddlRoleName').val(userData?.role).trigger('change');
                }
            }
            else {

                $('#ddlRoleName').append($('<option>', {
                    value: '',
                    text: 'Select Role'
                }));

                $.each(data, function (index, item) {
                    // if (item.id != optionIdToRemove) {
                    $('#ddlRoleName').append($('<option>', {
                        "data-roleId": item?.id,
                        value: item?.id,
                        text: item?.role
                    }));
                    // }
                });
                if (Object.keys(userData).length) {
                    $('#ddlRoleName').val(userData?.role).trigger('change');
                }
            }
        }
    }
    function SelectAllTreeViewExpended(open) {
        $('#treeview details').attr('open', open);
    }
    function infraObjectData(selectedCount, bf) {
        if (selectedCount === bf.assignedInfraObjects.length) {
            bf.isAll = true;
            bf.isPartial = false;
        } else if (selectedCount < bf.assignedInfraObjects.length) {
            if (selectedCount !== 0) {
                bf.isAll = false;
                bf.isPartial = true;
            }
            else {
                bf.isAll = false;
                bf.isPartial = false;
            }
        }
    }
    function businessFunctionCondition(selectedBusinessFCount, selectedBusinessPartialCount, d) {
        if (selectedBusinessFCount === d.assignedBusinessFunctions.length) {
            d.isAll = true;
            d.isPartial = false;
        } else if (selectedBusinessPartialCount !== 0 && (selectedBusinessPartialCount === d.assignedBusinessFunctions.length || selectedBusinessPartialCount < d.assignedBusinessFunctions.length)) {
            d.isAll = false;
            d.isPartial = true;
        } else if (selectedBusinessFCount < d.assignedBusinessFunctions.length && selectedBusinessFCount !== 0) {
            d.isAll = false;
            d.isPartial = true;
        }
        else if (selectedBusinessFCount > 0 && selectedBusinessPartialCount === 0) {
            d.isAll = true;
            d.isPartial = false;
        } else if (selectedBusinessFCount > 0 && selectedBusinessPartialCount > 0) {
            d.isAll = false;
            d.isPartial = true;
        } else if (selectedBusinessFCount === 0 && selectedBusinessPartialCount === 0) {
            d.isAll = false;
            d.isPartial = false;
        }
    }
    function JsonTreeView(s, id, funcId, infraId) {

        jsonData.assignedBusinessServices.forEach((d) => {
            if (id && funcId && !infraId) {
                if (d.id === id) {
                    d.assignedBusinessFunctions.forEach((f) => {
                        if (f.id === funcId) {
                            f.isAll = s;
                            f.isPartial = false;

                            f.assignedInfraObjects.forEach((infra) => {
                                infra.isSelected = s;

                                if (s) $('#treeList-error').text('').removeClass('field-validation-error')
                            });

                            let selectedBusinessFCount = d.assignedBusinessFunctions.reduce(function (count, busimessFunction) {
                                return count + (busimessFunction.isAll ? 1 : 0);
                            }, 0);

                            let selectedBusinessPartialCount = d.assignedBusinessFunctions.reduce(function (count, busimessFunction) {
                                return count + (busimessFunction.isPartial ? 1 : 0);
                            }, 0);

                            businessFunctionCondition(selectedBusinessFCount, selectedBusinessPartialCount, d);

                            //if (infraId || s === false)
                            //    if (infraId) {
                            //        f.assignedInfraObjects.forEach((infra) => {
                            //            //if (infra.id === infraId)
                            //            if (infra.id != null) {
                            //                infra.isSelected = s;
                            //            }
                            //        });
                            //    }
                            //var selectedBFCount = d.assignedBusinessFunctions.reduce(function (count, busimessFunction) {
                            //    return count + (busimessFunction.isAll ? 1 : 0);
                            //}, 0);

                            //if (selectedBFCount === d.assignedBusinessFunctions.length) {
                            //    d.isAll = true;
                            //    d.isPartial = true;
                            //} else if (selectedBFCount > 0) {
                            //    d.isAll = false;
                            //    d.isPartial = false;
                            //} else {
                            //    d.isAll = false;
                            //    d.isPartial = false;
                            //}
                        }

                    });
                }
            } else if (d.id === id && !funcId && !infraId) {
                d.isAll = s;
                d.isPartial = s;
                d.assignedBusinessFunctions.forEach((bf) => {
                    if (bf != null) {
                        //d.isAll = s;
                        //d.isPartial = s;
                        bf.isAll = s;
                        bf.isPartial = s;
                        bf.assignedInfraObjects.forEach((infra) => {
                            if (infra.id != null) {
                                infra.isSelected = s;

                                if (s) $('#treeList-error').text('').removeClass('field-validation-error')
                            }
                        });
                    }
                });
            }
            else if (d.id === id && funcId && infraId != null) {
                if (s === false) {
                    d.assignedBusinessFunctions.forEach((bf) => {
                        if (bf != null) {
                            bf.assignedInfraObjects.forEach((infra) => {
                                if (infra.id === infraId) {
                                    infra.isSelected = s;

                                    if (s) $('#treeList-error').text('').removeClass('field-validation-error')
                                }

                                let selectedInfraCount = bf.assignedInfraObjects.reduce(function (count, infra) {
                                    return count + (infra.isSelected ? 1 : 0);
                                }, 0);

                                infraObjectData(selectedInfraCount, bf)

                                let selectedBusinessFCount = d.assignedBusinessFunctions.reduce(function (count, busimessFunction) {
                                    return count + (busimessFunction.isAll ? 1 : 0);
                                }, 0);

                                let selectedBusinessPartialCount = d.assignedBusinessFunctions.reduce(function (count, busimessFunction) {
                                    return count + (busimessFunction.isPartial ? 1 : 0);
                                }, 0);

                                businessFunctionCondition(selectedBusinessFCount, selectedBusinessPartialCount, d);
                            });
                        }
                    });
                }
                else if (s === true) {
                    d.assignedBusinessFunctions.forEach((bf) => {
                        if (bf != null) {
                            bf.assignedInfraObjects.forEach((infra) => {
                                if (infra.id === infraId) {
                                    infra.isSelected = s;

                                    if (s) $('#treeList-error').text('').removeClass('field-validation-error')
                                }

                                let selectedCount = bf.assignedInfraObjects.reduce(function (count, infra) {
                                    return count + (infra.isSelected ? 1 : 0);
                                }, 0);


                                infraObjectData(selectedCount, bf)

                                let selectedBusinessFCount = d.assignedBusinessFunctions.reduce(function (count, busimessFunction) {
                                    return count + (busimessFunction.isAll ? 1 : 0);
                                }, 0);

                                let selectedBusinessPartialCount = d.assignedBusinessFunctions.reduce(function (count, busimessFunction) {
                                    return count + (busimessFunction.isPartial ? 1 : 0);
                                }, 0);

                                businessFunctionCondition(selectedBusinessFCount, selectedBusinessPartialCount, d)
                            });
                        }
                    });
                }
                else {
                    d.isAll = s;
                    d.isPartial = s;
                    d.assignedBusinessFunctions.forEach((bf) => {
                        if (bf != null) {
                            d.isAll = s;
                            d.isPartial = s;
                            bf.isAll = s;
                            bf.isPartial = s;
                            bf.assignedInfraObjects.forEach((infra) => {
                                if (infra.id == infraId) {
                                    infra.isSelected = s;

                                    if (s) $('#treeList-error').text('').removeClass('field-validation-error')
                                    d.assignedBusinessFunctions.forEach((bf) => {
                                        if (bf.id === funcId) {
                                            bf.isSelected = s;
                                        }
                                    });
                                }
                            });
                        }
                    });
                }
            }
        });
        const allBusinessServicesChecked = jsonData.assignedBusinessServices.every(service => service.isAll);

        if (allBusinessServicesChecked == true) {
            $("#txtinfraObjectAllFlag").val("true");
        }
        else {
            $("#txtinfraObjectAllFlag").val("false");
        }

        $("#selectAll").prop("checked", allBusinessServicesChecked);

        if (allBusinessServicesChecked) {
            jsonData.isAll = true;
            //$("#selectAll").css("background-color", "");
        } else {
            jsonData.isAll = false;
            //$("#selectAll").css("background-color", "grey");
        }

        $("#treeview").empty();
        $('#textProperties').val(JSON.stringify(jsonData));
        createTreeView($("#treeview"), jsonData, id);
        var jsonString = $('#textProperties').val();
        var validateData = JSON.stringify(jsonData);
        filterJSONData(validateData);
        areAllValuesFalse(validateData);
    }
    function SelectAllTreeView(check) {
        $('#selectAll').prop('checked', check);
        if (jsonData.assignedBusinessServices != undefined && jsonData !== '') {
            if (jsonData.assignedBusinessServices.length >= 1) {
                jsonData.assignedBusinessServices.forEach((d) => {
                    d.isAll = check;
                    d.isPartial = check;
                    d.assignedBusinessFunctions.forEach((f) => {
                        f.isAll = check;
                        f.isPartial = check;
                        f.assignedInfraObjects.forEach((infra) => {
                            infra.isSelected = check;
                        });
                    });
                });
                jsonData.isAll = check;

                $("#treeview").empty();
                $('#textProperties').val(JSON.stringify(jsonData));
                createTreeView($("#treeview"), jsonData);
                $("#treeview").enable = false;
            }
        }
    }
    function areAllValuesFalse(data) {
        var count = 0;
        if (userRoleValue != 'SiteAdmin') {
            var checkData = JSON.parse(data);
            if (checkData.isAll !== true) {
                if (checkData?.assignedBusinessServices && checkData?.assignedBusinessServices.length) {
                    checkData && checkData.assignedBusinessServices.forEach((bS) => {
                        bS.assignedBusinessFunctions.forEach((bF) => {
                            bF.assignedInfraObjects.forEach((infra) => {
                                if (infra.isSelected == true) {
                                    count++;
                                }
                            })
                        })
                    })
                } else {
                    count = 1
                }
            } else {
                count = 1
            }
            $('#textProperties').val(data);

            if (count == 0) return false
        }

        // If we haven't found any true values, then all values are false
        return true;
    }
    function updateTreeView(userData) {
        $("#treeview").empty();

        var userInfraObject = userData?.properties && JSON.parse(userData?.properties);

        jsonData = userInfraObject
        if (userRoleValue != 'SiteAdmin') {
            var container = $("#treeview");
            container.empty();
            createTreeView($("#treeview"), userInfraObject)
        }

        if (userInfraObject?.isAll == true) {
            $("#selectAll").prop('checked', true);
            //$("#selectAll").css("background-color", "");
        } else {
            $("#selectAll").prop('checked', false);
            //$("#selectAll").css("background-color", "grey");
        }

        jsonData?.assignedBusinessServices?.forEach((data) => {
            if (!data.isAll && !data.isPartial) {
                $("#selectAll").prop('checked', false);
                //$("#selectAll").css("background-color", "grey");
            }
        });

    }

    async function treeListView() {
        $("#treeview").empty();
        let companyId = $('#ddlCompanyName').children(":selected").attr('id')
        let isParentCompany = $('#ddlCompanyName').children(":selected").data('isparent')
        let roleName = $("#ddlRoleName :selected").text()
        let data = {};

        if (isParentCompany?.toLowerCase() == 'false') data.companyId = companyId

        let newData = data;

        //if (userRoleValue === "SuperAdmin" || userRoleValue === "SiteAdmin") {
        await $.ajax({
            url: RootUrl + userManageURL.infraObjectList,
            data: data,
            method: 'GET',
            dataType: 'json',
            success: function (result) {
                if (result) {
                    jsonData = result;
                    JSONDataForClickEditButton = jsonData;

                    populateTreeView(jsonData);
                    $('#selectAllOpen details').removeAttr('open');
                }
            },
            error: function (error) {
                console.error('Error:', error);
            }
        });
        //}
        if (!isCreate) {
            updateTreeView(userData);
        }

        if (roleName === "SuperAdmin") {
            SelectAllTreeView(true);
            $(".selecttree,#selectAll").prop("disabled", true);         
            $("#txtinfraObjectAllFlag").val("true");
        } else if (roleName === "Administrator") {
            //    SelectAllTreeView(false);
            $(".selecttree,#selectAll").prop("disabled", false);
            $("#txtinfraObjectAllFlag").val("false");
        } else {
            filterJSONData(jsonData)
            //  SelectAllTreeView(false);
            $(".selecttree,#selectAll").prop("disabled", false);          
            $("#txtinfraObjectAllFlag").val("false");
        }

        //else {
        //    await $.ajax({
        //        url: RootUrl + "Admin/User/GetUserInfraByUser",
        //        method: 'GET',
        //        data: {
        //            data: loggedInUserId
        //        },
        //        dataType: 'json',
        //        success: function (data) {
        //            //console.log(data)
        //            if (data) {
        //                infraData = data;
        //                jsonData = JSON.parse(infraData.properties);
        //                //console.log(jsonData);
        //                JSONDataForClickEditButton = JSON.parse(infraData.properties);

        //                if (isCreate == true) {
        //                    filterJSONData(jsonData);
        //                    var filteredDataJson = {
        //                        "isAll": jsonData.isAll, // Set to true if needed
        //                        "assignedBusinessServices": []
        //                    };
        //                    $.each(jsonData.assignedBusinessServices, function (indexService, service) {
        //                        var filteredService = {
        //                            id: service.id,
        //                            name: service.name,
        //                            isAll: service.isAll,
        //                            isPartial: service.isPartial,
        //                            assignedBusinessFunctions: []
        //                        };

        //                        $.each(service.assignedBusinessFunctions, function (indexFunction, func) {
        //                            var filteredFunction = {
        //                                id: func.id,
        //                                name: func.name,
        //                                isAll: func.isAll,
        //                                isPartial: func.isPartial,
        //                                assignedInfraObjects: []
        //                            };

        //                            $.each(func.assignedInfraObjects, function (indexObject, infraObject) {
        //                                if (infraObject.isSelected) {
        //                                    filteredFunction.assignedInfraObjects.push({
        //                                        id: infraObject.id,
        //                                        name: infraObject.name,
        //                                        isSelected: infraObject.isSelected
        //                                    });
        //                                }
        //                            });

        //                            if (filteredFunction.assignedInfraObjects.length > 0) {
        //                                filteredService.assignedBusinessFunctions.push(filteredFunction);
        //                            }
        //                        });
        //                        if (filteredService.assignedBusinessFunctions.length > 0) {
        //                            filteredDataJson.assignedBusinessServices.push(filteredService);
        //                            jsonData = filteredDataJson;
        //                        }
        //                    });

        //                    jsonData = filteredDataJson;

        //                    $.each(jsonData.assignedBusinessServices, function (index, service) {
        //                        service.isAll = false;
        //                        service.isPartial = false;

        //                        $.each(service.assignedBusinessFunctions, function (index, func) {
        //                            func.isAll = false;
        //                            func.isPartial = false;

        //                            $.each(func.assignedInfraObjects, function (index, infraObject) {
        //                                infraObject.isSelected = false;
        //                            });
        //                        });
        //                    });
        //                }

        //                populateTreeView(jsonData);
        //                $('#selectAllOpen details').removeAttr('open');
        //                $('#textProperties').val(JSON.stringify(jsonData));
        //            }
        //        },
        //        error: function (error) {
        //            console.error('Error', error);
        //        }
        //    });
        //}

    }
    function populateTreeView(jsonData) {
        var container = $("#treeview");
        container.empty(); // Clear any existing content   
        const newNode = document.querySelector('.new-node');
        if (newNode) {
            newNode.remove();
        }

        if (jsonData && jsonData.assignedBusinessServices.length) {
            $("#selectAll").css("background-color", "");
            createTreeView(container, jsonData);
            const treeElement = document.querySelector('.Workflow-Tree');
            treeElement.classList.remove("d-none");
        } else {
            //$("#selectAll").prop("checked", true);
            //container.text("No data available."); 
            const treeElement = document.querySelector('.Workflow-Tree');
            treeElement.classList.add("d-none")
            const newDiv = document.createElement('div');
            newDiv.className = 'new-node';
            const overallBS_noData = `<li class="list-group-item text-center" id="noDataFoundMessage">
            <img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">
        </li>`
            newDiv.innerHTML = overallBS_noData;
            treeElement.parentNode.insertBefore(newDiv, treeElement.nextSibling);
        }
    }
    function createTreeView(container, userInfraObject, serviceId = '') {
        var container = $("#treeview");
        container.empty();
        if (JSONDataForClickEditButton?.assignedBusinessServices?.length > 0) {
            JSONDataForClickEditButton?.assignedBusinessServices?.forEach(function (JSONData) {
                userInfraObject?.assignedBusinessServices?.forEach(function (infraObject) {
                    if (JSONData?.id === infraObject?.id) {
                        JSONData.isAll = infraObject.isAll;
                        JSONData.isPartial = infraObject.isPartial;

                        JSONData?.assignedBusinessFunctions?.forEach(function (jsonDataFunction) {
                            infraObject?.assignedBusinessFunctions?.forEach(function (infraFunction) {

                                if (jsonDataFunction?.id === infraFunction?.id) {
                                    jsonDataFunction.isAll = infraFunction.isAll;
                                    jsonDataFunction.isPartial = infraFunction.isPartial;

                                    jsonDataFunction?.assignedInfraObjects?.forEach(function (jsonDataInfa) {
                                        infraFunction?.assignedInfraObjects?.forEach(function (infra) {

                                            if (jsonDataInfa?.id === infra?.id) {
                                                jsonDataInfa.isSelected = infra.isSelected;
                                            }
                                        });
                                    });

                                }

                            });
                        });
                    }
                });
            });
            userInfraObject = [];
            jsonData = [];
            userInfraObject = JSONDataForClickEditButton;
            jsonData = JSONDataForClickEditButton;
        }


        if (userInfraObject?.isAll === true) {
            $("#selectAll").prop('checked', true);
        } else {
            $("#selectAll").prop('checked', false);
        }

        //setTimeout(() => {
        if (userInfraObject?.assignedBusinessServices?.length > 0) {
            userInfraObject.assignedBusinessServices.forEach((service) => {

                const serviceDetails = $(`<details id=${service.id} ${serviceId === service.id ? 'open' : null}></details>`);
                const serviceSummary = $('<summary></summary>');
                const serviceCheckbox = $(`<input type="checkbox" style="${!service.isAll ? service.isPartial ? "background-color:grey" : " " : ""}" class="form-check-input selecttree" onchange="selectTree(this)">`);
                serviceCheckbox.attr('businessId', service.id);
                serviceCheckbox.prop('checked', service.isAll);
                serviceSummary.append(serviceCheckbox);
                serviceSummary.append(' ' + service.name);

                if (service.assignedBusinessFunctions.length > 0) {
                    const functionList = $('<ul class="tree"></ul>');
                    createFunctions(functionList, service.assignedBusinessFunctions, service.id);
                    serviceDetails.append(functionList);
                }

                serviceDetails.append(serviceSummary);
                container.append(serviceDetails[0]);
            });
        }
        //}, 500)
    }
    function selectTree(checkbox) {
        let check = checkbox.checked;
        var businessId = $(checkbox).attr("businessid");
        var functionid = $(checkbox).attr("functionid");
        var infraid = $(checkbox).attr("infraid");
        JsonTreeView(check, businessId, functionid, infraid);
    }

    var checkboxElement = checkboxElement?.prop("checked", s);
    checkboxElement?.on("change", function () {
        selectTree(this);
    });
    function createFunctions(container, functions, id) {
        functions.forEach((func) => {
            const functionDetails = $('<details></details>');
            const functionSummary = $('<summary></summary>');
            const functionCheckbox = $(`<input type="checkbox" style="${!func.isAll ? func.isPartial ? "background-color:grey" : " " : ""}" class="form-check-input selecttree" onchange="selectTree(this)">`);
            functionCheckbox.prop('checked', func.isAll);
            functionCheckbox.attr('businessId', id);
            functionCheckbox.attr('functionId', func.id);
            functionSummary.append(functionCheckbox);
            functionSummary.append(' ' + func.name);

            if (func.assignedInfraObjects.length > 0) {
                const objectList = $('<ul class="tree"></ul>');
                createObjects(objectList, func.assignedInfraObjects, id, func.id);
                functionDetails.append(objectList);
                functionDetails.attr('open', 'open'); // To keep "assignedInfraObjects" expanded
            }
            functionDetails.append(functionSummary);
            container.append(functionDetails[0]);
        });
    }

    function createObjects(container, objects, parentId, functionId) {
        objects.forEach((obj) => {
            const objectDetails = $('<ul open></ul>'); // Add the "open" attribute
            const objectSummary =
                $('<summary style="list-style-type: none; padding : 1px 22px"></summary>'); // Add inline style to hide the arrow
            const objectCheckbox = $('<input type="checkbox" class="form-check-input selecttree" onchange="selectTree(this)">');
            objectCheckbox.prop('checked', obj.isSelected);
            objectCheckbox.attr('businessId', parentId);
            objectCheckbox.attr('functionId', functionId);
            objectCheckbox.attr('infraId', obj.id);

            objectSummary.append(objectCheckbox);
            objectSummary.append(' ' + obj.name);

            objectDetails.append(objectSummary);
            container.append(objectDetails[0]);
        });
    }

    async function ValidateAll(value) {
        $('#textProperties').val(JSON.stringify(jsonData));
        var type = $("#ddlLoginType").val();
        var type_error = $('#LoginType-error');
        var loginId = $("#textLoginId").val();
        var loginName = $("#textLoginName").val().toLowerCase();
        var loginPassword = $("#encriptedPassword").val()
        var confirmPassword = $("#userConfirmPassword").val()
        var companyName = $("#ddlCompanyName").val();
        var roleName = $("#ddlRoleName :selected").text()
        var encPwd = $("#encriptedPassword").val();
        var userName = $("#textUserName").val();
        var email = $("#textEmail").val();
        //var mobile = $("#textAlertMode").val();
        const IsGroup = $("#ADGroup").prop('checked');
        const isMobileChk = $("#chk-LDAP").prop('checked');
        const mobilePre = $("#mobilepre").val();
        const mobileNo = $("#mobilenum").val();
        if (isMobileChk == true && mobilePre != '' && mobileNo != '') {

            if (mobilePre && mobileNo) {
                const comMobile = mobilePre + '-' + mobileNo;
                $('#comMobile').val(comMobile);
            }
        } else {
            $('#comMobile').val('');
        }
        var sessionTimeout = $("#textSessionTimeout").val();
        var errorElementCompany = $('#Company-error');
        var errorElementRole = $('#Role-error');
        var Properties = $('#textProperties').val();
        var adDomain = $('#ddlDomain').val();
        
        var infraAllFlag = $('#txtinfraObjectAllFlag').val();

        var flag = true;
        var flagTree = true;
        var btnSave = $('#SaveFunction').text();
        var isName = await validateLoginName(loginName, loginId, userManageURL.nameExist);
        var isloginPassword = "";
        var isconfirmPassword = "";
        if (type == 'AD') {          
            isloginPassword = true;
            isconfirmPassword = true;  
            if (IsGroup) {
                var adUser = $('#ddlDomainGroup').val();
                var isADUserName = await validateDropDown(adUser, 'Select group', $('#AdGroup-error'));
            } else {
                var adUser = $('#ddlDomainUser').val();
                var isADUserName = await validateDropDown(adUser, 'Select user', $('#AdUser-error'));
            }
          


        } else {
            isloginPassword = await validateLoginPassword(loginPassword, $('#LoginPassword-error'));
            isconfirmPassword = await validateConfirmPassword(confirmPassword, $('#ConfirmPassword-error'));
        }

        var iscompanyName = await validateDropDown(companyName, 'Select company name', errorElementCompany);
        var isroleName = $("#ddlRoleName").prop('disabled') ? true : await validateDropDown(roleName, 'Select role', errorElementRole);
        var isTypename = await validateDropDown(type, 'Select authentication type ', type_error);
        var isADDomainName = (type?.toLowerCase() == 'ad') && await validateDropDown(adDomain, 'Select domain', $('#AdDomain-error'));
   
       


        if (value && ($('#textLoginName').is(':visible') || ($('#ADGroupName').is(':visible')))) {
            if (btnSave === 'Save') {
                if (isName && iscompanyName && isroleName && isTypename && isconfirmPassword && isloginPassword && (type == 'AD' ? (isADDomainName && isADUserName) : true)) {
                    let ConformPwdDecrypt = ""
                    if (confirmPassword) {
                        ConformPwdDecrypt = await DecryptPassword(confirmPassword);
                    }

                    let LoginPwdDecrypt = await DecryptPassword(loginPassword);
                    if (LoginPwdDecrypt.decrypt == ConformPwdDecrypt?.decrypt) {
                        if (loginPassword?.length < 60) {
                            let encryptedloginPass = await EncryptPassword(loginName + loginPassword);
                            $("#userPassword").val(encryptedloginPass);
                        }
                        if (confirmPassword?.length < 60) {
                            let encryptedconfirmPass = await EncryptPassword(loginName + confirmPassword);
                            $("#userConfirmPassword").val(encryptedconfirmPass);
                        }
                        treeListView();
                        form.steps('next')
                    }
                    else {
                        var decryptConformPwd = ConformPwdDecrypt.decrypt;
                        await validateConfirmPassword(decryptConformPwd);
                    }
                }
                else if (isName && iscompanyName && isroleName && (type == 'AD' ? (isADDomainName && isADUserName) : true) && type == 'AD') {
                    treeListView();
                    form.steps('next');
                }
            }
            else {
                if (isName && iscompanyName && isroleName) {
                    if (loginPassword?.length < 60) {
                        let loginPass = await EncryptPassword(loginName + loginPassword);
                        $("#userPassword").val(loginPass)
                    }
                    if (confirmPassword?.length < 60) {
                        let confirmPass = await EncryptPassword(loginName + confirmPassword);
                        $("#userConfirmPassword").val(confirmPass)
                    }
                    treeListView();
                    form.steps('next')
                }
            }
            flag = false;
        }

        if (flag) {
            var isuserName = await validateUserName(userName);
            var isemail = await validateEmail(email);
            const isMobileChk = $("#chk-LDAP").prop('checked');
            const isDashboardChk = $("#dashboardMode").prop('checked');
            const isMobilePre = isMobileChk ? await validateMobilePre(mobilePre) : true;
            const isMobileNo = isMobileChk ? await validateMobile(mobileNo) : true;
            const isDashboard = isDashboardChk ? await validateDropDown($('#dashboardList').val(), 'Select dashboard', $('#dashboard-error')) : true;
            //var ismobile = await validateMobile(mobile);
            var isChecked = $('#chk-workflowtype').prop('checked');
            $("#isPreferedChk").val(isChecked);
            var issessionTimeout = await validateSessionTimeout(sessionTimeout);

            if (value && $('#textEmail').is(':visible')) {
                ($('#chk-LDAP').prop('checked'))
                    ? isuserName && isemail && isMobilePre && isMobileNo && isDashboard && issessionTimeout
                        ? form.steps('next')
                        : null
                    : isuserName && isemail && issessionTimeout && isDashboard
                        ? form.steps('next')
                        : null;
            } else {
                form.steps('next');
            }
            flagTree = true
        }
        if (flagTree) {
            if (userRoleValue.toLowerCase() !== "siteadmin") {
                var isTreeList = Properties && await areAllValuesFalse(Properties);
                var infraFlagValidate = JSON.parse(Properties);
                if (infraFlagValidate.isAll == true) {
                    $("#txtinfraObjectAllFlag").val("true");
                }
            }
            if (userRoleValue.toLowerCase() == "siteadmin") {
                var isAllTrue = '{"isAll":true}';
                $('#textProperties').val(isAllTrue);
                $("#txtinfraObjectAllFlag").val("true");
                isTreeList = true;
            }
        }
        $('#textLoginName,#ddlCompanyName').prop('disabled', false);
       
        if (userData?.roleName && userData?.roleName?.toLowerCase() !== "siteadmin") $('#ddlRoleName').prop('disabled', false);
        if (!value) {

            if (!jsonData.isAll) {
                let businessServiceObject = {
                    isAll: false,
                    assignedBusinessServices: []
                };

                jsonData?.assignedBusinessServices?.forEach(function (bservice) {
                    if (bservice.isAll) {
                        businessServiceObject.assignedBusinessServices.push(bservice);
                    } else {
                        let businessFunctionArray = [];
                        bservice?.assignedBusinessFunctions?.forEach(function (bfunction) {
                            if (bfunction.isAll) {
                                businessFunctionArray.push(bfunction);
                            } else {
                                let infraObjectArray = [];
                                bfunction?.assignedInfraObjects?.forEach(function (infra) {
                                    if (infra.isSelected) {
                                        infraObjectArray.push(infra);
                                    }
                                });
                                if (infraObjectArray.length > 0) {
                                    let clonedBusinessFunction = Object.assign({}, bfunction);
                                    clonedBusinessFunction.assignedInfraObjects = infraObjectArray;
                                    businessFunctionArray.push(clonedBusinessFunction);
                                }
                            }
                        });

                        if (businessFunctionArray.length > 0) {
                            let clonedBusinessService = Object.assign({}, bservice);
                            clonedBusinessService.assignedBusinessFunctions = businessFunctionArray;
                            businessServiceObject.assignedBusinessServices.push(clonedBusinessService);
                        }
                    }
                });

                if (jsonData?.assignedBusinessServices && !jsonData?.assignedBusinessServices.length) {
                    businessServiceObject.isAll = true
                    $("#txtinfraObjectAllFlag").val("true");
                }

                $('#textProperties').val(JSON.stringify(businessServiceObject));

            }

            setTimeout(() => {
                if (!isTreeList && $("#ddlRoleName :selected").text()?.toLowerCase() !== 'superadmin') {
                    btnCrudEnable('SaveFunction');

                    $('#treeList-error').text('Select at least one infraobject').addClass('field-validation-error')
                    $(".content").scrollTop($(".content")[0].scrollHeight);

                } else {
                    let form = $("#example-form")
                    btnCrudDiasable('SaveFunction');

                    $('#treeList-error').text('').removeClass('field-validation-error')

                    if ($("#ddlRoleName :selected").text()?.toLowerCase() === 'superadmin') isTreeList = true

                    let createUser = {
                        "Id": $("#textLoginId").val(),
                        "LoginType": $("#loginType").val(),
                        "CompanyId": $("#textCompanyId").val(),
                        "CompanyName": $("#ddlCompanyName").val(),
                        "LoginName": $("#textLoginName").val(),                     
                        "LoginPassword": $("#userPassword").val(),
                        "ConfirmPassword": $("#userConfirmPassword").val(),
                        "EncryptPassword": $("#encriptedPassword").val(),
                        "Role": $("#textRoleId").val(),
                        "RoleName": $("#ddlRoleName :selected").text(),
                        "IsLock": $("#loginType").val(),
                        "IsGroup": IsGroup,
                        "IsReset": "",
                        "InfraObjectAllFlag": $("#txtinfraObjectAllFlag").val(),
                        "SessionTimeout": $("#textSessionTimeout").val(),
                        "IsVerify": "",
                        "TwoFactorAuthentication": "",
                        "Url": $("#dashboardList").val(),
                        "IsDefaultDashboard": $("#dashboardModeValue").val(),
                        "UserInfoCommand": {
                            "UserName": $("#textUserName").val(),
                            "Email": $("#textEmail").val(),
                            "AlertMode": $("#chk-AD").val() ? $("#chk-AD").val() : $("#chk-LDAP").val(),
                            "Mobile": $("#comMobile").val(),
                            "IsPreferredMode": $("#chk-workflowtype").is(":checked"), //$("#chk-workflowtype").val(),//? $("#chk-workflowtype").val() : $("#isPreferedChk").val(),
                        },
                        "UserInfraObjectCommand": {
                            "Properties": $("#textProperties").val(),
                            "UserId": $("#textUserId").val(),
                        },

                    }

                    if (loginId) {
                        ($('#chk-LDAP').checked)
                            ? isName && isuserName && iscompanyName && isroleName && isemail && ismobile && issessionTimeout && isTreeList
                                ? createOrUpdateUser(createUser) //form.trigger('submit')
                                : null
                            : isName && isuserName && iscompanyName && isroleName && isemail && issessionTimeout && isTreeList
                                ? createOrUpdateUser(createUser) //form.trigger('submit')
                                : null;
                    } else {
                        var isLoginPwd = ($('#ddlLoginType').val().toLowerCase() == "ad") ? true : isloginPassword;
                        ($('#chk-LDAP').checked)
                            ? (isName || isADgroupName) && isuserName && isLoginPwd && iscompanyName && isroleName && isemail && ismobile && issessionTimeout && isTreeList
                                ? createOrUpdateUser(createUser) //form.trigger('submit')
                                : null
                            : (isName || isADgroupName) && isuserName && isLoginPwd && iscompanyName && isroleName && isemail && issessionTimeout && isTreeList
                                ? createOrUpdateUser(createUser) //form.trigger('submit')
                                : null;
                    }
                }
            }, 300)
        }
    }

    async function createOrUpdateUser(createUser) {
        let response = await $.ajax({
            type: "POST",
            url: RootUrl + userManageURL.createOrUpdate,
            dataType: "json",
            headers: {
                'RequestVerificationToken': await gettoken()
            },
            //contentType: 'application/json',
            data: createUser,
            //traditional: true
        });

        if (response.success) {
            $("#CreateModal").modal('hide');
            notificationAlert("success", response.message.message);
            dataTable.ajax.reload();
            setTimeout(() => {
                if (editedLoginID.trim() === loggedInUserId && userRoleValue !== $("#ddlRoleName :selected").text()) {
                    notificationAlert("warning", "Sesison invalid");
                    setTimeout(() => {
                        window.location.assign(RootUrl + 'Account/Logout');
                    }, 2500)
                }
            }, 2500);
        } else {
            $("#CreateModal").modal('hide');
            errorNotification(response);
            dataTable.ajax.reload();
        }
    }

    async function validateMobilePre(value) {
        const errorElement = $('#MobilePre-error');

        if (!value) {
            errorElement.text('Select country code').addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true
        }
        //const validationResults = [
        //    await mobilePreUserReg(value)
        //];
        //return await CommonValidation(errorElement, validationResults);
    }

    async function validateMobile(value) {
        const errorElement = $('#Mobile-error');
        if (!value) {
            errorElement.text('Enter mobile number')
                .addClass('field-validation-error');
            return false;
        }
        else if (value) {
            const minLength = 7;
            if (value.length < minLength) {
                errorElement.text('Must be at least 7 characters')
                    .addClass('field-validation-error');
                return false;
            } else {
                errorElement.text('')
                    .removeClass('field-validation-error');
                return true;
            }
        }
        else {
            errorElement.text('')
                .removeClass('field-validation-error');
            return true;
        }
    }
    function populateModalFields(userData) {

        $('#ad_hide, #mobileInputContainer').hide();
       // $('#ADLoginName').show();
        $('#ddlLoginType').val(userData?.loginType);
        $("#loginType").val(userData?.loginType);
        $('#ddlCompanyName').val(userData?.companyName).trigger('change');       
        $('#textLoginId').val(userData?.id);
        $('#textLoginName').val(userData?.loginName);
        $('#textCompanyId').val(userData?.companyId);
        $('#textRoleId').val(userData?.role);
        $('#textUserName').val(userData?.userName);
        $('#textEmail').val(userData?.email);
        //$('#textAlertMode').val(userData.userInfo.mobile);
        if (userData.roleName == "SiteAdmin") {
            $('#chk-LDAP, #dashboardMode').prop('disabled', true);
        } else {
            $('#chk-LDAP, #dashboardMode').prop('disabled', false);
        }
        if (userData?.isDefaultDashboard) {

            $('#dashboardMode').prop('checked', userData?.isDefaultDashboard)
            $('#dashboardModeValue').val(userData?.isDefaultDashboard)
            $('#dashboardList').val(userData?.url)
            $('#dashboardInputContainer').removeClass('d-none')

        } else {

            $('#dashboardMode').prop('checked', userData?.isDefaultDashboard)
            $('#dashboardModeValue').val(userData?.isDefaultDashboard)
            $('#dashboardList').val('')
            $('#dashboardInputContainer').addClass('d-none')

        }

        $('#ddlLoginType').prop('disabled', true);

        if (userData?.mobile != null && userData?.mobile != "NA") {
            const mobileNumber = userData?.mobile.split('-');
            $('#mobilepre').val(mobileNumber[0]);
            $('#mobilenum').val(mobileNumber[1]);
            if (userData?.mobile != null) {
                $('#chk-LDAP').prop('checked', true);
                $('#mobileInputContainer').show();
            }
            else {
                $('#chk-LDAP').prop('checked', false);
                $('#mobileInputContainer').hide();
                $('#textAlertMode').val('');
                $('#AlertMode-error').text('').removeClass('field-validation-error')
            }
        }
        if (userData?.isPreferredMode) $('#chk-workflowtype').prop('checked', true)
        else $('#chk-workflowtype').prop('checked', false)


        $('#confirmation_mail').addClass('d-none')

        //if (userData.userInfo.mobile != null) {
        //    $('#chk-LDAP').prop('checked', true);
        //    $('#mobileInputContainer').show();
        //}
        //else {
        //    $('#chk-LDAP').prop('checked', false);
        //    $('#mobileInputContainer').hide();
        //    $('#textAlertMode').val('');
        //    $('#AlertMode-error').text('').removeClass('field-validation-error')
        //}

        $('#textSessionTimeout').val(userData?.sessionTimeout);
        $('#textProperties').val(userData?.properties);
        $('#textUserId').val(userData?.id);

        //updateTreeView(userData);

        let errorElement = ['#LoginName-error', '#LoginPassword-error', '#ConfirmPassword-error', '#Company-error', '#UserName-error', '#Role-error', '#Email-error', '#AlertMode-error', '#SessionTimeout-error', '#MobilePre-error', '#Mobile-error', '#treeList-error', '#dashboard-error'];
        errorElement.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });
    }

    async function validateLoginName(value, id = null, url) {
        let type = $("#ddlLoginType").val();
        const errorElement = $('#LoginName-error');

        if (!value) {
            errorElement.text('Enter login name')
                .addClass('field-validation-error');
            return false;
        }
        if (value.includes("<")) {
            $('#LoginName-error').text('Special characters not allowed').addClass('field-validation-error');
            return false;
        }
        var url = RootUrl + url;
        var data = {};
        data.loginName = value;
        data.id = id;
        const validationResults = [];

        validationResults.push(
            await ShouldNotBeginWithUnderScore(value),
            // await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumberForUser(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await secondChar(value),
            await IsNameExist(url, data, OnError)
        );

        if (type !== 'AD') {
            validationResults.push(await LoginSpecialCharValidate(value));
            validationResults.push(await ShouldNotBeginWithSpace(value));
            validationResults.push(await minMaxlength(value));
        }

        return await CommonValidation(errorElement, validationResults);
    }

    const ShouldNotBeginWithNumberForUser = (value) => { return (RegExp(/^\d+[a-zA-Z]/).test(value)) ? "Should not begin with number" : true; }

    const LoginSpecialCharValidate = (value) => {
        const regex = /^[a-zA-Z0-9_\s.]*$/;

        if (!regex.test(value)) {
            return "Special characters not allowed";
        }

        if (/^\./.test(value)) {
            return "Special characters not allowed";
        }

        if (/\.$/.test(value)) {
            return "Special characters not allowed";
        }

        return true;
    }

    async function validateDropDown(value, errorMessage, errorElement) {
        if (!value) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }

    async function validateUserName(value, id = null) {
        const errorElement = $('#UserName-error');
        if (!value) {
            errorElement.text('Enter full name')
                .addClass('field-validation-error');
            return false;
        }
        const validationResults = [
            await SpecialCharValidate(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithSpace(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            //await IsNameExist(value, id)
        ];
        return await CommonValidation(errorElement, validationResults);
    }

    async function validateEmail(value, id = null) {
        const errorElement = $('#Email-error');
        let format = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/; // /^[a-zA-Z0-9._]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
        const domainPart = value?.split('@')[1]; // Get everything after the '@' symbol
        const domainParts = domainPart?.split('.');

        // If there are more than two domain parts, it's invalid

        if (!value) {
            errorElement.text('Enter email address')
                .addClass('field-validation-error');
            return false;
        } else if (value.length >= 321) {
            errorElement.text('Enter the value less than 320 characters')
                .addClass('field-validation-error');
            return false;
        } else if (value.length) {
            if (format.test(value) == false) {
                errorElement.text('Invalid email')
                    .addClass('field-validation-error');
                return false;
            } else if (value.charAt(0) == "." || value.charAt(0) == "_") {
                errorElement.text('Invalid email')
                    .addClass('field-validation-error');
                return false;
            } else if (domainParts.length > 2) {

                errorElement.text('Email cannot have more than one top-level domain')
                    .addClass('field-validation-error');
                return false;

            }
            else {
                errorElement.text('')
                    .removeClass('field-validation-error');
                return true;
            }
        }
        else {
            errorElement.text('')
                .removeClass('field-validation-error');
            return true;
        }

        const validationResults = [await emailRegex(value)];
        return await CommonValidation(errorElement, validationResults);

        //const errorElement = $('#Email-error');
        //if (!value) {
        //    errorElement.text('Enter email address')
        //        .addClass('field-validation-error');
        //    return false;
        //}
        //const validationResults = [
        //    //await emailRegex(value),
        //    await validEmail(value),
        //];
        //return await CommonValidation(errorElement, validationResults);
    }

    const validEmail = (value) => { return (!(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,63}$/).test(value)) ? "Invalid email" : true; }

    async function validateSessionTimeout(value, id = null) {
        const errorElement = $('#SessionTimeout-error');
        if (!value || value == 0) {
            errorElement.text('Enter session timeout')
                .addClass('field-validation-error');
            return false;
        }
        if (parseInt(value, 10) < 5) {
            errorElement.text('A minimum of 5 minutes is required') //Minimum 5 minutes required
                .addClass('field-validation-error');
            return false;
        }
        const validationResults = [
            await OnlyNum(value)
        ];
        return await CommonValidation(errorElement, validationResults);
    }

    async function IsNameExist(url, data, errorFunc) {
        return !data.loginName.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
    }

    async function ValidateLoginType(value) {
        if (value == "AD") {
            $('#ad_hide').css('display', 'block');
            $('.inhouse_div').css('display', 'none');
        } else {
            $('#ad_hide').css('display', 'none');
            $('.inhouse_div').css('display', 'block');
        }
    }

    async function SetDomainUser(value, value2) {

        var url = RootUrl + userManageURL.domainUsers;
        var data = {};
        data.domainName = value;
        data.domainUserName = value2
        var result = await GetAsync(url, data, OnError);
        $('#ddlDomainUser').empty();
        $('#ddlDomainUser').append('<option value="">Select User</option>');
        if (result.length > 0) {
            for (var index = 0; index <= result.length; index++) {
                if (result[index] != undefined) {
                    $('#ddlDomainUser').append('<option value="' + result[index] + '">' + result[index] + '</option>');
                }

            }
        }

    }

    async function GetUserInfraObjectList(user) {
        var url = RootUrl + "Admin/User/GetUserInfraObjects";
        var data = {};
        data.Id = user.Id;
        var result = await GetAsync(url, data, OnError);
        jsonData = result;
    }

  
    async function SetDomain() {
        var url = RootUrl + userManageURL.domains;
        var data = {};
        var result = await GetAsync(url, data, OnError);

        //$('#ddlDomainUser').clear;
        $('#ddlDomain').empty();
        if (result.success == true) {
            $('#ddlDomain').append('<option value=""> Select Domain</option>');

            for (var index = 0; index < result.message.length; index++) {
                $('#ddlDomain').append('<option value="' + result.message[index] + '">' + result.message[index] + '</option>');
            }

        }
        else {
            notificationAlert("warning", result.message);
        }
    }
    function filterJSONData(jsonData) {

        // Assuming jsonData is your provided JSON data
        var filteredData = {
            "isAll": jsonData.isAll, // Set to true if needed
            "assignedBusinessServices": []
        };
        $.each(jsonData.assignedBusinessServices, function (indexService, service) {
            var filteredService = {
                id: service.id,
                name: service.name,
                isAll: service.isAll,
                isPartial: service.isPartial,
                assignedBusinessFunctions: []
            };

            $.each(service.assignedBusinessFunctions, function (indexFunction, func) {
                var filteredFunction = {
                    id: func.id,
                    name: func.name,
                    isAll: func.isAll,
                    isPartial: func.isPartial,
                    assignedInfraObjects: []
                };

                $.each(func.assignedInfraObjects, function (indexObject, infraObject) {

                    if (infraObject.isSelected) {
                        filteredFunction.assignedInfraObjects.push({
                            id: infraObject.id,
                            name: infraObject.name,
                            isSelected: infraObject.isSelected
                        });
                    }
                });
                if (filteredFunction.assignedInfraObjects.length > 0) {
                    filteredService.assignedBusinessFunctions.push(filteredFunction);
                }
            });
            if (filteredService.assignedBusinessFunctions.length > 0) {
                filteredData.assignedBusinessServices.push(filteredService);
                jsonData = filteredData;
            }
        });
        jsonData = filteredData;
    }
    function check_fst_lvl(dd) {
        var ss = $('#' + dd).parent().closest("ul").attr("id");
        if ($('#' + ss + ' > li input[type=checkbox]:checked').length == $('#' + ss + ' > li input[type=checkbox]').length) {
            $('#' + ss).siblings("input[type=checkbox]").prop('checked', true);
        }
        else {
            $('#' + ss).siblings("input[type=checkbox]").prop('checked', false);
        }
    }
    function pageLoad() {
        $(".plus").on('click', function () {
            $(this).toggleClass("minus").siblings("ul").toggle();
        })
    }
});

