﻿using ContinuityPatrol.Application.Features.UserInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetDetail;

namespace ContinuityPatrol.Application.Features.User.Queries.GetDetail;

public class GetUserDetailQueryHandler : IRequestHandler<GetUserDetailQuery, UserDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IUserInfoRepository _userInfoRepository;
    private readonly IUserInfraObjectRepository _userInfraObjectRepository;
    private readonly IUserRepository _userRepository;

    public GetUserDetailQueryHandler(IMapper mapper, IUserRepository userRepository,
        IUserInfoRepository userInfoRepository, IUserInfraObjectRepository userInfraObjectRepository)
    {
        _mapper = mapper;
        _userRepository = userRepository;
        _userInfoRepository = userInfoRepository;
        _userInfraObjectRepository = userInfraObjectRepository;
    }

    public async Task<UserDetailVm> Handle(GetUserDetailQuery request, CancellationToken cancellationToken)
    {
        var user = await _userRepository.GetByReferenceIdAsync(request.Id);

        var userInfo = await _userInfoRepository.GetUserInfoByUserIdAsync(request.Id);

        var userInfraObject = await _userInfraObjectRepository.GetUserInfraObjectByUserIdAsync(request.Id);

        Guard.Against.NullOrDeactive(user, nameof(Domain.Entities.User),
            new NotFoundException(nameof(Domain.Entities.User), request.Id));

        var userDetailDto = _mapper.Map<UserDetailVm>(user);

        userDetailDto.UserInfo = _mapper.Map<UserInfoDetailVm>(userInfo);

        userDetailDto.UserInfraObject = _mapper.Map<UserInfraObjectDetailVm>(userInfraObject);

        return userDetailDto;
    }
}