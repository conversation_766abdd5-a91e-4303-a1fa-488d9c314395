using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Events.Update;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Update;

public class UpdateApprovalMatrixApprovalCommandHandler : IRequestHandler<UpdateApprovalMatrixApprovalCommand,
    UpdateApprovalMatrixApprovalResponse>
{
    private readonly IApprovalMatrixApprovalRepository _approvalMatrixApprovalRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateApprovalMatrixApprovalCommandHandler(IMapper mapper,
        IApprovalMatrixApprovalRepository approvalMatrixApprovalRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _approvalMatrixApprovalRepository = approvalMatrixApprovalRepository;
        _publisher = publisher;
    }

    public async Task<UpdateApprovalMatrixApprovalResponse> Handle(UpdateApprovalMatrixApprovalCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _approvalMatrixApprovalRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.ApprovalMatrixApproval), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateApprovalMatrixApprovalCommand),
            typeof(Domain.Entities.ApprovalMatrixApproval));

        await _approvalMatrixApprovalRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateApprovalMatrixApprovalResponse
        {
            Message = Message.Update(nameof(Domain.Entities.ApprovalMatrixApproval), eventToUpdate.ProcessName),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new ApprovalMatrixApprovalUpdatedEvent { Name = eventToUpdate.ProcessName },
            cancellationToken);

        return response;
    }
}