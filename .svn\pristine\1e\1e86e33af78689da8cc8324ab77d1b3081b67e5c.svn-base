﻿using ContinuityPatrol.Application.Hubs;
using ContinuityPatrol.Shared.Core.Domain;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Alert.Events.UserLoginEvents.Update;

public class UserLoginUpdatedEventHandler : INotificationHandler<UserLoginUpdatedEvent>
{
    private readonly IAlertRepository _alertRepository;
    private readonly IHubContext<AlertHub> _hubContext;
    private readonly IUserInfraObjectRepository _userInfraObjectRepository;
    private readonly IUserLoginRepository _userLoginRepository;
    private readonly IUserRepository _userRepository;

    public UserLoginUpdatedEventHandler(IUserRepository userRepository,
        IUserInfraObjectRepository userInfraObjectRepository, IUserLoginRepository userLoginRepository,
        IAlertRepository alertRepository, IHubContext<AlertHub> hubContext)
    {
        _userRepository = userRepository;
        _userInfraObjectRepository = userInfraObjectRepository;
        _userLoginRepository = userLoginRepository;
        _alertRepository = alertRepository;
        _hubContext = hubContext;
    }

    public async Task Handle(UserLoginUpdatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var users = await _userRepository.ListAllAsync();

        var isAllInfraUsers = users.Where(x => x.InfraObjectAllFlag).ToList();

        var assignedInfraUsers = users.Where(x => x.InfraObjectAllFlag.Equals(false)).ToList();

        var notificationList = new List<AlertHubVm>();
        foreach (var user in isAllInfraUsers)
        {
            var userLogin = await _userLoginRepository.GetUserLoginByUserId(user.ReferenceId);

            var alertCount = new List<Domain.Entities.Alert>();

            var alertMessage = new Domain.Entities.Alert();

            if (userLogin != null)
            {
                if (userLogin.LastAlertId != 0)
                {
                    userLogin.LastAlertId = createdEvent.AlertId;

                    _ = _userLoginRepository.UpdateAsync(userLogin);
                }
                else
                {
                    var userLastAlert = await _alertRepository.GetByIdAsync(userLogin.LastAlertId);

                    var recentAlert = await _alertRepository.GetByIdAsync(createdEvent.AlertId);

                    alertMessage.UserMessage = recentAlert.UserMessage;

                    var alertCounts = await _alertRepository.GetByAlertId(userLogin.LastAlertId, userLastAlert.CreatedDate,
                        recentAlert.LastModifiedDate);

                    alertCount.AddRange(alertCounts);
                }

                var notificationDto = new AlertHubVm
                {
                    UserId = user.ReferenceId,
                    UserName = user.LoginName,
                    AlertId = userLogin.LastAlertId,
                    AlertCount = alertCount.Count > 0 ? alertCount.Count : 1,
                    UserMessage = alertMessage.UserMessage
                };
                notificationList.AddRange(notificationDto);
            }
        };

        foreach (var user in assignedInfraUsers)
        {
            var alertCount = new List<Domain.Entities.Alert>();

            var userInfraObject = await _userInfraObjectRepository.GetUserInfraObjectByUserIdAsync(user.ReferenceId);

            var deserializeValue = JsonConvert.DeserializeObject<AssignedEntity>(userInfraObject.Properties);

            var assignedInfra = deserializeValue.AssignedBusinessServices
                .SelectMany(x => x.AssignedBusinessFunctions
                    .SelectMany(infra => infra.AssignedInfraObjects)).ToList();

            var assignedInfraObjects = assignedInfra.Where(y => y.Id.Equals(createdEvent.InfraObjectId)).ToList();

            var userLogin = await _userLoginRepository.GetUserLoginByUserId(user.ReferenceId);

            if (userLogin != null)
            {
                var alertMessage = new Domain.Entities.Alert();

                foreach (var assignInfra in assignedInfraObjects)
                {
                    if (userLogin.LastAlertId != 0)
                    {
                        userLogin.LastAlertId = createdEvent.AlertId;

                        _ = _userLoginRepository.UpdateAsync(userLogin);
                    }
                    else
                    {
                        var userLastAlert = await _alertRepository.GetByIdAsync(userLogin.LastAlertId);

                        var recentAlert = await _alertRepository.GetByIdAsync(createdEvent.AlertId);

                        alertMessage.UserMessage = recentAlert.UserMessage;

                        var listOfAlertCount = await _alertRepository.GetLastAlertByInfraObject(assignInfra.Id,
                            userLastAlert.CreatedDate, recentAlert.LastModifiedDate);

                        alertCount.AddRange(listOfAlertCount);
                    }
                };

                if (assignedInfraObjects.Count > 0)
                {
                    var notificationDto = new AlertHubVm
                    {
                        UserId = user.ReferenceId,
                        UserName = user.LoginName,
                        AlertId = userLogin.LastAlertId,
                        AlertCount = alertCount.Count > 0 ? alertCount.Count : 1,
                        UserMessage = alertMessage.UserMessage
                    };
                    notificationList.AddRange(notificationDto);
                }
            }
        };

        await _hubContext.Clients.All.SendAsync("notification", notificationList);
    }
}