﻿namespace ContinuityPatrol.Application.Features.DashboardView.Queries.GetResilienceHealthStatus;

public class
    ResilienceHealthStatusDetailQueryHandler : IRequestHandler<ResilienceHealthStatusDetailQuery,
        ResilienceHealthStatusDetailVm>
{
   
    private readonly IInfraDashboardViewRepository _infraDashboardViewRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;

    public ResilienceHealthStatusDetailQueryHandler( IInfraDashboardViewRepository infraDashboardViewRepository,
        IInfraObjectRepository infraObjectRepository)
    {
        _infraDashboardViewRepository = infraDashboardViewRepository;
        _infraObjectRepository = infraObjectRepository;
    }

    public async Task<ResilienceHealthStatusDetailVm> Handle(ResilienceHealthStatusDetailQuery request,
        CancellationToken cancellationToken)
    {
        var heatMapStatus = await _infraDashboardViewRepository.GetIsAffectedDetailsById(request.InfraObjectId);
       
        var totalComponents = heatMapStatus?.HeatMapCount ?? 0;  
        var affectedCount = (totalComponents- heatMapStatus?.AffectedCount);
       
        var availabilityPercentage = totalComponents == 0
            ? 0
            : (double)affectedCount / totalComponents * 100;

        var infraObjectName = string.Empty;
        if (heatMapStatus == null)
        {
            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            infraObjectName = infraObject.Name;
        }


        return new ResilienceHealthStatusDetailVm
        {
            InfraObjectName = heatMapStatus== null
                ? infraObjectName
                : heatMapStatus.Name,

            Percentage = availabilityPercentage
        };
    }
}