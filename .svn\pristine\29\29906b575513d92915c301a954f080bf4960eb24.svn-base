$(async function () {
    //SaveAs   
    $(document).on('click', '#saveAsServerData', async function () {
        clearCloneErrorMessage();
        flagEdit = true;
        $("#totalCloneServer").text(0);
        $("#saveAllServers").css({ "opacity": "0.5", "pointer-events": "none" });
        $("#cloneDataTable").empty();
        $("#cloneTable").hide();

        getServerDataForSaveAsAndClone($('#cloneName'),)
    });

    $("#cloneName").on("change", async function () {
        commonInputValidation($(this).val(), " Select clone name", "cloneNameError");

        if ($(this).val()) {
            clonedserverRowData = await getserverData($(this).val());
            let siteNameLists = await fetchDataServer('Configuration/Server/GetSiteNames');

            if (Array.isArray(siteNameLists) && siteNameLists.length > 0) {
                let options = [];
                const sortedData = siteNameLists.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
                let cloneSiteName = $("#cloneSiteName");
                cloneSiteName.empty().append($('<option>').val('').text('Select Site Name'));
                sortedData.forEach(function (item) {
                    options.push($('<option>').val(item.id).text(item.name).attr('siteCategory', item.category));
                });
                cloneSiteName.append(options);
            }
        }
    });

    $("#cloneServerName").on("keyup", commonDebounce(async function () {
        const $cloneServerName = $("#cloneServerName");
        const sanitizedValue = $cloneServerName.val().replace(/\s{2,}/g, ' ');
        $cloneServerName.val(sanitizedValue);

        //InfraCommonFunctions.js InfraNameValidation
        let validation = await InfraNameValidation(sanitizedValue, "", "Configuration/Server/IsServerNameExist",
            $("#cloneServerNameError"), "Enter server name", 'Special characters not allowed', 'ServerName');

        if (flagEdit) {
            cloneServerNameValidation(validation, sanitizedValue);
        }
    }));

    $("#cloneSiteName").on("change", async function () {
        commonInputValidation($(this).val(), " Select site name", "cloneSiteNameError");
        siteCategory = $(this).find(":selected").attr("siteCategory");
        getServerType(clonedserverRowData?.roleTypeId, $('#cloneServerType'),"saveas");

        if ($(this).val()) {
            await $.ajax({
                type: "GET",
                //async: false, //dont't delete.
                url: RootUrl + 'Admin/LicenseManager/GetLicensesNamesWithCount',
                dataType: "json",
                data: { type: "server", roleType: clonedserverRowData?.roleTypeId, siteId: $(this).val(), serverId: "", replicationType: "" },
                success: function (result) {
                    if (result.success && (Array.isArray(result?.data) && result?.data?.length > 0)) {
                        let cloneLicensesNameLists = result?.data;

                        if (cloneLicensesNameLists && (Array.isArray(cloneLicensesNameLists) && cloneLicensesNameLists?.length > 0)) {
                            let options = [];
                            let cloneLicenseKey = $('#cloneLicenseKey');
                            cloneLicenseKey.empty().append($('<option>').val("").text(""));
                            cloneLicensesNameLists.forEach(function (item) {
                                options.push($('<option>').val(item?.id)
                                    .text(`${item?.poNumber || ''}`)
                                    .attr('licenseId', item?.id)
                                    .attr('remainingcount', item?.remainingCount || '0')
                                    .attr('licenseIsApplicable', item?.licenseIsApplicable))
                            });
                            cloneLicenseKey.append(options);
                        }
                    } else {
                        errorNotification(result)
                    }
                },
            });
        }
    });

    $("#cloneServerType").on("change", function () {
        commonInputValidation($(this).val(), " Select server type", "cloneServerTypeError");
    });

    $("#cloneLicenseKey").on("change", function () {
        commonInputValidation($(this).val(), " Select license key", "cloneLicenseKeyError");
    });

    $("#cloneIPAddress").on("keyup", function () {
        cloneIPAddressValidation($(this).val(), " Enter IP address", "cloneIPAddressError");
    });

    $("#cloneHostName").on("keyup", function () {
        commonInputValidation($(this).val(), " Enter host name", "cloneHostNameError");
    });

    $("#addCloneServer").on("click", async function () {
        let rowCount = $("#cloneDataTable tr").length;
        let serverProps = clonedserverRowData?.properties;
        let name = $("#cloneName").val();
        let serverName = $("#cloneServerName").val();
        let siteID = $("#cloneSiteName").val();
        let siteName = $("#cloneSiteName :selected").text();
        let serverTypeID = $("#cloneServerType :selected").attr('roletypeid');
        let serverTypeName = $("#cloneServerType :selected").text();
        let licenseKeyID = $("#cloneLicenseKey").val();
        let licenseKeyName = $("#cloneLicenseKey :selected").text();
        let IPAddress = $("#cloneIPAddress").val();
        let hostName = $("#cloneHostName").val();

        let validation = await InfraNameValidation(serverName, "", "Configuration/Server/IsServerNameExist",
            $("#cloneServerNameError"), "Enter server name", 'Special characters not allowed', 'ServerName');
        let ServerNameValidation = validation;

        if (validation && flagEdit) {
            ServerNameValidation = await cloneServerNameValidation(validation, serverName);
        }

        if ($("#addCloneServer").hasClass("cp-update")) {
            flagEdit = true;
            $("#addCloneServer").addClass("cp-circle-plus").removeClass("cp-update").prop("title", "Add");
        }
        let nameValidation = commonInputValidation(name, " Select clone name", "cloneNameError");
        let SiteNameValidation = commonInputValidation(siteID, " Select site name", "cloneSiteNameError");
        let ServerTypeNameValidation = commonInputValidation(serverTypeID, " Select server type", "cloneServerTypeError");
        let LicenseKeyNameValidation = commonInputValidation(licenseKeyID, " Select license key", "cloneLicenseKeyError");
        let IPAddressValidation = cloneIPAddressValidation(IPAddress, " Enter IP address", "cloneIPAddressError");
        let HostNameValidation = commonInputValidation(hostName, " Enter host name", "cloneHostNameError");

        if (nameValidation && ServerNameValidation && SiteNameValidation && ServerTypeNameValidation &&
            LicenseKeyNameValidation && IPAddressValidation && HostNameValidation) {

            let parsedServerProps = JSON.parse(serverProps);
            parsedServerProps.IpAddress = IPAddress;
            parsedServerProps.HostName = hostName;
            serverProps = JSON.stringify(parsedServerProps);

            $("#cloneTable").show();

            if (cloneServerSlNo > 0) {
                $("#cloneTable tbody tr").each(function () {
                    const row = $(this);
                    const cellWithSerialNumber = row.find("td:first");

                    if (cellWithSerialNumber.text().trim() === String(cloneServerSlNo)) {
                        row.find("td").eq(1).text($("#cloneServerName").val());
                        row.find("td").eq(2).text($("#cloneSiteName :selected").text());
                        row.find("td").eq(3).text($("#cloneServerType :selected").text());
                        row.find("td").eq(4).text($("#cloneLicenseKey :selected").text());
                        row.find("td").eq(5).text($("#cloneIPAddress").val());
                        row.find("td").eq(6).text($("#cloneHostName").val());

                        // Update the content or attributes in the 8th column
                        const cell = row.find("td").eq(8);

                        // Update the attributes of the "cloneEditButton"
                        const editButton = cell.find(".cloneEditButton");
                        editButton.attr("data-servername", $("#cloneServerName").val());
                        editButton.attr("data-ipaddress", $("#cloneIPAddress").val());
                        editButton.attr("data-hostname", $("#cloneHostName").val());
                        editButton.attr("data-siteid", $("#cloneSiteName").val());
                        editButton.attr("data-typeid", $("#cloneServerType :selected").attr('roletypeid'));
                        editButton.attr("data-licensekeyid", $("#cloneLicenseKey").val());

                        // update the delete button if needed
                        const deleteButton = cell.find(".saveAsDeleteButton");
                        deleteButton.attr("data-servername", $("#cloneServerName").val());

                        let selectedServer = clonedServerLists.ServerList[cloneServerSlNo - 1];
                        Object.assign(selectedServer, {
                            Name: serverName,
                            SiteId: siteID,
                            SiteName: siteName,
                            ServerTypeId: serverTypeID,
                            ServerType: serverTypeName,
                            Properties: serverProps,
                            LicenseId: licenseKeyID,
                            LicenseKey: licenseKeyName
                        });

                        return false;
                    }
                });
            } else {

                clonedServerLists.ServerId = name;
                clonedServerLists.ServerList.push({
                    "Name": serverName,
                    "SiteId": siteID,
                    "SiteName": siteName,
                    "ServerTypeId": serverTypeID,
                    "ServerType": serverTypeName,
                    "Properties": serverProps,
                    "LicenseId": licenseKeyID,
                    "LicenseKey": licenseKeyName
                });

                $("#totalCloneServer").text(rowCount + 1);
                $("#cloneDataTable").append(`<tr>
                            <td>${rowCount + 1}</td>
                            <td>${serverName}</td>
                            <td class="text-truncate">${siteName}</td>
                            <td>${serverTypeName}</td>
                            <td>${licenseKeyName}</td>
                            <td>${IPAddress}</td>
                            <td>${hostName}</td>
                            <td><span class="text-warning iconClass"><i class="cp-pending"></i></span></td>
                            <td>
                              <div class="d-flex align-items-center gap-2">
                                  <span role="button" title="Edit" class="cloneEditButton" data-servername="${serverName}"
                                    data-siteid="${siteID}" data-typeid="${serverTypeID}" data-licensekeyid="${licenseKeyID}"
                                    data-ipaddress="${IPAddress}" data-slno="${rowCount + 1}" data-hostname="${hostName}" >
                                    <i class="cp-edit"></i>
                                  </span>
                                  <span role="button" title="Delete" data-slno="${rowCount + 1}" data-servername="${serverName}"
                                       class="button saveAsDeleteButton" data-bs-toggle="modal" data-bs-target="#DeleteModalSaveAs">
                                    <i class="cp-Delete"></i>
                                  </span>
                             </div>
                           </td>
                         </tr>`);
            }

            cloneServerSlNo = 0;

            $("#saveAllServers").css({ "opacity": "1.0", "pointer-events": "" });

            $("#cloneServerName, #cloneIPAddress, #cloneHostName").val("");
            $("#cloneSiteName, #cloneServerType, #cloneLicenseKey").val("").trigger("change");
            $('#cloneSiteNameError, #cloneServerTypeError, #cloneLicenseKeyError').text('').removeClass('field-validation-error');
        }
    });

    $("#saveAllServers").on("click", async function () {
        await $.ajax({
            type: "POST",
            url: RootUrl + "Configuration/Server/SaveAllServer",
            dataType: "json",
            headers: {
                'RequestVerificationToken': await gettoken()
            },
            data: { command: clonedServerLists },
            success: function (result) {
                if (result?.success) {
                    if (result?.data?.success) {
                        let resultData = result?.data;
                        notificationAlert("success", resultData?.message);

                        //Comment this after added style in modal.
                        $("#saveasModal").modal("hide");
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);

                    } else {
                        errorNotification(result?.data)
                        $("#cloneModal").modal("hide");
                    }

                } else {
                    errorNotification(result)
                    $("#cloneModal").modal("hide");
                }
            },
        });
    });

    $("#cloneTable").on("click", ".cloneEditButton", function () {
        $("#cloneServerName").val($(this).attr("data-servername"));
        $("#cloneSiteName").val($(this).attr("data-siteid")).trigger("change");       
        setTimeout(() => {
            $("#cloneServerType").val($(this).attr("data-typeid")).trigger("change");
            setTimeout(() => {
                $("#cloneLicenseKey").val($(this).attr("data-licensekeyid")).trigger("change");
            },100)
        }, 100)
        $("#cloneIPAddress").val($(this).attr("data-ipaddress"));
        $("#cloneHostName").val($(this).attr("data-hostname"));
        cloneServerSlNo = $(this).data("slno");
        flagEdit = false;
        $("#addCloneServer").removeClass("cp-circle-plus").addClass("cp-update").prop("title", "Update");
    });

    $("#cloneTable").on("click", ".saveAsDeleteButton", function () {
        deleteCloneServerRow = $(this).data("slno");
        $("#deleteCloneServerData").text($(this).data("servername"));
    });

    $("#deleteSaveAsRow").on("click", async function () {
        $("#cloneTable tbody tr").each(function () {
            const row = $(this);
            const cellWithSerialNumber = row.find("td:first");

            if (cellWithSerialNumber.text().trim() === String(deleteCloneServerRow)) {
                row.remove();
                let index = Number(cellWithSerialNumber.text().trim())
                let indexToRemove = index - 1;
                if (indexToRemove >= 0 && indexToRemove < clonedServerLists.ServerList.length) {
                    clonedServerLists.ServerList.splice(indexToRemove, 1);
                }
                return false;
            }
        });
        $("#totalCloneServer").text($("#totalCloneServer").text() - 1);
        $("#saveasModal").modal("show");

        if ($("#cloneDataTable tr").length === 0) {
            $("#cloneTable").hide();
            $("#saveAllServers").css({ "opacity": "0.5", "pointer-events": "none" });
        }
    });

    $("#cancelDelete").on("click", function () {
        $("#saveasModal").modal("show");
    });

    //Clone
    $('.serverCloneButton').on('click', function () {
        $("#cloneModal").modal("show");
        $('#cloneServer, #inputCloneServer').val("");
        $('#cloneServerError, #inputCloneServerError').text("").removeClass("field-validation-error");
        getServerDataForSaveAsAndClone($("#cloneServer"));
    });

    $('#cloneButton').on("click", async function () {
        const $selectServerName = $('#cloneServer');
        const $cloneServerName = $('#inputCloneServer');

        let selectServerNameValidate = commonInputValidation($selectServerName.val(), "Select server name", "cloneServerError");

        let cloneServerNameValidate = await InfraNameValidation($cloneServerName.val(), "", "Configuration/Server/IsServerNameExist",
            $("#inputCloneServerError"), "Enter clone server name", 'Special characters not allowed', 'servername');

        if (selectServerNameValidate && cloneServerNameValidate) {
            async function saveAs() {
                await $.ajax({
                    type: "POST",
                    url: RootUrl + "Configuration/Server/SaveAsServer",
                    dataType: "json",
                    headers: {
                        'RequestVerificationToken': await gettoken()
                    },
                    data: { saveAsServerCommand: { "ServerId": $('#serverId').val(), "Name": $cloneServerName.val() } },
                    success: function (result) {
                        if (result.success) {
                            if (result?.data?.success) {
                                let resultData = result?.data;
                                notificationAlert("success", resultData?.message);
                                $('#serverId').val("");

                                //Comment this after added style in modal.
                                $("#cloneModal").modal("hide");
                                setTimeout(() => {
                                    window.location.reload();
                                }, 2000);

                            } else {
                                errorNotification(result?.data)
                                $("#cloneModal").modal("hide");
                            }

                        } else {
                            errorNotification(result)
                            $("#cloneModal").modal("hide");
                        }
                    },
                });
            }
            saveAs();
        }
    });

    $('#cloneServer').on('change', function () {
        const $serverName = $(this);
        $('#serverId').val($serverName.val());
        commonInputValidation($serverName.val(), "Select server name", "cloneServerError");
    });

    $('#inputCloneServer').on('keyup', commonDebounce(async function () {
        const $serverName = $(this);

        //InfraCommonFunctions.js InfraNameValidation
        await InfraNameValidation($serverName.val(), "", "Configuration/Server/IsServerNameExist",
            $("#inputCloneServerError"), "Enter clone server name", 'Special characters not allowed', 'servername');
    }));
});

async function getServerDataForSaveAsAndClone(selectServer) {
    await $.ajax({
        type: "GET",
        async: false, //dont't delete.
        url: RootUrl + 'Configuration/Server/GetServerNamesForSaveAs',
        dataType: "json",
        success: function (result) {
            if (result.success) {
                let response = result?.data;
                if (response && (Array.isArray(response) && response.length > 0)) {
                    selectServer.empty().append($('<option>').val("").text(""));
                    let options = [];
                    response.forEach(function (item) {
                        options.push($('<option>').val(item.id).text(item.name));
                    });
                    selectServer.append(options);
                }
            } else {
                errorNotification(result);
            }
        }
    })
}