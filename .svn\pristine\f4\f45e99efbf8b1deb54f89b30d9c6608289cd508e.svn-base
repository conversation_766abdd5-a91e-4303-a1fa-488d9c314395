const state = {
    jsonData: null,
    selectedValues: [],
    checkDetail: false,
    infraCheck: [],
    dataTable: null
};

const NotificationURL = {
    nameExistUrl: "Manage/NotificationManager/IsAlertReceiverNameExist",
    Pagination: "/Manage/NotificationManager/GetPagination",
    GetAllInfraObjectList:"Manage/NotificationManager/GetAllInfraObjectList"
}
function notifDebounce(func, delay = 300) {
    let timer;
    return function () {
        clearTimeout(timer);
        timer = setTimeout(() => {
            func.apply(this, arguments);
        }, delay);
    };
}
treeListView();
//pagination getData
$(function () {
    const NotificationPermission = {
        createPermission: $("#ConfigurationCreate").data("create-permission").toLowerCase(),
        deletePermission: $("#ConfigurationDelete").data("delete-permission").toLowerCase()
    }

    if (NotificationPermission.createPermission == 'false') {
        $("#CreteButton").removeClass('#CreteButton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', NotificationPermission.createPermission == 'false');
    }
   state.dataTable = $('#notificationManager').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow"></i>'
                }, infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": NotificationURL.Pagination,
                "dataType": "json",
                "data": function (d) {

                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "emailAddress" : sortIndex === 3 ? "properties" :
                        sortIndex === 4 ? "mobileNumber" : sortIndex === 5 ? "isActiveUser" : ""
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                    d.pageSize = d?.length;
                    d.searchString = state.selectedValues?.length === 0 ? $('#notifSearchInp').val() : state.selectedValues.join(';');
                    state.selectedValues.length = 0;
                },
                "error": function (xhr, err) {
                    if (xhr.status === 401) {
                        window.location.assign('/Account/Logout');
                    }
                },
                "dataSrc": function (json) {
                    if (json?.success) {
                        json.recordsTotal = json?.data?.totalPages;
                        json.recordsFiltered = json?.data?.totalCount;
                        if (json?.data?.data?.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        return json?.data?.data;
                    } else {
                        errorNotification(json)
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [0, 1, 2, 3, 4],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false,
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title="${data ?? "NA"}"> ${data ?? "NA"}</span></td >`;
                    }
                },
                {
                    "data": "emailAddress", "name": "Email Address", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<td><span title="${data ?? "NA"}"> ${data ?? "NA"}</span></td >`;
                        }
                        return data;
                    }
                },
                {
                    "data": "properties", "name": "InfraObject Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        let parsedatas = JSON.parse(data)
                        let infradats = []
                        parsedatas?.assignedBusinessServices?.forEach(service => {
                            service?.assignedBusinessFunctions?.forEach(func => {
                                func?.assignedInfraObjects?.forEach(infra => {
                                    if (infra?.isSelected) {
                                        infradats.push(infra?.name)
                                    }
                                });
                            });
                        });
                        if (type === 'display') {
                            return '<span title="' + infradats + '">' + infradats + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "mobileNumber", "name": "Mobile Number", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display' && data === null) {
                            return '<span>NA</span>';
                        } else {
                            return '<span >' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "isActiveUser", "name": "Active", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + (data == true ? "Active" : "InActive") + '" class="' + (data == true ? "cp-success text-success" : "cp-error text-danger") + '"></span>';
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        if (NotificationPermission.createPermission === 'true' && NotificationPermission.deletePermission === "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" class="edit-button" title="Edit" data-alertreceiver='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>                               
                                            <span role="button" class="delete-button"  title="Delete" data-alertreceiver-id="${row.id}" data-alertreceiver-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                   
                        </div>`;
                        }
                        else if (NotificationPermission.createPermission === 'true' && NotificationPermission.deletePermission === "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" class="edit-button"  title="Edit" data-alertreceiver='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>                                
                                            <span role="button"  class="icon-disabled"  title="Delete">
                                                <i class="cp-Delete"></i>
                                            </span>
                                   
                        </div>`;
                        }
                        else if (NotificationPermission.createPermission === 'false' && NotificationPermission.deletePermission === "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" class="icon-disabled"  title="Edit">
                                                <i class="cp-edit"></i>
                                            </span>
                                 
                                            <span role="button"  title="Delete"  class="delete-button" data-alertreceiver-id="${row.id}" data-alertreceiver-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                   
                        </div>`;
                        }
                        else {
                            return `
                        <div class="d-flex align-items-center gap-2">    
                                            <span role="button" class="icon-disabled"  title="Edit">
                                                <i class="cp-edit"></i>
                                          </span>                              
                                            <span role="button"   class="icon-disabled"  title="Delete">
                                                <i class="cp-Delete"></i>
                                            </span>                  
                        </div>`;
                        }
                    },
                    "orderable": false,
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    state.dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });
    //Search 
    $('#notifSearchInp').on('keydown input', notifDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const nameCheckbox = $("#Name");
        const emailNameCheckbox = $("#Email");
        const mobileCheckBox = $("#Mobile");
        const inputValue = $('#notifSearchInp').val();
        if (nameCheckbox.is(':checked')) {
            state.selectedValues.push(nameCheckbox.val() + inputValue);
        }
        if (emailNameCheckbox.is(':checked')) {
            state.selectedValues.push(emailNameCheckbox.val() + inputValue);
        }
        if (mobileCheckBox.is(':checked')) {
            state.selectedValues.push(mobileCheckBox.val() + inputValue);
        }
        let currentPage = state.dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            state.dataTable.ajax.reload(function (json) {
                if ($('#notifSearchInp').val().length === 0) {
                    if (json?.data?.data?.length === 0) {
                        $('.dataTables_empty').text('No Data Found');
                    }
                } else if (json?.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false)
        }
    }));
})
const shouldOpen = (service, func, infra, id, funcId, infraId) => {
    return service?.isAll || service?.isPartial || service?.id === id ||
        func?.id === funcId || func?.isAll || infra?.isSelected || infra?.id === infraId;
};
//ClearData's
$("#notifCreateButton,#notifyBtnCancel").on('click', function () {
    clearInputField();
});
const clearInputField = () => {
    $("#notifyId,#notifName,#notifEmail,#notifMobileNum,#notifMobilePre").val('');;
    $("#notifIsMobile,#notifActiveUser,#notifSendReport").prop("checked", false);
    $("#notifMob,#notifMobPre").hide();
    state.jsonData = null;
    $("#notifyTreeView").empty();
    treeListView();
    $("#notifSave").prop('disabled', false);
    state.checkDetail = false
    $('#notifSave').text('Save');
    $("#notifNameError,#notifEmailError,#notifMobileError,#notifMobilePreError").text('').removeClass('field-validation-error')
    $("#notifTreeError").text('').removeClass('text-danger');
};
$("#notifMob,#notifMobPre").hide();
//Validation and Event
$('#notifName').on('input', notifDebounce(async function () {
    let value = await sanitizeInput($("#notifName").val());
    $("#notifName").val(value);
    await validateName(value, $('#notifyId').val(), NotificationURL.nameExistUrl);
}));

$('#notifEmail').on('input keypress', async function () {
    if ($(this).val() !== undefined) {
        $(this).val($(this).val().replace(/  +/g, " "));
    }
    await validateEmail($(this).val());
});

$('#notifMobileNum').on('input keypress', async function (event) {
    if ($(this).val() !== undefined) {
        $(this).val($(this).val().replace(/  +/g, " "));
    }
    if (!/^[0-9]+$/.test(event.key)) {
        event.preventDefault();
    }
    await validateMobile($(this).val());
});

$.getJSON("/json/CountryDailCode.json", function (data) {
    if (data?.countrycode) {
        data?.countrycode?.forEach(function (value) {
            $('#notifMobilePre').append('<option value="' + value.dial_code + '">' + value.dial_code + '</option>');
        });
    }
}).fail(function () {
    console.error("Failed to load JSON data.");
});

$('#notifMobilePre').on('change', async function () {
    await validateMobilePre($(this).val());
});

//Checkbox
$('#notifIsMobile').on("change", function () {
    if (this.checked) {
        $("#notifMob,#notifMobPre,#notifMobileNum,#notifMobilePre").show();
    }
    else {
        $("#notifMob,#notifMobPre,#notifMobileNum,#notifMobilePre").hide();
        $('#notifMobileNum').val('');
        $('#notifMobilePre').val('').trigger('change')
        $("#notifMobileError,#notifMobilePreError").text('').removeClass('field-validation-error');
    }
});
//Update
$('#notificationManager').on('click', '.edit-button', function () {
    const alertData = $(this).data('alertreceiver');
    populateModalFields(alertData);
    $('#notifSave').text('Update');
    $('#CreateModal').modal('show');
});
function populateModalFields(alertData, ClickedEditButton = true) {
    $('#notifyId').val(alertData?.id);
    $('#notifName').val(alertData?.name);
    $('#notifEmail').val(alertData?.emailAddress);
    $('#textProperties').val(alertData?.properties);
    state.jsonData = JSON.parse(alertData?.properties);
    $('#notifIsMobile').prop("checked", alertData?.isMail);
    alertData.isMail ? $('input[name="isMail"]').prop("checked", true) : $('input[name="isMail"]').prop("checked", false);
    if (alertData.isMail == true) {
        $("#notifMob,#notifMobPre,#notifMobileNum,#notifMobilePre").show();
        const mobileNumber = alertData?.mobileNumber.split('-');
        $('#notifMobilePre').val(mobileNumber[0]);
        $('#notifMobileNum').val(mobileNumber[1]);
    }
    else {
        $("#notifMob,#notifMobPre,#notifMobileNum,#notifMobilePre").hide();
    }
    $("#notifActiveUser").prop('checked', alertData?.isActiveUser);
    alertData?.isActiveUser ? $('input[name="isActiveUser"]').prop("checked", true) : $('input[name="isActiveUser"]').prop("checked", false);
    $("#notifSendReport").prop('checked', alertData?.isSendReport);
    alertData?.isSendReport ? $('input[name="isSendReport"]').prop("checked", true) : $('input[name="isSendReport"]').prop("checked", false);
    $("#notifNameError,#notifEmailError,#notifMobileError,#notifMobilePreError").text('').removeClass('field-validation-error')
    updateTreeView(alertData, ClickedEditButton);
}
//Delete
$("#notificationManager").on('click', '.delete-button', function () {
    const alertId = $(this).data('alertreceiver-id');
    const alertName = $(this).data('alertreceiver-name');
    $("#deleteData").attr("title", alertName).text(alertName);
    $('#textDeleteId').val(alertId);
});
//Save Function
$("#notifSave").on("click", async function () {
    const name = $("#notifName").val();
    const emailAddress = $('#notifEmail').val();
    const mobilePre = $("#notifMobilePre").val();
    const mobileNo = $("#notifMobileNum").val();
    if (mobilePre && mobileNo) {
        const comMobile = mobilePre + '-' + mobileNo;
        $('#comMobile').val(comMobile)
    }
    const alertId = $('#notifyId').val();
    const infraTree = $("#notifyTreeView").html();
    const isMobileChk = $("#notifIsMobile").prop('checked');
    const isName = await validateName(name, alertId, NotificationURL.nameExistUrl);
    const isEmail = await validateEmail(emailAddress);
    const isMobileNo = isMobileChk ? await validateMobile(mobileNo) : true;
    const isMobilePre = isMobileChk ? await validateMobilePre(mobilePre) : true;
    const isInfraTree = validateInfraTree(infraTree, state.jsonData);
    if (isName && isEmail && isInfraTree && isMobilePre && isMobileNo && state.checkDetail) {
        $(this).prop('disabled', true);
        $("#CreateForm").trigger("submit");
    }
});

$("#notifSelectAll").on("change", function () {
    if ($("#notifSelectAll").prop("checked") == true) {
        $("#notifTreeError").text("")
        return true;
    } else {
        setTimeout(() => {
            $("#notifTreeError").text("Select at least one infraobject").css({ "color": "#FF0000", "font-size": "11px" })
        }, 500)
        return false
    }
})
function dataCheck(infras) {
    if (infras == true) {
        state.checkDetail = true
    }
}
async function validateName(value, id = null, url) {
    if (!value) {
        $('#notifNameError').text('Enter notification name')
            .addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        $('#notifNameError').text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    var url = RootUrl + url;
    var data = {};
    data.alertReceiverName = value;
    data.id = id;
    const validationResults = [
        SpecialCharValidateCustom(value),
        value == "_" ? ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? ShouldNotBeginWithSpace(value) :
            OnlyNumericsValidate(value),
        ShouldNotBeginWithUnderScore(value),
        ShouldNotBeginWithSpace(value),
        ShouldNotBeginWithNumber(value),
        SpaceWithUnderScore(value),
        ShouldNotEndWithUnderScore(value),
        ShouldNotEndWithSpace(value),
        MultiUnderScoreRegex(value),
        SpaceAndUnderScoreRegex(value),
        minMaxlength(value),
        secondChar(value),
       await IsNameExist(url, data, OnError)
    ];
    return CommonValidation($('#notifNameError'), validationResults);
}
function validateInfraTree(value, jsondata) {
    jsondata?.assignedBusinessServices?.forEach(service => {
        service?.assignedBusinessFunctions?.forEach(func => {
            func?.assignedInfraObjects?.forEach(infra => {
                dataCheck(infra?.isSelected)
            });
        });
    });
    if (value === undefined || value === null || value?.length === 0 || state.checkDetail == false) {
        $("#notifTreeError").text("Select at least one infraobject").css({ "color": "#FF0000", "font-size": "11px" })
    } else {
        $("#notifTreeError").text("")
        return true;
    }
}
async function validateMobilePre(value) {
    if (!value) {
        $('#notifMobilePreError').text('Select country code')
            .addClass('field-validation-error');
        return false;
    }
    let validationResults = false
    if (value) {
        validationResults = true
        $('#notifMobilePreError').text('').removeClass('field-validation-error');
        return true;
    }
    return  CommonValidation($('#notifMobilePreError'), validationResults);
}
async function validateMobile(value) {
    if (!value) {
        $('#notifMobileError').text('Enter mobile number')
            .addClass('field-validation-error');
        return false;
    } else if (value == 0) {
        $('#notifMobileError').text('Number not starts with zero')
            .addClass('field-validation-error');
        return false;
    }
    else if (value) {
        const minLength = 7
        if (value.length < minLength) {
            $('#notifMobileError').text('Must be at least 7 characters')
                .addClass('field-validation-error');
            return false;
        } else {
            $('#notifMobileError').text('')
                .removeClass('field-validation-error');
            return true;
        }
    }
    else {
        $('#notifMobileError').text('')
            .removeClass('field-validation-error');
        return true;
    }
}

async function validateEmail(value) {
    var format = /^[a-zA-Z0-9._]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
    if (!value) {
        $('#notifEmailError').text('Enter email address')
            .addClass('field-validation-error');
        return false;
    } else if (value.length >= 321) {
        $('#notifEmailError').text('Enter the value less than 320 characters')
            .addClass('field-validation-error');
        return false;
    } else if (value.length) {
        if (format.test(value) == false) {
            $('#notifEmailError').text('Invalid email')
                .addClass('field-validation-error');
            return false;
        } else if (value.charAt(0) == "." || value.charAt(0) == "_") {
            $('#notifEmailError').text('Invalid email')
                .addClass('field-validation-error');
            return false;
        } else {
            $('#notifEmailError').text('')
                .removeClass('field-validation-error');
            return true;
        }
    }
    else {
        $('#notifEmailError').text('')
            .removeClass('field-validation-error');
        return true;
    }
    const validationResults = [await emailRegex(value)];
    return await CommonValidation($('#notifEmailError'), validationResults);
}
//Name area
async function IsNameExist(url, data, errorFunc) {
    return !data?.alertReceiverName.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}
//Tree Function
async function treeListView() {
    $("#notifyTreeView").empty();
    await $.ajax({
        url: RootUrl + NotificationURL.GetAllInfraObjectList,
        method: 'GET',
        dataType: 'json',
        success: function (data) {
            state.jsonData = data?.data
            populateTreeView(state.jsonData);
        },
        error: function (error) {
            notificationAlert('Error:', error);
        }
    });
}
function SelectAllTreeViewExpended(open) {
    $('#notifExpand details').attr('open', open);
}
function populateTreeView(jsonData) {
    const container = $("#notifyTreeView");
    container.empty();
    if (jsonData && jsonData?.assignedBusinessServices) {
        createTreeView(container, jsonData);
    }
}
async function updateTreeView(alertData, ClickedEditButton) {
    $("#notifyTreeView").empty();
    let userInfraObject = JSON.parse(alertData?.properties);
    if (ClickedEditButton) {
        let jsonDetails = "";
        await $.ajax({
            url: RootUrl +NotificationURL.GetAllInfraObjectList,
            method: 'GET',
            dataType: 'json',
            success: function (data) {
                if (data?.success) {
                    jsonDetails = data?.data
                } else {
                    errorNotification(result)
                }
            },
            error: function (error) {
                notificationAlert('Error:', error);
            }
        });
        if (jsonDetails?.assignedBusinessServices?.length > 0) {
            jsonDetails.isAll = userInfraObject.isAll
            jsonDetails?.assignedBusinessServices?.forEach(function (JSONData) {
                userInfraObject?.assignedBusinessServices?.forEach(function (infraObject) {
                    if (JSONData?.id === infraObject?.id) {
                        JSONData.isAll = infraObject?.isAll;
                        JSONData.isPartial = infraObject?.isPartial;
                        JSONData?.assignedBusinessFunctions?.forEach(function (jsonDataFunction) {
                            infraObject?.assignedBusinessFunctions?.forEach(function (infraFunction) {
                                if (jsonDataFunction?.id === infraFunction?.id) {
                                    jsonDataFunction.isAll = infraFunction?.isAll;
                                    jsonDataFunction.isPartial = infraFunction?.isPartial;
                                    jsonDataFunction?.assignedInfraObjects?.forEach(function (jsonDataInfa) {
                                        infraFunction?.assignedInfraObjects?.forEach(function (infra) {
                                            if (jsonDataInfa?.id === infra?.id) {
                                                jsonDataInfa.isSelected = infra?.isSelected;
                                            }
                                        });
                                    });
                                }
                            });
                        });
                    }
                });
            });
            userInfraObject = [];
            userInfraObject = jsonDetails;
            jsonData = jsonDetails
        }
    }
    if (userInfraObject === null) {
        treeListView();
    }
    else {
        createTreeView($("#notifyTreeView"), userInfraObject);
    }
}
$('#textProperties').on('change', function () {
    let chk = this.checked, id = $(this).attr('businessid'), funcId = $(this).attr('functionId'), infraId = $(this).attr('infraId')
    // Update alertData.userInfraObject
    if (!alertData.userInfraObject) {
        alertData.userInfraObject = {};
    }
    if (!alertData.userInfraObject[id]) {
        alertData.userInfraObject[id] = {};
    }
    if (!alertData.userInfraObject[id][funcId]) {
        alertData.userInfraObject[id][funcId] = {};
    }
    alertData.userInfraObject[id][funcId][infraId] = chk;
    // Update the hidden input field
    $('#textProperties').val(JSON.stringify(alertData.properties));
});
function selectTree(checkbox) {
    let chk = checkbox.checked, businessId = $(checkbox).attr("businessid"), functionid = $(checkbox).attr("functionid"), infraid = $(checkbox).attr("infraid")
    infratree(state.jsonData)
    JsonTreeView(chk, businessId, functionid, infraid);
}
function infratree(data) {
    state.infraCheck = []
    if (data) {
        setTimeout(() => {
            infras(state.jsonData)
        }, 300)
    }
    function infras(d) {
        d?.assignedBusinessServices?.forEach(service => {
            service?.assignedBusinessFunctions?.forEach(func => {
                func?.assignedInfraObjects?.forEach(infra => {
                    if (infra?.isSelected == true) {
                       state.infraCheck.push(infra.isSelected)
                    }
                });
            });
        });
    }
    setTimeout(() => {
        if (state.infraCheck?.length == 0) {
           $("#notifTreeError").text("Select at least one infraobject").css("color", "#FF0000").css("font-size", "11px")
        } else {
            $("#notifTreeError").text("")
        }
    }, 300)
}
function JsonTreeView(s, id, funcId, infraId) {
    const updateServiceSelection = (service) => {
        const allSelected = service?.assignedBusinessFunctions?.every(f => f?.isAll);
        service.isAll = allSelected;
        service.isPartial = allSelected || service?.assignedBusinessFunctions?.some(f => f?.isAll);
    };
    state.jsonData?.assignedBusinessServices?.forEach(service => {
        if (service?.id !== id) return;
        const functions = service?.assignedBusinessFunctions || [];
        if (funcId && !infraId) {
            functions?.forEach(func => {
                if (func.id === funcId) {
                    func.isAll = func.isPartial = s;
                    func.assignedInfraObjects?.forEach(infra => {
                        if (infra?.id != null) infra.isSelected = s;
                    });
                }
            });
            updateServiceSelection(service);
        }
        else if (!funcId && !infraId) {
            service.isAll = service.isPartial = s;
            functions?.forEach(func => {
                func.isAll = func.isPartial = s;
                func.assignedInfraObjects?.forEach(infra => {
                    if (infra?.id != null) infra.isSelected = s;
                });
            });
        }
        else if (funcId && infraId != null) {
            setTimeout(() => $("#notifTreeError").text(""), 200);
            functions?.forEach(func => {
                if (func.id !== funcId) return;
                func?.assignedInfraObjects?.forEach(infra => {
                    if (infra?.id === infraId) infra.isSelected = s;
                });
                const selectedCount = func.assignedInfraObjects?.filter(i => i?.isSelected).length || 0;
                const total = func.assignedInfraObjects?.length || 0;
                func.isAll = selectedCount === total;
                func.isPartial = selectedCount > 0 && selectedCount < total;
            });

            const selectedFuncCount = functions.filter(f => f?.isAll).length;
            const totalFuncs = functions.length;

            service.isAll = selectedFuncCount === totalFuncs;
            service.isPartial = selectedFuncCount > 0 && selectedFuncCount < totalFuncs;
        }
    });

    const allChecked = state.jsonData?.assignedBusinessServices?.every(s => s?.isAll);
    $("#notifSelectAll").prop("checked", allChecked);
    state.jsonData.isAll = allChecked;

    $("#notifyTreeView").empty();
    $('#textProperties').val(JSON.stringify(state.jsonData));
    createTreeView($("#notifyTreeView"), state.jsonData, s, id, funcId, infraId);
}

$('#notifSelectAll').on('change', function () {
    let check = this.checked;
    check ? JsonAllTreeView(true) : JsonAllTreeView(false);
});
$('#notifExpand').on('toggle', function () {
    let open = this.open;
    open ? SelectAllTreeViewExpended(true) : SelectAllTreeViewExpended(false)
});
function JsonAllTreeView(chk) {
    $('.selecttree').prop('checked', chk);
    state.jsonData?.assignedBusinessServices?.forEach(service => {
        service.isAll = chk;
        service.isPartial = chk;
        service?.assignedBusinessFunctions?.forEach(func => {
            func.isAll = chk;
            func.isPartial = chk;
            func?.assignedInfraObjects?.forEach(infra => {
                infra.isSelected = chk;
            });
        });
    });
    state.jsonData.isAll = chk;
    $("#notifyTreeView").empty();
    $('#textProperties').val(JSON.stringify(state.jsonData));
    createTreeView($("#notifyTreeView"), state.jsonData);
    $("#notifyTreeView").enable = false;
}

async function createTreeView(container, userInfraObject, chk, id, funcId, infraId) {
    $("#notifSelectAll").prop('checked', userInfraObject?.isAll === true);
    container.empty();

    userInfraObject?.assignedBusinessServices?.forEach((service) => {
        const serviceDetails = $('<details></details>');
        const serviceSummary = $('<summary></summary>');
        const serviceCheckbox = $('<input type="checkbox" class="form-check-input selecttree" name="bs" onchange="selectTree(this)">');
        serviceCheckbox.attr('businessId', service.id);
        serviceCheckbox.prop('checked', service.isAll);
        serviceSummary.append(serviceCheckbox).append(' ' + service.name);

        const functionList = $('<ul class="tree"></ul>');
        service?.assignedBusinessFunctions?.forEach((func) => {
            const funcDetails = $('<details></details>');
            const funcSummary = $('<summary></summary>');
            const funcCheckbox = $('<input type="checkbox" class="form-check-input selecttree" name="bf" onchange="selectTree(this)">');
            funcCheckbox.attr({ businessId: service.id, functionId: func.id });
            funcCheckbox.prop('checked', func.isAll);
            funcSummary.append(funcCheckbox).append(' ' + func.name);

            const objectList = $('<ul class="tree"></ul>');
            func?.assignedInfraObjects?.forEach((infra) => {
                const shouldBeOpen = shouldOpen(service, func, infra, id, funcId, infraId);
                const objDetails = $(`<details${shouldBeOpen ? ' open' : ''}></details>`);
                const objSummary = $('<summary style="list-style-type: none;" class="rightarrow"></summary>');
                const objCheckbox = $('<input type="checkbox" name="infra" class="form-check-input selecttree" onchange="selectTree(this)">');
                objCheckbox.attr({ businessId: service.id, functionId: func.id, infraId: infra.id });
                objCheckbox.prop('checked', infra.isSelected);
                objSummary.append(objCheckbox).append(' ' + infra.name);
                objDetails.append(objSummary);
                objectList.append(objDetails);
            });

            const funcOpen = shouldOpen(service, func, null, id, funcId, null);
            funcDetails.attr('open', funcOpen);
            funcDetails.append(funcSummary).append(objectList);
            functionList.append(funcDetails);
        });

        const serviceOpen = shouldOpen(service, null, null, id, null, null);
        serviceDetails.attr('open', serviceOpen);
        serviceDetails.append(serviceSummary).append(functionList);
        container.append(serviceDetails);
    });
}


