﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.ServerLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.ServerLog.Queries.GetPaginatedList;

public class GetServerLogPaginatedListQueryHandler : IRequestHandler<GetServerLogPaginatedListQuery,
    PaginatedResult<ServerLogListVm>>
{
    private readonly IMapper _mapper;
    private readonly IServerLogRepository _serverLogRepository;

    public GetServerLogPaginatedListQueryHandler(IMapper mapper,
        IServerLogRepository serverLogRepository)
    {
        _mapper = mapper;
        _serverLogRepository = serverLogRepository;
    }

    public async Task<PaginatedResult<ServerLogListVm>> Handle(GetServerLogPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new ServerLogFilterSpecification(request.SearchString);

        var queryable =await _serverLogRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var serverLogList = _mapper.Map<PaginatedResult<ServerLogListVm>>(queryable);

        return serverLogList;
    }
}