﻿function initiateWorkflowSignalR() {
    let signalRUrl = $('#Chat_Bot').attr('signalRurl');

    let url = signalRUrl + "workflowhub";
    let connection = new signalR.HubConnectionBuilder()
        .withUrl(url, {
            skipNegotiation: true,
            rejectUnauthorized: false,
            transport: signalR.HttpTransportType.WebSockets
        })
        .configureLogging(signalR.LogLevel.Information)
        .build();

    async function startSignalRConnection() {
        try {
            await connection.start().then(() => {
                console.log('signalR Connected!')
            })

            await connection.onreconnected(connectionId => {
                console.log("Reconnected. Connection ID:", connectionId);
            });

            await connection.on("WorkflowOperationGroup Message", async (message) => {
                await WorkflowOperationGroupSignalR(message);
            });

            await connection.on("updateWorkflowOperation", async (message) => {
                await WorkflowOperationGroupSignalR(message);
            });
        } catch (err) {

            if ($('#LoadRunningProfile').length) {
                RunningExecutionList();
            }
            // console.log('signalR disconnected from WorkflowHub')
        }
    };


    connection.onclose(async () => {
        await new Promise(resolve => setTimeout(resolve, 5000));
        await startSignalRConnection();
    });

    startSignalRConnection()
}

async function WorkflowOperationGroupSignalR(message) {
    if (message) {
        if ($(`.runningWorkflowContainer[operationGroupId=${message?.id}]`).length) {
            let barValue = message?.progressStatus?.replace(/"/g, '');
            let runningcount = barValue.split('/');
            let runningValue = Number(runningcount[0]);
            let totalValue = Number(runningcount[1]);
            let widthPercentage = (runningValue / totalValue) * 100;
            let mode = message?.actionMode?.toLowerCase()

            let workflowStatus = message?.status?.toLowerCase()
            let statusStyle = updateStatusStyle(workflowStatus)
            let barStyle = `bg-${statusStyle}`
            let statusColor = `text-${statusStyle}`

            let workflowId = $(`.runningWorkflowContainer[operationGroupId=${message?.id}]`).attr('id')
            let isPause = message?.isPause == 1 || message?.isPause == 2

            // $(`#${workflowId} .parallelErrorModal, #${workflowId} .waitActionModal`).addClass('d-none')
            $(`#${workflowId} .btnNext, #${workflowId} .btnRetry, #${workflowId} .btnReload, #${workflowId} .btnPauseResume, #${workflowId} .aborted`)
                .prop('disabled', true).css("color", "var(--bs-secondary)");


            if (workflowStatus === 'pause' || workflowStatus === 'paused' || isPause) {
                $(`#${workflowId} .btnPauseResume span`).text('Rerun')

                message?.isPause == 2 && $(`#${workflowId} .btnPauseResume`).prop('disabled', false).css("color", "var(--bs-primary)");

                //$(`#${workflowId} .btnNext, #${workflowId} .btnRetry, #${workflowId} .btnReload`)
                //    .prop('disabled', true).css("color", "var(--bs-secondary)");
            }
            else if (workflowStatus === 'running') {
                $(`#${workflowId} .btnPauseResume span`).text('Pause')
                $(`#${workflowId} .btnPauseResume, #${workflowId} .aborted`).prop('disabled', false).css("color", "var(--bs-primary)");

                //$(`#${workflowId} .btnNext, #${workflowId} .btnRetry, #${workflowId} .btnReload`)
                //    .prop('disabled', true).css("color", "var(--bs-secondary)");


            }
            else if (workflowStatus == "error") {
                $(`#${workflowId} .btnPauseResume span`).text('Pause')
                //$(`#${workflowId} .btnPauseResume`).prop('disabled', true).css("color", "var(--bs-secondary)");


                $(`#${workflowId} .btnNext, #${workflowId} .btnRetry, #${workflowId} .btnReload, #${workflowId} .aborted`)
                    .prop('disabled', false).css("color", "var(--bs-primary)");


            }
            else if (workflowStatus == "success") {
                $(`#${workflowId} .btnPauseResume span`).text('Pause')
                //$(`#${workflowId} .btnPauseResume`).prop('disabled', true).css("color", "var(--bs-secondary)");
                //#${ workflowId } .btnRetry, #${ workflowId } .btnReload, 
                if (mode == 'auto') {
                    $(`#${workflowId} .aborted`).prop('disabled', false).css("color", "var(--bs-primary)");
                } else if (mode === 'step') {
                    $(`#${workflowId} .btnNext, #${workflowId} .btnRetry, #${workflowId} .btnReload, #${workflowId} .aborted`)
                        .prop('disabled', false).css("color", "var(--bs-primary)");
                }
            }
            else if (workflowStatus == "completed") {
                $(`#${workflowId} .btnPauseResume span`).text('Pause')
                //$(`#${workflowId} .btnPauseResume, #${workflowId} .btnNext, #${workflowId} .btnRetry, #${workflowId} .btnReload, #${workflowId} .aborted`)
                //    .prop('disabled', true).css("color", "var(--bs-secondary)");
            }
            else if (workflowStatus == "pending") {
                $(`#${workflowId} .btnPauseResume span`).text('Pause')
                //$(`#${workflowId} .btnPauseResume, #${workflowId} .btnNext, #${workflowId} .btnRetry, #${workflowId} .btnReload`)
                //    .prop('disabled', true).css("color", "var(--bs-secondary)");

                $(`#${workflowId} .aborted`).prop('disabled', false).css("color", "var(--bs-primary)");

            }
            else if (workflowStatus == "wait") {
                //$(`#${workflowId} .btnPauseResume, #${workflowId} .btnNext, #${workflowId} .btnRetry, #${workflowId} .btnReload`)
                //    .prop('disabled', true).css("color", "var(--bs-secondary)");

                $(`#${workflowId} .aborted`).prop('disabled', false).css("color", "var(--bs-primary)");
                $(`#${workflowId} .waitActionModal`).removeClass('d-none')

            } else if (workflowStatus === 'snap') {
                $(`#${workflowId} .aborted`).prop('disabled', false).css("color", "var(--bs-primary)");
                $(`#${workflowId} .snapActionModal`).removeClass('d-none')
            }
            else {
                $(`#${workflowId} .btnPauseResume, #${workflowId} .btnRetry, #${workflowId} .btnReload`)
                    .prop('disabled', true).css("color", "var(--bs-secondary)");

                $(`#${workflowId} .btnNext, #${workflowId} .aborted`).prop('disabled', false).css("color", "var(--bs-primary)");

            }

            $(`#${workflowId} .workflowStatus`).removeClass().addClass(`${statusColor} workflowStatus`).text(message?.status)
            $(`#${workflowId} .progressBarStatusText`).text(message.progressStatus || '0/0')
            $(`#${workflowId} .progressStatus`).css('width', `${widthPercentage}%`).removeClass().addClass(`progressStatus progress-bar progress-bar-striped progress-bar-animated ${barStyle}`)

            if (Number($(`#${workflowId} .workflowProgressBar`).attr('aria-valuemax')) !== totalValue) {
                $(`#${workflowId} .workflowProgressBar`).attr('aria-valuemax', totalValue)
            }

            $(`#${workflowId} .workflowProgressBar`).attr('aria-valuenow', runningValue);
            $(`#${workflowId} .runningActionContainer`).text(message.currentActionName).attr('title', message.currentActionName)

            if (workflowStatus == 'completed' || workflowStatus == 'aborted') {
                if ($("#UserList option:selected").val()  && $("#UserList option:selected").val() !== 'all') {
                    $("#UserList").trigger('change')
                }
                else {

                    loadExistingProfiles();
                }
            }
        }
    }
}


const initiateTimelineSignalR = async () => {
    let signalRUrl = $('#Chat_Bot').attr('signalRurl');
    let url = signalRUrl + "workflowactionresulthub";
    let connection = new signalR.HubConnectionBuilder()
        .withUrl(url, {
            skipNegotiation: true,
            rejectUnauthorized: false,
            //secure: false,
            //ws: true,
            transport: signalR.HttpTransportType.WebSockets
        })
        .configureLogging(signalR.LogLevel.Information)
        .build();

    async function startTimeLineSignalRConnection() {

        try {
            await connection.start();

            await connection.onreconnected(connectionId => {
                console.log("Reconnected. Connection ID:", connectionId);
            });

            await connection.on("workflowActionResult Message", async (message) => {
                if (message && message.hasOwnProperty('stepId')) {
                    await TimeLineViewSignalR(message);
                }

            });

            await connection.on("workflowActionResultUpdate", async (message) => {
                if (message && message.hasOwnProperty('stepId')) {
                    await TimeLineViewSignalR(message);
                }
            })


            // console.log("WorkflowActionResultHub Initiated.");
        } catch (err) {
            ActiveTimeLineList();
            // console.log('signalR disconnected from WorkflowActionResultHub')
        }
    };

    connection.onclose(async () => {
        await new Promise(resolve => setTimeout(resolve, 5000));
        await startTimeLineSignalRConnection();
    });

    // Start the connection.
    startTimeLineSignalRConnection();
}

const TimeLineViewSignalR = (message) => {

    let Icon;
   // $('.timeLineErrorMessage').empty();
    // $('.image').removeAttr("title");

    let workflowId = $(`.runningWorkflowContainer[operationGroupId=${message.workflowOperationGroupId}]`).attr('id')
    let profileId = $(`#${workflowId}`).parents('.profileContainer').attr('id')
    const elementId = message?.stepId;
    const $el = $(`#${CSS.escape(elementId)}`);

    if (message?.status?.toLowerCase() == 'wait') {
        $(`#${workflowId}-wait`).removeClass('d-none').attr('currentActionName', message.workflowActionName).attr('currentActionId', message.actionId).attr('errorMessage', message.message)
    }

    //if (message?.status?.toLowerCase() == 'running') {
    //    let getLiId = $(`.${message?.id}`)
    //    let checkNode = getLiId && getLiId?.attr('nodename')

    //    if (!checkNode && checkNode === 'null' && message?.nodeName) {
    //        getLiId.attr('nodeName', message?.nodeName)
    //        $(`#${message?.id}-node`).empty().removeClass('d-none').attr('title', message?.nodeName).append(`<i class="cp-network align-middle ms-1 fs-10"></i> ${message?.nodeName || 'NA'}`)
    //    }
    //}

    switch (message?.status?.toLowerCase()) {
        case "success":
            Icon = "cp-success text-success fs-6 image";
            break;
        case "error":
            Icon = "cp-error text-danger fs-6 image";
            break;
        case "skip":
            Icon = "cp-skipped text-info fs-6 image";
            break;
        case "bypassed":
            Icon = "cp-by-pass text-info fs-6 image";
            break;
        case "running":
            Icon = "text-primary cp-reload cp-animate image"
            break;
        case "wait":
            Icon = "cp-time text-primary image"
            break;
        case "pause":
        case "paused":
            Icon = "cp-circle-pause text-secondary fs-6 image";
            break;
        case "aborted":
            Icon = "cp-aborted text-warning fs-6 image";
            break;
        default:
            Icon = "cp-pending text-warning image"
            break;
    }

    $(`#${workflowId} .runningActionContainer`).text(message?.currentActionName).attr("title", message?.currentActionName)


    if (message?.successCount && $(`#${profileId} .successCount`)) $(`#${profileId} .successCount`).text(message?.successCount)

    if (message?.errorCount && $(`#${profileId} .errorCount`)) $(`#${profileId} .errorCount`).text(message?.errorCount)

    if (message?.runningCount && $(`#${profileId} .runningCount`)) $(`#${profileId} .runningCount`).text(message?.runningCount)

    if (message?.skipCount && $(`#${profileId} .skipCount`)) $(`#${profileId} .skipCount`).text(message?.skipCount)

    if (message?.bypassedCount && message?.bypassedCount !== 0 && $(`#${profileId} .byPassedCount`)) {
        $(`#${profileId} .byPassedCount`).text(message?.bypassedCount)
        $(`#${profileId} .byPassedCount`).parent().removeClass('d-none')
    }

    //TimeLineView
    if ($el?.length && $el?.is(':visible')) {
        if ($(`#${message?.stepId} .timeLineActineName`)?.length) {
            if (message?.status?.toLowerCase() === "error" && message?.message) {
                $(`#${message?.stepId} .timeLineErrorMessage`).text(message?.message)
                //container.parent().find('.timeLineErrorMessage').text(message?.message)
            } else {
                $(`#${message?.stepId} .timeLineErrorMessage`).text('')
            }
            if (!$(`#${message?.stepId} .image`).hasClass(Icon)) {
                $(`#${message?.stepId} .image`).removeClass().addClass(Icon).attr('data-actionid', message?.id)
            }

            //container.parent().parent().find('.image').removeClass().addClass(Icon)
            //container.parent().parent().find('.image').attr('data-actionid', message?.id)
        }

        $(`#${message?.stepId} .timeLineStartTime`).html(`<i class="cp-time fs-8 me-1"></i>` + message.startTime.replace('T', ' ').split('.')[0])
    }
    if (message?.status?.toLowerCase() === "success" || message?.status?.toLowerCase() === "skipped" || message?.status?.toLowerCase() === "aborted") {
        $(`#${message?.stepId} .timeLineEndTime`).removeClass('d-none').html(`<i class="cp-time-2 fs-8 me-1"></i>` + message?.endTime.replace('T', ' ').split('.')[0])
    }
   

    let parallelLength = $("#timeline_view .cp-Parallel").length
    let timelineLength = $("#timeline_view .cp-Parallel").parent().parent().find('cp-error').length

    if (timelineLength > 1 && parallelLength >= 1) {
        $(`#${workflowId} .parallelErrorModal`).removeClass('d-none')
        $(`#${workflowId}`).find('.btnNext, .btnRetry, .btnReload, .btnPauseResume').attr('disabled', true).removeClass('btn-outline-primary').addClass('btn-outline-secondary').css('color', 'var(--bs-secondary)')
    } else {
        $(`#${workflowId} .parallelErrorModal`).addClass('d-none')
        $(`#${workflowId}`).find('.btnNext, .btnRetry, .btnReload, .btnPauseResume').attr('disabled', false).removeClass('btn-outline-secondary').addClass('btn-outline-primary').css('color', 'var(--bs-primary)')
    }

    setTimeout(() => { 
        if ($('#individualCountCont').hasClass('open')) {
            updateTimelineCounts()
        }   
    }, 400)
}
function isInViewport(el) {
    const rect = el?.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.bottom <= $('#TimelineWorkflowContainer')?.parent()?.innerHeight()
    );
}

const updateTimelineCounts = () => {
    
        let reloadCount = $('#timeline_view .timeLineList .cp-reload').length
        let successCount = $('#timeline_view .timeLineList .cp-success').length
        let skipCount = $('#timeline_view .timeLineList .cp-skipped').length
        let errorCount = $('#timeline_view .timeLineList .cp-error').length
        let bypassCount = $('#timeline_view .timeLineList .cp-by-pass').length

        if (reloadCount && $('#timeline_view .cp-reload')[0]) {
            const reloadEl = $('#timeline_view .cp-reload')[0];
            if (!isInViewport(reloadEl)) reloadEl.scrollIntoView();            
        } else if (errorCount && $('#timeline_view .cp-error')[0]) {
            const errorEl = $('#timeline_view .cp-error')[0];
            if (!isInViewport(reloadEl)) errorEl.scrollIntoView();       
        } else if (successCount && $('#timeline_view .cp-success')) {
            const successEl = $('#timeline_view .cp-success').last()[0];
            if (!isInViewport(successEl)) successEl.scrollIntoView()
        } else if (bypassCount && $('#timeline_view .timeLineList').not('[style*="opacity: 0.5"]').not('cp-pending').last()[0]) {
            const bypassEl = $('#timeline_view .timeLineList').not('[style*="opacity: 0.5"]').not('cp-pending').last()[0];
            if (!isInViewport(bypassEl)) bypassEl.scrollIntoView();        
        } else {
            if ($('#timeline_view .image').not('.cp-pending').length && $('#timeline_view .image').not('.cp-pending').last()[0]) {
                const scrollEl = $('#timeline_view .image').not('.cp-pending').last()[0];
                if (!isInViewport(scrollEl)) scrollEl.scrollIntoView();        
            } else {
                if ($('#timeline_view .image').first()[0]) {
                    const scrollEl = $('#timeline_view .image').first()[0];
                    if (!isInViewport(scrollEl)) scrollEl.scrollIntoView();         
                }

            }
        }

        successCount && $('#timelineWFCountContainer #successTimelineContainer').text(successCount) || $('#timelineWFCountContainer #successTimelineContainer').text('0')
        reloadCount && $('#timelineWFCountContainer #runningTimelineContainer').text(reloadCount) || $('#timelineWFCountContainer #runningTimelineContainer').text('0')
        skipCount && $('#timelineWFCountContainer #skipTimelineContainer').text(skipCount) || $('#timelineWFCountContainer #skipTimelineContainer').text('0')
        errorCount && $('#timelineWFCountContainer #errorTimelineContainer').text(errorCount) || $('#timelineWFCountContainer #errorTimelineContainer').text('0')
        if (bypassCount) {
            $('#timelineWFCountContainer #bypassTimelineContainer').removeClass('d-none').text(bypassCount)
        } else {
            $('#timelineWFCountContainer #bypassTimelineContainer').addClass('d-none').text('0')
        }
}

$(function () {
   // initiateWorkflowSignalR();
  //  initiateTimelineSignalR();
    //setTimeout(() => {
    //    if (workflowServiceStatus) {        
    //        RunningExecutionList();
    //        ActiveTimeLineList();
    //    }
    //}, 3000)
})


// Webworker - SignalR Conncetion

$(function () {
    if (window.Worker) {
        setTimeout(() => {
            const worker = new Worker('../../js/ITAutomation/WorkflowExecution/WebWorker.js');

            let signalRUrl = $('#Chat_Bot').attr('signalRurl');

            // Define multiple SignalR connection configurations
            const connections = [
                { name: "workflowhub", url: `${signalRUrl}workflowhub`, sendConnection: "WorkflowOperationGroup Message", receiveConnection: "updateWorkflowOperation" },
                { name: "workflowactionresulthub", url: `${signalRUrl}workflowactionresulthub`, sendConnection: "workflowActionResult Message", receiveConnection: "workflowActionResultUpdate" },
                { name: "loghub", url: `${signalRUrl}loghub`, receiveConnection: "logevent" },
                { name: "notificationhub", url: `${signalRUrl}notificationhub`, receiveConnection: "notification" }
            ];

            // Start all connections
            worker.postMessage({ action: 'start', connections });

            // Handle messages from the worker
            worker.addEventListener('message', function (e) {
                const data = e?.data;

                if (data?.status === "connected") {
                    console.log(`[${data?.name}] Connected`);
                } else if (data?.status === "disconnected") {
                    console.log(`[${data?.name}] Disconnected`);
                } else if (data?.status === "error") {
                    console.error(`[${data?.name}] Error: ${data?.message}`);
                } else if (data?.status === "message") {
                    // Route data to the correct function
                    let message = data?.user;
                    switch (data?.name) {
                        case "workflowhub":
                            WorkflowOperationGroupSignalR(message);
                            break;
                        case "workflowactionresulthub":
                            if (message && message.hasOwnProperty('stepId')) {
                                TimeLineViewSignalR(message);
                            }
                            break;
                        case "notificationhub":
                            if (message) {
                                TimeLineViewSignalR(message);
                            }
                            break;
                        case "loghub":
                            if (message && message?.length) {
                                let activeGroupId = $('.runningWorkflowContainer.Active-Card').attr('operationgroupid')
                                if (!lastLogIdArray.includes(message[0]?.arrived) && activeGroupId == message[0]?.workflowOperationGroupId) {
                                    if ($('#workflowExecutionLogViewer').is(':visible')) {
                                        bindingLogInModal(message);
                                    } else {
                                        BindingLogEvent(message);
                                    }
                                }
                            }
                           // handleDataHubData(data.user, data.message);
                            break;
                    }
                }
            });

            // Stop all connections
            $('#stopWorker').on('click', function () {
                worker.postMessage({ action: 'stop' });
            });
        },500)
        
    } else {
        console.log("Web Workers are not supported in your browser.");
    }

    // Handle data from ChatHub
    //function handleChatData(user, message) {
    //    $('#ChatHubMessages').append(`<p><strong>${user}:</strong> ${message}</p>`);
    //}

    //// Handle data from NotificationHub
    //function handleNotificationData(user, message) {
    //    $('#NotificationHubMessages').append(`<p>[Notification] <strong>${user}:</strong> ${message}</p>`);
    //}

    //// Handle data from DataHub
    //function handleDataHubData(user, message) {
    //    $('#DataHubMessages').append(`<p>[Data] <strong>${user}:</strong> ${message}</p>`);
    //}
});


