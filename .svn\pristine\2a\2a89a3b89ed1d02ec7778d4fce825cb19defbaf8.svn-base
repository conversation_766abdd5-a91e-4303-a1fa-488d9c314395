﻿let globalId = "", globalImpactTypeId = "", globalCatagoryId = "", globalDeleteid = "", selectedValues = [], globalDriftData, dataTable, parameterNameiconType = "",parameterCatagoryIconType = ""
let parameterUrls = {
    isNameExits : "Drift/DriftParameter/IsNameExist",
    isCatagoryNameExits :"Drift/DriftParameter/IsCategoryNameExist",
    isimapacttypeNameExits : "Drift/DriftParameter/IsImpactTypeNameExist",
    getParameterPagination: "/Drift/Driftparameter/GetPagination",
    GetCplValidation: "Drift/DriftParameter/GetCplValidation",
    parameterCreateOrUpdate: "Drift/DriftParameter/CreateOrUpdate",
    parameterDelete: "Drift/Driftparameter/Delete",
    GetDriftImpactTypeList: "Drift/DriftParameter/GetDriftImpactTypeList",
    DriftImpactTypeCreateOrUpdate: "Drift/DriftParameter/DriftImpactTypeCreateOrUpdate",
    DeleteImpactType: "Drift/DriftParameter/DeleteImpactType",
    GetDriftCategoryList: "Drift/DriftParameter/GetDriftCategoryList",
    DriftCategoryCreateOrUpdate: "Drift/DriftParameter/DriftCategoryCreateOrUpdate",
    DeleteDriftCategory:"Drift/DriftParameter/DeleteDriftCategory"
}
function driftparameterdebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}
$(function () {
    let createPermission = $("#parameterDriftCreate").data("create-permission").toLowerCase();
    let deletePermission = $("#parameterDriftDelete").data("delete-permission").toLowerCase();
    if (createPermission == 'false') {
        $("#parameterCreate").removeClass('#parameterCreate').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }
    dataTable = $('#driftParameterTable').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }, infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": parameterUrls.getParameterPagination,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "driftCategoryName" : sortIndex === 3 ? "driftImpactTypeName" :  
                        sortIndex === 4 ? "severity"  : ""
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#parameterSearchInp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                        json.recordsTotal = json?.data?.totalPages;
                        json.recordsFiltered = json?.data?.totalCount;
                        if (json?.data?.data?.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        globalDriftData = json?.data?.data
                        return json?.data?.data;
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [2, 3, 4, 5],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
                            return (page * meta.settings._iDisplayLength) + meta.row + 1;
                        }
                        return data;
                    }, "orderable": false,
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title="${data ?? "NA"}"> ${data ?? "NA"}</span></td>`
                    }
                },
                {
                    "data": "driftCategoryName",
                    "name": "Category",
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td>${data ?? "NA"}</td>`;
                    }
                },
                {
                    "data": "driftImpactTypeName", 
                    "name": "Impact Type",
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td>${data ?? "NA"}</td>`;
                    }
                },
                {
                    "data": "severity", "name": "Severity", "autoWidth": true,
                    "render": function (data, type, row) {
                        const iconClasses = {
                            0: "fw-bold cp-up-doublearrow text-warning",
                            2: "fw-bold cp-critical-level text-danger",
                            3: "cp-warning text-primary",
                            1: "fw-bold cp-down-doublearrow text-success"
                        };
                        let iconClass = iconClasses[data] || ""; 

                        const tooltip = data == 0 ? "High" : data == 1 ? "Low" : data == 2 ? "Critical" : "Information"
                        return `<span><i class="${iconClass}"></i> ${tooltip}</span>`;
                    }
                },
                {
                    "render": function (data, type, row) {
                        let propertics = JSON.parse(row.properties)
                        propertiesvalue = propertics.propertiesvalue;
                        if (createPermission == "true" && deletePermission == "true") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="edit-button" name="overallupdate" catagoryIcon="${JSON.parse(row.properties).catagoryicon}" nameicon="${JSON.parse(row.properties).Nameicon}"  data_severity="${row.severity}"  data_impactType="${row.driftImpactTypeId}" data_catagory="${row.driftCategoryId}" data_name="${row.name}" updateId="${row.id}" onclick="overallEdit(this)" data-bs-toggle="modal" data-bs-target="#parameterCreateModal" data_textpath='${propertiesvalue}'>
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="delete-button" delete_id="${row.id}" deletename="${row.name}" onclick="overallDeleteBtn(this)" data-bs-toggle="modal" data-bs-target="#parameterDeleteModal">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>
                        </td>`;
                        }
                        if (createPermission == "false" && deletePermission == "true") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                                        <i class="cp-edit"></i>
                                                    </span>
                                <span role="button" title="Delete" class="delete-button" delete_id="${row.id}" deletename="${row.name}" onclick="overallDeleteBtn(this)" data-bs-toggle="modal" data-bs-target="#parameterDeleteModal">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>
                        </td>`;
                        }
                        if (createPermission == "true" && deletePermission == "false") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="edit-button" name="overallupdate" catagoryIcon="${JSON.parse(row.properties).catagoryicon}" nameicon="${JSON.parse(row.properties).Nameicon}"  data_severity="${row.severity}"  data_impactType="${row.driftImpactTypeId}" data_catagory="${row.driftCategoryId}" data_name="${row.name}" updateId="${row.id}" onclick="overallEdit(this)" data-bs-toggle="modal" data-bs-target="#parameterCreateModal" data_textpath='${propertiesvalue}'>
                                    <i class="cp-edit"></i>
                                <span role="button" title="Delete" class="icon-disabled">
                                                        <i class="cp-Delete"></i>
                                                    </span>    
                            </div>
                        </td>`;
                        }
                        if (createPermission == "false" && deletePermission == "false") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                                        <i class="cp-edit"></i>
                                                    </span>
                               <span role="button" title="Delete" class="icon-disabled">
                                                        <i class="cp-Delete"></i>
                                                    </span>
                            </div>
                        </td>`;
                        }
                    },

                    "orderable": false,
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        }
    )
    $('#parameterSearchInp').on('keydown input', driftparameterdebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        } 
        const inputValue = $('#parameterSearchInp').val();
        const nameCheckbox = $("#paraName");
        if (nameCheckbox.is(':checked')) {
            selectedValues.push(nameCheckbox.val() + inputValue);
        }
            selectedValues.push(inputValue);
            var currentPage = dataTable.page.info().page + 1;
            if (!isNaN(currentPage)) {
                dataTable.ajax.reload(function (json) {
                    if (e.target.value && json.recordsFiltered === 0) {
                        $('.dataTables_empty').text('No matching records found');
                    }
                }, false)
            }
    }, 500))
})
$(".parameterCancel").on("click", function () {
    clearErrorElements()
})
$("#parameterCreate").on("click", function () { 
    clearValue()
    getCatagory()
    getImpactType()
    $("#parameterNameiconSelected").attr("class", "cp-aborted")
    $("#parameterCatagaoryIcon").attr("class", "cp-aborted")
})
function clearValue() {
    $("#parameterCheckbox").prop("checked", false)
    $("#parameterName,#parameterCatagory,#parameterImpactType,#parameterSeverity,#parameterTextpath").val("")
    $('#driftParameterSave').text("Save");
    $('#parameterCollapseCatagoryIcon').removeClass('show')
    $('#paramterCollapseIcon').removeClass('show')
}
function clearErrorElements() {
$("#parameterTextpathError,#parameterNameError,#parameterHandlingModeError,#parameterCatagoryError,#parameterImpactTypeError, #parameterSeverityError,#parameterFindByError,#parameterThresholdError").text('').removeClass('field-validation-error');
}
function validateDriftDropDown(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg);
        errorElement.addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('');
        errorElement.removeClass('field-validation-error');
        return true;
    }
}
async function validateDriftNameDropDown(value, errorMsg, errorElement, url) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg);
        errorElement.addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    let urls = RootUrl + url;
    let data = {};
    data.id = null;
    data.name = value
    const validationResults = [
        await SpecialCharValidateCustom(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsSameNameExist(urls, data)
    ];
    const failedValidations = validationResults.filter(result => result !== true);
    if (failedValidations.length > 0) {
        errorElement.text(failedValidations[0]);
        errorElement.addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('');
        errorElement.removeClass('field-validation-error');
        return true;
    }
}
async function IsSameNameExist(url, inputValue) {
    return !inputValue.name.trim() || $("#driftParameterSave").text() == "Update" || $("#parameterAddProfileCatagorySave").text() == "Update" || $("#parameterAddProfileTypeSave").text() == "Update" ? true : (await GetAsync(url, inputValue, OnError)) ? " Name already exists" : true;
}

$("#parameterCollapseIcon div.parameterCategoryIcon i").on('click', function () {
    parameterNameiconType = $(this).attr("title")
    if ($(this).length) {
        parameterNameiconType = $(this)[0].classList[0]
    }
    $("#parameterNameiconSelected").attr("class", $(this).attr("class"))
    $('#parameterCollapseIcon').removeClass('show')
})
$("#parameterCollapseCatagoryIcon div.parameterCategoryIcon i").on('click', function () {
    parameterCatagoryIconType = $(this).attr("title")
    if ($(this).length) {
        parameterCatagoryIconType = $(this)[0].classList[0]
    }
    $("#parameterCatagaoryIcon").attr("class", $(this).attr("class"))
    $('#parameterCollapseCatagoryIcon').removeClass('show')
})
$("#parameterName").on("input", driftparameterdebounce( async function () {
    let value = await sanitizeInput($("#parameterName").val());
    $("#parameterName").val(value);
    validateDriftNameDropDown(value, "Enter parameter name", $("#parameterNameError"), parameterUrls.isNameExits)
}, 400))

$("#parameterCatagory").on("change", function () {
        validateDriftDropDown($(this).val(), "Select category", $("#parameterCatagoryError"))
})
$("#parameterImpactType").on("change", function () {
        validateDriftDropDown($(this).val(), "Select impact type", $("#parameterImpactTypeError"))
})
$("#parameterSeverity").on("change", function () {
        validateDriftDropDown($(this).val(), "Select severity", $("#parameterSeverityError"))
})
$("#parameterThreshold").on("keypress", function (event) {
    ["e", "E", "+", "-", "."].includes(event.key) && event.preventDefault()
    if ($(this).val().length >= 6) {
        event.preventDefault()
    }
})
$("#parameterThreshold").on("input", function (e) {      
     validateDriftDropDown($(this).val(), "Enter threshold", $("#parameter_threshold_error"))
})
$("#parameterTextpath").on("input", function () {
    validateDriftDropDown($(this).val(), "Enter path", $("#parameterTextpathError"))
})
$("#parameterExecute").on("click", async function () {
    let parameterExecuteTextpath = validateDriftDropDown($("#parameter_textpath").val(), "Enter path", $("#parameterTextpathError"))
    if (parameterExecuteTextpath) {
        executionCommment()
    }
})
 async function executionCommment() {
    await $.ajax({
        type: "POST",
        url: RootUrl + parameterUrls.GetCplValidation ,
        dataType: 'text',
        data: { script: $("#parameterTextpath").val() },
        success: function (result) {
            //const errorElement = $("#parameter_textpath_error")
            //if (result == null || result == undefined || result == "") {
            //    errorElement.text("Comment Success");
            //    errorElement.removeClass('field-validation-error');
            //    errorElement.addClass('text-success');
            //    errorElement.addClass('text-end');
            //    errorElement.css("width", "100%");
            //    errorElement.css("position", "absolute");
            //    return true;
            //} else {
            //    errorElement.text(result);
            //    errorElement.removeClass('text-success');
            //    errorElement.addClass('field-validation-error');
            //    return false;
            //}   
        },
    })
}
$("#driftParameterSave").on("click", async function () {
    const form = $("#parameterCreateModal")

    const parameterName = await validateDriftNameDropDown($("#parameterName").val(), "Enter parameter name", $("#parameterNameError"), parameterUrls.isNameExits)

    const parameterCatagory = validateDriftDropDown($("#parameterCatagory").val(), "Select category", $("#parameterCatagoryError"))

    const parameterImpactType = validateDriftDropDown($("#parameterImpactType").val(), "Select impact type", $("#parameterImpactTypeError"))

    const parameterSeverity = validateDriftDropDown($("#parameterSeverity").val(), "Select severity", $("#parameterSeverityError"))

    const parameterPath = await validateDriftDropDown($("#parameterTextpath").val(), "Enter path", $("#parameterTextpathError"))
    let datas
    
    if (parameterPath) {
        await $.ajax({
            type: "POST",
            url: RootUrl + parameterUrls.GetCplValidation,
            dataType: 'text',
            data: { script: $("#parameterTextpath").val() },
            success: function (result) {
                datas = true
                //const errorElement = $("#parameterTextpathError")
                //if (result == null || result == undefined || result == "") {
                //    datas = true
                //    errorElement.text("");
                //    errorElement.removeClass('field-validation-error');
                //    //return true;
                //} else {
                //    datas = false
                //    errorElement.text(result);
                //    errorElement.addClass('field-validation-error');
                //    //return false;
                //}
            },
        })
    }
    if (parameterName && parameterPath && parameterCatagory && parameterImpactType && parameterSeverity && datas)  {
        form.trigger("submit")
        let propertydata = JSON.stringify({
            "propertiesvalue": $("#parameterTextpath").val(),
            "Nameicon": parameterNameiconType == "" ? "cp-aborted" : parameterNameiconType,
            "catagoryicon": parameterCatagoryIconType == "" ? "cp-aborted" : parameterCatagoryIconType
        })
        let data = {
            "Name": $("#parameterName").val(),
            "DriftCategoryId": $("#parameterCatagory").val(),
            "DriftCategoryName": $("#parameterCatagory option:selected").text(),
            "DriftImpactTypeId": $("#parameterImpactType").val(),
            "DriftImpactTypeName": $("#parameterImpactType option:selected").text(),
            "Severity":Number($("#parameterSeverity").val()),
            "Properties": propertydata ,
            __RequestVerificationToken: gettoken()
        }

        $('#driftParameterSave').text() === "Update" ? data["id"] = globalId : null

        await $.ajax({
            type: "POST",
            url: RootUrl + parameterUrls.parameterCreateOrUpdate ,
            dataType: "json",
            data: data,
            success: function (result) {
                let data = result.data
                if (result.success) {
                    $('#parameterCreateModal').modal('hide');
                    notificationAlert('success', data.message)
                    setTimeout(() => {
                        dataTable.ajax.reload()
                    },1000) 
                } else {
                    errorNotification(result)
                }
            },
        })
        $('#driftParameterSave').text("Save");
        $("#parameterName").val("")
        $("#parameterCatagory,#parameterImpactType,#parameterSeverity,#parameterTextpath").val("").trigger("change")
        clearErrorElements()
    }
})
function overallEdit(data) {
    getCatagory()
    getImpactType()
    if ($(data).attr("name") == "overallupdate") {
        $('#driftParameterSave').text("Update");
        globalId = $(data).attr('updateId');
        globalDriftData?.forEach((data) => {       
            if (globalId === data.id) {  
                let propertiesvalue = JSON.parse(data?.properties)?.propertiesvalue
                $("#parameterTextpath").val(propertiesvalue)
            }
        })
        $("#parameterName").val($(data).attr('data_name'))
        setTimeout(() => {
            $("#parameterCatagory").val($(data).attr('data_catagory')).trigger("change")
            $("#parameterImpactType").val($(data).attr('data_impactType')).trigger("change")
            $("#parameterSeverity").val($(data).attr('data_severity')).trigger("change")
            //$("#parameterTextpath").val($(data).attr('data_textpath'))
            $("#parameterNameiconSelected").attr("class", $(data).attr('nameicon'))
            //$("#parameterCatagaoryIcon").attr("class", $(data).attr('catagoryIcon'))
        }, 800)
    }
}
function overallDeleteBtn(data){
    globalDeleteid = $(data).attr('delete_id')
    $("#parameterOverallDeletedId").text($(data).attr('deletename')).attr("title", $(data).attr('deletename'));
}
$("#parameterOverallConfirmDeleteButton").on("click", async function () {
    await $.ajax({
        type: "POST",
        url: RootUrl + parameterUrls.parameterDelete,
        dataType: "json",
        data: {
            id: globalDeleteid,
            __RequestVerificationToken: gettoken()
        },
        success: function (result) {
            let data = result.data
            if (result.success) {
                $('#parameterDeleteModal').modal('hide');
                notificationAlert("success", data.message)
                dataTable.ajax.reload()
            } else {
                errorNotification(result)
            }
        },
    })
})
//impact type
$(".parameterAddProfileTypeCancel").on("click", function () {
    $("#parameterAddProfileType").val("")
    $("#parameterAddProfiletypeError").text("").removeClass('field-validation-error');
})
$("#parameterImpactTypeModal").on("click", function () {
    $("#parameterAddProfileTypeSave").text("Save")
    getImpactType()
})

const getImpactType =async () => {
    $("#parameterImpactTypeModal tbody").empty()
   await $.ajax({
        type: "GET",
        url: RootUrl + parameterUrls.GetDriftImpactTypeList,
        dataType: "json",
        success: function (result) {
            let data = result.data
            $("#parameter_impact_type").empty()
            if (result.success) {
                data?.forEach(function (item, i) {
                    let sno = i + 1
                    $('#parameterImpactType').append('<option value=""></option>')
                    $("#parameterImpactType").append('<option value="' + item.id + '">' + item.impactType + '</option>')
                    $("#parameterImpactTypeModal tbody ").append('<tr>' +
                        '<td>' + sno + '</td>' +
                        '<td>' + item.impactType + '</td>' +
                        '<td>' +
                        '<div class="d-flex align-items-center gap-2" >' +
                        '<span role="button" title="Edit" class=" edit-button" onclick="editImpactType(this)" name="editcatagory" data_id="' + item.id + '" data_impacttype="' + item.impactType + '">' +
                        '<i class="cp-edit"></i>' +
                        '</span>' +
                        '<span role="button" title="Delete" class="delete-button" data-bs-toggle="modal" onclick="impactTypeDelete(this)" deletename="' + item.impactType + '" deleteId="' + item.id + '" data-bs-target="#parameterDeleteModal2">' +
                        '<i class="cp-Delete"></i>' +
                        '</span>' +
                        '</div>' +
                        '</td>' +
                        '</tr>')
                })
            } else {
                errorNotification(result)
            }
        },
    })
}
function editImpactType(data) {
    globalImpactTypeId = $(data).attr('data_id');
    $("#parameterAddProfileType").val($(data).attr('data_impacttype'))
    $("#parameterAddProfileTypeSave").text("Update")
}
$("#parameterAddProfileType").on("input",async function () {
    let value = await sanitizeInput($("#parameterAddProfileType").val());
    $("#parameterAddProfileType").val(value);
    validateDriftNameDropDown(value, "Enter impact type", $("#parameterAddProfileTypeError"), parameterUrls.isimapacttypeNameExits)
})
$("#parameterAddProfileTypeSave").on("click",async function () {
    const form = $("#parameterImpactTypeModalToggle")
    const add_profile = await validateDriftNameDropDown($("#parameterAddProfileType").val(), "Enter impact type", $("#parameterAddProfileTypeError"), parameterUrls.isimapacttypeNameExits)
    if (add_profile) {  
        form.trigger("submit")
            let data = {
                "ImpactType": $("#parameterAddProfileType").val(),
                __RequestVerificationToken: gettoken()
            }
        $('#parameterAddProfileTypeSave').text() === "Update" ? data["id"] = globalImpactTypeId : null
            await $.ajax({
                type: "POST",
                url: RootUrl + parameterUrls.DriftImpactTypeCreateOrUpdate ,
                dataType: "json",
                data: data,
                success: function (result) {
                    let data = result.data
                    if (result.success) {
                        notificationAlert("success", data.message)
                        getImpactType()
                    } else {
                        errorNotification(result)
                    }
                },
            })
        $("#parameterAddProfileTypeSave").text("Save")
        $("#parameterAddProfileType").val("")
        $("#parameterAddProfileTypeError").text("").removeClass('field-validation-error');
    }
})
function impactTypeDelete(data) {
    globalDeleteid = $(data).attr('deleteId')
    $("#parameterImpacttypeDeletedId").text($(data).attr('deletename')).attr("title", $(data).attr('deletename'));
}
$("#parameterImpacttypeConfirmDeleteButtonCancel").on("click", function () {
    deleteImpacttypeCancel()
})
function deleteImpacttypeCancel() {
    $("#parameterAddProfileType").val("")
    $("#parameterAddProfileTypeError").text("").removeClass('field-validation-error');
    $('#parameterDeleteModal2').modal('hide');
    $('#parameterImpactTypeModalToggle').modal('show');
    $("#parameterAddProfileTypeSave").text("Save")
}
$("#parameterImpacttypeConfirmDeleteButton").on("click", async function () {
    await $.ajax({
        type: "POST",
        url: RootUrl + parameterUrls.DeleteImpactType ,
        dataType: "json",
        data: {
            id: globalDeleteid,
            __RequestVerificationToken: gettoken()
        },
        success: function (result) {
            let data = result.data
            if (result.success) {
                notificationAlert("success", data.message)
                getImpactType()
                deleteImpacttypeCancel()
            } else {
                errorNotification(result)
            }
        },
    })
})
//catagory 
$("#parameterCategoryModal").on("click", function () {
    $("#parameterAddProfileCatagory").val("")
    $("#parameterAddProfileCatagoryError").text("").removeClass('field-validation-error');
    $("#parameterCatagaoryIcon").attr("class", "cp-aborted")
    $("#parameterAddProfileCatagorySave").text("Save")
    getCatagory()
})
const getCatagory = async () => {
    $("#parameterCatagoryTable tbody").empty()
    await $.ajax({
        type: "GET",
        url: RootUrl + parameterUrls.GetDriftCategoryList,
        dataType: "json",
        success: function (result) {
            let data = result.data
            if (result.success) {
                $("#parameterCatagory").empty()
                data?.forEach(function (item, i) { 
                    let sno = i + 1
                    $('#parameterCatagory').append('<option value=""></option>')
                    $("#parameterCatagory").append('<option value="' + item.id + '">' + item.categoryName + '</option>')

                    $("#parameterCatagoryTable tbody ").append('<tr>'+
                            '<td>'+sno+'</td>'+
                            '<td>' + item.categoryName+ '</td>'+
                            '<td>'+
                                '<div class="d-flex align-items-center gap-2" >'+
                            '<span role="button" title="Edit" class=" edit-button" onclick="editCatagory(this)" name="editcatagory" data_id="' + item.id + '" data_catagoryIcon="'+item.logo+'" data_categoryName="' + item.categoryName+'">'+
                                       '<i class="cp-edit"></i>'+
                                    '</span>'+
                            '<span role="button" title="Delete" class="delete-button" data-bs-toggle="modal" onclick="catagoryDelete(this)" deletename="' + item.categoryName +'" deleteId="'+item.id+'" data-bs-target="#parameterDeleteModal1">'+
                                        '<i class="cp-Delete"></i>'+
                                    '</span>'+
                                '</div>'+
                            '</td>'+
                            '</tr>')
                })
            } else {
                errorNotification(result)
            }
        },
    })
}
function editCatagory(data) {
    globalCatagoryId = $(data).attr('data_id');
    $("#parameterAddProfileCatagory").val($(data).attr('data_categoryName'))
    setTimeout(() => {
        $("#parameterCatagaoryIcon").attr("class", $(data).attr('data_catagoryIcon'))
    },500)
    $("#parameterAddProfileCatagorySave").text("Update")
}
$(".parameterAddProfileCatagoryCancel").on("click", function () {
    $("#parameterAddProfileCatagory").val("")
    $("#parameterAddProfileCatagoryError").text("").removeClass('field-validation-error');
})
$("#parameterAddProfileCatagory").on("input", async function () {
    let value = await sanitizeInput($("#parameterAddProfileCatagory").val());
    $("#parameterAddProfileCatagory").val(value);
    validateDriftNameDropDown(value, "Enter category name", $("#parameterAddProfileCatagoryError"), parameterUrls.isCatagoryNameExits)
})
$("#parameterAddProfileCatagorySave").on("click", async function () {
    const form = $("#parameterCategoryModalToggle")
    const add_profile = await validateDriftNameDropDown($("#parameterAddProfileCatagory").val(), "Enter category name", $("#parameterAddProfileCatagoryError"), parameterUrls.isCatagoryNameExits)
    if (add_profile) {
        form.trigger("submit")
        let data = {
            "CategoryName": $("#parameterAddProfileCatagory").val(),
            "Logo": parameterCatagoryIconType == "" ? "cp-aborted" : parameterCatagoryIconType,
            __RequestVerificationToken: gettoken()
        }

        $('#parameterAddProfileCatagorySave').text() === "Update" ? data["id"] = globalCatagoryId : null

        await $.ajax({
            type: "POST",
            url: RootUrl + parameterUrls.DriftCategoryCreateOrUpdate ,
            dataType: "json",
            data: data,
            success: function (result) {
                let data = result.data
                if (result.success) {
                    notificationAlert("success", data.message)
                    getCatagory()
                } else {
                    errorNotification(result)
                }
            },
        })
        $("#parameterAddProfileCatagorySave").text("Save")
        $("#parameterAddProfileCatagory").val("")
        $("#parameterCatagaoryIcon").attr("class", "cp-aborted")
        $("#parameterAddProfileCatagoryError").text("").removeClass('field-validation-error');  
    }
})
function catagoryDelete(data) {
    globalDeleteid = $(data).attr('deleteId')
    $("#parameterCatagoryDeletedId").text($(data).attr('deletename')).attr("title", $(data).attr('deletename'));
}
$("#parameterCatagoryConfirmDeleteButtonCancel").on("click", function () {
deleteCatagoryCancel()
})
function deleteCatagoryCancel() {
    $("#parameterAddProfileCatagory").val("")
    $("#parameterAddProfileCatagoryError").text("").removeClass('field-validation-error');
    $('#parameterDeleteModal1').modal('hide');
    $('#parameterCategoryModalToggle').modal('show');
    $("#parameterAddProfileCatagorySave").text("Save")
}
$("#parameterCatagoryConfirmDeleteButton").on("click", async function () {
    await $.ajax({
        type: "POST",
        url: RootUrl + parameterUrls.DeleteDriftCategory,
        dataType: "json",
        data: {
            id: globalDeleteid,
            __RequestVerificationToken: gettoken()
        },
        success: function (result) {
            let data = result.data
            if (result.success) {
                notificationAlert("success", data.message)
                getCatagory()
                deleteCatagoryCancel()
            } else {
                errorNotification(result)
            }
        },
    })
})
