﻿using ContinuityPatrol.Application.Features.ComponentType.Commands.Create;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Update;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Job.Queries.GetSolutionType;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class ComponentTypeProfile : Profile
{
    public ComponentTypeProfile()
    {
        CreateMap<CreateComponentTypeCommand, ComponentTypeViewModel>().ReverseMap();
        CreateMap<UpdateComponentTypeCommand, ComponentTypeViewModel>().ReverseMap();

        CreateMap<ComponentType, CreateComponentTypeCommand>().ReverseMap();
        CreateMap<UpdateComponentTypeCommand, ComponentType>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<ComponentType, ComponentTypeDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<ComponentType, ComponentTypeListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<ComponentType, ComponentTypeModel>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap< PaginatedResult<ComponentType>, PaginatedResult<ComponentTypeListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
        CreateMap<ComponentType, SolutionTypeListByPolicyNameVm>()
           .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
  
    }
}