﻿using ContinuityPatrol.Application.Features.Server.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.PageBuilderModel;
using ContinuityPatrol.Shared.Tests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Server.Commands;

public class CreateServerTests : IClassFixture<ServerFixture>, IClassFixture<SiteFixture>
{
    private readonly ServerFixture _serverFixture;
    private readonly SiteFixture _siteFixture;
    private readonly Mock<IServerRepository> _mockServerRepository;
    private readonly Mock<ISiteRepository> _mockSiteRepository = new();
    private readonly Mock<ISiteTypeRepository> _mockSiteTypeRepository = new();
    public readonly Mock<ILicenseManagerRepository> MockLicenseManagerServiceRepository;
    private readonly CreateServerCommandHandler _handler;

    public CreateServerTests(ServerFixture serverFixture, SiteFixture siteFixture)
    {
        _serverFixture = serverFixture;

        _siteFixture = siteFixture;

        _mockSiteTypeRepository = new Mock<ISiteTypeRepository>();

        var mockPublisher = new Mock<IPublisher>();

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();


        _mockServerRepository = ServerRepositoryMocks.CreateServerRepository(_serverFixture.Servers);

        _mockSiteRepository = SiteRepositoryMocks.CreateSiteRepository(_siteFixture.Sites);

        MockLicenseManagerServiceRepository = LicenseManagerModelRepositoryMocks.GetLicenseManagerPoNumberRepository(_serverFixture.LicenseManagers);

        _handler = new CreateServerCommandHandler(_serverFixture.Mapper, _mockServerRepository.Object, mockPublisher.Object, mockLoggedInUserService.Object, MockLicenseManagerServiceRepository.Object, _mockSiteRepository.Object, _mockSiteTypeRepository.Object);

        //_serverFixture.LicenseManagers[0].PONumber = "PO-3355";
        //_serverFixture.CreateServerCommand.LicenseKey = _serverFixture.LicenseManagers[0].PONumber;

    }
    
    [Fact]
    public async Task Handle_Should_IncreaseServerCount_When_AddValidServer()
    {
        var command = new CreateServerCommand
        {
            LicenseId = "123",
            LicenseKey = "ValidLicenseKey",
            RoleType = "Web",
            Properties = "{\"HostName\":\"MyHost\", \"IpAddress\":\"127.0.0.1\"}"
        };

        var licenseDetail = new Domain.Entities.LicenseManager { ReferenceId = "License123", PoNumber = "PO123" };
        var serverEntity = new Domain.Entities.Server { ReferenceId = "Server123", Name = "TestServer", SiteId = "Site123" };

        var site = new Domain.Entities.Site { TypeId = "ValidSiteTypeId" };
        var siteType = new Domain.Entities.SiteType { Category = "Primary" };

     
        MockLicenseManagerServiceRepository.Setup(x => x.GetLicenseDetailByIdAsync(command.LicenseId))
            .ReturnsAsync(licenseDetail);
        _mockServerRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.Server>()))
            .ReturnsAsync(serverEntity);
        _mockSiteRepository.Setup(x => x.GetByReferenceIdAsync(serverEntity.SiteId))
            .ReturnsAsync(site);
        _mockSiteTypeRepository.Setup(x => x.GetByReferenceIdAsync(site.TypeId))
            .ReturnsAsync(siteType);

        await _handler.Handle(command, CancellationToken.None);

        var allServers = await _mockServerRepository.Object.ListAllAsync();
        Assert.Equal(_serverFixture.Servers.Count, allServers.Count);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulServerResponse_When_AddValidServer()
    {
        var command = _serverFixture.CreateServerCommand;

        var mockLicenseDetail = new Domain.Entities.LicenseManager
        {
            ReferenceId = "valid-license-id",
            PoNumber = "PO12345"
        };
        MockLicenseManagerServiceRepository
            .Setup(x => x.GetLicenseDetailByIdAsync(It.IsAny<string>()))
            .ReturnsAsync(mockLicenseDetail);

        var mockSite = new Domain.Entities.Site
        {
            ReferenceId = command.SiteId,
            TypeId = "valid-site-type-id"
        };
        _mockSiteRepository
            .Setup(x => x.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync(mockSite);

        var mockSiteType = new Domain.Entities.SiteType
        {
            ReferenceId = mockSite.TypeId,
            Category = "primary"
        };
        _mockSiteTypeRepository
            .Setup(x => x.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync(mockSiteType);

        var mockServer = new Domain.Entities.Server
        {
            ReferenceId = "valid-server-id",
            Name = "TestServer"
        };
        _mockServerRepository
            .Setup(x => x.AddAsync(It.IsAny<Domain.Entities.Server>()))
            .ReturnsAsync(mockServer);

        var result = await _handler.Handle(command, CancellationToken.None);

        result.ShouldBeOfType<CreateServerResponse>();
        result.ServerId.ShouldBe(mockServer.ReferenceId);
        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        var mockLicenseDetail = new Domain.Entities.LicenseManager
        {
            ReferenceId = "valid-license-id",
            PoNumber = "PO12345"
        };
        MockLicenseManagerServiceRepository
            .Setup(x => x.GetLicenseDetailByIdAsync(It.IsAny<string>()))
            .ReturnsAsync(mockLicenseDetail);

        var mockSite = new Domain.Entities.Site
        {
            ReferenceId = "valid-site-id",
            TypeId = "valid-site-type-id"
        };
        _mockSiteRepository
            .Setup(x => x.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync(mockSite);

        var mockSiteType = new Domain.Entities.SiteType
        {
            ReferenceId = mockSite.TypeId,
            Category = "primary"
        };
        _mockSiteTypeRepository
            .Setup(x => x.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync(mockSiteType);

        var mockServer = new Domain.Entities.Server
        {
            ReferenceId = "valid-server-id",
            Name = "TestServer"
        };
        _mockServerRepository
            .Setup(x => x.AddAsync(It.IsAny<Domain.Entities.Server>()))
            .ReturnsAsync(mockServer);

        await _handler.Handle(_serverFixture.CreateServerCommand, CancellationToken.None);

        _mockServerRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.Server>()), Times.Once);
    }

}