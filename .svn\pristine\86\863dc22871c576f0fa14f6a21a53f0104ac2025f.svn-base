using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SiteTypeFixture : IDisposable
{
    public List<SiteType> SiteTypePaginationList { get; set; }
    public List<SiteType> SiteTypeList { get; set; }
    public SiteType SiteTypeDto { get; set; }


    public ApplicationDbContext DbContext { get; private set; }

    public SiteTypeFixture()
    {
        var fixture = new Fixture();

        SiteTypeList = fixture.Create<List<SiteType>>();

        SiteTypePaginationList = fixture.CreateMany<SiteType>(20).ToList();

        SiteTypeDto = fixture.Create<SiteType>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public SiteType CreateSiteType(
        string type = "Default Site Type",
        string category = "Default Category",
        bool isActive = true,
        bool isDelete = false)
    {
        return new SiteType
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = type,
            Category = category,
            IsActive = isActive,
            IsDelete = isDelete,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<SiteType> CreateMultipleSiteTypes(int count)
    {
        var siteTypes = new List<SiteType>();
        for (int i = 1; i <= count; i++)
        {
            siteTypes.Add(CreateSiteType(
                type: $"Site Type {i}",
                category: $"Category {i}"
            ));
        }
        return siteTypes;
    }

    public SiteType CreateSiteTypeWithSpecificId(string referenceId, string type = "Test Site Type")
    {
        return new SiteType
        {
            ReferenceId = referenceId,
            Type = type,
            Category = "Test Category",
            IsActive = true,
            IsDelete = false,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public SiteType CreateSiteTypeForCategory(string category, string type = null)
    {
        return CreateSiteType(
            type: type ?? $"Type for {category}",
            category: category
        );
    }

    public List<SiteType> CreateSiteTypesForCategories(List<string> categories)
    {
        var siteTypes = new List<SiteType>();
        foreach (var category in categories)
        {
            siteTypes.Add(CreateSiteTypeForCategory(category));
        }
        return siteTypes;
    }

    public List<SiteType> CreateSiteTypesWithStatus(int activeCount, int inactiveCount)
    {
        var siteTypes = new List<SiteType>();

        for (int i = 1; i <= activeCount; i++)
        {
            siteTypes.Add(CreateSiteType(
                type: $"Active Type {i}",
                category: $"Active Category {i}",
                isActive: true
            ));
        }

        for (int i = 1; i <= inactiveCount; i++)
        {
            siteTypes.Add(CreateSiteType(
                type: $"Inactive Type {i}",
                category: $"Inactive Category {i}",
                isActive: false
            ));
        }

        return siteTypes;
    }

    public SiteType CreatePrimarySiteType()
    {
        return CreateSiteType(
            type: "Primary",
            category: "Primary"
        );
    }

    public SiteType CreateDRSiteType()
    {
        return CreateSiteType(
            type: "DR",
            category: "DR"
        );
    }

    public SiteType CreateBackupSiteType()
    {
        return CreateSiteType(
            type: "Backup",
            category: "Backup"
        );
    }

    public List<SiteType> CreateStandardSiteTypes()
    {
        return new List<SiteType>
        {
            CreatePrimarySiteType(),
            CreateDRSiteType(),
            CreateBackupSiteType()
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
