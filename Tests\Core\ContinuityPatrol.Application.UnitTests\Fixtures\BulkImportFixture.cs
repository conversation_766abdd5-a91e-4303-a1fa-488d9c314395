using ContinuityPatrol.Application.Features.BulkImport.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Next;
using ContinuityPatrol.Application.Features.BulkImport.Commands.RollBack;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class BulkImportFixture : IDisposable
{
    public List<BulkImportOperationGroup> BulkImportOperationGroups { get; set; }
    public List<BulkImportActionResult> BulkImportActionResults { get; set; }
    public CreateBulkImportCommand CreateBulkImportCommand { get; set; }
    public NextBulkImportCommand NextBulkImportCommand { get; set; }
    public RollBackBulkImportCommand RollBackBulkImportCommand { get; set; }
    public IMapper Mapper { get; set; }

    public BulkImportFixture()
    {
        BulkImportOperationGroups = new List<BulkImportOperationGroup>
        {
            new BulkImportOperationGroup
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BulkImportOperationId = Guid.NewGuid().ToString(),
                InfraObjectName = "TestInfraObject",
                CompanyId = Guid.NewGuid().ToString(),
                Properties = "{\"ServerList\":[],\"DatabaseList\":[],\"ReplicationList\":[],\"InfraObject\":null}",
                ProgressStatus = "0/10",
                Status = "Pending",
                ErrorMessage = "",
                ConditionalOperation = 1,
                NodeId = "Node001",
                IsActive = true
            }
        };

        BulkImportActionResults = new List<BulkImportActionResult>
        {
            new BulkImportActionResult
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                NodeId = "Node001",
                NodeName = "TestNode",
                BulkImportOperationId = Guid.NewGuid().ToString(),
                BulkImportOperationGroupId = Guid.NewGuid().ToString(),
                EntityId = Guid.NewGuid().ToString(),
                EntityName = "TestEntity",
                EntityType = "Server",
                Status = "Pending",
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddHours(1),
                ErrorMessage = "",
                ConditionalOperation = 1,
                IsActive = true
            }
        };

        // Ensure we have at least the manual data
        if (!BulkImportOperationGroups.Any())
        {
            throw new InvalidOperationException("BulkImportOperationGroups should have been initialized with manual data");
        }

        if (!BulkImportActionResults.Any())
        {
            throw new InvalidOperationException("BulkImportActionResults should have been initialized with manual data");
        }

        // Create additional entities using AutoFixture and add to existing lists
        try
        {
            var additionalGroups = AutoBulkImportFixture.CreateMany<BulkImportOperationGroup>(2).ToList();
            BulkImportOperationGroups.AddRange(additionalGroups);

            var additionalResults = AutoBulkImportFixture.CreateMany<BulkImportActionResult>(2).ToList();
            BulkImportActionResults.AddRange(additionalResults);

            CreateBulkImportCommand = AutoBulkImportFixture.Create<CreateBulkImportCommand>();
            NextBulkImportCommand = AutoBulkImportFixture.Create<NextBulkImportCommand>();
            RollBackBulkImportCommand = AutoBulkImportFixture.Create<RollBackBulkImportCommand>();
        }
        catch (Exception ex)
        {
            // Fallback to minimal setup if AutoFixture fails
            // Log the exception for debugging
            System.Diagnostics.Debug.WriteLine($"AutoFixture failed: {ex.Message}");

            CreateBulkImportCommand = new CreateBulkImportCommand { Id = BulkImportOperationGroups.First().ReferenceId };
            NextBulkImportCommand = new NextBulkImportCommand { GroupId = BulkImportOperationGroups.First().ReferenceId };
            RollBackBulkImportCommand = new RollBackBulkImportCommand { GroupId = BulkImportOperationGroups.First().ReferenceId };
        }

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<BulkImportOperationGroupProfile>();
            cfg.AddProfile<BulkImportActionResultProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoBulkImportFixture
    {
        get
        {
            var fixture = new Fixture();

            // Configure fixture to handle circular references
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
                .ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            // Command customizations with valid GUIDs
            fixture.Customize<CreateBulkImportCommand>(c => c
                .With(b => b.Id, () => BulkImportOperationGroups.Any() ? BulkImportOperationGroups.First().ReferenceId : Guid.NewGuid().ToString()));

            fixture.Customize<NextBulkImportCommand>(c => c
                .With(b => b.GroupId, () => BulkImportOperationGroups.Any() ? BulkImportOperationGroups.First().ReferenceId : Guid.NewGuid().ToString()));

            fixture.Customize<RollBackBulkImportCommand>(c => c
                .With(b => b.GroupId, () => BulkImportOperationGroups.Any() ? BulkImportOperationGroups.First().ReferenceId : Guid.NewGuid().ToString()));

            // BulkImportOperationGroup customizations
            fixture.Customize<BulkImportOperationGroup>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
                .With(b => b.BulkImportOperationId, () => Guid.NewGuid().ToString())
                .With(b => b.InfraObjectName, () => $"TestInfraObject{fixture.Create<int>()}")
                .With(b => b.CompanyId, () => Guid.NewGuid().ToString())
                .With(b => b.Properties, "{\"ServerList\":[],\"DatabaseList\":[],\"ReplicationList\":[],\"InfraObject\":null}")
                .With(b => b.ProgressStatus, () => $"{fixture.Create<int>() % 10}/10")
                .With(b => b.Status, "Pending")
                .With(b => b.ErrorMessage, "")
                .With(b => b.ConditionalOperation, 1)
                .With(b => b.NodeId, () => $"Node{fixture.Create<int>():000}"));

            // BulkImportActionResult customizations
            fixture.Customize<BulkImportActionResult>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
                .With(b => b.CompanyId, () => Guid.NewGuid().ToString())
                .With(b => b.NodeId, () => $"Node{fixture.Create<int>():000}")
                .With(b => b.NodeName, () => $"TestNode{fixture.Create<int>()}")
                .With(b => b.BulkImportOperationId, () => Guid.NewGuid().ToString())
                .With(b => b.BulkImportOperationGroupId, () => Guid.NewGuid().ToString())
                .With(b => b.EntityId, () => Guid.NewGuid().ToString())
                .With(b => b.EntityName, () => $"TestEntity{fixture.Create<int>()}")
                .With(b => b.EntityType, "Server")
                .With(b => b.Status, "Pending")
                .With(b => b.StartTime, DateTime.Now)
                .With(b => b.EndTime, DateTime.Now.AddHours(1))
                .With(b => b.ErrorMessage, "")
                .With(b => b.ConditionalOperation, 1));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}
