using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ApprovalMatrixUsersFixture : IDisposable
{
    public List<ApprovalMatrixUsers> ApprovalMatrixUsersPaginationList { get; set; }
    public List<ApprovalMatrixUsers> ApprovalMatrixUsersList { get; set; }
    public ApprovalMatrixUsers ApprovalMatrixUsersDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ApprovalMatrixUsersFixture()
    {
        var fixture = new Fixture();

        ApprovalMatrixUsersList = fixture.Create<List<ApprovalMatrixUsers>>();

        ApprovalMatrixUsersPaginationList = fixture.CreateMany<ApprovalMatrixUsers>(20).ToList();

        ApprovalMatrixUsersPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        ApprovalMatrixUsersPaginationList.ForEach(x => x.IsActive = true);

        ApprovalMatrixUsersList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        ApprovalMatrixUsersList.ForEach(x => x.IsActive = true);

        ApprovalMatrixUsersDto = fixture.Create<ApprovalMatrixUsers>();
        ApprovalMatrixUsersDto.ReferenceId = Guid.NewGuid().ToString();
        ApprovalMatrixUsersDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
