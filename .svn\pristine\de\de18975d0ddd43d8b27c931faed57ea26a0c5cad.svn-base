﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class CredentialProfileRepositoryMocks
{
    public static Mock<ICredentialProfileRepository> CreateCredentialProfileRepository(List<CredentialProfile> credentialProfiles)
    {
        var mockCredentialProfileRepository = new Mock<ICredentialProfileRepository>();
        mockCredentialProfileRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(credentialProfiles);
        mockCredentialProfileRepository.Setup(repo => repo.AddAsync(It.IsAny<CredentialProfile>())).ReturnsAsync(
            (CredentialProfile credentialProfile) =>
            {
                credentialProfile.Id = new Fixture().Create<int>();
                credentialProfile.ReferenceId = new Fixture().Create<Guid>().ToString();
                credentialProfiles.Add(credentialProfile);

                return credentialProfile;
            });

        return mockCredentialProfileRepository;
    }

    public static Mock<ICredentialProfileRepository> UpdateCredentialProfileRepository(List<CredentialProfile> credentialProfiles)
    {
        var mockCredentialProfileRepository = new Mock<ICredentialProfileRepository>();

        mockCredentialProfileRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(credentialProfiles);

        mockCredentialProfileRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => credentialProfiles.SingleOrDefault(x => x.ReferenceId == i));

        mockCredentialProfileRepository.Setup(repo => repo.UpdateAsync(It.IsAny<CredentialProfile>())).ReturnsAsync((CredentialProfile credentialProfile) =>
        {
            var index = credentialProfiles.FindIndex(item => item.ReferenceId == credentialProfile.ReferenceId);

            credentialProfiles[index] = credentialProfile;

            return credentialProfile;
        });

        return mockCredentialProfileRepository;
    }

    public static Mock<ICredentialProfileRepository> DeleteCredentialProfileRepository(List<CredentialProfile> credentialProfiles)
    {
        var mockCredentialProfileRepository = new Mock<ICredentialProfileRepository>();

        mockCredentialProfileRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(credentialProfiles);

        mockCredentialProfileRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => credentialProfiles.SingleOrDefault(x => x.ReferenceId == i));

        mockCredentialProfileRepository.Setup(repo => repo.UpdateAsync(It.IsAny<CredentialProfile>())).ReturnsAsync((CredentialProfile credentialProfile) =>
        {
            var index = credentialProfiles.FindIndex(item => item.ReferenceId == credentialProfile.ReferenceId);

            credentialProfile.IsActive = false;
            credentialProfiles[index] = credentialProfile;

            return credentialProfile;
        });

        return mockCredentialProfileRepository;
    }

    public static Mock<ICredentialProfileRepository> GetCredentialProfileRepository(List<CredentialProfile> credentialProfiles)
    {
        var mockCredentialProfileRepository = new Mock<ICredentialProfileRepository>();

        mockCredentialProfileRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(credentialProfiles);

        mockCredentialProfileRepository.Setup(repo => repo.GetType(It.IsAny<string>())).ReturnsAsync(credentialProfiles);

        mockCredentialProfileRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => credentialProfiles.SingleOrDefault(x => x.ReferenceId == i));

        return mockCredentialProfileRepository;
    }

    public static Mock<ICredentialProfileRepository> GetCredentialProfileNamesRepository(List<CredentialProfile> credentialProfiles)
    {
        var mockCredentialProfileRepository = new Mock<ICredentialProfileRepository>();

        mockCredentialProfileRepository.Setup(repo => repo.GetCredentialProfileNames()).ReturnsAsync(credentialProfiles);

        return mockCredentialProfileRepository;
    }

    public static Mock<ICredentialProfileRepository> GetCredentialProfileNameUniqueRepository(List<CredentialProfile> credentialProfiles)
    {
        var mockCredentialProfileRepository = new Mock<ICredentialProfileRepository>();

        mockCredentialProfileRepository.Setup(repo => repo.IsCredentialProfileNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => credentialProfiles.Exists(x => x.Name == i && x.ReferenceId == j));

        return mockCredentialProfileRepository;
    }

    public static Mock<ICredentialProfileRepository> GetCredentialProfileEmptyRepository()
    {
        var mockCredentialProfileRepository = new Mock<ICredentialProfileRepository>();

        mockCredentialProfileRepository.Setup(repo => repo.GetType(It.IsAny<string>())).ReturnsAsync(new List<CredentialProfile>());

        mockCredentialProfileRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<CredentialProfile>());

        return mockCredentialProfileRepository;
    }

    public static Mock<ICredentialProfileRepository> GetPaginatedCredentialProfileRepository(List<CredentialProfile> credentialProfiles)
    {
        var mockCredentialProfileRepository = new Mock<ICredentialProfileRepository>();

        var queryableCredentialProfile = credentialProfiles.BuildMock();

        mockCredentialProfileRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableCredentialProfile);

        return mockCredentialProfileRepository;
    }

    public static Mock<ICredentialProfileRepository> GetCredentialProfileTypeRepository(List<CredentialProfile> credentialProfiles)
    {
        var mockCredentialProfileRepository = new Mock<ICredentialProfileRepository>();

        var queryableCredentialProfile = credentialProfiles.BuildMock();

        mockCredentialProfileRepository.Setup(repo => repo.GetCredentialProfileByType(It.IsAny<string>())).Returns(queryableCredentialProfile);

        return mockCredentialProfileRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateCredentialProfileEventRepository(List<UserActivity> userActivities)
    {
        var mockCredentialProfileEventRepository = new Mock<IUserActivityRepository>();
        mockCredentialProfileEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);
        mockCredentialProfileEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();
                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();
                userActivities.Add(userActivity);

                return userActivity;
            });

        return mockCredentialProfileEventRepository;
    }
}