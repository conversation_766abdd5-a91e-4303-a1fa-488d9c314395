﻿function RpoSummaray(valueData) {

    const timeConverter = (time, mode) => {
        
        if (!time) {
            return mode === 'chart' ? 0 : '00:00';
        }

        let splitTimeWithDay = '';
        let splitTime = '';
        if (time?.includes('.')) {
            splitTimeWithDay = time?.replace('+', '')?.split('.')[0];
            splitTime = time?.split('.')[1];
        } else {
            splitTime = time?.replace('+', '');
        }

        let getTime = splitTime?.split(':');
        if (mode === 'chart') {
            let totalMinutes = ((splitTimeWithDay ? +splitTimeWithDay : 0) * 24 * 60) + (+getTime[0] * 60) + +getTime[1];
            return totalMinutes;
        } else {
            let totalHours = ((splitTimeWithDay ? +splitTimeWithDay : 0) * 24) + +getTime[0] + Math.trunc(+getTime[1] / 60);
            return splitTimeWithDay ? `${totalHours} hours` : `${+getTime[0]} hours ${+getTime[1]} min`;
        }
    };

    
    // Create chart instance
    var chart = am4core.create("RPOSummary", am4charts.RadarChart);
    let configuredRPO;
    let rpoThreshold;
    if (chart.logo) {
        chart.logo.disabled = true;
    }
    // Add data
    if (valueData.hasOwnProperty('id')) {
        let currentRPO = (valueData?.currentRPO !== "NA" && valueData?.currentRPO !== "0" && valueData?.currentRPO !== "" && valueData?.currentRPO !== null && valueData?.currentRPO !== undefined) ? `${valueData?.currentRPO}` : '00:00'
        rpoThreshold = (valueData?.rpoThreshold !== "" && valueData?.rpoThreshold !== null && valueData?.rpoThreshold !== undefined) ? `${valueData?.rpoThreshold}` : 'NA'
        configuredRPO = (valueData?.configuredRPO !== "" && valueData?.configuredRPO !== null && valueData?.configuredRPO !== undefined) ? `${valueData?.configuredRPO}` : 'NA'
      

        chart.data = [{
            "category": "Computed " + timeConverter(valueData?.currentRPO, 'value'),
            "value": timeConverter(valueData?.currentRPO, 'chart'),
            "full": Number(configuredRPO)
        }, {
            "category": "Threshold " + rpoThreshold +" Min",
            "value": rpoThreshold,
            "full": Number(configuredRPO)
        }, {
            "category": "Agreed " + configuredRPO +" Min",
            "value": configuredRPO,
            "full": Number(configuredRPO)
        }];
    }
   

    else {

        $('#chartdata')
            .css('text-align', 'center')
            .html('<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">');

    }
    
 
    let colorvalue = +timeConverter(valueData?.currentRPO, "chart") < +rpoThreshold ? am4core.color("#41c200") : +timeConverter(valueData?.currentRPO, 'chart') > +configuredRPO ? am4core.color("#dc3545") : (+rpoThreshold < +timeConverter(valueData?.currentRPO, 'chart') && +timeConverter(valueData?.currentRPO, "chart") < +configuredRPO) ? am4core.color("#ff9632") : am4core.color("#e0e0e0")

    chart.colors.list = [
        colorvalue,
        am4core.color("#07cedb"),
        am4core.color("#946eff")
    ];

    // Make chart not full circle
    chart.startAngle = -90;
    chart.endAngle = 180;
    chart.innerRadius = am4core.percent(50);
    
    // Set number format
    chart.numberFormatter.numberFormat = "#.#'%'";

    // Create axes
    var categoryAxis = chart.yAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "category";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.grid.template.strokeOpacity = 0;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.fontWeight = 300;
    categoryAxis.renderer.labels.template.fontSize = 9;
    categoryAxis.renderer.labels.template.disabled = true;
    categoryAxis.renderer.labels.template.adapter.add("fill", function (fill, target) {
        return (target.dataItem.index >= 0) ? chart.colors.getIndex(target.dataItem.index) : fill;
    });


    categoryAxis.renderer.minGridDistance = 10;
    categoryAxis.renderer.cellStartLocation = 0.5;
    categoryAxis.renderer.cellEndLocation = 1;

    var valueAxis = chart.xAxes.push(new am4charts.ValueAxis());
    valueAxis.renderer.grid.template.strokeOpacity = 0;
    valueAxis.min = 0;
    valueAxis.max = Number(configuredRPO);
    valueAxis.strictMinMax = true;
    valueAxis.renderer.labels.template.disabled = true;

    // Create series
    var series1 = chart.series.push(new am4charts.RadarColumnSeries());
    series1.dataFields.valueX = "full";
    series1.dataFields.categoryY = "category";
    series1.clustered = false;
    series1.columns.template.fill = new am4core.InterfaceColorSet().getFor("alternativeBackground");
    series1.columns.template.fillOpacity = 0.08;
    series1.columns.template.cornerRadiusTopLeft = 20;
    series1.columns.template.strokeWidth = 0;
    series1.columns.template.radarColumn.cornerRadius = 20;

    var series2 = chart.series.push(new am4charts.RadarColumnSeries());
    series2.dataFields.valueX = "value";
    series2.dataFields.categoryY = "category";
    series2.clustered = false;
    series2.columns.template.strokeWidth = 0;
    series2.columns.template.tooltipText = "{category}";
    series2.columns.template.radarColumn.cornerRadius = 20;

    series2.columns.template.adapter.add("fill", function (fill, target) {
        return chart.colors.getIndex(target.dataItem.index);
    });

    // Change the padding values
    chart.padding(-10, -10, -10, -10)
    // Add a legend
    //chart.legend = new am4charts.Legend();
    //chart.legend.maxWidth = 150;
    //chart.legend.position = "bottom";
    //chart.legend.valueLabels.template.disabled = false;
    //chart.legend.labels.template.text = "[font-size:10px {color}]{name}";

    //chart.legend.itemContainers.template.paddingTop = 0;
    //chart.legend.itemContainers.template.paddingBottom = 5;


    //var markerTemplate = chart.legend.markers.template;
    //markerTemplate.width = 12;
    //markerTemplate.height = 12;

    //series2.events.on("dataitemsvalidated", function () {
    //    var data = [];
    //    series2.dataItems.each(function (dataItem) {
    //        data.push({ name: dataItem?.categoryY, fill: dataItem?.column?.fill, seriesDataItem: dataItem })
    //    })
    //    chart.legend.data = data;
    //    chart.legend.itemContainers.template.events.on("toggled", function (event) {
    //        var seriesDataItem = event.target.dataItem.dataContext.seriesDataItem;
    //        if (event.target.isActive) {
    //            seriesDataItem.hide(series2.interpolationDuration, 0, 0, ["valueX"]);
    //        }
    //        else {
    //            seriesDataItem.show(series2.interpolationDuration, 0, ["valueX"]);
    //        }
    //    })
    //})

    let label = chart.seriesContainer.createChild(am4core.Label);
    label.text = "[bold]RPO[/]";
    label.horizontalCenter = "middle";
    label.verticalCenter = "middle";
    label.fontSize = 12;

    // Add cursor
    //chart.cursor = "none";
    //chart.cursor.behavior = "none";
    //chart.cursor = new am4charts.RadarCursor();


}

