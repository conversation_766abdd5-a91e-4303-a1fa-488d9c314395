using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class DriftEventFilterSpecification : Specification<DriftEvent>
{
    public DriftEventFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.EntityName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("entitytype=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.EntityType.Contains(stringItem.Replace("entitytype=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("entity=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Entity.Contains(stringItem.Replace("entity=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("entityname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.EntityName.Contains(stringItem.Replace("entityname=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("entitystatus=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.EntityStatus.Contains(stringItem.Replace("entitystatus=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.EntityType.Contains(searchString) || p.Entity.Contains(searchString) ||
                    p.EntityName.Contains(searchString) || p.EntityStatus.Contains(searchString);
            }
        }
    }
}