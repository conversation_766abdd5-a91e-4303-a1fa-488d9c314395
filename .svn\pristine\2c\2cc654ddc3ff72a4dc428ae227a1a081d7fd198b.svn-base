﻿using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Create;
using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoSingleSignOnDataAttribute : AutoDataAttribute
{
    public AutoSingleSignOnDataAttribute()
          : base(() =>
           {
               var fixture = new Fixture();

               fixture.Customizations.Add(
                   new StringPropertyTruncateSpecimenBuilder<CreateSingleSignOnCommand>(p => p.ProfileName, 10));

               fixture.Customizations.Add(
                   new StringPropertyTruncateSpecimenBuilder<UpdateSingleSignOnCommand>(p => p.ProfileName, 10));

               return fixture;
           })
    {

    }
}