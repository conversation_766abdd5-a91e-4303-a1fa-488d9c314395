﻿using ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetByInfraObjectAndActionId;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionResult.Queries
{
    public class GetWorkflowActionResultByInfraObjectAndActionIdQueryHandlerTests
    {
        private readonly Mock<IWorkflowActionResultRepository> _mockWorkflowActionResultRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetWorkflowActionResultByInfraObjectAndActionIdQueryHandler _handler;

        public GetWorkflowActionResultByInfraObjectAndActionIdQueryHandlerTests()
        {
            _mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new GetWorkflowActionResultByInfraObjectAndActionIdQueryHandler(
                _mockWorkflowActionResultRepository.Object, _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnWorkflowActionResult_WhenEntityExistsAndIsActive()
        {
            var request = new GetWorkflowActionResultByInfraObjectAndActionIdQuery
            {
                InfraobjectId = Guid.NewGuid().ToString(),
                ActionId = Guid.NewGuid().ToString()
            };

            var workflowActionResult = new Domain.Entities.WorkflowActionResult
            {
                ReferenceId = Guid.NewGuid().ToString(),
                WorkflowActionName = "TestAction",
                IsActive = true
            };

            var expectedViewModel = new WorkflowActionResultByInfraObjectAndActionIdVm
            {
                Id = workflowActionResult.ReferenceId,
                WorkflowActionName = workflowActionResult.WorkflowActionName
            };

            _mockWorkflowActionResultRepository
                .Setup(repo => repo.GetByInfraObjectAndActionId(request.InfraobjectId, request.ActionId))
                .ReturnsAsync(workflowActionResult);

            _mockMapper
                .Setup(mapper => mapper.Map<WorkflowActionResultByInfraObjectAndActionIdVm>(workflowActionResult))
                .Returns(expectedViewModel);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(expectedViewModel.Id, result.Id);
            Assert.Equal(expectedViewModel.WorkflowActionName, result.WorkflowActionName);

            _mockWorkflowActionResultRepository.Verify(repo => repo.GetByInfraObjectAndActionId(request.InfraobjectId, request.ActionId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<WorkflowActionResultByInfraObjectAndActionIdVm>(workflowActionResult), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenEntityDoesNotExist()
        {
            var request = new GetWorkflowActionResultByInfraObjectAndActionIdQuery
            {
                InfraobjectId = Guid.NewGuid().ToString(),
                ActionId = Guid.NewGuid().ToString()
            };

            _mockWorkflowActionResultRepository
                .Setup(repo => repo.GetByInfraObjectAndActionId(request.InfraobjectId, request.ActionId))
                .ReturnsAsync((Domain.Entities.WorkflowActionResult)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(request, CancellationToken.None));

            _mockWorkflowActionResultRepository.Verify(repo => repo.GetByInfraObjectAndActionId(request.InfraobjectId, request.ActionId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<WorkflowActionResultByInfraObjectAndActionIdVm>(It.IsAny<Domain.Entities.WorkflowActionResult>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenEntityIsInactive()
        {
            var request = new GetWorkflowActionResultByInfraObjectAndActionIdQuery
            {
                InfraobjectId = Guid.NewGuid().ToString(),
                ActionId = Guid.NewGuid().ToString()
            };

            var inactiveWorkflowActionResult = new Domain.Entities.WorkflowActionResult
            {
                ReferenceId = Guid.NewGuid().ToString(),
                WorkflowActionName = "TestAction",
                IsActive = false
            };

            _mockWorkflowActionResultRepository
                .Setup(repo => repo.GetByInfraObjectAndActionId(request.InfraobjectId, request.ActionId))
                .ReturnsAsync(inactiveWorkflowActionResult);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(request, CancellationToken.None));

            _mockWorkflowActionResultRepository.Verify(repo => repo.GetByInfraObjectAndActionId(request.InfraobjectId, request.ActionId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<WorkflowActionResultByInfraObjectAndActionIdVm>(It.IsAny<Domain.Entities.WorkflowActionResult>()), Times.Never);
        }
    }
}
