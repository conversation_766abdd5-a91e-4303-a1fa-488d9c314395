<svg width="84" height="83" viewBox="0 0 84 83" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_5033_559)">
<mask id="mask0_5033_559" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="84" height="83">
<path d="M83.25 0H0.25V83H83.25V0Z" fill="white"/>
</mask>
<g mask="url(#mask0_5033_559)">
<g filter="url(#filter0_d_5033_559)">
<path d="M41.8027 71.3789C58.3713 71.3789 71.8027 57.9474 71.8027 41.3789C71.8027 24.8104 58.3713 11.3789 41.8027 11.3789C25.2342 11.3789 11.8027 24.8104 11.8027 41.3789C11.8027 57.9474 25.2342 71.3789 41.8027 71.3789Z" fill="white"/>
<g clip-path="url(#clip1_5033_559)">
<path d="M49.371 51.4372C49.2515 51.4374 49.1356 51.3964 49.0428 51.3211C48.95 51.2459 48.886 51.1409 48.8615 51.024C48.8369 50.9071 48.8534 50.7853 48.9081 50.6791C48.9629 50.5729 49.0525 50.4888 49.1619 50.4409C50.6299 49.7992 51.8737 48.7353 52.7352 47.3845C53.5966 46.0337 54.0366 44.4572 53.9993 42.8555C53.962 41.2539 53.4489 39.6996 52.5255 38.3904C51.6021 37.0812 50.31 36.0764 48.8137 35.5039C48.6861 35.4536 48.5835 35.355 48.5283 35.2295C48.473 35.104 48.4696 34.9617 48.5187 34.8337C48.5679 34.7056 48.6656 34.6022 48.7906 34.5459C48.9157 34.4896 49.0579 34.4849 49.1864 34.533C50.8768 35.1778 52.3369 36.3115 53.3805 37.7894C54.4242 39.2674 55.004 41.0226 55.0462 42.8314C55.0884 44.6402 54.591 46.4206 53.6173 47.9455C52.6437 49.4705 51.238 50.671 49.5795 51.394C49.5137 51.4227 49.4427 51.4374 49.371 51.4372Z" fill="#0091D9"/>
<path d="M29.5935 37.8707C29.5172 37.8707 29.4419 37.8539 29.3728 37.8215C29.3037 37.7891 29.2425 37.7419 29.1937 37.6833C29.1448 37.6247 29.1095 37.556 29.0901 37.4822C29.0707 37.4084 29.0678 37.3312 29.0816 37.2562C29.9596 32.5003 34.1222 29.0488 38.9793 29.0488C40.5233 29.0467 42.047 29.4003 43.4323 30.0822C44.8176 30.7641 46.0272 31.756 46.9672 32.9808C47.0512 33.0902 47.0883 33.2285 47.0704 33.3652C47.0524 33.502 46.9809 33.626 46.8715 33.71C46.7621 33.7939 46.6238 33.831 46.4871 33.8131C46.3503 33.7951 46.2263 33.7236 46.1423 33.6142C45.2994 32.5158 44.2147 31.6264 42.9725 31.015C41.7302 30.4036 40.3638 30.0867 38.9793 30.0888C34.6237 30.0888 30.8913 33.1828 30.1043 37.445C30.0823 37.5645 30.0191 37.6724 29.9258 37.7502C29.8325 37.8279 29.715 37.8706 29.5935 37.8707Z" fill="#00A1B3"/>
<path d="M45.8549 37.1878C45.7886 37.1878 45.7215 37.1891 45.6534 37.1915C45.2331 35.8008 44.3759 34.5824 43.2089 33.717C42.0419 32.8516 40.6272 32.3851 39.1743 32.3867C35.4447 32.3867 32.4104 35.4092 32.4104 39.124C32.4104 39.1464 32.4104 39.1689 32.4108 39.1916C31.4576 39.5035 30.623 40.1002 30.0196 40.9013C29.4163 41.7024 29.0732 42.6694 29.0366 43.6716C29.0001 44.6738 29.2719 45.6632 29.8154 46.5061C30.3588 47.349 31.1477 48.0049 32.0757 48.3853V47.2273C31.3918 46.8503 30.8384 46.2745 30.4889 45.5761C30.1393 44.8777 30.01 44.0896 30.1181 43.3161C30.2261 42.5427 30.5665 41.8202 31.094 41.2444C31.6216 40.6685 32.3116 40.2664 33.0727 40.0912L33.5082 39.9895L33.4757 39.5442C33.4639 39.3832 33.4584 39.2497 33.4584 39.124C33.4584 35.9855 36.0226 33.4322 39.1744 33.4322C40.4674 33.4306 41.7227 33.8677 42.735 34.6722C43.7473 35.4766 44.4567 36.6007 44.7472 37.8606L44.8512 38.3165L45.3169 38.2645C45.4955 38.2442 45.6752 38.2339 45.855 38.2335C48.4805 38.2335 50.6166 40.3605 50.6166 42.9748C50.6189 43.6649 50.4691 44.347 50.1779 44.9726C49.8866 45.5982 49.461 46.152 48.9313 46.5944V47.8838C49.7692 47.3682 50.4607 46.646 50.9393 45.7864C51.4179 44.9269 51.6676 43.9587 51.6646 42.9749C51.6645 39.7838 49.0583 37.1878 45.8549 37.1878Z" fill="#1C4189"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M40.4078 43.8515C38.1313 43.8515 36.2189 45.4153 35.6884 47.528L34.8304 47.3125C35.4573 44.8159 37.716 42.9668 40.4078 42.9668C42.3308 42.9668 44.0327 43.9107 45.0765 45.3595L45.2991 44.5265C45.3621 44.2905 45.6046 44.1503 45.8406 44.2134C46.0766 44.2764 46.2168 44.5189 46.1537 44.7549L45.5476 47.0229L43.3031 46.5099C43.065 46.4554 42.916 46.2183 42.9705 45.9801C43.0249 45.742 43.2621 45.593 43.5002 45.6475L44.33 45.8371C43.444 44.6325 42.0168 43.8515 40.4078 43.8515Z" fill="#64A01A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M40.2902 53.5685C42.5667 53.5685 44.4791 52.0046 45.0096 49.8919L45.8676 50.1074C45.2407 52.604 42.982 54.4531 40.2902 54.4531C38.3672 54.4531 36.6653 53.5092 35.6215 52.0604L35.3989 52.8934C35.3359 53.1294 35.0934 53.2696 34.8574 53.2066C34.6214 53.1435 34.4812 52.901 34.5443 52.665L35.1504 50.3971L37.3949 50.91C37.633 50.9645 37.782 51.2017 37.7275 51.4398C37.6731 51.678 37.4359 51.8269 37.1978 51.7725L36.3679 51.5828C37.254 52.7875 38.6812 53.5685 40.2902 53.5685Z" fill="#64A01A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M40.5109 45.7422H41.5192L40.3182 47.9983H42.15L39.9661 51.7547H38.9051L40.6124 48.8829H38.8493L40.5109 45.7422Z" fill="#1D428A"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_5033_559" x="0.595094" y="0.171327" width="82.4152" height="82.4152" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5.60379"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5033_559"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5033_559" result="shape"/>
</filter>
<clipPath id="clip0_5033_559">
<rect width="84" height="83" fill="white"/>
</clipPath>
<clipPath id="clip1_5033_559">
<rect width="26" height="26" fill="white" transform="translate(29.041 28.75)"/>
</clipPath>
</defs>
</svg>
