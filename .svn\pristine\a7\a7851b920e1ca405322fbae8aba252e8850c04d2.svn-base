using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.AdPasswordExpireModel;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordExpire.Queries;

public class GetAdPasswordExpireListQueryTests : IClassFixture<AdPasswordExpireFixture>
{
    private readonly AdPasswordExpireFixture _adPasswordExpireFixture;
    private readonly Mock<IAdPasswordExpireRepository> _mockAdPasswordExpireRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetAdPasswordExpireListQueryHandler _handler;

    public GetAdPasswordExpireListQueryTests(AdPasswordExpireFixture adPasswordExpireFixture)
    {
        _adPasswordExpireFixture = adPasswordExpireFixture;

        _mockAdPasswordExpireRepository = AdPasswordExpireRepositoryMocks.CreateQueryAdPasswordExpireRepository(_adPasswordExpireFixture.AdPasswordExpires);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<List<AdPasswordExpireListVm>>(It.IsAny<List<Domain.Entities.AdPasswordExpire>>()))
            .Returns((List<Domain.Entities.AdPasswordExpire> entities) => entities.Select(entity => new AdPasswordExpireListVm
            {
                Id = entity.ReferenceId,
                DomainServerId = entity.DomainServerId,
                DomainServer = entity.DomainServer,
                UserName = entity.UserName,
                Email = entity.Email,
                ServerList = entity.ServerList,
                NotificationDays = entity.NotificationDays,
                IsPassword = entity.IsPassword
            }).ToList());

        _handler = new GetAdPasswordExpireListQueryHandler(
            _mockMapper.Object,
            _mockAdPasswordExpireRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_AdPasswordExpireListVm_When_AdPasswordExpiresExist()
    {
        // Arrange
        var query = new GetAdPasswordExpireListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<AdPasswordExpireListVm>));
        result.Count.ShouldBeGreaterThan(0);
        result.First().Id.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_Call_ListAllAsync_OnlyOnce()
    {
        // Arrange
        var query = new GetAdPasswordExpireListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockAdPasswordExpireRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var query = new GetAdPasswordExpireListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<List<AdPasswordExpireListVm>>(It.IsAny<List<Domain.Entities.AdPasswordExpire>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoAdPasswordExpiresExist()
    {
        // Arrange
        var query = new GetAdPasswordExpireListQuery();
        _mockAdPasswordExpireRepository.Setup(x => x.ListAllAsync())
            .ReturnsAsync(new List<Domain.Entities.AdPasswordExpire>());

        _mockMapper.Setup(m => m.Map<List<AdPasswordExpireListVm>>(It.IsAny<List<Domain.Entities.AdPasswordExpire>>()))
            .Returns(new List<AdPasswordExpireListVm>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<AdPasswordExpireListVm>));
        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_MapEntitiesToViewModels_WithCorrectProperties()
    {
        // Arrange
        var testExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        testExpire.DomainServerId = "DS001";
        testExpire.DomainServer = "TestDomain.com";
        testExpire.UserName = "TestUser";
        testExpire.Email = "<EMAIL>";
        testExpire.ServerList = "Server1,Server2";
        testExpire.NotificationDays = "7,14,30";
        testExpire.IsPassword = true;

        var query = new GetAdPasswordExpireListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBeGreaterThan(0);
        
        var firstItem = result.First();
        firstItem.Id.ShouldBe(testExpire.ReferenceId);
        firstItem.DomainServerId.ShouldBe("DS001");
        firstItem.DomainServer.ShouldBe("TestDomain.com");
        firstItem.UserName.ShouldBe("TestUser");
        firstItem.Email.ShouldBe("<EMAIL>");
        firstItem.ServerList.ShouldBe("Server1,Server2");
        firstItem.NotificationDays.ShouldBe("7,14,30");
        firstItem.IsPassword.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_ReturnCorrectListType_When_MappingSuccessful()
    {
        // Arrange
        var query = new GetAdPasswordExpireListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<List<AdPasswordExpireListVm>>();
        result.GetType().ShouldBe(typeof(List<AdPasswordExpireListVm>));
    }

    [Fact]
    public async Task Handle_ReturnAllActiveItems_When_RepositoryHasData()
    {
        // Arrange
        var query = new GetAdPasswordExpireListQuery();
        var expectedCount = _adPasswordExpireFixture.AdPasswordExpires.Count;

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(expectedCount);
    }

    [Fact]
    public async Task Handle_NotCallMapper_When_NoDataExists()
    {
        // Arrange
        var query = new GetAdPasswordExpireListQuery();
        _mockAdPasswordExpireRepository.Setup(x => x.ListAllAsync())
            .ReturnsAsync(new List<Domain.Entities.AdPasswordExpire>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(0);
        _mockMapper.Verify(x => x.Map<List<AdPasswordExpireListVm>>(It.IsAny<List<Domain.Entities.AdPasswordExpire>>()), Times.Never);
    }
}
