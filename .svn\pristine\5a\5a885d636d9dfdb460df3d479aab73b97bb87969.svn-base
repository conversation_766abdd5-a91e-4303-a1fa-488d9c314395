﻿using ContinuityPatrol.Application.Features.Server.Events.LicenseInfoEvents.Update;
using ContinuityPatrol.Application.Features.Server.Events.Update;
using ContinuityPatrol.Application.Helper;

namespace ContinuityPatrol.Application.Features.Server.Commands.Update;

public class UpdateServerCommandHandler : IRequestHandler<UpdateServerCommand, UpdateServerResponse>
{
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IServerRepository _serverRepository;
    private readonly ISiteRepository _siteRepository;
    private readonly ISiteTypeRepository _siteTypeRepository;
    private readonly ILicenseInfoRepository _licenseInfoRepository;

    public UpdateServerCommandHandler(IMapper mapper, IServerRepository serverRepository, IPublisher publisher,
        ILicenseManagerRepository licenseManagerRepository, ISiteRepository siteRepository,
        ISiteTypeRepository siteTypeRepository, ILicenseInfoRepository licenseInfoRepository)
    {
        _mapper = mapper;
        _serverRepository = serverRepository;
        _publisher = publisher;
        _licenseManagerRepository = licenseManagerRepository;
        _siteRepository = siteRepository;
        _siteTypeRepository = siteTypeRepository;
        _licenseInfoRepository = licenseInfoRepository;
    }

    public async Task<UpdateServerResponse> Handle(UpdateServerCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _serverRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Server), request.Id);

        var licenseDtl = await _licenseManagerRepository.GetLicenseDetailByIdAsync(request.LicenseId);

        request.LicenseKey = SecurityHelper.Encrypt(request.LicenseKey);

        if (request.RoleType.Trim().ToLower().Equals("database"))
        {
            if (eventToUpdate.RoleType.Equals(request.RoleType))
            {
                request.IsAttached = eventToUpdate.IsAttached;
            }
            else
            {
                request.IsAttached = false;
            }
        }
        else
        {
            request.IsAttached = true;
        }


        _mapper.Map(request, eventToUpdate, typeof(UpdateServerCommand), typeof(Domain.Entities.Server));

        var ipAddress = GetJsonProperties.GetIpAddressFromProperties(request.Properties);

        var hostName = GetJsonProperties.GetHostNameFromProperties(request.Properties);

        


        //eventToUpdate.IsAttached = eventToUpdate.IsAttached || !request.RoleType.Trim().ToLower().Equals("database");
       // eventToUpdate.IsAttached = eventToUpdate.RoleType.Equals(request.RoleType) && (request.RoleType.Trim().ToLower().Equals("database"));

        await _serverRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateServerResponse
        {
            Message = Message.Update(nameof(Domain.Entities.Server), eventToUpdate.Name),

            ServerId = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new ServerUpdatedEvent { ServerName = eventToUpdate.Name }, cancellationToken);

        if (!request.RoleType.Trim().ToLower().Equals("database"))
        {
            var site = await _siteRepository.GetByReferenceIdAsync(eventToUpdate.SiteId);

            var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site.TypeId);

            //if (siteType.Category.ToLower().Contains("primary"))
            //{
            await _publisher.Publish(new ServerLicenseInfoUpdatedEvent
            {
                //Id = eventToUpdate.ReferenceId,
                EntityName = eventToUpdate.Name,
                LicenseId = licenseDtl.ReferenceId,
                PONumber = licenseDtl.PoNumber,
                EntityId = eventToUpdate.ReferenceId,
                EntityType = eventToUpdate.RoleType,
                Type = eventToUpdate.OSType,
                IpAddress = $"{ipAddress},{hostName}",
                BusinessServiceId = eventToUpdate.BusinessServiceId,
                BusinessServiceName = eventToUpdate.BusinessServiceName,
                Category = eventToUpdate.ServerType,
                Logo = request.Logo,
                SiteType = siteType.Category
            }, cancellationToken);
            // }
        }
        else
        {
            var licenseInfo = await _licenseInfoRepository.GetLicenseInfoByEntityId(eventToUpdate.ReferenceId);

            if(licenseInfo is not null)
                await _licenseInfoRepository.DeleteAsync(licenseInfo);
        }

        return response;
    }
}