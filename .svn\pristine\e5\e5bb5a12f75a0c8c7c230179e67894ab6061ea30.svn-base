﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;

namespace ContinuityPatrol.Persistence.Repositories;

public class DashboardViewLogRepository : BaseRepository<DashboardViewLog>, IDashboardViewLogRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public DashboardViewLogRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<DashboardViewLog>> ListAllAsync()
    {
        var businessService = _loggedInUserService.IsParent
            ? base.FilterBy(bs => bs.LastModifiedDate.Date == DateTime.Today.AddDays(-1))
            : base.FilterBy(bs =>
                bs.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                bs.LastModifiedDate.Date == DateTime.Today.AddDays(-1));

        var businessServiceDto = MapDashBoardViewLog(businessService);

        return _loggedInUserService.IsAllInfra
            ? await businessServiceDto.ToListAsync()
            : AssignedBusinessServices(businessServiceDto);
    }

    public async Task<List<DashboardViewLog>> GetDashboardViewLogByLast30daysList()
    {
        var thirtyDaysAgo = DateTime.Today.AddDays(-30);

        var dashboardViewLogs = _loggedInUserService.IsParent
            ? base.FilterBy(bs => bs.LastModifiedDate.Date >= thirtyDaysAgo)
            : base.FilterBy(bs =>
                bs.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                bs.LastModifiedDate.Date >= thirtyDaysAgo);

        var dashboardViewLogsList = MapDashBoardViewLog(dashboardViewLogs);

        return _loggedInUserService.IsAllInfra
            ? await dashboardViewLogsList.ToListAsync()
            : AssignedBusinessServices(dashboardViewLogsList).ToList();
    }



    public override Task<DashboardViewLog> GetByReferenceIdAsync(string id)
    {
        var businessService = base.GetByReferenceId(id,
            dataLag => dataLag.ReferenceId.Equals(id) && dataLag.CompanyId.Equals(_loggedInUserService.CompanyId));

        var businessServiceDto = MapDashBoardViewLog(businessService);

        return Task.FromResult(businessServiceDto.SingleOrDefault());
    }


    public async Task<List<DashboardViewLog>> GetDashboardViewLogByInfraObjectId(string infraObjectId)
    {
        //var oneDay = DateTime.Today;

        //var today = DateTime.Now.Hour;
        //var startOfDay = today;
        //var endOfDay = today.AddDays(1).AddTicks(-1);

        var infraObjects = _dbContext.DashboardViewLogs.AsNoTracking()
            .Where(x => x.InfraObjectId == infraObjectId && x.LastModifiedDate.Hour == 1);
            

        //var infraObejcts = _dbContext.DashboardViewLogs.AsNoTracking()
        //    .Active().Where(x=>x.InfraObjectId.Equals(infraObjectId) && x.LastModifiedDate)


       // var infraObjects = base.ListAllAsync(x => x.InfraObjectId.Equals(infraObjectId) && x.LastModifiedDate.Date >= oneDay);

        //var infraObjectDto = MapDashBoardViewLog(infraObjects);

        return await infraObjects.ToListAsync();
    }

    public async Task<IReadOnlyList<DashboardViewLog>> GetDataLagByOneDayReport(string infraObjectId)
    {
        // Guard.Against.InvalidGuidOrEmpty(infraObjectId, "InfraObjectId", "InfraObjectId cannot be invalid");

        var infraObjects = _loggedInUserService.IsParent
            ? base.FilterBy(infra =>
                infra.InfraObjectId.Equals(infraObjectId) && infra.LastModifiedDate.Date == DateTime.Today.AddDays(-1))
            : base.FilterBy(infra =>
                infra.CompanyId.Equals(_loggedInUserService.CompanyId) && infra.InfraObjectId.Equals(infraObjectId) &&
                infra.LastModifiedDate.Date == DateTime.Today.AddDays(-1));

        var infraObjectDto = MapDashBoardViewLog(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraObjectDto.ToListAsync()
            : AssignedInfraObjects(infraObjectDto);
    }


    //Filters
    public IReadOnlyList<DashboardViewLog> AssignedBusinessServices(IQueryable<DashboardViewLog> businessServices)
    {
        var services = new List<DashboardViewLog>();

        foreach (var businessService in businessServices)
            if (AssignedEntity.AssignedBusinessServices.Count > 0)
                services.AddRange(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
                                  where businessService?.BusinessServiceId == assignedBusinessService.Id
                                  select businessService);
        return services;
    }

    public IReadOnlyList<DashboardViewLog> AssignedBusinessFunctions(IQueryable<DashboardViewLog> businessFunctions)
    {
        var functions = new List<DashboardViewLog>();

        var assignedBusinessFunctions = new List<AssignedBusinessFunctions>();

        if (AssignedEntity.AssignedBusinessServices.Count > 0)
            foreach (var assignedBusinessServices in AssignedEntity.AssignedBusinessServices)
                assignedBusinessFunctions.AddRange(assignedBusinessServices.AssignedBusinessFunctions);

        foreach (var businessFunction in businessFunctions)
            if (assignedBusinessFunctions.Count > 0)
                functions.AddRange(from assignedBusinessFunction in assignedBusinessFunctions
                                   where businessFunction?.BusinessFunctionId == assignedBusinessFunction.Id
                                   select businessFunction);
        return functions;
    }

    public IQueryable<DashboardViewLog> GetByInfraObjectIdAsync(string id,
        Expression<Func<DashboardViewLog, bool>> expression = null)
    {
        return _loggedInUserService.IsParent
            ? Entities.Where(x => x.InfraObjectId.Equals(id))
            : FilterBy(expression);
    }

    public DashboardViewLog GetByInfraObjectId(DashboardViewLog infraObject)
    {
        var services = AssignedEntity.AssignedBusinessServices
            .SelectMany(assignedBusinessService => assignedBusinessService.AssignedBusinessFunctions)
            .SelectMany(assignedBusinessFunction => assignedBusinessFunction.AssignedInfraObjects)
            .Where(assignedInfraObjects => infraObject?.InfraObjectId == assignedInfraObjects.Id)
            .Select(_ => infraObject).SingleOrDefault();

        return services;
    }

    public IReadOnlyList<DashboardViewLog> AssignedInfraObjects(IQueryable<DashboardViewLog> infraObjects)
    {
        var infraObjectList = new List<DashboardViewLog>();

        var assignedBusinessFunctions = new List<AssignedBusinessFunctions>();

        if (AssignedEntity.AssignedBusinessServices.Count > 0)
            foreach (var assignedBusinessServices in AssignedEntity.AssignedBusinessServices)
                assignedBusinessFunctions.AddRange(assignedBusinessServices.AssignedBusinessFunctions);

        var assignedBusinessInfraObjects = new List<AssignedInfraObjects>();

        if (assignedBusinessFunctions.Count > 0)
            foreach (var assignedBusinessFunction in assignedBusinessFunctions)
                assignedBusinessInfraObjects.AddRange(assignedBusinessFunction.AssignedInfraObjects);

        foreach (var infraObject in infraObjects)
            if (assignedBusinessInfraObjects.Count > 0)
                infraObjectList.AddRange(from assignedInfraObject in assignedBusinessInfraObjects
                                         where infraObject?.InfraObjectId == assignedInfraObject.Id
                                         select infraObject);
        return infraObjectList;
    }

    private IQueryable<DashboardViewLog> MapDashBoardViewLog(IQueryable<DashboardViewLog> dashboardViewLog)
    {
        var mapDashBoardViewLog = dashboardViewLog.Select(data => new
        {
            DashboardViewLog = data,
            BusinessService = _dbContext.BusinessServices.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.BusinessServiceId)),
            BusinessFunction = _dbContext.BusinessFunctions.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.BusinessFunctionId)),
            InfraObject = _dbContext.InfraObjects.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.InfraObjectId)),
        });

        var mapDashBoardViewLogQuery = mapDashBoardViewLog.Select(result => new DashboardViewLog
        {
            #region old 
            //BusinessServiceId = result!.BusinessService.ReferenceId,
            //BusinessServiceName = result!.BusinessService.Name,
            //BusinessFunctionId = result.BusinessFunction.ReferenceId ?? string.Empty,
            //BusinessFunctionName = result.BusinessFunction.Name ?? string.Empty,
            //InfraObjectId = result.InfraObject.ReferenceId ?? string.Empty,
            //InfraObjectName = result.InfraObject.Name ?? string.Empty,
            //Priority = result.BusinessService.Priority,
            //Description = result.BusinessService.Description ?? string.Empty,
            //ConfiguredRPO = result.BusinessFunction.ConfiguredRPO ?? string.Empty,
            //ConfiguredRTO = result.BusinessFunction.ConfiguredRTO ?? string.Empty,
            //RPOThreshold = result.BusinessFunction.RPOThreshold ?? string.Empty,
            //SiteProperties = result.BusinessService.SiteProperties,
            #endregion
            Id = result.DashboardViewLog.Id,
            ReferenceId = result!.DashboardViewLog.ReferenceId,
            BusinessServiceId = result.BusinessService != null ? result.BusinessService.ReferenceId : string.Empty,
            BusinessServiceName = result.BusinessService != null ? result.BusinessService.Name : string.Empty,
            BusinessFunctionId = result.BusinessFunction != null ? result.BusinessFunction.ReferenceId : string.Empty,
            BusinessFunctionName = result.BusinessFunction != null ? result.BusinessFunction.Name : string.Empty,
            InfraObjectId = result.InfraObject != null ? result.InfraObject.ReferenceId : string.Empty,
            InfraObjectName = result.InfraObject != null ? result.InfraObject.Name : string.Empty,
            Priority = result.BusinessService != null ? result.BusinessService.Priority : 0,
            CompanyId = result!.DashboardViewLog.CompanyId,
            Description = result.BusinessService != null ? result.BusinessService.Description : string.Empty,
            EntityId = result.DashboardViewLog.EntityId ?? string.Empty,
            MonitorType = result.DashboardViewLog.MonitorType ?? string.Empty,
            Type = result.DashboardViewLog.Type,
            DataLagValue = result.DashboardViewLog.DataLagValue ?? string.Empty,
            Status = result.DashboardViewLog.Status ?? string.Empty,
            Properties = result.DashboardViewLog.Properties,
            ReplicationStatus = result.DashboardViewLog.ReplicationStatus,
            DROperationStatus = result.DashboardViewLog.DROperationStatus,
            ConfiguredRPO = result.BusinessFunction != null ? result.BusinessFunction.ConfiguredRPO : string.Empty,
            ConfiguredRTO = result.BusinessFunction != null ? result.BusinessFunction.ConfiguredRTO : string.Empty,
            RPOThreshold = result.BusinessFunction != null ? result.BusinessFunction.RPOThreshold : string.Empty,
            SiteProperties = result.BusinessService != null ? result.BusinessService.SiteProperties : string.Empty,
            CurrentRPO = result.DashboardViewLog.CurrentRPO ?? string.Empty,
            CurrentRTO = result.DashboardViewLog.CurrentRTO ?? string.Empty,
            State = result.DashboardViewLog.State ?? string.Empty,
            ErrorMessage = result.DashboardViewLog.ErrorMessage,
            IsActive = result.DashboardViewLog.IsActive,
            CreatedBy = result.DashboardViewLog.CreatedBy,
            CreatedDate = result.DashboardViewLog.CreatedDate,
            LastModifiedBy = result.DashboardViewLog.LastModifiedBy,
            LastModifiedDate = result.DashboardViewLog.LastModifiedDate
        });

        return mapDashBoardViewLogQuery;
    }
}