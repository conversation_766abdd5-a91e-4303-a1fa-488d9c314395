﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberJobManagement.Events.Paginated;

public class CyberJobManagementLogsPaginatedEventHandler :INotificationHandler<CyberJobManagementLogsPaginatedEvent>
{
    private readonly ILogger<CyberJobManagementLogsPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberJobManagementLogsPaginatedEventHandler(ILogger<CyberJobManagementLogsPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }
    public async Task Handle(CyberJobManagementLogsPaginatedEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.View} JobExecutionHistory",
            Entity = Modules.CyberJobManagement.ToString(),
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Job Execution History viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Job Execution History viewed");
    }
}
