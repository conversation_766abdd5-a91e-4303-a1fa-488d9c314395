using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ServerTypeFixture : IDisposable
{
    public List<ServerType> ServerTypePaginationList { get; set; }
    public List<ServerType> ServerTypeList { get; set; }
    public ServerType ServerTypeDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ServerTypeFixture()
    {
        var fixture = new Fixture();

        ServerTypeList = fixture.Create<List<ServerType>>();

        ServerTypePaginationList = fixture.CreateMany<ServerType>(20).ToList();

        ServerTypeDto = fixture.Create<ServerType>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public ServerType CreateServerType(
        string name = "Default Server Type",
        string logo = "default-logo.png",
        bool isActive = true,
        bool isDelete = false)
    {
        return new ServerType
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = name,
            Logo = logo,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
        };
    }

    public List<ServerType> CreateMultipleServerTypes(int count)
    {
        var serverTypes = new List<ServerType>();
        for (int i = 1; i <= count; i++)
        {
            serverTypes.Add(CreateServerType(
                name: $"Server Type {i}",
                logo: $"logo{i}.png"
            ));
        }
        return serverTypes;
    }

    public ServerType CreateServerTypeWithSpecificId(string referenceId, string name = "Test Server Type")
    {
        return new ServerType
        {
            ReferenceId = referenceId,
            Name = name,
            Logo = "test-logo.png",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
