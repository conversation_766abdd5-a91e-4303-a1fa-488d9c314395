using ContinuityPatrol.Web.Middlewares;

namespace ContinuityPatrol.Web.UnitTests.Middlewares;

public class RemoveReturnUrlMiddlewareTests
{
    private readonly Mock<RequestDelegate> _nextMock;
    private readonly RemoveReturnUrlMiddleware _middleware;
    private readonly DefaultHttpContext _context;

    public RemoveReturnUrlMiddlewareTests()
    {
        _nextMock = new Mock<RequestDelegate>();
        _middleware = new RemoveReturnUrlMiddleware(_nextMock.Object);
        _context = new DefaultHttpContext();
        _context.Response.Body = new MemoryStream();
    }

    [Fact]
    public async Task Invoke_WithoutReturnUrlParameter_ShouldCallNext()
    {
        // Arrange
        _context.Request.Path = "/dashboard";
        _context.Request.QueryString = new QueryString("?param1=value1&param2=value2");

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _nextMock.Verify(next => next(_context), Times.Once);
    }

    [Fact]
    public async Task Invoke_WithReturnUrlParameter_ShouldRemoveQueryStringAndRedirect()
    {
        // Arrange
        _context.Request.Path = "/dashboard";
        _context.Request.QueryString = new QueryString("?returnUrl=/previous/page");

        // Act
        await _middleware.Invoke(_context);

        // Assert
        Assert.Equal(302, _context.Response.StatusCode); // Redirect status code
        Assert.Contains("/dashboard", _context.Response.Headers["Location"].ToString());
        Assert.Equal(QueryString.Empty, _context.Request.QueryString);
        _nextMock.Verify(next => next(_context), Times.Never);
    }

    [Fact]
    public async Task Invoke_WithReturnUrlAndOtherParameters_ShouldRemoveAllQueryString()
    {
        // Arrange
        _context.Request.Path = "/dashboard";
        _context.Request.QueryString = new QueryString("?returnUrl=/previous/page&param1=value1&param2=value2");

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _context.Response.StatusCode.Should().Be(302);
        _context.Request.QueryString.Should().Be(QueryString.Empty);
        _nextMock.Verify(next => next(_context), Times.Never);
    }

    [Fact]
    public async Task Invoke_WithAccountLoginPathAndReturnUrl_ShouldRemoveAccountLoginFromPath()
    {
        // Arrange
        _context.Request.Path = "/Account/Login";
        _context.Request.QueryString = new QueryString("?returnUrl=/dashboard");

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _context.Response.StatusCode.Should().Be(302);
        _context.Response.Headers["Location"].Should().Contain("/");
        _context.Response.Headers["Location"].Should().NotContain("Account/Login");
    }

    [Fact]
    public async Task Invoke_WithNestedAccountLoginPathAndReturnUrl_ShouldRemoveAccountLoginFromPath()
    {
        // Arrange
        _context.Request.Path = "/admin/Account/Login";
        _context.Request.QueryString = new QueryString("?returnUrl=/dashboard");

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _context.Response.StatusCode.Should().Be(302);
        _context.Response.Headers["Location"].Should().Contain("/admin/");
        _context.Response.Headers["Location"].Should().NotContain("Account/Login");
    }

    [Fact]
    public async Task Invoke_WithRootPathAndReturnUrl_ShouldRedirectToRoot()
    {
        // Arrange
        _context.Request.Path = "/";
        _context.Request.QueryString = new QueryString("?returnUrl=/dashboard");

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _context.Response.StatusCode.Should().Be(302);
        _context.Response.Headers["Location"].Should().Contain("/");
    }

    [Theory]
    [InlineData("?returnUrl=/dashboard")]
    [InlineData("?returnUrl=/admin/users")]
    [InlineData("?returnUrl=/reports/summary")]
    public async Task Invoke_WithDifferentReturnUrls_ShouldAlwaysRedirect(string queryString)
    {
        // Arrange
        _context.Request.Path = "/test";
        _context.Request.QueryString = new QueryString(queryString);

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _context.Response.StatusCode.Should().Be(302);
        _nextMock.Verify(next => next(_context), Times.Never);
    }

    [Fact]
    public async Task Invoke_WithEmptyReturnUrl_ShouldStillRedirect()
    {
        // Arrange
        _context.Request.Path = "/test";
        _context.Request.QueryString = new QueryString("?returnUrl=");

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _context.Response.StatusCode.Should().Be(302);
        _nextMock.Verify(next => next(_context), Times.Never);
    }

    //[Fact]
    //public async Task Invoke_WithReturnUrlCaseInsensitive_ShouldRedirect()
    //{
    //    // Arrange
    //    _context.Request.Path = "/test";
    //    _context.Request.QueryString = new QueryString("?ReturnUrl=/dashboard");

    //    // Act
    //    await _middleware.Invoke(_context);

    //    // Assert
    //    // Note: This test depends on how QueryString.ContainsKey works with case sensitivity
    //    // The actual behavior may vary based on the implementation
    //    _nextMock.Verify(next => next(_context), Times.Once); // Assuming case-sensitive
    //}

    [Theory]
    [InlineData("/")]
    [InlineData("/dashboard")]
    [InlineData("/admin/users")]
    [InlineData("/reports/summary")]
    public async Task Invoke_WithDifferentPathsAndReturnUrl_ShouldRedirectToSamePath(string path)
    {
        // Arrange
        _context.Request.Path = path;
        _context.Request.QueryString = new QueryString("?returnUrl=/somewhere");

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _context.Response.StatusCode.Should().Be(302);
        _context.Response.Headers["Location"].Should().Contain(path);
    }

    [Fact]
    public async Task Invoke_WithComplexQueryStringIncludingReturnUrl_ShouldRemoveAllParameters()
    {
        // Arrange
        _context.Request.Path = "/search";
        _context.Request.QueryString = new QueryString("?query=test&page=1&returnUrl=/dashboard&sort=name");

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _context.Response.StatusCode.Should().Be(302);
        _context.Request.QueryString.Should().Be(QueryString.Empty);
        _context.Response.Headers["Location"].Should().Contain("/search");
    }

    //[Fact]
    //public async Task Invoke_WithAccountLoginInMiddleOfPath_ShouldReplaceCorrectly()
    //{
    //    // Arrange
    //    _context.Request.Path = "/api/Account/Login/verify";
    //    _context.Request.QueryString = new QueryString("?returnUrl=/dashboard");

    //    // Act
    //    await _middleware.Invoke(_context);

    //    // Assert
    //    _context.Response.StatusCode.Should().Be(302);
    //    _context.Response.Headers["Location"].Should().Contain("/api/");
    //    _context.Response.Headers["Location"].Should().Contain("/verify");
    //    _context.Response.Headers["Location"].Should().NotContain("Account/Login");
    //}

    [Fact]
    public async Task Invoke_WithEmptyPath_ShouldHandleGracefully()
    {
        // Arrange
        _context.Request.Path = "";
        _context.Request.QueryString = new QueryString("?returnUrl=/dashboard");

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _context.Response.StatusCode.Should().Be(302);
        _context.Request.QueryString.Should().Be(QueryString.Empty);
    }

    [Theory]
    [InlineData("?param1=value1")]
    [InlineData("?search=test&page=1")]
    [InlineData("?id=123&action=edit")]
    public async Task Invoke_WithoutReturnUrlButOtherParams_ShouldCallNext(string queryString)
    {
        // Arrange
        _context.Request.Path = "/test";
        _context.Request.QueryString = new QueryString(queryString);

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _nextMock.Verify(next => next(_context), Times.Once);
        _context.Request.QueryString.Should().Be(new QueryString(queryString));
    }
}

public class RemoveReturnUrlMiddlewareExtensionTests
{
    [Fact]
    public void UseRemoveReturnUrlMiddleware_ShouldRegisterMiddleware()
    {
        // Arrange
        var app = new Mock<IApplicationBuilder>();
        app.Setup(x => x.Use(It.IsAny<Func<RequestDelegate, RequestDelegate>>()))
            .Returns(app.Object);

        // Act
        var result = app.Object.UseRemoveReturnUrlMiddleware();

        // Assert
        result.Should().Be(app.Object);
        app.Verify(x => x.Use(It.IsAny<Func<RequestDelegate, RequestDelegate>>()), Times.Once);
    }

}
