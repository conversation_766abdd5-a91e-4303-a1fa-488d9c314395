﻿using AutoFixture;
using ContinuityPatrol.Application.Features.Archive.Commands.Create;
using ContinuityPatrol.Application.Features.Archive.Commands.Update;
using ContinuityPatrol.Application.Features.Archive.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Archive.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ArchiveModel;
using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;
using FluentValidation.Results;
using Microsoft.Extensions.Primitives;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class ArchiveControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<ILogger<ArchiveController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private  ArchiveController _controller;

        public ArchiveControllerShould()
        {
            Initialize();
        }
        public void Initialize()
        {
            _controller = new ArchiveController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }
        [Fact]
        public async Task List_PublishesEvent_AndReturnsView()
        {
            var result = await _controller.List();
            Assert.IsType<ViewResult>(result);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonResult_WithPaginatedData()
        {
         
            var query = new GetTableAccessPaginatedListQuery();
            _mockDataProvider.Setup(dp => dp.TableAccess.GetTableAccessPaginatedList(query))
                            .ReturnsAsync(new PaginatedResult<TableAccessListVm>());

            var result = await _controller.GetPagination(query);
          
            var jsonResult = Assert.IsType<JsonResult>(result);

            Assert.NotNull(jsonResult.Value);
          
        }

        [Fact]
        public async Task GetPaginated_ReturnsJsonResult_WithPaginatedArchives()
        {          
            var query = new GetArchivePaginatedListQuery();
            var page = new PaginatedResult<ArchiveListVm>();
            _mockDataProvider.Setup(dp => dp.Archive.GetPaginatedArchives(query))
             .ReturnsAsync(page);

            var result = await _controller.GetPaginated(query);
         
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
           
        }
      

        [Fact]
        public async Task CreateOrUpdate_CreatesOrUpdatesArchive_AndRedirectsToList()
        {
           
            var createCommand = new AutoFixture.Fixture().Create<CreateArchiveCommand>();
            var updateCommand = new AutoFixture.Fixture().Create<UpdateArchiveCommand>();
            var response = new BaseResponse { Success = true, Message = "Success" };

            _mockDataProvider.Setup(dp => dp.Archive.CreateAsync(It.IsAny<CreateArchiveCommand>()))
                             .ReturnsAsync(response);
            _mockDataProvider.Setup(dp => dp.Archive.UpdateAsync(It.IsAny<UpdateArchiveCommand>()))
                             .ReturnsAsync(response);
            _mockMapper.Setup(dp => dp.Map<CreateArchiveCommand>(createCommand)).Returns(createCommand);
            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            };
            _controller.ControllerContext.HttpContext.Request.Form = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
            {
                { "id", "" } 
            });

          
            var result = await _controller.CreateOrUpdate(createCommand, null);

            
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);

            _mockDataProvider.Verify(dp => dp.Archive.CreateAsync(It.IsAny<CreateArchiveCommand>()), Times.Once);

        }

        [Fact]
        public async Task Delete_DeletesArchive_AndRedirectsToList()
        {
           
            var response = new BaseResponse { Success = true, Message = "Deleted Successfully" };
            _mockDataProvider.Setup(dp => dp.Archive.DeleteAsync(It.IsAny<string>()))
                             .ReturnsAsync(response);
   
            var result = await _controller.Delete("some-id");
       
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockDataProvider.Verify(dp => dp.Archive.DeleteAsync(It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task IsArchiveNameExist_ReturnsTrue_IfNameExists()
        {
         
            _mockDataProvider.Setup(dp => dp.Archive.IsArchiveNameExist("name", "id"))
                             .ReturnsAsync(true);

            
            var result = await _controller.IsArchiveNameExist("name", "id");

            
            Assert.True(result);
            _mockDataProvider.Verify(dp => dp.Archive.IsArchiveNameExist("name", "id"), Times.Once);
        }

        [Fact]
        public async Task IsTableNameExist_ReturnsTrue_IfNameExists()
        {

            _mockDataProvider.Setup(dp => dp.Archive.IsTableNameExist("tableName", "id"))
                             .ReturnsAsync(true);


            var result = await _controller.IsTableNameExist("tableName", "id");


            Assert.True(result);
            _mockDataProvider.Verify(dp => dp.Archive.IsTableNameExist("tableName", "id"), Times.Once);
        }

        [Fact]
        public async Task List_ThrowsException_ReturnsView()
        {
            // Arrange
            _mockPublisher.Setup(p => p.Publish(It.IsAny<ArchivePaginatedEvent>(), It.IsAny<CancellationToken>()))
                         .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.List();

            // Assert
            Assert.IsType<ViewResult>(result);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetPagination_ThrowsException_ReturnsJsonException()
        {
            // Arrange
            var query = new GetTableAccessPaginatedListQuery();
            _mockDataProvider.Setup(dp => dp.TableAccess.GetTableAccessPaginatedList(query))
                            .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            Assert.IsType<JsonResult>(result);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetPaginated_ThrowsException_ReturnsJsonException()
        {
            // Arrange
            var query = new GetArchivePaginatedListQuery();
            _mockDataProvider.Setup(dp => dp.Archive.GetPaginatedArchives(query))
                            .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPaginated(query);

            // Assert
            Assert.IsType<JsonResult>(result);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatePath_RedirectsToList()
        {
            // Arrange
            var createCommand = new AutoFixture.Fixture().Create<CreateArchiveCommand>();
            var updateCommand = new AutoFixture.Fixture().Create<UpdateArchiveCommand>();
            var response = new BaseResponse { Success = true, Message = "Updated Successfully" };

            _mockDataProvider.Setup(dp => dp.Archive.UpdateAsync(It.IsAny<UpdateArchiveCommand>()))
                             .ReturnsAsync(response);
            _mockMapper.Setup(dp => dp.Map<UpdateArchiveCommand>(updateCommand)).Returns(updateCommand);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            };
            _controller.ControllerContext.HttpContext.Request.Form = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "some-id" } // Non-empty id triggers update path
            });

            // Act
            var result = await _controller.CreateOrUpdate(createCommand, updateCommand);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockDataProvider.Verify(dp => dp.Archive.UpdateAsync(It.IsAny<UpdateArchiveCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_ValidationException_RedirectsToListWithWarning()
        {
            // Arrange
            var createCommand = new AutoFixture.Fixture().Create<CreateArchiveCommand>();
            var updateCommand = new AutoFixture.Fixture().Create<UpdateArchiveCommand>();
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Property", "Validation error"));
            var validationException = new ValidationException(validationResult);

            _mockDataProvider.Setup(dp => dp.Archive.CreateAsync(It.IsAny<CreateArchiveCommand>()))
                             .ThrowsAsync(validationException);
            _mockMapper.Setup(dp => dp.Map<CreateArchiveCommand>(createCommand)).Returns(createCommand);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            };
            _controller.ControllerContext.HttpContext.Request.Form = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "" } // Empty id triggers create path
            });

            // Act
            var result = await _controller.CreateOrUpdate(createCommand, updateCommand);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_GeneralException_RedirectsToListWithWarning()
        {
            // Arrange
            var createCommand = new AutoFixture.Fixture().Create<CreateArchiveCommand>();
            var updateCommand = new AutoFixture.Fixture().Create<UpdateArchiveCommand>();

            _mockDataProvider.Setup(dp => dp.Archive.CreateAsync(It.IsAny<CreateArchiveCommand>()))
                             .ThrowsAsync(new Exception("General exception"));
            _mockMapper.Setup(dp => dp.Map<CreateArchiveCommand>(createCommand)).Returns(createCommand);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            };
            _controller.ControllerContext.HttpContext.Request.Form = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "" } // Empty id triggers create path
            });

            // Act
            var result = await _controller.CreateOrUpdate(createCommand, updateCommand);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task Delete_ThrowsException_RedirectsToListWithWarning()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.Archive.DeleteAsync(It.IsAny<string>()))
                             .ThrowsAsync(new Exception("Delete exception"));

            // Act
            var result = await _controller.Delete("some-id");

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task IsArchiveNameExist_ThrowsException_ReturnsFalse()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.Archive.IsArchiveNameExist("name", "id"))
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.IsArchiveNameExist("name", "id");

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsTableNameExist_ThrowsException_ReturnsFalse()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.Archive.IsTableNameExist("tableName", "id"))
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.IsTableNameExist("tableName", "id");

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void Controller_HasAreaAttribute()
        {
            // Arrange
            var controllerType = typeof(ArchiveController);

            // Act
            var areaAttribute = controllerType.GetCustomAttributes(typeof(AreaAttribute), false).FirstOrDefault() as AreaAttribute;

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void GetPagination_HasHttpGetAttribute()
        {
            // Arrange
            var methodInfo = typeof(ArchiveController).GetMethod("GetPagination");

            // Act
            var httpGetAttribute = methodInfo.GetCustomAttributes(typeof(HttpGetAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void GetPaginated_HasHttpGetAttribute()
        {
            // Arrange
            var methodInfo = typeof(ArchiveController).GetMethod("GetPaginated");

            // Act
            var httpGetAttribute = methodInfo.GetCustomAttributes(typeof(HttpGetAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void CreateOrUpdate_HasHttpPostAttribute()
        {
            // Arrange
            var methodInfo = typeof(ArchiveController).GetMethod("CreateOrUpdate");

            // Act
            var httpPostAttribute = methodInfo.GetCustomAttributes(typeof(HttpPostAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpPostAttribute);
        }

        [Fact]
        public void CreateOrUpdate_HasValidateAntiForgeryTokenAttribute()
        {
            // Arrange
            var methodInfo = typeof(ArchiveController).GetMethod("CreateOrUpdate");

            // Act
            var antiForgeryAttribute = methodInfo.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiForgeryAttribute);
        }

        [Fact]
        public void CreateOrUpdate_HasAntiXssAttribute()
        {
            // Arrange
            var methodInfo = typeof(ArchiveController).GetMethod("CreateOrUpdate");

            // Act
            var antiXssAttribute = methodInfo.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void IsArchiveNameExist_HasHttpGetAttribute()
        {
            // Arrange
            var methodInfo = typeof(ArchiveController).GetMethod("IsArchiveNameExist");

            // Act
            var httpGetAttribute = methodInfo.GetCustomAttributes(typeof(HttpGetAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void IsTableNameExist_HasHttpGetAttribute()
        {
            // Arrange
            var methodInfo = typeof(ArchiveController).GetMethod("IsTableNameExist");

            // Act
            var httpGetAttribute = methodInfo.GetCustomAttributes(typeof(HttpGetAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }
    }
}
