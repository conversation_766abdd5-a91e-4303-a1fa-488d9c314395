using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class AlertReceiverRepositoryTests : IClassFixture<AlertReceiverFixture>
{
    private readonly AlertReceiverFixture _alertReceiverFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly AlertReceiverRepository _repository;

    public AlertReceiverRepositoryTests(AlertReceiverFixture alertReceiverFixture)
    {
        _alertReceiverFixture = alertReceiverFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new AlertReceiverRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var alertReceiver = _alertReceiverFixture.AlertReceiverDto;

        // Act
        await _dbContext.AlertReceivers.AddAsync(alertReceiver);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(alertReceiver.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(alertReceiver.Name, result.Name);
        Assert.Equal(alertReceiver.EmailAddress, result.EmailAddress);
        Assert.Equal(alertReceiver.CompanyId, result.CompanyId);
        Assert.Single(_dbContext.AlertReceivers);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var alertReceiver = _alertReceiverFixture.AlertReceiverDto;
        await _dbContext.AlertReceivers.AddAsync(alertReceiver);
        await _dbContext.SaveChangesAsync();

        alertReceiver.Name = "Updated Name";
        alertReceiver.EmailAddress = "<EMAIL>";
        alertReceiver.IsMail = true;

        // Act
      _dbContext.AlertReceivers.Update(alertReceiver);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(alertReceiver.ReferenceId);
        // Assert
        Assert.Equal("Updated Name", result.Name);
        Assert.Equal("<EMAIL>", result.EmailAddress);
        Assert.True(result.IsMail);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var alertReceiver = _alertReceiverFixture.AlertReceiverDto;
    
        await _dbContext.AlertReceivers.AddAsync(alertReceiver);
        await _dbContext.SaveChangesAsync();

        alertReceiver.IsActive=false;

         _dbContext.AlertReceivers.Update(alertReceiver);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var alertReceiver = _alertReceiverFixture.AlertReceiverDto;
        await _dbContext.AlertReceivers.AddAsync(alertReceiver);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.GetByIdAsync(alertReceiver.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(alertReceiver.Id, result.Id);
        Assert.Equal(alertReceiver.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var alertReceiver = _alertReceiverFixture.AlertReceiverDto;
        await _dbContext.AlertReceivers.AddAsync(alertReceiver);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(alertReceiver.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(alertReceiver.ReferenceId, result.ReferenceId);
        Assert.Equal(alertReceiver.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var alertReceivers = _alertReceiverFixture.AlertReceiverList;
        await _repository.AddRangeAsync(alertReceivers);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(alertReceivers.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var alertReceivers = _alertReceiverFixture.AlertReceiverList;

        // Act
        var result = await _repository.AddRangeAsync(alertReceivers);

        // Assert
        Assert.Equal(alertReceivers.Count, result.Count());
        Assert.Equal(alertReceivers.Count, _dbContext.AlertReceivers.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var alertReceivers = _alertReceiverFixture.AlertReceiverList;
        await _repository.AddRangeAsync(alertReceivers);

        // Act
        var result = await _repository.RemoveRangeAsync(alertReceivers);

        // Assert
        Assert.Equal(alertReceivers.Count, result.Count());
        Assert.Empty(_dbContext.AlertReceivers);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region FindByFilterAsync Tests

    [Fact]
    public async Task FindByFilter_ShouldReturnFilteredEntities()
    {
        // Arrange
        var alertReceivers = _alertReceiverFixture.AlertReceiverList;
        var targetName = "TEST_RECEIVER";
        alertReceivers.First().Name = targetName;
        await _repository.AddRangeAsync(alertReceivers);

        // Act
        var result = await _repository.FindByFilterAsync(x => x.Name == targetName);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(targetName, result.First().Name);
    }

    [Fact]
    public async Task FindByFilter_ShouldReturnEmptyList_WhenNoMatch()
    {
        // Arrange
        var alertReceivers = _alertReceiverFixture.AlertReceiverList;
        await _repository.AddRangeAsync(alertReceivers);

        // Act
        var result = await _repository.FindByFilterAsync(x => x.Name == "NON_EXISTENT_NAME");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region FilterBy Tests

    [Fact]
    public void FilterBy_ShouldReturnFilteredQueryable()
    {
        // Arrange
        var alertReceivers = _alertReceiverFixture.AlertReceiverList;
        var targetName = "FILTER_RECEIVER";
        alertReceivers.First().Name = targetName;
        _dbContext.AlertReceivers.AddRange(alertReceivers);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.FilterBy(x => x.Name == targetName);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.ToList());
        Assert.Equal(targetName, result.First().Name);
    }

    [Fact]
    public void FilterBy_ShouldReturnEmptyQueryable_WhenNoMatch()
    {
        // Arrange
        var alertReceivers = _alertReceiverFixture.AlertReceiverList;
        _dbContext.AlertReceivers.AddRange(alertReceivers);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.FilterBy(x => x.Name == "NON_EXISTENT_NAME");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.ToList());
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public void PaginatedListAllAsync_ShouldReturnQueryable()
    {
        // Arrange
        var alertReceivers = _alertReceiverFixture.AlertReceiverPaginationList;
        _dbContext.AlertReceivers.AddRange(alertReceivers);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedQuery();;

        // Assert
        Assert.NotNull(result);
        Assert.Equal(alertReceivers.Count, result.Count());
    }

    [Fact]
    public void PaginatedListAllAsync_ShouldReturnEmptyQueryable_WhenNoEntities()
    {
        // Act
        var result = _repository.GetPaginatedQuery();;

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.ToList());
    }

    [Fact]
    public void PaginatedListAllAsync_ShouldFilterByCompanyId_WhenNotParent()
    {
     

        var repository = new AlertReceiverRepository(_dbContext, DbContextFactory.GetMockUserService());

        var alertReceivers = _alertReceiverFixture.AlertReceiverPaginationList;

        alertReceivers.Take(5).ToList().ForEach(x => x.CompanyId = "COMPANY_123");

        _dbContext.AlertReceivers.AddRange(alertReceivers);
        _dbContext.SaveChanges();

        // Act
        var result = repository.GetPaginatedQuery();;

        // Assert
        Assert.NotNull(result);
        var filteredResults = result.ToList();
        Assert.All(filteredResults, x => Assert.Equal("COMPANY_123", x.CompanyId));
    }

    #endregion

    #region IsAlertReceiverNameUnique Tests

    [Fact]
    public async Task IsAlertReceiverNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var alertReceiver = _alertReceiverFixture.AlertReceiverDto;
        alertReceiver.Name = "UniqueTestName";
        await _dbContext.AlertReceivers.AddAsync(alertReceiver);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsAlertReceiverNameUnique("UniqueTestName");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsAlertReceiverNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var alertReceivers = _alertReceiverFixture.AlertReceiverList;
        await _repository.AddRangeAsync(alertReceivers);

        // Act
        var result = await _repository.IsAlertReceiverNameUnique("NonExistentName");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsAlertReceiverNameUnique_ShouldReturnFalse_WhenNameIsNull()
    {
        // Act
        var result = await _repository.IsAlertReceiverNameUnique(null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsAlertReceiverNameUnique_ShouldReturnFalse_WhenNameIsEmpty()
    {
        // Act
        var result = await _repository.IsAlertReceiverNameUnique("");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsAlertReceiverNameExist Tests

    [Fact]
    public async Task IsAlertReceiverNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var alertReceiver = _alertReceiverFixture.AlertReceiverDto;
        alertReceiver.Name = "ExistingName";
        await _dbContext.AlertReceivers.AddAsync(alertReceiver);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsAlertReceiverNameExist("ExistingName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsAlertReceiverNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var alertReceivers = _alertReceiverFixture.AlertReceiverList;
        await _repository.AddRangeAsync(alertReceivers);

        // Act
        var result = await _repository.IsAlertReceiverNameExist("NonExistentName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsAlertReceiverNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntity()
    {
        // Arrange
        var alertReceiver = _alertReceiverFixture.AlertReceiverList;

        await _dbContext.AlertReceivers.AddRangeAsync(alertReceiver);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsAlertReceiverNameExist(alertReceiver[0].Name, alertReceiver[0].ReferenceId);

        // Assert
        Assert.True(!result);
    }

    [Fact]
    public async Task IsAlertReceiverNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var alertReceiver = _alertReceiverFixture.AlertReceiverDto;
        alertReceiver.Name = "SameName";
        await _dbContext.AlertReceivers.AddAsync(alertReceiver);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsAlertReceiverNameExist("SameName", alertReceiver.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region UpdateRange Tests

    [Fact]
    public async Task UpdateRange_ShouldUpdateEntities()
    {
        // Arrange
        var alertReceivers = _alertReceiverFixture.AlertReceiverList.Take(3).ToList();
        await _repository.AddRangeAsync(alertReceivers);

        // Modify entities
        foreach (var receiver in alertReceivers)
        {
            receiver.Name = "Updated Name";
            receiver.IsMail = true;
            receiver.IsActiveUser = false;
        }

        // Act
        var result = await _repository.UpdateRangeAsync(alertReceivers);

        // Assert
        Assert.Equal(alertReceivers.Count, result.Count());
        Assert.All(result, x => Assert.Equal("Updated Name", x.Name));
        Assert.All(result, x => Assert.True(x.IsMail));
        Assert.All(result, x => Assert.False(x.IsActiveUser));
    }

    [Fact]
    public async Task UpdateRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateRangeAsync(null));
    }

    #endregion

    #region Edge Cases and Negative Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var alertReceivers = _alertReceiverFixture.AlertReceiverList;
        var alertReceiver1 = alertReceivers[0];
        var alertReceiver2 = alertReceivers[1];

       
        await _dbContext.AlertReceivers.AddAsync(alertReceiver1);
        await _dbContext.AlertReceivers.AddAsync(alertReceiver2);
        await _dbContext.SaveChangesAsync();
        var results = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, results.Count);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.AlertReceivers.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var alertReceivers = _alertReceiverFixture.AlertReceiverList;

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(alertReceivers);
        var initialCount = alertReceivers.Count;

        var toUpdate = alertReceivers.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "Updated Name");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = alertReceivers.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Name == "Updated Name").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task IsAlertReceiverNameExist_ShouldHandleNullParameters()
    {
        // Act & Assert
        var result1 = await _repository.IsAlertReceiverNameExist(null, "valid-guid");
        var result2 = await _repository.IsAlertReceiverNameExist("TestName", null);
        var result3 = await _repository.IsAlertReceiverNameExist(null, null);

        Assert.False(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    [Fact]
    public async Task IsAlertReceiverNameExist_ShouldBeCaseSensitive()
    {
        // Arrange
        var alertReceiver = _alertReceiverFixture.AlertReceiverDto;
        alertReceiver.Name = "TestName";
        await _dbContext.AlertReceivers.AddAsync(alertReceiver);
        await _dbContext.SaveChangesAsync();

        // Act
        var result1 = await _repository.IsAlertReceiverNameExist("testname", "invalid-guid");
        var result2 = await _repository.IsAlertReceiverNameExist("TESTNAME", "invalid-guid");

        // Assert
        Assert.False(result1);
        Assert.False(result2);
    }

    [Fact]
    public async Task Repository_ShouldHandleSpecialCharactersInNames()
    {
        // Arrange
        var alertReceiver = _alertReceiverFixture.AlertReceiverDto;
        alertReceiver.Name = "Test@Name#123$%";

        // Act
        await _dbContext.AlertReceivers.AddAsync(alertReceiver);
        await _dbContext.SaveChangesAsync();
        var isUnique = await _repository.IsAlertReceiverNameUnique("Test@Name#123$%");
        var exists = await _repository.IsAlertReceiverNameExist("Test@Name#123$%", "invalid-guid");

        // Assert
        Assert.NotNull(alertReceiver);
        Assert.Equal("Test@Name#123$%", alertReceiver.Name);
        Assert.True(isUnique);
        Assert.True(exists);
    }

    #endregion
}
