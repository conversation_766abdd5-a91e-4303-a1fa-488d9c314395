﻿@model ContinuityPatrol.Domain.ViewModels.MonitorServicesListModel.MonitorServiceListViewModel

<div class="modal-dialog modal-dialog-centered modal-lg">
    <form class="modal-content" asp-controller="MonitoringServices" asp-antiforgery="true" id="CreateForm" asp-action="CreateOrUpdate" method="post" class="tab-wizard wizard-circle wizard clearfix">
        <div class="modal-header">
            <h6 class="page_title">
                <i class="cp-monitoring"></i><span>
                    Monitoring Service
                    Configuration
                </span>
            </h6>
            <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <div class="col-xl-12">
                    <div class="form-group">
                        <div class="form-label">Operational Service</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-business-service"></i></span>
                            <select asp-for="BusinessServiceName" class="form-select-modal" id="msBusinessService" data-placeholder="Select Operational Service">
                                <option></option>
                                @foreach (var businessService in Model.BusinessServiceList)
                                {
                                    <option id="@businessService.Id" value="@businessService.Name">@businessService.Name</option>
                                }
                            </select>
                        </div>
                        <span asp-validation-for="BusinessServiceId" id="BusinessServiceId-error"></span>
                    </div>
                </div>
                <div class="col-xl-12">
                    <div class="mb-3">
                        <div class="form-label">InfraObject</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-infra-object"></i></span>
                            <select asp-for="InfraObjectName" class="form-select-modal" id="msInfraObject" data-placeholder="Select InfraObject">
                                <option></option>
                            </select>
                        </div>
                        <span asp-validation-for="InfraObjectId" id="InfraObjectId-error"></span>
                    </div>
                </div>

                <div class="form-group" id="msMonitoringType">
                   <div class="row row-cols-4">
                        <div class="col-auto col">
                            <div class="form-check">
                                <input name="MonitoringType" type="radio" class="form-check-input msserverradio" value="PR">
                                <label title="" for="statelbl" class="form-check-label">
                                    PR
                                </label>
                            </div>
                        </div>
                        <div class="col-auto col">
                            <div class="form-check">
                                <input name="MonitoringType" type="radio" class="form-check-input msserverradio" value="DR">
                                <label title="" for="statelbl2" class="form-check-label">
                                    DR
                                </label>
                            </div>
                        </div>
                        <div class="col-auto col">
                            <div class="form-check">
                                <input name="MonitoringType" type="radio" class="form-check-input msserverradio" value="Both">
                                <label title="" for="statelbl2" class="form-check-label">
                                    Both
                                </label>
                            </div>
                        </div>
                    </div>
                    <span asp-validation-for="MonitoringType" id="MonitoringType-error"></span>
                </div>

                <div class="col-xl-12" id="MSServerDiv">
                    <div class="mb-3">
                        <div class="form-label">Server</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-server"></i></span>
                            <select asp-for="ServerName" class="form-select-modal" data-placeholder="Select Server" id="msServer">
                                <option value="" disabled selected>Select Server</option>

                            </select>
                        </div>
                        <span asp-validation-for="ServerName" id="ServerId-error"></span>
                    </div>
                </div>
                <div class="col-xl-12">
                    <div class="mb-3">
                        <div class="form-label">Server Authentication Type</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-approval-matrix-header-icon"></i></span>
                            <select asp-for="Type" class="form-select-modal" id="msAuthenticationType" data-placeholder="Select Server Authentication Type">
                                <option></option>
                            </select>
                        </div>
                        <span asp-validation-for="Type" id="Type-error"></span>
                    </div>
                </div>
                <div class="col-xl-12" id="workflowType">
                    <div class="mb-3">
                        <div class="form-label">Workflow Type</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-workflow-type"></i></span>
                            <select asp-for="WorkflowType" class="form-select-modal" id="msWorkflowType" data-placeholder="Select Workflow Type">
                                <option></option>
                                <option value="Custom">Custom</option>
                                <option value="Resiliency Readiness">Resiliency Readiness</option>
                                <option value="Monitoring">Monitoring</option>

                            </select>
                        </div>
                        <span asp-validation-for="WorkflowType" id="WorkflowType-error"></span>
                    </div>
                </div>
                <div class="col-xl-12" id="workflow">
                    <div class="mb-3">
                        <div class="form-label">Workflow Name</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-workflow"></i></span>
                            <select asp-for="WorkflowId" class="form-select-modal" id="msWorkflow" data-placeholder="Select Workflow Name">
                                <option value=""></option>
                            </select>
                            <span class="input-group-text pe-0" id="msWorkflowAdded" title="Add" role="button">
                                <i id="btnWorkflowSaveProfile" role="button" class="cp-circle-plus fs-5 text-primary ms-2"></i>
                            </span>
                        </div>
                        <span asp-validation-for="WorkflowName" id="WorkflowId-error"></span>
                    </div>
                </div>
                <div class="col-xl-12" id="command">
                    <div class="mb-3">
                        <div class="form-group">
                            <div class="row row-cols-4">
                                <div class="col-auto col">
                                    <div class="form-check">
                                        <input name="threadType" type="radio" class="form-check-input msradio" value="Service">
                                        <label title="" for="statelbl" class="form-check-label">
                                            Service
                                        </label>
                                    </div>
                                </div>
                                <div class="col-auto col">
                                    <div class="form-check">
                                        <input name="threadType" type="radio" class="form-check-input msradio" value="Process">
                                        <label title="" for="statelbl2" class="form-check-label">
                                            Process
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <span asp-validation-for="ThreadType" id="ThreadType-error"></span>
                        </div>
                        <div class="Service box" id="msServiceDiv">
                            <div class="form-group">
                                <div class="form-label"><span id="serviceNameLabel">Service Name</span></div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-settings me-1"></i></span>
                                    <input type="text" class="form-control" placeholder="Enter Service Name" id="msServiceName" maxlength="100" autocomplete="off">
                                    <span class="input-group-text pe-0" id="msServiceAdded" title="Add" role="button">
                                        <i id="btnServiceSaveProfile" role="button" class="cp-circle-plus fs-5 text-primary ms-2"></i>
                                    </span>
                                </div>
                                <span asp-validation-for="ServicePath" id="ServicePath-error"></span>
                            </div>
                        </div>
                        <div class="Process box" id="msProcessDiv">
                            <div class="form-group">
                                <div class="form-label">Process Name</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-settings me-1"></i></span>
                                    <input type="text" class="form-control" placeholder="Enter Process Name" id="msProcessName" maxlength="100" autocomplete="off">
                                    <span class="input-group-text pe-0" id="msProcessAdded" title="Add" role="button">
                                        <i id="btnProcessSaveProfile" role="button" class="cp-circle-plus fs-5 text-primary ms-2"></i>
                                    </span>
                                </div>
                                <span asp-validation-for="ServicePath" id="ProcessPath-error"></span>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="mb-3" id="msAddedName">
                    <div class="form-group">
                        <span class="form-label">Added Components</span>
                        <div class="input-group" style="height: 40px;overflow-y: auto; overflow-x:hidden;">
                            <div id="selectedPathDetails"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary">
                    <i class="cp-note me-1"></i>Note: All fields are
                    mandatory
                    except optional
                </small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm"
                            data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <button type="button" class="btn btn-primary btn-sm" id="btnMSSave">Save</button>
                </div>
            </div>
    </form>
</div>



