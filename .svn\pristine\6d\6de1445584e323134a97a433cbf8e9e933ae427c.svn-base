using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowProfileInfoRepositoryTests : IClassFixture<WorkflowProfileInfoFixture>,IClassFixture<WorkflowProfileFixture>,IClassFixture<WorkflowFixture>,IClassFixture<InfraObjectFixture>
    {
        private readonly WorkflowProfileInfoFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowProfileInfoRepository _repoParent;
        private readonly WorkFlowRepository _workFlowRepository;
        private readonly WorkflowProfileFixture _workflowProfileFixture;
        private readonly WorkflowFixture _workflowFixture;
        private readonly InfraObjectFixture _infraObjectFixture;

        public WorkflowProfileInfoRepositoryTests(WorkflowProfileInfoFixture fixture, WorkflowProfileFixture workflowProfileFixture, WorkflowFixture workflowFixture, InfraObjectFixture infraObjectFixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _workFlowRepository = new WorkFlowRepository(_dbContext, DbContextFactory.GetMockUserService());
            _repoParent = new WorkflowProfileInfoRepository(_dbContext, DbContextFactory.GetMockUserService(), _workFlowRepository);
            // _repoNotParent = new WorkflowProfileInfoRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent(), _workFlowRepository);
            _workflowProfileFixture = workflowProfileFixture;
            _workflowFixture = workflowFixture;
            _infraObjectFixture = infraObjectFixture;
        }

        [Fact]
        public async Task ListAllAsync_ReturnsAll_WhenIsAllInfra()
        {
            await _dbContext.WorkflowProfileInfos.AddRangeAsync(_fixture.WorkflowProfileInfoList);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.ListAllAsync();

            Assert.Equal(_fixture.WorkflowProfileInfoList.Count, result.Count);
        }

        [Fact]
        public async Task ConfiguredProfileInfo_ReturnsConfiguredProfiles()
        {
            await _dbContext.WorkflowProfileInfos.AddRangeAsync(_fixture.WorkflowProfileInfoList);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.ConfiguredProfileInfo();

            Assert.All(result, x => Assert.NotNull(x.ProfileId));
        }

        [Fact]
        public async Task GetByReferenceIdAsync_ReturnsEntity()
        {
            var entity = _fixture.WorkflowProfileInfoDto;
            await _dbContext.WorkflowProfileInfos.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetByReferenceIdAsync(entity.ReferenceId);

            Assert.NotNull(result);
            Assert.Equal(entity.ReferenceId, result.ReferenceId);
        }

        [Fact]
        public async Task GetWorkflowProfileInfoNames_ReturnsNames()
        {
            //_mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile")).ReturnsAsync(new List<string>());
            await _dbContext.WorkflowProfileInfos.AddRangeAsync(_fixture.WorkflowProfileInfoList);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowProfileInfoNames();

            Assert.All(result, x => Assert.NotNull(x.ProfileName));
        }

        [Fact]
        public void GetPaginatedQuery_ReturnsActiveOrdered_WhenIsAllInfra()
        {
            _dbContext.WorkflowProfileInfos.AddRange(_fixture.WorkflowProfileInfoPaginationList);
            _dbContext.SaveChanges();

            var result = _repoParent.GetPaginatedQuery().ToList();

            Assert.All(result, x => Assert.True(x.IsActive));
            Assert.True(result.SequenceEqual(result.OrderByDescending(x => x.Id)));
        }

        [Fact]
        public async Task GetWorkflowProfileInfoByProfileId_ReturnsEntity_WhenIsAllInfra()
        {
            var workflowProfile= _workflowProfileFixture.WorkflowProfileDto;
            await _dbContext.WorkflowProfiles.AddRangeAsync(workflowProfile);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowProfileInfoDto;
            entity.ProfileId = workflowProfile.ReferenceId;
            await _dbContext.WorkflowProfileInfos.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowProfileInfoByProfileId(entity.ProfileId);

            Assert.NotNull(result);
            Assert.Equal(entity.ProfileId, result.ProfileId);
        }

        [Fact]
        public async Task GetWorkflowProfileFilterByProfileId_ReturnsEntity()
        {
            var workflowProfile = _workflowProfileFixture.WorkflowProfileDto;
            await _dbContext.WorkflowProfiles.AddRangeAsync(workflowProfile);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowProfileInfoDto;
            entity.ProfileId = workflowProfile.ReferenceId;
            
            await _dbContext.WorkflowProfileInfos.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowProfileFilterByProfileId(entity.ProfileId);

            Assert.NotNull(result);
            Assert.Equal(entity.ProfileId, result.ProfileId);
        }

        [Fact]
        public async Task GetWorkflowProfileInfoByProfileIdAsync_ReturnsList_WhenIsAllInfra()
        {
            var workflowProfile = _workflowProfileFixture.WorkflowProfileDto;
            await _dbContext.WorkflowProfiles.AddRangeAsync(workflowProfile);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowProfileInfoDto;
            entity.ProfileId = workflowProfile.ReferenceId;
            await _dbContext.WorkflowProfileInfos.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowProfileInfoByProfileIdAsync(entity.ProfileId);

            Assert.All(result, x => Assert.Equal(entity.ProfileId, x.ProfileId));
        }

        [Fact]
        public async Task GetWorkflowProfileInfoByProfileIds_ReturnsList_WhenIsAllInfra()
        {
            var workflowProfile = _workflowProfileFixture.WorkflowProfileList;
            await _dbContext.WorkflowProfiles.AddRangeAsync(workflowProfile);
            await _dbContext.SaveChangesAsync();


            var WorkflowProFileinfo= _fixture.WorkflowProfileInfoList;

            WorkflowProFileinfo[0].ProfileId = workflowProfile[0].ReferenceId;
            WorkflowProFileinfo[1].ProfileId = workflowProfile[1].ReferenceId;
            WorkflowProFileinfo[2].ProfileId = workflowProfile[2].ReferenceId;

            await _dbContext.WorkflowProfileInfos.AddRangeAsync(_fixture.WorkflowProfileInfoList);
            await _dbContext.SaveChangesAsync();

            var ids = _fixture.WorkflowProfileInfoList.Select(x => x.ProfileId).ToList();
            var result = await _repoParent.GetWorkflowProfileInfoByProfileIds(ids);

            Assert.All(result, x => Assert.Contains(x.ProfileId, ids));
        }

        [Fact]
        public async Task GetProfileIdAttachByWorkflowId_ReturnsEntity_WhenIsAllInfra()
        {
            var workflow= _workflowFixture.WorkflowList;
            await _dbContext.WorkFlows.AddRangeAsync(workflow);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowProfileInfoDto;
            entity.WorkflowId = workflow[0].ReferenceId;
            await _dbContext.WorkflowProfileInfos.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetProfileIdAttachByWorkflowId(entity.WorkflowId);

            Assert.NotNull(result);
            Assert.Equal(entity.WorkflowId, result.WorkflowId);
        }

        [Fact]
        public async Task GetProfileIdAttachByInfraObjectId_ReturnsList_WhenIsAllInfra()
        {
            var infraObject = _infraObjectFixture.InfraObjectDto;

            await _dbContext.InfraObjects.AddAsync(infraObject);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowProfileInfoDto;
                entity.InfraObjectId=infraObject.ReferenceId;
            await _dbContext.WorkflowProfileInfos.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetProfileIdAttachByInfraObjectId(entity.InfraObjectId);

            Assert.All(result, x => Assert.Equal(entity.InfraObjectId, x.InfraObjectId));
        }

        [Fact]
        public async Task GetWorkflowProfileByInfraId_ReturnsList_WhenIsAllInfra()
        {
            var infraObject = _infraObjectFixture.InfraObjectDto;

            await _dbContext.InfraObjects.AddAsync(infraObject);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowProfileInfoDto;
            entity.InfraObjectId = infraObject.ReferenceId;
            await _dbContext.WorkflowProfileInfos.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowProfileByInfraId(entity.InfraObjectId);

            Assert.All(result, x => Assert.Equal(entity.InfraObjectId, x.InfraObjectId));
        }

        [Fact]
        public async Task GetWorkflowProfileByWorkflowId_ReturnsList_WhenIsAllInfra()
        {
            var worklfows = _workflowFixture.WorkflowList;

            await _dbContext.Workflows.AddRangeAsync(worklfows);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowProfileInfoDto;
            entity.WorkflowId = worklfows[0].ReferenceId;
            await _dbContext.WorkflowProfileInfos.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowProfileByWorkflowId(entity.WorkflowId);

            Assert.All(result, x => Assert.Equal(entity.WorkflowId, x.WorkflowId));
        }

        [Fact]
        public async Task IsWorkflowIdUnique_ReturnsTrue_WhenExists()
        {
            var entity = _fixture.WorkflowProfileInfoDto;
            await _dbContext.WorkflowProfileInfos.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.IsWorkflowIdUnique(entity.WorkflowId);

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowIdUnique_ReturnsFalse_WhenNotExists()
        {
            var result = await _repoParent.IsWorkflowIdUnique("non-existent");

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowProfileInfoNameExist_ReturnsTrue_WhenNameExists_AndIdIsInvalidGuid()
        {
            var entity = _fixture.WorkflowProfileInfoDto;
            entity.ProfileName = "TestName";
            await _dbContext.WorkflowProfileInfos.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.IsWorkflowProfileInfoNameExist("TestName", "invalid-guid");

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowProfileInfoNameExist_ReturnsFalse_WhenNameDoesNotExist_AndIdIsInvalidGuid()
        {
            var result = await _repoParent.IsWorkflowProfileInfoNameExist("NonExistent", "invalid-guid");

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowProfileInfoNameExist_ReturnsExpected_WhenIdIsValidGuid()
        {
            var id = Guid.NewGuid().ToString();
            var entity = _fixture.WorkflowProfileInfoDto;
            entity.ReferenceId = id;
            entity.ProfileName = "UniqueName";
            await _dbContext.WorkflowProfileInfos.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.IsWorkflowProfileInfoNameExist("UniqueName", id);

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowNameAndProfileNameUnique_ReturnsTrue_WhenExists()
        {
            var entity = _fixture.WorkflowProfileInfoDto;
            await _dbContext.WorkflowProfileInfos.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.IsWorkflowNameAndProfileNameUnique(entity.WorkflowId, entity.ProfileId);

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowNameAndProfileNameUnique_ReturnsFalse_WhenNotExists()
        {
            var result = await _repoParent.IsWorkflowNameAndProfileNameUnique("non-existent", "non-existent");

            Assert.False(result);
        }

        [Fact]
        public async Task GetWorkflowProfileInfoByWorkflowIdAndInfraObjectId_ReturnsEntity_WhenExists()
        {
            var entity = _fixture.WorkflowProfileInfoDto;
            await _dbContext.WorkflowProfileInfos.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowProfileInfoByWorkflowIdAndInfraObjectId(entity.WorkflowId, entity.InfraObjectId);

            Assert.NotNull(result);
            Assert.Equal(entity.WorkflowId, result.WorkflowId);
            Assert.Equal(entity.InfraObjectId, result.InfraObjectId);
        }

        [Fact]
        public async Task GetWorkflowProfileInfoByWorkflowIdAndInfraObjectId_ReturnsNull_WhenNotExists()
        {
            var result = await _repoParent.GetWorkflowProfileInfoByWorkflowIdAndInfraObjectId("non-existent", "non-existent");

            Assert.Null(result);
        }
    }
}