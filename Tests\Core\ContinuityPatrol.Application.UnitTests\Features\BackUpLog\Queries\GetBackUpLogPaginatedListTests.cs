using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BackUpLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUpLog.Queries;

public class GetBackUpLogPaginatedListTests : IClassFixture<BackUpLogFixture>
{
    private readonly BackUpLogFixture _backUpLogFixture;
    private readonly Mock<IBackUpLogRepository> _mockBackUpLogRepository;
    private readonly Mock<IMapper> _mapper;
    private readonly GetBackUpLogPaginatedListQueryHandler _handler;


    public GetBackUpLogPaginatedListTests(BackUpLogFixture backUpLogFixture)
    {
        _backUpLogFixture = backUpLogFixture;
        _mockBackUpLogRepository = BackUpLogRepositoryMocks.CreateBackUpLogRepository(_backUpLogFixture.BackUpLogs);

         _mapper = new Mock<IMapper>();
        _handler = new GetBackUpLogPaginatedListQueryHandler(_mapper.Object,
            _mockBackUpLogRepository.Object
            );
    }

    [Fact]
    public async Task Handle_ReturnPaginatedBackUpLogs_When_ValidQuery()
    {
        // Arrange
        var query = new GetBackUpLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            SortColumn = "DatabaseName",
            SortOrder = "asc"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ReturnFirstPage_When_PageNumber1()
    {
        // Arrange
        var query = new GetBackUpLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 5,
            SearchString = "",
            SortColumn = "DatabaseName",
            SortOrder = "asc"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeNull();
        query.PageSize.ShouldBe(5);
    }

    [Fact]
    public async Task Handle_ReturnSecondPage_When_PageNumber2()
    {
        // Arrange
        // Add more BackUpLogs to ensure we have enough for pagination
        for (int i = 0; i < 10; i++)
        {
            _backUpLogFixture.BackUpLogs.Add(new Domain.Entities.BackUpLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                HostName = $"PaginationServer{i}",
                DatabaseName = $"PaginationDB{i}",
                UserName = $"PaginationUser{i}",
                IsLocalServer = true,
                IsBackUpServer = false,
                BackUpPath = $@"C:\Backups\PaginationDB{i}.bak",
                Type = "Full",
                Status = "Completed",
                IsActive = true
            });
        }

        var query = new GetBackUpLogPaginatedListQuery
        {
            PageNumber = 2,
            PageSize = 5,
            SearchString = "",
            SortColumn = "DatabaseName",
            SortOrder = "asc"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
        query.PageSize.ShouldBe(5);
    }

    [Fact]
    public async Task Handle_ReturnFilteredResults_When_SearchStringProvided()
    {
        // Arrange
        // Add specific BackUpLog for search testing
        _backUpLogFixture.BackUpLogs.Add(new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "SearchableServer",
            DatabaseName = "SearchableDatabase",
            UserName = "SearchableUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\SearchableDatabase.bak",
            Type = "Full",
            Status = "Completed",
            IsActive = true
        });

        var query = new GetBackUpLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Searchable",
            SortColumn = "DatabaseName",
            SortOrder = "asc"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
        
    }
    
    [Fact]
    public async Task Handle_ReturnSortedResults_When_SortColumnProvided()
    {
        // Arrange
        var query = new GetBackUpLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            SortColumn = "HostName",
            SortOrder = "desc"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();

    }

    [Fact]
    public async Task Handle_ReturnAscendingSortedResults_When_SortOrderAsc()
    {
        // Arrange
        var query = new GetBackUpLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            SortColumn = "Type",
            SortOrder = "asc"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ReturnDescendingSortedResults_When_SortOrderDesc()
    {
        // Arrange
        var query = new GetBackUpLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            SortColumn = "Status",
            SortOrder = "desc"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ReturnCorrectPageSize_When_CustomPageSize()
    {
        // Arrange
        var query = new GetBackUpLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 3,
            SearchString = "",
            SortColumn = "DatabaseName",
            SortOrder = "asc"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
        query.PageSize.ShouldBe(3);

    }

    [Fact]
    public async Task Handle_ReturnEmptyResults_When_NoMatchingData()
    {
        // Arrange
        var emptyBackUpLogs = new List<Domain.Entities.BackUpLog>();
        var mockEmptyRepository = BackUpLogRepositoryMocks.CreateBackUpLogRepository(emptyBackUpLogs);
        var mockEmptyRepos = BackUpLogRepositoryMocks.CreateBackUpLogRepository(emptyBackUpLogs);

        var query = new GetBackUpLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            SortColumn = "DatabaseName",
            SortOrder = "asc"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ReturnCorrectTotalCount_When_MultiplePages()
    {
        // Arrange
        // Ensure we have enough data for multiple pages
        var totalLogs = _backUpLogFixture.BackUpLogs.Count(x => x.IsActive);
        
        var query = new GetBackUpLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 2,
            SearchString = "",
            SortColumn = "DatabaseName",
            SortOrder = "asc"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ReturnMappedData_When_ValidQuery()
    {
        // Arrange
        var query = new GetBackUpLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            SortColumn = "DatabaseName",
            SortOrder = "asc"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CallRepositoryOnce_When_QueryExecuted()
    {
        // Arrange
        var query = new GetBackUpLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            SortColumn = "DatabaseName",
            SortOrder = "asc"
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

    }

    [Fact]
    public async Task Handle_ReturnCorrectPaginationInfo_When_ValidQuery()
    {
        // Arrange
        var query = new GetBackUpLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 5,
            SearchString = "",
            SortColumn = "DatabaseName",
            SortOrder = "asc"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        query.ShouldNotBeNull();
        query.PageSize.ShouldBe(5);
    }
}
