using ContinuityPatrol.Application.Features.DataSetColumns.Commands.Create;
using ContinuityPatrol.Application.Features.DataSetColumns.Queries.GetColumnNames;
using ContinuityPatrol.Application.Features.DataSetColumns.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.DataSetColumnsModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DataSetColumnsFixture
{
    public CreateDataSetColumnsCommand CreateDataSetColumnsCommand { get; }
    public CreateDataSetColumnsResponse CreateDataSetColumnsResponse { get; }
    public DataSetColumnsListVm DataSetColumnsListVm { get; }
    public DataSetColumnsColumnNameVm DataSetColumnsColumnNameVm { get; }
    public GetDataSetColumnsListQuery GetDataSetColumnsListQuery { get; }
    public GetDataSetColumnsColumnNameQuery GetDataSetColumnsColumnNameQuery { get; }

    public DataSetColumnsFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CreateDataSetColumnsCommand>(c => c
            .With(b => b.TableName, "Enterprise_DataSet_Table")
            .With(b => b.ColumnName, "enterprise_column_id")
            .With(b => b.DataSetId, Guid.NewGuid().ToString()));

        fixture.Customize<CreateDataSetColumnsResponse>(c => c
            .With(b => b.DataSetColumnsId, Guid.NewGuid().ToString())
            .With(b => b.Message, "Enterprise DataSet Column created successfully!")
            .With(b => b.Success, true));

        fixture.Customize<DataSetColumnsListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.TableName, "Enterprise_Business_Service")
            .With(b => b.ColumnName, "business_service_id")
            .With(b => b.DataSetId, Guid.NewGuid().ToString()));
          

        fixture.Customize<DataSetColumnsColumnNameVm>(c => c
            .With(b => b.ColumnName, "enterprise_data_column")
            .With(b => b.IsPrimaryKey, false)
            .With(b => b.ColumnKey, "Column key value"));
      

        fixture.Customize<GetDataSetColumnsColumnNameQuery>(c => c
            .With(b => b.DBName, "ContinuityPatrol_Enterprise")
            .With(b => b.TableName, "Enterprise_Configuration_Table"));

        CreateDataSetColumnsCommand = fixture.Create<CreateDataSetColumnsCommand>();
        CreateDataSetColumnsResponse = fixture.Create<CreateDataSetColumnsResponse>();
        DataSetColumnsListVm = fixture.Create<DataSetColumnsListVm>();
        DataSetColumnsColumnNameVm = fixture.Create<DataSetColumnsColumnNameVm>();
        GetDataSetColumnsListQuery = fixture.Create<GetDataSetColumnsListQuery>();
        GetDataSetColumnsColumnNameQuery = fixture.Create<GetDataSetColumnsColumnNameQuery>();
    }
}
