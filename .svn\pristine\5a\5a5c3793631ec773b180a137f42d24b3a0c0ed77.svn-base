using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class TeamResourceFixture : IDisposable
{
    public List<TeamResource> TeamResourcePaginationList { get; set; }
    public List<TeamResource> TeamResourceList { get; set; }
    public TeamResource TeamResourceDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public TeamResourceFixture()
    {
        var fixture = new Fixture();

        TeamResourceList = fixture.Create<List<TeamResource>>();

        TeamResourcePaginationList = fixture.CreateMany<TeamResource>(20).ToList();

        TeamResourceDto = fixture.Create<TeamResource>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public TeamResource CreateTeamResource(
        string teamMasterId = null,
        string teamMasterName = "Default Team",
        string resourceId = "DEFAULT_USER",
        string resourceName = "Default Resource",
        string email = "<EMAIL>",
        string phone = "************",
        bool isActive = true,
        bool isDelete = false)
    {
        return new TeamResource
        {
            ReferenceId = Guid.NewGuid().ToString(),
            TeamMasterId = teamMasterId ?? Guid.NewGuid().ToString(),
            TeamMasterName = teamMasterName,
            ResourceId = resourceId,
            ResourceName = resourceName,
            Email = email,
            Phone = phone,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<TeamResource> CreateMultipleTeamResources(int count, string teamMasterId = null)
    {
        var resources = new List<TeamResource>();
        var masterId = teamMasterId ?? Guid.NewGuid().ToString();

        for (int i = 1; i <= count; i++)
        {
            resources.Add(CreateTeamResource(
                teamMasterId: masterId,
                teamMasterName: "Test Team",
                resourceId: $"USER_{i:D3}",
                resourceName: $"Resource {i}",
                email: $"resource{i}@company.com",
                phone: $"123-456-{7890 + i}"
            ));
        }
        return resources;
    }

    public TeamResource CreateTeamResourceWithSpecificId(string referenceId, string resourceId = "TEST_USER")
    {
        return new TeamResource
        {
            ReferenceId = referenceId,
            TeamMasterId = Guid.NewGuid().ToString(),
            TeamMasterName = "Test Team",
            ResourceId = resourceId,
            ResourceName = "Test Resource",
            Email = "<EMAIL>",
            Phone = "************",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public TeamResource CreateTeamResourceForTeam(string teamMasterId, string teamMasterName, string resourceName = null)
    {
        return CreateTeamResource(
            teamMasterId: teamMasterId,
            teamMasterName: teamMasterName,
            resourceName: resourceName ?? $"Resource for {teamMasterName}"
        );
    }

    public List<TeamResource> CreateTeamResourcesForTeam(string teamMasterId, string teamMasterName, int count)
    {
        var resources = new List<TeamResource>();
        for (int i = 1; i <= count; i++)
        {
            resources.Add(CreateTeamResource(
                teamMasterId: teamMasterId,
                teamMasterName: teamMasterName,
                resourceId: $"USER_{teamMasterName}_{i:D2}",
                resourceName: $"{teamMasterName} Member {i}",
                email: $"member{i}@{teamMasterName.ToLower().Replace(" ", "")}.com",
                phone: $"123-{teamMasterName.GetHashCode() % 1000:D3}-{i:D4}"
            ));
        }
        return resources;
    }

    public List<TeamResource> CreateTeamResourcesWithStatus(int activeCount, int inactiveCount, string teamMasterId = null)
    {
        var resources = new List<TeamResource>();
        var masterId = teamMasterId ?? Guid.NewGuid().ToString();

        for (int i = 1; i <= activeCount; i++)
        {
            resources.Add(CreateTeamResource(
                teamMasterId: masterId,
                resourceName: $"Active Resource {i}",
                resourceId: $"ACTIVE_USER_{i:D3}",
                isActive: true
            ));
        }

        for (int i = 1; i <= inactiveCount; i++)
        {
            resources.Add(CreateTeamResource(
                teamMasterId: masterId,
                resourceName: $"Inactive Resource {i}",
                resourceId: $"INACTIVE_USER_{i:D3}",
                isActive: false
            ));
        }

        return resources;
    }

    public TeamResource CreateDeveloper(string teamMasterId = null, string name = "John Developer")
    {
        return CreateTeamResource(
            teamMasterId: teamMasterId ?? Guid.NewGuid().ToString(),
            teamMasterName: "Development Team",
            resourceId: "DEV_001",
            resourceName: name,
            email: $"{name.ToLower().Replace(" ", ".")}@company.com",
            phone: "************"
        );
    }

    public TeamResource CreateQAEngineer(string teamMasterId = null, string name = "Jane QA")
    {
        return CreateTeamResource(
            teamMasterId: teamMasterId ?? Guid.NewGuid().ToString(),
            teamMasterName: "QA Team",
            resourceId: "QA_001",
            resourceName: name,
            email: $"{name.ToLower().Replace(" ", ".")}@company.com",
            phone: "************"
        );
    }

    public TeamResource CreateDevOpsEngineer(string teamMasterId = null, string name = "Bob DevOps")
    {
        return CreateTeamResource(
            teamMasterId: teamMasterId ?? Guid.NewGuid().ToString(),
            teamMasterName: "DevOps Team",
            resourceId: "DEVOPS_001",
            resourceName: name,
            email: $"{name.ToLower().Replace(" ", ".")}@company.com",
            phone: "************"
        );
    }

    public List<TeamResource> CreateStandardTeamResources(string teamMasterId = null)
    {
        var masterId = teamMasterId ?? Guid.NewGuid().ToString();
        return new List<TeamResource>
        {
            CreateDeveloper(masterId),
            CreateQAEngineer(masterId),
            CreateDevOpsEngineer(masterId)
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
