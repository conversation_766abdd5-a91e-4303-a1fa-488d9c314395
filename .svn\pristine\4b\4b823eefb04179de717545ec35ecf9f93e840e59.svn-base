﻿namespace ContinuityPatrol.Application.Features.WorkflowPermission.Commands.Create;

public class CreateWorkflowPermissionCommandValidator : AbstractValidator<CreateWorkflowPermissionCommand>
{
    private readonly IWorkflowPermissionRepository _workflowPermissionRepository;

    public CreateWorkflowPermissionCommandValidator(IWorkflowPermissionRepository workflowPermissionRepository)
    {
        _workflowPermissionRepository = workflowPermissionRepository;

        RuleFor(p => p.Description)
            .MaximumLength(250).WithMessage("InfraObject {PropertyName} Maximum 250 characters.")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Workflow Permission {PropertyName} contains invalid characters.")
            .When(p => p.Description.IsNotNullOrWhiteSpace());
    }
}