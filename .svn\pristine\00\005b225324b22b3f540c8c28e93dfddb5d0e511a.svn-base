﻿using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Events.Create;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionFieldMaster.Events
{
    public class CreateWorkflowActionFieldMasterEventTests
    {
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly Mock<ILogger<WorkflowActionFieldMasterCreatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly WorkflowActionFieldMasterCreatedEventHandler _handler;

        public CreateWorkflowActionFieldMasterEventTests()
        {
            _mockUserService = new Mock<ILoggedInUserService>();
            _mockLogger = new Mock<ILogger<WorkflowActionFieldMasterCreatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _handler = new WorkflowActionFieldMasterCreatedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldLogInformation_WhenWorkflowActionFieldMasterCreatedSuccessfully()
        {
            var createdEvent = new WorkflowActionFieldMasterCreatedEvent
            {
                Name = "Test Workflow Action Field Master"
            };

            _mockUserService.Setup(us => us.UserId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(us => us.LoginName).Returns("testuser");
            _mockUserService.Setup(us => us.RequestedUrl).Returns("/create");
            _mockUserService.Setup(us => us.CompanyId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(us => us.IpAddress).Returns("***********");

            await _handler.Handle(createdEvent, CancellationToken.None);

            _mockLogger.Verify(logger => logger.LogInformation(It.IsAny<string>()), Times.Once);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua => ua.ActivityDetails.Contains("created successfully"))), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldCallAddAsync_WhenEventIsHandled()
        {
            var createdEvent = new WorkflowActionFieldMasterCreatedEvent
            {
                Name = "Test Workflow Action Field Master"
            };

            _mockUserService.Setup(us => us.UserId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(us => us.LoginName).Returns("testuser");
            _mockUserService.Setup(us => us.RequestedUrl).Returns("/create");
            _mockUserService.Setup(us => us.CompanyId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(us => us.IpAddress).Returns("***********");

            await _handler.Handle(createdEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldUseCorrectUserActivityDetails()
        {
            var createdEvent = new WorkflowActionFieldMasterCreatedEvent
            {
                Name = "Test Workflow Action Field Master"
            };

            var userId = Guid.NewGuid().ToString();
            var loginName = "testuser";
            var companyId = Guid.NewGuid().ToString();
            var ipAddress = "***********";
            var expectedActivityDetails = $"WorkflowActionFieldMaster '{createdEvent.Name}' created successfully.";

            _mockUserService.Setup(us => us.UserId).Returns(userId);
            _mockUserService.Setup(us => us.LoginName).Returns(loginName);
            _mockUserService.Setup(us => us.RequestedUrl).Returns("/create");
            _mockUserService.Setup(us => us.CompanyId).Returns(companyId);
            _mockUserService.Setup(us => us.IpAddress).Returns(ipAddress);

            await _handler.Handle(createdEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua => ua.ActivityDetails == expectedActivityDetails)), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldGenerateNewUserIdIfNull_WhenHandlingEvent()
        {
            var createdEvent = new WorkflowActionFieldMasterCreatedEvent
            {
                Name = "Test Workflow Action Field Master"
            };

            _mockUserService.Setup(us => us.UserId).Returns(string.Empty);
            _mockUserService.Setup(us => us.LoginName).Returns("testuser");
            _mockUserService.Setup(us => us.RequestedUrl).Returns("/create");
            _mockUserService.Setup(us => us.CompanyId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(us => us.IpAddress).Returns("***********");

            var expectedUserId = Guid.NewGuid().ToString();

            await _handler.Handle(createdEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua => !string.IsNullOrEmpty(ua.CreatedBy))), Times.Once);
        }
    }
}
