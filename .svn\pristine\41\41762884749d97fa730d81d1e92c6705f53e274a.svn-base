﻿using ContinuityPatrol.Application.Features.LicenseManager.Events.BaseLicenseEvent.Create;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Create;

public class CreateBaseLicenseCommandHandler : IRequestHandler<CreateBaseLicenseCommand, CreateBaseLicenseResponse>
{
    private readonly ILicenseHistoryRepository _licenseHistoryRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateBaseLicenseCommandHandler(ILicenseManagerRepository licenseManagerRepository, IPublisher publisher,
        ILicenseHistoryRepository licenseHistoryRepository, ILoggedInUserService loggedInUserService, IMapper mapper)
    {
        _licenseManagerRepository = licenseManagerRepository;
        _publisher = publisher;
        _licenseHistoryRepository = licenseHistoryRepository;
        _loggedInUserService = loggedInUserService;
        _mapper = mapper;
    }

    public async Task<CreateBaseLicenseResponse> Handle(CreateBaseLicenseCommand request,
        CancellationToken cancellationToken)
    {
        var splitLicenseKey = await SplitLicenseKey(request.LicenseKey);

        var startDate = DateTime.Now;
        
        var jObject = JObject.Parse(splitLicenseKey.LicenseCount);

        var startDateStr = jObject.SelectToken("startDate")?.ToString();
        var endDateStr = jObject.SelectToken("endDate")?.ToString();

        if (startDateStr.IsNotNullOrWhiteSpace() &&
            DateTime.TryParse(startDateStr,
                CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedDate))
        {
            startDate = parsedDate;
        }

        var expiryDate =
            await _licenseManagerRepository.GetDateByExpireTime(splitLicenseKey.LicenseType, startDate, endDateStr);

        var licenseManager = _mapper.Map<Domain.Entities.LicenseManager>(splitLicenseKey);
        licenseManager.CompanyId = _loggedInUserService.CompanyId;
        licenseManager.CompanyName = _loggedInUserService.CompanyName;
        licenseManager.IsParent = _loggedInUserService.IsParent;
        licenseManager.ExpiryDate = SecurityHelper.Encrypt(expiryDate);
        licenseManager.LicenseKey = request.LicenseKey;

        if (splitLicenseKey.LicenseType.Contains("Enterprise-Unlimited"))
        {
            var license = await _licenseManagerRepository.GetLicensePoNumber();

            var isPrimary = license.Where(x => x.IsPrimary).ToList();

            if (!isPrimary.Any()) licenseManager.IsPrimary = true;
        }

        await _licenseManagerRepository.AddAsync(licenseManager);

        var licenseManagerHistory = _mapper.Map<Domain.Entities.LicenseHistory>(licenseManager);

        licenseManagerHistory.UpdaterId = _loggedInUserService.UserId;
        licenseManagerHistory.Id = 0;

        await _licenseHistoryRepository.AddAsync(licenseManagerHistory);

        var response = new CreateBaseLicenseResponse
        {
            Id = licenseManager.ReferenceId,

            Message = $"Base License '{splitLicenseKey.PoNumber}' added successfully."
        };

        await _publisher.Publish(new BaseLicenseCreatedEvent { PONumber = splitLicenseKey.PoNumber },
            cancellationToken);

        return response;
    }

    private Task<LicenseDto> SplitLicenseKey(string licenseKey)
    {
        var decryptLicenseKey = SecurityHelper.Decrypt(licenseKey);

        var splitLicenseKey = decryptLicenseKey.Split('*');

        var licenseDto = _mapper.Map<LicenseDto>(splitLicenseKey);

        return Task.FromResult(licenseDto);
    }
}