using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetList;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardWidgetModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DynamicDashboardWidgetControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DynamicDashboardWidgetsController _controller;
    private readonly DynamicDashboardWidgetFixture _dynamicDashboardWidgetFixture;

    public DynamicDashboardWidgetControllerTests()
    {
        _dynamicDashboardWidgetFixture = new DynamicDashboardWidgetFixture();

        var testBuilder = new ControllerTestBuilder<DynamicDashboardWidgetsController>();
        _controller = testBuilder.CreateController(
            _ => new DynamicDashboardWidgetsController(),
            out _mediatorMock);
    }

    #region GetDynamicDashboardWidgets Tests

    [Fact]
    public async Task GetDynamicDashboardWidgets_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedList = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardWidgetListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDynamicDashboardWidgets();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicDashboardWidgetListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item => Assert.NotNull(item.Id));
    }

    [Fact]
    public async Task GetDynamicDashboardWidgets_WithEmptyList_ReturnsEmptyOkResult()
    {
        // Arrange
        var emptyList = new List<DynamicDashboardWidgetListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardWidgetListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDynamicDashboardWidgets();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicDashboardWidgetListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDynamicDashboardWidgets_WithException_ThrowsException()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardWidgetListQuery>(), default))
            .ThrowsAsync(new Exception("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _controller.GetDynamicDashboardWidgets());
    }

    #endregion

    #region GetDynamicDashboardWidgetById Tests

    [Fact]
    public async Task GetDynamicDashboardWidgetById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetDetailVm;
        expectedDetail.ReferenceId = id;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardWidgetDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDynamicDashboardWidgetById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DynamicDashboardWidgetDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.ReferenceId);
        Assert.NotNull(returnedDetail.Name);
    }

    [Fact]
    public async Task GetDynamicDashboardWidgetById_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDynamicDashboardWidgetById(invalidId));
    }

    [Fact]
    public async Task GetDynamicDashboardWidgetById_WithNullId_ThrowsArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetDynamicDashboardWidgetById(null));
    }

    #endregion

    #region GetPaginatedDynamicDashboardWidgets Tests

    [Fact]
    public async Task GetPaginatedDynamicDashboardWidgets_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _dynamicDashboardWidgetFixture.GetDynamicDashboardWidgetPaginatedListQuery;
        var expectedResult = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicDashboardWidgets(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DynamicDashboardWidgetListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.Equal(expectedResult.CurrentPage, returnedResult.CurrentPage);
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboardWidgets_WithNullQuery_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetPaginatedDynamicDashboardWidgets(null));
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboardWidgets_WithLargePageSize_ReturnsValidResult()
    {
        // Arrange
        var query = _dynamicDashboardWidgetFixture.GetDynamicDashboardWidgetPaginatedListQuery;
        query.PageSize = 100;
        var expectedResult = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicDashboardWidgets(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DynamicDashboardWidgetListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
    }

    #endregion

    #region CreateDynamicDashboardWidget Tests

    [Fact]
    public async Task CreateDynamicDashboardWidget_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _dynamicDashboardWidgetFixture.CreateDynamicDashboardWidgetCommand;
        var expectedResponse = _dynamicDashboardWidgetFixture.CreateDynamicDashboardWidgetResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDynamicDashboardWidget(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDynamicDashboardWidgetResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task CreateDynamicDashboardWidget_WithNullCommand_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.CreateDynamicDashboardWidget(null));
    }

    [Fact]
    public async Task CreateDynamicDashboardWidget_WithInvalidCommand_ReturnsFailureResponse()
    {
        // Arrange
        var command = _dynamicDashboardWidgetFixture.CreateDynamicDashboardWidgetCommand;
        var failureResponse = new CreateDynamicDashboardWidgetResponse
        {
            Success = false,
            Message = "Validation failed"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.CreateDynamicDashboardWidget(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDynamicDashboardWidgetResponse>(createdResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Equal("Validation failed", returnedResponse.Message);
    }

    #endregion

    #region UpdateDynamicDashboardWidget Tests

    [Fact]
    public async Task UpdateDynamicDashboardWidget_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _dynamicDashboardWidgetFixture.UpdateDynamicDashboardWidgetCommand;
        var expectedResponse = _dynamicDashboardWidgetFixture.UpdateDynamicDashboardWidgetResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDynamicDashboardWidget(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDynamicDashboardWidgetResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDynamicDashboardWidget_WithNullCommand_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.UpdateDynamicDashboardWidget(null));
    }

    [Fact]
    public async Task UpdateDynamicDashboardWidget_WithInvalidCommand_ReturnsFailureResponse()
    {
        // Arrange
        var command = _dynamicDashboardWidgetFixture.UpdateDynamicDashboardWidgetCommand;
        var failureResponse = new UpdateDynamicDashboardWidgetResponse
        {
            Success = false,
            Message = "Update validation failed"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.UpdateDynamicDashboardWidget(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDynamicDashboardWidgetResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Equal("Update validation failed", returnedResponse.Message);
    }

    #endregion

    #region DeleteDynamicDashboardWidget Tests

    [Fact]
    public async Task DeleteDynamicDashboardWidget_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _dynamicDashboardWidgetFixture.DeleteDynamicDashboardWidgetResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDynamicDashboardWidgetCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDynamicDashboardWidget(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDynamicDashboardWidgetResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("deleted successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task DeleteDynamicDashboardWidget_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDynamicDashboardWidget(invalidId));
    }

    [Fact]
    public async Task DeleteDynamicDashboardWidget_WithNullId_ThrowsArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteDynamicDashboardWidget(null));
    }

    #endregion

    #region IsDynamicDashboardWidgetNameExist Tests

    [Fact]
    public async Task IsDynamicDashboardWidgetNameExist_WithExistingName_ReturnsTrue()
    {
        // Arrange
        var widgetName = "Enterprise Widget";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardWidgetNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDynamicDashboardWidgetNameExist(widgetName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedValue);
    }

    [Fact]
    public async Task IsDynamicDashboardWidgetNameExist_WithNonExistingName_ReturnsFalse()
    {
        // Arrange
        var widgetName = "Non-Existing Widget";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardWidgetNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDynamicDashboardWidgetNameExist(widgetName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    [Fact]
    public async Task IsDynamicDashboardWidgetNameExist_WithNullName_ThrowsArgumentException()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.IsDynamicDashboardWidgetNameExist(null, id));
    }

    #endregion

    #region Additional Comprehensive Test Cases

    [Fact]
    public async Task CreateDynamicDashboardWidget_WithComplexConfiguration_ReturnsCreatedResult()
    {
        // Arrange
        var command = _dynamicDashboardWidgetFixture.CreateDynamicDashboardWidgetCommand;
        command.Name = "Enterprise Real-Time Analytics Widget";
        var expectedResponse = _dynamicDashboardWidgetFixture.CreateDynamicDashboardWidgetResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDynamicDashboardWidget(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDynamicDashboardWidgetResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Real-Time Analytics Widget", command.Name);
    }

    [Fact]
    public async Task UpdateDynamicDashboardWidget_WithConfigurationChange_ReturnsOkResult()
    {
        // Arrange
        var command = _dynamicDashboardWidgetFixture.UpdateDynamicDashboardWidgetCommand;
        command.Name = "Updated Enterprise Widget";
        var expectedResponse = _dynamicDashboardWidgetFixture.UpdateDynamicDashboardWidgetResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDynamicDashboardWidget(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDynamicDashboardWidgetResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Updated Enterprise Widget", command.Name);
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboardWidgets_WithSearchFilter_ReturnsFilteredResults()
    {
        // Arrange
        var query = _dynamicDashboardWidgetFixture.GetDynamicDashboardWidgetPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 10;
        query.SearchString = "Enterprise";
        var expectedResult = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicDashboardWidgets(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DynamicDashboardWidgetListVm>>(okResult.Value);
        Assert.Equal("Enterprise", query.SearchString);
        Assert.Equal(1, returnedResult.CurrentPage);
        Assert.Equal(10, query.PageSize);
    }

    [Fact]
    public async Task GetDynamicDashboardWidgetById_WithComplexWidget_ReturnsDetailedWidget()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetDetailVm;
        expectedDetail.ReferenceId = id;
        expectedDetail.Name = "Enterprise Performance Monitoring Widget";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardWidgetDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDynamicDashboardWidgetById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DynamicDashboardWidgetDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.ReferenceId);
        Assert.Equal("Enterprise Performance Monitoring Widget", returnedDetail.Name);
    }

    [Fact]
    public async Task DeleteDynamicDashboardWidget_WithDependentDashboards_ReturnsSuccessResponse()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _dynamicDashboardWidgetFixture.DeleteDynamicDashboardWidgetResponse;
        expectedResponse.Message = "DynamicDashboardWidget and all dependent dashboard references deleted successfully";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDynamicDashboardWidgetCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDynamicDashboardWidget(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDynamicDashboardWidgetResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("dependent dashboard references", returnedResponse.Message);
    }

    #endregion

    #region Additional Test Cases Following CompanyControllerTests and BusinessServiceControllerTests Patterns

    [Fact]
    public async Task GetDynamicDashboardWidgets_ReturnsEmptyList_WhenNoWidgetsExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardWidgetListQuery>(), default))
            .ReturnsAsync(new List<DynamicDashboardWidgetListVm>());

        // Act
        var result = await _controller.GetDynamicDashboardWidgets();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicDashboardWidgetListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDynamicDashboardWidgetById_Throws_WhenWidgetNotFound()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardWidgetDetailQuery>(q => q.Id == id), default))
            .ThrowsAsync(new NotFoundException("DynamicDashboardWidget", id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetDynamicDashboardWidgetById(id));
    }

    [Fact]
    public async Task CreateDynamicDashboardWidget_Throws_WhenNameExists()
    {
        // Arrange
        var command = _dynamicDashboardWidgetFixture.CreateDynamicDashboardWidgetCommand;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDynamicDashboardWidgetCommand>(), default))
            .ThrowsAsync(new InvalidOperationException("Widget name already exists"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.CreateDynamicDashboardWidget(command));
    }

    [Fact]
    public async Task UpdateDynamicDashboardWidget_Throws_WhenWidgetNotFound()
    {
        // Arrange
        var command = _dynamicDashboardWidgetFixture.UpdateDynamicDashboardWidgetCommand;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDynamicDashboardWidgetCommand>(), default))
            .ThrowsAsync(new NotFoundException("DynamicDashboardWidget", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.UpdateDynamicDashboardWidget(command));
    }

    [Fact]
    public async Task DeleteDynamicDashboardWidget_Throws_WhenWidgetNotFound()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDynamicDashboardWidgetCommand>(c => c.Id == id), default))
            .ThrowsAsync(new NotFoundException("DynamicDashboardWidget", id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.DeleteDynamicDashboardWidget(id));
    }

    [Fact]
    public async Task IsDynamicDashboardWidgetNameExist_IncludesIdInQuery_WhenProvided()
    {
        // Arrange
        var widgetName = "Test Widget";
        var widgetId = Guid.NewGuid().ToString();
        string? capturedId = null;
        string? capturedName = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardWidgetNameUniqueQuery>(), default))
            .Callback<IRequest<bool>, CancellationToken>((request, _) =>
            {
                if (request is GetDynamicDashboardWidgetNameUniqueQuery query)
                {
                    capturedId = query.Id;
                    capturedName = query.Name;
                }
            })
            .ReturnsAsync(false);

        // Act
        await _controller.IsDynamicDashboardWidgetNameExist(widgetName, widgetId);

        // Assert
        Assert.Equal(widgetId, capturedId);
        Assert.Equal(widgetName, capturedName);
    }

    [Fact]
    public async Task IsDynamicDashboardWidgetNameExist_ExcludesIdFromQuery_WhenNotProvided()
    {
        // Arrange
        var widgetName = "Test Widget";
        string? capturedId = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardWidgetNameUniqueQuery>(), default))
            .Callback<IRequest<bool>, CancellationToken>((request, _) =>
            {
                if (request is GetDynamicDashboardWidgetNameUniqueQuery query)
                {
                    capturedId = query.Id;
                }
            })
            .ReturnsAsync(false);

        // Act
        await _controller.IsDynamicDashboardWidgetNameExist(widgetName, null);

        // Assert
        Assert.Null(capturedId);
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboardWidgets_HandlesSearchString_Correctly()
    {
        // Arrange
        var query = _dynamicDashboardWidgetFixture.GetDynamicDashboardWidgetPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 10;
        query.SearchString = "Enterprise";

        var expectedData = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetListVm.Take(1).ToList();
        var expectedResult = PaginatedResult<DynamicDashboardWidgetListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardWidgetPaginatedListQuery>(q => q.SearchString == "Enterprise"), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicDashboardWidgets(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<DynamicDashboardWidgetListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);
        Assert.Equal(1, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboardWidgets_HandlesDifferentPageSizes()
    {
        // Arrange
        var query = _dynamicDashboardWidgetFixture.GetDynamicDashboardWidgetPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 3;

        var expectedData = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetListVm.Take(3).ToList();
        var expectedResult = PaginatedResult<DynamicDashboardWidgetListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardWidgetPaginatedListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicDashboardWidgets(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<DynamicDashboardWidgetListVm>>(okResult.Value);
        Assert.Equal(3, paginatedResult.PageSize);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(3, paginatedResult.Data.Count);
    }

    [Fact]
    public async Task CreateDynamicDashboardWidget_Returns201Created_WithSuccessMessage()
    {
        // Arrange
        var command = _dynamicDashboardWidgetFixture.CreateDynamicDashboardWidgetCommand;
        var expectedMessage = $"DynamicDashboardWidget '{command.Name}' has been created successfully!";
        var expectedResponse = new CreateDynamicDashboardWidgetResponse
        {
            Id = Guid.NewGuid().ToString(),
            Success = true,
            Message = expectedMessage
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDynamicDashboardWidget(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateDynamicDashboardWidgetResponse>(createdResult.Value);
        Assert.True(response.Success);
        Assert.Contains(command.Name, response.Message);
    }

    [Fact]
    public async Task UpdateDynamicDashboardWidget_ReturnsOkResult_WithSuccessMessage()
    {
        // Arrange
        var command = _dynamicDashboardWidgetFixture.UpdateDynamicDashboardWidgetCommand;
        var expectedMessage = $"DynamicDashboardWidget '{command.Name}' has been updated successfully!";
        var expectedResponse = new UpdateDynamicDashboardWidgetResponse
        {
            Id = command.Id,
            Success = true,
            Message = expectedMessage
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDynamicDashboardWidget(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateDynamicDashboardWidgetResponse>(okResult.Value);
        Assert.True(response.Success);
        Assert.Contains(command.Name, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task GetDynamicDashboardWidgetById_WithValidId_ReturnsCorrectWidgetType()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetDetailVm;
        expectedDetail.ReferenceId = id;
        expectedDetail.Name = "Chart Widget";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardWidgetDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDynamicDashboardWidgetById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DynamicDashboardWidgetDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.ReferenceId);
        Assert.Equal("Chart Widget", returnedDetail.Name);
    }

    [Fact]
    public async Task IsDynamicDashboardWidgetNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var widgetName = "Existing Widget";
        var widgetId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardWidgetNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDynamicDashboardWidgetNameExist(widgetName, widgetId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsDynamicDashboardWidgetNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var widgetName = "New Widget";
        var widgetId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardWidgetNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDynamicDashboardWidgetNameExist(widgetName, widgetId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task DeleteDynamicDashboardWidget_ReturnsOkResult_WithSuccessMessage()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = new DeleteDynamicDashboardWidgetResponse
        {
            Success = true,
            Message = "DynamicDashboardWidget has been deleted successfully!"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDynamicDashboardWidgetCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDynamicDashboardWidget(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteDynamicDashboardWidgetResponse>(okResult.Value);
        Assert.True(response.Success);
        Assert.Contains("deleted successfully", response.Message);
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboardWidgets_WithEmptySearchString_ReturnsAllResults()
    {
        // Arrange
        var query = _dynamicDashboardWidgetFixture.GetDynamicDashboardWidgetPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 10;
        query.SearchString = "";

        var expectedData = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetListVm;
        var expectedResult = PaginatedResult<DynamicDashboardWidgetListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardWidgetPaginatedListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicDashboardWidgets(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<DynamicDashboardWidgetListVm>>(okResult.Value);
        Assert.Equal(expectedData.Count, paginatedResult.Data.Count);
        Assert.Equal(expectedData.Count, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task GetDynamicDashboardWidgets_WithSpecificWidgetTypes_ReturnsFilteredResults()
    {
        // Arrange
        var expectedList = _dynamicDashboardWidgetFixture.DynamicDashboardWidgetListVm;
        expectedList.ForEach(w => w.Properties = "{\"type\":\"chart\",\"config\":{\"chartType\":\"line\"}}");

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardWidgetListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDynamicDashboardWidgets();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicDashboardWidgetListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, widget => Assert.Contains("chart", widget.Properties));
    }

    #endregion
}
