using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class RsyncOptionRepositoryTests : IClassFixture<RsyncOptionFixture>
{
    private readonly RsyncOptionFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly RsyncOptionRepository _repository;

    public RsyncOptionRepositoryTests(RsyncOptionFixture fixture)
    {
        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new RsyncOptionRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var existingName = "Existing Option";
        var existingOption = _fixture.CreateRsyncOptionWithName(existingName);
        _dbContext.RsyncOptions.Add(existingOption);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(existingName, "INVALID_GUID");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var existingOption = _fixture.CreateRsyncOptionWithName("Existing Option");
        _dbContext.RsyncOptions.Add(existingOption);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("Non Existing Option", "INVALID_GUID");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsWithDifferentValidId()
    {
        // Arrange
        var existingName = "Existing Option";
        var existingOption = _fixture.CreateRsyncOptionWithName(existingName);
        _dbContext.RsyncOptions.Add(existingOption);
        await _dbContext.SaveChangesAsync();

        var differentValidId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsNameExist(existingName, differentValidId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsWithSameValidId()
    {
        // Arrange
        var existingName = "Existing Option";
        var existingOption = _fixture.CreateRsyncOptionWithName(existingName);
        _dbContext.RsyncOptions.Add(existingOption);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(existingName, existingOption.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameIsNullOrEmpty(string name)
    {
        // Arrange
        var existingOption = _fixture.CreateRsyncOptionWithName("Existing Option");
        _dbContext.RsyncOptions.Add(existingOption);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(name, "INVALID_GUID");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldBeCaseSensitive()
    {
        // Arrange
        var existingName = "Existing Option";
        var existingOption = _fixture.CreateRsyncOptionWithName(existingName);
        _dbContext.RsyncOptions.Add(existingOption);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("EXISTING OPTION", "INVALID_GUID");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsRsyncOptionNameUnique Tests

    [Fact]
    public async Task IsRsyncOptionNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var existingName = "Existing Option";
        var existingOption = _fixture.CreateRsyncOptionWithName(existingName);
        _dbContext.RsyncOptions.Add(existingOption);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsRsyncOptionNameUnique(existingName);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsRsyncOptionNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var existingOption = _fixture.CreateRsyncOptionWithName("Existing Option");
        _dbContext.RsyncOptions.Add(existingOption);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsRsyncOptionNameUnique("Non Existing Option");

        // Assert
        Assert.False(result);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task IsRsyncOptionNameUnique_ShouldReturnFalse_WhenNameIsNullOrEmpty(string name)
    {
        // Arrange
        var existingOption = _fixture.CreateRsyncOptionWithName("Existing Option");
        _dbContext.RsyncOptions.Add(existingOption);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsRsyncOptionNameUnique(name);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsRsyncOptionNameUnique_ShouldBeCaseSensitive()
    {
        // Arrange
        var existingName = "Existing Option";
        var existingOption = _fixture.CreateRsyncOptionWithName(existingName);
        _dbContext.RsyncOptions.Add(existingOption);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsRsyncOptionNameUnique("EXISTING OPTION");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults_WhenDataExists()
    {
        // Arrange
        var options = _fixture.CreateRsyncOptionsForPagination(15);
        _dbContext.RsyncOptions.AddRange(options);
        await _dbContext.SaveChangesAsync();

        var pageNumber = 1;
        var pageSize = 5;

        // Act
        var result = await _repository.PaginatedListAllAsync(pageNumber, pageSize, null, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(pageSize, result.Data.Count);
        Assert.Equal(15, result.TotalCount);
        Assert.Equal(3, result.TotalPages);
        Assert.Equal(pageNumber, result.CurrentPage);
        Assert.Equal(pageSize, result.PageSize);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnEmptyResults_WhenNoDataExists()
    {
        // Arrange
        var pageNumber = 1;
        var pageSize = 5;

        // Act
        var result = await _repository.PaginatedListAllAsync(pageNumber, pageSize, null, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.Data);
        Assert.Equal(0, result.TotalCount);
        Assert.Equal(0, result.TotalPages);
        Assert.Equal(pageNumber, result.CurrentPage);
        Assert.Equal(pageSize, result.PageSize);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnCorrectPage_WhenRequestingSecondPage()
    {
        // Arrange
        var options = _fixture.CreateRsyncOptionsForPagination(10);
        _dbContext.RsyncOptions.AddRange(options);
        await _dbContext.SaveChangesAsync();

        var pageNumber = 2;
        var pageSize = 3;

        // Act
        var result = await _repository.PaginatedListAllAsync(pageNumber, pageSize, null, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(pageSize, result.Data.Count);
        Assert.Equal(10, result.TotalCount);
        Assert.Equal(4, result.TotalPages);
        Assert.Equal(pageNumber, result.CurrentPage);
        Assert.Equal(pageSize, result.PageSize);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnOnlySelectedProperties()
    {
        // Arrange
        var option = _fixture.CreateRsyncOptionWithProperties(
            name: "Test Option",
            replicationType: "Rsync",
            properties: "{\"test\":\"value\"}"
        );
        _dbContext.RsyncOptions.Add(option);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, null, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.Data);
        
        var returnedOption = result.Data.First();
        Assert.Equal(option.Id, returnedOption.Id);
        Assert.Equal(option.ReferenceId, returnedOption.ReferenceId);
        Assert.Equal(option.Name, returnedOption.Name);
        Assert.Equal(option.ReplicationType, returnedOption.ReplicationType);
        Assert.Equal(option.Properties, returnedOption.Properties);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddRsyncOption_WhenValidEntity()
    {
        // Arrange
        var rsyncOption = _fixture.RsyncOptionDto;
        rsyncOption.Name = "Test Rsync Option";
        rsyncOption.ReplicationType = "Rsync";
        rsyncOption.Properties = "{\"archive\":true,\"verbose\":true,\"compress\":true}";

        // Act
        var result = await _repository.AddAsync(rsyncOption);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(rsyncOption.Name, result.Name);
        Assert.Equal(rsyncOption.ReplicationType, result.ReplicationType);
        Assert.Equal(rsyncOption.Properties, result.Properties);
        Assert.Single(_dbContext.RsyncOptions);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var rsyncOption = _fixture.RsyncOptionDto;
        _dbContext.RsyncOptions.Add(rsyncOption);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(rsyncOption.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(rsyncOption.Id, result.Id);
        Assert.Equal(rsyncOption.Name, result.Name);
        Assert.Equal(rsyncOption.ReplicationType, result.ReplicationType);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var rsyncOption = _fixture.RsyncOptionDto;
        _dbContext.RsyncOptions.Add(rsyncOption);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(rsyncOption.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(rsyncOption.ReferenceId, result.ReferenceId);
        Assert.Equal(rsyncOption.Name, result.Name);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity_WhenExists()
    {
        // Arrange
        var rsyncOption = _fixture.RsyncOptionDto;
        _dbContext.RsyncOptions.Add(rsyncOption);
        await _dbContext.SaveChangesAsync();

        var updatedName = "Updated Rsync Option";
        var updatedReplicationType = "RoboCopy";
        rsyncOption.Name = updatedName;
        rsyncOption.ReplicationType = updatedReplicationType;

        // Act
        var result = await _repository.UpdateAsync(rsyncOption);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(updatedName, result.Name);
        Assert.Equal(updatedReplicationType, result.ReplicationType);
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity_WhenExists()
    {
        // Arrange
        var rsyncOption = _fixture.RsyncOptionDto;
        _dbContext.RsyncOptions.Add(rsyncOption);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(rsyncOption);

        // Assert
        var deletedEntity = await _repository.GetByIdAsync(rsyncOption.Id);
        Assert.Null(deletedEntity);
    }

    [Theory]
    [InlineData("Rsync")]
    [InlineData("RoboCopy")]
    [InlineData("DataSync")]
    [InlineData("FastCopy")]
    public async Task Repository_ShouldHandleDifferentReplicationTypes(string replicationType)
    {
        // Arrange
        var rsyncOption = _fixture.CreateRsyncOptionWithReplicationType(replicationType);

        // Act
        var result = await _repository.AddAsync(rsyncOption);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(replicationType, result.ReplicationType);
    }

    [Fact]
    public async Task Repository_ShouldHandleSpecialCharacters()
    {
        // Arrange
        var rsyncOption = _fixture.CreateRsyncOptionWithSpecialCharacters();

        // Act
        var result = await _repository.AddAsync(rsyncOption);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(rsyncOption.Name, result.Name);
        Assert.Equal(rsyncOption.ReplicationType, result.ReplicationType);
        Assert.Equal(rsyncOption.Properties, result.Properties);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.RsyncOptions.RemoveRange(_dbContext.RsyncOptions);
        await _dbContext.SaveChangesAsync();
    }
}
