﻿using ContinuityPatrol.Application.Features.MonitorService.Event.UpdateStatus;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.MonitorService.Command.UpdateStatus;

public class
    UpdateMonitorServiceStatusCommandHandler : IRequestHandler<UpdateMonitorServiceStatusCommand,
        UpdateMonitorServiceStatusResponse>
{
    private readonly IJobScheduler _client;
    private readonly IMapper _mapper;
    private readonly IMonitorServiceRepository _monitorServiceRepository;
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;
    private readonly IPublisher _publisher;
    private readonly IWindowsService _windowsService;

    public UpdateMonitorServiceStatusCommandHandler(IMapper mapper, IMonitorServiceRepository monitorServiceRepository,
        ILoadBalancerRepository nodeConfigurationRepository, IWindowsService windowsService, IPublisher publisher, IJobScheduler client)
    {
        _mapper = mapper;
        _monitorServiceRepository = monitorServiceRepository;
        _nodeConfigurationRepository = nodeConfigurationRepository;
        _windowsService = windowsService;
        _publisher = publisher;
        _client = client;
    }

    public async Task<UpdateMonitorServiceStatusResponse> Handle(UpdateMonitorServiceStatusCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _monitorServiceRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.MonitorService), request.Id);

        eventToUpdate.Status = request.Status;

        eventToUpdate.IsServiceUpdate = request.IsServiceUpdate;

        var nodeConfig =
            await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString())
            ?? await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(),
                ServiceType.LoadBalancer.ToString());

        if (nodeConfig is null) throw new InvalidException("LoadBalancer not configured!.");

        var baseUrl = $"{nodeConfig.ConnectionType}://{nodeConfig.IPAddress}:{nodeConfig.Port}";

        var url = UrlHelper.GenerateMonitorServiceUrl(baseUrl, eventToUpdate.ReferenceId);

        var monitorResponse = await _windowsService.CheckWindowsService(url);

        if (!monitorResponse.Success) throw new WindowServiceException(monitorResponse.InActiveNodes, ServiceType.Monitor.ToString(), monitorResponse.Message);
        
        await _client.ScheduleJob(eventToUpdate.ReferenceId, new Dictionary<string, string> { ["url"] = url });

        _mapper.Map(eventToUpdate, typeof(UpdateMonitorServiceStatusCommand), typeof(Domain.Entities.MonitorService));

        await _monitorServiceRepository.UpdateAsync(eventToUpdate);

        await _publisher.Publish(
            new MonitorServiceStatusUpdatedEvent
            { ServerName = eventToUpdate.ServerName, Status = eventToUpdate.Status }, cancellationToken);

        return new UpdateMonitorServiceStatusResponse
        {
            Message = $"Monitor service '{eventToUpdate.ServerName}' Status '{eventToUpdate.Status}' successfully",

            Id = eventToUpdate.ReferenceId
        };
    }
}