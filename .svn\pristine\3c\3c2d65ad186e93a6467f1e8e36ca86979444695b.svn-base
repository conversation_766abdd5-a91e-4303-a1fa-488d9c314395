﻿
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title">
            <i class="cp-monitoring"></i><span>Detailed Infra Object Monitoring view – Veritas Volume Replicator</span>
        </h6>
        <span><i class="cp-"></i>Last Monitored Time : 12/9/2022 1:39:31 PM</span>
    </div>
    <div class="monitor_pages">
        <div class="row g-2 mt-0">
            <div class="col d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">INV_UNOINV</div>
                    <div class="card-body pt-0">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th class="text-truncate">Component</th>
                                    <th class="text-truncate text-primary">Primary</th>
                                    <th class="text-truncate">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-server me-1 fs-5"></i>MSSQL Server ...
                                    </td>
                                    <td class="text-truncate" >Inv_DB_...</td>
                                    <td class="text-truncate" >Inv_DB_...</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-database me-1 fs-5"></i>MSSQL Database...
                                    </td>
                                    <td class="text-truncate" >Unoinv</td>
                                    <td class="text-truncate" >Unoinv</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> <i class="text-secondary cp-mysql-data me-1 fs-5"></i>MSSQL Database...</td>
                                    <td class="text-truncate" >Online</td>
                                    <td class="text-truncate" >
                                        Online
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-open-lock me-1 fs-5"></i>Database Restrict...
                                    </td>
                                    <td class="text-truncate" >Multi_User</td>
                                    <td class="text-truncate" >
                                        NA
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
      
            <div class="col d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Veritas Volume Replicator (VVR) Replication Component</div>
                    <div class="card-body pt-0">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th class="text-truncate">Replication Component</th>
                                    <th class="text-truncate text-primary">Primary</th>
                                    <th class="text-truncate">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary  cp-database-warning me-1 fs-5"></i>VVR Server-IP...
                                    </td>
                                    <td class="text-truncate" >************</td>
                                    <td class="text-truncate" >************</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> <i class="text-secondary cp-time me-1 fs-5"></i>Disk Group Name</td>
                                    <td class="text-truncate" >DG_PR</td>
                                    <td class="text-truncate" >DG_PR</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-folder-server me-1 fs-5"></i>Replicated Volume...
                                    </td>
                                    <td class="text-truncate" >RVG</td>
                                    <td class="text-truncate" >RVG</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> <i class="text-secondary cp-control-file-name me-1 fs-5"></i>Rlink Name</td>
                                    <td class="text-truncate" ></td>
                                    <td class="text-truncate" ></td>
                                </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
            </div>

            <div class="col d-grid">
                <div class="row g-2">
                    <div class="col d-grid">
                        <div class="card Card_Design_None mb-2">
                            <div class="card-header card-title">Solution Diagram</div>
                            <div class="card-body text-center">
                                <div id="Solution_Diagram"></div>
                            </div>
                        </div>

                    </div>

                </div>

            </div>
        </div>
        <div class="row  g-2">
            <div class="col d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Replicated Volume Group (RVG) Monitoring - Summary</div>
                    <div class="card-body pt-0">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th class="text-truncate">Monitoring Component</th>
                                    <th class="text-truncate text-primary">Primary</th>
                                    <th class="text-truncate">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-rds-name me-1 fs-5"></i>RDS Name
                                    </td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-host-name me-1 fs-5"></i>Host Name
                                    </td>
                                    <td class="text-truncate" >************</td>
                                    <td class="text-truncate" >************</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> <i class="text-secondary cp-rvg-name me-1 fs-5"></i>RVG Name </td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >
                                        NA
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-Storage-chain me-1 fs-5"></i>Disk Group Name
                                    </td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >
                                        NA
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-speed-meter me-1 fs-5"></i>Data Volume Count
                                    </td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >
                                        NA
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-open-lock me-1 fs-5"></i>SRL
                                    </td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >
                                        NA
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">RLINKs Monitoring - Secondary Update</div>
                    <div class="card-body pt-0">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th class="text-truncate">Monitoring Component</th>
                                    <th class="text-truncate text-primary">Primary</th>
                                    <th class="text-truncate">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-server me-1 fs-5"></i>Disk Group Name
                                    </td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-link-on me-1 fs-5"></i>RLINKs Name
                                    </td>
                                    <td class="text-truncate" >************</td>
                                    <td class="text-truncate" >************</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> <i class="text-secondary cp-update me-1 fs-5"></i>Last Update (ID)</td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-update me-1 fs-5"></i>Last Update Time
                                    </td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> <i class="text-secondary cp-data-lag me-1 fs-5"></i>DataLag (Hrs:Mins:Secs)</td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >NA</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="row g-2 ">
            <div class="col d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">RLINKs Monitoring - Summary</div>
                    <div class="card-body pt-0">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th class="text-truncate">Monitoring Component</th>
                                    <th class="text-truncate text-primary">Primary</th>
                                    <th class="text-truncate">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-link-on me-1 fs-5"></i>RLINKs Name
                                    </td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary  cp-link-on me-1 fs-5"></i>RLINK State
                                    </td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> <i class="text-secondary cp-last-copied-transaction me-1 fs-5"></i>Synchronous Mode</td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >
                                        NA
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Synchronous Mode</div>
                    <div class="card-body pt-0">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th class="text-truncate">Monitoring Component</th>
                                    <th class="text-truncate text-primary">Primary</th>
                                    <th class="text-truncate">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-signal me-1 fs-5"></i>No.Of Messages Transmitted
                                    </td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-signal me-1 fs-5"></i>No.Of Blocks Transmitted
                                    </td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >NA</td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="row g-2">
            <div class="col d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Error Information</div>
                    <div class="card-body pt-0">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th class="text-truncate">Monitoring Component</th>
                                    <th class="text-truncate text-primary">Primary</th>
                                    <th class="text-truncate">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-database-warning me-1 fs-5"></i>Stream Errors
                                    </td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-database-warning me-1 fs-5"></i>Memory Errors
                                    </td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >NA</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">RLINKs Monitoring - Replication Performance</div>
                    <div class="card-body pt-0">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th class="text-truncate">Monitoring Component</th>
                                    <th class="text-truncate text-primary">Primary</th>
                                    <th class="text-truncate">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"> 
                                        <i class="text-secondary cp-apply-finish-time me-1 fs-5"></i>Last Check Time
                                    </td>
                                    <td class="text-truncate" >NA</td>
                                    <td class="text-truncate" >NA</td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/js/monitoring/veritasvolumereplicator.js"></script>
