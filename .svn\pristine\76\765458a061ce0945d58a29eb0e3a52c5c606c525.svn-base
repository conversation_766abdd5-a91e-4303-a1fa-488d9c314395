using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ReportScheduleExecutionRepositoryTests : IClassFixture<ReportScheduleExecutionFixture>, IDisposable
{
    private readonly ReportScheduleExecutionFixture _reportScheduleExecutionFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ReportScheduleExecutionRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public ReportScheduleExecutionRepositoryTests(ReportScheduleExecutionFixture reportScheduleExecutionFixture)
    {
        _reportScheduleExecutionFixture = reportScheduleExecutionFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _repository = new ReportScheduleExecutionRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        var existingExecutions = await _dbContext.Set<ReportScheduleExecution>().ToListAsync();
        _dbContext.Set<ReportScheduleExecution>().RemoveRange(existingExecutions);
        await _dbContext.SaveChangesAsync();
    }

    #region GetReportSchedulerExecutionByReportId Tests

    [Fact]
    public async Task GetReportSchedulerExecutionByReportId_ShouldReturnMatchingExecutions()
    {
        // Arrange
        await ClearDatabase();
        var reportId = "REPORT_123";

        var executions = new List<ReportScheduleExecution>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                ReportId = reportId,
                ReportName = "Test Report 1",
                ReportType = "Type1",
                Users = "User1,User2",
                ScheduleDateTime = "2024-01-01 10:00:00",
                IsActive = true
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                ReportId = reportId,
                ReportName = "Test Report 2",
                ReportType = "Type2",
                Users = "User3,User4",
                ScheduleDateTime = "2024-01-02 11:00:00",
                IsActive = true
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                ReportId = "DIFFERENT_REPORT",
                ReportName = "Different Report",
                ReportType = "Type3",
                Users = "User5",
                ScheduleDateTime = "2024-01-03 12:00:00",
                IsActive = true
            }
        };

        foreach (var execution in executions)
        {
            await _repository.AddAsync(execution);
        }

        // Act
        var result = await _repository.GetReportSchedulerExecutionByReportId(reportId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, e => Assert.Equal(reportId, e.ReportId));
        Assert.Contains(result, e => e.ReportName == "Test Report 1");
        Assert.Contains(result, e => e.ReportName == "Test Report 2");
        Assert.DoesNotContain(result, e => e.ReportName == "Different Report");
    }

    [Fact]
    public async Task GetReportSchedulerExecutionByReportId_ShouldReturnEmptyList_WhenNoMatchingReportId()
    {
        // Arrange
        await ClearDatabase();

        var executions = new List<ReportScheduleExecution>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                ReportId = "REPORT_123",
                ReportName = "Test Report 1",
                ReportType = "Type1",
                Users = "User1",
                ScheduleDateTime = "2024-01-01 10:00:00",
                IsActive = true
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                ReportId = "REPORT_456",
                ReportName = "Test Report 2",
                ReportType = "Type2",
                Users = "User2",
                ScheduleDateTime = "2024-01-02 11:00:00",
                IsActive = true
            }
        };

        foreach (var execution in executions)
        {
            await _repository.AddAsync(execution);
        }

        // Act
        var result = await _repository.GetReportSchedulerExecutionByReportId("NON_EXISTENT_REPORT");

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetReportSchedulerExecutionByReportId_ShouldReturnAllMatchingExecutions_IncludingInactive()
    {
        // Arrange
        await ClearDatabase();
        var reportId = "REPORT_123";

        var executions = new List<ReportScheduleExecution>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                ReportId = reportId,
                ReportName = "Active Execution",
                ReportType = "Type1",
                Users = "User1",
                ScheduleDateTime = "2024-01-01 10:00:00",
                IsActive = true
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(),
                ReportId = reportId,
                ReportName = "Inactive Execution",
                ReportType = "Type2",
                Users = "User2",
                ScheduleDateTime = "2024-01-02 11:00:00",
                IsActive = false
            }
        };

        foreach (var execution in executions)
        {
            await _dbContext.ReportSchedulesExecution.AddAsync(execution);
            _dbContext.SaveChanges();
        }

        // Act
        var result = await _repository.GetReportSchedulerExecutionByReportId(reportId);

        // Assert
        Assert.Equal(1, result.Count);
        Assert.Contains(result, e => e.ReportName == "Active Execution" && e.IsActive);
    }

    [Fact]
    public async Task GetReportSchedulerExecutionByReportId_ShouldHandleNullReportId()
    {
        // Arrange
        await ClearDatabase();

        var execution = new ReportScheduleExecution
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportId = "REPORT_123",
            ReportName = "Test Report",
            ReportType = "Type1",
            Users = "User1",
            ScheduleDateTime = "2024-01-01 10:00:00",
            IsActive = true
        };

        await _repository.AddAsync(execution);

        // Act
        var result = await _repository.GetReportSchedulerExecutionByReportId(null);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetReportSchedulerExecutionByReportId_ShouldHandleEmptyReportId()
    {
        // Arrange
        await ClearDatabase();

        var execution = new ReportScheduleExecution
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportId = "REPORT_123",
            ReportName = "Test Report",
            ReportType = "Type1",
            Users = "User1",
            ScheduleDateTime = "2024-01-01 10:00:00",
            IsActive = true
        };

        await _repository.AddAsync(execution);

        // Act
        var result = await _repository.GetReportSchedulerExecutionByReportId("");

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetReportSchedulerExecutionByReportId_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var reportId = "Report_123";

        var execution = new ReportScheduleExecution
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportId = reportId,
            ReportName = "Test Report",
            ReportType = "Type1",
            Users = "User1",
            ScheduleDateTime = "2024-01-01 10:00:00",
            IsActive = true
        };

        await _repository.AddAsync(execution);

        // Act
        var result1 = await _repository.GetReportSchedulerExecutionByReportId("Report_123");
        var result2 = await _repository.GetReportSchedulerExecutionByReportId("report_123");
        var result3 = await _repository.GetReportSchedulerExecutionByReportId("REPORT_123");

        // Assert
        Assert.Single(result1);  // Exact match should return result
        Assert.Empty(result2);   // Different case should return empty
        Assert.Empty(result3);   // Different case should return empty
    }

    [Fact]
    public async Task GetReportSchedulerExecutionByReportId_ShouldReturnCompleteEntityData()
    {
        // Arrange
        await ClearDatabase();
        var reportId = "REPORT_123";

        var execution = new ReportScheduleExecution
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportId = reportId,
            ReportName = "Complete Test Report",
            ReportType = "CompleteType",
            Users = "User1,User2,User3",
            ScheduleDateTime = "2024-01-01 10:30:45",
            IsActive = true,
            CreatedBy = "TestUser",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TestUser",
            LastModifiedDate = DateTime.UtcNow
        };

        await _repository.AddAsync(execution);

        // Act
        var result = await _repository.GetReportSchedulerExecutionByReportId(reportId);

        // Assert
        Assert.Single(result);
        var returnedExecution = result[0];
        Assert.Equal(execution.ReferenceId, returnedExecution.ReferenceId);
        Assert.Equal(execution.ReportId, returnedExecution.ReportId);
        Assert.Equal(execution.ReportName, returnedExecution.ReportName);
        Assert.Equal(execution.ReportType, returnedExecution.ReportType);
        Assert.Equal(execution.Users, returnedExecution.Users);
        Assert.Equal(execution.ScheduleDateTime, returnedExecution.ScheduleDateTime);
        Assert.Equal(execution.IsActive, returnedExecution.IsActive);
        Assert.Equal(execution.CreatedBy, returnedExecution.CreatedBy);
        Assert.Equal(execution.LastModifiedBy, returnedExecution.LastModifiedBy);
    }

    [Fact]
    public async Task GetReportSchedulerExecutionByReportId_ShouldHandleLargeDataset()
    {
        // Arrange
        await ClearDatabase();
        var reportId = "REPORT_LARGE";

        var executions = new List<ReportScheduleExecution>();
        for (int i = 1; i <= 100; i++)
        {
            executions.Add(new ReportScheduleExecution
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportId = reportId,
                ReportName = $"Test Report {i}",
                ReportType = $"Type{i % 5}",
                Users = $"User{i}",
                ScheduleDateTime = $"2024-01-{i:D2} 10:00:00",
                IsActive = i % 2 == 0 // Alternate between active and inactive
            });
        }

        foreach (var execution in executions)
        {
            await _dbContext.ReportSchedulesExecution.AddAsync(execution);
            _dbContext.SaveChanges();
        }

        // Act
        var result = await _repository.GetReportSchedulerExecutionByReportId(reportId);

        // Assert
        Assert.Equal(50, result.Count);
        Assert.All(result, e => Assert.Equal(reportId, e.ReportId));

    }

    [Fact]
    public async Task GetReportSchedulerExecutionByReportId_ShouldHandleSpecialCharactersInReportId()
    {
        // Arrange
        await ClearDatabase();
        var specialReportIds = new List<string>
        {
            "Report@123",
            "Report#456",
            "Report$789",
            "Report%ABC",
            "Report&DEF",
            "Report with spaces",
            "Report-with-dashes",
            "Report_with_underscores"
        };

        foreach (var reportId in specialReportIds)
        {
            var execution = new ReportScheduleExecution
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportId = reportId,
                ReportName = $"Report for {reportId}",
                ReportType = "SpecialType",
                Users = "SpecialUser",
                ScheduleDateTime = "2024-01-01 10:00:00",
                IsActive = true
            };

            await _repository.AddAsync(execution);
        }

        // Act & Assert
        foreach (var reportId in specialReportIds)
        {
            var result = await _repository.GetReportSchedulerExecutionByReportId(reportId);
            Assert.Single(result);
            Assert.Equal(reportId, result[0].ReportId);
        }
    }

    #endregion

    #region Additional GetReportSchedulerExecutionByReportId Tests

    [Fact]
    public async Task GetReportSchedulerExecutionByReportId_ShouldReturnMultipleExecutions_WhenMultipleExist()
    {
        // Arrange
        await ClearDatabase();
        var reportId = "REPORT_123";
        var executions = new List<ReportScheduleExecution>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportId = reportId,
                ReportName = "Success Report",
                ScheduleDateTime = DateTime.Now.AddDays(-1).ToString(),
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportId = reportId,
                ReportName = "Failed Report",
                ScheduleDateTime = DateTime.Now.AddDays(-2).ToString(),
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportId = reportId,
                ReportName = "Running Report",
                ScheduleDateTime = DateTime.Now.ToString(),
                IsActive = false
            }
        };

        foreach (var execution in executions)
        {
            await _dbContext.ReportSchedulesExecution.AddAsync(execution);
            _dbContext.SaveChanges();
        }

        // Act
        var result = await _repository.GetReportSchedulerExecutionByReportId(reportId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.Equal(reportId, r.ReportId));
        Assert.Contains(result, r => r.ReportName == "Success Report");
        Assert.Contains(result, r => r.ReportName == "Failed Report");
    }

    [Fact]
    public async Task GetReportSchedulerExecutionByReportId_ShouldIncludeInactiveExecutions()
    {
        // Arrange
        await ClearDatabase();
        var reportId = "REPORT_123";
        var executions = new List<ReportScheduleExecution>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportId = reportId,
                ReportName = "Active Report",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportId = reportId,
                ReportName = "Inactive Report",
                IsActive = false
            }
        };

        foreach (var execution in executions)
        {
            await _dbContext.ReportSchedulesExecution.AddAsync(execution);
            _dbContext.SaveChanges();
        }

        // Act
        var result = await _repository.GetReportSchedulerExecutionByReportId(reportId);

        // Assert
        Assert.Equal(1, result.Count);
        Assert.Contains(result, r => r.ReportName == "Active Report" && r.IsActive);
    }

    [Fact]
    public async Task GetReportSchedulerExecutionByReportId_ShouldReturnCompleteEntityDataWithAllFields()
    {
        // Arrange
        await ClearDatabase();
        var reportId = "REPORT_123";
        var execution = new ReportScheduleExecution
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportId = reportId,
            ReportName = "Complete Test Report",
            ReportType = "DetailedType",
            Users = "TestUser",
            ScheduleDateTime = "2024-01-01 10:00:00",
            IsActive = true
        };

        await _repository.AddAsync(execution);

        // Act
        var result = await _repository.GetReportSchedulerExecutionByReportId(reportId);

        // Assert
        Assert.Single(result);
        var returnedExecution = result[0];
        Assert.Equal(execution.ReferenceId, returnedExecution.ReferenceId);
        Assert.Equal(execution.ReportId, returnedExecution.ReportId);
        Assert.Equal(execution.ReportName, returnedExecution.ReportName);
        Assert.Equal(execution.ReportType, returnedExecution.ReportType);
        Assert.Equal(execution.Users, returnedExecution.Users);
        Assert.Equal(execution.ScheduleDateTime, returnedExecution.ScheduleDateTime);
        Assert.Equal(execution.IsActive, returnedExecution.IsActive);
    }

    [Fact]
    public async Task GetReportSchedulerExecutionByReportId_ShouldHandleLargeDatasetEfficiently()
    {
        // Arrange
        await ClearDatabase();
        var reportId = "REPORT_123";
        var executions = new List<ReportScheduleExecution>();

        // Create 100 executions
        for (int i = 0; i < 100; i++)
        {
            executions.Add(new ReportScheduleExecution
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportId = reportId,
                ReportName = $"Report_{i}",
                ReportType = $"Type_{i}",
                Users = $"User_{i}",
                ScheduleDateTime = $"2024-01-{(i % 28) + 1:D2} 10:00:00",
                IsActive = i % 2 == 0 // Alternate between active and inactive
            });
        }

        foreach (var execution in executions)
        {
            await _repository.AddAsync(execution);
        }

        // Act
        var result = await _repository.GetReportSchedulerExecutionByReportId(reportId);

        // Assert
        Assert.Equal(100, result.Count);
        Assert.All(result, r => Assert.Equal(reportId, r.ReportId));
    }

    [Fact]
    public async Task GetReportSchedulerExecutionByReportId_ShouldHandleSpecialCharactersInReportIdProperly()
    {
        // Arrange
        await ClearDatabase();
        var reportId = "REPORT_@#$%^&*()_+{}|:<>?[]\\;',./";
        var execution = new ReportScheduleExecution
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportId = reportId,
            ReportName = "Special Characters Report",
            ReportType = "SpecialType",
            Users = "SpecialUser",
            ScheduleDateTime = "2024-01-01 10:00:00",
            IsActive = true
        };

        await _repository.AddAsync(execution);

        // Act
        var result = await _repository.GetReportSchedulerExecutionByReportId(reportId);

        // Assert
        Assert.Single(result);
        Assert.Equal(reportId, result[0].ReportId);
    }

    [Fact]
    public async Task GetReportSchedulerExecutionByReportId_ShouldReturnEmptyList_WhenDatabaseIsCompletelyEmpty()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetReportSchedulerExecutionByReportId("ANY_REPORT_ID");

        // Assert
        Assert.Empty(result);
    }

    #endregion
}
