using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DriftJobFixture : IDisposable
{
    public List<DriftJob> DriftJobPaginationList { get; set; }
    public List<DriftJob> DriftJobList { get; set; }
    public DriftJob DriftJobDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string SolutionTypeId = "ST_123";
    public const string NodeId = "NODE_123";

    public ApplicationDbContext DbContext { get; private set; }

    public DriftJobFixture()
    {
        var fixture = new Fixture();

        DriftJobList = fixture.Create<List<DriftJob>>();

        DriftJobPaginationList = fixture.CreateMany<DriftJob>(20).ToList();

        DriftJobPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftJobPaginationList.ForEach(x => x.IsActive = true);
        DriftJobPaginationList.ForEach(x => x.SolutionTypeId = SolutionTypeId);
        DriftJobPaginationList.ForEach(x => x.NodeId = NodeId);

        DriftJobList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftJobList.ForEach(x => x.IsActive = true);
        DriftJobList.ForEach(x => x.SolutionTypeId = SolutionTypeId);
        DriftJobList.ForEach(x => x.NodeId = NodeId);

        DriftJobDto = fixture.Create<DriftJob>();
        DriftJobDto.ReferenceId = Guid.NewGuid().ToString();
        DriftJobDto.IsActive = true;
        DriftJobDto.SolutionTypeId = SolutionTypeId;
        DriftJobDto.NodeId = NodeId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
