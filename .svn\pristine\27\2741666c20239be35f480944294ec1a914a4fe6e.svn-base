using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SolutionTypeTablesFixture : IDisposable
{
    public List<SolutionTypeTables> SolutionTypeTablesPaginationList { get; set; }
    public List<SolutionTypeTables> SolutionTypeTablesList { get; set; }
    public SolutionTypeTables SolutionTypeTablesDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SolutionTypeTablesFixture()
    {
        var fixture = new Fixture();

        SolutionTypeTablesList = fixture.Create<List<SolutionTypeTables>>();

        SolutionTypeTablesPaginationList = fixture.CreateMany<SolutionTypeTables>(20).ToList();

        SolutionTypeTablesDto = fixture.Create<SolutionTypeTables>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
