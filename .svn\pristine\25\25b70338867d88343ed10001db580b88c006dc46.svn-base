﻿using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Domain.ViewModels.UserActivityModel;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserListModels;
using ContinuityPatrol.Domain.ViewModels.UserRoleModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel;
using ContinuityPatrol.Shared.Core.Wrapper;


namespace ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels;

public class UserViewModel
{
    public string Id { get; set; }
    public string LoginType { get; set; }
    public string CompanyId { get; set; }
    public string CompanyName { get; set; }
    public string LoginName { get; set; }
    public string LoginPassword { get; set; }
    public string ConfirmPassword { get; set; }
    public string EncryptPassword { get; set; }
    public string Role { get; set; }
    public string RoleName { get; set; }
    public bool IsLock { get; set; }
    public bool IsGroup { get; set; }
    public bool IsReset { get; set; }
    public bool InfraObjectAllFlag { get; set; }
    public int SessionTimeout { get; set; }
    public bool IsVerify { get; set; }
    public string TwoFactorAuthentication { get; set; }
    public string Url { get; set; }

    public bool IsDefaultDashboard { get; set; }
    public virtual UserInfoCommand UserInfoCommand { get; set; }
    public virtual GetByRoleIdVm GetByRoleIdVm { get; set; }
    public List<UserActivityLoginNameVm> GetUserActivityLoginNameVms { get; set; }
    public List<UserActivityListVm> GetStartTimeEndTimeByUser { get; set; }
    public List<WorkflowListVm> GetWorkflowList { get; set; }
    public List<WorkflowOperationGroupListVm> GetWorkflowOperationGroupList { get; set; }
    public virtual UserInfraObjectCommand UserInfraObjectCommand { get; set; }
    public UserResetViewModal userResetViewModal { get; set; }
    public UserUnLockModel UserUnLockModel { get; set; }
    public PaginatedResult<UserListVm> PaginatedUsers { get; set; }
    public PaginatedResult<UserRoleListVm> PaginatedUserRole { get; set; }

    public List<CompanyNameVm> Companies { get; set; }

    [ValidateNever] public List<string> Domains { get; set; }
}

public class UserResetViewModal
{
    public string UserId { get; set; }
    public string LoginName { get; set; }
    public string Email { get; set; }
    public string Password { get; set; }
    public string NewPassword { get; set; }
}

public class UserUnLockModel
{
    public string UserId { get; set; }
    public bool IsLock { get; set; }
}

public class GetByRoleIdVm
{
    public string Id { get; set; }

    public string UserId { get; set; }

    public string UserName { get; set; }

    public string RoleId { get; set; }

    public string RoleName { get; set; }

    public string Properties { get; set; }
}