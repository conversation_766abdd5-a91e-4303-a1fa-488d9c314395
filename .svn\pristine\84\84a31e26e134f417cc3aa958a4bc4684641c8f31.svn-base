﻿namespace ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetLastDrillDetails;

public class LastDrillDetailQueryHandler : IRequestHandler<LastDrillDetailQuery, LastDrillDetailVm>
{
    private readonly IWorkflowOperationRepository _workflowOperationRepository;
    public LastDrillDetailQueryHandler(IWorkflowOperationRepository workflowOperationRepository)
    {
        _workflowOperationRepository = workflowOperationRepository;
    }
    public async Task<LastDrillDetailVm> Handle(LastDrillDetailQuery request, CancellationToken cancellationToken)
    {
        var workflowOperation = await _workflowOperationRepository.GetLastWorkflowOperation();
        
        if (workflowOperation == null) return new LastDrillDetailVm();

        var lastDrillDetails = new LastDrillDetailVm
        {
            ProfileName = workflowOperation.ProfileName,
            Status = workflowOperation.Status,
            LastExecutionTime = workflowOperation.EndTime
        };

        var duration = workflowOperation.EndTime - workflowOperation.StartTime;

        lastDrillDetails.Duration = duration.TotalDays >= 1 ? $"{(int)duration.TotalDays}d {duration.Hours:D2}:{duration.Minutes:D2}:{duration.Seconds:D2}" : $"{duration.Hours:D2}:{duration.Minutes:D2}:{duration.Seconds:D2}";

        return lastDrillDetails;
    }
}
