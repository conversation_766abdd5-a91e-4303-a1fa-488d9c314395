﻿const operationTypeURL = {
    getPagination: "/Admin/OperationType/GetPagination",
    nameExists: "Admin/OperationType/IsWorkflowActionTypeExist",
    delete: "Admin/OperationType/Delete",
    createOrUpdate: "Admin/OperationType/CreateOrUpdate"
};
let globalComponetId = '';
let dataTable = "";
let selectedValues = [];

$(async function () {
    const createPermission = $("#ConfigurationCreate").data("create-permission").toLowerCase();
    const deletePermission = $("#ConfigurationDelete").data("delete-permission").toLowerCase();

    dataTable = $('#operationTypeTable').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
            },
            infoFiltered: ""
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        Sortable: true,
        processing: true,
        serverSide: true,
        filter: true,
        order: [],
        ajax: {
            type: "GET",
            url: operationTypeURL.getPagination,
            dataType: "json",
            data: d => {
                const sortIndex = d?.order[0]?.column || '';
                const sortValue = sortIndex === 1 ? "actionType" : "";
                const orderValue = d?.order[0]?.dir || 'asc';
                d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                d.pageSize = d?.length;
                d.searchString = selectedValues?.length === 0 ? $('#searchInputOT').val() : selectedValues?.join(';');
                d.sortColumn = sortValue;
                d.SortOrder = orderValue;
                selectedValues.length = 0;
            },
            dataSrc: json => {
                json.recordsTotal = json?.totalPages;
                json.recordsFiltered = json?.totalCount;
                $(".pagination-column").toggleClass("disabled", json?.data?.length === 0);
                return json?.data;
            }
        },
        columnDefs: [
            { targets: [1, 2], className: "truncate" }
        ],
        columns: [
            {
                data: null,
                name: "Sr. No.",
                autoWidth: true,
                orderable: false,
                render: (data, type, row, meta) => type === 'display' ? meta?.row + 1 : data
            },
            {
                data: "actionType",
                name: "Action Type",
                autoWidth: true,
                render: (data, type) => type === 'display' ? `<span title="${data}">${data}</span>` : data
            },
            {
                orderable: false,
                render: (data, type, row) => `
                    <div class="d-flex align-items-center gap-2">
                        <span role="button" title="Edit" class="editButtonOT ${createPermission === 'true' ? '' : 'icon-disabled'}" details='${JSON.stringify(row)}'>
                            <i class="cp-edit"></i>
                        </span>
                        <span role="button" title="Delete" class="deleteButtonOT ${deletePermission === 'true' ? '' : 'icon-disabled'}" data-id="${row?.id}" data-name="${row?.actionType}">
                            <i class="cp-Delete"></i>
                        </span>
                    </div>`
            }
        ],
        rowCallback: (row, data, index) => {
            const startIndex = dataTable?.context[0]?._iDisplayStart;
            $('td:eq(0)', row).html(startIndex + index + 1);
        },
        initComplete: () => {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
        }
    });

    dataTable.on('draw.dt', () => {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#searchInputOT').on('keydown', e => {
        if (e.key === '=' || e.key === 'Enter') e.preventDefault();
    });

    $('#searchInputOT').on('input', commonDebounce(function () {
        const actionType = $("#nameOT");
        let val = $(this).val()?.trim()?.replace(/\s+/g, ' ');
        $(this).val(val);

        if (actionType.is(':checked')) {
            selectedValues.push(actionType.val() + val);
        }

        dataTable.ajax.reload(json => {
            const msg = $('.dataTables_empty');
            if (val?.length === 0 && json?.data?.length === 0) msg.text('No Data Found');
            else if (json?.recordsFiltered === 0) msg.text('No matching records found');
        });
    }, 500));

    async function validateActionTypeName(value, id = null) {
        const errorElement = $('#actionTypeNameError');
        if (!value) {
            errorElement.text('Enter action type name').addClass('field-validation-error');
            return false;
        }
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed').addClass('field-validation-error');
            return false;
        }
        const url = RootUrl + operationTypeURL.nameExists;
        const data = { name: value, id };

        const validations = [
            //SpecialCharValidate(value),
            SpecialCharValidateCustom(value),
            ShouldNotBeginWithUnderScore(value),
            ShouldNotBeginWithSpace(value),
            OnlyNumericsValidate(value),
            ShouldNotBeginWithNumber(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            ShouldNotAllowMultipleSpace(value),
            SpaceAndUnderScoreRegex(value),
            minMaxlength(value),
            secondChar(value),
            await IsNameAlreadyExist(url, data, ' Name already exists')
        ];

        return CommonValidation(errorElement, validations);
    }

    const createOrUpdate = async () => {
        const data = {
            Id: $('#saveButtonOT').text() === 'Update' ? globalComponetId : '',
            ActionType: $('#actionTypeName').val(),
            IsDelete: true,
            __RequestVerificationToken: gettoken()
        };

        await $.ajax({
            type: "POST",
            url: RootUrl + operationTypeURL.createOrUpdate,
            data,
            dataType: "json",
            traditional: true,
            success: result => {
                if (result?.success && result?.data?.message) {
                    notificationAlert('success', result?.data?.message);
                    $('#createModal').modal('hide');
                    dataTable.ajax.reload();
                } else {
                    errorNotification(result);
                    $('#createModal').modal('hide');
                }
            }
        });
    };

    $('#createModalButton').on('click', () => {
        $('#actionTypeNameError').text('').removeClass('field-validation-error');
        $('#actionTypeName').val('');
        $('#saveButtonOT').text('Save');
        $('#createModal').modal('show');
        globalComponetId = "";
    });

    $('#saveButtonOT').on('click', async () => {
        const isValid = await validateActionTypeName($('#actionTypeName').val(), globalComponetId);
        if (isValid) createOrUpdate();
    });

    $('#actionTypeName').on('input', function () {
        const value = $(this).val()?.replace(/\s{2,}/g, ' ');
        $(this).val(value);
        validateActionTypeName(value, globalComponetId);
    });

    $(document).on('click', '.editButtonOT', function () {
        const data = JSON.parse($(this).attr('details'));
        $('#actionTypeName').val(data?.actionType);
        $('#actionTypeNameError').text('').removeClass('field-validation-error');
        $('#saveButtonOT').text('Update').attr('title', 'Update');
        globalComponetId = data?.id;
        setTimeout(() => $('#createModal').modal('show'), 300);
    });

    $(document).on('click', '.deleteButtonOT', function () {
        $('#workflowActionTypeId').val(this.dataset.id);
        $('#deleteData').text(this.dataset.name);
        $('#operationTypeDeleteModal').modal('show');
    });

    $('#confirmDeleteButtonOT').on('click', async () => {
        await $.ajax({
            type: 'DELETE',
            url: RootUrl + operationTypeURL.delete,
            data: { id: $('#workflowActionTypeId').val() },
            dataType: "json",
            success: result => {
                if (result?.success && result?.data?.message) {
                    notificationAlert('success', result?.data?.message);
                    $('#operationTypeDeleteModal').modal('hide');
                    dataTable.ajax.reload();
                } else {
                    errorNotification(result);
                    $('#operationTypeDeleteModal').modal('hide');
                }
            }
        });
    });
});