﻿namespace ContinuityPatrol.Application.Features.CyberComponent.Queries.GetBySiteId;

public class
    GetCyberComponentBySiteIdQueryHandler : IRequestHandler<GetCyberComponentBySiteIdQuery,
        List<CyberComponentBySiteIdVm>>
{
    private readonly ICyberComponentRepository _cyberComponentRepository;
    private readonly IMapper _mapper;

    public GetCyberComponentBySiteIdQueryHandler(IMapper mapper, ICyberComponentRepository cyberComponentRepository)
    {
        _mapper = mapper;
        _cyberComponentRepository = cyberComponentRepository;
    }

    public async Task<List<CyberComponentBySiteIdVm>> Handle(GetCyberComponentBySiteIdQuery request,
        CancellationToken cancellationToken)
    {
        var cyberComponent = await _cyberComponentRepository.GetCyberComponentBySiteId(request.SiteId);

        return cyberComponent.Count == 0
            ? new List<CyberComponentBySiteIdVm>()
            : _mapper.Map<List<CyberComponentBySiteIdVm>>(cyberComponent);
    }
}