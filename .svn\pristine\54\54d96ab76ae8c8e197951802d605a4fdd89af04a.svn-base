﻿using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Events.Request;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Request;

public class
    ApprovalMatrixRequestCommandHandler : IRequestHandler<ApprovalMatrixRequestCommand, ApprovalMatrixRequestResponse>
{
    private readonly IApprovalMatrixApprovalRepository _approvalMatrixApprovalRepository;
    private readonly IApprovalMatrixRequestRepository _approvalMatrixRequestRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public ApprovalMatrixRequestCommandHandler(IApprovalMatrixRequestRepository approvalMatrixRequestRepository,
        IApprovalMatrixApprovalRepository approvalMatrixApprovalRepository, I<PERSON><PERSON><PERSON> mapper, IPublisher publisher)
    {
        _approvalMatrixRequestRepository = approvalMatrixRequestRepository;
        _approvalMatrixApprovalRepository = approvalMatrixApprovalRepository;
        _mapper = mapper;
        _publisher = publisher;
    }

    public async Task<ApprovalMatrixRequestResponse> Handle(ApprovalMatrixRequestCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _approvalMatrixRequestRepository.GetByReferenceIdAsync(request.Id)
                            ?? throw new NotFoundException(nameof(Domain.Entities.ApprovalMatrixRequest), request.Id);

        eventToUpdate.IsRequest = true;

        _mapper.Map(request, eventToUpdate, typeof(ApprovalMatrixRequestCommand),
            typeof(Domain.Entities.ApprovalMatrixRequest));

        await _approvalMatrixRequestRepository.UpdateAsync(eventToUpdate);

        var approvalMatrixToUpdate = await _approvalMatrixApprovalRepository.GetByReferenceIdAsync(request.Id);

        if (approvalMatrixToUpdate == null)
        {
            var approvalMatrixApproval = _mapper.Map<Domain.Entities.ApprovalMatrixApproval>(request);

            await _approvalMatrixApprovalRepository.AddAsync(approvalMatrixApproval);
        }
        else
        {
            _mapper.Map(request, approvalMatrixToUpdate, typeof(UpdateApprovalMatrixApprovalCommand),
                typeof(Domain.Entities.ApprovalMatrixApproval));

            await _approvalMatrixApprovalRepository.UpdateAsync(approvalMatrixToUpdate);
        }

        var response = new ApprovalMatrixRequestResponse
        {
            Message = $"Approval Matrix Request send request successfully {eventToUpdate.ProcessName}",

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new ApprovalMatrixRequestedEvent { Name = eventToUpdate.ProcessName },
            cancellationToken);

        return response;
    }
}