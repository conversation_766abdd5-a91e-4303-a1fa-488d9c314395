﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Job.Events.UpdateJobStatus;

public class JobStatusUpdatedEventHandler : INotificationHandler<JobStatusUpdatedEvent>
{
    private readonly ILogger<JobStatusUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;

    private readonly ILoggedInUserService _userService;

    public JobStatusUpdatedEventHandler(ILoggedInUserService userService, ILogger<JobStatusUpdatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(JobStatusUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.MonitoringJob.ToString(),
            Action = $"{ActivityType.Update} {Modules.MonitoringJob}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Job '{updatedEvent.JobName}' Status :'{updatedEvent.Status}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Job '{updatedEvent.JobName}' Status :'{updatedEvent.Status}' updated successfully.");
    }
}