using ContinuityPatrol.Application.Features.PageWidget.Commands.Create;
using ContinuityPatrol.Application.Features.PageWidget.Commands.Delete;
using ContinuityPatrol.Application.Features.PageWidget.Commands.Update;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetList;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PageWidgetModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class PageWidgetService : BaseService, IPageWidgetService
{
    public PageWidgetService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<PageWidgetListVm>> GetPageWidgetList()
    {
        Logger.LogDebug("Get All PageWidgets");

        return await Mediator.Send(new GetPageWidgetListQuery());
    }

    public async Task<PageWidgetDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "PageWidget Id");

        Logger.LogDebug($"Get PageWidget Detail by Id '{id}'");

        return await Mediator.Send(new GetPageWidgetDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreatePageWidgetCommand createPageWidgetCommand)
    {
        Logger.LogDebug($"Create PageWidget '{createPageWidgetCommand}'");

        return await Mediator.Send(createPageWidgetCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdatePageWidgetCommand updatePageWidgetCommand)
    {
        Logger.LogDebug($"Update PageWidget '{updatePageWidgetCommand}'");

        return await Mediator.Send(updatePageWidgetCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "PageWidget Id");

        Logger.LogDebug($"Delete PageWidget Details by Id '{id}'");

        return await Mediator.Send(new DeletePageWidgetCommand { Id = id });
    }

    #region NameExist

    public async Task<bool> IsPageWidgetNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "PageWidget Name");

        Logger.LogDebug($"Check Name Exists Detail by PageWidget Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetPageWidgetNameUniqueQuery { Name = name, Id = id });
    }

    #endregion

    #region Paginated

    public async Task<PaginatedResult<PageWidgetListVm>> GetPaginatedPageWidgets(GetPageWidgetPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in PageWidget Paginated List");

        return await Mediator.Send(query);
    }

    #endregion
}