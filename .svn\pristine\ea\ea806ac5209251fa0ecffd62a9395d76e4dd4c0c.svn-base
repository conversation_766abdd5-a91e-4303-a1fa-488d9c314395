﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.AlertNotificationModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.AlertNotification.Queries.GetPaginatedList;

public class GetAlertNotificationPaginatedListQueryHandler : IRequestHandler<GetAlertNotificationPaginatedListQuery,
    PaginatedResult<AlertNotificationListVm>>
{
    private readonly IAlertNotificationRepository _alertNotificationRepository;
    private readonly IMapper _mapper;

    public GetAlertNotificationPaginatedListQueryHandler(IMapper mapper,
        IAlertNotificationRepository alertNotificationRepository)
    {
        _mapper = mapper;
        _alertNotificationRepository = alertNotificationRepository;
    }

    public async Task<PaginatedResult<AlertNotificationListVm>> Handle(
        GetAlertNotificationPaginatedListQuery request, CancellationToken cancellationToken)
    {

        var productFilterSpec = new AlertNotificationFilterSpecification(request.SearchString);

        var queryable = await _alertNotificationRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var alertNotificationsList = _mapper.Map<PaginatedResult<AlertNotificationListVm>>(queryable);
        

        return alertNotificationsList;


        //var queryable = _alertNotificationRepository.GetPaginatedQuery();

        //var productFilterSpec = new AlertNotificationFilterSpecification(request.SearchString);

        //var alertNotificationsList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<AlertNotificationListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return alertNotificationsList;
    }
}