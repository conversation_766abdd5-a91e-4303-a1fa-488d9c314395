﻿using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Events.Approval;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Approval;

public class
    ApprovalMatrixApprovalCommandHandler : IRequestHandler<ApprovalMatrixApprovalCommand,
        ApprovalMatrixApprovalResponse>
{
    private readonly IApprovalMatrixApprovalRepository _approvalMatrixApprovalRepository;
    private readonly IApprovalMatrixRequestRepository _approvalMatrixRequestRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IApprovalMatrixRepository _approvalMatrixRepository;
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IWorkflowTempRepository _workflowTempRepository;
    private readonly ISmtpConfigurationRepository _smtpConfigurationRepository;
    private readonly IEmailService _emailService;
    private readonly IApprovalMatrixUsersRepository _approvalMatrixUsersRepository;

    public ApprovalMatrixApprovalCommandHandler(IApprovalMatrixRequestRepository approvalMatrixRequestRepository,
        IApprovalMatrixApprovalRepository approvalMatrixApprovalRepository, IMapper mapper, IPublisher publisher, 
        IApprovalMatrixRepository approvalMatrixRepository, IWorkflowRepository workflowRepository, 
        IWorkflowTempRepository workflowTempRepository, ISmtpConfigurationRepository smtpConfigurationRepository, IEmailService emailService, IApprovalMatrixUsersRepository approvalMatrixUsersRepository)
    {
        _approvalMatrixRequestRepository = approvalMatrixRequestRepository;
        _approvalMatrixApprovalRepository = approvalMatrixApprovalRepository;
        _mapper = mapper;
        _publisher = publisher;
        _approvalMatrixRepository = approvalMatrixRepository;
        _workflowRepository = workflowRepository;
        _workflowTempRepository = workflowTempRepository;
        _smtpConfigurationRepository = smtpConfigurationRepository;
        _emailService = emailService;
        _approvalMatrixUsersRepository = approvalMatrixUsersRepository;
    }

    public async Task<ApprovalMatrixApprovalResponse> Handle(ApprovalMatrixApprovalCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _approvalMatrixApprovalRepository.GetByReferenceIdAsync(request.Id)
                            ?? throw new NotFoundException(nameof(Domain.Entities.ApprovalMatrixApproval), request.Id);

        eventToUpdate.Status = request.Status;

        _mapper.Map(request, eventToUpdate, typeof(ApprovalMatrixApprovalCommand),
            typeof(Domain.Entities.ApprovalMatrixApproval));

        var approvalMatrix = await _approvalMatrixRepository.GetByReferenceIdAsync(eventToUpdate.ApprovalMatrixId) 
                             ?? throw new NotFoundException(nameof(Domain.Entities.ApprovalMatrix), eventToUpdate.ApprovalMatrixId);

        await _approvalMatrixApprovalRepository.UpdateAsync(eventToUpdate);

        await ProcessWorkflow(approvalMatrix.Properties, eventToUpdate.Status, eventToUpdate.ProcessName,eventToUpdate.RequestId);

        await SendEmail(eventToUpdate);

        var response = new ApprovalMatrixApprovalResponse
        {
            Message = $"Approval Matrix request {(request.Status.Equals("Approved",StringComparison.OrdinalIgnoreCase) ? "approved" : "rejected")} '{request.ProcessName}' successfully ",

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new ApprovalMatrixApprovalEvent { Name = eventToUpdate.ProcessName },
            cancellationToken);

        return response;
    }

    public async Task ProcessWorkflow(string json, string status, string processName, string requestId)
    {
        var processStatus = status.ToLower() == "approved"
            ? "approverCount"
            : "rejectCount";

        var steps = JArray.Parse(json).ToDictionary(
            step => step["name"]?.ToString(),
            step => step as JObject
        );

        if (processName == null || !steps.TryGetValue(processName, out var current))
            return;

        var valid = await IsValidRule(status, current, processStatus);

        if (!valid)
            return;

        if (status.Equals("Rejected", StringComparison.OrdinalIgnoreCase))
        {
            var unapproved = await _approvalMatrixApprovalRepository.GetUnapprovedByRequestId(requestId);

            unapproved.ForEach(x => x.Status = "Rejected");

            await _approvalMatrixApprovalRepository.UpdateRangeAsync(unapproved);

            var approvalRequest = await _approvalMatrixRequestRepository.GetByRequestId(requestId);

            approvalRequest.Status = "Rejected";

            await _approvalMatrixRequestRepository.UpdateAsync(approvalRequest);

            var workflowTemp = await _workflowTempRepository.GetWorkflowTempByRequestId(requestId);

            if (workflowTemp is not null)
            {
                var workflow = await _workflowRepository.GetByReferenceIdAsync(workflowTemp.WorkflowId);

                workflow.IsDraft = false;

                await _workflowRepository.UpdateAsync(workflow);

                workflowTemp.IsActive = false;

                await _workflowTempRepository.UpdateAsync(workflowTemp);
            }

            return;
        }

        var nextName = current["straightLineData"]?.FirstOrDefault()?["to"]?.ToString();
        current = nextName != null && steps.TryGetValue(nextName, out var step) ? step : null;

        if (current is null)
        {
            var workflowTemp = await _workflowTempRepository.GetWorkflowTempByRequestId(requestId);

            if (workflowTemp is not null)
            {
                var workflow = await _workflowRepository.GetByReferenceIdAsync(workflowTemp.WorkflowId);

                workflow.IsDraft = false;
                workflow.Properties = workflowTemp.Properties;
                workflow.Version = workflowTemp.Version;

                await _workflowRepository.UpdateAsync(workflow);

                workflowTemp.IsActive = false;

                await _workflowTempRepository.UpdateAsync(workflowTemp);
            }

            var approvalRequest = await _approvalMatrixRequestRepository.GetByRequestId(requestId);

            approvalRequest.Status = "Approved";

            await _approvalMatrixRequestRepository.UpdateAsync(approvalRequest);

            return;
        }

        var userIds = await GetUserIds(current);

        var approvalUsers = await _approvalMatrixApprovalRepository.GetApprovalMatrixByApprovalIds(userIds);

        if (approvalUsers.Count > 0)
        {
            approvalUsers.ForEach(x => x.IsApproval = true);

            await _approvalMatrixApprovalRepository.UpdateRangeAsync(approvalUsers);
        }
    }

    private async Task SendEmail(Domain.Entities.ApprovalMatrixApproval approvalMatrix)
    {
        var smtpConfigurations = (await _smtpConfigurationRepository.ListAllAsync()).LastOrDefault();

        if (smtpConfigurations is not null)
        {
            var approvalMatrixUser = await _approvalMatrixUsersRepository.GetByUserIdAsync(approvalMatrix.ApprovalMatrixId);

            if(approvalMatrixUser is null || string.IsNullOrEmpty(approvalMatrixUser.Email))
            {
                return;
            }

            var emailTemplate = approvalMatrix.Status.Equals("Rejected", StringComparison.OrdinalIgnoreCase)
                ? EmailTemplateHelper.ApprovalMatrixRejectEmailBody(approvalMatrixUser.UserName,
                    approvalMatrix.ProcessName, approvalMatrix.ApproverName)
                : EmailTemplateHelper.ApprovalMatrixAcceptEmailBody(approvalMatrix.UserName,approvalMatrix.ProcessName,approvalMatrix.ApproverName);

            var logo = approvalMatrix.Status.Equals("Rejected", StringComparison.OrdinalIgnoreCase)
                ? "Reject"
                : "Approval";

            var imageNames = approvalMatrix.Status.Equals("Rejected", StringComparison.OrdinalIgnoreCase) ? new List<string>
            {
                "abstract.png",
                "cp_logo.png",
                "approval_matrix_rejected.png"
            } : new List<string>
            {
                "abstract.png",
                "cp_logo.png",
                "approval_matrix_approved.png"
            };

            var htmlView = HtmlEmailBuilder.BuildHtmlView(emailTemplate, imageNames, logo);

            var emailDto = new EmailDto
            {
                To = approvalMatrixUser.Email,
                From = smtpConfigurations.UserName,
                SmtpHost = smtpConfigurations.SmtpHost,
                HtmlBody = htmlView,
                EnableSSL = (bool)smtpConfigurations?.EnableSSL,
                Port = smtpConfigurations.Port,
                Password = smtpConfigurations.Password
            };

            await _emailService.SendEmail(emailDto);
        }

    }


    private async Task<bool> IsValidRule(string status, JObject current, string processStatus)
    {
        var userIds = await GetUserIds(current);

        var approvalUsers = await _approvalMatrixApprovalRepository.GetApprovalMatrixByApprovalIds(userIds);

        var approvalUserList = approvalUsers.Count(x => x.Status.Equals(status,StringComparison.OrdinalIgnoreCase));

        var dynamicRuleCount = ((JArray)current["ruleSet"] ?? new JArray())
            .FirstOrDefault(rule => rule[processStatus] != null);

        var type = dynamicRuleCount!["type"]?.ToString();

        var ruleCount = int.Parse(dynamicRuleCount["ruleCount"]?.ToString() ?? "0");

        return type switch
        {
            "All" => approvalUserList >= ruleCount,
            "Majority" => approvalUserList >= (ruleCount / 2 + 1),
            "Any" or "At Least One" => approvalUserList >= 1,
            "Custom" => approvalUserList == ruleCount,
            _ => false
        };
    }

    public Task<List<string>> GetUserIds(JObject current)
    {
        var userIds = current["userLists"]?
            .Select(u => u["id"]?.ToString())
            .Where(id => !string.IsNullOrEmpty(id))
            .ToList();

        return Task.FromResult(userIds);
    }
}