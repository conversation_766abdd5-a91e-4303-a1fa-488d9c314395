﻿namespace ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Commands.Create;

public class CreateBusinessServiceEvaluationCommand : IRequest<CreateBusinessServiceEvaluationResponse>
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string Description { get; set; }
    public string Grade { get; set; }
    public string GradeValue { get; set; }

    public override string ToString()
    {
        return $"Business Service Name: {BusinessServiceName};";
    }
}