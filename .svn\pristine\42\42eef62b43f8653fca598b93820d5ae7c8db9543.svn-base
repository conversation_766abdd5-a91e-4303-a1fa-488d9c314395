using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class BulkImportOperationRepositoryMocks
{
    public static Mock<IBulkImportOperationRepository> CreateBulkImportOperationRepository(List<BulkImportOperation> bulkImportOperations)
    {
        var mockBulkImportOperationRepository = new Mock<IBulkImportOperationRepository>();

        mockBulkImportOperationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportOperations);

        mockBulkImportOperationRepository.Setup(repo => repo.AddAsync(It.IsAny<BulkImportOperation>())).ReturnsAsync(
            (BulkImportOperation bulkImportOperation) =>
            {
                bulkImportOperation.Id = new Fixture().Create<int>();
                bulkImportOperation.ReferenceId = new Fixture().Create<Guid>().ToString();
                bulkImportOperations.Add(bulkImportOperation);
                return bulkImportOperation;
            });

        mockBulkImportOperationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BulkImportOperation>()));
        // .Returns(Task.CompletedTask);

        mockBulkImportOperationRepository.Setup(repo => repo.DeleteAsync(It.IsAny<BulkImportOperation>()));
            //.Returns(Task.CompletedTask);

        mockBulkImportOperationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportOperations.FirstOrDefault(x => x.ReferenceId == id));

        mockBulkImportOperationRepository.Setup(repo => repo.GetRunningStatus())
            .ReturnsAsync(() => bulkImportOperations.Where(x => x.Status == "Running" || x.Status == "Pending").ToList());

        mockBulkImportOperationRepository.Setup(repo => repo.GetDescriptionBulkImportStartAndEndTime(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string startTime, string endTime) => 
            {
                if (DateTime.TryParse(startTime, out var start) && DateTime.TryParse(endTime, out var end))
                {
                    return bulkImportOperations.Where(x => x.StartTime >= start && x.EndTime <= end).ToList();
                }
                return bulkImportOperations;
            });

        return mockBulkImportOperationRepository;
    }

    public static Mock<IBulkImportOperationRepository> CreateUpdateBulkImportOperationRepository(List<BulkImportOperation> bulkImportOperations)
    {
        var mockBulkImportOperationRepository = new Mock<IBulkImportOperationRepository>();

        mockBulkImportOperationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportOperations);

        mockBulkImportOperationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportOperations.FirstOrDefault(x => x.ReferenceId == id));

        mockBulkImportOperationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BulkImportOperation>()));
            //.Returns(Task.CompletedTask);

        return mockBulkImportOperationRepository;
    }

    public static Mock<IBulkImportOperationRepository> CreateDeleteBulkImportOperationRepository(List<BulkImportOperation> bulkImportOperations)
    {
        var mockBulkImportOperationRepository = new Mock<IBulkImportOperationRepository>();

        mockBulkImportOperationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportOperations);

        mockBulkImportOperationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportOperations.FirstOrDefault(x => x.ReferenceId == id));

        mockBulkImportOperationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BulkImportOperation>()));
           // .Returns(Task.CompletedTask);

        return mockBulkImportOperationRepository;
    }

    public static Mock<IBulkImportOperationRepository> CreateQueryBulkImportOperationRepository(List<BulkImportOperation> bulkImportOperations)
    {
        var mockBulkImportOperationRepository = new Mock<IBulkImportOperationRepository>();

        mockBulkImportOperationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportOperations);

        mockBulkImportOperationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportOperations.FirstOrDefault(x => x.ReferenceId == id));

        mockBulkImportOperationRepository.Setup(repo => repo.GetRunningStatus())
            .ReturnsAsync(() => bulkImportOperations.Where(x => x.Status == "Running" || x.Status == "Pending").ToList());

        mockBulkImportOperationRepository.Setup(repo => repo.GetDescriptionBulkImportStartAndEndTime(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string startTime, string endTime) => 
            {
                if (DateTime.TryParse(startTime, out var start) && DateTime.TryParse(endTime, out var end))
                {
                    return bulkImportOperations.Where(x => x.StartTime >= start && x.EndTime <= end).ToList();
                }
                return bulkImportOperations;
            });

        return mockBulkImportOperationRepository;
    }

    public static Mock<IUserActivityRepository> CreateBulkImportOperationEventRepository(List<UserActivity> userActivities)
    {
        var mockUserActivityRepository = new Mock<IUserActivityRepository>();

        mockUserActivityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        mockUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();
                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();
                userActivities.Add(userActivity);
                return userActivity;
            });

        return mockUserActivityRepository.As<IUserActivityRepository>();
    }

    public static Mock<IBulkImportOperationGroupRepository> CreateBulkImportOperationGroupRepository(List<BulkImportOperationGroup> bulkImportOperationGroups)
    {
        var mockBulkImportOperationGroupRepository = new Mock<IBulkImportOperationGroupRepository>();

        mockBulkImportOperationGroupRepository.Setup(repo => repo.AddRangeAsync(It.IsAny<List<BulkImportOperationGroup>>()));
           // .Returns(Task.CompletedTask);

        mockBulkImportOperationGroupRepository.Setup(repo => repo.GetBulkImportOperationGroupByBulkImportOperationIds(It.IsAny<List<string>>()))
            .ReturnsAsync((List<string> operationIds) => bulkImportOperationGroups.Where(x => operationIds.Contains(x.BulkImportOperationId)).ToList());

        return mockBulkImportOperationGroupRepository;
    }

    public static Mock<IBulkImportActionResultRepository> CreateBulkImportActionResultRepository(List<BulkImportActionResult> bulkImportActionResults)
    {
        var mockBulkImportActionResultRepository = new Mock<IBulkImportActionResultRepository>();

        //mockBulkImportActionResultRepository.Setup(repo => repo.GetByOperationIdAndOperationGroupId(It.IsAny<List<string>>()))
        //    .ReturnsAsync((List<string> groupIds) => bulkImportActionResults.Where(x => groupIds.Contains(x.BulkImportOperationGroupId)).ToList());

        //return mockBulkImportActionResultRepository;
        return mockBulkImportActionResultRepository;
    }
}
