﻿using AutoFixture;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByEntityId;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetRunQuery;
using ContinuityPatrol.Application.Features.PageWidget.Commands.Create;
using ContinuityPatrol.Application.Features.PageWidget.Commands.Update;
using ContinuityPatrol.Application.Features.PageWidget.Events.PaginatedView;
using ContinuityPatrol.Domain.ViewModels.DataSetModel;
using ContinuityPatrol.Domain.ViewModels.PageWidgetModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class ConfigureWidgetControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<IPageWidgetService> _mockPageWidgetService = new();
        private readonly Mock<IDataSetService> _mockDataSetService = new();
        private readonly Mock<ILogger<ConfigureWidgetController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Fixture _fixture = new();
        private ConfigureWidgetController _controller;

        public ConfigureWidgetControllerShould()
        {
            Initialize();
        }
        public void Initialize()
        {
            _controller = new ConfigureWidgetController(
                _mockPublisher.Object,
                _mockDataProvider.Object,
                _mockPageWidgetService.Object,
                _mockDataSetService.Object,
                _mockLogger.Object,
                _mockMapper.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_ShouldReturnViewResult()
        {
            var result = await _controller.List();

            Assert.IsType<ViewResult>(result);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task List_ThrowsException_PropagatesException()
        {
            // Arrange
            _mockPublisher.Setup(p => p.Publish(It.IsAny<PageWidgetPaginatedEvent>(), It.IsAny<CancellationToken>()))
                         .ThrowsAsync(new Exception("Test exception"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _controller.List());
        }
    
        [Fact]
        public async Task GetPageWidgetList_ReturnsJsonResult_WithPageWidgetList()
        {
            var pageWidgetList = new List<PageWidgetListVm>();
            _mockPageWidgetService.Setup(s => s.GetPageWidgetList()).ReturnsAsync(pageWidgetList);

            var result = await _controller.GetPageWidgetList();

            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public async Task GetPageWidgetList_ThrowsException_ReturnsJsonWithError()
        {
            // Arrange
            _mockPageWidgetService.Setup(s => s.GetPageWidgetList())
                                  .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPageWidgetList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult);
        }
      

        [Fact]
        public async Task GetAllTableAccesses_ReturnsJsonResult_WithTableAccesses()
        {
            var tableAccesses = new List<DataSetListVm>();
            _mockDataSetService.Setup(s => s.GetDataSetList()).ReturnsAsync(tableAccesses);

            var result = await _controller.GetAllTableAccesses();

            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public async Task GetAllTableAccesses_WithNonEmptyList_LogsSuccessMessage()
        {
            // Arrange
            var tableAccesses = new List<DataSetListVm>
            {
                new DataSetListVm { Id = "1", DataSetName = "Test Dataset" }
            };
            _mockDataSetService.Setup(s => s.GetDataSetList()).ReturnsAsync(tableAccesses);

            // Act
            var result = await _controller.GetAllTableAccesses();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult);

            // Verify that the count condition was executed (line 78)
            Assert.Single(tableAccesses); // This ensures the if condition was met
        }

        [Fact]
        public async Task GetAllTableAccesses_ThrowsException_ReturnsJsonWithError()
        {
            // Arrange
            _mockDataSetService.Setup(s => s.GetDataSetList())
                               .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllTableAccesses();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public async Task DatasetDetails_ReturnsJsonResult_WithDatasetDetails()
        {
            var dataSetDetails = new DataSetRunQueryVm();
            _mockDataSetService.Setup(s => s.RunQuery(It.IsAny<string>())).ReturnsAsync(dataSetDetails);

            var result = await _controller.DatasetDetails("someData");

            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public async Task DatasetDetails_ThrowsException_ReturnsJsonWithError()
        {
            // Arrange
            _mockDataSetService.Setup(s => s.RunQuery(It.IsAny<string>()))
                               .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DatasetDetails("someData");

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public async Task CreateOrUpdate_CreatesPageWidget_WhenIdIsEmpty()
        {
            // Arrange
            var viewModel = _fixture.Create<PageWidgetViewModel>();
            var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
            {
                { "id", "" }
            });
            _controller.Request.Form = formCollection;

            var command = new CreatePageWidgetCommand();
            var response = new BaseResponse { Success = true, Message = "Success" };

            _mockMapper.Setup(m => m.Map<CreatePageWidgetCommand>(viewModel)).Returns(command);
            _mockPageWidgetService.Setup(s => s.CreateAsync(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesPageWidget_WhenIdIsProvided()
        {
            // Arrange
            var viewModel = _fixture.Create<PageWidgetViewModel>();
            var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
            {
                { "id", "22" }
            });
            _controller.Request.Form = formCollection;

            var command = new UpdatePageWidgetCommand();
            var response = new BaseResponse { Success = true, Message = "Updated" };

            _mockMapper.Setup(m => m.Map<UpdatePageWidgetCommand>(viewModel)).Returns(command);
            _mockPageWidgetService.Setup(s => s.UpdateAsync(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesValidationException_ReturnsRedirectToList()
        {
            // Arrange
            var viewModel = _fixture.Create<PageWidgetViewModel>();
            var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
            {
                { "id", "" }
            });
            _controller.Request.Form = formCollection;

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Name", "Name is required"));
            var validationException = new ValidationException(validationResult);

            var command = new CreatePageWidgetCommand();
            _mockMapper.Setup(m => m.Map<CreatePageWidgetCommand>(viewModel)).Returns(command);
            _mockPageWidgetService.Setup(s => s.CreateAsync(command)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesGeneralException_ReturnsRedirectToList()
        {
            // Arrange
            var viewModel = _fixture.Create<PageWidgetViewModel>();
            var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
            {
                { "id", "" }
            });
            _controller.Request.Form = formCollection;

            var command = new CreatePageWidgetCommand();
            _mockMapper.Setup(m => m.Map<CreatePageWidgetCommand>(viewModel)).Returns(command);
            _mockPageWidgetService.Setup(s => s.CreateAsync(command)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task Delete_ReturnsRedirectResult()
        {
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockPageWidgetService.Setup(s => s.DeleteAsync(It.IsAny<string>())).ReturnsAsync(response);

            var result = await _controller.Delete("someId");

            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            Assert.Equal("ConfigureWidget", redirectResult.ControllerName);
            Assert.Equal("Admin", redirectResult.RouteValues["area"]);
        }

        [Fact]
        public async Task Delete_HandlesException_ReturnsRedirectToList()
        {
            // Arrange
            _mockPageWidgetService.Setup(s => s.DeleteAsync(It.IsAny<string>()))
                                  .ThrowsAsync(new Exception("Delete error"));

            // Act
            var result = await _controller.Delete("someId");

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public void DecryptDetails_ReturnsJsonResult_WithDecryptedDetails()
        {
            // Arrange
            var encryptedData = "encryptedData";

            // Act
            var result = _controller.DecryptDetails(encryptedData);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public void DecryptDetails_WithNullOrEmptyData_ReturnsEmptyDecrypt()
        {
            // Act
            var result = _controller.DecryptDetails("");

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public void DecryptDetails_WithNullData_ReturnsEmptyDecrypt()
        {
            // Act
            var result = _controller.DecryptDetails(null);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public void DecryptDetails_Returns_Json_With_Error_When_Exception_Occurs()
        {
            // Arrange
            // Create data that will pass the initial checks but fail at Convert.FromBase64String
            // Need: length >= 64, contains "$", but invalid base64
            string invalidEncryptedData = "validbase64key1234567890abcdefghijklmnopqrstuvwxyz1234567890$invalid_base64_data_that_will_cause_exception!@#$%^&*()";

            // Act
            var result = _controller.DecryptDetails(invalidEncryptedData);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Value);

            // Convert to JSON and check structure - should be error response
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("\"Message\":", json);
            Assert.Contains("\"ErrorCode\":", json);
        }

        [Fact]
        public async Task GetMonitorServiceStatusByIdAndType_ReturnsJsonResult_WithMonitorData()
        {
            var monitorData = new GetByEntityIdVm();
            _mockDataProvider.Setup(dp => dp.DashboardView.GetMonitorServiceStatusByIdAndType(It.IsAny<string>(), It.IsAny<string>()))
                             .ReturnsAsync(monitorData);

            var result = await _controller.GetMonitorServiceStatusByIdAndType("monitorId", "type");

            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public async Task GetMonitorServiceStatusByIdAndType_HandlesException_ReturnsJsonException()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.DashboardView.GetMonitorServiceStatusByIdAndType(It.IsAny<string>(), It.IsAny<string>()))
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetMonitorServiceStatusByIdAndType("monitorId", "type");

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult);
        }

        // ===== CONTROLLER ATTRIBUTE TESTS =====

        [Fact]
        public void Controller_ShouldHaveAreaAttribute()
        {
            // Arrange & Act
            var areaAttribute = _controller.GetType().GetCustomAttributes(typeof(AreaAttribute), false).FirstOrDefault() as AreaAttribute;

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void List_ShouldHaveAntiXssAttribute()
        {
            // Arrange & Act
            var method = _controller.GetType().GetMethod("List");
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void GetPageWidgetList_ShouldHaveAntiXssAttribute()
        {
            // Arrange & Act
            var method = _controller.GetType().GetMethod("GetPageWidgetList");
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void CreateOrUpdate_ShouldHaveValidateAntiForgeryTokenAndAntiXssAttributes()
        {
            // Arrange & Act
            var method = _controller.GetType().GetMethod("CreateOrUpdate");
            var validateAntiForgeryTokenAttribute = method.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();
            var httpPostAttribute = method.GetCustomAttributes(typeof(HttpPostAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
            Assert.NotNull(httpPostAttribute);
        }

        // ===== CONSTRUCTOR TESTS =====

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new ConfigureWidgetController(
                _mockPublisher.Object,
                _mockDataProvider.Object,
                _mockPageWidgetService.Object,
                _mockDataSetService.Object,
                _mockLogger.Object,
                _mockMapper.Object
            );

            // Assert
            Assert.NotNull(controller);
        }
    }
}

