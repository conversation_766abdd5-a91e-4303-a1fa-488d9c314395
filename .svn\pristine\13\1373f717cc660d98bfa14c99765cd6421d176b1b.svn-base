﻿let mId = sessionStorage.getItem("monitorId");
let monitortype = 'HyperV';
let infraObjectId = sessionStorage.getItem("infraobjectId");

setTimeout(() => { hypervmonitorstatus(mId, monitortype) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
setTimeout(() => { hyperVServer(infraObjectId) }, 250)

$('#mssqlserver').hide();
async function hyperVServer(id) {

    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
    let data = {}
    data.infraObjectId = id;
    let mssqlServerData = await getAysncWithHandler(url, data);

    if (mssqlServerData != null && mssqlServerData?.length > 0) {
        mssqlServerData?.forEach(data => {
            let value = data?.isServiceUpdate
            let parsed = []
            if (value && value !== 'NA') parsed = JSON?.parse(value)
            if (Array.isArray(parsed)) {
                parsed?.forEach(s => {
                    if (s?.Services?.length) {
                        $('#mssqlserver').show();
                        bindHyperVServer(mssqlServerData)
                    }
                })
            }
        })

    } else {
        $('#mssqlserver').hide();
    }

}
function bindHyperVServer(mssqlServerData) {

    let prType = { IpAddress: '--', Services: [] };
    let drType = { IpAddress: '--', Services: [] };

    // Loop through each item to find PR and DR entries
    mssqlServerData?.forEach(item => {
        let parsedServices = [];
        try {
            const value = item?.isServiceUpdate
            if (value && value !== 'NA') {
                parsedServices = JSON.parse(item?.isServiceUpdate)
            }
        } catch (e) {
            console.error("Invalid JSON in isServiceUpdate:", item?.isServiceUpdate);
        }

        parsedServices?.forEach(serviceGroup => {
            if (serviceGroup?.Type === 'PR') {
                prType.IpAddress = serviceGroup?.IpAddress || prType.IpAddress;
                prType.Services = [...prType.Services, ...(serviceGroup?.Services || [])];
            } else if (serviceGroup?.Type === 'DR') {
                drType.IpAddress = serviceGroup?.IpAddress || drType?.IpAddress;
                drType.Services = [...drType.Services, ...(serviceGroup?.Services || [])];
            }
        });
    });

    // Set header IPs
    $('#prIp').text('Primary (' + prType?.IpAddress + ')');
    $('#drIp').text('DR (' + drType?.IpAddress + ')');

    const prStatusSummary = getStatusSummary(prType.Services.map(s => s.Status).filter(Boolean));
    const drStatusSummary = getStatusSummary(drType.Services.map(s => s.Status).filter(Boolean));
    $('#prIp').html(`Primary (${prType.IpAddress}) <br ><span class="status-summary text-muted small">${prStatusSummary}</span>`);
    $('#drIp').html(`DR (${drType.IpAddress}) <br ><span class="status-summary text-muted small">${drStatusSummary}</span>`);

    // Unique list of all service names from both PR and DR
    let allServiceNames = [...new Set([
        ...prType?.Services?.map(s => s?.ServiceName),
        ...drType?.Services?.map(s => s?.ServiceName)
    ])];

    // Build table rows
    let tbody = $('#mssqlserverbody');
    tbody.empty();

    allServiceNames?.forEach(serviceName => {
        let prService = prType?.Services?.find(s => s?.ServiceName === serviceName);
        let drService = drType?.Services?.find(s => s?.ServiceName === serviceName);

        let prStatus = prService ? prService?.Status : '--';
        let drStatus = drService ? drService?.Status : '--';
        let prIcon = prStatus !== '--' ? `<i class="${getStatusIconClass(prStatus)} me-2 fs-6"></i>` : '';
        let drIcon = drStatus !== '--' ? `<i class="${getStatusIconClass(drStatus)} me-2 fs-6"></i>` : '';
        const iconService = serviceName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";

        let row = `
            <tr>
                <td><i class="${iconService} me-2 fs-6"></i><span>${serviceName}</span></td>
                <td>${prIcon}${prStatus}</td>
                <td>${drIcon}${drStatus}</td>
            </tr>
        `;
        tbody.append(row);
    });
}
function getStatusSummary(arr) {
    let countMap = {};
    arr?.forEach(status => {
        countMap[status] = (countMap[status] || 0) + 1;
    });
    let total = arr?.length;
    let statusSummary = Object.entries(countMap)
        .map(([status, count]) => `${count} ${status}`)
        .join(', ');
    return statusSummary ? `${statusSummary} out of ${total} services` : '--';
}
function getStatusIconClass(status) {
    if (!status) return "text-danger cp-disable";

    const lowerStatus = status.toLowerCase();
    if (lowerStatus === "running") {
        return "text-success cp-reload cp-animate";
    } else if (lowerStatus === "error" || lowerStatus === "stopped") {
        return "text-danger cp-fail-back";
    } else {
        return "text-danger cp-disable";
    }
}

$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})

async function hypervmonitorstatus(id, type) {

    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
}

let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'

function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}

function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}

function propertiesData(value) {

    if (value === undefined || value === null || value === '') {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
    else {
        let data = JSON?.parse(value?.properties);
        // postgreSolutionDiagram(data);

        //Database Details
        const hypervSummaryPR = data?.PR_HyperVReplicationMonitoring
        const hypervSummaryPRProp = ["PR_Hyper_V_Host_IP_Address", "PR_VM_Name",  "PR_VM_State",  "PR_VM_IP_Addresses",
            "PR_VM_Networking_Status", "PR_Replication_Type", "PR_Replication_State", "PR_Replication_Mode", "PR_Current_Primary_Server",
            "PR_Current_Replication_Server", "PR_Replication_Health", "PR_Replication_Port", "PR_Authentication_Type", "PR_Replicated_Size","PR_Last_synchronized_At"];
        const dataLag = ["PR_Datalag"]
        if (hypervSummaryPR !== '' && hypervSummaryPR !== null && hypervSummaryPR !== undefined) {
            bindProperties(value,hypervSummaryPR, hypervSummaryPRProp, dataLag);
        }
       
       
        const hypervSummaryDR = data?.DR_HyperVReplicationMonitoring
        const hypervSummaryDRProp = [ "DR_Hyper_V_Host_IP_Address",  "DR_VM_Name", "DR_VM_State",
            "DR_VM_IP_Addresses", "DR_VM_Networking_Status", "DR_Replication_Type", "DR_Replication_State", "DR_Replication_Mode",
            "DR_Current_Primary_Server", "DR_Current_Replication_Server", "DR_Replication_Health", "DR_Replication_Port", "DR_Authentication_Type",
            "DR_Replicated_Size", "DR_Last_synchronized_At"];
     
        if (hypervSummaryDR !== '' && hypervSummaryDR !== null && hypervSummaryDR !== undefined) {
            bindProperties(value,hypervSummaryDR, hypervSummaryDRProp);
        }
       
        //Datalag
        const datalag = checkAndReplace(data?.PR_Datalag);
        let dataLagValue = (datalag !== undefined && datalag !== "" && datalag !== null && datalag !== "0") ? `${(datalag)}` : 'NA';

        var result = "";

        if (dataLagValue?.includes(".")) {
            var value = dataLagValue?.split(".");
            var hours = value[0] * 24;
            var minutes = value[1]?.split(':')?.slice(0, 2)?.join(':');
            var min = minutes?.split(':');
            var firstValue = parseInt(min[0]) + parseInt(hours);
            result = firstValue + ":" + min[1];
            const minute = (parseInt(result[0]) * 60) + parseInt(result[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
        }
        else if (dataLagValue?.includes("+")) {
            const value = dataLagValue.split(" ");
            result = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')

        }
        else {
            result = dataLagValue?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
        }
    }
}

function setPropData(value,data, propSets) {
    propSets?.forEach(properties => {
        bindProperties(value,data, properties);
    });
}

function bindProperties(value,data, properties) {
    
    let prStatus = value?.prServerStatus?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : value?.prServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : value?.prServerStatus?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";
    let drStatus = value?.drServerStatus?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : value?.drServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : value?.drServerStatus?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";
    let prprimaryServer = data?.PR_Current_Primary_Server?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-manage-server me-1 text-primary";
    let drprimaryServer = data?.DR_Current_Primary_Server?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-manage-server me-1 text-primary";
    let prreplicaServer = data?.PR_Current_Replication_Server?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-manage-server me-1 text-primary";
    let drreplicaServer = data?.DR_Current_Replication_Server?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-manage-server me-1 text-primary";
    let prVmName =data?.PR_VM_Name ? "cp-name me-1 text-primary" : "cp-disable me-1 text-danger";
    let drVmName = data?.DR_VM_Name ? "cp-name me-1 text-primary" : "cp-disable me-1 text-danger";
    const iconMapping = {
        'PR_Hyper_V_Host_IP_Address': prStatus,
        'DR_Hyper_V_Host_IP_Address': drStatus,
        'PR_VM_IP_Addresses': prStatus,
        'DR_VM_IP_Addresses': drStatus,
        'PR_Current_Primary_Server': prprimaryServer,
        'DR_Current_Primary_Server': drprimaryServer,
        'PR_Current_Replication_Server': prreplicaServer,
        'DR_Current_Replication_Server': drreplicaServer,
        'PR_VM_Name': prVmName,
        'DR_VM_Name': drVmName,
    };

    properties?.forEach(property => {
        const value = data[property];
        let displayedValue = value !== undefined ? checkAndReplace(value) : 'NA';
        let iconClass = iconMapping[property] || '';

        // Add icons based on conditions
        switch (displayedValue?.toLowerCase()) {
            case 'na':
            case 'not allowed':
            case 'no':
                iconClass = 'text-danger cp-disable';
                break;
            case 'replicating':
                iconClass = 'text-primary cp-refresh';
                break;
            case 'streaming':
            case 'replica':
            case 'primary':
                iconClass = 'text-success cp-refresh';
                break;
            case 'running':
            case 'run':
                iconClass = 'text-success cp-reload cp-animate';
                break;
            case 'stopped':
            case 'stop':
                iconClass = 'text-danger cp-Stopped';
                break;
            case 'f':
            case 'false':
            case 'defer':
            case 'deferred':
                iconClass = 'text-danger cp-error';
                break;
            case 't':
            case 'true':
            case 'yes':
            case 'valid':
            case 'ok':
                iconClass = 'text-success cp-success';
                break;
            case 'pending':
                iconClass = 'text-warning cp-pending';
                break;
            case 'pause':
            case 'paused':
                iconClass = 'text-warning cp-circle-pause';
                break;
            case 'manual':
                iconClass = 'text-warning cp-settings';
                break;
            case 'synchronous_commit':
            case 'synchronized':
            case 'synchronizing':
            case 'sync':
                iconClass = 'text-success cp-refresh';
                break;
            case 'asynchronous_commit':
            case 'asynchronizing':
            case 'asynchronized':
            case 'async':
                iconClass = 'text-danger cp-refresh';
                break;
            case 'online':
                iconClass = 'text-success cp-online';
                break;
            case 'offline':
                iconClass = 'text-danger cp-offline';
                break;
            case 'enabled':
            case 'enable':
                iconClass = 'text-success cp-enables';
                break;
            case 'connected':
            case 'connect':
                iconClass = 'text-success cp-connected';
                break;
            case 'disconnected':
            case 'disconnect':
                iconClass = 'text-danger cp-disconnecteds';
                break;
            case 'standby':
            case 'to standby':
            case 'mounted':
                iconClass = 'text-warning cp-control-file-type';
                break;
            case 'required':
            case 'require':
                iconClass = 'text-warning cp-warning';
                break;
            case 'healthy':
            case 'normal':
                iconClass = 'text-success cp-health-success';
                break;
            case 'nothealthy':
            case 'not_healthy':
            case 'critical':
            case 'unhealthy':
                iconClass = 'text-danger cp-health-error';
                break;
            case 'error':
                iconClass = 'text-danger cp-fail-back';
                break;
            case 'on':
                iconClass = 'text-success cp-end';
                break;
            case 'off':
                iconClass = 'text-danger cp-end';
                break;
            case 'current':
            case 'read write':
                iconClass = 'text-success cp-file-edits';
                break;
            case 'primary':
                iconClass = 'text-primary cp-list-prsite';
                break;
            case 'secondary':
                iconClass = 'text-info cp-dr';
                break;
            case 'physical standby':
                iconClass = 'text-info cp-physical-drsite';
                break;
            case 'idle':
                iconClass = 'text-info cp-Idle';
                break;
            case 'enabled':
            case 'enable':
                iconClass = 'text-success cp-end';
                break;
            case 'disabled':
            case 'disable':
                iconClass = 'text-danger cp-disables';
                break;
            default:
                if (displayedValue?.includes('running')) {
                    iconClass = 'text-success cp-reload cp-animate';
                } else if (displayedValue?.includes('production') || displayedValue?.includes('archive recovery')) {
                    iconClass = 'text-warning cp-log-archive-config';
                }
                break;
        }
        // Displayed value with icon
        const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
        const mergeValue = `${iconHtml}${displayedValue}`;
        $(`#${property}`).html(mergeValue).attr('title', displayedValue);
    });
}

