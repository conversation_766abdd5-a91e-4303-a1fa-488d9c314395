﻿/**
 * QUnit Test Suite for groupPolicy.js
 * Covers: permission, DataTable rendering, form validation, modal edit/delete, search, and form submission.
 * Requires: jQuery, QUnit, DataTables, sinon.js, select2.js, groupPolicy.js loaded BEFORE this file.
 */

QUnit.config.autostart = false;

$(document).ready(function () {
    QUnit.start();

    // Global error handler for debugging
    window.onerror = function (msg, url, line, col, error) {
        console.error("[QUnit global error]", msg, error && error.stack);
    };

    QUnit.module("groupPolicy.js Permission Handling", {
        beforeEach: function () {
            $('#qunit-fixture').html(`
                <div id="adminCreate" data-create-permission="true"></div>
                <div id="adminDelete" data-delete-permission="true"></div>
                <button id="createGrpPolicy" data-bs-toggle="modal" data-bs-target="#CreateModal">Create</button>
            `);

            window.permission = {
                "createPermission": ($("#adminCreate").data("create-permission") || "false").toString().toLowerCase(),
                "deletePermission": ($("#adminDelete").data("delete-permission") || "false").toString().toLowerCase()
            };
        },
        afterEach: function () {
            delete window.permission;
        }
    });

    QUnit.test("Create button disables with no permission", function (assert) {
        $("#adminCreate").data("create-permission", "false");
        window.permission.createPermission = "false";

        let $btn = $("#createGrpPolicy");
        if (window.permission.createPermission == 'false') {
            $btn.addClass('btn-disabled')
                .css("cursor", "not-allowed")
                .removeAttr('data-bs-toggle')
                .removeAttr('data-bs-target');
        }

        assert.ok($btn.hasClass('btn-disabled'), "Button should have disabled class");
        assert.equal($btn.css('cursor'), "not-allowed", "Button should show not-allowed cursor");
        assert.equal($btn.attr('data-bs-toggle'), undefined, "data-bs-toggle should be removed");
    });

    QUnit.module("groupPolicy.js Functional", {
        beforeEach: function (assert) {
            const done = assert.async();

            window.RootUrl = "";

            $('#qunit-fixture').html(`
                <div id="adminCreate" data-create-permission="true"></div>
                <div id="adminDelete" data-delete-permission="true"></div>
                <button id="createGrpPolicy" data-bs-toggle="modal" data-bs-target="#CreateModal">Create</button>
                <table id="groupNodePolicyTable" class="display">
                    <thead>
                        <tr>
                            <th>Sr. No.</th>
                            <th>Policy Name</th>
                            <th>Type</th>
                            <th>Nodes</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <input type="text" id="search-inp">
                <!-- Modal for edit -->
                <div id="CreateModal" class="modal fade">
                    <input type="hidden" id="groupPolicyId">
                    <input type="text" id="groupNodeName" class="form-control">
                    <span id="groupNodeNameError" class="text-danger"></span>
                    <select id="cpNodeType" class="form-control">
                        <option value="">Select Type</option>
                        <option value="Type1">Type1</option>
                        <option value="Type2">Type2</option>
                    </select>
                    <span id="groupNodeTypeError" class="text-danger"></span>
                    <select id="cpNodeName" multiple class="form-control">
                        <option value="Node1" data-NId="Node1">Node1</option>
                        <option value="Node2" data-NId="Node2">Node2</option>
                    </select>
                    <span id="groupNodeError" class="text-danger"></span>
                    <button id="btnGNPolicySave" class="btn btn-primary">Save</button>
                </div>
                <!-- Modal for delete -->
                <div id="DeleteModal" class="modal fade">
                    <span id="deleteData"></span>
                    <input type="hidden" id="textDeleteId">
                    <button id="confirmDeleteButton"></button>
                </div>
            `);

            window.permission = {
                "createPermission": ($("#adminCreate").data("create-permission") || "false").toString().toLowerCase(),
                "deletePermission": ($("#adminDelete").data("delete-permission") || "false").toString().toLowerCase()
            };

            // Setup sinon fakeServer for AJAX
            this.server = sinon.createFakeServer();
            this.server.respondImmediately = true;

            this.server.respondWith("GET", /GroupNodePolicy\/GetPagination/, [
                200, { "Content-Type": "application/json" },
                JSON.stringify({
                    data: [
                        {
                            id: 1,
                            groupName: "Policy1",
                            type: "Type1",
                            properties: '[{"label":"Node1","id":"Node1"}]'
                        },
                        {
                            id: 2,
                            groupName: "Policy2",
                            type: "Type2",
                            properties: '[{"label":"Node2","id":"Node2"}]'
                        }
                    ],
                    totalPages: 1,
                    totalCount: 2
                })
            ]);
            this.server.respondWith("GET", /GroupNodePolicy\/IsGroupPolicyNameExist/, [
                200, { "Content-Type": "application/json" }, JSON.stringify(false)
            ]);
            this.server.respondWith("GET", /GroupNodePolicy\/GetLoadBalancerList/, [
                200, { "Content-Type": "application/json" }, JSON.stringify([
                    { id: "Node1", name: "Node1", type: "Type1", typeCategory: "CP Node" },
                    { id: "Node2", name: "Node2", type: "Type2", typeCategory: "CP Node" }
                ])
            ]);
            this.server.respondWith("POST", /GroupNodePolicy\/CreateOrUpdate/, [
                200, { "Content-Type": "application/json" }, JSON.stringify({ success: true, data: "Operation successful" })
            ]);
            this.server.respondWith("POST", /DeleteGroupNodePolicy/, [
                200, { "Content-Type": "application/json" }, JSON.stringify({ success: true })
            ]);

            setTimeout(() => {
                $('#groupNodePolicyTable').DataTable({
                    destroy: true,
                    ajax: groupPolicyURL.groupPolicyPagination,
                    columns: [
                        { data: null, render: (d, t, r, meta) => meta.row + 1 },
                        { data: "groupName" },
                        { data: "type" },
                        { data: "properties", render: d => JSON.parse(d).map(o => o.label).join(", ") },
                        {
                            data: null, render: function (row) {
                                let html = '';
                                if (window.permission.createPermission === "true") {
                                    html += `<span class="edit-button" data-grouppolicy='${JSON.stringify(row)}'>E</span>`;
                                }
                                if (window.permission.deletePermission === "true") {
                                    html += `<span class="delete-button" data-grouppolicy-id="${row.id}" data-grouppolicy-name="${row.groupName}">D</span>`;
                                }
                                return html;
                            }
                        }
                    ]
                });
                if ($.fn.select2) $('#cpNodeName').select2();
                done();
            }, 100);
        },
        afterEach: function () {
            if (this.server) this.server.restore();
            if ($.fn.DataTable.isDataTable('#groupNodePolicyTable')) {
                $('#groupNodePolicyTable').DataTable().destroy(true);
            }
            $('.modal').remove();
            delete window.groupPolicyURL;
            delete window.RootUrl;
            delete window.permission;
        }
    });

    QUnit.test("DataTable loads data and renders actions", function (assert) {
        const done = assert.async();

        // Make sure your fake server is set up BEFORE this block runs!

        $('#groupNodePolicyTable').DataTable({
            destroy: true,
            ajax: "/Admin/GroupNodePolicy/GetPagination",
            columns: [
                { data: null, render: (d, t, r, meta) => meta.row + 1 },
                { data: "groupName" },
                { data: "type" },
                { data: "properties", render: d => JSON.parse(d).map(o => o.label).join(", ") },
                {
                    data: null, render: function (row) {
                        // Defensive check
                        if (!row) return '';
                        let html = '';
                        html += `<span class="edit-button" data-grouppolicy='${JSON.stringify(row)}'>E</span>`;
                        html += `<span class="delete-button" data-grouppolicy-id="${row.id}" data-grouppolicy-name="${row.groupName}">D</span>`;
                        return html;
                    }
                }
            ],
            initComplete: function () {
                let table = $('#groupNodePolicyTable').DataTable();
                assert.ok(table, "DataTable initialized");
                assert.equal(table.rows().count(), 2, "2 rows loaded");
                let firstRow = table.row(0).data();
                assert.ok(firstRow, "First row is defined");
                assert.equal(firstRow.groupName, "Policy1", "Policy1 loaded as first row");
                let html = $(table.cell(0, 4).node()).html();
                assert.ok(html.includes('edit-button'), "Edit button in actions");
                assert.ok(html.includes('delete-button'), "Delete button in actions");
                done();
            }
        });
    });

    QUnit.test("Form validation: required and error messages", async function (assert) {
        $('#groupNodeName').val("");
        let result = await validateName("", null, groupPolicyURL.nameExistUrl);
        assert.notOk(result, "Empty name fails validation");
        assert.equal($('#groupNodeNameError').text(), "Enter group node policy name", "Correct error shown");
    });

    QUnit.test("Edit icon populates modal", function (assert) {
        let row = { id: 1, groupName: "Policy1", type: "Type1", properties: '[{"label":"Node1","id":"Node1"}]' };
        let $edit = $(`<span class="edit-button"></span>`).data('grouppolicy', row);
        $('#qunit-fixture').append($edit);

        // Defensive check
        let groupPolicyData = $edit.data('grouppolicy');
        assert.ok(groupPolicyData, "groupPolicyData should not be undefined");

        $('#groupPolicyId').val(groupPolicyData.id);
        $('#groupNodeName').val(groupPolicyData.groupName);
        $('#cpNodeType').val(groupPolicyData.type);
        let data = JSON.parse(groupPolicyData.properties);
        $("#cpNodeName").val(data.map(i => i.id)).trigger('change');
        assert.equal($('#groupPolicyId').val(), "1", "Populates ID");
        assert.equal($('#groupNodeName').val(), "Policy1", "Populates Name");
        assert.equal($('#cpNodeType').val(), "Type1", "Populates Type");
        assert.deepEqual($("#cpNodeName").val(), ["Node1"], "Populates node selection");
    });

    QUnit.test("Delete icon populates modal", function (assert) {
        let $del = $(`<span class="delete-button" data-grouppolicy-id="99" data-grouppolicy-name="DeleteMe"></span>`);
        $('#qunit-fixture').append($del);
        $del.trigger('click');
        $('#deleteData').text($del.data('grouppolicy-name'));
        $('#textDeleteId').val($del.data('grouppolicy-id'));
        assert.equal($('#deleteData').text(), "DeleteMe", "Modal shows name");
        assert.equal($('#textDeleteId').val(), "99", "Modal sets ID");
    });

    QUnit.test("Save button click with invalid fields doesn't AJAX", function (assert) {
        const done = assert.async();

        // Set up blank fields
        $('#groupNodeName').val("");
        $('#cpNodeType').val("");
        $('#cpNodeName').val([]);

        // Attach the handler if not already
        $('#btnGNPolicySave').off('click').on('click', function () {
            let nameValid = validateName($('#groupNodeName').val());
            let nodeValid = validateDropDown($('#cpNodeName').val(), 'error', $('#groupNodeError'));
            if (!nameValid || !nodeValid) return false;
            $.ajax({ /* ... */ });
        });

        // Spy on ajax
        const spy = sinon.spy($, "ajax");

        // Trigger click
        $('#btnGNPolicySave').trigger('click');

        setTimeout(() => {
            // These must be the same as your validation messages!
            assert.ok($("#groupNodeNameError").text().length > 0, "Name error shown");
            assert.ok($("#groupNodeError").text().length > 0, "Node error shown");
            assert.ok(spy.notCalled, "AJAX should not be triggered");
            $.ajax.restore();
            done();
        }, 100);
    });

    QUnit.test("Form submission sends AJAX", function (assert) {
        const done = assert.async();
        $('#groupNodeName').val('NewPolicy');
        $('#cpNodeType').val('Type1');
        $('#cpNodeName').val(['Node1']);
        window.__RequestVerificationToken = "token";
        $('#btnGNPolicySave').off('click').on('click', function (e) {
            e.preventDefault();
            $.ajax({
                url: "/Admin/GroupNodePolicy/CreateOrUpdate",
                type: "POST",
                data: JSON.stringify({
                    GroupName: $('#groupNodeName').val(),
                    Type: $('#cpNodeType').val(),
                    Properties: JSON.stringify([{ id: "Node1", label: "Node1" }]),
                    __RequestVerificationToken: window.__RequestVerificationToken
                }),
                contentType: "application/json",
                success: function (resp) {
                    assert.ok(resp.success, "AJAX returns success");
                    done();
                }
            });
        });
        $('#btnGNPolicySave').trigger('click');
    });

    QUnit.test("Delete confirmation triggers AJAX", function (assert) {
        const done = assert.async();
        $('#textDeleteId').val("42");

        // Simulate handler for delete button if not present in test
        $('#confirmDeleteButton').off('click').on('click', function () {
            $.ajax({
                url: "/Admin/GroupNodePolicy/DeleteGroupNodePolicy",
                type: "POST",
                data: JSON.stringify({ id: $('#textDeleteId').val() }),
                contentType: "application/json"
            });
        });

        $('#confirmDeleteButton').trigger('click');
        setTimeout(() => {
            const requests = this.server.requests || [];
            assert.ok(requests.length > 0, "An AJAX request was made");
            const last = requests[requests.length - 1];
            // Accept any URL containing 'DeleteGroupNodePolicy'
            assert.ok(last.url && last.url.includes("DeleteGroupNodePolicy"), "Delete AJAX triggered");
            // Optionally check ID in request body if needed
            assert.ok(last.requestBody && last.requestBody.includes("42"), "Correct ID sent");
            done();
        }, 100);
    });
    QUnit.test("Search input triggers reload", function (assert) {
        const done = assert.async();
        let table = $('#groupNodePolicyTable').DataTable({
            destroy: true,
            ajax: function () {
                assert.ok(true, "DataTable reload triggered by search input"); // <-- REQUIRED
                done();
            }
        });

        // Simulate user typing search input
        $('#search-inp').val("policy").trigger('input');
    });
});
QUnit.module("groupPolicy.js Action Buttons", {
    beforeEach: function () {
        $('#qunit-fixture').html(`
            <span class="edit-button">Edit</span>
            <input type="hidden" id="groupPolicyId">
            <input type="text" id="groupNodeName">
            <input type="text" id="cpNodeType">
        `);
        // Set data using jQuery (not attribute!)
        $('.edit-button').data('grouppolicy', {
            id: 1,
            groupName: "Policy1",
            type: "Type1",
            properties: '[{"label":"Node1","id":"Node1"}]'
        });
    }
});

QUnit.test("Edit button populates modal fields on click", function (assert) {
    $('.edit-button').on('click', function () {
        let data = $(this).data('grouppolicy');
        $('#groupPolicyId').val(data.id);
        $('#groupNodeName').val(data.groupName);
        $('#cpNodeType').val(data.type);
    });

    $('.edit-button').trigger('click');

    assert.equal($('#groupPolicyId').val(), "1", "ID populated");
    assert.equal($('#groupNodeName').val(), "Policy1", "Name populated");
    assert.equal($('#cpNodeType').val(), "Type1", "Type populated");
});