using ContinuityPatrol.Application.Features.HacmpCluster.Events.Create;

namespace ContinuityPatrol.Application.Features.HacmpCluster.Commands.Create;

public class CreateHacmpClusterCommandHandler : IRequestHandler<CreateHacmpClusterCommand, CreateHacmpClusterResponse>
{
    private readonly IHacmpClusterRepository _hacmpClusterRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateHacmpClusterCommandHandler(IMapper mapper, IHacmpClusterRepository hacmpClusterRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _hacmpClusterRepository = hacmpClusterRepository;
    }

    public async Task<CreateHacmpClusterResponse> Handle(CreateHacmpClusterCommand request,
        CancellationToken cancellationToken)
    {
        var hacmpCluster = _mapper.Map<Domain.Entities.HacmpCluster>(request);

        hacmpCluster = await _hacmpClusterRepository.AddAsync(hacmpCluster);

        var response = new CreateHacmpClusterResponse
        {
            Message = Message.Create("HACMP Cluster", hacmpCluster.Name),

            Id = hacmpCluster.ReferenceId
        };

        await _publisher.Publish(new HacmpClusterCreatedEvent { Name = hacmpCluster.Name }, cancellationToken);

        return response;
    }
}