﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Domain.ViewModels.FormTypeCategoryModel;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Domain.ViewModels.ReplicationMasterModel;
using ContinuityPatrol.Domain.ViewModels.RoboCopyModel;
using ContinuityPatrol.Domain.ViewModels.ServerSubTypeModel;
using ContinuityPatrol.Domain.ViewModels.ServerTypeModel;
using ContinuityPatrol.Domain.ViewModels.SiteModel;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class BulkImportInputControllerShould
    {
        private readonly Mock<ILogger<BulkImportInputController>> _mockLogger;
        private readonly Mock<IDataProvider> _mockDataProvider;
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
        private readonly Mock<IFormRepository> _mockFormRepository;
        private readonly BulkImportInputController _controller;

        public BulkImportInputControllerShould()
        {
            _mockLogger = new Mock<ILogger<BulkImportInputController>>();
            _mockDataProvider = new Mock<IDataProvider>();
            _mockLoggedInUserService = new Mock<ILoggedInUserService>();
            _mockFormRepository = new Mock<IFormRepository>();

            _controller = new BulkImportInputController(
                _mockLoggedInUserService.Object,
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockFormRepository.Object);

            // Setup HttpContext and TempData
            var httpContext = new DefaultHttpContext();
            httpContext.User = new ClaimsPrincipal(new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.Name, "testuser"),
                new Claim(ClaimTypes.NameIdentifier, "123")
            }, "mock"));

            _controller.ControllerContext = new ControllerContext()
            {
                HttpContext = httpContext
            };

            var tempData = new TempDataDictionary(httpContext, Mock.Of<ITempDataProvider>());
            _controller.TempData = tempData;
        }

        [Fact]
        public void List_ReturnsViewResult()
        {
            // Act
            var result = _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Null(viewResult.ViewName);
        }

        [Fact]
        public async Task Download_Template_WithOracleTemplate_ReturnsFileResult()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);
            Assert.Contains("BulkImportTemplate_Oracle_", fileResult.FileDownloadName);
            Assert.Contains(".xls", fileResult.FileDownloadName);

            VerifyAllMockCalls();

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithMssqlNLSTemplate_ReturnsFileResult()
        {
            // Arrange
            var templateType = "MssqlNLS";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);
            Assert.Contains("BulkImportTemplate_MssqlNLS_", fileResult.FileDownloadName);
            Assert.Contains(".xls", fileResult.FileDownloadName);

            VerifyAllMockCalls();

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithMSSQLMirroringTemplate_ReturnsFileResult()
        {
            // Arrange
            var templateType = "MSSQLMirroring";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);
            Assert.Contains("BulkImportTemplate_MSSQLMirroring_", fileResult.FileDownloadName);
            Assert.Contains(".xls", fileResult.FileDownloadName);

            VerifyAllMockCalls();

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithMSSQLAlWaysOnTemplate_ReturnsFileResult()
        {
            // Arrange
            var templateType = "MSSQLAlWaysOn";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);
            Assert.Contains("BulkImportTemplate_MSSQLAlWaysOn_", fileResult.FileDownloadName);
            Assert.Contains(".xls", fileResult.FileDownloadName);

            VerifyAllMockCalls();

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithOracleRacTemplate_ReturnsFileResult()
        {
            // Arrange
            var templateType = "OracleRac";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);
            Assert.Contains("BulkImportTemplate_OracleRac_", fileResult.FileDownloadName);
            Assert.Contains(".xls", fileResult.FileDownloadName);

            VerifyAllMockCalls();

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithPostgresTemplate_ReturnsFileResult()
        {
            // Arrange
            var templateType = "Postgres";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);
            Assert.Contains("BulkImportTemplate_Postgres_", fileResult.FileDownloadName);
            Assert.Contains(".xls", fileResult.FileDownloadName);

            VerifyAllMockCalls();

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithMongoTemplate_ReturnsFileResult()
        {
            // Arrange
            var templateType = "Mongo";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);
            Assert.Contains("BulkImportTemplate_Mongo_", fileResult.FileDownloadName);
            Assert.Contains(".xls", fileResult.FileDownloadName);

            VerifyAllMockCalls();

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithMysqlTemplate_ReturnsFileResult()
        {
            // Arrange
            var templateType = "Mysql";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);
            Assert.Contains("BulkImportTemplate_Mysql_", fileResult.FileDownloadName);
            Assert.Contains(".xls", fileResult.FileDownloadName);

            VerifyAllMockCalls();

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithRoboCopyTemplate_ReturnsFileResult()
        {
            // Arrange
            var templateType = "RoboCopy";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);
            Assert.Contains("BulkImportTemplate_RoboCopy_", fileResult.FileDownloadName);
            Assert.Contains(".xls", fileResult.FileDownloadName);

            VerifyAllMockCalls();

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithSRMTemplate_ReturnsFileResult()
        {
            // Arrange
            var templateType = "SRM";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);
            Assert.Contains("BulkImportTemplate_SRM_", fileResult.FileDownloadName);
            Assert.Contains(".xls", fileResult.FileDownloadName);

            VerifyAllMockCalls();

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithUnknownTemplate_ReturnsFileResult()
        {
            // Arrange
            var templateType = "UnknownTemplate";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);
            Assert.Contains("BulkImportTemplate_UnknownTemplate_", fileResult.FileDownloadName);
            Assert.Contains(".xls", fileResult.FileDownloadName);

            VerifyAllMockCalls();

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        //[Fact]
        //public async Task Download_Template_WithException_ReturnsContentResult()
        //{
        //    // Arrange
        //    var templateType = "Oracle";
        //    _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames())
        //        .ThrowsAsync(new Exception("Database error"));

        //    // Act
        //    var result = await _controller.Download_Template(templateType);

        //    // Assert
        //    var contentResult = Assert.IsType<ContentResult>(result);
        //    Assert.Equal("Database error", contentResult.Content);

        //    _mockLogger.Verify(
        //        x => x.Log(
        //            LogLevel.Error,
        //            It.IsAny<EventId>(),
        //            It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("An error occurred: Database error")),
        //            It.IsAny<Exception>(),
        //            It.IsAny<Func<It.IsAnyType, Exception, string>>()),
        //        Times.Once);
        //}

        [Fact]
        public async Task Download_Template_WithNullTemplateType_ReturnsErrorResult()
        {
            // Arrange
            string templateType = null;
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var contentResult = Assert.IsType<ContentResult>(result);
            Assert.Contains("Object reference not set to an instance of an object", contentResult.Content);

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithEmptyTemplateType_ReturnsFileResult()
        {
            // Arrange
            var templateType = "";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);
            Assert.Contains("BulkImportTemplate__", fileResult.FileDownloadName);
            Assert.Contains(".xls", fileResult.FileDownloadName);

            VerifyAllMockCalls();

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithWindowsFormType_ProcessesWinRMPortAndProxyAccessType()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();

            // Setup Windows form type with WinRM Port and Proxy Access Type
            var windowsFormType = new FormTypeCategoryListVm
            {
                FormId = "form-id-1",
                FormTypeName = "Windows",
                Name = "server",
                Version = JsonConvert.SerializeObject(new Dictionary<string, List<string>>
                {
                    ["Version"] = new List<string> { "2019", "2022" }
                })
            };

            var formTypes = new List<FormTypeCategoryListVm> { windowsFormType };
            _mockDataProvider.Setup(dp => dp.FormMapping.GetFormMappingList())
                .ReturnsAsync(formTypes);

            var formEntity = new Form
            {
                Properties = JsonConvert.SerializeObject(new Dictionary<string, dynamic>
                {
                    ["fields"] = new Dictionary<string, dynamic>
                    {
                        ["field1"] = new
                        {
                            config = new { label = "WinRM Port" },
                            options = new[]
                            {
                                new { value = "5985" },
                                new { value = "5986" }
                            }
                        },
                        ["field2"] = new
                        {
                            config = new { label = "Proxy Access Type" },
                            options = new[]
                            {
                                new { value = "Direct" },
                                new { value = "Proxy" }
                            }
                        }
                    }
                })
            };

            _mockFormRepository.Setup(fr => fr.GetByReferenceIdAsync("form-id-1"))
                .ReturnsAsync(formEntity);

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);

            _mockFormRepository.Verify(fr => fr.GetByReferenceIdAsync("form-id-1"), Times.Once);
        }

        [Fact]
        public async Task Download_Template_WithFormRepositoryException_HandlesGracefully()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();

            var windowsFormType = new FormTypeCategoryListVm
            {
                FormId = "form-id-1",
                FormTypeName = "Windows",
                Name = "server",
                Version = JsonConvert.SerializeObject(new Dictionary<string, List<string>>
                {
                    ["Version"] = new List<string> { "2019" }
                })
            };

            var formTypes = new List<FormTypeCategoryListVm> { windowsFormType };
            _mockDataProvider.Setup(dp => dp.FormMapping.GetFormMappingList())
                .ReturnsAsync(formTypes);

            _mockFormRepository.Setup(fr => fr.GetByReferenceIdAsync("form-id-1"))
                .ThrowsAsync(new Exception("Form repository error"));

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var contentResult = Assert.IsType<ContentResult>(result);
            Assert.Equal("Form repository error", contentResult.Content);
        }

        [Fact]
        public async Task Download_Template_WithNullFormProperties_HandlesGracefully()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();

            var windowsFormType = new FormTypeCategoryListVm
            {
                FormId = "form-id-1",
                FormTypeName = "Windows",
                Name = "server",
                Version = JsonConvert.SerializeObject(new Dictionary<string, List<string>>
                {
                    ["Version"] = new List<string> { "2019" }
                })
            };

            var formTypes = new List<FormTypeCategoryListVm> { windowsFormType };
            _mockDataProvider.Setup(dp => dp.FormMapping.GetFormMappingList())
                .ReturnsAsync(formTypes);

            var formEntity = new Form { Properties = null };
            _mockFormRepository.Setup(fr => fr.GetByReferenceIdAsync("form-id-1"))
                .ReturnsAsync(formEntity);

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var contentResult = Assert.IsType<ContentResult>(result);
            Assert.Contains("Value cannot be null", contentResult.Content);
        }

        [Fact]
        public async Task Download_Template_WithEmptyFormProperties_ReturnsErrorResult()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();

            var windowsFormType = new FormTypeCategoryListVm
            {
                FormId = "form-id-1",
                FormTypeName = "Windows",
                Name = "server",
                Version = JsonConvert.SerializeObject(new Dictionary<string, List<string>>
                {
                    ["Version"] = new List<string> { "2019" }
                })
            };

            var formTypes = new List<FormTypeCategoryListVm> { windowsFormType };
            _mockDataProvider.Setup(dp => dp.FormMapping.GetFormMappingList())
                .ReturnsAsync(formTypes);

            var formEntity = new Form { Properties = "{}" };
            _mockFormRepository.Setup(fr => fr.GetByReferenceIdAsync("form-id-1"))
                .ReturnsAsync(formEntity);

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var contentResult = Assert.IsType<ContentResult>(result);
            Assert.NotNull(contentResult.Content);
        }

        [Fact]
        public async Task Download_Template_WithInvalidJsonProperties_HandlesGracefully()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();

            var windowsFormType = new FormTypeCategoryListVm
            {
                FormId = "form-id-1",
                FormTypeName = "Windows",
                Name = "server",
                Version = JsonConvert.SerializeObject(new Dictionary<string, List<string>>
                {
                    ["Version"] = new List<string> { "2019" }
                })
            };

            var formTypes = new List<FormTypeCategoryListVm> { windowsFormType };
            _mockDataProvider.Setup(dp => dp.FormMapping.GetFormMappingList())
                .ReturnsAsync(formTypes);

            var formEntity = new Form { Properties = "invalid json" };
            _mockFormRepository.Setup(fr => fr.GetByReferenceIdAsync("form-id-1"))
                .ReturnsAsync(formEntity);

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var contentResult = Assert.IsType<ContentResult>(result);
            Assert.Contains("Unexpected character", contentResult.Content);
        }

        [Fact]
        public async Task Download_Template_WithEmptyBusinessServiceNames_ReturnsFileResult()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames())
                .ReturnsAsync(new List<BusinessServiceNameVm>());

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithEmptyFormMappingList_ReturnsErrorResult()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            _mockDataProvider.Setup(dp => dp.FormMapping.GetFormMappingList())
                .ReturnsAsync(new List<FormTypeCategoryListVm>());

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var contentResult = Assert.IsType<ContentResult>(result);
            Assert.NotNull(contentResult.Content);

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithNullSites_ReturnsFileResult()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            _mockDataProvider.Setup(dp => dp.Site.GetSites())
                .ReturnsAsync((List<SiteListVm>)null);

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithNullServerRoles_ReturnsFileResult()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            _mockDataProvider.Setup(dp => dp.ServerType.GetServerTypeList())
                .ReturnsAsync((List<ServerTypeListVm>)null);

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithNullServerTypes_ReturnsFileResult()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            _mockDataProvider.Setup(dp => dp.ServerSubType.GetServerSubTypeList())
                .ReturnsAsync((List<ServerSubTypeListVm>)null);

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithNullLicenseNames_ReturnsFileResult()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            _mockDataProvider.Setup(dp => dp.LicenseManager.GetAllPoNumbers())
                .ReturnsAsync((List<LicenseManagerNameVm>)null);

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithNullOperationalFunctionNames_ReturnsErrorResult()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionNames())
                .ReturnsAsync((List<BusinessFunctionNameVm>)null);

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var contentResult = Assert.IsType<ContentResult>(result);
            Assert.NotNull(contentResult.Content);

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithNullReplicationCategories_ReturnsErrorResult()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            _mockDataProvider.Setup(dp => dp.ReplicationMaster.GetReplicationMasterNames())
                .ReturnsAsync((List<ReplicationMasterNameVm>)null);

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var contentResult = Assert.IsType<ContentResult>(result);
            Assert.NotNull(contentResult.Content);

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithNullInfraList_ReturnsFileResult()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectList())
                .ReturnsAsync((List<InfraObjectListVm>)null);

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        [Fact]
        public async Task Download_Template_WithNullRoboCopyProperties_ReturnsFileResult()
        {
            // Arrange
            var templateType = "Oracle";
            SetupMockDataForDownloadTemplate();
            SetupFileSystemForDownloadTemplate();

            _mockDataProvider.Setup(dp => dp.RoboCopy.GetRoboCopyList())
                .ReturnsAsync((List<RoboCopyListVm>)null);

            // Act
            var result = await _controller.Download_Template(templateType);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.ms-excel", fileResult.ContentType);

            // Cleanup
            CleanupFileSystemForDownloadTemplate();
        }

        private void SetupMockDataForDownloadTemplate()
        {
            // Setup BusinessService
            var businessServiceNames = new List<BusinessServiceNameVm>
            {
                new BusinessServiceNameVm { Id = "bs1", Name = "Business Service 1" },
                new BusinessServiceNameVm { Id = "bs2", Name = "Business Service 2" }
            };
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames())
                .ReturnsAsync(businessServiceNames);

            // Setup FormMapping for database types
            var formMappingList = new List<FormTypeCategoryListVm>
            {
                new FormTypeCategoryListVm
                {
                    Name = "database",
                    FormTypeName = "Oracle",
                    Version = JsonConvert.SerializeObject(new Dictionary<string, List<string>>
                    {
                        ["Version"] = new List<string> { "11g", "12c", "19c" }
                    })
                },
                new FormTypeCategoryListVm
                {
                    Name = "database",
                    FormTypeName = "MSSQL",
                    Version = JsonConvert.SerializeObject(new Dictionary<string, List<string>>
                    {
                        ["Version"] = new List<string> { "2016", "2019", "2022" }
                    })
                },
                new FormTypeCategoryListVm
                {
                    Name = "server",
                    FormTypeName = "Windows",
                    Version = JsonConvert.SerializeObject(new Dictionary<string, List<string>>
                    {
                        ["Version"] = new List<string> { "2016", "2019", "2022" }
                    })
                },
                new FormTypeCategoryListVm
                {
                    Name = "server",
                    FormTypeName = "Linux",
                    Version = JsonConvert.SerializeObject(new Dictionary<string, List<string>>
                    {
                        ["Version"] = new List<string> { "RHEL 7", "RHEL 8", "Ubuntu 20.04" }
                    })
                },
                new FormTypeCategoryListVm
                {
                    Name = "replication",
                    FormTypeName = "Oracle DataGuard",
                    Version = JsonConvert.SerializeObject(new Dictionary<string, List<string>>
                    {
                        ["Version"] = new List<string> { "11g", "12c" }
                    })
                }
            };
            _mockDataProvider.Setup(dp => dp.FormMapping.GetFormMappingList())
                .ReturnsAsync(formMappingList);

            // Setup Sites
            var sites = new List<SiteListVm>
            {
                new SiteListVm { Id = "site1", Name = "Primary Site" },
                new SiteListVm { Id = "site2", Name = "DR Site" }
            };
            _mockDataProvider.Setup(dp => dp.Site.GetSites())
                .ReturnsAsync(sites);

            // Setup Server Roles
            var serverRoles = new List<ServerTypeListVm>
            {
                new ServerTypeListVm { Id = "role1", Name = "Database Server" },
                new ServerTypeListVm { Id = "role2", Name = "Application Server" }
            };
            _mockDataProvider.Setup(dp => dp.ServerType.GetServerTypeList())
                .ReturnsAsync(serverRoles);

            // Setup Server Types
            var serverTypes = new List<ServerSubTypeListVm>
            {
                new ServerSubTypeListVm { Id = "type1", Name = "Physical" },
                new ServerSubTypeListVm { Id = "type2", Name = "Virtual" }
            };
            _mockDataProvider.Setup(dp => dp.ServerSubType.GetServerSubTypeList())
                .ReturnsAsync(serverTypes);

            // Setup License Names
            var licenseNames = new List<LicenseManagerNameVm>
            {
                new LicenseManagerNameVm { Id = "lic1", PoNumber = "PO-001" },
                new LicenseManagerNameVm { Id = "lic2", PoNumber = "PO-002" }
            };
            _mockDataProvider.Setup(dp => dp.LicenseManager.GetAllPoNumbers())
                .ReturnsAsync(licenseNames);

            // Setup Business Function Names
            var businessFunctionNames = new List<BusinessFunctionNameVm>
            {
                new BusinessFunctionNameVm { Id = "bf1", Name = "Critical Function" },
                new BusinessFunctionNameVm { Id = "bf2", Name = "Important Function" }
            };
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionNames())
                .ReturnsAsync(businessFunctionNames);

            // Setup Replication Categories
            var replicationCategories = new List<ReplicationMasterNameVm>
            {
                new ReplicationMasterNameVm { Id = "rep1", Name = "Oracle DataGuard" },
                new ReplicationMasterNameVm { Id = "rep2", Name = "SQL Server AlwaysOn" },
                new ReplicationMasterNameVm { Id = "rep1", Name = "Oracle DataGuard" } // Duplicate to test GroupBy
            };
            _mockDataProvider.Setup(dp => dp.ReplicationMaster.GetReplicationMasterNames())
                .ReturnsAsync(replicationCategories);

            // Setup InfraObject List
            var infraObjectList = new List<InfraObjectListVm>
            {
                new InfraObjectListVm { Id = "infra1", Name = "Database Cluster 1" },
                new InfraObjectListVm { Id = "infra2", Name = "Application Cluster 1" }
            };
            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectList())
                .ReturnsAsync(infraObjectList);

            // Setup RoboCopy Properties
            var roboCopyProperties = new List<RoboCopyListVm>
            {
                new RoboCopyListVm { Id = "robo1", Name = "/MIR /R:3 /W:10" },
                new RoboCopyListVm { Id = "robo2", Name = "/E /COPY:DAT" }
            };
            _mockDataProvider.Setup(dp => dp.RoboCopy.GetRoboCopyList())
                .ReturnsAsync(roboCopyProperties);

            // Setup Form Repository for Windows form type processing
            var windowsFormProperties = new Form
            {
                Properties = JsonConvert.SerializeObject(new
                {
                    fields = new
                    {
                        field1 = new
                        {
                            config = new { label = "WinRM Port" },
                            options = new[]
                            {
                                new { value = "5985" },
                                new { value = "5986" }
                            }
                        },
                        field2 = new
                        {
                            config = new { label = "Proxy Access Type" },
                            options = new[]
                            {
                                new { value = "Direct" },
                                new { value = "Proxy" }
                            }
                        }
                    }
                })
            };
            _mockFormRepository.Setup(fr => fr.GetByReferenceIdAsync(It.IsAny<string>()))
                .ReturnsAsync(windowsFormProperties);
        }

        private void VerifyAllMockCalls()
        {
            _mockDataProvider.Verify(dp => dp.BusinessService.GetBusinessServiceNames(), Times.Once);
            _mockDataProvider.Verify(dp => dp.FormMapping.GetFormMappingList(), Times.AtLeastOnce);
            _mockDataProvider.Verify(dp => dp.Site.GetSites(), Times.Once);
            _mockDataProvider.Verify(dp => dp.ServerType.GetServerTypeList(), Times.Once);
            _mockDataProvider.Verify(dp => dp.ServerSubType.GetServerSubTypeList(), Times.Once);
            _mockDataProvider.Verify(dp => dp.LicenseManager.GetAllPoNumbers(), Times.Once);
            _mockDataProvider.Verify(dp => dp.BusinessFunction.GetBusinessFunctionNames(), Times.Once);
            _mockDataProvider.Verify(dp => dp.ReplicationMaster.GetReplicationMasterNames(), Times.Once);
            _mockDataProvider.Verify(dp => dp.InfraObject.GetInfraObjectList(), Times.Once);
            _mockDataProvider.Verify(dp => dp.RoboCopy.GetRoboCopyList(), Times.Once);
        }

        private void SetupFileSystemForDownloadTemplate()
        {
            // Create the wwwroot/Report directory if it doesn't exist
            var reportDir = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report");
            if (!Directory.Exists(reportDir))
            {
                Directory.CreateDirectory(reportDir);
            }
        }

        private void CleanupFileSystemForDownloadTemplate()
        {
            // Clean up any files created during the test
            var reportDir = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report");
            if (Directory.Exists(reportDir))
            {
                var files = Directory.GetFiles(reportDir, "BulkImportTemplate_*.xls");
                foreach (var file in files)
                {
                    try
                    {
                        File.Delete(file);
                    }
                    catch
                    {
                        // Ignore cleanup errors
                    }
                }
            }
        }
    }
}
