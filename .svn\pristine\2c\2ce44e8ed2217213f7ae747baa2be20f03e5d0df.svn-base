﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowPermission.Events.PaginatedView;

public class WorkflowPermissionPaginatedEventHandler : INotificationHandler<WorkflowPermissionPaginatedEvent>
{
    private readonly ILogger<WorkflowPermissionPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowPermissionPaginatedEventHandler(ILogger<WorkflowPermissionPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(WorkflowPermissionPaginatedEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.WorkflowPermission.ToString(),
            Action = $"{ActivityType.View} {Modules.WorkflowPermission}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "User Privileges viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("User Privileges viewed");
    }
}