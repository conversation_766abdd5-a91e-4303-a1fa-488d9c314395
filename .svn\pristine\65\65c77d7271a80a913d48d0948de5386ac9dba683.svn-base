﻿using ContinuityPatrol.Application.Features.RsyncOption.Commands.Create;
using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RsyncOptionModel;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Application.Features.RsyncOption.Commands.Update;
using ContinuityPatrol.Application.Features.RsyncOption.Events.PaginatedView;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class RSyncOptionsController : Controller
{
    private readonly IPublisher _publisher;
    private readonly ILogger<RSyncOptionsController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;

    public RSyncOptionsController(IPublisher publisher, ILogger<RSyncOptionsController> logger, IMapper mapper, IDataProvider dataProvider)
    {
        _publisher = publisher;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }

    [AntiXss]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in RSyncOptions");
        await _publisher.Publish(new RsyncOptionPaginatedEvent());
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(RsyncOptionViewModel rsyncOptionsViewModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in RSyncOptions");
        var rsyncId = Request.Form["id"].ToString();
        try
        {
            rsyncOptionsViewModel.Properties = rsyncOptionsViewModel.Properties.IsNullOrWhiteSpace() ? rsyncOptionsViewModel.Properties : SecurityHelper.Decrypt(rsyncOptionsViewModel.Properties);
            if (rsyncId.IsNullOrWhiteSpace())
            {
                var rsyncCreateCommand = _mapper.Map<CreateRsyncOptionCommand>(rsyncOptionsViewModel);
                _logger.LogDebug($"Creating RSyncOptions '{rsyncCreateCommand.Name}'");
                var response = await _dataProvider.RsyncOption.CreateAsync(rsyncCreateCommand);
                _logger.LogDebug("Create operation completed successfully in RSyncOptions, returning view.");
                return Json(new { success = true, data = response });
            }
            else
            {
                var rsyncUpdateCommand = _mapper.Map<UpdateRsyncOptionCommand>(rsyncOptionsViewModel);
                _logger.LogDebug($"Updating RSyncOptions '{rsyncUpdateCommand.Name}'");
                var updateResponse = await _dataProvider.RsyncOption.UpdateAsync(rsyncUpdateCommand);
                _logger.LogDebug("Update operation completed successfully in RSyncOptions, returning view.");
                return Json(new { success = true, data = updateResponse });
            }
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on rsync option page: {ex.ValidationErrors.FirstOrDefault()}");
            return ex.GetJsonException();
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on rsync option page while processing the request for create or update.", ex);
            return ex.GetJsonException();
        }
    }

    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in RSyncOptions");
        try
        {
            var responseDelete = await _dataProvider.RsyncOption.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted record in RSyncOptions");
            return Json(new { success = true, data = responseDelete });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on RSyncOptions.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetPagination(GetRsyncOptionPaginatedListQuery rsyncquery)
    {
        _logger.LogDebug("Entering GetPagination method in RSyncOptions");
        try
        {
            var rsyncList = await _dataProvider.RsyncOption.GetPaginatedRsyncOptions(rsyncquery);
            _logger.LogDebug("Successfully retrieved rsync option paginated list on RSyncOptions page");
            return Json(new { success = true, data = rsyncList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on rsync options page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetRsyncOptionsDataById(string id)
    {
        _logger.LogDebug("Entering GetRsyncOptionsDataById method in RSyncOptions");
        try
        {
            var rsyncData = await _dataProvider.RsyncOption.GetByReferenceId(id);
            _logger.LogDebug("Successfully retrieved rsync option dea");
            return Json(new { success = true, data = rsyncData });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on RSync options page while processing the request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<bool> IsRsyncNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsRsyncNameExist method in RSyncOptions");
        try
        {
            _logger.LogDebug("Returning result for IsRsyncNameExist on RSyncOptions");
            return await _dataProvider.RsyncOption.IsRsyncOptionNameExist(name, id);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on rsync options page while checking if rsync option name exists for : {name}.", ex);
            return false;
        }
    }

    public async Task<JsonResult> GetRsyncJobByReplicationId(string replicationId)
    {
        _logger.LogDebug("Entering GetRsyncJobByReplicationId method in RSyncOptions");
        try
        {
            var rsyncJobs = (await _dataProvider.RsyncJob.GetRsyncJobs()).Where(x => x.ReplicationId.Equals(replicationId)).ToList();
            _logger.LogDebug($"Successfully retrieved rsync job by replicationId '{replicationId}' in RSyncOptions");
            return Json(new { success = true, data = rsyncJobs });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on rsync options page while retrieving rsync job by replicationId.", ex);
            return ex.GetJsonException();
        }
    }
}
