﻿using ContinuityPatrol.Application.Features.AlertNotification.Commands.Create;
using ContinuityPatrol.Application.Features.AlertNotification.Commands.Delete;
using ContinuityPatrol.Application.Features.AlertNotification.Commands.Update;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetByInfraObjectId;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetList;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AlertNotificationModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class AlertNotificationsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<AlertNotificationListVm>>> GetAlertNotifications()
    {
        Logger.LogDebug("Get All AlertNotifications");

        return Ok(await Mediator.Send(new GetAlertNotificationListQuery()));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateAlertNotificationResponse>> CreateAlertNotification([FromBody] CreateAlertNotificationCommand createAlertNotificationCommand)
    {
        Logger.LogDebug($"Create AlertNotification '{createAlertNotificationCommand.InfraObjectId}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateAlertNotification), await Mediator.Send(createAlertNotificationCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateAlertNotificationResponse>> UpdateAlertNotification([FromBody] UpdateAlertNotificationCommand updateAlertNotificationCommand)
    {
        Logger.LogDebug($"Update AlertNotification '{updateAlertNotificationCommand.InfraObjectId}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateAlertNotificationCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteAlertNotificationResponse>> DeleteAlertNotification(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "AlertNotification Id");

        Logger.LogDebug($"Delete AlertNotification Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteAlertNotificationCommand { Id = id }));
    }

    [HttpGet("{id}", Name = "GetAlertNotification")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<AlertNotificationDetailVm>> GetAlertNotificationById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "AlertNotification Id");

        Logger.LogDebug($"Get AlertNotification Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetAlertNotificationDetailQuery { Id = id }));
    }

    [HttpGet("by/InfraObjectIdandCode")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<AlertNotificationDetailByInfraObjectIdVm>>> GetAlertNotificationByInfraObjectIdAndAlertCode(string infraObjectId, string alertCode)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "AlertNotification Id");

        Logger.LogDebug($"Get AlertNotification Detail by Id '{infraObjectId}' and  AlertCode '{alertCode}.");

        return Ok(await Mediator.Send(new GetAlertNotificationDetailByInfraObjectIdQuery { InfraObjectId = infraObjectId, AlertCode = alertCode }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<AlertNotificationListVm>>> GetPaginatedAlertNotifications([FromQuery] GetAlertNotificationPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in AlertNotification Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllAlertNotificationNameCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}
