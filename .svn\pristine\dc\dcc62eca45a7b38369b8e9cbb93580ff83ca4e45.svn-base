﻿using ContinuityPatrol.Application.Features.WorkflowInfraObject.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetByInfraObjectIdAndActionType;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetInfraObjectByWorkflowId;
using ContinuityPatrol.Domain.ViewModels.WorkflowInfraObjectModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetWorkflowInfraObjectByInfraObjectId;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IWorkflowInfraObjectService
{
    Task<List<WorkflowInfraObjectListVm>> GetWorkflowInfraObjectList();
    Task<List<GetInfraObjectByWorkflowIdVm>> GetInfraObjectByWorkflowId(string workflowId);
    Task<BaseResponse> CreateAsync(CreateWorkflowInfraObjectCommand createWorkflowInfraObject);
    Task<BaseResponse> DeleteAsync(DeleteWorkflowInfraObjectCommand deleteWorkflowInfraObject);
    Task<bool> WorkflowInfraObjectByWorkflowIdExist(string workflowId);
    Task<List<WorkflowInfraObjectByInfraObjectIdAndActionTypeVm>> GetWorkflowByInfraObjectIdAndActionType(string infraId, string actionType);
    Task<List<WorkflowInfraObjectByInfraObjectIdVm>> GetWorkflowByInfraObjectId(string infraId);

}