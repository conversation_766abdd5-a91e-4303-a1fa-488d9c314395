﻿namespace ContinuityPatrol.Application.Features.Database.Queries.GetDetail;

public class GetDatabaseDetailQueryHandler : IRequestHandler<GetDatabaseDetailQuery, DatabaseDetailVm>
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly IMapper _mapper;

    public GetDatabaseDetailQueryHandler(IMapper mapper, IDatabaseRepository databaseRepository)
    {
        _mapper = mapper;
        _databaseRepository = databaseRepository;
    }

    public async Task<DatabaseDetailVm> Handle(GetDatabaseDetailQuery request, CancellationToken cancellationToken)
    {
        var database = await _databaseRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(database, nameof(Domain.Entities.Database),
            new NotFoundException(nameof(Domain.Entities.Database), request.Id));

        //database.LicenseKey = SecurityHelper.Decrypt(database.LicenseKey);

        //database.Properties = GetJsonProperties.PasswordDecryption(database.Properties);

        var databaseDetailDto = _mapper.Map<DatabaseDetailVm>(database);

        return databaseDetailDto ?? throw new NotFoundException(nameof(Domain.Entities.Database), request.Id);
    }
}