﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class DashboardViewRepositoryMocks
{
    public static Mock<IDashboardViewRepository> CreateDashboardViewRepository(List<DashboardView> dashboardViews)
    {
        var createDashboardViewRepository = new Mock<IDashboardViewRepository>();

        createDashboardViewRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dashboardViews);
        createDashboardViewRepository.Setup(repo => repo.AddAsync(It.IsAny<DashboardView>())).ReturnsAsync((DashboardView dashboardView) =>
            {
                dashboardView.Id = new Fixture().Create<int>();

                dashboardView.ReferenceId = new Fixture().Create<Guid>().ToString();

                dashboardViews.Add(dashboardView);

                return dashboardView;
            });

        return createDashboardViewRepository;
    }

    public static Mock<IDashboardViewRepository> UpdateDashboardViewRepository(List<DashboardView> dashboardViews)
    {
        var updateDashboardViewRepository = new Mock<IDashboardViewRepository>();

        updateDashboardViewRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dashboardViews);
        updateDashboardViewRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.SingleOrDefault(x => x.ReferenceId == i));
        updateDashboardViewRepository.Setup(repo => repo.UpdateAsync(It.IsAny<DashboardView>())).ReturnsAsync((DashboardView dashboardView) =>
            {
                var index = dashboardViews.FindIndex(item => item.ReferenceId == dashboardView.ReferenceId);

                dashboardViews[index] = dashboardView;

                return dashboardView;
            });

        return updateDashboardViewRepository;
    }

    public static Mock<IDashboardViewRepository> DeleteDashboardViewRepository(List<DashboardView> dashboardViews)
    {
        var deleteDashboardViewRepository = new Mock<IDashboardViewRepository>();

        deleteDashboardViewRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dashboardViews);
        deleteDashboardViewRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.SingleOrDefault(x => x.ReferenceId == i));
        deleteDashboardViewRepository.Setup(repo => repo.UpdateAsync(It.IsAny<DashboardView>())).ReturnsAsync((DashboardView dashboardView) =>
            {
                var index = dashboardViews.FindIndex(item => item.ReferenceId == dashboardView.ReferenceId);

                dashboardView.IsActive = false;

                dashboardViews[index] = dashboardView;

                return dashboardView;
            });

        return deleteDashboardViewRepository;
    }

    public static Mock<IDashboardViewRepository> GetDashboardViewRepository(List<DashboardView> dashboardViews)
    {
        var dashboardViewRepository = new Mock<IDashboardViewRepository>();

        dashboardViewRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dashboardViews);
        dashboardViewRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.SingleOrDefault(x => x.ReferenceId == i));

        return dashboardViewRepository;
    }

    public static Mock<IDashboardViewRepository> GetDashboardViewEmptyRepository(List<DashboardView> dashboardViews)
    {
        var dashboardViewRepository = new Mock<IDashboardViewRepository>();

        dashboardViewRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<DashboardView>());

        return dashboardViewRepository;
    }

    public static Mock<IDashboardViewRepository> GetPaginatedDashboardViewRepository(List<DashboardView> dashboardViews)
    {
        var dashboardViewRepository = new Mock<IDashboardViewRepository>();

        var queryableDashboardView = dashboardViews.BuildMock();

        dashboardViewRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableDashboardView);
        
        return dashboardViewRepository;
    }

    public static Mock<IDashboardViewRepository> GetBusinessServiceSummaryReportRepository(List<DashboardView> dashboardViews)
    {
        var businessServiceSummaryReportRepository = new Mock<IDashboardViewRepository>();

        businessServiceSummaryReportRepository.Setup(repo => repo.GetBusinessViewListByBusinessServiceId(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.Where(x => x.BusinessServiceId == i).ToList());
        businessServiceSummaryReportRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dashboardViews);

        return businessServiceSummaryReportRepository;
    }

    public static Mock<IDashboardViewRepository> GetDashboardViewPaginatedListRepository(List<DashboardView> dashboardViews)
    {
        var dashboardViewPaginatedListRepository = new Mock<IDashboardViewRepository>();

        dashboardViewPaginatedListRepository.Setup(repo => repo.GetBusinessViewListByBusinessServiceId(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.Where(x => x.BusinessServiceId == i).ToList());
        dashboardViewPaginatedListRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dashboardViews);
        dashboardViewPaginatedListRepository.Setup(repo => repo.GetBusinessViewListByBusinessFunctionId(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.Where(x => x.BusinessFunctionId == i).ToList());

        return dashboardViewPaginatedListRepository;
    }

    public static Mock<IDashboardViewRepository> GetDataLagStatusByLast7DaysRepository(List<DashboardView> dashboardViews)
    {
        var dataLagStatusByLast7DaysRepository = new Mock<IDashboardViewRepository>();

        dataLagStatusByLast7DaysRepository.Setup(repo => repo.GetBusinessViewByLast7Days()).ReturnsAsync(dashboardViews);

        return dataLagStatusByLast7DaysRepository;
    }

    public static Mock<IDashboardViewRepository> GetDashboardViewByBusinessFunctionIdRepository(List<DashboardView> dashboardViews)
    {
        var dashboardViewByBusinessFunctionIdRepository = new Mock<IDashboardViewRepository>();

        dashboardViewByBusinessFunctionIdRepository.Setup(repo => repo.GetBusinessViewListByBusinessFunctionId(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.Where(x => x.BusinessFunctionId == i).ToList());

        return dashboardViewByBusinessFunctionIdRepository;
    }

    public static Mock<IDashboardViewRepository> GetDashboardViewByBusinessServiceIdRepository(List<DashboardView> dashboardViews)
    {
        var dashboardViewByBusinessServiceIdRepository = new Mock<IDashboardViewRepository>();

        dashboardViewByBusinessServiceIdRepository.Setup(repo => repo.GetBusinessViewListByBusinessServiceId(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.Where(x => x.BusinessServiceId == i).ToList());

        return dashboardViewByBusinessServiceIdRepository;
    }

    public static Mock<IDashboardViewRepository> GetDashboardViewByInfraObjectIdRepository(List<DashboardView> dashboardViews)
    {
        var getDashboardViewByInfraObjectIdRepository = new Mock<IDashboardViewRepository>();

        getDashboardViewByInfraObjectIdRepository.Setup(repo => repo.GetBusinessViewByInfraObjectId(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.SingleOrDefault(x => x.InfraObjectId == i));

        return getDashboardViewByInfraObjectIdRepository;
    }

    public static Mock<IDashboardViewRepository> GetDcMappingListRepository(List<DashboardView> dashboardViews)
    {
        var getDcMappingListRepository = new Mock<IDashboardViewRepository>();

        getDcMappingListRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dashboardViews);
        getDcMappingListRepository.Setup(repo => repo.GetBusinessViewByInfraObjectId(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.SingleOrDefault(x => x.InfraObjectId == i));
        getDcMappingListRepository.Setup(repo => repo.GetBusinessViewListByBusinessServiceId(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.Where(x => x.BusinessServiceId == i).ToList());
        getDcMappingListRepository.Setup(repo => repo.GetBusinessViewListByBusinessFunctionId(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.Where(x => x.BusinessFunctionId == i).ToList());

        return getDcMappingListRepository;
    }

    public static Mock<IDashboardViewRepository> GetItViewListByInfraObjectIdRepository(List<DashboardView> dashboardViews)
    {
        var getItViewListByInfraObjectIdRepository = new Mock<IDashboardViewRepository>();

        getItViewListByInfraObjectIdRepository.Setup(repo => repo.GetBusinessViewByInfraObjectId(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.SingleOrDefault(x => x.InfraObjectId == i));

        return getItViewListByInfraObjectIdRepository;
    }

    public static Mock<IDashboardViewRepository> GetItViewListRepository(List<DashboardView> dashboardViews)
    {
        var getItViewListByInfraObjectIdRepository = new Mock<IDashboardViewRepository>();

        getItViewListByInfraObjectIdRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dashboardViews);
        getItViewListByInfraObjectIdRepository.Setup(repo => repo.GetBusinessViewListByBusinessServiceId(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.Where(x => x.BusinessServiceId == i).ToList());
        getItViewListByInfraObjectIdRepository.Setup(repo => repo.GetBusinessViewListByBusinessFunctionId(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.Where(x => x.BusinessFunctionId == i).ToList());
        getItViewListByInfraObjectIdRepository.Setup(repo => repo.GetBusinessViewByInfraObjectId(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViews.SingleOrDefault(x => x.InfraObjectId == i));

        return getItViewListByInfraObjectIdRepository;
    }

    public static Mock<IDashboardViewRepository> GetInfraObjectSummaryReportRepository(List<DashboardView> dashboardViews)
    {
        var getInfraObjectSummaryReportRepository = new Mock<IDashboardViewRepository>();

        getInfraObjectSummaryReportRepository.Setup(repo => repo.GetInfraObjectSummaryReport()).ReturnsAsync(dashboardViews);

        return getInfraObjectSummaryReportRepository;
    }
}