using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.MenuBuilder.Events.Delete;

public class MenuBuilderDeletedEventHandler : INotificationHandler<MenuBuilderDeletedEvent>
{
    private readonly ILogger<MenuBuilderDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public MenuBuilderDeletedEventHandler(ILoggedInUserService userService, ILogger<MenuBuilderDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(MenuBuilderDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} MenuBuilder",
            Entity = "MenuBuilder",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"MenuBuilder '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"MenuBuilder '{deletedEvent.Name}' deleted successfully.");
    }
}
