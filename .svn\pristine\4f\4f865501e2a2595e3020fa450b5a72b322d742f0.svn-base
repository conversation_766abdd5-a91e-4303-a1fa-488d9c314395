﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-alert-dashboard"></i><span>Workflow Schedule Execution History</span></h6>     
            <div class="d-flex gap-3">
                <div class="form-group mb-0" id="typeoption">
                    <div class="d-flex gap-2">
                        <span class="input-group-text form-label mb-0" for="basic-url"> <i class="cp-activity-type me-1"></i>Type</span>
                        <div class="input-group" style="width:135px">
                            <select aria-label="Default select example" class="form-select" id="typeValue" data-placeholder="Select Type">
                                <option value="All">All</option>
                            </select>
                        </div>
                        
                    </div>
                </div>
                <div class="d-flex gap-2">
                    <div class="input-group" id="startDate">

                        <span class="input-group-text form-label mb-0" for="startDate" cursorshover="true"><i class="cp-calendar me-1"></i>Start&nbsp;Date</span>
                        <input placeholder="Select start date" type="date" id="start_date"
                               class="form-control custom-cursor-default-hover" value="" />
                        <span id="startdate-error" class="mt-5"></span>
                    </div>
                    
                </div>
                <div class="d-flex gap-2">
                    <div class="input-group" id="endDate">

                        <span class="input-group-text form-label mb-0" for="endDate"><i class="cp-calendar me-1"></i>End&nbsp;Date</span>
                        <input placeholder="Select end date" type="date" id="end_date"
                               class="form-control custom-cursor-default-hover" value="" />
                    </div>
                </div>
                <form class="d-flex gap-2">
                    <div class="input-group">
                        <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                        <span class="input-group-text pe-0"><i class="cp-search"></i></span>
                    </div>
                </form>
                @* <button type="button" id="Reset" title="Reset" class="btn btn-primary btn-sm"><i class="cp-reload"></i></button> *@
            </div>
            
        </div>
        <div class="card-body pt-0">
            <!-- chart section end -->
            <div id="collapetable" class="header_filter">
                <table class="table  table-hover dataTable tabledata no-footer" id="WorkflowScheduleExecutionHistorytabledata" style="width:100%">
                    <thead>
                        <tr class="">
                            <th class="SrNo_th">Sr.No</th>
                            <th class="">Workflow Name</th>
                            @* <th class="">Infraobject Name</th> *@
                            <th class="col-md-3">Node Name</th>
                            <th class="">Type</th>
                            @* <th class="">scheduler</th> *@
                            <th class="">Start Time</th>
                            <th class="">End Time</th>
                            <th class="">Status</th>
                            <th class="">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

@*     <!--Notification-->
    <div class='Notification'>
        <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' aria-atomic='true'>
            <div class='d-flex'>
                <div class='toast-body'>
                    <span id="alertClass" class='success-toast'>
                        <i id="icon" class='cp-check toast_icon'></i>
                    </span>
                    <span id="message">
                    </span>
                </div>
                <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
            </div>
        </div>
    </div> *@
    <!--Modal Error-->

    <div class="modal fade" id="ErrorModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-md modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-error-message"></i><span>Error Message</span></h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="list-group list-group-flush Profile-Select">
                        <div class="d-grid text-start">
                            <p id="error_message"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/itautomation/workflowscheduleexecutionhistory/workflowscheduleexecutionhistory.js"></script>