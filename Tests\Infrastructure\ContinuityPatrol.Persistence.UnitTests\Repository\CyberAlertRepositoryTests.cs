using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using FluentValidation;
using System.Threading.Tasks;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class CyberAlertRepositoryTests : IClassFixture<CyberAlertFixture>
{
    private readonly CyberAlertFixture _cyberAlertFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CyberAlertRepository _repository;

    public CyberAlertRepositoryTests(CyberAlertFixture cyberAlertFixture)
    {
        _cyberAlertFixture = cyberAlertFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CyberAlertRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region GetPaginatedListBySeverity Tests

    [Fact]
    public async Task GetPaginatedListBySeverity_ShouldReturnPaginatedResults()
    {
        // Arrange
        var alerts =_cyberAlertFixture.CyberAlertPaginationList;

        alerts[0].Severity = "Critical";
        alerts[1].Severity = "Critical";
        alerts[2].Severity = "Information";

        await _repository.AddRangeAsync(alerts);

       var productFilterSpec = new CyberAlertFilterSpecification("");
      
        // Act
        var result = await _repository.GetPaginatedListBySeverity(1, 10, productFilterSpec, "Critical", "Id", "desc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Data.Count);
        Assert.All(result.Data, x => Assert.Equal("Critical", x.Severity));
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(10, result.PageSize);
        Assert.Equal(2, result.TotalCount);
    }

    [Fact]
    public async Task GetPaginatedListBySeverity_ShouldReturnEmpty_WhenNoMatchingSeverity()
    {
        // Arrange
        var alerts = _cyberAlertFixture.CyberAlertList;
        await _repository.AddRangeAsync(alerts);

        var productFilterSpec = new CyberAlertFilterSpecification("");
        // Act
        var result = await _repository.GetPaginatedListBySeverity(1, 10, productFilterSpec, "NonExistentSeverity", "Id", "desc");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.Data);
        Assert.Equal(0, result.TotalCount);
    }

    [Fact]
    public async Task GetPaginatedListBySeverity_QueryableVersion_ShouldReturnFilteredQueryable()
    {
        // Arrange
        var alerts=_cyberAlertFixture.CyberAlertPaginationList;
       

        alerts[0].Severity = "High";
        alerts[1].Severity = "Critical";

        await _repository.AddRangeAsync(alerts);
   
        // Act
        var result = _repository.GetPaginatedListBySeverity("High");

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Single(resultList);
        Assert.Equal("High", resultList.First().Severity);
    }

    #endregion


    #region Severity and Alert Type Tests

    [Fact]
    public async Task Repository_ShouldFilterBySeverity()
    {
        // Arrange
        var alerts = _cyberAlertFixture.CyberAlertPaginationList;


        alerts[0].Severity = "High";
        alerts[1].Severity = "Critical";
        alerts[1].Severity = "Critical";
        await _repository.AddRangeAsync(alerts);

        // Act
        var criticalAlerts = await _repository.FindByFilterAsync(x => x.Severity == "Critical");
        var highAlerts = await _repository.FindByFilterAsync(x => x.Severity == "High");
    

        // Assert
        Assert.Single(criticalAlerts);
        Assert.Single(highAlerts);

        Assert.Equal("Critical", criticalAlerts.First().Severity);
        Assert.Equal("High", highAlerts.First().Severity);
    }

    [Fact]
    public async Task Repository_ShouldFilterByAlertType()
    {
        // Arrange
        var alerts = _cyberAlertFixture.CyberAlertPaginationList;


        alerts[0].Type = "ADPasswordExpire";
        alerts[1].Type = "Test_Cyber";
        alerts[2].Type = "Test_Cyber1";
        await _repository.AddRangeAsync(alerts);

        // Act
        var adtypeAlerts = await _repository.FindByFilterAsync(x => x.Type == "ADPasswordExpire");
        var test1Alerts = await _repository.FindByFilterAsync(x => x.Type == "Test_Cyber");
        var test2Alerts = await _repository.FindByFilterAsync(x => x.Type == "Test_Cyber1");

        // Assert
        Assert.Single(adtypeAlerts);
        Assert.Single(test2Alerts);
        Assert.Single(test1Alerts);
        Assert.Equal("ADPasswordExpire", adtypeAlerts.First().Type);
        Assert.Equal("Test_Cyber", test1Alerts.First().Type);
        Assert.Equal("Test_Cyber1", test2Alerts.First().Type);
    }

    #endregion
}

