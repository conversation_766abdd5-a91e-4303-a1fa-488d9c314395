using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class WorkflowTempRepositoryTests : IClassFixture<WorkflowTempFixture>
{
    private readonly WorkflowTempFixture _workflowTempFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly WorkflowTempRepository _repository;

    public WorkflowTempRepositoryTests(WorkflowTempFixture workflowTempFixture)
    {
        _workflowTempFixture = workflowTempFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new WorkflowTempRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_ReturnsNull_WhenNoData()
    {
        var repository = new WorkflowTempRepository(_dbContext, DbContextFactory.GetMockUserService());

        var result = await repository.GetWorkflowTempByRequestId("NON_EXISTENT_REQUEST_ID");
        Assert.Null(result);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_ReturnsWorkflowTemp_WhenRequestIdExists()
    {
        var requestId = "REQUEST_123";
        _workflowTempFixture.WorkflowTempDto.RequestId = requestId;
        _workflowTempFixture.WorkflowTempDto.IsActive = true;

        await _dbContext.WorkflowTemps.AddAsync(_workflowTempFixture.WorkflowTempDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowTempByRequestId(requestId);

        Assert.NotNull(result);
        Assert.Equal(requestId, result.RequestId);
        Assert.Equal(_workflowTempFixture.WorkflowTempDto.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_ReturnsNull_WhenRequestIdDoesNotExist()
    {
        var requestId = "REQUEST_123";
        _workflowTempFixture.WorkflowTempDto.RequestId = requestId;
        _workflowTempFixture.WorkflowTempDto.IsActive = true;

        await _dbContext.WorkflowTemps.AddAsync(_workflowTempFixture.WorkflowTempDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowTempByRequestId("DIFFERENT_REQUEST_ID");

        Assert.Null(result);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_ReturnsOnlyActiveWorkflowTemp()
    {
        var requestId = "REQUEST_123";

        // Create active workflow temp
        var activeWorkflowTemp = new WorkflowTemp
        {
            RequestId = requestId,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Active Workflow Temp"
        };

        // Create inactive workflow temp with same request ID
        var inactiveWorkflowTemp = new WorkflowTemp
        {
            RequestId = requestId,
            IsActive = false,
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Inactive Workflow Temp"
        };

        await _dbContext.WorkflowTemps.AddRangeAsync(new[] { activeWorkflowTemp, inactiveWorkflowTemp });
        await _dbContext.SaveChangesAsync();

        // The SaveChangesAsync automatically sets IsActive = true for all entities, so we need to manually update the inactive one
        inactiveWorkflowTemp.IsActive = false;
        _dbContext.WorkflowTemps.Update(inactiveWorkflowTemp);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowTempByRequestId(requestId);

        Assert.NotNull(result);
        Assert.True(result.IsActive);
        Assert.Equal("Active Workflow Temp", result.Name);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_ReturnsFirstMatch_WhenMultipleActiveExist()
    {
        var requestId = "REQUEST_123";
        
        var workflowTemp1 = new WorkflowTemp
        {
            RequestId = requestId,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "First Workflow Temp"
        };

        var workflowTemp2 = new WorkflowTemp
        {
            RequestId = requestId,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Second Workflow Temp"
        };

        await _dbContext.WorkflowTemps.AddRangeAsync(new[] { workflowTemp1, workflowTemp2 });
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowTempByRequestId(requestId);

        Assert.NotNull(result);
        Assert.Equal(requestId, result.RequestId);
        Assert.True(result.IsActive);
        // Should return one of the active workflow temps
        Assert.Contains(result.Name, new[] { "First Workflow Temp", "Second Workflow Temp" });
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_IsCaseSensitive()
    {
        var requestId = "REQUEST_123";
        _workflowTempFixture.WorkflowTempDto.RequestId = requestId;
        _workflowTempFixture.WorkflowTempDto.IsActive = true;

        await _dbContext.WorkflowTemps.AddAsync(_workflowTempFixture.WorkflowTempDto);
        await _dbContext.SaveChangesAsync();

        var result1 = await _repository.GetWorkflowTempByRequestId(requestId);
        var result2 = await _repository.GetWorkflowTempByRequestId(requestId.ToLower());

        Assert.NotNull(result1);
        Assert.Null(result2);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_HandlesNullRequestId()
    {
        var result = await _repository.GetWorkflowTempByRequestId(null);
        Assert.Null(result);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_HandlesEmptyRequestId()
    {
        var result = await _repository.GetWorkflowTempByRequestId("");
        Assert.Null(result);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_ReturnsCorrectProperties()
    {
        var requestId = "REQUEST_123";
        var workflowTemp = new WorkflowTemp
        {
            RequestId = requestId,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Test Workflow Temp",
            Properties = "{\"test\": \"value\"}",
            WorkflowId = "WORKFLOW_TEST",
            ApprovalMatrixId = "APPROVAL_TEST",
            Version = "1.5",
            CreatedBy = "TestUser",
            CreatedDate = DateTime.Now.AddDays(-1)
        };

        await _dbContext.WorkflowTemps.AddAsync(workflowTemp);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowTempByRequestId(requestId);

        Assert.NotNull(result);
        Assert.Equal(requestId, result.RequestId);
        Assert.Equal(workflowTemp.ReferenceId, result.ReferenceId);
        Assert.Equal("Test Workflow Temp", result.Name);
        Assert.Equal("{\"test\": \"value\"}", result.Properties);
        Assert.Equal("WORKFLOW_TEST", result.WorkflowId);
        Assert.Equal("APPROVAL_TEST", result.ApprovalMatrixId);
        Assert.Equal("1.5", result.Version);
        Assert.Equal("USER_456", result.CreatedBy);  // Mock user service returns USER_456
        Assert.True(result.IsActive);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_UsesAsNoTracking()
    {
        var requestId = "REQUEST_123";
        _workflowTempFixture.WorkflowTempDto.RequestId = requestId;
        _workflowTempFixture.WorkflowTempDto.IsActive = true;

        await _dbContext.WorkflowTemps.AddAsync(_workflowTempFixture.WorkflowTempDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowTempByRequestId(requestId);

        Assert.NotNull(result);
        // Verify that the entity is not being tracked
        var entry = _dbContext.Entry(result);
        Assert.Equal(Microsoft.EntityFrameworkCore.EntityState.Detached, entry.State);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_ReturnsWorkflowTempWithAllProperties()
    {
        var requestId = "REQUEST_FULL_PROPS";
        var workflowTemp = new WorkflowTemp
        {
            RequestId = requestId,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Complete Workflow Temp",
            Properties = "{\"key1\": \"value1\", \"key2\": \"value2\"}",
            WorkflowId = "WORKFLOW_123",
            ApprovalMatrixId = "APPROVAL_456",
            Version = "1.0",
            CreatedBy = "USER_456",
            CreatedDate = DateTime.Now.AddDays(-2),
            LastModifiedBy = "USER_456",
            LastModifiedDate = DateTime.Now.AddDays(-1)
        };

        await _dbContext.WorkflowTemps.AddAsync(workflowTemp);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowTempByRequestId(requestId);

        Assert.NotNull(result);
        Assert.Equal(requestId, result.RequestId);
        Assert.Equal(workflowTemp.ReferenceId, result.ReferenceId);
        Assert.Equal("Complete Workflow Temp", result.Name);
        Assert.Equal("{\"key1\": \"value1\", \"key2\": \"value2\"}", result.Properties);
        Assert.Equal("WORKFLOW_123", result.WorkflowId);
        Assert.Equal("APPROVAL_456", result.ApprovalMatrixId);
        Assert.Equal("1.0", result.Version);
        Assert.Equal("USER_456", result.CreatedBy);  // Mock user service returns USER_456
        Assert.Equal("USER_456", result.LastModifiedBy);  // SaveChangesAsync sets this to CreatedBy value
        Assert.True(result.IsActive);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_HandlesSpecialCharactersInRequestId()
    {
        var requestId = "REQUEST_!@#$%^&*()_+-=[]{}|;':\",./<>?";
        _workflowTempFixture.WorkflowTempDto.RequestId = requestId;
        _workflowTempFixture.WorkflowTempDto.IsActive = true;

        await _dbContext.WorkflowTemps.AddAsync(_workflowTempFixture.WorkflowTempDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowTempByRequestId(requestId);

        Assert.NotNull(result);
        Assert.Equal(requestId, result.RequestId);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_HandlesUnicodeCharactersInRequestId()
    {
        var requestId = "REQUEST_测试_🚀_αβγ";
        _workflowTempFixture.WorkflowTempDto.RequestId = requestId;
        _workflowTempFixture.WorkflowTempDto.IsActive = true;

        await _dbContext.WorkflowTemps.AddAsync(_workflowTempFixture.WorkflowTempDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowTempByRequestId(requestId);

        Assert.NotNull(result);
        Assert.Equal(requestId, result.RequestId);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_HandlesVeryLongRequestId()
    {
        var requestId = new string('A', 500); // Very long request ID
        _workflowTempFixture.WorkflowTempDto.RequestId = requestId;
        _workflowTempFixture.WorkflowTempDto.IsActive = true;

        await _dbContext.WorkflowTemps.AddAsync(_workflowTempFixture.WorkflowTempDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowTempByRequestId(requestId);

        Assert.NotNull(result);
        Assert.Equal(requestId, result.RequestId);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_ReturnsNullForInactiveWorkflowTemp()
    {
        var requestId = "REQUEST_INACTIVE";
        var inactiveWorkflowTemp = new WorkflowTemp
        {
            RequestId = requestId,
            IsActive = false,
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Inactive Workflow Temp"
        };

        await _dbContext.WorkflowTemps.AddAsync(inactiveWorkflowTemp);
        await _dbContext.SaveChangesAsync();

        // The SaveChangesAsync automatically sets IsActive = true, so we need to manually update it to false
        inactiveWorkflowTemp.IsActive = false;
        _dbContext.WorkflowTemps.Update(inactiveWorkflowTemp);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowTempByRequestId(requestId);

        Assert.Null(result);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_ReturnsCorrectWorkflowTempFromMultipleWorkflows()
    {
        var requestId = "REQUEST_MULTI_WORKFLOW";

        var workflowTemp1 = new WorkflowTemp
        {
            RequestId = requestId,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Workflow 1",
            WorkflowId = "WORKFLOW_1",
            Version = "1.0"
        };

        var workflowTemp2 = new WorkflowTemp
        {
            RequestId = requestId,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Workflow 2",
            WorkflowId = "WORKFLOW_2",
            Version = "2.0"
        };

        await _dbContext.WorkflowTemps.AddRangeAsync(new[] { workflowTemp1, workflowTemp2 });
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowTempByRequestId(requestId);

        Assert.NotNull(result);
        Assert.Equal(requestId, result.RequestId);
        // Should return one of the workflow temps (FirstOrDefault behavior)
        Assert.Contains(result.WorkflowId, new[] { "WORKFLOW_1", "WORKFLOW_2" });
        Assert.Contains(result.Version, new[] { "1.0", "2.0" });
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_PerformanceTestWithLargeDataset()
    {
        var targetRequestId = "TARGET_REQUEST";
        var workflowTemps = new List<WorkflowTemp>();

        // Create a large dataset
        for (int i = 0; i < 1000; i++)
        {
            workflowTemps.Add(new WorkflowTemp
            {
                RequestId = $"REQUEST_{i}",
                IsActive = true,
                ReferenceId = Guid.NewGuid().ToString(),
                Name = $"Workflow {i}"
            });
        }

        // Add the target workflow temp
        workflowTemps.Add(new WorkflowTemp
        {
            RequestId = targetRequestId,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Target Workflow"
        });

        await _dbContext.WorkflowTemps.AddRangeAsync(workflowTemps);
        await _dbContext.SaveChangesAsync();

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = await _repository.GetWorkflowTempByRequestId(targetRequestId);
        stopwatch.Stop();

        Assert.NotNull(result);
        Assert.Equal(targetRequestId, result.RequestId);
        Assert.Equal("Target Workflow", result.Name);
        // Performance assertion - should complete within reasonable time
        Assert.True(stopwatch.ElapsedMilliseconds < 1000, $"Query took {stopwatch.ElapsedMilliseconds}ms, expected < 1000ms");
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_HandlesWhitespaceOnlyRequestId()
    {
        var requestId = "   ";
        var result = await _repository.GetWorkflowTempByRequestId(requestId);
        Assert.Null(result);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_HandlesRequestIdWithLeadingTrailingSpaces()
    {
        var requestIdWithSpaces = "  REQUEST_SPACES  ";
        var requestIdTrimmed = "REQUEST_SPACES";

        _workflowTempFixture.WorkflowTempDto.RequestId = requestIdTrimmed;
        _workflowTempFixture.WorkflowTempDto.IsActive = true;

        await _dbContext.WorkflowTemps.AddAsync(_workflowTempFixture.WorkflowTempDto);
        await _dbContext.SaveChangesAsync();

        // Should not match due to exact string comparison
        var result = await _repository.GetWorkflowTempByRequestId(requestIdWithSpaces);
        Assert.Null(result);

        // Should match exact string
        var result2 = await _repository.GetWorkflowTempByRequestId(requestIdTrimmed);
        Assert.NotNull(result2);
    }

    [Fact]
    public async Task GetWorkflowTempByRequestId_ReturnsWorkflowTempWithNullProperties()
    {
        var requestId = "REQUEST_NULL_PROPS";
        var workflowTemp = new WorkflowTemp
        {
            RequestId = requestId,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Workflow with Nulls",
            Properties = null,
            WorkflowId = null,
            ApprovalMatrixId = null,
            Version = null,
            CreatedBy = null,
            LastModifiedBy = null
        };

        await _dbContext.WorkflowTemps.AddAsync(workflowTemp);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowTempByRequestId(requestId);

        Assert.NotNull(result);
        Assert.Equal(requestId, result.RequestId);
        Assert.Equal("Workflow with Nulls", result.Name);
        Assert.Null(result.Properties);
        Assert.Null(result.WorkflowId);
        Assert.Null(result.ApprovalMatrixId);
        Assert.Null(result.Version);
        Assert.Equal("USER_456", result.CreatedBy);  // SaveChangesAsync automatically sets this
        Assert.Equal("USER_456", result.LastModifiedBy);  // SaveChangesAsync automatically sets this
    }
}
