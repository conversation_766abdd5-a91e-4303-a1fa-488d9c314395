﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/dashboard.css" rel="stylesheet" />
@Html.AntiForgeryToken()

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
   
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title" title="Azure Database for Postgres Replication-PaaS-Monitoirng">
            <i class="cp-monitoring"></i><span>Azure Database for Postgres Replication-PaaS-Monitoirng :</span>
            <span id="infraName"></span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time :"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>
    </div>
    <div class="monitor_pages">
        <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row g-2 mt-0">
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                       PostgreSQL Replication Monitoring
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>Replication Monitoring</th>
                                    <th class="text-primary">Primary</th>
                                    <th>Replica</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-ip-address me-1"></i>PostgreSQL Server Name </td>
                                    <td class="text-truncate" id="ServerName"></td>
                                    <td class="text-truncate" id="DR_ServerName"></td>

                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-roate-settings me-1"></i>PostgreSQL Server Version</td>
                                    <td class="text-truncate" id="ServerVersion"><i class="text-danger cp-disable me-1"></i></td>
                                    <td class="text-truncate" id="DR_ServerVersion"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-ip-address me-1"></i>PostgreSQL Server FQDN
                                    </td>
                                    <td class="text-truncate" id="ServerFQDN"><i class="text-danger cp-disable me-1"></i></td>
                                    <td class="text-truncate" id="DR_ServerFQDN"><i class="text-danger cp-disable me-1"></i></td>

                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-database-success me-1"></i>PostgreSQL Server Resource Group Name
                                    </td>
                                    <td class="text-truncate" id="ServerResourceGroupName"></td>
                                    <td class="text-truncate" id="DR_ServerResourceGroupName"><i class="text-danger cp-disable me-1"></i></td>

                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-ip-address me-1"></i>PostgreSQL Server Location (Region)
                                    </td>
                                    <td class="text-truncate" id="ServerLocation"></td>
                                    <td class="text-truncate" id="DR_ServerLocation"></td>

                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-database-success me-1"></i>PostgreSQL Server Status
                                    </td>
                                    <td class="text-truncate" id="ServerStatus"><i class="text-danger cp-disable me-1"></i></td>
                                    <td class="text-truncate" id="DR_ServerStatus"></td>

                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-estimated-time me-1"></i>PostgreSQL Replication Role
                                    </td>
                                    <td class="text-truncate" id="ReplicationRole"><i class="text-danger cp-disable me-1"></i></td>
                                    <td class="text-truncate" id="DR_ReplicationRole"><i class="text-danger cp-disable me-1"></i></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-estimated-time me-1"></i>DataLag (sec)
                                    </td>
                                    <td class="text-truncate" id="Datalag"><i class="text-danger cp-disable me-1"></i></td>
                                    <td class="text-truncate" id="DR_Datalag"><i class="text-danger cp-disable me-1"></i></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center pt-0">
                        @* <img src="~/img/isomatric/solutiondiagram.svg" height="159px;" /> *@
                        <div id="Solution_Diagram" class="h-100 w-100"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 d-grid">
            <div class="card Card_Design_None mb-2 h-100" id="mssqlserver">
                <div class="card-header card-title" title="Service/Process/Workflow">Service/Process/Workflow</div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0 noDataimg" style="table-layout:fixed" id="tableCluster">
                        <thead class="align-middle">
                            <tr>
                                <th rowspan="2">Service / Process / Workflow Name</th>
                                <th colspan="2" class="text-center">Server IP/HostName</th>
                            </tr>
                            <tr>
                                <th id="prIp"></th>
                                <th id="drIp"></th>
                            </tr>
                        </thead>
                        <tbody id="mssqlserverbody">
                        </tbody>

                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>
<script src="~/js/Monitoring/MonitoringPostgresAzure.js"></script>
<script src="~/js/Monitoring/MonitoringServiceDetails.js"></script>