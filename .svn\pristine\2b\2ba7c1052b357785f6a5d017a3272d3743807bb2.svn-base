﻿using Microsoft.AspNetCore.Mvc;
using Moq;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using ContinuityPatrol.Application.Features.VeritasCluster.Commands.Create;
using ContinuityPatrol.Application.Features.VeritasCluster.Commands.Update;
using ContinuityPatrol.Application.Features.VeritasCluster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.VeritasClusterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Provider;
using Microsoft.Extensions.Logging;
using AutoMapper;
using MediatR;
using ContinuityPatrol.Domain.ViewModels.ServerModel;
using ContinuityPatrol.Shared.Tests.Fakes;
using Microsoft.AspNetCore.Http;
using AutoFixture;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class VeritasClusterControllerTests
    {
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<ILogger<VeritasClusterController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private VeritasClusterController _controller;

        public VeritasClusterControllerTests()
        {
           
            _controller = new VeritasClusterController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public void  List_ReturnsViewResult()
        {
            // Act
            var result = _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public async Task GetPaginated_ReturnsJsonResult()
        {
            // Arrange
            var query = new GetVeritasClusterPaginatedListQuery();
            var paginatedList = new PaginatedResult<VeritasClusterListVm>(); 
            _mockDataProvider.Setup(dp => dp.VeritasCluster.GetPaginatedVeritasClusters(It.IsAny<GetVeritasClusterPaginatedListQuery>()))
                .ReturnsAsync(paginatedList);

            // Act
            var result = await _controller.GetPaginated(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(paginatedList, jsonResult.Value);
        }

        [Fact]
        public async Task CreateOrUpdate_CreateVeritasCluster_ReturnsRedirectToActionResult()
        {
            
            var veritasClusterModel = new AutoFixture.Fixture().Create<VeritasClusterViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateVeritasClusterCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateVeritasClusterCommand>(It.IsAny<VeritasClusterViewModel>()))
                .Returns(command);
            _mockDataProvider.Setup(dp => dp.VeritasCluster.CreateAsync(It.IsAny<CreateVeritasClusterCommand>()))
                .ReturnsAsync(response);

            
            var result = await _controller.CreateOrUpdate(veritasClusterModel) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdateVeritasCluster_ReturnsRedirectToActionResult()
        {

            var veritasClusterModel = new AutoFixture.Fixture().Create<VeritasClusterViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id","22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateVeritasClusterCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateVeritasClusterCommand>(It.IsAny<VeritasClusterViewModel>()))
                .Returns(command);
            _mockDataProvider.Setup(dp => dp.VeritasCluster.UpdateAsync(It.IsAny<UpdateVeritasClusterCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(veritasClusterModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task GetServerList_ReturnsJsonResult()
        {
            
            var serverList = new List<ServerListVm>(); 
            _mockDataProvider.Setup(dp => dp.Server.GetServerList())
                .ReturnsAsync(serverList);

            // Act
            var result = await _controller.GetServerList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(serverList, jsonResult.Value);
        }

        [Fact]
        public async Task Delete_ReturnsRedirectToActionResult()
        {
            // Arrange
            var id = "123";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };

            _mockDataProvider.Setup(dp => dp.VeritasCluster.DeleteAsync(It.IsAny<string>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task IsVeritasClusterNameExist_ReturnsBoolean()
        {
            // Arrange
            var name = "Cluster1";
            var id = "123";
            _mockDataProvider.Setup(dp => dp.VeritasCluster.IsVeritasClusterNameExist(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.IsVeritasClusterNameExist(name, id);

            // Assert
            Assert.True(result);
        }
    }
}
