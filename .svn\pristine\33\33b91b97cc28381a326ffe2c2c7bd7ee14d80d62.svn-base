﻿using ContinuityPatrol.Application.Features.WorkflowActionType.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowActionType.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowActionType.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowActionType.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowActionType.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.WorkflowActionType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionTypeModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class WorkflowActionTypeController : CommonBaseController
{
    [HttpGet]
    public async Task<ActionResult<List<WorkflowActionTypeListVm>>> GetWorkflowActionTypes()
    {
        Logger.LogDebug("Get All WorkflowActionTypes");

        //return Ok(await Cache.GetOrAddAsync(
        //    ApplicationConstants.Cache.AllWorkflowActionTypesCacheKey + LoggedInUserService.CompanyId,
        //    () => Mediator.Send(new GetWorkflowActionTypeListQuery()), CacheExpiry));

        return Ok(await  Mediator.Send(new GetWorkflowActionTypeListQuery()));
    }

    [HttpPost]
    public async Task<ActionResult<CreateWorkflowActionTypeResponse>> CreateWorkflowActionTypes(
        [FromBody] CreateWorkflowActionTypeCommand createWorkflowActionTypesCommand)
    {
        Logger.LogDebug($"Create WorkflowActionTypes '{createWorkflowActionTypesCommand.ActionType}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateWorkflowActionTypes),
            await Mediator.Send(createWorkflowActionTypesCommand));
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult<DeleteWorkflowActionTypeResponse>> DeleteWorkflowActionType(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "WorkflowActionType Id");

        Logger.LogDebug($"Delete WorkflowActionType Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteWorkflowActionTypeCommand { Id = id }));
    }

    [HttpPut]
    public async Task<ActionResult<UpdateWorkflowActionTypeResponse>> UpdateWorkflowActionType(
        [FromBody] UpdateWorkflowActionTypeCommand updateWorkflowActionTypeCommand)
    {
        Logger.LogDebug($"Update WorkflowActionType '{updateWorkflowActionTypeCommand.ActionType}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateWorkflowActionTypeCommand));
    }
    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<PaginatedResult<WorkflowActionTypeListVm>>> GetPaginatedWorkflowActionType([FromQuery] GetWorkflowActionTypePaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in WorkflowActionType Paginated List");

        return Ok(await Mediator.Send(query));
    }
    [Route("name-exist"), HttpGet]
    public async Task<ActionResult> IsWorkflowActionTypeExist(string actionType, string? id)
    {
        Guard.Against.NullOrWhiteSpace(actionType, "WorkflowActionType");

        Logger.LogDebug($"Check Name Exists Detail by WorkflowActionType '{actionType}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetWorkflowActionTypeNameUniqueQuery { ActionType = actionType, ActionTypeId = id }));
    }
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys =
            { ApplicationConstants.Cache.AllWorkflowActionTypesCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}