﻿using ContinuityPatrol.Application.Features.AlertReceiver.Event.PaginatedView;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertReceiver.Events;

public class PaginatedAlertReceiverEventTests : IClassFixture<AlertReceiverFixture>, IClassFixture<UserActivityFixture>
{
    private readonly AlertReceiverFixture _alertReceiverFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly AlertReceiverPaginatedEventHandler _handler;

    public PaginatedAlertReceiverEventTests(AlertReceiverFixture alertReceiverFixture,
        UserActivityFixture userActivityFixture)
    {
        _alertReceiverFixture = alertReceiverFixture;

        _userActivityFixture = userActivityFixture;

        _mockUserActivityRepository = new Mock<IUserActivityRepository>();

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockAlertReceiverEventLogger = new Mock<ILogger<AlertReceiverPaginatedEventHandler>>();

        _mockUserActivityRepository =
            AlertReceiverRepositoryMocks.CreateAlertReceiverEventRepository(_userActivityFixture.UserActivities);

        _handler = new AlertReceiverPaginatedEventHandler(mockAlertReceiverEventLogger.Object,
            _mockUserActivityRepository.Object, mockLoggedInUserService.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_PaginateAlertReceiverEventPaginated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_alertReceiverFixture.AlertReceiverPaginatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_alertReceiverFixture.AlertReceiverPaginatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}