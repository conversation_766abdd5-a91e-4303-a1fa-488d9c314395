﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.MonitorService.Event.PaginatedView;

public class MonitorServicePaginatedEventHandler : INotificationHandler<MonitorServicePaginatedEvent>
{
    private readonly ILogger<MonitorServicePaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public MonitorServicePaginatedEventHandler(ILogger<MonitorServicePaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(MonitorServicePaginatedEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.MonitorService.ToString(),
            Action = $"{ActivityType.View} {Modules.MonitorService}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Monitoring Services viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Monitoring Services viewed");
    }
}