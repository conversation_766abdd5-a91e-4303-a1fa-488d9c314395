﻿using ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.DRReadyStatus.Commands;

public class UpdateDrReadyStatusTests : IClassFixture<DrReadyStatusFixture>
{
    private readonly DrReadyStatusFixture _drReadyStatusFixture;

    private readonly Mock<IDrReadyStatusRepository> _mockDrReadyStatusRepository;

    private readonly UpdateDRReadyStatusCommandHandler _handler;

    public UpdateDrReadyStatusTests(DrReadyStatusFixture drReadyStatusFixture)
    {
        _drReadyStatusFixture = drReadyStatusFixture;

        _mockDrReadyStatusRepository = DrReadyStatusRepositoryMocks.UpdateDrReadyStatusRepository(_drReadyStatusFixture.DrReadyStatuses);

        _handler = new UpdateDRReadyStatusCommandHandler(_drReadyStatusFixture.Mapper, _mockDrReadyStatusRepository.Object);
    }

    [Fact]
    public async Task Handle_ValidDRReadyStatus_UpdateReferenceIdAsync_ToDRReadyStatusRepo()
    {
        _drReadyStatusFixture.UpdateDrReadyStatusCommand.Id = _drReadyStatusFixture.DrReadyStatuses[0].ReferenceId;

        var result = await _handler.Handle(_drReadyStatusFixture.UpdateDrReadyStatusCommand, CancellationToken.None);

        var drReadyStatus = await _mockDrReadyStatusRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_drReadyStatusFixture.UpdateDrReadyStatusCommand.BusinessServiceName, drReadyStatus.BusinessServiceName);
    }

    [Fact]
    public async Task Handle_Return_ValidDRReadyStatusResponse_WhenUpdate_DRReadyStatus()
    {
        _drReadyStatusFixture.UpdateDrReadyStatusCommand.Id = _drReadyStatusFixture.DrReadyStatuses[0].ReferenceId;

        var result = await _handler.Handle(_drReadyStatusFixture.UpdateDrReadyStatusCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateDRReadyStatusResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_drReadyStatusFixture.UpdateDrReadyStatusCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidDRReadyStatusId()
    {
        _drReadyStatusFixture.UpdateDrReadyStatusCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_drReadyStatusFixture.UpdateDrReadyStatusCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        _drReadyStatusFixture.UpdateDrReadyStatusCommand.Id = _drReadyStatusFixture.DrReadyStatuses[0].ReferenceId;

        await _handler.Handle(_drReadyStatusFixture.UpdateDrReadyStatusCommand, CancellationToken.None);

        _mockDrReadyStatusRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockDrReadyStatusRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.DRReadyStatus>()), Times.Once);
    }
}