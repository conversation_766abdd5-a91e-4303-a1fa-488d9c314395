using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Events.Create;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Create;

public class CreateApprovalMatrixUsersCommandHandler : IRequestHandler<CreateApprovalMatrixUsersCommand, CreateApprovalMatrixUsersResponse>
{
    private readonly IApprovalMatrixUsersRepository _approvalMatrixUsersRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateApprovalMatrixUsersCommandHandler(IMapper mapper, IApprovalMatrixUsersRepository approvalMatrixUsersRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _approvalMatrixUsersRepository = approvalMatrixUsersRepository;
    }

    public async Task<CreateApprovalMatrixUsersResponse> Handle(CreateApprovalMatrixUsersCommand request, CancellationToken cancellationToken)
    {
        var approvalMatrixUsers = _mapper.Map<List<Domain.Entities.ApprovalMatrixUsers>>(request.ApprovalMatrixUsers);

         await _approvalMatrixUsersRepository.AddRangeAsync(approvalMatrixUsers);

         var usersString = string.Join(", ", approvalMatrixUsers.Select(x => x.UserName));

        var response = new CreateApprovalMatrixUsersResponse
        {
            Message = Message.Create(nameof(Domain.Entities.ApprovalMatrixUsers), usersString),

           // Id = approvalMatrixUsers.ReferenceId
        };

        await _publisher.Publish(new ApprovalMatrixUsersCreatedEvent { UserName = usersString }, cancellationToken);

        return response;
    }
}
