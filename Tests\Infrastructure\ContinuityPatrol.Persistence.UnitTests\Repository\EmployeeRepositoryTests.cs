using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class EmployeeRepositoryTests : IClassFixture<EmployeeFixture>
{
    private readonly EmployeeFixture _employeeFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly EmployeeRepository _repository;

    public EmployeeRepositoryTests(EmployeeFixture employeeFixture)
    {
        _employeeFixture = employeeFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new EmployeeRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnExpectedCount_WhenEmployeesExist()
    {
        await _dbContext.Employees.AddRangeAsync(_employeeFixture.EmployeeList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoEmployeesExist()
    {
        var result = await _repository.ListAllAsync();

        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsOnlyActiveEmployees_WhenInactiveEmployeesExist()
    {
        _employeeFixture.EmployeeList[0].IsActive = true;
 
        _employeeFixture.EmployeeList[2].IsActive = true;

        await _dbContext.Employees.AddRangeAsync(_employeeFixture.EmployeeList);
        await _dbContext.SaveChangesAsync();
        _employeeFixture.EmployeeList[1].IsActive = false;
        _dbContext.Employees.UpdateRange(_employeeFixture.EmployeeList);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.ListAllAsync();

        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsEmployee_WhenExists()
    {
        _dbContext.Employees.Add(_employeeFixture.EmployeeDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(_employeeFixture.EmployeeDto.ReferenceId);

        Assert.NotNull(result);
        Assert.Equal(_employeeFixture.EmployeeDto.ReferenceId, result.ReferenceId);
        Assert.Equal(_employeeFixture.EmployeeDto.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenReferenceIdDoesNotExist()
    {
        var nonExistentReferenceId = Guid.NewGuid().ToString();

        var result = await _repository.GetByReferenceIdAsync(nonExistentReferenceId);

        Assert.Null(result);
    }


    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ReturnsEmployee_WhenExists()
    {
        _employeeFixture.EmployeeDto.Id = 1;
        await _dbContext.Employees.AddAsync(_employeeFixture.EmployeeDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByIdAsync(1);

        Assert.NotNull(result);
        Assert.Equal(_employeeFixture.EmployeeDto.Id, result.Id);
        Assert.Equal(_employeeFixture.EmployeeDto.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenRecordDoesNotExist()
    {
        var nonExistentId = 999;
        var result = await _repository.GetByIdAsync(nonExistentId);

        Assert.Null(result);
    }


    #endregion

    #region AddAsync Tests


    [Fact]
    public async Task AddAsync_ShouldAddEmployee_WhenValidEmployee()
    {
        var employee = _employeeFixture.EmployeeDto;
        employee.Name = "John Doe";

        var result = await _repository.AddAsync(employee);
        await _dbContext.SaveChangesAsync();

        Assert.NotNull(result);
        Assert.Equal(employee.Name, result.Name);
        Assert.Single(_dbContext.Employees);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEmployeeIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEmployee_WhenValidEmployee()
    {
        _dbContext.Employees.Add(_employeeFixture.EmployeeDto);
        await _dbContext.SaveChangesAsync();

        _employeeFixture.EmployeeDto.Name = "Updated Name";
        var result = await _repository.UpdateAsync(_employeeFixture.EmployeeDto);
        Assert.NotNull(result);
        Assert.Equal("Updated Name", result.Name);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEmployeeIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithoutId()
    {
        var employeeName = "John Doe";
        _employeeFixture.EmployeeDto.Name = employeeName;

        await _dbContext.Employees.AddAsync(_employeeFixture.EmployeeDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsNameExist(employeeName, null);

        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameDoesNotExist_WithoutId()
    {
        var nonExistentName = "Non Existent Employee";

        var result = await _repository.IsNameExist(nonExistentName, null);

        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameExists_WithSameId()
    {
        var existingId = Guid.NewGuid().ToString();
        var employeeName = "Jane Smith";

        _employeeFixture.EmployeeDto.ReferenceId = existingId;
        _employeeFixture.EmployeeDto.Name = employeeName;

        await _dbContext.Employees.AddAsync(_employeeFixture.EmployeeDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsNameExist(employeeName, existingId);

        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithDifferentId()
    {
        var testId = Guid.NewGuid().ToString();
        var employeeName = "Bob Johnson";

        _employeeFixture.EmployeeDto.Name = employeeName;

        await _dbContext.Employees.AddAsync(_employeeFixture.EmployeeDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsNameExist(employeeName, testId);

        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithInvalidGuid()
    {
        var invalidId = "not-a-valid-guid";
        var employeeName = "Alice Brown";

        _employeeFixture.EmployeeDto.Name = employeeName;

        await _dbContext.Employees.AddAsync(_employeeFixture.EmployeeDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsNameExist(employeeName, invalidId);

        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameDoesNotExist_WithValidGuid()
    {
        var validId = Guid.NewGuid().ToString();
        var nonExistentName = "Non Existent Employee";

        var result = await _repository.IsNameExist(nonExistentName, validId);

        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_IsCaseSensitive()
    {
        var employeeName = "John Doe";
        _employeeFixture.EmployeeDto.Name = employeeName;

        await _dbContext.Employees.AddAsync(_employeeFixture.EmployeeDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsNameExist("JOHN DOE", null);

        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_HandlesEmptyString()
    {
        var result = await _repository.IsNameExist("", null);

        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_HandlesEmptyId()
    {
        var employeeName = "Test Employee";
        _employeeFixture.EmployeeDto.Name = employeeName;

        await _dbContext.Employees.AddAsync(_employeeFixture.EmployeeDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsNameExist(employeeName, "");

        Assert.True(result);
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldMarkAsInactive_WhenEmployeeExists()
    {
        _dbContext.Employees.Add(_employeeFixture.EmployeeDto);
        await _dbContext.SaveChangesAsync();

      var result = await _repository.DeleteAsync(_employeeFixture.EmployeeDto);
        var afterDelete = await _repository.ListAllAsync();
        Assert.True(afterDelete.Count()==0);
       
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEmployeeIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region FindByFilterAsync Tests

    [Fact]
    public async Task FindByFilterAsync_ReturnsMatchingEmployees_WhenFilterMatches()
    {
        _employeeFixture.EmployeeList[0].Name = "John Doe";
        _employeeFixture.EmployeeList[1].Name = "Jane Smith";
        _employeeFixture.EmployeeList[2].Name = "John Johnson";

        await _dbContext.Employees.AddRangeAsync(_employeeFixture.EmployeeList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.FindByFilterAsync(e => e.Name.Contains("John"));

        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains("John", x.Name));
    }

    [Fact]
    public async Task FindByFilterAsync_ReturnsEmpty_WhenNoMatches()
    {
        await _dbContext.Employees.AddRangeAsync(_employeeFixture.EmployeeList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.FindByFilterAsync(e => e.Name == "Non Existent");

        Assert.Empty(result);
    }

    #endregion
}
