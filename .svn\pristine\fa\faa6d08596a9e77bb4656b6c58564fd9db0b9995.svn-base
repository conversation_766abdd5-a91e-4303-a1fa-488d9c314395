using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowProfileInfoViewRepositoryTests : IClassFixture<WorkflowProfileInfoViewFixture>
    {
        private readonly WorkflowProfileInfoViewFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowProfileInfoViewRepository _repoParent;
        private readonly WorkflowProfileInfoViewRepository _repoNotParent;
        private readonly WorkFlowRepository _workFlowRepository;

        public WorkflowProfileInfoViewRepositoryTests(WorkflowProfileInfoViewFixture fixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _workFlowRepository= new WorkFlowRepository(_dbContext, DbContextFactory.GetMockUserService());
            _repoParent = new WorkflowProfileInfoViewRepository(_dbContext, DbContextFactory.GetMockUserService(), _workFlowRepository);
            _repoNotParent = new WorkflowProfileInfoViewRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent(), _workFlowRepository);
           
        }

        [Fact]
        public async Task GetWorkflowProfileInfoByProfileIds_ReturnsList_WhenIsAllInfra()
        {
            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(_fixture.WorkflowProfileInfoViewList);
            await _dbContext.SaveChangesAsync();

            var ids = _fixture.WorkflowProfileInfoViewList.Select(x => x.ProfileId).ToList();
            var result = await _repoParent.GetWorkflowProfileInfoByProfileIds(ids);

            Assert.All(result, x => Assert.Contains(x.ProfileId, ids));
        }

        [Fact]
        public async Task IsRunningWorkflow_ReturnsTuple_WhenIsParent()
        {
            var entity = _fixture.WorkflowProfileInfoViewDto;
            entity.IsRunning = true;
            entity.ProgressBar = "50%";
            await _dbContext.WorkflowProfileInfoViews.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.IsRunningWorkflow(entity.WorkflowId);

            Assert.True(result.IsRunning);
            Assert.Equal("50%", result.ProgressBar);
        }

        [Fact]
        public async Task IsRunningWorkflow_ReturnsFalse_WhenNotFound()
        {
            var result = await _repoParent.IsRunningWorkflow("non-existent");

            Assert.False(result.IsRunning);
            Assert.Null(result.ProgressBar);
        }

        [Fact]
        public async Task GetRunningProfileByProfileIds_ReturnsList_WhenIsParent()
        {
            var entity = _fixture.WorkflowProfileInfoViewDto;
            entity.IsRunning = true;
            await _dbContext.WorkflowProfileInfoViews.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var ids = new List<string> { entity.ProfileId };
            var result = await _repoParent.GetRunningProfileByProfileIds(ids);

            Assert.All(result, x => Assert.True(x.IsRunning));
        }

        [Fact]
        public async Task GetWorkflowProfileInfoViewByProfileIdandWorkflowIds_ReturnsList_WhenIsAllInfra()
        {
            var entity = _fixture.WorkflowProfileInfoViewDto;
            await _dbContext.WorkflowProfileInfoViews.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var ids = new List<string> { entity.WorkflowId };
            var result = await _repoParent.GetWorkflowProfileInfoViewByProfileIdandWorkflowIds(entity.ProfileId, ids);

            Assert.All(result, x => Assert.Equal(entity.ProfileId, x.ProfileId));
            Assert.All(result, x => Assert.Contains(x.WorkflowId, ids));
        }

        [Fact]
        public async Task GetWorkflowProfileInfoNames_ReturnsNames()
        {
            //_mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile")).ReturnsAsync(new List<string>());
            await _dbContext.WorkflowProfileInfoViews.AddRangeAsync(_fixture.WorkflowProfileInfoViewList);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowProfileInfoNames();

            Assert.All(result, x => Assert.NotNull(x.ProfileName));
        }
    }
}