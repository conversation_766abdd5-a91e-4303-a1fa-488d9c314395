using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class PluginManagerHistoryRepositoryTests : IClassFixture<PluginManagerHistoryFixture>
{
    private readonly PluginManagerHistoryFixture _pluginManagerHistoryFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly PluginManagerHistoryRepository _repository;

    public PluginManagerHistoryRepositoryTests(PluginManagerHistoryFixture pluginManagerHistoryFixture)
    {
        _pluginManagerHistoryFixture = pluginManagerHistoryFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockLoggedInUserService = LoggedInUserServiceRepositoryMocks.GetLoggedInUserServiceRepository();
        
        _repository = new PluginManagerHistoryRepository(_dbContext, mockLoggedInUserService.Object);
    }

    private async Task ClearDatabase()
    {
        _dbContext.PluginManagerHistories.RemoveRange(_dbContext.PluginManagerHistories);
        _dbContext.PluginManagers.RemoveRange(_dbContext.PluginManagers);
        await _dbContext.SaveChangesAsync();
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

   

    #region GetPluginManagerHistoryNames Tests

    [Fact]
    public async Task GetPluginManagerHistoryNames_ShouldReturnActivePluginManagers()
    {
        // Arrange
        await ClearDatabase();
        
        // Create active plugin managers
        var activePluginManager1 = new PluginManager 
        { 
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ActivePlugin1",
            IsActive = true,
            CreatedBy = "TestUser",
            CreatedDate = DateTime.UtcNow
        };
        var activePluginManager2 = new PluginManager 
        { 
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ActivePlugin2",
            IsActive = true,
            CreatedBy = "TestUser",
            CreatedDate = DateTime.UtcNow
        };
        
        // Create inactive plugin manager
        var inactivePluginManager = new PluginManager 
        { 
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "InactivePlugin",
            IsActive = false,
            CreatedBy = "TestUser",
            CreatedDate = DateTime.UtcNow
        };

        await _dbContext.PluginManagers.AddRangeAsync(new[] { activePluginManager1, activePluginManager2, inactivePluginManager });
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetPluginManagerHistoryNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, pmh => Assert.NotNull(pmh.ReferenceId));
        Assert.All(result, pmh => Assert.NotNull(pmh.LoginName));
        Assert.Contains(result, pmh => pmh.LoginName == "ActivePlugin1");
        Assert.Contains(result, pmh => pmh.LoginName == "ActivePlugin2");
        Assert.DoesNotContain(result, pmh => pmh.LoginName == "InactivePlugin");
    }

    [Fact]
    public async Task GetPluginManagerHistoryNames_ShouldReturnEmpty_WhenNoActivePluginManagers()
    {
        // Arrange
        await ClearDatabase();
        
        // Create only inactive plugin managers
        var inactivePluginManager = new PluginManager 
        { 
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "InactivePlugin",
            IsActive = false,
            CreatedBy = "TestUser",
            CreatedDate = DateTime.UtcNow
        };

        await _dbContext.PluginManagers.AddAsync(inactivePluginManager);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetPluginManagerHistoryNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetPluginManagerHistoryNames_ShouldReturnEmpty_WhenNoPluginManagers()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetPluginManagerHistoryNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsPluginManagerHistoryNameUnique Tests

    [Fact]
    public async Task IsPluginManagerHistoryNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPluginManagerHistory";
        var history = _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithSpecificLoginName(existingName);
        await _repository.AddAsync(history);

        // Act
        var result = await _repository.IsPluginManagerHistoryNameUnique(existingName);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsPluginManagerHistoryNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "NonExistentPluginManagerHistory";

        // Act
        var result = await _repository.IsPluginManagerHistoryNameUnique(nonExistentName);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsPluginManagerHistoryNameUnique_ShouldReturnTrue_WhenMultipleEntitiesWithSameName()
    {
        // Arrange
        await ClearDatabase();
        var sameName = "DuplicateName";
        var histories = _pluginManagerHistoryFixture.CreateMultiplePluginManagerHistoriesWithSameLoginName(sameName, 3);
        await _repository.AddRangeAsync(histories);

        // Act
        var result = await _repository.IsPluginManagerHistoryNameUnique(sameName);

        // Assert
        Assert.True(result);
    }

    #endregion

    #region IsPluginManagerHistoryNameExist Tests

    [Fact]
    public async Task IsPluginManagerHistoryNameExist_ShouldReturnTrue_WhenNameExistsForNewEntity()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPluginManagerHistory";
        var history = _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithSpecificLoginName(existingName);
        await _repository.AddAsync(history);

        // Act - Check for new entity (empty id)
        var result = await _repository.IsPluginManagerHistoryNameExist(existingName, string.Empty);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsPluginManagerHistoryNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "NonExistentPluginManagerHistory";

        // Act
        var result = await _repository.IsPluginManagerHistoryNameExist(nonExistentName, string.Empty);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsPluginManagerHistoryNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPluginManagerHistory";
        var history = _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithSpecificLoginName(existingName);
        await _repository.AddAsync(history);

        // Act - Check for same entity (using its own id)
        var result = await _repository.IsPluginManagerHistoryNameExist(existingName, history.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsPluginManagerHistoryNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntity()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPluginManagerHistory";
        var history1 = _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithSpecificLoginName(existingName);
        var history2 = _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithSpecificLoginName("DifferentName");
        await _repository.AddAsync(history1);
        await _repository.AddAsync(history2);

        // Act - Check if name exists for different entity
        var result = await _repository.IsPluginManagerHistoryNameExist(existingName, history2.ReferenceId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsPluginManagerHistoryNameExist_ShouldHandleInvalidGuid()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPluginManagerHistory";
        var history = _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithSpecificLoginName(existingName);
        await _repository.AddAsync(history);

        // Act - Check with invalid GUID (should treat as new entity)
        var result = await _repository.IsPluginManagerHistoryNameExist(existingName, "invalid-guid");

        // Assert
        Assert.True(result);
    }

    #endregion

    #region GetPluginManagerHistoryByPluginManagerId Tests

    [Fact]
    public async Task GetPluginManagerHistoryByPluginManagerId_ShouldReturnHistories_WhenPluginManagerIdExists()
    {
        // Arrange
        await ClearDatabase();
        var pluginManagerId = "abea4151-6ee0-4f6b-9fc0-2fa5c961d956";
        var histories = _pluginManagerHistoryFixture.CreatePluginManagerHistoriesWithSamePluginManagerId(pluginManagerId, 3);

        // Add some histories with different plugin manager IDs
        var otherHistories = _pluginManagerHistoryFixture.CreatePluginManagerHistoriesWithSamePluginManagerId("bbea4151-6ee0-4f6b-9fc0-2fa5c961d956", 2);

        await _repository.AddRangeAsync(histories);
        await _repository.AddRangeAsync(otherHistories);

        // Act
        var result = await _repository.GetPluginManagerHistoryByPluginManagerId(pluginManagerId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, h => Assert.Equal(pluginManagerId, h.PluginManagerId));
    }

    [Fact]
    public async Task GetPluginManagerHistoryByPluginManagerId_ShouldReturnEmpty_WhenPluginManagerIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentPluginManagerId = "ccea4151-6ee0-4f6b-9fc0-2fa5c961d956";

        // Act
        var result = await _repository.GetPluginManagerHistoryByPluginManagerId(nonExistentPluginManagerId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetPluginManagerHistoryByPluginManagerId_ShouldReturnEmpty_WhenNoHistories()
    {
        // Arrange
        await ClearDatabase();
        var pluginManagerId = "abea4151-6ee0-4f6b-9fc0-2fa5c961d956";

        // Act
        var result = await _repository.GetPluginManagerHistoryByPluginManagerId(pluginManagerId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetPluginManagerHistoryByPluginManagerId_ShouldThrow_WhenPluginManagerIdIsInvalid()
    {
        // Arrange
        var invalidPluginManagerId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<ContinuityPatrol.Shared.Core.Exceptions.InvalidArgumentException>(() =>
            _repository.GetPluginManagerHistoryByPluginManagerId(invalidPluginManagerId));
    }

    [Fact]
    public async Task GetPluginManagerHistoryByPluginManagerId_ShouldThrow_WhenPluginManagerIdIsEmpty()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ContinuityPatrol.Shared.Core.Exceptions.InvalidArgumentException>(() =>
            _repository.GetPluginManagerHistoryByPluginManagerId(string.Empty));
    }

    [Fact]
    public async Task GetPluginManagerHistoryByPluginManagerId_ShouldThrow_WhenPluginManagerIdIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _repository.GetPluginManagerHistoryByPluginManagerId(null));
    }

    [Fact]
    public async Task GetPluginManagerHistoryByPluginManagerId_ShouldFilterByCompanyId_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();
        var userCompanyId = "USER_COMPANY_123";
        var pluginManagerId = "abea4151-6ee0-4f6b-9fc0-2fa5c961d956";

        // Create mock for non-parent user
        var mockLoggedInUserService = LoggedInUserServiceRepositoryMocks.GetLoggedInUserServiceRepository();
        mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        mockLoggedInUserService.Setup(x => x.CompanyId).Returns(userCompanyId);

        var nonParentRepository = new PluginManagerHistoryRepository(_dbContext, mockLoggedInUserService.Object);

        // Create histories with same plugin manager ID but different company IDs
        var userCompanyHistories = _pluginManagerHistoryFixture.CreatePluginManagerHistoriesWithSamePluginManagerId(pluginManagerId, 2);
        foreach (var history in userCompanyHistories)
        {
            history.CompanyId = userCompanyId;
        }

        var otherCompanyHistories = _pluginManagerHistoryFixture.CreatePluginManagerHistoriesWithSamePluginManagerId(pluginManagerId, 3);
        foreach (var history in otherCompanyHistories)
        {
            history.CompanyId = "OTHER_COMPANY";
        }

        await nonParentRepository.AddRangeAsync(userCompanyHistories);
        await nonParentRepository.AddRangeAsync(otherCompanyHistories);

        // Act
        var result = await nonParentRepository.GetPluginManagerHistoryByPluginManagerId(pluginManagerId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only histories from user's company
        Assert.All(result, h => Assert.Equal(userCompanyId, h.CompanyId));
        Assert.All(result, h => Assert.Equal(pluginManagerId, h.PluginManagerId));
    }

    [Fact]
    public async Task GetPluginManagerHistoryByPluginManagerId_ShouldReturnAllHistories_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();
        var pluginManagerId = "abea4151-6ee0-4f6b-9fc0-2fa5c961d956";

        // Create histories with same plugin manager ID but different company IDs
        var company1Histories = _pluginManagerHistoryFixture.CreatePluginManagerHistoriesWithSamePluginManagerId(pluginManagerId, 2);
        foreach (var history in company1Histories)
        {
            history.CompanyId = "COMPANY_1";
        }

        var company2Histories = _pluginManagerHistoryFixture.CreatePluginManagerHistoriesWithSamePluginManagerId(pluginManagerId, 3);
        foreach (var history in company2Histories)
        {
            history.CompanyId = "COMPANY_2";
        }

        await _repository.AddRangeAsync(company1Histories);
        await _repository.AddRangeAsync(company2Histories);

        // Act - The default mock user service is set up as IsParent = true
        var result = await _repository.GetPluginManagerHistoryByPluginManagerId(pluginManagerId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(5, result.Count); // All histories regardless of company
        Assert.All(result, h => Assert.Equal(pluginManagerId, h.PluginManagerId));

        // Verify we have histories from both companies
        Assert.Contains(result, h => h.CompanyId == "COMPANY_1");
        Assert.Contains(result, h => h.CompanyId == "COMPANY_2");
    }

    [Fact]
    public async Task GetPluginManagerHistoryByPluginManagerId_ShouldReturnHistoriesWithCorrectPluginManagerId()
    {
        // Arrange
        await ClearDatabase();
        var pluginManagerId = "abea4151-6ee0-4f6b-9fc0-2fa5c961d956";

        // Create histories with the target plugin manager ID
        var targetHistories = _pluginManagerHistoryFixture.CreatePluginManagerHistoriesWithSamePluginManagerId(pluginManagerId, 3);

        // Create histories with different plugin manager ID
        var otherHistories = _pluginManagerHistoryFixture.CreatePluginManagerHistoriesWithSamePluginManagerId("bbea4151-6ee0-4f6b-9fc0-2fa5c961d956", 2);

        // Add all histories
        await _repository.AddRangeAsync(targetHistories);
        await _repository.AddRangeAsync(otherHistories);

        // Act
        var result = await _repository.GetPluginManagerHistoryByPluginManagerId(pluginManagerId);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count >= 3); // Should include at least the target histories (Active() filtering may not work in in-memory DB)
        Assert.All(result, h => Assert.Equal(pluginManagerId, h.PluginManagerId));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();
        var histories = new List<PluginManagerHistory>
        {
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(companyId: "COMPANY_1"),
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(companyId: "COMPANY_2"),
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(companyId: "COMPANY_3")
        };
        await _repository.AddRangeAsync(histories);

        // Act - The mock user service is set up as IsParent = true
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldFilterByCompanyId_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();
        var userCompanyId = "USER_COMPANY_123";

        // Create mock for non-parent user
        var mockLoggedInUserService = LoggedInUserServiceRepositoryMocks.GetLoggedInUserServiceRepository();
        mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        mockLoggedInUserService.Setup(x => x.CompanyId).Returns(userCompanyId);

        var nonParentRepository = new PluginManagerHistoryRepository(_dbContext, mockLoggedInUserService.Object);

        var histories = new List<PluginManagerHistory>
        {
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(companyId: userCompanyId),
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(companyId: userCompanyId),
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(companyId: "OTHER_COMPANY")
        };
        await nonParentRepository.AddRangeAsync(histories);

        // Act
        var result = await nonParentRepository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, h => Assert.Equal(userCompanyId, h.CompanyId));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmpty_WhenNoEntities()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRangeAsync Tests

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultipleEntities()
    {
        // Arrange
        await ClearDatabase();
        var histories = new List<PluginManagerHistory>
        {
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(),
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(),
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties()
        };

        // Act
        await _repository.AddRangeAsync(histories);

        // Assert
        var allHistories = await _repository.ListAllAsync();
        Assert.Equal(3, allHistories.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenListIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion


    #region Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        await ClearDatabase();
        var tasks = new List<Task>();

        // Act - Perform concurrent add operations
        for (int i = 0; i < 10; i++)
        {
            var history = _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(loginName: $"ConcurrentHistory_{i}");
            tasks.Add(_repository.AddAsync(history));
        }

        await Task.WhenAll(tasks);

        // Assert
        var allHistories = await _repository.ListAllAsync();
        Assert.Equal(10, allHistories.Count);
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_WhenUpdatingMultipleEntities()
    {
        // Arrange
        await ClearDatabase();
        var histories = new List<PluginManagerHistory>
        {
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(loginName: "History1"),
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(loginName: "History2"),
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(loginName: "History3")
        };
        await _repository.AddRangeAsync(histories);

        // Act - Update all entities
        foreach (var history in histories)
        {
            history.Version = "2.0.0";
            await _repository.UpdateAsync(history);
        }

        // Assert
        var allHistories = await _repository.ListAllAsync();
        Assert.Equal(3, allHistories.Count);
        Assert.All(allHistories, h => Assert.Equal("2.0.0", h.Version));
    }

    [Fact]
    public async Task Repository_ShouldHandleLargeDataSet()
    {
        // Arrange
        await ClearDatabase();
        var histories = new List<PluginManagerHistory>();

        // Create 100 plugin manager histories
        for (int i = 0; i < 100; i++)
        {
            histories.Add(_pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(loginName: $"History_{i:D3}"));
        }

        await _repository.AddRangeAsync(histories);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(100, result.Count);
    }

    [Fact]
    public async Task GetPluginManagerHistoryNames_ShouldHandleComplexPluginManagerData()
    {
        // Arrange
        await ClearDatabase();

        var pluginManagers = new List<PluginManager>
        {
            new PluginManager
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "<EMAIL>",
                IsActive = true,
                CreatedBy = "TestUser",
                CreatedDate = DateTime.UtcNow
            },
            new PluginManager
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "PluginWithVeryLongNameThatExceedsNormalLengthExpectations",
                IsActive = true,
                CreatedBy = "TestUser",
                CreatedDate = DateTime.UtcNow
            }
        };

        await _dbContext.PluginManagers.AddRangeAsync(pluginManagers);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetPluginManagerHistoryNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, pmh => pmh.LoginName == "<EMAIL>");
        Assert.Contains(result, pmh => pmh.LoginName == "PluginWithVeryLongNameThatExceedsNormalLengthExpectations");
    }

    [Fact]
    public async Task Repository_ShouldHandleSpecialCharactersInLoginName()
    {
        // Arrange
        await ClearDatabase();
        var specialLoginName = "<EMAIL>";
        var history = _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithSpecificLoginName(specialLoginName);

        // Act
        var addedHistory = await _repository.AddAsync(history);
        var nameExists = await _repository.IsPluginManagerHistoryNameExist(specialLoginName, string.Empty);
        var nameUnique = await _repository.IsPluginManagerHistoryNameUnique(specialLoginName);

        // Assert
        Assert.NotNull(addedHistory);
        Assert.Equal(specialLoginName, addedHistory.LoginName);
        Assert.True(nameExists);
        Assert.True(nameUnique);
    }

    [Fact]
    public async Task IsPluginManagerHistoryNameExist_ShouldHandleCaseSensitivity()
    {
        // Arrange
        await ClearDatabase();
        var originalName = "PluginManagerHistoryName";
        var history = _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithSpecificLoginName(originalName);
        await _repository.AddAsync(history);

        // Act - Check with different case
        var resultLowerCase = await _repository.IsPluginManagerHistoryNameExist(originalName.ToLower(), string.Empty);
        var resultUpperCase = await _repository.IsPluginManagerHistoryNameExist(originalName.ToUpper(), string.Empty);

        // Assert - Behavior depends on database collation, but method should not throw
        Assert.IsType<bool>(resultLowerCase);
        Assert.IsType<bool>(resultUpperCase);
    }

    [Fact]
    public async Task GetPluginManagerHistoryByPluginManagerId_ShouldHandleSpecialCharactersInPluginManagerId()
    {
        // Arrange
        await ClearDatabase();
        var specialPluginManagerId = "abea4151-6ee0-4f6b-9fc0-2fa5c961d956";
        var history = _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithSpecificPluginManagerId(specialPluginManagerId);
        await _repository.AddAsync(history);

        // Act
        var result = await _repository.GetPluginManagerHistoryByPluginManagerId(specialPluginManagerId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(specialPluginManagerId, result.First().PluginManagerId);
    }

    #endregion

    #region Edge Cases Tests

    [Fact]
    public async Task IsPluginManagerHistoryNameExist_ShouldReturnCorrectResult_WhenMultipleEntitiesWithSameName()
    {
        // Arrange
        await ClearDatabase();
        var sameName = "DuplicateLoginName";
        var histories = _pluginManagerHistoryFixture.CreateMultiplePluginManagerHistoriesWithSameLoginName(sameName, 3);
        await _repository.AddRangeAsync(histories);

        // Act
        var result = await _repository.IsPluginManagerHistoryNameExist(sameName, string.Empty);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task Repository_ShouldHandleEntityWithNullProperties()
    {
        // Arrange
        await ClearDatabase();
        var history = _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties();
        history.Properties = null;
        history.Description = null;
        history.Comments = null;

        // Act & Assert - Should not throw
        var result = await _repository.AddAsync(history);
        Assert.NotNull(result);
        Assert.Null(result.Properties);
        Assert.Null(result.Description);
        Assert.Null(result.Comments);
    }

    [Fact]
    public async Task IsPluginManagerHistoryNameExist_ShouldHandleEmptyAndNullNames()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert - Should not throw
        var resultEmpty = await _repository.IsPluginManagerHistoryNameExist(string.Empty, string.Empty);
        var resultNull = await _repository.IsPluginManagerHistoryNameExist(null, string.Empty);

        Assert.IsType<bool>(resultEmpty);
        Assert.IsType<bool>(resultNull);
    }

    [Fact]
    public async Task IsPluginManagerHistoryNameUnique_ShouldHandleEmptyAndNullNames()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert - Should not throw
        var resultEmpty = await _repository.IsPluginManagerHistoryNameUnique(string.Empty);
        var resultNull = await _repository.IsPluginManagerHistoryNameUnique(null);

        Assert.IsType<bool>(resultEmpty);
        Assert.IsType<bool>(resultNull);
    }



    [Fact]
    public async Task ListAllAsync_ShouldRespectCompanyIdFiltering_WithMixedCompanyData()
    {
        // Arrange
        await ClearDatabase();
        var userCompanyId = "USER_COMPANY_123";

        // Create mock for non-parent user
        var mockLoggedInUserService = LoggedInUserServiceRepositoryMocks.GetLoggedInUserServiceRepository();
        mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        mockLoggedInUserService.Setup(x => x.CompanyId).Returns(userCompanyId);

        var nonParentRepository = new PluginManagerHistoryRepository(_dbContext, mockLoggedInUserService.Object);

        // Create histories with various company IDs
        var histories = new List<PluginManagerHistory>
        {
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(companyId: userCompanyId),
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(companyId: "OTHER_COMPANY_1"),
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(companyId: userCompanyId),
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(companyId: "OTHER_COMPANY_2"),
            _pluginManagerHistoryFixture.CreatePluginManagerHistoryWithProperties(companyId: userCompanyId)
        };
        await nonParentRepository.AddRangeAsync(histories);

        // Act
        var result = await nonParentRepository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count); // Only histories with userCompanyId
        Assert.All(result, h => Assert.Equal(userCompanyId, h.CompanyId));
    }

    #endregion
}
