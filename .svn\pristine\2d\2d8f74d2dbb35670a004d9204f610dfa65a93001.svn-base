﻿using ContinuityPatrol.Application.Features.AlertInformation.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.AlertInformationModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertInformation.Queries;

public class GetAlertInformationPaginatedListQueryHandlerTests : IClassFixture<AlertInformationFixture>
{
    private readonly AlertInformationFixture _alertInformationFixture;
    private readonly Mock<IAlertInformationRepository> _mockAlertInformationRepository;
    private readonly GetAlertInformationPaginatedListQueryHandler _handler;

    public GetAlertInformationPaginatedListQueryHandlerTests(AlertInformationFixture alertInformationFixture)
    {
        _alertInformationFixture = alertInformationFixture;
        
        _mockAlertInformationRepository = AlertInformationRepositoryMocks.GetPaginatedAlertInformationRepository(_alertInformationFixture.AlertInformations);
        
        _handler = new GetAlertInformationPaginatedListQueryHandler(_mockAlertInformationRepository.Object, _alertInformationFixture.Mapper);

        _alertInformationFixture.AlertInformations[0].Type = "Alert_Testing_01";
        _alertInformationFixture.AlertInformations[0].Severity = "Low";
        _alertInformationFixture.AlertInformations[0].Code = "1";

        _alertInformationFixture.AlertInformations[1].Type = "Alert_Testing_02";
        _alertInformationFixture.AlertInformations[1].Severity = "High";
        _alertInformationFixture.AlertInformations[1].Code = "2";
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetAlertInformationPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertInformationListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_AlertInformations_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetAlertInformationPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "type=Alert_Testing_01;severity=Low;code=1" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertInformationListVm>>();

        Assert.NotNull(result.TotalCount);

        result.Data[0].Type.ShouldBe("Alert_Testing_01");

        result.Data[0].Severity.ShouldBe("Low");

        result.Data[0].Code.ShouldBe("1");
    }

    [Fact]
    public async Task Handle_Return_PaginatedAlertInformations_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetAlertInformationPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "High" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertInformationListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<AlertInformationListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Type.ShouldBe(_alertInformationFixture.AlertInformations[1].Type);

        result.Data[0].Severity.ShouldBe("High");

        result.Data[0].AlertFrequency.ShouldBe(_alertInformationFixture.AlertInformations[1].AlertFrequency);

        result.Data[0].Code.ShouldNotBeEmpty(_alertInformationFixture.AlertInformations[1].Code);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetAlertInformationPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "abc" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertInformationListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetAlertInformationPaginatedListQuery(), CancellationToken.None);

        _mockAlertInformationRepository.Verify(x => x.PaginatedListAllAsync(), Times.Once);
    }
}