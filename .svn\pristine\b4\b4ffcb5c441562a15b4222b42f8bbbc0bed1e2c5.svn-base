global using Xunit;
global using Newtonsoft.Json;
global using System.Text;
global using MediatR;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.AspNetCore.Mvc.Rendering;
global using Microsoft.Extensions.Caching.Distributed;
global using Microsoft.Extensions.Logging;
global using Moq;
global using Shouldly;
global using AutoMapper;
global using Microsoft.AspNetCore.Authentication;
global using Microsoft.AspNetCore.Authentication.Cookies;
global using Microsoft.AspNetCore.Http;
global using Microsoft.Extensions.DependencyInjection;
global using System.Security.Claims;
global using Microsoft.AspNetCore.Mvc.ViewFeatures;
global using Microsoft.AspNetCore.Diagnostics;