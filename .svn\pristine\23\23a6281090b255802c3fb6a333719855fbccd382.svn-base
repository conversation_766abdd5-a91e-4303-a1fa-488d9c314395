﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.LicenseManager.Events.UpdateState;

public class UpdateLicenseStateEventHandler : INotificationHandler<UpdateLicenseStateEvent>
{
    private readonly ILogger<UpdateLicenseStateEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public UpdateLicenseStateEventHandler(ILogger<UpdateLicenseStateEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(UpdateLicenseStateEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.LicenseManager.ToString(),
            Action = $"{ActivityType.Update} {Modules.LicenseManager}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails =
                $"License'{SecurityHelper.Decrypt(updatedEvent.PONumber)}' {(updatedEvent.IsState ? "Active" : "InActive")} successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation(
            $"License'{SecurityHelper.Decrypt(updatedEvent.PONumber)}' {(updatedEvent.IsState ? "Active" : "InActive")} successfully.");
    }
}