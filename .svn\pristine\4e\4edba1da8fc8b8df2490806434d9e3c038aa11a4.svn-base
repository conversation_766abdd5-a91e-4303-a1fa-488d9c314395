﻿@using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;
@model ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel.WorkflowProfileListVM
@* @model dynamic
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
} *@

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-workflow-profile"></i><span>Manage Workflow Profile List</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown"  title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="" id="workflowprofileName">
                                        <label class="form-check-label" for="workflowprofileName">
                                            Workflow-Profile Name
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                @* <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cp-add me-1"></i>Create</button> *@
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="tblmanageworkflowprofile" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Workflow-Profile Name</th>
                        <th>Workflow Name</th>
                        <th>InfraObject Name</th>
                        @* <th>Created By</th> *@
                        @* <th>Created Date</th> *@
                        <th class="Action-th">Four&nbsp;Eye&nbsp;Status</th>
                    </tr>
                </thead>
                <tbody>
                    @{
                        int i = 1;
                    }
                    @foreach (var workflow in Model.paginatedResult.Data)
                    {
                        <tr>
                            <td>
                                <span>@i</span>
                            </td>
                            <td>
                                <span class="Avatar_Logo">
                                    <img class="customer_logo" src="@workflow.ProfileName" title="" />
                                </span>
                                @workflow.ProfileName
                            </td>
                            <td>@workflow.WorkflowName</td>
                            <td>@workflow.InfraObjectName</td>
                            @* <td>@workflow.CreatedBy</td> *@
                            @* <td>@workflow.CreatedDate</td>  *@
                            <td class="Action-th">
                           
                                <div class="form-switch">
                                     @Html.CheckBox(workflow.Id, (workflow.IsFourEye == "1" ? true : false) , new { @class = "form-check-input",name="workflowprofile", role="switch", @id = workflow.Id, @onchange="onStatusChanged(this)" })
                                     <label class="form-check-label" for=@workflow.Id></label>
                                </div>
                            </td>
                        </tr>
                        i++;
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>


@section Scripts
    {
    <partial name="_ValidationScriptsPartial" />
}

<script type="text/javascript">
    // var RootUrl = '@Url.Content("~/")';

    function onStatusChanged(val) {

        var ischecked = val.checked == true ? 1 : 0;

        $.ajax({

            type: "POST",
            url: "/Manage/ManageWorkflowProfile/UpdateStatus",
            data: { status: val.checked, Id: val.id },
            success: function (data) {
                if (data) {
                    alert("Status updated successfully");
                }
                else {
                    alert("Status not updated");
                }
            },
            error: function (data) {
                alert("Error occured!!" + data)
            }


        });

    }



</script>
<script src="~/js/manageworkflowprofilelist.js"></script>

@* <script>

    $('#search-inp').on('keyup', function () {
        table.search($(this).val()).draw();
    });

    $(function () {
        var table = $('#WorkflowList').DataTable({
            language: {
                paginate: {
                    next: '<i class="cp-right-arrow"></i>',
                    previous: '<i class="cp-left-arrow"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,

        });
</script> *@
