﻿using ContinuityPatrol.Application.Features.SVCMssqlMonitorLog.Commands;
using ContinuityPatrol.Application.Features.SVCMssqlMonitorLog.Queries.GetByType;
using ContinuityPatrol.Application.Features.SVCMssqlMonitorLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SVCMssqlMonitorLog.Queries.GetList;
using ContinuityPatrol.Application.Features.SVCMssqlMonitorLog.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Manage;

public class SVCMsSqlMonitorLogService : BaseService, ISVCMsSqlMonitorLogService
{
    public SVCMsSqlMonitorLogService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateSVCMssqlMonitorLogCommand createSVCMssqlMonitorLogCommand)
    {
        Logger.LogInformation($"Create  SVCMssql Monitor Log '{createSVCMssqlMonitorLogCommand}'");

        return await Mediator.Send(createSVCMssqlMonitorLogCommand);
    }

    public async Task<List<SVCMssqlMonitorLogListVm>> GetAllSVCMssqlMonitorLog()
    {
        Logger.LogInformation("Get All  SVCMssql Monitor Logs");

        return await Mediator.Send(new GetSVCMssqlMonitorLogListQuery());
    }

    public async Task<SVCMssqlMonitorLogDetailVm> GetSVCMssqlMonitorLogById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "SVCMssqlMonitorLog  Detail By Id");

        Logger.LogInformation($"Get  SVCMssqlMonitorLog Detail by Id '{id}' ");

        return await Mediator.Send(new GetSVCMssqlMonitorLogDetailQuery { Id = id });
    }

    public async Task<PaginatedResult<SVCMssqlMonitorLogPaginatedListVm>> GetPaginatedSVCMssqlMonitorLog(
        GetSVCMssqlMonitorLogPaginatedListQuery query)
    {
        Logger.LogInformation("Get Searching Details in SVCMssqlMonitorLog Paginated List");

        return await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllSvcMsSqlMonitorStatusCacheKey,
            () => Mediator.Send(query));
    }

    public async Task<List<SVCMssqlMonitorLogDetailByTypeVm>> GetSVCMssqlMonitorLogByType(string type)
    {
        Guard.Against.InvalidGuidOrEmpty(type, "SVCMssqlMonitorLog Detail By Type");

        Logger.LogInformation($"Get  SVCMssqlMonitorLog Detail by Type '{type}'");

        return await Mediator.Send(new GetSVCMssqlMonitorLogDetailByTypeQuery { Type = type });
    }
}