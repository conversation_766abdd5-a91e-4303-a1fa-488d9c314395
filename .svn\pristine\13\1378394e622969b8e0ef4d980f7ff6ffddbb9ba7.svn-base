﻿using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Lock;
using ContinuityPatrol.Application.Features.WorkflowAction.Events.Lock;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowAction.Commands
{
    public class UpdateWorkflowActionLockTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IWorkflowActionRepository> _mockWorkflowActionRepository;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly UpdateWorkflowActionLockCommandHandler _handler;

        public UpdateWorkflowActionLockTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockWorkflowActionRepository = new Mock<IWorkflowActionRepository>();
            _mockPublisher = new Mock<IPublisher>();

            _handler = new UpdateWorkflowActionLockCommandHandler(_mockMapper.Object, _mockWorkflowActionRepository.Object, _mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_Should_LockWorkflowAction_When_RequestIsValid()
        {
            var request = new UpdateWorkflowActionLockCommand
            {
                Id = Guid.NewGuid().ToString(),
                IsLock = true
            };

            var workflowAction = new Domain.Entities.WorkflowAction
            {
                ReferenceId = request.Id,
                ActionName = "TestAction",
                IsLock = false
            };

            _mockWorkflowActionRepository
                .Setup(repo => repo.GetByReferenceIdAsync(request.Id))
                .ReturnsAsync(workflowAction);

            _mockMapper
                .Setup(m => m.Map(request, workflowAction, typeof(UpdateWorkflowActionLockCommand), typeof(Domain.Entities.WorkflowAction)));

            _mockWorkflowActionRepository
                .Setup(repo => repo.UpdateAsync(workflowAction))
                .ReturnsAsync(workflowAction);

            var response = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(response);
            Assert.Equal(request.Id, response.Id);
            Assert.Equal($"WorkflowAction '{"TestAction"}' UnLocked successfully.", response.Message);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<WorkflowActionLockEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_UnLockWorkflowAction_When_RequestIsValid()
        {
            var request = new UpdateWorkflowActionLockCommand
            {
                Id = Guid.NewGuid().ToString(),
                IsLock = false
            };

            var workflowAction = new Domain.Entities.WorkflowAction
            {
                ReferenceId = request.Id,
                ActionName = "TestAction",
                IsLock = true
            };

            _mockWorkflowActionRepository
                .Setup(repo => repo.GetByReferenceIdAsync(request.Id))
                .ReturnsAsync(workflowAction);

            _mockMapper
                .Setup(m => m.Map(request, workflowAction, typeof(UpdateWorkflowActionLockCommand), typeof(Domain.Entities.WorkflowAction)));

            _mockWorkflowActionRepository
                .Setup(repo => repo.UpdateAsync(workflowAction))
                .ReturnsAsync(workflowAction);

            var response = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(response);
            Assert.Equal(request.Id, response.Id);
            Assert.Equal($"WorkflowAction '{"TestAction"}' Locked successfully.", response.Message);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<WorkflowActionLockEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_ThrowNotFoundException_When_WorkflowActionDoesNotExist()
        {
            var request = new UpdateWorkflowActionLockCommand
            {
                Id = Guid.NewGuid().ToString(),
                IsLock = true
            };

            _mockWorkflowActionRepository
                .Setup(repo => repo.GetByReferenceIdAsync(request.Id))
                .ReturnsAsync((Domain.Entities.WorkflowAction)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(request, CancellationToken.None));
            _mockWorkflowActionRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.WorkflowAction>()), Times.Never);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<WorkflowActionLockEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_Should_ThrowInvalidGuidException_When_RequestIdIsInvalid()
        {
            var request = new UpdateWorkflowActionLockCommand
            {
                Id = "Guid.Empty",
                IsLock = true
            };

            await Assert.ThrowsAsync<InvalidArgumentException>(() => _handler.Handle(request, CancellationToken.None));
        }
    }
}
