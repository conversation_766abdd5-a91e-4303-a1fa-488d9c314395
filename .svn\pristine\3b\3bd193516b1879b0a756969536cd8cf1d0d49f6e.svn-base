using ContinuityPatrol.Application.Features.DynamicDashboard.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicDashboard.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicDashboard.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetList;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class DynamicDashboardService : BaseService,IDynamicDashboardService
{
    public DynamicDashboardService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<DynamicDashboardListVm>> GetDynamicDashboardList()
    {
        Logger.LogDebug("Get All DynamicDashboards");

        return await Mediator.Send(new GetDynamicDashboardListQuery());
    }

    public async Task<DynamicDashboardDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DynamicDashboard Id");

        Logger.LogDebug($"Get DynamicDashboard Detail by Id '{id}'");

        return await Mediator.Send(new GetDynamicDashboardDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateDynamicDashboardCommand createDynamicDashboardCommand)
    {
        Logger.LogDebug($"Create DynamicDashboard '{createDynamicDashboardCommand}'");

        return await Mediator.Send(createDynamicDashboardCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDynamicDashboardCommand updateDynamicDashboardCommand)
    {
        Logger.LogDebug($"Update DynamicDashboard '{updateDynamicDashboardCommand}'");

        return await Mediator.Send(updateDynamicDashboardCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DynamicDashboard Id");

        Logger.LogDebug($"Delete DynamicDashboard Details by Id '{id}'");

        return await Mediator.Send(new DeleteDynamicDashboardCommand { Id = id });
    }
     #region NameExist
 public async Task<bool> IsDynamicDashboardNameExist(string name, string id)
 {
     Guard.Against.NullOrWhiteSpace(name, "DynamicDashboard Name");

     Logger.LogDebug($"Check Name Exists Detail by DynamicDashboard Name '{name}' and Id '{id}'");

     return await Mediator.Send(new GetDynamicDashboardNameUniqueQuery { Name = name, Id = id });
 }
    #endregion

     #region Paginated
public async Task<PaginatedResult<DynamicDashboardListVm>> GetPaginatedDynamicDashboards(GetDynamicDashboardPaginatedListQuery query)
{
    Logger.LogDebug("Get Searching Details in DynamicDashboard Paginated List");

    return await Mediator.Send(query);
}
     #endregion
}
