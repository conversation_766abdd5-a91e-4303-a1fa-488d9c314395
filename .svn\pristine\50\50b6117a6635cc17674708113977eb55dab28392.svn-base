﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()
<style>
    .card-footer .nav-pills .nav-item .nav-link{
        background-color: #0d6efd !important;
        color: #fff;
    }

    .select2-container {
        z-index: 9999 !important;
    }
    .card-footer .nav-pills .nav-item .nav-link.active {
            background-color: #0d6efd !important;
            color:#fff;
    }
    .Escalation_Timeline_Card.active{
    box-shadow:4px 0rem 1rem rgb(132 138 153) !important;
    }</style>
<div class="page-content">
    <div class="card Card_Design_None">

        <div class="">
            <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                <div class="d-flex align-items-center">
                    <h6 class="page_title" title="Escalation Matrix">
                        <i class="cp-escalation_matrix_header-icon-3"></i>
                        <span>Escalation Matrix</span>
                    </h6>
                </div>
                <form class="d-flex">
                    <div class="input-group me-2 w-auto">
                        <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                        <div class="input-group-text">
                            <div class="dropdown">
                                <span data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-funnel"></i>
                                </span>
                                <ul class="dropdown-menu filter-dropdown">
                                    <li>
                                        <h6 class="dropdown-header">Filter Search</h6>
                                    </li>
                                    <li class="dropdown-item">
                                        <div>
                                            <input class="form-check-input" type="checkbox" value="" id="Name">
                                            <label class="form-check-label" for="Name">
                                                Name
                                            </label>
                                        </div>
                                    </li>

                                </ul>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-primary" id="levelCreate" >
                        <i class="cp-add me-1"></i>Create
                    </button>
                </form>

            </div>
        </div>
        <div class="card-body pt-0">
            <table id="tblEmatrix" class="table table-hover dataTable" style="width:100%">
                <thead>
                    <tr>
                        <th class="text-center">Sr.No</th>
                        <th>Matrix Code &amp; Name</th>
                        <th >Matrix Owner</th>
                        <th>Matrix User</th>
                        <th>Matrix Create Details</th>
                        <th>Matrix Update Details</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="tablebody">
                </tbody>
            </table>

        </div>
    </div>

    <!--Modal Create-->
    <div class="modal fade" data-bs-backdrop="static" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" width: 1000px;
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-xl ">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title">
                        <i class="cp-escalation_matrix_header-icon-3"></i>
                        <span>Escalation Matrix</span>
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="workflow-action row row-cols-2">

                        <div class="col">
                            <div id="escImage"class="w-100 text-center d-none">
                                <img src="~/img/isomatric/escalation_matrix.svg" class="img-fluid" style="width: 500px;" />
                            </div>
                            <div class="mx-auto" style="width: 30rem;">
                                <button type="button" id="end" class="btn btn-primary btn-sm">End</button>
                                <div class="Escalation_Timeline" id="Escalation_Time">
                                    <ul class="ul">
                                       
                                    </ul>
                                    <button type="button" id="start" class="btn btn-primary btn-sm">Start Escalation</button>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="mb-0 h-100 card shadow-sm">
                                <div class="card-body">
                                    @* <ul class="ms-2 nav nav-pills" id="pills-tab" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="pills-Level-tab" data-bs-toggle="pill"
                                                    data-bs-target="#pills-Level" type="button" role="tab"
                                                    aria-controls="pills-Level" aria-selected="true">
                                                <i class="cp-teams"></i> Level
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="pills-Teams-tab" data-bs-toggle="pill"
                                                    data-bs-target="#pills-Teams" type="button" role="tab"
                                                    aria-controls="pills-Teams" aria-selected="false">
                                                <i class="cp-teams"></i> Teams
                                            </button>
                                        </li>
                                    </ul> *@

                                    <div class="tab-content mt-3" id="pills-tabContent">
                                        <div class="tab-pane fade show active" id="pills-Level" role="tabpanel"
                                             aria-labelledby="pills-Level-tab" tabindex="0">
                                            <h6 class="mb-2"><i class="cp-teams fs-5 me-1"></i>Level</h6>
                                            <form>
                                                <div class="mb-3 form-group">
                                                    <div class="form-label">Level Name</div>
                                                    <div class="input-group">
                                                        <span class="input-group-text">
                                                            <i class="cp-name"></i>
                                                        </span>
                                                        <input type="text" class="form-control" id="escalationLevelName"
                                                               placeholder="Enter level name" autocomplete="off" />
                                                    </div> 
                                                    <span id="enNameEerror"></span>
                                                </div>
                                                <div class="mb-3 form-group">
                                                    <div class="form-label">Level Description</div>
                                                    <div class="input-group">
                                                        <span class="input-group-text">
                                                            <i class="cp-description"></i>
                                                        </span>
                                                        <input type="text" class="form-control" id="escalationLevelDescriptin"
                                                               placeholder="Enter level description" autocomplete="off" />
                                                    </div>
                                                </div>
                                                <div>
                                                   @*  <div class="d-flex align-items-end gap-3 mb-3">
                                                        <div class="form-group w-50">
                                                            <label class="form-label custom-cursor-default-hover"
                                                                   for="formBasicEmail">Escalation Time</label>
                                                            <div class="input-group">
                                                                <span class="input-group-text" id="basic-addon1">
                                                                    <i class="cp-apply-finish-time"></i>
                                                                </span>
                                                                <input type="text" class="form-control" id="escHour"
                                                                       placeholder="Enter Time" />
                                                                <span class="input-group-text form-label mb-0">Hours</span>
                                                            </div>
                                                            <span id="escHourError"></span>
                                                        </div>
                                                        <div class="form-group w-50">
                                                            <div class="input-group">
                                                                <span class="input-group-text" id="basic-addon1">
                                                                    <i class="cp-apply-finish-time"></i>
                                                                </span>
                                                                <input type="text" class="form-control" id="formBasicEmail"
                                                                       placeholder="Enter minutes" />
                                                                <span class="input-group-text form-label mb-0">Mins</span>
                                                            </div>
                                                            <span id="escMinuteError"></span>
                                                        </div>
                                                    </div> *@
                                                    <!-- Escalation Time -->
                                                    <div class="form-group mb-3">
                                                        <div class="form-label" title="Escalation Time">Escalation Time</div>
                                                        <div class="input-group">
                                                            <span class="input-group-text">
                                                                <i class="cp-time"></i>
                                                            </span>
                                                            <input 
                                                                   maxlength="30"
                                                                   type="number"
                                                                   class="form-control"
                                                                   id="escalationLevelTime"
                                                                   placeholder="Enter escalation time" autocomplete="off" />

                                                            <span class="input-group-text p-0 border-0">
                                                                <select id="escalationTimeZone"
                                                                        class="form-select"
                                                                        required>
                                                                    <option value="Days">day(s)</option>
                                                                    <option value="Hour">hour(s)</option>
                                                                    <option value="Mins">min(s)</option>
                                                                </select>
                                                            </span>
                                                        </div>
                                                        <span id="escHourError"></span>
                                                    </div>

                                                </div>
                                                <div class="form-group">
                                                    <div class="form-label">Operational Service Name</div>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="cp-business-service"></i></span>
                                                        <select class="form-select-modal select_actions" id="operationalService" name="workflowList" data-placeholder="Select operational service name">
                                                        </select>                                                       
                                                    </div>
                                                    <span id="operationError"></span>
                                                </div>
                                               
                                                @* <div>
                                                    <div class="form-check form-check-inline">
                                                        <input name="group1"
                                                               type="checkbox"
                                                               class="form-check-input custom-cursor-default-hover"><label title=""
                                                                                                                           class="form-check-label custom-cursor-default-hover">
                                                            Workflow
                                                            Creation
                                                        </label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input name="group1"
                                                               type="checkbox"
                                                               class="form-check-input custom-cursor-default-hover"
                                                               cursorshover="true"><label title=""
                                                                                          class="form-check-label custom-cursor-default-hover">
                                                            Workflow
                                                            Modification
                                                        </label>
                                                    </div>
                                                </div> *@
                                            </form>
                                        </div>
                                        <div class="tab-pane fade show" id="pills-Teams" role="tabpanel"
                                             aria-labelledby="pills-Teams-tab" tabindex="0">
                                            <h6 class="mb-2"><i class="cp-teams fs-5 me-1"></i>Teams</h6>
                                            <div class="d-flex">
                                                <div class="Filter_Search me-2 input-group">
                                                    <input autocomplete="off" type="search" id="txtSearch"
                                                           class="form-control" value="" style="min-height: 34px;">
                                                    <span class="ps-1  input-group-text">
                                                        <i class="cp-search"></i>

                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="dropdown dropend">
                                                        <button type="button" class="btn btn-primary" id="addName"
                                                                data-bs-toggle="dropdown" aria-expanded="false"
                                                                data-bs-auto-close="outside">
                                                            Add
                                                        </button>

                                                        <div class="dropdown-menu py-0" style="width:16rem; border:1px solid #e9e9e9 !important;">
                                                            <div class="p-2 card" id="unanimousId">
                                                                <div class="mb-3 form-group">
                                                                    <label class="form-label" for="useraddName">Name</label>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text"><i class="cp-name"></i></span>
                                                                        <input type="text" id="useraddName" class="form-control" placeholder="Enter Name" />
                                                                    </div>
                                                                </div>

                                                                <div class="mb-3 form-group">
                                                                    <label class="form-label" for="emailId">Email ID</label>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text"><i class="cp-email"></i></span>
                                                                        <input type="email" id="emailId" class="form-control" placeholder="Enter Email" />
                                                                    </div>
                                                                </div>

                                                                <div class="mb-3 form-group">
                                                                    <label class="form-label" for="phoneNumber">Phone Number</label>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text"><i class="cp-mobile-icon"></i></span>
                                                                        <input type="text" id="phoneNumber" class="form-control" placeholder="Enter Phone Number" />
                                                                    </div>
                                                                </div>

                                                                <!-- Optional Teams Group Select (uncomment if needed)
                                                                <div class="mb-3">
                                                                    <label class="form-label">Teams Group</label>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text"><i class="cp-teams"></i></span>
                                                                        <select class="form-select">
                                                                            <option value="">Select...</option>
                                                                            <option value="One">One</option>
                                                                            <option value="Two">Two</option>
                                                                            <option value="Three">Three</option>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                                -->

                                                                <div class="text-end">
                                                                    <button type="button" class="btn btn-secondary">Cancel</button>
                                                                    <button type="button" id="unanimousSave" class="btn btn-primary">Save</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>



                                                </div>
                                            </div>
                                           
                                            <div class="accordion mt-3 border border-secondary-subtle rounded"
                                                 id="accordionExample">
                                                <div class="accordion-item border-secondary-subtle">
                                                    <div class="border-secondary-subtle border-bottom  p-2 d-flex justify-content-between">
                                                        <span>User Name </span><span>
                                                            <span class="me-3">Mail</span><span class="me-4">SMS</span>
                                                        </span>
                                                    </div>
                                                    <div id="userListContainer" style="overflow-y:scroll;max-height:125px;">
                                                        <!-- User table will be injected here by JavaScript -->
                                                    </div>
                                                    <h2 class="accordion-header p-2 d-none">
                                                        <button class="accordion-button" type="button"
                                                                data-bs-toggle="collapse" data-bs-target="#collapseOne"
                                                                aria-expanded="true" aria-controls="collapseOne">
                                                            <div class="d-flex justify-content-between w-100 align-items-center">
                                                                <div class="form-check">
                                                                    <input type="checkbox" class="form-check-input">
                                                                    <label title=""
                                                                           class="form-check-label custom-cursor-default-hover"
                                                                           cursorshover="true">Dev_Team</label>
                                                                </div>
                                                                <span>
                                                                    <span class="me-4">
                                                                        <i class="cp-email
                                                                       "></i>
                                                                    </span>
                                                                    <span class="me-1">
                                                                        <i class="cp-mobile-icon
                                                                       "></i>
                                                                    </span>
                                                                </span>
                                                            </div>
                                                        </button>
                                                    </h2>
                                                    <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionExample">
    <div class="accordion-body pt-0">
        <!-- Placeholder for user list -->
     
    </div>
</div>
                                                    @* <div id="collapseOne" class="accordion-collapse collapse show"
                                                         data-bs-parent="#accordionExample">
                                                        <div class="accordion-body pt-0">
                                                            <table class="table-borderless table">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>
                                                                            <div class="mt-0 align-middle form-check">
                                                                                <input type="checkbox"
                                                                                       class="form-check-input"
                                                                                       cursorshover="true"><label title=""
                                                                                                                  class="form-check-label"
                                                                                                                  cursorshover="true">Dev_Team</label>
                                                                            </div>
                                                                        </td>
                                                                        <td>
                                                                            <img class="rounded-circle me-2"
                                                                                 alt="userimg"
                                                                                 src="/img/input_Icons/user-3.jpg"
                                                                                 width="30" height="30"
                                                                                 title="User">Ragul
                                                                        </td>
                                                                        <td><EMAIL> </td>

                                                                        <td>
                                                                            <div>
                                                                                <input type="checkbox" name="toggle"
                                                                                       id="SMSNotificationtoggle"><label for="SMSNotificationtoggle"></label>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div> *@
                                                </div>
                                                <div class="accordion-item">
                                                    <div class="accordion-item border-secondary-subtle">
                                                        <div class="border-secondary-subtle border-bottom  p-2 d-flex justify-content-between">                                                            
                                                                    <span>UserGroup Name</span><span>
                                                                <span class="me-3">Mail</span><span class="me-4">SMS</span>
                                                            </span>
                                                        </div>
                                                        <div id="userGroupListContainer" class="accordion" style="overflow-y:scroll;max-height:125px;">
                                                        <!-- User table will be injected here by JavaScript -->
                                                    </div>
                                                    <h2 class="accordion-header  p-2 d-none">
                                                        <button class="accordion-button collapsed" type="button"
                                                                data-bs-toggle="collapse" data-bs-target="#collapseTwo"
                                                                aria-expanded="false" aria-controls="collapseTwo">
                                                            <div class="d-flex justify-content-between w-100 align-items-center">
                                                                <div class="form-check">
                                                                    <input type="checkbox" class="form-check-input">
                                                                    <label title=""
                                                                           class="form-check-label custom-cursor-default-hover"
                                                                           cursorshover="true">Dev_Team</label>
                                                                </div>
                                                                <span>
                                                                    <span class="me-4">
                                                                        <i class="cp-email
                                                                       "></i>
                                                                    </span>
                                                                    <span class="me-1">
                                                                        <i class="cp-mobile-icon
                                                                       "></i>
                                                                    </span>
                                                                </span>
                                                            </div>
                                                        </button>
                                                    </h2>
                                                    <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                                                        @* <div class="accordion-body pt-0" id="userGroupListContainer">
                                                            <!-- Dynamic user group rows will be appended here -->
                                                        </div> *@
                                                    </div>

                                                    @* <div id="collapseTwo" class="accordion-collapse collapse"
                                                         data-bs-parent="#accordionExample">
                                                        <div class="accordion-body pt-0">
                                                            <table class="table-borderless table">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>
                                                                            <div class="mt-0 align-middle form-check">
                                                                                <input type="checkbox"
                                                                                       class="form-check-input"
                                                                                       cursorshover="true"><label title=""
                                                                                                                  class="form-check-label"
                                                                                                                  cursorshover="true">Dev_Team</label>
                                                                            </div>
                                                                        </td>
                                                                        <td>
                                                                            <img class="rounded-circle me-2"
                                                                                 alt="userimg"
                                                                                 src="/img/input_Icons/user-3.jpg"
                                                                                 width="30" height="30"
                                                                                 title="User">Ragul
                                                                        </td>
                                                                        <td><EMAIL> </td>

                                                                        <td>
                                                                            <div>
                                                                                <input type="checkbox" name="toggle"
                                                                                       id="SMSNotificationtoggle"><label for="SMSNotificationtoggle"></label>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div> *@
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                         <div class="card-footer border-0 d-flex justify-content-end gap-1 align-items-center">
                                @*     <button class="btn btn-sm btn-primary" id="pills-Level-tab" data-bs-toggle="pill" data-bs-target="#pills-Level" type="button" role="tab" aria-controls="pills-Level" aria-selected="true">Privous</button>
                                    <button class="btn btn-sm btn-primary" id="pills-Teams-tab" data-bs-toggle="pill" data-bs-target="#pills-Teams" type="button" role="tab" aria-controls="pills-Teams" aria-selected="false" tabindex="-1">Next</button>
                             <button class="btn btn-sm btn-primary">Save</button> *@
                                        <ul class="ms-2 nav nav-pills gap-1" id="pills-tab" role="tablist">
                                            <li class="nav-item" role="presentation">
                                                <button id="clearLevel" class="btn btn-primary">
                                                    Reset
                                                </button>
                                            </li>

                                            <li class="nav-item" role="presentation" style="display: none;">
                                                <button class="nav-link active text-white bg-primary"
                                                        id="previousClick"
                                                        data-bs-toggle="pill"
                                                        data-bs-target="#pills-Level"
                                                        type="button"
                                                        role="tab"
                                                        aria-controls="pills-Level"
                                                        aria-selected="true">
                                                    Previous
                                                </button>
                                            </li>

                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link"
                                                        id="nextFunction"
                                                        type="button"
                                                        role="tab"
                                                        aria-controls="pills-Teams"
                                                        aria-selected="false">
                                                    Next
                                                </button>
                                            </li>
                                            <li class="nav-item" role="presentation" style="display: none;">
                                                <button id="saveLevel" class="btn btn-primary">
                                                    Insert
                                                </button>
                                            </li>
                                        </ul>
                                        
                                    
                                    </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary">
                        <i class="bi bi-info-circle me-1"></i>Note: All fields are mandatory
                        except Optional
                    </small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" id="savebtnClick">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div> 
        
       
</div>

    <!--Modal Delete-->
    <div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">

                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p class="d-flex align-items-center justify-content-center gap-1">
                            You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="deleteData"></span>
                            data?
                        </p>

                    </div>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="confirmDeleteBtn">Yes</button>
                </div>

            </div>
        </div>
    </div>
    <div class="modal fade" data-bs-backdrop="static" id="DeleteModalLevel" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">

                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p class="d-flex align-items-center justify-content-center gap-1">
                            You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="deleteLevelData"></span>
                            data?
                        </p>

                    </div>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="confirmDeleteLevelBtn">Yes</button>
                </div>

            </div>
        </div>
    </div>



<div id="ManageExCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.CreateAndEdit" aria-hidden="true"></div>
<div id="ManageExDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.Delete" aria-hidden="true"></div>
<div id="ConfigurationCreatelev"
     data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit"
     aria-hidden="true"></div>
<div id="ConfigurationDeletelev"
     data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete"
     aria-hidden="true"></div>
    <script src="~/js/Manage/Escalation Matrix/escalationmatrix.js"></script>
