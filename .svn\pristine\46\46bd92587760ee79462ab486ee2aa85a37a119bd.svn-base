﻿using ContinuityPatrol.Application.Features.Rto.Commands.Update;
using ContinuityPatrol.Application.Features.Rto.Events.Update;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Rto.Commands
{
    public class UpdateRtoTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IRtoRepository> _mockRtoRepository;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly UpdateRtoCommandHandler _handler;

        public UpdateRtoTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockRtoRepository = new Mock<IRtoRepository>();
            _mockPublisher = new Mock<IPublisher>();
            _handler = new UpdateRtoCommandHandler(_mockMapper.Object, _mockRtoRepository.Object, _mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_Should_Update_Rto_When_RtoExists()
        {
            var rtoId = Guid.NewGuid().ToString();
            var existingRto = new Domain.Entities.Rto
            {
                ReferenceId = rtoId,
                BusinessServiceName = "OldServiceName"
            };

            var updateCommand = new UpdateRtoCommand
            {
                Id = rtoId,
                BusinessServiceName = "UpdatedServiceName"
            };

            _mockRtoRepository
                .Setup(repo => repo.GetByReferenceIdAsync(rtoId))
                .ReturnsAsync(existingRto);

            _mockMapper
                .Setup(mapper => mapper.Map(updateCommand, existingRto, typeof(UpdateRtoCommand), typeof(Domain.Entities.Rto)))
                .Callback(() => existingRto.BusinessServiceName = updateCommand.BusinessServiceName);

            _mockRtoRepository
                .Setup(repo => repo.UpdateAsync(existingRto))
                .ToString();

            var result = await _handler.Handle(updateCommand, CancellationToken.None);

            //result.Should().NotBeNull();
            //result.Message.Should().Contain("updated");
            //result.Id.Should().Be(rtoId);

            _mockRtoRepository.Verify(repo => repo.GetByReferenceIdAsync(rtoId), Times.Once);
            _mockRtoRepository.Verify(repo => repo.UpdateAsync(existingRto), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RtoUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Throw_NotFoundException_When_RtoDoesNotExist()
        {
            var rtoId = Guid.NewGuid().ToString();
            var updateCommand = new UpdateRtoCommand { Id = rtoId, BusinessServiceName = "UpdatedServiceName" };

            _mockRtoRepository
                .Setup(repo => repo.GetByReferenceIdAsync(rtoId))
                .ReturnsAsync((Domain.Entities.Rto)null);

            Func<Task> act = async () => await _handler.Handle(updateCommand, CancellationToken.None);

            //await act.Should().ThrowAsync<NotFoundException>()
            //    .WithMessage("*Rto*");

            _mockRtoRepository.Verify(repo => repo.GetByReferenceIdAsync(rtoId), Times.Once);
            _mockRtoRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.Rto>()), Times.Never);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RtoUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_Should_Publish_Event_After_Update()
        {
            var rtoId = Guid.NewGuid().ToString();
            var existingRto = new Domain.Entities.Rto
            {
                ReferenceId = rtoId,
                BusinessServiceName = "OldServiceName"
            };

            var updateCommand = new UpdateRtoCommand
            {
                Id = rtoId,
                BusinessServiceName = "UpdatedServiceName"
            };

            _mockRtoRepository
                .Setup(repo => repo.GetByReferenceIdAsync(rtoId))
                .ReturnsAsync(existingRto);

            _mockMapper
                .Setup(mapper => mapper.Map(updateCommand, existingRto, typeof(UpdateRtoCommand), typeof(Domain.Entities.Rto)))
                .Callback(() => existingRto.BusinessServiceName = updateCommand.BusinessServiceName);

            _mockRtoRepository
                .Setup(repo => repo.UpdateAsync(existingRto))
                .ToString();

            var result = await _handler.Handle(updateCommand, CancellationToken.None);

            //result.Should().NotBeNull();
            //result.Message.Should().Contain("updated");

            _mockPublisher.Verify(pub => pub.Publish(It.Is<RtoUpdatedEvent>(e => e.Name == "UpdatedServiceName"), It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}
