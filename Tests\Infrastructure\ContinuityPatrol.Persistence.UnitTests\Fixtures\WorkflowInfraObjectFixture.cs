using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowInfraObjectFixture : IDisposable
{
    public List<WorkflowInfraObject> WorkflowInfraObjectPaginationList { get; set; }
    public List<WorkflowInfraObject> WorkflowInfraObjectList { get; set; }
    public WorkflowInfraObject WorkflowInfraObjectDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowInfraObjectFixture()
    {
        var fixture = new Fixture();

        WorkflowInfraObjectList = fixture.Create<List<WorkflowInfraObject>>();
        WorkflowInfraObjectList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        WorkflowInfraObjectList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowInfraObjectPaginationList = fixture.CreateMany<WorkflowInfraObject>(20).ToList();

        WorkflowInfraObjectPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        WorkflowInfraObjectPaginationList.ForEach(x => x.CompanyId = CompanyId);


        WorkflowInfraObjectDto = fixture.Create<WorkflowInfraObject>();

        WorkflowInfraObjectDto.CompanyId = CompanyId;
        WorkflowInfraObjectDto.ReferenceId = Guid.NewGuid().ToString();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
