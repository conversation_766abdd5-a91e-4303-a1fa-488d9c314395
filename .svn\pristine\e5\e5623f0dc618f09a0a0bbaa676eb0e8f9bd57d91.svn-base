﻿using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.LicenseManager.Queries.GetList;

public class GetLicenseManagerListQueryHandler : IRequestHandler<GetLicenseManagerListQuery, List<LicenseManagerListVm>>
{
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly IMapper _mapper;

    public GetLicenseManagerListQueryHandler(IMapper mapper, ILicenseManagerRepository licenseManagerRepository,
        ILicenseInfoRepository licenseInfoRepository)
    {
        _mapper = mapper;
        _licenseManagerRepository = licenseManagerRepository;
        _licenseInfoRepository = licenseInfoRepository;
    }

    public async Task<List<LicenseManagerListVm>> Handle(GetLicenseManagerListQuery request,
        CancellationToken cancellationToken)
    {
        var licenseKey = await _licenseManagerRepository.ListAllBaseLicense();

        var licenseList = new List<LicenseManagerListVm>();

        foreach (var licenseDtl in licenseKey)
        {
            var databaseCount = await _licenseInfoRepository
                .GetAvailableCountByLicenseId(licenseDtl.ReferenceId, Modules.Database.ToString());
            var replicationCount = await _licenseInfoRepository
                .GetAvailableCountByLicenseId(licenseDtl.ReferenceId, Modules.Replication.ToString());

            var storageCount = await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(licenseDtl.ReferenceId, Modules.Server.ToString(),
                    "storage");
            var virtualizationCount = await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(licenseDtl.ReferenceId, Modules.Server.ToString(),
                    "virtualization");
            var applicationCount = await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(licenseDtl.ReferenceId, Modules.Server.ToString(),
                    "application");
            var dnsCount = await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(licenseDtl.ReferenceId, Modules.Server.ToString(), "dns")
                ;
            var networkCount = await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(licenseDtl.ReferenceId, Modules.Server.ToString(),
                    "network");
            var thirdPartyCount = await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(licenseDtl.ReferenceId, Modules.Server.ToString(),
                    "thirdparty");

            var licenseManagerListVm = _mapper.Map<LicenseManagerListVm>(licenseDtl);

            licenseManagerListVm.DerivedLicenseAvailableCountVm = new DerivedLicenseAvailableCountVm
            {
                DatabaseAvailableCount =
                    GetJsonProperties.GetLicenseJsonValue(licenseDtl.Properties, "primarydatabaseCount") -
                    databaseCount,
                ReplicationAvailableCount =
                    GetJsonProperties.GetLicenseJsonValue(licenseDtl.Properties, "primaryreplicationCount") -
                    replicationCount,
                StorageAvailableCount =
                    GetJsonProperties.GetLicenseJsonValue(licenseDtl.Properties, "primarystorageCount") -
                    storageCount,
                ApplicationAvailableCount =
                    GetJsonProperties.GetLicenseJsonValue(licenseDtl.Properties, "primaryapplicationCount") -
                    applicationCount,
                VirtualizationAvailableCount =
                    GetJsonProperties.GetLicenseJsonValue(licenseDtl.Properties, "primaryvirtualizationCount") -
                    virtualizationCount,
                DnsAvailableCount = GetJsonProperties.GetLicenseJsonValue(licenseDtl.Properties, "primarydnsCount") -
                                    dnsCount,
                NetworkAvailableCount =
                    GetJsonProperties.GetLicenseJsonValue(licenseDtl.Properties, "primarynetworkCount") -
                    networkCount,
                ThirdPartyAvailableCount =
                    GetJsonProperties.GetLicenseJsonValue(licenseDtl.Properties, "primarythirdPartyCount") -
                    thirdPartyCount
            };

            licenseList.Add(licenseManagerListVm);
        }

        var licenseLists = _mapper.Map<List<LicenseManagerListVm>>(licenseList);

        return licenseLists.Count <= 0
            ? new List<LicenseManagerListVm>()
            : _mapper.Map<List<LicenseManagerListVm>>(licenseLists);
    }
}