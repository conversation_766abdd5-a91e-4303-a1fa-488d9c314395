﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore.Metadata;

namespace ContinuityPatrol.Persistence.Persistence;

public partial class ApplicationDbContext : ModuleDbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options,
        ILoggedInUserService loggedInUserService)
        : base(options, loggedInUserService)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        #region Old-Mapping

        //modelBuilder.Entity<GlobalVariable>(entity => entity.ToTable("global_variable"));
        //modelBuilder.Entity<FiaCost>(entity => entity.ToTable("fia_cost"));
        //modelBuilder.Entity<ApprovalMatrixUsers>(entity => entity.ToTable("approval_matrix_users"));
        //modelBuilder.Entity<PowerMaxMonitorStatus>(entity => entity.ToTable("power_max_monitor_status"));
        //modelBuilder.Entity<WorkflowTemp>(entity => entity.ToTable("workflow_temp"));
        //modelBuilder.Entity<AdPasswordJob>(entity => entity.ToTable("ad_password_job"));
        //modelBuilder.Entity<WorkflowRunningAction>(entity => entity.ToTable("workflow_running_action"));
        //modelBuilder.Entity<AdPasswordExpire>(entity => entity.ToTable("ad_password_expire"));
        //modelBuilder.Entity<FiaInterval>(entity => entity.ToTable("fia_interval"));
        //modelBuilder.Entity<FiaImpactType>(entity => entity.ToTable("fia_impact_type"));
        //modelBuilder.Entity<FiaImpactCategory>(entity => entity.ToTable("fia_impact_category"));
        //modelBuilder.Entity<CyberAirGap>(entity => entity.ToTable("cyber_air_gap"));
        //modelBuilder.Entity<CyberSnaps>(entity => entity.ToTable("cyber_snaps"));
        //modelBuilder.Entity<WorkflowDrCalender>(entity => entity.ToTable("workflow_dr_calender"));
        //modelBuilder.Entity<CyberAlert>(entity => entity.ToTable("cyber_alert"));
        //modelBuilder.Entity<CyberMappingHistory>(entity => entity.ToTable("cyber_mapping_history"));
        //modelBuilder.Entity<CyberComponentMapping>(entity => entity.ToTable("cyber_component_mapping"));
        //modelBuilder.Entity<CyberAirGapStatus>(entity => entity.ToTable("cyber_air_gap_status"));
        //modelBuilder.Entity<CyberAirGapLog>(entity => entity.ToTable("cyber_air_gap_log"));
        //modelBuilder.Entity<CyberJobManagement>(entity => entity.ToTable("cyber_job_management"));
        //modelBuilder.Entity<CyberComponentGroup>(entity => entity.ToTable("cyber_component_group"));
        //modelBuilder.Entity<CyberComponent>(entity => entity.ToTable("cyber_component"));
        //modelBuilder.Entity<DriftResourceSummary>(entity => entity.ToTable("drift_resource_summary"));
        //modelBuilder.Entity<DriftEvent>(entity => entity.ToTable("drift_event"));
        //modelBuilder.Entity<DriftManagementMonitorLogs>(entity => entity.ToTable("drift_management_monitor_logs"));
        //modelBuilder.Entity<DriftManagementMonitorStatus>(entity => entity.ToTable("drift_management_monitor_status"));
        //modelBuilder.Entity<ApprovalMatrixRequest>(entity => entity.ToTable("approval_matrix_request"));
        //modelBuilder.Entity<ApprovalMatrixApproval>(entity => entity.ToTable("approval_matrix_approval"));
        //modelBuilder.Entity<DriftProfile>(entity => entity.ToTable("drift_profile"));
        //modelBuilder.Entity<DriftImpactTypeMaster>(entity => entity.ToTable("drift_impacttype_master"));
        //modelBuilder.Entity<DriftCategoryMaster>(entity => entity.ToTable("drift_category_master"));
        //modelBuilder.Entity<DriftParameter>(entity => entity.ToTable("drift_parameter"));
        //modelBuilder.Entity<DriftJob>(entity => entity.ToTable("drift_job"));
        //modelBuilder.Entity<BulkImportActionResult>(entity => entity.ToTable("bulk_import_action_result"));
        //modelBuilder.Entity<BulkImportOperation>(entity => entity.ToTable("bulk_import_operation"));
        //modelBuilder.Entity<BulkImportOperationGroup>(entity => entity.ToTable("bulk_import_operation_group"));
        //modelBuilder.Entity<DynamicDashboardWidget>(entity => entity.ToTable("dynamic_dashboard_widget"));
        //modelBuilder.Entity<DynamicDashboardMap>(entity => entity.ToTable("dynamic_dashboard_map"));
        //modelBuilder.Entity<DynamicSubDashboard>(entity => entity.ToTable("dynamic_sub_dashboard"));
        //modelBuilder.Entity<DynamicDashboard>(entity => entity.ToTable("dynamic_dashboard"));
        //modelBuilder.Entity<Setting>(entity => entity.ToTable("setting"));
        //modelBuilder.Entity<VeritasCluster>(entity => entity.ToTable("veritas_cluster"));
        //modelBuilder.Entity<HacmpCluster>(entity => entity.ToTable("hacmp_cluster"));
        //modelBuilder.Entity<FiaTemplate>(entity => entity.ToTable("fia_template"));
        //modelBuilder.Entity<BackUpLog>(entity => entity.ToTable("backup_log"));
        //modelBuilder.Entity<InfraMaster>(entity => entity.ToTable("infra_master"));
        //modelBuilder.Entity<BiaRules>(entity => entity.ToTable("bia_rules"));
        //modelBuilder.Entity<BackUp>(entity => entity.ToTable("backup"));
        //modelBuilder.Entity<Archive>(entity => entity.ToTable("archive"));
        //modelBuilder.Entity<PageWidget>(entity => entity.ToTable("page_widget"));
        //modelBuilder.Entity<PageBuilder>(entity => entity.ToTable("page_builder"));
        //modelBuilder.Entity<PageSolutionMapping>(entity => entity.ToTable("page_solution_mapping"));
        //modelBuilder.Entity<SolutionHistory>(entity => entity.ToTable("solution_history"));
        //modelBuilder.Entity<FormHistory>(entity => entity.ToTable("form_history"));
        //modelBuilder.Entity<Form>(entity => entity.ToTable("form"));
        //modelBuilder.Entity<FormType>(entity => entity.ToTable("form_type"));
        //modelBuilder.Entity<LicenseManager>(entity => entity.ToTable("compliance_control"));
        //modelBuilder.Entity<WorkflowAction>(entity => entity.ToTable("workflow_action"));
        //modelBuilder.Entity<WorkflowCategory>(entity => entity.ToTable("workflow_category"));
        //modelBuilder.Entity<PluginManager>(entity => entity.ToTable("plugin_manager"));
        //modelBuilder.Entity<PluginManagerHistory>(entity => entity.ToTable("plugin_manager_history"));
        //modelBuilder.Entity<LicenseInfo>(entity => entity.ToTable("compliance_info"));
        //modelBuilder.Entity<LoadBalancer>(entity => entity.ToTable("node_configuration"));
        //modelBuilder.Entity<LicenseHistory>(entity => entity.ToTable("compliance_control_history"));
        //modelBuilder.Entity<FormTypeCategory>(entity => entity.ToTable("form_type_category"));
        //modelBuilder.Entity<GroupPolicy>(entity => entity.ToTable("group_policy"));
        //modelBuilder.Entity<ComplianceHistory>(entity => entity.ToTable("compliance_history"));
        //modelBuilder.Entity<Setting>(entity => entity.ToTable("setting"));
        //modelBuilder.Entity<DrReady>(entity => entity.ToTable("dr_ready"));
        //modelBuilder.Entity<ImpactActivity>(entity => entity.ToTable("impact_activity"));
        //modelBuilder.Entity<Rto>(entity => entity.ToTable("rto"));
        //modelBuilder.Entity<DataLag>(entity => entity.ToTable("datalag"));
        //modelBuilder.Entity<DashboardView>(entity => entity.ToTable("dashboard_view"));
        //modelBuilder.Entity<DashboardViewLog>(entity => entity.ToTable("dashboard_view_log"));
        //modelBuilder.Entity<DRReadyLog>(entity => entity.ToTable("dr_ready_log"));
        //modelBuilder.Entity<HeatMapLog>(entity => entity.ToTable("heatmap_log"));
        //modelBuilder.Entity<HeatMapStatus>(entity => entity.ToTable("heatmap_status"));
        //modelBuilder.Entity<InfraSummary>(entity => entity.ToTable("infra_summary"));
        //modelBuilder.Entity<InfraObjectInfo>(entity => entity.ToTable("infraobject_info"));
        //modelBuilder.Entity<InfraOperationalStatus>(entity => entity.ToTable("infra_operational_status"));
        //modelBuilder.Entity<ImpactAvailability>(entity => entity.ToTable("impact_availability"));
        //modelBuilder.Entity<DRReadyStatus>(entity => entity.ToTable("dr_ready_status"));
        //modelBuilder.Entity<RiskMitigation>(entity => entity.ToTable("risk_mitigation"));
        //modelBuilder.Entity<BusinessServiceAvailability>(entity => entity.ToTable("business_service_availability"));
        //modelBuilder.Entity<BusinessServiceEvaluation>(entity => entity.ToTable("business_service_evaluation"));
        //modelBuilder.Entity<BusinessServiceHealthStatus>(entity => entity.ToTable("business_service_health_status"));
        //modelBuilder.Entity<BusinessServiceHealthLog>(entity => entity.ToTable("business_service_health_log"));
        //modelBuilder.Entity<DashboardView>(entity => entity.ToTable("dashboard_view"));
        //modelBuilder.Entity<RsyncOption>(entity => entity.ToTable("rsync_option"));
        //modelBuilder.Entity<RoboCopy>(entity => entity.ToTable("robocopy"));
        //modelBuilder.Entity<DataSyncOptions>(entity => entity.ToTable("datasync_options"));
        //modelBuilder.Entity<SiteLocation>(entity => entity.ToTable("site_location"));
        //modelBuilder.Entity<IncidentManagementSummary>(entity => entity.ToTable("incident_management_summary"));
        //modelBuilder.Entity<IncidentManagement>(entity => entity.ToTable("incident_management"));
        //modelBuilder.Entity<ServerSubType>(entity => entity.ToTable("server_subtype"));
        //modelBuilder.Entity<AccessManager>(entity => entity.ToTable("access_manager"));
        //modelBuilder.Entity<BusinessFunction>(entity => entity.ToTable("business_function"));
        //modelBuilder.Entity<BusinessService>(entity => entity.ToTable("business_service"));
        //modelBuilder.Entity<Company>(entity => entity.ToTable("company"));
        //modelBuilder.Entity<Job>(entity => entity.ToTable("job"));
        //modelBuilder.Entity<Node>(entity => entity.ToTable("node"));
        //modelBuilder.Entity<Database>(entity => entity.ToTable("database"));
        //modelBuilder.Entity<InfraObject>(entity => entity.ToTable("infraobject"));
        //modelBuilder.Entity<InfraObjectScheduler>(entity => entity.ToTable("infraobject_scheduler"));
        //modelBuilder.Entity<Server>(entity => entity.ToTable("server"));
        //modelBuilder.Entity<Replication>(entity => entity.ToTable("replication"));
        //modelBuilder.Entity<CredentialProfile>(entity => entity.ToTable("credential_profile"));
        //modelBuilder.Entity<ServerType>(entity => entity.ToTable("server_type"));
        //modelBuilder.Entity<ComponentType>(entity => entity.ToTable("component_type"));
        //modelBuilder.Entity<SingleSignOn>(entity => entity.ToTable("single_sign_on"));
        //modelBuilder.Entity<SiteType>(entity => entity.ToTable("site_type"));
        //modelBuilder.Entity<Site>(entity => entity.ToTable("site"));
        //modelBuilder.Entity<InfraObjectScheduler>(entity => entity.ToTable("infraobject_scheduler"));
        //modelBuilder.Entity<TeamMaster>(entity => entity.ToTable("team_master"));
        //modelBuilder.Entity<TeamResource>(entity => entity.ToTable("team_resources"));
        //modelBuilder.Entity<User>(entity => entity.ToTable("user"));
        //modelBuilder.Entity<UserInfo>(entity => entity.ToTable("user_info"));
        //modelBuilder.Entity<UserInfraObject>(entity => entity.ToTable("user_infraobject"));
        //modelBuilder.Entity<UserLogin>(entity => entity.ToTable("user_login"));
        //modelBuilder.Entity<UserActivity>(entity => entity.ToTable("user_activity"));
        //modelBuilder.Entity<UserRole>(entity => entity.ToTable("user_role"));
        //modelBuilder.Entity<UserCredential>(entity => entity.ToTable("user_credential"));
        //modelBuilder.Entity<RpoSlaDeviationReport>(entity => entity.ToTable("rpo_sla_deviation_report"));
        //modelBuilder.Entity<SmtpConfiguration>(entity => entity.ToTable("smtp_configuration"));
        //modelBuilder.Entity<AlertReceiver>(entity => entity.ToTable("alert_receiver"));
        //modelBuilder.Entity<AlertNotification>(entity => entity.ToTable("alert_notification"));
        //modelBuilder.Entity<AlertInformation>(entity => entity.ToTable("alert_information"));
        //modelBuilder.Entity<SmsConfiguration>(entity => entity.ToTable("sms_configuration"));
        //modelBuilder.Entity<AlertMaster>(entity => entity.ToTable("alert_master"));
        //modelBuilder.Entity<Alert>(entity => entity.ToTable("alert"));
        //modelBuilder.Entity<TableAccess>(entity => entity.ToTable("table_access"));
        //modelBuilder.Entity<DataSetColumns>(entity => entity.ToTable("dataset_columns"));
        //modelBuilder.Entity<Report>(entity => entity.ToTable("report"));
        //modelBuilder.Entity<ReportSchedule>(entity => entity.ToTable("report_schedule"));
        //modelBuilder.Entity<ReportScheduleExecution>(entity => entity.ToTable("reportschedule_executiondetails"));
        //modelBuilder.Entity<UserGroup>(entity => entity.ToTable("user_group"));
        //modelBuilder.Entity<DataSet>(entity => entity.ToTable("dataset"));
        //modelBuilder.Entity<Workflow>(entity => entity.ToTable("workflow"));
        //modelBuilder.Entity<WorkflowHistory>(entity => entity.ToTable("workflow_history"));
        //modelBuilder.Entity<WorkflowInfraObject>(entity => entity.ToTable("workflow_infraobject"));
        //modelBuilder.Entity<FourEyeApprovers>(entity => entity.ToTable("four_eye_approvers"));
        //modelBuilder.Entity<DrCalenderActivity>(entity => entity.ToTable("dr_calender_activity"));
        //modelBuilder.Entity<ReplicationMaster>(entity => entity.ToTable("replication_master"));
        //modelBuilder.Entity<InfraReplicationMapping>(entity => entity.ToTable("infra_replication_mapping"));
        //modelBuilder.Entity<AboutCp>(entity => entity.ToTable("about_cp"));
        //modelBuilder.Entity<GlobalSetting>(entity => entity.ToTable("global_setting"));
        //modelBuilder.Entity<IncidentManagement>(entity => entity.ToTable("incident_management"));
        //modelBuilder.Entity<EscalationMatrix>(entity => entity.ToTable("escalationmatrix"));
        //modelBuilder.Entity<EscalationMatrixLevel>(entity => entity.ToTable("escalationlevels"));
        //modelBuilder.Entity<TableInformation>().HasNoKey();
        //modelBuilder.Entity<ColumnInfo>().HasNoKey();
        //modelBuilder.Entity<Template>(entity => entity.ToTable("template"));
        //modelBuilder.Entity<TemplateHistory>(entity => entity.ToTable("template_history"));
        //modelBuilder.Entity<WorkflowExecutionTemp>(entity => entity.ToTable("workflow_execution_temp"));
        //modelBuilder.Entity<Workflow>(entity => entity.ToTable("workflow"));
        //modelBuilder.Entity<ApprovalMatrix>(entity => entity.ToTable("approval_matrix"));
        //modelBuilder.Entity<WorkflowInfraObject>(entity => entity.ToTable("workflow_infraobject"));
        //modelBuilder.Entity<WorkflowProfileInfo>(entity =>entity.ToTable("workflow_profile_info"));
        //modelBuilder.Entity<WorkflowActionType>(entity => entity.ToTable("workflow_actiontype"));
        //modelBuilder.Entity<WorkflowProfile>(entity => entity.ToTable("workflow_profile"));
        //modelBuilder.Entity<WorkflowOperation>(entity => entity.ToTable("workflow_operation"));
        //modelBuilder.Entity<WorkflowOperationGroup>(entity => entity.ToTable("workflow_operation_group"));
        //modelBuilder.Entity<WorkflowActionResult>(entity => entity.ToTable("workflow_action_result"));
        //modelBuilder.Entity<NodeWorkflowExecution>(entity => entity.ToTable("node_workflow_execution"));
        //modelBuilder.Entity<WorkflowExecutionEventLog>(entity => entity.ToTable("workflow_execution_event_log"));
        //modelBuilder.Entity<WorkflowPermission>(entity => entity.ToTable("workflow_permission"));
        //modelBuilder.Entity<InfraObjectSchedulerWorkflowDetail>(entity => entity.ToTable("infraobject_scheduler_workflow_details"));
        //modelBuilder.Entity<WorkflowPrediction>(entity => entity.ToTable("workflow_prediction"));
        //modelBuilder.Entity<WorkflowActionFieldMaster>(entity => entity.ToTable("workflow_actionfield_master"));
        //modelBuilder.Entity<MSSQLMonitorStatus>(entity => entity.ToTable("mssql_monitor_status"));
        //modelBuilder.Entity<MYSQLMonitorStatus>(entity => entity.ToTable("mysql_monitor_status"));
        //modelBuilder.Entity<OracleMonitorStatus>(entity => entity.ToTable("oracle_monitor_status"));
        //modelBuilder.Entity<OracleRACMonitorStatus>(entity => entity.ToTable("oraclerac_monitor_status"));
        //modelBuilder.Entity<PostgresMonitorStatus>(entity => entity.ToTable("postgres_monitor_status"));
        //modelBuilder.Entity<MYSQLMonitorLogs>(entity => entity.ToTable("mysql_monitor_logs"));
        //modelBuilder.Entity<MSSQLMonitorLogs>(entity => entity.ToTable("mssql_monitor_logs"));
        //modelBuilder.Entity<OracleMonitorLogs>(entity => entity.ToTable("oracle_monitor_logs"));
        //modelBuilder.Entity<PostgresMonitorLogs>(entity => entity.ToTable("postgres_monitor_logs"));
        //modelBuilder.Entity<OracleRACMonitorLogs>(entity => entity.ToTable("oraclerac_monitor_logs"));
        //modelBuilder.Entity<MSSQLAlwaysOnMonitorLogs>(entity => entity.ToTable("mssql_alwayson_monitor_logs"));
        //modelBuilder.Entity<MSSQLAlwaysOnMonitorStatus>(entity => entity.ToTable("mssql_alwayson_monitor_status"));
        //modelBuilder.Entity<StateMonitorLog>(entity => entity.ToTable("state_monitor_log"));
        //modelBuilder.Entity<StateMonitorStatus>(entity => entity.ToTable("state_monitor_status"));
        //modelBuilder.Entity<MonitorService>(entity => entity.ToTable("monitorservice"));
        //modelBuilder.Entity<Db2HaDrMonitorStatus>(entity => entity.ToTable("db2hadr_monitor_status"));
        //modelBuilder.Entity<DB2HADRMonitorLog>(entity => entity.ToTable("db2hadr_monitor_logs"));
        //modelBuilder.Entity<MongoDbMonitorStatus>(entity => entity.ToTable("mongodb_monitor_status"));
        //modelBuilder.Entity<MongoDBMonitorLog>(entity => entity.ToTable("mongodb_monitor_logs"));
        //modelBuilder.Entity<MSSQLDBMirroringLogs>(entity => entity.ToTable("mssqldbMiroring_monitor_logs"));
        //modelBuilder.Entity<MSSQLDBMirroringStatus>(entity => entity.ToTable("mssqldbMiroring_monitor_status"));
        ////modelBuilder.Entity<ApprovalMatrix>(entity => entity.ToTable("approval_matrix_new"));
        //modelBuilder.Entity<SVCGMMonitorLog>(entity => entity.ToTable("svc_globalmirror_monitor_logs"));
        //modelBuilder.Entity<SVCGMMonitorStatus>(entity => entity.ToTable("svc_globalmirror_monitor_status"));
        //modelBuilder.Entity<MsSqlNativeLogShippingMonitorStatus>(entity => entity.ToTable("mssql_nls_monitor_status"));
        //modelBuilder.Entity<MssqlNativeLogShippingMonitorLog>(entity => entity.ToTable("mssql_nls_monitor_logs"));
        //modelBuilder.Entity<Incident>(entity => entity.ToTable("incident"));
        //modelBuilder.Entity<IncidentDaily>(entity => entity.ToTable("incident_daily"));
        //modelBuilder.Entity<IncidentLogs>(entity => entity.ToTable("incident_logs"));
        //modelBuilder.Entity<RoboCopyJob>(entity => entity.ToTable("robocopy_job"));
        //modelBuilder.Entity<DataSyncJob>(entity => entity.ToTable("datasync_job"));
        //modelBuilder.Entity<RsyncJob>(entity => entity.ToTable("rsync_job"));
        //modelBuilder.Entity<ReplicationJob>(entity => entity.ToTable("replication_job"));
        //modelBuilder.Entity<RoboCopyMonitorLogs>(entity => entity.ToTable("robocopy_monitor_logs"));
        //modelBuilder.Entity<RsyncMonitorLog>(entity => entity.ToTable("rsync_monitor_logs"));
        //modelBuilder.Entity<SRMMonitorLog>(entity => entity.ToTable("srm_monitor_logs"));
        //modelBuilder.Entity<AzureStorageAccountMonitorlogs>(entity => entity.ToTable("azurestorage_account_monitor_logs"));
        //modelBuilder.Entity<InfraObjectSchedulerLogs>(entity => entity.ToTable("infraobject_scheduler_logs"));
        //modelBuilder.Entity<RpForVmMonitorStatus>(entity => entity.ToTable("rpforvm_monitor_status"));
        //modelBuilder.Entity<RpForVmMonitorLogs>(entity => entity.ToTable("rpforvm_monitor_logs"));
        //modelBuilder.Entity<ComponentSaveAll>(entity => entity.ToTable("component_saveall"));
        //modelBuilder.Entity<RpForVmCgEnableDisableStatus>(entity => entity.ToTable("rpforvm_cgenable_disable_status"));
        //modelBuilder.Entity<ServerLog>(entity => entity.ToTable("server_logs"));
        //modelBuilder.Entity<RpForVmCGMonitorLogs>(entity => entity.ToTable("rpforvm_cg_monitor_logs"));
        //modelBuilder.Entity<RpForVmCGMonitorStatus>(entity=>entity.ToTable("rpforvm_cg_monitor_status"));    
        //modelBuilder.Entity<FastCopyMonitor>(entity=>entity.ToTable("fast_copy_monitor"));
        //modelBuilder.Entity<ActiveDirectoryMonitorLog>(entity => entity.ToTable("activedirectory_monitor_logs"));
        //modelBuilder.Entity<SchedulerWorkflowActionResults>(entity => entity.ToTable("scheduler_workflow_actions_result"));
        //modelBuilder.Entity<DataSyncMonitorLog>(entity => entity.ToTable("datasync_monitor_logs"));
        //modelBuilder.Entity<FastCopyMonitorLog>(entity => entity.ToTable("fast_copy_monitor_logs"));
        //modelBuilder.Entity<ZertoVpgMonitorLog>(entity => entity.ToTable("zerto_vpg_monitor_logs"));
        //modelBuilder.Entity<SybaseRSHADRMonitorLog>(entity => entity.ToTable("sybase_rs_hadr_monitor_logs"));
        //modelBuilder.Entity<ResiliencyReadyWorkflowScheduleLog>(entity => entity.ToTable("resiliency_ready_workflow_schedule_logs"));
        //#region Views
        //modelBuilder.Entity<ServerView>().ToView("server_view").HasNoKey();
        //modelBuilder.Entity<DatabaseView>().ToView("database_view").HasNoKey();
        //modelBuilder.Entity<ReplicationView>().ToView("replication_view").HasNoKey();
        //modelBuilder.Entity<WorkflowCategoryView>().ToView("workflow_categoryView").HasNoKey();
        //modelBuilder.Entity<WorkflowView>().ToView("workflow_view").HasNoKey();
        //modelBuilder.Entity<UserView>().ToView("user_view").HasNoKey();
        //modelBuilder.Entity<InfraObjectView>().ToView("infraobject_view").HasNoKey();
        //modelBuilder.Entity<WorkflowProfileInfoView>().ToView("workflow_profile_info_view").HasNoKey();
        //modelBuilder.Entity<InfraDashboardView>().ToView("infra_dashboard_view").HasNoKey();
        //modelBuilder.Entity<OneViewEntitiesEventView>().ToView("OneView_Entities_Event").HasNoKey(); 
        //modelBuilder.Entity<OneViewRiskMitigationFailedDrillView>().ToView("OneView_RiskMitigation_Failed_Drill_View").HasNoKey();
        //modelBuilder.Entity<OneViewRiskMitigationCyberSecurityView>().ToView("OneView_Risk_Mitigation_CyberSecurityView").HasNoKey();
        //#endregion

        #endregion

        modelBuilder.Entity<GlobalVariable>(entity => entity.ToTable("GLOBAL_VARIABLE"));
        modelBuilder.Entity<FiaCost>(entity => entity.ToTable("FIA_COST"));
        modelBuilder.Entity<ApprovalMatrixUsers>(entity => entity.ToTable("APPROVAL_MATRIX_USERS"));
        modelBuilder.Entity<PowerMaxMonitorStatus>(entity => entity.ToTable("POWER_MAX_MONITOR_STATUS"));
        modelBuilder.Entity<WorkflowTemp>(entity => entity.ToTable("WORKFLOW_TEMP"));
        modelBuilder.Entity<AdPasswordJob>(entity => entity.ToTable("AD_PASSWORD_JOB"));
        modelBuilder.Entity<WorkflowRunningAction>(entity => entity.ToTable("WORKFLOW_RUNNING_ACTION"));
        modelBuilder.Entity<AdPasswordExpire>(entity => entity.ToTable("AD_PASSWORD_EXPIRE"));
        modelBuilder.Entity<FiaInterval>(entity => entity.ToTable("FIA_INTERVAL"));
        modelBuilder.Entity<FiaImpactType>(entity => entity.ToTable("FIA_IMPACT_TYPE"));
        modelBuilder.Entity<FiaImpactCategory>(entity => entity.ToTable("FIA_IMPACT_CATEGORY"));
        modelBuilder.Entity<CyberAirGap>(entity => entity.ToTable("CYBER_AIR_GAP"));
        modelBuilder.Entity<CyberSnaps>(entity => entity.ToTable("CYBER_SNAPS"));
        modelBuilder.Entity<WorkflowDrCalender>(entity => entity.ToTable("WORKFLOW_DR_CALENDER"));
        modelBuilder.Entity<CyberAlert>(entity => entity.ToTable("CYBER_ALERT"));
        modelBuilder.Entity<CyberMappingHistory>(entity => entity.ToTable("CYBER_MAPPING_HISTORY"));
        modelBuilder.Entity<CyberComponentMapping>(entity => entity.ToTable("CYBER_COMPONENT_MAPPING"));
        modelBuilder.Entity<CyberAirGapStatus>(entity => entity.ToTable("CYBER_AIR_GAP_STATUS"));
        modelBuilder.Entity<CyberAirGapLog>(entity => entity.ToTable("CYBER_AIR_GAP_LOG"));
        modelBuilder.Entity<CyberJobManagement>(entity => entity.ToTable("CYBER_JOB_MANAGEMENT"));
        modelBuilder.Entity<CyberComponentGroup>(entity => entity.ToTable("CYBER_COMPONENT_GROUP"));
        modelBuilder.Entity<CyberComponent>(entity => entity.ToTable("CYBER_COMPONENT"));
        modelBuilder.Entity<DriftResourceSummary>(entity => entity.ToTable("DRIFT_RESOURCE_SUMMARY"));
        modelBuilder.Entity<DriftEvent>(entity => entity.ToTable("DRIFT_EVENT"));
        modelBuilder.Entity<DriftManagementMonitorLogs>(entity => entity.ToTable("DRIFT_MANAGEMENT_MONITOR_LOGS"));
        modelBuilder.Entity<DriftManagementMonitorStatus>(entity => entity.ToTable("DRIFT_MANAGEMENT_MONITOR_STATUS"));
        modelBuilder.Entity<ApprovalMatrixRequest>(entity => entity.ToTable("APPROVAL_MATRIX_REQUEST"));
        modelBuilder.Entity<ApprovalMatrixApproval>(entity => entity.ToTable("APPROVAL_MATRIX_APPROVAL"));
        modelBuilder.Entity<DriftProfile>(entity => entity.ToTable("DRIFT_PROFILE"));
        modelBuilder.Entity<DriftImpactTypeMaster>(entity => entity.ToTable("DRIFT_IMPACTTYPE_MASTER"));
        modelBuilder.Entity<DriftCategoryMaster>(entity => entity.ToTable("DRIFT_CATEGORY_MASTER"));
        modelBuilder.Entity<DriftParameter>(entity => entity.ToTable("DRIFT_PARAMETER"));
        modelBuilder.Entity<DriftJob>(entity => entity.ToTable("DRIFT_JOB"));
        modelBuilder.Entity<BulkImportActionResult>(entity => entity.ToTable("BULK_IMPORT_ACTION_RESULT"));
        modelBuilder.Entity<BulkImportOperation>(entity => entity.ToTable("BULK_IMPORT_OPERATION"));
        modelBuilder.Entity<BulkImportOperationGroup>(entity => entity.ToTable("BULK_IMPORT_OPERATION_GROUP"));
        modelBuilder.Entity<DynamicDashboardWidget>(entity => entity.ToTable("DYNAMIC_DASHBOARD_WIDGET"));
        modelBuilder.Entity<DynamicDashboardMap>(entity => entity.ToTable("DYNAMIC_DASHBOARD_MAP"));
        modelBuilder.Entity<DynamicSubDashboard>(entity => entity.ToTable("DYNAMIC_SUB_DASHBOARD"));
        modelBuilder.Entity<DynamicDashboard>(entity => entity.ToTable("DYNAMIC_DASHBOARD"));
        modelBuilder.Entity<Setting>(entity => entity.ToTable("SETTING"));
        modelBuilder.Entity<VeritasCluster>(entity => entity.ToTable("VERITAS_CLUSTER"));
        modelBuilder.Entity<HacmpCluster>(entity => entity.ToTable("HACMP_CLUSTER"));
        modelBuilder.Entity<FiaTemplate>(entity => entity.ToTable("FIA_TEMPLATE"));
        modelBuilder.Entity<BackUpLog>(entity => entity.ToTable("BACKUP_LOG"));
        modelBuilder.Entity<InfraMaster>(entity => entity.ToTable("INFRA_MASTER"));
        modelBuilder.Entity<BiaRules>(entity => entity.ToTable("BIA_RULES"));
        modelBuilder.Entity<BackUp>(entity => entity.ToTable("BACKUP"));
        modelBuilder.Entity<Archive>(entity => entity.ToTable("ARCHIVE"));
        modelBuilder.Entity<PageWidget>(entity => entity.ToTable("PAGE_WIDGET"));
        modelBuilder.Entity<PageBuilder>(entity => entity.ToTable("PAGE_BUILDER"));
        modelBuilder.Entity<PageSolutionMapping>(entity => entity.ToTable("PAGE_SOLUTION_MAPPING"));
        modelBuilder.Entity<SolutionHistory>(entity => entity.ToTable("SOLUTION_HISTORY"));
        modelBuilder.Entity<FormHistory>(entity => entity.ToTable("FORM_HISTORY"));
        modelBuilder.Entity<Form>(entity => entity.ToTable("FORM"));
        modelBuilder.Entity<FormType>(entity => entity.ToTable("FORM_TYPE"));
        modelBuilder.Entity<LicenseManager>(entity => entity.ToTable("COMPLIANCE_CONTROL"));
        modelBuilder.Entity<WorkflowAction>(entity => entity.ToTable("WORKFLOW_ACTION"));
        modelBuilder.Entity<WorkflowCategory>(entity => entity.ToTable("WORKFLOW_CATEGORY"));
        modelBuilder.Entity<PluginManager>(entity => entity.ToTable("PLUGIN_MANAGER"));
        modelBuilder.Entity<PluginManagerHistory>(entity => entity.ToTable("PLUGIN_MANAGER_HISTORY"));
        modelBuilder.Entity<LicenseInfo>(entity => entity.ToTable("COMPLIANCE_INFO"));
        modelBuilder.Entity<LoadBalancer>(entity => entity.ToTable("NODE_CONFIGURATION"));
        modelBuilder.Entity<LicenseHistory>(entity => entity.ToTable("COMPLIANCE_CONTROL_HISTORY"));
        modelBuilder.Entity<FormTypeCategory>(entity => entity.ToTable("FORM_TYPE_CATEGORY"));
        modelBuilder.Entity<GroupPolicy>(entity => entity.ToTable("GROUP_POLICY"));
        modelBuilder.Entity<ComplianceHistory>(entity => entity.ToTable("COMPLIANCE_HISTORY"));
        modelBuilder.Entity<Setting>(entity => entity.ToTable("SETTING"));
        modelBuilder.Entity<DrReady>(entity => entity.ToTable("DR_READY"));
        modelBuilder.Entity<ImpactActivity>(entity => entity.ToTable("IMPACT_ACTIVITY"));
        modelBuilder.Entity<Rto>(entity => entity.ToTable("RTO"));
        modelBuilder.Entity<DataLag>(entity => entity.ToTable("DATALAG"));
        modelBuilder.Entity<DashboardView>(entity => entity.ToTable("DASHBOARD_VIEW"));
        modelBuilder.Entity<DashboardViewLog>(entity => entity.ToTable("DASHBOARD_VIEW_LOG"));
        modelBuilder.Entity<DRReadyLog>(entity => entity.ToTable("DR_READY_LOG"));
        modelBuilder.Entity<HeatMapLog>(entity => entity.ToTable("HEATMAP_LOG"));
        modelBuilder.Entity<HeatMapStatus>(entity => entity.ToTable("HEATMAP_STATUS"));
        modelBuilder.Entity<InfraSummary>(entity => entity.ToTable("INFRA_SUMMARY"));
        modelBuilder.Entity<InfraObjectInfo>(entity => entity.ToTable("INFRAOBJECT_INFO"));
        modelBuilder.Entity<InfraOperationalStatus>(entity => entity.ToTable("INFRA_OPERATIONAL_STATUS"));
        modelBuilder.Entity<ImpactAvailability>(entity => entity.ToTable("IMPACT_AVAILABILITY"));
        modelBuilder.Entity<DRReadyStatus>(entity => entity.ToTable("DR_READY_STATUS"));
        modelBuilder.Entity<RiskMitigation>(entity => entity.ToTable("RISK_MITIGATION"));
        modelBuilder.Entity<BusinessServiceAvailability>(entity => entity.ToTable("BUSINESS_SERVICE_AVAILABILITY"));
        modelBuilder.Entity<BusinessServiceEvaluation>(entity => entity.ToTable("BUSINESS_SERVICE_EVALUATION"));
        modelBuilder.Entity<BusinessServiceHealthStatus>(entity => entity.ToTable("BUSINESS_SERVICE_HEALTH_STATUS"));
        modelBuilder.Entity<BusinessServiceHealthLog>(entity => entity.ToTable("BUSINESS_SERVICE_HEALTH_LOG"));
        modelBuilder.Entity<DashboardView>(entity => entity.ToTable("DASHBOARD_VIEW"));
        modelBuilder.Entity<RsyncOption>(entity => entity.ToTable("RSYNC_OPTION"));
        modelBuilder.Entity<RoboCopy>(entity => entity.ToTable("ROBOCOPY"));
        modelBuilder.Entity<DataSyncOptions>(entity => entity.ToTable("DATASYNC_OPTIONS"));
        modelBuilder.Entity<SiteLocation>(entity => entity.ToTable("SITE_LOCATION"));
        modelBuilder.Entity<IncidentManagementSummary>(entity => entity.ToTable("INCIDENT_MANAGEMENT_SUMMARY"));
        modelBuilder.Entity<IncidentManagement>(entity => entity.ToTable("INCIDENT_MANAGEMENT"));
        modelBuilder.Entity<ServerSubType>(entity => entity.ToTable("SERVER_SUBTYPE"));
        modelBuilder.Entity<AccessManager>(entity => entity.ToTable("ACCESS_MANAGER"));
        modelBuilder.Entity<BusinessFunction>(entity => entity.ToTable("BUSINESS_FUNCTION"));
        modelBuilder.Entity<BusinessService>(entity => entity.ToTable("BUSINESS_SERVICE"));
        modelBuilder.Entity<Company>(entity => entity.ToTable("COMPANY"));
        modelBuilder.Entity<Job>(entity => entity.ToTable("JOB"));
        modelBuilder.Entity<Node>(entity => entity.ToTable("NODE"));
        modelBuilder.Entity<Database>(entity => entity.ToTable("DATABASE"));
        modelBuilder.Entity<InfraObject>(entity => entity.ToTable("INFRAOBJECT"));
        modelBuilder.Entity<InfraObjectScheduler>(entity => entity.ToTable("INFRAOBJECT_SCHEDULER"));
        modelBuilder.Entity<Server>(entity => entity.ToTable("SERVER"));
        modelBuilder.Entity<Replication>(entity => entity.ToTable("REPLICATION"));
        modelBuilder.Entity<CredentialProfile>(entity => entity.ToTable("CREDENTIAL_PROFILE"));
        modelBuilder.Entity<ServerType>(entity => entity.ToTable("SERVER_TYPE"));
        modelBuilder.Entity<ComponentType>(entity => entity.ToTable("COMPONENT_TYPE"));
        modelBuilder.Entity<SingleSignOn>(entity => entity.ToTable("SINGLE_SIGN_ON"));
        modelBuilder.Entity<SiteType>(entity => entity.ToTable("SITE_TYPE"));
        modelBuilder.Entity<Site>(entity => entity.ToTable("SITE"));
        modelBuilder.Entity<InfraObjectScheduler>(entity => entity.ToTable("INFRAOBJECT_SCHEDULER"));
        modelBuilder.Entity<TeamMaster>(entity => entity.ToTable("TEAM_MASTER"));
        modelBuilder.Entity<TeamResource>(entity => entity.ToTable("TEAM_RESOURCES"));
        modelBuilder.Entity<User>(entity => entity.ToTable("USER"));
        modelBuilder.Entity<UserInfo>(entity => entity.ToTable("USER_INFO"));
        modelBuilder.Entity<UserInfraObject>(entity => entity.ToTable("USER_INFRAOBJECT"));
        modelBuilder.Entity<UserLogin>(entity => entity.ToTable("USER_LOGIN"));
        modelBuilder.Entity<UserActivity>(entity => entity.ToTable("USER_ACTIVITY"));
        modelBuilder.Entity<UserRole>(entity => entity.ToTable("USER_ROLE"));
        modelBuilder.Entity<UserCredential>(entity => entity.ToTable("USER_CREDENTIAL"));
        modelBuilder.Entity<RpoSlaDeviationReport>(entity => entity.ToTable("RPO_SLA_DEVIATION_REPORT"));
        modelBuilder.Entity<SmtpConfiguration>(entity => entity.ToTable("SMTP_CONFIGURATION"));
        modelBuilder.Entity<AlertReceiver>(entity => entity.ToTable("ALERT_RECEIVER"));
        modelBuilder.Entity<AlertNotification>(entity => entity.ToTable("ALERT_NOTIFICATION"));
        modelBuilder.Entity<AlertInformation>(entity => entity.ToTable("ALERT_INFORMATION"));
        modelBuilder.Entity<SmsConfiguration>(entity => entity.ToTable("SMS_CONFIGURATION"));
        modelBuilder.Entity<AlertMaster>(entity => entity.ToTable("ALERT_MASTER"));
        modelBuilder.Entity<Alert>(entity => entity.ToTable("ALERT"));
        modelBuilder.Entity<TableAccess>(entity => entity.ToTable("TABLE_ACCESS"));
        modelBuilder.Entity<DataSetColumns>(entity => entity.ToTable("DATASET_COLUMNS"));
        modelBuilder.Entity<Report>(entity => entity.ToTable("REPORT"));
        modelBuilder.Entity<ReportSchedule>(entity => entity.ToTable("REPORT_SCHEDULE"));
        modelBuilder.Entity<ReportScheduleExecution>(entity => entity.ToTable("REPORTSCHEDULE_EXECUTIONDETAILS"));
        modelBuilder.Entity<UserGroup>(entity => entity.ToTable("USER_GROUP"));
        modelBuilder.Entity<DataSet>(entity => entity.ToTable("DATASET"));
        modelBuilder.Entity<Workflow>(entity => entity.ToTable("WORKFLOW"));
        modelBuilder.Entity<WorkflowHistory>(entity => entity.ToTable("WORKFLOW_HISTORY"));
        modelBuilder.Entity<WorkflowInfraObject>(entity => entity.ToTable("WORKFLOW_INFRAOBJECT"));
        modelBuilder.Entity<FourEyeApprovers>(entity => entity.ToTable("FOUR_EYE_APPROVERS"));
        modelBuilder.Entity<DrCalenderActivity>(entity => entity.ToTable("DR_CALENDER_ACTIVITY"));
        modelBuilder.Entity<ReplicationMaster>(entity => entity.ToTable("REPLICATION_MASTER"));
        modelBuilder.Entity<InfraReplicationMapping>(entity => entity.ToTable("INFRA_REPLICATION_MAPPING"));
        modelBuilder.Entity<AboutCp>(entity => entity.ToTable("ABOUT_CP"));
        modelBuilder.Entity<GlobalSetting>(entity => entity.ToTable("GLOBAL_SETTING"));
        modelBuilder.Entity<IncidentManagement>(entity => entity.ToTable("INCIDENT_MANAGEMENT"));
        modelBuilder.Entity<EscalationMatrix>(entity => entity.ToTable("ESCALATIONMATRIX"));
        modelBuilder.Entity<EscalationMatrixLevel>(entity => entity.ToTable("ESCALATIONLEVELS"));
        modelBuilder.Entity<TableInformation>().HasNoKey();
        modelBuilder.Entity<ColumnInfo>().HasNoKey();
        modelBuilder.Entity<Template>(entity => entity.ToTable("TEMPLATE"));
        modelBuilder.Entity<TemplateHistory>(entity => entity.ToTable("TEMPLATE_HISTORY"));
        modelBuilder.Entity<WorkflowExecutionTemp>(entity => entity.ToTable("WORKFLOW_EXECUTION_TEMP"));
        modelBuilder.Entity<Workflow>(entity => entity.ToTable("WORKFLOW"));
        modelBuilder.Entity<ApprovalMatrix>(entity => entity.ToTable("APPROVAL_MATRIX"));
        modelBuilder.Entity<WorkflowInfraObject>(entity => entity.ToTable("WORKFLOW_INFRAOBJECT"));
        modelBuilder.Entity<WorkflowProfileInfo>(entity => entity.ToTable("WORKFLOW_PROFILE_INFO"));
        modelBuilder.Entity<WorkflowActionType>(entity => entity.ToTable("WORKFLOW_ACTIONTYPE"));
        modelBuilder.Entity<WorkflowProfile>(entity => entity.ToTable("WORKFLOW_PROFILE"));
        modelBuilder.Entity<WorkflowOperation>(entity => entity.ToTable("WORKFLOW_OPERATION"));
        modelBuilder.Entity<WorkflowOperationGroup>(entity => entity.ToTable("WORKFLOW_OPERATION_GROUP"));
        modelBuilder.Entity<WorkflowActionResult>(entity => entity.ToTable("WORKFLOW_ACTION_RESULT"));
        modelBuilder.Entity<NodeWorkflowExecution>(entity => entity.ToTable("NODE_WORKFLOW_EXECUTION"));
        modelBuilder.Entity<WorkflowExecutionEventLog>(entity => entity.ToTable("WORKFLOW_EXECUTION_EVENT_LOG"));
        modelBuilder.Entity<WorkflowPermission>(entity => entity.ToTable("WORKFLOW_PERMISSION"));
        modelBuilder.Entity<InfraObjectSchedulerWorkflowDetail>(entity => entity.ToTable("INFRAOBJECT_SCHEDULER_WORKFLOW_DETAILS"));
        modelBuilder.Entity<WorkflowPrediction>(entity => entity.ToTable("WORKFLOW_PREDICTION"));
        modelBuilder.Entity<WorkflowActionFieldMaster>(entity => entity.ToTable("WORKFLOW_ACTIONFIELD_MASTER"));
        modelBuilder.Entity<MSSQLMonitorStatus>(entity => entity.ToTable("MSSQL_MONITOR_STATUS"));
        modelBuilder.Entity<MYSQLMonitorStatus>(entity => entity.ToTable("MYSQL_MONITOR_STATUS"));
        modelBuilder.Entity<OracleMonitorStatus>(entity => entity.ToTable("ORACLE_MONITOR_STATUS"));
        modelBuilder.Entity<OracleRACMonitorStatus>(entity => entity.ToTable("ORACLERAC_MONITOR_STATUS"));
        modelBuilder.Entity<PostgresMonitorStatus>(entity => entity.ToTable("POSTGRES_MONITOR_STATUS"));
        modelBuilder.Entity<MYSQLMonitorLogs>(entity => entity.ToTable("MYSQL_MONITOR_LOGS"));
        modelBuilder.Entity<MSSQLMonitorLogs>(entity => entity.ToTable("MSSQL_MONITOR_LOGS"));
        modelBuilder.Entity<OracleMonitorLogs>(entity => entity.ToTable("ORACLE_MONITOR_LOGS"));
        modelBuilder.Entity<PostgresMonitorLogs>(entity => entity.ToTable("POSTGRES_MONITOR_LOGS"));
        modelBuilder.Entity<OracleRACMonitorLogs>(entity => entity.ToTable("ORACLERAC_MONITOR_LOGS"));
        modelBuilder.Entity<MSSQLAlwaysOnMonitorLogs>(entity => entity.ToTable("MSSQL_ALWAYSON_MONITOR_LOGS"));
        modelBuilder.Entity<MSSQLAlwaysOnMonitorStatus>(entity => entity.ToTable("MSSQL_ALWAYSON_MONITOR_STATUS"));
        modelBuilder.Entity<StateMonitorLog>(entity => entity.ToTable("STATE_MONITOR_LOG"));
        modelBuilder.Entity<StateMonitorStatus>(entity => entity.ToTable("STATE_MONITOR_STATUS"));
        modelBuilder.Entity<MonitorService>(entity => entity.ToTable("MONITORSERVICE"));
        modelBuilder.Entity<Db2HaDrMonitorStatus>(entity => entity.ToTable("DB2HADR_MONITOR_STATUS"));
        modelBuilder.Entity<DB2HADRMonitorLog>(entity => entity.ToTable("DB2HADR_MONITOR_LOGS"));
        modelBuilder.Entity<MongoDbMonitorStatus>(entity => entity.ToTable("MONGODB_MONITOR_STATUS"));
        modelBuilder.Entity<MongoDBMonitorLog>(entity => entity.ToTable("MONGODB_MONITOR_LOGS"));
        modelBuilder.Entity<MSSQLDBMirroringLogs>(entity => entity.ToTable("MSSQLDBMIRORING_MONITOR_LOGS"));
        modelBuilder.Entity<MSSQLDBMirroringStatus>(entity => entity.ToTable("MSSQLDBMIRORING_MONITOR_STATUS"));
        //modelBuilder.Entity<ApprovalMatrix>(entity => entity.ToTable("APPROVAL_MATRIX_NEW"));
        modelBuilder.Entity<SVCGMMonitorLog>(entity => entity.ToTable("SVC_GLOBALMIRROR_MONITOR_LOGS"));
        modelBuilder.Entity<SVCGMMonitorStatus>(entity => entity.ToTable("SVC_GLOBALMIRROR_MONITOR_STATUS"));
        modelBuilder.Entity<MsSqlNativeLogShippingMonitorStatus>(entity => entity.ToTable("MSSQL_NLS_MONITOR_STATUS"));
        modelBuilder.Entity<MssqlNativeLogShippingMonitorLog>(entity => entity.ToTable("MSSQL_NLS_MONITOR_LOGS"));
        modelBuilder.Entity<Incident>(entity => entity.ToTable("INCIDENT"));
        modelBuilder.Entity<IncidentDaily>(entity => entity.ToTable("INCIDENT_DAILY"));
        modelBuilder.Entity<IncidentLogs>(entity => entity.ToTable("INCIDENT_LOGS"));
        modelBuilder.Entity<RoboCopyJob>(entity => entity.ToTable("ROBOCOPY_JOB"));
        modelBuilder.Entity<DataSyncJob>(entity => entity.ToTable("DATASYNC_JOB"));
        modelBuilder.Entity<RsyncJob>(entity => entity.ToTable("RSYNC_JOB"));
        modelBuilder.Entity<ReplicationJob>(entity => entity.ToTable("REPLICATION_JOB"));
        modelBuilder.Entity<RoboCopyMonitorLogs>(entity => entity.ToTable("ROBOCOPY_MONITOR_LOGS"));
        modelBuilder.Entity<RsyncMonitorLog>(entity => entity.ToTable("RSYNC_MONITOR_LOGS"));
        modelBuilder.Entity<SRMMonitorLog>(entity => entity.ToTable("SRM_MONITOR_LOGS"));
        modelBuilder.Entity<AzureStorageAccountMonitorlogs>(entity => entity.ToTable("AZURESTORAGE_ACCOUNT_MONITOR_LOGS"));
        modelBuilder.Entity<InfraObjectSchedulerLogs>(entity => entity.ToTable("INFRAOBJECT_SCHEDULER_LOGS"));
        modelBuilder.Entity<RpForVmMonitorStatus>(entity => entity.ToTable("RPFORVM_MONITOR_STATUS"));
        modelBuilder.Entity<RpForVmMonitorLogs>(entity => entity.ToTable("RPFORVM_MONITOR_LOGS"));
        modelBuilder.Entity<ComponentSaveAll>(entity => entity.ToTable("COMPONENT_SAVEALL"));
        modelBuilder.Entity<RpForVmCgEnableDisableStatus>(entity => entity.ToTable("RPFORVM_CGENABLE_DISABLE_STATUS"));
        modelBuilder.Entity<ServerLog>(entity => entity.ToTable("SERVER_LOGS"));
        modelBuilder.Entity<RpForVmCGMonitorLogs>(entity => entity.ToTable("RPFORVM_CG_MONITOR_LOGS"));
        modelBuilder.Entity<RpForVmCGMonitorStatus>(entity => entity.ToTable("RPFORVM_CG_MONITOR_STATUS"));
        modelBuilder.Entity<FastCopyMonitor>(entity => entity.ToTable("FAST_COPY_MONITOR"));
        modelBuilder.Entity<ActiveDirectoryMonitorLog>(entity => entity.ToTable("ACTIVEDIRECTORY_MONITOR_LOGS"));
        modelBuilder.Entity<SchedulerWorkflowActionResults>(entity => entity.ToTable("SCHEDULER_WORKFLOW_ACTIONS_RESULT"));
        modelBuilder.Entity<DataSyncMonitorLog>(entity => entity.ToTable("DATASYNC_MONITOR_LOGS"));
        modelBuilder.Entity<FastCopyMonitorLog>(entity => entity.ToTable("FAST_COPY_MONITOR_LOGS"));
        modelBuilder.Entity<ZertoVpgMonitorLog>(entity => entity.ToTable("ZERTO_VPG_MONITOR_LOGS"));
        modelBuilder.Entity<SybaseRSHADRMonitorLog>(entity => entity.ToTable("SYBASE_RS_HADR_MONITOR_LOGS"));
        modelBuilder.Entity<ResiliencyReadyWorkflowScheduleLog>(entity => entity.ToTable("RESILIENCY_READY_WORKFLOW_SCHEDULE_LOGS"));
        modelBuilder.Entity<MssqlAlwaysOnAvailabilityGroupMonitorLog>(entity => entity.ToTable("MSSQL_ALWAYSON_AVAILABILITY_GROUP_MONITOR_LOGS"));
        #region Views
        modelBuilder.Entity<ServerView>().ToView("SERVER_VIEW").HasNoKey();
        modelBuilder.Entity<DatabaseView>().ToView("DATABASE_VIEW").HasNoKey();
        modelBuilder.Entity<ReplicationView>().ToView("REPLICATION_VIEW").HasNoKey();
        modelBuilder.Entity<WorkflowCategoryView>().ToView("WORKFLOW_CATEGORYVIEW").HasNoKey();
        modelBuilder.Entity<WorkflowView>().ToView("WORKFLOW_VIEW").HasNoKey();
        modelBuilder.Entity<UserView>().ToView("USER_VIEW").HasNoKey();
        modelBuilder.Entity<InfraObjectView>().ToView("INFRAOBJECT_VIEW").HasNoKey();
        modelBuilder.Entity<WorkflowProfileInfoView>().ToView("WORKFLOW_PROFILE_INFO_VIEW").HasNoKey();
        modelBuilder.Entity<InfraDashboardView>().ToView("INFRA_DASHBOARD_VIEW").HasNoKey();
        modelBuilder.Entity<OneViewEntitiesEventView>().ToView("ONEVIEW_ENTITIES_EVENT").HasNoKey();
        modelBuilder.Entity<OneViewRiskMitigationFailedDrillView>().ToView("ONEVIEW_RISKMITIGATION_FAILED_DRILL_VIEW").HasNoKey();
        modelBuilder.Entity<OneViewRiskMitigationCyberSecurityView>().ToView("ONEVIEW_RISK_MITIGATION_CYBERSECURITYVIEW").HasNoKey();
        #endregion




    }
}
   