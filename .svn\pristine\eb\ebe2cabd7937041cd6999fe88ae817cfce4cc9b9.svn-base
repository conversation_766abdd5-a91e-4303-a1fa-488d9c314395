﻿using ContinuityPatrol.Application.Features.Report.Queries.GetDrDrillReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRTOReport;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using DevExpress.XtraCharts;
using Newtonsoft.Json;
using System.Data;
using System.Drawing;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{

    [SupportedOSPlatform("windows")]
    public partial class RTOReport : DevExpress.XtraReports.UI.XtraReport
    {
        public static List<WorkflowActionResultDrDrillReportVm> reportTablevalue = new List<WorkflowActionResultDrDrillReportVm>();
        public static List<RtoReportVm> RtoReportList = new List<RtoReportVm>();
        private readonly ILogger<PreBuildReportController> _logger;
        public  RTOReports RtoReports= new RTOReports();
        public RTOReport(string data)
        {
             var rtoxls= new RTOXlsReport(data);
            try
            {
                RtoReportList.Clear();
                RtoReports= JsonConvert.DeserializeObject<RTOReports>(data);
                _logger = PreBuildReportController._logger;
                RtoReportList.AddRange(RtoReports.RtoReportVm);

                InitializeComponent();
                ClientCompanyLogo();
                // this.DisplayName = "RTOReport_" + DateTime.Now.ToString("ddMMyyyy" + "_" + "hhmmsstt");
                foreach (var ReportTable in RtoReportList)
                {
                    if (ReportTable != null)
                    {
                        xrLabel64.Text = string.IsNullOrEmpty(ReportTable.WorkflowName?.ToString()) ? "-" : ReportTable.WorkflowName.ToString();
                        xrLabel3.Text = string.IsNullOrEmpty(ReportTable.UserName?.ToString()) ? "-" : ReportTable.UserName?.ToString();
                        xrLabel5.Text = string.IsNullOrEmpty(ReportTable.InfraObjectName?.ToString()) ? "-" : ReportTable.InfraObjectName.ToString();
                        xrLabel15.Text = string.IsNullOrEmpty(ReportTable.PRServerName?.ToString()) ? "-" : ReportTable.PRServerName.ToString();
                        xrLabel17.Text = string.IsNullOrEmpty(ReportTable.ConfiguredRTO?.ToString()) ? "-" : ReportTable.ConfiguredRTO.ToString();
                        xrLabel7.Text = ReportTable.ProductionIpAddress != null ? string.Join(", ", ReportTable.ProductionIpAddress)  : "-";
                        xrLabel9.Text = ReportTable.PRDbSid != null ? string.Join(", ", ReportTable.PRDbSid) : "-";
                        xrLabel12.Text = ReportTable.ProductionHostName != null ? string.Join(", ", ReportTable.ProductionHostName) : "-";

                        xrLabel20.Text = string.IsNullOrEmpty(ReportTable.WorkflowActionType?.ToString()) ? "-" : ReportTable.WorkflowActionType.ToString();
                        xrLabel47.Text = string.IsNullOrEmpty(ReportTable.TotalInfraComponentCount.ToString()) ? "-" : ReportTable.TotalInfraComponentCount.ToString();
                        xrLabel48.Text = string.IsNullOrEmpty(ReportTable.TotalProfilesExecutedCount.ToString()) ? "-" : ReportTable.TotalProfilesExecutedCount.ToString();
                        xrLabel52.Text = string.IsNullOrEmpty(ReportTable.TotalWorkflowExecutedCount.ToString()) ? "-" : ReportTable.TotalWorkflowExecutedCount.ToString();
                        xrLabel53.Text = string.IsNullOrEmpty(ReportTable.LastDrillStatus?.ToString()) ? "-" : ReportTable.LastDrillStatus.ToString();
                        xrLabel24.Text = string.IsNullOrEmpty(ReportTable.DRServerName?.ToString()) ? "-" : ReportTable.DRServerName.ToString();
                        xrLabel26.Text = ReportTable.DrIpAddress != null ? string.Join(", ", ReportTable.DrIpAddress) : "-";
                        xrLabel28.Text = ReportTable.DrHostName != null ? string.Join(", ", ReportTable.DrHostName) : "-";
                        xrLabel30.Text = ReportTable.DRDbSid != null ? string.Join(", ", ReportTable.DRDbSid) : "-";

                        string timeSpanString = ReportTable.ActualRTO.ToString();
                        xrLabel32.Text = string.IsNullOrEmpty(timeSpanString?.ToString()) ? "-" : timeSpanString.ToString();
                        this.xrLabel53.ForeColor = System.Drawing.Color.FromArgb(85, 183, 62);
                        if (xrLabel53.Text.Contains("Error") || xrLabel53.Text.Contains("Abort") || xrLabel53.Text.Contains("Failed"))
                        {
                            this.xrLabel53.ForeColor = System.Drawing.Color.FromArgb(254, 34, 35);
                        }
                        if (xrLabel53.Text.Contains("-"))
                        {
                            this.xrLabel53.ForeColor = System.Drawing.Color.FromArgb(10, 10, 10);
                        }
                    }
                    if (ReportTable.WorkflowActionResultRtoReportVms.Count > 0)
                    {
                        reportTablevalue = ReportTable.WorkflowActionResultRtoReportVms;
                        xrLabel62.Visible = true;
                        xrPictureBox41.Visible = true;
                        xrLabel45.Visible = true;
                    }
                    else
                    {
                        GroupHeader1.Visible = false;
                        Detail.Visible = false;
                    }
                    xrLabel40.Text = ReportTable.ProfileName.ToString();
                    this.DisplayName = ReportTable.ProfileName + "_RTOReport_" + DateTime.Now.ToString("ddMMyyyy" + "_" + "hhmmsstt");

                }
                var startDate = RtoReports.ActiveStartDate.ToString();
                xrLabel43.Text = startDate.ToDateTime().ToString("dd-MM-yyyy");
                var endDate = RtoReports.ActiveEndDate.ToString();
                xrLabel44.Text = endDate.ToDateTime().ToString("dd-MM-yyyy");
                this.DataSource = reportTablevalue;
                tableCell8.BeforePrint += tableCell_SerialNumber_BeforePrint;
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RTO Report. The error message : " + ex.Message); throw; }
        }
        private int serialNumber = 1;
        private void xrChart1_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var RtoReport = RtoReportList;
                Int64 SuccessCount = 0;
                Int64 FailuerCount = 0;
                Int64 SkippedCount = 0;
                Int64 bypassCount = 0;
                Int64 NodataFound = 0;
                foreach (var value in RtoReport)
                {
                    int LastActionCount = value.WorkflowActionResultRtoReportVms.Count;
                    foreach (var counts in value.WorkflowActionResultRtoReportVms)
                    {

                        if (counts.Status.Contains("Success"))
                        {
                            SuccessCount++;
                        }
                        if (counts.Status.Contains("Error") || counts.Status.Contains("Failuer") || counts.Status.Contains("Aborted"))
                        {
                            FailuerCount++;
                        }
                        if (counts.Status == "Skipped" || counts.Status == "Skip")
                        {
                            SkippedCount++;
                        }
                        if (counts.Status.ToLower().Contains("bypass"))
                        {
                            bypassCount = bypassCount + 1;
                        }
                    }
                    if (LastActionCount > 0)
                    {
                        if (value.WorkflowActionResultRtoReportVms[LastActionCount - 1].Status.Contains("Error") || value.WorkflowActionResultRtoReportVms[LastActionCount - 1].Status.Contains("Failuer") || value.WorkflowActionResultRtoReportVms[LastActionCount - 1].Status.Contains("Aborted"))
                        {
                            xrPictureBox40.Visible = true;
                            xrLabel60.Visible = true;
                            xrLabel61.Visible = true;
                            xrLabel61.Text = string.IsNullOrEmpty(value.WorkflowActionResultRtoReportVms[LastActionCount - 1].Message?.ToString()) ? "-" : value.WorkflowActionResultRtoReportVms[LastActionCount - 1].Message.ToString();

                            if (xrLabel61.Text != "-")
                            {

                                if (xrLabel61.Text.Length > 100)
                                {
                                    xrLabel61.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
                                    xrLabel61.Font = new DevExpress.Drawing.DXFont(xrLabel61.Font.Name, 7.5f, xrLabel61.Font.Style);
                                }
                                else
                                {
                                    xrLabel61.Font = new DevExpress.Drawing.DXFont(xrLabel61.Font.Name, 8f, xrLabel61.Font.Style);
                                }

                            }

                        }
                    }
                }

                lblSuccess.Text = SuccessCount.ToString();
                lblFailure.Text = FailuerCount.ToString();
                lblSkipped.Text = SkippedCount.ToString();
                labelbyPass.Text = bypassCount.ToString();

                if (SuccessCount == 0 && FailuerCount == 0 && SkippedCount == 0)
                {
                    ActionCount.Text = "00";
                    Series series1 = new Series("Series1", ViewType.Doughnut);
                    xrChart1.Series.Add(series1);
                    NodataFound = 1;
                    series1.DataSource = CreateChartData(SuccessCount, FailuerCount, SkippedCount, bypassCount, NodataFound);
                    DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                    doughnutSeriesView.MinAllowedSizePercentage = 70D;
                    // doughnutSeriesView.HoleRadiusPercent = 70;
                    series1.View = doughnutSeriesView;
                    series1.ArgumentScaleType = ScaleType.Auto;
                    series1.ArgumentDataMember = "Argument";
                    series1.ValueScaleType = ScaleType.Numerical;
                    series1.ValueDataMembers.AddRange(new string[] { "Value" });
                    // series1.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;
                    series1.Label.TextPattern = "{A}";
                    series1.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                    xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;

                }
                else
                {
                    var TotalCount = SuccessCount + FailuerCount + SkippedCount;
                    ActionCount.Text = TotalCount.ToString();
                    Series series = new Series("Series1", ViewType.Doughnut);
                    xrChart1.Series.Add(series);
                    series.DataSource = CreateChartData(SuccessCount, FailuerCount, SkippedCount, bypassCount, NodataFound);
                    DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                    doughnutSeriesView.MinAllowedSizePercentage = 70D;
                    series.View = doughnutSeriesView;
                    series.ArgumentScaleType = ScaleType.Auto;
                    series.ArgumentDataMember = "Argument";
                    series.ValueScaleType = ScaleType.Numerical;
                    series.ValueDataMembers.AddRange(new string[] { "Value" });
                    series.Label.TextPattern = "{A}\n{V}";
                    ((DoughnutSeriesLabel)series.Label).ResolveOverlappingMode = ResolveOverlappingMode.Default;
                    series.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                    xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
                }
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RTO Report's chart data. The error message : " + ex.Message); throw; }
        }
        private DataTable CreateChartData(Int64 SuccessCount, Int64 FailuerCount, Int64 SkippedCount, Int64 bypassCount, Int64 NodataFound)
        {
            DataTable table = new DataTable("Table1");
            table.Columns.Add("Argument", typeof(string));
            table.Columns.Add("Value", typeof(Int64));
            Random rnd = new Random();
            table.Rows.Add("Success", SuccessCount);
            table.Rows.Add("Skipped", SkippedCount);
            table.Rows.Add("Failure", FailuerCount);
            table.Rows.Add("ByPassed", bypassCount);
            table.Rows.Add("NodataFound", NodataFound);
            return table;
        }
        private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
        {
            XRTableCell cell = (XRTableCell)sender;

            cell.Text = serialNumber.ToString();
            serialNumber++;
        }

        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                _username.Text = "Report Generated By: " + RtoReports.ReportGeneratedBy.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RTO Report's User name. The error message : " + ex.Message); throw; }
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RTO Report's CP Version. The error message : " + ex.Message); throw; }
        }
        private void xrPictureBox5_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var RtoReport = RtoReportList;
                string ErrorText = "";
                foreach (var value in RtoReport)
                {
                    int LastActionCount = value.WorkflowActionResultRtoReportVms.Count;
                    if (LastActionCount > 0)
                    {
                        if (value.WorkflowActionResultRtoReportVms[LastActionCount - 1].Status.Contains("Error") || value.WorkflowActionResultRtoReportVms[LastActionCount - 1].Status.Contains("Failuer") || value.WorkflowActionResultRtoReportVms[LastActionCount - 1].Status.Contains("Aborted"))
                        {
                            ErrorText = string.IsNullOrEmpty(value.WorkflowActionResultRtoReportVms[LastActionCount - 1].Message?.ToString()) ? "-" : value.WorkflowActionResultRtoReportVms[LastActionCount - 1].Message.ToString();
                        }
                    }
                }
                if (ErrorText.Length <= 250)
                {
                    xrPictureBox5.SizeF = new SizeF(1079F, 388.542F);
                }
                else if (ErrorText.Length <= 600)
                {
                    xrPictureBox5.SizeF = new SizeF(1079F, 470.45F);
                }
                else if (ErrorText.Length <= 800)
                {
                    xrPictureBox5.SizeF = new SizeF(1079F, 505.45F);
                }
                else
                {
                    xrPictureBox5.SizeF = new SizeF(1079F, 515.45F);
                }
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RTO Report's Status image. The error message : " + ex.Message); throw; }
        }
        private void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "-" : PreBuildReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "-")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the customer logo in RTO Report" + ex.Message.ToString());
            }
        }
    }
}
