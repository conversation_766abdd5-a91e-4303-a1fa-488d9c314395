﻿using AutoFixture;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Create;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Update;
using ContinuityPatrol.Application.Features.ComponentType.Events.PaginatedView;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;
using FluentValidation.Results;
using Microsoft.Extensions.Primitives;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class ComponentTypeControllerShould
    {
        private readonly Mock<ILogger<ComponentTypeController>> _loggerMock =new();
        private readonly Mock<IMapper> _mapperMock = new();
        private readonly Mock<IDataProvider> _dataProviderMock = new();
        private readonly Mock<IPublisher> _publisherMock = new();
        private ComponentTypeController _controller;
        private readonly Fixture _fixture;

        public ComponentTypeControllerShould()
        {
            _fixture = new Fixture();
            Initialize();
        }
        internal void Initialize()
        {
            _controller = new ComponentTypeController(
               _loggerMock.Object,
               _publisherMock.Object,
               _mapperMock.Object,
               _dataProviderMock.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_Returns_View_With_ComponentTypes()
        {

            _dataProviderMock.Setup(m => m.ComponentType.GetComponentTypeList()).ReturnsAsync(It.IsAny<List<ComponentTypeListVm>>);
            var componentTypes = new List<ComponentTypeViewModel>();
            
            
            var result = await _controller.List() as ViewResult;

            
            Assert.NotNull(result);
            
        }

        [Fact]
        public async Task ServerList_Returns_Filtered_ComponentTypes()
        {
            
            string serverName = "Server";
            _dataProviderMock.Setup(m => m.ComponentType.GetComponentTypeList()).ReturnsAsync(It.IsAny<List<ComponentTypeListVm>>);

            
            var result = await _controller.ServerList(serverName) as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":false", json);
            
        }

        [Fact]
        public async Task DatabaseList_Returns_Database_ComponentTypes()
        {
            _dataProviderMock.Setup(m => m.ComponentType.GetComponentTypeList()).ReturnsAsync(It.IsAny<List<ComponentTypeListVm>>);
            var result = await _controller.DatabaseList() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task ReplicationList_Returns_Replication_ComponentTypes()
        {
            _dataProviderMock.Setup(m => m.ComponentType.GetComponentTypeList()).ReturnsAsync(It.IsAny<List<ComponentTypeListVm>>);
            var result = await _controller.ReplicationList() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task SingleSignOnList_Returns_SingleSignOn_ComponentTypes()
        {


            _dataProviderMock.Setup(m => m.ComponentType.GetComponentTypeList()).ReturnsAsync(It.IsAny<List<ComponentTypeListVm>>);
            var result = await _controller.SingleSignOnList() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task ServerList_Returns_ServerList_Type_ComponentTypes()
        {

            _dataProviderMock.Setup(m => m.ComponentType.GetComponentTypeList()).ReturnsAsync(It.IsAny<List<ComponentTypeListVm>>);
            var result = await _controller.ServerList() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":false", json);
        }
        
        [Fact]
        public async Task CreateOrUpdate_Creates_New_ComponentType()
        {
            // Arrange
            var viewModel = _fixture.Create<ComponentTypeViewModel>();
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "" }
            });
            _controller.Request.Form = formCollection;
            var command = new CreateComponentTypeCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mapperMock.Setup(m => m.Map<CreateComponentTypeCommand>(viewModel))
                .Returns(command);
            _dataProviderMock.Setup(dp => dp.ComponentType.CreateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task CreateOrUpdate_Updates_Existing_ComponentType()
        {
            // Arrange
            var viewModel = _fixture.Create<ComponentTypeViewModel>();
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "22" }
            });
            _controller.Request.Form = formCollection;
            var command = new UpdateComponentTypeCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mapperMock.Setup(m => m.Map<UpdateComponentTypeCommand>(viewModel))
                .Returns(command);
            _dataProviderMock.Setup(dp => dp.ComponentType.UpdateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

       
        [Fact]
        public async Task CreateOrUpdate_Handles_General_Exception()
        {
            // Arrange
            var viewModel = _fixture.Create<ComponentTypeViewModel>();
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "" }
            });
            _controller.Request.Form = formCollection;
            var command = new CreateComponentTypeCommand();

            _mapperMock.Setup(m => m.Map<CreateComponentTypeCommand>(viewModel))
                .Returns(command);
            _dataProviderMock.Setup(dp => dp.ComponentType.CreateAsync(command))
                .ThrowsAsync(new Exception("General error"));

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task Delete_Calls_Delete_Method()
        {
            // Arrange
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };

            _dataProviderMock.Setup(dp => dp.ComponentType.DeleteAsync(id))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task GetPagination_Returns_Paginated_List()
        {
            // Arrange
            var query = _fixture.Create<GetComponentTypePaginatedListQuery>();
            var paginatedResult = _fixture.Create<PaginatedResult<ComponentTypeListVm>>();
            _dataProviderMock.Setup(m => m.ComponentType.GetPaginatedComponentTypes(query))
                             .ReturnsAsync(paginatedResult);

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task ComponentTypeNameExist_Returns_True_When_Exists()
        {
            // Arrange
            var name = "ExistingName";
            var id = "1";

            _dataProviderMock.Setup(dp => dp.ComponentType.IsComponentTypeExist(name, id))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.ComponentTypeNameExist(name, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task ComponentTypeNameExist_Returns_False_When_Not_Exists()
        {
            // Arrange
            var name = "NonExistingName";
            var id = "1";

            _dataProviderMock.Setup(dp => dp.ComponentType.IsComponentTypeExist(name, id))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.ComponentTypeNameExist(name, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task List_ThrowsException_PropagatesException()
        {
            // Arrange
            _publisherMock.Setup(p => p.Publish(It.IsAny<ComponentTypePaginatedEvent>(), It.IsAny<CancellationToken>()))
                         .ThrowsAsync(new Exception("Test exception"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _controller.List());
        }

        [Fact]
        public async Task GetComponentTypeListByName_ReturnsJsonResult()
        {
            // Arrange
            var name = "TestName";
            var componentTypeList = _fixture.Create<List<ComponentTypeModel>>();
            _dataProviderMock.Setup(dp => dp.ComponentType.GetComponentTypeListByName(name))
                             .ReturnsAsync(componentTypeList);

            // Act
            var result = await _controller.GetComponentTypeListByName(name);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task GetComponentTypeListByName_ThrowsException_ReturnsJsonException()
        {
            // Arrange
            var name = "TestName";
            _dataProviderMock.Setup(dp => dp.ComponentType.GetComponentTypeListByName(name))
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetComponentTypeListByName(name);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task ServerList_WithName_ThrowsException_ReturnsJsonException()
        {
            // Arrange
            var name = "TestName";
            _dataProviderMock.Setup(dp => dp.ComponentType.GetComponentTypeList())
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.ServerList(name);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task DatabaseList_ThrowsException_ReturnsJsonException()
        {
            // Arrange
            _dataProviderMock.Setup(dp => dp.ComponentType.GetComponentTypeList())
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DatabaseList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task ReplicationList_ThrowsException_ReturnsJsonException()
        {
            // Arrange
            _dataProviderMock.Setup(dp => dp.ComponentType.GetComponentTypeList())
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.ReplicationList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task SingleSignOnList_ThrowsException_ReturnsJsonException()
        {
            // Arrange
            _dataProviderMock.Setup(dp => dp.ComponentType.GetComponentTypeList())
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.SingleSignOnList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task ServerList_NoParameter_ThrowsException_ReturnsJsonException()
        {
            // Arrange
            _dataProviderMock.Setup(dp => dp.ComponentType.GetComponentTypeList())
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.ServerList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task CreateOrUpdate_ValidationException_ReturnsJsonException()
        {
            // Arrange
            var viewModel = _fixture.Create<ComponentTypeViewModel>();
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "" }
            });
            _controller.Request.Form = formCollection;
            var command = new CreateComponentTypeCommand();
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Property", "Validation error"));
            var validationException = new ValidationException(validationResult);

            _mapperMock.Setup(m => m.Map<CreateComponentTypeCommand>(viewModel))
                .Returns(command);
            _dataProviderMock.Setup(dp => dp.ComponentType.CreateAsync(command))
                .ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task CreateOrUpdate_PropertiesNull_ReturnsJsonResult()
        {
            // Arrange
            var viewModel = _fixture.Create<ComponentTypeViewModel>();
            viewModel.Properties = null; // Null properties
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "" }
            });
            _controller.Request.Form = formCollection;
            var command = new CreateComponentTypeCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mapperMock.Setup(m => m.Map<CreateComponentTypeCommand>(viewModel))
                .Returns(command);
            _dataProviderMock.Setup(dp => dp.ComponentType.CreateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task Delete_ThrowsException_ReturnsJsonException()
        {
            // Arrange
            var id = "1";
            _dataProviderMock.Setup(dp => dp.ComponentType.DeleteAsync(id))
                             .ThrowsAsync(new Exception("Delete error"));

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task GetPagination_ThrowsException_ReturnsJsonException()
        {
            // Arrange
            var query = _fixture.Create<GetComponentTypePaginatedListQuery>();
            _dataProviderMock.Setup(dp => dp.ComponentType.GetPaginatedComponentTypes(query))
                             .ThrowsAsync(new Exception("Pagination error"));

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task ComponentTypeNameExist_ThrowsException_ReturnsFalse()
        {
            // Arrange
            var name = "TestName";
            var id = "1";
            _dataProviderMock.Setup(dp => dp.ComponentType.IsComponentTypeExist(name, id))
                             .ThrowsAsync(new Exception("Name check error"));

            // Act
            var result = await _controller.ComponentTypeNameExist(name, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void Controller_HasAreaAttribute()
        {
            // Arrange
            var controllerType = typeof(ComponentTypeController);

            // Act
            var areaAttribute = controllerType.GetCustomAttributes(typeof(AreaAttribute), false).FirstOrDefault() as AreaAttribute;

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void GetPagination_HasHttpGetAttribute()
        {
            // Arrange
            var methodInfo = typeof(ComponentTypeController).GetMethod("GetPagination");

            // Act
            var httpGetAttribute = methodInfo.GetCustomAttributes(typeof(HttpGetAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void ComponentTypeNameExist_HasHttpGetAttribute()
        {
            // Arrange
            var methodInfo = typeof(ComponentTypeController).GetMethod("ComponentTypeNameExist");

            // Act
            var httpGetAttribute = methodInfo.GetCustomAttributes(typeof(HttpGetAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void CreateOrUpdate_HasHttpPostAttribute()
        {
            // Arrange
            var methodInfo = typeof(ComponentTypeController).GetMethod("CreateOrUpdate");

            // Act
            var httpPostAttribute = methodInfo.GetCustomAttributes(typeof(HttpPostAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpPostAttribute);
        }

        [Fact]
        public void CreateOrUpdate_HasValidateAntiForgeryTokenAttribute()
        {
            // Arrange
            var methodInfo = typeof(ComponentTypeController).GetMethod("CreateOrUpdate");

            // Act
            var antiForgeryAttribute = methodInfo.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiForgeryAttribute);
        }

        [Fact]
        public void CreateOrUpdate_HasAntiXssAttribute()
        {
            // Arrange
            var methodInfo = typeof(ComponentTypeController).GetMethod("CreateOrUpdate");

            // Act
            var antiXssAttribute = methodInfo.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public async Task ServerList_WithName_FiltersCorrectly()
        {
            // Arrange
            var name = "Server";
            var componentTypeList = new List<ComponentTypeListVm>
            {
                new ComponentTypeListVm { FormTypeName = "Server" },
                new ComponentTypeListVm { FormTypeName = "Database" },
                new ComponentTypeListVm { FormTypeName = "Server" }
            };
            _dataProviderMock.Setup(dp => dp.ComponentType.GetComponentTypeList())
                             .ReturnsAsync(componentTypeList);

            // Act
            var result = await _controller.ServerList(name);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task ServerList_WithNullName_ReturnsAllItems()
        {
            // Arrange
            string name = null;
            var componentTypeList = _fixture.Create<List<ComponentTypeListVm>>();
            _dataProviderMock.Setup(dp => dp.ComponentType.GetComponentTypeList())
                             .ReturnsAsync(componentTypeList);

            // Act
            var result = await _controller.ServerList(name);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task DatabaseList_WithValidData_ReturnsFilteredDatabaseTypes()
        {
            // Arrange
            var componentTypeList = new List<ComponentTypeListVm>
            {
                new ComponentTypeListVm { FormTypeName = "Database" },
                new ComponentTypeListVm { FormTypeName = "Server" },
                new ComponentTypeListVm { FormTypeName = "DATABASE" }, // Test case insensitive
                new ComponentTypeListVm { FormTypeName = "Replication" }
            };
            _dataProviderMock.Setup(dp => dp.ComponentType.GetComponentTypeList())
                             .ReturnsAsync(componentTypeList);

            // Act
            var result = await _controller.DatabaseList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);

            // Verify the success response structure
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task ReplicationList_WithValidData_ReturnsFilteredReplicationTypes()
        {
            // Arrange
            var componentTypeList = new List<ComponentTypeListVm>
            {
                new ComponentTypeListVm { FormTypeName = "Replication" },
                new ComponentTypeListVm { FormTypeName = "Database" },
                new ComponentTypeListVm { FormTypeName = "REPLICATION" }, // Test case insensitive
                new ComponentTypeListVm { FormTypeName = "Server" }
            };
            _dataProviderMock.Setup(dp => dp.ComponentType.GetComponentTypeList())
                             .ReturnsAsync(componentTypeList);

            // Act
            var result = await _controller.ReplicationList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);

            // Verify the success response structure
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task SingleSignOnList_WithValidData_ReturnsFilteredSingleSignOnTypes()
        {
            // Arrange
            var componentTypeList = new List<ComponentTypeListVm>
            {
                new ComponentTypeListVm { FormTypeName = "Single SignOn" },
                new ComponentTypeListVm { FormTypeName = "Database" },
                new ComponentTypeListVm { FormTypeName = "SINGLE SIGNON" }, // Test case insensitive
                new ComponentTypeListVm { FormTypeName = "Server" }
            };
            _dataProviderMock.Setup(dp => dp.ComponentType.GetComponentTypeList())
                             .ReturnsAsync(componentTypeList);

            // Act
            var result = await _controller.SingleSignOnList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);

            // Verify the success response structure
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task ServerList_NoParameter_WithValidData_ReturnsFilteredServerTypes()
        {
            // Arrange
            var componentTypeList = new List<ComponentTypeListVm>
            {
                new ComponentTypeListVm { FormTypeName = "Server" },
                new ComponentTypeListVm { FormTypeName = "Database" },
                new ComponentTypeListVm { FormTypeName = "SERVER" }, // Test case insensitive
                new ComponentTypeListVm { FormTypeName = "Replication" }
            };
            _dataProviderMock.Setup(dp => dp.ComponentType.GetComponentTypeList())
                             .ReturnsAsync(componentTypeList);

            // Act
            var result = await _controller.ServerList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);

            // Verify the success response structure
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }
    }
}
