﻿using ContinuityPatrol.Application.Features.RiskMitigation.Commands.Create;
using ContinuityPatrol.Application.Features.RiskMitigation.Commands.Delete;
using ContinuityPatrol.Application.Features.RiskMitigation.Commands.Update;
using ContinuityPatrol.Application.Features.RiskMitigation.Queries.GetDetail;
using ContinuityPatrol.Application.Features.RiskMitigation.Queries.GetList;
using ContinuityPatrol.Application.Features.RiskMitigation.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RiskMitigationModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class RiskMitigationController : CommonBaseController
{
    [HttpPost]
    [Authorize(Policy = Permissions.Dashboard.Create)]
    public async Task<ActionResult<CreateRiskMitigationResponse>> CreateRiskMitigation(
        [FromBody] CreateRiskMitigationCommand createRiskMitigationCommand)
    {
        Logger.LogDebug($"Create RiskMitigation '{createRiskMitigationCommand.InfraObjectName}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateRiskMitigation), await Mediator.Send(createRiskMitigationCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Dashboard.Edit)]
    public async Task<ActionResult<UpdateRiskMitigationResponse>> UpdateRiskMitigation(
        [FromBody] UpdateRiskMitigationCommand updateRiskMitigationCommand)
    {
        Logger.LogDebug($"Update RiskMitigation '{updateRiskMitigationCommand.InfraObjectName}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateRiskMitigationCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Dashboard.Delete)]
    public async Task<ActionResult<DeleteRiskMitigationResponse>> DeleteRiskMitigation(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "RiskMitigation Id");

        Logger.LogDebug($"Delete RiskMitigation Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteRiskMitigationCommand { Id = id }));
    }

    [HttpGet("{id}", Name = "GetRiskMitigations")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<RiskMitigationDetailVm>> GetRiskMitigationById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "RiskMitigation Id");

        Logger.LogDebug($"Get RiskMitigation Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetRiskMitigationDetailQuery { Id = id }));
    }

    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<RiskMitigationListVm>>> GetRiskMitigation()
    {
        Logger.LogDebug("Get All RiskMitigation");

        return Ok(await Mediator.Send(new GetRiskMitigationListQuery()));
    }

    [Route("paginated-list")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<PaginatedResult<RiskMitigationListVm>>> GetPaginatedRiskMitigation([FromQuery] GetRiskMitigationPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in RiskMitigation Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllRiskMitigationCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}