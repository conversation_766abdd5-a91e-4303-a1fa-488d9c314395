using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Delete;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetList;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AdPasswordJobModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class AdPasswordJobsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<AdPasswordJobListVm>>> GetAdPasswordJobs()
    {
        Logger.LogDebug("Get All AdPasswordJobs");

        return Ok(await Mediator.Send(new GetAdPasswordJobListQuery()));
    }

    [HttpGet("{id}", Name = "GetAdPasswordJob")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<AdPasswordJobDetailVm>> GetAdPasswordJobById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "AdPasswordJob Id");

        Logger.LogDebug($"Get AdPasswordJob Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetAdPasswordJobDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Configuration.View)]
 public async Task<ActionResult<PaginatedResult<AdPasswordJobListVm>>> GetPaginatedAdPasswordJobs([FromQuery] GetAdPasswordJobPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in AdPasswordJob Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateAdPasswordJobResponse>> CreateAdPasswordJob([FromBody] CreateAdPasswordJobCommand createAdPasswordJobCommand)
    {
        Logger.LogDebug($"Create AdPasswordJob '{createAdPasswordJobCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateAdPasswordJob), await Mediator.Send(createAdPasswordJobCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateAdPasswordJobResponse>> UpdateAdPasswordJob([FromBody] UpdateAdPasswordJobCommand updateAdPasswordJobCommand)
    {
        Logger.LogDebug($"Update AdPasswordJob '{updateAdPasswordJobCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateAdPasswordJobCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteAdPasswordJobResponse>> DeleteAdPasswordJob(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "AdPasswordJob Id");

        Logger.LogDebug($"Delete AdPasswordJob Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteAdPasswordJobCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsAdPasswordJobNameExist(string adPasswordJobName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(adPasswordJobName, "AdPasswordJob Name");

     Logger.LogDebug($"Check Name Exists Detail by AdPasswordJob Name '{adPasswordJobName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetAdPasswordJobNameUniqueQuery { Name = adPasswordJobName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


