using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class OracleMonitorStatusFixture : IDisposable
{
    public List<OracleMonitorStatus> OracleMonitorStatusPaginationList { get; set; }
    public List<OracleMonitorStatus> OracleMonitorStatusList { get; set; }
    public OracleMonitorStatus OracleMonitorStatusDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public OracleMonitorStatusFixture()
    {
        var fixture = new Fixture();

        OracleMonitorStatusList = fixture.Create<List<OracleMonitorStatus>>();

        OracleMonitorStatusPaginationList = fixture.CreateMany<OracleMonitorStatus>(20).ToList();

        OracleMonitorStatusDto = fixture.Create<OracleMonitorStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
