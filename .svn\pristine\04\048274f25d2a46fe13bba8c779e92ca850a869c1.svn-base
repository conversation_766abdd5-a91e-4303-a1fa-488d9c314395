﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <View_SelectedScaffolderID>RazorViewEmptyScaffolder</View_SelectedScaffolderID>
    <View_SelectedScaffolderCategoryPath>root/Common/MVC/View</View_SelectedScaffolderCategoryPath>
    <Controller_SelectedScaffolderID>MvcControllerEmptyScaffolder</Controller_SelectedScaffolderID>
    <Controller_SelectedScaffolderCategoryPath>root/Common/MVC/Controller</Controller_SelectedScaffolderCategoryPath>
    <ActiveDebugProfile>ContinuityPatrol.Web</ActiveDebugProfile>
    <Controller_SelectedScaffolderID>MvcControllerEmptyScaffolder</Controller_SelectedScaffolderID>
    <Controller_SelectedScaffolderCategoryPath>root/Common/MVC/Controller</Controller_SelectedScaffolderCategoryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DebuggerFlavor>ProjectDebugger</DebuggerFlavor>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DebuggerFlavor>ProjectDebugger</DebuggerFlavor>
  </PropertyGroup>
  <ItemGroup>
    <Compile Update="Areas\Report\ReportTemplate\AirGapReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\Alert.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\BulkImport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\BulkImportReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\BusinessServiceCardViewReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\BusinessServiceOverviewReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\BusinessServiceSummaryReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\BusinessServiceSummaryXlsReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\CMDBImportReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\CyberResiliencyScheduleLogReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\CyberSnapReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\DatabaseComponentReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\DataLagStatusReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\DRDrillSummaryReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\DRDrillSummaryXLSReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\DriftReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\DriftReportXLS.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\DRReadinessLog.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\DRReadyReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\CGExecutionReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\ExecutedDRDrillReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\InfraObjectConfigurationReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\InfraObjectSchedulerLogReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\InfraObjectSummary.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\InfraObjectSummaryXlsReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\LicenseUtilizationOperationalServiceXlsReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\LicenseUtlizationReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\LicenseUtlizationXlsReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\ManageAlerts.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\ResiliencyReadinessReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RP4VMCGMonitoringReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLAActiveDirectory.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLAAzureStorageAccountReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLADataSyncReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLADB2HADRReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLADeviationReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLAMongoDBReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLAMssql2k19.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLAMssqlAlwaysOn.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLAMssqlAlwaysOnAvailabilityGroup.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLAMssqlDBMirroringReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLAMySQLReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLAODG.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLAOracleRac.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLAPostgresSQL.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLARoboCopyReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLARSyncReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLASRMReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLASVCReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLASybaseRSHADRReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RPOSLAZertoVPGReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RTOReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RTOXlsReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\RunBook.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\ScheduledJobWorkflowReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\ServerComponentReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\UserActivityReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
    <Compile Update="Areas\Report\ReportTemplate\WorkflowActionResult.cs">
      <SubType>XtraReport</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Areas\Report\ReportTemplate\InfraObjectConfigurationReport.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
</Project>