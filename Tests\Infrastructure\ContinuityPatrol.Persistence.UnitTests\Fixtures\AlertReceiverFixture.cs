using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class AlertReceiverFixture : IDisposable
{
    public List<AlertReceiver> AlertReceiverPaginationList { get; set; }
    public List<AlertReceiver> AlertReceiverList { get; set; }
    public AlertReceiver AlertReceiverDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public AlertReceiverFixture()
    {
        var fixture = new Fixture();

        AlertReceiverList = fixture.Create<List<AlertReceiver>>();

        AlertReceiverPaginationList = fixture.CreateMany<AlertReceiver>(20).ToList();

        AlertReceiverPaginationList.ForEach(x => x.CompanyId = CompanyId);
        AlertReceiverPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AlertReceiverPaginationList.ForEach(x => x.IsActive = true);

        AlertReceiverList.ForEach(x => x.CompanyId = CompanyId);
        AlertReceiverList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AlertReceiverList.ForEach(x => x.IsActive = true);

        AlertReceiverDto = fixture.Create<AlertReceiver>();
        AlertReceiverDto.CompanyId = CompanyId;
        AlertReceiverDto.ReferenceId = Guid.NewGuid().ToString();
        AlertReceiverDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
