﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.UserGroupModel.UserGrouplistModel
<div class="modal-dialog modal-dialog-centered ">
    <form class="modal-content" id="CreateForm" asp-controller="UserGroup" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-teams"></i><span>User Group Configuration</span></h6>
            <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label class="form-label">Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-name"></i></span>
                    <input asp-for="GroupName" type="text" id="textgroupName" class="form-control" placeholder="Enter Group Name" autocomplete="off" maxlength="100" />
                </div>
                <span asp-validation-for="GroupName" id="grpname-error"></span>
            </div>
            <div class="form-group">
                <label class="form-label">Description <small class="text-secondary">( Optional )</small></label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-description"></i></span>
                    <input asp-for="GroupDescription" maxlength="500" type="text" id="txtGroupDescription" class="form-control" placeholder="Enter Group Description" autocomplete="off" />
                </div>
                <span asp-validation-for="GroupDescription" id="grpdescription-error"></span>
            </div>
            <div class="form-group">
                <div class="form-label">Users</div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-user"></i></span>
                    <select class="form-select-modal" id="UsersList" data-placeholder="Select Users" multiple>
                    </select>
                </div>
                <span asp-validation-for="Usernames" id="username-error"></span>
            </div>
            <input asp-for="Id" type="hidden" id="textgroupId" class="form-control" />
            <input asp-for="UserProperties" type="hidden" id="datroperties" />
        </div>
        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm" id="SaveUserGroup">Save</button>
            </div>
        </div>
    </form>
</div>
@section Scripts
{
}
