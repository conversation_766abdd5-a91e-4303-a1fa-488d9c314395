﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.OracleRACMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.OracleRACMonitorLogs.Queries.GetPaginatedList;

public class GetOracleRACMonitorLogsPaginatedListQueryHandler : IRequestHandler<
    GetOracleRACMonitorLogsPaginatedListQuery, PaginatedResult<OracleRACMonitorLogsListVm>>
{
    private readonly IMapper _mapper;
    private readonly IOracleRacMonitorLogsRepository _oracleRacMonitorLogsRepository;

    public GetOracleRACMonitorLogsPaginatedListQueryHandler(
        IOracleRacMonitorLogsRepository oracleRacMonitorLogsRepository, IMapper mapper)
    {
        _oracleRacMonitorLogsRepository = oracleRacMonitorLogsRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<OracleRACMonitorLogsListVm>> Handle(
        GetOracleRACMonitorLogsPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _oracleRacMonitorLogsRepository.GetPaginatedQuery();

        var productFilterSpec = new OracleRacMonitorLogsFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<OracleRACMonitorLogsListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}