﻿using ContinuityPatrol.Application.Features.OracleMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.OracleMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.OracleMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.OracleMonitorLogsModel;

namespace ContinuityPatrol.Application.Mappings;

public class OracleMonitorLogsProfile : Profile
{
    public OracleMonitorLogsProfile()
    {
        CreateMap<OracleMonitorLogs, CreateOracleMonitorLogCommand>().ReverseMap();

        CreateMap<OracleMonitorLogs, OracleMonitorLogsDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<OracleMonitorLogs, OracleMonitorLogsListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<OracleMonitorLogs, OracleMonitorLogsDetailByTypeVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}