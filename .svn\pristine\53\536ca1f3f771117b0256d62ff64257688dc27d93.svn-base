﻿using ContinuityPatrol.Application.Features.Template.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Template.Queries;

public class GetTemplateNameUniqueQueryHandlerTests : IClassFixture<TemplateFixture>
{
    private readonly TemplateFixture _templateFixture;

    private Mock<ITemplateRepository> _mockTemplateRepository;

    private readonly GetTemplateNameUniqueQueryHandler _handler;

    public GetTemplateNameUniqueQueryHandlerTests(TemplateFixture templateFixture)
    {
        _templateFixture = templateFixture;

        _mockTemplateRepository = TemplateRepositoryMocks.GetTemplateNameUniqueRepository(_templateFixture.Templates);

        _handler = new GetTemplateNameUniqueQueryHandler(_mockTemplateRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_TemplateName_Exist()
    {
        _templateFixture.Templates[0].Name = "PR_Site";
        _templateFixture.Templates[0].IsActive = true;

        var result = await _handler.Handle(new GetTemplateNameUniqueQuery { TemplateId = _templateFixture.Templates[0].ReferenceId, TemplateName = _templateFixture.Templates[0].Name }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_TemplateNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetTemplateNameUniqueQuery { TemplateName = "Testing", TemplateId = 1.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_False_TemplateName_NotMatch()
    {
        var result = await _handler.Handle(new GetTemplateNameUniqueQuery { TemplateName = "Demo", TemplateId = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsTemplateNameExist_OneTime()
    {
        await _handler.Handle(new GetTemplateNameUniqueQuery(), CancellationToken.None);

        _mockTemplateRepository.Verify(x => x.IsTemplateNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockTemplateRepository = TemplateRepositoryMocks.GetTemplateEmptyRepository();

        var result = await _handler.Handle(new GetTemplateNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}