﻿using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Tests.Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;

namespace ContinuityPatrol.Shared.Tests.Mocks
{
    public class ControllerContextMocks
    {
        private readonly DefaultHttpContext _defaultContext = new DefaultHttpContext();

        private readonly Mock<ISession> _sessionMock = new Mock<ISession>();

        private readonly Mock<IHttpContextAccessor> _contextAccessorMock = new Mock<IHttpContextAccessor>();

        public ControllerContext CreateControllerContext()
        {
            var services = new ServiceCollection();

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            // Build the service provider
            var serviceProvider = services.BuildServiceProvider();

            // Create an HttpContext instance with a default HttpContextAccessor
            var httpContext = new DefaultHttpContext
            {
                RequestServices = serviceProvider,
                Session = new TestSession()
            };

            _contextAccessorMock.Setup(_ => _.HttpContext).Returns(httpContext);

            WebHelper.Configure(_contextAccessorMock.Object);

            var context = new ControllerContext()
            {
                HttpContext = httpContext
            };
            return context;

        }

        public ControllerContext Default() {
 
            _defaultContext.Session = new TestSession();

            _contextAccessorMock.Setup(_ => _.HttpContext).Returns(_defaultContext);

            WebHelper.Configure(_contextAccessorMock.Object);

            var context = new ControllerContext()
            {
                HttpContext = _defaultContext
            };
            return context;
        }

        public ControllerContext OldLogin()
        {
            var cookiesMock = new Mock<IRequestCookieCollection>();
            cookiesMock.Setup(c => c.Count).Returns(3);
            cookiesMock.Setup(c => c.Keys).Returns(new List<string> { "__Host-Identity" });

            var httpContextMock = new Mock<HttpContext>();
            httpContextMock.Setup(c => c.Request.Cookies).Returns(cookiesMock.Object);

            var controllerContext = new ControllerContext
            {
                HttpContext = httpContextMock.Object
            };

            return controllerContext;
        }
    }
}
