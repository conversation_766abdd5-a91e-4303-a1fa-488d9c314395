﻿using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Create;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Update;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetDetailByDatabaseId;
using ContinuityPatrol.Domain.ViewModels.InfraReplicationMappingModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class InfraReplicationMappingProfile : Profile
{
    public InfraReplicationMappingProfile()
    {
        CreateMap<CreateInfraReplicationMappingCommand, InfraReplicationMappingViewModel>().ReverseMap();
        CreateMap<UpdateInfraReplicationMappingCommand, InfraReplicationMappingViewModel>().ReverseMap();

        CreateMap<InfraReplicationMapping, CreateInfraReplicationMappingCommand>().ReverseMap();
        CreateMap<UpdateInfraReplicationMappingCommand, InfraReplicationMapping>()
            .ForMember(x => x.Id, y => y.Ignore());

        CreateMap<InfraReplicationMapping, InfraReplicationMappingDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraReplicationMapping, InfraReplicationMappingListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraReplicationMapping, InfraReplicationMappingByDatabaseIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraReplicationMapping, InfraReplicationMappingListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        
        CreateMap<PaginatedResult<InfraReplicationMapping>,PaginatedResult<InfraReplicationMappingListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}