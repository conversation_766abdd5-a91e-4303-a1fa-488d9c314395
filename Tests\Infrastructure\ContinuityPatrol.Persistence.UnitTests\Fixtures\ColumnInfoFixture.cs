using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ColumnInfoFixture : IDisposable
{
    public List<ColumnInfo> ColumnInfoPaginationList { get; set; }
    public List<ColumnInfo> ColumnInfoList { get; set; }
    public ColumnInfo ColumnInfoDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ColumnInfoFixture()
    {
        var fixture = new Fixture();

        ColumnInfoList = fixture.Create<List<ColumnInfo>>();

        ColumnInfoPaginationList = fixture.CreateMany<ColumnInfo>(20).ToList();

        ColumnInfoDto = fixture.Create<ColumnInfo>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
