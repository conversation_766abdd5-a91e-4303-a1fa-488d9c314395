﻿using ContinuityPatrol.Application.Features.Database.Commands.SaveAs;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Infrastructure.Contract;

namespace ContinuityPatrol.Application.UnitTests.Features.Database.Validators;

public class SaveAsDatabaseValidatorTests
{
    public List<Domain.Entities.Database> Databases { get; set; }
    private readonly Mock<IDatabaseRepository> _mockDatabaseRepository;
    private readonly Mock<IServerRepository> _mockServerRepository;
    private readonly Mock<ISiteRepository> _mockSiteRepository;
    private readonly Mock<ILicenseValidationService> _mockLicenseValidationService;
    private readonly Mock<ILicenseManagerRepository> _mockLicenseManagerRepository;
    private readonly Mock<ISiteTypeRepository> _mockSiteTypeRepository;
    private  SaveAsDatabaseCommandValidator  _validator;

	public SaveAsDatabaseValidatorTests()
    {
        Databases = new Fixture().Create<List<Domain.Entities.Database>>();

        _mockDatabaseRepository = DatabaseRepositoryMocks.SaveAsDatabaseRepository(Databases);

        _mockServerRepository = new Mock<IServerRepository>();
        _mockSiteRepository = new Mock<ISiteRepository>();
        _mockLicenseValidationService = new Mock<ILicenseValidationService>();
        _mockLicenseManagerRepository = new Mock<ILicenseManagerRepository>();
        _mockSiteTypeRepository = new Mock<ISiteTypeRepository>();
    }

    //Name

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_SaveAsDatabaseCommandValidator_Name_WithEmpty(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
		_validator = new SaveAsDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        saveAsDatabaseCommand.Name = "";
        saveAsDatabaseCommand.DatabaseId = Guid.NewGuid().ToString();
        
        var validateResult = await _validator.ValidateAsync(saveAsDatabaseCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_SaveAsDatabaseCommandValidator_Name_IsNull(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
		_validator = new SaveAsDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        saveAsDatabaseCommand.Name = null;
        saveAsDatabaseCommand.DatabaseId = Guid.NewGuid().ToString();
       
        var validateResult = await _validator.ValidateAsync(saveAsDatabaseCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameNotEmpty, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_SaveAsDatabaseCommandValidator_Name_MiniMumRange(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
		_validator = new SaveAsDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        saveAsDatabaseCommand.Name = "DB";
        saveAsDatabaseCommand.DatabaseId = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(saveAsDatabaseCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameRange, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_SaveAsDatabaseCommandValidator_Name_MaxiMumRange(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
		_validator = new SaveAsDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        saveAsDatabaseCommand.Name = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        saveAsDatabaseCommand.DatabaseId = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(saveAsDatabaseCommand, CancellationToken.None);
		Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Name should contain between 3 to 100 characters.");
		
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_SaveAsDatabaseCommandValidator_Valid_Name(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
		_validator = new SaveAsDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        saveAsDatabaseCommand.Name = "   PTS   ";
        saveAsDatabaseCommand.DatabaseId = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(saveAsDatabaseCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_SaveAsDatabaseCommandValidator_Valid_Name_With_DoubleSpace_InFront(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
		_validator = new SaveAsDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        saveAsDatabaseCommand.Name = "  PTS India";
        saveAsDatabaseCommand.DatabaseId = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(saveAsDatabaseCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_SaveAsDatabaseCommandValidator_Valid_Name_With_TripleSpace_InBetween(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
		_validator = new SaveAsDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        saveAsDatabaseCommand.Name = "PTS Technosoft    India";
        saveAsDatabaseCommand.DatabaseId = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(saveAsDatabaseCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_SaveAsDatabaseCommandValidator_Valid_Name_With_SpecialCharacters_InFront(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
		_validator = new SaveAsDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        saveAsDatabaseCommand.Name = "@#PTSTechnosofIndia";
        saveAsDatabaseCommand.DatabaseId = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(saveAsDatabaseCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_SaveAsDatabaseCommandValidator_Valid_Name_With_SpecialCharacters_Only(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
		_validator = new SaveAsDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        saveAsDatabaseCommand.Name = "!@#$^&%&:><;";
        saveAsDatabaseCommand.DatabaseId = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(saveAsDatabaseCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[0].ErrorMessage);
    }
    [Theory]
    [AutoDatabaseData]
    public async Task Verify_SaveAsDatabaseCommandValidator_Valid_Name_With_UnderScore_InFront(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
		_validator = new SaveAsDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        saveAsDatabaseCommand.Name = "_PTSTechnosofIndia";
        saveAsDatabaseCommand.DatabaseId = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(saveAsDatabaseCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_SaveAsDatabaseCommandValidator_Valid_Name_With_UnderScore_InFront_AndBack(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
		_validator = new SaveAsDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        saveAsDatabaseCommand.Name = "_PTSTechnosofIndia_";
        saveAsDatabaseCommand.DatabaseId = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(saveAsDatabaseCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_SaveAsDatabaseCommandValidator_Valid_Name_With_Numbers_InFront(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
		_validator = new SaveAsDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        saveAsDatabaseCommand.Name = "125PTSTechnosofIndia";
        saveAsDatabaseCommand.DatabaseId = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(saveAsDatabaseCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_SaveAsDatabaseCommandValidator_Valid_Name_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
		_validator = new SaveAsDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        saveAsDatabaseCommand.Name = "_125PTSTechnosofIndia_";
        saveAsDatabaseCommand.DatabaseId = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(saveAsDatabaseCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_SaveAsDatabaseCommandValidator_Valid_Name_With_UnderScore_InFront_With_Numbers_InBack(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
		_validator = new SaveAsDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        saveAsDatabaseCommand.Name = "_PTSTechnosofIndia456";
        saveAsDatabaseCommand.DatabaseId = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(saveAsDatabaseCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_SaveAsDatabaseCommandValidator_Valid_Name_With_Numbers_Only(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
		_validator = new SaveAsDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        saveAsDatabaseCommand.Name = "12345656778";
        saveAsDatabaseCommand.DatabaseId = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(saveAsDatabaseCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[0].ErrorMessage);
    }
}