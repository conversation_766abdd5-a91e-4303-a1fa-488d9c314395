using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ApprovalMatrixApprovalRepositoryTests : IClassFixture<ApprovalMatrixApprovalFixture>
{
    private readonly ApprovalMatrixApprovalFixture _approvalMatrixApprovalFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ApprovalMatrixApprovalRepository _repository;

    public ApprovalMatrixApprovalRepositoryTests(ApprovalMatrixApprovalFixture approvalMatrixApprovalFixture)
    {
        _approvalMatrixApprovalFixture = approvalMatrixApprovalFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new ApprovalMatrixApprovalRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var approvalMatrixApproval = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalDto;

        // Act
        var result = await _repository.AddAsync(approvalMatrixApproval);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(approvalMatrixApproval.ProcessName, result.ProcessName);
        Assert.Equal(approvalMatrixApproval.ApproverId, result.ApproverId);
        Assert.Single(_dbContext.ApprovalMatrixApprovals);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var approvalMatrixApproval = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalDto;
        await _repository.AddAsync(approvalMatrixApproval);

        approvalMatrixApproval.ProcessName = "UpdatedProcessName";
        approvalMatrixApproval.Status = "Approved";
        approvalMatrixApproval.IsApproval = true;

        // Act
        var result = await _repository.UpdateAsync(approvalMatrixApproval);

        // Assert
        Assert.Equal("UpdatedProcessName", result.ProcessName);
        Assert.Equal("Approved", result.Status);
        Assert.True(result.IsApproval);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.NullReferenceException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var approvalMatrixApproval = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalDto;
        await _repository.AddAsync(approvalMatrixApproval);

        // Act
        var result = await _repository.DeleteAsync(approvalMatrixApproval);

        // Assert
        Assert.Equal(approvalMatrixApproval.ProcessName, result.ProcessName);
        Assert.Empty(_dbContext.ApprovalMatrixApprovals);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var approvalMatrixApproval = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalDto;
        var addedEntity = await _repository.AddAsync(approvalMatrixApproval);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.ProcessName, result.ProcessName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var approvalMatrixApproval = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalDto;
        await _repository.AddAsync(approvalMatrixApproval);

        // Act
        var result = await _repository.GetByReferenceIdAsync(approvalMatrixApproval.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(approvalMatrixApproval.ReferenceId, result.ReferenceId);
        Assert.Equal(approvalMatrixApproval.ProcessName, result.ProcessName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var approvalMatrixApprovals = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalList;
        await _repository.AddRange(approvalMatrixApprovals);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(approvalMatrixApprovals.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenProcessNameExistsAndIdIsInvalid()
    {
        // Arrange
        var approvalMatrixApproval = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalDto;
        approvalMatrixApproval.ProcessName = "ExistingProcessName";
        await _repository.AddAsync(approvalMatrixApproval);

        // Act
        var result = await _repository.IsNameExist("ExistingProcessName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenProcessNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var approvalMatrixApprovals = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalList;
        await _repository.AddRange(approvalMatrixApprovals);

        // Act
        var result = await _repository.IsNameExist("NonExistentProcessName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenProcessNameExistsForSameEntity()
    {
        // Arrange
        var approvalMatrixApproval = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalDto;
        approvalMatrixApproval.ProcessName = "SameProcessName";
        await _repository.AddAsync(approvalMatrixApproval);

        // Act
        var result = await _repository.IsNameExist("SameProcessName", approvalMatrixApproval.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetApprovalMatrixByApprovalIds Tests

    [Fact]
    public async Task GetApprovalMatrixByApprovalIds_ShouldReturnMatchingApprovals()
    {
        // Arrange
        var approverIds = new List<string> { "APPROVER_001", "APPROVER_002", "APPROVER_003" };
        
        var approvals = new List<ApprovalMatrixApproval>
        {
            new ApprovalMatrixApproval 
            { 
                ApproverId = "APPROVER_001", 
                ProcessName = "Process1",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new ApprovalMatrixApproval 
            { 
                ApproverId = "APPROVER_002", 
                ProcessName = "Process2",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new ApprovalMatrixApproval 
            { 
                ApproverId = "APPROVER_004", 
                ProcessName = "Process3",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRange(approvals);

        // Act
        var result = await _repository.GetApprovalMatrixByApprovalIds(approverIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains(x.ApproverId, approverIds));
    }

    [Fact]
    public async Task GetApprovalMatrixByApprovalIds_ShouldReturnEmpty_WhenNoMatch()
    {
        // Arrange
        var approvals = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalList;
        await _repository.AddRange(approvals);

        var nonExistentApproverIds = new List<string> { "NON_EXISTENT_001", "NON_EXISTENT_002" };

        // Act
        var result = await _repository.GetApprovalMatrixByApprovalIds(nonExistentApproverIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetApprovalMatrixApprovalByRequestId Tests

    [Fact]
    public async Task GetApprovalMatrixApprovalByRequestId_ShouldReturnMatchingApprovals()
    {
        // Arrange
        var requestId = "REQUEST_001";
        
        var approvals = new List<ApprovalMatrixApproval>
        {
            new ApprovalMatrixApproval 
            { 
                RequestId = requestId, 
                ProcessName = "Process1",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new ApprovalMatrixApproval 
            { 
                RequestId = requestId, 
                ProcessName = "Process2",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new ApprovalMatrixApproval 
            { 
                RequestId = "REQUEST_002", 
                ProcessName = "Process3",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRange(approvals);

        // Act
        var result = await _repository.GetApprovalMatrixApprovalByRequestId(requestId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(requestId, x.RequestId));
    }

    [Fact]
    public async Task GetApprovalMatrixApprovalByRequestId_ShouldReturnEmpty_WhenNoMatch()
    {
        // Arrange
        var approvals = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalList;
        await _repository.AddRange(approvals);

        // Act
        var result = await _repository.GetApprovalMatrixApprovalByRequestId("NON_EXISTENT_REQUEST");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetUnapprovedByRequestId Tests

    [Fact]
    public async Task GetUnapprovedByRequestId_ShouldReturnUnapprovedApprovals()
    {
        // Arrange
        var requestId = "REQUEST_001";

        var approvals = new List<ApprovalMatrixApproval>
        {
            new ApprovalMatrixApproval
            {
                RequestId = requestId,
                Status = "Pending",
                ProcessName = "Process1",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new ApprovalMatrixApproval
            {
                RequestId = requestId,
                Status = "Approved",
                ProcessName = "Process2",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new ApprovalMatrixApproval
            {
                RequestId = requestId,
                Status = "Rejected",
                ProcessName = "Process3",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRange(approvals);

        // Act
        var result = await _repository.GetUnapprovedByRequestId(requestId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(requestId, x.RequestId));
        Assert.All(result, x => Assert.NotEqual("Approved", x.Status));
    }

    [Fact]
    public async Task GetUnapprovedByRequestId_ShouldReturnEmpty_WhenAllApproved()
    {
        // Arrange
        var requestId = "REQUEST_001";

        var approvals = new List<ApprovalMatrixApproval>
        {
            new ApprovalMatrixApproval
            {
                RequestId = requestId,
                Status = "Approved",
                ProcessName = "Process1",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new ApprovalMatrixApproval
            {
                RequestId = requestId,
                Status = "Approved",
                ProcessName = "Process2",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRange(approvals);

        // Act
        var result = await _repository.GetUnapprovedByRequestId(requestId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var approvals = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalList;
        var approval1 = approvals[0];
        var approval2 = approvals[1];

        // Act
        var task1 = _repository.AddAsync(approval1);
        var task2 = _repository.AddAsync(approval2);

        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.ApprovalMatrixApprovals.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var approvals = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRange(approvals);
        var initialCount = approvals.Count;

        var toUpdate = approvals.Take(2).ToList();
        toUpdate.ForEach(x => x.Status = "Approved");
        await _repository.UpdateRange(toUpdate);

        var toDelete = approvals.Skip(2).Take(1).ToList();
        await _repository.RemoveRange(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Status == "Approved").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleNullParameters()
    {
        // Act & Assert
        var result1 = await _repository.IsNameExist(null, "valid-guid");
        var result2 = await _repository.IsNameExist("TestProcessName", null);
        var result3 = await _repository.IsNameExist(null, null);

        Assert.False(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    [Fact]
    public async Task GetApprovalMatrixByApprovalIds_ShouldHandleEmptyList()
    {
        // Arrange
        var approvals = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalList;
        await _repository.AddRange(approvals);

        // Act
        var result = await _repository.GetApprovalMatrixByApprovalIds(new List<string>());

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetApprovalMatrixByApprovalIds_ShouldHandleNullList()
    {
        // Arrange
        var approvals = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalList;
        await _repository.AddRange(approvals);
        // Assert
        await Assert.ThrowsAsync<ArgumentNullException>(()=> _repository.GetApprovalMatrixByApprovalIds(null));

    }

    #endregion
}
