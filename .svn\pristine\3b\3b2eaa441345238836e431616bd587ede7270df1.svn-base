using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Shared.Services.Provider;

public interface IDataProvider
{
    #region DataProvider
    ICyberJobWorkflowSchedulerService CyberJobWorkflowScheduler { get; set; }
      IGlobalVariableService GlobalVariable { get; set; }

    ICGExecutionReportService CGExecutionReport { get; set; }
    ITwoStepAuthenticationService TwoStepAuthentication { get; set; }
    IFiaCostService FiaCost { get; set; }

    IApprovalMatrixUsersService ApprovalMatrixUsers { get; set; }

    IDataSyncJobService DataSyncJob { get; set; }
    IComponentSaveAllService ComponentSaveAll { get; set; }
    IWorkflowDrCalenderService WorkflowDrCalender { get; set; }

    IAdPasswordJobService AdPasswordJob { get; set; }

    IAdPasswordExpireService AdPasswordExpire { get; set; }

    IFiaIntervalService FiaInterval { get; set; }

    IFiaImpactTypeService FiaImpactType { get; set; }

    IFiaImpactCategoryService FiaImpactCategory { get; set; }

    ICyberAirGapService CyberAirGap { get; set; }

    ICyberSnapsService CyberSnaps { get; set; }

    ICyberAlertService CyberAlert { get; set; }

    ICyberComponentMappingService CyberComponentMapping { get; set; }
    ICyberAirGapLogService CyberAirGapLog { get; set; }

    ICyberAirGapStatusService CyberAirGapStatus { get; set; }


    IDriftEventService DriftEvent { get; set; }
    ICyberJobManagementService CyberJobManagement { get; set; }

    ICyberComponentGroupService CyberComponentGroup { get; set; }

    ICyberComponentService CyberComponent { get; set; }


    IDriftResourceSummaryService DriftResourceSummary { get; set; }

    IDriftManagementMonitorStatusService DriftManagementMonitorStatus { get; set; }

    IDriftJobService DriftJob { get; set; }

    IApprovalMatrixApprovalService ApprovalMatrixApproval { get; set; }

    IApprovalMatrixRequestService ApprovalMatrixRequest { get; set; }

    IDriftProfileService DriftProfile { get; set; }

    IDriftImpactTypeMasterService DriftImpactTypeMaster { get; set; }
    IDriftCategoryMasterService DriftCategoryMaster { get; set; }

    IDriftParameterService DriftParameter { get; set; }

    IEmployeeService Employee { get; set; }


    IBulkImportServices BulkImport { get; set; }
    IBulkImportActionResultService BulkImportActionResult { get; set; }

    IBulkImportOperationGroupService BulkImportOperationGroup { get; set; }

    IBulkImportOperationService BulkImportOperation { get; set; }

    IDynamicDashboardWidgetService DynamicDashboardWidget { get; set; }

    IDynamicDashboardMapService DynamicDashboardMap { get; set; }

    IDynamicSubDashboardService DynamicSubDashboard { get; set; }

    IDynamicDashboardService DynamicDashboard { get; set; }

    IRoboCopyJobService RoboCopyJob { get; set; }
    IRsyncJobService RsyncJob { get; set; }
    IVeritasClusterService VeritasCluster { get; set; }

    IHacmpClusterService HacmpCluster { get; set; }

    IFiaTemplateService FiaTemplate { get; set; }

    IBiaRulesService BiaRule { get; set; }

    IDb2HaDrMonitorLogService Db2HaDrMonitorLog { get; set; }
    IBackUpLogService BackUpLog { get; set; }

    IInfraMasterService InfraMaster { get; set; }

    IBackUpService BackUp { get; set; }

    IArchiveService Archive { get; set; }

    IPageSolutionMappingService PageSolutionMapping { get; set; }
    IPageWidgetService PageWidget { get; set; }

    IRsyncOptionService RsyncOption { get; set; }

    IRoboCopyService RoboCopy { get; set; }

    IDataSyncOptionsService DataSync { get; set; }

    IImpactActivityService ImpactActivity { get; set; }

    IPageBuilderService PageBuilder { get; set; }

    ISiteLocationService SiteLocation { get; set; }
    IUserLoginService UserLogin { get; set; }
    IIncidentManagementSummaryService IncidentManagementSummary { get; set; }
    IWorkflowActionFieldMasterService WorkflowActionFieldMaster { get; set; }
    ICompanyService Company { get; set; }
    ISolutionHistoryService SolutionHistory { get; set; }
    IAlertNotificationService AlertNotification { get; set; }
    IAlertInformationService AlertInformation { get; set; }
    IAlertMasterService AlertMasterService { get; set; }
    IAlertService Alerts { get; set; }
    IAlertReceiverService AlertReceiver { get; set; }
    IAccountService Account { get; set; }
    IUserService User { get; set; }
    IDataSetColumnsService DataSetColumns { get; set; }
    IServerService Server { get; set; }
    IServerTypeService ServerType { get; set; }
    ISiteService Site { get; set; }
    IFormService Form { get; set; }
    IFormTypeService FormType { get; set; }
    IFormHistoryService FormHistory { get; set; }
    ILicenseManagerService LicenseManager { get; set; }
    IWorkflowActionService WorkflowAction { get; set; }
    IWorkflowCategoryService WorkflowCategory { get; set; }
    IAccessManagerService AccessManager { get; set; }
    IBusinessFunctionService BusinessFunction { get; set; }
    IBusinessServiceService BusinessService { get; set; }
    ICredentialProfileService CredentialProfile { get; set; }
    IDatabaseService Database { get; set; }
    IDataSetService DataSet { get; set; }
    IInfraObjectService InfraObject { get; set; }
    ILoadBalancerService LoadBalancer { get; set; }
    INodeService Node { get; set; }
    IReplicationService Replication { get; set; }
    IMonitorService Monitor { get; set; }
    IComponentTypeService ComponentType { get; set; }
    ISettingService Setting { get; set; }
    ISingleSignOnService SingleSignOn { get; set; }
    ISiteTypeService SiteType { get; set; }
    ISmtpConfigurationService SmtpConfiguration { get; set; }
    ITableAccessService TableAccess { get; set; }
    IUserActivityService UserActivity { get; set; }
    IUserRoleService UserRole { get; set; }
    ITemplateService Template { get; set; }
    IWorkflowHistoryService WorkflowHistory { get; set; }
    IWorkflowInfraObjectService WorkflowInfraObject { get; set; }
    IWorkflowProfileInfoService WorkflowProfileInfo { get; set; }
    IGroupPolicyService GroupPolicy { get; set; }
    IWorkflowOperationGroupService WorkflowOperationGroup { get; set; }
    IWorkflowProfileService WorkflowProfile { get; set; }
    IWorkflowPermissionService WorkflowPermission { get; set; }
    IUserGroupService UserGroup { get; set; }
    IOracleMonitorLogsService OracleMonitorLogs { get; set; }
    IMssqlAlwaysOnMonitorStatusService MssqlAlwaysOnMonitorStatus { get; set; }
    IMssqlMonitorLogsService MssqlMonitorLogs { get; set; }
    IOracleRACMonitorLogsService OracleRACMonitorLogs { get; set; }
    IMssqlMonitorStatusService MssqlMonitorStatus { get; set; }
    IMysqlMonitorLogsService MysqlMonitorLogs { get; set; }
    IOracleMonitorStatusService OracleMonitorStatus { get; set; }
    IOracleRACMonitorStatusService OracleRACMonitorStatus { get; set; }
    IPostgresMonitorLogsService PostgresMonitorLogs { get; set; }
    IPostgresMonitorStatusService PostgresMonitorStatus { get; set; }
    IMysqlMonitorStatusService MysqlMonitorStatus { get; set; }
    IWorkflowActionTypeService WorkflowActionTypes { get; set; }
    IPluginManagerService PluginManager { get; set; }
    IWorkflowOperationService WorkflowOperation { get; set; }
    IJobService JobService { get; set; }
    IDrReadyService DrReady { get; set; }
    IManageWorkflowListService ManageWorkflowList { get; set; }
    IWorkflowService Workflow { get; set; }
    IReplicationMasterService ReplicationMaster { get; set; }
    IInfraReplicationMappingService InfraReplicationMapping { get; set; }
    IAboutCpService AboutCP { get; set; }
    IFormMappingService FormMapping { get; set; }
    IMonitorServicesService MonitorServices { get; set; }
    IWorkflowActionResultService WorkflowActionResult { get; set; }
    IRiskMitigationService RiskMitigation { get; set; }
    IUserInfraObjectService UserInfraObject { get; set; }
    ISmsConfigurationService SmsConfiguration { get; set; }
    IBusinessServiceAvailabilityService BusinessServiceAvailability { get; set; }
    IBusinessServiceEvaluationService BusinessServiceEvaluation { get; set; }
    ILicenseInfoService LicenseInfo { get; set; }
    IBusinessServiceHealthStatusService BusinessServiceHealthStatus { get; set; }
    IHeatMapStatusService HeatMapStatus { get; set; }
    IDrReadyStatusService DrReadyStatus { get; set; }
    IInfraObjectInfoService InfraObjectInfo { get; set; }
    IInfraSummaryService InfraSummary { get; set; }
    IImpactAvailabilityService ImpactAvailability { get; set; }
    IDashboardViewService DashboardView { get; set; }
    IWorkflowExecutionTempService WorkflowExecutionTemp { get; set; }
    IDashboardViewLogService DashboardViewLog { get; set; }
    IIncidentManagementService IncidentManagement { get; set; }
    IFourEyeApproverService FourEyeApproverService { get; set; }
    IDrCalenderService DRCalenderService { get; set; }
    IRpoSlaDeviationReportService RpoSlaDeviationReport { get; set; }
    IReportService Report { get; set; }
    IReportScheduleService ReportSchedule { get; set; }
    IDb2HaDrMonitorStatusService Db2HaDrMonitorStatus { get; set; }
    IMsSqlNativeLogShippingMonitorStatusService MsSqlNativeLogShippingMonitorStatus { get; set; }
    IMongoDbMonitorStatusService MongoDbMonitorStatus { get; set; }
    ISvcMsSqlMonitorStatusService SvcMsSqlMonitorStatus { get; set; }
    IWorkflowPredictionService WorkflowPredictionServices { get; set; }
    IGlobalSettingService GlobalSettings { get; set; }
    IMSSQLDbMirroringMonitorStatusService MSSQLDbMirroringMonitorStatus { get; set; }
    IServerSubTypeService ServerSubType { get; set; }
    IApprovalMatrixService approvalMatrixService { get; set; }
    ITeamMasterService TeamMasterService { get; set; }
    ITeamResourceService TeamResourceService { get; set; }
    IEscalationMatrixService EscalationService { get; set; }
    IEscalationMatrixLevelService EscalationMatrixLevelService { get; set; }
    IReplicationJobService ReplicationJobService { get; set; }

    IRpForVmMonitorStatusService RpForVmMonitorStatusService { get; set; }
    IUserInfoService UserInfo { get; set; }

    IServerLogService ServerLog { get; set; }

    IRpForVmCGMonitorLogsService RpForVmCGMonitorLogs { get; set; }
    IRpForVmCGMonitorStatusService RpForVmCGMonitorStatus { get; set; }
    IFastCopyMonitorService FastCopyMonitor { get; set; }

    #endregion
}
