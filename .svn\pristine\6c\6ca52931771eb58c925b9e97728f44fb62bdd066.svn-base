using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class BulkImportActionResultFixture
{
    public List<BulkImportActionResultListVm> BulkImportActionResultListVm { get; }
    public BulkImportActionResultDetailVm BulkImportActionResultDetailVm { get; }
    public CreateBulkImportActionResultCommand CreateBulkImportActionResultCommand { get; }
    public UpdateBulkImportActionResultCommand UpdateBulkImportActionResultCommand { get; }

    public BulkImportActionResultFixture()
    {
        var fixture = new Fixture();

        // Create sample BulkImportActionResult list data
        BulkImportActionResultListVm = new List<BulkImportActionResultListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Import-Node-01",
                BulkImportOperationId = Guid.NewGuid().ToString(),
                BulkImportOperationGroupId = Guid.NewGuid().ToString(),
                EntityId = Guid.NewGuid().ToString(),
                EntityName = "Server Infrastructure Import",
                EntityType = "Server",
                Status = "Completed",
                StartTime = DateTime.Now.AddHours(-2),
                EndTime = DateTime.Now.AddHours(-1),
                ErrorMessage = null
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Import-Node-02",
                BulkImportOperationId = Guid.NewGuid().ToString(),
                BulkImportOperationGroupId = Guid.NewGuid().ToString(),
                EntityId = Guid.NewGuid().ToString(),
                EntityName = "Database Configuration Import",
                EntityType = "Database",
                Status = "Failed",
                StartTime = DateTime.Now.AddHours(-3),
                EndTime = DateTime.Now.AddHours(-2),
                ErrorMessage = "Connection timeout during database import operation"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Import-Node-03",
                BulkImportOperationId = Guid.NewGuid().ToString(),
                BulkImportOperationGroupId = Guid.NewGuid().ToString(),
                EntityId = Guid.NewGuid().ToString(),
                EntityName = "Network Component Import",
                EntityType = "Network",
                Status = "In Progress",
                StartTime = DateTime.Now.AddMinutes(-30),
                EndTime = DateTime.Now.AddMinutes(30),
                ErrorMessage = null
            }
        };

        // Create detailed BulkImportActionResult data
        BulkImportActionResultDetailVm = new BulkImportActionResultDetailVm
        {
           
            Id = Guid.NewGuid().ToString(),
            CompanyId = Guid.NewGuid().ToString(),
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "Enterprise-Import-Node",
            BulkImportOperationId = Guid.NewGuid().ToString(),
            BulkImportOperationGroupId = Guid.NewGuid().ToString(),
            EntityId = Guid.NewGuid().ToString(),
            EntityName = "Critical Infrastructure Bulk Import",
            EntityType = "Infrastructure",
            Status = "Completed",
            StartTime = DateTime.Now.AddHours(-4),
            EndTime = DateTime.Now.AddHours(-3),
            ErrorMessage = null,
           
        };

        // Create command for creating BulkImportActionResult
        CreateBulkImportActionResultCommand = new CreateBulkImportActionResultCommand
        {
            CompanyId = Guid.NewGuid().ToString(),
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "New-Import-Node",
            BulkImportOperationId = Guid.NewGuid().ToString(),
            BulkImportOperationGroupId = Guid.NewGuid().ToString(),
            EntityId = Guid.NewGuid().ToString(),
            EntityName = "New Application Server Import",
            EntityType = "Application",
            Status = "Initiated",
            StartTime = DateTime.Now,
            EndTime = DateTime.Now.AddHours(1),
            ErrorMessage = null
        };

        // Create command for updating BulkImportActionResult
        UpdateBulkImportActionResultCommand = new UpdateBulkImportActionResultCommand
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = Guid.NewGuid().ToString(),
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "Updated-Import-Node",
            BulkImportOperationId = Guid.NewGuid().ToString(),
            BulkImportOperationGroupId = Guid.NewGuid().ToString(),
            ConditionalOperation = 1,
            EntityId = Guid.NewGuid().ToString(),
            EntityName = "Updated Security Component Import",
            EntityType = "Security",
            Status = "Completed",
            StartTime = DateTime.Now.AddHours(-1),
            EndTime = DateTime.Now,
            ErrorMessage = null
        };
    }
}
