using ContinuityPatrol.Domain.ViewModels.PageWidgetModel;

namespace ContinuityPatrol.Application.Features.PageWidget.Queries.GetList;

public class GetPageWidgetListQueryHandler : IRequestHandler<GetPageWidgetListQuery, List<PageWidgetListVm>>
{
    private readonly IMapper _mapper;
    private readonly IPageWidgetRepository _pageWidgetRepository;

    public GetPageWidgetListQueryHandler(IMapper mapper, IPageWidgetRepository pageWidgetRepository)
    {
        _mapper = mapper;
        _pageWidgetRepository = pageWidgetRepository;
    }

    public async Task<List<PageWidgetListVm>> Handle(GetPageWidgetListQuery request,
        CancellationToken cancellationToken)
    {
        var pageWidgets = await _pageWidgetRepository.ListAllAsync();

        if (pageWidgets.Count <= 0) return new List<PageWidgetListVm>();

        return _mapper.Map<List<PageWidgetListVm>>(pageWidgets);
    }
}