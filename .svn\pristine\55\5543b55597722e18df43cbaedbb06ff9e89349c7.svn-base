﻿using AutoFixture;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetBusinessFunctionByBusinessServiceId;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetSitePropertiesByBusinessServiceId;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByServerId;
using ContinuityPatrol.Application.Features.InfraObject.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObject.Commands.Update;
using ContinuityPatrol.Application.Features.InfraObject.Events.PaginatedView;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByBusinessServiceId;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetReplicationMasterByInfraMasterName;
using ContinuityPatrol.Application.Features.Server.Queries.GetRoleType;
using ContinuityPatrol.Application.Features.Site.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;
using ContinuityPatrol.Domain.ViewModels.HacmpClusterModel;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Domain.ViewModels.InfraReplicationMappingModel;
using ContinuityPatrol.Domain.ViewModels.NodeModel;
using ContinuityPatrol.Domain.ViewModels.ReplicationModel;
using ContinuityPatrol.Domain.ViewModels.ServerModel;
using ContinuityPatrol.Domain.ViewModels.VeritasClusterModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class InfraObjectControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher=new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<ILogger<InfraObjectController>> _mockLogger = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private  InfraObjectController _controller;

        public InfraObjectControllerShould()
        {
           
            _controller = new InfraObjectController(_mockPublisher.Object, _mockMapper.Object, _mockLogger.Object, _mockDataProvider.Object);
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
           
        }

        [Fact]
        public void List_ReturnsViewResult_WithModel()
        {
            
            
            var infraObjects = new List<InfraObjectListVm>();
            var businessServiceNames = new List<BusinessServiceNameVm>();
            var databaseServiceNames = new List<DatabaseListVm>();
            var nodeNames = new List<NodeNameVm>();
            var replicationServiceNames = new List<ReplicationListVm>();
            var serverNames = new List<ServerNameVm>();
            var businessFunctionNames = new List<BusinessFunctionNameVm>();

            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectList()).ReturnsAsync(infraObjects);
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames()).ReturnsAsync(businessServiceNames);
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationList()).ReturnsAsync(replicationServiceNames);
            _mockDataProvider.Setup(dp => dp.Server.GetServerNames()).ReturnsAsync(serverNames);
            _mockDataProvider.Setup(dp => dp.Node.GetNodeNames()).ReturnsAsync(nodeNames);
            _mockDataProvider.Setup(dp => dp.Database.GetDatabaseList()).ReturnsAsync(databaseServiceNames);
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionPaginatedList(It.IsAny<GetBusinessFunctionPaginatedListQuery>()))
                .ReturnsAsync(new PaginatedResult<BusinessFunctionListVm>());

          
            var result =  _controller.List();
            Assert.NotNull(result);
            
        }

        [Fact]
        public async Task SaveOrUpdate_CreatesInfraObject_WhenIdIsEmpty()
        {
            
            var infraObject =  new AutoFixture.Fixture().Create<InfraObjectViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            _mockMapper.Setup(m => m.Map<CreateInfraObjectCommand>(infraObject)).Returns(new CreateInfraObjectCommand());
            _mockDataProvider.Setup(dp => dp.InfraObject.CreateAsync(It.IsAny<CreateInfraObjectCommand>()))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Created" });

            // Act
            var result = await _controller.SaveOrUpdate(infraObject) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task SaveOrUpdate_UpdatesInfraObject_WhenIdIsNotEmpty()
        {
            // Arrange
            var infraObject = new AutoFixture.Fixture().Create<InfraObjectViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "22");
            var collection = new FormCollection(dic);
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.Request.Form = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues> { { "Id", "123" } });
            _mockMapper.Setup(m => m.Map<UpdateInfraObjectCommand>(infraObject)).Returns(new UpdateInfraObjectCommand());
            _mockDataProvider.Setup(dp => dp.InfraObject.UpdateAsync(It.IsAny<UpdateInfraObjectCommand>()))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Updated" });

            // Act
            var result = await _controller.SaveOrUpdate(infraObject) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task GetBusinessFunctions_ReturnsJsonResult_WithBusinessFunctionNames()
        {
            // Arrange
            var id = "1";
            var businessFunctionNames = new List<GetBusinessFunctionNameByBusinessServiceIdVm>();
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionNamesByBusinessServiceId(id))
                .ReturnsAsync(businessFunctionNames);

            // Act
            var result = await _controller.GetBusinessFunctions(id) as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.NotNull(result);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetInfraObjectNames_ReturnsJsonResult_WithInfraObjectNames()
        {
            
            var infraObjectNames = new List<GetInfraObjectNameVm>();
            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectNames())
                .ReturnsAsync(infraObjectNames);

            
            var result = await _controller.GetInfraObjectNames() as JsonResult;

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.NotNull(result);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetReplicationMasterByInfraMasterName_ReturnsJsonResult()
        {
            
            var infraMasterName = "master";
            var replicationMasters = new List<GetByInfraMasterNameVm>();
            _mockDataProvider.Setup(dp => dp.ReplicationMaster.GetReplicationMasterByInfraMasterName(infraMasterName))
                .ReturnsAsync(replicationMasters);

            
            var result = await _controller.GetReplicationMasterByInfraMasterName(infraMasterName) as JsonResult;

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.NotNull(result);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetServerRoleTypeAndServerType_ReturnsJsonResult()
        {
            
            var roleType = "role";
            var serverType = "server";
            var serverRoles = new List<ServerRoleTypeVm>();
            _mockDataProvider.Setup(dp => dp.Server.GetByRoleTypeAndServerType(roleType, serverType))
                .ReturnsAsync(serverRoles);

            
            var result = await _controller.GetServerRoleTypeAndServerType(roleType, serverType) as JsonResult;

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.NotNull(result);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetDatabase_ReturnsJsonResult_WithDatabaseList()
        {
            
            var id = "1";
            var databaseList = new List<GetDatabaseByServerIdVm>();
            _mockDataProvider.Setup(dp => dp.Database.GetByServerId(id))
                .ReturnsAsync(databaseList);

            
            var result = await _controller.GetDatabase(id) as JsonResult;


            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.NotNull(result);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetReplicationList_ReturnsJsonResult_WithReplicationList()
        {
            
            var replicationList = new List<ReplicationListVm>();
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationList())
                .ReturnsAsync(replicationList);

            
            var result = await _controller.GetReplicationList() as JsonResult;

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.NotNull(result);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetInfraObjectByBusinessServiceId_ReturnsJsonResult()
        {
            
            var businessServiceId = "1";
            var infraObjects = new List<GetInfraObjectByBusinessServiceIdVm>();
            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectByBusinessServiceId(businessServiceId))
                .ReturnsAsync(infraObjects);

            
            var result = await _controller.GetInfraObjectByBusinessServiceId(businessServiceId) as JsonResult;

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.NotNull(result);
            //Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task IsInfraObjectNameExist_ReturnsBool()
        {
            
            var infraObjectName = "TestName";
            var id = "1";
            _mockDataProvider.Setup(dp => dp.InfraObject.IsInfraObjectNameExist(infraObjectName, id))
                .ReturnsAsync(true);

            
            var result = await _controller.IsInfraObjectNameExist(infraObjectName, id);

            
            Assert.True(result);
        }

        [Fact]
        public async Task Delete_ReturnsRedirectToActionResult()
        {
            // Arrange
            var id = "1";
            _mockDataProvider.Setup(dp => dp.InfraObject.DeleteAsync(id))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Deleted" });

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonResult_WithPaginatedList()
        {
            // Arrange
            var query = new GetInfraObjectPaginatedListQuery { 
                PageNumber = 1,
            };
            var paginatedList = new PaginatedResult<InfraObjectListVm>();
            _mockDataProvider.Setup(dp => dp.InfraObject.GetPaginatedInfraObjects(query))
                .ReturnsAsync(paginatedList);

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.NotNull(result);
            Assert.Contains("\"Succeeded\":false", json);
        }

        [Fact]
        public async Task GetDatabaseListByName_ReturnsJsonResult()
        {
            var result = new List<ComponentTypeModel>();
            _mockDataProvider.Setup(dp => dp.ComponentType.GetComponentTypeListByName("Database"))
                .ReturnsAsync(result);

            var jsonResult = await _controller.GetDatabaseListByName() as JsonResult;

           
            var resultObject = Assert.IsType<JsonResult>(jsonResult);
            var json = JsonConvert.SerializeObject(resultObject.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetTypeByDatabaseIdAndReplicationMasterId_ReturnsJsonResult()
        {
            
            var databaseId = "db1";
            var replicationMasterId = "rep1";
            var type = "type";
            var replicationNames = new List<InfraReplicationMappingListVm>();
            _mockDataProvider.Setup(dp => dp.InfraReplicationMapping.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type))
                .ReturnsAsync(replicationNames);

            
            var result = await _controller.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type) as JsonResult;

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetVeritasClusters_ReturnsJsonResult()
        {
            
            var veritasClusters = new List<VeritasClusterListVm>();
            _mockDataProvider.Setup(dp => dp.VeritasCluster.GetVeritasClusterList())
                .ReturnsAsync(veritasClusters);

            
            var result = await _controller.GetVeritasClusters() as JsonResult;

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetHACMPClusters_ReturnsJsonResult()
        {

            var hacmpClusters = new List<HacmpClusterListVm>();
            _mockDataProvider.Setup(dp => dp.HacmpCluster.GetHacmpClusterList())
                .ReturnsAsync(hacmpClusters);


            var result = await _controller.GetHACMPClusters() as JsonResult;


            var jsonResult = Assert.IsType<JsonResult>(result);

            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
        }

        // ===== CONSTRUCTOR TESTS =====

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new InfraObjectController(
                _mockPublisher.Object,
                _mockMapper.Object,
                _mockLogger.Object,
                _mockDataProvider.Object
            );

            // Assert
            Assert.NotNull(controller);
        }

        // ===== LIST METHOD ADDITIONAL TESTS =====

        [Fact]
        public async Task List_ShouldPublishInfraObjectPaginatedEvent()
        {
            // Arrange
            var infraObjects = new List<InfraObjectListVm>();
            var businessServiceNames = new List<BusinessServiceNameVm>();
            var databaseServiceNames = new List<DatabaseListVm>();
            var nodeNames = new List<NodeNameVm>();
            var replicationServiceNames = new List<ReplicationListVm>();
            var serverNames = new List<ServerNameVm>();
            var businessFunctionNames = new List<BusinessFunctionNameVm>();

            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectList()).ReturnsAsync(infraObjects);
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames()).ReturnsAsync(businessServiceNames);
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationList()).ReturnsAsync(replicationServiceNames);
            _mockDataProvider.Setup(dp => dp.Server.GetServerNames()).ReturnsAsync(serverNames);
            _mockDataProvider.Setup(dp => dp.Node.GetNodeNames()).ReturnsAsync(nodeNames);
            _mockDataProvider.Setup(dp => dp.Database.GetDatabaseList()).ReturnsAsync(databaseServiceNames);
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionPaginatedList(It.IsAny<GetBusinessFunctionPaginatedListQuery>()))
                .ReturnsAsync(new PaginatedResult<BusinessFunctionListVm>());

            // Act
            await _controller.List();

            // Assert
            _mockPublisher.Verify(p => p.Publish(It.IsAny<InfraObjectPaginatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task List_ShouldCallAllDataProviders()
        {
            // Arrange
            var infraObjects = new List<InfraObjectListVm>();
            var businessServiceNames = new List<BusinessServiceNameVm>();
            var databaseServiceNames = new List<DatabaseListVm>();
            var nodeNames = new List<NodeNameVm>();
            var replicationServiceNames = new List<ReplicationListVm>();
            var serverNames = new List<ServerNameVm>();

            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectList()).ReturnsAsync(infraObjects);
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames()).ReturnsAsync(businessServiceNames);
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationList()).ReturnsAsync(replicationServiceNames);
            _mockDataProvider.Setup(dp => dp.Server.GetServerNames()).ReturnsAsync(serverNames);
            _mockDataProvider.Setup(dp => dp.Node.GetNodeNames()).ReturnsAsync(nodeNames);
            _mockDataProvider.Setup(dp => dp.Database.GetDatabaseList()).ReturnsAsync(databaseServiceNames);
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionPaginatedList(It.IsAny<GetBusinessFunctionPaginatedListQuery>()))
                .ReturnsAsync(new PaginatedResult<BusinessFunctionListVm>());

            // Act
            await _controller.List();

            // Assert
            _mockDataProvider.Verify(dp => dp.InfraObject.GetInfraObjectList(), Times.Once);
            _mockDataProvider.Verify(dp => dp.BusinessService.GetBusinessServiceNames(), Times.Once);
            _mockDataProvider.Verify(dp => dp.Replication.GetReplicationList(), Times.Once);
            _mockDataProvider.Verify(dp => dp.Database.GetDatabaseList(), Times.Once);
            _mockDataProvider.Verify(dp => dp.BusinessFunction.GetBusinessFunctionPaginatedList(It.IsAny<GetBusinessFunctionPaginatedListQuery>()), Times.Once);
        }

        // ===== SAVEORUPDATE METHOD ADDITIONAL TESTS =====

        [Fact]
        public async Task SaveOrUpdate_ShouldHandleValidationException()
        {
            // Arrange
            var infraObject = new AutoFixture.Fixture().Create<InfraObjectViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Name", "Name is required"));
            var validationException = new ValidationException(validationResult);

            _mockMapper.Setup(m => m.Map<CreateInfraObjectCommand>(infraObject)).Throws(validationException);

            // Act
            var result = await _controller.SaveOrUpdate(infraObject) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task SaveOrUpdate_ShouldHandleGeneralException()
        {
            // Arrange
            var infraObject = new AutoFixture.Fixture().Create<InfraObjectViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var exception = new Exception("Database connection failed");
            _mockMapper.Setup(m => m.Map<CreateInfraObjectCommand>(infraObject)).Throws(exception);

            // Act
            var result = await _controller.SaveOrUpdate(infraObject) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        // ===== GETBUSINESSFUNCTIONS METHOD ADDITIONAL TESTS =====

        [Fact]
        public async Task GetBusinessFunctions_ShouldReturnErrorForNullId()
        {
            // Act
            var result = await _controller.GetBusinessFunctions(null) as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("Id is not valid format", json);
        }

        [Fact]
        public async Task GetBusinessFunctions_ShouldReturnErrorForEmptyId()
        {
            // Act
            var result = await _controller.GetBusinessFunctions("") as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("Id is not valid format", json);
        }

        [Fact]
        public async Task GetBusinessFunctions_ShouldHandleException()
        {
            // Arrange
            var id = "1";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionNamesByBusinessServiceId(id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetBusinessFunctions(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== EXCEPTION HANDLING TESTS FOR OTHER METHODS =====

        [Fact]
        public async Task GetInfraObjectNames_ShouldHandleException()
        {
            // Arrange
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectNames()).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetInfraObjectNames() as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetReplicationMasterByInfraMasterName_ShouldReturnErrorForNullName()
        {
            // Act
            var result = await _controller.GetReplicationMasterByInfraMasterName(null) as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":false", json);
        }

        [Fact]
        public async Task GetReplicationMasterByInfraMasterName_ShouldHandleException()
        {
            // Arrange
            var infraMasterName = "master";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.ReplicationMaster.GetReplicationMasterByInfraMasterName(infraMasterName)).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetReplicationMasterByInfraMasterName(infraMasterName) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetServerRoleTypeAndServerType_ShouldHandleException()
        {
            // Arrange
            var roleType = "role";
            var serverType = "server";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.Server.GetByRoleTypeAndServerType(roleType, serverType)).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetServerRoleTypeAndServerType(roleType, serverType) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetDatabase_ShouldReturnErrorForNullId()
        {
            // Act
            var result = await _controller.GetDatabase(null) as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("Id is not valid format", json);
        }

        [Fact]
        public async Task GetDatabase_ShouldHandleException()
        {
            // Arrange
            var id = "1";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.Database.GetByServerId(id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetDatabase(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetReplicationList_ShouldHandleException()
        {
            // Arrange
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationList()).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetReplicationList() as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetInfraObjectByBusinessServiceId_ShouldReturnErrorForNullId()
        {
            // Act
            var result = await _controller.GetInfraObjectByBusinessServiceId(null) as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetInfraObjectByBusinessServiceId_ShouldHandleException()
        {
            // Arrange
            var businessServiceId = "1";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectByBusinessServiceId(businessServiceId)).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetInfraObjectByBusinessServiceId(businessServiceId) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetServersByInfraObjectId_ReturnsJsonResult_WithServerData()
        {
            // Arrange
            var infraObjectId = "123";
            var serverData = new InfraObjectDetailVm
            {
                Id = "123",
                Name = "Test InfraObject",
                BusinessServiceId = "bs1",
                BusinessServiceName = "Test Business Service",
                BusinessFunctionId = "bf1",
                BusinessFunctionName = "Test Business Function",
                ServerProperties = "test server properties",
                DatabaseProperties = "test database properties",
                ReplicationProperties = "test replication properties"
            };

            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectById(infraObjectId))
                .ReturnsAsync(serverData);

            // Act
            var result = await _controller.GetServersByInfraObjectId(infraObjectId) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":", json);

            // Verify the service was called with correct parameter
            _mockDataProvider.Verify(dp => dp.InfraObject.GetInfraObjectById(infraObjectId), Times.Once);
        }

        [Fact]
        public async Task GetServersByInfraObjectId_WithNullData_ReturnsJsonResult()
        {
            // Arrange
            var infraObjectId = "123";
            InfraObjectDetailVm nullData = null;

            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectById(infraObjectId))
                .ReturnsAsync(nullData);

            // Act
            var result = await _controller.GetServersByInfraObjectId(infraObjectId) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":null", json);

            // Verify the service was called
            _mockDataProvider.Verify(dp => dp.InfraObject.GetInfraObjectById(infraObjectId), Times.Once);
        }

        [Fact]
        public async Task GetServersByInfraObjectId_WithEmptyId_ReturnsJsonResult()
        {
            // Arrange
            var infraObjectId = "";
            var serverData = new InfraObjectDetailVm();

            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectById(infraObjectId))
                .ReturnsAsync(serverData);

            // Act
            var result = await _controller.GetServersByInfraObjectId(infraObjectId) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);

            // Verify the service was called with empty string
            _mockDataProvider.Verify(dp => dp.InfraObject.GetInfraObjectById(infraObjectId), Times.Once);
        }

        [Fact]
        public async Task GetServersByInfraObjectId_LogsDebugMessages()
        {
            // Arrange
            var infraObjectId = "123";
            var serverData = new InfraObjectDetailVm { Id = "123", Name = "Test" };

            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectById(infraObjectId))
                .ReturnsAsync(serverData);

            // Act
            var result = await _controller.GetServersByInfraObjectId(infraObjectId) as JsonResult;

            // Assert
            Assert.NotNull(result);

            // Verify that the service was called (logging verification is complex with different Moq versions)
            _mockDataProvider.Verify(dp => dp.InfraObject.GetInfraObjectById(infraObjectId), Times.Once);
        }

        [Fact]
        public async Task GetServersByInfraObjectId_ShouldHandleException()
        {
            // Arrange
            var infraObjectId = "1";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectById(infraObjectId)).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetServersByInfraObjectId(infraObjectId) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task IsInfraObjectNameExist_ShouldReturnFalse_OnException()
        {
            // Arrange
            var infraObjectName = "TestName";
            var id = "1";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.InfraObject.IsInfraObjectNameExist(infraObjectName, id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.IsInfraObjectNameExist(infraObjectName, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task Delete_ShouldHandleException()
        {
            // Arrange
            var id = "1";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.InfraObject.DeleteAsync(id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task GetPagination_ShouldHandleException()
        {
            // Arrange
            var query = new GetInfraObjectPaginatedListQuery { PageNumber = 1 };
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.InfraObject.GetPaginatedInfraObjects(query)).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetDatabaseListByName_ShouldHandleException()
        {
            // Arrange
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.ComponentType.GetComponentTypeListByName("Database")).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetDatabaseListByName() as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetTypeByDatabaseIdAndReplicationMasterId_ShouldHandleException()
        {
            // Arrange
            var databaseId = "db1";
            var replicationMasterId = "rep1";
            var type = "type";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.InfraReplicationMapping.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type)).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetVeritasClusters_ShouldHandleException()
        {
            // Arrange
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.VeritasCluster.GetVeritasClusterList()).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetVeritasClusters() as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetHACMPClusters_ShouldHandleException()
        {
            // Arrange
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.HacmpCluster.GetHacmpClusterList()).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetHACMPClusters() as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== MISSING METHODS TESTS =====

        [Fact]
        public async Task SitePropertiesByBusinessService_ShouldReturnJsonResult_WithSiteDetails()
        {
            // Arrange
            var businessServiceId = "1";
            var siteDetails = new SitePropertiesByBusinessServiceIdVm
            {
                SiteProperties = "test properties",
                ReplicationStatus = new List<int> { 1, 2, 3 },
                DROperationStatus = new List<int> { 4, 5, 6 }
            };
            _mockDataProvider.Setup(dp => dp.DashboardView.GetSitePropertiesByBusinessServiceId(businessServiceId))
                .ReturnsAsync(siteDetails);

            // Act
            var result = await _controller.SitePropertiesByBusinessService(businessServiceId) as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task SitePropertiesByBusinessService_ShouldReturnErrorForNullId()
        {
            // Act
            var result = await _controller.SitePropertiesByBusinessService(null) as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("Id is not valid format", json);
        }

        [Fact]
        public async Task SitePropertiesByBusinessService_ShouldHandleException()
        {
            // Arrange
            var businessServiceId = "1";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.DashboardView.GetSitePropertiesByBusinessServiceId(businessServiceId)).ThrowsAsync(exception);

            // Act
            var result = await _controller.SitePropertiesByBusinessService(businessServiceId) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetSiteTypeDetails_ShouldReturnJsonResult_WithSiteList()
        {
            // Arrange
            var siteId = "1";
            var siteDetail = new SiteDetailVm
            {
                Id = "1",
                Name = "Test Site",
                LocationId = "loc1",
                Location = "Test Location",
                TypeId = "type1",
                Type = "Primary",
                Category = "Production",
                PlatformType = "Cloud",
                CompanyId = "comp1",
                CompanyName = "Test Company",
                Lat = "40.7128",
                Lng = "-74.0060",
                DataTemperature = "Normal"
            };
            _mockDataProvider.Setup(dp => dp.Site.GetSiteById(siteId))
                .ReturnsAsync(siteDetail);

            // Act
            var result = await _controller.GetSiteTypeDetails(siteId) as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetSiteTypeDetails_ShouldReturnErrorForNullId()
        {
            // Act
            var result = await _controller.GetSiteTypeDetails(null) as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("Id is not valid format", json);
        }

        [Fact]
        public async Task GetSiteTypeDetails_ShouldReturnErrorForEmptyId()
        {
            // Act
            var result = await _controller.GetSiteTypeDetails("") as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("Id is not valid format", json);
        }

        [Fact]
        public async Task GetSiteTypeDetails_ShouldHandleException()
        {
            // Arrange
            var siteId = "1";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.Site.GetSiteById(siteId)).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetSiteTypeDetails(siteId) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== SPECIFIC TESTS FOR MISSING CODE COVERAGE =====

        [Fact]
        public async Task List_ShouldTransformDatabaseServiceListCorrectly()
        {
            // Arrange
            var originalDatabaseList = new List<DatabaseListVm>
            {
                new DatabaseListVm { Id = "db1", Name = "Database1", DatabaseType = "SQL Server" },
                new DatabaseListVm { Id = "db2", Name = "Database2", DatabaseType = "Oracle" },
                new DatabaseListVm { Id = "db3", Name = "Database3", DatabaseType = "MySQL" }
            };

            var infraObjects = new List<InfraObjectListVm>();
            var businessServiceNames = new List<BusinessServiceNameVm>();
            var replicationServiceNames = new List<ReplicationListVm>();

            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectList()).ReturnsAsync(infraObjects);
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames()).ReturnsAsync(businessServiceNames);
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationList()).ReturnsAsync(replicationServiceNames);
            _mockDataProvider.Setup(dp => dp.Database.GetDatabaseList()).ReturnsAsync(originalDatabaseList);
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionPaginatedList(It.IsAny<GetBusinessFunctionPaginatedListQuery>()))
                .ReturnsAsync(new PaginatedResult<BusinessFunctionListVm>());

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            var model = Assert.IsType<InfraObjectViewModel>(result.Model);

            // Verify the database transformation
            Assert.NotNull(model.Database);
            Assert.Equal(3, model.Database.Count);

            // Verify each transformed database item
            Assert.Equal("db1", model.Database[0].Id);
            Assert.Equal("Database1", model.Database[0].Name);
            Assert.Equal("SQL Server", model.Database[0].DatabaseType);

            Assert.Equal("db2", model.Database[1].Id);
            Assert.Equal("Database2", model.Database[1].Name);
            Assert.Equal("Oracle", model.Database[1].DatabaseType);

            Assert.Equal("db3", model.Database[2].Id);
            Assert.Equal("Database3", model.Database[2].Name);
            Assert.Equal("MySQL", model.Database[2].DatabaseType);
        }

        [Fact]
        public async Task List_ShouldTransformReplicationServiceListCorrectly()
        {
            // Arrange
            var originalReplicationList = new List<ReplicationListVm>
            {
                new ReplicationListVm { Id = "rep1", Name = "Replication1", Type = "Master" },
                new ReplicationListVm { Id = "rep2", Name = "Replication2", Type = "Slave" },
                new ReplicationListVm { Id = "rep3", Name = "Replication3", Type = "Backup" }
            };

            var infraObjects = new List<InfraObjectListVm>();
            var businessServiceNames = new List<BusinessServiceNameVm>();
            var databaseServiceNames = new List<DatabaseListVm>();

            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectList()).ReturnsAsync(infraObjects);
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames()).ReturnsAsync(businessServiceNames);
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationList()).ReturnsAsync(originalReplicationList);
            _mockDataProvider.Setup(dp => dp.Database.GetDatabaseList()).ReturnsAsync(databaseServiceNames);
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionPaginatedList(It.IsAny<GetBusinessFunctionPaginatedListQuery>()))
                .ReturnsAsync(new PaginatedResult<BusinessFunctionListVm>());

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            var model = Assert.IsType<InfraObjectViewModel>(result.Model);

            // Verify the replication transformation
            Assert.NotNull(model.ReplicationNames);
            Assert.Equal(3, model.ReplicationNames.Count);

            // Verify each transformed replication item
            Assert.Equal("rep1", model.ReplicationNames[0].Id);
            Assert.Equal("Replication1", model.ReplicationNames[0].Name);
            Assert.Equal("Master", model.ReplicationNames[0].Type);

            Assert.Equal("rep2", model.ReplicationNames[1].Id);
            Assert.Equal("Replication2", model.ReplicationNames[1].Name);
            Assert.Equal("Slave", model.ReplicationNames[1].Type);

            Assert.Equal("rep3", model.ReplicationNames[2].Id);
            Assert.Equal("Replication3", model.ReplicationNames[2].Name);
            Assert.Equal("Backup", model.ReplicationNames[2].Type);
        }

        [Fact]
        public async Task List_ShouldHandleEmptyDatabaseServiceList()
        {
            // Arrange
            var emptyDatabaseList = new List<DatabaseListVm>();
            var infraObjects = new List<InfraObjectListVm>();
            var businessServiceNames = new List<BusinessServiceNameVm>();
            var replicationServiceNames = new List<ReplicationListVm>();

            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectList()).ReturnsAsync(infraObjects);
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames()).ReturnsAsync(businessServiceNames);
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationList()).ReturnsAsync(replicationServiceNames);
            _mockDataProvider.Setup(dp => dp.Database.GetDatabaseList()).ReturnsAsync(emptyDatabaseList);
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionPaginatedList(It.IsAny<GetBusinessFunctionPaginatedListQuery>()))
                .ReturnsAsync(new PaginatedResult<BusinessFunctionListVm>());

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            var model = Assert.IsType<InfraObjectViewModel>(result.Model);

            // Verify empty database list is handled correctly
            Assert.NotNull(model.Database);
            Assert.Empty(model.Database);
        }

        [Fact]
        public async Task List_ShouldHandleEmptyReplicationServiceList()
        {
            // Arrange
            var emptyReplicationList = new List<ReplicationListVm>();
            var infraObjects = new List<InfraObjectListVm>();
            var businessServiceNames = new List<BusinessServiceNameVm>();
            var databaseServiceNames = new List<DatabaseListVm>();

            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectList()).ReturnsAsync(infraObjects);
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames()).ReturnsAsync(businessServiceNames);
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationList()).ReturnsAsync(emptyReplicationList);
            _mockDataProvider.Setup(dp => dp.Database.GetDatabaseList()).ReturnsAsync(databaseServiceNames);
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionPaginatedList(It.IsAny<GetBusinessFunctionPaginatedListQuery>()))
                .ReturnsAsync(new PaginatedResult<BusinessFunctionListVm>());

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            var model = Assert.IsType<InfraObjectViewModel>(result.Model);

            // Verify empty replication list is handled correctly
            Assert.NotNull(model.ReplicationNames);
            Assert.Empty(model.ReplicationNames);
        }

        [Fact]
        public async Task List_ShouldTransformBothDatabaseAndReplicationListsSimultaneously()
        {
            // Arrange
            var databaseList = new List<DatabaseListVm>
            {
                new DatabaseListVm { Id = "db1", Name = "TestDB", DatabaseType = "PostgreSQL" }
            };

            var replicationList = new List<ReplicationListVm>
            {
                new ReplicationListVm { Id = "rep1", Name = "TestReplication", Type = "Primary" }
            };

            var infraObjects = new List<InfraObjectListVm>();
            var businessServiceNames = new List<BusinessServiceNameVm>();

            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectList()).ReturnsAsync(infraObjects);
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames()).ReturnsAsync(businessServiceNames);
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationList()).ReturnsAsync(replicationList);
            _mockDataProvider.Setup(dp => dp.Database.GetDatabaseList()).ReturnsAsync(databaseList);
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionPaginatedList(It.IsAny<GetBusinessFunctionPaginatedListQuery>()))
                .ReturnsAsync(new PaginatedResult<BusinessFunctionListVm>());

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            var model = Assert.IsType<InfraObjectViewModel>(result.Model);

            // Verify both transformations work together
            Assert.NotNull(model.Database);
            Assert.Single(model.Database);
            Assert.Equal("db1", model.Database[0].Id);
            Assert.Equal("TestDB", model.Database[0].Name);
            Assert.Equal("PostgreSQL", model.Database[0].DatabaseType);

            Assert.NotNull(model.ReplicationNames);
            Assert.Single(model.ReplicationNames);
            Assert.Equal("rep1", model.ReplicationNames[0].Id);
            Assert.Equal("TestReplication", model.ReplicationNames[0].Name);
            Assert.Equal("Primary", model.ReplicationNames[0].Type);
        }

        [Fact]
        public async Task List_ShouldCreateNewInstancesInTransformation()
        {
            // Arrange
            var originalDatabase = new DatabaseListVm { Id = "db1", Name = "Original", DatabaseType = "SQL" };
            var originalReplication = new ReplicationListVm { Id = "rep1", Name = "Original", Type = "Master" };

            var databaseList = new List<DatabaseListVm> { originalDatabase };
            var replicationList = new List<ReplicationListVm> { originalReplication };

            var infraObjects = new List<InfraObjectListVm>();
            var businessServiceNames = new List<BusinessServiceNameVm>();

            _mockDataProvider.Setup(dp => dp.InfraObject.GetInfraObjectList()).ReturnsAsync(infraObjects);
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames()).ReturnsAsync(businessServiceNames);
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationList()).ReturnsAsync(replicationList);
            _mockDataProvider.Setup(dp => dp.Database.GetDatabaseList()).ReturnsAsync(databaseList);
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionPaginatedList(It.IsAny<GetBusinessFunctionPaginatedListQuery>()))
                .ReturnsAsync(new PaginatedResult<BusinessFunctionListVm>());

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            var model = Assert.IsType<InfraObjectViewModel>(result.Model);

            // Verify new instances are created (not same reference)
            Assert.NotSame(originalDatabase, model.Database[0]);
            Assert.NotSame(originalReplication, model.ReplicationNames[0]);

            // But values should be copied correctly
            Assert.Equal(originalDatabase.Id, model.Database[0].Id);
            Assert.Equal(originalDatabase.Name, model.Database[0].Name);
            Assert.Equal(originalDatabase.DatabaseType, model.Database[0].DatabaseType);

            Assert.Equal(originalReplication.Id, model.ReplicationNames[0].Id);
            Assert.Equal(originalReplication.Name, model.ReplicationNames[0].Name);
            Assert.Equal(originalReplication.Type, model.ReplicationNames[0].Type);
        }
    }
}
