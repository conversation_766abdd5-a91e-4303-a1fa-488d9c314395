using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowTemp.Events.Delete;

public class WorkflowTempDeletedEventHandler : INotificationHandler<WorkflowTempDeletedEvent>
{
    private readonly ILogger<WorkflowTempDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowTempDeletedEventHandler(ILoggedInUserService userService, ILogger<WorkflowTempDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowTempDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} WorkflowTemp",
            Entity = "WorkflowTemp",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"WorkflowTemp '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"WorkflowTemp '{deletedEvent.Name}' deleted successfully.");
    }
}
