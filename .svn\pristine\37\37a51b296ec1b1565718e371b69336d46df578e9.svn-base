﻿using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyExecutionReport;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using DevExpress.XtraCharts;
using Newtonsoft.Json;
using System.Data;
using System.Drawing;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    [SupportedOSPlatform("windows")]
    public partial class DRReadinessLog : DevExpress.XtraReports.UI.XtraReport
    {
        private readonly ILogger<PreBuildReportController> _logger;
        public  DRReadyExecutionReport drReadyExecutionReport = new DRReadyExecutionReport();
        public DRReadinessLog(string data)
        {
            try
            {               
                drReadyExecutionReport = JsonConvert.DeserializeObject<DRReadyExecutionReport>(data);                 
                _logger = PreBuildReportController._logger;
                var report = drReadyExecutionReport.DRReadyExecutionReportVm;
                var businessServiceName = drReadyExecutionReport.DRReadyExecutionReportDataName;
                report.ForEach(x => x.DRReady = string.IsNullOrEmpty(x.DRReady) ? "NA" : (x.DRReady == "0" ? "No" : (x.DRReady == "1" ? "Yes" : x.DRReady)));
                report.ForEach(x => x.ErrorMessage = string.IsNullOrEmpty(x.ErrorMessage) || string.IsNullOrWhiteSpace(x.ErrorMessage) ? x.ErrorMessage = "" : x.ErrorMessage);
                report.ForEach(x => x.ErrorMessage = x.DRReady == "Yes" ? "Success" : x.ErrorMessage);
                var bsName = report.Where(x => !string.IsNullOrEmpty(x.BusinessServiceName)).Select(x => x.BusinessServiceName).Distinct().Count();
                var bfName = report.Where(x => !string.IsNullOrEmpty(x.BusinessFunctionName)).Select(x => x.BusinessFunctionName).Distinct().Count();
                var infraName = report.Where(x => !string.IsNullOrEmpty(x.InfraObjectName)).Select(x => x.InfraObjectName).Distinct().Count();
                var HealthUP = report.Where(x => x.DRReady == "1" || x.DRReady == "Yes").Count();
                var HealthDown = report.Where(x => x.DRReady == "0" || x.DRReady == "No").Count();

                InitializeComponent();
                ClientCompanyLogo();
                this.DisplayName = "Resiliency Readiness Execution Log Report_" + DateTime.Now.ToString("ddMMyyyy" + "_" + "hhmmsstt");

                tableCell8.BeforePrint += tableCell_SerialNumber_BeforePrint;
                var startDate = drReadyExecutionReport.ActiveStartDate.ToString();
                var endDate = drReadyExecutionReport.ActiveEndDate.ToString();
                xrLblFromDate.Text = startDate.ToDateTime().ToString("dd-MM-yyyy");
                xrLblEndDate.Text = endDate.ToDateTime().ToString("dd-MM-yyyy");
                xrLblYes.Text = HealthUP.ToString("D2");
                xrLblNo.Text = HealthDown.ToString("D2");
                xrLblBsCount.Text = bsName.ToString("D2");
                xrLblBfCount.Text = bfName.ToString("D2");
                xrLblInfraCount.Text = infraName.ToString("D2");

                lblBsName.Text = businessServiceName;
                DataSource = report;
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Resiliency Readiness Execution Log Report. The error message : " + ex.Message); throw; }
        }

        int serialNumber =1;

        private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
        {
            XRTableCell cell = (XRTableCell)sender;

            cell.Text = serialNumber.ToString();
            serialNumber++;
        }

        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                _username.Text = "Report Generated By: " + drReadyExecutionReport.ReportGeneratedBy.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Resiliency Readiness Execution Log Report's User name. The error message : " + ex.Message); throw; }
        }


        private void xrChart1_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var report = drReadyExecutionReport.DRReadyExecutionReportVm;
                var HealthUP = report.Where(x => x.DRReady == "1" || x.DRReady == "Yes").Count();
                var HealthDown = report.Where(x => x.DRReady == "0" || x.DRReady == "No").Count();
                Int64 NodataFound = 0;

                if (HealthUP == 0 && HealthDown == 0)
                {
                    Series series1 = new Series("Series1", ViewType.Doughnut);
                    xrChart1.Series.Add(series1);
                    NodataFound = 1;
                    series1.DataSource = CreateChartData(HealthUP, HealthDown, NodataFound);
                    DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                    doughnutSeriesView.MinAllowedSizePercentage = 70D;
                    series1.View = doughnutSeriesView;
                    series1.ArgumentScaleType = ScaleType.Auto;
                    series1.ArgumentDataMember = "Argument";
                    series1.ValueScaleType = ScaleType.Numerical;
                    series1.ValueDataMembers.AddRange(new string[] { "Value" });
                    series1.Label.TextPattern = "{A}";
                    series1.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                    xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
                }
                else
                {
                    Series series = new Series("Series1", ViewType.Doughnut);
                    xrChart1.Series.Add(series);
                    series.DataSource = CreateChartData(HealthUP, HealthDown, NodataFound);
                    DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                    doughnutSeriesView.MinAllowedSizePercentage = 70D;
                    series.View = doughnutSeriesView;
                    series.ArgumentScaleType = ScaleType.Auto;
                    series.ArgumentDataMember = "Argument";
                    series.ValueScaleType = ScaleType.Numerical;
                    series.ValueDataMembers.AddRange(new string[] { "Value" });
                    series.Label.TextPattern = "{A}\n{V}";
                    // series.Label.LineVisibility = DevExpress.Utils.DefaultBoolean.False;
                    // series.Label.TextColor = Color.White;
                    series.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                    xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
                }
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Resiliency Readiness Execution Log Report's Chart. The error message : " + ex.Message); throw; }
        }
        private DataTable CreateChartData(Int64 HealthUP, Int64 HealthDown, Int64 NodataFound)
        {
            DataTable table = new DataTable("Table1");
            table.Columns.Add("Argument", typeof(string));
            table.Columns.Add("Value", typeof(Int64));
            Random rnd = new Random();
            table.Rows.Add("Yes", HealthUP);
            table.Rows.Add("No",HealthDown);
            table.Rows.Add("No Data Found", NodataFound);

            return table;
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Resiliency Readiness Execution Log Report's CP Version. The error message : " + ex.Message); throw; }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the customer logo in Resiliency Readiness Execution Log Report" + ex.Message.ToString());
            }
        }

    }
}
