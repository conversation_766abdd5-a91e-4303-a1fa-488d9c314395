﻿namespace ContinuityPatrol.Application.Features.SVCMssqlMonitorLog.Commands;

public class
    CreateSVCMssqlMonitorLogCommandHandler : IRequestHandler<CreateSVCMssqlMonitorLogCommand,
        CreateSVCMssqlMonitorLogResponse>
{
    private readonly IMapper _mapper;
    private readonly ISVCMssqlMonitorLogRepository _svcMssqlMonitorLogRepository;

    public CreateSVCMssqlMonitorLogCommandHandler(IMapper mapper,
        ISVCMssqlMonitorLogRepository svcMssqlMonitorLogRepository)
    {
        _mapper = mapper;
        _svcMssqlMonitorLogRepository = svcMssqlMonitorLogRepository;
    }

    public async Task<CreateSVCMssqlMonitorLogResponse> Handle(CreateSVCMssqlMonitorLogCommand request,
        CancellationToken cancellationToken)
    {
        var svcMssqlMonitorLog = _mapper.Map<Domain.Entities.SVCMssqlMonitorLog>(request);

        svcMssqlMonitorLog = await _svcMssqlMonitorLogRepository.AddAsync(svcMssqlMonitorLog);

        var response = new CreateSVCMssqlMonitorLogResponse
        {
            Message = Message.Create(nameof(Domain.Entities.SVCMssqlMonitorLog), svcMssqlMonitorLog.ReferenceId),
            Id = svcMssqlMonitorLog.ReferenceId
        };

        return response;
    }
}