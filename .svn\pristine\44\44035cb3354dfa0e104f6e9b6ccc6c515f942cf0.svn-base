﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class ImpactActivityRepositoryMocks
{
    public static Mock<IImpactActivityRepository> CreateImpactActivityRepository(List<ImpactActivity> impactActivities)
    {
        var impactActivityRepository = new Mock<IImpactActivityRepository>();

        impactActivityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(impactActivities);

        impactActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<ImpactActivity>())).ReturnsAsync(
            (ImpactActivity impactActivity) =>
            {
                impactActivity.Id = new Fixture().Create<int>();

                impactActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                impactActivities.Add(impactActivity);

                return impactActivity;
            });
        return impactActivityRepository;
    }

    public static Mock<IImpactActivityRepository> DeleteImpactActivityRepository(List<ImpactActivity> impactActivities)
    {
        var impactActivityRepository = new Mock<IImpactActivityRepository>();

        impactActivityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(impactActivities);

        impactActivityRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => impactActivities.SingleOrDefault(x => x.ReferenceId == i));

        impactActivityRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ImpactActivity>())).ReturnsAsync((ImpactActivity impactActivity) =>
        {
            var index = impactActivities.FindIndex(item => item.ReferenceId == impactActivity.ReferenceId);

            impactActivity.IsActive = false;

            impactActivities[index] = impactActivity;

            return impactActivity;
        });
        return impactActivityRepository;
    }
    public static Mock<IImpactActivityRepository> UpdateImpactActivityRepository(List<ImpactActivity> impactActivities)
    {
        var impactActivityRepository = new Mock<IImpactActivityRepository>();

        impactActivityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(impactActivities);

        impactActivityRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => impactActivities.SingleOrDefault(x => x.ReferenceId == i));

        impactActivityRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ImpactActivity>())).ReturnsAsync((ImpactActivity impactActivity) =>
        {
            var index = impactActivities.FindIndex(item => item.ReferenceId == impactActivity.ReferenceId);

            impactActivities[index] = impactActivity;

            return impactActivity;

        });
        return impactActivityRepository;
    }
    public static Mock<IImpactActivityRepository> GetImpactActivityRepository(List<ImpactActivity> impactActivities)
    {
        var impactActivityRepository = new Mock<IImpactActivityRepository>();

        impactActivityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(impactActivities);

        impactActivityRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => impactActivities.SingleOrDefault(x => x.ReferenceId == i));

        return impactActivityRepository;
    }

    public static Mock<IImpactActivityRepository> GetImpactActivityEmptyRepository()
    {
        var impactActivityRepository = new Mock<IImpactActivityRepository>();

        impactActivityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<ImpactActivity>());

        return impactActivityRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateImpactActivityEventRepository(List<UserActivity> userActivities)
    {
        var impactActivityRepository = new Mock<IUserActivityRepository>();

        impactActivityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        impactActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return impactActivityRepository;
    }
}