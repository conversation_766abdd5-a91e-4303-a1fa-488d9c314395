﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Settings;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Shared.Tests.Helper;

public class ControllerTestBuilder<TController> where TController : CommonBaseController
{
    public TController CreateController(
        Func<IServiceProvider, TController> controllerFactory,
        out Mock<IMediator> mediatorMock, Mock<IMapper>? mapperOverride = null)
    {
        mediatorMock = new Mock<IMediator>();
        var loggerMock = new Mock<ILogger<CommonBaseController>>();
        var userServiceMock = new Mock<ILoggedInUserService>();
        userServiceMock.Setup(u => u.CompanyId).Returns(Guid.NewGuid().ToString);

        var cacheInstance = new CachingService();
        var mapperMock = mapperOverride ?? new Mock<IMapper>();

        // Setup cache settings mock
        var cacheSettingsMock = new Mock<IOptions<CacheSettings>>();
        cacheSettingsMock.Setup(x => x.Value).Returns(new CacheSettings { SlidingExpiration = 2 });

        var serviceProviderMock = new Mock<IServiceProvider>();
        serviceProviderMock.Setup(x => x.GetService(typeof(IMediator))).Returns(mediatorMock.Object);
        serviceProviderMock.Setup(x => x.GetService(typeof(ILogger<CommonBaseController>))).Returns(loggerMock.Object);
        serviceProviderMock.Setup(x => x.GetService(typeof(ILoggedInUserService))).Returns(userServiceMock.Object);
        serviceProviderMock.Setup(x => x.GetService(typeof(IAppCache))).Returns(cacheInstance);
        serviceProviderMock.Setup(x => x.GetService(typeof(IMapper))).Returns(mapperMock.Object);
        serviceProviderMock.Setup(x => x.GetService(typeof(IOptions<CacheSettings>))).Returns(cacheSettingsMock.Object);

        var httpContext = new DefaultHttpContext
        {
            RequestServices = serviceProviderMock.Object
        };

        var controllerContext = new ControllerContext
        {
            HttpContext = httpContext
        };

        var controller = controllerFactory(serviceProviderMock.Object);
        controller.ControllerContext = controllerContext;
        return controller;
    }
}