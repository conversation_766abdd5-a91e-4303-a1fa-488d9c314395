using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class JobFixture : IDisposable
{
    public List<Job> JobPaginationList { get; set; }
    public List<Job> JobList { get; set; }
    public Job JobDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public JobFixture()
    {
        var fixture = new Fixture();

        JobList = fixture.Create<List<Job>>();

        JobPaginationList = fixture.CreateMany<Job>(20).ToList();

        JobPaginationList.ForEach(x => x.CompanyId = CompanyId);

        JobList.ForEach(x => x.CompanyId = CompanyId);

        JobDto = fixture.Create<Job>();

        JobDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
