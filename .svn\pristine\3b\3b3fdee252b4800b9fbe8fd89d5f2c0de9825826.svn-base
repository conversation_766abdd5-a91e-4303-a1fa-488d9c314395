using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class BulkImportOperationGroupRepositoryTests : IClassFixture<BulkImportOperationGroupFixture>
{
    private readonly BulkImportOperationGroupFixture _bulkImportOperationGroupFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly BulkImportOperationGroupRepository _repository;

    public BulkImportOperationGroupRepositoryTests(BulkImportOperationGroupFixture bulkImportOperationGroupFixture)
    {
        _bulkImportOperationGroupFixture = bulkImportOperationGroupFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new BulkImportOperationGroupRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var bulkImportOperationGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroupDto;

        // Act
        var result = await _repository.AddAsync(bulkImportOperationGroup);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(bulkImportOperationGroup.BulkImportOperationId, result.BulkImportOperationId);
        Assert.Equal(bulkImportOperationGroup.InfraObjectName, result.InfraObjectName);
        Assert.Single(_dbContext.BulkImportOperationGroups);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var bulkImportOperationGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroupDto;
        await _repository.AddAsync(bulkImportOperationGroup);

        bulkImportOperationGroup.InfraObjectName = "UpdatedInfraObjectName";
        bulkImportOperationGroup.Status = "UpdatedStatus";
        bulkImportOperationGroup.ProgressStatus = "UpdatedProgressStatus";

        // Act
        var result = await _repository.UpdateAsync(bulkImportOperationGroup);

        // Assert
        Assert.Equal("UpdatedInfraObjectName", result.InfraObjectName);
        Assert.Equal("UpdatedStatus", result.Status);
        Assert.Equal("UpdatedProgressStatus", result.ProgressStatus);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var bulkImportOperationGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroupDto;
        await _repository.AddAsync(bulkImportOperationGroup);

        // Act
        var result = await _repository.DeleteAsync(bulkImportOperationGroup);

        // Assert
        Assert.Equal(bulkImportOperationGroup.BulkImportOperationId, result.BulkImportOperationId);
        Assert.Empty(_dbContext.BulkImportOperationGroups);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var bulkImportOperationGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroupDto;
        var addedEntity = await _repository.AddAsync(bulkImportOperationGroup);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.BulkImportOperationId, result.BulkImportOperationId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var bulkImportOperationGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroupDto;
        await _repository.AddAsync(bulkImportOperationGroup);

        // Act
        var result = await _repository.GetByReferenceIdAsync(bulkImportOperationGroup.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(bulkImportOperationGroup.ReferenceId, result.ReferenceId);
        Assert.Equal(bulkImportOperationGroup.BulkImportOperationId, result.BulkImportOperationId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var bulkImportOperationGroups = _bulkImportOperationGroupFixture.BulkImportOperationGroupList;
        await _repository.AddRangeAsync(bulkImportOperationGroups);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(bulkImportOperationGroups.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetBulkImportOperationGroupByBulkImportOperationIds Tests

    [Fact]
    public async Task GetBulkImportOperationGroupByBulkImportOperationIds_ShouldReturnMatchingGroups()
    {
        // Arrange
        var operationIds = new List<string> { "OPERATION_001", "OPERATION_002" };
        
        var operationGroups = new List<BulkImportOperationGroup>
        {
            new BulkImportOperationGroup 
            { 
                BulkImportOperationId = "OPERATION_001", 
                InfraObjectName = "Infra1",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportOperationGroupFixture.CompanyId,
                IsActive = true
            },
            new BulkImportOperationGroup 
            { 
                BulkImportOperationId = "OPERATION_002", 
                InfraObjectName = "Infra2",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportOperationGroupFixture.CompanyId,
                IsActive = true
            },
            new BulkImportOperationGroup 
            { 
                BulkImportOperationId = "OPERATION_003", 
                InfraObjectName = "Infra3",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportOperationGroupFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(operationGroups);

        // Act
        var result = await _repository.GetBulkImportOperationGroupByBulkImportOperationIds(operationIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains(x.BulkImportOperationId, operationIds));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetBulkImportOperationGroupByBulkImportOperationIds_ShouldReturnEmpty_WhenNoMatch()
    {
        // Arrange
        var operationGroups = _bulkImportOperationGroupFixture.BulkImportOperationGroupList;
        await _repository.AddRangeAsync(operationGroups);

        var nonExistentOperationIds = new List<string> { "NON_EXISTENT_001", "NON_EXISTENT_002" };

        // Act
        var result = await _repository.GetBulkImportOperationGroupByBulkImportOperationIds(nonExistentOperationIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetBulkImportOperationGroupByBulkImportOperationId Tests

    [Fact]
    public async Task GetBulkImportOperationGroupByBulkImportOperationId_ShouldReturnMatchingGroups()
    {
        // Arrange
        var operationId = "OPERATION_001";
        
        var operationGroups = new List<BulkImportOperationGroup>
        {
            new BulkImportOperationGroup 
            { 
                BulkImportOperationId = operationId, 
                InfraObjectName = "Infra1",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportOperationGroupFixture.CompanyId,
                IsActive = true
            },
            new BulkImportOperationGroup 
            { 
                BulkImportOperationId = operationId, 
                InfraObjectName = "Infra2",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportOperationGroupFixture.CompanyId,
                IsActive = true
            },
            new BulkImportOperationGroup 
            { 
                BulkImportOperationId = "OPERATION_002", 
                InfraObjectName = "Infra3",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportOperationGroupFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(operationGroups);

        // Act
        var result = await _repository.GetBulkImportOperationGroupByBulkImportOperationId(operationId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(operationId, x.BulkImportOperationId));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetBulkImportOperationGroupByBulkImportOperationId_ShouldReturnEmpty_WhenNoMatch()
    {
        // Arrange
        var operationGroups = _bulkImportOperationGroupFixture.BulkImportOperationGroupList;
        await _repository.AddRangeAsync(operationGroups);

        // Act
        var result = await _repository.GetBulkImportOperationGroupByBulkImportOperationId("NON_EXISTENT_OPERATION");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var operationGroups = _bulkImportOperationGroupFixture.BulkImportOperationGroupList;
        var operationGroup1 = operationGroups[0];
        var operationGroup2 = operationGroups[1];

        // Act
        var task1 = _repository.AddAsync(operationGroup1);
        var task2 = _repository.AddAsync(operationGroup2);

        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.BulkImportOperationGroups.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var operationGroups = _bulkImportOperationGroupFixture.BulkImportOperationGroupList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(operationGroups);
        var initialCount = operationGroups.Count;

        var toUpdate = operationGroups.Take(2).ToList();
        toUpdate.ForEach(x => x.Status = "Updated");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = operationGroups.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Status == "Updated").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleStatusFiltering()
    {
        // Arrange
        var operationGroups = new List<BulkImportOperationGroup>
        {
            new BulkImportOperationGroup
            {
                BulkImportOperationId = "OP1",
                Status = "Running",
                InfraObjectName = "Infra1",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportOperationGroupFixture.CompanyId,
                IsActive = true
            },
            new BulkImportOperationGroup
            {
                BulkImportOperationId = "OP2",
                Status = "Completed",
                InfraObjectName = "Infra2",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportOperationGroupFixture.CompanyId,
                IsActive = true
            },
            new BulkImportOperationGroup
            {
                BulkImportOperationId = "OP3",
                Status = "Running",
                InfraObjectName = "Infra3",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportOperationGroupFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(operationGroups);

        // Act
        var runningGroups = await _repository.FindByFilterAsync(x => x.Status == "Running");
        var completedGroups = await _repository.FindByFilterAsync(x => x.Status == "Completed");

        // Assert
        Assert.Equal(2, runningGroups.Count);
        Assert.Single(completedGroups);
        Assert.All(runningGroups, x => Assert.Equal("Running", x.Status));
        Assert.All(completedGroups, x => Assert.Equal("Completed", x.Status));
    }

    [Fact]
    public async Task Repository_ShouldHandleEmptyListParameters()
    {
        // Arrange
        var operationGroups = _bulkImportOperationGroupFixture.BulkImportOperationGroupList;
        await _repository.AddRangeAsync(operationGroups);

        // Act
        var result = await _repository.GetBulkImportOperationGroupByBulkImportOperationIds(new List<string>());

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task Repository_ShouldHandleNullListParameters()
    {
      var res=await _repository.GetBulkImportOperationGroupByBulkImportOperationIds(null);

        // Assert
        Assert.NotNull(res);
        Assert.Empty(res);
    }

    #endregion
}
