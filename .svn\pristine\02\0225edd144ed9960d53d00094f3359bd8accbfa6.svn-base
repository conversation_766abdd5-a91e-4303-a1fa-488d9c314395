using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class IsvcgmMonitorLogRepositoryTests : IClassFixture<IsvcgmMonitorLogFixture>, IDisposable
{
    private readonly IsvcgmMonitorLogFixture _isvcgmMonitorLogFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly IsvcgmMonitorLogRepository _repository;
    private readonly Mock<IConfiguration> _mockConfiguration;

    public IsvcgmMonitorLogRepositoryTests(IsvcgmMonitorLogFixture isvcgmMonitorLogFixture)
    {
        _isvcgmMonitorLogFixture = isvcgmMonitorLogFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockConfiguration = new Mock<IConfiguration>();

        //// Setup mock configuration
        //_mockConfiguration.Setup(x => x.GetConnectionString("Default")).Returns("test-connection-string");
        //_mockConfiguration.Setup(x => x.GetConnectionString("DBProvider")).Returns("test-db-provider");

        _repository = new IsvcgmMonitorLogRepository(_dbContext, _mockConfiguration.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.SVcgmMonitorLogs.RemoveRange(_dbContext.SVcgmMonitorLogs);
        await _dbContext.SaveChangesAsync();
    }

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ReturnsMatchingLogs_WhenTypeExists()
    {
        // Arrange
        await ClearDatabase();
        var type = "Error";

        var svcgmMonitorLogs = new List<SVCGMMonitorLog>
        {
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = type,
                //Message = "Error message 1",
                //LogLevel = "Error",
                //Timestamp = DateTime.UtcNow,
                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = type,
                //Message = "Error message 2",
                //LogLevel = "Error",
                //Timestamp = DateTime.UtcNow,
                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Warning", // Different type
                //Message = "Warning message",
                //LogLevel = "Warning",
                //Timestamp = DateTime.UtcNow,
                IsActive = true
            }
        };

        await _dbContext.SVcgmMonitorLogs.AddRangeAsync(svcgmMonitorLogs);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(type);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, log =>
        {
            Assert.Equal(type, log.Type);
            Assert.True(log.IsActive);
        });
    }

    [Fact]
    public async Task GetDetailByType_ReturnsEmpty_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentType = "NonExistentType";

        var svcgmMonitorLog = new SVCGMMonitorLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "Error",
            //Message = "Error message",
            //LogLevel = "Error",
            //Timestamp = DateTime.UtcNow,
            IsActive = true
        };

        await _dbContext.SVcgmMonitorLogs.AddAsync(svcgmMonitorLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(nonExistentType);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ExcludesInactiveLogs()
    {
        // Arrange
        await ClearDatabase();
        var type = "Error";

        var svcgmMonitorLogs = new List<SVCGMMonitorLog>
        {
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = type,

                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = type,

                IsActive = false // Inactive
            }
        };

        await _dbContext.SVcgmMonitorLogs.AddRangeAsync(svcgmMonitorLogs);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType(type);

        // Assert
        Assert.Single(result);
        Assert.True(result.First().IsActive);
    }

    [Fact]
    public async Task GetDetailByType_IsCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var type = "Error";

        var svcgmMonitorLog = new SVCGMMonitorLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = type,
            //Message = "Error message",
            //LogLevel = "Error",
            //Timestamp = DateTime.UtcNow,
            IsActive = true
        };

        await _dbContext.SVcgmMonitorLogs.AddAsync(svcgmMonitorLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var resultExactCase = await _repository.GetDetailByType("Error");
        var resultDifferentCase = await _repository.GetDetailByType("error");

        // Assert
        Assert.Single(resultExactCase); // Exact case should match
        Assert.Empty(resultDifferentCase); // Different case should not match (case sensitive)
    }

    #endregion

    #region GetSvcgmMonitorLogsByInfraObjectId Tests

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ExecutesWithoutError()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_123";
        var startDate = DateTime.UtcNow.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.ToString("yyyy-MM-dd");

        // Act & Assert - Should not throw exception
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        // Note: This method has complex database operations that are difficult to test in unit tests
        // The main goal is to ensure it executes without throwing exceptions
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_HandlesNullInfraObjectId()
    {
        // Arrange
        await ClearDatabase();
        var mockConfig = new Mock<IConfiguration>();
        mockConfig.Setup(c => c.GetConnectionString("Default")).Returns("+FEh/9O8KeNXwWTbCQrAwrjs9CtB1If4Bv9hVZR7cUhD4jDXZhuFtfO/dKonNu2wSXTFaZCDmHMJhF1P1ZhYU5BFlnl+KUwMUdaJFtpH81+0IEMZ/aTkvnqxLAXD1DhNpHF8MF0eccC/6ZQfQ9cciCHvK6EHeqwiUSiNVH2ZvxK0+45C/K+Lsye887UEdxiFdK/AqQ5Tbs6gxF8+Fu5PrasWfECOfSAdBGLuwrpDfleKWp28cQYhPh+/NXKAR6qVvCLhPkkOCbOn/e0DpuZZc3hiGiS6MFjuNTPmxaIeav7p8C86jkIqizxAXO3ILS6aL7fcXHmxddSSOs7IqF8kf6qQEphsthu9/LcwXH9/IQKHyCngte");
        mockConfig.Setup(c => c.GetConnectionString("DBProvider")).Returns("oracle"); // or whatever you're using

        var startDate = DateTime.UtcNow.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.ToString("yyyy-MM-dd");

        // Act & Assert - Should not throw exception
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(null, startDate, endDate);

        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_HandlesInvalidDateFormat()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_123";
        var invalidStartDate = "invalid-date";
        var invalidEndDate = "invalid-date";

        // Act & Assert - Should handle gracefully or throw expected exception
        try
        {
            var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, invalidStartDate, invalidEndDate);
            Assert.NotNull(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for invalid date format
            Assert.True(ex is FormatException || ex is ArgumentException || ex is InvalidOperationException);
        }
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_AddsSVCGMMonitorLogSuccessfully()
    {
        // Arrange
        await ClearDatabase();
        var svcgmMonitorLog = new SVCGMMonitorLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "Test",
            //Message = "Test message for add",
            //LogLevel = "Info",
            //Timestamp = DateTime.UtcNow,
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(svcgmMonitorLog);

        // Assert
        Assert.NotNull(result);
        //Assert.Equal("Test message for add", result.Message);

        //var savedEntity = await _dbContext.SVcgmMonitorLogs
        //    .FirstOrDefaultAsync(x => x.Message == "Test message for add");
        //Assert.NotNull(savedEntity);
    }

    [Fact]
    public async Task UpdateAsync_UpdatesSVCGMMonitorLogSuccessfully()
    {
        // Arrange
        await ClearDatabase();
        var svcgmMonitorLog = new SVCGMMonitorLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "Test",
            //Message = "Original message",
            //LogLevel = "Info",
            //Timestamp = DateTime.UtcNow,
            IsActive = true
        };

        await _dbContext.SVcgmMonitorLogs.AddAsync(svcgmMonitorLog);
        await _dbContext.SaveChangesAsync();

        // Modify the entity
        //svcgmMonitorLog.Message = "Updated message";
        //svcgmMonitorLog.LogLevel = "Warning";

        // Act
        var result = await _repository.UpdateAsync(svcgmMonitorLog);

        // Assert
        Assert.NotNull(result);
        //Assert.Equal("Updated message", result.Message);
        //Assert.Equal("Warning", result.LogLevel);
    }

    [Fact]
    public async Task DeleteAsync_DeletesSVCGMMonitorLogSuccessfully()
    {
        // Arrange
        await ClearDatabase();
        var svcgmMonitorLog = new SVCGMMonitorLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "Test",
            //Message = "Message to delete",
            //LogLevel = "Info",
            //Timestamp = DateTime.UtcNow,
            IsActive = true
        };

        await _dbContext.SVcgmMonitorLogs.AddAsync(svcgmMonitorLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.DeleteAsync(svcgmMonitorLog);

        // Assert
        Assert.NotNull(result);

        //var deletedEntity = await _dbContext.SVcgmMonitorLogs
        //    .FirstOrDefaultAsync(x => x.Message == "Message to delete");
        //Assert.Null(deletedEntity);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsMatchingLog_WhenReferenceIdExists()
    {
        // Arrange
        await ClearDatabase();
        var referenceId = Guid.NewGuid().ToString();

        var svcgmMonitorLog = new SVCGMMonitorLog
        {
            ReferenceId = referenceId,
            Type = "Test",
            //Message = "Test reference message",
            //LogLevel = "Info",
            //Timestamp = DateTime.UtcNow,
            IsActive = true
        };

        await _dbContext.SVcgmMonitorLogs.AddAsync(svcgmMonitorLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        //Assert.Equal("Test reference message", result.Message);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenReferenceIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentReferenceId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentReferenceId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region Missing Basic Repository Methods

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var svcgmMonitorLog = new SVCGMMonitorLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "Test",
            IsActive = true
        };

        await _dbContext.SVcgmMonitorLogs.AddAsync(svcgmMonitorLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(svcgmMonitorLog.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(svcgmMonitorLog.Id, result.Id);
        Assert.Equal(svcgmMonitorLog.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByIdAsync(999999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        await ClearDatabase();
        var svcgmMonitorLogs = new List<SVCGMMonitorLog>
        {
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Info",
                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Warning",
                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Error",
                IsActive = false // Inactive
            }
        };

        await _dbContext.SVcgmMonitorLogs.AddRangeAsync(svcgmMonitorLogs);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only active entities
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmpty_WhenNoActiveEntities()
    {
        // Arrange
        await ClearDatabase();
        var inactiveLog = new SVCGMMonitorLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "Error",
            IsActive = false
        };

        await _dbContext.SVcgmMonitorLogs.AddAsync(inactiveLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultipleEntities()
    {
        // Arrange
        await ClearDatabase();
        var svcgmMonitorLogs = new List<SVCGMMonitorLog>
        {
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Info",
                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Warning",
                IsActive = true
            }
        };

        // Act
        var result = await _repository.AddRangeAsync(svcgmMonitorLogs);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());

        var savedEntities = await _dbContext.SVcgmMonitorLogs.ToListAsync();
        Assert.Equal(2, savedEntities.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    [Fact]
    public async Task RemoveRangeAsync_ShouldRemoveMultipleEntities()
    {
        // Arrange
        await ClearDatabase();
        var svcgmMonitorLogs = new List<SVCGMMonitorLog>
        {
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Info",
                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Warning",
                IsActive = true
            }
        };

        await _dbContext.SVcgmMonitorLogs.AddRangeAsync(svcgmMonitorLogs);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.RemoveRangeAsync(svcgmMonitorLogs);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());

        var remainingEntities = await _dbContext.SVcgmMonitorLogs.ToListAsync();
        Assert.Empty(remainingEntities);
    }

    [Fact]
    public async Task RemoveRangeAsync_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    [Fact]
    public async Task FindByFilterAsync_ShouldReturnMatchingEntities()
    {
        // Arrange
        await ClearDatabase();
        var svcgmMonitorLogs = new List<SVCGMMonitorLog>
        {
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Error",
                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Warning",
                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Error",
                IsActive = false // Inactive
            }
        };

        await _dbContext.SVcgmMonitorLogs.AddRangeAsync(svcgmMonitorLogs);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.FindByFilterAsync(x => x.Type == "Error");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Only active Error entities
        Assert.Equal("Error", result.First().Type);
        Assert.True(result.First().IsActive);
    }

    [Fact]
    public async Task FindByFilterAsync_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var svcgmMonitorLog = new SVCGMMonitorLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "Info",
            IsActive = true
        };

        await _dbContext.SVcgmMonitorLogs.AddAsync(svcgmMonitorLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.FindByFilterAsync(x => x.Type == "NonExistent");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public void FilterBy_ShouldReturnQueryableWithFilter()
    {
        // Arrange
        var svcgmMonitorLogs = new List<SVCGMMonitorLog>
        {
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Error",
                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Warning",
                IsActive = true
            }
        };

        _dbContext.SVcgmMonitorLogs.AddRange(svcgmMonitorLogs);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.FilterBy(x => x.Type == "Error");

        // Assert
        Assert.NotNull(result);
        Assert.IsAssignableFrom<IQueryable<SVCGMMonitorLog>>(result);

        var materializedResult = result.ToList();
        Assert.Single(materializedResult);
        Assert.Equal("Error", materializedResult.First().Type);
    }

    [Fact]
    public void QueryAll_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var svcgmMonitorLogs = new List<SVCGMMonitorLog>
        {
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Error",
                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Warning",
                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Info",
                IsActive = false // Inactive
            }
        };

        _dbContext.SVcgmMonitorLogs.AddRange(svcgmMonitorLogs);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.QueryAll(x => x.IsActive);

        // Assert
        Assert.NotNull(result);
        Assert.IsAssignableFrom<IQueryable<SVCGMMonitorLog>>(result);

        var materializedResult = result.ToList();
        Assert.Equal(2, materializedResult.Count); // Only active entities
        Assert.All(materializedResult, x => Assert.True(x.IsActive));
    }

    [Fact]
    public void QueryAll_WithExpression_ShouldReturnFilteredEntities()
    {
        // Arrange
        var svcgmMonitorLogs = new List<SVCGMMonitorLog>
        {
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Error",
                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Warning",
                IsActive = true
            }
        };

        _dbContext.SVcgmMonitorLogs.AddRange(svcgmMonitorLogs);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.QueryAll(x => x.Type == "Error");

        // Assert
        Assert.NotNull(result);
        Assert.IsAssignableFrom<IQueryable<SVCGMMonitorLog>>(result);

        var materializedResult = result.ToList();
        Assert.Single(materializedResult);
        Assert.Equal("Error", materializedResult.First().Type);
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnQueryableForPagination()
    {
        // Arrange
        var svcgmMonitorLogs = new List<SVCGMMonitorLog>
        {
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Error",
                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Warning",
                IsActive = true
            }
        };

        _dbContext.SVcgmMonitorLogs.AddRange(svcgmMonitorLogs);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.IsAssignableFrom<IQueryable<SVCGMMonitorLog>>(result);

        var materializedResult = result.ToList();
        Assert.Equal(2, materializedResult.Count);
    }

    [Fact]
    public void GetByReferenceId_ShouldReturnQueryableWithFilter()
    {
        // Arrange
        var referenceId = Guid.NewGuid().ToString();
        var svcgmMonitorLogs = new List<SVCGMMonitorLog>
        {
            new SVCGMMonitorLog
            {
                ReferenceId = referenceId,
                Type = "Error",
                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Warning",
                IsActive = true
            }
        };

        _dbContext.SVcgmMonitorLogs.AddRange(svcgmMonitorLogs);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetByReferenceId(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.IsAssignableFrom<IQueryable<SVCGMMonitorLog>>(result);

        var materializedResult = result.ToList();
        Assert.Single(materializedResult);
        Assert.Equal(referenceId, materializedResult.First().ReferenceId);
    }

    [Fact]
    public void GetByReferenceId_WithExpression_ShouldReturnFilteredQueryable()
    {
        // Arrange
        var referenceId = Guid.NewGuid().ToString();
        var svcgmMonitorLogs = new List<SVCGMMonitorLog>
        {
            new SVCGMMonitorLog
            {
                ReferenceId = referenceId,
                Type = "Error",
                IsActive = true
            },
            new SVCGMMonitorLog
            {
                ReferenceId = referenceId,
                Type = "Warning",
                IsActive = true
            }
        };

        _dbContext.SVcgmMonitorLogs.AddRange(svcgmMonitorLogs);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetByReferenceId(referenceId, x => x.Type == "Error");

        // Assert
        Assert.NotNull(result);
        Assert.IsAssignableFrom<IQueryable<SVCGMMonitorLog>>(result);

        var materializedResult = result.ToList();
        Assert.Single(materializedResult);
        Assert.Equal("Error", materializedResult.First().Type);
    }

    #endregion

    #region Error Handling and Edge Cases

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    [Fact]
    public async Task GetDetailByType_ShouldThrow_WhenTypeIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.GetDetailByType(null));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeIsEmpty()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType("");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeIsWhitespace()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType("   ");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleNullParameters()
    {
        // Act & Assert - Should not throw exception
        var result1 = await _repository.GetSvcgmMonitorLogsByInfraObjectId(null, "2023-01-01", "2023-01-02");
        var result2 = await _repository.GetSvcgmMonitorLogsByInfraObjectId("INFRA_123", null, "2023-01-02");
        var result3 = await _repository.GetSvcgmMonitorLogsByInfraObjectId("INFRA_123", "2023-01-01", null);

        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.NotNull(result3);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleEmptyParameters()
    {
        // Act & Assert - Should not throw exception
        var result1 = await _repository.GetSvcgmMonitorLogsByInfraObjectId("", "2023-01-01", "2023-01-02");
        var result2 = await _repository.GetSvcgmMonitorLogsByInfraObjectId("INFRA_123", "", "2023-01-02");
        var result3 = await _repository.GetSvcgmMonitorLogsByInfraObjectId("INFRA_123", "2023-01-01", "");

        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.NotNull(result3);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldHandleNullReferenceId()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync(null);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldHandleEmptyReferenceId()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldHandleWhitespaceReferenceId()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("   ");

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetDatabaseNameFromConnectionString Tests

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldReturnDatabaseName_ForMySql()
    {
        // Arrange
        var connectionString = "Server=localhost;Database=TestDB;Uid=user;Pwd=password;";
        var provider = "mysql";

        // Act
        var result = _repository.GetType()
            .GetMethod("GetDatabaseNameFromConnectionString", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
            ?.Invoke(_repository, new object[] { connectionString, provider }) as string;

        // Assert
        Assert.Equal("TestDB", result);
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldReturnDatabaseName_ForMsSql()
    {
        // Arrange
        var connectionString = "Server=localhost;Database=TestDB;Trusted_Connection=true;";
        var provider = "mssql";

        // Act
        var result = _repository.GetType()
            .GetMethod("GetDatabaseNameFromConnectionString", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
            ?.Invoke(_repository, new object[] { connectionString, provider }) as string;

        // Assert
        Assert.Equal("TestDB", result);
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldReturnDatabaseName_ForPostgreSql()
    {
        // Arrange
        var connectionString = "Host=localhost;Database=TestDB;Username=user;Password=password;";
        var provider = "npgsql";

        // Act
        var result = _repository.GetType()
            .GetMethod("GetDatabaseNameFromConnectionString", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
            ?.Invoke(_repository, new object[] { connectionString, provider }) as string;

        // Assert
        Assert.Equal("TestDB", result);
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldReturnDatabaseName_ForOracle()
    {
        // Arrange
        var connectionString = "Data Source=localhost:1521/XE;User Id=user;Password=password;";
        var provider = "oracle";

        // Act
        var result = _repository.GetType()
            .GetMethod("GetDatabaseNameFromConnectionString", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
            ?.Invoke(_repository, new object[] { connectionString, provider }) as string;

        // Assert
        Assert.Equal("localhost:1521/XE", result);
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldThrow_ForUnsupportedProvider()
    {
        // Arrange
        var connectionString = "Server=localhost;Database=TestDB;";
        var provider = "unsupported";

        // Act & Assert
        var exception = Assert.Throws<System.Reflection.TargetInvocationException>(() =>
            _repository.GetType()
                .GetMethod("GetDatabaseNameFromConnectionString", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.Invoke(_repository, new object[] { connectionString, provider }));

        Assert.IsType<ArgumentException>(exception.InnerException);
        Assert.Contains("Unsupported provider name", exception.InnerException.Message);
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldThrow_WhenNoDatabaseInConnectionString()
    {
        // Arrange
        var connectionString = "Server=localhost;Trusted_Connection=true;"; // No Database parameter
        var provider = "mssql";

        // Act & Assert
        var exception = Assert.Throws<System.Reflection.TargetInvocationException>(() =>
            _repository.GetType()
                .GetMethod("GetDatabaseNameFromConnectionString", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.Invoke(_repository, new object[] { connectionString, provider }));

        Assert.IsType<ArgumentException>(exception.InnerException);
        Assert.Contains("Unable to extract database name", exception.InnerException.Message);
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldHandleCaseInsensitiveProvider()
    {
        // Arrange
        var connectionString = "Server=localhost;Database=TestDB;Trusted_Connection=true;";
        var provider = "MSSQL"; // Uppercase

        // Act
        var result = _repository.GetType()
            .GetMethod("GetDatabaseNameFromConnectionString", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
            ?.Invoke(_repository, new object[] { connectionString, provider }) as string;

        // Assert
        Assert.Equal("TestDB", result);
    }

    #endregion

    #region IsTableExistAsync Tests

    [Fact]
    public async Task IsTableExistAsync_ShouldReturnFalse_WhenTableNameIsNull()
    {
        // Act
        var result = await _repository.IsTableExistAsync(null, "dbo", "mssql");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldReturnFalse_WhenTableNameIsEmpty()
    {
        // Act
        var result = await _repository.IsTableExistAsync("", "dbo", "mssql");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldReturnFalse_WhenSchemaNameIsNull()
    {
        // Act
        var result = await _repository.IsTableExistAsync("TestTable", null, "mssql");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldReturnFalse_WhenSchemaNameIsEmpty()
    {
        // Act
        var result = await _repository.IsTableExistAsync("TestTable", "", "mssql");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldReturnFalse_WhenProviderNameIsNull()
    {
        // Act
        var result = await _repository.IsTableExistAsync("TestTable", "dbo", null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldReturnFalse_WhenProviderNameIsEmpty()
    {
        // Act
        var result = await _repository.IsTableExistAsync("TestTable", "dbo", "");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleOracleProvider()
    {
        // Arrange
        var tableName = "SVcgmMonitorLogs";
        var schemaName = "TESTSCHEMA";
        var providerName = "oracle";

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment without Oracle connection
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleMsSqlProvider()
    {
        // Arrange
        var tableName = "SVcgmMonitorLogs";
        var schemaName = "dbo";
        var providerName = "mssql";

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleMySqlProvider()
    {
        // Arrange
        var tableName = "SVcgmMonitorLogs";
        var schemaName = "testdb";
        var providerName = "mysql";

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment without MySQL connection
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandlePostgreSqlProvider()
    {
        // Arrange
        var tableName = "SVcgmMonitorLogs";
        var schemaName = "public";
        var providerName = "npgsql";

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment without PostgreSQL connection
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleCaseInsensitiveProviderName()
    {
        // Arrange
        var tableName = "SVcgmMonitorLogs";
        var schemaName = "dbo";
        var providerName = "MSSQL"; // Uppercase

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    #endregion

    #region Enhanced GetSvcgmMonitorLogsByInfraObjectId Tests

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleValidParameters()
    {
        // Arrange
        var infraObjectId = "INFRA_123";
        var startDate = "2023-01-01";
        var endDate = "2023-01-31";

        // Act & Assert - Should not throw exception
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<SVCGMMonitorLog>>(result);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleEmptyInfraObjectId()
    {
        // Arrange
        var infraObjectId = "";
        var startDate = "2023-01-01";
        var endDate = "2023-01-31";

        // Act & Assert - Should not throw exception
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<SVCGMMonitorLog>>(result);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleWhitespaceInfraObjectId()
    {
        // Arrange
        var infraObjectId = "   ";
        var startDate = "2023-01-01";
        var endDate = "2023-01-31";

        // Act & Assert - Should not throw exception
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<SVCGMMonitorLog>>(result);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleEmptyStartDate()
    {
        // Arrange
        var infraObjectId = "INFRA_123";
        var startDate = "";
        var endDate = "2023-01-31";

        // Act & Assert - Should not throw exception
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<SVCGMMonitorLog>>(result);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleEmptyEndDate()
    {
        // Arrange
        var infraObjectId = "INFRA_123";
        var startDate = "2023-01-01";
        var endDate = "";

        // Act & Assert - Should not throw exception
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<SVCGMMonitorLog>>(result);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleWhitespaceStartDate()
    {
        // Arrange
        var infraObjectId = "INFRA_123";
        var startDate = "   ";
        var endDate = "2023-01-31";

        // Act & Assert - Should not throw exception
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<SVCGMMonitorLog>>(result);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleWhitespaceEndDate()
    {
        // Arrange
        var infraObjectId = "INFRA_123";
        var startDate = "2023-01-01";
        var endDate = "   ";

        // Act & Assert - Should not throw exception
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<SVCGMMonitorLog>>(result);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleAllNullParameters()
    {
        // Act & Assert - Should not throw exception
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(null, null, null);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<SVCGMMonitorLog>>(result);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleAllEmptyParameters()
    {
        // Act & Assert - Should not throw exception
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId("", "", "");

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<SVCGMMonitorLog>>(result);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleValidDateFormats()
    {
        // Arrange
        var infraObjectId = "INFRA_123";
        var startDate = "2023-12-01";
        var endDate = "2023-12-31";

        // Act & Assert - Should not throw exception
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<SVCGMMonitorLog>>(result);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleDateTimeFormats()
    {
        // Arrange
        var infraObjectId = "INFRA_123";
        var startDate = "2023-01-01 00:00:00";
        var endDate = "2023-01-31 23:59:59";

        // Act & Assert - Should not throw exception
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<SVCGMMonitorLog>>(result);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleSameDateRange()
    {
        // Arrange
        var infraObjectId = "INFRA_123";
        var date = "2023-01-01";

        // Act & Assert - Should not throw exception
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, date, date);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<SVCGMMonitorLog>>(result);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleReversedDateRange()
    {
        // Arrange
        var infraObjectId = "INFRA_123";
        var startDate = "2023-01-31"; // Later date
        var endDate = "2023-01-01";   // Earlier date

        // Act & Assert - Should handle gracefully
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<SVCGMMonitorLog>>(result);
    }

    #endregion

    #region GetTableName Tests

    [Fact]
    public void GetTableName_ShouldReturnTableName_ForSVCGMMonitorLog()
    {
        // Act
        var result = _repository.GetTableName<SVCGMMonitorLog>();

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);
        Assert.IsType<string>(result);
        // The actual table name depends on Entity Framework configuration
        // but it should return a non-null, non-empty string
    }

    [Fact]
    public void GetTableName_ShouldReturnConsistentResult()
    {
        // Act
        var result1 = _repository.GetTableName<SVCGMMonitorLog>();
        var result2 = _repository.GetTableName<SVCGMMonitorLog>();

        // Assert
        Assert.Equal(result1, result2);
        Assert.NotNull(result1);
        Assert.NotEmpty(result1);
    }

    [Fact]
    public void GetTableName_ShouldHandleDifferentEntityTypes()
    {
        // Act & Assert - Should not throw exception for valid entity types
        try
        {
            var result = _repository.GetTableName<SVCGMMonitorLog>();
            Assert.NotNull(result);
            Assert.NotEmpty(result);
        }
        catch (Exception ex)
        {
            // If it throws, it should be a meaningful exception
            Assert.True(ex is ArgumentException || ex is InvalidOperationException);
        }
    }

    #endregion

    #region Integration Tests for Complex Scenarios

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldUseGetTableNameMethod()
    {
        // This test verifies that GetSvcgmMonitorLogsByInfraObjectId uses GetTableName internally
        // Arrange
        var infraObjectId = "INFRA_123";
        var startDate = "2023-01-01";
        var endDate = "2023-01-31";

        // Act & Assert - Should not throw exception and should use GetTableName internally
        var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<SVCGMMonitorLog>>(result);

        // Verify that GetTableName works (it's called internally)
        var tableName = _repository.GetTableName<SVCGMMonitorLog>();
        Assert.NotNull(tableName);
        Assert.NotEmpty(tableName);
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleConfigurationErrors()
    {
        // This test verifies behavior when configuration is missing or invalid
        // Arrange
        var infraObjectId = "INFRA_123";
        var startDate = "2023-01-01";
        var endDate = "2023-01-31";

        // Act & Assert - Should handle configuration errors gracefully
        try
        {
            var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, startDate, endDate);
            Assert.NotNull(result);
        }
        catch (Exception ex)
        {
            // Expected exceptions for configuration issues
            Assert.True(ex is InvalidOperationException ||
                       ex is ArgumentException ||
                       ex is NotSupportedException ||
                       ex is System.Security.Cryptography.CryptographicException);
        }
    }

    [Fact]
    public async Task GetSvcgmMonitorLogsByInfraObjectId_ShouldHandleDatabaseConnectionErrors()
    {
        // This test verifies behavior when database connection fails
        // Arrange
        var infraObjectId = "INFRA_123";
        var startDate = "2023-01-01";
        var endDate = "2023-01-31";

        // Act & Assert - Should handle database connection errors gracefully
        try
        {
            var result = await _repository.GetSvcgmMonitorLogsByInfraObjectId(infraObjectId, startDate, endDate);
            Assert.NotNull(result);
        }
        catch (Exception ex)
        {
            // Expected exceptions for database connection issues
            Assert.True(ex is InvalidOperationException ||
                       ex is System.Data.Common.DbException ||
                       ex is TimeoutException ||
                       ex is NotSupportedException);
        }
    }

    #endregion
}
