using ContinuityPatrol.Application.Features.BackUpLog.Commands.Create;
using ContinuityPatrol.Application.Features.BackUpLog.Commands.Delete;
using ContinuityPatrol.Application.Features.BackUpLog.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class BackUpLogFixture : IDisposable
{
    public List<BackUpLog> BackUpLogs { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateBackUpLogCommand CreateBackUpLogCommand { get; set; }
    public UpdateBackUpLogCommand UpdateBackUpLogCommand { get; set; }
    public DeleteBackUpLogCommand DeleteBackUpLogCommand { get; set; }
    public IMapper Mapper { get; set; }

    public BackUpLogFixture()
    {
        BackUpLogs = new List<BackUpLog>
        {
            new BackUpLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                HostName = "TestServer01",
                DatabaseName = "TestDatabase",
                UserName = "TestUser",
                IsLocalServer = true,
                IsBackUpServer = false,
                BackUpPath = @"C:\Backups\TestDatabase.bak",
                Type = "Full",
                Status = "Completed",
                Properties = "{\"compression\":\"true\",\"encryption\":\"false\",\"size\":\"1024MB\"}",
                IsActive = true
            }
        };

        BackUpLogs = AutoBackUpLogFixture.Create<List<BackUpLog>>();
        UserActivities = AutoBackUpLogFixture.Create<List<UserActivity>>();
        CreateBackUpLogCommand = AutoBackUpLogFixture.Create<CreateBackUpLogCommand>();
        UpdateBackUpLogCommand = AutoBackUpLogFixture.Create<UpdateBackUpLogCommand>();
        DeleteBackUpLogCommand = AutoBackUpLogFixture.Create<DeleteBackUpLogCommand>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<BackUpLogProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoBackUpLogFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateBackUpLogCommand>(p => p.HostName, 100));
            fixture.Customize<CreateBackUpLogCommand>(c => c.With(b => b.HostName, "TestServer01"));
            fixture.Customize<CreateBackUpLogCommand>(c => c.With(b => b.DatabaseName, "TestDatabase"));
            fixture.Customize<CreateBackUpLogCommand>(c => c.With(b => b.UserName, "TestUser"));
            fixture.Customize<CreateBackUpLogCommand>(c => c.With(b => b.IsLocalServer, true));
            fixture.Customize<CreateBackUpLogCommand>(c => c.With(b => b.IsBackUpServer, false));
            fixture.Customize<CreateBackUpLogCommand>(c => c.With(b => b.BackUpPath, @"C:\Backups\TestDatabase.bak"));
            fixture.Customize<CreateBackUpLogCommand>(c => c.With(b => b.Type, "Full"));
            fixture.Customize<CreateBackUpLogCommand>(c => c.With(b => b.Status, "Completed"));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateBackUpLogCommand>(p => p.HostName, 100));
            fixture.Customize<UpdateBackUpLogCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateBackUpLogCommand>(c => c.With(b => b.HostName, "UpdatedServer01"));
            fixture.Customize<UpdateBackUpLogCommand>(c => c.With(b => b.DatabaseName, "UpdatedDatabase"));
            fixture.Customize<UpdateBackUpLogCommand>(c => c.With(b => b.UserName, "UpdatedUser"));
            fixture.Customize<UpdateBackUpLogCommand>(c => c.With(b => b.IsLocalServer, false));
            fixture.Customize<UpdateBackUpLogCommand>(c => c.With(b => b.IsBackUpServer, true));
            fixture.Customize<UpdateBackUpLogCommand>(c => c.With(b => b.BackUpPath, @"D:\Backups\UpdatedDatabase.bak"));
            fixture.Customize<UpdateBackUpLogCommand>(c => c.With(b => b.Type, "Differential"));
            fixture.Customize<UpdateBackUpLogCommand>(c => c.With(b => b.Status, "In Progress"));

            fixture.Customize<DeleteBackUpLogCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<BackUpLog>(c => c.With(b => b.ReferenceId, Guid.NewGuid().ToString()));
            fixture.Customize<BackUpLog>(c => c.With(b => b.IsActive, true));
            fixture.Customize<BackUpLog>(c => c.With(b => b.HostName, "TestServer01"));
            fixture.Customize<BackUpLog>(c => c.With(b => b.DatabaseName, "TestDatabase"));
            fixture.Customize<BackUpLog>(c => c.With(b => b.UserName, "TestUser"));
            fixture.Customize<BackUpLog>(c => c.With(b => b.IsLocalServer, true));
            fixture.Customize<BackUpLog>(c => c.With(b => b.IsBackUpServer, false));
            fixture.Customize<BackUpLog>(c => c.With(b => b.BackUpPath, @"C:\Backups\TestDatabase.bak"));
            fixture.Customize<BackUpLog>(c => c.With(b => b.Type, "Full"));
            fixture.Customize<BackUpLog>(c => c.With(b => b.Status, "Completed"));
            fixture.Customize<BackUpLog>(c => c.With(b => b.Properties, "{\"compression\":\"true\",\"encryption\":\"false\",\"size\":\"1024MB\"}"));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}
