using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class TableAccessRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly TableAccessRepository _repository;
    private readonly TableAccessFixture _fixture;

    public TableAccessRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = databaseFixture.DbContext;
        _repository = new TableAccessRepository(_dbContext);
        _fixture = new TableAccessFixture();
    }

    #region IsTableAccessNameExist Tests

    [Fact]
    public async Task IsTableAccessNameExist_ShouldReturnTrue_WhenTableNameExists_WithoutValidGuid()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess = _fixture.CreateTableAccess(tableName: "TestTable");
        await _repository.AddAsync(tableAccess);

        // Act
        var result = await _repository.IsTableAccessNameExist("TestTable", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTableAccessNameExist_ShouldReturnFalse_WhenTableNameDoesNotExist_WithoutValidGuid()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess = _fixture.CreateTableAccess(tableName: "TestTable");
        await _repository.AddAsync(tableAccess);

        // Act
        var result = await _repository.IsTableAccessNameExist("NonExistentTable", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTableAccessNameExist_ShouldReturnTrue_WhenTableNameExists_WithValidGuid()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess1 = _fixture.CreateTableAccess(tableName: "TestTable");
        var tableAccess2 = _fixture.CreateTableAccess(tableName: "TestTable");
        await _repository.AddAsync(tableAccess1);
        await _repository.AddAsync(tableAccess2);

        // Act
        var result = await _repository.IsTableAccessNameExist("TestTable", tableAccess1.ReferenceId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTableAccessNameExist_ShouldReturnFalse_WhenTableNameDoesNotExist_WithValidGuid()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess = _fixture.CreateTableAccess(tableName: "TestTable");
        await _repository.AddAsync(tableAccess);

        // Act
        var result = await _repository.IsTableAccessNameExist("NonExistentTable", tableAccess.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTableAccessNameExist_ShouldHandleNullAndEmptyValues()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        var result1 = await _repository.IsTableAccessNameExist(null, "invalid-guid");
        Assert.False(result1);

        var result2 = await _repository.IsTableAccessNameExist("", "invalid-guid");
        Assert.False(result2);

        var result3 = await _repository.IsTableAccessNameExist("TestTable", null);
        Assert.False(result3);

        var result4 = await _repository.IsTableAccessNameExist("TestTable", "");
        Assert.False(result4);
    }

    #endregion

    #region IsTableAccessNameUnique Tests

    [Fact]
    public async Task IsTableAccessNameUnique_ShouldReturnTrue_WhenTableNameExists()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess = _fixture.CreateTableAccess(tableName: "TestTable");
        await _repository.AddAsync(tableAccess);

        // Act
        var result = await _repository.IsTableAccessNameUnique("TestTable");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTableAccessNameUnique_ShouldReturnFalse_WhenTableNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess = _fixture.CreateTableAccess(tableName: "TestTable");
        await _repository.AddAsync(tableAccess);

        // Act
        var result = await _repository.IsTableAccessNameUnique("NonExistentTable");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTableAccessNameUnique_ShouldReturnTrue_WhenMultipleTablesWithSameName()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess1 = _fixture.CreateTableAccess(tableName: "TestTable");
        var tableAccess2 = _fixture.CreateTableAccess(tableName: "TestTable");
        await _repository.AddAsync(tableAccess1);
        await _repository.AddAsync(tableAccess2);

        // Act
        var result = await _repository.IsTableAccessNameUnique("TestTable");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTableAccessNameUnique_ShouldReturnFalse_WhenEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsTableAccessNameUnique("AnyTable");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTableAccessNameUnique_ShouldHandleNullAndEmptyValues()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        var result1 = await _repository.IsTableAccessNameUnique(null);
        Assert.False(result1);

        var result2 = await _repository.IsTableAccessNameUnique("");
        Assert.False(result2);
    }

    [Fact]
    public async Task IsTableAccessNameUnique_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess = _fixture.CreateTableAccess(tableName: "TestTable");
        await _repository.AddAsync(tableAccess);

        // Act
        var result1 = await _repository.IsTableAccessNameUnique("TestTable");
        var result2 = await _repository.IsTableAccessNameUnique("testtable");
        var result3 = await _repository.IsTableAccessNameUnique("TESTTABLE");

        // Assert
        Assert.True(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnProjectedActiveTableAccesses()
    {
        // Arrange
        await ClearDatabase();

        var activeTableAccess = _fixture.CreateTableAccess(tableName: "ActiveTable", isActive: true);
        var inactiveTableAccess = _fixture.CreateTableAccess(tableName: "InactiveTable", isActive: false);

        await _repository.AddAsync(activeTableAccess);
        await _repository.AddAsync(inactiveTableAccess);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var tableAccess = result.First();
        Assert.Equal(activeTableAccess.Id, tableAccess.Id);
        Assert.Equal(activeTableAccess.ReferenceId, tableAccess.ReferenceId);
        Assert.Equal(activeTableAccess.TableName, tableAccess.TableName);
        Assert.Equal(activeTableAccess.SchemaName, tableAccess.SchemaName);
        Assert.Equal(activeTableAccess.IsChecked, tableAccess.IsChecked);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoActiveTableAccesses()
    {
        // Arrange
        await ClearDatabase();

        var inactiveTableAccess = _fixture.CreateTableAccess(isActive: false);
        await _repository.AddAsync(inactiveTableAccess);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetTableAccessNames Tests

    [Fact]
    public async Task GetTableAccessNames_ShouldReturnProjectedActiveTableAccesses()
    {
        // Arrange
        await ClearDatabase();

        var tableAccesses = _fixture.CreateMultipleTableAccesses(3, "dbo", true);
        var inactiveTableAccess = _fixture.CreateTableAccess(schemaName: "dbo", tableName: "InactiveTable", isActive: false);

        await _dbContext.TableAccesses.AddRangeAsync(tableAccesses);
        _dbContext.SaveChanges();

        await _dbContext.TableAccesses.AddAsync(inactiveTableAccess);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetTableAccessNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);

        // Verify projection
        var firstResult = result.First();
        Assert.NotNull(firstResult.ReferenceId);
        Assert.NotNull(firstResult.TableName);
        Assert.NotNull(firstResult.SchemaName);

        // Verify ordering by SchemaName
        var orderedResults = result.OrderBy(x => x.SchemaName).ToList();
        Assert.Equal(result, orderedResults);
    }

    [Fact]
    public async Task GetTableAccessNames_ShouldReturnEmptyList_WhenNoActiveTableAccesses()
    {
        // Arrange
        await ClearDatabase();

        var inactiveTableAccess = _fixture.CreateTableAccess(isActive: false);

        await _dbContext.TableAccesses.AddRangeAsync(inactiveTableAccess);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetTableAccessNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetTableAccessNames_ShouldOrderBySchemaName()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess1 = _fixture.CreateTableAccess(schemaName: "zschema", tableName: "Table1");
        var tableAccess2 = _fixture.CreateTableAccess(schemaName: "aschema", tableName: "Table2");
        var tableAccess3 = _fixture.CreateTableAccess(schemaName: "mschema", tableName: "Table3");

        await _repository.AddAsync(tableAccess1);
        await _repository.AddAsync(tableAccess2);
        await _repository.AddAsync(tableAccess3);

        // Act
        var result = await _repository.GetTableAccessNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.Equal("aschema", result[0].SchemaName);
        Assert.Equal("mschema", result[1].SchemaName);
        Assert.Equal("zschema", result[2].SchemaName);
    }

    #endregion

    #region GetSchemaNameList Tests

    [Fact]
    public async Task GetSchemaNameList_ShouldReturnActiveTableAccesses()
    {
        // Arrange
        await ClearDatabase();

        var activeTableAccess = _fixture.CreateTableAccess(isActive: true);
        var inactiveTableAccess = _fixture.CreateTableAccess(isActive: false);

        await _dbContext.TableAccesses.AddRangeAsync(activeTableAccess,inactiveTableAccess);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetSchemaNameList();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(activeTableAccess.ReferenceId, result.First().ReferenceId);
    }

    [Fact]
    public async Task GetSchemaNameList_ShouldReturnEmptyList_WhenNoActiveTableAccesses()
    {
        // Arrange
        await ClearDatabase();

        var inactiveTableAccess = _fixture.CreateTableAccess(isActive: false);
       await _dbContext.TableAccesses.AddAsync(inactiveTableAccess);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetSchemaNameList();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetTableNameListBySchema Tests

    [Fact]
    public async Task GetTableNameListBySchema_ShouldReturnCheckedTablesForSchema()
    {
        // Arrange
        await ClearDatabase();

        var checkedTable = _fixture.CreateTableAccess(schemaName: "dbo", tableName: "CheckedTable", isChecked: true);
        var uncheckedTable = _fixture.CreateTableAccess(schemaName: "dbo", tableName: "UncheckedTable", isChecked: false);
        var differentSchemaTable = _fixture.CreateTableAccess(schemaName: "other", tableName: "OtherTable", isChecked: true);

        await _repository.AddAsync(checkedTable);
        await _repository.AddAsync(uncheckedTable);
        await _repository.AddAsync(differentSchemaTable);

        // Act
        var result = await _repository.GetTableNameListBySchema("dbo");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var tableAccess = result.First();
        Assert.Equal(checkedTable.Id, tableAccess.Id);
        Assert.Equal(checkedTable.ReferenceId, tableAccess.ReferenceId);
        Assert.Equal(checkedTable.TableName, tableAccess.TableName);
        Assert.Equal(checkedTable.SchemaName, tableAccess.SchemaName);
        Assert.Equal(checkedTable.IsChecked, tableAccess.IsChecked);
    }

    [Fact]
    public async Task GetTableNameListBySchema_ShouldReturnEmptyList_WhenNoCheckedTablesForSchema()
    {
        // Arrange
        await ClearDatabase();

        var uncheckedTable = _fixture.CreateTableAccess(schemaName: "dbo", isChecked: false);
        await _repository.AddAsync(uncheckedTable);

        // Act
        var result = await _repository.GetTableNameListBySchema("dbo");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetTableNameListBySchema_ShouldReturnEmptyList_WhenSchemaNotFound()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess = _fixture.CreateTableAccess(schemaName: "dbo", isChecked: true);
        await _repository.AddAsync(tableAccess);

        // Act
        var result = await _repository.GetTableNameListBySchema("nonexistent");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetTableAccessByTableNames Tests

    [Fact]
    public async Task GetTableAccessByTableNames_ShouldReturnMatchingActiveTableAccesses()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess1 = _fixture.CreateTableAccess(tableName: "Table1");
        var tableAccess2 = _fixture.CreateTableAccess(tableName: "Table2");
        var tableAccess3 = _fixture.CreateTableAccess(tableName: "Table3");
        var inactiveTableAccess = _fixture.CreateTableAccess(tableName: "Table4", isActive: false);

        await _repository.AddAsync(tableAccess1);
        await _repository.AddAsync(tableAccess2);
        await _repository.AddAsync(tableAccess3);
        await _repository.AddAsync(inactiveTableAccess);

        var tableNames = new List<string> { "Table1", "Table3", "Table4" };

        // Act
        var result = await _repository.GetTableAccessByTableNames(tableNames);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only active tables should be returned
        Assert.Contains(result, x => x.TableName == "Table1");
        Assert.Contains(result, x => x.TableName == "Table3");
        Assert.DoesNotContain(result, x => x.TableName == "Table4"); // Inactive table should not be returned
    }

    [Fact]
    public async Task GetTableAccessByTableNames_ShouldReturnEmptyList_WhenNoMatchingTables()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess = _fixture.CreateTableAccess(tableName: "ExistingTable");
        await _repository.AddAsync(tableAccess);

        var tableNames = new List<string> { "NonExistentTable1", "NonExistentTable2" };

        // Act
        var result = await _repository.GetTableAccessByTableNames(tableNames);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetTableAccessByTableNames_ShouldReturnEmptyList_WhenEmptyTableNamesList()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess = _fixture.CreateTableAccess();
        await _repository.AddAsync(tableAccess);

        var tableNames = new List<string>();

        // Act
        var result = await _repository.GetTableAccessByTableNames(tableNames);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetUnUsedTableAccessByTableNames Tests

    [Fact]
    public async Task GetUnUsedTableAccessByTableNames_ShouldReturnNonMatchingActiveTableAccesses()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess1 = _fixture.CreateTableAccess(tableName: "Table1");
        var tableAccess2 = _fixture.CreateTableAccess(tableName: "Table2");
        var tableAccess3 = _fixture.CreateTableAccess(tableName: "Table3");
        var inactiveTableAccess = _fixture.CreateTableAccess(tableName: "Table4", isActive: false);

        await _repository.AddAsync(tableAccess1);
        await _repository.AddAsync(tableAccess2);
        await _repository.AddAsync(tableAccess3);
        await _repository.AddAsync(inactiveTableAccess);

        var tableNames = new List<string> { "Table1", "Table3" };

        // Act
        var result = await _repository.GetUnUsedTableAccessByTableNames(tableNames);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Table2", result.First().TableName);
    }

    [Fact]
    public async Task GetUnUsedTableAccessByTableNames_ShouldReturnAllActiveTables_WhenEmptyTableNamesList()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess1 = _fixture.CreateTableAccess(tableName: "Table1");
        var tableAccess2 = _fixture.CreateTableAccess(tableName: "Table2");
        var inactiveTableAccess = _fixture.CreateTableAccess(tableName: "Table3", isActive: false);

        await _repository.AddAsync(tableAccess1);
        await _repository.AddAsync(tableAccess2);
        await _repository.AddAsync(inactiveTableAccess);

        var tableNames = new List<string>();

        // Act
        var result = await _repository.GetUnUsedTableAccessByTableNames(tableNames);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.TableName == "Table1");
        Assert.Contains(result, x => x.TableName == "Table2");
    }

    [Fact]
    public async Task GetUnUsedTableAccessByTableNames_ShouldReturnEmptyList_WhenAllTablesAreUsed()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess1 = _fixture.CreateTableAccess(tableName: "Table1");
        var tableAccess2 = _fixture.CreateTableAccess(tableName: "Table2");

        await _repository.AddAsync(tableAccess1);
        await _repository.AddAsync(tableAccess2);

        var tableNames = new List<string> { "Table1", "Table2" };

        // Act
        var result = await _repository.GetUnUsedTableAccessByTableNames(tableNames);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetTableAccessByTableName Tests

    [Fact]
    public async Task GetTableAccessByTableName_ShouldReturnTableAccess_WhenTableNameExists()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess = _fixture.CreateTableAccess(tableName: "TestTable");
        await _repository.AddAsync(tableAccess);

        // Act
        var result = await _repository.GetTableAccessByTableName("TestTable");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(tableAccess.ReferenceId, result.ReferenceId);
        Assert.Equal(tableAccess.TableName, result.TableName);
        Assert.Equal(tableAccess.SchemaName, result.SchemaName);
    }

    [Fact]
    public async Task GetTableAccessByTableName_ShouldReturnNull_WhenTableNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess = _fixture.CreateTableAccess(tableName: "TestTable");
        await _repository.AddAsync(tableAccess);

        // Act
        var result = await _repository.GetTableAccessByTableName("NonExistentTable");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetTableAccessByTableName_ShouldReturnNull_WhenTableIsInactive()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess = _fixture.CreateTableAccess(tableName: "TestTable", isActive: false);
        await _repository.AddAsync(tableAccess);

        // Act
        var result = await _repository.GetTableAccessByTableName("TestTable");

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetSchemas Tests

    [Fact]
    public async Task GetSchemas_ShouldReturnMySqlTables_WhenProviderIsMySQL()
    {
        // Arrange
        await ClearDatabase();

        // Note: This test would require setting up raw SQL execution in the in-memory database
        // For now, we'll test the method signature and basic functionality
        var schema = "testdb";
        var provider = "mysql";

        // Act & Assert
        // Since we're using in-memory database, we expect this to throw or return empty
        // In a real scenario, this would execute the MySQL-specific query
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _repository.GetSchemas(schema, provider));

        // Verify the exception is related to raw SQL execution in in-memory database
        Assert.Contains("relational", exception.Message.ToLower());
    }

    [Fact]
    public async Task GetSchemas_ShouldReturnMsSqlTables_WhenProviderIsMsSQL()
    {
        // Arrange
        await ClearDatabase();

        var schema = "testdb";
        var provider = "mssql";

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _repository.GetSchemas(schema, provider));

        Assert.Contains("relational", exception.Message.ToLower());
    }

    //[Fact]
    //public async Task GetSchemas_ShouldReturnOracleTables_WhenProviderIsOracle()
    //{
    //    // Arrange
    //    await ClearDatabase();

    //    var schema = "testdb";
    //    var provider = "oracle";

    //    // Act & Assert
    //    var exception = await Assert.ThrowsAsync<InvalidOperationException>(
    //        () => _repository.GetSchemas(schema, provider));

    //    Assert.Contains("relational", exception.Message.ToLower());
    //}

    //[Fact]
    //public async Task GetSchemas_ShouldReturnOracleTables_WhenProviderIsUnknown()
    //{
    //    // Arrange
    //    await ClearDatabase();

    //    var schema = "testdb";
    //    var provider = "unknown";

    //    // Act & Assert
    //    var exception = await Assert.ThrowsAsync<InvalidOperationException>(
    //        () => _repository.GetSchemas(schema, provider));

    //    Assert.Contains("relational", exception.Message.ToLower());
    //}

    #endregion

    #region GetTableAccessListAsync Tests

    [Fact]
    public async Task GetTableAccessListAsync_ShouldReturnTableAccessListWithConfigurationStatus()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess1 = _fixture.CreateTableAccess(tableName: "Table1");
        var tableAccess2 = _fixture.CreateTableAccess(tableName: "Table2");
        var inactiveTableAccess = _fixture.CreateTableAccess(tableName: "Table3", isActive: false);

        await _dbContext.TableAccesses.AddRangeAsync(tableAccess1,tableAccess2,inactiveTableAccess);
        _dbContext.SaveChanges();

        // Create archive with table reference
        var archive = _fixture.CreateArchive(tableNameProperties: tableAccess1.ReferenceId);
        await _dbContext.Archives.AddAsync(archive);

        // Create dataset with table reference
        var dataSet = _fixture.CreateDataSet(tableAccessId: tableAccess2.ReferenceId);
        await _dbContext.DataSets.AddAsync(dataSet);

        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetTableAccessListAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only active table accesses

        var table1Result = result.FirstOrDefault(x => x.TableName == "Table1");
        Assert.NotNull(table1Result);
        Assert.Equal(tableAccess1.ReferenceId, table1Result.Id);
        Assert.Equal(tableAccess1.SchemaName, table1Result.SchemaName);
        Assert.Equal(tableAccess1.IsChecked, table1Result.IsChecked);
        Assert.True(table1Result.IsConfigured); // Should be configured due to archive

        var table2Result = result.FirstOrDefault(x => x.TableName == "Table2");
        Assert.NotNull(table2Result);
        Assert.Equal(tableAccess2.ReferenceId, table2Result.Id);
        Assert.True(table2Result.IsConfigured); // Should be configured due to dataset
    }

    [Fact]
    public async Task GetTableAccessListAsync_ShouldReturnEmptyList_WhenNoActiveTableAccesses()
    {
        // Arrange
        await ClearDatabase();

        var inactiveTableAccess = _fixture.CreateTableAccess(isActive: false);
        await _dbContext.TableAccesses.AddAsync(inactiveTableAccess);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetTableAccessListAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetTableAccessListAsync_ShouldSetIsConfiguredFalse_WhenNoArchiveOrDataSetReference()
    {
        // Arrange
        await ClearDatabase();

        var tableAccess = _fixture.CreateTableAccess();
        await _repository.AddAsync(tableAccess);

        // Act
        var result = await _repository.GetTableAccessListAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.False(result.First().IsConfigured);
    }

    #endregion

    #region Integration Tests

    [Fact]
    public async Task TableAccessRepository_ShouldHandleComplexScenarios()
    {
        // Arrange
        await ClearDatabase();

        // Create multiple table accesses with different configurations
        var tableAccesses = _fixture.CreateMultipleTableAccesses(5);
        foreach (var tableAccess in tableAccesses)
        {
            await _repository.AddAsync(tableAccess);
        }

        // Test multiple operations
        var nameExistsResult = await _repository.IsTableAccessNameExist(tableAccesses[0].TableName, "invalid-guid");
        var uniqueResult = await _repository.IsTableAccessNameUnique(tableAccesses[0].TableName);
        var allTableAccesses = await _repository.ListAllAsync();
        var tableAccessNames = await _repository.GetTableAccessNames();
        var schemaList = await _repository.GetSchemaNameList();

        // Assert
        Assert.True(nameExistsResult);
        Assert.True(uniqueResult);
        Assert.Equal(5, allTableAccesses.Count);
        Assert.Equal(5, tableAccessNames.Count);
        Assert.Equal(5, schemaList.Count);
    }

    #endregion

    #region Helper Methods

    private async Task ClearDatabase()
    {
        var existingEntities = _dbContext.TableAccesses.ToList();
        _dbContext.TableAccesses.RemoveRange(existingEntities);

        var existingArchives = _dbContext.Archives.ToList();
        _dbContext.Archives.RemoveRange(existingArchives);

        var existingDataSets = _dbContext.DataSets.ToList();
        _dbContext.DataSets.RemoveRange(existingDataSets);

        await _dbContext.SaveChangesAsync();
    }

    #endregion
}
