﻿using ContinuityPatrol.Application.Features.InfraSummary.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraSummary.Commands;

public class UpdateInfraSummaryTests : IClassFixture<InfraSummaryFixture>
{
    private readonly InfraSummaryFixture _infraSummaryFixture;
    private readonly Mock<IInfraSummaryRepository> _mockInfraSummaryRepository;
    private readonly UpdateInfraSummaryCommandHandler _handler;

    public UpdateInfraSummaryTests(InfraSummaryFixture infraSummaryFixture)
    {
        _infraSummaryFixture = infraSummaryFixture;

        _mockInfraSummaryRepository = InfraSummaryRepositoryMocks.UpdateInfraSummaryRepository(_infraSummaryFixture.InfraSummaries);

        _handler = new UpdateInfraSummaryCommandHandler(_infraSummaryFixture.Mapper, _mockInfraSummaryRepository.Object);

        _infraSummaryFixture.InfraSummaries[0].Type = "Linux Template";
        _infraSummaryFixture.InfraSummaries[1].Type = "Windows-2019";
        _infraSummaryFixture.InfraSummaries[2].Type = "FORMCHECKL";
    }

    [Fact]
    public async Task Handle_ValidInfraSummary_UpdateToInfraSummariesRepoCount()
    {
        _infraSummaryFixture.UpdateInfraSummaryCommand.Id = _infraSummaryFixture.InfraSummaries[0].ReferenceId;

        _infraSummaryFixture.UpdateInfraSummaryCommand.Type = "Linux Template";

        var result = await _handler.Handle(_infraSummaryFixture.UpdateInfraSummaryCommand, CancellationToken.None);

        var infraSummary = await _mockInfraSummaryRepository.Object.GetByReferenceIdAsync(result.InfraSummaryId);

        Assert.Equal(_infraSummaryFixture.UpdateInfraSummaryCommand.Type, infraSummary.Type);
    }

    [Fact]
    public async Task Handle_Return_UpdateInfraSummaryResponse_When_InfraSummaryUpdated()
    {
        _infraSummaryFixture.UpdateInfraSummaryCommand.Id = _infraSummaryFixture.InfraSummaries[0].ReferenceId;

        var result = await _handler.Handle(_infraSummaryFixture.UpdateInfraSummaryCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateInfraSummaryResponse));

        result.InfraSummaryId.ShouldBe(null);

        result.InfraSummaryId.ShouldBe(null);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _infraSummaryFixture.UpdateInfraSummaryCommand.Id = _infraSummaryFixture.InfraSummaries[0].ReferenceId;

        await _handler.Handle(_infraSummaryFixture.UpdateInfraSummaryCommand, CancellationToken.None);

        _mockInfraSummaryRepository.Verify(x => x.GetInfraSummaryByType(It.IsAny<string>()), Times.Once);
    }
}