﻿using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowAction.Validators;

public class UpdateWorkflowActionValidatorTests
{
    private readonly Mock<IWorkflowActionRepository> _mockWorkflowActionRepository;

    public List<Domain.Entities.WorkflowAction> WorkflowActions { get; set; }

    public UpdateWorkflowActionValidatorTests()
    {
        WorkflowActions = new Fixture().Create<List<Domain.Entities.WorkflowAction>>();

        _mockWorkflowActionRepository = WorkflowActionRepositoryMocks.UpdateWorkflowActionRepository(WorkflowActions);
    }

    //FormTypeName

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_ActionName_WithEmpty(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_ActionName_IsNull(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = null;
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameNotNullRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_ActionName_MinimumRange(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "AB";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameRangeRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_ActionName_MaximumRange(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWUVTSRQPONMLK_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWUVTSRQPONMLK_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWUVTSRQPONMLK_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWUVTSRQPONMLK_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWUVTSRQPONMLK_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWUVTSRQPONMLK_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWUVTSRQPONMLK_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWUVTSRQPONMLK_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWUVTSRQPONMLK";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameRangeRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Valid_ActionName(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "  Cognizant  ";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Valid_ActionName_With_SingleSpace_InFront(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = " Cognizant";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Valid_ActionName_With_SingleSpace_InBack(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "Cognizant ";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Valid_ActionName_With_TripleSpace_InBetween(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "Cognizant   Solution";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Valid_ActionName_With_SpecialCharacters_InFront(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "*&^%$Cognizant Solution";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Valid_ActionName_With_SpecialCharacters_InBack(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "Cognizant Solution*&%$$#";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Valid_ActionName_With_SpecialCharacters_InBetween(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "Cognizant*&^%%$&Solution";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Valid_ActionName_With_SpecialCharacters_Only(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "#$*&^%%$&";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Valid_ActionName_With_UnderScore_InFront(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "_Cognizant Solution";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Valid_ActionName_With_UnderScore_InBack(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "Cognizant Solution_";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Valid_ActionName_With_Numbers_InFront(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "876Cognizant Solution";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Valid_ActionName_With_UnderScoreAndNumbers_InFront(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "_876Cognizant Solution";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Valid_ActionName_With_UnderScore_InFront_AndNumbers_InBack(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "_Cognizant Solution765";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Valid_ActionName_With_Numbers_Only(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.ActionName = "************";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }


    //Properties

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Properties_WithEmpty(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.Properties = "";
        updateWorkflowActionCommand.Type = "Operation";
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowActionCommand.ActionName = "VerifyClusterState";

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);

        Assert.Contains(validateResult.Errors, e => e.PropertyName == "Properties" && e.ErrorMessage == "Please Enter the Properties.");
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_UpdateWorkflowActionCommandValidator_Properties_IsNull(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        var validator = new UpdateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        updateWorkflowActionCommand.Properties = null;
        updateWorkflowActionCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionPropertiesNotNullRequired, (string)validateResult.Errors[3].ErrorMessage);
    }
}