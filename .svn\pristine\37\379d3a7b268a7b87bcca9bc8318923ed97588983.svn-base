namespace ContinuityPatrol.Application.Features.IncidentManagement.Queries.GetDetail;

public class
    GetIncidentManagementDetailsQueryHandler : IRequestHandler<GetIncidentManagementDetailQuery,
        IncidentManagementDetailVm>
{
    private readonly IIncidentManagementRepository _incidentManagementRepository;
    private readonly IMapper _mapper;

    public GetIncidentManagementDetailsQueryHandler(IMapper mapper,
        IIncidentManagementRepository incidentManagementRepository)
    {
        _mapper = mapper;
        _incidentManagementRepository = incidentManagementRepository;
    }

    public async Task<IncidentManagementDetailVm> Handle(GetIncidentManagementDetailQuery request,
        CancellationToken cancellationToken)
    {
        var incidentManagement = await _incidentManagementRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(incidentManagement, nameof(Domain.Entities.IncidentManagement),
            new NotFoundException(nameof(Domain.Entities.IncidentManagement), request.Id));

        var incidentManagementDetailDto = _mapper.Map<IncidentManagementDetailVm>(incidentManagement);

        return incidentManagementDetailDto;
    }
}