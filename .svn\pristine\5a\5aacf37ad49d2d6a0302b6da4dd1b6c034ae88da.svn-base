﻿using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.OneViewRiskMitigationCyberSecurity;

public class GetOneViewRiskMitigationCyberSecurityQueryHandler : IRequestHandler<GetOneViewRiskMitigationCyberSecurityQuery, List<OneViewRiskMitigationCyberSecurityView>>
{
    private readonly IOneViewRiskMitigationCyberSecurityViewRepository _oneViewRiskMitigationCyberSecurityViewRepository;
    public GetOneViewRiskMitigationCyberSecurityQueryHandler(IOneViewRiskMitigationCyberSecurityViewRepository oneViewRiskMitigationCyberSecurityViewRepository)
    {
        _oneViewRiskMitigationCyberSecurityViewRepository = oneViewRiskMitigationCyberSecurityViewRepository;
            
    }
    public async Task<List<OneViewRiskMitigationCyberSecurityView>> Handle(GetOneViewRiskMitigationCyberSecurityQuery request, CancellationToken cancellationToken)
    {
        var result = await _oneViewRiskMitigationCyberSecurityViewRepository.ListAllAsync();
        return result;
    }
}
