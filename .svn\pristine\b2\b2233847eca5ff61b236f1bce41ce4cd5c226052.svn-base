﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class DashboardViewLogRepositoryMocks
{
    public static Mock<IDashboardViewLogRepository> CreateDashboardViewLogRepository(List<DashboardViewLog> dashboardViewLogs)
    {
        var createDashboardViewLogRepository = new Mock<IDashboardViewLogRepository>();

        createDashboardViewLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dashboardViewLogs);

        createDashboardViewLogRepository.Setup(repo => repo.AddAsync(It.IsAny<DashboardViewLog>())).ReturnsAsync(
            (DashboardViewLog dashboardViewLog) =>
            {
                dashboardViewLog.Id = new Fixture().Create<int>();

                dashboardViewLog.ReferenceId = new Fixture().Create<Guid>().ToString();

                dashboardViewLogs.Add(dashboardViewLog);

                return dashboardViewLog;
            });

        return createDashboardViewLogRepository;
    }

    public static Mock<IDashboardViewLogRepository> UpdateDashboardViewLogRepository(List<DashboardViewLog> dashboardViewLogs)
    {
        var updateDashboardViewLogRepository = new Mock<IDashboardViewLogRepository>();

        updateDashboardViewLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dashboardViewLogs);

        updateDashboardViewLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViewLogs.SingleOrDefault(x => x.ReferenceId == i));

        updateDashboardViewLogRepository.Setup(repo => repo.UpdateAsync(It.IsAny<DashboardViewLog>())).ReturnsAsync((DashboardViewLog dashboardViewLog) =>
        {
            var index = dashboardViewLogs.FindIndex(item => item.ReferenceId == dashboardViewLog.ReferenceId);

            dashboardViewLogs[index] = dashboardViewLog;

            return dashboardViewLog;

        });
        return updateDashboardViewLogRepository;
    }

    public static Mock<IDashboardViewLogRepository> DeleteDashboardViewLogRepository(List<DashboardViewLog> dashboardViewLogs)
    {
        var deleteDashboardViewLogRepository = new Mock<IDashboardViewLogRepository>();

        deleteDashboardViewLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dashboardViewLogs);

        deleteDashboardViewLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViewLogs.SingleOrDefault(x => x.ReferenceId == i));

        deleteDashboardViewLogRepository.Setup(repo => repo.UpdateAsync(It.IsAny<DashboardViewLog>())).ReturnsAsync((DashboardViewLog dashboardViewLog) =>
        {
            var index = dashboardViewLogs.FindIndex(item => item.ReferenceId == dashboardViewLog.ReferenceId);

            dashboardViewLog.IsActive = false;

            dashboardViewLogs[index] = dashboardViewLog;

            return dashboardViewLog;
        });

        return deleteDashboardViewLogRepository;
    }

    public static Mock<IDashboardViewLogRepository> GetPaginatedDashboardViewLogRepository(List<DashboardViewLog> dashboardViewLogs)
    {
        var dashboardViewLogRepository = new Mock<IDashboardViewLogRepository>();

        var queryableDashboardViewLog = dashboardViewLogs.BuildMock();

        dashboardViewLogRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableDashboardViewLog);

        return dashboardViewLogRepository;
    }

    public static Mock<IDashboardViewLogRepository> GetDashboardViewLogRepository(List<DashboardViewLog> dashboardViewLogs)
    {
        var dashboardViewLogRepository = new Mock<IDashboardViewLogRepository>();

        dashboardViewLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dashboardViewLogs);

        dashboardViewLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => dashboardViewLogs.SingleOrDefault(x => x.ReferenceId == i));

        return dashboardViewLogRepository;
    }

    public static Mock<IDashboardViewLogRepository> GetDashboardViewLogEmptyRepository()
    {
        var dashboardViewLogEmptyRepository = new Mock<IDashboardViewLogRepository>();

        dashboardViewLogEmptyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<DashboardViewLog>());

        return dashboardViewLogEmptyRepository;
    }

    public static Mock<IDashboardViewLogRepository> GetDataLagOneDayReportRepository(List<DashboardViewLog> dashboardViewLogs)
    {
        var dashboardViewLogRepository = new Mock<IDashboardViewLogRepository>();

        dashboardViewLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dashboardViewLogs);

        dashboardViewLogRepository.Setup(repo => repo.GetDataLagByOneDayReport(It.IsAny<string>())).ReturnsAsync(dashboardViewLogs);

        return dashboardViewLogRepository;
    }

}