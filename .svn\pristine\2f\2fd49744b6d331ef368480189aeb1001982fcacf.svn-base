﻿namespace ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetNameUnique;

public class GetCredentialProfileNameUniqueQueryHandler : IRequestHandler<GetCredentialProfileNameUniqueQuery, bool>
{
    private readonly ICredentialProfileRepository _credentialProfile;

    public GetCredentialProfileNameUniqueQueryHandler(ICredentialProfileRepository credentialProfile)
    {
        _credentialProfile = credentialProfile;
    }

    public async Task<bool> Handle(GetCredentialProfileNameUniqueQuery request, CancellationToken cancellationToken)
    {
        var credentialProfile =
            await _credentialProfile.IsCredentialProfileNameExist(request.CredentialProfileName,
                request.CredentialProfileId);

        return credentialProfile;
    }
}