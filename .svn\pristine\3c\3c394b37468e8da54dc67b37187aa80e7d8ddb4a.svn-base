using ContinuityPatrol.Domain.ViewModels.VeritasClusterModel;

namespace ContinuityPatrol.Application.Features.VeritasCluster.Queries.GetList;

public class GetVeritasClusterListQueryHandler : IRequestHandler<GetVeritasClusterListQuery, List<VeritasClusterListVm>>
{
    private readonly IMapper _mapper;
    private readonly IVeritasClusterRepository _veritasClusterRepository;

    public GetVeritasClusterListQueryHandler(IMapper mapper, IVeritasClusterRepository veritasClusterRepository)
    {
        _mapper = mapper;
        _veritasClusterRepository = veritasClusterRepository;
    }

    public async Task<List<VeritasClusterListVm>> Handle(GetVeritasClusterListQuery request,
        CancellationToken cancellationToken)
    {
        var veritasClusters = await _veritasClusterRepository.ListAllAsync();

        if (veritasClusters.Count <= 0) return new List<VeritasClusterListVm>();

        return _mapper.Map<List<VeritasClusterListVm>>(veritasClusters);
    }
}