﻿@using ContinuityPatrol.Domain.Entities
@model ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel.WorkflowProfileInfoViewModel;


<!--Modal Create-->
<div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
        <form id="CreateForm1" asp-controller="WorkflowProfileManagement" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data">
            @Html.AntiForgeryToken()
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-workflow-profile"></i><span>Workflow Profile Configuration</span></h6>
                <button type="button" class="btn-close" title="Close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">

                <div class="mb-3 form-group">
                    <div class="form-label">Profile Name</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-name"></i></span>
                        <input asp-for="WorkflowProfile.Name" id="textProfileName" class="form-control" placeholder="Enter Profile Name" maxlength="200" />
                        <input asp-for="WorkflowProfile.Id" id="textProfileId" type="hidden" class="form-control" />
                    
                    </div>
                    <span asp-validation-for="WorkflowProfile.Name" id="ProfileNameError"></span>
                </div>
                <div class="col ">
                    <div class="mb-3 form-group">
                        <div class="form-label">Execution Policy</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-group-policy"></i></span>
                            <select asp-for="WorkflowProfile.ExecutionPolicy" class=" form-select-modal" id="selectExecutionPolicy" data-live-search="true" data-placeholder="Select Execution Policy">
                                <option value=""></option>
                                <option value="2">Distribution Policy</option>
                                <option value="1">Group Node Policy</option>
                            </select>
                            <input type="hidden" id="textExecutionId" class="form-control" />
                        </div>
                        <span asp-for="WorkflowProfile.ExecutionPolicy" id="ExecutionPolicyError"></span>
                    </div>
                </div>
                <div class="col">
                    <div class="mb-3 form-group" id="GroupPolicy">
                        <div class="form-label">Group Node Policy</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-standby-file"></i></span>
                            <select asp-for="WorkflowProfile.GroupPolicyName" id="selectGroupPolicy" class="form-select-modal" data-live-search="true" data-placeholder="Select Group Node Policy">
                                <option value=""></option>
                              @*   @foreach (var GroupPolicies in Model.GroupPolicies)
                                {
                                    @if (GroupPolicies.Type == "WorkflowService" || GroupPolicies.Type == "Workflow Service")
                                    {
                                        <option groupId="@GroupPolicies.Id" value="@GroupPolicies.GroupName">@GroupPolicies.GroupName</option>
                                    }
                                } *@
                            </select>
                            
                        </div>
                        <span asp-validation-for="WorkflowProfile.GroupPolicyName" id="GroupPolicyError"></span>
                        
                    </div>
                    <input asp-for="WorkflowProfile.GroupPolicyId" type="hidden" id="textGroupPolicyId" class="form-control" />
                </div>
                <div class="mb-3 form-group" id="profilePasswordField">
                    <div class="form-label">Password</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-lock"></i></span>
                        <input asp-for="WorkflowProfile.Password" id="workflowPassword" class="txtPassword1 form-control" type="password" autocomplete="off" placeholder="Enter Password" maxlength="30" />
                        <span role="button" class="input-group-text eye-change toggle-password"><i class="cp-password-visible fs-6"></i></span>
                    </div>
                    <span id="Password-error"></span>
                </div>
                <div class="mb-3 form-group" id="profilePasswordField1">
                    <div class="form-label">Confirm Password</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-lock"></i></span>
                        <input id="workflowConfirmPassword" type="password" class="form-control txtPassword2" autocomplete="off" placeholder="Enter Confirm Password" maxlength="30" />
                        <span role="button" class="input-group-text eye-change toggle-password"><i class="cp-password-visible fs-6"></i></span>
                    </div>
                    <span id="Confirmpassword-error"></span>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button id="profileSaveFunctions" type="button" class="btn btn-primary btn-sm">Save</button>
                </div>
            </div>

        </form>
    </div>
</div>



