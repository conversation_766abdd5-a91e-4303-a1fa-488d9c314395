﻿let mId = sessionStorage.getItem("monitorId");
let monitortype = 'ApplicationNoReplication';
let infraObjectId = sessionStorage.getItem("infraobjectId");
setTimeout(() => { msSQLServer(mId, monitortype) }, 250)
setTimeout(() => { msSQLServer1(infraObjectId) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)

    $("#replicationTable").empty();
   

async function msSQLServer(id, type) {
    
    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
};
function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}

function propertiesData(value) {
    let ipprdata;
    if (value === undefined || value === null || value === '') {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
    else {
        let data = JSON?.parse(value?.properties);
        let customSite = data?.NoReplicationModels?.length > 1;
        if (customSite) {
            $("#Sitediv").show();
        } else {
            $("#Sitediv").hide();
        }


        $(".siteContainer").empty();


        data?.NoReplicationModels?.forEach((a, index) => {
            let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
            $(".siteContainer").append(selectTab);
        });


        if (data?.NoReplicationModels?.length > 0) {
            $("#siteName0 .nav-link").addClass("active");
            displaySiteData(data?.NoReplicationModels[0]);
        }



        let defaultSite = data?.NoReplicationModels?.find(d => d?.Type === 'DR') || data?.NoReplicationModels[0];
        if (defaultSite) {
            displaySiteData(defaultSite);
        }

        $(document).on('click', '.siteListChange', function () {
            $(".siteListChange .nav-link").removeClass("active");
            $(this).find(".nav-link").addClass("active");
            let siteId = $(this)[0].id
            let getSiteName = $(`#${siteId} .siteName`).text()

            let MonitoringModel = data?.NoReplicationModels.find(d => d.Type === getSiteName);
            if (MonitoringModel) {
                $('#PR_Server_IpAddress').find('i').remove();
                $('#DR_Server_IpAddress').find('i').remove();
                displaySiteData(MonitoringModel);
            }
        });

        function displaySiteData(siteData) {
            let obj = {};
            $('.dynamicSite-header').text(siteData?.Type).attr('title', siteData?.Type);
            
            for (let key in siteData?.MonitoringModel) {
                obj[`DR_` + key] = siteData?.MonitoringModel[key];
            }
            ipprdata = obj?.DR_ConnectViaHostName?.toLowerCase() === "true" ? obj?.DR_Server_HostName : obj?.DR_Server_IpAddress

            $("#DR_Server_IpAddress").text(ipprdata)
            let MonitoringModelapplication = [
                "DR_Server_Name",
            ];

            if (Object.keys(obj)?.length > 0) {
                bindProperties(obj, MonitoringModelapplication, value, ipprdata);
            }
        }

        let applicationDetail = data?.PrNoReplicationModel?.PrMonitorModel
        ipprdata = applicationDetail?.Pr_ConnectViaHostName?.toLowerCase() === "true" ? applicationDetail?.PR_Server_HostName : applicationDetail?.PR_Server_IpAddress

        $('#PR_Server_IpAddress').text(ipprdata)
        const applicationProp = [
            "PR_Server_Name",
        ];

        bindProperties(applicationDetail, applicationProp, value, ipprdata);
    }
}
function setPropData(data, propSets, value) {
    
    propSets.forEach(properties => {
        bindProperties(data, properties, value);
    });
}

function bindProperties(data, properties, value) {

    let prservericon = data?.PR_Server_Name ? "cp-server me-1 text-primary" : "text-danger cp-disable"
    let drservericon = data?.DR_Server_Name ? "cp-server me-1 text-primary" : "text-danger cp-disable"
    const iconMapping = {
      
        'PR_Server_Name': prservericon,
        'DR_Server_Name': drservericon,
    };

    $('#PR_Server_IpAddress').find('i').remove();
    $('#DR_Server_IpAddress').find('i').remove();

    const prIconHtml = value?.prServerStatus?.toLowerCase() === "up" ? '<i class="cp-up-linearrow text-success me-1 fs-6"></i>' :
        value?.prServerStatus?.toLowerCase() === "down" ? '<i class="cp-down-linearrow text-danger me-1 fs-6"></i>' :
            value?.prServerStatus?.toLowerCase() === "pending" ? '<i class="cp-pending text-warning me-1 fs-6"></i>' : '';
    $('#PR_Server_IpAddress').prepend(prIconHtml);

    const drIconHtml = value?.drServerStatus?.toLowerCase() === "up" ? '<i class="cp-up-linearrow text-success me-1 fs-6"></i>' :
        value?.drServerStatus?.toLowerCase() === "down" ? '<i class="cp-down-linearrow text-danger me-1 fs-6"></i>' :
            value?.drServerStatus?.toLowerCase() === "pending" ? '<i class="cp-pending text-warning me-1 fs-6"></i>' : '';
    $('#DR_Server_IpAddress').prepend(drIconHtml);
    properties?.forEach(property => {
        const value = data[property];
        let displayedValue = value !== undefined ? checkAndReplace(value) : 'NA';
        let iconClass = iconMapping[property] || '';

        switch (displayedValue.toLowerCase()) {
            case 'na':
                iconClass = 'text-danger cp-disable';
                break;
            case 'not allowed':
            case 'no':
                iconClass = 'text-danger cp-disagree';
                break;
            case 'disabled':
            case 'disable':
                iconClass = 'text-danger cp-disables';
                break;
            case 'enabled':
            case 'enable':
                iconClass = 'text-success cp-enables';
                break;
        }
      
        const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
        const mergeValue = `${iconHtml}${displayedValue}`;
        $(`#${property}`).html(mergeValue).attr('title', displayedValue);

    });
}
async function msSQLServer1(infraObjectId) {
    
    $.ajax({
        url: "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId",
        method: 'GET',
        data: {
            infraObjectId: infraObjectId,
        },
        dataType: 'json',
        async: true,
        success: function (res) {
            const value = res.data;
            
            let strData = ''
            if (value != null && value?.length > 0) {
                $('#mssqlserver').show();
             
            } else {
                $('#mssqlserver').hide();
            }
            
            value?.forEach((list, i) => {
               
                let serverName = "";

                if (list.workflowName && (list?.isServiceUpdate?.toLowerCase() === "error" || list?.isServiceUpdate?.toLowerCase() === "stopped")) {
                    serverName = checkAndReplace(list?.failedActionName);
                } else {
                    serverName = checkAndReplace(list?.servicePath === null ? list?.workflowName : list?.workflowName === null ? list?.servicePath : list?.workflowName);
                }
                const ipAddress = checkAndReplace(list?.ipAddress);
                const status = checkAndReplace(list?.isServiceUpdate);
                const iconServer = serverName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";
                const iconIp = ipAddress === "NA" ? "text-danger cp-disable" : "text-secondary cp-ip-address";
                const iconStatus = status?.toLowerCase() === "running" ? "text-success cp-reload cp-animate" : status?.toLowerCase() === "error" ? "text-danger cp-fail-back" : "text-danger cp-disable";

                strData += `<tr>
                    <td class="fw-semibold text-truncate" title="${serverName}">
                      <i class="${iconServer}"></i> <span id="serviceName">${serverName}</span>
                    </td>
                    <td class="text-truncate"><i class="${iconIp}"></i> <span id="ipaddress"></span>${ipAddress}</td>
                    <td class="text-truncate"><i class="${iconStatus}"></i>
                        <span>
                            ${status}
                        </span>
                    </td>
                </tr>`


            });

            $('#replicationTable').append(strData);
           
        }
    })
}

function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}





$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})
