﻿using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.AlertMasterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertMaster.Queries;

public class GetAlertMasterPaginatedListQueryHandlerTests : IClassFixture<AlertMasterFixture>
{
    private readonly AlertMasterFixture _alertMasterFixture;

    private readonly Mock<IAlertMasterRepository> _mockAlertMasterRepository;

    private readonly GetAlertMasterPaginatedListQueryHandler _handler;

    public GetAlertMasterPaginatedListQueryHandlerTests(AlertMasterFixture alertMasterFixture)
    {
        _alertMasterFixture = alertMasterFixture;

        _mockAlertMasterRepository = AlertMasterRepositoryMocks.GetPaginatedAlertMasterRepository(_alertMasterFixture.AlertMasters);

        _handler = new GetAlertMasterPaginatedListQueryHandler(_mockAlertMasterRepository.Object,
            _alertMasterFixture.Mapper);

        _alertMasterFixture.AlertMasters[0].AlertName = "Alert";
        _alertMasterFixture.AlertMasters[0].AlertMessage = "Failed";
        _alertMasterFixture.AlertMasters[0].AlertPriority = "Email";

        _alertMasterFixture.AlertMasters[1].AlertName = "Name";
        _alertMasterFixture.AlertMasters[1].AlertMessage = "DRServer";
        _alertMasterFixture.AlertMasters[1].AlertPriority = "Phone";
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryString_NotMatch()
    {
        var result = await _handler.Handle(new GetAlertMasterPaginatedListQuery { PageNumber = 0, PageSize = 0, SearchString = "cpadmin" }, CancellationToken.None);

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedAlertMaster_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetAlertMasterPaginatedListQuery { PageNumber = 0, PageSize = 10, SearchString = "Alert" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertMasterListVm>>();

        result.TotalCount.ShouldBe(3);

        result.Data[0].Id.ShouldBe(_alertMasterFixture.AlertMasters[0].ReferenceId);
        result.Data[0].AlertName.ShouldBe("Alert");
        result.Data[0].AlertMessage.ShouldBe("Failed");
        result.Data[0].AlertPriority.ShouldBe("Email");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetAlertMasterPaginatedListQuery { PageNumber = 2, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertMasterListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_AlertMaster_With_MultipleQuery_StringParameter()
    {
        _alertMasterFixture.AlertMasters[0].ReferenceId = "b0793398-f936-4e98-a456-6a25e3c6598d";

        var result = await _handler.Handle(new GetAlertMasterPaginatedListQuery { PageNumber = 0, PageSize = 10, SearchString = "alertname=Alert;alertmessage=Failed;alertpriority=Email;" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertMasterListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBe(_alertMasterFixture.AlertMasters[0].ReferenceId);
        result.Data[0].AlertName.ShouldBe("Alert");
        result.Data[0].AlertMessage.ShouldBe("Failed");
        result.Data[0].AlertPriority.ShouldBe("Email");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetAlertMasterPaginatedListQuery(), CancellationToken.None);

        _mockAlertMasterRepository.Verify(repo => repo.GetPaginatedQuery());
    }
}