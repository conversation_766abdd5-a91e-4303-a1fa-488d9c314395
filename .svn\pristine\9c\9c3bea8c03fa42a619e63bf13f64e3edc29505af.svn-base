using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.EmployeeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.Employee.Queries.GetPaginatedList;

public class
    GetEmployeePaginatedListQueryHandler : IRequestHandler<GetEmployeePaginatedListQuery,
        PaginatedResult<EmployeeListVm>>
{
    private readonly IEmployeeRepository _employeeRepository;
    private readonly IMapper _mapper;

    public GetEmployeePaginatedListQueryHandler(IMapper mapper, IEmployeeRepository employeeRepository)
    {
        _mapper = mapper;
        _employeeRepository = employeeRepository;
    }

    public async Task<PaginatedResult<EmployeeListVm>> Handle(GetEmployeePaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _employeeRepository.GetPaginatedQuery();

        var productFilterSpec = new EmployeeFilterSpecification(request.SearchString);

        var employeeList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<EmployeeListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return employeeList;
    }
}