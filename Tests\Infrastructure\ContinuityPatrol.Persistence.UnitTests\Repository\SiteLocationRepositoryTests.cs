using System.Runtime.Intrinsics.X86;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class SiteLocationRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly SiteLocationRepository _repository;
    private readonly SiteLocationFixture _fixture;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public SiteLocationRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = databaseFixture.DbContext;
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        
        _repository = new SiteLocationRepository(_dbContext, _mockLoggedInUserService.Object);
        _fixture = new SiteLocationFixture();

        // Setup default mock behavior
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnActiveSiteLocations()
    {
        // Arrange
        await ClearDatabase();

        var location1 = _fixture.CreateSiteLocation(city: "New York", isActive: true);
        var location2 = _fixture.CreateSiteLocation(city: "London", isActive: true);
        var location3 = _fixture.CreateSiteLocation(city: "Tokyo", isActive: false);


        await _dbContext.SiteLocations.AddRangeAsync(location1, location2, location3);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.City == "New York");
        Assert.Contains(result, s => s.City == "London");
        Assert.DoesNotContain(result, s => s.City == "Tokyo");
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoActiveSiteLocations()
    {
        // Arrange
        await ClearDatabase();

        var location = _fixture.CreateSiteLocation(city: "Inactive City", isActive: false);

        await _dbContext.SiteLocations.AddAsync(location);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnProjectedFields()
    {
        // Arrange
        await ClearDatabase();

        var location = _fixture.CreateSiteLocation(
            city: "Test City",
            cityAscii: "Test City ASCII",
            country: "Test Country",
            lat: 40.7128,
            lng: -74.0060,
            iso2: "US",
            iso3: "USA",
            isActive: true
        );


        await _dbContext.SiteLocations.AddAsync(location);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
       

    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenCityExistsAndIdIsNotValidGuid()
    {
        // Arrange
        await ClearDatabase();

        var location = _fixture.CreateSiteLocation(city: "Existing City");
        await _repository.AddAsync(location);

        // Act
        var result = await _repository.IsNameExist("Existing City", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenCityDoesNotExistAndIdIsNotValidGuid()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsNameExist("Non-existing City", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenCityExistsButIdMatches()
    {
        // Arrange
        await ClearDatabase();

        var location = _fixture.CreateSiteLocation(city: "Test City");
        await _repository.AddAsync(location);

        // Act
        var result = await _repository.IsNameExist("Test City", location.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenCityExistsAndIdIsDifferent()
    {
        // Arrange
        await ClearDatabase();

        var location = _fixture.CreateSiteLocation(city: "Test City");
        await _repository.AddAsync(location);

        // Act
        var result = await _repository.IsNameExist("Test City", Guid.NewGuid().ToString());

        // Assert
        Assert.True(result);
    }

    #endregion

    #region IsSiteLocationNameUnique Tests

    [Fact]
    public async Task IsSiteLocationNameUnique_ShouldReturnTrue_WhenCityExists()
    {
        // Arrange
        await ClearDatabase();

        var location = _fixture.CreateSiteLocation(city: "Existing City");
        await _repository.AddAsync(location);

        // Act
        var result = await _repository.IsSiteLocationNameUnique("Existing City");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsSiteLocationNameUnique_ShouldReturnFalse_WhenCityDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsSiteLocationNameUnique("Non-existing City");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSiteLocationNameUnique_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();

        var location = _fixture.CreateSiteLocation(city: "Test City");
        await _repository.AddAsync(location);

        // Act
        var result = await _repository.IsSiteLocationNameUnique("test city");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults()
    {
        // Arrange
        await ClearDatabase();

        var location1 = _fixture.CreateSiteLocation(city: "City A");
        var location2 = _fixture.CreateSiteLocation(city: "City B");
        var location3 = _fixture.CreateSiteLocation(city: "City C");

        await _repository.AddAsync(location1);
        await _repository.AddAsync(location2);
        await _repository.AddAsync(location3);

        string? searchString = null;

        var specification = new SiteLocationFilterSpecification(searchString);
        // Act
        var result = await _repository.PaginatedListAllAsync(1, 2, specification, "City", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Data.Count);
        Assert.Equal(3, result.TotalCount);
        Assert.Equal(2, result.PageSize);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnProjectedFields()
    {
        // Arrange
        await ClearDatabase();

        var location = _fixture.CreateSiteLocation(
            city: "Test City",
            country: "Test Country",
            lat: 40.7128,
            lng: -74.0060
        );


        await _dbContext.SiteLocations.AddAsync(location);
        _dbContext.SaveChanges();
        string? searchString = null;

        var specification = new SiteLocationFilterSpecification(searchString);
        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "City", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.Data);
  
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldApplySpecification()
    {
        // Arrange
        await ClearDatabase();

        var location1 = _fixture.CreateSiteLocation(city: "New York", country: "USA");
        var location2 = _fixture.CreateSiteLocation(city: "London", country: "UK");
        var location3 = _fixture.CreateSiteLocation(city: "Los Angeles", country: "USA");

        await _dbContext.SiteLocations.AddRangeAsync(location1, location2, location3);
        _dbContext.SaveChanges();

        string? searchString = null;

        var specification = new SiteLocationFilterSpecification(searchString);
        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "City", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Data.Count);

    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldApplySorting()
    {
        // Arrange
        await ClearDatabase();

        var location1 = _fixture.CreateSiteLocation(city: "Zebra City");
        var location2 = _fixture.CreateSiteLocation(city: "Alpha City");
        var location3 = _fixture.CreateSiteLocation(city: "Beta City");

        await _repository.AddAsync(location1);
        await _repository.AddAsync(location2);
        await _repository.AddAsync(location3);

        string? searchString = null;

        var specification = new SiteLocationFilterSpecification(searchString);
        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "City", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Data.Count);
        Assert.Equal("Alpha City", result.Data[0].City);
        Assert.Equal("Beta City", result.Data[1].City);
        Assert.Equal("Zebra City", result.Data[2].City);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.SiteLocations.RemoveRange(_dbContext.SiteLocations);
        await _dbContext.SaveChangesAsync();
    }
}
