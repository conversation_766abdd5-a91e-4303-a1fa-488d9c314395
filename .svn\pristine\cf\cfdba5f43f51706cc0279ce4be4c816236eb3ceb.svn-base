﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowProfileRepository : BaseRepository<WorkflowProfile>, IWorkflowProfileRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IWorkflowRepository _workflowRepository;

    public WorkflowProfileRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService, IWorkflowRepository workflowRepository) :
        base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
        _workflowRepository = workflowRepository;
    }

    public override async Task<IReadOnlyList<WorkflowProfile>> ListAllAsync()
    {
        var workflowPermission = await _workflowRepository.GetWorkflowPermissions("profile");

        var workflowProfiles = SelectToWorkflowProfiles(MapWorkflowProfile(base.QueryAll(workflowProfile => workflowProfile.CompanyId.Equals(_loggedInUserService.CompanyId))));

        // var workflowProfileDto = MapWorkflowProfile(workflowProfiles);

        var workflowProfile = workflowProfiles.ToList();

        return workflowPermission.Count > 0
            ? workflowProfile.Concat(await base.FindByFilter(x => workflowPermission.Contains(x.ReferenceId))).DistinctBy(x => x.ReferenceId).ToList()
            : workflowProfile;
    }

    public override Task<WorkflowProfile> GetByReferenceIdAsync(string id)
    {
        var workflowProfiles = base.GetByReferenceId(id,
           workflow => workflow.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                          workflow.ReferenceId.Equals(id));

        var workflowProfileDto = MapWorkflowProfile(workflowProfiles);

        return Task.FromResult(workflowProfileDto.FirstOrDefault());
    }

    public async Task<List<WorkflowProfile>> GetByProfileIdAsync(List<string> id)
    {
        var workflowProfiles = _loggedInUserService.IsParent
            ? await base.FilterBy(x => id.Contains(x.ReferenceId)).Select(x=> new WorkflowProfile
            {
                Id = x.Id,
                ReferenceId = x.ReferenceId,
                Name = x.Name
            }).ToListAsync()
            : await base.FilterBy(x => id.Contains(x.ReferenceId) && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new WorkflowProfile
                {
                    Id = x.Id,
                    ReferenceId = x.ReferenceId,
                    Name = x.Name
                }).ToListAsync();

        return workflowProfiles;
    }


    public async Task<List<WorkflowProfile>> GetWorkflowProfileNames()
    {
        var workflowPermission = await _workflowRepository.GetWorkflowPermissions("profile");

        var workflowProfiles = base
            .QueryAll(data => data.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new WorkflowProfile { ReferenceId = x.ReferenceId, Name = x.Name, GroupPolicyId =x.GroupPolicyId, GroupPolicyName =x.GroupPolicyName, ExecutionPolicy = x.ExecutionPolicy })
            .OrderBy(x => x.Name);

        var workflowProfile = await workflowProfiles.ToListAsync();

        return workflowPermission.Count > 0
            ? workflowProfile.Concat(await base.FindByFilter(x => workflowPermission.Contains(x.ReferenceId))).DistinctBy(x => x.ReferenceId).ToList()
            : workflowProfile;
    }

    public override async Task<PaginatedResult<WorkflowProfile>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<WorkflowProfile> productFilterSpec, string sortColumn, string sortOrder)
    {
        return _loggedInUserService.IsParent
            ? await SelectToWorkflowProfiles(Entities
                .Specify(productFilterSpec)
                .DescOrderById())
                .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder)
            : await SelectToWorkflowProfiles(Entities
                .Specify(productFilterSpec)
                .Where(x => x.ReferenceId.Equals(_loggedInUserService.CompanyId))
                .DescOrderById())
                .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }


    public override IQueryable<WorkflowProfile> GetPaginatedQuery()
    {
        return base.QueryAll(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
            .AsNoTracking().OrderByDescending(x => x.Id);
    }


    public Task<bool> IsWorkflowProfileNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.WorkflowProfiles.Any(e => e.Name.Equals(name)))
            : Task.FromResult(_dbContext.WorkflowProfiles.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsWorkflowProfileNameUnique(string name)
    {
        var matches = _dbContext.WorkflowProfiles.Any(e => e.Name.Equals(name));

        return Task.FromResult(matches);
    }

    private IQueryable<WorkflowProfile> SelectToWorkflowProfiles(IQueryable<WorkflowProfile> query)
    {
        return query.Select(data => new WorkflowProfile
        {
            Id = data.Id,
            ReferenceId = data.ReferenceId,
            CompanyId = data.CompanyId,
            Name = data.Name,
            Status = data.Status,
            Password = data.Password,
            GroupPolicyId = data.GroupPolicyId,
            GroupPolicyName = data.GroupPolicyName,
            ExecutionPolicy = data.ExecutionPolicy,
            IsFourEye = data.IsFourEye
        });
    }

    private IQueryable<WorkflowProfile> MapWorkflowProfile(IQueryable<WorkflowProfile> workflowProfiles)
    {
        var mapWorkflowProfile = workflowProfiles.Select(data => new
        {
            WorkflowProfile = data,
            GroupPolicy = _dbContext.GroupPolicies.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.GroupPolicyId))
        });

        var workflowProfileQuery = mapWorkflowProfile.Select(result => new WorkflowProfile
        {
            Id = result.WorkflowProfile.Id,
            ReferenceId = result.WorkflowProfile.ReferenceId,
            CompanyId = result.WorkflowProfile.CompanyId,
            Name = result.WorkflowProfile.Name,
            Status = result.WorkflowProfile.Status,
            Password = result.WorkflowProfile.Password,
            GroupPolicyId = result.GroupPolicy.ReferenceId,
            GroupPolicyName = result.WorkflowProfile.GroupPolicyName,
            ExecutionPolicy = result.WorkflowProfile.ExecutionPolicy,
            IsActive = result.WorkflowProfile.IsActive,
            CreatedBy = result.WorkflowProfile.CreatedBy,
            CreatedDate = result.WorkflowProfile.CreatedDate,
            LastModifiedBy = result.WorkflowProfile.LastModifiedBy,
            LastModifiedDate = result.WorkflowProfile.LastModifiedDate,
            IsFourEye = result.WorkflowProfile.IsFourEye
        });

        return workflowProfileQuery;
    }
}