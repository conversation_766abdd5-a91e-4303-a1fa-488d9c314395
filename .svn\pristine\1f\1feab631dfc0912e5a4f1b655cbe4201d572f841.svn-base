﻿@using ContinuityPatrol.Domain.Entities
@model ContinuityPatrol.Domain.ViewModels.HacmpClusterModel.HacmpClusterViewModel
@Html.AntiForgeryToken()
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-cluster-database"></i><span>HACMP Cluster</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="hacmpSearchInp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown" title="Filter">
                            <span data-bs-toggle="dropdown"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="name=" id="hacmpNameFilter">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="server=" id="hacmpServerFilter">
                                        <label class="form-check-label" for="ServerName">
                                            Server Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="lssrcpath=" id="Lssrcpath">
                                        <label class="form-check-label" for="PathName">
                                            LSSRC Path
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="clrginfopath=" id="Clrgpath">
                                        <label class="form-check-label" for="Path">
                                            CLRG Info Path
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="resourcegroup=" id="groupValue">
                                        <label class="form-check-label" for="GroupeName">
                                            Resource Group Name
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" id="hacmpCreateBtn" class="btn btn-primary btn-sm">
                    <i class="cp-add me-1"></i>Create
                </button>
            </form>
        </div>
        <div class="pt-1 card-body" style="height: calc(100vh - 141px);">
            <div>
                <table class="datatable table table-hover dataTable no-footer" style="width:100%" id="hacmpClusterTable">
                    <thead>
                        <tr>
                            <th>Sr No</th>
                            <th>Name </th>
                            <th>Server</th>
                            <th>LSSRC Path</th>
                            <th>CLRG Info Path</th>
                            <th>Resource Group Name</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div id="hacmpConfigCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
        <!-- Your Configuration content here -->
    </div>
    <div id="hacmpConfigDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
        <!-- Your Configuration content here -->
    </div>
    <!--Modal Create-->
    <div class="modal fade" id="hacmpCreateModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="configureModalLabel" aria-hidden="true">
        <partial name="Configuration" />
    </div>
    <!--Modal Delete-->
    <div class="modal fade" id="hacmpDeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <partial name="Delete" />
    </div>
</div>
@section Scripts
{
    <partial name="_ValidationScriptsPartial" />
}
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Configuration/Infra Components/HACMPCluster/HACMPCluster.js"></script>
