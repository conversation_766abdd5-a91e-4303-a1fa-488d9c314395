﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class MonitorServiceRepository : BaseRepository<MonitorService>, IMonitorServiceRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public MonitorServiceRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<MonitorService>> ListAllAsync()
    {
        var monitorServices = FilterRequiredField(base.ListAllAsync(monitorService => monitorService.WorkflowName.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await monitorServices.ToListAsync()
            : GetAssignedInfraObjects(monitorServices);
    }

    public override async Task<MonitorService> GetByReferenceIdAsync(string id)
    {
        var monitorServices = base.GetByReferenceIdAsync(id,
            monitorService => monitorService.BusinessServiceId.Equals(_loggedInUserService.CompanyId) &&
                              monitorService.ReferenceId.Equals(id));

        return _loggedInUserService.IsAllInfra
            ? await monitorServices.FirstOrDefaultAsync()
            : GetInfraObjectByReferenceId(monitorServices.FirstOrDefault());
    }

    public async Task<List<MonitorService>> GetMonitorServiceNames()
    {
        var monitorServices = base
            .ListAllAsync(infraObject => infraObject.InfraObjectId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new MonitorService { ReferenceId = x.ReferenceId, WorkflowName = x.WorkflowName });

        return _loggedInUserService.IsAllInfra
            ? await monitorServices.ToListAsync()
            : GetAssignedInfraObjects(monitorServices).ToList();
    }
    public async Task<List<MonitorService>> GetMonitorServiceListByInfraObjectId(string infraObjectId)
    {
        var monitorService = base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId))
            .Select(x => new MonitorService
            {
                WorkflowId=x.WorkflowId,
                WorkflowName=x.WorkflowName,
                ServerId=x.ServerId,
                ServerName=x.ServerName,
                Status=x.Status,
                IsServiceUpdate=x.IsServiceUpdate,
                FailedActionId=x.FailedActionId,
                FailedActionName=x.FailedActionName,

            });
        return _loggedInUserService.IsAllInfra
           ? await monitorService.ToListAsync()
           : GetAssignedInfraObjects(monitorService).ToList();

    }
    public async Task<List<MonitorService>> GetMonitorServiceByInfraObjectId(string infraObjectId)
    {
        return await _dbContext.MonitorServices.Active()
            .Where(x => x.InfraObjectId.Equals(infraObjectId)).ToListAsync();
    }

    public async Task<List<MonitorService>> GetByInfraObjectIdAndBusinessFunctionId(string infraObjectId,
        string businessServiceId)
    {
        return await _dbContext.MonitorServices
            .Where(x => x.InfraObjectId.Equals(infraObjectId) && x.BusinessServiceId.Equals(businessServiceId) &&
                        x.IsActive).ToListAsync();
    }

    public Task<bool> IsMonitorServiceNameExist(string servicePath, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.MonitorServices.Any(e => e.ServicePath.Equals(servicePath)))
            : Task.FromResult(_dbContext.MonitorServices.Where(e => e.ServicePath.Equals(servicePath)).ToList()
                .Unique(id));
    }
    public override async Task<PaginatedResult<MonitorService>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<MonitorService> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await FilterRequiredField(Entities.Specify(productFilterSpec).DescOrderById())
           .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
        
    public override IQueryable<MonitorService> PaginatedListAllAsync()
    {
        if (_loggedInUserService.IsParent)
            return Entities.Where(x => x.IsActive)
                .AsNoTracking()
                .OrderByDescending(x => x.Id);

        return Entities.Where(x => x.IsActive && x.BusinessServiceId.Equals(_loggedInUserService.CompanyId))
            .AsNoTracking()
            .OrderByDescending(x => x.Id);
    }

    public Task<bool> IsMonitorServicePathExist(string servicePath, string infraObjectId,string type,string threadType,string workflowType,string workflowId,string workflowName, string serverId, string id)
    {
        bool match = false;
        if (!id.IsValidGuid())
        {
            if (type.ToLower() == "use workflow")
            {
                match = _dbContext.MonitorServices.Any(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.WorkflowType.Equals(workflowType) && e.WorkflowId.Equals(workflowId) && e.ServicePath.Equals(servicePath));

            }
            else
            {
                match = _dbContext.MonitorServices.Any(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.ThreadType.Equals(threadType) && e.ServicePath.Equals(servicePath));

            }
        }
        else {
            if (type.ToLower() == "use workflow")
            {
                var list = _dbContext.MonitorServices.Where(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.WorkflowType.Equals(workflowType) && e.WorkflowId.Equals(workflowId)).ToList();
                match = list.Unique(id);
            }
            else
            {
                var list = _dbContext.MonitorServices.Where(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.ThreadType.Equals(threadType) && e.ServicePath.Equals(servicePath)).ToList();
                match = list.Unique(id); 
            }
        }

        return Task.FromResult(match);
    }
    public Task<bool> IsMonitorServicePathUnique(string servicePath, string infraObjectId, string type, string threadType, string workflowType, string workflowId, string workflowName, string serverId)
    {
        bool match = false;
        if (type.ToLower() == "use workflow")
        {
            match = _dbContext.MonitorServices.Any(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.WorkflowType.Equals(workflowType) && e.WorkflowId.Equals(workflowId));

        }
        else
        {
            match = _dbContext.MonitorServices.Any(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.ThreadType.Equals(threadType) && e.ServicePath.Equals(servicePath));

        }
        return Task.FromResult(match);
    }


    private IQueryable<MonitorService>FilterRequiredField(IQueryable<MonitorService> monitors)
    {
        return monitors.Select(x => new MonitorService
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            InfraObjectId = x.InfraObjectId,
            InfraObjectName = x.InfraObjectName,
            BusinessServiceName = x.BusinessServiceName,
            BusinessServiceId = x.BusinessServiceId,
            ServerId = x.ServerId,
            ServerName = x.ServerName,
            Type = x.Type,
            ThreadType = x.ThreadType,
            WorkflowId = x.WorkflowId,
            WorkflowName = x.WorkflowName,
            WorkflowType = x.WorkflowType,
            ServicePath = x.ServicePath,
            Status=x.Status,
            WorkflowVersion=x.WorkflowVersion,
            IsServiceUpdate=x.IsServiceUpdate,
            NodeId=x.NodeId,
            NodeName=x.NodeName,
            FailedActionId=x.FailedActionId,
            FailedActionName=x.FailedActionName,
            LastModifiedDate=x.LastModifiedDate,
            LastExecutionTime=x.LastExecutionTime,
            MonitoringType = x.MonitoringType,
            Properties = x.Properties
        }); 
    }

}