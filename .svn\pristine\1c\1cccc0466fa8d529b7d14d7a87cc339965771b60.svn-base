﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller
{
    public class HP3ParwithESXIControllerShould
    {
        private readonly HP3ParwithESXIController _controller;

        public HP3ParwithESXIControllerShould()
        {
            
            _controller = new HP3ParwithESXIController();
        }

        [Fact]
        public void List_ReturnsViewResult()
        {
            
            var result = _controller.List();

            
            var viewResult = Assert.IsType<ViewResult>(result);
            
        }
    }
}
