﻿using ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionResult.Queries;

public class GetWorkflowActionResultNameQueryHandlerTests : IClassFixture<WorkflowActionResultFixture>
{
    private readonly WorkflowActionResultFixture _workflowActionResultFixture;

    private Mock<IWorkflowActionResultRepository> _mockWorkflowActionResultRepository;

    private readonly GetWorkflowActionResultNameQueryHandler _handler;

    public GetWorkflowActionResultNameQueryHandlerTests(WorkflowActionResultFixture workflowActionResultFixture)
    {
        _workflowActionResultFixture = workflowActionResultFixture;

        _mockWorkflowActionResultRepository = WorkflowActionResultRepositoryMocks.GetWorkflowActionResultNameRepository(_workflowActionResultFixture.WorkflowActionResults);

        _handler = new GetWorkflowActionResultNameQueryHandler(_workflowActionResultFixture.Mapper, _mockWorkflowActionResultRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowActionResult_Name()
    {
        var result = await _handler.Handle(new GetWorkflowActionResultNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowActionResultNameVm>>();

        result[0].Id.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].ReferenceId);
        result[0].WorkflowActionName.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].WorkflowActionName);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowActionResultNamesCount()
    {
        var result = await _handler.Handle(new GetWorkflowActionResultNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowActionResultNameVm>>();

        result.Count.ShouldBe(_workflowActionResultFixture.WorkflowActionResults.Count);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockWorkflowActionResultRepository = WorkflowActionResultRepositoryMocks.GetWorkflowActionResultEmptyRepository();

        var handler = new GetWorkflowActionResultNameQueryHandler(_workflowActionResultFixture.Mapper, _mockWorkflowActionResultRepository.Object);

        var result = await handler.Handle(new GetWorkflowActionResultNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetWorkflowActionResultNamesMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowActionResultNameQuery(), CancellationToken.None);

        _mockWorkflowActionResultRepository.Verify(x => x.GetWorkflowActionResultNames(), Times.Once);
    }
}