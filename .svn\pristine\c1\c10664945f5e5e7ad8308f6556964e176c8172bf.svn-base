﻿using ContinuityPatrol.Application.Constants;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Application.Services;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Persistence.Services.Helper;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Responses;
using UserRole = ContinuityPatrol.Shared.Core.Enums.UserRole;

namespace ContinuityPatrol.Persistence.Services;

/// <summary>
/// Service for handling password-based authentication
/// </summary>
public class PasswordAuthenticationService : BaseAuthenticationService, IAuthenticationService
{
    public const int DefaultTimeout = 30;
    private readonly IAccessManagerRepository _accessManagerRepository;
    private readonly ICompanyRepository _companyRepository;
    private readonly IWebHostEnvironment _env;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILogger<BaseAuthenticationService> _logger;
    private readonly IUserInfraObjectRepository _userInfraObjectRepository;
    private readonly IUserRepository _userRepository;
    private readonly IDynamicDashboardMapRepository _dynamicDashboardMapRepository;
    private readonly IUserLoginRepository _userLoginRepository;

    /// <summary>
    /// Initializes a new instance of the <see cref="PasswordAuthenticationService"/> class.
    /// </summary>
    public PasswordAuthenticationService(
        IConfiguration config,
        IUserRepository userRepository,
        IUserLoginRepository userLoginRepository,
        ICompanyRepository companyRepository,
        ILogger<BaseAuthenticationService> logger,
        IAccessManagerRepository accessManagerRepository,
        IUserCredentialRepository userCredentialRepository,
        IUserInfraObjectRepository userInfraObjectRepository,
        ILicenseManagerRepository licenseManagerRepository,
        IWebHostEnvironment env,
        IEmailService emailService,
        ISmtpConfigurationRepository smtpConfigurationRepository,
        IUserInfoRepository userInfoRepository,
        IAlertRepository alertRepository,
        IDynamicDashboardMapRepository dynamicDashboardMapRepository,
        IGlobalSettingRepository globalSettingRepository)
        : base(config, userRepository, userLoginRepository, userCredentialRepository, logger, emailService, smtpConfigurationRepository, userInfoRepository, alertRepository, globalSettingRepository)
    {
        _userRepository = userRepository;
        _userLoginRepository = userLoginRepository;
        _companyRepository = companyRepository;
        _logger = logger;
        _accessManagerRepository = accessManagerRepository;
        _userInfraObjectRepository = userInfraObjectRepository;
        _licenseManagerRepository = licenseManagerRepository;
        _env = env;
        _dynamicDashboardMapRepository = dynamicDashboardMapRepository;
    }

    /// <summary>
    /// Authenticates a user using password-based authentication
    /// </summary>
    public async Task<AuthenticationServiceResponse> AuthenticateAsync(AuthenticationRequest request)
    {
        try
        {
            _logger.LogInformation("Verifying Access key for user '{LoginName}'", request.LoginName);

            var user = await _userRepository.FindByLoginNameAsync(request.LoginName);
            Guard.Against.Null(user, nameof(User),
                new AuthenticationException(Authentication.InvalidLogin, (int)ErrorCode.InvalidAuthentication));

            await VerifyPasswordReset(user);

            if (!IsPasswordValid(request.Password, user.LoginPassword))
            {
                _logger.LogError("Invalid access key for user '{LoginName}'", request.LoginName);
                await VerifyLoginAttempt(user);
            }

            VerifyCompanyIfMismatch(user, request.CompanyId);
            VerifyUserLock(user);

            var userLogin = await _userLoginRepository.GetUserLoginByUserId(user.ReferenceId);
            await UpdateAuthenticateUserLogin(userLogin, user.LoginName, user.ReferenceId, request.IpAddress, user.SessionTimeout);
            await UpdateAuthenticateUserCredential(user);

            var permissions = await GetUserPermissions(user);
            var company = await _companyRepository.GetParentCompanyByLoginCompanyId(user.CompanyId);

            if (company is null)
                _logger.LogError("Exception occurred while fetching company details by Login company Id {CompanyId}", user.CompanyId);

            var assignedInfra = await GetAssignedInfra(user);
            var (isLicense, isLicenseEmpty) = await ValidateLicense(user);
            var url = user.IsDefaultDashboard ? user.Url : string.Empty;

            return BuildAuthenticationResponse(user, userLogin, company, permissions, assignedInfra, isLicense, isLicenseEmpty, url);
        }
        catch (AuthenticationException)
        {
            // Let authentication exceptions propagate as they are already properly formatted
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during authentication for user '{LoginName}'", request.LoginName);
            throw new AuthenticationException("An unexpected error occurred during authentication", (int)ErrorCode.InvalidAuthentication);
        }
    }

    /// <summary>
    /// Validates if the provided password matches the stored password
    /// </summary>
    private bool IsPasswordValid(string inputPassword, string storedPassword)
    {
        return SecurityHelper.Decrypt(inputPassword) == SecurityHelper.Decrypt(storedPassword);
    }

    /// <summary>
    /// Verifies company if there's a mismatch between user's company and requested company
    /// </summary>
    private void VerifyCompanyIfMismatch(User user, string companyId)
    {
        if (user.CompanyId != companyId)
            VerifyCompany(user, companyId);
    }

    /// <summary>
    /// Verifies if the user account is locked
    /// </summary>
    private void VerifyUserLock(User user)
    {
        if (user.IsLock)
            throw new AuthenticationException(string.Format(Authentication.UserAccountLocked, user.LoginName),
                (int)ErrorCode.AccountLocked);
    }

    /// <summary>
    /// Gets the user's permissions based on their role
    /// </summary>
    private async Task<IEnumerable<string>> GetUserPermissions(User user)
    {
        var keyValuePairs = default(UserRole).ToDictionary();
        var accessManager = await _accessManagerRepository.GetAccessManagerByRoleId(user.Role);

        if (!keyValuePairs.Any(x => x.Value.Equals(user.Role)) && accessManager is null)
        {
            _logger.LogError(Authentication.InvalidAccess);
            throw new AuthenticationException(Authentication.InvalidAccess, (int)ErrorCode.InvalidAuthentication);
        }

        return user.Role switch
        {
            var role when role.Equals(keyValuePairs[UserRole.Operator.ToString()], StringComparison.OrdinalIgnoreCase) => Permissions.OperatorAll(),
            var role when role.Equals(keyValuePairs[UserRole.Manager.ToString()], StringComparison.OrdinalIgnoreCase) => Permissions.ManageAll(),
            var role when role.Equals(keyValuePairs[UserRole.SuperAdmin.ToString()], StringComparison.OrdinalIgnoreCase) ||
                         role.Equals(keyValuePairs[UserRole.SiteAdmin.ToString()], StringComparison.OrdinalIgnoreCase) ||
                         role.Equals(keyValuePairs[UserRole.Administrator.ToString()], StringComparison.OrdinalIgnoreCase) => Permissions.All(),
            _ => PermissionAccess.GetPermissionAccessList(accessManager?.Properties)
        };
    }

    /// <summary>
    /// Gets the assigned infrastructure for the user
    /// </summary>
    private async Task<string> GetAssignedInfra(User user)
    {
        if (user.InfraObjectAllFlag) return string.Empty;

        var result = await _userInfraObjectRepository.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId);
        return result.Properties.IsNotNullOrEmpty() ? result.Properties : string.Empty;
    }

    /// <summary>
    /// Validates the license for the user's company
    /// </summary>
    private async Task<(bool isLicense, bool isLicenseEmpty)> ValidateLicense(User user)
    {
        var license = await _licenseManagerRepository.GetLicenseExpiryDateByCompanyId(user.CompanyId);
        var isLicense = false;
        var isLicenseEmpty = false;

        if (license.Any())
        {
            isLicense = license.Any(x =>
            {
                if (DateTime.TryParseExact(x.ExpiryDate, "dd MMMM yyyy", CultureInfo.GetCultureInfo("en-IN"),
                        DateTimeStyles.None, out var expiryDate))
                    return expiryDate >= DateTime.UtcNow.Date;

                return false;
            });

            isLicenseEmpty = true;

            if (!_env.EnvironmentName.Equals("Development", StringComparison.OrdinalIgnoreCase))
            {
                var macAddress = await _licenseManagerRepository.GetMacAddress();

                if (!license.Any(licenseItem => macAddress.Any(mac => licenseItem.MacAddress.Split(',').Contains(mac))))
                {
                    isLicenseEmpty = false;
                    _logger.LogInformation("Mismatched License MAC Address");
                }
            }
        }

        return (isLicense, isLicenseEmpty);
    }

    /// <summary>
    /// Builds the authentication response with all user details
    /// </summary>
    private AuthenticationServiceResponse BuildAuthenticationResponse(User user, UserLogin userLogin, Company company, IEnumerable<string> permissions, string assignedInfra, bool isLicense,        bool isLicenseEmpty,
        string url)
    {
        return new AuthenticationServiceResponse
        {
            IsAuthorized = true,
            IsParent = company is { IsParent: true },
            LoginName = user.LoginName,
            CompanyId = user.CompanyId,
            CompanyName = user.CompanyName,
            Role = user.Role,
            RoleName = user.RoleName,
            Permissions = permissions,
            UserId = user.ReferenceId,
            SessionTimeout = user.SessionTimeout == 0 ? DefaultTimeout : user.SessionTimeout,
            IsAllInfra = user.InfraObjectAllFlag,
            AssignedInfras = assignedInfra,
            TwoFactorAuthentication = user.TwoFactorAuthentication,
            IsReset = user.IsReset,
            AuthenticationType = user.LoginType,
            IsLicenseValidity = isLicense,
            LicenseEmpty = isLicenseEmpty,
            ParentCompanyId = company?.ParentId.IsNotNullOrWhiteSpace() == true ? company.ParentId : company?.ReferenceId,
            IsDefaultDashboard = user.IsDefaultDashboard,
            Url = url,
            LastPasswordChanged = userLogin?.LastPasswordChanged
        };
    }
}