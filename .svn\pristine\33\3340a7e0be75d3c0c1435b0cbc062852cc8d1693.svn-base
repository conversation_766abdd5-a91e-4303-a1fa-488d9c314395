﻿namespace ContinuityPatrol.Application.Features.TableAccess.Commands.Update;

public class UpdateTableAccessCommand : IRequest<UpdateTableAccessResponse>
{
    public List<UpdateTableAccess> UpdateTableAccess { get; set; }
}

public class UpdateTableAccess
{
    public string Id { get; set; }
    public string TableName { get; set; }
    public string SchemaName { get; set; }
    public bool IsChecked { get; set; }

    public override string ToString()
    {
        return $"TableName: {TableName}; Id:{Id};";
    }
}