﻿namespace ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels;

public class UserViewListVm
{
    public string Id { get; set; }
    public string LoginType { get; set; }
    public string CompanyId { get; set; }
    public string CompanyName { get; set; }
    public string LoginName { get; set; }
    [Column(TypeName = "NCLOB")] public string LoginPassword { get; set; }
    public string Role { get; set; }
    public string RoleName { get; set; }
    public string RoleLogo { get; set; }
    public bool IsLock { get; set; }
    public bool IsReset { get; set; }
    public bool InfraObjectAllFlag { get; set; }
    public int SessionTimeout { get; set; }
    public bool IsVerify { get; set; }
    public string TwoFactorAuthentication { get; set; }
    public bool IsGroup { get; set; }
    public bool IsDefaultDashboard { get; set; }
    public string Url { get; set; }
    public string UserName { get; set; }
    public string Mobile { get; set; }
    public string Email { get; set; }
    public string AlertMode { get; set; }
    public bool IsPreferredMode { get; set; }
    public bool IsEmailSuccess { get; set; }
    public string LogoName { get; set; }
    [Column(TypeName = "NCLOB")] public string Properties { get; set; }
    public int IsApplication { get; set; }
    public bool IsLoggedIn { get; set; }
    public string LoginDate { get; set; }
}