using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DriftJobRepositoryTests : IClassFixture<DriftJobFixture>
{
    private readonly DriftJobFixture _driftJobFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DriftJobRepository _repository;

    public DriftJobRepositoryTests(DriftJobFixture driftJobFixture)
    {
        _driftJobFixture = driftJobFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DriftJobRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var driftJob = _driftJobFixture.DriftJobDto;

        // Act
        await _dbContext.DriftJobs.AddAsync(driftJob);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(driftJob.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftJob.Name, result.Name);
        Assert.Equal(driftJob.SolutionTypeId, result.SolutionTypeId);
        Assert.Single(_dbContext.DriftJobs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var driftJob = _driftJobFixture.DriftJobDto;
        await _dbContext.DriftJobs.AddAsync(driftJob);
        await _dbContext.SaveChangesAsync();

        driftJob.Name = "Updated Job Name";

        // Act
        _dbContext.DriftJobs.Update(driftJob);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(driftJob.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Job Name", result.Name);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var driftJob = _driftJobFixture.DriftJobDto;
        await _dbContext.DriftJobs.AddAsync(driftJob);
        await _dbContext.SaveChangesAsync();

        // Act
        driftJob.IsActive = false;

        _dbContext.DriftJobs.Update(driftJob);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var driftJob = _driftJobFixture.DriftJobDto;
        var addedEntity = await _repository.AddAsync(driftJob);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var driftJob = _driftJobFixture.DriftJobDto;
        await _repository.AddAsync(driftJob);

        // Act
        var result = await _repository.GetByReferenceIdAsync(driftJob.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftJob.ReferenceId, result.ReferenceId);
        Assert.Equal(driftJob.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var driftJobs = _driftJobFixture.DriftJobList;
        await _repository.AddRangeAsync(driftJobs);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftJobs.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var driftJob = _driftJobFixture.DriftJobDto;
        driftJob.Name = "ExistingJobName";
        await _repository.AddAsync(driftJob);

        // Act
        var result = await _repository.IsNameExist("ExistingJobName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var driftJobs = _driftJobFixture.DriftJobList;
        await _repository.AddRangeAsync(driftJobs);

        // Act
        var result = await _repository.IsNameExist("NonExistentJobName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var driftJob = _driftJobFixture.DriftJobDto;
        driftJob.Name = "SameJobName";
        await _repository.AddAsync(driftJob);

        // Act
        var result = await _repository.IsNameExist("SameJobName", driftJob.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntity()
    {
        // Arrange
        var driftJob= _driftJobFixture.DriftJobList;
        var driftJob1 = driftJob[0];
        driftJob1.Name = "ExistingJobName";
       await _dbContext.DriftJobs.AddAsync(driftJob1);
       await _dbContext.SaveChangesAsync();
        var driftJob2 = driftJob[1];
        driftJob2.Name = "DifferentJobName";
        await _dbContext.DriftJobs.AddAsync(driftJob2);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.IsNameExist("ExistingJobName", driftJob2.ReferenceId);

        // Assert
        Assert.True(result);
    }

    #endregion

    #region GetDriftJobByIdsAsync Tests

    [Fact]
    public async Task GetDriftJobByIdsAsync_ShouldReturnMatchingEntities()
    {
        // Arrange
        var driftJobs = _driftJobFixture.DriftJobList;
        await _repository.AddRangeAsync(driftJobs);

        var idsToSearch = driftJobs.Take(3).Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repository.GetDriftJobByIdsAsync(idsToSearch);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Contains(x.ReferenceId, idsToSearch));
    }

    [Fact]
    public async Task GetDriftJobByIdsAsync_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var driftJobs = _driftJobFixture.DriftJobList;
        await _repository.AddRangeAsync(driftJobs);

        var nonExistentIds = new List<string> { "non-existent-1", "non-existent-2" };

        // Act
        var result = await _repository.GetDriftJobByIdsAsync(nonExistentIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDriftJobByIdsAsync_ShouldReturnEmptyList_WhenIdsListIsEmpty()
    {
        // Arrange
        var driftJobs = _driftJobFixture.DriftJobList;
        await _repository.AddRangeAsync(driftJobs);

        var emptyIds = new List<string>();

        // Act
        var result = await _repository.GetDriftJobByIdsAsync(emptyIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDriftJobByIdsAsync_ShouldReturnPartialMatches_WhenSomeIdsExist()
    {
        // Arrange
        var driftJobs = _driftJobFixture.DriftJobList;
        await _repository.AddRangeAsync(driftJobs);

        var mixedIds = new List<string>
        {
            driftJobs.First().ReferenceId, // Exists
            "non-existent-id", // Doesn't exist
            driftJobs.Last().ReferenceId // Exists
        };

        // Act
        var result = await _repository.GetDriftJobByIdsAsync(mixedIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only the existing ones
        Assert.Contains(result, x => x.ReferenceId == driftJobs.First().ReferenceId);
        Assert.Contains(result, x => x.ReferenceId == driftJobs.Last().ReferenceId);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var driftJobs = _driftJobFixture.DriftJobList;

        // Act
        var result = await _repository.AddRangeAsync(driftJobs);

        // Assert
        Assert.Equal(driftJobs.Count, result.Count());
        Assert.Equal(driftJobs.Count, _dbContext.DriftJobs.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var driftJobs = _driftJobFixture.DriftJobList;
        await _repository.AddRangeAsync(driftJobs);

        // Act
        var result = await _repository.RemoveRangeAsync(driftJobs);

        // Assert
        Assert.Equal(driftJobs.Count, result.Count());
        Assert.Empty(_dbContext.DriftJobs);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var driftJobs = _driftJobFixture.DriftJobList.Take(5).ToList();
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(driftJobs);
        var initialCount = driftJobs.Count;
        
        var toUpdate = driftJobs.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedJobName");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = driftJobs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.Name == "UpdatedJobName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
