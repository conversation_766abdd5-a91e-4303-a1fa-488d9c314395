﻿using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator.Helper;

namespace ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator;

public class CreateBulkImportValidatorCommandValidator : AbstractValidator<BulkImportValidatorCommand>
{
    public CreateBulkImportValidatorCommandValidator(IServerRepository serverRepository,
        IDatabaseRepository databaseRepository,
        IReplicationRepository replicationRepository, IInfraObjectRepository infraObjectRepository)

    {
        //RuleFor(e => e)
        //    .MustAsync(IsGlobalSettingEnable)
        //    .WithMessage("The bulk import feature is not enabled in the global settings.");


        RuleForEach(x => x.ServerList)
            .NotEmpty()
            .SetValidator(new CreateBulkImportComponentServerValidator(serverRepository))
            .NotNull();

        RuleForEach(x => x.DatabaseList)
            .NotEmpty()
            .SetValidator(new CreateBulkImportComponentDataBaseValidator(databaseRepository))
            .NotNull();

        RuleForEach(x => x.ReplicationList)
            .NotEmpty()
            .SetValidator(new CreateBulkImportComponentReplicationValidator(replicationRepository))
            .NotNull();

        RuleFor(x => x.InfraObject)
            .NotEmpty()
            .SetValidator(new CreateBulkImportComponentInfraObjectValidator(infraObjectRepository))
            .NotNull();
    }
}