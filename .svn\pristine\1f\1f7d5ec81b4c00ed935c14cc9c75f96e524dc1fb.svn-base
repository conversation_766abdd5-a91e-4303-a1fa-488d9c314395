﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Node.Events.Update;

public class NodeUpdatedEventHandler : INotificationHandler<NodeUpdatedEvent>
{
    private readonly ILogger<NodeUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public NodeUpdatedEventHandler(ILoggedInUserService userService, ILogger<NodeUpdatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(NodeUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.Node.ToString(),
            Action = $"{ActivityType.Update} {Modules.Node}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $" Node '{updatedEvent.NodeName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Node '{updatedEvent.NodeName}' updated successfully.");
    }
}