﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title"><i class="cp-monitoring"></i><span>Oracle Full DB - NetApp-SnapMirror</span></h6>
        <span><i class="cp-time me-2"></i>Last Monitored Time : 12/9/2022 1:39:31 PM</span>
    </div>
    <div class="monitor_pages">
        <div class="row g-2 mt-0">
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                        Multi Tenancy
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>Multi Tenancy</th>
                                    <th class="text-primary">Primary</th>
                                    <th>DR</th>

                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-control-file-type me-1"></i>CDB</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>


                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-open-mode me-1"></i>Containers</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-open-mode me-1"></i>PDBs</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center pt-0">
                        @* <img src="~/img/isomatric/solutiondiagram.svg" height="159px;" /> *@
                        <div id="Solution_Diagram" class="w-50"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-2">

            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title">
                        Instance Details
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>
                                        Instance Details
                                    </th>
                                    <th class="text-primary">Primary</th>
                                    <th>DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-instance-name me-1"></i>INSTANCE NAME</td>
                                    <td class="text-truncate">QAODG11G </td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-instance-id me-1"></i>INSTANCE ID</td>
                                    <td class="text-truncate">1 </td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-coord-Time me-1"></i>INSTANCE STARTUP TIME</td>
                                    <td class="text-truncate">13-06-2023 12:46:51</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-open-mode me-1"></i>OPEN MODE </td>
                                    <td class="text-truncate"><i class="text-warning cp-file-edits me-1"></i>READ WRITE</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-cluster-database me-1"></i>CLUSTER DATABASE</td>
                                    <td class="text-truncate"><i class="text-success cp-estimated-time me-1"></i>No</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-ip-address me-1"></i>CONTROL FILE NAME</td>
                                    <td class="text-truncate"><i class="text-warning cp-file-edits me-1"></i>/u01/app/oracle/o..</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-ip-address me-1"></i>PARAMETER FILE</td>
                                    <td class="text-truncate"><i class="text-warning cp-file-edits me-1"></i>/u01/app/oracle/o..</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-ip-address me-1"></i>PARAMETER NAME</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NO</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-0" style="height:calc(100vh - 283px);overflow:auto">
                    <div class="card-header card-title">
                        Database Details
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead class="position-sticky top-0 z-3">
                                <tr>
                                    <th>
                                        Database Details
                                    </th>
                                    <th class="text-primary">Primary</th>
                                    <th>DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-database me-1"></i>DATABASE NAME</td>
                                    <td class="text-truncate">QAODG11G </td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-database-role me-1"></i>DATABASE ROLE</td>
                                    <td class="text-truncate">PRIMARY </td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-database-unique me-1"></i>DATABASE UNIQUE NAME</td>
                                    <td class="text-truncate">QAODG11G </td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-open-mode me-1"></i>OPEN MODE </td>
                                    <td class="text-truncate"><i class="text-warning cp-file-edits me-1"></i>READ WRITE</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                            
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-coord-Time me-1"></i>DATABASE CREATED TIME</td>
                                    <td class="text-truncate">13-06-2023 12:46:51</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-control-file-type me-1"></i>CONTROLFILE TYPE</td>
                                    <td class="text-truncate"><i class="text-warning cp-file-edits me-1"></i>CURRENT</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-current-scn me-1"></i>CURRENT SCN</td>
                                    <td class="text-truncate">40248444</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-warning me-1"></i>FLASHBACK_ON</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NO</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-datas me-1"></i>DATABASE VERSION</td>
                                    <td class="text-truncate">11.2.0.1.0</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-database me-1"></i>DATABASE INCARNATION</td>
                                    <td class="text-truncate">2 </td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-refresh me-1"></i>RESETLOGS CHANGE</td>
                                    <td class="text-truncate">945184</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-reset-log-change me-1"></i>RESETLOGS MODE</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NOT ALLOWED</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr> 
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-refresh me-1"></i>ARCHIVE MODE</td>
                                    <td class="text-truncate">ARCHIVELOG</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-storage-name me-1"></i>DB SIZE (in MB)</td>
                                    <td class="text-truncate">ARCHIVELOG</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-db-create-online me-1"></i>DB CREATE FILE DEST </td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-database me-1"></i>DB FILE NAME CONVERT</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>   
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-ip-address me-1"></i>DB CREATE ONLINE LOG DEST1</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr> 
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-log-file-name me-1"></i>LOG FILE NAME CONVERT</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>  
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-roate-settings me-1"></i>DB RECOVERY FILE DEST</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr> 
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-roate-settings me-1"></i>DB RECOVERY FILE DEST SIZE</td>
                                    <td class="text-truncate">/u01/app/oracle/f..</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr> 
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-warning me-1"></i>DB FLASHBACK RETENTION TARGET</td>
                                    <td class="text-truncate">1440</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>NA</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title">
                        OracleFullDB_NetappSnapmirror_Infraobject
                    </div>
                    <div class="card-body pt-0 p-2">
                  No data found image
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title">
                        Replication Details
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>
                                        Replication Monitor
                                    </th>
                                    <th class="text-primary"></th>
                                   
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-replication-on me-1"></i>Replication Type</td>
                                    <td class="text-truncate">Storage NetApp SnapMirror </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-replication-source me-1"></i>Source</td>
                                    <td class="text-truncate">fas3020:SOL_Snapmirror_1GB_1</td>
                                   
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-file-location me-1"></i>Destination</td>
                                    <td class="text-truncate">fas3020:SOL_Snapmirror_1GB_1</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-park-solid-group me-1"></i>State</td>
                                    <td class="text-truncate">Snapmirrored</td>
                                  
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-data-lag me-1"></i>Lag</td>
                                    <td class="text-truncate"><i class="text-success cp-estimated-time me-1"></i>559:23:54</td>
                                   
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-site-names me-1"></i>Status </td>
                                    <td class="text-truncate"><i class="text-success cp-Idle me-1"></i>Idle</td>
                                </tr>
                              
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-12 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title">
                        ASM Details
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>
                                      
                                    </th>
                                    <th class="text-primary">Primary</th>
                                    <th>DR</th>

                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-asm me-1"></i>ASM DG Details</td>
                                    <td class="text-truncate">
                                        <table class="table mb-0" style="table-layout:fixed">
                                            <thead>
                                                <tr>
                                                    <th>
                                                        #
                                                    </th>
                                                    <th class="text-primary">STATE</th>
                                                    <th class="text-primary">TYPE</th>
                                                    <th class="text-primary">TOTAL_MB</th>
                                                    <th class="text-primary">FREE_MB</th>
                                                    <th class="text-primary">USED(%)</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>0</td>
                                                    <td>NA</td>
                                                    <td>NA</td>
                                                    <td>NA</td>
                                                    <td>NA</td>
                                                    <td>0</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td class="text-truncate">
                                        <table class="table mb-0" style="table-layout:fixed">
                                            <thead>
                                                <tr>
                                                    <th>
                                                        #
                                                    </th>
                                                    <th class="text-primary">STATE</th>
                                                    <th class="text-primary">TYPE</th>
                                                    <th class="text-primary">TOTAL_MB</th>
                                                    <th class="text-primary">FREE_MB</th>
                                                    <th class="text-primary">USED(%)</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>0</td>
                                                    <td>NA</td>
                                                    <td>NA</td>
                                                    <td>NA</td>
                                                    <td>NA</td>
                                                    <td>0</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-12 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title">
                        TNS Service Details
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>
                                        TNS Service Details
                                    </th>
                                    <th class="text-primary">Primary</th>
                                    <th>DR</th>

                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-asm me-1"></i>Services</td>
                                    <td class="text-truncate">
                                        sudo: 3 incorrect password attempt
                                    </td>
                                    <td class="text-truncate">
                                        NA
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-12 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                        Services
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>
                                        Service Name
                                    </th>
                                    <th class="text-primary">Server IP/HostName</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-database-warning me-1"></i>MSSQL Server</td>
                                    <td class="text-truncate"><i class="text-success cp-fal-server me-1"></i>**************</td>
                                    <td class="text-truncate"><i class="text-primary cp-reload me-1 cp-animate"></i>Running</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-log-file-name me-1"></i>MSSQL Server</td>
                                    <td class="text-truncate"><i class="text-success cp-fal-server me-1"></i>**************</td>
                                    <td class="text-truncate"><i class="text-primary cp-reload me-1 cp-animate"></i>Running</td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>


    </div>
</div>