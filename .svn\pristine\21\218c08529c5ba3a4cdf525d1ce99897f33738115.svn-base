﻿using ContinuityPatrol.Application.Features.Job.Commands.UpdateJobState;
using ContinuityPatrol.Application.Features.Job.Commands.UpdateJobStatus;
using ContinuityPatrol.Application.Features.Job.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Create;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Delete;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Update;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.UpdateReplicationJobState;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.UpdateReplicationJobStatus;
using ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetList;
using ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetPaginationList;
using ContinuityPatrol.Application.Features.ReplicationJob.Queries.IsNameUnique;
using ContinuityPatrol.Domain.ViewModels.ReplicationJobModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class ReplicationJobController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<ReplicationJobListVm>>> GetReplicationJobList()
    {
        Logger.LogDebug("Get all ReplicationJob");

        return await Mediator.Send(new GetReplicationJobListQuery());
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateReplicationJobResponse>> CreateReplicationJob([FromBody] CreateReplicationJobCommand createReplicationJobCommand)
    {
        Logger.LogDebug($"Create ReplicationJob '{createReplicationJobCommand.Name}'");


        return CreatedAtAction(nameof(CreateReplicationJob), await Mediator.Send(createReplicationJobCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateReplicationJobResponse>> UpdateReplicationJob([FromBody] UpdateReplicationJobCommand  updateReplicationJobCommand)
    {
        Logger.LogDebug($"Update ReplicationJob'{updateReplicationJobCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateReplicationJobCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Admin.Delete)]
    public async Task<ActionResult<DeleteReplicationJobResponse>> DeleteReplicationJob(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ReplicationJob Id");

        Logger.LogDebug($"Delete ReplicationJob Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteReplicationJobCommand { Id = id }));
    }

    [HttpGet("{id}", Name = "ReplicationJob")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<ReplicationJobListVm>> GetReplicationJobById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ReplicationJob Id");

        Logger.LogDebug($"Get ReplicationJob Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetReplicationJobDetailQuery { JobId = id }));
    }

    [HttpPut("updatereplicationjobstatus")]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateReplicationJobStatusResponse>> UpdateReplicationJobStatus([FromBody] UpdateReplicationJobStatusCommand updateReplicationJobStatus)
    {
        Logger.LogDebug($"Updating Replication Job '{updateReplicationJobStatus.Name}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateReplicationJobStatus));
    }

    [HttpPut("updatereplicationjobstate")]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateReplicationJobStateResponse>> UpdateReplicationJobState([FromBody] UpdateReplicationJobStateCommand updateReplicationJobStateCommand)
    {
        Logger.LogDebug($"Updating Replication Job State TO Count '{updateReplicationJobStateCommand.UpdateReplicationJobStates.Count}'.");

        ClearDataCache();

        return Ok(await Mediator.Send(updateReplicationJobStateCommand));
    }
    [Route("name-exist"), HttpGet]
    public async Task<ActionResult> IsReplicationJobNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "Replication Job Name");

        Logger.LogDebug($"Check Name Exists Detail by Replication Job Name '{name}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetReplicationJobNameUniqueQuery { Name = name, Id = id }));
    }


    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<PaginatedResult<ReplicationJobListVm>>> GetPaginated([FromQuery] GetReplicationJobPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in ReplicationJob Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllReplicationJobCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllPageReplicationJobNameCacheKey };

        ClearCache(cacheKeys);
    }
}
