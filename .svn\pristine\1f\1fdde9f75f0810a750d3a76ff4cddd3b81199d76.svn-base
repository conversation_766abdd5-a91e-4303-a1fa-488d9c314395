﻿let mappingModuleMethods = {
    GetZoneList: "CyberResiliency/Component/GetSiteList",
    GetMappingList: "CyberResiliency/Manage/GetMappingList",
    GetComponentsBySite: "CyberResiliency/Manage/GetComponentsBySiteId",
    createOrUpdate: "CyberResiliency/Manage/CreateOrUpdate",
    GetAirgapList: "CyberResiliency/Manage/GetAirgapList",
    GetCyberAirGapList: "CyberResiliency/Manage/GetCyberAirGapService"
}

let airGapArray = []
let globalAirgapArray = [];
let mappingProperty;

const getRandomId = (value) => {
    return value + "_" + Math.floor(Math.random() * 11110 * Math.random() * 103180)
}

const getCyberAirgapList = async () => {

    await $.ajax({
        type: "GET",
        url: RootUrl + mappingModuleMethods?.GetAirgapList,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.data) {
                if (Array.isArray(result?.data) && result?.data?.length) {
                    globalAirgapArray = result?.data
                }
            }
        }
    })
}

getCyberAirgapList();

const getComponentMappingList = async () => {

    await $.ajax({
        type: "GET",
        url: RootUrl + mappingModuleMethods?.GetMappingList,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.data) {
                if (Array.isArray(result?.data) && result?.data?.length) {
                    let parsedData = result?.data[0]?.properties && JSON.parse(result?.data[0]?.properties)
                    mappingProperty = parsedData
                    setComponentmapping(parsedData)
                } else {
                    $('#parentDiagramContainer').empty().append('<img src="/../../img/isomatric/no_data_found.svg" class="Card_NoData_Img" style="margin-left: 35%;margin-top: 7%;">')
                }
            }
        }
    })
}

getComponentMappingList();

$(function () {
    $('#diagramaticContainer').droppable({

        accept: '.draggableCont, .siteDraggableContainer',
        drop: function (event, ui) {
            checkCanvasPosition()
        }

    })
})

const checkCanvasPosition = () => {
    $('.cyberAirgapImage').remove()
    let canvasData = $('.flowLineAdded')

    for (let i = 0; i < canvasData?.length; i++) {
        let Id = canvasData[i]?.id
        if ($(`#${Id}`).attr('details')?.length) {
            let canvasDetails = JSON.parse($(`#${Id}`)?.attr('details'))

            if (canvasDetails?.length) {
                canvasDetails?.forEach((d) => {
                    let flowLineId = d?.flowLineId
                    $(`#${flowLineId} polyline`).remove();
                    let endId = $(`#${flowLineId} defs`)?.children()?.first()[0]?.id
                    connectSVGLine(flowLineId, d?.sourceId, d?.targetId, d?.status, d?.airgapId, d?.airgapName, d?.color, endId)
                })
            }
        }
    }
}

const setComponentmapping = (mappingData) => {
    let airgapArr = []  
    let positionObj = []
    let componentCont = ''
    reduceSize = 0;

    mappingData?.length && mappingData.forEach((d, i) => {
        let zoneId = getRandomId('zone');
        let containerId = getRandomId('container')
        let parsedData = d?.properties
        let parsedDataLength = parsedData?.length
        let getSiteColor = d?.siteColor

        if (d?.airgapArray?.length) airgapArr = airgapArr.concat(d?.airgapArray)

        positionObj.push({ id: containerId, top: d?.position?.top, left: d?.position?.left })

        componentCont += `<div class="siteDraggableContainer" id="${containerId}" style="width:${parsedDataLength >= 3 ? '40%' : 'fit-content'}">
    <div class="card drag-card zoneContainer mb-0" id="${zoneId}" style="width:100%;border:1px solid ${getSiteColor}">
     <div class="card-header siteNameCont card-title" id="${d?.siteId}">${d?.siteName}</div>
    <div class="card-body pt-0 p-2">`

        for (let i = 0; i < parsedDataLength; i++) {
            if (parsedData[i]?.hasOwnProperty('isGroup')) {

                let getGroupColor = parsedData[i]?.groupColor
                let ParallelId = getRandomId('parallel')
                componentCont += `<div class='row w-50 mx-0 px-2'><div class='parallelCont p-2 w-100' id="${ParallelId}" style="border:1px solid ${getGroupColor}"><div class="w-100 fw-semibold compGroupName px-2" id="${parsedData[i]?.id}">${parsedData[i]?.name}</div>`

                parsedData[i]?.groupArray?.length && parsedData[i]?.groupArray.forEach((d) => {
                    componentCont += `<div class="componentContainer ${parsedData[i]?.groupArray?.length > 1 ? 'w-50' : ''} mb-1" id="${d?.id}">
                                <div class="text-center">
                                    <p class="mb-0 fs-10 fw-semibold componentName">${d?.name}</p>
                                    <div class="position-relative componentIcon" id="${getRandomId('component')}" serverType="${d?.serverType}">
                                        <img src=${d?.icon} height="30px" width="30px" />
                                       <span class="componentCount ${!Number(d?.count) ? 'd-none' : ''}" style="position: absolute;bottom: -3px;right: 28px;background-color: #0d6efd;padding: 2px 5px;border-radius: 50%;color: #fff;font-size: 10px;" value="${d?.count}">+${d?.count}</span>
                                    </div>
                                </div>
                            </div>`
                });
                componentCont += `</div></div>`;
            }
        }

        componentCont += `<div class="row nonGroupComps ${parsedDataLength >= 8 ? '' : parsedDataLength >= 5 ? '' : ''} mx-0" >`

        for (let i = 0; i < parsedDataLength; i++) {
            if (!parsedData[i]?.hasOwnProperty('isGroup')) {

                let componentId = getRandomId('component')
                componentCont += `<div class="componentContainer ${parsedDataLength == 1 ? 'col-12' : parsedDataLength < 3 ? 'col-6' : (parsedDataLength == 3) ? 'col-4' : 'col-3'} mb-1" id="${parsedData[i]?.id}">
                        <div class="text-center">
                            <p class="fw-semibold fs-9 componentName mb-0">${parsedData[i]?.name}</p>
                            <div class="position-relative componentIcon" id="${componentId}" serverType="${parsedData[i]?.serverType}">
                                <img src=${parsedData[i]?.icon} height="30px" width="30px"/>  
                                <span class="componentCount ${!Number(parsedData[i]?.count) ? 'd-none' : ''}" style="position: absolute;bottom: -8px;right: ${parsedDataLength > 2 ? '25' : '0'}px;padding: 2px 5px;border-radius: 50%;color: #0d6efd;font-size: 11px;" value="${parsedData[i]?.count}">+${parsedData[i]?.count}</span>
                            </div>
                        </div>
                    </div>`
            }
        }
        componentCont += `</div></div></div></div>`
    })

    $('#cyberContainer').append('<div class="emptyClass" style="height:50px;width:100%"></div>').append(componentCont).append('<div class="emptyClass" style="height:50px;width:100%"></div>')
    $('#cyberContainer').children().not('.emptyClass').last().prev().css('top', '30px')

    setTimeout(() => {
        $(`.siteDraggableContainer`).draggable()
        setTimeout(() => {
            positionObj.forEach((d, i) => {
                $(`#${d?.id}`).css('left', `${d?.left}px`)
                $(`#${d?.id}`).css('top', `${d?.top}px`)
            })
        }, 100)

        setTimeout(() => {
            if (airgapArr?.length) createConnectionFlow(airgapArr)
        }, 800)
    }, 300)
}

$('#fullScreenView').on('click', function () {
    $('#DiagramContModal').modal('show')
})

const createConnectionFlow = (airgapArr) => {
    let ajaxPromises = airgapArr?.length && airgapArr?.map((d) => {
        let flowLineId = getRandomId('flowline');
        let data = { airGapId: d?.airgapId };

        return $.ajax({
            type: "GET",
            url: RootUrl + mappingModuleMethods?.GetCyberAirGapList,
            data: data,
            dataType: "json",
            traditional: true
        }).then((result) => {
            if (result?.success) {
                let details = {
                    sourceId: d?.sourceId, targetId: d?.targetId, status: result?.data?.status, airgapId: d?.airgapId,
                    airgapName: d?.airgapName, color: d?.color, flowLineId: flowLineId
                };

                let $sourceElement = $(`#${d?.sourceId}`);
                let existingDetails = $sourceElement?.attr('details');
                let airGapDetails = existingDetails ? JSON.parse(existingDetails) : [];
                airGapDetails.push(details);
                $sourceElement.attr('details', JSON.stringify(airGapDetails));

                $(`#${d?.sourceId}, #${d?.targetId}`).addClass('flowLineAdded').attr('flowLineId', flowLineId);
                $(`#${d?.sourceId}`).attr('partnerId', d?.targetId);
                $(`#${d?.targetId}`).attr('partnerId', d?.sourceId);
                airGapArray.push({ source: d?.sourceId, target: d?.targetId });

                let endArrowId = getRandomId('arrow');
                let svgLine = `<svg id="${flowLineId}" width="${$('#cyberContainer').width()}" height="${$('#cyberContainer').height()}" style="position:absolute;">
                    <defs>
                        <marker id='${endArrowId}' markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                            <path d="M0,0 L10,5 L0,10 Z" fill="${d.color}" />
                        </marker>
                    </defs>
                </svg>`;
                $('#cyberContainer').prepend(svgLine);

                let getAirGapId = globalAirgapArray?.length && globalAirgapArray.find((k) => k?.name === d?.airgapName);
                let airGapid = d?.airgapId || getAirGapId?.id;

                connectSVGLine(flowLineId, d?.sourceId, d?.targetId, result?.data?.status, airGapid, d?.airgapName, d?.color, endArrowId);
            }
        });
    });

    Promise.all(ajaxPromises)
        .then(() => {
            convertToImage();
        })
        .catch((error) => {
            console.error("One or more AJAX requests failed:", error);
        });
};

const drawPolyLine = (connectPoints, points = []) => {
    points?.length && points.forEach((point) => connectPoints.push(point))
}

const connectSVGLine = (lineId, sourceId, targetId, status, airgapId, airgapName, color, endArrowId) => {
  
    let imageSrc = ''

    status?.toLowerCase() === 'close' || status?.toLowerCase() === 'disable' || status?.toLowerCase() === 'lock' ?
        imageSrc = '/../img/Component_icons/airgap-off-vertical.svg' : imageSrc = '/../img/Component_icons/airgap-on-vertical.svg';

    let connectPoints = [];
    let points = [];
    let sourceContainerId = $(`#${sourceId}`)?.parents('.siteDraggableContainer')[0]?.id
    let targetContainerId = $(`#${targetId}`)?.parents('.siteDraggableContainer')[0]?.id

    let sourceComponentPositionX = $(`#${sourceContainerId}`)?.position()?.left + ($(`#${sourceContainerId}`)?.width())
    let sourceComponentPositionY = $(`#${sourceContainerId}`)?.position()?.top + ($(`#${sourceContainerId}`)?.height())
    let targetComponentPositionX = $(`#${targetContainerId}`)?.position()?.left + ($(`#${targetContainerId}`)?.width())
    let targetComponentPositionY = $(`#${targetContainerId}`)?.position()?.top + ($(`#${targetContainerId}`)?.height())

    let sourcePorts = getPortPositions($(`#${sourceContainerId}`));
    let targetPorts = getPortPositions($(`#${targetContainerId}`));

    const nearestPortPair = findNearestPortPair(sourcePorts, targetPorts, sourceContainerId, targetContainerId, sourceId, targetId, airgapId);

    let startX = nearestPortPair?.source?.x;
    let startY = nearestPortPair?.source?.y;
    let endX = nearestPortPair?.target?.x;
    let endY = nearestPortPair?.target?.y;

    const midX = startX + (endX - startX) / 2;
    const midY = startY - ((startY - endY) / 2);

    const imageWidth = 27;
    const imageHeight = 26
    const adjustedX = (imageWidth / 2);
    const adjustedY = (imageHeight / 2);

    let imageLeft = endX;
    let imageTop = startY + adjustedY;

    if (targetComponentPositionY > sourceComponentPositionY) {
        // target is below source

        if (sourceComponentPositionY < $(`#${targetContainerId}`).position().top) {

            ({ points, imageLeft } = connectLinesTargetBelowSource(nearestPortPair, startX, startY, midX, midY, endX, endY, sourceComponentPositionX, imageLeft, points))

        } else {

            ({ points, imageLeft, imageTop, imageSrc } = connectStraightLines(nearestPortPair, startX, startY, midX, endX, endY, sourceContainerId, sourceComponentPositionY, status, imageLeft, imageTop, imageSrc, adjustedY, points))
        }

    } else if (targetComponentPositionY < sourceComponentPositionY) {
        // target is above source

        if ($(`#${sourceContainerId}`).position().top > targetComponentPositionY) {

            ({ points, imageLeft } = connectLinesTargetAboveSource(nearestPortPair, startX, startY, midX, midY, endX, endY, sourceComponentPositionX, imageLeft, points));
            imageTop = endY + adjustedY

        } else {

            ({ points, imageLeft, imageTop, imageSrc } = connectStraightLines(nearestPortPair, startX, startY, midX, endX, endY, sourceContainerId, sourceComponentPositionY, status, imageLeft, imageTop, imageSrc, adjustedY, points))

        }
    }

    drawPolyLine(connectPoints, points)
    $('#cyberContainer cyberAirgapImage').remove();
    $('#cyberContainer').append(`<img class="position-absolute cyberAirgapImage" src='${imageSrc}' flowLineId="${lineId}" airgapid="${airgapId}" style="top:${imageTop}px;left:${imageLeft - adjustedX}px" />`)

    createPolylineWithArrow(connectPoints, color, lineId, endArrowId)

}

// lines for target below source
const connectLinesTargetBelowSource = (nearestPortPair, startX, startY, midX, midY, endX, endY, sourceComponentPositionX, imageLeft, points) => {

    if ((nearestPortPair?.source?.id == 'rightMiddle' && nearestPortPair?.target?.id == 'leftMiddle') || (nearestPortPair?.source?.id == 'leftMiddle' && nearestPortPair?.target?.id == 'rightMiddle')) {

        points = [[startX, startY], [midX, startY], [midX, endY], [endX, endY]]
        imageLeft = midX

    } else if (nearestPortPair?.source?.id == 'bottomMiddle' && (nearestPortPair?.target?.id == 'leftMiddle' || nearestPortPair?.target?.id == 'rightMiddle')) {
        points = [[startX, startY], [startX, endY], [endX, endY]]
        imageLeft = startX

    } else if (nearestPortPair?.source?.id == 'bottomMiddle' && nearestPortPair?.target?.id == 'topMiddle') {
        points = [[endX, startY], [endX, endY]]

        if (sourceComponentPositionX < endX) {
            points = [[startX, startY], [startX, midY], [endX, midY], [endX, endY]]
            imageLeft = startX
        }

    } else if ((nearestPortPair?.source?.id == 'rightMiddle' || nearestPortPair?.source?.id == 'leftMiddle') && nearestPortPair?.target?.id == 'topMiddle') {
        points = [[startX, startY], [endX, startY], [endX, endY]]

    } else if ((nearestPortPair?.source?.id == 'rightMiddle' && nearestPortPair?.target?.id == 'rightMiddle') || (nearestPortPair?.source?.id == 'leftMiddle' && nearestPortPair?.target?.id == 'leftMiddle')) {
        let midPoint = nearestPortPair?.source?.id == 'rightMiddle' ? startX + 50 : startX - 50

        points = [[startX, startY], [midPoint, startY], [midPoint, endY], [endX, endY]]
        imageLeft = midPoint
    }

    return { points, imageLeft }
}

// lines for target above source
const connectLinesTargetAboveSource = (nearestPortPair, startX, startY, midX, midY, endX, endY, sourceComponentPositionX, imageLeft, points) => {

    if ((nearestPortPair?.source?.id == 'rightMiddle' && nearestPortPair?.target?.id == 'leftMiddle') || (nearestPortPair?.source?.id == 'leftMiddle' && nearestPortPair?.target?.id == 'rightMiddle')) {

        points = [[startX, startY], [midX, startY], [midX, endY], [endX, endY]]
        imageLeft = midX

    } else if (nearestPortPair?.source?.id == 'topMiddle' && (nearestPortPair?.target?.id == 'leftMiddle' || nearestPortPair?.target?.id == 'rightMiddle')) {
        points = [[startX, startY], [startX, endY], [endX, endY]]
        imageLeft = startX

    } else if ((nearestPortPair?.source?.id == 'leftMiddle' || nearestPortPair?.source?.id == 'rightMiddle') && nearestPortPair?.target?.id == 'bottomMiddle') {

        points = [[endX, endY], [endX, startY], [startX, startY]]

    } else if (nearestPortPair?.source?.id == 'topMiddle' && nearestPortPair?.target?.id == 'bottomMiddle') {
        points = [[endX, endY], [endX, startY]]

        if (sourceComponentPositionX < endX) {
            points = [[startX, startY], [startX, midY], [endX, midY], [endX, endY]]
            imageLeft = startX
        }

    } else if ((nearestPortPair?.source?.id == 'rightMiddle' && nearestPortPair?.target?.id == 'rightMiddle') || (nearestPortPair?.source?.id == 'leftMiddle' && nearestPortPair?.target?.id == 'leftMiddle')) {
        let midPoint = nearestPortPair?.source?.id == 'rightMiddle' ? startX + 50 : startX - 50

        points = [[startX, startY], [midPoint, startY], [midPoint, endY], [endX, endY]]
        imageLeft = midPoint
    } else if ((nearestPortPair?.source?.id == 'rightMiddle' || nearestPortPair?.source?.id == 'leftMiddle') && nearestPortPair?.target?.id == 'topMiddle') {
        let midPoint = nearestPortPair?.source?.id == 'rightMiddle' ? startX + 50 : startX - 50

        points = [[startX, startY], [midPoint, startY], [midPoint, endY - 50], [endX, endY - 50], [endX, endY]];
        imageLeft = midPoint
    }

    return { points, imageLeft }
}

// straight lines from target to source
const connectStraightLines = (nearestPortPair, startX, startY, midX, endX, endY, sourceContainerId, sourceComponentPositionY, status, imageLeft, imageTop, imageSrc, adjustedY, points) => {
    if ((nearestPortPair?.source?.id == 'rightMiddle' && nearestPortPair?.target?.id == 'leftMiddle') || (nearestPortPair?.source?.id == 'leftMiddle' && nearestPortPair?.target?.id == 'rightMiddle')) {

        if ($(`#${sourceContainerId}`)?.position()?.top > endY) {
            points = [[startX, startY], [midX, startY], [midX, endY], [endX, endY]]

            imageLeft = midX
            imageTop = endY + adjustedY

        } else if (sourceComponentPositionY < endY) {
            points = [[startX, startY], [midX, startY], [midX, endY], [endX, endY]]

            imageLeft = midX
            imageTop = startY + adjustedY

        } else {

            imageSrc = ['close', 'disable', 'lock'].includes(status?.toLowerCase()) ? '/../img/Component_icons/airgap_off.svg' : '/../img/Component_icons/airgap_on.svg';
            points = [[startX, endY], [endX, endY]]

            imageLeft = (startX + endX) / 2
            imageTop = endY - adjustedY
        }

    } else if (nearestPortPair?.source?.id == 'bottomMiddle' && (nearestPortPair?.target?.id == 'leftMiddle' || nearestPortPair?.target?.id == 'rightMiddle')) {
        points = [[startX, startY], [startX, endY], [endX, endY]]
        imageLeft = startX

    } else if ((nearestPortPair?.source?.id == 'rightMiddle' || nearestPortPair?.source?.id == 'leftMiddle') && nearestPortPair?.target?.id == 'topMiddle') {
        points = [[startX, startY], [endX, startY], [endX, endY]]

    } else if ((nearestPortPair?.source?.id === 'topMiddle' && nearestPortPair?.target?.id === 'topMiddle') ||
        (nearestPortPair?.source?.id === 'bottomMiddle' && nearestPortPair?.target?.id === 'bottomMiddle')) {

        let controlY = nearestPortPair?.source?.id === 'topMiddle'
            ? Math.min(startY, endY) - 25
            : Math.max(startY, endY) + 25;

        points = [
            [startX, startY], [startX, controlY], [endX, controlY], [endX, endY]
        ];

        imageLeft = (startX + endX) / 2;
        imageTop = controlY - adjustedY;

        imageSrc = ['close', 'disable', 'lock'].includes(status?.toLowerCase()) ? '/../img/Component_icons/airgap_off.svg' : '/../img/Component_icons/airgap_on.svg';
    }

    return { points, imageLeft, imageTop, imageSrc }
}

function calculateDistance(point1, point2) {
    const dx = point1?.x - point2?.x;
    const dy = point1?.y - point2?.y;
    return Math.sqrt(dx * dx + dy * dy);
}

function findNearestPortPair(sourcePorts, targetPorts, sourceContainerId, targetContainerId, sourceId, targetId, airgapId) {
    let nearestPair = { source: null, target: null };
    let minimumDistance = Infinity;
    let sourceContainerOriginalPorts = $(`#${sourceContainerId}`).attr('portDetails')
    let targetContainerOriginalPorts = $(`#${targetContainerId}`).attr('portDetails')

    sourceContainerOriginalPorts = sourceContainerOriginalPorts ? JSON.parse(sourceContainerOriginalPorts) : []
    targetContainerOriginalPorts = targetContainerOriginalPorts ? JSON.parse(targetContainerOriginalPorts) : []

    sourcePorts?.length && sourcePorts.forEach(sourcePort => {

        if (isPortOccupied(sourcePort, sourceContainerOriginalPorts)) return;

        targetPorts.forEach(targetPort => {

            if (isPortOccupied(targetPort, targetContainerOriginalPorts)) return;

            const distance = calculateDistance(sourcePort, targetPort);
            if (distance < minimumDistance) {
                minimumDistance = distance;
                nearestPair = { source: sourcePort, target: targetPort };
            }
        });
    });

    if (nearestPair) setPortOccupied(sourceContainerId, targetContainerId, sourceId, targetId, nearestPair, airgapId)

    return nearestPair;
}

function setPortOccupied(source, target, sourceId, targetId, nearestPair, airgapId) {

    const sourceContainerOriginalPorts = JSON.parse($(`#${source}`).attr('portDetails') || '[]');
    const targetContainerOriginalPorts = JSON.parse($(`#${target}`).attr('portDetails') || '[]');

    if (nearestPair) {
        sourceContainerOriginalPorts.push({ sourceId, targetId, port: nearestPair?.source?.id, airgapId })
        targetContainerOriginalPorts.push({ sourceId, targetId, port: nearestPair?.target?.id, airgapId })
    }

    $(`#${source}`).attr('portDetails', JSON.stringify(sourceContainerOriginalPorts))
    $(`#${target}`).attr('portDetails', JSON.stringify(targetContainerOriginalPorts))
}

function isPortOccupied(ports, occupiedDetails = []) {
    return occupiedDetails.some(portObj => portObj?.port?.toLowerCase() == ports?.id?.toLowerCase())
}

function getPortPositions($div) {
    let offset = $div?.position();
    let width = $div?.width();
    let height = $div?.height();

    return [
        {
            id: 'topMiddle',
            x: offset?.left + width / 2,
            y: offset?.top
        },
        {
            id: 'leftMiddle',
            x: offset?.left,
            y: offset?.top + height / 2
        },
        {
            id: 'rightMiddle',
            x: offset?.left + width,
            y: offset?.top + height / 2
        },
        {
            id: 'bottomMiddle',
            x: offset?.left + width / 2,
            y: offset?.top + height
        }
    ];
}

function createPolylineWithArrow(points, color, lineId, endArrowId) {

    setTimeout(() => {
        $(`#${lineId} polyline`).remove();
        const polyline = document.createElementNS("http://www.w3.org/2000/svg", "polyline");
        color = color || 'blue'
        $(polyline).attr({
            points: points?.join(" "),
            fill: "none",
            stroke: color,
            "stroke-width": 1,
            "stroke-dasharray": "4, 4",
            "marker-end": `url(#${endArrowId})`,
        });

        $(`#${lineId}`).append(polyline);
        $(`#${lineId} path`).attr('fill', color);
        $('.componentContainer').removeClass('selected')
    })
}

const convertToImage = async () => {

   const canvas = await html2canvas($("#cyberContainer")[0]);
   let convertToBase64 = canvas.toDataURL();
    
    // Create an <img> element
    const imgElement = document.createElement('img');
    imgElement.src = convertToBase64;
    imgElement.style.width = "100%";
    imgElement.style.height = "auto"; 

    const parentContainer = document.getElementById('parentDiagramContainer');
    parentContainer.innerHTML = '';
    parentContainer.appendChild(imgElement.cloneNode(true));

    const detailContainer = document.getElementById('detailDiagramContainer');
    detailContainer.innerHTML = '';
    detailContainer.appendChild(imgElement);
};













