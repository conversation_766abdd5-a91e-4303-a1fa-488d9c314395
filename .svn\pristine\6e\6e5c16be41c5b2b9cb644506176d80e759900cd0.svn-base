using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class RpoSlaDeviationReportRepositoryTests : IClassFixture<RpoSlaDeviationReportFixture>
{
    private readonly RpoSlaDeviationReportFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly RpoSlaDeviationReportRepository _repository;

    public RpoSlaDeviationReportRepositoryTests(RpoSlaDeviationReportFixture fixture)
    {
        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new RpoSlaDeviationReportRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region GetRpoSlaDeviationReportListByBusinessServiceId Tests

    [Fact]
    public async Task GetRpoSlaDeviationReportListByBusinessServiceId_ShouldReturnMatchingRecords_WhenRecordsExist()
    {
        // Arrange
        var businessServiceId = "BS_TEST_001";
        var matchingReports = new List<RpoSlaDeviationReport>
        {
            _fixture.CreateRpoSlaDeviationReportWithBusinessServiceId(businessServiceId),
            _fixture.CreateRpoSlaDeviationReportWithBusinessServiceId(businessServiceId)
        };
        var nonMatchingReport = _fixture.CreateRpoSlaDeviationReportWithBusinessServiceId("DIFFERENT_BS");

        _dbContext.RpoSlaDeviationReports.AddRange(matchingReports);
        _dbContext.RpoSlaDeviationReports.Add(nonMatchingReport);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRpoSlaDeviationReportListByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, report => Assert.Equal(businessServiceId, report.BusinessServiceId));
        Assert.All(result, report => Assert.True(report.IsActive));
    }

    [Fact]
    public async Task GetRpoSlaDeviationReportListByBusinessServiceId_ShouldReturnEmptyList_WhenNoMatchingRecords()
    {
        // Arrange
        var report = _fixture.CreateRpoSlaDeviationReportWithBusinessServiceId("DIFFERENT_BS");
        _dbContext.RpoSlaDeviationReports.Add(report);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRpoSlaDeviationReportListByBusinessServiceId("NON_EXISTENT_BS");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetRpoSlaDeviationReportListByBusinessServiceId_ShouldReturnOnlyActiveRecords()
    {
        // Arrange
        var businessServiceId = "BS_ACTIVE_TEST";
        var activeReport = _fixture.CreateRpoSlaDeviationReportWithProperties(businessServiceId: businessServiceId, isActive: true);
        var inactiveReport = _fixture.CreateRpoSlaDeviationReportWithProperties(businessServiceId: businessServiceId, isActive: false);

        _dbContext.RpoSlaDeviationReports.AddRange(activeReport, inactiveReport);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetRpoSlaDeviationReportListByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsActive);
        Assert.Equal(businessServiceId, result.First().BusinessServiceId);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task GetRpoSlaDeviationReportListByBusinessServiceId_ShouldReturnEmptyList_WhenBusinessServiceIdIsNullOrEmpty(string businessServiceId)
    {
        // Arrange
        var report = _fixture.CreateRpoSlaDeviationReportWithBusinessServiceId("VALID_BS");
        _dbContext.RpoSlaDeviationReports.Add(report);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRpoSlaDeviationReportListByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetRpoSlaDeviationReportListByInfraObjectId Tests

    [Fact]
    public async Task GetRpoSlaDeviationReportListByInfraObjectId_ShouldReturnMatchingRecords_WhenRecordsExist()
    {
        // Arrange
        var infraObjectId = "INFRA_TEST_001";
        var matchingReports = new List<RpoSlaDeviationReport>
        {
            _fixture.CreateRpoSlaDeviationReportWithInfraObjectId(infraObjectId),
            _fixture.CreateRpoSlaDeviationReportWithInfraObjectId(infraObjectId)
        };
        var nonMatchingReport = _fixture.CreateRpoSlaDeviationReportWithInfraObjectId("DIFFERENT_INFRA");

        _dbContext.RpoSlaDeviationReports.AddRange(matchingReports);
        _dbContext.RpoSlaDeviationReports.Add(nonMatchingReport);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRpoSlaDeviationReportListByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, report => Assert.Equal(infraObjectId, report.InfraObjectId));
        Assert.All(result, report => Assert.True(report.IsActive));
    }

    [Fact]
    public async Task GetRpoSlaDeviationReportListByInfraObjectId_ShouldReturnEmptyList_WhenNoMatchingRecords()
    {
        // Arrange
        var report = _fixture.CreateRpoSlaDeviationReportWithInfraObjectId("DIFFERENT_INFRA");
        _dbContext.RpoSlaDeviationReports.Add(report);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRpoSlaDeviationReportListByInfraObjectId("NON_EXISTENT_INFRA");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetRpoSlaDeviationReportListByInfraObjectId_ShouldReturnOnlyActiveRecords()
    {
        // Arrange
        var infraObjectId = "INFRA_ACTIVE_TEST";
        var activeReport = _fixture.CreateRpoSlaDeviationReportWithProperties(infraObjectId: infraObjectId, isActive: true);
        var inactiveReport = _fixture.CreateRpoSlaDeviationReportWithProperties(infraObjectId: infraObjectId, isActive: false);

        _dbContext.RpoSlaDeviationReports.AddRange(activeReport, inactiveReport);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetRpoSlaDeviationReportListByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsActive);
        Assert.Equal(infraObjectId, result.First().InfraObjectId);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task GetRpoSlaDeviationReportListByInfraObjectId_ShouldReturnEmptyList_WhenInfraObjectIdIsNullOrEmpty(string infraObjectId)
    {
        // Arrange
        var report = _fixture.CreateRpoSlaDeviationReportWithInfraObjectId("VALID_INFRA");
        _dbContext.RpoSlaDeviationReports.Add(report);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRpoSlaDeviationReportListByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetRpoSlaDeviationReportListByStartTimeAndEndTimeAndBusinessServiceId Tests

    [Fact]
    public async Task GetRpoSlaDeviationReportListByStartTimeAndEndTimeAndBusinessServiceId_ShouldReturnMatchingRecords_WhenRecordsExistInDateRange()
    {
        // Arrange
        var businessServiceId = "BS_DATE_TEST";
        var startDate = DateTime.UtcNow.AddDays(-5);
        var endDate = DateTime.UtcNow.AddDays(1);

        var reportsInRange = _fixture.CreateMultipleRpoSlaDeviationReportsWithDateRange(businessServiceId, startDate, endDate, 3);
        var reportOutOfRange = _fixture.CreateRpoSlaDeviationReportWithDates(businessServiceId, createdDate: DateTime.UtcNow.AddDays(-10));

        _dbContext.RpoSlaDeviationReports.AddRange(reportsInRange);
        _dbContext.RpoSlaDeviationReports.Add(reportOutOfRange);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRpoSlaDeviationReportListByStartTimeAndEndTimeAndBusinessServiceId(
            businessServiceId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Equal(4, result.Count);
        Assert.All(result, report => Assert.Equal(businessServiceId, report.BusinessServiceId));
        Assert.All(result, report => Assert.True(report.IsActive));
        Assert.All(result, report => Assert.True(report.CreatedDate.Date >= startDate.Date && report.CreatedDate.Date <= endDate.Date));
    }

    [Fact]
    public async Task GetRpoSlaDeviationReportListByStartTimeAndEndTimeAndBusinessServiceId_ShouldReturnEmptyList_WhenNoRecordsInDateRange()
    {
        // Arrange
        var businessServiceId = "BS_NO_DATE_MATCH";
        var report = _fixture.CreateRpoSlaDeviationReportWithDates(businessServiceId, createdDate: DateTime.UtcNow.AddDays(-10));
        _dbContext.RpoSlaDeviationReports.Add(report);
        await _dbContext.SaveChangesAsync();

        var startDate = DateTime.UtcNow.AddDays(-5);
        var endDate = DateTime.UtcNow.AddDays(-1);

        // Act
        var result = await _repository.GetRpoSlaDeviationReportListByStartTimeAndEndTimeAndBusinessServiceId(
            businessServiceId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetRpoSlaDeviationReportListByStartTimeAndEndTimeAndInfraObjectId Tests

    [Fact]
    public async Task GetRpoSlaDeviationReportListByStartTimeAndEndTimeAndInfraObjectId_ShouldReturnMatchingRecords_WhenRecordsExistInDateRange()
    {
        // Arrange
        var infraObjectId = "INFRA_DATE_TEST";
        var startDate = DateTime.UtcNow.AddDays(-5);
        var endDate = DateTime.UtcNow.AddDays(1);

        var reportsInRange = new List<RpoSlaDeviationReport>();
        for (int i = 0; i < 3; i++)
        {
            var reportDate = startDate.AddDays(i);
            reportsInRange.Add(_fixture.CreateRpoSlaDeviationReportWithDates(infraObjectId: infraObjectId, createdDate: reportDate));
        }

        var reportOutOfRange = _fixture.CreateRpoSlaDeviationReportWithDates(infraObjectId: infraObjectId, createdDate: DateTime.UtcNow.AddDays(-10));

        _dbContext.RpoSlaDeviationReports.AddRange(reportsInRange);
        _dbContext.RpoSlaDeviationReports.Add(reportOutOfRange);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRpoSlaDeviationReportListByStartTimeAndEndTimeAndInfraObjectId(
            infraObjectId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Equal(4, result.Count);
        Assert.All(result, report => Assert.Equal(infraObjectId, report.InfraObjectId));
        Assert.All(result, report => Assert.True(report.IsActive));
        Assert.All(result, report => Assert.True(report.CreatedDate.Date >= startDate.Date && report.CreatedDate.Date <= endDate.Date));
    }

    [Fact]
    public async Task GetRpoSlaDeviationReportListByStartTimeAndEndTimeAndInfraObjectId_ShouldReturnEmptyList_WhenNoRecordsInDateRange()
    {
        // Arrange
        var infraObjectId = "INFRA_NO_DATE_MATCH";
        var report = _fixture.CreateRpoSlaDeviationReportWithDates(infraObjectId: infraObjectId, createdDate: DateTime.UtcNow.AddDays(-10));
        _dbContext.RpoSlaDeviationReports.Add(report);
        await _dbContext.SaveChangesAsync();

        var startDate = DateTime.UtcNow.AddDays(-5);
        var endDate = DateTime.UtcNow.AddDays(-1);

        // Act
        var result = await _repository.GetRpoSlaDeviationReportListByStartTimeAndEndTimeAndInfraObjectId(
            infraObjectId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetListByStartTimeAndEndTime Tests

    [Fact]
    public async Task GetListByStartTimeAndEndTime_ShouldReturnMatchingRecords_WhenRecordsExistInDateRange()
    {
        // Arrange
        var infraObjectId = "INFRA_TIME_RANGE_TEST";
        var startDate = DateTime.UtcNow.AddDays(-5);
        var endDate = DateTime.UtcNow.AddDays(1);

        var reportsInRange = new List<RpoSlaDeviationReport>();
        for (int i = 0; i < 3; i++)
        {
            var reportDate = startDate.AddDays(i);
            reportsInRange.Add(_fixture.CreateRpoSlaDeviationReportWithDates(
                infraObjectId: infraObjectId,
                createdDate: reportDate,
                lastModifiedDate: reportDate));
        }

        var reportOutOfRange = _fixture.CreateRpoSlaDeviationReportWithDates(
            infraObjectId: infraObjectId,
            createdDate: DateTime.UtcNow.AddDays(-10),
            lastModifiedDate: DateTime.UtcNow.AddDays(-10));

        _dbContext.RpoSlaDeviationReports.AddRange(reportsInRange);
        _dbContext.RpoSlaDeviationReports.Add(reportOutOfRange);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetListByStartTimeAndEndTime(
            startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"), infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(4, result.Count);
        Assert.All(result, report => Assert.Equal(infraObjectId, report.InfraObjectId));
        Assert.All(result, report => Assert.True(report.IsActive));
    }

    [Fact]
    public async Task GetListByStartTimeAndEndTime_ShouldReturnEmptyList_WhenNoRecordsInDateRange()
    {
        // Arrange
        var infraObjectId = "INFRA_NO_TIME_MATCH";
        var report = _fixture.CreateRpoSlaDeviationReportWithDates(
            infraObjectId: infraObjectId,
            createdDate: DateTime.UtcNow.AddDays(-10),
            lastModifiedDate: DateTime.UtcNow.AddDays(-10));
        _dbContext.RpoSlaDeviationReports.Add(report);
        await _dbContext.SaveChangesAsync();

        var startDate = DateTime.UtcNow.AddDays(-5);
        var endDate = DateTime.UtcNow.AddDays(-1);

        // Act
        var result = await _repository.GetListByStartTimeAndEndTime(
            startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"), infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddRpoSlaDeviationReport_WhenValidEntity()
    {
        // Arrange
        var report = _fixture.RpoSlaDeviationReportDto;
        report.BusinessServiceId = "BS_TEST_001";
        report.BusinessServiceName = "Test Business Service";
        report.BusinessFunctionId = "BF_TEST_001";
        report.BusinessFunctionName = "Test Business Function";
        report.InfraObjectId = "INFRA_TEST_001";
        report.InfraObjectName = "Test Infrastructure Object";
        report.DataLag = "2 hours";

        // Act
        var result = await _repository.AddAsync(report);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(report.BusinessServiceId, result.BusinessServiceId);
        Assert.Equal(report.BusinessServiceName, result.BusinessServiceName);
        Assert.Equal(report.BusinessFunctionId, result.BusinessFunctionId);
        Assert.Equal(report.BusinessFunctionName, result.BusinessFunctionName);
        Assert.Equal(report.InfraObjectId, result.InfraObjectId);
        Assert.Equal(report.InfraObjectName, result.InfraObjectName);
        Assert.Equal(report.DataLag, result.DataLag);
        Assert.Single(_dbContext.RpoSlaDeviationReports);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var report = _fixture.RpoSlaDeviationReportDto;
        _dbContext.RpoSlaDeviationReports.Add(report);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(report.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(report.Id, result.Id);
        Assert.Equal(report.BusinessServiceId, result.BusinessServiceId);
        Assert.Equal(report.InfraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity_WhenExists()
    {
        // Arrange
        var report = _fixture.RpoSlaDeviationReportDto;
        _dbContext.RpoSlaDeviationReports.Add(report);
        await _dbContext.SaveChangesAsync();

        var updatedDataLag = "Updated Data Lag";
        var updatedBusinessServiceName = "Updated Business Service Name";
        report.DataLag = updatedDataLag;
        report.BusinessServiceName = updatedBusinessServiceName;

        // Act
        var result = await _repository.UpdateAsync(report);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(updatedDataLag, result.DataLag);
        Assert.Equal(updatedBusinessServiceName, result.BusinessServiceName);
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity_WhenExists()
    {
        // Arrange
        var report = _fixture.RpoSlaDeviationReportDto;
        _dbContext.RpoSlaDeviationReports.Add(report);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(report);

        // Assert
        var deletedEntity = await _repository.GetByIdAsync(report.Id);
        Assert.Null(deletedEntity);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.RpoSlaDeviationReports.RemoveRange(_dbContext.RpoSlaDeviationReports);
        await _dbContext.SaveChangesAsync();
    }
}
