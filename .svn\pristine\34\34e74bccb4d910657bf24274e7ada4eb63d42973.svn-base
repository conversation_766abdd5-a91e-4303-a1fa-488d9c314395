﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowActionRepository : BaseRepository<WorkflowAction>, IWorkflowActionRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public WorkflowActionRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<WorkflowAction>> GetWorkflowActionNames()
    {
        return await _dbContext.WorkflowActions.AsNoTracking().Active()
            .Select(x => new WorkflowAction
                { ReferenceId = x.ReferenceId, ActionName = x.ActionName, NodeId = x.NodeId })
            .ToListAsync();
    }

    public Task<bool> IsWorkflowActionNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.ActionName.Equals(name))
            : Entities.Where(e => e.ActionName.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsWorkflowActionNameUnique(string name)
    {
        var matches = _dbContext.WorkflowActions.AsNoTracking().Any(e => e.ActionName.Equals(name));

        return Task.FromResult(matches);
    }

    public async Task<List<WorkflowAction>> GetWorkflowActionDetailsByNodeId(string nodeId)
    {
        return await FilterRequiredFields(_dbContext.WorkflowActions.AsNoTracking().Active()
            .Where(e => e.NodeId.Equals(nodeId)))
            .ToListAsync();
    }

    public async Task<List<WorkflowAction>> GetWorkflowActionsByIds(List<string> ids)
    {
        return await _dbContext.WorkflowActions.AsNoTracking().Active()
                .Where(e => ids.Contains(e.ReferenceId))
            .ToListAsync();
    }



    public override IQueryable<WorkflowAction> GetPaginatedQuery()
    {
        return Entities.Where(x => x.IsActive)
            .AsNoTracking()
            .OrderByDescending(x => x.Id);
    }

    public  async Task<WorkflowAction> GetWorkflowActionDetailsByName(string name)
    {
        return await base.FilterBy(x=>x.ActionName.ToLower().Equals(name.ToLower())).FirstOrDefaultAsync();
    }

    private IQueryable<WorkflowAction> FilterRequiredFields(IQueryable<WorkflowAction> query)
    {
        return query.Select(x => new WorkflowAction
        {
            Id=x.Id,
            ReferenceId = x.ReferenceId,
            CompanyId=x.CompanyId,
            ActionName = x.ActionName,
            Properties=x.Properties,
            Type = x.Type,
            Script=x.Script,
            NodeId = x.NodeId,
            Version=x.Version,
            IsLock = x.IsLock
        });
    }

    //public override Task<WorkflowAction> GetByReferenceIdAsync(string id)
    //{
    //    return _loggedInUserService.IsParent
    //        ? base.GetByReferenceIdAsync(id)
    //        : Task.FromResult(FindByFilterAsync(workflowAction => workflowAction.ReferenceId.Equals(id) && workflowAction.CompanyId.Equals(_loggedInUserService.CompanyId)).Result.SingleOrDefault());
    //}
}