using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DriftParameterRepositoryTests : IClassFixture<DriftParameterFixture>
{
    private readonly DriftParameterFixture _driftParameterFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DriftParameterRepository _repository;

    public DriftParameterRepositoryTests(DriftParameterFixture driftParameterFixture)
    {
        _driftParameterFixture = driftParameterFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DriftParameterRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var driftParameter = _driftParameterFixture.DriftParameterDto;

        // Act
        var result = await _repository.AddAsync(driftParameter);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftParameter.Name, result.Name);
        Assert.Equal(driftParameter.DriftCategoryId, result.DriftCategoryId);
        Assert.Single(_dbContext.DriftParameters);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var driftParameter = _driftParameterFixture.DriftParameterDto;
        await _repository.AddAsync(driftParameter);

        driftParameter.Name = "Updated Parameter Name";

        // Act
        var result = await _repository.UpdateAsync(driftParameter);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Parameter Name", result.Name);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var driftParameter = _driftParameterFixture.DriftParameterDto;
        await _repository.AddAsync(driftParameter);

        // Act
        var result = await _repository.DeleteAsync(driftParameter);

        // Assert
        Assert.Equal(driftParameter.Name, result.Name);
        Assert.Empty(_dbContext.DriftParameters);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var driftParameter = _driftParameterFixture.DriftParameterDto;
        var addedEntity = await _repository.AddAsync(driftParameter);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var driftParameter = _driftParameterFixture.DriftParameterDto;
        await _repository.AddAsync(driftParameter);

        // Act
        var result = await _repository.GetByReferenceIdAsync(driftParameter.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftParameter.ReferenceId, result.ReferenceId);
        Assert.Equal(driftParameter.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var driftParameters = _driftParameterFixture.DriftParameterList;
        await _repository.AddRangeAsync(driftParameters);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftParameters.Count, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldNotReturnInactiveEntities()
    {
        // Arrange
        var driftParameters = _driftParameterFixture.DriftParameterList;
        driftParameters.First().IsActive = false; // Make one inactive
         _dbContext.DriftParameters.AddRange(driftParameters);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftParameters.Count - 1, result.Count); // Should exclude the inactive one
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var driftParameter = _driftParameterFixture.DriftParameterDto;
        driftParameter.Name = "ExistingParameterName";
        await _repository.AddAsync(driftParameter);

        // Act
        var result = await _repository.IsNameExist("ExistingParameterName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var driftParameters = _driftParameterFixture.DriftParameterList;
        await _repository.AddRangeAsync(driftParameters);

        // Act
        var result = await _repository.IsNameExist("NonExistentParameterName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var driftParameter = _driftParameterFixture.DriftParameterDto;
        driftParameter.Name = "SameParameterName";
        await _repository.AddAsync(driftParameter);

        // Act
        var result = await _repository.IsNameExist("SameParameterName", driftParameter.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntity()
    {
        // Arrange
        var driftParm=_driftParameterFixture.DriftParameterList;
        var driftParameter1 = driftParm[0];
        driftParameter1.Name = "ExistingParameterName";
        await _dbContext.DriftParameters.AddAsync(driftParameter1);
        await _dbContext.SaveChangesAsync();
        var driftParameter2 = driftParm[1];
        driftParameter2.Name = "DifferentParameterName";
        await _dbContext.DriftParameters.AddAsync(driftParameter2);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.IsNameExist("ExistingParameterName", driftParameter2.ReferenceId);

        // Assert
        Assert.True(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var driftParameters = _driftParameterFixture.DriftParameterList;

        // Act
        var result = await _repository.AddRangeAsync(driftParameters);

        // Assert
        Assert.Equal(driftParameters.Count, result.Count());
        Assert.Equal(driftParameters.Count, _dbContext.DriftParameters.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var driftParameters = _driftParameterFixture.DriftParameterList;
        await _repository.AddRangeAsync(driftParameters);

        // Act
        var result = await _repository.RemoveRangeAsync(driftParameters);

        // Assert
        Assert.Equal(driftParameters.Count, result.Count());
        Assert.Empty(_dbContext.DriftParameters);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public void GetPaginatedQuery_ShouldReturnQueryableOrderedByIdDescending()
    {
        // Arrange
        var driftParameters = _driftParameterFixture.DriftParameterList;
        _repository.AddRangeAsync(driftParameters).Wait();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Any());

        // Verify ordering by checking if the first item has a higher ID than the last
        var resultList = result.ToList();
        if (resultList.Count > 1)
        {
            Assert.True(resultList.First().Id >= resultList.Last().Id);
        }
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnEmptyQueryable_WhenNoEntities()
    {
        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.False(result.Any());
    }

    #endregion

    #region GetDriftParameterByCategoryId Tests

    [Fact]
    public async Task GetDriftParameterByCategoryId_ShouldReturnEntitiesWithMatchingCategoryId()
    {
        // Arrange
        var driftParameters = _driftParameterFixture.DriftParameterList;
        await _repository.AddRangeAsync(driftParameters);

        // Act
        var result = await _repository.GetDriftParameterByCategoryId(DriftParameterFixture.DriftCategoryId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DriftParameterFixture.DriftCategoryId, x.DriftCategoryId));
    }

    [Fact]
    public async Task GetDriftParameterByCategoryId_ShouldReturnEmptyList_WhenNoCategoryMatches()
    {
        // Arrange
        var driftParameters = _driftParameterFixture.DriftParameterList;
        await _repository.AddRangeAsync(driftParameters);

        // Act
        var result = await _repository.GetDriftParameterByCategoryId("non-existent-category-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDriftParameterByImpactTypeId Tests

    [Fact]
    public async Task GetDriftParameterByImpactTypeId_ShouldReturnEntitiesWithMatchingImpactTypeId()
    {
        // Arrange
        var driftParameters = _driftParameterFixture.DriftParameterList;
        await _repository.AddRangeAsync(driftParameters);

        // Act
        var result = await _repository.GetDriftParameterByImpactTypeId(DriftParameterFixture.DriftImpactTypeId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DriftParameterFixture.DriftImpactTypeId, x.DriftImpactTypeId));
    }

    [Fact]
    public async Task GetDriftParameterByImpactTypeId_ShouldReturnEmptyList_WhenNoImpactTypeMatches()
    {
        // Arrange
        var driftParameters = _driftParameterFixture.DriftParameterList;
        await _repository.AddRangeAsync(driftParameters);

        // Act
        var result = await _repository.GetDriftParameterByImpactTypeId("non-existent-impact-type-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var driftParameters = _driftParameterFixture.DriftParameterList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(driftParameters);
        var initialCount = driftParameters.Count;

        var toUpdate = driftParameters.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedParameterName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = driftParameters.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Name == "UpdatedParameterName").ToList();
        Assert.Equal(2, updated.Count);
    }

   

    #endregion
}
