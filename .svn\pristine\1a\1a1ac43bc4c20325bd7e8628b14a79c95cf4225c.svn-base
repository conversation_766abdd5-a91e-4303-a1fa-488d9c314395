﻿using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MYSQLMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Manage;

public class MysqlMonitorStatusService : BaseClient, IMysqlMonitorStatusService
{
    public MysqlMonitorStatusService(IConfiguration config, IAppCache cache, ILogger<MysqlMonitorStatusService> logger)
        : base(config, cache, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateMYSQLMonitorStatusCommand createMysqlMonitorStatusCommand)
    {
        var request = new RestRequest("api/v6/mysqlmonitorstatus", Method.Post);

        request.AddJsonBody(createMysqlMonitorStatusCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateMYSQLMonitorStatusCommand updateMysqlMonitorStatusCommand)
    {
        var request = new RestRequest("api/v6/mysqlmonitorstatus", Method.Put);

        request.AddJsonBody(updateMysqlMonitorStatusCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<List<MYSQLMonitorStatusListVm>> GetAllMYSQLMonitorStatus()
    {
        var request = new RestRequest("api/v6/mysqlmonitorstatus");

        return await GetFromCache<List<MYSQLMonitorStatusListVm>>(request, "GetAllMYSQLMonitorStatus");
    }

    public async Task<MYSQLMonitorStatusDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/mysqlmonitorstatus/{id}");

        return await Get<MYSQLMonitorStatusDetailVm>(request);
    }

    public async Task<List<MYSQLMonitorStatusDetailByTypeVm>> GetMYSQLMonitorStatusByType(string type)
    {
        var request = new RestRequest($"api/v6/mysqlmonitorstatus/type?type={type}");

        return await Get<List<MYSQLMonitorStatusDetailByTypeVm>>(request);
    }

    public async Task<PaginatedResult<MYSQLMonitorStatusListVm>> GetPaginatedMYSQLMonitorStatus(GetMYSQLMonitorStatusPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/mysqlmonitorstatus/paginated-list");

        return await Get<PaginatedResult<MYSQLMonitorStatusListVm>>(request);
    }

    //public async Task<string> GetMYSQLMonitorStatusByInfraObjectId(string infraObjectId)
    //{
    //    var request = new RestRequest($"api/v6/mysqlmonitorstatus/by/{infraObjectId}");

    //    return await Get<string>(request);
    //}
}