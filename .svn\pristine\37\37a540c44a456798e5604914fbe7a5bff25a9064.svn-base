using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class BusinessServiceRepositoryTests : IClassFixture<BusinessServiceFixture>
{
    private readonly BusinessServiceFixture _businessServiceFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly BusinessServiceRepository _repository;

    public BusinessServiceRepositoryTests(BusinessServiceFixture businessServiceFixture)
    {
        _businessServiceFixture = businessServiceFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new BusinessServiceRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;

        // Act
        var result = await _repository.AddAsync(businessService);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessService.Name, result.Name);
        Assert.Equal(businessService.Description, result.Description);
        Assert.Single(_dbContext.BusinessServices);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        await _repository.AddAsync(businessService);

        businessService.Name = "UpdatedName";
        businessService.Description = "UpdatedDescription";
        businessService.Priority = 1;

        // Act
        var result = await _repository.UpdateAsync(businessService);

        // Assert
        Assert.Equal("UpdatedName", result.Name);
        Assert.Equal("UpdatedDescription", result.Description);
        Assert.Equal(1, result.Priority);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.NullReferenceException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        await _repository.AddAsync(businessService);

        // Act
        var result = await _repository.DeleteAsync(businessService);

        // Assert
        Assert.Equal(businessService.Name, result.Name);
        Assert.Empty(_dbContext.BusinessServices);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        var addedEntity = await _repository.AddAsync(businessService);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        await _repository.AddAsync(businessService);

        // Act
        var result = await _repository.GetByReferenceIdAsync(businessService.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessService.ReferenceId, result.ReferenceId);
        Assert.Equal(businessService.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        await _repository.AddRangeAsync(businessServices);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServices.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsBusinessServiceNameExist Tests

    [Fact]
    public async Task IsBusinessServiceNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        businessService.Name = "ExistingName";
        await _repository.AddAsync(businessService);

        // Act
        var result = await _repository.IsBusinessServiceNameExist("ExistingName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsBusinessServiceNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        await _repository.AddRangeAsync(businessServices);

        // Act
        var result = await _repository.IsBusinessServiceNameExist("NonExistentName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsBusinessServiceNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        businessService.Name = "SameName";
        await _repository.AddAsync(businessService);

        // Act
        var result = await _repository.IsBusinessServiceNameExist("SameName", businessService.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsBusinessServiceNameUnique Tests

    [Fact]
    public async Task IsBusinessServiceNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        businessService.Name = "UniqueName";
        await _repository.AddAsync(businessService);

        // Act
        var result = await _repository.IsBusinessServiceNameUnique("UniqueName");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsBusinessServiceNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        await _repository.AddRangeAsync(businessServices);

        // Act
        var result = await _repository.IsBusinessServiceNameUnique("NonExistentName");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceList;
        var businessService1 = businessService[0];
        var businessService2 = businessService[1];
     

        // Act
        var task1 = _repository.AddAsync(businessService1);
        var task2 = _repository.AddAsync(businessService2);

        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.BusinessServices.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(businessServices);
        var initialCount = businessServices.Count;

        var toUpdate = businessServices.Take(2).ToList();
        toUpdate.ForEach(x => x.Priority = 1);
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = businessServices.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Priority == 1).ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleNullParametersGracefully()
    {
        // Act & Assert
        var result1 = await _repository.IsBusinessServiceNameExist(null, "valid-guid");
        var result2 = await _repository.IsBusinessServiceNameExist("TestName", null);
        var result3 = await _repository.IsBusinessServiceNameUnique(null);

        Assert.False(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    [Fact]
    public async Task Repository_ShouldHandlePriorityFiltering()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;

        businessServices[0].Priority = 1;
        businessServices[1].Priority = 2;
        businessServices[2].Priority = 1;
       
        _dbContext.BusinessServices.AddRange(businessServices);

        _dbContext.SaveChanges();

        // Act
        var criticalServices = await _repository.FindByFilterAsync(x => x.Priority == 1);
        var highServices = await _repository.FindByFilterAsync(x => x.Priority == 2);

        // Assert
        Assert.Equal(2, criticalServices.Count);
        Assert.Single(highServices);
        Assert.All(criticalServices, x => Assert.Equal(1, x.Priority));
        Assert.All(highServices, x => Assert.Equal(2, x.Priority));
    }

    #endregion
}
