﻿namespace ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetNameUnique;

public class GetGlobalSettingNameUniqueQueryHandler : IRequestHandler<GetGlobalSettingNameUniqueQuery, bool>
{
    private readonly IGlobalSettingRepository _globalSettingRepository;

    public GetGlobalSettingNameUniqueQueryHandler(IGlobalSettingRepository globalSettingRepository)
    {
        _globalSettingRepository = globalSettingRepository;
    }

    public async Task<bool> Handle(GetGlobalSettingNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _globalSettingRepository.IsGlobalSettingKeyExist(request.GlobalSettingKey, request.Id);
    }
}