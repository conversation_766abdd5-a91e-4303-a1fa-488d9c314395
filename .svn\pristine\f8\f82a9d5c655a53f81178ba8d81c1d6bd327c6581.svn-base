using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class PostgresMonitorStatusFixture : IDisposable
{
    public List<PostgresMonitorStatus> PostgresMonitorStatusPaginationList { get; set; }
    public List<PostgresMonitorStatus> PostgresMonitorStatusList { get; set; }
    public PostgresMonitorStatus PostgresMonitorStatusDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public PostgresMonitorStatusFixture() 
    {
        var fixture = new Fixture();

        PostgresMonitorStatusList = fixture.Create<List<PostgresMonitorStatus>>();

        PostgresMonitorStatusPaginationList = fixture.CreateMany<PostgresMonitorStatus>(20).ToList();

        PostgresMonitorStatusDto = fixture.Create<PostgresMonitorStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
