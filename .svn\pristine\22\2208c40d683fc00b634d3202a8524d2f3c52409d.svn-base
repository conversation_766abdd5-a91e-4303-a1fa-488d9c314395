let osType = '', userName = '', confirmPassword = '', OsType = '', selectedData = [], serverdata = [], isChecked = false;

const BulkCredentialURL = {
    ServerByUserUrl: "Configuration/BulkServerCredential/GetServerByUserName",
    ServerUrl: "Configuration/BulkServerCredential/GetServerByUserName",
    PasswordUrl: "Configuration/BulkServerCredential/UpdateBulkPassword"
}

$("#ckBox, #SubAuthenticat").hide();

let createPermission = $("#configurationCreate").data("create-permission").toLowerCase();

if (createPermission == 'false') {
    $(".btn-save").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
}
if (createPermission == 'false') {
    $("#cancel").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
}

// Clear
function clearInputs() {
    const errorElements = ['#userNameEror,#passwordError,#serverError,#operatingError,#ConfirmPassword-error'];
    clearInputFields('bulkCredentials', errorElements);
    osType = '', userName = '', OsType = '', selectedData = [], supAthCat = [], confirmPassword = '', isChecked = false;
    $("#SubAuthenticat,#ckBox").hide() 
    $('#operatingSystem, #serverName ,#SubAuthentication').empty();
    $('#serverError').text('').removeClass('field-validation-error');
    $('#operatingError').text('').removeClass('field-validation-error');
}

// Cancel
$('#cancel').on('click', function () {
    clearInputs();
})

//---DServer --->
async function SetUserName() {

    let url = RootUrl + BulkCredentialURL.ServerByUserUrl;
    let data = {};
    data.userName = userName
    data.osType = osType
    let result = await GetAsync(url, data, OnError);
    let uniqueOsTypes = {};
    $('#operatingSystem').empty()
    if (Array.isArray(result) && result?.length > 0) {
        $('#operatingSystem').append('<option value="all" data-id="uniqueValue" data-name="selectAll" >Select all</option>');
        for (let index = 0; index < result.length; index++) {
            let osTypeValue = result[index].osType;

            if (!uniqueOsTypes[osTypeValue]) {
                uniqueOsTypes[osTypeValue] = true;
                $('#operatingSystem').append('<option  value="' + result[index].osTypeId + '">' + result[index].osType + '</option>');
            }

        }
    }
}

//--- Database Name --->
async function SetServerName() {
    let url = RootUrl + BulkCredentialURL.ServerUrl;
    let data = {};
    data.userName = userName;
    data.osType = osType;
    data.substituteAuthentication = isChecked
    let result = await GetAsync(url, data, OnError);
    let uniqueIpaddress = {};

    if (result != "") {
        $('#serverName').empty();
        $('#serverName').append('<option value="all" data-id="uniqueVal" data-name="selectAll">Select all</option>');
        serverdata = [];

        for (let index = 0; index < result.length; index++) {
            let properties = JSON.parse(result[index].properties);
            let isAzure = Object.keys(properties).includes('Host');

            let ConnectVia = properties?.ConnectViaHostName;
            let hostName = properties?.HostName;
            let IpAddress = properties?.IpAddress;
            let id = result[index].id;
            let names = result[index].name;
            let osType = result[index].osType;

            serverdata.push({ id: id, name: names, ipAddress: IpAddress, hostName: hostName, osType: osType });

            if (isAzure) {
                ConnectVia = true;
                hostName = properties?.Host;
            }
            let name = result[index].name;
            let propertiesData = properties ? JSON.stringify(properties) : null;

            if (!uniqueIpaddress[IpAddress]) {
                uniqueIpaddress[IpAddress] = true;

                $('#serverName').append('<option value="' + (ConnectVia ? hostName : IpAddress) + '" data-id="' + id + '" data-name="' + name + '" data-properties="' + encodeURIComponent(propertiesData) + '">' + (ConnectVia ? hostName : IpAddress) + '</option>');
            }
        }
    }
}

// Server Name Selection
$("#serverName").on('change', function () {
    const value = $(this).val();   
    if (value.length == 0) {
        $('#SubAuthentication').empty()
    }
    if ($(this).val().includes('all')) {

        $('#serverName option').prop('selected', 'selected')
        $('#serverName').find('[value=all]').prop('selected', false).trigger('change')
    }
    const selectedOptions = $('#serverName').val();
    selectedData = [];
    serverdata && serverdata?.length && serverdata.map((server) => {
        if (selectedOptions.includes(server?.ipAddress) || selectedOptions.includes(server?.hostName)) {
            selectedData.push({ Id: server?.id, Name: server?.name, osType: server?.osType, SubAuthUserNames: [] })
        }
    })

    selectedOptions.forEach((selectedValue) => {
        const selectedOption = $('#serverName option[value="' + selectedValue + '"]');
        const propertiesData = selectedOption.data('properties');
        const properties = propertiesData ? JSON.parse(decodeURIComponent(propertiesData)) : null;   
        const dataId = selectedOption.data('id');   
        if (properties) {
            const { IpAddress, HostName, ConfigureSubstituteAuthentication } = properties;      
            if (value.includes(IpAddress) || value.includes(HostName) && ConfigureSubstituteAuthentication?.length > 0) {
              
                ConfigureSubstituteAuthentication?.forEach(auth => {                   
                    if ($('#SubAuthentication option[value="' + auth.SubstituteAuthenticationUser + '"]').length === 0) {
                        $('#SubAuthentication').append(`<option server-id="${dataId}" value="${auth.SubstituteAuthenticationUser}">${auth.SubstituteAuthenticationUser}</option>`);
                    }
                });
            }
        }
    });
    validateDropDown(value, 'Select server', 'serverError');
});

 

$("#SubAuthentication").on('change', function () {
    
    const value = $(this).val(); 
    const selectedOption = $(this).find("option:selected"); 
    let supAthCat = [];  
  
    selectedOption.each((_, option) => {
        let optionServerId = $(option).attr('server-id');
        let optionValue = $(option).val();

        let obj = { id: optionServerId, name: optionValue }

        supAthCat.push(obj)

    });

    selectedData?.length && selectedData.forEach((data) => {

        supAthCat?.length && supAthCat.forEach((findData) => {

            if (findData?.id == data?.Id && !data?.SubAuthUserNames?.includes(findData?.name)) {
                data.SubAuthUserNames.push(findData?.name)
            }

        })         
        
    })
    
    validateDropDown(value, 'Select operating system', 'operatingError');
});



$('#inlineCheckbox1').change(function () {

    isChecked = $(this).prop('checked');
    if (!isChecked) {
        $("#SubAuthenticat").hide()
    } else {
        $("#SubAuthenticat").show()
    }
    SetServerName();  
});

// operating System
$("#operatingSystem").on('change', function () {
    if ($(this).val().includes('all')) {

        $('#operatingSystem option').prop('selected', 'selected')
        $('#operatingSystem').find('[value=all]').prop('selected', false).trigger('change')
    }
    const value = $(this).val();

    const arrayOfSelectedOptions = $('#operatingSystem option:selected').map(function () {
        return $(this).text();
    }).get();
   
    if (arrayOfSelectedOptions.includes('Linux') && arrayOfSelectedOptions.length === 1) {
        $("#ckBox").show();
    } else {
        $("#SubAuthentication,#ckBox").hide();
        $('#inlineCheckbox1').prop('checked', false);
       
    }

    OsType = arrayOfSelectedOptions.join(',');
    osType = Array.isArray(value) ? value.join(',') : value;


    if (osType.length === 0) {

        $('#serverName').val('').trigger('change');
        $('#serverName').select2('destroy').select2();
        $('#operatingError').text('').removeClass('field-validation-error');

    } else {
        SetServerName();
    }
    validateDropDown(value, ' Select operating system', 'operatingError');
});


function validateUserDropDown(value, errormessage, errorElements) {

    if (!value) {
        $('#' + errorElements).text(errormessage).addClass('field-validation-error');
        return false;
    }
    if (value.startsWith('_')) {
        $('#' + errorElements).text('Should not begin with underscore').addClass('field-validation-error');
        return false;
    }
    if (value.endsWith('_')) {
        $('#' + errorElements).text('Should not end with underscore').addClass('field-validation-error');
        return false;
    }
    if (value.length < 2 || value.length > 100) {
        $('#' + errorElements).text("Between 2 to 100 characters").addClass('field-validation-error');
        return false;
    }
    if (value.includes("<")) {
        $('#userNameEror').text('Special characters not allowed').addClass('field-validation-error');
        return false;
    }
    $('#' + errorElements).text('').removeClass('field-validation-error');
    return true;
}

$('#Password').on('keydown input', function () {
    let value = $(this).val();
    $(this).val(value);
    validatePasswordDropDown(value, 'Enter password', 'passwordError');

    let cnfm = $('#ConfirmPassword').val('');
    if (cnfm) {
        $('#ConfirmPassword-error').text('').removeClass('field-validation-error');
        return true;
    }

});

$('#ConfirmPassword').on('keydown keyup input', async function () {
    let value = $(this).val();
    await validateConfirmPassword(value);
});

$('#ConfirmPassword').on('input', function () {
    let confirmPassword = $(this).val();
    validateConfirmPassword(confirmPassword);
});

$("#Password,#ConfirmPassword").on('blur', async function () {
    let password = $(this).val().trim();
    if (password != "" && password.length > 0 && password.length < 64) {
        try {
            let blurPass = await EncryptPassword(password, "#" + this.id);
            $(this).val(blurPass)

        } catch (error) {
            console.error("Error hashing password on blur: " + error);
        }
    }

    $(this).attr('type', 'password');
    $('.toggle-password i').removeClass('fs-6');
    $(this).siblings('.toggle-password').find('i').addClass('cp-password-visible fs-6');

});

$("#Password,#ConfirmPassword").on('focus', async function () {

    let encryptedPassword = $(this).val();
    if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
        let afterLoginName = await onfocusPassword(encryptedPassword);
        $(this).val(afterLoginName);
    }

});

$('#userName').on('blur', async function () {

    userName = $(this).val().trim();
    osType = ''
    if (userName.length > 1) {
        await SetUserName();
    } else {
        await SetUserName();
        await SetServerName();
        $('#serverName').empty()
    }
});

$('#userName').on('input', function () {

    let userNameData = $(this).val().replace(/ /g, '');
    $(this).val(userNameData);
    let server = $('#serverName').val()
    let operatSystem = $("#operatingSystem").val()
    if (userNameData === '' && server != 0 && operatSystem != 0) {      
        $("#operatingSystem,#serverName").empty()
        $("#operatingError").text('Select operating system').addClass('field-validation-error');
        $("#serverError").text('Select server').addClass('field-validation-error');

    }
    validateUserDropDown(userNameData, 'Enter username', 'userNameEror');
});


async function NewPassword(value) {
    const errorElement = $('#passwordError');

    if (!value) {
        errorElement.text('Enter password').addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('').removeClass('field-validation-error');
    }
    return true;
}
async function ConfirmPassword(value) {
    const errorElement = $('#ConfirmPassword-error');

    if (!value) {
        errorElement.text('Enter confirm password').addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
    }

    return true;
}

function validateDropDown(value, errormessage, errorElements) {

    if (value.length === 0 && (Array.isArray(value) || typeof value === 'string')) {
        $('#' + errorElements).text(errormessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElements).text('').removeClass('field-validation-error');
        return true;
    }

}

function validatePasswordDropDown(value, errormessage, errorElements) {

    value = value.trim();
    if (value.length === 0 && (Array.isArray(value) || typeof value === 'string')) {     
        $('#' + errorElements).text(errormessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElements).text('').removeClass('field-validation-error');
        return true;
    }

}

async function validateNewPassword(value) {

    const errorElement = $('#passwordError');
    const validationResult = await passwordRegex(value);


    if (!value) {
        errorElement.text('Enter password').addClass('field-validation-error');
        return false;
    }

    else if (validationResult !== true) {
        errorElement.text('Invalid password').addClass('field-validation-error');
        return false;
    }

    errorElement.text('').removeClass('field-validation-error');
    return true;
}


async function validateConfirmPassword(value) {
    const errorElement = $('#ConfirmPassword-error');

    if (!value) {
        errorElement.text('Enter confirm password').addClass('field-validation-error');
        $("#SaveFunction").prop('disabled', true);
        return false;
    }

    const encryptPassword = $('#Password').val();
    let newPassword = await onfocusPassword(encryptPassword);
    let newdecryptPassword = encryptPassword;


    if (newPassword !== value && newdecryptPassword !== value) {
        errorElement.text(' Password does not match').addClass('field-validation-error');
    } else {
        errorElement.text('').removeClass('field-validation-error');
    }

    if ($('#passwordError').text()) {

        $("#SaveFunction").prop('disabled', true);
    } else if (errorElement.text() === '') {
        $("#SaveFunction").prop('disabled', false);
        return true;
    } else {
        $("#SaveFunction").prop('disabled', true);
    }

    return false;
}

// save Function
$("#SaveFunction").on('click', async function () {
    let userName = $("#userName").val()
    let password = $("#Password").val();
    let server = $("#serverName").val();
    let operating = $("#operatingSystem").val();
    let isuserName = await validateUserDropDown(userName, ' Enter username', 'userNameEror');
    let isPassword = await validatePasswordDropDown(password, ' Enter password', 'passwordError');
    let isServer = await validateDropDown(server, ' Select server', 'serverError');
    let isoperating = await validateDropDown(operating, ' Select operating system', 'operatingError');
    confirmPassword = $("#ConfirmPassword").val();
    let loginName = $("#loginName").data("loginnames");
    let isConfirmPassword = await ConfirmPassword(confirmPassword);
  

    if (isuserName && loginName && isServer && isoperating && isPassword && isConfirmPassword) {

        if (password.length < 30) {
            let pass = await EncryptPassword(password, "#Password")
            $("#Password").attr('type', 'password')
            $("#Password").val(pass);
        }
        if (confirmPassword.length < 30) {
            let cnfmpass = await EncryptPassword(confirmPassword, "#ConfirmPassword")
            $("#ConfirmPassword").attr('type', 'password')
            $("#ConfirmPassword").val(cnfmpass);
        }

        $('#DuplicateActionsModal').modal('show');
    }
});

$("#duplicateConfirmation").on('click', function () {
    $("#duplicateConfirmation").prop("disabled", true);
    
    let data = {};
    data.IsSubstituteAuthentication = isChecked;
    data.UpdateBulkPasswordLists = selectedData;
    data.Password = confirmPassword;
    data.__RequestVerificationToken = gettoken()
    
    $.ajax({
        url: RootUrl + BulkCredentialURL.PasswordUrl,
        method: 'POST',
        dataType: 'json',
        data: data,
        success: function (result) {

            if (result.success) {
                clearInputs();
                notificationAlert("success", result.data.message)
                $('#DuplicateActionsModal').modal('hide');
                $("#duplicateConfirmation").prop("disabled", false);

            }
            else {
                notificationAlert("warning", "errors occurred while saving credential")
                $('#DuplicateActionsModal').modal('hide');
                $("#duplicateConfirmation").prop("disabled", false);

            }
        },
    });
});

// password --->
$(".toggle-password").on('click', async function () {
    let input = $(this).prev();
    var icon = $(this).find("i");

    if (input.attr("type") === "password") {

        showPassword(input, icon);
        let encryptedPassword = input.val();

        if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
            let afterLoginName = await onfocusPassword(encryptedPassword);
            input.val(afterLoginName);

        }

    } else {

        hidePassword(input, icon);
    }
});

$(".cp-password-hide").on('mouseover', function () {
    $(this).attr("title", "Hide Password");
});

$(".cp-password-visible").on('mouseover', function () {
    $(this).attr("title", "Show Password");
});


function showPassword(input, icon) {
    input.attr("type", "text");
    icon.removeClass("cp-password-visible").addClass("cp-password-hide");
    icon.attr("title", "Hide Password");
    $(".cp-password-hide").on('mouseover', function () {
        $(this).attr("title", "Hide Password");
    });

}

function hidePassword(input, icon) {
    input.attr("type", "password");
    icon.removeClass("cp-password-hide").addClass("cp-password-visible");
    var icon = $(".cp-password-visible");
    icon.attr("title", "Show Password");
    $(".cp-password-visible").on('mouseover', function () {
        $(this).attr("title", "Show Password");
    });

}

async function onfocusPassword(encryptedPassword) {
    let loginName = $("#loginName").data("loginnames");

    if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
        try {

            let decryptedPassword = await DecryptPassword(encryptedPassword);

            return decryptedPassword

        } catch (error) {
            console.error("Error decrypting password on focus: " + error);
        }
    }
    return null;
}

