﻿using ContinuityPatrol.Domain.ViewModels.MongoDbMonitorStatusModel;

namespace ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetList;

public class
    MongoDbMonitorStatusListQueryHandler : IRequestHandler<MongoDbMonitorStatusListQuery,
        List<MongoDbMonitorStatusListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMongoDbMonitorStatusRepository _mongoDbMonitorStatusRepository;

    public MongoDbMonitorStatusListQueryHandler(IMapper mapper,
        IMongoDbMonitorStatusRepository mongoDbMonitorStatusRepository)
    {
        _mapper = mapper;
        _mongoDbMonitorStatusRepository = mongoDbMonitorStatusRepository;
    }

    public async Task<List<MongoDbMonitorStatusListVm>> Handle(MongoDbMonitorStatusListQuery request,
        CancellationToken cancellationToken)
    {
        var mongoDbMonitorStatus = await _mongoDbMonitorStatusRepository.ListAllAsync();

        return mongoDbMonitorStatus.Count <= 0
            ? new List<MongoDbMonitorStatusListVm>()
            : _mapper.Map<List<MongoDbMonitorStatusListVm>>(mongoDbMonitorStatus);
    }
}