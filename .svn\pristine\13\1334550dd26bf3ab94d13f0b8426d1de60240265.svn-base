using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordJob.Queries;

public class GetAdPasswordJobDetailQueryTests : IClassFixture<AdPasswordJobFixture>
{
    private readonly AdPasswordJobFixture _adPasswordJobFixture;
    private readonly Mock<IAdPasswordJobRepository> _mockAdPasswordJobRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetAdPasswordJobDetailsQueryHandler _handler;

    public GetAdPasswordJobDetailQueryTests(AdPasswordJobFixture adPasswordJobFixture)
    {
        _adPasswordJobFixture = adPasswordJobFixture;

        _mockAdPasswordJobRepository = AdPasswordJobRepositoryMocks.CreateQueryAdPasswordJobRepository(_adPasswordJobFixture.AdPasswordJobs);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<AdPasswordJobDetailVm>(It.IsAny<Domain.Entities.AdPasswordJob>()))
            .Returns((Domain.Entities.AdPasswordJob entity) => new AdPasswordJobDetailVm
            {
                Id = entity.ReferenceId,
                DomainServerId = entity.DomainServerId,
                DomainServer = entity.DomainServer,
                State = entity.State,
                IsSchedule = entity.IsSchedule,
                ScheduleType = entity.ScheduleType,
                CronExpression = entity.CronExpression,
                ScheduleTime = entity.ScheduleTime,
                NodeId = entity.NodeId,
                NodeName = entity.NodeName,
                ExceptionMessage = entity.ExceptionMessage
            });

        _handler = new GetAdPasswordJobDetailsQueryHandler(
            _mockMapper.Object,
            _mockAdPasswordJobRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_AdPasswordJobDetailVm_When_AdPasswordJobExists()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        var query = new GetAdPasswordJobDetailQuery { Id = existingJob.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(AdPasswordJobDetailVm));
        result.Id.ShouldBe(existingJob.ReferenceId);
        result.DomainServer.ShouldBe(existingJob.DomainServer);
       // result.State.ShouldBe(existingJob.State);
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        var result = await _handler.Handle(new GetAdPasswordJobDetailQuery  { Id = _adPasswordJobFixture.AdPasswordJobs[0].ReferenceId }, CancellationToken.None );

        _mockAdPasswordJobRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var result = await _handler.Handle(new GetAdPasswordJobDetailQuery { Id = _adPasswordJobFixture.AdPasswordJobs[0].ReferenceId }, CancellationToken.None);

        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AdPasswordJobNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var query = new GetAdPasswordJobDetailQuery { Id = nonExistentId };

        _mockAdPasswordJobRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.AdPasswordJob)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AdPasswordJobIsInactive()
    {
        // Arrange
        var inactiveJob = _adPasswordJobFixture.AdPasswordJobs.First();
        inactiveJob.IsActive = false;
        var query = new GetAdPasswordJobDetailQuery { Id = inactiveJob.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_MapEntityToViewModel_WithCorrectProperties()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        existingJob.DomainServerId = "DS001";
        existingJob.DomainServer = "TestDomain.com";
        existingJob.State = "Active";
        existingJob.IsSchedule = 1;
        existingJob.ScheduleType = 1;
        existingJob.CronExpression = "0 0 12 * * ?";
        existingJob.ScheduleTime = "12:00:00";
        existingJob.NodeId = "Node001";
        existingJob.NodeName = "TestNode";
        existingJob.ExceptionMessage = "";

        var query = new GetAdPasswordJobDetailQuery { Id = existingJob.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(existingJob.ReferenceId);
        result.DomainServerId.ShouldBe("DS001");
        result.DomainServer.ShouldBe("TestDomain.com");
        result.State.ShouldBe("Active");
        result.IsSchedule.ShouldBe(1);
        
    }

    [Fact]
    public async Task Handle_CallRepositoryWithCorrectId_When_QueryExecuted()
    {
        // Arrange
        var testId = Guid.NewGuid().ToString();
        var query = new GetAdPasswordJobDetailQuery { Id = testId };

        _mockAdPasswordJobRepository.Setup(x => x.GetByReferenceIdAsync(testId))
            .ReturnsAsync(_adPasswordJobFixture.AdPasswordJobs.First());

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockAdPasswordJobRepository.Verify(x => x.GetByReferenceIdAsync(testId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectViewModelType_When_MappingSuccessful()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        var query = new GetAdPasswordJobDetailQuery { Id = existingJob.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
       
    }

    [Fact]
    public async Task Handle_MapScheduleProperties_WithCorrectValues()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        existingJob.IsSchedule = 1;
        existingJob.ScheduleType = 2;
        existingJob.CronExpression = "0 30 14 * * ?";
        existingJob.ScheduleTime = "14:30:00";

        var query = new GetAdPasswordJobDetailQuery { Id = existingJob.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.IsSchedule.ShouldBe(1);
        result.ScheduleType.ShouldBe(2);
       
    }

    [Fact]
    public async Task Handle_MapNodeProperties_WithCorrectValues()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        existingJob.NodeId = "Node123";
        existingJob.NodeName = "ProductionNode";
        existingJob.ExceptionMessage = "Test exception message";

        var query = new GetAdPasswordJobDetailQuery { Id = existingJob.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.NodeId.ShouldBe("Node123");
        result.NodeName.ShouldBe("ProductionNode");
        result.ExceptionMessage.ShouldBe("Test exception message");
    }
}
