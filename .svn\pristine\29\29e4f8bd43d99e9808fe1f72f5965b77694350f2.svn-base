﻿let ProfileStatus = { 'active': [], 'running': [] };
let workflowServiceStatus = false;
let executePermission = $("#orchestrationExecute").data("executePermission").toLowerCase();
let globalProfileId = '';
let infraObjectStatus = []
let currentActiveContainerId = '';

let noDataImages = {
    'ProfileContainer': "<img src='../../img/isomatric/Workflow_Execution_No_Data_Found.svg' class='Card_NoData_ImgExe' style='margin-left:337px;margin-top:110px;'>",
    'timeLine': "<img src='../../img/isomatric/Timeline_View_No_Data_Found.svg' class='Card_NoData_ImgExeExe center' style='width: 220px;margin-top:77px;margin-left:55px;'>",
    'logView': "<img src='../../img/isomatric/Log_Viewer_No_Data_Found.svg' class='Card_NoData_ImgExeExe' style='width:180px;margin-left:122px;margin-top:12px;'>"
}

let executionMethods = {
    'CheckWindowServiceurl': 'ITAutomation/WorkflowExecution/CheckWindowsService',
    'Users': 'ITAutomation/WorkflowExecution/LoadUsers',
    'LoadProfleList': 'ITAutomation/WorkflowExecution/LoadProfleList',
    'LoadRunningProfile': 'ITAutomation/WorkflowExecution/LoadRunningProfiles',
    'getProfileInfo': 'ITAutomation/WorkflowExecution/GetProfileInfo',
    'getWorkflowById': 'ITAutomation/WorkflowExecution/GetWorkflowById',
    'createExecutionTemp': 'ITAutomation/WorkflowExecution/CreateWorkflowExecutionTemp',
    'updateExecutionTemp': 'ITAutomation/WorkflowExecution/UpdateWorkflowExecutionTemp',
    'deleteExecutionTemp': 'ITAutomation/WorkflowExecution/DeleteWorkflowExecutionTemp',
    'updateConditions': 'ITAutomation/WorkflowExecution/UpdateConditions',
    'getActionResultByOperationGroupId': 'ITAutomation/WorkflowExecution/GetWorkflowActionResultByGroupId',
    'verifyProfile': 'ITAutomation/WorkflowExecution/VerifyProfile',
    'updateCompleteStatus': 'ITAutomation/WorkflowExecution/UpdateCompleteStatus',
    'updateInfraObjectStatus': 'ITAutomation/WorkflowExecution/UpdateInfraObjectState',
    'currentExecutionList': 'ITAutomation/WorkflowExecution/CurrentExecutionList',
    'getWorkflowsByProfileId': 'ITAutomation/WorkflowExecution/GetWorkflowProfileInfo',
    'getWorkflowListByProfileId': 'ITAutomation/WorkflowExecution/GetWorkflowProfileInfoList',
    'getSnapList': 'ITAutomation/WorkflowExecution/GetSnapList',
    'getActionDetails': 'ITAutomation/WorkflowExecution/GetActionDetails',
    'getProfileByUserId': 'ITAutomation/WorkflowExecution/LoadRunningProfilesByUserId',
    'getLogsByGroupId': 'ITAutomation/WorkflowExecution/GetWorkflowExecutionLogByGroupId',
    'updateLogStatus': 'ITAutomation/WorkflowExecution/UpdateWorkflowLogStatus',
    'getServerDateTime': 'ITAutomation/WorkflowExecution/GetServerDateTime',
}

const getRandomId = (value) => {
    return value + "_" + Math.floor(Math.random() * 11110 * Math.random() * 103180)
}


const loadUsersData = async () => {
    $('#UserList').empty()

    await $.ajax({
        type: "GET",
        url: RootUrl + executionMethods.Users,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result && result?.success) {
                let html = '';
                if (result?.data?.length) {
                    html += `<option value="all" selected>All</option>`
                    result?.data?.forEach((user) => {
                        html += `<option value="${user.userId}"> ${user.userName} </option>`
                    })
                    $('#UserList').append(html)
                }
            } else {
                errorNotification(result)
            }
        }
    })
}

const loadProfileList = async () => {
    await $.ajax({
        type: "GET",
        url: RootUrl + executionMethods.LoadProfleList,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result && result?.success) {
                let html = '';
                if (result?.data?.length) {
                    html += `<option value=""></option>`
                    result?.data?.forEach((profile) => {
                        if (!profile.isRunning) {
                            html += `<option value="${profile.id}"> ${profile.name} </option>`
                        }
                    })
                    $('#ProfileList').empty().append(html)
                }
            } else {
                errorNotification(result)
            }
        }
    })
}

const checkWorkflowService = async () => {
    await $.ajax({
        type: "POST",
        url: RootUrl + executionMethods.CheckWindowServiceurl,
        data: { type: 'workflow', __RequestVerificationToken: gettoken() },
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                let html = workflowServiceMessage(result)
                notificationAlert("success", html, 'execution')             
                if (result.activeNodes.length) {
                    workflowServiceStatus = true;
                    $('.btnStart:visible').prop('disabled', false)  
                    if (!$('#btnRunningProfileIcon').hasClass('cp-animate')) {
                        $('#btnRunningProfileIcon').addClass('cp-animate')
                        $('#messageWarningText').addClass('d-none')
                        
                    }
                } else {
                    workflowServiceStatus = false      
                    $('#btnRunningProfileIcon').removeClass('cp-animate')
                    $('#messageWarningText').removeClass('d-none') 
                    $(`.btnUpdateOperations.btnAbortOperation.parallelErrorModal.waitActionModal.snapActionModal`).css('pointer-events', 'none').prop('disabled', true)
                }             
            } else {
                workflowServiceStatus = false
                $('#btnRunningProfileIcon').removeClass('cp-animate')
                $('#messageWarningText').removeClass('d-none')
                $(`.btnUpdateOperations.btnAbortOperation.parallelErrorModal.waitActionModal.snapActionModal`).css('pointer-events', 'none').prop('disabled', true)

                errorNotification(result)
            }
        }
    })
    $("#logViewer").empty().append(noDataImages.logView)
}

const workflowServiceMessage = (result) => {
    let html = ''
    if (result?.activeNodes && Array.isArray(result?.activeNodes) && result?.activeNodes?.length) {
        for (let i = 0; i < result?.activeNodes?.length; i++) {
            html += `<div class='mb-1'><i class="cp-network fs-5 text-success" ></i> '${result.activeNodes[i]}'</div>`;
        }
    }
    if (result?.inActiveNodes && Array.isArray(result?.activeNodes) && result?.inActiveNodes?.length) {
        for (let i = 0; i < result?.inActiveNodes?.length; i++) {
            html += `<div class='mb-1'><i class="cp-network fs-5 text-danger" ></i> '${result.inActiveNodes[i]}'</div>`;
        }
    }
    return html;
}

$(async function () { 
    await loadProfileList();
    await loadUsersData();
    await loadExistingProfiles();
    await checkWorkflowService();
  
})


$("#ProfileList").on('change', WFExeEventDebounce(function () {
    let profileId = $("#ProfileList").val();
    if (profileId.length != 0) {
        // profileId.forEach((id) => {
        //   if (!ProfileStatus.active.includes(id)) {
        let profileData = { id: profileId }
        loadProfileContainer(profileData)
        ProfileStatus.active.push(profileId)
        //    }
   // })
    }
}, 500))

const loadExistingProfiles = async (data = {}, mode='') => {
    ProfileStatus = { 'active': [], 'running': [] }
    $('#parentWEProfileLoader').removeClass('d-none')
    let profileurl = executionMethods.LoadRunningProfile
    if (mode === 'user') {
        profileurl = executionMethods.getProfileByUserId
    }
    await $.ajax({
        type: "GET",
        url: RootUrl + profileurl,
        data : data,
        dataType: "json",
        traditional: true,
        success: function (response) {
            if (response && response.success && Array.isArray(response.data)) {
                response = response?.data?.filter(data => data.workflowOperationGroupListVm.length)
                if (response.length) {
                    let html = "";
                    let responseLength = response.length;
                    if (response[0]?.workflowOperationGroupListVm[0]?.workflowId) {
                        if (currentActiveContainerId && currentActiveContainerId.length) {
                            let filterdOperationGroupList = response[0]?.workflowOperationGroupListVm.filter((d) => d.id == currentActiveContainerId)
                            if (filterdOperationGroupList.length) {
                                let workflowName = filterdOperationGroupList[0]?.workflowName
                                let nodeName = filterdOperationGroupList[0]?.nodeName

                                let workflowData = {
                                    id: filterdOperationGroupList[0]?.workflowId,
                                    workflowGroupId: filterdOperationGroupList[0]?.id
                                }
                                TimeLineView(workflowData, workflowName, nodeName);

                                let data = {
                                    groupId: filterdOperationGroupList[0]?.id,
                                    isFilter: true
                                }
                                LogViewData(data, 'workflowLogViewer');

                                filterdOperationGroupList[0]?.isLog ? $('#logViewerToggle').removeClass().addClass('cp-circle-switch text-danger fs-5 me-2').attr('title', 'Disable') :
                                    $('#logViewerToggle').removeClass().addClass('cp-circle-switch text-success fs-5 me-2').attr('title', 'Enable')

                                $('#TimelineWorkflowContainer .timelineWorkflowName').text(workflowName || '-').attr('title', workflowName || '-')
                                $('#TimelineWorkflowContainer .timelineNodeName').attr('title', nodeName || 'NA')
                                $('#timelineWorkflowNameContainer').removeClass('d-none')
                            }
                        } else {
                            let workflowName = response[0]?.workflowOperationGroupListVm[0]?.workflowName
                            let nodeName = response[0]?.workflowOperationGroupListVm[0]?.nodeName

                            let workflowData = {
                                id: response[0]?.workflowOperationGroupListVm[0]?.workflowId,
                                workflowGroupId: response[0]?.workflowOperationGroupListVm[0]?.id
                            }
                            TimeLineView(workflowData, workflowName, nodeName);

                            let data = {
                                groupId: response[0]?.workflowOperationGroupListVm[0]?.id,
                                isFilter: true
                            }
                            LogViewData(data, 'workflowLogViewer');

                            response[0]?.workflowOperationGroupListVm[0].isLog ? $('#logViewerToggle').removeClass().addClass('cp-circle-switch text-danger fs-5 me-2').attr('title', 'Disable') :
                                $('#logViewerToggle').removeClass().addClass('cp-circle-switch text-success fs-5 me-2').attr('title', 'Enable')

                            $('#TimelineWorkflowContainer .timelineWorkflowName').text(workflowName || '-').attr('title', workflowName || '-')
                            $('#TimelineWorkflowContainer .timelineNodeName').attr('title', nodeName || 'NA')
                            $('#timelineWorkflowNameContainer').removeClass('d-none')
                        }
                       
                    }

                    for (var i = 0; i < responseLength; i++) {
                        ProfileStatus.running.push(response[i]?.profileId)
                        ProfileStatus.active.push(response[i]?.profileId)
                        if (response[i].workflowOperationGroupListVm.length != 0) {
                            html += checkRunningProfile(response[i])
                            $('#runningProfileContainer').removeClass('d-none')
                        }
                    }

                    //$('#ProfileList').val(ProfileStatus.running).trigger('change');
                    $('#LoadRunningProfile').empty().append(html);

                   // if (response[0]?.workflowOperationGroupListVm[0]?.id) {
                        if (currentActiveContainerId?.length) {
                            $(`.runningWorkflowContainer[operationGroupId=${currentActiveContainerId}]`).addClass("Active-Card")
                        } else {
                            $('.runningWorkflowContainer').first().addClass('Active-Card')
                        }

                    $('#timelineWFCountContainer .timelineConditionalCounts, #logViewerToggle').removeClass('d-none')
                 //   }

                    //response.forEach((data) => {
                    //    if (data?.workflowOperationGroupListVm?.length != 0) {
                    //        data?.workflowOperationGroupListVm.forEach((childData) => {
                    //            $("." + childData?.id).find(".ProfileName").html(`<i class="cp-action-name fs-7 me-1"></i>` + childData?.currentActionName).attr("title", childData?.currentActionName)
                    //        })
                    //    }
                    //})
                    if (!workflowServiceStatus) {
                        ///   setTimeout(() => {
                       // $('#LoadRunningProfile button').css('pointer-events', 'none')
                     //   $(`.btnNext, .btnRetry, .btnReload, .btnPauseResume, .aborted`).prop('disabled', true).css("color", "var(--bs-secondary)");
                        $('#btnRunningProfileIcon').removeClass('cp-animate')
                    //    $('#messageWarningText').removeClass('d-none')
                        //   },1000)                     
                    } else {
                        if (!$('#btnRunningProfileIcon').hasClass('cp-animate')) {
                            $('#btnRunningProfileIcon').addClass('cp-animate')
                       //     $('#messageWarningText').addClass('d-none')
                        }
                    }                   
                } else {
                    $('#LoadRunningProfile').empty().html(noDataImages.ProfileContainer)
                    $("#timeline_view").empty().html(noDataImages.timeLine)
                    $("#workflowLogViewer").empty().html(noDataImages.logView)
                    //$("#timeline_view, #timeline_WorkflowName, #UserList").empty()
                    //$("#wrapper, #timeline_view").show()
                    //$(".Workflow-Execution").addClass("d-flex align-items-center justify-content-center")
                    //$("#wrapper").css('text-align', 'center').html(image1);
                    //$("#timeline_view").html("<img src='../../img/isomatric/Timeline_View_No_Data_Found.svg' class='Card_NoData_ImgExeExe center mt-4' style='width:275px;margin-left:20px;'>");
                    //$('#timeline_WorkflowName').append('<div class="card-header fw-semibold">Timeline View</div>');
                    //$(".TimelineWorkflowName").empty()
                    //$('#ProfileList').val([]).trigger('change');
                    return;
                }
            }
            else {
                //var data = $("#LoadRunningProfile").children().length
                //if (data == 0) {
                //    $("#timeline_view, #timeline_WorkflowName, #UserList").empty()
                //    $("#wrapper, #timeline_view").show()
                //    $(".Workflow-Execution").addClass("d-flex align-items-center justify-content-center")
                //    $("#wrapper").css('text-align', 'center').html(image1);
                //    $("#timeline_view").html("<img src='../../img/isomatric/Timeline_View_No_Data_Found.svg' class='Card_NoData_ImgExeExe center mt-4' style='width:275px;margin-left:20px;'>");
                //    $('#timeline_WorkflowName').append('<div class="card-header fw-semibold">Timeline View</div>');
                //    // $('#timeline_WorkflowName').append('<div class="card-header TimelineWorkflowName">' + data.workflowName + '</div>');
                //    $(".TimelineWorkflowName").empty()
                //    $('#ProfileList').val([]).trigger('change');
                //    return;
                //}
                errorNotification(response)
            }
            if (executePermission == 'false') {
                $(".btnNext,.btnRetry,.btnReload,.aborted").attr('disabled', '').removeAttr('onclick');
            }
        }
    })
    $('#parentWEProfileLoader').addClass('d-none');
}

const checkRunningProfile = (response) => {

    let workflowRunningStatus = []
    let workflowArray = response.workflowOperationGroupListVm;
    let operationId = response?.id
    let workflowlength = workflowArray.length;
    let html = profileParentContainer(response, 'running')

    for (let j = 0; j < workflowlength; j++) {
        let WorkflowId = workflowArray[j].id.toString();
        let WorkflowName = workflowArray[j].workflowName;
        let barValue = JSON.stringify(workflowArray[j].progressStatus)
        let str = barValue.replace(/"/g, '');
        let runningcount = str.split('/');
        let runningValue = Number(runningcount[0]);
        let totalcount = barValue.split('/')[1];
        let totalValue = parseInt(totalcount, 10);
        let widthPercentage = (runningValue / totalValue) * 100;

        $(".SignalRStatus").removeClass("text-danger")
        $(".progressStatus").removeClass("bg-danger")
        $(".progressStatus").removeClass("bg-success")

        let statusStyle = updateStatusStyle(workflowArray[j]?.status)

        let barStyle = `bg-${statusStyle}`
        let textStyle = `text-${statusStyle}`
        let statusData = workflowArray[j]?.status?.toLowerCase() === "resume" ? "Running" : workflowArray[j].status;

        let modeStatus = workflowArray[j]?.actionMode == "Auto" ? 'cp-auto-mode auto-mode-bg circle' : workflowArray[j]?.actionMode == "Step" ? 'cp-step-mode Success_Running' : 'cp-simulation--mode simulation-mode-bg circle'
        let titleStatus = workflowArray[j]?.actionMode == "Auto" ? 'title="Auto mode"' : workflowArray[j]?.actionMode == "Step" ? 'title="Step mode"' : 'title="simulation mode"'

        let checkAbortStatus = workflowArray[j]?.status?.toLowerCase() === "completed" || workflowArray[j]?.status?.toLowerCase() === "aborted"
        let conditionStatus = (workflowArray[j].isPause === 1 || workflowArray[j].isPause === 2) ? "Rerun" : 'Pause'
        let conditionStatusEnable = (workflowArray[j].isPause === 1 || checkAbortStatus || workflowArray[j].isPause === 2) ? "disabled" : ''

        html += `<li class='list-group-item border-top list-group-item-action d-flex justify-content-between align-items-center runningWorkflowContainer' id="${workflowArray[j].workflowId}" operationGroupId="${workflowArray[j].id}" operationId="${workflowArray[j].workflowOperationId}" isCustom="${workflowArray[j].isCustom}" tempId="${workflowArray[j].workflowExecutionTempId}" nodeName="${workflowArray[j].nodeName}" isLog='${workflowArray[j].isLog}'><div class='d-flex'>
                    <div class='mt-2'><i class="${modeStatus} me-3 p-2" ${titleStatus}></i></div><div class='text-truncate' style='max-width:200px;width:200px'>
                    <span class='fw-bold infraObjectContainer flush-${response?.profileId}' id=${workflowArray[j].infraObjectId} title=${workflowArray[j].infraObjectName}></i>${workflowArray[j].infraObjectName}</span><br/>
                    <div class='text-light text-truncate mt-1 workflowTextContainer' title='${workflowArray[j].workflowName}' workflowName=${workflowArray[j].workflowName}><i class='cp-circle-workflow me-2'></i>${workflowArray[j].workflowName}</div>
                    </div></div><div class='d-flex w-25'><div class='text-light text-truncate flex-fill'>
                    <span class="d-inline-block text-truncate" style="max-width:75%"><i class="cp-action-name fs-8 me-1"></i><span class='runningActionContainer'>${workflowArray[j].currentActionName || ''}</span></span><br /><div class='d-flex align-items-center gap-2 mt-1'>
                    <div class='progress w-25 workflowProgressBar' role='progressbar' aria-valuenow='${runningValue}' aria-valuemin='0' aria-valuemax='${totalValue}' style='height:6px;'>
                    <div class='progressStatus progress-bar ${barStyle} progress-bar-striped progress-bar-animated' style='width:${widthPercentage}%;'></div></div>
                    <span class='workflowStatus ${textStyle}'>${statusData || 'Pending'}</span><span class='ms-2 progressBarStatusText' >${workflowArray[j].progressStatus || '0/0'}</span>
                    </div></div></div><div class='btn-group-sm'>`

        /*html += "<div class='text-secondary mt-1 RunningWorkflow' id=" + stringifyData + "><i class='cp-flow me-2' ></i>" +  workflowArray[j].workflowName + "</div>";*/

        let btnDisable = disableButtonBasedOnMode(workflowArray[j]?.actionMode, workflowArray[j]?.status)

        html += `<button type='button' class='btn btn-outline-secondary border-0 btnPauseResume btnUpdateOperations' buttonStatus='${conditionStatus}_btn' ${checkAbortStatus || btnDisable.includes('pause') ? 'disabled' : "style='color: var(--bs-primary);'"}><i class='cp-circle-play fs-6 me-2'></i><span class='align-middle'>${conditionStatus}</span></button>
                            <button type='button' class='btn btn-outline-secondary border-0 btnNext btnUpdateOperations' buttonStatus='next_btn'  ${btnDisable.includes('next') ? 'disabled' : "style='color: var(--bs-primary);'"} ><i class='cp-circle-playnext fs-6 me-2'></i><span class='align-middle'>Next</span></button>
                            <button type='button' class='btn btn-outline-secondary border-0 btnRetry btnUpdateOperations' buttonStatus='retry_btn'  ${btnDisable.includes('retry') ? 'disabled' : "style='color: var(--bs-primary);'"}><i class='cp-retry fs-6 me-2'></i><span class='align-middle'>Retry</span></button>
                            <button type='button' class='btn btn-outline-secondary border-0 btnReload btnUpdateOperations' buttonStatus='reload_btn'  ${btnDisable.includes('reload') ? 'disabled' : "style='color: var(--bs-primary);'"}><i class='cp-reload fs-6 me-2'></i><span class='align-middle'>Reload</span></button>
                            <button type='button' class='btn btn-outline-primary border-0 aborted btnAbortOperation' buttonStatus='aborted' ${(checkAbortStatus || btnDisable.includes('abort')) ? 'disabled' : "style='color: var(--bs-primary);'"} ><i class='cp-disable fs-6 me-2'></i><span  class='align-middle'>Abort</span></button>
                            <button type='button' class='btn btn-outline-danger border-0 pt-0 parallelErrorModal d-none' id='${getRandomId('error')}' title='Failed Actions' ${conditionStatusEnable ? 'disabled' : ''}><i class='cp-fail-back blinkingEffect '></i></button>
                            <button type='button' class='btn btn-outline-warning border-0 pt-0 waitActionModal ${workflowArray[j]?.status?.toLowerCase() === 'wait' ? '' : 'd-none'}' id='${workflowArray[j].workflowId}-wait' currentActionName='${workflowArray[j].currentActionName}' errorMessage='${btoa(workflowArray[j].message)} ' currentActionId='${workflowArray[j].currentActionId} ' title='Wait' ${conditionStatusEnable ? 'disabled' : ''}><i class='cp-time blinkingEffect'></i></button>
                            <button type='button' class='btn pt-0 snapActionModal ${workflowArray[j]?.status?.toLowerCase() === 'snap' ? '' : 'd-none'}' id='${workflowArray[j].workflowId}-snap' currentActionName='${workflowArray[j].currentActionName}' workflowId='${workflowArray[j].workflowId}' title='Snap' ${conditionStatusEnable ? 'disabled' : ''}><i class='cp-snap-1 blinkingEffect '></i></button>`
        
        html += "</div></li>";

        if (workflowArray[j].status?.toLowerCase() == 'waitmessage') {
            loadMessageDetails(workflowArray[j].workflowId, workflowArray[j].currentActionName, workflowArray[j].message)
        }

        if (workflowArray[j].status?.toLowerCase() != 'completed') {
            if (workflowArray[j].status?.toLowerCase() != 'aborted') workflowRunningStatus.push(workflowArray[j].status);
        }

        if (workflowArray.length == j + 1) {
            if (workflowRunningStatus.length == 0) {
                html += `<li class='list-group-item text-end px-0'><div><button type='button' class='btn btn-primary btnCompleted my-2 px-2 py-0 rounded-1' id='btnProfileCompleted'>Completed</button></div></li>`;
            }
        }
    }
    html += "</ul></div></div ></div ></div >";
    return html;
}

const updateStatusStyle = (status) => {
    return status?.toLowerCase() === "error" ? "danger" :
        status?.toLowerCase() === "aborted" ? "warning" :
            status?.toLowerCase() === "pending" ? "secondary" :
                status?.toLowerCase() === "running" ? "primary" :
                    (status?.toLowerCase() === "completed" || status?.toLowerCase() === "success") ? "success" : "primary";

}

const disableButtonBasedOnMode = (actionMode, status) => {
    let disabledButtons = [];
    let getStatus = status?.toLowerCase();
    if (actionMode?.toLowerCase() === 'auto') {
        switch (getStatus) {
            case 'pending':
                disabledButtons = ['pause', 'next', 'retry', 'reload'];
                break;
            case 'success':
                disabledButtons = ['pause', 'next', 'retry', 'reload'];
                break;
            case 'running':
                disabledButtons = ['next', 'retry', 'reload'];
                break;
            case 'error':
                disabledButtons = ['pause'];
                break;
            case 'aborted':
                disabledButtons = ['pause', 'next', 'retry', 'reload', 'abort'];
                break;
            case 'completed':
                disabledButtons = ['pause', 'next', 'retry', 'reload', 'abort'];
                break;
            case 'wait':
                disabledButtons = ['pause', 'next', 'retry', 'reload'];
                break;
            case 'snap':
                disabledButtons = ['pause', 'next', 'retry', 'reload'];
                break;
            case 'start':
                disabledButtons = ['pause', 'next', 'retry', 'reload'];
                break;
            default:
                disabledButtons = ['next', 'retry', 'reload'];
                break;
        }
    } else if (actionMode?.toLowerCase() === 'step') {

        switch (getStatus) {
            case 'pending':
                disabledButtons = ['pause', 'next', 'retry', 'reload'];
                break;
            case 'success':
                disabledButtons = ['pause'];
                break;
            case 'running':
                disabledButtons = ['next', 'retry', 'reload'];
                break;
            case 'error':
                disabledButtons = ['pause'];
                break;
            case 'aborted':
                disabledButtons = ['pause', 'next', 'retry', 'reload', 'abort'];
                break;
            case 'completed':
                disabledButtons = ['pause', 'next', 'retry', 'reload', 'abort'];
                break;
            case 'wait':
                disabledButtons = ['pause', 'next', 'retry', 'reload'];
                break;
            case 'snap':
                disabledButtons = ['pause', 'next', 'retry', 'reload'];
                break;
            case 'start':
                disabledButtons = ['pause', 'next', 'retry', 'reload'];
                break;
            default:
                disabledButtons = ['next', 'retry', 'reload'];
                break;
        }
    }

    return disabledButtons
}

$(document).on('click', '.runningWorkflowContainer', async function () {

    $('#individualCountCont')?.removeClass('d-none')
    $('.timelineConditionalCounts')?.removeClass('d-none')

    if (!$(this).hasClass('Active-Card')) {
      
        $('.runningWorkflowContainer, .workflowListContainer').removeClass('Active-Card')
        $(this).addClass('Active-Card')

        let workflowId = $(this).attr('id')
        let workflowName = $(this).find('.workflowTextContainer').text();
        let nodeName = $(this).attr('nodeName')
        let operationGroupId = $(this).attr('operationGroupId')
        let isLog = $(this).attr('isLog')

        let data = {
            id: workflowId,
            workflowGroupId: operationGroupId
        }
        await TimeLineView(data, workflowName, nodeName)

        let logData = {
            groupId: operationGroupId,
            isHub: true,
            isFilter: true
        }
        await LogViewData(logData, 'workflowLogViewer'); 

        isLog == 'true' ? $('#logViewerToggle').removeClass().addClass('cp-circle-switch text-danger fs-5 me-2').attr('title', 'Disable') :
            $('#logViewerToggle').removeClass().addClass('cp-circle-switch text-success fs-5 me-2').attr('title', 'Enable')

        $('#TimelineWorkflowContainer .timelineWorkflowName').text(workflowName || '-').attr('title', workflowName || '-')
        $('#TimelineWorkflowContainer .timelineNodeName').attr('title', nodeName || 'NA')
    
        $('#timelineWorkflowNameContainer').removeClass('d-none')
    }
})


const loadProfileContainer = async (data) => {

    $('.timelineNodeName')?.attr('title', '')
    $('#individualCountCont')?.addClass('d-none')
    $('.timelineConditionalCounts')?.addClass('d-none')

    await $.ajax({
        type: "GET",
        url: RootUrl + executionMethods.getProfileInfo,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (response) {
            if (response.success) {
                let result = response.data

                if (result && Array.isArray(result) && result.length) {
                    result.forEach((d) => {
                        if (d.workflowProfileInfos && d.workflowProfileInfos.length) {
                            let workflowData = {
                                id: d.workflowProfileInfos[0].workflowId,
                                workflowName: d.workflowProfileInfos[0].workflowName,
                            };
                            TimeLineView(workflowData);
                            appendProfileContainer(d)
                        } else {
                            notificationAlert('warning', `This profile requires a workflow`)
                            $('#ProfileList').val('').trigger('change')
                        }
                       
                    })
                }
            } else {
                errorNotification(response)
            }
        }
    })
}

const appendProfileContainer = (data) => {
    let html = '';
    html += profileParentContainer(data);
    let workflowProfileInfoLength = data.workflowProfileInfos.length;
    for (let i = 0; i < workflowProfileInfoLength; i++) {
        html += profileWFListContainer(data.workflowProfileInfos[i], data?.profileName, data?.profileId);
        //  wfCheckList.push(data?.workflowProfileInfos[i]?.workflowName);
    }
    html += `</ul></div><li class='list-group-item border-top text-end px-0'><div id='newBtnforSave'><button type='button' class='btn btn-primary d-none' btn-sm my-2 px-2 py-0 rounded-1 me-2'>Simulate</button><button type='button' class='btn btn-primary btnStart btn-sm my-2 px-2 py-0 rounded-1' ${!workflowServiceStatus ? 'disabled' : ''}>Start</button></div></li>`;
    html += "</div></div></div>";

    $('.workflowListContainer').parents('.profileContainer').remove()
    if ($('#LoadRunningProfile').find('.Card_NoData_ImgExe').length) {
        $('#LoadRunningProfile').empty().prepend(html);
    } else {
        $('#LoadRunningProfile').prepend(html);
    }

    $('.runningWorkflowContainer, .workflowListContainer').removeClass('Active-Card')
    $('.workflowListContainer').first().addClass('Active-Card')

    const workflowName = $('.workflowListContainer')?.first()?.attr('workflowname');
   
    $('#TimelineWorkflowContainer .timelineWorkflowName')?.text(workflowName || '-').attr('title', workflowName || '-')
    $(".Workflow-Execution").animate({ scrollTop: 0 }, "slow");
}

const profileParentContainer = (response, mode = '') => {
    let rendomId = getRandomId('random')
    let btnCont = '';

    if (mode === 'running') {
        btnCont = `<div class='btn-group btn-group-sm' style='min-width: 285px; display: block;' id='${response?.workflowOperationGroupListVm[0]?.workflowOperationId}'>
                    <button type='button' class='btn actionIcon'><i class='text-success cp-success fs-5 me-1' title='Success'></i><span class='align-middle successCount' id='${getRandomId('success')}'>${response.workflowActionStatusCount.successCount}</span></button>
                    <button type='button' class='btn actionIcon'><i class='text-primary cp-reload cp-animate fs-6 me-1' title='Running'></i><span class='align-middle runningCount' id='${getRandomId('running')}'>${response.workflowActionStatusCount.runningCount}</span></button>
                    <button type='button' class='btn actionIcon '><i class='text-info cp-skipped fs-5 me-1' title='Skip'></i><span class='align-middle skipCount' id='${getRandomId('skip')}'>${response?.workflowActionStatusCount?.skipCount || 0}</span></button>
                    <button type='button' class='btn actionIcon'><i class='text-danger cp-error fs-5 me-1' title='Error'></i><span class='align-middle errorCount' id='${getRandomId('error')}'>${response.workflowActionStatusCount.errorCount}</span></button>
                    <button type='button' class='btn actionIcon ${response?.workflowActionStatusCount?.bypassedCount !== 0 ? '' : 'd-none'}'><i class='text-secondary cp-by-pass fs-5 me-1' title='Bypass'></i><span class='align-middle byPassedCount' id='${getRandomId('bypass')}'>${response.workflowActionStatusCount.bypassedCount}</span></button>
                   </div>`
    }

    let html = `<div class='accordion accordion-flush mb-3 profileContainer' id="${response?.profileId}">
                    <div class='accordion-item'> <div class='accordion-header'><div class='accordion-button collapsed d-flex gap-3 justify-content-between accordion-after-icon-none'>
                    <div class='d-flex align-items-center' style='width:400px;'><div class=''> 
                    <input class='form-check-input profileParentCheckBox ${mode === 'running' ? 'd-none' : ''}' ${mode === 'running' ? '' : 'checked'} type='checkbox'  ${mode === 'running' ? 'disabled' : ''}></div>
                    <i class='cp-workflow-profile me-2 text-primary'></i>
                    <span class="profileNameClass text-primary">${response?.profileName}</span><span class='completedWorkflowCount ms-2 fs-8 text-secondary'></span></div>
                    ${btnCont}<div class="d-flex align-items-center gap-3"><span class="border border-secondary rounded-1 text-secondary fs-8 d-none" style="padding: 2px 4px; line-height:normal;">${response?.nodeName || 'NA'}<i class="cp-network align-middle ms-1 fs-7"></i></span>
                    <a role='button' class='profileCollapseIcon' data-bs-target='#flush-${response?.profileId}' aria-expanded='false' aria-controls='flush-collapseOne'><i class='cp-circle-uparrow text-secondary iconCollapseContainer'></i><i class='cp-subtract fs-6  text-primary d-none' title='collapse'></i><i class='cp-close ms-3 fs-7 text-secondary btnProfileClose ${mode === 'running' ? 'd-none' : ''}' ></i></a>
                    </div></div></div><div id='flush-${response?.profileId}' class='accordion-collapse collapse show executeProfileContainer' data-bs-parent='#accordionFlushExample'>
                    <div class='accordion-body'><ul class='list-group list-group-flush workflowList Profile-Select' id='${rendomId}'>`
    return html
}

const profileWFListContainer = (j) => {
    let html = `<li class='list-group-item border-top list-group-item-action d-flex align-items-center workflowListContainer' id="${j?.workflowId}" workflowName="${j?.workflowName}" state="${j?.state}" isCustom="${j?.isCustom}" isParallel="${j?.isParallel}" isLock="${j?.isLock}" tempId="${j?.customId}" ${j?.isLock ? 'disabled' : ''}>
                    <div class='me-3'><input class='form-check-input profileChildCheckBox' type='checkbox' ${j?.state?.toLowerCase() === 'lock' ? 'disabled' : 'checked '} ></div>
                    <div class='d-flex w-50'><div class='mt-1'><i class='cp-idea Success_Running me-2'></i></div><div class="text-truncate me-1">
                    <i title="${j?.state || 'NA'}" class="fs-7 me-2 ${j?.state?.toLowerCase() === 'locked' ? 'cp-lock text-warning' : j?.state?.toLowerCase() === 'active' ? 'cp-active-inactive text-success' : j?.state?.toLowerCase() === 'maintenance' ? 'cp-maintenance text-primary' : ''}"></i>
                    <span class='fw-bold infraObjectContainer' id=${j?.infraObjectId}>${j?.infraObjectName}<i class="cp-pin-point ms-1 fs-8 text-success ${j.isCustom ? '' : 'd-none'}"></i></span><br />
                    <div class='text-light text-truncate mt-1 workflowTextContainer'><i class='cp-flow me-2'></i><span>${j?.workflowName}</span></div></div></div>
                    <div class='d-flex w-25'><div class='text-light flex-fill'><span>Job Status</span><br/>
                    <div class='d-flex align-items-center gap-2 mt-1'><span><i class='cp-selects fs-6 me-2'></i>Not Started</span></div></div> </div>
                    <div class='d-flex flex-fill text-end'><div class='text-light flex-fill d-flex justify-content-end gap-2' role='button'>
                    <i class='cp-simulation--mode d-none' title='Simulate'></i>
                    <div class='form-check justify-content-end mt-2' title='Custom Execution'><i class='cp-custom-workflow-execution btnCustomWorkflow'></i><input class='form-check-input checkStatus d-none' type='checkbox' role='switch'></div >
                    <i class='cp-edit checkStatus d-none' status='true' style ='cursor: pointer;'></i > <span class='fs-8 text-dark'></span>
                    </div></li>`;
    return html;
}

const setProfileCount = (profileId) => {
    let getDenominator = $(`#${profileId} .workflowList.Profile-Select`).find('.runningWorkflowContainer').length
    let completedList = $(`#${profileId} .workflowList.Profile-Select`).find('.runningWorkflowContainer').find('[workflowStatus=Aborted], [workflowStatus=Completed]').length
    let statusColor = getDenominator === completedList ? 'text-success' : 'text-secondary'
    $(`#${profileId} .completedWorkflowCount`).removeClass().addClass(`completedWorkflowCount ms-2 fs-8 ${statusColor}`).text(`(${completedList}/${getDenominator})`)
    return true;
}

$(document).on('click', '.profileCollapseIcon', WFExeEventDebounce(function (e) {
    e.stopPropagation();
    let profileId = $(this).parents('.profileContainer')[0].id
    let collpaseId = $(this).attr('data-bs-target')
    if ($(`${collpaseId}`).hasClass('show')) {
        $(this).find('.iconCollapseContainer').removeClass('cp-circle-uparrow').addClass('cp-circle-downarrow').attr('title', 'expand')
        setProfileCount(profileId)
    } else {
        $(this).find('.iconCollapseContainer').removeClass('cp-circle-downarrow').addClass('cp-circle-uparrow').attr('title', 'collapse');
        $(`#${profileId} .completedWorkflowCount`).text('');
    }
    $(`${collpaseId}`).collapse('toggle')
}, 400))


$(document).on('click', '.btnProfileClose', function () {
    let getProfileId = $(this).parents('.profileContainer')[0].id
    $(`#${getProfileId}`).remove();
    ProfileStatus['active'] = ProfileStatus.active.filter((d) => d !== getProfileId)
    $('#ProfileList').val('').trigger('change');
})

$(document).on('change', '.profileParentCheckBox', function (e) {
    let getProfileId = $(this).parents('.profileContainer')[0].id
    $(`#${getProfileId} .profileChildCheckBox`).not(':disabled').prop('checked', e.target.checked)
})

$(document).on('change', '.profileChildCheckBox', function (e) {
    let getProfileId = $(this).parents('.profileContainer')[0].id

    if ($(`#${getProfileId} .profileChildCheckBox`).length === $(`#${getProfileId} .profileChildCheckBox:checked`).length) {
        $(`#${getProfileId} .profileParentCheckBox`).prop('checked', true)
    } else {
        $(`#${getProfileId} .profileParentCheckBox`).prop('checked', false)
    }
})

$(document).on('click', '.workflowListContainer', WFExeEventDebounce(async function (e) {
    e.stopPropagation();
    if (!$(this).hasClass("Active-Card")) {
        $('.workflowListContainer, .runningWorkflowContainer').removeClass('Active-Card')
        let getWorkflowId = $(this).attr('id')
        let getWorkflowName = $(this).attr('workflowName')

        let data = {
            id: getWorkflowId,
            workflowName: getWorkflowName
        }

        $('#TimelineWorkflowContainer .timelineWorkflowName')?.text(getWorkflowName || '-').attr('title', getWorkflowName || '-')
        await defaultTimeLineView(data, false)
        $(this).addClass("Active-Card")  
    }

    $('.timelineNodeName')?.attr('title', '')
    $('#individualCountCont')?.addClass('d-none')
    $('.timelineConditionalCounts')?.addClass('d-none')
    $('.btnCustomWorkflow').removeClass('active')
}, 800))

//$(document).on('click', '.page-content', function (e) {
//    if ($('#executionDropDownMenu').is(':visible') && e.target.id != 'btnRunningProfiles') {
//        $('#btnRunningProfiles').dropdown('toggle')
//    }   
//})






