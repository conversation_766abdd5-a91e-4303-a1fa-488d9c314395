﻿using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.LicenseInfo.Queries;

public class GetLicenseInfoByBusinessServiceIdListQueryHandlerTests : IClassFixture<LicenseInfoFixture>
{
    private readonly LicenseInfoFixture _licenseInfoFixture;

    private readonly Mock<ILicenseInfoRepository> _mockLicenseInfoRepository;

    private readonly GetLicenseInfoByBusinessServiceIdListQueryHandler _handler;

    public GetLicenseInfoByBusinessServiceIdListQueryHandlerTests(LicenseInfoFixture licenseInfoFixture)
    {
        _licenseInfoFixture = licenseInfoFixture;

        _mockLicenseInfoRepository = new Mock<ILicenseInfoRepository>();

        _mockLicenseInfoRepository = LicenseInfoRepositoryMocks.GetLicenseInfoByBusinessServiceIdRepository(_licenseInfoFixture.LicenseInfos);

        _handler = new GetLicenseInfoByBusinessServiceIdListQueryHandler(_mockLicenseInfoRepository.Object, _licenseInfoFixture.Mapper);

        //_licenseInfoFixture.LicenseInfos[0].BusinessServiceId = "6a934b92-3f2e-4bb4-bcb4-12b0e793fd00";
        //_licenseInfoFixture.LicenseInfos[1].BusinessServiceId = "6a934b92-3f2e-4bb4-bcb4-12b0e793fd00";
        //_licenseInfoFixture.LicenseInfos[2].BusinessServiceId = "6a934b92-3f2e-4bb4-bcb4-12b0e793fd00";
    }
    
    [Fact]
    public async Task Handle_Return_Valid_LicenseInfoByBusinessServicesList()
    {
        var licenseInfo = new List<Domain.Entities.LicenseInfo> {
              new Domain.Entities.LicenseInfo
              {
                  ReferenceId=_licenseInfoFixture.LicenseInfos[0].ReferenceId,
                  LicenseId=_licenseInfoFixture.LicenseInfos[0].LicenseId,
                  BusinessServiceId=_licenseInfoFixture.LicenseInfos[0].BusinessServiceId,
                  EntityId=_licenseInfoFixture.LicenseInfos[0].EntityId,
                  Type=_licenseInfoFixture.LicenseInfos[0].Type,
                  EntityName=_licenseInfoFixture.LicenseInfos[0].EntityName,
                  EntityField=_licenseInfoFixture.LicenseInfos[0].EntityField,
                  Entity=_licenseInfoFixture.LicenseInfos[0].Entity
              }
        };
        _mockLicenseInfoRepository.Setup(dp => dp.GetLicenseByBusinessServiceId(_licenseInfoFixture.LicenseInfos[0].BusinessServiceId)).ReturnsAsync(licenseInfo);

        var result = await _handler.Handle(new GetLicenseInfoDetailByBusinessServiceIdListQuery { BusinessServiceId = _licenseInfoFixture.LicenseInfos[0].BusinessServiceId }, CancellationToken.None);

        result.ShouldBeOfType<List<LicenseInfoByBusinessServiceIdListVm>>();

        
        result[0].LicenseId.ShouldBe(_licenseInfoFixture.LicenseInfos[0].LicenseId);
        result[0].BusinessServiceId.ShouldBe(_licenseInfoFixture.LicenseInfos[0].BusinessServiceId);
        result[0].EntityId.ShouldBe(_licenseInfoFixture.LicenseInfos[0].EntityId);
        result[0].Type.ShouldBe(_licenseInfoFixture.LicenseInfos[0].Type);
        result[0].EntityName.ShouldBe(_licenseInfoFixture.LicenseInfos[0].EntityName);
        result[0].Entity.ShouldBe(_licenseInfoFixture.LicenseInfos[0].Entity);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidLicenseManagerId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetLicenseInfoDetailByBusinessServiceIdListQuery { BusinessServiceId = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("Not Found");
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetLicenseInfoDetailByBusinessServiceIdListQuery { BusinessServiceId = _licenseInfoFixture.LicenseInfos[0].BusinessServiceId }, CancellationToken.None);

        _mockLicenseInfoRepository.Verify(x => x.GetLicenseByBusinessServiceId(It.IsAny<string>()), Times.Once);
    }
}