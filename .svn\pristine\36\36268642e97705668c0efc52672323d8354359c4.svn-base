namespace ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetDetail;

public class
    GetApprovalMatrixRequestDetailsQueryHandler : IRequestHandler<GetApprovalMatrixRequestDetailQuery,
        ApprovalMatrixRequestDetailVm>
{
    private readonly IApprovalMatrixRequestRepository _approvalMatrixRequestRepository;
    private readonly IMapper _mapper;

    public GetApprovalMatrixRequestDetailsQueryHandler(IMapper mapper,
        IApprovalMatrixRequestRepository approvalMatrixRequestRepository)
    {
        _mapper = mapper;
        _approvalMatrixRequestRepository = approvalMatrixRequestRepository;
    }

    public async Task<ApprovalMatrixRequestDetailVm> Handle(GetApprovalMatrixRequestDetailQuery request,
        CancellationToken cancellationToken)
    {
        var approvalMatrixRequest = await _approvalMatrixRequestRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(approvalMatrixRequest, nameof(Domain.Entities.ApprovalMatrixRequest),
            new NotFoundException(nameof(Domain.Entities.ApprovalMatrixRequest), request.Id));

        var approvalMatrixRequestDetailDto = _mapper.Map<ApprovalMatrixRequestDetailVm>(approvalMatrixRequest);

        return approvalMatrixRequestDetailDto;
    }
}