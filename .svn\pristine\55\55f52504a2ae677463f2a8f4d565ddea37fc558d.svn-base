﻿namespace ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Commands.Update;

public class
    UpdateSQLDBMirroringStatusCommandHandler : IRequestHandler<UpdateSQLDBMirroringStatusCommand,
        UpdateSQLDBMirroringStatusResponse>
{
    private readonly IMsSqlDbMirroringStatusRepository _dbmirroringMonitorStatusRepository;

    private readonly IMapper _mapper;

    public UpdateSQLDBMirroringStatusCommandHandler(IMapper mapper,
        IMsSqlDbMirroringStatusRepository dbmirroringMonitorStatusRepository)
    {
        _mapper = mapper;
        _dbmirroringMonitorStatusRepository = dbmirroringMonitorStatusRepository;
    }

    public async Task<UpdateSQLDBMirroringStatusResponse> Handle(UpdateSQLDBMirroringStatusCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _dbmirroringMonitorStatusRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.MSSQLDBMirroringStatus), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateSQLDBMirroringStatusCommand),
            typeof(Domain.Entities.MongoDbMonitorStatus));

        await _dbmirroringMonitorStatusRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateSQLDBMirroringStatusResponse
        {
            Message = Message.Update(nameof(Domain.Entities.MSSQLDBMirroringStatus), eventToUpdate.ReferenceId),

            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}