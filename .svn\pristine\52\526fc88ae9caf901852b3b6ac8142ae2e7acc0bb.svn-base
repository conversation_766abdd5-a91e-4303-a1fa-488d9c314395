﻿namespace ContinuityPatrol.Application.Features.DataSet.Commands.Update;

public class UpdateDataSetCommand : IRequest<UpdateDataSetResponse>
{
    public string Id { get; set; }
    public string DataSetName { get; set; }
    public string Description { get; set; }
    public string StoredQuery { get; set; }
    public string TableAccessId { get; set; }
    public string PrimaryTableName { get; set; }
    public string PrimaryTablePKColumn { get; set; }
    public string QueryType { get; set; }
    public string StoredProcedureName { get; set; }

    public override string ToString()
    {
        return $"DatasetName: {DataSetName}; Id:{Id};";
    }
}