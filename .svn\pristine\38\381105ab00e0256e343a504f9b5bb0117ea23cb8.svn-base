﻿using ContinuityPatrol.Application.Features.WorkflowInfraObject.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowInfraObject.Commands;

public class CreateWorkflowInfraObjectTests : IClassFixture<WorkflowInfraObjectFixture>
{
    private readonly WorkflowInfraObjectFixture _workflowInfraObjectFixture;

    private readonly Mock<IWorkflowInfraObjectRepository> _mockWorkflowInfraObjectRepository;

    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    private readonly CreateWorkflowInfraObjectCommandHandler _handler;

    public CreateWorkflowInfraObjectTests(WorkflowInfraObjectFixture workflowInfraObjectFixture)
    {
        _workflowInfraObjectFixture = workflowInfraObjectFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockLoggedInUserService = new Mock<ILoggedInUserService>();

        _mockWorkflowInfraObjectRepository = new Mock<IWorkflowInfraObjectRepository>();

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(Guid.NewGuid().ToString());

        _mockWorkflowInfraObjectRepository = WorkflowInfraObjectRepositoryMocks.CreateWorkflowInfraObjectRepository(_workflowInfraObjectFixture.WorkflowInfraObjects);

        _mockWorkflowInfraObjectRepository.Setup(x => x.GetWorkflowInfraObjectDetailByInfraObjectId(It.IsAny<string>())).ReturnsAsync(new List<Domain.Entities.WorkflowInfraObject> { new Domain.Entities.WorkflowInfraObject { InfraObjectName = "TestInfraObject", ActionType = "TestAction" } });

        _handler = new CreateWorkflowInfraObjectCommandHandler(_workflowInfraObjectFixture.Mapper, _mockWorkflowInfraObjectRepository.Object, mockPublisher.Object, _mockLoggedInUserService.Object);
    }

    [Fact]
    public async Task Handle_Should_Increase_Count_When_AddValid_WorkflowInfraObject()
    {
        await _handler.Handle(_workflowInfraObjectFixture.CreateWorkflowInfraObjectCommand, CancellationToken.None);

        var allInfraObjects = await _mockWorkflowInfraObjectRepository.Object.ListAllAsync();

        allInfraObjects.Count.ShouldBe(_workflowInfraObjectFixture.WorkflowInfraObjects.Count);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulWorkflowInfraObjectResponse_When_AddValidWorkflowInfraObject()
    {
        var result = await _handler.Handle(_workflowInfraObjectFixture.CreateWorkflowInfraObjectCommand, CancellationToken.None);

        result.ShouldBeOfType<CreateWorkflowInfraObjectResponse>();

        result.WorkflowInfraObjectId.ShouldNotBeNullOrEmpty();

        result.Message.ShouldContain("Workflow");

        result.Message.ShouldContain("attached to infraobject");

        var expectedWorkflowName = _workflowInfraObjectFixture.CreateWorkflowInfraObjectCommand.WorkflowName;

        var expectedInfraObjectName = _workflowInfraObjectFixture.CreateWorkflowInfraObjectCommand.InfraObjectName;

        result.Message.ShouldContain(expectedWorkflowName);

        result.Message.ShouldContain(expectedInfraObjectName);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowInfraObjectFixture.CreateWorkflowInfraObjectCommand, CancellationToken.None);

        _mockWorkflowInfraObjectRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.WorkflowInfraObject>()),
            Times.Once);
    }
}