﻿using ContinuityPatrol.Application.Features.ReportSchedule.Commands.Delete;
using ContinuityPatrol.Application.Features.ReportSchedule.Event.Delete;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.ReportSchedule.Commands
{
    public class DeleteReportScheduleTests
    {
        private readonly Mock<IJobScheduler> _mockJobScheduler;
        private readonly Mock<ILoadBalancerRepository> _mockLoadBalancerRepository;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly Mock<IReportScheduleRepository> _mockReportScheduleRepository;
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
        private readonly DeleteReportScheduleCommandHandler _handler;

        public DeleteReportScheduleTests()
        {
            _mockJobScheduler = new Mock<IJobScheduler>();
            _mockLoadBalancerRepository = new Mock<ILoadBalancerRepository>();
            _mockPublisher = new Mock<IPublisher>();
            _mockReportScheduleRepository = new Mock<IReportScheduleRepository>();
            _mockLoggedInUserService = new Mock<ILoggedInUserService>();

            _handler = new DeleteReportScheduleCommandHandler(
                _mockPublisher.Object,
                _mockReportScheduleRepository.Object,
                _mockLoadBalancerRepository.Object,
                _mockJobScheduler.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldReturnResponse_WhenScheduleDeletedSuccessfully()
        {
            var scheduleId = Guid.NewGuid().ToString();
            var command = new DeleteReportScheduleCommand { Id = scheduleId };

            var reportSchedule = new Domain.Entities.ReportSchedule
            {
                ReferenceId = scheduleId,
                ReportName = "Test Report",
                IsActive = true
            };

            var nodeConfig = new Domain.Entities.LoadBalancer
            {
                ConnectionType = "http",
                IPAddress = "127.0.0.1",
                Port = 8080
            };

            _mockReportScheduleRepository.Setup(r => r.GetByReferenceIdAsync(scheduleId))
                .ReturnsAsync(reportSchedule);

            _mockReportScheduleRepository.Setup(r => r.UpdateAsync(It.IsAny<Domain.Entities.ReportSchedule>()))
                .ReturnsAsync(reportSchedule);

            _mockPublisher.Setup(p => p.Publish(It.IsAny<ReportScheduleDeleteEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            _mockLoadBalancerRepository.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory("ALL", "Load Balancer"))
                .ReturnsAsync(nodeConfig);

            _mockJobScheduler.Setup(js => js.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
                .Returns(Task.CompletedTask);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("Report Scheduler", result.Message.Trim());
            Assert.Equal("Test Report", result.Message.Trim());
            Assert.False(reportSchedule.IsActive);

            _mockReportScheduleRepository.Verify(r => r.UpdateAsync(It.IsAny<Domain.Entities.ReportSchedule>()), Times.Once);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<ReportScheduleDeleteEvent>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockJobScheduler.Verify(js => js.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenScheduleDoesNotExist()
        {
            var scheduleId = Guid.NewGuid().ToString();
            var command = new DeleteReportScheduleCommand { Id = scheduleId };

            _mockReportScheduleRepository.Setup(r => r.GetByReferenceIdAsync(scheduleId))
                .ReturnsAsync((Domain.Entities.ReportSchedule)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_ShouldThrowArgumentException_WhenIdIsInvalid()
        {
            var command = new DeleteReportScheduleCommand { Id = "invalid-guid" };

            await Assert.ThrowsAsync<ArgumentException>(() => _handler.Handle(command, CancellationToken.None));
        }
    }
}
