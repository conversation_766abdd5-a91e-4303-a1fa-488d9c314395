﻿using ContinuityPatrol.Application.Features.BulkImport.Commands.Create.Helper;
using ContinuityPatrol.Application.Features.InfraObject.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObject.Commands.Update;
using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateState;
using ContinuityPatrol.Application.Features.InfraObject.Events.DashboardViewEvent.Create;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetByReplicationCategoryType;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetByReplicationType;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByBusinessFunctionId;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByBusinessServiceId;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByDrReady;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByServerId;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectDetailById;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class InfraObjectProfile : Profile
{
    public InfraObjectProfile()
    {
        CreateMap<InfraObject, CreateInfraObjectCommand>().ReverseMap();
        CreateMap<UpdateInfraObjectCommand, InfraObject>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<InfraObjectDetailVm, UpdateInfraObjectCommand>().ReverseMap();
        CreateMap<UpdateInfraObjectCommand, InfraObjectDetailVm>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<CreateInfraObjectCommand, InfraObjectViewModel>().ReverseMap();
        CreateMap<UpdateInfraObjectCommand, InfraObjectViewModel>().ReverseMap();

        CreateMap<InfraObject, InfraObjectDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraObject, InfraObjectListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraObject, GetInfraObjectNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraObject, GetInfraObjectByBusinessFunctionIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraObjectView, GetInfraObjectByBusinessFunctionIdVm>()
          .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraObject, GetInfraObjectByBusinessServiceIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraObject, InfraObjectByDrReadyListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraObject, GetInfraObjectByServerIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraObjectView, GetInfraObjectByServerIdVm>()
           .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<UpdateInfraObjectListCommand, InfraObject>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<InfraObject, InfraObjectListByReplicationTypeVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraObject, InfraObjectListByReplicationCategoryTypeVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraObjectView, InfraObjectListByReplicationCategoryTypeVm>()
           .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraObjectView, GetInfraObjectByBusinessServiceIdVm>()
         .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));


        CreateMap<InfraObjectView, InfraObjectListByReplicationTypeVm>()
                 .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<MonitorService, MonitorServiceDto>().ReverseMap();

        CreateMap<DashboardView, InfraObjectDashboardViewCreatedEvent>().ReverseMap();
        CreateMap<InfraObjectView, GetInfraObjectDetailByIdVm>()
           .ForMember(dest => dest.InfraObjectId, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<InfraObject, GetInfraObjectDetailByIdVm>()
            .ForMember(dest => dest.InfraObjectId, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<InfraDashboardView, GetInfraObjectDetailByIdVm>()
           .ForMember(dest => dest.InfraObjectId, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<GetInfraObjectDetailByIdVm, GetInfraObjectDetailByIdVm>().ReverseMap();
           

        //DashboardDetails

        CreateMap<Database, DatabaseDto>()
            .ForMember(dest => dest.DatabaseId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DatabaseName, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.ModeType))
            .ForMember(dest => dest.Sid,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonDatabaseSidValue(src.Properties)))
            .ForMember(dest => dest.Version,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "version")));
        CreateMap<DatabaseView, DatabaseDto>()
           .ForMember(dest => dest.DatabaseId, opt => opt.MapFrom(src => src.ReferenceId))
           .ForMember(dest => dest.DatabaseName, opt => opt.MapFrom(src => src.Name))          
           .ForMember(dest => dest.Sid, opt => opt.MapFrom(src => src.SID))
           .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.ModeType))
           .ForMember(dest => dest.Version, opt => opt.MapFrom(src => src.Version))
           .ForMember(dest => dest.NodeName, opt => opt.MapFrom(src => src.Name));

        CreateMap<Server, ServerDto>()
            .ForMember(dest => dest.ServerId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.ServerName, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.IPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "IpAddress")))
            .ForMember(dest => dest.HostName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "HostName")))
            .ForMember(dest => dest.ConnectViaHostName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "ConnectViaHostName")));
        CreateMap<ServerView, ServerDto>()
            .ForMember(dest => dest.ServerId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.ServerName, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.ServerType))
            .ForMember(dest => dest.IPAddress,
                opt => opt.MapFrom(src => src.IpAddress))
            .ForMember(dest => dest.HostName,
                opt => opt.MapFrom(src => src.HostName))
            .ForMember(dest => dest.ConnectViaHostName,
                opt => opt.MapFrom(src => src.ConnectViaHostName));


        //BulkDataInsert
        CreateMap<Server, CreateBulkDataServerListCommand>().ReverseMap();
        CreateMap<Database, CreateBulkDataDataBaseListCommand>().ReverseMap();
        CreateMap<Replication, CreateBulkDataReplicationListCommand>().ReverseMap();
        CreateMap<InfraObject, CreateBulkDataInfraObjectListCommand>().ReverseMap();

        //DrDrill
        CreateMap<List<InfraObject>, DrReadyCount>()
            .ForMember(dest => dest.TotalCount, opt => opt.MapFrom(src => src.Count))
            .ForMember(dest => dest.TrueCount, opt => opt.MapFrom(src => src.Count(x => x.DRReady)))
            .ForMember(dest => dest.FalseCount, opt => opt.MapFrom(src => src.Count(x => !x.DRReady)));

        //CreateMap<List<Server>, InfraObjectDetailCountVm>()
        //    .ForMember(dest => dest.InfraObjectUpCount,
        //        opt => opt.MapFrom(src => src.Count(x => x.Status.Trim().ToLower().Equals("up"))))
        //    .ForMember(dest => dest.InfraObjectDownCount,
        //        opt => opt.MapFrom(src =>
        //            src.Count(x => x.Status.Trim().ToLower().Equals("down") || string.IsNullOrWhiteSpace(x.Status))));


        //CreateMap<List<Server>, InfraObjectDetailCountByTypeNameVm>()
        //    .ForMember(dest => dest.InfraObjectUpCount,
        //        opt => opt.MapFrom(src => src.Count(x => x.Status.Trim().ToLower().Equals("up"))))
        //    .ForMember(dest => dest.InfraObjectDownCount,
        //        opt => opt.MapFrom(src =>
        //            src.Count(x => x.Status.Trim().ToLower().Equals("down") || string.IsNullOrWhiteSpace(x.Status))));

        CreateMap<PaginatedResult<InfraObject>,PaginatedResult<InfraObjectListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));

        CreateMap<InfraObjectView, InfraObjectListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<PaginatedResult<InfraObjectView>, PaginatedResult<InfraObjectListVm>>()
           .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}