﻿using ContinuityPatrol.Application.Features.FourEyeApprovers.Commands.Create;
using ContinuityPatrol.Application.Features.FourEyeApprovers.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.FourEyeApprovers.Queries.GetPaginatedListProfile;
using ContinuityPatrol.Application.Features.FourEyeApprovers.Queries.GetPaginatedListWorkflow;
using ContinuityPatrol.Domain.ViewModels.FourEyeApproversModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class FourEyeApproverService : BaseClient, IFourEyeApproverService
{
    public FourEyeApproverService(IConfiguration config, IAppCache cache, ILogger<FourEyeApproverService> logger)
       : base(config, cache, logger)
    {

    }


    public Task<BaseResponse> CreateAsync(CreateFourEyeApproversCommand createFourEyeApproversCommand)
    {
        throw new NotImplementedException();
    }

    public Task<PaginatedResult<FourEyeApproversListVM>> GetPaginatedFourEyeApprovers(GetFourEyeApproversPaginatedListQuery query)
    {
        throw new NotImplementedException();
    }

    public Task<PaginatedResult<FourEyeApproversListVM>> GetPaginatedFourEyeApprovers_Profile(GetFourEyeApproversPaginatedListProfileQuery query)
    {
        throw new NotImplementedException();
    }

    public Task<PaginatedResult<FourEyeApproversListVM>> GetPaginatedFourEyeApprovers_Workflow(GetFourEyeApproversPaginatedListWorkflowQuery query)
    {
        throw new NotImplementedException();
    }
}
