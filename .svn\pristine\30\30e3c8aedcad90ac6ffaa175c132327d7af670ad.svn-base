﻿using ContinuityPatrol.Application.Features.Workflow.Events.Create;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Infrastructure.Hubs;

namespace ContinuityPatrol.Application.Features.Workflow.Commands.Create;

public class CreateWorkflowCommandHandler : IRequestHandler<CreateWorkflowCommand, CreateWorkflowResponse>
{
    private readonly IHubContext<NotificationHub> _hubContext;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IVersionManager _versionManager;
    private readonly IWorkflowHistoryRepository _workflowHistoryRepository;
    private readonly IWorkflowRepository _workflowRepository;

    public CreateWorkflowCommandHandler(IMapper mapper, IWorkflowRepository workflowRepository,
        IWorkflowHistoryRepository workflowHistoryRepository, ILoggedInUserService userService, IPublisher publisher,
        IVersionManager versionManager, IHubContext<NotificationHub> hubContext)
    {
        _mapper = mapper;
        _workflowRepository = workflowRepository;
        _workflowHistoryRepository = workflowHistoryRepository;
        _loggedInUserService = userService;
        _publisher = publisher;
        _versionManager = versionManager;
        _hubContext = hubContext;
    }

    public async Task<CreateWorkflowResponse> Handle(CreateWorkflowCommand request, CancellationToken cancellationToken)
    {
        request.CompanyId = _loggedInUserService.CompanyId ?? request.CompanyId;

        var workflow = _mapper.Map<Domain.Entities.Workflow>(request);

        var version = await _versionManager.GetVersion(request.Version);

        workflow.Version = version;

        workflow = await _workflowRepository.AddAsync(workflow);

        var workflowHistory = new Domain.Entities.WorkflowHistory
        {
            LoginName = _loggedInUserService.LoginName,
            CompanyId = _loggedInUserService.CompanyId,
            WorkflowId = workflow.ReferenceId,
            WorkflowName = workflow.Name,
            Version = workflow.Version,
            Properties = workflow.Properties,
            UpdaterId = _loggedInUserService.UserId,
            Comments = request.Comments
        };
        await _workflowHistoryRepository.AddAsync(workflowHistory);

        var response = new CreateWorkflowResponse
        {
            Message = Message.Create(nameof(Domain.Entities.Workflow), workflow.Name),

            WorkflowId = workflow.ReferenceId
        };

        await _hubContext.Clients.All.SendAsync("notification", new
        {
            Message = $"Workflow '{workflow.Name} created' by '{_loggedInUserService.LoginName}.'",
            UserName = _loggedInUserService.LoginName
        }, cancellationToken);

        await _publisher.Publish(new WorkflowCreatedEvent { WorkflowName = workflow.Name }, cancellationToken);

        return response;
    }
}