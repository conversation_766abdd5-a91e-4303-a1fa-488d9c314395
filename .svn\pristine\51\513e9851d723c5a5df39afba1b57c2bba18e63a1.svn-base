namespace ContinuityPatrol.Application.Features.ServerSubType.Commands.Update;

public class UpdateServerSubTypeCommandValidator : AbstractValidator<UpdateServerSubTypeCommand>
{
    private readonly IServerSubTypeRepository _serverSubTypeRepository;

    public UpdateServerSubTypeCommandValidator(IServerSubTypeRepository serverSubTypeRepository)
    {
        _serverSubTypeRepository = serverSubTypeRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.ServerTypeName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(y => y)
            .NotNull()
            .MustAsync(VerifyGuid)
            .WithMessage("Id is invalid");
    }

    private Task<bool> VerifyGuid(UpdateServerSubTypeCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.Id, "ServerSubType Id");
        Guard.Against.InvalidGuidOrEmpty(p.ServerTypeId, "ServerType Id");

        return Task.FromResult(true);
    }
}