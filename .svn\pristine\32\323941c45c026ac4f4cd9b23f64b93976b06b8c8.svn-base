﻿using ContinuityPatrol.Application.Features.BulkImport.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Next;
using ContinuityPatrol.Application.Features.BulkImport.Commands.RollBack;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class BulkImportController : CommonBaseController
{
   
    [HttpPost]
    [Authorize(Policy = Permissions.Dashboard.Create)]
    public async Task<ActionResult> CreateBulkImport([FromBody] CreateBulkImportCommand  createBulkImportCommand)
    {
        Logger.LogDebug($"Create bulk Import {createBulkImportCommand.Id}.");

        return Ok(await Mediator.Send(createBulkImportCommand));
    }

    [HttpPut, Route("Next")]
    [Authorize(Policy = Permissions.Dashboard.Edit)]
    public async Task<ActionResult> NextBulkImportAction([FromBody] NextBulkImportCommand nextBulkImportCommand)
    {
        Logger.LogDebug($"Next bulk Import {nextBulkImportCommand.GroupId}.");

        return Ok(await Mediator.Send(nextBulkImportCommand));
    }

    [HttpPut, Route("RollBack")]
    [Authorize(Policy = Permissions.Dashboard.Edit)]
    public async Task<ActionResult> RollBackBulkImportAction([FromBody] RollBackBulkImportCommand rollBackBulkImportCommand)
    {
        Logger.LogDebug($"RollBack bulk Import {rollBackBulkImportCommand.GroupId}.");

        return Ok(await Mediator.Send(rollBackBulkImportCommand));
    }


    [NonAction]
    public override void ClearDataCache()
    {

    }
}
