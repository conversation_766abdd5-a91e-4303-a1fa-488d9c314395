﻿
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/dashboard.css" rel="stylesheet" />
@Html.AntiForgeryToken()

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title" title="MySQL NLS Detail Monitoring">
            <i class="cp-monitoring"></i><span>MySQL NLS Detail Monitoring :</span> <span id="infraName"></span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>
    </div>
    <div class="monitor_pages">
        <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row g-2 mt-0">
            <div class="col-5 d-grid">
                <div class="card Card_Design_None  h-100 mb-0">
                    @* <div class="card-header card-title" title="MySQL Infra">MySQL Infra</div> *@
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed;">
                            <thead>
                                <tr>
                                    <th title="Database Details">Database Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody style="">
                                <tr>
                                   <td class="fw-semibold text-truncate" title="IP Address/HostName">
                                        <i class="text-secondary cp-server me-1 fs-6"></i><span>IP Address/HostName</span>
                                    </td>
                                    <td class="text-truncate"><span  id="PR_Server_IpAddress"></span></td>
                                    <td class="text-truncate"><span  id="DR_Server_IpAddress"></span></td>
                                </tr>
                                <tr>
                                   <td class="fw-semibold text-truncate" title="Database Name">
                                        <i class="text-secondary cp-database me-1 fs-6"></i><span >Database Name</span>
                                    </td>
                                   <td class="text-truncate"><span  id="PR_Database"></span></td>
                                   <td class="text-truncate"><span  id="DR_Database"></span></td>
                                </tr>
                                <tr>
                                   <td class="fw-semibold text-truncate" title="Database Version">
                                        <i class="text-secondary cp-database-success me-1 fs-6"></i>
                                        <span >Database Version</span>
                                    </td>
                                    <td class="text-truncate"><span id="PR_Database_Version"></span></td>
                                    <td class="text-truncate"><span  id="DR_Database_Version"></span></td>
                                </tr>
                                <tr>
                                   <td class="fw-semibold text-truncate" title="Database Service Status">
                                        <i class="text-secondary cp-mysql-data me-1 fs-6"></i>
                                        <span >Database Service Status</span>
                                    </td>
                                   <td class="text-truncate"><span  id="PRService_Status"></span></td>
                                   <td class="text-truncate"><span  id="DR_Service_Status"></span></td>
                                </tr>
                                <tr>
                                   <td class="fw-semibold text-truncate" title="Database Status(Read-Only)">
                                        <i class="text-secondary cp-Storage-chain me-1 fs-6"></i>
                                        <span >Database Status(Read-Only)</span>
                                    </td>
                                   <td class="text-truncate"><span  id="PRDatabase_State"></span></td>
                                   <td class="text-truncate"><span  id="DR_Database_State"></span></td>
                                </tr>
                                <tr>
                                   <td class="fw-semibold text-truncate" title="Slave Running Status">
                                        <i class="text-secondary cp-Storage-chain me-1 fs-6"></i>
                                        <span >Slave Running Status</span>
                                    </td>
                                    <td class="text-truncate"><span id="PR_Slave_Running_State"></span></td>
                                    <td class="text-truncate"><span id="DR_Slave_Running_State"></span></td>
                                </tr>
                                <tr>
                                   <td class="fw-semibold text-truncate" title="Slave IO Running Status">
                                        <i class="text-secondary cp-Storage-chain me-1 fs-6"></i>
                                        <span >Slave IO Running Status</span>
                                    </td>
                                    <td class="text-truncate"><span  id="PR_IO_Running_Status"></span></td>
                                    <td class="text-truncate"><span  id="DR_IO_Running_Status"></span></td>
                                </tr>
                                <tr>
                                   <td class="fw-semibold text-truncate" title="Slave SQL Running Status">
                                        <i class="text-secondary cp-Storage-chain me-1 fs-6"></i>
                                        <span >Slave SQL Running Status</span>
                                    </td>
                                    <td class="text-truncate"><span  id="PR_SQL_Running_Status"></span></td>
                                    <td class="text-truncate"><span  id="DR_SQL_Running_Status"></span></td>
                                </tr>
                                <tr>
                                   <td class="fw-semibold text-truncate" title="Master Log File">
                                        <i class="text-secondary cp-log-file-name me-1 fs-6"></i>
                                        <span >Master Log File</span>
                                    </td>
                                    <td class="text-truncate"><span  id="PR_Master_Log_File"></span></td>
                                    <td class="text-truncate"><span  id="DR_Master_Log_File"></span></td>
                                </tr>
                                <tr>
                                   <td class="fw-semibold text-truncate" title="Relay Master Log File">
                                        <i class="text-secondary cp-log-file-name me-1 fs-6"></i>
                                        <span >Relay Master Log File</span>
                                    </td>
                                    <td class="text-truncate"><span  id="PR_Relay_Master_Log_File"></span></td>
                                    <td class="text-truncate"><span  id="DR_Relay_Master_Log_File"></span></td>
                                </tr>
                                <tr>
                                   <td class="fw-semibold text-truncate" title="Master Log Position">
                                        <i class="text-secondary cp-log-file-name me-1 fs-6"></i>
                                        <span >Master Log Position</span>
                                    </td>
                                    <td class="text-truncate"><span  id="PR_Master_Log_Position"></span></td>
                                    <td class="text-truncate"><span  id="DR_Master_Log_Position"></span></td>
                                </tr>
                                <tr>
                                   <td class="fw-semibold text-truncate" title="Exec Master Log Position">
                                        <i class="text-secondary cp-log-file-name me-1 fs-6"></i>
                                        <span >Exec Master Log Position</span>
                                    </td>
                                    <td class="text-truncate"><span  id="PR_Exec_Master_Log_Position"></span></td>
                                    <td class="text-truncate"><span  id="DR_Exec_Master_Log_Position"></span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-7 d-grid">

       
                        <div class="card Card_Design_None mb-2">
                            <div class="card-header card-title" title="Solution Diagram">Solution Diagram</div>
                            <div class="card-body text-center">
                                <div id="Solution_Diagram" class="w-100 h-100"> </div>
                            </div>
                        </div>
                 
                 
                  
                            <div class="card mb-2">
                                <div class="card-body">
                        <i class="cp-last-copied-transaction text-primary fs-4 me-2 bg-body-secondary p-1 rounded-2" title="Replication Connect State"></i><span class="fw-semibold">
                                        Replication Connect State
                                    </span>
                                    <div class="d-flex mt-3">
                                        <div class="w-50 d-grid">
                                            <small class="text-primary fw-semibold" title="Primary">Primary</small>
                                            @*<i class="cp-success text-success"></i>*@
                                            <span id="PRReplication_Connect_State"></span>
                                        </div>
                                        <div class="w-50 d-grid">
                                <small title="DR" class="text-info fw-semibold dynamicSite-header">DR</small>
                                            @*<i class="cp-success text-success"></i>*@
                                            <span id="DR_Replication_Connect_State"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card mb-0">
                                <div class="card-body">
                        <i class="cp-data-lag text-primary fs-4 me-2 bg-body-secondary p-1 rounded-2" title="Datalag"></i><span class="fw-semibold">DataLag</span>
                                    <div class="d-flex mt-3">
                                        <div class="w-50 d-grid">
                                            @*<small class="text-primary" title="Primary">Primary</small>*@
                                            <span>
                                                @* <i class="cp-time text-primary mt-2"></i> *@ <span id="DR_PR_Datalag"></span>
                                            </span>
                                        </div>
                                        @* <div class="w-50 d-grid">
                                        <small class="text-info" title="DR">DR</small>
                                        </div>*@
                                    </div>
                                </div>
                            </div>
                      
            </div>
            <div class="col-12 d-grid">
                <div class="card Card_Design_None mb-0" id="mssqlserver">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span title=" Services ">
                            Services
                        </span>

                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" id="tableCluster" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Service / Process / Workflow Name">Service / Process / Workflow Name</th>
                                    <th class="text-primary" title="Server IP/HostName">Server IP/HostName</th>
                                    <th class="">Status</th>
                                    <th class="">Last Monitored Time</th>
                                </tr>
                            </thead>
                            <tbody id="mssqlserverbody">                                

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
           
        </div>
    </div>
</div>

<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
@* <script src="~/js/monitoring/mysql.js"></script> *@
<script src="~/js/Monitoring/MonitoringMySQL.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>