﻿using ContinuityPatrol.Application.Features.Company.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.Company.Queries;

public class GetCompanyPaginatedListQueryHandlerTests : IClassFixture<CompanyFixture>
{
    private readonly GetCompanyPaginatedListQueryHandler _handler;
    private readonly Mock<ICompanyRepository> _mockCompanyRepository;
    private readonly CompanyFixture _companyNewFixture;

    public GetCompanyPaginatedListQueryHandlerTests(CompanyFixture companyFixture)
    {
        _companyNewFixture = companyFixture;

        _companyNewFixture.Companies[0].ReferenceId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
        _companyNewFixture.Companies[0].Name = "Perpetuuiti Technosoft Pvt Ltd";
        _companyNewFixture.Companies[0].DisplayName = "PTS";
        _companyNewFixture.Companies[0].LogoName = "Perpetuuiti";

        _companyNewFixture.Companies[1].ParentId = _companyNewFixture.Companies[0].ReferenceId;
        _companyNewFixture.Companies[1].Name = "PTS123";
        _companyNewFixture.Companies[1].WebAddress = "www.ptechnosoft.com";
        _companyNewFixture.Companies[1].LogoName = "PTS_Test";

        _mockCompanyRepository = CompanyRepositoryMocks.GetPaginatedCompanyRepository(_companyNewFixture.Companies);

        _handler = new GetCompanyPaginatedListQueryHandler(_companyNewFixture.Mapper, _mockCompanyRepository.Object);
    }

    [Fact]
    public void Should_Create_Query_With_Valid_Parameters()
    {
        // Act
        var query = new GetCompanyPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "test",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        // Assert
        Assert.NotNull(query);
        Assert.Equal(1, query.PageNumber);
        Assert.Equal(10, query.PageSize);
        Assert.Equal("test", query.SearchString);
        Assert.Equal("Name", query.SortColumn);
        Assert.Equal("asc", query.SortOrder);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetCompanyPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<CompanyListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_ParentCompanies_ParentName_ShouldBeNA()
    {
        var result = await _handler.Handle(new GetCompanyPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<CompanyListVm>>();

        var parentCompany = result.Data.FirstOrDefault(x => x.IsParent);

        if (parentCompany != null)
        {
            parentCompany.ParentName.ShouldBe("NA");

            parentCompany.ParentId.ShouldBe(0.ToString());
        }
    }

    [Fact]
    public async Task Handle_Return_ChildCompanies_ParentName_ShouldNotEmptyOrNA()
    {
        _mockCompanyRepository.Setup(x => x.GetCompanyByLoginCompanyId(It.IsAny<string>())).ReturnsAsync(_companyNewFixture.Companies[0]);

        var result = await _handler.Handle(new GetCompanyPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<CompanyListVm>>();

        var childCompany = result.Data.FirstOrDefault(x => x.IsParent == false);

        if (childCompany != null)
        {
            childCompany.ParentName.ShouldNotBeNullOrEmpty();
            
            Assert.True(childCompany.ParentId.IsValidGuid());
        }
    }

    [Fact]
    public async Task Handle_Return_Companies_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetCompanyPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=Perpetuuiti;displayname=PTS;webaddress=ptechnosoft;logoname=Perpetuuiti" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<CompanyListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Name.ShouldBe("Perpetuuiti Technosoft Pvt Ltd");

        result.Data[0].DisplayName.ShouldBe("PTS");

        result.Data[1].WebAddress.ShouldBe("www.ptechnosoft.com");
    }

    [Fact]
    public async Task Handle_Return_PaginatedCompanies_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetCompanyPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<CompanyListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<CompanyListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].CompanyLogo.ShouldNotBeEmpty();

        result.Data[0].DisplayName.ShouldBe("PTS");

        result.Data[0].WebAddress.ShouldNotBeEmpty();

        result.Data[0].DisplayName.ShouldNotBeEmpty();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetCompanyPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABCD" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<CompanyListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetCompanyPaginatedListQuery(), CancellationToken.None);

        _mockCompanyRepository.Verify(x => x.PaginatedListAllAsync(It.IsAny<int>(), 
            It.IsAny<int>(), It.IsAny<CompanyFilterSpecification>(), 
            It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }
}