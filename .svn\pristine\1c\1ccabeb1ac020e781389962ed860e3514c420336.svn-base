﻿using ContinuityPatrol.Domain.ViewModels.FourEyeApproversModel;
using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IFourEyeRepository : IRepository<FourEyeApprovers>
{
    Task<IQueryable<FourEyeApproversListVM>> GetFourEyeApprovers();
    Task<IQueryable<FourEyeApproversListVM>> GetFourEyeApproversWorkflow();
    Task<IQueryable<FourEyeApproversListVM>> GetFourEyeApproversProfile();
}