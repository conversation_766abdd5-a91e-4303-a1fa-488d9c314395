﻿using ContinuityPatrol.Application.Features.FastCopyMonitor.Queries.GetByDataSyncId;
using ContinuityPatrol.Application.Features.FastCopyMonitor.Queries.GetPagination;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class FastCopyMonitorProfile:Profile
{
    public FastCopyMonitorProfile()
    {
        CreateMap<FastCopyMonitor, FatCopyMonitorListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<FastCopyMonitor, FastCopyMonitorPaginatedListVm>()
            .ForMember(dest=>dest.Id,opt=>opt.MapFrom(src=>src.ReferenceId));
        CreateMap<PaginatedResult<FastCopyMonitor>, PaginatedResult<FastCopyMonitorPaginatedListVm>>()
          .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));

    }

}
