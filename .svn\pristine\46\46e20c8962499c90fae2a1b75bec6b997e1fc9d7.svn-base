﻿namespace ContinuityPatrol.Application.Features.Company.Commands.Update;

public class UpdateCompanyCommand : IRequest<UpdateCompanyResponse>
{
    public string Id { get; set; }

    public string Name { get; set; }

    public string DisplayName { get; set; }

    public bool IsParent { get; set; }

    public string ParentId { get; set; }

    public string CompanyLogo { get; set; }

    public string LogoName { get; set; }

    public string WebAddress { get; set; }

    public override string ToString()
    {
        return $"Name: {Name}; DisplayName: {DisplayName}; Id:{Id};";
    }
}