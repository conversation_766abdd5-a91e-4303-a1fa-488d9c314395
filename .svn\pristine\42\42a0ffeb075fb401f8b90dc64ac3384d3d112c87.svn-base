﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.Report.Queries.GetSchedulerWorkflowActionResultsReport;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Infrastructure.Persistence;
using Microsoft.Identity.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ContinuityPatrol.Persistence.Repositories
{
    public class SchedulerWorkflowActionResultsRepository : BaseRepository<SchedulerWorkflowActionResults>, ISchedulerWorkflowActionResultsRepository
    {
        private readonly ILoggedInUserService _loggedInUserService;
        private readonly ApplicationDbContext _dbContext;
        public SchedulerWorkflowActionResultsRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext, loggedInUserService)
        {
            _dbContext = dbContext;
            _loggedInUserService = loggedInUserService;
        }
        public async Task<List<SchedulerWorkflowActionResults>> GetSchedulerWorkflowActionResultListByWorkflowId(string workflowId, string infraReferenceId)
        {
            var result = await _dbContext.SchedulerWorkflowActionResults.Active()
            .Where(x => x.WorkflowId.Equals(workflowId) 
            && x.InfraObjectSchedulerLogsId.Equals(infraReferenceId))
            .ToListAsync();

            return result;
        }
    }
}