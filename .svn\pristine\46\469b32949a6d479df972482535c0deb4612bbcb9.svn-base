﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ContinuityPatrol.Application.Features.Report.Queries.GetCyberSnapList
{
    public class GetCyberSnapsListQueryHandler : IRequestHandler<GetCyberSnapsListQuery, List<GetCyberSnapsListVm>>
    {
        private readonly ILogger<GetCyberSnapsListQueryHandler> _logger;
        private readonly ICyberSnapsRepository _cyberSnapsRepository;
        private readonly IMapper _mapper;

        public GetCyberSnapsListQueryHandler(ILogger<GetCyberSnapsListQueryHandler> logger, ICyberSnapsRepository cyberSnapsRepository, IMapper mapper)
        {
            _logger = logger;
            _cyberSnapsRepository = cyberSnapsRepository;
            _mapper = mapper;
        }
        public async Task<List<GetCyberSnapsListVm>> Handle(GetCyberSnapsListQuery request, CancellationToken cancellationToken)
        {
            var getCyberSnapListVms = await _cyberSnapsRepository.GetCyberSnapsListByDate(request.StartDate, request.EndDate);
            var seenTags = new HashSet<string>();

            // Remove duplicates based on the Tag property
            var uniqueCyberSnapListVms = getCyberSnapListVms
                .Where(cyberSnapList => seenTags.Add(cyberSnapList.Tag)) // Add returns true only if the tag is unique
                .ToList();
            return uniqueCyberSnapListVms.Count == 0 ? new List<GetCyberSnapsListVm>() : _mapper.Map<List<GetCyberSnapsListVm>>(uniqueCyberSnapListVms);
        }
    }
}
