using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberComponentGroupModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Cyber;

public class CyberComponentGroupService : BaseService, ICyberComponentGroupService
{
    public CyberComponentGroupService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<CyberComponentGroupListVm>> GetCyberComponentGroupList()
    {
        Logger.LogDebug("Get All CyberComponentGroups");

        return await Mediator.Send(new GetCyberComponentGroupListQuery());
    }

    public async Task<CyberComponentGroupDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberComponentGroup Id");

        Logger.LogDebug($"Get CyberComponentGroup Detail by Id '{id}'");

        return await Mediator.Send(new GetCyberComponentGroupDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateCyberComponentGroupCommand createCyberComponentGroupCommand)
    {
        Logger.LogDebug($"Create CyberComponentGroup '{createCyberComponentGroupCommand}'");

        return await Mediator.Send(createCyberComponentGroupCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateCyberComponentGroupCommand updateCyberComponentGroupCommand)
    {
        Logger.LogDebug($"Update CyberComponentGroup '{updateCyberComponentGroupCommand}'");

        return await Mediator.Send(updateCyberComponentGroupCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberComponentGroup Id");

        Logger.LogDebug($"Delete CyberComponentGroup Details by Id '{id}'");

        return await Mediator.Send(new DeleteCyberComponentGroupCommand { Id = id });
    }
    #region NameExist
    public async Task<bool> IsCyberComponentGroupNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "CyberComponentGroup Name");

        Logger.LogDebug($"Check Name Exists Detail by CyberComponentGroup Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetCyberComponentGroupNameUniqueQuery { Name = name, Id = id });
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<CyberComponentGroupListVm>> GetPaginatedCyberComponentGroups(GetCyberComponentGroupPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in CyberComponentGroup Paginated List");

        return await Mediator.Send(query);
    }
    #endregion
}
