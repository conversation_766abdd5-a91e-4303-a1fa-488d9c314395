﻿using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Delete;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetList;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class ApprovalMatrixController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<ApprovalMatrixListVm>>> GetApprovalMatrixList()
    {
        Logger.LogDebug("Get All ApprovalMatrices");

        return Ok(await Cache.GetOrAddAsync(
            ApplicationConstants.Cache.AllApprovalMatrixCacheKey + LoggedInUserService.CompanyId,
            () => Mediator.Send(new GetApprovalMatrixListQuery()), CacheExpiry));
    }

    [HttpGet("{id}", Name = "GetApprovalMatrix")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<ApprovalMatrixDetailVm>> GetApprovalMatrixById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ApprovalMatrix Id");

        Logger.LogDebug($"Get ApprovalMatrix Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetApprovalMatrixDetailQuery { Id = id }));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Orchestration.Create)]
    public async Task<ActionResult<CreateApprovalMatrixResponse>> CreateApprovalMatrix(
        [FromBody] CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        Logger.LogDebug($"Create Approval Matrix '{createApprovalMatrixCommand.Name}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateApprovalMatrix), await Mediator.Send(createApprovalMatrixCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Orchestration.Edit)]
    public async Task<ActionResult<UpdateApprovalMatrixResponse>> UpdateApprovalMatrix(
        [FromBody] UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        Logger.LogDebug($"Update Approval Matrix '{updateApprovalMatrixCommand.Name}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateApprovalMatrixCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Orchestration.Delete)]
    public async Task<ActionResult<DeleteApprovalMatrixResponse>> DeleteApprovalMatrix(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Approval Matrix Id");

        Logger.LogDebug($"Delete ApprovalMatrix Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteApprovalMatrixCommand { Id = id }));
    }

    [Route("paginated-list")]
    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<PaginatedResult<ApprovalMatrixListVm>>> GetPaginatedApprovalMatrixList(
        [FromQuery] GetApprovalMatrixPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in ApprovalMatrix Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [Route("name-exist"), HttpGet]
    public async Task<ActionResult> IsApprovalMatrixNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "Approval Matrix Name");

        Logger.LogDebug($"Check Name Exists Detail by Approval Matrix Name '{name}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetMatrixNameUniqueQuery { MatrixName = name, Id = id }));
    }


    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys =
        {
            ApplicationConstants.Cache.AllApprovalMatrixCacheKey + LoggedInUserService.CompanyId,
            ApplicationConstants.Cache.AllApprovalMatrixNameCacheKey
        };

        ClearCache(cacheKeys);
    }
}