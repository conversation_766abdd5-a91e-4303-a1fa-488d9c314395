using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Web.Middlewares;
using ValidationResult = FluentValidation.Results.ValidationResult;

namespace ContinuityPatrol.Web.UnitTests.Middlewares;

public class ErrorHandlingMiddlewareTests
{
    private readonly Mock<RequestDelegate> _nextMock;
    private readonly Mock<ILogger<ErrorHandlingMiddleware>> _loggerMock;
    private readonly ErrorHandlingMiddleware _middleware;
    private readonly DefaultHttpContext _context;

    public ErrorHandlingMiddlewareTests()
    {
        _nextMock = new Mock<RequestDelegate>();
        _loggerMock = new Mock<ILogger<ErrorHandlingMiddleware>>();
        _middleware = new ErrorHandlingMiddleware(_nextMock.Object, _loggerMock.Object);
        _context = new DefaultHttpContext();
        _context.Response.Body = new MemoryStream();
    }

    [Fact]
    public async Task InvokeAsync_WithNoException_ShouldCallNext()
    {
        // Arrange
        _context.Request.Method = "GET";
        _context.Request.Path = "/test";

        // Act
        await _middleware.InvokeAsync(_context);

        // Assert
        _nextMock.Verify(next => next(_context), Times.Once);
    }

    [Fact]
    public async Task InvokeAsync_WithValidationException_ShouldLogValidationErrors()
    {
        //// Arrange
        //var validationErrors = new List<ValidationFailure>
        //{
        //    new ValidationFailure("Field1", "Field1 is required"),
        //    new ValidationFailure("Field2", "Field2 is invalid")
        //};

        var validationResult = new ValidationResult
        {
            Errors = new List<FluentValidation.Results.ValidationFailure>
            {
                new("Field1", "Field1 is required"),
                new("Field2", "Field2 is invalid")
            }
        };



        var validationException = new ValidationException(validationResult);
        
        _nextMock.Setup(next => next(_context)).ThrowsAsync(validationException);

        // Act
        await _middleware.InvokeAsync(_context);

        // Assert
        foreach (var error in validationResult.Errors)
        {
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<object>(v => v.ToString()!.Contains($"Validation Error: {error}")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<object, Exception?, string>>()),
                Times.Once);
        }
    }

    [Fact]
    public async Task InvokeAsync_WithGeneralException_ShouldLogErrorAndRethrow()
    {
        // Arrange
        var exception = new InvalidOperationException("Test exception");
        _nextMock.Setup(next => next(_context)).ThrowsAsync(exception);
        _context.Request.Path = "/test/path";

        // Act & Assert
        var thrownException = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _middleware.InvokeAsync(_context));

        Assert.Equal(exception, thrownException);

        // Verify exception feature is set
        var exceptionFeature = _context.Features.Get<IExceptionHandlerPathFeature>();
        Assert.NotNull(exceptionFeature);
        Assert.Equal(exception, exceptionFeature.Error);
        Assert.Equal("/test/path", exceptionFeature.Path);

        // Verify logging
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<object>(v => v.ToString()!.Contains("ErrorHandlingMiddleware")),
                It.IsAny<Exception>(),
                It.IsAny<Func<object, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task InvokeAsync_WithExceptionWithInnerException_ShouldLogBothMessages()
    {
        // Arrange
        var innerException = new ArgumentException("Inner exception message");
        var outerException = new InvalidOperationException("Outer exception message", innerException);
        _nextMock.Setup(next => next(_context)).ThrowsAsync(outerException);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _middleware.InvokeAsync(_context));

        // Verify logging includes both exception messages
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<object>(v => v.ToString()!.Contains("Outer exception message") &&
                                   v.ToString()!.Contains("Inner exception message")),
                It.IsAny<Exception>(),
                It.IsAny<Func<object, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task InvokeAsync_WithNullPath_ShouldHandleGracefully()
    {
        // Arrange
        var exception = new InvalidOperationException("Test exception");
        _nextMock.Setup(next => next(_context)).ThrowsAsync(exception);
        _context.Request.Path = new PathString(); // Default/null path

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _middleware.InvokeAsync(_context));

        // Verify exception feature is set with empty path
        var exceptionFeature = _context.Features.Get<IExceptionHandlerPathFeature>();
        Assert.NotNull(exceptionFeature);
        Assert.Equal(exception, exceptionFeature.Error);
        Assert.Empty(exceptionFeature.Path);
    }

    [Theory]
    [InlineData("GET")]
    [InlineData("POST")]
    [InlineData("PUT")]
    [InlineData("DELETE")]
    public async Task InvokeAsync_WithDifferentHttpMethods_ShouldCallNext(string httpMethod)
    {
        // Arrange
        _context.Request.Method = httpMethod;
        _context.Request.Path = "/test";

        // Act
        await _middleware.InvokeAsync(_context);

        // Assert
        _nextMock.Verify(next => next(_context), Times.Once);
    }

    [Fact]
    public async Task InvokeAsync_WithValidationExceptionContainingMultipleErrors_ShouldLogAllErrors()
    {


        var validationResult = new ValidationResult
        {
            Errors = new List<FluentValidation.Results.ValidationFailure>
            {
                new("Name", "Name is required"),
                new("Email", "Email is invalid"),
                new("Age", "Age must be positive"),
                new("PhoneNumber", "Phone number format is incorrect"),
            }
        };

        var validationException = new ValidationException(validationResult);
        
        _nextMock.Setup(next => next(_context)).ThrowsAsync(validationException);

        // Act
        await _middleware.InvokeAsync(_context);

        // Assert
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.IsAny<object>(),
                It.IsAny<Exception>(),
                It.IsAny<Func<object, Exception?, string>>()),
            Times.Exactly(validationResult.Errors.Count));
    }

    [Fact]
    public async Task InvokeAsync_WithEmptyValidationErrors_ShouldNotLogValidationErrors()
    {
        // Arrange

        var validationResult = new ValidationResult
        {
            Errors = new List<FluentValidation.Results.ValidationFailure>()
        };


        var validationException = new ValidationException(validationResult);
        _nextMock.Setup(next => next(_context)).ThrowsAsync(validationException);

        // Act
        await _middleware.InvokeAsync(_context);

        // Assert
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<object>(v => v.ToString()!.Contains("Validation Error:")),
                It.IsAny<Exception>(),
                It.IsAny<Func<object, Exception?, string>>()),
            Times.Never);
    }

    [Fact]
    public async Task InvokeAsync_WithComplexPath_ShouldSetCorrectPathInFeature()
    {
        // Arrange
        var exception = new InvalidOperationException("Test exception");
        _nextMock.Setup(next => next(_context)).ThrowsAsync(exception);
        _context.Request.Path = "/api/v1/users/123/details";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _middleware.InvokeAsync(_context));

        // Verify exception feature path
        var exceptionFeature = _context.Features.Get<IExceptionHandlerPathFeature>();
        Assert.Equal("/api/v1/users/123/details", exceptionFeature!.Path);
    }
}

// Note: The HandleExceptionAsync method is private, so we'll test it indirectly through integration tests
// or by making it internal and using InternalsVisibleTo attribute for more comprehensive testing.

public class ErrorHandlingMiddlewareIntegrationTests
{
    // These tests would require a more complex setup with actual HTTP context and session management
    // They are placeholders for integration testing scenarios

    [Fact]
    public void HandleExceptionAsync_WithValidationException_ShouldReturnBadRequest()
    {
        // This would be implemented as an integration test
        // Testing the private HandleExceptionAsync method indirectly
        Assert.True(true); // Placeholder
    }

    [Fact]
    public void HandleExceptionAsync_WithBadRequestException_ShouldReturnBadRequest()
    {
        // This would be implemented as an integration test
        Assert.True(true); // Placeholder
    }

    [Fact]
    public void HandleExceptionAsync_WithNotFoundException_ShouldReturnNotFound()
    {
        // This would be implemented as an integration test
        Assert.True(true); // Placeholder
    }

    [Fact]
    public void HandleExceptionAsync_WithAuthenticationException_ShouldReturnUnauthorized()
    {
        // This would be implemented as an integration test
        Assert.True(true); // Placeholder
    }

    [Fact]
    public void HandleExceptionAsync_WithTokenExpiredException_ShouldReturnUnauthorized()
    {
        // This would be implemented as an integration test
        Assert.True(true); // Placeholder
    }
}
