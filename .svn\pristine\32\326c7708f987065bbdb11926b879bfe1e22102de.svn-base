﻿using ContinuityPatrol.Application.Features.Workflow.Events.SaveAs;

namespace ContinuityPatrol.Application.UnitTests.Features.Workflow.Events
{
    public class WorkflowSaveAsEventTests
    {
        private readonly Mock<ILogger<WorkflowSaveAsEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly WorkflowSaveAsEventHandler _handler;

        public WorkflowSaveAsEventTests()
        {
            _mockLogger = new Mock<ILogger<WorkflowSaveAsEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _handler = new WorkflowSaveAsEventHandler(
                _mockLogger.Object,
                _mockUserActivityRepository.Object,
                _mockUserService.Object
            );
        }

        [Fact]
        public async Task Handle_Should_Create_UserActivity_When_WorkflowSaveAsEvent_Is_Handled()
        {
            var eventData = new WorkflowSaveAsEvent
            {
                WorkflowName = "TestWorkflow"
            };

            _mockUserService.Setup(s => s.UserId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(s => s.LoginName).Returns("TestUser");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://testurl.com");
            _mockUserService.Setup(s => s.CompanyId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            await _handler.Handle(eventData, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == _mockUserService.Object.UserId &&
                activity.LoginName == _mockUserService.Object.LoginName &&
                activity.RequestUrl == _mockUserService.Object.RequestedUrl &&
                activity.CompanyId == _mockUserService.Object.CompanyId &&
                activity.HostAddress == _mockUserService.Object.IpAddress &&
                activity.Action == "SaveAs Workflow" &&
                activity.Entity == "Workflow" &&
                activity.ActivityType == "SaveAs" &&
                activity.ActivityDetails == "Workflow 'TestWorkflow' save-as successfully."
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation(It.Is<string>(s => s.Contains("save-as successfully"))), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Log_Information_When_Workflow_SaveAsEvent_Is_Handled()
        {
            var eventData = new WorkflowSaveAsEvent
            {
                WorkflowName = "TestWorkflow"
            };

            _mockUserService.Setup(s => s.UserId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(s => s.LoginName).Returns("TestUser");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://testurl.com");
            _mockUserService.Setup(s => s.CompanyId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            await _handler.Handle(eventData, CancellationToken.None);

            _mockLogger.Verify(logger => logger.LogInformation(It.Is<string>(s => s.Contains("save-as successfully"))), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Not_Create_UserActivity_When_Service_Properties_Are_Null()
        {
            var eventData = new WorkflowSaveAsEvent
            {
                WorkflowName = "TestWorkflow"
            };

            _mockUserService.Setup(s => s.UserId).Returns<string>(null);
            _mockUserService.Setup(s => s.LoginName).Returns<string>(null);

            await _handler.Handle(eventData, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Never);
            _mockLogger.Verify(logger => logger.LogInformation(It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task Handle_Should_Call_AddAsync_Once_For_Valid_WorkflowSaveAsEvent()
        {
            var eventData = new WorkflowSaveAsEvent
            {
                WorkflowName = "TestWorkflow"
            };

            _mockUserService.Setup(s => s.UserId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(s => s.LoginName).Returns("TestUser");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://testurl.com");
            _mockUserService.Setup(s => s.CompanyId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            await _handler.Handle(eventData, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        }
    }
}
