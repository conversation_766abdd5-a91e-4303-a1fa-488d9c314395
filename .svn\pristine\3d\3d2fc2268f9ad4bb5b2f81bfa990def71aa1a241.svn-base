﻿using ContinuityPatrol.Shared.Core.Exceptions;
using Newtonsoft.Json;
using System.Net;

namespace ContinuityPatrol.Web.Middlewares;

public class AntiXssMiddleware
{
    private readonly RequestDelegate _next;
   // private ErrorResponse _error;
    private const int StatusCode = (int)HttpStatusCode.BadRequest;

    public AntiXssMiddleware(RequestDelegate next)
    {
        _next = next ?? throw new ArgumentNullException(nameof(next));
    }
    public async Task Invoke(HttpContext context)
    {
        try
        {
            // Check XSS in URL
            if (IsDangerous(context.Request.Path.Value))
            { 
                throw new AntiXssException("A Potentially dangerous request was detected.", 5005);
            }

            // Check XSS in query string
            if (IsDangerous(WebUtility.UrlDecode(context.Request.QueryString.Value)))
            {
                throw new AntiXssException("A Potentially dangerous request was detected.", 5005);
            }

            // Check XSS in request content
            var originalBody = context.Request.Body;
            var content = await ReadRequestBody(context);

            if (ContainsScript(content))
            {
                throw new AntiXssException("A Potentially dangerous request was detected.", 5005);
            }

            await _next(context);
        }
        catch (AntiXssException)
        {
            await RespondWithError(context);
        }
    }

    private static bool IsDangerous(string input)
    {
        return !string.IsNullOrWhiteSpace(input) && CrossSiteScriptingValidation.IsDangerousString(input, out _);
    }

    private async Task RespondWithError(HttpContext context)
    {

        context.Response.Clear();
        context.Response.ContentType = "application/json; charset=utf-8";
        context.Response.StatusCode = StatusCode;

        var errorResponse = new ErrorResponse
        {
            ErrorCode = 5005,
            Description = "Error from AntiXssMiddleware: A Potentially dangerous request was detected."
        };

        await context.Response.WriteAsync(JsonConvert.SerializeObject(errorResponse));
    }
    
    private static async Task<string> ReadRequestBody(HttpContext context)
    {
        var buffer = new MemoryStream();
        await context.Request.Body.CopyToAsync(buffer);
        context.Request.Body = buffer;
        buffer.Position = 0;

        var encoding = Encoding.UTF8;

        var requestContent = await new StreamReader(buffer, encoding).ReadToEndAsync();
        context.Request.Body.Position = 0;

        return requestContent;
    }

    private static bool ContainsScript(string input)
    {
        string scriptPattern = @"<script\b[^>]*>(.*?)<\/script>";
        return Regex.IsMatch(input, scriptPattern, RegexOptions.IgnoreCase);
    }

}

public static class AntiXssMiddlewareExtension
{
    public static IApplicationBuilder UseAntiXssMiddleware(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<AntiXssMiddleware>();
    }
}


/// <summary>
/// Imported from System.Web.CrossSiteScriptingValidation Class
/// </summary>
public static class CrossSiteScriptingValidation
{
    private static readonly char[] StartingChars = { '<', '&' };

    #region Public methods

    public static bool IsDangerousString(string s, out int matchIndex)
    {
        //bool inComment = false;
        matchIndex = 0;

        for (var i = 0; ;)
        {

            // Look for the start of one of our patterns 
            var n = s.IndexOfAny(StartingChars, i);

            // If not found, the string is safe
            if (n < 0) return false;

            // If it's the last char, it's safe 
            if (n == s.Length - 1) return false;

            matchIndex = n;

            switch (s[n])
            {
                case '<':
                    // If the < is followed by a letter or '!', it's unsafe (looks like a tag or HTML comment)
                    if (IsAtoZ(s[n + 1]) || s[n + 1] == '!' || s[n + 1] == '/' || s[n + 1] == '?') return true;
                    break;
                case '&':
                    // If the & is followed by a #, it's unsafe (e.g. S) 
                    if (s[n + 1] == '#') return true;
                    break;

            }

            // Continue searching
            i = n + 1;
        }
    }

    #endregion

    #region Private methods

    private static bool IsAtoZ(char c)
    {
        return (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z');
    }

    #endregion

    //public static void AddHeaders(this IHeaderDictionary headers)
    //{
    //    if (!headers["P3P"].Any())
    //    {
    //        headers.Add("P3P", "CP=\"IDC DSP COR ADM DEVi TAIi PSA PSD IVAi IVDi CONi HIS OUR IND CNT\"");
    //    }
    //}
    //public static string ToJSON(this object value)
    //{
    //    return JsonConvert.SerializeObject(value);
    //}
}

public class ErrorResponse
{
    public int ErrorCode { get; set; }
    public string Description { get; set; }
}
