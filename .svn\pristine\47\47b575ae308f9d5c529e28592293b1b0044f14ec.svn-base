﻿using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Application.Features.DriftJob.Commands.UpdateState;

public class UpdateDriftJobStateCommand:IRequest<BaseResponse>
{
    public string State { get; set; }
    public string Reason { get; set; }
    public List<UpdateDriftJobState> UpdateDriftJobState { get; set; }

}
public class UpdateDriftJobState
{ 
    public string Id { get; set; }
    public string Name { get; set; }
    
}
