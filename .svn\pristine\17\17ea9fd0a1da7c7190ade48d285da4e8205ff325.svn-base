﻿using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceHealthStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class BusinessServiceHealthStatusProfile : Profile
{
    public BusinessServiceHealthStatusProfile()
    {
        CreateMap<BusinessServiceHealthStatus, CreateBusinessServiceHealthStatusCommand>().ReverseMap();
        CreateMap<UpdateBusinessServiceHealthStatusCommand, BusinessServiceHealthStatus>()
            .ForMember(x => x.Id, y => y.Ignore());

        CreateMap<BusinessServiceHealthStatus, BusinessServiceHealthStatusListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<BusinessServiceHealthStatus, BusinessServiceHealthStatusDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<BusinessServiceHealthStatus, BusinessServiceHealthStatusListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<PaginatedResult<BusinessServiceHealthStatus>, PaginatedResult<BusinessServiceHealthStatusListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}