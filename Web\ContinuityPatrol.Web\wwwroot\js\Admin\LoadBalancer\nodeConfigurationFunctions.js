﻿ function handleCheckboxChange(selector) {
        const isChecked = $(selector).prop('checked');
        const value = isChecked ? true : false;
        $(selector).val(value);
    }

async function check_port(ip, port) {
    const portExists = ip_port_data?.some(x => x.ipAddress.trim() == ip && x.port == port);

    if ($("#save").text() == "Save" && port != "") {
        if (portExists) {
            setTimeout(() => {
                $("#loadBalPortError")
                    .text("Port number already exists with IP address")
                    .addClass("field-validation-error");
            }, 100)
            return false;
        } else {
            $("#loadBalPortError").text("").removeClass("field-validation-error");
            return true;
        }
    }

}

async function validateportExists(port, ip, id, url) {
    const errorElement = $("#loadBalPortError");

    let url_name = RootUrl + url;
    let data = {};
    data.port = port;
    data.ipAddress = ip
    data.id = id
    const validationResults = [

        await IsPortExist(url_name, data, errorElement)

    ];
    return await CommonValidation(errorElement, validationResults);
}

async function IsPortExist(url, data, errorFunc) {
    return !data.port.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Port already exists" : true;
}

async function validateName(value, id = null, url) {
    const errorElement = $('#loadBalNameError');
    if (!value) {
        errorElement.text('Enter name')
            .addClass('field-validation-error');
        return false;
    }
    if (value.includes("<")) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    let url_name = RootUrl + url;
    let data = {};
    data.loadBalancerName = value;
    data.id = id;
    const validationResults = [
        await SpecialCharValidateCustom(value), //SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await ShouldNotBeginWithNumber(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsNameExist(url_name, data, OnError)
    ];
    return await CommonValidation(errorElement, validationResults);
};

async function validateIpAddress(value) {
    const errorElement = $('#loadBalIPAddressError');
    if (!value) {
        errorElement.text('Enter IP address')
            .addClass('field-validation-error');
        return false;
    }
    const validationResults = [await IpaddressReg(value)];
    return await CommonValidation(errorElement, validationResults);
};

async function validateHostName(value) {
    const errorElement = $('#loadBalHostNameError');
    if (!value) {
        errorElement.text('Enter host name')
            .addClass('field-validation-error');
        return false;
    }

    const validationResults = [
        await HostNameReg(value),
        await ShouldNotBeginWithUnderScore(value),
        await HostNameBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await ShouldNotBeginWithNumber(value),
        await minMaxlength(value, 30),
        await secondChar(value)
    ];

    return await CommonValidation(errorElement, validationResults);
};

const HostNameBeginWithSpace = (value) => {
    const regex = /^(?!\.)(?![\s-])[\w\s.-]*\w[\w\s.-]*(?<!\.)$/;
    return regex.test(value) ? true : "Should not begin with space";
}

async function validatePort(value) {

    const errorElement = $('#loadBalPortError');
    if (!value) {
        errorElement.text('Enter port')
            .addClass('field-validation-error');
        return false;
    }
    const validationResults = [
        await OnlyNum(value),
        await PortReg(value)

    ];

    return await CommonValidation(errorElement, validationResults);
};
function setRadioInput(name, value) {
    if (value)
        $(`input[name="${name}"][value="${value}"]`).prop("checked", true);
}
function handleTypeCategory(typeCategory) {
    const isLoadBalancer = typeCategory?.toLowerCase() === "loadbalancer";
    $('#typeAll').toggle(isLoadBalancer);
    toggleDisableEnable($('#drIcon'), isLoadBalancer);
    toggleDisableEnable($('#prIcon'), !isLoadBalancer);
    $('#node-http').toggleClass('d-none', isLoadBalancer);
}
function triggerServiceChange(type) {
    const serviceMap = {
        monitorservice: "#monitorService",
        workflowservice: "#workflowService",
        resiliencyreadyservice: "#bothService",
        all: "#all"
    };
    const selector = serviceMap[type?.toLowerCase()];
    if (selector) $(selector).trigger("change");
}
function clearValidationErrors(selectors) {
    selectors.forEach(selector => {
        $(selector).text('').removeClass('field-validation-error');
    });
}
function populateModalFields(loadBalancerData) {

    $('#nameLB').val(loadBalancerData?.name);
    $('#IpAddressLB').val(loadBalancerData?.ipAddress);
    $('#hostNameLB').val(loadBalancerData?.hostName);
    $('#portLB').val(loadBalancerData?.port);
    $('#loadBalancerId').val(loadBalancerData?.id);
    $('#status').val(loadBalancerData?.status);
    $('#chkLBTestConnection').prop("checked", !!loadBalancerData?.isConnection);

    globalTypeCategory = loadBalancerData?.typeCategory;
    setRadioInput("typecategory", globalTypeCategory);

    $('#prIcon, #drIcon').prop('disabled', false);
    handleTypeCategory(globalTypeCategory);

    globalType = loadBalancerData?.type;
    setRadioInput("type", globalType);

    const isLoadBalancer = globalTypeCategory.toLowerCase() === "loadbalancer";
    $('input[name="type"][value="All"]').prop('checked', isLoadBalancer);
    setRadioInput("connectionType", loadBalancerData?.connectionType);
    triggerServiceChange(loadBalancerData?.type);
    serviceTypeToggle();
    clearValidationErrors(['#loadBalNameError', '#loadBalIPAddressError', '#loadBalHostNameError', '#loadBalPortError']);
}

async function IsNameExist(url, data, errorFunc) {
    return !data.loadBalancerName.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}
function toggleDisableEnable(element, condition) {
    element.prop('disabled', condition);
}
function typeCategoryBased() {
    const selectedCategory = $('input[name="typecategory"]:checked').val();
    const isSaveMode = $('#save').text() === 'Save';

    const normalizeType = type => type?.replace(/\s+/g, '').toLowerCase();
    const filterType = type => globalAllLoadBalancer.filter(item =>
        item?.typeCategory?.toLowerCase() === "loadbalancer" &&
        normalizeType(item?.type) === normalizeType(type)
    );

    const moniterType = filterType("MonitorService");
    const workflowType = filterType("WorkflowService");
    const drReadyType = filterType("ResiliencyReadyService");

    if (selectedCategory === "Load Balancer") {
        $('#node-http').addClass('d-none');
        $('input[name="connectionType"][value="https"]').prop("checked", true);
        $('#typeAll').show();
        toggleDisableEnable($('#all'), false);

        $('input[name="type"][value="MonitorService"]').prop("checked", false);
        $('#prIcon').prop('checked', true);

        serviceTypeToggle(moniterType, workflowType, drReadyType);

        if (!isSaveMode) {
            if (moniterType?.length) toggleServiceType("monitorservice");
            if (workflowType?.length) toggleServiceType("workflowservice");;
            if (drReadyType?.length) toggleServiceType("resiliencyreadyservice");;
            const typeLower = globalType?.toLowerCase();

            if (typeLower === 'all') {
                $('#monitorService, #workflowService, #bothService').prop('disabled', true);
            }

            if (typeLower === 'monitorservice') {
                toggleServiceType("monitorservice");
                toggleDisableEnable($('#monitorService'), false);
            }

            if (typeLower === 'workflowservice') {
                toggleServiceType("workflowservice");;
                toggleDisableEnable($('#workflowService'), false);
            }

            if (typeLower === 'resiliencyreadyservice') {
                toggleServiceType("resiliencyreadyservice");
                toggleDisableEnable($('#bothService'), false);
            }

            $('input[name="type"][value="' + globalType + '"]').prop("checked", true);
        }

    } else {
    
        $('#drIcon').prop('checked', true);
        $('#typeAll').hide();
        $('#node-http').removeClass('d-none');
        $('input[name="connectionType"][value="http"]').prop("checked", true);
        $('input[name="type"][value="MonitorService"]').prop("checked", true);
        $('input[name="type"][value="All"]').prop("checked", false);
        toggleDisableEnable($('#prIcon'), false);
        toggleDisableEnable($('#monitorService'), false);
        toggleDisableEnable($('#workflowService'), false);
        toggleDisableEnable($('#bothService'), false);
    }
}
function serviceTypeToggle(moniterType, workflowType, drReadyType, isAllTypesPresent = false) {
    const mode = $('#save').text();
    const normalizedGlobalType = globalType?.toLowerCase();
    const normalizedGlobalTypeCategory = globalTypeCategory?.toLowerCase();

    const toggleByType = {
        monitorservice: () => {
            toggleServiceType("monitorservice");
            toggleDisableEnable($('#monitorService'), false);
        },
        workflowservice: () => {
            toggleServiceType("workflowservice");;
            toggleDisableEnable($('#workflowService'), false);
        },
        resiliencyreadyservice: () => {
            toggleServiceType("resiliencyreadyservice");;
            toggleDisableEnable($('#bothService'), false);
        },
        all: () => {
            toggleDisableEnable($('#prIcon'), false);
            toggleDisableEnable($('#all'), false);
            toggleDisableEnable($('#monitorService'), true);
            toggleDisableEnable($('#workflowService'), true);
            toggleDisableEnable($('#bothService'), true);
        }
    };

    if (mode === 'Save') {
        if (!isAllTypesPresent) {
            if (moniterType?.length) toggleServiceType("monitorservice");
            if (workflowType?.length) toggleServiceType("workflowservice");
            if (drReadyType?.length) toggleServiceType("resiliencyreadyservice");

            const enabled = findEnabledTypeCheckboxes();
            if (enabled.length > 0) {
                $('input[name="type"][value="' + enabled[0] + '"]').prop("checked", true);
            }
        }
        return;
    }
    $('input[name="type"][value="' + globalType + '"]').prop("checked", true);

    if (normalizedGlobalTypeCategory === 'cpnode') {
        const allItems = globalAllLoadBalancer.filter(item =>
            item?.typeCategory?.toLowerCase() === "loadbalancer" &&
            item?.type === "All"
        );
        if (allItems?.length > 0) {
            toggleDisableEnable($('#prIcon'), true);
            $('#typeAll').hide();
        }
        return;
    }
    const types = {
        monitorservice: globalAllLoadBalancer.filter(item =>
            item?.typeCategory?.toLowerCase() === "loadbalancer" &&
            item?.type?.toLowerCase() === "monitorservice"
        ),
        workflowservice: globalAllLoadBalancer.filter(item =>
            item?.typeCategory?.toLowerCase() === "loadbalancer" &&
            item?.type?.toLowerCase() === "workflowservice"
        ),
        resiliencyreadyservice: globalAllLoadBalancer.filter(item =>
            item?.typeCategory?.toLowerCase() === "loadbalancer" &&
            item?.type?.toLowerCase() === "resiliencyreadyservice"
        )
    };

    if (types.monitorservice.length) toggleServiceType("monitorservice");
    if (types.workflowservice.length) toggleServiceType("workflowservice");
    if (types.resiliencyreadyservice.length) toggleServiceType("resiliencyreadyservice");

    if (toggleByType[normalizedGlobalType]) {
        toggleByType[normalizedGlobalType]();
    }
}
function toggleServiceType(typeKey) {
    const typeMap = {
        monitorservice: '#monitorService',
        workflowservice: '#workflowService',
        resiliencyreadyservice: '#bothService'
    };

    const selector = typeMap[typeKey.toLowerCase()];
    if (!selector) return;

    $('#typeAll').show();
    toggleDisableEnable($('#prIcon'), false);
    toggleDisableEnable($('#all'), true);
    toggleDisableEnable($(selector), true);

    $('input[name="type"][value="All"]').prop("checked", false);
    $('#prIcon').prop('checked', true);
    $('#drIcon').prop('checked', false);
}
function findEnabledTypeCheckboxes() {
    let enabledCheckboxes = [];
    $('input[name="type"]').each(function () {

        if (!$(this).prop('disabled')) {
            enabledCheckboxes.push($(this).val());
        }
    });
    return enabledCheckboxes;
}
