﻿using ContinuityPatrol.Application.Features.Node.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.NodeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.Node.Queries;

public class GetNodePaginatedListQueryHandlerTests : IClassFixture<NodeFixture>
{
    private readonly NodeFixture _nodeFixture;

    private readonly Mock<INodeRepository> _mockNodeRepository;

    private readonly GetNodePaginatedListQueryHandler _handler;

    public GetNodePaginatedListQueryHandlerTests(NodeFixture nodeFixture)
    {
        _nodeFixture = nodeFixture;

        _mockNodeRepository = NodeRepositoryMocks.GetPaginatedNodeRepository(_nodeFixture.Nodes);

        _handler = new GetNodePaginatedListQueryHandler(_nodeFixture.Mapper, _mockNodeRepository.Object);

        _nodeFixture.Nodes[0].ServerName = "DRServer";
        _nodeFixture.Nodes[0].Name = "cpadmin";
        _nodeFixture.Nodes[0].ServerId = "382f12cd-3985-46cc-a1ea-a3420d87b80a";
        _nodeFixture.Nodes[0].Type = "Pratheesh";
        _nodeFixture.Nodes[0].Properties = "name;test";

        _nodeFixture.Nodes[1].ServerName = "DRServer";
        _nodeFixture.Nodes[1].Name = "cpadmin";
        _nodeFixture.Nodes[1].ServerId = "382f12cd-3985-46cc-a1ea-a3420d87b80a";
        _nodeFixture.Nodes[1].Type = "Pratheesh";
        _nodeFixture.Nodes[1].Properties = "name;test";

    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryString_NotMatch()
    {
        var result = await _handler.Handle(new GetNodePaginatedListQuery { PageNumber = 0, PageSize = 0, SearchString = "cpadmin" }, CancellationToken.None);

        result.TotalCount.ShouldBe(2);
    }

    [Fact]
    public async Task Handle_Return_PaginatedNode_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetNodePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "cpadmin" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<NodeListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBe(_nodeFixture.Nodes[0].ReferenceId);
        result.Data[0].Name.ShouldBe("cpadmin");
        result.Data[0].ServerName.ShouldBe(_nodeFixture.Nodes[0].ServerName);
        result.Data[0].ServerId.ShouldBe(_nodeFixture.Nodes[0].ServerId);
        result.Data[0].Type.ShouldBe(_nodeFixture.Nodes[0].Type);
        result.Data[0].Properties.ShouldBe(_nodeFixture.Nodes[0].Properties);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetNodePaginatedListQuery { PageNumber = 2, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<NodeListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_Node_With_MultipleQuery_StringParameter()
    {
        var result = await _handler.Handle(new GetNodePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=cpadmin;servername=DRServer;type=Pratheesh;properties=name;test" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<NodeListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Name.ShouldBe(_nodeFixture.Nodes[0].Name);
        result.Data[0].Id.ShouldBe(_nodeFixture.Nodes[0].ReferenceId);
        result.Data[0].ServerName.ShouldBe(_nodeFixture.Nodes[0].ServerName);
        result.Data[0].ServerId.ShouldBe(_nodeFixture.Nodes[0].ServerId);
        result.Data[0].Type.ShouldBe(_nodeFixture.Nodes[0].Type);
        result.Data[0].Properties.ShouldBe(_nodeFixture.Nodes[0].Properties);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetNodePaginatedListQuery(), CancellationToken.None);

        _mockNodeRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
    }
}