using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AdPasswordJobModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IAdPasswordJobService
{
    Task<List<AdPasswordJobListVm>> GetAdPasswordJobList();
    Task<BaseResponse> CreateAsync(CreateAdPasswordJobCommand createAdPasswordJobCommand);
    Task<BaseResponse> UpdateAsync(UpdateAdPasswordJobCommand updateAdPasswordJobCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<AdPasswordJobDetailVm> GetByReferenceId(string id);
    #region NameExist
 Task<bool> IsAdPasswordJobNameExist(string name, string id);
   #endregion
    #region Paginated
 Task<PaginatedResult<AdPasswordJobListVm>> GetPaginatedAdPasswordJobs(GetAdPasswordJobPaginatedListQuery query);
    #endregion
}
