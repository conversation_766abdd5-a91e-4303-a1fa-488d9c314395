﻿
let mId = sessionStorage.getItem("monitorId");
let monitortype = 'SybaseRSHADR';
let infraObjectId = sessionStorage.getItem("infraobjectId");
setTimeout(() => { sybaseWithRSHADRmonitorstatus(mId, monitortype) }, 500)

setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})

async function sybaseWithRSHADRmonitorstatus(id, type) {
    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
}
let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'
function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}
function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}
function propertiesData(value) {
    if (value === undefined || value === null || value === '') {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
    else {
        let data = JSON?.parse(value?.properties)
        console.log(data, 'sybase')
        let customSite = data?.SybaseWithHADRMonitoring?.length > 1;
        if (customSite) {
            $("#Sitediv").show();
        } else {
            $("#Sitediv").hide();
        }

        $(".siteContainer").empty();
        data?.SybaseWithHADRMonitoring?.forEach((a, index) => {
            let selectTab = `
                <li class="nav-item siteListChange" id='siteName${index}'>
                    <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
                </li>
                <li class="nav-item vr"></li>`;
            $(".siteContainer").append(selectTab);
        });
        if (data?.SybaseWithHADRMonitoring?.length > 0) {

            $("#siteName0 .nav-link").addClass("active");
            displaySiteData(data?.SybaseWithHADRMonitoring[0]);
        }
        let defaultSite = data?.SybaseWithHADRMonitoring?.find(d => d?.Type === 'DR') || data?.SybaseWithHADRMonitoring[0];
        if (defaultSite) {

            displaySiteData(defaultSite);
        }
        $(document).on('click', '.siteListChange', function () {

            $(".siteListChange .nav-link").removeClass("active");
            $(this).find(".nav-link").addClass("active");
            let siteId = $(this)[0]?.id
            let getSiteName = $(`#${siteId} .siteName`).text()
            $('#customServer').html(getSiteName)

            let MonitoringModel = data?.SybaseWithHADRMonitoring?.find(d => d?.Type === getSiteName);
            if (MonitoringModel) {

                displaySiteData(MonitoringModel);
            }
        });

        function displaySiteData(siteData) {
            
            let obj = {};
            for (let key in siteData?.ASEServerMonitoring) {
                obj[`DR_` + key] = siteData?.ASEServerMonitoring[key];
            }
            let MonitoringModelActiveDirectory = [
                "DR_DataServer", "DR_BackupServer", "DR_DatabaseName",
                "DR_DatabaseStatus", "DR_DatabaseLoggingStatus", "DR_DataDeviceDetails", "DR_Dataspaceused",
                "DR_LoggingDeviceDetails", "DR_LogSpaceUsed", "DR_ASEVersion",
            ];
            if (Object.keys(obj)?.length > 0) {
                bindProperties(obj, MonitoringModelActiveDirectory, value);
            }

            //For seperate Line
            if (obj?.DR_DataDeviceDetails?.includes(';')) {
                let list = obj?.DR_DataDeviceDetails?.split(';').filter(item => item.trim() !== '')
                let listHtml = list.map((item, index) => {
                    let icon = index === 0 ? getIconClass(item.trim(), 'DR_DataDeviceDetails', obj, item.trim()) : '';
                    return `<li class="list-group-item text-truncate ps-0" title="${item.trim()}">${icon}${item.trim()}</li>`;
                }).join('');
                $("#DR_DataDeviceDetails").empty().append(listHtml);
            }
            if (obj?.DR_LoggingDeviceDetails?.includes(';')) {
                let list = obj?.DR_LoggingDeviceDetails?.split(';').filter(item => item.trim() !== '')
                let listHtml = list.map((item, index) => {
                    let icon = index === 0 ? getIconClass(item.trim(), 'DR_LoggingDeviceDetails', obj, item.trim()) : '';
                    return `<li class="list-group-item text-truncate ps-0" title="${item.trim()}">${icon}${item.trim()}</li>`;
                }).join('');
                $("#DR_LoggingDeviceDetails").empty().append(listHtml);
            }

            //DR Replication Server Monitoring
            let objdb = {};
            for (let key in siteData?.ReplicationServerMonitoring) {
                objdb['DR_' + key] = siteData?.ReplicationServerMonitoring[key];
            }

            let DatabaseModelProp = [
                "DR_DistributionMode", "DR_HADRMode", "DR_HADRState", "DR_LogicalHostname",
                "DR_RMAHost", "DR_ServerIpAddress", "DR_ServerName", "DR_ServerStatus",
                "DR_ServerVersion", "DR_SynchronizationMode", "DR_SynchronizationState", "DR_ServerIpAddress"
            ];
            if (Object.keys(objdb).length > 0) {
                bindProperties(objdb, DatabaseModelProp, value);
            }

            //DR Replication Details
            let objRep = {};
            for (let key in siteData?.ReplicationServerMonitoring?.ReplicationDBDetails) {
                objRep['DR_' + key] = siteData?.ReplicationServerMonitoring?.ReplicationDBDetails[key];
            }

            let ReplicaProp = [
                "DR_CommitTime", "DR_Latency", "DR_Path", "DR_State",
            ];
            if (Object.keys(objdb).length > 0) {
                bindProperties(objRep, ReplicaProp, value);
            }

            //Master DBReplication Details
            let objdbRep = {};
            for (let key in siteData?.ReplicationServerMonitoring?.MasterDBReplicationDetails) {
                objdbRep['DR_MasterDB_' + key] = siteData?.ReplicationServerMonitoring?.MasterDBReplicationDetails[key];
            }

            let DBReplicaProp = [
                "DR_MasterDB_CommitTime", "DR_MasterDB_Latency", "DR_MasterDB_Path", "DR_MasterDB_State",
            ];
            if (Object.keys(objdb).length > 0) {
                bindProperties(objdbRep, DBReplicaProp, value);
            }
        }
        let dbDetail = data?.PrSybaseWithHADRMonitoring?.ASEServerMonitoring
        const dbDetailsProp = [
            "DataServer", "BackupServer", "DatabaseName",
            "DatabaseStatus", "DatabaseLoggingStatus", "DataDeviceDetails", "Dataspaceused",
            "LoggingDeviceDetails", "LogSpaceUsed", "ASEVersion",
        ];       
        bindProperties(dbDetail, dbDetailsProp, value);

        //For seperate Line
        if (dbDetail?.DataDeviceDetails?.includes(';')) {
            let list = dbDetail?.DataDeviceDetails?.split(';').filter(item => item.trim() !== '')
            let listHtml = list.map((item, index) => {
                let icon = index === 0 ? getIconClass(item.trim(), 'DataDeviceDetails', dbDetail, item.trim()) : '';
                return `<li class="list-group-item text-truncate ps-0" title="${item.trim()}">${icon}${item.trim()}</li>`;
            }).join('');
            $("#DataDeviceDetails").empty().append(listHtml);
        }
        if (dbDetail?.LoggingDeviceDetails?.includes(';')) {
            let list = dbDetail?.LoggingDeviceDetails?.split(';').filter(item => item.trim() !== '')
            let listHtml = list.map((item,index)=> {
                let icon = index === 0 ? getIconClass(item.trim(), 'LoggingDeviceDetails', dbDetail, item.trim()) : '' ;
                return `<li class="list-group-item text-truncate ps-0" title="${item.trim()}">${icon}${item.trim()}</li>`;
            }).join('');
            //let listHtml = list.map(item => `<li class="list-group-item p-1 text-truncate"
            //title="${item.trim()}">${icon}${item.trim()}</li>`).join('');
            $("#LoggingDeviceDetails").empty().append(listHtml);
        }

        //Replication Server Monitoring
        let replicaServer = data?.PrSybaseWithHADRMonitoring?.ReplicationServerMonitoring
        const repserverProp = [
            "DistributionMode", "HADRMode", "HADRState", "LogicalHostname",
            "RMAHost", "ServerIpAddress", "ServerName", "ServerStatus", "ServerIpAddress",
            "ServerVersion", "SynchronizationMode", "SynchronizationState",
        ]
        if (replicaServer !== '' && replicaServer !== null && replicaServer !== undefined) {
            bindProperties(replicaServer, repserverProp, value);
        }

        //Replication Details
        let replicaDetails = data?.PrSybaseWithHADRMonitoring?.ReplicationServerMonitoring?.ReplicationDBDetails
        const repDetailProps = [
            "CommitTime", "Latency", "Path", "State",
        ]
        if (replicaDetails !== '' && replicaDetails !== null && replicaDetails !== undefined) {
            bindProperties(replicaDetails, repDetailProps, value);
        }

        //Master DBReplication Details
        let MasterDBDetails = data?.PrSybaseWithHADRMonitoring?.ReplicationServerMonitoring?.MasterDBReplicationDetails
        
        const MasterDBDetailProps = [
            "CommitTime", "Latency", "Path", "State",
        ]
        if (MasterDBDetails) {
            let formattedMasterDBDetails = {};
            for (let key in MasterDBDetails) {
                formattedMasterDBDetails[`MasterDB_${key}`] = MasterDBDetails[key];
            }
            bindProperties(formattedMasterDBDetails, MasterDBDetailProps.map(k => `MasterDB_${k}`), value);
        }
       
    }
}

function bindProperties(data, properties, value) {

    properties?.forEach(property => {
        const values = data[property];
        const displayedValue = value !== undefined ? checkAndReplace(values) : 'NA';
        // Displayed value with icon
        const iconHtml = getIconClass(displayedValue, property, data, value);        
        const mergeValue = `${iconHtml}${displayedValue}`;
        $(`#${property}`).html(mergeValue).attr('title', displayedValue);
    });

}
function getIconClass(displayedValue, property, data, value) {
    let prServer = data?.DataServer ? 'text-primary cp-db-server ' : "text-danger cp-disable"
    let drServer = data?.DR_DataServer ? 'text-primary cp-db-server ' : "text-danger cp-disable"
    let prBackup = data?.BackupServer ? 'text-primary cp-synbase-backup-server ' : "text-danger cp-disable"
    let drBackup = data?.DR_BackupServer ? 'text-primary cp-synbase-backup-server ' : "text-danger cp-disable"
    let prDatabase = data?.DatabaseName ? 'text-primary cp-database ' : "text-danger cp-disable"
    let drDatabase = data?.DR_DatabaseName ? 'text-primary cp-database ' : "text-danger cp-disable"
    let prDatabaseStatus = data?.DatabaseStatus ? 'text-success cp-success ' : "text-danger cp-disable"
    let drDatabaseStatus = data?.DR_DatabaseStatus ? 'text-success cp-success ' : "text-danger cp-disable"
    let prLogStatus = data?.DatabaseLoggingStatus ? 'text-success cp-success ' : "text-danger cp-disable"
    let drLogStatus = data?.DR_DatabaseLoggingStatus ? 'text-success cp-success ' : "text-danger cp-disable"
    let prVersion = data?.ASEVersion ? 'text-primary cp-file-edits ' : "text-danger cp-disable"
    let drVersion = data?.DR_ASEVersion ? 'text-primary cp-file-edits ' : "text-danger cp-disable"
    let prDevice = data?.DataDeviceDetails ? 'text-primary cp-report ' : "text-danger cp-disable"
    let drDevice = data?.DR_DataDeviceDetails ? 'text-primary cp-report ' : "text-danger cp-disable"
    let prLog = data?.LogSpaceUsed ? 'text-primary cp-file-edits ' : "text-danger cp-disable"
    let drLog = data?.DR_LogSpaceUsed ? 'text-primary cp-file-edits ' : "text-danger cp-disable"
    let prIP = data?.ServerIpAddress ? 'text-primary cp-ip-address ' : "text-danger cp-disable"
    let drIP = data?.DR_ServerIpAddress ? 'text-primary cp-ip-address' : "text-danger cp-disable"
    let prSeverName = data?.ServerName ? 'text-primary cp-server ' : "text-danger cp-disable"
    let drSeverName = data?.DR_ServerName ? 'text-primary cp-server ' : "text-danger cp-disable"
    let prTime = data?.MasterDB_CommitTime ? 'text-success cp-estimated-time' : "text-danger cp-disable"
    let drTime = data?.DR_MasterDB_CommitTime ? 'text-success cp-estimated-time' : "text-danger cp-disable"
    let prComTime = data?.CommitTime ? 'text-success cp-estimated-time ' : "text-danger cp-disable"
    let drComTime = data?.DR_CommitTime ? 'text-success cp-estimated-time ' : "text-danger cp-disable"
    let prMode = data?.DistributionMode ? 'text-primary cp-remote-login ' : "text-danger cp-disable"
    let drMode = data?.DR_DistributionMode ? 'text-primary cp-remote-login ' : "text-danger cp-disable"
    let prHost = data?.RMAHost ? 'text-primary cp-host-name ' : "text-danger cp-disable"
    let drHost = data?.DR_RMAHost ? 'text-primary cp-host-name' : "text-danger cp-disable"
    let prLogHost = data?.LogicalHostname ? 'text-primary cp-host-name ' : "text-danger cp-disable"
    let drLogHost = data?.DR_LogicalHostname ? 'text-primary cp-host-name ' : "text-danger cp-disable"
    let prdata = data?.Dataspaceused ? 'text-primary cp-report ' : "text-danger cp-disable"
    let drdata = data?.DR_Dataspaceused ? 'text-primary cp-report ' : "text-danger cp-disable"
    let prlogDevice = data?.LoggingDeviceDetails ? 'text-primary cp-report' : "text-danger cp-disable"
    let drlogDevice = data?.DR_LoggingDeviceDetails ? 'text-primary cp-report' : "text-danger cp-disable"
    let prRepVersion = data?.ServerVersion ? 'text-primary cp-file-edits' : "text-danger cp-disable"
    let drRepVersion = data?.DR_ServerVersion ? 'text-primary cp-file-edits ' : "text-danger cp-disable"
    let prMaster = data?.MasterDB_Path ? 'text-primary cp-master ' : "text-danger cp-disable"
    let drMaster = data?.DR_MasterDB_Path ? 'text-primary cp-master' : "text-danger cp-disable"
    let prPath = data?.Path ? 'text-primary cp-file-edits ' : "text-danger cp-disable"
    let drPath = data?.DR_Path ? 'text-primary cp-file-edits ' : "text-danger cp-disable"

    const iconMapping = {
        'DataServer': prServer,
        'DR_DataServer': drServer,
        'BackupServer': prBackup,
        'DR_BackupServer': drBackup,
        'DatabaseName': prDatabase,
        'DR_DatabaseName': drDatabase,
        'DatabaseStatus': prDatabaseStatus,
        'DR_DatabaseStatus': drDatabaseStatus,
        'DatabaseLoggingStatus': prLogStatus,
        'DR_DatabaseLoggingStatus': drLogStatus,
        'ASEVersion': prVersion,
        'DR_ASEVersion': drVersion,
        'DataDeviceDetails': prDevice,
        'DR_DataDeviceDetails': drDevice,
        'LogSpaceUsed': prLog,
        'DR_LogSpaceUsed': drLog,
        'ServerIpAddress': prIP,
        'DR_ServerIpAddress': drIP,
        'ServerName': prSeverName,
        'DR_ServerName': drSeverName,
        'MasterDB_CommitTime': prTime,
        'DR_MasterDB_CommitTime': drTime,
        'CommitTime': prComTime,
        'DR_CommitTime': drComTime,
        'DistributionMode': prMode,
        'DR_DistributionMode': drMode,
        'RMAHost': prHost,
        'DR_RMAHost': drHost,
        'Dataspaceused': prdata,
        'DR_Dataspaceused': drdata,
        'Dataspaceused': prdata,
        'DR_Dataspaceused': drdata,
        'LoggingDeviceDetails': prlogDevice,
        'DR_LoggingDeviceDetails': drlogDevice,
        'ServerVersion': prRepVersion,
        'DR_ServerVersion': drRepVersion,
        'MasterDB_Path': prMaster,
        'DR_MasterDB_Path': drMaster,
        'Path': prPath,
        'DR_Path': drPath,
        'LogicalHostname': prLogHost,
        'DR_LogicalHostname': drLogHost,
    }
    let iconClass = iconMapping[property] || '';

    switch (displayedValue?.toLowerCase()) {

        case 'not allowed':
        case 'na':
            iconClass = 'text-danger cp-disable';
            break;
        case 'no':
            iconClass = 'text-danger cp-disagree';
            break;
        case 'streaming':
            iconClass = 'text-success cp-refresh';
            break;
        case 'running':
        case 'run':
            iconClass = 'text-success cp-reload cp-animate';
            break;
        case 'stopped':
        case 'stop':
            iconClass = 'text-danger cp-Stopped';
            break;
        case 'f':
        case 'false':
        case 'defer':
        case 'deferred':
            iconClass = 'text-danger cp-error';
            break;
        case 't':
        case 'yes':
        case 'deferred':
            iconClass = 'text-success cp-agree';
            break;
        case 'valid':
            iconClass = 'text-success cp-success';
            break;
        case 'pending':
            iconClass = 'text-warning cp-pending';
            break;
        case 'pause':
        case 'paused':
            iconClass = 'text-warning cp-circle-pause';
            break;
        case 'manual':
            iconClass = 'text-warning cp-settings';
            break;
        case 'synchronous_commit':
        case 'synchronized':
        case 'synchronizing':
        case 'sync':
        case 'synchronous':
            iconClass = 'text-success cp-refresh';
            break;
        case 'asynchronous_commit':
        case 'asynchronizing':
        case 'asynchronized':
        case 'async':
        case 'asynchronous':
            iconClass = 'text-danger cp-refresh';
            break;
        case 'online':
            iconClass = 'text-success cp-online';
            break;
        case 'offline':
            iconClass = 'text-danger cp-offline';
            break;
        case 'disabled':
        case 'disable':
            iconClass = 'text-danger cp-disables';
            break;
        case 'enabled':
        case 'enable':
            iconClass = 'text-success cp-enables';
            break;
        case 'connected':
        case 'connect':
            iconClass = 'text-success cp-connected';
            break;
        case 'disconnected':
        case 'disconnect':
            iconClass = 'text-danger cp-disconnecteds';
            break;
        case 'standby':
        case 'to standby':
            iconClass = 'text-warning cp-control-file-type';
            break;
        case 'required':
        case 'require':
            iconClass = 'text-warning cp-warning';
            break
        case 'healthy':
            iconClass = 'text-success cp-health-success';
            break;
        case 'not_healthy':
        case 'nothealthy':
        case 'unhealthy':
            iconClass = 'text-danger cp-health-error';
            break;
        case 'error':
            iconClass = 'text-danger cp-fail-back';
            break;
        case 'on':
            iconClass = 'text-success cp-end';
            break;
        case 'off':
            iconClass = 'text-danger cp-end';
            break;
        case 'current':
        case 'read write':
            iconClass = 'text-success cp-file-edits';
            break
        case 'primary':
            iconClass = 'text-primary cp-list-prsite';
            break
        case 'secondary':
            iconClass = 'text-info cp-dr';
            break
        case 'physical standby':
            iconClass = 'text-info cp-physical-drsite';
            break
        case 'active':
            iconClass = 'cp-active-inactive text-success'
            break
        case 'inactive':
            iconClass = 'cp-active-inactive text-danger'
            break
        default:
            break;
    }
    return iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
}