﻿namespace ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetDetail;

public class
    GetReplicationMasterDetailQueryHandler : IRequestHandler<GetReplicationMasterDetailQuery, ReplicationMasterDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IReplicationMasterRepository _replicationMasterRepository;

    public GetReplicationMasterDetailQueryHandler(IReplicationMasterRepository replicationMasterRepository,
        IMapper mapper)
    {
        _replicationMasterRepository = replicationMasterRepository;
        _mapper = mapper;
    }

    public async Task<ReplicationMasterDetailVm> Handle(GetReplicationMasterDetailQuery request,
        CancellationToken cancellationToken)
    {
        var replicationMaster = await _replicationMasterRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(replicationMaster, nameof(Domain.Entities.ReplicationMaster),
            new NotFoundException(nameof(Domain.Entities.ReplicationMaster), request.Id));

        var replicationMasterDetailDto = _mapper.Map<ReplicationMasterDetailVm>(replicationMaster);

        return replicationMasterDetailDto ??
               throw new NotFoundException(nameof(Domain.Entities.ReplicationMaster), request.Id);
    }
}