﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoFixture.AutoFakeItEasy" Version="4.18.1" />
    <PackageReference Include="AutoFixture.AutoMoq" Version="4.18.1" />
    <PackageReference Include="AutoFixture.Xunit2" Version="4.18.1" />
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Castle.Core" Version="5.2.1" />
    <PackageReference Include="FluentAssertions" Version="8.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
    <PackageReference Include="Microsoft.Data.SqlClient.SNI.runtime" Version="6.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.ObjectPool" Version="9.0.4" />
    <PackageReference Include="Microsoft.NETCore.Targets" Version="5.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
    <PackageReference Include="MockQueryable.Moq" Version="7.0.3" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="Shouldly" Version="4.3.0" />
    <PackageReference Include="WireMock.Net" Version="1.8.11" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Infrastructure\ContinuityPatrol.Persistence\ContinuityPatrol.Persistence.csproj" />
    <ProjectReference Include="..\..\..\Shared\ContinuityPatrol.Shared.Services\ContinuityPatrol.Shared.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.test.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
