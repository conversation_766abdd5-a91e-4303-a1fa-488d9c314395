const accManagerURL = {
    createOrUpdate: "Admin/AccessManager/CreateOrUpdate",
    userRole: "Admin/AccessManager/GetRoleDetails"
};


    $('#btnAccSave').prop('disabled', true);
    let createPermission = $("#AdminCreate")?.data("create-permission")?.toLowerCase();
    let currentuserRoleId = sessionStorage.getItem('userRoleId');
    if (createPermission == 'false') {
        $("#btnAccSave, #btnCancel").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
    }

    //Functions
    function updateCheckboxByRole(role) {
        let checkboxes = $('input[type="checkbox"]'),
            disableAll = ["SuperAdmin", "Administrator", "Operator", "Manager"].includes(role);
        checkboxes.toggleClass('opacity-80', disableAll);
        checkboxes.prop({ 'checked': !disableAll, 'disabled': disableAll });
        $("#btnAccSave").prop("disabled", true);
    };
    function validateDropDown(value, errorMessage, errorElement) {
        if (!value) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    };
    function anyCheckboxChecked() {
        return $('.form-check-input').is(':checked');
    };
    function getCheckedCategories(data) {
        let count = 0;
        for (const category in data.Permissions) {
            const permissions = data.Permissions[category];
            const categoryChecked = Object.values(permissions).some(checkbox => checkbox);
            if (categoryChecked) {
                count++;
            }
        }
        const errorElement = document.getElementById('treeList-error');
        if (count === 0) {
            errorElement.textContent = 'Please check at least one checkbox';
            errorElement.classList.add('field-validation-error');
            return false;
        } else {
            errorElement.textContent = '';
            errorElement.classList.remove('field-validation-error');
            return true;
        }
    }
function getUserRoleById(roleId) {
    $.ajax({
        url: '/Admin/AccessManager/GetRoleDetails?id=' + roleId,
        type: 'GET'
    }).done(function (res) {
        if (res.success) {
            const data = res.data;
            $('#userRoleRefId').val(data.id); // <== your test checks this
            $('#btnAccSave').text('Update');   // <== your test checks this
            const props = JSON.parse(data.properties);
            $('#chkMonitor').prop('checked', props.Permissions.Dashboard.Monitor); // or .View?
        }
    });
}


    //Events

    $(document).on('change', '#txtUserRoleName', function () {
        $("#btnCancel").prop("disabled", true);
        let userRole = $("#txtUserRoleName option:selected"),
            errorElement = $('#selectUserRole-error'),
            treeListErrorElement = $('#treeList-error'),
            userRoleName = userRole.text().trim(),
            userRoleId = userRole.attr("id");

        $("#btnAccSave, #btnCancel").toggle(!['Administrator', 'Manager', 'Operator', 'SuperAdmin'].includes(userRoleName));
        updateCheckboxByRole(userRoleName);
        getUserRoleById(userRoleId);
        validateDropDown(userRoleName, "select role", errorElement);
        treeListErrorElement.text('').removeClass('field-validation-error');
        if (createPermission === 'false') {
            $("#btnAccSave, #btnCancel").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle data-bs-target id');
        }
        $('#chkInfraFull').prop('checked', userRoleName === 'SuperAdmin');
        $('#chkInfraCustom').prop('checked', userRoleName !== 'SuperAdmin');
    });

    if (currentuserRoleId != null) {
        var userRole = $('#txtUserRoleName option[id="' + currentuserRoleId + '"]').val();
        $('#txtUserRoleName').val(userRole).trigger('change');
        sessionStorage.removeItem('userRoleId');
    }
    else {
        let currentUserRole = $("#currentuserRole")?.data("role");
        $('#txtUserRoleName').val(currentUserRole).trigger('change');
    };

    $('#btnCancel').on('click', function () {
        userRoleName = $("#txtUserRoleName option:selected").text().trim();
        userRoleId = $("#txtUserRoleName option:selected").attr("id");
        $('#TreeView').empty();
        $('#btnAccSave').text('Save').prop("disabled", true);
        $('#btnCancel').prop("disabled", true);
        $(':checkbox, .AccessManager_Table input[type="checkbox"]').prop("checked", false);
        getUserRoleById(userRoleId);
        updateCheckboxByRole(userRoleName);
        $('#selectUserRole-error, #selectUserName-error').hide();
        $('#chkInfraFull').prop('checked', userRoleName === 'SuperAdmin');
        $('#chkInfraCustom').prop('checked', userRoleName !== 'SuperAdmin');
    });

    $('#btnAccSave').on('click', async function (event) {
        let userRole = $("#txtUserRoleName option:selected");
        let userRoleRefId = $('#userRoleRefId').val();
        let userRoleName = userRole.text().trim();
        let userRoleId = userRole.attr("id");
        let jsonData = {
            "Permissions": {
                "Dashboard": {
                    "View": true,
                    "Monitor": $('#chkMonitor').is(':checked'),
                    "Management": $('#chkManagment').is(':checked')
                },
                "Configuration": {
                    "CreateAndEdit": $('#chkConfigAdd').is(':checked'),
                    "Delete": $('#chkConfigDelete').is(':checked'),
                    "View": $('#chkConfigView').is(':checked')
                },
                "Manage": {
                    "CreateAndEdit": $('#chkManageAdd').is(':checked'),
                    "Delete": $('#chkManageDelete').is(':checked'),
                    "View": $('#chkManageView').is(':checked')
                },
                "Alerts": {
                    "CreateAndEdit": $('.chkalertsAdd').is(':checked'),
                    "Delete": $('#chkalertsDelete').is(':checked'),
                    "View": $('#chkalertsView').is(':checked')
                },
                "Reports": {

                    "CreateAndEdit": $('#chkReportsAdd').is(':checked'),
                    "Delete": $('#chkReportsDelete').is(':checked'),
                    "View": $('#chkReportsView').is(':checked'),
                },
                "Orchestration": {

                    "CreateAndEdit": $('#chkOrchestrationAdd').is(':checked'),
                    "Delete": $('#chkOrchestrationDelete').is(':checked'),
                    "View": $('#chkOrchestrationView').is(':checked'),
                    "Execute": $('#chkOrchestrationExecute').is(':checked')
                },
                "Admin": {
                    "CreateAndEdit": $('#chkAdminAdd').is(':checked'),
                    "Delete": $('#chkAdminDelete').is(':checked'),
                    "View": $('#chkAdminView').is(':checked')
                },
                "Drift": {
                    "CreateAndEdit": $('#chkDriftAdd').is(':checked'),
                    "Delete": $('#chkDriftDelete').is(':checked'),
                    "View": $('#chkDriftView').is(':checked')
                },
                "ResiliencyReadiness": {
                    "CreateAndEdit": $('#chkResilencyAdd').is(':checked'),
                    "Delete": $('#chkResilencyDelete').is(':checked'),
                    "View": $('#chkResilencyView').is(':checked')
                },
                "Cyber": {
                    "CreateAndEdit": $('#chkCyberAdd').is(':checked'),
                    "Delete": $('#chkCyberDelete').is(':checked'),
                    "View": $('#chkCyberView').is(':checked')
                },
                "CloudConnect": {
                    "View": $('#chkcloudView').is(':checked')
                }
            }
        }
        let jsonString = JSON.stringify(jsonData);
        let result = getCheckedCategories(jsonData);
        let sanitizeaccessArray = ['txtUserRoleName', 'chkMonitor', 'chkManagment', 'chkConfigAdd', 'chkConfigDelete', 'chkConfigView', 'chkManageAdd',
            'chkManageDelete', 'chkManageView', 'chkalertsAdd', 'chkalertsDelete', 'chkalertsView', 'chkReportsAdd', 'chkReportsDelete', 'chkReportsView',
            'chkOrchestrationAdd', 'chkOrchestrationDelete', 'chkOrchestrationView', 'chkOrchestrationExecute', 'chkAdminAdd', 'chkAdminDelete', 'chkAdminView', 'chkCyberAdd', 'chkCyberDelete', 'chkCyberView',
            'chkResilencyAdd', 'chkResilencyDelete', 'chkResilencyView', 'chkDriftAdd', 'chkDriftDelete', 'chkDriftView', 'chkcloudView']
        sanitizeContainer(sanitizeaccessArray);

        if (userRoleName != null && result != false) {
            let token = $('input[name="__RequestVerificationToken"]').val();

            await $.ajax({
                url: RootUrl + accManagerURL.createOrUpdate,
                type: "POST",
                dataType: "json",
                headers: { "RequestVerificationToken": token },
                data: {
                    Id: userRoleRefId,
                    RoleId: userRoleId,
                    RoleName: userRoleName,
                    Properties: jsonString,
                },
                success: function (response) {
                    if (response?.success) {
                        notificationAlert("success", response.data)
                        $('#txtUserRoleName').trigger('change');
                        $('#btnAccSave').prop("disabled", true).text("Update");
                    }
                },
                error: function (response) {
                    errorNotification(response)
                }
            });
        }
        else if (result == false) {
            event.preventDefault();
        }
    });

    $('.form-check-input').on('change', function () {
        let isChecked = anyCheckboxChecked();
        $("#btnCancel, #btnAccSave").prop("disabled", !isChecked);
    });

    $(document).on('change', '.btnAccessAdd', function () {
        if ($(this).is(':checked')) {
            $(this).closest('tr').find('.btnAccessView').prop('checked', true);
        } else {
            $(this).closest('tr').find('.btnAccessDelete').prop('checked', false);
            $(this).closest('tr').find('.btnAccessExcute').prop('checked', false);
        }
    });

    $(document).on('change', '.btnAccessDelete', function () {
        if ($(this).is(':checked')) {
            $(this).closest('tr').find('.btnAccessView').prop('checked', true);
            $(this).closest('tr').find('.btnAccessAdd').prop('checked', true);
        } else {
            $(this).closest('tr').find('.btnAccessExcute').prop('checked', false);
        }
    });

    $(document).on('change', '.btnAccessView', function () {
        let row = $(this).closest('tr');
        if (!$(this).is(':checked')) {
            row.find('input[type="checkbox"]').prop('checked', false);
        }
    });

    $(document).on('change', '.btnAccessExcute', function () {
        let row = $(this).closest('tr');
        if ($(this).is(':checked')) {
            row.find('input[type="checkbox"]').prop('checked', true);
        }
    });
