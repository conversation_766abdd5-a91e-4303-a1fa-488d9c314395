﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class DatabaseRepositoryMocks
{
    public static Mock<IDatabaseRepository> CreateDatabaseRepository(List<Database> databases)
    {
        var databaseRepository = new Mock<IDatabaseRepository>();

        databaseRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(databases);

        //databaseRepository.Setup(repo => repo.GetBusinessServiceByServerId(It.IsAny<string>())).ReturnsAsync(server);

        databaseRepository.Setup(repo => repo.AddAsync(It.IsAny<Database>())).ReturnsAsync(
            (Database database) =>
            {
                database.Id = new Fixture().Create<int>();

                database.ReferenceId = new Fixture().Create<Guid>().ToString();

                databases.Add(database);

                return database;
            });

        return databaseRepository;
    }

    public static Mock<IDatabaseRepository> UpdateDatabaseRepository(List<Database> databases)
    {
        var mockDatabaseRepository = new Mock<IDatabaseRepository>();

        mockDatabaseRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(databases);

        //mockDatabaseRepository.Setup(repo => repo.GetBusinessServiceByServerId(It.IsAny<string>())).ReturnsAsync(server);

        mockDatabaseRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => databases.SingleOrDefault(x => x.ReferenceId == i));

        mockDatabaseRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Database>())).ReturnsAsync((Database database) =>
        {
            var index = databases.FindIndex(item => item.ReferenceId == database.ReferenceId);

            databases[index] = database;

            return database;
        });

        return mockDatabaseRepository;
    }

    public static Mock<IDatabaseRepository> DeleteDatabaseRepository(List<Database> databases)
    {
        var mockDatabaseRepository = new Mock<IDatabaseRepository>();

        mockDatabaseRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(databases);

        mockDatabaseRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => databases.SingleOrDefault(x => x.ReferenceId == i));

        //mockDatabaseRepository.Setup(repo => repo.GetDatabaseByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(databases);

        mockDatabaseRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Database>())).ReturnsAsync((Database database) =>
        {
            var index = databases.FindIndex(item => item.ReferenceId == database.ReferenceId);

            database.IsActive = false;

            databases[index] = database;

            return database;
        });

        return mockDatabaseRepository;
    }

    public static Mock<IDatabaseRepository> SaveAsDatabaseRepository(List<Database> databases)
    {
        var databaseRepository = new Mock<IDatabaseRepository>();

        databaseRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(databases);

        databaseRepository.Setup(repo => repo.AddAsync(It.IsAny<Database>())).ReturnsAsync(
            (Database database) =>
            {
                database.Id = new Fixture().Create<int>();

                database.ReferenceId = new Fixture().Create<Guid>().ToString();

                databases.Add(database);

                return database;
            });

        return databaseRepository;
    }

    public static Mock<IDatabaseRepository> GetDatabaseRepository(List<Database> databases)
    {
        var databaseRepository = new Mock<IDatabaseRepository>();

        databaseRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(databases);

       // databaseRepository.Setup(repo => repo.GetType(It.IsAny<string>())).ReturnsAsync(databases);

        databaseRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => databases.SingleOrDefault(x => x.ReferenceId == i));

        return databaseRepository;
    }

    public static Mock<IDatabaseRepository> GetDatabaseNamesRepository(List<Database> databases)
    {
        var databaseNamesRepository = new Mock<IDatabaseRepository>();

        databaseNamesRepository.Setup(repo => repo.GetDatabaseNames()).ReturnsAsync(databases);

        return databaseNamesRepository;
    }

    public static Mock<IDatabaseRepository> GetDatabaseNameUniqueRepository(List<Database> databases)
    {
        var databaseNameUniqueRepository = new Mock<IDatabaseRepository>();

        databaseNameUniqueRepository.Setup(repo => repo.IsDatabaseNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => databases.Exists(x => x.Name == i && x.ReferenceId == j));

        return databaseNameUniqueRepository;
    }

    public static Mock<IDatabaseRepository> GetDatabaseEmptyRepository()
    {
        var mockDatabaseRepository = new Mock<IDatabaseRepository>();

        //mockDatabaseRepository.Setup(repo => repo.GetType(It.IsAny<string>())).ReturnsAsync(new List<Database>());

        mockDatabaseRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Database>());

        mockDatabaseRepository.Setup(repo => repo.GetDatabaseNames()).ReturnsAsync(new List<Database>());

        mockDatabaseRepository.Setup(repo => repo.GetDatabaseByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(new List<Database>());

        mockDatabaseRepository.Setup(repo => repo.GetDatabaseByDatabaseTypeId(It.IsAny<string>())).ReturnsAsync(new List<Database>());

        return mockDatabaseRepository;
    }

    public static Mock<IDatabaseRepository> GetPaginatedDatabaseRepository(List<Database> databases)
    {
        var databaseRepository = new Mock<IDatabaseRepository>();

        var queryableDatabases = databases.BuildMock();

        databaseRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableDatabases);

        return databaseRepository;
    }

    public static Mock<IDatabaseRepository> GetDatabaseTypeRepository(List<Database> databases)
    {
        var databaseRepository = new Mock<IDatabaseRepository>();

        var queryableDatabases = databases.BuildMock();

        databaseRepository.Setup(repo => repo.GetDatabaseByType(It.IsAny<string>())).Returns(queryableDatabases);

        return databaseRepository;
    }


    public static Mock<IDatabaseRepository> GetDatabaseByDatabaseTypeIdRepository(List<Database> databases)
    {
        var databaseRepository = new Mock<IDatabaseRepository>();

        databaseRepository.Setup(repo => repo.GetDatabaseByDatabaseTypeId(It.IsAny<string>())).ReturnsAsync(databases);

        return databaseRepository;
    }

    
    public static Mock<IDatabaseRepository> GetDatabaseListByLicenseKey(List<Database> databases)
    {
        var databaseRepository = new Mock<IDatabaseRepository>();

        databaseRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(databases);

        databaseRepository.Setup(repo => repo.GetDatabaseListByLicenseKey(It.IsAny<string>())).ReturnsAsync(databases);

        return databaseRepository;
    }

    public static Mock<IDatabaseRepository> GetDatabaseByServerIdRepository(List<Database> databases)
    {
        var databaseRepository = new Mock<IDatabaseRepository>();

        databaseRepository.Setup(repo => repo.GetDatabaseByServerId(It.IsAny<string>())).ReturnsAsync(databases);

        return databaseRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateDatabaseEventRepository(List<UserActivity> userActivities)
    {
        var databaseEventRepository = new Mock<IUserActivityRepository>();

        databaseEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        databaseEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return databaseEventRepository;
    }
}