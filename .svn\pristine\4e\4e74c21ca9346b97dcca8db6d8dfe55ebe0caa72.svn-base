﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowProfileInfoRepositoryMocks
{
    public static Mock<IWorkflowProfileInfoRepository> CreateWorkflowProfileInfoRepository(List<WorkflowProfileInfo> workflowProfileInfos)
    {
        var workflowProfileInfoRepository = new Mock<IWorkflowProfileInfoRepository>();

        workflowProfileInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowProfileInfos);

        workflowProfileInfoRepository.Setup(repo => repo.GetProfileIdAttachByWorkflowId(It.IsAny<string>())).ReturnsAsync((string i) => workflowProfileInfos.SingleOrDefault(x => x.ReferenceId == i));

        workflowProfileInfoRepository.Setup(repo => repo.AddAsync(It.IsAny<WorkflowProfileInfo>())).ReturnsAsync(
            (WorkflowProfileInfo workflowProfileInfo) =>
            {
                workflowProfileInfo.Id = new Fixture().Create<int>();
                workflowProfileInfo.ReferenceId = new Fixture().Create<Guid>().ToString();
                workflowProfileInfos.Add(workflowProfileInfo);
                return workflowProfileInfo;
            });
        return workflowProfileInfoRepository;
    }

    public static Mock<IWorkflowProfileInfoRepository> UpdateWorkflowProfileInfoRepository(List<WorkflowProfileInfo> workflowProfileInfos)
    {
        var workflowProfileInfoRepository = new Mock<IWorkflowProfileInfoRepository>();

        workflowProfileInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowProfileInfos);

        workflowProfileInfoRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowProfileInfos.SingleOrDefault(x => x.ReferenceId == i));

        workflowProfileInfoRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowProfileInfo>())).ReturnsAsync((WorkflowProfileInfo workflowProfileInfo) =>
        {
            var index = workflowProfileInfos.FindIndex(item => item.ReferenceId == workflowProfileInfo.ReferenceId);
            workflowProfileInfos[index] = workflowProfileInfo;
            return workflowProfileInfo;
        });
        return workflowProfileInfoRepository;
    }

    public static Mock<IWorkflowProfileInfoRepository> DeleteWorkflowProfileInfoRepository(List<WorkflowProfileInfo> workflowProfileInfos)
    {
        var workflowProfileInfoRepository = new Mock<IWorkflowProfileInfoRepository>();

        workflowProfileInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowProfileInfos);

        workflowProfileInfoRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowProfileInfos.SingleOrDefault(x => x.ReferenceId == i));

        workflowProfileInfoRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowProfileInfo>())).ReturnsAsync((WorkflowProfileInfo workflowProfileInfo) =>
        {
            var index = workflowProfileInfos.FindIndex(item => item.ReferenceId == workflowProfileInfo.ReferenceId);
            workflowProfileInfo.IsActive = false;
            workflowProfileInfos[index] = workflowProfileInfo;

            return workflowProfileInfo;
        });

        return workflowProfileInfoRepository;
    }

    public static Mock<IWorkflowProfileInfoRepository> GetWorkflowProfileInfoRepository(List<WorkflowProfileInfo> workflowProfileInfos)
    {
        var workflowProfileInfoRepository = new Mock<IWorkflowProfileInfoRepository>();

        workflowProfileInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowProfileInfos);

        workflowProfileInfoRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowProfileInfos.SingleOrDefault(x => x.ReferenceId == i));

        return workflowProfileInfoRepository;
    }

    public static Mock<IWorkflowProfileInfoRepository> GetWorkflowProfileInfoDetailByProfileIdRepository(List<WorkflowProfileInfo> workflowProfileInfos)
    {
        var workflowProfileInfoRepository = new Mock<IWorkflowProfileInfoRepository>();

        workflowProfileInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowProfileInfos);

        workflowProfileInfoRepository.Setup(repo => repo.GetWorkflowProfileInfoByProfileId(It.IsAny<string>())).ReturnsAsync((string i) => workflowProfileInfos.SingleOrDefault(x => x.ProfileId == i));

        return workflowProfileInfoRepository;
    }

    public static Mock<IWorkflowProfileInfoRepository> GetWorkflowProfileInfoByProfileIdRepository(List<WorkflowProfileInfo> workflowProfileInfos)
    {
        var workflowProfileInfoRepository = new Mock<IWorkflowProfileInfoRepository>();

        workflowProfileInfoRepository.Setup(repo => repo.GetWorkflowProfileInfoByProfileIdAsync(It.IsAny<string>())).ReturnsAsync(workflowProfileInfos);

        return workflowProfileInfoRepository;
    }

    //public static Mock<IWorkflowProfileInfoRepository> GetWorkflowProfileInfoByWorkflowIdRepository(List<WorkflowProfileInfo> workflowProfileInfos)
    //{
    //    var workflowProfileInfoRepository = new Mock<IWorkflowProfileInfoRepository>();

    //    workflowProfileInfoRepository.Setup(repo => repo.GetProfileIdAttachByWorkflowId(It.IsAny<string>())).ReturnsAsync((string i) => workflowProfileInfos.SingleOrDefault(x => x.WorkflowId == i));

    //    return workflowProfileInfoRepository;
    //}

    public static Mock<IWorkflowProfileInfoRepository> GetWorkflowProfileInfoNameUniqueRepository(List<WorkflowProfileInfo> workflowProfileInfos)
    {
        var workflowProfileInfoRepository = new Mock<IWorkflowProfileInfoRepository>();

        workflowProfileInfoRepository.Setup(repo => repo.IsWorkflowProfileInfoNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => workflowProfileInfos.Exists(x => x.ProfileName == i && x.ReferenceId == j));
        
        return workflowProfileInfoRepository;
    }

    public static Mock<IWorkflowProfileInfoRepository> GetWorkflowProfileInfoEmptyRepository()
    {
        var workflowProfileInfoEmptyRepository = new Mock<IWorkflowProfileInfoRepository>();

        workflowProfileInfoEmptyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowProfileInfo>());

        workflowProfileInfoEmptyRepository.Setup(repo => repo.GetWorkflowProfileByInfraId(It.IsAny<string>())).ReturnsAsync(new List<WorkflowProfileInfo>());

        workflowProfileInfoEmptyRepository.Setup(repo => repo.GetWorkflowProfileFilterByProfileId(It.IsAny<string>())).ReturnsAsync((WorkflowProfileInfo)null);

        workflowProfileInfoEmptyRepository.Setup(repo => repo.GetWorkflowProfileInfoByProfileId(It.IsAny<string>())).ReturnsAsync((WorkflowProfileInfo)null);

        return workflowProfileInfoEmptyRepository;
    }

    public static Mock<IWorkflowProfileInfoRepository> GetWorkflowProfileInfoNamesRepository(List<WorkflowProfileInfo> workflowProfileInfos)
    {
        var workflowProfileInfoNamesRepository = new Mock<IWorkflowProfileInfoRepository>();

        workflowProfileInfoNamesRepository.Setup(repo => repo.GetWorkflowProfileInfoNames()).ReturnsAsync(workflowProfileInfos);

        return workflowProfileInfoNamesRepository;
    }

    public static Mock<IWorkflowProfileInfoRepository> GetPaginatedWorkflowProfileInfoRepository(List<WorkflowProfileInfo> workflowProfileInfos)
    {
        var workflowProfileInfoRepository = new Mock<IWorkflowProfileInfoRepository>();

        var queryableNode = workflowProfileInfos.BuildMock();

        workflowProfileInfoRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableNode);

        return workflowProfileInfoRepository;
    }

    public static Mock<IWorkflowProfileInfoRepository> GetWorkflowProfileByInfraIdRepository(List<WorkflowProfileInfo> workflowProfileInfos)
    {
        var workflowProfileInfoRepository = new Mock<IWorkflowProfileInfoRepository>();

        workflowProfileInfoRepository.Setup(repo => repo.GetWorkflowProfileByInfraId(It.IsAny<string>())).ReturnsAsync(workflowProfileInfos);

        return workflowProfileInfoRepository;
    }

    //Events
    public static Mock<IUserActivityRepository> CreateWorkflowProfileInfoEventRepository(List<UserActivity> userActivities)
    {
        var workflowProfileInfoEventRepository = new Mock<IUserActivityRepository>();       

        workflowProfileInfoEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return workflowProfileInfoEventRepository;
    }
}
