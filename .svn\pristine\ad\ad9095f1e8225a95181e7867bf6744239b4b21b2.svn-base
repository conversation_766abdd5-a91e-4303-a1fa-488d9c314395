using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public static class ApprovalMatrixApprovalRepositoryMocks
{
    public static Mock<IApprovalMatrixApprovalRepository> CreateApprovalMatrixApprovalRepository(List<ApprovalMatrixApproval> approvalMatrixApprovals)
    {
        var mockApprovalMatrixApprovalRepository = new Mock<IApprovalMatrixApprovalRepository>();

        mockApprovalMatrixApprovalRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(approvalMatrixApprovals);

        mockApprovalMatrixApprovalRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => approvalMatrixApprovals.FirstOrDefault(x => x.ReferenceId == id));

        mockApprovalMatrixApprovalRepository.Setup(repo => repo.GetApprovalMatrixApprovalByRequestId(It.IsAny<string>()))
            .ReturnsAsync((string requestId) => approvalMatrixApprovals.Where(x => x.RequestId == requestId).ToList());

        mockApprovalMatrixApprovalRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string name, string id) => 
                approvalMatrixApprovals.Any(x => x.ProcessName == name && x.ReferenceId != id && x.IsActive));

        mockApprovalMatrixApprovalRepository.Setup(repo => repo.AddAsync(It.IsAny<ApprovalMatrixApproval>()))
            .ReturnsAsync((ApprovalMatrixApproval approvalMatrixApproval) =>
            {
                approvalMatrixApproval.ReferenceId = Guid.NewGuid().ToString();
                approvalMatrixApproval.Id = approvalMatrixApprovals.Count + 1;
                approvalMatrixApprovals.Add(approvalMatrixApproval);
                return approvalMatrixApproval;
            });

        //mockApprovalMatrixApprovalRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ApprovalMatrixApproval>()))
        //    .Returns(Task.CompletedTask)
        //    .Callback<ApprovalMatrixApproval>((approvalMatrixApproval) =>
        //    {
        //        var existingApproval = approvalMatrixApprovals.FirstOrDefault(x => x.ReferenceId == approvalMatrixApproval.ReferenceId);
        //        if (existingApproval != null)
        //        {
        //            existingApproval.ProcessName = approvalMatrixApproval.ProcessName;
        //            existingApproval.Description = approvalMatrixApproval.Description;
        //            existingApproval.UserName = approvalMatrixApproval.UserName;
        //            existingApproval.Status = approvalMatrixApproval.Status;
        //            existingApproval.Message = approvalMatrixApproval.Message;
        //            existingApproval.IsApproval = approvalMatrixApproval.IsApproval;
        //            existingApproval.ApproverName = approvalMatrixApproval.ApproverName;
        //            existingApproval.StartDateTime = approvalMatrixApproval.StartDateTime;
        //            existingApproval.EndDateTime = approvalMatrixApproval.EndDateTime;
        //            existingApproval.IsActive = approvalMatrixApproval.IsActive;
        //        }
        //    });

        //mockApprovalMatrixApprovalRepository.Setup(repo => repo.DeleteAsync(It.IsAny<ApprovalMatrixApproval>()))
        //    .Returns(Task.CompletedTask)
        //    .Callback<ApprovalMatrixApproval>((approvalMatrixApproval) =>
        //    {
        //        approvalMatrixApprovals.Remove(approvalMatrixApproval);
        //    });

        //mockApprovalMatrixApprovalRepository.Setup(repo => repo.PaginatedListAllAsync(
        //    It.IsAny<int>(), It.IsAny<int>(), It.IsAny<ISpecification<ApprovalMatrixApproval>>(), 
        //    It.IsAny<string>(), It.IsAny<string>()))
        //    .ReturnsAsync((int pageNumber, int pageSize, ISpecification<ApprovalMatrixApproval> spec, string sortColumn, string sortOrder) =>
        //    {
        //        var filteredApprovals = approvalMatrixApprovals.Where(x => x.IsActive).ToList();
        //        var totalCount = filteredApprovals.Count;
        //        var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        //        var skip = (pageNumber - 1) * pageSize;
        //        var pagedApprovals = filteredApprovals.Skip(skip).Take(pageSize).ToList();

        //        return new PaginatedResult<ApprovalMatrixApproval>
        //        {
        //            Data = pagedApprovals,
        //            PageSize = pageSize,
        //            TotalCount = totalCount,
        //            TotalPages = totalPages
        //        };
        //    });

        return mockApprovalMatrixApprovalRepository;
    }

    public static Mock<IApprovalMatrixRepository> CreateApprovalMatrixRepository(List<ApprovalMatrix> approvalMatrices)
    {
        var mockApprovalMatrixRepository = new Mock<IApprovalMatrixRepository>();

        mockApprovalMatrixRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => approvalMatrices.FirstOrDefault(x => x.ReferenceId == id));

        mockApprovalMatrixRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(approvalMatrices);

        //mockApprovalMatrixRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ApprovalMatrix>()))
        //    .Returns(Task.CompletedTask)
        //    .Callback<ApprovalMatrix>((approvalMatrix) =>
        //    {
        //        var existingMatrix = approvalMatrices.FirstOrDefault(x => x.ReferenceId == approvalMatrix.ReferenceId);
        //        if (existingMatrix != null)
        //        {
        //            existingMatrix.Name = approvalMatrix.Name;
        //            existingMatrix.Description = approvalMatrix.Description;
        //            existingMatrix.Properties = approvalMatrix.Properties;
        //            existingMatrix.IsActive = approvalMatrix.IsActive;
        //        }
        //    });

        return mockApprovalMatrixRepository;
    }

    public static Mock<IWorkflowRepository> CreateWorkflowRepository(List<Workflow> workflows)
    {
        var mockWorkflowRepository = new Mock<IWorkflowRepository>();


        //mockWorkflowRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Workflow>()))
        //    .Returns(Task.CompletedTask)
        //    .Callback<Workflow>((workflow) =>
        //    {
        //        var existingWorkflow = workflows.FirstOrDefault(x => x.ReferenceId == workflow.ReferenceId);
        //        if (existingWorkflow != null)
        //        {
        //            existingWorkflow.IsActive = workflow.IsActive;
        //        }
        //    });

        return mockWorkflowRepository;
    }

    public static Mock<IWorkflowTempRepository> CreateWorkflowTempRepository(List<WorkflowTemp> workflowTemps)
    {
        var mockWorkflowTempRepository = new Mock<IWorkflowTempRepository>();


        //mockWorkflowTempRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowTemp>()))
        //    .Returns(Task.CompletedTask)
        //    .Callback<WorkflowTemp>((workflowTemp) =>
        //    {
        //        var existingWorkflowTemp = workflowTemps.FirstOrDefault(x => x.ReferenceId == workflowTemp.ReferenceId);
        //        if (existingWorkflowTemp != null)
        //        {
        //            existingWorkflowTemp.IsActive = workflowTemp.IsActive;
        //        }
        //    });

        return mockWorkflowTempRepository;
    }

    public static Mock<IApprovalMatrixRequestRepository> CreateApprovalMatrixRequestRepository(List<ApprovalMatrixRequest> approvalMatrixRequests)
    {
        var mockApprovalMatrixRequestRepository = new Mock<IApprovalMatrixRequestRepository>();

        mockApprovalMatrixRequestRepository.Setup(repo => repo.GetByRequestId(It.IsAny<string>()))
            .ReturnsAsync((string requestId) => approvalMatrixRequests.FirstOrDefault(x => x.RequestId == requestId));

        //mockApprovalMatrixRequestRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ApprovalMatrixRequest>()))
        //    .Returns(Task.CompletedTask)
        //    .Callback<ApprovalMatrixRequest>((request) =>
        //    {
        //        var existingRequest = approvalMatrixRequests.FirstOrDefault(x => x.RequestId == request.RequestId);
        //        if (existingRequest != null)
        //        {
        //            existingRequest.Status = request.Status;
        //            existingRequest.IsActive = request.IsActive;
        //        }
        //    });

        return mockApprovalMatrixRequestRepository;
    }

    public static Mock<IUserActivityRepository> CreateUserActivityRepository(List<UserActivity> userActivities)
    {
        var mockUserActivityRepository = new Mock<IUserActivityRepository>();

        mockUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
            .ReturnsAsync((UserActivity userActivity) =>
            {
                userActivity.Id = userActivities.Count + 1;
                userActivity.ReferenceId = Guid.NewGuid().ToString();
                userActivities.Add(userActivity);
                return userActivity;
            });

        mockUserActivityRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(userActivities);

        return mockUserActivityRepository;
    }
}
