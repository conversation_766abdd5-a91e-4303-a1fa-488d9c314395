﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Repositories;

public class PluginManagerHistoryRepository : BaseRepository<PluginManagerHistory>, IPluginManagerHistoryRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public PluginManagerHistoryRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public Task<List<PluginManagerHistory>> GetPluginManagerHistoryNames()
    {
        var matches = _dbContext.PluginManagers.Where(e => e.IsActive)
            .Select(x => new PluginManagerHistory { ReferenceId = x.ReferenceId, LoginName = x.Name })
            .ToList();

        return Task.FromResult(matches);
    }

    public Task<bool> IsPluginManagerHistoryNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(x => x.LoginName.Equals(name))
            : Entities.Where(y => y.LoginName.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsPluginManagerHistoryNameUnique(string name)
    {
        var match = _dbContext.PluginManagerHistories.Any(x => x.LoginName.Equals(name));

        return Task.FromResult(match);
    }

    public override Task<IReadOnlyList<PluginManagerHistory>> ListAllAsync()
    {
        return _loggedInUserService.IsParent
            ? base.ListAllAsync()
            : FindByFilterAsync(
                pluginManagerHistory => pluginManagerHistory.CompanyId.Equals(_loggedInUserService.CompanyId));
    }

    public Task<List<PluginManagerHistory>> GetPluginManagerHistoryByPluginManagerId(string pluginId)
    {
        Guard.Against.InvalidGuidOrEmpty(pluginId, "PlugInManagerId", "PlugInManagerId cannot be invalid");

        if (_loggedInUserService.IsParent)
            return _dbContext.PluginManagerHistories
                .Active()
                .Where(e => e.PluginManagerId.Equals(pluginId)).ToListAsync();

        return _dbContext.PluginManagerHistories
            .Active()
            .Where(pluginManagerHistory => pluginManagerHistory.PluginManagerId.Equals(pluginId) &&
                                           pluginManagerHistory.CompanyId.Equals(_loggedInUserService.CompanyId))
            .ToListAsync();
    }
}