﻿namespace ContinuityPatrol.Application.Features.DashboardView.Queries.GetDetail;

public class GetDashboardViewDetailQueryHandler : IRequestHandler<GetDashboardViewDetailQuery, DashboardViewDetailVm>
{
    private readonly IDashboardViewRepository _dashboardViewRepository;
    private readonly IMapper _mapper;

    public GetDashboardViewDetailQueryHandler(IMapper mapper, IDashboardViewRepository dashboardViewRepository)
    {
        _mapper = mapper;
        _dashboardViewRepository = dashboardViewRepository;
    }

    public async Task<DashboardViewDetailVm> Handle(GetDashboardViewDetailQuery request,
        CancellationToken cancellationToken)
    {
        var dataLagStatus = await _dashboardViewRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(dataLagStatus, nameof(Domain.Entities.DashboardView),
            new NotFoundException(nameof(Domain.Entities.DashboardView), request.Id));

        var dataLagStatusDetailDto = _mapper.Map<DashboardViewDetailVm>(dataLagStatus);

        return dataLagStatusDetailDto == null
            ? throw new NotFoundException(nameof(Domain.Entities.DashboardView), request.Id)
            : dataLagStatusDetailDto;
    }
}