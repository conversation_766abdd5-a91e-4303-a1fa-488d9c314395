using ContinuityPatrol.Domain.ViewModels.ServerSubTypeModel;

namespace ContinuityPatrol.Application.Features.ServerSubType.Queries.GetList;

public class GetServerSubTypeListQueryHandler : IRequestHandler<GetServerSubTypeListQuery, List<ServerSubTypeListVm>>
{
    private readonly IMapper _mapper;
    private readonly IServerSubTypeRepository _serverSubTypeRepository;

    public GetServerSubTypeListQueryHandler(IMapper mapper, IServerSubTypeRepository serverSubTypeRepository)
    {
        _mapper = mapper;
        _serverSubTypeRepository = serverSubTypeRepository;
    }

    public async Task<List<ServerSubTypeListVm>> Handle(GetServerSubTypeListQuery request,
        CancellationToken cancellationToken)
    {
        var serverSubTypes = await _serverSubTypeRepository.ListAllAsync();

        return serverSubTypes.Count <= 0
            ? new List<ServerSubTypeListVm>()
            : _mapper.Map<List<ServerSubTypeListVm>>(serverSubTypes);
    }
}