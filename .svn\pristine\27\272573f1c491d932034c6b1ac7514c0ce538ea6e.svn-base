﻿@model ContinuityPatrol.Domain.ViewModels.FormTypeModel.FormTypeViewModel

@Html.AntiForgeryToken()
<div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel ">
    <form class="modal-content" id="CreateForm">
        @* asp-controller="FormType" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data" *@
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-form-name"></i><span>Form Type Configuration</span></h6>
            <button type="button" class="btn-close" title="Close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label class="form-label" for="formTypeName">Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-name"></i></span>
                    <input asp-for="FormTypeName" type="text" id="formTypeName" class="form-control" maxlength="100"                          
                           placeholder="Enter Form Type Name" autocomplete="off" />
                    <input asp-for="Id" type="hidden" id="formTypeID" />                   
                </div>
                <span asp-validation-for="FormTypeName" id="FormTypeName-error"></span>
            </div>            
        </div>
        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm" id="SaveFunction">Save</button>
            </div>
        </div>
    </form>
</div>
