﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <SatelliteResourceLanguages>en</SatelliteResourceLanguages>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
 
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="8.1.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="8.1.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="8.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
  
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Infrastructure\ContinuityPatrol.Persistence\ContinuityPatrol.Persistence.csproj" />
  </ItemGroup>

</Project>
