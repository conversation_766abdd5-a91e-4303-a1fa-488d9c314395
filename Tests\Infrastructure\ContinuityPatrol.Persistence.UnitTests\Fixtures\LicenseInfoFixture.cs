using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class LicenseInfoFixture : IDisposable
{
    public List<LicenseInfo> LicenseInfoPaginationList { get; set; }
    public List<LicenseInfo> LicenseInfoList { get; set; }
    public LicenseInfo LicenseInfoDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string LicenseId = "LICENSE_123";
    public const string BusinessServiceId = "BS_123";
    public const string EntityId = "ENTITY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public LicenseInfoFixture()
    {
        var fixture = new Fixture();

        LicenseInfoList = fixture.Create<List<LicenseInfo>>();

        LicenseInfoPaginationList = fixture.CreateMany<LicenseInfo>(20).ToList();

        LicenseInfoPaginationList.ForEach(x =>
        {
            x.CompanyId = CompanyId;
            x.LicenseId = LicenseId;
            x.BusinessServiceId = BusinessServiceId;
            x.EntityId = EntityId;
            x.IsActive = true;
            x.PONumber = "PO" + fixture.Create<int>();
            x.BusinessServiceName = "Service" + fixture.Create<int>();
            x.Category = "Category" + fixture.Create<int>();
            x.EntityName = "Entity" + fixture.Create<int>();
            x.Entity = new[] { "Database", "Server", "Replication" }[fixture.Create<int>() % 3];
            x.EntityType = new[] { "Primary", "Secondary", "Backup" }[fixture.Create<int>() % 3];
            x.Type = new[] { "Production", "Development", "Testing" }[fixture.Create<int>() % 3];
            x.EntityField = "Field" + fixture.Create<int>();
            x.Logo = "logo" + fixture.Create<int>() + ".png";
        });

        LicenseInfoList.ForEach(x =>
        {
            x.CompanyId = CompanyId;
            x.LicenseId = LicenseId;
            x.BusinessServiceId = BusinessServiceId;
            x.EntityId = EntityId;
            x.IsActive = true;
            x.PONumber = "PO" + fixture.Create<int>();
            x.BusinessServiceName = "Service" + fixture.Create<int>();
            x.Category = "Category" + fixture.Create<int>();
            x.EntityName = "Entity" + fixture.Create<int>();
            x.Entity = new[] { "Database", "Server", "Replication" }[fixture.Create<int>() % 3];
            x.EntityType = new[] { "Primary", "Secondary", "Backup" }[fixture.Create<int>() % 3];
            x.Type = new[] { "Production", "Development", "Testing" }[fixture.Create<int>() % 3];
            x.EntityField = "Field" + fixture.Create<int>();
            x.Logo = "logo" + fixture.Create<int>() + ".png";
        });

        LicenseInfoDto = fixture.Create<LicenseInfo>();
        LicenseInfoDto.CompanyId = CompanyId;
        LicenseInfoDto.LicenseId = LicenseId;
        LicenseInfoDto.BusinessServiceId = BusinessServiceId;
        LicenseInfoDto.EntityId = EntityId;
        LicenseInfoDto.IsActive = true;
        LicenseInfoDto.PONumber = "PO123";
        LicenseInfoDto.BusinessServiceName = "TestService";
        LicenseInfoDto.Category = "TestCategory";
        LicenseInfoDto.EntityName = "TestEntity";
        LicenseInfoDto.Entity = "Database";
        LicenseInfoDto.EntityType = "Primary";
        LicenseInfoDto.Type = "Production";
        LicenseInfoDto.EntityField = "TestField";
        LicenseInfoDto.Logo = "testlogo.png";

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
