using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.Create;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.Update;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.UpdateJobState;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.UpdateJobStatus;
using ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberJobManagementLogsModel;
using ContinuityPatrol.Domain.ViewModels.CyberJobManagementModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ICyberJobManagementService
{
    Task<List<CyberJobManagementListVm>> GetCyberJobManagementList();
    Task<BaseResponse> CreateAsync(CreateCyberJobManagementCommand createCyberJobManagementCommand);
    Task<BaseResponse> UpdateAsync(UpdateCyberJobManagementCommand updateCyberJobManagementCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<CyberJobManagementDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsCyberJobManagementNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<CyberJobManagementListVm>> GetPaginatedCyberJobManagements(GetCyberJobManagementPaginatedListQuery query);
    Task<PaginatedResult<CyberJobManagementLogsListVm>> GetPaginatedCyberJobManagementLogs(GetCyberJobManagementLogsPaginatedListQuery query);
    #endregion
    Task<List<CyberJobManagementStatusVm>> GetCyberJobManagementStatus();
    Task<BaseResponse> UpdateStatus(UpdateCyberJobManagementStatusCommand updateCyberJobManagementStatusCommand);
    Task<BaseResponse> UpdateState(UpdateCyberJobManagementStateCommand updateCyberJobManagementStateCommand);
    Task<List<CyberJobManagementLogsListVm>> GetCyberJobManagementLogsList();

}
