using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberComponent.Events.Update;

public class CyberComponentUpdatedEventHandler : INotificationHandler<CyberComponentUpdatedEvent>
{
    private readonly ILogger<CyberComponentUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberComponentUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<CyberComponentUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(CyberComponentUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.CyberComponent}",
            Entity = Modules.CyberComponent.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Component '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Component '{updatedEvent.Name}' updated successfully.");
    }
}