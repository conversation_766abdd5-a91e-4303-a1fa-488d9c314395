﻿using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessFunction.Queries;

public class GetBusinessFunctionNameQueryHandlerTests : IClassFixture<BusinessFunctionFixture>
{
    private readonly BusinessFunctionFixture _businessFunctionFixture;
    private Mock<IBusinessFunctionRepository> _mockBusinessFunctionRepository;
    private readonly GetBusinessFunctionNameQueryHandler _handler;

    public GetBusinessFunctionNameQueryHandlerTests(BusinessFunctionFixture businessFunctionFixture)
    {
        _businessFunctionFixture = businessFunctionFixture;

        _mockBusinessFunctionRepository = BusinessFunctionRepositoryMocks.GetBusinessFunctionNameRepository(_businessFunctionFixture.BusinessFunctions);

        _handler = new GetBusinessFunctionNameQueryHandler(_businessFunctionFixture.Mapper, _mockBusinessFunctionRepository.Object);
    }

    //[Fact]
    //public async Task Handle_Return_Active_BusinessFunction_Name()
    //{
    //    var result = await _handler.Handle(new BusinessFunctionNameQuery(), CancellationToken.None);

    //    result.ShouldBeOfType<List<BusinessFunctionNameVm>>();

    //    result[0].Id.ShouldBe(_businessFunctionFixture.BusinessFunctions[0].ReferenceId);
    //    result[0].Name.ShouldBe(_businessFunctionFixture.BusinessFunctions[0].Name);
    //}

    [Fact]
    public async Task Handle_Return_Active_BusinessFunctionNamesCount()
    {
        var result = await _handler.Handle(new BusinessFunctionNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<BusinessFunctionNameVm>>();
        result.Count.ShouldBe(_businessFunctionFixture.BusinessFunctions.Count);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockBusinessFunctionRepository = BusinessFunctionRepositoryMocks.GetBusinessFunctionEmptyRepository();

        var handler = new GetBusinessFunctionNameQueryHandler(_businessFunctionFixture.Mapper, _mockBusinessFunctionRepository.Object);

        var result = await handler.Handle(new BusinessFunctionNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetBusinessFunctionNamesMethod_OneTime()
    {
        await _handler.Handle(new BusinessFunctionNameQuery(), CancellationToken.None);

        _mockBusinessFunctionRepository.Verify(x => x.GetBusinessFunctionNames(), Times.Once);
    }
}