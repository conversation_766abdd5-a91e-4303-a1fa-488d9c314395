using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class PageBuilderRepositoryTests : IClassFixture<PageBuilderFixture>
{
    private readonly PageBuilderFixture _pageBuilderFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly PageBuilderRepository _repository;

    public PageBuilderRepositoryTests(PageBuilderFixture pageBuilderFixture)
    {
        _pageBuilderFixture = pageBuilderFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockLoggedInUserService = LoggedInUserServiceRepositoryMocks.GetLoggedInUserServiceRepository();
        
        _repository = new PageBuilderRepository(_dbContext, mockLoggedInUserService.Object);
    }

    private async Task ClearDatabase()
    {
        _dbContext.PageBuilders.RemoveRange(_dbContext.PageBuilders);
        await _dbContext.SaveChangesAsync();
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var pageBuilder = _pageBuilderFixture.PageBuilderDto;

        // Act
        var result = await _repository.AddAsync(pageBuilder);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(pageBuilder.Name, result.Name);
        Assert.Equal(pageBuilder.Type, result.Type);
        Assert.Single(_dbContext.PageBuilders);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForNewEntity()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPageBuilder";
        var pageBuilder = _pageBuilderFixture.CreatePageBuilderWithSpecificName(existingName);
        await _repository.AddAsync(pageBuilder);

        // Act - Check for new entity (empty id)
        var result = await _repository.IsNameExist(existingName, string.Empty);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "NonExistentPageBuilder";

        // Act
        var result = await _repository.IsNameExist(nonExistentName, string.Empty);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPageBuilder";
        var pageBuilder = _pageBuilderFixture.CreatePageBuilderWithSpecificName(existingName);
        await _repository.AddAsync(pageBuilder);

        // Act - Check for same entity (using its own id)
        var result = await _repository.IsNameExist(existingName, pageBuilder.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntity()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPageBuilder";
        var pageBuilder1 = _pageBuilderFixture.CreatePageBuilderWithSpecificName(existingName);
        var pageBuilder2 = _pageBuilderFixture.CreatePageBuilderWithSpecificName("DifferentName");
        await _repository.AddAsync(pageBuilder1);
        await _repository.AddAsync(pageBuilder2);

        // Act - Check if name exists for different entity
        var result = await _repository.IsNameExist(existingName, pageBuilder2.ReferenceId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleInvalidGuid()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPageBuilder";
        var pageBuilder = _pageBuilderFixture.CreatePageBuilderWithSpecificName(existingName);
        await _repository.AddAsync(pageBuilder);

        // Act - Check with invalid GUID (should treat as new entity)
        var result = await _repository.IsNameExist(existingName, "invalid-guid");

        // Assert
        Assert.True(result);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntitiesWithSelectedFields()
    {
        // Arrange
        await ClearDatabase();
        var pageBuilders = new List<PageBuilder>
        {
            _pageBuilderFixture.CreatePageBuilderWithProperties(name: "PageBuilder1"),
            _pageBuilderFixture.CreatePageBuilderWithProperties(name: "PageBuilder2"),
            _pageBuilderFixture.CreatePageBuilderWithProperties(name: "PageBuilder3")
        };
        await _repository.AddRangeAsync(pageBuilders);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        
        // Verify that only selected fields are populated (as per the override implementation)
        foreach (var pageBuilder in result)
        {
            Assert.NotNull(pageBuilder.Id);
            Assert.NotNull(pageBuilder.ReferenceId);
            Assert.NotNull(pageBuilder.Name);
            Assert.NotNull(pageBuilder.Type);
            Assert.NotNull(pageBuilder.Properties);
            // IsLock and IsPublish should be populated
            Assert.NotNull(pageBuilder.IsLock);
            Assert.NotNull(pageBuilder.IsPublish);
        }
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmpty_WhenNoEntities()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEntitiesInDescendingOrder()
    {
        // Arrange
        await ClearDatabase();
        var pageBuilders = new List<PageBuilder>
        {
            _pageBuilderFixture.CreatePageBuilderWithProperties(name: "PageBuilder1"),
            _pageBuilderFixture.CreatePageBuilderWithProperties(name: "PageBuilder2"),
            _pageBuilderFixture.CreatePageBuilderWithProperties(name: "PageBuilder3")
        };
        
        // Add them one by one to ensure different creation times
        foreach (var pb in pageBuilders)
        {
            await _repository.AddAsync(pb);
            await Task.Delay(10); // Small delay to ensure different timestamps
        }

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        // Should be ordered by Id descending (DescOrderById)
        for (int i = 0; i < result.Count - 1; i++)
        {
            Assert.True(result[i].Id >= result[i + 1].Id);
        }
    }

    #endregion

    #region AddRangeAsync Tests

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultipleEntities()
    {
        // Arrange
        await ClearDatabase();
        var pageBuilders = new List<PageBuilder>
        {
            _pageBuilderFixture.CreatePageBuilderWithProperties(),
            _pageBuilderFixture.CreatePageBuilderWithProperties(),
            _pageBuilderFixture.CreatePageBuilderWithProperties()
        };

        // Act
        await _repository.AddRangeAsync(pageBuilders);

        // Assert
        var allPageBuilders = await _repository.ListAllAsync();
        Assert.Equal(3, allPageBuilders.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenListIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        await ClearDatabase();
        var pageBuilder = _pageBuilderFixture.CreatePageBuilderWithProperties();
        await _repository.AddAsync(pageBuilder);

        pageBuilder.Name = "UpdatedPageBuilder";
        pageBuilder.Type = "UpdatedType";
        pageBuilder.IsLock = true;

        // Act
        var result = await _repository.UpdateAsync(pageBuilder);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedPageBuilder", result.Name);
        Assert.Equal("UpdatedType", result.Type);
        Assert.True(result.IsLock);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldDeleteEntity()
    {
        // Arrange
        await ClearDatabase();
        var pageBuilder = _pageBuilderFixture.CreatePageBuilderWithProperties();
        await _repository.AddAsync(pageBuilder);

        // Act
        var result = await _repository.DeleteAsync(pageBuilder);

        // Assert
        Assert.NotNull(result);
        var allPageBuilders = await _repository.ListAllAsync();
        Assert.Empty(allPageBuilders);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var pageBuilder = _pageBuilderFixture.CreatePageBuilderWithProperties();
        await _repository.AddAsync(pageBuilder);

        // Act
        var result = await _repository.GetByReferenceIdAsync(pageBuilder.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(pageBuilder.ReferenceId, result.ReferenceId);
        Assert.Equal(pageBuilder.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ContinuityPatrol.Shared.Core.Exceptions.InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        await ClearDatabase();
        var tasks = new List<Task>();

        // Act - Perform concurrent add operations
        for (int i = 0; i < 10; i++)
        {
            var pageBuilder = _pageBuilderFixture.CreatePageBuilderWithProperties(name: $"ConcurrentPageBuilder_{i}");
            tasks.Add(_repository.AddAsync(pageBuilder));
        }

        await Task.WhenAll(tasks);

        // Assert
        var allPageBuilders = await _repository.ListAllAsync();
        Assert.Equal(10, allPageBuilders.Count);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleCaseSensitivity()
    {
        // Arrange
        await ClearDatabase();
        var originalName = "PageBuilderName";
        var pageBuilder = _pageBuilderFixture.CreatePageBuilderWithSpecificName(originalName);
        await _repository.AddAsync(pageBuilder);

        // Act - Check with different case
        var resultLowerCase = await _repository.IsNameExist(originalName.ToLower(), string.Empty);
        var resultUpperCase = await _repository.IsNameExist(originalName.ToUpper(), string.Empty);

        // Assert - Behavior depends on database collation, but method should not throw
        Assert.IsType<bool>(resultLowerCase);
        Assert.IsType<bool>(resultUpperCase);
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_WhenUpdatingMultipleEntities()
    {
        // Arrange
        await ClearDatabase();
        var pageBuilders = new List<PageBuilder>
        {
            _pageBuilderFixture.CreatePageBuilderWithProperties(name: "PageBuilder1"),
            _pageBuilderFixture.CreatePageBuilderWithProperties(name: "PageBuilder2"),
            _pageBuilderFixture.CreatePageBuilderWithProperties(name: "PageBuilder3")
        };
        await _repository.AddRangeAsync(pageBuilders);

        // Act - Update all entities
        foreach (var pageBuilder in pageBuilders)
        {
            pageBuilder.Type = "UpdatedType";
            pageBuilder.IsLock = true;
            await _repository.UpdateAsync(pageBuilder);
        }

        // Assert
        var allPageBuilders = await _repository.ListAllAsync();
        Assert.Equal(3, allPageBuilders.Count);
        Assert.All(allPageBuilders, pb => Assert.Equal("UpdatedType", pb.Type));
        Assert.All(allPageBuilders, pb => Assert.True(pb.IsLock));
    }

    [Fact]
    public async Task ListAllAsync_ShouldHandleLargeDataSet()
    {
        // Arrange
        await ClearDatabase();
        var pageBuilders = new List<PageBuilder>();

        // Create 100 page builders
        for (int i = 0; i < 100; i++)
        {
            pageBuilders.Add(_pageBuilderFixture.CreatePageBuilderWithProperties(name: $"PageBuilder_{i:D3}"));
        }

        await _repository.AddRangeAsync(pageBuilders);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(100, result.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleSpecialCharactersInName()
    {
        // Arrange
        await ClearDatabase();
        var specialName = "<EMAIL>";
        var pageBuilder = _pageBuilderFixture.CreatePageBuilderWithSpecificName(specialName);

        // Act
        var addedPageBuilder = await _repository.AddAsync(pageBuilder);
        var nameExists = await _repository.IsNameExist(specialName, string.Empty);

        // Assert
        Assert.NotNull(addedPageBuilder);
        Assert.Equal(specialName, addedPageBuilder.Name);
        Assert.True(nameExists);
    }

    [Fact]
    public async Task Repository_ShouldHandleEntityWithNullProperties()
    {
        // Arrange
        await ClearDatabase();
        var pageBuilder = _pageBuilderFixture.CreatePageBuilderWithProperties();
        pageBuilder.Properties = null;
        pageBuilder.Type = null;

        // Act & Assert - Should not throw
        var result = await _repository.AddAsync(pageBuilder);
        Assert.NotNull(result);
        Assert.Null(result.Properties);
        Assert.Null(result.Type);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleEmptyAndNullNames()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert - Should not throw
        var resultEmpty = await _repository.IsNameExist(string.Empty, string.Empty);
        var resultNull = await _repository.IsNameExist(null, string.Empty);

        Assert.IsType<bool>(resultEmpty);
        Assert.IsType<bool>(resultNull);
    }

    #endregion

    #region Edge Cases Tests

    [Fact]
    public async Task Repository_ShouldHandlePublishedAndLockedStates()
    {
        // Arrange
        await ClearDatabase();
        var lockedPageBuilder = _pageBuilderFixture.CreateLockedPageBuilder();
        var publishedPageBuilder = _pageBuilderFixture.CreatePublishedPageBuilder();

        // Act
        await _repository.AddAsync(lockedPageBuilder);
        await _repository.AddAsync(publishedPageBuilder);

        // Assert
        var allPageBuilders = await _repository.ListAllAsync();
        Assert.Equal(2, allPageBuilders.Count);

        var locked = allPageBuilders.FirstOrDefault(pb => pb.IsLock);
        var published = allPageBuilders.FirstOrDefault(pb => pb.IsPublish);

        Assert.NotNull(locked);
        Assert.NotNull(published);
        Assert.True(locked.IsLock);
        Assert.True(published.IsPublish);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnCorrectResult_WhenMultipleEntitiesWithSameName()
    {
        // Arrange
        await ClearDatabase();
        var sameName = "DuplicateName";
        var pageBuilders = _pageBuilderFixture.CreateMultiplePageBuildersWithSameName(sameName, 3);
        await _repository.AddRangeAsync(pageBuilders);

        // Act
        var result = await _repository.IsNameExist(sameName, string.Empty);

        // Assert
        Assert.True(result);
    }

    #endregion
}
