﻿.DesignArea-Tab .nav-link {
    color: var(--bs-dark);
}

    .DesignArea-Tab .nav-link:focus, .nav-link:hover {
        background-color: var(--bs-dropdown-link-hover-bg);
        color: var(--bs-primary);
    }
.DesignArea-Tab .nav-item .active {
    color: var(--bs-primary) !important;
}

.drag > li {
    cursor: move !important;
    border: 1px solid var(--bs-gray-300);
    background: #fff;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    overflow: hidden;
    border-radius: 5px !important;
    width: 30%;
    height: 70px;
    float: left;
    margin: 5px 5px !important;
    box-shadow: 0 0.1rem 0.5rem rgba(0, 0, 0, 0.1);
}

.drag .drag-button {
    cursor: move;
    /* font-size: 1em; */
    line-height: 1.8em;
    display: block;
    height: 100%;
    width: 100%;
    background: transparent;
    border: 0;
    text-align: left;
    padding: 10px;
    border-radius: 0;
    display: grid;
    align-items: center;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    justify-content: center;
}

    .drag .drag-button > span {
        float: none !important;
        margin-right: auto !important;
        text-align: center !important;
        width: 20px !important;
        height: 20px !important;
        margin: auto;
    }

.CreateWidget_accordion {
    --bs-accordion-btn-icon-width: 0.9rem !important;
    --bs-accordion-btn-padding-x: 1rem;
    --bs-accordion-btn-padding-y: 0.8rem;
}

    .CreateWidget_accordion .accordion-button {
        font-size:13px;
    }

.cellActive {
    text-align:center;
}

    .cellActive .Active {
        background-color: aliceblue;
    }

.min-hight-100{
    min-height:100px;
    max-height:auto;
}

#contextMenu {
    display: none;
    position: absolute;
    background-color: #fff;
    border: 1px solid #ccc;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}
#contextMenuCustom {
    display: none;
    position: absolute;
    background-color: #fff;
    border: 1px solid #ccc;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 99999;
}


.dotted-border {
    border-style: dashed !important;
    border: 1px solid #ccc;
    box-shadow: none !important;
}

/*.pageBuilderSetColumnDesign {
    border: 1px solid #ccc;
    padding: 10px;
    cursor: pointer;
    float: left;
    margin-right: 10px;
}

.pageBuilderSetRowDesign {
    overflow: hidden;
}*/

.layoutcontainer {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px;
}

.layoutcard {
    background-color: var(--bs-body-bg);
    width: 200px;
    /*height: 150px;*/
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    /*padding: 30px;*/
    box-sizing: border-box;
    position: relative;
}

.card-show{
    display:flex;
    align-items:center;
    gap:10px;
}

.card-header {
    padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);
    margin-bottom: 0;
    color: var(--bs-card-cap-color);
    border-bottom: var(--bs-card-border-width) solid var(--bs-card-border-color);
    background-color: var(--bs-body-bg)
}