﻿namespace ContinuityPatrol.Application.Features.PluginManager.Commands.Create;

public class CreatePluginManagerCommand : IRequest<CreatePluginManagerResponse>
{
    public string Name { get; set; }
    public string CompanyId { get; set; }
    public string Properties { get; set; }
    public string Description { get; set; }
    public string Version { get; set; }

    public override string ToString()
    {
        return $"Name: {Name};";
    }
}