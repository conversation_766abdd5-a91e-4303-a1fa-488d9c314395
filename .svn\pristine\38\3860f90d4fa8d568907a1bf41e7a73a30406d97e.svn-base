﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller;

public class SVCGlobalMirrorOracleFullDBRacControllerShould
{
    private readonly SVCGlobalMirrorOracleFullDBRacController _controller;

    public SVCGlobalMirrorOracleFullDBRacControllerShould()
    {
        _controller = new SVCGlobalMirrorOracleFullDBRacController();
    }

    [Fact]
    public void List_Returns_ViewResult()
    {
            
        var result = _controller.List();

            
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.NotNull(viewResult);
    }
}