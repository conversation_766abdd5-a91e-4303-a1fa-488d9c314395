using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class FiaIntervalFixture : IDisposable
{
    public List<FiaInterval> FiaIntervalPaginationList { get; set; }
    public List<FiaInterval> FiaIntervalList { get; set; }
    public FiaInterval FiaIntervalDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public FiaIntervalFixture()
    {
        var fixture = new Fixture();

        FiaIntervalList = fixture.Create<List<FiaInterval>>();

        FiaIntervalPaginationList = fixture.CreateMany<FiaInterval>(20).ToList();

        FiaIntervalPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FiaIntervalPaginationList.ForEach(x => x.IsActive = true);

        FiaIntervalList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FiaIntervalList.ForEach(x => x.IsActive = true);

        FiaIntervalDto = fixture.Create<FiaInterval>();
        FiaIntervalDto.ReferenceId = Guid.NewGuid().ToString();
        FiaIntervalDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
