﻿using ContinuityPatrol.Domain.ViewModels.IncidentDailyModel;

namespace ContinuityPatrol.Application.Features.IncidentDaily.Queries.GetList;

public class GetIncidentDailyListQueryHandler : IRequestHandler<GetIncidentDailyListQuery, List<IncidentDailyListVm>>
{
    private readonly IIncidentDailyRepository _incidentDailyRepository;
    private readonly IMapper _mapper;

    public GetIncidentDailyListQueryHandler(IMapper mapper, IIncidentDailyRepository incidentDailyRepository)
    {
        _incidentDailyRepository = incidentDailyRepository;
        _mapper = mapper;
    }

    public async Task<List<IncidentDailyListVm>> Handle(GetIncidentDailyListQuery request,
        CancellationToken cancellationToken)
    {
        var IncidentDailyList = await _incidentDailyRepository.ListAllAsync();

        return IncidentDailyList.Count <= 0
            ? new List<IncidentDailyListVm>()
            : _mapper.Map<List<IncidentDailyListVm>>(IncidentDailyList);
    }
}