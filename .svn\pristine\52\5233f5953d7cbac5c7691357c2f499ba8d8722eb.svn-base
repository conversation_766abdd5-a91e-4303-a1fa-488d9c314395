﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.EscalationMatrix.Events.PaginatedView;

public class EscalationMatrixPaginatedEventHandler : INotificationHandler<EscalationMatrixPaginatedEvent>
{
    private readonly ILogger<EscalationMatrixPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public EscalationMatrixPaginatedEventHandler(ILoggedInUserService userService,
        ILogger<EscalationMatrixPaginatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(EscalationMatrixPaginatedEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.View} {Modules.EscalationMatrix}",
            Entity = Modules.EscalationMatrix.ToString(),
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Escalation Matrix viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Escalation Matrix viewed");
    }
}