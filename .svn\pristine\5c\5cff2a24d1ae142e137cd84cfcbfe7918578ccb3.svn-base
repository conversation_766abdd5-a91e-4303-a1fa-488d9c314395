﻿namespace ContinuityPatrol.Application.Features.ServerType.Commands.Update;

public class UpdateServerTypeCommandValidator : AbstractValidator<UpdateServerTypeCommand>
{
    private readonly IServerTypeRepository _serverTypeRepository;

    public UpdateServerTypeCommandValidator(IServerTypeRepository serverTypeRepository)
    {
        _serverTypeRepository = serverTypeRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 30).WithMessage("{PropertyName} should contain between 3 to 30 characters.")
            .NotNull();

        RuleFor(y => y)
            .NotNull()
            .MustAsync(VerifyGuid)
            .WithMessage("Id is invalid");

        RuleFor(p => p)
            .MustAsync(IsServerTypeNameExist)
            .WithMessage("A same name already exists");
    }

    private Task<bool> VerifyGuid(UpdateServerTypeCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.Id, "ServerType Id");

        return Task.FromResult(true);
    }


    public async Task<bool> IsServerTypeNameExist(UpdateServerTypeCommand updateServerTypeCommand,
        CancellationToken cancellationToken)
    {
        return !await _serverTypeRepository.IsServerTypeNameExist(updateServerTypeCommand.Name,
            updateServerTypeCommand.Id);
    }
}