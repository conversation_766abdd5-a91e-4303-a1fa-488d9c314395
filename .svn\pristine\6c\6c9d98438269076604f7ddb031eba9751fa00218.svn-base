using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DataSyncJobFixture : IDisposable
{
    public List<DataSyncJob> DataSyncJobPaginationList { get; set; }
    public List<DataSyncJob> DataSyncJobList { get; set; }
    public DataSyncJob DataSyncJobDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string ReplicationId = "REPLICATION_123";
    public const string DataSyncOptionId = "DATASYNC_OPTION_123";
    public const string SiteId = "SITE_123";

    public ApplicationDbContext DbContext { get; private set; }

    public DataSyncJobFixture()
    {
        var fixture = new Fixture();

        DataSyncJobList = fixture.Create<List<DataSyncJob>>();

        DataSyncJobPaginationList = fixture.CreateMany<DataSyncJob>(20).ToList();

        DataSyncJobPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DataSyncJobPaginationList.ForEach(x => x.IsActive = true);
        DataSyncJobPaginationList.ForEach(x => x.ReplicationId = ReplicationId);
        DataSyncJobPaginationList.ForEach(x => x.DataSyncOptionId = DataSyncOptionId);
        DataSyncJobPaginationList.ForEach(x => x.SiteId = SiteId);

        DataSyncJobList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DataSyncJobList.ForEach(x => x.IsActive = true);
        DataSyncJobList.ForEach(x => x.ReplicationId = ReplicationId);
        DataSyncJobList.ForEach(x => x.DataSyncOptionId = DataSyncOptionId);
        DataSyncJobList.ForEach(x => x.SiteId = SiteId);

        DataSyncJobDto = fixture.Create<DataSyncJob>();
        DataSyncJobDto.ReferenceId = Guid.NewGuid().ToString();
        DataSyncJobDto.IsActive = true;
        DataSyncJobDto.ReplicationId = ReplicationId;
        DataSyncJobDto.DataSyncOptionId = DataSyncOptionId;
        DataSyncJobDto.SiteId = SiteId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
