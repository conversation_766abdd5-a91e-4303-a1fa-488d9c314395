﻿
using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller;

public class ZFSOracleFullDBControllerShould
{
    private readonly ZFSOracleFullDBController _controller;

    public ZFSOracleFullDBControllerShould()
    {
            
        _controller = new ZFSOracleFullDBController();
    }

    [Fact]
    public void List_Returns_ViewResult()
    {
            
        var result = _controller.List();

            
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.NotNull(viewResult);
    }
}