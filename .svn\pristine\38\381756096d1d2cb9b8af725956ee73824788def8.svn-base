using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class DataSyncOptionsRepository : BaseRepository<DataSyncOptions>, IDataSyncOptionsRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public DataSyncOptionsRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<bool> IsNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? await Entities.AnyAsync(e => e.Name.Equals(name))
            : (await Entities.Where(e => e.Name.Equals(name)).ToListAsync()).Unique(id);
    }

    public async Task<bool> IsDataSyncNameUnique(string name)
    {
        return await _dbContext.DataSyncOptions.AnyAsync(e => e.Name.Equals(name));
    }

    public override async Task<PaginatedResult<DataSyncOptions>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<DataSyncOptions> specification, string sortColumn, string sortOrder)
    {
        return await Entities.Specify(specification).DescOrderById()
            .Select(x=> new DataSyncOptions
            {
                Id=x.Id,
                ReferenceId=x.ReferenceId,
                Name=x.Name,
                ReplicationType=x.ReplicationType,
                Properties=x.Properties
            }).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
}