﻿namespace ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetByType;

public class MongoDbMonitorStatusDetailByTypeQueryHandler : IRequestHandler<MongoDbMonitorStatusDetailByTypeQuery,
    List<MongoDbMonitorStatusDetailByTypeVm>>
{
    private readonly IMapper _mapper;
    private readonly IMongoDbMonitorStatusRepository _mongoDbMonitorStatusRepository;

    public MongoDbMonitorStatusDetailByTypeQueryHandler(IMapper mapper,
        IMongoDbMonitorStatusRepository mongoDbMonitorStatusRepository)
    {
        _mapper = mapper;
        _mongoDbMonitorStatusRepository = mongoDbMonitorStatusRepository;
    }

    public async Task<List<MongoDbMonitorStatusDetailByTypeVm>> Handle(MongoDbMonitorStatusDetailByTypeQuery request,
        CancellationToken cancellationToken)
    {
        var mongoDbMonitorStatus = await _mongoDbMonitorStatusRepository.GetDetailByType(request.Type);

        return mongoDbMonitorStatus.Count <= 0
            ? new List<MongoDbMonitorStatusDetailByTypeVm>()
            : _mapper.Map<List<MongoDbMonitorStatusDetailByTypeVm>>(mongoDbMonitorStatus);
    }
}