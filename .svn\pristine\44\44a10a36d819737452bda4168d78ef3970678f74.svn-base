﻿namespace ContinuityPatrol.Application.Features.ApprovalMatrixTemplate.Command.Create;

public class CreateApprovalMatrixTemplateCommandHandler : IRequestHandler<CreateApprovalMatrixTemplateCommand,
    CreateApprovalMatrixTemplateResponse>
{
    private readonly IApprovalMatrixTemplateRepository _approvalMatrixTemplateRepository;
    private readonly IMapper _mapper;

    public CreateApprovalMatrixTemplateCommandHandler(IMapper mapper,
        IApprovalMatrixTemplateRepository approvalMatrixTemplateRepository)
    {
        _mapper = mapper;
        _approvalMatrixTemplateRepository = approvalMatrixTemplateRepository;
    }

    public async Task<CreateApprovalMatrixTemplateResponse> Handle(CreateApprovalMatrixTemplateCommand request,
        CancellationToken cancellationToken)
    {
        var approvalMatrix = _mapper.Map<Domain.Entities.ApprovalMatrixTemplate>(request);

        approvalMatrix = await _approvalMatrixTemplateRepository.AddAsync(approvalMatrix);

        var response = new CreateApprovalMatrixTemplateResponse
        {
            Message = Message.Create(nameof(Domain.Entities.ApprovalMatrixTemplate), approvalMatrix.Name)
                .Replace("ApprovalMatrixTemplate", "ApprovalMatrix Template"),
            Id = approvalMatrix.ReferenceId
        };

        return response;
    }
}