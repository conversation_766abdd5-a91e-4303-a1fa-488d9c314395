using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class InfraObjectFixture : IDisposable
{
    public List<InfraObject> InfraObjectPaginationList { get; set; }
    public List<InfraObject> InfraObjectList { get; set; }
    public InfraObject InfraObjectDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public InfraObjectFixture()
    {
        var fixture = new Fixture();

        InfraObjectList = fixture.Create<List<InfraObject>>();

        InfraObjectPaginationList = fixture.CreateMany<InfraObject>(20).ToList();

        InfraObjectPaginationList.ForEach(x => x.CompanyId = CompanyId);

        InfraObjectList.ForEach(x => x.CompanyId = CompanyId);

        InfraObjectDto = fixture.Create<InfraObject>();

        InfraObjectDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
