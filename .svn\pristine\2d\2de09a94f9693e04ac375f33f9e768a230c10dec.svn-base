﻿
@{
    ViewData["Title"] = "LicenseUtilizationReport";
}

@using ContinuityPatrol.Web.Areas.Report.Controllers;
@using DevExpress.AspNetCore
@using ContinuityPatrol.Web.Areas.Report.ReportTemplate

<style>
    .form-group {
        margin-left: 550px;
        margin-top: 60px;
    }
</style>
@section Styles
    {
    <link href="~/css/viewer.part.bundle.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/thirdparty.bundle.css" rel="stylesheet" asp-append-version="true" />
    }
@section HeaderScripts
    {
    <script src="~/js/common/thirdparty.bundle.js" asp-append-version="true"></script>
    <script src="~/js/common/viewer.part.bundle.js" asp-append-version="true"></script>
   }


<div class="card card-custom gutter-b">
    <div class="card-body p-0">
        <div id="reportContainer">
            @{
                var reportData = ViewData["LicenseUtilizationReportData"]?.ToString();
                var reportDataObj = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(reportData);
                string reportType = reportDataObj.LicenseType;
                if (reportType == "PONumber")
                {
                    @Html.DevExpress().WebDocumentViewer("LicenseUtilizationDocumentViewer").Height("1150px").Bind(new LicenseUtlizationReport(ViewData["LicenseUtilizationReportData"].ToString()))
                }
                else if (reportType == "BusinessService")
                {
                    @Html.DevExpress().WebDocumentViewer("LicenseUtilizationDocumentViewer").Height("1150px").Bind(new LicenseUtilizationOperationalServiceReport(ViewData["LicenseUtilizationReportData"].ToString()))
                }
            }

        </div>
    </div>
</div>

@*<div class="row mt-3">
    <div class="col">
        <img src="/img/logo/cplogo.svg" height="35" />
    </div>
    <div class="col text-end">
        <img src="/img/logo/pts_logo.png" height="35" />
    </div>
    <div class="col-12">
        <div class="bg-secondary rounded-0 text-light mt-1">
            <h6 class="Report-Header text-center">License Utlization Report</h6>
        </div>
    </div>
</div>
<div class="card">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-xl-6 p-0">
                <div class="card bg-light">
                    <div class="card-header text-primary fw-bold border-bottom">
                        License Utilization Summary
                    </div>
                    <div class="card-body">
                        <div class="row row-cols-lg-3 bg-light rounded">
                            <div class="border-end col">
                                <div class="d-flex justify-content-between mt-3">
                                    <div class="mb-2">
                                        <i class="cp-server fs-2"></i>
                                    </div>
                                    <span class="rounded-circle fs-6 fw-light badge "
                                          style="height: 30px; width: 30px; padding: 7px;background:#FF6150">32</span>
                                </div>
                                <div class="mb-2">Servers Configured</div>
                            </div>
                            <div class="col border-end">
                                <div class="d-flex justify-content-between  mt-3">
                                    <div class="mb-2">
                                        <i class="cp-database fs-2"></i>
                                    </div>
                                    <span class="rounded-circle fs-6 fw-light badge"
                                          style="height: 30px; width: 30px; padding: 7px;background:#5b65f6">18</span>
                                </div>
                                <div class="mb-2">Database Utilized</div>
                            </div>
                            <div class=" col">
                                <div class="d-flex justify-content-between mt-3">
                                    <div class="mb-2">
                                        <i class="cp-configure-settings fs-2"></i>
                                    </div>
                                    <span class="rounded-circle fs-6 fw-light badge bg-success"
                                          style="height: 30px; width: 30px; padding: 7px;background:#50C878">14</span>
                                </div>
                                <div class="mb-2">Application Utilized</div>
                            </div>

                        </div>
                    </div>
                </div>
               
            </div>
            <!-- chart Start -->
            <div class="col-xl-6">
                <div id="License_Utilization_Chart" style="width:100%; height:100%;"></div>
            </div>
            <!-- chart end --> 
            <div class="col-12">
                <div class=" bg-secondary rounded-0 text-light mt-1">
                    <h6 class="Report-Header text-center">DataBase License Report</h6>
                </div>
            </div>
         
        </div>
        <!-- table -->
       
           

     
           
        <div class="table_list row mt-1">
            <table class="reportsfont mt-3 table table-sm">
                <thead class="bg-light">
                    <tr style="color: black;">
                        <th class="col-1 text-center">Sr.No</th>
                        <th>Server Name</th>
                        <th>ServerType</th>
                        <th>Server IP Address</th>
                        <th>DBSr.No</th>
                        <th>Database Name</th>
                        <th>Database Type</th>
                        <th>Database SID</th>
                        <th>Created Date</th>
                    </tr>
                </thead>
                <tbody>
                    <tr style="color: black;">
                        <th class="col-1 text-center">1</th>
                        <td>BODS_DB_DR_1_145</td>
                        <td>DRDBServer</td>
                        <td>************</td>
                        <td>1</td>
                        <td>BODS_DRDB_1_145</td>
                        <td>Oracle</td>
                        <td>TBP</td>
                        <td>09-06-2022 13:31:41</td>
                    </tr>
                    <tr style="color: black;">
                        <th class="col-1 text-center">2</th>
                        <td>BODS_DB_DR_1_145</td>
                        <td>DRDBServer</td>
                        <td>************</td>
                        <td>1</td>
                        <td>BODS_DRDB_1_145</td>
                        <td>Oracle</td>
                        <td>TBP</td>
                        <td>09-06-2022 13:31:41</td>
                    </tr>
                  
                    <tr style="color: black;">
                        <th class="col-1 text-center">3</th>
                        <td>BODS_DB_DR_1_145</td>
                        <td>DRDBServer</td>
                        <td>************</td>
                        <td>1</td>
                        <td>BODS_DRDB_1_145</td>
                        <td>Oracle</td>
                        <td>TBP</td>
                        <td>09-06-2022 13:31:41</td>
                    </tr>
                    <tr style="color: black;">
                        <th class="col-1 text-center">4</th>
                        <td>BODS_DB_DR_1_145</td>
                        <td>DRDBServer</td>
                        <td>************</td>
                        <td>1</td>
                        <td>BODS_DRDB_1_145</td>
                        <td>Oracle</td>
                        <td>TBP</td>
                        <td>09-06-2022 13:31:41</td>
                    </tr>
                  
                    <tr style="color: black;">
                        <th class="col-1 text-center">6</th>
                        <td>BODS_DB_DR_1_145</td>
                        <td>DRDBServer</td>
                        <td>************</td>
                        <td>1</td>
                        <td>BODS_DRDB_1_145</td>
                        <td>Oracle</td>
                        <td>TBP</td>
                        <td>09-06-2022 13:31:41</td>
                    </tr>
                    <tr style="color: black;">
                        <th class="col-1 text-center">7</th>
                        <td>BODS_DB_DR_1_145</td>
                        <td>DRDBServer</td>
                        <td>************</td>
                        <td>1</td>
                        <td>BODS_DRDB_1_145</td>
                        <td>Oracle</td>
                        <td>TBP</td>
                        <td>09-06-2022 13:31:41</td>
                    </tr>
                    <tr style="color: black;">
                        <th class="col-1 text-center">8</th>
                        <td>BODS_DB_DR_1_145</td>
                        <td>DRDBServer</td>
                        <td>************</td>
                        <td>1</td>
                        <td>BODS_DRDB_1_145</td>
                        <td>Oracle</td>
                        <td>TBP</td>
                        <td>09-06-2022 13:31:41</td>
                    </tr>
                    <tr style="color: black;">
                        <th class="col-1 text-center">9</th>
                        <td>BODS_DB_DR_1_145</td>
                        <td>DRDBServer</td>
                        <td>************</td>
                        <td>1</td>
                        <td>BODS_DRDB_1_145</td>
                        <td>Oracle</td>
                        <td>TBP</td>
                        <td>09-06-2022 13:31:41</td>
                    </tr>
                    <tr style="color: black;">
                        <th class="col-1 text-center">10</th>
                        <td>BODS_DB_DR_1_145</td>
                        <td>DRDBServer</td>
                        <td>************</td>
                        <td>1</td>
                        <td>BODS_DRDB_1_145</td>
                        <td>Oracle</td>
                        <td>TBP</td>
                        <td>09-06-2022 13:31:41</td>
                    </tr>
                   
                </tbody>
            </table>
        </div>
    </div>
</div>

<script src="~/js/report-charts/license_utilization.js"></script>*@