﻿namespace ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetNameUnique;

public class GetReplicationMasterNameUniqueQueryHandler : IRequestHandler<GetReplicationMasterNameUniqueQuery, bool>
{
    private readonly IReplicationMasterRepository _replicationMasterRepository;

    public GetReplicationMasterNameUniqueQueryHandler(IReplicationMasterRepository replicationMasterRepository)
    {
        _replicationMasterRepository = replicationMasterRepository;
    }

    public async Task<bool> Handle(GetReplicationMasterNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _replicationMasterRepository.IsReplicationMasterNameExist(request.ReplicationMasterName,
            request.ReplicationMasterId);
    }
}