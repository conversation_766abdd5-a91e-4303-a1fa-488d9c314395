﻿using ContinuityPatrol.Web.Areas.Report.Controllers;
using Newtonsoft.Json;
using System.Drawing;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Application.Features.Report.Queries.GetRPOSLAReport.Models;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    [SupportedOSPlatform("windows")]
    public partial class RPOSLASRMReport : DevExpress.XtraReports.UI.XtraReport
    {
        public static string username;
        public static TimeSpan ConfigRPO;
        private static TimeSpan Threshold;
        private static Int64 ConfiguredRPO;
        private static Int64 ConfiguredThreshold;
        private string RportDateOption;
        private List<GetRPOSLASRMReportVM> ReportList = new List<GetRPOSLASRMReportVM>();
        public  GetRPOSLASRMBusinessServiceDetails ReportData = new GetRPOSLASRMBusinessServiceDetails();
        private readonly ILogger<PreBuildReportController> _logger;
        
        private class SpDatalag
        {
            public TimeSpan Datalag { get; set; }
            public int TimeStamp { get; set; }
            public string Date { get; set; }
        }
        public RPOSLASRMReport(string data)
        {
            try
            {
                _logger = PreBuildReportController._logger;

                InitializeComponent();
                ClientCompanyLogo();
                
                ReportData = JsonConvert.DeserializeObject<GetRPOSLASRMBusinessServiceDetails>(data);
                
                ReportList = ReportData.GetRPOSLASRMReportVM;

                RportDateOption = ReportData.DateOption;

                this.DataSource = ReportList;
                
                var infraObjectName = !string.IsNullOrEmpty(ReportData.InfraObjectName)
                    ? ReportData.InfraObjectName
                    : "-";
                this.DisplayName = "RPOSLAReport_" + infraObjectName + "_" + DateTime.Now.ToString("ddMMyyyy" + "_" + "hhmmsstt");
                lblInfra.Text = infraObjectName;

                lblBusiness.Text = !string.IsNullOrEmpty(ReportData.BusinessServiceName)
                    ? ReportData.BusinessServiceName
                    : "-";
                lblPRIP.Text = !string.IsNullOrEmpty(ReportData.PRIPAddress)
                    ? ReportData.PRIPAddress
                    : "-";
                lblDRIP.Text = !string.IsNullOrEmpty(ReportData.DRIPAddress)
                    ? ReportData.DRIPAddress
                    : "-";
                username = ReportData.ReportGeneratedBy;

                ConfiguredRPO = ReportList.LastOrDefault().ConfigureRPO.ToInt64();
                ConfiguredThreshold = ReportList.LastOrDefault().Threshold.ToInt64();

                ConfigRPO = TimeSpan.FromMinutes(ConfiguredRPO);
                Threshold = TimeSpan.FromMinutes(ConfiguredThreshold);

                //lblConfiguredRPO.Text = "Configured RPO(" + ConfiguredRPO.ToString() + " Mins)";
                //lblThreshold.Text = "Threshold Exceeded(" + ConfiguredThreshold.ToString() + " Mins)";

                lblUnderThreshold.Text = "DataLag ≤ " + Threshold + " (Threshold)";
                lblDatalagExceed.Text = "DataLag > " + ConfigRPO + " (Configured RPO)";
                lblThresholdExceed.Text = "(Threshold) " + Threshold + " < Threshold Exceeded ≤ " + ConfigRPO + " (Configured RPO)";

                var fromdate = ReportData.FromDate;
                var todate = ReportData.ToDate;
                DateTime reportFromdate = DateTime.Parse(fromdate);
                DateTime reportTodate = DateTime.Parse(todate);
                lblfromdate.Text = reportFromdate.ToString("dd/MM/yyyy");
                lbltodate.Text = reportTodate.ToString("dd/MM/yyyy");

            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RPOSLASRM Report. The error message : " + ex.Message); throw; }

        }
        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try { 
            _username.Text = "Report Generated By: " + username;
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RPOSLASRM Report's User Name. The error message : " + ex.Message); throw; }
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try { 
            var builder = new ConfigurationBuilder()
                 .SetBasePath(Directory.GetCurrentDirectory())
                 .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            IConfigurationRoot configuration = builder.Build();
            var version = configuration["CP:Version"];
            xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RPOSLASRM Report's CP Version. The error message : " + ex.Message); throw; }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the RPOSLASRM Report's customer logo" + ex.Message.ToString());
            }
        }
    }
}
