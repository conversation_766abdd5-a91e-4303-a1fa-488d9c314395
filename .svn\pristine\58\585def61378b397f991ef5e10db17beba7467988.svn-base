using ContinuityPatrol.Application.Features.DriftParameter.Events.Create;

namespace ContinuityPatrol.Application.Features.DriftParameter.Commands.Create;

public class
    CreateDriftParameterCommandHandler : IRequestHandler<CreateDriftParameterCommand, CreateDriftParameterResponse>
{
    private readonly IDriftParameterRepository _driftParameterRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateDriftParameterCommandHandler(IMapper mapper, IDriftParameterRepository driftParameterRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _driftParameterRepository = driftParameterRepository;
    }

    public async Task<CreateDriftParameterResponse> Handle(CreateDriftParameterCommand request,
        CancellationToken cancellationToken)
    {
        var driftParameter = _mapper.Map<Domain.Entities.DriftParameter>(request);

        driftParameter = await _driftParameterRepository.AddAsync(driftParameter);

        var response = new CreateDriftParameterResponse
        {
            Message = Message.Create("Drift Parameter", driftParameter.Name),

            Id = driftParameter.ReferenceId
        };

        await _publisher.Publish(new DriftParameterCreatedEvent { Name = driftParameter.Name }, cancellationToken);

        return response;
    }
}