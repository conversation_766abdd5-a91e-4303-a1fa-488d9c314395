﻿//namespace ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDRReadyReport;

//public class DrReadyStatusForDrReadyReportQueryHandler : IRequestHandler<DRReadyStatusForDRReadyReportQuery, List<DrReadyStatusForDrReadyReportVm>>
//{
//    private readonly IDRReadyStatusRepository _dRReadyStatusRepository;
//    private readonly IHeatMapStatusRepository _heatMapStatusRepository;
//    private readonly IMapper _mapper;

//    public DrReadyStatusForDrReadyReportQueryHandler(IMapper mapper, IDRReadyStatusRepository dRReadyStatusRepository, IHeatMapStatusRepository heatMapStatusRepository)
//    {
//        _mapper = mapper;
//        _dRReadyStatusRepository = dRReadyStatusRepository;
//        _heatMapStatusRepository = heatMapStatusRepository;
//    }

//    public async Task<List<DrReadyStatusForDrReadyReportVm>> Handle(DRReadyStatusForDRReadyReportQuery request, CancellationToken cancellationToken)
//    {
//        var drReadyStatusList = request.BusinessServiceId.IsNotNullOrWhiteSpace()
//            ? await _dRReadyStatusRepository.GetDrReadyStatusByBusinessServiceId(request.BusinessServiceId) 
//            : await _dRReadyStatusRepository.ListAllAsync();

//        var removeDuplicateInfra = drReadyStatusList?.DistinctBy(x=>x?.InfraObjectId).ToList();

//        var drReadyReport = _mapper.Map<List<DrReadyStatusForDrReadyReportVm>>(removeDuplicateInfra);

//        foreach(var infra in drReadyReport)
//        {
//            int infraDownCount =0;
//            int infraUpCount =0;

//            var infraObjects = await _heatMapStatusRepository.GetHeatMapByInfraObjectId(infra.InfraObjectId);

//            var infraTotalCount = infraObjects?.DistinctBy(x=>x?.InfraObjectId).ToList();

//            foreach(var infra in infraTotalCount)
//            {
//                var infraObject =await _heatMapStatusRepository.GetHeatMapByInfraObjectId(infra.InfraObjectId);

//                var downCount = infraObject?.Count(x => x.HeatmapStatus.ToLower().Trim().Equals("down"));

//                //(infraDownCount, infraUpCount) = downCount == 0 ? (infraDownCount + 1, infraUpCount) : (infraDownCount, infraUpCount + 1);


//                //var downCount = infraObject?.Count(x => x.HeatmapStatus.ToLower().Trim().Equals("down"));

//                if (downCount == 0)
//                {
//                    infraUpCount +=1;
//                }
//                else
//                {
//                    infraDownCount += 1;
//                }

//            };

//            //var infraDetails = _mapper.Map<List<InfraObjectCountList>>(infra);

//            var infraDetails = new InfraObjectCountList
//            {
//                BusinessServiceId = infra?.BusinessServiceId,
//                BusinessFunctionId = infra?.BusinessFunctionId,
//                BusinessFunctionName = infra?.BusinessFunctionName,
//                InfraObjectId = infra?.InfraObjectId,
//                UpCount = infraUpCount,
//                DownCount = infraDownCount,
//                TotalCount = infraTotalCount.Count,
//            };
//            infra.InfraObjectCountLists.Add(infraDetails);
//        };

//        return drReadyReport;
//    }
//}

