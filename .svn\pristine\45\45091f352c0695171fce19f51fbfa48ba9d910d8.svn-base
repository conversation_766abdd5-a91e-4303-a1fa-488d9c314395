﻿using ContinuityPatrol.Application.Features.WorkflowHistory.Events.Create;

namespace ContinuityPatrol.Application.Features.WorkflowHistory.Commands.Create;

public class
    CreateWorkflowHistoryCommandHandler : IRequestHandler<CreateWorkflowHistoryCommand, CreateWorkflowHistoryResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IWorkflowHistoryRepository _workflowHistoryRepository;

    public CreateWorkflowHistoryCommandHandler(IMapper mapper, IWorkflowHistoryRepository workflowHistoryRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _workflowHistoryRepository = workflowHistoryRepository;
        _publisher = publisher;
    }

    public async Task<CreateWorkflowHistoryResponse> Handle(CreateWorkflowHistoryCommand request,
        CancellationToken cancellationToken)
    {
        var workflowHistory = _mapper.Map<Domain.Entities.WorkflowHistory>(request);

        workflowHistory = await _workflowHistoryRepository.AddAsync(workflowHistory);

        var response = new CreateWorkflowHistoryResponse
        {
            Message = Message.Create(nameof(Domain.Entities.WorkflowHistory), workflowHistory.WorkflowName),
            Id = workflowHistory.ReferenceId
        };

        await _publisher.Publish(new WorkflowHistoryCreatedEvent { WorkflowHistoryName = workflowHistory.WorkflowName },
            cancellationToken);

        return response;
    }
}