using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.AdPasswordJobModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordJob.Queries;

public class GetAdPasswordJobPaginatedListQueryTests : IClassFixture<AdPasswordJobFixture>
{
    private readonly AdPasswordJobFixture _adPasswordJobFixture;
    private readonly Mock<IAdPasswordJobRepository> _mockAdPasswordJobRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetAdPasswordJobPaginatedListQueryHandler _handler;

    public GetAdPasswordJobPaginatedListQueryTests(AdPasswordJobFixture adPasswordJobFixture)
    {
        _adPasswordJobFixture = adPasswordJobFixture;

        _mockAdPasswordJobRepository = AdPasswordJobRepositoryMocks.CreateQueryAdPasswordJobRepository(_adPasswordJobFixture.AdPasswordJobs);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper for individual entities
        _mockMapper.Setup(m => m.Map<AdPasswordJobListVm>(It.IsAny<Domain.Entities.AdPasswordJob>()))
            .Returns((Domain.Entities.AdPasswordJob entity) => new AdPasswordJobListVm
            {
                Id = entity.ReferenceId,
                DomainServerId = entity.DomainServerId,
                DomainServer = entity.DomainServer,
                State = entity.State,
                IsSchedule = entity.IsSchedule,
                ScheduleType = entity.ScheduleType,
                CronExpression = entity.CronExpression,
                ScheduleTime = entity.ScheduleTime,
                NodeId = entity.NodeId,
                NodeName = entity.NodeName,
                ExceptionMessage = entity.ExceptionMessage
            });

        _handler = new GetAdPasswordJobPaginatedListQueryHandler(
            _mockMapper.Object,
            _mockAdPasswordJobRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_PaginatedResult_When_AdPasswordJobsExist()
    {
        // Arrange
        var query = new GetAdPasswordJobPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = ""
        };

        Assert.NotNull(query);
        Assert.Equal(1, query.PageNumber);
    }

    [Fact]
    public async Task Handle_Call_GetPaginatedQuery_OnlyOnce()
    {
        // Arrange
        var query = new GetAdPasswordJobPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockAdPasswordJobRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_ForEachEntity()
    {
        // Arrange
        var query = new GetAdPasswordJobPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<AdPasswordJobListVm>(It.IsAny<Domain.Entities.AdPasswordJob>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_PassCorrectParameters_When_CallingPagination()
    {
        // Arrange
        var query = new GetAdPasswordJobPaginatedListQuery
        {
            PageNumber = 2,
            PageSize = 5,
            SearchString = "test"
        };

        var result = await _handler.Handle(new GetAdPasswordJobPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "LKJH" }, CancellationToken.None);

        result.CurrentPage.ShouldBe(2);
        result.PageSize.ShouldBe(5);
    }

    [Fact]
    public async Task Handle_ReturnCorrectPaginationInfo_When_QueryExecuted()
    {
        // Arrange
        var query = new GetAdPasswordJobPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 2
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.CurrentPage.ShouldBe(1);
        result.PageSize.ShouldBe(2);
        result.TotalCount.ShouldBeGreaterThan(0);
        result.TotalPages.ShouldBeGreaterThan(0);
    }

    [Fact]
    public async Task Handle_MapEntitiesToViewModels_WithCorrectProperties()
    {
        // Arrange
        var testJob = _adPasswordJobFixture.AdPasswordJobs.First();
        testJob.DomainServerId = "DS001";
        testJob.DomainServer = "TestDomain.com";
        testJob.State = "Active";
        testJob.IsSchedule = 1;
        testJob.ScheduleType = 1;

        var query = new GetAdPasswordJobPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Data.ShouldNotBeNull();
        result.Data.Count.ShouldBeGreaterThan(0);
        
        var firstItem = result.Data.First();
        firstItem.Id.ShouldBe(testJob.ReferenceId);
        firstItem.DomainServerId.ShouldBe("DS001");
        firstItem.DomainServer.ShouldBe("TestDomain.com");
        firstItem.State.ShouldBe("Active");
        firstItem.IsSchedule.ShouldBe(1);
        firstItem.ScheduleType.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_ReturnEmptyData_When_NoMatchingRecords()
    {
        // Arrange
        var query = new GetAdPasswordJobPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "NonExistentSearchTerm"
        };

        var emptyQueryable = new List<Domain.Entities.AdPasswordJob>().AsQueryable();
        _mockAdPasswordJobRepository.Setup(x => x.GetPaginatedQuery())
            .Returns(emptyQueryable);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Data.Count.ShouldBe(0);
        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_HandleDifferentPageSizes_When_QueryExecuted()
    {
        // Arrange
        var query = new GetAdPasswordJobPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 1
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.PageSize.ShouldBe(1);
        result.Data.Count.ShouldBeLessThanOrEqualTo(1);
    }

    [Fact]
    public async Task Handle_CreateFilterSpecification_When_SearchStringProvided()
    {
        // Arrange
        var query = new GetAdPasswordJobPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "TestSearch"
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockAdPasswordJobRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }

    [Fact]
    public async Task Handle_MapScheduleProperties_WithCorrectValues()
    {
        // Arrange
        var testJob = _adPasswordJobFixture.AdPasswordJobs.First();
        testJob.IsSchedule = 1;
        testJob.ScheduleType = 2;
        testJob.CronExpression = "0 30 14 * * ?";
        testJob.ScheduleTime = "14:30:00";

        var query = new GetAdPasswordJobPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        var firstItem = result.Data.First();
        firstItem.IsSchedule.ShouldBe(1);
        firstItem.ScheduleType.ShouldBe(2);
        firstItem.CronExpression.ShouldBe("0 30 14 * * ?");
        firstItem.ScheduleTime.ShouldBe("14:30:00");
    }

    [Fact]
    public async Task Handle_MapNodeProperties_WithCorrectValues()
    {
        // Arrange
        var testJob = _adPasswordJobFixture.AdPasswordJobs.First();
        testJob.NodeId = "Node123";
        testJob.NodeName = "ProductionNode";
        testJob.ExceptionMessage = "Test exception message";

        var query = new GetAdPasswordJobPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        var firstItem = result.Data.First();
        firstItem.NodeId.ShouldBe("Node123");
        firstItem.NodeName.ShouldBe("ProductionNode");
        firstItem.ExceptionMessage.ShouldBe("Test exception message");
    }

    [Fact]
    public async Task Handle_HandleEmptySearchString_When_QueryExecuted()
    {
        // Arrange
        var query = new GetAdPasswordJobPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = ""
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Data.Count.ShouldBeGreaterThan(0);
        _mockAdPasswordJobRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}
