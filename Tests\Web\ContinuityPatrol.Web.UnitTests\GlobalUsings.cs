global using Xunit;
global using Newtonsoft.Json;
global using System.Text;
global using MediatR;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.AspNetCore.Mvc.Rendering;
global using Microsoft.Extensions.Caching.Distributed;
global using Microsoft.Extensions.Logging;
global using Moq;
global using Shouldly;
global using AutoMapper;
global using Microsoft.AspNetCore.Authentication;
global using Microsoft.AspNetCore.Authentication.Cookies;
global using Microsoft.AspNetCore.Http;
global using Microsoft.Extensions.DependencyInjection;
global using System.Security.Claims;
global using Microsoft.AspNetCore.Mvc.ViewFeatures;
global using Microsoft.AspNetCore.Diagnostics;
global using Microsoft.AspNetCore.Builder;
global using System.Net;
global using FluentAssertions;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.Diagnostics.HealthChecks;
global using System.Text.Json;
global using Microsoft.AspNetCore.Razor.TagHelpers;
global using AutoFixture;