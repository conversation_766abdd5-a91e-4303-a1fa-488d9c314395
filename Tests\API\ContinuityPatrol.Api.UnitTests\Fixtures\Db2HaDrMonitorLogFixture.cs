using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Commands.Create;
using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Queries.GetByType;
using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Queries.GetList;
using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class Db2HaDrMonitorLogFixture : IDisposable
{
    public CreateDB2HADRMonitorLogCommand CreateDB2HADRMonitorLogCommand { get; }
    public CreateDB2HADRMonitorLogResponse CreateDB2HADRMonitorLogResponse { get; }
    public GetDB2HADRMonitorLogDetailQuery GetDB2HADRMonitorLogDetailQuery { get; }
    public DB2HADRMonitorLogDetailVm DB2HADRMonitorLogDetailVm { get; }
    public GetDB2HADRMonitorLogListQuery GetDB2HADRMonitorLogListQuery { get; }
    public List<DB2HADRMonitorLogListVm> DB2HADRMonitorLogListVm { get; }
    public GetDB2HADRMonitorLogPaginatedListQuery GetDB2HADRMonitorLogPaginatedListQuery { get; }
    public PaginatedResult<DB2HADRMonitorLogPaginatedListVm> DB2HADRMonitorLogPaginatedResult { get; }
    public GetDB2HADRMonitorLogDetailByTypeQuery GetDB2HADRMonitorLogDetailByTypeQuery { get; }
    public DB2HADRMonitorLogDetailByTypeVm DB2HADRMonitorLogDetailByTypeVm { get; }

    public Db2HaDrMonitorLogFixture()
    {
        var fixture = new Fixture();

        // Configure fixture for enterprise scenarios
        fixture.Customize<CreateDB2HADRMonitorLogCommand>(c => c
            .With(x => x.Type, "Enterprise DB2 HADR")
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectName, "Enterprise DB2 Primary Database")
            .With(x => x.WorkflowId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, "Enterprise HADR Monitoring Workflow")
            .With(x => x.ConfiguredRPO, "15")
            .With(x => x.DataLagValue, "5")
            .With(x => x.Threshold, "30")
            .With(x => x.Properties, @"{
                ""primaryServer"": ""PROD-DB2-01"",
                ""standbyServer"": ""PROD-DB2-02"",
                ""replicationMode"": ""SYNC"",
                ""compressionEnabled"": true,
                ""encryptionEnabled"": true,
                ""monitoringInterval"": 60
            }"));

        fixture.Customize<CreateDB2HADRMonitorLogResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise DB2 HADR Monitor Log created successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DB2HADRMonitorLogDetailVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Type, "Enterprise DB2 HADR Detail")
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectName, "Enterprise DB2 Primary Database Detail")
            .With(x => x.WorkflowId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, "Enterprise HADR Detail Workflow")
            .With(x => x.ConfiguredRPO, "15")
            .With(x => x.DataLagValue, "3")
            .With(x => x.Properties, @"{
                ""detailLevel"": ""comprehensive"",
                ""alertThreshold"": ""critical"",
                ""backupStrategy"": ""continuous"",
                ""recoveryPoint"": ""minimal""
            }"));

        fixture.Customize<DB2HADRMonitorLogListVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Type, "Enterprise DB2 HADR List")
            .With(x => x.InfraObjectName, "Enterprise DB2 List Database")
            .With(x => x.WorkflowName, "Enterprise HADR List Workflow")
            .With(x => x.ConfiguredRPO, "10")
            .With(x => x.DataLagValue, "2"));

        fixture.Customize<DB2HADRMonitorLogPaginatedListVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Type, "Enterprise DB2 HADR Paginated")
            .With(x => x.InfraObjectName, "Enterprise DB2 Paginated Database")
            .With(x => x.WorkflowName, "Enterprise HADR Paginated Workflow")
            .With(x => x.ConfiguredRPO, "20")
            .With(x => x.DataLagValue, "7"));

        fixture.Customize<DB2HADRMonitorLogDetailByTypeVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Type, "Enterprise DB2 HADR Type")
            .With(x => x.InfraObjectName, "Enterprise DB2 Type Database")
            .With(x => x.WorkflowName, "Enterprise HADR Type Workflow")
            .With(x => x.ConfiguredRPO, "12")
            .With(x => x.DataLagValue, "4"));

        fixture.Customize<GetDB2HADRMonitorLogPaginatedListQuery>(c => c
            .With(x => x.SearchString, "Enterprise")
            .With(x => x.PageNumber, 1)
            .With(x => x.PageSize, 10)
            .With(x => x.SortColumn, "InfraObjectName")
            .With(x => x.SortOrder, "ASC"));

        // Create instances
        CreateDB2HADRMonitorLogCommand = fixture.Create<CreateDB2HADRMonitorLogCommand>();
        CreateDB2HADRMonitorLogResponse = fixture.Create<CreateDB2HADRMonitorLogResponse>();
        
        GetDB2HADRMonitorLogDetailQuery = new GetDB2HADRMonitorLogDetailQuery { Id = Guid.NewGuid().ToString() };
        DB2HADRMonitorLogDetailVm = fixture.Create<DB2HADRMonitorLogDetailVm>();
        
        GetDB2HADRMonitorLogListQuery = new GetDB2HADRMonitorLogListQuery();
        DB2HADRMonitorLogListVm = fixture.CreateMany<DB2HADRMonitorLogListVm>(3).ToList();
        
        GetDB2HADRMonitorLogPaginatedListQuery = fixture.Create<GetDB2HADRMonitorLogPaginatedListQuery>();
        DB2HADRMonitorLogPaginatedResult = new PaginatedResult<DB2HADRMonitorLogPaginatedListVm>
        {
            Data = fixture.CreateMany<DB2HADRMonitorLogPaginatedListVm>(5).ToList(),
            TotalCount = 5,
            PageSize = 10,
            Succeeded = true
        };
        
        GetDB2HADRMonitorLogDetailByTypeQuery = new GetDB2HADRMonitorLogDetailByTypeQuery { Type = "Enterprise DB2 HADR" };
        DB2HADRMonitorLogDetailByTypeVm = fixture.Create<DB2HADRMonitorLogDetailByTypeVm>();
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
