﻿using ContinuityPatrol.Application.Features.User.Queries.GetHasUser;

namespace ContinuityPatrol.Application.UnitTests.Features.User.Queries
{
    public class GetHasUserQueryHandlerTests
    {
        [Fact]
        public async Task Handle_ReturnsTrue_WhenUsersExist()
        {
            var mockUserRepository = new Mock<IUserRepository>();
            mockUserRepository
                .Setup(repo => repo.GetUserNames())
                .ReturnsAsync(new List<Domain.Entities.User> { new Domain.Entities.User(), new Domain.Entities.User() });

            var handler = new GetHasUserQueryHandler(mockUserRepository.Object);
            var query = new GetHasUserQuery();
            var cancellationToken = CancellationToken.None;

            var result = await handler.Handle(query, cancellationToken);

            Assert.True(result);
            mockUserRepository.Verify(repo => repo.GetUserNames(), Times.Once);
        }

        [Fact]
        public async Task Handle_ReturnsFalse_WhenNoUsersExist()
        {
            var mockUserRepository = new Mock<IUserRepository>();
            mockUserRepository
                .Setup(repo => repo.GetUserNames())
                .ReturnsAsync(new List<Domain.Entities.User>());

            var handler = new GetHasUserQueryHandler(mockUserRepository.Object);
            var query = new GetHasUserQuery();
            var cancellationToken = CancellationToken.None;

            var result = await handler.Handle(query, cancellationToken);

            Assert.False(result);
            mockUserRepository.Verify(repo => repo.GetUserNames(), Times.Once);
        }
    }
}
