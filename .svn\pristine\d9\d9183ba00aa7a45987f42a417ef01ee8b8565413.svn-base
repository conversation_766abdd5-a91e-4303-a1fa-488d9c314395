using AutoFixture;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;
using Job = ContinuityPatrol.Domain.Entities.Job;
using Template = ContinuityPatrol.Domain.Entities.Template;
using ComponentType = ContinuityPatrol.Domain.Entities.ComponentType;
using GroupPolicy = ContinuityPatrol.Domain.Entities.GroupPolicy;
using System.Threading;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class JobRepositoryTests : IClassFixture<JobFixture>, IDisposable
{
    private readonly JobFixture _jobFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly JobRepository _repository;
    private readonly JobRepository _repositoryNotParent;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public JobRepositoryTests(JobFixture jobFixture)
    {
        _jobFixture = jobFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        
        // Setup different repository configurations
        _repository = new JobRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new JobRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    private static ILoggedInUserService GetMockLoggedInUserIsNotAllInfra()
    {
        var mock = new Mock<ILoggedInUserService>();
        mock.Setup(x => x.CompanyId).Returns("COMPANY_123");
        mock.Setup(x => x.UserId).Returns("USER_456");
        mock.Setup(x => x.IsParent).Returns(false);
        mock.Setup(x => x.IsAllInfra).Returns(false);
        mock.Setup(x => x.IsAuthenticated).Returns(true);
        mock.Setup(x => x.AssignedInfras).Returns("{}");
        return mock.Object;
    }

    private async Task ClearDatabase()
    {
        _dbContext.Jobs.RemoveRange(_dbContext.Jobs);
        _dbContext.Templates.RemoveRange(_dbContext.Templates);
        _dbContext.ComponentTypes.RemoveRange(_dbContext.ComponentTypes);
        _dbContext.GroupPolicies.RemoveRange(_dbContext.GroupPolicies);
        await _dbContext.SaveChangesAsync();
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        await ClearDatabase();
        var job = _jobFixture.JobDto;

        // Act
        var result = await _repository.AddAsync(job);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(job.Name, result.Name);
        Assert.Equal(job.CompanyId, result.CompanyId);
        Assert.Single(_dbContext.Jobs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        await ClearDatabase();
        var job = _jobFixture.JobDto;
        job.ReferenceId = "580bd592-16bc-4939-bc5b-b1a0c89ed07c";

        await _repository.AddAsync(job);

        // Act
        job.Name = "UpdatedJobName";
        job.Status = "Updated";
        await _repository.UpdateAsync(job);

        // Assert
        var updatedJob = await _repository.GetByReferenceIdAsync(job.ReferenceId);
        Assert.NotNull(updatedJob);
        Assert.Equal("UpdatedJobName", updatedJob.Name);
        Assert.Equal("Updated", updatedJob.Status);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests


    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var job = _jobFixture.JobDto;
        await _repository.AddAsync(job);

        // Act
        var result = await _repository.GetByIdAsync(job.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(job.Id, result.Id);
        Assert.Equal(job.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var job = _jobFixture.JobDto; 
        job.ReferenceId = "580bd592-16bc-4939-bc5b-b1a0c89ed07c";

        await _repository.AddAsync(job);

        // Act
        var result = await _repository.GetByReferenceIdAsync(job.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(job.ReferenceId, result.ReferenceId);
        Assert.Equal(job.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldFilterByCompanyId()
    {
        // Arrange

        await ClearDatabase();
        var job = _jobFixture.JobDto;
        job.CompanyId = "DIFFERENT_COMPANY";
       job.ReferenceId = "580bd592-16bc-4939-bc5b-b1a0c89ed07c";
        await _repository.AddAsync(job);
        _dbContext.SaveChanges();
        // Act
        var result = await _repositoryNotParent.GetByReferenceIdAsync(job.ReferenceId);

        // Assert
        Assert.Null(result); // Should not return job from different company
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_FilteredByCompanyId()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
         Assert.NotNull(result);
        Assert.Equal(jobs.Count, result.Count);
        //Assert.All(result, x => Assert.Equal(JobFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmpty_WhenNoEntities()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsJobNameUnique Tests

    [Fact]
    public async Task IsJobNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();
        var job = _jobFixture.JobDto;
        await _repository.AddAsync(job);

        // Act
        var result = await _repository.IsJobNameUnique(job.Name);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsJobNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsJobNameUnique("NonExistentJobName");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsJobNameUnique_ShouldHandleNullOrEmptyName()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result1 = await _repository.IsJobNameUnique(null);
        var result2 = await _repository.IsJobNameUnique("");

        // Assert
        Assert.False(result1);
        Assert.False(result2);
    }

    #endregion

    #region IsJobNameExist Tests

    [Fact]
    public async Task IsJobNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var job = _jobFixture.JobDto;
        await _repository.AddAsync(job);

        // Act
        var result = await _repository.IsJobNameExist(job.Name, "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsJobNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsJobNameExist("NonExistentJobName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsJobNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        await ClearDatabase();
        var job = _jobFixture.JobDto;
        await _repository.AddAsync(job);

        // Act
        var result = await _repository.IsJobNameExist(job.Name, job.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetBySolutionTypeId Tests

    //[Fact]
    //public async Task GetBySolutionTypeId_ShouldReturnMatchingJobs()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var jobs = _jobFixture.JobList;
    //    var solutionTypeId = "SOLUTION_TYPE_123";
    //    jobs.ForEach(x => x.SolutionTypeId = solutionTypeId);
    //    await _repository.AddRangeAsync(jobs);

    //    // Act
    //    var result = await _repository.GetBySolutionTypeId(solutionTypeId);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Equal(jobs.Count, result.Count);
    //    Assert.All(result, x => Assert.Equal(solutionTypeId, x.SolutionTypeId));
    //}

    [Fact]
    public async Task GetBySolutionTypeId_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetBySolutionTypeId("NON_EXISTENT_SOLUTION_TYPE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    //[Fact]
    //public async Task GetBySolutionTypeId_ShouldFilterByCompanyId()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var jobs = _jobFixture.JobList;
    //    var solutionTypeId = "SOLUTION_TYPE_123";
    //    jobs.ForEach(x =>
    //    {
    //        x.SolutionTypeId = solutionTypeId;
    //        x.CompanyId = JobFixture.CompanyId;
    //    });
    //    await _repository.AddRangeAsync(jobs);

    //    // Act
    //    var result = await _repository.GetBySolutionTypeId(solutionTypeId);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.All(result, x => Assert.Equal(JobFixture.CompanyId, x.CompanyId));
    //}

    #endregion

    #region GetInfraObjectPropertyBySolutiontypeId Tests

    //[Fact]
    //public async Task GetInfraObjectPropertyBySolutiontypeId_ShouldReturnProperties()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var jobs = _jobFixture.JobList;
    //    var solutionTypeId = "SOLUTION_TYPE_123";
    //    jobs.ForEach(x =>
    //    {
    //        x.SolutionTypeId = solutionTypeId;
    //        x.InfraObjectProperties = $"[\"INFRA_OBJECT_{x.Id}\"]";
    //    });
    //    await _repository.AddRangeAsync(jobs);

    //    // Act
    //    var result = await _repository.GetInfraObjectPropertyBySolutiontypeId(solutionTypeId);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Equal(jobs.Count, result.Count);
    //    Assert.All(result, x => Assert.NotNull(x));
    //}

    [Fact]
    public async Task GetInfraObjectPropertyBySolutiontypeId_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetInfraObjectPropertyBySolutiontypeId("NON_EXISTENT_SOLUTION_TYPE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetByPolicy Tests

    //[Fact]
    //public async Task GetByPolicy_ShouldReturnMatchingJobs()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var jobs = _jobFixture.JobList;
    //    var policy = "EXECUTION_POLICY_123";
    //    jobs.ForEach(x => x.ExecutionPolicy = policy);
    //    await _repository.AddRangeAsync(jobs);

    //    // Act
    //    var result = await _repository.GetByPolicy(policy);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Equal(jobs.Count, result.Count);
    //    Assert.All(result, x => Assert.Equal(policy, x.ExecutionPolicy));
    //}

    [Fact]
    public async Task GetByPolicy_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetByPolicy("NON_EXISTENT_POLICY");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByPolicy_ShouldFilterByCompanyId()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        var policy = "EXECUTION_POLICY_123";
        jobs.ForEach(x =>
        {
            x.ExecutionPolicy = policy;
            x.CompanyId = JobFixture.CompanyId;
        });
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetByPolicy(policy);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(JobFixture.CompanyId, x.CompanyId));
    }

    #endregion

    #region GetJobsByInfraObjectId Tests

    [Fact]
    public async Task GetJobsByInfraObjectId_ShouldReturnMatchingJobs_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        var infraObjectId = "INFRA_OBJECT_123";
        jobs.ForEach(x => x.InfraObjectProperties = $"[{infraObjectId}]");
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetJobsByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(jobs.Count, result.Count);
        Assert.All(result, x => Assert.Contains(infraObjectId, x.InfraObjectProperties));
    }

    [Fact]
    public async Task GetJobsByInfraObjectId_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetJobsByInfraObjectId("NON_EXISTENT_INFRA_OBJECT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetJobsByInfraObjectId_ShouldFilterByCompanyId_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        
        var infraObjectId = "INFRA_OBJECT_123";
        jobs.ForEach(x =>
        {
            x.InfraObjectProperties = $"[{infraObjectId}]";
         x.CompanyId = "ChHILD_COMPANY_123";
        });
        await _repositoryNotParent.AddRangeAsync(jobs);

        // Act
        var result = await _repositoryNotParent.GetJobsByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
    }

    #endregion

    #region GetJobsListByTemplateId Tests

    [Fact]
    public async Task GetJobsListByTemplateId_ShouldReturnMatchingJobs_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        var templateId = "TEMPLATE_123";
        jobs.ForEach(x => x.TemplateId = templateId);
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetJobsListByTemplateId(templateId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(jobs.Count, result.Count);
      
    }

    [Fact]
    public async Task GetJobsListByTemplateId_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetJobsListByTemplateId("NON_EXISTENT_TEMPLATE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetJobsListByTemplateId_ShouldFilterByCompanyId_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        var templateId = "TEMPLATE_123";
        jobs.ForEach(x =>
        {
            x.TemplateId = templateId;
            x.CompanyId = "ChHILD_COMPANY_123";
        });
        await _repositoryNotParent.AddRangeAsync(jobs);

        // Act
        var result = await _repositoryNotParent.GetJobsListByTemplateId(templateId);

        // Assert
        Assert.NotNull(result);
    }

    #endregion

    #region GetJobByTemplateId Tests

    [Fact]
    public async Task GetJobByTemplateId_ShouldReturnFirstMatchingJob()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        var templateId = "TEMPLATE_123";
       

      
        jobs.ForEach(x => x.TemplateId = templateId);
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetJobByTemplateId(templateId);

        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetJobByTemplateId_ShouldReturnNull_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetJobByTemplateId("NON_EXISTENT_TEMPLATE");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetJobByTemplateId_ShouldFilterByCompanyId_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        var templateId = "TEMPLATE_123";
        jobs.ForEach(x =>
        {
            x.TemplateId = templateId;
            x.CompanyId = "ChHILD_COMPANY_123";
            });
        await _repositoryNotParent.AddRangeAsync(jobs);

        // Act
        var result = await _repositoryNotParent.GetJobByTemplateId(templateId);

        // Assert
        Assert.NotNull(result);
    }

    #endregion

    #region GetJobByIds Tests

    [Fact]
    public async Task GetJobByIds_ShouldReturnMatchingJobs_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        var templateIds = new List<string> { "TEMPLATE_001", "TEMPLATE_002" };
        jobs[0].TemplateId = templateIds[0];
        jobs[1].TemplateId = templateIds[1];
        jobs[2].TemplateId = "TEMPLATE_003"; // Should not be included
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetJobByIds(templateIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains(x.TemplateId, templateIds));
        Assert.All(result, x =>
        {
            Assert.NotNull(x.ReferenceId);
            Assert.NotNull(x.Name);
            Assert.NotNull(x.TemplateId);
        });
    }

    [Fact]
    public async Task GetJobByIds_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetJobByIds(new List<string> { "NON_EXISTENT_TEMPLATE" });

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    //[Fact]
    //public async Task GetJobByIds_ShouldFilterByCompanyId_WhenIsParentFalse()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var jobs = _jobFixture.JobList;
    //    var templateIds = new List<string> { "TEMPLATE_001" };
    //    jobs.ForEach(x =>
    //    {
    //        x.TemplateId = templateIds[0];
    //        x.CompanyId = JobFixture.CompanyId;
    //    });
    //    await _repositoryNotParent.AddRangeAsync(jobs);

    //    // Act
    //    var result = await _repositoryNotParent.GetJobByIds(templateIds);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.All(result, x => Assert.Equal(JobFixture.CompanyId, x.CompanyId));
    //}

    #endregion

    #region GetJobByGroupNodePolicyId Tests

    [Fact]
    public async Task GetJobByGroupNodePolicyId_ShouldReturnMatchingJobs()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        var groupPolicyId = "GROUP_POLICY_123";
        jobs.ForEach(x => x.GroupPolicyId = groupPolicyId);
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetJobByGroupNodePolicyId(groupPolicyId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(jobs.Count, result.Count);
        Assert.All(result, x => Assert.Equal(groupPolicyId, x.GroupPolicyId));
    }

    [Fact]
    public async Task GetJobByGroupNodePolicyId_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetJobByGroupNodePolicyId("NON_EXISTENT_GROUP_POLICY");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    //[Fact]
    //public async Task GetJobByGroupNodePolicyId_ShouldFilterByCompanyId_WhenIsParentFalse()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var jobs = _jobFixture.JobList;
    //    var groupPolicyId = "GROUP_POLICY_123";
    //    jobs.ForEach(x =>
    //    {
    //        x.GroupPolicyId = groupPolicyId;
    //        x.CompanyId = JobFixture.CompanyId;
    //    });
    //    await _repositoryNotParent.AddRangeAsync(jobs);

    //    // Act
    //    var result = await _repositoryNotParent.GetJobByGroupNodePolicyId(groupPolicyId);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.All(result, x => Assert.Equal(JobFixture.CompanyId, x.CompanyId));
    //}

    #endregion

    #region GetJobNames Tests

    [Fact]
    public async Task GetJobNames_ShouldReturnOnlyReferenceIdAndName()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetJobNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(jobs.Count, result.Count);
        Assert.All(result, x =>
        {
            Assert.NotNull(x.ReferenceId);
            Assert.NotNull(x.Name);
            // Other properties should be default/null since we only select ReferenceId and Name
        });
    }

    //[Fact]
    //public async Task GetJobNames_ShouldReturnEmpty_WhenNoEntities()
    //{
    //    // Arrange
    //    await ClearDatabase();

    //    // Act
    //    var result = await _repository.GetJobNames();

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Empty(result);
    //}

    //[Fact]
    //public async Task GetJobNames_ShouldFilterByCompanyId()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var jobs = _jobFixture.JobList;
    //    jobs.ForEach(x => x.CompanyId = JobFixture.CompanyId);
    //    await _repository.AddRangeAsync(jobs);

    //    // Act
    //    var result = await _repository.GetJobNames();

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.All(result, x => Assert.Equal(JobFixture.CompanyId, x.CompanyId));
    //}

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobPaginationList;
        await _repository.AddRangeAsync(jobs);

        var specification = new JobFilterSpecification();
        var pageNumber = 1;
        var pageSize = 5;
        var sortColumn = "Name";
        var sortOrder = "asc";

        // Act
        var result = await _repository.PaginatedListAllAsync(pageNumber, pageSize, specification, sortColumn, sortOrder);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.CurrentPage >= 1);
        Assert.True(result.PageSize >= 1);
        Assert.NotNull(result.Data);
        Assert.True(result.Data.Count <= pageSize);
    }

    //[Fact]
    //public async Task PaginatedListAllAsync_ShouldFilterByCompanyId_WhenIsParentFalse()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var jobs = _jobFixture.JobPaginationList;
    //    jobs.ForEach(x => x.CompanyId = JobFixture.CompanyId);
    //    await _repositoryNotParent.AddRangeAsync(jobs);

    //    var specification = new JobFilterSpecification();
    //    var pageNumber = 1;
    //    var pageSize = 5;
    //    var sortColumn = "Name";
    //    var sortOrder = "asc";

    //    // Act
    //    var result = await _repositoryNotParent.PaginatedListAllAsync(pageNumber, pageSize, specification, sortColumn, sortOrder);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.NotNull(result.Data);
    //    Assert.All(result.Data, x => Assert.Equal(JobFixture.CompanyId, x.CompanyId));
    //}

    [Fact]
    public async Task PaginatedListAllAsync_ShouldHandleInvalidParameters()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        await _repository.AddRangeAsync(jobs);

        var specification = new JobFilterSpecification();

        // Act & Assert - Should handle edge cases gracefully
        var result1 = await _repository.PaginatedListAllAsync(0, 10, specification, "Name", "asc"); // Page 0
        var result2 = await _repository.PaginatedListAllAsync(1, 0, specification, "Name", "asc"); // Page size 0
        var result3 = await _repository.PaginatedListAllAsync(1, 10, specification, "", "asc"); // Empty sort column
        var result4 = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", ""); // Empty sort order

        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.NotNull(result3);
        Assert.NotNull(result4);
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public async Task GetPaginatedQuery_ShouldReturnQueryableForPagination()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.IsAssignableFrom<IQueryable<ContinuityPatrol.Domain.Entities.Job>>(result);
    }

    //[Fact]
    //public async Task GetPaginatedQuery_ShouldFilterByCompanyId()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var jobs = _jobFixture.JobList;
    //    jobs.ForEach(x => x.CompanyId = JobFixture.CompanyId);
    //    await _repository.AddRangeAsync(jobs);

    //    // Act
    //    var result = _repository.GetPaginatedQuery();
    //    var resultList = await result.ToListAsync();

    //    // Assert
    //    Assert.NotNull(resultList);
    //    Assert.All(resultList, x => Assert.Equal(JobFixture.CompanyId, x.CompanyId));
    //}

    [Fact]
    public async Task GetPaginatedQuery_ShouldOrderByDescendingId()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = _repository.GetPaginatedQuery();
        var resultList = await result.ToListAsync();

        // Assert
        Assert.NotNull(resultList);
        if (resultList.Count > 1)
        {
            for (int i = 0; i < resultList.Count - 1; i++)
            {
                Assert.True(resultList[i].Id >= resultList[i + 1].Id);
            }
        }
    }

    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task AllMethods_ShouldHandleEmptyDatabase_Gracefully()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert - All these should not throw exceptions and return appropriate empty results
        var listAll = await _repository.ListAllAsync();
        var getNames = await _repository.GetJobNames();
        var getBySolutionType = await _repository.GetBySolutionTypeId("SOLUTION_TYPE_123");
        var getByPolicy = await _repository.GetByPolicy("POLICY_123");
        var getByInfraObject = await _repository.GetJobsByInfraObjectId("INFRA_OBJECT_123");
        var getByTemplate = await _repository.GetJobsListByTemplateId("TEMPLATE_123");
        var getByGroupPolicy = await _repository.GetJobByGroupNodePolicyId("GROUP_POLICY_123");
        var getInfraProperties = await _repository.GetInfraObjectPropertyBySolutiontypeId("SOLUTION_TYPE_123");

        Assert.Empty(listAll);
        Assert.Empty(getNames);
        Assert.Empty(getBySolutionType);
        Assert.Empty(getByPolicy);
        Assert.Empty(getByInfraObject);
        Assert.Empty(getByTemplate);
        Assert.Empty(getByGroupPolicy);
        Assert.Empty(getInfraProperties);
    }

    //[Fact]
    //public async Task StringSearchMethods_ShouldHandleSpecialCharacters()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var job = _jobFixture.JobDto;
    //    job.Name = "Test-Job_With@Special#Characters";
    //    job.ExecutionPolicy = "Policy-With_Special@Characters";
    //    job.SolutionTypeId = "Solution_Type@123";
    //    await _repository.AddAsync(job);

    //    // Act
    //    var isNameUnique = await _repository.IsJobNameUnique("Test-Job_With@Special#Characters");
    //    var isNameExist = await _repository.IsJobNameExist("Test-Job_With@Special#Characters", "invalid-guid");
    //    var byPolicy = await _repository.GetByPolicy("Policy-With_Special@Characters");
    //    var bySolutionType = await _repository.GetBySolutionTypeId("Solution_Type@123");

    //    // Assert
    //    Assert.True(isNameUnique);
    //    Assert.True(isNameExist);
    //    Assert.NotEmpty(byPolicy);
    //    Assert.NotEmpty(bySolutionType);
    //}

    [Fact]
    public async Task JsonPropertyMethods_ShouldHandleJsonArrays()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;

        jobs[0].InfraObjectProperties = "[\"INFRA_001\", \"INFRA_002\"]";
        jobs[1].InfraObjectProperties = "[\"INFRA_003\", \"INFRA_004\"]";
        jobs[2].InfraObjectProperties = "[\"INFRA_005\"]";

        await _repository.AddRangeAsync(jobs);

        // Act
        var result1 = await _repository.GetJobsByInfraObjectId("INFRA_001");
        var result2 = await _repository.GetJobsByInfraObjectId("INFRA_003");
        var result3 = await _repository.GetJobsByInfraObjectId("INFRA_005");

        // Assert
        Assert.NotEmpty(result1);
        Assert.NotEmpty(result2);
        Assert.NotEmpty(result3);
    }

    [Fact]
    public async Task NullAndEmptyParameterMethods_ShouldHandleGracefully()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert - All these should not throw exceptions
        var isNameUnique1 = await _repository.IsJobNameUnique(null);
        var isNameUnique2 = await _repository.IsJobNameUnique("");
        var isNameExist1 = await _repository.IsJobNameExist(null, "id");
        var isNameExist2 = await _repository.IsJobNameExist("", "id");
        var getBySolutionType = await _repository.GetBySolutionTypeId(null);
        var getByPolicy = await _repository.GetByPolicy(null);
        var getByInfraObject = await _repository.GetJobsByInfraObjectId(null);
        var getByTemplate = await _repository.GetJobsListByTemplateId(null);
        var getByGroupPolicy = await _repository.GetJobByGroupNodePolicyId(null);

        Assert.False(isNameUnique1);
        Assert.False(isNameUnique2);
        Assert.False(isNameExist1);
        Assert.False(isNameExist2);
        Assert.Empty(getBySolutionType);
        Assert.Empty(getByPolicy);
        Assert.Empty(getByInfraObject);
        Assert.Empty(getByTemplate);
        Assert.Empty(getByGroupPolicy);
    }

    [Fact]
    public async Task Repository_ShouldHandleLargeDatasets()
    {
        // Arrange
        await ClearDatabase();
        var largeDataset = new List<ContinuityPatrol.Domain.Entities.Job>();

        for (int i = 0; i < 100; i++)
        {
            var job = new ContinuityPatrol.Domain.Entities.Job
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = $"Job_{i:D3}",
                CompanyId = JobFixture.CompanyId,
                SolutionTypeId = $"SOLUTION_TYPE_{i % 10}",
                TemplateId = $"TEMPLATE_{i % 5}",
                ExecutionPolicy = $"POLICY_{i % 3}",
                GroupPolicyId = $"GROUP_POLICY_{i % 4}",
                Status = i % 2 == 0 ? "Active" : "Inactive",
                State = i % 3 == 0 ? "Running" : "Stopped",
                InfraObjectProperties = $"[\"INFRA_OBJECT_{i}\"]",
                IsActive = true,
                CreatedDate = DateTime.UtcNow,
                CreatedBy = "TestUser"
            };
            largeDataset.Add(job);
        }

        await _repository.AddRangeAsync(largeDataset);

        // Act
        var allResults = await _repository.ListAllAsync();
        var paginatedResults = await _repository.PaginatedListAllAsync(1, 20, new JobFilterSpecification(), "Name", "asc");
        var nameResults = await _repository.GetJobNames();
        var solutionTypeResults = await _repository.GetBySolutionTypeId("SOLUTION_TYPE_1");

        // Assert
        Assert.NotNull(allResults);
        Assert.NotNull(paginatedResults);
        Assert.NotNull(nameResults);
        Assert.NotNull(solutionTypeResults);

        // Verify pagination works with large datasets
        Assert.True(paginatedResults.PageSize <= 20);
        Assert.Equal(100, allResults.Count);
        Assert.Equal(100, nameResults.Count);
        Assert.Equal(10, solutionTypeResults.Count); // 100 jobs / 10 solution types = 10 jobs per type
    }

    #endregion

    #region Complex Operations and Integration Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(jobs);
        var initialCount = jobs.Count;

        var toUpdate = jobs.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedJobName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = jobs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Name == "UpdatedJobName").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldRespectUserPermissions_AcrossAllMethods()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _jobFixture.JobList;
        var solutionTypeId = "SOLUTION_TYPE_123";
        var templateId = "TEMPLATE_123";

        jobs.ForEach(x =>
        {
            x.SolutionTypeId = solutionTypeId;
            x.TemplateId = templateId;
            x.CompanyId = JobFixture.CompanyId;
        });

        await _repository.AddRangeAsync(jobs);

        // Act - Test with different user permission configurations
        var parentResults = await _repository.GetBySolutionTypeId(solutionTypeId);
        var notParentResults = await _repositoryNotParent.GetBySolutionTypeId(solutionTypeId);

        // Assert
        Assert.NotNull(parentResults);
        Assert.NotNull(notParentResults);

        // Both should execute without error and respect the user permission filtering logic
        Assert.All(parentResults, x => Assert.Equal(solutionTypeId, x.SolutionTypeId));
        Assert.All(notParentResults, x => Assert.Equal(JobFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task MappingMethods_ShouldHandleRelatedEntities()
    {
        // Arrange
        await ClearDatabase();

        // Add related entities first
        var template = new Template { ReferenceId = "TEMPLATE_123", Name = "Test Template", IsActive = true };
        var componentType = new ComponentType { ReferenceId = "COMPONENT_123", ComponentName = "Test Component", IsActive = true };
        var groupPolicy = new GroupPolicy { ReferenceId = "GROUP_POLICY_123", GroupName = "Test Group Policy", IsActive = true };

        _dbContext.Templates.Add(template);
        _dbContext.ComponentTypes.Add(componentType);
        _dbContext.GroupPolicies.Add(groupPolicy);
        await _dbContext.SaveChangesAsync();

        var job = _jobFixture.JobDto;
        job.TemplateId = template.ReferenceId;
        job.SolutionTypeId = componentType.ReferenceId;
        job.GroupPolicyId = groupPolicy.ReferenceId;
        await _repository.AddAsync(job);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var mappedJob = result.First();

        // Note: The mapping may not work perfectly in in-memory database, but should not throw exceptions
        Assert.NotNull(mappedJob);
        Assert.Equal(job.ReferenceId, mappedJob.ReferenceId);
    }

    #endregion
}

// Simple concrete specification for testing
public class JobFilterSpecification : Specification<ContinuityPatrol.Domain.Entities.Job>
{
    public JobFilterSpecification(string searchString = null)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = x => x.IsActive;
        }
        else
        {
            Criteria = x => x.IsActive && x.Name.Contains(searchString);
        }
    }
}
