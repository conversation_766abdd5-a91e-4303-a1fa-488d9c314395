/*.left-img {
    background-image: url('../img/login/LeftImage.jpg');
    background-repeat: no-repeat;
    background-size: cover;
    display: grid;
    align-items: end;
    color: #fff;
}*/

.right-img {
    background-image: url(../img/login/Abstract.jpg);
    background-repeat: no-repeat;
    background-size: contain;
    background-position: right;
}

.login_card {
    border: none;
}

    .login_card .card-header {
        background-color: transparent;
        border-bottom: 0px solid #000;
    }

    .login_card .card-footer {
        background-color: transparent;
        border-top: 0px solid #000;
    }

.form-group {
    position: relative;
    margin-bottom: 1rem !important;
}

.input-group {
    color: var(--bs-body-color);
    background-color: transparent;
    border-bottom: 1px solid var(--bs-gray-300);
    border-radius: 0rem;
    align-items: center;
    flex-wrap: inherit;
    height: 35px;
}

    .input-group:focus {
        color: var(--bs-body-color);
        background-color: transparent;
        border-bottom: 1px solid var(--bs-primary);
    }

    .input-group:focus-within {
        color: var(--bs-body-color);
        background-color: transparent;
        border-bottom: 1px solid var(--bs-primary);
    }

.input-group-text:focus-within {
    color: var(--bs-body-color);
    background-color: transparent;
    border-bottom: 1px solid var(--bs-primary);
}

.form-control {
    font-size: var(--bs-body-font-size);
    color: var(--bs-gray-700);
    background-color: #fff;
    border: none;
}

    .form-control::placeholder {
        color: var(--bs-gray-500) !important;
        font-size: var(--bs-body-font-size-small);
        opacity: 1
    }

    .form-control:focus {
        background-color: transparent;
        border: none;
        outline: 0;
        box-shadow: none;
    }

    .form-control::-moz-placeholder {
        color: var(--bs-gray-700);
        font-size: var(--bs-body-font-size-small);
    }

    .form-control::placeholder {
        color: var(--bs-gray-700);
        font-size: var(--bs-body-font-size-small);
    }

.form-select {
    font-size: var(--bs-body-font-size);
    color: var(--bs-gray-700);
    background-color: var(--bs-gray-100);
    border: none;
}

    .form-select::placeholder {
        color: var(--bs-gray-500) !important;
        opacity: 1;
        font-size: var(--bs-body-font-size-small);
    }

    .form-select:focus {
        background-color: var(--bs-gray-200);
        border: none;
        outline: 0;
        box-shadow: none;
    }

    .form-select::-moz-placeholder {
        color: var(--bs-gray-700);
        font-size: var(--bs-body-font-size-small);
    }

    .form-select::placeholder {
        color: var(--bs-gray-700);
        font-size: var(--bs-body-font-size-small);
    }


.input-group-text {
    font-size: var(--bs-input-icon-font-size);
    color: var(--bs-body-color);
    background-color: transparent;
    border: none;
    padding: 4px 2px;
    padding-left: 0px
}

.input-group .select2-container {
    width: 100% !important;
}

.form-label {
    color: var(--bs-gray-700);
    font-weight: var(--bs-form-font-weight);
    font-size: var(--bs-form-font-size);
}

.form-check-label {
    color: var(--bs-gray-700);
    font-weight: var(--bs-form-font-weight);
    font-size: var(--bs-form-font-size);
    text-decoration: none;
}

:focus-visible {
    outline: -webkit-focus-ring-color auto 0px;
}

.form-check-input {
    border-color: var(--bs-gray-400);
}


@-moz-document url-prefix() {
    input[type=password]::placeholder {
        font-size: var(--bs-body-font-size);
        color: var(--bs-gray-700);
    }

    .h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
        font-weight: 400;
    }
}

.field-validation-error {
    width: 100%;
    font-size: 11px;
    position: absolute;
    color: var(--bs-form-invalid-color);
    text-align: end;
    border-top: 1px solid var(--bs-form-invalid-color);
    margin-top:-1px;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    font-size: var(--bs-body-font-size);
    color: var(--bs-gray-700);
    background-color: var(--bs-gray-100);
    border: none;
    outline:none;
}

.select2-container--default .select2-selection--single {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 0px;
    top: 5px;
}

    .select2-container--default .select2-selection--single .select2-selection__arrow b {
        margin-left: -10px;
    }

.select2-dropdown {
    background-color: white;
    border: 0px solid #aaa;
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
}


.face_detaction {
    width: 200px;
    border-image: url('../../assets/img/isomatric/face-boder.svg') 30 / 19px round;
}

.login-btn {
    width: 130px;
    height: 38px;
}

.Notification {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index:1;
}

    .Notification .toast {
        margin-bottom: 10px;
        font-size: var(--bs-body-font-size);
        border-width: 0px;
        background-color: #fff;
        --bs-toast-max-width: 400px;
    }

    .Notification .toast-body {
        display: flex;
        align-items: center;
    }

.Card_Design_None {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,.01) !important;
}

    .Card_Design_None .card-header {
        background-color: transparent;
        border-bottom: 0px solid #000;
    }

    .Card_Design_None .card-body {
        background-color: transparent;
        border-bottom: 0px solid #000;
    }

    .Card_Design_None .card-footer {
        background-color: transparent;
        border-top: 0px solid #000;
    }

/*Custom Style*/
.success-toast .success-toast::before {
    content: "\f633"
}

.unauthorised-toast .unauthorised-toast::before {
    content: "\f8a9"
}

.warning-toast .warning-toast::before {
    content: "\f63c"
}

.toast_icon {
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    font-size: 18px;
    text-align: center;
    margin-right: 13px;
    border-radius: 50%;
    padding: 4px;
}

.success-toast .toast_icon {
    background: rgb(108,192,33);
    background: linear-gradient(60deg, rgba(108,192,33,1) 0%, rgba(93,188,29,1) 52%, rgba(167,209,50,1) 100%);
}

.unauthorised-toast .toast_icon {
    background: rgb(255,32,70);
    background: linear-gradient(60deg, rgba(255,32,70,1) 0%, rgba(251,43,99,1) 52%, rgba(244,57,137,1) 100%);
}

.info-toast .toast_icon {
    background: rgb(29,106,211);
    background: linear-gradient(166deg, rgba(29,106,211,1) 23%, rgba(34,135,220,1) 71%, rgba(39,165,230,1) 100%);
}

.warning-toast .toast_icon {
    background: rgb(255,153,26);
    background: linear-gradient(166deg, rgba(255,153,26,1) 0%, rgba(255,187,14,1) 71%, rgba(255,222,2,1) 100%);
}

::-ms-reveal {
    display: none
}

/*End Custom Style*/
