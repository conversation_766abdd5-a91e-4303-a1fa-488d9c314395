using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Delete;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetList;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixApprovalModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class ApprovalMatrixApprovalsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<List<ApprovalMatrixApprovalListVm>>> GetApprovalMatrixApprovals()
    {
        Logger.LogDebug("Get All ApprovalMatrixApprovals");

        return Ok(await Mediator.Send(new GetApprovalMatrixApprovalListQuery()));
    }

    [HttpGet("{id}", Name = "GetApprovalMatrixApproval")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<ApprovalMatrixApprovalDetailVm>> GetApprovalMatrixApprovalById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ApprovalMatrixApproval Id");

        Logger.LogDebug($"Get ApprovalMatrixApproval Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetApprovalMatrixApprovalDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Manage.View)]
 public async Task<ActionResult<PaginatedResult<ApprovalMatrixApprovalListVm>>> GetPaginatedApprovalMatrixApprovals([FromQuery] GetApprovalMatrixApprovalPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in ApprovalMatrixApproval Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Manage.Create)]
    public async Task<ActionResult<CreateApprovalMatrixApprovalResponse>> CreateApprovalMatrixApproval([FromBody] CreateApprovalMatrixApprovalCommand createApprovalMatrixApprovalCommand)
    {
        Logger.LogDebug($"Create ApprovalMatrixApproval '{createApprovalMatrixApprovalCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateApprovalMatrixApproval), await Mediator.Send(createApprovalMatrixApprovalCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Manage.Edit)]
    public async Task<ActionResult<UpdateApprovalMatrixApprovalResponse>> UpdateApprovalMatrixApproval([FromBody] UpdateApprovalMatrixApprovalCommand updateApprovalMatrixApprovalCommand)
    {
        Logger.LogDebug($"Update ApprovalMatrixApproval '{updateApprovalMatrixApprovalCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateApprovalMatrixApprovalCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Manage.Delete)]
    public async Task<ActionResult<DeleteApprovalMatrixApprovalResponse>> DeleteApprovalMatrixApproval(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ApprovalMatrixApproval Id");

        Logger.LogDebug($"Delete ApprovalMatrixApproval Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteApprovalMatrixApprovalCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsApprovalMatrixApprovalNameExist(string approvalMatrixApprovalName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(approvalMatrixApprovalName, "ApprovalMatrixApproval Name");

     Logger.LogDebug($"Check Name Exists Detail by ApprovalMatrixApproval Name '{approvalMatrixApprovalName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetApprovalMatrixApprovalNameUniqueQuery { Name = approvalMatrixApprovalName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


