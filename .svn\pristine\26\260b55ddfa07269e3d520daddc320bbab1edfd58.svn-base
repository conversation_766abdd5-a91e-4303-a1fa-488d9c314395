﻿using ContinuityPatrol.Application.Features.Site.Queries.GetSiteByCompanyId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Site.Queries;

public class GetSiteByCompanyIdQueryHandlerTests : IClassFixture<SiteFixture>
{
    private readonly SiteFixture _siteFixture;

    private Mock<ISiteRepository> _mockSiteRepository;
    private Mock<ISiteTypeRepository> _mockSiteTypeRepository;

    private readonly GetSiteByCompanyIdQueryHandler _handler;

    public GetSiteByCompanyIdQueryHandlerTests(SiteFixture siteFixture)
    {
        _siteFixture = siteFixture;

        _mockSiteTypeRepository = new Mock<ISiteTypeRepository>();

        _mockSiteRepository = SiteRepositoryMocks.GetSiteByCompanyIdRepository(_siteFixture.Sites);

        _handler = new GetSiteByCompanyIdQueryHandler(_mockSiteRepository.Object, _siteFixture.Mapper, _mockSiteTypeRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnSites_When_ValidCompanyId()
    {
        var result = await _handler.Handle(new GetSiteByCompanyIdQuery { Id = _siteFixture.Sites[0].CompanyId }, CancellationToken.None);

        result.ShouldBeOfType<List<GetSiteByCompanyIdVm>>();
        result.Count.ShouldBe(3);
        result[0].Id.ShouldBe(_siteFixture.Sites[0].ReferenceId);
        result[0].Name.ShouldBe(_siteFixture.Sites[0].Name);
    }

    [Fact]

    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockSiteRepository = SiteRepositoryMocks.GetSiteEmptyRepository();

        var handler = new GetSiteByCompanyIdQueryHandler(_mockSiteRepository.Object, _siteFixture.Mapper, _mockSiteTypeRepository.Object);

        var result = await handler.Handle(new GetSiteByCompanyIdQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetSiteByCompanyIdMethod_OneTime()
    {
        await _handler.Handle(new GetSiteByCompanyIdQuery(), CancellationToken.None);

        _mockSiteRepository.Verify(x => x.GetSiteByCompanyId(It.IsAny<string>()), Times.Once);
    }
}