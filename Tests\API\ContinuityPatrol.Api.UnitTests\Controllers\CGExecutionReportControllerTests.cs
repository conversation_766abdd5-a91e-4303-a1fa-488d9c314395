using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.Create;
using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.Delete;
using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.Update;
using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.UpdateConditionActionId;
using ContinuityPatrol.Application.Features.CGExecutionReport.Queries.GetCgExecutionReportPaginatedList;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Xunit;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class CGExecutionReportControllerTests : IClassFixture<CGExecutionReportFixture>
{
    private readonly CGExecutionReportFixture _cgExecutionReportFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly CGExecutionReportController _controller;

    public CGExecutionReportControllerTests(CGExecutionReportFixture cgExecutionReportFixture)
    {
        _cgExecutionReportFixture = cgExecutionReportFixture;

        var testBuilder = new ControllerTestBuilder<CGExecutionReportController>();
        _controller = testBuilder.CreateController(
            _ => new CGExecutionReportController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetCgExecutionPaginatedList_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = _cgExecutionReportFixture.GetCgExecutionPaginatedListQuery;

        var expectedData = new List<CgExecutionPaginatedListVm>
        {
            new()
            {
                WorkflowOperationId = Guid.NewGuid().ToString(),
                WorkflowName = "Test-CG-01",
                Status = "Enabled"
            }
        };

        var expectedPaginatedResult = PaginatedResult<CgExecutionPaginatedListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCgExecutionPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetCgExecutionPaginatedList(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<CgExecutionPaginatedListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<CgExecutionPaginatedListVm>>(okResult.Value);

        Assert.Equal(expectedData.Count, paginatedResult.Data.Count);
        Assert.Equal(query.PageNumber, paginatedResult.CurrentPage);
        Assert.Equal(query.PageSize, paginatedResult.PageSize);
        Assert.Equal("Test-CG-01", paginatedResult.Data.First().WorkflowName);
        Assert.Equal("Enabled", paginatedResult.Data.First().Status);
    }

    [Fact]
    public async Task Create_Returns201Created()
    {
        // Arrange
        var command = _cgExecutionReportFixture.CreateCGExecutionCommand;
        var expectedMessage = $"CGExecutionReport '{command.CGName}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCGExecutionResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCGExecutionReport(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCGExecutionResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task Update_ReturnsOk()
    {
        // Arrange
        var command = _cgExecutionReportFixture.UpdateCGExecutionCommand;
        var expectedMessage = $"CGExecutionReport '{command.CGName}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateCGExecutionCommand>(), default))
            .ReturnsAsync(new UpdateCGExecutionResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCGExecutionReport(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCGExecutionResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task Delete_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "CGExecutionReport 'Test CG' has been deleted successfully!.";
        var cgExecutionId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCGExecutionCommand>(c => c.Id == cgExecutionId), default))
            .ReturnsAsync(new DeleteCGExecutionResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteCGExecutionReport(cgExecutionId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteCGExecutionResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task ResiliencyReadinessWorkflowScheduleLog_ReturnsOk()
    {
        // Arrange
        var command = _cgExecutionReportFixture.ResiliencyReadinessWorkflowScheduleLogCommand;
        var expectedMessage = "Resiliency readiness workflow schedule log has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new ResiliencyReadyWorkflowScheduleLogResponse
            {
                Message = expectedMessage,
                Id = command.WorkflowOperationId
            });

        // Act
        var result = await _controller.UpdateConditionActionId(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<ResiliencyReadyWorkflowScheduleLogResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task Create_ValidatesCGName()
    {
        // Arrange
        var command = new CreateCGExecutionCommand
        {
            CGName = "", // Empty CG name should cause validation error
            WorkflowName = "Test Workflow"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("CGName is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateCGExecutionReport(command));
    }

    [Fact]
    public async Task Update_ValidatesCGExecutionExists()
    {
        // Arrange
        var command = new UpdateCGExecutionCommand
        {
            Id = Guid.NewGuid().ToString(),
            CGName = "Updated CG"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("CGExecution not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateCGExecutionReport(command));
    }

    [Fact]
    public void ClearDataCache_ThrowsNotImplementedException()
    {
        // Act & Assert
        // CGExecutionReportController throws NotImplementedException for ClearDataCache
        Assert.Throws<NotImplementedException>(() => _controller.ClearDataCache());
    }

    #region Additional Test Cases

    [Fact]
    public async Task CreateCGExecutionReport_WithNullCommand_PassesToMediator()
    {
        // Arrange
        CreateCGExecutionCommand command = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateCGExecutionCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new ArgumentNullException(nameof(command)));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.CreateCGExecutionReport(command));
    }

    [Fact]
    public async Task CreateCGExecutionReport_WithValidCommand_ReturnsCreatedAtActionResult()
    {
        // Arrange
        var command = _cgExecutionReportFixture.CreateCGExecutionCommand;
        var expectedResponse = new CreateCGExecutionResponse
        {
            Id = Guid.NewGuid().ToString(),
            Message = "CGExecutionReport created successfully",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateCGExecutionCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateCGExecutionReport(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<CreateCGExecutionResponse>>(result);
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(actionResult.Result);
        var response = Assert.IsType<CreateCGExecutionResponse>(createdAtActionResult.Value);

        Assert.Equal(expectedResponse.Id, response.Id);
        Assert.Equal(expectedResponse.Message, response.Message);
        Assert.True(response.Success);
        Assert.Equal(nameof(_controller.CreateCGExecutionReport), createdAtActionResult.ActionName);
    }

    [Fact]
    public async Task UpdateCGExecutionReport_WithNullCommand_PassesToMediator()
    {
        // Arrange
        UpdateCGExecutionCommand command = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateCGExecutionCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new ArgumentNullException(nameof(command)));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.UpdateCGExecutionReport(command));
    }

    [Fact]
    public async Task UpdateCGExecutionReport_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _cgExecutionReportFixture.UpdateCGExecutionCommand;
        var expectedResponse = new UpdateCGExecutionResponse
        {
            Id = command.Id,
            Message = "CGExecutionReport updated successfully",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateCGExecutionCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateCGExecutionReport(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<UpdateCGExecutionResponse>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var response = Assert.IsType<UpdateCGExecutionResponse>(okResult.Value);

        Assert.Equal(expectedResponse.Id, response.Id);
        Assert.Equal(expectedResponse.Message, response.Message);
        Assert.True(response.Success);
    }

    [Fact]
    public async Task UpdateConditionActionId_WithNullCommand_PassesToMediator()
    {
        // Arrange
        ResiliencyReadinessWorkflowScheduleLogCommand command = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<ResiliencyReadinessWorkflowScheduleLogCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new ArgumentNullException(nameof(command)));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.UpdateConditionActionId(command));
    }

    [Fact]
    public async Task UpdateConditionActionId_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _cgExecutionReportFixture.ResiliencyReadinessWorkflowScheduleLogCommand;
        var expectedResponse = new ResiliencyReadyWorkflowScheduleLogResponse
        {
            Id = command.WorkflowOperationId,
            Message = "Condition action ID updated successfully",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<ResiliencyReadinessWorkflowScheduleLogCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateConditionActionId(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<ResiliencyReadyWorkflowScheduleLogResponse>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var response = Assert.IsType<ResiliencyReadyWorkflowScheduleLogResponse>(okResult.Value);

        Assert.Equal(expectedResponse.Id, response.Id);
        Assert.Equal(expectedResponse.Message, response.Message);
        Assert.True(response.Success);
    }

    [Fact]
    public async Task DeleteCGExecutionReport_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteCGExecutionReport(invalidId));
    }

    [Fact]
    public async Task DeleteCGExecutionReport_WithEmptyId_ThrowsArgumentException()
    {
        // Arrange
        var emptyId = string.Empty;

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteCGExecutionReport(emptyId));
    }

    [Fact]
    public async Task DeleteCGExecutionReport_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedResponse = new DeleteCGExecutionResponse
        {
            Message = "CGExecutionReport deleted successfully",
            Success = true,
            IsActive = false
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCGExecutionCommand>(c => c.Id == validId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteCGExecutionReport(validId);

        // Assert
        var actionResult = Assert.IsType<ActionResult<DeleteCGExecutionResponse>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var response = Assert.IsType<DeleteCGExecutionResponse>(okResult.Value);

        Assert.Equal(expectedResponse.Message, response.Message);
        Assert.True(response.Success);
        Assert.False(response.IsActive);
    }

    [Fact]
    public async Task GetCgExecutionPaginatedList_WithNullQuery_PassesToMediator()
    {
        // Arrange
        GetCgExecutionPaginatedListQuery query = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCgExecutionPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new ArgumentNullException(nameof(query)));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetCgExecutionPaginatedList(query));
    }

    [Fact]
    public async Task GetCgExecutionPaginatedList_WithEmptyResult_ReturnsEmptyPaginatedList()
    {
        // Arrange
        var query = _cgExecutionReportFixture.GetCgExecutionPaginatedListQuery;
        var expectedPaginatedResult = PaginatedResult<CgExecutionPaginatedListVm>.Success(
            data: new List<CgExecutionPaginatedListVm>(),
            count: 0,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCgExecutionPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetCgExecutionPaginatedList(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<CgExecutionPaginatedListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<CgExecutionPaginatedListVm>>(okResult.Value);

        Assert.Empty(paginatedResult.Data);
        Assert.Equal(0, paginatedResult.TotalCount);
        Assert.Equal(query.PageNumber, paginatedResult.CurrentPage);
        Assert.Equal(query.PageSize, paginatedResult.PageSize);
    }

    [Fact]
    public async Task GetCgExecutionPaginatedList_WithMultipleResults_ReturnsCorrectPaginatedList()
    {
        // Arrange
        var query = _cgExecutionReportFixture.GetCgExecutionPaginatedListQuery;
        var expectedData = new List<CgExecutionPaginatedListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                WorkflowOperationId = Guid.NewGuid().ToString(),
                WorkflowName = "CG-Workflow-01",
                Status = "Enabled",
                CgCount = "5",
                Type = "Enable",
                EnableStartTime = DateTime.UtcNow.AddHours(-2),
                EnableEndTime = DateTime.UtcNow.AddHours(-1)
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                WorkflowOperationId = Guid.NewGuid().ToString(),
                WorkflowName = "CG-Workflow-02",
                Status = "Disabled",
                CgCount = "3",
                Type = "Disable",
                EnableStartTime = DateTime.UtcNow.AddHours(-4),
                EnableEndTime = DateTime.UtcNow.AddHours(-3)
            }
        };

        var expectedPaginatedResult = PaginatedResult<CgExecutionPaginatedListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCgExecutionPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetCgExecutionPaginatedList(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<CgExecutionPaginatedListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<CgExecutionPaginatedListVm>>(okResult.Value);

        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal("CG-Workflow-01", paginatedResult.Data.First().WorkflowName);
        Assert.Equal("CG-Workflow-02", paginatedResult.Data.Last().WorkflowName);
        Assert.Equal("Enabled", paginatedResult.Data.First().Status);
        Assert.Equal("Disabled", paginatedResult.Data.Last().Status);
    }

    [Fact]
    public async Task CreateCGExecutionReport_WithMediatorException_ThrowsException()
    {
        // Arrange
        var command = _cgExecutionReportFixture.CreateCGExecutionCommand;
        var expectedException = new InvalidOperationException("Database connection failed");

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateCGExecutionCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var actualException = await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.CreateCGExecutionReport(command));
        Assert.Equal(expectedException.Message, actualException.Message);
    }

    [Fact]
    public async Task UpdateCGExecutionReport_WithMediatorException_ThrowsException()
    {
        // Arrange
        var command = _cgExecutionReportFixture.UpdateCGExecutionCommand;
        var expectedException = new InvalidOperationException("Record not found");

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateCGExecutionCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var actualException = await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateCGExecutionReport(command));
        Assert.Equal(expectedException.Message, actualException.Message);
    }

    [Fact]
    public async Task UpdateConditionActionId_WithMediatorException_ThrowsException()
    {
        // Arrange
        var command = _cgExecutionReportFixture.ResiliencyReadinessWorkflowScheduleLogCommand;
        var expectedException = new InvalidOperationException("Workflow operation not found");

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<ResiliencyReadinessWorkflowScheduleLogCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var actualException = await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateConditionActionId(command));
        Assert.Equal(expectedException.Message, actualException.Message);
    }

    [Fact]
    public async Task DeleteCGExecutionReport_WithMediatorException_ThrowsException()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedException = new InvalidOperationException("Cannot delete active CG execution");

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCGExecutionCommand>(c => c.Id == validId), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var actualException = await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.DeleteCGExecutionReport(validId));
        Assert.Equal(expectedException.Message, actualException.Message);
    }

    [Fact]
    public async Task GetCgExecutionPaginatedList_WithMediatorException_ThrowsException()
    {
        // Arrange
        var query = _cgExecutionReportFixture.GetCgExecutionPaginatedListQuery;
        var expectedException = new InvalidOperationException("Query execution failed");

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCgExecutionPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var actualException = await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.GetCgExecutionPaginatedList(query));
        Assert.Equal(expectedException.Message, actualException.Message);
    }

    #endregion
}
