const SSOURL = {
    createOrUpdate: "Configuration/SingleSignOn/CreateOrUpdate",
    delete: "Configuration/SingleSignOn/Delete",
    getFormMappingListByName: "Admin/FormMapping/GetFormMappingListByName",
    getSingleSignOnById: "Configuration/SingleSignOn/GetSingleSignOnById",
    getSingleSignOnList: "Configuration/SingleSignOn/GetSingleSignOnList",
    isSingleSignOnNameExist: "Configuration/SingleSignOn/IsSingleSignOnNameExist",
    serverDataDecrypt: 'Configuration/Server/ServerDataDecrypt',
    serverDataEncrypt: 'Configuration/Server/ServerDataEncrypt'
};

let selectedValues = [];
let dataTable = "";
let createPermission = $("#configurationCreate").data("create-permission")?.toLowerCase();
let deletePermission = $("#configurationDelete").data("delete-permission")?.toLowerCase();
let propsSSO = "";
let isEditSSO = false;
let btnDisableSSO = false;
let singleSignOnIcon = "";

$(async function () {
    infraPreventSpecialKeys('#search-inp, #singleSignOnName'); //commonfunctions.js

    if (createPermission == 'false') {
        $('#SingleSignOn-CreateButton').removeClass('#SingleSignOn-CreateButton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }

    dataTable = $('#singleSignOnList').DataTable(
        {
            language: {
                decimal: ",",
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous" ></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "Sortable": true,
            "order": [],
            fixedColumns: {
                left: 1,
                right: 1
            },
            "ajax": {
                "type": "GET",
                "url": "/Configuration/SingleSignOn/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "profileName" : sortIndex === 2 ? "signOnType" : "";
                    let orderValue = d?.order[0]?.dir || 'asc';
                    let selectedType = $('#selectType').val();
                    selectedType = selectedType === "all" ? "" : selectedType;
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.TypeId = selectedType;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                        const { data } = json;
                        const hasData = data?.data?.length === 0
                        $(".pagination-column").toggleClass("disabled", hasData);
                        json.recordsTotal = data?.totalPages;
                        json.recordsFiltered = data?.totalCount;
                        return data?.data;
                    }
                    else {
                        errorNotification(json)
                    }
                },
            },
            "columnDefs": [
                {
                    "targets": [1],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        return type === 'display' ? meta.row + 1 : data;
                    }
                },
                {
                    "data": "profileName", "name": "name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>${data || "NA"}</span>` : data;
                    }
                },
                {
                    "data": "signOnType", "name": "name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            const iconList = JSON.parse(row?.properties);
                            const iconClass = iconList?.icon || "cp-images";
                            return `<span title='${data || "NA"}'><i class='${iconClass} me-1'></i>${data || "NA"}</span>`;
                        }
                        return data;
                    }
                },
                {
                    "orderable": false, "width": '100px',
                    "render": function (data, type, row) {
                        const rowId = row?.id;
                        const profileName = row?.profileName;
                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                                <div class="d-flex align-items-center gap-2">                                         
                                    <span role="button" title="Edit"
                                        class="ssoEditButton"
                                        data-sso="${rowId}">
                                        <i class="cp-edit "></i>
                                    </span>
                                    <span role="button" title="Delete"
                                        class="ssoDeleteButton"
                                        data-bs-toggle="modal" 
                                        data-ssoname="${profileName}" 
                                        data-ssoid="${rowId}" 
                                        data-bs-target="#DeleteModal">
                                        <i class="cp-Delete "></i>
                                    </span>                           
                                </div>`;
                        } else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                                <div class="d-flex align-items-center gap-2">                                         
                                    <span role="button" title="Edit" class="ssoEditButton" data-sso='${rowId}'>
                                        <i class="cp-edit "></i>
                                    </span>
                                    <span role="button" title="Delete" class="icon-disabled">
                                        <i class="cp-Delete "></i>
                                    </span>                           
                                </div>`;

                        } else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                                <div class="d-flex align-items-center gap-2">                                         
                                    <span role="button" title="Edit" class="icon-disabled">
                                        <i class="cp-edit "></i>
                                    </span>
                                    <span role="button" title="Delete" class="ssoDeleteButton" data-bs-toggle="modal" 
                                        data-ssoname="${profileName}" data-ssoid="${rowId}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                        <i class="cp-Delete "></i>
                                    </span>                           
                                </div>`;
                        }
                        else {
                            return `
                                <div class="d-flex align-items-center gap-2">                                         
                                    <span role="button" title="Edit" class="icon-disabled">
                                        <i class="cp-edit "></i>
                                    </span>
                                    <span role="button" title="Delete" class="icon-disabled">
                                        <i class="cp-Delete "></i>
                                    </span>                           
                                </div>`;
                        }
                    }
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart; // Get the start index of displayed data
                let counter = startIndex + index + 1; // Calculate the serial number based on start index and index
                $('td:eq(0)', row).html(counter); // Update the cell in the first column with the serial number
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            },
        });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#search-inp').on('input', commonDebounce(async function (e) {
        const checkboxes = ["Name", "CompanyName"];
        let sanitizedValue = $(this).val().replace(/^\s/, '').replace(/\s+/g, ' ').replace(/\s$/, ' ');

        if (sanitizedValue.trim() === "") {
            $(this).val("");
            sanitizedValue = "";
        } else {
            $(this).val(sanitizedValue);
        }
        checkboxes.forEach(id => {
            const checkbox = $(`#${id}`);

            if (checkbox.is(':checked')) {
                selectedValues.push(checkbox.val() + sanitizedValue);
            }
        });
        dataTable.ajax.reload(function (json) {
            let $dataTables_empty = $('.dataTables_empty');

            if (sanitizedValue.length === 0 && json?.data?.data?.length === 0) {
                $dataTables_empty.text('No Data Found');
            } else if (json?.recordsFiltered === 0) {
                $dataTables_empty.text('No matching records found');
            }
        })
    }));

    $('#selectType').on("change", function () {
        dataTable.ajax.reload(function (json) {
            if (json?.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    });

    $('.form-select-sm').select2({
        "language": { "noResults": function () { return "No results found"; } },
    });

    $('.modal').on('shown.bs.modal', function async() {
        this1 = $(this);
    });

    $('.modal').on('hidden.bs.modal', function () {
        isEditSSO = false;
    });

    $("#SSOFormTypeID").on("change", function async() {
        singleSignOnProperties($("#SSOFormTypeID option:selected").attr("formTypeId"));
    });

    $('#singleSignOnName').on('keyup', commonDebounce(function () {
        const $singleSignOnName = $("#singleSignOnName");
        $singleSignOnName.val($singleSignOnName.val().replace(/\s{2,}/g, ' '));

        //CommonFunctions.js InfraNameValidation
        InfraNameValidation($singleSignOnName.val(), $('#singleSignOnID').val(),
            SSOURL.isSingleSignOnNameExist, $("#profileNameError"), "Enter profile name",
            'Special characters not allowed', 'SingleSignOnName');
    }));

    $('.next_btn').on('click', async function () {
        const validateSingleSignOnName = await InfraNameValidation($("#singleSignOnName").val(), $('#singleSignOnID').val(),
            SSOURL.isSingleSignOnNameExist, $("#profileNameError"), "Enter profile name",
            'Special characters not allowed', 'SingleSignOnName');
        const validateSingleSignOnType = commonValidationSSO($("#SSOFormTypeID option:selected").text(), " Select single sign-on type", "signOnTypeError");

        if (validateSingleSignOnName && validateSingleSignOnType) {
            form.steps('next');
        }
    });

    $('.prev_btn').on('click', function () {
        form.steps('previous');
        removeErrorForSelect('.dynamic-select-tag'); //commonfunctions.js
        const inputValues = $(".formeo-render .f-field-group input[type='text']:visible, .formeo-render .f-field-group input[type='number']:visible, .formeo-render .f-field-group input[type='password']:visible, .formeo-render .f-field-group input[type='date']:visible, .formeo-render .f-field-group textarea[type='textarea']:visible");
        removeErrorForInputs(inputValues, 'sso');                         
    });

    $(".create-model").on("click", function () {
        $('#SSOTypeTitleIcon').removeClass().addClass("cp-sign-on-type");
        isEditSSO = false;
        clearErrorMessage();
        $("#singleSignOnName, #SSOFormTypeID, #singleSignOnProps, #singleSignOnID").val("");
        let element = document.getElementById('singleSignOnHeaderType');

        if (element) {
            element.innerText = "Type";
        }
        $('#singleSignOnFormRenderingArea').empty();
        $('#saveButton').text('Save');
        form.steps('previous')
    });

    $('#saveButton').on('click', async function () {
        let res = await inputFormValidation('singlesignon');

        if (res) {
            let fd = await SSOSaveFormFields();
            fd.icon = singleSignOnIcon;
            const keys = Object.keys(fd);
            keys.forEach(key => {
                if (key.startsWith('f-')) {
                    delete fd[key];
                }
            });
            let formDataJson = JSON.stringify(fd);
            let hiddenInput = document.getElementById('singleSignOnProps');
            hiddenInput.value = formDataJson;

            if (!btnDisableSSO) {
                btnDisableSSO = true;
                $('#singleSignOnFormRenderingArea :input').prop('disabled', true);
                const form = $('#ssoForm')[0];
                const formData = new FormData(form);
                $('#singleSignOnFormRenderingArea :input').prop('disabled', false);

                let response = await infraCreateOrUpdate(RootUrl + SSOURL.createOrUpdate, formData);
                $('#CreateModal').modal('hide');
                btnDisableSSO = false;

                if (response?.success) {
                    notificationAlert("success", response?.data?.message);
                    getSSOType(SSOURL.getSingleSignOnList);
                    setTimeout(() => {
                        let selectedSSOType = $("#SSOFormTypeID :selected").val();
                        dataTableCreateAndUpdate($("#saveButton"), dataTable, $("#selectType"), selectedSSOType);
                    }, 2000)
                } else {
                    errorNotification(response);
                }
            }
        }
    });

    $('#singleSignOnList').on('click', '.ssoEditButton', async function () {
        let ssoData = $(this).data("sso");
        clearErrorMessage();
        isEditSSO = true;
        $('#saveButton').text('Update');
        $('#CreateModal').modal('show');
        form.steps('previous');

        let getSingleSignOnById = await infraGetRequestWithData(RootUrl + SSOURL.getSingleSignOnById, { id: ssoData });
        if (getSingleSignOnById && typeof getSingleSignOnById === 'object' && Object.keys(getSingleSignOnById).length > 0) {
            populateSSOModalFields(getSingleSignOnById);
        }
    });

    $('#singleSignOnList').on('click', '.ssoDeleteButton', function () {
        $("#deleteData").attr("title", $(this).data('ssoname'));
        $("#deleteData").text($(this).data("ssoname"));
        $("#textDeleteId").val($(this).data("ssoid"));
    });

    $("#confirmDeleteButton").on("click", async function () {
        const form = $('#deleteSSO')[0];
        const formData = new FormData(form);

        if (!btnDisableSSO) {
            btnDisableSSO = true;
            let response = await infraDeleteData(RootUrl + SSOURL.delete, formData); //commonfunctions.js  
            $("#DeleteModal").modal("hide");
            btnDisableSSO = false;

            if (response?.success) {
                notificationAlert("success", response?.data?.message);
                setTimeout(() => {
                    dataTableDelete(dataTable);
                }, 2000)
            } else {
                errorNotification(response);
            }
        }
    });

    let getFormMappingListByName = await infraGetRequestWithData(RootUrl + SSOURL.getFormMappingListByName, { name: 'single signon' });
    if (getFormMappingListByName?.length) {
        let SSOType = $('#SSOFormTypeID');
        let options = [];
        SSOType.empty().append($('<option>').val("").text(""));
        getFormMappingListByName?.forEach(function (item) {
            options.push($('<option>')
                .val(item?.formTypeId)
                .text(item?.formTypeName)
                .attr("formTypeId", item?.formTypeId)
                .attr("ssologo", item?.logo));
        });
        SSOType.append(options);
    }
    getSSOType(SSOURL.getSingleSignOnList);
});
