﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.DashboardViewModel.DashboardBusinessViewModel
@using ContinuityPatrol.Shared.Services.Helper
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "ServiceAvailability";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/dashboard.css" rel="stylesheet" />
@Html.AntiForgeryToken()
<div class="page-content pageblur">
    <div class="col-12">
        <h6 class="page_title mb-2">
            <i class="cp-business-service"></i><span>Service Availability</span>
        </h6>
    </div>
    <div class="row g-2">
        <div class="col-12 col-sm-12 col-md-4 col-xl-3 col-xxl-3">
            <div class="Card_Design_None mb-2 ">
                <div class="d-flex pb-2">
                    <div class="input-group rounded-1 border-0 bg-white shadow-sm">
                        <input id="search-bs" class="form-control bg-transparent" type="search" placeholder="Search Operational Service" />
                        <div id="searchFilter" style="display: inline-flex"></div>
                        <div class="input-group-text pe-2">
                            <div class="dropdown">
                                <i type="button" class="cp-filter" title="Filter" data-bs-toggle="dropdown" data-bs-auto-close="outside"></i>
                                <form class="dropdown-menu p-0">
                                    <div class="accordion accordion-flush filter-accordion" id="accordionFlushExample">
                                        <div class="accordion-item">
                                            <h2 class="accordion-header">
                                                <button id="btnAll" class="btn btn-sm btn-tresprent collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                                                    All
                                                </button>
                                            </h2>
                                            <div id="flush-collapseOne" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                                <div class="accordion-body p-0">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                                                    Status
                                                </button>
                                            </h2>
                                            <div id="flush-collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                                <div class="accordion-body p-0">
                                                    <ul class="list-group list-group-flush">
                                                        <li class="list-group-item" role="button" id="filterItem"><i class="cp-success me-2 text-success"></i>Available</li>
                                                        <li class="list-group-item" role="button" id="filterItem"><i class="cp-warning me-2 text-warning"></i>NotAvailable</li>
                                                        <li class="list-group-item" role="button" id="filterItem"><i class="cp-Impact me-2 text-danger"></i>MajorImpact</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                                                    Priority
                                                </button>
                                            </h2>
                                            <div id="flush-collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                                <div class="accordion-body p-0">
                                                    <ul class="list-group list-group-flush">
                                                        <li class="list-group-item" role="button" id="priorityItems"><i class="cp-up-doublearrow me-2 text-danger"></i>High</li>
                                                        <li class="list-group-item" role="button" id="priorityItems"><i class="cp-equal me-2 text-warning"></i>Medium</li>
                                                        <li class="list-group-item" role="button" id="priorityItems"><i class="cp-down-doublearrow me-2 text-info"></i>Low</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="companyId" data-companyid="@WebHelper.UserSession.CompanyId">
                    <span type="button" class="rounded-1 p-2 ms-1 bg-white shadow-sm loadBusinessTableData" title="Service Availability Overview" data-bs-toggle="modal" data-bs-target="#BusinessView-CardList"><i class="cp-full-screen"></i></span>
                </div>
                <div class="pe-1 businessData" id="businessData" style="height: calc(100vh - 148px); overflow-y: auto;">
                    @foreach (var BusinessView in Model.GetPaginatedBusinessViews)
                    {
                        <div type="button" class="card border data-businessId='@BusinessView.BusinessServiceId' BtnService mb-1 @if(BusinessView == Model.GetPaginatedBusinessViews.First()) {
        <text>Active-Card</text>
        }else{
                         } shadow-none"  data-businessId='@BusinessView.BusinessServiceId'  data-businessName='@BusinessView.BusinessServiceName' data-businessdetails='@Json.Serialize(BusinessView)' status="@BusinessView.Status" priority="@BusinessView.Priority">
                            @{
                                string normalizedStatus = BusinessView.Status.Replace(" ", "").ToLower();
                                bool readyStatus = BusinessView.IsDRReady;
                                bool dataLagExceed = BusinessView.IsDataLagExceed;
                                bool isPartial = BusinessView.IsPartial;
                                string iconClass = readyStatus && !dataLagExceed && isPartial ? "cp-protection-mode" : readyStatus && !dataLagExceed && !isPartial ? "cp-protection-mode" : !readyStatus && !dataLagExceed && isPartial ? "cp-partially-ready" : !readyStatus && !dataLagExceed && !isPartial ? "cp-shiled-error" : readyStatus && dataLagExceed && isPartial ? "cp-protection-mode" : !readyStatus && dataLagExceed && isPartial ? "cp-partially-ready" : "cp-shiled-error";
                                string iconText = readyStatus && !dataLagExceed && isPartial ? "success" : readyStatus && !dataLagExceed && !isPartial ? "success" : !readyStatus && !dataLagExceed && isPartial ? "warning" : !readyStatus && !dataLagExceed && !isPartial ? "danger" : readyStatus && dataLagExceed && isPartial ? "success" : !readyStatus && dataLagExceed && isPartial ? "warning" : "danger";
                                string iconTitle = readyStatus && !dataLagExceed && isPartial ? "Resiliency Ready" : readyStatus && !dataLagExceed && !isPartial ? "Resiliency Ready" : !readyStatus && !dataLagExceed && isPartial ? "Resiliency Partial Ready" : !readyStatus && !dataLagExceed && !isPartial ? "Resiliency Not Ready" : readyStatus && dataLagExceed && isPartial ? "Resiliency Ready" : !readyStatus && dataLagExceed && isPartial ? "Resiliency Partial Ready" : "Resiliency Not Ready";
                                //string iconText = readyStatus && dataLagExceed && isPartial ? "warning" : readyStatus && dataLagExceed == false ? "success" : "danger";
                                //string iconTitle = readyStatus && dataLagExceed && isPartial ? "Resiliency Partial Ready" : readyStatus && dataLagExceed == false ? "Resiliency Ready" : "Resiliency Not Ready";
                                string statusClass = normalizedStatus == "available" ? "success" :
                                normalizedStatus == "notavailable" ? "warning" : "danger";
                                string priorityText = BusinessView.Priority == 1 ? "High" :
                              BusinessView.Priority == 2 ? "Medium" :
                              BusinessView.Priority == 3 ? "Low" : "";
                                string priorityClass = BusinessView.Priority == 1 ? "text-danger" :
                                    BusinessView.Priority == 2 ? "text-warning" :
                                    BusinessView.Priority == 3 ? "text-info" : "";
                                string priorityStyle = BusinessView.Priority == 1 ? "bg-danger-subtle" :
                                        BusinessView.Priority == 2 ? "bg-warning-subtle" :
                                        BusinessView.Priority == 3 ? "bg-info-subtle" : "";
                            }
                                    <div class="card-header p-2 header">
                                        <span class="list-title">
                                              <i title="@(iconTitle)" class="@iconClass  text-@(iconText) me-1 fw-semibold"></i>
                                                        <span title="@BusinessView.BusinessServiceName">@BusinessView.BusinessServiceName</span> <span  text-primary  class="align-middle @priorityStyle @priorityClass badge  bg-primary ms-1">@priorityText</span>
                                            </span>
                                        <span class="d-flex gap-2 align-items-center text-light">
                                    @{
                                        string prSite = string.Empty;
                                        string drSite = string.Empty;
                                        dynamic infraObjectStatus = string.Empty;
                                        List<int> fList = new List<int>();
                                        string iconToDisplay = "cp-on-arrow";
                                        foreach (var infra in infraObjectStatus)
                                        {
                                            if (infra != null)
                                            {
                                                fList.Add(infra.DROperationStatus);
                                            }
                                        }
                                        if (fList.Contains(0) && fList.Contains(2))
                                        {
                                            iconToDisplay = "cp-on-arrow";
                                        }
                                        else if (fList.All(x => x == 0))
                                        {
                                            iconToDisplay = "cp-on-arrow";
                                        }
                                        else if (fList.All(x => x == 2))
                                        {
                                            iconToDisplay = "cp-off-arrow";
                                        }
                                    }
                                    @if (BusinessView.WorkflowIsRunning)
                                    {
                                        <a asp-area="ITAutomation" asp-controller="WorkflowExecution" asp-action="List"><i title="Available" class="cp-reload cp-animate text-primary"></i></a>
                                                               
                                    }

                                    <i title="@BusinessView.Status" class="cp-@(normalizedStatus == "available" ? "success" : normalizedStatus == "notavailable" ? "warning" : "affecteds") text-@(statusClass)"></i>
                                        </span>
                                    </div>
                                    <div class="card-body p-2 border-top">
                                @{
                                    double rpoThreshold = 0;
                                    double configuredRpo = 0;
                                    double percentage = 0;
                                    double CurrentRPOMinutes = 0;
                                    string gradientColor = "";
                                    double CurrentRTOMinutes = 0;
                                    int lastDigitInt = 0;
                                    if (BusinessView != null)
                                    {
                                        // Try parsing the RPOThreshold and ConfiguredRPO strings to double
                                        double.TryParse(BusinessView.RPOThreshold, out rpoThreshold);
                                        double.TryParse(BusinessView.ConfiguredRPO, out configuredRpo);
                                        string currentRPOString = BusinessView.CurrentRPO;
                                        string currentRTOString = BusinessView.CurrentRTO;
                                        if (!string.IsNullOrEmpty(currentRTOString) && currentRTOString != "00:00:00")
                                        {
                                            TimeSpan currentRTOTimeSpan;
                                            if (TimeSpan.TryParse(currentRTOString, out currentRTOTimeSpan))
                                            {
                                                // Calculate total minutes
                                                CurrentRTOMinutes = currentRTOTimeSpan.TotalMinutes;
                                                CurrentRTOMinutes = Math.Round(CurrentRTOMinutes);
                                            }
                                        }
                                        if (!string.IsNullOrEmpty(currentRPOString) && currentRPOString != "00:00:00")
                                        {
                                            TimeSpan currentRPOTimeSpan;
                                            if (TimeSpan.TryParse(currentRPOString, out currentRPOTimeSpan))
                                            {
                                                // Calculate total minutes
                                                CurrentRPOMinutes = currentRPOTimeSpan.TotalMinutes;
                                                CurrentRPOMinutes = Math.Round(CurrentRPOMinutes);
                                                string minutesString = CurrentRPOMinutes.ToString();
                                                lastDigitInt = (int)CurrentRPOMinutes;

                                                if (CurrentRPOMinutes != 0 && minutesString.Length > 1)
                                                {
                                                    char lastDigit = minutesString[minutesString.Length - 1];
                                                    lastDigitInt = int.Parse(lastDigit.ToString());
                                                }
                                            }
                                        }
                                        // Check if the parsed values are valid before performing the calculation
                                        if (rpoThreshold != 0)
                                        {
                                            double rawPercentage = (rpoThreshold * 100) / configuredRpo;
                                            percentage = Math.Truncate(rawPercentage);

                                            //  gradientColor = $"linear-gradient(90deg, #41c200 {CurrentRPOMinutes}%, #efefef {CurrentRPOMinutes - percentage}%)";
                                            if (CurrentRPOMinutes > 0)
                                            {
                                                if (CurrentRPOMinutes > configuredRpo)
                                                {
                                                    gradientColor = "linear-gradient(90deg, red, #efefef)";
                                                    gradientColor = $"linear-gradient(90deg, #FF0000 {100}%, #efefef {CurrentRPOMinutes - percentage}%)";
                                                }
                                                else if (CurrentRPOMinutes < rpoThreshold)
                                                {
                                                    gradientColor = "linear-gradient(90deg, #41c200, #efefef)";
                                                    gradientColor = $"linear-gradient(90deg, #41c200 {CurrentRPOMinutes}%, #efefef {CurrentRPOMinutes - percentage}%)";
                                                }
                                                else
                                                {
                                                    gradientColor = "linear-gradient(90deg, orange, #efefef)";
                                                    gradientColor = $"linear-gradient(90deg, orange {50 + lastDigitInt}%, #efefef {CurrentRPOMinutes - percentage}%)";
                                                }
                                            }
                                        }
                                    }
                                }
                                        <div class="d-flex gap-2">
                                            <div style="width:60%;">
                                                <h6 class="mb-3"><i class="cp-active-inactive text-custom-color me-1"></i><small>RPO</small></h6>
                                                <div class="d-grid">
                                                            <div class="d-flex align-items-end" style="line-height: 0.5;">
                                                            <div class="flex-fill">
                                                                    <div class="fs-6">@CurrentRPOMinutes&nbsp;<small class="fw-normal text-light fs-9">Mins</small></div>
                                                            </div>
                                                            <div class="flex-fill">
                                                            <div class="text-center">@(string.IsNullOrEmpty(BusinessView?.RPOThreshold) ? "NA" : BusinessView?.RPOThreshold)&nbsp;<small class="fw-normal text-light fs-9">Mins</small></div>

                                                        </div>
                                                            <div class="flex-fill text-end">
                                                                            <div class="">@(string.IsNullOrEmpty(BusinessView?.ConfiguredRPO) ? "NA" : BusinessView?.ConfiguredRPO) <small class="fw-normal text-light fs-9">Mins</small></div>
                                                            </div>
                                                        </div>
                                                        <div class="text-center">
                                                                <input id="range" 
                                                                    type="range" 
                                                                    data-actual="@(string.IsNullOrEmpty(BusinessView?.RPOThreshold))" 
                                                                    step="10" value="50" min="0" max="100" 
                                                                    oninput="limitSlider(this)" style="background: @gradientColor;">
                                                        </div>
                                                    <div class="d-flex">
                                                        <div class="flex-fill">
                                                            <small class="text-light">Current</small>
                                                        </div>
                                                        <div class="flex-fill text-center">
                                                            <small class="text-light">Threshold</small>
                                                        </div>
                                                        <div class="flex-fill text-end">
                                                            <small class="text-light">Configured</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="vr bg-dark-subtle"></div>
                                            <div style="width:40%;">
                                                <h6 class="mb-3"><i class="cp-active-inactive text-rto-color me-1"></i><small>RTO</small></h6>
                                                        <div class="d-grid align-content-between" style="height:44px;">
                                                            <div class="d-flex justify-content-between"  style="line-height: 0.5;">
                                                            <div class="fs-6">@CurrentRTOMinutes&nbsp;<small class="fw-normal text-light fs-9">Mins</small></div>
                                                            <div class="">@(string.IsNullOrEmpty(BusinessView?.ConfiguredRTO) ? "NA" : BusinessView?.ConfiguredRTO) <small class="fw-normal text-light fs-9">Mins</small></div>
                                                        </div>
                                                        <div class="d-flex justify-content-between">
                                                            <small class="text-light">Computed</small>
                                                            <small class="text-light">Configured</small>
                                                        </div>
                                                    </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                    }
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-12 col-md-6 col-xl-6 col-xxl-6">
            <div class="card Card_Design_None mb-2">
                <div class="card-header p-2 d-flex justify-content-between">
                    <span class="card-title">Resilience Interdependency</span>
                    <ul class="nav nav-pills bg-primary-subtle rounded-1 BusinessView-NavTab shadow-sm" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active px-2 py-1 rounded-1 small" id="pills-Datalag-tab" data-bs-toggle="pill" data-bs-target="#pills-Datalag" type="button" role="tab" aria-controls="pills-Datalag" aria-selected="true"><i class="cp-datalog me-1"></i>Datalag</button>
                        </li>
                        <li class="nav-item btn-sm" role="presentation">
                            <button class="nav-link px-2 py-1 rounded-1 small" id="pills-Availability-tab" data-bs-toggle="pill" data-bs-target="#pills-Availability" type="button" role="tab" aria-controls="pills-Availability" aria-selected="false"><i class="cp-success me-1"></i>Availability</button>
                        </li>
                        <li class="nav-item btn-sm" role="presentation">
                            <button class="nav-link px-2 py-1 rounded-1 small" id="pills-RTO-tab" data-bs-toggle="pill" data-bs-target="#pills-RTO" type="button" role="tab" aria-controls="pills-RTO" aria-selected="false"><i class="cp-RTO me-1"></i>RTO</button>
                        </li>
                    </ul>
                </div>
                <div class="card-body pt-2 p-2">
                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-Datalag" role="tabpanel" aria-labelledby="pills-Datalag-tab" tabindex="0">
                            <div class="row g-3">
                                <div class="col-6">
                                    <div class="header">
                                        <div class="small fw-semibold">Operational Functions</div>
                                    </div>
                                    <div id="BusinessFunctionsChart" style="height:110px;"></div>
                                </div>
                                <div class="col-6 border-start">
                                    <div class="header">
                                        <div class="small fw-semibold">InfraObjects</div>
                                    </div>
                                    <div id="InfraObjectsChart" style="height:110px;"></div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade show " id="pills-Availability" role="tabpanel" aria-labelledby="pills-Availability-tab" tabindex="0">
                            <div class="row">
                                <div class="col-6">
                                    <div class="header">
                                        <div class="small fw-semibold">Operational Functions</div>
                                    </div>
                                    <div id="AvailabilityBusinessFunctionsChart" style="height:110px;"></div>
                                </div>
                                <div class="col-6 border-start">
                                    <div class="header">
                                        <div class="small fw-semibold">InfraObjects</div>
                                    </div>
                                    <div id="AvailabilityInfraObjectsChart" style="height:110px;"></div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-RTO" role="tabpanel" aria-labelledby="pills-RTO-tab" tabindex="0">
                            <div class="row">
                                <div class="col-6">
                                    <div class="header">
                                        <div class="small fw-semibold">Operational Functions</div>
                                    </div>
                                    <div id="RTOBusinessFunctionsChart" style="height:110px;" role="button" onclick="GetDrDrillDetailsByBusinessService()"></div>
                                </div>
                                <div class="col-6 border-start">
                                    <div class="header">
                                        <div class="small fw-semibold">InfraObjects</div>
                                    </div>
                                    <div id="RTOInfraObjectsChart" style="height:110px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header d-flex align-items-center justify-content-between pb-0">
                        <span class="card-title">Operational Services View</span><i class="cp-full-screen" title="Operational Service Overview" role="button" data-bs-toggle="modal" data-bs-target="#BusinessTreeZoom"></i>
                    </div>
                    <div class="card-body p-0">
                        <div class="treewrapper d-flex" id="treewrapper" style="width:100%; height: calc(100vh - 306px);"></div>
                    </div>
                </div>
            </div>
        </div>
        <button id="btn_ShowHideModel" style="display:none;" data-bs-target="#DriftManagementModal" data-bs-toggle="modal"></button>
        <div class="col-12 col-sm-12 col-md-4 col-xl-3 col-xxl-3">
            <div class="card Card_Design_None border mb-1 h-20">
                <div class="card-header">
                    <span class="card-title">Operational Heat Map</span>
                </div>
                <div class="card-body pt-0 px-3 pb-2">
                    <div class="row row-cols-3 g-3 align-items-center h-100">
                        <div class="col">
                            <div>
                                <div class="d-flex align-items-center">
                                    <div class="position-relative impactDetails" role="button" type="Server">
                                        <img class="me-1" src="~/img/charts_img/datacenter/server.svg" loading="lazy" type="Server" height="35" alt="server count" title="server count"/>
                                        <span class=" serverDownCount impactDetails" type="Server" style="left:22px;"></span>
                                    </div>
                                    <span>
                                        <span>Server</span>
                                        <span class="d-flex align-items-center serverdone">
                                            <span class="card-title fw-semibold serverTotalCount"></span> <br />
                                        </span>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-flex align-items-center">
                                <div class="position-relative impactDetails" role="button" type="Database">
                                    <img class="me-1" src="~/img/charts_img/datacenter/database.svg" loading="lazy" type="Database" height="35" alt="database count" title="database count" />
                                    <span class=" databaseDownCount impactDetails" type="Database" style="left:22px;"></span>
                                </div>
                                <span>
                                    <span>Database</span>
                                    <span class="d-flex align-items-center databasedone">
                                        <small class="card-title fw-semibold databaseTotalCount"></small><br />
                                    </span>
                                </span>
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-flex align-items-center">
                                <div class="position-relative impactDetails" role="button" type="Replication">
                                    <img class="me-1" src="~/img/charts_img/datacenter/Replication.svg" loading="lazy" type="Replication" height="35" alt="Replication count" title="Replication count" />
                                    <span class=" replicationDownCount impactDetails" type="Replication" style="left:22px;"></span>
                                </div>
                                <span>
                                    <span>Replication</span>
                                    <span class="d-flex align-items-center replicationdone">
                                        <small class="card-title fw-semibold replicationTotalCount"></small><br />
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="d-grid" style="height: calc(100vh - 175px);">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header pb-0">
                        <span class="card-title">Data Flow Topology</span>
                    </div>
                    <div class="card-body pt-0 p-2">
                        <div class="d-flex align-items-center" id="DataCenter" style="height:100%; width:100%; zoom:90%;"></div>
                    </div>
                </div>
                <div class="card Card_Design_None mb-0">
                    <div class="card-header header">
                        <div class="d-grid">
                            <span class="card-title">Drill Overview</span>
                            <small class="text-light">(Current Year)</small>
                        </div>
                        <span><small class="text-light">Last Drill Executed</small> : <span class="" id="LastDrillDate">24-01-24</span></span>
                    </div>
                    <div class="card-body py-0 ps-0">
                        <div class="d-flex align-items-center">
                            <div id="DrillSummary_chart" role="button" onclick="GetDrillOverview()" style="height:100%; width:100%;"></div>
                            <div class="ms-3">
                                <div>
                                    <span class="fs-6 text-success" style="line-height:1;" id="RTOAchievd">0</span><br />
                                    <small class="text-light">RTO&nbsp;Achieved</small>
                                </div>
                                <div>
                                    <span class="fs-6 text-danger" style="line-height:1;" id="RTOExceeded">0</span><br />
                                    <small class="text-light">RTO&nbsp;Exceeded</small>
                                </div>
                                <div>
                                    <span class="fs-6" style="line-height:1; color:orange" id="RTONotApplicable">0</span><br />
                                    <small class="text-light">Not Applicable</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Business Tree Overview Modal -->
<div class="modal fade" id="BusinessTreeZoom" tabindex="-1" data-bs-keyboard="false" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-business-service"></i><span>Operational Service Overview</span></h6>
                <button type="button" class="btn-close ms-2 " data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body py-2">
                <div class="card-body pt-0 pb-1" style="height:500px;">
                    <div class="Infrastructure-chart" id="wrapper1" style="width:1250px; height:100%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Business Service Overview Modal -->
<div class="modal fade" id="BusinessView-CardList" tabindex="-1" data-bs-keyboard="false" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="BusinessOverviewModal" />
</div>

<!-- Drift Management Modal -->
<div class="modal fade" id="DriftManagementModal" data-bs-keyboard="false" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollabel">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title">
                    <i class="cp-drift-management me-1"></i> <span>
                        Drift Job Management
                    </span>
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-3 g-3 mb-2">
                    <div class="col">
                        <div class="d-flex align-items-center">
                            <span class="circle me-2 p-2" style="background-color:var(--bs-gray-200)"><i class="cp-bag text-primary fs-5 fw-semibold"></i> </span> <span> <span id="totalbs" class="fs-6 fw-semibold"></span><br> <span class="text-light">Operational Services</span> </span>
                        </div>
                    </div>
                    <div class="col">
                        <div class="d-flex align-items-center"><span class="circle me-2 p-2" style="background-color:var(--bs-gray-200)"><i class="cp-success text-success fs-5 fw-semibold"></i> </span> <span> <span id="non_impactid" class="fs-6 fw-semibold"></span><br> <span class="text-light">Non - Impacted</span> </span></div>
                    </div>
                    <div class="col">
                        <div class="d-flex align-items-center"><span class="circle me-2 p-2" style="background-color:var(--bs-gray-200)"><i class="cp-affecteds text-danger fs-5 fw-semibold"></i> </span> <span> <span id="impacted" class="fs-6 fw-semibold"></span><br> <span class="text-light">Impacted</span> </span></div>
                    </div>
                </div>
                <div class="card Card_Design_None">
                    <div class="card-header p-0 " style="background-color:var(--bs-gray-200)">
                        <div class="list-group list-group-horizontal fw-semibold">
                            <div class="border-0 w-25 flex-fill p-2 list-group-item">Operational Service</div>
                            <div class="border-0 w-25 flex-fill p-2 list-group-item">Total InfraObject</div>
                            <div class="border-0 w-25 flex-fill p-2 list-group-item">Non Impacted infraobjects</div>
                            <div class="border-0 w-25 flex-fill p-2 list-group-item">Impacted infraobjects</div>
                            <div class="border-0 rounded-0 p-2 text-center list-group-item" style="width:7%"></div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="myGroup"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="impactModalModal" data-bs-keyboard="false" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollabel" style="min-height:400px;width:1300px">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i id="page_Icon" class="cp-service-heatmap-database"></i><span class="page_titleheatmap">Database Heatmap Details</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body" style="min-height: 400px;">
                <table id="HeatmapTable" class="table table-hover datatable" style="table-layout: fixed;">
                </table>
            </div>
        </div>
    </div>
</div>

@* Executed Drill Details  Modal*@
<div class="modal fade" id="ExecutedDrillModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollabel" style="min-height:400px;width:1300px">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-drill-executed"></i><span class="page_titleDrill">Executed Drill Details - CPOS_DS</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div id="ExecutedDrillModalAppend"></div>
        </div>
    </div>
</div>

<script src="~/lib/d3charts/d3.min.js"></script>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/lib/amcharts4/dataviz.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/js/dashboard-charts/businessview-dashboard/availabilitybusinessfunctions-chart.js"></script>
<script src="~/js/dashboard-charts/businessview-dashboard/availabilityinfraobjects-chart.js"></script>
<script src="~/js/dashboard-charts/businessit_charts.js"></script>
<script src="~/js/dashboard-charts/datacenter.js"></script>
<script src="~/js/dashboard-charts/businessview-dashboard/infraobjects-chart.js"></script>
<script src="~/js/dashboard-charts/businessview-dashboard/drillsummary-chart.js"></script>
<script src="~/js/dashboard-charts/businessview-dashboard/rtobusinessfunction.js"></script>
<script src="~/js/dashboard-charts/businessview-dashboard/rtoinfraobjects-chart.js"></script>
<script src="~/js/dashboard-charts/businessview-dashboard/drbusinessfunctions-chart.js"></script>
<script src="~/js/dashboard-charts/businessview-dashboard/drinfraobjects-chart.js"></script>
<script src="~/js/dashboard-charts/businessview-dashboard/businessfunction.js"></script>
<script src="~/js/dashboard-charts/DashboardBusinessView.js"></script>