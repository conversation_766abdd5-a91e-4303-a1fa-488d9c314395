﻿let mappingModuleMethods = {
    GetZoneList: "CyberResiliency/Component/GetSiteList",
    GetMappingList: "CyberResiliency/manage/GetMappingList",
    GetComponentsBySite: "CyberResiliency/manage/GetComponentsBySiteId",
    GetServerBySiteId: "CyberResiliency/manage/GetServersBySiteId",
    GetComponentsGroup: "CyberResiliency/manage/GetComponentsGroup",
    createOrUpdate: "CyberResiliency/manage/CreateOrUpdate",
    GetAirgapList: "CyberResiliency/manage/GetAirgapList",
    Delete: "CyberResiliency/manage/Delete",
    GetCyberAirGapList: "CyberResiliency/manage/GetCyberAirGapService"
}

let groupColorArray = ["#844896", "#ff7800", "#5db7e1", "#de5fa9", "#cef47a"];
let siteColorArray = ['#bd77f4', '#ef627e', '#f6a151', '#7c86f9', '#c9c338']

let topInc = []
let isSourceTargetId = []
let globalAirgapArray = []
let airGapArray = []
let globalCyberMappingId = ''
let globalZoneId = ''
let convertToBase64 = '';
let reduceSize = 0

$('#cyberContainer').on("contextmenu", function (e) {

    e.preventDefault();
    let menu = $(".contextMenu");

    if (menu.is(":visible")) {       
        menu.hide()
    } else {
        menu.hide();
        $('#btnDelete, #btnEdit, #btnRemove').hide();

        if ($('.selected')?.length) $('#btnDelete').show();

        if (e?.target?.classList?.contains('cyberAirgapImage')) {

            let airgapId = e?.target?.getAttribute('airgapid');
            $('#btnEdit, #btnRemove').attr('airgapid', airgapId).show();
        }
        menu.fadeIn(100);

        let pageX = e?.pageX;
        let pageY = e?.pageY;
        let mwidth = $(".UlContextBtn").width();
        let mheight = $(".UlContextBtn").height() + 20;
        let screenWidth = $(window).width();
        let screenHeight = $(window).height();
        let scrTop = $(window).scrollTop();

        if (pageX + mwidth > screenWidth) {
            pageX = pageX - mwidth
        }
        if (pageY + mheight > screenHeight + scrTop) {
            pageY = pageY - mheight;
        }
        menu.css({
            top: pageY - 50,
            left: pageX
        });
    }
})

$('#btnDelete').on('click', function (e) {
    if ($('.selected.siteDraggableContainer')?.length) {
        let getId = $('.selected.siteDraggableContainer')[0]?.id
        if ($(`#${getId}`)?.find('.flowLineAdded')?.length) {
            notificationAlert('warning', 'deletion is not allowed when the airgap is connected')
        } else {
            $(`#${getId}`)?.remove();
        }
    }
    $('.contextMenu').hide()
})

$('#btnEdit').on('click', function (e) {
    let isAirgapId = $(this)?.attr('airgapid')
    $('#airgapList').val(isAirgapId)

    let airGapConfig = $('.cyberAirgapImage')
    $.each(airGapConfig, function (idx, obj) {
        let airGapId = obj?.getAttribute('airgapid')
        if (isAirgapId !== airGapId) $(`#airgapList option[value='${airGapId}']`).attr('disabled', true)
    })

    $('#loadAirgapModal').modal('show')
    $('.contextMenu').hide()
})

$('#btnRemove').on('click', function (e) {
    let airgapId = ''

    if (e?.target?.closest('li')) airgapId = e?.target?.closest('li')?.getAttribute('airgapid')
    else airgapId = e?.target?.getAttribute('airgapid')

    if (airgapId) {

        const targetImg = $(`img[airgapid="${airgapId}"]`);
        if (targetImg) targetImg?.remove()

        $('.flowLineAdded').each(function () {
            if ($(this)?.attr('details')) {
                let data = $(this)?.attr('details')
                data = data && JSON.parse(data);

                if (data && Array.isArray(data) && data?.length) {
                    data.forEach((d, i) => {
                        if (d?.airgapId === airgapId) {
                            let sourceContainer = $(`#${d?.sourceId}`)?.parents('.siteDraggableContainer')[0]?.id
                            let targetContainer = $(`#${d?.targetId}`)?.parents('.siteDraggableContainer')[0]?.id

                            let sourceContainerOriginalPorts = JSON.parse($(`#${sourceContainer}`).attr('portDetails') || '[]');
                            let targetContainerOriginalPorts = JSON.parse($(`#${targetContainer}`).attr('portDetails') || '[]');

                            sourceContainerOriginalPorts = sourceContainerOriginalPorts?.length ? sourceContainerOriginalPorts.filter((ports) => ports?.airgapId !== d?.airgapId) : [];
                            targetContainerOriginalPorts = targetContainerOriginalPorts?.length ? targetContainerOriginalPorts.filter((ports) => ports?.airgapId !== d?.airgapId) : [];

                            $(`#${sourceContainer}`).attr('portDetails', JSON.stringify(sourceContainerOriginalPorts));
                            $(`#${targetContainer}`).attr('portDetails', JSON.stringify(targetContainerOriginalPorts));

                            $(`#${d?.flowLineId}`).remove();
                            $(`#${d?.targetId}`).removeClass('flowLineAdded')

                            data.splice(i, 1)
                            if (!data?.length) $(this).removeClass('flowLineAdded')                          
                        }
                    });

                    $(this).attr('details', JSON.stringify(data))
                }
            }
        });

        $('#btnEdit').removeAttr('airgapid')
        $('.componentContainer').removeClass('source_selected target_selected selected')
        $('.contextMenu').hide()
    }
})

const getCyberComponentGroupList = async (siteId) => {
    let filteredArray = []
    await $.ajax({
        type: "GET",
        url: RootUrl + mappingModuleMethods?.GetComponentsGroup,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.data) {
                if (Array.isArray(result?.data) && result?.data?.length) {
                    filteredArray = result?.data?.filter((d) => d?.siteId === siteId)
                }
            }
        }
    })
    return filteredArray;
}

const getCyberSiteList = async () => {
    $(`#cyberZoneList`).empty();

    await $.ajax({
        type: "GET",
        url: RootUrl + mappingModuleMethods?.GetZoneList,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.data) {
                if (Array.isArray(result?.data) && result?.data?.length) {
                    
                    let zoneoptions = '<option></option>'
                    result?.data?.forEach((d) => {
                        zoneoptions += `<option value="${d?.id}">${d?.name}</option>`
                    })
                    $(`#cyberZoneList`).append(zoneoptions)
                    $(`#cyberZoneList option`).attr('disabled', false)
                    let siteExistCont = $('.siteNameCont')
                    $.each(siteExistCont, function (idx, obj) {
                        $(`#cyberZoneList option[value='${obj?.id}']`).attr('disabled', true)
                    })
                }
            }
        }
    })
}

const getCyberAirgapList = async () => {
    $(`#airgapList`).empty();

    await $.ajax({
        type: "GET",
        url: RootUrl + mappingModuleMethods?.GetAirgapList,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.data) {
                if (Array.isArray(result?.data) && result?.data?.length) {
                    globalAirgapArray = result?.data             
                    let zoneoptions = '<option></option>'
                    result?.data?.forEach((d) => {
                        zoneoptions += `<option value="${d?.id}">${d?.name}</option>`
                    })
                    $(`#airgapList`).append(zoneoptions)

                }
            }
        }
    })
}

getCyberAirgapList();

const getRandomId = (value) => {
    return value + "_" + Math.floor(Math.random() * 11110 * Math.random() * 103180)
}

$(function () {
    $('.draggableCont').draggable({ revert: "invalid", helper: 'clone' });

    $('.siteDraggableContainer').draggable({ containment: '#diagramaticContainer' });

    $('#diagramaticContainer').droppable({

        accept: '.draggableCont, .siteDraggableContainer',
        drop: function (event, ui) {

            isSourceTargetId = []
            if (ui?.draggable[0]?.innerText?.toLowerCase() === 'zone') {
                createZoneContainer()
            }
            checkCanvasPosition()
        }

    })
});

const checkCanvasPosition = () => {
    $('.cyberAirgapImage').remove()
    let canvasData = $('.flowLineAdded')

    for (let i = 0; i < canvasData?.length; i++) {
        let Id = canvasData[i]?.id;

        if ($(`#${Id}`).attr('details')?.length) {
            let canvasDetails = JSON.parse($(`#${Id}`)?.attr('details'))

            if (canvasDetails?.length) {
                canvasDetails?.forEach((d) => {
                    let flowLineId = d?.flowLineId
                    let sourceContainerId = $(`#${d?.sourceId}`).parents('.siteDraggableContainer')[0]?.id
                    let targetContainerId = $(`#${d?.targetId}`).parents('.siteDraggableContainer')[0]?.id

                    $(`#${flowLineId} polyline`).remove();
                    if (sourceContainerId && !isSourceTargetId.includes(sourceContainerId)) {
                        isSourceTargetId.push(sourceContainerId)
                        $(`#${sourceContainerId}`).removeAttr('portDetails')
                    }
                    if (targetContainerId && !isSourceTargetId.includes(targetContainerId)) {
                        isSourceTargetId.push(targetContainerId)
                        $(`#${targetContainerId}`).removeAttr('portDetails')
                    }

                    let endId = $(`#${flowLineId} defs`)?.children()?.first()[0]?.id
                    connectSVGLine(flowLineId, d?.sourceId, d?.targetId, d?.status, d?.airgapId, d?.airgapName, d?.color, endId)
                })
            }
        }
    }
}

// Resizable initialization for elements already in the DOM
$(function () {
    $("#checkCont").resizable({
        handles: "n, e, s, w",
        resize: function (event, ui) {
            console.log(event, ui)
        }
    });
});

const createZoneContainer = () => {
    let zoneId = getRandomId('zone');
    let containerId = getRandomId('container')

    let zoneHtml = `<div class="siteDraggableContainer" id="${containerId}" style="width:fit-content; left:200px">
    <div class="card drag-card zoneContainer mb-0" id="${zoneId}" style="min-height:150px;min-width:250px;width:100%;max-height:fit-content">
     <div class="card-header siteNameCont card-title text-end p-2">
      <button type="button" class="btn-close fs-8 removeNewZone" aria-label="Close"></button>
     </div>
    <div class="card-body zone-card pt-0 p-2">
        <div class="text-center">
            <button class="btn btn-sm btn-primary drag-btn addCyberZone" zone="${zoneId}"><i class="cp-add" ></i></button>
            <p class="mb-0 mt-2">Choose Zone</p>
        </div>
    </div>
</div>
</div>`;

    globalZoneId = zoneId

    if ($('#cyberContainer')?.children()?.length) {
        $('#cyberContainer').children().last().remove();
        $('#cyberContainer').append(zoneHtml).append('<div class="emptyClass" style="height:10px;width:100%"></div>');
    } else {
        $('#cyberContainer').append('<div class="emptyClass" style="height:50px;width:100%"></div>').append(zoneHtml).append('<div class="emptyClass" style="height:50px;width:100%"></div>');
    }

    setTimeout(() => {
        $(`#${zoneId}`).resizable({
            handles: "n, e, s, w",
        });
        $('.siteDraggableContainer').draggable()
    }, 300)
}

$(document).on('click', '.addCyberZone', function () {
    getCyberSiteList()
    globalZoneId = $(this)?.attr('zone')
    $('#cyberZoneModal').modal('show')
})

$(document).on('click', '.removeNewZone', function () {

    let getParent = $(this)?.closest('.siteDraggableContainer')
    if (getParent) getParent?.remove()
})

const getComponentMappingList = async () => {
    $('#cyberContainer')?.empty()

    await $.ajax({
        type: "GET",
        url: RootUrl + mappingModuleMethods?.GetMappingList,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.data) {
                if (Array.isArray(result?.data) && result?.data?.length) {
                    let parsedData = result?.data[0]?.properties && JSON.parse(result?.data[0]?.properties)
                    globalCyberMappingId = result?.data[0]?.id
                    setComponentmapping(parsedData)
                    $('#btnSaveCyberMapping').text('Update').attr('title', 'Update')
                }
            }
        }
    })
}

getComponentMappingList();

const setComponentmapping = (mappingData) => {
    let airgapArr = []
    let componentCont = ''
    let positionObj = []
    reduceSize = 0;

    mappingData?.length && mappingData?.forEach((d, i) => {
        let zoneId = getRandomId('zone');
        let containerId = getRandomId('container')
        let parsedData = d?.properties || []
        let parsedDataLength = parsedData?.length
        let getSiteColor = d?.siteColor

        if (d?.airgapArray?.length && globalAirgapArray?.length) {
            d.airgapArray.forEach(j => {
                const matched = globalAirgapArray.find(i => i?.name === j?.airgapName);
                if (matched) {
                    airgapArr.push({
                        airgapName: j?.airgapName, svgId: j?.svgId, color: j?.color, sourceId: j?.sourceId,
                        status: j?.status, targetId: j?.targetId, airgapId: matched?.id });
                }
            });
        }

        positionObj.push({ id: containerId, top: d?.position?.top, left: d?.position?.left })

        componentCont += `<div class="siteDraggableContainer" id="${containerId}" style="width:${parsedDataLength >= 3 ? '40%' : 'fit-content'}">
    <div class="card drag-card zoneContainer mb-0" id="${zoneId}" style="width:100%;border:1px solid ${getSiteColor}">
     <div class="card-header siteNameCont card-title" id="${d?.siteId}">${d?.siteName}</div>
    <div class="card-body pt-0 p-2">`

        for (let i = 0; i < parsedDataLength; i++) {
            if (parsedData[i]?.hasOwnProperty('isGroup')) {

                let getGroupColor = parsedData[i]?.groupColor
                let ParallelId = getRandomId('parallel')
                componentCont += `<div class='row w-50 mx-0 px-2'><div class='parallelCont p-2 w-100' id="${ParallelId}" style="border:1px solid ${getGroupColor}"><div class="w-100 fw-semibold compGroupName px-2" id="${parsedData[i]?.id}">${parsedData[i]?.name}</div>`

                parsedData[i]?.groupArray?.forEach((d) => {
                    componentCont += `<div class="componentContainer ${parsedData[i]?.groupArray?.length > 1 ? 'w-50' : ''} mb-1" id="${d?.id}">
                                <div class="text-center">
                                    <p class="mb-0 fs-10 fw-semibold componentName">${d?.name}</p>
                                    <div class="position-relative componentIcon" id="${getRandomId('component')}" serverType="${d?.serverType}">
                                        <img src=${d?.icon} height="30px" width="30px" />
                                       <span class="componentCount ${!Number(d?.count) ? 'd-none' : ''}" style="position: absolute;bottom: -3px;right: 28px;background-color: #0d6efd;padding: 2px 5px;border-radius: 50%;color: #fff;font-size: 10px;" value="${d?.count}">+${d?.count}</span>
                                    </div>
                                </div>
                            </div>`
                });
                componentCont += `</div></div>`;
            }
        }

        componentCont += `<div class="row nonGroupComps ${parsedDataLength >= 8 ? '' : parsedDataLength >= 5 ? '' : ''} mx-0" >`

        for (let i = 0; i < parsedDataLength; i++) {
            if (!parsedData[i]?.hasOwnProperty('isGroup')) {

                let componentId = getRandomId('component')
                componentCont += `<div class="componentContainer ${parsedDataLength == 1 ? 'col-12' : parsedDataLength < 3 ? 'col-6' : (parsedDataLength == 3) ? 'col-4' : 'col-3'} mb-1" id="${parsedData[i]?.id}">
                        <div class="text-center">
                            <p class="fw-semibold fs-9 componentName mb-0">${parsedData[i]?.name}</p>
                            <div class="position-relative componentIcon" id="${componentId}" serverType="${parsedData[i]?.serverType}">
                                <img src=${parsedData[i]?.icon} height="30px" width="30px"/>  
                                <span class="componentCount ${!Number(parsedData[i]?.count) ? 'd-none' : ''}" style="position: absolute;bottom: -8px;right: ${parsedDataLength > 2 ? '25' : '0'}px;padding: 2px 5px;border-radius: 50%;color: #0d6efd;font-size: 11px;" value="${parsedData[i]?.count}">+${parsedData[i]?.count}</span>
                            </div>
                        </div>
                    </div>`
            }
        }
        componentCont += `</div></div></div></div>`

        setTimeout(() => {
            $(`#${containerId}`).resizable({
                handles: "n, e, s, w",
            });
        }, 300)

    })

    $('#cyberContainer').append('<div class="emptyClass" style="height:50px;width:100%"></div>').append(componentCont).append('<div class="emptyClass" style="height:50px;width:100%"></div>')
    $('#cyberContainer').children().not('.emptyClass').last().prev().css('top', '30px')
 
    setTimeout(() => {
        $(`.siteDraggableContainer`).draggable()
        setTimeout(() => {
            positionObj.forEach((d, i) => {
                $(`#${d?.id}`).css('left', `${d?.left}px`)
                $(`#${d?.id}`).css('top', `${d?.top}px`)
            })
        }, 100)

        setTimeout(() => {
            if (airgapArr?.length) createConnectionFlow(airgapArr)           
        }, 800)
    }, 300)

}

const createConnectionFlow = (airgapArr) => {

    airgapArr.forEach((d) => {
        let flowLineId = getRandomId('flowline');
        let details;
        let data = { airGapId: d?.airgapId }

        $.ajax({
            type: "GET",
            url: RootUrl + mappingModuleMethods?.GetCyberAirGapList,
            data: data,
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result?.success) {
                    details = { sourceId: d?.sourceId, targetId: d?.targetId, status: result?.data?.status, airgapId: d?.airgapId, airgapName: d?.airgapName, color: d?.color, flowLineId: flowLineId }

                    if ($(`#${d?.sourceId}`).attr('details')?.length) {
                        let airGapDetails = JSON.parse($(`#${d?.sourceId}`).attr('details')) || []
                        airGapDetails.push(details)
                        $(`#${d?.sourceId}`).attr('details', JSON.stringify(airGapDetails))
                    } else {
                        let detailsArray = []
                        detailsArray.push(details)
                        $(`#${d?.sourceId}`).attr('details', JSON.stringify(detailsArray))
                    }

                    $(`#${d?.sourceId}, #${d?.targetId}`).addClass('flowLineAdded').attr('flowLineId', flowLineId)
                    $(`#${d?.sourceId}`).attr('partnerId', d?.targetId)
                    $(`#${d?.targetId}`).attr('partnerId', d?.sourceId)
                    airGapArray.push({ source: d?.sourceId, target: d?.targetId })

                    let endArrowId = getRandomId('arrow')

                    let svgLine = `<svg id="${flowLineId}" width="${$('#cyberContainer').width()}" height="${$('#cyberContainer').height()}" style="position:absolute;"> <defs>
                        <marker id='${endArrowId}' markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                            <path d="M0,0 L10,5 L0,10 Z" fill="${d.color}" />
                        </marker>
                      </defs></svg>`;

                    $('#cyberContainer').prepend(svgLine)

                    let getAirGapId = globalAirgapArray?.length && globalAirgapArray.filter((k) => k?.name === d?.airgapName)
                    let airGapid = d?.airgapId ?? getAirGapId[0]?.id

                    connectSVGLine(flowLineId, d?.sourceId, d?.targetId, result?.data?.status, airGapid, d?.airgapName, d?.color, endArrowId);
                }
            }
        })
    })
}

$('#cyberZoneList').on('input', function (e) {
    if (e?.target?.value) $('#cyberZoneError').text('').removeClass('field-validation-error');
})

$('#airgapList').on('input', function (e) {
    if (e?.target?.value) $('#airGapError').text('').removeClass('field-validation-error');
})

$('#btnLoadComponents').on('click', async function () {
    let selectedZoneId = $('#cyberZoneList :selected').val()

    if (!selectedZoneId) {
        $('#cyberZoneError').text('select zone').addClass('field-validation-error');
    } else {
        let componentGroupData = await getCyberComponentGroupList(selectedZoneId)

        let data = { siteId: selectedZoneId }

        await $.ajax({
            type: "GET",
            url: RootUrl + mappingModuleMethods?.GetServerBySiteId,
            data: data,
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result?.data && Array.isArray(result?.data)) {
                    let compHtml = '';
                    let data = result?.data
                    let modifiedArray = []

                    componentGroupData?.length && componentGroupData.forEach((d) => {
                        let parsedDetail = d?.componentProperties ? JSON.parse(d?.componentProperties) : []
                        let groupComponents = []
                        if (parsedDetail?.length) {
                                parsedDetail.forEach((k) => {
                                    let findind = data?.findIndex((t) => t?.id === k?.id)
                                    if (findind !== -1) {
                                        groupComponents.push(data[findind])
                                        data.splice(findind, 1)
                                    }
                                })
                        }
                        modifiedArray.push({ id: d?.id, isGroup: true, name: d?.groupName, groupArray: groupComponents })
                    })

                    let finalData = modifiedArray.concat(data);
                    $(`#${globalZoneId} .card-body`).empty();

                    if (finalData?.length) {
                        for (let i = 0; i < finalData?.length; i++) {

                            if (finalData[i]?.hasOwnProperty('isGroup')) {
                                let findDuplicateGroupComp = []
                                let ParallelId = getRandomId('parallel')
                                let getGroupRandomColor = groupColorArray[Math.floor(Math.random() * groupColorArray?.length)];
                                compHtml = `<div class='row justify-content-center w-50 mx-0 px-2'><div class='parallelCont p-2 w-100' id="${ParallelId}" style="border:1px solid ${getGroupRandomColor}"><div class="w-100 fw-semibold compGroupName px-2" id="${finalData[i]?.id}">${finalData[i]?.name}</div>`
                                
                                finalData[i]?.groupArray?.length && finalData[i]?.groupArray?.forEach((k) => {
                                    let filtered = findDuplicateComp.filter((d) => d?.serverType === k?.serverType)
                                    if (!filtered?.length) {
                                        k['count'] = 0
                                        findDuplicateComp.push(k)
                                    } else {
                                        let getIndex = findDuplicateComp?.length && findDuplicateComp.findIndex((q) => q?.serverType === k?.serverType)
                                        if (getIndex !== -1) findDuplicateComp[getIndex]['count']++
                                    }
                                })

                                findDuplicateGroupComp?.length && findDuplicateGroupComp.forEach((d) => {

                                    let serverType = d?.roleType?.toLowerCase()
                                    let parentImage = getImageByType(serverType)
                                    
                                    compHtml += `<div class="componentContainer w-50 p-1" id="${d?.id}">
                                <div class="text-center">
                                    <p class="mb-1 fs-9 fw-semibold componentName">${d?.name}</p>
                                    <div class="position-relative componentIcon" id="${getRandomId('component')}" serverType="${serverType}">
                                        <img src=${"/.." + parentImage} height="30px" width="30px" />
                                          <span class="componentCount ${!Number(d?.count) ? 'd-none' : ''}" style="position: absolute;bottom: 0;right: 32%;" value="${d?.count}">+${d?.count}</span>
                                    </div>
                                </div>
                            </div>`
                                });

                                compHtml += `<div><div>`;
                                $(`#${globalZoneId} .card-body`).append(compHtml)
                            }
                        }

                        let compHtml1 = '';
                        let findDuplicateComp = []

                        finalData?.length && finalData.forEach((k) => {

                            if (!k.hasOwnProperty('isGroup')) {
                                let filterd = findDuplicateComp?.length && findDuplicateComp.filter((d) => d?.serverType === k?.serverType)
                                if (!filterd?.length) {
                                    k['count'] = 0
                                    findDuplicateComp.push(k)
                                } else {
                                    let getIndex = findDuplicateComp.findIndex((q) => q?.serverType === k?.serverType)
                                    if (getIndex !== -1) findDuplicateComp[getIndex]['count']++
                                }
                            }

                        })

                        for (let i = 0; i < findDuplicateComp?.length; i++) {
                            if (!findDuplicateComp[i]?.hasOwnProperty('isGroup')) {

                                let serverType = findDuplicateComp[i]?.roleType?.toLowerCase()
                                let parentImage = getImageByType(serverType)

                                compHtml1 += `<div class="componentContainer mb-1 ${findDuplicateComp?.length == 1 ? 'col-12' : findDuplicateComp?.length < 3 ? 'col-6' : (findDuplicateComp?.length == 3) ? 'col-4' : 'col-3'}" id="${findDuplicateComp[i]?.id}">
                        <div class="text-center">
                            <p class="mb-1 fs-9 fw-semibold componentName">${findDuplicateComp[i]?.name}</p>
                            <div class="position-relative componentIcon" id="${getRandomId('component')}" serverType="${serverType}">
                                 <img src=${"/.." + parentImage} height="30px" width="30px"/>   
                                 <span class="componentCount ${!Number(findDuplicateComp[i]?.count) ? 'd-none' : ''}" style="position: absolute;bottom: 0;right: 32%;" value="${findDuplicateComp[i]?.count}">+${findDuplicateComp[i]?.count}</span>
                            </div>
                        </div>
                    </div>`
                            }
                        }

                        let componentCont = `<div class="row nonGroupComps h-100 align-items-center">${compHtml1}</div>`
                        if (findDuplicateComp?.length >= 3) $(`#${globalZoneId}`).closest('.siteDraggableContainer').css('width', '40%') 
                        $(`#${globalZoneId}`).find('.card-body').removeClass('zone-card').append(componentCont)
                        let siteName = $('#cyberZoneList :selected').text()
                        $(`#${globalZoneId}`).find('.siteNameCont').attr('id', selectedZoneId).removeClass('text-end').text(siteName)

                        let getRandomColor = siteColorArray[Math.floor(Math.random() * siteColorArray.length)];
                        $(`#${globalZoneId}`).css({
                            border: `1px solid ${getRandomColor}`,
                            minHeight: '',
                            minWidth: ''
                        })
                        $('#cyberZoneModal').modal('hide')
                    } else {
                        notificationAlert('warning', 'This zone has no hosts')
                        $(`#${globalZoneId}`).remove();
                        $('#cyberZoneModal').modal('hide')
                    }
                }
            }
        })
    }
})

function getImageByType(serverType = '') {
    return {
        database: "/img/charts_img/datacenter/database.svg",
        server: "/img/charts_img/datacenter/server.svg",
        storage: "/img/charts_img/datacenter/storage.svg",
        thirdparty: "/img/charts_img/datacenter/thirdparty.svg",
        virtualization: "/img/charts_img/datacenter/virtualization.svg",
        application: "/img/charts_img/datacenter/application.svg",
        dns: "/img/charts_img/datacenter/dns.svg"
    }[serverType] || "/img/charts_img/datacenter/Switches.svg";
}

$(document).on('click', '.componentContainer ', function (e) {
    e.stopPropagation();

    $('.cyberAirgapTooltip').remove();
    $('.siteDraggableContainer').removeClass('selected')
    if (!$('.selected')?.length) $('.componentContainer').removeClass('source_selected target_selected selected')

    if ($(this)?.hasClass('selected')) {
        $(this).removeClass('source_selected target_selected selected')
    } else {
        if ($('.selected')?.length < 2) {
            if (!$('.selected')?.length) $(this).addClass('source_selected selected')
            else if ($('.selected')?.length) $(this).addClass('target_selected selected')
        }
    }

})

$(document).on('click', '.siteDraggableContainer ', function (e) {
    e.stopPropagation();
    $('.cyberAirgapTooltip').remove();

    if ($(this)?.hasClass('selected')) {
        $(this).removeClass('selected')
    } else {
        $('.componentContainer').removeClass('source_selected target_selected selected')
        $('.siteDraggableContainer').removeClass('selected')
        if (!$(this)?.find('.removeNewZone')?.length) $(this).addClass('selected')
    }

})

const showAirGapDetails = (airGapid) => {
    let filterdAirGap = globalAirgapArray?.length && globalAirgapArray.filter((d) => d?.id === airGapid)

    let sourceSwitch = filterdAirGap[0]?.source && JSON.parse(filterdAirGap[0]?.source)
    let targetSwitch = filterdAirGap[0]?.target && JSON.parse(filterdAirGap[0]?.target)

    if (sourceSwitch && Array.isArray(sourceSwitch) && sourceSwitch?.length) sourceSwitch = sourceSwitch.map(s => s?.name).join(',')
    if (targetSwitch && Array.isArray(targetSwitch) && targetSwitch?.length) targetSwitch = targetSwitch.map(t => t?.name).join(',')

    let tableData = `<div class="card shadow-lg mb-0 bg-white"><div class="card-header fs-7 fw-semibold px-2" style="margin-right: 110px;"><i class="cp-configure-settings me-1"></i>AirGap Properties</div><div class="card-body p-0"><table class='table mb-0'>
        <tbody>
            <tr>
                <td><i class="cp-action-name me-2 text-primary"></i>Name</td>
                <td>:</td>
                <td class='fw-medium text-wrap fs-8'>${filterdAirGap[0]?.name}</td>
            </tr
             <tr>
                <td><i class="cp-data-source me-2 text-primary"></i>Source</td>
                <td>:</td>
                <td class='fw-medium fs-8'>${filterdAirGap[0]?.sourceComponentName || 'NA'}</td>
            </tr>
            <tr>
                <td><i class="cp-target_archieve me-2 text-primary"></i>Target</td>
                <td>:</td>
                <td class='fw-medium fs-8'>${filterdAirGap[0]?.targetComponentName || 'NA'}</td>
            </tr>            
             <tr>
                <td><i class="cp-Job-status me-2 text-primary"></i>Status</td>
                <td>:</td>
                <td class='fw-medium fs-8'>${filterdAirGap[0]?.status}</td>
            </tr>            
           <tr>
                <td><i class="cp-circle-switch me-2 text-primary"></i>Source Switch</td>
                <td>:</td>
                <td class='fw-medium fs-8 text-wrap'>${sourceSwitch}</td>
            </tr>
            <tr>
                <td><i class="cp-circle-switch me-2 text-primary"></i>Target Switch</td>
                <td>:</td>
                <td class='fw-medium fs-8 text-wrap'>${targetSwitch}</td>
            </tr>

            </tbody></table></div></div>`

    $('<div class="cyberAirgapTooltip"></div>').html(tableData).appendTo('#cyberContainer').fadeIn('slow');
    $('.cyberAirgapTooltip').css({
        top: 10,
        left: 10
    })

    $('.contextMenu').hide()
}

$('#btnAirgap').on('click', function () {
    $('#airgapList').val('')
    $('.dynamicColor').removeClass('active')

    if ($('.selected').length > 1) {
        let airGapConfig = $('.cyberAirgapImage')
        $(`#airgapList option`).attr('disabled', false)
   
        $.each(airGapConfig, function (idx, obj) {
            let airGapId = obj.getAttribute('airgapid')
            $(`#airgapList option[value='${airGapId}']`).attr('disabled', true)
        })
        setTimeout(() => {
            $('#multiCollapseExample1').removeClass('show')
            $('#loadAirgapModal').modal('show')
        }, 200)
    } else {
        notificationAlert('info', 'Minimum two selections required')
    }

})

$(document).on('click', '.dynamicColor', function () {
    $('.dynamicColor').removeClass('active')
    $(this).addClass('active')
})

$('#btnAddAirgap').on('click', function () {
    let isAirGapEdit = $('#btnEdit')?.attr('airgapid')
    let getAirgapId = $('#airgapList :selected')?.val()
    let airGapColor = $('.dynamicColor.active')?.parent()?.attr('for')
    let selectedAirgap = globalAirgapArray?.length && globalAirgapArray?.filter((d) => d?.id === getAirgapId)
    let flowLineId = getRandomId('flowline')

    if (!getAirgapId) {
        $('#airGapError').text('select airgap').addClass('field-validation-error');
        return;
    }

    if (isAirGapEdit) {

        const targetImg = $(`img[airgapid="${isAirGapEdit}"]`);

        $('.flowLineAdded').each(function () {
            if ($(this)?.attr('details')) {
                let data = $(this)?.attr('details')
                data = data && JSON.parse(data);

                if (data && Array.isArray(data) && data?.length) {
                    data.map((d) => {
                        if (d?.airgapId == isAirGapEdit) {
                            d.airgapId = selectedAirgap[0]?.id
                            d.airgapName = selectedAirgap[0]?.name
                            d.color = airGapColor

                            $(`#${d?.flowLineId}`).find('polyline').attr('stroke', airGapColor)
                            $(`#${d?.flowLineId} defs`).find('path').attr('fill', airGapColor)
                        }
                    })

                    $(this).attr('details', JSON.stringify(data))
                    if (targetImg) targetImg.attr('airgapid', selectedAirgap[0]?.id)
                }
            }
        });
    } else {
        const sourceId = $('.source_selected')?.first()?.attr('id');
        const targetId = $('.target_selected')?.first()?.attr('id');

        let details = {
            sourceId: sourceId,
            targetId: targetId,
            status: selectedAirgap[0]?.status,
            airgapId: selectedAirgap[0]?.id,
            airgapName: selectedAirgap[0]?.name,
            color: airGapColor,
            flowLineId: flowLineId
        }

        if ($(`#${sourceId}`).attr('details')?.length) {
            let airGapDetails = JSON.parse($(`#${sourceId}`).attr('details'))
            airGapDetails.push(details)
            $(`#${sourceId}`).attr('details', JSON.stringify(airGapDetails))
        } else {
            let detailsArray = []
            detailsArray.push(details)
            $(`#${sourceId}`).attr('details', JSON.stringify(detailsArray))
        }

        $(`#${sourceId}, #${targetId}`).attr('flowLineId', flowLineId)
        $(`#${sourceId}, #${targetId}`).addClass('flowLineAdded')

        $(`#${sourceId}`).attr('partnerId', targetId)
        $(`#${targetId}`).attr('partnerId', sourceId)

        airGapArray.push({ source: sourceId, target: sourceId })
        let endArrowId = getRandomId('arrow')

        let svgLine = `<svg id="${flowLineId}" width="${$('#cyberContainer').width()}" height="${$('#cyberContainer').height()}" style="position:absolute;">
         <defs>
            <marker id="${endArrowId}" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                <path d="M0,0 L10,5 L0,10 Z" fill="${airGapColor}" />
            </marker>
        </defs>
     </svg>`
        
        $('#cyberContainer').prepend(svgLine)
        connectSVGLine(flowLineId, sourceId, targetId, selectedAirgap[0]?.status, selectedAirgap[0]?.id, selectedAirgap[0]?.name, airGapColor, endArrowId)       
    }

    $('#btnEdit').removeAttr('airgapid')
    $('#loadAirgapModal').modal('hide')
})

const drawPolyLine = (connectPoints, points = []) => {

    points?.length && points.forEach((point) => connectPoints.push(point))

}

const connectSVGLine = (lineId, sourceId, targetId, status, airgapId, airgapName, color, endArrowId) => {

    let imageSrc = ''

    imageSrc =  ['close', 'disable', 'lock'].includes(status?.toLowerCase()) ?
        '/../img/Component_icons/airgap-off-vertical.svg' : '/../img/Component_icons/airgap-on-vertical.svg';

    let connectPoints = [];
    let points = [];
    let sourceContainerId = $(`#${sourceId}`).parents('.siteDraggableContainer')[0]?.id
    let targetContainerId = $(`#${targetId}`).parents('.siteDraggableContainer')[0]?.id

    let sourceComponentPositionX = $(`#${sourceContainerId}`)?.position()?.left + ($(`#${sourceContainerId}`)?.width())
    let sourceComponentPositionY = $(`#${sourceContainerId}`)?.position()?.top + ($(`#${sourceContainerId}`)?.height())
    let targetComponentPositionX = $(`#${targetContainerId}`)?.position()?.left + ($(`#${targetContainerId}`)?.width())
    let targetComponentPositionY = $(`#${targetContainerId}`)?.position()?.top + ($(`#${targetContainerId}`)?.height())

    let sourcePorts = getPortPositions($(`#${sourceContainerId}`));
    let targetPorts = getPortPositions($(`#${targetContainerId}`));

    const nearestPortPair = findNearestPortPair(sourcePorts, targetPorts, sourceContainerId, targetContainerId, sourceId, targetId, airgapId);

    let startX = nearestPortPair?.source?.x;
    let startY = nearestPortPair?.source?.y;
    let endX = nearestPortPair?.target?.x;
    let endY = nearestPortPair?.target?.y;

    const midX = startX + (endX - startX) / 2;
    const midY = startY - ((startY - endY) / 2);

    const imageWidth = 27;
    const imageHeight = 26
    const adjustedX = (imageWidth / 2);
    const adjustedY = (imageHeight / 2);

    let imageLeft = endX;
    let imageTop = startY + adjustedY;

    if (targetComponentPositionY > sourceComponentPositionY) {
        // target is below source

        if (sourceComponentPositionY < $(`#${targetContainerId}`).position().top) {

            ({ points, imageLeft } = connectLinesTargetBelowSource(nearestPortPair, startX, startY, midX, midY, endX, endY, sourceComponentPositionX, imageLeft, points))

        } else {

            ({ points, imageLeft, imageTop, imageSrc } = connectStraightLines(nearestPortPair, startX, startY, midX, endX, endY, sourceContainerId, sourceComponentPositionY, status, imageLeft, imageTop, imageSrc, adjustedY, points))
        }

    } else if (targetComponentPositionY < sourceComponentPositionY) {
        // target is above source

        if ($(`#${sourceContainerId}`).position().top > targetComponentPositionY) {

            ({ points, imageLeft } = connectLinesTargetAboveSource(nearestPortPair, startX, startY, midX, midY, endX, endY, sourceComponentPositionX, imageLeft, points));

            imageTop = endY + adjustedY

        } else {

            ({ points, imageLeft, imageTop, imageSrc } = connectStraightLines(nearestPortPair, startX, startY, midX, endX, endY, sourceContainerId, sourceComponentPositionY, status, imageLeft, imageTop, imageSrc, adjustedY, points))

        }
    }

    drawPolyLine(connectPoints, points)
    $('#cyberContainer cyberAirgapImage').remove();
    $('#cyberContainer').append(`<img class="position-absolute cyberAirgapImage" src='${imageSrc}' flowLineId="${lineId}" airgapid="${airgapId}" style="top:${imageTop}px;left:${imageLeft - adjustedX}px" />`)

    createPolylineWithArrow(connectPoints, color, lineId, endArrowId)
}

// lines for target below source
const connectLinesTargetBelowSource = (nearestPortPair, startX, startY, midX, midY, endX, endY, sourceComponentPositionX, imageLeft, points) => {

    if ((nearestPortPair?.source?.id == 'rightMiddle' && nearestPortPair?.target?.id == 'leftMiddle') || (nearestPortPair?.source?.id == 'leftMiddle' && nearestPortPair?.target?.id == 'rightMiddle')) {

        points = [[startX, startY], [midX, startY], [midX, endY], [endX, endY]]
        imageLeft = midX

    } else if (nearestPortPair?.source?.id == 'bottomMiddle' && (nearestPortPair?.target?.id == 'leftMiddle' || nearestPortPair?.target?.id == 'rightMiddle')) {
        points = [[startX, startY], [startX, endY], [endX, endY]]
        imageLeft = startX

    } else if (nearestPortPair?.source?.id == 'bottomMiddle' && nearestPortPair?.target?.id == 'topMiddle') {
        points = [[endX, startY], [endX, endY]]

        if (sourceComponentPositionX < endX) {
            points = [[startX, startY], [startX, midY], [endX, midY], [endX, endY]]
            imageLeft = startX
        }

    } else if ((nearestPortPair?.source?.id == 'rightMiddle' || nearestPortPair?.source?.id == 'leftMiddle') && nearestPortPair?.target?.id == 'topMiddle') {
        points = [[startX, startY], [endX, startY], [endX, endY]]

    } else if ((nearestPortPair?.source?.id == 'rightMiddle' && nearestPortPair?.target?.id == 'rightMiddle') || (nearestPortPair?.source?.id == 'leftMiddle' && nearestPortPair?.target?.id == 'leftMiddle')) {
        let midPoint = nearestPortPair?.source?.id == 'rightMiddle' ? startX + 50 : startX - 50

        points = [[startX, startY], [midPoint, startY], [midPoint, endY], [endX, endY]]
        imageLeft = midPoint
    }

    return { points, imageLeft }
}

// lines for target above source
const connectLinesTargetAboveSource = (nearestPortPair, startX, startY, midX, midY, endX, endY, sourceComponentPositionX, imageLeft, points) => {

    if ((nearestPortPair?.source?.id == 'rightMiddle' && nearestPortPair?.target?.id == 'leftMiddle') || (nearestPortPair?.source?.id == 'leftMiddle' && nearestPortPair?.target?.id == 'rightMiddle')) {

        points = [[startX, startY], [midX, startY], [midX, endY], [endX, endY]]
        imageLeft = midX

    } else if (nearestPortPair?.source?.id == 'topMiddle' && (nearestPortPair?.target?.id == 'leftMiddle' || nearestPortPair?.target?.id == 'rightMiddle')) {
        points = [[startX, startY], [startX, endY], [endX, endY]]
        imageLeft = startX

    } else if ((nearestPortPair?.source?.id == 'leftMiddle' || nearestPortPair?.source?.id == 'rightMiddle') && nearestPortPair?.target?.id == 'bottomMiddle') {

        points = [[endX, endY], [endX, startY], [startX, startY]]

    } else if (nearestPortPair?.source?.id == 'topMiddle' && nearestPortPair?.target?.id == 'bottomMiddle') {
        points = [[endX, endY], [endX, startY]]

        if (sourceComponentPositionX < endX) {
            points = [[startX, startY], [startX, midY], [endX, midY], [endX, endY]]
            imageLeft = startX
        }

    } else if ((nearestPortPair?.source?.id == 'rightMiddle' && nearestPortPair?.target?.id == 'rightMiddle') || (nearestPortPair?.source?.id == 'leftMiddle' && nearestPortPair?.target?.id == 'leftMiddle')) {
        let midPoint = nearestPortPair?.source?.id == 'rightMiddle' ? startX + 50 : startX - 50

        points = [[startX, startY], [midPoint, startY], [midPoint, endY], [endX, endY]]
        imageLeft = midPoint
    } else if ((nearestPortPair?.source?.id == 'rightMiddle' || nearestPortPair?.source?.id == 'leftMiddle') && nearestPortPair?.target?.id == 'topMiddle') {
        let midPoint = nearestPortPair?.source?.id == 'rightMiddle' ? startX + 50 : startX - 50

        points = [[startX, startY], [midPoint, startY], [midPoint, endY - 50], [endX, endY - 50],  [endX, endY]];
        imageLeft = midPoint
    }

    return { points, imageLeft }
}

// straight lines from target to source
const connectStraightLines = (nearestPortPair, startX, startY, midX, endX, endY, sourceContainerId, sourceComponentPositionY, status, imageLeft, imageTop, imageSrc, adjustedY, points) => {
    if ((nearestPortPair?.source?.id == 'rightMiddle' && nearestPortPair?.target?.id == 'leftMiddle') || (nearestPortPair?.source?.id == 'leftMiddle' && nearestPortPair?.target?.id == 'rightMiddle')) {

        if ($(`#${sourceContainerId}`).position().top > endY) {
            points = [[startX, startY], [midX, startY], [midX, endY], [endX, endY]]

            imageLeft = midX
            imageTop = endY + adjustedY

        } else if (sourceComponentPositionY < endY) {
            points = [[startX, startY], [midX, startY], [midX, endY], [endX, endY]]

            imageLeft = midX
            imageTop = startY + adjustedY

        } else {

            imageSrc = ['close', 'disable', 'lock'].includes(status?.toLowerCase()) ? '/../img/Component_icons/airgap_off.svg' : '/../img/Component_icons/airgap_on.svg';
            points = [[startX, endY], [endX, endY]]

            imageLeft = (startX + endX) / 2
            imageTop = endY - adjustedY
        }

    } else if (nearestPortPair?.source?.id == 'bottomMiddle' && (nearestPortPair?.target?.id == 'leftMiddle' || nearestPortPair?.target?.id == 'rightMiddle')) {
        points = [[startX, startY], [startX, endY], [endX, endY]]
        imageLeft = startX

    } else if ((nearestPortPair?.source?.id == 'rightMiddle' || nearestPortPair?.source?.id == 'leftMiddle') && nearestPortPair?.target?.id == 'topMiddle') {
        points = [[startX, startY], [endX, startY], [endX, endY]]

    } else if ((nearestPortPair?.source?.id === 'topMiddle' && nearestPortPair?.target?.id === 'topMiddle') ||
        (nearestPortPair?.source?.id === 'bottomMiddle' && nearestPortPair?.target?.id === 'bottomMiddle')) {

        let controlY = nearestPortPair?.source?.id === 'topMiddle'
            ? Math.min(startY, endY) - 25
            : Math.max(startY, endY) + 25;

        points = [
            [startX, startY], [startX, controlY],
            [endX, controlY], [endX, endY]
        ];

        imageLeft = (startX + endX) / 2;
        imageTop = controlY - adjustedY;

        imageSrc = ['close', 'disable', 'lock'].includes(status?.toLowerCase()) ? '/../img/Component_icons/airgap_off.svg' : '/../img/Component_icons/airgap_on.svg';
    }

    return { points, imageLeft, imageTop, imageSrc }
}

function calculateDistance(point1, point2) {
    const dx = point1?.x - point2?.x;
    const dy = point1?.y - point2?.y;
    return Math.sqrt(dx * dx + dy * dy);
}

function findNearestPortPair(sourcePorts, targetPorts, sourceContainerId, targetContainerId, sourceId, targetId, airgapId) {
    let nearestPair = { source: null, target: null };
    let minimumDistance = Infinity;
    let sourceContainerOriginalPorts = $(`#${sourceContainerId}`)?.attr('portDetails')
    let targetContainerOriginalPorts = $(`#${targetContainerId}`)?.attr('portDetails')

    sourceContainerOriginalPorts = sourceContainerOriginalPorts ? JSON.parse(sourceContainerOriginalPorts) : []
    targetContainerOriginalPorts = targetContainerOriginalPorts ? JSON.parse(targetContainerOriginalPorts) : []

    sourcePorts.forEach(sourcePort => {

        if (isPortOccupied(sourcePort, sourceContainerOriginalPorts)) return;

        targetPorts.forEach(targetPort => {

            if (isPortOccupied(targetPort, targetContainerOriginalPorts)) return;

            const distance = calculateDistance(sourcePort, targetPort);
            if (distance < minimumDistance) {
                minimumDistance = distance;
                nearestPair = { source: sourcePort, target: targetPort };
            }
        });
    });

    if (nearestPair) setPortOccupied(sourceContainerId, targetContainerId, sourceId, targetId, nearestPair, airgapId)
 
    return nearestPair;
}

function setPortOccupied(source, target, sourceId, targetId, nearestPair, airgapId) {

    const sourceContainerOriginalPorts = JSON.parse($(`#${source}`)?.attr('portDetails') || '[]');
    const targetContainerOriginalPorts = JSON.parse($(`#${target}`)?.attr('portDetails') || '[]');

    if (nearestPair) {
        sourceContainerOriginalPorts.push({ sourceId, targetId, port: nearestPair?.source?.id, airgapId })
        targetContainerOriginalPorts.push({ sourceId, targetId, port: nearestPair?.target?.id, airgapId })
    }

    $(`#${source}`).attr('portDetails', JSON.stringify(sourceContainerOriginalPorts))
    $(`#${target}`).attr('portDetails', JSON.stringify(targetContainerOriginalPorts))
}

function isPortOccupied(ports, occupiedDetails = []) {
    return occupiedDetails?.some(portObj => portObj?.port?.toLowerCase() == ports?.id?.toLowerCase())
}

function getPortPositions($div) {
    let offset = $div?.position();
    let width = $div?.width();
    let height = $div?.height();

    return [
        {
            id: 'topMiddle',
            x: offset.left + width / 2,
            y: offset.top
        },
        {
            id: 'leftMiddle',
            x: offset.left,
            y: offset.top + height / 2
        },
        {
            id: 'rightMiddle',
            x: offset.left + width,
            y: offset.top + height / 2
        },
        {
            id: 'bottomMiddle',
            x: offset.left + width / 2,
            y: offset.top + height
        }
    ];
}

function createPolylineWithArrow(points, color, lineId, endArrowId) {

    setTimeout(() => {
        $(`#${lineId} polyline`).remove();
        const polyline = document.createElementNS("http://www.w3.org/2000/svg", "polyline");
        color = color || 'blue'
        $(polyline).attr({
            points: points?.join(" "),
            fill: "none",
            stroke: color,
            "stroke-width": 1,
            "stroke-dasharray": "4, 4",
            "marker-end": `url(#${endArrowId})`,
        });

        $(`#${lineId}`).append(polyline);
        $(`#${lineId} path`).attr('fill', color);
        $('.componentContainer').removeClass('selected')
    })
}

$(document).on('click', '.cyberAirgapImage', function () {
    let airgapId = $(this)?.attr('airgapid')
    showAirGapDetails(airgapId)
})

$(document).on('click', '#cyberContainer', function (e) {
    if (!e?.target?.classList?.contains('cyberAirgapImage')) {
        $('.cyberAirgapTooltip').remove();
    }
})

$('#loadAirgapModal').on('.btnCancelAirgap', function () {
    $('#btnEdit').removeAttr('airgapid')
})

const convertToImage = async () => {

    await html2canvas($("#cyberContainer")[0]).then((canvas) => {
        convertToBase64 = canvas?.toDataURL();
    });

    return convertToBase64
}

$('#btnSaveCyberMapping').on('click', async function () {

    let cyberContainer = $('#cyberContainer').children().not('.emptyClass').not('canvas').not('img').not('svg')
    let mappingContainer = []
    let convertImage = await convertToImage();

    $.each(cyberContainer, function (idx, obj) {

        let getContId = obj?.id
        let siteId = $(`#${getContId}`)?.find('.siteNameCont')?.attr('id')
        let siteName = $(`#${getContId}`)?.find('.siteNameCont')?.text()
        let siteColor = $(`#${getContId} .zoneContainer`)?.attr('style')?.split(' ')[2]
        let cardCont = $(`#${getContId} .card-body`)?.children()
        let componentArray = [];
        let airgapArray = [];

        $.each(cardCont, function (idx, item) {

            if ($(item).find('.parallelCont').length) {

                let groupArray = [];
                let getGroupId = $(item)?.find('.parallelCont')[0]?.id
                let comp = $(`#${getGroupId} .componentContainer`)
                let groupId = $(`#${getGroupId} .compGroupName`)[0]?.id
                let groupName = $(`#${getGroupId} .compGroupName`)?.text()
                let groupColor = $(`#${getGroupId}`)?.attr('style')?.split(' ')[2]

                $.each(comp, function (id, val) {

                    let compObj = {}
                    let componentid = val?.id

                    compObj['name'] = $(`#${componentid} .componentName`).text()
                    compObj['id'] = componentid
                    compObj['icon'] = $(`#${componentid} .componentIcon`).find('img').attr('src')
                    compObj['serverType'] = $(`#${componentid} .componentIcon`).attr('serverType')
                    compObj['count'] = $(`#${componentid} .componentCount`).attr('value')
                    groupArray.push(compObj);

                    let findAirgap = $(`#${componentid}`).attr('details')

                    if (findAirgap?.length) {
                        let parsedDetail = findAirgap && JSON.parse(findAirgap)
                        airgapArray = airgapArray.concat(parsedDetail)
                    }

                })
                componentArray.push({ id: groupId, isGroup: true, name: groupName, groupArray: groupArray, groupColor: groupColor })
            } else {
                if ($(`#${getContId} .nonGroupComps`).children().length) {
                    let componentContainer = $(`#${getContId} .nonGroupComps .componentContainer`)

                    $.each(componentContainer, function (idx, obj) {
                        let compObj = {}
                        let componentid = obj.id

                        compObj['name'] = $(`#${componentid} .componentName`).text()
                        compObj['id'] = componentid
                        compObj['icon'] = $(`#${componentid} .componentIcon`).find('img').attr('src')
                        compObj['serverType'] = $(`#${componentid} .componentIcon`).attr('serverType')
                        compObj['count'] = $(`#${componentid} .componentCount`).attr('value')
                        componentArray.push(compObj);

                        let findAirgap = $(`#${componentid}`).attr('details')

                        if (findAirgap?.length) {
                            let parsedDetail = findAirgap && JSON.parse(findAirgap)
                            airgapArray = airgapArray.concat(parsedDetail)
                        }
                    })
                }
            }
        })

        let getPosition = {
            top: parseInt($(`#${getContId}`).css('top'), 10),
            left: parseInt($(`#${getContId}`).css('left'), 10)
        }

        mappingContainer.push({ siteId: siteId, siteName: siteName, properties: componentArray, airgapArray: airgapArray, position: getPosition, siteColor: siteColor })
    })

    mappingContainer[0]['convertedImage'] = convertImage

    let mappingData = {
        Id: '',
        Name: getRandomId('mapping'),
        Properties: JSON.stringify(mappingContainer),
        __RequestVerificationToken: gettoken()
    }

    globalCyberMappingId.length ? mappingData['Id'] = globalCyberMappingId : '';

    await $.ajax({
        type: "POST",
        url: RootUrl + mappingModuleMethods?.createOrUpdate,
        data: mappingData,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.success) {
                notificationAlert('success', result?.data?.message)
                $('#btnSaveCyberMapping').text('Update').attr('title', 'Update')
            } else {
                errorNotification(result)
            }
        }
    })
})

$('#btnCreateGroup').on('click', function () {
    let ParallelId = getRandomId('parallel')
    $('.selected').removeClass('w-25').addClass('w-50')
    $('.selected').wrapAll("<div class='row justify-content-center w-50 mx-0 px-2'><div class='parallelCont border-warning p-2 w-100' id=" + ParallelId + "></div></div>")
    let groupContainer = $(`#${ParallelId}`)?.parent()
    let contId = $('.selected').first().parents('.zoneContainer')[0]?.id
    $('.componentContainer.w-25').removeClass('w-25').addClass('w-50')
    $(`#${contId} .card-body`).prepend(groupContainer)
})

$('#btnDeleteMapping').on('click', function () {
    $('#textDeleteId').val(globalCyberMappingId)
    $('#ComponentMappingDelete').modal('show')

})

$('#btnDeleteCyberComponentMapping').on('click', async function (e) {
    e.preventDefault();

    if (globalCyberMappingId) {
        let data = { id: globalCyberMappingId }

        await $.ajax({
            type: 'DELETE',
            url: RootUrl + mappingModuleMethods?.Delete,
            data: data,
            dataType: "json",
            success: function (result) {
                if (result?.success) {
                    notificationAlert('success', result?.data?.message)
                    getComponentMappingList();
                    $('#btnSaveCyberMapping').text('Save').attr('title', 'Save')
                } else {
                    errorNotification(result)
                }
                $('#ComponentMappingDelete').modal('hide')
            }
        })
    }
})













