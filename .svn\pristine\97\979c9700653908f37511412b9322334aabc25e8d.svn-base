using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class OracleMonitorStatusFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "Oracle";

    public List<OracleMonitorStatus> OracleMonitorStatusPaginationList { get; set; }
    public List<OracleMonitorStatus> OracleMonitorStatusList { get; set; }
    public OracleMonitorStatus OracleMonitorStatusDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public OracleMonitorStatusFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<OracleMonitorStatus>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow));

        OracleMonitorStatusPaginationList = _fixture.CreateMany<OracleMonitorStatus>(20).ToList();
        OracleMonitorStatusList = _fixture.CreateMany<OracleMonitorStatus>(5).ToList();
        OracleMonitorStatusDto = _fixture.Create<OracleMonitorStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public OracleMonitorStatus CreateOracleMonitorStatusWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<OracleMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public OracleMonitorStatus CreateOracleMonitorStatusWithWhitespace()
    {
        return CreateOracleMonitorStatusWithProperties(type: "  Oracle  ");
    }

    public List<OracleMonitorStatus> CreateMultipleOracleMonitorStatusWithSameType(string type, int count)
    {
        var statuses = new List<OracleMonitorStatus>();
        for (int i = 0; i < count; i++)
        {
            statuses.Add(CreateOracleMonitorStatusWithProperties(type: type, isActive: true));
        }
        return statuses;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
