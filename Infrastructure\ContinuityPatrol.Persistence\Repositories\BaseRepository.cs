﻿using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Persistence;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Infrastructure.Persistence;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class BaseRepository<T> : IRepository<T> where T : BaseEntity
{
    private readonly ILoggedInUserService _loggedInUserService;
    protected readonly ModuleDbContext DbContext;
    private AssignedEntity _assignedEntity;
    public DbSet<T> Entities;

    private HashSet<string> _assignedServiceIdsCache;
    private HashSet<string> _assignedFunctionIdsCache;
    private HashSet<string> _assignedInfraObjectIdsCache;

    public BaseRepository(ModuleDbContext dbContext, ILoggedInUserService loggedInUserService = null)
    {
        DbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        _loggedInUserService = loggedInUserService;
        Entities = DbContext.Set<T>();
    }

    public AssignedEntity AssignedEntity => _assignedEntity ??= LoadAssignedEntity() ?? new AssignedEntity();

    private AssignedEntity LoadAssignedEntity()
    {
        if (_loggedInUserService == null || !_loggedInUserService.IsAuthenticated)
            throw new SessionExpiredException("Session logged out, no action taken too long");

        if (IsAllInfra) return new AssignedEntity();

        return _loggedInUserService.AssignedInfras.IsNotNullOrEmpty()
            ? JsonConvert.DeserializeObject<AssignedEntity>(_loggedInUserService.AssignedInfras)
            : new AssignedEntity();
    }

    public bool IsParent => _loggedInUserService.IsParent;
    public bool IsAllInfra => _loggedInUserService.IsAllInfra;

    public virtual async Task<T> GetByReferenceIdAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Id", "Id cannot be invalid");

        return await Entities.AsNoTracking().FirstOrDefaultAsync(x => x.ReferenceId==id);
    }

    public IQueryable<T> GetByReferenceId(string id, Expression<Func<T, bool>> expression = null)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Id", "Id cannot be invalid");
        return IsParent
            ? Entities.AsNoTracking().Where(x => x.ReferenceId == id)
            : FilterBy(expression ?? (x => x.ReferenceId == id));
    }

    public T GetBusinessServiceByReferenceId(T businessService)
    {
        if (businessService == null)
            return null;

        // Use cached HashSet for O(1) lookup
        _assignedServiceIdsCache ??= AssignedEntity.AssignedBusinessServices
            .Select(s => s.Id)
            .ToHashSet();

        return _assignedServiceIdsCache.Contains(businessService.ReferenceId) ? businessService : null;
    }

    public T GetBusinessFunctionByReferenceId(T businessFunction)
    {
        if (businessFunction == null)
            return null;

        // Use cached HashSet for O(1) lookup
        _assignedFunctionIdsCache ??= AssignedEntity.AssignedBusinessServices
            .SelectMany(service => service.AssignedBusinessFunctions)
            .Select(f => f.Id)
            .ToHashSet();

        return _assignedFunctionIdsCache.Contains(businessFunction.ReferenceId) ? businessFunction : null;
    }

    public T GetInfraObjectByReferenceId(T infraObject)
    {
        if (infraObject == null)
            return null;

        // Use cached HashSet for O(1) lookup
        _assignedInfraObjectIdsCache ??= AssignedEntity.AssignedBusinessServices
            .SelectMany(service => service.AssignedBusinessFunctions)
            .SelectMany(function => function.AssignedInfraObjects)
            .Select(obj => obj.Id)
            .ToHashSet();

        return _assignedInfraObjectIdsCache.Contains(infraObject.ReferenceId) ? infraObject : null;
    }


    public virtual async Task<IReadOnlyList<T>> ListAllAsync()
    {
        return await Entities.AsNoTracking().DescOrderById().ToListAsync();
    }

    public virtual IQueryable<T> QueryAll(Expression<Func<T, bool>> expression)
    {
        Guard.Against.Null(expression, nameof(expression));

        return IsParent ? Entities.AsNoTracking().DescOrderById() : FilterBy(expression);
    }

    public virtual IQueryable<T> FilterBy(Expression<Func<T, bool>> expression)
    {
        Guard.Against.Null(expression, nameof(expression));

        return Entities.AsNoTracking().Where(expression).DescOrderById();
    }

    public virtual async Task<IReadOnlyList<T>> FindByFilterAsync(Expression<Func<T, bool>> expression)
    {
        return await Entities.AsNoTracking().Where(expression).DescOrderById().ToListAsync();
    }

    public virtual IQueryable<T> GetPaginatedQuery()
    {
        return Entities.AsNoTracking().DescOrderById();
    }

    public virtual async Task<PaginatedResult<T>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<T> productFilterSpec,string sortColumn,string sortOrder)
    {
        return await Entities
            .AsNoTracking()
            .Specify(productFilterSpec)
            .DescOrderById()
            .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public virtual async Task<T> AddAsync(T entity)
    {
        Guard.Against.Null(entity, nameof(entity));

        return await ExecuteWithRetryAsync(async () =>
        {
            await Entities.AddAsync(entity);
            await DbContext.SaveChangesAsync();
            return entity;
        });
    }

    public virtual async Task<T> UpdateAsync(T entity)
    {
        Guard.Against.Null(entity, nameof(entity));

        return await ExecuteWithRetryAsync(async () =>
        {
            DbContext.ChangeTracker.Clear();
            Entities.Attach(entity);
            DbContext.Entry(entity).State = EntityState.Modified;
            await DbContext.SaveChangesAsync();
            return entity;
        });
    }

    public virtual async Task<IEnumerable<T>> UpdateRangeAsync(IEnumerable<T> entities)
    {
        var entityArray = entities?.ToArray() ?? throw new ArgumentNullException(nameof(entities));
        if (!entityArray.Any()) return Enumerable.Empty<T>();

        DbContext.ChangeTracker.Clear();
        DbContext.Set<T>().UpdateRange(entityArray);
        await DbContext.SaveChangesAsync();
        return entityArray;
    }

    public virtual async Task<T> DeleteAsync(T entity)
    {
        Guard.Against.Null(entity, nameof(entity));

        Entities.Remove(entity);
        DbContext.Entry(entity).State = EntityState.Deleted;
        await DbContext.SaveChangesAsync();
        return entity;
    }

    public virtual async Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities)
    {
        var entityArray = entities?.ToArray() ?? throw new ArgumentNullException(nameof(entities));
        if (!entityArray.Any()) return Enumerable.Empty<T>();

        await DbContext.Set<T>().AddRangeAsync(entityArray);
        await DbContext.SaveChangesAsync();
        return entityArray;

    }
    public virtual async Task<IEnumerable<T>> RemoveRangeAsync(IEnumerable<T> entities)
    {
        var entityArray = entities?.ToArray() ?? throw new ArgumentNullException(nameof(entities));
        if (!entityArray.Any()) return Enumerable.Empty<T>();

        DbContext.Set<T>().RemoveRange(entityArray);
        await DbContext.SaveChangesAsync();
        return entityArray;
    }
    public IReadOnlyList<T> GetAssignedBusinessServices(IQueryable<T> businessServices)
    {
        if (businessServices == null)
            return Array.Empty<T>();

        // Use cached HashSet for O(1) lookups
        _assignedServiceIdsCache ??= AssignedEntity.AssignedBusinessServices
            .Select(s => s.Id)
            .ToHashSet();

        if (!_assignedServiceIdsCache.Any())
            return Array.Empty<T>();

        return businessServices
            .AsNoTracking()
            .Where(bs => _assignedServiceIdsCache.Contains(bs.ReferenceId))
            .ToList();
    }

    public IReadOnlyList<T> GetAssignedBusinessFunctions(IQueryable<T> businessFunctions)
    {
        if (businessFunctions == null)
            return Array.Empty<T>();

        // Use cached HashSet for O(1) lookups
        _assignedFunctionIdsCache ??= AssignedEntity.AssignedBusinessServices
            .SelectMany(service => service.AssignedBusinessFunctions)
            .Select(f => f.Id)
            .ToHashSet();

        if (!_assignedFunctionIdsCache.Any())
            return Array.Empty<T>();

        return businessFunctions
            .AsNoTracking()
            .Where(f => _assignedFunctionIdsCache.Contains(f.ReferenceId))
            .ToList();
    }

    public IReadOnlyList<T> GetAssignedInfraObjects(IQueryable<T> infraObjects)
    {
        if (infraObjects == null)
            return Array.Empty<T>();

        // Use cached HashSet for O(1) lookups
        _assignedInfraObjectIdsCache ??= AssignedEntity?.AssignedBusinessServices
            .SelectMany(service => service.AssignedBusinessFunctions)
            .SelectMany(function => function.AssignedInfraObjects)
            .Select(obj => obj.Id)
            .ToHashSet();

        if (_assignedInfraObjectIdsCache == null || !_assignedInfraObjectIdsCache.Any())
            return Array.Empty<T>();

        return infraObjects
            .AsNoTracking()
            .Where(obj => _assignedInfraObjectIdsCache.Contains(obj.ReferenceId))
            .ToList();
    }

    public IQueryable<T> GetPaginatedAssignedBusinessServices(IQueryable<T> businessServices)
    {
        if (businessServices == null)
            return Enumerable.Empty<T>().AsQueryable();

        // Use cached HashSet for O(1) lookups
        _assignedServiceIdsCache ??= AssignedEntity.AssignedBusinessServices
            .Select(s => s.Id)
            .ToHashSet();

        if (!_assignedServiceIdsCache.Any())
            return Enumerable.Empty<T>().AsQueryable();

        return businessServices
            .AsNoTracking()
            .Where(s => _assignedServiceIdsCache.Contains(s.ReferenceId));
    }

    public IQueryable<T> GetPaginatedBusinessFunctions(IQueryable<T> businessFunctions)
    {
        if (businessFunctions == null)
            return Enumerable.Empty<T>().AsQueryable();

        // Use cached HashSet for O(1) lookups
        _assignedFunctionIdsCache ??= AssignedEntity.AssignedBusinessServices
            .SelectMany(b => b.AssignedBusinessFunctions)
            .Select(f => f.Id)
            .ToHashSet();

        if (!_assignedFunctionIdsCache.Any())
            return Enumerable.Empty<T>().AsQueryable();

        return businessFunctions
            .AsNoTracking()
            .Where(f => _assignedFunctionIdsCache.Contains(f.ReferenceId));
    }

    public IQueryable<T> GetPaginatedInfraObjects(IQueryable<T> infraObjects)
    {
        if (infraObjects == null)
            return Enumerable.Empty<T>().AsQueryable();

        // Use cached HashSet for O(1) lookups
        _assignedInfraObjectIdsCache ??= AssignedEntity.AssignedBusinessServices
            .SelectMany(b => b.AssignedBusinessFunctions)
            .SelectMany(f => f.AssignedInfraObjects)
            .Select(i => i.Id)
            .ToHashSet();

        if (!_assignedInfraObjectIdsCache.Any())
            return Enumerable.Empty<T>().AsQueryable();

        return infraObjects
            .AsNoTracking()
            .Where(i => _assignedInfraObjectIdsCache.Contains(i.ReferenceId));
    }

    public virtual async Task<T> GetByIdAsync(int id)
    {
        return await Entities.FindAsync(id);
    }

    private async Task<TResult> ExecuteWithRetryAsync<TResult>(Func<Task<TResult>> operation)
    {
        const int maxRetries = 5;
        var retryDelay = 1000;
        var retries = 0;

        var strategy = DbContext.Database.CreateExecutionStrategy();

        return await strategy.ExecuteAsync(async () =>
        {
            while (true)
            {
                await using var transaction = await DbContext.Database.BeginTransactionAsync();
                try
                {
                    var result = await operation();
                    await transaction.CommitAsync();
                    return result;
                }
                catch (DbUpdateException ex) when (ex.InnerException is SqlException sqlEx &&

                                                   (sqlEx.Number == 1205 || // Deadlock

                                                    sqlEx.Number == 1222 || // Lock request timeout

                                                    sqlEx.Number == 40197 || // Transaction rollback due to dependency

                                                    sqlEx.Number == 40549 || // Session terminated due to deadlock

                                                    sqlEx.Number == 40550))// Session terminated due to excessive lock contentionerge change
 

             
 
                {
                    retries++;
                    await transaction.RollbackAsync();

                    if (retries >= maxRetries) throw;
                    await Task.Delay(retryDelay);
                    retryDelay *= 2;
                }
            }
        });
    }
}