﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FormTypeCategory.Events.Create;

public class FormTypeCategoryCreatedEventHandler : INotificationHandler<FormTypeCategoryCreatedEvent>
{
    private readonly ILogger<FormTypeCategoryCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FormTypeCategoryCreatedEventHandler(ILoggedInUserService userService,
        ILogger<FormTypeCategoryCreatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(FormTypeCategoryCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var result = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} {Modules.FormTypeCategory}",
            Entity = Modules.FormTypeCategory.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"Form Type Category '{createdEvent.Name}' created successfully."
        };

        await _userActivityRepository.AddAsync(result);

        _logger.LogInformation($"FormType Category '{createdEvent.Name}' created successfully.");
    }
}