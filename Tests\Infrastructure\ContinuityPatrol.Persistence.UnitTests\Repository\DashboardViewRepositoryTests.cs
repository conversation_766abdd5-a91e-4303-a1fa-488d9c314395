using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DashboardViewRepositoryTests : IClassFixture<DashboardViewFixture>,IClassFixture<BusinessFunctionFixture>,IClassFixture<BusinessServiceFixture>,IClassFixture<InfraObjectFixture>
{
    private readonly DashboardViewFixture _dashboardViewFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DashboardViewRepository _repository;
    private readonly DashboardViewRepository _repositoryNotParent;
    private readonly BusinessFunctionFixture _businessFunctionFixture;
    private readonly BusinessServiceFixture _businessServiceFixture;
    private readonly InfraObjectFixture _infraObjectFixture;


    public DashboardViewRepositoryTests(DashboardViewFixture dashboardViewFixture, BusinessFunctionFixture businessFunctionFixture, BusinessServiceFixture businessServiceFixture,InfraObjectFixture infraObjectFixture)
    {
        _dashboardViewFixture = dashboardViewFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DashboardViewRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new DashboardViewRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
        _businessFunctionFixture = businessFunctionFixture;
        _businessServiceFixture = businessServiceFixture;
        _infraObjectFixture = infraObjectFixture;
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var dashboardView = _dashboardViewFixture.DashboardViewDto;

        // Act
        await _dbContext.DashboardViews.AddAsync(dashboardView);
        await _dbContext.SaveChangesAsync();

    var result = await _repository.GetByReferenceIdAsync(dashboardView.ReferenceId);
        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardView.ReferenceId, result.ReferenceId);
      //  Assert.Equal(dashboardView.BusinessFunctionId, result.BusinessFunctionId);
        Assert.Equal(dashboardView.CompanyId, result.CompanyId);
        Assert.Single(_dbContext.DashboardViews);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var dashboardView = _dashboardViewFixture.DashboardViewDto;
        await _repository.AddAsync(dashboardView);

        dashboardView.BusinessServiceName = "Updated Service Name";

        // Act
        var result = await _repository.UpdateAsync(dashboardView);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Service Name", result.BusinessServiceName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var dashboardView = _dashboardViewFixture.DashboardViewDto;
        await _dbContext.DashboardViews.AddAsync(dashboardView);
        await _dbContext.SaveChangesAsync();
        // Act
        dashboardView.IsActive = false;

         _dbContext.DashboardViews.Update(dashboardView);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dashboardView = _dashboardViewFixture.DashboardViewDto;
        var addedEntity = await _repository.AddAsync(dashboardView);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange

        var bs = _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(bs);

        var bf = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(bf);

        var infra = _infraObjectFixture.InfraObjectDto;
        await _dbContext.InfraObjects.AddAsync(infra);

        var dashboardView = _dashboardViewFixture.DashboardViewDto;
     
        dashboardView.BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        dashboardView.BusinessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";
        dashboardView.InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c";
        dashboardView.CompanyId = "COMPANY_123";

        await _repository.AddAsync(dashboardView);

        // Act
        var result = await _repository.GetByReferenceIdAsync(dashboardView.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardView.ReferenceId, result.ReferenceId);
        Assert.Equal(dashboardView.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var bs = _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(bs);

        var bf = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(bf);

        var infra = _infraObjectFixture.InfraObjectDto;
        await _dbContext.InfraObjects.AddAsync(infra);
        var dashboardView = _dashboardViewFixture.DashboardViewDto;
       
        dashboardView.BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        dashboardView.BusinessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";
        dashboardView.InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c";
        dashboardView.CompanyId = "ChHILD_COMPANY_123";

        await _repositoryNotParent.AddAsync(dashboardView);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repositoryNotParent.GetByReferenceIdAsync(dashboardView.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardView.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        await _repository.AddRangeAsync(dashboardViews);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardViews.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        await _repositoryNotParent.AddRangeAsync(dashboardViews);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDashboardNames Tests

    [Fact]
    public async Task GetDashboardNames_ShouldReturnDashboardNames_WhenIsAllInfraTrue()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        await _repository.AddRangeAsync(dashboardViews);

        // Act
        var result = await _repository.GetDashboardNames();

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.NotNull(x.InfraObjectId));
        Assert.All(result, x => Assert.NotNull(x.InfraObjectName));
        Assert.All(result, x => Assert.NotNull(x.MonitorType));
        Assert.All(result, x => Assert.NotNull(x.EntityId));
    }

    [Fact]
    public async Task GetDashboardNames_ShouldReturnFilteredDashboardNames_WhenIsAllInfraFalse()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        dashboardViews[0].CompanyId = "ChHILD_COMPANY_123";
        dashboardViews[0].BusinessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";
        dashboardViews[0].BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";

        dashboardViews[0].InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c";
        await _repositoryNotParent.AddRangeAsync(dashboardViews);

        // Act
        var result = await _repositoryNotParent.GetDashboardNames();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public void GetPaginatedQuery_ShouldReturnQueryableOrderedByIdDescending_WhenIsAllInfraTrue()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        _repository.AddRangeAsync(dashboardViews).Wait();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Any());
        
        // Verify ordering by checking if the first item has a higher ID than the last
        var resultList = result.ToList();
        if (resultList.Count > 1)
        {
            Assert.True(resultList.First().Id >= resultList.Last().Id);
        }
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnFilteredQueryable_WhenIsAllInfraFalse()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        _repositoryNotParent.AddRangeAsync(dashboardViews).Wait();

        // Act
        var result = _repositoryNotParent.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region GetByEntityIdAndType Tests

    [Fact]
    public async Task GetByEntityIdAndType_ShouldReturnEntity_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var dashboardView = _dashboardViewFixture.DashboardViewDto;
        dashboardView.MonitorType = "TestType";
        await _repository.AddAsync(dashboardView);

        // Act
        var result = await _repository.GetByEntityIdAndType(dashboardView.EntityId, "TestType");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardView.EntityId, result.EntityId);
        Assert.Equal("TestType", result.MonitorType);
    }

    [Fact]
    public async Task GetByEntityIdAndType_ShouldReturnFilteredEntity_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var bs= _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(bs);

        var bf=_businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(bf);
       
        var infra=_infraObjectFixture.InfraObjectDto;
        await _dbContext.InfraObjects.AddAsync(infra);
        
        var dashboardView = _dashboardViewFixture.DashboardViewDto;
        dashboardView.MonitorType = "TestType";
       
        dashboardView.BusinessServiceId = bs.ReferenceId;
        dashboardView.BusinessFunctionId = bf.ReferenceId;
        dashboardView.InfraObjectId = infra.ReferenceId;
        dashboardView.CompanyId = "ChHILD_COMPANY_123";

        await _dbContext.DashboardViews.AddAsync(dashboardView);

        await _dbContext.SaveChangesAsync();
      
        // Act
        var result = await _repositoryNotParent.GetByEntityIdAndType(dashboardView.EntityId, "TestType");

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    [Fact]
    public async Task GetByEntityIdAndType_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        await _repository.AddRangeAsync(dashboardViews);

        // Act
        var result = await _repository.GetByEntityIdAndType("non-existent-entity-id", "non-existent-type");

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetBusinessViewByInfraObjectId Tests

    [Fact]
    public async Task GetBusinessViewByInfraObjectId_ShouldReturnEntity_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var dashboardView = _dashboardViewFixture.DashboardViewDto;

        dashboardView.CompanyId = "ChHILD_COMPANY_123";
        dashboardView.BusinessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";
        dashboardView.BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";

        dashboardView.InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c";
        await _dbContext.DashboardViews.AddAsync(dashboardView);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.GetBusinessViewByInfraObjectId(DashboardViewFixture.InfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(DashboardViewFixture.InfraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetBusinessViewByInfraObjectId_ShouldReturnFilteredEntity_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var dashboardView = _dashboardViewFixture.DashboardViewDto;
        dashboardView.CompanyId = "ChHILD_COMPANY_123";
        dashboardView.BusinessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";
        dashboardView.BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";

        await _dbContext.DashboardViews.AddAsync(dashboardView);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repositoryNotParent.GetBusinessViewByInfraObjectId(dashboardView.InfraObjectId);

        // Assert
        // Result should be filtered based on assigned infrastructure
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetBusinessViewByInfraObjectId_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        await _repository.AddRangeAsync(dashboardViews);

        // Act
        var result = await _repository.GetBusinessViewByInfraObjectId("non-existent-infra-id");

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;

        // Act
        var result = await _repository.AddRangeAsync(dashboardViews);

        // Assert
        Assert.Equal(dashboardViews.Count, result.Count());
        Assert.Equal(dashboardViews.Count, _dbContext.DashboardViews.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        await _repository.AddRangeAsync(dashboardViews);

        // Act
        var result = await _repository.RemoveRangeAsync(dashboardViews);

        // Assert
        Assert.Equal(dashboardViews.Count, result.Count());
        Assert.Empty(_dbContext.DashboardViews);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region GetSitePropertiesByBusinessServiceId Tests

    [Fact]
    public async Task GetSitePropertiesByBusinessServiceId_ShouldReturnEntity_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var bs = _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(bs);

        var dashboardView = _dashboardViewFixture.DashboardViewDto;
 

        dashboardView.BusinessServiceId = bs.ReferenceId;
    
        await _repository.AddAsync(dashboardView);

        // Act
        var result = await _repository.GetSitePropertiesByBusinessServiceId(DashboardViewFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(DashboardViewFixture.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetSitePropertiesByBusinessServiceId_ShouldReturnFilteredEntity_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var bs = _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(bs);

        var dashboardView = _dashboardViewFixture.DashboardViewDto;
       
        dashboardView.BusinessServiceId = bs.ReferenceId;

        dashboardView.CompanyId = "ChHILD_COMPANY_123";
        await _repositoryNotParent.AddAsync(dashboardView);

        // Act
        var result = await _repositoryNotParent.GetSitePropertiesByBusinessServiceId(DashboardViewFixture.BusinessServiceId);

        // Assert
        // Result should be filtered based on assigned infrastructure
        Assert.NotNull(result);
    }

    #endregion

    #region GetBusinessViewListByBusinessServiceIds Tests

    [Fact]
    public async Task GetBusinessViewListByBusinessServiceIds_ShouldReturnEntities_WhenExists_AndIsAllInfraTrue()
    {

        // Arrange
        var BsList = _businessServiceFixture.BusinessServiceDto;
        BsList.CompanyId = "ChHILD_COMPANY_123";
        await _dbContext.BusinessServices.AddAsync(BsList);

        var dashboardViews = _dashboardViewFixture.DashboardViewList;

        dashboardViews[0].BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";

        await _repository.AddRangeAsync(dashboardViews);

        var businessServiceIds = new List<string> { DashboardViewFixture.BusinessServiceId };

        // Act
        var result = await _repository.GetBusinessViewListByBusinessServiceIds(businessServiceIds);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Contains(x.BusinessServiceId, businessServiceIds));
    }

    [Fact]
    public async Task GetBusinessViewListByBusinessServiceIds_ShouldReturnFilteredEntities_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        await _repositoryNotParent.AddRangeAsync(dashboardViews);

        var businessServiceIds = new List<string> { DashboardViewFixture.BusinessServiceId };

        // Act
        var result = await _repositoryNotParent.GetBusinessViewListByBusinessServiceIds(businessServiceIds);

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region GetBusinessViewListByBusinessServiceId Tests

    [Fact]
    public async Task GetBusinessViewListByBusinessServiceId_ShouldReturnEntities_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;

        await _repository.AddRangeAsync(dashboardViews);

        // Act
        var result = await _repository.GetBusinessViewListByBusinessServiceId("c9b3cd51-f688-4667-be33-46f82b7086fa");

        // Assert
        Assert.NotNull(result);

    }

    [Fact]
    public async Task GetBusinessViewListByBusinessServiceId_ShouldReturnFilteredEntities_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;

        dashboardViews[0].CompanyId = "COMPANY_123";
        dashboardViews.ForEach(x => x.BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa");

        await _repositoryNotParent.AddRangeAsync(dashboardViews);

        // Act
        var result = await _repositoryNotParent.GetBusinessViewListByBusinessServiceId("c9b3cd51-f688-4667-be33-46f82b7086fa");

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    [Fact]
    public async Task GetBusinessViewListByBusinessServiceId_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _repository.GetBusinessViewListByBusinessServiceId("invalid-guid"));
    }

    #endregion

    #region GetBusinessViewListByBusinessServiceIdDatalag Tests

    [Fact]
    public async Task GetBusinessViewListByBusinessServiceIdDatalag_ShouldReturnEntities_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;

        dashboardViews[0].BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";

        await _repository.AddRangeAsync(dashboardViews);

        // Act
        var result = await _repository.GetBusinessViewListByBusinessServiceIdDatalag("c9b3cd51-f688-4667-be33-46f82b7086fa");

        // Assert
        Assert.NotNull(result);
        //Assert.All(result, x => Assert.Equal(DashboardViewFixture.BusinessServiceId, x.BusinessServiceId));
    }

    [Fact]
    public async Task GetBusinessViewListByBusinessServiceIdDatalag_ShouldReturnFilteredEntities_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;

        dashboardViews[0].CompanyId = "COMPANY_123";
        dashboardViews.ForEach(x => x.BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa");

        await _repositoryNotParent.AddRangeAsync(dashboardViews);

        // Act
        var result = await _repositoryNotParent.GetBusinessViewListByBusinessServiceIdDatalag("c9b3cd51-f688-4667-be33-46f82b7086fa");

        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetBusinessViewListByBusinessServiceIdDatalag_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _repository.GetBusinessViewListByBusinessServiceIdDatalag("invalid-guid"));
    }

    #endregion

    #region GetBusinessViewListByBusinessFunctionId Tests

    [Fact]
    public async Task GetBusinessViewListByBusinessFunctionId_ShouldReturnEntities_WhenExists_AndIsAllInfraTrue()
    {
        var bsList=_businessFunctionFixture.BusinessFunctionList;

        bsList[0].ReferenceId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";
        await _dbContext.BusinessFunctions.AddRangeAsync(bsList);
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        dashboardViews[0].CompanyId = "ChHILD_COMPANY_123";
        dashboardViews[0].BusinessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";
        dashboardViews[0].BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";

        dashboardViews[0].InfraObjectId = "INFRA_1";
        await _repository.AddRangeAsync(dashboardViews);




        // Act
        var result = await _repository.GetBusinessViewListByBusinessFunctionId(dashboardViews[0].BusinessFunctionId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DashboardViewFixture.BusinessFunctionId, x.BusinessFunctionId));
    }

    [Fact]
    public async Task GetBusinessViewListByBusinessFunctionId_ShouldReturnFilteredEntities_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        dashboardViews[0].CompanyId = "ChHILD_COMPANY_123";
        dashboardViews[0].BusinessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";
        dashboardViews[0].BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";

        dashboardViews[0].InfraObjectId = "INFRA_1";

        await _repositoryNotParent.AddRangeAsync(dashboardViews);

        // Act
        var result = await _repositoryNotParent.GetBusinessViewListByBusinessFunctionId(dashboardViews[0].BusinessFunctionId);

        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetBusinessViewListByBusinessFunctionId_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _repository.GetBusinessViewListByBusinessFunctionId("invalid-guid"));
    }

    #endregion

    #region GetBusinessViewByLast7Days Tests

    [Fact]
    public async Task GetBusinessViewByLast7Days_ShouldReturnEntitiesFromLast7Days()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        // Set some views within last 7 days
        dashboardViews.Take(3).ToList().ForEach(x => x.LastModifiedDate = DateTime.Now.AddDays(-3));
        // Set some views older than 7 days
        dashboardViews.Skip(3).ToList().ForEach(x => x.LastModifiedDate = DateTime.Now.AddDays(-10));

        await _repository.AddRangeAsync(dashboardViews);

        // Act
        var result = await _repository.GetBusinessViewByLast7Days();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.True(x.LastModifiedDate.Date >= DateTime.Now.Date.AddDays(-7)));
    }

    [Fact]
    public async Task GetBusinessViewByLast7Days_ShouldReturnEmptyList_WhenNoRecentEntities()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        // Set all views older than 7 days
        dashboardViews.ForEach(x => x.LastModifiedDate = DateTime.Now.AddDays(-10));

        _dbContext.DashboardViews.AddRange(dashboardViews);

        // Act
        var result = await _repository.GetBusinessViewByLast7Days();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetInfraObjectSummaryReport Tests

    [Fact]
    public async Task GetInfraObjectSummaryReport_ShouldReturnEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        await _repository.AddRangeAsync(dashboardViews);

        // Act
        var result = await _repository.GetInfraObjectSummaryReport();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardViews.Count, result.Count);
    }

    [Fact]
    public async Task GetInfraObjectSummaryReport_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var dashboardViews = _dashboardViewFixture.DashboardViewList;
        await _repositoryNotParent.AddRangeAsync(dashboardViews);

        // Act
        var result = await _repositoryNotParent.GetInfraObjectSummaryReport();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange

        var bs = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bs);

        var dashboardViews = _dashboardViewFixture.DashboardViewList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(dashboardViews);
        var initialCount = dashboardViews.Count;

        var toUpdate = dashboardViews.Take(2).ToList();
        toUpdate.ForEach(x => x.BusinessServiceId = bs.ReferenceId);
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = dashboardViews.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.BusinessServiceId == bs.ReferenceId).ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
