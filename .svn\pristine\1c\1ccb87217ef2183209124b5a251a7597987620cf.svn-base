using ContinuityPatrol.Application.Features.FiaImpactCategory.Events.Update;

namespace ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Update;

public class
    UpdateFiaImpactCategoryCommandHandler : IRequestHandler<UpdateFiaImpactCategoryCommand,
        UpdateFiaImpactCategoryResponse>
{
    private readonly IFiaImpactCategoryRepository _fiaImpactCategoryRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateFiaImpactCategoryCommandHandler(IMapper mapper,
        IFiaImpactCategoryRepository fiaImpactCategoryRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _fiaImpactCategoryRepository = fiaImpactCategoryRepository;
        _publisher = publisher;
    }

    public async Task<UpdateFiaImpactCategoryResponse> Handle(UpdateFiaImpactCategoryCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _fiaImpactCategoryRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.FiaImpactCategory), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateFiaImpactCategoryCommand),
            typeof(Domain.Entities.FiaImpactCategory));

        await _fiaImpactCategoryRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateFiaImpactCategoryResponse
        {
            Message = Message.Update(nameof(Domain.Entities.FiaImpactCategory), eventToUpdate.Name),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new FiaImpactCategoryUpdatedEvent { Name = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}