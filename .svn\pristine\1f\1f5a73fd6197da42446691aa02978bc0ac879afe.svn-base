﻿using ContinuityPatrol.Application.Features.Setting.Commands.Create;
using ContinuityPatrol.Application.Features.Setting.Commands.Update;
using ContinuityPatrol.Application.Features.Setting.Queries.GetBySKey;
using ContinuityPatrol.Application.Features.Setting.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SettingModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ISettingService
{
    Task<bool> IsSettingNameExist(string settingName, string id);
    Task<PaginatedResult<SettingListVm>> GetSettingPaginatedList(GetSettingPaginatedListQuery query);
    Task<BaseResponse> DeleteAsync(string settingId);
    Task<BaseResponse> UpdateAsync(UpdateSettingCommand setting);
    Task<BaseResponse> CreateAsync(CreateSettingCommand setting);
    Task<List<SettingListVm>> GetSettingsList();
    Task<GetSettingBySKeyVm> GetSettingBySKey(string sKey);
}