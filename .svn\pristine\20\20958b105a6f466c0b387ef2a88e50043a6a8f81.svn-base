using ContinuityPatrol.Application.Features.DriftEvent.Commands.Create;
using ContinuityPatrol.Application.Features.DriftEvent.Commands.Update;
using ContinuityPatrol.Application.Features.DriftEvent.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftEvent.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftEventModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Drift;

public class DriftEventService : BaseClient, IDriftEventService
{
    public DriftEventService(IConfiguration config, IAppCache cache, ILogger<DriftEventService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<DriftEventListVm>> GetDriftEventList()
    {
        var request = new RestRequest("api/v6/driftevents");

        return await GetFromCache<List<DriftEventListVm>>(request, "GetDriftEventList");
    }

    public async Task<BaseResponse> CreateAsync(CreateDriftEventCommand createDriftEventCommand)
    {
        var request = new RestRequest("api/v6/driftevents", Method.Post);

        request.AddJsonBody(createDriftEventCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDriftEventCommand updateDriftEventCommand)
    {
        var request = new RestRequest("api/v6/driftevents", Method.Put);

        request.AddJsonBody(updateDriftEventCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/driftevents/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<DriftEventDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/driftevents/{id}");

        return await Get<DriftEventDetailVm>(request);
    }
    #region NameExist
    public async Task<bool> IsDriftEventNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/driftevents/name-exist?drifteventName={name}&id={id}");

        return await Get<bool>(request);
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<DriftEventListVm>> GetPaginatedDriftEvents(GetDriftEventPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/driftevents/paginated-list");

        return await Get<PaginatedResult<DriftEventListVm>>(request);
    }
    #endregion
}
