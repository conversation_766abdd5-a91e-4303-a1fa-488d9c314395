QUnit.module("AccessManager Tests", hooks => {
    let $fixture;

    hooks.beforeEach(() => {
        $fixture = $("#qunit-fixture");
        $fixture.append(`
            <select id="txtUserRoleName">
                <option id="1">Administrator</option>
                <option id="2">CustomRole</option>
            </select>
            <div id="treeList-error"></div>
            <button id="btnAccSave"></button>
            <button id="btnCancel"></button>
            <input id="chkMonitor" type="checkbox" class="form-check-input">
            <input id="chkManagment" type="checkbox" class="form-check-input">
            <input id="chkConfigAdd" type="checkbox" class="form-check-input">
            <input id="chkConfigDelete" type="checkbox" class="form-check-input">
            <input id="chkConfigView" type="checkbox" class="form-check-input">
            <input id="chkcloudView" type="checkbox" class="form-check-input">
        `);
    });

    QUnit.test("updateCheckboxByRole disables checkboxes for SuperAdmin", assert => {
        const role = "SuperAdmin";
        $("input[type='checkbox']").prop("checked", false).prop("disabled", false);

        const checkboxes = $("input[type='checkbox']");
        const disableAll = ["SuperAdmin", "Administrator", "Operator", "Manager"].includes(role);
        checkboxes.toggleClass('opacity-80', disableAll);
        checkboxes.prop({ 'checked': !disableAll, 'disabled': disableAll });

        checkboxes.each(function () {
            assert.ok($(this).hasClass('opacity-80'), "Checkbox has 'opacity-80' class");
            assert.ok($(this).prop('disabled'), "Checkbox is disabled");
        });
    });

    QUnit.test("validateDropDown returns false when value is empty", assert => {
        const result = validateDropDown('', "Error Message", $('#treeList-error'));
        assert.strictEqual(result, false, "Empty value triggers error");
        assert.ok($('#treeList-error').hasClass('field-validation-error'), "Error class is applied");
    });

    QUnit.test("validateDropDown returns true when value is provided", assert => {
        const result = validateDropDown('Valid', "", $('#treeList-error'));
        assert.strictEqual(result, true, "Valid value passes validation");
        assert.notOk($('#treeList-error').hasClass('field-validation-error'), "Error class is removed");
    });

    QUnit.test("anyCheckboxChecked detects checked checkbox", assert => {
        $('#chkMonitor').prop('checked', true);
        assert.ok(anyCheckboxChecked(), "Checkbox is detected as checked");
    });

    QUnit.test("getCheckedCategories returns true with valid permission", assert => {
        const mockData = {
            Permissions: {
                Dashboard: {
                    View: true,
                    Monitor: false
                }
            }
        };
        const result = getCheckedCategories(mockData);
        assert.true(result, "Valid permission returns true");
    });

    QUnit.test("getCheckedCategories returns false with no checkbox checked", assert => {
        const mockData = {
            Permissions: {
                Dashboard: {
                    View: false,
                    Monitor: false
                }
            }
        };
        const result = getCheckedCategories(mockData);
        assert.false(result, "No checkbox checked returns false");
        assert.ok($('#treeList-error').hasClass('field-validation-error'), "Error message shown");
    });

    QUnit.test("AJAX call to getUserRoleById triggers success callback", assert => {
        const done = assert.async();
        const testRoleId = "2";

        // Mock AJAX
        const originalAjax = $.ajax;
        $.ajax = function (options) {
            assert.equal(options.url.includes("GetRoleDetails"), true, "Correct URL is called");
            options.success({
                id: "2",
                roleName: "CustomRole",
                properties: JSON.stringify({
                    Permissions: {
                        Dashboard: { View: true, Monitor: true, Management: false }
                    }
                })
            });
            done();
        };

        getUserRoleById(testRoleId);

        $.ajax = originalAjax; // restore original
    });
});
