﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class DrReadyLogRepositoryMocks
{
    public static Mock<IDrReadyLogRepository> CreateDrReadyLogRepository(List<DRReadyLog> drReadyLogs)
    {
        var createDrReadyLogRepository = new Mock<IDrReadyLogRepository>();

        createDrReadyLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(drReadyLogs);

        createDrReadyLogRepository.Setup(repo => repo.AddAsync(It.IsAny<DRReadyLog>())).ReturnsAsync(
            (DRReadyLog drReadyLog) =>
            {
                drReadyLog.Id = new Fixture().Create<int>();

                drReadyLog.ReferenceId = new Fixture().Create<Guid>().ToString();

                drReadyLogs.Add(drReadyLog);

                return drReadyLog;
            });

        return createDrReadyLogRepository;
    }

    public static Mock<IDrReadyLogRepository> UpdateDrReadyLogRepository(List<DRReadyLog> drReadyLogs)
    {
        var updateDrReadyLogRepository = new Mock<IDrReadyLogRepository>();

        updateDrReadyLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(drReadyLogs);

        updateDrReadyLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => drReadyLogs.SingleOrDefault(x => x.ReferenceId == i));

        updateDrReadyLogRepository.Setup(repo => repo.UpdateAsync(It.IsAny<DRReadyLog>())).ReturnsAsync((DRReadyLog drReadyLog) =>
        {
            var index = drReadyLogs.FindIndex(item => item.ReferenceId == drReadyLog.ReferenceId);

            drReadyLogs[index] = drReadyLog;

            return drReadyLog;

        });
        return updateDrReadyLogRepository;
    }

    public static Mock<IDrReadyLogRepository> DeleteDrReadyLogRepository(List<DRReadyLog> drReadyLogs)
    {
        var deleteDrReadyLogRepository = new Mock<IDrReadyLogRepository>();

        deleteDrReadyLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(drReadyLogs);

        deleteDrReadyLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => drReadyLogs.SingleOrDefault(x => x.ReferenceId == i));

        deleteDrReadyLogRepository.Setup(repo => repo.UpdateAsync(It.IsAny<DRReadyLog>())).ReturnsAsync((DRReadyLog drReadyLog) =>
        {
            var index = drReadyLogs.FindIndex(item => item.ReferenceId == drReadyLog.ReferenceId);

            drReadyLog.IsActive = false;

            drReadyLogs[index] = drReadyLog;

            return drReadyLog;
        });

        return deleteDrReadyLogRepository;
    }

    public static Mock<IDrReadyLogRepository> GetDrReadyLogRepository(List<DRReadyLog> drReadyLogs)
    {
        var drReadyLogRepository = new Mock<IDrReadyLogRepository>();

        drReadyLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(drReadyLogs);

        drReadyLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => drReadyLogs.SingleOrDefault(x => x.ReferenceId == i));

        return drReadyLogRepository;
    }

    public static Mock<IDrReadyLogRepository> GetDrReadyLogEmptyRepository()
    {
        var drReadyLogEmptyRepository = new Mock<IDrReadyLogRepository>();

        drReadyLogEmptyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<DRReadyLog>());

        return drReadyLogEmptyRepository;
    }

    public static Mock<IDrReadyLogRepository> GetPaginatedDrReadyLogRepository(List<DRReadyLog> drReadyLogs)
    {
        var drReadyLogRepository = new Mock<IDrReadyLogRepository>();

        var queryableDrReadyLog = drReadyLogs.BuildMock();

        drReadyLogRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableDrReadyLog);

        return drReadyLogRepository;
    }

    public static Mock<IDrReadyLogRepository> GetDrReadyLogByBusinessServiceIdRepository(List<DRReadyLog> drReadyLogs)
    {
        var drReadyLogRepository = new Mock<IDrReadyLogRepository>();

        drReadyLogRepository.Setup(repo => repo.GetDrReadyLogByBusinessServiceId(It.IsAny<string>())).ReturnsAsync((string i) => drReadyLogs.SingleOrDefault(x => x.BusinessServiceId == i));

        return drReadyLogRepository;
    }

    public static Mock<IDrReadyLogRepository> GetDrReadyLogByLast7DaysRepository(List<DRReadyLog> drReadyLogs)
    {
        var drReadyLogRepository = new Mock<IDrReadyLogRepository>();

        drReadyLogRepository.Setup(repo => repo.GetDrReadyLogByLast7Days()).ReturnsAsync(drReadyLogs);

        return drReadyLogRepository;
    }

    public static Mock<IDrReadyLogRepository> GetDrReadyReportByBusinessServiceIdRepository(List<DRReadyLog> drReadyLogs)
    {
        var drReadyLogRepository = new Mock<IDrReadyLogRepository>();

        drReadyLogRepository.Setup(repo => repo.GetDrReadyLogForDrReadyReportByBusinessServiceId(It.IsAny<string>())).ReturnsAsync((string i) => drReadyLogs.Where(x => x.BusinessServiceId == i).ToList());

        return drReadyLogRepository;
    }
}