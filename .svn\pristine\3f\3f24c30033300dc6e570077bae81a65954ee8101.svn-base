﻿using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Create;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Update;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Event.PaginatedView;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.InfraReplicationMappingModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class InfraReplicationMappingController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IDataProvider _provider;
    private readonly ILogger<InfraReplicationMappingController> _logger;
    private readonly IMapper _mapper;

    public InfraReplicationMappingController(IPublisher publisher, IDataProvider provider, IMapper mapper, ILogger<InfraReplicationMappingController> logger)
    {
        _publisher = publisher;
        _provider = provider;
        _logger = logger;
        _mapper = mapper;
    }

    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in InfraReplicationMapping");

        await _publisher.Publish(new InfraReplicationMappingPaginatedEvent());

        return View();
    }


    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(InfraReplicationMappingViewModel infraReplicationMapping)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in InfraReplicationMapping");

        var infraReplicationId = Request.Form["Id"].ToString();

        try
        {
            if (infraReplicationId.IsNullOrWhiteSpace())
            {
                var create = _mapper.Map<CreateInfraReplicationMappingCommand>(infraReplicationMapping);

                var result = await _provider.InfraReplicationMapping.CreateAsync(create);

                _logger.LogDebug($"Creating InfraReplicationMapping '{create.Type}'.");

                TempData.NotifySuccess(result.Message);
            }
            else
            {
                var update = _mapper.Map<UpdateInfraReplicationMappingCommand>(infraReplicationMapping);

                var result = await _provider.InfraReplicationMapping.UpdateAsync(update);

                _logger.LogDebug($"Updating InfraReplicationMapping '{update.Type}'.");

                TempData.NotifySuccess(result.Message);
            }
            _logger.LogDebug("CreateOrUpdate operation completed successfully in infra replication mapping, returning view.");
            return RedirectToAction("List");
        }
        catch (ValidationException ex)
        {
            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());
            _logger.LogError($"Validation exception on infra replication mapping page: {ex.ValidationErrors.FirstOrDefault()}");
            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on infra replication mapping page while processing the request for create or update.", ex);
            TempData.NotifyWarning(ex.Message);
            return RedirectToAction("List");
        }
    }

    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in InfraReplicationMapping");

        try
        {
            var replication = await _provider.InfraReplicationMapping.DeleteAsync(id);
            TempData.NotifySuccess(replication.Message);
            _logger.LogDebug("Successfully deleted record in InfraReplicationMapping");
            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on infra replication mapping.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetPagination(GetInfraReplicationMappingPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in InfraReplicationMapping");

        try
        {
            _logger.LogDebug("Successfully retrieved InfraReplicationMapping paginated list on infra replication mapping page");
            return Json(await _provider.InfraReplicationMapping.GetPaginatedInfraReplicationMapping(query));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on infraReplicationMapping page while processing the pagination request.", ex);

            return ex.GetJsonException();
        }
    }

    public async Task<JsonResult> GetReplicationComponentType()
    {
        _logger.LogDebug("Entering GetReplicationComponentType method in InfraReplicationMapping");

        try
        {
            var serverList = await _provider.ComponentType.GetComponentTypeListByName("Replication");
            _logger.LogDebug("Successfully retrieving component type for replication in InfraReplicationMapping");
            return Json(new { Success = true, data = serverList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on infra replication mapping page while retrieving the component type for replication.", ex);
            return ex.GetJsonException();
        }
    }

    public async Task<JsonResult> GetDatabaseComponentType()
    {
        _logger.LogDebug("Entering GetDatabaseComponentType method in InfraReplicationMapping");

        try
        {
            var databaseList = await _provider.ComponentType.GetComponentTypeListByName("Database");
            _logger.LogDebug("Successfully retrieving component type for database in InfraReplicationMapping");
            return Json(new { Success = true, data = databaseList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on infra replication mapping page while retrieving the component type for database.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetReplicationMasterByInfraMasterName(string infraMasterName)
    {
        _logger.LogDebug("Entering GetReplicationMasterByInfraMasterName method in InfraReplicationMapping");

        if (string.IsNullOrWhiteSpace(infraMasterName))
        {
            return Json("");
        }

        try
        {
            var replicationMasterNames = await _provider.ReplicationMaster.GetReplicationMasterByInfraMasterName(infraMasterName);
            _logger.LogDebug($"Successfully retrieving replication master by infraMasterName '{infraMasterName}' in InfraReplicationMapping");
            return Json(new { Success = true, data = replicationMasterNames });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on infra replication mapping page while retrieving replication master detail by infraMasterName '{infraMasterName}'.", ex);
            return ex.GetJsonException();
        }
    }
}