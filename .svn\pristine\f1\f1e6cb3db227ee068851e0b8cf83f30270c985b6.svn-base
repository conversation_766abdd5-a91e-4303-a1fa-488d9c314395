﻿const approvalURL = {
    getPaginatedlist: "/Manage/Template/GetPaginatedlist",
    nameExist: "Manage/Template/IsApprovalMatrixNameExist",
    createOrUpdate: "Manage/Template/CreateOrUpdate",
    usersList: "Manage/Template/ApprovalMatrixUsersList",
    getBusinessFunctions: "Manage/Template/GetBusinessFunctions",
    delete: "Manage/Template/Delete"
};
let templateButtonDisable = false;
let titlesArray = "";
let startButtonValue = 1;
let dataTable = "";
let processNameArray = { propsId: "", name: "", straightLineData: "", properties: [], userLists: [], ruleSet: [], SLA: {}, notification: [] };
let editedData = "";
let propertiesID = "";
let editedProcessName = "";
let addProcessName = "Add";
let modifyProcessName = "Update";
let approvalTemplateName = "";
let businessServiceName = "";
let businessFunctionName = "";
let editWhileCreate = false;
let straightLineData = [];
let addTransition = false;
let selectedUsersArrayLists = [];
let userListsLength = "";


let permission = {
    "create": $("#createApprovalMatrix").data("create-permission").toLowerCase(),
    "delete": $("#deleteApprovalMatrix").data("delete-permission").toLowerCase()
}

if (permission.create == 'false') {
    $(".btn-approvalmattemplate-Create").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
}

$(function () {
    approvalPreventSpecialKeys('#searchTemplate');
    let selectedValues = [];
    dataTable = $('#templateList').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "fixedColumns": {
                left: 1,
                right: 1
            },
            "ajax": {
                "type": "GET",
                "url": approvalURL.getPaginatedlist,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "Name" : sortIndex === 2 ? "businessServiceName" : sortIndex === 3 ? "businessFunctionName" : ""
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#searchTemplate').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    const isEmpty = json?.data?.length === 0;
                    $(".pagination-column").toggleClass("disabled", isEmpty);
                    return json?.data;
                }
            },
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        return type === 'display' ? meta.row + 1 : data;
                    },
                    "orderable": false
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>  ${data || "NA"} </span>` : data;
                    }
                },
                {
                    "data": "businessServiceName", "name": "businessServiceName", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>  ${data || "NA"} </span>` : data;
                    }
                },
                {
                    "data": "businessFunctionName", "name": "businessFunctionName", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>  ${data || "NA"} </span>` : data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        const isEditAllowed = permission.create === "true";
                        const isDeleteAllowed = permission.delete === "true";

                        const editBtn = isEditAllowed
                            ? `<span role="button" title="Edit" class="btnTemplateEdit" data-template='${JSON.stringify(row)}'>
                   <i class="cp-edit"></i>
               </span>`
                            : `<span role="button" title="Edit" class="icon-disabled">
                   <i class="cp-edit"></i>
               </span>`;

                        const deleteBtn = isDeleteAllowed
                            ? `<span role="button" title="Delete" class="btnTemplateDelete" data-template-id="${row.id}" data-template-name="${row.name}" data-bs-toggle="modal" data-bs-target="#deleteTemplateModal">
                   <i class="cp-Delete"></i>
               </span>`
                            : `<span role="button" title="Delete" class="icon-disabled">
                   <i class="cp-Delete"></i>
               </span>`;
                        return `<div class="d-flex align-items-center gap-2">${editBtn}${deleteBtn}</div>`;
                    },
                    orderable: false
                }
                
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#searchTemplate').on('input', commonDebounce(function (e) {
        const $input = $(this);
        const nameCheckbox = $("#Name");

        // Sanitize: collapse multiple spaces, trim both ends
        let sanitizedValue = $input.val().replace(/\s+/g, ' ').trim();

        $input.val(sanitizedValue); // Reflect cleaned value in input

        // Clear previously selected values if needed
        if (sanitizedValue === "") {
            $input.val("");
        }

        // Ensure selectedValues is modified only when appropriate
        if (nameCheckbox.is(':checked') && sanitizedValue) {
            selectedValues.push(nameCheckbox.val() + sanitizedValue);
        }

        // Reload datatable with optional text fallback if empty
        dataTable.ajax.reload(function (json) {
            if (sanitizedValue.length === 0 && json?.data?.length === 0) {
                $('.dataTables_empty').text('No Data Found');
            } else if (json?.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        });
    }, 500));


    $("#Create").on("click", function () {
        resetTemplateState();
        resetTemplateUI();
        initBlankTemplateTree();
    });

    $("#approvalTemplateName").on("keyup", function () {
        templateNameValidation($(this).val(), $('#templateID').val(), approvalURL.nameExist,
            $("#approvalTemplateNameError"), "Enter template name")
    });

    $("#inputDuration").on("keyup", function () {
        commonInputValidation($(this).val(), $("#enterDurationError"), "Enter duration");
    });

    $("#selectDuration").on("change", function () {
        commonInputValidation($(this).val(), $("#selectDurationError"), "Select duration");
    });

    $("#saveApprovalTemplate").on("click", async function () {
        $('#saveprocessModal').modal("show");
        setTimeout(() => {
            $('#approvalTemplateName').val(approvalTemplateName || "");
            $("#selectOperationalService").val(businessServiceName || "").trigger("change");
            $("#operationalServiceError").text("").removeClass("field-validation-error");
        })
    })

    $("#saveTemplateName").on("click", async function () {
        if (titlesArray.length > 1) {
            straightLineData = titlesArray.slice(0, -1).map((from, i) => ({
                from,
                to: titlesArray[i + 1],
            }));
        }

        const templateName = $("#approvalTemplateName").val();
        const templateId = $("#templateID").val();
        const serviceId = $("#selectOperationalService").val();
        const functionId = $("#selectOperationalFunction").val();

        const validateTemplateName = await templateNameValidation(
            templateName,
            templateId,
            approvalURL.nameExist,
            $("#approvalTemplateNameError"),
            "Enter template name"
        );

        const operationalServiceValid = commonInputValidation(
            serviceId,
            $("#operationalServiceError"),
            "Select operational service"
        );

        const templateData = (editedData && processNameArray.name)
            ? replaceObjectIfEmptyName(editedData, processNameArray)
            : editedData || processNameArray;

        templateData.straightLineData = straightLineData;
        const flattenedProperties = flattenPropertiesWithRoot(templateData);

        if (validateTemplateName && operationalServiceValid && !templateButtonDisable) {
            templateButtonDisable = true;

            const approvalMatrixTemplate = {
                Id: templateId || "",
                BusinessServiceId: serviceId,
                BusinessServiceName: $("#selectOperationalService :selected").text(),
                BusinessFunctionId: functionId,
                BusinessFunctionName: $("#selectOperationalFunction :selected").text(),
                Name: templateName,
                Description: "Description",
                Properties: JSON.stringify(flattenedProperties),
            };

            try {
                const response = await $.ajax({
                    type: "POST",
                    url: RootUrl + approvalURL.createOrUpdate,
                    dataType: "json",
                    data: approvalMatrixTemplate,
                    headers: {
                        "RequestVerificationToken": await gettoken()
                    }
                });

                if (response?.success) {
                    notificationAlert("success", response.data?.message);
                    dataTableCreateAndUpdate($("#saveApprovalTemplate"), dataTable);
                } else {
                    errorNotification(response);
                }

                clearFieldsAfterSace();
            } catch (error) {
                console.error("Save template failed:", error);
                errorNotification("An unexpected error occurred while saving the template.");
            } finally {
                templateButtonDisable = false;
            }
        }

        // Re-enable input fields
        $("#ApTwo, #RjTwo").prop("disabled", false);
    });


    $("#addTemplate").on("click", async function () {
        $("#approvalProcessName, #approvalTemplateName, #textDescription, #inputDuration, #ApTwo, #RjTwo").val("");
        $(["#approvalProcessNameError","#approvalTemplateNameError","#UserNameError","#enterDurationError","#selectDurationError","#operationalServiceError"].join(", ")).text("").removeClass("field-validation-error");
        $("#userNameList, #selectDuration, #ApOne, #RjOne, #selectOperationalService").val("").trigger("change.select2");

        $("#ApThree, #RjThree").text(0);
        $("#saveProcessName").text("Add");
        $("#saveApprovalTemplate").css("visibility", "hidden");
        $("#processDelete").addClass("d-none");
        $("#ApTwo, #RjTwo").prop("disabled", false);
        $("#messageIcon, #emailIcon").prop("checked", false);
        toggleWizard(true);
        addTransition = true;
        if (selectedUsersArrayLists.length) {
            await getUsersData();
        }
    });


    $("#selectOperationalService").on("change", async function () {
        commonInputValidation($(this).val(), $("#operationalServiceError"), "Select operational service");
        if ($(this).val()) {
            await $.ajax({
                type: "GET",
                async: false,
                url: RootUrl + approvalURL.getBusinessFunctions,
                dataType: "json",
                data: { id: $(this).val() },
                success: function (result) {
                    if (result?.success) {
                        if (result && result?.data?.length > 0) {
                            let $selectOperationalFunction = $('#selectOperationalFunction');
                            $selectOperationalFunction.empty().append('<option value=""></option>');
                            result?.data?.forEach(item => {
                                $selectOperationalFunction.append('<option value="' + item.id + '">' + item.name + '</option>');
                            });
                            if (businessFunctionName) {
                                $selectOperationalFunction.val(businessFunctionName);
                            }
                        }
                    } else {
                        errorNotification(result);
                    }
                },
            });
        }
    });

    $("#approvalProcessName").on("keyup input", function () {
        commonInputValidation($(this).val(), $("#approvalProcessNameError"), "Enter process name");
    });

    $("#ApOne").on("change", function () {
        commonInputValidation($(this).val(), $("#ApOneError"), "Select option", "approver");
    });

    $("#ApTwo").on("keyup", function (event) {
        let value = $(this).val().trim();
        let userLength = $("#userNameList").val().length;
        let userValue = parseInt(value, 10) || 0;

        if (userLength < userValue) {
            $(this).val(userLength);
        }
        commonInputValidation(value, $("#ApTwoError"), "Enter approvers");
    });

    $("#RjOne").on("change", function () {
        commonInputValidation($(this).val(), $("#RjOneError"), "Select option", "reject");
    });

    $("#RjTwo").on("keyup", function () {
        const $this = $(this);
        const inputVal = $this.val().trim();
        const userCount = $("#userNameList").val().length;
        const numericVal = parseInt(inputVal, 10) || 0;

        if (numericVal > userCount) {
            $this.val(userCount);
        }

        commonInputValidation($this.val().trim(), $("#RjTwoError"), "Enter approvers");
    });


    $("#processDelete").on("click", function () {
        if (!propertiesID || !editedData) return;
        editedData = removeObjectByPropsId(editedData, propertiesID);
        const flatList = flattenPropertiesWithRoot(editedData);
        updateProcessDeleteButtonVisibility(flatList);
        resetApprovalContainer();
        setTimeout(() => {
            recursiveEdit(editedData);
        }, 100);
    });

    $("#userNameList").on("change", function () {
        let value = $(this).val();
        $("#ApThree, #RjThree").text(value.length);
        commonInputValidation(value[0], $("#UserNameError"), "Select user");
    });

    $("#saveProcessName").on("click", function () {
        const selectedUsers = getSelectedUsers();
        const processName = $("#approvalProcessName").val();
        const textDescription = $("#textDescription").val();
        const enterDuration = $("#inputDuration").val();
        const selectDuration = $("#selectDuration").val();

        const approvalObject = getRuleObject("#ApOne", "#ApTwo", "#ApThree");
        const rejectObject = getRuleObject("#RjOne", "#RjTwo", "#RjThree");
        const notification = buildNotificationObject();

        if (!validateRuleFields()) return;

        if ($("#saveProcessName").text() === modifyProcessName) {
            const newData = {
                propsId: propertiesID,
                name: processName,
                userLists: selectedUsers,
                description: textDescription,
                SLA: { duration: enterDuration, period: selectDuration },
                ruleSet: [approvalObject, rejectObject],
                notification: [notification],
            };

            editedData = updateByPropsId(editedData || processNameArray, propertiesID, newData);

            $("#closeOffcanvas").trigger("click");
            $("#approvalContainer").empty();
            startButtonValue = 1;

            $("#offcanvasExample").offcanvas("hide");

            $("#approvalTemplateName").val(approvalTemplateName || "");
            $("#selectOperationalService").val(businessServiceName || "");
            setTimeout(() => {
                $("#selectOperationalFunction").val(businessFunctionName || "");
            });

            recursiveEdit(editedData, editWhileCreate);
        } else {
            createEditTemplate(processName,textDescription,selectedUsers,enterDuration,selectDuration,approvalObject,rejectObject,notification);
        }

        // Reset disabled state & trigger end node
        $("#ApTwo").prop("disabled", false);
        $("#RjTwo").prop("disabled", false);
        $("#endTemplate").trigger("click");
    });


    //$("#userList").on("change", function () {
    //    let $this = $(this);
    //    if ($this.prop("checked")) {
    //        $('#dynamicLabel').html("User List");
    //    }
    //});
    //$("#userGroupList").on("change", function () {
    //    let $this = $(this);
    //    if ($this.prop("checked")) {
    //        $('#dynamicLabel').html("User Group List");
    //    }
    //});
    //$("#addTransition").on("click", function () {
    //    $("#templateFromName").val("");
    //    $("#templateToName").val("");
    //    $("#transitionName").val("");
    //    $("#transitionColour").val("");
    //    $("#addTransitionModal").modal("show");
    //});

    //$("#templateFromName").on("input", function () {
    //    commonInputValidation($(this).val().trim(), $("#templateFromNameError"), "Enter From Name");
    //});

    //$("#templateToName").on("input", function () {
    //    commonInputValidation($(this).val().trim(), $("#templateToNameError"), "Enter To Name");
    //});

    //$("#transitionName").on("input", function () {
    //    commonInputValidation($(this).val().trim(), $("#transitionNameError"), "Enter Transition Name");
    //});

    //$("#transitionColour").on("input", function () {
    //    commonInputValidation($(this).val().trim(), $("#transitionColourError"), "Enter Colour Code");
    //});

    //$("#validateTransition").on("click", function () {
    //    let fromName = $("#templateFromName").val();
    //    let toName = $("#templateToName").val();
    //    let transitionName = $("#transitionName").val();
    //    let transitionColour = $("#transitionColour").val();
    //    let templateFromNameValidation = commonInputValidation(fromName, $("#templateFromNameError"), "Enter From Name");
    //    let templateToNameValidation = commonInputValidation(toName, $("#templateToNameError"), "Enter To Name");
    //    let templateTransitionNameValidation = commonInputValidation(transitionName, $("#transitionNameError"), "Enter Transition Name");
    //    let templateTransitionColourValidation = commonInputValidation(transitionColour, $("#transitionColourError"), "Enter Colour Code");
    //    if (templateFromNameValidation && templateToNameValidation && templateTransitionNameValidation && templateTransitionColourValidation) {
    //        addProcessLine(fromName, toName, transitionName, transitionColour);
    //    }
    //});
    $("#nextButton").on("click", function () {

        const {isValidName, isValidDuration, isValidSelectDuration, isValidUser, userValue } = validateProcessForm();

        if (isValidName && isValidDuration && isValidSelectDuration && isValidUser) {
            toggleWizard(false);
        }

        updateRuleCountsIfChanged(userValue);
    });
    $("#previousButton").on("click", function () {
        toggleWizard(true);
    });
    $("#endTemplate").on("click", function () {
        const container = $("#approvalContainer");

        // Only proceed if at least one box is in the container
        if (container.children("div").length > 0) {
            $("#saveApprovalTemplate").css("visibility", "visible");

            // Prevent duplicate end node
            if ($("#endbutton").length === 0) {
                container.scrollTop(0);

                const lastDiv = container.children("div").last();

                createEndConnectorLine(container, lastDiv);
                container.append(createEndButton());

                container.scrollTop(container[0].scrollHeight);

                // Uncomment to support transition modal if needed
                // if (addTransition) {
                //     $("#templateFromName, #templateToName, #transitionName, #transitionColour").val("");
                //     $("#addTransitionModal").modal("show");
                // }
            }
        }
    });

    GetBusinessServiceList();
    getUsersData();
});

$(document).on("click", '.btnTemplateDelete', function () {
    let templateID = $(this).attr('data-template-id');
    let templateName = $(this).attr('data-template-name');
    $('#deleteData').attr('title', templateName).text(templateName);
    $('#textDeleteId').val(templateID);
});

$(document).on("click", ".btnTemplateEdit", function () {
    const templateData = JSON.parse($(this).attr("data-template"));
    handleTemplateEdit(templateData);
});



$("#confirmDeleteButton").on("click", async function () {
    if (templateButtonDisable) return;

    const form = $("#templateDelete")[0];
    const formData = new FormData(form);

    templateButtonDisable = true;

    try {
        const response = await $.ajax({
            type: "DELETE",
            url: `${RootUrl}${approvalURL.delete}`,
            headers: {
                'RequestVerificationToken': await gettoken()
            },
            data: formData,
            contentType: false,
            processData: false
        });

        $("#deleteTemplateModal").modal("hide");

        if (response?.success) {
            notificationAlert("success", response?.data?.message);
            setTimeout(() => {
                dataTableDelete(dataTable);
            }, 2000);
        } else {
            errorNotification(response);
        }
    } catch (error) {
        console.error("Delete request failed:", error);
        errorNotification("An unexpected error occurred while deleting the template.");
    } finally {
        templateButtonDisable = false;
    }
});
