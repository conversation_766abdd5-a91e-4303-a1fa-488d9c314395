﻿QUnit.module("Utility Functions");

QUnit.test("renderSpan returns correct span HTML", assert => {
    assert.equal(renderSpan("Hello"), '<span title="Hello">Hello</span>', "Should wrap string");
    assert.equal(renderSpan(""), '<span title="NA">NA</span>', "Should default empty to NA");
    assert.equal(renderSpan(null), '<span title="NA">NA</span>', "Should default null to NA");
});

QUnit.module("Workflow List DataTable", hooks => {
    let tableInstance;

    hooks.beforeEach(() => {
        // Clean and reset fixture
        if ($.fn.DataTable.isDataTable('#WorkFlowList')) {
            $('#WorkFlowList').DataTable().destroy();
        }

        $('#qunit-fixture').html('');
        $('#qunit-fixture').append(`
            <table id="WorkFlowList"><tbody></tbody></table>
            <input type="text" id="wfListSearch" />
            <div class="pagination-column"></div>
            <div id="userLoginName">adminuser</div>
        `);
    });

    QUnit.test("DataTable initializes and AJAX parameters are correct", assert => {
        const done = assert.async();
        let alreadyDone = false;

        $('#wfListSearch').val("testSearch");
        selectedValues = [];

        function buildAjaxData(d) {
            d.PageNumber = 1;
            d.pageSize = 10;
            d.searchString = selectedValues.length > 0
                ? selectedValues.join(';')
                : $('#wfListSearch').val();
            selectedValues.length = 0;
            return d;
        }

        tableInstance = $('#WorkFlowList').DataTable({
            serverSide: false,
            ajax: function (data, callback) {
                const processed = buildAjaxData(data);

                assert.ok(true, "AJAX function was called.");
                assert.equal(
                    processed.searchString,
                    "testSearch",
                    `Search string should be 'testSearch': got "${processed.searchString}"`
                );

                assert.equal(
                    processed.pageSize,
                    10,
                    "Page size should be 10"
                );

                callback({
                    data: [],
                    recordsTotal: 0,
                    recordsFiltered: 0
                });

                // ✅ Prevent multiple done() calls
                if (!alreadyDone) {
                    alreadyDone = true;
                    done();
                }
            },
            columns: [
                { data: 'workflowName' },
                { data: 'businessServiceName' }
            ]
        });

        tableInstance.ajax.reload();
    });


    QUnit.test("rendered span fallback to 'NA'", assert => {
        const renderSpan = (data, type) => {
            const value = data || 'NA';
            return type === 'display' ? `<span title="${value}">${value}</span>` : value;
        };

        assert.equal(renderSpan("data", "display"), '<span title="data">data</span>', "Displays normal value");
        assert.equal(renderSpan("", "display"), '<span title="NA">NA</span>', "Fallbacks to NA on empty");
        assert.equal(renderSpan(null, "display"), '<span title="NA">NA</span>', "Fallbacks to NA on null");
    });
});



QUnit.module("Event Handlers");

QUnit.test("SessionStorage set on .viewModeUnique click", assert => {
    $(document).on('click', '.viewModeUnique', function () {
        sessionStorage.setItem('WorkflowId', $(this).data("workflowId"));
    });

    const $btn = $('<i class="viewModeUnique" data-workflow-id="123"></i>');
    $(document.body).append($btn);
    $btn.trigger("click");

    assert.equal(sessionStorage.getItem("WorkflowId"), "123", "WorkflowId stored in session");
    $btn.remove();
});


QUnit.test("Search filters push correct keys", assert => {
    selectedValues = [];
    $('#wfListSearch').val("disk");
    $('#qunit-fixture').append(`
        <input type="checkbox" id="name" checked />
        <input type="checkbox" id="infraname" />
    `);
    const keys = [
        { key: 'name', val: 'workflow' },
        { key: 'infraname', val: 'infraobject' }
    ];
    keys.forEach(x => {
        if ($(`#${x.key}`).is(':checked')) {
            selectedValues.push(`${x.val}=disk`);
        }
    });
    assert.deepEqual(selectedValues, ["workflow=disk"], "Correct filter keys pushed");
});
