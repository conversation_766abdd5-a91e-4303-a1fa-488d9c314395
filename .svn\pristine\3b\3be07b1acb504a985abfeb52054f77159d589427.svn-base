﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ContinuityPatrol.Application.Features.Report.Queries.GetSchedulerWorkflowActionResultsReport
{
    public class GetSchedulerWorkflowActionResultsQuery : IRequest<SchedulerWorkflowActionResultsVm>
    {
        public string WorkflowId { get; set; }
        public string InfraObjectId { get; set; }
    }
}
