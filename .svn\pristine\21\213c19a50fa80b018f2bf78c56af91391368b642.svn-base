﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="">
            <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                <div class="d-flex align-items-center">
                    <h6 class="page_title">
                        <i class="cp-BIA-cost"></i>
                        <span>Business Function FIA</span>
                    </h6>
                </div>
            </div>
        </div>
        <div class="card-body pt-0">
            <table class="table table-hover dataTable" style="width:100%">
                <thead>
                    <tr>
                        <th>Sr.No</th>
                        <th>Business Function Name</th>
                        <th>Selected FIA Template</th>
                        <th>Business Service Name</th>
                        <th>Criticality Level</th>
                        <th>Configured RTO</th>
                        <th>Configured RPO</th>
                        <th>Manage Dependency Rules</th>
                        <th>View Report</th>
                        <th>View RTO</th>
                        <th>Perform FIA</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>BF_CON_TEST</td>
                        <td>NA</td>
                        <td>BS_TEST</td>
                        <td>High</td>
                        <td><i class="cp-time me-1"></i>10 Min(s)</td>
                        <td><i class="cp-time me-1"></i>20 Min(s)</td>
                        <td><a asp-area="Configuration" asp-controller="BiaRules" asp-action="List" class="nav-link"><i class="cp-network"></i></a></td>
                        <td>
                            <span role="button" title="Edit" class="edit_businessService-button">
                                <i class="cp-password-visible text-primary"></i>
                            </span>
                        </td>
                        <td>
                            <span role="button" title="Edit" class="edit_businessService-button">
                                <i class="cp-edit"></i>
                            </span>
                        </td>
                        <td>
                            <span role="button" title="Edit" class="edit_businessService-button" data-bs-target="#PerformQuantitativeModal" data-bs-toggle="modal">
                                <i class="cp-edit"></i>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>MongoDB_BF</td>
                        <td>NA</td>
                        <td>MongoDB</td>
                        <td>High</td>
                        <td><i class="cp-time me-1"></i>10 Min(s)</td>
                        <td><i class="cp-time me-1"></i>20 Min(s)</td>
                        <td><a asp-area="Configuration" asp-controller="BiaRules" asp-action="List" class="nav-link"><i class="cp-network"></i></a></td>
                        <td>
                            <span role="button" title="Edit" class="edit_businessService-button">
                                <i class="cp-password-visible text-primary"></i>
                            </span>
                        </td>
                        <td>
                            <span role="button" title="Edit" class="edit_businessService-button">
                                <i class="cp-edit"></i>
                            </span>
                        </td>
                        <td>
                            <span role="button" title="Edit" class="edit_businessService-button">
                                <i class="cp-edit"></i>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>ALWAYSON_PR</td>
                        <td>NA</td>
                        <td>CSK_PR</td>
                        <td>High</td>
                        <td><i class="cp-time me-1"></i>20 Min(s)</td>
                        <td><i class="cp-time me-1"></i>1 Day(s)</td>
                        <td><a asp-area="Configuration" asp-controller="BiaRules" asp-action="List" class="nav-link"><i class="cp-network"></i></a></td>
                        <td>
                            <span role="button" title="Edit" class="edit_businessService-button">
                                <i class="cp-password-visible text-primary"></i>
                            </span>
                        </td>
                        <td>
                            <span role="button" title="Edit" class="edit_businessService-button">
                                <i class="cp-edit"></i>
                            </span>
                        </td>
                        <td>
                            <span role="button" title="Edit" class="edit_businessService-button" data-bs-target="#PerformQuantitativeModal" data-bs-toggle="modal">
                                <i class="cp-edit"></i>
                            </span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <!-- Perform Quantitative Financial Impact - Modal Start -->
    <div class="modal fade" id="PerformQuantitativeModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <form class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title" title="Perform Quantitative Financial Impact"><i class="cp-BIA-cost"></i><span>Perform Quantitative Financial Impact</span></h6>
                    <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="min-height:25rem ">
                    <div class="d-flex align-items-center justify-content-between gap-3">
                        <div class="form-group w-100">
                            <div class="form-label" title="Impact Severity">Impact Severity</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-Impact"></i></span>
                                <select class="form-select-modal" data-placeholder="Select Impact Severity">
                                    <option></option>
                                    <option value="BE_TEST">BE_TEST</option>
                                    <option value="MongoDb">MongoDb</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group w-100">
                            <div class="form-label" title="Select FIA Templates">Select FIA Templates</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-Impact"></i></span>
                                <select class="form-select-modal mb-0" data-placeholder="Select FIA Template">
                                    <option></option>
                                   
                                </select>
                            </div>
                        </div>
                    </div>
                    <div>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Impact</th>
                                    <th>0 To 6 Hours</th>
                                    <th>6 To 12 Hours</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="3" class="p-0">
                                        <div class="accordion" id="accordionExample">
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                                        ImpactTypeName: minimal
                                                    </button>
                                                </h2>
                                                <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionExample">
                                                    <div class="accordion-body">
                                                        <table class="table " style="vertical-align:bottom">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        Major Impact
                                                                    </td>
                                                                    <td>
                                                                        <div class="input-group">
                                                                            <input type="text" class="form-control" />
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div class="input-group">
                                                                            <input type="text" class="form-control" />
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>
                                                                        Total Impact
                                                                    </td>
                                                                    <td>
                                                                        <div class="input-group">
                                                                            <input type="text" class="form-control" />
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div class="input-group">
                                                                            <input type="text" class="form-control" />
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>
                                                                        Minor Impact
                                                                    </td>
                                                                    <td>
                                                                        <div class="input-group">
                                                                            <input type="text" class="form-control" />
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div class="input-group">
                                                                            <input type="text" class="form-control" />
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th scope="row">Total</th>
                                    <td>
                                        <div class="input-group">
                                            <input type="text" class="form-control" placeholder="0.00" disabled />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <input type="text" class="form-control" placeholder="0.00" disabled />
                                        </div>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" title="Cancel" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" title="Save">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- Perform Quantitative Financial Impact - Modal End -->
</div>
