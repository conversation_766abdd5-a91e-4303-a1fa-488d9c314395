﻿using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Delete;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Update;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.UpdateState;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetList;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetNames;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.InfraObjectSchedulerLogPagination;
using ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class InfraObjectSchedulersController : CommonBaseController
    {
     [HttpGet]
     [Authorize(Policy = Permissions.Configuration.View)]
      public async Task<ActionResult<List<InfraObjectSchedulerListVm>>> GetInfraObjectSchedulers()
      {
         Logger.LogDebug("Get All InfraObjectSchedulers");

        return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllInfraObjectSchedulersCacheKey + LoggedInUserService.CompanyId, () => Mediator.Send(new GetInfraObjectSchedulerListQuery()), CacheExpiry));
      }

    [HttpGet("{id}", Name = "GetInfraObjectScheduler")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<InfraObjectSchedulerDetailVm>> GetInfraObjectSchedulerById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "InfraObjectScheduler Id");

        Logger.LogDebug($"Get InfraObjectScheduler Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetInfraObjectSchedulerDetailQuery { Id = id }));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateInfraObjectSchedulerResponse>> CreateInfraObjectScheduler([FromBody] CreateInfraObjectSchedulerCommand createInfraObjectSchedulerCommand)
    {
        Logger.LogDebug($"Create InfraObjectScheduler '{createInfraObjectSchedulerCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateInfraObjectScheduler), await Mediator.Send(createInfraObjectSchedulerCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteInfraObjectSchedulerResponse>> DeleteInfraObjectScheduler(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "InfraObjectScheduler Id");

        Logger.LogDebug($"Delete InfraObjectScheduler Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteInfraObjectSchedulerCommand { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<InfraObjectSchedulerListVm>>> GetPaginatedInfraObjectSchedulers([FromQuery] GetInfraObjectSchedulerPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in InfraObjectScheduler Paginated List");

        return Ok(await Mediator.Send(query));
    }
    [Route("logs"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<InfraObjectSchedulerLogsListVm>>> GetPaginatedInfraObjectSchedulerLogs([FromQuery] GetInfraObjectSchedulerLogsPaginationQuery query)
    {
        Logger.LogDebug("Get Searching Details in InfraObjectSchedulerLogs Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpGet, Route("names")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<InfraObjectSchedulerNameVm>>> GetInfraObjectSchedulerNames()
    {
        Logger.LogDebug("Get All InfraObjectScheduler Names");

        return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllInfraObjectSchedulerNamesCacheKey, () => Mediator.Send(new GetInfraObjectSchedulerNameQuery()), CacheExpiry));

    }

    [HttpGet, Route("name-exist")]
    public async Task<ActionResult> IsInfraObjectSchedulerNameExist(string infraObjectName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(infraObjectName, "InfraObjectScheduler Name");

        Logger.LogDebug($"Check Name Exists Detail by InfraObjectScheduler Name '{infraObjectName}' and Id '{id}' ");

        return Ok(await Mediator.Send(new GetInfraObjectSchedulerNameUniqueQuery { InfraObjectName = infraObjectName, Id = id }));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateInfraObjectSchedulerResponse>> UpdateInfraObjectScheduler([FromBody] UpdateInfraObjectSchedulerCommand updateInfraObjectSchedulerCommand)
    {
        Logger.LogDebug($"Update InfraObject '{updateInfraObjectSchedulerCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateInfraObjectSchedulerCommand));
    }


    [HttpPut("updateStatus")]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateInfraObjectSchedulerStatusResponse>> UpdateInfraObjectSchedulerStatus([FromBody] UpdateInfraObjectSchedulerStatusCommand updateInfraObjectSchedulerStatusCommand)
    {
        Logger.LogDebug($"Update InfraObject Scheduler Status '{updateInfraObjectSchedulerStatusCommand.Status}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateInfraObjectSchedulerStatusCommand));
    }

    [HttpPut("updateState")]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateInfraObjectSchedulerStateResponse>> UpdateInfraObjectSchedulerState([FromBody] UpdateInfraObjectSchedulerStateCommand updateInfraObjectSchedulerStateCommand)
    {
        Logger.LogDebug($"Update InfraObject Scheduler state '{updateInfraObjectSchedulerStateCommand.UpdateInfraObjectSchedulerStates.FirstOrDefault()?.State}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateInfraObjectSchedulerStateCommand));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys =  { ApplicationConstants.Cache.AllInfraObjectSchedulersCacheKey + LoggedInUserService.CompanyId};

        ClearCache(cacheKeys);
    }
}