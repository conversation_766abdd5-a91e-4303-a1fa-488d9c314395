﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.RsyncJob.Events.Update;

public class RsyncJobUpdatedEventHandler : INotificationHandler<RsyncJobUpdatedEvent>
{
    private readonly ILogger<RsyncJobUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public RsyncJobUpdatedEventHandler(ILoggedInUserService userService, ILogger<RsyncJobUpdatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(RsyncJobUpdatedEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} RsyncJob",
            Entity = "RsyncJob",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Rsync Job '{notification.Name}' updated successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };
        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Rsync Job '{notification.Name}' updated successfully.");
    }
}