﻿using ContinuityPatrol.Application.Features.Form.Queries.GetType;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Form.Queries;

public class GetFormTypeQueryHandlerTests : IClassFixture<FormFixture>
{
    private readonly FormFixture _formFixture;

    private Mock<IFormRepository> _mockFormRepository;

    private readonly GetFormTypeQueryHandler _handler;

    public GetFormTypeQueryHandlerTests(FormFixture formFixture)
    {
        _formFixture = formFixture;

        _mockFormRepository = FormRepositoryMocks.GetFormRepository(_formFixture.Forms);

        _handler = new GetFormTypeQueryHandler(_formFixture.Mapper, _mockFormRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Valid_FormsType()
    {
        var result = await _handler.Handle(new GetFormTypeQuery { Type = _formFixture.Forms[0].Type }, CancellationToken.None);

        result.ShouldBeOfType<List<FormTypeVm>>();

        result[0].Id.ShouldBe(_formFixture.Forms[0].ReferenceId);
        result[0].Name.ShouldBe(_formFixture.Forms[0].Name);
        result[0].Properties.ShouldBe(_formFixture.Forms[0].Properties);
        result[0].Type.ShouldBe(_formFixture.Forms[0].Type);
        result[0].Version.ShouldBe(_formFixture.Forms[0].Version);
    }

    [Fact]
    public async Task Handle_ReturnEmptyType_When_NoRecords()
    {
        _mockFormRepository = FormRepositoryMocks.GetFormEmptyRepository();

        var handler = new GetFormTypeQueryHandler(_formFixture.Mapper, _mockFormRepository.Object);

        var result = await handler.Handle(new GetFormTypeQuery { Type = _formFixture.Forms[0].Type }, CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetFormTypeQuery { Type = _formFixture.Forms[0].Type }, CancellationToken.None);

        _mockFormRepository.Verify(x => x.GetFormType(It.IsAny<string>()), Times.Once);
    }
}