//using ContinuityPatrol.Application.Features.BiaImpact.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Create;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Delete;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Update;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetBiaRulesByEntityId;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetList;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BiaRulesModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.FiaBia;

public class BiaRulesService : BaseService,IBiaRulesService
{
    public BiaRulesService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<BiaRulesListVm>> GetBiaImpactList()
    {
        Logger.LogDebug("Get All BiaImpacts");

        return await Mediator.Send(new GetBiaRulesListQuery());
    }

    public async Task<BiaRulesDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BiaImpact Id");

        Logger.LogDebug($"Get BiaImpact Detail by Id '{id}'");

        return await Mediator.Send(new GetBiaRulesDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateBiaRulesCommand createBiaImpactCommand)
    {
        Logger.LogDebug($"Create BiaImpact '{createBiaImpactCommand}'");

        return await Mediator.Send(createBiaImpactCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateBiaRulesCommand updateBiaImpactCommand)
    {
        Logger.LogDebug($"Update BiaImpact '{updateBiaImpactCommand}'");

        return await Mediator.Send(updateBiaImpactCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BiaImpact Id");

        Logger.LogDebug($"Delete BiaImpact Details by Id '{id}'");

        return await Mediator.Send(new DeleteBiaRulesCommand { Id = id });
    }
    #region NameExist
    // public async Task<bool> IsBiaImpactNameExist(string name, string? id)
    // {
    //     Guard.Against.NullOrWhiteSpace(name, "BiaImpact Name");
    //
    //     Logger.LogDebug($"Check Name Exists Detail by BiaImpact Name '{name}' and Id '{id}'");
    //
    //     return await Mediator.Send(new GetBiaImpactNameUniqueQuery { Name = name, Id = id });
    // }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<BiaRulesListVm>> GetPaginatedBiaImpacts(GetBiaRulesPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in BiaImpact Paginated List");

        return await Mediator.Send(query);
    }
    #endregion

    public async Task<BiaRulesListVm> GetBiaRulesByEntityIdAndType(string entityId, string type)
    {
        Logger.LogDebug("Get BiaRules Details by EntityId and Type");

        return await Mediator.Send(new GetBiaRulesByEntityIdQuery { EntityId = entityId, Type = type });
    }
}
