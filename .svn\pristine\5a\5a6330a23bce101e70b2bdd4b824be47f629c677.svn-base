namespace ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Delete;

public class DeleteBulkImportActionResultCommandHandler : IRequestHandler<DeleteBulkImportActionResultCommand,
    DeleteBulkImportActionResultResponse>
{
    private readonly IBulkImportActionResultRepository _bulkImportActionResultRepository;
    private readonly IPublisher _publisher;

    public DeleteBulkImportActionResultCommandHandler(
        IBulkImportActionResultRepository bulkImportActionResultRepository, IPublisher publisher)
    {
        _bulkImportActionResultRepository = bulkImportActionResultRepository;

        _publisher = publisher;
    }

    public async Task<DeleteBulkImportActionResultResponse> Handle(DeleteBulkImportActionResultCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _bulkImportActionResultRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.BulkImportActionResult),
            new NotFoundException(nameof(Domain.Entities.BulkImportActionResult), request.Id));

        eventToDelete.IsActive = false;

        await _bulkImportActionResultRepository.UpdateAsync(eventToDelete);

        var response = new DeleteBulkImportActionResultResponse
        {
            // Message = Message.Delete(nameof(Domain.Entities.BulkImportActionResult), eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        // await _publisher.Publish(new BulkImportActionResultDeletedEvent { Name = eventToDelete.Name }, cancellationToken);

        return response;
    }
}