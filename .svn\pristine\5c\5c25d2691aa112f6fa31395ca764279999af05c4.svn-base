using ContinuityPatrol.Domain.ViewModels.CyberJobManagementModel;

namespace ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetList;

public class
    GetCyberJobManagementListQueryHandler : IRequestHandler<GetCyberJobManagementListQuery,
        List<CyberJobManagementListVm>>
{
    private readonly ICyberJobManagementRepository _cyberJobManagementRepository;
    private readonly IMapper _mapper;

    public GetCyberJobManagementListQueryHandler(IMapper mapper,
        ICyberJobManagementRepository cyberJobManagementRepository)
    {
        _mapper = mapper;
        _cyberJobManagementRepository = cyberJobManagementRepository;
    }

    public async Task<List<CyberJobManagementListVm>> Handle(GetCyberJobManagementListQuery request,
        CancellationToken cancellationToken)
    {
        var cyberJobManagements = await _cyberJobManagementRepository.ListAllAsync();

        if (cyberJobManagements.Count <= 0) return new List<CyberJobManagementListVm>();

        return _mapper.Map<List<CyberJobManagementListVm>>(cyberJobManagements);
    }
}