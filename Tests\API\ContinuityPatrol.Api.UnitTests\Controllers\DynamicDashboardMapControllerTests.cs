using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetByUserId;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetDefaultDashboardByRoleId;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetList;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardMapModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DynamicDashboardMapControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DynamicDashboardMapsController _controller;
    private readonly DynamicDashboardMapFixture _dynamicDashboardMapFixture;

    public DynamicDashboardMapControllerTests()
    {
        _dynamicDashboardMapFixture = new DynamicDashboardMapFixture();

        var testBuilder = new ControllerTestBuilder<DynamicDashboardMapsController>();
        _controller = testBuilder.CreateController(
            _ => new DynamicDashboardMapsController(),
            out _mediatorMock);
    }

    #region GetDynamicDashboardMaps Tests

    [Fact]
    public async Task GetDynamicDashboardMaps_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedList = _dynamicDashboardMapFixture.DynamicDashboardMapListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardMapListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDynamicDashboardMaps();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicDashboardMapListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item => Assert.NotNull(item.Id));
    }

    [Fact]
    public async Task GetDynamicDashboardMaps_WithEmptyList_ReturnsEmptyOkResult()
    {
        // Arrange
        var emptyList = new List<DynamicDashboardMapListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardMapListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDynamicDashboardMaps();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicDashboardMapListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDynamicDashboardMaps_WithException_ThrowsException()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardMapListQuery>(), default))
            .ThrowsAsync(new Exception("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _controller.GetDynamicDashboardMaps());
    }

    #endregion

    #region GetDynamicDashboardMapById Tests

    [Fact]
    public async Task GetDynamicDashboardMapById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _dynamicDashboardMapFixture.DynamicDashboardMapDetailVm;
        expectedDetail.ReferenceId = id;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardMapDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDynamicDashboardMapById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DynamicDashboardMapDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.ReferenceId);
        Assert.NotNull(returnedDetail.DashBoardSubName);
    }

    [Fact]
    public async Task GetDynamicDashboardMapById_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDynamicDashboardMapById(invalidId));
    }

    [Fact]
    public async Task GetDynamicDashboardMapById_WithNullId_ThrowsArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetDynamicDashboardMapById(null));
    }

    #endregion

    #region GetDefaultDashboardByRoleId Tests

    [Fact]
    public async Task GetDefaultDashboardByRoleId_WithValidRoleId_ReturnsOkResult()
    {
        // Arrange
        var roleId = Guid.NewGuid().ToString();
        var expectedDashboard = _dynamicDashboardMapFixture.DynamicDashboardMapListVm.First();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDefaultDashboardByRoleIdQuery>(q => q.RoleId == roleId), default))
            .ReturnsAsync(expectedDashboard);

        // Act
        var result = await _controller.GetDefaultDashboardByRoleId(roleId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDashboard = Assert.IsType<DynamicDashboardMapListVm>(okResult.Value);
        Assert.NotNull(returnedDashboard);
        Assert.NotNull(returnedDashboard.Id);
    }

    [Fact]
    public async Task GetDefaultDashboardByRoleId_WithNullRoleId_ReturnsOkResult()
    {
        // Arrange
        string roleId = null;
        var expectedDashboard = _dynamicDashboardMapFixture.DynamicDashboardMapListVm.First();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDefaultDashboardByRoleIdQuery>(), default))
            .ReturnsAsync(expectedDashboard);

        // Act
        var result = await _controller.GetDefaultDashboardByRoleId(roleId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDashboard = Assert.IsType<DynamicDashboardMapListVm>(okResult.Value);
        Assert.NotNull(returnedDashboard);
    }

    [Fact]
    public async Task GetDefaultDashboardByRoleId_WithNonExistentRoleId_ReturnsNull()
    {
        // Arrange
        var roleId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDefaultDashboardByRoleIdQuery>(q => q.RoleId == roleId), default))
            .ReturnsAsync((DynamicDashboardMapListVm)null);

        // Act
        var result = await _controller.GetDefaultDashboardByRoleId(roleId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Null(okResult.Value);
    }

    #endregion

    #region GetDefaultDashboardByUserId Tests

    [Fact]
    public async Task GetDefaultDashboardByUserId_WithValidUserId_ReturnsOkResult()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var expectedDashboard = _dynamicDashboardMapFixture.DynamicDashboardMapListVm.First();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardMapByUserIdQuery>(q => q.UserId == userId), default))
            .ReturnsAsync(expectedDashboard);

        // Act
        var result = await _controller.GetDefaultDashboardByUserId(userId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDashboard = Assert.IsType<DynamicDashboardMapListVm>(okResult.Value);
        Assert.NotNull(returnedDashboard);
        Assert.NotNull(returnedDashboard.Id);
    }

    [Fact]
    public async Task GetDefaultDashboardByUserId_WithNullUserId_ReturnsOkResult()
    {
        // Arrange
        string userId = null;
        var expectedDashboard = _dynamicDashboardMapFixture.DynamicDashboardMapListVm.First();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardMapByUserIdQuery>(), default))
            .ReturnsAsync(expectedDashboard);

        // Act
        var result = await _controller.GetDefaultDashboardByUserId(userId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDashboard = Assert.IsType<DynamicDashboardMapListVm>(okResult.Value);
        Assert.NotNull(returnedDashboard);
    }

    [Fact]
    public async Task GetDefaultDashboardByUserId_WithNonExistentUserId_ReturnsNull()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardMapByUserIdQuery>(q => q.UserId == userId), default))
            .ReturnsAsync((DynamicDashboardMapListVm)null);

        // Act
        var result = await _controller.GetDefaultDashboardByUserId(userId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Null(okResult.Value);
    }

    #endregion

    #region UpdateDynamicDashboardMap Tests

    [Fact]
    public async Task UpdateDynamicDashboardMap_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _dynamicDashboardMapFixture.UpdateDynamicDashboardMapCommand;
        var expectedResponse = _dynamicDashboardMapFixture.UpdateDynamicDashboardMapResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDynamicDashboardMap(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDynamicDashboardMapResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDynamicDashboardMap_WithNullCommand_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.UpdateDynamicDashboardMap(null));
    }

    [Fact]
    public async Task UpdateDynamicDashboardMap_WithInvalidCommand_ReturnsFailureResponse()
    {
        // Arrange
        var command = _dynamicDashboardMapFixture.UpdateDynamicDashboardMapCommand;
        var failureResponse = new UpdateDynamicDashboardMapResponse
        {
            Success = false,
            Message = "Update validation failed"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.UpdateDynamicDashboardMap(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDynamicDashboardMapResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Equal("Update validation failed", returnedResponse.Message);
    }

    #endregion

    #region CreateDynamicDashboardMap Tests

    [Fact]
    public async Task CreateDynamicDashboardMap_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _dynamicDashboardMapFixture.CreateDynamicDashboardMapCommand;
        var expectedResponse = _dynamicDashboardMapFixture.CreateDynamicDashboardMapResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDynamicDashboardMap(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDynamicDashboardMapResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task CreateDynamicDashboardMap_WithNullCommand_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.CreateDynamicDashboardMap(null));
    }

    [Fact]
    public async Task CreateDynamicDashboardMap_WithComplexMapping_ReturnsCreatedResult()
    {
        // Arrange
        var command = _dynamicDashboardMapFixture.CreateDynamicDashboardMapCommand;
        command.DashBoardSubName = "Enterprise Multi-Role Dashboard";
        command.Type = "Advanced";
        command.IsDefault = true;
        command.IsView = true;
        var expectedResponse = _dynamicDashboardMapFixture.CreateDynamicDashboardMapResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDynamicDashboardMap(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDynamicDashboardMapResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Multi-Role Dashboard", command.DashBoardSubName);
        Assert.Equal("Advanced", command.Type);
        Assert.True(command.IsDefault);
    }

    #endregion

    #region DeleteDynamicDashboardMap Tests

    [Fact]
    public async Task DeleteDynamicDashboardMap_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _dynamicDashboardMapFixture.DeleteDynamicDashboardMapResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDynamicDashboardMapCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDynamicDashboardMap(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDynamicDashboardMapResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("deleted successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task DeleteDynamicDashboardMap_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDynamicDashboardMap(invalidId));
    }

    [Fact]
    public async Task DeleteDynamicDashboardMap_WithNullId_ThrowsArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteDynamicDashboardMap(null));
    }

    #endregion

    #region GetPaginatedDynamicDashboardMaps Tests

    [Fact]
    public async Task GetPaginatedDynamicDashboardMaps_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _dynamicDashboardMapFixture.GetDynamicDashboardMapPaginatedListQuery;
        var expectedResult = _dynamicDashboardMapFixture.DynamicDashboardMapPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicDashboardMaps(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DynamicDashboardMapListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.Equal(expectedResult.CurrentPage, returnedResult.CurrentPage);
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboardMaps_WithNullQuery_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetPaginatedDynamicDashboardMaps(null));
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboardMaps_WithUserFiltering_ReturnsFilteredResults()
    {
        // Arrange
        var query = _dynamicDashboardMapFixture.GetDynamicDashboardMapPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 10;
        query.SearchString = "Enterprise User";
        var expectedResult = _dynamicDashboardMapFixture.DynamicDashboardMapPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicDashboardMaps(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DynamicDashboardMapListVm>>(okResult.Value);
        Assert.Equal("Enterprise User", query.SearchString);
        Assert.Equal(1, returnedResult.CurrentPage);
    }

    #endregion

    #region IsDynamicDashboardMapNameExist Tests

    [Fact]
    public async Task IsDynamicDashboardMapNameExist_WithExistingName_ReturnsTrue()
    {
        // Arrange
        var mapName = "Enterprise Dashboard Map";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardMapNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDynamicDashboardMapNameExist(mapName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedValue);
    }

    [Fact]
    public async Task IsDynamicDashboardMapNameExist_WithNonExistingName_ReturnsFalse()
    {
        // Arrange
        var mapName = "Non-Existing Map";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardMapNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDynamicDashboardMapNameExist(mapName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    [Fact]
    public async Task IsDynamicDashboardMapNameExist_WithNullName_ThrowsArgumentException()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.IsDynamicDashboardMapNameExist(null, id));
    }

    #endregion

    #region Additional Test Cases Following CompanyControllerTests and BusinessServiceControllerTests Patterns

    [Fact]
    public async Task GetDynamicDashboardMaps_ReturnsEmptyList_WhenNoMapsExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardMapListQuery>(), default))
            .ReturnsAsync(new List<DynamicDashboardMapListVm>());

        // Act
        var result = await _controller.GetDynamicDashboardMaps();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicDashboardMapListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDynamicDashboardMapById_Throws_WhenMapNotFound()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardMapDetailQuery>(q => q.Id == id), default))
            .ThrowsAsync(new NotFoundException("DynamicDashboardMap", id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetDynamicDashboardMapById(id));
    }

    [Fact]
    public async Task CreateDynamicDashboardMap_Throws_WhenMappingExists()
    {
        // Arrange
        var command = _dynamicDashboardMapFixture.CreateDynamicDashboardMapCommand;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDynamicDashboardMapCommand>(), default))
            .ThrowsAsync(new InvalidOperationException("Dashboard mapping already exists for this user"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.CreateDynamicDashboardMap(command));
    }

    [Fact]
    public async Task UpdateDynamicDashboardMap_Throws_WhenMapNotFound()
    {
        // Arrange
        var command = _dynamicDashboardMapFixture.UpdateDynamicDashboardMapCommand;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDynamicDashboardMapCommand>(), default))
            .ThrowsAsync(new NotFoundException("DynamicDashboardMap", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.UpdateDynamicDashboardMap(command));
    }

    [Fact]
    public async Task DeleteDynamicDashboardMap_Throws_WhenMapNotFound()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDynamicDashboardMapCommand>(c => c.Id == id), default))
            .ThrowsAsync(new NotFoundException("DynamicDashboardMap", id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.DeleteDynamicDashboardMap(id));
    }

    [Fact]
    public async Task IsDynamicDashboardMapNameExist_IncludesIdInQuery_WhenProvided()
    {
        // Arrange
        var mapName = "Test Dashboard Map";
        var mapId = Guid.NewGuid().ToString();
        string? capturedId = null;
        string? capturedName = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardMapNameUniqueQuery>(), default))
            .Callback<IRequest<bool>, CancellationToken>((request, _) =>
            {
                if (request is GetDynamicDashboardMapNameUniqueQuery query)
                {
                    capturedId = query.Id;
                    capturedName = query.Name;
                }
            })
            .ReturnsAsync(false);

        // Act
        await _controller.IsDynamicDashboardMapNameExist(mapName, mapId);

        // Assert
        Assert.Equal(mapId, capturedId);
        Assert.Equal(mapName, capturedName);
    }

    [Fact]
    public async Task IsDynamicDashboardMapNameExist_ExcludesIdFromQuery_WhenNotProvided()
    {
        // Arrange
        var mapName = "Test Dashboard Map";
        string? capturedId = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardMapNameUniqueQuery>(), default))
            .Callback<IRequest<bool>, CancellationToken>((request, _) =>
            {
                if (request is GetDynamicDashboardMapNameUniqueQuery query)
                {
                    capturedId = query.Id;
                }
            })
            .ReturnsAsync(false);

        // Act
        await _controller.IsDynamicDashboardMapNameExist(mapName, null);

        // Assert
        Assert.Null(capturedId);
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboardMaps_HandlesSearchString_Correctly()
    {
        // Arrange
        var query = _dynamicDashboardMapFixture.GetDynamicDashboardMapPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 10;
        query.SearchString = "Enterprise";

        var expectedData = _dynamicDashboardMapFixture.DynamicDashboardMapListVm.Take(1).ToList();
        var expectedResult = PaginatedResult<DynamicDashboardMapListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardMapPaginatedListQuery>(q => q.SearchString == "Enterprise"), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicDashboardMaps(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<DynamicDashboardMapListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);
        Assert.Equal(1, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task GetDefaultDashboardByRoleId_HandlesMultipleRoles_Correctly()
    {
        // Arrange
        var roleId1 = Guid.NewGuid().ToString();
        var roleId2 = Guid.NewGuid().ToString();
        var dashboardId1 = Guid.NewGuid().ToString();
        var dashboardId2 = Guid.NewGuid().ToString();

        // Ensure IDs are different
        while (dashboardId1 == dashboardId2)
        {
            dashboardId2 = Guid.NewGuid().ToString();
        }

        // Create unique dashboard objects for each role
        var dashboard1 = new DynamicDashboardMapListVm
        {
            Id = dashboardId1,
            DashBoardSubId = Guid.NewGuid().ToString(),
            DashBoardSubName = "Role1 Dashboard",
            UserId = Guid.NewGuid().ToString(),
            UserName = "Role1User",
            RoleId = roleId1,
            RoleName = "Administrator",
            Type = "Role",
            IsDefault = true,
            IsView = true,
            Url = "/dashboard/admin"
        };

        var dashboard2 = new DynamicDashboardMapListVm
        {
            Id = dashboardId2,
            DashBoardSubId = Guid.NewGuid().ToString(),
            DashBoardSubName = "Role2 Dashboard",
            UserId = Guid.NewGuid().ToString(),
            UserName = "Role2User",
            RoleId = roleId2,
            RoleName = "Manager",
            Type = "Role",
            IsDefault = true,
            IsView = true,
            Url = "/dashboard/manager"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDefaultDashboardByRoleIdQuery>(q => q.RoleId == roleId1), default))
            .ReturnsAsync(dashboard1);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDefaultDashboardByRoleIdQuery>(q => q.RoleId == roleId2), default))
            .ReturnsAsync(dashboard2);

        // Act
        var result1 = await _controller.GetDefaultDashboardByRoleId(roleId1);
        var result2 = await _controller.GetDefaultDashboardByRoleId(roleId2);

        // Assert
        var okResult1 = Assert.IsType<OkObjectResult>(result1.Result);
        var okResult2 = Assert.IsType<OkObjectResult>(result2.Result);
        var returnedDashboard1 = Assert.IsType<DynamicDashboardMapListVm>(okResult1.Value);
        var returnedDashboard2 = Assert.IsType<DynamicDashboardMapListVm>(okResult2.Value);

        Assert.NotEqual(returnedDashboard1.Id, returnedDashboard2.Id);
        Assert.Equal(roleId1, returnedDashboard1.RoleId);
        Assert.Equal(roleId2, returnedDashboard2.RoleId);
    }

    [Fact]
    public async Task GetDefaultDashboardByUserId_HandlesMultipleUsers_Correctly()
    {
        // Arrange
        var userId1 = Guid.NewGuid().ToString();
        var userId2 = Guid.NewGuid().ToString();
        var dashboardId1 = Guid.NewGuid().ToString();
        var dashboardId2 = Guid.NewGuid().ToString();

        // Ensure IDs are different
        while (dashboardId1 == dashboardId2)
        {
            dashboardId2 = Guid.NewGuid().ToString();
        }

        // Create unique dashboard objects for each user
        var dashboard1 = new DynamicDashboardMapListVm
        {
            Id = dashboardId1,
            DashBoardSubId = Guid.NewGuid().ToString(),
            DashBoardSubName = "User1 Dashboard",
            UserId = userId1,
            UserName = "User1",
            RoleId = Guid.NewGuid().ToString(),
            RoleName = "Role1",
            Type = "User",
            IsDefault = true,
            IsView = true,
            Url = "/dashboard/user1"
        };

        var dashboard2 = new DynamicDashboardMapListVm
        {
            Id = dashboardId2,
            DashBoardSubId = Guid.NewGuid().ToString(),
            DashBoardSubName = "User2 Dashboard",
            UserId = userId2,
            UserName = "User2",
            RoleId = Guid.NewGuid().ToString(),
            RoleName = "Role2",
            Type = "User",
            IsDefault = true,
            IsView = true,
            Url = "/dashboard/user2"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardMapByUserIdQuery>(q => q.UserId == userId1), default))
            .ReturnsAsync(dashboard1);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardMapByUserIdQuery>(q => q.UserId == userId2), default))
            .ReturnsAsync(dashboard2);

        // Act
        var result1 = await _controller.GetDefaultDashboardByUserId(userId1);
        var result2 = await _controller.GetDefaultDashboardByUserId(userId2);

        // Assert
        var okResult1 = Assert.IsType<OkObjectResult>(result1.Result);
        var okResult2 = Assert.IsType<OkObjectResult>(result2.Result);
        var returnedDashboard1 = Assert.IsType<DynamicDashboardMapListVm>(okResult1.Value);
        var returnedDashboard2 = Assert.IsType<DynamicDashboardMapListVm>(okResult2.Value);

        Assert.NotEqual(returnedDashboard1.Id, returnedDashboard2.Id);
        Assert.Equal(userId1, returnedDashboard1.UserId);
        Assert.Equal(userId2, returnedDashboard2.UserId);
    }

    [Fact]
    public async Task CreateDynamicDashboardMap_Returns201Created_WithSuccessMessage()
    {
        // Arrange
        var command = _dynamicDashboardMapFixture.CreateDynamicDashboardMapCommand;
        var expectedMessage = $"DynamicDashboardMap for user '{command.UserName}' has been created successfully!";
        var expectedResponse = new CreateDynamicDashboardMapResponse
        {
            Id = Guid.NewGuid().ToString(),
            Success = true,
            Message = expectedMessage
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDynamicDashboardMap(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateDynamicDashboardMapResponse>(createdResult.Value);
        Assert.True(response.Success);
        Assert.Contains(command.UserName, response.Message);
    }

    [Fact]
    public async Task UpdateDynamicDashboardMap_ReturnsOkResult_WithSuccessMessage()
    {
        // Arrange
        var command = _dynamicDashboardMapFixture.UpdateDynamicDashboardMapCommand;
        var expectedMessage = $"DynamicDashboardMap for user '{command.UserName}' has been updated successfully!";
        var expectedResponse = new UpdateDynamicDashboardMapResponse
        {
            Id = command.Id,
            Success = true,
            Message = expectedMessage
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDynamicDashboardMap(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateDynamicDashboardMapResponse>(okResult.Value);
        Assert.True(response.Success);
        Assert.Contains(command.UserName, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task GetDynamicDashboardMapById_WithValidId_ReturnsCorrectMapDetails()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _dynamicDashboardMapFixture.DynamicDashboardMapDetailVm;
        expectedDetail.ReferenceId = id;
        expectedDetail.UserName = "Test User";
        expectedDetail.RoleName = "Administrator";
        expectedDetail.Type = "User";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardMapDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDynamicDashboardMapById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DynamicDashboardMapDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.ReferenceId);
        Assert.Equal("Test User", returnedDetail.UserName);
        Assert.Equal("Administrator", returnedDetail.RoleName);
        Assert.Equal("User", returnedDetail.Type);
    }

    [Fact]
    public async Task GetDefaultDashboardByRoleId_WithNonExistentRole_ReturnsNull()
    {
        // Arrange
        var roleId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDefaultDashboardByRoleIdQuery>(q => q.RoleId == roleId), default))
            .ReturnsAsync((DynamicDashboardMapListVm)null);

        // Act
        var result = await _controller.GetDefaultDashboardByRoleId(roleId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Null(okResult.Value);
    }

    [Fact]
    public async Task GetDefaultDashboardByUserId_WithNonExistentUser_ReturnsNull()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardMapByUserIdQuery>(q => q.UserId == userId), default))
            .ReturnsAsync((DynamicDashboardMapListVm)null);

        // Act
        var result = await _controller.GetDefaultDashboardByUserId(userId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Null(okResult.Value);
    }

    [Fact]
    public async Task IsDynamicDashboardMapNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var mapName = "Existing Map";
        var mapId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardMapNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDynamicDashboardMapNameExist(mapName, mapId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsDynamicDashboardMapNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var mapName = "New Map";
        var mapId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardMapNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDynamicDashboardMapNameExist(mapName, mapId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task DeleteDynamicDashboardMap_ReturnsOkResult_WithSuccessMessage()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = new DeleteDynamicDashboardMapResponse
        {
            Success = true,
            Message = "DynamicDashboardMap has been deleted successfully!"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDynamicDashboardMapCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDynamicDashboardMap(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteDynamicDashboardMapResponse>(okResult.Value);
        Assert.True(response.Success);
        Assert.Contains("deleted successfully", response.Message);
    }

    #endregion
}
