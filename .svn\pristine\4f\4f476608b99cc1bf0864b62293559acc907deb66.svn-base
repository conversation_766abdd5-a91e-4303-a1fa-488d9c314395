﻿using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetDataLagStatusReport;
using ContinuityPatrol.Application.Features.Report.Commands.Create;
using ContinuityPatrol.Application.Features.Report.Commands.Update;
using ContinuityPatrol.Application.Features.Report.Queries.AirGapReport;
using ContinuityPatrol.Application.Features.Report.Queries.BusinessServiceSummaryReport;
using ContinuityPatrol.Application.Features.Report.Queries.CyberSnapsReport;
using ContinuityPatrol.Application.Features.Report.Queries.DriftReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetCGExecutionReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetCyberSnapList;
using ContinuityPatrol.Application.Features.Report.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Report.Queries.GetDrDrillReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyExecutionReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetInfraObjectConfigurationReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReportByBusinessService;
using ContinuityPatrol.Application.Features.Report.Queries.GetNames;
using ContinuityPatrol.Application.Features.Report.Queries.GetRPOSLAReport.Models;
using ContinuityPatrol.Application.Features.Report.Queries.GetRTOReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetSchedulerWorkflowActionResultsReport;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSchedulerLogs.Models;
using ContinuityPatrol.Application.Features.Report.Queries.UserActivityReport;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Domain.ViewModels.ReportModel;
using ContinuityPatrol.Domain.ViewModels.RpoSlaDeviationReportModel;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Mappings;

public class ReportProfile : Profile
{
    public ReportProfile()
    {
        CreateMap<Report, CreateReportCommand>().ReverseMap();
        CreateMap<UpdateReportCommand, Report>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<Report, ReportDetailVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<Report, ReportListVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<Report, GetReportNameVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        #region DataLagStatusReport

        CreateMap<DashboardViewLog, DataLagStatusReportVm>().ReverseMap();

        CreateMap<DashboardView, InfraObjectDataLagStatusReport>().ReverseMap();

        #endregion


        #region InfraObjectConfigurationReport

        //InfraObjectConfigurationReport
        CreateMap<InfraObject, InfraObjectConfigurationReportVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.PRServerName, opt => opt.MapFrom(src => GetJsonValue(src.ServerProperties,"PR.name")))
            .ForMember(dest => dest.DRServerName, opt => opt.MapFrom(src => GetJsonValue(src.ServerProperties, "DR.name")))
            .ForMember(dest => dest.PRDatabaseId, opt => opt.MapFrom(src => GetJsonValue(src.DatabaseProperties, "PR.id")))
            .ForMember(dest => dest.PRDatabaseName, opt => opt.MapFrom(src => GetJsonValue(src.DatabaseProperties, "PR.name")))
            .ForMember(dest => dest.DRDatabaseId, opt => opt.MapFrom(src => GetJsonValue(src.DatabaseProperties, "DR.id")))
            .ForMember(dest => dest.DRDatabaseName,opt => opt.MapFrom(src => GetJsonValue(src.DatabaseProperties,"DR.name")))
            .ForMember(dest => dest.PRReplicationName, opt => opt.MapFrom(src => GetJsonValue(src.ReplicationProperties, "PR.name")))
            .ForMember(dest => dest.DRReplicationName, opt => opt.MapFrom(src => GetJsonValue(src.ReplicationProperties, "DR.name")));

        #endregion

        #region UserActivityReport

        //UserActivityReport

        CreateMap<UserActivity, UserActivityReportVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.CreatedDate,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy hh:mm:ss tt")));

        #endregion

        #region DrReadyReport

        //DrReadyReport
        CreateMap<DRReadyStatus, DrReadyStatusForDrReadyReportVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DRReady, opt => opt.MapFrom(src => src.DRReady));
        CreateMap<DRReadyStatus, InfraObjectCountList>().ReverseMap();
        CreateMap<DrReadyStatusForDrReadyReportVm, InfraObjectCountList>().ReverseMap();

        #endregion

        #region DrReadyExecutionLogReport

        //DrReadyExecutionLogReport

        CreateMap<DRReadyLog, DRReadyExecutionReportVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<RpoSlaDeviationReport, GetRpoSlaDeviationReportByStartTimeAndEndTimeVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<DashboardView, BusinessServiceSummaryReportVm>()
            .ForMember(m => m.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()));

        #endregion

        #region LicenseReport

        //LicenseReport
        CreateMap<DatabaseReportVm, LicenseInfo>().ReverseMap();
        // CreateMap<ServerDbReportVm, LicenseInfo>().ReverseMap();
        CreateMap<ReplicationReportVm, LicenseInfo>().ReverseMap();
        CreateMap<StorageReportVm, LicenseInfo>().ReverseMap();
        CreateMap<NetworkReportVm, LicenseInfo>().ReverseMap();
        CreateMap<ApplicationReportVm, LicenseInfo>().ReverseMap();
        CreateMap<DNSReportVm, LicenseInfo>().ReverseMap();
        CreateMap<ThirdPartyReportVm, LicenseInfo>().ReverseMap();
        CreateMap<VirtualizationReportVm, LicenseInfo>().ReverseMap();


        CreateMap<LicenseReportVm, LicenseInfo>().ReverseMap();
        // CreateMap<LicenseManager, LicenseReportVm>().ForMember(dest => dest.LicenseId, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<LicenseManager, ChildLicenseReportVm>()
            .ForMember(dest => dest.LicenseId, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<LicenseReportDetail, LicenseInfo>().ReverseMap();
        CreateMap<ChildLicenseReportVm, LicenseInfo>().ReverseMap();


        //BsLicense
        CreateMap<BusinessFunctionLicenseDto, LicenseInfo>().ReverseMap();

        CreateMap<BSDatabaseReportVm, LicenseInfo>().ReverseMap();
        // CreateMap<BSServerDbReportVm, LicenseInfo>().ReverseMap();
        CreateMap<BSReplicationReportVm, LicenseInfo>().ReverseMap();
        CreateMap<BSStorageReportVm, LicenseInfo>().ReverseMap();
        CreateMap<BSNetworkReportVm, LicenseInfo>().ReverseMap();
        CreateMap<BSApplicationReportVm, LicenseInfo>().ReverseMap();
        CreateMap<BSDNSReportVm, LicenseInfo>().ReverseMap();
        CreateMap<BSThirdPartyReportVm, LicenseInfo>().ReverseMap();
        CreateMap<BSVirtualizationReportVm, LicenseInfo>().ReverseMap();

        #endregion

        #region RPOSLAReportOracle

        //Oracle
        CreateMap<OracleMonitorLogs, GetOracleRPOSLABusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "PrOracleDataGuardModel.PrMonitoringModel.PR_Server_IpAddress")))
            .ForMember(dest => dest.PRDatabaseName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "PrOracleDataGuardModel.PrMonitoringModel.PR_Database_Sid")))
            .ForMember(dest => dest.PRLogSequence,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrOracleDataGuardModel.PrMonitoringModel.PR_Log_sequence")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "OracleDataGuardModels", "MonitoringModel.Server_IpAddress")))
            .ForMember(dest => dest.DRDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "OracleDataGuardModels", "MonitoringModel.Database_Sid")))
            .ForMember(dest => dest.DRLogSequence,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "OracleDataGuardModels", "MonitoringModel.Log_sequence")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<OracleMonitorLogs, GetOracleRPOSLAReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.PRArchiveLog,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrOracleDataGuardModel.PrMonitoringModel.PR_Log_sequence")))
            .ForMember(dest => dest.DRArchiveLog,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "OracleDataGuardModels", "MonitoringModel.Log_sequence")))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")));

        #endregion

        #region RPOSLAReportMSSQLAlwaysOn

        //MSSQLAlwaysOn
        CreateMap<MSSQLAlwaysOnMonitorLogs, GetMSSQLAlwaysOnBusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "PrAlwaysOnMonitoringModel.PrMonitoringModel.PR_Server_IpAddress")))
            .ForMember(dest => dest.PRDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrAlwaysOnMonitoringModel.PrMonitoringModel.PR_Database")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "AlwaysOnMonitoringModels", "MonitoringModel.Server_IpAddress")))
            .ForMember(dest => dest.DRDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "AlwaysOnMonitoringModels", "MonitoringModel.Database")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));


        CreateMap<MSSQLAlwaysOnMonitorLogs, GetMSSQLAlwaysOnRPOSLAReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")))
            .ForMember(dest => dest.GroupRolePR,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrAlwaysOnMonitoringModel.PrMonitoringModel.PrAvailabilityGroupMonitoring.PR_Availability_Group_Role")))
            .ForMember(dest => dest.GroupRoleDR,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties,
                        "AlwaysOnMonitoringModels",
                        "MonitoringModel.AvailabilityGroupMonitoring.Availability_Group_Role")))
            .ForMember(dest => dest.ReplicaModePR,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrAlwaysOnMonitoringModel.PrMonitoringModel.PrAvailabilityGroupMonitoring.PR_Availability_Mode")))
            .ForMember(dest => dest.ReplicaModeDR,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties,
                        "AlwaysOnMonitoringModels", "MonitoringModel.AvailabilityGroupMonitoring.Availability_Mode")));

        #endregion

        #region RPOSLAReportMySQL

        //MySQL
        CreateMap<MYSQLMonitorLogs, GetRPOSLAMySqlBusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "PrMySqlMonitoringModel.MonitoringModel.PR_Server_IpAddress")))
            .ForMember(dest => dest.PRDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrMySqlMonitoringModel.MonitoringModel.PR_Database")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "MySqlMonitoringModels", "MonitoringModel.Server_IpAddress")))
            .ForMember(dest => dest.DRDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "MySqlMonitoringModels", "MonitoringModel.Database")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<MYSQLMonitorLogs, GetRPOSLAMySqlReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")))
            .ForMember(dest => dest.PrIpAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "PrMySqlMonitoringModel.MonitoringModel.PR_Server_IpAddress")))
            .ForMember(dest => dest.DrIpAddress,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "MySqlMonitoringModels", "MonitoringModel.Server_IpAddress")))
            .ForMember(dest => dest.PrDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrMySqlMonitoringModel.MonitoringModel.PR_Database")))
            .ForMember(dest => dest.DrDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "MySqlMonitoringModels", "MonitoringModel.Database")))
            .ForMember(dest => dest.PrMasterLogFile,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "PrMySqlMonitoringModel.MonitoringModel.PR_Master_Log_File")))
            .ForMember(dest => dest.DrMasterLogFile,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "MySqlMonitoringModels", "MonitoringModel.Master_Log_File")))
            .ForMember(dest => dest.PrRelayMasterLogFil,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "PrMySqlMonitoringModel.MonitoringModel.PR_Relay_Master_Log_File")))
            .ForMember(dest => dest.DrRelayMasterLogFil,
                opt => opt.MapFrom(src => GetJsonArrayValues(src.Properties, "MySqlMonitoringModels",
                    "MonitoringModel.Relay_Master_Log_File")));

        #endregion

        #region RPOSLAReportMSSQL

        //MSSQL
        CreateMap<MSSQLMonitorLogs, GetRPOSLABusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "PR_Server_IpAddress")))
            .ForMember(dest => dest.PRDatabaseName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "PR_Database")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "DR_Server_IpAddress")))
            .ForMember(dest => dest.DRDatabaseName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "DR_Database")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<MSSQLMonitorLogs, GetRPOSLAReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")))
            .ForMember(dest => dest.ConfigureRPO,
                opt => opt.MapFrom(src => Convert.ToString(src.CreatedDate, CultureInfo.InvariantCulture)))
            .ForMember(dest => dest.PRArchiveLog,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "PR_Archivelog_compression")))
            .ForMember(dest => dest.DRArchiveLog,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "DR_Archivelog_compression")));

        #endregion

        #region RPOSLAReportPostgres

        //Postgres
        CreateMap<PostgresMonitorLogs, GetRPOSLAPostgresBusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "PrPostgresMonitoringModel.MonitoringModel.PR_Server_IpAddress")))
            .ForMember(dest => dest.PRDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrPostgresMonitoringModel.MonitoringModel.PR_Database")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "PostgresMonitoringModels", "MonitoringModel.Server_IpAddress")))
            .ForMember(dest => dest.DRDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "PostgresMonitoringModels", "MonitoringModel.Database")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<PostgresMonitorLogs, GetRPOSLAPostgresReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")))
            .ForMember(dest => dest.PrReplicationStatus,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "PrPostgresMonitoringModel.MonitoringModel.PR_ReplicationStatus")))
            .ForMember(dest => dest.DrReplicationStatus,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "PostgresMonitoringModels",
                        "MonitoringModel.ReplicationStatus")))
            .ForMember(dest => dest.LastLogReceiveLocation,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrPostgresMonitoringModel.MonitoringModel.PR_LSN_Logs")))
            .ForMember(dest => dest.LastLogReplyLocation,
                opt => opt.MapFrom(src => GetJsonArrayValues(src.Properties, "PostgresMonitoringModels",
                    "MonitoringModel.LastWalReplayLsnDR")));

        #endregion

        #region RPOSLAReportOracleRAC

        //OracleRAC
        CreateMap<OracleRACMonitorLogs, GetOracleRacRPOSLABusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src => GetJsonListOfArrayValues(src.Properties,
                    "MonitoringOracleRacModel", "PrModel.PrMonitoringModel.PrServerDetails.PR_Server_IpAddress")))
            .ForMember(dest => dest.PRDatabaseName,
                opt => opt.MapFrom(src => GetJsonListOfArrayValues(src.Properties,
                    "MonitoringOracleRacModel", "PrModel.PrMonitoringModel.PrServerDetails.PR_Database_Sid")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src => GetJsonNestedListOfArrayValues(src.Properties,
                    "MonitoringOracleRacModel", "DrModels", "MonitoringModel.ServerDetails.Server_IpAddress")))
            .ForMember(dest => dest.DRDatabaseName,
                opt => opt.MapFrom(src => GetJsonNestedListOfArrayValues(src.Properties,
                    "MonitoringOracleRacModel", "DrModels", "MonitoringModel.ServerDetails.Database_Sid")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<OracleRACMonitorLogs, GetOracleRacRPOSLAReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")))
            .ForMember(dest => dest.PrIpAddress,
                opt => opt.MapFrom(src => GetJsonListOfArrayValues(src.Properties,
                    "MonitoringOracleRacModel", "PrModel.PrMonitoringModel.PrServerDetails.PR_Server_IpAddress")))
            .ForMember(dest => dest.DrIpAddress,
                opt => opt.MapFrom(src => GetJsonNestedListOfArrayValues(src.Properties,
                    "MonitoringOracleRacModel", "DrModels", "MonitoringModel.ServerDetails.Server_IpAddress")))
            .ForMember(dest => dest.PrArchiveLog,
                opt => opt.MapFrom(src => GetJsonListOfArrayValues(src.Properties,
                    "MonitoringOracleRacModel", "PrModel.PrMonitoringModel.PrServerDetails.PR_Log_sequence")))
            .ForMember(dest => dest.ArchiveLogApplied,
                opt => opt.MapFrom(src => GetJsonNestedListOfArrayValues(src.Properties,
                    "MonitoringOracleRacModel", "DrModels", "MonitoringModel.ServerDetails.Log_sequence")));

        #endregion

        #region RPOSLAReportDB2HADR

        //DB2HADR
        CreateMap<DB2HADRMonitorLog, GetRPOSLADB2HADRBusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "PR_Server_IpAddress")))
            .ForMember(dest => dest.PRDatabaseName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "PR_Database")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "DR_Server_IpAddress")))
            .ForMember(dest => dest.DRDatabaseName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "DR_Database")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<DB2HADRMonitorLog, GetRPOSLADB2HADRReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.MonitorTimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")))
            .ForMember(dest => dest.PrIpAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "PR_Server_IpAddress")))
            .ForMember(dest => dest.DrIpAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "DR_Server_IpAddress")))
            .ForMember(dest => dest.PrLogFile,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "HADResult.PRLogFile")))
            .ForMember(dest => dest.DrLogFile,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "HADResult.DRLogFile")))
            .ForMember(dest => dest.PrDatabaseName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "PR_Database")))
            .ForMember(dest => dest.DrDatabaseName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "DR_Database")));

        #endregion

        #region RPOSLAMongoDB

        CreateMap<MongoDBMonitorLog, GetMongoDBRPOSLABusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "PRMongoDBMonitoringPRModel.PRMongoDBMonitoringModel.PR_Server_IpAddress")))
            .ForMember(dest => dest.PRDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PRMongoDBMonitoringPRModel.PRMongoDBMonitoringModel.PRMongoDBMonitoring.PRDBName")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "MongoDBMonitoringModel", "Server_IpAddress")))
            .ForMember(dest => dest.DRDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "MongoDBMonitoringModel", "MongoDBMonitoring.DBName")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<MongoDBMonitorLog, GetRPOSLAReportMongoDBVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")))
            .ForMember(dest => dest.MongodStatusPR,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PRMongoDBMonitoringPRModel.PRMongoDBMonitoringModel.PRMongoDBMonitoring.PRMongodbStatus")))
            .ForMember(dest => dest.MongodStatusDR,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "MongoDBMonitoringModel", "MongoDBMonitoring.MongodbStatus")))
            .ForMember(dest => dest.PRServerHostName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "PRMongoDBMonitoringPRModel.PRMongoDBMonitoringModel.PR_Server_HostName")))
            .ForMember(dest => dest.DRServerHostName,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "MongoDBMonitoringModel", "Server_HostName")));

        #endregion

        #region RPOSLAMSSQLDBMirroring

        //MSSQLDBMirroring
        CreateMap<MSSQLDBMirroringLogs, GetMSSQLDBMirroringRPOSLABusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties, "PrMSSQLDBMirroringModel.PR_Server_IpAddress")))
            .ForMember(dest => dest.PRDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties, "PrMSSQLDBMirroringModel.PRDatabaseName")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "MSSQLDBMirroringModel", "Server_IpAddress")))
            .ForMember(dest => dest.DRDatabaseName,
                opt => opt.MapFrom(src => GetJsonArrayValues(src.Properties, "MSSQLDBMirroringModel", "DatabaseName")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<MSSQLDBMirroringLogs, GetMSSQLDBMirroringRPOSLAReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")))
            .ForMember(dest => dest.ProductionOperationMode,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrMSSQLDBMirroringModel.sqlDBMirroringPr.PROpreationMode")))
            .ForMember(dest => dest.DROperationMode,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "MSSQLDBMirroringModel", "sqlDBMirroring.OpreationMode")))
            .ForMember(dest => dest.ProductionMirroringState,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrMSSQLDBMirroringModel.sqlDBMirroringPr.PRMirroringState")))
            .ForMember(dest => dest.DRMirroringState,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "MSSQLDBMirroringModel", "sqlDBMirroring.MirroringState")));

        #endregion

        #region RPOSLASVCGM

        CreateMap<SVCGMMonitorLog, GetRPOSLASVCGMBusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "")))
            .ForMember(dest => dest.PRDatabaseName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "")))
            .ForMember(dest => dest.DRDatabaseName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<SVCGMMonitorLog, GetRPOSLASVCGMReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")));

        #endregion

        #region RPOSLAAzsureStorageAccount

        CreateMap<AzureStorageAccountMonitorlogs, GetRPOSLAAzureStorageAccountBusinessServiceDetails>()
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));
        CreateMap<AzureStorageAccountMonitorlogs, GetRPOSLAAzureStorageAccountReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.Threshold, opt => opt.MapFrom(src => src.Threshold))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")))
            .ForMember(dest => dest.PRAccountLocation,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "AzureStorageReplicationMonitoring.AzureStorageAccountReplicationPRMonitoring.Primary.Location")))
            .ForMember(dest => dest.DRAccountLocation,
               opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "AzureStorageReplicationMonitoring.AzureStorageAccountReplicationMonitoring", "Secondary.Location")))
            .ForMember(dest => dest.FailoverProgressState,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "AzureStorageReplicationMonitoring.AzureStorageAccountReplication.FailoverProgressState")))
            .ForMember(dest => dest.LastFailoverTime,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "AzureStorageReplicationMonitoring.AzureStorageAccountReplication.LastGeoFailoverTime")))
            .ForMember(dest => dest.GeoReplicationState,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "AzureStorageReplicationMonitoring.AzureStorageAccountReplication.GeoReplicationType")))
            .ForMember(dest => dest.LastSyncTime,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "AzureStorageReplicationMonitoring.AzureStorageAccountReplication.LastSyncTime")));

        #endregion

        #region RoboCopy

        CreateMap<RoboCopyMonitorLogs, GetRPOSLARoboCopyBusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "PrRoboCopyMonitorngModel.MonitoringModel.PR_Server_IpAddress")))
            .ForMember(dest => dest.PRDatabaseName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "NA")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "RoboCopyMonitoringModels", "MonitoringModel.Server_IpAddress")))
            .ForMember(dest => dest.DRDatabaseName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "NA")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<RoboCopyMonitorLogs, GetRPOSLARoboCopyReportVm>().ReverseMap();

        #endregion

        #region Rsync

        CreateMap<RsyncMonitorLog, GetRPOSLARSyncBusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "PrRSyncReplicationModel.PrMonitoringModel.PR_Server_IpAddress")))
            .ForMember(dest => dest.PRDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrRSyncReplicationModel.PrMonitoringModel.PR_Database")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "RSyncReplicationModels", "MonitoringModel.Server_IpAddress")))
            .ForMember(dest => dest.DRDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "RSyncReplicationModels", "MonitoringModel.Database")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<RsyncMonitorLog, GetRsyncRPOSLAReportVm>().ReverseMap();

        #endregion

        #region MSSqlNativeLogShipping

        //MSSqlNLS
        CreateMap<MssqlNativeLogShippingMonitorLog, GetMSSqlNLSBusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrNlsMonitoringModel.MonitoringModel.PR_Server_IpAddress")))
            .ForMember(dest => dest.PRDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrNlsMonitoringModel.MonitoringModel.PR_Database_Name")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "NlsOnMonitoringModels", "MonitoringModel.Server_IpAddress")))
            .ForMember(dest => dest.DRDatabaseName,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "NlsOnMonitoringModels", "MonitoringModel.Database_Name")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<MssqlNativeLogShippingMonitorLog, GetMSSqlNLSRPOSLAReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")))
            .ForMember(dest => dest.LSNLastBackupLog,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrNlsMonitoringModel.MonitoringModel.PRLastLSN_backup")))
            .ForMember(dest => dest.LSNLastRestoredLog,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "NlsOnMonitoringModels", "MonitoringModel.LastLSN_restored")))
            .ForMember(dest => dest.LastGeneratedLog,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "PrNlsMonitoringModel.MonitoringModel.PR_Last_Backup_Transaction_Log")))
            .ForMember(dest => dest.LastAppliedLog,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrNlsMonitoringModel.MonitoringModel.PRlast_backup_date")))
            .ForMember(dest => dest.LastLogGeneratedTime,
                opt => opt.MapFrom(src => GetJsonArrayValues(src.Properties, "NlsOnMonitoringModels",
                    "MonitoringModel.Last_Copied_Transaction_Log")))
            .ForMember(dest => dest.LastLogAppliedTime,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "NlsOnMonitoringModels", "MonitoringModel.last_restored_date")));

        #endregion

        #region RPOSLASRM

        //SRM
        CreateMap<SRMMonitorLog, GetRPOSLASRMBusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "PR_SRMServer_IPAddress")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "DR_SRMServer_IPAddress")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<SRMMonitorLog, GetRPOSLASRMReportVM>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.Threshold, opt => opt.MapFrom(src => src.Threshold))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")))
            .ForMember(dest => dest.PRProtectionGroupName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "PR_Protection_GroupName")))
            .ForMember(dest => dest.DRProtectionGroupName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "DR_Protection_GroupName")))
            .ForMember(dest => dest.PRRecoveryPlanAndState,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "PR_Recovery_Plan_And_State")))
            .ForMember(dest => dest.DRRecoveryPlanAndState,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties, "DR_Recovery_Plan_And_State")));

        #endregion

        #region RPOSLAActiveDirectory
        //WindowsActiveDirectory
        CreateMap<ActiveDirectoryMonitorLog, GetActiveDirectoryBusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src => GetJsonValue(src.Properties,
                    "PrActiveDirectoryReplication.ActiveDirectoryMonitoring.IPAddress")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "ActiveDirectoryReplication", "ActiveDirectoryMonitoring.IPAddress")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));


        CreateMap<ActiveDirectoryMonitorLog, GetActiveDirectoryRPOSLAReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")))
            .ForMember(dest => dest.PRController,
                opt => opt.MapFrom(src =>
                    GetJsonValue(src.Properties,
                        "PrActiveDirectoryReplication.ActiveDirectoryMonitoring.DomainControllerName")))
            .ForMember(dest => dest.DRController,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties,
                        "ActiveDirectoryReplication",
                        "ActiveDirectoryMonitoring.DomainControllerName")))
            .ForMember(dest => dest.PRLastRepSuccess,
                opt => opt.MapFrom(src =>
                    GetJsonValue(src.Properties,
                        "PrActiveDirectoryReplication.ActiveDirectoryMonitoring.LastReplicationSuccess")))
            .ForMember(dest => dest.DRLastRepSuccess,
                opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties,
                        "ActiveDirectoryReplication", "ActiveDirectoryMonitoring.LastReplicationSuccess")));
        #endregion

        #region RPOSLADataSyncReport
        //DataSync
        CreateMap<DataSyncMonitorLog, GetDataSyncBusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src => GetJsonValue(src.Properties,
                    "PrDataSyncReplicationModel.PrMonitoringModel.PR_Server_IpAddress")))
            .ForMember(dest => dest.DRIPAddress,
                 opt => opt.MapFrom(src =>
                    GetJsonArrayValues(src.Properties, "DataSyncReplicationModels", "MonitoringModel.Server_IpAddress")))
            .ForMember(dest => dest.Date,
                 opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<DataSyncMonitorLog, GetDataSyncRPOSLAReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")));

        CreateMap<FastCopyMonitorLog, GetFastCopyMonitorVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.SourceIP, opt => opt.MapFrom(src => src.SourceIP))
            .ForMember(dest => dest.DestinationIP, opt => opt.MapFrom(src => src.DestinationIP))
            .ForMember(dest => dest.SourcePath, opt => opt.MapFrom(src => src.SourcePath))
            .ForMember(dest => dest.DestinationPath, opt => opt.MapFrom(src => src.DestinationPath))
            .ForMember(dest => dest.ReplicationFileName, opt => opt.MapFrom(src => src.LastFileName))
            .ForMember(dest => dest.LastFileSize, opt => opt.MapFrom(src => src.LastFileSize))
            .ForMember(dest => dest.TotalFileSize, opt => opt.MapFrom(src => src.TotalFilesSize))
            .ForMember(dest => dest.IncrementalFilesCount, opt => opt.MapFrom(src => src.IncrementalFilesCount))
            .ForMember(dest => dest.ReplicationFilesCount, opt => opt.MapFrom(src => src.IncrementalFilesCount))
            .ForMember(dest => dest.StartTime, opt => opt.MapFrom(src => src.StartTime))
            .ForMember(dest => dest.EndTime, opt => opt.MapFrom(src => src.EndTime));

        #endregion

        #region RPOSLAZetroVPG
        CreateMap<ZertoVpgMonitorLog, GetRPOSLAZetroVpgBusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties, "ZertoVPGMonitoringPR.ReplicationMonitoringPR.IPAddress")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src => GetJsonArrayValues(src.Properties, "ZertoVPGMonitoring", "ReplicationMonitoring.IPAddress")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<ZertoVpgMonitorLog, GetRPOSLAZertoVpgReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")))
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties, "ZertoVPGMonitoringPR.ReplicationMonitoringPR.IPAddress")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src => GetJsonArrayValues(src.Properties, "ZertoVPGMonitoring", "ReplicationMonitoring.IPAddress")))
            .ForMember(dest => dest.PRSiteName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "ZertoVPGMonitoringPR.ReplicationMonitoringPR.SiteName")))
            .ForMember(dest => dest.DRSiteName,
                opt => opt.MapFrom(src => GetJsonArrayValues(src.Properties, "ZertoVPGMonitoring", "ReplicationMonitoring.SiteName")))
            .ForMember(dest => dest.PRVPGName,
                opt => opt.MapFrom(src => GetJsonArrayValues(src.Properties, "ZertoVPGMonitoring", "ReplicationMonitoring.VPGName")))
            .ForMember(dest => dest.DRVPGName,
                opt => opt.MapFrom(src => GetJsonArrayValues(src.Properties, "ZertoVPGMonitoring", "ReplicationMonitoring.VPGName")))
            .ForMember(dest => dest.PRVPGState,
                opt => opt.MapFrom(src => GetJsonArrayValues(src.Properties, "ZertoVPGMonitoring", "ReplicationMonitoring.VPGState")))
            .ForMember(dest => dest.DRVPGState,
                opt => opt.MapFrom(src => GetJsonArrayValues(src.Properties, "ZertoVPGMonitoring", "ReplicationMonitoring.VPGState")));


        #endregion

        #region RPOSLASybaseRSHADRReport

        CreateMap<SybaseRSHADRMonitorLog,GetSybaseRSHADRBusinessServiceDetails>()
            .ForMember(dest => dest.PrIpAddress,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties, "PrSybaseWithHADRMonitoring.ReplicationServerMonitoring.ServerIpAddress")))
            .ForMember(dest => dest.DrIpAddress,
                opt => opt.MapFrom(src => GetJsonArrayValues(src.Properties, "SybaseWithHADRMonitoring", "ReplicationServerMonitoring.ServerIpAddress")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));

        CreateMap<SybaseRSHADRMonitorLog, GetRPOSLASybaseRSHADRReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.PrIpAddress, opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "PrSybaseWithHADRMonitoring.ReplicationServerMonitoring.ServerIpAddress")))
            .ForMember(dest => dest.DrIpAddress, opt => opt.MapFrom(src => GetJsonArrayValues(src.Properties, "SybaseWithHADRMonitoring", "ReplicationServerMonitoring.ServerIpAddress")))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.TimeStamp, opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")));

        #endregion

        #region RPOSLAReportMSSQLAlwaysOnAvailabilityGroupMonitor

        //MSSQLAlwaysOnAvailabilityGroupMonitor
        CreateMap<MssqlAlwaysOnAvailabilityGroupMonitorLog, GetMSSQLAlwaysOnAvailabilityGroupMonitorBusinessServiceDetails>()
            .ForMember(dest => dest.PRIPAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties,
                    "PrAlwaysOnAvailabilityGroupMonitoringDetails.IpAddress")))
            .ForMember(dest => dest.DRIPAddress,
                opt => opt.MapFrom(src =>
                    GetJsonNestedListOfArrayValues(src.Properties, "AlwaysOnAvailabilityGroupMonitoringDetails", "AlwaysOnAvailabilityGroupMonitoringDetailsList","IpAddress")))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt")));


        CreateMap<MssqlAlwaysOnAvailabilityGroupMonitorLog, GetMssqlAlwaysOnAvailabilityGroupMonitorLogReportVm>()
            .ForMember(dest => dest.SrNo, opt => opt.MapFrom(src => GetJsonProperties.GenerateId()))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DataLag, opt => opt.MapFrom(src => src.DataLagValue))
            .ForMember(dest => dest.ConfigureRPO, opt => opt.MapFrom(src => src.ConfiguredRPO))
            .ForMember(dest => dest.IsDataLagExceeded, opt => opt.MapFrom(src => CalculateDataLag(src)))
            .ForMember(dest => dest.IsThresholdExceeded, opt => opt.MapFrom(src => CalculateThreshold(src)))
            .ForMember(dest => dest.TimeStamp,
                opt => opt.MapFrom(src => src.CreatedDate.ToString("dd-MM-yyyy HH:mm:ss")))
            .ForMember(dest => dest.GroupRolePR,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrAlwaysOnAvailabilityGroupMonitoringDetails.AvailabilityGroupMonitoring.AvailabilityGroupRole")))
            .ForMember(dest => dest.GroupRoleDR,
                opt => opt.MapFrom(src =>
                    GetJsonNestedListOfArrayValues(src.Properties,
                        "AlwaysOnAvailabilityGroupMonitoringDetails", "AlwaysOnAvailabilityGroupMonitoringDetailsList",
                        "AvailabilityGroupMonitoring.AvailabilityGroupRole")))
            .ForMember(dest => dest.ReplicaModePR,
                opt => opt.MapFrom(src =>
                    GetJsonProperties.GetJsonValue(src.Properties,
                        "PrAlwaysOnAvailabilityGroupMonitoringDetails.AvailabilityGroupMonitoring.ReplicaMode")))
            .ForMember(dest => dest.ReplicaModeDR,
                opt => opt.MapFrom(src =>
                    GetJsonNestedListOfArrayValues(src.Properties,
                        "AlwaysOnAvailabilityGroupMonitoringDetails", "AlwaysOnAvailabilityGroupMonitoringDetailsList",
                        "AvailabilityGroupMonitoring.ReplicaMode")));

        #endregion

        #region DRDRillReport

        //DrDrill

        CreateMap<BusinessService, BusinessServiceDrDillDetails>()
            .ForMember(dest => dest.SiteProperties,
                opt => opt.MapFrom(src => src.SiteProperties))
            .ForMember(dest => dest.BusinessServiceName,
                opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.BusinessServiceId,
                opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<WorkflowOperationGroup, WorkflowOperationGroupDrDrillReportVm>()
            .ForMember(dest => dest.StartTime, opt => opt.MapFrom(src => Convert.ToString(src.CreatedDate)))
            .ForMember(dest => dest.EndTime, opt => opt.MapFrom(src => Convert.ToString(src.LastModifiedDate)));

        CreateMap<WorkflowActionResult, WorkflowActionResultDrDrillReportVm>()
            .ForMember(dest => dest.WorkflowActionId, opt => opt.MapFrom(src => src.ActionId))
            .ForMember(dest => dest.ExecutionNode, opt => opt.MapFrom(src => src.NodeId))
            .ForMember(dest => dest.StartTime, opt => opt.MapFrom(src => Convert.ToString(src.StartTime)))
            .ForMember(dest => dest.EndTime, opt => opt.MapFrom(src => Convert.ToString(src.EndTime)));

        CreateMap<WorkflowOperation, WorkflowOperationDrDrillReportVm>()
            .ForMember(dest => dest.WorkflowOperationId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.UserId, opt => opt.MapFrom(src => src.CreatedBy))
            .ForMember(dest => dest.StartTime, opt => opt.MapFrom(src => Convert.ToString(src.StartTime)))
            .ForMember(dest => dest.EndTime, opt => opt.MapFrom(src => Convert.ToString(src.EndTime)));

        CreateMap<WorkflowOperation, RtoReportVm>()
            .ForMember(dest => dest.WorkflowOperationId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.UserId, opt => opt.MapFrom(src => src.CreatedBy))
            .ForMember(dest => dest.StartTime, opt => opt.MapFrom(src => Convert.ToString(src.StartTime)))
            .ForMember(dest => dest.EndTime, opt => opt.MapFrom(src => Convert.ToString(src.EndTime)));

        #endregion

        #region AirGap Report

        CreateMap<CyberAirGapLog, AirGapListReportVm>()
            .ForMember(dest => dest.AirGapId, opt => opt.MapFrom(src => src.AirGapId))
            .ForMember(dest => dest.AirGapName, opt => opt.MapFrom(src => src.AirGapName))
            .ForMember(dest => dest.StartTime, opt => opt.MapFrom(src => src.StartTime.ToString("dd-MM-yyyy hh:mm:ss tt")))
            .ForMember(dest => dest.EndTime, opt => opt.MapFrom(src => src.EndTime.ToString("dd-MM-yyyy hh:mm:ss tt")));
        CreateMap<CyberAirGapLog, GetAirGapListVm>()
            .ForMember(dest => dest.AirGapId, opt => opt.MapFrom(src => src.AirGapId))
            .ForMember(dest => dest.AirGapName, opt => opt.MapFrom(src => src.AirGapName))
            .ForMember(dest => dest.StartTime, opt => opt.MapFrom(src => src.StartTime.ToString("dd-MM-yyyy hh:mm:ss tt")))
            .ForMember(dest => dest.EndTime, opt => opt.MapFrom(src => src.EndTime.ToString("dd-MM-yyyy hh:mm:ss tt")));
        #endregion

        #region DriftReport

        //DriftReport
        CreateMap<DriftEvent, DriftEventReportVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DriftResourceSummary, DriftResourceVm>()
            .ForMember(dest => dest.InfraObjectId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.IsConflict, opt => opt.MapFrom(src => src.IsConflict));

        #endregion

        #region CyberSnaps Report

        CreateMap<CyberSnaps, GetCyberSnapsListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<CyberSnaps, CyberSnapsReportVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        #endregion

        #region InfraObjectSchedulerLogsReport
        CreateMap<InfraObjectSchedulerLogs, InfraObjectSchedulerLogsReport>()
           .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.ReferenceId));
        #endregion

        #region SchedulerWorkflowActionReport
        CreateMap<SchedulerWorkflowActionResults, ScheduleWorkflowActionResultsReportVm>()
            .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.ReferenceId));
        #endregion

        #region CGExecutionReport
        CreateMap<RpForVmCgEnableDisableStatus, CGExecutionReportVm>()
            .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.ReferenceId));
        #endregion
    }

    #region Helper

    private static bool CalculateDataLag<T>(T src)
    {
        try
        {
            var jsonString = JsonConvert.SerializeObject(src);

            var jsonObject = JObject.Parse(jsonString);

            var configuredRpo = jsonObject.SelectToken("ConfiguredRPO")?.ToString();

            var dataLag = jsonObject.SelectToken("DataLagValue")?.ToString();

            if (dataLag is null or "NA") return false;
            var dataLagMinutesValue = GetJsonProperties.ConvertToMinutes(dataLag);

            if (dataLagMinutesValue > Convert.ToInt32(configuredRpo)) return true;
            return false;
        }
        catch (Exception)
        {
            return false;
        }
    }

    private static bool CalculateThreshold<T>(T src)
    {
        try
        {
            var jsonString = JsonConvert.SerializeObject(src);

            var jsonObject = JObject.Parse(jsonString);

            var configuredRpo = jsonObject.SelectToken("ConfiguredRPO")?.ToString();

            var threshold = jsonObject.SelectToken("Threshold")?.ToString();

            var dataLag = jsonObject.SelectToken("DataLagValue")?.ToString();

            var dataLagMinutesValue = GetJsonProperties.ConvertToMinutes(dataLag);

            if (dataLagMinutesValue > Convert.ToInt32(threshold) &&
                dataLagMinutesValue <= Convert.ToInt32(configuredRpo)) return true;

            return false;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public static string GetJsonArrayValues(string json, string arrayPath, string propertyPath)
    {
        try
        {
            var jsonObject = JObject.Parse(json);
            if (jsonObject.SelectToken(arrayPath) is JArray { Count: > 0 } arrayToken)
                foreach (var item in arrayToken!)
                {
                    var jsonValue = !string.IsNullOrEmpty(item.SelectToken(propertyPath)?.ToString())
                        && item.SelectToken(propertyPath)?.ToString() !="NA"
                        ? item.SelectToken(propertyPath)?.ToString()
                        : "NA";

                    return jsonValue;
                }
            else
                return "NA";
        }
        catch
        {
            return "NA";
        }

        return "NA";
    }

    public static List<string> GetJsonListOfArrayValues(string json, string arrayPath, string propertyPath)
    {
        var data = new List<string>();

        var jsonObject = JObject.Parse(json);
        if (jsonObject.SelectToken(arrayPath) is JArray arrayToken && arrayToken.Count > 0)
            foreach (var item in arrayToken!)
            {
                var jsonValue = !string.IsNullOrEmpty(item.SelectToken(propertyPath)?.ToString())
                    && item.SelectToken(propertyPath)?.ToString() !="NA"
                    ? item.SelectToken(propertyPath)!.ToString()
                    : "-";

                data.Add(jsonValue);
            }
        else
            data.Add("-");

        return data;
    }

    public static List<string> GetJsonNestedListOfArrayValues(string json, string reportArrayPath, string jsonArrayPath,
        string propertyPath)
    {
        var data = new List<string>();

        var jsonObject = JObject.Parse(json);
        if (jsonObject.SelectToken(reportArrayPath) is JArray { Count: > 0 } reportArrayValues)
            foreach (var reportValues in reportArrayValues)
            {
                if (reportValues.SelectToken(jsonArrayPath) is JArray jsonArrayValues)
                    foreach (var jsonValues in jsonArrayValues)
                    {
                        var jsonValue = !string.IsNullOrEmpty(jsonValues.SelectToken(propertyPath)?.ToString()) 
                            && jsonValues.SelectToken(propertyPath)?.ToString() !="NA"
                            ? jsonValues.SelectToken(propertyPath)!.ToString()
                            : "-" ;
                        data.Add(jsonValue);
                    }
            }
        else
            data.Add("-");

        return data;
    }
    public static string GetJsonValue(string json, string jsonPath)
    {
        var jsonObject = JObject.Parse(json);
        return jsonObject.SelectToken(jsonPath)?.ToString() ?? "-";
    }

    #endregion
}