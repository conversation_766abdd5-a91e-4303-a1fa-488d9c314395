﻿using ContinuityPatrol.Application.Features.DashboardView.Event.OperationalAnalyticsView;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;


namespace ContinuityPatrol.Web.Areas.Dashboard.Controllers;
[Area("Dashboard")]
public class AnalyticsController : BaseController
{

    private readonly IDataProvider _dataProvider;
    private readonly ILogger<AnalyticsController> _logger;
    private readonly IPublisher _publisher;
  
    public AnalyticsController( IDataProvider dataProvider, IPublisher publisher, ILogger<AnalyticsController> logger)
    {
        _dataProvider = dataProvider;
        _publisher = publisher;
        _logger = logger;
    }
 
    public async Task<IActionResult> Index()
    {
        await _publisher.Publish(new OperationalAnalyticsEvent());

      return View();
     }

    //GetOperationalReadinessDetails
    [HttpGet]
    public async Task<IActionResult> GetDrillAnalytics()
    {

        try
        {
            var operationalDetails = await _dataProvider.DashboardView.GetDrillAnalytics();

          

            return Json(new { success = true, data = operationalDetails });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on server type page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }

    }

    //GetOperationalFailureAnalytics
    [HttpGet]
    public async Task<IActionResult> GetComponentFailureAnalytics()
    {
        try
        {
            var operationalFailure = await _dataProvider.DashboardView.GetComponentFailureAnalytics();
            return Json(new { success = true, data = operationalFailure });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on server type page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }

    }

    [HttpGet]
    public async Task<IActionResult> GetOperationalAvailabilityAnalytics()
    {
        try
        {
            var availableOperation = await _dataProvider.DashboardView.GetOperationalAvailabilityAnalytics();
            return Json(new { success = true, data = availableOperation });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on server type page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }

    }
    //GetOperationalReadinessAnalytics

    [HttpGet]
    public async Task<IActionResult> GetWorkflowAnalytics()
    {
        try
        {
            var operationalReadiness = await _dataProvider.DashboardView.GetWorkflowAnalytics();
            return Json(new { success = true, data = operationalReadiness });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on server type page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }

    }

    [HttpGet]
    public async Task<IActionResult> GetSlaBreach()
    {
        try
        {
            var getSLA = await _dataProvider.DashboardView.GetSlaBreach();
            return Json(new { success = true, data = getSLA });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on server type page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }

    }

    [HttpGet]
    public async Task<IActionResult> GetOperationalHealthSummary()
    {
        try
        {
            var getHealthSummary = await _dataProvider.DashboardView.GetOperationalServiceHealthSummary();
            return Json(new { success = true, data = getHealthSummary });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on server type page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }

    }    

}