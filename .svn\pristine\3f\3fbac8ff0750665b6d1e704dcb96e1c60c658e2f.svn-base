﻿using ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerWorkflowDetailModel;

namespace ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Queries.GetList;

public class GetInfraObjectSchedulerWorkflowDetailListQueryHandler : IRequestHandler<
    GetInfraObjectSchedulerWorkflowDetailListQuery, List<InfraObjectSchedulerWorkflowDetailListVm>>
{
    private readonly IInfraObjectSchedulerWorkflowDetailRepository _infraObjectSchedulerWorkflowDetailRepository;
    private readonly IMapper _mapper;

    public GetInfraObjectSchedulerWorkflowDetailListQueryHandler(IMapper mapper,
        IInfraObjectSchedulerWorkflowDetailRepository infraObjectSchedulerWorkflowDetailRepository)
    {
        _mapper = mapper;
        _infraObjectSchedulerWorkflowDetailRepository = infraObjectSchedulerWorkflowDetailRepository;
    }

    public async Task<List<InfraObjectSchedulerWorkflowDetailListVm>> Handle(
        GetInfraObjectSchedulerWorkflowDetailListQuery request, CancellationToken cancellationToken)
    {
        var infraObjectSchedulerWorkflowDetail =
            (await _infraObjectSchedulerWorkflowDetailRepository.ListAllAsync()).ToList();

        return infraObjectSchedulerWorkflowDetail.Count == 0
            ? new List<InfraObjectSchedulerWorkflowDetailListVm>()
            : _mapper.Map<List<InfraObjectSchedulerWorkflowDetailListVm>>(infraObjectSchedulerWorkflowDetail);
    }
}