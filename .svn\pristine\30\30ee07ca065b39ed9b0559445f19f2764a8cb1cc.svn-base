using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberComponentMapping.Events.Create;

public class CyberComponentMappingCreatedEventHandler : INotificationHandler<CyberComponentMappingCreatedEvent>
{
    private readonly ILogger<CyberComponentMappingCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberComponentMappingCreatedEventHandler(ILoggedInUserService userService,
        ILogger<CyberComponentMappingCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(CyberComponentMappingCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create}{Modules.CyberResiliencyMapping}",
            Entity = Modules.CyberResiliencyMapping.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"Cyber Resiliency Mapping '{createdEvent.Name}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Cyber Resiliency Mapping '{createdEvent.Name}' created successfully.");
    }
}