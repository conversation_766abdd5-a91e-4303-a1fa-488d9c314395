﻿using ContinuityPatrol.Application.Features.ComponentType.Commands.Create;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Update;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class ComponentTypeService : BaseClient, IComponentTypeService
{
    public ComponentTypeService(IConfiguration config, IAppCache cache, ILogger<ComponentTypeService> logger)
        : base(config, cache, logger)
    {
    }

    public async Task<List<ComponentTypeListVm>> GetComponentTypeList()
    {
        var request = new RestRequest("api/v6/componenttypes");

        return await Get<List<ComponentTypeListVm>>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateComponentTypeCommand createComponentTypeCommand)
    {
        var request = new RestRequest("api/v6/componenttypes", Method.Post);

        request.AddJsonBody(createComponentTypeCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateComponentTypeCommand updateComponentTypeCommand)
    {
        var request = new RestRequest("api/v6/componenttypes", Method.Put);

        request.AddJsonBody(updateComponentTypeCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string componentTypeId)
    {
        var request = new RestRequest($"api/v6/componenttypes/{componentTypeId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<ComponentTypeDetailVm> GetComponentTypeById(string componentTypeId)
    {
        var request = new RestRequest($"api/v6/componenttypes/{componentTypeId}");

        return await Get<ComponentTypeDetailVm>(request);
    }

    public async Task<bool> IsComponentTypeExist(string componentType, string id)
    {
        var request = new RestRequest($"api/v6/componenttypes/name-exist?type={componentType}&id={id}");

        return await Get<bool>(request);
    }

    public async Task<List<ComponentTypeModel>> GetComponentTypeListByName(string name)
    {
        var request = new RestRequest($"api/v6/componenttypes/by/name?name={name}");

        return await Get<List<ComponentTypeModel>>(request);
    }

    public async Task<PaginatedResult<ComponentTypeListVm>> GetPaginatedComponentTypes(GetComponentTypePaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/componenttypes/paginated-list");

        return await Get<PaginatedResult<ComponentTypeListVm>>(request);
    }

}