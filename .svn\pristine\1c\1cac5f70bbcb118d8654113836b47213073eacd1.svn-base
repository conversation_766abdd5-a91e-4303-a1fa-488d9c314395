﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.UpdateLog;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetByMaintenanceInfraObject;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetByWorkflowOperationId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetLogDataByGroupId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetNames;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatus;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatusList;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningUserDetails;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupByInfraObjectId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupByNodeId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupListByWorkflowId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowServiceStatus;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class WorkflowOperationGroupController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<WorkflowOperationGroupListVm>>> GetWorkflowOperationGroupList()
    {
        Logger.LogDebug("Get All WorkflowOperationGroup");

        //return Ok(await Cache.GetOrAddAsync(
        //    ApplicationConstants.Cache.AllWorkflowOperationGroupCacheKey + LoggedInUserService.CompanyId,
        //    () => Mediator.Send(new GetWorkflowOperationGroupListQuery()), CacheExpiry));

        return Ok(await Mediator.Send(new GetWorkflowOperationGroupListQuery()));
    }

    [HttpGet("{id}", Name = "WorkflowOperationGroup")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<WorkflowOperationGroupDetailVm>> GetWorkflowOperationGroupById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "WorkflowOperationGroup Id");

        Logger.LogDebug($"Get WorkflowOperationGroup Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetWorkflowOperationGroupDetailQuery { Id = id }));
    }

    [HttpGet("nodeId")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<WorkflowOperationGroupByNodeIdVm>>> GetWorkflowOperationGroupByNodeId(string nodeId)
    {
        Guard.Against.InvalidGuidOrEmpty(nodeId, "WorkflowOperationGroup nodeId");

        Logger.LogDebug($"Get WorkflowOperationGroup Detail by Id '{nodeId}'");

        return Ok(await Mediator.Send(new GetWorkflowOperationGroupByNodeIdQuery { NodeId = nodeId }));
    }

    [HttpGet("workflowId")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<WorkflowOperationGroupListByWorkflowIdVm>>>
        GetWorkflowOperationGroupByWorkflowId(string workflowId, string workflowOperationId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowId, "WorkflowOperationGroup WorkflowId & WorkflowOperationId");

        Logger.LogDebug("Get WorkflowOperationGroup List by WorkflowId & WorkflowOperationId ");

        return Ok(await Mediator.Send(new GetWorkflowOperationGroupListByWorkflowIdQuery
            { WorkflowId = workflowId, WorkflowOperationId = workflowOperationId }));
    }


    [HttpPost]
    [Authorize(Policy = Permissions.Orchestration.Create)]
    public async Task<ActionResult<CreateWorkflowOperationGroupResponse>> CreateWorkflowOperationGroup(
        [FromBody] CreateWorkflowOperationGroupCommand createWorkflowOperationGroupCommand)
    {
        Logger.LogDebug($" Create WorkflowOperationGroup '{createWorkflowOperationGroupCommand.ProfileId}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateWorkflowOperationGroup),
            await Mediator.Send(createWorkflowOperationGroupCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Orchestration.Edit)]
    public async Task<ActionResult<UpdateWorkflowOperationGroupResponse>> UpdateWorkflowOperationGroup(
        [FromBody] UpdateWorkflowOperationGroupCommand updateWorkflowOperationGroupCommand)
    {
        Logger.LogDebug(
            $"Update WorkflowOperationGroup '{updateWorkflowOperationGroupCommand.CurrentActionName}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateWorkflowOperationGroupCommand));
    }

    [HttpPut("update-log-state")]
    [Authorize(Policy = Permissions.Orchestration.Edit)]
    public async Task<ActionResult<UpdateOperationGroupLogResponse>> UpdateOperationGroupLog([FromBody] UpdateOperationGroupLogCommand updateWorkflowOperationGroupCommand)
    {
        Logger.LogDebug($"Update WorkflowOperationGroup workflowName '{updateWorkflowOperationGroupCommand.WorkflowName}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateWorkflowOperationGroupCommand));
    }


    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Orchestration.Delete)]
    public async Task<ActionResult<DeleteWorkflowOperationGroupResponse>> DeleteWorkflowOperationGroup(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "WorkflowOperationGroup Id");

        Logger.LogDebug($"Delete WorkflowOperationGroup Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteWorkflowOperationGroupCommand { Id = id }));
    }

    [HttpGet]
    [Route("runningStatus")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<WorkflowOperationGroupRunningStatusVm>>> GetWorkflowOperationGroupRunningList()
    {
        Logger.LogDebug("Get All WorkflowOperationGroup Running Status.");

        return Ok(await Mediator.Send(new GetWorkflowOperationGroupRunningStatusQuery()));
    }

    [HttpGet("userId")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<WorkflowOperationGroupRunningUserVm>>>
        GetWorkflowOperationGroupByRunningUserId(string userId)
    {
        Logger.LogDebug("Get All WorkflowOperationGroup Running Status.");

        return Ok(await Mediator.Send(new GetWorkflowOperationGroupRunningUserDetailQuery { UserId = userId }));
    }

    [HttpGet]
    [Route("names")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<WorkflowOperationGroupNameVm>>> GetWorkflowOperationGroupNames()
    {
        Logger.LogDebug("Get All WorkflowOperationGroup Names");

        //return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllWorkflowOperationGroupNameCacheKey,
        //    () => Mediator.Send(new GetWorkflowOperationGroupNameQuery()), CacheExpiry));

        return Ok(await Mediator.Send(new GetWorkflowOperationGroupNameQuery()));
    }
    #region
    [HttpGet]
    [Route("runningStatusList")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<ProfileRunningCountListVm>>> GetWorkflowOperationGroupRunningStatusList()
    {
        Logger.LogDebug("Get All WorkflowOperationGroup Running Status List.");

        return Ok(await Mediator.Send(new GetWorkflowOperationGroupRunningStatusListQuery()));
    }
    #endregion


    [HttpGet]
    [Route("by/{workflowOperationId}")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<GetByWorkflowOperationIdVm>>> GetWorkflowOperationGroupByWorkflowOperationId(
        string workflowOperationId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowOperationId, "WorkflowOperationGroup Id");

        Logger.LogDebug($"Get WorkflowOperationGroup Detail by Id '{workflowOperationId}'");

        return Ok(await Mediator.Send(new GetByWorkflowOperationIdQuery { WorkflowOperationId = workflowOperationId }));
    }

    [Route("infraObjectId")]
    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<WorkflowOperationGroupByInfraObjectIdVm>>> GetWorkflowOperationGroupByInfraObjectId(
        string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "InfraObject Id");

        Logger.LogDebug($"Get WorkflowOperationGroup Detail by Id '{infraObjectId}'");

        return Ok(await Mediator.Send(new GetWorkflowOperationGroupByInfraObjectIdQuery
            { InfraObjectId = infraObjectId }));
    }

    [Route("name-exist")]
    [HttpGet]
    public async Task<ActionResult> IsWorkflowOperationGroupNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "WorkflowOperationGroup Name");

        Logger.LogDebug($"Check Name Exists Detail by WorkflowOperationGroup Name '{name}' and id '{id}'");

        return Ok(await Mediator.Send(new GetWorkflowOperationGroupNameUniqueQuery
            { WorkflowOperationGroupName = name, WorkflowOperationGroupId = id }));
    }

    [Route("paginated-list")]
    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<PaginatedResult<WorkflowOperationGroupListVm>>> GetPaginatedWorkflowOperationGroup([FromQuery] GetWorkflowOperationGroupPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in WorkflowOperationGroup Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpGet]
    [Route("active-infraobjects")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<GetMaintenanceInfraObjectListVm>>> GetMaintenanceInfraObjectList(
        string infraObjectId)
    {
        Logger.LogDebug($"Check Running Workflow Active InfraObject Id'{infraObjectId}'");

        return Ok(await Mediator.Send(new GetMaintenanceInfraObjectListQuery { InfraObjectId = infraObjectId }));
    }
    
    [Route("log-push")]
    [HttpGet] 
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<GetLogByGroupIdVm>>> GetLogDataByGroupId(string workflowOperationGroupId,bool isHub)
    {
        Logger.LogDebug($"Get Seq Log Detail by WorkflowOperationGroupId '{workflowOperationGroupId}'");

        return Ok(await Mediator.Send(new GetLogByGroupIdQuery { GroupId=workflowOperationGroupId }));
    }
    [AllowAnonymous]
    [HttpGet("check-windows-service")]
   // [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<GetWorkflowServiceResponse>> CheckWindowsServiceConnection(string type)
    {
        Logger.LogDebug("Check Workflow Service Status");

        return Ok(await Mediator.Send(new GetWorkflowServiceStatusQuery {Type = type }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys =
        {
            ApplicationConstants.Cache.AllWorkflowOperationGroupCacheKey + LoggedInUserService.CompanyId,
            ApplicationConstants.Cache.AllWorkflowOperationGroupNameCacheKey
        };

        ClearCache(cacheKeys);
    }
}