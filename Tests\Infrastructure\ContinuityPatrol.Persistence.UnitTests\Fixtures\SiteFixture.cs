using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SiteFixture : IDisposable
{
    public List<Site> SitePaginationList { get; set; }
    public List<Site> SiteList { get; set; }
    public Site SiteDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SiteFixture()
    {
        var fixture = new Fixture();

        SiteList = fixture.Create<List<Site>>();

        SitePaginationList = fixture.CreateMany<Site>(20).ToList();

        SitePaginationList.ForEach(x => x.CompanyId = CompanyId);

        SiteList.ForEach(x => x.CompanyId = CompanyId);

        SiteDto = fixture.Create<Site>();

        SiteDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public Site CreateSite(
        string name = "Default Site",
        string companyId = "COMPANY_123",
        string typeId = "TYPE_001",
        string locationId = "LOC_001",
        string location = "Default Location",
        string platformType = "Windows",
        string dataTemperature = "Hot",
        bool isActive = true,
        bool isDelete = false)
    {
        return new Site
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = name,
            CompanyId = companyId,
            TypeId = typeId,
            LocationId = locationId,
            Location = location,
            PlatformType = platformType,
            DataTemperature = dataTemperature,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<Site> CreateMultipleSites(int count, string companyId = "COMPANY_123")
    {
        var sites = new List<Site>();
        for (int i = 1; i <= count; i++)
        {
            sites.Add(CreateSite(
                name: $"Site {i}",
                companyId: companyId,
                typeId: $"TYPE_{i:D3}",
                locationId: $"LOC_{i:D3}",
                location: $"Location {i}"
            ));
        }
        return sites;
    }

    public Site CreateSiteWithSpecificId(string referenceId, string name = "Test Site")
    {
        return new Site
        {
            ReferenceId = referenceId,
            Name = name,
            CompanyId = "COMPANY_123",
            TypeId = "TYPE_001",
            LocationId = "LOC_001",
            Location = "Test Location",
            PlatformType = "Windows",
            DataTemperature = "Hot",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public Site CreateSiteForCompany(string companyId, string name = null)
    {
        return CreateSite(
            name: name ?? $"Site for {companyId}",
            companyId: companyId
        );
    }

    public Site CreateSiteForType(string typeId, string name = null)
    {
        return CreateSite(
            name: name ?? $"Site for {typeId}",
            typeId: typeId
        );
    }

    public Site CreateSiteForLocation(string locationId, string location, string name = null)
    {
        return CreateSite(
            name: name ?? $"Site at {location}",
            locationId: locationId,
            location: location
        );
    }

    public Site CreateSiteWithPlatform(string platformType, string name = null)
    {
        return CreateSite(
            name: name ?? $"Site with {platformType}",
            platformType: platformType
        );
    }

    public Site CreateSiteWithDataTemperature(string dataTemperature, string name = null)
    {
        return CreateSite(
            name: name ?? $"Site with {dataTemperature} data",
            dataTemperature: dataTemperature
        );
    }

    public List<Site> CreateSitesWithStatus(int activeCount, int inactiveCount, string companyId = "COMPANY_123")
    {
        var sites = new List<Site>();

        for (int i = 1; i <= activeCount; i++)
        {
            sites.Add(CreateSite(
                name: $"Active Site {i}",
                companyId: companyId,
                isActive: true
            ));
        }

        for (int i = 1; i <= inactiveCount; i++)
        {
            sites.Add(CreateSite(
                name: $"Inactive Site {i}",
                companyId: companyId,
                isActive: false
            ));
        }

        return sites;
    }

    public List<Site> CreateSitesForTypes(List<string> typeIds, string companyId = "COMPANY_123")
    {
        var sites = new List<Site>();
        for (int i = 0; i < typeIds.Count; i++)
        {
            sites.Add(CreateSite(
                name: $"Site for {typeIds[i]}",
                companyId: companyId,
                typeId: typeIds[i]
            ));
        }
        return sites;
    }

    public List<Site> CreateSitesForLocations(List<string> locations, string companyId = "COMPANY_123")
    {
        var sites = new List<Site>();
        for (int i = 0; i < locations.Count; i++)
        {
            sites.Add(CreateSite(
                name: $"Site at {locations[i]}",
                companyId: companyId,
                locationId: $"LOC_{i:D3}",
                location: locations[i]
            ));
        }
        return sites;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
