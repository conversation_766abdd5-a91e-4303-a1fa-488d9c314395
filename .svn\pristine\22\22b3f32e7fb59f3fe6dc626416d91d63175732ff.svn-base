using ContinuityPatrol.Application.Features.DynamicSubDashboard.Events.Delete;

namespace ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Delete;

public class
    DeleteDynamicSubDashboardCommandHandler : IRequestHandler<DeleteDynamicSubDashboardCommand,
        DeleteDynamicSubDashboardResponse>
{
    private readonly IDynamicSubDashboardRepository _dynamicSubDashboardRepository;
    private readonly IPublisher _publisher;

    public DeleteDynamicSubDashboardCommandHandler(IDynamicSubDashboardRepository dynamicSubDashboardRepository,
        IPublisher publisher)
    {
        _dynamicSubDashboardRepository = dynamicSubDashboardRepository;

        _publisher = publisher;
    }

    public async Task<DeleteDynamicSubDashboardResponse> Handle(DeleteDynamicSubDashboardCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _dynamicSubDashboardRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.DynamicSubDashboard),
            new NotFoundException(nameof(Domain.Entities.DynamicSubDashboard), request.Id));

        eventToDelete.IsActive = false;

        await _dynamicSubDashboardRepository.UpdateAsync(eventToDelete);

        var response = new DeleteDynamicSubDashboardResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.DynamicSubDashboard), eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new DynamicSubDashboardDeletedEvent { Name = eventToDelete.Name }, cancellationToken);

        return response;
    }
}