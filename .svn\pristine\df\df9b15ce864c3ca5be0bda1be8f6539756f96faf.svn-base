﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.LicenseInfoModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetPaginatedList;

public class
    GetLicenseInfoPaginatedListQueryHandler : IRequestHandler<GetLicenseInfoPaginatedListQuery,
        PaginatedResult<LicenseInfoListVm>>
{
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly IMapper _mapper;

    public GetLicenseInfoPaginatedListQueryHandler(ILicenseInfoRepository licenseInfoRepository, IMapper mapper)
    {
        _licenseInfoRepository = licenseInfoRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<LicenseInfoListVm>> Handle(GetLicenseInfoPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new LicenseInfoFilterSpecification(request.SearchString);
       
        var queryable = request.Entity != null
            ?await _licenseInfoRepository.GetLicenseInfoEntityQueryable(request.Entity, request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder)
            :await _licenseInfoRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var licenseInfoList = _mapper.Map<PaginatedResult<LicenseInfoListVm>>(queryable);
        //var queryable = request.Entity != null
        //    ? _licenseInfoRepository.GetLicenseInfoEntityQueryable(request.Entity)
        //    : _licenseInfoRepository.GetPaginatedQuery();      

        //var licenseInfoList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<LicenseInfoListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return licenseInfoList;
    }
}