﻿.modal-body {
    max-height: calc(100vh - 79px);
    overflow-y: auto;
}

summary {
    padding: 2px 0px;
}

details {
    margin-left: 15px;
}

    details span {
/*        margin-left: 30px;
        padding: 2px 0px;*/
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 2;
    }

.ActionBuilder-Tree {
    height: calc(100vh - 175px);
    overflow-y: auto;
}

.Workflow-Tree {
    height: calc(100vh - 215px);
    overflow-y: auto;
}

.collapse-tools {
    position: absolute;
    right: 15px;
    width: 16rem !important;
    border: none;
}

.bd-content .bd-code-snippet {
    margin-bottom: 1rem;
}

.bd-clipboard,
.bd-edit {
    position: relative;
    display: none;
    float: right;
}

    .bd-clipboard + .highlight,
    .bd-edit + .highlight {
        margin-top: 0;
    }

.highlight {
    height: calc(100vh - 128px);
    overflow-y: auto;
    border-radius: 0.5rem;
}

.ui-sortable-handle {
    display: flex;
}
/*tamik*/
.form-builder-dialog.data-dialog {
    z-index: 999 !important
}


/*Form Builder & Action Builder Form Style*/
.formeo.formeo-editor .prop-inputs label {
    width: 215px;

}

.formeo input[type=checkbox], .formeo input[type=radio] {
    vertical-align: middle;
    margin-left: 4px;
}

.bd-code-snippet {
    line-height: 2;
    font-size: var(--bs-body-font-size-small);
}

.ActionWizard .steps {
    justify-content: center !important;
    display: flex !important;
}

    .ActionWizard .steps > ul {
        width: 60% !important;
    }

.ActionWizard .content {
    height: calc(100vh - 126px) !important;
}

.formeo.formeo-editor .formeo-stage {
    /*height: calc(100vh - 127px) !important;*/
    height: calc(100vh - 200px) !important;
    overflow-y: auto !important;
    padding-right: 3px !important;
    margin-right: 0px !important;
}

.field-control, .formeo-controls .field-control {
    width: 30.4%;
    height: 70px;
    float: left;
    margin: 5px 5px !important;
    box-shadow: 0 0.1rem 0.5rem rgba(0, 0, 0, 0.1);
}
}

.formeo-controls .control-group > li:last-child {
    border-radius: 0 0 0px 0px !important;
}

.field-control .control-icon {
    float: none !important;
    margin-right: auto !important;
    text-align: center !important;
    width: 20px !important;
    height: 20px !important;
    margin: auto;
}

.field-control button, .formeo-controls .field-control button {
    display: grid !important;
    justify-content: center;
    align-items:center;
}

.svg-icon {
    width: 16px !important;
    height: 16px !important;
    margin-bottom: 0px;
}

.formeo.formeo-editor .group-actions .svg-icon {
    width: 12px !important;
    height: 12px !important;
}

.formeo button:focus {
    border: 0px solid #66afe9 !important;
}

.field-control button:focus {
    box-shadow: inset 0 0 0 0px #66afe9;
}

.CompareAction-Scroll {
    height: calc(100vh - 225px);
    overflow-y: auto;
}

.action-btn-wrap svg.svg-icon.f-i-remove {
    padding: 2px;
}

.formeo.formeo-editor .field-edit .prop-control:last-child:first-child {
    border: none;
}

svg.svg-icon.f-i-remove {
    padding: 4px;
}

.formeo .f-input-group select {
    background-position: right 10px top 10px;
    background-size: 8px;
}

/*Form Builder & Action Builder*/