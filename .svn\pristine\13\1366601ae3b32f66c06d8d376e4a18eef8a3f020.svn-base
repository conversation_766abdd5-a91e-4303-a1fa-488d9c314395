namespace ContinuityPatrol.Domain.Entities;

public class BulkImportOperationGroup : AuditableEntity
{
    public string BulkImportOperationId { get; set; }
    public string InfraObjectName { get; set; }
    public string CompanyId { get; set; }
    [Column(TypeName = "NCLOB")] public string Properties { get; set; }
	public string ProgressStatus { get; set; }
    public string Status { get; set; }
    [Column(TypeName = "NCLOB")] public string ErrorMessage { get; set; }
	public int ConditionalOperation { get; set; }
	public string NodeId { get; set; }
		
}
