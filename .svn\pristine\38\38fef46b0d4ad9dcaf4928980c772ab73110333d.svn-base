﻿using ContinuityPatrol.Application.Features.Replication.Commands.Update;
using ContinuityPatrol.Application.Features.Replication.Events.LicenseInfoEvents.Update;
using ContinuityPatrol.Application.Features.Replication.Events.Update;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Replication.Commands
{
    public class UpdateReplicationTests
    {
        private readonly Mock<IReplicationRepository> _mockReplicationRepository;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly Mock<ISiteRepository> _mockSiteRepository;
        private readonly Mock<ISiteTypeRepository> _mockSiteTypeRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly UpdateReplicationCommandHandler _handler;

        public UpdateReplicationTests()
        {
            _mockReplicationRepository = new Mock<IReplicationRepository>();
            _mockPublisher = new Mock<IPublisher>();
            _mockSiteRepository = new Mock<ISiteRepository>();
            _mockSiteTypeRepository = new Mock<ISiteTypeRepository>();
            _mockMapper = new Mock<IMapper>();

            _handler = new UpdateReplicationCommandHandler(
                _mockMapper.Object,
                _mockReplicationRepository.Object,
                _mockPublisher.Object,
                _mockSiteRepository.Object,
                _mockSiteTypeRepository.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldSucceed_WhenReplicationIsValid()
        {
            var replicationId = Guid.NewGuid().ToString();
            var command = new UpdateReplicationCommand
            {
                Id = replicationId,
                LicenseId = "12345",
                LicenseKey = "TestLicenseKey",
                Type = "Perpetuuiti",
                Logo = "TestLogo"
            };

            var existingReplication = new Domain.Entities.Replication
            {
                ReferenceId = replicationId,
                Name = "TestReplication",
                SiteId = Guid.NewGuid().ToString(),
                Type = "Perpetuuiti",
                LicenseKey = "OldLicenseKey"
            };

            var site = new Domain.Entities.Site { TypeId = Guid.NewGuid().ToString() };
            var siteType = new Domain.Entities.SiteType { Category = "Primary" };

            _mockReplicationRepository
                .Setup(repo => repo.GetByReferenceIdAsync(replicationId))
                .ReturnsAsync(existingReplication);

            _mockMapper
                .Setup(mapper => mapper.Map(command, existingReplication, typeof(UpdateReplicationCommand), typeof(Domain.Entities.Replication)));

            _mockReplicationRepository
                .Setup(repo => repo.UpdateAsync(existingReplication))
                .Returns(ToString);

            _mockSiteRepository
                .Setup(repo => repo.GetByReferenceIdAsync(existingReplication.SiteId))
                .ReturnsAsync(site);

            _mockSiteTypeRepository
                .Setup(repo => repo.GetByReferenceIdAsync(site.TypeId))
                .ReturnsAsync(siteType);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(replicationId, result.ReplicationId);
            Assert.Equal($"Replication '{existingReplication.Name}' updated successfully.", result.Message);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<ReplicationUpdatedEvent>(), CancellationToken.None), Times.Once);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<ReplicationLicenseInfoUpdatedEvent>(), CancellationToken.None), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenReplicationDoesNotExist()
        {
            var replicationId = Guid.NewGuid().ToString();
            var command = new UpdateReplicationCommand { Id = replicationId };

            _mockReplicationRepository
                .Setup(repo => repo.GetByReferenceIdAsync(replicationId))
                .ReturnsAsync((Domain.Entities.Replication)null);

            await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(command, CancellationToken.None)
            );
        }

        [Fact]
        public async Task Handle_ShouldNotPublishLicenseInfoEvent_WhenTypeIsNotPerpetuuiti()
        {
            var replicationId = Guid.NewGuid().ToString();
            var command = new UpdateReplicationCommand
            {
                Id = replicationId,
                LicenseId = "NA",
                Type = "NonPerpetuuiti"
            };

            var existingReplication = new Domain.Entities.Replication
            {
                ReferenceId = replicationId,
                Name = "TestReplication",
                Type = "NonPerpetuuiti"
            };

            _mockReplicationRepository
                .Setup(repo => repo.GetByReferenceIdAsync(replicationId))
                .ReturnsAsync(existingReplication);

            _mockMapper
                .Setup(mapper => mapper.Map(command, existingReplication, typeof(UpdateReplicationCommand), typeof(Domain.Entities.Replication)));

            _mockReplicationRepository
                .Setup(repo => repo.UpdateAsync(existingReplication))
                .Returns(ToString);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(replicationId, result.ReplicationId);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<ReplicationUpdatedEvent>(), CancellationToken.None), Times.Once);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<ReplicationLicenseInfoUpdatedEvent>(), CancellationToken.None), Times.Never);
        }
    }
}
