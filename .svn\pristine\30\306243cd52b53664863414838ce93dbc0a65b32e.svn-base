﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.UpdateWorkflowProfilePassword;
using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.WorkflowProfileAuthentication;
using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetNames;
using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetPassword;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class WorkflowProfileController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<WorkflowProfileListVm>>> GetWorkflowProfiles()
    {
        Logger.LogDebug("Get All WorkflowProfiles");

        //return Ok(await Cache.GetOrAddAsync(
        //    ApplicationConstants.Cache.AllWorkflowProfileCacheKey + LoggedInUserService.CompanyId,
        //    () => Mediator.Send(new GetWorkflowProfileListQuery()), CacheExpiry));

        return Ok(await Mediator.Send(new GetWorkflowProfileListQuery()));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Orchestration.Create)]
    public async Task<ActionResult<CreateWorkflowProfileResponse>> CreateWorkflowProfile(
        [FromBody] CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        Logger.LogDebug($"Create WorkflowProfile '{createWorkflowProfileCommand.Name}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateWorkflowProfile), await Mediator.Send(createWorkflowProfileCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Orchestration.Edit)]
    public async Task<ActionResult<UpdateWorkflowProfileResponse>> UpdateWorkflowProfile(
        [FromBody] UpdateWorkflowProfileCommand updateWorkflowProfileCommand)
    {
        Logger.LogDebug($"Update WorkflowProfile '{updateWorkflowProfileCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateWorkflowProfileCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Orchestration.Delete)]
    public async Task<ActionResult<DeleteWorkflowProfileResponse>> DeleteWorkflowProfile(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "WorkflowProfile Id");

        Logger.LogDebug($"Delete WorkflowProfile Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteWorkflowProfileCommand { Id = id }));
    }

    [HttpGet("{id}", Name = "GetWorkflowProfile")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<WorkflowProfileDetailVm>> GetWorkflowProfileById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "WorkflowProfile Id");

        Logger.LogDebug($"Get WorkflowProfile Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetWorkflowProfileDetailQuery { Id = id }));
    }

    [HttpGet]
    [Route("names")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<WorkflowProfileNameVm>>> GetWorkflowProfileNames()
    {
        Logger.LogDebug("Get All WorkflowProfile Names");

        // return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllWorkflowProfileNameCacheKey, () => Mediator.Send(new GetWorkflowProfileNameQuery()), CacheExpiry));
        return Ok(await Mediator.Send(new GetWorkflowProfileNameQuery()));
    }


    [Route("name-exist")]
    [HttpGet]
    public async Task<ActionResult> IsWorkflowProfileNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "WorkflowProfile Name");

        Logger.LogDebug($"Check Name Exists Detail by WorkflowProfile Name '{name}' and id '{id}'");

        return Ok(await Mediator.Send(new GetWorkflowProfileNameUniqueQuery { ProfileName = name, ProfileId = id }));
    }
    [Route("password-exist")]
    [HttpGet]
    public async Task<ActionResult> IsWorkflowProfilePasswordExist(string workflowProfileId, string password)
    {
       Logger.LogDebug($"Check Password Exists Detail by WorkflowProfile Id '{workflowProfileId}' and Password '{password}'");

        return Ok(await Mediator.Send(new GetWorkflowProfilePasswordQuery { WorkflowProfileId = workflowProfileId, Password = password }));
    }
    [Route("paginated-list")]
    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<PaginatedResult<WorkflowProfileListVm>>> GetPaginatedWorkflowProfile([FromQuery] GetWorkflowProfilePaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in WorkflowProfile Paginated List");

        return Ok(await Mediator.Send(query));
    }
    [Route("authentication")]
    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<WorkflowProfileAuthenticationResponse>> WorkflowProfileAuthentication([FromQuery] WorkflowProfileAuthenticationCommand workflowProfileAuthenticationCommand)
    {
        Logger.LogDebug("Check Workflow Profile Authentication");

        return Ok(await Mediator.Send(workflowProfileAuthenticationCommand));
    }
    [Route("changepassword")]
    [HttpPut]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<UpdateWorkflowProfilePasswordResponse>> WorkflowProfileChangePassword([FromBody] UpdateWorkflowProfilePasswordCommand updateWorkflowProfilePasswordCommand)
    {
        Logger.LogDebug($"Update WorkflowProfile ChangePassword '{updateWorkflowProfilePasswordCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateWorkflowProfilePasswordCommand));
    }
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys =
        {
            ApplicationConstants.Cache.AllWorkflowProfileInfosCacheKey + LoggedInUserService.CompanyId,
            ApplicationConstants.Cache.AllWorkflowProfileInfosNameCacheKey
        };

        ClearCache(cacheKeys);
    }
}