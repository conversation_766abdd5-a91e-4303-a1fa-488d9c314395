﻿using ContinuityPatrol.Application.Features.SolutionHistory.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.SolutionHistory.Commands;

public class CreateSolutionHistoryTests : IClassFixture<SolutionHistoryFixture>
{
    private readonly SolutionHistoryFixture _solutionHistoryFixture;

    private readonly Mock<ISolutionHistoryRepository> _mockSolutionHistoryRepository;

    private readonly CreateSolutionHistoryCommandHandler _handler;

    public CreateSolutionHistoryTests(SolutionHistoryFixture solutionHistoryFixture)
    {
        _solutionHistoryFixture = solutionHistoryFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockSolutionHistoryRepository = SolutionHistoryRepositoryMocks.CreateSolutionHistoryRepository(_solutionHistoryFixture.SolutionHistories);

        _handler = new CreateSolutionHistoryCommandHandler(_solutionHistoryFixture.Mapper, _mockSolutionHistoryRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Should_IncreaseSolutionHistoryCount_When_AddValidSolutionHistory()
    {
        await _handler.Handle(_solutionHistoryFixture.CreateSolutionHistoryCommand, CancellationToken.None);

        var allCategories = await _mockSolutionHistoryRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_solutionHistoryFixture.SolutionHistories.Count);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulSolutionHistoryResponse_When_AddValidSolutionHistory()
    {
        var result = await _handler.Handle(_solutionHistoryFixture.CreateSolutionHistoryCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateSolutionHistoryResponse));

        result.SolutionHistoryId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_solutionHistoryFixture.CreateSolutionHistoryCommand, CancellationToken.None);

        _mockSolutionHistoryRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.SolutionHistory>()), Times.Once);
    }
}
