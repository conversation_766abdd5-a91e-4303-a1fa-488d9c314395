﻿using DevExpress.XtraReports.UI;
using ContinuityPatrol.Web.Areas.Dashboard.Controllers;
using System.Drawing;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using ContinuityPatrol.Domain.ViewModels.ServerModel;
using Newtonsoft.Json;
using DevExpress.Office.Utils;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;
using ContinuityPatrol.Shared.Services.Helper;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    public partial class ServerComponentReport : DevExpress.XtraReports.UI.XtraReport
    {
        public string username;
        private readonly ILogger<ServerController> _logger;
        bool IsNotNullOrEmpty(string value) => !string.IsNullOrEmpty(value);
        public string ReportGeneratedName;
        public ServerComponentReport(string data)
        {
            try
            {     
                _logger = ServerController._logger;
                var servercomponentreport = GetServerDetails(data);
                ReportGeneratedName = WebHelper.UserSession.LoginName;
                InitializeComponent();
                ClientCompanyLogo();
                this.DataSource = servercomponentreport;
                tableCell8.BeforePrint += tableCell_SerialNumber_BeforePrint;
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Server Component Report. The error message : " + ex.Message); throw; }
        }


        public List<ServerReportVm> GetServerDetails(string data)
        {
            try
            {
                var serverlist = JsonConvert.DeserializeObject<List<ServerListVm>>(data);
                var Serverlistreport = serverlist;
                var ServerReportDetails = new List<ServerReportVm>();
                foreach (var server in Serverlistreport)
                {
                    string conhost = string.Empty;
                    string ipAddress = string.Empty;
                    string authtype = string.Empty;
                    string sshuser = string.Empty;
                    string subauth = string.Empty;
                    string subtype=string.Empty;

                    var properties = new Dictionary<string, object>();
                    if (!string.IsNullOrEmpty(server.Properties))
                    {
                        properties = JsonConvert.DeserializeObject<Dictionary<string, object>>(server.Properties);
                        if (properties.ContainsKey("ConnectViaHostName"))
                        {
                            conhost = properties["ConnectViaHostName"]?.ToString();
                            if (conhost.ToLower() == "true")
                            {
                                ipAddress = properties["HostName"]?.ToString();

                            }
                            else
                            {
                                ipAddress = properties["IpAddress"]?.ToString();

                            }
                        }
                        
                        if (properties.ContainsKey("SubstituteAuthentication"))
                        {
                            subauth = properties["SubstituteAuthentication"]?.ToString();
                        }
                        if (properties.ContainsKey("SubstituteAuthenticationType"))
                        {
                            subtype = properties["SubstituteAuthenticationType"]?.ToString();
                        }
                        
                        if (properties.ContainsKey("AuthenticationType"))
                        {
                            authtype = properties["AuthenticationType"]?.ToString();
                            if (authtype == "SshPassword")
                            {
                                sshuser = properties["SSHUser"]?.ToString();
                            }
                            else if (authtype == "WMI")
                            {
                                sshuser = properties["WMIUser"]?.ToString();
                            }
                            else if (authtype == "PowerShell")
                            {
                                sshuser = properties["PowerShellUser"]?.ToString();
                            }
                            else
                            {
                                sshuser = "-";
                            }
                        }

                    }

                    var report = new ServerReportVm
                    {
                        Name = server.Name,
                        ServerType = server.ServerType,
                        IpAddress = ipAddress,
                        ConHost = conhost,
                        SubType = subtype,
                        SubAuth = subauth,
                        OSType = server.OSType,
                        AuthType = authtype,
                        SSHUser = sshuser,
                        Status = server.Status,
                        IsAttached = server.IsAttached.ToString(),
                        ExceptionMsg = server.ExceptionMessage
                       
                    };

                    ServerReportDetails.Add(report);
                }
                return ServerReportDetails;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while get parse data for the Server Component Report. The error message : " + ex.Message);
                throw;
            }
        }

        private void xrPageInfo1_BeforePrint(object sender, CancelEventArgs e)
        {

        }
        private int serialNumber = 1;

        private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
        {
            XRTableCell cell = (XRTableCell)sender;
            cell.Text = serialNumber.ToString();
            serialNumber++;
        }
        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                _username.Text = "Report Generated By: " + ReportGeneratedName.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Server Component Report's User name. The error message : " + ex.Message); throw; }
        }

        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Server Component Report's CP Version. The error message : " + ex.Message); throw; }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(ServerController.CompanyLogo) ? "NA" : ServerController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the customer logo in Server Component Report" + ex.Message.ToString());
            }
        }
    }
}
