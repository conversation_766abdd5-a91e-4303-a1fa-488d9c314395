﻿using Moq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using AutoMapper;
using ContinuityPatrol.Application.Features.DataSet.Commands.Create;
using ContinuityPatrol.Application.Features.DataSet.Commands.Update;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DataSetModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using MediatR;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetSchemaNameList;
using Microsoft.AspNetCore.Mvc.Rendering;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetRunQuery;
using ContinuityPatrol.Shared.Tests.Fakes;
using Microsoft.AspNetCore.Http;
using AutoFixture;
using ContinuityPatrol.Shared.Core.Wrapper;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class DataSetControllerShould
    {
        private readonly Mock<IPublisher> _publisherMock = new ();
        private readonly Mock<IMapper> _mapperMock = new();
        private readonly Mock<IDataProvider> _dataProviderMock = new();
        private readonly Mock<ILogger<DataSetController>> _loggerMock = new();
        private readonly Mock<IConfiguration> _configMock = new();
        private  DataSetController _controller;

        public DataSetControllerShould()
        {


            Initialize();

        }
        internal void Initialize()
        {
            _controller = new DataSetController(
                _publisherMock.Object,
                _loggerMock.Object,
                _dataProviderMock.Object,
                _mapperMock.Object,
                _configMock.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_Returns_View_With_Data()
        {
            // Arrange
            var dataSetView = new List<DataSetListVm>();
            var schemaNames = new List<SchemaNameListVm> {
              
            };

            
            _dataProviderMock.Setup(dp => dp.DataSet.GetDataSetList())
                .ReturnsAsync(dataSetView);
            _dataProviderMock.Setup(dp => dp.TableAccess.GetSchemaNames())
                .ReturnsAsync(schemaNames);

            // Act
            var result = await _controller.List() as ViewResult;
            var model = result?.Model as DataSetModel;

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(model);
            Assert.Equal(dataSetView, model.PaginatedDataSet);
            
        }
        
        [Fact]
        public async Task CreateOrUpdate_Creates_DataSet()
        {
            // Arrange
            var dataSetModel =new  AutoFixture.Fixture().Create<DataSetModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateDataSetCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mapperMock.Setup(m => m.Map<CreateDataSetCommand>(dataSetModel))
                .Returns(command);
            _dataProviderMock.Setup(dp => dp.DataSet.CreateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(dataSetModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task CreateOrUpdate_Updates_DataSet()
        {
            // Arrange
            var dataSetModel = new AutoFixture.Fixture().Create<DataSetModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateDataSetCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mapperMock.Setup(m => m.Map<UpdateDataSetCommand>(dataSetModel))
                .Returns(command);
            _dataProviderMock.Setup(dp => dp.DataSet.UpdateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(dataSetModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }
        
        [Fact]
        public void CreateOrUpdate_Handles_Exception()
        {
            // Arrange
            var dataSetModel = new AutoFixture.Fixture().Create<DataSetModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id","");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateDataSetCommand();

            _mapperMock.Setup(m => m.Map<CreateDataSetCommand>(dataSetModel))
                .Returns(command);
            _dataProviderMock.Setup(dp => dp.DataSet.CreateAsync(command))
                .ThrowsAsync(new Exception("Error"));

            // Act
            var result =  _controller.CreateOrUpdate(dataSetModel);

            // Assert
            Assert.NotNull(result);
            
            
        }

        [Fact]
        public async Task Delete_Calls_Delete_Method()
        {
            // Arrange
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };

            _dataProviderMock.Setup(dp => dp.DataSet.DeleteAsync(id))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task RunQuery_Returns_Json_With_Data()
        {
            
            var runQuery = "SELECT * FROM Table";
            string connectionString = string.Empty;
            var queryResult = new DataSetRunQueryVm();

            _configMock.Setup(c => c.GetSection("ConnectionStrings").GetSection("DBProvider").Value)
                .Returns("SomeDbProvider");
            //_configMock.Setup(c => c.GetConnectionString("Default")).Returns("SomeValue");

            _dataProviderMock.Setup(dp => dp.DataSet.RunQuery(runQuery))
                .ReturnsAsync(queryResult);

            var result = await _controller.RunQuery(runQuery) as JsonResult;
            var jsonResult = result?.Value as dynamic;

          
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task RunQuery_Handles_Exception()
        {
            
            var runQuery = "SELECT * FROM Table";

            //_configMock.Setup(c => c.GetSection("ConnectionStrings:DBProvider").Value)
            //    .Returns("SomeDbProvider");
            //_configMock.Setup(c => c.GetConnectionString("Default"))
            //    .Returns("SomeConnectionString");
            //_dataProviderMock.Setup(dp => dp.DataSet.RunQuery(runQuery))
            //    .ThrowsAsync(new Exception("Error"));

            
            var result = await _controller.RunQuery(runQuery) as JsonResult;
            var jsonResult = result?.Value as dynamic;

            
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetPagination_Returns_Json_With_Data()
        {
            // Arrange
            var query = new DataSetListVm();

            var paginatedQuery = new GetDataSetPaginatedListQuery();

            var paginatedList= new PaginatedResult<DataSetListVm>();

            _mapperMock.Setup(m => m.Map<GetDataSetPaginatedListQuery>(query))
                .Returns(paginatedQuery);
            _dataProviderMock.Setup(dp => dp.DataSet.GetDataSetPaginatedList(paginatedQuery))
                .ReturnsAsync(paginatedList);

            // Act
            var result = await _controller.GetPagination(paginatedQuery) as JsonResult;
            var jsonResult = result?.Value as dynamic;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(paginatedList, jsonResult);
        }

        //[Fact]
        //public async Task DataSetNameExist_Returns_Bool()
        //{
        //    // Arrange
        //    var dataSetName = "DataSetName";
        //    var id = "1";
        //    var exists = true;

        //    _dataProviderMock.Setup(dp => dp.DataSet.IsDataSetNameExist(dataSetName, id))
        //        .ReturnsAsync(exists);

        //    // Act
        //    var result = await _controller.DataSetNameExist(dataSetName, id);

        //    // Assert
        //    Assert.Equal(exists, result);
        //}

        [Fact]
        public void  GetDBDetail_Returns_Json_With_DBProvider()
        {
            
            var dbProvider = "SomeDbProvider";
            var encryptedDbProvider = CryptographyHelper.Encrypt(dbProvider);

            //_configMock.Setup(c => c.GetValue<string>("ConnectionStrings:DBProvider"))
            //    .Returns(encryptedDbProvider);

            // Act
            var result =  _controller.GetDbDetail() as JsonResult;
            var jsonResult = result?.Value as dynamic;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
        }

        [Fact]
        public void GetDBDetail_Handles_Exception()
        {
            
            //_configMock.Setup(c => c.GetValue<string>("ConnectionStrings:DBProvider"))
            //    .Throws(new Exception("Error"));

            
            var result =  _controller.GetDbDetail() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);


        }
    }
    
}
