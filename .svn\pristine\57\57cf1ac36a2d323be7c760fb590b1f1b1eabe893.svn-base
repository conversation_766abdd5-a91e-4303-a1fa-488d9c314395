using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class CyberComponentRepository : BaseRepository<CyberComponent>, ICyberComponentRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public CyberComponentRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public override async Task<IReadOnlyList<CyberComponent>> ListAllAsync()
    {
        var cyberComponent = base.ListAllAsync(x => x.IsActive);

        var component = MapCyberComponent(cyberComponent);

        return await component.ToListAsync();
    }
    public override Task<CyberComponent> GetByReferenceIdAsync(string id)
    {
        var cyberComponent = base.GetByReferenceIdAsync(id, x =>
                  x.ReferenceId.Equals(id));

        var component = MapCyberComponent(cyberComponent);

        return component.FirstOrDefaultAsync();
    }
    public override async Task<PaginatedResult<CyberComponent>> PaginatedListAllAsync(int pageNumber,int pageSize,Specification<CyberComponent> specification, string sortColumn, string sortOrder)
    {
        var component =await MapCyberComponent(Entities.Specify(specification).DescOrderById()).ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);

        return component;
    }
    public override IQueryable<CyberComponent> PaginatedListAllAsync()
    {
        var cyberComponent = base.ListAllAsync(x => x.IsActive);

        var component = MapCyberComponent(cyberComponent);

        return component.AsNoTracking().OrderByDescending(x => x.Id);
    }
    public async Task<List<CyberComponent>> GetCyberComponentBySiteId(string siteId)
    {
        var cyberComponent = base.FilterBy(x => x.SiteId.Equals(siteId));

        var component = MapCyberComponent(cyberComponent);

        return await component.ToListAsync();
        //return await base.FilterBy(x => x.SiteId == siteId).ToListAsync();
    }

    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.Name.Equals(name))
            : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }
    private IQueryable<CyberComponent> MapCyberComponent(IQueryable<CyberComponent> cyberComponents)
    {
       return cyberComponents.Select(x => new
        {
            Site = _dbContext.Sites.FirstOrDefault(s => s.ReferenceId.Equals(x.SiteId)),
            CyberComponent = x
        })
        .Select(res => new CyberComponent
        {
            Id = res.CyberComponent.Id,
            ReferenceId = res.CyberComponent.ReferenceId,
            Name = res.CyberComponent.Name,
            Description = res.CyberComponent.Description,
            SiteId = res.Site.ReferenceId ?? res.CyberComponent.SiteId,
            SiteName = res.Site.Name ?? res.CyberComponent.SiteName,
            Type = res.CyberComponent.Type,
            ServerType = res.CyberComponent.ServerType,
            ServerTypeId = res.CyberComponent.ServerTypeId,
            Properties = res.CyberComponent.Properties,
            Logo = res.CyberComponent.Logo,
            Status = res.CyberComponent.Status,
            IsActive = res.CyberComponent.IsActive,
            CreatedBy = res.CyberComponent.CreatedBy,
            CreatedDate = res.CyberComponent.CreatedDate,
            LastModifiedBy = res.CyberComponent.LastModifiedBy,
            LastModifiedDate = res.CyberComponent.LastModifiedDate
        });
    }
}
