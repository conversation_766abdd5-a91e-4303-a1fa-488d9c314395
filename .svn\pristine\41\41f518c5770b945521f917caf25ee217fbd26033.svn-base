﻿namespace ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Events.Update;

public class MSSQLMonitorStatusUpdatedEventHandler : INotificationHandler<MSSQLMonitorStatusUpdatedEvent>
{
    private readonly ILogger<MSSQLMonitorStatusUpdatedEventHandler> _logger;
    private readonly IMssqlMonitorStatusRepository _msSQLMonitorStatusRepository;

    public MSSQLMonitorStatusUpdatedEventHandler(ILogger<MSSQLMonitorStatusUpdatedEventHandler> logger,
        IMssqlMonitorStatusRepository msSqlMonitorStatusRepository)
    {
        _logger = logger;
        _msSQLMonitorStatusRepository = msSqlMonitorStatusRepository;
    }

    public async Task Handle(MSSQLMonitorStatusUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"MSSQLMonitorStatus '{updatedEvent.InfraObjectName}' Update successfully.");

        var msSqlMonitorServiceUpdate =
            await _msSQLMonitorStatusRepository.GetMssqlMonitorStatusByInfraObjectIdAsync(updatedEvent.InfraObjectId);

        msSqlMonitorServiceUpdate.Type = updatedEvent.Type;
        msSqlMonitorServiceUpdate.InfraObjectId = updatedEvent.InfraObjectId;
        msSqlMonitorServiceUpdate.InfraObjectName = updatedEvent.InfraObjectName;
        msSqlMonitorServiceUpdate.WorkflowId = updatedEvent.WorkflowId;
        msSqlMonitorServiceUpdate.WorkflowName = updatedEvent.WorkflowName;
        msSqlMonitorServiceUpdate.Properties = updatedEvent.Properties;
        msSqlMonitorServiceUpdate.ConfiguredRPO = updatedEvent.ConfiguredRPO;
        msSqlMonitorServiceUpdate.DataLagValue = updatedEvent.DataLagValue;

        await _msSQLMonitorStatusRepository.UpdateAsync(msSqlMonitorServiceUpdate);
    }
}