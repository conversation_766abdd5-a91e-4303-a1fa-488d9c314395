﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using Microsoft.EntityFrameworkCore;

namespace ContinuityPatrol.Persistence.Repositories;

public class DatalagImpactAvailabilityViewRepository:BaseRepository<DatalagImpactAvailabilityView>, IDatalagImpactAvailabilityViewRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public DatalagImpactAvailabilityViewRepository( ApplicationDbContext dbContext,ILoggedInUserService loggedInUserService):base(dbContext,loggedInUserService)
    {
        _loggedInUserService = loggedInUserService;
        _dbContext = dbContext;
                
    }

    public async Task<DatalagImpactAvailabilityView> GetByBusinessServiceId(string businessServiceId)
    {
        var result=await _dbContext.DatalagImpactAvailabilityViews.FirstOrDefaultAsync(x=>x.ReferenceId==businessServiceId);

        return result;
    }
}
