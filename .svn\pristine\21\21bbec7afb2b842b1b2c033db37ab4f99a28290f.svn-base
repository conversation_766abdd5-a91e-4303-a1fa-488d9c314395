﻿using ContinuityPatrol.Application.Features.Report.Queries.GetSchedulerWorkflowActionResultsReport;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ContinuityPatrol.Application.Contracts.Persistence
{
    public interface ISchedulerWorkflowActionResultsRepository
    {
        Task<List<SchedulerWorkflowActionResults>> GetSchedulerWorkflowActionResultListByWorkflowId(string workflowId, string infraReferenceId);
    }
}
