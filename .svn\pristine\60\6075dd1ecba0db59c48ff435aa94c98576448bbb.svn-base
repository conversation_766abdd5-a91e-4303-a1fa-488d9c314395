using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Delete;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetList;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class ApprovalMatrixControllerTests : IClassFixture<ApprovalMatrixFixture>
{
    private readonly ApprovalMatrixFixture _approvalMatrixFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly ApprovalMatrixController _controller;

    public ApprovalMatrixControllerTests(ApprovalMatrixFixture approvalMatrixFixture)
    {
        _approvalMatrixFixture = approvalMatrixFixture;

        var testBuilder = new ControllerTestBuilder<ApprovalMatrixController>();
        _controller = testBuilder.CreateController(
            _ => new ApprovalMatrixController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetApprovalMatrixList_ReturnsExpectedList()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        _controller.Cache.Remove(ApplicationConstants.Cache.AllApprovalMatrixCacheKey + companyId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_approvalMatrixFixture.ApprovalMatrixListVm);

        // Act
        var result = await _controller.GetApprovalMatrixList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var approvalMatrices = Assert.IsAssignableFrom<List<ApprovalMatrixListVm>>(okResult.Value);
        Assert.Equal(3, approvalMatrices.Count);
    }

    [Fact]
    public async Task GetApprovalMatrixList_ReturnsEmptyList_WhenNoMatricesExist()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        _controller.Cache.Remove(ApplicationConstants.Cache.AllApprovalMatrixCacheKey + companyId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixListQuery>(), default))
            .ReturnsAsync(new List<ApprovalMatrixListVm>());

        // Act
        var result = await _controller.GetApprovalMatrixList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var approvalMatrices = Assert.IsAssignableFrom<List<ApprovalMatrixListVm>>(okResult.Value);
        Assert.Empty(approvalMatrices);
    }

    [Fact]
    public async Task GetApprovalMatrixById_ReturnsApprovalMatrix_WhenIdIsValid()
    {
        // Arrange
        var approvalMatrixId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetApprovalMatrixDetailQuery>(q => q.Id == approvalMatrixId), default))
            .ReturnsAsync(_approvalMatrixFixture.ApprovalMatrixDetailVm);

        // Act
        var result = await _controller.GetApprovalMatrixById(approvalMatrixId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var approvalMatrix = Assert.IsType<ApprovalMatrixDetailVm>(okResult.Value);
        Assert.NotNull(approvalMatrix);
    }

    [Fact]
    public async Task GetApprovalMatrixById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetApprovalMatrixById("invalid-guid"));
    }

    [Fact]
    public async Task CreateApprovalMatrix_Returns201Created()
    {
        // Arrange
        var command = _approvalMatrixFixture.CreateApprovalMatrixCommand;
        var expectedMessage = $"ApprovalMatrix '{command.Name}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateApprovalMatrixResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateApprovalMatrix(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateApprovalMatrixResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateApprovalMatrix_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"ApprovalMatrix '{_approvalMatrixFixture.UpdateApprovalMatrixCommand.Name}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateApprovalMatrixCommand>(), default))
            .ReturnsAsync(new UpdateApprovalMatrixResponse
            {
                Message = expectedMessage,
                Id = _approvalMatrixFixture.UpdateApprovalMatrixCommand.Id
            });

        // Act
        var result = await _controller.UpdateApprovalMatrix(_approvalMatrixFixture.UpdateApprovalMatrixCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateApprovalMatrixResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteApprovalMatrix_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "ApprovalMatrix 'Test Matrix' has been deleted successfully!.";
        var approvalMatrixId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteApprovalMatrixCommand>(c => c.Id == approvalMatrixId), default))
            .ReturnsAsync(new DeleteApprovalMatrixResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteApprovalMatrix(approvalMatrixId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteApprovalMatrixResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetPaginatedApprovalMatrix_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetApprovalMatrixPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _approvalMatrixFixture.ApprovalMatrixListVm;
        var expectedPaginatedResult = PaginatedResult<ApprovalMatrixListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedApprovalMatrixList(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<ApprovalMatrixListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<ApprovalMatrixListVm>>(okResult.Value);

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    

    [Fact]
    public async Task GetApprovalMatrixList_CallsCorrectQuery()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        _controller.Cache.Remove(ApplicationConstants.Cache.AllApprovalMatrixCacheKey + companyId);

        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<ApprovalMatrixListVm>());

        // Act
        await _controller.GetApprovalMatrixList();

        // Assert
        Assert.True(queryExecuted);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task CreateApprovalMatrix_ValidatesDateRange()
    {
        // Arrange
        var command = new CreateApprovalMatrixCommand
        {
            Name = "Test Matrix",
            StartDate = DateTime.Now.AddDays(10), // Start date after end date
            EndDate = DateTime.Now.AddDays(5)
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("Start date must be before end date"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateApprovalMatrix(command));
    }

    [Fact]
    public async Task UpdateApprovalMatrix_ValidatesMatrixExists()
    {
        // Arrange
        var command = new UpdateApprovalMatrixCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Updated Matrix",
            Description = "Updated description"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("ApprovalMatrix not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateApprovalMatrix(command));
    }

    [Fact]
    public async Task GetPaginatedApprovalMatrix_HandlesFilteringByStatus()
    {
        // Arrange
        var query = new GetApprovalMatrixPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var activeMatrices = new List<ApprovalMatrixListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Active Matrix 1",
                status = "Active",
                BusinessServiceName = "Service 1",
                BusinessFunctionName = "Function 1"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Active Matrix 2",
                status = "Active",
                BusinessServiceName = "Service 2",
                BusinessFunctionName = "Function 2"
            }
        };

        var expectedPaginatedResult = PaginatedResult<ApprovalMatrixListVm>.Success(
            data: activeMatrices,
            count: activeMatrices.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedApprovalMatrixList(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<ApprovalMatrixListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<ApprovalMatrixListVm>>(okResult.Value);

        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, matrix => Assert.Equal("Active", matrix.status));
    }

    [Fact]
    public async Task CreateApprovalMatrix_HandlesComplexBusinessRules()
    {
        // Arrange
        var command = new CreateApprovalMatrixCommand
        {
            Name = "Enterprise Approval Matrix",
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Enterprise Security Service",
            BusinessFunctionId = Guid.NewGuid().ToString(),
            BusinessFunctionName = "Critical Security Operations",
            Description = "Complex approval matrix with multi-tier authorization and escalation procedures",
            Properties = "{\"escalationLevels\":3,\"timeoutHours\":24,\"requiresJustification\":true,\"auditRequired\":true}",
            StartDate = DateTime.Now,
            EndDate = DateTime.Now.AddYears(2)
        };

        var expectedMessage = $"ApprovalMatrix '{command.Name}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateApprovalMatrixResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateApprovalMatrix(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateApprovalMatrixResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteApprovalMatrix_VerifiesMatrixIsDeactivated()
    {
        // Arrange
        var matrixId = Guid.NewGuid().ToString();
        var expectedMessage = "ApprovalMatrix 'Test Matrix' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteApprovalMatrixCommand>(c => c.Id == matrixId), default))
            .ReturnsAsync(new DeleteApprovalMatrixResponse
            {
                IsActive = false, // Verify it's deactivated
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteApprovalMatrix(matrixId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteApprovalMatrixResponse>(okResult.Value);
        Assert.False(response.IsActive);
        Assert.Equal(expectedMessage, response.Message);
    }

    
    [Fact]
    public async Task GetApprovalMatrixList_HandlesLargeDataSets()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        _controller.Cache.Remove(ApplicationConstants.Cache.AllApprovalMatrixCacheKey + companyId);

        var largeDataSet = new List<ApprovalMatrixListVm>();
        for (int i = 0; i < 1000; i++)
        {
            largeDataSet.Add(new ApprovalMatrixListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = $"Matrix {i}",
                status = i % 2 == 0 ? "Active" : "Draft",
                BusinessServiceName = $"Service {i % 10}",
                BusinessFunctionName = $"Function {i % 5}"
            });
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(largeDataSet);

        // Act
        var result = await _controller.GetApprovalMatrixList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var approvalMatrices = Assert.IsAssignableFrom<List<ApprovalMatrixListVm>>(okResult.Value);
        Assert.Equal(1000, approvalMatrices.Count);
    }

    [Fact]
    public async Task IsApprovalMatrixNameExist_WithExistingName_ReturnsTrue()
    {
        // Arrange
        var matrixName = "Existing Matrix Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMatrixNameUniqueQuery>(q =>
                q.MatrixName == matrixName && q.Id == id), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsApprovalMatrixNameExist(matrixName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsApprovalMatrixNameExist_WithNewName_ReturnsFalse()
    {
        // Arrange
        var matrixName = "New Matrix Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMatrixNameUniqueQuery>(q =>
                q.MatrixName == matrixName && q.Id == id), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsApprovalMatrixNameExist(matrixName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsApprovalMatrixNameExist_WithSpecialCharactersInName_WorksCorrectly()
    {
        // Arrange
        var matrixName = "Matrix-Name_With@Special#Characters!";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMatrixNameUniqueQuery>(q =>
                q.MatrixName == matrixName && q.Id == id), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsApprovalMatrixNameExist(matrixName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsApprovalMatrixNameExist_WithEmptyName_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsApprovalMatrixNameExist("", null));

        Assert.Contains("Required input 'Approval Matrix Name' was empty", exception.Message);
    }

    [Fact]
    public async Task IsApprovalMatrixNameExist_WithWhitespaceName_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsApprovalMatrixNameExist("   ", null));

        Assert.Contains("Required input 'Approval Matrix Name' was empty", exception.Message);
    }

    [Fact]
    public async Task IsApprovalMatrixNameExist_WithNullName_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _controller.IsApprovalMatrixNameExist(null!, null));

        Assert.Contains("Value cannot be null. (Parameter 'Approval Matrix Name')", exception.Message);
    }

    [Fact]
    public async Task IsApprovalMatrixNameExist_WithNullId_WorksCorrectly()
    {
        // Arrange
        var matrixName = "Test Matrix Name";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMatrixNameUniqueQuery>(q =>
                q.MatrixName == matrixName && q.Id == null), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsApprovalMatrixNameExist(matrixName, null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsApprovalMatrixNameExist_WithLongName_WorksCorrectly()
    {
        // Arrange
        var longMatrixName = "This is a very long approval matrix name that might be used in enterprise environments with detailed descriptions and complex naming conventions";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMatrixNameUniqueQuery>(q =>
                q.MatrixName == longMatrixName && q.Id == id), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsApprovalMatrixNameExist(longMatrixName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }
}
