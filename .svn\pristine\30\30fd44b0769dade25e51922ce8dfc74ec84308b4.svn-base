﻿namespace ContinuityPatrol.Application.Features.SVCMssqlMonitorLog.Queries.GetDetail;

public class
    GetSVCMssqlMonitorLogDetailQueryHandler : IRequestHandler<GetSVCMssqlMonitorLogDetailQuery,
        SVCMssqlMonitorLogDetailVm>
{
    private readonly IMapper _mapper;
    private readonly ISVCMssqlMonitorLogRepository _svcMssqlMonitorLogRepository;

    public GetSVCMssqlMonitorLogDetailQueryHandler(ISVCMssqlMonitorLogRepository svcMssqlMonitorLogRepository,
        IMapper mapper)
    {
        _svcMssqlMonitorLogRepository = svcMssqlMonitorLogRepository;
        _mapper = mapper;
    }

    public async Task<SVCMssqlMonitorLogDetailVm> Handle(GetSVCMssqlMonitorLogDetailQuery request,
        CancellationToken cancellationToken)
    {
        var svcMssqlMonitorLogList = await _svcMssqlMonitorLogRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(svcMssqlMonitorLogList, nameof(SVCMssqlMonitorLog),
            new NotFoundException(nameof(SVCMssqlMonitorLog), request.Id));

        var mssqlAlwaysOnMonitorLogsDetailDto = _mapper.Map<SVCMssqlMonitorLogDetailVm>(svcMssqlMonitorLogList);

        return mssqlAlwaysOnMonitorLogsDetailDto ?? throw new NotFoundException(nameof(SVCMssqlMonitorLog), request.Id);
    }
}