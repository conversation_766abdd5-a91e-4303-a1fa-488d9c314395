﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Repositories;

public class DatabaseRepository : BaseRepository<Database>, IDatabaseRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IInfraObjectRepository _infraObjectRepository;

    public DatabaseRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService,
         IInfraObjectRepository infraObjectRepository) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
        _infraObjectRepository = infraObjectRepository;
    }

    public override async Task<IReadOnlyList<Database>> ListAllAsync()
    {
        var databases = base.QueryAll(database =>
            database.CompanyId.Equals(_loggedInUserService.CompanyId));

        var database = MapDatabases(databases);

        return _loggedInUserService.IsAllInfra
            ? await database.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(database);
    }


    public async Task<List<Database>> GetDatabaseByDatabaseTypeId(string databaseTypeId)
    {
        var database = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.DatabaseTypeId.Equals(databaseTypeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.DatabaseTypeId.Equals(databaseTypeId));

        var databases = MapDatabases(database);

        return _loggedInUserService.IsAllInfra
            ? await databases.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(databases).ToList();
    }

    public Task<List<Database>> GetByUserName(string userName)
    {
        var uerNameList = FilterByJObjectKeyEqualValue(database => database.Properties, "user", userName).ToList();

        var databaseList = _loggedInUserService.IsParent
            ? uerNameList
            : uerNameList.Where(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.Properties.Contains(userName)).ToList();

        return Task.FromResult(_loggedInUserService.IsAllInfra
            ? databaseList
            : GetAssignedBusinessServicesByDatabases(databaseList.AsQueryable()).ToList());
    }

    public async Task<List<Database>> GetByUserNameAndDatabaseType(string userName, string databaseTypeId)
    {
        var splitDatabaseTypeId = databaseTypeId.Split(',');

        var dataBase = await GetByUserName(userName);

        var result = dataBase.Where(x => splitDatabaseTypeId.Contains(x.DatabaseTypeId)).ToList();

        return result;
    }

    public override async Task<Database> GetByReferenceIdAsync(string id)
    {
        var database = base.GetByReferenceId(id,
            data => data.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                    data.ReferenceId.Equals(id));

        var databases = MapDatabases(database);

        return _loggedInUserService.IsAllInfra
            ? await databases.FirstOrDefaultAsync()
            : GetBusinessServiceByDatabase(databases.FirstOrDefault());

    }

    public async Task<List<Database>> GetByDatabaseIdsAsync(List<string> ids)
    {
        var databases = _loggedInUserService.IsParent
            ? base.FilterBy(x => ids.Contains(x.ReferenceId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && ids.Contains(x.ReferenceId));

        var databaseDto = MapDatabases(databases);

        return await databaseDto.ToListAsync();
    }

    public async Task<List<Database>> GetDatabaseByServerId(string serverId)
    {
        var serverIds = serverId.Split(",", StringSplitOptions.RemoveEmptyEntries);

        var database = _loggedInUserService.IsParent
            ? _dbContext.Databases.Where(x => x.IsActive && serverIds.Contains(x.ServerId))
            : _dbContext.Databases.Where(x => x.IsActive && serverIds.Contains(x.ServerId) && _loggedInUserService.CompanyId.Equals(x.CompanyId));

        var databases = MapDatabases(database);

        return _loggedInUserService.IsAllInfra
            ? await databases.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(databases).ToList();
    }

    public async Task<List<Database>> GetDatabaseNames()
    {
        var databases = base.QueryAll(database => database.CompanyId.Equals(_loggedInUserService.CompanyId) && database.IsActive);

        var database = _loggedInUserService.IsAllInfra
            ? await databases.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(databases);

        return database.Select(x => new Database { ReferenceId = x.ReferenceId, Name = x.Name }).ToList();
    }

    public override IQueryable<Database> GetPaginatedQuery()
    {
        var databases = base.QueryAll(database =>
            database.CompanyId.Equals(_loggedInUserService.CompanyId));

        var database = MapDatabases(databases);

        return _loggedInUserService.IsAllInfra
            ? database.AsNoTracking().OrderByDescending(x => x.Id)
            : GetPaginatedAssignedBusinessServicesByDatabases(database).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public async Task<bool> IsDatabaseNameUnique(string name)
    {
        return await _dbContext.Databases.AnyAsync(e => e.Name.Equals(name));
    }

    public async Task<bool> IsDatabaseNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? await _dbContext.Databases.AnyAsync(e => e.Name.Equals(name))
            : (await _dbContext.Databases.Where(e => e.Name.Equals(name)).ToListAsync()).Unique(id);
    }

    //public async Task<bool> IsDatabaseLicenseCountExitMaxLimit(string licenseId,string serverId,string siteType)
    //{
    //    var licenseDtl = await _licenseManagerRepository.GetLicenseDetailByIdAsync(licenseId) ??
    //                     throw new InvalidException("License GetList is null.");

    //    if (DateTime.TryParseExact(licenseDtl.ExpiryDate, "dd MMMM yyyy", CultureInfo.GetCultureInfo("en-IN"),
    //            DateTimeStyles.None, out var expiryDate))
    //        if (expiryDate < DateTime.UtcNow.Date)
    //            throw new InvalidOperationException("The license key has expired.");

    //    var siteTypeFirstString = SplitAndReplaceFirstOccurrence(siteType);

    //    var databaseCount = GetJsonProperties.GetLicenseJsonValue(licenseDtl.Properties, $"{siteTypeFirstString.ToLower()}databaseCount");

    //    return databaseCount > await GetDatabaseCountByLicenseKey(licenseId, serverId);
    //}

    public async Task<Dictionary<string, int>> GetDatabaseCountByLicenseIds(List<string> licenseId, List<string> siteIds)
    {
        var serverIds = await _dbContext.Servers
           .Active()
           .AsNoTracking()
           .Where(x => siteIds.Contains(x.SiteId))
           .Select(x => x.ReferenceId)
           .ToListAsync();

        var databaseCounts = await _dbContext.Databases
           .Active()
           .AsNoTracking()
           .Where(x => licenseId.Contains(x.LicenseId) && serverIds.Contains(x.ServerId))
           .GroupBy(x => x.LicenseId)
           .Select(group => new
           {
               LicenseId = group.Key,
               Count = group.Count()
           })
           .ToDictionaryAsync(x => x.LicenseId, x => x.Count);

        return databaseCounts;
    }


    public async Task<Dictionary<string, int>> GetDatabaseCountByLicenseIdsAndDatabaseTypeId(List<string> licenseId, List<string> siteIds,string databaseTypeId)
    {
        var serverIds = await _dbContext.Servers
            .Active()
            .AsNoTracking()
            .Where(x => siteIds.Contains(x.SiteId))
            .Select(x => x.ReferenceId)
            .ToListAsync();

        var databaseCounts = await _dbContext.Databases
            .Active()
            .AsNoTracking()
            .Where(x => licenseId.Contains(x.LicenseId) && serverIds.Contains(x.ServerId) && x.DatabaseTypeId.Equals(databaseTypeId))
            .GroupBy(x => x.LicenseId)
            .Select(group => new
            {
                LicenseId = group.Key,
                Count = group.Count()
            })
            .ToDictionaryAsync(x => x.LicenseId, x => x.Count);

        return databaseCounts;
    }


    public async Task<int> GetDatabaseCountByLicenseKey(string licenseId, List<string> siteIds)
    {
        var serverIds = await _dbContext.Servers
            .Active()
            .AsNoTracking()
            .Where(x => siteIds.Contains(x.SiteId))
            .Select(x => x.ReferenceId)
            .ToListAsync();

        return await _dbContext.Databases.Active()
            .CountAsync(x => x.LicenseId.Equals(licenseId) && serverIds.Contains(x.ServerId));
    }

    public async Task<int> GetCountByTypeAndLicenseKey(string licenseId, string type, List<string> siteIds)
    {
        var serverIds = await _dbContext.Servers
            .Active()
            .AsNoTracking()
            .Where(x => siteIds.Contains(x.SiteId))
            .Select(x => x.ReferenceId)
            .ToListAsync();

        return await _dbContext.Databases.Active()
            .CountAsync(x => x.LicenseId.Equals(licenseId) && x.DatabaseTypeId.Equals(type) && serverIds.Contains(x.ServerId));
    }

    public async Task<int> GetCountByTypeIdsAndLicenseId(string licenseId, List<string> typeIds, List<string> siteIds)
    {
        var serverIds = await _dbContext.Servers
            .Active()
            .AsNoTracking()
            .Where(x => siteIds.Contains(x.SiteId))
            .Select(x => x.ReferenceId)
            .ToListAsync();

        return await _dbContext.Databases.Active()
            .CountAsync(x => x.LicenseId.Equals(licenseId) && !typeIds.Contains(x.DatabaseTypeId) && serverIds.Contains(x.ServerId));
    }


    public async Task<List<Database>> GetDatabaseListByLicenseKey(string licenseId)
    {
        var databases = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.LicenseId.Equals(licenseId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.LicenseId.Equals(licenseId));

        var database = MapDatabases(databases);

        return _loggedInUserService.IsAllInfra
            ? await database.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(database).ToList();
    }

    public IQueryable<Database> GetDatabaseByType(string databaseTypeId)
    {
        var databases = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.DatabaseTypeId.Equals(databaseTypeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.DatabaseTypeId.Equals(databaseTypeId));

        var database = MapDatabases(databases);

        return _loggedInUserService.IsAllInfra
            ? database.AsNoTracking().OrderByDescending(x => x.Id)
            : GetPaginatedAssignedBusinessServicesByDatabases(database).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public async Task<List<Database>> GetDatabaseType(string type)
    {
        var databases = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Type.Equals(type))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.Type.Equals(type));

        var database = MapDatabases(databases);
        return _loggedInUserService.IsAllInfra
            ? await database.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(database).ToList();
    }

    public async Task<List<Database>> GetDatabaseByBusinessServiceId(string businessServiceId)
    {
        var database = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.BusinessServiceId.Equals(businessServiceId));

        var databases = MapDatabases(database);

        return _loggedInUserService.IsAllInfra
            ? await databases.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(databases).ToList();
    }
    public async Task<List<Database>> GetDatabaseByNodeId(string nodeId)
    {
        var database = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Properties.Equals(nodeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.Properties.Equals(nodeId));

        var databases = MapDatabases(database);

        return _loggedInUserService.IsAllInfra
            ? await databases.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(databases).ToList();
    }

    public override Task<Database> GetByIdAsync(int id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByIdAsync(id)
            : Task.FromResult(FindByFilterAsync(database =>
                    database.Id.Equals(id) && database.CompanyId.Equals(_loggedInUserService.CompanyId)).Result
                .SingleOrDefault());
    }

    public async Task<List<Database>> GetByDatabaseTypeIdAndFormVersion(string databaseTypeId, string formVersion)
    {
        var databases = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.DatabaseTypeId.Equals(databaseTypeId) && x.FormVersion.Equals(formVersion))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.DatabaseTypeId.Equals(databaseTypeId) && x.FormVersion.Equals(formVersion));

        var database = MapDatabases(databases);

        return _loggedInUserService.IsAllInfra
            ? await database.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(database).ToList();
    }

    private IReadOnlyList<Database> GetAssignedBusinessServicesByDatabases(IQueryable<Database> businessServices)
    {
        var databases = new List<Database>();

        foreach (var businessService in businessServices)
            if (AssignedEntity.AssignedBusinessServices.Count > 0)
                databases.AddRange(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
                                   where businessService.BusinessServiceId == assignedBusinessService.Id
                                   select businessService);

        var infraObjects = _infraObjectRepository.GetPaginatedQuery();

        //databases = databases.Where(server => infraObjects.Any(x =>
        //    server.ReferenceId.Equals(x.PRDatabaseId) ||
        //    server.ReferenceId.Equals(x.DRDatabaseId) ||
        //    server.ReferenceId.Equals(x.NearDRDatabaseId))).ToList();
       return infraObjects.Any() ? databases.Where(database => infraObjects.Any(x => x.DatabaseProperties.Contains(database.ReferenceId))).ToList()
            : databases;


    }

    private IQueryable<Database> GetPaginatedAssignedBusinessServicesByDatabases(IQueryable<Database> businessServices)
    {
        var assignedServiceIds = AssignedEntity.AssignedBusinessServices.Select(s => s.Id);

        businessServices = businessServices.Where(s => assignedServiceIds.Contains(s.BusinessServiceId));

        var infraObjects = _infraObjectRepository.GetPaginatedQuery();

        businessServices = businessServices.Where(database => infraObjects.Any(x => x.DatabaseProperties.Contains(database.ReferenceId)));

        //businessServices = businessServices.Where(server => infraObjects.Any(x =>
        //    server.ReferenceId.Equals(x.PRDatabaseId) ||
        //    server.ReferenceId.Equals(x.DRDatabaseId) ||
        //    server.ReferenceId.Equals(x.NearDRDatabaseId)));

        return businessServices;
    }

    private Database GetBusinessServiceByDatabase(Database businessService)
    {
        var services = AssignedEntity.AssignedBusinessServices
            .Where(assignedBusinessService => businessService?.BusinessServiceId == assignedBusinessService.Id)
            .Select(_ => businessService).SingleOrDefault();

        return services;
    }

    public IQueryable<Database> FilterByJObjectKeyEqualValue(Expression<Func<Database, string>> jsonSelector, string key, string searchInput)
    {
        var compiledSelector = jsonSelector.Compile();

        bool Filter(Database entity)
        {
            var json = compiledSelector(entity);
            if (string.IsNullOrEmpty(json))
                return false;
            var jObject = JObject.Parse(json);

            return jObject.Properties()
                          .Any(jp => jp.Name.Contains(key, StringComparison.OrdinalIgnoreCase)
                                     && jp.Value.ToString().Equals(searchInput, StringComparison.OrdinalIgnoreCase));
        }
        return Entities.AsEnumerable().Where(entity => Filter(entity)).AsQueryable().DescOrderById();

    }
    public IQueryable<Database> MapDatabases(IQueryable<Database> databases)
    {
        var mappedDatabases = databases.Select(data => new
        {
            Database = data,
            BusinessService = _dbContext.BusinessServices.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId == data.BusinessServiceId),
            ComponentType = _dbContext.ComponentTypes.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId == data.DatabaseTypeId),
            License = _dbContext.LicenseManagers.Active().AsNoTracking()
                .FirstOrDefault(y => y.ReferenceId.Equals(data.LicenseId)),
            Server = _dbContext.Servers.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId == data.ServerId)
        });

        var mappedDatabasesQuery = mappedDatabases.Select(result => new Database
        {
            Id = result.Database.Id,
            ReferenceId = result.Database.ReferenceId,
            Name = result.Database.Name,
            Type = result.Database.Type,
            ServerId = result.Server.ReferenceId,
            ServerName = result.Server.Name,
            DatabaseTypeId = result.ComponentType.ReferenceId,
            DatabaseType = result.ComponentType.ComponentName ?? result.Database.DatabaseType,
            BusinessServiceId = result.BusinessService.ReferenceId,
            BusinessServiceName = result.BusinessService.Name,
            CompanyId = result.Database.CompanyId,
            Properties = result.Database.Properties,
            ModeType = result.Database.ModeType,
            LicenseId = result.License.ReferenceId,
            LicenseKey = SecurityHelper.Decrypt(result.License.PoNumber),
            FormVersion = result.Database.FormVersion,
            Version = result.Database.Version,
            ExceptionMessage = result.Database.ExceptionMessage,
            IsActive = result.Database.IsActive,
            CreatedBy = result.Database.CreatedBy,
            CreatedDate = result.Database.CreatedDate,
            LastModifiedBy = result.Database.LastModifiedBy,
            LastModifiedDate = result.Database.LastModifiedDate,
            IsConnection = result.Database.IsConnection
        });

        return mappedDatabasesQuery;
    }

    public async Task<Database> GetByDatabaseName(string name)
    {
        return await base.FilterBy(x => x.Name.Equals(name)).FirstOrDefaultAsync();
    }
}