﻿using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class InfraObjectFilterSpecification : Specification<InfraObjectView>
{
    public InfraObjectFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            searchString = searchString.Replace("-", " -").Replace("-", "- ").Replace(" ", "").Trim();

            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("businessservice=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.BusinessServiceName.Replace(" ", "").Contains(stringItem.Replace("businessservice=",
                            "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("businessfunction=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.BusinessFunctionName.Replace(" ", "").Contains(stringItem.Replace("businessfunction=",
                            "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("replication=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.ReplicationCategoryType.Replace(" ", "").Contains(stringItem.Replace("replication=",
                            "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("activity=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.TypeName.Replace(" ", "").Contains(stringItem.Replace("activity=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("name=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Name.Replace(" ", "").Contains(stringItem.Replace("name=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p => p.Name.Replace(" ", "").Contains(searchString) ||
                                p.BusinessServiceName.Replace(" ", "").Contains(searchString) ||
                                p.BusinessFunctionName.Replace(" ", "").Contains(searchString) ||
                                p.ReplicationCategoryType.Replace(" ", "").Contains(searchString) ||
                                p.TypeName.Replace(" ", "").Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.Name != null;
        }
    }
}