{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "None",
      "Microsoft.AspNetCore.Hosting.Diagnostics": "None",
      "Microsoft.EntityFrameworkCore": "None"
    }
  },
  "ApiSettings": {
    "ApiUrl": "https://***********:1000/"
  },
  "SeqConfig": {
    "ServerUrl": "http://***********:5341",
    "ApiKey": "YNgDU0FtfvHkChDdO2xP",
    "Username": "eP9LNIYSBVwVV4WQ6MducnR//dnewZrRbWBEmjjVHEA=$H3r86NBjEQ3rGpB0mqTxQg8jzo5ghek6mHeq3N+a8vrZ",
    "Password": "drXltNHflna07X6TalHC08EBRH3HKFuTNroCm4LVD7k=$LZKU8c5Upf+0Pt1XGTkmgARhE3Ccf81YC+ClrkH8daXv55AT+Q==",
    "path": "C:\\CP\\Logs\\SeqLogs\\"
  },
  "SignalR": {
    "Url": "https://***********:7079/"
  },
  "ChatBotURL": {
    "Url": "https://***********:8083/"
  },
  "CP": {
    "Version": "6.0"
  },
  "UpdateApiVersion": {
    "Version": "6.0.0"
  },
 // "ConnectionStrings": {
    //MYSql
    //"DBProvider": "R23mWJv58siaEGPygviEUg==",
    //"Default": "962r3hdT5e/DE+at9Qmw+4M7MqRPrVl5wcvxHlqhMhNA5jD+7OJAPHJ3wlqHPEraPxR89YZAdzi6cFbQaI/Zk/7WBJOaCaLwFnp5uIBt78+MeF4cZrpMk5FNoYmSmG/4sWdA6mTCsyqQO01jv0aEFJWRNSM9G2SzzA8ne6sqfkGpO673MEACy8rfIaWCdSqWB9quEPbiVGvyk8BhWrr0wNUAkOZ6icfuNjPTjNMJqfdDsVhzYlb5EWtlGlNDe50y"

    //"DBProvider": "tvHNBMgwGtcsdLILBsKZ3Q==",
    //"Default": "fSpiur6LjJ0AhRlBGNX36omnWn6J/YpQr72NbuX6vK3VdZ53NE5+TrUC8XhoN2T+JJnSMo0FRZSs+AC8m/okjT20RXue5nE+/tXSKxBcKrQhgE1a/SbNnKa7Vd4yS3Zq4zbrj9bartnqV9gEyFQVa464PDjPIm7PEahlZnUxXoccTa0HsQLB8ay5wcvmHZci+VRV4fu19sCN86KhIZznUW8OwOx4NRNZtfP3Q0Ew53oNj2fdo42Lvhz00Twj+BlejwjfN/GyFvr2USDDtBh+7w=="
    //oracle
    //"DBProvider": "i0P07XcwQEcHoVOaemmQyA==",
    //"Default": "cYohHf3dwgSOW12GtBlRvxKwYxqlZ9TylR/SyOd33vrW9GjWJUXuW3IPDxcAdxHSQdJ+PU9FKEOXRVdlhZ4urH+EJlohlhgYw06erXWUZ+2QLj+aLFUyOdnDPwiGnF6+vcFLHFiTqMCJgeDpB016cMqKKRJaU3r6pFTmePYF3lPcBvvbt4Y+v9CNViynYRtEyU951NX/DWNOzrKbzGNS87C5vRqG8ETnm5H43DY3V90uTVWNLSkWd5n0GaJ6EuqEDEHkmE1KPs6T9QDWHegrRu+Yc4JAPjgcq4jKxp/dkVqP9Tk1cYVhSdowx9dfpxrHROoNe2srV3CQuSdRJOgjH/3Ak8h1MUDaDAiSz6KsZB0="
    //"DBProvider": "i0P07XcwQEcHoVOaemmQyA==",
    //"Default": "FZrsfKRn+02ueFIWyl6NtBIWiJ2UE6jvCiery/hqi8wBYwwcujPWPMxaJ5sx1+TZAlc1oUyc9tE1i6mnQvTgMsrtFdaMGBMO+S3KWkHY95uwWEq8oInqgsqoA9Yd384MEOJFzt+LAJhfotMfBUjJep0ZsGZ0Z+rvA+tGP+w76FOS3PkWGE6XRIPZrtM3PMe84w9eJWYg3eBqIXAM/r6dE1wzJfqkFJTN+rX0s5X0AxZNA271VUc2zcW6AV1i5oibw7kqG6PUsSEPPvFrI2joK/IxLJypmxuouJeIiPMDtLhUMghVMMuBGkAGRLxeMH4Jw1h5nObzWhq4cv8CxQ/fMkJeLORlU2NVrf3EVTrKEZ0="
 // },
  "ConnectionStrings": {
    "DBProvider": "905clI+aAqPTFEWo83NkxQ==",
    "Default": "5EfXJhfqdORYP69ZwSSjm9J7nXNGBygRJOI7CMc++bCicl789LrcfOsagjG8RDQbuIxSm1NZ69oPmmQMXjKurfwvwYnlCgiNhSmJtY4Ovcnzj3nBhqiFNspkT+5nyCg35ik4YaDMi/N8/bzltRgyUFTtxCcZa9aeJRIg5GFR2KioeUQO47BjNTa9pUifxd2bUjbtPffoLIosE3yFyB/d5PxUNcPZG3tq2yGYXXHNsOmbM1yG1avkU6J6YbaoNpi1PivQ/fs6rE9M9oD4XXhzzQ=="
  },
  "DatabaseSettings": {
    "CommandTimeoutSeconds": 180
  },
  //"IpRateLimiting": {
  //  "EnableEndpointRateLimiting": true,
  //  "StackBlockedRequests": false,
  //  "RealIpHeader": "X-Real-IP",
  //  "ClientIdHeader": "X-ClientId",
  //  "GeneralRules": [
  //    {
  //      "Endpoint": "*",
  //      "Period": "5m",
  //      "Limit": 100000
  //    }
  //  ]
  //},
  //"IpRateLimitPolicies": {
  //  "ClientIdRules": [
  //    {
  //      "Ip": "127.0.0.1",
  //      "Rules": [
  //        {
  //          "Endpoint": "*",
  //          "Period": "5m",
  //          "Limit": 100000
  //        }
  //      ]
  //    }
  //  ]
  //},
  "x-api-key": "pgH7QzFHJx4w46fI~5Uzi4RvtTwlEXp",
  "DataProvider": {
    "Default": "db"
  },
  "Policies": {
    "Default": "localhost"
  },
  "App": {
    "CorsOrigins": "https://localhost:7079/,https://localhost:7079,http://localhost:3001,http://subdomain1.localhost:3000,http://subdomain2.localhost:3000"
  },
  "CacheSettings": {
    "SlidingExpiration": 60
  },
  "JwtSettings": {
    "Key": "84322CFB66934ECC86D547C5CF4F2EFC",
    "Issuer": "http://localhost:8047",
    "Audience": "http://localhost:8047",
    "DurationInMinutes": 60
  },
  "Serilog": {
    "Using": [
      "Serilog.Sinks.File",
      "Serilog.Enrichers.ClientInfo"
    ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Error",
        "System": "Error",
        "Quartz": "Error"
      }
    },
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "C:\\CP\\CP_Web_log-.txt",
          "fileSizeLimitBytes": "524288000",
          "rollOnFileSizeLimit": true,
          "retainedFileCountLimit": null,
          "rollingInterval": "Day",
          "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} | Level: {Level:u3} | EventCode: {EventCode} | CorrID: {CorrID} | User: {User} | IP: {IP} | Server: {Server} | Session: {Session} | Message: {Message:lj}{NewLine}{Exception}"
          // "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{UserName}] [{UserRole}] [{Level:u3}] : [{ClientIp}/{MachineName}/{ThreadId}] - {Message}{NewLine}{Exception}"
        }
      }
      //{
      //  "Name": "Logger",
      //  "Args": {
      //    "configureLogger": {
      //      "Filter": [
      //        {
      //          "Name": "ByIncludingOnly",
      //          "Args": {
      //            "expression": "UserRole = 'SiteAdmin'"
      //          }
      //        }
      //      ],
      //      "WriteTo": [
      //        {
      //          "Name": "File",
      //          "Args": {
      //            "path": "C:\\CP\\Logs\\SiteAdminLogs-.txt",
      //            "fileSizeLimitBytes": "524288000",
      //            "rollOnFileSizeLimit": true,
      //            "retainedFileCountLimit": null,
      //            "rollingInterval": "Day",
      //            "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} | Level: {Level:u3} | EventCode: {EventCode} | CorrID: {CorrID} | User: {User} | IP: {IP} | Server: {Server} | Session: {Session} | Event: {EventName} | Message: {Message:lj}{NewLine}{Exception}"
      //            //"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{UserName}] [{UserRole}] [{Level:u3}] : [{ClientIp}/{MachineName}/{ThreadId}] - {Message}{NewLine}{Exception}"
      //          }
      //        }
      //      ]
      //    }
      //  }
      //}
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithThreadId",
      "WithCorrelationId",
      "WithClientIp"]
  },

  "RedisCacheUrl": "127.0.0.1:6379,abortConnect=false,connectTimeout=30000,responseTimeout=30000",
  "AllowedHosts": "*"
}