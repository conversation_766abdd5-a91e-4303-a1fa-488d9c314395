﻿using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.LoadBalancer.Commands;

public class UpdateLoadBalancerTests : IClassFixture<LoadBalancerFixture>
{
    private readonly LoadBalancerFixture _loadBalancerFixture;

    private readonly Mock<ILoadBalancerRepository> _mockLoadBalancerRepository;

    private readonly UpdateLoadBalancerCommandHandler _handler;

    public UpdateLoadBalancerTests(LoadBalancerFixture loadBalancerFixture)
    {
        _loadBalancerFixture = loadBalancerFixture;

        Mock<IPublisher> mockPublisher = new();

        //var mockWindowsService = new Mock<IWindowsService>();

        _mockLoadBalancerRepository = LoadBalancerRepositoryMocks.UpdateLoadBalancerRepository(_loadBalancerFixture.LoadBalancers);

        _handler = new UpdateLoadBalancerCommandHandler(_loadBalancerFixture.Mapper, _mockLoadBalancerRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidLoadBalancer_UpdateReferenceIdAsync_ToLoadBalancersRepo()
    {
        _loadBalancerFixture.UpdateLoadBalancerCommand.Id = _loadBalancerFixture.LoadBalancers[0].ReferenceId;

        var result = await _handler.Handle(_loadBalancerFixture.UpdateLoadBalancerCommand, CancellationToken.None);

        var loadBalancer = await _mockLoadBalancerRepository.Object.GetByReferenceIdAsync(result.NodeConfigurationId);

        Assert.Equal(_loadBalancerFixture.UpdateLoadBalancerCommand.Name, loadBalancer.Name);
    }

    [Fact]
    public async Task Handle_Return_ValidLoadBalancerResponse_WhenUpdate_LoadBalancer_InActiveState()
    {
        _loadBalancerFixture.UpdateLoadBalancerCommand.Id = _loadBalancerFixture.LoadBalancers[0].ReferenceId;

        var result = await _handler.Handle(_loadBalancerFixture.UpdateLoadBalancerCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateLoadBalancerResponse));

        result.NodeConfigurationId.ShouldBeGreaterThan(0.ToString());

        result.NodeConfigurationId.ShouldBe(_loadBalancerFixture.UpdateLoadBalancerCommand.Id);

        //result.Message.ShouldContain($" LoadBalancer '{_loadBalancerFixture.UpdateLoadBalancerCommand.Name}' has been updated successfully and Node is in '{_loadBalancerFixture.UpdateLoadBalancerCommand.Status}' state.");
    }

    [Fact]
    public async Task Handle_Return_ValidLoadBalancerResponse_WhenUpdate_LoadBalancer()
    {
        _loadBalancerFixture.UpdateLoadBalancerCommand.Id = _loadBalancerFixture.LoadBalancers[0].ReferenceId;

        var result = await _handler.Handle(_loadBalancerFixture.UpdateLoadBalancerCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateLoadBalancerResponse));

        result.NodeConfigurationId.ShouldBeGreaterThan(0.ToString());

        result.NodeConfigurationId.ShouldBe(_loadBalancerFixture.UpdateLoadBalancerCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidLoadBalancerId()
    {
        _loadBalancerFixture.UpdateLoadBalancerCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_loadBalancerFixture.UpdateLoadBalancerCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        _loadBalancerFixture.UpdateLoadBalancerCommand.Id = _loadBalancerFixture.LoadBalancers[0].ReferenceId;

        await _handler.Handle(_loadBalancerFixture.UpdateLoadBalancerCommand, CancellationToken.None);

        _mockLoadBalancerRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockLoadBalancerRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.LoadBalancer>()), Times.Once);
    }

}