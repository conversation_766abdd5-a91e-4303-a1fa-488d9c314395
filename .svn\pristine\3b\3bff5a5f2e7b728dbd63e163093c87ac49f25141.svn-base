﻿using ContinuityPatrol.Application.Features.WorkflowInfraObject.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetByInfraObjectIdAndActionType;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetInfraObjectByWorkflowId;
using ContinuityPatrol.Domain.ViewModels.WorkflowInfraObjectModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetWorkflowInfraObjectByInfraObjectId;

namespace ContinuityPatrol.Services.Api.Impl.Orchestration;

public class WorkflowInfraObjectService : BaseClient, IWorkflowInfraObjectService
{
    public WorkflowInfraObjectService(IConfiguration config, IAppCache cache, ILogger<WorkflowInfraObjectService> logger)
        : base(config, cache, logger)
    {
    }

    public async Task<List<WorkflowInfraObjectListVm>> GetWorkflowInfraObjectList()
    {
        var request = new RestRequest("api/v6/workflowinfraobject");

        return await GetFromCache<List<WorkflowInfraObjectListVm>>(request, "GetWorkflowInfraObjectList");
    }

    public async Task<bool> WorkflowInfraObjectByWorkflowIdExist(string workflowId)
    {
        var request = new RestRequest($"api/v6/workflowinfraobject/workflowid-exist?workflowId={workflowId}");

        return await Get<bool>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateWorkflowInfraObjectCommand createWorkflowInfraObject)
    {
        var request = new RestRequest("api/v6/workflowinfraobject/attach-infraobject", Method.Post);

        request.AddJsonBody(createWorkflowInfraObject);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(DeleteWorkflowInfraObjectCommand deleteWorkflowInfraObject)
    {
        var request = new RestRequest("api/v6/workflowinfraobject/de-attach-infraobject", Method.Delete);

        request.AddJsonBody(deleteWorkflowInfraObject);

        return await Delete<BaseResponse>(request);
    }

    public async Task<List<WorkflowInfraObjectByInfraObjectIdAndActionTypeVm>> GetWorkflowByInfraObjectIdAndActionType(string infraId, string actionType)
    {
        var request = new RestRequest($"api/v6/workflowinfraobject/infraobjectidandactiontype?infraObjectId={infraId}&actionType={actionType}");

        return await Get<List<WorkflowInfraObjectByInfraObjectIdAndActionTypeVm>>(request);
    }

    public async Task<List<GetInfraObjectByWorkflowIdVm>> GetInfraObjectByWorkflowId(string workflowId)
    {
        var request = new RestRequest($"api/v6/workflowinfraobject/workflowid?workflowId={workflowId}");

        return await Get<List<GetInfraObjectByWorkflowIdVm>>(request);
    }
    public async Task<List<WorkflowInfraObjectByInfraObjectIdVm>> GetWorkflowByInfraObjectId(string infraId)
    {
        var request = new RestRequest($"api/v6/workflowinfraobject/by/{infraId}");

        return await Get<List<WorkflowInfraObjectByInfraObjectIdVm>>(request);
    }

   
}