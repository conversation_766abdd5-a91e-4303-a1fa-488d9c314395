using AutoFixture;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class GroupPolicyRepositoryTests : IClassFixture<GroupPolicyFixture>, IDisposable
{
    private readonly GroupPolicyFixture _groupPolicyFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly GroupPolicyRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public GroupPolicyRepositoryTests(GroupPolicyFixture groupPolicyFixture)
    {
        _groupPolicyFixture = groupPolicyFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new GroupPolicyRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.GroupPolicies.RemoveRange(_dbContext.GroupPolicies);
        await _dbContext.SaveChangesAsync();
    }

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsGroupPolicy_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        
        var referenceId = Guid.NewGuid().ToString();
        var groupPolicy = new GroupPolicy
        {
            ReferenceId = referenceId,
            GroupName = "Test Group",
            CompanyId = "COMPANY_123",
            Type = "Security",
            Properties = "test properties",
            IsActive = true
        };

        await _dbContext.GroupPolicies.AddAsync(groupPolicy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal("Test Group", result.GroupName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsGroupPolicy_WhenIsParentFalseAndCompanyMatches()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        
        var referenceId = Guid.NewGuid().ToString();
        var groupPolicy = new GroupPolicy
        {
            ReferenceId = referenceId,
            GroupName = "Test Group",
            CompanyId = companyId,
            Type = "Security",
            Properties = "test properties",
            IsActive = true
        };

        await _dbContext.GroupPolicies.AddAsync(groupPolicy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal("Test Group", result.GroupName);
        Assert.Equal(companyId, result.CompanyId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenIsParentFalseAndCompanyDoesNotMatch()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("DIFFERENT_COMPANY");
        
        var referenceId = Guid.NewGuid().ToString();
        var groupPolicy = new GroupPolicy
        {
            ReferenceId = referenceId,
            GroupName = "Test Group",
            CompanyId = "COMPANY_123",
            Type = "Security",
            Properties = "test properties",
            IsActive = true
        };

        await _dbContext.GroupPolicies.AddAsync(groupPolicy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        var nonExistentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ReturnsFilteredGroupPolicies_WhenDataExists()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);

        var groupPolicies = new List<GroupPolicy>
        {
            new GroupPolicy 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                GroupName = "Group1", 
                CompanyId = companyId,
                Type = "Security",
                Properties = "props1",
                IsActive = true 
            },
            new GroupPolicy 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                GroupName = "Group2", 
                CompanyId = companyId,
                Type = "Network",
                Properties = "props2",
                IsActive = true 
            },
            new GroupPolicy 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                GroupName = "Group3", 
                CompanyId = "DIFFERENT_COMPANY",
                Type = "Security",
                Properties = "props3",
                IsActive = true 
            }
        };

        await _dbContext.GroupPolicies.AddRangeAsync(groupPolicies);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, result.Count); // Only policies from the user's company
        Assert.All(result, gp => Assert.Equal(companyId, gp.CompanyId));
        Assert.Contains(result, x => x.GroupName == "Group1");
        Assert.Contains(result, x => x.GroupName == "Group2");
        Assert.DoesNotContain(result, x => x.GroupName == "Group3");
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoDataForCompany()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");

        var groupPolicy = new GroupPolicy
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GroupName = "Group1",
            CompanyId = "DIFFERENT_COMPANY",
            Type = "Security",
            Properties = "props1",
            IsActive = true
        };

        await _dbContext.GroupPolicies.AddAsync(groupPolicy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsOnlySelectedFields()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);

        var groupPolicy = new GroupPolicy
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GroupName = "Test Group",
            CompanyId = companyId,
            Type = "Security",
            Properties = "test properties",
            IsActive = true,
            CreatedBy = "TestUser",
            CreatedDate = DateTime.Now
        };

        await _dbContext.GroupPolicies.AddAsync(groupPolicy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Single(result);
        var returnedPolicy = result.First();
        Assert.Equal(groupPolicy.Id, returnedPolicy.Id);
        Assert.Equal(groupPolicy.ReferenceId, returnedPolicy.ReferenceId);
        Assert.Equal(groupPolicy.GroupName, returnedPolicy.GroupName);
        Assert.Equal(groupPolicy.Type, returnedPolicy.Type);
        Assert.Equal(groupPolicy.Properties, returnedPolicy.Properties);
        Assert.Equal(groupPolicy.CompanyId, returnedPolicy.CompanyId);
        // These should be null/default as they're not selected in FilterRequiredGroupPolicy
        Assert.Null(returnedPolicy.CreatedBy);
        Assert.Equal(DateTime.MinValue, returnedPolicy.CreatedDate);
    }

    #endregion

    #region GetGroupPolicyNames Tests

    [Fact]
    public async Task IsGroupPolicyNameExist_ReturnsTrue_WhenKeyExists_WithDifferentId()
    {
        // Arrange
        await ClearDatabase();
        var groupname = "GroupName1";
        var existingId = Guid.NewGuid().ToString();
        var differentId = "";

        var Group = new GroupPolicy
        {
            ReferenceId = existingId,
           GroupName = groupname,
            IsActive = true
        };

        await _dbContext.GroupPolicies.AddAsync(Group);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsGroupPolicyNameExist(groupname, differentId);

        // Assert
        Assert.True(result);
    }
    [Fact]
    public async Task GetGroupPolicyNames_ReturnsOnlyReferenceIdAndGroupName()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);

        var groupPolicies = new List<GroupPolicy>
        {
            new GroupPolicy 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                GroupName = "Group1", 
                CompanyId = companyId,
                Type = "Security",
                Properties = "props1",
                IsActive = true 
            },
            new GroupPolicy 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                GroupName = "Group2", 
                CompanyId = companyId,
                Type = "Network",
                Properties = "props2",
                IsActive = true 
            },
            new GroupPolicy 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                GroupName = "Group3", 
                CompanyId = "DIFFERENT_COMPANY",
                Type = "Security",
                Properties = "props3",
                IsActive = true 
            }
        };

        await _dbContext.GroupPolicies.AddRangeAsync(groupPolicies);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetGroupPolicyNames();

        // Assert
        Assert.Equal(2, result.Count); // Only policies from the user's company
        
        var firstResult = result.First();
        Assert.NotNull(firstResult.ReferenceId);
        Assert.NotNull(firstResult.GroupName);
        // Other properties should be null/default as they're not selected
        Assert.Null(firstResult.Type);
        Assert.Null(firstResult.Properties);
        Assert.Equal(0, firstResult.Id);
    }

    [Fact]
    public async Task GetGroupPolicyNames_ReturnsEmpty_WhenNoDataForCompany()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");

        // Act
        var result = await _repository.GetGroupPolicyNames();

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsFilteredResults_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var groupPolicies = new List<GroupPolicy>
        {
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Security Group",
                CompanyId = "COMPANY_123",
                Type = "Security",
                Properties = "security props",
                IsActive = true
            },
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Network Group",
                CompanyId = "COMPANY_456",
                Type = "Network",
                Properties = "network props",
                IsActive = true
            }
        };

        await _dbContext.GroupPolicies.AddRangeAsync(groupPolicies);
        await _dbContext.SaveChangesAsync();

        var filterSpec = new GroupPolicyFilterSpecification("Security");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, filterSpec, "Id", "desc");

        // Assert
        Assert.Equal(1, result.Data.Count);
        Assert.Equal("Security Group", result.Data.First().GroupName);
        Assert.Equal(1, result.TotalCount);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsFilteredResults_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);

        var groupPolicies = new List<GroupPolicy>
        {
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Security Group",
                CompanyId = companyId,
                Type = "Security",
                Properties = "security props",
                IsActive = true
            },
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Network Group",
                CompanyId = "DIFFERENT_COMPANY",
                Type = "Network",
                Properties = "network props",
                IsActive = true
            }
        };

        await _dbContext.GroupPolicies.AddRangeAsync(groupPolicies);
        await _dbContext.SaveChangesAsync();

        var filterSpec = new GroupPolicyFilterSpecification("");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, filterSpec, "Id", "desc");

        // Assert
        Assert.Equal(1, result.Data.Count);
        Assert.Equal("Security Group", result.Data.First().GroupName);
        Assert.Equal(companyId, result.Data.First().CompanyId);
        Assert.Equal(1, result.TotalCount);
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public async Task GetPaginatedQuery_ReturnsOrderedQuery_ForUserCompany()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);

        var groupPolicies = new List<GroupPolicy>
        {
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Group1",
                CompanyId = companyId,
                Type = "Security",
                IsActive = true
            },
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Group2",
                CompanyId = companyId,
                Type = "Network",
                IsActive = true
            },
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Group3",
                CompanyId = "DIFFERENT_COMPANY",
                Type = "Security",
                IsActive = true
            }
        };

        await _dbContext.GroupPolicies.AddRangeAsync(groupPolicies);
        await _dbContext.SaveChangesAsync();

        // Act
        var query = _repository.GetPaginatedQuery();
        var result = query.ToList();

        // Assert
        Assert.Equal(2, result.Count); // Only policies from the user's company
        Assert.All(result, gp => Assert.Equal(companyId, gp.CompanyId));
        // Should be ordered by Id descending
        Assert.True(result[0].Id > result[1].Id);
    }

    #endregion

    #region IsGroupPolicyNameUnique Tests

    [Fact]
    public async Task IsGroupPolicyNameUnique_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();
        var groupName = "Unique Group";
        var groupPolicy = new GroupPolicy
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GroupName = groupName,
            CompanyId = "COMPANY_123",
            Type = "Security",
            IsActive = true
        };

        await _dbContext.GroupPolicies.AddAsync(groupPolicy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsGroupPolicyNameUnique(groupName);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsGroupPolicyNameUnique_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "NonExistent Group";

        // Act
        var result = await _repository.IsGroupPolicyNameUnique(nonExistentName);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsGroupPolicyNameUnique_IsCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var groupName = "Test Group";
        var groupPolicy = new GroupPolicy
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GroupName = groupName,
            CompanyId = "COMPANY_123",
            Type = "Security",
            IsActive = true
        };

        await _dbContext.GroupPolicies.AddAsync(groupPolicy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsGroupPolicyNameUnique("TEST GROUP");

        // Assert
        Assert.False(result); // Should not match due to case sensitivity
    }

    #endregion

    #region GetType Tests

    [Fact]
    public async Task GetType_ReturnsMatchingGroupPolicies_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var groupPolicies = new List<GroupPolicy>
        {
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Security Group 1",
                CompanyId = "COMPANY_123",
                Type = "Security Policy",
                Properties = "props1",
                IsActive = true
            },
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Security Group 2",
                CompanyId = "COMPANY_456",
                Type = "Security Policy",
                Properties = "props2",
                IsActive = true
            },
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Network Group",
                CompanyId = "COMPANY_123",
                Type = "Network Policy",
                Properties = "props3",
                IsActive = true
            }
        };

        await _dbContext.GroupPolicies.AddRangeAsync(groupPolicies);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetType("security policy");

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, gp => Assert.Equal("Security Policy", gp.Type));
    }

    [Fact]
    public async Task GetType_ReturnsMatchingGroupPolicies_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);

        var groupPolicies = new List<GroupPolicy>
        {
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Security Group 1",
                CompanyId = companyId,
                Type = "Security Policy",
                Properties = "props1",
                IsActive = true
            },
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Security Group 2",
                CompanyId = "DIFFERENT_COMPANY",
                Type = "Security Policy",
                Properties = "props2",
                IsActive = true
            },
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Network Group",
                CompanyId = companyId,
                Type = "Network Policy",
                Properties = "props3",
                IsActive = true
            }
        };

        await _dbContext.GroupPolicies.AddRangeAsync(groupPolicies);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetType("security policy");

        // Assert
        Assert.Single(result);
        Assert.Equal("Security Policy", result.First().Type);
        Assert.Equal(companyId, result.First().CompanyId);
    }

    [Fact]
    public async Task GetType_IgnoresSpacesAndCase()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var groupPolicy = new GroupPolicy
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GroupName = "Test Group",
            CompanyId = "COMPANY_123",
            Type = "Security Policy",
            Properties = "props",
            IsActive = true
        };

        await _dbContext.GroupPolicies.AddAsync(groupPolicy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetType("SECURITY POLICY");

        // Assert
        Assert.Single(result);
        Assert.Equal("Security Policy", result.First().Type);
    }

    [Fact]
    public async Task GetType_ReturnsEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var groupPolicy = new GroupPolicy
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GroupName = "Test Group",
            CompanyId = "COMPANY_123",
            Type = "Security Policy",
            Properties = "props",
            IsActive = true
        };

        await _dbContext.GroupPolicies.AddAsync(groupPolicy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetType("NonExistent Type");

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetGroupPolicyByLoadBalancerId Tests

    [Fact]
    public async Task GetGroupPolicyByLoadBalancerId_ReturnsMatchingGroupPolicies_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var loadBalancerId = "LB_123";
        var groupPolicies = new List<GroupPolicy>
        {
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Group 1",
                CompanyId = "COMPANY_123",
                Type = "Security",
                Properties = $"{{\"loadBalancerId\":\"{loadBalancerId}\",\"other\":\"value\"}}",
                IsActive = true
            },
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Group 2",
                CompanyId = "COMPANY_456",
                Type = "Network",
                Properties = $"{{\"loadBalancerId\":\"{loadBalancerId}\",\"config\":\"test\"}}",
                IsActive = true
            },
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Group 3",
                CompanyId = "COMPANY_123",
                Type = "Security",
                Properties = "{\"loadBalancerId\":\"DIFFERENT_LB\",\"other\":\"value\"}",
                IsActive = true
            }
        };

        await _dbContext.GroupPolicies.AddRangeAsync(groupPolicies);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetGroupPolicyByLoadBalancerId(loadBalancerId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, gp => Assert.Contains(loadBalancerId, gp.Properties));
    }

    [Fact]
    public async Task GetGroupPolicyByLoadBalancerId_ReturnsMatchingGroupPolicies_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);

        var loadBalancerId = "LB_123";
        var groupPolicies = new List<GroupPolicy>
        {
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Group 1",
                CompanyId = companyId,
                Type = "Security",
                Properties = $"{{\"loadBalancerId\":\"{loadBalancerId}\",\"other\":\"value\"}}",
                IsActive = true
            },
            new GroupPolicy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                GroupName = "Group 2",
                CompanyId = "DIFFERENT_COMPANY",
                Type = "Network",
                Properties = $"{{\"loadBalancerId\":\"{loadBalancerId}\",\"config\":\"test\"}}",
                IsActive = true
            }
        };

        await _dbContext.GroupPolicies.AddRangeAsync(groupPolicies);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetGroupPolicyByLoadBalancerId(loadBalancerId);

        // Assert
        Assert.Single(result);
        Assert.Contains(loadBalancerId, result.First().Properties);
        Assert.Equal(companyId, result.First().CompanyId);
    }

    [Fact]
    public async Task GetGroupPolicyByLoadBalancerId_ReturnsEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var groupPolicy = new GroupPolicy
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GroupName = "Test Group",
            CompanyId = "COMPANY_123",
            Type = "Security",
            Properties = "{\"loadBalancerId\":\"DIFFERENT_LB\",\"other\":\"value\"}",
            IsActive = true
        };

        await _dbContext.GroupPolicies.AddAsync(groupPolicy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetGroupPolicyByLoadBalancerId("NON_EXISTENT_LB");

        // Assert
        Assert.Empty(result);
    }

    #endregion
}
