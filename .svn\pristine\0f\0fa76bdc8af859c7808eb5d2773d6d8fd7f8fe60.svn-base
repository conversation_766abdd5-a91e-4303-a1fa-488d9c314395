﻿@model ContinuityPatrol.Domain.ViewModels.DatabaseModel.DatabaseViewModel;
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<link href="~/lib/jquery.steps/jquery.steps.css" rel="stylesheet" />
<link href="~/css/wizard.css" rel="stylesheet" />
<link href="~/lib/formeo/formeo.min.css" rel="stylesheet" />

<style>
    .f-checkbox {
        display: flex;
        align-items: center;
    }

    .wizard > .content > .body label {
        margin-bottom: -1px;
    }
</style>

@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-database"></i><span>Database</span></h6>
            <form class="d-flex">
                <span class="input-group-text form-label mb-0 me-2" for="basic-url">Database Type</span>
                <div class="form-group mb-0">
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-database-type"></i></span>
                        <div class="input-group border-0 me-2" style="width: 250px !important;">
                            <select class="form-select" id="selectType"></select>
                        </div>
                    </div>
                </div>
                <div class="input-group mx-2 w-100">
                    <input type="search" id="searchInputDatabase" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="name=" id="nameFltrDB">
                                        <label class="form-check-label" for="nameFltrDB">
                                            Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="sid=" id="dynamicFltrDB">
                                        <label class="form-check-label" for="dynamicFltrDB">
                                            <span id="dynamicLabel">Oracle SID</span>
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="type=" id="typeFltrDB">
                                        <label class="form-check-label" for="typeFltrDB">
                                            Type
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="version=" id="versionFltrDB">
                                        <label class="form-check-label" for="versionFltrDB">
                                            Version
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="mode=" id="modeTypeFltrDB">
                                        <label class="form-check-label" for="modeTypeFltrDB">
                                            Status
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="form-group-lg">
                    <div class="input-group" style="width: 150px;">
                        <span class="input-group-text"><i class="cp-activity-type"></i></span>
                        <select class="form-select" id="SolutionType" data-placeholder="Select Report Type">
                            <option value="">Select</option>
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                        </select>
                    </div>
                    <div>
                        <span id="SolutionType_Error" style="width: 150px;"></span>
                    </div>
                </div>

                <button id="BtnDBDownload" type="button" title="Download" class="btn btn-primary btn-sm ms-2 me-2">
                    <i class="cp-download"></i>
                </button>

                <button type="button" id="buttonTestConnectionDB" class="btn btn-primary text-nowrap me-2 py-1 px-2 d-none">
                    <i class="cp-test-connection" title="Test Connection" role="button"></i>
                </button>

                <button type="button" class="btn btn-sm btn-primary rounded-1 me-2" title="Refresh" id="btnRefreshDatabase">
                    <i class="cp-refresh"></i>
                </button>

                <div class="btn-group">
                    <button type="button" class="btn btn-primary btn-sm create-model" id="btnCreateDatabase" data-bs-toggle="modal" data-bs-target="#CreateModal">
                        <i class="cp-add me-1"></i>Create
                    </button>
                    <button type="button" class="btn btn-primary btn-sm rounded-end dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="cp-down-arrow fs-7"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li id="saveAsDatabasesData"><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#saveasModal">Save As</a></li>
                        <li id="cloneDatabaseData"><a class="dropdown-item">Clone</a></li>
                    </ul>
                </div>
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="dataTableListsDatabase" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th"> Sr. No.</th>
                        <th>Name</th>
                        <th id="dynamicHeader">Database Type</th>
                        <th>Type</th>
                        <th>Version</th>
                        <th id="dynamicHeaderTwo">Port Number</th>
                        <th>Status</th>
                        <th class="Action-th py-0">
                            <div class="d-flex align-items-center py-1">
                                <i id="testConnectionAllDB" class="bg-white btn-outline-secondary p-1 cp-test-connection me-1"
                                   test-status="off" title="Test Connection" role="button"></i>
                                Action
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!--Modal Error-->
<div class="modal fade" id="ErrorModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-md modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-error-message"></i><span>Error Message</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <div class="list-group list-group-flush Profile-Select">
                    <div class="d-grid">
                        <span id="databaseException" style="display: block; word-break: break-all;">
                        </span>
                        <div class="text-center">
                            <span id="databaseExceptionNA">
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="configurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>

<div id="configurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>

<!--Modal Create-->
<div class="modal fade " id="CreateModal" data-bs-backdrop="static" aria-labelledby="configureModalLabel" aria-hidden="true">
    <partial name="Configuration" />
</div>

<!--Modal Delete-->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="Delete" />
</div>

<!--Version Restore-->
<div class="modal fade" data-bs-backdrop="static" id="RestoreDBModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="~/img/isomatric/confirmation.svg" alt="confirmation Img" />
            </div>
            <div class="modal-body text-center pt-0">
                <h4>Are you sure?</h4>
                <p class="d-inline-block align-bottom gap-2 ">
                    You want to continue <span class="font-weight-bolder align-bottom text-truncate text-primary d-inline-block" style="max-width:100px" id="databaseVersionName"></span>
                    data with new version <span class="font-weight-bolder align-bottom text-truncate text-primary d-inline-block" style="max-width:100px" id="newVersion"></span> ?
                </p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" id="cancelDBFormRestore" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-primary btn-sm" id="confirmDBFormRestore">Yes</button>
                <button type="submit" class="btn btn-primary btn-sm" id="confirmDBFormRestoreAll">Yes to all</button>
            </div>
        </div>
    </div>
</div>

<!--Save As Modal -->
<div class="modal fade" id="saveasModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="saveasModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-save"></i><span>Save As</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createFormDBSaveAs">
                    <div>
                        <div class="row row-cols-2">
                            <div>
                                <div class="form-group">
                                    <div class="form-label">Clone Name</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-copy-option"></i></span>
                                        <select data-placeholder="Select Clone Name"
                                                id="cloneDatabaseName"
                                                class="form-select-modal">
                                            <option value="">Select Clone Name</option>
                                        </select>
                                    </div>
                                    <span id="cloneDatabaseNameError"></span>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <div class="form-label">Database Type</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-database-type"></i></span>
                                        <select data-placeholder="Select Database Type"
                                                id="cloneDatabaseType"
                                                class="form-select-modal">
                                            <option value="">Select Database Type</option>
                                        </select>
                                    </div>
                                    <span id="cloneDatabaseTypeError"></span>
                                </div>
                            </div>
                        </div>
                        <div class="row row-cols-5">
                            <div>
                                <div class="form-group">
                                    <div class="form-label">Name</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-name"></i></span>
                                        <input type="text"
                                               class="form-control"
                                               autocomplete="off"
                                               id="cloneDatabaseNameInput"
                                               placeholder="Enter Name" />
                                    </div>
                                    <span id="cloneDatabaseNameInputError"></span>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <div class="form-label">Server Type</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-production-server-name"></i></span>
                                        <select data-placeholder="Select Server Type"
                                                class="form-select-modal"
                                                id="cloneServerType">
                                            <option value="">Select Server Type</option>
                                        </select>
                                    </div>
                                    <span id="cloneServerTypeError"></span>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <div class="form-label">License Key</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-license-key"></i></span>
                                        <select data-placeholder="Select License Key"
                                                class="form-select-modal"
                                                id="cloneDatabaseLicenseKey">
                                            <option value="">Select License Key</option>
                                        </select>
                                    </div>
                                    <span id="cloneLicenseKeyError"></span>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <div class="form-label">Oracle SID</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-oracle"></i></span>
                                        <input type="text"
                                               class="form-control"
                                               autocomplete="off"
                                               id="cloneOracleID"
                                               placeholder="Enter Oracle SID" />
                                    </div>
                                    <span id="cloneOracleIDError"></span>
                                </div>
                            </div>
                            <div>
                                <div class="d-flex align-items-center gap-2">
                                    <div class="form-group w-100">
                                        <div class="form-label">Instance Name</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-instance-name"></i></span>
                                            <input type="text"
                                                   class="form-control"
                                                   autocomplete="off"
                                                   id="cloneInstanceName"
                                                   placeholder="Enter Instance Name" />
                                        </div>
                                        <span id="cloneInstanceNameError"></span>
                                    </div>
                                    <div>
                                        <i role="button" id="addSaveAsDatabase" title="Add" class="cp-circle-plus fs-5 text-primary"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="height:300px;overflow:auto">
                        <table class="table table-hover" style="width:100% !important" id="cloneTable">
                            <thead class="position-sticky top-0 z-3">
                                <tr>
                                    <th class="SrNo_th">Sr.No</th>
                                    <th>Name</th>
                                    <th>Database Type</th>
                                    <th>Server Type</th>
                                    <th>License Key</th>
                                    <th>Oracle SID</th>
                                    <th>Instance Name</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody id="cloneDataTable">
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <div class="modal-footer justify-content-between">
                <div class="d-flex align-items-center gap-3">
                    <span class="fw-semibold fs-7">Total<span class="mx-2 py-1 px-2 rounded-circle bg-primary-subtle text-primary" id="totalCloneDatabase">0</span></span>
                    <span class="fw-semibold fs-7">Completed<span class="mx-2 py-1 px-2 rounded-circle bg-success-subtle text-success" id="successCloneDatabase">0</span></span>
                    <span class="fw-semibold fs-7">Error<span class="mx-2 py-1 px-2 rounded-circle bg-danger-subtle text-danger" id="failureCloneDatabase">0</span></span>
                </div>
                <div>
                    <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="databaseSaveAs">Save All</button>
                </div>

            </div>
        </div>
    </div>
</div>

<!--Modal Delete SaveAs Table Row-->
<div class="modal fade" data-bs-backdrop="static" id="DeleteModalSaveAs" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="/img/isomatric/delete.png" />
            </div>
            <div class="modal-body text-center pt-0">
                <h4>Are you sure?</h4>
                <p class="d-flex align-items-center justify-content-center gap-1">
                    You want to delete the
                    <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px"
                          id="deleteCloneDatabaseData">
                    </span> data?
                </p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" id="cancelDelete" data-bs-dismiss="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="deleteSaveAsRow" data-bs-dismiss="modal">Yes</button>
            </div>
        </div>
    </div>
</div>

<!--Clone Modal-->
<div class="modal fade" data-bs-backdrop="static" id="cloneModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-m modal-dialog-centered">
        <div class="modal-content">
            <form>
                <div class="modal-header p-3">
                    <h6 class="page_title">
                        <i class="cp-database"></i>
                        <span>Clone Database</span>
                    </h6>
                    <button type="button" class="btn-close" title="Close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body pt-0">
                    <div class="form-group">
                        <div class="form-label">Database Name</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-name"></i></span>
                            <select data-placeholder="Select Database Name"
                                    class="form-select-modal"
                                    id="cloneDatabase">
                                <option></option>
                            </select>
                        </div>
                        <span id="cloneDatabaseError"></span>
                    </div>
                    <div class="form-group">
                        <div class="form-label">Clone Database Name</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-name"></i></span>
                            <input maxlength="100" id="inputCloneDatabase" type="text" class="form-control"
                                   placeholder="Enter Clone Database Name" autocomplete="off" />
                        </div>
                        <span id="inputCloneDatabaseError"></span>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary">
                        <i class="cp-note me-1"></i>Note: All fields are mandatory
                        except optional
                    </small>
                    <div class="gap-2 d-flex ms-auto">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                        <button type="button" class="btn btn-primary btn-sm" id="cloneDatabaseButton">Clone</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>


<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/common/wizard.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.js"></script>
<script src="~/lib/formeo/formeo.min.js"></script>

<script src="~/js/configuration/infra components/commonfunctions.js"></script>
<script src="~/js/configuration/infra components/database/databaseformbuilder.js"></script>
<script src="~/js/configuration/infra components/database/databasefunctions.js"></script>
<script src="~/js/configuration/infra components/database/database.js"></script>
<script src="~/js/configuration/infra components/database/databasesaveas.js"></script>
<script src="~/js/configuration/infra components/form builder/formbuildervalidationonchange.js"></script>
<script src="~/js/configuration/infra components/form builder/formbuildervalidation.js"></script>
<script src="~/js/configuration/infra components/form builder/formbuildercommonfunctions.js"></script>
