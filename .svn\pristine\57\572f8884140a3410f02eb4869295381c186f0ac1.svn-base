﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetLogDataByGroupId;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Infrastructure.Hubs;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperationGroup.Queries
{
    public class GetWorkflowOperationGroupLogByGroupIdQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISeqService> _mockSeqService;
        private readonly Mock<ILogger<GetLogByGroupIdQueryHandler>> _mockLogger;
        private readonly Mock<IJobScheduler> _mockJobScheduler;
        private readonly GetLogByGroupIdQueryHandler _handler;

        public GetWorkflowOperationGroupLogByGroupIdQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSeqService = new Mock<ISeqService>();
            _mockLogger = new Mock<ILogger<GetLogByGroupIdQueryHandler>>();
            _mockJobScheduler = new Mock<IJobScheduler>();
            _handler = new GetLogByGroupIdQueryHandler(_mockMapper.Object, _mockSeqService.Object, _mockLogger.Object, _mockJobScheduler.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedLogs_WhenLogsExist()
        {
            var request = new GetLogByGroupIdQuery { GroupId = "test-group-id"};

            var seqLogs = new List<LogHubVm>
            {
               new LogHubVm { WorkflowOperationGroupId = Guid.NewGuid().ToString(), Message = "Test log message" },
               new LogHubVm { WorkflowOperationGroupId = Guid.NewGuid().ToString(), Message = "Another log message" }
            };
            var mappedLogs = new List<GetLogByGroupIdVm>
            {
                new GetLogByGroupIdVm { Timestamp = "1", Message = "Test log message" },
                new GetLogByGroupIdVm { Timestamp = "2", Message = "Another log message" }
            };
            _mockSeqService.Setup(s => s.GetSeqLogsByGroupIdAsync(It.IsAny<string>(), false,false))
                .ReturnsAsync(seqLogs);

            _mockMapper.Setup(m => m.Map<List<GetLogByGroupIdVm>>(It.IsAny<List<LogHubVm>>()))
                .Returns(mappedLogs);

            _mockJobScheduler.Setup(js => js.ScheduleSeqServiceJob(request.GroupId, It.IsAny<Dictionary<string, string>>()))
                .Returns(Task.CompletedTask);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal(mappedLogs, result);
            _mockSeqService.Verify(s => s.GetSeqLogsByGroupIdAsync(request.GroupId, false, false), Times.Once);
            _mockJobScheduler.Verify(js => js.ScheduleSeqServiceJob(request.GroupId, It.IsAny<Dictionary<string, string>>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoLogsExist()
        {
            var request = new GetLogByGroupIdQuery { GroupId = "test-group-id" };

            _mockSeqService.Setup(s => s.GetSeqLogsByGroupIdAsync(It.IsAny<string>(), false, false))
                .ReturnsAsync(new List<LogHubVm>());

            _mockJobScheduler.Setup(js => js.ScheduleSeqServiceJob(request.GroupId, It.IsAny<Dictionary<string, string>>()))
                .Returns(Task.CompletedTask);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);
            _mockSeqService.Verify(s => s.GetSeqLogsByGroupIdAsync(request.GroupId, false, false), Times.Once);
            _mockJobScheduler.Verify(js => js.ScheduleSeqServiceJob(request.GroupId, It.IsAny<Dictionary<string, string>>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldLogErrorAndRetryOnException()
        {
            var request = new GetLogByGroupIdQuery { GroupId = "test-group-id" };

            var exception = new Exception("Test exception");

            _mockSeqService.SetupSequence(s => s.GetSeqLogsByGroupIdAsync(It.IsAny<string>(), false, false))
                .ThrowsAsync(exception)
                .ReturnsAsync(new List<LogHubVm>
                {
                    new LogHubVm { WorkflowOperationGroupId = "Guid.NewGuid().ToString()", Message = "Test log message" }
                });
            var mappedLogs = new List<GetLogByGroupIdVm>
            {
               new GetLogByGroupIdVm { Timestamp = "1", Message = "Log after retry" }
            };
            _mockMapper.Setup(m => m.Map<List<GetLogByGroupIdVm>>(It.IsAny<List<LogHubVm>>()))
                .Returns(mappedLogs);

            _mockJobScheduler.Setup(js => js.ScheduleSeqServiceJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
                .Returns(Task.CompletedTask);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(mappedLogs, result);
            _mockLogger.Verify(log => log.Log(It.Is<LogLevel>(level => level == LogLevel.Error), It.IsAny<string>(), It.IsAny<object[]>()), Times.Once);
            _mockSeqService.Verify(s => s.GetSeqLogsByGroupIdAsync(It.IsAny<string>(), false, false), Times.Exactly(2));
            _mockJobScheduler.Verify(js => js.ScheduleSeqServiceJob(request.GroupId, It.IsAny<Dictionary<string, string>>()), Times.Exactly(2));
        }
    }
}