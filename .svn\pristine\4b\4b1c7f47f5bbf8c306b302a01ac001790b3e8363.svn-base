namespace ContinuityPatrol.Application.Features.SiteLocation.Commands.Update;

public class UpdateSiteLocationCommandValidator : AbstractValidator<UpdateSiteLocationCommand>
{
    private readonly ISiteLocationRepository _siteLocationRepository;

    public UpdateSiteLocationCommandValidator(ISiteLocationRepository siteLocationRepository)
    {
        _siteLocationRepository = siteLocationRepository;

        RuleFor(p => p.City)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters");

        RuleFor(p => p.CityAscii)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters");

        RuleFor(p => p.Country)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters");

        RuleFor(p => p.Lat)
            .NotEmpty().WithMessage("Latitude is required")
            .Matches(@"^-?\d*\.?\d*$")
            .WithMessage("Please enter a valid Latitude")
            .NotNull()
            .WithMessage("Latitude should not be null");

        RuleFor(p => p.Lng)
            .NotEmpty().WithMessage("Longitude is required")
            .Matches(@"^-?\d*\.?\d*$")
            .WithMessage("Please enter a valid Longitude")
            .NotNull()
            .WithMessage("Longitude should not be null");


        RuleFor(e => e)
            .MustAsync(SiteLocationNameUnique)
            .WithMessage("A same city already exists");

        RuleFor(y => y)
            .NotNull()
            .MustAsync(VerifyGuid)
            .WithMessage("Id is invalid");
    }

    private async Task<bool> SiteLocationNameUnique(UpdateSiteLocationCommand e, CancellationToken token)
    {
        return !await _siteLocationRepository.IsNameExist(e.City, e.Id);
    }

    private Task<bool> VerifyGuid(UpdateSiteLocationCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.Id, "SiteLocation Id");

        return Task.FromResult(true);
    }
}