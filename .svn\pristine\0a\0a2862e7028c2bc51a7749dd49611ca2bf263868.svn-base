﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/dashboard.css" rel="stylesheet" />

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title"><i class="cp-monitoring"></i><span>MSSQL NLS Detail Monitoring :<span id="infraName"></span></span></h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>
    </div>
    <div class="monitor_pages">
        <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row g-2 mt-0">
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title">Component Monitor</div>
                    <div class="card-body pt-0 p-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Component Monitor</th>
                                    <th class="text-primary">Primary</th>
                                    <th class="text-info dynamicSite-header">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-server me-1"></i>Server Name</td>
                                    <td class="text-truncate" id="PR_Server_Name"></td>
                                    <td class="text-truncate" id="DR_Server_Name"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-ip-address me-1"></i>IP Address/Host Name</td>
                                    <td class="text-truncate" id="PR_Server_IpAddress"></td>
                                    <td class="text-truncate" id="DR_Server_IpAddress"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-database me-1"></i>Database Name</td>
                                    <td class="text-truncate" id="PR_Database_Name"></td>
                                    <td class="text-truncate" id="DR_Database_Name"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-drill-action-type me-1"></i>Last Generated Transaction Log</td>
                                    <td class="text-truncate" id="PR_Last_Backup_Transaction_Log"></td>
                                    <td class="text-truncate" id="">--</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-drill-action-type me-1"></i>Last Applied Transaction Log</td>
                                    <td class="text-truncate" id="">--</td>
                                    <td class="text-truncate" id="DR_Last_Restored_Transaction_Log"></td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center">
                        <div id="Solution_Diagram" class="w-100 h-100"></div>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title">SQL Log Shipping Health</div>
                    <div class="card-body pt-0 p-2" style="height:calc(50vh - 300px);overflow:auto">
                        <table class="table mb-0" style="table-layout:fixed;">
                            <thead class="position-sticky top-0 z-3">
                                <tr>
                                    <th>MSSQL<span class="version"></span> health</th>
                                    <th class="text-primary">Primary</th>
                                    <th class="text-info dynamicSite-header">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-database-unique me-1"></i>MSSQL<span class="version"></span> Server Instance</td>
                                    <td class="text-truncate" id="PR_MSSQL_Server_Instance_Name"></td>
                                    <td class="text-truncate" id="DR_MSSQL_Server_Instance_Name"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-mysql-data me-1"></i>MSSQL<span class="version"></span> Database</td>
                                    <td class="text-truncate" id="PR_Database"></td>
                                    <td class="text-truncate" id="DR_Database"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-database-success me-1"></i>MSSQL<span class="version"></span> Database State</td>
                                    <td class="text-truncate" id="PR_Database_State"></td>
                                    <td class="text-truncate" id="DR_Database_State"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="MSSQL2019 Database Recovery Model"><i class="text-secondary cp-roate-settings me-1"></i>MSSQL<span class="version"></span> Database Recovery Model</td>
                                    <td class="text-truncate" id="PR_Database_Recovery_Model"></td>
                                    <td class="text-truncate" id="DR_Database_Recovery_Model"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-transaction me-1"></i>Transaction Log Shipping State</td>
                                    <td class="text-truncate" id="PR_Transaction_Log_Shipping_State"></td>
                                    <td class="text-truncate" id="DR_Transaction_Log_Shipping_State"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-apply-finish-time me-1"></i>Database Access Restrict Status</td>
                                    <td class="text-truncate" id="PR_Database_Access_Mode_Description"></td>
                                    <td class="text-truncate" id="DR_Database_Access_Mode_Description"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-server-times me-1"></i>Backup Job Status </td>
                                    <td class="text-truncate" id="PR_Backup_Job_Status"></td>
                                    <td class="text-truncate" id="DR_Backup_Job_Status"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-backup_data me-1"></i>Backup Job Execution Status</td>
                                    <td class="text-truncate" id="PR_Backup_Job_Execution_Status"></td>
                                    <td class="text-truncate" id="DR_Backup_Job_Execution_Status"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-last-copied-transaction me-1"></i>Copy Job Status</td>
                                    <td class="text-truncate" id="PR_Copy_Job_State"></td>
                                    <td class="text-truncate" id="DR_Copy_Job_State"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-file-copy-job-execution me-1"></i>Copy Job Execution Status</td>
                                    <td class="text-truncate" id="PR_Copy_Job_Execution_Status"></td>
                                    <td class="text-truncate" id="DR_Copy_Job_Execution_Status"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-time me-1"></i>Restore Job Status</td>
                                    <td class="text-truncate" id="PR_Restore_Job_State"></td>
                                    <td class="text-truncate" id="DR_Restore_Job_State"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-time me-1"></i>Restore Job Execution Status</td>
                                    <td class="text-truncate" id="PR_Restore_Job_Execution_Status"></td>
                                    <td class="text-truncate" id="DR_Restore_Job_Execution_Status"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-database-updatability me-1"></i>Database Updateability</td>
                                    <td class="text-truncate" id="PR_Database_Updateability_State"></td>
                                    <td class="text-truncate" id="DR_Database_Updateability_State"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-apply-finish-time me-1"></i>SQLNative<span class="version"></span> Server Edition</td>
                                    <td class="text-truncate" id="PR_MSSQL_Server_Edition"></td>
                                    <td class="text-truncate" id="DR_MSSQL_Server_Edition"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-mysql-data me-1"></i>SQLNative <span class="version"></span> Server Release</td>
                                    <td class="text-truncate" id="PR_MSSQL_Server_Version"></td>
                                    <td class="text-truncate" id="DR_MSSQL_Server_Version"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-datas me-1"></i>Database Size (in MB)</td>
                                    <td class="text-truncate" id="PR_Database_Size"></td>
                                    <td class="text-truncate" id="DR_Database_Size"></td>
                                </tr>


                            </tbody>

                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">

                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title">SQL Log Shipping Status</div>
                    <div class="card-body pt-0 p-2">
                        <table style="table-layout:fixed" class="table mb-0">

                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate">SQL Log Shipping Name</td>
                                    <th>Status</th>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-last-backup me-1"></i>Last Backup Transaction Log</td>
                                    <td class="text-truncate" id="PR_Last_Backup_Transaction_Log_Dup"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-download me-1"></i>LSN of Last Backup Log Name</td>
                                    <td class="text-truncate" id="PRLastLSN_backup"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-last-copied-transaction me-1"></i>Last backup Date & Time</td>
                                    <td class="text-truncate" id="PRlast_backup_date"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-file-calender me-1"></i>Last Copied Transaction Log</td>
                                    <td class="text-truncate" id="DR_Last_Copied_Transaction_Log"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary  cp-last-copied-transaction me-1"></i>LSN of Last Copied Log Name</td>
                                    <td class="text-truncate" id="LastLSN_copied"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-file-calender me-1"></i>Last Copied Date & Time</td>
                                    <td class="text-truncate" id="last_copied_date"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-time me-1"></i>Last Restored Transaction Log</td>
                                    <td class="text-truncate" id="Last_Restored_Transaction_Log"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-control-file-type me-1"></i>LSN of Last Restored Log Name</td>
                                    <td class="text-truncate" id="LastLSN_restored"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-time me-1"></i>Last Restore Date & Time</td>
                                    <td class="text-truncate" id="last_restored_date"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">

                <div class="card Card_Design_None" id="mssqlserver">
                    <div class="card-header card-title">Services</div>
                    <div class="card-body pt-0 p-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th title="Service / Process / Workflow Name">Service / Process / Workflow Name</th>
                                    <th class="text-primary">Server IP/HostName </th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="mssqlserverbody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @* <div class="col-6 d-grid">

            <div class="card Card_Design_None">
            <div class="card-header card-title">Replication Monitor</div>
            <div class="card-body pt-0 p-2">
            <table style="table-layout:fixed" class="table mb-0">
            <thead>
            <tr>
            <th>Replication Monitor</th>
            <th>Status</th>
            </tr>
            </thead>
            <tbody>
            <tr>
            <td class="fw-semibold text-truncate"><i class="text-secondary cp-replication-on me-1"></i>Replication Type</td>
            <td class="text-truncate" id="ReplicationType"></td>
            </tr>
            <tr>
            <td class="fw-semibold text-truncate"><i class="text-secondary cp-last-copied-transaction me-1"></i>LSN of Last Backup Log</td>
            <td class="text-truncate" id="PRLastLSN_backup"></td>
            </tr>
            <tr>
            <td class="fw-semibold text-truncate"><i class="text-secondary cp-last-copied-transaction me-1"></i>LSN of Last Copied Log</td>
            <td class="text-truncate" id="DRLastLSN_copied"></td>
            </tr>
            <tr>
            <td class="fw-semibold text-truncate"><i class="text-secondary cp-last-copied-transaction me-1"></i>Last Copied Log</td>
            <td class="text-truncate" id="DRlast_copied_file"></td>
            </tr>
            <tr>
            <td class="fw-semibold text-truncate"><i class="text-secondary cp-last-copied-transaction me-1"></i>LSN of Last Restored Log</td>
            <td class="text-truncate" id="DRLastLSN_restored"></td>
            </tr>

            </tbody>
            </table>
            </div>
            </div>
            </div> *@
        </div>
    </div>
</div>

<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/js/monitoring/MonitoringSolutionDiagram.js"></script>
<script src="~/js/monitoring/MonitoringMssqlNLS.js"></script>

