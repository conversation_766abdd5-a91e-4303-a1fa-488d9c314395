﻿using ContinuityPatrol.Application.Features.UserRole.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.UserRole.Queries;

public class GetUserRoleDetailQueryHandlerTests : IClassFixture<UserRoleFixture>
{
    private readonly UserRoleFixture _userRoleFixture;

    private readonly Mock<IUserRoleRepository> _mockUserRoleRepository;

    private readonly GetUserRoleDetailQueryHandler _handler;

    public GetUserRoleDetailQueryHandlerTests(UserRoleFixture userRoleFixture)
    {
        _userRoleFixture = userRoleFixture;

        _mockUserRoleRepository = UserRoleRepositoryMocks.GetUserRoleRepository(_userRoleFixture.UserRoles);

        _handler = new GetUserRoleDetailQueryHandler(_userRoleFixture.Mapper, _mockUserRoleRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_UserRoleDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetUserRoleDetailQuery { Id = _userRoleFixture.UserRoles[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<UserRoleDetailVm>();
        result.Id.ShouldBe(_userRoleFixture.UserRoles[0].ReferenceId);
        result.Role.ShouldBe(_userRoleFixture.UserRoles[0].Role);
        result.IsDelete.ShouldBe(_userRoleFixture.UserRoles[0].IsDelete);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidUserRoleId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetUserRoleDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetUserRoleDetailQuery { Id = _userRoleFixture.UserRoles[0].ReferenceId }, CancellationToken.None);

        _mockUserRoleRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}