﻿using ContinuityPatrol.Application.Features.Server.Queries.GetDetail;
using ContinuityPatrol.Application.Helper;

namespace ContinuityPatrol.Application.Features.DashboardView.Queries.GetByEntityId;

public class GetByEntityIdQueryHandler : IRequestHandler<GetByEntityIdQuery, GetByEntityIdVm>
{
    private readonly IDashboardViewRepository _dashboardViewRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IMapper _mapper;
    private readonly IPageBuilderRepository _pageBuilderRepository;
    private readonly IPageSolutionMappingRepository _pageSolutionMappingRepository;
    private readonly IServerRepository _serverRepository;

    public GetByEntityIdQueryHandler(IMapper mapper, IDashboardViewRepository dashboardViewRepository,
        IInfraObjectRepository infraObjectRepository, IPageSolutionMappingRepository pageSolutionMappingRepository,
        IPageBuilderRepository pageBuilderRepository, IServerRepository serverRepository)
    {
        _mapper = mapper;
        _dashboardViewRepository = dashboardViewRepository;
        _infraObjectRepository = infraObjectRepository;
        _pageSolutionMappingRepository = pageSolutionMappingRepository;
        _pageBuilderRepository = pageBuilderRepository;
        _serverRepository = serverRepository;
    }

    public async Task<GetByEntityIdVm> Handle(GetByEntityIdQuery request, CancellationToken cancellationToken)
    {
        var dashboard = await _dashboardViewRepository.GetByEntityIdAndType(request.EntityId, request.Type);

        var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(dashboard?.InfraObjectId);

        var pageSolutionMapping =
            await _pageSolutionMappingRepository.GetByReplicationCategoryTypeId(infraObject?.ReplicationCategoryTypeId);

        var pageBuilder = pageSolutionMapping != null
            ? await _pageBuilderRepository.GetByReferenceIdAsync(pageSolutionMapping.PageBuilderId)
            : null;

        Guard.Against.NullOrDeactive(dashboard, nameof(Domain.Entities.DashboardView),
            new NotFoundException(nameof(Domain.Entities.DashboardView), request.EntityId));
        //new Code
        var infraServerProperty = InfraComponentPropertyHelper.GetPropertyDetails(infraObject?.ServerProperties);

        var servers = await _serverRepository.GetByServerIdsAsync(infraServerProperty?.Select(x => x.Id).ToList());

        //var infraPropertyLookup = infraServerProperty?.ToDictionary(inf => inf.Id, inf => inf.Key);

        var dashboardVm = _mapper.Map<GetByEntityIdVm>(dashboard);

        dashboardVm.ServerStatus = servers.Select(server =>
            {
                var matchingInfraProperty = infraServerProperty?.FirstOrDefault(inf => inf.Id == server.ReferenceId);
                return new ServerStatus
                {
                    Key = matchingInfraProperty?.Key,
                    ServerDetail = _mapper.Map<ServerDetailVm>(server)
                };
            }).ToList();

        dashboardVm.PageProperties = pageBuilder?.Properties ?? "NA";
        #region Old Code
        //var dashboardVm = _mapper.Map<GetByEntityIdVm>(dashboard);
        //dashboardVm.PageProperties = pageBuilder?.Properties ?? "NA";
        //dashboardVm.PrServerStatus = prServer?.Status ?? "NA";
        //dashboardVm.DrServerStatus = drServer?.Status ?? "NA";

        //var prServer = infraObject.PRServerId.IsNotNullOrWhiteSpace()
        //    ? await _serverRepository.GetByReferenceIdAsync(infraObject?.PRServerId)
        //    : null;

        //var drServer = infraObject.DRServerId.IsNotNullOrWhiteSpace()
        //    ? await _serverRepository.GetByReferenceIdAsync(infraObject?.DRServerId)
        //    : null;

        //var dashboardVm = _mapper.Map<GetByEntityIdVm>(dashboard);
        //dashboardVm.PageProperties = pageBuilder?.Properties ?? "NA";
        //dashboardVm.PrServerStatus = prServer?.Status ?? "NA";
        //dashboardVm.DrServerStatus = drServer?.Status ?? "NA";
        #endregion
        return dashboardVm;
    }
}