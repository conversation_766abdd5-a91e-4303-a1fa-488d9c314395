using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.GlobalVariableModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetPaginatedList;

public class GetGlobalVariablePaginatedListQueryHandler : IRequestHandler<GetGlobalVariablePaginatedListQuery, PaginatedResult<GlobalVariableListVm>>
{
    private readonly IGlobalVariableRepository _globalVariableRepository;
    private readonly IMapper _mapper;

    public GetGlobalVariablePaginatedListQueryHandler(IMapper mapper, IGlobalVariableRepository globalVariableRepository)
    {
        _mapper = mapper;
        _globalVariableRepository = globalVariableRepository;
    }

    public async Task<PaginatedResult<GlobalVariableListVm>> Handle(GetGlobalVariablePaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _globalVariableRepository.GetPaginatedQuery();

        var productFilterSpec = new GlobalVariableFilterSpecification(request.SearchString);

        var globalVariableList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<GlobalVariableListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return globalVariableList;
    }
}
