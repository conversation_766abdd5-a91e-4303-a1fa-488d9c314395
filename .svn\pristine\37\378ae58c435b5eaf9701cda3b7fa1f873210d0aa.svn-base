using ContinuityPatrol.Application.Features.CyberAirGap.Events.Create;

namespace ContinuityPatrol.Application.Features.CyberAirGap.Commands.Create;

public class CreateCyberAirGapCommandHandler : IRequestHandler<CreateCyberAirGapCommand, CreateCyberAirGapResponse>
{
    private readonly ICyberAirGapRepository _cyberAirGapRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateCyberAirGapCommandHandler(IMapper mapper, ICyberAirGapRepository cyberAirGapRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _cyberAirGapRepository = cyberAirGapRepository;
    }

    public async Task<CreateCyberAirGapResponse> Handle(CreateCyberAirGapCommand request,
        CancellationToken cancellationToken)
    {
        var cyberAirGap = _mapper.Map<Domain.Entities.CyberAirGap>(request);

        cyberAirGap = await _cyberAirGapRepository.AddAsync(cyberAirGap);

        var response = new CreateCyberAirGapResponse
        {
            Message = Message.Create("Airgap", cyberAirGap.Name),

            Id = cyberAirGap.ReferenceId
        };

        await _publisher.Publish(new CyberAirGapCreatedEvent { Name = cyberAirGap.Name }, cancellationToken);

        return response;
    }
}