﻿namespace ContinuityPatrol.Domain.ViewModels.MonitorServicesListModel;

public class MonitorServiceListVm
{
    public string Id { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string ServerId { get; set; }
    public string ServerName { get; set; }
    public string IPAddress { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string ServicePath { get; set; }
    public string Type { get; set; }
    public string ThreadType { get; set; }
    public string Status { get; set; }
    public string WorkflowVersion { get; set; }
    public string WorkflowType { get; set; }
    public string IsServiceUpdate { get; set; }
    public string LastExecutionTime { get; set; }
    public string LastModifiedDate { get; set; }
    public string FailedActionId { get; set; }
    public string FailedActionName { get; set; }

}