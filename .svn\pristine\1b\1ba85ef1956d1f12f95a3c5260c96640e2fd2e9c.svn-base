﻿using ContinuityPatrol.Application.Features.DataSyncJob.Events.Delete;

namespace ContinuityPatrol.Application.Features.DataSyncJob.Commands.Delete;

public class DeleteDataSyncJobCommandHandler : IRequestHandler<DeleteDataSyncJobCommand, DeleteDataSyncJobResponse>
{
    private readonly IDataSyncJobRepository _dataSyncJobRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public DeleteDataSyncJobCommandHandler(IDataSyncJobRepository dataSyncJobRepository, IMapper mapper,
        IPublisher publisher)
    {
        _dataSyncJobRepository = dataSyncJobRepository;
        _mapper = mapper;
        _publisher = publisher;
    }

    public async Task<DeleteDataSyncJobResponse> Handle(DeleteDataSyncJobCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _dataSyncJobRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(DataSyncJob), request.Id);

        eventToUpdate.IsActive = false;

        await _dataSyncJobRepository.UpdateAsync(eventToUpdate);

        var response = new DeleteDataSyncJobResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.DataSyncJob), eventToUpdate.ReplicationName),
            IsActive = eventToUpdate.IsActive
        };

        await _publisher.Publish(new DataSyncJobDeletedEvent { Name = eventToUpdate.ReplicationName },
            CancellationToken.None);
        return response;
    }
}