﻿//let noDataImage1 = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">'

//GetCyberJobManagementStatus()
//GetInfrastructureSummary()
//GetCyberSnapsList()
//GetCyberAlertsCount()
//GetAirGapsStatus()
//$(".airgapStatusAccordion").empty()

//function GetCyberSnapsList() {

//    $.ajax({
//        url: "/CyberResiliency/CyberResiliency/GetCyberSnapsList",
//        dataType: "json",
//        traditional: true,
//        type: 'GET',
//        success: function (result) {

//            if (result?.success) {
//                $(".totalOperationalServices").text(result?.data?.totalBusinessServiceCount)
//                $(".nonConflictedServices").text(result?.data?.totalNonConflictCount)
//                $(".conflictedServices").text(result?.data?.totalConflictCount)
//                //resourceConflictScroe(result.data)
//            }
//            else {
//                $(".totalOperationalServices").text("0")
//                $(".nonConflictedServices").text("0")
//                $(".conflictedServices").text("0")
//            }
//        }
//    })
//}

//function GetCyberJobList() {

//    $("#jobTableDetails").empty()

//    $.ajax({
//        type: 'GET',
//        url: RootUrl + "CyberResiliency/CyberResiliency/GetCyberJobList",
//        dataType: "json",
//        traditional: true,
//        success: function (result) {

//            if (result.success) {
//                let html = '';

//                if (result?.data && Array.isArray(result?.data) && result?.data.length) {

//                    result?.data.forEach((data, index) => {
//                        let statusClassMap = {
//                            "pending": "cp-pending text-warning me-1",
//                            "running": "text-success cp-reload cp-animate me-1",
//                            "success": "cp-success text-success me-1"
//                        };

//                        let stateClassMap = {
//                            "active": "cp-active-inactive text-success me-1",
//                            "inactive": "cp-active-inactive text-danger me-1"
//                        };

//                        let statusClass = statusClassMap[data?.status?.toLowerCase()] || "cp-error text-danger me-1";
//                        let stateClass = stateClassMap[data?.state?.toLowerCase()] || "cp-active-inactive text-danger me-1";

//                        html += ` <tr><td>${index + 1}</td><td>${data?.name || 'NA'}</td><td>${data?.workflowName || 'NA'}</td>
//                    <td>${data?.scheduleTime || 'NA'}</td><td><i title="${data?.status || 'NA'}" class="${statusClass}"></i><span title="${data?.status || 'NA'}">${data?.status || 'NA'}</span></td>
//                    <td><i title="${data?.state || 'NA'}" class="${stateClass}"></i><span title="${data?.state || 'NA'}">${data?.state || 'NA'}</span></td>
//                    <td>${data?.lastExecutedTime || 'NA'}</td></tr>`

//                    })
//                } else {
//                    html += '<tr class="no-records"><td colspan="8" class="text-center">No matching records found</td></tr>'
//                }

//                $("#jobTableDetails").append(html)
//            }
//            else {
//                errorNotification(result)
//            }
//        }
//    })
//}

//function GetCyberAlertList() {

//    $("#alertTableDetails").empty()

//    $.ajax({
//        type: 'GET',
//        url: RootUrl + "CyberResiliency/CyberResiliency/GetCyberAlertsCount",
//        dataType: "json",
//        traditional: true,
//        success: function (result) {

//            if (result.success) {
//                let html = '';

//                if (result?.data?.cyberAlertListVms && Array.isArray(result?.data?.cyberAlertListVms) && result?.data.cyberAlertListVms.length) {

//                    result?.data.cyberAlertListVms.forEach((data, index) => {
//                        let severity = data?.severity.toLowerCase();
//                        const capitalized = data?.severity?.charAt(0).toUpperCase() + data?.severity?.slice(1).toLowerCase();
//                        const createdDate = data?.createdDate;

//                        const date = new Date(createdDate);
//                        let formattedDate = date.getFullYear() + '-' +
//                            ('0' + (date.getMonth() + 1)).slice(-2) + '-' +
//                            ('0' + date.getDate()).slice(-2) + ' ' +
//                            ('0' + date.getHours()).slice(-2) + ':' +
//                            ('0' + date.getMinutes()).slice(-2) + ':' +
//                            ('0' + date.getSeconds()).slice(-2);

//                        let iconclass = severity === "high" ? "cp-up-doublearrow text-warning" : severity === "critical" ?
//                            "cp-critical-level text-danger" : severity === "low" ?
//                                "cp-down-doublearrow text-success" : "cp-warning text-primary";

//                        html += `<tr><td>${index + 1}</td>
//                                     <td>${data?.type}</td>
//                                     <td><span  title="${data?.systemMessage || 'NA'}" class="text-truncate" style="max-width:450px;display:inline-block">${data?.systemMessage || 'NA'}</span></td>
//                                     <td><i class="me-1 fw-bold ${iconclass}"></i> ${capitalized}</td>
//                                     <td>${formattedDate}</td>
//                                 </tr>`
//                    })
//                } else {
//                    html += '<tr class="no-records"><td colspan="5" class="text-center">No Data Found</td></tr>'
//                }

//                $("#alertTableDetails").append(html)

//            }
//            else {
//                errorNotification(result)
//            }
//        }
//    })
//}

//$('#jobToggle').on('click', function () {
//    GetCyberJobList();
//    $('#JobModal').modal('show')
//})

//$('#alertToggle').on('click', function () {
//    GetCyberAlertList();
//    $('#AlertModal').modal('show')
//})

//function GetCyberJobManagementStatus() {

//    $.ajax({
//        url: "/CyberResiliency/CyberResiliency/GetCyberJobManagementStatus",
//        dataType: "json",
//        traditional: true,
//        type: 'GET',
//        success: function (result) {

//            if (result?.success) {
//                if (result?.data != 0) {
//                    GetCyberJobManagementStatusChart(result?.data)
//                } else {
//                    $("#JobManagementStatus").append('<div class="carousel-inner" id="infrasummary" style="text-align: center;"><img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img"></div>')
//                }
//            }
//            else {
//                //$(".totalOperationalServices").text("0")
//                //$(".nonConflictedServices").text("0")
//                //$(".conflictedServices").text("0")
//            }
//        }
//    })
//}

//function GetAirGapsStatus() {
//    $(".airgapStatusAccordion").empty();

//    $.ajax({
//        url: "/CyberResiliency/CyberResiliency/GetAirGapsStatus",
//        dataType: "json",
//        traditional: true,
//        type: 'GET',
//        success: function (result) {

//            if (result?.success) {
//                if (result?.data?.length != 0) {
//                    airgapTableDetails(result?.data)
//                    result?.data.forEach((data, index) => {

//                        let showindex = index == 0 ? "" : ""
//                        let textStatus = data?.status?.toLowerCase() == "open" ? "text-bg-success" : "text-bg-danger";
//                        let textName = (data?.status?.toLowerCase() == "open" || data?.status?.toLowerCase() == "enable" || data?.status?.toLowerCase() == "unlock") ? "Open" : "Close";
//                        let workflowStatus = data?.workflowStatus?.toLowerCase() === "completed" ? "" : data?.workflowStatus?.toLowerCase() === "running" ? "cp-reload cp-animate" : data?.workflowStatus?.toLowerCase() === "error" ? "cp-affecteds text-danger cp-animate" : "";
//                        let nameIcon = (data?.status?.toLowerCase() == "open" || data?.status?.toLowerCase() == "enable" || data?.status?.toLowerCase() == "unlock") ? "cp-open-lock text-success" : "cp-lock text-danger";

//                        let html = `<div class="accordion-item"><div class="accordion-header">
//                      <button class="accordion-button collapsed" type="button" >
//                      <p class="mb-0 flex-fill" style="font-size:12px"><i class='${nameIcon} me-2' title='${textName}'></i>${data?.name || 'NA'} <i class="text-primary ms-2 ${workflowStatus}" style="font-size: 14px;"></i></p><div>
//                      <span><i class="cp-down-arrow fs-7" role="button" data-bs-toggle="collapse" data-bs-target="#flush-collapse${index}" aria-expanded="false" aria-controls="flush-collapse${index}"></i></span></button></div>
//                            <div id="flush-collapse${index}" class="accordion-collapse collapse ${showindex}" data-bs-parent="#accordionFlushExample">
//                                <div class="accordion-body" ><div id="air-gap-diagram${index}" class=" w-100" style="height:65px"></div>`

//                        if (data?.getAirGapsServeListVms?.length != 0) {

//                            let filteredSource = data?.getAirGapsServeListVms.filter(d => d?.type?.toLowerCase() == 'source')
//                            let filteredTarget = data?.getAirGapsServeListVms.filter(d => d?.type?.toLowerCase() == 'target')
//                            //<span class="d-block my-1 fs-8 ">Cyber Recovery Vault</span>
//                            //<span class="d-block my-1 fs-8">Clean Room</span>
//                            //<div class="form-check form-switch"><input class="form-check-input open" type="checkbox" airgapId=${data?.id} airgapName=${data?.name} role="switch" onclick="UpdateAirGapStatus(this)" id="flexSwitchCheckChecked${index}" checked></div></div >

//                            //html += `<div><div class="d-flex align-items-center justify-content-between"> <div><p><span class="fs-9 ${data?.getAirGapsServeListVms[0]?.type?.toLowerCase() == 'target' ? 'text-secondary' : 'text-dark fw-semibold'}">${data?.getAirGapsServeListVms[0]?.name || 'NA'}</span><span class="d-block
//                            //fs-8 ${data?.getAirGapsServeListVms[0]?.type?.toLowerCase() == 'target' ? 'text-secondary' : 'text-secondary'}">${data?.getAirGapsServeListVms[0]?.type || 'NA'}</span>
//                            //                        <span class="fw-semibold d-block my-1"><i class="${data?.getAirGapsServeListVms[0]?.siteName && 'cp-active-inactive'} ${data?.getAirGapsServeListVms[0]?.type?.toLowerCase() == 'target' ? 'text-secondary' : 'text-primary'} me-1"></i>
//                            //                        ${data?.getAirGapsServeListVms[0]?.siteName || 'NA'}</span>

//                            //                    </p>
//                            //                </div><div>

//                            //                    <p class=" text-end">
//                            //                      <span class="fs-9 text-dark fw-semibold">${data?.getAirGapsServeListVms[1]?.name || 'NA'}</span>
//                            //                        <span class="d-block fs-8 text-secondary">
//                            //                            ${data?.getAirGapsServeListVms[1]?.type || 'NA'}
//                            //                        </span>
//                            //                        <span class="fw-semibold d-block my-1"><i class="${data?.getAirGapsServeListVms[1]?.siteName && 'cp-active-inactive'} me-1"></i>${data?.getAirGapsServeListVms[1]?.siteName || 'NA'}</span>

//                            //                    </p>

//                            //                </div>
//                            //            </div>
//                            //        </div>`

//                            html += `<div><div class="d-flex align-items-center justify-content-between">
//                                      <div><p>
//                                      <span class="fs-9 text-dark fw-semibold">
//                                     ${filteredSource && Array.isArray(filteredSource) && filteredSource.length
//                                    ? filteredSource.map((fs) => `<li class="text-dark">
//                                    <span class="fs-8 fw-semibold">${fs?.name || 'NA'} </span><br />
//                                    <span class="ms-3 fs-9"><i class="cp-ip-address fs-9 me-1"></i>${fs?.ipAddress || 'NA'} </span>
//                                    <br />
//                                    <span class="ms-3 fs-9"><i class="cp-port fs-9 me-1"></i>${fs?.port || 'NA'} </span>
//                                    </li>`).join('')
//                                    : ''
//                                }
//                                      </span>`;

//                        }

//                        //<span class="fw-semibold d-block my-1">
//                        //    <i class="${filteredSource?.length && filteredSource[0]?.siteName ? 'cp-active-inactive' : ''} me-1"></i>
//                        //    ${filteredSource?.length && filteredSource[0]?.siteName || 'NA'}
//                        //</span> </p ></div ></div ></div >

//                        //<div><p class="text-end">
//                        //    <span class="fs-9 text-dark fw-semibold">
//                        //        ${
//                        //            filteredTarget && Array.isArray(filteredTarget) && filteredTarget.length
//                        //                ? filteredTarget.map((fs) => `<li>
//                        //         <span class="fs-8 fw-semibold">${fs?.name || 'NA'}</span>
//                        //            <span class="ms-3 fs-9"><i class="cp-ip-address fs-9 me-1"></i>${fs?.ipAddress || 'NA'}</span>
//                        //        </li>`).join('')
//                        //                : ''
//                        //        }
//                        //    </span>
//                        //    <span class="d-block fs-8 text-secondary mt-2"> ${filteredTarget?.length && filteredTarget[0]?.type || 'NA'}</span>
//                        //    <span class="fw-semibold d-block my-1">
//                        //        <i class="${filteredTarget?.length && filteredTarget[0]?.siteName ? 'cp-active-inactive' : ''} me-1"></i>
//                        //        ${filteredTarget?.length && filteredTarget[0]?.siteName || 'NA'}
//                        //    </span>
//                        //</p>
//                        //</div>

//                        //<div class="py-2 w-50">
//                        //                   <span>
//                        //                       <span class="d-flex align-items-center gap-1">
//                        //                           <span class="p-2 rounded  bg-white"><i class="cp-RTO text-primary align-middle"></i></span>
//                        //                           <span class="text-primary">RPO <small class="text-secondary">15 Mins</small> ( 50%)</span>
//                        //                       </span>
//                        //                   </span>

//                        //                   <div class="d-flex gap-1 my-2">
//                        //                       <div class="w-50 text-center">
//                        //                       <div id="threshold-chart${index}" style="height:80px"></div>
//                        //                           <span class="mt-2 fs-8" >Threshold</span>
//                        //                       </div>
//                        //                       <span>
//                        //                           <span class="d-block mb-2">
//                        //                               <span class="text-warning">30</span> Mins <br />
//                        //                               Configuration
//                        //                            </span>
//                        //                           <span class="d-block">
//                        //                               <span class="text-warning">0</span> Mins <br />
//                        //                               Computed
//                        //                           </span>
//                        //                       </span>
//                        //                   </div>
//                        //               </div>
//                        //               <div class="vr bg-secondary-subtle"></div>
//                        let rpo = data?.rpo ? data?.rpo : "NA"
//                        html += `<div class="border-light-subtle border-top d-flex align-items-center justify-content-between">

//                                        <div class="py-2">

//                                            <table class="table table-sm table-borderless mb-0">
//                                                <thead>
//                                                    <tr>
//                                                        <th colspan="3" class="bg-transparent fw-normal text-primary">Last Activity</th>
//                                                    </tr>
//                                                </thead>
//                                                <tbody>
//                                                    <tr>
//                                                        <td>
//                                                            Opened Time
//                                                        </td>
//                                                        <td>
//                                                            :
//                                                        </td>
//                                                        <td>
//                                                            ${data?.startTime && !isNaN(new Date(data.startTime).getTime()) && data.startTime !== '0001-01-01T00:00:00'
//                                ? new Date(data.startTime).toLocaleString()
//                                : '-'}

//                                                        </td>
//                                                    </tr>
//                                                    <tr>
//                                                        <td>
//                                                            Closed Time
//                                                        </td>
//                                                        <td>
//                                                            :
//                                                        </td>
//                                                        <td>
//                                                          ${data?.endTime && !isNaN(new Date(data.endTime).getTime()) && data.endTime !== '0001-01-01T00:00:00'
//                                ? new Date(data.endTime).toLocaleString()
//                                : '-'}
//                                                        </td>
//                                                    </tr>
//                                                    <tr>
//                                                        <td>
//                                                            Time Period
//                                                        </td>
//                                                        <td>
//                                                            :
//                                                        </td>
//                                                        <td>
//                                                            ${rpo}
//                                                        </td>
//                                                    </tr>
//                                                </tbody>
//                                            </table>
//                                        </div>

//                                    </div>

//                                </div>
//                            </div>
//                        </div>`
//                        $(".airgapStatusAccordion").append(html)

//                        if (data?.getAirGapsServeListVms != 0) {
//                            airgapDiagramChart('air-gap-diagram' + index, data)
//                        }
//                        threshouldChart("threshold-chart" + index)

//                        data?.status?.toLowerCase() == "open" || data?.status?.toLowerCase() == "enable" || data?.status?.toLowerCase() == "unlock" ? $("#flexSwitchCheckChecked" + index).prop("checked", true) : $("#flexSwitchCheckChecked" + index).prop("checked", false)
//                    })
//                }
//                else {

//                    $(".airgapStatusAccordion").append('<div class="carousel-inner" id="infrasummary" style="text-align: center;"><img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img"></div>')
//                    //$(".totalOperationalServices").text("0")
//                    //$(".nonConflictedServices").text("0")
//                    //$(".conflictedServices").text("0")
//                }
//            }
//        }
//    })

//}
//function GetCyberAlertsCount() {

//    $.ajax({
//        url: "/CyberResiliency/CyberResiliency/GetCyberAlertsCount",
//        dataType: "json",
//        traditional: true,
//        type: 'GET',
//        success: function (result) {

//            if (result?.success) {
//                //if (result.data != 0) {
//                GetCyberAlertsCountChart(result?.data)
//                //}
//            }
//            else {
//                //$(".totalOperationalServices").text("0")
//                //$(".nonConflictedServices").text("0")
//                //$(".conflictedServices").text("0")
//            }
//        }
//    })
//}

//function airgapDiagramChart(id, airgapData) {
//    //document.getElementById("tableData").innerHTML = counts;

//    let airgapArray = []
//    airgapData?.getAirGapsServeListVms?.length && airgapData?.getAirGapsServeListVms.forEach((data, index) => {

//        let x = 0
//        let y = 0
//        let isAlreadyTypeExist = airgapArray?.length && airgapArray.some(d => d?.siteName?.toLowerCase() == data?.siteName?.toLowerCase())

//        if (index == 0) {
//            x = 10
//            y = 30
//        }
//        else {
//            x = 90
//            y = 30
//        }
//        airgapArray.push(
//            {
//                name: data.name,
//                fixed: true,

//                x: am4core.percent(x),
//                y: am4core.percent(y),
//                tag: "✔",
//                ip: data?.ipAddress,
//                Connection: "HTTP",
//                Service: "Monitor Service",
//                siteName: !isAlreadyTypeExist ? data?.siteName : '',
//                host: "CD030",
//                Port: "6002",
//                value: 10,
//                image: "/img/charts_img/DataCenter/server.svg",

//            })
//    })
//    am4core.useTheme(am4themes_animated);

//    // Create chart
//    var chart = am4core.create(id,
//        am4plugins_forceDirected.ForceDirectedTree
//    );
//    if (chart.logo) {
//        chart.logo.disabled = true;
//    }
//    // Create series
//    var series = chart.series.push(
//        new am4plugins_forceDirected.ForceDirectedSeries()
//    );
//    chart.colors.list = [am4core.color("#ff9c0d"), am4core.color("#40c200")];
//    series.data = [{
//        name: "Air Gap 1",
//        fixed: true,
//        x: am4core.percent(50),
//        y: am4core.percent(30),
//        ip: "",
//        Connection: "HTTP",
//        Service: "Monitor Service",
//        host: "CD030",
//        Port: "6002",
//        value: 15,
//        image: (airgapData?.status?.toLowerCase() === 'open' || airgapData?.status?.toLowerCase() === 'enable' || airgapData?.status?.toLowerCase() === 'unlock') ? "/img/charts_img/DataCenter/cyber-lock-open.svg" : "/img/charts_img/DataCenter/cyber-lock.svg",
//        children: airgapArray
//    },];

//    // Set up data fields
//    series.dataFields.value = "value";
//    series.dataFields.fixed = "fixed";
//    series.dataFields.name = "name";
//    series.dataFields.ip = "ip";
//    series.dataFields.Connection = "Connection";
//    (series.dataFields.Service = "Service"),
//        (series.dataFields.host = "host"),
//        (series.dataFields.Port = "Port"),
//        (series.dataFields.id = "id");
//    series.dataFields.children = "children";
//    series.dataFields.tag = "tag";
//    series.dataFields.linkWith = "link";
//    series.dataFields.id = "name";
//    series.manyBodyStrength = -18;
//    // Add labels
//    series.nodes.template.label.text = "{siteName}";
//    series.nodes.template.label.valign = "bottom";

//    series.nodes.template.label.fill = am4core.color("#000");
//    series.nodes.template.label.dy = -5;
//    series.nodes.template.label.fontSize = 10;
//    series.nodes.template.label.fontWeight = "bold";

//    series.nodes.template.tooltipText =
//        "[font-size: 15px; #0479ff; ]{name}\n[/] IP Address : [bold]{ip} [/]\n Connection Type : [bold]{Connection} [/] \n Service Type : [bold]{Service}[/]\n Host Name : [bold]{host}[/]\n Port : [bold]{Port}[/]\n";
//    series.fontSize = 11;
//    series.minRadius = 35;
//    series.maxRadius = 35;

//    series.tooltip.autoTextColor = false;
//    series.tooltip.getFillFromObject = false;
//    series.tooltip.label.fill = am4core.color("#1A1A1A");
//    series.tooltip.label.background.fill = am4core.color("#fff");

//    series.links.template.strokeWidth = 1;
//    series.links.template.strokeDasharray = "5,3";
//    series.nodes.template.circle.strokeWidth = 0;
//    series.nodes.template.circle.disabled = true;
//    series.nodes.template.outerCircle.disabled = true;

//    series.dataFields.fixed = "fixed";
//    series.nodes.template.propertyFields.x = "x";
//    series.nodes.template.propertyFields.y = "y";



//    // Change the padding values
//    // chart.padding(-15, -15, -15, -15)

//    // Configure icons
//    var icon = series.nodes.template.createChild(am4core.Image);
//    icon.propertyFields.href = "image";
//    icon.horizontalCenter = "middle";
//    icon.verticalCenter = "middle";
//    icon.width = 40;
//    icon.height = 40;
//    series.centerStrength = 0.5;

//}

//let globalEntityData;
//function GetInfrastructureSummary() {
//    $.ajax({
//        url: "/CyberResiliency/CyberResiliency/GetInfrastructureSummaryCount",
//        dataType: "json",
//        traditional: true,
//        type: 'GET',
//        success: function (result) {

//            if (result?.success) {

//                if (result?.data && result?.data?.length && result?.data != 0) {
//                    $(".infrastructureSummary").empty();
//                    globalEntityData = result?.data
//                    result?.data.forEach((data) => {




//                        let dynamicIcon = data?.key?.toLowerCase();
//                        let upcount = 0
//                        let downcount = 0
//                        //let Name= data.entityType.toLowerCase() == "database" ? "Database" : data.entityType.toLowerCase() == "server" ? "Server" : data.entityType.toLowerCase() == "switch" ? "Switches" : data.entityType.toLowerCase() == "storage" ?"Storage":"Server"

//                        if (data?.value && data?.value?.length && data?.value != 0) {
//                            let serverInfrastructure = data?.value.forEach((Summarycount) => {
//                                if (Summarycount?.status?.toLowerCase() == "up") {
//                                    upcount += 1
//                                }
//                                else {
//                                    downcount += 1
//                                }
//                            })
//                        }

//                        let parentimage = dynamicIcon === "database" ? "/img/layouts_img/Cyber-BG/Database_BG.svg"
//                            : dynamicIcon === "server" ? "/img/layouts_img/Cyber-BG/Server_BG.svg"
//                                : dynamicIcon === "storage" ? "/img/layouts_img/Cyber-BG/Storages_BG.svg"
//                                    : dynamicIcon === "thirdparty" ? "/img/layouts_img/Cyber-BG/ThirdParty_BG.svg"
//                                        : dynamicIcon === "virtualization" ? "/img/layouts_img/Cyber-BG/Virtualization_BG.svg"
//                                            : dynamicIcon === "application" ? "/img/layouts_img/Cyber-BG/Application_BG.svg"
//                                                : dynamicIcon === "dns" ? "/img/layouts_img/Cyber-BG/DNS_BG.svg"
//                                                    : "/img/layouts_img/Cyber-BG/Switches_BG.svg";

//                        let image = dynamicIcon === "database" ? "/img/charts_img/datacenter/database.svg"
//                            : dynamicIcon === "server" ? "/img/charts_img/datacenter/server.svg"
//                                : dynamicIcon === "storage" ? "/img/charts_img/datacenter/storage.svg"
//                                    : dynamicIcon === "thirdparty" ? "/img/charts_img/datacenter/thirdparty.svg"
//                                        : dynamicIcon === "virtualization" ? "/img/charts_img/datacenter/virtualization.svg"
//                                            : dynamicIcon === "application" ? "/img/charts_img/datacenter/application.svg"
//                                                : dynamicIcon === "dns" ? "/img/charts_img/datacenter/dns.svg"
//                                                    : "/img/charts_img/datacenter/Switches.svg"

//                        let html = `<div class="col d-grid">
//                            <div class="card mb-0 h-100" style="background-image: url('${parentimage}');background-size:cover; background-repeat: no-repeat;">
//                                <div class="card-body p-2">
//                                    <div class="d-flex justify-content-between align-items-start">
//                                        <div class="d-grid">
//                                            <span class="fw-bold">
//                                                ${data?.key || 'NA'}
//                                            </span>
//                                            <span class="fs-5 fw-semibold">
//                                                ${data?.value?.length || 0}
//                                            </span>
//                                        </div>
//                                        <span><img src="${image}" /></span>
//                                    </div>
//                                </div>
//                                <div class="card-footer p-2 pt-0 d-flex align-items-center gap-3">
//                                    <span class="fs-6"  onclick="${upcount > 0 ? `InfrastructureSummarydata('${data?.key}','up')` : ''}" role='${upcount > 0 ? "button" : ""}'>${upcount}<i class="cp-up-linearrow ms-1 text-success fs-8 align-middle"></i></span>
//                                    <span class="fs-6" onclick="${downcount > 0 ? `InfrastructureSummarydata('${data?.key}','down')` : ''}" role='${downcount > 0 ? "button" : ""}'>${downcount}<i class="cp-down-linearrow ms-1 text-danger fs-8 align-middle"></i></span>
//                                </div>
//                            </div>
//                        </div>`

//                        $(".infrastructureSummary").append(html)
//                    })
//                } else {
//                    $(".infrastructureSummary").append('<div class="carousel-inner" id="infrasummary" style="text-align: center;"><img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img"></div>')
//                }
//            }
//            else {
//                //$(".totalOperationalServices").text("0")
//                //$(".nonConflictedServices").text("0")
//                //$(".conflictedServices").text("0")
//            }
//        }
//    })
//}

//function InfrastructureSummarydata(type = null, letstatus = null) {

//    $("#InfrastructureSummaryModal").modal("show")
//    $(".infraSummaryStautus").empty()
//    let infraCount = 0;

//    globalEntityData && globalEntityData?.length && globalEntityData.forEach((data, i) => {

//        if (data?.key == type) {

//            if (data?.value && data?.value?.length) {
//                let upNo = 0
//                let downNo = 0

//                data?.value.forEach((dataChild, j) => {
//                    let propertices = JSON.parse(dataChild.properties)
//                    let ipHost = propertices?.IpAddress != "NA" && propertices?.HostName != "NA" ? propertices?.IpAddress + "/" + propertices?.HostName : "NA"
//                    let status = dataChild?.status?.toLowerCase() == "up" ? "text-success" : dataChild?.status?.toLowerCase() == "down" ? "text-danger" : "text-warning"
//                    let textName = dataChild?.status?.toLowerCase() == "up" ? "UP" : dataChild?.status?.toLowerCase() == "down" ? "Down" : "Pending"
//                    if (letstatus == "up" && dataChild?.status?.toLowerCase() == "up") {
//                        let html = `<tr>
//                <td>${upNo += 1}</td>
//                 <td>${dataChild?.name || 'NA'}</td>
//                  <td>${ipHost || 'NA'}</td>
//                   <td>${dataChild?.siteName || 'NA'}</td>
//                    <td><i class="cp-up-linearrow me-1 ${status}"></i>${textName || 'NA'}</td>
//                            </tr>`
//                        $(".infraSummaryStautus").append(html)
//                    }
//                    else if (dataChild?.status?.toLowerCase() == "down" && letstatus == "down" || dataChild?.status?.toLowerCase() == "pending" && letstatus == "down") {
//                        let html = `<tr>
//                <td>${downNo += 1}</td>
//                 <td>${dataChild?.name || 'NA'}</td>
//                  <td>${ipHost || 'NA'}</td>
//                   <td>${dataChild?.siteName || 'NA'}</td>
//                    <td><i class="cp-down-linearrow me-1 ${status}"></i>${textName || 'NA'}</td>
//                            </tr>`
//                        $(".infraSummaryStautus").append(html)
//                    }
//                })

//            }
//        }
//        if (type === null && letstatus == null) {
//            if (data?.value && data?.value?.length) {

//                data?.value.forEach((dataChild, j) => {
//                    let propertices = JSON.parse(dataChild.properties)
//                    let ipHost = propertices?.HpAddress != "NA" && propertices?.HostName != "NA" ? propertices?.IpAddress + "/" + propertices?.HostName : "NA"
//                    let status = dataChild?.status?.toLowerCase() == "up" ? "text-success" : dataChild?.status?.toLowerCase() == "down" ? "text-danger" : "text-warning"
//                    let textName = dataChild?.status?.toLowerCase() == "up" ? "UP" : dataChild?.status?.toLowerCase() == "down" ? "Down" : "Pending"

//                    let html = `<tr>
//                                    <td>${infraCount += 1}</td>
//                                    <td>${dataChild?.name || 'NA'}</td>
//                                    <td>${ipHost || 'NA'}</td>
//                                    <td>${dataChild?.siteName || 'NA'}</td>
//                                    <td><i class="cp-up-linearrow me-1 ${status}"></i>${textName || 'NA'}</td>
//                                </tr>`
//                    $(".infraSummaryStautus").append(html)

//                })

//            }
//        }
//    })

//}

//function GetCyberAlertsCountChart(alertData) {

//    let alertSeverityCount = alertData?.alertSeverityCount
//    let CyberAlertData = []
//    let alertObject;


//    Object.keys(alertSeverityCount).forEach(function (key) {

//        if (key?.toLowerCase() == "information") {
//            alertObject = {
//                "country": "Information",
//                "litres": alertSeverityCount[key],
//                "color": am4core.color("#8960ff")
//            }

//        }
//        else if (key?.toLowerCase() == "warning") {
//            alertObject = {
//                "country": "Warning",
//                "litres": alertSeverityCount[key],
//                "color": am4core.color("#ffcb00")
//            }
//        }
//        else if (key?.toLowerCase() == "critical") {
//            alertObject = {
//                "country": "Critical",
//                "litres": alertSeverityCount[key],
//                "color": am4core.color("#ff003c")
//            }
//        }
//        else if (key?.toLowerCase() == "high") {
//            alertObject = {
//                "country": "High",
//                "litres": alertSeverityCount[key],
//                "color": am4core.color("#ffc107")
//            }
//        }
//        else if (key?.toLowerCase() == "low") {
//            alertObject = {
//                "country": "Low",
//                "litres": alertSeverityCount[key],
//                "color": am4core.color("#00ff04")
//            }
//        }
//        CyberAlertData.push(alertObject)

//    });


//    if (alertSeverityCount?.information) {
//        $(".informationStatus").removeClass("d-none")
//        $(".informationCount").text(alertSeverityCount?.information || 0)
//    }
//    if (alertSeverityCount?.warning) {
//        $(".warningStatus").removeClass("d-none")
//        $(".warningCount").text(alertSeverityCount?.warning || 0)
//    }
//    if (alertSeverityCount.high) {
//        $(".highStatus").removeClass("d-none")
//        $(".highCount").text(alertSeverityCount.high || 0)
//    }
//    if (alertSeverityCount.critical) {
//        $(".criticalStatus").removeClass("d-none")
//        $(".criticalCount").text(alertSeverityCount.critical || 0)
//    }
//    if (alertSeverityCount.low) {
//        $(".lowStatus").removeClass("d-none")
//        $(".lowCount").text(alertSeverityCount.low || 0)
//    }
//    var chart = am4core.create("CyberAlertsChart", am4charts.PieChart);
//    if (chart.logo) {
//        chart.logo.disabled = true;
//    }

//    chart.data = CyberAlertData;

//    chart.startAngle = 140;
//    chart.endAngle = 400;
//    // Add and configure Series
//    var pieSeries = chart.series.push(new am4charts.PieSeries());
//    pieSeries.dataFields.value = "litres";
//    pieSeries.dataFields.category = "country";
//    pieSeries.slices.template.propertyFields.fill = "color";
//    pieSeries.slices.template.stroke = am4core.color("#fff");
//    pieSeries.slices.template.strokeWidth = 5;
//    pieSeries.slices.template.strokeOpacity = 5;
//    pieSeries.slices.template.cornerRadius = 20;
//    pieSeries.slices.template.innerCornerRadius = 20;
//    // Let's cut a hole in our Pie chart the size of 40% the radius
//    chart.innerRadius = am4core.percent(65);
//    chart.padding(-10, -20, -10, -20);

//    // Disable ticks and labels
//    pieSeries.labels.template.disabled = true;
//    pieSeries.ticks.template.disabled = true;

//    // Disable tooltips
//    //pieSeries.slices.template.tooltipText = "";



//    // // Add a legend
//    // chart.legend = new am4charts.Legend();
//    // chart.legend.position = "bottom";
//    // chart.legend.valueLabels.template.disabled = true;
//    // chart.legend.labels.template.text = "[font-size:12px ]{name}";
//    // chart.legend.labels.template.fill = am4core.color("#6c757d");
//    // chart.legend.markers.template.children.getIndex(0).cornerRadius(30, 30, 30, 30);
//    // var markerTemplate = chart.legend.markers.template;
//    // markerTemplate.width = 10;
//    // markerTemplate.height = 10;

//    let label1 = chart.seriesContainer.createChild(am4core.Label);
//    label1.text = `[bold]${alertData.totalCount}[/] \n [font-size:12px] Total [/]`;
//    label1.horizontalCenter = "middle";
//    label1.verticalCenter = "middle";
//    label1.textAlign = "middle";
//    label1.fontSize = 17;


//}


//function threshouldChart(id) {


//    am4core.useTheme(am4themes_animated);

//    // Create chart instance
//    var chart = am4core.create(id, am4charts.RadarChart);
//    if (chart.logo) {
//        chart.logo.disabled = true;
//    }
//    // Add data
//    chart.data = [{
//        "category": "",
//        "value": 50,
//        // "value1": 40,
//        "full": 100
//    },];

//    // Make chart not full circle
//    chart.startAngle = 0;
//    chart.endAngle = 360;
//    chart.innerRadius = am4core.percent(80);

//    // Set number format
//    chart.numberFormatter.numberFormat = "";

//    // Create axes
//    var categoryAxis = chart.yAxes.push(new am4charts.CategoryAxis());
//    categoryAxis.dataFields.category = "category";
//    // categoryAxis.renderer.grid.template.location = 0;
//    categoryAxis.renderer.grid.template.strokeOpacity = 0;



//    categoryAxis.renderer.minGridDistance = 100;

//    var valueAxis = chart.xAxes.push(new am4charts.ValueAxis());
//    valueAxis.renderer.grid.template.strokeOpacity = 0;
//    valueAxis.min = 0;
//    valueAxis.max = 100;
//    categoryAxis.renderer.grid.push(new am4charts.Grid()).disabled = true;
//    valueAxis.renderer.grid.push(new am4charts.Grid()).disabled = true;
//    // valueAxis.strictMinMax = false;

//    // Create series
//    var series1 = chart.series.push(new am4charts.RadarColumnSeries());
//    series1.dataFields.valueX = "full";
//    series1.dataFields.categoryY = "category";
//    series1.clustered = false;
//    // Set the fill color of the columns to red
//    series1.columns.template.fill = new am4core.InterfaceColorSet().getFor("alternativeBackground");

//    // Optionally, you can also set the stroke color
//    series1.columns.template.stroke = am4core.color("green");
//    series1.columns.template.fillOpacity = 0.08;
//    series1.columns.template.cornerRadiusTopLeft = 100;
//    series1.columns.template.strokeWidth = 0;
//    series1.columns.template.radarColumn.cornerRadius = 100;

//    series1.columns.template.rotation = 300; // Start from the top


//    // // chart.innerRadius = 100;
//    var label = chart.seriesContainer.createChild(am4core.Label);
//    label.text = "50%";
//    label.horizontalCenter = "middle";
//    label.verticalCenter = "middle";
//    label.fontSize = 10;
//    label.rotation = 0;

//    var series2 = chart.series.push(new am4charts.RadarColumnSeries());
//    series2.dataFields.valueX = "value";
//    series2.dataFields.categoryY = "category";
//    series2.clustered = false;
//    series2.columns.template.strokeWidth = 0;
//    series2.columns.template.radarColumn.cornerRadius = 100;
//    series2.columns.template.fill = am4core.color("green");


//    chart.padding(0, 0, 0, 0);


//}

//function GetCyberJobManagementStatusChart(chartdata) {

//    let JobData = []
//    let jobObject;
//    let totalJobs = 0

//    if (chartdata && !chartdata?.length) {
//        JobData.push(
//            {
//                "country": "No Data",
//                "litres": 1,
//                "color": am4core.color("#d3d3d3")
//            }
//        );
//    } else {
//        chartdata?.length && chartdata.forEach((data) => {
//            totalJobs += data.count
//            if (data.status.toLowerCase() == "pending") {
//                jobObject = {
//                    "country": "Pending",
//                    "litres": data?.count,
//                    "color": am4core.color("#ffcb00")
//                }

//            }
//            else if (data.status.toLowerCase() == "running") {
//                jobObject = {
//                    "country": "Running",
//                    "litres": data?.count,
//                    "color": am4core.color("#00acef")
//                }
//            }
//            else if (data.status.toLowerCase() == "abort") {
//                jobObject = {
//                    "country": "Abort",
//                    "litres": data?.count,
//                    "color": am4core.color("#8960ff")
//                }
//            }
//            else if (data?.status?.toLowerCase() == "completed") {
//                jobObject = {
//                    "country": "Completed",
//                    "litres": data?.count,
//                    "color": am4core.color("#ff4191")
//                }
//            }
//            else if (data?.status?.toLowerCase() == "success") {
//                jobObject = {
//                    "country": "Success",
//                    "litres": data?.count,
//                    "color": am4core.color("#6eeb34")
//                }
//            }
//            else if (data?.status?.toLowerCase() == "error") {
//                jobObject = {
//                    "country": "Error",
//                    "litres": data.count,
//                    "color": am4core.color("#d92518")
//                }
//            }
//            else {
//                jobObject = {
//                    "country": data?.status,
//                    "litres": data?.count,
//                    "color": am4core.color("#a68785")
//                }
//            }
//            JobData.push(jobObject)
//        })
//    }

//    var chart = am4core.create("JobManagementStatus", am4charts.PieChart);
//    if (chart.logo) {
//        chart.logo.disabled = true;
//    }
//    // Add data
//    chart.data = JobData

//    var pieSeries = chart.series.push(new am4charts.PieSeries());
//    pieSeries.dataFields.value = "litres";
//    pieSeries.dataFields.category = "country";
//    pieSeries.slices.template.propertyFields.fill = "color";
//    pieSeries.slices.template.stroke = am4core.color("#fff");
//    pieSeries.slices.template.strokeWidth = 5;
//    pieSeries.slices.template.strokeOpacity = 5;
//    pieSeries.slices.template.cornerRadius = 20;
//    pieSeries.slices.template.innerCornerRadius = 20;

//    // Let's cut a hole in our Pie chart the size of 40% the radius
//    chart.innerRadius = am4core.percent(65);
//    chart.padding(0, 0, 0, 0);
//    // Disable ticks and labels
//    pieSeries.labels.template.disabled = true;
//    pieSeries.ticks.template.disabled = true;

//    // Disable tooltips
//    //pieSeries.slices.template.tooltipText = "";

//    if (chartdata && chartdata?.length) {
//        pieSeries.slices.template.tooltipText = "{category}: {value}";
//    } else {
//        pieSeries.slices.template.tooltipText = "";
//    }

//    let label2 = chart.seriesContainer.createChild(am4core.Label);

//    if (chartdata && !chartdata?.length) {
//        label2.text = `[bold]NA[/]`;
//    } else {
//        label2.text = `[bold]${totalJobs}[/] \n [font-size:10px] Total Jobs [/]`;
//    }

//    label2.horizontalCenter = "middle";
//    label2.verticalCenter = "middle";
//    label2.textAlign = "middle";
//    label2.fontSize = 17;

//    // Add a legend
//    chart.legend = new am4charts.Legend();
//    // chart.legend.position = "bottom";
//    chart.legend.valueLabels.template.disabled = true;
//    chart.legend.labels.template.text = "[font-size:12px ]{name}";
//    chart.legend.labels.template.fill = am4core.color("#6c757d");

//    // Customize legend layout
//    chart.legend.itemContainers.template.width = am4core.percent(33.33); // 3 items per row
//    chart.legend.itemContainers.template.paddingTop = 5;
//    chart.legend.itemContainers.template.paddingBottom = 5;

//    chart.legend.marginTop = 20;
//    chart.legend.marginBottom = 20;

//    // Place first two items in the bottom row
//    chart.legend.data = chart.data.slice(0, 2).concat(chart.data.slice(2));

//    chart.legend.itemContainers.template.padding(8, 0, 0, 0);
//    // chart.legend.markers.template.children.getIndex(0).cornerRadius(30, 30, 30, 30);
//    var markerTemplate = chart.legend.markers.template;
//    markerTemplate.width = 15;
//    markerTemplate.height = 15;

//    // Adjust chart's height to accommodate legend
//    chart.height = am4core.percent(100);
//    chart.svgContainer.htmlElement.style.height = "400px";

//}

//function airgapTableDetails(airgapdata) {

//    $(".airgapTableDetails").empty()
//    airgapdata?.length && airgapdata.forEach((i, index) => {

//        let source = "NA"
//        let target = "NA"
//        let textStatus = (i?.status?.toLowerCase() == "open" || i?.status?.toLowerCase() === 'enable' || i?.status?.toLowerCase() === 'unlock') ? "text-bg-success" : "text-bg-danger"
//        let textName = (i?.status?.toLowerCase() == "open" || i?.status?.toLowerCase() === 'enable' || i?.status?.toLowerCase() === 'unlock') ? "Open" : "Closed"
//        if (i?.getAirGapsServeListVms != 0) {
//            source = i.getAirGapsServeListVms.filter((s) => s?.type?.toLowerCase() === 'source')
//                .map((s) => s?.name || 'NA')
//                .join(', ');

//            target = i.getAirGapsServeListVms.filter((s) => s?.type?.toLowerCase() === 'target')
//                .map((s) => s?.name || 'NA')
//                .join(', ');

//        }

//        let html = ` <tr><td>${index + 1}</td><td>${i?.name}</td> <td>${source}</td><td>${target}</td><td>${i?.startTime && !isNaN(new Date(i.startTime).getTime()) && i.startTime !== '0001-01-01T00:00:00' ? new Date(i.startTime).toLocaleString() : 'NA'}</td><td>${i?.endTime && !isNaN(new Date(i?.endTime).getTime()) && i?.endTime !== '0001-01-01T00:00:00' ? new Date(i?.endTime).toLocaleString() : 'NA'}</td>
//        <td>${i?.rpo ? i?.rpo : "NA"} </td><td>
//                                    <span class="badge ${textStatus} rounded-pill px-2 py-1 fs-8 fw-normal">${textName}</span>
//                                </td></tr>`
//        $(".airgapTableDetails").append(html)
//    })

//}


//$("#cancelButton").on("click", function (e) {
//    let checkId = e.target.getAttribute("checkId")
//    $("#" + checkId).prop("checked", !e.target.getAttribute("status"))
//})

//function UpdateAirGapStatus(data) {

//    $("#DeleteModalAction").modal("show")

//    $("#confirmButton").attr("airgapId", data.getAttribute("airgapId"))
//    $("#confirmButton").attr("airgapName", data.getAttribute("airgapName"))
//    $("#confirmButton").attr("status", data?.checked)
//    $("#cancelButton").attr("status", data?.checked)
//    $("#cancelButton").attr("checkId", data?.id)

//    if (data?.checked) {
//        $("#deleteStatus").text("enable " + data.getAttribute("airgapName"))
//    }
//    else {
//        $("#deleteStatus").text("disable " + data.getAttribute("airgapName"))
//    }

//}

//$("#confirmButton").on("click", function (e) {

//    let id = e.target.getAttribute("airgapId")
//    let Name = e.target.getAttribute("airgapName")
//    let status = e.target.getAttribute("status") == "true" ? "Open" : "Close"
//    let formData = {
//        __RequestVerificationToken: gettoken(),
//        id: id,
//        name: Name,
//        status: status
//    }
//    $.ajax({
//        type: "PUT",
//        url: "/CyberResiliency/CyberResiliency/UpdateAirGapStatus",
//        data: formData,
//        dataType: "json",
//        success: function (data) {

//            if (data.success) {

//                $('#alertClass').removeClass("info-toast").addClass("success-toast")
//                $('#message').text(data?.message)
//                $('#mytoastrdata').toast({ delay: 3000 });
//                $('#mytoastrdata').toast('show');
//                $(".iconClass").removeClass("cp-exclamation").addClass("cp-check")

//                setTimeout(() => {
//                    GetAirGapsStatus();
//                }, 3000);


//            }
//            else {
//                $('#alertClass').removeClass("success-toast").addClass("info-toast")
//                $('#message').text(data)
//                $(".iconClass").removeClass("cp-check").addClass("cp-exclamation")
//                $('#mytoastrdata').toast({ delay: 3000 });
//                $('#mytoastrdata').toast('show');

//            }
//            $("#DeleteModalAction").modal("hide")
//        }
//    })

//})

//// Call the function only once after 3 seconds (3000 milliseconds)

//$(function () {

//    let selectedValues = [];
//    let dataTable = $('#snap_table').DataTable(

//        {
//            language: {
//                paginate: {
//                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
//                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous" ></i>'
//                }
//            },
//            //dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
//            scrollY: true,
//            deferRender: true,
//            scroller: true,
//            "processing": true,
//            "serverSide": false,
//            "filter": true,
//            "bPaginate": false,
//            "info": false,
//            "order": [],
//            "ajax": {
//                "type": "GET",
//                "url": "/CyberResiliency/Snap/GetPagination",
//                "dataType": "json",
//                "data": function (d) {
//                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
//                    d.pageSize = d.length;
//                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
//                    selectedValues.length = 0;
//                },
//                "dataSrc": function (json) {
//                    json.recordsTotal = json?.data?.totalPages;
//                    json.recordsFiltered = json?.data?.totalCount;
//                    if (json?.data && json?.data?.data && json?.data?.data?.length === 0) {
//                        $(".pagination-column").addClass("disabled")
//                    }
//                    else {
//                        $(".pagination-column").removeClass("disabled")
//                    }

//                    if (json?.data?.data) {
//                        getFilteredStorageGroup(json?.data?.data)

//                    }

//                    return json.data.data;
//                },
//                "error": function (xhr, status, error) {
//                    if (error.status === 401) {
//                        window.location.assign('/Account/Logout')
//                    }
//                },
//            },

//            "columns": [

//                {
//                    "data": "name", "name": "Name", "autoWidth": true,
//                    "render": function (data, type, row) {

//                        if (type === 'display') {
//                            return `<span  title=${data || 'NA'}><i class="cp-snap-1 me-1"></i>${data || 'NA'}</span>`
//                        }
//                        return data;
//                    }
//                },
//                {
//                    "data": "gen", "name": "Gen", "autoWidth": true,
//                    "render": function (data, type, row) {

//                        if (type === 'display') {
//                            return `<span  title=${data || 'NA'}>${data || 'NA'}</span>`
//                        }
//                        return data;
//                    }
//                },

//                //{
//                //    "data": "zone", "name": "Zone", "autoWidth": true,
//                //    "render": function (data, type, row) {

//                //        if (data) {
//                //            if (type === 'display') {
//                //                return `<i class="me-1 cp-location"></i><span  title=${data || 'NA'}>${data || 'NA'}</span>`
//                //            }
//                //            return data;
//                //        }
//                //    }
//                //},

//                //{
//                //    "data": "status", "name": "Status", "autoWidth": true,
//                //    "render": function (data, type, row) {

//                //        if (data) {
//                //            if (type === 'display') {
//                //                if (data == "Immutable") {
//                //                    return `<span  title=${data || 'NA'}><i class="cp-protection-mode me-1"></i>${data || 'NA'}</span>`
//                //                }
//                //                else if (data == "-") {
//                //                    return `<span class="text-center w-100 d-inline-block">${data || 'NA'}</span>`
//                //                }
//                //            }
//                //            return data;
//                //        }
//                //    }
//                //},

//                //{
//                //    "data": "secureStatus", "name": "RetentionPeriod", "autoWidth": true,
//                //    "render": function (data, type, row) {

//                //        if (data) {
//                //            if (type === 'display') {
//                //                return `<span  title=${data || 'NA'}>${data || 'NA'}</span>`
//                //            }
//                //            return data;
//                //        }
//                //    }
//                //},
//                //{
//                //    "data": "size", "name": "size", "autoWidth": true,

//                //    "render": function (data, type, row) {



//                //        if (data) {
//                //            if (type === 'display') {
//                //                return `<span  title=${data || 'NA'}><i class="me-1 cp-control-file-type"></i>${data || 'NA'}</span>`
//                //            }
//                //            return data;
//                //        }

//                //    }
//                //},

//                {
//                    "data": "storageGroupName", "name": "Storage Group", "autoWidth": true,
//                    "render": function (data, type, row) {

//                        if (type === 'display') {
//                            return `<span  title=${data || 'NA'}>${data || 'NA'}</span>`
//                        }
//                        return data;
//                    }
//                },

//                {
//                    "data": "timeStamp", "name": "TimeStamp", "autoWidth": true,

//                    "render": function (data, type, row) {

//                        let formattedDate;
//                        if (data) {
//                            const date = new Date(data);
//                            formattedDate = date.getFullYear() + '-' +
//                                ('0' + (date.getMonth() + 1)).slice(-2) + '-' +
//                                ('0' + date.getDate()).slice(-2) + ' ' +
//                                ('0' + date.getHours()).slice(-2) + ':' +
//                                ('0' + date.getMinutes()).slice(-2) + ':' +
//                                ('0' + date.getSeconds()).slice(-2);
//                        }
//                        else {
//                            formattedDate = "NA"
//                        }
//                        return formattedDate || 'NA'

//                    }
//                },
//                {
//                    "data": "linkedStatus", "name": "Linked Status", "autoWidth": true,
//                    "render": function (data, type, row) {

//                        //if (type === 'display') {
//                        //    return `<span  title=${data || 'NA'}>${data || 'NA'}</span>`
//                        //}

//                        if (type === 'display') {
//                            let tag = data || '-';
//                            tag = tag == 'NA' ? '-' : tag
//                            const iconClass =
//                                tag?.toLowerCase() === "linked" ? "text-bg-success badge" :
//                                    tag?.toLowerCase() === "unlinked" ? "text-bg-danger badge px-1" :
//                                        "";

//                            return `<span class="align-middle ${iconClass} ms-1" > ${tag}</span >`;
//                        }

//                        return data;
//                    }
//                },
//                //{
//                //    "data": "tag", "name": "Tag", "autoWidth": true,
//                //    "render": function (data, type, row) {

//                //        if (type === 'display') {
//                //            const tag = data || 'NA'
//                //            const iconClass =
//                //                tag?.toLowerCase() === "good" ? "text-bg-success" :
//                //                    tag?.toLowerCase() === "bad" ? "text-bg-danger px-2" :
//                //                        "text-bg-secondary";

//                //            return `<span class="align-middle ${iconClass} badge ms-1" > ${tag}</span >`;
//                //        }
//                //        return data;

//                //    }
//                //},

//            ],

//            "columnDefs": [
//                {
//                    "targets": [1],
//                    "className": "truncate"
//                }
//            ],
//            "rowCallback": function (row, data, index) {


//                $('.dataTables_scrollBody').css('height', "calc(50vh - 172px)");
//            },
//            initComplete: function () {

//                $('.paginate_button.page-item.previous').attr('title', 'Previous');
//                $('.paginate_button.page-item.next').attr('title', 'Next');
//            }
//        });
//    dataTable.on('draw.dt', function () {
//        $('.paginate_button.page-item.previous').attr('title', 'Previous');
//        $('.paginate_button.page-item.next').attr('title', 'Next');
//    });


//    $('#search-inp').on('keydown input', commonDebounce(function (e) {

//        if (e.key === '=' || e.key === 'Enter') {
//            e.preventDefault();
//            return false;
//        }
//        const SnapNameCheckbox = $("#Name");

//        const inputValue = $('#search-inp').val();
//        if (SnapNameCheckbox.is(':checked')) {
//            selectedValues.push(SnapNameCheckbox.val() + inputValue);
//        }

//        dataTable.ajax.reload(function (json) {

//            if (json.recordsFiltered === 0) {
//                $('.dataTables_empty').text('No matching records found');
//            }
//        })

//    }, 500));

//    $('#search-inp').attr('autocomplete', 'off');

//})

//const getFilteredStorageGroup = (snapList) => {
//    let groupList = []
//    let html = ''
//    //snapStorageGroupSelect

//    snapList.forEach((d) => {
//        if (!groupList.includes(d.storageGroupName)) {
//            html += `<option value='${d.storageGroupName}'>${d.storageGroupName}</option>`;
//            groupList.push(d.storageGroupName)
//        }
//    })

//    if (groupList && groupList.length) {
//        $('#snapStorageGroupSelect').append(html)
//    }

//}

//function filterTableWithSearchText(inputSelector, tableSelector, numColumns) {
//    $(inputSelector).on("keyup input", function () {
//        var filter = $(this).val();
//        let categoryFlagStatus = true;
//        let counter = 1;

//        $(tableSelector + " tr").each(function () {
//            var $i = 0;
//            var splitText = $(this).text();

//            if (splitText.search(new RegExp(filter, "i")) >= 0) {
//                $i++;
//            }
//            if ($i > 0) {
//                $(this).show();
//                $(this).find("td:first").text(counter); // Update serial number
//                counter++;
//                categoryFlagStatus = false;
//            } else {
//                $(this).hide();
//            }
//        });

//        // If no rows match, show the "No matching records" message
//        if (categoryFlagStatus) {
//            $(tableSelector).append('<tr class="no-records"><td colspan="' + numColumns + '" style="text-align: center;">No matching records found</td></tr>');
//        } else {
//            $(".no-records").remove();
//        }
//    });
//}

//filterTableWithSearchText("#search-infra", ".infraSummaryStautus", 5);
//filterTableWithSearchText("#search-JobStatus", ".jobTableDetails", 7);
//filterTableWithSearchText("#search-AirgapStatus", ".airgapTableDetails", 8);
//filterTableWithSearchText("#search-Alert", ".alertTableDetails", 5);

//$('#btnRemoveSnapSelectDate').on('click', function () {
//    document.getElementById("snapDashboardDate").value = "";
//    const tbody = document.getElementById("snapListTableBody");
//    const rows = tbody.querySelectorAll("tr");
//    rows.forEach(row => {
//        row.removeAttribute("style");
//    });
//    $(".no-records").remove();
//})


//function filterTableWithDate(inputSelector, tableSelector, dateColumnIndex, numColumns) {
//    $(inputSelector).on("keyup input", function () {
//        var filter = $(this).val();
//        var altered = new Date(filter).toString().split(' ')
//        //let SplitData =
//        //var formattedDate = `${parts[0]}/${parts[1]}/${parts[2]}`;
//        let categoryFlagStatus = true;
//        $(tableSelector + " tr").each(function () {
//            var $columns = $(this).find("td");
//            if ($columns.length > 0) {
//                var timeStampText = $columns.eq(dateColumnIndex).text().trim();
//                //var dateOnly = timeStampText.split(" ")[0];

//                //if (dateOnly.includes('-')) {
//                //    formattedDate = `${parts[0]}-${parts[1]}-${parts[2]}`
//                //}

//                if (timeStampText.includes(altered[0]) && timeStampText.includes(altered[1]) && timeStampText.includes(altered[2]) && timeStampText.includes(altered[3])) {
//                    $(this).show();
//                    categoryFlagStatus = false;
//                } else {
//                    $(this).hide();
//                }
//            }
//        });

//        if (categoryFlagStatus) {
//            $(tableSelector).append('<tr class="no-records"><td colspan="' + numColumns + '" style="text-align: center;">No matching records found</td></tr>');
//        } else {
//            $(".no-records").remove();
//        }
//    });
//}

//filterTableWithDate("#snapDashboardDate", "#snapListTableBody", 3, 4);

//function filterTableWithDate(inputSelector, tableSelector, dateColumnIndex, numColumns) {
//    $(inputSelector).on("keyup input", function () {
//        var filter = $(this).val();
//        var altered = new Date(filter).toString().split(' ')
//        //let SplitData =
//        //var formattedDate = `${parts[0]}/${parts[1]}/${parts[2]}`;
//        let categoryFlagStatus = true;
//        $(tableSelector + " tr").each(function () {
//            var $columns = $(this).find("td");
//            if ($columns.length > 0) {
//                var timeStampText = $columns.eq(dateColumnIndex).text().trim();
//                //var dateOnly = timeStampText.split(" ")[0];

//                //if (dateOnly.includes('-')) {
//                //    formattedDate = `${parts[0]}-${parts[1]}-${parts[2]}`
//                //}

//                if (timeStampText.includes(altered[0]) && timeStampText.includes(altered[1]) && timeStampText.includes(altered[2]) && timeStampText.includes(altered[3])) {
//                    $(this).show();
//                    categoryFlagStatus = false;
//                } else {
//                    $(this).hide();
//                }
//            }
//        });

//        if (categoryFlagStatus) {
//            $(tableSelector).append('<tr class="no-records"><td colspan="' + numColumns + '" style="text-align: center;">No matching records found</td></tr>');
//        } else {
//            $(".no-records").remove();
//        }
//    });
//}



//$('#snapStorageGroupSelect').on('change', function () {
//    let selectedValue = $(this).val()

//    if (selectedValue !== 'All') {
//        $('#snapListTableBody' + " tr").each(function () {
//            var $columns = $(this).find("td");
//            if ($columns.length > 0) {
//                var timeStampText = $columns.eq(2).text().trim();

//                if (selectedValue == timeStampText) {
//                    $(this).show();

//                } else {
//                    $(this).hide();
//                }
//            }
//        });
//    } else {
//        const tbody = document.getElementById("snapListTableBody");
//        const rows = tbody.querySelectorAll("tr");
//        rows.forEach(row => {
//            row.removeAttribute("style");
//        });
//    }


//    //if (categoryFlagStatus) {
//    //    $(tableSelector).append('<tr class="no-records"><td colspan="' + numColumns + '" style="text-align: center;">No matching records found</td></tr>');
//    //} else {
//    //    $(".no-records").remove();
//    //}
//})

//$(document).on('click', '.snapFilterList', function () {
//    let value = $(this).attr('value');
//    let categoryFlagStatus = true;
//    $("#snapListTableBody .no-records").remove();
//    if (value !== 'all') {
//        $('#snapListTableBody' + " tr").each(function () {
//            var $columns = $(this).find("td");
//            if ($columns.length > 0) {
//                var timeStampText = $columns.eq(4).text().trim();

//                if (value == timeStampText.toLowerCase()) {
//                    $(this).show();
//                    categoryFlagStatus = false;
//                } else {
//                    $(this).hide();
//                }
//            }
//        });
//    } else {
//        const tbody = document.getElementById("snapListTableBody");
//        const rows = tbody.querySelectorAll("tr");
//        rows.forEach(row => {
//            row.removeAttribute("style");
//        });
//    }

//    if (categoryFlagStatus) {
//        $("#snapListTableBody").append('<tr class="no-records"><td colspan="4" style="text-align: center;">No matching records found</td></tr>');
//    }
//    $('#snapTableDropdown').dropdown('toggle')
//})

//$('#snapSearchFilter').on('click', function () {
//    $('#snapCollapseContainer').removeClass('show')

let noDataImage1 = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">'

GetCyberJobManagementStatus()
GetInfrastructureSummary()
GetCyberSnapsList()
GetCyberAlertsCount()
GetAirGapsStatus()
$(".airgapStatusAccordion").empty()

function GetCyberSnapsList() {

    $.ajax({
        url: "/CyberResiliency/CyberResiliency/GetCyberSnapsList",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (result) {

            if (result?.success) {
                $(".totalOperationalServices").text(result?.data?.totalBusinessServiceCount)
                $(".nonConflictedServices").text(result?.data?.totalNonConflictCount)
                $(".conflictedServices").text(result?.data?.totalConflictCount)
                //resourceConflictScroe(result.data)
            }
            else {
                $(".totalOperationalServices").text("0")
                $(".nonConflictedServices").text("0")
                $(".conflictedServices").text("0")
            }
        }
    })
}

function GetCyberJobList() {

    $("#jobTableDetails").empty()

    $.ajax({
        type: 'GET',
        url: RootUrl + "CyberResiliency/CyberResiliency/GetCyberJobList",
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result.success) {
                let html = '';

                if (result?.data && Array.isArray(result?.data) && result?.data.length) {

                    result?.data.forEach((data, index) => {
                        let statusClassMap = {
                            "pending": "cp-pending text-warning me-1",
                            "running": "text-success cp-reload cp-animate me-1",
                            "success": "cp-success text-success me-1"
                        };

                        let stateClassMap = {
                            "active": "cp-active-inactive text-success me-1",
                            "inactive": "cp-active-inactive text-danger me-1"
                        };

                        let statusClass = statusClassMap[data?.status?.toLowerCase()] || "cp-error text-danger me-1";
                        let stateClass = stateClassMap[data?.state?.toLowerCase()] || "cp-active-inactive text-danger me-1";

                        html += ` <tr><td>${index + 1}</td><td>${data?.name || 'NA'}</td><td>${data?.workflowName || 'NA'}</td>
                    <td>${data?.scheduleTime || 'NA'}</td><td><i title="${data?.status || 'NA'}" class="${statusClass}"></i><span title="${data?.status || 'NA'}">${data?.status || 'NA'}</span></td>
                    <td><i title="${data?.state || 'NA'}" class="${stateClass}"></i><span title="${data?.state || 'NA'}">${data?.state || 'NA'}</span></td>
                    <td>${data?.lastExecutedTime || 'NA'}</td></tr>`

                    })
                } else {
                    html += '<tr class="no-records"><td colspan="8" class="text-center">No matching records found</td></tr>'
                }

                $("#jobTableDetails").append(html)
            }
            else {
                errorNotification(result)
            }
        }
    })
}

function GetCyberAlertList() {

    $("#alertTableDetails").empty()

    $.ajax({
        type: 'GET',
        url: RootUrl + "CyberResiliency/CyberResiliency/GetCyberAlertsCount",
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result.success) {
                let html = '';

                if (result?.data?.cyberAlertListVms && Array.isArray(result?.data?.cyberAlertListVms) && result?.data.cyberAlertListVms.length) {

                    result?.data.cyberAlertListVms.forEach((data, index) => {
                        let severity = data?.severity.toLowerCase();
                        const capitalized = data?.severity?.charAt(0).toUpperCase() + data?.severity?.slice(1).toLowerCase();
                        const createdDate = data?.createdDate;

                        const date = new Date(createdDate);
                        let formattedDate = date.getFullYear() + '-' +
                            ('0' + (date.getMonth() + 1)).slice(-2) + '-' +
                            ('0' + date.getDate()).slice(-2) + ' ' +
                            ('0' + date.getHours()).slice(-2) + ':' +
                            ('0' + date.getMinutes()).slice(-2) + ':' +
                            ('0' + date.getSeconds()).slice(-2);

                        let iconclass = severity === "high" ? "cp-up-doublearrow text-warning" : severity === "critical" ?
                            "cp-critical-level text-danger" : severity === "low" ?
                                "cp-down-doublearrow text-success" : "cp-warning text-primary";

                        html += `<tr><td>${index + 1}</td>
                                     <td>${data?.type}</td>
                                     <td><span  title="${data?.systemMessage || 'NA'}" class="text-truncate" style="max-width:450px;display:inline-block">${data?.systemMessage || 'NA'}</span></td>
                                     <td><i class="me-1 fw-bold ${iconclass}"></i> ${capitalized}</td>
                                     <td>${formattedDate}</td>
                                 </tr>`
                    })
                } else {
                    html += '<tr class="no-records"><td colspan="5" class="text-center">No matching records found</td></tr>'
                }

                $("#alertTableDetails").append(html)

            }
            else {
                errorNotification(result)
            }
        }
    })
}

$('#jobToggle').on('click', function () {
    GetCyberJobList();
    $('#JobModal').modal('show')
})

$('#alertToggle').on('click', function () {
    GetCyberAlertList();
    $('#AlertModal').modal('show')
})

function GetCyberJobManagementStatus() {

    $.ajax({
        url: "/CyberResiliency/CyberResiliency/GetCyberJobManagementStatus",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (result) {

            if (result?.success) {
                if (result?.data != 0) {
                    GetCyberJobManagementStatusChart(result?.data)
                } else {
                    $("#JobManagementStatus").append('<div class="carousel-inner" id="infrasummary" style="text-align: center;"><img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img"></div>')
                }
            }
            else {
                //$(".totalOperationalServices").text("0")
                //$(".nonConflictedServices").text("0")
                //$(".conflictedServices").text("0")
            }
        }
    })
}

function GetAirGapsStatus() {
    $(".airgapStatusAccordion").empty();

    $.ajax({
        url: "/CyberResiliency/CyberResiliency/GetAirGapsStatus",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (result) {

            if (result?.success) {
                if (result?.data?.length != 0) {
                    airgapTableDetails(result?.data)
                    result?.data.forEach((data, index) => {

                        let showindex = index == 0 ? "" : ""
                        let textStatus = data?.status?.toLowerCase() == "open" ? "text-bg-success" : "text-bg-danger";
                        let textName = (data?.status?.toLowerCase() == "open" || data?.status?.toLowerCase() == "enable" || data?.status?.toLowerCase() == "unlock") ? "Open" : "Close";
                        let workflowStatus = data?.workflowStatus?.toLowerCase() === "completed" ? "" : data?.workflowStatus?.toLowerCase() === "running" ? "cp-reload cp-animate" : data?.workflowStatus?.toLowerCase() === "error" ? "cp-affecteds text-danger cp-animate" : "";
                        let nameIcon = (data?.status?.toLowerCase() == "open" || data?.status?.toLowerCase() == "enable" || data?.status?.toLowerCase() == "unlock") ? "cp-open-lock text-success" : "cp-lock text-danger";

                        let html = `<div class="accordion-item"><div class="accordion-header">
                      <button class="accordion-button collapsed" type="button" >
                      <p class="mb-0 flex-fill" style="font-size:12px"><i class='${nameIcon} me-2' title='${textName}'></i>${data?.name || 'NA'} <i class="text-primary ms-2 ${workflowStatus}" style="font-size: 14px;"></i></p><div>
                      <span><i class="cp-down-arrow fs-7" role="button" data-bs-toggle="collapse" data-bs-target="#flush-collapse${index}" aria-expanded="false" aria-controls="flush-collapse${index}"></i></span></button></div>
                            <div id="flush-collapse${index}" class="accordion-collapse collapse ${showindex}" data-bs-parent="#accordionFlushExample">
                                <div class="accordion-body" ><div id="air-gap-diagram${index}" class=" w-100" style="height:65px"></div>`

                        if (data?.getAirGapsServeListVms?.length != 0) {

                            let filteredSource = data?.getAirGapsServeListVms.filter(d => d?.type?.toLowerCase() == 'source')
                            let filteredTarget = data?.getAirGapsServeListVms.filter(d => d?.type?.toLowerCase() == 'target')
                            //<span class="d-block my-1 fs-8 ">Cyber Recovery Vault</span>
                            //<span class="d-block my-1 fs-8">Clean Room</span>
                            //<div class="form-check form-switch"><input class="form-check-input open" type="checkbox" airgapId=${data?.id} airgapName=${data?.name} role="switch" onclick="UpdateAirGapStatus(this)" id="flexSwitchCheckChecked${index}" checked></div></div >

                            //html += `<div><div class="d-flex align-items-center justify-content-between"> <div><p><span class="fs-9 ${data?.getAirGapsServeListVms[0]?.type?.toLowerCase() == 'target' ? 'text-secondary' : 'text-dark fw-semibold'}">${data?.getAirGapsServeListVms[0]?.name || 'NA'}</span><span class="d-block
                            //fs-8 ${data?.getAirGapsServeListVms[0]?.type?.toLowerCase() == 'target' ? 'text-secondary' : 'text-secondary'}">${data?.getAirGapsServeListVms[0]?.type || 'NA'}</span>
                            //                        <span class="fw-semibold d-block my-1"><i class="${data?.getAirGapsServeListVms[0]?.siteName && 'cp-active-inactive'} ${data?.getAirGapsServeListVms[0]?.type?.toLowerCase() == 'target' ? 'text-secondary' : 'text-primary'} me-1"></i>
                            //                        ${data?.getAirGapsServeListVms[0]?.siteName || 'NA'}</span>

                            //                    </p>
                            //                </div><div>

                            //                    <p class=" text-end">
                            //                      <span class="fs-9 text-dark fw-semibold">${data?.getAirGapsServeListVms[1]?.name || 'NA'}</span>
                            //                        <span class="d-block fs-8 text-secondary">
                            //                            ${data?.getAirGapsServeListVms[1]?.type || 'NA'}
                            //                        </span>
                            //                        <span class="fw-semibold d-block my-1"><i class="${data?.getAirGapsServeListVms[1]?.siteName && 'cp-active-inactive'} me-1"></i>${data?.getAirGapsServeListVms[1]?.siteName || 'NA'}</span>

                            //                    </p>

                            //                </div>
                            //            </div>
                            //        </div>`

                            html += `<div><div class="d-flex align-items-center justify-content-between">
                                      <div><p>
                                      <span class="fs-9 text-dark fw-semibold">
                                     ${filteredSource && Array.isArray(filteredSource) && filteredSource.length
                                    ? filteredSource.map((fs) => `<li class="text-dark">
                                    <span class="fs-8 fw-semibold">${fs?.name || 'NA'} </span><br />
                                    <span class="ms-3 fs-9"><i class="cp-ip-address fs-9 me-1"></i>${fs?.ipAddress || 'NA'} </span>
                                    <br />
                                    <span class="ms-3 fs-9"><i class="cp-port fs-9 me-1"></i>${fs?.port || 'NA'} </span>
                                    </li>`).join('')
                                    : ''
                                }
                                      </span>`;

                        }

                        //<span class="fw-semibold d-block my-1">
                        //    <i class="${filteredSource?.length && filteredSource[0]?.siteName ? 'cp-active-inactive' : ''} me-1"></i>
                        //    ${filteredSource?.length && filteredSource[0]?.siteName || 'NA'}
                        //</span> </p ></div ></div ></div >

                        //<div><p class="text-end">
                        //    <span class="fs-9 text-dark fw-semibold">
                        //        ${
                        //            filteredTarget && Array.isArray(filteredTarget) && filteredTarget.length
                        //                ? filteredTarget.map((fs) => `<li>
                        //         <span class="fs-8 fw-semibold">${fs?.name || 'NA'}</span>
                        //            <span class="ms-3 fs-9"><i class="cp-ip-address fs-9 me-1"></i>${fs?.ipAddress || 'NA'}</span>
                        //        </li>`).join('')
                        //                : ''
                        //        }
                        //    </span>
                        //    <span class="d-block fs-8 text-secondary mt-2"> ${filteredTarget?.length && filteredTarget[0]?.type || 'NA'}</span>
                        //    <span class="fw-semibold d-block my-1">
                        //        <i class="${filteredTarget?.length && filteredTarget[0]?.siteName ? 'cp-active-inactive' : ''} me-1"></i>
                        //        ${filteredTarget?.length && filteredTarget[0]?.siteName || 'NA'}
                        //    </span>
                        //</p>
                        //</div>

                        //<div class="py-2 w-50">
                        //                   <span>
                        //                       <span class="d-flex align-items-center gap-1">
                        //                           <span class="p-2 rounded  bg-white"><i class="cp-RTO text-primary align-middle"></i></span>
                        //                           <span class="text-primary">RPO <small class="text-secondary">15 Mins</small> ( 50%)</span>
                        //                       </span>
                        //                   </span>

                        //                   <div class="d-flex gap-1 my-2">
                        //                       <div class="w-50 text-center">
                        //                       <div id="threshold-chart${index}" style="height:80px"></div>
                        //                           <span class="mt-2 fs-8" >Threshold</span>
                        //                       </div>
                        //                       <span>
                        //                           <span class="d-block mb-2">
                        //                               <span class="text-warning">30</span> Mins <br />
                        //                               Configuration
                        //                            </span>
                        //                           <span class="d-block">
                        //                               <span class="text-warning">0</span> Mins <br />
                        //                               Computed
                        //                           </span>
                        //                       </span>
                        //                   </div>
                        //               </div>
                        //               <div class="vr bg-secondary-subtle"></div>
                        let rpo = data?.rpo ? data?.rpo : "NA"
                        html += `<div class="border-light-subtle border-top d-flex align-items-center justify-content-between">
                                       
                                        <div class="py-2">
                             
                                            <table class="table table-sm table-borderless mb-0">
                                                <thead>
                                                    <tr>
                                                        <th colspan="3" class="bg-transparent fw-normal text-primary">Last Activity</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            Opened Time
                                                        </td>
                                                        <td>
                                                            :
                                                        </td>
                                                        <td>
                                                            ${data?.startTime && !isNaN(new Date(data.startTime).getTime()) && data.startTime !== '0001-01-01T00:00:00'
                                ? new Date(data.startTime).toLocaleString()
                                : '-'}  

                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Closed Time
                                                        </td>
                                                        <td>
                                                            :
                                                        </td>
                                                        <td>
                                                          ${data?.endTime && !isNaN(new Date(data.endTime).getTime()) && data.endTime !== '0001-01-01T00:00:00'
                                ? new Date(data.endTime).toLocaleString()
                                : '-'}
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            Time Period
                                                        </td>
                                                        <td>
                                                            :
                                                        </td>
                                                        <td>
                                                            ${rpo}
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                       
                                    </div>
                              
                                </div>
                            </div>
                        </div>`
                        $(".airgapStatusAccordion").append(html)

                        if (data?.getAirGapsServeListVms != 0) {
                            airgapDiagramChart('air-gap-diagram' + index, data)
                        }
                        threshouldChart("threshold-chart" + index)

                        data?.status?.toLowerCase() == "open" || data?.status?.toLowerCase() == "enable" || data?.status?.toLowerCase() == "unlock" ? $("#flexSwitchCheckChecked" + index).prop("checked", true) : $("#flexSwitchCheckChecked" + index).prop("checked", false)
                    })
                }
                else {

                    $(".airgapStatusAccordion").append('<div class="carousel-inner" id="infrasummary" style="text-align: center;"><img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img"></div>')
                    //$(".totalOperationalServices").text("0")
                    //$(".nonConflictedServices").text("0")
                    //$(".conflictedServices").text("0")
                }
            }
        }
    })

}
function GetCyberAlertsCount() {

    $.ajax({
        url: "/CyberResiliency/CyberResiliency/GetCyberAlertsCount",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (result) {

            if (result?.success) {
                //if (result.data != 0) {
                GetCyberAlertsCountChart(result?.data)
                //}
            }
            else {
                //$(".totalOperationalServices").text("0")
                //$(".nonConflictedServices").text("0")
                //$(".conflictedServices").text("0")
            }
        }
    })
}

function airgapDiagramChart(id, airgapData) {
    //document.getElementById("tableData").innerHTML = counts;

    let airgapArray = []
    airgapData?.getAirGapsServeListVms?.length && airgapData?.getAirGapsServeListVms.forEach((data, index) => {

        let x = 0
        let y = 0
        let isAlreadyTypeExist = airgapArray?.length && airgapArray.some(d => d?.siteName?.toLowerCase() == data?.siteName?.toLowerCase())

        if (index == 0) {
            x = 10
            y = 30
        }
        else {
            x = 90
            y = 30
        }
        airgapArray.push(
            {
                name: data.name,
                fixed: true,

                x: am4core.percent(x),
                y: am4core.percent(y),
                tag: "✔",
                ip: data?.ipAddress,
                Connection: "HTTP",
                Service: "Monitor Service",
                siteName: !isAlreadyTypeExist ? data?.siteName : '',
                host: "CD030",
                Port: "6002",
                value: 10,
                image: "/img/charts_img/DataCenter/server.svg",

            })
    })
    am4core.useTheme(am4themes_animated);

    // Create chart
    var chart = am4core.create(id,
        am4plugins_forceDirected.ForceDirectedTree
    );
    if (chart.logo) {
        chart.logo.disabled = true;
    }
    // Create series
    var series = chart.series.push(
        new am4plugins_forceDirected.ForceDirectedSeries()
    );
    chart.colors.list = [am4core.color("#ff9c0d"), am4core.color("#40c200")];
    series.data = [{
        name: "Air Gap 1",
        fixed: true,
        x: am4core.percent(50),
        y: am4core.percent(30),
        ip: "",
        Connection: "HTTP",
        Service: "Monitor Service",
        host: "CD030",
        Port: "6002",
        value: 15,
        image: (airgapData?.status?.toLowerCase() === 'open' || airgapData?.status?.toLowerCase() === 'enable' || airgapData?.status?.toLowerCase() === 'unlock') ? "/img/charts_img/DataCenter/cyber-lock-open.svg" : "/img/charts_img/DataCenter/cyber-lock.svg",
        children: airgapArray
    },];

    // Set up data fields
    series.dataFields.value = "value";
    series.dataFields.fixed = "fixed";
    series.dataFields.name = "name";
    series.dataFields.ip = "ip";
    series.dataFields.Connection = "Connection";
    (series.dataFields.Service = "Service"),
        (series.dataFields.host = "host"),
        (series.dataFields.Port = "Port"),
        (series.dataFields.id = "id");
    series.dataFields.children = "children";
    series.dataFields.tag = "tag";
    series.dataFields.linkWith = "link";
    series.dataFields.id = "name";
    series.manyBodyStrength = -18;
    // Add labels
    series.nodes.template.label.text = "{siteName}";
    series.nodes.template.label.valign = "bottom";

    series.nodes.template.label.fill = am4core.color("#000");
    series.nodes.template.label.dy = -5;
    series.nodes.template.label.fontSize = 10;
    series.nodes.template.label.fontWeight = "bold";

    series.nodes.template.tooltipText =
        "[font-size: 15px; #0479ff; ]{name}\n[/] IP Address : [bold]{ip} [/]\n Connection Type : [bold]{Connection} [/] \n Service Type : [bold]{Service}[/]\n Host Name : [bold]{host}[/]\n Port : [bold]{Port}[/]\n";
    series.fontSize = 11;
    series.minRadius = 35;
    series.maxRadius = 35;

    series.tooltip.autoTextColor = false;
    series.tooltip.getFillFromObject = false;
    series.tooltip.label.fill = am4core.color("#1A1A1A");
    series.tooltip.label.background.fill = am4core.color("#fff");

    series.links.template.strokeWidth = 1;
    series.links.template.strokeDasharray = "5,3";
    series.nodes.template.circle.strokeWidth = 0;
    series.nodes.template.circle.disabled = true;
    series.nodes.template.outerCircle.disabled = true;

    series.dataFields.fixed = "fixed";
    series.nodes.template.propertyFields.x = "x";
    series.nodes.template.propertyFields.y = "y";



    // Change the padding values
    // chart.padding(-15, -15, -15, -15)

    // Configure icons
    var icon = series.nodes.template.createChild(am4core.Image);
    icon.propertyFields.href = "image";
    icon.horizontalCenter = "middle";
    icon.verticalCenter = "middle";
    icon.width = 40;
    icon.height = 40;
    series.centerStrength = 0.5;

}

let globalEntityData;
function GetInfrastructureSummary() {
    $.ajax({
        url: "/CyberResiliency/CyberResiliency/GetInfrastructureSummaryCount",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (result) {

            if (result?.success) {

                if (result?.data && result?.data?.length && result?.data != 0) {
                    $(".infrastructureSummary").empty();
                    globalEntityData = result?.data
                    result?.data.forEach((data) => {




                        let dynamicIcon = data?.key?.toLowerCase();
                        let upcount = 0
                        let downcount = 0
                        //let Name= data.entityType.toLowerCase() == "database" ? "Database" : data.entityType.toLowerCase() == "server" ? "Server" : data.entityType.toLowerCase() == "switch" ? "Switches" : data.entityType.toLowerCase() == "storage" ?"Storage":"Server"

                        if (data?.value && data?.value?.length && data?.value != 0) {
                            let serverInfrastructure = data?.value.forEach((Summarycount) => {
                                if (Summarycount?.status?.toLowerCase() == "up") {
                                    upcount += 1
                                }
                                else {
                                    downcount += 1
                                }
                            })
                        }

                        let parentimage = dynamicIcon === "database" ? "/img/layouts_img/Cyber-BG/Database_BG.svg"
                            : dynamicIcon === "server" ? "/img/layouts_img/Cyber-BG/Server_BG.svg"
                                : dynamicIcon === "storage" ? "/img/layouts_img/Cyber-BG/Storages_BG.svg"
                                    : dynamicIcon === "thirdparty" ? "/img/layouts_img/Cyber-BG/ThirdParty_BG.svg"
                                        : dynamicIcon === "virtualization" ? "/img/layouts_img/Cyber-BG/Virtualization_BG.svg"
                                            : dynamicIcon === "application" ? "/img/layouts_img/Cyber-BG/Application_BG.svg"
                                                : dynamicIcon === "dns" ? "/img/layouts_img/Cyber-BG/DNS_BG.svg"
                                                    : "/img/layouts_img/Cyber-BG/Switches_BG.svg";

                        let image = dynamicIcon === "database" ? "/img/charts_img/datacenter/database.svg"
                            : dynamicIcon === "server" ? "/img/charts_img/datacenter/server.svg"
                                : dynamicIcon === "storage" ? "/img/charts_img/datacenter/storage.svg"
                                    : dynamicIcon === "thirdparty" ? "/img/charts_img/datacenter/thirdparty.svg"
                                        : dynamicIcon === "virtualization" ? "/img/charts_img/datacenter/virtualization.svg"
                                            : dynamicIcon === "application" ? "/img/charts_img/datacenter/application.svg"
                                                : dynamicIcon === "dns" ? "/img/charts_img/datacenter/dns.svg"
                                                    : "/img/charts_img/datacenter/Switches.svg"

                        let html = `<div class="col d-grid">
                            <div class="card mb-0 h-100" style="background-image: url('${parentimage}');background-size:cover; background-repeat: no-repeat;">
                                <div class="card-body p-2">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="d-grid">
                                            <span class="fw-bold">
                                                ${data?.key || 'NA'}
                                            </span>
                                            <span class="fs-5 fw-semibold">
                                                ${data?.value?.length || 0}
                                            </span>
                                        </div>
                                        <span><img src="${image}" /></span>
                                    </div>
                                </div>
                                <div class="card-footer p-2 pt-0 d-flex align-items-center gap-3">
                                    <span class="fs-6"  onclick="${upcount > 0 ? `InfrastructureSummarydata('${data?.key}','up')` : ''}" role='${upcount > 0 ? "button" : ""}'>${upcount}<i class="cp-up-linearrow ms-1 text-success fs-8 align-middle"></i></span>
                                    <span class="fs-6" onclick="${downcount > 0 ? `InfrastructureSummarydata('${data?.key}','down')` : ''}" role='${downcount > 0 ? "button" : ""}'>${downcount}<i class="cp-down-linearrow ms-1 text-danger fs-8 align-middle"></i></span>
                                </div>
                            </div>
                        </div>`

                        $(".infrastructureSummary").append(html)
                    })
                } else {
                    $(".infrastructureSummary").append('<div class="carousel-inner" id="infrasummary" style="text-align: center;"><img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img"></div>')
                }
            }
            else {
                //$(".totalOperationalServices").text("0")
                //$(".nonConflictedServices").text("0")
                //$(".conflictedServices").text("0")
            }
        }
    })
}

function InfrastructureSummarydata(type = null, letstatus = null) {

    $("#InfrastructureSummaryModal").modal("show")
    $(".infraSummaryStautus").empty()
    let infraCount = 0;

    globalEntityData && globalEntityData?.length && globalEntityData.forEach((data, i) => {

        if (data?.key == type) {

            if (data?.value && data?.value?.length) {
                let upNo = 0
                let downNo = 0

                data?.value.forEach((dataChild, j) => {
                    let propertices = JSON.parse(dataChild.properties)
                    let ipHost = propertices?.IpAddress != "NA" && propertices?.HostName != "NA" ? propertices?.IpAddress + "/" + propertices?.HostName : "NA"
                    let status = dataChild?.status?.toLowerCase() == "up" ? "text-success" : dataChild?.status?.toLowerCase() == "down" ? "text-danger" : "text-warning"
                    let textName = dataChild?.status?.toLowerCase() == "up" ? "UP" : dataChild?.status?.toLowerCase() == "down" ? "Down" : "Pending"
                    if (letstatus == "up" && dataChild?.status?.toLowerCase() == "up") {
                        let html = `<tr>
                <td>${upNo += 1}</td>
                 <td>${dataChild?.name || 'NA'}</td>
                  <td>${ipHost || 'NA'}</td>
                   <td>${dataChild?.siteName || 'NA'}</td>
                    <td><i class="cp-up-linearrow me-1 ${status}"></i>${textName || 'NA'}</td>
                            </tr>`
                        $(".infraSummaryStautus").append(html)
                    }
                    else if (dataChild?.status?.toLowerCase() == "down" && letstatus == "down" || dataChild?.status?.toLowerCase() == "pending" && letstatus == "down") {
                        let html = `<tr>
                <td>${downNo += 1}</td>
                 <td>${dataChild?.name || 'NA'}</td>
                  <td>${ipHost || 'NA'}</td>
                   <td>${dataChild?.siteName || 'NA'}</td>
                    <td><i class="cp-down-linearrow me-1 ${status}"></i>${textName || 'NA'}</td>
                            </tr>`
                        $(".infraSummaryStautus").append(html)
                    }
                })

            }
        }
        if (type === null && letstatus == null) {
            if (data?.value && data?.value?.length) {

                data?.value.forEach((dataChild, j) => {
                    let propertices = JSON.parse(dataChild.properties)
                    let ipHost = propertices?.HpAddress != "NA" && propertices?.HostName != "NA" ? propertices?.IpAddress + "/" + propertices?.HostName : "NA"
                    let status = dataChild?.status?.toLowerCase() == "up" ? "text-success" : dataChild?.status?.toLowerCase() == "down" ? "text-danger" : "text-warning"
                    let textName = dataChild?.status?.toLowerCase() == "up" ? "UP" : dataChild?.status?.toLowerCase() == "down" ? "Down" : "Pending"

                    let html = `<tr>
                                    <td>${infraCount += 1}</td>
                                    <td>${dataChild?.name || 'NA'}</td>
                                    <td>${ipHost || 'NA'}</td>
                                    <td>${dataChild?.siteName || 'NA'}</td>
                                    <td><i class="cp-up-linearrow me-1 ${status}"></i>${textName || 'NA'}</td>
                                </tr>`
                    $(".infraSummaryStautus").append(html)

                })

            }
        }
    })

}

function GetCyberAlertsCountChart(alertData) {

    let alertSeverityCount = alertData?.alertSeverityCount
    let CyberAlertData = []
    let alertObject;


    Object.keys(alertSeverityCount).forEach(function (key) {

        if (key?.toLowerCase() == "information") {
            alertObject = {
                "country": "Information",
                "litres": alertSeverityCount[key],
                "color": am4core.color("#8960ff")
            }

        }
        else if (key?.toLowerCase() == "warning") {
            alertObject = {
                "country": "Warning",
                "litres": alertSeverityCount[key],
                "color": am4core.color("#ffcb00")
            }
        }
        else if (key?.toLowerCase() == "critical") {
            alertObject = {
                "country": "Critical",
                "litres": alertSeverityCount[key],
                "color": am4core.color("#ff003c")
            }
        }
        else if (key?.toLowerCase() == "high") {
            alertObject = {
                "country": "High",
                "litres": alertSeverityCount[key],
                "color": am4core.color("#ffc107")
            }
        }
        else if (key?.toLowerCase() == "low") {
            alertObject = {
                "country": "Low",
                "litres": alertSeverityCount[key],
                "color": am4core.color("#00ff04")
            }
        }
        CyberAlertData.push(alertObject)

    });


    if (alertSeverityCount?.information) {
        $(".informationStatus").removeClass("d-none")
        $(".informationCount").text(alertSeverityCount?.information || 0)
    }
    if (alertSeverityCount?.warning) {
        $(".warningStatus").removeClass("d-none")
        $(".warningCount").text(alertSeverityCount?.warning || 0)
    }
    if (alertSeverityCount.high) {
        $(".highStatus").removeClass("d-none")
        $(".highCount").text(alertSeverityCount.high || 0)
    }
    if (alertSeverityCount.critical) {
        $(".criticalStatus").removeClass("d-none")
        $(".criticalCount").text(alertSeverityCount.critical || 0)
    }
    if (alertSeverityCount.low) {
        $(".lowStatus").removeClass("d-none")
        $(".lowCount").text(alertSeverityCount.low || 0)
    }
    var chart = am4core.create("CyberAlertsChart", am4charts.PieChart);
    if (chart.logo) {
        chart.logo.disabled = true;
    }

    chart.data = CyberAlertData;

    chart.startAngle = 140;
    chart.endAngle = 400;
    // Add and configure Series
    var pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "litres";
    pieSeries.dataFields.category = "country";
    pieSeries.slices.template.propertyFields.fill = "color";
    pieSeries.slices.template.stroke = am4core.color("#fff");
    pieSeries.slices.template.strokeWidth = 5;
    pieSeries.slices.template.strokeOpacity = 5;
    pieSeries.slices.template.cornerRadius = 20;
    pieSeries.slices.template.innerCornerRadius = 20;
    // Let's cut a hole in our Pie chart the size of 40% the radius
    chart.innerRadius = am4core.percent(65);
    chart.padding(-10, -20, -10, -20);

    // Disable ticks and labels
    pieSeries.labels.template.disabled = true;
    pieSeries.ticks.template.disabled = true;

    // Disable tooltips
    //pieSeries.slices.template.tooltipText = "";



    // // Add a legend
    // chart.legend = new am4charts.Legend();
    // chart.legend.position = "bottom";
    // chart.legend.valueLabels.template.disabled = true;
    // chart.legend.labels.template.text = "[font-size:12px ]{name}";
    // chart.legend.labels.template.fill = am4core.color("#6c757d");
    // chart.legend.markers.template.children.getIndex(0).cornerRadius(30, 30, 30, 30);
    // var markerTemplate = chart.legend.markers.template;
    // markerTemplate.width = 10;
    // markerTemplate.height = 10;

    let label1 = chart.seriesContainer.createChild(am4core.Label);
    label1.text = `[bold]${alertData.totalCount}[/] \n [font-size:12px] Total [/]`;
    label1.horizontalCenter = "middle";
    label1.verticalCenter = "middle";
    label1.textAlign = "middle";
    label1.fontSize = 17;


}


function threshouldChart(id) {


    am4core.useTheme(am4themes_animated);

    // Create chart instance
    var chart = am4core.create(id, am4charts.RadarChart);
    if (chart.logo) {
        chart.logo.disabled = true;
    }
    // Add data
    chart.data = [{
        "category": "",
        "value": 50,
        // "value1": 40,
        "full": 100
    },];

    // Make chart not full circle
    chart.startAngle = 0;
    chart.endAngle = 360;
    chart.innerRadius = am4core.percent(80);

    // Set number format
    chart.numberFormatter.numberFormat = "";

    // Create axes
    var categoryAxis = chart.yAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "category";
    // categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.grid.template.strokeOpacity = 0;



    categoryAxis.renderer.minGridDistance = 100;

    var valueAxis = chart.xAxes.push(new am4charts.ValueAxis());
    valueAxis.renderer.grid.template.strokeOpacity = 0;
    valueAxis.min = 0;
    valueAxis.max = 100;
    categoryAxis.renderer.grid.push(new am4charts.Grid()).disabled = true;
    valueAxis.renderer.grid.push(new am4charts.Grid()).disabled = true;
    // valueAxis.strictMinMax = false;

    // Create series
    var series1 = chart.series.push(new am4charts.RadarColumnSeries());
    series1.dataFields.valueX = "full";
    series1.dataFields.categoryY = "category";
    series1.clustered = false;
    // Set the fill color of the columns to red
    series1.columns.template.fill = new am4core.InterfaceColorSet().getFor("alternativeBackground");

    // Optionally, you can also set the stroke color
    series1.columns.template.stroke = am4core.color("green");
    series1.columns.template.fillOpacity = 0.08;
    series1.columns.template.cornerRadiusTopLeft = 100;
    series1.columns.template.strokeWidth = 0;
    series1.columns.template.radarColumn.cornerRadius = 100;

    series1.columns.template.rotation = 300; // Start from the top


    // // chart.innerRadius = 100;
    var label = chart.seriesContainer.createChild(am4core.Label);
    label.text = "50%";
    label.horizontalCenter = "middle";
    label.verticalCenter = "middle";
    label.fontSize = 10;
    label.rotation = 0;

    var series2 = chart.series.push(new am4charts.RadarColumnSeries());
    series2.dataFields.valueX = "value";
    series2.dataFields.categoryY = "category";
    series2.clustered = false;
    series2.columns.template.strokeWidth = 0;
    series2.columns.template.radarColumn.cornerRadius = 100;
    series2.columns.template.fill = am4core.color("green");


    chart.padding(0, 0, 0, 0);


}

function GetCyberJobManagementStatusChart(chartdata) {

    let JobData = []
    let jobObject;
    let totalJobs = 0

    if (chartdata && !chartdata?.length) {
        JobData.push(
            {
                "country": "No Data",
                "litres": 1,
                "color": am4core.color("#d3d3d3")
            }
        );
    } else {
        chartdata?.length && chartdata.forEach((data) => {
            totalJobs += data.count
            if (data.status.toLowerCase() == "pending") {
                jobObject = {
                    "country": "Pending",
                    "litres": data?.count,
                    "color": am4core.color("#ffcb00")
                }

            }
            else if (data.status.toLowerCase() == "running") {
                jobObject = {
                    "country": "Running",
                    "litres": data?.count,
                    "color": am4core.color("#00acef")
                }
            }
            else if (data.status.toLowerCase() == "abort") {
                jobObject = {
                    "country": "Abort",
                    "litres": data?.count,
                    "color": am4core.color("#8960ff")
                }
            }
            else if (data?.status?.toLowerCase() == "completed") {
                jobObject = {
                    "country": "Completed",
                    "litres": data?.count,
                    "color": am4core.color("#ff4191")
                }
            }
            else if (data?.status?.toLowerCase() == "success") {
                jobObject = {
                    "country": "Success",
                    "litres": data?.count,
                    "color": am4core.color("#6eeb34")
                }
            }
            else if (data?.status?.toLowerCase() == "error") {
                jobObject = {
                    "country": "Error",
                    "litres": data.count,
                    "color": am4core.color("#d92518")
                }
            }
            else {
                jobObject = {
                    "country": data?.status,
                    "litres": data?.count,
                    "color": am4core.color("#a68785")
                }
            }
            JobData.push(jobObject)
        })
    }

    var chart = am4core.create("JobManagementStatus", am4charts.PieChart);
    if (chart.logo) {
        chart.logo.disabled = true;
    }
    // Add data
    chart.data = JobData

    var pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "litres";
    pieSeries.dataFields.category = "country";
    pieSeries.slices.template.propertyFields.fill = "color";
    pieSeries.slices.template.stroke = am4core.color("#fff");
    pieSeries.slices.template.strokeWidth = 5;
    pieSeries.slices.template.strokeOpacity = 5;
    pieSeries.slices.template.cornerRadius = 20;
    pieSeries.slices.template.innerCornerRadius = 20;

    // Let's cut a hole in our Pie chart the size of 40% the radius
    chart.innerRadius = am4core.percent(65);
    chart.padding(0, 0, 0, 0);
    // Disable ticks and labels
    pieSeries.labels.template.disabled = true;
    pieSeries.ticks.template.disabled = true;

    // Disable tooltips
    //pieSeries.slices.template.tooltipText = "";

    if (chartdata && chartdata?.length) {
        pieSeries.slices.template.tooltipText = "{category}: {value}";
    } else {
        pieSeries.slices.template.tooltipText = "";
    }

    let label2 = chart.seriesContainer.createChild(am4core.Label);

    if (chartdata && !chartdata?.length) {
        label2.text = `[bold]NA[/]`;
    } else {
        label2.text = `[bold]${totalJobs}[/] \n [font-size:10px] Total Jobs [/]`;
    }

    label2.horizontalCenter = "middle";
    label2.verticalCenter = "middle";
    label2.textAlign = "middle";
    label2.fontSize = 17;

    // Add a legend
    chart.legend = new am4charts.Legend();
    // chart.legend.position = "bottom";
    chart.legend.valueLabels.template.disabled = true;
    chart.legend.labels.template.text = "[font-size:12px ]{name}";
    chart.legend.labels.template.fill = am4core.color("#6c757d");

    // Customize legend layout
    chart.legend.itemContainers.template.width = am4core.percent(33.33); // 3 items per row
    chart.legend.itemContainers.template.paddingTop = 5;
    chart.legend.itemContainers.template.paddingBottom = 5;

    chart.legend.marginTop = 20;
    chart.legend.marginBottom = 20;

    // Place first two items in the bottom row
    chart.legend.data = chart.data.slice(0, 2).concat(chart.data.slice(2));

    chart.legend.itemContainers.template.padding(8, 0, 0, 0);
    // chart.legend.markers.template.children.getIndex(0).cornerRadius(30, 30, 30, 30);
    var markerTemplate = chart.legend.markers.template;
    markerTemplate.width = 15;
    markerTemplate.height = 15;

    // Adjust chart's height to accommodate legend
    chart.height = am4core.percent(100);
    chart.svgContainer.htmlElement.style.height = "400px";

}

function airgapTableDetails(airgapdata) {

    $(".airgapTableDetails").empty()
    airgapdata?.length && airgapdata.forEach((i, index) => {

        let source = "NA"
        let target = "NA"
        let textStatus = (i?.status?.toLowerCase() == "open" || i?.status?.toLowerCase() === 'enable' || i?.status?.toLowerCase() === 'unlock') ? "text-bg-success" : "text-bg-danger"
        let textName = (i?.status?.toLowerCase() == "open" || i?.status?.toLowerCase() === 'enable' || i?.status?.toLowerCase() === 'unlock') ? "Open" : "Closed"
        if (i?.getAirGapsServeListVms != 0) {
            source = i.getAirGapsServeListVms.filter((s) => s?.type?.toLowerCase() === 'source')
                .map((s) => s?.name || 'NA')
                .join(', ');

            target = i.getAirGapsServeListVms.filter((s) => s?.type?.toLowerCase() === 'target')
                .map((s) => s?.name || 'NA')
                .join(', ');

        }

        let html = ` <tr><td>${index + 1}</td><td>${i?.name}</td> <td>${source}</td><td>${target}</td><td>${i?.startTime && !isNaN(new Date(i.startTime).getTime()) && i.startTime !== '0001-01-01T00:00:00' ? new Date(i.startTime).toLocaleString() : 'NA'}</td><td>${i?.endTime && !isNaN(new Date(i?.endTime).getTime()) && i?.endTime !== '0001-01-01T00:00:00' ? new Date(i?.endTime).toLocaleString() : 'NA'}</td>
        <td>${i?.rpo ? i?.rpo : "NA"} </td><td>
                                    <span class="badge ${textStatus} rounded-pill px-2 py-1 fs-8 fw-normal">${textName}</span>
                                </td></tr>`
        $(".airgapTableDetails").append(html)
    })

}


$("#cancelButton").on("click", function (e) {
    let checkId = e.target.getAttribute("checkId")
    $("#" + checkId).prop("checked", !e.target.getAttribute("status"))
})

function UpdateAirGapStatus(data) {

    $("#DeleteModalAction").modal("show")

    $("#confirmButton").attr("airgapId", data.getAttribute("airgapId"))
    $("#confirmButton").attr("airgapName", data.getAttribute("airgapName"))
    $("#confirmButton").attr("status", data?.checked)
    $("#cancelButton").attr("status", data?.checked)
    $("#cancelButton").attr("checkId", data?.id)

    if (data?.checked) {
        $("#deleteStatus").text("enable " + data.getAttribute("airgapName"))
    }
    else {
        $("#deleteStatus").text("disable " + data.getAttribute("airgapName"))
    }

}

$("#confirmButton").on("click", function (e) {

    let id = e.target.getAttribute("airgapId")
    let Name = e.target.getAttribute("airgapName")
    let status = e.target.getAttribute("status") == "true" ? "Open" : "Close"
    let formData = {
        __RequestVerificationToken: gettoken(),
        id: id,
        name: Name,
        status: status
    }
    $.ajax({
        type: "PUT",
        url: "/CyberResiliency/CyberResiliency/UpdateAirGapStatus",
        data: formData,
        dataType: "json",
        success: function (data) {

            if (data.success) {

                $('#alertClass').removeClass("info-toast").addClass("success-toast")
                $('#message').text(data?.message)
                $('#mytoastrdata').toast({ delay: 3000 });
                $('#mytoastrdata').toast('show');
                $(".iconClass").removeClass("cp-exclamation").addClass("cp-check")

                setTimeout(() => {
                    GetAirGapsStatus();
                }, 3000);


            }
            else {
                $('#alertClass').removeClass("success-toast").addClass("info-toast")
                $('#message').text(data)
                $(".iconClass").removeClass("cp-check").addClass("cp-exclamation")
                $('#mytoastrdata').toast({ delay: 3000 });
                $('#mytoastrdata').toast('show');

            }
            $("#DeleteModalAction").modal("hide")
        }
    })

})

// Call the function only once after 3 seconds (3000 milliseconds)

$(function () {

    let selectedValues = [];
    let dataTable = $('#snap_table').DataTable(

        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous" ></i>'
                }
            },
            //dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": false,
            "filter": true,
            "bPaginate": false,
            "info": false,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/CyberResiliency/Snap/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    json.recordsTotal = json?.data?.totalPages;
                    json.recordsFiltered = json?.data?.totalCount;
                    if (json?.data && json?.data?.data && json?.data?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }

                    if (json?.data?.data) {
                        getFilteredStorageGroup(json?.data?.data)

                    }

                    return json.data.data;
                },
                "error": function (xhr, status, error) {
                    if (error.status === 401) {
                        window.location.assign('/Account/Logout')
                    }
                },
            },

            "columns": [

                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<span  title=${data || 'NA'}><i class="cp-snap-1 me-1"></i>${data || 'NA'}</span>`
                        }
                        return data;
                    }
                },
                {
                    "data": "gen", "name": "Gen", "autoWidth": true,
                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<span  title=${data || 'NA'}>${data || 'NA'}</span>`
                        }
                        return data;
                    }
                },

                //{
                //    "data": "zone", "name": "Zone", "autoWidth": true,
                //    "render": function (data, type, row) {

                //        if (data) {
                //            if (type === 'display') {
                //                return `<i class="me-1 cp-location"></i><span  title=${data || 'NA'}>${data || 'NA'}</span>`
                //            }
                //            return data;
                //        }
                //    }
                //},

                //{
                //    "data": "status", "name": "Status", "autoWidth": true,
                //    "render": function (data, type, row) {

                //        if (data) {
                //            if (type === 'display') {
                //                if (data == "Immutable") {
                //                    return `<span  title=${data || 'NA'}><i class="cp-protection-mode me-1"></i>${data || 'NA'}</span>`
                //                }
                //                else if (data == "-") {
                //                    return `<span class="text-center w-100 d-inline-block">${data || 'NA'}</span>`
                //                }
                //            }
                //            return data;
                //        }
                //    }
                //},

                //{
                //    "data": "secureStatus", "name": "RetentionPeriod", "autoWidth": true,
                //    "render": function (data, type, row) {

                //        if (data) {
                //            if (type === 'display') {
                //                return `<span  title=${data || 'NA'}>${data || 'NA'}</span>`
                //            }
                //            return data;
                //        }
                //    }
                //},
                //{
                //    "data": "size", "name": "size", "autoWidth": true,

                //    "render": function (data, type, row) {



                //        if (data) {
                //            if (type === 'display') {
                //                return `<span  title=${data || 'NA'}><i class="me-1 cp-control-file-type"></i>${data || 'NA'}</span>`
                //            }
                //            return data;
                //        }

                //    }
                //},

                {
                    "data": "storageGroupName", "name": "Storage Group", "autoWidth": true,
                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<span  title=${data || 'NA'}>${data || 'NA'}</span>`
                        }
                        return data;
                    }
                },

                {
                    "data": "timeStamp", "name": "TimeStamp", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<span  title=${data || 'NA'}>${data || 'NA'}</span>`
                        }
                        return data;

                    }
                },
                {
                    "data": "linkedStatus", "name": "Linked Status", "autoWidth": true,
                    "render": function (data, type, row) {

                        //if (type === 'display') {
                        //    return `<span  title=${data || 'NA'}>${data || 'NA'}</span>`
                        //}

                        if (type === 'display') {
                            let tag = data || '-';
                            tag = tag == 'NA' ? '-' : tag
                            const iconClass =
                                tag?.toLowerCase() === "linked" ? "text-bg-success badge" :
                                    tag?.toLowerCase() === "unlinked" ? "text-bg-danger badge px-1" :
                                        "";

                            return `<span class="align-middle ${iconClass} ms-1" > ${tag}</span >`;
                        }

                        return data;
                    }
                },
                //{
                //    "data": "tag", "name": "Tag", "autoWidth": true,
                //    "render": function (data, type, row) {

                //        if (type === 'display') {
                //            const tag = data || 'NA'
                //            const iconClass =
                //                tag?.toLowerCase() === "good" ? "text-bg-success" :
                //                    tag?.toLowerCase() === "bad" ? "text-bg-danger px-2" :
                //                        "text-bg-secondary";

                //            return `<span class="align-middle ${iconClass} badge ms-1" > ${tag}</span >`;
                //        }
                //        return data;

                //    }
                //},

            ],

            "columnDefs": [
                {
                    "targets": [1],
                    "className": "truncate"
                }
            ],
            "rowCallback": function (row, data, index) {


                $('.dataTables_scrollBody').css('height', "calc(50vh - 172px)");
            },
            initComplete: function () {

                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });


    $('#search-inp').on('keydown input', commonDebounce(function (e) {

        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const SnapNameCheckbox = $("#Name");

        const inputValue = $('#search-inp').val();
        if (SnapNameCheckbox.is(':checked')) {
            selectedValues.push(SnapNameCheckbox.val() + inputValue);
        }

        dataTable.ajax.reload(function (json) {

            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })

    }, 500));

    $('#search-inp').attr('autocomplete', 'off');

})

const getFilteredStorageGroup = (snapList) => {
    let groupList = []
    let html = ''
    //snapStorageGroupSelect

    snapList.forEach((d) => {
        if (!groupList.includes(d.storageGroupName)) {
            html += `<option value='${d.storageGroupName}'>${d.storageGroupName}</option>`;
            groupList.push(d.storageGroupName)
        }
    })

    if (groupList && groupList.length) {
        $('#snapStorageGroupSelect').append(html)
    }

}

function filterTableWithSearchText(inputSelector, tableSelector, numColumns) {
    $(inputSelector).on("keyup input", function () {
        var filter = $(this).val();
        let categoryFlagStatus = true;
        let counter = 1;

        $(tableSelector + " tr").each(function () {
            var $i = 0;
            var splitText = $(this).text();

            if (splitText.search(new RegExp(filter, "i")) >= 0) {
                $i++;
            }
            if ($i > 0) {
                $(this).show();
                $(this).find("td:first").text(counter); // Update serial number
                counter++;
                categoryFlagStatus = false;
            } else {
                $(this).hide();
            }
        });

        // If no rows match, show the "No matching records" message
        if (categoryFlagStatus) {
            $(tableSelector).append('<tr class="no-records"><td colspan="' + numColumns + '" style="text-align: center;">No matching records found</td></tr>');
        } else {
            $(".no-records").remove();
        }
    });
}

filterTableWithSearchText("#search-infra", ".infraSummaryStautus", 5);
filterTableWithSearchText("#search-JobStatus", ".jobTableDetails", 7);
filterTableWithSearchText("#search-AirgapStatus", ".airgapTableDetails", 8);
filterTableWithSearchText("#search-Alert", ".alertTableDetails", 5);


$('#btnRemoveSnapSelectDate').on('click', function () {
    document.getElementById("snapDashboardDate").value = "";
    const tbody = document.getElementById("snapListTableBody");
    const rows = tbody.querySelectorAll("tr");
    rows.forEach(row => {
        row.removeAttribute("style");
    });
    $(".no-records").remove();
})


function filterTableWithDate(inputSelector, tableSelector, dateColumnIndex, numColumns) {
    $(inputSelector).on("keyup input", function () {
        var filter = $(this).val();
        var altered = new Date(filter).toString().split(' ')
        //let SplitData = 
        //var formattedDate = `${parts[0]}/${parts[1]}/${parts[2]}`;
        let categoryFlagStatus = true;
        $(tableSelector + " tr").each(function () {
            var $columns = $(this).find("td");
            if ($columns.length > 0) {
                var timeStampText = $columns.eq(dateColumnIndex).text().trim();
                //var dateOnly = timeStampText.split(" ")[0];

                //if (dateOnly.includes('-')) {
                //    formattedDate = `${parts[0]}-${parts[1]}-${parts[2]}`
                //}

                if (timeStampText.includes(altered[0]) && timeStampText.includes(altered[1]) && timeStampText.includes(altered[2]) && timeStampText.includes(altered[3])) {
                    $(this).show();
                    categoryFlagStatus = false;
                } else {
                    $(this).hide();
                }
            }
        });

        if (categoryFlagStatus) {
            $(tableSelector).append('<tr class="no-records"><td colspan="' + numColumns + '" style="text-align: center;">No matching records found</td></tr>');
        } else {
            $(".no-records").remove();
        }
    });
}

filterTableWithDate("#snapDashboardDate", "#snapListTableBody", 3, 4);

function filterTableWithDate(inputSelector, tableSelector, dateColumnIndex, numColumns) {
    $(inputSelector).on("keyup input", function () {
        var filter = $(this).val();
        var altered = new Date(filter).toString().split(' ')
        //let SplitData = 
        //var formattedDate = `${parts[0]}/${parts[1]}/${parts[2]}`;
        let categoryFlagStatus = true;
        $(tableSelector + " tr").each(function () {
            var $columns = $(this).find("td");
            if ($columns.length > 0) {
                var timeStampText = $columns.eq(dateColumnIndex).text().trim();
                //var dateOnly = timeStampText.split(" ")[0];

                //if (dateOnly.includes('-')) {
                //    formattedDate = `${parts[0]}-${parts[1]}-${parts[2]}`
                //}

                if (timeStampText.includes(altered[0]) && timeStampText.includes(altered[1]) && timeStampText.includes(altered[2]) && timeStampText.includes(altered[3])) {
                    $(this).show();
                    categoryFlagStatus = false;
                } else {
                    $(this).hide();
                }
            }
        });

        if (categoryFlagStatus) {
            $(tableSelector).append('<tr class="no-records"><td colspan="' + numColumns + '" style="text-align: center;">No matching records found</td></tr>');
        } else {
            $(".no-records").remove();
        }
    });
}



$('#snapStorageGroupSelect').on('change', function () {
    let selectedValue = $(this).val()

    if (selectedValue !== 'All') {
        $('#snapListTableBody' + " tr").each(function () {
            var $columns = $(this).find("td");
            if ($columns.length > 0) {
                var timeStampText = $columns.eq(2).text().trim();

                if (selectedValue == timeStampText) {
                    $(this).show();

                } else {
                    $(this).hide();
                }
            }
        });
    } else {
        const tbody = document.getElementById("snapListTableBody");
        const rows = tbody.querySelectorAll("tr");
        rows.forEach(row => {
            row.removeAttribute("style");
        });
    }


    //if (categoryFlagStatus) {
    //    $(tableSelector).append('<tr class="no-records"><td colspan="' + numColumns + '" style="text-align: center;">No matching records found</td></tr>');
    //} else {
    //    $(".no-records").remove();
    //}
})

$(document).on('click', '.snapFilterList', function () {
    let value = $(this).attr('value');
    let categoryFlagStatus = true;
    $("#snapListTableBody .no-records").remove();
    if (value !== 'all') {
        $('#snapListTableBody' + " tr").each(function () {
            var $columns = $(this).find("td");
            if ($columns.length > 0) {
                var timeStampText = $columns.eq(4).text().trim();

                if (value == timeStampText.toLowerCase()) {
                    $(this).show();
                    categoryFlagStatus = false;
                } else {
                    $(this).hide();
                }
            }
        });
    } else {
        const tbody = document.getElementById("snapListTableBody");
        const rows = tbody.querySelectorAll("tr");
        rows.forEach(row => {
            row.removeAttribute("style");
        });
    }

    if (categoryFlagStatus) {
        $("#snapListTableBody").append('<tr class="no-records"><td colspan="4" style="text-align: center;">No matching records found</td></tr>');
    }
    $('#snapTableDropdown').dropdown('toggle')
})

$('#snapSearchFilter').on('click', function () {
    $('#snapCollapseContainer').removeClass('show')
})