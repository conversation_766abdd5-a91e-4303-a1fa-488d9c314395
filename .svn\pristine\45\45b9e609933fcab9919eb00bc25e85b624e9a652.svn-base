﻿namespace ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Commands.Delete;

public class DeleteBusinessServiceHealthStatusCommandHandler : IRequestHandler<DeleteBusinessServiceHealthStatusCommand,
    DeleteBusinessServiceHealthStatusResponse>
{
    private readonly IBusinessServiceHealthStatusRepository _businessServiceHealthRepository;

    public DeleteBusinessServiceHealthStatusCommandHandler(
        IBusinessServiceHealthStatusRepository businessServiceHealthRepository)
    {
        _businessServiceHealthRepository = businessServiceHealthRepository;
    }

    public async Task<DeleteBusinessServiceHealthStatusResponse> Handle(
        DeleteBusinessServiceHealthStatusCommand request, CancellationToken cancellationToken)
    {
        var eventToDelete = await _businessServiceHealthRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.BusinessServiceHealthStatus),
            new NotFoundException(nameof(Domain.Entities.BusinessServiceHealthStatus), request.Id));

        eventToDelete.IsActive = false;

        await _businessServiceHealthRepository.UpdateAsync(eventToDelete);

        var response = new DeleteBusinessServiceHealthStatusResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.BusinessServiceHealthStatus), eventToDelete.ReferenceId),

            IsActive = eventToDelete.IsActive
        };

        return response;
    }
}