
am4internal_webpackJsonp(["cc1e"],{QJ7E:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i={};n.d(i,"geoArea",function(){return Vt}),n.d(i,"geoBounds",function(){return ye}),n.d(i,"geoCentroid",function(){return Ce}),n.d(i,"geoCircle",function(){return Fe}),n.d(i,"geoClipAntimeridian",function(){return Qe}),n.d(i,"geoClipCircle",function(){return $e}),n.d(i,"geoClipExtent",function(){return cn}),n.d(i,"geoClipRectangle",function(){return rn}),n.d(i,"geoContains",function(){return jn}),n.d(i,"geoDistance",function(){return mn}),n.d(i,"geoGraticule",function(){return En}),n.d(i,"geoGraticule10",function(){return Cn}),n.d(i,"geoInterpolate",function(){return Nn}),n.d(i,"geoLength",function(){return dn}),n.d(i,"geoPath",function(){return Gi}),n.d(i,"geoAlbers",function(){return Qi}),n.d(i,"geoAlbersUsa",function(){return $i}),n.d(i,"geoAzimuthalEqualArea",function(){return ir}),n.d(i,"geoAzimuthalEqualAreaRaw",function(){return nr}),n.d(i,"geoAzimuthalEquidistant",function(){return or}),n.d(i,"geoAzimuthalEquidistantRaw",function(){return rr}),n.d(i,"geoConicConformal",function(){return hr}),n.d(i,"geoConicConformalRaw",function(){return lr}),n.d(i,"geoConicEqualArea",function(){return Ki}),n.d(i,"geoConicEqualAreaRaw",function(){return Ji}),n.d(i,"geoConicEquidistant",function(){return gr}),n.d(i,"geoConicEquidistantRaw",function(){return dr}),n.d(i,"geoEqualEarth",function(){return wr}),n.d(i,"geoEqualEarthRaw",function(){return _r}),n.d(i,"geoEquirectangular",function(){return fr}),n.d(i,"geoEquirectangularRaw",function(){return pr}),n.d(i,"geoGnomonic",function(){return xr}),n.d(i,"geoGnomonicRaw",function(){return Sr}),n.d(i,"geoIdentity",function(){return Mr}),n.d(i,"geoProjection",function(){return Yi}),n.d(i,"geoProjectionMutator",function(){return Xi}),n.d(i,"geoMercator",function(){return ur}),n.d(i,"geoMercatorRaw",function(){return ar}),n.d(i,"geoNaturalEarth1",function(){return Or}),n.d(i,"geoNaturalEarth1Raw",function(){return jr}),n.d(i,"geoOrthographic",function(){return Er}),n.d(i,"geoOrthographicRaw",function(){return Lr}),n.d(i,"geoStereographic",function(){return Ir}),n.d(i,"geoStereographicRaw",function(){return Cr}),n.d(i,"geoTransverseMercator",function(){return Gr}),n.d(i,"geoTransverseMercatorRaw",function(){return Tr}),n.d(i,"geoRotation",function(){return ke}),n.d(i,"geoStream",function(){return yt}),n.d(i,"geoTransform",function(){return Di});var r={};n.d(r,"normalizePoint",function(){return fo}),n.d(r,"normalizeMultiline",function(){return go}),n.d(r,"wrapAngleTo180",function(){return vo}),n.d(r,"geoToPoint",function(){return mo});var o={};n.d(o,"Mercator",function(){return Wo}),n.d(o,"Miller",function(){return qu}),n.d(o,"Eckert6",function(){return Yu}),n.d(o,"Orthographic",function(){return Xu}),n.d(o,"Stereographic",function(){return Uu}),n.d(o,"Albers",function(){return Ju}),n.d(o,"AlbersUsa",function(){return Ku}),n.d(o,"NaturalEarth1",function(){return Qu}),n.d(o,"AzimuthalEqualArea",function(){return $u}),n.d(o,"EqualEarth",function(){return ts}),n.d(o,"Projection",function(){return oo});var a={};n.d(a,"LegendDataItem",function(){return u.b}),n.d(a,"Legend",function(){return u.a}),n.d(a,"LegendSettings",function(){return u.c}),n.d(a,"HeatLegend",function(){return s.a}),n.d(a,"MapChartDataItem",function(){return Io}),n.d(a,"MapChart",function(){return To}),n.d(a,"MapSeriesDataItem",function(){return Dr}),n.d(a,"MapSeries",function(){return Nr}),n.d(a,"MapObject",function(){return Vr}),n.d(a,"MapPolygon",function(){return $r}),n.d(a,"MapImage",function(){return kr}),n.d(a,"MapLine",function(){return xo}),n.d(a,"MapLineObject",function(){return yo}),n.d(a,"MapSpline",function(){return Do}),n.d(a,"MapArc",function(){return zo}),n.d(a,"Graticule",function(){return Oo}),n.d(a,"MapPolygonSeriesDataItem",function(){return io}),n.d(a,"MapPolygonSeries",function(){return ro}),n.d(a,"MapLineSeriesDataItem",function(){return Mo}),n.d(a,"MapLineSeries",function(){return jo}),n.d(a,"MapSplineSeriesDataItem",function(){return Vo}),n.d(a,"MapSplineSeries",function(){return ko}),n.d(a,"MapImageSeriesDataItem",function(){return bo}),n.d(a,"MapImageSeries",function(){return Po}),n.d(a,"MapArcSeriesDataItem",function(){return Ao}),n.d(a,"MapArcSeries",function(){return Ro}),n.d(a,"GraticuleSeriesDataItem",function(){return Lo}),n.d(a,"GraticuleSeries",function(){return Eo}),n.d(a,"multiPolygonToGeo",function(){return Hr}),n.d(a,"multiLineToGeo",function(){return Wr}),n.d(a,"multiPointToGeo",function(){return Zr}),n.d(a,"pointToGeo",function(){return Yr}),n.d(a,"multiGeoPolygonToMultipolygon",function(){return Jr}),n.d(a,"getBackground",function(){return Qr}),n.d(a,"multiGeoLineToMultiLine",function(){return Ur}),n.d(a,"multiGeoToPoint",function(){return qr}),n.d(a,"getCircle",function(){return Kr}),n.d(a,"ZoomControl",function(){return Ho}),n.d(a,"SmallMap",function(){return co}),n.d(a,"Projection",function(){return oo}),n.d(a,"projections",function(){return o}),n.d(a,"geo",function(){return r}),n.d(a,"d3geo",function(){return i});var u=n("uWmK"),s=n("2OXf"),c=n("m4/l"),l=n("2I/e"),h=n("hD5A"),p=n("MIZb"),f=n("aM7D"),d=n("aCit"),g=n("Mtpk"),v=n("Gg2j");function m(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function y(t){var e,n,i;function r(t,i){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.length;if(r<o){if(0!==e(i,i))return o;do{var a=r+o>>>1;n(t[a],i)<0?r=a+1:o=a}while(r<o)}return r}return 2!==t.length?(e=m,n=function(e,n){return m(t(e),n)},i=function(e,n){return t(e)-n}):(e=t===m||t===function(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}?t:b,n=t,i=t),{left:r,center:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=r(t,e,n,(arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.length)-1);return o>n&&i(t[o-1],e)>-i(t[o],e)?o-1:o},right:function(t,i){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.length;if(r<o){if(0!==e(i,i))return o;do{var a=r+o>>>1;n(t[a],i)<=0?r=a+1:o=a}while(r<o)}return r}}}function b(){return 0}function P(t){return null===t?NaN:+t}var _=y(m);_.right,_.left,y(P).center;w(M),w(function(t){var e=M(t);return function(t,n,i,r,o){e(t,n,(i<<=2)+0,(r<<=2)+0,o<<=2),e(t,n,i+1,r+1,o),e(t,n,i+2,r+2,o),e(t,n,i+3,r+3,o)}});function w(t){return function(e,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:n;if(!((n=+n)>=0))throw new RangeError("invalid rx");if(!((i=+i)>=0))throw new RangeError("invalid ry");var r=e.data,o=e.width,a=e.height;if(!((o=Math.floor(o))>=0))throw new RangeError("invalid width");if(!((a=Math.floor(void 0!==a?a:r.length/o))>=0))throw new RangeError("invalid height");if(!o||!a||!n&&!i)return e;var u=n&&t(n),s=i&&t(i),c=r.slice();return u&&s?(S(u,c,r,o,a),S(u,r,c,o,a),S(u,c,r,o,a),x(s,r,c,o,a),x(s,c,r,o,a),x(s,r,c,o,a)):u?(S(u,r,c,o,a),S(u,c,r,o,a),S(u,r,c,o,a)):s&&(x(s,r,c,o,a),x(s,c,r,o,a),x(s,r,c,o,a)),e}}function S(t,e,n,i,r){for(var o=0,a=i*r;o<a;)t(e,n,o,o+=i,1)}function x(t,e,n,i,r){for(var o=0,a=i*r;o<i;++o)t(e,n,o,o+a,i)}function M(t){var e=Math.floor(t);if(e===t)return function(t){var e=2*t+1;return function(n,i,r,o,a){if((o-=a)>=r){for(var u=t*i[r],s=a*t,c=r,l=r+s;c<l;c+=a)u+=i[Math.min(o,c)];for(var h=r,p=o;h<=p;h+=a)u+=i[Math.min(o,h+s)],n[h]=u/e,u-=i[Math.max(r,h-s)]}}}(t);var n=t-e,i=2*t+1;return function(t,r,o,a,u){if((a-=u)>=o){for(var s=e*r[o],c=u*e,l=c+u,h=o,p=o+c;h<p;h+=u)s+=r[Math.min(a,h)];for(var f=o,d=a;f<=d;f+=u)s+=r[Math.min(a,f+c)],t[f]=(s+n*(r[Math.max(o,f-l)]+r[Math.min(a,f+l)]))/i,s-=r[Math.max(o,f-c)]}}}function j(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var O=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this._partials=new Float64Array(32),this._n=0}return function(t,e,n){e&&j(t.prototype,e),n&&j(t,n),Object.defineProperty(t,"prototype",{writable:!1})}(t,[{key:"add",value:function(t){for(var e=this._partials,n=0,i=0;i<this._n&&i<32;i++){var r=e[i],o=t+r,a=Math.abs(t)<Math.abs(r)?t-(o-r):r-(o-t);a&&(e[n++]=a),t=o}return e[n]=t,this._n=n+1,this}},{key:"valueOf",value:function(){var t,e,n,i=this._partials,r=this._n,o=0;if(r>0){for(o=i[--r];r>0&&(t=o,!(n=(e=i[--r])-((o=t+e)-t))););r>0&&(n<0&&i[r-1]<0||n>0&&i[r-1]>0)&&(e=2*n)==(t=o+e)-o&&(o=t)}return o}}]),t}();var L=Array.prototype;L.slice,L.map;Math.sqrt(50),Math.sqrt(10),Math.sqrt(2);function E(t){"@babel/helpers - typeof";return(E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function C(){C=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},r=i.iterator||"@@iterator",o=i.asyncIterator||"@@asyncIterator",a=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function s(t,e,n,i){var r=e&&e.prototype instanceof h?e:h,o=Object.create(r.prototype),a=new S(i||[]);return o._invoke=function(t,e,n){var i="suspendedStart";return function(r,o){if("executing"===i)throw new Error("Generator is already running");if("completed"===i){if("throw"===r)throw o;return M()}for(n.method=r,n.arg=o;;){var a=n.delegate;if(a){var u=P(a,n);if(u){if(u===l)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===i)throw i="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i="executing";var s=c(t,e,n);if("normal"===s.type){if(i=n.done?"completed":"suspendedYield",s.arg===l)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(i="completed",n.method="throw",n.arg=s.arg)}}}(t,n,a),o}function c(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=s;var l={};function h(){}function p(){}function f(){}var d={};u(d,r,function(){return this});var g=Object.getPrototypeOf,v=g&&g(g(x([])));v&&v!==e&&n.call(v,r)&&(d=v);var m=f.prototype=h.prototype=Object.create(d);function y(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function b(t,e){var i;this._invoke=function(r,o){function a(){return new e(function(i,a){!function i(r,o,a,u){var s=c(t[r],t,o);if("throw"!==s.type){var l=s.arg,h=l.value;return h&&"object"==E(h)&&n.call(h,"__await")?e.resolve(h.__await).then(function(t){i("next",t,a,u)},function(t){i("throw",t,a,u)}):e.resolve(h).then(function(t){l.value=t,a(l)},function(t){return i("throw",t,a,u)})}u(s.arg)}(r,o,i,a)})}return i=i?i.then(a,a):a()}}function P(t,e){var n=t.iterator[e.method];if(void 0===n){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,P(t,e),"throw"===e.method))return l;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return l}var i=c(n,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,l;var r=i.arg;return r?r.done?(e[t.resultName]=r.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,l):r:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,l)}function _(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function w(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(_,this),this.reset(!0)}function x(t){if(t){var e=t[r];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function e(){for(;++i<t.length;)if(n.call(t,i))return e.value=t[i],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:M}}function M(){return{value:void 0,done:!0}}return p.prototype=f,u(m,"constructor",f),u(f,"constructor",p),p.displayName=u(f,a,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,u(t,a,"GeneratorFunction")),t.prototype=Object.create(m),t},t.awrap=function(t){return{__await:t}},y(b.prototype),u(b.prototype,o,function(){return this}),t.AsyncIterator=b,t.async=function(e,n,i,r,o){void 0===o&&(o=Promise);var a=new b(s(e,n,i,r),o);return t.isGeneratorFunction(n)?a:a.next().then(function(t){return t.done?t.value:a.next()})},y(m),u(m,a,"Generator"),u(m,r,function(){return this}),u(m,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=[];for(var n in t)e.push(n);return e.reverse(),function n(){for(;e.length;){var i=e.pop();if(i in t)return n.value=i,n.done=!1,n}return n.done=!0,n}},t.values=x,S.prototype={constructor:S,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(w),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function i(n,i){return a.type="throw",a.arg=t,e.next=n,i&&(e.method="next",e.arg=void 0),!!i}for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r],a=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var u=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(u&&s){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(t,e){for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),w(n),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var i=n.completion;if("throw"===i.type){var r=i.arg;w(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:x(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),l}},t}var I=C().mark(D);function T(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return G(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return G(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,u=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){u=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(u)throw o}}}}function G(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function D(t){var e,n,i;return C().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:e=T(t),r.prev=1,e.s();case 3:if((n=e.n()).done){r.next=8;break}return i=n.value,r.delegateYield(i,"t0",6);case 6:r.next=3;break;case 8:r.next=13;break;case 10:r.prev=10,r.t1=r.catch(1),e.e(r.t1);case 13:return r.prev=13,e.f(),r.finish(13);case 16:case"end":return r.stop()}},I,null,[[1,10,13,16]])}function N(t){return Array.from(D(t))}function z(t,e,n){t=+t,e=+e,n=(r=arguments.length)<2?(e=t,t=0,1):r<3?1:+n;for(var i=-1,r=0|Math.max(0,Math.ceil((e-t)/n)),o=new Array(r);++i<r;)o[i]=t+i*n;return o}Math.random;var V=1e-6,k=1e-12,A=Math.PI,R=A/2,F=A/4,B=2*A,H=180/A,W=A/180,Z=Math.abs,q=Math.atan,Y=Math.atan2,X=Math.cos,U=Math.ceil,J=Math.exp,K=(Math.floor,Math.hypot),Q=Math.log,$=Math.pow,tt=Math.sin,et=Math.sign||function(t){return t>0?1:t<0?-1:0},nt=Math.sqrt,it=Math.tan;function rt(t){return t>1?0:t<-1?A:Math.acos(t)}function ot(t){return t>1?R:t<-1?-R:Math.asin(t)}function at(t){return(t=tt(t/2))*t}function ut(){}function st(t,e){t&&lt.hasOwnProperty(t.type)&&lt[t.type](t,e)}var ct={Feature:function(t,e){st(t.geometry,e)},FeatureCollection:function(t,e){for(var n=t.features,i=-1,r=n.length;++i<r;)st(n[i].geometry,e)}},lt={Sphere:function(t,e){e.sphere()},Point:function(t,e){t=t.coordinates,e.point(t[0],t[1],t[2])},MultiPoint:function(t,e){for(var n=t.coordinates,i=-1,r=n.length;++i<r;)t=n[i],e.point(t[0],t[1],t[2])},LineString:function(t,e){ht(t.coordinates,e,0)},MultiLineString:function(t,e){for(var n=t.coordinates,i=-1,r=n.length;++i<r;)ht(n[i],e,0)},Polygon:function(t,e){pt(t.coordinates,e)},MultiPolygon:function(t,e){for(var n=t.coordinates,i=-1,r=n.length;++i<r;)pt(n[i],e)},GeometryCollection:function(t,e){for(var n=t.geometries,i=-1,r=n.length;++i<r;)st(n[i],e)}};function ht(t,e,n){var i,r=-1,o=t.length-n;for(e.lineStart();++r<o;)i=t[r],e.point(i[0],i[1],i[2]);e.lineEnd()}function pt(t,e){var n=-1,i=t.length;for(e.polygonStart();++n<i;)ht(t[n],e,1);e.polygonEnd()}var ft,dt,gt,vt,mt,yt=function(t,e){t&&ct.hasOwnProperty(t.type)?ct[t.type](t,e):st(t,e)},bt=new O,Pt=new O,_t={point:ut,lineStart:ut,lineEnd:ut,polygonStart:function(){bt=new O,_t.lineStart=wt,_t.lineEnd=St},polygonEnd:function(){var t=+bt;Pt.add(t<0?B+t:t),this.lineStart=this.lineEnd=this.point=ut},sphere:function(){Pt.add(B)}};function wt(){_t.point=xt}function St(){Mt(ft,dt)}function xt(t,e){_t.point=Mt,ft=t,dt=e,gt=t*=W,vt=X(e=(e*=W)/2+F),mt=tt(e)}function Mt(t,e){e=(e*=W)/2+F;var n=(t*=W)-gt,i=n>=0?1:-1,r=i*n,o=X(e),a=tt(e),u=mt*a,s=vt*o+u*X(r),c=u*i*tt(r);bt.add(Y(c,s)),gt=t,vt=o,mt=a}var jt,Ot,Lt,Et,Ct,It,Tt,Gt,Dt,Nt,zt,Vt=function(t){return Pt=new O,yt(t,_t),2*Pt};function kt(t){return[Y(t[1],t[0]),ot(t[2])]}function At(t){var e=t[0],n=t[1],i=X(n);return[i*X(e),i*tt(e),tt(n)]}function Rt(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]}function Ft(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function Bt(t,e){t[0]+=e[0],t[1]+=e[1],t[2]+=e[2]}function Ht(t,e){return[t[0]*e,t[1]*e,t[2]*e]}function Wt(t){var e=nt(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=e,t[1]/=e,t[2]/=e}var Zt={point:qt,lineStart:Xt,lineEnd:Ut,polygonStart:function(){Zt.point=Jt,Zt.lineStart=Kt,Zt.lineEnd=Qt,Dt=new O,_t.polygonStart()},polygonEnd:function(){_t.polygonEnd(),Zt.point=qt,Zt.lineStart=Xt,Zt.lineEnd=Ut,bt<0?(jt=-(Lt=180),Ot=-(Et=90)):Dt>V?Et=90:Dt<-V&&(Ot=-90),zt[0]=jt,zt[1]=Lt},sphere:function(){jt=-(Lt=180),Ot=-(Et=90)}};function qt(t,e){Nt.push(zt=[jt=t,Lt=t]),e<Ot&&(Ot=e),e>Et&&(Et=e)}function Yt(t,e){var n=At([t*W,e*W]);if(Gt){var i=Ft(Gt,n),r=Ft([i[1],-i[0],0],i);Wt(r),r=kt(r);var o,a=t-Ct,u=a>0?1:-1,s=r[0]*H*u,c=Z(a)>180;c^(u*Ct<s&&s<u*t)?(o=r[1]*H)>Et&&(Et=o):c^(u*Ct<(s=(s+360)%360-180)&&s<u*t)?(o=-r[1]*H)<Ot&&(Ot=o):(e<Ot&&(Ot=e),e>Et&&(Et=e)),c?t<Ct?$t(jt,t)>$t(jt,Lt)&&(Lt=t):$t(t,Lt)>$t(jt,Lt)&&(jt=t):Lt>=jt?(t<jt&&(jt=t),t>Lt&&(Lt=t)):t>Ct?$t(jt,t)>$t(jt,Lt)&&(Lt=t):$t(t,Lt)>$t(jt,Lt)&&(jt=t)}else Nt.push(zt=[jt=t,Lt=t]);e<Ot&&(Ot=e),e>Et&&(Et=e),Gt=n,Ct=t}function Xt(){Zt.point=Yt}function Ut(){zt[0]=jt,zt[1]=Lt,Zt.point=qt,Gt=null}function Jt(t,e){if(Gt){var n=t-Ct;Dt.add(Z(n)>180?n+(n>0?360:-360):n)}else It=t,Tt=e;_t.point(t,e),Yt(t,e)}function Kt(){_t.lineStart()}function Qt(){Jt(It,Tt),_t.lineEnd(),Z(Dt)>V&&(jt=-(Lt=180)),zt[0]=jt,zt[1]=Lt,Gt=null}function $t(t,e){return(e-=t)<0?e+360:e}function te(t,e){return t[0]-e[0]}function ee(t,e){return t[0]<=t[1]?t[0]<=e&&e<=t[1]:e<t[0]||t[1]<e}var ne,ie,re,oe,ae,ue,se,ce,le,he,pe,fe,de,ge,ve,me,ye=function(t){var e,n,i,r,o,a,u;if(Et=Lt=-(jt=Ot=1/0),Nt=[],yt(t,Zt),n=Nt.length){for(Nt.sort(te),e=1,o=[i=Nt[0]];e<n;++e)ee(i,(r=Nt[e])[0])||ee(i,r[1])?($t(i[0],r[1])>$t(i[0],i[1])&&(i[1]=r[1]),$t(r[0],i[1])>$t(i[0],i[1])&&(i[0]=r[0])):o.push(i=r);for(a=-1/0,e=0,i=o[n=o.length-1];e<=n;i=r,++e)r=o[e],(u=$t(i[1],r[0]))>a&&(a=u,jt=r[0],Lt=i[1])}return Nt=zt=null,jt===1/0||Ot===1/0?[[NaN,NaN],[NaN,NaN]]:[[jt,Ot],[Lt,Et]]},be={sphere:ut,point:Pe,lineStart:we,lineEnd:Me,polygonStart:function(){be.lineStart=je,be.lineEnd=Oe},polygonEnd:function(){be.lineStart=we,be.lineEnd=Me}};function Pe(t,e){t*=W;var n=X(e*=W);_e(n*X(t),n*tt(t),tt(e))}function _e(t,e,n){re+=(t-re)/++ne,oe+=(e-oe)/ne,ae+=(n-ae)/ne}function we(){be.point=Se}function Se(t,e){t*=W;var n=X(e*=W);ge=n*X(t),ve=n*tt(t),me=tt(e),be.point=xe,_e(ge,ve,me)}function xe(t,e){t*=W;var n=X(e*=W),i=n*X(t),r=n*tt(t),o=tt(e),a=Y(nt((a=ve*o-me*r)*a+(a=me*i-ge*o)*a+(a=ge*r-ve*i)*a),ge*i+ve*r+me*o);ie+=a,ue+=a*(ge+(ge=i)),se+=a*(ve+(ve=r)),ce+=a*(me+(me=o)),_e(ge,ve,me)}function Me(){be.point=Pe}function je(){be.point=Le}function Oe(){Ee(fe,de),be.point=Pe}function Le(t,e){fe=t,de=e,t*=W,e*=W,be.point=Ee;var n=X(e);ge=n*X(t),ve=n*tt(t),me=tt(e),_e(ge,ve,me)}function Ee(t,e){t*=W;var n=X(e*=W),i=n*X(t),r=n*tt(t),o=tt(e),a=ve*o-me*r,u=me*i-ge*o,s=ge*r-ve*i,c=K(a,u,s),l=ot(c),h=c&&-l/c;le.add(h*a),he.add(h*u),pe.add(h*s),ie+=l,ue+=l*(ge+(ge=i)),se+=l*(ve+(ve=r)),ce+=l*(me+(me=o)),_e(ge,ve,me)}var Ce=function(t){ne=ie=re=oe=ae=ue=se=ce=0,le=new O,he=new O,pe=new O,yt(t,be);var e=+le,n=+he,i=+pe,r=K(e,n,i);return r<k&&(e=ue,n=se,i=ce,ie<V&&(e=re,n=oe,i=ae),(r=K(e,n,i))<k)?[NaN,NaN]:[Y(n,e)*H,ot(i/r)*H]},Ie=function(t){return function(){return t}},Te=function(t,e){function n(n,i){return n=t(n,i),e(n[0],n[1])}return t.invert&&e.invert&&(n.invert=function(n,i){return(n=e.invert(n,i))&&t.invert(n[0],n[1])}),n};function Ge(t,e){return[Z(t)>A?t+Math.round(-t/B)*B:t,e]}function De(t,e,n){return(t%=B)?e||n?Te(ze(t),Ve(e,n)):ze(t):e||n?Ve(e,n):Ge}function Ne(t){return function(e,n){return[(e+=t)>A?e-B:e<-A?e+B:e,n]}}function ze(t){var e=Ne(t);return e.invert=Ne(-t),e}function Ve(t,e){var n=X(t),i=tt(t),r=X(e),o=tt(e);function a(t,e){var a=X(e),u=X(t)*a,s=tt(t)*a,c=tt(e),l=c*n+u*i;return[Y(s*r-l*o,u*n-c*i),ot(l*r+s*o)]}return a.invert=function(t,e){var a=X(e),u=X(t)*a,s=tt(t)*a,c=tt(e),l=c*r-s*o;return[Y(s*r+c*o,u*n+l*i),ot(l*n-u*i)]},a}Ge.invert=Ge;var ke=function(t){function e(e){return(e=t(e[0]*W,e[1]*W))[0]*=H,e[1]*=H,e}return t=De(t[0]*W,t[1]*W,t.length>2?t[2]*W:0),e.invert=function(e){return(e=t.invert(e[0]*W,e[1]*W))[0]*=H,e[1]*=H,e},e};function Ae(t,e,n,i,r,o){if(n){var a=X(e),u=tt(e),s=i*n;null==r?(r=e+i*B,o=e-s/2):(r=Re(a,r),o=Re(a,o),(i>0?r<o:r>o)&&(r+=i*B));for(var c,l=r;i>0?l>o:l<o;l-=s)c=kt([a,-u*X(l),-u*tt(l)]),t.point(c[0],c[1])}}function Re(t,e){(e=At(e))[0]-=t,Wt(e);var n=rt(-e[1]);return((-e[2]<0?-n:n)+B-V)%B}var Fe=function(){var t,e,n=Ie([0,0]),i=Ie(90),r=Ie(6),o={point:function(n,i){t.push(n=e(n,i)),n[0]*=H,n[1]*=H}};function a(){var a=n.apply(this,arguments),u=i.apply(this,arguments)*W,s=r.apply(this,arguments)*W;return t=[],e=De(-a[0]*W,-a[1]*W,0).invert,Ae(o,u,s,1),a={type:"Polygon",coordinates:[t]},t=e=null,a}return a.center=function(t){return arguments.length?(n="function"==typeof t?t:Ie([+t[0],+t[1]]),a):n},a.radius=function(t){return arguments.length?(i="function"==typeof t?t:Ie(+t),a):i},a.precision=function(t){return arguments.length?(r="function"==typeof t?t:Ie(+t),a):r},a},Be=function(){var t,e=[];return{point:function(e,n,i){t.push([e,n,i])},lineStart:function(){e.push(t=[])},lineEnd:ut,rejoin:function(){e.length>1&&e.push(e.pop().concat(e.shift()))},result:function(){var n=e;return e=[],t=null,n}}},He=function(t,e){return Z(t[0]-e[0])<V&&Z(t[1]-e[1])<V};function We(t,e,n,i){this.x=t,this.z=e,this.o=n,this.e=i,this.v=!1,this.n=this.p=null}var Ze=function(t,e,n,i,r){var o,a,u=[],s=[];if(t.forEach(function(t){if(!((e=t.length-1)<=0)){var e,n,i=t[0],a=t[e];if(He(i,a)){if(!i[2]&&!a[2]){for(r.lineStart(),o=0;o<e;++o)r.point((i=t[o])[0],i[1]);return void r.lineEnd()}a[0]+=2*V}u.push(n=new We(i,t,null,!0)),s.push(n.o=new We(i,null,n,!1)),u.push(n=new We(a,t,null,!1)),s.push(n.o=new We(a,null,n,!0))}}),u.length){for(s.sort(e),qe(u),qe(s),o=0,a=s.length;o<a;++o)s[o].e=n=!n;for(var c,l,h=u[0];;){for(var p=h,f=!0;p.v;)if((p=p.n)===h)return;c=p.z,r.lineStart();do{if(p.v=p.o.v=!0,p.e){if(f)for(o=0,a=c.length;o<a;++o)r.point((l=c[o])[0],l[1]);else i(p.x,p.n.x,1,r);p=p.n}else{if(f)for(c=p.p.z,o=c.length-1;o>=0;--o)r.point((l=c[o])[0],l[1]);else i(p.x,p.p.x,-1,r);p=p.p}c=(p=p.o).z,f=!f}while(!p.v);r.lineEnd()}}};function qe(t){if(e=t.length){for(var e,n,i=0,r=t[0];++i<e;)r.n=n=t[i],n.p=r,r=n;r.n=n=t[0],n.p=r}}function Ye(t){return Z(t[0])<=A?t[0]:et(t[0])*((Z(t[0])+A)%B-A)}var Xe=function(t,e){var n=Ye(e),i=e[1],r=tt(i),o=[tt(n),-X(n),0],a=0,u=0,s=new O;1===r?i=R+V:-1===r&&(i=-R-V);for(var c=0,l=t.length;c<l;++c)if(p=(h=t[c]).length)for(var h,p,f=h[p-1],d=Ye(f),g=f[1]/2+F,v=tt(g),m=X(g),y=0;y<p;++y,d=P,v=w,m=S,f=b){var b=h[y],P=Ye(b),_=b[1]/2+F,w=tt(_),S=X(_),x=P-d,M=x>=0?1:-1,j=M*x,L=j>A,E=v*w;if(s.add(Y(E*M*tt(j),m*S+E*X(j))),a+=L?x+M*B:x,L^d>=n^P>=n){var C=Ft(At(f),At(b));Wt(C);var I=Ft(o,C);Wt(I);var T=(L^x>=0?-1:1)*ot(I[2]);(i>T||i===T&&(C[0]||C[1]))&&(u+=L^x>=0?1:-1)}}return(a<-V||a<V&&s<-k)^1&u},Ue=function(t,e,n,i){return function(r){var o,a,u,s=e(r),c=Be(),l=e(c),h=!1,p={point:f,lineStart:g,lineEnd:v,polygonStart:function(){p.point=m,p.lineStart=y,p.lineEnd=b,a=[],o=[]},polygonEnd:function(){p.point=f,p.lineStart=g,p.lineEnd=v,a=N(a);var t=Xe(o,i);a.length?(h||(r.polygonStart(),h=!0),Ze(a,Ke,t,n,r)):t&&(h||(r.polygonStart(),h=!0),r.lineStart(),n(null,null,1,r),r.lineEnd()),h&&(r.polygonEnd(),h=!1),a=o=null},sphere:function(){r.polygonStart(),r.lineStart(),n(null,null,1,r),r.lineEnd(),r.polygonEnd()}};function f(e,n){t(e,n)&&r.point(e,n)}function d(t,e){s.point(t,e)}function g(){p.point=d,s.lineStart()}function v(){p.point=f,s.lineEnd()}function m(t,e){u.push([t,e]),l.point(t,e)}function y(){l.lineStart(),u=[]}function b(){m(u[0][0],u[0][1]),l.lineEnd();var t,e,n,i,s=l.clean(),p=c.result(),f=p.length;if(u.pop(),o.push(u),u=null,f)if(1&s){if((e=(n=p[0]).length-1)>0){for(h||(r.polygonStart(),h=!0),r.lineStart(),t=0;t<e;++t)r.point((i=n[t])[0],i[1]);r.lineEnd()}}else f>1&&2&s&&p.push(p.pop().concat(p.shift())),a.push(p.filter(Je))}return p}};function Je(t){return t.length>1}function Ke(t,e){return((t=t.x)[0]<0?t[1]-R-V:R-t[1])-((e=e.x)[0]<0?e[1]-R-V:R-e[1])}var Qe=Ue(function(){return!0},function(t){var e,n=NaN,i=NaN,r=NaN;return{lineStart:function(){t.lineStart(),e=1},point:function(o,a){var u=o>0?A:-A,s=Z(o-n);Z(s-A)<V?(t.point(n,i=(i+a)/2>0?R:-R),t.point(r,i),t.lineEnd(),t.lineStart(),t.point(u,i),t.point(o,i),e=0):r!==u&&s>=A&&(Z(n-r)<V&&(n-=r*V),Z(o-u)<V&&(o-=u*V),i=function(t,e,n,i){var r,o,a=tt(t-n);return Z(a)>V?q((tt(e)*(o=X(i))*tt(n)-tt(i)*(r=X(e))*tt(t))/(r*o*a)):(e+i)/2}(n,i,o,a),t.point(r,i),t.lineEnd(),t.lineStart(),t.point(u,i),e=0),t.point(n=o,i=a),r=u},lineEnd:function(){t.lineEnd(),n=i=NaN},clean:function(){return 2-e}}},function(t,e,n,i){var r;if(null==t)r=n*R,i.point(-A,r),i.point(0,r),i.point(A,r),i.point(A,0),i.point(A,-r),i.point(0,-r),i.point(-A,-r),i.point(-A,0),i.point(-A,r);else if(Z(t[0]-e[0])>V){var o=t[0]<e[0]?A:-A;r=n*o/2,i.point(-o,r),i.point(0,r),i.point(o,r)}else i.point(e[0],e[1])},[-A,-R]);var $e=function(t){var e=X(t),n=6*W,i=e>0,r=Z(e)>V;function o(t,n){return X(t)*X(n)>e}function a(t,n,i){var r=[1,0,0],o=Ft(At(t),At(n)),a=Rt(o,o),u=o[0],s=a-u*u;if(!s)return!i&&t;var c=e*a/s,l=-e*u/s,h=Ft(r,o),p=Ht(r,c);Bt(p,Ht(o,l));var f=h,d=Rt(p,f),g=Rt(f,f),v=d*d-g*(Rt(p,p)-1);if(!(v<0)){var m=nt(v),y=Ht(f,(-d-m)/g);if(Bt(y,p),y=kt(y),!i)return y;var b,P=t[0],_=n[0],w=t[1],S=n[1];_<P&&(b=P,P=_,_=b);var x=_-P,M=Z(x-A)<V;if(!M&&S<w&&(b=w,w=S,S=b),M||x<V?M?w+S>0^y[1]<(Z(y[0]-P)<V?w:S):w<=y[1]&&y[1]<=S:x>A^(P<=y[0]&&y[0]<=_)){var j=Ht(f,(-d+m)/g);return Bt(j,p),[y,kt(j)]}}}function u(e,n){var r=i?t:A-t,o=0;return e<-r?o|=1:e>r&&(o|=2),n<-r?o|=4:n>r&&(o|=8),o}return Ue(o,function(t){var e,n,s,c,l;return{lineStart:function(){c=s=!1,l=1},point:function(h,p){var f,d=[h,p],g=o(h,p),v=i?g?0:u(h,p):g?u(h+(h<0?A:-A),p):0;if(!e&&(c=s=g)&&t.lineStart(),g!==s&&(!(f=a(e,d))||He(e,f)||He(d,f))&&(d[2]=1),g!==s)l=0,g?(t.lineStart(),f=a(d,e),t.point(f[0],f[1])):(f=a(e,d),t.point(f[0],f[1],2),t.lineEnd()),e=f;else if(r&&e&&i^g){var m;v&n||!(m=a(d,e,!0))||(l=0,i?(t.lineStart(),t.point(m[0][0],m[0][1]),t.point(m[1][0],m[1][1]),t.lineEnd()):(t.point(m[1][0],m[1][1]),t.lineEnd(),t.lineStart(),t.point(m[0][0],m[0][1],3)))}!g||e&&He(e,d)||t.point(d[0],d[1]),e=d,s=g,n=v},lineEnd:function(){s&&t.lineEnd(),e=null},clean:function(){return l|(c&&s)<<1}}},function(e,i,r,o){Ae(o,t,n,r,e,i)},i?[0,-t]:[-A,t-A])},tn=function(t,e,n,i,r,o){var a,u=t[0],s=t[1],c=0,l=1,h=e[0]-u,p=e[1]-s;if(a=n-u,h||!(a>0)){if(a/=h,h<0){if(a<c)return;a<l&&(l=a)}else if(h>0){if(a>l)return;a>c&&(c=a)}if(a=r-u,h||!(a<0)){if(a/=h,h<0){if(a>l)return;a>c&&(c=a)}else if(h>0){if(a<c)return;a<l&&(l=a)}if(a=i-s,p||!(a>0)){if(a/=p,p<0){if(a<c)return;a<l&&(l=a)}else if(p>0){if(a>l)return;a>c&&(c=a)}if(a=o-s,p||!(a<0)){if(a/=p,p<0){if(a>l)return;a>c&&(c=a)}else if(p>0){if(a<c)return;a<l&&(l=a)}return c>0&&(t[0]=u+c*h,t[1]=s+c*p),l<1&&(e[0]=u+l*h,e[1]=s+l*p),!0}}}}},en=1e9,nn=-en;function rn(t,e,n,i){function r(r,o){return t<=r&&r<=n&&e<=o&&o<=i}function o(r,o,u,c){var l=0,h=0;if(null==r||(l=a(r,u))!==(h=a(o,u))||s(r,o)<0^u>0)do{c.point(0===l||3===l?t:n,l>1?i:e)}while((l=(l+u+4)%4)!==h);else c.point(o[0],o[1])}function a(i,r){return Z(i[0]-t)<V?r>0?0:3:Z(i[0]-n)<V?r>0?2:1:Z(i[1]-e)<V?r>0?1:0:r>0?3:2}function u(t,e){return s(t.x,e.x)}function s(t,e){var n=a(t,1),i=a(e,1);return n!==i?n-i:0===n?e[1]-t[1]:1===n?t[0]-e[0]:2===n?t[1]-e[1]:e[0]-t[0]}return function(a){var s,c,l,h,p,f,d,g,v,m,y,b=a,P=Be(),_={point:w,lineStart:function(){_.point=S,c&&c.push(l=[]);m=!0,v=!1,d=g=NaN},lineEnd:function(){s&&(S(h,p),f&&v&&P.rejoin(),s.push(P.result()));_.point=w,v&&b.lineEnd()},polygonStart:function(){b=P,s=[],c=[],y=!0},polygonEnd:function(){var e=function(){for(var e=0,n=0,r=c.length;n<r;++n)for(var o,a,u=c[n],s=1,l=u.length,h=u[0],p=h[0],f=h[1];s<l;++s)o=p,a=f,h=u[s],p=h[0],f=h[1],a<=i?f>i&&(p-o)*(i-a)>(f-a)*(t-o)&&++e:f<=i&&(p-o)*(i-a)<(f-a)*(t-o)&&--e;return e}(),n=y&&e,r=(s=N(s)).length;(n||r)&&(a.polygonStart(),n&&(a.lineStart(),o(null,null,1,a),a.lineEnd()),r&&Ze(s,u,e,o,a),a.polygonEnd());b=a,s=c=l=null}};function w(t,e){r(t,e)&&b.point(t,e)}function S(o,a){var u=r(o,a);if(c&&l.push([o,a]),m)h=o,p=a,f=u,m=!1,u&&(b.lineStart(),b.point(o,a));else if(u&&v)b.point(o,a);else{var s=[d=Math.max(nn,Math.min(en,d)),g=Math.max(nn,Math.min(en,g))],P=[o=Math.max(nn,Math.min(en,o)),a=Math.max(nn,Math.min(en,a))];tn(s,P,t,e,n,i)?(v||(b.lineStart(),b.point(s[0],s[1])),b.point(P[0],P[1]),u||b.lineEnd(),y=!1):u&&(b.lineStart(),b.point(o,a),y=!1)}d=o,g=a,v=u}return _}}var on,an,un,sn,cn=function(){var t,e,n,i=0,r=0,o=960,a=500;return n={stream:function(n){return t&&e===n?t:t=rn(i,r,o,a)(e=n)},extent:function(u){return arguments.length?(i=+u[0][0],r=+u[0][1],o=+u[1][0],a=+u[1][1],t=e=null,n):[[i,r],[o,a]]}}},ln={sphere:ut,point:ut,lineStart:function(){ln.point=pn,ln.lineEnd=hn},lineEnd:ut,polygonStart:ut,polygonEnd:ut};function hn(){ln.point=ln.lineEnd=ut}function pn(t,e){an=t*=W,un=tt(e*=W),sn=X(e),ln.point=fn}function fn(t,e){t*=W;var n=tt(e*=W),i=X(e),r=Z(t-an),o=X(r),a=i*tt(r),u=sn*n-un*i*o,s=un*n+sn*i*o;on.add(Y(nt(a*a+u*u),s)),an=t,un=n,sn=i}var dn=function(t){return on=new O,yt(t,ln),+on},gn=[null,null],vn={type:"LineString",coordinates:gn},mn=function(t,e){return gn[0]=t,gn[1]=e,dn(vn)},yn={Feature:function(t,e){return Pn(t.geometry,e)},FeatureCollection:function(t,e){for(var n=t.features,i=-1,r=n.length;++i<r;)if(Pn(n[i].geometry,e))return!0;return!1}},bn={Sphere:function(){return!0},Point:function(t,e){return _n(t.coordinates,e)},MultiPoint:function(t,e){for(var n=t.coordinates,i=-1,r=n.length;++i<r;)if(_n(n[i],e))return!0;return!1},LineString:function(t,e){return wn(t.coordinates,e)},MultiLineString:function(t,e){for(var n=t.coordinates,i=-1,r=n.length;++i<r;)if(wn(n[i],e))return!0;return!1},Polygon:function(t,e){return Sn(t.coordinates,e)},MultiPolygon:function(t,e){for(var n=t.coordinates,i=-1,r=n.length;++i<r;)if(Sn(n[i],e))return!0;return!1},GeometryCollection:function(t,e){for(var n=t.geometries,i=-1,r=n.length;++i<r;)if(Pn(n[i],e))return!0;return!1}};function Pn(t,e){return!(!t||!bn.hasOwnProperty(t.type))&&bn[t.type](t,e)}function _n(t,e){return 0===mn(t,e)}function wn(t,e){for(var n,i,r,o=0,a=t.length;o<a;o++){if(0===(i=mn(t[o],e)))return!0;if(o>0&&(r=mn(t[o],t[o-1]))>0&&n<=r&&i<=r&&(n+i-r)*(1-Math.pow((n-i)/r,2))<k*r)return!0;n=i}return!1}function Sn(t,e){return!!Xe(t.map(xn),Mn(e))}function xn(t){return(t=t.map(Mn)).pop(),t}function Mn(t){return[t[0]*W,t[1]*W]}var jn=function(t,e){return(t&&yn.hasOwnProperty(t.type)?yn[t.type]:Pn)(t,e)};function On(t,e,n){var i=z(t,e-V,n).concat(e);return function(t){return i.map(function(e){return[t,e]})}}function Ln(t,e,n){var i=z(t,e-V,n).concat(e);return function(t){return i.map(function(e){return[e,t]})}}function En(){var t,e,n,i,r,o,a,u,s,c,l,h,p=10,f=p,d=90,g=360,v=2.5;function m(){return{type:"MultiLineString",coordinates:y()}}function y(){return z(U(i/d)*d,n,d).map(l).concat(z(U(u/g)*g,a,g).map(h)).concat(z(U(e/p)*p,t,p).filter(function(t){return Z(t%d)>V}).map(s)).concat(z(U(o/f)*f,r,f).filter(function(t){return Z(t%g)>V}).map(c))}return m.lines=function(){return y().map(function(t){return{type:"LineString",coordinates:t}})},m.outline=function(){return{type:"Polygon",coordinates:[l(i).concat(h(a).slice(1),l(n).reverse().slice(1),h(u).reverse().slice(1))]}},m.extent=function(t){return arguments.length?m.extentMajor(t).extentMinor(t):m.extentMinor()},m.extentMajor=function(t){return arguments.length?(i=+t[0][0],n=+t[1][0],u=+t[0][1],a=+t[1][1],i>n&&(t=i,i=n,n=t),u>a&&(t=u,u=a,a=t),m.precision(v)):[[i,u],[n,a]]},m.extentMinor=function(n){return arguments.length?(e=+n[0][0],t=+n[1][0],o=+n[0][1],r=+n[1][1],e>t&&(n=e,e=t,t=n),o>r&&(n=o,o=r,r=n),m.precision(v)):[[e,o],[t,r]]},m.step=function(t){return arguments.length?m.stepMajor(t).stepMinor(t):m.stepMinor()},m.stepMajor=function(t){return arguments.length?(d=+t[0],g=+t[1],m):[d,g]},m.stepMinor=function(t){return arguments.length?(p=+t[0],f=+t[1],m):[p,f]},m.precision=function(p){return arguments.length?(v=+p,s=On(o,r,90),c=Ln(e,t,v),l=On(u,a,90),h=Ln(i,n,v),m):v},m.extentMajor([[-180,-90+V],[180,90-V]]).extentMinor([[-180,-80-V],[180,80+V]])}function Cn(){return En()()}var In,Tn,Gn,Dn,Nn=function(t,e){var n=t[0]*W,i=t[1]*W,r=e[0]*W,o=e[1]*W,a=X(i),u=tt(i),s=X(o),c=tt(o),l=a*X(n),h=a*tt(n),p=s*X(r),f=s*tt(r),d=2*ot(nt(at(o-i)+a*s*at(r-n))),g=tt(d),v=d?function(t){var e=tt(t*=d)/g,n=tt(d-t)/g,i=n*l+e*p,r=n*h+e*f,o=n*u+e*c;return[Y(r,i)*H,Y(o,nt(i*i+r*r))*H]}:function(){return[n*H,i*H]};return v.distance=d,v},zn=function(t){return t},Vn=new O,kn=new O,An={point:ut,lineStart:ut,lineEnd:ut,polygonStart:function(){An.lineStart=Rn,An.lineEnd=Hn},polygonEnd:function(){An.lineStart=An.lineEnd=An.point=ut,Vn.add(Z(kn)),kn=new O},result:function(){var t=Vn/2;return Vn=new O,t}};function Rn(){An.point=Fn}function Fn(t,e){An.point=Bn,In=Gn=t,Tn=Dn=e}function Bn(t,e){kn.add(Dn*t-Gn*e),Gn=t,Dn=e}function Hn(){Bn(In,Tn)}var Wn=An,Zn=1/0,qn=Zn,Yn=-Zn,Xn=Yn;var Un,Jn,Kn,Qn,$n={point:function(t,e){t<Zn&&(Zn=t);t>Yn&&(Yn=t);e<qn&&(qn=e);e>Xn&&(Xn=e)},lineStart:ut,lineEnd:ut,polygonStart:ut,polygonEnd:ut,result:function(){var t=[[Zn,qn],[Yn,Xn]];return Yn=Xn=-(qn=Zn=1/0),t}},ti=0,ei=0,ni=0,ii=0,ri=0,oi=0,ai=0,ui=0,si=0,ci={point:li,lineStart:hi,lineEnd:di,polygonStart:function(){ci.lineStart=gi,ci.lineEnd=vi},polygonEnd:function(){ci.point=li,ci.lineStart=hi,ci.lineEnd=di},result:function(){var t=si?[ai/si,ui/si]:oi?[ii/oi,ri/oi]:ni?[ti/ni,ei/ni]:[NaN,NaN];return ti=ei=ni=ii=ri=oi=ai=ui=si=0,t}};function li(t,e){ti+=t,ei+=e,++ni}function hi(){ci.point=pi}function pi(t,e){ci.point=fi,li(Kn=t,Qn=e)}function fi(t,e){var n=t-Kn,i=e-Qn,r=nt(n*n+i*i);ii+=r*(Kn+t)/2,ri+=r*(Qn+e)/2,oi+=r,li(Kn=t,Qn=e)}function di(){ci.point=li}function gi(){ci.point=mi}function vi(){yi(Un,Jn)}function mi(t,e){ci.point=yi,li(Un=Kn=t,Jn=Qn=e)}function yi(t,e){var n=t-Kn,i=e-Qn,r=nt(n*n+i*i);ii+=r*(Kn+t)/2,ri+=r*(Qn+e)/2,oi+=r,ai+=(r=Qn*t-Kn*e)*(Kn+t),ui+=r*(Qn+e),si+=3*r,li(Kn=t,Qn=e)}var bi=ci;function Pi(t){this._context=t}Pi.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._context.closePath(),this._point=NaN},point:function(t,e){switch(this._point){case 0:this._context.moveTo(t,e),this._point=1;break;case 1:this._context.lineTo(t,e);break;default:this._context.moveTo(t+this._radius,e),this._context.arc(t,e,this._radius,0,B)}},result:ut};var _i,wi,Si,xi,Mi,ji=new O,Oi={point:ut,lineStart:function(){Oi.point=Li},lineEnd:function(){_i&&Ei(wi,Si),Oi.point=ut},polygonStart:function(){_i=!0},polygonEnd:function(){_i=null},result:function(){var t=+ji;return ji=new O,t}};function Li(t,e){Oi.point=Ei,wi=xi=t,Si=Mi=e}function Ei(t,e){xi-=t,Mi-=e,ji.add(nt(xi*xi+Mi*Mi)),xi=t,Mi=e}var Ci=Oi;function Ii(){this._string=[]}function Ti(t){return"m0,"+t+"a"+t+","+t+" 0 1,1 0,"+-2*t+"a"+t+","+t+" 0 1,1 0,"+2*t+"z"}Ii.prototype={_radius:4.5,_circle:Ti(4.5),pointRadius:function(t){return(t=+t)!==this._radius&&(this._radius=t,this._circle=null),this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._string.push("Z"),this._point=NaN},point:function(t,e){switch(this._point){case 0:this._string.push("M",t,",",e),this._point=1;break;case 1:this._string.push("L",t,",",e);break;default:null==this._circle&&(this._circle=Ti(this._radius)),this._string.push("M",t,",",e,this._circle)}},result:function(){if(this._string.length){var t=this._string.join("");return this._string=[],t}return null}};var Gi=function(t,e){var n,i,r=4.5;function o(t){return t&&("function"==typeof r&&i.pointRadius(+r.apply(this,arguments)),yt(t,n(i))),i.result()}return o.area=function(t){return yt(t,n(Wn)),Wn.result()},o.measure=function(t){return yt(t,n(Ci)),Ci.result()},o.bounds=function(t){return yt(t,n($n)),$n.result()},o.centroid=function(t){return yt(t,n(bi)),bi.result()},o.projection=function(e){return arguments.length?(n=null==e?(t=null,zn):(t=e).stream,o):t},o.context=function(t){return arguments.length?(i=null==t?(e=null,new Ii):new Pi(e=t),"function"!=typeof r&&i.pointRadius(r),o):e},o.pointRadius=function(t){return arguments.length?(r="function"==typeof t?t:(i.pointRadius(+t),+t),o):r},o.projection(t).context(e)},Di=function(t){return{stream:Ni(t)}};function Ni(t){return function(e){var n=new zi;for(var i in t)n[i]=t[i];return n.stream=e,n}}function zi(){}function Vi(t,e,n){var i=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),null!=i&&t.clipExtent(null),yt(n,t.stream($n)),e($n.result()),null!=i&&t.clipExtent(i),t}function ki(t,e,n){return Vi(t,function(n){var i=e[1][0]-e[0][0],r=e[1][1]-e[0][1],o=Math.min(i/(n[1][0]-n[0][0]),r/(n[1][1]-n[0][1])),a=+e[0][0]+(i-o*(n[1][0]+n[0][0]))/2,u=+e[0][1]+(r-o*(n[1][1]+n[0][1]))/2;t.scale(150*o).translate([a,u])},n)}function Ai(t,e,n){return ki(t,[[0,0],e],n)}function Ri(t,e,n){return Vi(t,function(n){var i=+e,r=i/(n[1][0]-n[0][0]),o=(i-r*(n[1][0]+n[0][0]))/2,a=-r*n[0][1];t.scale(150*r).translate([o,a])},n)}function Fi(t,e,n){return Vi(t,function(n){var i=+e,r=i/(n[1][1]-n[0][1]),o=-r*n[0][0],a=(i-r*(n[1][1]+n[0][1]))/2;t.scale(150*r).translate([o,a])},n)}zi.prototype={constructor:zi,point:function(t,e){this.stream.point(t,e)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};var Bi=16,Hi=X(30*W),Wi=function(t,e){return+e?function(t,e){function n(i,r,o,a,u,s,c,l,h,p,f,d,g,v){var m=c-i,y=l-r,b=m*m+y*y;if(b>4*e&&g--){var P=a+p,_=u+f,w=s+d,S=nt(P*P+_*_+w*w),x=ot(w/=S),M=Z(Z(w)-1)<V||Z(o-h)<V?(o+h)/2:Y(_,P),j=t(M,x),O=j[0],L=j[1],E=O-i,C=L-r,I=y*E-m*C;(I*I/b>e||Z((m*E+y*C)/b-.5)>.3||a*p+u*f+s*d<Hi)&&(n(i,r,o,a,u,s,O,L,M,P/=S,_/=S,w,g,v),v.point(O,L),n(O,L,M,P,_,w,c,l,h,p,f,d,g,v))}}return function(e){var i,r,o,a,u,s,c,l,h,p,f,d,g={point:v,lineStart:m,lineEnd:b,polygonStart:function(){e.polygonStart(),g.lineStart=P},polygonEnd:function(){e.polygonEnd(),g.lineStart=m}};function v(n,i){n=t(n,i),e.point(n[0],n[1])}function m(){l=NaN,g.point=y,e.lineStart()}function y(i,r){var o=At([i,r]),a=t(i,r);n(l,h,c,p,f,d,l=a[0],h=a[1],c=i,p=o[0],f=o[1],d=o[2],Bi,e),e.point(l,h)}function b(){g.point=v,e.lineEnd()}function P(){m(),g.point=_,g.lineEnd=w}function _(t,e){y(i=t,e),r=l,o=h,a=p,u=f,s=d,g.point=y}function w(){n(l,h,c,p,f,d,r,o,i,a,u,s,Bi,e),g.lineEnd=b,b()}return g}}(t,e):function(t){return Ni({point:function(e,n){e=t(e,n),this.stream.point(e[0],e[1])}})}(t)};var Zi=Ni({point:function(t,e){this.stream.point(t*W,e*W)}});function qi(t,e,n,i,r,o){if(!o)return function(t,e,n,i,r){function o(o,a){return[e+t*(o*=i),n-t*(a*=r)]}return o.invert=function(o,a){return[(o-e)/t*i,(n-a)/t*r]},o}(t,e,n,i,r);var a=X(o),u=tt(o),s=a*t,c=u*t,l=a/t,h=u/t,p=(u*n-a*e)/t,f=(u*e+a*n)/t;function d(t,o){return[s*(t*=i)-c*(o*=r)+e,n-c*t-s*o]}return d.invert=function(t,e){return[i*(l*t-h*e+p),r*(f-h*t-l*e)]},d}function Yi(t){return Xi(function(){return t})()}function Xi(t){var e,n,i,r,o,a,u,s,c,l,h=150,p=480,f=250,d=0,g=0,v=0,m=0,y=0,b=0,P=1,_=1,w=null,S=Qe,x=null,M=zn,j=.5;function O(t){return s(t[0]*W,t[1]*W)}function L(t){return(t=s.invert(t[0],t[1]))&&[t[0]*H,t[1]*H]}function E(){var t=qi(h,0,0,P,_,b).apply(null,e(d,g)),i=qi(h,p-t[0],f-t[1],P,_,b);return n=De(v,m,y),u=Te(e,i),s=Te(n,u),a=Wi(u,j),C()}function C(){return c=l=null,O}return O.stream=function(t){return c&&l===t?c:c=Zi(function(t){return Ni({point:function(e,n){var i=t(e,n);return this.stream.point(i[0],i[1])}})}(n)(S(a(M(l=t)))))},O.preclip=function(t){return arguments.length?(S=t,w=void 0,C()):S},O.postclip=function(t){return arguments.length?(M=t,x=i=r=o=null,C()):M},O.clipAngle=function(t){return arguments.length?(S=+t?$e(w=t*W):(w=null,Qe),C()):w*H},O.clipExtent=function(t){return arguments.length?(M=null==t?(x=i=r=o=null,zn):rn(x=+t[0][0],i=+t[0][1],r=+t[1][0],o=+t[1][1]),C()):null==x?null:[[x,i],[r,o]]},O.scale=function(t){return arguments.length?(h=+t,E()):h},O.translate=function(t){return arguments.length?(p=+t[0],f=+t[1],E()):[p,f]},O.center=function(t){return arguments.length?(d=t[0]%360*W,g=t[1]%360*W,E()):[d*H,g*H]},O.rotate=function(t){return arguments.length?(v=t[0]%360*W,m=t[1]%360*W,y=t.length>2?t[2]%360*W:0,E()):[v*H,m*H,y*H]},O.angle=function(t){return arguments.length?(b=t%360*W,E()):b*H},O.reflectX=function(t){return arguments.length?(P=t?-1:1,E()):P<0},O.reflectY=function(t){return arguments.length?(_=t?-1:1,E()):_<0},O.precision=function(t){return arguments.length?(a=Wi(u,j=t*t),C()):nt(j)},O.fitExtent=function(t,e){return ki(O,t,e)},O.fitSize=function(t,e){return Ai(O,t,e)},O.fitWidth=function(t,e){return Ri(O,t,e)},O.fitHeight=function(t,e){return Fi(O,t,e)},function(){return e=t.apply(this,arguments),O.invert=e.invert&&L,E()}}function Ui(t){var e=0,n=A/3,i=Xi(t),r=i(e,n);return r.parallels=function(t){return arguments.length?i(e=t[0]*W,n=t[1]*W):[e*H,n*H]},r}function Ji(t,e){var n=tt(t),i=(n+tt(e))/2;if(Z(i)<V)return function(t){var e=X(t);function n(t,n){return[t*e,tt(n)/e]}return n.invert=function(t,n){return[t/e,ot(n*e)]},n}(t);var r=1+n*(2*i-n),o=nt(r)/i;function a(t,e){var n=nt(r-2*i*tt(e))/i;return[n*tt(t*=i),o-n*X(t)]}return a.invert=function(t,e){var n=o-e,a=Y(t,Z(n))*et(n);return n*i<0&&(a-=A*et(t)*et(n)),[a/i,ot((r-(t*t+n*n)*i*i)/(2*i))]},a}var Ki=function(){return Ui(Ji).scale(155.424).center([0,33.6442])},Qi=function(){return Ki().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])};var $i=function(){var t,e,n,i,r,o,a=Qi(),u=Ki().rotate([154,0]).center([-2,58.5]).parallels([55,65]),s=Ki().rotate([157,0]).center([-3,19.9]).parallels([8,18]),c={point:function(t,e){o=[t,e]}};function l(t){var e=t[0],a=t[1];return o=null,n.point(e,a),o||(i.point(e,a),o)||(r.point(e,a),o)}function h(){return t=e=null,l}return l.invert=function(t){var e=a.scale(),n=a.translate(),i=(t[0]-n[0])/e,r=(t[1]-n[1])/e;return(r>=.12&&r<.234&&i>=-.425&&i<-.214?u:r>=.166&&r<.234&&i>=-.214&&i<-.115?s:a).invert(t)},l.stream=function(n){return t&&e===n?t:t=function(t){var e=t.length;return{point:function(n,i){for(var r=-1;++r<e;)t[r].point(n,i)},sphere:function(){for(var n=-1;++n<e;)t[n].sphere()},lineStart:function(){for(var n=-1;++n<e;)t[n].lineStart()},lineEnd:function(){for(var n=-1;++n<e;)t[n].lineEnd()},polygonStart:function(){for(var n=-1;++n<e;)t[n].polygonStart()},polygonEnd:function(){for(var n=-1;++n<e;)t[n].polygonEnd()}}}([a.stream(e=n),u.stream(n),s.stream(n)])},l.precision=function(t){return arguments.length?(a.precision(t),u.precision(t),s.precision(t),h()):a.precision()},l.scale=function(t){return arguments.length?(a.scale(t),u.scale(.35*t),s.scale(t),l.translate(a.translate())):a.scale()},l.translate=function(t){if(!arguments.length)return a.translate();var e=a.scale(),o=+t[0],l=+t[1];return n=a.translate(t).clipExtent([[o-.455*e,l-.238*e],[o+.455*e,l+.238*e]]).stream(c),i=u.translate([o-.307*e,l+.201*e]).clipExtent([[o-.425*e+V,l+.12*e+V],[o-.214*e-V,l+.234*e-V]]).stream(c),r=s.translate([o-.205*e,l+.212*e]).clipExtent([[o-.214*e+V,l+.166*e+V],[o-.115*e-V,l+.234*e-V]]).stream(c),h()},l.fitExtent=function(t,e){return ki(l,t,e)},l.fitSize=function(t,e){return Ai(l,t,e)},l.fitWidth=function(t,e){return Ri(l,t,e)},l.fitHeight=function(t,e){return Fi(l,t,e)},l.scale(1070)};function tr(t){return function(e,n){var i=X(e),r=X(n),o=t(i*r);return o===1/0?[2,0]:[o*r*tt(e),o*tt(n)]}}function er(t){return function(e,n){var i=nt(e*e+n*n),r=t(i),o=tt(r),a=X(r);return[Y(e*o,i*a),ot(i&&n*o/i)]}}var nr=tr(function(t){return nt(2/(1+t))});nr.invert=er(function(t){return 2*ot(t/2)});var ir=function(){return Yi(nr).scale(124.75).clipAngle(179.999)},rr=tr(function(t){return(t=rt(t))&&t/tt(t)});rr.invert=er(function(t){return t});var or=function(){return Yi(rr).scale(79.4188).clipAngle(179.999)};function ar(t,e){return[t,Q(it((R+e)/2))]}ar.invert=function(t,e){return[t,2*q(J(e))-R]};var ur=function(){return sr(ar).scale(961/B)};function sr(t){var e,n,i,r=Yi(t),o=r.center,a=r.scale,u=r.translate,s=r.clipExtent,c=null;function l(){var o=A*a(),u=r(ke(r.rotate()).invert([0,0]));return s(null==c?[[u[0]-o,u[1]-o],[u[0]+o,u[1]+o]]:t===ar?[[Math.max(u[0]-o,c),e],[Math.min(u[0]+o,n),i]]:[[c,Math.max(u[1]-o,e)],[n,Math.min(u[1]+o,i)]])}return r.scale=function(t){return arguments.length?(a(t),l()):a()},r.translate=function(t){return arguments.length?(u(t),l()):u()},r.center=function(t){return arguments.length?(o(t),l()):o()},r.clipExtent=function(t){return arguments.length?(null==t?c=e=n=i=null:(c=+t[0][0],e=+t[0][1],n=+t[1][0],i=+t[1][1]),l()):null==c?null:[[c,e],[n,i]]},l()}function cr(t){return it((R+t)/2)}function lr(t,e){var n=X(t),i=t===e?tt(t):Q(n/X(e))/Q(cr(e)/cr(t)),r=n*$(cr(t),i)/i;if(!i)return ar;function o(t,e){r>0?e<-R+V&&(e=-R+V):e>R-V&&(e=R-V);var n=r/$(cr(e),i);return[n*tt(i*t),r-n*X(i*t)]}return o.invert=function(t,e){var n=r-e,o=et(i)*nt(t*t+n*n),a=Y(t,Z(n))*et(n);return n*i<0&&(a-=A*et(t)*et(n)),[a/i,2*q($(r/o,1/i))-R]},o}var hr=function(){return Ui(lr).scale(109.5).parallels([30,30])};function pr(t,e){return[t,e]}pr.invert=pr;var fr=function(){return Yi(pr).scale(152.63)};function dr(t,e){var n=X(t),i=t===e?tt(t):(n-X(e))/(e-t),r=n/i+t;if(Z(i)<V)return pr;function o(t,e){var n=r-e,o=i*t;return[n*tt(o),r-n*X(o)]}return o.invert=function(t,e){var n=r-e,o=Y(t,Z(n))*et(n);return n*i<0&&(o-=A*et(t)*et(n)),[o/i,r-et(i)*nt(t*t+n*n)]},o}var gr=function(){return Ui(dr).scale(131.154).center([0,13.9389])},vr=1.340264,mr=-.081106,yr=893e-6,br=.003796,Pr=nt(3)/2;function _r(t,e){var n=ot(Pr*tt(e)),i=n*n,r=i*i*i;return[t*X(n)/(Pr*(vr+3*mr*i+r*(7*yr+9*br*i))),n*(vr+mr*i+r*(yr+br*i))]}_r.invert=function(t,e){for(var n,i=e,r=i*i,o=r*r*r,a=0;a<12&&(o=(r=(i-=n=(i*(vr+mr*r+o*(yr+br*r))-e)/(vr+3*mr*r+o*(7*yr+9*br*r)))*i)*r*r,!(Z(n)<k));++a);return[Pr*t*(vr+3*mr*r+o*(7*yr+9*br*r))/X(i),ot(tt(i)/Pr)]};var wr=function(){return Yi(_r).scale(177.158)};function Sr(t,e){var n=X(e),i=X(t)*n;return[n*tt(t)/i,tt(e)/i]}Sr.invert=er(q);var xr=function(){return Yi(Sr).scale(144.049).clipAngle(60)},Mr=function(){var t,e,n,i,r,o,a,u=1,s=0,c=0,l=1,h=1,p=0,f=null,d=1,g=1,v=Ni({point:function(t,e){var n=b([t,e]);this.stream.point(n[0],n[1])}}),m=zn;function y(){return d=u*l,g=u*h,o=a=null,b}function b(n){var i=n[0]*d,r=n[1]*g;if(p){var o=r*t-i*e;i=i*t+r*e,r=o}return[i+s,r+c]}return b.invert=function(n){var i=n[0]-s,r=n[1]-c;if(p){var o=r*t+i*e;i=i*t-r*e,r=o}return[i/d,r/g]},b.stream=function(t){return o&&a===t?o:o=v(m(a=t))},b.postclip=function(t){return arguments.length?(m=t,f=n=i=r=null,y()):m},b.clipExtent=function(t){return arguments.length?(m=null==t?(f=n=i=r=null,zn):rn(f=+t[0][0],n=+t[0][1],i=+t[1][0],r=+t[1][1]),y()):null==f?null:[[f,n],[i,r]]},b.scale=function(t){return arguments.length?(u=+t,y()):u},b.translate=function(t){return arguments.length?(s=+t[0],c=+t[1],y()):[s,c]},b.angle=function(n){return arguments.length?(e=tt(p=n%360*W),t=X(p),y()):p*H},b.reflectX=function(t){return arguments.length?(l=t?-1:1,y()):l<0},b.reflectY=function(t){return arguments.length?(h=t?-1:1,y()):h<0},b.fitExtent=function(t,e){return ki(b,t,e)},b.fitSize=function(t,e){return Ai(b,t,e)},b.fitWidth=function(t,e){return Ri(b,t,e)},b.fitHeight=function(t,e){return Fi(b,t,e)},b};function jr(t,e){var n=e*e,i=n*n;return[t*(.8707-.131979*n+i*(i*(.003971*n-.001529*i)-.013791)),e*(1.007226+n*(.015085+i*(.028874*n-.044475-.005916*i)))]}jr.invert=function(t,e){var n,i=e,r=25;do{var o=i*i,a=o*o;i-=n=(i*(1.007226+o*(.015085+a*(.028874*o-.044475-.005916*a)))-e)/(1.007226+o*(.045255+a*(.259866*o-.311325-.005916*11*a)))}while(Z(n)>V&&--r>0);return[t/(.8707+(o=i*i)*(o*(o*o*o*(.003971-.001529*o)-.013791)-.131979)),i]};var Or=function(){return Yi(jr).scale(175.295)};function Lr(t,e){return[X(e)*tt(t),tt(e)]}Lr.invert=er(ot);var Er=function(){return Yi(Lr).scale(249.5).clipAngle(90+V)};function Cr(t,e){var n=X(e),i=1+X(t)*n;return[n*tt(t)/i,tt(e)/i]}Cr.invert=er(function(t){return 2*q(t)});var Ir=function(){return Yi(Cr).scale(250).clipAngle(142)};function Tr(t,e){return[Q(it((R+e)/2)),-t]}Tr.invert=function(t,e){return[-e,2*q(J(t))-R]};var Gr=function(){var t=sr(Tr),e=t.center,n=t.rotate;return t.center=function(t){return arguments.length?e([-t[1],t[0]]):[(t=e())[1],-t[0]]},t.rotate=function(t){return arguments.length?n([t[0],t[1],t.length>2?t[2]+90:90]):[(t=n())[0],t[1],t[2]-90]},n([0,0,90]).scale(159.155)},Dr=function(t){function e(){var e=t.call(this)||this;return e.className="MapSeriesDataItem",e.values.value={},e.applyTheme(),e}return Object(c.c)(e,t),Object.defineProperty(e.prototype,"value",{get:function(){return this.values.value.value},set:function(t){this.setValue("value",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"zoomLevel",{get:function(){return this.properties.zoomLevel},set:function(t){this.setProperty("zoomLevel",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"zoomGeoPoint",{get:function(){return this.properties.zoomGeoPoint},set:function(t){this.setProperty("zoomGeoPoint",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"east",{get:function(){return this._east},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"west",{get:function(){return this._west},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"south",{get:function(){return this._south},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"north",{get:function(){return this._north},enumerable:!0,configurable:!0}),e.prototype.updateExtremes=function(){var t=this.getFeature().geometry;if(t){var e=ye(t),n=e[0][0],i=e[0][1],r=e[1][1],o=e[1][0],a=!1;r!=this.north&&(this._north=v.round(r,6),a=!0),i!=this.south&&(this._south=v.round(i,6),a=!0),o!=this.east&&(this._east=v.round(o,6),a=!0),n!=this.west&&(this._west=v.round(n,6),a=!0),this._east<this._west&&(this._east=180,this._west=-180),a&&this.component.invalidateDataItems()}},e.prototype.getFeature=function(){return{}},e}(f.b),Nr=function(t){function e(){var e=t.call(this)||this;return e.className="MapSeries",e.isMeasured=!1,e.nonScalingStroke=!0,e.dataFields.value="value",e.ignoreBounds=!1,e.tooltip&&(e.tooltip.showInViewport=!0),e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createDataItem=function(){return new Dr},e.prototype.checkInclude=function(t,e,n){if(t){if(0==t.length)return!1;if(-1==t.indexOf(n))return!1}return!(e&&e.length>0&&-1!=e.indexOf(n))},Object.defineProperty(e.prototype,"useGeodata",{get:function(){return this.getPropertyValue("useGeodata")},set:function(t){this.setPropertyValue("useGeodata",t)&&this.invalidateData()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"include",{get:function(){return this.getPropertyValue("include")},set:function(t){this.setPropertyValue("include",t)&&this.processIncExc()},enumerable:!0,configurable:!0}),e.prototype.processIncExc=function(){this.invalidateData()},Object.defineProperty(e.prototype,"ignoreBounds",{get:function(){return this.getPropertyValue("ignoreBounds")},set:function(t){this.setPropertyValue("ignoreBounds",t)&&this.chart&&this.chart.updateExtremes()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"exclude",{get:function(){return this.getPropertyValue("exclude")},set:function(t){this.setPropertyValue("exclude",t)&&this.processIncExc()},enumerable:!0,configurable:!0}),e.prototype.handleObjectAdded=function(t){var e=t.newValue;e.parent=this,e.series=this,e.strokeWidth=e.strokeWidth},Object.defineProperty(e.prototype,"geodata",{get:function(){return this._geodata},set:function(t){if(t!=this._geodata){this._geodata=t,this.reverseGeodata&&this.chart.processReverseGeodata(this._geodata);for(var e=this.data.length-1;e>=0;e--)1==this.data[e].madeFromGeoData&&this.data.splice(e,1);this.disposeData(),this.invalidateData()}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"reverseGeodata",{get:function(){return this.getPropertyValue("reverseGeodata")},set:function(t){this.setPropertyValue("reverseGeodata",t)&&this._geodata&&this.chart.processReverseGeodata(this._geodata)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"geodataSource",{get:function(){return this._dataSources.geodata||this.getDataSource("geodata"),this._dataSources.geodata},set:function(t){var e=this;this._dataSources.geodata&&this.removeDispose(this._dataSources.geodata),this._dataSources.geodata=t,this._dataSources.geodata.component=this,this.events.on("inited",function(){e.loadData("geodata")},void 0,!1),this.setDataSourceEvents(t,"geodata")},enumerable:!0,configurable:!0}),e.prototype.getFeatures=function(){},e.prototype.validateDataItems=function(){t.prototype.validateDataItems.call(this),this.updateExtremes()},e.prototype.updateExtremes=function(){var t,e,n,i;this.dataItems.each(function(r){(r.north>t||!g.isNumber(t))&&(t=r.north),(r.south<e||!g.isNumber(e))&&(e=r.south),(r.west<i||!g.isNumber(i))&&(i=r.west),(r.east>n||!g.isNumber(n))&&(n=r.east)}),this._mapObjects&&this._mapObjects.each(function(r){(r.north>t||!g.isNumber(t))&&(t=r.north),(r.south<e||!g.isNumber(e))&&(e=r.south),(r.west<i||!g.isNumber(i))&&(i=r.west),(r.east>n||!g.isNumber(n))&&(n=r.east)}),this.north==t&&this.east==n&&this.south==e&&this.west==i||(this._north=t,this._east=n,this._west=i,this._south=e,this.dispatch("geoBoundsChanged"),this.ignoreBounds||this.chart.updateExtremes())},Object.defineProperty(e.prototype,"north",{get:function(){return g.isNumber(this._northDefined)?this._northDefined:this._north},set:function(t){this._northDefined=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"south",{get:function(){return g.isNumber(this._southDefined)?this._southDefined:this._south},set:function(t){this._southDefined=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"west",{get:function(){return g.isNumber(this._westDefined)?this._westDefined:this._west},set:function(t){this._westDefined=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"east",{get:function(){return g.isNumber(this._eastDefined)?this._eastDefined:this._east},set:function(t){this._eastDefined=t},enumerable:!0,configurable:!0}),e.prototype.processConfig=function(e){if(g.hasValue(e.geodata)&&g.isString(e.geodata)){var n=e.geodata;if(g.hasValue(window["am4geodata_"+e.geodata]))e.geodata=window["am4geodata_"+e.geodata];else try{e.geodata=JSON.parse(e.geodata)}catch(t){throw Error("MapChart error: Geodata `"+n+"` is not loaded or is incorrect.")}}t.prototype.processConfig.call(this,e)},e.prototype.asIs=function(e){return"geodata"==e||t.prototype.asIs.call(this,e)},e.prototype.updateTooltipBounds=function(){this.tooltip&&this.topParent&&this.tooltip.setBounds({x:10,y:10,width:this.topParent.maxWidth-20,height:this.topParent.maxHeight-20})},e}(f.a);d.c.registeredClasses.MapSeries=Nr,d.c.registeredClasses.MapSeriesDataItem=Dr;var zr=n("C6dT"),Vr=function(t){function e(){var e=t.call(this)||this;return e.className="MapObject",e.isMeasured=!1,e.layout="none",e.clickable=!0,e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.validate=function(){this.series&&this.series.itemReaderText&&(this.readerTitle=this.series.itemReaderText),t.prototype.validate.call(this)},e.prototype.updateExtremes=function(){var t=this.getFeature();if(t){var e=t.geometry;if(e){var n=ye(e),i=n[0][0],r=n[0][1],o=n[1][1],a=n[1][0],u=!1;o!=this.north&&(this._north=v.round(o,8),u=!0),r!=this.south&&(this._south=v.round(r),u=!0),a!=this.east&&(this._east=v.round(a),u=!0),i!=this.west&&(this._west=v.round(i),u=!0),u&&(this.dispatch("geoBoundsChanged"),this.series&&this.series.invalidateDataItems())}}},e.prototype.getFeature=function(){return{}},Object.defineProperty(e.prototype,"east",{get:function(){return g.isNumber(this._east)?this._east:this.dataItem?this.dataItem.east:void 0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"west",{get:function(){return g.isNumber(this._west)?this._west:this.dataItem?this.dataItem.west:void 0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"south",{get:function(){return g.isNumber(this._south)?this._south:this.dataItem?this.dataItem.south:void 0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"north",{get:function(){return g.isNumber(this._north)?this._north:this.dataItem?this.dataItem.north:void 0},enumerable:!0,configurable:!0}),e.prototype.showTooltip=function(e){var n=t.prototype.showTooltip.call(this,e);return n&&"always"==this.showTooltipOn&&!this.series.chart.events.has("mappositionchanged",this.handleTooltipMove,this)&&this.addDisposer(this.series.chart.events.on("mappositionchanged",this.handleTooltipMove,this)),n},e.prototype.handleTooltipMove=function(t){this.tooltip.isHidden||this.showTooltip()},e.prototype.setDataItem=function(e){t.prototype.setDataItem.call(this,e),this.applyAccessibility()},e}(zr.a);d.c.registeredClasses.MapObject=Vr;var kr=function(t){function e(){var e=t.call(this)||this;return e.className="MapImage",e.applyTheme(),e}return Object(c.c)(e,t),Object.defineProperty(e.prototype,"latitude",{get:function(){var t=this.getPropertyValue("latitude");return!g.isNumber(t)&&this.dataItem&&this.dataItem.geoPoint&&(t=this.dataItem.geoPoint.latitude),t},set:function(t){this.setPropertyValue("latitude",t,!1,!0),this.updateExtremes()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"longitude",{get:function(){var t=this.getPropertyValue("longitude");return!g.isNumber(t)&&this.dataItem&&this.dataItem.geoPoint&&(t=this.dataItem.geoPoint.longitude),t},set:function(t){this.setPropertyValue("longitude",t,!1,!0),this.updateExtremes()},enumerable:!0,configurable:!0}),e.prototype.validatePosition=function(){if(g.isNumber(this.latitude)&&g.isNumber(this.longitude)){var e=this.series.chart.projection.d3Projection([this.longitude,this.latitude]),n=this.series.chart.projection.d3Path({type:"Point",coordinates:[this.longitude,this.latitude]});this.__disabled=!n,this.moveTo({x:e[0],y:e[1]})}t.prototype.validatePosition.call(this)},e.prototype.getFeature=function(){return{type:"Feature",geometry:{type:"Point",coordinates:[this.longitude,this.latitude]}}},e}(Vr);d.c.registeredClasses.MapImage=kr;var Ar=n("R6wv"),Rr=n("o+vr"),Fr=n.n(Rr),Br=n("hJ5i");function Hr(t){return Br.map(t,function(t){var e=t[0],n=t[1],i=[];return e&&i.push(Zr(e)),n&&i.push(Zr(n)),i})}function Wr(t){return Br.map(t,function(t){return Zr(t)})}function Zr(t){return Br.map(t,function(t){return Yr(t)})}function qr(t){return Br.map(t,Xr)}function Yr(t){return{longitude:t[0],latitude:t[1]}}function Xr(t){return[t.longitude,t.latitude]}function Ur(t){return Br.map(t,function(t){return Br.map(t,Xr)})}function Jr(t){return Br.map(t,function(t){var e=t[0],n=t[1],i=[];return e&&i.push(qr(e)),n&&i.push(qr(n)),i})}function Kr(t,e,n){return[Fe().center([t,e]).radius(n)().coordinates]}function Qr(t,e,n,i){var r=[];-180==i&&(i=-179.9999),-90==n&&(n=-89.9999),90==t&&(t=89.9999),180==e&&(e=179.9999);for(var o=Math.min(90,(e-i)/Math.ceil((e-i)/90)),a=(t-n)/Math.ceil((t-n)/90),u=i;u<e;u+=o){var s=[];r.push([s]),u+o>e&&(o=e-u);for(var c=u;c<=u+o;c+=5)s.push([c,t]);for(var l=t;l>=n;l-=a)s.push([u+o,l]);for(c=u+o;c>=u;c-=5)s.push([c,n]);for(l=n;l<=t;l+=a)s.push([u,l])}return r}var $r=function(t){function e(){var e=t.call(this)||this;e.className="MapPolygon",e.polygon=e.createChild(Ar.a),e.polygon.shouldClone=!1,e.polygon.applyOnClones=!0,e.setPropertyValue("precision",.5);var n=new p.a;return e.fill=n.getFor("secondaryButton"),e.stroke=n.getFor("secondaryButtonStroke"),e.strokeOpacity=1,e.tooltipPosition="pointer",e.nonScalingStroke=!0,e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.getFeature=function(){if(this.multiPolygon&&this.multiPolygon.length>0)return{type:"Feature",geometry:{type:"MultiPolygon",coordinates:this.multiPolygon}}},Object.defineProperty(e.prototype,"multiGeoPolygon",{get:function(){var t=this.getPropertyValue("multiGeoPolygon");return!t&&this.dataItem&&(t=this.dataItem.multiGeoPolygon),t},set:function(t){this.setPropertyValue("multiGeoPolygon",t,!0),this.multiPolygon=Jr(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"multiPolygon",{get:function(){var t=this.getPropertyValue("multiPolygon");return!t&&this.dataItem&&(t=this.dataItem.multiPolygon),t},set:function(t){this.setPropertyValue("multiPolygon",t)&&(this.updateExtremes(),this.invalidate())},enumerable:!0,configurable:!0}),e.prototype.validate=function(){if(this.series){var e=this.series.chart.projection,n=e.d3Path;if(this.multiPolygon){if(this.series){var i={type:"MultiPolygon",coordinates:this.multiPolygon};e.d3Projection.precision(this.precision),this.polygon.path=n(i)}if(this.series.calculateVisualCenter){var r=0,o=this.multiPolygon[0];if(this.multiPolygon.length>1)for(var a=0;a<this.multiPolygon.length;a++){var u=this.multiPolygon[a],s=Vt({type:"Polygon",coordinates:u});s>r&&(o=u,r=s)}var c=Fr()(o);this._visualLongitude=c[0],this._visualLatitude=c[1]}else this._visualLongitude=this.longitude,this._visualLatitude=this.latitude}}t.prototype.validate.call(this)},e.prototype.measureElement=function(){},Object.defineProperty(e.prototype,"latitude",{get:function(){return this.north+(this.south-this.north)/2},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"longitude",{get:function(){return this.east+(this.west-this.east)/2},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"visualLatitude",{get:function(){var t=this.getPropertyValue("visualLatitude");return g.isNumber(t)?t:this._adapterO?this._adapterO.apply("visualLatitude",this._visualLatitude):this._visualLatitude},set:function(t){this.setPropertyValue("visualLatitude",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"visualLongitude",{get:function(){var t=this.getPropertyValue("visualLongitude");return g.isNumber(t)?t:this._adapterO?this._adapterO.apply("visualLongitude",this._visualLongitude):this._visualLongitude},set:function(t){this.setPropertyValue("visualLongitude",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pixelWidth",{get:function(){return this.polygon.pixelWidth},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pixelHeight",{get:function(){return this.polygon.pixelHeight},enumerable:!0,configurable:!0}),e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.polygon.copyFrom(e.polygon)},e.prototype.updateExtremes=function(){t.prototype.updateExtremes.call(this)},Object.defineProperty(e.prototype,"boxArea",{get:function(){return(this.north-this.south)*(this.east-this.west)},enumerable:!0,configurable:!0}),e.prototype.getTooltipX=function(){return this.series.chart.projection.convert({longitude:this.visualLongitude,latitude:this.visualLatitude}).x},e.prototype.getTooltipY=function(){return this.series.chart.projection.convert({longitude:this.visualLongitude,latitude:this.visualLatitude}).y},Object.defineProperty(e.prototype,"precision",{get:function(){return this.getPropertyValue("precision")},set:function(t){this.setPropertyValue("precision",t,!0)},enumerable:!0,configurable:!0}),e}(Vr);d.c.registeredClasses.MapPolygon=$r;var to=n("vMqJ"),eo=n("v9UT"),no=n("Wglt"),io=function(t){function e(){var e=t.call(this)||this;return e.className="MapPolygonSeriesDataItem",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.getFeature=function(){if(this.multiPolygon&&this.multiPolygon.length>0)return{type:"Feature",geometry:{type:"MultiPolygon",coordinates:this.multiPolygon}}},Object.defineProperty(e.prototype,"mapPolygon",{get:function(){var t=this;if(!this._mapPolygon){var e=this.component.mapPolygons.create();this._mapPolygon=e,this.addSprite(e),this._disposers.push(new h.b(function(){t.component&&t.component.mapPolygons.removeValue(e)})),this.mapObject=e}return this._mapPolygon},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"polygon",{get:function(){return this._polygon},set:function(t){this._polygon=t,this.multiPolygon=[t]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"multiPolygon",{get:function(){return this._multiPolygon},set:function(t){this._multiPolygon=t,this.updateExtremes()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"geoPolygon",{get:function(){return this._geoPolygon},set:function(t){this._geoPolygon=t,this.multiGeoPolygon=[t]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"multiGeoPolygon",{get:function(){return this._multiGeoPolygon},set:function(t){this._multiGeoPolygon=t,this.multiPolygon=Jr(t)},enumerable:!0,configurable:!0}),e}(Dr),ro=function(t){function e(){var e=t.call(this)||this;return e.calculateVisualCenter=!1,e.className="MapPolygonSeries",e.dataFields.multiPolygon="multiPolygon",e.dataFields.polygon="polygon",e.dataFields.geoPolygon="geoPolygon",e.dataFields.multiGeoPolygon="multiGeoPolygon",e.setPropertyValue("sortPolygonsBy","area"),e.setPropertyValue("sortPolygonsReversed",!1),e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createDataItem=function(){return new io},e.prototype.processIncExc=function(){this.mapPolygons.clear(),t.prototype.processIncExc.call(this)},e.prototype.validateData=function(){if(this.useGeodata||this.geodata){var e=this._dataSources.geodata?void 0:this.chart.geodata;if(this.geodata&&(e=this.geodata),e){var n=void 0;if("FeatureCollection"==e.type?n=e.features:"Feature"==e.type?n=[e]:-1!=["Point","LineString","Polygon","MultiPoint","MultiLineString","MultiPolygon"].indexOf(e.type)?n=[{geometry:e}]:console.log("nothing found in geoJSON"),n)for(var i=function(t,e){var i=n[t],o=i.geometry;if(o){var a=o.type,u=i.id;if(r.chart.geodataNames&&r.chart.geodataNames[u]&&(i.properties.name=r.chart.geodataNames[u]),"Polygon"==a||"MultiPolygon"==a){if(!r.checkInclude(r.include,r.exclude,u))return"continue";var s=o.coordinates;s&&"Polygon"==a&&(s=[s]);var c=Br.find(r.data,function(t,e){return t.id==u});c?c.multiPolygon||(c.multiPolygon=s):(c={multiPolygon:s,id:u,madeFromGeoData:!0},r.data.push(c)),eo.softCopyProperties(i.properties,c)}}},r=this,o=0,a=n.length;o<a;o++)i(o)}}t.prototype.validateData.call(this)},e.prototype.validate=function(){if(t.prototype.validate.call(this),this.dataItems.each(function(t){eo.used(t.mapPolygon)}),"none"!=this.sortPolygonsBy){var e=this.sortPolygonsBy,n=this.sortPolygonsReversed;this.mapPolygons.sort(function(t,i){var r="",o="",a=-1,u=1;switch(e){case"area":r=t.boxArea,o=i.boxArea,a=-1,u=1;break;case"name":r=t.dataItem.dataContext.name||"",o=i.dataItem.dataContext.name||"",a=1,u=-1;break;case"id":r=t.dataItem.dataContext.id||"",o=i.dataItem.dataContext.id||"",a=1,u=-1;break;case"latitude":r=n?t.south:t.north,o=n?i.south:i.north,a=-1,u=1;break;case"longitude":r=n?t.east:t.west,o=n?i.east:i.west,a=1,u=-1}return r<o?n?u:a:r>o?n?a:u:0}),this.mapPolygons.each(function(t,e){t.validate(),t.zIndex||t.propertyFields.zIndex||(t.zIndex=1e6-e)})}},Object.defineProperty(e.prototype,"mapPolygons",{get:function(){if(!this._mapPolygons){var t=new $r,e=new to.e(t);this._disposers.push(new to.c(e)),this._disposers.push(e.template),e.template.focusable=!0,e.events.on("inserted",this.handleObjectAdded,this,!1),this._mapPolygons=e,this._mapObjects=e}return this._mapPolygons},enumerable:!0,configurable:!0}),e.prototype.getPolygonById=function(t){return no.find(this.mapPolygons.iterator(),function(e){return e.dataItem.dataContext.id==t})},e.prototype.copyFrom=function(e){this.mapPolygons.template.copyFrom(e.mapPolygons.template),t.prototype.copyFrom.call(this,e)},e.prototype.getFeatures=function(){var t=this,e=[];return this.dataItems.each(function(t){var n=t.getFeature();n&&e.push(n)}),this.mapPolygons.each(function(n){if(-1==t.dataItems.indexOf(n._dataItem)){var i=n.getFeature();i&&e.push(i)}}),e},Object.defineProperty(e.prototype,"sortPolygonsBy",{get:function(){return this.getPropertyValue("sortPolygonsBy")},set:function(t){this.setPropertyValue("sortPolygonsBy",t)&&this.invalidateData()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"sortPolygonsReversed",{get:function(){return this.getPropertyValue("sortPolygonsReversed")},set:function(t){this.setPropertyValue("sortPolygonsReversed",t)&&this.invalidateData()},enumerable:!0,configurable:!0}),e}(Nr);d.c.registeredClasses.MapPolygonSeries=ro,d.c.registeredClasses.MapPolygonSeriesDataItem=io;var oo=function(){function t(){this.d3Projection=fr()}return Object.defineProperty(t.prototype,"d3Projection",{get:function(){return this._d3Projection},set:function(t){this._d3Projection=t,t.precision(.1),this._d3Path=Gi().projection(t),this.chart&&this.chart.invalidateProjection()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"d3Path",{get:function(){return this._d3Path},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"scale",{get:function(){return this.d3Projection.scale()/100},enumerable:!0,configurable:!0}),t.prototype.convert=function(t){var e=this.d3Projection([t.longitude,t.latitude]);if(e)return{x:e[0],y:e[1]}},t.prototype.invert=function(t){var e=this.d3Projection.invert([t.x,t.y]);if(e)return{longitude:e[0],latitude:e[1]}},t.prototype.project=function(t,e){return this.convert({longitude:t*v.DEGREES,latitude:e*v.DEGREES})},t.prototype.unproject=function(t,e){return this.invert({x:t,y:e})},t.prototype.rotate=function(t,e,n,i){var r=e*v.RADIANS,o=n*v.RADIANS;i*=v.RADIANS;var a=t.longitude*v.RADIANS+r,u=t.latitude*v.RADIANS,s=Math.cos(o),c=Math.sin(o),l=Math.cos(i),h=Math.sin(i),p=Math.cos(u),f=Math.cos(a)*p,d=Math.sin(a)*p,g=Math.sin(u),m=g*s+f*c;return{longitude:v.DEGREES*Math.atan2(d*l-m*h,f*s-g*c),latitude:v.DEGREES*Math.asin(m*l+d*h)}},t.prototype.unrotate=function(t,e,n,i){var r=e*v.RADIANS,o=n*v.RADIANS;i*=v.RADIANS;var a=t.longitude*v.RADIANS-r,u=t.latitude*v.RADIANS,s=Math.cos(o),c=Math.sin(o),l=Math.cos(i),h=Math.sin(i),p=Math.cos(u),f=Math.cos(a)*p,d=Math.sin(a)*p,g=Math.sin(u),m=g*l-d*h;return{longitude:v.DEGREES*Math.atan2(d*l+g*h,f*s+m*c),latitude:v.DEGREES*Math.asin(m*s-f*c)}},t.prototype.intermediatePoint=function(t,e,n){var i=Nn([t.longitude,t.latitude],[e.longitude,e.latitude])(n);return{longitude:i[0],latitude:i[1]}},t.prototype.multiDistance=function(t){for(var e=0,n=0;n<t.length;n++){var i=t[n];if(i.length>1)for(var r=1;r<i.length;r++){var o=i[r-1],a=i[r];e+=this.distance(o,a)}}return e},t.prototype.distance=function(t,e){return mn([t.longitude,t.latitude],[e.longitude,e.latitude])},t.prototype.positionToPoint=function(t,e){if(t){var n=this.positionToGeoPoint(t,e),i=this.positionToGeoPoint(t,e-.01),r=this.positionToGeoPoint(t,e+.01);if(i&&r){var o=this.convert(n),a=this.convert(i),u=this.convert(r);return{x:o.x,y:o.y,angle:v.getAngle(a,u)}}}return{x:0,y:0,angle:0}},t.prototype.positionToGeoPoint=function(t,e){if(t){for(var n=this.multiDistance(t),i=0,r=0,o=0,a=void 0,u=void 0,s=0;s<t.length;s++){var c=t[s];if(c.length>1){for(var l=1;l<c.length;l++)if(a=c[l-1],u=c[l],r=i/n,o=(i+=this.distance(a,u))/n,r<=e&&o>e){s=t.length;break}}else 1==c.length&&(a=c[0],u=c[0],r=0,o=1)}if(a&&u){var h=(e-r)/(o-r);return this.intermediatePoint(a,u,h)}}return{longitude:0,latitude:0}},t}();d.c.registeredClasses.Projection=oo;var ao=n("FzPm"),uo=n("GtDR"),so=n("8ZqG"),co=function(t){function e(){var e=t.call(this)||this;e._chart=new h.d,e.className="SmallMap",e.align="left",e.valign="bottom",e.percentHeight=20,e.percentWidth=20,e.margin(5,5,5,5);var n=new p.a;e.background.fillOpacity=.9,e.background.fill=n.getFor("background"),e.events.on("hit",e.moveToPosition,e,!1),e.events.on("maxsizechanged",e.updateMapSize,e,!1),e.seriesContainer=e.createChild(zr.a),e.seriesContainer.shouldClone=!1;var i=e.createChild(uo.a);return i.shouldClone=!1,i.stroke=n.getFor("alternativeBackground"),i.strokeWidth=1,i.strokeOpacity=.5,i.fill=Object(so.c)(),i.verticalCenter="middle",i.horizontalCenter="middle",i.isMeasured=!1,i.visible=!1,e.rectangle=i,e._disposers.push(e._chart),e.applyTheme(),e}return Object(c.c)(e,t),Object.defineProperty(e.prototype,"series",{get:function(){return this._series||(this._series=new to.b,this._series.events.on("inserted",this.handleSeriesAdded,this,!1),this._series.events.on("removed",this.handleSeriesRemoved,this,!1)),this._series},enumerable:!0,configurable:!0}),e.prototype.handleSeriesAdded=function(t){var e=t.newValue;if(this.chart.series.contains(e)){var n=e.clone();this._series.removeValue(e),this._series.push(n),e=n,this.chart.dataUsers.push(n)}e.chart=this.chart,e.parent=this.seriesContainer,e.interactionsEnabled=!1,e.events.on("inited",this.updateMapSize,this,!1),e.hidden=!1},e.prototype.handleSeriesRemoved=function(t){this.invalidate()},e.prototype.moveToPosition=function(t){var e=eo.spritePointToSprite(t.spritePoint,this,this.seriesContainer),n=this.chart.seriesPointToGeo(e);this.chart.zoomToGeoPoint(n,this.chart.zoomLevel,!0)},Object.defineProperty(e.prototype,"chart",{get:function(){return this._chart.get()},set:function(t){this.chart!=t&&this._chart.set(t,new h.c([t.events.on("mappositionchanged",this.updateRectangle,this,!1),t.events.on("scaleratiochanged",this.updateMapSize,this,!1)]))},enumerable:!0,configurable:!0}),e.prototype.updateRectangle=function(){var t=this.chart,e=t.zoomLevel,n=this.rectangle;n.width=this.pixelWidth/e,n.height=this.pixelHeight/e;var i=Math.min(this.percentWidth,this.percentHeight)/100,r=t.seriesContainer;n.x=Math.ceil(-r.pixelX*i/e)+this.seriesContainer.pixelX,n.y=Math.ceil(-r.pixelY*i/e)+this.seriesContainer.pixelY,n.validate()},e.prototype.updateMapSize=function(){if(this.chart){var t=this.chart.scaleRatio*Math.min(this.percentWidth,this.percentHeight)/100;this.seriesContainer.scale=t;var e={width:0,height:0,x:0,y:0};try{e=this.seriesContainer.group.node.getBBox()}catch(t){}e.width>0&&(this.rectangle.visible=!0),this.seriesContainer.x=this.pixelWidth/2-e.x*t-e.width/2*t,this.seriesContainer.y=this.pixelHeight/2-e.y*t-e.height/2*t,this.updateRectangle(),this.afterDraw()}},e.prototype.afterDraw=function(){t.prototype.afterDraw.call(this),this.rectangle.maskRectangle={x:-1,y:-1,width:Math.ceil(this.pixelWidth+2),height:Math.ceil(this.pixelHeight+2)}},e.prototype.processConfig=function(e){if(e&&g.hasValue(e.series)&&g.isArray(e.series))for(var n=0,i=e.series.length;n<i;n++){var r=e.series[n];g.hasValue(r)&&g.isString(r)&&this.map.hasKey(r)&&(e.series[n]=this.map.getKey(r))}t.prototype.processConfig.call(this,e)},e}(zr.a);d.c.registeredClasses.SmallMap=co;var lo=n("WYhe"),ho=n("Q4nc"),po=n("0FpR");function fo(t){var e=vo(t.longitude),n=Math.asin(Math.sin(t.latitude*v.RADIANS))*v.DEGREES,i=vo(t.latitude);return Math.abs(i)>90&&(e=vo(e+180)),t.longitude=e,t.latitude=n,t}function go(t){return Br.each(t,function(t){Br.each(t,function(t){fo(t)})}),t}function vo(t){return(t%=360)>180&&(t-=360),t<-180&&(t+=360),t}function mo(t){return{x:t.longitude,y:t.latitude}}var yo=function(t){function e(){var e=t.call(this)||this;return e.adjustRotation=!0,e.className="MapLineObject",e.isMeasured=!1,e.layout="none",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.validatePosition=function(){var e=this.mapLine;if(e){var n=e.positionToPoint(this.position);if(this.x=n.x,this.y=n.y,this.adjustRotation&&(this.rotation=n.angle),this.mapLine.dataItem){var i=this.mapLine.dataItem.component;this.scale=1/i.scale}if(e.shortestDistance){var r=this.mapLine.series.chart.projection,o=r.positionToGeoPoint(e.multiGeoLine,this.position),a=r.d3Path({type:"Point",coordinates:[o.longitude,o.latitude]});this.__disabled=!a}}t.prototype.validatePosition.call(this)},Object.defineProperty(e.prototype,"position",{get:function(){return this.getPropertyValue("position")},set:function(t){this.setPropertyValue("position",t,!1,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"adjustRotation",{get:function(){return this.getPropertyValue("adjustRotation")},set:function(t){this.setPropertyValue("adjustRotation",t,!1,!0)},enumerable:!0,configurable:!0}),e}(zr.a);d.c.registeredClasses.MapLineObject=yo;var bo=function(t){function e(){var e=t.call(this)||this;return e.className="MapImageSeriesDataItem",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.getFeature=function(){return{type:"Feature",geometry:{type:"Point",coordinates:this.point}}},Object.defineProperty(e.prototype,"mapImage",{get:function(){var t=this;if(!this._mapImage){var e=this.component.mapImages.create();this.addSprite(e),this._mapImage=e,this._disposers.push(e),this._disposers.push(new h.b(function(){t.component&&t.component.mapImages.removeValue(e)})),this.mapObject=e}return this._mapImage},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"point",{get:function(){return this._point},set:function(t){this._point=t,this._geoPoint=Yr(t),this.updateExtremes()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"multiPoint",{get:function(){return[this._point]},set:function(t){this._point=t[0],this._geoPoint=Yr(this._point),this.updateExtremes()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"geoPoint",{get:function(){return this._geoPoint},set:function(t){this._geoPoint=t,this.point=[t.longitude,t.latitude]},enumerable:!0,configurable:!0}),e}(Dr),Po=function(t){function e(){var e=t.call(this)||this;return e.className="MapImageSeries",e.dataFields.multiPoint="multiPoint",e.dataFields.point="point",e.dataFields.geoPoint="geoPoint",e.dataFields.multiGeoPoint="multiGeoPoint",e.ignoreBounds=!0,e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createDataItem=function(){return new bo},e.prototype.validateData=function(){var e=this;if(this.data.length>0&&0==this._parseDataFrom&&this.mapImages.clear(),this.useGeodata&&(this.useGeodata||this.geodata)){var n=this.chart.geodata,i=void 0;if("FeatureCollection"==n.type?i=n.features:"Feature"==n.type?i=[n]:-1!=["Point","LineString","Polygon","MultiPoint","MultiLineString","MultiPolygon"].indexOf(n.type)?i=[{geometry:n}]:console.log("nothing found in geoJSON"),i)for(var r=function(t,e){var n=i[t],r=n.geometry;if(r){var a=r.type,u=n.id;if("Point"==a||"MultiPoint"==a){if(!o.checkInclude(o.include,o.exclude,u))return"continue";var s=r.coordinates;"Point"==a&&(s=[s]);var c=Br.find(o.data,function(t,e){return t.id==u});c?c.multiPoint||(c.multiPoint=s):(c={multiPoint:s,id:u,madeFromGeoData:!0},o.data.push(c)),eo.softCopyProperties(n.properties,c)}}},o=this,a=0,u=i.length;a<u;a++)r(a)}t.prototype.validateData.call(this),no.each(this.dataItems.iterator(),function(t){var n=t.mapImage;n.isDisposed()||(e.mapImages.moveValue(n),g.isNumber(n.latitude)&&g.isNumber(n.latitude)&&(t.geoPoint={latitude:n.latitude,longitude:n.longitude}))})},Object.defineProperty(e.prototype,"mapImages",{get:function(){if(!this._mapImages){var t=new kr,e=new to.e(t);this._disposers.push(new to.c(e)),this._disposers.push(e.template),e.template.focusable=!0,e.events.on("inserted",this.handleObjectAdded,this,!1),this._mapImages=e,this._mapObjects=e}return this._mapImages},enumerable:!0,configurable:!0}),e.prototype.validateDataElement=function(e){t.prototype.validateDataElement.call(this,e),e.mapImage.invalidate()},e.prototype.validate=function(){t.prototype.validate.call(this),no.each(this.mapImages.iterator(),function(t){t.validatePosition()})},e.prototype.copyFrom=function(e){this.mapImages.template.copyFrom(e.mapImages.template),t.prototype.copyFrom.call(this,e)},e.prototype.getFeatures=function(){var t=this,e=[];return this.dataItems.each(function(t){var n=t.getFeature();n&&e.push(n)}),this.mapImages.each(function(n){if(-1==t.dataItems.indexOf(n._dataItem)){var i=n.getFeature();i&&e.push(i)}}),e},e.prototype.getImageById=function(t){return no.find(this.mapImages.iterator(),function(e){var n=e.dataItem.dataContext;if(e.id==t||n&&n.id==t)return!0})},e}(Nr);d.c.registeredClasses.MapImageSeries=Po,d.c.registeredClasses.MapImageSeriesDataItem=bo;var _o=n("Rnbi"),wo=n("jfaP"),So=n("tjMS"),xo=function(t){function e(){var e=t.call(this)||this;e._imageListeners={},e.className="MapLine",e.createLine(),e.line.stroke=Object(so.c)(),e.line.parent=e,e.strokeOpacity=1,e.setPropertyValue("precision",.1);var n=new p.a;return e.stroke=n.getFor("grid"),e.shortestDistance=!0,e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createLine=function(){this.line=new wo.a},e.prototype.positionToPoint=function(t){return this.shortestDistance?this.series.chart.projection.positionToPoint(this.multiGeoLine,t):this.line?this.line.positionToPoint(t):{x:0,y:0,angle:0}},Object.defineProperty(e.prototype,"multiGeoLine",{get:function(){var t=this.getPropertyValue("multiGeoLine");return!t&&this.dataItem&&this.dataItem.multiGeoLine&&(t=this.dataItem.multiGeoLine),t},set:function(t){if(t&&t.length>0){this.setPropertyValue("multiGeoLine",go(t),!0);var e=Ur(t);this.setPropertyValue("multiLine",e),this.updateExtremes()}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"multiLine",{get:function(){var t=this.getPropertyValue("multiLine");return!t&&this.dataItem&&this.dataItem.multiLine&&(t=this.dataItem.multiLine),t},set:function(t){this.setPropertyValue("multiLine",t),this.multiGeoLine=Wr(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"imagesToConnect",{get:function(){return this.getPropertyValue("imagesToConnect")},set:function(t){var e=this;if(this.setPropertyValue("imagesToConnect",t,!0),this.handleImagesToConnect(),this.series){var n=this.series.chart;n&&n.series.each(function(t){t instanceof Po&&(t.isReady()||e._disposers.push(t.events.on("ready",e.handleImagesToConnect,e,!1)))})}},enumerable:!0,configurable:!0}),e.prototype.handleImagesToConnect=function(){var t,e,n=this;if(this.imagesToConnect){var i=[],r=[i],o=function(t){if(g.isString(t)){var e=a.series.chart;e&&e.series.each(function(e){if(e instanceof Po){var n=e.getImageById(t);n&&(t=n)}})}if(t instanceof kr&&(i.push({longitude:t.longitude,latitude:t.latitude}),!a._imageListeners[t.uid])){var r=t.events.on("propertychanged",function(t){"longitude"!=t.property&&"latitude"!=t.property||(n.handleImagesToConnect(),n.invalidate())},a,!1);a._imageListeners[t.uid]=r,a._disposers.push(r)}},a=this;try{for(var u=Object(c.g)(this.imagesToConnect),s=u.next();!s.done;s=u.next()){o(s.value)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(e=u.return)&&e.call(u)}finally{if(t)throw t.error}}this.multiGeoLine=r}},e.prototype.validate=function(){var e=this.series.chart;if(this.multiLine){if(this.shortestDistance)e.projection.d3Projection.precision(this.precision),this.line.path=e.projection.d3Path(this.getFeature());else{for(var n=[],i=0,r=this.multiLine.length;i<r;i++){for(var o=this.multiLine[i],a=[],u=0,s=o.length;u<s;u++){var c=o[u],l=this.series.chart.projection.convert({longitude:c[0],latitude:c[1]});a.push(l)}n.push(a)}this.line.segments=n}this._arrow&&this._arrow.validatePosition(),no.each(this.lineObjects.iterator(),function(t){t.validatePosition()}),this.handleGlobalScale()}else this.imagesToConnect&&this.handleImagesToConnect();t.prototype.validate.call(this)},e.prototype.getFeature=function(){if(this.multiLine&&this.multiLine.length>0&&this.multiLine[0]&&this.multiLine[0].length>0)return{type:"Feature",geometry:{type:"MultiLineString",coordinates:this.multiLine}}},e.prototype.measureElement=function(){},Object.defineProperty(e.prototype,"shortestDistance",{get:function(){return this.getPropertyValue("shortestDistance")},set:function(t){this.setPropertyValue("shortestDistance",t,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"lineObjects",{get:function(){return this._lineObjects||(this._lineObjects=new to.e(new yo),this._lineObjects.events.on("inserted",this.handleLineObjectAdded,this,!1),this._disposers.push(new to.c(this._lineObjects)),this._disposers.push(this._lineObjects.template)),this._lineObjects},enumerable:!0,configurable:!0}),e.prototype.handleLineObjectAdded=function(t){var e=t.newValue;e.mapLine=this,e.shouldClone=!1,e.parent=this},Object.defineProperty(e.prototype,"arrow",{get:function(){if(!this._arrow){var t=this.createChild(yo);t.shouldClone=!1,t.width=8,t.height=10,t.mapLine=this,t.position=.5;var e=t.createChild(_o.a);e.fillOpacity=1,e.width=Object(So.c)(100),e.height=Object(So.c)(100),e.rotation=90,e.horizontalCenter="middle",e.verticalCenter="middle",this._arrow=t}return this._arrow},set:function(t){this._arrow=t,t.mapLine=this,t.parent=this},enumerable:!0,configurable:!0}),e.prototype.copyFrom=function(e){t.prototype.copyFrom.call(this,e),this.line.copyFrom(e.line),this.lineObjects.copyFrom(e.lineObjects),e._arrow&&(this.arrow=e.arrow.clone())},Object.defineProperty(e.prototype,"latitude",{get:function(){return this.north+(this.south-this.north)/2},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"longitude",{get:function(){return this.east+(this.west-this.east)/2},enumerable:!0,configurable:!0}),e.prototype.getTooltipX=function(){var t=this.getPropertyValue("tooltipX");return t instanceof So.a||(t=Object(So.c)(50)),t instanceof So.a?this.positionToPoint(t.value).x:0},e.prototype.getTooltipY=function(){var t=this.getPropertyValue("tooltipY");return t instanceof So.a||(t=Object(So.c)(50)),t instanceof So.a?this.positionToPoint(t.value).y:0},Object.defineProperty(e.prototype,"precision",{get:function(){return this.getPropertyValue("precision")},set:function(t){this.setPropertyValue("precision",t,!0)},enumerable:!0,configurable:!0}),e}(Vr);d.c.registeredClasses.MapLine=xo;var Mo=function(t){function e(){var e=t.call(this)||this;return e.className="MapLineSeriesDataItem",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.getFeature=function(){if(this.multiLine&&this.multiLine.length>0)return{type:"Feature",geometry:{type:"MultiLineString",coordinates:this.multiLine}}},Object.defineProperty(e.prototype,"mapLine",{get:function(){var t=this;if(!this._mapLine){var e=this.component.mapLines.create();this._mapLine=e,this.addSprite(e),this._disposers.push(e),this._disposers.push(new h.b(function(){t.component&&t.component.mapLines.removeValue(e)})),this.mapObject=e}return this._mapLine},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"line",{get:function(){return this._line},set:function(t){this._line=t,this.multiLine=[t]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"multiLine",{get:function(){return this._multiLine},set:function(t){this._multiLine=t,this._multiGeoLine=Wr(t),this.updateExtremes()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"geoLine",{get:function(){return this._geoLine},set:function(t){this._geoLine=t,this.multiLine=Ur([t])},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"multiGeoLine",{get:function(){return this._multiGeoLine},set:function(t){this._multiGeoLine=t,this.multiLine=Ur(t)},enumerable:!0,configurable:!0}),e}(Dr),jo=function(t){function e(){var e=t.call(this)||this;return e.className="MapLineSeries",e.dataFields.multiLine="multiLine",e.dataFields.line="line",e.dataFields.geoLine="geoLine",e.dataFields.multiGeoLine="multiGeoLine",e.ignoreBounds=!0,e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createDataItem=function(){return new Mo},e.prototype.validateData=function(){if(this.useGeodata||this.geodata){var e=this.chart.geodata;if(e){var n=void 0;if("FeatureCollection"==e.type?n=e.features:"Feature"==e.type?n=[e]:-1!=["Point","LineString","Polygon","MultiPoint","MultiLineString","MultiPolygon"].indexOf(e.type)?n=[{geometry:e}]:console.log("nothing found in geoJSON"),n)for(var i=function(t,e){var i=n[t],o=i.geometry;if(o){var a=o.type,u=i.id;if("LineString"==a||"MultiLineString"==a){if(!r.checkInclude(r.include,r.exclude,u))return"continue";var s=o.coordinates,c=Br.find(r.data,function(t,e){return t.id==u});"LineString"==a&&(s=[s]),c?c.multiLine||(c.multiLine=s):(c={multiLine:s,id:u,madeFromGeoData:!0},r.data.push(c)),eo.softCopyProperties(i.properties,c)}}},r=this,o=0,a=n.length;o<a;o++)i(o)}}t.prototype.validateData.call(this)},Object.defineProperty(e.prototype,"mapLines",{get:function(){if(!this._mapLines){var t=this.createLine(),e=new to.e(t);this._disposers.push(new to.c(e)),this._disposers.push(e.template),e.events.on("inserted",this.handleObjectAdded,this,!1),this._mapLines=e,this._mapObjects=e}return this._mapLines},enumerable:!0,configurable:!0}),e.prototype.createLine=function(){return new xo},e.prototype.validate=function(){this.dataItems.each(function(t){eo.used(t.mapLine)}),t.prototype.validate.call(this),this.mapLines.each(function(t){t.validate()})},e.prototype.copyFrom=function(e){this.mapLines.template.copyFrom(e.mapLines.template),t.prototype.copyFrom.call(this,e)},e.prototype.getFeatures=function(){var t=this,e=[];return this.dataItems.each(function(t){var n=t.getFeature();n&&e.push(n)}),this.mapLines.each(function(n){if(-1==t.dataItems.indexOf(n._dataItem)){var i=n.getFeature();i&&e.push(i)}}),e},e.prototype.getLineById=function(t){return no.find(this.mapLines.iterator(),function(e){return e.dataItem.dataContext.id==t})},e}(Nr);d.c.registeredClasses.MapLineSeries=jo,d.c.registeredClasses.MapLineSeriesDataItem=Mo;var Oo=function(t){function e(){var e=t.call(this)||this;return e.className="Graticule",e.applyTheme(),e.shortestDistance=!0,e}return Object(c.c)(e,t),e}(xo);d.c.registeredClasses.Graticule=Oo;var Lo=function(t){function e(){var e=t.call(this)||this;return e.className="GraticuleSeriesDataItem",e.applyTheme(),e}return Object(c.c)(e,t),e}(Mo),Eo=function(t){function e(){var e=t.call(this)||this;return e.className="GraticuleSeries",e.longitudeStep=10,e.latitudeStep=10,e.north=90,e.south=-90,e.east=-180,e.west=180,e.fitExtent=!0,e.singleSprite=!0,e.events.disableType("geoBoundsChanged"),e.mapLines.template.line.strokeOpacity=.08,e.ignoreBounds=!1,e.hiddenInLegend=!0,e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createDataItem=function(){return new Lo},e.prototype.validateData=function(){var e=this;t.prototype.validateData.call(this),this.mapLines.clear();var n=En();if(n){n.stepMinor([this.longitudeStep,this.latitudeStep]),n.stepMajor([360,360]);var i=this.chart;if(this.fitExtent?n.extent([[i.east,i.north],[i.west,i.south]]):n.extent([[this.east,this.north],[this.west,this.south]]),this.singleSprite){this.mapLines.create().multiLine=n().coordinates}else{var r=n.lines();Br.each(r,function(t){e.mapLines.create().multiLine=[t.coordinates]})}}},e.prototype.createLine=function(){return new Oo},Object.defineProperty(e.prototype,"latitudeStep",{get:function(){return this.getPropertyValue("latitudeStep")},set:function(t){this.setPropertyValue("latitudeStep",t)&&this.invalidateData()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"longitudeStep",{get:function(){return this.getPropertyValue("longitudeStep")},set:function(t){this.setPropertyValue("longitudeStep",t)&&this.invalidateData()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"fitExtent",{get:function(){return this.getPropertyValue("fitExtent")},set:function(t){this.setPropertyValue("fitExtent",t)&&this.invalidateData()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"singleSprite",{get:function(){return this.getPropertyValue("singleSprite")},set:function(t){this.setPropertyValue("singleSprite",t)&&this.invalidateData()},enumerable:!0,configurable:!0}),e}(jo);d.c.registeredClasses.GraticuleSeries=Eo,d.c.registeredClasses.GraticuleSeriesDataItem=Lo;var Co=n("zhwk"),Io=function(t){function e(){var e=t.call(this)||this;return e.className="MapChartDataItem",e.applyTheme(),e}return Object(c.c)(e,t),e}(l.b),To=function(t){function e(){var e=t.call(this)||this;e.scaleRatio=1,e.zoomDuration=1e3,e.zoomEasing=po.cubicOut,e.minZoomLevel=1,e.maxZoomLevel=32,e._prevZoomGeoPoint={latitude:0,longitude:0},e.className="MapChart",e.projection=new oo,e.setPropertyValue("deltaLatitude",0),e.setPropertyValue("deltaLongitude",0),e.setPropertyValue("deltaGamma",0),e.maxPanOut=.7,e.homeZoomLevel=1,e.zoomStep=2,e.layout="absolute",e.centerMapOnZoomOut=!0,e.padding(0,0,0,0),eo.used(e.backgroundSeries),e.minWidth=10,e.minHeight=10,e.events.once("inited",e.handleAllInited,e,!1);var n=e.seriesContainer;n.visible=!1,n.inert=!0,n.resizable=!0,n.events.on("transformed",e.handleMapTransform,e,!1),n.events.on("doublehit",e.handleDoubleHit,e,!1),n.events.on("dragged",e.handleDrag,e,!1),n.zIndex=0,n.dragWhileResize=!0,n.adapter.add("scale",function(t,n){return v.fitToRange(t,e.minZoomLevel,e.maxZoomLevel)}),e.events.on("maxsizechanged",function(t){0!=t.previousWidth&&0!=t.previousHeight||(e.updateExtremes(),e.updateCenterGeoPoint())},void 0,!1);var i=e.chartContainer;i.parent=e,i.zIndex=-1,e._disposers.push(e.events.on("maxsizechanged",function(){if(e.inited){e._mapAnimation&&e._mapAnimation.stop();var t=!0;e.series.each(function(e){e.updateTooltipBounds(),e.inited&&!e.dataInvalid||(t=!1)}),t&&e.updateScaleRatio(),e.zoomToGeoPoint(e._zoomGeoPointReal,e.zoomLevel,!0,0)}},void 0,!1));var r=i.background;r.fillOpacity=0,r.events.on("down",function(t){e.seriesContainer.dragStart(t.target.interactions.downPointers.getIndex(0))},e),r.events.on("up",function(t){e.seriesContainer.dragStop()},e),r.events.on("doublehit",e.handleDoubleHit,e),r.focusable=!0,i.events.on("down",e.handleMapDown,e,!1),e.addDisposer(n.events.on("down",function(){var t=e.seriesContainer.interactions.inertias.getKey("move");t&&t.done()})),e.background.fillOpacity=0,e._disposers.push(Object(Co.b)().body.events.on("keyup",function(t){if(e.topParent.hasFocused){var n=lo.b.getEventKey(t.event);if(!e._zoomControl||!e._zoomControl.thumb.isFocused)switch(n){case"up":e.pan({x:0,y:.1});break;case"down":e.pan({x:0,y:-.1});break;case"left":e.pan({x:.1,y:0});break;case"right":e.pan({x:-.1,y:0})}}},e)),e.mouseWheelBehavior="zoom";var o=Object(Co.b)();e._disposers.push(o.body.events.on("down",e.handlePanDown,e)),e._disposers.push(o.body.events.on("up",e.handlePanUp,e));var a=e.seriesContainer.createChild(ao.a);return a.radius=10,a.inert=!0,a.isMeasured=!1,a.events.on("transformed",e.handlePanMove,e,!1),a.interactionsEnabled=!1,a.opacity=0,a.x=0,a.y=0,e.panSprite=a,e.panBehavior="move",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.handlePanDown=function(t){var e=eo.documentPointToSvg(t.pointer.point,this.htmlContainer);e.x>0&&e.y>0&&e.x<this.svgContainer.width&&e.y<this.svgContainer.height&&(this._downPointOrig=eo.documentPointToSprite(t.pointer.point,this.seriesContainer),this.panSprite.moveTo(this._downPointOrig),this.panSprite.dragStart(t.pointer),this._downDeltaLongitude=this.deltaLongitude,this._downDeltaLatitude=this.deltaLatitude)},e.prototype.handlePanUp=function(t){this._downPointOrig&&this.panSprite.dragStop(t.pointer,!0),this._downPointOrig=void 0},e.prototype.handlePanMove=function(){if(!this.seriesContainer.isResized){if(Object(Co.b)().areTransformed([this.panSprite.interactions,this.seriesContainer.interactions]))return;var t=this.projection.d3Projection,e=this.panBehavior;if("move"!=e&&"none"!=e&&this._downPointOrig&&t.rotate){var n=t.rotate(),i=n[0],r=n[1],o=n[2];t.rotate([0,0,0]);var a=this.projection.invert(this._downPointOrig),u={x:this.panSprite.pixelX,y:this.panSprite.pixelY},s=void 0;u&&(s=this.projection.invert(u)),t.rotate([i,r,o]),s&&("rotateLat"!=e&&"rotateLongLat"!=e||(this.deltaLatitude=this._downDeltaLatitude+s.latitude-a.latitude),"rotateLong"!=e&&"rotateLongLat"!=e||(this.deltaLongitude=this._downDeltaLongitude+s.longitude-a.longitude))}}},e.prototype.handleAllInited=function(){var t=this,e=!0;if(this.seriesContainer.visible=!0,this.series.each(function(t){t.inited&&!t.dataInvalid||(e=!1)}),e)this.updateCenterGeoPoint(),this.updateScaleRatio(),this.goHome(0);else{var n=d.c.events.once("exitframe",function(){t.removeDispose(n),t.handleAllInited()},this,!1);this.addDisposer(n)}},e.prototype.updateZoomGeoPoint=function(){var t=eo.svgPointToSprite({x:this.innerWidth/2+this.pixelPaddingLeft,y:this.innerHeight/2+this.pixelPaddingTop},this.series.getIndex(0)),e=this.projection.invert(t);this._zoomGeoPointReal=e},e.prototype.updateCenterGeoPoint=function(){var t,e,n,i;if(this.backgroundSeries){var r=this.backgroundSeries.getFeatures();if(r.length>0){var o=this.projection.d3Path.bounds(r[0].geometry);t=o[0][0],n=o[0][1],e=o[1][0],i=o[1][1]}}else this.series.each(function(r){var o=r.group.node.getBBox();(t>o.x||!g.isNumber(t))&&(t=o.x),(e<o.x+o.width||!g.isNumber(e))&&(e=o.x+o.width),(n>o.y||!g.isNumber(n))&&(n=o.y),(i<o.y+o.height||!g.isNumber(i))&&(i=o.y+o.height)});this.seriesMaxLeft=t,this.seriesMaxRight=e,this.seriesMaxTop=n,this.seriesMaxBottom=i,this.seriesWidth=e-t,this.seriesHeight=i-n,this.seriesWidth>0&&this.seriesHeight>0?(this.chartContainer.visible=!0,this._centerGeoPoint=this.projection.invert({x:t+(e-t)/2,y:n+(i-n)/2}),this._zoomGeoPointReal&&g.isNumber(this._zoomGeoPointReal.latitude)||(this._zoomGeoPointReal=this._centerGeoPoint)):this.chartContainer.visible=!1},e.prototype.handleDrag=function(){var t=this.zoomLevel*this.scaleRatio,e=this.seriesWidth*t,n=this.seriesHeight*t,i=this.seriesContainer,r=this.seriesMaxLeft*t,o=this.seriesMaxRight*t,a=this.seriesMaxTop*t,u=this.seriesMaxBottom*t,s=i.pixelX,c=i.pixelY,l=this.maxPanOut,h=Math.min(this.maxWidth*(1-l)-e-r,-r);s<h&&(s=h);var p=Math.max(this.maxWidth*l-r,this.maxWidth-o);s>p&&(s=p);var f=Math.min(this.maxHeight*(1-l)-n-a,-a);c<f&&(c=f);var d=Math.max(this.maxHeight*l-a,this.maxHeight-u);c>d&&(c=d),i.moveTo({x:s,y:c},void 0,void 0,!0),this._zoomGeoPointReal=this.zoomGeoPoint},e.prototype.applyInternalDefaults=function(){t.prototype.applyInternalDefaults.call(this),g.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Map")),g.hasValue(this.background.readerTitle)||(this.background.role="application",this.background.readerTitle=this.language.translate("Use plus and minus keys on your keyboard to zoom in and out"))},e.prototype.handleMapDown=function(){this._mapAnimation&&this._mapAnimation.stop()},e.prototype.handleDoubleHit=function(t){var e=eo.documentPointToSvg(t.point,this.htmlContainer,this.svgContainer.cssScale),n=this.svgPointToGeo(e);this.zoomIn(n)},e.prototype.handleWheel=function(t){var e=this.seriesContainer.interactions.inertias.getKey("move");e&&e.done();var n=eo.documentPointToSvg(t.point,this.htmlContainer,this.svgContainer.cssScale),i=this.svgPointToGeo(n);t.shift.y<0?this.zoomIn(i,void 0,this.interactions.mouseOptions.sensitivity):this.zoomOut(i,void 0,this.interactions.mouseOptions.sensitivity)},Object.defineProperty(e.prototype,"mouseWheelBehavior",{get:function(){return this.getPropertyValue("mouseWheelBehavior")},set:function(t){this.setPropertyValue("mouseWheelBehavior",t)&&("none"!=t?(this._mouseWheelDisposer=this.chartContainer.events.on("wheel",this.handleWheel,this,!1),this._disposers.push(this._mouseWheelDisposer)):(this._mouseWheelDisposer&&this._mouseWheelDisposer.dispose(),this.chartContainer.wheelable=!1))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"panBehavior",{get:function(){return this.getPropertyValue("panBehavior")},set:function(t){if(this.setPropertyValue("panBehavior",t)){var e=this.seriesContainer;switch(this.panSprite.draggable=!1,e.draggable=!1,t){case"move":e.draggable=!0;break;default:this.panSprite.draggable=!0}}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"centerMapOnZoomOut",{get:function(){return this.getPropertyValue("centerMapOnZoomOut")},set:function(t){this.setPropertyValue("centerMapOnZoomOut",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"projection",{get:function(){return this.getPropertyValue("projection")},set:function(t){var e=this;this.setPropertyValue("projection",t)&&(this.invalidateProjection(),t.chart=this,this._backgroundSeries&&this._backgroundSeries.invalidate(),this.inited&&this.updateExtremes(),this.series.each(function(t){t.events.once("validated",function(){e.updateCenterGeoPoint(),e.updateScaleRatio(),e.goHome(0)})}))},enumerable:!0,configurable:!0}),e.prototype.validateDataItems=function(){t.prototype.validateDataItems.call(this),this.updateExtremes()},e.prototype.updateExtremes=function(){var t,e,n,i;this.series.each(function(r){r.ignoreBounds||r instanceof Eo&&r.fitExtent||((r.north>e||!g.isNumber(e))&&(e=r.north),(r.south<i||!g.isNumber(i))&&(i=r.south),(r.west<n||!g.isNumber(n))&&(n=r.west),(r.east>t||!g.isNumber(t))&&(t=r.east))});var r=[],o=!1;this.series.each(function(t){t instanceof Eo&&!t.fitExtent&&(r=t.getFeatures(),o=!0)}),o||this.series.each(function(t){t.ignoreBounds||t instanceof Eo&&t.fitExtent||(r=r.concat(t.getFeatures()))});var a=v.max(50,this.innerWidth),u=v.max(50,this.innerHeight),s=this.projection.d3Projection;if(r.length>0&&s&&(this.east!=t||this.west!=n||this.north!=e||this.south!=i)){if(this.east=t,this.west=n,this.north=e,this.south=i,s.rotate){var c=s.rotate(),l=c[0],h=c[1],p=c[2];this.deltaLongitude=l,this.deltaLatitude=h,this.deltaGamma=p}var f={type:"FeatureCollection",features:r},d=s.scale();if(s.fitSize([a,u],f),s.scale()!=d&&this.invalidateDataUsers(),this.series.each(function(t){t instanceof Eo&&t.invalidateData()}),this._backgroundSeries){var m=this._backgroundSeries.mapPolygons.getIndex(0);m&&(m.multiPolygon=Qr(this.north,this.east,this.south,this.west))}this._fitWidth=a,this._fitHeight=u}this._zoomGeoPointReal&&g.isNumber(this._zoomGeoPointReal.latitude)||this.goHome(0)},e.prototype.updateScaleRatio=function(){var t;this.updateCenterGeoPoint();var e=this.innerWidth/this.seriesWidth,n=this.innerHeight/this.seriesHeight;t=v.min(e,n),(g.isNaN(t)||t==1/0)&&(t=1),t!=this.scaleRatio&&(this.scaleRatio=t,no.each(this.series.iterator(),function(e){e.scale=t,e.updateTooltipBounds()}),this.backgroundSeries.scale=t,this.dispatch("scaleratiochanged"))},e.prototype.svgPointToGeo=function(t){var e=this.series.getIndex(0);if(e){var n=eo.svgPointToSprite(t,e);return this.seriesPointToGeo(n)}},e.prototype.geoPointToSVG=function(t){var e=this.series.getIndex(0);if(e){var n=this.geoPointToSeries(t);return eo.spritePointToSvg(n,e)}},e.prototype.seriesPointToGeo=function(t){return this.projection.invert(t)},e.prototype.geoPointToSeries=function(t){return this.projection.convert(t)},Object.defineProperty(e.prototype,"geodata",{get:function(){return this._geodata},set:function(t){t!=this._geodata&&(this._geodata=t,this.reverseGeodata&&this.processReverseGeodata(this._geodata),this.invalidateData(),this.dataUsers.each(function(t){for(var e=t.data.length-1;e>=0;e--)1==t.data[e].madeFromGeoData&&t.data.splice(e,1);t.disposeData(),t.invalidateData()}))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"reverseGeodata",{get:function(){return this.getPropertyValue("reverseGeodata")},set:function(t){this.setPropertyValue("reverseGeodata",t)&&this._geodata&&this.processReverseGeodata(this._geodata)},enumerable:!0,configurable:!0}),e.prototype.processReverseGeodata=function(t){for(var e=0;e<t.features.length;e++)for(var n=t.features[e],i=0;i<n.geometry.coordinates.length;i++)if("MultiPolygon"==n.geometry.type)for(var r=0;r<n.geometry.coordinates[i].length;r++)n.geometry.coordinates[i][r].reverse();else n.geometry.coordinates[i].reverse()},e.prototype.zoomToGeoPoint=function(t,e,n,i,r){var o=this;if(!t){var a=!1;if(this.series.each(function(t){t.dataItems.length>0&&(a=!0)}),!a)return;t=this.zoomGeoPoint}if(t&&g.isNumber(t.longitude)&&g.isNumber(t.latitude)){this._zoomGeoPointReal=t,e=v.fitToRange(e,this.minZoomLevel,this.maxZoomLevel);var u=this.projection.convert(t);if(u){var s=this.geoPointToSVG(t),c=eo.svgPointToSprite(s,this);n&&(c={x:this.innerWidth/2,y:this.innerHeight/2}),g.isNumber(i)||(i=this.zoomDuration);var l=c.x-u.x*e*this.scaleRatio,h=c.y-u.y*e*this.scaleRatio;return!r&&e<this.zoomLevel&&this.centerMapOnZoomOut&&e<1.5&&(l=this.innerWidth/2-(this.seriesMaxLeft+(this.seriesMaxRight-this.seriesMaxLeft)/2)*e*this.scaleRatio,h=this.innerHeight/2-(this.seriesMaxTop+(this.seriesMaxBottom-this.seriesMaxTop)/2)*e*this.scaleRatio),this._mapAnimation=this.seriesContainer.animate([{property:"scale",to:e},{property:"x",from:this.seriesContainer.pixelX,to:l},{property:"y",from:this.seriesContainer.pixelY,to:h}],i,this.zoomEasing),this._disposers.push(this._mapAnimation.events.on("animationended",function(){o._zoomGeoPointReal=o.zoomGeoPoint})),this.seriesContainer.validatePosition(),this._mapAnimation}}},e.prototype.zoomToMapObject=function(t,e,n,i){void 0==n&&(n=!0);var r=this.seriesContainer.interactions.inertias.getKey("move");if(r&&r.done(),t instanceof kr)return g.isNaN(e)&&(e=5),this.zoomToGeoPoint({latitude:t.latitude,longitude:t.longitude},e,n,i,!0);var o=t.dataItem;if(o&&g.isNumber(o.zoomLevel)&&(e=o.zoomLevel),t instanceof $r){var a=t.dataItem,u=t.polygon.bbox;0!=u.width&&0!=u.height||(u=t.polygon.group.getBBox()),g.isNumber(e)||(e=Math.min(this.seriesWidth/u.width,this.seriesHeight/u.height));var s=void 0;if(a&&g.hasValue(a.zoomGeoPoint))s=a.zoomGeoPoint;else{var c={x:u.x+u.width/2,y:u.y+u.height/2},l=eo.spritePointToSprite(c,t.polygon,t.series);s=this.seriesPointToGeo(l)}return this.zoomToGeoPoint(s,e,!0,i,!0)}},e.prototype.zoomToRectangle=function(t,e,n,i,r,o,a){g.isNaN(r)&&(r=1);var u=v.min(i,e),s=v.max(i,e);i=u,e=s;var c=v.normalizeAngle(180-this.deltaLongitude);c>180&&(c-=360);var l=i+(e-i)/2,h=i-e;i<c&&e>c&&(l+=180,h=v.normalizeAngle(e-i-360));var p=r*Math.min((this.south-this.north)/(n-t),Math.abs((this.west-this.east)/h));return this.zoomToGeoPoint({latitude:t+(n-t)/2,longitude:l},p,o,a,!0)},e.prototype.zoomIn=function(t,e,n){void 0===n&&(n=1);var i=1+(this.zoomStep-1)*n;return i<1&&(i=1),this.zoomToGeoPoint(t,this.zoomLevel*i,!1,e)},e.prototype.zoomOut=function(t,e,n){void 0===n&&(n=1);var i=1+(this.zoomStep-1)*n;return i<1&&(i=1),this.zoomToGeoPoint(t,this.zoomLevel/i,!1,e)},e.prototype.pan=function(t,e){var n=this.geoPointToSVG(this.zoomGeoPoint);n.x+=this.pixelWidth*t.x,n.y+=this.pixelHeight*t.y,this.zoomToGeoPoint(this.svgPointToGeo(n),this.zoomLevel,!0,e,!0)},Object.defineProperty(e.prototype,"zoomGeoPoint",{get:function(){var t=eo.spritePointToSvg({x:this.pixelWidth/2,y:this.pixelHeight/2},this);return this.svgPointToGeo(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"zoomLevel",{get:function(){return this.seriesContainer.scale},set:function(t){this.seriesContainer.scale=t},enumerable:!0,configurable:!0}),e.prototype.handleMapTransform=function(){this.zoomLevel!=this._prevZoomLevel&&(this.dispatch("zoomlevelchanged"),this._prevZoomLevel=this.zoomLevel,this.svgContainer.readerAlert(this.language.translate("Zoom level changed to %1",this.language.locale,g.castString(this.zoomLevel)))),!this.zoomGeoPoint||this._prevZoomGeoPoint.latitude==this.zoomGeoPoint.latitude&&this._prevZoomGeoPoint.longitude==this.zoomGeoPoint.longitude||this.dispatch("mappositionchanged")},Object.defineProperty(e.prototype,"smallMap",{get:function(){if(!this._smallMap){var t=new co;this.smallMap=t}return this._smallMap},set:function(t){this._smallMap&&this.removeDispose(this._smallMap),this._smallMap=t,this._smallMap.chart=this,t.parent=this.chartContainer},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"zoomControl",{get:function(){return this._zoomControl},set:function(t){this._zoomControl&&this.removeDispose(this._zoomControl),this._zoomControl=t,t.chart=this,t.parent=this.chartContainer,t.plusButton.exportable=!1,t.minusButton.exportable=!1},enumerable:!0,configurable:!0}),e.prototype.createSeries=function(){return new Nr},Object.defineProperty(e.prototype,"deltaLongitude",{get:function(){return this.getPropertyValue("deltaLongitude")},set:function(t){t=v.round(t,3),this.setPropertyValue("deltaLongitude",vo(t))&&(this.rotateMap(),this.updateZoomGeoPoint())},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"deltaLatitude",{get:function(){return this.getPropertyValue("deltaLatitude")},set:function(t){t=v.round(t,3),this.setPropertyValue("deltaLatitude",t)&&(this.rotateMap(),this.updateZoomGeoPoint())},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"deltaGamma",{get:function(){return this.getPropertyValue("deltaGamma")},set:function(t){t=v.round(t,3),this.setPropertyValue("deltaGamma",t)&&(this.rotateMap(),this.updateZoomGeoPoint())},enumerable:!0,configurable:!0}),e.prototype.rotateMap=function(){this.projection.d3Projection&&this.projection.d3Projection.rotate&&(this.projection.d3Projection.rotate([this.deltaLongitude,this.deltaLatitude,this.deltaGamma]),this.invalidateProjection())},Object.defineProperty(e.prototype,"maxPanOut",{get:function(){return this.getPropertyValue("maxPanOut")},set:function(t){this.setPropertyValue("maxPanOut",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"homeGeoPoint",{get:function(){return this.getPropertyValue("homeGeoPoint")},set:function(t){this.setPropertyValue("homeGeoPoint",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"homeZoomLevel",{get:function(){return this.getPropertyValue("homeZoomLevel")},set:function(t){this.setPropertyValue("homeZoomLevel",t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"zoomStep",{get:function(){return this.getPropertyValue("zoomStep")},set:function(t){this.setPropertyValue("zoomStep",t)},enumerable:!0,configurable:!0}),e.prototype.invalidateProjection=function(){this.east=void 0,this.invalidateDataUsers(),this.updateCenterGeoPoint()},Object.defineProperty(e.prototype,"geodataSource",{get:function(){var t=this;this._dataSources.geodata||this.getDataSource("geodata").events.on("parseended",function(){t.events.once("datavalidated",function(){t.goHome(0)})});return this._dataSources.geodata},set:function(t){var e=this;this._dataSources.geodata&&this.removeDispose(this._dataSources.geodata),this._dataSources.geodata=t,this._dataSources.geodata.component=this,this.events.on("inited",function(){e.loadData("geodata")},this,!1),this.setDataSourceEvents(t,"geodata")},enumerable:!0,configurable:!0}),e.prototype.processConfig=function(e){if(g.hasValue(e.geodata)&&g.isString(e.geodata)){var n=e.geodata;if(g.hasValue(window["am4geodata_"+e.geodata]))e.geodata=window["am4geodata_"+e.geodata];else try{e.geodata=JSON.parse(e.geodata)}catch(t){this.raiseCriticalError(Error("MapChart error: Geodata `"+n+"` is not loaded or is incorrect."),!0)}}g.hasValue(e.projection)&&g.isString(e.projection)&&(e.projection=this.createClassInstance(e.projection)),g.hasValue(e.smallMap)&&!g.hasValue(e.smallMap.type)&&(e.smallMap.type="SmallMap"),g.hasValue(e.zoomControl)&&!g.hasValue(e.zoomControl.type)&&(e.zoomControl.type="ZoomControl"),t.prototype.processConfig.call(this,e)},e.prototype.handleSeriesAdded=function(e){t.prototype.handleSeriesAdded.call(this,e);var n=e.newValue;n.scale=this.scaleRatio,n.events.on("validated",this.updateCenterGeoPoint,this,!1)},e.prototype.configOrder=function(e,n){return e==n?0:"smallMap"==e?1:"smallMap"==n?-1:"series"==e?1:"series"==n?-1:t.prototype.configOrder.call(this,e,n)},e.prototype.asIs=function(e){return"projection"==e||"geodata"==e||t.prototype.asIs.call(this,e)},Object.defineProperty(e.prototype,"centerGeoPoint",{get:function(){return this._centerGeoPoint},enumerable:!0,configurable:!0}),e.prototype.goHome=function(t){var e=this.homeGeoPoint;e||(e=this.centerGeoPoint),e&&this.zoomToGeoPoint(e,this.homeZoomLevel,!0,t,!0)},e.prototype.setPaper=function(e){return this.svgContainer&&(this.svgContainer.hideOverflow=!0),t.prototype.setPaper.call(this,e)},Object.defineProperty(e.prototype,"backgroundSeries",{get:function(){var t=this;if(!this._backgroundSeries){var e=new ro;e.parent=this.seriesContainer,e.chart=this,e.hiddenInLegend=!0,e.mapPolygons.template.focusable=!1,e.addDisposer(new h.b(function(){t._backgroundSeries=void 0})),this._disposers.push(e);var n=(new p.a).getFor("background"),i=e.mapPolygons.template.polygon;i.stroke=n,i.fill=n,i.fillOpacity=0,i.strokeOpacity=0,e.mapPolygons.create(),this._backgroundSeries=e}return this._backgroundSeries},enumerable:!0,configurable:!0}),e.prototype.setLegend=function(e){t.prototype.setLegend.call(this,e),e&&(e.parent=this)},e.prototype.setTapToActivate=function(e){t.prototype.setTapToActivate.call(this,e),this.seriesContainer.interactions.isTouchProtected=!0,this.panSprite.interactions.isTouchProtected=!0},e.prototype.handleTapToActivate=function(){t.prototype.handleTapToActivate.call(this),this.seriesContainer.interactions.isTouchProtected=!1,this.panSprite.interactions.isTouchProtected=!1},e.prototype.handleTapToActivateDeactivation=function(){t.prototype.handleTapToActivateDeactivation.call(this),this.seriesContainer.interactions.isTouchProtected=!0,this.panSprite.interactions.isTouchProtected=!0},e.prototype.asFunction=function(e){return"zoomEasing"==e||t.prototype.asIs.call(this,e)},e.prototype.hasLicense=function(){if(ho.a.commercialLicense)return!0;if(!t.prototype.hasLicense.call(this))return!1;for(var e=0;e<ho.a.licenses.length;e++)if(ho.a.licenses[e].match(/^MP.{5,}/i))return!0;return!1},e}(l.a);d.c.registeredClasses.MapChart=To;var Go=n("xgTw"),Do=function(t){function e(){var e=t.call(this)||this;return e.className="MapSpline",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createLine=function(){this.line=new Go.a,this.line.tensionX=.8,this.line.tensionY=.8},Object.defineProperty(e.prototype,"shortestDistance",{get:function(){return!1},set:function(t){},enumerable:!0,configurable:!0}),e}(xo);d.c.registeredClasses.MapSpline=Do;var No=n("MXvJ"),zo=function(t){function e(){var e=t.call(this)||this;return e.className="MapArc",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createLine=function(){this.line=new No.a},Object.defineProperty(e.prototype,"shortestDistance",{get:function(){return!1},set:function(t){},enumerable:!0,configurable:!0}),e}(xo);d.c.registeredClasses.MapArc=zo;var Vo=function(t){function e(){var e=t.call(this)||this;return e.className="MapSplineSeriesDataItem",e.applyTheme(),e}return Object(c.c)(e,t),e}(Mo),ko=function(t){function e(){var e=t.call(this)||this;return e.className="MapSplineSeries",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createDataItem=function(){return new Vo},e.prototype.createLine=function(){return new Do},e}(jo);d.c.registeredClasses.MapSplineSeries=ko,d.c.registeredClasses.MapSplineSeriesDataItem=Vo;var Ao=function(t){function e(){var e=t.call(this)||this;return e.className="MapArcSeriesDataItem",e.applyTheme(),e}return Object(c.c)(e,t),e}(Mo),Ro=function(t){function e(){var e=t.call(this)||this;return e.className="MapArcSeries",e.applyTheme(),e}return Object(c.c)(e,t),e.prototype.createDataItem=function(){return new Ao},e.prototype.createLine=function(){return new zo},e}(jo);d.c.registeredClasses.MapArcSeries=Ro,d.c.registeredClasses.MapArcSeriesDataItem=Ao;var Fo=n("aGXA"),Bo=n("CnhP"),Ho=function(t){function e(){var e=t.call(this)||this;e._chart=new h.d,e.className="ZoomControl",e.align="right",e.valign="bottom",e.layout="vertical",e.padding(5,5,5,5);var n=new p.a,i=e.createChild(Fo.a);i.shouldClone=!1,i.label.text="+",e.plusButton=i;var r=e.createChild(zr.a);r.shouldClone=!1,r.background.fill=n.getFor("alternativeBackground"),r.background.fillOpacity=.05,r.background.events.on("hit",e.handleBackgroundClick,e,!1),r.events.on("sizechanged",e.updateThumbSize,e,!1),e.slider=r;var o=r.createChild(Fo.a);o.shouldClone=!1,o.padding(0,0,0,0),o.draggable=!0,o.events.on("drag",e.handleThumbDrag,e,!1),e.thumb=o;var a=e.createChild(Fo.a);return a.shouldClone=!1,a.label.text="-",e.minusButton=a,e.thumb.role="slider",e.thumb.readerLive="polite",e.thumb.readerTitle=e.language.translate("Use arrow keys to zoom in and out"),e.minusButton.readerTitle=e.language.translate("Press ENTER to zoom in"),e.plusButton.readerTitle=e.language.translate("Press ENTER to zoom out"),e.applyTheme(),e.events.on("propertychanged",function(t){"layout"==t.property&&e.fixLayout()},void 0,!1),e._disposers.push(e._chart),e.fixLayout(),e}return Object(c.c)(e,t),e.prototype.fixLayout=function(){var t=this.plusButton,e=this.minusButton,n=this.thumb,i=this.slider;t.x=void 0,t.y=void 0,e.x=void 0,e.y=void 0,n.x=void 0,n.y=void 0,i.x=void 0,i.y=void 0,t.padding(6,10,6,10),e.padding(6,10,6,10),e.label.align="center",e.label.valign="middle",t.label.align="center",t.label.valign="middle","vertical"==this.layout?(this.width=40,this.height=void 0,e.width=Object(So.c)(100),e.height=void 0,n.width=Object(So.c)(100),n.height=void 0,t.width=Object(So.c)(100),t.height=void 0,i.width=Object(So.c)(100),e.marginTop=1,t.marginBottom=2,i.height=0,e.toFront(),t.toBack(),n.minX=0,n.maxX=0,n.minY=0):"horizontal"==this.layout&&(this.height=40,this.width=void 0,e.height=Object(So.c)(100),e.width=void 0,t.height=Object(So.c)(100),t.width=void 0,n.height=Object(So.c)(100),n.width=void 0,n.minX=0,n.minY=0,n.maxY=0,i.height=Object(So.c)(100),i.width=0,e.toBack(),t.toFront())},e.prototype.handleBackgroundClick=function(t){var e=t.target,n=t.spritePoint.y,i=this.chart,r=Math.log(i.maxZoomLevel)/Math.LN2,o=Math.log(i.minZoomLevel)/Math.LN2,a=(e.pixelHeight-n)/e.pixelHeight*(o+(r-o)),u=Math.pow(2,a);i.zoomToGeoPoint(i.zoomGeoPoint,u)},Object.defineProperty(e.prototype,"chart",{get:function(){return this._chart.get()},set:function(t){var e=this;this._chart.set(t,new h.c([t.events.on("maxsizechanged",this.updateThumbSize,this,!1),t.events.on("zoomlevelchanged",this.updateThumb,this,!1),this.minusButton.events.on("hit",function(){t.zoomOut(t.zoomGeoPoint)},t,!1),Object(Co.b)().body.events.on("keyup",function(n){e.topParent.hasFocused&&(lo.b.isKey(n.event,"plus")?t.zoomIn():lo.b.isKey(n.event,"minus")&&t.zoomOut())},t),this.plusButton.events.on("hit",function(){t.zoomIn(t.zoomGeoPoint)},t,!1)]))},enumerable:!0,configurable:!0}),e.prototype.updateThumbSize=function(){if(this.chart){var t=this.slider,e=this.thumb;"vertical"==this.layout?(e.minHeight=Math.min(this.slider.pixelHeight,20),e.height=t.pixelHeight/this.stepCount,e.maxY=t.pixelHeight-e.pixelHeight,e.pixelHeight<=1?e.visible=!1:e.visible=!0):(e.minWidth=Math.min(this.slider.pixelWidth,20),e.width=t.pixelWidth/this.stepCount,e.maxX=t.pixelWidth-e.pixelWidth,e.pixelWidth<=1?e.visible=!1:e.visible=!0)}},e.prototype.updateThumb=function(){var t=this.slider,e=this.chart,n=this.thumb;if(!n.isDown){var i=(Math.log(e.zoomLevel)-Math.log(this.chart.minZoomLevel))/Math.LN2;"vertical"==this.layout?n.y=t.pixelHeight-(t.pixelHeight-n.pixelHeight)*i/this.stepCount-n.pixelHeight:n.x=t.pixelWidth*i/this.stepCount}},e.prototype.handleThumbDrag=function(){var t,e=this.slider,n=this.chart,i=this.thumb;t=Math.log(this.chart.minZoomLevel)/Math.LN2+(t="vertical"==this.layout?this.stepCount*(e.pixelHeight-i.pixelY-i.pixelHeight)/(e.pixelHeight-i.pixelHeight):this.stepCount*i.pixelX/e.pixelWidth);var r=Math.pow(2,t);n.zoomToGeoPoint(void 0,r,!1,0)},Object.defineProperty(e.prototype,"stepCount",{get:function(){return Math.log(this.chart.maxZoomLevel)/Math.LN2-Math.log(this.chart.minZoomLevel)/Math.LN2},enumerable:!0,configurable:!0}),e.prototype.createBackground=function(){return new Bo.a},e}(zr.a);d.c.registeredClasses.ZoomControl=Ho;var Wo=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=ur(),e}return Object(c.c)(e,t),e}(oo);d.c.registeredClasses.Mercator=Wo;var Zo=Math.abs,qo=Math.atan,Yo=Math.atan2,Xo=(Math.ceil,Math.cos),Uo=Math.exp,Jo=Math.floor,Ko=Math.log,Qo=Math.max,$o=Math.min,ta=Math.pow,ea=(Math.round,Math.sign||function(t){return t>0?1:t<0?-1:0}),na=Math.sin,ia=Math.tan,ra=1e-6,oa=1e-12,aa=Math.PI,ua=aa/2,sa=aa/4,ca=Math.SQRT1_2,la=ma(2),ha=ma(aa),pa=2*aa,fa=180/aa,da=aa/180;function ga(t){return t>1?ua:t<-1?-ua:Math.asin(t)}function va(t){return t>1?0:t<-1?aa:Math.acos(t)}function ma(t){return t>0?Math.sqrt(t):0}function ya(t){return(Uo(t)-Uo(-t))/2}function ba(t){return(Uo(t)+Uo(-t))/2}function Pa(t,e){var n=Xo(e),i=function(t){return t?t/Math.sin(t):1}(va(n*Xo(t/=2)));return[2*n*na(t)*i,na(e)*i]}Pa.invert=function(t,e){if(!(t*t+4*e*e>aa*aa+ra)){var n=t,i=e,r=25;do{var o,a=na(n),u=na(n/2),s=Xo(n/2),c=na(i),l=Xo(i),h=na(2*i),p=c*c,f=l*l,d=u*u,g=1-f*s*s,v=g?va(l*s)*ma(o=1/g):o=0,m=2*v*l*u-t,y=v*c-e,b=o*(f*d+v*l*s*p),P=o*(.5*a*h-2*v*c*u),_=.25*o*(h*u-v*c*f*a),w=o*(p*s+v*d*l),S=P*_-w*b;if(!S)break;var x=(y*P-m*w)/S,M=(m*_-y*b)/S;n-=x,i-=M}while((Zo(x)>ra||Zo(M)>ra)&&--r>0);return[n,i]}};function _a(t,e){var n=ia(e/2),i=ma(1-n*n),r=1+i*Xo(t/=2),o=na(t)*i/r,a=n/r,u=o*o,s=a*a;return[4/3*o*(3+u-3*s),4/3*a*(3+3*u-s)]}_a.invert=function(t,e){if(e*=3/8,!(t*=3/8)&&Zo(e)>1)return null;var n=1+t*t+e*e,i=ma((n-ma(n*n-4*e*e))/2),r=ga(i)/3,o=i?function(t){return Ko(t+ma(t*t-1))}(Zo(e/i))/3:function(t){return Ko(t+ma(t*t+1))}(Zo(t))/3,a=Xo(r),u=ba(o),s=u*u-a*a;return[2*ea(t)*Yo(ya(o)*a,.25-s),2*ea(e)*Yo(u*na(r),.25+s)]};var wa=ma(8),Sa=Ko(1+la);function xa(t,e){var n=Zo(e);return n<sa?[t,Ko(ia(sa+e/2))]:[t*Xo(n)*(2*la-1/na(n)),ea(e)*(2*la*(n-sa)-Ko(ia(n/2)))]}xa.invert=function(t,e){if((i=Zo(e))<Sa)return[t,2*qo(Uo(e))-ua];var n,i,r=sa,o=25;do{var a=Xo(r/2),u=ia(r/2);r-=n=(wa*(r-sa)-Ko(u)-i)/(wa-a*a/(2*u))}while(Zo(n)>oa&&--o>0);return[t/(Xo(r)*(wa-1/na(r))),ea(e)*r]};function Ma(t,e){return[t*Xo(e)/Xo(e/=2),2*na(e)]}Ma.invert=function(t,e){var n=2*ga(e/2);return[t*Xo(n/2)/Xo(n),n]};function ja(t,e,n){var i,r,o,a=100;n=void 0===n?0:+n,e=+e;do{(r=t(n))===(o=t(n+ra))&&(o=r+ra),n-=i=-1*ra*(r-e)/(r-o)}while(a-- >0&&Zo(i)>ra);return a<0?NaN:n}function Oa(t,e){var n,i=t*na(e),r=30;do{e-=n=(e+na(e)-i)/(1+Xo(e))}while(Zo(n)>ra&&--r>0);return e/2}function La(t,e,n){function i(i,r){return[t*i*Xo(r=Oa(n,r)),e*na(r)]}return i.invert=function(i,r){return r=ga(r/e),[i/(t*Xo(r)),ga((2*r+na(2*r))/n)]},i}var Ea=La(la/ua,la,aa),Ca=2.00276,Ia=1.11072;function Ta(t,e){var n=Oa(aa,e);return[Ca*t/(1/Xo(e)+Ia/Xo(n)),(e+la*na(n))/Ca]}Ta.invert=function(t,e){var n,i,r=Ca*e,o=e<0?-sa:sa,a=25;do{i=r-la*na(o),o-=n=(na(2*o)+2*o-aa*na(i))/(2*Xo(2*o)+2+aa*Xo(i)*la*Xo(o))}while(Zo(n)>ra&&--a>0);return i=r-la*na(o),[t*(1/Xo(i)+Ia/Xo(o))/Ca,i]};function Ga(t,e){return[t*Xo(e),e]}Ga.invert=function(t,e){return[t/Xo(e),e]};La(1,4/aa,aa);function Da(t,e){var n=ma(1-na(e));return[2/ha*t*n,ha*(1-n)]}Da.invert=function(t,e){var n=(n=e/ha-1)*n;return[n>0?t*ma(aa/n)/2:0,ga(1-n)]};var Na=ma(3);function za(t,e){return[Na*t*(2*Xo(2*e/3)-1)/ha,Na*ha*na(e/3)]}za.invert=function(t,e){var n=3*ga(e/(Na*ha));return[ha*t/(Na*(2*Xo(2*n/3)-1)),n]};function Va(t){var e=Xo(t);function n(t,n){return[t*e,na(n)/e]}return n.invert=function(t,n){return[t/e,ga(n*e)]},n}function ka(t,e){var n=ma(8/(3*aa));return[n*t*(1-Zo(e)/aa),n*e]}ka.invert=function(t,e){var n=ma(8/(3*aa)),i=e/n;return[t/(n*(1-Zo(i)/aa)),i]};function Aa(t,e){var n=ma(4-3*na(Zo(e)));return[2/ma(6*aa)*t*n,ea(e)*ma(2*aa/3)*(2-n)]}Aa.invert=function(t,e){var n=2-Zo(e)/ma(2*aa/3);return[t*ma(6*aa)/(2*n),ea(e)*ga((4-n*n)/3)]};function Ra(t,e){var n=ma(aa*(4+aa));return[2/n*t*(1+ma(1-4*e*e/(aa*aa))),4/n*e]}Ra.invert=function(t,e){var n=ma(aa*(4+aa))/2;return[t*n/(1+ma(1-e*e*(4+aa)/(4*aa))),e*n/2]};function Fa(t,e){var n=(2+ua)*na(e);e/=2;for(var i=0,r=1/0;i<10&&Zo(r)>ra;i++){var o=Xo(e);e-=r=(e+na(e)*(o+2)-n)/(2*o*(1+o))}return[2/ma(aa*(4+aa))*t*(1+Xo(e)),2*ma(aa/(4+aa))*na(e)]}Fa.invert=function(t,e){var n=e*ma((4+aa)/aa)/2,i=ga(n),r=Xo(i);return[t/(2/ma(aa*(4+aa))*(1+r)),ga((i+n*(r+2))/(2+ua))]};function Ba(t,e){return[t*(1+Xo(e))/ma(2+aa),2*e/ma(2+aa)]}Ba.invert=function(t,e){var n=ma(2+aa),i=e*n/2;return[n*t/(1+Xo(i)),i]};function Ha(t,e){for(var n=(1+ua)*na(e),i=0,r=1/0;i<10&&Zo(r)>ra;i++)e-=r=(e+na(e)-n)/(1+Xo(e));return n=ma(2+aa),[t*(1+Xo(e))/n,2*e/n]}Ha.invert=function(t,e){var n=1+ua,i=ma(n/2);return[2*t*i/(1+Xo(e*=i)),ga((e+na(e))/n)]};var Wa=function(){return Yi(Ha).scale(173.044)},Za=3+2*la;function qa(t,e){var n=na(t/=2),i=Xo(t),r=ma(Xo(e)),o=Xo(e/=2),a=na(e)/(o+la*i*r),u=ma(2/(1+a*a)),s=ma((la*o+(i+n)*r)/(la*o+(i-n)*r));return[Za*(u*(s-1/s)-2*Ko(s)),Za*(u*a*(s+1/s)-2*qo(a))]}qa.invert=function(t,e){if(!(n=_a.invert(t/1.2,1.065*e)))return null;var n,i=n[0],r=n[1],o=20;t/=Za,e/=Za;do{var a=i/2,u=r/2,s=na(a),c=Xo(a),l=na(u),h=Xo(u),p=Xo(r),f=ma(p),d=l/(h+la*c*f),g=d*d,v=ma(2/(1+g)),m=(la*h+(c+s)*f)/(la*h+(c-s)*f),y=ma(m),b=y-1/y,P=y+1/y,_=v*b-2*Ko(y)-t,w=v*d*P-2*qo(d)-e,S=l&&ca*f*s*g/l,x=(la*c*h+f)/(2*(h+la*c*f)*(h+la*c*f)*f),M=-.5*d*v*v*v,j=M*S,O=M*x,L=(L=2*h+la*f*(c-s))*L*y,E=(la*c*h*f+p)/L,C=-la*s*l/(f*L),I=b*j-2*E/y+v*(E+E/m),T=b*O-2*C/y+v*(C+C/m),G=d*P*j-2*S/(1+g)+v*P*S+v*d*(E-E/m),D=d*P*O-2*x/(1+g)+v*P*x+v*d*(C-C/m),N=T*G-D*I;if(!N)break;var z=(w*T-_*D)/N,V=(_*G-w*I)/N;i-=z,r=Qo(-ua,$o(ua,r-V))}while((Zo(z)>ra||Zo(V)>ra)&&--o>0);return Zo(Zo(r)-ua)<ra?[0,r]:o&&[i,r]};var Ya=Xo(35*da);function Xa(t,e){var n=ia(e/2);return[t*Ya*ma(1-n*n),(1+Ya)*n]}Xa.invert=function(t,e){var n=e/(1+Ya);return[t&&t/(Ya*ma(1-n*n)),2*qo(n)]};function Ua(t,e){var n=e/2,i=Xo(n);return[2*t/ha*Xo(e)*i*i,ha*ia(n)]}Ua.invert=function(t,e){var n=qo(e/ha),i=Xo(n),r=2*n;return[t*ha/2/(Xo(r)*i*i),r]};var Ja=function(t,e,n,i,r,o,a,u){function s(s,c){if(!c)return[t*s/aa,0];var l=c*c,h=t+l*(e+l*(n+l*i)),p=c*(r-1+l*(o-u+l*a)),f=(h*h+p*p)/(2*p),d=s*ga(h/f)/aa;return[f*na(d),c*(1+l*u)+f*(1-Xo(d))]}return arguments.length<8&&(u=0),s.invert=function(s,c){var l,h,p=aa*s/t,f=c,d=50;do{var g=f*f,v=t+g*(e+g*(n+g*i)),m=f*(r-1+g*(o-u+g*a)),y=v*v+m*m,b=2*m,P=y/b,_=P*P,w=ga(v/P)/aa,S=p*w,x=v*v,M=(2*e+g*(4*n+6*g*i))*f,j=r+g*(3*o+5*g*a),O=(2*(v*M+m*(j-1))*b-y*(2*(j-1)))/(b*b),L=Xo(S),E=na(S),C=P*L,I=P*E,T=p/aa*(1/ma(1-x/_))*(M*P-v*O)/_,G=I-s,D=f*(1+g*u)+P-C-c,N=O*E+C*T,z=C*w,V=1+O-(O*L-I*T),k=I*w,A=N*k-V*z;if(!A)break;p-=l=(D*N-G*V)/A,f-=h=(G*k-D*z)/A}while((Zo(l)>ra||Zo(h)>ra)&&--d>0);return[p,f]},s};Ja(2.8284,-1.6988,.75432,-.18071,1.76003,-.38914,.042555),Ja(2.583819,-.835827,.170354,-.038094,1.543313,-.411435,.082742),Ja(5/6*aa,-.62636,-.0344,0,1.3493,-.05524,0,.045);function Ka(t,e){var n=t*t,i=e*e;return[t*(1-.162388*i)*(.87-952426e-9*n*n),e*(1+i/12)]}Ka.invert=function(t,e){var n,i=t,r=e,o=50;do{var a=r*r;r-=n=(r*(1+a/12)-e)/(1+a/4)}while(Zo(n)>ra&&--o>0);o=50,t/=1-.162388*a;do{var u=(u=i*i)*u;i-=n=(i*(.87-952426e-9*u)-t)/(.87-.00476213*u)}while(Zo(n)>ra&&--o>0);return[i,r]};Ja(2.6516,-.76534,.19123,-.047094,1.36289,-.13965,.031762);function Qa(t,e){var n=ea(t),i=ea(e),r=Xo(e),o=Xo(t)*r,a=na(t)*r,u=na(i*e);t=Zo(Yo(a,u)),e=ga(o),Zo(t-ua)>ra&&(t%=ua);var s=function(t,e){if(e===ua)return[0,0];var n,i,r=na(e),o=r*r,a=o*o,u=1+a,s=1+3*a,c=1-a,l=ga(1/ma(u)),h=c+o*u*l,p=(1-r)/h,f=ma(p),d=p*u,g=ma(d),v=f*c;if(0===t)return[0,-(v+o*g)];var m,y=Xo(e),b=1/y,P=2*r*y,_=(-h*y-(-3*o+l*s)*P*(1-r))/(h*h),w=-b*P,S=-b*(o*u*_+p*s*P),x=-2*b*(c*(.5*_/f)-2*o*f*P),M=4*t/aa;if(t>.222*aa||e<aa/4&&t>.175*aa){if(n=(v+o*ma(d*(1+a)-v*v))/(1+a),t>aa/4)return[n,n];var j=n,O=.5*n;n=.5*(O+j),i=50;do{var L=ma(d-n*n),E=n*(x+w*L)+S*ga(n/g)-M;if(!E)break;E<0?O=n:j=n,n=.5*(O+j)}while(Zo(j-O)>ra&&--i>0)}else{n=ra,i=25;do{var C=n*n,I=ma(d-C),T=x+w*I,G=n*T+S*ga(n/g)-M,D=T+(S-w*C)/I;n-=m=I?G/D:0}while(Zo(m)>ra&&--i>0)}return[n,-v-o*ma(d-n*n)]}(t>aa/4?ua-t:t,e);return t>aa/4&&(u=s[0],s[0]=-s[1],s[1]=-u),s[0]*=n,s[1]*=-i,s}Qa.invert=function(t,e){Zo(t)>1&&(t=2*ea(t)-t),Zo(e)>1&&(e=2*ea(e)-e);var n=ea(t),i=ea(e),r=-n*t,o=-i*e,a=o/r<1,u=function(t,e){var n=0,i=1,r=.5,o=50;for(;;){var a=r*r,u=ma(r),s=ga(1/ma(1+a)),c=1-a+r*(1+a)*s,l=(1-u)/c,h=ma(l),p=l*(1+a),f=h*(1-a),d=p-t*t,g=ma(d),v=e+f+r*g;if(Zo(i-n)<oa||0==--o||0===v)break;v>0?n=r:i=r,r=.5*(n+i)}if(!o)return null;var m=ga(u),y=Xo(m),b=1/y,P=2*u*y,_=(-c*y-(-3*r+s*(1+3*a))*P*(1-u))/(c*c);return[aa/4*(t*(-2*b*(.5*_/h*(1-a)-2*r*h*P)+-b*P*g)+-b*(r*(1+a)*_+l*(1+3*a)*P)*ga(t/ma(p))),m]}(a?o:r,a?r:o),s=u[0],c=u[1],l=Xo(c);return a&&(s=-ua-s),[n*(Yo(na(s)*l,-na(c))+aa),i*ga(Xo(s)*l)]};function $a(t,e){var n,i,r,o,a;if(e<ra)return[(o=na(t))-(n=e*(t-o*(i=Xo(t)))/4)*i,i+n*o,1-e*o*o/2,t-n];if(e>=1-ra)return n=(1-e)/4,r=1/(i=ba(t)),[(o=function(t){return((t=Uo(2*t))-1)/(t+1)}(t))+n*((a=i*ya(t))-t)/(i*i),r-n*o*r*(a-t),r+n*o*r*(a+t),2*qo(Uo(t))-ua+n*(a-t)/i];var u=[1,0,0,0,0,0,0,0,0],s=[ma(e),0,0,0,0,0,0,0,0],c=0;for(i=ma(1-e),a=1;Zo(s[c]/u[c])>ra&&c<8;)n=u[c++],s[c]=(n-i)/2,u[c]=(n+i)/2,i=ma(n*i),a*=2;r=a*u[c]*t;do{r=(ga(o=s[c]*na(i=r)/u[c])+r)/2}while(--c);return[na(r),o=Xo(r),o/Xo(r-i),r]}function tu(t,e){if(!e)return t;if(1===e)return Ko(ia(t/2+sa));for(var n=1,i=ma(1-e),r=ma(e),o=0;Zo(r)>ra;o++){if(t%aa){var a=qo(i*ia(t)/n);a<0&&(a+=aa),t+=a+~~(t/aa)*aa}else t+=t;r=(n+i)/2,i=ma(n*i),r=((n=r)-i)/2}return t/(ta(2,o)*n)}function eu(t,e){var n=(la-1)/(la+1),i=ma(1-n*n),r=tu(ua,i*i),o=Ko(ia(aa/4+Zo(e)/2)),a=Uo(-1*o)/ma(n),u=function(t,e){var n=t*t,i=e+1,r=1-n-e*e;return[.5*((t>=0?ua:-ua)-Yo(r,2*t)),-.25*Ko(r*r+4*n)+.5*Ko(i*i+n)]}(a*Xo(-1*t),a*na(-1*t)),s=function(t,e,n){var i=Zo(t),r=ya(Zo(e));if(i){var o=1/na(i),a=1/(ia(i)*ia(i)),u=-(a+n*(r*r*o*o)-1+n),s=(-u+ma(u*u-(n-1)*a*4))/2;return[tu(qo(1/ma(s)),n)*ea(t),tu(qo(ma((s/a-1)/n)),1-n)*ea(e)]}return[0,tu(qo(r),1-n)*ea(e)]}(u[0],u[1],i*i);return[-s[1],(e>=0?1:-1)*(.5*r-s[0])]}eu.invert=function(t,e){var n=(la-1)/(la+1),i=ma(1-n*n),r=function(t,e,n){var i,r,o;return t?(i=$a(t,n),e?(o=(r=$a(e,1-n))[1]*r[1]+n*i[0]*i[0]*r[0]*r[0],[[i[0]*r[2]/o,i[1]*i[2]*r[0]*r[1]/o],[i[1]*r[1]/o,-i[0]*i[2]*r[0]*r[2]/o],[i[2]*r[1]*r[2]/o,-n*i[0]*i[1]*r[0]/o]]):[[i[0],0],[i[1],0],[i[2],0]]):[[0,(r=$a(e,1-n))[0]/r[1]],[1/r[1],0],[r[2]/r[1],0]]}(.5*tu(ua,i*i)-e,-t,i*i),o=function(t,e){var n=e[0]*e[0]+e[1]*e[1];return[(t[0]*e[0]+t[1]*e[1])/n,(t[1]*e[0]-t[0]*e[1])/n]}(r[0],r[1]);return[Yo(o[1],o[0])/-1,2*qo(Uo(-.5*Ko(n*o[0]*o[0]+n*o[1]*o[1])))-ua]};ga(1-1/3),Va(0);var nu=.7109889596207567,iu=.0528035274542;function ru(t,e){return e>-nu?((t=Ea(t,e))[1]+=iu,t):Ga(t,e)}ru.invert=function(t,e){return e>-nu?Ea.invert(t,e-iu):Ga.invert(t,e)};function ou(t,e){return Zo(e)>nu?((t=Ea(t,e))[1]-=e>0?iu:-iu,t):Ga(t,e)}ou.invert=function(t,e){return Zo(e)>nu?Ea.invert(t,e+(e>0?iu:-iu)):Ga.invert(t,e)};function au(t,e){return[3/pa*t*ma(aa*aa/3-e*e),e]}au.invert=function(t,e){return[pa/3*t/ma(aa*aa/3-e*e),e]};var uu=aa/la;function su(t,e){return[t*(1+ma(Xo(e)))/2,e/(Xo(e/2)*Xo(t/6))]}su.invert=function(t,e){var n=Zo(t),i=Zo(e),r=ra,o=ua;i<uu?o*=i/uu:r+=6*va(uu/i);for(var a=0;a<25;a++){var u=na(o),s=ma(Xo(o)),c=na(o/2),l=Xo(o/2),h=na(r/6),p=Xo(r/6),f=.5*r*(1+s)-n,d=o/(l*p)-i,g=s?-.25*r*u/s:0,v=.5*(1+s),m=(1+.5*o*c/l)/(l*p),y=o/l*(h/6)/(p*p),b=g*y-m*v,P=(f*y-d*v)/b,_=(d*g-f*m)/b;if(o-=P,r-=_,Zo(P)<ra&&Zo(_)<ra)break}return[t<0?-r:r,e<0?-o:o]};function cu(t,e){var n=t*t,i=e*e;return[t*(.975534+i*(-.0143059*n-.119161+-.0547009*i)),e*(1.00384+n*(.0802894+-.02855*i+199025e-9*n)+i*(.0998909+-.0491032*i))]}cu.invert=function(t,e){var n=ea(t)*aa,i=e/2,r=50;do{var o=n*n,a=i*i,u=n*i,s=n*(.975534+a*(-.0143059*o-.119161+-.0547009*a))-t,c=i*(1.00384+o*(.0802894+-.02855*a+199025e-9*o)+a*(.0998909+-.0491032*a))-e,l=.975534-a*(.119161+3*o*.0143059+.0547009*a),h=-u*(.238322+.2188036*a+.0286118*o),p=u*(.1605788+7961e-7*o+-.0571*a),f=1.00384+o*(.0802894+199025e-9*o)+a*(3*(.0998909-.02855*o)-.245516*a),d=h*p-f*l,g=(c*h-s*f)/d,v=(s*p-c*l)/d;n-=g,i-=v}while((Zo(g)>ra||Zo(v)>ra)&&--r>0);return r&&[n,i]};function lu(t,e){return[na(t)/Xo(e),ia(e)*Xo(t)]}lu.invert=function(t,e){var n=t*t,i=e*e+1,r=n+i,o=t?ca*ma((r-ma(r*r-4*n))/n):1/ma(i);return[ga(t*o),ea(e)*va(o)]};function hu(t,e){return[t,1.25*Ko(ia(sa+.4*e))]}hu.invert=function(t,e){return[t,2.5*qo(Uo(.8*e))-.625*aa]};var pu=function(){return Yi(hu).scale(108.318)};var fu=ma(6),du=ma(7);function gu(t,e){var n=ga(7*na(e)/(3*fu));return[fu*t*(2*Xo(2*n/3)-1)/du,9*na(n/3)/du]}gu.invert=function(t,e){var n=3*ga(e*du/9);return[t*du/(fu*(2*Xo(2*n/3)-1)),ga(3*na(n)*fu/7)]};function vu(t,e){for(var n,i=(1+ca)*na(e),r=e,o=0;o<25&&(r-=n=(na(r/2)+na(r)-i)/(.5*Xo(r/2)+Xo(r)),!(Zo(n)<ra));o++);return[t*(1+2*Xo(r)/Xo(r/2))/(3*la),2*ma(3)*na(r/2)/ma(2+la)]}vu.invert=function(t,e){var n=e*ma(2+la)/(2*ma(3)),i=2*ga(n);return[3*la*t/(1+2*Xo(i)/Xo(i/2)),ga((n+na(i))/(1+ca))]};function mu(t,e){for(var n,i=ma(6/(4+aa)),r=(1+aa/4)*na(e),o=e/2,a=0;a<25&&(o-=n=(o/2+na(o)-r)/(.5+Xo(o)),!(Zo(n)<ra));a++);return[i*(.5+Xo(o))*t/1.5,i*o]}mu.invert=function(t,e){var n=ma(6/(4+aa)),i=e/n;return Zo(Zo(i)-ua)<ra&&(i=i<0?-ua:ua),[1.5*t/(n*(.5+Xo(i))),ga((i/2+na(i))/(1+aa/4))]};function yu(t,e){var n=e*e,i=n*n,r=n*i;return[t*(.84719-.13063*n+r*r*(.05494*n-.04515-.02326*i+.00331*r)),e*(1.01183+i*i*(.01926*n-.02625-.00396*i))]}yu.invert=function(t,e){var n,i,r,o,a=e,u=25;do{a-=n=(a*(1.01183+(r=(i=a*a)*i)*r*(.01926*i-.02625-.00396*r))-e)/(1.01183+r*r*(.21186*i-.23625+-.05148*r))}while(Zo(n)>oa&&--u>0);return[t/(.84719-.13063*(i=a*a)+(o=i*(r=i*i))*o*(.05494*i-.04515-.02326*r+.00331*o)),a]};function bu(t,e){return[t*(1+Xo(e))/2,2*(e-ia(e/2))]}bu.invert=function(t,e){for(var n=e/2,i=0,r=1/0;i<10&&Zo(r)>ra;++i){var o=Xo(e/2);e-=r=(e-ia(e/2)-n)/(1-.5/(o*o))}return[2*t/(1+Xo(e)),e]};function Pu(t,e){var n=na(e),i=Xo(e),r=ea(t);if(0===t||Zo(e)===ua)return[0,e];if(0===e)return[t,0];if(Zo(t)===ua)return[t*i,ua*n];var o=aa/(2*t)-2*t/aa,a=2*e/aa,u=(1-a*a)/(n-a),s=o*o,c=u*u,l=1+s/c,h=1+c/s,p=(o*n/u-o/2)/l,f=(c*n/s+u/2)/h,d=f*f-(c*n*n/s+u*n-1)/h;return[ua*(p+ma(p*p+i*i/l)*r),ua*(f+ma(d<0?0:d)*ea(-e*o)*r)]}Pu.invert=function(t,e){var n=(t/=ua)*t,i=n+(e/=ua)*e,r=aa*aa;return[t?(i-1+ma((1-i)*(1-i)+4*n))/(2*t)*ua:0,ja(function(t){return i*(aa*na(t)-2*t)*aa+4*t*t*(e-na(t))+2*aa*t-r*e},0)]};var _u=1.0148,wu=.23185,Su=-.14499,xu=.02406,Mu=_u,ju=5*wu,Ou=7*Su,Lu=9*xu;function Eu(t,e){var n=e*e;return[t,e*(_u+n*n*(wu+n*(Su+xu*n)))]}Eu.invert=function(t,e){e>1.790857183?e=1.790857183:e<-1.790857183&&(e=-1.790857183);var n,i=e;do{var r=i*i;i-=n=(i*(_u+r*r*(wu+r*(Su+xu*r)))-e)/(Mu+r*r*(ju+r*(Ou+Lu*r)))}while(Zo(n)>ra);return[t,i]};function Cu(t,e){if(Zo(e)<ra)return[t,0];var n=ia(e),i=t*na(e);return[na(i)/n,e+(1-Xo(i))/n]}Cu.invert=function(t,e){if(Zo(e)<ra)return[t,0];var n,i=t*t+e*e,r=.5*e,o=10;do{var a=ia(r),u=1/Xo(r),s=i-2*e*r+r*r;r-=n=(a*s+2*(r-e))/(2+s*u*u+2*(r-e)*a)}while(Zo(n)>ra&&--o>0);return a=ia(r),[(Zo(e)<Zo(r+1/a)?ga(t*a):ea(e)*ea(t)*(va(Zo(t*a))+ua))/na(r),r]};var Iu=[[0,90],[-90,0],[0,0],[90,0],[180,0],[0,-90]],Tu=([[0,2,1],[0,3,2],[5,1,2],[5,2,3],[0,1,4],[0,4,3],[5,4,1],[5,3,4]].map(function(t){return t.map(function(t){return Iu[t]})}),2/ma(3));function Gu(t,e){var n=Da(t,e);return[n[0]*Tu,n[1]]}Gu.invert=function(t,e){return Da.invert(t/Tu,e)};var Du=[[.9986,-.062],[1,0],[.9986,.062],[.9954,.124],[.99,.186],[.9822,.248],[.973,.31],[.96,.372],[.9427,.434],[.9216,.4958],[.8962,.5571],[.8679,.6176],[.835,.6769],[.7986,.7346],[.7597,.7903],[.7186,.8435],[.6732,.8936],[.6213,.9394],[.5722,.9761],[.5322,1]];function Nu(t,e){var n,i=$o(18,36*Zo(e)/aa),r=Jo(i),o=i-r,a=(n=Du[r])[0],u=n[1],s=(n=Du[++r])[0],c=n[1],l=(n=Du[$o(19,++r)])[0],h=n[1];return[t*(s+o*(l-a)/2+o*o*(l-2*s+a)/2),ea(e)*(c+o*(h-u)/2+o*o*(h-2*c+u)/2)]}Du.forEach(function(t){t[1]*=1.593415793900743}),Nu.invert=function(t,e){var n=90*e,i=$o(18,Zo(n/5)),r=Qo(0,Jo(i));do{var o=Du[r][1],a=Du[r+1][1],u=Du[$o(19,r+2)][1],s=u-o,c=u-2*a+o,l=2*(Zo(e)-a)/s,h=c/s,p=l*(1-h*l*(1-2*h*l));if(p>=0||1===r){n=(e>=0?5:-5)*(p+i);var f,d=50;do{p=(i=$o(18,Zo(n)/5))-(r=Jo(i)),o=Du[r][1],a=Du[r+1][1],u=Du[$o(19,r+2)][1],n-=(f=ea(e)*(a+p*(u-o)/2+p*p*(u-2*a+o)/2)-e)*fa}while(Zo(f)>oa&&--d>0);break}}while(--r>=0);var g=Du[r][0],v=Du[r+1][0],m=Du[$o(19,r+2)][0];return[t/(v+p*(m-g)/2+p*p*(m-2*v+g)/2),n*da]};function zu(t,e){var n=ia(e/2),i=na(sa*n);return[t*(.74482-.34588*i*i),1.70711*n]}zu.invert=function(t,e){var n=e/1.70711,i=na(sa*n);return[t/(.74482-.34588*i*i),2*qo(n)]};function Vu(t,e){if(Zo(e)<ra)return[t,0];var n=Zo(e/ua),i=ga(n);if(Zo(t)<ra||Zo(Zo(e)-ua)<ra)return[0,ea(e)*aa*ia(i/2)];var r=Xo(i),o=Zo(aa/t-t/aa)/2,a=o*o,u=r/(n+r-1),s=u*(2/n-1),c=s*s,l=c+a,h=u-c,p=a+u;return[ea(t)*aa*(o*h+ma(a*h*h-l*(u*u-c)))/l,ea(e)*aa*(s*p-o*ma((a+1)*l-p*p))/l]}Vu.invert=function(t,e){if(Zo(e)<ra)return[t,0];if(Zo(t)<ra)return[0,ua*na(2*qo(e/aa))];var n=(t/=aa)*t,i=(e/=aa)*e,r=n+i,o=r*r,a=-Zo(e)*(1+r),u=a-2*i+n,s=-2*a+1+2*i+o,c=i/s+(2*u*u*u/(s*s*s)-9*a*u/(s*s))/27,l=(a-u*u/(3*s))/s,h=2*ma(-l/3),p=va(3*c/(l*h))/3;return[aa*(r-1+ma(1+2*(n-i)+o))/(2*t),ea(e)*aa*(-h*Xo(p+aa/3)-u/(3*s))]};function ku(t,e){if(Zo(e)<ra)return[t,0];var n=Zo(e/ua),i=ga(n);if(Zo(t)<ra||Zo(Zo(e)-ua)<ra)return[0,ea(e)*aa*ia(i/2)];var r=Xo(i),o=Zo(aa/t-t/aa)/2,a=o*o,u=r*(ma(1+a)-o*r)/(1+a*n*n);return[ea(t)*aa*u,ea(e)*aa*ma(1-u*(2*o+u))]}ku.invert=function(t,e){if(!t)return[0,ua*na(2*qo(e/aa))];var n=Zo(t/aa),i=(1-n*n-(e/=aa)*e)/(2*n),r=ma(i*i+1);return[ea(t)*aa*(r-i),ea(e)*ua*na(2*Yo(ma((1-2*i*n)*(i+r)-n),ma(r+i+n)))]};function Au(t,e){if(Zo(e)<ra)return[t,0];var n=e/ua,i=ga(n);if(Zo(t)<ra||Zo(Zo(e)-ua)<ra)return[0,aa*ia(i/2)];var r=(aa/t-t/aa)/2,o=n/(1+Xo(i));return[aa*(ea(t)*ma(r*r+1-o*o)-r),aa*o]}Au.invert=function(t,e){if(!e)return[t,0];var n=e/aa,i=(aa*aa*(1-n*n)-t*t)/(2*aa*t);return[t?aa*(ea(t)*ma(i*i+1)-i):0,ua*na(2*qo(n))]};function Ru(t,e){if(!e)return[t,0];var n=Zo(e);if(!t||n===ua)return[0,e];var i=n/ua,r=i*i,o=(8*i-r*(r+2)-5)/(2*r*(i-1)),a=o*o,u=i*o,s=r+a+2*u,c=i+3*o,l=t/ua,h=l+1/l,p=ea(Zo(t)-ua)*ma(h*h-4),f=p*p,d=(p*(s+a-1)+2*ma(s*(r+a*f-1)+(1-r)*(r*(c*c+4*a)+12*u*a+4*a*a)))/(4*s+f);return[ea(t)*ua*d,ea(e)*ua*ma(1+p*Zo(d)-d*d)]}Ru.invert=function(t,e){var n;if(!t||!e)return[t,e];var i=ea(e);e=Zo(e)/aa;var r=ea(t)*t/ua,o=(r*r-1+4*e*e)/Zo(r),a=o*o,u=e*(2-(e>.5?$o(e,Zo(t)):0)),s=t*t+e*e,c=50;do{var l=u*u,h=(8*u-l*(l+2)-5)/(2*l*(u-1)),p=(3*u-l*u-10)/(2*l*u),f=h*h,d=u*h,g=u+h,v=g*g,m=u+3*h,y=-2*g*(4*d*f+(1-4*l+3*l*l)*(1+p)+f*(14*l-6-a+(8*l-8-2*a)*p)+d*(12*l-8+(10*l-10-a)*p)),b=ma(v*(l+f*a-1)+(1-l)*(l*(m*m+4*f)+f*(12*d+4*f)));u-=n=(o*(v+f-1)+2*b-r*(4*v+a))/(o*(2*h*p+2*g*(1+p))+y/b-8*g*(o*(-1+f+v)+2*b)*(1+p)/(a+4*v))}while(n*s*s>ra&&--c>0);return[ea(t)*(ma(o*o+4)+o)*aa/4,i*ua*u]};var Fu=4*aa+3*ma(3),Bu=2*ma(2*aa*ma(3)/Fu);La(Bu*ma(3)/aa,Bu,Fu/6);function Hu(t,e){return[t*ma(1-3*e*e/(aa*aa)),e]}Hu.invert=function(t,e){return[t/ma(1-3*e*e/(aa*aa)),e]};function Wu(t,e){var n=Xo(e),i=Xo(t)*n,r=1-i,o=Xo(t=Yo(na(t)*n,-na(e))),a=na(t);return[a*(n=ma(1-i*i))-o*r,-o*n-a*r]}Wu.invert=function(t,e){var n=(t*t+e*e)/-2,i=ma(-n*(2+n)),r=e*n+t*i,o=t*n-e*i,a=ma(o*o+r*r);return[Yo(i*r,a*(1+n)),a?-ga(i*o/a):0]};function Zu(t,e){var n=Pa(t,e);return[(n[0]+t/ua)/2,(n[1]+e)/2]}Zu.invert=function(t,e){var n=t,i=e,r=25;do{var o,a=Xo(i),u=na(i),s=na(2*i),c=u*u,l=a*a,h=na(n),p=Xo(n/2),f=na(n/2),d=f*f,g=1-l*p*p,v=g?va(a*p)*ma(o=1/g):o=0,m=.5*(2*v*a*f+n/ua)-t,y=.5*(v*u+i)-e,b=.5*o*(l*d+v*a*p*c)+.5/ua,P=o*(h*s/4-v*u*f),_=.125*o*(s*f-v*u*l*h),w=.5*o*(c*p+v*d*a)+.5,S=P*_-w*b,x=(y*P-m*w)/S,M=(m*_-y*b)/S;n-=x,i-=M}while((Zo(x)>ra||Zo(M)>ra)&&--r>0);return[n,i]};var qu=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=pu(),e}return Object(c.c)(e,t),e}(oo);d.c.registeredClasses.Miller=qu;var Yu=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=Wa(),e}return Object(c.c)(e,t),e}(oo);d.c.registeredClasses.Eckert6=Yu;var Xu=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=Er(),e}return Object(c.c)(e,t),e}(oo);d.c.registeredClasses.Orthographic=Xu;var Uu=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=Ir(),e}return Object(c.c)(e,t),e}(oo);d.c.registeredClasses.Stereographic=Uu;var Ju=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=Qi(),e}return Object(c.c)(e,t),e}(oo);d.c.registeredClasses.Albers=Ju;var Ku=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=$i(),e}return Object(c.c)(e,t),e}(oo);d.c.registeredClasses.AlbersUsa=Ku;var Qu=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=Or(),e}return Object(c.c)(e,t),e}(oo);d.c.registeredClasses.NaturalEarth1=Qu;var $u=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=ir(),e}return Object(c.c)(e,t),e}(oo);d.c.registeredClasses.AzimuthalEqualArea=$u;var ts=function(t){function e(){var e=t.call(this)||this;return e.d3Projection=wr(),e}return Object(c.c)(e,t),e}(oo);d.c.registeredClasses.EqualEarth=ts,window.am4maps=a},QaCB:function(t,e,n){"use strict";function i(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}Object.defineProperty(e,"__esModule",{value:!0}),n.d(e,"default",function(){return r});var r=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o;if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.data=e,this.length=this.data.length,this.compare=n,this.length>0)for(var i=(this.length>>1)-1;i>=0;i--)this._down(i)}return function(t,e,n){e&&i(t.prototype,e),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1})}(t,[{key:"push",value:function(t){this.data.push(t),this.length++,this._up(this.length-1)}},{key:"pop",value:function(){if(0!==this.length){var t=this.data[0],e=this.data.pop();return this.length--,this.length>0&&(this.data[0]=e,this._down(0)),t}}},{key:"peek",value:function(){return this.data[0]}},{key:"_up",value:function(t){for(var e=this.data,n=this.compare,i=e[t];t>0;){var r=t-1>>1,o=e[r];if(n(i,o)>=0)break;e[t]=o,t=r}e[t]=i}},{key:"_down",value:function(t){for(var e=this.data,n=this.compare,i=this.length>>1,r=e[t];t<i;){var o=1+(t<<1),a=e[o],u=o+1;if(u<this.length&&n(e[u],a)<0&&(o=u,a=e[u]),n(a,r)>=0)break;e[t]=a,t=o}e[t]=r}}]),t}();function o(t,e){return t<e?-1:t>e?1:0}},"o+vr":function(t,e,n){"use strict";var i=n("QaCB");function r(t,e,n){var r,u,s,c;e=e||1;for(var l=0;l<t[0].length;l++){var h=t[0][l];(!l||h[0]<r)&&(r=h[0]),(!l||h[1]<u)&&(u=h[1]),(!l||h[0]>s)&&(s=h[0]),(!l||h[1]>c)&&(c=h[1])}var p=s-r,f=c-u,d=Math.min(p,f),g=d/2;if(0===d){var v=[r,u];return v.distance=0,v}for(var m=new i(void 0,o),y=r;y<s;y+=d)for(var b=u;b<c;b+=d)m.push(new a(y+g,b+g,g,t));var P=function(t){for(var e=0,n=0,i=0,r=t[0],o=0,u=r.length,s=u-1;o<u;s=o++){var c=r[o],l=r[s],h=c[0]*l[1]-l[0]*c[1];n+=(c[0]+l[0])*h,i+=(c[1]+l[1])*h,e+=3*h}return 0===e?new a(r[0][0],r[0][1],0,t):new a(n/e,i/e,0,t)}(t),_=new a(r+p/2,u+f/2,0,t);_.d>P.d&&(P=_);for(var w=m.length;m.length;){var S=m.pop();S.d>P.d&&(P=S,n&&console.log("found best %d after %d probes",Math.round(1e4*S.d)/1e4,w)),S.max-P.d<=e||(g=S.h/2,m.push(new a(S.x-g,S.y-g,g,t)),m.push(new a(S.x+g,S.y-g,g,t)),m.push(new a(S.x-g,S.y+g,g,t)),m.push(new a(S.x+g,S.y+g,g,t)),w+=4)}n&&(console.log("num probes: "+w),console.log("best distance: "+P.d));var x=[P.x,P.y];return x.distance=P.d,x}function o(t,e){return e.max-t.max}function a(t,e,n,i){this.x=t,this.y=e,this.h=n,this.d=function(t,e,n){for(var i=!1,r=1/0,o=0;o<n.length;o++)for(var a=n[o],s=0,c=a.length,l=c-1;s<c;l=s++){var h=a[s],p=a[l];h[1]>e!=p[1]>e&&t<(p[0]-h[0])*(e-h[1])/(p[1]-h[1])+h[0]&&(i=!i),r=Math.min(r,u(t,e,h,p))}return 0===r?0:(i?1:-1)*Math.sqrt(r)}(t,e,i),this.max=this.d+this.h*Math.SQRT2}function u(t,e,n,i){var r=n[0],o=n[1],a=i[0]-r,u=i[1]-o;if(0!==a||0!==u){var s=((t-r)*a+(e-o)*u)/(a*a+u*u);s>1?(r=i[0],o=i[1]):s>0&&(r+=a*s,o+=u*s)}return(a=t-r)*a+(u=e-o)*u}i.default&&(i=i.default),t.exports=r,t.exports.default=r}},["QJ7E"]);
