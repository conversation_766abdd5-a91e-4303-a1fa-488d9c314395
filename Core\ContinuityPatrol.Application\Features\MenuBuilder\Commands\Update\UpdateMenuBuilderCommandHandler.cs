using ContinuityPatrol.Application.Features.MenuBuilder.Events.Update;

namespace ContinuityPatrol.Application.Features.MenuBuilder.Commands.Update;

public class UpdateMenuBuilderCommandHandler : IRequestHandler<UpdateMenuBuilderCommand, UpdateMenuBuilderResponse>
{
    private readonly IMenuBuilderRepository _menuBuilderRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateMenuBuilderCommandHandler(IMapper mapper, IMenuBuilderRepository menuBuilderRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _menuBuilderRepository = menuBuilderRepository;
        _publisher = publisher;
    }

    public async Task<UpdateMenuBuilderResponse> Handle(UpdateMenuBuilderCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _menuBuilderRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.MenuBuilder), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateMenuBuilderCommand), typeof(Domain.Entities.MenuBuilder));

        await _menuBuilderRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateMenuBuilderResponse
        {
            Message = Message.Update(nameof(Domain.Entities.MenuBuilder), eventToUpdate.Name),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new MenuBuilderUpdatedEvent { Name = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}
