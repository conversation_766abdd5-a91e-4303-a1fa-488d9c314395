﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class AlertInformationRepositoryMocks
{
    public static Mock<IAlertInformationRepository> CreateAlertInformationRepository(List<AlertInformation> alertInformations)
    {
        var alertInformationRepository = new Mock<IAlertInformationRepository>();

        alertInformationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alertInformations);

        alertInformationRepository.Setup(repo=>repo.AddAsync(It.IsAny<AlertInformation>())).ReturnsAsync(
            (AlertInformation alertInformation) =>
            {
                alertInformation.Id = new Fixture().Create<int>();
                
                alertInformation.ReferenceId = new Fixture().Create<Guid>().ToString();

                alertInformations.Add(alertInformation);

                return alertInformation;
            });
        return alertInformationRepository;
    }

    public static Mock<IAlertInformationRepository> UpdateAlertInformationRepository(List<AlertInformation> alertInformations)
    {
        var alertInformationRepository = new Mock<IAlertInformationRepository>();

        alertInformationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alertInformations);

        alertInformationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alertInformations.SingleOrDefault(x => x.ReferenceId == i));

        alertInformationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AlertInformation>())).ReturnsAsync((AlertInformation alertInformation) =>
        {
            var index = alertInformations.FindIndex(item => item.ReferenceId == alertInformation.ReferenceId);

            alertInformations[index] = alertInformation;

            return alertInformation;

        });
        return alertInformationRepository;
    }

    public static Mock<IAlertInformationRepository> DeleteAlertInformationRepository(List<AlertInformation> alertInformations)
    {
        var alertInformationRepository = new Mock<IAlertInformationRepository>();

        alertInformationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alertInformations);

        alertInformationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alertInformations.SingleOrDefault(x => x.ReferenceId == i));

        alertInformationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AlertInformation>())).ReturnsAsync(
            (AlertInformation alertInformation) =>
            {
                var index = alertInformations.FindIndex(item => item.ReferenceId == alertInformation.ReferenceId);
                
                alertInformation.IsActive = false;
                
                alertInformations[index]= alertInformation;
                
                return alertInformation;
            });
        return alertInformationRepository;
    }

    public static Mock<IAlertInformationRepository> GetAlertInformationRepository(List<AlertInformation> alertInformations)
    {
        var alertInformationRepository = new Mock<IAlertInformationRepository>();

        alertInformationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alertInformations);

        alertInformationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alertInformations.SingleOrDefault(x => x.ReferenceId == i));

        return alertInformationRepository;
    }

    public static Mock<IAlertInformationRepository> GetAlertInformationEmptyRepository()
    {
        var alertInformationEmptyRepository = new Mock<IAlertInformationRepository>();

        alertInformationEmptyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<AlertInformation>());

        return alertInformationEmptyRepository;
    }

    public static Mock<IAlertInformationRepository> GetPaginatedAlertInformationRepository(List<AlertInformation> alertInformations)
    {
        var alertInformationRepository = new Mock<IAlertInformationRepository>();

        //var queryableAccessManager = alertInformations.BuildMock();

        //alertInformationRepository.Setup(repo => repo.GetPaginatedQuery())
        //    .Returns(queryableAccessManager);

        alertInformationRepository.Setup(repo => repo.PaginatedListAllAsync(
          It.IsAny<int>(),
          It.IsAny<int>(),
          It.IsAny<Specification<AlertInformation>>(),
          It.IsAny<string>(),
          It.IsAny<string>()))
      .ReturnsAsync((int pageNumber, int pageSize, Specification<AlertInformation> spec, string sortColumn, string sortOrder) =>
      {
          var sortedAlertinformation = alertInformations.AsQueryable();

          if (spec.Criteria != null)
          {
              sortedAlertinformation = sortedAlertinformation.Where(spec.Criteria);
          }

          if (!string.IsNullOrWhiteSpace(sortColumn))
          {
              sortedAlertinformation = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase)
                  ? sortedAlertinformation.OrderByDescending(c => c.Type)
                  : sortedAlertinformation.OrderBy(c => c.Type);
          }

          var totalCount = sortedAlertinformation.Count();
          var paginated = sortedAlertinformation
              .Skip((pageNumber - 1) * pageSize)
              .Take(pageSize)
              .ToList();

          return PaginatedResult<AlertInformation>.Success(paginated, totalCount, pageNumber, pageSize);
      });

        return alertInformationRepository;
    }

    public static Mock<IAlertInformationRepository> GetAlertInformationByCodeRepository(List<AlertInformation> alertInformations)
    {
        var alertInformationRepository = new Mock<IAlertInformationRepository>();

        alertInformationRepository.Setup(repo => repo.GetAlertInformationByCode(It.IsAny<string>())).ReturnsAsync(alertInformations);

        return alertInformationRepository;
    }
}
