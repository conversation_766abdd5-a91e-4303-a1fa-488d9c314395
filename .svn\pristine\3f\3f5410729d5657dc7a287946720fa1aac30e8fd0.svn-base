namespace ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetDetail;

public class
    GetCyberComponentGroupDetailsQueryHandler : IRequestHandler<GetCyberComponentGroupDetailQuery,
        CyberComponentGroupDetailVm>
{
    private readonly ICyberComponentGroupRepository _cyberComponentGroupRepository;
    private readonly IMapper _mapper;

    public GetCyberComponentGroupDetailsQueryHandler(IMapper mapper,
        ICyberComponentGroupRepository cyberComponentGroupRepository)
    {
        _mapper = mapper;
        _cyberComponentGroupRepository = cyberComponentGroupRepository;
    }

    public async Task<CyberComponentGroupDetailVm> Handle(GetCyberComponentGroupDetailQuery request,
        CancellationToken cancellationToken)
    {
        var cyberComponentGroup = await _cyberComponentGroupRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(cyberComponentGroup, nameof(Domain.Entities.CyberComponentGroup),
            new NotFoundException(nameof(Domain.Entities.CyberComponentGroup), request.Id));

        var cyberComponentGroupDetailDto = _mapper.Map<CyberComponentGroupDetailVm>(cyberComponentGroup);

        return cyberComponentGroupDetailDto;
    }
}