﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.MongoDBMonitorLog.Queries.GetPaginatedList;

public class GetMongoDBMonitorLogPaginatedListQueryHandler : IRequestHandler<GetMongoDBMonitorLogPaginatedListQuery,
    PaginatedResult<MongoDBMonitorLogPaginatedListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMongoDbMonitorLogRepository _mongoDBMonitorLogRepository;

    public GetMongoDBMonitorLogPaginatedListQueryHandler(IMongoDbMonitorLogRepository mongoDBMonitorLogRepository,
        IMapper mapper)
    {
        _mongoDBMonitorLogRepository = mongoDBMonitorLogRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<MongoDBMonitorLogPaginatedListVm>> Handle(
        GetMongoDBMonitorLogPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _mongoDBMonitorLogRepository.GetPaginatedQuery();

        var productFilterSpec = new MongoDbMonitorLogFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<MongoDBMonitorLogPaginatedListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}