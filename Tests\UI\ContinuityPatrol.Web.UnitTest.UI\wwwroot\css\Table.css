﻿table thead th{background-color:var(--bs-primary-bg-subtle)!important;color:var(--bs-dark)!important;font-weight:var(--bs-Table-title-weight)}.datatable table td,.datatable table th{text-overflow:unset;max-width:unset}.Avatar_Logo{width:23px;height:23px;margin-right:5px;border-radius:50px;color:#fff;text-align:center;line-height:1.5rem;display:inline-block}table.dataTable span.highlight{background-color:#ff8;border-radius:.28571429rem}table.dataTable span.column_highlight{background-color:#fc9;border-radius:.28571429rem}.dataTables_scrollBody{max-height:calc(100vh - 235px);height:calc(100vh - 235px)}.header_filter .dataTables_scrollBody{max-height:calc(100vh - 269px);height:calc(100vh - 269px)}.card_ScrollBody{height:calc(100vh - 128px);overflow-y:auto}.truncate,.truncateDrift{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}table.dataTable tfoot td,table.dataTable tfoot th,table.dataTable thead td,table.dataTable thead th{background-color:var(--bs-primary-bg-subtle);color:var(--bs-dark)}.pagination .next .bi-chevron-right::before,.pagination .previous .bi-chevron-left::before{font-weight:800!important}.SrNo_th{width:51px!important}i.cp-left-arrow.fw-bold.table-pagination-arrow,i.cp-left-arrow.fw-semibold.table-pagination-arrow,i.cp-rignt-arrow.fw-bold.table-pagination-arrow,i.cp-rignt-arrow.fw-semibold.table-pagination-arrow{font-size:10px}.Action-th.sorting{padding-right:9px!important;width:80px!important}div.dataTables_wrapper div.dataTables_paginate ul.pagination{gap:.3rem!important}.dataTables_scrollHeadInner{padding:0!important;width:100%!important}.dataTables_scrollHead table.dataTable{width:100%!important}.customer_logo{height:18px;margin-right:5px;border-radius:50%;border:1px solid #b7b7b7;width:18px;padding:2px}.truncate{max-width:15em}.truncateDrift{max-width:10em}.Category_Icon table{table-layout:fixed}.Category_Icon td{padding:4px;text-align:center}.AccessManager_Table td{padding:11px 0}#tblCompany tr{vertical-align:middle}.pagination-column{display:flex;align-items:center}.License_Tab .pagination li.paginate_button.page-item.active{background-color:inherit;padding:0!important;margin-top:0!important;border:inherit}
.table{
    text-align:start;
} 