﻿namespace ContinuityPatrol.Application.Features.UserActivity.Commands.Create;

public class CreateUserActivityCommandHandler : IRequestHandler<CreateUserActivityCommand, CreateUserActivityResponse>
{
    private readonly IMapper _mapper;
    private readonly IUserActivityRepository _userActivityRepository;

    public CreateUserActivityCommandHandler(IMapper mapper, IUserActivityRepository userActivityRepository)
    {
        _mapper = mapper;
        _userActivityRepository = userActivityRepository;
    }

    public async Task<CreateUserActivityResponse> Handle(CreateUserActivityCommand request,
        CancellationToken cancellationToken)
    {
        var userActivity = _mapper.Map<Domain.Entities.UserActivity>(request);

        userActivity = await _userActivityRepository.AddAsync(userActivity);

        var response = new CreateUserActivityResponse
        {
            Message = Message.Create(nameof(Domain.Entities.UserActivity), userActivity.LoginName),
            Id = userActivity.ReferenceId
        };
        return response;
    }
}