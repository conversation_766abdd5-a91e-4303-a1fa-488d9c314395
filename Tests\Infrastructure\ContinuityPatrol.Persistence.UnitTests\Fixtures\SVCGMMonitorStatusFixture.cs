using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SVCGMMonitorStatusFixture
{
    public SVCGMMonitorStatus CreateSVCGMMonitorStatus(
        string type = "SVCGM_REPLICATION",
        string infraObjectId = "INFRA_001",
        string infraObjectName = "Default SVCGM Object",
        string workflowId = "WF_001",
        string workflowName = "Default Workflow",
        string properties = null,
        string configuredRPO = "15",
        string dataLagValue = "5",
        string threshold = "10",
        bool isActive = true,
        bool isDelete = false)
    {
        return new SVCGMMonitorStatus
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = type,
            InfraObjectId = infraObjectId,
            InfraObjectName = infraObjectName,
            WorkflowId = workflowId,
            WorkflowName = workflowName,
            Properties = properties ?? "{\"rpo\": \"15\", \"status\": \"active\", \"lastSync\": \"2024-01-01T10:00:00Z\"}",
            ConfiguredRPO = configuredRPO,
            DataLagValue = dataLagValue,
            Threshold = threshold,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<SVCGMMonitorStatus> CreateMultipleSVCGMMonitorStatuses(int count, string infraObjectId = "INFRA_001")
    {
        var statuses = new List<SVCGMMonitorStatus>();
        for (int i = 1; i <= count; i++)
        {
            statuses.Add(CreateSVCGMMonitorStatus(
                type: $"SVCGM_TYPE_{i}",
                infraObjectId: $"{infraObjectId}_{i}",
                infraObjectName: $"SVCGM Object {i}",
                workflowId: $"WF_{i:D3}",
                workflowName: $"Workflow {i}",
                configuredRPO: (15 + i).ToString(),
                dataLagValue: i.ToString(),
                threshold: (10 + i).ToString()
            ));
        }
        return statuses;
    }

    public SVCGMMonitorStatus CreateSVCGMMonitorStatusWithSpecificId(string referenceId, string type = "SVCGM_REPLICATION")
    {
        return new SVCGMMonitorStatus
        {
            ReferenceId = referenceId,
            Type = type,
            InfraObjectId = "INFRA_TEST",
            InfraObjectName = "Test SVCGM Object",
            WorkflowId = "WF_TEST",
            WorkflowName = "Test Workflow",
            Properties = "{\"test\": true}",
            ConfiguredRPO = "15",
            DataLagValue = "5",
            Threshold = "10",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public SVCGMMonitorStatus CreateSVCGMMonitorStatusForType(string type, string infraObjectId = "INFRA_001")
    {
        return CreateSVCGMMonitorStatus(
            type: type,
            infraObjectId: infraObjectId,
            infraObjectName: $"SVCGM Object for {type}",
            workflowName: $"Workflow for {type}"
        );
    }

    public List<SVCGMMonitorStatus> CreateSVCGMMonitorStatusesForTypes(List<string> types, string infraObjectId = "INFRA_001")
    {
        var statuses = new List<SVCGMMonitorStatus>();
        foreach (var type in types)
        {
            statuses.Add(CreateSVCGMMonitorStatusForType(type, infraObjectId));
        }
        return statuses;
    }

    public List<SVCGMMonitorStatus> CreateSVCGMMonitorStatusesWithStatus(int activeCount, int inactiveCount, string infraObjectId = "INFRA_001")
    {
        var statuses = new List<SVCGMMonitorStatus>();
        
        for (int i = 1; i <= activeCount; i++)
        {
            statuses.Add(CreateSVCGMMonitorStatus(
                type: $"SVCGM_ACTIVE_{i}",
                infraObjectId: $"{infraObjectId}_{i}",
                infraObjectName: $"Active SVCGM {i}",
                isActive: true
            ));
        }
        
        for (int i = 1; i <= inactiveCount; i++)
        {
            statuses.Add(CreateSVCGMMonitorStatus(
                type: $"SVCGM_INACTIVE_{i}",
                infraObjectId: $"{infraObjectId}_{i + activeCount}",
                infraObjectName: $"Inactive SVCGM {i}",
                isActive: false
            ));
        }
        
        return statuses;
    }

    public SVCGMMonitorStatus CreateSVCGMMonitorStatusForInfraObject(string infraObjectId, string infraObjectName = null)
    {
        return CreateSVCGMMonitorStatus(
            infraObjectId: infraObjectId,
            infraObjectName: infraObjectName ?? $"SVCGM Object for {infraObjectId}",
            workflowName: $"Workflow for {infraObjectId}"
        );
    }

    public List<SVCGMMonitorStatus> CreateSVCGMMonitorStatusesForInfraObjects(List<string> infraObjectIds)
    {
        var statuses = new List<SVCGMMonitorStatus>();
        foreach (var infraObjectId in infraObjectIds)
        {
            statuses.Add(CreateSVCGMMonitorStatusForInfraObject(infraObjectId));
        }
        return statuses;
    }

    public SVCGMMonitorStatus CreateSVCGMMonitorStatusWithProperties(Dictionary<string, object> properties)
    {
        var propertiesJson = System.Text.Json.JsonSerializer.Serialize(properties);
        return CreateSVCGMMonitorStatus(properties: propertiesJson);
    }

    public SVCGMMonitorStatus CreateSVCGMMonitorStatusWithComplexProperties()
    {
        var complexProperties = new Dictionary<string, object>
        {
            {"rpo", "15"},
            {"status", "healthy"},
            {"lastSync", "2024-01-01T10:00:00Z"},
            {"replicationDetails", new Dictionary<string, object>
                {
                    {"sourceDatastore", "DS001"},
                    {"targetDatastore", "DS002"},
                    {"replicationMode", "sync"},
                    {"compressionEnabled", true}
                }
            },
            {"performance", new Dictionary<string, object>
                {
                    {"throughput", "150MB/s"},
                    {"latency", "3ms"},
                    {"errorRate", "0.005%"}
                }
            },
            {"monitoring", new Dictionary<string, object>
                {
                    {"alertsEnabled", true},
                    {"thresholdBreaches", 0},
                    {"lastHealthCheck", "2024-01-01T09:55:00Z"}
                }
            }
        };

        return CreateSVCGMMonitorStatusWithProperties(complexProperties);
    }

    public SVCGMMonitorStatus CreateSVCGMMonitorStatusForWorkflow(string workflowId, string workflowName = null, string infraObjectId = "INFRA_001")
    {
        return CreateSVCGMMonitorStatus(
            infraObjectId: infraObjectId,
            workflowId: workflowId,
            workflowName: workflowName ?? $"Workflow {workflowId}",
            infraObjectName: $"SVCGM Object for {workflowId}"
        );
    }

    public SVCGMMonitorStatus CreateSVCGMMonitorStatusWithRPOSettings(string configuredRPO, string dataLagValue, string threshold)
    {
        return CreateSVCGMMonitorStatus(
            configuredRPO: configuredRPO,
            dataLagValue: dataLagValue,
            threshold: threshold,
            properties: $"{{\"rpo\": \"{configuredRPO}\", \"dataLag\": \"{dataLagValue}\", \"threshold\": \"{threshold}\"}}"
        );
    }

    public List<SVCGMMonitorStatus> CreateSVCGMMonitorStatusesForRPOTesting()
    {
        return new List<SVCGMMonitorStatus>
        {
            CreateSVCGMMonitorStatusWithRPOSettings("15", "5", "10"),
            CreateSVCGMMonitorStatusWithRPOSettings("30", "10", "20"),
            CreateSVCGMMonitorStatusWithRPOSettings("60", "25", "40"),
            CreateSVCGMMonitorStatusWithRPOSettings("120", "50", "80")
        };
    }

    public SVCGMMonitorStatus CreateSVCGMReplicationStatus(string infraObjectId = "INFRA_REPL")
    {
        return CreateSVCGMMonitorStatus(
            type: "SVCGM_REPLICATION",
            infraObjectId: infraObjectId,
            infraObjectName: "SVCGM Replication Object",
            workflowName: "Replication Workflow",
            properties: "{\"type\": \"replication\", \"mode\": \"async\", \"status\": \"running\"}"
        );
    }

    public SVCGMMonitorStatus CreateSVCGMBackupStatus(string infraObjectId = "INFRA_BACKUP")
    {
        return CreateSVCGMMonitorStatus(
            type: "SVCGM_BACKUP",
            infraObjectId: infraObjectId,
            infraObjectName: "SVCGM Backup Object",
            workflowName: "Backup Workflow",
            properties: "{\"type\": \"backup\", \"schedule\": \"daily\", \"status\": \"completed\"}"
        );
    }

    public SVCGMMonitorStatus CreateSVCGMSyncStatus(string infraObjectId = "INFRA_SYNC")
    {
        return CreateSVCGMMonitorStatus(
            type: "SVCGM_SYNC",
            infraObjectId: infraObjectId,
            infraObjectName: "SVCGM Sync Object",
            workflowName: "Sync Workflow",
            properties: "{\"type\": \"sync\", \"frequency\": \"hourly\", \"status\": \"syncing\"}"
        );
    }

    public List<SVCGMMonitorStatus> CreateStandardSVCGMMonitorStatuses()
    {
        return new List<SVCGMMonitorStatus>
        {
            CreateSVCGMReplicationStatus(),
            CreateSVCGMBackupStatus(),
            CreateSVCGMSyncStatus()
        };
    }

    public SVCGMMonitorStatus CreateMinimalSVCGMMonitorStatus()
    {
        return new SVCGMMonitorStatus
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "SVCGM_MINIMAL",
            InfraObjectId = "MINIMAL_INFRA",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };
    }

    public SVCGMMonitorStatus CreateSVCGMMonitorStatusForTesting(
        string testName,
        string type = null,
        string infraObjectId = null)
    {
        return CreateSVCGMMonitorStatus(
            type: type ?? $"SVCGM_{testName.ToUpper()}",
            infraObjectId: infraObjectId ?? $"INFRA_{testName.ToUpper()}",
            infraObjectName: $"Test SVCGM for {testName}",
            workflowName: $"Test Workflow for {testName}"
        );
    }
}
