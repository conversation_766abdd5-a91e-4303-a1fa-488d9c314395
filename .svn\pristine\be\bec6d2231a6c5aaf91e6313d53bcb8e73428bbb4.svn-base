﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class ImpactAvailabilityRepository : BaseRepository<ImpactAvailability>, IImpactAvailabilityRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public ImpactAvailabilityRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<ImpactAvailability>> ListAllAsync()
    {
        var businessServices = base.QueryAll(businessService => businessService.IsActive);

        return _loggedInUserService.IsAllInfra
            ? await businessServices.ToListAsync()
            : AssignedBusinessServices(businessServices);
    }

    public override Task<ImpactAvailability> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilter(imp => imp.ReferenceId.Equals(id)).Result.SingleOrDefault());
    }

    public async Task<ImpactAvailability> GetImpactAvailabilityByBusinessServiceId(string businessServiceId)
    {
        return await
            SelectImpactAvailability(_dbContext.ImpactAvailabilities
                .Active()
                .AsNoTracking())
                .FirstOrDefaultAsync(x => x.BusinessServiceId.Equals(businessServiceId));
    }


    private IQueryable<ImpactAvailability> SelectImpactAvailability(IQueryable<ImpactAvailability> impactAvailabilities)
    {
        return impactAvailabilities.Select(x => new ImpactAvailability
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            BusinessServiceId = x.BusinessServiceId,
            BusinessServiceName = x.BusinessServiceName,
            ServiceUp = x.ServiceUp,
            ServiceDown = x.ServiceDown,
            TotalBusinessFunctionCount = x.TotalBusinessFunctionCount,
            BusinessFunctionUp = x.BusinessFunctionUp,
            BusinessFunctionDown = x.BusinessFunctionDown,
            TotalInfraObjectCount = x.TotalInfraObjectCount,
            InfraObjectUp = x.InfraObjectUp,
            InfraObjectDown = x.InfraObjectDown,
            MajorServiceImpact = x.MajorServiceImpact,
            MinorServiceImpact = x.MinorServiceImpact,
            TotalBusinessFunctionImpact = x.TotalBusinessFunctionImpact,
            BusinessFunctionAvailable = x.BusinessFunctionAvailable,
            TotalInfraObjectImpact = x.TotalInfraObjectImpact,
            InfraObjectAvailable = x.InfraObjectAvailable,
            InfraPartial = x.InfraPartial,
            InfraMajor = x.InfraMajor
        });
    }



    public IReadOnlyList<ImpactAvailability> AssignedBusinessServices(IQueryable<ImpactAvailability> businessServices)
    {
        var services = new List<ImpactAvailability>();

        foreach (var businessService in businessServices)
            if (AssignedEntity.AssignedBusinessServices.Count > 0)
                services.AddRange(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
                    where businessService.BusinessServiceId == assignedBusinessService.Id
                    select businessService);
        return services;
    }
}