﻿using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectDetailById;

public class GetInfraObjectDetailByIdQueryHandler : IRequestHandler<GetInfraObjectDetailByIdQuery, GetInfraObjectDetailByIdVm>
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly IInfraOperationalStatusRepository _infraOperationalStatusRepository;
    private readonly IMapper _mapper;
    private readonly IMonitorServiceRepository _monitorServiceRepository;
    private readonly IServerRepository _serverRepository;
    private readonly ISiteRepository _siteRepository;
    private readonly IInfraObjectViewRepository _infraObjectViewRepository;
    private readonly IServerViewRepository _serverViewRepository;
    private readonly IDatabaseViewRepository _databaseViewRepository;

    public GetInfraObjectDetailByIdQueryHandler(IMapper mapper,
        IServerRepository serverRepository, IDatabaseRepository databaseRepository, IServerViewRepository serverViewRepository,
        IMonitorServiceRepository monitorServiceRepository, ISiteRepository siteRepository, IDatabaseViewRepository databaseViewRepository,
        IInfraOperationalStatusRepository infraOperationalStatusRepository, IInfraObjectViewRepository infraObjectViewRepository)
    {
        _mapper = mapper;
        _serverRepository = serverRepository;
        _databaseRepository = databaseRepository;
        _monitorServiceRepository = monitorServiceRepository;
        _serverViewRepository = serverViewRepository;
        _siteRepository = siteRepository;
        _infraOperationalStatusRepository = infraOperationalStatusRepository;
        _infraObjectViewRepository = infraObjectViewRepository;
        _databaseViewRepository = databaseViewRepository;
    }

    public async Task<GetInfraObjectDetailByIdVm> Handle(GetInfraObjectDetailByIdQuery request, CancellationToken cancellationToken)
    {
        var infraObject = await _infraObjectViewRepository.GetInfraObjectViewByInfraObjectId(request.InfraObjectId);


        Guard.Against.NullOrDeactive(infraObject, nameof(Domain.Entities.InfraObject),
            new NotFoundException(nameof(Domain.Entities.InfraObject), request.InfraObjectId));

        var infraObjectDto = _mapper.Map<GetInfraObjectDetailByIdVm>(infraObject);

        var monitorServices = await _monitorServiceRepository.GetMonitorServiceListByInfraObjectId(request.InfraObjectId);

        infraObjectDto.MonitorServiceDto = _mapper.Map<List<MonitorServiceDto>>(monitorServices);

        var serverIdList = infraObjectDto.MonitorServiceDto.Where(x=>x.ServerId.IsNotNullOrWhiteSpace()).Select(x => x.ServerId).ToList();

        if (serverIdList.Any())
        {
            var serverList = await _serverViewRepository.GetServerTypeByIds(serverIdList);

            infraObjectDto.MonitorServiceDto = infraObjectDto.MonitorServiceDto
                .Join(serverList,
                monitor => monitor.ServerId,
                server => server.ReferenceId,
                (monitor, server) =>
                {
                    monitor.ServerType = server.ServerType;
                    monitor.IpAddress = server.IpAddress;
                    return monitor;
                }).ToList();
        }

        var subTypes = new List<string> { "oracle", "oracle-rac" };

        if (infraObject.SubType.IsNotNullOrWhiteSpace() && subTypes.Contains(infraObject.SubType.ToLower())&&
            infraObject.ReplicationTypeName.ToLower().Equals("native replication-oracle-rac"))
        {
            await ProcessRacPropertiesAsync(infraObjectDto);
        }
        else if (infraObject.SubType.IsNotNullOrWhiteSpace() && infraObject.SubType.ToLower().Equals("mssql-ag"))
        {

            await ProcessMssqlAgServerPropertiesAsync(infraObjectDto);
            await ProcessDatabasePropertiesAsync(infraObjectDto);
        }
        else
        {
            if (infraObjectDto.DROperationStatus == 15)
            {
                var infraOperationalStatus =
                    await _infraOperationalStatusRepository.GetInfraOperationalStatusByInfraId(infraObjectDto
                        .InfraObjectId);

                if (infraOperationalStatus is not null && infraOperationalStatus.Properties.IsNotNullOrWhiteSpace())
                {
                    var serverIds = await ProcessPropertiesAsync(
                        infraObjectDto,
                        infraOperationalStatus.Properties,
                        infraObjectDto.ServerProperties,
                        "ServerId",
                        ProcessServerAsync);

                    var databaseIds = await ProcessPropertiesAsync(
                        infraObjectDto,
                        infraOperationalStatus.Properties,
                        infraObjectDto.DatabaseProperties,
                        "DatabaseId",
                        ProcessDatabaseAsync);

                    await ProcessRemainingPropertiesAsync(infraObjectDto, infraObjectDto.ServerProperties, serverIds,
                        ProcessServerAsync);
                    await ProcessRemainingPropertiesAsync(infraObjectDto, infraObjectDto.DatabaseProperties,
                        databaseIds, ProcessDatabaseAsync);
                }
            }
            else
            {
                await ProcessServerPropertiesAsync(infraObjectDto);
                await ProcessDatabasePropertiesAsync(infraObjectDto);
            }

            await ProcessSrmServersAsync(infraObjectDto);
            await ProcessClusterServersAsync(infraObjectDto);
        }

        return infraObjectDto;
    }

    #region Private Methods
    private async Task ProcessMssqlAgServerPropertiesAsync(GetInfraObjectDetailByIdVm infraObjectDto)
    {
        if (infraObjectDto.ServerProperties.IsNullOrWhiteSpace()) return;

        var serverJson = JObject.Parse(infraObjectDto.ServerProperties);

        var details = (JArray)serverJson.SelectToken("DR.currentPRDetails");

        var prServers = details!.Where(d => d["currentPR"]?.Value<bool>() == true).ToList();

        var drServers = details!.Where(d => d["currentPR"]?.Value<bool>() == false).ToList();


        var prObject = serverJson.SelectToken("PR")!;
        var prCurrentPr = prObject["currentPR"]?.Value<bool>() ?? false;

        (prCurrentPr ? prServers : drServers).Add(prObject);

        var allServers = new JObject
        {
            ["PR"] = JArray.FromObject(prServers),
            ["DR"] = JArray.FromObject(drServers)
        };


        var prServerIds = allServers.SelectToken("PR")!
            .OfType<JObject>()
            .SelectMany(prServer =>
            {
                var ids = prServer["id"]?.ToString();
                return ids?.Split(',').Where(id => !string.IsNullOrWhiteSpace(id)) ?? Enumerable.Empty<string>();
            })
            .Distinct()
            .ToList();

        var prServerDto = await GetServerDto(prServerIds, "PR");

        infraObjectDto.ServerDto.AddRange(prServerDto);

        var drServerIds = allServers.SelectToken("DR")!
                 .OfType<JObject>()
                 .SelectMany(drServer =>
                 {
                     var ids = drServer["id"]?.ToString();
                     return ids?.Split(',').Where(id => !string.IsNullOrWhiteSpace(id)) ?? Enumerable.Empty<string>();
                 })
                 .Distinct()
                 .ToList();

        var drServerDto = await GetServerDto(drServerIds, "DR");

        infraObjectDto.ServerDto.AddRange(drServerDto);
    }


    private async Task<List<ServerDto>> GetServerDto(List<string> serverIds, string type)
    {
        var servers = serverIds.Count > 0
            ? await _serverViewRepository.GetByServerIdsAsync(serverIds)
            : new List<ServerView>();

        var serverDto = _mapper.Map<List<ServerDto>>(servers);

        serverDto.ForEach(x => x.CurrentPr = type.Equals("PR"));

        var siteIds = servers.Where(x => x.SiteId.IsNotNullOrWhiteSpace()).
            Select(x => x.SiteId).ToList();

        var sites = siteIds.Count > 0
            ? await _siteRepository.GetSitesByIds(siteIds)
            : new List<Domain.Entities.Site>();

        var siteIdWithSeverId = sites.ToDictionary(x => x.ReferenceId, x => x.Location);

        serverDto = serverDto
            .Select(ser =>
            {
                ser.Location = ser.SiteId != null && siteIdWithSeverId.TryGetValue(ser.SiteId, out var location)
                    ? location
                    : "NA";
                return ser;
            })
            .ToList();

        return serverDto;
    }


    private async Task ProcessRacPropertiesAsync(GetInfraObjectDetailByIdVm infraObjectDto)
    {
        if (infraObjectDto.DatabaseProperties.IsNullOrWhiteSpace()) return;

        var databaseJson = JObject.Parse(infraObjectDto.DatabaseProperties);
        var databaseIds = GetPropertyNames(databaseJson)
            .SelectMany(serverProps =>
            {
                var id = databaseJson.SelectToken($"{serverProps}.id")?.ToString()
                         ?? databaseJson.SelectToken($"{serverProps}.Id")?.ToString();
                return id.IsNotNullOrWhiteSpace() ? id!.Split(',') : Array.Empty<string>();
            })
            .Where(id => id != null)
            .ToList();

        var databaseDetails = databaseIds.Count > 0
            ? await _databaseViewRepository.GetByDatabaseIdsAsync(databaseIds)
            : new List<DatabaseView>();

        var databaseDto = _mapper.Map<List<DatabaseDto>>(databaseDetails);

        infraObjectDto.DatabaseDto.AddRange(databaseDto);

        var serverIdWithDatabaseId = databaseDetails.Where(x => x.ServerId.IsNotNullOrWhiteSpace())
            .ToDictionary(x => x.ServerId, x => x);

        var serverIds = serverIdWithDatabaseId.Keys.ToList();

        var servers = serverIds.Count > 0
            ? await _serverViewRepository.GetByServerIdsAsync(serverIds)
            : new List<ServerView>();

        var serverDto = _mapper.Map<List<ServerDto>>(servers);

        var siteIds = servers.Where(x => x.SiteId.IsNotNullOrWhiteSpace())
            .Select(x => x.SiteId).ToList();

        var sites = siteIds.Count > 0
            ? await _siteRepository.GetSitesByIds(siteIds)
            : new List<Domain.Entities.Site>();

        var siteIdWithSeverId = sites.ToDictionary(x => x.ReferenceId, x => x.Location);

        serverDto = serverDto
            .Select(ser =>
            {
                ser.NodeName = serverIdWithDatabaseId[ser.ServerId].Name;
                ser.Location = siteIds.Count > 0 ? siteIdWithSeverId[ser.SiteId] : "NA";
                return ser;
            })
            .ToList();

        infraObjectDto.ServerDto.AddRange(serverDto);
    }


    private async Task<HashSet<string>> ProcessPropertiesAsync(GetInfraObjectDetailByIdVm infraObjectDto,
        string operationalProperties,
        string targetProperties,
        string idKey,
        Func<string, string, GetInfraObjectDetailByIdVm, Task> processFunc)
    {
        var processedIds = new HashSet<string>();
        var propertiesJson = JObject.Parse(operationalProperties);
        var propertyNames = GetPropertyNames(propertiesJson);

        foreach (var propertyName in propertyNames)
        {
            var id = propertiesJson.SelectToken($"{propertyName}.{idKey}")?.ToString();
            if (id.IsNotNullOrWhiteSpace())
            {
                await processFunc(id, propertyName, infraObjectDto);
                processedIds.Add(id);
            }
        }

        return processedIds;
    }

    private async Task ProcessRemainingPropertiesAsync(
        GetInfraObjectDetailByIdVm infraObjectDto,
        string propertiesJsonString,
        HashSet<string> excludedIds,
        Func<string, string, GetInfraObjectDetailByIdVm, Task> processFunc)
    {
        var propertiesJson = JObject.Parse(propertiesJsonString);
        foreach (var property in propertiesJson.Properties())
        {
            var id = property.Value["id"]?.ToString();
            if (id.IsNotNullOrWhiteSpace() && !excludedIds.Contains(id))
                await processFunc(id, property.Name, infraObjectDto);
        }
    }

    private async Task ProcessServerAsync(string id, string propertyName, GetInfraObjectDetailByIdVm infraObjectDto)
    {
        var server = await _serverRepository.GetByReferenceIdAsync(id);
        if (server is not null)
        {
            var serverDto = _mapper.Map<ServerDto>(server);
            serverDto.ServerType = infraObjectDto.DROperationStatus == 2 &&
                                   serverDto.ServerType.Trim().ToLower().Contains("dr")
                ? "PR"
                : propertyName;

            var siteDtl = await _siteRepository.GetByReferenceIdAsync(server.SiteId);
            serverDto.Location = siteDtl.Location;

            infraObjectDto.ServerDto.AddRange(serverDto);
        }
    }

    private async Task ProcessDatabaseAsync(string id, string propertyName, GetInfraObjectDetailByIdVm infraObjectDto)
    {
        var database = await _databaseRepository.GetByReferenceIdAsync(id);
        if (database is not null)
        {
            var databaseDto = _mapper.Map<DatabaseDto>(database);
            databaseDto.Type = infraObjectDto.DROperationStatus == 2 && databaseDto.Type.Trim().ToLower().Contains("pr")
                ? "DR"
                : propertyName;

            infraObjectDto.DatabaseDto.AddRange(databaseDto);
        }
    }


    private async Task ProcessDatabasePropertiesAsync(GetInfraObjectDetailByIdVm infraObjectDto)
    {
        if (infraObjectDto.DatabaseProperties.IsNullOrWhiteSpace()) return;

        var databaseJson = JObject.Parse(infraObjectDto.DatabaseProperties);

        var databaseIds = GetPropertyNames(databaseJson)
            .SelectMany(serverProps =>
            {
                var id = databaseJson.SelectToken($"{serverProps}.id")?.ToString()
                         ?? databaseJson.SelectToken($"{serverProps}.Id")?.ToString();
                return id.IsNotNullOrWhiteSpace() ? id!.Split(',') : Array.Empty<string>();
            })
            .Where(id => id != null)
            .ToList();

        var databaseDetails = databaseIds.Count > 0
           ? await _databaseViewRepository.GetByDatabaseIdsAsync(databaseIds)
           : new List<DatabaseView>();

        var databaseDto = _mapper.Map<List<DatabaseDto>>(databaseDetails);

        databaseDto = databaseDto.Select(db =>
        {
            db.Type = infraObjectDto.DROperationStatus == 2
                ? db.Type.Trim().ToLower().Contains("pr") ? "DR" : "PR"
                : db.Type;
            return db;
        }).ToList();

        infraObjectDto.DatabaseDto.AddRange(databaseDto);
    }

    private async Task ProcessServerPropertiesAsync(GetInfraObjectDetailByIdVm infraObjectDto)
    {
        if (infraObjectDto.ServerProperties.IsNullOrWhiteSpace()) return;

        var serverJson = JObject.Parse(infraObjectDto.ServerProperties);
        var serverIds = GetPropertyNames(serverJson)
            .SelectMany(serverProps =>
            {
                var id = serverJson.SelectToken($"{serverProps}.id")?.ToString()
                         ?? serverJson.SelectToken($"{serverProps}.Id")?.ToString();
                return id.IsNotNullOrWhiteSpace() ? id!.Split(',') : Array.Empty<string>();
            })
        .Where(id => id != null)
            .ToList();

        var servers = serverIds.Count > 0
            ? await _serverViewRepository.GetByServerIdsAsync(serverIds)
            : new List<ServerView>();

        var serverDto = _mapper.Map<List<ServerDto>>(servers);

        var siteIds = servers.Where(x => x.SiteId.IsNotNullOrWhiteSpace()).
            Select(x => x.SiteId).ToList();

        var sites = siteIds.Count > 0
            ? await _siteRepository.GetSitesByIds(siteIds)
            : new List<Domain.Entities.Site>();

        var siteIdWithSeverId = sites.ToDictionary(x => x.ReferenceId, x => x.Location);

        serverDto = serverDto
           .Select(ser =>
           {
               ser.Location = ser.SiteId != null && siteIdWithSeverId.TryGetValue(ser.SiteId, out var location)
                   ? location
                   : "NA";
               return ser;
           })
           .ToList();

        infraObjectDto.ServerDto.AddRange(serverDto);
    }

    private async Task ProcessSrmServersAsync(GetInfraObjectDetailByIdVm infraObjectDto)
    {
        if (infraObjectDto.ServerProperties.IsNullOrWhiteSpace()) return;

        var serverJson = JObject.Parse(infraObjectDto.ServerProperties);

        var srmServerIds = serverJson.SelectToken("SRMServer") is JArray srmServerList
             ? srmServerList
                 .Select(srm => srm["id"]?.ToString())
                 .Where(id => id.IsNotNullOrWhiteSpace())
        .ToList()
        : new List<string>();

        var servers = srmServerIds.Count > 0
          ? await _serverViewRepository.GetByServerIdsAsync(srmServerIds)
          : new List<ServerView>();

        var serverDto = _mapper.Map<List<ServerDto>>(servers)
            .Select(s => { s.Type = "SRMServer"; return s; })
            .ToList();

        var siteIds = servers.Where(x => x.SiteId.IsNotNullOrWhiteSpace())
            .Select(x => x.SiteId).ToList();

        var sites = siteIds.Count > 0
            ? await _siteRepository.GetSitesByIds(siteIds)
            : new List<Domain.Entities.Site>();

        var siteIdWithSeverId = sites.ToDictionary(x => x.ReferenceId, x => x.Location);

        serverDto = serverDto
            .Select(ser =>
            {
                ser.Location = ser.SiteId != null && siteIdWithSeverId.TryGetValue(ser.SiteId, out var location)
                    ? location
                    : "NA";
                return ser;
            })
            .ToList();

        infraObjectDto.ServerDto.AddRange(serverDto);
    }

    private async Task ProcessClusterServersAsync(GetInfraObjectDetailByIdVm infraObjectDto)
    {
        if (infraObjectDto.ServerProperties.IsNullOrWhiteSpace()) return;

        var serverJson = JObject.Parse(infraObjectDto.ServerProperties);

        var isCluster = serverJson.SelectToken("$.isCluster")?.ToString();

        if (isCluster.IsNotNullOrEmpty() && isCluster!.ToLower() == "true")
        {
            var clusters = serverJson.SelectToken("$.clusters") as JArray;

            if (clusters != null && clusters.Count > 0)
            {

                var clusterIds = clusters
                    .Select(cluster => cluster["id"]?.ToString())
                    .Where(id => !string.IsNullOrWhiteSpace(id))
                    .ToList();


                var servers = clusterIds.Any()
                    ? await _serverViewRepository.GetByServerIdsAsync(clusterIds)
                    : new List<ServerView>();


                var serverDto = _mapper.Map<List<ServerDto>>(servers)
                    .Select(c =>
                    {
                        c.Type = serverJson.SelectToken("$.clusterType")?.ToString();
                        return c;
                    })
                    .ToList();

                infraObjectDto.ServerDto.AddRange(serverDto);
            }

        }
    }

    private static List<string> GetPropertyNames(JObject json)
    {
        var propertyNames = new List<string>();
        foreach (var property in json.Properties()) propertyNames.Add(property.Name);
        return propertyNames;
    }

    #endregion
}