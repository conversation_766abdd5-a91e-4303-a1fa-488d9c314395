﻿using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Application.Features.Report.Queries.BusinessServiceSummaryReport;

namespace ContinuityPatrol.Application.UnitTests.Features.Report.Queries
{
    public class GetBusinessServiceSummaryReportQueryHandlerTests
    {
        private readonly Mock<IDashboardViewRepository> _mockDashboardViewRepository;
        private readonly Mock<IHeatMapStatusViewRepository> _mockHeatMapStatusViewRepository;
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly GetBusinessServiceSummaryReportQueryHandler _handler;

        public GetBusinessServiceSummaryReportQueryHandlerTests()
        {
            _mockDashboardViewRepository = new Mock<IDashboardViewRepository>();
            _mockHeatMapStatusViewRepository = new Mock<IHeatMapStatusViewRepository>();
            _mockLoggedInUserService = new Mock<ILoggedInUserService>();
            _mockMapper = new Mock<IMapper>();
            _mockPublisher = new Mock<IPublisher>();

            _handler = new GetBusinessServiceSummaryReportQueryHandler(
                _mockMapper.Object,
                _mockDashboardViewRepository.Object,
                _mockHeatMapStatusViewRepository.Object,
                _mockLoggedInUserService.Object,
                _mockPublisher.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldReturnBusinessServiceSummaryReport_WhenCalled()
        {
            var query = new GetBusinessServiceSummaryReportQuery();
            var dashboardViews = new List<Domain.Entities.DashboardView>
            {
                new Domain.Entities.DashboardView
                {
                    BusinessServiceId = "Service1",
                    BusinessFunctionId = "Function1",
                    InfraObjectId = "Infra1",
                    State = "Normal"
                },
                new Domain.Entities.DashboardView
                {
                    BusinessServiceId = "Service1",
                    BusinessFunctionId = "Function1",
                    InfraObjectId = "Infra2",
                    State = "Maintenance"
                }
            };

            var heatMapStatuses = new List<Domain.Entities.HeatMapStatus>
            {
                new Domain.Entities.HeatMapStatus { InfraObjectId = "Infra1", IsAffected = false },
                new Domain.Entities.HeatMapStatus { InfraObjectId = "Infra2", IsAffected = true }
            };

            _mockDashboardViewRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(dashboardViews);

            _mockDashboardViewRepository.Setup(repo => repo.GetBusinessViewListByBusinessServiceId("Service1"))
                .ReturnsAsync(dashboardViews);

            _mockDashboardViewRepository.Setup(repo => repo.GetBusinessViewListByBusinessFunctionId("Function1"))
                .ReturnsAsync(dashboardViews);

            //_mockHeatMapStatusRepository.Setup(repo => repo.GetHeatMapByInfraObjectId("Infra1"))
            //    .ReturnsAsync(heatMapStatuses.Where(h => h.InfraObjectId == "Infra1").ToList());

            //_mockHeatMapStatusRepository.Setup(repo => repo.GetHeatMapByInfraObjectId("Infra2"))
            //    .ReturnsAsync(heatMapStatuses.Where(h => h.InfraObjectId == "Infra2").ToList());

            _mockMapper.Setup(mapper => mapper.Map<List<BusinessServiceSummaryReportVm>>(It.IsAny<List<Domain.Entities.DashboardView>>()))
                .Returns(new List<BusinessServiceSummaryReportVm>
                {
                new BusinessServiceSummaryReportVm
                {
                    BusinessServiceId = "Service1",
                    Up = 0,
                    Down = 0,
                    Maintenance = 0,
                    Health = "Not Affected"
                }
                });

            _mockLoggedInUserService.Setup(service => service.LoginName).Returns("TestUser");

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("TestUser", result.ReportGeneratedBy);
            Assert.Equal(1, result.BusinessServiceSummaryReportVms.Count);
            Assert.Equal("Service1", result.BusinessServiceSummaryReportVms[0].BusinessServiceId);
            Assert.Equal(1, result.BusinessServiceSummaryReportVms[0].Down);
            Assert.Equal(1, result.BusinessServiceSummaryReportVms[0].Maintenance);
            Assert.Equal("Affected", result.BusinessServiceSummaryReportVms[0].Health);

            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<ReportViewedEvent>(), CancellationToken.None), Times.Once);
        }
    }
}
