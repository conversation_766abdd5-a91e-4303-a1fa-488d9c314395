﻿namespace ContinuityPatrol.Application.Features.ImpactAvailability.Commands.Delete;

public class
    DeleteImpactAvailabilityCommandHandler : IRequestHandler<DeleteImpactAvailabilityCommand,
        DeleteImpactAvailabilityResponse>
{
    private readonly IImpactAvailabilityRepository _impactAvailabilityRepository;

    public DeleteImpactAvailabilityCommandHandler(IImpactAvailabilityRepository impactAvailabilityRepository)
    {
        _impactAvailabilityRepository = impactAvailabilityRepository;
    }

    public async Task<DeleteImpactAvailabilityResponse> Handle(DeleteImpactAvailabilityCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _impactAvailabilityRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(eventToDelete, nameof(Domain.Entities.ImpactAvailability),
            new NotFoundException(nameof(Domain.Entities.ImpactAvailability), request.Id));

        eventToDelete.IsActive = false;

        await _impactAvailabilityRepository.UpdateAsync(eventToDelete);

        var response = new DeleteImpactAvailabilityResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.ImpactAvailability), eventToDelete.ReferenceId),

            IsActive = eventToDelete.IsActive
        };

        return response;
    }
}