﻿namespace ContinuityPatrol.Application.Features.DataSyncJob.Commands.Create;

public class CreateDataSyncJobCommand : IRequest<CreateDataSyncJobResponse>
{
    public string ReplicationId { get; set; }
    public string ReplicationName { get; set; }
    public string DataSyncOptionId { get; set; }
    public string ReplicationTypeId { get; set; }
    public string ReplicationType { get; set; }
    public string SiteId { get; set; }
    public string SiteName { get; set; }
    public string Properties { get; set; }
    public string JobProperties { get; set; }
    public string ScheduleProperties { get; set; }
    public string SourceDirectory { get; set; }
    public string DestinationDirectory { get; set; }
    public string ModeType { get; set; }
    public string LastSuccessfullReplTime { get; set; }

}