﻿using ContinuityPatrol.Application.Features.DashboardViewLog.Commands.Create;
using ContinuityPatrol.Application.Features.DashboardViewLog.Commands.Update;
using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.DashboardViewLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class DashboardViewLogProfile : Profile
{
    public DashboardViewLogProfile()
    {
        CreateMap<DashboardViewLog, CreateDashboardViewLogCommand>().ReverseMap();
        CreateMap<UpdateDashboardViewLogCommand, DashboardViewLog>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<DashboardViewLog, DashboardViewLogDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DashboardViewLog, DashboardViewLogListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<DashboardViewLog, DashboardViewLogByInfraObjectId>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<PaginatedResult<DashboardViewLog>, PaginatedResult<DashboardViewLogListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}