using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class RiskMitigationFixture : IDisposable
{
    public List<RiskMitigation> RiskMitigationPaginationList { get; set; }
    public List<RiskMitigation> RiskMitigationList { get; set; }
    public RiskMitigation RiskMitigationDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public RiskMitigationFixture()
    {
        var fixture = new Fixture();

        RiskMitigationList = fixture.Create<List<RiskMitigation>>();

        RiskMitigationPaginationList = fixture.CreateMany<RiskMitigation>(20).ToList();

        RiskMitigationDto = fixture.Create<RiskMitigation>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
