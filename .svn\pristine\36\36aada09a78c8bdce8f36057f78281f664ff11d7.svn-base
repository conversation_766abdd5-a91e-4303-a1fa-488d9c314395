﻿using ContinuityPatrol.Application.Features.StateMonitorLog.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.StateMonitorLog.Commands;

public class DeleteStateMonitorLogTests : IClassFixture<StateMonitorLogFixture>
{
    private readonly StateMonitorLogFixture _stateMonitorLogFixture;

    private Mock<IStateMonitorLogRepository> _mockStateMonitorLogRepository;

    private DeleteStateMonitorLogCommandHandler _handler;

    public DeleteStateMonitorLogTests(StateMonitorLogFixture stateMonitorLogFixture)
    {
        _stateMonitorLogFixture = stateMonitorLogFixture;

        _mockStateMonitorLogRepository = new Mock<IStateMonitorLogRepository>();

        _mockStateMonitorLogRepository = StateMonitorLogRepositoryMocks.DeleteStateMonitorLogRepository(_stateMonitorLogFixture.StateMonitorLogs);

        _handler = new DeleteStateMonitorLogCommandHandler(_mockStateMonitorLogRepository.Object);

        _stateMonitorLogFixture.StateMonitorLogs[0].IsActive = true;
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_StateMonitorLogDeleted()
    {
        var validGuid = _stateMonitorLogFixture.StateMonitorLogs[0].ReferenceId;

        _stateMonitorLogFixture.StateMonitorLogs[0].ReferenceId = validGuid;

        _mockStateMonitorLogRepository
            .Setup(repo => repo.GetByReferenceIdAsync(validGuid))
            .ReturnsAsync(_stateMonitorLogFixture.StateMonitorLogs[0]);

        var result = await _handler.Handle(new DeleteStateMonitorLogCommand { Id = validGuid }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();

        _mockStateMonitorLogRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.StateMonitorLog>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_DeleteStateMonitorLogResponse_When_StateMonitorLogDeleted()
    {
        var validGuid = _stateMonitorLogFixture.StateMonitorLogs[0].ReferenceId;

        _stateMonitorLogFixture.StateMonitorLogs[0].ReferenceId = validGuid;

        var result = await _handler.Handle(new DeleteStateMonitorLogCommand { Id = validGuid }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteStateMonitorLogResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);

        _mockStateMonitorLogRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.StateMonitorLog>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_StateMonitorLogDeleted()
    {
        var validGuid = _stateMonitorLogFixture.StateMonitorLogs[0].ReferenceId;

        _stateMonitorLogFixture.StateMonitorLogs[0].ReferenceId = validGuid;

        await _handler.Handle(new DeleteStateMonitorLogCommand { Id = validGuid }, CancellationToken.None);

        var stateMonitorLog = await _mockStateMonitorLogRepository.Object.GetByReferenceIdAsync(_stateMonitorLogFixture.StateMonitorLogs[0].ReferenceId);

        stateMonitorLog.IsActive.ShouldBeFalse();

        _mockStateMonitorLogRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.StateMonitorLog>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidStateMonitorLogId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteStateMonitorLogCommand { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteStateMonitorLogCommand { Id = _stateMonitorLogFixture.StateMonitorLogs[0].ReferenceId }, CancellationToken.None);

        _mockStateMonitorLogRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockStateMonitorLogRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.StateMonitorLog>()), Times.Once);
    }
}