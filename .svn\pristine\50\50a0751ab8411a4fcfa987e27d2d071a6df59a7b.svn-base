﻿namespace ContinuityPatrol.Application.Features.UserRole.Queries.GetNames;

public class GetUserRoleNamesQueryHandler:IRequestHandler<GetUserRoleNamesQuery, List<UserRoleNamesVm>>
{
    private readonly IMapper _mapper;
    private readonly IUserRoleRepository _userRoleRepository;

    public GetUserRoleNamesQueryHandler(IMapper mapper, IUserRoleRepository userRoleRepository)
    {
        _mapper = mapper;
        _userRoleRepository = userRoleRepository;
    }

    public async Task<List<UserRoleNamesVm>> Handle(GetUserRoleNamesQuery request, CancellationToken cancellationToken)
    {
        var userRoles = await _userRoleRepository.GetRoles();

        return userRoles;
    }
}   

