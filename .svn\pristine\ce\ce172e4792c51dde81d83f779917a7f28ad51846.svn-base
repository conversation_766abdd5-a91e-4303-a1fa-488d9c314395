﻿global using AutoMapper;
global using ContinuityPatrol.Application.Contracts.Persistence;
global using ContinuityPatrol.Domain.Entities;
global using ContinuityPatrol.Shared.Core.Exceptions;
global using ContinuityPatrol.Shared.Core.Extensions;
global using ContinuityPatrol.Shared.Core.Helper;
global using FluentValidation;
global using MediatR;
global using Microsoft.AspNetCore.SignalR;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Logging;
global using Newtonsoft.Json.Linq;
global using System.ComponentModel.DataAnnotations;
global using System.Reflection;
global using System.Text.RegularExpressions;
global using System.Globalization;
global using Microsoft.Extensions.Caching.Memory;
global using Microsoft.Data.SqlClient;
global using MySqlConnector;
global using Npgsql;
global using Oracle.ManagedDataAccess.Client;
global using System.Data.Common;
global using System.Text;
global using Microsoft.AspNetCore.Mvc.Rendering;
global using System.Security.Claims;
global using Microsoft.AspNetCore.Http;
global using Quartz;