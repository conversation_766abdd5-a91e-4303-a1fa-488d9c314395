using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.Report.Queries.DriftReport;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class DriftEventRepository : BaseRepository<DriftEvent>, IDriftEventRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public DriftEventRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<DriftEvent>> GetInfraObjectIdByStatus(string startDate, string endDate, string infraObjectId)
    {
        var result = await _dbContext.DriftEvents.Active()
            .Where(x => x.CreatedDate.Date >= startDate.ToDateTime() &&
                x.CreatedDate.Date <= endDate.ToDateTime() &&
                (x.InfraObjectId.Equals(infraObjectId) || infraObjectId.ToLower().Equals("all")))
            .ToListAsync();
        return result;
    }

    public async Task<List<DriftEvent>> GetStartDateAndEndDate(string startDate, string endDate)
    {
        var result = await _dbContext.DriftEvents.Active()
            .Where(x => x.CreatedDate.Date >= startDate.ToDateTime() &&
                        x.CreatedDate.Date <= endDate.ToDateTime()).ToListAsync();
        return result;
    }

    public async Task<List<DriftEvent>> GetStartDateEndDateAndInfraObjectIdAndStatus(string startDate, string endDate, string infraObjectId, string statusId)
    {
        var result = await _dbContext.DriftEvents.Active()
            .Where(x => x.CreatedDate.Date >= startDate.ToDateTime() &&
                        x.CreatedDate.Date <= endDate.ToDateTime() &&
                        (x.InfraObjectId.Equals(infraObjectId) || infraObjectId.ToLower().Equals("all")) &&
                        (x.EntityStatus.Equals(statusId) || statusId.ToLower().Equals("all")))
            .ToListAsync();
        return result;
    }

    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
                ? Entities.Any(e => e.EntityName.Equals(name))
                : Entities.Where(e => e.EntityName.Equals(name)).ToList().Unique(id));
    }
}