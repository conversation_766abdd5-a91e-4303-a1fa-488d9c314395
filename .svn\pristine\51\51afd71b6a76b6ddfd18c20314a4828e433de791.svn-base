
//Server Deployment table starts here.
let deploymentsReplicaNameTD = (value, i = 0, arraylength = 0) =>
    `<div class="form-group">
       <div class="">
         <input value="${value}" ${value ? (i < arraylength - 1 ? "disabled" : "") : ""} class="form-control deploymentsReplicaName" placeholder="Deployments ReplicaSet Name" type="text" name="DeploymentsReplicaSetName"/>
       </div>
       <span class="replicaNamevalidation-error"></span>
    </div>`;

let deploymentsNameTD = (value, i = 0, arraylength = 0) =>
    `<div class="form-group">
        <div class="">
           <input value="${value}" ${value ? (i < arraylength - 1 ? "disabled" : "") : ""} class="form-control deploymentsName" placeholder="Deployments Name" type="text" name="DeploymentsName" />
        </div>
        <span class="namevalidation-error"></span>
    </div>`;

function deploymentTableWhileCreate(id) {
    const $table = document.querySelector(`#f-${id}`);
    //let tableField = $($table).find('.custom-table');
    if ($table.children.length == 0) {
        let $tHead = $(' <thead> </thead>')
        let $headerRow = $('<tr class=".deployment_row" id="deploymentTable"></tr>');
        $headerRow.append('<th>Deployments ReplicaSet Name</th>');
        $headerRow.append('<th>Deployments Name</th>');
        $headerRow.append('<th>Action</th>');

        $tHead.append($headerRow[0])
        $table.append($tHead[0]);

        let $tBody = $('<tbody></tbody>');
        let $dataRow = $('<tr class="deploymentRow"></tr>');
        $dataRow.append(`<td>${deploymentsReplicaNameTD('')}</td>`);
        $dataRow.append(`<td>${deploymentsNameTD('')}</td>`);

        let $actionCell = $('<td style="height:64px" class="d-flex align-items-center justify-content-center"> </td>');
        $actionCell.append(`<span role="button" title="Add"  class="deployAddButton" onclick="event.preventDefault(); handleAddDeployment('${id}', this.parentElement.parentElement, event)"> <i  class="cp-add"></i></span>`);
        $actionCell.append(`<span role="button" title="Edit"  style="display: none;"  class="deployEditButton"  onclick="event.preventDefault(); handleEditDeployment('${id}', this.parentElement.parentElement, event)"> <i  class="cp-edit"></i></span>`);
        $actionCell.append(`<span class="deployUpdateButton" style="display: none;" role="button" onclick="handleUpdateDeploy('${id}',this.parentElement.parentElement, event)" title="Update" class="updateButton"><i class="cp-up-linearrow"></i></span>`);
        $actionCell.append(`<span role="button" title="Delete"  style="display: none;"  class="deployDeleteButton" onclick="event.preventDefault(); handleDelete(event)"> <i  class="cp-Delete"></i></span>`);

        $dataRow.append($actionCell);

        $tBody.append($dataRow[0])
        $table.append($tBody[0]);

        const $wrapper = document.createElement('div');
        $wrapper.classList.add('table-container');
        $table.parentNode.insertBefore($wrapper, $table);
        $wrapper.appendChild($table);

        //When create there is a space between table and other inputs.
        //$wrapper.style.height = '300px';
        //$wrapper.style.overflow = 'auto';
    }
}

function deploymentTableWhileEdit(formData) {
    let tableIDCollection = document.getElementsByClassName('deployments-table');
    let tableID = tableIDCollection[0];
    let $tableId

    if (tableID) {
        $tableId = $(tableID).attr('id');
        //let $tableId = $(element).find('table').attr('id');
        let $table = document.querySelector(`#${$tableId}`)
        let $tHead = $('<thead></thead>')
        let $headerRow = $('<tr class=".deployment_row" id="deploymentTable"></tr>');
        $headerRow.append('<th>Deployments ReplicaSet Name</th>');
        $headerRow.append('<th>Deployments Name</th>');
        $headerRow.append('<th>Action</th>');
        $tHead.append($headerRow[0])
        $table.append($tHead[0]);
        let arraylength = formData.ConfigureDeployments.length;
        let $tBody = $('<tbody></tbody>')
        for (let i = 0; i < arraylength; i++) {
            let $dataRow = $('<tr></tr>');
            $dataRow.append(`<td>${deploymentsReplicaNameTD(formData.ConfigureDeployments[i].DeploymentsReplicaSetName, i, arraylength)}</td>`);
            $dataRow.append(`<td>${deploymentsNameTD(formData.ConfigureDeployments[i].DeploymentsName, i, arraylength)}</td>`);

            let $actionCell = $('<td style="height:64px" class="d-flex align-items-center justify-content-center"> </td>');
            $actionCell.append(`<span role="button" title="Add" style="display: ${arraylength - 1 === i ? '' : 'none'};" class="deployAddButton" onclick="event.preventDefault(); handleAddDeployment('${$tableId}', this.parentElement.parentElement, event)"> <i  class="cp-add"></i></span>`);
            $actionCell.append(`<span role="button" title="Edit" style="display: ${arraylength - 1 !== i ? '' : 'none'};" class="deployEditButton" onclick="event.preventDefault(); handleEditDeployment('${$tableId}', this.parentElement.parentElement, event)"> <i  class="cp-edit"></i></span>`);
            $actionCell.append(`<span class="deployUpdateButton" style="display: none;" role="button" onclick="handleUpdateDeploy('${$tableId}',this.parentElement.parentElement, event)" title="Update" class="updateButton"><i class="cp-up-linearrow"></i></span>`);
            $actionCell.append(`<span role="button" title="Delete" style="display: ${arraylength - 1 !== i ? '' : 'none'};" class="deployDeleteButton" onclick="event.preventDefault(); handleDelete(event)"> <i  class="cp-Delete"></i></span>`);

            $dataRow.append($actionCell);

            $tBody.append($dataRow[0])
            $table.append($tBody[0]);
        }
        const $wrapper = document.createElement('div');
        $wrapper.classList.add('table-container');
        $table.parentNode.insertBefore($wrapper, $table);
        $wrapper.appendChild($table);

        //When edit there is a space between table and other inputs.
        //$wrapper.style.height = '300px';
        //$wrapper.style.overflow = 'auto';
    }
}

async function tableFieldDeploymentsValidation(requiredInputs, actiontype = null) {
    const validationResults = await Promise.all(requiredInputs.map(async function (index, _) {
        if (actiontype === "save") {
            if (index === requiredInputs.length - 1 && requiredInputs.length !== 1) {
                return true;
            }
        }
        let $this = $(this);
        if ($this.is(':visible')) {
            let associatedLabel = $this.attr('placeholder');
            let toLowerCase = associatedLabel.toLowerCase()
            let $deploy1 = $this.closest('.form-group').find('.replicaNamevalidation-error');
            let $deploy2 = $this.closest('.form-group').find('.namevalidation-error');
            let inputValue2 = $this?.val();

            $deploy1.text("").removeClass("field-validation-error");
            $deploy2.text("").removeClass("field-validation-error");

            if (inputValue2 === "") {
                if (associatedLabel.toLowerCase() === "deployments name") {
                    $deploy2.text("Enter " + toLowerCase).addClass("field-validation-error");
                }
                if (associatedLabel.toLowerCase() === "deployments replicaset name") {
                    $deploy1.text("Enter " + toLowerCase).addClass("field-validation-error");
                }
                return false;
            }
            else {
                return true;
            }
        } else {
            return true
        }
    }));

    return validationResults.every(result => result);
}

$(document).on('input', '.deploymentsReplicaName', function () {
    tableFieldDeploymentsValidation($('.deploymentsReplicaName'));
});

$(document).on('input', '.deploymentsName', function () {
    tableFieldDeploymentsValidation($('.deploymentsName'));
});

async function deploymentsReplicaSetName(actiontype = null) {
    let replicaSetName = $("input[name='DeploymentsReplicaSetName']");
    if (replicaSetName?.length > 0) {
        return await tableFieldDeploymentsValidation(replicaSetName, actiontype);
    }
    return true;
}

async function deploymentsName(actiontype = null) {
    let deploymentsName = $("input[name='DeploymentsName']");
    if (deploymentsName?.length > 0) {
        return await tableFieldDeploymentsValidation(deploymentsName, actiontype);
    }
    return true;
}

async function handleAddDeployment(id, newRow, event) {
    event.preventDefault();
    let replicaName = await deploymentsReplicaSetName("add");
    let name = await deploymentsName("add");
    if (replicaName && name) {
        const table = document.querySelector(`#f-${id}`) ? document.querySelector(`#f-${id}`) : document.querySelector(`#${id}`);
        const clonedRow = newRow.cloneNode(true);
        const rows = document.querySelectorAll('.deploymentRow');
        const deployAddButton = newRow.querySelector(".deployAddButton");
        const deployEditButton = newRow.querySelector(".deployEditButton");
        const deployUpdateButton = newRow.querySelector(".deployUpdateButton");
        const deployDeleteButton = newRow.querySelector(".deployDeleteButton");

        deployAddButton.style.display = "none";
        deployEditButton.style.display = "";
        deployDeleteButton.style.display = "";

        const inputElements = clonedRow.querySelectorAll('input');
        const selectElements = clonedRow.querySelectorAll('select');
        inputElements.forEach(input => { input.value = ''; });
        selectElements.forEach(select => { select.value = ''; });

        newRow.querySelector(".deploymentsReplicaName").setAttribute("disabled", true);
        newRow.querySelector(".deploymentsName").setAttribute("disabled", true);

        table.appendChild(clonedRow);
    }
}

function handleEditDeployment(id, newRow, event) {
    newRow.querySelector(".deploymentsReplicaName").removeAttribute("disabled");
    newRow.querySelector(".deploymentsName").removeAttribute("disabled");

    newRow.querySelector('.deploymentsReplicaName').focus();

    const cpEditElement = newRow.querySelector(".deployEditButton");
    const deployUpdateButton = newRow.querySelector(".deployUpdateButton");

    if (cpEditElement) {
        cpEditElement.style.display = "none";
        deployUpdateButton.style.display = "";
    }
}

function handleUpdateDeploy(id, newRow, event) {
    newRow.querySelector(".deploymentsReplicaName").setAttribute("disabled", true);
    newRow.querySelector(".deploymentsName").setAttribute("disabled", true);
    const cpEditElement = newRow.querySelector(".deployEditButton");
    const deployUpdateButton = newRow.querySelector(".deployUpdateButton");

    if (cpEditElement) {
        cpEditElement.style.display = "";
        deployUpdateButton.style.display = "none";
    }
}
//Server Deployment table ends here.


//Server Sudosu table starts here.
function sudosuTableWhileCreate(id) {
    const $table = document.querySelector(`#f-${id}`);

    if ($table?.children?.length == 0) {
        let $tHead = $(' <thead> </thead>')
        let $headerRow = $('<tr class=".header_row" id="substituteAuthentication"></tr>');
        $headerRow.append('<th>Authenticate Type</th>');
        $headerRow.append('<th>Path</th>');
        $headerRow.append('<th>User</th>');
        $headerRow.append('<th>Password</th>');
        $headerRow.append('<th>Action</th>');

        $tHead.append($headerRow[0])
        $table.append($tHead[0]);

        let $tBody = $('<tbody></tbody>');
        let $dataRow = $('<tr></tr>');
        $dataRow.append('<td><select class="substituteAuthenticationType" data-placeholder="Select Auth Type" name="SubstituteAuthenticationType"><option value="" hidden>Select Auth Type</option><option value="sudo su">sudo su</option><option value="su">su</option><option value="asu">asu</option><option value="sudo">sudo</option><option value="privrun">privrun</option><option value="IsPasswordless">ispasswordless</option><option value="other">other</option></select></td>');
        $dataRow.append('<td><input class="substitutePath" placeholder="Enter Path" autocomplete="off" type="text" name="SubstituteAuthenticationPath"/></td>');
        $dataRow.append('<td><input class="substituteUserName" autocomplete="off" placeholder="Enter User" type="text" name="SubstituteAuthenticationUser" /> </td>');
        $dataRow.append('<td><input class="substitutePassword" placeholder="Enter Password" type="password" name="SubstituteAuthenticationPassword" /></td>');

        let $actionCell = $('<td style="height:62px" class="d-flex align-items-center justify-content-center"> </td>');
        $actionCell.append(`<span role="button" title="Add" class="sudosuAddButton" onclick="event.preventDefault(); handleAddSudosu('${id}', this.parentElement.parentElement, event)"> <i  class="cp-add "></i></span>`);

        $actionCell.append(`<span role="button" title="Edit"  style="display: none;"  class="sudosuEditButton"  onclick="event.preventDefault(); handleEditSudosu('${id}', this.parentElement.parentElement, event)"> <i  class="cp-edit"></i></span>`);
        $actionCell.append(`<span class="sudosuUpdateButton" style="display: none;" role="button" onclick="handleUpdateSudosu('${id}',this.parentElement.parentElement, event)" title="Update" class="updateButton"><i class="cp-up-linearrow"></i></span>`);
        $actionCell.append(`<span role="button" title="Delete"  style="display: none;"  class="sudosuDeleteButton" onclick="event.preventDefault(); handleDelete(event)"> <i  class="cp-Delete"></i></span>`);

        $dataRow.append($actionCell);
        $tBody.append($dataRow[0])
        $table.append($tBody[0]);
    }
}

async function sudosuTableWhileEdit(formData) {
    let tableIDCollection = document.getElementsByClassName('custom-table');
    let tableID = tableIDCollection[0];
    let $tableId
    if (tableID) {
        $tableId = $(tableID).attr('id');
        //let $tableId = $(element).find('table').attr('id');
        let $table = document.querySelector(`#${$tableId}`)
        let $tHead = $('<thead></thead>')
        let $headerRow = $('<tr class=".header_row" id="substituteAuthentication"></tr>');
        $headerRow.append('<th>Authenticate Type</th>');
        $headerRow.append('<th>Path</th>');
        $headerRow.append('<th>User</th>');
        $headerRow.append('<th>Password</th>');
        $headerRow.append('<th>Action</th>');
        $tHead.append($headerRow[0])
        $table.append($tHead[0]);

        let $tBody = $('<tbody></tbody>')
        for (let i = 0; i < formData.ConfigureSubstituteAuthentication.length; i++) {
            let $dataRow = $('<tr></tr>');
            let res = '';

            $dataRow.append(`<td><select disabled class="substituteAuthenticationType" data-placeholder="Select Auth Type" name="SubstituteAuthenticationType">
                        <option value="" selected hidden>Select Auth Type</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'sudo su' ? 'selected' : ''}>sudo su</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'su' ? 'selected' : ''}>su</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'asu' ? 'selected' : ''}>asu</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'sudo' ? 'selected' : ''}>sudo</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'privrun' ? 'selected' : ''}>privrun</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'IsPasswordless' ? 'selected' : ''}>ispasswordless</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'other' ? 'selected' : ''}>other</option>
                        </select></td>`);

            if (formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationPassword !== '') {
                await $.ajax({
                    type: "POST",
                    url: RootUrl + 'Configuration/Server/ServerDataDecrypt',
                    data: { data: formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationPassword, __RequestVerificationToken: gettoken() },
                    dataType: 'text',
                    success: function (decryptedValue) {
                        res = decryptedValue;
                    }
                });
            }

            $dataRow.append(`<td><input class="substitutePath"  disabled placeholder="Enter Path" autocomplete="off" name="SubstituteAuthenticationPath" value="${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationPath}" type="text"/></td>`);
            $dataRow.append(`<td><input class="substituteUserName" disabled autocomplete="off" placeholder="Enter User" name="SubstituteAuthenticationUser" value="${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationUser}" type="text" /> </td>`);
            $dataRow.append(`<td><input class="substitutePassword" disabled placeholder="Enter Password" name="SubstituteAuthenticationPassword" value="${res}" type="password" /></td>`);

            let $actionCell = $('<td style="height:62px" class="d-flex align-items-center justify-content-center"></td>');

            $actionCell.append(`<span role="button" title="Add" style="display: ${formData.ConfigureSubstituteAuthentication.length - 1 === i ? '' : 'none'};" class="sudosuAddButton" onclick="event.preventDefault(); handleAddSudosu('${$tableId}', this.parentElement.parentElement, event)"> <i  class="cp-add "></i></span>`);

            $actionCell.append(`<span role="button" title="Edit"  style="display: ${formData.ConfigureSubstituteAuthentication.length - 1 !== i ? '' : 'none'};"  class="sudosuEditButton"  onclick="event.preventDefault(); handleEditSudosu('${$tableId}', this.parentElement.parentElement, event)"> <i  class="cp-edit"></i></span>`);
            $actionCell.append(`<span class="sudosuUpdateButton" style="display: none;" role="button" onclick="handleUpdateSudosu('${$tableId}',this.parentElement.parentElement, event)" title="Update" class="updateButton"><i class="cp-up-linearrow"></i></span>`);
            $actionCell.append(`<span role="button" title="Delete"  style="display: ${formData.ConfigureSubstituteAuthentication.length - 1 !== i ? '' : 'none'};"  class="sudosuDeleteButton" onclick="event.preventDefault(); handleDelete(event)"> <i  class="cp-Delete"></i></span>`);

            $dataRow.append($actionCell);
            $tBody.append($dataRow[0])
            $table.append($tBody[0]);

            setTimeout(() => {
                let lastRow = $('.custom-table tr:last').get(0);
                if (lastRow) {
                    const inputs = lastRow.querySelectorAll('input[type="text"]');
                    const inputPassword = lastRow.querySelectorAll('input[type="password"]');
                    const selects = lastRow.querySelectorAll('select');

                    // Disable the first two input elements, if available
                    if (inputs.length >= 2) {
                        inputs[0].removeAttribute('disabled');
                        inputs[1].removeAttribute('disabled');
                    }

                    if (inputPassword.length >= 1) {
                        inputPassword[0].removeAttribute('disabled');
                    }

                    // Disable the first select element, if available
                    if (selects.length >= 1) {
                        selects[0].removeAttribute('disabled');
                    }

                    // Trigger focus on the input with class "sourceDirectory"
                    $(lastRow).find('select.substituteAuthenticationType').trigger('focus');
                }
            }, 100)
        }
    }
}

function SubstituteAuthType() {

    return new Promise((resolve, reject) => {
        let selectInput = $("select[name='SubstituteAuthenticationType']");
        if (selectInput?.length > 0) {
            selectInput.each(function () {
                let $this = $(this);
                if ($this.is(':visible')) {
                    let associatedLabel = $this.attr('data-placeholder');
                    let toLowerCase = associatedLabel.charAt(0).toUpperCase() + associatedLabel.slice(1).toLowerCase();
                    $this.next(".dynamic-input-field").remove();
                    let value = $this.val().replace(/\s/g, "");
                    if (value) {
                        resolve(true);
                    } else {
                        $this.after("<div class='dynamic-input-field table-select-field-validation-error'><span class='required-field-msg'> " + toLowerCase + "</span></div>");
                        resolve(false);
                    }
                } else {
                    resolve(true);
                }
            });
        }
    });
}

async function tableFieldValidation(requiredInputs) {
    const validationResults = await Promise.all(requiredInputs.map(async function () {
        let $this = $(this);
        if ($this.is(':visible')) {
            let associatedLabel = $this.attr('placeholder');
            let toLowerCase = associatedLabel.charAt(0).toUpperCase() + associatedLabel.slice(1).toLowerCase();
            let inputValue2 = $this?.val();
            $this.next(".dynamic-input-field").remove();

            if (inputValue2 === "") {
                $this.after("<div class='dynamic-input-field table-field-validation-error'><span class='required-field-msg'> " + toLowerCase + "</span></div>");
                return false;
            }
            else {
                return true;
            }
        } else {
            return true
        }
    }));

    return validationResults.every(result => result);

    //requiredInputs?.each(async function () {
    //    let $this = $(this);
    //    if ($this.is(':visible')) {
    //        let associatedLabel = $this.attr('placeholder');
    //        let toLowerCase = associatedLabel.charAt(0).toUpperCase() + associatedLabel.slice(1).toLowerCase();
    //        let inputValue2 = $this?.val();
    //        $this.next(".dynamic-input-field").remove();

    //        if (inputValue2 === "") {
    //            // Show error message for each empty required field                               
    //            $this.after("<div class='dynamic-input-field table-field-validation-error'><span class='required-field-msg'> " + toLowerCase + "</span></div>");
    //            inputRqrd = false;
    //            return false;
    //        }
    //        else {
    //            return true;
    //        }
    //    }
    //});
}

async function SubstituteAuthPath() {
    let requiredInputs = $("input[name='SubstituteAuthenticationPath']");
    if (requiredInputs?.length > 0) {
        return await tableFieldValidation(requiredInputs);
    }
    return true;
}

async function SubstituteAuthUser() {
    let requiredInputs = $("input[name='SubstituteAuthenticationUser']");
    if (requiredInputs?.length > 0) {
        return await tableFieldValidation(requiredInputs);
    }
    return true;
}

async function SubstituteAuthPassword() {
    let requiredInputs = $("input[name='SubstituteAuthenticationPassword']");
    if (requiredInputs?.length > 0) {
        return await tableFieldValidation(requiredInputs);
    }
    return true;
}

$(document).on("change", ".substituteAuthenticationType", function () {
    let $this = $(this);
    let value = $this.val();
    if (value) {
        if ($this.next(".dynamic-input-field").length > 0) {
            $this.next(".dynamic-input-field").remove();
        }
    }
});

$(document).on("input", ".substituteUserName", function () {
    let $this = $(this);
    let value = $this.val();
    if (value) {
        if ($this.next(".dynamic-input-field").length > 0) {
            $this.next(".dynamic-input-field").remove();
        }
    }
});

async function handleAddSudosu(id, newRow, event) {
    enableValidation = false;
    let type = await SubstituteAuthType();
    //let path = await SubstituteAuthPath();
    let user = await SubstituteAuthUser();
    //let pwd = await SubstituteAuthPassword();

    if (type && user) {
        event.preventDefault();
        const table = document.querySelector(`#f-${id}`) ? document.querySelector(`#f-${id}`) : document.querySelector(`#${id}`);

        const clonedRow = newRow.cloneNode(true);
        const sudosuAddButton = newRow.querySelector(".sudosuAddButton");
        const sudosuEditButton = newRow.querySelector(".sudosuEditButton");
        const sudosuUpdateButton = newRow.querySelector(".sudosuUpdateButton");
        const sudosuDeleteButton = newRow.querySelector(".sudosuDeleteButton");

        sudosuAddButton.style.display = "none";
        sudosuEditButton.style.display = "";
        sudosuDeleteButton.style.display = "";

        const clonedRowDeleteButton = clonedRow.querySelector(".sudosuDeleteButton");
        clonedRowDeleteButton.style.display = "";

        const inputElements = clonedRow.querySelectorAll('input');
        const selectElements = clonedRow.querySelectorAll('select');
        inputElements.forEach(input => { input.value = ''; });
        selectElements.forEach(select => { select.value = ''; });

        newRow.querySelector(".substituteAuthenticationType").setAttribute("disabled", true);
        newRow.querySelector(".substitutePath").setAttribute("disabled", true);
        newRow.querySelector(".substituteUserName").setAttribute("disabled", true);
        newRow.querySelector(".substitutePassword").setAttribute("disabled", true);

        table.appendChild(clonedRow);
    }
}

function handleEditSudosu(id, newRow, event) {
    newRow.querySelector(".substituteAuthenticationType").removeAttribute("disabled");
    newRow.querySelector(".substitutePath").removeAttribute("disabled");
    newRow.querySelector(".substituteUserName").removeAttribute("disabled");
    newRow.querySelector(".substitutePassword").removeAttribute("disabled");

    newRow.querySelector('.substituteAuthenticationType').focus();

    const sudosuEditButton = newRow.querySelector(".sudosuEditButton");
    const sudosuUpdateButton = newRow.querySelector(".sudosuUpdateButton");

    if (sudosuEditButton) {
        sudosuEditButton.style.display = "none";
        sudosuUpdateButton.style.display = "";
    }
}

function handleUpdateSudosu(id, newRow, event) {
    newRow.querySelector(".substituteAuthenticationType").setAttribute("disabled", true);
    newRow.querySelector(".substitutePath").setAttribute("disabled", true);
    newRow.querySelector(".substituteUserName").setAttribute("disabled", true);
    newRow.querySelector(".substitutePassword").setAttribute("disabled", true);

    const sudosuEditButton = newRow.querySelector(".sudosuEditButton");
    const sudosuUpdateButton = newRow.querySelector(".sudosuUpdateButton");

    if (sudosuEditButton) {
        sudosuEditButton.style.display = "";
        sudosuUpdateButton.style.display = "none";
    }
}

function handleDelete(event) {
    event.preventDefault();
    $("#DeleteModalSudoSuTable").modal("show");
    document.getElementById("deleteSudoSuTableRow").addEventListener('click', async function () {
        let rowToDelete = $(event.target).closest('tr');
        //var previousRow = rowToDelete.prev();
        rowToDelete.remove();
        $("#DeleteModalSudoSuTable").modal("hide");

        let customTable = $('.custom-table').find('tr').length;

        if (customTable === 2) {
            const table = document.querySelector('.custom-table');
            const addButton = table.querySelector('.sudosuAddButton');
            const editButton = table.querySelector('.sudosuEditButton');
            const deleteButton = table.querySelector('.sudosuDeleteButton');

            let lastRow = $('.custom-table tr:last').get(0);
            if (lastRow) {
                const inputs = lastRow.querySelectorAll('input[type="text"]');
                const inputPassword = lastRow.querySelectorAll('input[type="password"]');
                const selects = lastRow.querySelectorAll('select');

                // Disable the first two input elements, if available
                if (inputs.length >= 2) {
                    inputs[0].removeAttribute('disabled');
                    inputs[1].removeAttribute('disabled');
                }

                if (inputPassword.length >= 1) {
                    inputPassword[0].removeAttribute('disabled');
                }

                // Disable the first select element, if available
                if (selects.length >= 1) {
                    selects[0].removeAttribute('disabled');
                }

                // Trigger focus on the input with class "sourceDirectory"
                $(lastRow).find('select.substituteAuthenticationType').trigger('focus');
            }
            if (editButton) {
                editButton.style.display = 'none';
            }
            if (addButton) {
                addButton.style.display = '';
            }
            if (deleteButton) {
                deleteButton.style.display = 'none';
            }
        } else {
            let lastRow = $('.custom-table tr:last').get(0);

            if (lastRow) {
                const hasAddButton = lastRow.querySelector('.sudosuAddButton');
                const hasDeleteButton = lastRow.querySelector('.sudosuDeleteButton');
                const hasEdit = lastRow.querySelector('.sudosuEditButton');

                const inputs = lastRow.querySelectorAll('input[type="text"]');
                const inputPassword = lastRow.querySelectorAll('input[type="password"]');
                const selects = lastRow.querySelectorAll('select');

                // Disable the first two input elements, if available
                if (inputs.length >= 3) {
                    inputs[0].removeAttribute('disabled');
                    inputs[1].removeAttribute('disabled');
                }

                if (inputPassword.length >= 1) {
                    inputPassword[0].removeAttribute('disabled');
                }

                // Disable the first select element, if available
                if (selects.length >= 1) {
                    selects[0].removeAttribute('disabled');
                }

                // Trigger focus on the input with class "sourceDirectory"
                $(lastRow).find('select.substituteAuthenticationType').trigger('focus');

                // Hide the edit button and show the add button, if they exist
                if (hasEdit.style.display !== "none") {
                    hasEdit.style.display = 'none';
                }
                if (hasAddButton.style.display === "none") {
                    hasAddButton.style.display = '';
                }
            }
        }


        //const hasAddButton = previousRow.find('.sudosuAddButton').get(0);
        //const hasDeleteButton = previousRow.find('.sudosuDeleteButton').get(0);
        //const hasEdit = previousRow.find('.sudosuEditButton').get(0);

        //if (hasEdit.style.display !== "none" && hasDeleteButton.style.display !== "none" && hasAddButton.style.display === "none") {
        //    hasEdit.style.display = 'none';
        //    hasDeleteButton.style.display = 'none';
        //    hasAddButton.style.display = '';
        //}       
    });
}
//Server Sudosu table ends here.


//Name already exists error server, Database, Replication, SSO, Rsync....
async function IsNameExist(url, data, errorFunc, dataname) {
    return !data[dataname]?.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}

async function GetAsync(url, data, errorFunc) {
    return await $.get(url, data).fail(errorFunc);
}

async function InfraNameValidation(value, id = null, nameExistURL, errorelement, nullerror, specialcharactererror, dataname) {
    if (!value) {
        errorelement.text(nullerror).addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        errorelement.text(specialcharactererror).addClass('field-validation-error');
        return false;
    }
    const url = RootUrl + nameExistURL;
    let data = { 'Id': id, [dataname]: value };
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        (dataname?.toLowerCase() == 'servername' || dataname?.toLowerCase() == 'databasename' || dataname?.toLowerCase() == 'replicationname') ? true : await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsNameExist(url, data, OnError, dataname)
    ];
    return await CommonValidation(errorelement, validationResults);
};

function encrypt(value) {
    const encryptedValue = btoa(JSON.stringify(value));
    return encryptedValue;
}

// Decryption function
function decrypt(encryptedValue) {
    const decryptedValue = JSON.parse(atob(encryptedValue));
    return decryptedValue;
}

function setFieldAttrValues(field) {
    const { id, meta, attrs } = field;
    if (meta?.id === "textarea") {
        const textArea = $(`#f-${id}`);
        textArea.attr('title', '');
        textArea.attr('autocomplete', 'off');
    }
    if (meta?.id === "ip-address") {
        const ipAddress = $(`#f-${id}`);
        ipAddress.attr('title', '');
        ipAddress.attr('maxlength', '40');
        ipAddress.attr('autocomplete', 'off');
    }
    if (meta?.id === "paragraph") {
        const paragraph = $(`#f-${id}`);
        paragraph.attr('title', '');
        paragraph.attr('autocomplete', 'off');
    }
    if (meta?.id === "text-input") {
        const textInput = $(`#f-${id}`);
        textInput.attr('title', '');
        let maxLength = textInput.attr('maxlength');
        if (!maxLength) {
            if (attrs?.name?.toLowerCase().includes("path")) {
                textInput.attr('maxlength', '500');
            } else {
                textInput.attr('maxlength', '100');
            }
        }
        textInput.attr('autocomplete', 'off');
    }
    if (meta?.id === "password-input") {
        const passwordInput = $(`#f-${id}`);
        passwordInput.attr('title', '');
        passwordInput.attr('maxlength', '40');
        passwordInput.attr('autocomplete', 'off');
    }
    if (meta?.id === "number") {
        const numberField = $(`#f-${id}`);
        numberField.attr('title', '');
        numberField.attr('autocomplete', 'off');
        const minLength = numberField?.attr('minlength');
        const maxLength = numberField?.attr('maxlength');
        const intMinValue = parseInt(minLength, 10);
        const intMaxValue = parseInt(maxLength, 10);
        if (intMinValue && intMaxValue && intMinValue > 0 && intMaxValue > 0) {
            numberField.on("keyup keydown", function (event) {
                let eventKey = event.key;
                const validKeys = ["Home", "End", "Backspace", "Delete", "ArrowLeft", "ArrowRight", ...Array.from({ length: 10 }, (_, i) => i.toString())];
                if (validKeys.includes(eventKey)) {
                    const inputValue = $(this).val();
                    const allowedKeys = ['Home', 'End', 'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight'];

                    if (inputValue.length >= intMaxValue && !allowedKeys.includes(event.key)) {
                        event.preventDefault();
                    }
                    const numericValue = parseInt($(this).val().length, 10);

                    if (isNaN(numericValue) || intMinValue < 1 || numericValue > intMaxValue) {
                        $(this).val('');
                    }
                } else {
                    event.preventDefault();
                }
            });
        } else {
            numberField.prop('min', '1');
            numberField.attr('max', '99999');
            numberField.on("keyup keydown", function (event) {
                let eventKey = event.key;
                const validKeys = ["Backspace", "Delete", "ArrowLeft", "ArrowRight", ...Array.from({ length: 10 }, (_, i) => i.toString())];
                if (validKeys.includes(eventKey)) {
                    const inputValue = $(this).val();
                    const allowedKeys = ['Home', 'End', 'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight'];

                    if (inputValue.length >= 5 && !allowedKeys.includes(event.key)) {
                        event.preventDefault();
                    }
                    const numericValue = parseInt($(this).val(), 10);

                    if (isNaN(numericValue) || numericValue < 1 || numericValue > 99999) {
                        $(this).val('');
                    }
                } else {
                    event.preventDefault();
                }
            });
        }
    }
}

async function getDBOptionsFromServer(type) {
    let dbType = type;

    if (type.toLowerCase() === "all" || type.toLowerCase() === "select database type") {
        dbType = "";
    }
    const result = await $.ajax({
        url: RootUrl + "Configuration/Database/GetDatabaseNames",
        method: 'GET',
        dataType: 'json',
        data: { type: dbType }
    });

    if (result.success) {
        const options = [];
        options.push({ value: '', text: '' });
        $.each(result?.data, function (index, item) {
            options.push({ value: item.id, text: item.name });
        });
        return options;
    } else {
        errorNotification(result);
    }
}

async function getServOptionsFromServer(ServerRoleID, ServerTypeID) {
    const result = await $.ajax({
        url: RootUrl + "Configuration/Server/GetServerNames",
        method: 'GET',
        dataType: 'json',
        data: { 'roleType': ServerRoleID, 'serverType': ServerTypeID }
    });

    if (result.success) {
        const options = [];
        options.push({ value: '', text: '' });
        $.each(result?.data, function (index, item) {
            options.push({ value: item.id, text: item.name });
        });
        return options;
    } else {
        errorNotification(result);
    }
}

async function getSSOOptionsFromServer() {
    const result = await $.ajax({
        url: RootUrl + "Admin/ComponentType/SingleSignOnList",
        method: 'GET',
        dataType: 'json'
    });

    if (result.success) {
        const options = [];
        options.push({ value: '', text: '' });
        const uniqueValues = new Set();
        $.each(result?.data, function (index, item) {
            if (!uniqueValues.has(item?.id?.toLowerCase())) {
                options.push({ value: item.id, text: item.componentName });
                uniqueValues.add(item?.id?.toLowerCase());
            }
        });
        return options;
    } else {
        errorNotification(result);
    }
}

async function getSSOProfileNameOptionsFromServer(value) {
    if (value) {
        const result = await $.ajax({
            url: RootUrl + "Configuration/SingleSignOn/GetSingleSignOnByType",
            type: 'POST',
            data: { type: value, __RequestVerificationToken: gettoken() },
            dataType: 'json'
        });

        if (result.success) {
            const options = [];
            options.push({ value: "", text: "Select Profile" });
            $.each(result?.data, function (index, item) {
                options.push({ value: item.id, text: item.profileName });
            });
            return options;
        } else {
            errorNotification(result);
        }
    } else {
        const options = [];
        options.push({ value: "", text: "Select Profile" });
        return options;
    }
}

async function getRepOptionsFromServer() {
    const result = await $.ajax({
        url: RootUrl + "Configuration/Replication/GetReplicationNames",
        method: 'GET',
        dataType: 'json'
    });

    if (result.success) {
        const options = [];
        options.push({ value: '', text: '' });
        $.each(result?.data, function (index, item) {
            options.push({ value: item.id, text: item.name });
        });
        return options;
    } else {
        errorNotification(result);
    }
}

async function getNodesFromServer() {
    const result = await $.ajax({
        url: RootUrl + "Configuration/Node/GetNodeNames",
        method: 'GET',
        dataType: 'json'
    });

    if (result.success) {
        const options = [];
        options.push({ value: '', text: '' });
        $.each(result?.data, function (index, item) {
            options.push({ value: item.id, text: item.name });
        });
        return options;
    } else {
        errorNotification(result);
    }
}

async function getWorkflowsActionsFromServer(id) {
    if (id) {
        const result = await $.ajax({
            url: RootUrl + "ITAutomation/WorkflowList/GetWorkflowById",
            method: 'GET',
            dataType: 'json',
            data: { workflowId: id }
        });

        if (result.success) {
            const options = [];
            let Properties = JSON.parse(result?.data.properties);
            let propLength = Properties.nodes.length;

            for (let i = 0; i < propLength; i++) {
                if (Properties?.nodes[i]?.hasOwnProperty('children')) {
                    Properties?.nodes[i]?.children?.forEach(function (obj) {
                        let Obj = { 'id': obj.actionInfo.uniqueId, value: obj.actionInfo.actionName };
                        options.push(Obj);
                    });
                } else if (!Properties.nodes[i].hasOwnProperty('groupName')) {
                    let obj = { 'id': Properties.nodes[i].actionInfo.uniqueId, value: Properties.nodes[i].actionInfo.actionName };
                    options.push(obj);
                }
            }

            return options;
        } else {
            errorNotification(result);
        }
    }
}

async function getWorkflowsFromServer(isAction, id) {
    const result = await $.ajax({
        url: RootUrl + "ITAutomation/WorkflowList/GetWorkflowNames",
        method: 'GET',
        dataType: 'json'
    });

    if (result.success) {
        const options = [];
        options.push({ value: '', text: '' });
        $.each(result?.data, function (index, item) {
            options.push({ value: item.id, text: item.name });
        });
        return options;
    } else {
        errorNotification(result);
    }
}

//Server Edit function
function serverEditpopulateDynamicFields(data) {
    let formData = JSON.parse(data);
    $('#formRenderingArea .formeo-render .f-field-group').each(async function (index, element) {
        let fieldName = $(element).find('input, select, textarea, table').attr('name');
        let fieldClass = $(element).find('input, select, textarea, table').attr('class');
        let fieldVal = $(element).find('input, select, textarea').attr('value');
        let fieldType = $(element).find('input, select, textarea').attr('type');

        //if (fieldType === "date") {
        //    console.log(fieldType);
        //    let datePicker = document.getElementById(fieldName);
        //    datePicker.value = dateToBind;
        //}
        //if (fieldType === "date") {
        //    $(element).find('input[type="date"]').val(chkValue).trigger("change");
        //}

        if (fieldName && formData.hasOwnProperty(fieldName) || fieldVal) {

            let value = formData[fieldName];
            let SSOValue = formData[fieldName + "ID"];
            let checkbox = $(element).find('input[type="checkbox"]').attr("value")
            let chkValue = formData[checkbox]

            //if (value || chkValue) {
            if (fieldType == 'radio') {
                $(element).find('input[type="radio"]').map((index, radio) => {
                    let radioValue = $(radio).val();
                    if (radioValue === formData[radioValue]) {
                        $(radio).prop('checked', true);
                    }
                });
            }

            if (typeof value === "boolean") {
                $(element).find('input[type="checkbox"]').prop('checked', chkValue);
                $(element).find('input[type="checkbox"]').trigger("change")
            }
            else if (fieldVal) {
                if (typeof chkValue == 'string') chkValue = (chkValue == '0' || chkValue?.toLowerCase() == 'false') ? false : true
                $(element).find('input[type="checkbox"]').prop('checked', chkValue).trigger("change");
            }
            else if (typeof value === "object") {
                if (value) {
                    /*const selectElement = document.getElementById('f-0d95bdab-911d-4521-91aa-56a0b40fe093');*/
                    const valuesArray = Object.values(value);
                    const containsObject = Array.isArray(valuesArray) && valuesArray.some(item => typeof item === 'object');
                    let labelsArray;
                    if (containsObject) {
                        labelsArray = valuesArray.map(item => item.value);
                    } else {
                        labelsArray = valuesArray.map(item => item);
                    }
                    //$(element).find('input, select, textarea').val(labelsArray).change();
                    const selectElement = $(element).find('select');
                    // Loop through options in the select element
                    setTimeout(function () {
                        selectElement?.find('option').each(function () {
                            const optionValue = $(this).val();
                            if (labelsArray.includes(optionValue)) {
                                $(this).prop('selected', true);
                            }
                        });
                        selectElement.trigger('change');
                    }, 350)
                }
                //if (value) {
                /*const selectElement = document.getElementById('f-0d95bdab-911d-4521-91aa-56a0b40fe093');*/
                //const valuesArray = Object.values(value);
                //$(element).find('input, select, textarea').val(valuesArray).change();
                //$(element).find('input, select, textarea').trigger("change");
                //}
            }
            else {
                if (fieldName.includes("singlesignon")) {
                    $(element).find('input, select, textarea').val(SSOValue).trigger("change")
                }
                if (!fieldName.includes("singlesignon")) {
                    $(element).find('input, select, textarea').val(value).trigger("change");

                    if (fieldType == 'select') {

                        if (fieldName?.toLowerCase()?.replace(/\s+/g, '') == 'authenticationtype' && (value?.toLowerCase()?.replace(/\s+/g, '') == 'apitoken' || value?.toLowerCase()?.replace(/\s+/g, '') == 'basicapiauthentication')) {
                            let getAuthElement = $("select[name='APISubAuthentication']")

                            $(element).find('input, select, textarea').val('API').trigger("change");

                            if (value?.toLowerCase()?.replace(/\s+/g, '') == 'apitoken') getAuthElement.val(value).trigger("change");
                            else if (value?.toLowerCase()?.replace(/\s+/g, '') == 'basicapiauthentication') getAuthElement.val('BasicAuthentication').trigger("change");
                        }

                    }

                    //for migration 4.5 to 6. Get Confirmation whether it will affect exist saved data???..
                    //let selectElement = $(element).find('select')?.[0];
                    //if (selectElement) {
                    //    let options = selectElement.getElementsByTagName("option");
                    //    for (let option of options) {
                    //        option.value = option.value.toLowerCase();
                    //    }
                    //    selectElement.innerHTML = selectElement.innerHTML;
                    //    value = value.replace(/\s+/g, '').toLowerCase();
                    //    $(element).find('select').val(value).trigger("change");
                    //}
                }
                //if (fieldName.toLocaleLowerCase()?.trim() === 'ipaddress' || fieldName.toLocaleLowerCase()?.trim() === 'hostname' || fieldName.toLocaleLowerCase()?.trim() === 'virtualipaddress') {
                //    let ipAddressInput = document.querySelector('input[name="IpAddress"]');
                //    if (ipAddressInput) {
                //        ipAddressInput.setAttribute("readonly", "readonly");
                //    }
                //    let VirtualIPAddress = document.querySelector('input[name="VirtualIPAddress"]');
                //    if (VirtualIPAddress) {
                //        VirtualIPAddress.setAttribute("readonly", "readonly");
                //    }
                //    let hostName = document.querySelector('input[name="HostName"]');
                //    if (hostName) {
                //        hostName.setAttribute("readonly", "readonly");
                //    }
                //}
                if (fieldName.toLowerCase().includes("singlesignon")) {
                    if (formData["signonprofileID"]) {
                        setTimeout(() => {
                            $("#f-new-select-id8rhdgry0").val(formData["signonprofileID"]).trigger("change");
                        }, 250);
                    }
                }
                if (fieldName === "@@workflow_name") {
                    if (formData["@@workflow_actions"]) {
                        setTimeout(() => {
                            $("#f-new-select-id8actions0").val(formData["@@workflow_actions"]);
                            $("#f-new-select-id8actions0").trigger("change");
                        }, 250);
                    }
                }
            }
            //}
        }

        if (fieldClass == 'custom-table' && formData.hasOwnProperty('ConfigureSubstituteAuthentication')) {

            if (formData.ConfigureSubstituteAuthentication.length > 0) {
                sudosuTableWhileEdit(formData);
            }
        }

        if (fieldClass == 'deployments-table' && formData.hasOwnProperty('ConfigureDeployments')) {

            if (formData.ConfigureDeployments.length > 0) {
                deploymentTableWhileEdit(formData);
            }
        }

        //    if (fieldClass == 'dynamic-custom-table' && formData.hasOwnProperty('DynamicCustomTable')) {
        //        if (formData.DynamicCustomTable.length > 0) {
        //            let tableIDCollection = document.getElementsByClassName('dynamic-custom-table');
        //            let tableID = tableIDCollection[0];
        //            let $tableId
        //            if (tableID) {
        //                $tableId = $(tableID).attr('id');
        //                let $table = document.querySelector(`#${$tableId}`)
        //                //$table.innerHTML = '';
        //                let $tHead = $(' <thead> </thead>')
        //                let $headerRow = $('<tr class="header_row5"></tr>');
        //                for (let i = 0; i < formData?.DynamicCustomTable[0]?.length; i++) {
        //                    $headerRow.append(`<th>${formData?.DynamicCustomTable[0][i]}</th>`);
        //                }
        //                //// Append the header row to the table
        //                $tHead.append($headerRow[0])
        //                $table.append($tHead[0]);

        //                let $tBody = $('<tbody></tbody>');
        //                for (let i = 0; i < formData?.DynamicCustomTable[1]?.length; i++) {
        //                    let $dataRow = $('<tr></tr>');
        //                    let values = Object.values(formData?.DynamicCustomTable[1][i]);
        //                    let keys = Object.keys(formData?.DynamicCustomTable[1][i]);
        //                    //console.log(keys);
        //                    for (let j = 0; j < values.length; j++) {
        //                        //let dynamicID = Math.floor((Math.random() * 10000) + 1);
        //                        $dataRow.append(`<td><input type="text" value=${values[j]} name="${keys[j]}" /></td>`);
        //                    }

        //                    //// Add action buttons to the data row
        //                    let $actionCell = $('<td style="height:62px" class="d-flex align-items-center justify-content-center"></td>');

        //                    $actionCell.append(`<span role="button" title="Add" onclick="event.preventDefault(); handleAddDynamicRow('${$tableId}', this.parentElement.parentElement, event)">
        //                                        <i class="cp-add"></i>
        //                                    </span>`);

        //                    i !== 0 && $actionCell.append(`<span role="button" title="Delete" class="delete-button">
        //                                   <i onclick="handleDelete(event)" class="cp-Delete"></i>
        //                               </span>`);

        //                    $dataRow.append($actionCell);
        //                    $tBody.append($dataRow[0])
        //                    $table.append($tBody[0]);
        //                }
        //            }
        //        }
        //    }
    });

    let encryption = $("input[type='password']"); //when it's optional also
    encryption?.each(function () {
        let $this = $(this);
        let id = $this.attr("id");
        document.getElementById(id)?.addEventListener("focus", async function () {

            if ($this.is(':visible')) {
                let $thisName = $(this).attr('name');
                let $thisval = $(this).val();
                if ($thisval) {
                    let pwd = await DecryptPassword($thisval);
                    $(`input[name=${$thisName}]`).val(pwd);
                }
            }
        });

        document.getElementById(id)?.addEventListener('blur', async function () {
            if ($this.is(':visible')) {
                let $thisName = $(this).attr('name');
                let $thisval = $(this).val();
                if ($thisval) {
                    let pwd = await EncryptPassword($thisval);
                    $(`input[name=${$thisName}]`).val(pwd);
                }
            }
        });
    });
    btnCrudEnable('saveButton');
}

//$(document).on("input", ".deploymentsReplicaName", function () {
//    let $this = $(this);
//    let value = $this.val();
//    let associatedLabel = $this.attr('placeholder');
//    let toLowerCase = associatedLabel.charAt(0).toUpperCase() + associatedLabel.slice(1).toLowerCase();
//    if (value) {
//        if ($this.next(".dynamic-input-field").length > 0) {
//            $this.next(".dynamic-input-field").remove();
//        }
//    } else {
//        $this.after("<div class='dynamic-input-field table-field-deploymentsReplicaNamevalidation-error'><span class='required-field-msg'> " + toLowerCase + "</span></div>");
//    }
//});

//$(document).on("input", ".deploymentsName", function () {
//    let $this = $(this);
//    let value = $this.val();
//    let associatedLabel = $this.attr('placeholder');
//    let toLowerCase = associatedLabel.charAt(0).toUpperCase() + associatedLabel.slice(1).toLowerCase();
//    if (value) {
//        if ($this.next(".dynamic-input-field").length > 0) {
//            $this.next(".dynamic-input-field").remove();
//        }
//    } else {
//        $this.after("<div class='dynamic-input-field table-field-deploymentsNamevalidation-error'><span class='required-field-msg'> " + toLowerCase + "</span></div>");
//    }
//});

//async function handleAddDynamicRow(id, newRow, event) {
//    event.preventDefault();
//    const table = document.querySelector(`#f-${id}`) ? document.querySelector(`#f-${id}`) : document.querySelector(`#${id}`);
//    const clonedRow = newRow.cloneNode(true);

//    // Clear input and select values in the cloned row
//    const inputElements = clonedRow.querySelectorAll('input');
//    inputElements.forEach(input => { input.value = ''; });

//    // Check if the cloned row already has a delete button
//    const hasDeleteButton = clonedRow.querySelector('.delete-button');
//    if (!hasDeleteButton) {
//        const lastTd = clonedRow.querySelector('td:last-child');
//        lastTd.innerHTML += '<span role="button" title="Delete" class="delete-button"><i onclick="handleDelete(event)" class="cp-Delete"></i></span>';
//    }

//    // Append the cloned row to the table
//    table.appendChild(clonedRow);
//}

async function populateFormbuilderDynamicFields(fields) {
    for (const key in fields) {
        if (fields.hasOwnProperty(key)) {
            const field = fields[key];
            const { id, meta, config, attrs } = field;
            if (meta.id === "password-input") {
                //setTimeout(() => {
                //    const fieldElement = document.querySelector(`#f-${id}`);; // Assuming id is the ID of the password input field
                //    if (fieldElement) {
                //        const fieldGroup = fieldElement.closest('.f-field-group'); // Assuming the input field is within a container with class 'f-field-group'
                //        if (fieldGroup) {
                //            // Create an icon element (for Font Awesome, use <i> tag)
                //            const icon = document.createElement('span');
                //            icon.classList.add('cp-password-hide'); // Example classes for Font Awesome icon
                //            // Append the icon to the field container
                //            fieldGroup.appendChild(icon);
                //        }
                //    }
                //}, 1000)
            }
            if (meta.id === "database") {
                const type = attrs.DatabaseTypeID;
                const _options = await getDBOptionsFromServer(type);
                const selectField = document.querySelector(`#f-${id}`);
                if (selectField) {
                    nextButtonStyle(' ', ' ');
                    _options.forEach(option => {
                        const optionElement = document.createElement("option");
                        optionElement.value = option.value;
                        optionElement.text = option.text;
                        selectField.appendChild(optionElement);
                    });
                } else {
                    let error = { message: "Check the database condition field in the form builder module." }
                    errorNotification(error);
                    nextButtonStyle('0.5', 'none');
                }
            }
            if (meta.id === "server") {
                const role = attrs.ServerRole;
                const type = attrs.ServerType;
                const ServerRoleID = attrs.ServerRoleID;
                const ServerTypeID = attrs.ServerTypeID;
                const _options = await getServOptionsFromServer(ServerRoleID, ServerTypeID);
                const selectField = document.querySelector(`#f-${id}`);
                if (selectField) {
                    nextButtonStyle(' ', ' ');
                    _options.forEach(option => {
                        const optionElement = document.createElement("option");
                        optionElement.value = option.value;
                        optionElement.text = option.text;
                        selectField.appendChild(optionElement);
                    });
                } else {
                    let error = { message: "Check the server condition field in the form builder module." }
                    errorNotification(error);
                    nextButtonStyle('0.5', 'none');
                }
            }
            if (meta.id === "replication") {
                const _options = await getRepOptionsFromServer();
                const selectField = document.querySelector(`#f-${id}`);
                if (selectField) {
                    nextButtonStyle(' ', ' ');
                    _options.forEach(option => {
                        const optionElement = document.createElement("option");
                        optionElement.value = option.value;
                        optionElement.text = option.text;
                        selectField.appendChild(optionElement);
                    });
                } else {
                    let error = { message: "Check the replication condition field in the form builder module." }
                    errorNotification(error);
                    nextButtonStyle('0.5', 'none');
                }
            }
            if (meta.id === "workflow") {
                let isAction = attrs.dependentAction
                const _options = await getWorkflowsFromServer(isAction);
                const selectField = document.querySelector(`#f-${id}`);
                if (selectField) {
                    nextButtonStyle(' ', ' ');
                    if (isAction) {
                        const duplicatedSelectField = selectField.parentNode.parentNode.parentNode.parentNode.cloneNode(true);
                        const labelElement = duplicatedSelectField.querySelector('label');
                        labelElement.textContent = 'Workflows Actions';
                        labelElement.htmlFor = 'f-new-select-id8actions0';
                        const selectElement = duplicatedSelectField.querySelector('select');
                        selectElement.name = '@@workflow_actions';
                        selectElement.multiple = true
                        selectElement.id = 'f-new-select-id8actions0';
                        selectElement.setAttribute('placeholder', 'Select Workflow Actions');
                        selectField.parentNode.parentNode.parentNode.parentNode.appendChild(duplicatedSelectField);

                        _options.forEach(option => {
                            const optionElement = document.createElement("option");
                            optionElement.value = option.value;
                            optionElement.text = option.text;
                            selectField.appendChild(optionElement);
                        });

                        selectElement.innerHTML = '';
                        const optionElement2 = document.createElement("option");
                        optionElement2.value = '';
                        optionElement2.text = "Select Workflow Actions";
                        selectElement.appendChild(optionElement2);

                        $(`#f-${id}`).on("change", async function () {
                            const _options2 = await getWorkflowsActionsFromServer(this.value);
                            selectElement.innerHTML = '';
                            _options2.forEach(option => {
                                const optionElement = document.createElement("option");
                                optionElement.value = option.id;
                                optionElement.text = option.value;
                                selectElement.appendChild(optionElement);
                            });
                        })
                    }
                    else {
                        _options.forEach(option => {
                            const optionElement = document.createElement("option");
                            optionElement.value = option.value;
                            optionElement.text = option.text;
                            selectField.appendChild(optionElement);
                        });
                    }

                } else {
                    let error = { message: "Check the workflow condition field in the form builder module." }
                    errorNotification(error);
                    nextButtonStyle('0.5', 'none');
                }
            }
            if (meta.id === "singlesignon") {
                const _options = await getSSOOptionsFromServer();
                const selectField = document.querySelector(`#f-${id}`);
                if (selectField) {
                    nextButtonStyle('', '');
                    const profileField = selectField.parentNode.cloneNode(true);
                    const labelElement = profileField.querySelector('label');
                    labelElement.textContent = 'Profile';
                    labelElement.htmlFor = 'f-new-select-id8rhdgry0';
                    const selectElement = profileField.querySelector('select');
                    selectElement.name = 'signonprofile';
                    selectElement.id = 'f-new-select-id8rhdgry0';
                    selectElement.setAttribute('placeholder', 'Select Profile');
                    selectField.parentNode.parentNode.appendChild(profileField);

                    _options.forEach(option => {
                        const optionElement = document.createElement("option");
                        optionElement.value = option.value;
                        optionElement.text = option.text;
                        selectField.appendChild(optionElement);
                    });

                    selectElement.innerHTML = '';
                    const optionElement2 = document.createElement("option");
                    optionElement2.value = '';
                    optionElement2.text = "Select Profile";
                    selectElement.appendChild(optionElement2);

                    $(`#f-${id}`).on("change", async function () {
                        const _options2 = await getSSOProfileNameOptionsFromServer(this.value);
                        selectElement.innerHTML = '';
                        _options2.forEach(option => {
                            const optionElement = document.createElement("option");
                            optionElement.value = option.value;
                            optionElement.text = option.text;
                            selectElement.appendChild(optionElement);
                        });
                    });
                } else {
                    let error = { message: "Check the single sign-on condition field in the form builder module." }
                    errorNotification(error);
                    nextButtonStyle('0.5', 'none');
                }
            }
            if (meta.id === "nodes") {
                const _options = await getNodesFromServer();
                const selectNodeField = document.querySelector(`#f-${id}`);

                if (selectNodeField) {
                    nextButtonStyle('', '');
                    _options.forEach(option => {
                        const optionElement = document.createElement("option");
                        optionElement.value = option.value;
                        optionElement.text = option.text;
                        selectNodeField?.appendChild(optionElement);
                    });
                } else {
                    let error = { message: "Check the nodes condition field in the form builder module." }
                    errorNotification(error);
                    nextButtonStyle('0.5', 'none');
                }
            }
            if (meta.id === "table") {
                setTimeout(() => {
                    sudosuTableWhileCreate(id);
                }, 1000)
            }

            if (meta.id === "deploymentTable") {
                setTimeout(() => {
                    deploymentTableWhileCreate(id);
                }, 1000)
            }

            //    if (meta.id === "dynamicTable") {
            //        setTimeout(() => {
            //            const $table = document.querySelector(`#f-${id}`);
            //            //$table.innerHTML = '';
            //            //console.log(id, attrs, $table);
            //            let keys = Object.keys(attrs?.dynamicHeader);
            //            let length = keys.length;
            //            if ($table?.children?.length == 0) {
            //                let $tHead = $(' <thead> </thead>')
            //                let $headerRow = $('<tr class="header_row5"></tr>');
            //                for (let i = 0; i < length; i++) {
            //                    $headerRow.append(`<th id="${keys[i]}">${attrs?.dynamicHeader[keys[i]]}</th>`);
            //                }
            //                $headerRow.append('<th>Action</th>');
            //                // Append the header row to the table
            //                $tHead.append($headerRow[0])
            //                $table.append($tHead[0]);

            //                let $tBody = $('<tbody></tbody>');
            //                let $dataRow = $('<tr></tr>');
            //                for (let i = 0; i < length; i++) {
            //                    let dynamicID = Math.floor((Math.random() * 10000) + 1);
            //                    $dataRow.append(`<td><input type="text" name="dynamicData${dynamicID}" placeholder="Enter ${attrs?.dynamicHeader[keys[i]]}" /></td>`);
            //                }
            //                // Add action buttons to the data row
            //                let $actionCell = $('<td style="height:62px" class="d-flex align-items-center justify-content-center"> </td>');
            //                $actionCell.append(`<span role="button" title="Add"  onclick="event.preventDefault(); handleAddDynamicRow('${id}', this.parentElement.parentElement, event)"> <i  class="cp-add "></i></span>`);
            //                //$actionCell.append('<span role="button" title="Delete" class="delete-button" ><i onclick="handleDelete(event)" class="cp-Delete"></i></span>');

            //                $dataRow.append($actionCell);
            //                // Append the data row to the table
            //                $tBody.append($dataRow[0])
            //                $table.append($tBody[0]);
            //            }
            //        }, 1000);
            //    }
        }
    }
}

function nextButtonStyle(opacity, pointerEvents) {
    $('.next_btn').css({
        'opacity': opacity,
        'pointer-events': pointerEvents,
    });
}

$('.btn-cancel').on('click', function () {
    nextButtonStyle('', '');
});

function formBuilderTextConditions(event, parsedJsonData) {
    let selectedValue = event.target.value;
    let selectedid = event.target.id;
    let typ = event.target.type;
    let getId = selectedid.replace('f-', '');

    if (typ === "text") {
        let replacedId = getId.replace(/-0$/, '');
        let field = parsedJsonData?.fields && parsedJsonData?.fields[replacedId];

        if (field?.conditions && field?.conditions.length > 0) {
            let isVisible = false;
            field.conditions.forEach(function (condition) {
                let isMatchingCondition = condition.if.some(function (ifClause) {
                    sourceField = parsedJsonData?.fields[ifClause.source.substring(7)];
                    return ifClause.target === selectedValue;
                });

                if (isMatchingCondition) {
                    isVisible = true;
                }
            });

            field.conditions.forEach(function (condition) {
                condition.then.forEach(function (thenClause) {
                    condition.if.forEach(function (ifClause) {
                        let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);

                        if (targetElement) {

                            if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {
                                if (isVisible) {
                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                } else {
                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add("d-none")
                                }
                            }
                        }
                    });
                });
            });
        }
    }
}

function formBuilderConditions(event, parsedJsonData) {

    let selectedValue = event.target.value;
    let selectedid = event.target.id;
    let typ = event.target.type;
    let getId = selectedid.replace('f-', '');

    if (typ === "radio" || typ === "checkbox") {

        let replacedId = getId.replace(/-0$/, '');
        let field = parsedJsonData?.fields && parsedJsonData?.fields[replacedId];

        if (field?.conditions && field?.conditions.length > 0) {
            let isVisible = false;
            field.conditions.forEach(function (condition) {
                let isMatchingCondition = condition.if.some(function (ifClause) {
                    sourceField = parsedJsonData?.fields[ifClause.source.substring(7)];
                    return ifClause.target === selectedValue;
                });

                if (isMatchingCondition) {
                    isVisible = true;
                }
            });
            field.conditions.forEach(function (condition) {
                condition.then.forEach(function (thenClause) {
                    condition.if.forEach(function (ifClause) {
                        let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                        let targetElementChk = document.getElementById(`f-${thenClause.target.substring(7)}-0`);
                        let srcElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                        let parentID = targetElement && targetElement.parentNode.parentNode.getAttribute('id')

                        if (targetElement) {

                            if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {
                                if (isVisible) {
                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                    targetElement.parentNode.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                    if (targetElement.type === "select-one") {
                                        $(`#${targetElement.id}`).val("").trigger("change");
                                    }
                                    let parentDivId = $(`#${targetElement.id}`).closest('div').parent().closest('div').attr('id');
                                    setTimeout(() => {
                                        $(`#${parentDivId}`)
                                            .find('.field-validation-error-selecttag')
                                            .remove();
                                    }, 0)
                                }
                                if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                    if (targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) return false;
                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add("d-none");
                                    let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                    let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                    if (textField?.length > 0) {
                                        removeValidationWhenUncheck(textField);
                                    }
                                    if (selectField?.length > 0) {
                                        removeValidationWhenUncheck(selectField);
                                    }
                                }
                            }
                            else if (ifClause.comparison === "notEquals") {
                                if (targetElement && event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                    if ($('#' + parentID).children().length > 1) {
                                        targetElement.parentNode.classList.add('d-none')
                                    } else {
                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none')
                                        let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                        let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                        if (textField?.length > 0) {
                                            removeValidationWhenUncheck(textField);
                                        }
                                        if (selectField?.length > 0) {
                                            removeValidationWhenUncheck(selectField);
                                        }
                                    }
                                }
                                else if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                    let isForm = 0
                                    let isFound = Object.keys(parsedJsonData.fields).some(function (id) {
                                        let field = parsedJsonData.fields[id];

                                        if ((replacedId !== id) && field.conditions && field.conditions.length > 0) {
                                            return field.conditions.some(function (condition) {
                                                let isMatchingCondition = condition.then.some(function (ifClauses) {
                                                    return (ifClauses.target === thenClause.target) && (++isForm);
                                                });
                                                return isMatchingCondition && condition.if.some(function (ifClauses) {
                                                    return ifClauses.target === $('#' + `f-${id}`).val();
                                                });
                                            });
                                        }
                                        return false;
                                    });

                                    if (isForm > 0) {
                                        if (isFound) {
                                            if ($('#' + parentID).children().length > 1) {
                                                targetElement.parentNode.classList.remove('d-none')
                                            } else {
                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                                            }
                                        }
                                    } else {
                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                                    }
                                }
                            }
                        }
                        else if (targetElementChk) {

                            if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {
                                if (isVisible) {
                                    targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                    if (targetElementChk.type === "select-one") {
                                        $(`#${targetElementChk.id}`).val("").trigger("change");
                                    }
                                    let parentDivId = $(`#${targetElementChk.id}`).closest('div').parent().closest('div').attr('id');
                                    setTimeout(() => {
                                        $(`#${parentDivId}`)
                                            .find('.field-validation-error-selecttag')
                                            .remove();
                                    }, 0)
                                }
                                if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                    if (targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) return false;
                                    targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.add("d-none");
                                    let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                    let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                    if (textField?.length > 0) {
                                        removeValidationWhenUncheck(textField);
                                    }
                                    if (selectField?.length > 0) {
                                        removeValidationWhenUncheck(selectField);
                                    }
                                }
                            }
                            else if (ifClause.comparison === "notEquals") {

                                if (targetElementChk && event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                    targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                    let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                    let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                    if (textField?.length > 0) {
                                        removeValidationWhenUncheck(textField);
                                    }
                                    if (selectField?.length > 0) {
                                        removeValidationWhenUncheck(selectField);
                                    }
                                }
                                else if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                    targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                                }
                            }
                        }
                    });
                });
            });
        }
    };

    if (typ === "select-one") {
        let field = parsedJsonData?.fields && parsedJsonData.fields[getId];

        if (field?.conditions && field?.conditions.length > 0) {
            let isMatchingCondition = field.conditions.some(function (condition) {
                return condition.if.some(function (ifClause) {
                    if (ifClause.source === `fields.${getId}` && ifClause.target === selectedValue) {
                        return true;
                    }
                });
            });

            if (isMatchingCondition) {
                field.conditions.forEach(function (condition) {
                    condition.then.forEach(function (thenClause) {
                        condition.if.forEach(function (ifClause) {
                            let targetID = thenClause.target.substring(7);
                            let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);

                            if (targetElement === null) {
                                targetElement = document.getElementById(`f-${thenClause.target.substring(7)}-0`);
                            }
                            let parentID = targetElement && targetElement.parentNode.parentNode.getAttribute('id')

                            if (targetElement && thenClause.targetProperty === 'isVisible') {
                                if (ifClause.target === selectedValue && thenClause.assignment === 'equals' &&
                                    (targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none") ||
                                        targetElement.parentNode.classList.contains("d-none"))) {
                                    let isFound = Object.keys(parsedJsonData.fields).some(function (id) {
                                        let field = parsedJsonData.fields[id];

                                        if ((getId !== id) && field.conditions && field.conditions.length > 0) {
                                            return field.conditions.some(function (condition) {
                                                return condition.then.some(function (ifClauses) {
                                                    return ifClauses.target === thenClause.target && $('#' + `f-${id}-0`).prop('checked');
                                                });
                                            });
                                        }
                                        return false;
                                    });

                                    if (!isFound) {
                                        if ($('#' + parentID).children().length > 1) {
                                            targetElement.parentNode.classList.remove('d-none')
                                        }
                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                        correctElementId = targetElement.id;
                                        if (targetElement.type !== "checkbox") {
                                            $(`#${correctElementId}`).val("").trigger("change");
                                        }
                                        if (targetElement.type === "checkbox") {
                                            $(`#${correctElementId}`).prop("checked", false);
                                        }
                                        let parentDivId = $(`#${correctElementId}`).closest('div').parent().closest('div').attr('id');
                                        setTimeout(() => {
                                            $(`#${parentDivId}`)
                                                .find('.field-validation-error-selecttag')
                                                .remove();
                                        }, 0)
                                    }
                                } else if (ifClause.target !== selectedValue) {

                                    let getTargetId = thenClause?.target ? thenClause?.target?.replace('fields.', '') : '';

                                    checkChildConditions(getTargetId, parsedJsonData)

                                    //Added for Group control - ilakkyah
                                    //let hideGroupSelectParent = $(targetElement).parent().parent().find('.f-field-group').filter('.d-none').length !==
                                    //    $(targetElement).parent().parent().find('.f-field-group').length - 1

                                    if (targetElement.type !== "checkbox") {
                                        targetElement.value = ""
                                    }
                                    if (targetElement.type === "checkbox") {
                                        $(`#${correctElementId}`).prop("checked", false);
                                    }

                                    //remove this if enable groupcontrol
                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');

                                    //Added for Group control - ilakkyah
                                    //($(targetElement)?.parent()?.parent()?.find('.f-field-group')?.length > 1 && hideGroupSelectParent) ? targetElement.parentNode.classList.add('d-none')
                                    //    : targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none') 

                                    let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                    let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                    if (textField?.length > 0) {
                                        removeValidationWhenUncheck(textField);
                                    }
                                    if (selectField?.length > 0) {
                                        removeValidationWhenUncheck(selectField);
                                    }
                                }
                            }
                            if (targetElement && thenClause.targetProperty === "isNotVisible" &&
                                ifClause.target !== selectedValue && // ( || ifClause.target === selectedValue) && for group control
                                ifClause.comparison === 'notEquals' && // ( || ifClause?.comparison?.toLowerCase() === 'equals') &&
                                !targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains('d-none')) {
                                //if (ifClause.target === selectedValue) { //Parent select option condition to child.

                                //for group control
                                //let hideGroupSelectParent = $(targetElement).parent().parent().find('.f-field-group').filter('.d-none').length !==
                                //    $(targetElement).parent().parent().find('.f-field-group').length - 1

                                if (targetElement.type !== "checkbox") {
                                    targetElement.value = ""
                                }

                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');

                                //for group control
                                //($(targetElement)?.parent()?.parent()?.find('.f-field-group')?.length > 1 && hideGroupSelectParent) ? targetElement.parentNode.classList.add('d-none') 
                                //    : targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none') 

                                let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                if (textField?.length > 0) {
                                    removeValidationWhenUncheck(textField);
                                }
                                if (selectField?.length > 0) {
                                    removeValidationWhenUncheck(selectField);
                                }
                                //}
                            }
                        });
                    });
                });
            } else {
                let field = parsedJsonData?.fields && parsedJsonData.fields[getId];

                if (field.conditions && field.conditions.length > 0) {
                    field.conditions.forEach(function (condition) {
                        condition.then.forEach(function (thenClause) {
                            condition.if.forEach(function (ifClause) {
                                let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                let sourceElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                                let sourceElementID = (`f-${ifClause.source.substring(7)}`)
                                let sourceName = document.getElementsByName(`f-${ifClause.source.substring(7)}`);

                                if (targetElement && selectedValue !== ifClause.target && ifClause.comparison !== 'notEquals' && (selectedid === sourceElementID || selectedid == sourceName)) {
                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                    let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                    let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                    if (textField?.length > 0) {
                                        removeValidationWhenUncheck(textField);
                                    }
                                    if (selectField?.length > 0) {
                                        removeValidationWhenUncheck(selectField);
                                    }
                                }

                                if (targetElement && selectedValue !== ifClause.target &&
                                    ifClause.comparison === 'notEquals' &&
                                    (selectedid === sourceElementID || selectedid == sourceName)) {
                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                    let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                    let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                    if (textField?.length > 0) {
                                        removeValidationWhenUncheck(textField);
                                    }
                                    if (selectField?.length > 0) {
                                        removeValidationWhenUncheck(selectField);
                                    }
                                }
                            });
                        });
                    });
                }
            }
        }
    }
}

function checkChildConditions(getTargetId, parsedJsonData) {



    findIsGroup.each((index, element) => {

        if (!getInputId) return;

        let modifiedInputId = getInputId.replace('f-', '');
        if (getInputType === 'checkbox' || getInputType === 'radio') {
            modifiedInputId = modifiedInputId.replace(/-0$/, '');
        }

        let getFormField = parsedJsonData?.fields?.[modifiedInputId];
        if (!getFormField) return;

        // Hide the field
        $(`#${getInputId}`).closest('.formeo-column').addClass('d-none');

        // Reset values based on type
        if (['checkbox', 'radio'].includes(getInputType)) {
            $(`#${getInputId}`).prop("checked", false);
        } else if (getInputType === 'select') {
            $(`#${getInputId}`).val("").trigger('change');
        } else {
            $(`#${getInputId}`).val("");
        }

        // Process field conditions
        getFormField?.conditions?.forEach((cond) => {
            let sourceField = cond?.if?.[0]?.source;
            if (!sourceField || sourceField !== `fields.${modifiedInputId}`) return;

            let thenElement = cond?.then?.[0]?.target?.substring(7);
            let targetElement = thenElement ? document.getElementById(`f-${thenElement}`) : null;

            if (targetElement) {
                $(targetElement).closest('.formeo-column').addClass('d-none');
                checkChildConditions(thenElement, parsedJsonData);
            }
        });
    });
}


function checkChildConditions(getTargetId, parsedJsonData) {

    if (getTargetId) {

        let findIsGroup = $(`#f-${getTargetId}`)?.closest('.formeo-column')?.find('.f-field-group');

        if (findIsGroup?.length) {

            findIsGroup?.each((index, element) => {

                let getInput = $(element)?.find('input, select, textarea');
                let getInputId = getInput.attr('id');
                let getInputType = getInput.attr('type');

                let modifiedInputId = getInputId ? getInputId.replace('f-', '') : '';

                if (getInputType == 'checkbox' || getInputType == 'radio') modifiedInputId = modifiedInputId?.replace(/-0$/, '');

                if (modifiedInputId) {
                    let getFormField = parsedJsonData && parsedJsonData.fields[modifiedInputId]

                    if (getFormField) {

                        $(`#${getInputId}`).closest('.formeo-row-wrap').addClass('d-none');

                        if (['checkbox', 'radio'].includes(getInputType)) {
                            $(`#${getInputId}`).prop("checked", false);
                        } else if (getInputType === 'select') {
                            $(`#${getInputId}`).val("").trigger('change')
                        } else {
                            $(`#${getInputId}`).val("")
                        }

                        if (getInputType == 'checkbox' || getInputType == 'radio' || getInputType == 'select') {
                            getFormField?.conditions && getFormField?.conditions?.length && getFormField?.conditions?.forEach((cond, cIndex) => {

                                let sourceField = cond?.if?.[0]?.source;
                                if (!sourceField || sourceField !== `fields.${modifiedInputId}`) return;

                                let thenElement = cond?.then?.[0]?.target?.substring(7);
                                let targetElement = thenElement ? document.getElementById(`f-${thenElement}`) : null;

                                if (targetElement) {
                                    $(targetElement).closest('.formeo-row-wrap').addClass('d-none');
                                    checkChildConditions(thenElement, parsedJsonData);
                                }

                            })
                        }
                    }
                }
            });

        }
    }
}

function removeValidationWhenUncheck(targets) {
    targets.forEach(function (target) {
        target.parentNode.removeChild(target);
    });
}

function databaseFormBuilderConditions(event, field, parsedJsonData, selectedValue, selectedid) {
    if (field?.conditions && field?.conditions?.length > 0) {
        let isVisible = false;
        field.conditions.forEach(function (condition) {
            let isMatchingCondition = condition.if.some(function (ifClause) {
                sourceField = parsedJsonData.fields[ifClause.source.substring(7)];
                return ifClause.target === selectedValue;
            });
            if (isMatchingCondition) {
                isVisible = true;
            }
        });
        field.conditions.forEach(function (condition) {
            condition.then.forEach(function (thenClause) {
                condition.if.forEach(function (ifClause) {
                    let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                    let targetElementChk = document.getElementById(`f-${thenClause.target.substring(7)}-0`);
                    let srcElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                    if (targetElement) {
                        if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {
                            if (isVisible) {
                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                            }
                            if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                if (targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) return false;
                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add("d-none");
                                let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                if (textField?.length > 0) {
                                    removeValidationWhenUncheck(textField);
                                }
                                if (selectField?.length > 0) {
                                    removeValidationWhenUncheck(selectField);
                                }
                            }
                        }
                        else if (ifClause.comparison === "notEquals") {

                            if (targetElement && event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                if (textField?.length > 0) {
                                    removeValidationWhenUncheck(textField);
                                }
                                if (selectField?.length > 0) {
                                    removeValidationWhenUncheck(selectField);
                                }
                            }
                            else if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                            }
                        }
                    }
                    else if (targetElementChk) {
                        if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {
                            if (isVisible) {
                                targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                            }
                            if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                if (targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) return false;
                                targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.add("d-none");
                                let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                if (textField?.length > 0) {
                                    removeValidationWhenUncheck(textField);
                                }
                                if (selectField?.length > 0) {
                                    removeValidationWhenUncheck(selectField);
                                }
                            }
                        }
                        else if (ifClause.comparison === "notEquals") {

                            if (targetElementChk && event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                if (textField?.length > 0) {
                                    removeValidationWhenUncheck(textField);
                                }
                                if (selectField?.length > 0) {
                                    removeValidationWhenUncheck(selectField);
                                }
                            }
                            else if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                            }
                        }
                    }
                });
            });
        });
    }
}

function formBuilderDBCondition(event, parsedJsonData) {
    let selectedValue = event.target.value;
    let selectedid = event.target.id;
    let typ = event.target.type;
    let checked = event.target.checked;
    let getId = selectedid.replace('f-', '');

    if (typ === "radio" || typ === "checkbox") {

        let replacedId = getId.replace(/-0$/, '');
        let field = parsedJsonData?.fields && parsedJsonData?.fields[replacedId];

        //if comment this password field is visible so dont comment this code.
        if (selectedValue === 'IsRac' && checked) {
            setTimeout(() => {
                databaseFormBuilderConditions(event, field, parsedJsonData, selectedValue, selectedid);
            }, 150)
        }

        databaseFormBuilderConditions(event, field, parsedJsonData, selectedValue, selectedid);
    };

    if (typ === "select-one") {
        Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
            let field = parsedJsonData.fields[fieldId];

            if (field.conditions && field.conditions.length > 0) {
                let isMatchingCondition = field.conditions.some(function (condition) {
                    return condition.if.some(function (ifClause) {
                        if (ifClause.source === `fields.${fieldId}` && ifClause.target === selectedValue) {
                            return true;
                        }
                    });
                });

                if (isMatchingCondition) {
                    field.conditions.forEach(function (condition) {
                        condition.then.forEach(function (thenClause) {
                            condition.if.forEach(function (ifClause) {
                                let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                if (targetElement && thenClause.targetProperty === 'isVisible') {
                                    if (ifClause.target === selectedValue && thenClause.assignment === 'equals' && targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none")) {
                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                        correctElementId = targetElement.id
                                    } else if (ifClause.target !== selectedValue) {
                                        targetElement.value = ""
                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                        let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                        let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                        if (textField?.length > 0) {
                                            removeValidationWhenUncheck(textField);
                                        }
                                        if (selectField?.length > 0) {
                                            removeValidationWhenUncheck(selectField);
                                        }
                                    }
                                }
                            });
                        });
                    });
                } else {
                    Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                        let field = parsedJsonData.fields[fieldId];

                        if (field.conditions && field.conditions.length > 0) {
                            field.conditions.forEach(function (condition) {
                                condition.then.forEach(function (thenClause) {
                                    condition.if.forEach(function (ifClause) {
                                        let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                        let sourceElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                                        let sourceName = document.getElementsByName(`f-${ifClause.source.substring(7)}`);
                                        if (targetElement && selectedValue !== ifClause.target && ifClause.comparison !== 'notEquals' && (selectedid === sourceElement || selectedid == sourceName)) {
                                            targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                            let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                            let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                            if (textField?.length > 0) {
                                                removeValidationWhenUncheck(textField);
                                            }
                                            if (selectField?.length > 0) {
                                                removeValidationWhenUncheck(selectField);
                                            }
                                        }
                                    });
                                });
                            });
                        }
                    });
                }
            }
        });
    }
}

function formBuilderSSOCondition(event, parsedJsonData) {
    let selectedValue = event.target.value;
    let selectedid = event.target.id;
    let typ = event.target.type
    if (typ === "radio" || typ === "checkbox") {
        Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
            let field = parsedJsonData.fields[fieldId];

            if (field.conditions && field.conditions.length > 0) {
                let isVisible = false;
                field.conditions.forEach(function (condition) {
                    let isMatchingCondition = condition.if.some(function (ifClause) {
                        sourceField = parsedJsonData.fields[ifClause.source.substring(7)];
                        return ifClause.target === selectedValue;
                    });
                    if (isMatchingCondition) {
                        isVisible = true;
                    }
                });
                field.conditions.forEach(function (condition) {
                    condition.then.forEach(function (thenClause) {
                        condition.if.forEach(function (ifClause) {
                            let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                            let targetElementChk = document.getElementById(`f-${thenClause.target.substring(7)}-0`);
                            let srcElement = document.getElementById(`f-${ifClause.source.substring(7)}`);

                            if (targetElement) {
                                if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {
                                    if (isVisible) {
                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                    }
                                    if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                        if (targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) return false;
                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.add("d-none");
                                        let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                        let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                        if (textField?.length > 0) {
                                            removeValidationWhenUncheck(textField);
                                        }
                                        if (selectField?.length > 0) {
                                            removeValidationWhenUncheck(selectField);
                                        }
                                    }
                                }
                                else if (ifClause.comparison === "notEquals") {

                                    if (targetElement && event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                        let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                        let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                        if (textField?.length > 0) {
                                            removeValidationWhenUncheck(textField);
                                        }
                                        if (selectField?.length > 0) {
                                            removeValidationWhenUncheck(selectField);
                                        }
                                    }
                                    else if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                    }
                                }
                            }
                            else if (targetElementChk) {

                                if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {
                                    if (isVisible) {
                                        targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                    }
                                    if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                        if (targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) return false;
                                        targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.add("d-none");
                                        let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                        let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                        if (textField?.length > 0) {
                                            removeValidationWhenUncheck(textField);
                                        }
                                        if (selectField?.length > 0) {
                                            removeValidationWhenUncheck(selectField);
                                        }
                                    }
                                }
                                else if (ifClause.comparison === "notEquals") {

                                    if (targetElementChk && event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                        targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                        let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                        let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                        if (textField?.length > 0) {
                                            removeValidationWhenUncheck(textField);
                                        }
                                        if (selectField?.length > 0) {
                                            removeValidationWhenUncheck(selectField);
                                        }
                                    }
                                    else if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                        targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                                    }
                                }
                            }
                        });
                    });
                });
            }
        });
    };

    if (typ === "select-one") {
        Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
            let field = parsedJsonData.fields[fieldId];

            if (field.conditions && field.conditions.length > 0) {
                let isMatchingCondition = field.conditions.some(function (condition) {
                    return condition.if.some(function (ifClause) {
                        if (ifClause.source === `fields.${fieldId}` && ifClause.target === selectedValue) {
                            return true;
                        }
                    });
                });

                if (isMatchingCondition) {
                    field.conditions.forEach(function (condition) {
                        condition.then.forEach(function (thenClause) {
                            condition.if.forEach(function (ifClause) {
                                let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);

                                if (targetElement && thenClause.targetProperty === 'isVisible') {
                                    if (ifClause.target === selectedValue && thenClause.assignment === 'equals' && targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none")) {
                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                        correctElementId = targetElement.id
                                    } else if (ifClause.target !== selectedValue) {
                                        targetElement.value = ""
                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                        let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                        let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                        if (textField?.length > 0) {
                                            removeValidationWhenUncheck(textField);
                                        }
                                        if (selectField?.length > 0) {
                                            removeValidationWhenUncheck(selectField);
                                        }
                                    }
                                }
                            });
                        });
                    });
                } else {
                    Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                        let field = parsedJsonData.fields[fieldId];

                        if (field.conditions && field.conditions.length > 0) {
                            field.conditions.forEach(function (condition) {
                                condition.then.forEach(function (thenClause) {
                                    condition.if.forEach(function (ifClause) {
                                        let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                        let sourceElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                                        let sourceName = document.getElementsByName(`f-${ifClause.source.substring(7)}`);

                                        if (targetElement && selectedValue !== ifClause.target && ifClause.comparison !== 'notEquals' && (selectedid === sourceElement || selectedid == sourceName)) {
                                            targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                            let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                            let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                            if (textField?.length > 0) {
                                                removeValidationWhenUncheck(textField);
                                            }
                                            if (selectField?.length > 0) {
                                                removeValidationWhenUncheck(selectField);
                                            }
                                        }
                                    });
                                });
                            });
                        }
                    });
                }
            }
        });
    }
}
