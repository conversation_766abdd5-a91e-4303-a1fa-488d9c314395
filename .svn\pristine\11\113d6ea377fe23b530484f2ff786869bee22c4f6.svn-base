﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class JobRepository : BaseRepository<Job>, IJobRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public JobRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<Job>> ListAllAsync()
    {
        var jobDto =await ToFilterJob(MappingJob(base.QueryAll(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)))).ToListAsync();

        return jobDto;
    }
    public Task<bool> IsJobNameUnique(string name)
    {
        var matches = _dbContext.Jobs.Any(e => e.Name.Equals(name));

        return Task.FromResult(matches);
    }

    public async Task<List<Job>>GetBySolutionTypeId(string solutiontypeId)
    { 
        var result= await  _dbContext.Jobs.Active().Where(j=> j.SolutionTypeId.Equals(solutiontypeId) && j.CompanyId.Equals(_loggedInUserService.CompanyId)).ToListAsync();

        return result;
    }
    public async Task<List<string>> GetInfraObjectPropertyBySolutiontypeId(string solutiontypeId)
    { 
        var result= await  _dbContext.Jobs.Active().Where(j=> j.SolutionTypeId.Equals(solutiontypeId) && j.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(x=>x.InfraObjectProperties).ToListAsync();

        return result;
    }

    public async Task<List<Job>> GetByPolicy(string policy)
    {
        var result = await _dbContext.Jobs.Active().Where(j => j.ExecutionPolicy.Equals(policy) && j.CompanyId.Equals(_loggedInUserService.CompanyId)).ToListAsync();

        return result;
    }
    public override async Task<PaginatedResult<Job>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<Job> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await (_loggedInUserService.IsParent
            ? ToFilterJob(MappingJob(Entities.Specify(productFilterSpec).DescOrderById()))
            : ToFilterJob(MappingJob(Entities.Specify(productFilterSpec).Where(x=>x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()))).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
        
    }
    public override IQueryable<Job> GetPaginatedQuery()
    {
        var jobs = base.QueryAll(x => x.CompanyId.Equals(_loggedInUserService.CompanyId));

        var jobDto = MappingJob(jobs);

        return jobDto.AsNoTracking().OrderByDescending(x => x.Id);
    }

    public override async Task<Job> GetByReferenceIdAsync(string id)
    {
        var jobs = base.GetByReferenceId(id,
                job => job.CompanyId.Equals(_loggedInUserService.CompanyId) && job.ReferenceId.Equals(id));

        return await Task.FromResult(jobs.FirstOrDefault());
    }

    public Task<bool> IsJobNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.Jobs.Any(e => e.Name.Equals(name)))
            : Task.FromResult(_dbContext.Jobs.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }


    public async Task<List<Job>> GetJobsByInfraObjectId(string infraObjectId)
    {
        //var jobs = _loggedInUserService.IsParent
        //    ? base.FilterBy(x => x.InfraObjectProperties.Contains(infraObjectId))
        //    : base.FilterBy(x => x.InfraObjectProperties.Contains(infraObjectId) && x.CompanyId.Equals(_loggedInUserService.CompanyId));

        //var jobDto = MappingJob(jobs);

        //return await jobDto.ToListAsync(); 
        
        var jobDto =await  ToFilterJob(MappingJob(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectProperties.Contains(infraObjectId))
            : base.FilterBy(x => x.InfraObjectProperties.Contains(infraObjectId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)))).ToListAsync();

        return  jobDto;
    }

    //public async Task<List<Job>> GetJobsListByInfraObjectId(string infraObjectId)
    //{
    //    return await _dbContext.Jobs.Active()
    //        .Where(job => job.InfraObjectProperties.Contains(infraObjectId)).ToListAsync();
    //}

    public async Task<List<Job>> GetJobsListByTemplateId(string templateId)
    {
        var jobs = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.TemplateId.Equals(templateId))
            : base.FilterBy(x => x.TemplateId.Equals(templateId) && x.CompanyId.Equals(_loggedInUserService.CompanyId));

        var jobDto = MappingJob(jobs);

        return await jobDto.ToListAsync();
    }

    public async Task<Job> GetJobByTemplateId(string templateId)
    {
        var jobs = await ToFilterJob(MappingJob(_loggedInUserService.IsParent
           ? base.FilterBy(x => x.TemplateId.Equals(templateId))
           : base.FilterBy(x => x.TemplateId.Equals(templateId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)))).FirstOrDefaultAsync();

        return jobs;
    }

    public async Task<List<Job>> GetJobByIds(List<string> templateId)
    {
        var jobs = _loggedInUserService.IsParent
           ? await base.FilterBy(x => templateId.Contains(x.TemplateId))
           .Select(x=> new Job
           {
               ReferenceId = x.ReferenceId,
               Name = x.Name,
               TemplateId = x.TemplateId

           }).ToListAsync()
           : await base.FilterBy(x => templateId.Contains(x.TemplateId) && x.CompanyId.Equals(_loggedInUserService.CompanyId))
           .Select(x => new Job
           {
               ReferenceId = x.ReferenceId,
               Name = x.Name,
               TemplateId = x.TemplateId

           }).ToListAsync();

        return jobs;
    }

    public async Task<List<Job>> GetJobByGroupNodePolicyId(string groupPolicyId)
    {
        var jobs = await ToFilterJob(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.GroupPolicyId.Equals(groupPolicyId))
            :base.FilterBy(x=>x.GroupPolicyId.Equals(groupPolicyId) &&x.CompanyId.Equals(_loggedInUserService.CompanyId))).ToListAsync();
        return jobs;
    }
    public async Task<List<Job>> GetJobNames()
    {
        var jobs = base
            .QueryAll(job => job.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new Job { ReferenceId = x.ReferenceId, Name = x.Name });

        return await jobs.ToListAsync();
    }

    private IQueryable<Job> MappingJob(IQueryable<Job> jobs)
    {
        var mappedJobs = jobs.Select(data => new
        {
            Job = data,
            Template = _dbContext.Templates.Active().AsNoTracking().Select(temp=> new Template{ReferenceId=temp.ReferenceId,Name=temp.Name }).FirstOrDefault(x => x.ReferenceId.Equals(data.TemplateId)), 
            ComponentType = _dbContext.ComponentTypes.Active().AsNoTracking().Select(comp => new ComponentType { ReferenceId = comp.ReferenceId, ComponentName = comp.ComponentName }).FirstOrDefault(x => x.ReferenceId.Equals(data.SolutionTypeId)),
            GroupPolicy = _dbContext.GroupPolicies.Active().AsNoTracking().Select(group => new GroupPolicy { ReferenceId = group.ReferenceId,GroupName = group.GroupName }).FirstOrDefault(x => x.ReferenceId.Equals(data.GroupPolicyId)),
        });

        var mappedJobQuery = mappedJobs.Select(res => new Job
        {
            Id = res.Job.Id,
            ReferenceId = res.Job.ReferenceId,
            Name = res.Job.Name,
            InfraObjectProperties = res.Job.InfraObjectProperties,
            TemplateId = res.Template.ReferenceId,
            TemplateName = res.Template.Name,
            SolutionTypeId = res.ComponentType.ReferenceId,
            SolutionType = res.ComponentType.ComponentName ?? res.Job.SolutionType,
            NodeId = res.Job.NodeId,
            NodeName = res.Job.NodeName,
            Status = res.Job.Status,
            CronExpression = res.Job.CronExpression,
            GroupPolicyId = res.GroupPolicy.ReferenceId,
            GroupPolicyName = res.GroupPolicy.GroupName,
            IsSchedule = res.Job.IsSchedule,
            ScheduleType = res.Job.ScheduleType,
            ScheduleTime = res.Job.ScheduleTime,
            Type = res.Job.Type,
            State = res.Job.State,
            ExecutionPolicy = res.Job.ExecutionPolicy,
            ExceptionMessage= res.Job.ExceptionMessage,
            IsActive = res.Job.IsActive,
            CreatedBy = res.Job.CreatedBy,
            CreatedDate = res.Job.CreatedDate,
            LastModifiedBy = res.Job.LastModifiedBy,
            LastModifiedDate = res.Job.LastModifiedDate,
            LastExecutionTime=res.Job.LastExecutionTime
        });

        return mappedJobQuery;
    }

    private IQueryable<Job>ToFilterJob(IQueryable<Job> jobs)
    {
        return jobs.Select(j => new Job
        {
            Id = j.Id,
            ReferenceId = j.ReferenceId,
            Name = j.Name,
            InfraObjectProperties = j.InfraObjectProperties,
            TemplateId = j.TemplateId,
            TemplateName = j.TemplateName,
            SolutionTypeId = j.SolutionTypeId,
            SolutionType = j.SolutionType,
            NodeId = j.NodeId,
            NodeName = j.NodeName,
            Status = j.Status,
            CronExpression = j.CronExpression,
            GroupPolicyId = j.GroupPolicyId,
            GroupPolicyName = j.GroupPolicyName,
            IsSchedule = j.IsSchedule,
            ScheduleType = j.ScheduleType,
            ScheduleTime = j.ScheduleTime,
            Type = j.Type,
            State = j.State,
            ExecutionPolicy = j.ExecutionPolicy,
            ExceptionMessage = j.ExceptionMessage,
            LastExecutionTime = j.LastExecutionTime
        });
    }

  
}