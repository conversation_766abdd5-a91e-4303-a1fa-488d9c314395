﻿using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.UpdateConditionActionId;
using ContinuityPatrol.Application.Features.CGExecutionReport.Queries.GetCgExecutionReportPaginatedList;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Cyber;

public class CGExecutionReportService : BaseClient, ICGExecutionReportService
{
    public CGExecutionReportService(IConfiguration config, IAppCache cache, ILogger<CGExecutionReportService> logger)
       : base(config, cache, logger)
    {
    }

    public async Task<PaginatedResult<CgExecutionPaginatedListVm>> GetCgExecutionPaginatedList(GetCgExecutionPaginatedListQuery query)
    {
        var request = new RestRequest($"api/v6/cgexecutionreport/paginated-list{query}");

        request.AddJsonBody(query);

        return await Get<PaginatedResult<CgExecutionPaginatedListVm>>(request);

    }

    public async Task<BaseResponse> Update(ResiliencyReadinessWorkflowScheduleLogCommand workflowScheduleLogCommand)
    {
        var request = new RestRequest("api/v6/cgexecutionreport/update-currentactionid", Method.Put);

        request.AddJsonBody(workflowScheduleLogCommand);

        return await Put<BaseResponse>(request);
    }
}
