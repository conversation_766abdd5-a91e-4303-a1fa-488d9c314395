﻿
function monitorTypePostgres(value, infraObjectName, moniterType, parsedData) {
    let prWfName = [], drWfName = [];
    let prStatusArr = [], drStatusArr = [];
    let prWfDisplay = '--', drWfDisplay = '--';
    let prStatusDisplay = '--', drStatusDisplay = '--';
    let iconWF = '', iconStatus = '';
    let monitor = value?.monitorServiceDetails;

    if (value?.monitorServiceDetails?.length > 0) {
        value?.monitorServiceDetails?.forEach(list => {
            let parsed = [];
            const isValidJson = list?.isServiceUpdate && Array.isArray(list?.isServiceUpdate)

            if (isValidJson) {
                try {
                    parsed = JSON?.parse(list?.isServiceUpdate);
                } catch (err) {
                    console.warn('Invalid JSON in isServiceUpdate:', list?.isServiceUpdate);
                    parsed = [];
                }
            }
            parsed?.forEach(entry => {
                entry?.Services?.forEach(service => {
                    if (entry?.Type?.toLowerCase() === 'pr') {
                        if (service?.ServiceName && service?.ServiceName !== 'NA') {
                            prWfName.push(service?.ServiceName);
                        }
                        if (service?.Status && service?.Status !== 'NA') {
                            prStatusArr.push(service?.Status);
                        }
                    } else if (entry?.Type?.toLowerCase() === 'dr') {
                        if (service?.ServiceName && service?.ServiceName !== 'NA') {
                            drWfName.push(service?.ServiceName);
                        }
                        if (service?.Status && service?.Status !== 'NA') {
                            drStatusArr.push(service?.Status);
                        }
                    }
                });
            });
        });

        // Unique workflow names
        prWfDisplay = prWfName?.length > 0 ? [...new Set(prWfName)].join(', ') : '--';
        drWfDisplay = drWfName?.length > 0 ? [...new Set(drWfName)].join(', ') : '--';

        // Status summary
        function getStatusSummary(arr) {
            let countMap = {};
            arr?.forEach(status => {
                countMap[status] = (countMap[status] || 0) + 1;
            });
            let total = arr?.length;
            let statusSummary = Object.entries(countMap)
                .map(([status, count]) => `${count} ${status}`)
                .join(', ');
            return statusSummary ? `${statusSummary} / ${total}` : '--';
        }

        prStatusDisplay = getStatusSummary(prStatusArr);
        drStatusDisplay = getStatusSummary(drStatusArr);

        iconWF = (prWfDisplay !== '--' || drWfDisplay !== '--') ? '<i class="text-primary cp-monitoring-services me-1 fs-6"></i>' : '';

        iconStatus = (prStatusDisplay !== '--' || drStatusDisplay !== '--') ? '<i class="text-primary cp-Job-status me-1 fs-6"></i>' : '';

    }

    const getDRDetails = (data, value, obj = null) => {
        
        let tdHtml = '';
        data.forEach((item, i) => {
            let iconClass = getIconClass(value, item);
            let tableData = obj ? item?.MonitoringModel[obj][value] : item?.MonitoringModel[value];

            tdHtml += `<td class="text-truncate"><i class="${iconClass} me-1"></i>${tableData || 'NA'}</td>`
        })
        return tdHtml
    }
    const getIconClass = (value, monitoringData) => {
        let iconClass = '';

        if (value === 'Server_IpAddress' || value === 'Server_HostName') {
            let text = monitoringData?.MonitoringModel?.Server_Status?.toLowerCase()
            iconClass = text === 'down' ? "cp-down-linearrow me-1 text-danger" : text === 'pending' ? 'cp-pending me-1 text-warning' : text === 'up' ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";

        } else if (value === 'Database') {

            iconClass = monitoringData?.MonitoringModel?.Database ? 'cp-database me-1 text-primary' : "cp-disable me-1 text-danger";

        } else if (value === 'DatabaseClusterStatus') {
            iconClass = monitoringData?.MonitoringModel?.DatabaseClusterStatus ? "cp-log-file-name me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'RecoveryStatus') {
            iconClass = monitoringData?.MonitoringModel?.RecoveryStatus?.toLowerCase() === "running" ? "cp-reload cp-animate me-1 text-success" : monitoringData?.MonitoringModel?.RecoveryStatus?.toLowerCase() === "paused" ? "cp-circle-pause me-1 text-warning" : monitoringData?.MonitoringModel?.RecoveryStatus?.toLowerCase() === 'enabled' ? "text-success cp-enables me-1" : monitoringData?.MonitoringModel?.RecoveryStatus?.toLowerCase() === 'disabled' ? "text-danger cp-disables me-1" : "cp-disable me-1 text-danger";

        } else if (value === 'ReplicationStatus') {
            iconClass = monitoringData?.MonitoringModel?.ReplicationStatus?.includes("NA") ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.ReplicationStatus?.toLowerCase()?.includes("stopped") ? "cp-log-file-name me-1 text-primary" : monitoringData?.MonitoringModel?.ReplicationStatus?.toLowerCase()?.includes("streaming") ? "cp-refresh me-1 text-primary" : "text-success cp-reload cp-animate";

        } else if (value === 'CurrentWalLsn') {
            iconClass = monitoringData?.MonitoringModel?.CurrentWalLsn?.toLowerCase()?.includes('na') ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.CurrentWalLsn ? "cp-file-location me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'LastWalReceiveLsn') {
            iconClass = monitoringData?.MonitoringModel?.LastWalReceiveLsn ? "cp-connected me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'LastWalReplayLsnDR') {
            iconClass = monitoringData?.MonitoringModel?.LastWalReplayLsnDR ? "cp-time text-primary mt-2" : "cp-disable me-1 text-danger";

        }
        else if (value === 'PR_Datalag') {
            iconClass = monitoringData.MonitoringModel?.PR_Datalag ? "cp-time me-1 text-primary" : "cp-disable me-1 text-danger";

        }
        return iconClass;
    }

    const getDynamicHeader = (PostgresMonitoringModels) => {
        
        let dynamicHeader = '';

        PostgresMonitoringModels?.length && PostgresMonitoringModels?.map((data) => {
            dynamicHeader += `<th>${data?.Type}</th>`
        })

        return dynamicHeader;
    }
    if (moniterType === "Postgres") {
        
        let ipOrHostName;
        let application = value?.monitorServiceDetails[0]?.isServiceUpdate?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.isServiceUpdate?.toLowerCase()?.includes("running") ? "text-success cp-reload cp-animate" : "text-danger cp-fail-back";
        let workflow = value?.monitorServiceDetails[0]?.workflowName?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.workflowName ? "cp-log-archive-config me-1 text-primary" : "cp-disable me-1 text-danger";
        let prreplicationStatus = parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_ReplicationStatus?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_ReplicationStatus?.toLowerCase()?.includes("stopped") ? "cp-Stopped me-1 text-primary" : parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_ReplicationStatus?.toLowerCase()?.includes("streaming") ? "cp-refresh me-1 text-primary" : "text-success cp-reload cp-animate";
        let prlogLocation = parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.CurrentWalLsnPR?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.CurrentWalLsnPR ? "cp-file-location me-1 text-primary" : "cp-disable me-1 text-danger"  
        let prrecoverystate = (parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_RecoveryStatus)?.toLowerCase() === "running" ? "cp-reload cp-animate me-1 text-success" : (parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_RecoveryStatus)?.toLowerCase() === "paused" ? "cp-circle-pause me-1 text-warning" : (parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_RecoveryStatus)?.toLowerCase() === 'enabled' ? "text-success cp-enables me-1" : (parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_RecoveryStatus)?.toLowerCase() === 'disabled' ? "text-danger cp-disables me-1" : "cp-disable me-1 text-danger";
        let drrecoverystate = parsedData?.DR_RecoveryStatus?.includes("NA") ? "cp-disable me-1 text-danger" : (parsedData?.DR_RecoveryStatus)?.toLowerCase() === "running" ? "cp-reload cp-animate me-1 text-success" : "cp-circle-pause me-1 text-warning";
        let pripaddress = parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_Server_Status.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : value?.prServerStatus?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : value?.prServerStatus?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";
        let dripaddress = value?.drServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : value?.drServerStatus?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : value?.drServerStatus?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";         
        let prdatalag = parsedData?.PrPostgresMonitoringModel?.MonitoringModel ?.PR_DataLagInSize?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-transport-lag me-1 text-primary"
        let prdatalagtime = parsedData?.PostgresMonitoringModel?.MonitoringModel ?.PR_Datalag?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-time me-1 text-primary"
        let ipprdata = parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.Pr_ConnectViaHostName?.toLowerCase() === "true" ? parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_Server_HostName : parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_Server_IpAddress
        let drdata = parsedData?.PostgresMonitoringModels.map((ip) => ip?.MonitoringModel?.connectViaHostName);
        parsedData?.PostgresMonitoringModels.forEach((ip, index) => {

            let isHostName = drdata[index]?.toLowerCase() === "true";
            value = isHostName ? 'Server_HostName' : 'Server_IpAddress';
            ipOrHostName = isHostName
                ? getDRDetails(parsedData?.PostgresMonitoringModels, 'Server_HostName')
                : getDRDetails(parsedData?.PostgresMonitoringModels, 'Server_IpAddress');
        });

        let infraobjectdata =
            '<div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm " style="table-layout:fixed">' +
            '<thead style="position: sticky;top: 0px;">' +
            '<tr>' +
            '<th>Component Monitor</th>' +
            '<th>Production Server</th>' +
            `${getDynamicHeader(parsedData?.PostgresMonitoringModels)}` +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td>' + 'IP Address/Host Name' + '</td>' +
            '<td>' + '<i class="' + pripaddress + '"></i>' + (ipprdata || 'NA') + '</td>' +
            `${ipOrHostName}` +  
            '</tr>' +
            '<tr>' +
            '<td>' + 'Database Name' + '</td>' +
            '<td>' + '<i class="cp-database me-1 text-primary"></i>' + (parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_Database !== undefined && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_Database !== null && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_Databasee !== "" ? parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_Database : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.PostgresMonitoringModels, 'Database')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Database Cluster State" + '</td>' +
            '<td>' + '<i class="cp-cluster-database me-1 text-primary"></i>' + (parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_DatabaseClusterStatus !== undefined && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_DatabaseClusterStatus !== null && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_DatabaseClusterStatus !== "" ? parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_DatabaseClusterStatus : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.PostgresMonitoringModels, 'DatabaseClusterStatus')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Database Recovery State" + '</td>' +
            '<td>' + '<i class="' + prrecoverystate + '"></i>' + (parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_RecoveryStatus !== undefined && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_RecoveryStatus !== null && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_RecoveryStatus !== "" ? parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_RecoveryStatus : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.PostgresMonitoringModels, 'RecoveryStatus')}` +
            '</tr>';
        if (Array.isArray(monitor) && monitor?.length > 0) {
            infraobjectdata +=
                '<tr id="prWorkflow">' +
                '<td>Monitoring workflow</td>' +
                '<td>' + iconWF + prWfDisplay + '</td>' +
                '<td>' + iconWF + drWfDisplay + '</td>' +
                '</tr>' +
                '<tr id="prStatus">' +
                '<td>Application Status</td>' +
                '<td>' + iconStatus + prStatusDisplay + '</td>' +
                '<td>' + iconStatus + drStatusDisplay + '</td>' +
                '</tr>';
        }
            infraobjectdata += generateMonitorServiceDetailsRow(workflow, application, value?.monitorServiceDetails);

        infraobjectdata += '</tbody>' +
            '</table>' +
            '</div>' +
            '<div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm " style="table-layout:fixed">' +
            '<thead style="position: sticky;top: 0px;z-index: 1;">' +
            '<tr>' +
            '<th>Replication Monitor</th>' +
            '<th>Production Server</th>' +
            `${getDynamicHeader(parsedData?.PostgresMonitoringModels)}` +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td>' + "Replication Status" + '</td>' +
            '<td>' + '<i class="' + prreplicationStatus + '"></i>' + (parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_ReplicationStatus !== undefined && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_ReplicationStatus !== null && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_ReplicationStatus !== "" ? parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_ReplicationStatus : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.PostgresMonitoringModels, 'ReplicationStatus')}` +
            '</tr>' +
            '<tr>' + 
            '<td>' + "Current WAL log location" + '</td>' +
            '<td>' + '<i class="'+prlogLocation+'"></i>' + (parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.CurrentWalLsnPR !== undefined && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.CurrentWalLsnPR !== null && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.CurrentWalLsnPR !== "" ? parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.CurrentWalLsnPR : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.PostgresMonitoringModels, 'CurrentWalLsn')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Last WAL log receive location" + '</td>' +
            '<td>' + '<i class="cp-disable me-1 text-danger"></i>' + (parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_LastWalReceiveLsn !== undefined && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_LastWalReceiveLsn !== null && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_LastWalReceiveLsn !== "" ? parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_LastWalReceiveLsn : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.PostgresMonitoringModels, 'LastWalReceiveLsn')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Last WAL log replay location" + '</td>' +
            '<td>' + '<i class="cp-disable me-1 text-danger"></i>' + (parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_LastWalReplayLsnPR !== undefined && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_LastWalReplayLsnPR !== null && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_LastWalReplayLsnPR !== "" ? parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_LastWalReplayLsnPR : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.PostgresMonitoringModels, 'LastWalReplayLsnDR')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "DataLag (in MB)" + '</td>' +
            '<td>' + '<i class="' + prdatalag  + '"></i>' + (parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_DataLagInSize !== undefined && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_DataLagInSize !== null && parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_DataLagInSize !== "" ? parsedData?.PrPostgresMonitoringModel?.MonitoringModel?.PR_DataLagInSize : 'NA') + '</td>' +
            '<tr>' + 
            '<td>' + "DataLag (hh:mm:ss)" + '</td>' +
            `${getDRDetails(parsedData?.PostgresMonitoringModels, 'PR_Datalag')}` +
            '</tr>' +
            '</tbody>' +
            '</table>' +
            '</div>' 
           


        setTimeout(() => {
            $("#infraobjectalldata").append(infraobjectdata);
        }, 200)


    }
}
