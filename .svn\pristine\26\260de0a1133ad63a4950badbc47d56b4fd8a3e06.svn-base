﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.MssqlNativeLogShippingMonitorLog.Queries.GetPaginatedList;

public class GetMssqlNativeLogShippingMonitorLogPaginatedListQueryHandler : IRequestHandler<
    GetMssqlNativeLogShippingMonitorLogPaginatedListQuery,
    PaginatedResult<MssqlNativeLogShippingMonitorLogPaginatedListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMssqlNativeLogShippingMonitorLogRepository _mssqlNativeLogShippingMonitorLogRepository;

    public GetMssqlNativeLogShippingMonitorLogPaginatedListQueryHandler(
        IMssqlNativeLogShippingMonitorLogRepository mssqlNativeLogShippingMonitorLogRepository, IMapper mapper)
    {
        _mssqlNativeLogShippingMonitorLogRepository = mssqlNativeLogShippingMonitorLogRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<MssqlNativeLogShippingMonitorLogPaginatedListVm>> Handle(
        GetMssqlNativeLogShippingMonitorLogPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _mssqlNativeLogShippingMonitorLogRepository.GetPaginatedQuery();

        var productFilterSpec = new MsSqlNativeLogShippingMonitorLogFilterSpecification(request.SearchString);

        var mssqlNativeLogShippingMonitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<MssqlNativeLogShippingMonitorLogPaginatedListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return mssqlNativeLogShippingMonitorLog;
    }
}