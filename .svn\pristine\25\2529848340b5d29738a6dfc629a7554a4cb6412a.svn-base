using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ServerTypeFixture : IDisposable
{
    public List<ServerType> ServerTypePaginationList { get; set; }
    public List<ServerType> ServerTypeList { get; set; }
    public ServerType ServerTypeDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ServerTypeFixture()
    {
        var fixture = new Fixture();

        ServerTypeList = fixture.Create<List<ServerType>>();

        ServerTypePaginationList = fixture.CreateMany<ServerType>(20).ToList();

        ServerTypeDto = fixture.Create<ServerType>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
