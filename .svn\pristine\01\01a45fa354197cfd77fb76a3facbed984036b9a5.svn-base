﻿using ContinuityPatrol.Application.Features.SiteType.Commands.Create;
using ContinuityPatrol.Application.Features.SiteType.Commands.Update;
using ContinuityPatrol.Application.Features.SiteType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SiteTypeModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Application.Features.SiteType.Events.PaginatedView;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class SiteTypeController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IDataProvider _provider;
    private readonly IMapper _mapper;
    private readonly ILogger<SiteTypeController> _logger;


    public SiteTypeController(IPublisher publisher, IMapper mapper, IDataProvider provider, ILogger<SiteTypeController> logger)
    {
        _publisher = publisher;
        _provider = provider;
        _logger = logger;
        _mapper = mapper;
    }

    [HttpGet]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in SiteType");

        await _publisher.Publish(new SiteTypePaginatedEvent());
       // var siteTypeList = await _provider.SiteType.GetSiteTypeList();

        //var list = new SiteTypeListModel
        //{
        //    SiteTypes = siteTypeList
        //};

        return View();
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(SiteTypeListModel siteType)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in SiteType");

        var siteTypeId = Request.Form["id"].ToString();

        try
        {
            if (siteTypeId.IsNullOrWhiteSpace())
            {
                _logger.LogDebug($"Creating SiteType '{siteType.Type}'");
                var siteTypeCreateCommand = _mapper.Map<CreateSiteTypeCommand>(siteType);
                var result = await _provider.SiteType.CreateAsync(siteTypeCreateCommand);

                TempData.NotifySuccess(result.Message);

            }
            else
            {
                _logger.LogDebug($"Updating SiteType '{siteType.Type}'");
                var siteTypeUpdateCommand = _mapper.Map<UpdateSiteTypeCommand>(siteType);
                var result = await _provider.SiteType.UpdateAsync(siteTypeUpdateCommand);

                TempData.NotifySuccess(result.Message);

            }

            return RedirectToAction("List");
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on SiteType page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on SiteType page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());
            return RedirectToAction("List");
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetSiteTypeList()
    {
        _logger.LogDebug("Entering GetSiteTypeList method in SiteType");

        try
        {
            var result = await _provider.SiteType.GetSiteTypeList();

            _logger.LogDebug("Successfully retrieved SiteType names in SiteType");

            return Json(result);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on SiteType page while retrieving SiteType List.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<IActionResult> Delete(string id,string name)
    {
        _logger.LogDebug("Entering Delete method in SiteType");

        try
        {
            _logger.LogDebug($"Deleting SiteType Details by Id '{id}'");

            var response = await _provider.SiteType.DeleteAsync(id ,name);

            TempData.NotifySuccess(response.Message);

            return RedirectToAction("List");

        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred while deleting record on SiteType.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }


    [HttpGet]
    public async Task<bool> IsSiteTypeExist(string type, string id)
    {
        _logger.LogDebug("Entering IsSiteTypeExist method in SiteType");

        try
        {
            var nameExist = await _provider.SiteType.IsSiteTypeExist(type, id);

            _logger.LogDebug("Returning result for IsSiteTypeExist on SiteType");

            return nameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on SiteType while checking if SiteType name exists for : {type}.", ex);

            return false;
        }

      
    }

    [HttpGet]
    public async Task<JsonResult> GetPagination(GetSiteTypePaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in SiteType");

        try
        {
            var result = await _provider.SiteType.GetSiteTypePaginatedList(query);

            _logger.LogDebug("Successfully retrieved SiteType paginated list on SiteType page");

            return Json(result);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on SiteType page while processing the pagination request.", ex);

            return ex.GetJsonException();
        }
    }
}