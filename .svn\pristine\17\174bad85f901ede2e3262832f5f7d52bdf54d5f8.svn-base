﻿using ContinuityPatrol.Application.Features.TeamResource.Commands.Create;
using ContinuityPatrol.Application.Features.TeamResource.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.TeamResourceModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract
{
    public interface ITeamResourceService
    {

        Task<bool> IsTeamMemberNameExist(string teamMasterName, string id);

        Task<bool> IsTeamMemberNameUnique(string teamMasterName);

        Task<List<TeamResourceListVm>> GetTeamMasterIdByTeamMember(string teamMasterId);

        Task<List<TeamResourceListVm>> GetTeamMemberNames();

        Task<PaginatedResult<TeamResourceListVm>> GetTeamMemberList(GetTeamResourcePaginatedListQuery query);
        Task<PaginatedResult<TeamResourceListVm>> GetTeamMemberListAll(string query);

        Task<BaseResponse> CreateAsync(CreateTeamResourceCommand team);


        Task<BaseResponse> DeleteAsync(string teamId);
    }
}
