﻿namespace ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Commands.Create;

public class
    CreateMSSQLMonitorStatusCommandHandler : IRequestHandler<CreateMSSQLMonitorStatusCommand,
        CreateMSSQLMonitorStatusResponse>
{
    private readonly IMapper _mapper;
    private readonly IMssqlMonitorStatusRepository _mSSQLMonitorStatusRepository;

    public CreateMSSQLMonitorStatusCommandHandler(IMapper mapper,
        IMssqlMonitorStatusRepository mSSQLMonitorStatusRepository)
    {
        _mapper = mapper;
        _mSSQLMonitorStatusRepository = mSSQLMonitorStatusRepository;
    }

    public async Task<CreateMSSQLMonitorStatusResponse> Handle(CreateMSSQLMonitorStatusCommand request,
        CancellationToken cancellationToken)
    {
        var mssqlMonitorStatus = _mapper.Map<Domain.Entities.MSSQLMonitorStatus>(request);

        mssqlMonitorStatus = await _mSSQLMonitorStatusRepository.AddAsync(mssqlMonitorStatus);

        var response = new CreateMSSQLMonitorStatusResponse
        {
            Message = Message.Create(nameof(Domain.Entities.MSSQLMonitorStatus), mssqlMonitorStatus.ReferenceId),
            Id = mssqlMonitorStatus.ReferenceId
        };

        return response;
    }
}