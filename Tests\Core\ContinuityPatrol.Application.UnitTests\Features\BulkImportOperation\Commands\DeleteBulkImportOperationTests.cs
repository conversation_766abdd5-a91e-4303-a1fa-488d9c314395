using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperation.Commands;

public class DeleteBulkImportOperationTests : IClassFixture<BulkImportOperationFixture>
{
    private readonly BulkImportOperationFixture _bulkImportOperationFixture;
    private readonly Mock<IBulkImportOperationRepository> _mockBulkImportOperationRepository;
    private readonly Mock<IPublisher> _publisher;
    private readonly DeleteBulkImportOperationCommandHandler _handler;

    public DeleteBulkImportOperationTests(BulkImportOperationFixture bulkImportOperationFixture)
    {
        _bulkImportOperationFixture = bulkImportOperationFixture;

        _mockBulkImportOperationRepository = BulkImportOperationRepositoryMocks.CreateDeleteBulkImportOperationRepository(_bulkImportOperationFixture.BulkImportOperations);
        _publisher = new Mock<IPublisher>();
        _handler = new DeleteBulkImportOperationCommandHandler(
            _mockBulkImportOperationRepository.Object, _publisher!.Object);
    }

    [Fact]
    public async Task Handle_Return_DeleteBulkImportOperationResponse_When_BulkImportOperationDeleted()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var command = new DeleteBulkImportOperationCommand { Id = existingOperation.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(DeleteBulkImportOperationResponse));
        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var command = new DeleteBulkImportOperationCommand { Id = existingOperation.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_UpdateAsync_OnlyOnce()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var command = new DeleteBulkImportOperationCommand { Id = existingOperation.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperation>()), Times.Once);
    }

    [Fact]
    public async Task Handle_SetIsActiveToFalse_When_BulkImportOperationDeleted()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        existingOperation.IsActive = true; // Ensure it starts as active
        var command = new DeleteBulkImportOperationCommand { Id = existingOperation.ReferenceId };

        Domain.Entities.BulkImportOperation capturedOperation = null;
        
        _mockBulkImportOperationRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperation>()))
        .Callback<Domain.Entities.BulkImportOperation>(operation => capturedOperation = operation)
        .ReturnsAsync((Domain.Entities.BulkImportOperation operation) => operation);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedOperation.ShouldNotBeNull();
        capturedOperation.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportOperationNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new DeleteBulkImportOperationCommand { Id = nonExistentId };

        _mockBulkImportOperationRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportOperation)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_CreateResponseWithCorrectProperties_When_BulkImportOperationDeleted()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        existingOperation.UserName = "TestUser";
        var command = new DeleteBulkImportOperationCommand { Id = existingOperation.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();
        result.Message.ShouldContain("BulkImportOperation");
        result.Message.ShouldContain("TestUser");
    }

    [Fact]
    public async Task Handle_CallRepositoryWithCorrectId_When_QueryExecuted()
    {
        // Arrange
        var testId = Guid.NewGuid().ToString();
        var command = new DeleteBulkImportOperationCommand { Id = testId };

        _mockBulkImportOperationRepository.Setup(x => x.GetByReferenceIdAsync(testId))
            .ReturnsAsync(_bulkImportOperationFixture.BulkImportOperations.First());

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.GetByReferenceIdAsync(testId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_DeleteSuccessful()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var command = new DeleteBulkImportOperationCommand { Id = existingOperation.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<DeleteBulkImportOperationResponse>();
        result.GetType().ShouldBe(typeof(DeleteBulkImportOperationResponse));
    }

    [Fact]
    public async Task Handle_PerformSoftDelete_When_EntityExists()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        existingOperation.IsActive = true;
        var command = new DeleteBulkImportOperationCommand { Id = existingOperation.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        existingOperation.IsActive.ShouldBeFalse();
        _mockBulkImportOperationRepository.Verify(x => x.UpdateAsync(existingOperation), Times.Once);
    }

    [Fact]
    public async Task Handle_NotCallDeleteAsync_When_SoftDeletePerformed()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var command = new DeleteBulkImportOperationCommand { Id = existingOperation.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.DeleteAsync(It.IsAny<Domain.Entities.BulkImportOperation>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ReturnIsActiveFalse_When_DeleteCompleted()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var command = new DeleteBulkImportOperationCommand { Id = existingOperation.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_WithCorrectMessage_When_EntityNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new DeleteBulkImportOperationCommand { Id = nonExistentId };

        _mockBulkImportOperationRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportOperation)null);

        // Act & Assert
        var exception = await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
        
        exception.Message.ShouldContain("BulkImportOperation");
        exception.Message.ShouldContain(nonExistentId);
    }

    [Fact]
    public async Task Handle_UseGuardAgainstNull_When_EntityNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new DeleteBulkImportOperationCommand { Id = nonExistentId };

        _mockBulkImportOperationRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportOperation)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }
}
