using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ReplicationFixture : IDisposable
{
    public List<Replication> ReplicationPaginationList { get; set; }
    public List<Replication> ReplicationList { get; set; }
    public Replication ReplicationDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ReplicationFixture()
    {
        var fixture = new Fixture();

        ReplicationList = fixture.Create<List<Replication>>();

        ReplicationPaginationList = fixture.CreateMany<Replication>(20).ToList();

        ReplicationPaginationList.ForEach(x => x.CompanyId = CompanyId);

        ReplicationList.ForEach(x => x.CompanyId = CompanyId);

        ReplicationDto = fixture.Create<Replication>();

        ReplicationDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
