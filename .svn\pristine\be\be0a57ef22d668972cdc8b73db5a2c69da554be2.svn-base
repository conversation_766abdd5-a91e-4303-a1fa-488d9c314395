﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class TeamResourceRepository : BaseRepository<TeamResource>, ITeamResourceRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public TeamResourceRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    /*  public override Task<IReadOnlyList<TeamResource>> ListAllAsync()
      {
          return _loggedInUserService.IsParent
              ? base.ListAllAsync()
              : FindByFilterAsync(teamResource => teamResource.TeamMasterName.Equals(_loggedInUserService.CompanyId));
      }*/

    /* public override Task<TeamResource> GetByReferenceIdAsync(string id)
     {
         return _loggedInUserService.IsParent
             ? base.GetByReferenceIdAsync(id)
             : Task.FromResult(FindByFilterAsync(teamResource =>
                 teamResource.ReferenceId.Equals(id) &&
                 teamResource.TeamMasterName.Equals(_loggedInUserService.CompanyId)).Result.SingleOrDefault());
     }*/

    /*public override IQueryable<TeamResource> GetPaginatedQuery()
    {
        if (_loggedInUserService.IsParent)
            return Entities.Where(x => x.IsActive)
                .AsNoTracking()
                .OrderByDescending(x => x.Id);

        return Entities.Where(x => x.IsActive && x.TeamMasterName.Equals(_loggedInUserService.CompanyId))
            .AsNoTracking()
            .OrderByDescending(x => x.Id);
    }*/

    public Task<List<TeamResource>> GetTeamResourceNames()
    {
        if (!_loggedInUserService.IsParent)
            return _dbContext.TeamResources.Active()
                .Where(x => x.TeamMasterName.Equals(_loggedInUserService.CompanyId))
                .Select(x => new TeamResource { ReferenceId = x.ReferenceId, TeamMasterName = x.TeamMasterName })
                .OrderBy(x => x.TeamMasterName)
                .ToListAsync();
        return _dbContext.TeamResources
            .Active()
            .Select(x => new TeamResource { ReferenceId = x.ReferenceId, TeamMasterName = x.TeamMasterName })
            .OrderBy(x => x.TeamMasterName)
            .ToListAsync();
    }

    public Task<bool> IsTeamResourceNameExist(string teamMasterName, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.TeamResources.Any(e => e.ResourceName.Equals(teamMasterName)))
            : Task.FromResult(_dbContext.TeamResources.Where(e => e.ResourceName.Equals(teamMasterName)).ToList()
                .Unique(id));
    }

    public Task<bool> IsTeamResourceNameUnique(string teamMasterName)
    {
        var matches = _dbContext.TeamResources.Any(e => e.TeamMasterName.Equals(teamMasterName));

        return Task.FromResult(matches);
    }

    public async Task<List<TeamResource>> GetTeamMasterIdByTeamResource(string teamMasterId)
    {
        return await _dbContext.TeamResources
            .Where(x => x.TeamMasterId.Equals(teamMasterId) && x.IsActive).ToListAsync();
    }
}