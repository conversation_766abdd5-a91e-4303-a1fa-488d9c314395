﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IDatabaseRepository : IRepository<Database>
{
    Task<List<Database>> GetDatabaseNames();
    Task<bool> IsDatabaseNameUnique(string name);
    Task<bool> IsDatabaseNameExist(string databaseName, string databaseId);
    Task<List<Database>> GetDatabaseByServerId(string serverId);

    Task<List<Database>> GetDatabaseByDatabaseTypeId(string databaseTypeId);

    // Task<bool> IsDatabaseLicenseCountExitMaxLimit(string licenseId, string serverId, string siteType);
    Task<List<Database>> GetDatabaseListByLicenseKey(string licenseId);
    IQueryable<Database> GetDatabaseByType(string dataBaseTypeId);
    Task<List<Database>> GetDatabaseType(string type);
    Task<List<Database>> GetDatabaseByBusinessServiceId(string businessServiceId);
    Task<List<Database>> GetDatabaseByNodeId(string nodeId);
    Task<List<Database>> GetByUserName(string userName);
    Task<List<Database>> GetByUserNameAndDatabaseType(string userName, string databaseTypeId);
    Task<List<Database>> GetByDatabaseTypeIdAndFormVersion(string databaseTypeId, string formVersion);
    Task<int> GetDatabaseCountByLicenseKey(string licenseId, List<string> siteIds);
    Task<List<Database>> GetByDatabaseIdsAsync(List<string> ids);
    Task<Database> GetByDatabaseName(string name);
    Task<Dictionary<string, int>> GetDatabaseCountByLicenseIds(List<string> licenseId, List<string> siteIds);
    Task<int> GetCountByTypeAndLicenseKey(string licenseId, string type, List<string> siteIds);
    Task<int> GetCountByTypeIdsAndLicenseId(string licenseId, List<string> typeIds, List<string> siteIds);
}