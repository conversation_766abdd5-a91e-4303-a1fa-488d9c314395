﻿using ContinuityPatrol.Application.Features.DriftJob.Events.UpdateState;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Application.Features.DriftJob.Commands.UpdateState;

public class UpdateDriftJobStateCommandHandler:IRequestHandler<UpdateDriftJobStateCommand, BaseResponse>
{
    private readonly IDriftJobRepository _driftJobRepository;
    private readonly IPublisher _publisher;
    public UpdateDriftJobStateCommandHandler(IDriftJobRepository driftJobRepository, IPublisher publisher)
    {
        _driftJobRepository = driftJobRepository;
        _publisher = publisher;
    }

    public async Task<BaseResponse> Handle(UpdateDriftJobStateCommand request, CancellationToken cancellationToken)
    {
        if (request.UpdateDriftJobState.Count == 0)
            throw new InvalidDataException("Update drift Job State List Can't be empty.");

        var driftJobIds=request.UpdateDriftJobState.Select(x=>x.Id).ToList();

        var driftJobs = await _driftJobRepository.GetDriftJobByIdsAsync(driftJobIds);

        if (driftJobs.Count != 0)
        {
            driftJobs.ForEach(x=>x.State = request.State);

            await _driftJobRepository.UpdateRangeAsync(driftJobs);
        }

        foreach (var driftJob in driftJobs)
        {
            await _publisher.Publish(
         new DriftJobStateUpdatedEvent { DriftJobName = driftJob.Name, State = driftJob.State },
         cancellationToken);
        }

       
        var response = new BaseResponse
        {
            Message = request.UpdateDriftJobState.Count == 1
                ? $"Monitoring Job state has been updated to '{request.State}' state successfully"
                : $"Monitoring Jobs state has been updated to '{request.State}' state successfully"
        };
        return response;
       
    }
}
