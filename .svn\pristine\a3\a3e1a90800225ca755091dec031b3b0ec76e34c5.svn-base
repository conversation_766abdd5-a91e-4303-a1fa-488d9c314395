using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class AlertInformationRepositoryTests : IClassFixture<AlertInformationFixture>
{
    private readonly AlertInformationFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly AlertInformationRepository _repository;

    public AlertInformationRepositoryTests(AlertInformationFixture fixture)
    {
        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new AlertInformationRepository(_dbContext);
    }

    [Fact]
    public async Task GetAlertInformationByCode_ReturnsList_WhenCodeExists()
    {
        var alert = _fixture.AlertInformationDto;
        alert.Code = "CODE123";
        alert.IsActive = true;
        _dbContext.AlertInformations.Add(alert);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetAlertInformationByCode("CODE123");

        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("CODE123", result[0].Code);
    }

    [Fact]
    public async Task GetAlertInformationByCode_ReturnsEmpty_WhenCodeDoesNotExist()
    {
        var result = await _repository.GetAlertInformationByCode("NON_EXISTENT_CODE");
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetAlertInformationByCode_ReturnsEmpty_WhenInactive()
    {
        var alert = _fixture.AlertInformationDto;
        alert.Code = "CODE_INACTIVE";
        alert.IsActive = false;
        _dbContext.AlertInformations.Add(alert);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetAlertInformationByCode("CODE_INACTIVE");

        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetAlertInformationByCode_Throws_WhenCodeIsNull()
    {
        var result = await _repository.GetAlertInformationByCode(null);
        Assert.Equal(0, result?.Count);
    }

    [Fact]
    public async Task GetByReferenceId_ReturnsEntity_WhenReferenceIdExistsAndActive()
    {
        var alert = _fixture.AlertInformationDto;
        alert.ReferenceId = "e60f8037-0ba0-4d04-b7a6-8abfc07d49ac";
        alert.IsActive = true;
        _dbContext.AlertInformations.Add(alert);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync("e60f8037-0ba0-4d04-b7a6-8abfc07d49ac");

        Assert.NotNull(result);
        Assert.Equal("e60f8037-0ba0-4d04-b7a6-8abfc07d49ac", result.ReferenceId);
    }
    [Fact]
    public async Task GetByReferenceId_ThrowInvalidArgumentExceptionForInvalidReferenceId()
    {
        var alert = _fixture.AlertInformationDto;
        alert.ReferenceId = "083e0ff4-61b6-4cc5-b84b-4afa49f73d7a";
        alert.IsActive = true;
        _dbContext.AlertInformations.Add(alert);
        await _dbContext.SaveChangesAsync();

       await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.GetByReferenceIdAsync(null));
    }

    [Fact]
    public async Task GetByReferenceId_ReturnsNull_WhenReferenceIdDoesNotExist()
    {
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceId_ReturnsNull_WhenIsactive()
    {
        var alert = _fixture.AlertInformationDto;
        alert.ReferenceId = "083e0ff4-61b6-4cc5-b84b-4afa49f73d7a";
        alert.IsActive = false;
        _dbContext.AlertInformations.Add(alert);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d6a");
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceId_Throws_WhenReferenceIdIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.GetByReferenceIdAsync(null));
    }

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        var alert = _fixture.AlertInformationDto;
        alert.Code = "ADD_CODE";
        alert.IsActive = true;

        var result = await _repository.AddAsync(alert);

        Assert.NotNull(result);
        Assert.Equal("ADD_CODE", result.Code);
        Assert.Single(_dbContext.AlertInformations);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        var alert = _fixture.AlertInformationDto;
        alert.Code = "UPDATE_CODE";
        alert.IsActive = true;
        _dbContext.AlertInformations.Add(alert);
        await _dbContext.SaveChangesAsync();

        alert.AlertFrequency = 8;
        var result = await _repository.UpdateAsync(alert);

        Assert.Equal(8, result.AlertFrequency);
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        var alert = _fixture.AlertInformationDto;
        alert.Code = "DELETE_CODE";
        alert.IsActive = true;
        _dbContext.AlertInformations.Add(alert);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.DeleteAsync(alert);

        Assert.Equal("DELETE_CODE", result.Code);
        Assert.Empty(_dbContext.AlertInformations);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsAllEntities_WhenDataExists()
    {
        var alerts = _fixture.AlertInformationList;
        _dbContext.AlertInformations.AddRange(alerts);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        Assert.NotNull(result);
        Assert.Equal(alerts.Count, result.Count);
        foreach (var alert in alerts)
        {
            Assert.Contains(result, x => x.Id == alert.Id);
        }
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoDataExists()
    {
        var result = await _repository.ListAllAsync();

        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmptyList_WhenAllInactive()
    {
       
        var alerts = _fixture.AlertInformationList;
        foreach (var alert in alerts)
            alert.IsActive = false;

        _dbContext.AlertInformations.AddRange(alerts);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        Assert.NotNull(result);
        Assert.Empty(result);
    }


    [Fact]
    public async Task PaginatedListAllAsync_ReturnsCorrectPage_WhenDataExists()
    {
     
        var alerts = _fixture.AlertInformationPaginationList;
        foreach (var alert in alerts)
            alert.IsActive = true;
        _dbContext.AlertInformations.AddRange(alerts);
        await _dbContext.SaveChangesAsync();

        int pageNumber = 2;
        int pageSize = 5;

        Specification<AlertInformation>? spec = null;


        var result = await _repository.PaginatedListAllAsync(
            pageNumber,
            pageSize,
            spec,
            sortColumn: "Id",
            sortOrder: "asc"
        );

      
        Assert.NotNull(result);
        Assert.Equal(alerts.Count, result.TotalCount);
        Assert.Equal(pageSize, result.Data.Count);
        Assert.Equal(alerts.OrderBy(x => x.Id).Skip(pageSize).First().Id, result.Data.First().Id);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsEmpty_WhenNoData()
    {
 
        var result = await _repository.PaginatedListAllAsync(
            pageNumber: 1,
            pageSize: 10,
            productFilterSpec: null,
            sortColumn: "Id",
            sortOrder: "asc"
        );
        Assert.NotNull(result);
        Assert.Equal(0, result.TotalCount);
        Assert.Empty(result.Data);
    }

    [Fact]
    public async Task PaginatedListAllAsync_AppliesFilter()
    {
        // Arrange
        var alerts = _fixture.AlertInformationPaginationList;
        foreach (var alert in alerts)
            alert.IsActive = true;
        alerts[0].Code = "FILTER_ME";
        _dbContext.AlertInformations.AddRange(alerts);
        await _dbContext.SaveChangesAsync();

        var spec = new AlertInformationFilterSpecification("FILTER_ME");

        var result = await _repository.PaginatedListAllAsync(
            pageNumber: 1,
            pageSize: 10,
            productFilterSpec: spec,
            sortColumn: "Id",
            sortOrder: "asc"
        );

        Assert.NotNull(result);
        Assert.Equal(1, result.TotalCount);
        Assert.Single(result.Data);
        Assert.Equal("FILTER_ME", result.Data.First().Code);
    }
}