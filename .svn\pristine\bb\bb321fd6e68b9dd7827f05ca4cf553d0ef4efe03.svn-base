﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Repositories;

public class ServerRepository : BaseRepository<Server>, IServerRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IInfraObjectRepository _infraObjectRepository;

    public ServerRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService,
        IInfraObjectRepository infraObjectRepository) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
        _infraObjectRepository = infraObjectRepository;
    }

    public override async Task<IReadOnlyList<Server>> ListAllAsync()
    {
        var servers = base.QueryAll(server =>
            server.CompanyId.Equals(_loggedInUserService.CompanyId) && server.IsActive);

        var serverDto = MapServer(servers);

        return _loggedInUserService.IsAllInfra
            ? await serverDto.ToListAsync()
            : GetAssignedBusinessServicesByServers(serverDto);
    }

    public async Task<List<Server>> GetType(string serverTypeId)
    {
        var servers = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.ServerTypeId.Equals(serverTypeId))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.ServerTypeId.Equals(serverTypeId));

        var serverDto = MapServer(servers);

        return _loggedInUserService.IsAllInfra
            ? await serverDto.ToListAsync()
            : GetAssignedBusinessServicesByServers(serverDto).ToList();
    }

    public async Task<List<Server>> GetTypeName(string serverType)
    {
        var servers = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.ServerType.Trim().ToLower().Equals(serverType.Trim().ToLower()))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.ServerType.Trim().ToLower().Equals(serverType.Trim().ToLower()));

        return await servers.ToListAsync();

    }

    public async Task<List<Server>> GetRoleType(string roleTypeId)
    {
        var servers = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.RoleTypeId.Equals(roleTypeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.RoleTypeId.Equals(roleTypeId));

        var serverDto = MapServer(servers);

        return _loggedInUserService.IsAllInfra
            ? await serverDto.ToListAsync()
            : GetAssignedBusinessServicesByServers(serverDto).ToList();
    }

    public async Task<List<Server>> GetServerBySiteId(string siteId)
    {
        var servers = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.SiteId.Equals(siteId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.SiteId.Equals(siteId));

        var serverDto = MapServer(servers);

        return _loggedInUserService.IsAllInfra
            ? await serverDto.ToListAsync()
            : GetAssignedBusinessServicesByServers(serverDto).ToList();
    }
    
    public async Task<List<Server>> GetServerBySiteIds(List<string> siteIds)
    {
        var servers = _loggedInUserService.IsParent
            ? base.FilterBy(x => siteIds.Contains(x.SiteId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) &&siteIds.Contains(x.SiteId));

        var serverDto = MapServer(servers);

        return _loggedInUserService.IsAllInfra
            ? await serverDto.ToListAsync()
            : GetAssignedBusinessServicesByServers(serverDto).ToList();
    }

    public override Task<Server> GetByReferenceIdAsync(string id)
    {
        var servers = base.GetByReferenceId(id,
            server => server.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                      server.ReferenceId.Equals(id));

        var serverDto = MapServer(servers);

        return Task.FromResult(serverDto.SingleOrDefault());
    }

    public async Task<List<Server>> GetServerNames()
    {
        return _loggedInUserService.IsAllInfra
            ? await base.QueryAll(server =>
                server.CompanyId.Equals(_loggedInUserService.CompanyId) && server.IsActive)
                .Select(x => new Server
                {
                    ReferenceId = x.ReferenceId, Name = x.Name, ServerTypeId = x.ServerTypeId, ServerType = x.ServerType, RoleType = x.RoleType
                }).ToListAsync()
            : GetAssignedBusinessServicesByServers(base.QueryAll(server =>
                server.CompanyId.Equals(_loggedInUserService.CompanyId) && server.IsActive)).Select(x => new Server
            {
                ReferenceId = x.ReferenceId,
                Name = x.Name,
                ServerTypeId = x.ServerTypeId,
                ServerType = x.ServerType,
                RoleType = x.RoleType
            }).ToList();
    }

    public async Task<List<Server>> GetByServerIdsAsync(List<string> ids)
    {
        var servers = _loggedInUserService.IsParent
            ? base.FilterBy(x => ids.Contains(x.ReferenceId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && ids.Contains(x.ReferenceId));

        var serverDto = MapServer(servers);

        return await serverDto.ToListAsync();
    }

    public override IQueryable<Server> GetPaginatedQuery()
    {
        var servers = base.QueryAll(server =>
            server.CompanyId.Equals(_loggedInUserService.CompanyId) && server.IsActive);

        var serverDto = MapServer(servers);

        return _loggedInUserService.IsAllInfra
            ? serverDto.AsNoTracking().OrderByDescending(x => x.Id)
            : GetPaginatedAssignedBusinessServicesByServers(serverDto).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public async Task<bool> IsServerNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? await _dbContext.Servers.AnyAsync(e => e.Name.Equals(name))
            : (await _dbContext.Servers.Where(e => e.Name.Equals(name)).ToListAsync()).Unique(id);
    }

    public async Task<bool> IsServerNameUnique(string name)
    {
        return await _dbContext.Servers.AnyAsync(e => e.Name.Equals(name));
    }

    public async Task<Dictionary<string,int>> GetServerCountByLicenseIds(List<string> licenseId, string roleType, string siteTypeId)
    {
        var site = await _dbContext.Sites
           .Active()
           .AsNoTracking()
           .Where(x => x.TypeId.Equals(siteTypeId))
           .Select(x => x.ReferenceId)
           .ToListAsync();

        var serverCounts = await _dbContext.Servers
        .Active()
        .AsNoTracking()
        .Where(x => licenseId.Contains(x.LicenseId) && x.RoleType.Equals(roleType) && site.Contains(x.SiteId))
        .GroupBy(x => x.LicenseId)
        .Select(group => new
        {
            LicenseId = group.Key,
            Count = group.Count()
        })
        .ToDictionaryAsync(x => x.LicenseId, x => x.Count);


        return serverCounts;

        //var count = await _dbContext.Servers.Active()
        //    .CountAsync(x => licenseId.Contains(x.LicenseId) && x.RoleType.Equals(roleType) && site.Contains(x.SiteId));

    }

    public async Task<int> GetServerCountByLicenseKey(string licenseId, string roleType, string siteTypeId)
    {
        var site = await _dbContext.Sites
            .Active()
            .AsNoTracking()
            .Where(x => x.TypeId.Equals(siteTypeId))
            .Select(x => x.ReferenceId)
            .ToListAsync();

        return await _dbContext.Servers.Active()
            .CountAsync(x => x.LicenseId.Equals(licenseId) && x.RoleType.Equals(roleType) && site.Contains(x.SiteId));
    }

    public async Task<List<Server>> GetServerByLicenseKey(string licenseId)
    {
        var servers = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.LicenseId.Equals(licenseId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.LicenseId.Equals(licenseId));

        var serverDto = MapServer(servers);

        return _loggedInUserService.IsAllInfra
            ? await serverDto.ToListAsync()
            : GetAssignedBusinessServicesByServers(serverDto).ToList();
    }

    public async Task<Server> GetServerByServerName(string serverName)
    {
        return await _dbContext.Servers
            .AsNoTracking()
            .Active()
            .FirstOrDefaultAsync(x => x.Name.ToLower() == serverName.ToLower());
    }

    public async Task<List<Server>> GetServerType(string serverTypeId)
    {
        var servers = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.ServerTypeId.Equals(serverTypeId))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.ServerTypeId.Equals(serverTypeId));

        var serverDto = MapServer(servers);

        return _loggedInUserService.IsAllInfra
            ? await serverDto.ToListAsync()
            : GetAssignedBusinessServicesByServers(serverDto).ToList();
    }


    public IQueryable<Server> GetServerByOsType(string osTypeId)
    {
        var servers = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.OSTypeId.Equals(osTypeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.OSTypeId.Equals(osTypeId));

        var serverDto = MapServer(servers);

        return _loggedInUserService.IsAllInfra
            ? serverDto
            : GetPaginatedAssignedBusinessServicesByServers(serverDto);
    }

    public async Task<int> GetServerStatusCount(string status)
    {
        if (!_loggedInUserService.IsParent)
            return await _dbContext.Servers.Active()
                .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                            x.Status.ToLower().Equals(status.ToLower())).CountAsync();
        return await _dbContext.Servers.Active()
            .Where(x => x.Status.ToLower().Equals(status.ToLower())).CountAsync();
    }

    public override Task<Server> GetByIdAsync(int id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByIdAsync(id)
            : Task.FromResult(FindByFilterAsync(server =>
                    server.Id.Equals(id) && server.CompanyId.Equals(_loggedInUserService.CompanyId)).Result
                .SingleOrDefault());
    }

    public async Task<List<Server>> GetByUserName(string userName)
    {
        return await Task.Run(() =>
        {
            var uerNameList = FilterByJObjectKeyEqualValue(server => server.Properties, "user", userName).ToList();
            var servers = _loggedInUserService.IsParent
                ? uerNameList
                : uerNameList.Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).ToList();

            return _loggedInUserService.IsAllInfra
                ? servers
                : GetAssignedBusinessServicesByServers(servers.AsQueryable()).ToList();
        });
    }

    public async Task<List<Server>> GetServerByBusinessServiceId(string businessServiceId)
    {
        var servers = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.BusinessServiceId.Equals(businessServiceId));

        var serverDto = MapServer(servers);

        return _loggedInUserService.IsAllInfra
            ? await serverDto.ToListAsync()
            : GetAssignedBusinessServicesByServers(serverDto).ToList();
    }

    public async Task<List<Server>> GetByIpAddress(string ipAddress)
    {
        var serverVm = new List<Server>();

        var servers = base.QueryAll(x => x.Properties.Contains(ipAddress));

        var serverDto = MapServer(servers);

        var serverList = _loggedInUserService.IsAllInfra
            ? await serverDto.ToListAsync()
            : GetAssignedBusinessServicesByServers(serverDto).ToList();

        foreach (var server in serverList)
        {
            var propertyIpAddress = GetJsonProperties.GetIpAddressFromProperties(server.Properties);

            if (propertyIpAddress.Equals(ipAddress))
            {
                serverVm.Add(server);
            }
        }

        return serverVm;
    }

    public async Task<List<Server>> GetByUserNameAndOsType(string userName, string osTypeId)
    {
        var splitOsTypeId = osTypeId.Split(',');

        var servers = base.QueryAll(server =>
            server.CompanyId.Equals(_loggedInUserService.CompanyId));

        return _loggedInUserService.IsAllInfra
            ? await servers.Where(x => x.Properties.Contains(userName))
                .Where(x => splitOsTypeId.Contains(x.OSTypeId)).ToListAsync()
            : await servers.Where(x => x.Properties.Contains(userName))
                .Where(x => splitOsTypeId.Contains(x.OSTypeId)).ToListAsync();
    }

    private IReadOnlyList<Server> GetAssignedBusinessServicesByServers(IQueryable<Server> businessServices)
    {
        var servers = new List<Server>();

        foreach (var businessService in businessServices)
            if (AssignedEntity.AssignedBusinessServices.Count > 0)
                servers.AddRange(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
                                  where businessService.BusinessServiceId == assignedBusinessService.Id
                                  select businessService);

        var infraObjects = _infraObjectRepository.GetPaginatedQuery();

        return infraObjects.Any() ? servers.Where(server => infraObjects.Any(x => x.ServerProperties.Contains(server.ReferenceId))).ToList()
            : servers;
        //services = services.Where(server => infraObjects.Any(x =>
        //    server.ReferenceId.Equals(x.PRServerId) ||
        //    server.ReferenceId.Equals(x.DRServerId) ||
        //    server.ReferenceId.Equals(x.NearDRServerId))).ToList();

    }

    public async Task<List<Server>> GetServerByOsTypeIdAndFormVersion(string osTypeId, string formVersion)
    {
        var servers =
            base.FilterBy(x => x.OSTypeId.Equals(osTypeId) && x.FormVersion.Equals(formVersion));

        var serverDto = MapServer(servers);

        return _loggedInUserService.IsAllInfra
            ? await serverDto.ToListAsync()
            : GetAssignedBusinessServicesByServers(serverDto).ToList();
    }

    private IQueryable<Server> GetPaginatedAssignedBusinessServicesByServers(IQueryable<Server> businessServices)
    {
        var assignedServiceIds = AssignedEntity.AssignedBusinessServices.Select(s => s.Id);

        businessServices = businessServices.Where(s => assignedServiceIds.Contains(s.BusinessServiceId));

        var infraObjects = _infraObjectRepository.GetPaginatedQuery();

        //businessServices = businessServices.Where(server => infraObjects.Any(x =>
        //    server.ReferenceId.Equals(x.PRServerId) ||
        //    server.ReferenceId.Equals(x.DRServerId) ||
        //    server.ReferenceId.Equals(x.NearDRServerId)));
        businessServices = businessServices.Where(server => infraObjects.Any(x => x.ServerProperties.Contains(server.ReferenceId)));

        return businessServices;
    }

    public virtual IQueryable<Server> FilterByJObjectKeyEqualValue(Expression<Func<Server, string>> jsonSelector, string key, string searchInput)
    {
        var compiledSelector = jsonSelector.Compile();

        bool Filter(Server entity)
        {
            var json = compiledSelector(entity);
            if (string.IsNullOrEmpty(json))
                return false;
            var jObject = JObject.Parse(json);

            return jObject.Properties()
                          .Any(jp => jp.Name.Contains(key, StringComparison.OrdinalIgnoreCase)
                                     && jp.Value.ToString().Equals(searchInput, StringComparison.OrdinalIgnoreCase));
        }
        return Entities.AsEnumerable().Where(entity => Filter(entity)).AsQueryable().DescOrderById();

    }

    public async Task<List<Server>> GetByUserNameAndOsTypeForBulkCredential(string userName, string osTypeId)
    {
        var splitOsTypeId = osTypeId.Split(',');

        // Run the filtering logic on a background thread to avoid blocking
        return await Task.Run(() =>
        {
            var uerNameList = FilterByJObjectKeyEqualValue(server => server.Properties, "user", userName).ToList();
            var servers = _loggedInUserService.IsParent
                ? uerNameList.Where(x => splitOsTypeId.Contains(x.OSTypeId))
                : uerNameList.Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && splitOsTypeId.Contains(x.OSTypeId)).ToList();

            return _loggedInUserService.IsAllInfra
                ? servers.ToList()
                : GetAssignedBusinessServicesByServers(servers.AsQueryable()).ToList();
        });
    }



    private IQueryable<Server> MapServer(IQueryable<Server> server)
    {
        var mappedServers = server.Select(data => new
        {
            Server = data,

            Company = _dbContext.Companies.Active().AsNoTracking()
                .FirstOrDefault(c => c.ReferenceId.Equals(data.CompanyId)),

            BusinessService = _dbContext.BusinessServices.Active().AsNoTracking()
                .FirstOrDefault(bs => bs.ReferenceId.Equals(data.BusinessServiceId)),

            Site = _dbContext.Sites.Active().AsNoTracking()
                .FirstOrDefault(site => site.ReferenceId.Equals(data.SiteId)),

            RoleType = _dbContext.ServerTypes.Active().AsNoTracking()
                .FirstOrDefault(role => role.ReferenceId.Equals(data.RoleTypeId)),

            ServerType = _dbContext.ServerSubTypes.Active().AsNoTracking()
                .FirstOrDefault(type => type.ReferenceId.Equals(data.ServerTypeId)),

            ComponentType = _dbContext.ComponentTypes.Active().AsNoTracking()
                .FirstOrDefault(osTy => osTy.ReferenceId.Equals(data.OSTypeId)),

            License = _dbContext.LicenseManagers.Active().AsNoTracking()
                .FirstOrDefault(y => y.ReferenceId.Equals(data.LicenseId))
        });

        var mappedServerQuery = mappedServers.Select(res => new Server
        {
            Id = res.Server.Id,
            ReferenceId = res.Server.ReferenceId,
            CompanyId = res.Company.ReferenceId,
            Name = res.Server.Name,
            BusinessServiceId = res.BusinessService.ReferenceId,
            BusinessServiceName = res.BusinessService.Name,
            SiteId = res.Site.ReferenceId,
            SiteName = res.Site.Name,
            RoleTypeId = res.RoleType.ReferenceId,
            RoleType = res.RoleType.Name,
            ServerTypeId = res.ServerType.ReferenceId,
            ServerType = res.ServerType.Name,
            OSType = res.ComponentType.ComponentName ?? res.Server.OSType,
            OSTypeId = res.ComponentType.ReferenceId,
            Status = res.Server.Status,
            Properties = res.Server.Properties,
            LicenseId = res.License.ReferenceId,
            LicenseKey = SecurityHelper.Decrypt(res.License.PoNumber),
            FormVersion = res.Server.FormVersion,
            Version = res.Server.Version,
            ExceptionMessage = res.Server.ExceptionMessage,
            IsAttached = res.Server.IsAttached,
            IsActive = res.Server.IsActive,
            CreatedBy = res.Server.CreatedBy,
            CreatedDate = res.Server.CreatedDate,
            LastModifiedBy = res.Server.LastModifiedBy,
            LastModifiedDate = res.Server.LastModifiedDate,
            IsConnection = res.Server.IsConnection
            //IsAttached=res.Server.IsAttached

        });
        return mappedServerQuery;
    }


}