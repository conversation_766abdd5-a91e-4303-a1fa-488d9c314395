﻿using ContinuityPatrol.Application.Features.WorkflowExecutionEventLog.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowExecutionEventLog.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class WorkflowExecutionEventLogFixture : IDisposable
{
    public IMapper Mapper { get; }
    public List<WorkflowExecutionEventLog> WorkflowExecutionEventLogs { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateWorkflowExecutionEventLogCommand CreateWorkflowExecutionEventLogCommand { get; set; }
    public UpdateWorkflowExecutionEventLogCommand UpdateWorkflowExecutionEventLogCommand { get; set; }
    //public WorkflowExecutionEventLogCreatedEvent WorkflowExecutionEventLogCreatedEvent { get; set; }
    //public WorkflowExecutionEventLogDeletedEvent WorkflowExecutionEventLogDeletedEvent { get; set; }
    //public WorkflowExecutionEventLogUpdatedEvent WorkflowExecutionEventLogUpdatedEvent { get; set; }


    public WorkflowExecutionEventLogFixture()
    {
        WorkflowExecutionEventLogs = AutoWorkflowExecutionEventLogFixture.Create<List<WorkflowExecutionEventLog>>();
        UserActivities = AutoWorkflowExecutionEventLogFixture.Create<List<UserActivity>>();
        CreateWorkflowExecutionEventLogCommand = AutoWorkflowExecutionEventLogFixture.Create<CreateWorkflowExecutionEventLogCommand>();
        UpdateWorkflowExecutionEventLogCommand = AutoWorkflowExecutionEventLogFixture.Create<UpdateWorkflowExecutionEventLogCommand>();
        //WorkflowExecutionEventLogCreatedEvent = AutoWorkflowExecutionEventLogFixture.Create<WorkflowExecutionEventLogCreatedEvent>();
        //WorkflowExecutionEventLogDeletedEvent = AutoWorkflowExecutionEventLogFixture.Create<WorkflowExecutionEventLogDeletedEvent>();
        //WorkflowExecutionEventLogUpdatedEvent = AutoWorkflowExecutionEventLogFixture.Create<WorkflowExecutionEventLogUpdatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<WorkflowExecutionEventLogProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoWorkflowExecutionEventLogFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateWorkflowExecutionEventLogCommand>(p => p.LoginName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateWorkflowExecutionEventLogCommand>(p => p.Id, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserActivity>(p => p.LoginName, 10));
            fixture.Customize<UpdateWorkflowExecutionEventLogCommand>(c => c.With(b => b.Id, 0.ToString()));
            fixture.Customize<WorkflowExecutionEventLog>(c => c.With(b => b.IsActive, true));

            //fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<WorkflowExecutionEventLogCreatedEvent>(p => p.TemplateName, 10));

            //fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<WorkflowExecutionEventLogDeletedEvent>(p => p.TemplateName, 10));

            //fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<WorkflowExecutionEventLogUpdatedEvent>(p => p.TemplateName, 10));

            return fixture;
        }
    }
    public void Dispose()
    {

    }
}
