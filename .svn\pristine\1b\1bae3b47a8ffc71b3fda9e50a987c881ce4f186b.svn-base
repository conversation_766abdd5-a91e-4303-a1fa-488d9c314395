﻿using ContinuityPatrol.Application.Features.SiteType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SiteTypeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.SiteType.Queries
{
    public class GetSiteTypePaginatedListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISiteTypeRepository> _mockSiteTypeRepository;
        private readonly GetSiteTypePaginatedListQueryHandler _handler;

        public GetSiteTypePaginatedListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSiteTypeRepository = new Mock<ISiteTypeRepository>();

            _handler = new GetSiteTypePaginatedListQueryHandler(
                _mockMapper.Object,
                _mockSiteTypeRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnPaginatedResult_WhenValidRequest()
        {
            var query = new GetSiteTypePaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 10,
                SearchString = "Test"
            };

            var mockSiteTypes = new List<Domain.Entities.SiteType>
            {
                new Domain.Entities.SiteType { Id = 1, Category = "Site1" },
                new Domain.Entities.SiteType { Id = 2, Type = "Site2" }
            };

            var mappedResult = new List<SiteTypeListVm>
            {
                new SiteTypeListVm { Id = Guid.NewGuid().ToString(), Category = "Site1" },
                new SiteTypeListVm { Id = Guid.NewGuid().ToString(), Type = "Site2" }
            };

            var paginatedResult = new PaginatedResult<Domain.Entities.SiteType>
            {
                Data = mockSiteTypes,
                TotalCount = mockSiteTypes.Count,
                TotalPages = 1,
                PageSize = 10
            };

            _mockSiteTypeRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(mockSiteTypes.AsQueryable());

            _mockMapper
                .Setup(m => m.Map<SiteTypeListVm>(It.IsAny<Domain.Entities.SiteType>()))
                .Returns((Domain.Entities.SiteType source) => mappedResult.FirstOrDefault(vm => vm.Id == source.Id.ToString()));

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Data.Count);
            Assert.Equal("Site1", result.Data[0].Type);
            Assert.Equal("Site2", result.Data[1].Category);

            _mockSiteTypeRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(m => m.Map<SiteTypeListVm>(It.IsAny<Domain.Entities.SiteType>()), Times.Exactly(2));
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyPaginatedResult_WhenNoResults()
        {
            var query = new GetSiteTypePaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 10,
                SearchString = "NonExistent"
            };

            _mockSiteTypeRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(Enumerable.Empty<Domain.Entities.SiteType>().AsQueryable());

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);

            _mockSiteTypeRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }
    }
}
