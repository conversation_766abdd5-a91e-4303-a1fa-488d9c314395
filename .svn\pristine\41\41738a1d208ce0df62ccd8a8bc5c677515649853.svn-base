﻿using ContinuityPatrol.Application.Features.DataSet.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.DataSetModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.DataSet.Queries;

public class GetDataSetPaginatedListQueryHandlerTests : IClassFixture<DataSetFixture>
{
    private readonly GetDataSetPaginatedListQueryHandler _handler;

    private readonly Mock<IDataSetRepository> _mockDataSetRepository;

    public GetDataSetPaginatedListQueryHandlerTests(DataSetFixture dataSetFixture)
    {
        var dataSetNewFixture = dataSetFixture;

        dataSetFixture.DataSets[0].DataSetName = "Column";
        dataSetFixture.DataSets[0].Description = "Pending";
        dataSetFixture.DataSets[0].PrimaryTableName = "List_Site";
        dataSetFixture.DataSets[0].StoredProcedureName = "Configuration";
        dataSetFixture.DataSets[0].QueryType = "Search";
        dataSetFixture.DataSets[0].StoredQuery = "select CreatedBy,CreatedDate,Id,IsActive,LastModifiedBy from cp6_admin.form";

        dataSetFixture.DataSets[1].DataSetName = "Data_Query";
        dataSetFixture.DataSets[1].Description = "Updated";
        dataSetFixture.DataSets[1].PrimaryTableName = "Test_Purpose";
        dataSetFixture.DataSets[1].StoredProcedureName = "DataBase";
        dataSetFixture.DataSets[1].QueryType = "Paginated";
        dataSetFixture.DataSets[1].StoredQuery = "select CreatedBy,CreatedDate,Id,IsActive,LastModifiedBy from cp6_admin.workflow";

        _mockDataSetRepository = DataSetRepositoryMocks.GetPaginatedDataSetRepository(dataSetNewFixture.DataSets);

        _handler = new GetDataSetPaginatedListQueryHandler(dataSetNewFixture.Mapper, _mockDataSetRepository.Object);

    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetDataSetPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Vas" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<DataSetListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedDataSets_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetDataSetPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Column" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<DataSetListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<DataSetListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].DataSetName.ShouldBe("Column");

        result.Data[0].Description.ShouldBe("Pending");

        result.Data[0].PrimaryTableName.ShouldBe("List_Site");

        result.Data[0].StoredProcedureName.ShouldBe("Configuration");

        result.Data[0].QueryType.ShouldBe("Search");

        result.Data[0].StoredQuery.ShouldBe("select CreatedBy,CreatedDate,Id,IsActive,LastModifiedBy from cp6_admin.form");


    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetDataSetPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<DataSetListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_Reports_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetDataSetPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "datasetname=Data_Query;description=Updated;primarytablename=Test_Purpose;storedprocedurename=DataBase;querytype=paginated;storedquery=select CreatedBy,CreatedDate,Id,IsActive,LastModifiedBy from cp6_admin.workflow" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<DataSetListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].DataSetName.ShouldBe("Data_Query");

        result.Data[0].Description.ShouldBe("Updated");

        result.Data[0].PrimaryTableName.ShouldBe("Test_Purpose");

        result.Data[0].StoredProcedureName.ShouldBe("DataBase");

        result.Data[0].QueryType.ShouldBe("Paginated");

        result.Data[0].StoredQuery.ShouldBe("select CreatedBy,CreatedDate,Id,IsActive,LastModifiedBy from cp6_admin.workflow");

        result.Data[0].PrimaryTablePKColumn.ShouldBeGreaterThan(0.ToString());
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetDataSetPaginatedListQuery(), CancellationToken.None);

        _mockDataSetRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }

}

