﻿namespace ContinuityPatrol.Application.Features.Alert.Queries.GetAlertListByStartOfWeek;

public class
    GetAlertListByStartOfWeekQueryHandler : IRequestHandler<GetAlertListByStartOfWeekQuery,
        List<AlertListByStartOfWeekVm>>
{
    private readonly IAlertRepository _alertRepository;
    private readonly IMapper _mapper;

    public GetAlertListByStartOfWeekQueryHandler(IAlertRepository alertRepository, IMapper mapper)
    {
        _alertRepository = alertRepository;
        _mapper = mapper;
    }

    public async Task<List<AlertListByStartOfWeekVm>> Handle(GetAlertListByStartOfWeekQuery request,
        CancellationToken cancellationToken)
    {
        var alertList = request.StartDate != null && request.EndDate != null
            ? await _alertRepository.GetAlertListFilterByDate(request.StartDate,
                request.EndDate)
            : await _alertRepository.ListAllAsync();
        var alertGroup = alertList.GroupBy(x => x.LastModifiedDate.Date)
            .Select(group => new AlertListByStartOfWeekVm
            {
                StartOfWeeks = group.Key,
                AlertListCount = _mapper.Map<List<AlertListDateVm>>(group.ToList()).Count.ToString()
            })
            .ToList();
        return alertGroup;
    }
}