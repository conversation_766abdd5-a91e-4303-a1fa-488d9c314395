﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class DrCalenderRepository : BaseRepository<DrCalenderActivity>, IDrCalenderRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public DrCalenderRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<DrCalenderActivity>> ListAllAsync()
    {
        if (_loggedInUserService.IsParent)
            return await base.ListAllAsync().ConfigureAwait(false);
        
        return await FindByFilterAsync(drCalender => drCalender.ReferenceId == _loggedInUserService.CompanyId).ConfigureAwait(false);
    }

    public async Task<bool> IsDrCalenderNameUnique(string name)
    {
        return await _dbContext.DrCalenderActivity.AnyAsync(e => e.ActivityName.Equals(name));
    }

    public async Task<IReadOnlyList<DrCalenderActivity>> DrCalendarDrillEventList()
    {
        if (_loggedInUserService.IsParent)
            return await FindByFilterAsync(dr => dr.ScheduledStartDate >= DateTime.Today).ConfigureAwait(false);
        return await FindByFilterAsync(dr => dr.ScheduledStartDate >= DateTime.Today && dr.CompanyId == _loggedInUserService.CompanyId).ConfigureAwait(false);
    }
    public async Task<bool> IsActivityNameExist(string name, string id, DateTime scheduleStartDate)
    {
        if (!id.IsValidGuid())
        {
            return await _dbContext.DrCalenderActivity
                .AnyAsync(x => x.ActivityName == name && x.ScheduledStartDate == scheduleStartDate);
        }

        var list = await _dbContext.DrCalenderActivity
            .Where(x => x.ActivityName == name && x.ScheduledStartDate == scheduleStartDate)
            .ToListAsync();

        return list.Unique(id); 
    }

    public async Task<IReadOnlyList<DrCalenderActivity>> UpComingDrillList()
    {
        var tenDaysAfter = DateTime.Now.Date.AddDays(10);

        if (_loggedInUserService.IsParent)
            return await FindByFilterAsync(dr =>
                dr.ScheduledStartDate >= DateTime.Now &&
                dr.ScheduledStartDate.Date <= tenDaysAfter
            ).ConfigureAwait(false);
        return await FindByFilterAsync(dr =>
            dr.ScheduledStartDate >= DateTime.Now &&
            dr.ScheduledStartDate.Date <= tenDaysAfter &&
            dr.CompanyId == _loggedInUserService.CompanyId
        ).ConfigureAwait(false);
    }

    public async  Task<List<DrCalenderActivity>> GetByWorkflowProfileId(string workflowProfileId)
    {
        var result =await (_loggedInUserService.IsParent
            ?Entities.DescOrderById().Where(dr => dr.WorkflowProfiles.Contains(workflowProfileId)&& dr.ScheduledStartDate<DateTime.Now)
            : Entities.DescOrderById().Where(dr=>dr.WorkflowProfiles.Contains(workflowProfileId) && dr.CompanyId.Equals(_loggedInUserService.CompanyId) && dr.ScheduledStartDate < DateTime.Now))
            .Select( x=>new DrCalenderActivity
            {
                Id = x.Id,
                ReferenceId=x.ReferenceId,
                ActivityName = x.ActivityName,
                ActivityType = x.ActivityType,
                ScheduledStartDate = x.ScheduledStartDate,
                ScheduledEndDate = x.ScheduledEndDate,
                WorkflowProfiles = x.WorkflowProfiles,
                CompanyId = x.CompanyId,
                Responsibility=x.Responsibility

            }).ToListAsync();

        return result.ToList();
    }
}