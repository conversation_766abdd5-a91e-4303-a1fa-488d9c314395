﻿using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetVerifyWorkflowDetail;
using ContinuityPatrol.Application.Features.Workflow.Commands.Create;
using ContinuityPatrol.Application.Features.Workflow.Commands.Lock;
using ContinuityPatrol.Application.Features.Workflow.Commands.Publish;
using ContinuityPatrol.Application.Features.Workflow.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Workflow.Commands.Update;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.UpdateWorkflowProfilePassword;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;

namespace ContinuityPatrol.Application.Mappings;

public class WorkflowProfile : Profile
{
    public WorkflowProfile()
    {

        CreateMap<List<Workflow>, VerifyWorkflowDetailVm>()
            .ForMember(dest => dest.TotalWorkflowCount, opt => opt.MapFrom(src => src.Count))
            .ForMember(dest => dest.WorkflowVerifyCount, opt => opt.MapFrom(src => src.Count(x=>x.IsVerify)))
            .ForMember(dest => dest.WorkflowNotVerifyCount, opt => opt.MapFrom(src => src.Count(x=>!x.IsVerify)));

        CreateMap<Workflow, CreateWorkflowCommand>().ReverseMap();
        CreateMap<Workflow, SaveAsWorkflowCommand>().ReverseMap();
        CreateMap<WorkflowDetailVm, Workflow>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<UpdateWorkflowCommand, Workflow>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<UpdateWorkflowCommand, WorkflowDetailVm>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<UpdateWorkflowCommand, WorkflowViewModel>().ForMember(x => x.Id, y => y.Ignore());

        
        CreateMap<CreateWorkflowCommand, WorkflowViewModel>().ReverseMap();
        CreateMap<UpdateWorkflowCommand, WorkflowViewModel>().ReverseMap();

        CreateMap<Workflow, WorkflowListVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<Workflow, WorkflowDetailVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<Workflow, WorkflowNameVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowActionResult, GetWorkflowActionByIdVm>()
            .ForMember(dest => dest.ActionId, opt => opt.MapFrom(src => src.ReferenceId));


        CreateMap<WorkflowRunningAction, GetWorkflowActionByIdVm>().ReverseMap();
        CreateMap<GetWorkflowActionByIdVm, WorkflowRunningAction>().ReverseMap();

        #region Lock

        CreateMap<UpdateWorkflowLockCommand, Workflow>().ForMember(x => x.Id, y => y.Ignore());

        #endregion

        #region Publish

        CreateMap<UpdateWorkflowPublishCommand, Workflow>().ForMember(x => x.Id, y => y.Ignore());

        #endregion

        CreateMap<UpdateWorkflowProfilePasswordCommand, Workflow>().ForMember(x => x.Id, y => y.Ignore());
    }
}