using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Delete;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class ApprovalMatrixUsersFixture : IDisposable
{
    public List<ApprovalMatrixUsers> ApprovalMatrixUsers { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateApprovalMatrixUsersCommand CreateApprovalMatrixUsersCommand { get; set; }
    public UpdateApprovalMatrixUsersCommand UpdateApprovalMatrixUsersCommand { get; set; }
    public DeleteApprovalMatrixUsersCommand DeleteApprovalMatrixUsersCommand { get; set; }
    public IMapper Mapper { get; set; }

    public ApprovalMatrixUsersFixture()
    {
        ApprovalMatrixUsers = new List<ApprovalMatrixUsers>
        {
            new ApprovalMatrixUsers
            {
                ReferenceId = Guid.NewGuid().ToString(),
                UserId = Guid.NewGuid().ToString(),
                UserName = "TestUser1",
                Email = "<EMAIL>",
                MobileNumber = "+**********",
                BusinessServiceProperties = "{\"department\":\"IT\",\"role\":\"Manager\"}",
                UserType = "Manager",
                AcceptType = "Email",
                IsLink = true,
                IsActive = true
            }
        };

        ApprovalMatrixUsers = AutoApprovalMatrixUsersFixture.Create<List<ApprovalMatrixUsers>>();
        UserActivities = AutoApprovalMatrixUsersFixture.Create<List<UserActivity>>();
        CreateApprovalMatrixUsersCommand = AutoApprovalMatrixUsersFixture.Create<CreateApprovalMatrixUsersCommand>();
        UpdateApprovalMatrixUsersCommand = AutoApprovalMatrixUsersFixture.Create<UpdateApprovalMatrixUsersCommand>();
        DeleteApprovalMatrixUsersCommand = AutoApprovalMatrixUsersFixture.Create<DeleteApprovalMatrixUsersCommand>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ApprovalMatrixUsersProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoApprovalMatrixUsersFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateApprovalMatrixUsersCommandList>(p => p.UserName, 100));
            fixture.Customize<CreateApprovalMatrixUsersCommandList>(c => c.With(a => a.UserId, Guid.NewGuid().ToString()));
            fixture.Customize<CreateApprovalMatrixUsersCommandList>(c => c.With(a => a.UserName, "TestUser"));
            fixture.Customize<CreateApprovalMatrixUsersCommandList>(c => c.With(a => a.Email, "<EMAIL>"));
            fixture.Customize<CreateApprovalMatrixUsersCommandList>(c => c.With(a => a.MobileNumber, "+**********"));
            fixture.Customize<CreateApprovalMatrixUsersCommandList>(c => c.With(a => a.BusinessServiceProperties, "{\"department\":\"IT\",\"role\":\"Manager\"}"));
            fixture.Customize<CreateApprovalMatrixUsersCommandList>(c => c.With(a => a.UserType, "Manager"));
            fixture.Customize<CreateApprovalMatrixUsersCommandList>(c => c.With(a => a.AcceptType, "Email"));
            fixture.Customize<CreateApprovalMatrixUsersCommandList>(c => c.With(a => a.IsLink, true));

            fixture.Customize<CreateApprovalMatrixUsersCommand>(c => c.With(a => a.ApprovalMatrixUsers, 
                new List<CreateApprovalMatrixUsersCommandList>
                {
                    new CreateApprovalMatrixUsersCommandList
                    {
                        UserId = Guid.NewGuid().ToString(),
                        UserName = "TestUser1",
                        Email = "<EMAIL>",
                        MobileNumber = "+**********",
                        BusinessServiceProperties = "{\"department\":\"IT\",\"role\":\"Manager\"}",
                        UserType = "Manager",
                        AcceptType = "Email",
                        IsLink = true
                    },
                    new CreateApprovalMatrixUsersCommandList
                    {
                        UserId = Guid.NewGuid().ToString(),
                        UserName = "TestUser2",
                        Email = "<EMAIL>",
                        MobileNumber = "+1234567891",
                        BusinessServiceProperties = "{\"department\":\"HR\",\"role\":\"Supervisor\"}",
                        UserType = "Supervisor",
                        AcceptType = "SMS",
                        IsLink = false
                    }
                }));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateApprovalMatrixUsersCommand>(p => p.UserName, 100));
            fixture.Customize<UpdateApprovalMatrixUsersCommand>(c => c.With(a => a.Id, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateApprovalMatrixUsersCommand>(c => c.With(a => a.UserId, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateApprovalMatrixUsersCommand>(c => c.With(a => a.UserName, "UpdatedUser"));
            fixture.Customize<UpdateApprovalMatrixUsersCommand>(c => c.With(a => a.Email, "<EMAIL>"));
            fixture.Customize<UpdateApprovalMatrixUsersCommand>(c => c.With(a => a.MobileNumber, "+1234567892"));
            fixture.Customize<UpdateApprovalMatrixUsersCommand>(c => c.With(a => a.BusinessServiceProperties, "{\"department\":\"Finance\",\"role\":\"Director\"}"));
            fixture.Customize<UpdateApprovalMatrixUsersCommand>(c => c.With(a => a.UserType, "Director"));
            fixture.Customize<UpdateApprovalMatrixUsersCommand>(c => c.With(a => a.AcceptType, "Both"));
            fixture.Customize<UpdateApprovalMatrixUsersCommand>(c => c.With(a => a.IsLink, false));

            fixture.Customize<DeleteApprovalMatrixUsersCommand>(c => c.With(a => a.Id, Guid.NewGuid().ToString()));

            fixture.Customize<ApprovalMatrixUsers>(c => c.With(a => a.ReferenceId, Guid.NewGuid().ToString()));
            fixture.Customize<ApprovalMatrixUsers>(c => c.With(a => a.IsActive, true));
            fixture.Customize<ApprovalMatrixUsers>(c => c.With(a => a.UserId, Guid.NewGuid().ToString()));
            fixture.Customize<ApprovalMatrixUsers>(c => c.With(a => a.UserName, "TestUser"));
            fixture.Customize<ApprovalMatrixUsers>(c => c.With(a => a.Email, "<EMAIL>"));
            fixture.Customize<ApprovalMatrixUsers>(c => c.With(a => a.MobileNumber, "+**********"));
            fixture.Customize<ApprovalMatrixUsers>(c => c.With(a => a.BusinessServiceProperties, "{\"department\":\"IT\",\"role\":\"Manager\"}"));
            fixture.Customize<ApprovalMatrixUsers>(c => c.With(a => a.UserType, "Manager"));
            fixture.Customize<ApprovalMatrixUsers>(c => c.With(a => a.AcceptType, "Email"));
            fixture.Customize<ApprovalMatrixUsers>(c => c.With(a => a.IsLink, true));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}
