﻿
let mId = sessionStorage.getItem("monitorId");
let monitortype = 'Hp3par';
let infraObjectId = sessionStorage.getItem("infraobjectId");
let replicaType = sessionStorage.getItem('replicationType');
setTimeout(() => { hp3parmonitorstatus(mId, monitortype) }, 250)
//setTimeout(() => { msSQLServer(infraObjectId) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
$('#mssqlserver').hide();
//async function msSQLServer(id) {

//    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
//    let data = {}
//    data.infraObjectId = id;
//    let mssqlServerData = await getAysncWithHandler(url, data);

//    if (mssqlServerData != null && mssqlServerData?.length > 0) {
//        $('#mssqlserver').show();
//        bindMSSQLServer(mssqlServerData)
//    } else {
//        $('#mssqlserver').hide();
//    }

//}
$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})

//function bindMSSQLServer(mssqlServerData) {
//    const rowsValue = mssqlServerData?.map((list, i) => {
//        let serverName = "";

//        if (list?.workflowName && (list?.isServiceUpdate?.toLowerCase() === "error" || list?.isServiceUpdate?.toLowerCase() === "stopped")) {
//            serverName = checkAndReplace(list?.failedActionName);
//        } else {
//            serverName = checkAndReplace(list?.servicePath === null ? list?.workflowName : list?.workflowName === null ? list?.servicePath : list?.workflowName);
//        }

//        const ipAddress = checkAndReplace(list?.ipAddress);
//        const status = checkAndReplace(list?.isServiceUpdate);

//        const iconServer = serverName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";
//        const iconIp = ipAddress === "NA" ? "text-danger cp-disable" : "text-secondary cp-ip-address";

//        let iconStatus;
//        if (status?.toLowerCase() === "running") {
//            iconStatus = "text-success cp-reload cp-animate";
//        } else if (status?.toLowerCase() === "error" || status?.toLowerCase() === "stopped") {
//            iconStatus = "text-danger cp-fail-back";
//        } else {
//            iconStatus = "text-danger cp-disable";
//        }

//        return `<tr><td><i class="${iconServer} me-1 fs-6"></i><span>${serverName}</span></td><td><i class="${iconIp} me-1 fs-6"></i>${ipAddress}</td><td><i class="${iconStatus} me-1 fs-6"></i>${status}</td></tr>`;
//    }).join('');

//    $('#mssqlserverbody').append(rowsValue);
//}


async function hp3parmonitorstatus(id, type) {

    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
}

let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'


function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}

function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}

function propertiesData(value) {
    let ipprdata;
    if (value === undefined || value === null || value === '') {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
    else {
        let data = JSON?.parse(value?.properties);
        let customSite = data?.Hp3parReplicationDRModel?.length > 1;
        if (customSite) {
            $("#Sitediv").show();
        } else {
            $("#Sitediv").hide();
        }


        $(".siteContainer").empty();


        data?.Hp3parReplicationDRModel?.forEach((a, index) => {
            let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
            $(".siteContainer").append(selectTab);
        });


        if (data?.Hp3parReplicationDRModel?.length > 0) {
            $("#siteName0 .nav-link").addClass("active");
            displaySiteData(data?.Hp3parReplicationDRModel[0]);
        }



        let defaultSite = data?.Hp3parReplicationDRModel?.find(d => d?.Type === 'DR') || data?.Hp3parReplicationDRModel[0];
        if (defaultSite) {
            displaySiteData(defaultSite);
        }

        $(document).on('click', '.siteListChange', function () {
            $(".siteListChange .nav-link").removeClass("active");
            $(this).find(".nav-link").addClass("active");
            let siteId = $(this)[0]?.id
            let getSiteName = $(`#${siteId} .siteName`).text()

            let MonitoringModel = data?.Hp3parReplicationDRModel?.find(d => d?.Type === getSiteName);
            if (MonitoringModel) {

                displaySiteData(MonitoringModel);
            }
        });

        function displaySiteData(siteData) {
            let obj = {};
            $('.dynamicSite-header').text(siteData?.Type).attr('title', siteData?.Type);
            
            for (let key in siteData?.Hp3parReplicationMonitoring) {
                obj[key] = siteData?.Hp3parReplicationMonitoring[key];
            }
            //  ipprdata = obj?.DR_connectViaHostName?.toLowerCase() === "true" ? obj?.DR_Server_HostName : obj?.DR_Server_IpAddress

            ///  $("#DR_Server_IpAddress").text(ipprdata)
            let MonitoringModelhp3par = [
                "DR_Server_Name","DR_Server_IpAddress", "DRStorageIPAddress",
                "DRStorageName", "DRGroupName", "DRRole", "DRReplicationMode", "DRState", "DRGroupState", "DRDatalag",
                "LocalVVName", "RemoteVVName", "SyncState", "LastSyncTime"
            ];

            if (Object.keys(obj)?.length > 0) {
                bindProperties(obj, MonitoringModelhp3par, value);
            }
            
            let obj1 = {};
            for (let key in siteData?.VirtualVolumeMonitoring) {
                obj1[key] = siteData?.VirtualVolumeMonitoring[key];
            }
            //  ipprdata = obj?.DR_connectViaHostName?.toLowerCase() === "true" ? obj?.DR_Server_HostName : obj?.DR_Server_IpAddress

            ///  $("#DR_Server_IpAddress").text(ipprdata)
            let MonitoringModelvirtual = [
                
                "LocalVVName", "RemoteVVName", "SyncState", "LastSyncTime"
            ];

            if (Object.keys(obj1)?.length > 0) {
                bindProperties(obj1, MonitoringModelvirtual, value);
            }
            
        }
        let dbhp3parDetail = data?.Hp3ParReplicationPRModel?.Hp3parReplicationPRMonitoring
        ///  ipprdata = dbpostgresDetail?.Pr_ConnectViaHostName?.toLowerCase() === "true" ? dbpostgresDetail?.PR_Server_HostName : dbpostgresDetail?.PR_Server_IpAddress

        // $('#PR_Server_IpAddress').text(ipprdata)
        const dbhp3parDetailsProp = [
            "PR_Server_Name", "PR_Server_IpAddress", "PRStorageIPAddress",
            "PRStorageName", "PRGroupName", "PRRole", "PRReplicationMode", "PRState", "PRGroupState","PR_Datalag"
        ];

        bindProperties(dbhp3parDetail, dbhp3parDetailsProp, value);


        //Datalag
        const datalag = checkAndReplace(data?.PR_Datalag);
        let dataLagValue = (datalag !== undefined && datalag !== "" && datalag !== null && datalag !== "0") ? `${(datalag)}` : 'NA';

        var result = "";

        //if (dataLagValue?.includes(".")) {
        //    var value = dataLagValue?.split(".");
        //    var hours = value[0] * 24;
        //    var minutes = value[1]?.split(':')?.slice(0, 2)?.join(':');
        //    var min = minutes?.split(':');
        //    var firstValue = parseInt(min[0]) + parseInt(hours);
        //    result = firstValue + ":" + min[1];
        //    const minute = (parseInt(result[0]) * 60) + parseInt(result[1]);
        //    minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
        //}
        //else if (dataLagValue?.includes("+")) {
        //    const value = dataLagValue.split(" ");
        //    result = value[1]?.split(':')?.slice(0, 2)?.join(':');
        //    const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
        //    const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
        //    minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')

        //}
        //else {
        //    result = dataLagValue?.split(':')?.slice(0, 2)?.join(':');
        //    const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
        //    const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
        //    minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
        //}
    }
}
$("#replica").text(replicaType).prepend('<i class="cp-replication-type me-1 text-primary"></i>');
function setPropData(data, propSets, value) {
    propSets.forEach(properties => {
        bindProperties(data, properties, value);
    });
}

function bindProperties(data, properties, value) {

    let prStatus = value?.prServerStatus?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : value?.prServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : value?.prServerStatus?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";
    let drStatus = value?.drServerStatus?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : value?.drServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : value?.drServerStatus?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";
    let prservericon = data?.PR_Server_Name ? "cp-server me-1 text-primary" : "text-danger cp-disable"
    let drservericon = data?.DR_Server_Name ? "cp-server me-1 text-primary" : "text-danger cp-disable"
    let prstorage = data?.PRStorageName ? "text-primary cp-storage-name me-1 " : "text-danger cp-disable"
    let drstorage = data?.DRStorageName ? "text-primary cp-storage-name me-1" : "text-danger cp-disable"
    let prgroup = data?.PRGroupName ? "cp-group-policy me-1 text-primary" : "text-danger cp-disable"
    let drgroup = data?.DRGroupName ? "cp-group-policy me-1 text-primary" : "text-danger cp-disable"
    let prrole = data?.PRRole?.toLowerCase()?.includes("primary") ? "text-primary cp-list-prsite" : data?.PRRole?.toLowerCase()?.includes("secondary") ? "text-info cp-dr" : "text-danger cp-disable"
    let drrole = data?.DRRole?.toLowerCase()?.includes("primary") ? "text-primary cp-list-prsite" : data?.DRRole?.toLowerCase()?.includes("secondary") ? "text-info cp-dr" : "text-danger cp-disable"
    let prreplicat = data?.PRReplicationMode ? "cp-replication-type me-1 text-primary" : "text-danger cp-disable"
    let drreplicat = data?.DRReplicationMode ? "cp-replication-type me-1 text-primary" : "text-danger cp-disable"
    let prstate = data?.PRState ? "cp-relationship-state me-1 text-primary" : "text-danger cp-disable"
    let drstate = data?.DRState ? "cp-relationship-state me-1 text-primary" : "text-danger cp-disable"
    let prlag = data?.PR_Datalag ? "cp-time text-primary mt-1" : "text-danger cp-disable"
   
    let prstatus = data?.PRGroupState ? " cp-reload me-1 text-primary" : "text-danger cp-disable"
    let drstatus = data?.DRGroupState ? " cp-reload me-1 text-primary" : "text-danger cp-disable"
    const iconMapping = {
        'PR_Server_IpAddress': prStatus,
        'DR_Server_IpAddress': drStatus,
        'PRStorageIPAddress': prStatus,
        'DRStorageIPAddress': prStatus,
        'PR_Server_Name': prservericon,
        'DR_Server_Name': drservericon,
        'PRStorageName': prstorage,
        'DRStorageName': drstorage,
        'PRGroupName': prgroup,
        'DRGroupName': drgroup,
        'PRRole': prrole,
        'DRRole': drrole,
        'PRReplicationMode': prreplicat,
        'DRReplicationMode': drreplicat,
        'PRState': prstate,
        'DRState': drstate,
        'PR_Datalag': prlag,
     
        'PRGroupState': prstatus,
        'DRGroupState': drstatus,
    };

    properties?.forEach(property => {
        const value = data[property];
        let displayedValue = value !== undefined ? checkAndReplace(value) : 'NA';
        let iconClass = iconMapping[property] || '';

        // Add icons based on conditions
        switch (displayedValue?.toLowerCase()) {
            case 'na':
                iconClass = 'text-danger cp-disable';
                break;
            case 'not allowed':
            case 'no':
                iconClass = 'text-danger cp-disagree';
                break;
            case 'disabled':
            case 'disable':
                iconClass = 'text-danger cp-disables';
                break;
            case 'enabled':
            case 'enable':
                iconClass = 'text-success cp-enables';
                break;
            case ' streaming ':
                iconClass = 'text-success cp-refresh';
                break;
            case 'running':
            case 'run':
                iconClass = 'text-success cp-reload cp-animate';
                break;
            case 'stopped':
            case 'stop':
                iconClass = 'text-danger cp-Stopped';
                break;
            case 'f':
            case 'false':
            case 'defer':
            case 'deferred':
                iconClass = 'text-danger cp-error';
                break;
            case 't':
            case 'true':
            case 'yes':
                iconClass = 'text-success  cp-agree';
                break;
            case 'valid':
                iconClass = 'text-success cp-success';
                break;
            case 'pending':
                iconClass = 'text-warning cp-pending';
                break;
            case 'pause':
            case 'paused':
                iconClass = 'text-warning cp-circle-pause';
                break;
            case 'manual':
                iconClass = 'text-warning cp-settings';
                break;
            case 'idle':
                iconClass = 'text-primary cp-Idle';
                break;
            case 'synchronous_commit':
            case 'synchronized':
            case 'synchronizing':
            case 'sync':
                iconClass = 'text-success cp-refresh';
                break;
            case 'asynchronous_commit':
            case 'asynchronizing':
            case 'asynchronized':
            case 'async':
                iconClass = 'text-danger cp-refresh';
                break;
            case 'online':
                iconClass = 'text-success cp-online';
                break;
            case 'offline':
                iconClass = 'text-danger cp-offline';
                break;
            case 'enabled':
            case 'connected':
            case 'connect':
                iconClass = 'text-success cp-connected';
                break;
            case 'disconnected':
            case 'disconnect':
                iconClass = 'text-danger cp-disconnecteds';
                break;
            case 'standby':
            case 'to standby':
            case 'mounted':
                iconClass = 'text-warning cp-control-file-type';
                break;
            case 'required':
            case 'require':
                iconClass = 'text-warning cp-warning';
                break;
            case 'healthy':
                iconClass = 'text-success cp-health-success';
                break;
            case 'nothealthy':
            case 'not_healthy':
            case 'unhealthy':
                iconClass = 'text-danger cp-health-error';
                break;
            case 'error':
                iconClass = 'text-danger cp-fail-back';
                break;
            case 'on':
                iconClass = 'text-success cp-end';
                break;
            case 'off':
                iconClass = 'text-danger cp-end';
                break;
            case 'current':
            case 'read write':
                iconClass = 'text-success cp-file-edits';
                break;
            case 'primary':
                iconClass = 'text-primary cp-list-prsite';
                break;
            case 'secondary':
                iconClass = 'text-info cp-dr';
                break;
            case 'physical standby':
                iconClass = 'text-info cp-physical-drsite';
                break;
           
            default:
                if (displayedValue?.includes('running')) {
                    iconClass = 'text-success cp-reload cp-animate';
                } else if (displayedValue?.includes('production') || displayedValue?.includes('archive recovery')) {
                    iconClass = 'text-warning cp-log-archive-config';
                }
                break;
        }
        // Displayed value with icon
        const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
        const mergeValue = `${iconHtml}${displayedValue}`;
        $(`#${property}`).html(mergeValue).attr('title', displayedValue);

    });
}
