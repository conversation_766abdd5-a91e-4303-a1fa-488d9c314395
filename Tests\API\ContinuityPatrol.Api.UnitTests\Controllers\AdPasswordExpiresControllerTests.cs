using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Delete;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetList;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AdPasswordExpireModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class AdPasswordExpiresControllerTests : IClassFixture<AdPasswordExpireFixture>
{
    private readonly AdPasswordExpireFixture _adPasswordExpireFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly AdPasswordExpiresController _controller;

    public AdPasswordExpiresControllerTests(AdPasswordExpireFixture adPasswordExpireFixture)
    {
        _adPasswordExpireFixture = adPasswordExpireFixture;

        var testBuilder = new ControllerTestBuilder<AdPasswordExpiresController>();
        _controller = testBuilder.CreateController(
            _ => new AdPasswordExpiresController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAdPasswordExpires_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAdPasswordExpireListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_adPasswordExpireFixture.AdPasswordExpireListVm);

        // Act
        var result = await _controller.GetAdPasswordExpires();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var adPasswordExpires = Assert.IsAssignableFrom<List<AdPasswordExpireListVm>>(okResult.Value);
        Assert.Equal(3, adPasswordExpires.Count);
    }

    [Fact]
    public async Task GetAdPasswordExpires_ReturnsEmptyList_WhenNoAdPasswordExpiresExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAdPasswordExpireListQuery>(), default))
            .ReturnsAsync(new List<AdPasswordExpireListVm>());

        // Act
        var result = await _controller.GetAdPasswordExpires();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var adPasswordExpires = Assert.IsAssignableFrom<List<AdPasswordExpireListVm>>(okResult.Value);
        Assert.Empty(adPasswordExpires);
    }

    [Fact]
    public async Task GetAdPasswordExpireById_ReturnsAdPasswordExpire_WhenIdIsValid()
    {
        // Arrange
        var adPasswordExpireId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAdPasswordExpireDetailQuery>(q => q.Id == adPasswordExpireId), default))
            .ReturnsAsync(_adPasswordExpireFixture.AdPasswordExpireDetailVm);

        // Act
        var result = await _controller.GetAdPasswordExpireById(adPasswordExpireId);

        // Assert
        Assert.IsType<OkObjectResult>(result.Result);
    }

    [Fact]
    public async Task GetAdPasswordExpireById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetAdPasswordExpireById("invalid-guid"));
    }

    [Fact]
    public async Task CreateAdPasswordExpire_Returns201Created()
    {
        // Arrange
        var command = _adPasswordExpireFixture.CreateAdPasswordExpireCommand;
        var expectedMessage = $"AdPasswordExpire '{command.UserName}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAdPasswordExpireResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateAdPasswordExpire(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateAdPasswordExpireResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task CreateAdPasswordExpire_Throws_WhenUserNameExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateAdPasswordExpireCommand>(), default))
            .ThrowsAsync(new InvalidOperationException("UserName exists"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.CreateAdPasswordExpire(_adPasswordExpireFixture.CreateAdPasswordExpireCommand));
    }

    [Fact]
    public async Task UpdateAdPasswordExpire_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"AdPasswordExpire '{_adPasswordExpireFixture.UpdateAdPasswordExpireCommand.UserName}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateAdPasswordExpireCommand>(), default))
            .ReturnsAsync(new UpdateAdPasswordExpireResponse
            {
                Message = expectedMessage,
                Id = _adPasswordExpireFixture.UpdateAdPasswordExpireCommand.Id
            });

        // Act
        var result = await _controller.UpdateAdPasswordExpire(_adPasswordExpireFixture.UpdateAdPasswordExpireCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateAdPasswordExpireResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAdPasswordExpire_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "AdPasswordExpire 'testuser' has been deleted successfully!.";
        var adPasswordExpireId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAdPasswordExpireCommand>(c => c.Id == adPasswordExpireId), default))
            .ReturnsAsync(new DeleteAdPasswordExpireResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteAdPasswordExpire(adPasswordExpireId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteAdPasswordExpireResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAdPasswordExpire_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteAdPasswordExpire("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedAdPasswordExpires_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetAdPasswordExpirePaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _adPasswordExpireFixture.AdPasswordExpireListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAdPasswordExpirePaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<AdPasswordExpireListVm>.Success(
                data: expectedData,
                count: expectedData.Count,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        // Act
        var result = await _controller.GetPaginatedAdPasswordExpires(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<AdPasswordExpireListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<AdPasswordExpireListVm>>(okResult.Value);

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(3, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task IsAdPasswordExpireNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAdPasswordExpireNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsAdPasswordExpireNameExist("ExistingUser", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsAdPasswordExpireNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetAdPasswordExpireNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsAdPasswordExpireNameExist("NewUser", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsAdPasswordExpireNameExist_ThrowsInvalidArgumentException_WhenNameIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsAdPasswordExpireNameExist("", null));
    }

    [Fact]
    public async Task IsAdPasswordExpireNameExist_ThrowsArgumentNullException_WhenNameIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _controller.IsAdPasswordExpireNameExist(null!, null));
    }

    [Fact]
    public async Task GetAdPasswordExpires_CallsCorrectQuery()
    {
        // Arrange
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAdPasswordExpireListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<AdPasswordExpireListVm>());

        // Act
        await _controller.GetAdPasswordExpires();

        // Assert
        Assert.True(queryExecuted);
    }

    [Fact]
    public async Task CreateAdPasswordExpire_ClearsCacheAfterCreation()
    {
        // Arrange
        var command = _adPasswordExpireFixture.CreateAdPasswordExpireCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAdPasswordExpireResponse
            {
                Message = "Created successfully",
                Id = Guid.NewGuid().ToString()
            });

        // Act
        await _controller.CreateAdPasswordExpire(command);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

   

    [Fact]
    public async Task IsAdPasswordExpireNameExist_HandlesComplexDomainNames()
    {
        // Arrange
        var complexDomainName = "subdomain.complex-domain-name.enterprise.com";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAdPasswordExpireNameUniqueQuery>(q => q.Name == complexDomainName), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsAdPasswordExpireNameExist(complexDomainName, null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    
    [Fact]
    public async Task DeleteAdPasswordExpire_VerifiesPasswordExpireIsDeactivated()
    {
        // Arrange
        var adPasswordExpireId = Guid.NewGuid().ToString();
        var expectedMessage = "AdPasswordExpire 'Test Policy' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAdPasswordExpireCommand>(c => c.Id == adPasswordExpireId), default))
            .ReturnsAsync(new DeleteAdPasswordExpireResponse
            {
                IsActive = false, // Verify it's deactivated
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteAdPasswordExpire(adPasswordExpireId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteAdPasswordExpireResponse>(okResult.Value);
        Assert.False(response.IsActive);
        Assert.Equal(expectedMessage, response.Message);
    }

    

    [Fact]
    public async Task IsAdPasswordExpireNameExist_HandlesUnicodeCharacters()
    {
        // Arrange
        var unicodeName = "Política de Contraseña - Administración";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAdPasswordExpireNameUniqueQuery>(q => q.Name == unicodeName), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsAdPasswordExpireNameExist(unicodeName, null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }
}
