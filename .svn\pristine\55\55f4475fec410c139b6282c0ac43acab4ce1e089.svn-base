﻿using ContinuityPatrol.Domain.ViewModels.HeatMapStatusModel;

namespace ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetImpactDetails;

public class GetImpactDetailQueryHandler : IRequestHandler<GetImpactDetailQuery, ImpactDetailVm>
{
    private readonly IDatabaseViewRepository _databaseViewRepository;
     private readonly IHeatMapStatusRepository _heatMapStatusRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IReplicationViewRepository _replicationViewRepository;
    private readonly IServerViewRepository _serverViewRepository;

    private readonly IBusinessFunctionRepository _businessFunctionRepository;
    private readonly IDashboardViewRepository _dashboardViewRepository;

    public GetImpactDetailQueryHandler(IInfraObjectRepository infraObjectRepository,
        IServerViewRepository serverViewRepository, IReplicationViewRepository replicationViewRepository,  IHeatMapStatusRepository heatMapStatusRepository,
    IDatabaseViewRepository databaseViewRepository, 
        IBusinessFunctionRepository businessFunctionRepository, IDashboardViewRepository dashboardViewRepository)
    {
        _infraObjectRepository = infraObjectRepository;
         _heatMapStatusRepository = heatMapStatusRepository;
        _serverViewRepository = serverViewRepository;
        _replicationViewRepository = replicationViewRepository;
        _databaseViewRepository = databaseViewRepository;
        _businessFunctionRepository = businessFunctionRepository;
        _dashboardViewRepository = dashboardViewRepository;
    }

    public async Task<ImpactDetailVm> Handle(GetImpactDetailQuery request, CancellationToken cancellationToken)
    {
        #region old Working 
        //var impactDetail = await _heatMapStatusRepository.GroupHeatMapTypeByServiceId(request.BusinessServiceId) ?? new ImpactDetailVm();

        //var server = await _serverViewRepository.ServerCountAsync();

        //var replication = await _replicationViewRepository.ReplicationCountAsync();

        //var database = await _databaseViewRepository.DatabaseCountAsync();

        //impactDetail.ServerTotalCount = server;
        //impactDetail.ReplicationTotalCount = replication;
        //impactDetail.DatabaseTotalCount = database;

        //return impactDetail;
        #endregion
        var impactDetdail = await _heatMapStatusRepository.GroupHeatMapTypeByServiceId(request.BusinessServiceId) ?? new ImpactDetailVm();

            var impactDetail = new ImpactDetailVm();
        #region server
        var serverList = await _serverViewRepository.GetServerByBusinessServiceId(request.BusinessServiceId);

        var downServerDetail = serverList.Where(x => x.Status != null && x.Status.ToLower().Equals("down")).ToList();

        //var infraList = await _infraObjectRepository.ListAllAsync();
        var infraList = await _infraObjectRepository.GetInfraObjectByBusinessServiceId(request.BusinessServiceId);


        var infraObjectListByDownServer = infraList
            .Where(i => i.ServerProperties != null)
            .SelectMany(i => downServerDetail
                .Where(ds => i.ServerProperties.Contains(ds.ReferenceId))
                .Select(ds => new HeatMapStatusListVm
                {
                    BusinessFunctionId = i.BusinessServiceId,
                    BusinessFunctionName=i.BusinessFunctionName,    
                    BusinessServiceId=i.BusinessServiceId,
                    BusinessServiceName=i.BusinessServiceName,
                    InfraObjectId = i.ReferenceId,
                    InfraObjectName=i.Name,
                    EntityId = ds.ReferenceId,
                    EntityName = ds.Name,
                    Status=ds.Status,
                    Type=ds.OSType,
                    IpAddress= ds.IpAddress ?? ds.HostName,
                    ErrorMessage = ds.ExceptionMessage
                })
            )
            .ToList();

        impactDetail.ServerDownList = infraObjectListByDownServer;
        impactDetail.ServerDownCount = impactDetail.ServerDownList.Count;

        #endregion
        #region Database
        var databaseList = await _databaseViewRepository.GetDatabaseByBusinessServiceId(request.BusinessServiceId);

        var downdatabaseDetail = databaseList.Where(x => x.ModeType != null && x.ModeType.ToLower().Equals("down")).ToList();

        var infraObjectListByDownDatabase = infraList
           .Where(i => i.ServerProperties != null)
           .SelectMany(i => downdatabaseDetail
               .Where(ds => i.DatabaseProperties.Contains(ds.ReferenceId))
               .Select(ds => new HeatMapStatusListVm
               {
                   BusinessFunctionId = i.BusinessServiceId,
                   BusinessFunctionName = i.BusinessFunctionName,
                   BusinessServiceId = i.BusinessServiceId,
                   BusinessServiceName = i.BusinessServiceName,
                   InfraObjectId = i.ReferenceId,
                   InfraObjectName = i.Name,
                   EntityId = ds.ReferenceId,
                   EntityName = ds.Name,
                   Type = ds.DatabaseType,
                   Status = ds.ModeType,
                   DatabaseName  = ds.SID,
                   ErrorMessage = ds.ExceptionMessage
               })
           )
           .ToList();

        impactDetail.DatabaseDownList = infraObjectListByDownDatabase;
        impactDetail.DatabaseDownCount = impactDetail.DatabaseDownList.Count;

        #endregion
        #region Replication
        var replicationList = await _replicationViewRepository.GetReplicationByBusinessServiceId(request.BusinessServiceId);

        var businessFunction = await _businessFunctionRepository.GetBusinessFunctionListByBusinessServiceId(request.BusinessServiceId);

        var dashboardView = await _dashboardViewRepository.GetBusinessViewListByBusinessServiceId(request.BusinessServiceId);



        var tasks = infraList
            .Where(i => i.ReplicationProperties != null)
            .SelectMany(i =>
            {
                var businessFunction1 = businessFunction.FirstOrDefault(x => x.ReferenceId == i.BusinessFunctionId);
                var dashboard = dashboardView.FirstOrDefault(x => x.InfraObjectId == i.ReferenceId);

                return replicationList
                    .Where(ds => i.ReplicationProperties.Contains(ds.ReferenceId))
                    .Select(async ds =>
                    {
                        int totalMinutes = 0;

                        if (dashboard?.DataLagValue != null)
                        {
                            if (dashboard.DataLagValue.Length <= 8)
                            {
                                totalMinutes = Convert.ToInt16(await ConvertToMinutes(dashboard.DataLagValue));
                            }
                            else
                            {
                                string[] parts = dashboard.DataLagValue.Contains(".")
                                    ? dashboard.DataLagValue.Split('.')
                                    : dashboard.DataLagValue.Split(' ');
                                int days = Convert.ToInt32(parts[0]);
                                int totalMinutesFromDays = await ConvertDaysToMinutes(days);
                                int minutes = Convert.ToInt16(await ConvertToMinutes(parts[1]));
                                totalMinutes = totalMinutesFromDays + minutes;      
                            }
                        }
                            
                        bool isAffected = totalMinutes > Convert.ToInt32(businessFunction1?.ConfiguredRPO ?? "0");
                        if (!isAffected)
                            return null;

                        return new HeatMapStatusListVm
                            {
                                BusinessFunctionId = i.BusinessFunctionId,
                                BusinessFunctionName = i.BusinessFunctionName,
                                BusinessServiceId = i.BusinessServiceId,
                                BusinessServiceName = i.BusinessServiceName,
                                InfraObjectId = i.ReferenceId,
                                InfraObjectName = i.Name,
                                EntityId = ds.ReferenceId,
                                EntityName = ds.Name,
                                ErrorMessage = isAffected ? $"Replication: {ds.Name} - status - datalag exceed. Configured RPO: {businessFunction1.ConfiguredRPO}, Current RPO: {dashboard.CurrentRPO} and the given InfraObject {i.Name} ." : "",
                                Type = ds.Type,
                                Status = isAffected ? "datalag exceeded" : ""
                            };
                        
                    });
            }).ToList();

        // Await all tasks and return the result
        var infraObjectListByDownReplication = (await Task.WhenAll(tasks)).ToList();


        impactDetail.ReplicationDownList = infraObjectListByDownReplication;

        impactDetail.ReplicationDownCount = infraObjectListByDownReplication.Count;
        #endregion

        var server = await _serverViewRepository.ServerCountAsync();

        var replication = await _replicationViewRepository.ReplicationCountAsync();

        var database = await _databaseViewRepository.DatabaseCountAsync();

        impactDetail.ServerTotalCount = server;
        impactDetail.ReplicationTotalCount = replication;
        impactDetail.DatabaseTotalCount = database;

        return impactDetail;
    }

    private Task<int> ConvertDaysToMinutes(int days)
    {
        const int hoursPerDay = 24;
        const int minutesPerHour = 60;

        int totalMinutes = days * hoursPerDay * minutesPerHour;

        return Task.FromResult(totalMinutes);
    }

    private Task<double> ConvertToMinutes(string timeStamp)
    {
        return Task.FromResult(timeStamp.StartsWith("+") && timeStamp.Substring(1).Split(' ') is var parts && parts.Length == 2 && TimeSpan.TryParse(parts[1], out var timeSpanPlus)
               ? timeSpanPlus.TotalMinutes
               : TimeSpan.TryParse(timeStamp, out var timeSpan)
               ? timeSpan.TotalMinutes
               : 0);
    }
}