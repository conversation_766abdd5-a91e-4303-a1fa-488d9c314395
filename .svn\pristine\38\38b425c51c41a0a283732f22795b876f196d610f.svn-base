﻿using ContinuityPatrol.Application.Features.WorkflowActionType.Events.Delete;

namespace ContinuityPatrol.Application.Features.WorkflowActionType.Commands.Delete;

public class
    DeleteWorkflowActionTypeCommandHandler : IRequestHandler<DeleteWorkflowActionTypeCommand,
        DeleteWorkflowActionTypeResponse>
{
    private readonly IPublisher _publisher;
    private readonly IWorkflowActionTypeRepository _workflowActionTypeRepository;

    public DeleteWorkflowActionTypeCommandHandler(IWorkflowActionTypeRepository workflowActionTypeRepository,
        IPublisher publisher)
    {
        _workflowActionTypeRepository = workflowActionTypeRepository;
        _publisher = publisher;
    }

    public async Task<DeleteWorkflowActionTypeResponse> Handle(DeleteWorkflowActionTypeCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _workflowActionTypeRepository.GetWorkflowActionTypeById(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.WorkflowActionType),
            new NotFoundException(nameof(Domain.Entities.WorkflowActionType), request.Id));

        eventToDelete.IsActive = false;

        await _workflowActionTypeRepository.UpdateAsync(eventToDelete);

        var response = new DeleteWorkflowActionTypeResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.WorkflowActionType), eventToDelete.ActionType),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new WorkflowActionTypeDeletedEvent { ActionType = eventToDelete.ActionType },
            cancellationToken);

        return response;
    }
}