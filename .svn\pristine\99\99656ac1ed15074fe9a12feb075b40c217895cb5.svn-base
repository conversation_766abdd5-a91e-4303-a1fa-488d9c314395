using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.Extensions.Configuration;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DataSetColumnsRepositoryTests : IClassFixture<DataSetColumnsFixture>
{
    private readonly DataSetColumnsFixture _dataSetColumnsFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DataSetColumnsRepository _repository;
    private readonly Mock<IConfiguration> _mockConfiguration;

    public DataSetColumnsRepositoryTests(DataSetColumnsFixture dataSetColumnsFixture)
    {
        _dataSetColumnsFixture = dataSetColumnsFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockConfiguration = new Mock<IConfiguration>();
        
        // Setup configuration mock
        var mockSection = new Mock<IConfigurationSection>();
        mockSection.Setup(x => x.Value).Returns("mysql");
        _mockConfiguration.Setup(x => x.GetSection("ConnectionStrings").GetSection("DBProvider")).Returns(mockSection.Object);
        
        _repository = new DataSetColumnsRepository(_dbContext, _mockConfiguration.Object);
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var dataSetColumns = _dataSetColumnsFixture.DataSetColumnsDto;

        // Act
        await _dbContext.DataSetColumns.AddAsync(dataSetColumns);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(dataSetColumns.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSetColumns.TableName, result.TableName);
        Assert.Equal(dataSetColumns.DataSetId, result.DataSetId);
        Assert.Equal(dataSetColumns.ColumnName, result.ColumnName);
        Assert.Single(_dbContext.DataSetColumns);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var dataSetColumns = _dataSetColumnsFixture.DataSetColumnsDto;
        await _dbContext.DataSetColumns.AddAsync(dataSetColumns);
        await _dbContext.SaveChangesAsync();

        dataSetColumns.ColumnName = "UpdatedColumnName";

        // Act
        _dbContext.DataSetColumns.Update(dataSetColumns);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(dataSetColumns.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedColumnName", result.ColumnName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var dataSetColumns = _dataSetColumnsFixture.DataSetColumnsDto;
        await _dbContext.DataSetColumns.AddAsync(dataSetColumns);
        await _dbContext.SaveChangesAsync();

        // Act
        dataSetColumns.IsActive = false;

        _dbContext.DataSetColumns.Update(dataSetColumns);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dataSetColumns = _dataSetColumnsFixture.DataSetColumnsDto;
        var addedEntity = await _repository.AddAsync(dataSetColumns);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.TableName, result.TableName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dataSetColumns = _dataSetColumnsFixture.DataSetColumnsDto;
        await _repository.AddAsync(dataSetColumns);

        // Act
        var result = await _repository.GetByReferenceIdAsync(dataSetColumns.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSetColumns.ReferenceId, result.ReferenceId);
        Assert.Equal(dataSetColumns.TableName, result.TableName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var dataSetColumns = _dataSetColumnsFixture.DataSetColumnsList;
        await _repository.AddRangeAsync(dataSetColumns);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSetColumns.Count, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldNotReturnInactiveEntities()
    {
        // Arrange
        var dataSetColumns = _dataSetColumnsFixture.DataSetColumnsList;
        dataSetColumns.First().IsActive = false; // Make one inactive
        await _repository.AddRangeAsync(dataSetColumns);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSetColumns.Count - 1, result.Count); // Should exclude the inactive one
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var dataSetColumns = _dataSetColumnsFixture.DataSetColumnsList;

        // Act
        var result = await _repository.AddRangeAsync(dataSetColumns);

        // Assert
        Assert.Equal(dataSetColumns.Count, result.Count());
        Assert.Equal(dataSetColumns.Count, _dbContext.DataSetColumns.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var dataSetColumns = _dataSetColumnsFixture.DataSetColumnsList;
        await _repository.AddRangeAsync(dataSetColumns);

        // Act
        var result = await _repository.RemoveRangeAsync(dataSetColumns);

        // Assert
        Assert.Equal(dataSetColumns.Count, result.Count());
        Assert.Empty(_dbContext.DataSetColumns);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var dataSetColumns = _dataSetColumnsFixture.DataSetColumnsList.Take(5).ToList();
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(dataSetColumns);
        var initialCount = dataSetColumns.Count;
        
        var toUpdate = dataSetColumns.Take(2).ToList();
        toUpdate.ForEach(x => x.ColumnName = "UpdatedColumnName");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = dataSetColumns.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.ColumnName == "UpdatedColumnName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
