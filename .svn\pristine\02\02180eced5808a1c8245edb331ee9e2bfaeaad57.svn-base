﻿using ContinuityPatrol.Application.Features.SvcMsSqlMonitorStatus.Queries.GetByType;

namespace ContinuityPatrol.Application.UnitTests.Features.SvcMsSqlMonitorStatus.Queries
{
    public class SvcMsSqlMonitorStatusDetailByTypeQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISvcMsSqlMonitorStatusRepository> _mockSvcMsSqlMonitorStatusRepository;
        private readonly SvcMsSqlMonitorStatusDetailByTypeQueryHandler _handler;

        public SvcMsSqlMonitorStatusDetailByTypeQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSvcMsSqlMonitorStatusRepository = new Mock<ISvcMsSqlMonitorStatusRepository>();
            _handler = new SvcMsSqlMonitorStatusDetailByTypeQueryHandler(_mockMapper.Object, _mockSvcMsSqlMonitorStatusRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedResult_WhenResultsExist()
        {
            var query = new SvcMsSqlMonitorStatusDetailByTypeQuery { Type = "Critical" };

            var svcMsSqlMonitorStatusEntities = new List<Domain.Entities.SvcMsSqlMonitorStatus>
            {
                new Domain.Entities.SvcMsSqlMonitorStatus { Id = 1, WorkflowName = "Critical", Type = "Active" },
                new Domain.Entities.SvcMsSqlMonitorStatus { Id = 2, WorkflowName = "Critical", Type = "Inactive" }
            };

            var expectedViewModels = new List<SvcMsSqlMonitorStatusDetailByTypeVm>
            {
                new SvcMsSqlMonitorStatusDetailByTypeVm { WorkflowName = "Critical", Type = "Active" },
                new SvcMsSqlMonitorStatusDetailByTypeVm { WorkflowName = "Critical", Type = "Inactive" }
            };

            _mockSvcMsSqlMonitorStatusRepository.Setup(repo => repo.GetDetailByType(query.Type))
                .ReturnsAsync(svcMsSqlMonitorStatusEntities);

            _mockMapper.Setup(m => m.Map<List<SvcMsSqlMonitorStatusDetailByTypeVm>>(svcMsSqlMonitorStatusEntities))
                .Returns(expectedViewModels);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(expectedViewModels.Count, result.Count);
            Assert.Equal(expectedViewModels, result);

            _mockSvcMsSqlMonitorStatusRepository.Verify(repo => repo.GetDetailByType(query.Type), Times.Once);
            _mockMapper.Verify(m => m.Map<List<SvcMsSqlMonitorStatusDetailByTypeVm>>(svcMsSqlMonitorStatusEntities), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoResultsExist()
        {
            var query = new SvcMsSqlMonitorStatusDetailByTypeQuery { Type = "Critical" };

            _mockSvcMsSqlMonitorStatusRepository.Setup(repo => repo.GetDetailByType(query.Type))
                .ReturnsAsync(new List<Domain.Entities.SvcMsSqlMonitorStatus>());

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);

            _mockSvcMsSqlMonitorStatusRepository.Verify(repo => repo.GetDetailByType(query.Type), Times.Once);
            _mockMapper.Verify(m => m.Map<List<SvcMsSqlMonitorStatusDetailByTypeVm>>(It.IsAny<List<Domain.Entities.SvcMsSqlMonitorStatus>>()), Times.Never);
        }
    }
}
