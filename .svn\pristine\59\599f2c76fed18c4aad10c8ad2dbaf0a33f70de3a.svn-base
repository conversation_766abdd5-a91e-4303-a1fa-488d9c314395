using ContinuityPatrol.Application.Features.CyberAlert.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAlert.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAlert.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAlert.Queries.GetCyberAlertCount;
using ContinuityPatrol.Application.Features.CyberAlert.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.CyberAlertModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class CyberAlertFixture
{
    public CreateCyberAlertCommand CreateCyberAlertCommand { get; }
    public UpdateCyberAlertCommand UpdateCyberAlertCommand { get; }
    public DeleteCyberAlertCommand DeleteCyberAlertCommand { get; }
    public CyberAlertListVm CyberAlertListVm { get; }
    public CyberAlertDetailVm CyberAlertDetailVm { get; }
    public CyberAlertCountVm CyberAlertCountVm { get; }

    public CyberAlertFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CreateCyberAlertCommand>(c => c
            .With(b => b.Type, "System Error")
            .With(b => b.Severity, "Critical")
            .With(b => b.SystemMessage, "Database connection timeout occurred during backup operation")
            .With(b => b.UserMessage, "Backup operation failed due to database connectivity issues. Please check network connection and retry.")
            .With(b => b.JobName, "Daily Database Backup")
            .With(b => b.ClientAlertId, Guid.NewGuid().ToString())
            .With(b => b.IsResolve, 0)
            .With(b => b.IsAcknowledgement, 0)
            .With(b => b.EntityId, Guid.NewGuid().ToString())
            .With(b => b.EntityType, "Database")
            .With(b => b.AlertCategoryId, 1));

        fixture.Customize<UpdateCyberAlertCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Type, "Network Warning")
            .With(b => b.Severity, "Warning")
            .With(b => b.SystemMessage, "Network latency increased above threshold during replication")
            .With(b => b.UserMessage, "Network performance degraded. Monitoring replication performance.")
            .With(b => b.JobName, "Replication Monitor")
            .With(b => b.ClientAlertId, Guid.NewGuid().ToString())
            .With(b => b.IsResolve, 1)
            .With(b => b.IsAcknowledgement, 1)
            .With(b => b.EntityId, Guid.NewGuid().ToString())
            .With(b => b.EntityType, "Network")
            .With(b => b.AlertCategoryId, 2));

        fixture.Customize<DeleteCyberAlertCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<CyberAlertListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Type, () => fixture.Create<bool>() ? "System Error" : "Performance Warning")
            .With(b => b.Severity, () => fixture.Create<bool>() ? "Critical" : "Warning")
            .With(b => b.SystemMessage, () => $"System alert: {fixture.Create<string>().Substring(0, 20)}")
            .With(b => b.UserMessage, () => $"User notification: {fixture.Create<string>().Substring(0, 30)}")
            .With(b => b.JobName, () => $"Job-{fixture.Create<string>().Substring(0, 10)}")
            .With(b => b.ClientAlertId, Guid.NewGuid().ToString())
            .With(b => b.IsResolve, () => fixture.Create<int>() % 2)
            .With(b => b.IsAcknowledgement, () => fixture.Create<int>() % 2)
            .With(b => b.EntityId, Guid.NewGuid().ToString())
            .With(b => b.EntityType, () => fixture.Create<bool>() ? "Database" : "Network")
            .With(b => b.AlertCategoryId, () => fixture.Create<int>() % 5 + 1)
            .With(b => b.CreatedDate, () => DateTime.Now.AddDays(-(fixture.Create<int>() % 30)))
            .With(b => b.LastModifiedDate, () => DateTime.Now.AddHours(-(fixture.Create<int>() % 24))));

        fixture.Customize<CyberAlertDetailVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Type, "Critical System Failure")
            .With(b => b.Severity, "Critical")
            .With(b => b.SystemMessage, "Critical system failure detected in primary database server. Automatic failover initiated to secondary server. Data integrity verified.")
            .With(b => b.UserMessage, "Critical alert: Primary database server has failed. System has automatically switched to backup server. All services are operational. Technical team has been notified.")
            .With(b => b.JobName, "Database Failover Monitor")
            .With(b => b.ClientAlertId, Guid.NewGuid().ToString())
            .With(b => b.IsResolve, 0)
            .With(b => b.IsAcknowledgement, 1)
            .With(b => b.EntityId, Guid.NewGuid().ToString())
            .With(b => b.EntityType, "Database Server")
            .With(b => b.AlertCategoryId, 1));

        fixture.Customize<CyberAlertCountVm>(c => c
            .With(b => b.TotalCount, 150)
            .With(b => b.AlertSeverityCount, new Dictionary<string, int>
            {
                { "critical", 25 },
                { "warning", 75 },
                { "info", 50 }
            }));

        CreateCyberAlertCommand = fixture.Create<CreateCyberAlertCommand>();
        UpdateCyberAlertCommand = fixture.Create<UpdateCyberAlertCommand>();
        DeleteCyberAlertCommand = fixture.Create<DeleteCyberAlertCommand>();
        CyberAlertListVm = fixture.Create<CyberAlertListVm>();
        CyberAlertDetailVm = fixture.Create<CyberAlertDetailVm>();
        CyberAlertCountVm = fixture.Create<CyberAlertCountVm>();
    }
}
