﻿namespace ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessFunctionId;

public class GetDashboardViewByBusinessFunctionIdQueryHandler : IRequestHandler<
    GetDashboardViewByBusinessFunctionIdQuery, List<DashboardViewByBusinessFunctionIdVm>>
{
    private readonly IDashboardViewRepository _dashboardViewRepository;
    private readonly IMapper _mapper;

    public GetDashboardViewByBusinessFunctionIdQueryHandler(IMapper mapper,
        IDashboardViewRepository dashboardViewRepository)
    {
        _mapper = mapper;
        _dashboardViewRepository = dashboardViewRepository;
    }

    public async Task<List<DashboardViewByBusinessFunctionIdVm>> Handle(
        GetDashboardViewByBusinessFunctionIdQuery request, CancellationToken cancellationToken)
    {
        var dataLagStatus =
            (await _dashboardViewRepository.GetBusinessViewListByBusinessFunctionId(request.BusinessFunctionId))
            .ToList();

        return dataLagStatus.Count <= 0
            ? new List<DashboardViewByBusinessFunctionIdVm>()
            : _mapper.Map<List<DashboardViewByBusinessFunctionIdVm>>(dataLagStatus);
    }
}