﻿using ContinuityPatrol.Application.Features.User.Events.UserUnLock;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.User.Events
{
    public class UserUserUnLockEventTests
    {
        private readonly Mock<ILogger<UserUserUnLockEventHandler>> _loggerMock;
        private readonly Mock<IUserActivityRepository> _userActivityRepositoryMock;
        private readonly Mock<ILoggedInUserService> _userServiceMock;

        private readonly UserUserUnLockEventHandler _handler;

        public UserUserUnLockEventTests()
        {
            _loggerMock = new Mock<ILogger<UserUserUnLockEventHandler>>();
            _userActivityRepositoryMock = new Mock<IUserActivityRepository>();
            _userServiceMock = new Mock<ILoggedInUserService>();

            _handler = new UserUserUnLockEventHandler(
                _loggerMock.Object,
                _userActivityRepositoryMock.Object,
                _userServiceMock.Object
            );
        }

        [Fact]
        public async Task Handle_Should_CreateUserActivity_And_LogInformation()
        {
            var notification = new UserUserUnLockEvent
            {
                UserName = "TestUser"
            };

            _userServiceMock.Setup(x => x.UserId).Returns("12345");
            _userServiceMock.Setup(x => x.LoginName).Returns("TestLogin");
            _userServiceMock.Setup(x => x.RequestedUrl).Returns("/test/url");
            _userServiceMock.Setup(x => x.CompanyId).Returns("67890");
            _userServiceMock.Setup(x => x.IpAddress).Returns("127.0.0.1");

            await _handler.Handle(notification, CancellationToken.None);

            _userActivityRepositoryMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "12345" &&
                activity.LoginName == "TestLogin" &&
                activity.RequestUrl == "/test/url" &&
                activity.CompanyId == "67890" &&
                activity.HostAddress == "127.0.0.1" &&
                activity.Entity == Modules.User.ToString() &&
                activity.Action == $"{ActivityType.Unlock} {Modules.User}" &&
                activity.ActivityType == ActivityType.Unlock.ToString() &&
                activity.ActivityDetails == $"User 'TestUser' unlocked successfully!."
            )), Times.Once);

            _loggerMock.Verify(log => log.LogInformation(It.Is<string>(msg =>
                msg == "User 'TestUser' unlocked successfully!."
            )), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_NotThrowException_When_NotificationIsValid()
        {
            var notification = new UserUserUnLockEvent
            {
                UserName = "ValidUser"
            };

            _userServiceMock.Setup(x => x.UserId).Returns("54321");
            _userServiceMock.Setup(x => x.LoginName).Returns("ValidLogin");
            _userServiceMock.Setup(x => x.RequestedUrl).Returns("/valid/url");
            _userServiceMock.Setup(x => x.CompanyId).Returns("98765");
            _userServiceMock.Setup(x => x.IpAddress).Returns("***********");

            var exception = await Record.ExceptionAsync(() => _handler.Handle(notification, CancellationToken.None));

            Assert.Null(exception);

            _userActivityRepositoryMock.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
            _loggerMock.Verify(log => log.LogInformation(It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Call_All_Dependencies()
        {
            var notification = new UserUserUnLockEvent
            {
                UserName = "DependencyTestUser"
            };

            _userServiceMock.Setup(x => x.UserId).Returns("112233");
            _userServiceMock.Setup(x => x.LoginName).Returns("DependencyTestLogin");
            _userServiceMock.Setup(x => x.RequestedUrl).Returns("/dependency/test/url");
            _userServiceMock.Setup(x => x.CompanyId).Returns("445566");
            _userServiceMock.Setup(x => x.IpAddress).Returns("********");

            await _handler.Handle(notification, CancellationToken.None);

            _userServiceMock.VerifyGet(x => x.UserId, Times.Once);
            _userServiceMock.VerifyGet(x => x.LoginName, Times.Once);
            _userServiceMock.VerifyGet(x => x.RequestedUrl, Times.Once);
            _userServiceMock.VerifyGet(x => x.CompanyId, Times.Once);
            _userServiceMock.VerifyGet(x => x.IpAddress, Times.Once);

            _userActivityRepositoryMock.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
            _loggerMock.Verify(log => log.LogInformation(It.IsAny<string>()), Times.Once);
        }
    }
}
