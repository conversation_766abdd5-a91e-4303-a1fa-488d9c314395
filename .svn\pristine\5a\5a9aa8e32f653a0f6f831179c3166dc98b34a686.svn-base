﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CGExecutionReport.Events.Update;

public class CGExecutionUpdatedEventHandler : INotificationHandler<CGExecutionUpdatedEvent>
{
    private readonly ILogger<CGExecutionUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CGExecutionUpdatedEventHandler(ILoggedInUserService userService, ILogger<CGExecutionUpdatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }
    public async Task Handle(CGExecutionUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.CGExecution}",
            Entity = Modules.CGExecution.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"CG Execution '{updatedEvent.WorkflowName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"CG Execution '{updatedEvent.WorkflowName}' updated successfully.");
    }
}
