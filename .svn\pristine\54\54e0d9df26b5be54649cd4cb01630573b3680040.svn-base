﻿using ContinuityPatrol.Application.Features.Company.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Company.Events;

public class DeleteCompanyEventTests : IClassFixture<CompanyFixture>, IClassFixture<UserActivityFixture>
{
    private readonly CompanyFixture _companyFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly CompanyDeletedEventHandler _handler;

    public DeleteCompanyEventTests(CompanyFixture companyFixture, UserActivityFixture userActivityFixture)
    {
        _companyFixture = companyFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockCompanyEventLogger = new Mock<ILogger<CompanyDeletedEventHandler>>();

        _mockUserActivityRepository = CompanyRepositoryMocks.CreateCompanyEventRepository(_userActivityFixture.UserActivities);

        _handler = new CompanyDeletedEventHandler(mockLoggedInUserService.Object, mockCompanyEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateCompanyEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_companyFixture.CompanyDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_companyFixture.CompanyDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}