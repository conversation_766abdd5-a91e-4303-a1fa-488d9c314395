using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class OracleMonitorLogsFixture : IDisposable
{
    public List<OracleMonitorLogs> OracleMonitorLogsPaginationList { get; set; }
    public List<OracleMonitorLogs> OracleMonitorLogsList { get; set; }
    public OracleMonitorLogs OracleMonitorLogsDto { get; set; }


    public ApplicationDbContext DbContext { get; private set; }

    public OracleMonitorLogsFixture()
    {
        var fixture = new Fixture();

        OracleMonitorLogsList = fixture.Create<List<OracleMonitorLogs>>();

        OracleMonitorLogsPaginationList = fixture.CreateMany<OracleMonitorLogs>(20).ToList();

        OracleMonitorLogsDto = fixture.Create<OracleMonitorLogs>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
