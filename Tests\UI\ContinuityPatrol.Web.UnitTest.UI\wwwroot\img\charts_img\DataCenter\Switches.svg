<svg width="39" height="48" viewBox="0 0 39 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_78_4993)">
<g filter="url(#filter0_d_78_4993)">
<path d="M35.2908 13.499H3.55006L1.08398 26.1476H37.916L35.2908 13.499Z" fill="url(#paint0_radial_78_4993)"/>
<path d="M37.916 26.1489L1.08398 26.0693V34.5017H37.8365L37.916 26.1489Z" fill="url(#paint1_radial_78_4993)"/>
<rect x="2.51758" y="27.8174" width="1.27281" height="1.27281" fill="white"/>
<rect x="2.51758" y="30.0459" width="1.27281" height="1.27281" fill="white"/>
<rect x="2.51758" y="32.2734" width="1.27281" height="1.27281" fill="white"/>
<path d="M13.7315 27.5H8.87891V31.7162H9.99262V33.0686H12.6178V31.7162H13.7315V27.5Z" fill="#234A85"/>
<path d="M23.2764 27.5H18.4238V31.7162H19.5375V33.0686H22.1627V31.7162H23.2764V27.5Z" fill="#234A85"/>
<path d="M32.8233 27.5H27.9707V31.7162H29.0844V33.0686H31.7096V31.7162H32.8233V27.5Z" fill="#234A85"/>
</g>
</g>
<defs>
<filter id="filter0_d_78_4993" x="-0.916016" y="12.499" width="40.832" height="25.0029" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_78_4993"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_78_4993" result="shape"/>
</filter>
<radialGradient id="paint0_radial_78_4993" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-5.76385 12.664) scale(58.0191 34.4517)">
<stop stop-color="#9AEDFF"/>
<stop offset="0.516877" stop-color="#9CCAFF"/>
<stop offset="1" stop-color="#74B5FF"/>
</radialGradient>
<radialGradient id="paint1_radial_78_4993" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-5.74906 25.5127) scale(57.8938 22.9678)">
<stop stop-color="#3B9DB3"/>
<stop offset="0.516877" stop-color="#346FB4"/>
<stop offset="1" stop-color="#246ABB"/>
</radialGradient>
<clipPath id="clip0_78_4993">
<rect width="39" height="48" fill="white"/>
</clipPath>
</defs>
</svg>
