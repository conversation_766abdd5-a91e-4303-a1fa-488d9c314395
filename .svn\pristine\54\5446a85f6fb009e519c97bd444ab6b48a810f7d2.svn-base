﻿using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Routing;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class ActionListControllerShould
    {
        private ActionListController _controller;

        public ActionListControllerShould()
        {
            Initialize();
        }

        internal void Initialize()
        {
            _controller = new ActionListController();
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public void List_ReturnsViewResult()
        {
            // Act
            var result = _controller.List();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
            var viewResult = result as ViewResult;
            Assert.Null(viewResult.ViewName);
            Assert.Null(viewResult.Model);
        }

        [Fact]
        public void List_PublishesEvent_AndReturnsView()
        {
            // Act
            var result = _controller.List();

            // Assert
            Assert.IsType<ViewResult>(result);
            Assert.NotNull(result);
        }

        [Fact]
        public void List_HasAntiXssAttribute()
        {
            // Arrange
            var methodInfo = typeof(ActionListController).GetMethod("List");

            // Act
            var antiXssAttribute = methodInfo.GetCustomAttributes(typeof(AntiXssAttribute), false);

            // Assert
            Assert.NotNull(antiXssAttribute);
            Assert.Single(antiXssAttribute);
            Assert.IsType<AntiXssAttribute>(antiXssAttribute[0]);
        }

        [Fact]
        public void AntiXssAttribute_ThrowsException_WhenScriptDetected()
        {
            // Arrange
            var antiXssAttribute = new AntiXssAttribute();
            var actionContext = new ActionContext(
                new DefaultHttpContext(),
                new RouteData(),
                new ActionDescriptor()
            );

            var actionArguments = new Dictionary<string, object>
            {
                { "maliciousInput", "<script>alert('xss')</script>" }
            };

            var actionExecutingContext = new ActionExecutingContext(
                actionContext,
                new List<IFilterMetadata>(),
                actionArguments,
                _controller
            );

            // Act & Assert
            var exception = Assert.Throws<AntiXssException>(() =>
                antiXssAttribute.OnActionExecuting(actionExecutingContext));

            Assert.Equal("A Potentially dangerous request was detected.", exception.Message);
            Assert.Equal(5005, exception.ErrorCode);
        }

        [Fact]
        public void AntiXssAttribute_DoesNotThrowException_WhenNoScriptDetected()
        {
            // Arrange
            var antiXssAttribute = new AntiXssAttribute();
            var actionContext = new ActionContext(
                new DefaultHttpContext(),
                new RouteData(),
                new ActionDescriptor()
            );

            var actionArguments = new Dictionary<string, object>
            {
                { "safeInput", "This is safe text" }
            };

            var actionExecutingContext = new ActionExecutingContext(
                actionContext,
                new List<IFilterMetadata>(),
                actionArguments,
                _controller
            );

            // Act & Assert
            var exception = Record.Exception(() =>
                antiXssAttribute.OnActionExecuting(actionExecutingContext));

            Assert.Null(exception);
        }

        [Fact]
        public void AntiXssAttribute_ThrowsException_WhenObjectPropertyContainsScript()
        {
            // Arrange
            var antiXssAttribute = new AntiXssAttribute();
            var actionContext = new ActionContext(
                new DefaultHttpContext(),
                new RouteData(),
                new ActionDescriptor()
            );

            var maliciousObject = new TestModel
            {
                Name = "<script>alert('xss')</script>",
                Description = "Safe description"
            };

            var actionArguments = new Dictionary<string, object>
            {
                { "model", maliciousObject }
            };

            var actionExecutingContext = new ActionExecutingContext(
                actionContext,
                new List<IFilterMetadata>(),
                actionArguments,
                _controller
            );

            // Act & Assert
            var exception = Assert.Throws<AntiXssException>(() =>
                antiXssAttribute.OnActionExecuting(actionExecutingContext));

            Assert.Equal("A Potentially dangerous request was detected.", exception.Message);
            Assert.Equal(5005, exception.ErrorCode);
        }

        [Fact]
        public void AntiXssAttribute_DoesNotThrowException_WhenObjectPropertiesAreSafe()
        {
            // Arrange
            var antiXssAttribute = new AntiXssAttribute();
            var actionContext = new ActionContext(
                new DefaultHttpContext(),
                new RouteData(),
                new ActionDescriptor()
            );

            var safeObject = new TestModel
            {
                Name = "Safe name",
                Description = "Safe description"
            };

            var actionArguments = new Dictionary<string, object>
            {
                { "model", safeObject }
            };

            var actionExecutingContext = new ActionExecutingContext(
                actionContext,
                new List<IFilterMetadata>(),
                actionArguments,
                _controller
            );

            // Act & Assert
            var exception = Record.Exception(() =>
                antiXssAttribute.OnActionExecuting(actionExecutingContext));

            Assert.Null(exception);
        }

        [Fact]
        public void AntiXssAttribute_DoesNotThrowException_WhenNoArguments()
        {
            // Arrange
            var antiXssAttribute = new AntiXssAttribute();
            var actionContext = new ActionContext(
                new DefaultHttpContext(),
                new RouteData(),
                new ActionDescriptor()
            );

            var actionArguments = new Dictionary<string, object>();

            var actionExecutingContext = new ActionExecutingContext(
                actionContext,
                new List<IFilterMetadata>(),
                actionArguments,
                _controller
            );

            // Act & Assert
            var exception = Record.Exception(() =>
                antiXssAttribute.OnActionExecuting(actionExecutingContext));

            Assert.Null(exception);
        }

        [Fact]
        public void AntiXssAttribute_DoesNotThrowException_WhenArgumentIsNull()
        {
            // Arrange
            var antiXssAttribute = new AntiXssAttribute();
            var actionContext = new ActionContext(
                new DefaultHttpContext(),
                new RouteData(),
                new ActionDescriptor()
            );

            var actionArguments = new Dictionary<string, object>
            {
                { "nullInput", null }
            };

            var actionExecutingContext = new ActionExecutingContext(
                actionContext,
                new List<IFilterMetadata>(),
                actionArguments,
                _controller
            );

            // Act & Assert
            var exception = Record.Exception(() =>
                antiXssAttribute.OnActionExecuting(actionExecutingContext));

            Assert.Null(exception);
        }

        [Fact]
        public void AntiXssAttribute_OnActionExecuted_DoesNothing()
        {
            // Arrange
            var antiXssAttribute = new AntiXssAttribute();
            var actionContext = new ActionContext(
                new DefaultHttpContext(),
                new RouteData(),
                new ActionDescriptor()
            );

            var actionExecutedContext = new ActionExecutedContext(
                actionContext,
                new List<IFilterMetadata>(),
                _controller
            );

            // Act & Assert
            var exception = Record.Exception(() =>
                antiXssAttribute.OnActionExecuted(actionExecutedContext));

            Assert.Null(exception);
        }

        [Fact]
        public void Controller_HasAreaAttribute()
        {
            // Arrange
            var controllerType = typeof(ActionListController);

            // Act
            var areaAttribute = controllerType.GetCustomAttributes(typeof(AreaAttribute), false);

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Single(areaAttribute);
            var area = areaAttribute[0] as AreaAttribute;
            Assert.Equal("Admin", area.RouteValue);
        }

        // Test model class for AntiXss testing
        public class TestModel
        {
            public string Name { get; set; }
            public string Description { get; set; }
            public int Id { get; set; }
        }
    }
}

