﻿using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Shared.Core.Helper;

public static class GuardClauseExtensions
{
    public static bool True(this IGuardClause guardClause, bool input, string parameterName, Exception exception)
    {
        if (input)
        {
            if (exception is null) throw new ArgumentNullException(parameterName);
            throw exception;
        }

        return false;
    }

    public static bool False(this IGuardClause guardClause, bool input, string parameterName, Exception exception)
    {
        if (!input)
        {
            if (exception is null) throw new ArgumentNullException(parameterName);
            throw exception;
        }

        return true;
    }


    public static T Default<T>(this IGuardClause guardClause, [AllowNull] [NotNull] T input, string parameterName,
        string message = null)
    {
        var t = default(T);
        if (EqualityComparer<T>.Default.Equals(input, t) || input == null)
            throw new InvalidArgumentException(message ?? $"Parameter '{parameterName}' is default value for type ");
        return input;
    }

    public static string InvalidFormat(this IGuardClause guardClause, string input, string parameterName,
        string regexPattern, string message = null)
    {
        if (input != Regex.Match(input, regexPattern).Value)
            throw new InvalidArgumentException(message ?? $"Input '{parameterName}' was not in required format");
        return input;
    }


    public static T InvalidInput<T>(this IGuardClause guardClause, T input, string parameterName,
        Func<T, bool> predicate, string message = null)
    {
        if (!predicate(input))
            throw new InvalidArgumentException(message ?? $"Input '{parameterName}' did not satisfy the options");
        return input;
    }

    public static int Negative(this IGuardClause guardClause, int input, string parameterName, string message = null)
    {
        return Negative<int>(guardClause, input, parameterName, message);
    }


    public static long Negative(this IGuardClause guardClause, long input, string parameterName, string message = null)
    {
        return Negative<long>(guardClause, input, parameterName, message);
    }


    public static decimal Negative(this IGuardClause guardClause, decimal input, string parameterName,
        string message = null)
    {
        return Negative<decimal>(guardClause, input, parameterName, message);
    }


    public static float Negative(this IGuardClause guardClause, float input, string parameterName,
        string message = null)
    {
        return Negative<float>(guardClause, input, parameterName, message);
    }


    public static double Negative(this IGuardClause guardClause, double input, string parameterName,
        string message = null)
    {
        return Negative<double>(guardClause, input, parameterName, message);
    }


    public static TimeSpan Negative(this IGuardClause guardClause, TimeSpan input, string parameterName,
        string message = null)
    {
        return Negative<TimeSpan>(guardClause, input, parameterName, message);
    }


    private static T Negative<T>(IGuardClause guardClause, T input, string parameterName, string message = null)
        where T : struct, IComparable
    {
        if (input.CompareTo(default(T)) < 0)
            throw new InvalidArgumentException(message ?? $"Required input '{parameterName}' cannot be negative.");
        return input;
    }


    public static int NegativeOrZero(this IGuardClause guardClause, int input, string parameterName,
        string message = null)
    {
        return NegativeOrZero<int>(guardClause, input, parameterName, message);
    }


    public static long NegativeOrZero(this IGuardClause guardClause, long input, string parameterName,
        string message = null)
    {
        return NegativeOrZero<long>(guardClause, input, parameterName, message);
    }


    public static decimal NegativeOrZero(this IGuardClause guardClause, decimal input, string parameterName,
        string message = null)
    {
        return NegativeOrZero<decimal>(guardClause, input, parameterName, message);
    }


    public static float NegativeOrZero(this IGuardClause guardClause, float input, string parameterName,
        string message = null)
    {
        return NegativeOrZero<float>(guardClause, input, parameterName, message);
    }


    public static double NegativeOrZero(this IGuardClause guardClause, double input, string parameterName,
        string message = null)
    {
        return NegativeOrZero<double>(guardClause, input, parameterName, message);
    }


    public static TimeSpan NegativeOrZero(this IGuardClause guardClause, TimeSpan input, string parameterName,
        string message = null)
    {
        return NegativeOrZero<TimeSpan>(guardClause, input, parameterName, message);
    }


    private static T NegativeOrZero<T>(IGuardClause guardClause, T input, string parameterName, string message = null)
        where T : struct, IComparable
    {
        if (input.CompareTo(default(T)) <= 0)
            return NegativeOrZero(guardClause, input, parameterName,
                new InvalidArgumentException(message ??
                                             $"Required input '{parameterName}' cannot be zero or negative."));
        return input;
    }


    public static T NegativeOrZero<T>(IGuardClause guardClause, T input, string parameterName, Exception exception)
        where T : struct, IComparable
    {
        if (input.CompareTo(default(T)) <= 0)
        {
            if (exception is null)
                throw new InvalidArgumentException($"Required input '{parameterName}' cannot be zero or negative.");
            throw exception;
        }

        return input;
    }

    public static T NotFound<T>(this IGuardClause guardClause, [NotNull] string key, [NotNull] T input,
        string parameterName)
    {
        guardClause.NullOrEmpty(key, "key");
        if (input == null) throw new NotFoundException(key, parameterName);
        return input;
    }


    public static T NotFound<TKey, T>(this IGuardClause guardClause, [NotNull] TKey key, [NotNull] T input,
        string parameterName)
        where TKey : struct
    {
        guardClause.Null(key, "key", string.Empty);
        if (input == null) throw new NotFoundException(key.ToString(), parameterName);
        return input;
    }

    public static void Null(this IGuardClause guardClause, object input, string parameterName)
    {
        Null(guardClause, input, parameterName, new ArgumentNullException(parameterName));
    }

    public static T Null<T>(this IGuardClause guardClause, [NotNull] T input, string parameterName,
        string message = null)
    {
        if (!string.IsNullOrEmpty(message))
            return Null(guardClause, input, parameterName, new ArgumentNullException(message, (Exception)null));

        return Null(guardClause, input, parameterName, new ArgumentNullException(parameterName));
    }


    public static T Null<T>(this IGuardClause guardClause, [NotNull] T input, string parameterName, Exception exception)
    {
        if (input is null)
        {
            if (exception is null) throw new ArgumentNullException(parameterName);
            throw exception;
        }

        return input;
    }

    public static T NullOrDeactive<T>(this IGuardClause guardClause, [NotNull] T input, string parameterName,
        Exception exception)
    {
        if (input is null)
        {
            if (exception is null) throw new ArgumentNullException(parameterName);
            throw exception;
        }

        if (input is AuditableEntity { IsActive: false })
        {
            if (exception is null) throw new ArgumentNullException(parameterName);
            throw exception;
        }

        return input;
    }


    public static string NullOrEmpty(this IGuardClause guardClause, [NotNull] string input, string parameterName,
        string message = null)
    {
        Guard.Against.Null(input, parameterName, string.Empty);
        if (input == string.Empty)
            throw new InvalidArgumentException(message ?? $"Required input '{parameterName}' was empty.");
        return input;
    }

    public static string InvalidGuidOrEmpty(this IGuardClause guardClause, [NotNull] string input, string parameterName, string message = null)
    {
        if(input == null)
        {
            throw new ArgumentNullException(parameterName);
        }

        if (string.IsNullOrWhiteSpace(input))
        {
            throw new InvalidArgumentException($"Input '{parameterName ?? "guidString"}' is not valid format.");
        }

        if (!Guid.TryParse(input, out var guid))
        {
            throw new InvalidArgumentException($"Input '{parameterName ?? "guidString"}' is not valid format.");
        }

        if (guid == Guid.Empty)
        {
            throw new InvalidArgumentException($"Input '{parameterName ?? "guidString"}' is not valid format.");
        }

        return input;
    }

    public static string NullOrWhiteSpace(this IGuardClause guardClause, string input, string parameterName,
        string message = null)
    {
        Guard.Against.NullOrEmpty(input, parameterName);
        if (string.IsNullOrWhiteSpace(input))
            throw new InvalidArgumentException(message ?? $"Required input '{parameterName}' was empty.");
        return input;
    }


    public static int OutOfRange(this IGuardClause guardClause, int input, string parameterName, int rangeFrom,
        int rangeTo, string message = null)
    {
        return guardClause.OutOfRange<int>(input, parameterName, rangeFrom, rangeTo, message);
    }


    public static DateTime OutOfRange(this IGuardClause guardClause, DateTime input, string parameterName,
        DateTime rangeFrom, DateTime rangeTo, string message = null)
    {
        return guardClause.OutOfRange<DateTime>(input, parameterName, rangeFrom, rangeTo, message);
    }


    public static decimal OutOfRange(this IGuardClause guardClause, decimal input, string parameterName,
        decimal rangeFrom, decimal rangeTo, string message = null)
    {
        return guardClause.OutOfRange<decimal>(input, parameterName, rangeFrom, rangeTo, message);
    }


    public static short OutOfRange(this IGuardClause guardClause, short input, string parameterName, short rangeFrom,
        short rangeTo, string message = null)
    {
        return guardClause.OutOfRange<short>(input, parameterName, rangeFrom, rangeTo, message);
    }


    public static double OutOfRange(this IGuardClause guardClause, double input, string parameterName, double rangeFrom,
        double rangeTo, string message = null)
    {
        return guardClause.OutOfRange<double>(input, parameterName, rangeFrom, rangeTo, message);
    }


    public static float OutOfRange(this IGuardClause guardClause, float input, string parameterName, float rangeFrom,
        float rangeTo, string message = null)
    {
        return guardClause.OutOfRange<float>(input, parameterName, rangeFrom, rangeTo, message);
    }


    public static TimeSpan OutOfRange(this IGuardClause guardClause, TimeSpan input, string parameterName,
        TimeSpan rangeFrom, TimeSpan rangeTo, string message = null)
    {
        return guardClause.OutOfRange<TimeSpan>(input, parameterName, rangeFrom, rangeTo, message);
    }


    private static T OutOfRange<T>(this IGuardClause guardClause, T input, string parameterName, T rangeFrom, T rangeTo,
        string message = null)
    {
        var @default = Comparer<T>.Default;
        if (@default.Compare(rangeFrom, rangeTo) > 0)
            throw new InvalidArgumentException(message ?? "rangeFrom should be less or equal than rangeTo");
        if (@default.Compare(input, rangeFrom) < 0 || @default.Compare(input, rangeTo) > 0)
        {
            if (!string.IsNullOrEmpty(message)) throw new ArgumentOutOfRangeException(message, (Exception)null);
            throw new ArgumentOutOfRangeException(parameterName, $"Input '{parameterName}' was out of range");
        }

        return input;
    }


    public static int OutOfRange<T>(this IGuardClause guardClause, int input, string parameterName,
        string message = null)
        where T : struct, Enum
    {
        if (!Enum.IsDefined(typeof(T), input))
        {
            if (!string.IsNullOrEmpty(message)) throw new InvalidEnumArgumentException(message);
            throw new InvalidEnumArgumentException(parameterName, Convert.ToInt32(input), typeof(T));
        }

        return input;
    }


    public static T OutOfRange<T>(IGuardClause guardClause, T input, string parameterName, string message = null)
        where T : struct, Enum
    {
        if (!Enum.IsDefined(typeof(T), input))
        {
            if (!string.IsNullOrEmpty(message)) throw new InvalidEnumArgumentException(message);
            throw new InvalidEnumArgumentException(parameterName, Convert.ToInt32(input), typeof(T));
        }

        return input;
    }


    private static IEnumerable<T> OutOfRange<T>(this IGuardClause guardClause, IEnumerable<T> input,
        string parameterName, T rangeFrom, T rangeTo, string message = null)
        where T : IComparable
    {
        var @default = Comparer<T>.Default;
        if (@default.Compare(rangeFrom, rangeTo) > 0)
            throw new InvalidArgumentException(message ?? "rangeFrom should be less or equal than rangeTo");
        var enumerable = input as T[] ?? input.ToArray();

        if (enumerable.Any(x =>
            {
                if (@default.Compare(x, rangeFrom) < 0) return true;
                return @default.Compare(x, rangeTo) > 0;
            }))
        {
            if (!string.IsNullOrEmpty(message)) throw new ArgumentOutOfRangeException(message, (Exception)null);
            throw new ArgumentOutOfRangeException(parameterName,
                message ?? $"Input '{parameterName}' had out of range item(s)");
        }

        return enumerable;
    }


    public static IEnumerable<int> OutOfRange(this IGuardClause guardClause, IEnumerable<int> input,
        string parameterName, int rangeFrom, int rangeTo, string message = null)
    {
        return guardClause.OutOfRange<int>(input, parameterName, rangeFrom, rangeTo, message);
    }


    public static IEnumerable<long> OutOfRange(this IGuardClause guardClause, IEnumerable<long> input,
        string parameterName, long rangeFrom, long rangeTo, string message = null)
    {
        return guardClause.OutOfRange<long>(input, parameterName, rangeFrom, rangeTo, message);
    }


    public static IEnumerable<decimal> OutOfRange(this IGuardClause guardClause, IEnumerable<decimal> input,
        string parameterName, decimal rangeFrom, decimal rangeTo, string message = null)
    {
        return guardClause.OutOfRange<decimal>(input, parameterName, rangeFrom, rangeTo, message);
    }


    public static IEnumerable<float> OutOfRange(this IGuardClause guardClause, IEnumerable<float> input,
        string parameterName, float rangeFrom, float rangeTo, string message = null)
    {
        return guardClause.OutOfRange<float>(input, parameterName, rangeFrom, rangeTo, message);
    }


    public static IEnumerable<double> OutOfRange(this IGuardClause guardClause, IEnumerable<double> input,
        string parameterName, double rangeFrom, double rangeTo, string message = null)
    {
        return guardClause.OutOfRange<double>(input, parameterName, rangeFrom, rangeTo, message);
    }


    public static IEnumerable<short> OutOfRange(this IGuardClause guardClause, IEnumerable<short> input,
        string parameterName, short rangeFrom, short rangeTo, string message = null)
    {
        return guardClause.OutOfRange<short>(input, parameterName, rangeFrom, rangeTo, message);
    }


    public static IEnumerable<TimeSpan> OutOfRange(this IGuardClause guardClause, IEnumerable<TimeSpan> input,
        string parameterName, TimeSpan rangeFrom, TimeSpan rangeTo, string message = null)
    {
        return guardClause.OutOfRange<TimeSpan>(input, parameterName, rangeFrom, rangeTo, message);
    }


    //public static DateTime OutOfSQLDateRange(this IGuardClause guardClause, DateTime input, string parameterName, string message = null)
    //{
    //    return guardClause.OutOfRange<DateTime>(input, parameterName, new DateTime(552877920000000000L), new DateTime(3155378975999970000L), message);
    //}


    public static int Zero(this IGuardClause guardClause, int input, string parameterName, string message = null)
    {
        return Zero<int>(guardClause, input, parameterName, message);
    }


    public static long Zero(this IGuardClause guardClause, long input, string parameterName, string message = null)
    {
        return Zero<long>(guardClause, input, parameterName, message);
    }


    public static decimal Zero(this IGuardClause guardClause, decimal input, string parameterName,
        string message = null)
    {
        return Zero<decimal>(guardClause, input, parameterName, message);
    }


    public static float Zero(this IGuardClause guardClause, float input, string parameterName, string message = null)
    {
        return Zero<float>(guardClause, input, parameterName, message);
    }


    public static double Zero(this IGuardClause guardClause, double input, string parameterName, string message = null)
    {
        return Zero<double>(guardClause, input, parameterName, message);
    }


    public static TimeSpan Zero(this IGuardClause guardClause, TimeSpan input, string parameterName)
    {
        return Zero(guardClause, input, parameterName);
    }


    private static T Zero<T>(IGuardClause guardClause, T input, string parameterName, string message = null)
        where T : struct
    {
        if (EqualityComparer<T>.Default.Equals(input, default))
            throw new InvalidArgumentException(message ?? $"Required input '{parameterName}' cannot be zero.");
        return input;
    }
}