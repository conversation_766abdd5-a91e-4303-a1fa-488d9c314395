using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.SendEmail;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowDrCalenderModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Orchestration;

public class WorkflowDrCalenderService : BaseClient, IWorkflowDrCalenderService
{
    public WorkflowDrCalenderService(IConfiguration config, IAppCache cache, ILogger<WorkflowDrCalenderService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<WorkflowDrCalenderListVm>> GetWorkflowDrCalenderList()
    {
        var request = new RestRequest("api/v6/workflowdrcalenders");

        return await GetFromCache<List<WorkflowDrCalenderListVm>>(request, "GetWorkflowDrCalenderList");
    }

    public async Task<BaseResponse> CreateAsync(CreateWorkflowDrCalenderCommand createWorkflowDrCalenderCommand)
    {
        var request = new RestRequest("api/v6/workflowdrcalenders", Method.Post);

        request.AddJsonBody(createWorkflowDrCalenderCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateWorkflowDrCalenderCommand updateWorkflowDrCalenderCommand)
    {
        var request = new RestRequest("api/v6/workflowdrcalenders", Method.Put);

        request.AddJsonBody(updateWorkflowDrCalenderCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/workflowdrcalenders/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<WorkflowDrCalenderDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/workflowdrcalenders/{id}");

        return await Get<WorkflowDrCalenderDetailVm>(request);
    }

    public async Task<BaseResponse> SendEmail(WorkflowDrCalenderSendEmailCommand workflowDrCalenderSendEmailCommand)
    {
        var request = new RestRequest("api/v6/workflowdrcalenders/sendeemail");

        request.AddJsonBody(workflowDrCalenderSendEmailCommand);

        return await Post<BaseResponse>(request);

    }
    #region NameExist
    public async Task<bool> IsWorkflowDrCalenderNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/workflowdrcalenders/name-exist?workflowdrcalenderName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<WorkflowDrCalenderListVm>> GetPaginatedWorkflowDrCalenders(GetWorkflowDrCalenderPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/workflowdrcalenders/paginated-list");

      return await Get<PaginatedResult<WorkflowDrCalenderListVm>>(request);
  }
   #endregion
}
