﻿namespace ContinuityPatrol.Application.Features.ReportSchedule.Commands.Update;

public class UpdateReportScheduleCommand : IRequest<UpdateReportScheduleResponse>
{
    public string Id { get; set; }
    public string ReportName { get; set; }
    public string ReportType { get; set; }
    public string CompanyId { get; set; }
    public string NodeId { get; set; }
    public string NodeName { get; set; }
    public string Type { get; set; }
    public string FromDate { get; set; }
    public string ToDate { get; set; }
    public string ScheduleTime { get; set; }
    public string ReportProperties { get; set; }
    public string UserProperties { get; set; }

    public override string ToString()
    {
        return $"ReportName: {ReportName}; Id:{Id};";
    }
}