﻿using ContinuityPatrol.Web.Areas.Report.Controllers;
using Newtonsoft.Json;
using System.Drawing;
using ContinuityPatrol.Application.Features.Report.Queries.GetInfraObjectConfigurationReport;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    public partial class InfraObjectConfigurationReport : DevExpress.XtraReports.UI.XtraReport
    {
        public InfraReport infraobject;
        private readonly ILogger<PreBuildReportController> _logger;
        #region OracleRac Node Properties
        //public class Properties
        //{
        //    public string prNodeId { get; set; }
        //    public string prNodeName { get; set; }
        //    public string drNodeId { get; set; }
        //    public string drNodeName { get; set; }
        //}

        //public class InfraObjectList
        //{
        //    public string Id { get; set; }
        //    public string Name { get; set; }
        //    public List<string> PRServerName { get; set; } = new();
        //    public List<string> PRIPAddress { get; set; } = new();
        //    public List<string> DRServerName { get; set; } = new();
        //    public List<string> DRIPAddress { get; set; } = new();
        //    public string State { get; set; }
        //    public string PRDatabaseId { get; set; }
        //    public string PRDatabaseName { get; set; }
        //    public string DRDatabaseId { get; set; }
        //    public string DRDatabaseName { get; set; }
        //    public string TypeName { get; set; }
        //    public string SubType { get; set; }
        //    public string PRReplicationName { get; set; }
        //    public string DRReplicationName { get; set; }
        //    public string ReplicationTypeName { get; set; }
        //    public string ReplicationCategoryType { get; set; }
        //    public string BusinessServiceName { get; set; }
        //    public string NodeProperties { get; set; }
        //    public List<string> PrNodeId { get; set; }
        //    public List<string> PrNodeName { get; set; }
        //    public List<string> DrNodeId { get; set; }
        //    public List<string> DrNodeName { get; set; }

        //}
        //list for get properties values separately
        //public List<string> PrNodeId = new List<string>();
        //public List<string> PrNodeName = new List<string>();
        //public List<string> DrNodeId = new List<string>();
        //public List<string> DrNodeName = new List<string>();
        #endregion
        public InfraObjectConfigurationReport(string data)
        {
            try
            {
                infraobject= new InfraReport();
                _logger = PreBuildReportController._logger;

                infraobject = JsonConvert.DeserializeObject<InfraReport>(data);
                
                InitializeComponent();
                ClientCompanyLogo();
                if (infraobject.InfraObjectConfigurationReportVm.GetType().GetProperties().Length == 0)
                {
                    throw new Exception("No Data Found");
                }
               // this.DisplayName = infraobject.InfraObjectConfigurationReportVm.Name.ToString() + "_InfraObjectConfigurationReport_" + DateTime.Now.ToString("yyyy-MM-dd");
                xrLabel28.Visible = false;
                xrLabel37.Visible = false;
                xrPictureBox19.Visible = false;
                xrShape9.Visible = false;
                xrLabel38.Visible = false;
                xrLabel39.Visible = false;
                xrPictureBox30.Visible = false;
                xrShape10.Visible = false;
                xrShape12.HeightF = 260;
                xrShape13.HeightF = 260;

                // data source assign
                this.DataSource = infraobject.InfraObjectConfigurationReportVm;


                #region Oracle rac node design
                //List<InfraObjectList> infralistDatasource = new List<InfraObjectList>();
                //var propeties = InfraList[0].NodeProperties?.ToString();
                //if (InfraList[0].SubType.IsNotNullOrEmpty() && InfraList[0].SubType.ToLower().Equals("oracle") && InfraList[0].ReplicationTypeName.IsNotNullOrEmpty() && InfraList[0].ReplicationTypeName.ToLower().Equals("native replication-oracle-rac"))
                //{
                //    xrLabel28.Visible = true;
                //    xrLabel37.Visible = true;
                //    xrPictureBox19.Visible = true;
                //    xrShape9.Visible = true;
                //    xrLabel38.Visible = true;
                //    xrLabel39.Visible = true;
                //    xrPictureBox30.Visible = true;
                //    xrShape10.Visible = true;
                //    xrShape12.HeightF = 342;
                //    xrShape13.HeightF = 342;

                //    //convert json to list               
                //    if (!string.IsNullOrEmpty(propeties) && !string.IsNullOrWhiteSpace(propeties))
                //    {
                //        List<Properties> propertiesList = JsonConvert.DeserializeObject<List<Properties>>(propeties);
                //        foreach (var nodevalues in propertiesList)
                //        {
                //            //add node values to list
                //            PrNodeId.Add(nodevalues.prNodeId);
                //            PrNodeName.Add(nodevalues.prNodeName);
                //            DrNodeId.Add(nodevalues.drNodeId);
                //            DrNodeName.Add(nodevalues.drNodeName);

                //        }
                //    }
                //    else
                //    {
                //        PrNodeId.Add("NA");
                //        PrNodeName.Add("NA");
                //        DrNodeId.Add("NA");
                //        DrNodeName.Add("NA");
                //    }
                //    infralistDatasource.Clear();
                //    foreach (var infra in InfraList)
                //    {
                //        InfraObjectList infraObj = new InfraObjectList();
                //        infraObj.Id = infra.Id;
                //        infraObj.Name = infra.Name;
                //        infraObj.PRServerName = infra.PRServerName;
                //        infraObj.PRIPAddress = infra.PRIPAddress;
                //        infraObj.DRServerName = infra.DRServerName;
                //        infraObj.DRIPAddress = infra.DRIPAddress;
                //        infraObj.State = infra.State;
                //        infraObj.PRDatabaseId = infra.PRDatabaseId;
                //        infraObj.PRDatabaseName = infra.PRDatabaseName;
                //        infraObj.DRDatabaseId = infra.DRDatabaseId;
                //        infraObj.DRDatabaseName = infra.DRDatabaseName;
                //        infraObj.TypeName = infra.TypeName;
                //        infraObj.SubType = infra.SubType;
                //        infraObj.PRReplicationName = infra.PRReplicationName;
                //        infraObj.DRReplicationName = infra.DRReplicationName;
                //        infraObj.ReplicationTypeName = infra.ReplicationTypeName;
                //        infraObj.ReplicationCategoryType = infra.ReplicationCategoryType;
                //        infraObj.BusinessServiceName = infra.BusinessServiceName;
                //        infraObj.PrNodeId = PrNodeId;
                //        infraObj.PrNodeName = PrNodeName;
                //        infraObj.DrNodeId = DrNodeId;
                //        infraObj.DrNodeName = DrNodeName;
                //        infralistDatasource.Add(infraObj);

                //    }
                //    // oracle rac type data source assign
                //    this.DataSource = infralistDatasource;
                //}
                //else
                //{
                //    xrLabel28.Visible = false;
                //    xrLabel37.Visible = false;
                //    xrPictureBox19.Visible = false;
                //    xrShape9.Visible = false;
                //    xrLabel38.Visible = false;
                //    xrLabel39.Visible = false;
                //    xrPictureBox30.Visible = false;
                //    xrShape10.Visible = false;
                //    xrShape12.HeightF = 260;
                //    xrShape13.HeightF = 260;

                //    // data source assign
                //    this.DataSource = InfraList;
                //}
                #endregion
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the InfraObject configuration Report. The error message : " + ex.Message); throw; }
        }
        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                xrLabel16.Text = "Report Generated By: " + infraobject.ReportGeneratedBy.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the InfraObject configuration Report's User name. The error message : " + ex.Message); throw; }
        }
        public void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel23.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the InfraObject configuration Report's CP Version. The error message : " + ex.Message); throw; }
        }

        [SupportedOSPlatform("windows")]
        private static Image LoadImageFromFile(string path)
        {
            return Image.FromFile(path);
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo.ToString();
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        if (OperatingSystem.IsWindows())
                        {
                            prClientLogo.Image = LoadImageFromFile(ms.ToString());
                        }
                        else
                        {
                            throw new PlatformNotSupportedException("Image loading only works on Windows in this context.");
                        }
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the customer logo in InfraObject Configuration Report" + ex.Message.ToString());
            }
        }
    }
}
