﻿using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.LoadBalancerModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.LoadBalancer.Queries;

public class GetLoadBalancerPaginatedListQueryHandlerTests : IClassFixture<LoadBalancerFixture>
{
    private readonly LoadBalancerFixture _loadBalancerFixture;

    private readonly GetLoadBalancerPaginatedListQueryHandler _handler;

    private readonly Mock<ILoadBalancerRepository> _mockLoadBalancerRepository;

    public GetLoadBalancerPaginatedListQueryHandlerTests(LoadBalancerFixture loadBalancerFixture)
    {
        _loadBalancerFixture = loadBalancerFixture;

        _loadBalancerFixture.LoadBalancers[0].Name = "Node_Configuration";
        _loadBalancerFixture.LoadBalancers[0].ConnectionType = "http";
        _loadBalancerFixture.LoadBalancers[0].HostName = "Cp6";
      //  _loadBalancerFixture.LoadBalancers[0].Status = "Running";


        _loadBalancerFixture.LoadBalancers[1].Name = "Testing_Nodes";
        _loadBalancerFixture.LoadBalancers[1].ConnectionType = "https";
        _loadBalancerFixture.LoadBalancers[1].HostName = "Cp4.5";
       // _loadBalancerFixture.LoadBalancers[1].Status = "Pending";

        _mockLoadBalancerRepository = LoadBalancerRepositoryMocks.GetPaginatedLoadBalancerRepository(_loadBalancerFixture.LoadBalancers);

        _handler = new GetLoadBalancerPaginatedListQueryHandler(_loadBalancerFixture.Mapper, _mockLoadBalancerRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetLoadBalancerPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<LoadBalancerListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedLoadBalancers_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetLoadBalancerPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Node_Configuration" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<LoadBalancerListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<LoadBalancerListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("Node_Configuration");

        result.Data[0].IPAddress.ShouldBe(_loadBalancerFixture.LoadBalancers[0].IPAddress);

        result.Data[0].HostName.ShouldBe("Cp6");

        result.Data[0].Port.ShouldBeGreaterThan(0);

        result.Data[0].ConnectionType.ShouldBe("http");

       // result.Data[0].Status.ShouldBe("Running");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetLoadBalancerPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<LoadBalancerListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_LoadBalancers_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetLoadBalancerPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=Node_Configuration;connectiontype=http;hostname=Cp6;Status=Running" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<LoadBalancerListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("Node_Configuration");

        result.Data[0].IPAddress.ShouldBe(_loadBalancerFixture.LoadBalancers[0].IPAddress);

        result.Data[0].HostName.ShouldBe("Cp6");

        result.Data[0].Port.ShouldBeGreaterThan(0);

        result.Data[0].ConnectionType.ShouldBe("http");

      //  result.Data[0].Status.ShouldBe("Running");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetLoadBalancerPaginatedListQuery(), CancellationToken.None);

        _mockLoadBalancerRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}

