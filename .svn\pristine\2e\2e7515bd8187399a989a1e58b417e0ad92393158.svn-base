﻿using ContinuityPatrol.Application.Features.RsyncJob.Commands.Create;
using ContinuityPatrol.Application.Features.RsyncJob.Commands.Delete;
using ContinuityPatrol.Application.Features.RsyncJob.Commands.Update;
using ContinuityPatrol.Application.Features.RsyncJob.Queries.GetDetail;
using ContinuityPatrol.Application.Features.RsyncJob.Queries.GetList;
using ContinuityPatrol.Application.Features.RsyncJob.Queries.GetPaginated;
using ContinuityPatrol.Domain.ViewModels.RsyncJobModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class RsyncJobController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<RsyncJobListVm>>> GetRsyncJobs()
    {
        Logger.LogDebug("Get All Rsync Jobs");

        return Ok(await Mediator.Send(new GetRsyncJobListQuery()));
    }

    [HttpGet("{id}", Name = "GetRsyncJob")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<RsyncJobDetailVm>> GetRsyncJobById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Rsync Job Id");

        Logger.LogDebug($"Get Rsync Job Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetRsyncJobDetailQuery { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<RsyncJobListVm>>> GetPaginatedRsyncJobs([FromQuery] GetRsyncJobPaginatedQuery query)
    {
        Logger.LogDebug("Get Searching Details in Rsync Job Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateRsyncJobResponse>> CreateRsyncJob([FromBody] CreateRsyncJobCommand createRsyncJobCommand)
    {
        Logger.LogDebug($"Create Rsync Job'{createRsyncJobCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateRsyncJob), await Mediator.Send(createRsyncJobCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateRsyncJobResponse>> UpdateRsyncJob([FromBody] UpdateRsyncJobCommand updateRsyncJobCommand)
    {
        Logger.LogDebug($"Update Rsync Job '{updateRsyncJobCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateRsyncJobCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteRsyncJobResponse>> DeleteRsyncJob(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Rsync Job Id");

        Logger.LogDebug($"Delete Rsync Job Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteRsyncJobCommand { Id = id }));
    }


    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllRsyncCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllPageRsyncJobCacheKey };

        ClearCache(cacheKeys);
    }
}
