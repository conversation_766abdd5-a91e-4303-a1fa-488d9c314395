using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class CyberJobManagementLogsFixture : IDisposable
{
    public const string CompanyId = "550e8400-e29b-41d4-a716-446655440000";
    public const string ParentCompanyId = "550e8400-e29b-41d4-a716-446655440001";
    public const string JobId = "JOB_001";
    public const string WorkflowId = "WORKFLOW_001";

    public List<CyberJobManagementLogs> CyberJobManagementLogsPaginationList { get; set; }
    public List<CyberJobManagementLogs> CyberJobManagementLogsList { get; set; }
    public CyberJobManagementLogs CyberJobManagementLogsDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public CyberJobManagementLogsFixture()
    {
        var fixture = new Fixture();

        CyberJobManagementLogsList = fixture.Create<List<CyberJobManagementLogs>>();
        CyberJobManagementLogsList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        CyberJobManagementLogsList.ForEach(x => x.IsActive = true);

        CyberJobManagementLogsPaginationList = fixture.CreateMany<CyberJobManagementLogs>(20).ToList();
        CyberJobManagementLogsPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        CyberJobManagementLogsPaginationList.ForEach(x => x.IsActive =true);

        CyberJobManagementLogsDto = fixture.Create<CyberJobManagementLogs>();

        CyberJobManagementLogsDto.IsActive =true;
        CyberJobManagementLogsDto.ReferenceId = Guid.NewGuid().ToString();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
