using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.MenuBuilderModel;
using ContinuityPatrol.Shared.Core.Wrapper;


namespace ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetPaginatedList;

public class GetMenuBuilderPaginatedListQueryHandler : IRequestHandler<GetMenuBuilderPaginatedListQuery, PaginatedResult<MenuBuilderListVm>>
{
    private readonly IMenuBuilderRepository _menuBuilderRepository;
    private readonly IMapper _mapper;

    public GetMenuBuilderPaginatedListQueryHandler(IMapper mapper, IMenuBuilderRepository menuBuilderRepository)
    {
        _mapper = mapper;
        _menuBuilderRepository = menuBuilderRepository;
    }

    public async Task<PaginatedResult<MenuBuilderListVm>> Handle(GetMenuBuilderPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _menuBuilderRepository.GetPaginatedQuery();

        var productFilterSpec = new MenuBuilderFilterSpecification(request.SearchString);

        var menuBuilderList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<MenuBuilderListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return menuBuilderList;
    }
}
