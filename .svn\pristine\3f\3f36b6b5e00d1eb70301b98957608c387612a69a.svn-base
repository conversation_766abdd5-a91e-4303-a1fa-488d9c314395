﻿using ContinuityPatrol.Application.Features.UserInfo.Commands.Create;
using ContinuityPatrol.Application.Features.UserInfo.Commands.Delete;
using ContinuityPatrol.Application.Features.UserInfo.Commands.Update;
using ContinuityPatrol.Application.Features.UserInfo.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class UserInfoService :BaseService, IUserInfoService
{
    public UserInfoService(IHttpContextAccessor accessor) : base(accessor)
    {
            
    }
    public async  Task<BaseResponse> CreateAsync(CreateUserInfoCommand createUserInfoCommand)
    {
        Logger.LogDebug($"Create userInfo '{createUserInfoCommand.LogoName}'");

        return await Mediator.Send(createUserInfoCommand);

    }

    public async  Task<BaseResponse> DeleteAsync(string userId)
    {
        Logger.LogDebug($"Delete userInfo by  UserId '{userId}'");

        return await Mediator.Send(new DeleteUserInfoCommand { UserId = userId });
    }

    public async  Task<UserInfoDetailVm> GetByReferenceId(string userId)
    {

        Logger.LogDebug($"Get userInfo by  UserId '{userId}'");

        return await Mediator.Send( new  GetUserInfoDetailQuery { UserId= userId });
    }

    public async Task<BaseResponse> UpdateAsync(UpdateUserInfoCommand updateUserInfoCommand)
    {

        Logger.LogDebug($"Update userInfo '{updateUserInfoCommand.LogoName}'");

        return await Mediator.Send(updateUserInfoCommand);
    }
}
