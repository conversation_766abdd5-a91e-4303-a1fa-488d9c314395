﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Repositories;

public class PostgresMonitorStatusRepository : BaseRepository<PostgresMonitorStatus>, IPostgresMonitorStatusRepository
{
    private readonly ApplicationDbContext _dbContext;

    public PostgresMonitorStatusRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<List<PostgresMonitorStatus>> GetDetailByType(string type)
    {
        return await _dbContext.PostgresMonitorStatuses.Where(x => x.IsActive && x.Type.Equals(type)).ToListAsync();
    }

    public async Task<PostgresMonitorStatus> GetPostgresMonitorStatusByInfraObjectIdAsync(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "InfraObjectId", "InfraObjectId cannot be invalid");

        return await _dbContext.PostgresMonitorStatuses
            .Where(x => x.InfraObjectId.Equals(infraObjectId))
            //.Select(x => new PostgresMonitorStatus { ReferenceId = x.ReferenceId })
            .FirstOrDefaultAsync();
    }
}