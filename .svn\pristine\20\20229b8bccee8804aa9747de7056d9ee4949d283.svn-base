﻿using ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Create;
using ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Delete;
using ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Update;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessFunctionListByBusinessServiceId;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessServiceDrReadyDetails;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessServiceIdByCount;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDrReadinessByBusinessServices;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DRReadyStatusModel;
using ContinuityPatrol.Domain.ViewModels.GetBusinessServiceIdByCount;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class DrReadyStatusController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<DRReadyStatusListVm>>> GetDrReadyStatus()
    {
        Logger.LogDebug("Get All DRReady Status");

        return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllDRReadyStatusCacheKey + LoggedInUserService.CompanyId, () => Mediator.Send(new GetDRReadyStatusListQuery()), CacheExpiry));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Dashboard.Create)]
    public async Task<ActionResult<CreateDRReadyStatusResponse>> CreateDrReadyStatus(
        [FromBody] CreateDRReadyStatusCommand createDrReadyStatusCommand)
    {
        Logger.LogDebug($"Create DRReady Status '{createDrReadyStatusCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateDrReadyStatus), await Mediator.Send(createDrReadyStatusCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Dashboard.Edit)]
    public async Task<ActionResult<UpdateDRReadyStatusResponse>> UpdateDrReadyStatus(
        [FromBody] UpdateDRReadyStatusCommand updateDrReadyStatusCommand)
    {
        Logger.LogDebug($"Update DRReady Status '{updateDrReadyStatusCommand.Id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateDrReadyStatusCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Dashboard.Delete)]
    public async Task<ActionResult<DeleteDRReadyStatusResponse>> DeleteDrReadyStatus(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DRReady Status Id");

        Logger.LogDebug($"Delete DRReady status Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteDRReadyStatusCommand { Id = id }));
    }

    [HttpGet("{id}", Name = "GetDRReadyStatus")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<DRReadyStatusDetailVm>> GetDrReadyStatusById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DRReadyStatus Id");

        Logger.LogDebug($"Get DRReadyStatus Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetDRReadyStatusDetailQuery { Id = id }));
    }

    [Route("paginated-list")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<PaginatedResult<DRReadyStatusListVm>>> GetPaginatedDrReadyStatus([FromQuery] GetDRReadyStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in DRReadyStatus Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpGet("businessservice-dr-readiness-details")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<BusinessServiceDrReadyDetailVm>>> GetBusinessServiceDrReady(string? businessServiceId)
    {
        Logger.LogDebug($"Get All DRReady Status by '{businessServiceId}'");

        return Ok(await Mediator.Send(new GetBusinessServiceDrReadyDetailQuery { BusinessServiceId = businessServiceId }));
    }

    [HttpGet("readiness-details")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<GetDrReadinessByBusinessServiceVm>> GetReadinessDetails(string? businessServiceId)
    {
        Logger.LogDebug($"Get All Readiness by '{businessServiceId}'");

        return Ok(await Mediator.Send(new GetDrReadinessByBusinessServiceQuery { BusinessServiceId = businessServiceId }));
    }

    [HttpGet("businessServiceId")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<DRReadyStatusByBusinessServiceIdVm>> GetDrReadyStatusByBusinessServiceId(
        string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "DRReadyStatus By businessServiceId");

        Logger.LogDebug($"Get DRReadyStatus Detail by BusinessServiceId '{businessServiceId}'");

        return Ok(await Mediator.Send(new GetDRReadyStatusByBusinessServiceIdQuery
        { BusinessServiceId = businessServiceId }));
    }

    [HttpGet]
    [Route("businessfunction-by-businessserviceid")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<GetBusinessFunctionListByBusinessServiceIdVm>>>
        GetBusinessFunctionListByBusinessServiceId(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "DRReadyStatus BusinessFunctionList By businessServiceId");

        Logger.LogDebug($"Get DRReadyStatus BusinessFunctionList by BusinessServiceId '{businessServiceId}'");

        return Ok(await Mediator.Send(new GetBusinessFunctionListByBusinessServiceIdQuery
        { BusinessServiceId = businessServiceId }));
    }

    [HttpGet("AllCount")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<BusinessServiceIdByCountVm>> GetBusinessServiceIdByCount(string businessServiceId)
    {
        Logger.LogDebug("Get All BusinessServiceIdByCount");

        return Ok(await Mediator.Send(new GetBusinessServiceIdByCountQuery { BusinessServiceId = businessServiceId }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllDRReadyStatusCacheKey + LoggedInUserService.CompanyId };
        ClearCache(cacheKeys);
    }
}
