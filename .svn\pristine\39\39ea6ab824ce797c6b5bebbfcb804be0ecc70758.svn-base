﻿namespace ContinuityPatrol.Application.Features.ApprovalMatrixTemplate.Queries.GetTemplateNameUniqueQuery;

public class GetTemplateNameUniqueQueryHandler : IRequestHandler<GetTemplateNameUniqueQuery, bool>
{
    private readonly IApprovalMatrixTemplateRepository _approvalMatrixTemplateRepository;

    public GetTemplateNameUniqueQueryHandler(IApprovalMatrixTemplateRepository approvalMatrixTemplateRepository)
    {
        _approvalMatrixTemplateRepository = approvalMatrixTemplateRepository;
    }

    public async Task<bool> Handle(GetTemplateNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _approvalMatrixTemplateRepository.IsApprovalMatrixNameUnique(request.TemplateName);
    }
}