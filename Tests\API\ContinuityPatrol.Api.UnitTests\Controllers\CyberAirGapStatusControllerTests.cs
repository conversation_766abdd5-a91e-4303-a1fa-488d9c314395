using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapStatusModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class CyberAirGapStatusControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly CyberAirGapStatussController _controller;
    private readonly CyberAirGapStatusFixture _cyberAirGapStatusFixture;

    public CyberAirGapStatusControllerTests()
    {
        _cyberAirGapStatusFixture = new CyberAirGapStatusFixture();

        var testBuilder = new ControllerTestBuilder<CyberAirGapStatussController>();
        _controller = testBuilder.CreateController(
            _ => new CyberAirGapStatussController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetCyberAirGapStatuss_ReturnsExpectedList()
    {
        // Arrange
        var expectedCyberAirGapStatuss = new List<CyberAirGapStatusListVm>
        {
            _cyberAirGapStatusFixture.CyberAirGapStatusListVm,
            _cyberAirGapStatusFixture.CyberAirGapStatusListVm,
            _cyberAirGapStatusFixture.CyberAirGapStatusListVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberAirGapStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCyberAirGapStatuss);

        // Act
        var result = await _controller.GetCyberAirGapStatuss();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberAirGapStatuss = Assert.IsAssignableFrom<List<CyberAirGapStatusListVm>>(okResult.Value);
        Assert.Equal(3, cyberAirGapStatuss.Count);
    }

    [Fact]
    public async Task GetCyberAirGapStatusById_ReturnsExpectedDetail()
    {
        // Arrange
        var cyberAirGapStatusId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapStatusDetailQuery>(q => q.Id == cyberAirGapStatusId), default))
            .ReturnsAsync(_cyberAirGapStatusFixture.CyberAirGapStatusDetailVm);

        // Act
        var result = await _controller.GetCyberAirGapStatusById(cyberAirGapStatusId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberAirGapStatus = Assert.IsType<CyberAirGapStatusDetailVm>(okResult.Value);
        Assert.Equal(_cyberAirGapStatusFixture.CyberAirGapStatusDetailVm.AirGapName, cyberAirGapStatus.AirGapName);
    }

    [Fact]
    public async Task GetPaginatedCyberAirGapStatuss_ReturnsExpectedResults()
    {
        // Arrange
        var query = new GetCyberAirGapStatusPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = new List<CyberAirGapStatusListVm>
        {
            _cyberAirGapStatusFixture.CyberAirGapStatusListVm,
            _cyberAirGapStatusFixture.CyberAirGapStatusListVm
        };
        var expectedResults = PaginatedResult<CyberAirGapStatusListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapStatusPaginatedListQuery>(q => 
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberAirGapStatuss(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberAirGapStatusListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task CreateCyberAirGapStatus_ReturnsCreatedAtAction()
    {
        // Arrange
        var command = _cyberAirGapStatusFixture.CreateCyberAirGapStatusCommand;
        var expectedMessage = "CyberAirGapStatus has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberAirGapStatusResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberAirGapStatus(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberAirGapStatusResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberAirGapStatus_ReturnsOk()
    {
        // Arrange
        var command = _cyberAirGapStatusFixture.UpdateCyberAirGapStatusCommand;
        var expectedMessage = "CyberAirGapStatus has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberAirGapStatusResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberAirGapStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberAirGapStatusResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteCyberAirGapStatus_ReturnsOk()
    {
        // Arrange
        var cyberAirGapStatusId = Guid.NewGuid().ToString();
        var expectedMessage = "CyberAirGapStatus has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCyberAirGapStatusCommand>(c => c.Id == cyberAirGapStatusId), default))
            .ReturnsAsync(new DeleteCyberAirGapStatusResponse
            {
                Message = expectedMessage,
                IsActive = false
            });

        // Act
        var result = await _controller.DeleteCyberAirGapStatus(cyberAirGapStatusId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteCyberAirGapStatusResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public async Task UpdateStatus_ReturnsOk()
    {
        // Arrange
        var command = _cyberAirGapStatusFixture.UpdateAirGapStatusCommand;
        var expectedMessage = "AirGap status has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateAirGapStatusResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateAirGapStatusResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task IsCyberAirGapStatusNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var airGapStatusName = "Existing Air Gap Status";
        var airGapStatusId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapStatusNameUniqueQuery>(q => 
                q.Name == airGapStatusName && q.Id == airGapStatusId), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsCyberAirGapStatusNameExist(airGapStatusName, airGapStatusId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.True(exists);
    }

    [Fact]
    public async Task IsCyberAirGapStatusNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var airGapStatusName = "Unique Air Gap Status Name";
        var airGapStatusId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapStatusNameUniqueQuery>(q => 
                q.Name == airGapStatusName && q.Id == airGapStatusId), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsCyberAirGapStatusNameExist(airGapStatusName, airGapStatusId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.False(exists);
    }

    [Fact]
    public async Task CreateCyberAirGapStatus_ValidatesAirGapId()
    {
        // Arrange
        var command = new CreateCyberAirGapStatusCommand
        {
            AirGapId = "", // Empty AirGapId should cause validation error
            AirGapName = "Test Air Gap Status",
            Description = "Test status monitor",
            SourceSiteId = Guid.NewGuid().ToString(),
            TargetSiteId = Guid.NewGuid().ToString(),
            Port = 8443,
            Status = "Healthy"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("AirGapId is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateCyberAirGapStatus(command));
    }

    [Fact]
    public async Task UpdateCyberAirGapStatus_ValidatesStatusExists()
    {
        // Arrange
        var command = new UpdateCyberAirGapStatusCommand
        {
            Id = Guid.NewGuid().ToString(),
            AirGapId = Guid.NewGuid().ToString(),
            AirGapName = "Non-existent Air Gap Status",
            Description = "Test status monitor",
            SourceSiteId = Guid.NewGuid().ToString(),
            TargetSiteId = Guid.NewGuid().ToString(),
            Port = 8443,
            Status = "Healthy"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("CyberAirGapStatus not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateCyberAirGapStatus(command));
    }

    [Fact]
    public async Task CreateCyberAirGapStatus_HandlesComplexStatusMonitoring()
    {
        // Arrange
        var command = new CreateCyberAirGapStatusCommand
        {
            AirGapId = Guid.NewGuid().ToString(),
            AirGapName = "Enterprise Critical Air Gap Status Monitor",
            Description = "Comprehensive real-time status monitoring for enterprise air gap replication with advanced health metrics and alerting",
            SourceSiteId = Guid.NewGuid().ToString(),
            SourceSiteName = "Primary Production Data Center",
            TargetSiteId = Guid.NewGuid().ToString(),
            TargetSiteName = "Disaster Recovery Site",
            Port = 8443,
            Source = "{\"statusMonitoring\":{\"enabled\":true,\"interval\":\"15s\",\"healthChecks\":[\"connectivity\",\"throughput\",\"latency\",\"errorRate\"],\"metrics\":{\"cpu\":\"15%\",\"memory\":\"45%\",\"disk\":\"60%\",\"network\":\"125Mbps\"},\"alerts\":{\"thresholds\":{\"cpu\":\"80%\",\"memory\":\"85%\",\"disk\":\"90%\",\"latency\":\"100ms\"},\"notifications\":[\"email\",\"sms\",\"webhook\"]},\"lastUpdate\":\"2024-01-15T14:30:00Z\"}}",
            Target = "{\"statusMonitoring\":{\"enabled\":true,\"interval\":\"15s\",\"healthChecks\":[\"connectivity\",\"throughput\",\"latency\",\"errorRate\"],\"metrics\":{\"cpu\":\"12%\",\"memory\":\"40%\",\"disk\":\"55%\",\"network\":\"120Mbps\"},\"alerts\":{\"thresholds\":{\"cpu\":\"80%\",\"memory\":\"85%\",\"disk\":\"90%\",\"latency\":\"100ms\"},\"notifications\":[\"email\",\"sms\",\"webhook\"]},\"lastUpdate\":\"2024-01-15T14:30:05Z\"}}",
            SourceComponentId = Guid.NewGuid().ToString(),
            SourceComponentName = "Production Status Monitoring Agent",
            TargetComponentId = Guid.NewGuid().ToString(),
            TargetComponentName = "DR Status Monitoring Agent",
            WorkflowId = Guid.NewGuid().ToString(),
            WorkflowStatus = "Actively Monitoring",
            StartTime = DateTime.Now.AddHours(-24),
            EndTime = DateTime.Now.AddHours(24),
            RPO = "30 seconds",
            Status = "Healthy",
            IsFileTransfered = true
        };

        var expectedMessage = "CyberAirGapStatus has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberAirGapStatusResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberAirGapStatus(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberAirGapStatusResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateStatus_HandlesStatusTransition()
    {
        // Arrange
        var command = new UpdateAirGapStatusCommand
        {
            Id = Guid.NewGuid().ToString(),
            Status = "Warning"
        };

        var expectedMessage = "AirGap status has been updated to Warning successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateAirGapStatusResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateAirGapStatusResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task UpdateCyberAirGapStatus_HandlesHealthStatusChange()
    {
        // Arrange
        var command = new UpdateCyberAirGapStatusCommand
        {
            Id = Guid.NewGuid().ToString(),
            AirGapId = Guid.NewGuid().ToString(),
            AirGapName = "Updated Enterprise Air Gap Status Monitor",
            Description = "Updated status monitoring with enhanced alerting",
            SourceSiteId = Guid.NewGuid().ToString(),
            SourceSiteName = "Updated Primary Site",
            TargetSiteId = Guid.NewGuid().ToString(),
            TargetSiteName = "Updated DR Site",
            Port = 9443,
            Source = "{\"statusUpdate\":{\"health\":\"WARNING\",\"issues\":[\"High latency detected\"],\"metrics\":{\"latency\":\"150ms\",\"throughput\":\"80Mbps\"}}}",
            Target = "{\"statusUpdate\":{\"health\":\"WARNING\",\"issues\":[\"Intermittent connectivity\"],\"metrics\":{\"latency\":\"145ms\",\"throughput\":\"75Mbps\"}}}",
            SourceComponentId = Guid.NewGuid().ToString(),
            SourceComponentName = "Updated Source Monitor",
            TargetComponentId = Guid.NewGuid().ToString(),
            TargetComponentName = "Updated Target Monitor",
            WorkflowId = Guid.NewGuid().ToString(),
            WorkflowStatus = "Monitoring with Alerts",
            StartTime = DateTime.Now.AddHours(-2),
            EndTime = DateTime.Now.AddHours(22),
            RPO = "1 minute",
            Status = "Warning",
            IsFileTransfered = false
        };

        var expectedMessage = "CyberAirGapStatus has been updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberAirGapStatusResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberAirGapStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberAirGapStatusResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task GetCyberAirGapStatuss_HandlesEmptyList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberAirGapStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<CyberAirGapStatusListVm>());

        // Act
        var result = await _controller.GetCyberAirGapStatuss();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberAirGapStatuss = Assert.IsAssignableFrom<List<CyberAirGapStatusListVm>>(okResult.Value);
        Assert.Empty(cyberAirGapStatuss);
    }

    [Fact]
    public async Task GetCyberAirGapStatusById_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetCyberAirGapStatusById(invalidId));
    }

    [Fact]
    public async Task GetCyberAirGapStatusById_HandlesNotFound()
    {
        // Arrange
        var cyberAirGapStatusId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapStatusDetailQuery>(q => q.Id == cyberAirGapStatusId), default))
            .ThrowsAsync(new NotFoundException("CyberAirGapStatus", cyberAirGapStatusId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetCyberAirGapStatusById(cyberAirGapStatusId));
    }

    [Fact]
    public async Task GetPaginatedCyberAirGapStatuss_HandlesFilteringByHealthStatus()
    {
        // Arrange
        var query = new GetCyberAirGapStatusPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Critical"
        };

        var expectedData = new List<CyberAirGapStatusListVm>
        {
            new CyberAirGapStatusListVm
            {
                Id = Guid.NewGuid().ToString(),
                AirGapName = "Critical Status Monitor 1",
                Status = "Critical",
                IsFileTransfered = false
            },
            new CyberAirGapStatusListVm
            {
                Id = Guid.NewGuid().ToString(),
                AirGapName = "Critical Status Monitor 2",
                Status = "Critical",
                IsFileTransfered = false
            }
        };
        var expectedResults = PaginatedResult<CyberAirGapStatusListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapStatusPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize && q.SearchString == query.SearchString), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberAirGapStatuss(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberAirGapStatusListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, status => Assert.Equal("Critical", status.Status));
    }

    [Fact]
    public async Task CreateCyberAirGapStatus_HandlesInvalidAirGapReference()
    {
        // Arrange
        var command = new CreateCyberAirGapStatusCommand
        {
            AirGapId = Guid.NewGuid().ToString(),
            AirGapName = "Non-existent Air Gap Status",
            Description = "Test status monitor",
            SourceSiteId = Guid.NewGuid().ToString(),
            TargetSiteId = Guid.NewGuid().ToString(),
            Port = 8443,
            Status = "Healthy"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("CyberAirGap", command.AirGapId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.CreateCyberAirGapStatus(command));
    }

    [Fact]
    public async Task DeleteCyberAirGapStatus_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteCyberAirGapStatus(invalidId));
    }

    [Fact]
    public async Task UpdateStatus_HandlesCriticalStatusTransition()
    {
        // Arrange
        var command = new UpdateAirGapStatusCommand
        {
            Id = Guid.NewGuid().ToString(),
            Status = "Critical"
        };

        var expectedMessage = "AirGap status has been updated to Critical - immediate attention required!";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateAirGapStatusResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateAirGapStatusResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task CreateCyberAirGapStatus_HandlesHighFrequencyMonitoring()
    {
        // Arrange
        var command = new CreateCyberAirGapStatusCommand
        {
            AirGapId = Guid.NewGuid().ToString(),
            AirGapName = "High-Frequency Status Monitor",
            Description = "Real-time status monitoring with 5-second intervals",
            SourceSiteId = Guid.NewGuid().ToString(),
            SourceSiteName = "Primary Monitoring Center",
            TargetSiteId = Guid.NewGuid().ToString(),
            TargetSiteName = "DR Monitoring Center",
            Port = 8443,
            Source = "{\"highFrequencyMonitoring\":{\"enabled\":true,\"interval\":\"5s\",\"metrics\":{\"heartbeat\":\"active\",\"latency\":\"<10ms\",\"throughput\":\"1Gbps\",\"errorRate\":\"0.001%\"},\"alerts\":{\"realTime\":true,\"escalation\":[\"immediate\",\"manager\",\"executive\"]},\"dashboard\":\"live\"}}",
            Target = "{\"highFrequencyMonitoring\":{\"enabled\":true,\"interval\":\"5s\",\"metrics\":{\"heartbeat\":\"active\",\"latency\":\"<12ms\",\"throughput\":\"950Mbps\",\"errorRate\":\"0.002%\"},\"alerts\":{\"realTime\":true,\"escalation\":[\"immediate\",\"manager\",\"executive\"]},\"dashboard\":\"live\"}}",
            SourceComponentId = Guid.NewGuid().ToString(),
            SourceComponentName = "Real-time Monitoring Agent",
            TargetComponentId = Guid.NewGuid().ToString(),
            TargetComponentName = "Real-time Monitoring Agent",
            WorkflowId = Guid.NewGuid().ToString(),
            WorkflowStatus = "High-Frequency Monitoring Active",
            StartTime = DateTime.Now.AddHours(-1),
            EndTime = DateTime.Now.AddHours(23),
            RPO = "5 seconds",
            Status = "Healthy",
            IsFileTransfered = true
        };

        var expectedMessage = "CyberAirGapStatus has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberAirGapStatusResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberAirGapStatus(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberAirGapStatusResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberAirGapStatus_HandlesMaintenanceMode()
    {
        // Arrange
        var command = new UpdateCyberAirGapStatusCommand
        {
            Id = Guid.NewGuid().ToString(),
            AirGapId = Guid.NewGuid().ToString(),
            AirGapName = "Maintenance Mode Status Monitor",
            Description = "Status monitor during scheduled maintenance",
            SourceSiteId = Guid.NewGuid().ToString(),
            SourceSiteName = "Primary Site (Maintenance)",
            TargetSiteId = Guid.NewGuid().ToString(),
            TargetSiteName = "DR Site (Standby)",
            Port = 8443,
            Source = "{\"maintenanceMode\":{\"enabled\":true,\"startTime\":\"2024-01-15T02:00:00Z\",\"estimatedDuration\":\"4h\",\"maintenanceType\":\"Hardware Upgrade\",\"impact\":\"Replication Suspended\",\"fallbackPlan\":\"Manual Sync Post-Maintenance\"}}",
            Target = "{\"maintenanceMode\":{\"enabled\":true,\"mode\":\"Standby\",\"readyForResumption\":true,\"lastSync\":\"2024-01-15T01:59:59Z\",\"pendingOperations\":0,\"storageReady\":true}}",
            SourceComponentId = Guid.NewGuid().ToString(),
            SourceComponentName = "Maintenance Coordinator",
            TargetComponentId = Guid.NewGuid().ToString(),
            TargetComponentName = "Standby Monitor",
            WorkflowId = Guid.NewGuid().ToString(),
            WorkflowStatus = "Scheduled Maintenance",
            StartTime = DateTime.Now.AddHours(-2),
            EndTime = DateTime.Now.AddHours(2),
            RPO = "Suspended",
            Status = "Maintenance",
            IsFileTransfered = false
        };

        var expectedMessage = "CyberAirGapStatus has been updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberAirGapStatusResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberAirGapStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberAirGapStatusResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task IsCyberAirGapStatusNameExist_ThrowsException_WhenNameIsEmpty()
    {
        // Arrange
        var emptyName = "";
        var airGapStatusId = Guid.NewGuid().ToString();

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.IsCyberAirGapStatusNameExist(emptyName, airGapStatusId));
    }

    [Fact]
    public async Task UpdateStatus_HandlesRecoveryFromCritical()
    {
        // Arrange
        var command = new UpdateAirGapStatusCommand
        {
            Id = Guid.NewGuid().ToString(),
            Status = "Healthy"
        };

        var expectedMessage = "AirGap status has been recovered to Healthy - all systems operational!";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateAirGapStatusResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateAirGapStatusResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task CreateCyberAirGapStatus_HandlesMultiSiteMonitoring()
    {
        // Arrange
        var command = new CreateCyberAirGapStatusCommand
        {
            AirGapId = Guid.NewGuid().ToString(),
            AirGapName = "Multi-Site Status Monitoring Hub",
            Description = "Centralized status monitoring for multiple air gap connections across geographic regions",
            SourceSiteId = Guid.NewGuid().ToString(),
            SourceSiteName = "Global Monitoring Center",
            TargetSiteId = Guid.NewGuid().ToString(),
            TargetSiteName = "Regional Monitoring Centers",
            Port = 8443,
            Source = "{\"multiSiteMonitoring\":{\"sites\":[{\"name\":\"North America\",\"status\":\"Healthy\",\"connections\":15},{\"name\":\"Europe\",\"status\":\"Warning\",\"connections\":12},{\"name\":\"Asia Pacific\",\"status\":\"Healthy\",\"connections\":18}],\"aggregatedMetrics\":{\"totalConnections\":45,\"healthyConnections\":33,\"warningConnections\":12,\"criticalConnections\":0},\"globalRPO\":\"30 seconds\"}}",
            Target = "{\"multiSiteMonitoring\":{\"centralizedDashboard\":true,\"alertAggregation\":\"enabled\",\"crossSiteFailover\":\"automatic\",\"loadBalancing\":\"active\",\"redundancy\":\"triple\",\"complianceReporting\":\"real-time\"}}",
            SourceComponentId = Guid.NewGuid().ToString(),
            SourceComponentName = "Global Status Aggregator",
            TargetComponentId = Guid.NewGuid().ToString(),
            TargetComponentName = "Regional Status Collectors",
            WorkflowId = Guid.NewGuid().ToString(),
            WorkflowStatus = "Global Monitoring Active",
            StartTime = DateTime.Now.AddDays(-1),
            EndTime = DateTime.Now.AddDays(30),
            RPO = "15 seconds",
            Status = "Healthy",
            IsFileTransfered = true
        };

        var expectedMessage = "CyberAirGapStatus has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberAirGapStatusResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberAirGapStatus(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberAirGapStatusResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }
}
