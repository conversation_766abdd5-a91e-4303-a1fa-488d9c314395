﻿using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.DynamicSubDashboardModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Manage.Controllers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.Manage.Controllers
{
    public class CustomDashboardControllerTests
    {
        private readonly Mock<IDataProvider> _dataProviderMock = new();
        private readonly Mock<IDynamicSubDashboardService> _pageBuilderServiceMock = new();
        private readonly Mock<ILogger<CustomDashboardController>> _loggerMock = new();
        private readonly Mock<IMapper> _mapperMock = new();
        private CustomDashboardController _controller;

        public CustomDashboardControllerTests()
        {
            Initialize();
        }
        public void Initialize()
        { 
            _controller = new CustomDashboardController
               (
                _pageBuilderServiceMock.Object,
                _dataProviderMock.Object,
                _loggerMock.Object,
                _mapperMock.Object
               );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public void List_Returns_ViewResult()
        {
            var result = _controller.List();

            var viewResult = Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public async Task GetPageBuilderList_Returns_JsonResult_OnSuccess()
        {
            var expectedPageBuilders = new List<DynamicSubDashboardListVm> { new DynamicSubDashboardListVm { Id = "1" } };
            _dataProviderMock.Setup(d => d.DynamicSubDashboard.GetDynamicSubDashboardList())
                .ReturnsAsync(expectedPageBuilders);

            var result = await _controller.GetPageBuilderList();

            var jsonResult = Assert.IsType<JsonResult>(result);
            dynamic jsonData = jsonResult.Value;
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetPageBuilderList_Returns_JsonResult_OnFailure()
        {
            _dataProviderMock.Setup(d => d.DynamicSubDashboard.GetDynamicSubDashboardList())
                .ThrowsAsync(new Exception("Database error"));

            var result = await _controller.GetPageBuilderList();

            var jsonResult = Assert.IsType<JsonResult>(result);
            var jsonData = jsonResult.Value as dynamic; 
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":false", json);
        }


        [Fact]
        public async Task CreateOrUpdate_Creates_New_Dashboard_If_Id_Is_Empty()
        {
            var viewModel = new Fixture().Create<DynamicSubDashboardViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateDynamicSubDashboardCommand();

            var model = new DynamicSubDashboardViewModel { Id = string.Empty };
            var createCommand = new CreateDynamicSubDashboardCommand();
            var expectedResponse = new BaseResponse { Success = true, Message = "Created successfully" };

            _mapperMock.Setup(m => m.Map<CreateDynamicSubDashboardCommand>(model))
                .Returns(createCommand);
            _pageBuilderServiceMock.Setup(p => p.CreateAsync(createCommand))
                .ReturnsAsync(expectedResponse);

            var result = await _controller.CreateOrUpdate(model);

            var jsonResult = Assert.IsType<JsonResult>(result);
           
        }

        [Fact]
        public async Task CreateOrUpdate_Updates_Dashboard_If_Id_Is_Not_Empty()
        {
            var viewModel = new Fixture().Create<DynamicSubDashboardViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateDynamicSubDashboardCommand();

            var model = new DynamicSubDashboardViewModel { Id = "1" };
            var updateCommand = new UpdateDynamicSubDashboardCommand();
            var expectedResponse = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mapperMock.Setup(m => m.Map<UpdateDynamicSubDashboardCommand>(model))
                .Returns(updateCommand);
            _pageBuilderServiceMock.Setup(p => p.UpdateAsync(updateCommand))
                .ReturnsAsync(expectedResponse);

            var result = await _controller.CreateOrUpdate(model);

            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(expectedResponse, jsonResult.Value);
        }

        [Fact]
        public async Task GetByReferenceId_Returns_JsonResult_OnSuccess()
        {
            var id = "1";
            var expectedPageBuilders = new DynamicSubDashboardDetailVm();
            _dataProviderMock.Setup(d => d.DynamicSubDashboard.GetByReferenceId(id))
                .ReturnsAsync(expectedPageBuilders);

            var result = await _controller.GetByReferenceId(id);

            var jsonResult = Assert.IsType<JsonResult>(result);
            dynamic jsonData = jsonResult.Value;
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":true", json);
           
        }

        [Fact]
        public async Task GetByReferenceId_Returns_JsonResult_OnFailure()
        {
            var id = "1";
            _dataProviderMock.Setup(d => d.DynamicSubDashboard.GetByReferenceId(id))
                .ThrowsAsync(new Exception("Database error"));

            var result = await _controller.GetByReferenceId(id);

            var jsonResult = Assert.IsType<JsonResult>(result);
           
        }


        [Fact]
        public async Task Delete_Returns_RedirectToAction_OnSuccess()
        {
            var id = "1";
            var expectedResponse = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _dataProviderMock.Setup(d => d.DynamicSubDashboard.DeleteAsync(id))
                .ReturnsAsync(expectedResponse);

            var result = await _controller.Delete(id);

            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            Assert.Equal("Manage", redirectResult.RouteValues["area"]);
        }
    }
}

