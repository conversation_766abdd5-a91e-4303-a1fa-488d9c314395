using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class BulkImportOperationGroupRepository : BaseRepository<BulkImportOperationGroup>, IBulkImportOperationGroupRepository
{
    private readonly ApplicationDbContext _dbContext;

    public BulkImportOperationGroupRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
    }

    public async Task<List<BulkImportOperationGroup>> GetBulkImportOperationGroupByBulkImportOperationIds(List<string> ids)
    {
        return await _dbContext.BulkImportOperationGroups
            .AsNoTracking()
            .Active()
            .Where(x=> ids.Contains(x.BulkImportOperationId))
            .Select(MapToBulkImportOperationGroup())
            .ToListAsync();
    }
    public async Task<List<BulkImportOperationGroup>>GetBulkImportOperationGroupByBulkImportOperationId(string id)
    {
        return await _dbContext.BulkImportOperationGroups
            .AsNoTracking()
            .Active()
            .Where(x => x.BulkImportOperationId == id)
            .Select(MapToBulkImportOperationGroup())
            .ToListAsync();
    }

    private static Expression<Func<BulkImportOperationGroup, BulkImportOperationGroup>> MapToBulkImportOperationGroup()
    {
        return x => new BulkImportOperationGroup
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            BulkImportOperationId = x.BulkImportOperationId,
            InfraObjectName = x.InfraObjectName,
            CompanyId = x.CompanyId,
            Properties = x.Properties,
            ProgressStatus = x.ProgressStatus,
            Status = x.Status,
            ErrorMessage = x.ErrorMessage,
            ConditionalOperation = x.ConditionalOperation,
            NodeId = x.NodeId
        };
    }
}
