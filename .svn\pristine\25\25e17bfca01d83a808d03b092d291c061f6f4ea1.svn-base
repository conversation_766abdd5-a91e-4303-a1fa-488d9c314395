﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class BusinessServiceEvaluationRepositoryMocks
{
    public static Mock<IBusinessServiceEvaluationRepository> CreateBusinessServiceEvaluationRepository(List<BusinessServiceEvaluation> businessServiceEvaluations)
    {
        var businessServiceEvaluationRepository = new Mock<IBusinessServiceEvaluationRepository>();

        businessServiceEvaluationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServiceEvaluations);

        businessServiceEvaluationRepository.Setup(repo => repo.AddAsync(It.IsAny<BusinessServiceEvaluation>())).ReturnsAsync(
            (BusinessServiceEvaluation businessServiceEvaluation) =>
            {
                businessServiceEvaluation.Id = new Fixture().Create<int>();

                businessServiceEvaluation.ReferenceId = new Fixture().Create<Guid>().ToString();

                businessServiceEvaluations.Add(businessServiceEvaluation);

                return businessServiceEvaluation;
            });

        return businessServiceEvaluationRepository;
    }

    public static Mock<IBusinessServiceEvaluationRepository> UpdateBusinessServiceEvaluationRepository(List<BusinessServiceEvaluation> businessServiceEvaluations)
    {
        var businessServiceEvaluationRepository = new Mock<IBusinessServiceEvaluationRepository>();

        businessServiceEvaluationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServiceEvaluations);

        businessServiceEvaluationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessServiceEvaluations.SingleOrDefault(x => x.ReferenceId == i));

        businessServiceEvaluationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BusinessServiceEvaluation>())).ReturnsAsync((BusinessServiceEvaluation businessServiceEvaluation) =>
        {
            var index = businessServiceEvaluations.FindIndex(item => item.ReferenceId == businessServiceEvaluation.ReferenceId);

            businessServiceEvaluations[index] = businessServiceEvaluation;

            return businessServiceEvaluation;

        });
        return businessServiceEvaluationRepository;
    }

    public static Mock<IBusinessServiceEvaluationRepository> DeleteBusinessServiceEvaluationRepository(List<BusinessServiceEvaluation> businessServiceEvaluations)
    {
        var businessServiceEvaluationRepository = new Mock<IBusinessServiceEvaluationRepository>();

        businessServiceEvaluationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServiceEvaluations);

        businessServiceEvaluationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessServiceEvaluations.SingleOrDefault(x => x.ReferenceId == i));

        businessServiceEvaluationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BusinessServiceEvaluation>())).ReturnsAsync((BusinessServiceEvaluation businessServiceEvaluation) =>
        {
            var index = businessServiceEvaluations.FindIndex(item => item.ReferenceId == businessServiceEvaluation.ReferenceId);

            businessServiceEvaluation.IsActive = false;

            businessServiceEvaluations[index] = businessServiceEvaluation;

            return businessServiceEvaluation;
        });

        return businessServiceEvaluationRepository;
    }

    public static Mock<IBusinessServiceEvaluationRepository> GetBusinessServiceEvaluationRepository(List<BusinessServiceEvaluation> businessServiceEvaluations)
    {
        var businessServiceEvaluationRepository = new Mock<IBusinessServiceEvaluationRepository>();

        businessServiceEvaluationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServiceEvaluations);

        businessServiceEvaluationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessServiceEvaluations.SingleOrDefault(x => x.ReferenceId == i));

        businessServiceEvaluationRepository.Setup(repo => repo.GetBusinessServiceEvaluationByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessServiceEvaluations.SingleOrDefault(x => x.ReferenceId == i));
        
        return businessServiceEvaluationRepository;
    }

    public static Mock<IBusinessServiceEvaluationRepository> GetBusinessServiceEvaluationEmptyRepository()
    {
        var businessServiceEvaluationRepository = new Mock<IBusinessServiceEvaluationRepository>();

        businessServiceEvaluationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<BusinessServiceEvaluation>());

        return businessServiceEvaluationRepository;
    }
}