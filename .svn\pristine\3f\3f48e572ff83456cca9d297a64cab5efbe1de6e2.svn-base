using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IDriftManagementMonitorStatusRepository : IRepository<DriftManagementMonitorStatus>
{
    Task<bool> IsNameExist(string name, string id);

    Task<List<DriftManagementMonitorStatus>> GetDriftManagementStatusByInfraObjectId(string infraObjectId);
    Task<DriftManagementMonitorStatus> GetByInfraObjectIdAsync(string infraObjectId);
}