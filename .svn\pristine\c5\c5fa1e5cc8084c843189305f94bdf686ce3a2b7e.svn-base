﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetPaginatedList;

public class GetWorkflowActionPaginatedListQueryHandler : IRequestHandler<GetWorkflowActionPaginatedListQuery,
    PaginatedResult<WorkflowActionListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowActionRepository _workflowActionRepository;

    public GetWorkflowActionPaginatedListQueryHandler(IMapper mapper,
        IWorkflowActionRepository workflowActionRepository)
    {
        _mapper = mapper;
        _workflowActionRepository = workflowActionRepository;
    }

    public async Task<PaginatedResult<WorkflowActionListVm>> Handle(GetWorkflowActionPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _workflowActionRepository.GetPaginatedQuery();

        var productFilterSpec = new WorkflowActionFilterSpecification(request.SearchString);

        var workflowActionLists = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<WorkflowActionListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return workflowActionLists;
    }
}