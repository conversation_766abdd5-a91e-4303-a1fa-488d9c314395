using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MongoDBMonitorLogFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "MongoDB";

    public List<MongoDBMonitorLog> MongoDBMonitorLogPaginationList { get; set; }
    public List<MongoDBMonitorLog> MongoDBMonitorLogList { get; set; }
    public MongoDBMonitorLog MongoDBMonitorLogDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public MongoDBMonitorLogFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<MongoDBMonitorLog>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow));
            //.With(x => x.CompanyId, CompanyId));

        MongoDBMonitorLogPaginationList = _fixture.CreateMany<MongoDBMonitorLog>(20).ToList();
        MongoDBMonitorLogList = _fixture.CreateMany<MongoDBMonitorLog>(5).ToList();
        MongoDBMonitorLogDto = _fixture.Create<MongoDBMonitorLog>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public MongoDBMonitorLog CreateMongoDBMonitorLogWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<MongoDBMonitorLog>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
          
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public MongoDBMonitorLog CreateMongoDBMonitorLogWithWhitespace()
    {
        return CreateMongoDBMonitorLogWithProperties(type: "  MongoDB  ");
    }

    public MongoDBMonitorLog CreateMongoDBMonitorLogWithLongType(int length)
    {
        var longType = new string('A', length);
        return CreateMongoDBMonitorLogWithProperties(type: longType);
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonTypes = { "MongoDB", "MongoReplication", "MongoSharding", "MongoCluster" };
        public static readonly string[] CommonInfraObjectIds = { "INFRA_001", "INFRA_002", "INFRA_003" };
        public static readonly string[] CommonWorkflowIds = { "WF_001", "WF_002", "WF_003" };
    }
}
