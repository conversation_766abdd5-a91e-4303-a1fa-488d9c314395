﻿namespace ContinuityPatrol.Application.Features.Report.Queries.GetDrDrillReport;

public class WorkflowActionResultDrDrillReportVm
{
    public string WorkflowActionId { get; set; }
    public string WorkflowActionName { get; set; }
    public string WorkflowOperationId { get; set; }
    public string WorkflowOperationGroupId { get; set; }
    public bool IsParallel { get; set; }
    public string Status { get; set; }
    public string StartTime { get; set; }
    public int StartRto { get; set; }
    public string EndTime { get; set; }
    public TimeSpan TotalTime { get; set; }
    public string Message { get; set; }
    public string ExecutionNode { get; set; }
}