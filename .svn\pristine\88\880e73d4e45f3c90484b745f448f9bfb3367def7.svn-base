using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class BackUpLogFixture : IDisposable
{
    public List<BackUpLog> BackUpLogPaginationList { get; set; }
    public List<BackUpLog> BackUpLogList { get; set; }
    public BackUpLog BackUpLogDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public BackUpLogFixture()
    {
        var fixture = new Fixture();

        BackUpLogList = fixture.Create<List<BackUpLog>>();

        BackUpLogPaginationList = fixture.CreateMany<BackUpLog>(20).ToList();

        BackUpLogPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BackUpLogPaginationList.ForEach(x => x.IsActive = true);

        BackUpLogList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BackUpLogList.ForEach(x => x.IsActive = true);

        BackUpLogDto = fixture.Create<BackUpLog>();
        BackUpLogDto.ReferenceId = Guid.NewGuid().ToString();
        BackUpLogDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
