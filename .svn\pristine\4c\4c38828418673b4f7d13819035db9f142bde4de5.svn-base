﻿using ContinuityPatrol.Application.Features.BulkImport.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Next;
using ContinuityPatrol.Application.Features.BulkImport.Commands.RollBack;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class BulkImportService : BaseClient, IBulkImportServices
{
    public BulkImportService(IConfiguration configuration,IAppCache appCache,ILogger<BulkImportService> logger): base(configuration,appCache,logger)
    {
        
    }
   
    public async Task<BaseResponse> CreateBulkImport(CreateBulkImportCommand createBulkImportCommand)
    {
        var request = new RestRequest("api/v6/bulkimport/create", Method.Post);

        request.AddJsonBody(createBulkImportCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> NextBulkImportAction(NextBulkImportCommand nextBulkImportCommand)
    {
        var request = new RestRequest("api/v6/bulkimport/next", Method.Put);

        request.AddJsonBody(nextBulkImportCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> RollBackBulkImportAction(RollBackBulkImportCommand rollBackBulkImportCommand)
    {
        var request = new RestRequest("/api/v6/bulkimport/rollback", Method.Put);

        request.AddJsonBody(rollBackBulkImportCommand);

        return await Put<BaseResponse>(request);
    }
}
