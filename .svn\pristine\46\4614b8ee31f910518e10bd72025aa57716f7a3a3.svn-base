﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DriftJob.Events.UpdateState;

public class DriftJobStateUpdatedEventHandler : INotificationHandler<DriftJobStateUpdatedEvent>
{
    private readonly ILogger<DriftJobStateUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DriftJobStateUpdatedEventHandler(ILoggedInUserService userService, ILogger<DriftJobStateUpdatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(DriftJobStateUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.DriftJob.ToString(),
            Action = $"{ActivityType.Update} {Modules.DriftJob}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Drift Job Name :'{updatedEvent.DriftJobName}' , State:{updatedEvent.State} updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation(
            $"Drift Job Name :'{updatedEvent.DriftJobName}',  State : {updatedEvent.State} updated successfully.");
    }
}