﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.MSSQLDBMirroingLogsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.MSSQLDBMirroingLogs.Queries.GetPaginatedList;

public class GetMSSQLDbMonitorLogsPaginatedListQueryHandler : IRequestHandler<GetMSSQLDbMonitorLogsPaginatedListQuery,
    PaginatedResult<MSSQLDBMirroringLogListVm>>
{
    private readonly IMsSqlDbMirroringLogRepository _dbmirroringMonitorLogsRepository;


    private readonly IMapper _mapper;

    public GetMSSQLDbMonitorLogsPaginatedListQueryHandler(IMapper mapper,
        IMsSqlDbMirroringLogRepository dbmirroringMonitorLogsRepository)
    {
        _mapper = mapper;
        _dbmirroringMonitorLogsRepository = dbmirroringMonitorLogsRepository;
    }


    public async Task<PaginatedResult<MSSQLDBMirroringLogListVm>> Handle(
        GetMSSQLDbMonitorLogsPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _dbmirroringMonitorLogsRepository.GetPaginatedQuery();

        var productFilterSpec = new MssqlDbMirroingMonitorLogsFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<MSSQLDBMirroringLogListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}