﻿using ContinuityPatrol.Application.Features.TableAccess.Commands.Update;
using ContinuityPatrol.Application.Features.TableAccess.Event.Update;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.TableAccess.Commands
{
    public class UpdateTableAccessTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ITableAccessRepository> _mockTableAccessRepository;
        private readonly Mock<IDataSetRepository> _mockDataSetRepository;
        private readonly Mock<IArchiveRepository> _mockArchiveRepository;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly UpdateTableAccessCommandHandler _handler;

        public UpdateTableAccessTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockTableAccessRepository = new Mock<ITableAccessRepository>();
            _mockDataSetRepository = new Mock<IDataSetRepository>();
            _mockPublisher = new Mock<IPublisher>();
            _mockArchiveRepository = new Mock<IArchiveRepository>();


            _handler = new UpdateTableAccessCommandHandler(
                _mockMapper.Object,
                _mockTableAccessRepository.Object,
                _mockPublisher.Object,
                _mockDataSetRepository.Object, _mockArchiveRepository.Object);
        }

        [Fact]
        public async Task Handle_UpdatesSingleTableAccess_ReturnsExpectedResponse()
        {
            var updateTableAccessCommand = new UpdateTableAccessCommand
            {
                UpdateTableAccess = new List<UpdateTableAccess>
                {
                    new UpdateTableAccess { Id = "ValidId", TableName = "UpdatedTable" }
                }
            };

            var tableAccessEntity = new Domain.Entities.TableAccess
            {
                ReferenceId = "ValidId",
                TableName = "OldTableName"
            };

            _mockDataSetRepository
                .Setup(repo => repo.GetDataSetByTableAccessId("ValidId"))
                .ReturnsAsync(new List<Domain.Entities.DataSet>());

            _mockTableAccessRepository
                .Setup(repo => repo.GetByReferenceIdAsync("ValidId"))
                .ReturnsAsync(tableAccessEntity);

            _mockTableAccessRepository
                .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.TableAccess>()))
                .ReturnsAsync(tableAccessEntity);

            _mockMapper
                .Setup(mapper => mapper.Map(updateTableAccessCommand.UpdateTableAccess[0], tableAccessEntity,
                    typeof(UpdateTableAccess), typeof(Domain.Entities.TableAccess)))
                .Callback((object source, object destination, System.Type sourceType, System.Type destinationType) =>
                {
                    var update = (UpdateTableAccess)source;
                    var entity = (Domain.Entities.TableAccess)destination;
                    entity.TableName = update.TableName;
                });

            var cancellationToken = CancellationToken.None;

            var response = await _handler.Handle(updateTableAccessCommand, cancellationToken);

            _mockTableAccessRepository.Verify(repo => repo.UpdateAsync(It.Is<Domain.Entities.TableAccess>(e =>
                e.TableName == "UpdatedTable")), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.Is<TableAccessUpdatedEvent>(e =>
                e.TableName == "UpdatedTable"), cancellationToken), Times.Once);

            Assert.NotNull(response);
            Assert.Equal(" Table Access 'UpdatedTable' has been updated successfully", response.Message);
        }

        [Fact]
        public async Task Handle_ThrowsInvalidException_WhenTableAccessInUse()
        {
            var updateTableAccessCommand = new UpdateTableAccessCommand
            {
                UpdateTableAccess = new List<UpdateTableAccess>
                {
                    new UpdateTableAccess { Id = "InUseId", TableName = "InUseTable" }
                }
            };

            _mockDataSetRepository
                .Setup(repo => repo.GetDataSetByTableAccessId("InUseId"))
                .ReturnsAsync(new List<Domain.Entities.DataSet> { new Domain.Entities.DataSet() });

            var cancellationToken = CancellationToken.None;

            var exception = await Assert.ThrowsAsync<InvalidException>(() =>
                _handler.Handle(updateTableAccessCommand, cancellationToken));

            Assert.Equal("This Table Access is currently in use.", exception.Message);

            _mockTableAccessRepository.Verify(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()), Times.Never);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<TableAccessUpdatedEvent>(), cancellationToken), Times.Never);
        }

        [Fact]
        public async Task Handle_ThrowsNotFoundException_WhenTableAccessNotFound()
        {
            var updateTableAccessCommand = new UpdateTableAccessCommand
            {
                UpdateTableAccess = new List<UpdateTableAccess>
                {
                   new UpdateTableAccess { Id = "InvalidId", TableName = "NonExistentTable" }
                }
            };

            _mockDataSetRepository
                .Setup(repo => repo.GetDataSetByTableAccessId("InvalidId"))
                .ReturnsAsync(new List<Domain.Entities.DataSet>());

            _mockTableAccessRepository
                .Setup(repo => repo.GetByReferenceIdAsync("InvalidId"))
                .ReturnsAsync((Domain.Entities.TableAccess)null);

            var cancellationToken = CancellationToken.None;

            var exception = await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(updateTableAccessCommand, cancellationToken));

            Assert.Equal("TableAccess (InvalidId) is not found or not authorized", exception.Message);

            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<TableAccessUpdatedEvent>(), cancellationToken), Times.Never);
        }

        [Fact]
        public async Task Handle_UpdatesMultipleTableAccess_ReturnsSuccessResponse()
        {
            var updateTableAccessCommand = new UpdateTableAccessCommand
            {
                UpdateTableAccess = new List<UpdateTableAccess>
                {
                   new UpdateTableAccess { Id = "ValidId1", TableName = "UpdatedTable1" },
                   new UpdateTableAccess { Id = "ValidId2", TableName = "UpdatedTable2" }
                }
            };

            var tableAccessEntities = new List<Domain.Entities.TableAccess>
            {
                new Domain.Entities.TableAccess { ReferenceId = "ValidId1", TableName = "OldTableName1" },
                new Domain.Entities.TableAccess { ReferenceId = "ValidId2", TableName = "OldTableName2" }
            };

            _mockDataSetRepository
                .Setup(repo => repo.GetDataSetByTableAccessId(It.IsAny<string>()))
                .ReturnsAsync(new List<Domain.Entities.DataSet>());

            _mockTableAccessRepository
                .Setup(repo => repo.GetByReferenceIdAsync(It.Is<string>(id => id == "ValidId1")))
                .ReturnsAsync(tableAccessEntities[0]);

            _mockTableAccessRepository
                .Setup(repo => repo.GetByReferenceIdAsync(It.Is<string>(id => id == "ValidId2")))
                .ReturnsAsync(tableAccessEntities[1]);

            var cancellationToken = CancellationToken.None;

            var response = await _handler.Handle(updateTableAccessCommand, cancellationToken);

            Assert.NotNull(response);
            Assert.Equal("Table Access updated successfully.", response.Message);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<TableAccessUpdatedEvent>(), cancellationToken), Times.Exactly(2));
        }
    }
}
