using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class AdPasswordJobRepositoryMocks
{
    public static Mock<IAdPasswordJobRepository> CreateAdPasswordJobRepository(List<AdPasswordJob> adPasswordJobs)
    {
        var mockAdPasswordJobRepository = new Mock<IAdPasswordJobRepository>();

        mockAdPasswordJobRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(adPasswordJobs);

        mockAdPasswordJobRepository.Setup(repo => repo.AddAsync(It.IsAny<AdPasswordJob>())).ReturnsAsync(
            (AdPasswordJob adPasswordJob) =>
            {
                adPasswordJob.Id = new Fixture().Create<int>();
                adPasswordJob.ReferenceId = new Fixture().Create<Guid>().ToString();
                adPasswordJobs.Add(adPasswordJob);
                return adPasswordJob;
            });

        //mockAdPasswordJobRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AdPasswordJob>()))
        //    .Returns(Task.CompletedTask);

        //mockAdPasswordJobRepository.Setup(repo => repo.DeleteAsync(It.IsAny<AdPasswordJob>()))
        //    .Returns(Task.CompletedTask);

        mockAdPasswordJobRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => adPasswordJobs.FirstOrDefault(x => x.ReferenceId == id));

        mockAdPasswordJobRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);

        return mockAdPasswordJobRepository;
    }

    public static Mock<IAdPasswordJobRepository> CreateDeleteAdPasswordJobRepository(List<AdPasswordJob> adPasswordJobs)
    {
        var mockAdPasswordJobRepository = new Mock<IAdPasswordJobRepository>();

        mockAdPasswordJobRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(adPasswordJobs);

        mockAdPasswordJobRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => adPasswordJobs.FirstOrDefault(x => x.ReferenceId == id));

        mockAdPasswordJobRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AdPasswordJob>()));
            //.Returns(Task.CompletedTask);

        return mockAdPasswordJobRepository;
    }

    public static Mock<IAdPasswordJobRepository> CreateAdPasswordJobEventRepository(List<UserActivity> userActivities)
    {
        var mockUserActivityRepository = new Mock<IUserActivityRepository>();

        mockUserActivityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        mockUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();
                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();
                userActivities.Add(userActivity);
                return userActivity;
            });

        return mockUserActivityRepository.As<IAdPasswordJobRepository>();
    }

    public static Mock<IAdPasswordJobRepository> CreateUpdateAdPasswordJobRepository(List<AdPasswordJob> adPasswordJobs)
    {
        var mockAdPasswordJobRepository = new Mock<IAdPasswordJobRepository>();

        mockAdPasswordJobRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(adPasswordJobs);

        mockAdPasswordJobRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => adPasswordJobs.FirstOrDefault(x => x.ReferenceId == id));

        mockAdPasswordJobRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AdPasswordJob>()));
           // .Returns(Task.CompletedTask);

        mockAdPasswordJobRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);

        return mockAdPasswordJobRepository;
    }

    public static Mock<IAdPasswordJobRepository> CreateQueryAdPasswordJobRepository(List<AdPasswordJob> adPasswordJobs)
    {
        var mockAdPasswordJobRepository = new Mock<IAdPasswordJobRepository>();

        mockAdPasswordJobRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(adPasswordJobs);

        mockAdPasswordJobRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => adPasswordJobs.FirstOrDefault(x => x.ReferenceId == id));

        mockAdPasswordJobRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string name, string id) =>
            {
                if (string.IsNullOrEmpty(id))
                    return adPasswordJobs.Any(x => x.DomainServer == name);
                return adPasswordJobs.Any(x => x.DomainServer == name && x.ReferenceId != id);
            });

        // Mock GetPaginatedQuery for paginated queries
        var queryable = adPasswordJobs.AsQueryable();
        mockAdPasswordJobRepository.Setup(repo => repo.GetPaginatedQuery())
            .Returns(queryable);

        return mockAdPasswordJobRepository;
    }

    public static Mock<ILoadBalancerRepository> CreateLoadBalancerRepository(List<LoadBalancer> loadBalancers)
    {
        var mockLoadBalancerRepository = new Mock<ILoadBalancerRepository>();

        mockLoadBalancerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(loadBalancers);

        mockLoadBalancerRepository.Setup(repo => repo.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string type, string typeCategory) =>
                loadBalancers.FirstOrDefault(x => x.Type == type && x.TypeCategory == typeCategory) ??
                loadBalancers.FirstOrDefault(x => x.TypeCategory == typeCategory));

        return mockLoadBalancerRepository;
    }
}
