﻿using ContinuityPatrol.Application.Features.SVCMssqlMonitorLog.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.SVCMssqlMonitorLog.Queries
{
    public class GetSVCMssqlMonitorLogDetailQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISVCMssqlMonitorLogRepository> _mockSVCMssqlMonitorLogRepository;
        private readonly GetSVCMssqlMonitorLogDetailQueryHandler _handler;

        public GetSVCMssqlMonitorLogDetailQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSVCMssqlMonitorLogRepository = new Mock<ISVCMssqlMonitorLogRepository>();
            _handler = new GetSVCMssqlMonitorLogDetailQueryHandler(_mockSVCMssqlMonitorLogRepository.Object, _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedLogDetail_WhenLogExists()
        {
            var query = new GetSVCMssqlMonitorLogDetailQuery { Id = Guid.NewGuid().ToString() };

            var logEntity = new Domain.Entities.SVCMssqlMonitorLog
            {
                ReferenceId = query.Id,
                WorkflowName = "Error",
                Type = "Sample error log"
            };

            var logViewModel = new SVCMssqlMonitorLogDetailVm
            {
                Id = logEntity.ReferenceId,
                WorkflowName = "Message",
                Type = "LogType"
            };

            _mockSVCMssqlMonitorLogRepository.Setup(repo => repo.GetByReferenceIdAsync(query.Id))
                .ReturnsAsync(logEntity);

            _mockMapper.Setup(m => m.Map<SVCMssqlMonitorLogDetailVm>(logEntity))
                .Returns(logViewModel);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(logViewModel.Id, result.Id);
            Assert.Equal(logViewModel.Type, result.Type);

            _mockSVCMssqlMonitorLogRepository.Verify(repo => repo.GetByReferenceIdAsync(query.Id), Times.Once);
            _mockMapper.Verify(m => m.Map<SVCMssqlMonitorLogDetailVm>(logEntity), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenLogDoesNotExist()
        {
            var query = new GetSVCMssqlMonitorLogDetailQuery { Id = Guid.NewGuid().ToString() };

            _mockSVCMssqlMonitorLogRepository.Setup(repo => repo.GetByReferenceIdAsync(query.Id))
                .ReturnsAsync((Domain.Entities.SVCMssqlMonitorLog)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, CancellationToken.None));

            _mockSVCMssqlMonitorLogRepository.Verify(repo => repo.GetByReferenceIdAsync(query.Id), Times.Once);
            _mockMapper.Verify(m => m.Map<SVCMssqlMonitorLogDetailVm>(It.IsAny<Domain.Entities.SVCMssqlMonitorLog>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenLogIsInactive()
        {
            var query = new GetSVCMssqlMonitorLogDetailQuery { Id = Guid.NewGuid().ToString() };

            var logEntity = new Domain.Entities.SVCMssqlMonitorLog
            {
                ReferenceId = query.Id,
                IsActive = false, 
                WorkflowName = "Error",
                Type = "Inactive log"
            };

            _mockSVCMssqlMonitorLogRepository.Setup(repo => repo.GetByReferenceIdAsync(query.Id))
                .ReturnsAsync(logEntity);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, CancellationToken.None));

            _mockSVCMssqlMonitorLogRepository.Verify(repo => repo.GetByReferenceIdAsync(query.Id), Times.Once);
            _mockMapper.Verify(m => m.Map<SVCMssqlMonitorLogDetailVm>(It.IsAny<Domain.Entities.SVCMssqlMonitorLog>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldCallRepositoryMethodOnce()
        {
            var query = new GetSVCMssqlMonitorLogDetailQuery { Id = Guid.NewGuid().ToString() };

            var logEntity = new Domain.Entities.SVCMssqlMonitorLog
            {
                ReferenceId = query.Id,
                WorkflowName = "Info",
                Type = "Sample info log"
            };

            _mockSVCMssqlMonitorLogRepository.Setup(repo => repo.GetByReferenceIdAsync(query.Id))
                .ReturnsAsync(logEntity);

            _mockMapper.Setup(m => m.Map<SVCMssqlMonitorLogDetailVm>(logEntity))
                .Returns(new SVCMssqlMonitorLogDetailVm());

            await _handler.Handle(query, CancellationToken.None);

            _mockSVCMssqlMonitorLogRepository.Verify(repo => repo.GetByReferenceIdAsync(query.Id), Times.Once);
        }
    }
}
