﻿using ContinuityPatrol.Application.Features.Database.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Infrastructure.Contract;
using FluentValidation;
using System.Reflection;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace ContinuityPatrol.Application.UnitTests.Features.Database.Validators;

public class CreateDatabaseValidatorTests
{
    private readonly Mock<IDatabaseRepository> _mockDatabaseRepository;
    private readonly Mock<IServerRepository> _mockServerRepository;
    private readonly Mock<ISiteRepository> _mockSiteRepository;
    private readonly Mock<ISiteTypeRepository> _mockSiteTypeRepository;
    private readonly Mock<ILicenseManagerRepository> _mockLicenseManagerRepository;
    private readonly Mock<ILicenseValidationService> _mockLicenseValidationService;
    private readonly Mock<CreateDatabaseCommandValidator> _mockcreateDbCommandValidator=new Mock<CreateDatabaseCommandValidator>();
    private readonly Mock<IMapper> _mapperMock = new();
      

    public CreateDatabaseValidatorTests()
    {
        var databases = new Fixture().Create<List<Domain.Entities.Database>>();
        var servers = new Fixture().Create<List<Domain.Entities.Server>>();
        var sites = new Fixture().Create<List<Domain.Entities.Site>>();
        var licenseManager = new Fixture().Create<List<Domain.Entities.LicenseManager>>();
        

        _mockLicenseValidationService = new Mock<ILicenseValidationService>();
        _mockServerRepository = ServerRepositoryMocks.CreateServerRepository(servers);
        _mockSiteRepository = SiteRepositoryMocks.CreateSiteRepository(sites);
        _mockDatabaseRepository = DatabaseRepositoryMocks.CreateDatabaseRepository(databases);
        _mockLicenseManagerRepository = LicenseManagerRepositoryMocks.UpdateBaseLicenseRepository(licenseManager);
        _mockSiteTypeRepository = new Mock<ISiteTypeRepository>();


    }

    //Name

    static T RemovePrefixesFromObject<T>(T obj, string prefixPattern, string jsonPrefix)
    {
        //var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
        //                          .Where(p => p.PropertyType == typeof(string));

        //foreach (var property in properties)
        //{
        //    var currentValue = (string)property.GetValue(obj);
        //    if (currentValue != null)
        //    {
        //        var newValue = Regex.Replace(currentValue, prefixPattern, "");
        //        property.SetValue(obj, newValue);
        //    }
        //}
        //return obj;


        var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
                                      .Where(p => p.PropertyType == typeof(string));

        foreach (var property in properties)
        {
            var currentValue = (string)property.GetValue(obj);
            if (currentValue != null)
            {
                if (property.Name == jsonPrefix)
                {
                    // Convert to JSON if property name matches the specific prefix
                    var jsonValue = JsonSerializer.Serialize(new { Properties = currentValue });
                    property.SetValue(obj, jsonValue);
                }
                else
                {
                    // Remove prefixes for other properties
                    var newValue = Regex.Replace(currentValue, prefixPattern, "");
                    property.SetValue(obj, newValue);
                }
            }
        }
        return obj;
    }
    

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Name_InDatabase_WithEmpty(CreateDatabaseCommand createDatabaseCommand)
    {
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";

        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        updatedDatabases.Name = "";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        _mapperMock.Setup(m => m.Map<CreateDatabaseCommand>(updatedDatabases)).Returns(new CreateDatabaseCommand());
        var updatedJson = JsonSerializer.Serialize(updatedDatabases, new JsonSerializerOptions { WriteIndented = true });

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameRequired, validateResult.Errors[2].ErrorMessage);
    }



    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Name_InDatabase_IsNull(CreateDatabaseCommand createDatabaseCommand)
    {
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";

        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        updatedDatabases.Name = null;
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";

        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        _mapperMock.Setup(m => m.Map<CreateDatabaseCommand>(updatedDatabases)).Returns(new CreateDatabaseCommand());
        var updatedJson = JsonSerializer.Serialize(updatedDatabases, new JsonSerializerOptions { WriteIndented = true });

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameNotEmpty, validateResult.Errors[3].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Name_InDatabase_MiniMumRange(CreateDatabaseCommand createDatabaseCommand)
    {
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";

        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);

        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        updatedDatabases.Name = "DB";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        _mapperMock.Setup(m => m.Map<CreateDatabaseCommand>(updatedDatabases)).Returns(new CreateDatabaseCommand());
        var updatedJson = JsonSerializer.Serialize(updatedDatabases, new JsonSerializerOptions { WriteIndented = true });

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseLicenseCount, validateResult.Errors[2].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Name_InDatabase_MaxiMumRange(CreateDatabaseCommand createDatabaseCommand)
    {
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";

        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        updatedDatabases.Name = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 1000000;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 100000;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        _mapperMock.Setup(m => m.Map<CreateDatabaseCommand>(updatedDatabases)).Returns(new CreateDatabaseCommand());
        var updatedJson = JsonSerializer.Serialize(updatedDatabases, new JsonSerializerOptions { WriteIndented = true });
        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseLicenseCount, validateResult.Errors[2].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_Name_InDatabase(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.Name = "   PTS   ";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
       

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[3].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_Name_InDatabase_With_DoubleSpace_InFront(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.Name = "  PTS India";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[3].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_Name_InDatabase_With_TripleSpace_InBetween(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.Name = "PTS Technosoft    India";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
       

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[2].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_Name_InDatabase_With_SpecialCharacters_InFront(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.Name = "@#PTSTechnosofIndia";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[2].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_Name_InDatabase_With_SpecialCharacters_Only(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.Name = "!@#$^&%&:><;";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[2].ErrorMessage);
    }
    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_Name_InDatabase_With_UnderScore_InFront(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.Name = "_PTSTechnosofIndia";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[2].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_Name_InDatabase_With_UnderScore_InFront_AndBack(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.Name = "_PTSTechnosofIndia_";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[2].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_Name_InDatabase_With_Numbers_InFront(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.Name = "125PTSTechnosofIndia";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[3].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_Name_InDatabase_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.Name = "_125PTSTechnosofIndia_";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[2].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_Name_InDatabase_With_UnderScore_InFront_With_Numbers_InBack(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.Name = "_PTSTechnosofIndia456";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[2].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_Name_InDatabase_With_Numbers_Only(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);

        createDatabaseCommand.Name = "12345656778";
        createDatabaseCommand.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[3].ErrorMessage);
    }

    //DatabaseType

    //[Theory]
    //[AutoDatabaseData]
    //public async Task Verify_Create_DatabaseType_InDatabase_WithEmpty(CreateDatabaseCommand createDatabaseCommand)
    //{
    //    var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object);

    //    createDatabaseCommand.DatabaseType = "";
    //    createDatabaseCommand.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";

    //    var validateResult = await validator.ValidateAsync(createDatabaseCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Database.DatabaseTypeRequired, validateResult.Errors[2].ErrorMessage);
    //}

    //[Theory]
    //[AutoDatabaseData]
    //public async Task Verify_Create_DatabaseType_InDatabase_IsNull(CreateDatabaseCommand createDatabaseCommand)
    //{
    //    var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object);

    //    createDatabaseCommand.DatabaseType = null;
    //    createDatabaseCommand.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";

    //    var validateResult = await validator.ValidateAsync(createDatabaseCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Database.DatabaseTypeNotEmpty, validateResult.Errors[3].ErrorMessage);
    //}

    //[Theory]
    //[AutoDatabaseData]
    //public async Task Verify_Create_Valid_DatabaseType_InDatabase(CreateDatabaseCommand createDatabaseCommand)
    //{
    //    var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object);

    //    createDatabaseCommand.DatabaseType = "   SQL  ";
    //    createDatabaseCommand.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";

    //    var validateResult = await validator.ValidateAsync(createDatabaseCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Database.DatabaseTypeValid, validateResult.Errors[2].ErrorMessage);
    //}

    //ServerName

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_ServerName_InDatabase_WithEmpty(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = "";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseServerNameRequired, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_ServerName_InDatabase_IsNull(CreateDatabaseCommand createDatabaseCommand)
    {

        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = null;
        updatedDatabases.ServerId = "467eda06-774d-4669-bd50-41ce25784d7c";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseServerNameNotEmpty, validateResult.Errors[5].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_ServerName_InDatabase_MiniMumRange(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = "PR";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);

        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[2].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_ServerName_InDatabase_MaxiMumRange(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRS";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);

        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseNameValid, validateResult.Errors[2].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_ServerName_InDatabase(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = "   PRServer   ";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseServerNameValid, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_ServerName_InDatabase_DoubleSpace_InFront(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = "  DRServer";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseServerNameValid, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_ServerName_InDatabase_DoubleSpace_InBack(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = "PRServer  ";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseServerNameValid, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_ServerName_InDatabase_With_TripleSpace_InBetween(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = "DR   Server";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
       

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseServerNameValid, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_ServerName_InDatabase_With_SpecialCharacters_InFront(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = "@#%PR Server";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
       

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseServerNameValid, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_ServerName_InDatabase_With_SpecialCharacters_InBack(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = "DR Server$%^";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseServerNameValid, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_ServerName_InDatabase_With_SpecialCharacters_Only(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = "#$@%&#(*(^";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseServerNameValid, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_ServerName_InDatabase_With_UnderScore_InFront(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = "_PRServer";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
       

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseServerNameValid, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_ServerName_InDatabase_With_UnderScore_InBack(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = "PRServer_";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
       

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseServerNameValid, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_ServerName_InDatabase_With_UnderScoreAndNumbers_InFront(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = "_354PRServer";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseServerNameValid, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_ServerName_InDatabase_With_NumbersAndUnderScore_InBack(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = "PRServer344_";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
       

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseServerNameValid, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Valid_ServerName_InDatabase_With_Numbers_Only(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.ServerName = "**********";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
       

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseServerNameValid, validateResult.Errors[4].ErrorMessage);
    }


    //Properties

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Properties_InDatabase_WithEmpty(CreateDatabaseCommand createDatabaseCommand)
    {
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";

        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        updatedDatabases.Properties = "";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        
        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabasePropertiesRequired, validateResult.Errors[5].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_Create_Properties_InDatabase_IsNull(CreateDatabaseCommand createDatabaseCommand)
    {
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";

        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        
        updatedDatabases.Properties = null;
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";

        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server { 
             Properties=null
        };
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
        _mapperMock.Setup(m => m.Map<CreateDatabaseCommand>(updatedDatabases)).Returns(new CreateDatabaseCommand());

        
        
        var updatedJson = JsonSerializer.Serialize(updatedDatabases, new JsonSerializerOptions { WriteIndented = true });

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabasePropertiesNotEmpty, validateResult.Errors[6].ErrorMessage);
    }

    //LicenseKey

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_IsLicenseKey_InDatabase_WithEmpty(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.LicenseKey = "";
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
       

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseLicenseKeyRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDatabaseData]
    public async Task Verify_IsLicenseKey_InDatabase_IsNull(CreateDatabaseCommand createDatabaseCommand)
    {
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        var updatedDatabases = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        updatedDatabases.LicenseKey = null;
        updatedDatabases.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype = new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, sitetype, dbCount, index)).ReturnsAsync(dbLicenseCount);
       

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseLicenseKeyNotNullRequired, validateResult.Errors[1].ErrorMessage);
    }


    //DatabaseCountValidation

    [Theory]
    [AutoDatabaseData]
    public async Task Validate_LicenseKey_InDatabase_Exit_Maximum_limit(CreateDatabaseCommand createDatabaseCommand)
    {
        var db= new AutoFixture.Fixture().Create<CreateDatabaseCommand>();
        var prefixes = new List<string> { "BusinessServiceId", "BusinessServiceName", "CompanyId", "CreatedBy", "DatabaseType", "ExceptionMessage", "FormVersion", "LastModified", "LicenseId", "ServerId", "DatabaseTypeId", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";

        var updatedDatabases = RemovePrefixesFromObject(db, pattern,jsonPrefix);
        
        var validator = new CreateDatabaseCommandValidator(_mockDatabaseRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object);

        createDatabaseCommand.LicenseKey = "PONumberb5f266b0-66f5-49e8-a1d8-c44765588d93";
        createDatabaseCommand.ServerId = "8e58d5be-0aa7-4fc9-ae94-c83e8f9f8584";

        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(updatedDatabases.LicenseId)).ReturnsAsync(license);
        var server=new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(updatedDatabases.ServerId)).ReturnsAsync(server);
        var site = new  Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(server.SiteId)).ReturnsAsync(site);
        var sitetype= new Domain.Entities.SiteType();
        int dbCount = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(site.TypeId)).ReturnsAsync(sitetype);
        _mockDatabaseRepository.Setup(dp => dp.GetDatabaseCountByLicenseKey(updatedDatabases.LicenseId, It.IsAny<List<string>>())).ReturnsAsync(dbCount);
        int index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(site.ReferenceId)).ReturnsAsync(index);
        bool dbLicenseCount = true;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license,sitetype,dbCount, index)).ReturnsAsync(dbLicenseCount);
        _mapperMock.Setup(m => m.Map<CreateDatabaseCommand>(updatedDatabases)).Returns(new CreateDatabaseCommand());
        var updatedJson = JsonSerializer.Serialize(updatedDatabases, new JsonSerializerOptions { WriteIndented = true });

        var validateResult = await validator.ValidateAsync(updatedDatabases, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Database.DatabaseCountLimit, validateResult.Errors[1].ErrorMessage);
    }
}

