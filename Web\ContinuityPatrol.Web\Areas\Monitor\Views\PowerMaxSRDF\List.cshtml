﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title" title="Dell PowerMax">
            <i class="cp-monitoring"></i><span>PowerMax SRDF Detail Monitoring :<span id="infraName"></span></span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time :"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>
    </div>
    <div class="row g-2 mt-0">
        <div class="col-md-12 col-lg-12 col-xl-12 d-grid">
            <div class="card Card_Design_None h-100">
                <div class="card-header card-title">PowerMax SRDF Monitoring</div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0 noDataimg" style="table-layout:fixed">
                        <thead>
                            <tr>
                                <th title="Replication Details">Component</th>
                                <th class="text-primary">Primary Site Information</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="fw-semibold text-truncate" title="Site Name">
                                   Site Name
                                </td>
                                <td class="text-truncate"><span id="SiteName"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate " title="PowerMax IP,Port No">PowerMax IP,Port No</td>
                                <td class="text-truncate"><span id="PowermaxIPPort"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate " title="Source Array">
                                    Source Array
                                </td>
                                <td class="text-truncate"><span id="SourceArray"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate " title="SG Name">
                                    SG Name
                                </td>
                                <td class="text-truncate"><span id="StorageGroupName"></span> </td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate " title="Protected vCenter Site">
                                    Protected vCenter Site
                                </td>
                                <td class="text-truncate"><span id="ProtectedvCenterSite"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate " title="Protected Datastores">
                                    Protected Datastores
                                </td>
                                <td class="text-truncate"><span id="ProtectedDatastores"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate " title="Total Protected VM Count">
                                    Total Protected VM Count
                                </td>
                                <td class="text-truncate"><span id="TotalProtectedVMCount"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate " title="RDF Type(RDF State)">
                                   RDF Type(RDF State)
                                </td>
                                <td class="text-truncate"><span id="RDFTypeAndState"></span></td>
                            </tr>
                           
                        </tbody>

                    </table>
                </div>
            </div>
        </div>

        <div class="col-12 mt-0 pb-2">
            <div class="card Card_Design_None mb-2">
                <div class="card-header card-title d-flex align-items-center justify-content-between">
                    <span>Replication (Storage Array) Monitoring</span>
                </div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0" style="table-layout:fixed" id="tblReplication">
                        <thead></thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-12 mt-0 pb-2">
            <div class="card Card_Design_None mb-2">
                <div class="card-header card-title d-flex align-items-center justify-content-between">
                    <span>Replication (Storage Group-RDF Group) Monitoring</span>
                </div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0" style="table-layout:fixed" id="tblRDFGroup">
                        <thead></thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-12 mt-0 pb-2">
            <div class="card Card_Design_None mb-2">
                <div class="card-header card-title d-flex align-items-center justify-content-between">
                    <span>Replication (Storage Group-RDF Volume) Monitoring</span>
                </div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0" style="table-layout:fixed" id="tblRDFVolume">
                        <thead></thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-12 mt-0 pb-2">
            <div class="card Card_Design_None mb-2">
                <div class="card-header card-title d-flex align-items-center justify-content-between">
                    <span>Protected VMs Monitoring</span>
                </div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0" style="table-layout:fixed" id="tblProtectedVM">
                        <thead></thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/js/monitoring/MonitoringSolutionDiagram.js"></script>
<script src="~/js/monitoring/PowerMaxSRDF.js"></script>