﻿//using ContinuityPatrol.Configuration.Core.Contracts;
//using ContinuityPatrol.Shared.Core.Exceptions;

//namespace ContinuityPatrol.Configuration.Core.Features.UserInfraObject.Commands.Update
//{
//    public class UpdateUserInfraObjectCommandHandler : IRequestHandler<UpdateUserInfraObjectCommand, UpdateUserInfraObjectResponse>
//    {

//        private readonly IMapper _mapper;
//        private readonly IUserInfraObjectRepository _userInfraObjectRepository;

//        public UpdateUserInfraObjectCommandHandler(IMapper mapper, IUserInfraObjectRepository userInfraObjectRepository)
//        {
//            _mapper = mapper;
//            _userInfraObjectRepository = userInfraObjectRepository;
//        }

//        public async Task<UpdateUserInfraObjectResponse> Handle(UpdateUserInfraObjectCommand request, CancellationToken cancellationToken)
//        {
//            var eventToUpdate = await _userInfraObjectRepository.GetByReferenceIdAsync(request.Id);

//            if (eventToUpdate == null)
//            {
//                throw new NotFoundException(nameof(Domain.Entities.UserInfraObject), request.Id);
//            }
//            _mapper.Map(request, eventToUpdate, typeof(UpdateUserInfraObjectCommand), typeof(Domain.Entities.UserInfraObject));

//            await _userInfraObjectRepository.UpdateAsync(eventToUpdate);

//            var response = new UpdateUserInfraObjectResponse
//            {
//                Message = $" UserInfraObject '{eventToUpdate.UserName}' has been updated successfully",
//                UserInfraObjectId = eventToUpdate.Id

//            };
//            return response;
//        }
//    }
//}

