using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.BackUpLog.Commands.Create;
using ContinuityPatrol.Application.Features.BackUpLog.Commands.Delete;
using ContinuityPatrol.Application.Features.BackUpLog.Commands.Update;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetList;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BackUpLogModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class BackUpLogControllerTests : IClassFixture<BackUpLogFixture>
{
    private readonly BackUpLogFixture _backUpLogFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly BackUpLogsController _controller;

    public BackUpLogControllerTests(BackUpLogFixture backUpLogFixture)
    {
        _backUpLogFixture = backUpLogFixture;

        var testBuilder = new ControllerTestBuilder<BackUpLogsController>();
        _controller = testBuilder.CreateController(
            _ => new BackUpLogsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetBackUpLogs_ReturnsExpectedList()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
 

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBackUpLogListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_backUpLogFixture.BackUpLogListVm);

        // Act
        var result = await _controller.GetBackUpLogs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var backUpLogs = Assert.IsAssignableFrom<List<BackUpLogListVm>>(okResult.Value);
        Assert.Equal(3, backUpLogs.Count);
    }

    [Fact]
    public async Task GetBackUpLogs_ReturnsEmptyList_WhenNoBackUpLogsExist()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
     

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBackUpLogListQuery>(), default))
            .ReturnsAsync(new List<BackUpLogListVm>());

        // Act
        var result = await _controller.GetBackUpLogs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var backUpLogs = Assert.IsAssignableFrom<List<BackUpLogListVm>>(okResult.Value);
        Assert.Empty(backUpLogs);
    }

    [Fact]
    public async Task GetBackUpLogById_ReturnsBackUpLog_WhenIdIsValid()
    {
        // Arrange
        var backUpLogId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBackUpLogDetailQuery>(q => q.Id == backUpLogId), default))
            .ReturnsAsync(_backUpLogFixture.BackUpLogDetailVm);

        // Act
        var result = await _controller.GetBackUpLogById(backUpLogId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var backUpLog = Assert.IsType<BackUpLogDetailVm>(okResult.Value);
        Assert.NotNull(backUpLog);
    }

    [Fact]
    public async Task GetBackUpLogById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetBackUpLogById("invalid-guid"));
    }

    [Fact]
    public async Task CreateBackUpLog_Returns201Created()
    {
        // Arrange
        var command = _backUpLogFixture.CreateBackUpLogCommand;
        var expectedMessage = $"BackUpLog '{command.DatabaseName}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBackUpLogResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBackUpLog(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBackUpLogResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBackUpLog_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"BackUpLog '{_backUpLogFixture.UpdateBackUpLogCommand.DatabaseName}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateBackUpLogCommand>(), default))
            .ReturnsAsync(new UpdateBackUpLogResponse
            {
                Message = expectedMessage,
                Id = _backUpLogFixture.UpdateBackUpLogCommand.Id
            });

        // Act
        var result = await _controller.UpdateBackUpLog(_backUpLogFixture.UpdateBackUpLogCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBackUpLogResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteBackUpLog_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "BackUpLog 'Test Log' has been deleted successfully!.";
        var backUpLogId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBackUpLogCommand>(c => c.Id == backUpLogId), default))
            .ReturnsAsync(new DeleteBackUpLogResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteBackUpLog(backUpLogId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBackUpLogResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetPaginatedBackUpLogs_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetBackUpLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _backUpLogFixture.BackUpLogListVm;
        var expectedPaginatedResult = PaginatedResult<BackUpLogListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBackUpLogPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedBackUpLogs(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<BackUpLogListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<BackUpLogListVm>>(okResult.Value);

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task IsBackUpLogNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBackUpLogNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsBackUpLogNameExist("ExistingLog", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsBackUpLogNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetBackUpLogNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsBackUpLogNameExist("NewLog", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }
}
