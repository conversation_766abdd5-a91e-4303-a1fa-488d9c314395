﻿let noDataSolutionDiagramMonitor = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'
let noDataImage1 = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">'
let noData1 = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
let noPluggable1 = '<div class="NoData text-center p-2 d-grid justify-content-center">' +
    '<img src="/img/isomatric/nodatalag.svg" class="mx-auto" />' +
    '<span class="text-danger">' +
    'Pluggable Databases not configured.' +
    '</span>' +
    '</div>';   

let infraObject = sessionStorage.getItem("infraobjectId");
//let moniterType = sessionStorage.getItem("moniterType");
let moniterStatus = sessionStorage.getItem("moniterStatus")

$(document).on('change', '#clusterDetails', function () {
    mode = $('#clusterDetails').find(':selected').text();
    monitoringSolution(infraObjectId, mode)
    debugger
});
async function monitoringSolution(infraObject, mode) {
    debugger
    $.ajax({
        url: "/Monitor/Monitoring/GetInfraObjectDetailsById",
        method: 'GET',
        data: {
            infraObjectId: infraObject,
        },
        dataType: 'json',
        async: true,
        success: function (res) {
            const value = res.data;
            debugger
            let moniterType = mode
            let modeValue = mode;
            let prServer = "";
            let prDB = "";
            let drServer = "";
            let drDB = "";
            let drOperation = checkValue(value?.drOperationStatus)
            let replicationType = checkValue(value?.replicationCategoryType)
            let replicationTypemonitor = checkValue(value?.replicationTypeName)
            let infrastate = checkValue(value?.state)

            am4core.useTheme(am4themes_animated);
            am4core.options.autoSetClassName = true;
            // Create chart
            var chart = am4core.create("Solution_Diagram", am4plugins_forceDirected.ForceDirectedTree);

            if (chart.logo) {
                chart.logo.disabled = true;
            }
            // Create series
            var series = chart.series.push(
                new am4plugins_forceDirected.ForceDirectedSeries()
            );

            if (moniterType === null) {
                xValueParent = am4core.percent(10);
                xValueChild = am4core.percent(30);
                xValueSubChild = am4core.percent(70);
                xValueLastChild = am4core.percent(90);

            }

            let image;

            switch (true) {
                case moniterType?.toLowerCase()?.includes("goldengate") && value?.subType?.toLowerCase()?.includes("oracle"):
                    image = "/img/charts_img/DataCenter/oracle golden gate.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("azurestorage"):
                    image = "/img/charts_img/DataCenter/Azure_storage.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("azuremssqlpaas"):
                    image = "/img/charts_img/DataCenter/Azure_MSSQL.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("azuremysqlpaas"):
                    image = "/img/charts_img/DataCenter/Azure_Mysql.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("recoverpointforvm"):
                    image = "/img/charts_img/DataCenter/Azure_RecoverPointVm.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("oracle") || (value?.subType)?.toLowerCase()?.includes("oracle"):
                case moniterType?.toLowerCase()?.includes("oracle_dataguard") || (value?.subType)?.toLowerCase()?.includes("oracle_dataguard"):
                case moniterType?.toLowerCase()?.includes("rac") || (value?.subType)?.toLowerCase()?.includes("oraclerac") || (value?.subType)?.toLowerCase()?.includes("rac"):
                    image = "/img/charts_img/DataCenter/oracle.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("mysql") || (value?.subType)?.toLowerCase()?.includes("mysql"):
                    image = "/img/charts_img/DataCenter/my_sql.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("mssql") || (value?.subType)?.toLowerCase()?.includes("mssqlalwayson") || (value?.subType)?.toLowerCase()?.includes("always") || (value?.subType)?.toLowerCase()?.includes("mssql"):
                    image = "/img/charts_img/DataCenter/MSSQL.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("postgres") || (value?.subType)?.toLowerCase()?.includes("postgres"):
                    image = "/img/charts_img/DataCenter/Postgresql.svg";
                    break;
                case moniterType?.toLowerCase() === "svc" || (value?.name)?.toLowerCase()?.includes("svc"):
                    image = "/img/charts_img/DataCenter/IBM.svg";
                    break;
                case moniterType?.toLowerCase() === "mongodb" || (value?.subType)?.toLowerCase()?.includes("mongodb"):
                    image = "/img/Database_Icon/cp_mongo_db.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("db2hadr") || (value?.subType)?.toLowerCase()?.includes("db2hadr") || (value?.subType)?.toLowerCase()?.includes("ibm"):
                    image = "/img/charts_img/DataCenter/IBM.svg";
                    break;
                case moniterType?.toLowerCase() === "mssqldbmirroring" || (value?.subType)?.toLowerCase()?.includes("mirror"):
                    image = "/img/charts_img/DataCenter/MSSQL.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("hyperv") || (value?.subType)?.toLowerCase()?.includes("hyperv"):
                    image = "/img/charts_img/DataCenter/windows-1.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("rsync") || (value?.subType)?.toLowerCase()?.includes("rsync"):
                    image = "/img/charts_img/DataCenter/rsync.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("robocopy") || (value?.subType)?.toLowerCase()?.includes("robocopy"):
                    image = "/img/charts_img/DataCenter/RoboCopy.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("as400") || (value?.subType)?.toLowerCase()?.includes("as400"):
                    image = "/img/charts_img/DataCenter/IBM-AIX.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("netapp") || (value?.subType)?.toLowerCase()?.includes("netapp"):
                    image = "/img/DB-Logo/cp_netapp.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("hp3par") || (value?.subType)?.toLowerCase()?.includes("hp3par"):
                    image = "/img/charts_img/DataCenter/3PAR_logo.svg";
                    break;
                case moniterType?.toLowerCase()?.includes("srm") || (value?.subType)?.toLowerCase()?.includes("srm"):
                    image = "/img/charts_img/DataCenter/srm_vmware.svg";
                    break;

                case moniterType?.toLowerCase() === "applicationnoreplication":
                case replicationType?.toLowerCase() === "application - no replication":
                case replicationType?.toLowerCase() === "application - no - replication":
                case replicationType?.toLowerCase() === "application-no replication":
                case replicationType?.toLowerCase() === "application-no-replication":

                    image = "/img/charts_img/DataCenter/replication-off.svg";
                    break;
                default:
                    image = "/img/charts_img/DataCenter/defaultapplication.svg";
                    break;
            }
            var xValueParent, xValueChild, xValueSubChild, xValueLastChild;

            switch (moniterType?.toLowerCase()) {

                case "mssqlalwayson":
                    xValueParent = am4core.percent(20);
                    xValueChild = am4core.percent(35);
                    xValueSubChild = am4core.percent(65);
                    xValueLastChild = am4core.percent(80);
                    break;
                case "mssqlnls":
                    xValueParent = am4core.percent(10);
                    xValueChild = am4core.percent(30);
                    xValueSubChild = am4core.percent(70);
                    xValueLastChild = am4core.percent(90);
                    break;
                case "oracle":
                    xValueParent = am4core.percent(26);
                    xValueChild = am4core.percent(38);
                    xValueSubChild = am4core.percent(63);
                    xValueLastChild = am4core.percent(75);
                    break;
                case "mysql":
                    xValueParent = am4core.percent(24);
                    xValueChild = am4core.percent(36);
                    xValueSubChild = am4core.percent(64);
                    xValueLastChild = am4core.percent(76);
                    break;
                case "oraclerac":
                    xValueParent = am4core.percent(12);
                    xValueChild = am4core.percent(30);
                    xValueSubChild = am4core.percent(70);
                    xValueLastChild = am4core.percent(87);
                    break;
                case "postgres":
                case "postgresql":
                    xValueParent = am4core.percent(10);
                    xValueChild = am4core.percent(30);
                    xValueSubChild = am4core.percent(70);
                    xValueLastChild = am4core.percent(90);
                    break;
                case "hyperv":
                    xValueParent = am4core.percent(10);
                    xValueChild = am4core.percent(30);
                    xValueSubChild = am4core.percent(70);
                    xValueLastChild = am4core.percent(90);
                    break;
                case "db2hadr":
                    xValueParent = am4core.percent(26);
                    xValueChild = am4core.percent(38);
                    xValueSubChild = am4core.percent(63);
                    xValueLastChild = am4core.percent(75);
                case "robocopy":
                    xValueParent = am4core.percent(26);
                    xValueChild = am4core.percent(38);
                    xValueSubChild = am4core.percent(63);
                    xValueLastChild = am4core.percent(75);
                    break;
                case "rsyncappreplication":
                case "rsync":
                    xValueParent = am4core.percent(26);
                    xValueChild = am4core.percent(38);
                    xValueSubChild = am4core.percent(63);
                    xValueLastChild = am4core.percent(75);
                    break;


            }
            let nodePro = JSON.parse(value?.nodeProperties);
            if (moniterType === "MssqlAlwaysOn" || moniterType === "Postgres" || moniterType === "Mysql" || moniterType === "Oracle" || moniterType === "MssqlNLS" || moniterType === "HyperV" || moniterType === "DB2HADR" || moniterType === "RoboCopy" || moniterType === "RSyncAppReplication" || value?.subType?.toLowerCase()?.includes("ibm") || value?.subType?.toLowerCase()?.includes("mssqlnls")
                || (nodePro?.length < 0 || nodePro === null || nodePro === "" && value?.subType?.toLowerCase()?.includes("oracle")) || value?.subType?.toLowerCase()?.includes("mysql") || value?.subType?.toLowerCase()?.includes("mssqlalwayson") || value?.subType?.toLowerCase()?.includes("postgres") || value?.subType?.toLowerCase()?.includes("hyperv")) {

                var pripaddress = checkValue(value?.serverDto[0]?.ipAddress)
                var dripaddress = checkValue(value?.serverDto[1]?.ipAddress)
                var prdatabase = checkValue(value?.databaseDto[0]?.sid)
                var drdatabase = checkValue(value?.databaseDto[1]?.sid)
                var prOstype = checkValue(value?.serverDto[0]?.osType)
                var drOstype = checkValue(value?.serverDto[1]?.osType)
                var prOslocation = checkValue(value?.serverDto[0]?.location)
                var drOslocation = checkValue(value?.serverDto[1]?.location)
                var prhost = checkValue(value?.serverDto[0]?.hostName)
                var drhost = checkValue(value?.serverDto[1]?.hostName)
                var prStatus = checkValue(value?.serverDto[0]?.status)
                var drStatus = checkValue(value?.serverDto[1]?.status)
                var prdbStatus = checkValue(value?.serverDto[0]?.status?.toLowerCase() != "down" ? value?.databaseDto[0]?.status : value?.serverDto[0]?.status)
                var drdbStatus = checkValue(value?.serverDto[1]?.status?.toLowerCase() != "down" ? value?.databaseDto[1]?.status : value?.serverDto[1]?.status)
                var prversion = checkValue(value?.databaseDto[0]?.version)
                var drversion = checkValue(value?.databaseDto[1]?.version)
                var prdatabasetype = checkValue(value?.databaseDto[0]?.databaseType)
                var drdatabasetype = checkValue(value?.databaseDto[1]?.databaseType)
                var prroleType = checkValue(value.serverDto[0]?.roleType)
                var drroleType = checkValue(value.serverDto[1]?.roleType)
                prServer = checkValue(value?.serverDto[0]?.status);
                prDB = checkValue(value?.serverDto[0]?.status?.toLowerCase() != "down" ? value?.databaseDto[0]?.status : value?.serverDto[0]?.status);
                drServer = checkValue(value?.serverDto[1]?.status)
                drDB = checkValue(value?.serverDto[1]?.status?.toLowerCase() != "down" ? value?.databaseDto[1]?.status : value?.serverDto[1]?.status);

            }

            else if (moniterType?.toLowerCase() === "oraclerac" || value?.name?.toLowerCase()?.includes("oracle_rac") || value?.name?.toLowerCase()?.includes("oraclerac") || value?.name?.toLowerCase()?.includes("rac")) {

                let nodeProperties = JSON.parse(value.nodeProperties);
                let nodeNamesArray = [];

                nodeNamesArray = nodeProperties.map(node => {
                    return {
                        prNodeName: node.prNodeName,
                        drNodeName: node.drNodeName
                    };
                });




                modeValue = modeValue === "" ? `${nodeNamesArray[0].prNodeName} - ${nodeNamesArray[0].drNodeName}` : modeValue;



                const nodeName = modeValue.split(' - ')[0];
                const nodeNames = modeValue.split(' - ')[1];


                const databaseSolution = value.databaseDto.filter((t) => t.nodeName === nodeName);
                const databaseSolutiondata = value.databaseDto.filter((t) => t.nodeName === nodeNames);
                const serverSolution = value.serverDto.filter((t) => t.nodeName === nodeName);
                const serverSolutiondata = value.serverDto.filter((t) => t.nodeName === nodeNames);


                const checkValue = (value) => value !== undefined ? value : "NA";

                var prdatabase = checkValue(databaseSolution[0]?.sid);
                var drdatabase = checkValue(databaseSolutiondata[0]?.sid);
                var pripaddress = checkValue(serverSolution[0]?.ipAddress);
                var dripaddress = checkValue(serverSolutiondata[0]?.ipAddress);
                var prdbStatus = checkValue(serverSolution[0]?.status?.toLowerCase() != "down" ? databaseSolution[0]?.status : serverSolution[0]?.status);
                var drdbStatus = checkValue(serverSolutiondata[0]?.status?.toLowerCase() != "down" ? databaseSolutiondata[0]?.status : serverSolutiondata[0]?.status);
                var prversion = checkValue(serverSolution[0]?.version);
                var drversion = checkValue(serverSolutiondata[0]?.version);
                var prdatabasetype = checkValue(databaseSolution[0]?.databaseType);
                var drdatabasetype = checkValue(databaseSolutiondata[0]?.databaseType);
                var prOstype = checkValue(serverSolution[0]?.osType);
                var drOstype = checkValue(serverSolutiondata[0]?.osType);
                var prOslocation = checkValue(serverSolution[0]?.location);
                var drOslocation = checkValue(serverSolutiondata[0]?.location);
                var prhost = checkValue(serverSolution[0]?.hostName);
                var drhost = checkValue(serverSolutiondata[0]?.hostName);
                var prStatus = checkValue(serverSolution[0]?.status);
                var drStatus = checkValue(serverSolutiondata[0]?.status);
                var prroleType = checkValue(serverSolution[0]?.roleType)
                var drroleType = checkValue(serverSolutiondata[0]?.roleType)
                prServer = checkValue(serverSolution[0]?.status);
                prDB = checkValue(serverSolution[0]?.status?.toLowerCase() != "down" ? databaseSolution[0]?.status : serverSolution[0]?.status);
                drServer = checkValue(serverSolutiondata[0]?.status);
                drDB = checkValue(serverSolutiondata[0]?.status?.toLowerCase() != "down" ? databaseSolutiondata[0]?.status : serverSolutiondata[0]?.status);

            }
            else {
                var pripaddress = checkValue(value?.serverDto[0]?.ipAddress)
                var dripaddress = checkValue(value?.serverDto[1]?.ipAddress)
                var prdatabase = checkValue(value?.databaseDto[0]?.sid)
                var drdatabase = checkValue(value?.databaseDto[1]?.sid)
                var prOstype = checkValue(value?.serverDto[0]?.osType)
                var drOstype = checkValue(value?.serverDto[1]?.osType)
                var prOslocation = checkValue(value?.serverDto[0]?.location)
                var drOslocation = checkValue(value?.serverDto[1]?.location)
                var prhost = checkValue(value?.serverDto[0]?.hostName)
                var drhost = checkValue(value?.serverDto[1]?.hostName)
                var prStatus = checkValue(value?.serverDto[0]?.status)
                var drStatus = checkValue(value?.serverDto[1]?.status)
                var prdbStatus = checkValue(value?.serverDto[0]?.status?.toLowerCase() != "down" ? value?.databaseDto[0]?.status : value?.serverDto[0]?.status)
                var drdbStatus = checkValue(value?.serverDto[1]?.status?.toLowerCase() != "down" ? value?.databaseDto[1]?.status : value?.serverDto[1]?.status)
                var prversion = checkValue(value?.databaseDto[0]?.version)
                var drversion = checkValue(value?.databaseDto[1]?.version)
                var prdatabasetype = checkValue(value?.databaseDto[0]?.databaseType)
                var drdatabasetype = checkValue(value?.databaseDto[1]?.databaseType)
                var prroleType = checkValue(value?.serverDto[0]?.roleType)
                var drroleType = checkValue(value?.serverDto[1]?.roleType)
                prServer = checkValue(value?.serverDto[0]?.status);
                prDB = checkValue(value?.serverDto[0]?.status?.toLowerCase() != "down" ? value?.databaseDto[0]?.status : value?.serverDto[0]?.status);
                drServer = checkValue(value?.serverDto[1]?.status)
                drDB = checkValue(value?.serverDto[1]?.status?.toLowerCase() != "down" ? value?.databaseDto[1]?.status : value?.serverDto[1]?.status)
            }
            setTimeout(() => {

                if (moniterType === "" || moniterType === undefined || value?.state?.toLowerCase() === "maintenance" || value?.serverDto[0]?.status?.toLowerCase() !== "up" || value?.serverDto[1]?.status?.toLowerCase() !== "up" || value?.databaseDto[0]?.status?.toLowerCase() !== "up" || value?.databaseDto[1]?.status?.toLowerCase() !== "up") {
                    $('.amcharts-ForceDirectedLink-group').css('animation', 'am-dashesIn 1s  linear infinite')

                }
                else if (moniterStatus == 2) {

                    $('.amcharts-ForceDirectedLink-group').css('animation', 'am-moving-dashesIn 1s reverse linear infinite')
                }
                else {
                    $('.amcharts-ForceDirectedLink-group').css('animation', 'am-moving-dashesIn 1s linear infinite')
                }
            }, 300)
            let databaseImage;

            if (prdatabasetype || drdatabasetype) {
                const databaseType = (prdatabasetype || drdatabasetype)?.toLowerCase();

                if (databaseType?.includes("oracle")) {
                    databaseImage = "/img/charts_img/DataCenter/oracle.svg";
                } else if (databaseType?.includes("mysql")) {
                    databaseImage = "/img/charts_img/DataCenter/my_sql.svg";
                } else if (databaseType?.includes("mssql") || databaseType?.includes("always") || databaseType?.includes("ms-sql")) {
                    databaseImage = "/img/charts_img/DataCenter/MSSQL.svg";
                } else if (databaseType?.includes("postgres")) {
                    databaseImage = "/img/charts_img/DataCenter/Postgresql.svg";
                } else if (databaseType?.includes("ibm")) {
                    databaseImage = "/img/charts_img/DataCenter/IBM.svg";
                }
            }
            chart.colors.list = [am4core.color("#ff9c0d"), am4core.color("#40c200")];

            series.data = [{

                fixed: true,
                x: xValueParent,
                y: am4core.percent(60),
                tagimage: prroleType?.toLowerCase() === "database" ? (prDB?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : prDB?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : prDB?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg") : "",
                ip: prdatabase,
                status: prdbStatus,
                type: prroleType?.toLowerCase() === "database" ? prdatabasetype : replicationType,
                version: prversion,
                value: 10,
                serverimage: databaseImage,
                dbimage: prroleType?.toLowerCase() === "database" ? "/img/charts_img/DataCenter/database.svg" : "/img/charts_img/DataCenter/ApplicationRe.svg",
                children: [{
                    name: "Primary",
                    fixed: true,
                    x: xValueChild,
                    y: am4core.percent(60),
                    tagimage: (prServer?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : prServer?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : prServer?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg"),
                    ip: pripaddress,
                    hostname: prhost,
                    location: prOslocation,
                    os: prOstype,
                    prstatus: prdbStatus,
                    prostatus: prStatus,
                    value: 10,
                    image: "/img/charts_img/DataCenter/PR.svg",
                    activeicon: moniterStatus == 2 ? "" : "/img/charts_img/DataCenter/crown_1.svg",
                    serverimage: prOstype?.toLowerCase() === "windows" ? "/img/charts_img/DataCenter/windows-1.svg" : prOstype?.toLowerCase() === "linux" ? "/img/charts_img/DataCenter/linux.svg" : "/img/charts_img/DataCenter/windows-1.svg",
                    children: [
                        {
                            dbname: replicationTypemonitor,
                            status: prServer,
                            fixed: true,
                            x: am4core.percent(50),
                            y: am4core.percent(60),
                            mainimage: (infrastate?.toLowerCase() !== "active" ? "/img/charts_img/DataCenter/maintenance.svg" : null),
                            value: 10,
                            image: image,
                            children: [
                                {
                                    name: "DR",
                                    fixed: true,
                                    x: xValueSubChild,
                                    y: am4core.percent(60),
                                    tagimage: (drServer?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : drServer?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : drServer?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg"),
                                    ip: dripaddress,
                                    hostname: drhost,
                                    os: drOstype,
                                    location: drOslocation,
                                    status: drStatus,
                                    value: 10,
                                    activeicon: moniterStatus == 2 ? "/img/charts_img/DataCenter/crown_1.svg" : "",
                                    serverimage: drOstype?.toLowerCase() === "windows" ? "/img/charts_img/DataCenter/windows-1.svg" : drOstype?.toLowerCase() === "linux" ? "/img/charts_img/DataCenter/linux.svg" : "/img/charts_img/DataCenter/windows-1.svg",
                                    image: "/img/charts_img/DataCenter/DR.svg",

                                    children: [{

                                        fixed: true,
                                        x: xValueLastChild,
                                        y: am4core.percent(60),
                                        tagimage: prroleType?.toLowerCase() === "database" ? (drDB?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : drDB?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : drDB?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg") : "",
                                        ip: drdatabase,
                                        status: drdbStatus,
                                        type: drroleType?.toLowerCase() === "database" ? drdatabasetype : replicationType,
                                        version: drversion,
                                        value: 10,
                                        serverimage: databaseImage,
                                        dbimage: drroleType?.toLowerCase() === "database" ? "/img/charts_img/DataCenter/database.svg" : "/img/charts_img/DataCenter/ApplicationRe.svg",
                                    }],
                                }
                            ],
                        }
                    ]
                }]

            },];
            // Set up data fields
            series.dataFields.value = "value";
            series.dataFields.fixed = "fixed";
            series.dataFields.dbname = "dbname";
            series.dataFields.activeicon = "activeicon";
            series.dataFields.serverimage = "serverimage";
            series.dataFields.name = "name";
            series.dataFields.ip = "ip";
            series.dataFields.Connection = "Connection";
            (series.dataFields.Service = "Service"),
                (series.dataFields.host = "host"),
                (series.dataFields.Port = "Port"),
                (series.dataFields.id = "id");
            series.dataFields.children = "children";
            series.dataFields.tag = "tag";
            series.dataFields.linkWith = "link";
            series.links.template.propertyFields.userClassName = "lineClass"
            series.links.template.adapter.add("stroke", function (stroke, target) {
                if (target.dataItem && target.dataItem.dataContext) {
                    const sourceNode = target.dataItem.dataContext;

                    const sourceStatus = sourceNode.status;
                    const prStatusSource = sourceNode.prstatus;
                    // Set individual stroke colors 
                    if (sourceStatus) {

                        if (moniterType === '' || moniterType === undefined) {
                            return am4core.color("grey");
                        } else {
                            if (sourceStatus?.toLowerCase() === "up") {
                                return am4core.color("#41c200");
                            } else if (sourceStatus?.toLowerCase() === "down") {
                                return am4core.color("red");
                            } else if (sourceStatus?.toLowerCase() === "pending") {
                                return am4core.color("grey");
                            } else {
                                return am4core.color("grey");
                            }
                        }

                    }
                    if (prStatusSource) {
                        if (moniterType === '' || moniterType === undefined) {
                            return am4core.color("grey");
                        } else {
                            if (prStatusSource?.toLowerCase() === "up") {
                                return am4core.color("#41c200");
                            } else if (prStatusSource?.toLowerCase() === "down") {
                                return am4core.color("red");
                            } else if (prStatusSource?.toLowerCase() === "pending") {
                                return am4core.color("grey");
                            } else {
                                return am4core.color("grey");
                            }
                        }

                    }
                }
                return stroke;
            });



            series.dataFields.id = "name";
            series.manyBodyStrength = -18;
            // Add labels
            series.nodes.template.label.text = "{dbname}\n{ip}";
            series.nodes.template.label.valign = "bottom";
            series.nodes.template.label.truncate = true;
            series.nodes.template.label.hideOversized = true;
            series.nodes.template.label.maxWidth = 100;
            series.nodes.template.label.fill = am4core.color("#000");
            series.nodes.template.label.dy = -2;

            series.nodes.template.adapter.add("tooltipText", function (text, target) {
                if (!target.dataItem) return "";
                const dataItem = target.dataItem.dataContext;

                if (target.dataItem.name === "Primary" || target.dataItem.name === "DR") {
                    const hostText = dataItem.hostname ?? "NA";
                    const osText = dataItem.os ?? "NA";
                    const osLocation = dataItem.location ?? "NA";
                    const statusText = (target.dataItem.name === "DR") ? (dataItem.status ?? "NA") : (dataItem.prostatus ?? "NA");

                    return `[bold; #0479ff;]Hostname: [/]${hostText}\n[/] [bold; #0479ff;]Location: [/]${osLocation}\n[/] [bold; #0479ff;]OS: [/]${osText}\n[/] [bold; #0479ff;]Status: [/]${statusText}`;
                } else if (dataItem.type) {
                    const typeText = dataItem.type ?? "NA";
                    const versionText = dataItem.version ?? "NA";
                    const statusText = dataItem.status ?? "NA";

                    return `[bold; #0479ff;]Type: [/]${typeText}\n[/] [bold; #0479ff;]Version: [/]${versionText}\n[/] [bold; #0479ff;]Status: [/]${statusText}`;
                } else if (replicationTypemonitor) {

                    const replicaText = dataItem.dbname ?? "NA";
                    return `[bold; #0479ff;]${replicaText}`;
                }

                return "";
            });

            series.fontSize = 11;
            series.minRadius = 35;
            series.maxRadius = 35;

            series.tooltip.autoTextColor = false;
            series.tooltip.getFillFromObject = false;
            series.tooltip.label.fill = am4core.color("#1A1A1A");
            series.tooltip.label.background.fill = am4core.color("#fff");

            series.links.template.strokeWidth = 2;
            //series.links.template.strokeDasharray = "5,3";
            series.nodes.template.circle.strokeWidth = 0;
            series.nodes.template.circle.disabled = true;
            series.nodes.template.outerCircle.disabled = true;

            series.dataFields.fixed = "fixed";
            series.nodes.template.propertyFields.x = "x";
            series.nodes.template.propertyFields.y = "y";

            // Add tag
            var tag = series.nodes.template.createChild(am4core.Label);
            tag.text = "{tag}";
            tag.strokeWidth = 0;
            // tag = am4core.percent(50)
            tag.fill = am4core.color("#fff");
            tag.background = new am4core.RoundedRectangle();
            tag.background.cornerRadius(10, 10, 10, 10);
            tag.background.fill = am4core.color("#41c200");
            tag.padding(2, 4, 2, 4);
            tag.zIndex = 10;
            tag.width = '8px';
            tag.height = '10px';
            tag.fontSize = 8;
            tag.verticalCenter = "top";
            tag.textAlign = 'middle'
            tag.horizontalCenter = "left";

            tag.adapter.add("dy", function (dy, target) {
                return -target.parent.circle.radius + 40;
            });
            tag.adapter.add("dx", function (dy, target) {
                return target.parent.circle.radius - 65;
            });
            tag.adapter.add("textOutput", function (text, target) {
                if (text === "") {
                    target.disabled = true;
                }
                return text;
            });

            tag.adapter.add("fill", function (fill, target) {
                if (target.dataItem && target.dataItem.tag == "✔") {
                    return am4core.color("#fff");
                } else {
                    return fill;
                }
            });
            tag.background.adapter.add("fill", function (fill, target) {
                if (target.dataItem && target.dataItem.tag == "✖") {
                    return am4core.color("red");
                } else {
                    return fill;
                }
            });
            tag.background.adapter.add("fill", function (fill, target) {
                if (target.dataItem && target.dataItem.tag == "⟳") {
                    return am4core.color("#FF9632");
                } else {
                    return fill;
                }
            });
            // Change the padding values
            chart.padding(-15, -15, -15, -15)

            // Configure icons
            var icon = series.nodes.template.createChild(am4core.Image);
            icon.propertyFields.href = "image";
            icon.horizontalCenter = "middle";
            icon.verticalCenter = "middle";
            icon.width = 55;
            icon.height = 55;


            // Configure icons
            var dbicon = series.nodes.template.createChild(am4core.Image);
            dbicon.propertyFields.href = "dbimage";
            dbicon.horizontalCenter = "middle";
            dbicon.verticalCenter = "middle";
            dbicon.width = 35;
            dbicon.height = 35;
            series.centerStrength = 0.5;
            var tagicon = series.nodes.template.createChild(am4core.Image);
            tagicon.strokeWidth = 0;
            tagicon.propertyFields.href = "tagimage";
            tagicon.dy = 0;
            tagicon.dx = -30
            tagicon.zIndex = 10;
            tagicon.width = '15px';
            tagicon.height = '15px';
            tagicon.verticalCenter = "top";
            tagicon.textAlign = 'center'
            tagicon.horizontalCenter = "left";

            var tagicon = series.nodes.template.createChild(am4core.Image);
            tagicon.text = "{tagicon}";
            tagicon.strokeWidth = 0;
            tagicon.propertyFields.href = "mainimage";
            tagicon.dy = 0;
            tagicon.dx = -30
            tagicon.zIndex = 10;
            tagicon.width = '15px';
            tagicon.height = '15px';
            tagicon.verticalCenter = "top";
            tagicon.textAlign = 'center'
            tagicon.horizontalCenter = "left";

            var activetag = series.nodes.template.createChild(am4core.Label);
            //activetag.text = "{activetag}";
            activetag.strokeWidth = 0;
            // tag = am4core.percent(50)
            //activetag.fill = am4core.color("blue");
            activetag.background = new am4core.RoundedRectangle();
            activetag.background.cornerRadius(10, 10, 10, 10);
            activetag.background.fill = am4core.color("#fff");
            activetag.padding(2, 5, 2, 5);
            activetag.zIndex = 10;
            activetag.width = '10px';
            activetag.height = '10px';
            activetag.verticalCenter = "top";
            activetag.textAlign = 'center'
            activetag.horizontalCenter = "left";
            activetag.adapter.add("dy", function (dy, target) {
                return -target.parent.circle.radius + -8;
            });
            activetag.adapter.add("dx", function (dy, target) {
                return target.parent.circle.radius - 40;
            });

            activetag.background.adapter.add("fill", function (fill, target) {
                if (target.dataItem && target.dataItem.activetag == true) {
                    return am4core.color("#41c200");
                } else {
                    return fill;
                }
            });

            var icons = series.nodes.template.createChild(am4core.Image);
            icons.propertyFields.href = "serverimage";
            icons.horizontalCenter = "middle";
            icons.verticalCenter = "middle";
            icons.width = 35;
            icons.height = 35;
            icons.dy = 20;
            icons.dx = 15
            var icons = series.nodes.template.createChild(am4core.Image);
            icons.propertyFields.href = "activeicon";
            icons.horizontalCenter = "middle";
            icons.verticalCenter = "middle";
            icons.width = 15;
            icons.height = 15;
            icons.dy = -40;
            icons.dx = 0
        }

    });
    function checkValue(value) {
        return (value !== null && value !== '' && value !== undefined) ? value : "NA";
    }

}



