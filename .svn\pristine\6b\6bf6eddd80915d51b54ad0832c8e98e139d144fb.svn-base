using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class AdPasswordExpireRepositoryMocks
{
    public static Mock<IAdPasswordExpireRepository> CreateAdPasswordExpireRepository(List<AdPasswordExpire> adPasswordExpires)
    {
        var mockAdPasswordExpireRepository = new Mock<IAdPasswordExpireRepository>();

        mockAdPasswordExpireRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(adPasswordExpires);

        mockAdPasswordExpireRepository.Setup(repo => repo.AddAsync(It.IsAny<AdPasswordExpire>())).ReturnsAsync(
            (AdPasswordExpire adPasswordExpire) =>
            {
                adPasswordExpire.Id = new Fixture().Create<int>();
                adPasswordExpire.ReferenceId = new Fixture().Create<Guid>().ToString();
                adPasswordExpires.Add(adPasswordExpire);
                return adPasswordExpire;
            });

        mockAdPasswordExpireRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AdPasswordExpire>()));
        // .Returns(Task.CompletedTask);

        mockAdPasswordExpireRepository.Setup(repo => repo.DeleteAsync(It.IsAny<AdPasswordExpire>()));
           // .Returns(Task.CompletedTask);

        mockAdPasswordExpireRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => adPasswordExpires.FirstOrDefault(x => x.ReferenceId == id));

        mockAdPasswordExpireRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);

        return mockAdPasswordExpireRepository;
    }

    public static Mock<IAdPasswordExpireRepository> CreateUpdateAdPasswordExpireRepository(List<AdPasswordExpire> adPasswordExpires)
    {
        var mockAdPasswordExpireRepository = new Mock<IAdPasswordExpireRepository>();

        mockAdPasswordExpireRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(adPasswordExpires);

        mockAdPasswordExpireRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => adPasswordExpires.FirstOrDefault(x => x.ReferenceId == id));

        mockAdPasswordExpireRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AdPasswordExpire>()));
           // .Returns(Task.CompletedTask);

        mockAdPasswordExpireRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);

        return mockAdPasswordExpireRepository;
    }

    public static Mock<IAdPasswordExpireRepository> CreateDeleteAdPasswordExpireRepository(List<AdPasswordExpire> adPasswordExpires)
    {
        var mockAdPasswordExpireRepository = new Mock<IAdPasswordExpireRepository>();

        mockAdPasswordExpireRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(adPasswordExpires);

        mockAdPasswordExpireRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => adPasswordExpires.FirstOrDefault(x => x.ReferenceId == id));

        mockAdPasswordExpireRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AdPasswordExpire>()));
           // .Returns(Task.CompletedTask);

        return mockAdPasswordExpireRepository;
    }

    public static Mock<IAdPasswordExpireRepository> CreateQueryAdPasswordExpireRepository(List<AdPasswordExpire> adPasswordExpires)
    {
        var mockAdPasswordExpireRepository = new Mock<IAdPasswordExpireRepository>();

        mockAdPasswordExpireRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(adPasswordExpires);

        mockAdPasswordExpireRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => adPasswordExpires.FirstOrDefault(x => x.ReferenceId == id));

        mockAdPasswordExpireRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string name, string id) =>
            {
                if (string.IsNullOrEmpty(id))
                    return adPasswordExpires.Any(x => x.UserName == name);
                return adPasswordExpires.Any(x => x.UserName == name && x.ReferenceId != id);
            });

        //mockAdPasswordExpireRepository.Setup(repo => repo.PaginatedListAllAsync(It.IsAny<int>(), It.IsAny<int>(),
        //    It.IsAny<object>(), It.IsAny<string>(), It.IsAny<string>()))
        //    .ReturnsAsync((int pageNumber, int pageSize, object spec, string sortColumn, string sortOrder) =>
        //    {
        //        var totalCount = adPasswordExpires.Count;
        //        var items = adPasswordExpires.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
        //        return new ContinuityPatrol.Shared.Core.Wrapper.PaginatedResult<AdPasswordExpire>(items, totalCount, pageNumber, pageSize);
        //    });

        return mockAdPasswordExpireRepository;
    }

    public static Mock<IAdPasswordExpireRepository> CreateAdPasswordExpireEventRepository(List<UserActivity> userActivities)
    {
        var mockUserActivityRepository = new Mock<IUserActivityRepository>();

        mockUserActivityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        mockUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();
                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();
                userActivities.Add(userActivity);
                return userActivity;
            });

        return mockUserActivityRepository.As<IAdPasswordExpireRepository>();
    }
}
