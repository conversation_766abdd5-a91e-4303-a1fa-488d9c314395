﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowHistoryRepository : BaseRepository<WorkflowHistory>, IWorkflowHistoryRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public WorkflowHistoryRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override Task<IReadOnlyList<WorkflowHistory>> ListAllAsync()
    {
        return _loggedInUserService.IsParent
            ? base.ListAllAsync()
            : FindByFilterAsync(workflowHistory => workflowHistory.CompanyId.Equals(_loggedInUserService.CompanyId));
    }

    public override Task<WorkflowHistory> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilterAsync(workflowHistory =>
                workflowHistory.ReferenceId.Equals(id) &&
                workflowHistory.CompanyId.Equals(_loggedInUserService.CompanyId)).Result.SingleOrDefault());
    }

    public override IQueryable<WorkflowHistory> GetPaginatedQuery()
    {
        if (_loggedInUserService.IsParent)
            return Entities.Where(x => x.IsActive)
                .AsNoTracking()
                .OrderByDescending(x => x.Id);

        return Entities.Where(x => x.IsActive && x.CompanyId.Equals(_loggedInUserService.CompanyId))
            .AsNoTracking()
            .OrderByDescending(x => x.Id);
    }

    public Task<List<WorkflowHistory>> GetWorkflowHistoryNames()
    {
        if (!_loggedInUserService.IsParent)
            return _dbContext.WorkFlowHistories.Active()
                .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new WorkflowHistory { ReferenceId = x.ReferenceId, WorkflowName = x.WorkflowName })
                .OrderBy(x => x.WorkflowName)
                .ToListAsync();
        return _dbContext.WorkFlowHistories
            .Active()
            .Select(x => new WorkflowHistory { ReferenceId = x.ReferenceId, WorkflowName = x.WorkflowName })
            .OrderBy(x => x.WorkflowName)
            .ToListAsync();
    }

    public async Task<List<WorkflowHistory>> GetWorkflowHistoryByWorkflowId(string workflowId)
    {
        if (!_loggedInUserService.IsParent)
            return await _dbContext.WorkFlowHistories.Active()
                .Where(x => x.WorkflowId.Equals(workflowId) && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .OrderByDescending(x => x.Id)
                .ToListAsync();
        return await _dbContext.WorkFlowHistories.Active()
            .Where(x => x.WorkflowId.Equals(workflowId))
            .OrderByDescending(x => x.Id)
            .ToListAsync();
    }

    public Task<bool> IsWorkflowHistoryNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.WorkFlowHistories.Any(e => e.WorkflowName.Equals(name)))
            : Task.FromResult(_dbContext.WorkFlowHistories.Where(e => e.WorkflowName.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsWorkflowHistoryNameUnique(string name)
    {
        var matches = _dbContext.WorkFlowHistories.Any(e => e.WorkflowName.Equals(name));

        return Task.FromResult(matches);
    }

    public Task<WorkflowHistory> GetWorkflowHistoryByWorkflowIdAndVersion(string workflowId, string version)
    {
        if (!_loggedInUserService.IsParent)
            return Task.FromResult(_dbContext.WorkFlowHistories.Active()
                .Where(x => x.WorkflowId.Equals(workflowId) && x.Version.Equals(version) &&
                            x.CompanyId.Equals(_loggedInUserService.CompanyId)).SingleOrDefault());

        return Task.FromResult(_dbContext.WorkFlowHistories.Active()
            .Where(x => x.WorkflowId.Equals(workflowId) && x.Version.Equals(version)).SingleOrDefault());
        ;
    }
}