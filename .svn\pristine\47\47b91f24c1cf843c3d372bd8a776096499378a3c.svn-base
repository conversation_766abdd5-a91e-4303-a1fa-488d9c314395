﻿using ContinuityPatrol.Application.Features.RoboCopy.Events.Create;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.RoboCopy.Events
{
    public class CreateRoboCopyEventTests
    {
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly Mock<ILogger<RoboCopyCreatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly RoboCopyCreatedEventHandler _handler;

        public CreateRoboCopyEventTests()
        {
            _mockUserService = new Mock<ILoggedInUserService>();
            _mockLogger = new Mock<ILogger<RoboCopyCreatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();

            _handler = new RoboCopyCreatedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldLogAndSaveUserActivity_WhenEventIsHandled()
        {
            var createdEvent = new RoboCopyCreatedEvent { Name = "TestRoboCopy" };
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(s => s.UserId).Returns("12345");
            _mockUserService.Setup(s => s.LoginName).Returns("TestUser");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://example.com");
            _mockUserService.Setup(s => s.CompanyId).Returns("Company123");
            _mockUserService.Setup(s => s.IpAddress).Returns("127.0.0.1");

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Returns(ToString);

            await _handler.Handle(createdEvent, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "12345" &&
                activity.LoginName == "TestUser" &&
                activity.RequestUrl == "http://example.com" &&
                activity.CompanyId == "Company123" &&
                activity.HostAddress == "127.0.0.1" &&
                activity.Action.Contains("Create RoboCopy") &&
                activity.ActivityType == ActivityType.Create.ToString() &&
                activity.ActivityDetails.Contains($"RoboCopy '{createdEvent.Name}' created successfully.") &&
                !string.IsNullOrEmpty(activity.CreatedBy) &&
                !string.IsNullOrEmpty(activity.LastModifiedBy)
            )), Times.Once);

            _mockLogger.Verify(logger =>
                logger.LogInformation(It.Is<string>(msg => msg.Contains($"RoboCopy '{createdEvent.Name}' created successfully."))),
                Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldHandleEmptyUserIdGracefully()
        {
            var createdEvent = new RoboCopyCreatedEvent { Name = "TestRoboCopy" };
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(s => s.UserId).Returns(string.Empty);
            _mockUserService.Setup(s => s.LoginName).Returns("TestUser");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://example.com");
            _mockUserService.Setup(s => s.CompanyId).Returns("Company123");
            _mockUserService.Setup(s => s.IpAddress).Returns("127.0.0.1");

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Returns(ToString);

            await _handler.Handle(createdEvent, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                !string.IsNullOrEmpty(activity.CreatedBy) &&
                !string.IsNullOrEmpty(activity.LastModifiedBy)
            )), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldNotThrowException_WhenRepositoryThrowsException()
        {
            var createdEvent = new RoboCopyCreatedEvent { Name = "TestRoboCopy" };
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(s => s.UserId).Returns("12345");

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .ThrowsAsync(new Exception("Database error"));

            var exception = await Record.ExceptionAsync(() => _handler.Handle(createdEvent, cancellationToken));
            Assert.Null(exception);

            _mockLogger.Verify(logger =>
                logger.LogInformation(It.Is<string>(msg => msg.Contains($"RoboCopy '{createdEvent.Name}' created successfully."))),
                Times.Once);
        }
    }
}
