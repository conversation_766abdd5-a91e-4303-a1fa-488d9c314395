﻿using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperation.Queries;

public class GetWorkflowOperationDetailQueryHandlerTests : IClassFixture<WorkflowOperationFixture>
{
    private readonly WorkflowOperationFixture _workflowOperationFixture;

    private readonly Mock<IWorkflowOperationRepository> _mockWorkflowOperationRepository;

    private readonly GetWorkflowOperationDetailQueryHandler _handler;

    public GetWorkflowOperationDetailQueryHandlerTests(WorkflowOperationFixture workflowOperationFixture)
    {
        _workflowOperationFixture = workflowOperationFixture;

        _mockWorkflowOperationRepository = WorkflowOperationRepositoryMocks.GetWorkflowOperationRepository(_workflowOperationFixture.WorkflowOperations);

        _handler = new GetWorkflowOperationDetailQueryHandler(_workflowOperationFixture.Mapper, _mockWorkflowOperationRepository.Object);
    }
    
    [Fact]
    public async Task Handle_Return_WorkflowOperation_Details_When_Valid()
    {
        var result = await _handler.Handle(new GetWorkflowOperationDetailQuery { Id = _workflowOperationFixture.WorkflowOperations[0].ReferenceId },CancellationToken.None);

        result.ShouldBeOfType<WorkflowOperationDetailVm>();

        result.Id.ShouldBe(_workflowOperationFixture.WorkflowOperations[0].ReferenceId);
        result.ProfileId.ShouldBe(_workflowOperationFixture.WorkflowOperations[0].ProfileId);
        result.Status.ShouldBe(_workflowOperationFixture.WorkflowOperations[0].Status);
        result.Description.ShouldBe(_workflowOperationFixture.WorkflowOperations[0].Description);
        result.ProfileName.ShouldBe(_workflowOperationFixture.WorkflowOperations[0].ProfileName);
        result.StartTime.ShouldBe(_workflowOperationFixture.WorkflowOperations[0].StartTime);
        result.EndTime.ShouldBe(_workflowOperationFixture.WorkflowOperations[0].EndTime);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_Invalid_WorkflowOperationId()
    {
        var handler = new GetWorkflowOperationDetailQueryHandler(_workflowOperationFixture.Mapper, _mockWorkflowOperationRepository.Object);

        await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(new GetWorkflowOperationDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_GetWorkflowOperationId_OneTime()
    {
        await _handler.Handle(new GetWorkflowOperationDetailQuery { Id = _workflowOperationFixture.WorkflowOperations[0].ReferenceId }, CancellationToken.None);

        _mockWorkflowOperationRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once());
    }
}