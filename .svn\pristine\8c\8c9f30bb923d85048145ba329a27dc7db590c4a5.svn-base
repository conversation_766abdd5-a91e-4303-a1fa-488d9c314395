using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SiteTypeFixture : IDisposable
{
    public List<SiteType> SiteTypePaginationList { get; set; }
    public List<SiteType> SiteTypeList { get; set; }
    public SiteType SiteTypeDto { get; set; }


    public ApplicationDbContext DbContext { get; private set; }

    public SiteTypeFixture()
    {
        var fixture = new Fixture();

        SiteTypeList = fixture.Create<List<SiteType>>();

        SiteTypePaginationList = fixture.CreateMany<SiteType>(20).ToList();

        SiteTypeDto = fixture.Create<SiteType>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
