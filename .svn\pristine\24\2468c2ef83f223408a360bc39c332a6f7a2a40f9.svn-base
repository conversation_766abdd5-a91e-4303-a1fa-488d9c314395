﻿namespace ContinuityPatrol.Domain.Entities;

public class ComponentType : AuditableEntity
{
    public string ComponentName { get; set; }
    public string FormTypeId { get; set; }
    public string FormTypeName { get; set; }
    [Column(TypeName = "NCLOB")] public string Properties { get; set; }
    public string Logo { get; set; }
    [Column(TypeName = "NCLOB")] public string Version { get; set; }
    [Column(TypeName = "NCLOB")] public string ComponentProperties { get; set; }
    public bool IsDatabase { get; set; }
    public bool IsReplication { get; set; }
    public bool IsServer { get; set; }
    public bool IsCustom { get; set; }
}