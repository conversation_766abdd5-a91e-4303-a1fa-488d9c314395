using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetByDynamicDashboardId;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetList;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicSubDashboardModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class DynamicSubDashboardsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<DynamicSubDashboardListVm>>> GetDynamicSubDashboards()
    {
        Logger.LogDebug("Get All DynamicSubDashboards");

        return Ok(await Mediator.Send(new GetDynamicSubDashboardListQuery()));
    }

    [HttpGet("dynamicDashboardId")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<DynamicSubDashboardListVm>>> GetByDashboardIdAsync(string dynamicDashboardId)
    {
        Logger.LogDebug($"Get All DynamicSubDashboards by dynamic dashboard Id '{dynamicDashboardId}'");

        return Ok(await Mediator.Send(new GetByDynamicDashBoardIdListQuery {DynamicDashboardId = dynamicDashboardId }));
    }

    [HttpGet("{id}", Name = "GetDynamicSubDashboard")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<DynamicSubDashboardDetailVm>> GetDynamicSubDashboardById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DynamicSubDashboard Id");

        Logger.LogDebug($"Get DynamicSubDashboard Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetDynamicSubDashboardDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Admin.View)]
 public async Task<ActionResult<PaginatedResult<DynamicSubDashboardListVm>>> GetPaginatedDynamicSubDashboards([FromQuery] GetDynamicSubDashboardPaginatedListQuery query)
 {
     if (query == null)
         throw new ArgumentNullException(nameof(query));

     Logger.LogDebug("Get Searching Details in DynamicSubDashboard Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<ActionResult<CreateDynamicSubDashboardResponse>> CreateDynamicSubDashboard([FromBody] CreateDynamicSubDashboardCommand createDynamicSubDashboardCommand)
    {
        if (createDynamicSubDashboardCommand == null)
            throw new ArgumentNullException(nameof(createDynamicSubDashboardCommand));

        Logger.LogDebug($"Create DynamicSubDashboard '{createDynamicSubDashboardCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateDynamicSubDashboard), await Mediator.Send(createDynamicSubDashboardCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<ActionResult<UpdateDynamicSubDashboardResponse>> UpdateDynamicSubDashboard([FromBody] UpdateDynamicSubDashboardCommand updateDynamicSubDashboardCommand)
    {
        if (updateDynamicSubDashboardCommand == null)
            throw new ArgumentNullException(nameof(updateDynamicSubDashboardCommand));

        Logger.LogDebug($"Update DynamicSubDashboard '{updateDynamicSubDashboardCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateDynamicSubDashboardCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Admin.Delete)]
    public async Task<ActionResult<DeleteDynamicSubDashboardResponse>> DeleteDynamicSubDashboard(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DynamicSubDashboard Id");

        Logger.LogDebug($"Delete DynamicSubDashboard Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteDynamicSubDashboardCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsDynamicSubDashboardNameExist(string dynamicSubDashboardName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(dynamicSubDashboardName, "DynamicSubDashboard Name");

     Logger.LogDebug($"Check Name Exists Detail by DynamicSubDashboard Name '{dynamicSubDashboardName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetDynamicSubDashboardNameUniqueQuery { Name = dynamicSubDashboardName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}