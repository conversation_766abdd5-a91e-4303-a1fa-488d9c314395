﻿using ContinuityPatrol.Application.Features.SiteLocation.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.SiteLocationModel;

namespace ContinuityPatrol.Application.UnitTests.Features.SiteLocation.Queries
{
    public class GetSiteLocationListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISiteLocationRepository> _mockSiteLocationRepository;
        private readonly GetSiteLocationListQueryHandler _handler;

        public GetSiteLocationListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSiteLocationRepository = new Mock<ISiteLocationRepository>();
            _handler = new GetSiteLocationListQueryHandler(_mockMapper.Object, _mockSiteLocationRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedSiteLocationList_WhenSiteLocationsExist()
        {
            var query = new GetSiteLocationListQuery();

            var siteLocations = new List<Domain.Entities.SiteLocation>
            {
                new Domain.Entities.SiteLocation { Id = 1, City = "Chennai" },
                new Domain.Entities.SiteLocation { Id = 2, Country = "India" }
            };

            var siteLocationListVms = new List<SiteLocationListVm>
            {
                new SiteLocationListVm { Id = "1", City = "Chennai" },
                new SiteLocationListVm { Id = "2", Country = "India" }
            };

            _mockSiteLocationRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(siteLocations);

            _mockMapper.Setup(mapper => mapper.Map<List<SiteLocationListVm>>(siteLocations))
                .Returns(siteLocationListVms);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("1", result[0].Id);
            Assert.Equal("Chennai", result[0].City);
            Assert.Equal("2", result[1].Id);
            Assert.Equal("India", result[1].Country);

            _mockSiteLocationRepository.Verify(repo => repo.ListAllAsync(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<SiteLocationListVm>>(siteLocations), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoSiteLocationsExist()
        {
            var query = new GetSiteLocationListQuery();

            _mockSiteLocationRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(new List<Domain.Entities.SiteLocation>());

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);

            _mockSiteLocationRepository.Verify(repo => repo.ListAllAsync(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<SiteLocationListVm>>(It.IsAny<List<Domain.Entities.SiteLocation>>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldCallRepositoryAndMapperOnce_WhenSiteLocationsExist()
        {
            var query = new GetSiteLocationListQuery();

            var siteLocations = new List<Domain.Entities.SiteLocation>
            {
                new Domain.Entities.SiteLocation { Id = 1, City = "Chennai" }
            };

            var siteLocationListVms = new List<SiteLocationListVm>
            {
                new SiteLocationListVm { Id = "1", Country = "Chennai" }
            };

            _mockSiteLocationRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(siteLocations);

            _mockMapper.Setup(mapper => mapper.Map<List<SiteLocationListVm>>(siteLocations))
                .Returns(siteLocationListVms);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal("1", result[0].Id);
            Assert.Equal("Chennai", result[0].City);

            _mockSiteLocationRepository.Verify(repo => repo.ListAllAsync(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<SiteLocationListVm>>(siteLocations), Times.Once);
        }
    }
}
