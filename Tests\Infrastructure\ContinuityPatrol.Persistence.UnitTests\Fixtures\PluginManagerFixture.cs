using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class PluginManagerFixture : IDisposable
{
    public List<PluginManager> PluginManagerPaginationList { get; set; }
    public List<PluginManager> PluginManagerList { get; set; }
    public PluginManager PluginManagerDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string ParentCompanyId = "PARENT_COMPANY";

    public ApplicationDbContext DbContext { get; private set; }

    public PluginManagerFixture()
    {
        var fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        fixture.Customize<PluginManager>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .With(x => x.LastModifiedDate, DateTime.UtcNow)
            .With(x => x.CreatedBy, "TestUser")
            .With(x => x.LastModifiedBy, "TestUser")
            .With(x => x.CompanyId, CompanyId)
            .With(x => x.Name, () => $"PluginManager_{Guid.NewGuid().ToString().Substring(0, 8)}")
            .With(x => x.Version, () => $"{fixture.Create<int>() % 10}.{fixture.Create<int>() % 10}.{fixture.Create<int>() % 10}")
            .With(x => x.Description, () =>
            {
                var desc = fixture.Create<string>();
                return desc.Length > 50 ? desc.Substring(0, 50) : desc;
            })
            .With(x => x.Properties, () =>
            {
                var props = fixture.Create<string>();
                return props.Length > 100 ? props.Substring(0, 100) : props;
            }));

        PluginManagerList = fixture.CreateMany<PluginManager>(5).ToList();
        PluginManagerPaginationList = fixture.CreateMany<PluginManager>(20).ToList();
        PluginManagerDto = fixture.Create<PluginManager>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public PluginManager CreatePluginManagerWithProperties(
        string name = null,
        string companyId = null,
        string version = null,
        string description = null,
        bool isActive = true)
    {
        var fixture = new Fixture();
        return new PluginManager
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = name ?? $"PluginManager_{Guid.NewGuid().ToString().Substring(0, 8)}",
            CompanyId = companyId ?? CompanyId,
            Version = version ?? $"{fixture.Create<int>() % 10}.{fixture.Create<int>() % 10}.{fixture.Create<int>() % 10}",
            Description = description ?? (fixture.Create<string>().Length > 50 ? fixture.Create<string>().Substring(0, 50) : fixture.Create<string>()),
            Properties = fixture.Create<string>().Length > 100 ? fixture.Create<string>().Substring(0, 100) : fixture.Create<string>(),
            IsActive = isActive,
            CreatedDate = DateTime.UtcNow,
            LastModifiedDate = DateTime.UtcNow,
            CreatedBy = "TestUser",
            LastModifiedBy = "TestUser"
        };
    }

    public PluginManager CreatePluginManagerWithSpecificName(string name)
    {
        return CreatePluginManagerWithProperties(name: name);
    }

    public List<PluginManager> CreatePluginManagersWithSameCompany(string companyId, int count)
    {
        var pluginManagers = new List<PluginManager>();
        for (int i = 0; i < count; i++)
        {
            pluginManagers.Add(CreatePluginManagerWithProperties(
                name: $"PluginManager_{companyId}_{i}",
                companyId: companyId));
        }
        return pluginManagers;
    }

    public List<PluginManager> CreatePluginManagersWithDifferentCompanies(int count)
    {
        var pluginManagers = new List<PluginManager>();
        for (int i = 0; i < count; i++)
        {
            var companyId = i % 2 == 0 ? CompanyId : ParentCompanyId;
            pluginManagers.Add(CreatePluginManagerWithProperties(
                name: $"PluginManager_Mixed_{i}",
                companyId: companyId));
        }
        return pluginManagers;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
