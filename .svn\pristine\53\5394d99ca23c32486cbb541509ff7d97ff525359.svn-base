﻿let createPermission = $("#configurationCreate").data("create-permission")?.toLowerCase();
let deletePermission = $("#configurationDelete").data("delete-permission")?.toLowerCase();
let isSession = false;
let dataTable = "";
let selectedValues = [];
let versions = "";
let databaseProperties;
let isEdit = false;
let this1;
let logo;
let btnDisableDB = false;
let getdatabase = '';
//let previousVersion = "";
let licenseIdForCountError = "";
let reportSearchStr = "";
let clonedDatabaseRowData = "";
let clonedDatabaseLists = {
    "DatabaseId": "",
    "DatabaseList": [
    ]
};

let flagEdit = true;
let cloneDatabaseSlNo = 0;
let deleteCloneDatabaseRow = "";

let checkedTestConnection = [];

$(async function () {
    $('#search-inp, #databaseName').on('keypress', databsepreventEnterKey);
    $("#DBTestConnectionBtn").addClass("d-none");

    if (createPermission == 'false') {
        $("#database-createbutton").removeClass('#database-createbutton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }

    await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Configuration/Database/GetPagination",
        dataType: "json",
        success: function (result) {
            if (result?.success && (Array.isArray(result?.data?.data) && result?.data?.data?.length > 0)) {
                let databaseType = $('#selectType');
                let options = [];
                const uniqueValues = new Set();
                result?.data?.data?.forEach(function (dbtype, index) {
                    const dbTypeId = dbtype?.databaseTypeId?.toLowerCase();
                    if (!uniqueValues.has(dbTypeId)) {
                        options.push($('<option>').val(dbtype?.databaseTypeId).text(dbtype?.databaseType));
                        uniqueValues.add(dbTypeId);
                    }
                });
                databaseType.append(options);
                databaseType.prop('selectedIndex', 0).trigger('change');

            } else {
                errorNotification(result)
            }
        },
    });

    dataTable = $('#databaseList').DataTable(
        {
            language: {
                decimal: ",",
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous" ></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "Sortable": true,
            "order": [],
            fixedColumns: {
                left: 1,
                right: 1
            },
            "ajax": {
                "type": "GET",
                "url": "/Configuration/Database/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    let selectedDBType = $('#selectType :selected').text().replaceAll(" ", "").toLowerCase();
                    let secondColumn = "";
                    let fifthColumn = "";

                    if (selectedDBType === "mssql") {
                        secondColumn = "sid";
                        fifthColumn = "authenticationMode";
                    } else if (["oracle", "mysql", "oracle_racs", "oracleracs", "mssql_2kx", "mssql2kx"].includes(selectedDBType)) {
                        secondColumn = "sid";
                        fifthColumn = "port";
                    } else {
                        secondColumn = "databaseType";
                        fifthColumn = "port";
                    }

                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? secondColumn : sortIndex === 3 ? "type" :
                        sortIndex === 4 ? "version" : sortIndex === 5 ? fifthColumn : sortIndex === 6 ? "modeType" : "";
                    let orderValue = d?.order[0]?.dir || 'asc';

                    let selectedType = $('#selectType').val();
                    if (getdatabase?.length > 0) {
                        selectedType = getdatabase;
                    }
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');

                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;

                    d.DatabaseTypeId = selectedType;
                    selectedValues.length = 0;
                    reportSearchStr = d.searchString;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                        const { data } = json;
                        const hasData = data?.data?.length > 0;
                        $(".pagination-column").toggleClass("disabled", !hasData);
                        $("#TestConnectionAll").prop("disabled", !hasData);
                        json.recordsTotal = data?.totalPages;
                        json.recordsFiltered = data?.totalCount;
                        return data?.data;
                    }
                    else {
                        errorNotification(json)
                    }
                },
            },
            "columnDefs": [
                {
                    "targets": [1, 2],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        return type === 'display' ? meta.row + 1 : data;
                    }
                },
                {
                    "data": "name", "name": "name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>${data || "NA"} </span>` : data;
                    }
                },
                {
                    "data": "properties", "name": "name", "autoWidth": true,
                    "render": function (data, type, row) {
                        let iconList = JSON.parse(row?.properties);
                        if (type === 'display') {
                            let selectType = $('#selectType option:selected').text().toLowerCase();
                            let dynamicHeader = $("#dynamicHeader");
                            let dynamicLabel = $("#dynamicLabel");
                            let dynamicFilter = $("#dynamicFilter");
                            dynamicHeader.empty();
                            let headerText, filterValue, propsValue = 'NA';

                            if (selectType.includes("oracle")) {
                                headerText = "Oracle SID";
                                filterValue = "sid=";
                                propsValue = iconList?.OracleSID || iconList?.oracleSID || "NA";
                            } else if (selectType.includes("mssql")) {
                                headerText = "Database SID";
                                filterValue = "sid=";
                                propsValue = iconList?.DatabaseSID || iconList?.databaseSID || "NA";
                            } else {
                                let keys = ['DatabaseName', 'DatabaseSID', 'Database', 'InstanceName', 'InstallationName',
                                    'databaseName', 'databaseSID', 'database', 'instanceName', 'installationName'];
                                let foundKey = keys.find(key => iconList?.[key]);
                                let formattedName = foundKey?.replace(/([a-z])([A-Z])/g, '$1 $2');
                                headerText = formattedName;
                                filterValue = foundKey?.toLowerCase()?.includes("inst") ? "instance=" : "sid=";
                                propsValue = keys.map(key => iconList?.[key]).find(Boolean) || 'NA';
                                if (propsValue == 'NA') {
                                    filterValue = "database="
                                    headerText = "Database Type";
                                    propsValue = iconList?.['DatabaseType'] || iconList?.['databaseType'] || 'NA';
                                }
                            }
                            dynamicHeader.html(headerText);
                            dynamicLabel.html(headerText);
                            dynamicFilter.val(filterValue);
                            return `<span title='${propsValue}'> ${propsValue} </span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "type", "name": "type", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>${data || "NA"}</span>` : data;
                    }
                },
                {
                    "data": "version", "name": "version", "autowidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>${data || "NA"}</span>` : data;
                    }
                },
                {
                    "data": "properties", "name": "port", "autowidth": true,
                    "render": function (data, type, row) {
                        if (data) {
                            const cleanedVersion = JSON.parse(data);
                            const port = cleanedVersion.Port || cleanedVersion.port || "NA";
                            const typeText = $('#selectType option:selected').text().toLowerCase();

                            if (type === 'display') {
                                let headerText = typeText === "mssql" ? "Authentication Mode" : "Port Number";
                                let content = typeText === "mssql"
                                    ? cleanedVersion?.AuthenticationMode || cleanedVersion?.authenticationMode || "NA"
                                    : port;

                                $("#dynamicHeaderTwo").text(headerText);
                                return `<span title='${content}'>${content}</span>`;
                            }
                            return data;
                        }
                        return data;
                    }
                },
                {
                    "data": "modeType", "name": "modeType", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            const statusForError = data?.toLowerCase() || "NA";
                            const statusClass = {
                                'up': 'text-success',
                                'pending': 'text-warning cp-pending',
                                'down': 'text-danger'
                            };
                            const tooltipText = {
                                'up': 'Up',
                                'pending': 'Pending',
                                'down': 'Down'
                            };
                            const iconClass = statusClass[statusForError] || '';
                            const tooltip = tooltipText[statusForError] || statusForError;
                            return `
                                    <span title="${tooltip}">
                                         <i class="text-primary ${statusForError !== 'pending' ? `cp-${statusForError}-linearrow` : ''} me-1 ${iconClass}"></i>${tooltip}
                                    </span>`;
                        }
                        return data;
                    },
                },
                {
                    "orderable": false,
                    "render": function (data, type, row) {
                        let exceptionMessage = row?.exceptionMessage || "NA";
                        let rowId = row?.id || "NA";
                        let errorHTML = `<span title="Error Message" role="button" id="database-exception-Button" data-database="${exceptionMessage}"
                                           data-bs-toggle="modal" data-bs-target="#ErrorModal">
                                        <i class="cp-error-message"></i>
                                    </span>`

                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                                <div class="d-flex align-items-center gap-2">
                                    <span role="button" title="Test Connection" class="">
                                        <i class="cp-test-connection TestConnection" test-status="off" data-dbid="${rowId}"></i>
                                    </span>
                                    <span role="button" title="Edit" class="database-edit-button" data-database='${rowId}'>
                                        <i class="cp-edit "></i>
                                    </span>
                                    <span role="button" title="Delete" class="database-delete-button" data-bs-toggle="modal" 
                                        data-dbname="${row?.name}" data-dbid="${rowId}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                        <i class="cp-Delete"></i>
                                    </span>
                                    ${(row?.modeType?.toLowerCase() === "down" && exceptionMessage) ? errorHTML : ""}                                          
                                </div>`;
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                                <div class="d-flex align-items-center  gap-2">
                                    <span role="button" title="Edit" class="database-edit-button" aria-disabled="true" data-database='${rowId}'>
                                    <i class="cp-edit"></i>
                                    </span> 
                                    <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                    </span>   
                                    <span role="button" title="Test Connection" class="">
                                        <i class="cp-test-connection TestConnection" test-status="off" data-dbid="${rowId}"></i>
                                    </span>
                                    ${(row?.modeType?.toLowerCase() === "down" && exceptionMessage) ? errorHTML : ""}        
                                </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                                <div class="d-flex align-items-center  gap-2">
                                    <span role="button" title="Edit" class="icon-disabled">
                                        <i class="cp-edit"></i>
                                    </span>  
                                    <span role="button" title="Delete" class="database-delete-button" data-dbid="${rowId}" data-dbname="${row?.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                    </span>   
                                    <span role="button" title="Test connection" class="icon-disabled">
                                        <i class="cp-test-connection TestConnection" test-status="off" data-dbid="${rowId}"></i>
                                    </span>  
                                    ${(row?.modeType?.toLowerCase() === "down" && exceptionMessage) ? errorHTML : ""}        
                                 </div>`;
                        }
                        else {
                            return `
                                <div class="d-flex align-items-center  gap-2">
                                    <span role="button" title="Edit" class="icon-disabled">
                                       <i class="cp-edit"></i>
                                    </span>  
                                    <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                    </span>
                                    ${(row?.modeType?.toLowerCase() === "down" && exceptionMessage) ? errorHTML : ""}  
                                </div>`;
                        }
                    }
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart; // Get the start index of displayed data
                var counter = startIndex + index + 1; // Calculate the serial number based on start index and index
                $('td:eq(0)', row).html(counter); // Update the cell in the first column with the serial number
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            },
        });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });    

    $('#search-inp').on('keyup input', commonDebounce(async function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        } else {
            const checkboxes = ["Name", "dynamicFilter", "DatabaseType", "ModeType", "DatabaseVersion"];
            let sanitizedValue = $(this).val().replace(/^\s/, '').replace(/\s+/g, ' ').replace(/\s$/, ' ');
            if (sanitizedValue.trim() === "") {
                $(this).val("");
                sanitizedValue = "";
            } else {
                $(this).val(sanitizedValue);
            }

            checkboxes.forEach(id => {
                const checkbox = $(`#${id}`);
                if (checkbox.is(':checked')) {
                    selectedValues.push(checkbox.val() + sanitizedValue);
                }
            });
            dataTable.ajax.reload(function (json) {
                let $dataTables_empty = $('.dataTables_empty');

                if (sanitizedValue.length === 0 && json?.data?.data?.length === 0) {
                    $dataTables_empty.text('No Data Found');
                } else if (json?.recordsFiltered === 0) {
                    $dataTables_empty.text('No matching records found');
                }

            })
        }
        $('#TestConnectionAll').attr('test-status', "off");
        $(".TestConnection").attr('test-status', "off");
    }));

    $('#selectType').on("change", function () {
        dataTable.ajax.reload(function (json) {
            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    });

    clearSession();

    $('.form-select-sm').select2({
        "language": {
            "noResults": function () {
                return "No Results Found";
            }
        },
    });

    $("#btnServerRefresh").on("click", commonDebounce(function () {
        dataTable.ajax.reload();
        $("#DBTestConnectionBtn").addClass("d-none");
        $('#TestConnectionAll')
            .removeClass("btn btn-primary")
            .addClass("bg-white btn-outline-secondary")
            .attr("test-status", "off");
        $(".TestConnection")
            .removeClass('text-success')
            .attr("test-status", "on")
            .trigger('click');
    }));

    $("#DBTestConnectionBtn").on("click", async function () {
        $("#DBTestConnectionBtn").addClass("d-none");
        let testConnectionIDs = {
            'id': checkedTestConnection,
            __RequestVerificationToken: gettoken()
        }
        await $.ajax({
            type: "POST",
            url: RootUrl + 'Configuration/Database/DatabaseTestConnection',
            data: testConnectionIDs,
            datatype: "json",
            success: function (result) {
                checkedTestConnection = [];
                const $testConnectionAll = $('#TestConnectionAll');
                const $testConnection = $(".TestConnection");
                if (result.success) {
                    dataTable.ajax.reload();
                    notificationAlert("success", result?.data.message);
                }
                else {
                    errorNotification(result);
                }
                $("#DBTestConnectionBtn").addClass("d-none");
                $testConnectionAll
                    .removeClass("btn btn-primary text-success")
                    .addClass("bg-white btn-outline-secondary")
                    .attr("test-status", "off");
                $testConnection
                    .removeClass("text-success")
                    .attr("test-status", "off");
            }
        });
    });

    $('#TestConnectionAll').on("click", function () {
        const $testConnectionAll = $('#TestConnectionAll');
        const $testConnection = $(".TestConnection");
        let status = $(this).attr("test-status");
        checkedTestConnection = [];

        if (status == "off") {
            $("#DBTestConnectionBtn").removeClass("d-none");
            $testConnectionAll
                .removeClass("bg-white btn-outline-secondary")
                .addClass("btn btn-primary")
                .attr("test-status", "on");
            $testConnection
                .addClass('text-success')
                .attr("test-status", "off")
                .trigger('click');
        } else {
            $("#DBTestConnectionBtn").addClass("d-none");
            $testConnectionAll
                .removeClass("btn btn-primary")
                .addClass("bg-white btn-outline-secondary")
                .attr("test-status", "off");
            $testConnection
                .removeClass('text-success')
                .attr("test-status", "on")
                .trigger('click');
        }
    });

    $('.modal').on('shown.bs.modal', function async() {
        this1 = $(this);
    });

    $('.modal').on('hidden.bs.modal', function () {
        isEdit = false;
    });

    $('#databaseName').on('keyup', commonDebounce(async function () {
        const $databaseName = $("#databaseName");
        const sanitizedValue = $databaseName.val().replace(/\s{2,}/g, ' ');
        $databaseName.val(sanitizedValue);

        //InfraCommonFunctions.js InfraNameValidation
        await InfraNameValidation(sanitizedValue, $('#databaseID').val(), "Configuration/Database/IsDatabaseNameExist",
            $("#nameError"), "Enter database name", 'Special characters not allowed',
            'DatabaseName');
    }));

    $("#databaseLicenseKey").on("change", function () {
        const $selectedLicenseKey = $("#databaseLicenseKey :selected");
        let count = $selectedLicenseKey.attr('remainingcount');

        if (count !== null && count !== undefined) {
            $("#information").html(`<i class="cp-note me-1 fs-8"></i><span>Remaining count ${count} </span>`);
        }
        $('#databaseLicenseID').val($selectedLicenseKey.attr("licenseID"));
        commonDatabaseValidation($selectedLicenseKey.val(), " Select license key", "licenseKeyError");
        licenseCountValidation();
    });

    $("#databaseType").on("change", function () {
        const $selectedOption = $("#databaseType option:selected");
        let databaseType = $selectedOption.text();
        logo = $selectedOption.attr("logo");
        let formTypeID = $selectedOption.attr("formTypeID");
        $('#databaseTypeValue').val(databaseType);
        const logoClass = (logo && logo !== "d-flex") ? logo : "cp-database";
        $('#databaseTypeTitleIcon').removeClass().addClass(logoClass);
        $("#databaseLogo").val(logo);
        let element = document.getElementById('databaseTypeName');

        if (element) {
            const upperCaseType = databaseType.toUpperCase();
            element.setAttribute('title', upperCaseType);
            element.innerText = upperCaseType;
        }
        const filteredObjects = versions.filter(obj => {
            try {
                return obj.formTypeId === formTypeID;
            } catch (error) {
                return false;
            }
        });
        const selectedVersion = document.getElementById("databaseVersion");
        selectedVersion.innerHTML = '<option value="">Select version</option>';
        const versionArray = JSON.parse(filteredObjects[0]?.version || "[]");
        versionArray.Version.forEach(formversion => {
            const option = document.createElement("option");
            option.value = formversion;
            option.textContent = formversion;
            option.setAttribute("nodevalue", filteredObjects[0]?.formTypeId);
            selectedVersion.appendChild(option);
        });
        commonDatabaseValidation(databaseType, " Select type", "databaseTypeError");
        licenseCount();
    });

    $("#businessServiceID").on("change", function () {
        $("#businessServiceName").val($('#businessServiceID option:selected').attr('businessServiceName'));
        commonDatabaseValidation($("#businessServiceID").val(), " Select operational service", "businessServiceIdError");
    });

    $("#databaseVersion").on("change", function () {
        commonDatabaseValidation($("#databaseVersion").val(), " Select version", "versionError");
        databaseProps($("#databaseVersion option:selected").attr("nodevalue"), $("#databaseVersion").val());
    });

    $("#databaseServer").on("change", async function () {
        const $selectedOption = $("#databaseServer option:selected");
        const type = $selectedOption.attr("nodeValue") || "";
        const id = $selectedOption.attr("id") || "";
        const modifiedString = type.toLowerCase().includes("dbserver") ? type.replace(/DBServer/gi, "") + "Database" : type;
        $("#hiddenDatabaseType").val(modifiedString);
        $("#databaseServerID").val(id);
        $("#DBServerName").val($selectedOption.text());
        commonDatabaseValidation($selectedOption.text(), " Select server", "serverError");
        licenseCount();
    })

    $(".next_btn").on("click", async function () {

        //InfraCommonFunctions.js InfraNameValidation
        let validateName = await InfraNameValidation($("#databaseName").val(), $('#databaseID').val(),
            "Configuration/Database/IsDatabaseNameExist", $("#nameError"), "Enter database name",
            'Special characters not allowed', 'DatabaseName');
        let validateDBType = commonDatabaseValidation($("#databaseType").val(), " Select database type", "databaseTypeError");
        let businessServiceName = commonDatabaseValidation($("#businessServiceID").val(), " Select operational service", "businessServiceIdError");
        let validateServerType = commonDatabaseValidation($("#databaseServer").val(), " Select server", "serverError");
        let validateLicenseKey = commonDatabaseValidation($("#databaseLicenseKey").val(), " Select license key", "licenseKeyError");
        let validateVersion = commonDatabaseValidation($("#databaseVersion").val(), " Select version", "versionError");

        if (licenseCountValidation() && validateName && validateDBType && validateServerType && validateLicenseKey && validateVersion && businessServiceName) {
            form.steps('next');
        }
    });

    $('.prev_btn').on('click', function () {
        const removeElements = (selector) => {
            document.querySelectorAll(selector)?.forEach(element => element.remove());
        };
        removeElements('.dynamic-select-tag');

        const inputValues = $(".formeo-render .f-field-group input[type='text']:visible, .formeo-render .f-field-group input[type='number']:visible, .formeo-render .f-field-group input[type='password']:visible");
        //const inputValues = $(".formeo-render .f-field-group input[type='text']:visible");
        if (inputValues?.length) {
            inputValues?.each(function () {
                let $this = $(this);
                let res = $this.val();
                if (!res) {
                    $this.siblings('.dynamic-input-field').remove();
                }
            })
        }
    });

    $("#save_btn").on("click", async function () {
        const res = await inputFormValidation('database');

        if (!res) return;
        document.querySelector('#databaseType').disabled = false;
        let fd = await databaseSaveFormFields();
        fd.icon = logo;
        Object.keys(fd).forEach(key => {
            if (key.startsWith('f-')) delete fd[key];
        });
        fd.databaseType = $("#databaseType option:selected").text();
        const databaseVersion = $("#databaseVersion").val().trim();
        $("#databaseVersionTwo").val(databaseVersion);
        fd.version = databaseVersion;
        const $selectedServer = $("#databaseServer option:selected");
        const type = $selectedServer.attr("nodeValue");
        const id = $selectedServer.attr("id");
        const modifiedString = type.toLowerCase().includes("server") ? type.replace(/Server/gi, "") : type;
        $("#databaseStatus").val("Pending");
        $("#hiddenDatabaseType").val(modifiedString);
        $("#databaseServerID").val(id);
        async function encrypt() {
            let encryption = await propertyEncryption(fd);
            $('#databaseProps').val(encryption);
        }
        await encrypt();

        if (!btnDisableDB) {
            btnDisableDB = true;
            $('#databaseFormRenderingArea :input').prop('disabled', true);
            const form = $('#createFormDB')[0];
            const formData = new FormData(form);
            $('#databaseFormRenderingArea :input').prop('disabled', false);

            let response = await $.ajax({
                type: "POST",
                url: RootUrl + "Configuration/Database/CreateOrUpdate",
                headers: {
                    'RequestVerificationToken': await gettoken()
                },
                data: formData,
                contentType: false,
                processData: false,
            });

            if (response?.success) {
                $('#CreateModal').modal('hide');
                btnDisableDB = false;
                notificationAlert("success", response?.data?.message);
                setTimeout(() => {
                    //dataTable.ajax.reload();  
                    dataTableCreateAndUpdate($("#save_btn"), dataTable);
                    checkedTestConnection = [];
                }, 2000)
            } else {
                $('#CreateModal').modal('hide');
                errorNotification(response);
                btnDisableDB = false;
            }
        }
    });

    $('#databaseList').on('click', '.database-edit-button', function () {
        clearErrorMessage();
        isEdit = true;
        document.querySelector('#databaseType').disabled = true;
        $('#save_btn').text('Update');
        $('#CreateModal').modal('show');
        form.steps('previous');
        $.ajax({
            url: RootUrl + "Configuration/Database/GetByReferenceId",
            method: 'GET',
            dataType: 'json',
            data: { id: $(this).data("database") },
            success: function (result) {
                if (result.success) {
                    populateDatabaseModal(result?.data);
                } else {
                    errorNotification(result);
                }
            }
        })
    });

    $(".create-model").on("click", function () {
        $("#information").html("")
        licenseIdForCountError = "";
        $("#databaseID, #databaseFormVersion").val('');
        $('#databaseTypeTitleIcon').attr('class', 'cp-database');
        document.querySelector('#databaseType').disabled = false;
        isEdit = false;
        clearErrorMessage();
        $("#databaseLicenseKey").empty();
        const databaseTypeNameElement = document.getElementById('databaseTypeName');
        if (databaseTypeNameElement) {
            databaseTypeNameElement.setAttribute('title', 'Database');
            databaseTypeNameElement.innerText = 'Database';
        }
        $('#databaseFormRenderingArea, #databaseVersion').empty();
        $('#save_btn').text('Save');
        form.steps('previous')
    });

    $('#databaseList').on('click', '#database-exception-Button', function () {
        let databaseData = $(this).data('database');
        let text = databaseData ? databaseData.split("$") : null;
        let html = "";
        if (Array.isArray(text)) {
            text.forEach((data, index) => {
                let modifiedData = data.replace(/(Node Name :|Error Message :)/g, '<strong>$1</strong>');
                html += `<div>${modifiedData}.</div>`
                if ((index + 1) % 2 === 0) {
                    html += `<br>`;
                }
            });
        } else {
            html = "NA";
        }
        $('#databaseException').html(html);
    });

    $('#databaseList').on('click', '.database-delete-button', function () {
        const dbName = $(this).data("dbname");
        $("#deleteData").attr("title", dbName).text(dbName);
        $("#textDeleteId").val($(this).data("dbid"));
    });   

    $("#confirmDeleteButton").on("click", async function () {
        const form = $('#deleteDatabase')[0];
        const formData = new FormData(form);

        if (!btnDisableDB) {
            btnDisableDB = true;
            let response = await $.ajax({
                type: "POST",
                url: RootUrl + "Configuration/Database/Delete",
                headers: {
                    'RequestVerificationToken': await gettoken()
                },
                data: formData,
                contentType: false,
                processData: false,
            });

            if (response?.success) {
                $("#DeleteModal").modal("hide");
                notificationAlert("success", response?.data?.message);
                btnDisableDB = false;
                setTimeout(() => {
                    //dataTable.ajax.reload();
                    dataTableDelete(dataTable);
                    checkedTestConnection = [];
                }, 2000)
            } else {
                $("#DeleteModal").modal("hide");
                errorNotification(response);
                btnDisableDB = false;
            }
        }
    });

    let businessServiceNameData = await fetchDataDatabase('Configuration/OperationalService/GetBusinessServiceNames');
    if (businessServiceNameData && (Array.isArray(businessServiceNameData) && businessServiceNameData.length > 0)) {
        let businessService = $('#businessServiceID');
        let options = [];
        const sortedData = businessServiceNameData?.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
        sortedData.forEach(function (item) {
            options.push($('<option>').val(item.id).text(item.name).attr('businessServiceName', item.name));
        });
        businessService.append(options);
    }

    let databaseServerNameData = await fetchDataDatabase('Configuration/Server/DatabaseServerNameList');
    if (databaseServerNameData && (Array.isArray(databaseServerNameData) && databaseServerNameData.length > 0)) {
        let databaseServer = $('#databaseServer');
        let options = [];
        const sortedData = databaseServerNameData?.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
        sortedData.forEach(function (item) {
            options.push($('<option>').val(item.id).text(item.name).attr("nodeValue", item.serverType).attr("id", item.id));
        });
        databaseServer.append(options);
    }

    versions = "";
    let databaseListData = await fetchDataDatabase('Admin/FormMapping/GetFormMappingListByName', 'database');
    if (databaseListData && (Array.isArray(databaseListData) && databaseListData.length > 0)) {
        let databaseType = $('#databaseType');
        let options = [];
        versions = databaseListData;
        databaseListData.forEach(function (item) {
            options.push($('<option>').val(item.formTypeId).text(item.formTypeName).attr("id", item.id).attr("logo", item.logo).attr("formTypeID", item.formTypeId));
        });
        databaseType.append(options);
    }
});

$(document).on('click', '.TestConnection', function () {
    let id = $(this).data("dbid");
    let status = $(this).attr("test-status");
    let $DBTestConnectionBtn = $("#DBTestConnectionBtn");
    let $testConnectionAll = $('#TestConnectionAll');

    if (status == "off") {
        $DBTestConnectionBtn.removeClass("d-none");
        checkedTestConnection.push(id);
        $(this).attr("test-status", "on");
        $(this).addClass('text-success');
    } else {
        checkedTestConnection = checkedTestConnection.filter(item => item !== id);
        $("#DBTestConnectionBtn").addClass("d-none");
        $(this).attr("test-status", "off")
        $(this).removeClass('text-success');
        $testConnectionAll
            .removeClass("btn btn-primary")
            .addClass("bg-white btn-outline-secondary")
            .attr("test-status", "off");
    }
    let count = $(".TestConnection[test-status='on']").length;
    const table = document.getElementById('databaseList');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    const rowCount = rows.length;

    if (rowCount === count) {
        $testConnectionAll
            .removeClass("bg-white btn-outline-secondary")
            .addClass("btn btn-primary");
    }
    let anyChecked = $(".TestConnection[test-status='on']").length > 0;

    if (!anyChecked) {
        $("#DBTestConnectionBtn").addClass("d-none");
    } else {
        $DBTestConnectionBtn.removeAttr("style");
    }
});

$('#SolutionType').on('change', async function () {
    const errorElement = $('#SolutionType_Error');
    errorElement.text('');
    errorElement.removeClass('field-validation-error');
});

$('#BtnDBDownload').on('click', async function () {
    try {
        let templateType = $("#SolutionType option:selected").val();
        const errorElement = $('#SolutionType_Error');
        if (!templateType) {
            errorElement.text("Select Report Type");
            errorElement.addClass('field-validation-error');
            return false;
        }
        else {
            errorElement.text('');
            errorElement.removeClass('field-validation-error');
        }

        $('#BtnDBDownload').addClass("disabled");
        $('#SolutionType').addClass("disabled");
        var selectedTypeId = $('#selectType').val();
        const url = `/Configuration/Database/LoadReport?type=${templateType}&selectedTypeId=${selectedTypeId}&searchString=${reportSearchStr}`;
        const response = await fetch(url);
        if (response.ok) {
            const blob = await response.blob();
            var alertClass, iconClass, message;
            if (blob.size > 0) {
                const DateTime = new Date().toLocaleString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit', fractionalSecondDigits: 3, hour12: false }).replace(/[^0-9]/g, '');
                if (templateType == "pdf") { downloadDB(blob, "DatabaseComponent_" + DateTime + ".pdf", "application/pdf"); }
                else { downloadDB(blob, "DatabaseComponent_" + DateTime + ".xls", "application/vnd.ms-excel"); }
                alertClass = "success-toast";
                iconClass = "cp-check toast_icon";
                message = "DatabaseComponent report downloaded successfully";
            }
            else {
                alertClass = "warning-toast";
                iconClass = "cp-exclamation toast_icon";
                message = "DatabaseComponentReport Download Error";
            }
        }
        else {
            alertClass = "warning-toast";
            iconClass = "cp-exclamation toast_icon";
            message = "DatabaseComponentReport Download Error";
        }
        $('#alertClass').removeClass().addClass(alertClass);
        $('#icon').removeClass().addClass(iconClass);
        $('#notificationAlertmessage').text(message);
        $('#mytoastrdata').toast({ delay: 3000 }).toast('show');
        $('#BtnDBDownload').removeClass("disabled");
    }
    catch (error) {
        $('#BtnDBDownload').removeClass("disabled");
        notificationAlert("An error occurred:", error.message);
    }
    $("#SolutionType").val("").trigger("change");
    //const solutionTypeDropdown = document.getElementById('SolutionType');
    //solutionTypeDropdown.value = 'pdf';
    //$("#SolutionType").val("pdf").trigger("change");
});

//$("#cancelDBFormRestore").on("click", function () {
//    $('#databaseFormRenderingArea').empty();
//    $('#RestoreDBModal').modal('hide');
//});

//$("#confirmDBFormRestore").on("click", function () {
//    let databaseData = {
//        Id: $('#databaseID').val(),
//        OldFormVersion: previousVersion,
//        NewFormVersion: $("#databaseFormVersion").val(),
//        IsUpdateAll: false,
//        __RequestVerificationToken: gettoken()
//    }
//    updateDBFormVersion(databaseData);
//    nextButtonStyle('', '');
//    $('#RestoreDBModal').modal('hide');
//});

//$("#confirmDBFormRestoreAll").on("click", function () {
//    let databaseData = {
//        DatabaseTypeId: $('#databaseType').val(),
//        OldFormVersion: previousVersion,
//        NewFormVersion: $("#databaseFormVersion").val(),
//        IsUpdateAll: true,
//        __RequestVerificationToken: gettoken()
//    };
//    updateDBFormVersion(databaseData);
//    nextButtonStyle('', '');
//    $('#RestoreDBModal').modal('hide');
//});

