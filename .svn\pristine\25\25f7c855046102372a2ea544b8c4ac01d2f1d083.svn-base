﻿using ContinuityPatrol.Application.Features.UserInfraObject.Commands.Create;
using ContinuityPatrol.Application.Features.UserInfraObject.Commands.Update;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoUserInfraObjectDataAttribute : AutoDataAttribute
{
    public AutoUserInfraObjectDataAttribute() : base(() =>
    {
        var fixture = new Fixture();

        fixture.Customize<CreateUserInfraObjectCommand>(c => c.With(b => b.UserId, 0.ToString));

        fixture.Customize<UpdateUserInfraObjectCommand>(c => c.With(b => b.UserId, 0.ToString));

        return fixture;
    })
    {

    }
}