﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories
{
    public class RsyncJobRepository:BaseRepository<RsyncJob>, IRsyncJobRepository
    {
        private readonly ILoggedInUserService _loggedInUserService;
        private readonly ApplicationDbContext _dbContext;

        public RsyncJobRepository(ApplicationDbContext dbContext,ILoggedInUserService loggedInUserService):base(dbContext)
        {
            _dbContext = dbContext;
            _loggedInUserService = loggedInUserService;
        }

    }
}
