﻿using ContinuityPatrol.Application.Features.FormType.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FormType.Validators;

public class UpdateFormTypeValidatorTests 
{
    private readonly Mock<IFormTypeRepository> _mockFormTypeRepository;
    private UpdateFormTypeCommandValidator validator;
	public List<Domain.Entities.FormType> FormTypes { get; set; }

    public UpdateFormTypeValidatorTests()
    {
        FormTypes = new Fixture().Create<List<Domain.Entities.FormType>>();

        _mockFormTypeRepository = FormTypeRepositoryMocks.UpdateFormTypeRepository(FormTypes);
    }

    //FormTypeName

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_FormTypeName_InFormType_WithEmpty(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_FormTypeName_InFormType_IsNull(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = null;
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameNotNullRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_FormTypeName_InFormType_MinimumRange(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "CB";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameRangeRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_FormTypeName_InFormType_MaximumRange(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXUVTSRSQPOMNLKABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXUVTSRSQPOMNLKABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXUVTSRSQPOMNLKABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXUVTSRSQPOMNLKABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXUVTSRSQPOMNLKABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXUVTSRSQPOMNLKABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXUVTSRSQPOMNLK";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
		Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Form Type Name should contain between 3 to 100 characters.");
		
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_Valid_FormTypeName_InFormType(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "  CTS  ";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_Valid_FormTypeName_InFormType_With_SingleSpace_InFront(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = " CTS Tech";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_Valid_FormTypeName_InFormType_With_SingleSpace_InBack(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "CTS Tech ";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_Valid_FormTypeName_InFormType_With_TripleSpace_InBetween(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "CTS   Tech";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_Valid_FormTypeName_InFormType_With_SpecialCharacters_InFront(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "(**&^%CTS Tech";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_Valid_FormTypeName_InFormType_With_SpecialCharacters_InBack(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "CTS Tech*&&^%$";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_Valid_FormTypeName_InFormType_With_SpecialCharacters_InBetween(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "CTS$#%$^%&Tech";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_Valid_FormTypeName_InFormType_With_SpecialCharacters_Only(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "#%$^%&&^^%";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_Valid_FormTypeName_InFormType_With_UnderScore_InFront(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "_CTS Tech";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_Valid_FormTypeName_InFormType_With_UnderScore_InBack(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "CTS Tech_";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_Valid_FormTypeName_InFormType_With_UnderScore_InFront_With_Numbers_InFront(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "_876CTS Tech";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_Valid_FormTypeName_InFormType_With_Numbers_InFront(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "876CTS Tech";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_Valid_FormTypeName_InFormType_With_Numbers_InBack_With_UnderScore_InFront(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "_CTS Tech436";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormTypeData]
    public async Task Verify_Update_Valid_FormTypeName_InFormType_With_Numbers_Only(UpdateFormTypeCommand updateFormTypeCommand)
    {
        validator = new UpdateFormTypeCommandValidator(_mockFormTypeRepository.Object);

        updateFormTypeCommand.FormTypeName = "************";
        updateFormTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateFormTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormType.FormTypeNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }
}