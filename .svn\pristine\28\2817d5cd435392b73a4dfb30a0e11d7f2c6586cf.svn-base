﻿using ContinuityPatrol.Application.Features.DataSet.Commands.Create;
using ContinuityPatrol.Application.Features.DataSet.Commands.Update;
using ContinuityPatrol.Application.Features.DataSet.Event.Create;
using ContinuityPatrol.Application.Features.DataSet.Event.Delete;
using ContinuityPatrol.Application.Features.DataSet.Event.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class DataSetFixture : IDisposable
{
    public IMapper Mapper { get; }
    public List<DataSet> DataSets { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateDataSetCommand CreateDataSetCommand { get; set; }
    public UpdateDataSetCommand UpdateDataSetCommand { get; set; }
    public DataSetCreatedEvent DataSetCreatedEvent { get; set; }
    public DataSetDeletedEvent DataSetDeletedEvent { get; set; }
    public DataSetUpdatedEvent DataSetUpdatedEvent { get; set; }

    public DataSetFixture()
    {
        DataSets = AutoDataSetFixture.Create<List<DataSet>>();

        UserActivities = AutoDataSetFixture.Create<List<UserActivity>>();

        CreateDataSetCommand = AutoDataSetFixture.Create<CreateDataSetCommand>();

        UpdateDataSetCommand = AutoDataSetFixture.Create<UpdateDataSetCommand>();

        DataSetCreatedEvent = AutoDataSetFixture.Create<DataSetCreatedEvent>();

        DataSetDeletedEvent = AutoDataSetFixture.Create<DataSetDeletedEvent>();

        DataSetUpdatedEvent = AutoDataSetFixture.Create<DataSetUpdatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<DataSetProfile>();
        });

        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoDataSetFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateDataSetCommand>(p => p.DataSetName, 10));
            fixture.Customize<CreateDataSetCommand>(c => c.With(b => b.Description, 0.ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateDataSetCommand>(p => p.DataSetName, 10));
            fixture.Customize<UpdateDataSetCommand>(c => c.With(b => b.Id, 0.ToString));
            fixture.Customize<DataSet>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserActivity>(p => p.LoginName, 10));
            fixture.Customize<UpdateDataSetCommand>(c => c.With(b => b.Id, 0.ToString()));
            fixture.Customize<DataSet>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<DataSetCreatedEvent>(p => p.DatasetName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<DataSetDeletedEvent>(p => p.DatasetName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<DataSetUpdatedEvent>(p => p.DatasetName, 10));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}