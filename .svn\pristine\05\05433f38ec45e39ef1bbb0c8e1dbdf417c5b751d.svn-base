using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.CyberSnapsModel;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class CyberSnapsRepository : BaseRepository<CyberSnaps>, ICyberSnapsRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public CyberSnapsRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.Name.Equals(name))
            : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }
    public async Task<List<CyberSnaps>> GetCyberSnapsBySnapTagName(string cyberSnapTagName, string startDate, string endDate)
    {
        return await _dbContext.CyberSnaps.Active()
    .Where(x => x.CreatedDate.Date >= startDate.ToDateTime() &&
      x.CreatedDate.Date <= endDate.ToDateTime() && (cyberSnapTagName.ToLower() == "all" || x.Tag == cyberSnapTagName))
    .ToListAsync();
    }
    public async Task<List<CyberSnaps>> GetCyberSnapsListByDate(string startDate, string endDate)
    {
        return await _dbContext.CyberSnaps.Active()
      .Where(x => x.CreatedDate.Date >= startDate.ToDateTime() &&
      x.CreatedDate.Date <= endDate.ToDateTime()).ToListAsync();
    }
    public async Task<List<CyberSnaps>> GetCyberSnapsByStorageGroupNameAndLinkedStatus(Expression<Func<CyberSnaps, bool>> expression)
    {
        return await _dbContext.CyberSnaps.Active().Where(expression).ToListAsync();
    }

    public async Task<PaginatedResult<CyberSnapsListVm>> GetCyberSnapsByDateTime(int pageNumber, int pageSize, Specification<CyberSnaps> specification, string sortColumn, string sortOrder, DateTime startDate, DateTime endDate)
    {


        var formats = new[] {
            "MMM d HH:mm:ss yyyy",
            "MMM dd HH:mm:ss yyyy"
            };

        var baseQuery = await _dbContext.CyberSnaps
        .AsNoTracking().Specify(specification).ToListAsync();

        var query = baseQuery
                .AsEnumerable()
                .Where(x =>
                {
                    var timestampPart = x.TimeStamp.Substring(4).Trim();

                    timestampPart = Regex.Replace(timestampPart, @"\s{2,}", " ");

                    if (DateTime.TryParseExact(timestampPart, formats, CultureInfo.InvariantCulture,
                        DateTimeStyles.None, out DateTime parsedDate))
                    {
                        return parsedDate >= startDate && parsedDate < endDate.AddDays(1);
                    }


                    return false;
                })
                .ToList();

        var filtered = query
           .Select(x => new CyberSnapsListVm
           {
               Id = x.ReferenceId,
               Name = x.Name,
               Gen = x.Gen,
               TimeStamp = x.TimeStamp,
               StorageGroupName = x.StorageGroupName,
               LinkedStatus = x.LinkedStatus,
               LinkedSGTime = x.LinkedSGTime,
               LinkSG = x.LinkSG

           }).AsQueryable();

        var totalCount = filtered.Count();

        pageNumber = pageNumber <= 0 ? 1 : pageNumber;
        pageSize = pageSize <= 0 ? totalCount : pageSize;

        var pagedData = filtered
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList();

        return new PaginatedResult<CyberSnapsListVm>(
            succeeded: true,
            data: pagedData,
            count: totalCount,
            page: pageNumber,
            pageSize: pageSize
        );
    }
}