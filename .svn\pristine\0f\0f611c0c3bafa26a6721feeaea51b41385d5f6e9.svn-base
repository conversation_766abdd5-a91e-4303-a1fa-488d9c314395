﻿using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Helper;

public static class GetJsonProperties
{
    public static List<int> AutoIncrement { get; set; } = new();

    public static List<string> LicenseEntity { get; set; }


    public static bool IsRtoAchieved(string currentRTO, string configuredRTO)
    {
        var currentRto = ConvertToMinutes1(currentRTO);
        return int.TryParse(configuredRTO, out var configuredRto) && currentRto <= configuredRto;
    }

    public static bool IsRtoExceeded(string currentRTO, string configuredRTO)
    {
        var currentRto = ConvertToMinutes1(currentRTO);
        return int.TryParse(configuredRTO, out var configuredRto) && currentRto > configuredRto;
    }

    public static bool IsRpoAchieved(string currentRTO, string configuredRTO)
    {
        var currentRto = ConvertToMinutes1(currentRTO);
        return int.TryParse(configuredRTO, out var configuredRto) && currentRto <= configuredRto;
    }

    public static bool IsRpoExceeded(string currentRPO, string configuredRPO)
    {
        var currentRto = ConvertToMinutes1(currentRPO);
        return int.TryParse(configuredRPO, out var configuredRto) && currentRto > configuredRto;
    }




    private static int ConvertToMinutes1(string rtoString)
    {
        if (string.IsNullOrWhiteSpace(rtoString))
            return 0;

        rtoString = rtoString.Replace("+", "").Replace(" ", ".");

        if (Regex.IsMatch(rtoString, @"^\d{1,2}:\d{2}$"))
            rtoString = "00:" + rtoString; // Convert "50:00" to "00:50:00"

        return TimeSpan.TryParse(rtoString, out var timeSpan) ? (int)Math.Round(timeSpan.TotalMinutes) : 0;
    }


    public static bool IsValidJson(string properties)
    {
        try
        {
            //var decryptString = SecurityHelper.Decrypt(properties);

            return JsonConvert.DeserializeObject(properties) != null;
        }
        catch (Exception)
        {
            return IsJsonFormat(properties);
        }
    }


    private static bool IsJsonFormat(string properties)
    {
        if (string.IsNullOrWhiteSpace(properties))
            return false;

        properties = properties.Trim();
        if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
            (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
            try
            {
                return JsonConvert.DeserializeObject(properties) != null;
            }
            catch (JsonException)
            {
                return false;
            }

        return false;
    }


    public static string PasswordEncryption(string request)
    {
        dynamic jsonObj = JsonConvert.DeserializeObject(request);
        if (jsonObj != null && (jsonObj.ContainsKey("Password") || jsonObj.ContainsKey("password")))
        {
            var result = jsonObj.ContainsKey("Password")
                ? jsonObj.SelectToken("Password").ToString()
                : jsonObj.SelectToken("password").ToString();
            var key = ((JObject)jsonObj).ContainsKey("Password") ? "Password" : "password";
            jsonObj[key] = SecurityHelper.Encrypt(result);

            request = JsonConvert.SerializeObject(jsonObj, Formatting.Indented);
        }

        return request;
    }

    public static string PasswordDecryption(string request)
    {
        dynamic jsonObj = JsonConvert.DeserializeObject(request);
        if (jsonObj != null && (jsonObj.ContainsKey("Password") || jsonObj.ContainsKey("password")))
        {
            var result = jsonObj.ContainsKey("Password")
                ? jsonObj.SelectToken("Password").ToString()
                : jsonObj.SelectToken("password").ToString();
            var key = ((JObject)jsonObj).ContainsKey("Password") ? "Password" : "password";
            jsonObj[key] = SecurityHelper.Decrypt(result);

            request = JsonConvert.SerializeObject(jsonObj, Formatting.Indented);
        }

        return request;
    }

    public static string GetIpAddressFromProperties(string request)
    {
        if (string.IsNullOrEmpty(request)) return null;
        dynamic jsonObj = JsonConvert.DeserializeObject(request.Trim().ToLower());

        var result = jsonObj != null && !string.IsNullOrEmpty(jsonObj.SelectToken("ipaddress")?.ToString())
            ? jsonObj.SelectToken("ipaddress").ToString()
            : "NA";

        return result;
    }

    public static string GetHostNameFromProperties(string request)
    {
        if (string.IsNullOrEmpty(request)) return null;
        dynamic jsonObj = JsonConvert.DeserializeObject(request.Trim());

        var result = jsonObj != null && !string.IsNullOrEmpty(jsonObj.SelectToken("HostName")?.ToString())
            ? jsonObj.SelectToken("HostName").ToString()
            : "NA";

        return result;
    }

    public static string IsConnectionHostName(string request)
    {
        dynamic jsonObj = JsonConvert.DeserializeObject(request.Trim().ToLower());

        var hostName = jsonObj?.SelectToken("hostname")?.ToString() ?? "NA";

        //var isConnection = jsonObj?.SelectToken("connectviahostname")?.ToString();

        //if (isConnection is not null && isConnection.ToLower() == "true")
        //{
        //    return jsonObj.SelectToken("HostName") ?? "NA";
        //}


        //var result = jsonObj != null && !string.IsNullOrEmpty(jsonObj.SelectToken("connectviaipaddress")?.ToString())
        //    ? jsonObj.SelectToken("connectviaipaddress").ToString()
        //    : "NA";

        return hostName;
    }

    public static string GetConnectionViaHostNameProperties(string request)
    {
        dynamic jsonObj = JsonConvert.DeserializeObject(request.Trim().ToLower());

        var result = jsonObj != null && !string.IsNullOrEmpty(jsonObj.SelectToken("connectviahostname")?.ToString())
            ? jsonObj.SelectToken("connectviahostname").ToString()
            : "NA";

        return result;
    }



    public static dynamic ReplacePasswords(dynamic jsonProperties, string newPassword)
    {
        // Use LINQ to iterate over the properties and update those that match the "password" condition
        foreach (var property in jsonProperties)
        {
            if (property.Name.Contains("password", StringComparison.OrdinalIgnoreCase))
            {
                jsonProperties[property.Name] = newPassword;
            }
        }

        return jsonProperties;
    }

    public static dynamic ReplacePassword(dynamic jsonProperties, string propertyName, string newPassword)
    {
        if (!string.IsNullOrEmpty((string)jsonProperties[propertyName]))
        {
            var jsonToken = JToken.Parse(jsonProperties.ToString());
            var passwordToken = jsonToken.SelectToken(propertyName);
            passwordToken?.Replace(newPassword);
            jsonProperties[propertyName] = newPassword;
        }

        return jsonProperties;
    }

    public static dynamic ReplaceServerIpAndHostName(dynamic jsonProperties, string newIpAddress, string newHostName)
    {
        var jsonToken = JToken.Parse(jsonProperties.ToString());

        var ipAddressToken = jsonToken.SelectToken("IpAddress");

        var hostNameToken = jsonToken.SelectToken("HostName");

        hostNameToken?.Replace(newHostName);

        jsonProperties.HostName = newHostName;

        ipAddressToken?.Replace(newIpAddress);

        jsonProperties.IpAddress = newIpAddress;

        return jsonProperties;
    }

    public static bool GetJsonValueAsBool(string json, string jsonPath)
    {
        try
        {
            var jsonObject = JObject.Parse(json);
            return jsonObject.SelectToken(jsonPath)?.Value<bool>() ?? false;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public static string GetJsonValue(string json, string jsonPath)
    {
        try
        {
            var jsonObject = JObject.Parse(json);
            return jsonObject.SelectToken(jsonPath)?.ToString() ?? "NA";
        }
        catch
        {
            return null;
        }
    }

    public static List<string> GetJsonArrayValues(string json, string arrayPath, string objpath, string detailpath,
        string propertyPath)
    {
        var data = new List<string>();

        var jsonObject = JObject.Parse(json);
        var arrayToken = jsonObject.SelectToken(arrayPath) as JArray;

        foreach (var item in arrayToken!)
        {
            var serverdetails = item.SelectToken(objpath).ToString();
            var serverJsonObject = JObject.Parse(serverdetails);
            var propertyToken = serverJsonObject?.SelectToken(detailpath).ToString();
            var jsonString = JObject.Parse(propertyToken);
            var jsonValue = jsonString.SelectToken(propertyPath)!.ToString();

            data.Add(jsonValue);
        }

        return data;
    }

    public static string GetJsonStringBuilder(dynamic existLicenseKey, dynamic updateLicenseKey)
    {
        var existingArray = existLicenseKey.Split('*');

        var updatingArray = updateLicenseKey.Split('*');


        var sb = new StringBuilder(updateLicenseKey);

        for (var i = 0; i < existingArray.Length; i++)
        {
            var existingObject = existingArray[i];
            var updatingObject = updatingArray[i];
            if (i == 4)
            {
                var props = AddLicenseCount(existingArray[i], updatingArray[i]);
                sb.Replace(updatingArray[i], props);
            }
            else
            {
                if (existingObject.ToString() != updatingObject.ToString())
                {
                    int index = sb.ToString().IndexOf(updatingObject.ToString());
                    if (index >= 0 && index < sb.Length)
                        sb.Replace(existingArray[i], updatingArray[i]);
                    else
                        sb.Replace(existingArray[i], updatingArray[i]);
                }
            }
        }

        return sb.ToString();
    }

    public static int GenerateId()
    {
        var increment = AutoIncrement.Count == 0 ? 1 : AutoIncrement.LastOrDefault() + 1;
        AutoIncrement.Add(increment);
        return increment;
    }

    public static void ClearAutoIncrement()
    {
        AutoIncrement.Clear();
    }

    //License

    public static int GetLicenseJsonValue(string json, string jsonPath)
    {
        var jsonObject = JObject.Parse(json);
        return jsonObject.SelectToken(jsonPath)?.Value<int>() ?? 0;
    }

    public static dynamic AddLicenseCount(dynamic firstJsonString, string secondJsonString)
    {
        try
        {
            JObject firstJsonObject = JObject.Parse(firstJsonString);
            var secondJsonObject = JObject.Parse(secondJsonString);

            var isDatabase = firstJsonObject.SelectToken("isDatabase")?.Value<bool>() ?? false;


            if (isDatabase)
            {
                if (firstJsonObject["DatabaseDto"] is JObject firstDatabaseDto &&
                    secondJsonObject["DatabaseDto"] is JObject secondDatabaseDto)
                {
                    foreach (var property in firstDatabaseDto.Properties())
                    {
                        if (firstDatabaseDto[property.Name] is JArray firstArray &&
                            secondDatabaseDto[property.Name] is JArray secondArray)
                        {
                            foreach (var secondItem in secondArray)
                            {
                                var secondId = secondItem["id"]?.ToString();
                                var secondCount = secondItem["count"]?.ToObject<int>() ?? 0;

                                var matchingItem = firstArray.FirstOrDefault(x => x["id"]?.ToString() == secondId);
                                if (matchingItem != null)
                                {
                                    // Update count if ID matches
                                    var firstCount = matchingItem["count"]?.ToObject<int>() ?? 0;
                                    matchingItem["count"] = firstCount + secondCount;
                                }
                                else
                                {
                                    var secondType = secondItem["type"]?.ToString();

                                    var matchingOthers = firstArray.FirstOrDefault(x => x["type"]?.ToString() == secondType);

                                    var firstCount = matchingOthers!["count"]?.ToObject<int>() ?? 0;
                                    matchingOthers["count"] = firstCount + secondCount;
                                }
                            }
                        }
                    }
                }
            }



            foreach (var property in firstJsonObject)
            {
                var jsonPath = $"['{property.Key}']";

                if (jsonPath.ToLower().Contains("count"))
                {
                    var firstDatabaseCount = firstJsonObject.SelectToken(jsonPath)!.Value<int>();
                    var secondDatabaseCount = secondJsonObject.SelectToken(jsonPath)!.Value<int>();

                    var totalDatabaseCount = firstDatabaseCount + secondDatabaseCount;

                    firstJsonObject.SelectToken(jsonPath)!.Replace(new JValue(totalDatabaseCount));
                }
            }

            return firstJsonObject.ToString();
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.ToString());

            return null;
        }
    }

    public static dynamic SubtractLicenseCount(dynamic firstJsonString, List<LicenseInfo> licenseInfo)
    {
        JObject firstJsonObject = JObject.Parse(firstJsonString);
        //JObject secondJsonObject = JObject.Parse(secondJsonString);

        var firstDatabaseCount = firstJsonObject.SelectToken("databaseCount")!.Value<int>();
        //int secondDatabaseCount = secondJsonObject.SelectToken("databaseCount").Value<int>();

        var totalDatabaseCount = firstDatabaseCount - 10;

        firstJsonObject.SelectToken("databaseCount")!.Replace(new JValue(totalDatabaseCount));

        return firstJsonObject.ToString();
    }

    public static string GetJsonKeysContainingOracleSid(string json, string sid)
    {
        json = json.Trim().TrimStart().TrimEnd();
        var jSonObject = JObject.Parse(json);
        var jSonList = new List<string>
            { "DatabaseName", "DatabaseSID", "Database", "SID", "OracleSID", "InstanceName" };

        var jSonValue = jSonList.Where(item => jSonObject.ContainsKey(item))
            .Select(item => new KeyValuePair<string, JToken>(item, jSonObject[item]))
            .FirstOrDefault();
        if (jSonValue.Key.IsNullOrWhiteSpace()) return jSonObject.ToString();

        jSonObject[jSonValue.Key.Trim()] = sid;

        return jSonObject.ToString();
    }

    public static string GetJsonDatabaseSidValue(string json)
    {
        if (!string.IsNullOrEmpty(json))
        {
            json = json.Trim().TrimStart().TrimEnd();
            var jSonObject = JObject.Parse(json);
            var jSonList = new List<string>
                { "DatabaseName", "DatabaseSID", "Database", "SID", "OracleSID", "InstanceName" };

            var jSonValue = jSonList.Where(item => jSonObject.ContainsKey(item))
                .Select(item => new KeyValuePair<string, JToken>(item, jSonObject[item]))
                .FirstOrDefault();

            var result = !jSonValue.Key.IsNullOrWhiteSpace()
                ? jSonValue.Value?.ToString().IsNotNullOrWhiteSpace() == true
                    ? jSonValue.Value.ToString()
                    : "NA"
                : "NA";

            return result;
        }

        return json;
    }

    public static TimeSpan GetTimeSpans(string value)
    {
        var pattern = @"^((\+\d{2})? (\d{2}:\d{2}:\d{2})|(\d+\.\d{2}:\d{2}:\d{2})|(\d{2}:\d{2}:\d{2}))$";

        var match = Regex.Match(value, pattern);

        if (match.Success)
        {
            // Handle format like "+01 10:00:00"
            if (match.Groups[2].Success)
            {
                // Extract the offset hours, hours, minutes, and seconds
                var parts = match.Value.Split(' ');

                // Extract the offset hours
                var offsetHours = int.Parse(parts[0].Substring(1)); // Remove the '+' sign

                // Extract the time portion (hours, minutes, seconds)
                var timeParts = parts[1].Split(':');
                var hours = int.Parse(timeParts[0]);
                var minutes = int.Parse(timeParts[1]);
                var seconds = int.Parse(timeParts[2]);

                // Create a TimeSpan object with the extracted values
                var timeSpan = new TimeSpan(offsetHours, hours, minutes, seconds);


                return timeSpan;
            }

            if (match.Groups[4].Success)
            {
                // Handle formats like "1.02:16:00" or "1.01:09:20" or "1.04:08:00"
                var dayAndTimeParts = match.Groups[4].Value.Split(':', '.');

                if (dayAndTimeParts.Length == 4)
                {
                    var days = int.Parse(dayAndTimeParts[0]);
                    var hours = int.Parse(dayAndTimeParts[1]);
                    var minutes = int.Parse(dayAndTimeParts[2]);
                    var seconds = int.Parse(dayAndTimeParts[3]);

                    return new TimeSpan(days, hours, minutes, seconds);
                }
            }
            else if (match.Groups[5].Success)
            {
                // Handle formats like "00:00:00" or "1.02:16:00"
                var timeSpan = TimeSpan.Parse(match.Groups[5].Value);
                return timeSpan;
            }
        }

        return TimeSpan.Zero;
    }

    public static int ConvertToMinutes(string timestamp)
    {
        var pattern = @"^((\+\d{2})? (\d{2}:\d{2}:\d{2})|(\d+\.\d{2}:\d{2}:\d{2})|(\d{2}:\d{2}:\d{2}))$";

        var match = Regex.Match(timestamp, pattern);

        if (match.Success)
        {
            if (match.Groups[2].Success)
            {
                // Handle format like "+01 10:00:00"
                var offsetHours = int.Parse(match.Groups[2].Value.Substring(1));
                var timeSpan = TimeSpan.Parse(match.Groups[3].Value);
                return (int)(timeSpan.TotalMinutes + offsetHours * 60);
            }

            if (match.Groups[4].Success)
            {
                // Handle formats like "1.02:16:00" or "1.01:09:20" or "1.04:08:00"
                var dayAndTimeParts = match.Groups[4].Value.Split(':', '.');

                if (dayAndTimeParts.Length == 4)
                {
                    var days = int.Parse(dayAndTimeParts[0]);
                    var hours = int.Parse(dayAndTimeParts[1]);
                    var minutes = int.Parse(dayAndTimeParts[2]);
                    var seconds = int.Parse(dayAndTimeParts[3]);

                    return (int)(days * 24 * 60 + hours * 60 + minutes + seconds / 60.0);
                }
            }
            else if (match.Groups[5].Success)
            {
                // Handle formats like "00:00:00" or "1.02:16:00"
                var timeSpan = TimeSpan.Parse(match.Groups[5].Value);
                return (int)timeSpan.TotalMinutes;
            }
        }

        return -1; // Return -1 if the conversion fails
    }

    public static double ConvertToSeconds(string timeValue)
    {
        if (string.IsNullOrEmpty(timeValue)) throw new ArgumentNullException(nameof(timeValue));

        if (TimeSpan.TryParse(timeValue, out var timeSpan)) return timeSpan.TotalSeconds;

        var parts = timeValue.Split(new[] { ' ', ':', '.', '+' }, StringSplitOptions.RemoveEmptyEntries);

        switch (parts.Length)
        {
            case 4:
            {
                var days = int.Parse(parts[0]);
                var hours = int.Parse(parts[1]);
                var minutes = int.Parse(parts[2]);
                var seconds = int.Parse(parts[3]);

                return days * 24 * 3600 + hours * 3600 + minutes * 60 + seconds;
            }

            case 3:
            {
                var hours = int.Parse(parts[0]);
                var minutes = int.Parse(parts[1]);
                var seconds = int.Parse(parts[2]);

                return hours * 3600 + minutes * 60 + seconds;
            }
            case 2:
            {
                var hours = int.Parse(parts[0]);
                var minutes = int.Parse(parts[1]);

                return hours * 3600 + minutes * 60;
            }
            default:
                throw new FormatException("Invalid time format.");
        }
    }

     //For Server bulkPaswordUpdate
  public static string GetJsonObjectByKeyValue(string properties, string key, List<string> values, string newPassword)
{
        if (string.IsNullOrWhiteSpace(properties))
            return properties;

        var jsonProperties = JsonConvert.DeserializeObject<Dictionary<string, object>>(properties);

        if (!jsonProperties.ContainsKey("ConfigureSubstituteAuthentication") || jsonProperties["ConfigureSubstituteAuthentication"] == null)
            return properties;

        var jsonArray = jsonProperties["ConfigureSubstituteAuthentication"].ToString();

        if (string.IsNullOrWhiteSpace(jsonArray))
            return properties;

        var jsonObjects = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(jsonArray);

        if (jsonObjects == null)
            return properties;

        foreach (var obj in jsonObjects)
        {
            if (obj.ContainsKey(key) && values.Contains(obj[key]?.ToString()?.Trim()))
            {
                var passwordKeys = obj.Keys.Where(k => k.IndexOf("password", StringComparison.OrdinalIgnoreCase) >= 0).ToList();

                foreach (var passwordKey in passwordKeys)
                {
                    obj[passwordKey] = newPassword;
                }
            }
        }

        jsonProperties["ConfigureSubstituteAuthentication"] = jsonObjects;

        return JsonConvert.SerializeObject(jsonProperties, Formatting.Indented);
       
    }

}