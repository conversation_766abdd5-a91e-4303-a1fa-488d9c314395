﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;

namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowPermissionRepository : BaseRepository<WorkflowPermission>, IWorkflowPermissionRepository
{
    private readonly IConfiguration _configuration;
    private readonly ApplicationDbContext _dbContext;

    public WorkflowPermissionRepository(ApplicationDbContext dbContext, IConfiguration configuration) :
        base(dbContext)
    {
        _dbContext = dbContext;
        _configuration = configuration;
    }

    public async Task<List<WorkflowPermission>> GetWorkflowPermissionByUserIdAsync(string userGroupId)
    {
        return await _dbContext.WorkflowPermissions.Active().Where(x => x.UserProperties.Contains(userGroupId)).ToListAsync();    
    }
}