namespace ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetDetail;

public class
    GetCyberJobManagementDetailsQueryHandler : IRequestHandler<GetCyberJobManagementDetailQuery,
        CyberJobManagementDetailVm>
{
    private readonly ICyberJobManagementRepository _cyberJobManagementRepository;
    private readonly IMapper _mapper;

    public GetCyberJobManagementDetailsQueryHandler(IMapper mapper,
        ICyberJobManagementRepository cyberJobManagementRepository)
    {
        _mapper = mapper;
        _cyberJobManagementRepository = cyberJobManagementRepository;
    }

    public async Task<CyberJobManagementDetailVm> Handle(GetCyberJobManagementDetailQuery request,
        CancellationToken cancellationToken)
    {
        var cyberJobManagement = await _cyberJobManagementRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(cyberJobManagement, nameof(Domain.Entities.CyberJobManagement),
            new NotFoundException(nameof(Domain.Entities.CyberJobManagement), request.Id));

        var cyberJobManagementDetailDto = _mapper.Map<CyberJobManagementDetailVm>(cyberJobManagement);

        return cyberJobManagementDetailDto;
    }
}