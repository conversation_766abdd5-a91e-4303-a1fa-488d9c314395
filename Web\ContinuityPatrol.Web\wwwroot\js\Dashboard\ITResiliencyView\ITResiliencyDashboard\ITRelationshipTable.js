﻿let global = []
let globalDrTypes = []
let monitorConfigMap = {};
let monitorType = ''
let ocpProp = ''

fetch("/json/MonitorTypeData.json")
    .then(response => response.json())
        .then(data => {
            monitorConfigMap = data;
            
    })
    .catch(error => {
        console.error("Error loading monitorConfig.json:", error);
    });
const mergeIpWithPort = (flat, ipKey, portKey) => {
    const ip = flat[ipKey];
    const port = flat[portKey];
    if (ip || port) {
        flat[ipKey] = [ip, port].filter(Boolean).join(',');
    }
}

async function DataSyncTableList(dataSyncJobIds) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: "/Monitor/Monitoring/GetFastMonitorList",
            method: 'GET',
            data: {
                dataSyncJobIds: JSON.stringify(dataSyncJobIds)
            },
            dataType: 'json',
            success: function (res) {
                resolve(res?.data || []);
            },
            error: function (err) {
                console.error("AJAX error:", err);
                reject(err);
            }
        });
    });
}
function commonRelationshipforMonitor(infraObjectId, moniterType, glob) {
    //console.log(moniterType, 'monitorType');
    $.ajax({
        url: "/Dashboard/ITResiliencyView/GetMonitoringDetailsByInfraObjectId/",
        method: 'GET',
        data: { infraObjectId },
        dataType: 'json',
        success: function (result) {
            const data = result?.data;
            global = data;
            
            const properties = JSON?.parse(data?.properties || '{}'); 
            //console.log(properties, 'properties');
            ocpProp = properties;
            monitorType = (moniterType || '').trim();

            const config = monitorConfigMap[monitorType];
            if (!config) return;

            const prModel = getNestedValue(properties, config?.Path?.PR);
            const drRawArray = getNestedValue(properties, config?.Path?.DR);

            const drTypeLabels = Array.isArray(drRawArray)
                ? drRawArray?.map(d => d?.Type || 'DR')  // ✅ Collect "DR", "NearDR", etc.
                : [drRawArray?.Type || 'DR'];
            globalDrTypes = drTypeLabels

            const flatPR = flattenMonitoringModel(prModel);
            Object.keys(flatPR)?.forEach(k => flatPR[k] = sanitizeValue(flatPR[k]));


            const drObjects = Array.isArray(drRawArray)
                ? drRawArray?.map(d => ({
                    type: d?.Type || 'DR',
                    flatModel: flattenMonitoringModel(d?.MonitoringModel || d)
                }))
                : [{
                    type: drRawArray?.Type || 'DR',
                    flatModel: flattenMonitoringModel(drRawArray)
                }];

            const flatDR = flattenMonitoringModel(drObjects);

            drObjects?.forEach(obj => {
                Object.keys(obj?.flatModel)?.forEach(k => {
                    obj.flatModel[k] = sanitizeValue(obj?.flatModel[k]);
                });
            });

            // Special handling for RSync
            if (monitorType?.toLowerCase() === "rsyncappreplication") {
                drObjects?.forEach(dr => {
                    const rsyncModelArray = dr?.flatModel;
                    const entries = Object.values(rsyncModelArray)?.filter(item =>
                        item?.RSyncJobId && item?.SourceIP && item?.DestinationIP
                    );

                    if (entries?.length) {
                        const aggregated = {};
                        entries?.forEach(item => {
                            Object.entries(item)?.forEach(([key, value]) => {
                                aggregated[key] = aggregated[key]
                                    ? aggregated[key] + ', ' + value
                                    : value;
                            });
                        });

                        // Merge aggregated result
                        Object.assign(dr.flatModel, aggregated);
                    }
                });
            }           

            if (monitorType?.toLowerCase() === "robocopy") {
                const roboCopyArray = drRawArray?.[0]?.MonitoringModel?.RoboCopyMonitorModel || [];
                const Count = Array.isArray(roboCopyArray) ? roboCopyArray?.length : 0;
                flatPR["MonitoringMode"] = Count.toString();
                drObjects.forEach(dr => {
                    dr.flatModel["MonitoringMode"] = (dr?.type === 'PR') ? Count.toString() : '';
                });          
            }
            if (monitorType?.toLowerCase() === "srm") {

                mergeIpWithPort(flatPR, 'PR_VCenter_IPAddress', 'PR_VCenter_Port');
                mergeIpWithPort(flatPR, 'PR_SRMServer_IPAddress', 'PR_SRMServer_Port');
                mergeIpWithPort(flatDR?.flatModel, 'DR_VCenter_IPAddress', 'DR_VCenter_Port');
                mergeIpWithPort(flatDR?.flatModel, 'DR_SRMServer_IPAddress', 'DR_SRMServer_Port');
            }
            if (monitorType?.toLowerCase() === "datasyncappreplication") {
                const jobIds = drRawArray[0]?.MonitoringModel?.DataSyncJobIds || [];

                if (Array.isArray(jobIds) && jobIds?.length > 0) {
                    DataSyncTableList(jobIds).then(dataGlob => {
                        drObjects?.forEach(dr => {
                            const model = dr?.flatModel;
                            if (!model) return;

                            const aggregated = {};

                            dataGlob?.forEach(item => {
                                Object.entries(item || {})?.forEach(([key, value]) => {
                                    if (value !== null && value !== undefined && value !== '') {
                                        aggregated[key] = aggregated[key]
                                            ? aggregated[key] + ', ' + value
                                            : value;
                                    }
                                });
                            });

                            Object.assign(model, aggregated);
                        });

                        bindTabs(flatPR, drObjects, monitorType);
                    });

                    return; 
                }
            }
            if (monitorType?.toLowerCase() === "mssqlalwaysonavailabilitygroup") {
                if (flatPR) {
                    flatPR["Server_Name"] = properties?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.ServerName || 'NA';
                    flatPR["Server_IpAddress"] = properties?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.IpAddress || 'NA';
                }
                drObjects?.forEach((dr, i) => {
                    const originalData = Array.isArray(drRawArray) ? drRawArray[i] : drRawArray;
                    const list = originalData?.AlwaysOnAvailabilityGroupMonitoringDetailsList || [];

                    const aggregated = {};

                    list?.forEach(entry => {
                        const ag = entry?.AvailabilityGroupMonitoring || {};

                        aggregated["AvailabilityGroupNameAvailability_Group_Name"] = appendValue(aggregated["Availability_Group_Name"], ag?.AvailabilityGroupName);
                        aggregated["Availability_Group_Role"] = appendValue(aggregated["Availability_Group_Role"], ag?.AvailabilityGroupRole);
                        aggregated["Availability_Mode"] = appendValue(aggregated["Availability_Mode"], ag?.ReplicaMode);
                        aggregated["Availability_Group_Connnected_State"] = appendValue(aggregated["Availability_Group_Connnected_State"], ag?.AvailabilityGroupConnnectedState);

                        aggregated["Server_Name"] = appendValue(aggregated["Server_Name"], entry?.ServerName);
                        aggregated["Server_IpAddress"] = appendValue(aggregated["Server_IpAddress"], entry?.IpAddress);
                    });

                    Object.assign(dr.flatModel, aggregated);
                });
            }
            if (monitorType?.toLowerCase() === "oraclerac") {
                const prModel = getNestedValue(properties, config?.Path?.PR); 
                const drModel = getNestedValue(properties, config?.Path?.DR); 
                handleOracleRac(prModel, drModel);                
            }

            bindTabs(flatPR, drObjects, monitorType);
        }
    });
}
function renderNodeSelector(prModel) {
    const $select = $('<select id="nodeSelect" class="form-select w-100"><option disabled selected>Select Node Name</option></select>');
    prModel?.forEach((item, index) => {
        const isSelected = index === 0;
        const option = $('<option>')
            .val(index)
            .text(item?.Node || `Node ${index + 1}`)
            .prop('selected', isSelected);
        $select.append(option);
    });

    $('#nodeRelationCont').empty().append($select);
}
function handleOracleRac(prModel, drModelArray) {
    renderNodeSelector(prModel); 

    setTimeout(() => {
        bindNodeData(prModel, drModelArray); 
        $('#nodeSelect').trigger('change'); 
    }, 0);
}
function bindNodeData(prModel, drModelArray) {
   
    bindOracleRacNodeData(prModel, drModelArray, 0);

    $('#nodeSelect').on('change', function () {
        const selectedIndex = parseInt($(this).val());
        bindOracleRacNodeData(prModel, drModelArray, selectedIndex);
    });
}
function bindOracleRacNodeData(prModel, drModelArray, index) {
    
    const prData = prModel[index]?.PrModel?.PrMonitoringModel || {};
    const nodeName = prModel[index]?.Node;
    const matchingDr = drModelArray?.find((drNode) => drNode?.Node?.toLowerCase() === nodeName?.toLowerCase());
    const drData = (matchingDr?.DrModels || [])?.map(dr => dr?.MonitoringModel);

    const flatPR = flattenMonitoringModel(prData);
    flatPR["PR_Replication_Mode"] = ocpProp?.PR_Replication_Mode || 'NA';

    const drObjects = drData?.map(d => ({
        type: 'DR',
        flatModel: {
            ...flattenMonitoringModel(d),
            DR_Replication_Mode: ocpProp?.DR_Replication_Mode || 'NA'
        }
        //flatModel: flattenMonitoringModel(d)
    }));

    bindTabs(flatPR, drObjects, "OracleRac");
}
function appendValue(current, incoming) {
    return current ? current + ", " + incoming : incoming;
}
function sanitizeValue(val) {
    if (!val || typeof val !== 'string') return val || "NA";
    const trimmed = val.trim();
    if (!trimmed || trimmed === "," || trimmed === ",," || trimmed === "-,-") {
        return "NA";
    }
    return trimmed;
}
function flattenMonitoringModel(model) {
    const flat = {};
    Object.entries(model || {})?.forEach(([key, val]) => {
        if (typeof val === 'object' && val !== null) {
            Object.entries(val)?.forEach(([nestedKey, nestedVal]) => {
                flat[nestedKey] = nestedVal;
            });
        } else {
            flat[key] = val;
        }
    });
    return flat;
}
function getIconHtml(key, value) {
    value = value?.toLowerCase()?.trim();
    //const prStatus = global?.prServerStatus?.toLowerCase();
    //const drStatus = global?.drServerStatus?.toLowerCase();
    const PRServerArray = glob?.serverDto?.filter((d) => d?.serverType?.toLowerCase()?.includes('pr'));
    const DRServerArray = glob?.serverDto?.filter((d) => d?.serverType?.toLowerCase()?.includes('dr'));
    
    const prStatus = PRServerArray[0]?.status?.toLowerCase();
    const drStatus = DRServerArray[0]?.status?.toLowerCase();
   
    const iconMap = [
        { check: () => ["na", "not allowed", "no","n/a"]?.includes(value), icon: 'cp-disable', color: 'text-danger' },
        { check: () => value?.includes("synchr"), icon: 'cp-refresh', color: 'text-success' },
        { check: () => value?.includes("asynch"), icon: 'cp-refresh', color: 'text-danger' },
        { check: () => value === "healthy", icon: 'cp-health-success', color: 'text-success' },
        { check: () => value === "unhealthy", icon: 'cp-health-error', color: 'text-danger' },
        { check: () => value === "online", icon: 'cp-online', color: 'text-success' },
        { check: () => value === "offline", icon: 'cp-offline', color: 'text-danger' },
        { check: () => value?.includes("primary"), icon: 'cp-list-prsite', color: 'text-primary' },
        { check: () => value === "error", icon: 'cp-fail-back', color: 'text-danger' },
        { check: () => value === "valid", icon: 'cp-success', color: 'text-success' },
        { check: () => value?.includes("secondary") || value?.includes("dr site"), icon: 'cp-dr', color: 'text-info' },
        { check: () => value === "up", icon: 'cp-up-linearrow ', color: 'text-success' },
        { check: () => value === "down", icon: 'cp-down-linearrow', color: 'text-danger' },
        { check: () => value?.includes("connected"), icon: 'cp-connected', color: 'text-success' },
        { check: () => value === "disconnected", icon: 'cp-disconnected', color: 'text-danger' },
        { check: () => value === "disabled", icon: 'cp-disables', color: 'text-danger' },
        { check: () => value === "enabled", icon: 'cp-enables', color: 'text-success' },
        { check: () => value === "streaming" || key?.includes("MirroringState") || key?.includes("ClusterInfrastructure"), icon: 'cp-refresh', color: 'text-primary' },
        { check: () => value?.includes("running"), icon: 'cp-reload cp-animate', color: 'text-success' },
        { check: () => key?.includes("Server_Edition") || key.includes("Server_Name") || key?.includes('ServerName') || key?.includes('OpenShiftClusterFQDN'), icon: 'cp-stand-server', color: 'text-primary' },
        { check: () => key?.includes("Protection") || key?.includes("Recovery_Plan_And_State"), icon: 'cp-protection-mode', color: 'text-primary' },
        { check: () => key?.includes("AvailabilityGroupName") || key?.includes("Recovery_Plan_And_State"), icon: 'cp-group', color: 'text-primary' },
        { check: () => key?.includes("DistributionVersion"), icon: 'cp-version', color: 'text-primary' },
        { check: () => key?.includes("Version"), icon: 'cp-version', color: 'text-primary' },
        { check: () => key?.includes("ProjectsName"), icon: 'cp-form-name', color: 'text-success' },
        { check: () => key?.includes("Last_Backup_Transaction_Log") || key?.includes("last_copied_file") || key?.includes("LastLSN_restored") || key?.includes("LastLSN_copied") || key?.includes("LastLSN_backup"), icon: 'cp-primary', color: 'text-primary' },
        { check: () => key?.includes("Server_NetworkAddress"), icon: 'cp-fal-server', color: 'text-primary' },
        { check: () => key?.includes("StateDescription"), icon: 'cp-relationship-state', color: 'text-success' },
        { check: () => key?.includes("replicationType"), icon: 'cp-replication-type', color: 'text-primary' },
        { check: () => key?.includes("DatabaseClusterStatus") || key?.includes("OpreationMode"), icon: 'cp-cluster-database', color: 'text-primary' },
        { check: () => key?.includes("ClusterResourceName"), icon: 'cp-cluster-database', color: 'text-success' },
        { check: () => key?.includes("Database"), icon: 'cp-database', color: 'text-primary' },
        { check: () => key?.includes("RecoveryStatus"), icon: 'cp-reload cp-animate', color: 'text-success' },
        { check: () => key?.includes("ReplicationStatus"), icon: 'cp-Stopped', color: 'text-primary' },
        { check: () => key?.includes("StorageId"), icon: 'cp-storage-name', color: 'text-primary' },
        { check: () => key?.includes("lastHeartbeatMessage"), icon: 'cp-Stopped', color: 'text-success' },
        { check: () => key?.includes("CurrentWalLsn") || key?.includes("Destination"), icon: 'cp-file-location', color: 'text-primary' },
        { check: () => key?.includes("UnrestoredLog"), icon: 'cp-online', color: 'text-primary' },
        { check: () => key?.includes("RecoveryRate"), icon: 'cp-success-rate', color: 'text-success' },
        { check: () => key?.includes("LastReplicationResult") || key?.includes("ReplicationFailureFirstRecordedTime"), icon: 'cp-success', color: 'text-primary' },
        { check: () => key?.includes("LogGenerateRate") || key?.includes("SentRate"), icon: 'cp-file-edits', color: 'text-primary' },
        { check: () => key?.includes("UnsentLog"), icon: ' cp-control-file-type', color: 'text-warning' },
        { check: () => key?.includes("DBRole"), icon: 'cp-database-role', color: 'text-primary' },
        { check: () => key?.includes("health"), icon: 'cp-health', color: 'text-success' },
        { check: () => key?.includes("memberID"), icon: 'cp-id', color: 'text-primary' },
        { check: () => key?.includes("CurrentPriority"), icon: 'cp-priority', color: 'text-success' },
        { check: () => key?.includes("replicaSetName") || key?.toLowerCase()?.includes("sourcepath") || key?.toLowerCase()?.includes("destinationpath"), icon: 'cp-report-path', color: 'text-primary' },
        { check: () => key?.includes("replicaSetName") || key?.includes("DestinationPath"), icon: 'cp-replication-source', color: 'text-success' },
        { check: () => key?.includes("TransactionPerSecond"), icon: 'cp-timer-meter', color: 'text-primary' },       
        { check: () => key?.includes("LastWalReceiveLsn") || key?.includes("Replication_Connect_State"), icon: 'cp-connected', color: 'text-primary' },
        { check: () => key?.includes("DomainControllerName") || key?.includes("ADReplicationSite"), icon: 'cp-site-names', color: 'text-primary' },
        { check: () => key?.includes("LastWalReplayLsnDR") || key?.includes("TransactionDelay"), icon: 'cp-time', color: 'text-primary' },
        { check: () => key?.includes("Log_sequence"), icon: 'cp-log-archive-config', color: 'text-primary' },
        { check: () => key?.includes("Replication_Mode"), icon: 'cp-replication-on', color: 'text-primary' },
        { check: () => key?.includes("Dataguard_status"), icon: 'cp-dataguard-status', color: 'text-primary' },
        { check: () => key?.includes("Service_Name") || key?.includes("Services"), icon: 'cp-service', color: 'text-primary' },
        { check: () => key?.includes("Volume"), icon: 'cp-volume-adjustment', color: 'text-primary' },
        { check: () => key?.includes("DomainName"), icon: 'cp-command-center', color: 'text-primary' },
        { check: () => key?.includes("State"), icon: 'cp-relationship-state', color: 'text-primary' },
        { check: () => key?.includes("PR_Datalag") || key?.includes("DataLag"), icon: 'cp-time', color: 'text-primary' },
        { check: () => key?.includes("Lag"), icon: 'cp-data-lag', color: 'text-primary' },        
        { check: () => key?.includes("Status"), icon: 'cp-ohas-status', color: 'text-primary' },
        { check: () => key?.includes("LastReplicationResult"), icon: 'cp-snap-file', color: 'text-primary' },
       
        { check: () => key?.includes("LastReplicationSuccess"), icon: 'cp-time', color: 'text-success' },
        { check: () => key?.includes("LastReplicationAttempt"), icon: 'cp-estimated-time', color: 'text-success' },
        { check: () => key?.includes("TotalFilesSize") || key?.includes("TotalTransferfileSize") || key?.includes("totalFilesSize") || key?.includes("skippedFilesCount"), icon: 'cp-file-size', color: 'text-primary' },
        { check: () => key?.includes("ConsecutiveReplicationFailures") || key?.includes("ReplicationPartnerName") || key?.includes("TotalNumberoffiles") || key?.includes("NumberOfRegFilesTransfer") || key?.includes("totalFilesCount"), icon: 'cp-files', color: 'text-primary' },
        { check: () => key?.includes("PR_DataLagInSize"), icon: 'cp-transport-lag', color: 'text-primary' },
        { check: () => key?.includes("Master_Log_File") || key?.includes("Relay_Master_Log_File") || key?.includes("Master_Log_Position") || key?.includes("Exec_Master_Log_Position"), icon: 'cp-log-file-name', color: 'text-primary' }
    ];
   
        if (key?.includes('Server_IpAddress') || key?.includes("Server_HostName") || key?.includes('IpAddress') || key?.includes("IPAddress")) {
            if (prStatus === 'down' || drStatus === 'down') {
                return '<i class="cp-down-linearrow me-1 text-danger"></i>';
            } else if (prStatus === 'up' || drStatus === 'up') {
                return '<i class="cp-up-linearrow me-1 text-success"></i>';
            } else if (prStatus === 'pending' || drStatus === 'pending') {
                return '<i class="cp-pending me-1 text-warning"></i>';
            }
        }
    
    for (const { check, icon, color } of iconMap) {
        if (check()) return `<i class="${icon} me-1 ${color}"></i>`;
    }

    return `<i class=""></i>`;
}
function getNestedValue(obj, path) {
    return path?.reduce((acc, key) => acc?.[key], obj);
}

function renderTable(prKeys, drKeys, label, prDetails, drDetailsArray, labels = {}, footerRowHtml = '', drTypeLabels = []) {
    const prOnlyKeys = ['PR_Datalag', 'replicationType'];

    // Normalize drDetailsArray to array of flatModels
    const drArray = Array.isArray(drDetailsArray)
        ? drDetailsArray : Object.values(drDetailsArray || {}).filter(obj => obj?.flatModel);

    const isMultiDR = drArray?.length > 1;

    const rows = prKeys?.map((prKey, i) => {
        const drKey = drKeys[i];
        const rowLabel = labels[prKey] || labels[drKey] || prKey.replace(/^PR_?/, '').replace(/_/g, ' ');

        const prValue = prDetails[prKey] ?? 'NA';
        const prIcon = getIconHtml(prKey, prValue);

        let drCells = '';

        if (prOnlyKeys?.includes(prKey)) {
            drCells = isMultiDR
                ? drArray?.map(() => `<td></td>`).join('')
                : `<td></td>`;
        } else {
            drCells = drArray?.map(drObj => {
                const drValue = drObj?.flatModel?.[drKey] ?? 'NA';
                const drIcon = getIconHtml(drKey, drValue);
                return `<td title="${drValue}">${drIcon}${drValue}</td>`;
            }).join('');
        }

        return `
            <tr>
                <td>${rowLabel}</td>
                <td title="${prValue}">${prIcon}${prValue}</td>
                ${drCells}
            </tr>
        `;
    }).join('');

    const drHeaderColumns = monitorType !== 'SRM'
        ? (isMultiDR
            ? drTypeLabels?.map(type => `<th>${type}</th>`).join('')
            : `<th>DR</th>`)
        : `<th>Recovery Site</th>`;

    const prHead = monitorType !== 'SRM'
        ? `<th>Primary</th>`
        : `<th>Protected Site</th>`;

    return `
        <table class="table table-hover mb-0 table-sm" style="table-layout:fixed">
            <thead style="position: sticky;top: 0px;z-index: 1;">
                <tr>
                    <th>${label}</th>
                    ${prHead}
                    ${drHeaderColumns}
                </tr>
            </thead>
            <tbody>
                ${rows}
                ${footerRowHtml}
            </tbody>
        </table>
    `;
}
function bindTabs(prDetails, drDetails, monitorType) {
    const config = monitorConfigMap[monitorType];
    if (!config?.tableData?.length) return;   
    const monitor = global?.monitorServiceDetails || [];
    const isServiceUpdate = monitor?.[0]?.isServiceUpdate?.toLowerCase() ?? '';
    const workflowName = monitor?.[0]?.workflowName ?? '';
    const footerHtml = generateMonitorServiceDetailsRow(workflowName, isServiceUpdate, monitor);

    // GoldenGate custom renderer
    const isGoldenGate = monitorType?.toLowerCase() === 'goldengatereplication';

    const tabHtmlList = config?.tableData?.map((tab) => {
        const prKeysBlock = tab?.keys?.find(k => k?.type === 'PR') || {};
        const drKeysBlock = tab?.keys?.find(k => k?.type === 'DR') || {};

        const prKeys = prKeysBlock?.keys?.map(k => k?.key) || [];
        const drKeys = drKeysBlock?.keys?.map(k => k?.key) || [];

        const labelMap = {};
        [...(prKeysBlock?.keys || []), ...(drKeysBlock?.keys || [])]?.forEach(k => {
            if (k?.key && k?.label) {
                labelMap[k.key] = k?.label;
            }
        });

        const title = monitorType !== 'SRM' ? (tab?.type === 'replication' ? 'Replication Monitor' : 'Component Monitor') : (tab?.type === 'replication' ? 'Protection Group & Recovery Plan Monoitoring' : 'Site Level Monitoring');
        const includeFooter = tab?.type === 'component' ? footerHtml : '';

        const prDataMap = { ...prDetails };
        const drDataMap = { ...drDetails };

        if (tab?.type === 'replication') {
            const replicationType = global?.replicationType || 'NA';
            prDataMap["replicationType"] = replicationType;
            drDataMap["replicationType"] = '';
        }

        // GoldenGate custom profile tab logic
        if (isGoldenGate && tab?.type === 'replication') {
            const prRepl = prDetails?.PRReplicationDetails || [];
            const drArray = Array.isArray(drDetails)
                ? drDetails : Object.values(drDetails || {}).filter(obj => obj?.flatModel);

            let rows = '';
            prRepl.forEach((item, idx) => {
                rows += '<tr>';
                if (idx === 0) rows += `<td rowspan="${prRepl.length}">Primary</td>`;
                rows += `<td>${item?.Program || ''}</td><td>${item?.Group || ''}</td><td>${item?.Status || ''}</td><td>${item?.LagAtCheckPoint || ''}</td>`;
                rows += '</tr>';
            });

            drArray?.forEach(dr => {
                const drDetails = dr?.flatModel?.ReplicationDetails || [];
                drDetails?.forEach((item, idx) => {
                    rows += '<tr>';
                    if (idx === 0) rows += `<td rowspan="${drDetails.length}">${dr?.type || 'DR'}</td>`;
                    rows += `<td>${item?.Program || ''}</td><td>${item?.Group || ''}</td><td>${item?.Status || ''}</td><td>${item?.LagAtCheckPoint || ''}</td>`;
                    rows += '</tr>';
                });
            });

            return `
                <table class="table mb-0 align-middle" style="table-layout:fixed">
                    <thead>
                        <tr>
                            <th>Server Type</th><th>Program</th><th>Group</th><th>Status</th><th>Lag at checkpoint</th>
                        </tr>
                    </thead>
                    <tbody>${rows}</tbody>
                </table>
            `;
        }

        return renderTable(
            prKeys,
            drKeys,
            title,
            prDataMap,
            drDataMap,
            labelMap,
            includeFooter,
            globalDrTypes
        );
    });
    let finalHtml = '';

    if (monitorType?.toLowerCase() === "openshift") {
        const cluster = ocpProp?.ClusterMonitoring || {};
        const configBlock = monitorConfigMap[monitorType]?.tableData?.find(tab => tab?.type === 'component');
        const prKeysBlock = configBlock?.keys?.find(k => k?.type === 'PR');
        const keys = prKeysBlock?.keys || [];

        const rows = keys?.map(({ key, label }) => {
            const value = cluster?.[key] ?? 'NA';
            const icon = getIconHtml(key, value);
            return `
            <tr>
                <td>${label}</td>
                <td title="${value}">${icon}${value}</td>
            </tr>
        `;
        }).join('');

        finalHtml = `
        <div class="tab-content mt-2">
            <div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel">
                <table class="table table-hover mb-0 table-sm" style="table-layout:fixed">
                    <thead style="position: sticky;top: 0px;">
                        <tr>
                            <th>Component Monitor</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${rows}
                    </tbody>
                </table>
            </div>
            <div class="tab-pane fade" id="profile-tab-pane" role="tabpanel"></div>
        </div>
    `;
    } else {
        finalHtml = `
        <div class="tab-content mt-2">
            <div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel">
                ${tabHtmlList[0] || ''}
            </div>
            <div class="tab-pane fade" id="profile-tab-pane" role="tabpanel">
                ${tabHtmlList[1] || ''}
            </div>
        </div>
    `;
    }

    // Final render
    $("#infraobjectalldata").html(finalHtml);
}