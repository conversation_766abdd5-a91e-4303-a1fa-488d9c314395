using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ReportFixture : IDisposable
{
    public List<Report> ReportPaginationList { get; set; }
    public List<Report> ReportList { get; set; }
    public Report ReportDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public ReportFixture()
    {
        var fixture = new Fixture();

        ReportList = fixture.Create<List<Report>>();

        ReportPaginationList = fixture.CreateMany<Report>(20).ToList();

        ReportDto = fixture.Create<Report>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
