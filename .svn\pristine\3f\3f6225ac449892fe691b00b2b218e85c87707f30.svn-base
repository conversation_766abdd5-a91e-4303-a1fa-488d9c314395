﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/treeview.css" rel="stylesheet" />
<div class="page-content px-3">
    <div>
        <div class="d-flex align-items-center justify-content-between mb-2">
            <h6 class="page_title">
                <i class="cp-BIA-rules"></i>
                <span>Log Directory</span>
            </h6>
            <button class="btn btn-primary btn-sm">Download</button>
        </div>
        <div class="row">
            <div class="col-4">
                <div class="card Card_Design_None">
                    <div class="card-body" style="height:calc(100vh - 113px);overflow:auto">
                        <div class="tree-menu">
                            <ul class="tree">
                                <li>
                                    <span role="button">Logs</span>
                                    <ul>
                                        <li>
                                            <span role="button">New folder</span>
                                            <ul class="sub-parent">
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>

                                            </ul>
                                        </li>
                                        <li>
                                            <span role="button">New folder</span>
                                            <ul class="sub-parent">
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>

                                            </ul>
                                        </li>
                                        <li>
                                            <span role="button">New folder</span>
                                            <ul class="sub-parent">
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>

                                            </ul>
                                        </li>
                                        <li>
                                            <span role="button">New folder</span>
                                            <ul class="sub-parent">
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>

                                            </ul>
                                        </li>
                                        <li>
                                            <span role="button">New folder</span>
                                            <ul class="sub-parent">
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>
                                                <li>
                                                    <span role="button"><i class="cp-doc-file me-1"></i>CP_Web_log-20250124.txt</span>
                                                </li>

                                            </ul>
                                        </li>
                                    </ul>

                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="card Card_Design_None">
                    <div class="card-body" style="height:calc(100vh - 113px);overflow:auto">
                        <div>
                            <ul class="log-list-message" style="list-style:none;padding:0px">
                                <li>
                                    <span>
                                        2025-01-24 10:08:31.569 +05:30 ] [INF] : [/PTS-D00067/1] – =
                                        Starting Continuity Patrol Web UI
                                    </span>
                                </li>                         
                                <li>
                                    <span>
                                        2025-01-24 10:09:06.038 +05:30 [Anonymous] [INF] : [::1/PTS-D00067/9] - Routing to prepare view with ApplicationStatus: Qualified
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:49.810 +05:30 [Anonymous] [INF]: [::1/PTS-D00067/11] - Verifying Access key for user 'Mark'.
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:50.460 +05:30 [Anonymous] [INF]: [::1/PTS-D00067/14] - User 'Mark' authorized.
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:50.814 +05:30 [Anonymous] [INF]: [::1/PTS-D00067/19] - User 'mark' Logged in successfully
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:50.999 +05:30 [Anonymous] [INF] : [::1/PTS-D00067/14] - Post Logged in Status Succeeded
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:51.087 +05:30 [mark] [INF] : [::1/PTS-D00067/19] - Service Availability viewed
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:56.552 +05:30 [mark] [WRN]: [::1/PTS-D00067/31] - Your license 'pts' is going to expire 'Today'.
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:08:31.569 +05:30 ] [INF] : [/PTS-D00067/1] – =
                                        Starting Continuity Patrol Web UI
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:06.038 +05:30 [Anonymous] [INF] : [::1/PTS-D00067/9] - Routing to prepare view with ApplicationStatus: Qualified
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:49.810 +05:30 [Anonymous] [INF]: [::1/PTS-D00067/11] - Verifying Access key for user 'Mark'.
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:50.460 +05:30 [Anonymous] [INF]: [::1/PTS-D00067/14] - User 'Mark' authorized.
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:50.814 +05:30 [Anonymous] [INF]: [::1/PTS-D00067/19] - User 'mark' Logged in successfully
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:50.999 +05:30 [Anonymous] [INF] : [::1/PTS-D00067/14] - Post Logged in Status Succeeded
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:51.087 +05:30 [mark] [INF] : [::1/PTS-D00067/19] - Service Availability viewed
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:56.552 +05:30 [mark] [WRN]: [::1/PTS-D00067/31] - Your license 'pts' is going to expire 'Today'.
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:08:31.569 +05:30 ] [INF] : [/PTS-D00067/1] – =
                                        Starting Continuity Patrol Web UI
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:06.038 +05:30 [Anonymous] [INF] : [::1/PTS-D00067/9] - Routing to prepare view with ApplicationStatus: Qualified
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:49.810 +05:30 [Anonymous] [INF]: [::1/PTS-D00067/11] - Verifying Access key for user 'Mark'.
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:50.460 +05:30 [Anonymous] [INF]: [::1/PTS-D00067/14] - User 'Mark' authorized.
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:50.814 +05:30 [Anonymous] [INF]: [::1/PTS-D00067/19] - User 'mark' Logged in successfully
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:50.999 +05:30 [Anonymous] [INF] : [::1/PTS-D00067/14] - Post Logged in Status Succeeded
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:51.087 +05:30 [mark] [INF] : [::1/PTS-D00067/19] - Service Availability viewed
                                    </span>
                                </li>
                                <li>
                                    <span>
                                        2025-01-24 10:09:56.552 +05:30 [mark] [WRN]: [::1/PTS-D00067/31] - Your license 'pts' is going to expire 'Today'.
                                    </span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
   
   
   
</div>
<script>
    $(function () {
        // Initially show only the first sublist (if it exists), hide others
        $('ul.tree ul').hide();
        $('ul.tree ul:first').show();  // Only show the first sublist

        $('.tree li > ul').each(function (i) {
            var $subUl = $(this);
            var $parentLi = $subUl.parent('li');
            var $toggleIcon = i === 0
                ? '<i class="js-toggle-icon"><span class="cp-circle-minus"></span></i>'
                : '<i class="js-toggle-icon"><span class="cp-circle-plus"></span></i>';

            // Set initial folder icon
            var $folderIcon = i === 0
                ? '<i class="cp-folder-open me-1"></i>'
                : '<i class="cp-folder-close me-1"></i>';

            $parentLi.addClass('has-children');

            // Prepend the folder icon and toggle icon to the parent <li> and set click handler
            $parentLi.prepend($folderIcon).prepend($toggleIcon).find('.js-toggle-icon').on('click', function () {
                var $thisIcon = $(this);
                var $iconSpan = $thisIcon.find('span');
                var $folderIconSpan = $parentLi.find('i.cp-folder-open, i.cp-folder-close');

                // Toggle the folder icon and open/close the sublist
                if ($iconSpan.hasClass('cp-circle-minus')) {
                    // If the icon is a minus, switch to plus, close the folder and change to closed icon
                    $iconSpan.removeClass('cp-circle-minus').addClass('cp-circle-plus');
                    $folderIconSpan.removeClass('cp-folder-open').addClass('cp-folder-close'); // Change to closed icon
                    $subUl.stop(true, true).slideUp('fast'); // Slide up (close)
                } else {
                    // If the icon is a plus, switch to minus, open the folder and change to open icon
                    $iconSpan.removeClass('cp-circle-plus').addClass('cp-circle-minus');
                    $folderIconSpan.removeClass('cp-folder-close').addClass('cp-folder-open'); // Change to open icon
                    $subUl.stop(true, true).slideDown('fast'); // Slide down (open)
                }
            });
        });
    });


</script>