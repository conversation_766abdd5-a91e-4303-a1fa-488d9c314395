using ContinuityPatrol.Application.Features.BiaRules.Commands.Create;
using ContinuityPatrol.Application.Features.BiaRules.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BiaRules.Commands;

public class CreateBiaRulesTests : IClassFixture<BiaRulesFixture>
{
    private readonly BiaRulesFixture _biaRulesFixture;
    private readonly Mock<IBiaRulesRepository> _mockBiaRulesRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly CreateBiaRulesCommandHandler _handler;

    public CreateBiaRulesTests(BiaRulesFixture biaRulesFixture)
    {
        _biaRulesFixture = biaRulesFixture;
        _mockBiaRulesRepository = BiaRulesRepositoryMocks.CreateBiaRulesRepository(_biaRulesFixture.BiaRules);
        _mockPublisher = new Mock<IPublisher>();

        _handler = new CreateBiaRulesCommandHandler(
            _biaRulesFixture.Mapper,
            _mockBiaRulesRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_CreateBiaRules_When_ValidCommand()
    {
        // Arrange
        var command = new CreateBiaRulesCommand
        {
            Description = "Test BIA Rule",
            Type = "RTO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"threshold\":\"4\",\"unit\":\"hours\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(30).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RTO_001"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules created successfully");
        result.Id.ShouldNotBeNullOrEmpty();

        _mockBiaRulesRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.BiaRules>()), Times.Once);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<BiaRulesCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateBiaRules_When_ValidRTORule()
    {
        // Arrange
        var command = new CreateBiaRulesCommand
        {
            Description = "RTO Rule for Critical Systems",
            Type = "RTO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"threshold\":\"2\",\"unit\":\"hours\",\"priority\":\"high\"}",
            EffectiveDateFrom = DateTime.Now.ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddYears(1).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RTO_CRITICAL"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules created successfully");
        result.Id.ShouldNotBeNullOrEmpty();

        _mockBiaRulesRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.BiaRules>(b => 
            b.Type == "RTO" && 
            b.Description == "RTO Rule for Critical Systems" &&
            b.RuleCode == "BIA_RTO_CRITICAL")), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateBiaRules_When_ValidRPORule()
    {
        // Arrange
        var command = new CreateBiaRulesCommand
        {
            Description = "RPO Rule for Database Systems",
            Type = "RPO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"threshold\":\"1\",\"unit\":\"hours\",\"backup_frequency\":\"hourly\"}",
            EffectiveDateFrom = DateTime.Now.ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddYears(1).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RPO_DATABASE"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules created successfully");
        result.Id.ShouldNotBeNullOrEmpty();

        _mockBiaRulesRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.BiaRules>(b => 
            b.Type == "RPO" && 
            b.Description == "RPO Rule for Database Systems" &&
            b.RuleCode == "BIA_RPO_DATABASE")), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateBiaRules_When_InactiveRule()
    {
        // Arrange
        var command = new CreateBiaRulesCommand
        {
            Description = "Inactive BIA Rule",
            Type = "RTO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"threshold\":\"8\",\"unit\":\"hours\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(30).ToString("yyyy-MM-dd"),
            IsEffective = false,
            RuleCode = "BIA_RTO_INACTIVE"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules created successfully");
        result.Id.ShouldNotBeNullOrEmpty();

        _mockBiaRulesRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.BiaRules>(b => 
            b.IsEffective == false &&
            b.RuleCode == "BIA_RTO_INACTIVE")), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateBiaRules_When_ComplexProperties()
    {
        // Arrange
        var complexProperties = "{\"threshold\":\"4\",\"unit\":\"hours\",\"escalation\":{\"level1\":\"2h\",\"level2\":\"4h\"},\"notifications\":[\"email\",\"sms\"],\"business_impact\":\"high\"}";
        var command = new CreateBiaRulesCommand
        {
            Description = "Complex BIA Rule with Multiple Properties",
            Type = "RTO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = complexProperties,
            EffectiveDateFrom = DateTime.Now.ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddYears(2).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RTO_COMPLEX"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules created successfully");
        result.Id.ShouldNotBeNullOrEmpty();

        _mockBiaRulesRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.BiaRules>(b => 
            b.Properties == complexProperties &&
            b.RuleCode == "BIA_RTO_COMPLEX")), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateBiaRules_When_FutureDateRange()
    {
        // Arrange
        var command = new CreateBiaRulesCommand
        {
            Description = "Future Effective BIA Rule",
            Type = "RTO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"threshold\":\"6\",\"unit\":\"hours\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(30).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(365).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RTO_FUTURE"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules created successfully");
        result.Id.ShouldNotBeNullOrEmpty();

        _mockBiaRulesRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.BiaRules>(b => 
            b.EffectiveDateFrom == DateTime.Now.AddDays(30).ToString("yyyy-MM-dd") &&
            b.EffectiveDateTo == DateTime.Now.AddDays(365).ToString("yyyy-MM-dd"))), Times.Once);
    }

    [Fact]
    public async Task Handle_PublishEvent_When_BiaRulesCreated()
    {
        // Arrange
        var command = _biaRulesFixture.CreateBiaRulesCommand;

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockPublisher.Verify(x => x.Publish(
            It.Is<BiaRulesCreatedEvent>(e => e.Name == command.Type), 
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_MapCorrectly_When_ValidCommand()
    {
        // Arrange
        var command = new CreateBiaRulesCommand
        {
            Description = "Mapping Test Rule",
            Type = "RTO",
            EntityId = "test-entity-123",
            Properties = "{\"test\":\"value\"}",
            EffectiveDateFrom = "2024-01-01",
            EffectiveDateTo = "2024-12-31",
            IsEffective = true,
            RuleCode = "BIA_TEST_MAP"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBiaRulesRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.BiaRules>(b =>
            b.Description == command.Description &&
            b.Type == command.Type &&
            b.EntityId == command.EntityId &&
            b.Properties == command.Properties &&
            b.EffectiveDateFrom == command.EffectiveDateFrom &&
            b.EffectiveDateTo == command.EffectiveDateTo &&
            b.IsEffective == command.IsEffective &&
            b.RuleCode == command.RuleCode)), Times.Once);
    }


    [Fact]
    public async Task Handle_ReturnCorrectResponse_When_BiaRulesCreated()
    {
        // Arrange
        var command = new CreateBiaRulesCommand
        {
            Description = "Response Test Rule",
            Type = "RPO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"threshold\":\"30\",\"unit\":\"minutes\"}",
            EffectiveDateFrom = DateTime.Now.ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddMonths(6).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RPO_RESPONSE"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<CreateBiaRulesResponse>();
        result.Message.ShouldBe("BIA Rules created successfully");
        result.Id.ShouldNotBeNullOrEmpty();
        Guid.TryParse(result.Id, out _).ShouldBeTrue();
    }
}
