﻿
@{
    ViewData["Title"] = "DRDrillReport";
}

@using ContinuityPatrol.Web.Areas.Report.Controllers
@using DevExpress.AspNetCore
@using ContinuityPatrol.Web.Areas.Report.ReportTemplate

<style>
    .form-group {
        margin-left: 550px;
        margin-top: 60px;
    }
</style>
@section Styles
    {
    <link href="~/css/viewer.part.bundle.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/thirdparty.bundle.css" rel="stylesheet" asp-append-version="true" />
    }
@section HeaderScripts
    {
    <script src="~/js/thirdparty.bundle.js" asp-append-version="true"></script>
    <script src="~/js/viewer.part.bundle.js" asp-append-version="true"></script>
   }

<div class="card card-custom gutter-b">
    <div class="card-body p-0">
        <div id="reportContainer">
            @{
                bool reportType = false;
                var reportData = ViewData["DRDrillReportData"]?.ToString();
                var reportDataObj = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(reportData);
                reportType = reportDataObj.IsCustom;
                if (!reportType)
                {
                    @Html.DevExpress().WebDocumentViewer("DRDrillSummaryDocumentViewer").Height("1150px").Bind(new DRDrillSummaryReport(ViewData["DRDrillReportData"].ToString()))
                }
                else if (reportType)
                {
                    @Html.DevExpress().WebDocumentViewer("DRDrillSummaryDocumentViewer").Height("1150px").Bind(new WorkflowActionResult(ViewData["DRDrillReportData"].ToString()))

                }
            }
        </div>
    </div>
</div>

@*<div class="row mt-3">
    <div class="col">
        <img src="~/img/logo/cplogo.svg" height="35" />
    </div>
    <div class="col text-end">
        <img src="~/img/logo/pts_logo.png" height="35" />
    </div>
    <div class="col-12">
        <h6 class="Report-Header">DR Drill Summary</h6>
    </div>
</div>
<div class="d-flex">
    <div class="card bg-light w-50">
        <div class="card-header text-primary fw-bold border-bottom">
            BusinessService Development
        </div>
        <div class="card-body">
            <p>Datacentre</p>
            <div class="d-flex justify-content-between text-center mt-3">
                <div class="d-grid">
                    <i class="cp-list-neardrsite fs-4"></i>
                    <small>Chennai <small>(NDR)</small></small>
                </div>
                <div>
                    <i class="cp-on-arrow fs-5"></i>
                </div>
                <div class="d-grid">
                    <i class="cp-list-prsite fs-4"></i>
                    <small>Chennai <small>(PR)</small></small>
                </div>
                <div class="d-grid">
                    <i class="cp-off-arrow fs-5"></i>
                </div>
                <div class="d-grid">
                    <i class="cp-cloud-server fs-4"></i>
                    <small>Chennai <small>(DR)</small></small>
                </div>
            </div>
        </div>
    </div>
    <div class="flex-fill d-flex align-items-center">
        <div id="ConfiguredRTO" style="width:100%; height:150px;"></div>
        <div class="d-grid gap-2">
            <div class="border-start border-5" style="border-color:#3d5a80 !important;">
                <span class="text-success ms-2">00:01:55 <i class="cp-server-up"></i></span>
                <span class="ms-2">Saved RTO</span>
            </div>
            <div class="border-start border-5 d-grid" style="border-color:#bdc02b !important;">
                <span class="ms-2">00:01:55</span>
                <span class="ms-2">Actual RTO</span>
            </div>
        </div>
    </div>
    <div class="flex-fill d-flex align-items-center">
        <div id="ActualRTOChart" style="width:100%; height:160px;"></div>
        <div class="d-grid gap-2">
            <div class="border-start border-5" style="border-color:#2f4558 !important;">
                <span class="text-success ms-2">00:01:55 <i class="cp-circle-playnext fs-6"></i></span>
                <span class="ms-2">Skipped</span>
            </div>
            <div class="border-start border-5" style="border-color:#50c878 !important;">
                <span class="ms-2">00:01:55 <i class="cp-success fs-6"></i></span>
                <span class="ms-2">Succeeded</span>
            </div>
        </div>
    </div>
</div>
<div class="row row-cols-2">
    <div class="col">
        <div class=" bg-light rounded-2 h-100">
            <ol class="list-group list-group-flush">
                <li class="list-group-item fw-bold text-primary">
                    Profile Details
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-user-profile me-1"></i>Profile Executed By</div>
                    <span>krish</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-application me-1"></i>Report Functional Category</div>
                    <span>WPM_MultipleWF</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-configure-settings me-1"></i>Configured RTO</div>
                    <span>2 mins</span>
                </li>
            </ol>
        </div>
    </div>
    <div class="col">
        <div class="bg-light rounded-2">
            <ol class="list-group list-group-flush">
                <li class="list-group-item fw-bold text-primary">
                    DR Excecution Details
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-apply-finish-time me-1"></i>Drill Start Time</div>
                    <span>04-08-2023 13:08:10</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-apply-finish-time me-1"></i>Drill End Time</div>
                    <span>01-01-0001 00:00:00</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-timer-meter me-1"></i>Actual RTO (Drill Execution Time)</div>
                    <span>00:00:00</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-estimated-time me-1"></i>Saved RTO</div>
                    <span class="text-success ms-2">00:01:55 <i class="cp-server-up"></i></span>
                </li>
            </ol>
        </div>

    </div>
</div>
<div class="row">
    <div class="col-12">
        <div class="table-responsive mt-3">
            <table class="table table-sm table-hover">
                <thead class="bg-light">
                    <tr>
                        <th>Sr.No</th>
                        <th>WorkFlow</th>
                        <th>InfraObject</th>
                        <th>Start Time</th>
                        <th>End Time</th>
                        <th>Total Time (hh:mm:ss)</th>
                        <th>PR IP/Host Name</th>
                        <th>DR IP/Host Name</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <th class="text-center">1</th>
                        <td>WF_multipleWF_1</td>
                        <td>MSSSQLAlwaysON_Infra</td>
                        <td>04-08-2023 13:08:10</td>
                        <td>04-08-2023 13:08:10</td>
                        <td>00:00:00</td>
                        <td>***********</td>
                        <td>***********</td>
                        <td>NA</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="row row-cols-2">
    <div class="col-12">
        <h6 class="Report-Header">Parallel DR Operation Details</h6>
    </div>
    <div class="col">
        <div class=" bg-light rounded-2 h-100">
            <ol class="list-group list-group-flush">
                <li class="list-group-item fw-bold text-primary">
                    DR Information
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-workflow-configuration me-1"></i>WF_multipleWF_1</div>
                    <span>WF_multipleWF_1</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-drill-action-type me-1"></i>Drill Action Type</div>
                    <span>Custom</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-server me-1"></i>DR Server Name</div>
                    <span>MSSQlAlwaysON_DRServer</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-dr me-1"></i>DR IP/Host Name</div>
                    <span>***********</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-database-sid me-1"></i>DR DataBase Name/SID</div>
                    <span>MSSQLAlwaysON_DRDB</span>
                </li>
            </ol>
        </div>
    </div>
    <div class="col">
        <div class="bg-light rounded-2 h-100">
            <ol class="list-group list-group-flush">
                <li class="list-group-item fw-bold text-primary">
                    PR Information
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-server-type me-1"></i>PR Server Name</div>
                    <span>MSSQlAlwaysON_PRServer</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-database-sid me-1"></i>PR DataBase Name/SID</div>
                    <span>MSSQLAlwaysON_PRDB</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-production me-1"></i>PR IP/Host Name</div>
                    <span>***********</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="me-auto"><i class="cp-legend me-1"></i>LEGEND</div>
                    <span>NA: Not Availabel</span>
                </li>
            </ol>
        </div>

    </div>
</div>
<div class="row">
    <div class="col-12">
        <div class="table-responsive mt-3">
            <table class="table table-sm table-hover">
                <thead class="bg-light">
                    <tr>
                        <th>Sr.No</th>
                        <th>WorkFlow</th>
                        <th>InfraObject</th>
                        <th>Start Time</th>
                        <th>End Time</th>
                        <th>Total Time (hh:mm:ss)</th>
                        <th>PR IP/Host Name</th>
                        <th>DR IP/Host Name</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <th class="text-center">1</th>
                        <td>WF_multipleWF_1</td>
                        <td>MSSSQLAlwaysON_Infra</td>
                        <td>04-08-2023 13:08:10</td>
                        <td>04-08-2023 13:08:10</td>
                        <td>00:00:00</td>
                        <td>***********</td>
                        <td>***********</td>
                        <td>NA</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script src="~/js/report-charts/drillsummary.js"></script>*@