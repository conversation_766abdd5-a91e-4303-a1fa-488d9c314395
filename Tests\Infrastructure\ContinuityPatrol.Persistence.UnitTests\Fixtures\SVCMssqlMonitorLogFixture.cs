using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SVCMssqlMonitorLogFixture
{
    public SVCMssqlMonitorLog CreateSVCMssqlMonitorLog(
        string type = "MSSQL_REPLICATION",
        string infraObjectId = "INFRA_001",
        string infraObjectName = "Default MSSQL Object",
        string workflowId = "WF_001",
        string workflowName = "Default Workflow",
        string properties = null,
        string configuredRPO = "15",
        string dataLagValue = "5",
        DateTime? createdDate = null,
        DateTime? lastModifiedDate = null,
        bool isActive = true,
        bool isDelete = false)
    {
        var now = DateTime.UtcNow;
        return new SVCMssqlMonitorLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = type,
            InfraObjectId = infraObjectId,
            InfraObjectName = infraObjectName,
            WorkflowId = workflowId,
            WorkflowName = workflowName,
            Properties = properties ?? "{\"rpo\": \"15\", \"status\": \"active\", \"lastSync\": \"2024-01-01T10:00:00Z\"}",
            ConfiguredRPO = configuredRPO,
            DataLagValue = dataLagValue,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = createdDate ?? now,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = lastModifiedDate ?? now
        };
    }

    public List<SVCMssqlMonitorLog> CreateMultipleSVCMssqlMonitorLogs(int count, string infraObjectId = "INFRA_001")
    {
        var logs = new List<SVCMssqlMonitorLog>();
        for (int i = 1; i <= count; i++)
        {
            logs.Add(CreateSVCMssqlMonitorLog(
                type: $"MSSQL_TYPE_{i}",
                infraObjectId: infraObjectId,
                infraObjectName: $"MSSQL Object {i}",
                workflowId: $"WF_{i:D3}",
                workflowName: $"Workflow {i}",
                configuredRPO: (15 + i).ToString(),
                dataLagValue: i.ToString(),
                createdDate: DateTime.UtcNow.AddDays(-i)
            ));
        }
        return logs;
    }

    public SVCMssqlMonitorLog CreateSVCMssqlMonitorLogWithSpecificId(string referenceId, string type = "MSSQL_REPLICATION")
    {
        return new SVCMssqlMonitorLog
        {
            ReferenceId = referenceId,
            Type = type,
            InfraObjectId = "INFRA_TEST",
            InfraObjectName = "Test MSSQL Object",
            WorkflowId = "WF_TEST",
            WorkflowName = "Test Workflow",
            Properties = "{\"test\": true}",
            ConfiguredRPO = "15",
            DataLagValue = "5",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public SVCMssqlMonitorLog CreateSVCMssqlMonitorLogForType(string type, string infraObjectId = "INFRA_001")
    {
        return CreateSVCMssqlMonitorLog(
            type: type,
            infraObjectId: infraObjectId,
            infraObjectName: $"MSSQL Object for {type}",
            workflowName: $"Workflow for {type}"
        );
    }

    public List<SVCMssqlMonitorLog> CreateSVCMssqlMonitorLogsForTypes(List<string> types, string infraObjectId = "INFRA_001")
    {
        var logs = new List<SVCMssqlMonitorLog>();
        foreach (var type in types)
        {
            logs.Add(CreateSVCMssqlMonitorLogForType(type, infraObjectId));
        }
        return logs;
    }

    public List<SVCMssqlMonitorLog> CreateSVCMssqlMonitorLogsWithStatus(int activeCount, int inactiveCount, string infraObjectId = "INFRA_001")
    {
        var logs = new List<SVCMssqlMonitorLog>();
        
        for (int i = 1; i <= activeCount; i++)
        {
            logs.Add(CreateSVCMssqlMonitorLog(
                type: $"MSSQL_ACTIVE_{i}",
                infraObjectId: infraObjectId,
                infraObjectName: $"Active MSSQL {i}",
                isActive: true
            ));
        }
        
        for (int i = 1; i <= inactiveCount; i++)
        {
            logs.Add(CreateSVCMssqlMonitorLog(
                type: $"MSSQL_INACTIVE_{i}",
                infraObjectId: infraObjectId,
                infraObjectName: $"Inactive MSSQL {i}",
                isActive: false
            ));
        }
        
        return logs;
    }

    public SVCMssqlMonitorLog CreateSVCMssqlMonitorLogForInfraObject(string infraObjectId, string infraObjectName = null)
    {
        return CreateSVCMssqlMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: infraObjectName ?? $"MSSQL Object for {infraObjectId}",
            workflowName: $"Workflow for {infraObjectId}"
        );
    }

    public List<SVCMssqlMonitorLog> CreateSVCMssqlMonitorLogsForInfraObjects(List<string> infraObjectIds)
    {
        var logs = new List<SVCMssqlMonitorLog>();
        foreach (var infraObjectId in infraObjectIds)
        {
            logs.Add(CreateSVCMssqlMonitorLogForInfraObject(infraObjectId));
        }
        return logs;
    }

    public SVCMssqlMonitorLog CreateSVCMssqlMonitorLogWithProperties(Dictionary<string, object> properties)
    {
        var propertiesJson = System.Text.Json.JsonSerializer.Serialize(properties);
        return CreateSVCMssqlMonitorLog(properties: propertiesJson);
    }

    public SVCMssqlMonitorLog CreateSVCMssqlMonitorLogWithComplexProperties()
    {
        var complexProperties = new Dictionary<string, object>
        {
            {"rpo", "15"},
            {"status", "running"},
            {"lastSync", "2024-01-01T10:00:00Z"},
            {"databaseDetails", new Dictionary<string, object>
                {
                    {"primaryDatabase", "DB001"},
                    {"secondaryDatabase", "DB002"},
                    {"replicationMode", "async"},
                    {"compressionEnabled", true}
                }
            },
            {"performance", new Dictionary<string, object>
                {
                    {"throughput", "200MB/s"},
                    {"latency", "2ms"},
                    {"errorRate", "0.001%"}
                }
            },
            {"alwaysOnDetails", new Dictionary<string, object>
                {
                    {"availabilityGroup", "AG001"},
                    {"replica", "REPLICA001"},
                    {"synchronizationState", "SYNCHRONIZED"}
                }
            }
        };

        return CreateSVCMssqlMonitorLogWithProperties(complexProperties);
    }

    public List<SVCMssqlMonitorLog> CreateSVCMssqlMonitorLogsForDateRange(string infraObjectId, DateTime startDate, DateTime endDate, int count)
    {
        var logs = new List<SVCMssqlMonitorLog>();
        var dateRange = (endDate - startDate).TotalDays;
        var interval = dateRange / count;

        for (int i = 0; i < count; i++)
        {
            var logDate = startDate.AddDays(i * interval);
            logs.Add(CreateSVCMssqlMonitorLog(
                infraObjectId: infraObjectId,
                infraObjectName: $"MSSQL Object {i + 1}",
                workflowName: $"Workflow {i + 1}",
                createdDate: logDate,
                lastModifiedDate: logDate
            ));
        }
        
        return logs;
    }

    public SVCMssqlMonitorLog CreateSVCMssqlMonitorLogForWorkflow(string workflowId, string workflowName = null, string infraObjectId = "INFRA_001")
    {
        return CreateSVCMssqlMonitorLog(
            infraObjectId: infraObjectId,
            workflowId: workflowId,
            workflowName: workflowName ?? $"Workflow {workflowId}",
            infraObjectName: $"MSSQL Object for {workflowId}"
        );
    }

    public SVCMssqlMonitorLog CreateSVCMssqlMonitorLogWithRPOSettings(string configuredRPO, string dataLagValue)
    {
        return CreateSVCMssqlMonitorLog(
            configuredRPO: configuredRPO,
            dataLagValue: dataLagValue,
            properties: $"{{\"rpo\": \"{configuredRPO}\", \"dataLag\": \"{dataLagValue}\"}}"
        );
    }

    public List<SVCMssqlMonitorLog> CreateSVCMssqlMonitorLogsForRPOTesting()
    {
        return new List<SVCMssqlMonitorLog>
        {
            CreateSVCMssqlMonitorLogWithRPOSettings("15", "5"),
            CreateSVCMssqlMonitorLogWithRPOSettings("30", "10"),
            CreateSVCMssqlMonitorLogWithRPOSettings("60", "25"),
            CreateSVCMssqlMonitorLogWithRPOSettings("120", "50")
        };
    }

    public SVCMssqlMonitorLog CreateMSSQLAlwaysOnLog(string infraObjectId = "INFRA_ALWAYSON")
    {
        return CreateSVCMssqlMonitorLog(
            type: "MSSQL_ALWAYSON",
            infraObjectId: infraObjectId,
            infraObjectName: "MSSQL AlwaysOn Object",
            workflowName: "AlwaysOn Workflow",
            properties: "{\"type\": \"alwayson\", \"availabilityGroup\": \"AG001\", \"status\": \"synchronized\"}"
        );
    }

    public SVCMssqlMonitorLog CreateMSSQLReplicationLog(string infraObjectId = "INFRA_REPL")
    {
        return CreateSVCMssqlMonitorLog(
            type: "MSSQL_REPLICATION",
            infraObjectId: infraObjectId,
            infraObjectName: "MSSQL Replication Object",
            workflowName: "Replication Workflow",
            properties: "{\"type\": \"replication\", \"mode\": \"transactional\", \"status\": \"running\"}"
        );
    }

    public SVCMssqlMonitorLog CreateMSSQLLogShippingLog(string infraObjectId = "INFRA_LOGSHIP")
    {
        return CreateSVCMssqlMonitorLog(
            type: "MSSQL_LOGSHIPPING",
            infraObjectId: infraObjectId,
            infraObjectName: "MSSQL Log Shipping Object",
            workflowName: "Log Shipping Workflow",
            properties: "{\"type\": \"logshipping\", \"schedule\": \"15min\", \"status\": \"active\"}"
        );
    }

    public List<SVCMssqlMonitorLog> CreateStandardMSSQLMonitorLogs()
    {
        return new List<SVCMssqlMonitorLog>
        {
            CreateMSSQLAlwaysOnLog(),
            CreateMSSQLReplicationLog(),
            CreateMSSQLLogShippingLog()
        };
    }

    public SVCMssqlMonitorLog CreateMinimalSVCMssqlMonitorLog()
    {
        return new SVCMssqlMonitorLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "MSSQL_MINIMAL",
            InfraObjectId = "MINIMAL_INFRA",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };
    }

    public SVCMssqlMonitorLog CreateSVCMssqlMonitorLogForTesting(
        string testName,
        string type = null,
        string infraObjectId = null)
    {
        return CreateSVCMssqlMonitorLog(
            type: type ?? $"MSSQL_{testName.ToUpper()}",
            infraObjectId: infraObjectId ?? $"INFRA_{testName.ToUpper()}",
            infraObjectName: $"Test MSSQL for {testName}",
            workflowName: $"Test Workflow for {testName}"
        );
    }
}
