using ContinuityPatrol.Application.Features.AccessManager.Commands.Create;
using ContinuityPatrol.Application.Features.AccessManager.Commands.Update;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetByRole;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.AccessManagerModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class AccessManagerFixture
{
    public List<AccessManagerListVm> AccessManagerListVm { get; }
    public AccessManagerDetailVm AccessManagerDetailVm { get; }
    public CreateAccessManagerCommand CreateAccessManagerCommand { get; }
    public UpdateAccessManagerCommand UpdateAccessManagerCommand { get; }
    public GetByRoleIdVm GetByRoleIdVm { get; }

    public AccessManagerFixture()
    {
        var fixture = new Fixture();

        // Create sample AccessManager list data
        AccessManagerListVm = new List<AccessManagerListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                RoleId = "ADMIN_ROLE",
                RoleName = "Administrator",
                Properties = "{\"permissions\":[\"read\",\"write\",\"delete\"]}",
                ProfileProperties = "{\"department\":\"IT\",\"level\":\"senior\"}"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                RoleId = "USER_ROLE",
                RoleName = "Standard User",
                Properties = "{\"permissions\":[\"read\"]}",
                ProfileProperties = "{\"department\":\"Operations\",\"level\":\"junior\"}"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                RoleId = "MANAGER_ROLE",
                RoleName = "Manager",
                Properties = "{\"permissions\":[\"read\",\"write\"]}",
                ProfileProperties = "{\"department\":\"Management\",\"level\":\"senior\"}"
            }
        };

        // Create detailed AccessManager data
        AccessManagerDetailVm = new AccessManagerDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            RoleId = "ADMIN_ROLE",
            RoleName = "Administrator",
            Properties = "{\"permissions\":[\"read\",\"write\",\"delete\"],\"modules\":[\"configuration\",\"monitoring\",\"reporting\"]}",
            ProfileProperties = "{\"department\":\"IT\",\"level\":\"senior\",\"clearanceLevel\":\"high\"}"
        };

        // Create command for creating AccessManager
        CreateAccessManagerCommand = new CreateAccessManagerCommand
        {
            CompanyId = Guid.NewGuid().ToString(),
            RoleId = "NEW_ROLE",
            RoleName = "New Test Role",
            Properties = "{\"permissions\":[\"read\",\"write\"]}",
            ProfileProperties = "{\"department\":\"Testing\",\"level\":\"intermediate\"}"
        };

        // Create command for updating AccessManager
        UpdateAccessManagerCommand = new UpdateAccessManagerCommand
        {
            Id = Guid.NewGuid().ToString(),
            RoleId = "UPDATED_ROLE",
            RoleName = "Updated Test Role",
            Properties = "{\"permissions\":[\"read\",\"write\",\"modify\"]}",
            ProfileProperties = "{\"department\":\"Testing\",\"level\":\"senior\"}"
        };

        // Create GetByRoleIdVm
        GetByRoleIdVm = new GetByRoleIdVm
        {
            Id = Guid.NewGuid().ToString(),
            RoleId = "ADMIN_ROLE",
            RoleName = "Administrator",
            Properties = "{\"permissions\":[\"read\",\"write\",\"delete\"]}",
            ProfileProperties = "{\"department\":\"IT\",\"level\":\"senior\"}"
        };
    }
}
