﻿@model ContinuityPatrol.Domain.ViewModels.SmtpConfigurationModel.SmtpConfigurationViewModel
@using ContinuityPatrol.Shared.Services.Helper
@{
    ViewData["Title"] = "SMTPConfiguration";
}
<div>
    @Html.AntiForgeryToken()
    <h6 class="body-header-text mb-3 page_title">
        <i class="cp-email"></i>
        <span>SMTP Configuration</span>
    </h6>

    <form id="CreateSMTPForm" asp-controller="SmtpConfiguration" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data">

        <div class="form-group mb-3">

            <label class="form-label " cursorshover="true">SMTP Host / IP Address</label>

            <div class="input-group">
                <span class="input-group-text">
                    <i class="cp-email"></i>
                </span>
                <input asp-for="SmtpHost" autocomplete="off"
                       placeholder="Enter SMTP Host / IP Address" type="text"
                       id="smtpConfigHost" class="form-control">
            </div>
            <span id="smtpConfigHostError"></span>
        </div>

        <div class="form-group mb-3">
            <label class="form-label " cursorshover="true">Port</label>
            <div class="input-group">
                <span class="input-group-text">
                    <i class="cp-port"></i>
                </span>
                <input asp-for="Port" autocomplete="off" placeholder="Enter SMTP Port"
                       maxlength="5" type="text" id="smtpConfigPort"
                       class="form-control">
            </div>
            <span id="smtpConfigPortError"></span>
        </div>

        <div class="d-flex gap-2 align-items-end">
            <div class="form-group mb-3 w-100">
                <label class="form-label " cursorshover="true">Email / Username</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="cp-email"></i>
                    </span>
                    <input asp-for="UserName" autocomplete="off" placeholder="Enter SMTP Email / Username"
                           type="text" id="smtpConfigUserName"
                           class="form-control">
                </div>
                <span id="smtpConfigEmailError"></span>
            </div>
            <div class="form-group mb-2">
                <div class="form-check">
                    <input asp-for="IsPasswordLess" type="checkbox"
                           class="form-check-input custom-cursor-default-hover" id="paswordcheck"><label title="" for="chk-htmltype" class="form-check-label">
                        isPasswordLess
                    </label>
                </div>
            </div>
        </div>

        <div class="form-group mb-3" id="txtPassword">
            <label class="form-label ">Password</label>
            <div class="input-group">
                <span class="input-group-text">
                    <i class="cp-lock"></i>
                </span>

                <input type="password" class="form-control" id="smtpConfigPassword" asp-for="Password" maxlength="30" autocomplete="off" placeholder="Enter SMTP Password">
                <span class="input-group-text toggle-password-SMTP"><i class="cp-password-visible fs-6"></i></span>
            </div>

            <span id="smtpConfigPasswordError"></span>
        </div>

        <div class="form-group mb-1">
            <div class="form-check">
                <input asp-for="IsEmail" type="checkbox" class="form-check-input custom-cursor-default-hover" id="chkEmail"><label title="" for="chk-htmltype" class="form-check-label">
                    IsEnableToEMail
                </label>
            </div>
        </div>
        <div class="form-group" id="EmailDiv">
            <label class="form-label " cursorshover="true">To Email Address</label>
            <div class="input-group">
                <span class="input-group-text">
                    <i class="cp-email"></i>
                </span>
                <input asp-for="ToEmail" autocomplete="off" placeholder="Enter To Email Address" type="email" id="txtSmtpEmail" class="form-control">
            </div>
            <span id="SmtpToEmail-error"></span>
        </div>

        <div class="form-group mb-1">
            <div class="form-check">
                <input asp-for="IsMask" type="checkbox"
                       class="form-check-input custom-cursor-default-hover" id="chkMask"><label title="" for="chk-htmltype" class="form-check-label">
                    IsMaskFromAddress
                </label>
            </div>
        </div>

        <div class="form-group" id="maskDiv">
            <label class="form-label " cursorshover="true">Mask From Address</label>
            <div class="input-group">
                <span class="input-group-text">
                    <i class="cp-email"></i>
                </span>
                <input asp-for="MaskFromAddress" autocomplete="off" placeholder="Enter Mask From Address"
                       type="text" id="smtpConfigMask"
                       class="form-control">
            </div>
            <span id="SmtpMaskAddress-error"></span>
        </div>

        <div class="form-group mb-2">
            <div class="form-check">
                <input asp-for="EnableSSL" type="checkbox"
                       id="chk-enableSSl"
                       class="form-check-input custom-cursor-default-hover"><label class="form-check-label">
                    SSL for
                    sending E-Mail
                </label>
            </div>
        </div>

        <div class="form-group mb-2">
            <div class="form-check">
                <input asp-for="IsBodyHTML" type="checkbox"
                       id="chk-isBodyHtml"
                       class="form-check-input custom-cursor-default-hover"><label title="" for="chk-htmltype" class="form-check-label">
                    HTML for
                    Email Formatting
                </label>
            </div>
        </div>

        <input asp-for="CompanyId" type="hidden" id="smtpConfigCompanyId" data-company="@WebHelper.UserSession.CompanyId" class="form-control" />
        
        <input asp-for="Id" type="hidden" id="smtpConfigId" class="form-control" />

    </form>

    <div>
        <div class="d-flex justify-content-end mb-3">
            <div>

                <input type="hidden" id="smtpConfigTestSmtp">
                <input type="hidden" id="smtpConfigTestPort">
                <input type="hidden" id="smtpConfigTestUserName">
                <input type="hidden" id="smtpConfigTestPassword">
                <input type="hidden" id="smtpConfigTestEnableSSL">
                <input type="hidden" id="smtpConfigTestIsBodyHTML">
                <input type="hidden" id="smtpConfigTestCompanyId" data-company="@WebHelper.UserSession.CompanyId">
                <input type="hidden" id="smtpConfigIsPassword">
                <input type="hidden" id="smtpConfigIsEmail">
                <input type="hidden" id="smtpConfigToEmail">
                 <input type="hidden" id="smtpConfigIsMask">
                <input type="hidden" id="smtpConfigMaskAddress">
                <button type="submit" id="btnSmtpTest"
                        class="btn btn-primary animation-btn me-2 btn btn-primary">
                    <span class="btn-text">Test Mail</span>
                </button>

            </div>
            <div>

                <button type="button" id="btnSmtpSave"
                        class="btn btn-primary animation-btn me-2 btn btn-primary">
                    <span class="btn-text">Save</span>
                </button>

            </div>
        </div>
    </div>
</div>

<div id="adminCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.CreateAndEdit" aria-hidden="true"></div>


<script src="~/js/Admin/Settings/SmtpConfiguration.js"></script>