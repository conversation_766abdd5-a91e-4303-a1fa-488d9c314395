﻿QUnit.module('ComponentType.js Unit Tests');

QUnit.test('componentTypeValidation should validate empty and filled values', function (assert) {
    const $errorDiv = $('<div id="componentTypeError"></div>');
    $('body').append($errorDiv);

    let result = componentTypeValidation("", " Select type", "componentTypeError");
    assert.strictEqual(result, false, "Empty value should return false and set error");

    result = componentTypeValidation("Server", " Select type", "componentTypeError");
    assert.strictEqual(result, true, "Valid value should return true and clear error");

    $errorDiv.remove();
});

QUnit.test('componentVersionValidation should validate for Server/Database type', function (assert) {
    $('body').append('<select id="selectComponentType"><option>Server</option></select>');
    $('#selectComponentType').val('Server');

    $('body').append('<div id="componentTypeVersionError"></div>');

    let result = componentVersionValidation("", " Add version", "componentTypeVersionError");
    assert.strictEqual(result, false, "Empty version should return false");

    result = componentVersionValidation("v1.0", " Add version", "componentTypeVersionError");
    assert.strictEqual(result, true, "Valid version should return true");

    $('#selectComponentType, #componentTypeVersionError').remove();
});

QUnit.test('addVersion should prevent duplicates', function (assert) {
    versions = [];
    $('body').append('<div id="selectedVersionDetails"></div><div id="componentTypeVersionError"></div>');

    addVersion("v1.0");
    addVersion("v1.0");

    assert.strictEqual(versions.length, 1, "Duplicate version should not be added");

    $('#selectedVersionDetails, #componentTypeVersionError').remove();
});

QUnit.test('updateProperties should fill componentProperties input', function (assert) {
    $('body').append(`
        <input type="checkbox" id="isServerChecked" checked>
        <input type="checkbox" id="isDatabaseChecked">
        <input type="checkbox" id="isReplicationChecked" checked>
        <input type="checkbox" id="isClusterChecked">
        <input type="checkbox" id="isRacChecked" checked>
        <input type="checkbox" id="isMultiPRChecked">
        <input type="checkbox" id="isMultiDRChecked">
        <input type="hidden" id="componentProperties">
    `);

    updateProperties();

    let value = $('#componentProperties').val();
    assert.ok(value.includes('"Server":true'), "Server true expected in JSON");
    assert.ok(value.includes('"Replication":true'), "Replication true expected");

    $('#isServerChecked, #isDatabaseChecked, #isReplicationChecked, #isClusterChecked, #isRacChecked, #isMultiPRChecked, #isMultiDRChecked, #componentProperties').remove();
});

QUnit.test('handleKeyPress should trigger button click on Enter key', function (assert) {
    const done = assert.async();

    $('body').append('<button id="btnSaveProfile"></button><input id="componentTypeVersion">');

    document.getElementById('btnSaveProfile').addEventListener('click', function () {
        assert.ok(true, 'Button click triggered');
        done();
    });

    const event = new KeyboardEvent('keydown', { keyCode: 13 });
    Object.defineProperty(event, 'target', { value: { id: 'componentTypeVersion' } });

    handleKeyPress(event);

    $('#btnSaveProfile, #componentTypeVersion').remove();
});
