﻿using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Impl;
using ContinuityPatrol.Infrastructure.Models;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Infrastructure.UnitTests.Impl;

public class EmailServiceTests
{
    private readonly Mock<ILogger<EmailService>> _loggerMock = new();
    private readonly Mock<ISmtpClientFactory> _smtpClientFactoryMock = new();
    private readonly Mock<ISmtpClientWrapper> _smtpClientWrapperMock = new();

    private EmailService CreateService()
    {
        _smtpClientFactoryMock
            .Setup(f => f.Create(It.IsAny<string>(), It.IsAny<int>()))
            .Returns(_smtpClientWrapperMock.Object);

        return new EmailService(_loggerMock.Object, _smtpClientFactoryMock.Object);
    }

    [Fact]
    public async Task SendEmail_WithPasswordless_ShouldSendSuccessfully()
    {
        // Arrange
        var service = CreateService();
        _smtpClientWrapperMock.Setup(x => x.SendMailAsync(It.IsAny<MailMessage>())).Returns(Task.CompletedTask);

        var dto = new EmailDto
        {
            IsPasswordLess = true,
            From = SecurityHelper.Encrypt("<EMAIL>"),
            To = "<EMAIL>",
            Subject = "Test Subject",
            Body = "Test Body",
            SmtpHost = "localhost",
            Port = "25",
            EnableSSL = false
        };

        // Act
        var result = await service.SendEmail(dto);

        // Assert
        Assert.True(result);
        _smtpClientWrapperMock.Verify(x => x.SendMailAsync(It.IsAny<MailMessage>()), Times.Once);
    }

    [Fact]
    public async Task SendEmail_WithCredentials_ShouldSendSuccessfully()
    {
        var service = CreateService();
        _smtpClientWrapperMock.Setup(x => x.SendMailAsync(It.IsAny<MailMessage>())).Returns(Task.CompletedTask);

        var dto = new EmailDto
        {
            IsPasswordLess = false,
            From = SecurityHelper.Encrypt("<EMAIL>"),
            Password = SecurityHelper.Encrypt("secret"),
            To = "<EMAIL>",
            Subject = "Test",
            Body = "Body",
            SmtpHost = "localhost",
            Port = "25",
            EnableSSL = true
        };

        var result = await service.SendEmail(dto);

        Assert.True(result);
        _smtpClientWrapperMock.Verify(x => x.SendMailAsync(It.IsAny<MailMessage>()), Times.Once);
    }

    [Fact]
    public async Task SendEmail_ThrowsSmtpException_ShouldRethrow()
    {
        var service = CreateService();
        _smtpClientWrapperMock.Setup(x => x.SendMailAsync(It.IsAny<MailMessage>()))
            .ThrowsAsync(new SmtpException("SMTP failed"));

        var dto = new EmailDto
        {
            IsPasswordLess = true,
            From = SecurityHelper.Encrypt("<EMAIL>"),
            To = "<EMAIL>",
            Subject = "Subject",
            Body = "Body",
            SmtpHost = "localhost",
            Port = "25"
        };

        var ex = await Assert.ThrowsAsync<SmtpException>(() => service.SendEmail(dto));
        Assert.Contains("SMTP", ex.Message);
    }
    [Fact]
    public async Task SendTestEmail_WithPasswordless_ShouldSendSuccessfully()
    {
        var service = CreateService();
        _smtpClientWrapperMock.Setup(x => x.SendMailAsync(It.IsAny<MailMessage>())).Returns(Task.CompletedTask);

        var dto = new SendTestEmailDto
        {
            IsPasswordLess = true,
            UserName = SecurityHelper.Encrypt("<EMAIL>"),
            IsEmail = true,
            ToEmail = "<EMAIL>",
            Subject = "Test",
            Body = AlternateView.CreateAlternateViewFromString("Body", null, "text/html"),
            SmtpHost = "localhost",
            Port = "25"
        };

        var result = await service.SendTestEmail(dto);
        Assert.True(result);
    }

    [Fact]
    public async Task SendTestEmail_WithCredentials_ShouldSendSuccessfully()
    {
        var service = CreateService();
        _smtpClientWrapperMock.Setup(x => x.SendMailAsync(It.IsAny<MailMessage>())).Returns(Task.CompletedTask);

        var dto = new SendTestEmailDto
        {
            IsPasswordLess = false,
            UserName = SecurityHelper.Encrypt("<EMAIL>"),
            Password = SecurityHelper.Encrypt("secret"),
            Subject = "Test",
            Body = AlternateView.CreateAlternateViewFromString("Body", null, "text/html"),
            SmtpHost = "localhost",
            Port = "25",
            EnableSSL = true
        };

        var result = await service.SendTestEmail(dto);
        Assert.True(result);
    }

    [Fact]
    public async Task SendTestEmail_WithException_ShouldReturnFalse()
    {
        var service = CreateService();
        _smtpClientWrapperMock.Setup(x => x.SendMailAsync(It.IsAny<MailMessage>()))
            .ThrowsAsync(new Exception("SMTP failed"));

        var dto = new SendTestEmailDto
        {
            IsPasswordLess = true,
            UserName = SecurityHelper.Encrypt("<EMAIL>"),
            ToEmail = "<EMAIL>",
            IsEmail = true,
            Subject = "Test",
            Body = AlternateView.CreateAlternateViewFromString("Body", null, "text/html"),
            SmtpHost = "localhost",
            Port = "25"
        };

        var result = await service.SendTestEmail(dto);
        Assert.False(result);
    }

    [Fact]
    public async Task SendEmail_WhenBodyIsEmpty_ShouldIsPasswordLessUseHtmlBody()
    {
        // Arrange
        var service = CreateService();

        bool? isBodyHtml = null;
        var alternateViewCount = 0;

        _smtpClientWrapperMock
            .Setup(x => x.SendMailAsync(It.IsAny<MailMessage>()))
            .Callback<MailMessage>(msg =>
            {
                isBodyHtml = msg.IsBodyHtml;
                alternateViewCount = msg.AlternateViews.Count;
            })
            .Returns(Task.CompletedTask);

        var dto = new EmailDto
        {
            IsPasswordLess = true,
            From = SecurityHelper.Encrypt("<EMAIL>"),
            To = "<EMAIL>",
            Subject = "Alert",
            Body = "",
            HtmlBody = AlternateView.CreateAlternateViewFromString("<b>HTML Body</b>", null, "text/html"),
            SmtpHost = "localhost",
            Port = "25"
        };

        // Act
        var result = await service.SendEmail(dto);

        // Assert
        Assert.True(result);
        Assert.True(isBodyHtml.HasValue && isBodyHtml.Value); // IsBodyHtml is true
        Assert.True(alternateViewCount > 0); // HtmlBody added
    }


    [Fact]
    public async Task SendEmail_WhenBodyIsEmpty_ShouldUseHtmlBody()
    {
        // Arrange
        var service = CreateService();

        bool? isBodyHtml = null;
        var alternateViewCount = 0;

        _smtpClientWrapperMock
            .Setup(x => x.SendMailAsync(It.IsAny<MailMessage>()))
            .Callback<MailMessage>(msg =>
            {
                isBodyHtml = msg.IsBodyHtml;
                alternateViewCount = msg.AlternateViews.Count;
            })
            .Returns(Task.CompletedTask);

        var dto = new EmailDto
        {
            IsPasswordLess = false,
            From = SecurityHelper.Encrypt("<EMAIL>"),
            To = "<EMAIL>",
            Subject = "Alert",
            Body = "",
            HtmlBody = AlternateView.CreateAlternateViewFromString("<b>HTML Body</b>", null, "text/html"),
            SmtpHost = "localhost",
            Port = "25"
        };

        // Act
        var result = await service.SendEmail(dto);

        // Assert
        Assert.True(result);
        Assert.True(isBodyHtml.HasValue && isBodyHtml.Value); // IsBodyHtml is true
        Assert.True(alternateViewCount > 0); // HtmlBody added
    }



    [Fact]
    public async Task SendEmail_WithInvalidPort_ShouldThrowFormatException()
    {
        var service = CreateService();

        var dto = new EmailDto
        {
            IsPasswordLess = true,
            From = SecurityHelper.Encrypt("<EMAIL>"),
            To = "<EMAIL>",
            Subject = "Subject",
            Body = "Body",
            Port = "1234567123456", // simulate invalid port range or overflown int
            SmtpHost = "localhost"
        };

        await Assert.ThrowsAsync<InvalidException>(() =>
            service.SendEmail(dto));
    }
    [Fact]
    public async Task SendEmail_WhenExceptionOccurs_ShouldThrowInvalidException()
    {
        var service = CreateService();

        _smtpClientWrapperMock.Setup(x => x.SendMailAsync(It.IsAny<MailMessage>()))
            .ThrowsAsync(new Exception("Unexpected"));

        var dto = new EmailDto
        {
            IsPasswordLess = true,
            From = SecurityHelper.Encrypt("<EMAIL>"),
            To = "<EMAIL>",
            Subject = "Subject",
            Body = "Body",
            SmtpHost = "localhost",
            Port = "25"
        };

        await Assert.ThrowsAsync<InvalidException>(() => service.SendEmail(dto));
    }
    [Fact]
    public async Task SendTestEmail_WhenIsEmailFalse_ShouldUseDecryptedUserNameAsTo()
    {
        var service = CreateService();

        _smtpClientWrapperMock.Setup(x => x.SendMailAsync(It.IsAny<MailMessage>()))
            .Returns(Task.CompletedTask);

        var dto = new SendTestEmailDto
        {
            IsPasswordLess = true,
            UserName = SecurityHelper.Encrypt("<EMAIL>"),
            IsEmail = false,
            ToEmail = null, // fallback
            Subject = "Test",
            Body = AlternateView.CreateAlternateViewFromString("HTML Body", null, "text/html"),
            SmtpHost = "localhost",
            Port = "25"
        };

        var result = await service.SendTestEmail(dto);
        Assert.True(result);
    }

    [Fact]
    public async Task SendTestEmail_ThrowsSmtpException_ShouldThrowSmtpException()
    {
        var service = CreateService();

        _smtpClientWrapperMock.Setup(x => x.SendMailAsync(It.IsAny<MailMessage>()))
            .ThrowsAsync(new SmtpException("SMTP problem"));

        var dto = new SendTestEmailDto
        {
            IsPasswordLess = true,
            UserName = SecurityHelper.Encrypt("<EMAIL>"),
            IsEmail = true,
            ToEmail = "<EMAIL>",
            Subject = "Test",
            Body = AlternateView.CreateAlternateViewFromString("HTML Body", null, "text/html"),
            SmtpHost = "localhost",
            Port = "25"
        };

        await Assert.ThrowsAsync<SmtpException>(() => service.SendTestEmail(dto));
    }


}