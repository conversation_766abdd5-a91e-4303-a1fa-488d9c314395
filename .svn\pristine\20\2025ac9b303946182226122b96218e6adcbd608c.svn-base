﻿using ContinuityPatrol.Application.Features.WorkflowHistory.Events.Update;

namespace ContinuityPatrol.Application.Features.WorkflowHistory.Commands.Update;

public class
    UpdateWorkflowHistoryCommandHandler : IRequestHandler<UpdateWorkflowHistoryCommand, UpdateWorkflowHistoryResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IWorkflowHistoryRepository _workflowHistoryRepository;

    public UpdateWorkflowHistoryCommandHandler(IMapper mapper, IWorkflowHistoryRepository workflowHistoryRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _workflowHistoryRepository = workflowHistoryRepository;
        _publisher = publisher;
    }

    public async Task<UpdateWorkflowHistoryResponse> Handle(UpdateWorkflowHistoryCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _workflowHistoryRepository.GetByReferenceIdAsync(request.WorkflowId);

        //var value = update.Count;

        //request.Version = value + 1;

        //request.Id = string.Empty;

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.WorkflowHistory), request.WorkflowId);

        //var data = _mapper.Map<Domain.Entities.WorkflowHistory>(request);

        //var update = await _workflowHistoryRepository.AddAsync(data);

        _mapper.Map(request, eventToUpdate, typeof(UpdateWorkflowHistoryCommand),
            typeof(Domain.Entities.WorkflowHistory));

        await _workflowHistoryRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateWorkflowHistoryResponse
        {
            Message = Message.Update(nameof(Domain.Entities.WorkflowHistory), eventToUpdate.WorkflowName),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new WorkflowHistoryUpdatedEvent { WorkflowHistoryName = eventToUpdate.WorkflowName },
            cancellationToken);

        return response;
    }
}