using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class FiaImpactTypeFixture : IDisposable
{
    public List<FiaImpactType> FiaImpactTypePaginationList { get; set; }
    public List<FiaImpactType> FiaImpactTypeList { get; set; }
    public FiaImpactType FiaImpactTypeDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public FiaImpactTypeFixture()
    {
        var fixture = new Fixture();

        FiaImpactTypeList = fixture.Create<List<FiaImpactType>>();

        FiaImpactTypePaginationList = fixture.CreateMany<FiaImpactType>(20).ToList();

        FiaImpactTypePaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FiaImpactTypePaginationList.ForEach(x => x.IsActive = true);

        FiaImpactTypeList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FiaImpactTypeList.ForEach(x => x.IsActive = true);

        FiaImpactTypeDto = fixture.Create<FiaImpactType>();
        FiaImpactTypeDto.ReferenceId = Guid.NewGuid().ToString();
        FiaImpactTypeDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
