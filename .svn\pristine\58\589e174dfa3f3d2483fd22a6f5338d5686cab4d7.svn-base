using ContinuityPatrol.Domain.ViewModels.BackUpModel;

namespace ContinuityPatrol.Application.Features.BackUp.Queries.GetList;

public class GetBackUpListQueryHandler : IRequestHandler<GetBackUpListQuery, List<BackUpListVm>>
{
    private readonly IBackUpRepository _backUpRepository;
    private readonly IMapper _mapper;

    public GetBackUpListQueryHandler(IMapper mapper, IBackUpRepository backUpRepository)
    {
        _mapper = mapper;
        _backUpRepository = backUpRepository;
    }

    public async Task<List<BackUpListVm>> Handle(GetBackUpListQuery request, CancellationToken cancellationToken)
    {
        var backUps = await _backUpRepository.ListAllAsync();

        if (backUps.Count <= 0) return new List<BackUpListVm>();

        return _mapper.Map<List<BackUpListVm>>(backUps);
    }
}