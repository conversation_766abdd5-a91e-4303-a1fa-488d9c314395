﻿namespace ContinuityPatrol.Application.Features.Report.Queries.GetRunBookReport;

public class GetRunBookReportVm
{
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string ReportGeneratedBy { get; set; }
    public string Date { get; set; }

    public List<GetRunBookReportListVm> GetRunBookReportListVms { get; set; } = new();
}

public class GetRunBookReportListVm
{
    public int SrNo { get; set; }
    public string WorkflowActionName { get; set; }
    public string WorkflowAction { get; set; }
    public string ActionType { get; set; }
    public string RTO { get; set; }
    public string Properties { get; set; }
    public string IsParallel { get; set; }
    public string GroupName { get; set; }
}