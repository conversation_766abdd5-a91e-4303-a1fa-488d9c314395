﻿namespace ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Create;

public class CreateWorkflowProfileInfoCommandValidator : AbstractValidator<CreateWorkflowProfileInfoCommand>
{
    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;

    public CreateWorkflowProfileInfoCommandValidator(IWorkflowProfileInfoRepository workflowProfileInfoRepository)
    {
        _workflowProfileInfoRepository = workflowProfileInfoRepository;

        RuleFor(p => p.ProfileName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+(?:[_\s-]?))([a-zA-Z\d]+(?:[_\s-]?))*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 200).WithMessage("{PropertyName} should contain between 3 to 200 characters.");

        RuleFor(p => p.BusinessServiceName)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.BusinessFunctionName)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.InfraObjectName)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.WorkflowName)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Length(3, 200).WithMessage("{PropertyName} should contain between 3 to 200 characters");

        RuleFor(p => p)
            .NotNull()
            .MustAsync(IsValidGuidId)
            .WithMessage("Id is invalid.");
    }

    private Task<bool> IsValidGuidId(CreateWorkflowProfileInfoCommand createWorkflowProfileInfoCommand,
        CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(createWorkflowProfileInfoCommand.ProfileId, "Profile Id");
        Guard.Against.InvalidGuidOrEmpty(createWorkflowProfileInfoCommand.BusinessServiceId, "BusinessService Id");
        Guard.Against.InvalidGuidOrEmpty(createWorkflowProfileInfoCommand.BusinessFunctionId, "BusinessFunction Id");
        Guard.Against.InvalidGuidOrEmpty(createWorkflowProfileInfoCommand.WorkflowId, "Workflow Id");
        return Task.FromResult(true);
    }
}