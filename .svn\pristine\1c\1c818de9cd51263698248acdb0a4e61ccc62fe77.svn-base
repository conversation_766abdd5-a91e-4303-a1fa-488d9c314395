﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class CompanyFilterSpecification : Specification<Company>
{
    public CompanyFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.Name != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("displayname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.DisplayName.Contains(stringItem.Replace("displayname=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("logoname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.LogoName.Contains(stringItem.Replace("logoname=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("name=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "", StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("webaddress=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.WebAddress.Contains(stringItem.Replace("webaddress=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.Name.Contains(searchString) || p.DisplayName.Contains(searchString) ||
                    p.LogoName.Contains(searchString) || p.WebAddress.Contains(searchString);
            }
        }
    }
}