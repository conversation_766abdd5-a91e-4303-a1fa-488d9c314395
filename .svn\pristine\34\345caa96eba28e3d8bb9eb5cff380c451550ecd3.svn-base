﻿using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Events.Create;

namespace ContinuityPatrol.Application.UnitTests.Features.PostgresMonitorStatus.Events
{
    public class CreatePostgresMonitorStatusEventTests
    {
        private readonly Mock<ILogger<PostgresMonitorStatusCreatedEventHandler>> _mockLogger;
        private readonly Mock<IPostgresMonitorStatusRepository> _mockPostgresMonitorStatusRepository;
        private readonly PostgresMonitorStatusCreatedEventHandler _handler;

        public CreatePostgresMonitorStatusEventTests()
        {
            _mockLogger = new Mock<ILogger<PostgresMonitorStatusCreatedEventHandler>>();
            _mockPostgresMonitorStatusRepository = new Mock<IPostgresMonitorStatusRepository>();
            _handler = new PostgresMonitorStatusCreatedEventHandler(_mockLogger.Object, _mockPostgresMonitorStatusRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldAddPostgresMonitorStatus_WhenEventIsProcessed()
        {
            var createdEvent = new PostgresMonitorStatusCreatedEvent
            {
                Type = "StatusType",
                InfraObjectId = "infra123",
                InfraObjectName = "TestInfraObject",
                WorkflowId = "workflow123",
                WorkflowName = "TestWorkflow",
                Properties = "TestProperties",
                ConfiguredRPO = "10",
                DataLagValue = "5"
            };
            await _handler.Handle(createdEvent, CancellationToken.None);

            _mockPostgresMonitorStatusRepository.Verify(r => r.AddAsync(It.Is<Domain.Entities.PostgresMonitorStatus>(p =>
                p.Type == createdEvent.Type &&
                p.InfraObjectId == createdEvent.InfraObjectId &&
                p.InfraObjectName == createdEvent.InfraObjectName &&
                p.WorkflowId == createdEvent.WorkflowId &&
                p.WorkflowName == createdEvent.WorkflowName &&
                p.Properties == createdEvent.Properties &&
                p.ConfiguredRPO == createdEvent.ConfiguredRPO &&
                p.DataLagValue == createdEvent.DataLagValue
            )), Times.Once);

            _mockLogger.Verify(l => l.LogInformation(It.Is<string>(s =>
                s.Contains(createdEvent.InfraObjectName) && s.Contains("created successfully")
            )), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldNotThrowException_WhenRepositoryFailsToAddPostgresMonitorStatus()
        {
            var createdEvent = new PostgresMonitorStatusCreatedEvent
            {
                Type = "StatusType",
                InfraObjectId = "infra123",
                InfraObjectName = "TestInfraObject",
                WorkflowId = "workflow123",
                WorkflowName = "TestWorkflow",
                Properties = "TestProperties",
                ConfiguredRPO = "10",
                DataLagValue = "5"
            };
            _mockPostgresMonitorStatusRepository.Setup(r => r.AddAsync(It.IsAny<Domain.Entities.PostgresMonitorStatus>()))
                .ThrowsAsync(new System.Exception("Database error"));

            var exception = await Assert.ThrowsAsync<System.Exception>(() => _handler.Handle(createdEvent, CancellationToken.None));
            Assert.Equal("Database error", exception.Message);
        }

        [Fact]
        public async Task Handle_ShouldNotCallRepository_WhenEventHasInvalidData()
        {
            var createdEvent = new PostgresMonitorStatusCreatedEvent
            {
                Type = null,
                InfraObjectId = "infra123",
                InfraObjectName = "TestInfraObject",
                WorkflowId = "workflow123",
                WorkflowName = "TestWorkflow",
                Properties = "TestProperties",
                ConfiguredRPO = "10",
                DataLagValue = "5"
            };
            await _handler.Handle(createdEvent, CancellationToken.None);

            _mockPostgresMonitorStatusRepository.Verify(r => r.AddAsync(It.IsAny<Domain.Entities.PostgresMonitorStatus>()), Times.Never);
            _mockLogger.Verify(l => l.LogError(It.Is<string>(s => s.Contains("Invalid event data"))), Times.Once);
        }
    }
}
