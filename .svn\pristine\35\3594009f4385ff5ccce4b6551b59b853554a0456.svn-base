﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="">
            <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                <div class="d-flex align-items-center">
                    <h6 class="page_title">
                        <i class="cp-BIA-template"></i>
                        <span>FIA Templates List</span>
                    </h6>
                </div>
                <form class="d-flex align-items-center">
                    <div class="input-group me-2 w-auto">
                        <input type="search" id="fiaSearchInp" class="form-control" placeholder="Search" />
                        <div class="input-group-text">
                            <div class="dropdown">
                                <span data-bs-toggle="dropdown" title="Filter">
                                    <i class="cp-filter"></i>
                                </span>
                                <ul class="dropdown-menu filter-dropdown">
                                    <li>
                                        <h6 class="dropdown-header">Filter Search</h6>
                                    </li>
                                    <li class="dropdown-item">
                                        <div>
                                            <input class="form-check-input" type="checkbox" value="name=" id="fiaFilterName">
                                            <label class="form-check-label" for="fiaFilterName">
                                                Template Name
                                            </label>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-primary btn-sm fiaPermissionCreateModal" data-bs-toggle="modal" id="fiaOverallCreateModal" data-bs-target="#fiaCreateModal"><i class="cp-add me-1"></i>Create</button>
                </form>
            </div>
        </div>
        <div class="card-body pt-0">
            <table class="table table-hover dataTable" style="width:100%" id="fiaOverallTableList">
                <thead>
                    <tr>
                        <th>Sr.No</th>
                        <th>Template Name</th>
                        <th>Username</th>
                        <th>Template In Used</th>
                        <th>Template Used By</th>
                        <th>Created Date</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
    <!-- Modal -->
    <div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="fiaCreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <form class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-BIA-template"></i><span>FIA Templates Configuration</span></h6>
                    <button type="button" title="Close" class="btn-close fiaTemplateCancel" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="min-height:25rem ">
                    <div class="form-group">
                        <div class="form-label">Template Name</div>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="cp-name"></i>
                            </span>
                            <input autocomplete="off" class="form-control" type="text" id="fiaTemplateName" placeholder="Enter Template Name" maxlength="100">
                        </div>
                        <span id="fiaTemplateNameError"></span>
                    </div>
                    <div class="d-flex gap-2 align-items-center">
                        <div class="form-group w-100">
                            <div class="form-label">Impact Category</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-Impact"></i></span>
                                <select class="form-select-modal" id="fiaImpactCategory" data-placeholder="Select Impact Category" multiple>
                                    <option></option>
                                </select>
                            </div>
                            <span id="fiaImpactCategoryError"></span>
                        </div>
                        <span type="button" data-bs-target="#fiaImpactCategoryModalToggle" title="Add Impact Catagory" data-bs-toggle="modal" id="fiaImpactTypeModal"><i class="cp-circle-plus text-primary"></i></span>
                    </div>
                    <div class="d-flex gap-2 align-items-center">
                        <div class="form-group w-100">
                            <div class="form-label">Impact Type</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-activity-type"></i></span>
                                <select class="form-select-modal mb-0" id="fiaImpactType" data-placeholder="Select Impact Type" multiple>
                                    <option></option>
                                </select>
                            </div>
                            <span id="fiaImpactTypeError"></span>
                        </div>
                        <span type="button" data-bs-target="#fiaManageImpactCategoryModalToggle" title="Add Impact Type" id="fiaImpactMasterModal" data-bs-toggle="modal"><i class="cp-circle-plus text-primary"></i></span>
                    </div>
                    <div class="d-flex gap-2 align-items-center">
                        <div class="form-group w-100">
                            <div class="form-label">Interval</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-calendar"></i></span>
                                <select class="form-select-modal" id="fiaInterval" data-placeholder=" Select Interval" multiple>
                                    <option></option>
                                </select>
                            </div>
                            <span id="fiaIntervalError"></span>
                        </div>
                        <span type="button" data-bs-target="#fiaTimeIntervalModalToggle1" title="Add Interval" id="fiaTimeIntervalModal" data-bs-toggle="modal">
                            <i class="cp-circle-plus text-primary"></i>
                        </span>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm fiaOverallTableList fiaOverallCancel" data-bs-dismiss="modal" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" id="fiaTemplateSave">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    @* Impact Category Modal Start *@
    <div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="fiaImpactCategoryModalToggle" aria-hidden="true" aria-labelledby="fiaImpactCategoryModalToggle" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-Impact"></i><span>Continuity Patrol :: Impact Category</span></h6>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">Impact Category</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-Impact"></i>
                                    </span>
                                    <input autocomplete="off" class="form-control" maxlength="100" type="text" placeholder="Enter Impact Category" id="fiaModalImpactCatagory">
                                </div>
                                <span id="fiaModalImpactCatagoryError"></span>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">Impact Category Description (Optional)</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-description"></i>
                                    </span>
                                    <input autocomplete="off" maxlength="500" class="form-control" type="text" placeholder="Enter Category Description" id="fiaModalImpactDescription">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <table class="table table-hover dataTable" style="width:100%;table-layout:fixed" id="fiaTimeImpacttypetable">
                            <thead>
                                <tr>
                                    <th>Sr.No</th>
                                    <th>Impact Category Name</th>
                                    <th>Impact Category Description</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm fiaOverallCancel" data-bs-target="#fiaCreateModal" data-bs-toggle="modal" title="" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" title="" id="fiaImpacttypeBtnSave">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @* Impact Category Modal End *@
    @*Continuity Patrol :: Manage Impact Category To Impact Type Relationship Start *@
    <div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="fiaManageImpactCategoryModalToggle" aria-hidden="true" aria-labelledby="fiaImpactCategoryModalToggle" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-activity-type"></i><span>Continuity Patrol :: Manage Impact Category To Impact Type Relationship</span></h6>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12 col-md-12">
                            <div class="form-group">
                                <div class="form-label">Impact Category</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-Impact"></i></span>
                                    <select class="form-select-modal" data-placeholder="Select Impact Category" id="typeImpactCatagory">
                                        <option></option>
                                    </select>
                                </div>
                                <span id="typeImpactCatagoryError"></span>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">Impact Type</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-name"></i>
                                    </span>
                                    <input autocomplete="off" maxlength="100" class="form-control" type="text" placeholder="Enter Impact Type" id="typeImpactType">
                                </div>
                                <span id="typeImpactTypeError"></span>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">Impact Type Description (Optional)</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-description"></i>
                                    </span>
                                    <input autocomplete="off" class="form-control" maxlength="500" type="text" placeholder="Enter Impact Type Description" id="typeImpactDescription">
                                </div>
                                <span id="typeImpactDescriptionError"></span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <table class="table table-hover dataTable" id="fiaTimeImpactcatagorytable" style="width:100%;table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>Sr.No</th>
                                    <th>Impact Type</th>
                                    <th>Impact Type Description</th>
                                    <th>Impact Category</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>  </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm fiaOverallCancel" data-bs-target="#fiaCreateModal" data-bs-toggle="modal" title="" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" title="" id="fiaImpactMastersaveBtn">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @* Continuity Patrol :: Manage Impact Category To Impact Type Relationship End *@
    @* Continuity Patrol :: Time Interval Modal Start *@
    <div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="fiaTimeIntervalModalToggle1" aria-hidden="true" aria-labelledby="fiaImpactCategoryModalToggle1" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel ">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-calendar"></i><span>Continuity Patrol :: Time Interval</span></h6>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">Start Time Interval</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-apply-finish-time"></i>
                                    </span>
                                    <input autocomplete="off" class="form-control" placeholder="Enter start time interval" type="number" id="fiaMinTime">
                                </div>
                                <span id="fiaMinTimeError"></span>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">Hour or Days</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-calendar"></i></span>
                                    <select class="form-select-modal" data-placeholder="Select Time" id="fiaMinTimeUnit">
                                        <option value=""></option>
                                        <option value="Hours">Hours</option>
                                        <option value="Days">Days</option>
                                    </select>
                                </div>
                                <span id="fiaMinTimeUnitError"></span>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">End Time Interval</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-apply-finish-time"></i>
                                    </span>
                                    <input autocomplete="off" class="form-control" placeholder="Enter end time interval" type="number" id="fiaMaxTime">
                                </div>
                                <span id="fiaMaxTimeError"></span>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">Hour or Days</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-calendar"></i></span>
                                    <select class="form-select-modal" data-placeholder="Select Time" id="fiaMaxTimeUnit">
                                        <option value=""></option>
                                        <option value="Hours">Hours</option>
                                        <option value="Days">Days</option>
                                    </select>
                                </div>
                                <span id="fiaMaxTimeUnitError"></span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <table class="table table-hover dataTable" id="fiaTimeIntevalTable" style="width:100%">
                            <thead>
                                <tr>
                                    <th colspan="2">Time Interval</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm fiaOverallCancel" id="fiaTimeIntervalCancelBtn" data-bs-target="#fiaCreateModal" data-bs-toggle="modal" title="" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" id="fiaTimeIntervalSaveBtn" title="">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="ConfigurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true"></div>
    <div id="ConfigurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true"></div>
    @* Continuity Patrol :: Time Interval End *@
    <!--Modal Reject-->
    <div class="modal fade" data-bs-backdrop="static" id="fiaDeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p class="d-flex align-items-center justify-content-center gap-1">
                            You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="fiaDeletedId"></span>
                            data?
                        </p>
                    </div>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-target="#fiaTimeIntervalModalToggle1" data-bs-toggle="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="fiaConfirmDeleteButton" data-bs-target="#fiaTimeIntervalModalToggle1" data-bs-toggle="modal">Yes</button>
                </div>
            </div>
        </div>
    </div>
    <!--Modal Reject1-->
    <div class="modal fade" data-bs-backdrop="static" id="fiaImpactTypeDeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p class="d-flex align-items-center justify-content-center gap-1">
                            You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="fiaImpactTypeDeletedId"></span>
                            data?
                        </p>
                    </div>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-target="#fiaImpactCategoryModalToggle" data-bs-toggle="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="fiaImpactTypeConfirmDeleteButton" data-bs-target="#fiaImpactCategoryModalToggle" data-bs-toggle="modal">Yes</button>
                </div>
            </div>
        </div>
    </div>
    <!--Modal Reject2-->
    <div class="modal fade" data-bs-backdrop="static" id="fiaImpactMasterDeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p class="d-flex align-items-center justify-content-center gap-1">
                            You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="fiaImpactMasterDeletedId"></span>
                            data?
                        </p>
                    </div>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-target="#fiaManageImpactCategoryModalToggle" data-bs-toggle="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="fiaImpactMasterConfirmDeleteButton" data-bs-target="#fiaManageImpactCategoryModalToggle" data-bs-toggle="modal">Yes</button>
                </div>
            </div>
        </div>
    </div>
    <!--Modal Reject3-->
    <div class="modal fade" data-bs-backdrop="static" id="fiaOverallDelete" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p class="d-flex align-items-center justify-content-center gap-1">
                            You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="fiaOverallDeletedId"></span>
                            data?
                        </p>
                    </div>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="#fiaOverallTableList" data-bs-toggle="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="fiaOverallConfirmDeleteButton">Yes</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Configuration/FIA-BIA/FIA Templates/fiaTemplate.js"></script>
