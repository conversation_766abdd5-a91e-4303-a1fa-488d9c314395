﻿using ContinuityPatrol.Domain.ViewModels.LogViewerModel;

namespace ContinuityPatrol.Application.Features.LogViewer.Queries.GetList;

public class GetLogViewerListQueryHandler : IRequestHandler<GetLogViewerListQuery, List<LogViewerListVm>>
{
    private readonly IMapper _mapper;
    private readonly ILogViewerRepository _logViewerRepository;

    public GetLogViewerListQueryHandler(IMapper mapper, ILogViewerRepository logViewerRepository)
    {
        _mapper = mapper;
        _logViewerRepository = logViewerRepository;
    }

    public async Task<List<LogViewerListVm>> Handle(GetLogViewerListQuery request, CancellationToken cancellationToken)
    {
        var serverLogs = (await _logViewerRepository.ListAllAsync()).ToList();

        return serverLogs.Count == 0 ? new List<LogViewerListVm>() : _mapper.Map<List<LogViewerListVm>>(serverLogs);
    }
}