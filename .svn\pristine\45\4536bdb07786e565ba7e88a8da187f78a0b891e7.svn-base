using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Create;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Update;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetList;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftCategoryMasterModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DriftCategoryMasterControllerTests : IClassFixture<DriftCategoryMasterFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DriftCategoryMastersController _controller;
    private readonly DriftCategoryMasterFixture _driftCategoryMasterFixture;

    public DriftCategoryMasterControllerTests(DriftCategoryMasterFixture driftCategoryMasterFixture)
    {
        _driftCategoryMasterFixture = driftCategoryMasterFixture;

        var testBuilder = new ControllerTestBuilder<DriftCategoryMastersController>();
        _controller = testBuilder.CreateController(
            _ => new DriftCategoryMastersController(),
            out _mediatorMock);
    }

    #region GetDriftCategoryMasters Tests

    [Fact]
    public async Task GetDriftCategoryMasters_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedList = _driftCategoryMasterFixture.DriftCategoryMasterListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftCategoryMasterListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDriftCategoryMasters();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftCategoryMasterListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item => Assert.Contains("Enterprise", item.CategoryName));
    }

    [Fact]
    public async Task GetDriftCategoryMasters_WithEmptyResult_ReturnsOkWithEmptyList()
    {
        // Arrange
        var emptyList = new List<DriftCategoryMasterListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftCategoryMasterListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDriftCategoryMasters();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftCategoryMasterListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDriftCategoryMasters_WithException_ThrowsException()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftCategoryMasterListQuery>(), default))
            .ThrowsAsync(new NullReferenceException("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<NullReferenceException>(() => _controller.GetDriftCategoryMasters());
    }

    #endregion

    #region CreateDriftCategoryMaster Tests

    [Fact]
    public async Task CreateDriftCategoryMaster_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftCategoryMasterFixture.CreateDriftCategoryMasterCommand;
        var expectedResponse = _driftCategoryMasterFixture.CreateDriftCategoryMasterResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftCategoryMaster(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftCategoryMasterResponse>(createdResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task CreateDriftCategoryMaster_WithNullCommand_HandlesGracefully()
    {
        // Arrange
        CreateDriftCategoryMasterCommand nullCommand = null;

        var successResponse = new CreateDriftCategoryMasterResponse
        {
            Success = true,
            Message = "DriftCategoryMaster created successfully",
            Id = Guid.NewGuid().ToString()
        };

        _mediatorMock
            .Setup(m => m.Send(nullCommand, default))
            .ReturnsAsync(successResponse);

        // Act
        var result = await _controller.CreateDriftCategoryMaster(nullCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftCategoryMasterResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task CreateDriftCategoryMaster_WithInvalidCategoryName_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftCategoryMasterFixture.CreateDriftCategoryMasterCommand;
        command.CategoryName = ""; // Invalid empty name

        var failureResponse = new CreateDriftCategoryMasterResponse
        {
            Success = false,
            Message = "Category name cannot be empty"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.CreateDriftCategoryMaster(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftCategoryMasterResponse>(createdResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("empty", returnedResponse.Message);
    }

    #endregion

    #region UpdateDriftCategoryMaster Tests

    [Fact]
    public async Task UpdateDriftCategoryMaster_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _driftCategoryMasterFixture.UpdateDriftCategoryMasterCommand;
        var expectedResponse = _driftCategoryMasterFixture.UpdateDriftCategoryMasterResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftCategoryMaster(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftCategoryMasterResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
        Assert.True(Guid.TryParse(returnedResponse.Id, out _));
    }

    [Fact]
    public async Task UpdateDriftCategoryMaster_WithNullCommand_HandlesGracefully()
    {
        // Arrange
        UpdateDriftCategoryMasterCommand nullCommand = null;

        var successResponse = new UpdateDriftCategoryMasterResponse
        {
            Success = true,
            Message = "DriftCategoryMaster updated successfully",
            Id = Guid.NewGuid().ToString()
        };

        _mediatorMock
            .Setup(m => m.Send(nullCommand, default))
            .ReturnsAsync(successResponse);

        // Act
        var result = await _controller.UpdateDriftCategoryMaster(nullCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftCategoryMasterResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDriftCategoryMaster_WithInvalidId_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftCategoryMasterFixture.UpdateDriftCategoryMasterCommand;
        command.Id = "invalid-guid";

        var failureResponse = new UpdateDriftCategoryMasterResponse
        {
            Success = false,
            Message = "Invalid ID format"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.UpdateDriftCategoryMaster(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftCategoryMasterResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("Invalid", returnedResponse.Message);
    }

    #endregion

    #region GetDriftCategoryMasterById Tests

    [Fact]
    public async Task GetDriftCategoryMasterById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _driftCategoryMasterFixture.DriftCategoryMasterDetailVm;
        expectedDetail.Id = id;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftCategoryMasterDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftCategoryMasterById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftCategoryMasterDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.Id);
        Assert.Contains("Enterprise", returnedDetail.CategoryName);
    }

    [Fact]
    public async Task GetDriftCategoryMasterById_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDriftCategoryMasterById(invalidId));
    }

    [Fact]
    public async Task GetDriftCategoryMasterById_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetDriftCategoryMasterById(nullId));
    }

    #endregion

    #region DeleteDriftCategoryMaster Tests

    [Fact]
    public async Task DeleteDriftCategoryMaster_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftCategoryMasterFixture.DeleteDriftCategoryMasterResponse;
        expectedResponse.IsActive = false;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDriftCategoryMasterCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftCategoryMaster(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftCategoryMasterResponse>(okResult.Value);
        Assert.Equal(false, returnedResponse.IsActive);
        Assert.True(returnedResponse.Success);
        Assert.Contains("deleted successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task DeleteDriftCategoryMaster_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDriftCategoryMaster(invalidId));
    }

    [Fact]
    public async Task DeleteDriftCategoryMaster_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteDriftCategoryMaster(nullId));
    }

    #endregion

    #region GetPaginatedDriftCategoryMasters Tests

    [Fact]
    public async Task GetPaginatedDriftCategoryMasters_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _driftCategoryMasterFixture.GetDriftCategoryMasterPaginatedListQuery;
        var expectedResult = _driftCategoryMasterFixture.DriftCategoryMasterPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftCategoryMasters(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftCategoryMasterListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.True(returnedResult.Succeeded);
        Assert.NotEmpty(returnedResult.Data);
    }

    [Fact]
    public async Task GetPaginatedDriftCategoryMasters_WithNullQuery_HandlesGracefully()
    {
        // Arrange
        GetDriftCategoryMasterPaginatedListQuery nullQuery = null;

        var emptyResult = new PaginatedResult<DriftCategoryMasterListVm>
        {
            Data = new List<DriftCategoryMasterListVm>(),
            TotalCount = 0,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(nullQuery, default))
            .ReturnsAsync(emptyResult);

        // Act
        var result = await _controller.GetPaginatedDriftCategoryMasters(nullQuery);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftCategoryMasterListVm>>(okResult.Value);
        Assert.True(returnedResult.Succeeded);
        Assert.Empty(returnedResult.Data);
    }

    [Fact]
    public async Task GetPaginatedDriftCategoryMasters_WithInvalidPageSize_ReturnsEmptyResult()
    {
        // Arrange
        var query = _driftCategoryMasterFixture.GetDriftCategoryMasterPaginatedListQuery;
        query.PageSize = 0; // Invalid page size

        var emptyResult = new PaginatedResult<DriftCategoryMasterListVm>
        {
            Data = new List<DriftCategoryMasterListVm>(),
            TotalCount = 0,
            Succeeded = false
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(emptyResult);

        // Act
        var result = await _controller.GetPaginatedDriftCategoryMasters(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftCategoryMasterListVm>>(okResult.Value);
        Assert.False(returnedResult.Succeeded);
        Assert.Empty(returnedResult.Data);
    }

    #endregion

    #region IsDriftCategoryMasterNameUnique Tests

    [Fact]
    public async Task IsDriftCategoryMasterNameUnique_WithUniqueName_ReturnsTrue()
    {
        // Arrange
        var categoryName = "Unique Enterprise Category";
        var id = Guid.NewGuid().ToString();
        var query = new GetDriftCategoryMasterNameUniqueQuery {Name = categoryName,Id=id };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftCategoryMasterNameUniqueQuery>(q => q.Name == categoryName), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDriftCategoryMasterNameExist(categoryName,id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var isUnique = Assert.IsType<bool>(okResult.Value);
        Assert.True(isUnique);
    }

    [Fact]
    public async Task IsDriftCategoryMasterNameUnique_WithDuplicateName_ReturnsFalse()
    {
        // Arrange
        var categoryName = "Duplicate Enterprise Category";
        var id = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftCategoryMasterNameUniqueQuery>(q => q.Name == categoryName), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDriftCategoryMasterNameExist(categoryName,id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var isUnique = Assert.IsType<bool>(okResult.Value);
        Assert.False(isUnique);
    }

    [Fact]
    public async Task IsDriftCategoryMasterNameUnique_WithNullName_ThrowsArgumentNullException()
    {
        // Arrange
        string nullName = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.IsDriftCategoryMasterNameExist(nullName,""));
    }

    #endregion

    #region Additional Comprehensive Test Cases

    [Fact]
    public async Task CreateDriftCategoryMaster_WithUnicodeCharacters_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftCategoryMasterFixture.CreateDriftCategoryMasterCommand;
        command.CategoryName = "Enterprise 企业 Категория";
        var expectedResponse = _driftCategoryMasterFixture.CreateDriftCategoryMasterResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftCategoryMasterCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftCategoryMaster(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftCategoryMasterResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("企业", command.CategoryName);
    }

    [Fact]
    public async Task CreateDriftCategoryMaster_WithMaxLengthCategoryName_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftCategoryMasterFixture.CreateDriftCategoryMasterCommand;
        command.CategoryName = new string('A', 255); // Maximum length
        var expectedResponse = _driftCategoryMasterFixture.CreateDriftCategoryMasterResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftCategoryMasterCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftCategoryMaster(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftCategoryMasterResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal(255, command.CategoryName.Length);
    }

    [Fact]
    public async Task UpdateDriftCategoryMaster_WithCaseInsensitiveName_ReturnsOkResult()
    {
        // Arrange
        var command = _driftCategoryMasterFixture.UpdateDriftCategoryMasterCommand;
        command.CategoryName = "enterprise category"; // lowercase
        var expectedResponse = _driftCategoryMasterFixture.UpdateDriftCategoryMasterResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftCategoryMasterCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftCategoryMaster(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftCategoryMasterResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("enterprise category", command.CategoryName);
    }

    [Fact]
    public async Task GetPaginatedDriftCategoryMasters_WithLargePageSize_ReturnsOkResult()
    {
        // Arrange
        var query = _driftCategoryMasterFixture.GetDriftCategoryMasterPaginatedListQuery;
        query.PageSize = 1000; // Large page size
        var expectedResult = _driftCategoryMasterFixture.DriftCategoryMasterPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftCategoryMasterPaginatedListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftCategoryMasters(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftCategoryMasterListVm>>(okResult.Value);
        Assert.True(returnedResult.Succeeded);
        Assert.Equal(1000, query.PageSize);
    }

    [Fact]
    public async Task GetPaginatedDriftCategoryMasters_WithSortingByName_ReturnsOkResult()
    {
        // Arrange
        var query = _driftCategoryMasterFixture.GetDriftCategoryMasterPaginatedListQuery;
        query.SortColumn = "CategoryName";
        query.SortOrder = "ASC";
        var expectedResult = _driftCategoryMasterFixture.DriftCategoryMasterPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftCategoryMasterPaginatedListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftCategoryMasters(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftCategoryMasterListVm>>(okResult.Value);
        Assert.True(returnedResult.Succeeded);
        Assert.Equal("CategoryName", query.SortColumn);
        Assert.Equal("ASC", query.SortOrder);
    }

    [Fact]
    public async Task GetDriftCategoryMasterById_WithRecentlyCreatedId_ReturnsOkResult()
    {
        // Arrange
        var recentId = Guid.NewGuid().ToString();
        var expectedDetail = _driftCategoryMasterFixture.DriftCategoryMasterDetailVm;
        expectedDetail.Id = recentId;
       

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftCategoryMasterDetailQuery>(), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftCategoryMasterById(recentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftCategoryMasterDetailVm>(okResult.Value);
        Assert.Equal(recentId, returnedDetail.Id);
       
    }

    [Fact]
    public async Task DeleteDriftCategoryMaster_WithCascadeDelete_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftCategoryMasterFixture.DeleteDriftCategoryMasterResponse;
        expectedResponse.Message = "Category and related items deleted successfully";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteDriftCategoryMasterCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftCategoryMaster(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftCategoryMasterResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("related items", returnedResponse.Message);
    }

    [Fact]
    public async Task IsDriftCategoryMasterNameUnique_WithTrimmedWhitespace_ReturnsTrue()
    {
        // Arrange
        var categoryName = "  Enterprise Category  "; // With whitespace
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftCategoryMasterNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDriftCategoryMasterNameExist(categoryName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var isUnique = Assert.IsType<bool>(okResult.Value);
        Assert.False(isUnique); // Should return false for unique names
    }

    [Fact]
    public async Task GetDriftCategoryMasters_WithFilteredResults_ReturnsOkResult()
    {
        // Arrange
        var filteredList = new List<DriftCategoryMasterListVm>
        {
            new DriftCategoryMasterListVm
            {
                Id = Guid.NewGuid().ToString(),
                CategoryName = "Enterprise Critical Category",
                Logo = ""
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftCategoryMasterListQuery>(), default))
            .ReturnsAsync(filteredList);

        // Act
        var result = await _controller.GetDriftCategoryMasters();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftCategoryMasterListVm>>(okResult.Value);
        Assert.Single(returnedList);
        Assert.Contains("Critical", returnedList.First().CategoryName);
    }

    #endregion
}
