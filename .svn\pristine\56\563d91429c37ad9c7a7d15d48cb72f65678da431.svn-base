﻿using ContinuityPatrol.Application.Features.OracleMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.OracleMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.OracleMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.OracleMonitorLogs.Queries.GetList;
using ContinuityPatrol.Application.Features.OracleMonitorLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.OracleMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class OracleMonitorLogsController : CommonBaseController
{
    [HttpPost]
    [Authorize(Policy = Permissions.Manage.Create)]
    public async Task<ActionResult<CreateOracleMonitorLogResponse>> CreateOracleMonitorLog([FromBody] CreateOracleMonitorLogCommand createOracleMonitorLogCommand)
    {
        Logger.LogDebug($"Create Oracle Monitor Log  '{createOracleMonitorLogCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateOracleMonitorLog), await Mediator.Send(createOracleMonitorLogCommand));
    }

    [HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<List<OracleMonitorLogsListVm>>> GetAllOracleMonitorLogs()
    {
        Logger.LogDebug("Get All  Oracle Monitor Logs");

        return Ok(await Mediator.Send(new GetOracleMonitorLogsListQuery()));
    }
    [HttpGet("{id}")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<OracleMonitorLogsDetailVm>> OracleMonitorLogsById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Oracle MonitorLogs Detail By Id");

        Logger.LogDebug($"Get Oracle Monitor logs Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetOracleMonitorLogsDetailQuery { Id = id }));
    }
    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<List<OracleMonitorLogsListVm>>> GetPaginatedOracleMonitorLogs([FromQuery] GetOracleMonitorLogsPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Oracle MonitorLogs Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpGet("type")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<OracleMonitorLogsDetailByTypeVm>> GetOracleMonitorLogsByType(string type)
    {
        Guard.Against.NullOrEmpty(type, "Oracle MonitorLogs Detail By Type");

        Logger.LogDebug($"Get  Oracle Monitor logs Detail by Id '{type}'");

        return Ok(await Mediator.Send(new GetOracleMonitorLogsDetailByTypeQuery { Type = type }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllOracleMonitorLogsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllOracleMonitorLogsNameCacheKey };

        ClearCache(cacheKeys);
    }
}