﻿using ContinuityPatrol.Application.Features.SVCMssqlMonitorLog.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.SVCMssqlMonitorLog.Queries
{
    public class GetSVCMssqlMonitorLogPaginatedListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISVCMssqlMonitorLogRepository> _mockSVCMssqlMonitorLogRepository;
        private readonly GetSVCMssqlMonitorLogPaginatedListQueryHandler _handler;

        public GetSVCMssqlMonitorLogPaginatedListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSVCMssqlMonitorLogRepository = new Mock<ISVCMssqlMonitorLogRepository>();
            _handler = new GetSVCMssqlMonitorLogPaginatedListQueryHandler(_mockSVCMssqlMonitorLogRepository.Object, _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnPaginatedLogs_WhenDataExists()
        {
            var query = new GetSVCMssqlMonitorLogPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = "Info"
            };

            var logs = new List<Domain.Entities.SVCMssqlMonitorLog>
            {
                new Domain.Entities.SVCMssqlMonitorLog { ReferenceId = Guid.NewGuid().ToString(), WorkflowName = "Info", Type = "Log 1" },
                new Domain.Entities.SVCMssqlMonitorLog { ReferenceId = Guid.NewGuid().ToString(), WorkflowName = "Info", Type = "Log 2" }
            }.AsQueryable();

            var paginatedResult = new PaginatedResult<SVCMssqlMonitorLogPaginatedListVm>
            {
                Data = new List<SVCMssqlMonitorLogPaginatedListVm>
                {
                    new SVCMssqlMonitorLogPaginatedListVm { Id = logs.ElementAt(0).ReferenceId, WorkflowName = "Log 1", Type = "Info" },
                    new SVCMssqlMonitorLogPaginatedListVm { Id = logs.ElementAt(1).ReferenceId, WorkflowName = "Log 2", Type = "Info" }
                },
                TotalCount = logs.Count(),
                PageSize = 2
            };

            _mockSVCMssqlMonitorLogRepository.Setup(repo => repo.PaginatedListAllAsync())
                .Returns(logs);

            _mockMapper.Setup(m => m.Map<SVCMssqlMonitorLogPaginatedListVm>(It.IsAny<Domain.Entities.SVCMssqlMonitorLog>()))
                .Returns((Domain.Entities.SVCMssqlMonitorLog log) => new SVCMssqlMonitorLogPaginatedListVm
                {
                    Id = log.ReferenceId,
                    WorkflowName = "Message",
                    Type = "LogType"
                });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(paginatedResult.Data.Count, result.Data.Count);
            Assert.Equal(paginatedResult.Data.First().Id, result.Data.First().Id);

            _mockSVCMssqlMonitorLogRepository.Verify(repo => repo.PaginatedListAllAsync(), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyPaginationResult_WhenNoDataMatches()
        {
            var query = new GetSVCMssqlMonitorLogPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = "NotExist"
            };

            var logs = Enumerable.Empty<Domain.Entities.SVCMssqlMonitorLog>().AsQueryable();

            _mockSVCMssqlMonitorLogRepository.Setup(repo => repo.PaginatedListAllAsync())
                .Returns(logs);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);

            _mockSVCMssqlMonitorLogRepository.Verify(repo => repo.PaginatedListAllAsync(), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldApplyFilterAndPaginationCorrectly()
        {
            var query = new GetSVCMssqlMonitorLogPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 1,
                SearchString = "Error"
            };

            var logs = new List<Domain.Entities.SVCMssqlMonitorLog>
            {
                new Domain.Entities.SVCMssqlMonitorLog { ReferenceId = Guid.NewGuid().ToString(), WorkflowName = "Error", Type = "Error Log 1" },
                new Domain.Entities.SVCMssqlMonitorLog { ReferenceId = Guid.NewGuid().ToString(), WorkflowName = "Info", Type = "Info Log" }
            }.AsQueryable();

            _mockSVCMssqlMonitorLogRepository.Setup(repo => repo.PaginatedListAllAsync())
                .Returns(logs);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Single(result.Data);
            Assert.Equal("Error Log 1", result.Data.First().Type);

            _mockSVCMssqlMonitorLogRepository.Verify(repo => repo.PaginatedListAllAsync(), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldCallRepositoryMethodsOnce()
        {
            var query = new GetSVCMssqlMonitorLogPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = string.Empty
            };

            _mockSVCMssqlMonitorLogRepository.Setup(repo => repo.PaginatedListAllAsync())
                .Returns(Enumerable.Empty<Domain.Entities.SVCMssqlMonitorLog>().AsQueryable());

            await _handler.Handle(query, CancellationToken.None);

            _mockSVCMssqlMonitorLogRepository.Verify(repo => repo.PaginatedListAllAsync(), Times.Once);
        }
    }
}
