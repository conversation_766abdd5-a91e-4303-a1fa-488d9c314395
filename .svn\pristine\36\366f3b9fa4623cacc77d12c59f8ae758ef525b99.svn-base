﻿namespace ContinuityPatrol.Application.Features.SvcMsSqlMonitorStatus.Commands.Update;

public class UpdateSvcMsSqlMonitorStatusCommandHandler : IRequestHandler<UpdateSvcMsSqlMonitorStatusCommand,
    UpdateSvcMsSqlMonitorStatusResponse>
{
    private readonly IMapper _mapper;
    private readonly ISvcMsSqlMonitorStatusRepository _svcMsSqlMonitorStatusRepository;

    public UpdateSvcMsSqlMonitorStatusCommandHandler(IMapper mapper,
        ISvcMsSqlMonitorStatusRepository svcMsSqlMonitorStatusRepository)
    {
        _mapper = mapper;
        _svcMsSqlMonitorStatusRepository = svcMsSqlMonitorStatusRepository;
    }

    public async Task<UpdateSvcMsSqlMonitorStatusResponse> Handle(UpdateSvcMsSqlMonitorStatusCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _svcMsSqlMonitorStatusRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.SvcMsSqlMonitorStatus), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateSvcMsSqlMonitorStatusCommand),
            typeof(Domain.Entities.SvcMsSqlMonitorStatus));

        await _svcMsSqlMonitorStatusRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateSvcMsSqlMonitorStatusResponse
        {
            Message = Message.Update(nameof(Domain.Entities.SvcMsSqlMonitorStatus), eventToUpdate.ReferenceId),

            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}