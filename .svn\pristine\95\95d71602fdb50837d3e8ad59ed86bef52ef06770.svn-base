﻿using ContinuityPatrol.Application.Features.TeamResource.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.TeamResourceModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.TeamResource.Queries;

public class GetTeamResourcePaginatedListQueryHandlerTests : IClassFixture<TeamResourceFixture>
{
    private readonly GetTeamResourcePaginatedListQueryHandler _handler;

    private readonly Mock<ITeamResourceRepository> _mockTeamResourceRepository;

    public GetTeamResourcePaginatedListQueryHandlerTests(TeamResourceFixture teamResourceFixture)
    {
        var teamResourceNewFixture = teamResourceFixture;

        teamResourceNewFixture.TeamResources[0].TeamMasterId = "b328f475-3d82-4caa-938a-68dba3db992e";
        teamResourceNewFixture.TeamResources[0].TeamMasterName = "Resource_Tested";
        teamResourceNewFixture.TeamResources[0].ResourceName = "team_Config";
        teamResourceNewFixture.TeamResources[0].ResourceId = "3a844ab4-a8c6-48cc-8554-875fabba1ccd";


        teamResourceNewFixture.TeamResources[1].TeamMasterId = "b328f475-3d82-4caa-938a-68dba3db992e";
        teamResourceNewFixture.TeamResources[1].TeamMasterName = "Resource_Tested123";
        teamResourceNewFixture.TeamResources[1].ResourceName = "team_Config123";
        teamResourceNewFixture.TeamResources[1].ResourceId = "3a844ab4-a8c6-48cc-8554-875fabba1ccd";


        _mockTeamResourceRepository = TeamResourceRepositoryMocks.GetPaginatedTeamResourceRepository(teamResourceNewFixture.TeamResources);

        var mockTeamMasterRepository = new Mock<ITeamMasterRepository>();

        _handler = new GetTeamResourcePaginatedListQueryHandler(teamResourceNewFixture.Mapper, _mockTeamResourceRepository.Object, mockTeamMasterRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetTeamResourcePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Resource_Tested" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<TeamResourceListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedTeamResources_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetTeamResourcePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "",TeamName= "Resource_Tested" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<TeamResourceListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<TeamResourceListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].ResourceName.ShouldBe("team_Config");

        result.Data[0].TeamMasterName.ShouldBe("Resource_Tested");

        result.Data[0].TeamMasterId.ShouldBe("b328f475-3d82-4caa-938a-68dba3db992e");

        result.Data[0].ResourceId.ShouldBe("3a844ab4-a8c6-48cc-8554-875fabba1ccd");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetTeamResourcePaginatedListQuery { PageNumber = 1, PageSize = 10 ,TeamName = "Resource_Tested" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<TeamResourceListVm>>();

        result.TotalCount.ShouldBe(1);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_TeamResources_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetTeamResourcePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "", TeamName="Resource_Tested" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<TeamResourceListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].ResourceName.ShouldBe("team_Config");

        result.Data[0].TeamMasterName.ShouldBe("Resource_Tested");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetTeamResourcePaginatedListQuery(), CancellationToken.None);

        _mockTeamResourceRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}
