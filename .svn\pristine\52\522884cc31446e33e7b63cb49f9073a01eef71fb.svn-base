﻿//defaultcompany
$(function () {

    let form = $("#example-form");
    const errorElements = ['#Name-error', '#DisplayName-error', '#WebAddress-error', '#LoginName-error', '#LoginPassword-error', '#ConfirmPassword-error'];
    $("#cancel").on("click", function () {
        let currentStep = $('.example-form').steps('getCurrentStep');
        let stepTitle = currentStep?.title;
        if (stepTitle.includes('Login Information')) {
            $("#textLoginName, #Password, #textConfirmPassword").val('');
            $('.toggle-password i').addClass('cp-password-visible fs-6');
            $('#Password').attr('type', 'password');
            errorElements.forEach(element => {
                $(element).text('').removeClass('field-validation-error')
            });
        } else if (stepTitle.includes('Company Information')) {
            $("#textName, #textDisplayName, #textWebAddress").val('');
            errorElements.forEach(element => {
                $(element).text('').removeClass('field-validation-error')
            });
        }
    });

    $('#textName').on('keyup', async function () {
        const companyId = $('#textCompanyId').val();
        const value = $(this).val();
        const sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        await validateName(value, companyId);
    });
    $('#textDisplayName').on('keyup', async function () {
        const companyId = $('#textCompanyId').val();
        const value = $(this).val();
        const sanitizedDValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedDValue);
        await validateDisplayName(value, companyId);
    });
    $('#textWebAddress').on('keyup', async function () {
        const value = $(this).val();
        const sanitizedWValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedWValue);
        await validateWebAddress(value);
    });
    const secondChar = (value) => {
        return value.charAt(1) === " " ? 'Invalid format' : true
    }
    async function validateName(value, id = null) {
        const errorElement = $('#Name-error');
        if (!value) {
            errorElement.text('Enter company name')
                .addClass('field-validation-error');
            return false;
        }
        const validationResults = [           
            await SpecialCharValidate(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithSpace(value),
            await OnlyNumericsValidate(value),
            //await ShouldNotBeginWithNumber(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
        ];
        return await CommonValidation(errorElement, validationResults);
    }

    async function validateDisplayName(value, id = null) {
        const errorElement = $('#DisplayName-error');
        if (!value) {
            errorElement.text('Enter display name')
                .addClass('field-validation-error');
            return false;
        }
        const validationResults = [
            await SpecialCharValidate(value),
            //await ShouldNotAcceptMoreThanOneSpace(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithSpace(value),
            await OnlyNumericsValidate(value),
          // await ShouldNotBeginWithNumber(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await displaylength(value),
            await secondChar(value),

        ];
        return await CommonValidation(errorElement, validationResults);
    }

    async function validateWebAddress(value) {
        const errorElement = $('#WebAddress-error');
        if (!value) {
            errorElement.text('Enter web address')
                .addClass('field-validation-error')
            return false;
        }
        const validationResults = [await WebValidate(value)];
        return await CommonValidation(errorElement, validationResults);
    }

    $(".next_btn").on("click", async function () {
        const name = $("#textName").val();
        const displayName = $("#textDisplayName").val();
        const webAddress = $("#textWebAddress").val();
        const companyId = $('#textCompanyId').val();
        const isName = await validateName(name, companyId);
        const isDisplayName = await validateDisplayName(displayName, companyId);
        const isWebAddress = await validateWebAddress(webAddress);
        if (isName && isDisplayName && isWebAddress) {
            form.steps("next");
        }
    });

    const getVersion = async () => {
        let returnValue = '';
        await $.ajax({
            type: 'GET',
            url: RootUrl + 'ITAutomation/WorkflowTemplate/GetConfigurationUrl',
            datatype: "json",
            traditional: true,
            success: function (result) {
                $('.cpVersionData').val(result.cpVersion)
                $('.cpVersionData').text(result.cpVersion)
            }
        })
        return returnValue;
    }
    getVersion()


    function CommonValidation(errorElement, validationResults) {
        const failedValidations = validationResults.filter(result => result !== true);
        if (failedValidations.length > 0) {
            errorElement.text(failedValidations[0])
                .addClass('field-validation-error')
            return false;
        } else {
            errorElement.text('')
                .removeClass('field-validation-error')
            return true;
        }
    }

    const SpecialCharValidate = (value) => { const regex = /^[a-zA-Z0-9_\s]+$/; return regex.test(value) ? true : "Special characters not allowed"; }
    const OnlyNumericsValidate = (value) => { const regex = /^[0-9]+$/; return regex.test(value) ? "Only numerics not allowed" : true; }
    const ShouldNotBeginWithNumber = (value) => { return (!RegExp(/^[a-zA-Z]/).test(value)) ? "Should not begin with number" : true; }
    const ShouldNotBeginWithUnderScore = (value) => { return (RegExp(/(^_+)/).test(value)) ? "Should not begin with underscore" : true; }
    const ShouldNotBeginWithSpace = (value) => { return (!RegExp(/^(?![\s-])[\w\s-]+$/).test(value)) ? "Should not begin with space" : true; }
    const ShouldNotAcceptMoreThanOneSpace = (value) => { return (!RegExp(/^(?![\s-])[\w\s-]+$/).test(value)) ? "Should not accept more than one space" : true; }
    const SpaceWithUnderScore = (value) => { return (RegExp(/(([a-zA-z0-9]+)(\s+)(_)([a-zA-Z0-9]+)*)/).test(value)) ? "Invalid format" : true; }
    const minMaxlength = (value) => { return value.length > 2 && value.length < 101 ? true : "Between 3 to 100 characters" }
    const ShouldNotEndWithUnderScore = (value) => { return (RegExp(/([a-zA-Z0-9]+)(_+$)/).test(value)) ? "Should not end with underscore" : true; }
    const ShouldNotEndWithSpace = (value) => { return (!RegExp(/^[^\s]+(\s+[^\s]+)*$/).test(value)) ? "Should not end with space" : true; }
    const MultiUnderScoreRegex = (value) => { return (RegExp(/(([a-zA-z0-9]+)(_)(_+)([a-zA-Z0-9]+)*)/).test(value)) ? "Invalid format" : true; }
    const SpaceAndUnderScoreRegex = (value) => { return (RegExp(/(([a-zA-z0-9]+)(_)(\s+)([a-zA-Z0-9]+)*)/).test(value)) ? "Invalid format" : true; }
    //const WebAddressValidate = (value) => { return (!RegExp(/^((ftp|http|https|smtp):\/\/)??(www.)?(?!.*(ftp|http|https|www.))[a-zA-Z0-9_-]+(\.[a-zA-Z]+)+((\/)[\w]+)*(\/\w+\?[a-zA-Z0-9_]+=\w+(&[a-zA-Z0-9_]+=\w+)*)?\/?$/).test(value)) ? "Enter valid web address" : true; }
    //const webAddValidation = (value) => { return (!RegExp(/^((ftp|http|https|smtp):\/\/)??(WWW.)?(?!.*(ftp|http|https|WWW.))[a-zA-Z0-9_-]+(\.[a-zA-Z]+)+((\/)[\W]+)*(\/\W+\?[a-zA-Z0-9_]+=\W+(&[a-zA-Z0-9_]+=\W+)*)?\/?$/).test(value)) ? "Enter valid web address" : true; }
    const WebValidate = (value) => {
        return (!RegExp(/(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/gi).test(value)) ? "Enter valid web address" : true;
    }
    const WebAddressValidate = (value) => {
        return (!RegExp(/^((ftp|http|https|smtp):\/\/)??(www.)?(?!.*(ftp|http|https|www.))[a-zA-Z0-9_-]+(\.[a-zA-Z]+)+((\/)[\w]+)*(\/\w+\?[a-zA-Z0-9_]+=\w+(&[a-zA-Z0-9_]+=\w+)*)?\/?$/).test(value)) ? "Enter valid web address" : true;
    }
    
    const displaylength = (value) => { return value.length > 2 && value.length < 16 ? true : "Between 3 to 15 characters"; }
});