﻿
const configurationRequired = async () => {
    try {
        const result = await $.ajax({
            type: "GET",
            url: "/Configuration/Site/GetComponentDetails",
            dataType: "json",
            traditional: true
        });
        if (result.success) {
          
            const urlLowerCase = window.location.href?.toLowerCase();
            const businessdashboardLocat = urlLowerCase?.includes("serviceavailability");
            const itviewdashboardLocat = urlLowerCase?.includes("itresiliencyview");
            const analayticsdashboardLocat = urlLowerCase?.includes("analytics");
            const resiliencydashboardLocat = urlLowerCase?.includes("resiliencymapping")
            const siteLocat = urlLowerCase?.includes("site");
            const serviceLocat = urlLowerCase?.includes("operationalservice");
            const functionLocat = urlLowerCase?.includes("operationalfunction");
            const serverLocat = urlLowerCase?.includes("server");
            const databaseLocat = urlLowerCase?.includes("database");
            const replicationLocat = urlLowerCase?.includes("replication");

            const overallBS_noData = '<img src = "../../img/blur_view/operational_availability_view.png" style="width:100%;height: calc(100vh - 46px)">';
            const overallIT_noData = '<img src="../../img/blur_view/it_view.png" style="width:100%;height: calc(100vh - 46px)">';
            const overallAnalytics_noData = '<img src="../../img/blur_view/operational_analytics.png" style="width:100%;height: calc(100vh - 46px)">';
            const overallDcmapping_noData = '<img src="../../img/blur_view/dc_mapping.png" style="width:100%;height: calc(100vh - 46px)">';
            if (result?.data?.infraObject === 0 && businessdashboardLocat) {
                $(".pageblur").html(overallBS_noData);
            }
            if (result?.data?.infraObject === 0 && itviewdashboardLocat) {
                $(".pageblur").html(overallIT_noData);
            }
            if (result?.data?.infraObject === 0 && analayticsdashboardLocat) {
                $(".pageblur").html(overallAnalytics_noData);
            }
            if (result?.data?.infraObject === 0 && resiliencydashboardLocat) {
                $(".pageblur").html(overallDcmapping_noData);
            }

            if ((businessdashboardLocat || itviewdashboardLocat || analayticsdashboardLocat || resiliencydashboardLocat) && result?.data?.sites < 2) {
                getRequired("Site", '/Configuration/Site/List', 700);
            }
            else if ((businessdashboardLocat || itviewdashboardLocat || analayticsdashboardLocat || resiliencydashboardLocat) && result?.data?.operationService === 0) {
                getRequired("Operational Service", '/Configuration/OperationalService/List', 700);
            }
            else if ((businessdashboardLocat || itviewdashboardLocat || analayticsdashboardLocat || resiliencydashboardLocat) && result?.data?.operationFunction === 0) {
                $('#mytoastr').toast('hide')
                getRequired("Operational Function", '/Configuration/OperationalFunction/List', 700);
            }
            else if ((businessdashboardLocat || itviewdashboardLocat || analayticsdashboardLocat || resiliencydashboardLocat) && result?.data?.server < 2) {
                $('#mytoastr').toast('hide')
                getRequired("Server", '/Configuration/Server/List', 700);
            }
            else if ((businessdashboardLocat || itviewdashboardLocat || analayticsdashboardLocat || resiliencydashboardLocat) && result?.data?.database < 2 && (result.filteredPrServers.length >= 1 || result.filteredDrServers.length >= 1)) {
                getRequired("Database", '/Configuration/Database/List', 700);
            }
            else if ((businessdashboardLocat || itviewdashboardLocat || analayticsdashboardLocat || resiliencydashboardLocat) && result?.data?.replication === 0) {
                getRequired("Replication", '/Configuration/Replication/List', 700);
            }
            else if ((businessdashboardLocat || itviewdashboardLocat || analayticsdashboardLocat || resiliencydashboardLocat) && result?.data?.infraObject === 0) {
                getRequired("Infraobject", '/Configuration/Infraobject/List', 700);
            }
            else if (siteLocat && result?.data?.sites >= 2 && result?.data?.operationService === 0) {
                getRequired("Operational Service", '/Configuration/OperationalService/List', 1000);
            }
            else if (serviceLocat && result?.data?.operationService === 1 && result?.data?.operationFunction === 0) {
                getRequired("Operational Function", '/Configuration/OperationalFunction/List', 1000);
            }
            else if (functionLocat && result?.data?.operationFunction === 1 && result?.data?.server === 0) {
                getRequired("Server", '/Configuration/Server/List', 1000);
            }
            else if (serverLocat && result?.data?.server >= 1 && (result.filteredPrServers.length >= 1 || result.filteredDrServers.length >= 1) && result?.data?.database === 0) {
                getRequired("Database", '/Configuration/Database/List', 1000);
            }
            else if (databaseLocat && result?.data?.database >= 2 && result?.data?.replication === 0) {
                getRequired("Replication", '/Configuration/Replication/List', 1000);
            }
            else if (replicationLocat && result?.data?.replication === 1 && result?.data?.infraObject === 0) {
                getRequired("Infraobject", '/Configuration/Infraobject/List', 1000);
            }


            $("#redirect").on('click', () => {
                const configurationText = $("#configurationText").text().trim();
                switch (configurationText) {
                    case "Site":
                        window.location.assign('/Configuration/Site/List');
                        break;
                    case "Operational Service":
                        window.location.assign('/Configuration/OperationalService/List');
                        break;
                    case "Operational Function":
                        window.location.assign('/Configuration/OperationalFunction/List');
                        break;
                    case "Server":
                        window.location.assign('/Configuration/Server/List');
                        break;
                    case "Database":
                        window.location.assign('/Configuration/Database/List');
                        break;
                    case "Replication":
                        window.location.assign('/Configuration/Replication/List');
                        break;
                    case "Infraobject":
                        window.location.assign('/Configuration/Infraobject/List');
                        break;
                    default:
                        return;
                }

            });
        }

    } catch (error) {
       
    }
};

const getRequired = (text, url, timer) => {
    setTimeout(() => {
        $("#configurationText").text(text);
        $("#ConfigurationReq").toast("show");
        $("#redirect").on('click', () => {
            window.location.assign(url);
        });
    }, timer)

};


(() => {
    if (window.location.href.includes('/Dashboard/') || window.location.href.includes('/Configuration/')) {
        setTimeout(() => {
            configurationRequired();
        },1000)
        
    }
})()

 /// logoutTimer

const toastTriggers = document.getElementById('timeliveToastBtn')
const toastLiveExamples = document.getElementById('timeliveToast')

if (toastTriggers) {
    const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExamples)
    toastTriggers.addEventListener('click', () => {
        toastBootstrap.show()
    })
}


function flipTo(digit, n) {
    var current = digit.attr('data-num');
    digit.attr('data-num', n);
    digit.find('.front').attr('data-content', current);
    digit.find('.back, .under').attr('data-content', n);
    digit.find('.flap').css('display', 'block');
    setTimeout(function () {
        digit.find('.base').text(n);
        digit.find('.flap').css('display', 'none');
    }, 350);
}

function jumpTo(digit, n) {
    digit.attr('data-num', n);
    digit.find('.base').text(n);
}

function updateGroup(group, n, flip) {
    var digit1 = $('.ten' + group);
    var digit2 = $('.' + group);
    n = String(n);
    if (n.length == 1) n = '0' + n;
    var num1 = n.substr(0, 1);
    var num2 = n.substr(1, 1);
    if (digit1.attr('data-num') != num1) {
        if (flip) flipTo(digit1, num1);
        else jumpTo(digit1, num1);
    }
    if (digit2.attr('data-num') != num2) {
        if (flip) flipTo(digit2, num2);
        else jumpTo(digit2, num2);
    }
}

function setTime(flip) {
    var t = new Date();
    updateGroup('hour', t.getHours(), flip);
    updateGroup('min', t.getMinutes(), flip);
    updateGroup('sec', t.getSeconds(), flip);
}

$(document).ready(function () {

    setTime(false);
    setInterval(function () {
        setTime(true);
    }, 1000);

});