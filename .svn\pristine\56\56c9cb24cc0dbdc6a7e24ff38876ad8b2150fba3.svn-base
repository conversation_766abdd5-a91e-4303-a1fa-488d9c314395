﻿@model ContinuityPatrol.Domain.ViewModels.ApprovalMatrixModel.ApprovalMatrixListVm

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/timeline.css" rel="stylesheet" />
<style>
    .team-tabs .nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
        color: #fff !important;
        background: var(--bs-nav-link-color) !important;
        border: 0px;
    }
</style>
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="">
            <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                <div class="d-flex align-items-center">
                    <h6 class="page_title" title="Approval Matrix">
                        <i class="cp-approval-matrix"></i>
                        <span>Approval Request</span>
                    </h6>
                    <ul class="ms-2 nav nav-pills approval_request" id="pills-tab" role="tablist">
                        <li class="nav-item me-2" role="presentation">
                            <button class="nav-link active" id="pills-myrequest-tab" data-bs-toggle="pill"
                                    data-bs-target="#pills-myrequest" type="button" role="tab"
                                    aria-controls="pills-myrequest" aria-selected="true">
                                My request
                            </button>
                        </li>
                        <li class="nav-item me-2" role="presentation">
                            <button class="nav-link" id="pills-myapproval-tab" data-bs-toggle="pill"
                                    data-bs-target="#pills-myapproval" type="button" role="tab"
                                    aria-controls="pills-myapproval" aria-selected="false">
                                My approval
                            </button>
                        </li>

                    </ul>

                </div>
                <form class="d-flex">
                    <div class="input-group me-2 w-auto">
                        <input type="search" id="searchInputRequest" class="form-control" placeholder="Search" autocomplete="off" />
                        <div class="input-group-text">
                            <div class="dropdown">
                                <span data-bs-toggle="dropdown" title="Filter">
                                    <i class="cp-filter"></i>
                                </span>
                                <ul class="dropdown-menu filter-dropdown">
                                    <li>
                                        <h6 class="dropdown-header">Filter Search</h6>
                                    </li>
                                    <li class="dropdown-item">
                                        <div>
                                            <input class="form-check-input" type="checkbox" value="" id="nameRequest">
                                            <label class="form-check-label" for="nameRequest">
                                                Process Name
                                            </label>
                                        </div>
                                    </li>

                                </ul>
                            </div>
                        </div>
                    </div>
                    @*  <button type="button" class="btn btn-primary" data-bs-toggle="modal" id="createRequest"
                            data-bs-target="#CreateModal">
                        <i class="cp-add me-1"></i>Create
                    </button> *@
                </form>
            </div>
        </div>
        <div class="card-body pt-0">
            <div class="tab-content" id="pills-tabContent">
                <div class="tab-pane show active" id="pills-myrequest" role="tabpanel"
                     aria-labelledby="pills-myrequest-tab" tabindex="0">
                    <table id="dataTableListsMyRequest" class="datatable table table-hover dataTable no-footer" style="width:100% !important">
                        <thead>
                            <tr>
                                <th class="SrNo_th">Sr. No.</th>                               
                                <th>Request ID</th>
                                <th>Status</th>
                                <th>Reason</th>
                                <th>Created By</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>

                        </tbody>
                    </table>
                </div>
            </div>

            <div class="tab-content" id="pills-tabContent">
                <div class="tab-pane" id="pills-myapproval" role="tabpanel"
                     aria-labelledby="pills-myapproval-tab" tabindex="0">
                    <table id="dataTableListsMyApproval" class="datatable table table-hover dataTable no-footer" style="width:100% !important">
                        <thead>
                            <tr>
                                <th class="SrNo_th">Sr. No.</th>
                                <th>Request ID</th>
                                @*  <th>Process Name</th> *@
                                <th>Description</th>
                                <th>Status</th>
                                @*  <th>Approver</th>     *@                           
                                <th>Created By</th>
                                @* <th>End Date & Time</th> *@
                                <th>Created On</th>
                                <th class="Action-th">Action</th>
                            </tr>
                        </thead>
                        <tbody>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!--Modal Create-->
<div class="modal fade" data-bs-backdrop="static" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-xl ">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title">
                    <i class="cp-approval-matrix"></i>
                    <span>Approval Request</span>
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-2">
                        <div class="mb-0 h-100 card">
                            <div class="card-body">
                                <div class="ms-2">
                                    <h6>Template List</h6>
                                    <div id="templateLists" class="mt-3">
                                    </div>
                                </div>
                                @*  <ul class="ms-2 nav nav-pills" id="pills-tab" role="tablist">
                                <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="pills-processflow-tab"
                                data-bs-toggle="pill" data-bs-target="#pills-processflow" type="button"
                                role="tab" aria-controls="pills-processflow"
                                aria-selected="true">
                                Process Flow
                                </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pills-configure-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-configure" type="button" role="tab"
                                aria-controls="pills-configure" aria-selected="false">
                                Configure
                                </button>
                                </li>
                                </ul>

                                <div class="tab-content" id="pills-tabContent">
                                <div class="tab-pane fade show active" id="pills-processflow" role="tabpanel"
                                aria-labelledby="pills-processflow-tab" tabindex="0">
                                <div class="d-flex justify-content-between border-secondary-subtle mt-3 border p-2 pt-3 pb-3 rounded-3">
                                <div>
                                <i class="cp-name"></i>
                                <span class="ms-2">Process Name</span>
                                </div>
                                <div style="border: 2px dotted rgb(213, 217, 220);">&nbsp;&nbsp;</div>
                                </div>
                                <div class="d-flex justify-content-between mt-3 border-secondary-subtle border p-2 pt-3 pb-3 rounded-3">
                                <div>
                                <i class="cp-process"></i>
                                <span class="ms-2">Process</span>
                                </div>
                                <div style="border: 2px dotted rgb(213, 217, 220);">&nbsp;&nbsp;</div>
                                </div>
                                <div class="d-flex justify-content-between mt-3 border border-secondary-subtle p-2 pt-3 pb-3 rounded-3">
                                <div>
                                <i class="cp-condition"></i>
                                <span class="ms-2">Condition</span>
                                </div>
                                <div style="border: 2px dotted rgb(213, 217, 220);">&nbsp;&nbsp;</div>
                                </div>
                                <div class="d-flex justify-content-between mt-3 border border-secondary-subtle p-2 pt-3 pb-3 rounded-3">
                                <div>
                                <i class="cp-success"></i>
                                <span class="ms-2">Approved</span>
                                </div>
                                <div style="border: 2px dotted rgb(213, 217, 220);">&nbsp;&nbsp;</div>
                                </div>
                                <div class="d-flex justify-content-between mt-3 border border-secondary-subtle p-2 pt-3 pb-3 rounded-3">
                                <div>
                                <i class="cp-error"></i>
                                <span class="ms-2">Rejected</span>
                                </div>
                                <div style="border: 2px dotted rgb(213, 217, 220);">&nbsp;&nbsp;</div>
                                </div>
                                </div>
                                <div class="tab-pane fade show" id="pills-configure" role="tabpanel"
                                aria-labelledby="pills-configure-tab" tabindex="0">
                                <form>
                                <div class="mb-3 form-group">
                                <div class="form-label">Name</div>
                                <div class="input-group">
                                <span class="input-group-text">
                                <i class="cp-name"></i>
                                </span>
                                <input type="text" class="form-control"
                                placeholder="Enter Name" />
                                </div>
                                </div>
                                <div class="mb-3 form-group">
                                <div class="form-label">Description</div>
                                <div class="input-group">
                                <span class="input-group-text">
                                <i class="cp-description"></i>
                                </span>
                                <input type="text" class="form-control"
                                placeholder="Enter Description" />
                                </div>
                                </div>
                                <div class="mb-3 form-group">
                                <div class="form-label">Time</div>
                                <div class="input-group">
                                <span class="input-group-text">
                                <i class="cp-apply-finish-time"></i>
                                </span>
                                <input type="time" class="form-control" />
                                </div>
                                </div>
                                <div class="mb-3 ">
                                <label class="form-label"
                                for="formBasicEmail">Notification Type</label>
                                <div class="mb-3 d-flex gap-3">
                                <div class="form-check">
                                <input type="checkbox" id="formBasicCheckbox" class="form-check-input custom-cursor-default-hover" cursorshover="true">
                                <label title="" for="formBasicCheckbox" class="form-check-label custom-cursor-default-hover" cursorshover="true">Via Email</label>
                                </div>
                                <div class="form-check">
                                <input type="checkbox" id="formBasicCheckbox" class="form-check-input custom-cursor-default-hover">
                                <label title="" for="formBasicCheckbox" class="form-check-label custom-cursor-default-hover" cursorshover="true">Via Web</label>
                                </div>
                                </div>
                                </div>
                                <div class="mb-3">
                                <div class="form-label">User</div>
                                <div class="input-group">
                                <span class="input-group-text"><i class="cp-user"></i></span>
                                <select class="form-select-modal" title="Some placeholder text...">
                                <option value="One">One</option>
                                <option value="Two">Two</option>
                                <option value="Three">Three</option>
                                </select>
                                </div>
                                </div>
                                <div class="d-flex justify-content-end gap-2">
                                <button type="button" title="Cancel" class="btn btn-secondary">Cancel</button>
                                <button type="button" title="Save" class="btn btn-primary">Save</button>
                                </div>
                                </form>
                                </div>
                                </div>*@
                            </div>
                        </div>
                    </div>
                    <div class="col-7">
                        <div class="text-center dropContainer" id="templateDiagram" style="height: calc(100vh - 225px); overflow-y: auto; position: relative">
                        </div>
                        <button type="button" class="d-none btn btn-primary btn-sm" id="endTemplate">End</button>
                        @* <img alt="Clippathgroup" src="/img/isomatric/Clippathgroup.png" class="img-fluid"> *@
                    </div>
                    <div class="col-3">
                        <div class="mb-0 h-100 card p-3">    
                            <div id="editedProcessData">

                            </div>
                            @*                             <div class="card-body">
                            <ul class="ms-2 nav nav-pills" id="pills-tab" role="tablist">
                            <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="pills-Level-tab" data-bs-toggle="pill"
                            data-bs-target="#pills-Level" type="button" role="tab"
                            aria-controls="pills-Level" aria-selected="true">
                            <i class="cp-teams"></i> Level
                            </button>
                            </li>
                            <li class="nav-item" role="presentation">
                            <button class="nav-link" id="pills-Teams-tab" data-bs-toggle="pill"
                            data-bs-target="#pills-Teams" type="button" role="tab"
                            aria-controls="pills-Teams" aria-selected="false">
                            <i class="cp-teams"></i> Teams
                            </button>
                            </li>
                            </ul>

                            <div class="tab-content mt-3" id="pills-tabContent">
                            <div class="tab-pane fade show active" id="pills-Level" role="tabpanel" aria-labelledby="pills-Level-tab" tabindex="0">
                            <form>
                            <div class="mb-3 form-group">
                            <div class="form-label">Level Name</div>
                            <div class="input-group">
                            <span class="input-group-text">
                            <i class="cp-name"></i>
                            </span>
                            <input type="text" class="form-control"
                            placeholder="Enter Level Name" />
                            </div>
                            </div>
                            <div class="mb-3 form-group">
                            <div class="form-label">Level Description</div>
                            <div class="input-group">
                            <span class="input-group-text">
                            <i class="cp-name"></i>
                            </span>
                            <input type="text" class="form-control" placeholder="Enter Level Description" />
                            </div>
                            </div>
                            <div>
                            <div class="d-flex align-items-end gap-3 mb-3">
                            <div class="form-group w-50">
                            <label class="form-label custom-cursor-default-hover" for="formBasicEmail">Escalation Time</label>
                            <div class="input-group">
                            <span class="input-group-text" id="basic-addon1">
                            <i class="cp-apply-finish-time"></i>
                            </span>
                            <select class="form-select-modal">
                            <option>Select Hours</option>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            </select>
                            <span class="input-group-text form-label mb-0">Mins</span>
                            </div>
                            </div>
                            <div class="form-group w-50 ">
                            <div class="input-group">
                            <span class="input-group-text" id="basic-addon1">
                            <i class="cp-apply-finish-time"></i>
                            </span>
                            <select aria-label="Default select example" class="form-select-modal" id="formBasicEmail">
                            <option>Select Mins</option>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            </select>
                            <span class="input-group-text form-label mb-0">Mins</span>
                            </div>
                            </div>
                            </div>
                            </div>
                            <div class="mb-3">
                            <div class="form-label">Business Service Name</div>
                            <div class="input-group">
                            <span class="input-group-text"><i class="cp-admin"></i></span>
                            <select class="form-select-modal" title="Some placeholder text...">
                            <option value="One">Business Service</option>
                            <option value="Two">Two</option>
                            <option value="Three">Three</option>
                            </select>
                            </div>
                            </div>
                            <div class="mb-3">
                            <div class="form-label">Notification Type</div>
                            <div class="p-4">
                            <div class="d-flex align-items-center gap-3">
                            <div class="d-flex flex-column align-items-center">
                            <span class="p-3 shadow-sm rounded">
                            <i class="cp-email align-middle text-primary fw-semibold fs-5"></i>
                            </span>
                            <span class="mt-2">Email</span>
                            </div>
                            <div class="d-flex flex-column align-items-center">
                            <span class="p-3 shadow-sm rounded">
                            <i class="cp-message-alert align-middle text-primary fw-semibold fs-5"></i>
                            </span>
                            <span class="mt-2">SMS</span>
                            </div>
                            <div class="d-flex flex-column align-items-center">
                            <span class="p-3 shadow-sm rounded">
                            <i class="cp-alerts align-middle text-primary fw-semibold fs-5"></i>
                            </span>
                            <span class="mt-2">Application</span>
                            </div>
                            </div>

                            </div>
                            </div>
                            <div>
                            <div class="form-check form-check-inline">
                            <input name="group1" type="checkbox" class="form-check-input custom-cursor-default-hover">
                            <label title="" class="form-check-label custom-cursor-default-hover">Workflow Creation</label>
                            </div>
                            <div class="form-check form-check-inline">
                            <input name="group1" type="checkbox" class="form-check-input custom-cursor-default-hover" cursorshover="true">
                            <label title="" class="form-check-label custom-cursor-default-hover">Workflow Modification</label>
                            </div>
                            </div>
                            </form>
                            </div>
                            <div class="tab-pane fade show" id="pills-Teams" role="tabpanel" aria-labelledby="pills-Teams-tab" tabindex="0">
                            <div class="d-flex">
                            <div class="Filter_Search me-2 input-group">
                            <input autocomplete="off" type="search" id="txtSearch" class="form-control" value="" style="min-height: 34px;">
                            <span class="ps-1  input-group-text"><i class="cp-search"></i></span>
                            </div>
                            <div>
                            <div class="dropdown dropend">
                            <button type="button" class="btn btn-primary" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside">
                            Add
                            </button>
                            <div class="dropdown-menu py-0 shadow" style="width:16rem; border:1px solid #e9e9e9 !important;">
                            <form class="p-2">
                            <div class="mb-3 form-group">
                            <div class="form-label">Name</div>
                            <div class="input-group">
                            <span class="input-group-text">
                            <i class="cp-name"></i>
                            </span>
                            <input type="text" class="form-control" placeholder="Enter Name" />
                            </div>
                            </div>
                            <div class="mb-3 form-group">
                            <div class="form-label">Email ID</div>
                            <div class="input-group">
                            <span class="input-group-text">
                            <i class="cp-email"></i>
                            </span>
                            <input type="email" class="form-control" placeholder="Enter Email" />
                            </div>
                            </div>
                            <div class="mb-3 form-group">
                            <div class="form-label">Phone Number</div>
                            <div class="input-group">
                            <span class="input-group-text">
                            <i class="cp-mobile-icon"></i>
                            </span>
                            <input type="text" class="form-control" placeholder="Enter Phone Number" />
                            </div>
                            </div>
                            <div class="mb-3">
                            <div class="form-label">Teams Group</div>
                            <div class="input-group">
                            <span class="input-group-text">
                            <i class="cp-teams"></i>
                            </span>
                            <select class="form-select-modal" title="Some placeholder text...">
                            <option value="One">One</option>
                            <option value="Two">Two</option>
                            <option value="Three">Three</option>
                            </select>
                            </div>
                            </div>
                            <div class="text-end">
                            <button type="submit" class="btn btn-secondary">cancel</button>
                            <button type="submit" class="btn btn-primary">save</button>
                            </div>
                            </form>
                            </div>
                            </div>
                            </div>
                            </div>
                            <div class="accordion mt-3 border border-secondary-subtle rounded" id="accordionExample">
                            <div class="accordion-item border-secondary-subtle">
                            <div class="border-secondary-subtle border-bottom  p-2 d-flex justify-content-between">
                            <span>Team Name</span><span>
                            <span class="me-3">Mail</span><span class="me-4">SMS</span>
                            </span>
                            </div>
                            <h2 class="accordion-header p-2">
                            <button class="accordion-button" type="button"
                            data-bs-toggle="collapse" data-bs-target="#collapseOne"
                            aria-expanded="true" aria-controls="collapseOne">
                            <div class="d-flex justify-content-between w-100 align-items-center">
                            <div class="form-check">
                            <input type="checkbox" class="form-check-input">
                            <label title=""
                            class="form-check-label custom-cursor-default-hover"
                            cursorshover="true">Dev_Team</label>
                            </div>
                            <span>
                            <span class="me-4">
                            <i class="cp-email
                            "></i>
                            </span>
                            <span class="me-1">
                            <i class="cp-mobile-icon
                            "></i>
                            </span>
                            </span>
                            </div>
                            </button>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse show"
                            data-bs-parent="#accordionExample">
                            <div class="accordion-body pt-0">
                            <table class="table-borderless table">
                            <tbody>
                            <tr>
                            <td>
                            <div class="mt-0 align-middle form-check">
                            <input type="checkbox"
                            class="form-check-input"
                            cursorshover="true"><label title=""
                            class="form-check-label"
                            cursorshover="true">Dev_Team</label>
                            </div>
                            </td>
                            <td>
                            <img class="rounded-circle me-2"
                            alt="userimg"
                            src="/img/input_Icons/user-3.jpg"
                            width="30" height="30"
                            title="User">Ragul
                            </td>
                            <td><EMAIL> </td>

                            <td>
                            <div>
                            <input type="checkbox" name="toggle"
                            id="SMSNotificationtoggle"><label for="SMSNotificationtoggle"></label>
                            </div>
                            </td>
                            </tr>
                            </tbody>
                            </table>
                            </div>
                            </div>
                            </div>
                            <div class="accordion-item">
                            <h2 class="accordion-header  p-2">
                            <button class="accordion-button collapsed" type="button"
                            data-bs-toggle="collapse" data-bs-target="#collapseTwo"
                            aria-expanded="false" aria-controls="collapseTwo">
                            <div class="d-flex justify-content-between w-100 align-items-center">
                            <div class="form-check">
                            <input type="checkbox" class="form-check-input">
                            <label title=""
                            class="form-check-label custom-cursor-default-hover"
                            cursorshover="true">Dev_Team</label>
                            </div>
                            <span>
                            <span class="me-4">
                            <i class="cp-email
                            "></i>
                            </span>
                            <span class="me-1">
                            <i class="cp-mobile-icon
                            "></i>
                            </span>
                            </span>
                            </div>
                            </button>
                            </h2>
                            <div id="collapseTwo" class="accordion-collapse collapse"
                            data-bs-parent="#accordionExample">
                            <div class="accordion-body pt-0">
                            <table class="table-borderless table">
                            <tbody>
                            <tr>
                            <td>
                            <div class="mt-0 align-middle form-check">
                            <input type="checkbox"
                            class="form-check-input"
                            cursorshover="true"><label title=""
                            class="form-check-label"
                            cursorshover="true">Dev_Team</label>
                            </div>
                            </td>
                            <td>
                            <img class="rounded-circle me-2"
                            alt="userimg"
                            src="/img/input_Icons/user-3.jpg"
                            width="30" height="30"
                            title="User">Ragul
                            </td>
                            <td><EMAIL> </td>
                            <td>
                            <div>
                            <input type="checkbox" name="toggle" id="SMSNotificationtoggle"><label for="SMSNotificationtoggle"></label>
                            </div>
                            </td>
                            </tr>
                            </tbody>
                            </table>
                            </div>
                            </div>
                            </div>
                            </div>
                            </div>
                            </div>
                            </div>
                            *@
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except Optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#teamModal">Save</button>                   
                </div>
            </div>
        </div>
    </div>
</div>

<!--Modal View-->
<div class="modal fade" data-bs-backdrop="static" id="viewModalApprovalReject" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title">
                    <i class="cp-approval-matrix"></i>
                    <span>View Approval Matrix</span>
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pt-0">
                <div id="matrixTimeline" class="approvel-matrix-timeline">

                    <ul class="timeline mt-0" id="approvalMatrixTimeline">
                        <!-- Item 1 -->

                        @*  <li class="text-center">
                            <span class="date-wrapper rounded">Nov 12, 2023</span>

                        </li>
                        <li>
                            <div class="direction-r">
                                <div class="flag-wrapper">
                                    <span class="hexa" data-item-status="created"></span>
                                    <span class="time-wrapper"><span class="time small">10:28 AM</span><span class="alert alert-primary py-1 px-2 small rounded-pill">Created</span></span>
                                </div>
                                <div class="desc">Monobalan Created the matrix</div>
                            </div>
                        </li>

                        <!-- Item 2 -->
                        <li>
                            <div class="direction-l">
                                <div class="flag-wrapper">
                                    <span class="hexa" data-item-status="approval"></span>
                                    <span class="time-wrapper"><span class="alert alert-success py-1 px-2 small rounded-pill">Approval</span><span class="time small">10:28 AM</span></span>
                                    <span class="flag">Approval Matrix Template</span>
                                </div>
                                <div class="desc">Send approvel request to all the users</div>
                            </div>
                        </li>

                        <!-- Item 3 -->
                        <li>
                            <div class="direction-r">
                                <div class="flag-wrapper">
                                    <span class="hexa" data-item-status="rejected"></span>
                                    <span class="flag">Level - 01</span>
                                    <span class="time-wrapper"><span class="time small">10:28 AM</span><span class="alert alert-danger py-1 px-2 small rounded-pill">Rejected</span></span>
                                </div>
                                <div class="desc">Vinoth Kumar AG has rejected the matrix</div>
                            </div>
                        </li>
                        <!-- Item 4 -->
                        <li>
                            <div class="direction-l">
                                <div class="flag-wrapper">
                                    <span class="hexa" data-item-status="approval"></span>
                                    <span class="flag">Level - 01</span>
                                    <span class="time-wrapper"><span class="alert alert-success py-1 px-2 small rounded-pill">Approval</span><span class="time small">10:28 AM</span></span>
                                </div>
                                <div class="desc">Selvam has approved the matrix</div>
                            </div>
                        </li>
                        <!-- Item 5 -->
                        <li>
                            <div class="direction-r">
                                <div class="flag-wrapper">
                                    <span class="hexa" data-item-status="rejected"></span>
                                    <span class="flag">Level - 02</span>
                                    <span class="time-wrapper"><span class="time small">10:28 AM</span><span class="alert alert-danger py-1 px-2 small rounded-pill">Rejected</span></span>
                                </div>
                                <div class="desc">Martin Arasaratnam has approved the matrix</div>
                            </div>
                        </li>
                        <!-- Item 5 -->
                        <li>
                            <div class="direction-l">
                                <div class="flag-wrapper">
                                    <span class="hexa" data-item-status="completed"></span>
                                    <!-- <span class="flag">Level - 01</span> -->
                                    <span class="time-wrapper"><span class="alert alert-success py-1 px-2 small rounded-pill">Approval</span><span class="time small">10:28 AM</span></span>
                                </div>
                                <div class="desc">Request matrix completed successfully</div>
                            </div>
                        </li> *@
                    </ul>
                </div>

            </div>

        </div>
    </div>
</div>

<!--Modal withdraw-->

      <div class="modal fade" data-bs-backdrop="static" id="approvalModalWithdraw" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p>You want to withdraw <span class="font-weight-bolder text-primary" id="withdrawName">
                            </span> request?
                        </p>                      
                    </div>                   
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" id="cancelButtonWithdraw" data-bs-dismiss="modal">No</button>
                    <button type="button" class="btn btn-primary btn-sm" id="confirmWithdraw">Yes</button>
                </div>
                 <input type="hidden" id="withdrawRequestId"/>
                 <input type="hidden" id="withdrawId"/>
                 <input type="hidden" id="withdrawStatus"/>
            </div>
        </div>
    </div>

<!--Modal Approve-->

    <div class="modal fade" data-bs-backdrop="static" id="approvalModalRequest" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/Approve.png" alt="Approve Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p>You want to approve <span class="font-weight-bolder text-primary" id="approvalName">
                            </span> request?
                        </p>                      
                    </div>
                    <div class="mb-3 form-group">
                        <div class="form-label">Remark</div>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="cp-name"></i>
                            </span>
                            <input type="text" class="form-control" id="approveRemarkRequest" autocomplete="off"
                                   placeholder="Enter Remark" />                            
                        </div>
                        <span id="approveRemarkRequestError"></span>
                    </div>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" id="cancelButtonApprove" data-bs-dismiss="modal">No</button>
                    <button type="button" class="btn btn-primary btn-sm" id="confirmApproveRequest">Yes</button>
                </div>
                 <input type="hidden" id="approveRequestId"/>
                 <input type="hidden" id="approveProcessName"/>
                 <input type="hidden" id="approveApprovalStatus"/>
            </div>
        </div>
    </div>


<!--Modal Reject-->

    <div class="modal fade" data-bs-backdrop="static" id="rejectModalRequest" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">

                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p>You want to reject <span class="font-weight-bolder text-primary" id="rejectName"></span> request?</p>
                    </div>                      
                    <div class="mb-3 form-group">
                        <div class="form-label">Reason</div>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="cp-name"></i>
                            </span>
                            <input type="text" class="form-control" id="rejectReasonRequest" autocomplete="off"
                                   placeholder="Enter Reason" />                           
                        </div>
                        <span id="rejectReasonRequestError"></span>
                    </div>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" id="cancelButtonReject" data-bs-dismiss="modal">No</button>
                    <button type="button" class="btn btn-primary btn-sm" id="confirmRejectRequest">Yes</button>
                </div>
                 <input type="hidden" id="rejectRequestId"/>
                 <input type="hidden" id="rejectProcessName"/>
                 <input type="hidden" id="rejectApprovalStatus"/>
            </div>
        </div>
    </div>

<!--Team Modal -->
<div class="modal fade" id="teamModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="teamModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-sm">
        <div class="modal-content">
            <div class="modal-header team-tabs p-0">
                <nav class="w-100">
                    <div class="nav nav-tabs" id="nav-tab" role="tablist">
                        <button class="nav-link active w-50" id="nav-individuals-tab" data-bs-toggle="tab" data-bs-target="#nav-individuals" type="button" role="tab" aria-controls="nav-individuals" aria-selected="true">Individuals</button>
                        <button class="nav-link w-50" id="nav-teams-tab" data-bs-toggle="tab" data-bs-target="#nav-teams" type="button" role="tab" aria-controls="nav-teams" aria-selected="false">Teams</button>

                    </div>
                </nav>
            </div>
            <div class="modal-body " style="min-height:400px">
                <div class="tab-content" id="nav-tabContent">
                    <div class="tab-pane fade show active" id="nav-individuals" role="tabpanel" aria-labelledby="nav-individuals-tab" tabindex="0">
                        <h6>Select the users</h6>
                        <div class="d-flex align-items-center justify-content-between border-bottom py-2">
                            <div class="d-flex align-items-center gap-2">
                                <div class="">

                                    <img src="~/img/profile-img/user.jpg" width="40" class="img-fluid rounded-circle" />
                                </div>
                                <div>
                                    <p class="mb-0 fs-7 fw-semibold">Sakthi Saravanan R</p>
                                    <span class="fs-8">UX UI Designer</span>
                                </div>
                            </div>
                            <div>
                                <input class="form-check" type="checkbox" />
                            </div>
                        </div>
                        <div class="d-flex align-items-center justify-content-between border-bottom py-2">
                            <div class="d-flex align-items-center gap-2">
                                <div class="">

                                    <img src="~/img/profile-img/user.jpg" width="40" class="img-fluid rounded-circle" />
                                </div>
                                <div>
                                    <p class="mb-0 fs-7 fw-semibold">Sri Vignesh</p>
                                    <span class="fs-8">Developer</span>
                                </div>
                            </div>
                            <div>
                                <input class="form-check" type="checkbox" />
                            </div>
                        </div>
                        <div class="d-flex align-items-center justify-content-between border-bottom py-2">
                            <div class="d-flex align-items-center gap-2">
                                <div class="">

                                    <img src="~/img/profile-img/user.jpg" width="40" class="img-fluid rounded-circle" />
                                </div>
                                <div>
                                    <p class="mb-0 fs-7 fw-semibold">Vijay Sarva</p>
                                    <span class="fs-8">Senior Manager</span>
                                </div>
                            </div>
                            <div>
                                <input class="form-check" type="checkbox" />
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="nav-teams" role="tabpanel" aria-labelledby="nav-teams-tab" tabindex="0">
                        <h6>Select the Team</h6>
                        <div class="d-flex align-items-center justify-content-between border-bottom py-2">
                            <div class="d-flex  gap-2">
                                <div class="">

                                    <img src="~/img/profile-img/user.jpg" width="40" class="img-fluid rounded-circle" />
                                </div>
                                <div>
                                    <p class="mb-0 fs-7 fw-semibold">Design Team</p>
                                    <span class="fs-8">Managing Team</span>
                                    <div class="d-flex align-items-center gap-2 my-2">
                                        <div class="teams">
                                            <span class="avatar">
                                                <img src="https://picsum.photos/70">
                                            </span>
                                            <span class="avatar">
                                                <img src="https://picsum.photos/80">
                                            </span>
                                            <span class="avatar">
                                                <img src="https://picsum.photos/90">
                                            </span>
                                            <span class="avatar">
                                                <img src="https://picsum.photos/100">
                                            </span>
                                        </div>
                                        <span><small>+5 Members</small></span>
                                    </div>

                                </div>
                            </div>
                            <div>
                                <input class="form-check" type="checkbox" />
                            </div>
                        </div>
                        <div class="d-flex align-items-center justify-content-between border-bottom py-2">
                            <div class="d-flex  gap-2">
                                <div class="">

                                    <img src="~/img/profile-img/user.jpg" width="40" class="img-fluid rounded-circle" />
                                </div>
                                <div>
                                    <p class="mb-0 fs-7 fw-semibold">Design Team</p>
                                    <span class="fs-8">Managing Team</span>
                                    <div class="d-flex align-items-center gap-2 my-2">
                                        <div class="teams">
                                            <span class="avatar">
                                                <img src="https://picsum.photos/70">
                                            </span>
                                            <span class="avatar">
                                                <img src="https://picsum.photos/80">
                                            </span>
                                            <span class="avatar">
                                                <img src="https://picsum.photos/90">
                                            </span>
                                            <span class="avatar">
                                                <img src="https://picsum.photos/100">
                                            </span>
                                        </div>
                                        <span><small>+5 Members</small></span>
                                    </div>

                                </div>
                            </div>
                            <div>
                                <input class="form-check" type="checkbox" />
                            </div>
                        </div>
                        <div class="d-flex align-items-center justify-content-between border-bottom py-2">
                            <div class="d-flex  gap-2">
                                <div class="">

                                    <img src="~/img/profile-img/user.jpg" width="40" class="img-fluid rounded-circle" />
                                </div>
                                <div>
                                    <p class="mb-0 fs-7 fw-semibold">Design Team</p>
                                    <span class="fs-8">Managing Team</span>
                                    <div class="d-flex align-items-center gap-2 my-2">
                                        <div class="teams">
                                            <span class="avatar">
                                                <img src="https://picsum.photos/70">
                                            </span>
                                            <span class="avatar">
                                                <img src="https://picsum.photos/80">
                                            </span>
                                            <span class="avatar">
                                                <img src="https://picsum.photos/90">
                                            </span>
                                            <span class="avatar">
                                                <img src="https://picsum.photos/100">
                                            </span>
                                        </div>
                                        <span><small>+5 Members</small></span>
                                    </div>

                                </div>
                            </div>
                            <div>
                                <input class="form-check" type="checkbox" />
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm">Save</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<script src="~/js/Manage/approvalmatrix/request.js"></script>
