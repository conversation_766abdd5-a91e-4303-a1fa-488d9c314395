using AutoFixture;
using ContinuityPatrol.Application.Features.CyberComponent.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponent.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberComponent.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetBySiteId;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetInfrastructureSummary;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberComponentModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class CyberComponentFixture
{
    public CyberComponentListVm CyberComponentListVm { get; }
    public CyberComponentDetailVm CyberComponentDetailVm { get; }
    public CreateCyberComponentCommand CreateCyberComponentCommand { get; }
    public UpdateCyberComponentCommand UpdateCyberComponentCommand { get; }
    public DeleteCyberComponentCommand DeleteCyberComponentCommand { get; }
    public CyberComponentBySiteIdVm CyberComponentBySiteIdVm { get; }
    public GetInfrastructureSummaryVm GetInfrastructureSummaryVm { get; }
    public GetCyberComponentPaginatedListQuery GetCyberComponentPaginatedListQuery { get; }
    public GetCyberComponentNameUniqueQuery GetCyberComponentNameUniqueQuery { get; }

    public CyberComponentFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CyberComponentListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, () => $"Enterprise Component {fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.Properties, () => $"{{\"componentType\":\"{fixture.Create<string>().Substring(0, 8)}\",\"tier\":\"production\",\"criticality\":\"high\"}}")
            .With(b => b.SiteId, Guid.NewGuid().ToString())
            .With(b => b.SiteName, () => $"Enterprise Site {fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.Type, () => fixture.Create<bool>() ? "Database Server" : "Application Server")
            .With(b => b.ServerTypeId, Guid.NewGuid().ToString())
            .With(b => b.ServerType, () => fixture.Create<bool>() ? "Physical" : "Virtual")
            .With(b => b.Logo, () => $"logo_{fixture.Create<string>().Substring(0, 6)}.png")
            .With(b => b.Status, () => fixture.Create<bool>() ? "Active" : "Inactive")
            .With(b => b.Description, () => $"Enterprise cyber component for {fixture.Create<string>().Substring(0, 10)} operations"));

        fixture.Customize<CyberComponentDetailVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Enterprise Mission-Critical Database Server")
            .With(b => b.Properties, "{\"componentDetails\":{\"type\":\"database_server\",\"tier\":\"tier1\",\"criticality\":\"mission_critical\",\"specifications\":{\"cpu\":\"64 cores\",\"memory\":\"512GB\",\"storage\":\"10TB NVMe SSD\",\"network\":\"10Gbps\"},\"software\":{\"os\":\"Red Hat Enterprise Linux 8\",\"database\":\"Oracle 19c Enterprise\",\"clustering\":\"Oracle RAC\",\"backup\":\"RMAN with Data Guard\"},\"security\":{\"encryption\":\"TDE enabled\",\"authentication\":\"Kerberos\",\"auditing\":\"comprehensive\",\"compliance\":[\"SOX\",\"PCI-DSS\",\"GDPR\"]},\"monitoring\":{\"tools\":[\"Oracle Enterprise Manager\",\"Splunk\",\"Nagios\"],\"metrics\":[\"performance\",\"availability\",\"security\"],\"alerting\":\"24x7\"}}}")
            .With(b => b.SiteId, Guid.NewGuid().ToString())
            .With(b => b.SiteName, "Enterprise Primary Data Center")
            .With(b => b.Type, "Database Server")
            .With(b => b.ServerTypeId, Guid.NewGuid().ToString())
            .With(b => b.ServerType, "Physical")
            .With(b => b.Logo, "enterprise_database_server.png")
            .With(b => b.Description, "Mission-critical enterprise database server supporting core business applications with high availability, disaster recovery, and comprehensive security controls"));

        fixture.Customize<CreateCyberComponentCommand>(c => c
            .With(b => b.Name, "New Enterprise Application Server")
            .With(b => b.Properties, "{\"newComponent\":{\"type\":\"application_server\",\"tier\":\"tier2\",\"purpose\":\"web_application_hosting\",\"specifications\":{\"cpu\":\"32 cores\",\"memory\":\"256GB\",\"storage\":\"2TB SSD\"},\"software\":{\"os\":\"Ubuntu 20.04 LTS\",\"runtime\":\"Java 11\",\"application_server\":\"Apache Tomcat 9\",\"load_balancer\":\"Nginx\"},\"security\":{\"ssl\":\"enabled\",\"firewall\":\"configured\",\"monitoring\":\"enabled\"}}}")
            .With(b => b.SiteId, Guid.NewGuid().ToString())
            .With(b => b.SiteName, "Enterprise Application Site")
            .With(b => b.Type, "Application Server")
            .With(b => b.ServerTypeId, Guid.NewGuid().ToString())
            .With(b => b.ServerType, "Virtual")
            .With(b => b.Logo, "application_server.png")
            .With(b => b.Status, "Active")
            .With(b => b.Description, "New enterprise application server for hosting critical business applications"));

        fixture.Customize<UpdateCyberComponentCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Updated Enterprise Database Server")
            .With(b => b.Properties, "{\"updatedComponent\":{\"type\":\"database_server\",\"tier\":\"tier1\",\"enhancements\":[\"performance_tuning\",\"security_hardening\",\"monitoring_upgrade\"],\"specifications\":{\"cpu\":\"128 cores\",\"memory\":\"1TB\",\"storage\":\"20TB NVMe SSD\"},\"new_features\":{\"ai_optimization\":\"enabled\",\"auto_scaling\":\"configured\",\"advanced_encryption\":\"implemented\"}}}")
            .With(b => b.SiteId, Guid.NewGuid().ToString())
            .With(b => b.SiteName, "Enterprise Updated Data Center")
            .With(b => b.Type, "Enhanced Database Server")
            .With(b => b.ServerTypeId, Guid.NewGuid().ToString())
            .With(b => b.ServerType, "Physical")
            .With(b => b.Logo, "enhanced_database_server.png")
            .With(b => b.Status, "Active")
            .With(b => b.Description, "Updated enterprise database server with enhanced performance, security, and monitoring capabilities"));

        fixture.Customize<DeleteCyberComponentCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<CyberComponentBySiteIdVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Site-Specific Enterprise Component")
            .With(b => b.Properties, "{\"siteComponent\":{\"location\":\"primary_datacenter\",\"rack\":\"A-01\",\"power\":\"redundant\",\"cooling\":\"dedicated\"}}")
            .With(b => b.SiteId, Guid.NewGuid().ToString())
            .With(b => b.SiteName, "Enterprise Primary Site")
            .With(b => b.Type, "Infrastructure Component")
            .With(b => b.ServerTypeId, Guid.NewGuid().ToString())
            .With(b => b.ServerType, "Physical")
            .With(b => b.Logo, "site_component.png")
            .With(b => b.Status, "Active")
            .With(b => b.Description, "Site-specific enterprise component for primary data center operations"));

        fixture.Customize<GetInfrastructureSummaryVm>(c => c
            .With(b => b.EntityType, "Database Servers")
            .With(b => b.Count, 25)
            .With(b => b.ServerInfrastructureSummaryVms, new List<ServerInfrastructureSummaryVm>
            {
                new ServerInfrastructureSummaryVm
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Enterprise DB Server 01",
                    IpAddress = "**********",
                    HostName = "entdb01.enterprise.com",
                    SiteId = Guid.NewGuid().ToString(),
                    SiteName = "Enterprise Primary DC",
                    Status = "Active"
                }
            }));

        fixture.Customize<GetCyberComponentPaginatedListQuery>(c => c
            .With(b => b.PageNumber, 1)
            .With(b => b.PageSize, 10)
            .With(b => b.SearchString, "Enterprise")
            .With(b => b.SortColumn, "Name")
            .With(b => b.SortOrder, "ASC"));

        fixture.Customize<GetCyberComponentNameUniqueQuery>(c => c
            .With(b => b.Name, "Enterprise Component Name")
            .With(b => b.Id, Guid.NewGuid().ToString()));

        CyberComponentListVm = fixture.Create<CyberComponentListVm>();
        CyberComponentDetailVm = fixture.Create<CyberComponentDetailVm>();
        CreateCyberComponentCommand = fixture.Create<CreateCyberComponentCommand>();
        UpdateCyberComponentCommand = fixture.Create<UpdateCyberComponentCommand>();
        DeleteCyberComponentCommand = fixture.Create<DeleteCyberComponentCommand>();
        CyberComponentBySiteIdVm = fixture.Create<CyberComponentBySiteIdVm>();
        GetInfrastructureSummaryVm = fixture.Create<GetInfrastructureSummaryVm>();
        GetCyberComponentPaginatedListQuery = fixture.Create<GetCyberComponentPaginatedListQuery>();
        GetCyberComponentNameUniqueQuery = fixture.Create<GetCyberComponentNameUniqueQuery>();
    }
}
