﻿using ContinuityPatrol.Application.Features.ComponentType.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.ComponentType.Commands;

public class CreateComponentTypeTests : IClassFixture<ComponentTypeFixture>
{
    private readonly ComponentTypeFixture _componentTypeFixture;
    private readonly Mock<IComponentTypeRepository> _mockComponentTypeRepository;
    private readonly CreateComponentTypeCommandHandler _handler;

    public CreateComponentTypeTests(ComponentTypeFixture componentTypeFixture)
    {
        _componentTypeFixture = componentTypeFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockComponentTypeRepository = ComponentTypeRepositoryMocks.CreateComponentTypeRepository(_componentTypeFixture.ComponentTypes);

        _handler = new CreateComponentTypeCommandHandler(_componentTypeFixture.Mapper, _mockComponentTypeRepository.Object, mockPublisher.Object);

        _componentTypeFixture.CreateComponentTypeCommand.Properties =
            "{\"name\":\"MSSQL\",\"version\":\"[\\\"2015\\\",\\\"2016\\\",\\\"2017\\\",\\\"2018\\\",\\\"2019\\\"]\",\"icon\":\"cp-mssql\"}";

    }

    [Fact]
    public async Task Handle_Should_IncreaseComponentTypeCount_When_AddValidComponentType()
    {
        await _handler.Handle(_componentTypeFixture.CreateComponentTypeCommand, CancellationToken.None);

        var allCategories = await _mockComponentTypeRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_componentTypeFixture.ComponentTypes.Count);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulComponentTypeResponse_When_AddValidComponentType()
    {
        var result = await _handler.Handle(_componentTypeFixture.CreateComponentTypeCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateComponentTypeResponse));

        result.ComponentTypeId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_componentTypeFixture.CreateComponentTypeCommand, CancellationToken.None);

        _mockComponentTypeRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.ComponentType>()), Times.Once);
    }
}
