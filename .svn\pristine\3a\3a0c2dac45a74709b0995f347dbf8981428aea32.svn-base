﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Create;

public class CreateWorkflowOperationGroupCommand : IRequest<CreateWorkflowOperationGroupResponse>
{
    public string ProfileId { get; set; }
    public string ProfileName { get; set; }
    public string Password { get; set; }

    [JsonIgnore] public string CompanyId { get; set; }

    public string Description { get; set; }
    public string UserName { get; set; }
    public string RunMode { get; set; }
    public bool IsDrCalendar { get; set; }
    public string DrCalendarId { get; set; }
    public List<CreateWorkflowOperationGroupListCommand> CreateWorkflowOperationGroupListCommands { get; set; }
}