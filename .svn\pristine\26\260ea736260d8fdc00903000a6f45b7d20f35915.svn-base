using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberComponentMapping.Events.Delete;

public class CyberComponentMappingDeletedEventHandler : INotificationHandler<CyberComponentMappingDeletedEvent>
{
    private readonly ILogger<CyberComponentMappingDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberComponentMappingDeletedEventHandler(ILoggedInUserService userService,
        ILogger<CyberComponentMappingDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(CyberComponentMappingDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.CyberResiliencyMapping}",
            Entity = Modules.CyberResiliencyMapping.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"Cyber Resiliency Mapping '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Cyber Resiliency Mapping '{deletedEvent.Name}' deleted successfully.");
    }
}