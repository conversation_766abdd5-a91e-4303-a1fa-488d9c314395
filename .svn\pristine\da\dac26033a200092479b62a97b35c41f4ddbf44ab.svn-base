using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixUsersModel;
using ContinuityPatrol.Shared.Core.Wrapper;


namespace ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetPaginatedList;

public class GetApprovalMatrixUsersPaginatedListQueryHandler : IRequestHandler<GetApprovalMatrixUsersPaginatedListQuery, PaginatedResult<ApprovalMatrixUsersListVm>>
{
    private readonly IApprovalMatrixUsersRepository _approvalMatrixUsersRepository;
    private readonly IMapper _mapper;

    public GetApprovalMatrixUsersPaginatedListQueryHandler(IMapper mapper, IApprovalMatrixUsersRepository approvalMatrixUsersRepository)
    {
        _mapper = mapper;
        _approvalMatrixUsersRepository = approvalMatrixUsersRepository;
    }

    public async Task<PaginatedResult<ApprovalMatrixUsersListVm>> Handle(GetApprovalMatrixUsersPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _approvalMatrixUsersRepository.GetPaginatedQuery();

        var productFilterSpec = new ApprovalMatrixUsersFilterSpecification(request.SearchString);

        var approvalMatrixUsersList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<ApprovalMatrixUsersListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return approvalMatrixUsersList;
    }
}
