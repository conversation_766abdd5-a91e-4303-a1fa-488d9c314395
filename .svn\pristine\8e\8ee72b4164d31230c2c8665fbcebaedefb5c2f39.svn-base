﻿using ContinuityPatrol.Domain.Entities;
using Moq;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class LicenseManagerRepositoryMocks
{
    public static Mock<ILicenseManagerRepository> CreateLicenseManagerRepository(List<LicenseManager> licenseManagers)
    {
        var licenseManagerRepository = new Mock<ILicenseManagerRepository>();
        licenseManagerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(licenseManagers);
        licenseManagerRepository.Setup(repo => repo.GetDateByExpireTime(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<string>())).ReturnsAsync(licenseManagers[0].ExpiryDate);
        licenseManagerRepository.Setup(repo => repo.AddAsync(It.IsAny<LicenseManager>())).ReturnsAsync(
            (LicenseManager licenseManager) =>
            {
                licenseManager.Id = new Fixture().Create<int>();
                licenseManager.ReferenceId = new Fixture().Create<Guid>().ToString();
                licenseManagers.Add(licenseManager);
                return licenseManager;
            });

        return licenseManagerRepository;
    }

    public static Mock<ILicenseManagerRepository> UpdateBaseLicenseRepository(List<LicenseManager> licenseManagers)
    {
        var licenseManagerRepository = new Mock<ILicenseManagerRepository>();
        licenseManagerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(licenseManagers);
        licenseManagerRepository.Setup(repo => repo.GetDateByExpireTime(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<string>())).ReturnsAsync(licenseManagers[0].ExpiryDate);
        licenseManagerRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => licenseManagers.SingleOrDefault(x => x.ReferenceId == i));
        licenseManagerRepository.Setup(repo => repo.UpdateAsync(It.IsAny<LicenseManager>())).ReturnsAsync((LicenseManager licenseManager) =>
            {
                var index = licenseManagers.FindIndex(item => item.Id == licenseManager.Id);

                licenseManagers[index] = licenseManager;

                return licenseManager;
            });

        return licenseManagerRepository;
    }

    //public static Mock<ILicenseManagerRepository> DeleteLicenseManagerRepository(List<LicenseManager> licenseManagers)
    //{
    //    var licenseManagerRepository = new Mock<ILicenseManagerRepository>();
    //    licenseManagerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(licenseManagers);

    //    licenseManagerRepository.Setup(repo => repo.GetBaseLicenseByParentIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => licenseManagers.SingleOrDefault(x => x.ReferenceId == i));

    //    licenseManagerRepository.Setup(repo => repo.UpdateAsync(It.IsAny<LicenseManager>())).ReturnsAsync((LicenseManager licenseManager) =>
    //    {
    //        var index = licenseManagers.FindIndex(item => item.ReferenceId == licenseManager.ReferenceId);

    //        licenseManager.IsActive = false;
    //        licenseManagers[index] = licenseManager;

    //        return licenseManager;
    //    });

    //    return licenseManagerRepository;
    //}

    public static Mock<ILicenseManagerRepository> GetLicenseManagerRepository(List<LicenseManager> licenseManagers)
    {
        var licenseManagerRepository = new Mock<ILicenseManagerRepository>();

        licenseManagerRepository.Setup(repo => repo.ListAllLicense()).ReturnsAsync(licenseManagers);

        licenseManagerRepository.Setup(repo => repo.GetLicenseDetailByIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => licenseManagers.SingleOrDefault(x => x.ReferenceId == i));
    
        return licenseManagerRepository;
    }

    public static Mock<ILicenseManagerRepository> GetPaginatedLicenseManagerRepository(List<LicenseManager> licenseManagers)
    {
        var licenseManagerRepository = new Mock<ILicenseManagerRepository>();

        var queryableLicenseManager = licenseManagers.BuildMock();

        licenseManagerRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableLicenseManager);

        return licenseManagerRepository;
    }

    public static Mock<ILicenseManagerRepository> GetLicenseManagerNamesRepository(List<LicenseManager> licenseManagers)
    {
        var licenseManagerRepository = new Mock<ILicenseManagerRepository>();

        licenseManagerRepository.Setup(repo => repo.GetLicensePoNumber()).ReturnsAsync(licenseManagers);

        return licenseManagerRepository;
    }

    public static Mock<ILicenseManagerRepository> GetLicenseManagerEmptyRepository()
    {
        var licenseManagerRepository = new Mock<ILicenseManagerRepository>();

        licenseManagerRepository.Setup(repo => repo.ListAllBaseLicense()).ReturnsAsync(new List<LicenseManager>());

        return licenseManagerRepository;
    }

    public static Mock<ILicenseManagerRepository> GetLicenseDetailByCompanyIdRepository(List<LicenseManager> licenseManagers)
    {
        var licenseManagerRepository = new Mock<ILicenseManagerRepository>();

        licenseManagerRepository.Setup(repo => repo.GetLicenseDetailByCompanyId(It.IsAny<string>())).ReturnsAsync((string i) => licenseManagers.Where(x => x.CompanyId == i).ToList());

        return licenseManagerRepository;
    }


    //public static Mock<ILicenseManagerRepository> GetByPoNumberRepository(List<LicenseManager> licenseManagers)
    //{
    //    //var licenseManagerRepository = new Mock<ILicenseManagerRepository>();
    //    //licenseManagerRepository.Setup(repo => repo.ListAllLicense()).ReturnsAsync(licenseManagers);

    //    //licenseManagerRepository.Setup(repo => repo.GetLicenseDetailByPoNumber(It.IsAny<string>())).ReturnsAsync((string i) => licenseManagers.SingleOrDefault(x => x.PONumber == i));


    //    //return licenseManagerRepository;
    //}

    //Events
    public static Mock<IUserActivityRepository> CreateLicenseManagerEventRepository(List<UserActivity> userActivities)
    {
        var licenseManagerEventRepository = new Mock<IUserActivityRepository>();

        licenseManagerEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return licenseManagerEventRepository;
    }

    public static Mock<ILoggedInUserService> LoggedInUserServiceMock()
    {
        var mock=new Mock<ILoggedInUserService>();
        mock.Setup(x=>x.IsParent).Returns(true);
        return mock;
    }
}