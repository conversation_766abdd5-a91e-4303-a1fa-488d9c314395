﻿namespace ContinuityPatrol.Application.Features.LicenseManager.Queries.GetDecommissionByPoNumber;

public class DecommissionDetailQueryHandler : IRequestHandler<DecommissionDetailQuery, object>
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly IMapper _mapper;
    private readonly IReplicationRepository _replicationRepository;
    private readonly IServerRepository _serverRepository;
    private readonly IWorkflowInfraObjectRepository _workflowInfraObjectRepository;
    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;
    private readonly IWorkflowRepository _workflowRepository;

    public DecommissionDetailQueryHandler(I<PERSON>apper mapper, IServerRepository serverRepository,
        IDatabaseRepository databaseRepository, IReplicationRepository replicationRepository,
        IInfraObjectRepository infraObjectRepository, IWorkflowRepository workflowRepository,
        ILicenseInfoRepository licenseInfoRepository, ILicenseManagerRepository licenseManagerRepository,
        IWorkflowInfraObjectRepository workflowInfraObjectRepository,
        IWorkflowProfileInfoRepository workflowProfileInfoRepository)
    {
        _mapper = mapper;
        _serverRepository = serverRepository;
        _databaseRepository = databaseRepository;
        _replicationRepository = replicationRepository;
        _infraObjectRepository = infraObjectRepository;
        _workflowRepository = workflowRepository;
        _licenseInfoRepository = licenseInfoRepository;
        _licenseManagerRepository = licenseManagerRepository;
        _workflowInfraObjectRepository = workflowInfraObjectRepository;
        _workflowProfileInfoRepository = workflowProfileInfoRepository;
    }

    public async Task<object> Handle(DecommissionDetailQuery request, CancellationToken cancellationToken)
    {
        var licenseInfo =
            await _licenseInfoRepository.GetLicenseInfoByLicenseIdAndEntityId(request.LicenseId, request.EntityId);

        var infraList = new List<Domain.Entities.InfraObject>();

        var workflow = new List<Domain.Entities.Workflow>();

        var workflowProfileInfos = new List<Domain.Entities.WorkflowProfileInfo>();

        var workflowInfraObject = new List<Domain.Entities.WorkflowInfraObject>();

        Guard.Against.NullOrDeactive(licenseInfo, nameof(Domain.Entities.LicenseInfo),
            new NotFoundException(nameof(Domain.Entities.LicenseInfo), request.LicenseId));

        var license = await _licenseManagerRepository.GetLicenseDetailByIdAsync(licenseInfo.LicenseId);

        Guard.Against.NullOrDeactive(license, nameof(Domain.Entities.LicenseManager),
            new NotFoundException(nameof(Domain.Entities.LicenseManager), request.LicenseId));


        if (request.EntityType.Trim().ToLower().Equals("server"))
        {
            var servers = await _serverRepository.GetByReferenceIdAsync(request.EntityId);

            Guard.Against.NullOrDeactive(servers, nameof(Domain.Entities.Server),
                new NotFoundException(nameof(Domain.Entities.Server), request.EntityName));

            var databases = await _databaseRepository.GetDatabaseByServerId(servers.ReferenceId);

            var serverInfra = await _infraObjectRepository.GetInfraObjectByServerId(request.EntityId);

            infraList.AddRange(serverInfra);

            databases.ForEach(x =>
            {
                var databaseInfra = _infraObjectRepository.GetInfraObjectByDatabaseId(x.ReferenceId).Result;

                infraList.AddRange(databaseInfra);

                workflow = _workflowRepository.GetWorkflowPropertiesByDatabaseId(x.ReferenceId).Result;
            });

            infraList.ForEach(x =>
            {
                workflowInfraObject = _workflowInfraObjectRepository
                    .GetWorkflowInfraObjectDetailByInfraObjectId(x.ReferenceId).Result;

                //var wfMapping = _mapper.Map<List<Domain.Entities.Workflow>>(workflowInfraObject);

                //workflow.AddRangeAsync(wfMapping);

                workflowProfileInfos = _workflowProfileInfoRepository.GetProfileIdAttachByInfraObjectId(x.ReferenceId)
                    .Result;
            });

            var workflowDto = await _workflowRepository.GetWorkflowPropertiesByServerId(servers.ReferenceId);

            workflow.AddRange(workflowDto);

            var serverMap = _mapper.Map<DecommissionDetailVm>(servers);

            serverMap.DecommissionDatabaseDetailVm = _mapper.Map<List<DecommissionDatabaseDetailVm>>(databases);

            var infraObjects = infraList?.DistinctBy(x => x?.ReferenceId).ToList();

            serverMap.DecommissionInfraObjectDetailVm =
                _mapper.Map<List<DecommissionInfraObjectDetailVm>>(infraObjects);

            var workflowMap = workflow?.DistinctBy(x => x?.ReferenceId).ToList();

            workflowMap.ForEach(x =>
            {
                var workflowProfileInfo =
                    _workflowProfileInfoRepository.GetProfileIdAttachByWorkflowId(x.ReferenceId).Result;

                var wfInfraObject = _workflowInfraObjectRepository.GetInfraObjectFromWorkflowId(x.ReferenceId).Result;

                workflowInfraObject.AddRange(wfInfraObject);

                workflowProfileInfos.AddRange(workflowProfileInfo);
            });

            serverMap.DecommissionWorkflowDetailVms = _mapper.Map<List<DecommissionWorkflowDetailVm>>(workflowMap);

            var workflowInfra = workflowInfraObject?.Where(x => x != null).DistinctBy(x => x.ReferenceId).ToList();

            serverMap.DecommissionWorkflowInfraObjectDetailVms =
                _mapper.Map<List<DecommissionWorkflowInfraObjectDetailVm>>(workflowInfra);

            var wfProfileInfo = workflowProfileInfos?.Where(x => x != null)?.DistinctBy(x => x?.ReferenceId).ToList();

            serverMap.DecommissionWorkflowProfiles = _mapper.Map<List<DecommissionWorkflowProfile>>(wfProfileInfo);

            return serverMap;
        }

        if (request.EntityType.Trim().ToLower().Equals("database"))
        {
            var databases = await _databaseRepository.GetByReferenceIdAsync(request.EntityId);

            Guard.Against.NullOrDeactive(databases, nameof(Domain.Entities.Database),
                new NotFoundException(nameof(Domain.Entities.Database), request.EntityName));

            var databaseInfra = await _infraObjectRepository.GetInfraObjectByDatabaseId(databases.ReferenceId);

            workflow = await _workflowRepository.GetWorkflowPropertiesByDatabaseId(databases.ReferenceId);

            databaseInfra.ForEach(x =>
            {
                var workflowInfraObject = _workflowInfraObjectRepository
                    .GetWorkflowInfraObjectDetailByInfraObjectId(x.ReferenceId).Result;

                //var wfMapping = _mapper.Map<List<Domain.Entities.Workflow>>(workflowInfraObject);

                //workflow.AddRangeAsync(wfMapping);

                workflowProfileInfos = _workflowProfileInfoRepository.GetProfileIdAttachByInfraObjectId(x.ReferenceId)
                    .Result;
            });

            var databaseMap = _mapper.Map<DecommissionDatabase>(databases);

            databaseMap.DecommissionInfraObjectDetailVm =
                _mapper.Map<List<DecommissionInfraObjectDetailVm>>(databaseInfra);

            var workflowMap = workflow?.DistinctBy(x => x?.ReferenceId).ToList();

            workflowMap.ForEach(x =>
            {
                var workflowProfileInfo =
                    _workflowProfileInfoRepository.GetProfileIdAttachByWorkflowId(x.ReferenceId).Result;

                var wfInfraObject = _workflowInfraObjectRepository.GetInfraObjectFromWorkflowId(x.ReferenceId).Result;

                workflowInfraObject.AddRange(wfInfraObject);

                workflowProfileInfos.AddRange(workflowProfileInfo);
            });

            databaseMap.DecommissionWorkflowDetailVms = _mapper.Map<List<DecommissionWorkflowDetailVm>>(workflowMap);

            var workflowInfra = workflowInfraObject?.Where(x => x != null).DistinctBy(x => x.ReferenceId).ToList();

            databaseMap.DecommissionWorkflowInfraObjectDetailVms =
                _mapper.Map<List<DecommissionWorkflowInfraObjectDetailVm>>(workflowInfra);

            var wfProfileInfo = workflowProfileInfos?.Where(x => x != null)?.DistinctBy(x => x?.ReferenceId).ToList();

            databaseMap.DecommissionWorkflowProfiles = _mapper.Map<List<DecommissionWorkflowProfile>>(wfProfileInfo);

            return databaseMap;
        }

        if (request.EntityType.Trim().ToLower().Equals("replication"))
        {
            var replication = await _replicationRepository.GetByReferenceIdAsync(request.EntityId);

            Guard.Against.NullOrDeactive(replication, nameof(Domain.Entities.Replication),
                new NotFoundException(nameof(Domain.Entities.Replication), request.EntityName));

            var replicationInfra = await _infraObjectRepository.GetInfraObjectByReplicationId(replication.ReferenceId);

            workflow = await _workflowRepository.GetWorkflowPropertiesByReplicationId(replication.ReferenceId);

            replicationInfra.ForEach(x =>
            {
                var workflowInfraObject = _workflowInfraObjectRepository
                    .GetWorkflowInfraObjectDetailByInfraObjectId(x.ReferenceId).Result;

                //var wfMapping = _mapper.Map<List<Domain.Entities.Workflow>>(workflowInfraObject);

                //workflow.AddRangeAsync(wfMapping);

                workflowProfileInfos = _workflowProfileInfoRepository.GetProfileIdAttachByInfraObjectId(x.ReferenceId)
                    .Result;
            });

            var replicationMap = _mapper.Map<DecommissionReplication>(replication);

            replicationMap.DecommissionInfraObjectDetailVm =
                _mapper.Map<List<DecommissionInfraObjectDetailVm>>(replicationInfra);

            var workflowMap = workflow?.DistinctBy(x => x?.ReferenceId).ToList();

            workflowMap.ForEach(x =>
            {
                var workflowProfileInfo =
                    _workflowProfileInfoRepository.GetProfileIdAttachByWorkflowId(x.ReferenceId).Result;

                var wfInfraObject = _workflowInfraObjectRepository.GetInfraObjectFromWorkflowId(x.ReferenceId).Result;

                workflowInfraObject.AddRange(wfInfraObject);

                workflowProfileInfos.AddRange(workflowProfileInfo);
            });

            replicationMap.DecommissionWorkflowDetailVms = _mapper.Map<List<DecommissionWorkflowDetailVm>>(workflowMap);

            var workflowInfra = workflowInfraObject?.Where(x => x != null).DistinctBy(x => x.ReferenceId).ToList();

            replicationMap.DecommissionWorkflowInfraObjectDetailVms =
                _mapper.Map<List<DecommissionWorkflowInfraObjectDetailVm>>(workflowInfra);


            var wfProfileInfo = workflowProfileInfos?.Where(x => x != null)?.DistinctBy(x => x?.ReferenceId).ToList();

            replicationMap.DecommissionWorkflowProfiles = _mapper.Map<List<DecommissionWorkflowProfile>>(wfProfileInfo);

            return replicationMap;
        }

        throw new InvalidException($"Unsupported type:{request.EntityType}");
    }
}