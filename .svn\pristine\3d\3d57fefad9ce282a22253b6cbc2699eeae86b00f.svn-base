﻿using ContinuityPatrol.Application.Features.RpForVmMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.RpForVmMonitorStatus.Queries.GetSymmetrix;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Manage;

public class RpForVmMonitorStatusService:BaseService, IRpForVmMonitorStatusService
{
    public RpForVmMonitorStatusService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<RpForVmMonitorStatusDetailByInfraObjectIdAndGroupNameVm> GetRpForVmMonitorStatusDetailByInfraObjectIdAndConsistencyGroupName(string infraObjectId, string consistancyGroupName)
    {
        Logger.LogDebug($"Get RpForVm Monitor Status Detail By InfraObjectId {infraObjectId} and ConsistancyGroupName '{consistancyGroupName}'");

        return await Mediator.Send(new GetRpForVmMonitorStatusDetailByInfraObjectIdAndGroupNameQuery
        {
            InfraObjectId = infraObjectId,
            ConsistencyGroupName = consistancyGroupName
        });
    }

    public async Task<SymmetrixListVm> GetSymmetrix(GetSymmetrixListQuery query)
    {
        Logger.LogDebug("Get SymmetrixList");

        // ClearDataCache();

        return await Mediator.Send(query);
    }

}
