﻿let parameterListName = [], updateProperties = [], selectedValues = [], properties = "", updateId = "", globalDeletedId = "", globalId = "", dataTable
let profileurls = {
    isnameExits: "Drift/Driftprofile/IsNameExist",
    getProfilePagination: "/Drift/DriftProfile/GetPagination",
    GetDriftCategoryList: "Drift/Driftprofile/GetDriftCategoryList",
    CreateOrUpdate: "Drift/DriftProfile/CreateOrUpdate",
    Delete: "Drift/DriftProfile/Delete"
}
function driftprofiledebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}
$(function () {
    let createPermission = $("#profileDriftCreate").data("create-permission").toLowerCase();
    let deletePermission = $("#profileDriftDelete").data("delete-permission").toLowerCase();

    if (createPermission == 'false') {
        $("#profileCreateButton").removeClass('#profileCreateButton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }
    dataTable = $('#driftProfileTable').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }, infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": profileurls.getProfilePagination,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : ""
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#profileSearchInp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                        json.recordsTotal = json?.data?.totalPages;
                        json.recordsFiltered = json?.data?.totalCount;
                        if (json?.data?.data?.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        updateProperties = []
                        updateProperties.push(json.data.data)

                        return json?.data?.data;
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [0, 1, 2],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
                            return (page * meta.settings._iDisplayLength) + meta.row + 1;
                        }
                        return data;
                    }, "orderable": false,
                },
                {
                    "data": "name", "name": "Profile Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title="${data == null ? "NA" : data}"> ${data == null ? "NA" : data}</span></td>`
                    }
                },
                {
                    "render": function (data, type, row) {
                        if (createPermission == "true" && deletePermission == "true") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="edit-button btnDriftEdit" name="profileOverallUpdate"  data_catagory="${row.componentCategoryProperties}"   data_name="${row.name}" updateId="${row.id}" data-bs-toggle="modal" data-bs-target="#profileCreateModal" >
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="delete-button btnDriftDelete" delete_id="${row.id}" deletename="${row.name}" data-bs-toggle="modal" data-bs-target="#profileDeleteModal">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>
                        </td>`;
                        }
                        if (createPermission == "false" && deletePermission == "true") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                              <span role="button" title="Edit" class="icon-disabled">
                                <i class="cp-edit"></i>
                            </span>
                                <span role="button" title="Delete" class="delete-button btnDriftDelete" delete_id="${row.id}" deletename="${row.name}" data-bs-toggle="modal" data-bs-target="#profileDeleteModal">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>
                        </td>`;
                        }
                        if (createPermission == "true" && deletePermission == "false") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="edit-button btnDriftEdit" name="profileOverallUpdate"  data_catagory="${row.componentCategoryProperties}"   data_name="${row.name}" updateId="${row.id}" data-bs-toggle="modal" data-bs-target="#profileCreateModal" >
                                    <i class="cp-edit"></i>

                                                    <span role="button" title="Delete" class="delete-button" data-user-id="${row.id}" data-user-name="${row.loginName}" data-bs-toggle="modal" data-bs-target="#profileDeleteModal">
                                                        <i class="cp-Delete"></i>
                                                    </span>         
                            </div>
                        </td>`;
                        }
                        if (createPermission == "false" && deletePermission == "false") {
                           
                                return `<td>
                            <div class="d-flex align-items-center  gap-2">
                              <span role="button" title="Edit" class="icon-disabled">
                                <i class="cp-edit"></i>
                            </span>
                                <span role="button" title="Delete" class="icon-disabled">
                        <i class="cp-Delete"></i>
                    </span>
                            </div>
                        </td>`;
                        }
                    },
                    "orderable": false,
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        }
    )

    $('#profileSearchInp').on('keydown input', driftprofiledebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        } 
        const nameCheckbox = $("#profileNames");
        const inputValue = $('#profileSearchInp').val();
            if (nameCheckbox.is(':checked')) {
                selectedValues.push(nameCheckbox.val() + inputValue);
            }
        var currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {
                if (e.target.value && json.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false)
        }
    }, 500))
})

    $(".profileClose").on("click", function () {
        $("#addProfileData").empty()
        $(".profileListCheckbox").prop("checked", false).removeAttr("disabled")
        $("#profileName").val("")
        listCancel()
        $("#profileCreateModal").modal("hide")
        $("#addProfileSave").text("Save")
        $("#profileNameError").text("").removeClass('field-validation-error');
    })
function listCancel() {
        $("#parameterListSearch").val("")
        $(".profileListCheckbox,.profileMainCheckbox").prop("checked", false)
        $("#profileListCheckboxError,#profileMainCheckboxError").text("").removeClass('field-validation-error');
    }
    $(document).on('click', '.btnDriftEdit', function () {
        let data = this;
        $("#addProfileSave").text("Update")
        if ($(data).attr('name') == "profileOverallUpdate") {
            $("#profileName").val($(data).attr('data_name'))
            $("#addProfileData").empty()
            updateId = $(data).attr('updateId')
            updateProperties[0]?.forEach((x, i) => {     
                if (x.id == updateId) {
                    globalId = x.id
                    let updateProperty = JSON.parse(x.properties)
                    updateProperty?.forEach((x, i) => {
                        var html = ""
                        html += '<div id="nodata_parameter" ><details class="categorydetails categorydetails' + x.categoryName + '" open=""><summary class="categorysummary d-flex align-items-center categorysummary' + x.categoryName + ' text-truncate" title=""><span class="w-100 d-flex align-items-center justify-content-between"><span><i custom-cursor-on-hover"=""></i>' + x.categoryName + '</span><span class="cp-Delete" data_delete="' + x.categoryId + '" categoryName="categorydetails' + x.categoryName + '" parentname="' + x.categoryName + '" onclick="overalldelete(this)"></span></span></summary>'
                          x?.children?.forEach((y, j) => {
                            let thers = y.Threshold == "" ? "" : y.Threshold
                            setTimeout(() => {
                                if (thers.length > 20) {
                                    $(".paramshowhide" + y.parameterID + "").addClass("text-truncate").css({ "maxWidth": "300px" }).attr("title", thers)
                                } else {
                                    $(".paramshowhide" + y.parameterID + "").removeClass("text-truncate").attr("title", "")
                                }
                            }, 300)
                            let Thresholdshowhide = y.Threshold == "" ? "d-none" : ""
                            let NotificationData = y.Notification ? "checked" : ""
                            let update_enable_disable = y.parameter_enable_disable == "true" ? "background-color:#DCDCDC" : ""
                            let update_enable_disable_text = y.parameter_enable_disable == "true" ? "Enabled" : "Disabled"
                            parameterListName?.forEach((x, i) => {
                                x.id == y.parameterID ? y.ParameterName = x.name : ""
                                x.id == y.parameterID ? y.ParameterIcon = x.icon : ""
                            })
                            html += '<div id="' + y.parameterID + '" class="profileparamchild enable-disabled' + y.parameterID + '" style=' + update_enable_disable + '>'
                            html += '<div class="d-flex align-items-center justify-content-between ms-4 mb-2">'
                              html += '<span role="button" parameter_enable_disable="' + y.parameter_enable_disable +'" paramId="' + y.parameterID + '" threshould="' + thers + '" class="text-truncate w-75 parameter_value_check  parameter_value_check' + y.parameterID + '"  title="' + y.ParameterName + '">'
                            html += '<i class="' + y.ParameterIcon + ' me-1" icon="' + y.ParameterIcon + '"></i> ' + y.ParameterName + ''
                            html += '</span>'
                            html += '<div class="d-flex gap-2 ">'
                            html += '<span style="font-size:10px" class="badge text-bg-warning py-1 paramshowhide' + y.parameterID + '  ' + Thresholdshowhide + '">Thershold <span class=" parameter_thers' + y.parameterID + '" >' + " - " + thers + '</span></span>'
                            html += '<div class="dropdown">'
                            html += '<i class="cp-vertical-dots" data-bs-toggle="dropdown" aria-expanded="false" role="button"></i>'
                            html += '<ul class="dropdown-menu">'
                            html += '<li><a class="dropdown-item enable' + y.parameterID + '" data_enable="' + y.parameterID + '" onclick="enable(this)">' + update_enable_disable_text + '</a></li>'
                            html += '<li><a class="dropdown-item" onclick="profile_delete(this)" categoryName="categorydetails' + x.categoryName + '"  data_delete="' + y.parameterID + '">Delete</a></li>'
                            html += '<li><a class="dropdown-item d-flex align-items-center justify-content-between" >Notification <input type="checkbox"  class="form-check notification_checkbox' + y.ParameterName + '" id="notification_checkbox" ' + NotificationData + ' /></a></li>'
                            html += '<li><a class="dropdown-item thersholdValue" onclick="thersholdopen(this)" thersholdoperator="' + y.Thresholdoperators + '" thersholdvalue="' + y.Threshold + '"  data_id="' + y.parameterID + '" >Threshold</a></li>'
                            html += '</ul></div></div></div></div>'

                            $(".profile_list_checkbox" + y.parameterID + "").prop("checked", true)
                            $(".profile_list_checkbox" + y.parameterID + "").attr("disabled", "disabled")
                        })
                        html += '</details>'
                        $("#addProfileData").append(html)
                    })
                }
            })
        }
    })
    async function parameter_list() {
        await $.ajax({
            type: "GET",
            url: RootUrl + profileurls.GetDriftCategoryList ,
            dataType: 'json',
            success: function (result) {
                $("#profileNodataParameter").empty()
                result?.forEach((x, i) => {

                    let html = ""
                    html += '<details class="filterTitle">'
                    html += '<summary   class=" categorysummary text-truncate" title="">'
                    html += '<i  custom-cursor-on-hover" catagoryid="' + x.id + '" class="' + x.catagoryIcon + '"></i> ' + x.name + '</summary>'
                    x?.list?.forEach((y, i) => {
                        parameterListName.push({ "id": y.id, "name": y.name, "catagoryId": x.id, "icon": JSON.parse(y.properties).Nameicon })
                        html += '<div class="d-flex align-items-center justify-content-between ms-3">'
                        html += '<span role="button">'
                        html += '<i class="' + JSON.parse(y.properties).Nameicon + ' me-1"></i>' + y.name + '</span>'
                        html += '<span>'
                        html += '<input class="form-check profile_list_checkbox  profile_list_checkbox' + y.id + '"  catagoryid="' + x.id + '" categoryName="' + x.name + '"  type="checkbox" data_severity="' + y.severity + '"  data_icon="' + JSON.parse(y.properties).Nameicon + '" data_name="' + y.name + '" onchange="check_list()" name="profile_list_checkbox"  value="' + y.id + '"/>'
                        html += '</span>'
                        html += '</div>'
                    })
                    html += '</details>'
                    $("#profileNodataParameter").append(html)
                })
            },
        })
    }
    parameter_list()
    function validateProfileDropDown(value, errorMsg, errorElement) {
        if (!value || value.length === 0) {
            errorElement.text(errorMsg);
            errorElement.addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('');
            errorElement.removeClass('field-validation-error');
            return true;
        }
    }
    async function validateProfileName(value, id, errorElement, url) {
        if (!value) {
            errorElement.text('Enter profile name')
            errorElement.addClass('field-validation-error');
            return false;
        }
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }
        var url = RootUrl + url;
        var data = {};
        data.id = id;
        data.name = value
        const validationResults = [
            await SpecialCharValidate(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithSpace(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsSameNameExist(url, data)
        ];

        const failedValidations = validationResults.filter(result => result !== true);

        if (failedValidations.length > 0) {
            errorElement.text(failedValidations[0]);
            errorElement.addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('');
            errorElement.removeClass('field-validation-error');
            return true;
        }
    }
    async function IsSameNameExist(url, inputValue) {
        return !inputValue.name.trim() ? true : (await GetAsync(url, inputValue, OnError)) ? " Name already exists" : true;
    }
$("#parameterListSearch").on("input", function () {
        var filter = $(this).val().toLowerCase();
        var nodes = document.getElementsByClassName('filterTitle');
        for (let i = 0; i < nodes.length; i++) {
            if (nodes[i].innerText.toLowerCase().includes(filter)) {
                nodes[i].style.display = "block";
            } else {
                nodes[i].style.display = "none";
            }
        }
    })
    $("#profileCreateButton").on("click", function () {
        globalId = ""
        updateId = null
        $("#parameterListSearch,#profileName").val("")
        $('#addProfileSave').text("Save");
        $("#addProfileData").empty()
        $("#profileNameError").text("").removeClass('field-validation-error');
        parameter_list()
    })
    $('#profileName').on('input', driftprofiledebounce(async function () {
        let value = await sanitizeInput($('#profileName').val());
        $("#profileName").val(value);
        await validateProfileName(value, updateId, $("#profileNameError"), profileurls.isnameExits);
    }, 400));

    function checked(id, name) {
        const errorElement = id
        if (typeof name == "string") {
            let checkFind = document.querySelectorAll(name)
            let arr = []
            checkFind?.forEach((obj, idx) => {
                if (obj.checked) {
                    arr.push(obj.checked == true)
                }
            })
            if (arr.length) {
                errorElement.text('').removeClass('field-validation-error');
                return true;
            } else {
                errorElement.text("Select atleast one list").addClass('field-validation-error');
                return false;
            }
        } else {
            if (name != 0) {
                errorElement.text('').removeClass('field-validation-error');
                return true;
            } else {
                errorElement.text("Select atleast one list").addClass('field-validation-error');
                return false;
            }
        }
    }
    function check_list() {
        checked($("#profileListCheckboxError"), 'input[name="profile_list_checkbox"]')
    }
    let data_param
    $("#profileListCheck").on("click", function () {
        let assign = true
        $("#profileMainCheckboxError").text("").removeClass('field-validation-error');
        let checkfind = document.querySelectorAll('input[name=profile_list_checkbox]')
        var datas = checked($("#profileListCheckboxError"), 'input[name="profile_list_checkbox"]')
        let checkvalue = ""
        let getdatas = document.getElementsByClassName('parameter_value_check')

        for (let i = 0; i < getdatas.length; i++) {
            checkvalue = getdatas[i].getAttribute('title')
        }
        if (datas) {
            if (checkfind.length != 0) {
                checkfind?.forEach((obj, i) => {
                    if (obj.disabled != true) {
                        if (obj.getAttribute('data_name') == checkvalue) {
                            assign = false
                        } else {
                            assign = true
                        }
                        if (obj.checked == true && assign) {
                            if (!obj.disabled) {
                                let getdatascategory = document.getElementsByClassName('categorydetails' + obj.getAttribute('categoryname') + '')
                                var html = ""
                                if (getdatascategory.length == 0) {
                                    html += '<div id="nodata_parameter" name=overall_delete"> <details class="categorydetails categorydetails' + obj.getAttribute('categoryname') + ' " open=""><summary class="categorysummary d-flex align-items-center categorysummary' + obj.getAttribute('categoryname') + ' text-truncate"  data_delete="' + obj.getAttribute("catagoryid") + '" title=""><span class="w-100 d-flex align-items-center justify-content-between"><span><i custom-cursor-on-hover"=""></i>' + obj.getAttribute('categoryname') + '</span><span class="cp-Delete" data_delete="' + obj.getAttribute("catagoryid") + '" categoryName="categorydetails' + obj.getAttribute('categoryname') + '" parentname="' + obj.getAttribute('categoryname') + '" onclick="overalldelete(this)"></span></span></summary>'
                                    html += '<div id="' + obj.value + '" class="profileparamchild enable-disabled' + obj.value + '">'
                                    html += '<div class="d-flex align-items-center justify-content-between ms-4 mb-2">'
                                    html += '<span role="button" paramId="' + obj.value + '" class="text-truncate w-75 parameter_value_check  parameter_value_check' + obj.value + '"  title="' + obj.getAttribute('data_name') + '">'
                                    html += '<i class="' + obj.getAttribute('data_icon') + ' me-1" icon="' + obj.getAttribute('data_icon') + '"></i> ' + obj.getAttribute('data_name') + ''
                                    html += '</span>'
                                    html += '<div class="d-flex gap-2 ">'
                                    html += '<span style="font-size:10px" class="badge text-bg-warning py-1 paramshowhide' + obj.value + '">Thershold <span class=" parameter_thers' + obj.value + '" ></span></span>'
                                    html += '<div class="dropdown">'
                                    html += '<i class="cp-vertical-dots" data-bs-toggle="dropdown" aria-expanded="false" role="button"></i>'
                                    html += '<ul class="dropdown-menu">'
                                    html += '<li><a class="dropdown-item enable' + obj.value + '" data_enable="' + obj.value + '" onclick="enable(this)">Disabled</a></li>'
                                    html += '<li><a class="dropdown-item" onclick="profile_delete(this)" categoryName="categorydetails' + obj.getAttribute('categoryname') + '"  data_delete="' + obj.value + '">Delete</a></li>'
                                    html += '<li><a class="dropdown-item d-flex align-items-center justify-content-between" >Notification <input type="checkbox" class="form-check notification_checkbox' + obj.value + '"  id="notification_checkbox" /></a></li>'
                                    html += '<li><a class="dropdown-item thersholdValue" onclick="thersholdopen(this)" data_id="' + obj.value + '" >Threshold</a></li>'
                                    html += '</ul>'
                                    html += '</div>'
                                    html += '</div>'
                                    html += '</div>'
                                    html += '</div>'
                                    html += '</details>'
                                    $("#addProfileData").append(html)
                                }
                                else {
                                    html += '<div id="' + obj.value + '" class="profileparamchild enable-disabled' + obj.value + '">'
                                    html += '<div class="d-flex align-items-center justify-content-between ms-4 mb-2">'
                                    html += '<span role="button" paramId="' + obj.value + '" class="text-truncate w-75 parameter_value_check parameter_value_check' + obj.value + '" title="' + obj.getAttribute('data_name') + '">'
                                    html += '<i class="' + obj.getAttribute('data_icon') + ' me-1" icon="' + obj.getAttribute('data_icon') + '"></i> ' + obj.getAttribute('data_name') + ''
                                    html += '</span>'
                                    html += '<div class="d-flex gap-2 ">'
                                    html += '<span style="font-size:10px" class="badge text-bg-warning py-1 paramshowhide' + obj.value + '">Thershold <span class=" parameter_thers' + obj.value + '" ></span></span>'
                                    html += '<div class="dropdown">'
                                    html += '<i class="cp-vertical-dots" data-bs-toggle="dropdown" aria-expanded="false" role="button"></i>'
                                    html += '<ul class="dropdown-menu">'
                                    html += '<li><a class="dropdown-item enable' + obj.value + '" data_enable="' + obj.value + '"  onclick="enable(this)">Disabled</a></li>'
                                    html += '<li><a class="dropdown-item" onclick="profile_delete(this)" categoryName="categorydetails' + obj.getAttribute('categoryname') + '"  data_delete="' + obj.value + '">Delete</a></li>'
                                    html += '<li><a class="dropdown-item d-flex align-items-center justify-content-between" >Notification <input type="checkbox" class="form-check notification_checkbox' + obj.value + '"  id="notification_checkbox" /></a></li>'
                                    html += '<li><a class="dropdown-item thersholdValue" onclick="thersholdopen(this)" data_id="' + obj.value + '" >Threshold</a></li>'
                                    html += '</ul>'
                                    html += '</div>'
                                    html += '</div>'
                                    html += '</div>'
                                    html += '</div>'
                                    $(".categorydetails" + obj.getAttribute('categoryname') + "").append(html)
                                }
                                //$(".parameter_thers" + obj.value + "").text("(0)")
                                $(".paramshowhide" + obj.value + "").css({ "display": "none" })
                                if (obj.getAttribute('data_severity') == 2) {
                                    $(".notification_checkbox" + obj.value + "").prop('checked', true)
                                } else {
                                    $(".notification_checkbox" + obj.value + "").prop('checked', false)
                                }
                                $(obj).prop("disabled", true)
                            }
                        }
                    }
                })
            }
        }
    })
    function enable(d) {
        let enabledthers = $(d).attr("data_enable")
        if ($(d).text() == "Disabled") {
            $(".enable" + enabledthers + "").text('Enabled')
            $(".enable-disabled" + enabledthers + "").css({ backgroundColor: "#DCDCDC" })
            $(".parameter_value_check" + enabledthers).attr("parameter_enable_disable", true)
        } else {
            $(".enable" + enabledthers + "").text('Disabled')
            $(".enable-disabled" + enabledthers + "").css({ backgroundColor: "" })
            $(".parameter_value_check" + enabledthers).attr("parameter_enable_disable", false)
        }
    }
    function overalldelete(d) {
        let checkfind = document.querySelectorAll('input[name=profile_list_checkbox]')
        let paradelete = $(d).attr("data_delete")
        let parent = d.getAttribute("categoryname")
        let length = $("." + parent + " .profileparamchild").length

        checkfind?.forEach((obj, i) => {
            parameterListName?.forEach((x, i) => {
                if ((paradelete == x.catagoryId) == true) {
                    $(`#${paradelete}`).remove();
                };
                if (obj.getAttribute("categoryname") == d.getAttribute("parentname")) {
                    $(obj).prop("disabled", false)
                    $(obj).prop("checked", false)
                }
            })
        });
        if (length) {
            $("." + parent).parent().remove()
        }
    }
    function profile_delete(d) {
        let checkfind = document.querySelectorAll('input[name=profile_list_checkbox]')
        let paradelete = $(d).attr("data_delete")
        let parent = d.getAttribute("categoryname")
        let length = $("." + parent + " .profileparamchild").length

        checkfind?.forEach((obj, i) => {
            if ((obj.value == paradelete) == true) {
                $(`#${paradelete}`).remove();
                $(obj).prop("disabled", false)
                $(obj).prop("checked", false)
            };
        });
        if (length <= 1) {
            $("." + parent).parent().remove()
        }
    }
    $("#profileParameterThreshold").on("keypress", function (e) {
        const value = $(this).val()
        if (value.length >= 200) {
            e.preventDefault()
        }
        //var key = e.which;
        //if ((key < 48 || key > 57) && !(key == 8 || key == 9 || key == 13 || key == 37 || key == 39 )) {
        //    return false;
        //}
    })
    $(".profileThersholdClose").on("click", function () {
        $("#profileThersholdModal").modal("hide")
        $("#profileCreateModal").modal("show")
    })
    function thersholdopen(d) {
        let updatethers = $(d).attr("thersholdvalue")
        let updatethersproperties = $(d).attr("thersholdoperator")
        if (updatethers == "" || undefined || null) {
            $("#profileParameterThreshold").val("")
            //$("#parameter_thresholdoperator").val("")
        } else {
            $("#profileParameterThreshold").val(updatethers)
            /*  $("#parameter_thresholdoperator").val(updatethersproperties)*/
        }
        let id = d.getAttribute("data_id")
        $("#profileThersholdModal").modal("show")
        $("#profileThersholdAdd").attr("data_id", id)
    }
$("#profileThersholdAdd").on("click", function (data) {
        let id = data.currentTarget.getAttribute("data_id")
    let parameter_threshold = $("#profileParameterThreshold").val()
        if (parameter_threshold != 0 || 00 || 000) {
            if (parameter_threshold.length >= 20) {
                $(".paramshowhide" + id + "").addClass("text-truncate").css({ "maxWidth": "300px" }).attr("title", parameter_threshold)
            } else {
                $(".paramshowhide" + id + "").removeClass("text-truncate").attr("title", "")
            }
            //let parameter_threshold_operators = $("#parameter_thresholdoperator option:selected").val()
            $(".parameter_thers" + id + "").text(" - " + parameter_threshold)
            $(".parameter_value_check" + id).attr("threshould", parameter_threshold)
            //$(".parameter_value_check" + id).attr("thersholdopeators", parameter_threshold_operators)
            $(".paramshowhide" + id + "").css({ "display": "block" }).removeClass("d-none")
        }
    $("#profileThersholdModal").modal("hide")
    $("#profileCreateModal").modal("show")
    })
    $("#addProfileSave").on("click", async function () {
        let profile = await validateProfileName($('#profileName').val(), updateId, $("#profileNameError"), profileurls.isnameExits);
        let main_check = checked($("#profileMainCheckboxError"), $("#addProfileData").children().length)
        
        if (main_check && profile) {
            let data = []
            let Category_profile = $("#addProfileData").children()
            Category_profile?.each((x, i) => {
                let categoryDetails = {
                    "categoryName": i.querySelector(".categorysummary").textContent.trim(),
                    "categoryId": i.querySelector(".categorysummary").getAttribute("data_delete")
                }
                let parameterCheck = i.querySelectorAll(".parameter_value_check")
              
                let childrenData = []
                parameterCheck?.forEach((y, j) => {
                    console.log(y.getAttribute("parameter_enable_disable"))
                    let thershold = y.getAttribute("threshould") == undefined ? "" : y.getAttribute("threshould")
                    let thersholdopeators = y.getAttribute("thersholdopeators")
                    let thers = {
                        "parameterID": y.getAttribute("paramId"),
                        "ParameterIcon": y.querySelector('i').getAttribute("icon"),
                        "ParameterName": y.textContent.trim(),
                        "Notification": y.nextSibling.querySelector('#notification_checkbox').checked,
                        "Threshold": thershold,
                        "Thresholdoperators": thersholdopeators,
                        "parameter_enable_disable": y.getAttribute("parameter_enable_disable")
                    }
                    console.log(thers)
                    childrenData.push(thers)
                })
                categoryDetails.children = childrenData
                data.push(categoryDetails)
            })

            let profiledata = {
                "Name": $('#profileName').val(),
                "Properties": JSON.stringify(data),
                //"ComponentTypeId": $('#component_type').val(),
                //"ComponentType": $("#component_type option:selected").text(),
                //"ComponentCategoryProperties": strArrayData,
                __RequestVerificationToken: gettoken()
            }
            console.log(profiledata)
            $('#addProfileSave').text() === "Update" ? profiledata["id"] = globalId : null
            await $.ajax({
                type: "POST",
                url: RootUrl + profileurls.CreateOrUpdate,
                dataType: "json",
                data: profiledata,
                success: function (result) {
                    let data = result.data
                    if (result.success) {
                        $('#profileCreateModal').modal('hide');
                        notificationAlert("success", data.message)
                        $('#addProfileSave').text("Save");
                        $("#addProfileData").empty()
                        parameter_list()
                        dataTable.ajax.reload()
                    } else {
                        errorNotification(result)
                    }
                },
            })
        }
    })

    $(document).on('click', '.btnDriftDelete', function () {
        let data = this
        globalDeletedId = $(data).attr("delete_id")
        $("#profileOverallDeletedId").text($(data).attr("deletename")).attr("title", $(data).attr("deletename"))
    })
$("#profileOverallConfirmDeleteButton").on("click", async function () {
        await $.ajax({
            type: "POST",
            url: RootUrl + profileurls.Delete,
            dataType: "json",
            data: {
                id: globalDeletedId,
                __RequestVerificationToken: gettoken()
            },
            success: function (result) {
                let data = result.data
                if (result.success) {
                    $('#profileDeleteModal').modal('hide');
                    $('#addProfileSave').text("Save");
                    $("#addProfileData").empty()
                    notificationAlert("success", data.message)
                    updateProperties = []
                    dataTable.ajax.reload()
                } else {
                    errorNotification(result)
                }
            },
        })
    })

