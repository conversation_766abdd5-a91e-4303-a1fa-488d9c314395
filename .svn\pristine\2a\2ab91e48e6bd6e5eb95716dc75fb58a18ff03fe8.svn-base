﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.ServerLogModel.ServerLogViewModel
@using ContinuityPatrol.Domain.ViewModels.ServerLogModel
@using ContinuityPatrol.Shared.Services.Helper
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-serverlog-configure"></i><span>Server Log Configure</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown"  title="Filter"><i class="cp-filter" ></i></span>
                            <ul class="dropdown-menu filter-dropdown" >
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="name=" id="ServerLogNames">
                                        <label class="form-check-label" for="Name">
                                            ServerLog Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="username=" id="UserNames">
                                        <label class="form-check-label" for="Name">
                                            User Name
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" id="create" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="serverLogTable" class="table table-hover dataTable" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>ServerLog Name</th>
                        <th>IP Address</th>
                        <th >User Name</th>
                        <th >Folder Path</th>
                        <th class="Action-th ">Action</th>
                    </tr>
                </thead>
                <tbody>                   
                </tbody>
            </table>
        </div>
    </div>
</div>
<!-- Modal Create Custom Type-->
<div class="modal fade" id="CreateModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form class="modal-content" asp-controller="ServerLog" id="CreateForm"  asp-action="CreateOrUpdate" method="post">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-server-log"></i><span>Server Log Configuration</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-target="#CreateModal" data-bs-toggle="modal"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="animation-label form-label custom-cursor-default-hover" cursorshover="true">ServerLog Name</label>
                    <div class="input-group cp-name">
                        <span class="input-group-text">
                            <i class="cp-server-name"></i>
                        </span>
                        <input asp-for="Name" type="text" maxlength="100" id="serverLogName" class="form-control" autocomplete="off"  placeholder="Enter Server Log Name" />
                    </div>
                     <input asp-for="Id" type="hidden" id="serverNameId" />
                    <span asp-for="Name" id="ServerLogName-error"></span>
                </div>   
                <div class="form-group">
                    <label class="animation-label form-label custom-cursor-default-hover" cursorshover="true">IPAddress</label>
                    <div class="input-group">
                        <span class="input-group-text cp-ip-address">
                            <i class="cp-ipaddress-name"></i>
                        </span>
                        <input asp-for="IPAddress" type="text" maxlength="100" id="ipaddress" class="form-control" autocomplete="off"  placeholder="Enter IP Address" />
                    </div>
                    @* <input asp-for="Id" type="hidden" id="serverNameId" /> *@
                    <span asp-for="IPAddress" id="IPAddress-error"></span>
                </div>               
                <div class="form-group">
                    <label class="animation-label form-label custom-cursor-default-hover"
                           cursorshover="true">User Name</label>
                    <div class="input-group">
                        <span class="input-group-text cp-user">
                            <i class="cp-user-name"></i>
                        </span>
                        <input asp-for="UserName" type="text" maxlength="100" class="form-control" autocomplete="off" id="userName" placeholder="Enter User Name" />
                    </div>
                    <span  asp-for="user" id="UserName-error"></span>
                </div>
                <div class="form-group">
                    <label class="animation-label form-label custom-cursor-default-hover"
                           cursorshover="true">Password</label>
                    <div class="input-group">
                        <span class="input-group-text cp-lock">
                            <i class="cp-password"></i>
                        </span>
                        <input asp-for="Password" type="password" id="passwordID" autocomplete="off" class="form-control" placeholder="Enter Password" />
                    </div>
                    <span asp-for="Lat" id="Password-error"></span>
                </div>
                <div class="form-group">
                    <label class="animation-label form-label custom-cursor-default-hover"
                           cursorshover="true">Folder Path</label>
                    <div class="input-group">
                        <span class="input-group-text cp-report-path">
                            <i class="cp-Path"></i>
                        </span>
                        <input asp-for="FolderPath" type="text" id="pathId" autocomplete="off" class="form-control" placeholder="Enter Folder Path" />
                    </div>
                    <span asp-for="Lng" id="Path-error"></span>
                </div>              
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary ModalFooter-Note-Text"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" id="cancelFunction" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-sm btn-primary" id="SaveFunction">Save</button>
                </div>
            </div>
        </form>
    </div>
</div>
<div id="configurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<div id="configurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Configuration/ServerLog/ServerLog.js"></script>