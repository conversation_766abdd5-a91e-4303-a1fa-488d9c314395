﻿using ContinuityPatrol.Application.Features.SiteLocation.Commands.Create;
using ContinuityPatrol.Application.Features.SiteLocation.Commands.Update;
using ContinuityPatrol.Application.Features.SiteLocation.Events.PaginatedView;
using ContinuityPatrol.Application.Features.SiteLocation.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SiteLocationModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class SiteLocationControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<ILogger<SiteLocationController>> _mockLogger = new();
        private SiteLocationController _controller;

        public SiteLocationControllerShould()
        {
            _controller = new SiteLocationController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_ReturnsViewResult_AndPublishesEvent()
        {
            // Arrange
            _mockPublisher.Setup(p => p.Publish(It.IsAny<SiteLocationPaginatedEvent>(), default))
                         .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Null(viewResult.Model); // List() returns View() without model
            _mockPublisher.Verify(p => p.Publish(It.IsAny<SiteLocationPaginatedEvent>(), default), Times.Once);
        }

        [Fact]
        public async Task GetPaginated_ReturnsJsonResult_WhenSuccessful()
        {
            // Arrange
            var query = new GetSiteLocationPaginatedListQuery();
            var paginatedResult = new PaginatedResult<SiteLocationListVm>();
            _mockDataProvider.Setup(p => p.SiteLocation.GetPaginatedSiteLocations(query))
                            .ReturnsAsync(paginatedResult);

            // Act
            var result = await _controller.GetPaginated(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task GetPaginated_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var query = new GetSiteLocationPaginatedListQuery();
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(p => p.SiteLocation.GetPaginatedSiteLocations(query))
                            .ThrowsAsync(exception);

            // Act
            var result = await _controller.GetPaginated(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task CreateOrUpdate_CreatesSiteLocation_WhenIdIsEmpty()
        {
            // Arrange
            var siteLocationViewModel = new SiteLocationViewModel { City = "Test City" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty id for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateSiteLocationCommand();
            var response = new BaseResponse { Message = "Created" };

            _mockMapper.Setup(m => m.Map<CreateSiteLocationCommand>(siteLocationViewModel)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SiteLocation.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(siteLocationViewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
            Assert.True(siteLocationViewModel.IsDelete); // Should be set to true
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesSiteLocation_WhenIdIsNotEmpty()
        {
            // Arrange
            var siteLocationViewModel = new SiteLocationViewModel { City = "Test City" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "123"); // Non-empty id for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateSiteLocationCommand();
            var response = new BaseResponse { Message = "Updated" };

            _mockMapper.Setup(m => m.Map<UpdateSiteLocationCommand>(siteLocationViewModel)).Returns(updateCommand);
            _mockDataProvider.Setup(p => p.SiteLocation.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(siteLocationViewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
            Assert.True(siteLocationViewModel.IsDelete); // Should be set to true
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var siteLocationViewModel = new SiteLocationViewModel { City = "Test City" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(p => p.SiteLocation.CreateAsync(It.IsAny<CreateSiteLocationCommand>()))
                            .ThrowsAsync(exception);

            // Act
            var result = await _controller.CreateOrUpdate(siteLocationViewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task Delete_ReturnsJsonResult_WhenSuccessful()
        {
            // Arrange
            var id = "1";
            var response = new BaseResponse { Message = "Deleted" };
            _mockDataProvider.Setup(p => p.SiteLocation.DeleteAsync(id)).ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task Delete_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var id = "1";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(p => p.SiteLocation.DeleteAsync(id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task IsSiteLocationExist_ReturnsJsonResult_WhenExists()
        {
            // Arrange
            var name = "Location1";
            var id = "1";
            _mockDataProvider.Setup(p => p.SiteLocation.IsSiteLocationNameExist(name, id)).ReturnsAsync(true);

            // Act
            var result = await _controller.IsSiteLocationExist(name, id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":true", json);
        }

        [Fact]
        public async Task IsSiteLocationExist_ReturnsJsonResult_WhenNotExists()
        {
            // Arrange
            var name = "Location1";
            var id = "1";
            _mockDataProvider.Setup(p => p.SiteLocation.IsSiteLocationNameExist(name, id)).ReturnsAsync(false);

            // Act
            var result = await _controller.IsSiteLocationExist(name, id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":false", json);
        }

        [Fact]
        public async Task IsSiteLocationExist_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var name = "Location1";
            var id = "1";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(p => p.SiteLocation.IsSiteLocationNameExist(name, id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.IsSiteLocationExist(name, id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

    }
}
