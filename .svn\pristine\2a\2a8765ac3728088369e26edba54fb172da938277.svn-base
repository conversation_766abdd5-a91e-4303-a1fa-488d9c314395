using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.AdPasswordExpire.Events.Update;

public class AdPasswordExpireUpdatedEventHandler : INotificationHandler<AdPasswordExpireUpdatedEvent>
{
    private readonly ILogger<AdPasswordExpireUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public AdPasswordExpireUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<AdPasswordExpireUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(AdPasswordExpireUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} ADPasswordExpire",
            Entity = "ADPasswordExpire",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"ADPasswordExpire '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"ADPasswordExpire '{updatedEvent.Name}' updated successfully.");
    }
}
