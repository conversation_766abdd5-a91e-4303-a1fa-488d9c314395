using ContinuityPatrol.Application.Features.BiaRules.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BiaRules.Queries;

public class GetBiaRulesDetailTests : IClassFixture<BiaRulesFixture>
{
    private readonly BiaRulesFixture _biaRulesFixture;
    private readonly Mock<IBiaRulesRepository> _mockBiaRulesRepository;
    private readonly GetBiaImpactDetailsQueryHandler _handler;

    public GetBiaRulesDetailTests(BiaRulesFixture biaRulesFixture)
    {
        _biaRulesFixture = biaRulesFixture;
        _mockBiaRulesRepository = BiaRulesRepositoryMocks.CreateBiaRulesRepository(_biaRulesFixture.BiaRules);

        _handler = new GetBiaImpactDetailsQueryHandler(
            _biaRulesFixture.Mapper,
            _mockBiaRulesRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnBiaRulesDetail_When_ValidId()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var query = new GetBiaRulesDetailQuery { Id = existingBiaRule.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<BiaRulesDetailVm>();
        result.Id.ShouldBe(existingBiaRule.ReferenceId);
        result.Description.ShouldBe(existingBiaRule.Description);
        result.Type.ShouldBe(existingBiaRule.Type);
        result.EntityId.ShouldBe(existingBiaRule.EntityId);
        result.Properties.ShouldBe(existingBiaRule.Properties);
        result.EffectiveDateFrom.ShouldBe(existingBiaRule.EffectiveDateFrom);
        result.EffectiveDateTo.ShouldBe(existingBiaRule.EffectiveDateTo);
        result.IsEffective.ShouldBe(existingBiaRule.IsEffective);
        result.RuleCode.ShouldBe(existingBiaRule.RuleCode);

        _mockBiaRulesRepository.Verify(x => x.GetByReferenceIdAsync(existingBiaRule.ReferenceId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnRTORuleDetail_When_ValidRTORuleId()
    {
        // Arrange
        var rtoRule = _biaRulesFixture.BiaRules.First(x => x.Type == "RTO");
        var query = new GetBiaRulesDetailQuery { Id = rtoRule.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Type.ShouldBe("RTO");
        result.Id.ShouldBe(rtoRule.ReferenceId);
        result.Description.ShouldBe(rtoRule.Description);
        result.EntityId.ShouldBe(rtoRule.EntityId);
        result.Properties.ShouldBe(rtoRule.Properties);
        result.RuleCode.ShouldBe(rtoRule.RuleCode);
    }

    [Fact]
    public async Task Handle_ReturnRPORuleDetail_When_ValidRPORuleId()
    {
        // Arrange
        // Add RPO rule to the fixture
        var rpoRule = new Domain.Entities.BiaRules
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "RPO Rule Detail Test",
            Type = "RPO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"threshold\":\"1\",\"unit\":\"hours\",\"backup_frequency\":\"hourly\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-10).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(20).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RPO_DETAIL_TEST",
            IsActive = true
        };
        _biaRulesFixture.BiaRules.Add(rpoRule);

        var query = new GetBiaRulesDetailQuery { Id = rpoRule.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Type.ShouldBe("RPO");
        result.Id.ShouldBe(rpoRule.ReferenceId);
        result.Description.ShouldBe("RPO Rule Detail Test");
        result.Properties.ShouldContain("backup_frequency");
        result.RuleCode.ShouldBe("BIA_RPO_DETAIL_TEST");
    }

    [Fact]
    public async Task Handle_ReturnEffectiveRuleDetail_When_ValidEffectiveRuleId()
    {
        // Arrange
        var effectiveRule = _biaRulesFixture.BiaRules.First(x => x.IsEffective);
        var query = new GetBiaRulesDetailQuery { Id = effectiveRule.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsEffective.ShouldBeTrue();
        result.Id.ShouldBe(effectiveRule.ReferenceId);
        result.Description.ShouldBe(effectiveRule.Description);
        result.Type.ShouldBe(effectiveRule.Type);
    }

    [Fact]
    public async Task Handle_ReturnInactiveRuleDetail_When_ValidInactiveRuleId()
    {
        // Arrange
        // Add inactive rule to the fixture
        var inactiveRule = new Domain.Entities.BiaRules
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "Inactive Rule Detail Test",
            Type = "RTO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"threshold\":\"8\",\"unit\":\"hours\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-5).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(5).ToString("yyyy-MM-dd"),
            IsEffective = false,
            RuleCode = "BIA_RTO_INACTIVE_DETAIL",
            IsActive = true
        };
        _biaRulesFixture.BiaRules.Add(inactiveRule);

        var query = new GetBiaRulesDetailQuery { Id = inactiveRule.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsEffective.ShouldBeFalse();
        result.Id.ShouldBe(inactiveRule.ReferenceId);
        result.Description.ShouldBe("Inactive Rule Detail Test");
        result.RuleCode.ShouldBe("BIA_RTO_INACTIVE_DETAIL");
    }

    [Fact]
    public async Task Handle_ReturnRuleWithComplexProperties_When_ValidComplexRuleId()
    {
        // Arrange
        var complexProperties = "{\"threshold\":\"2\",\"unit\":\"hours\",\"escalation\":{\"level1\":\"1h\",\"level2\":\"2h\"},\"notifications\":[\"email\",\"sms\"],\"business_impact\":\"critical\"}";
        var complexRule = new Domain.Entities.BiaRules
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "Complex Rule Detail Test",
            Type = "RTO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = complexProperties,
            EffectiveDateFrom = DateTime.Now.AddDays(-7).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(14).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RTO_COMPLEX_DETAIL",
            IsActive = true
        };
        _biaRulesFixture.BiaRules.Add(complexRule);

        var query = new GetBiaRulesDetailQuery { Id = complexRule.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Properties.ShouldBe(complexProperties);
        result.Properties.ShouldContain("escalation");
        result.Properties.ShouldContain("notifications");
        result.Properties.ShouldContain("business_impact");
        result.RuleCode.ShouldBe("BIA_RTO_COMPLEX_DETAIL");
    }

    [Fact]
    public async Task Handle_ReturnRuleWithDateRange_When_ValidRuleWithDatesId()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var query = new GetBiaRulesDetailQuery { Id = existingBiaRule.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.EffectiveDateFrom.ShouldNotBeNullOrEmpty();
        result.EffectiveDateTo.ShouldNotBeNullOrEmpty();
        
        // Verify date format
        DateTime.TryParse(result.EffectiveDateFrom, out _).ShouldBeTrue();
        DateTime.TryParse(result.EffectiveDateTo, out _).ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BiaRuleNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var query = new GetBiaRulesDetailQuery { Id = nonExistentId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));

        _mockBiaRulesRepository.Verify(x => x.GetByReferenceIdAsync(nonExistentId), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BiaRuleIsNull()
    {
        // Arrange
        var nullId = Guid.NewGuid().ToString();
        _mockBiaRulesRepository.Setup(x => x.GetByReferenceIdAsync(nullId))
            .ReturnsAsync((Domain.Entities.BiaRules)null!);

        var query = new GetBiaRulesDetailQuery { Id = nullId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));

        _mockBiaRulesRepository.Verify(x => x.GetByReferenceIdAsync(nullId), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BiaRuleIsInactive()
    {
        // Arrange
        // Add inactive rule to the fixture
        var inactiveRule = new Domain.Entities.BiaRules
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "Inactive Rule",
            Type = "RTO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"threshold\":\"4\",\"unit\":\"hours\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-5).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(5).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RTO_INACTIVE",
            IsActive = false // Inactive rule
        };
        _biaRulesFixture.BiaRules.Add(inactiveRule);

        var query = new GetBiaRulesDetailQuery { Id = inactiveRule.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_MapCorrectly_When_ValidBiaRule()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var query = new GetBiaRulesDetailQuery { Id = existingBiaRule.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(existingBiaRule.ReferenceId); // Id is mapped from ReferenceId
        result.Description.ShouldBe(existingBiaRule.Description);
        result.Type.ShouldBe(existingBiaRule.Type);
        result.EntityId.ShouldBe(existingBiaRule.EntityId);
        result.Properties.ShouldBe(existingBiaRule.Properties);
        result.EffectiveDateFrom.ShouldBe(existingBiaRule.EffectiveDateFrom);
        result.EffectiveDateTo.ShouldBe(existingBiaRule.EffectiveDateTo);
        result.IsEffective.ShouldBe(existingBiaRule.IsEffective);
        result.RuleCode.ShouldBe(existingBiaRule.RuleCode);
    }


    [Fact]
    public async Task Handle_CallRepositoryOnce_When_QueryExecuted()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var query = new GetBiaRulesDetailQuery { Id = existingBiaRule.ReferenceId };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBiaRulesRepository.Verify(x => x.GetByReferenceIdAsync(existingBiaRule.ReferenceId), Times.Once);
        _mockBiaRulesRepository.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_ReturnCorrectDetailType_When_ValidQuery()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var query = new GetBiaRulesDetailQuery { Id = existingBiaRule.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<BiaRulesDetailVm>();
        result.GetType().Name.ShouldBe("BiaRulesDetailVm");
    }
}
