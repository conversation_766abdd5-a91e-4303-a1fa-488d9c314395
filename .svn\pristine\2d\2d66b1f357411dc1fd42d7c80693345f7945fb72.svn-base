﻿let url = "Admin/ActionBuilder/WorkflowCategoryList"
let actionurl = "Admin/WorkflowAction/WorkflowActionList"
let posturl = "Admin/ActionBuilder/CreateOrUpdate"
let compareJsonUrl = "Admin/WorkflowAction/WorkflowActionCompareJson"
let lockEncryptUrl = "Admin/WorkflowAction/CryptoEncryptPassword"
let lockDecryptUrl = "Admin/WorkflowAction/CryptoDecryptPassword" 
let listActionUrl = "Admin/WorkflowAction/workflowActionDataList"
let createActionUrl = "Admin/WorkflowAction/ActionCreateOrUpdate"
let updateActionUrl = "Admin/WorkflowAction/workflowActionDataList"
let icon = ""
let noDataImage = "<figure class='figure'><img src='../../img/isomatric/no_data_found.svg' class='Card_NoData_Img' style='width: 300px;margin-top:120px'><figcaption class='figure-caption text-center text-danger'>No Matching Records found</figcaption></figure >"
let noDataImage1 = "<figure class='figure'><img src='../../img/isomatric/no_data_found.svg' class='Card_NoData_Img' style='width: 150px;margin-top:100px'><figcaption class='figure-caption text-center text-danger'>No Matching Records found</figcaption></figure > "

let propertyList = ""
let jsonResult = ""

$("#categoryName").empty()
$(".form-select").select2({
    tags: true,
});

WorkflowCategoryList();


$(".search-inp-category").on("keyup", function () {

    var filter = $(this).val();
    let categoryFlagStatus = true
    $("#workflowList details").each(function () {

        var $i = 0;
        $(this).find(".searchData").each(function () {
            var splitText = $(this).text().split(" ")
            if (splitText[0].search(new RegExp(filter, "i")) >= 0) {
                $i++;
            }
        });

        if ($i > 0) {
            $(this).closest("#workflowList details").show();
            categoryFlagStatus = false
        } else {
            $(this).closest("#workflowList details").hide();
        }

    });
    if (categoryFlagStatus) {
        $("#workflowListNoData").show()
        $("#workflowListNoData").css({ 'text-align': 'center' }).html(noDataImage1);
    }
    else {
        $("#workflowListNoData").hide()
        $("#workflowListNoData").html("")
    }
})

$("#collapseExample div.Category_Icon i").on("click", function () {

    $("#imageSelected").attr("class", $(this).attr("class"))
    $("#CreateCategory").attr("image", $(this).attr("class"))
})
$("#collapseIcon div.Category_Icon i").on("click", function () {

    $("#imageSubSelected").attr("class", $(this).attr("class"))
    $("#TitleFooterCategory").attr("image", $(this).attr("class"))
})


document.getElementById("search-inp").addEventListener("search", function (event) {
    $(".actionType").removeAttr("style")
    $("#actionTypeListImage").hide()
});

document.getElementById("search-inpcategory").addEventListener("search", function (event) {
    $('#workflowList').find("details").removeAttr("style")
    $("#workflowListNoData").hide()
});


$(".search-inp-type").on("keyup", function () {

    var filter = $(this).val();
    let selectionType = $("#selectionType option:selected").val()
    let typeStatus = true
    $(".actionType").each(function () {
        var $i = 0;
        let searchType = $(this).attr("type")
        if (selectionType == "all") {
            $(this).find(".card-body").each(function () {
                var splitText = $(this).text().split(" ")
                if (splitText[0].search(new RegExp(filter, "i")) >= 0) {
                    $i++;
                }
            });
            if ($i > 0) {
                $(this).closest(".actionType").show();
                typeStatus = false
            } else {
                $(this).closest(".actionType").hide();
            }
        }
        else {
            if (searchType == selectionType) {
                $(this).find(".card-body").each(function () {
                    var splitText = $(this).text().split(" ")
                    if (splitText[0].search(new RegExp(filter, "i")) >= 0) {
                        $i++;
                    }
                });
                if ($i > 0) {
                    $(this).closest(".actionType").show();
                    typeStatus = false
                } else {
                    $(this).closest(".actionType").hide();
                }
            }
        }
    });
    if (typeStatus) {
        $("#actionTypeListImage").show()
        $("#actionTypeListImage").css({ 'text-align': 'center' }).html(noDataImage);
    }
    else {
        $("#actionTypeListImage").hide()
        $("#actionTypeListImage").html("")
    }
})


$('.select2-icon').select2({
    templateSelection: formatText,
    templateResult: formatText
});

document.addEventListener('DOMContentLoaded', function () {
    var element = document.getElementById('AddCategory_Popover');
    if (element) {
        var popover = new bootstrap.Popover(element, {
            container: 'body',
            title: "Add Category",
            html: true,
            content: function () {
                return document.getElementById('popover-content').innerHTML;
            },
        });
    }
});


$("#CancelCategory").on("click", function () {

    $(".dropdown-menu").removeClass("show");
})

$('#collapseExampleImage').on("click", function () {

    $("#multiCollapseExample1").hide()
    $("#collapseExample").show()

})
$('#multiCollapseExample1Color').on("click", function () {

    $("#collapseExample").hide()
    $("#multiCollapseExample1").show()

    if ($("#editColorTable").children().length != 0) {
        $("#colorTable").append($("#editColorTable").children())
    }

})
$('#collapseIconImage').on("click", function () {

    $("#multiCollapseIcon").hide()
    $("#collapseIcon").show()

})
$('#multiCollapseIconColor').on("click", function () {

    $("#collapseIcon").hide()
    $("#multiCollapseIcon").show()

})
$("span.dynamicColor").on("click", function () {

    $("#colorSelected").attr("color", $(this).css("background-color").trim())

})

$("#collapse").on("click", function () {

    var icondata = $(this).find("i").attr("class")
    if (icondata == "cp-circle-downarrow fs-5 text-primary" || icondata == "fs-5 text-primary cp-circle-downarrow") {
        $(".cp-circle-downarrow").addClass("cp-circle-rightarrow fs-5 text-primary")
        $(".cp-circle-downarrow").removeClass("cp-circle-downarrow")
        $(".cp-circle-rightarrow").attr("title", "Collapse")
        $("#workflowList details").prop("open", false)
    }
    else {
        $(".cp-circle-rightarrow").addClass("cp-circle-downarrow fs-5 text-primary")
        $(".cp-circle-rightarrow").removeClass("cp-circle-rightarrow")
        $(".cp-circle-downarrow").attr("title", "Expand")
        $("#workflowList details").prop("open", true)
    }
})


$("details>summary").on("click", function (e) {

    e.preventDefault()
    if ($(this).parent().attr('open')) {
        if ($(this).attr("parentid")) {
            $(this).parent().removeAttr('open');
        }
        else {
            $(this).parent().removeAttr('open');
        }
    } else {
        if ($(this).attr("parentid")) {
            if ($(this).parent().parent("details").children("details").length >= 1) {
                $(this).parent().parent("details").children("details").removeAttr('open');
                $(this).parent().parent("details").children("details").children("summary").removeClass("text-primary");
                $(this).parent().attr('open', '');
                $(this).parent().children("summary").addClass("text-primary");
            }

        } else {
            $("details>summary").parent().removeAttr('open');
            $(this).parent().attr('open', '');
            $("details>summary").parent().children("summary").removeClass("text-primary");
            $(this).parent().children("summary").addClass("text-primary");

        }
    }
})


$("#CreateCategory").on("click", async function (e) {

    e.preventDefault();
    $("#Name-error").empty()
    var categoryName = $("#categoryName").val()

    if (categoryName == "") {
        $("#Name-error").text('Enter category name')
            .addClass('field-validation-error')
        return false;
    }
    var isName = await validateName(categoryName, $("#Name-error"));
    var existFlagData = exitcategoryData(categoryName, "CreateCategory")

    if (existFlagData) {
        //$('#alertClass').removeClass();
        //$('#alertClass').addClass("info-toast")
        //$('#message').text(categoryName +" is already exist")
        //$('#mytoastrdata').toast({ delay: 3000 });
        //$('#mytoastrdata').toast('show');

        $("#Name-error").text("Name already exists").addClass('field-validation-error')
        return false;
    }
    var image = $(this).attr("image")
    var color = $("#colorSelected").attr("color")
    //if (color) {
    //    $('#alertClass').removeClass();
    //    $('#alertClass').addClass("info-toast")
    //    $('#message').text("please select any one color")
    //    $('#mytoastrdata').toast({ delay: 3000 });
    //    $('#mytoastrdata').toast('show');
    //    return false;
    //}

    if (image) {
        var imageSelected = image
    }
    else {
        var imageSelected = "cp-images"
    }
    if (color) {
        var colorSelected = color
    }
    else {
        var colorSelected = 'rgb(255,255,255)'
    }
    var propertyData = { 'id': 0, 'nodeId': Uuidv4(), 'parentId': '', 'title': categoryName, 'subtitle': '', 'expanded': true, 'noDragging': false, 'icon': imageSelected, color: colorSelected, 'children': [], 'properties': [] }
    var formData = {
        Name: categoryName,
        __RequestVerificationToken: gettoken(),
        Properties: JSON.stringify(propertyData),
        Version: "1.0.0"
    };
    if (isName) {
        $.ajax({
            type: "POST",
            url: RootUrl + posturl,
            data: formData,
            dataType: "json",
            traditional: true,
            success: function (data) {

                $('#alertClass').removeClass();
                $('#alertClass').addClass("success-toast")
                $('#message').text(data.message)
                $('#mytoastrdata').toast({ delay: 3000 });
                $('#mytoastrdata').toast('show');
                setTimeout(function () {
                    window.location.reload()
                }, 1000)
                $("#Name-error").text('').removeClass('field-validation-error')
            },
            error: function () {


            }

        })

        $(".dropdown-menu").removeClass("show");

    }

});

$(".categoryplus").on("click", function (e) {

    $("#categoryName").val("")
    $("#collapseExample").hide()
    $("#Name-error").text("").removeClass('field-validation-error')
})

$("#actionFooterCategory").on("click", function (e) {

    e.preventDefault();
    $("#actionNameCategory-error").empty()
    var actionNameInput = $("#actionNameInput").val()
    if (actionNameInput == "") {
        $("#actionNameCategory-error").text('Enter category name')
            .addClass('field-validation-error')
        return false;
    }
})


//CATEGORY UPDATE DATA
$(".EditCategory,.EditSubCategory,.EditchidSubCategory,.AddSubCategory,.AddchidSubCategory").on("click", function () {

    var id = $(this).attr("id")
    var nodeId = $(this).attr("nodeId")
    var parentId = $(this).attr("parentId")
    var parentName = $(this).attr("parentName")
    var className = $(this).attr("class")
    var name = $(this).attr("name")
    $("#namecategory-error").text("").removeClass('field-validation-error')
    $("#CategoryNameInput").attr('placeholder', 'Enter Sub Category Name')
    var title = $(this).attr("title")
    var type = $(this).attr("type")
    if (className == "EditCategory") {
        $("#CategoryNameInput").attr('placeholder', 'Enter Category Name')
        var color = $(this).attr("color")
        $("#editColorTable").append($("#colorTable").children())
        $("#multiCollapseIconColor").show();

    } else {
        $("#multiCollapseIconColor").hide();
    }
    if (className == "EditCategory" || className == "EditSubCategory" || className == "EditchidSubCategory") {
        $("#AddCategory").modal("show")
        $("#TitleHeaderCategory").text(title)
        //$("#TitleBodyCategory").text(title)
        $("#CategoryNameInput").val(name)
        $("#CategoryNameInput").attr("workflowid", id)
        $("#CategoryNameInput").attr("nodeId", nodeId)
        $("#imageSubSelected").removeClass()
        $("#imageSubSelected").addClass("imageSubSelected " + $(this).attr("icon"))


        $("#CategoryNameInput").attr("parentId", parentId)
        $("#CategoryNameInput").attr("parentName", parentName)
        $("#CategoryNameInput").attr("classNameData", className)
        $("#TitleFooterCategory").text(type)
    }

    else {
        $("#CategoryNameInput").val("")
        $("#AddCategory").modal("show")
        $("#TitleHeaderCategory").text(title)
        // $("#TitleBodyCategory").text(title)
        $("#CategoryNameInput").attr("workflowid", id)
        $("#CategoryNameInput").attr("nodeId", nodeId)
        $("#CategoryNameInput").attr("parentId", parentId)
        $("#CategoryNameInput").attr("classNameData", className)
        $("#CategoryNameInput").attr("nameData", name)
        $("#TitleFooterCategory").text(type)
    }
})
//ADD CATEGORY AND SUB CATEGORY

$(".DeleteSubCategory,.DeleteSubChildCategory").on("click", function () {
    let confirmButton = $("#confirmButton")
    confirmButton.attr("dataId", $(this).attr("id")).attr("dataNodeId", $(this).attr("nodeId")).attr("dataParentId", $(this).attr("parentId")).attr("dataparentName", $(this).attr("parentName")).attr("dataClass", $(this).attr("class")).attr("workflowcategoryname", $(this).attr("data-workflowcategory-name"))
    $("#DeleteModalAction").modal("show")

})


$("#confirmButton").on("click", function () {

    let id = $(this).attr("dataId")
    let nodeId = $(this).attr("dataNodeId")
    let parentId = $(this).attr("dataParentId")
    let parentName = $(this).attr("dataparentName")
    let className = $(this).attr("dataClass")
    let namedata = $(this).attr("workflowcategoryname")
    let propertyData = WorkflowPropertyDeleteList(id, nodeId, parentId, parentName, className)
    let formData = {
        Id: id,
        Name: parentName,
        __RequestVerificationToken: gettoken(),
        Properties: JSON.stringify(propertyData),
        Version: "1.0.0"
    };

    $.ajax({
        type: "POST",
        url: RootUrl + posturl,
        data: formData,
        dataType: "json",
        traditional: true,
        success: function (data) {
            $('#alertClass').removeClass();
            $('#alertClass').addClass("success-toast")
            // $('#message').text("Workflow subcategory '" + namedata + "' has been deleted successfully")
            $('#message').text("Workflow Sub-Category has been deleted successfully")
            $('#mytoastrdata').toast({ delay: 3000 });
            $('#mytoastrdata').toast('show');
            $("#DeleteModalAction").modal("hide")
            setTimeout(function () {
                window.location.reload()
            }, 1000)
        }

    })
    $("#AddCategory").modal("hide");
})

$("#cancelButton").on("click", function () {
    $("#DeleteModalAction").modal("hide")
})


let exportJsonResult;
let selectJson = []
$("#exportAction").on("click", function (e) {


    selectJson = []
    let main_Name = e.target.getAttribute("mainparentname")
    let parent_Name = e.target.getAttribute("parentname")
    let Name = e.target.getAttribute("name")
    let nodeId = e.target.getAttribute("nodeid")
    let referenceId = e.target.getAttribute("referenceId")
    let parentId = e.target.getAttribute("parentId")

    if (document.querySelectorAll('.d_select:checked').length == 0) {

        $('#alertClass').removeClass("success-toast")
        $('#alertClass').addClass("info-toast")
        $('#message').text("Select action type")
        $(".iconClass").removeClass("cp-check")
        $(".iconClass").addClass("cp-exclamation")
        $('#mytoastrdata').toast({ delay: 3000 });
        $('#mytoastrdata').toast('show');

        return;
    }



    if ($("#selectAllExport").prop("checked") == true) {
        exportJsonResult = {
            referenceId: referenceId,
            nodeId: nodeId,
            mainName: main_Name,
            parentId: parentId,
            parentName: parent_Name,
            Name: Name,
            ActionResultJson: jsonResult
           
        }
    }
    else {
        let selectedAction = document.querySelectorAll('.d_select:checked')
        selectedAction.forEach((data) => {
            jsonResult.forEach((jsondata) => {
                if (data.id == jsondata.id) {
                    selectJson.push(jsondata)
                }
            })
        })
        exportJsonResult = {
            referenceId: referenceId,
            nodeId: nodeId,
            mainName: main_Name,
            parentId: parentId,
            parentName: parent_Name,
            Name: Name,
            ActionResultJson: selectJson
        }
    }

    var blob = new Blob([JSON.stringify(exportJsonResult, null, 2)], { type: 'application/json' });

    // Create a URL for the Blob
    var url = URL.createObjectURL(blob);

    // Create a temporary anchor element
    var a = document.createElement('a');
    a.href = url;
    a.download = main_Name + "_" + parent_Name + "_" + Name + '.json';

    // Append anchor to the body (required for Firefox)
    document.body.appendChild(a);

    // Trigger the download
    a.click();

    // Remove the anchor element
    document.body.removeChild(a);

    // Revoke the Blob URL
    URL.revokeObjectURL(url);

    $('#alertClass').removeClass();
    $('#alertClass').addClass("success-toast")
    $('#message').text("Workflow Action exported successfully")
    $('#mytoastrdata').toast({ delay: 3000 });
    $('#mytoastrdata').toast('show');


})

$(".exportCategoryAction").on("click", function (e) {

    let main_Name = e.currentTarget.getAttribute("mainparentname")
    let parent_Name = e.currentTarget.getAttribute("parentname")
    let Name = e.currentTarget.getAttribute("name")
    let ParentNodeId = e.currentTarget.getAttribute("parentNodeId")
    let nodeId = e.currentTarget.getAttribute("nodeId")
    let referenceId = e.currentTarget.getAttribute("referenceId")
    let parentId = e.currentTarget.getAttribute("parentId")
    let categoryIcon = e.currentTarget.getAttribute("categoryIcon")
    exportJsonResult = {
        referenceId: referenceId,
        parentNodeId: ParentNodeId,
        nodeId: nodeId,
        mainName: main_Name,
        parentId: parentId,
        parentName: parent_Name,
        Name: Name,
        categoryIcon: categoryIcon,
        ActionResultJson: jsonResult

    }
    var blob = new Blob([JSON.stringify(exportJsonResult, null, 2)], { type: 'application/json' });

    // Create a URL for the Blob
    var url = URL.createObjectURL(blob);

    // Create a temporary anchor element
    var a = document.createElement('a');
    a.href = url;
    a.download = main_Name + "_" + parent_Name + "_" + Name + '.json';

    // Append anchor to the body (required for Firefox)
    document.body.appendChild(a);

    // Trigger the download
    a.click();

    // Remove the anchor element
    document.body.removeChild(a);

    // Revoke the Blob URL
    URL.revokeObjectURL(url);

    $('#alertClass').removeClass();
    $('#alertClass').addClass("success-toast")
    $('#message').text("Workflow Action exported successfully")
    $('#mytoastrdata').toast({ delay: 3000 });
    $('#mytoastrdata').toast('show');

})


$("#selectAllExport").on("click", function () {
    $(this).prop("checked") == true ? $(".d_select").prop("checked", true) : $(".d_select").prop("checked", false)

})
$("#selectAllImport").on("click", function () {
    $(this).prop("checked") == true ? $(".importSelect").prop("checked", true) : $(".importSelect").prop("checked", false)
})


let fileJsonData;
$('#importAction').on('change', function (event) {

    let file = event.target.files[0];
    if (file && file.type === "application/json") {
        let reader = new FileReader();
        reader.onload = function (e) {
            try {
                $("#importAction-error").text("").removeClass('field-validation-error')
                let json = JSON.parse(e.target.result);
                fileJsonData = json
            } catch (error) {
                $('#importAction-error').text("Error parsing json: " + error.message).addClass('field-validation-error')
            }
        };
        reader.onerror = function (e) {
            $('#importAction-error').text("Error reading file: " + e.target.error).addClass('field-validation-error')
        };
        reader.readAsText(file);
    } else {
        $('#importAction-error').text("Select valid JSON file.").addClass('field-validation-error')
    }
});

$(".importActionNode,.importCategoryActionNode").on("click", function (e) {

    $("#loadImportAction").attr("nodeid", e.currentTarget.getAttribute("nodeid"))
    $("#loadImportAction").attr("workflowStatus", e.currentTarget.getAttribute("importName"))
    $("#importAction-error").text("").removeClass('field-validation-error')
    $("#importAction").val("")
})



$("#loadImportAction").on("click", async function (e) {
    $("#save_import").attr("disabled", false)
    $("#save_import").css("cursor", "pointer")
    let workflowStatus = e.target.getAttribute("workflowStatus")
    let input = $("#importAction").val()
    if (workflowStatus == "action") {
        
        if (input == "") {
            $('#importAction-error').text('Choose file').addClass('field-validation-error')
            return;
        }
        let file = $("#importAction")[0].files[0].type;
        if (file !== "application/json") {
            $('#importAction-error').text('Select valid JSON file').addClass('field-validation-error')
            return;
        }
       
        let jsonData = fileJsonData?.nodeId;
        if (!jsonData) {
            $('#importAction-error').text('Select valid JSON file').addClass('field-validation-error')
            return;
        }

        else {
            //if (fileJsonData.nodeId != e.target.getAttribute("nodeid")) {
            //    $('#alertClass').removeClass();
            //    $('#alertClass').addClass("success-toast")
            //    $('#message').text("Select vaild action")
            //    $('#mytoastrdata').toast({ delay: 3000 });
            //    $('#mytoastrdata').toast('show');
            //    return false;
            //}

            $("#actionDataTable").empty()
            if (fileJsonData?.ActionResultJson != "") {
                fileJsonData?.ActionResultJson?.forEach((data) => {
                    $("#actionDataTable").append('<tr><td><input class="form-check importSelect" id="' + data.id + '" onclick="selectImport(this)" type="checkbox" checked></td><td>' + data.type + '</td><td>' + data.actionName + '</td><td>' + data.version + '</td></tr>')
                })
                $("#save_import").attr('nodeid', e.target.getAttribute("nodeid"))
                $("#ImportActionModal").modal("hide")
                $("#importmodal").modal("show")
            }
            else {
                $('#importAction-error').text('Atleast create one action').addClass('field-validation-error')
                return;
            }
        }
    }
    else {

        let id = fileJsonData?.referenceId
        let parentNodeId = fileJsonData?.parentNodeId
        let nodeId = fileJsonData?.nodeId
        let parentId = fileJsonData?.parentId
        let Name = fileJsonData?.Name
        let imageSelected = fileJsonData?.categoryIcon

        if (input == "") {
            $('#importAction-error').text('Choose file').addClass('field-validation-error')
            return;
        }
        let file = $("#importAction")[0].files[0].type;
        if (file!== "application/json") {
            $('#importAction-error').text('Select valid JSON file').addClass('field-validation-error')
            return;
        }

        var existFlagData = exitcategoryData(Name, "AddchidSubCategory", parentNodeId)
            if (existFlagData) {

                $("#importAction-error").text("Name already exists").addClass('field-validation-error')

        }
        else  {
                let propertyData = WorkflowPropertyAddList(id, parentNodeId, parentId, Name, "importCategoryAction", imageSelected, nodeId)

            var formData = {
                Id: id,
                Name: fileJsonData?.mainName,
                __RequestVerificationToken: gettoken(),
                Properties: JSON.stringify(propertyData),
                Version: "1.0.0"
            };

            $.ajax({
                type: "POST",
                url: RootUrl + posturl,
                data: formData,
                dataType: "json",
                traditional: true,
                success: function (data) {

                  
                    ImportWorkflowAction = {
                        "ImportWorkflowActionListCommands": fileJsonData.ActionResultJson
                    }

                    $.ajax({
                        type: "PUT",
                        url: RootUrl + 'Admin/WorkflowAction/ImportWorkflowAction',
                        dataType: "json",
                        /*   contentType: "application/json",*/
                        data: { "importWorkflowActionCommand": ImportWorkflowAction, __RequestVerificationToken: gettoken() },
                        success: function (response) {
                            
                            console.log(response)
                            $("#ImportActionModal").modal("hide")
                            if (response.success) {
                                $('#alertClass').removeClass();
                                $('#alertClass').addClass("success-toast")
                                $('#message').text(response.message.message)
                                $('#mytoastrdata').toast({ delay: 3000 });
                                $('#mytoastrdata').toast('show');
                            }
                            else {
                                $('#alertClass').removeClass("success-toast")
                                $('#alertClass').addClass("info-toast")
                                $('#message').text(response.message)
                                $(".iconClass").removeClass("cp-check")
                                $(".iconClass").addClass("cp-exclamation")
                                $('#mytoastrdata').toast({ delay: 3000 });
                                $('#mytoastrdata').toast('show');
                            }
                            //getActionListView(fileJsonData.nodeId)
                        }

                    })
                }

            })
        }
    }
})
let ImportWorkflowAction;
let selectedImportAction;
$("#save_import").on("click", function (e) {
    
    selectedImportAction = []
    ImportWorkflowAction = ""
    let nodeId = e.target.getAttribute("nodeid")
    if ($("#selectAllImport").prop("checked") == true) {
        if (fileJsonData.nodeId == nodeId) {
            fileJsonData.nodeId = nodeId
        
            ImportWorkflowAction = {
                "ImportWorkflowActionListCommands": fileJsonData.ActionResultJson
            }
        }
        else {
            fileJsonData.nodeId = nodeId
            fileJsonData.ActionResultJson.forEach((jsondataAction) => {
                jsondataAction.id = null
                jsondataAction.nodeId = nodeId
                jsondataAction.actionName = jsondataAction.actionName + "_Copy" + randomNumber(1, 10000)
               
            })
            ImportWorkflowAction = {
                "ImportWorkflowActionListCommands": fileJsonData.ActionResultJson
            }
        }
    }
    else {

        let selectedAction = document.querySelectorAll('.importSelect:checked')
        if (selectedAction.length != 0) {
            if (fileJsonData.nodeId == nodeId) {
                fileJsonData.nodeId = nodeId
                selectedAction.forEach((data) => {
                    fileJsonData.ActionResultJson.forEach((jsondataAction) => {
                        if (data.id == jsondataAction.id) {
                            selectedImportAction.push(jsondataAction)
                        }
                    })
                })
            }
            else {
                fileJsonData.nodeId = nodeId
                selectedAction.forEach((data) => {
                    fileJsonData.ActionResultJson.forEach((jsondataAction) => {
                        if (data.id == jsondataAction.id) {
                            jsondataAction.nodeId = nodeId
                            jsondataAction.id = null
                            jsondataAction.actionName = jsondataAction.actionName + "_Copy" + randomNumber(1, 10000)
                            selectedImportAction.push(jsondataAction)
                        }

                    })
                })
            }

            ImportWorkflowAction = {
                "ImportWorkflowActionListCommands": selectedImportAction
            }
        }
        else {
            $('#alertClass').removeClass();
            $('#alertClass').addClass("info-toast")
            $('#message').text("Select action type")
            $('#mytoastrdata').toast({ delay: 3000 });
            $('#mytoastrdata').toast('show');
            return;
        }

    }
    $("#save_import").attr("disabled", true)
    $("#save_import").css("cursor", "not-allowed")
    $.ajax({
        type: "PUT",
        url: RootUrl + 'Admin/WorkflowAction/ImportWorkflowAction',
        dataType: "json",
        /*   contentType: "application/json",*/
        data: { "importWorkflowActionCommand": ImportWorkflowAction, __RequestVerificationToken: gettoken() },
        success: function (response) {

            console.log(response)
            $("#importmodal").modal("hide")
            if (response.success) {
                $("#save_import").attr("disabled", false)
                $("#save_import").css("cursor", "pointer")
                $('#alertClass').removeClass();
                $('#alertClass').addClass("success-toast")
                $('#message').text(response.message.message)
                $('#mytoastrdata').toast({ delay: 3000 });
                $('#mytoastrdata').toast('show');
            }
            else {
                $("#save_import").attr("disabled", false)
                $("#save_import").css("cursor", "pointer")
                $('#alertClass').removeClass("success-toast")
                $('#alertClass').addClass("info-toast")
                $('#message').text(response.message)
                $(".iconClass").removeClass("cp-check")
                $(".iconClass").addClass("cp-exclamation")
                $('#mytoastrdata').toast({ delay: 3000 });
                $('#mytoastrdata').toast('show');
            }
            getActionListView(fileJsonData.nodeId)
        }

    })
})

$("#TitleFooterCategory").on("click", async function (e) {
    e.preventDefault();

    var id = $("#CategoryNameInput").attr("workflowid")
    var nodeId = $("#CategoryNameInput").attr("nodeId")
    var parentId = $("#CategoryNameInput").attr("parentId")
    var classNameData = $("#CategoryNameInput").attr("classNameData")
    var image = $("#imageSubSelected").attr("class")
    if (image) {
        var imageSelected = image
    }
    else {
        var imageSelected = "cp-images"
    }
    var color = $("#colorSelected").attr("color")

    if (color) {
        var colorSelected = color
    }
    else {
        var colorSelected = 'rgb(255,255,255)'
    }
    var nameValue = $("#CategoryNameInput").val()
    //if (nameValue == "") {
    //    $("#namecategory-error").html("Please enter the category name")
    //    return false;
    //}
    let nameDataText = classNameData === "EditCategory" ? 'Enter category name' : 'Enter sub category name'
    var isName = await validateName(nameValue, $("#namecategory-error"), nameDataText);

    if (isName) {
        if (classNameData == "AddSubCategory" || classNameData == "AddchidSubCategory") {

            var nameData = $("#CategoryNameInput").attr("nameData")

            var existFlagData = exitcategoryData(nameValue, classNameData, nodeId)
            if (existFlagData) {

                //$("#namecategory-error").text(nameValue + " is already exists")
                $("#namecategory-error").text("Name already exists").addClass('field-validation-error')
                return false;

            }
            var propertyData = WorkflowPropertyAddList(id, nodeId, parentId, nameValue, classNameData, imageSelected)

        }
        else if (classNameData == "EditCategory" || classNameData == "EditSubCategory" || classNameData == "EditchidSubCategory") {
            if (classNameData == "EditCategory") {
                var nameData = nameValue
            }
            else {
                var nameData = $("#CategoryNameInput").attr("parentName")
            }
            var propertyData = WorkflowPropertyUpdateList(id, nodeId, parentId, nameValue, classNameData, imageSelected, colorSelected)
        }

        //var propertyData = { 'id': id, 'nodeId': 0, 'parentId': '', 'title': Name, 'subtitle': '', 'expanded': true, 'noDragging': false, 'icon': '', 'children': [], 'properties': [] }

        var formData = {
            Id: id,
            Name: nameData,
            __RequestVerificationToken: gettoken(),
            Properties: JSON.stringify(propertyData),
            Version: "1.0.0"
        };

        $.ajax({
            type: "POST",
            url: RootUrl + posturl,
            data: formData,
            dataType: "json",
            traditional: true,
            success: function (data) {

                $('#alertClass').removeClass();
                $('#alertClass').addClass("success-toast")
                $('#message').text("WorkflowCategory '" + nameValue + "' has been updated successfully")
                $('#mytoastrdata').toast({ delay: 3000 });
                $('#mytoastrdata').toast('show');
                setTimeout(function () {
                    window.location.reload()
                }, 1000)
            }

        })

        $("#AddCategory").modal("hide");
    }
})


$("#selectionType").on("change", function () {

    let actionFlagStatus = true
    var filter = $(this).val();

    $(".actionType").each(function () {
        if (filter == "all") {
            $(this).show();
            actionFlagStatus = false
        }
        else {
            //if (filter == "Common") {
            //    //if ($(this).attr("type") == filter || $(this).attr("type")=="all") {
            //    if ($(this).attr("type") == filter) {
            //        $(this).closest(".actionType").show();
            //        actionFlagStatus = false
            //    }
            //    else {
            //        $(this).closest(".actionType").hide();
            //    }
            //} else {
            if ($(this).attr("type") == filter) {

                $(this).closest(".actionType").show();
                actionFlagStatus = false

            }
            else {
                $(this).closest(".actionType").hide();
            }
            //}
        }
    });
    if (actionFlagStatus) {
        $("#actionTypeListImage").css({ 'text-align': 'center' }).html(noDataImage);
    }
    else {
        $("#actionTypeListImage").html("")
    }
})

$('.deleteCategoryList').on("click", function () {

    var workflowcategoryid = $(this).data('workflowcategoryId');
    let workflowName = $(this).data('workflowcategoryName');
    $('#textDeleteId').val(workflowcategoryid);
    $('#deleteData').text(workflowName);
    $('#commonFormDelete').attr('action', '/Admin/ActionBuilder/Delete')

});


$(".categoryactionlist").on("click", function () {

    $('#selectionType').val('all')
    $(".categoryactionlist").removeClass("text-primary");
    $(".categoryactionlist").children("span").removeClass("text-primary")
    $(".cp-horizontal-dots.float-end").removeClass("text-primary")
    $(this).addClass("text-primary");
    $(this).children("span").addClass("text-primary")
    $(this).next("div").children("i").addClass("text-primary")
    var nodeId = $(this).attr("nodeId")
    icon = $(this).children("i").attr("class")
    var iconData = icon.split("_")
    if (iconData[0] == "Action") {
        icon = $(this).parent().parent().children("summary").children("i").attr("class")
    }

    getActionListView(nodeId)

})

$("#cancelImport").on("click", function () {

    $("#importAction").val("")
})

function getActionListView(nodeId) {

    $.ajax({
        url: RootUrl + actionurl,
        type: 'GET',
        async: true,
        data: { nodeId: nodeId },
        success: function (result) {
            jsonResult=""
            $(".actionTypelist").empty()
            if (result?.length != 0) {
                jsonResult = result

                var ActionResult = ""
                result.forEach(function (jsonresult) {

                    let colorName = JSON.parse(jsonresult?.properties)
                    let lockStatusdnone = jsonresult.isLock == false ? "d-none" : ""
                    let lockStatus = jsonresult.isLock == false ? "cp-lock" : "cp-open-lock"
                    let lockText = jsonresult.isLock == false ? "Unlock" : "Lock"
                    let locktitle = jsonresult.isLock == false ? "Lock" : "Unlock"
                    let lockIcon = jsonresult.isLock == false ? "cp-open-lock" : "cp-lock"
                    ActionResult += "<div class='col actionType mt-0' type=" + jsonresult.type + ">"
                    ActionResult += "<div class='card border mb-2'>"
                    ActionResult += "<div class='card-header pb-0'>"
                    ActionResult += "<div class='dropdown d-flex justify-content-md-between'>"
                    ActionResult += "<i class='" + lockStatus + " lockFixedStatus' role='button' title=" + locktitle + "  aria-expanded='false' onclick='lockUnlockFunc()'></i>"
                    ActionResult += "<span class='d-flex align-items-center gap-2'><input class='form-check d-none d_select' id=" + jsonresult.id + " onclick='selectExport(this)' type='checkbox' checked/><i class='cp-horizontal-dots' title='More' role='button' data-bs-toggle='dropdown' aria-expanded='false'></i>"
                    ActionResult += "<ul class='dropdown-menu'>"
                    ActionResult += "<li class='editActionList'  id=" + jsonresult.id + " nodeId=" + jsonresult.nodeId + " type='" + jsonresult.type + "$" + jsonresult.actionName + "$" + jsonresult.nodeId + "'  lockStatus=" + jsonresult.isLock + "  onclick='editActionListView(this,event)'><a class='dropdown-item'  href='#'><i class='cp-edit me-2'></i>Edit</a></li>"
                    ActionResult += "<li class='deleteActionList " + lockStatusdnone + "' id=" + jsonresult.id + " name='" + jsonresult.actionName + "' nodeId=" + jsonresult.nodeId + " onclick='deleteActionListView(this)' data-bs-toggle='modal' data-bs-target='#DeleteModal'><a class='dropdown-item' href='#'><i class='cp-Delete me-2'></i>Delete</a></li>"
                    ActionResult += "<li data-bs-toggle='modal' data-bs-target='#CompareVersion' id=" + jsonresult.id + " onclick='compareActionListView(this)' nodeId=" + jsonresult.nodeId + "><a class='dropdown-item' href='#'><i class='cp-drill-action-type me-2'></i>Compare Version</a></li>"
                    ActionResult += "<li data-bs-toggle='modal' data-bs-target='#SaveAs' onclick='saveAsActionListView(this)' id=" + jsonresult.id + " type='" + jsonresult.type + "$" + jsonresult.nodeId + "$" + jsonresult.id + "' nodeId=" + jsonresult.nodeId + "><a class='dropdown-item' href='#'><i class='cp-save me-2'></i>Save As</a></li>"
                    ActionResult += "<li data-bs-toggle='modal' data-bs-target='#LockModal' onclick='lockModalActionListView(this)' id='" + jsonresult.id + "' nodeId=" + jsonresult.nodeId + " lockStatus=" + jsonresult.isLock + " ><a class='dropdown-item' href='#'><i class='" + lockIcon + " me-2'></i>" + lockText + "</a></li>"
                    ActionResult += "</ul></span>"
                    ActionResult += "</div>"
                    ActionResult += "</div>"
                    ActionResult += "<div class='card-body text-center pt-0'>"
                    ActionResult += "<div class='Card-Avatar " + colorName.color + "'><i class='" + icon + " fs-1'></i></div>"
                    //ActionResult += "<div class='Card-Avatar'><i class='" + icon + " fs-1'></i></div>"
                    ActionResult += "<span class='mb-1 text-truncate d-grid' title=" + jsonresult.actionName +">" + jsonresult.actionName + "</span>"
                    ActionResult += "<small class='text-secondary'>" + jsonresult.version + "</small>"
                    ActionResult += "</div>"
                    ActionResult += "</div>"
                    ActionResult += "</div>"
                    $("#d_selectall").addClass("d-none")
                    $(".d_select").addClass("d-none")
                    $("#exportAction").addClass("d-none")

                })
                $(".actionTypelist").append(ActionResult)
                $("#importAction").val("")
            }
            $("#selectionType").trigger("change")
        }

    })

}

let length = $(".d_select").length
function selectExport(data) {

    if ($(".d_select").length == $(".d_select:checked").length) {
        $("#selectAllExport").prop("checked", true)

    }
    else {
        $("#selectAllExport").prop("checked", false)
    }

}

function selectImport(data) {

    if ($(".importSelect").length == $(".importSelect:checked").length) {
        $("#selectAllImport").prop("checked", true)

    }
    else {
        $("#selectAllImport").prop("checked", false)
    }

}


function selectExportActionListView(data) {

    $(".d_select").removeClass("d-none")
    $("#d_selectall").removeClass("d-none")
    $("#exportAction").removeClass("d-none")
    $("#exportAction").removeAttr("mainparentname")
    $("#exportAction").removeAttr("parentname")
    $("#exportAction").removeAttr("name")
    $("#exportAction").attr("mainparentname", data.getAttribute("mainparentname"))
    $("#exportAction").attr("parentname", data.getAttribute("parentname"))
    $("#exportAction").attr("name", data.getAttribute("name"))
    $("#exportAction").attr("nodeId", data.getAttribute("nodeId"))
    $("#exportAction").attr("referenceId", data.getAttribute("referenceId"))
    $("#exportAction").attr("parentId", data.getAttribute("parentId"))
}


function importActionListView() {

    $("#ImportWorkflowModal").modal("show")
}



function deleteActionListView(data) {

    $("#deleteDataAction").text(data.getAttribute("name"));
    var WorkflowActionId = data.id
    $('#textDeleteId').val(WorkflowActionId);
    $('#commonFormDelete').attr('action', '/Admin/WorkflowAction/Delete')

}
function compareActionListView(data) {

    var WorkflowActionId = data.id

    $.ajax({
        url: RootUrl + compareJsonUrl,
        type: 'GET',
        data: { nodeId: WorkflowActionId },
        async: true,
        success: function (result) {

            $("#previousJson").empty()
            $("#currentJson").empty()
            var previousData = JSON.parse(result.previous)
            var currentData = JSON.parse(result.current)
            $("#currentJson").val(JSON.parse(currentData.Properties))
            $("#currentJson").text(currentData.Properties)
            previousData.forEach(function (data) {
                if (data.length != 0) {
                    if (data.Version == currentData.Version) {
                        $("#previousJson").val(JSON.parse(data.Properties))

                        $("#previousJson").text(JSON.stringify(data.Properties, undefined, 2))
                    }
                }
            })

        }
    })
}

function jsonResultActionView(id) {

    let jsonResultData = jsonResult.filter((result) => {
        return result.id == id

    })
    //jsonResultData.forEach(function (index, item) {
    //    // Example: Update a property in filtered results
    //    item.isLock = false;
    //});


    return jsonResultData;
}

function formatText(icon) {
    return $('<span><i class=" ' + $(icon.element).data('icon') + '"></i> ' + icon.text + '</span>');
};


function Uuidv4() {
    return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c =>
        (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
    );
}

function WorkflowCategoryList() {

    $.ajax({
        url: RootUrl + url,
        type: 'GET',
        async: true,
        success: function (result) {

            propertyList = result
        }
    })

}

function WorkflowPropertyAddList(id, nodeId, parentId, Name, classNameData, imageSelected,importNodeId) {

    var propertyjsonResultJson = ""
    let fieldNameInput = $("#fieldNameInput").val()
    let description = $("#commendInput").val()
    if (propertyList.length !== 0) {
        propertyList.forEach(function (jsonresult) {
            var jsonResultJson = JSON.parse(jsonresult.properties)
            if (classNameData == "AddSubCategory") {

                if (nodeId == jsonResultJson.nodeId) {
                    jsonResultJson.Id = id
                    //jsonResultJson.title = Name
                    //jsonResultJson.nodeId = nodeId
                    jsonResultJson.children.push({ 'id': id, 'nodeId': Uuidv4(), 'parentId': nodeId, 'title': Name, 'subtitle': '', 'expanded': true, 'noDragging': false, 'icon': imageSelected, 'children': [], 'properties': [] })
                    propertyjsonResultJson = jsonResultJson
                }
            }
            else if (classNameData == "addChildSubComment") {
                jsonResultJson.children.forEach(function (jsonresult) {
                    if (nodeId == jsonresult.nodeId) {
                        //jsonResultJson.id = id
                        //jsonResultJson.title = Name
                        //jsonResultJson.nodeId = nodeId
                        jsonresult.children.push({
                            'id': id, 'nodeId': Uuidv4(), 'parentId': nodeId, 'title': Name, 'subtitle': '', 'expanded': true, 'noDragging': false, 'icon': imageSelected, 'children': [], 'properties': [], "ActionId": '', "field_type": fieldNameInput, ActionType: fieldNameInput, "EventType": fieldNameInput, "field_name": fieldNameInput, "Description": description, "length": 6, "FinalDescription": description, "Value": "", "Active": true, "Properties": {}, "OnError": {}, "ApplyRule": null, "IsComment": null, "Log": false
                        })
                        propertyjsonResultJson = jsonResultJson
                    }
                }
                )
            }

            else if (classNameData == "importCategoryAction") {
                jsonResultJson.children.forEach(function (jsonresult) {
                    if (nodeId == jsonresult.nodeId) {
                        //jsonResultJson.id = id
                        //jsonResultJson.title = Name
                        //jsonResultJson.nodeId = nodeId
                        jsonresult.children.push({ 'id': id, 'nodeId': importNodeId, 'parentId': nodeId, 'title': Name, 'subtitle': '', 'expanded': true, 'noDragging': false, 'icon': imageSelected, 'children': [], 'properties': [] })
                        propertyjsonResultJson = jsonResultJson
                    }
                }
                )
            }
            else {
                jsonResultJson.children.forEach(function (jsonresult) {
                    if (nodeId == jsonresult.nodeId) {
                        //jsonResultJson.id = id
                        //jsonResultJson.title = Name
                        //jsonResultJson.nodeId = nodeId
                        jsonresult.children.push({ 'id': id, 'nodeId': Uuidv4(), 'parentId': nodeId, 'title': Name, 'subtitle': '', 'expanded': true, 'noDragging': false, 'icon': imageSelected, 'children': [], 'properties': [] })
                        propertyjsonResultJson = jsonResultJson
                    }
                }
                )
            }

        })


    }

    return propertyjsonResultJson;

}

function WorkflowPropertyUpdateList(id, nodeId, parentId, Name, classNameData, imageSelected, colorSelected) {

    var propertyjsonResultJson = ""
    let fieldNameInput = $("#fieldNameInput").val()
    let description = $("#commendInput").val()
    if (propertyList.length !== 0) {
        propertyList.forEach(function (jsonresult) {
            var jsonResultJson = JSON.parse(jsonresult.properties)
            if (classNameData == "EditCategory") {

                if (nodeId == jsonResultJson.nodeId) {
                    jsonResultJson.id = id
                    jsonResultJson.title = Name
                    jsonResultJson.nodeId = nodeId
                    jsonResultJson.parentId = parentId
                    jsonResultJson.icon = imageSelected
                    jsonResultJson.color = colorSelected

                    // jsonResultJson.children.push({ 'id': id, 'nodeId': Uuidv4(), 'parentId': nodeId, 'title': Name, 'subtitle': '', 'expanded': true, 'noDragging': false, 'icon': '', 'children': [], 'properties': [] })
                    propertyjsonResultJson = jsonResultJson
                }
            }
            else if (classNameData == "EditchidSubComment") {

                jsonResultJson.children.forEach(function (jsonresult) {
                    if (nodeId == jsonresult.nodeId) {
                        jsonresult.id = id
                        jsonresult.title = Name
                        jsonresult.nodeId = nodeId
                        jsonresult.parentId = parentId
                        jsonresult.icon = imageSelected
                        jsonresult.parentId = parentId
                        jsonresult.ActionId = '',
                            jsonresult.field_type = fieldNameInput,
                            jsonresult.ActionType = fieldNameInput,
                            jsonresult.EventType = fieldNameInput,
                            jsonresult.field_name = fieldNameInput,
                            jsonresult.Description = description,
                            jsonresult.length = 6,
                            jsonresult.FinalDescription = description,
                            jsonresult.Value = "",
                            jsonresult.Active = true,
                            jsonresult.Properties = {},
                            jsonresult.OnError = {},
                            jsonresult.ApplyRule = null,
                            jsonresult.IsComment = null,
                            jsonresult.Log = false
                        //jsonresult.children.push({ 'id': id, 'nodeId': Uuidv4(), 'parentId': nodeId, 'title': Name, 'subtitle': '', 'expanded': true, 'noDragging': false, 'icon': '', 'children': [], 'properties': [] })
                        propertyjsonResultJson = jsonResultJson
                    }

                    jsonresult.children.forEach(function (jsonresultchild) {
                        if (nodeId == jsonresultchild.nodeId) {
                            jsonresultchild.id = id
                            jsonresultchild.title = Name
                            jsonresultchild.nodeId = nodeId
                            jsonresultchild.icon = imageSelected
                            jsonresultchild.parentId = parentId
                            jsonresultchild.ActionId = '',
                                jsonresultchild.field_type = fieldNameInput,
                                jsonresultchild.ActionType = fieldNameInput,
                                jsonresultchild.EventType = fieldNameInput,
                                jsonresultchild.field_name = fieldNameInput,
                                jsonresultchild.Description = description,
                                jsonresultchild.length = 6,
                                jsonresultchild.FinalDescription = description,
                                jsonresultchild.Value = "",
                                jsonresultchild.Active = true,
                                jsonresultchild.Properties = {},
                                jsonresultchild.OnError = {},
                                jsonresultchild.ApplyRule = null,
                                jsonresultchild.IsComment = null,
                                jsonresultchild.Log = false

                            propertyjsonResultJson = jsonResultJson
                        }
                    })



                }
                )
            }
            else if (classNameData == "EditSubCategory" || classNameData == "EditchidSubCategory") {

                jsonResultJson.children.forEach(function (jsonresult) {
                    if (nodeId == jsonresult.nodeId) {
                        jsonresult.id = id
                        jsonresult.title = Name
                        jsonresult.nodeId = nodeId
                        jsonresult.parentId = parentId
                        jsonresult.icon = imageSelected
                        jsonresult.parentId = parentId
                        //jsonresult.children.push({ 'id': id, 'nodeId': Uuidv4(), 'parentId': nodeId, 'title': Name, 'subtitle': '', 'expanded': true, 'noDragging': false, 'icon': '', 'children': [], 'properties': [] })
                        propertyjsonResultJson = jsonResultJson
                    }

                    jsonresult.children.forEach(function (jsonresultchild) {
                        if (nodeId == jsonresultchild.nodeId) {
                            jsonresultchild.id = id
                            jsonresultchild.title = Name
                            jsonresultchild.nodeId = nodeId
                            jsonresultchild.icon = imageSelected
                            jsonresultchild.parentId = parentId
                            propertyjsonResultJson = jsonResultJson
                        }
                    })



                }
                )
            }

        })


    }

    return propertyjsonResultJson;

}

function WorkflowPropertyDeleteList(id, nodeId, parentId, Name, classNameData) {

    var propertyjsonResultJson = ""

    if (propertyList.length !== 0) {
        propertyList.forEach(function (jsonresult) {
            var jsonResultJson = JSON.parse(jsonresult.properties)
            if (classNameData == "DeleteSubCategory" || classNameData == "DeleteSubChildCategory") {

                $.each(jsonResultJson.children, function (index, jsonresult) {
                    if (nodeId == jsonresult.nodeId) {

                        jsonResultJson.children.splice(index, 1);
                        propertyjsonResultJson = jsonResultJson
                        return false;


                    }

                    $.each(jsonresult.children, function (index, jsonresultchild) {
                        if (nodeId == jsonresultchild.nodeId) {
                            jsonresult.children.splice(index, 1);
                            propertyjsonResultJson = jsonResultJson
                            return false;
                        }
                    })



                }
                )

            }


        })


    }

    return propertyjsonResultJson

}

async function categoryInputvalidation(data) {

    $("#Name-error").empty()
    var categoryNameValid = $("#categoryName")
    var categoryName = $("#categoryName").val()
    const errorElement = $("#Name-error");
    var buttonText = $(".finish_btn").text()
    var flagNameList = alreadyExistActionList(data.value)
    var existFlagData = exitcategoryData(categoryName, "CreateCategory")


    if (data.value == "") {
        errorElement.text('Enter category name')
            .addClass('field-validation-error')

    }

    else if (existFlagData) {

        errorElement.text("Name already exists").addClass('field-validation-error')

    }
    else {

        const validationResults = [
            await SpecialCharValidate(data.value),
            await ShouldNotBeginWithUnderScore(data.value),
            await ShouldNotBeginWithSpace(data.value),
            await OnlyNumericsValidate(data.value),
            await ShouldNotBeginWithNumber(data.value),
            await SpaceWithUnderScore(data.value),
            await ShouldNotEndWithUnderScore(data.value),
            await ShouldNotEndWithSpace(data.value),
            await MultiUnderScoreRegex(data.value),
            await SpaceAndUnderScoreRegex(data.value),
            await minMaxlength(data.value),
            await secondChar(data.value),

        ];
        categoryNameValid.val(categoryNameValid.val().replace(/(\s{2,})|[^a-zA-Z0-9_']/g, ' ').replace(/^\s*/, ''));
        return await CommonValidation(errorElement, validationResults);

    }

}
async function subcategoryInputvalidation(data) {

    $("#namecategory-error").empty()
    var subcategoryElement = $("#CategoryNameInput")
    const errorElement = $("#namecategory-error");
    var classNameData = $("#CategoryNameInput").attr("classNameData")
    let nameDataText = classNameData === "EditCategory" ? 'Enter category name' : 'Enter sub category name'
    if (data.value == "") {
        errorElement.text(nameDataText)
            .addClass('field-validation-error')

    }

    else {

        const validationResults = [
            await SpecialCharValidate(data.value),
            await ShouldNotBeginWithUnderScore(data.value),
            await ShouldNotBeginWithSpace(data.value),
            await OnlyNumericsValidate(data.value),
            await ShouldNotBeginWithNumber(data.value),
            await SpaceWithUnderScore(data.value),
            await ShouldNotEndWithUnderScore(data.value),
            await ShouldNotEndWithSpace(data.value),
            await MultiUnderScoreRegex(data.value),
            await SpaceAndUnderScoreRegex(data.value),
            await minMaxlength(data.value),
            await secondChar(data.value),

        ];
        subcategoryElement.val(subcategoryElement.val().replace(/(\s{2,})|[^a-zA-Z0-9_']/g, ' ').replace(/^\s*/, ''));
        return await CommonValidation(errorElement, validationResults);
    }

}

const saveAsInputvalidation = ajaxDebounce(async function(data) {   
    
    $("#actionName-error").empty()
    var saveAsElement = $("#saveAsActionName")
    var saveAsName = $("#saveAsActionName").val()
    const errorElement = $("#actionName-error");
    let url = RootUrl + "Admin/WorkflowAction/WorkflowActionNameExist";
    let data2 = { actionName: data.value };
    if (data.value == "") {
        errorElement.text('Enter action name')
            .addClass('field-validation-error')
    }
    else {
       //saveAsElement.val(saveAsElement.val().replace(/(\s{2,})|[^a-zA-Z0-9_']/g, ' ').replace(/^\s*/, ''));
        const validationResults = await Promise.all([
            await SpecialCharValidate(data.value),
            await ShouldNotBeginWithUnderScore(data.value),
            await ShouldNotBeginWithSpace(data.value),
            await OnlyNumericsValidate(data.value),
            await ShouldNotBeginWithNumber(data.value),
            await SpaceWithUnderScore(data.value),
            await ShouldNotEndWithUnderScore(data.value),
            await ShouldNotEndWithSpace(data.value),
            await MultiUnderScoreRegex(data.value),
            await SpaceAndUnderScoreRegex(data.value),
            await minMaxCompanylength(data.value),
            await secondChar(data.value),
            //await IsFormNameExist(url, data2, OnError)
        ]);
        return await CommonValidation(errorElement, validationResults);
    }

},500)

//async function IsFormNameExist(url, data, errorFunc) {
//    return !data.actionName.trim() ? true : (await GetFormAsync(url, data, errorFunc)) ? "Name already exists" : true;
//}

async function GetFormAsync(url, data, errorFunc) {
    return await $.get(url, data).fail(errorFunc);
}

function exitcategoryData(name, className, parentId) {
    var flagCategoryName = false;
    var propertyListJson = propertyList

    propertyListJson.forEach(function (json) {

        var jsonData = JSON.parse(json.properties)
        if (className == "CreateCategory") {
            if (json.name == name) {
                flagCategoryName = true
            }
        }
        if (jsonData.children.length != 0) {
            jsonData.children.forEach(function (jsonChild) {
                if (className == "AddSubCategory") {
                    if (jsonChild.title == name && parentId == jsonData.parentId) {
                        flagCategoryName = true
                    }
                }
                if (jsonChild.children.length != 0) {
                    jsonChild.children.forEach(function (jsonSubChild) {
                        if (className == "AddchidSubCategory") {
                            if (jsonSubChild.title == name && parentId == jsonSubChild.parentId) {
                                flagCategoryName = true
                            }
                        }
                    })
                }
            })
        }
    })

    return flagCategoryName
}


function ajaxDebounce(func, delay) {
    let debounceTimeout;
    return function (...args) {
        const context = this;
        clearTimeout(debounceTimeout);
        debounceTimeout = setTimeout(() => func.apply(context, args), delay);
    };
}