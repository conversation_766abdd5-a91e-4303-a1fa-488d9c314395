using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SmsConfigurationFixture : IDisposable
{
    public List<SmsConfiguration> SmsConfigurationPaginationList { get; set; }
    public List<SmsConfiguration> SmsConfigurationList { get; set; }
    public SmsConfiguration SmsConfigurationDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public SmsConfigurationFixture()
    {
        var fixture = new Fixture();

        SmsConfigurationList = fixture.Create<List<SmsConfiguration>>();

        SmsConfigurationPaginationList = fixture.CreateMany<SmsConfiguration>(20).ToList();

        SmsConfigurationDto = fixture.Create<SmsConfiguration>();


        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
