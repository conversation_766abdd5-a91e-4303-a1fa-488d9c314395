﻿using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.ReplicationMaster.Queries
{
    public class GetReplicationMasterNameUniqueQueryHandlerTests : IClassFixture<ReplicationMasterFixture>
    {
        private readonly ReplicationMasterFixture _replicationMasterFixture;

        private Mock<IReplicationMasterRepository> _mockReplicationMasterRepository;

        private readonly GetReplicationMasterNameUniqueQueryHandler _handler;

        public GetReplicationMasterNameUniqueQueryHandlerTests(ReplicationMasterFixture replicationMasterFixture)
        {
            _replicationMasterFixture = replicationMasterFixture;

            _mockReplicationMasterRepository = ReplicationMasterRepositoryMocks.GetReplicationMasterNameUniqueRepository(_replicationMasterFixture.ReplicationMasters);

            _handler = new GetReplicationMasterNameUniqueQueryHandler(_mockReplicationMasterRepository.Object);
        }

        //[Fact]
        //public async Task Handle_Return_True_ReplicationMasterName_Exist()
        //{
        //    _replicationMasterFixture.ReplicationMasters[0].Name = "Replication_Master";
        //    _replicationMasterFixture.ReplicationMasters[0].IsActive = true;

        //    var result = await _handler.Handle(new GetReplicationMasterNameUniqueQuery { ReplicationMasterId = _replicationMasterFixture.ReplicationMasters[0].Name, ReplicationMasterName = _replicationMasterFixture.ReplicationMasters[0].ReferenceId }, CancellationToken.None);

        //    result.ShouldBeTrue();
        //}

        [Fact]
        public async Task Handle_Return_False_ReplicationMasterNameAndId_NotMatch()
        {
            var result = await _handler.Handle(new GetReplicationMasterNameUniqueQuery { ReplicationMasterId = "Replication_Email", ReplicationMasterName = _replicationMasterFixture.ReplicationMasters[0].ReferenceId }, CancellationToken.None);

            result.ShouldBeFalse();
        }

        [Fact]
        public async Task Handle_Call_IsReplicationMasterNameExist_OneTime()
        {
            var handler = new GetReplicationMasterNameUniqueQueryHandler(_mockReplicationMasterRepository.Object);

            await handler.Handle(new GetReplicationMasterNameUniqueQuery { ReplicationMasterId = _replicationMasterFixture.ReplicationMasters[0].ReferenceId, ReplicationMasterName = _replicationMasterFixture.ReplicationMasters[0].ReferenceId }, CancellationToken.None);

            _mockReplicationMasterRepository.Verify(x => x.IsReplicationMasterNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Return_False_ReplicationMasterName_NotMatch()
        {
            var result = await _handler.Handle(new GetReplicationMasterNameUniqueQuery { ReplicationMasterId = "DR_Pro", ReplicationMasterName = 0.ToString() }, CancellationToken.None);

            result.ShouldBeFalse();
        }

        [Fact]
        public async Task Handle_Return_EmptyList_When_NoRecords()
        {
            _mockReplicationMasterRepository = ReplicationMasterRepositoryMocks.GetReplicationMasterEmptyRepository();

            var result = await _handler.Handle(new GetReplicationMasterNameUniqueQuery(), CancellationToken.None);

            result.ShouldBe(false);
        }
    }
}
