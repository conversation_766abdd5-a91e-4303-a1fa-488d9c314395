using ContinuityPatrol.Application.Features.DRReadyLog.Commands.Create;
using ContinuityPatrol.Application.Features.DRReadyLog.Commands.Delete;
using ContinuityPatrol.Application.Features.DRReadyLog.Commands.Update;
using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetByLast7Days;
using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetList;
using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DRReadyLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DrReadyLogFixture : IDisposable
{
    public CreateDRReadyLogCommand CreateDRReadyLogCommand { get; }
    public CreateDRReadyLogResponse CreateDRReadyLogResponse { get; }
    public UpdateDRReadyLogCommand UpdateDRReadyLogCommand { get; }
    public UpdateDRReadyLogResponse UpdateDRReadyLogResponse { get; }
    public DeleteDRReadyLogCommand DeleteDRReadyLogCommand { get; }
    public DeleteDRReadyLogResponse DeleteDRReadyLogResponse { get; }
    public GetDRReadyLogDetailQuery GetDRReadyLogDetailQuery { get; }
    public DRReadyLogDetailVm DRReadyLogDetailVm { get; }
    public GetDRReadyLogListQuery GetDRReadyLogListQuery { get; }
    public List<DRReadyLogListVm> DRReadyLogListVm { get; }
    public GetDRReadyLogPaginatedListQuery GetDRReadyLogPaginatedListQuery { get; }
    public PaginatedResult<DRReadyLogListVm> DRReadyLogPaginatedResult { get; }
    public GetDRReadyLogByBusinessServiceIdQuery GetDRReadyLogByBusinessServiceIdQuery { get; }
    public DRReadyLogByBusinessServiceIdVm DRReadyLogByBusinessServiceIdVm { get; }
    public GetDRReadyLogByLast7DaysQuery GetDRReadyLogByLast7DaysQuery { get; }
    public List<DRReadyLogByLast7DaysVm> DRReadyLogByLast7DaysVm { get; }

    public DrReadyLogFixture()
    {
        var fixture = new Fixture();

        // Configure fixture for enterprise DR Ready Log scenarios
        fixture.Customize<CreateDRReadyLogCommand>(c => c
            .With(x => x.UserId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceName, "Enterprise Critical Business Service")
            .With(x => x.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(x => x.BusinessFunctionName, "Enterprise Critical Function")
            .With(x => x.IsProtected, "Yes")
            .With(x => x.AffectedInfra, "5")
            .With(x => x.ActiveInfra, "150")
            .With(x => x.WorkflowId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, "Enterprise DR Workflow")
            .With(x => x.WorkflowStatus, "Completed")
            .With(x => x.FailedActionName, "")
            .With(x => x.FailedActionId, "")
            .With(x => x.ActiveBusinessFunction, "25")
            .With(x => x.AffectedBusinessFunction, "2")
            .With(x => x.DRReady, "Yes")
            .With(x => x.NotReady, "No")
            .With(x => x.WorkflowAttach, "Enterprise_DR_Plan.pdf")
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectName, "Enterprise Primary Server")
            .With(x => x.ComponentName, "Database Server")
            .With(x => x.Type, "DR_EXECUTION")
            .With(x => x.ErrorMessage, "")
            .With(x => x.StartTime, DateTime.UtcNow.AddHours(-2))
            .With(x => x.EndTime, DateTime.UtcNow));

        fixture.Customize<CreateDRReadyLogResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise DR Ready Log created successfully!")
            .With(x => x.Success, true));

        fixture.Customize<UpdateDRReadyLogCommand>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.UserId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceName, "Enterprise Updated Business Service")
            .With(x => x.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(x => x.BusinessFunctionName, "Enterprise Updated Function")
            .With(x => x.IsProtected, "Yes")
            .With(x => x.AffectedInfra, "3")
            .With(x => x.ActiveInfra, "180")
            .With(x => x.WorkflowId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, "Enterprise Updated DR Workflow")
            .With(x => x.WorkflowStatus, "In Progress")
            .With(x => x.FailedActionName, "")
            .With(x => x.FailedActionId, "")
            .With(x => x.ActiveBusinessFunction, "30")
            .With(x => x.AffectedBusinessFunction, "1")
            .With(x => x.DRReady, "Yes")
            .With(x => x.NotReady, "No")
            .With(x => x.WorkflowAttach, "Enterprise_Updated_DR_Plan.pdf")
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectName, "Enterprise Updated Server")
            .With(x => x.ComponentName, "Application Server")
            .With(x => x.Type, "DR_UPDATE")
            .With(x => x.ErrorMessage, "")
            .With(x => x.StartTime, DateTime.UtcNow.AddHours(-1))
            .With(x => x.EndTime, DateTime.UtcNow));

        fixture.Customize<UpdateDRReadyLogResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise DR Ready Log updated successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DeleteDRReadyLogResponse>(c => c
            .With(x => x.Message, "Enterprise DR Ready Log deleted successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DRReadyLogDetailVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.UserId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceName, "Enterprise Detail Business Service")
            .With(x => x.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(x => x.BusinessFunctionName, "Enterprise Detail Function")
            .With(x => x.IsProtected, "Yes")
            .With(x => x.AffectedInfra, "7")
            .With(x => x.ActiveInfra, "200")
            .With(x => x.WorkflowId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, "Enterprise Detail DR Workflow")
            .With(x => x.WorkflowStatus, "Completed")
            .With(x => x.FailedActionName, "")
            .With(x => x.FailedActionId, "")
            .With(x => x.ActiveBusinessFunction, "35")
            .With(x => x.AffectedBusinessFunction, "3")
            .With(x => x.DRReady, "Yes")
            .With(x => x.NotReady, "No")
            .With(x => x.WorkflowAttach, "Enterprise_Detail_DR_Plan.pdf")
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectName, "Enterprise Detail Server")
            .With(x => x.ComponentName, "Web Server")
            .With(x => x.Type, "DR_DETAIL")
            .With(x => x.ErrorMessage, "")
            .With(x => x.StartTime, DateTime.UtcNow.AddHours(-3))
            .With(x => x.EndTime, DateTime.UtcNow.AddMinutes(-30)));

        fixture.Customize<DRReadyLogListVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.UserId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceName, "Enterprise List Business Service")
            .With(x => x.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(x => x.BusinessFunctionName, "Enterprise List Function")
            .With(x => x.IsProtected, "Yes")
            .With(x => x.AffectedInfra, "4")
            .With(x => x.ActiveInfra, "120")
            .With(x => x.WorkflowId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, "Enterprise List DR Workflow")
            .With(x => x.WorkflowStatus, "Completed")
            .With(x => x.DRReady, "Yes")
            .With(x => x.NotReady, "No"));

        fixture.Customize<DRReadyLogByBusinessServiceIdVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceName, "Enterprise Business Service by ID")
            .With(x => x.WorkflowName, "Enterprise Business Service DR Workflow")
            .With(x => x.WorkflowStatus, "Completed")
            .With(x => x.DRReady, "Yes"));

        fixture.Customize<DRReadyLogByLast7DaysVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x=>x.InfraObjectName,"Enterprise Last 7 Days InfraObjects")
            .With(x=>x.WorkflowId,Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, "Enterprise Last 7 Days Workflow")
            .With(x => x.LogsSchedulerId, Guid.NewGuid().ToString())
            .With(x => x.Reason, "Yes")
            .With(x=>x.IsDRReady,true)
            .With(x => x.StartTime, DateTime.UtcNow.AddDays(-3))
            .With(x => x.EndTime, DateTime.UtcNow.AddDays(-3).AddHours(2)));

        fixture.Customize<GetDRReadyLogPaginatedListQuery>(c => c
            .With(x => x.SearchString, "Enterprise")
            .With(x => x.PageNumber, 1)
            .With(x => x.PageSize, 10)
            .With(x => x.SortColumn, "BusinessServiceName")
            .With(x => x.SortOrder, "ASC"));

        // Create instances
        CreateDRReadyLogCommand = fixture.Create<CreateDRReadyLogCommand>();
        CreateDRReadyLogResponse = fixture.Create<CreateDRReadyLogResponse>();
        
        UpdateDRReadyLogCommand = fixture.Create<UpdateDRReadyLogCommand>();
        UpdateDRReadyLogResponse = fixture.Create<UpdateDRReadyLogResponse>();
        
        DeleteDRReadyLogCommand = new DeleteDRReadyLogCommand { Id = Guid.NewGuid().ToString() };
        DeleteDRReadyLogResponse = fixture.Create<DeleteDRReadyLogResponse>();
        
        GetDRReadyLogDetailQuery = new GetDRReadyLogDetailQuery { Id = Guid.NewGuid().ToString() };
        DRReadyLogDetailVm = fixture.Create<DRReadyLogDetailVm>();
        
        GetDRReadyLogListQuery = new GetDRReadyLogListQuery();
        DRReadyLogListVm = fixture.CreateMany<DRReadyLogListVm>(5).ToList();
        
        GetDRReadyLogPaginatedListQuery = fixture.Create<GetDRReadyLogPaginatedListQuery>();
        DRReadyLogPaginatedResult = new PaginatedResult<DRReadyLogListVm>
        {
            Data = fixture.CreateMany<DRReadyLogListVm>(8).ToList(),
            TotalCount = 8,
            PageSize = 10,
            Succeeded = true
        };
        
        GetDRReadyLogByBusinessServiceIdQuery = new GetDRReadyLogByBusinessServiceIdQuery { BusinessServiceId = Guid.NewGuid().ToString() };
        DRReadyLogByBusinessServiceIdVm = fixture.Create<DRReadyLogByBusinessServiceIdVm>();
        
        GetDRReadyLogByLast7DaysQuery = new GetDRReadyLogByLast7DaysQuery();
        DRReadyLogByLast7DaysVm = fixture.CreateMany<DRReadyLogByLast7DaysVm>(7).ToList();
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
