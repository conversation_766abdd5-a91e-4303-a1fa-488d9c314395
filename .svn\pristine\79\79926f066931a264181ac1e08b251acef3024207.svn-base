using ContinuityPatrol.Application.Features.BulkImport.Commands.Create.Helper;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class BulkImportRepositoryMocks
{
    public static Mock<IBulkImportOperationGroupRepository> CreateBulkImportOperationGroupRepository(List<BulkImportOperationGroup> bulkImportOperationGroups)
    {
        var mockBulkImportOperationGroupRepository = new Mock<IBulkImportOperationGroupRepository>();

        mockBulkImportOperationGroupRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportOperationGroups);

        mockBulkImportOperationGroupRepository.Setup(repo => repo.AddAsync(It.IsAny<BulkImportOperationGroup>())).ReturnsAsync(
            (BulkImportOperationGroup bulkImportOperationGroup) =>
            {
                bulkImportOperationGroup.Id = new Fixture().Create<int>();
                bulkImportOperationGroup.ReferenceId = new Fixture().Create<Guid>().ToString();
                bulkImportOperationGroups.Add(bulkImportOperationGroup);
                return bulkImportOperationGroup;
            });

        mockBulkImportOperationGroupRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BulkImportOperationGroup>()));
        //.Returns(Task.CompletedTask);

        mockBulkImportOperationGroupRepository.Setup(repo => repo.DeleteAsync(It.IsAny<BulkImportOperationGroup>()));
           // .Returns(Task.CompletedTask);

        mockBulkImportOperationGroupRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportOperationGroups.FirstOrDefault(x => x.ReferenceId == id));

        mockBulkImportOperationGroupRepository.Setup(repo => repo.GetBulkImportOperationGroupByBulkImportOperationIds(It.IsAny<List<string>>()))
            .ReturnsAsync((List<string> ids) => bulkImportOperationGroups.Where(x => ids.Contains(x.BulkImportOperationId)).ToList());

        mockBulkImportOperationGroupRepository.Setup(repo => repo.GetBulkImportOperationGroupByBulkImportOperationId(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportOperationGroups.Where(x => x.BulkImportOperationId == id).ToList());

        return mockBulkImportOperationGroupRepository;
    }

    public static Mock<IBulkImportActionResultRepository> CreateBulkImportActionResultRepository(List<BulkImportActionResult> bulkImportActionResults)
    {
        var mockBulkImportActionResultRepository = new Mock<IBulkImportActionResultRepository>();

        mockBulkImportActionResultRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportActionResults);

        mockBulkImportActionResultRepository.Setup(repo => repo.AddAsync(It.IsAny<BulkImportActionResult>())).ReturnsAsync(
            (BulkImportActionResult bulkImportActionResult) =>
            {
                bulkImportActionResult.Id = new Fixture().Create<int>();
                bulkImportActionResult.ReferenceId = new Fixture().Create<Guid>().ToString();
                bulkImportActionResults.Add(bulkImportActionResult);
                return bulkImportActionResult;
            });

        mockBulkImportActionResultRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BulkImportActionResult>()));
        // .Returns(Task.CompletedTask);

        mockBulkImportActionResultRepository.Setup(repo => repo.DeleteAsync(It.IsAny<BulkImportActionResult>()));
           // .Returns(Task.CompletedTask);

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportActionResults.FirstOrDefault(x => x.ReferenceId == id));

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByEntityIdAndBulkImportOperationId(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string entityId, string operationId) => 
                bulkImportActionResults.FirstOrDefault(x => x.EntityId == entityId && x.BulkImportOperationId == operationId));

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByOperationIdAndOperationGroupId(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string operationId, string operationGroupId) => 
                bulkImportActionResults.Where(x => x.BulkImportOperationId == operationId && x.BulkImportOperationGroupId == operationGroupId).ToList());

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByOperationIdsAndOperationGroupIds(It.IsAny<List<string>>(), It.IsAny<List<string>>()))
            .ReturnsAsync((List<string> operationIds, List<string> operationGroupIds) => 
                bulkImportActionResults.Where(x => operationIds.Contains(x.BulkImportOperationId) && operationGroupIds.Contains(x.BulkImportOperationGroupId)).ToList());

        mockBulkImportActionResultRepository.Setup(repo => repo.GetActionByOperationGroupIdAndEntityType(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string operationGroupId, string entityType) => 
                bulkImportActionResults.FirstOrDefault(x => x.BulkImportOperationGroupId == operationGroupId && x.EntityType == entityType));

        return mockBulkImportActionResultRepository;
    }

    public static Mock<BulkImportHelperService> CreateBulkImportHelperService()
    {
        var mockBulkImportHelperService = new Mock<BulkImportHelperService>();

        mockBulkImportHelperService.Setup(service => service.CreateServer(It.IsAny<List<CreateBulkDataServerListCommand>>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        //mockBulkImportHelperService.Setup(service => service.CreateDatabase(It.IsAny<List<CreateBulkDataDatabaseListCommand>>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
        //    .Returns(Task.CompletedTask);

        mockBulkImportHelperService.Setup(service => service.CreateReplication(It.IsAny<List<CreateBulkDataReplicationListCommand>>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        mockBulkImportHelperService.Setup(service => service.CreateInfraObject(It.IsAny<CreateBulkImportOperationListCommand>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        mockBulkImportHelperService.Setup(service => service.CreateWorkflow(It.IsAny<CreateBulkImportOperationListCommand>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        mockBulkImportHelperService.Setup(service => service.DeleteServer(It.IsAny<string >(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        mockBulkImportHelperService.Setup(service => service.DeleteDatabase(It.IsAny<string >(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        mockBulkImportHelperService.Setup(service => service.DeleteReplication(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        mockBulkImportHelperService.Setup(service => service.DeleteInfraObject(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        return mockBulkImportHelperService;
    }
}
