﻿@{
    ViewData["Title"] = "InfraObjectSchedulerLogReport";
}

@using DevExpress.AspNetCore
@using ContinuityPatrol.Web.Areas.Report.ReportTemplate

<style>
    .form-group {
        margin-left: 550px;
        margin-top: 60px;
    }
</style>
@section Styles
    {
    <link href="~/css/viewer.part.bundle.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/thirdparty.bundle.css" rel="stylesheet" asp-append-version="true" />
    }
@section HeaderScripts
    {
    <script src="~/js/common/thirdparty.bundle.js" asp-append-version="true"></script>
    <script src="~/js/common/viewer.part.bundle.js" asp-append-version="true"></script>
   }

<div class="card card-custom gutter-b">
    <div class="card-body p-0">
        <div id="reportContainer">
            @Html.DevExpress().WebDocumentViewer("InfraObjectSchedulerLogReportDocumentViewer").Height("1150px").Bind(new InfraObjectSchedulerLogReport(ViewData["InfraObjectSchedulerLogReportData"].ToString()))
        </div>
    </div>
</div>