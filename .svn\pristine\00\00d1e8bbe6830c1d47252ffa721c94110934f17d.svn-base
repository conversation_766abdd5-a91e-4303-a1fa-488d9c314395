﻿const approvalURL = {
    getPaginatedlist: "/Manage/Template/GetPaginatedlist",
    nameExist: "Manage/Template/IsApprovalMatrixNameExist",
    createOrUpdate: "Manage/Template/CreateOrUpdate",
    usersList: "Manage/Template/ApprovalMatrixUsersList",
    delete: "Manage/Template/Delete"
};
let templateButtonDisable = false;
let titlesArray = "";
let startButtonValue = 1;
let dataTable = "";
let processNameArray = { propsId: "", name: "", businessServiceId: "", businessServiceName: "", straightLineData: "", properties: [], userLists: [], ruleSet: [], SLA: {}, notification: [] };
let editedData = "";
let propertiesID = "";
let editedProcessName = "";
let addProcessName = "Add";
let modifyProcessName = "Update";
let approvalTemplateName = "";
let editWhileCreate = false;
let straightLineData = [];
let addTransition = false;
var createPermission = $("#AdminCreate").data("create-permission").toLowerCase();
var deletePermission = $("#AdminDelete").data("delete-permission").toLowerCase();

if (createPermission == 'false') {
    $(".btn-approvalmattemplate-Create").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
}

$(function () {
    approvalPreventSpecialKeys('#search-inp');
    let selectedValues = [];
    dataTable = $('#templateList').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "fixedColumns": {
                left: 1,
                right: 1
            },
            "ajax": {
                "type": "GET",
                "url": approvalURL.getPaginatedlist,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "Name" : ""
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    const isEmpty = json?.data?.length === 0;
                    $(".pagination-column").toggleClass("disabled", isEmpty);
                    return json?.data;
                }
            },
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title='${data || "NA"}'>  ${data || "NA"} </span>`;
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        return `<div class="d-flex align-items-center gap-2">
                                        <span role="button" title="Edit" class="edit-button" data-template='${JSON.stringify(row)}'>
                                            <i class="cp-edit"></i>
                                        </span>
                                        <span role="button" title="Delete" class="delete-button" data-template-id="${row.id}" data-template-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                            <i class="cp-Delete"></i>
                                        </span>                                                                                                               
                              </div>`;
                    },
                    "orderable": false
                }
            ],

            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },

            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#search-inp').on('input', commonDebounce(function (e) {
        const NameCheckbox = $("#Name");

        if (NameCheckbox.is(':checked')) {
            selectedValues.push(NameCheckbox.val() + inputValue);
        }
        dataTable.ajax.reload(function (json) {
            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    }, 500));

    $("#Create").on("click", function () {
        $("#templateTitle").text("Untitled Template");
        addTransition = true;
        editWhileCreate = false;
        $("#saveProcessName").text("Add");
        $("#saveApprovalTemplate").text("Save");
        $("#saveApprovalTemplate").css("visibility", "hidden");
        $('#templateID').val("");
        approvalTemplateName = "";
        startButtonValue = 1;
        editedData = "";
        propertiesID = "";
        editedProcessName = "";
        $("#approvalContainer").empty();
        processNameArray = { propsId: "", name: "", businessServiceId: "", businessServiceName: "", straightLineData: "", properties: [], userLists: [], ruleSet: [], SLA: {}, notification: [] };
    })

    $("#approvalTemplateName").on("keyup", function () {
        templateNameValidation($(this).val(), $('#templateID').val(), approvalURL.nameExist,
            $("#approvalTemplateNameError"), "Enter template name")
    });

    $("#inputDuration").on("keyup", function () {
        commonInputValidation($(this).val(), $("#enterDurationError"), "Enter duration");
    });

    $("#selectDuration").on("change", function () {
        commonInputValidation($(this).val(), $("#selectDurationError"), "Select duration");
    });

    $("#saveApprovalTemplate").on("click", async function () {
        $('#saveprocessModal').modal("show");
        setTimeout(() => {
            $('#approvalTemplateName').val(approvalTemplateName || "");
        })
    })

    $("#saveTemplateName").on('click', async function () {
        if (titlesArray.length) {
            for (let i = 0; i < titlesArray.length - 1; i++) {
                straightLineData.push({ from: titlesArray[i], to: titlesArray[i + 1] });
            }
        }
        let validateTemplateName = await templateNameValidation($("#approvalTemplateName").val(), $('#templateID').val(),
            approvalURL.nameExist, $("#approvalTemplateNameError"), "Enter template name");

        let templateData = (editedData && processNameArray.name) ?
            replaceObjectIfEmptyName(editedData, processNameArray) :
            editedData || processNameArray;

        templateData.straightLineData = straightLineData;

        if (validateTemplateName && !templateButtonDisable) {
            templateButtonDisable = true;
            let approvalMatrixTemplate = {
                "Id": $('#templateID').val() || "",
                "BusinessServiceId": "BusinessServiceId",
                "BusinessServiceName": "BusinessServiceName",
                "Name": $("#approvalTemplateName").val(),
                "Description": "Description",
                "Properties": JSON.stringify(templateData),
                "WorkflowProfileNames": "WorkflowProfileNames",
                "TemplateName": $("#approvalTemplateName").val(),
                "ActionType": "ActionType",
                "Status": "Status",
                "WorkflowProfileIds": "WorkflowProfileIds",
                "WorkflowNames": "WorkflowNames",
                "WorkflowIds": "WorkflowIds",
                "ApprovalFlag": "ApprovalFlag",
                "RejectedFlag": "RejectedFlag",
                "ApprovedBy": "selvan"
            };

            $.ajax({
                type: 'Post',
                url: RootUrl + approvalURL.createOrUpdate,
                dataType: "json",
                data: approvalMatrixTemplate,
                headers: {
                    'RequestVerificationToken': await gettoken()
                },
                success: function (response) {
                    if (response.success) {
                        notificationAlert("success", response?.data?.message);
                        dataTableCreateAndUpdate($("#saveApprovalTemplate"), dataTable);
                    } else {
                        errorNotification(response);
                    }
                    clearFieldsAfterSace();
                    templateButtonDisable = false;
                },
            })
        }
    })

    $("#addTemplate").on("click", function () {
        $("#approvalProcessName, #approvalTemplateName, #textDescription, #inputDuration, #ApTwo, #RjTwo").val("");
        $("#approvalProcessNameError, #approvalTemplateNameError, #UserNameError, #enterDurationError, #selectDurationError, #operationalServiceError")
            .text("")
            .removeClass("field-validation-error");
        $("#userNameList, #selectDuration, #ApOne, #RjOne, #selectOperationalService").val("").trigger("change.select2");
        $("#ApThree, #RjThree").text(0);
        $("#saveProcessName").text("Add");
        $("#saveApprovalTemplate").css("visibility", "hidden");
        $("#processDelete").addClass("d-none");
        toggleWizard(true);
        addTransition = true;
    });

    $("#selectOperationalService").on("change", function () {
        commonInputValidation($(this).val(), $("#operationalServiceError"), "Select operational service");
    });

    $("#approvalProcessName").on("keyup input", function () {
        commonInputValidation($(this).val(), $("#approvalProcessNameError"), "Enter process name");
    });

    $("#ApOne").on("change", function () {
        commonInputValidation($(this).val(), $("#ApOneError"), "Select option", "approver");
    });

    $("#ApTwo").on("keyup", function (event) {
        let value = $(this).val().trim();
        let userLength = $("#userNameList").val().length;
        let userValue = parseInt(value, 10) || 0;

        if (userLength < userValue) {
            $(this).val(userLength);
        }
        commonInputValidation(value, $("#ApTwoError"), "Enter approvers");
    });

    $("#RjOne").on("change", function () {
        commonInputValidation($(this).val(), $("#RjOneError"), "Select option", "reject");
    });

    $("#RjTwo").on("keyup", function () {
        let value = $(this).val().trim();
        let userLength = $("#userNameList").val().length;
        let userValue = parseInt(value, 10) || 0;

        if (userLength < userValue) {
            $(this).val(userLength);
        }
        commonInputValidation(value, $("#RjTwoError"), "Enter approvers");
    });

    $("#processDelete").on("click", function () {
        editedData = removeObjectByPropsId(editedData, propertiesID);
        $("#approvalContainer").empty();
        startButtonValue = 1;
        addTransition = false;
        setTimeout(() => {
            recursiveEdit(editedData);
        }, 100)
    })

    $("#userNameList").on("change", function () {
        let value = $(this).val();
        $("#ApThree, #RjThree").text(value.length);
        commonInputValidation(value[0], $("#UserNameError"), "Select user");
    });

    $("#saveProcessName").on("click", function () {
        let selectedUsers = [];
        $("#userNameList :selected").each(function () {

            if ($(this).val()) {
                selectedUsers.push({
                    id: $(this).val(),
                    name: $(this).text()
                });
            }
        });
        let processName = $("#approvalProcessName").val();
        //let selectedUser = $("#userNameList").val();
        let textDescription = $("#textDescription").val();
        let enterDuration = $("#inputDuration").val();
        let selectDuration = $("#selectDuration").val();
        let approvalOne = $("#ApOne").val();
        let appprovalTwo = $("#ApTwo").val();
        let rejectOne = $("#RjOne").val();
        let rejectTwo = $("#RjTwo").val();
        let operationalService = $("#selectOperationalService").val();
        let operationalServiceName = $("#selectOperationalService :selected").text();

        let appOneValidation = commonInputValidation(approvalOne, $("#ApOneError"), "Select option");
        let appTwoValidation = commonInputValidation(appprovalTwo, $("#ApTwoError"), "Enter approvars");
        let rejOneValidation = commonInputValidation(rejectOne, $("#RjOneError"), "Select option");
        let rejTwoValidation = commonInputValidation(rejectTwo, $("#RjTwoError"), "Enter approvars");
        let approvalObject = { type: approvalOne, ruleCount: appprovalTwo, approverCount: $("#ApThree").text() };
        let rejectObject = { type: rejectOne, ruleCount: rejectTwo, rejectCount: $("#RjThree").text() };
        let notification = { email: true, sms: false, application: false };

        if (appOneValidation && appTwoValidation && rejOneValidation && rejTwoValidation) {
            if ($("#saveProcessName").text() === modifyProcessName) {
                let newData = {
                    propsId: propertiesID,
                    name: processName,
                    userLists: selectedUsers,
                    description: textDescription,
                    SLA: { duration: enterDuration, period: selectDuration },
                    ruleSet: [approvalObject, rejectObject],
                    notification: [notification],
                };
                editedData = updateByPropsId(editedData || processNameArray, propertiesID, newData);
                $("#closeOffcanvas").trigger("click");
                $("#approvalContainer").empty();
                startButtonValue = 1;
                $('#approvalTemplateName').val(approvalTemplateName || "");
                $("#offcanvasExample").offcanvas("hide");
                recursiveEdit(editedData, editWhileCreate);
            } else {
                createEditTemplate(processName, textDescription, selectedUsers, enterDuration, selectDuration,
                    approvalObject, rejectObject, notification, operationalService, operationalServiceName);
            }
        }
    });

    //$("#userList").on("change", function () {
    //    let $this = $(this);
    //    if ($this.prop("checked")) {
    //        $('#dynamicLabel').html("User List");
    //    }
    //});
    //$("#userGroupList").on("change", function () {
    //    let $this = $(this);
    //    if ($this.prop("checked")) {
    //        $('#dynamicLabel').html("User Group List");
    //    }
    //});
    //$("#addTransition").on("click", function () {
    //    $("#templateFromName").val("");
    //    $("#templateToName").val("");
    //    $("#transitionName").val("");
    //    $("#transitionColour").val("");
    //    $("#addTransitionModal").modal("show");
    //});

    $("#templateFromName").on("input", function () {
        commonInputValidation($(this).val().trim(), $("#templateFromNameError"), "Enter From Name");
    });

    $("#templateToName").on("input", function () {
        commonInputValidation($(this).val().trim(), $("#templateToNameError"), "Enter To Name");
    });

    $("#transitionName").on("input", function () {
        commonInputValidation($(this).val().trim(), $("#transitionNameError"), "Enter Transition Name");
    });

    $("#transitionColour").on("input", function () {
        commonInputValidation($(this).val().trim(), $("#transitionColourError"), "Enter Colour Code");
    });

    //$("#validateTransition").on("click", function () {
    //    let fromName = $("#templateFromName").val();
    //    let toName = $("#templateToName").val();
    //    let transitionName = $("#transitionName").val();
    //    let transitionColour = $("#transitionColour").val();
    //    let templateFromNameValidation = commonInputValidation(fromName, $("#templateFromNameError"), "Enter From Name");
    //    let templateToNameValidation = commonInputValidation(toName, $("#templateToNameError"), "Enter To Name");
    //    let templateTransitionNameValidation = commonInputValidation(transitionName, $("#transitionNameError"), "Enter Transition Name");
    //    let templateTransitionColourValidation = commonInputValidation(transitionColour, $("#transitionColourError"), "Enter Colour Code");
    //    if (templateFromNameValidation && templateToNameValidation && templateTransitionNameValidation && templateTransitionColourValidation) {
    //        addProcessLine(fromName, toName, transitionName, transitionColour);
    //    }
    //});

    $("#nextButton").on("click", function () {
        let operationalServiceValidation = commonInputValidation($("#selectOperationalService").val(), $("#operationalServiceError"), "Select operational service");
        let enterDurationValidation = commonInputValidation($("#inputDuration").val(), $("#enterDurationError"), "Enter duration");
        let selectDurationValidation = commonInputValidation($("#selectDuration").val(), $("#selectDurationError"), "Select duration");
        let userValidation = $("#userNameList").val().length > 0 ? true : commonInputValidation("", $("#UserNameError"), "Select user");
        let nameValidation = commonInputValidation($("#approvalProcessName").val(), $("#approvalProcessNameError"), "Enter process name");

        if (operationalServiceValidation && enterDurationValidation && selectDurationValidation
            && userValidation && nameValidation) {
            toggleWizard(false);
        }
    });

    $("#previousButton").on("click", function () {
        toggleWizard(true);
    });

    $("#endTemplate").on("click", function () {
        let container = $("#approvalContainer");

        if (container.find("div").length > 0) {
            $("#saveApprovalTemplate").css("visibility", "visible");
            //$("#addTransition").attr("disabled", false);
        }
        if ($("#endbutton").length === 0) {
            container.scrollTop(0);
            let endArrowId = getRandomId('arrow');
            let endPolylineId = getRandomId('polyline');
            let lastDiv = container.children("div").last();
            let position = lastDiv.position();
            let boxWidth = lastDiv.outerWidth();
            let boxHeight = lastDiv.outerHeight();
            let centerPoint = position.left + (boxWidth / 2);
            let startPoint = position.top + boxHeight;
            let endPoint = position.top + (boxHeight * 2);
            let points = [[centerPoint, startPoint], [centerPoint, endPoint]];
            let svgLine = `
                            <svg id="endsvg" 
                                 style="overflow:visible; position:absolute; top:0; left:0;">
                                <defs>
                                    <marker id="${endArrowId}" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                                        <path d="M0,0 L10,5 L0,10 Z" fill="black" />
                                    </marker>
                                </defs>                                                           
                                <polyline class="${endPolylineId}" 
                                          points="${points}" 
                                          fill="none" stroke="green" stroke-width="1" 
                                          stroke-dasharray="4,4" 
                                          marker-end="url(#${endArrowId})"/>                        
                            </svg>`;
            container.append(svgLine);
            let endButton = `
                              <div id="endbutton"
                                 class="endButton"
                                 style="border-radius:20px;
                                 font-weight:bold;
                                 box-shadow:0 2px 5px rgba(0,0,0,0.2);">
                                 <img title="End" src="/img/input_Icons/End.svg" width="30" height="30" draggable="false" loading="lazy" alt="End image">
                             </div>`;
            container.append(endButton);
            container.scrollTop(container[0].scrollHeight);

            if (addTransition) {
                //$("#templateFromName").val("");
                //$("#templateToName").val("");
                //$("#transitionName").val("");
                //$("#transitionColour").val("");
                //$("#addTransitionModal").modal("show");
            }
        }
    });

    GetBusinessServiceList();

    $.ajax({
        type: "GET",
        url: RootUrl + approvalURL.usersList,
        data: {},
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result && result.length > 0) {
                let options = [];
                let userNameList = $('#userNameList');
                result.forEach(function (item) {
                    options.push($('<option>').val(item.id).text(item.userName));
                });
                userNameList.append(options)
            }
            else {
                errorNotification(result);
            }
        }
    })    
});

$(document).on("click", '.delete-button', function () {
    let templateID = $(this).attr('data-template-id');
    let templateName = $(this).attr('data-template-name');
    $('#deleteData').attr('title', templateName).text(templateName);
    $('#textDeleteId').val(templateID);
});

$("#confirmDeleteButton").on("click", async function () {
    const form = $('#templateDelete')[0];
    const formData = new FormData(form);

    if (!templateButtonDisable) {
        templateButtonDisable = true;
        const response = await $.ajax({
            type: "DELETE",
            url: RootUrl + approvalURL.delete,
            headers: {
                'RequestVerificationToken': await gettoken()
            },
            data: formData,
            contentType: false,
            processData: false,
        });
        $("#DeleteModal").modal("hide");

        if (response?.success) {
            notificationAlert("success", response?.data?.message);
            setTimeout(() => {
                dataTableDelete(dataTable);
            }, 2000)
        } else {
            errorNotification(response);
        }
        templateButtonDisable = false;
    }
});
