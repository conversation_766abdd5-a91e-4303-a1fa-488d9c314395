﻿
function validateDropDown(value, errorMsg, errorElement) {
    if (!value || value?.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error')
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
//infraobject to businessfunction model
$('#biaInfraBs').on('change', function () {
    let businessServiceId = ""
    businessServiceId = $('#biaInfraBs option:selected').val()
    treeFunction(businessServiceId)
    validateDropDown($(this).val(), "Select operational service", $('#biaInfraBsError'));
})
$('#biaInfraInfraObject').on('change', function () {
    validateDropDown($(this).val(), "Select infraobject", $('#biaInfraInfraObjectError'));
})
$('#biaInfraInfraComponent').on('change', function () {
    validateDropDown($(this).val(), "Select infra component", $('#biaInfraInfraComponentError'));
})
$('#biaInfraBusinessFunction').on('change', function () {
    validateDropDown($(this).val(), "Select operational function", $('#biaInfraBusinessFunctionError'));
})
$('#biaInfraImpact').on('change', function () {
    validateDropDown($(this).val(), "Select impact", $('#biaInfraImpactError'));
})
$('#biaInfraBs1').on('change', function () {
    validateDropDown($(this).val(), "Select operational service", $('#biaInfraBs1Error'));
})
$('#biaInfraImpact1').on('change', function () {
    validateDropDown($(this).val(), "Select impact", $('#biaInfraImpact1Error'));
})
$('#biaInfraDate').on('change', function () {
    validateDropDown($(this).val(), "Select date", $('#biaInfraDateError'));
})

$("#biaNavHomeTab,#biaNavProfileTab,#biaNavContactTab").on("click", function () {
    let scheDule
    if ($("#biaNavHomeTab").attr('class').includes("active")) {
        scheDule = 1
    } else if ($("#biaNavProfileTab").attr('class').includes("active")) {
        scheDule = 2
    } else {
        scheDule = 3
    }
    drawTree()
    getBia(scheDule)
})
$("#createBiaModal").one("click", async function () {
    if (($('#biaTreeBS option:selected').val() == "") == true) {
        await infraBfEdit()
    }
})

$('#biaInfraBs').on("change", async function () {
    let id = $(this).val()
    await $.ajax({
        type: "POST",
        url: RootUrl + biaUrls?.GetInfraObjectByBusinessServiceId,
        dataType: "json",
        data: {
            businessServiceId: id,
            __RequestVerificationToken: gettoken()
        },
        success: function (result) {
            let datas = result?.data
            if (result?.success) {
                $('#biaInfraInfraObject').empty().append('<option title="" value=""></option>')
                datas.forEach((value, index) => {
                    $('#biaInfraInfraObject').append('<option title="' + value.name + '" value="' + value.id + '">' + value.name + '</option>')
                });
            }
        },
    })
})
$('#biaInfraInfraComponent').on("change", function () {
    assignType = ""
    if (Array.isArray(assignData) == true) {
        assignData?.forEach((x, i) => {
            if ($('#biaInfraInfraComponent option:selected').val() == x?.serverId) {
                if (Object.keys(x)?.includes("serverName") == true) {
                    assignType = "server"
                }
            } else if ($('#biaInfraInfraComponent option:selected').val() == x?.databaseId) {
                if (Object.keys(x)?.includes("databaseName") == true) {
                    assignType = "database"
                }
            }
        })
    }
})
$('#biaInfraInfraObject').on("change", async function () {
    $('#biaInfraInfraComponent').empty().append('<option title="" value=""></option>')
    let id = $(this).val()
    await $.ajax({
        type: "POST",
        url: RootUrl + biaUrls?.GetInfraObjectById,
        dataType: "json",
        data: {
            infraObjectId: id,
            __RequestVerificationToken: gettoken()
        },
        success: function (result) {
            let value = result?.data
            if (result?.success) {
                $("#biaInfraBs1,#biaInfraBusinessFunction").empty().append('<option title="" value=""></option>')
                $('#biaInfraBs1').append('<option title="' + value?.businessServiceName + '" value="' + value?.businessServiceId + '">' + value?.businessServiceName + '</option>')
                $('#biaInfraBusinessFunction').append('<option title="' + value?.businessFunctionName + '" value="' + value?.businessFunctionId + '">' + value?.businessFunctionName + '</option>')
                $("#biaInfraBusinessFunction,#biaInfraBs1").each(function () {
                    $(this).siblings('[value="' + this.value + '"]').remove()
                })
                let arr = value?.serverDto?.concat(value?.databaseDto)
                assignData = arr
                arr?.forEach((x, i) => {
                    if (x?.serverName != null && x?.serverId != null) {
                        $('#biaInfraInfraComponent').append('<option title="' + x?.serverName + '" value="' + x?.serverId + '">' + x?.serverName + '</option>')
                    }
                    if (x?.databaseName != null && x?.databaseId != null) {
                        $('#biaInfraInfraComponent').append('<option title="' + x?.databaseName + '" value="' + x?.databaseId + '">' + x?.databaseName + '</option>')
                    }
                })
            }
        },
    })
})
$("#biaInfraBfSave").on("click", async function () {
    let form = $("#biaCreateModal");

    let isinfrabusinessservice = validateDropDown($("#biaInfraBs").val(), "Select operational service", $('#biaInfraBsError'));

    let isinfraobject = validateDropDown($("#biaInfraInfraObject").val(), "Select  infraobject", $('#biaInfraInfraObjectError'));

    let isinfracomponent = validateDropDown($("#biaInfraInfraComponent").val(), "Select infra component", $('#biaInfraInfraComponentError'));

    let isbusinessfunction = validateDropDown($("#biaInfraBusinessFunction").val(), "Select operational function", $('#biaInfraBusinessFunctionError'));

    let isinfraimpact = validateDropDown($("#biaInfraImpact").val(), "Select impact", $('#biaInfraImpactError'));

    let isinfrabusinessservice1 = validateDropDown($("#biaInfraBs1").val(), "Select operational service", $('#biaInfraBs1Error'));

    let isinfraimpact1 = validateDropDown($("#biaInfraImpact1").val(), "Select impact", $('#biaInfraImpact1Error'));

    let isinfradate = validateDropDown($("#biaInfraDate").val(), "Select date", $('#biaInfraDateError'));

    if (isinfrabusinessservice & isinfraobject & isinfracomponent & isinfradate & isbusinessfunction & isinfraimpact & isinfraimpact1 & isinfrabusinessservice1) {
        form.trigger("submit");
        let jsonData = [
            {
                "BusinessServiceName": $("#biaInfraBs option:selected").text(),
                "BusinessServiceId": $("#biaInfraBs").val(),
                "InfraObjectName": $("#biaInfraInfraObject option:selected").text(),
                "InfraObjectId": $("#biaInfraInfraObject").val(),
                "InfraComponentName": $("#biaInfraInfraComponent option:selected").text(),
                "InfraComponentId": $("#biaInfraInfraComponent").val(),
                "InfraComponentType": assignType,
                "IsNotAvailableThen": {
                    "BusinessFunctionName": $("#biaInfraBusinessFunction option:selected").text(),
                    "BusinessFunctionId": $("#biaInfraBusinessFunction").val(),
                    "BusinessFunctionImpact": $("#biaInfraImpact").val(),

                },
                "then": {
                    "BusinessServiceName": $("#biaInfraBs1 option:selected").text(),
                    "BusinessServiceId": $("#biaInfraBs1").val(),
                    "BusinessServiceImpact": $("#biaInfraImpact1").val()
                },
                "propertytreedata": treeInfraBf,
                "Impact": $("#biaInfraImpact1").val()
            }
        ]

        let data = {
            "Description": $("#biaInfraBFDescription").val().length > 0 ? $("#biaInfraBFDescription").val() : "NA",
            "Type": 1,
            "EntityId": $("#biaInfraBs").val(),
            "Properties": JSON.stringify(jsonData),
            "EffectiveDateFrom": $("#biaInfraDate").val(),
            "EffectiveDateTo": $("#biaInfraDate").val(),
            "IsEffective": "",
            "RuleCode": "Queue process",
            __RequestVerificationToken: gettoken()
        }
        $('#biaInfraBfSave').text() === "Update" ? data["id"] = globalInfraBfId : null
        await $.ajax({
            type: "POST",
            url: RootUrl + biaUrls?.CreateOrUpdate,
            dataType: "json",
            data: data,
            success: function (result) {
                let data = result?.data
                if (result?.success) {
                    notificationAlert("success", data?.message)
                    getBia(1)
                    $("#biaCreateModal").modal('hide');

                } else {
                    errorNotification(result)
                }
            },
        })
        $("#biaInfraBFDescription").val("")
        $("#biaInfraBs,#biaInfraInfraObject,#biaInfraInfraComponent,#biaInfraImpact,#biaInfraBusinessFunction,#biaInfraBs1,#biaInfraImpact1,#biaInfraDate").val("").trigger('change')
        $('#biaInfraBfSave').text("Save")
        clearBiaruleErrorElements()
    }
})
function infraToBsdelete(data) {
    globalInfratoBfDeleteId = $(data).attr('deleteId')
    $("#biaInfratoBfDeletedId").text($(data).attr('delete_name')).attr("title", $(data).attr('delete_name'))
}
$("#biaInfratoBfConfirmDeleteButton").on("click", async function () {
    await $.ajax({
        type: "POST",
        url: RootUrl + biaUrls?.Delete,
        dataType: "json",
        data: {
            id: globalInfratoBfDeleteId,
            __RequestVerificationToken: gettoken()
        },
        success: function (result) {
            let data = result?.data
            if (result?.success) {
                notificationAlert("success", data?.message)
                $("#biaDeleteModal").modal('hide');
            } else {
                errorNotification(result)
            }
        },
    })
    getBia()
})
//business function to business function modal
$('#biaBfBusinessFunction').on("change", function () {
    validateDropDown($(this).val(), "Select impact", $('#biaBfBusinessFunctionError'));
    if ($(this).val()) {
        $('#biaBfBusinessFunction1').empty().append('<option value=""></option>')
        $("#biaBfBusinessFunction1Error").text('').removeClass('field-validation-error')
        affectBf?.forEach((value, index) => {
            if (value?.id != $('#biaBfBusinessFunction').val()) {
                $('#biaBfBusinessFunction1').append('<option bs-Name="' + value.businessServiceName + '" bs-Id="' + value.businessServiceId + '" title="' + value?.name + '"  value="' + value?.id + '">' + value?.name + '</option>')
            }
        });
    }
})
$('#biaBfImpact').on('change', function () {
    validateDropDown($(this).val(), "Select impact", $('#biaBfImpactError'));
})
$('#biaBfBusinessFunction1').on('change', function () {
    validateDropDown($(this).val(), "Select operational function", $('#biaBfBusinessFunction1Error'));
})
$('#biaBfImpact1').on('change', function () {
    validateDropDown($(this).val(), "Select impact", $('#biaBfImpact1Error'));
})
$('#biaBfDate').on('change', function () {
    validateDropDown($(this).val(), "Select date", $('#biaBfDateError'));
})
function bfToBsdelete(data) {
    globalBftoBfDeleteId = $(data).attr('deleteId')
    $("#biaBftoBfDeletedid").text($(data).attr('delete_name')).attr("title", $(data).attr('delete_name'));
}
$("#biaCreateBfModal").one("click", function () {
    bfBfedit()
})
async function bfBfedit(datas) {
    $('#biaBfBusinessFunction').empty()
    await $.ajax({
        type: "GET",
        url: RootUrl + biaUrls?.GetBusinessFunctionList,
        dataType: "json",
        data: { id: "" },
        success: function (result) {
            let data = result?.data
            if (result?.success) {
                data?.forEach((value, index) => {
                    $('#biaBfBusinessFunction').append('<option title="" value=""></option>')
                    $('#biaBfBusinessFunction').append('<option title="' + value?.name + '" bs-Name="' + value.businessServiceName + '" bs-Id="' + value.businessServiceId + '" value="' + value?.id + '">' + value?.name + '</option>')

                })
                $("#biaBfBusinessFunction,#biaBfBusinessFunction1").each(function () {
                    $(this).siblings('[value="' + this.value + '"]').remove()
                })
                if ($(datas).attr('name') == "bfupdatename") {
                    $('#biaBfBfSave').text("Update");
                    globalBfBfId = $(datas).attr('updateId');
                    $("#biaBFBFDescription").val($(datas).attr('description') == "NA" ? "" : $(datas).attr('description'))
                    $("#biaBfBusinessFunction").val($(datas).attr('Bf-Bf')).trigger('change')
                    $("#biaBfImpact").val($(datas).attr('bf_impact')).trigger('change')
                    $("#biaBfBusinessFunction1").val($(datas).attr('Bf_Bf1')).trigger('change')
                    $("#biaBfImpact1").val($(datas).attr('bf_impact1')).trigger('change')
                    $("#biaBfDate").val($(datas).attr('date')).trigger('change')
                }
            } else {
                errorNotification(result)
            }
        },
    })
}
$("#biaBftoBfConfirmDeleteButton").on("click", async function () {
    await $.ajax({
        type: "POST",
        url: RootUrl + biaUrls?.Delete,
        dataType: "json",
        data: {
            id: globalBftoBfDeleteId,
            __RequestVerificationToken: gettoken()
        },
        success: function (result) {
            let data = result?.data
            if (result?.success) {
                notificationAlert("success", data?.message)
                $("#biaDeleteModal1").modal('hide');
            } else {
                errorNotification(result)
            }
        },
    })
    getBia()
})
$("#biaBfBfSave").on("click", async function () {
    let form = $("#biaBusinessFunctionModal")

    let isbusinessfunction = validateDropDown($("#biaBfBusinessFunction").val(), "Select operational function", $('#biaBfBusinessFunctionError'));

    let isbfimpact = validateDropDown($("#biaBfImpact").val(), "Select impact", $('#biaBfImpactError'));

    let isbusinessfunction1 = validateDropDown($("#biaBfBusinessFunction1").val(), "Select operational function", $('#biaBfBusinessFunction1Error'));

    let isbfimpact1 = validateDropDown($("#biaBfImpact1").val(), "Select impact", $('#biaBfImpact1Error'));

    let isbfdate = validateDropDown($("#biaBfDate").val(), "Select date", $('#biaBfDateError'));

    if (isbusinessfunction & isbfimpact & isbusinessfunction1 & isbfimpact1 & isbfdate) {
        form.trigger("submit")
        let jsonData = [{
            "if": {
                "BusinessServiceId": $("#biaBfBusinessFunction option:selected").attr("bs-Id"),
                "BusinessServiceName": $("#biaBfBusinessFunction option:selected").attr("bs-Name"),
                "BusinessFunctionName": $("#biaBfBusinessFunction option:selected").text(),
                "BusinessFunctionId": $("#biaBfBusinessFunction").val(),
                "BusinessFunctionImpact": $("#biaBfImpact").val(),
            },
            "then": {
                "BusinessServiceId": $("#biaBfBusinessFunction1 option:selected").attr("bs-Id"),
                "BusinessServiceName": $("#biaBfBusinessFunction1 option:selected").attr("bs-Name"),
                "BusinessFunctionName": $("#biaBfBusinessFunction1 option:selected").text(),
                "BusinessFunctionId": $("#biaBfBusinessFunction1").val(),
                "BusinessFunctionImpact": $("#biaBfImpact1").val()
            }
        }
        ]
        let data = {
            "Description": $("#biaBFBFDescription").val().length > 0 ? $("#biaBFBFDescription").val() : "NA",
            "Type": 2,
            "EntityId": $("#biaBfBusinessFunction").val(),
            "Properties": JSON.stringify(jsonData),
            "EffectiveDateFrom": $("#biaBfDate").val(),
            "EffectiveDateTo": $("#biaBfDate").val(),
            "IsEffective": "",
            "RuleCode": "",
            __RequestVerificationToken: gettoken()
        }
        $('#biaBfBfSave').text() === "Update" ? data["id"] = globalBfBfId : null
        await $.ajax({
            type: "POST",
            url: RootUrl + biaUrls?.CreateOrUpdate,
            dataType: "json",
            data: data,
            success: function (result) {
                let data = result?.data
                if (result?.success) {
                    notificationAlert("success", data?.message)
                    getBia(2)
                    $("#biaBusinessFunctionModal").modal('hide');

                } else {
                    errorNotification(result)
                }
            },
        })
        $("#biaBFBFDescription").val("")
        $("#biaBfImpact1,#biaBfBusinessFunction1,#biaBfImpact,#biaBfBusinessFunction,#biaBfDate").val("").trigger('change')
        $('#biaBfBfSave').text("Save")
        clearBiaruleErrorElements()
    }
})
// business service to business service modal
$('#biaBsBusinessService').on('change', function () {
    validateDropDown($(this).val(), "Select operational service", $('#biaBsBusinessServiceError'));
    if ($(this).val()) {
        $('#biaBsInfraComponent').empty().append('<option value=""></option>')
        $("#biaBsInfraComponentError").text('').removeClass('field-validation-error');
        affectBs?.forEach((value, index) => {
            if (value?.id != $('#biaBsInfraComponent').val()) {
                $('#biaBsInfraComponent').append('<option title="' + value?.name + '" value="' + value?.id + '">' + value?.name + '</option>')
            }
        });
    }
})
$('#biaBsImpact').on('change', function () {
    validateDropDown($(this).val(), "Select impact", $('#biaBsImpactError'));
})
$('#biaBsInfraComponent').on('change', function () {
    validateDropDown($(this).val(), "Select operational service", $('#biaBsInfraComponentError'));
 arrayData = [];
    const selectBs = this.querySelectorAll('option:checked');
    selectBs?.forEach(option => {
        const val = option.textContent;
        const id = option.value;
        const obj = { Id: id, Val: val };
        arrayData.push(obj);
    });

})
$('#biaBsDate').on('change', function () {
    validateDropDown($(this).val(), "Select date", $('#biaBsDateError'));
})
$("#biaBsBsSave").on("click", async function () {
    let form = $("biaBusinessServiceModal")

    let isbusinessservice = validateDropDown($("#biaBsBusinessService").val(), "Select operational service", $('#biaBsBusinessServiceError'));

    let isbsimpact = validateDropDown($("#biaBsImpact").val(), "Select impact", $('#biaBsImpactError'));
    let isbscomponent = validateDropDown(
        ($("#biaBsInfraComponent").val() || []).filter(v => v !== '')?.length ? $("#biaBsInfraComponent").val() : null,
        "Select operational service",
        $('#biaBsInfraComponentError')
    );


    let isbsdate = validateDropDown($("#biaBsDate").val(), "Select date", $('#biaBsDateError'));

    if (isbusinessservice & isbsimpact & isbscomponent & isbsdate) {
        form.trigger("submit")
        let jsonData = [{
            "BusinessServiceName": $("#biaBsBusinessService option:selected").text(),
            "BusinessServiceId": $("#biaBsBusinessService").val(),
            "Impact": $("#biaBsImpact").val(),
            "Is": {
                "ByBusinessserviceProperties": arrayData
            }
        }
        ]
        let data = {
            "Description": $("#biaBSBSDescription").val().length > 0 ? $("#biaBSBSDescription").val() : "NA",
            "Type": 3,
            "EntityId": $("#biaBsBusinessService").val(),
            "Properties": JSON.stringify(jsonData),
            "EffectiveDateFrom": $("#biaBsDate").val(),
            "EffectiveDateTo": $("#biaBsDate").val(),
            "IsEffective": "",
            "RuleCode": "",
            __RequestVerificationToken: gettoken()
        }
        $('#biaBsBsSave').text() === "Update" ? data["id"] = globalBSBSId : null
        await $.ajax({
            type: "POST",
            url: RootUrl + biaUrls?.CreateOrUpdate,
            dataType: "json",
            data: data,
            success: function (result) {
                let data = result?.data
                if (result?.success) {
                    notificationAlert("success", data?.message)
                    getBia(3)
                    $("#biaBusinessServiceModal").modal('hide');
                } else {
                    errorNotification(result)
                }
            },
        })
        $("#biaBSBSDescription").val("")
        $("#biaBsBusinessService,#biaBsImpact,#biaBsInfraComponent,#biaBsDate").val("").trigger('change')
        $('#biaBsBsSave').text("Save")
        clearBiaruleErrorElements()
    }
})
function bsToBsdelete(data) {
    globalBstoBsDeleteId = $(data).attr('deleteId')
    $("#biaBstoBsDeletedId").text($(data).attr('delete_name')).attr("title", $(data).attr('delete_name'));
}
$("#biaBstoBsConfirmDeleteButton").on("click", async function () {
    await $.ajax({
        type: "POST",
        url: RootUrl + biaUrls?.Delete,
        dataType: "json",
        data: {
            id: globalBstoBsDeleteId,
            __RequestVerificationToken: gettoken()
        },
        success: function (result) {
            let data = result?.data
            if (result?.success) {
                notificationAlert("success", data?.message)
                $("#biaDeleteModal2").modal('hide');
            } else {
                errorNotification(result)
            }
        },
    })
    getBia()
})
$("#biaCreateSecondModal").one("click", function () {
    bSBSBinddata()
})
async function bSBSBinddata(datas) {
    await $.ajax({
        type: "GET",
        url: RootUrl + biaUrls?.GetBusinessServiceList,
        dataType: "json",
        success: function (result) {
            let data = result?.data
            if (result?.success) {
                affectBs = data
                data?.forEach((value, index) => {
                    $('#biaBsBusinessService').append('<option title="' + value?.name + '" value="' + value?.id + '">' + value?.name + '</option>')
                });
                $("#biaBsBusinessService option,#biaBsInfraComponent option").each(function () {
                    $(this).siblings('[value="' + this.value + '"]').remove()
                })
                if ($(datas).attr('name') == "updatename") {
                    $('#biaBsBsSave').text("Update");
                    globalBSBSId = $(datas).attr('updateId');
                    $("#biaBSBSDescription").val($(datas).attr('description') == "NA" ? "" : $(datas).attr('description'))
                    $("#biaBsBusinessService").val($(datas).attr('Bs-Bs')).trigger('change')
                    $("#biaBsImpact").val($(datas).attr('bs_impact')).trigger('change')
                    setTimeout(() => {
                        $("#biaBsInfraComponent").val($(datas).attr('Bs_Bs1').split(",")).trigger('change')
                    }, 500)
                    $("#biaBsDate").val($(datas).attr('date')).trigger('change')
                }
            } else {
                errorNotification(result)
            }
        },
    })
}

$('#biaTreeBS').on("change", function () {
    let businessServiceId = ""
    businessServiceId = $('#biaTreeBS option:selected').val()
    treeFunction(businessServiceId)
})
async function treeFunction(ServiceId) {
    if (ServiceId != "") {
        await $.ajax({
            url: RootUrl + biaUrls?.GetBIABusinessServiceTreeViewListByBusinessServiceId,
            data: {
                businessServiceId: ServiceId
            },
            dataType: "json",
            traditional: true,
            type: 'GET',
            success: function (data) {
                if (data?.success) {
                    $(".biaTreeWrapper").empty()
                    if (data == "") {
                        $(".biaTreeWrapper").css('text-align', 'center').html(image);
                    }
                    else {
                        treeInfraBf = data?.data
                        newJsonCreate(data?.data, "")
                    }
                } else {
                    if (data.success == false) {
                        $("#biaInfraBs").val("").trigger("change")
                    }
                    errorNotification(data)
                }
            },
        })
    }
}
