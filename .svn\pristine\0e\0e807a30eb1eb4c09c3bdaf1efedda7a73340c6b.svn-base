﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/dashboard.css" rel="stylesheet" />
@Html.AntiForgeryToken()

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title"><i class="cp-monitoring"></i><span title="ORACLE DATAGUARD MONITORING">Oracle Dataguard Detail Monitoring : <span id="infraName"></span></span></h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>
    </div>
    <div id="noDataimg" class="monitor_pages">
        <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row mb-2 g-2 mt-0">
            <div class="col-5">
                <div class="card Card_Design_None h-100">
                    <div class="card-header card-title" style="font-size:15px" title="Database Details">Database Details</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Database Details">Database Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Server Name"><i class="text-secondary cp-server me-1 fs-6"></i>Server Name</td>
                                    <td class="text-truncate"><i class="cp-stand-server me-1 text-primary"></i><span id="PR_Server_Name"></span></td>
                                    <td class="text-truncate"><i class="cp-stand-server me-1 text-primary"></i><span id="DR_Server_Name"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database SID"><i class="text-secondary cp-database me-1 fs-6"></i>Database SID</td>
                                    <td class="text-truncate"><i class="cp-database me-1 text-primary"></i><span id="PR_Database_Sid"></span></td>
                                    <td class="text-truncate"><i class="cp-database me-1 text-primary"></i><span id="DR_Database_Sid"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Unique Name"><i class="text-secondary cp-fal-server me-1 fs-6"></i>Database Unique Name</td>
                                    <td class="text-truncate"><span id="PR_Unique_Name"></span></td>
                                    <td class="text-truncate"><span id="DR_Unique_Name"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Created Time"><i class="text-secondary cp-database-time me-1 fs-6"></i>Database Created Time</td>
                                    <td class="text-truncate"><span id="PR_Database_createdtime"></span></td>
                                    <td class="text-truncate"><span id="DR_Database_createdtime"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Databse Version"><i class="text-secondary cp-hcl me-1 fs-6"></i>Database Version</td>
                                    <td class="text-truncate"><span id="PR_Database_version"></span></td>
                                    <td class="text-truncate"><span id="DR_Database_version"></span></td>
                                </tr>
                            </tbody>
                            @* <tfoot>
                            <tr>
                            <td class="fw-semibold text-truncate" ><i class="cp-hcl me-1 text-primary fs-6"></i><span id="prdbVersion"></span></td>
                            <td class="text-truncate"><i class="cp-table-date me-1 ]]"></i><span id="dbCreatetime"></span></td>
                            <td class="text-truncate"><i class="cp-hcl me-1 "></i><span id="drdbVersion"></span></td>
                            </tr>
                            </tfoot>*@
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-7 d-grid">
                <div class="card Card_Design_None h-100">
                    <div class="card-header card-title" style="font-size:15px" title="Solution Diagram">Solution Diagram</div>
                    <div class="card-body text-center align-items-center justify-content-center w-100 h-100" id="Solution_Diagram" >
                        @* <img src="~/img/isomatric/solutiondiagram.svg" height="159px;" /> *@
                        @* <div id="SolutionDiagramChart" style="width:100%; height:100%;"></div> *@
                    </div>
                </div>
            </div>
        </div>


        <div class="row g-2 mb-2">
            <div class="col-4 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title" style="font-size:15px" title="Database Mode">Database Mode</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Database Mode">Database Mode</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Role"><i class="text-secondary cp-database-role me-1 fs-6"></i>Database Role</td>
                                    <td class="text-truncate"><span id="PR_Database_role"></span></td>
                                    <td class="text-truncate"><span id="DR_Database_role"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Open Mode"><i class="text-secondary cp-open-mode me-1 fs-6"></i>Open Mode</td>
                                    <td class="text-truncate"><span id="PR_Openmode"></span></td>
                                    <td class="text-truncate"><span id="DR_Openmode"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Reset Log Mode"><i class="text-secondary cp-refresh me-1 fs-6"></i>Reset Log Mode</td>
                                    <td class="text-truncate"><span id="PR_Reset_logsmode"></span></td>
                                    <td class="text-truncate"><span id="DR_Reset_logsmode"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Archive Mode"><i class="text-secondary cp-archive-mode me-1 fs-6"></i>Archive Mode</td>
                                    <td class="text-truncate"><span id="PR_Archive_mode"></span></td>
                                    <td class="text-truncate"><span id="DR_Archive_mode"></span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title" style="font-size:15px" title="Instance Details">Instance Details</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Instance Details">Instance Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Instance Name"><span><i class="text-secondary cp-instance-name me-1 fs-6"></i>Instance Name</span></td>
                                    <td class="text-truncate"><span id="PR_InstanceName"></span></td>
                                    <td class="text-truncate"><span id="DR_InstanceName"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Instance ID"><span><i class="text-secondary cp-instance-id me-1 fs-6"></i>Instance ID</span></td>
                                    <td class="text-truncate"><span id="PR_InstanceId"></span></td>
                                    <td class="text-truncate"><span id="DR_InstanceId"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Instance Startup Time"><span class="text-truncate d-inline-block"><i class="text-secondary cp-estimated-time me-1 fs-6"></i>Instance Startup Time</span> </td>
                                    <td class="text-truncate"><span id="PR_InstanceStartUpTime"></span></td>
                                    <td class="text-truncate"><span id="DR_InstanceStartUpTime"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Open Mode"><span class="text-truncate d-inline-block"><i class="text-secondary cp-open-mode me-1 fs-6"></i>Open Mode</span> </td>
                                    <td class="text-truncate"><span id="PR_OpenMode"></span></td>
                                    <td class="text-truncate"><span id="DR_OpenMode"></span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title" style="font-size:15px" title="Database Size">Database Size</div>
                    <div class="card-body d-flex pt-0 align-items-center gap-4 justify-content-center">
                        <div>
                            <i class="cp-database-sizes text-light" style="font-size: 5.9rem;"></i>
                        </div>
                        <div class="d-grid  border-start border-3">
                            <div class="text-primary ms-2 fw-semibold" title="Primary">Primary</div>
                            <span class="text-secondary mb-1 ms-2" title="Database Size">Database Size</span>
                            <h6 class="mb-0 fw-bold ms-2" id="PR_Dbsize"></h6>
                        </div>
                        @*<div class="w-50" id="DatabaseSize"></div>*@
                        <div>
                            <div class="d-grid">
                                <div class="text-info ms-2 fw-semibold dynamicSite-header" title="DR">DR</div>
                                <span class="text-secondary mb-1 ms-2" title="Database Size">Database Size</span>
                                <h6 class="mb-0 fw-bold ms-2" id="DR_Dbsize"></h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row g-2 mb-2">

            <div class="col-8 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title" style="font-size:15px" title="Replication Archive Details">Replication Archive Details</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Replication Details">Replication Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Archiver"><i class="text-secondary cp-instant-name me-1 fs-6"></i>Archiver</td>
                                    <td class="text-truncate">@* <i class="text-primary cp-instant-name me-1 fs-6"></i> *@<span id="PR_Archiver"></span></td>
                                    <td class="text-truncate">@* <i class="text-primary cp-instant-name me-1 fs-6"></i> *@<span id="DR_Archiver"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Archiver Destination Location"><i class="text-secondary cp-location me-1 fs-6"></i>Archiver Destination Location</td>
                                    <td class="text-truncate"><span class="text-truncate d-inline-block" id="PR_Archive_Dest_Location"></span></td>
                                    <td class="text-truncate"><span class="text-truncate d-inline-block" id="DR_Archive_Dest_Location"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Archive Log Compression"><i class="text-secondary cp-zip-file me-1 fs-6"></i>Archive Log Compression</td>
                                    <td class="text-truncate"><span id="PR_Archivelog_compression"></span></td>
                                    <td class="text-truncate"><span id="DR_Archivelog_compression"></span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="d-flex gap-2">
                    <div class="card Card_Design_None w-50 mb-2">
                        <div class="card-body">
                            <i class="cp-log-sequence text-primary fs-4 me-2 bg-body-secondary p-1 rounded-2" title="Log Sequence"></i><span title="Log Sequence" class="fw-semibold">Log Sequence</span>
                            <div class=" mt-3">
                                <div class="w-50 d-grid mb-2">
                                    <small class="text-primary fs-7 mb-1 fw-semibold" title="Primary">Primary</small>
                                    <h6 class="mb-0  fs-7" id="PR_Log_sequence"></h6>
                                </div>
                                <div class="w-50 d-grid">
                                    <small class="text-info fs-7 mb-1 fw-semibold dynamicSite-header" title="DR">DR</small>
                                    <h6 class="mb-0 fs-7 d-inline-block" id="DR_Log_sequence"></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card Card_Design_None w-50 mb-2">
                        <div class="card-body">
                            <i class="cp-current-scn text-primary fs-4 me-2 bg-body-secondary p-1 rounded-2" title="Current SCN"></i><span title="Current SCN" class="fw-semibold">Current SCN</span>
                            <div class=" mt-3">
                                <div class="w-50 d-grid mb-2">

                                    <small class="text-primary fs-7 mb-1 fw-semibold" title="Primary">Primary</small>
                                    <h6 class="mb-0  fs-7" id="PR_Currentscn"></h6>

                                </div>
                                <div class="w-50 d-grid">
                                    <small class="text-info fs-7 mb-1 fw-semibold dynamicSite-header" title="DR">DR</small>
                                    <h6 class="mb-0 fs-7" id="DR_Currentscn"></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card Card_Design_None mb-0">
                    @* <div class="card-header card-title" style="font-size:15px" title="TNS Service Details">TNS Service Details</div> *@
                    <div class="card-body">
                        <i class="cp-current-scn text-primary fs-4 me-2 bg-body-secondary p-1 rounded-2" title="TNS Service Details"></i><span title="TNS Service Details" class="fw-semibold">TNS Service Details</span>
                        <div class="d-flex mt-3">
                            <div class="w-50 d-grid mb-2">
                                <span class="fw-semibold text-primary mb-1">Primary</span>
                                <h6 class="mb-0 fs-7 d-inline-block" id="PR_TNSServiceName"></h6>
                            </div>
                            <div class="w-50 d-grid mb-2">
                                <span class="fw-semibold text-info mb-1 dynamicSite-header">DR</span>
                                <h6 class="mb-0 fs-7  d-inline-block" id="DR_TNSServiceName"></h6>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="row g-2 mb-2">
            <div class="col-6">
                <div class="card Card_Design_None h-100">
                    <div class="card-header card-title" style="font-size:15px" title="Database Info">Database Info</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Database Details">Database Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Control File Type"><span><i class="text-secondary cp-control-file-type me-1 fs-6"></i>Control File Type</span></td>
                                    <td class="text-truncate"><span id="PR_Control_filetype"></span></td>
                                    <td class="text-truncate"><span id="DR_Control_filetype"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Control File Name"><span><i class="text-secondary cp-control-file-name me-1 fs-6"></i>Control File Name</span></td>
                                    <td class="text-truncate"><span id="PR_Control_filename"></span></td>
                                    <td class="text-truncate"><span id="DR_Control_filename"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="CLUSTER Database"><span><i class="text-secondary cp-cluster-database me-1 fs-6"></i>CLUSTER Database</span></td>
                                    <td class="text-truncate"><span id="PR_IsClusterDatabase"></span></td>
                                    <td class="text-truncate"><span id="DR_IsClusterDatabase"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Reset Log Change"><span><i class="text-secondary cp-reset-log-change me-1 fs-6"></i>Reset Log Change</span></td>
                                    <td class="text-truncate"><span id="PR_DB_Reset_logschange"></span></td>
                                    <td class="text-truncate"><span id="DR_DB_Reset_logschange"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Parameter File"><span><i class="text-secondary cp-parameter-file me-1 fs-6"></i>Parameter File</span></td>
                                    <td class="text-truncate"><span id="PR_ParameterFile"></span></td>
                                    <td class="text-truncate"><span id="DR_ParameterFile"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Platform Name"><span><i class="text-secondary cp-platform-name me-1 fs-6"></i>Platform Name</span></td>
                                    <td class="text-truncate"><span id="PR_Platform_name"></span></td>
                                    <td class="text-truncate"><span id="DR_Platform_name"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Incarnation"><span><i class="text-secondary cp-database me-1 fs-6"></i>Database Incarnation</span></td>
                                    <td class="text-truncate"><span id="PR_Database_incarnation"></span></td>
                                    <td class="text-truncate"><span id="DR_Database_incarnation"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DB Create File Dest"><span><i class="text-secondary cp-database-page me-1 fs-6"></i>DB Create File Dest</span></td>
                                    <td class="text-truncate"><span id="PR_Db_create_file_dest"></span></td>
                                    <td class="text-truncate"><span id="DR_Db_create_file_dest"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DB Create Online LogDest"><span><i class="text-secondary cp-db-create-online me-1 fs-6"></i>DB Create Online LogDest</span></td>
                                    <td class="text-truncate"><span id="PR_Db_create_online_log_dest1"></span></td>
                                    <td class="text-truncate"><span id="DR_Db_create_online_log_dest1"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DB Recovery File Dest"><span><i class="text-secondary cp-file-location me-1 fs-6"></i>DB Recovery File Dest</span></td>
                                    <td class="text-truncate"><span id="PR_Db_recovery_file_dest"></span></td>
                                    <td class="text-truncate"><span id="DR_Db_recovery_file_dest"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DB Recovery File Dest Size"><span><i class="text-secondary cp-roate-settings me-1 fs-6"></i>DB Recovery File Dest Size</span></td>
                                    <td class="text-truncate"><span id="PR_Db_recovery_file_dest_size"></span></td>
                                    <td class="text-truncate"><span id="DR_Db_recovery_file_dest_size"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DB File name Convert"><span><i class="text-secondary cp-generate me-1 fs-6"></i>DB File name Convert</span></td>
                                    <td class="text-truncate"><span id="PR_Db_file_name_convert"></span></td>
                                    <td class="text-truncate"><span id="DR_Db_file_name_convert"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Log File Name Convert"><span><i class="text-secondary cp-log-file-name me-1 fs-6"></i>Log File Name Convert</span></td>
                                    <td class="text-truncate"><span id="PR_Log_file_name_convert"></span></td>
                                    <td class="text-truncate"><span id="DR_Log_file_name_convert"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Flashback_ON"><span><i class="text-secondary cp-warning me-1 fs-6"></i>Flashback_ON</span></td>
                                    <td class="text-truncate"><span id="PR_Flashback_on"></span></td>
                                    <td class="text-truncate"><span id="DR_Flashback_on"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Flashback Retention Target"><span><i class="text-secondary cp-warning me-1 fs-6"></i>Flashback Retention Target</span></td>
                                    <td class="text-truncate"><span id="PR_Db_flashback_retention_target"></span></td>
                                    <td class="text-truncate"><span id="DR_Db_flashback_retention_target"></span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="card Card_Design_None h-100">
                    <div class="card-header card-title" style="font-size:15px" title="Replication Info">Replication Info</div>
                    <div class="card-body pt-0 p-2" style="height:calc(100vh - 500px);overflow:auto">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead class="position-sticky top-0 z-3">
                                <tr>
                                    <th title="Replication Details">Replication Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Active DG Enabled"><i class="text-secondary cp-active-dg-enable me-1 fs-6" title="Active DG Enabled"></i>Active DG Enabled</td>
                                    <td class="text-truncate"><span id="PR_Active_DG_Enabled"></span></td>
                                    <td class="text-truncate"><span id="DR_Active_DG_Enabled"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Affirm"><i class="text-secondary cp-affirm me-1 fs-6"></i>Affirm</td>
                                    <td class="text-truncate"><span id="PR_Affirm"></span></td>
                                    <td class="text-truncate"><span id="DR_Affirm"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Remote Login Password File"><i class="text-secondary cp-remote-login me-1 fs-6"></i>Remote Login Password File</td>
                                    <td class="text-truncate"><span id="PR_Remote_login_passwordfile"></span></td>
                                    <td class="text-truncate"><span id="DR_Remote_login_passwordfile"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Protection Mode"><i class="text-secondary cp-protection-mode me-1 fs-6"></i>Protection Mode</td>
                                    <td class="text-truncate"><span id="PR_Protection_mode"></span></td>
                                    <td class="text-truncate"><span id="DR_Protection_mode"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Transmit Mode"><i class="text-secondary cp-right-left me-1 fs-6"></i>Transmit Mode</td>
                                    <td class="text-truncate"><span id="PR_Transmit_mode"></span></td>
                                    <td class="text-truncate"><span id="DR_Transmit_mode"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Recovery Mode"><i class="text-secondary cp-time me-1 fs-6"></i>Recovery Mode</td>
                                    <td class="text-truncate"><span id="PR_Recovery_mode"></span></td>
                                    <td class="text-truncate"><span id="DR_Recovery_mode"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-refresh-clock me-1 fs-6"></i>Recovery Status</td>
                                    <td class="text-truncate"><span id="PR_Recovery_Status"></span></td>
                                    <td class="text-truncate"><span id="DR_Recovery_Status"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Delay Mins"><i class="text-secondary cp-time-one me-1 fs-6"></i>Delay Mins</td>
                                    <td class="text-truncate"><span id="PR_Delay_mins"></span></td>
                                    <td class="text-truncate"><span id="DR_Delay_mins"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DG_Broker_Status"><i class="text-secondary cp-active-dg-enable me-1 fs-6"></i>DG_Broker_Status</td>
                                    <td class="text-truncate"><span id="PR_Dg_broker_status"></span></td>
                                    <td class="text-truncate"><span id="DR_Dg_broker_status"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DataGuard Status"><i class="text-secondary cp-dataguard-status me-1 fs-6"></i>DataGuard Status</td>
                                    <td class="text-truncate"><span id="PR_Dataguard_status"></span></td>
                                    <td class="text-truncate"><span id="DR_Dataguard_status"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Switchover Status"><i class="text-secondary cp-switch-over me-1 fs-6"></i>Switchover Status</td>
                                    <td class="text-truncate"><span id="PR_Switchover_status"></span></td>
                                    <td class="text-truncate"><span id="DR_Switchover_status"></span></td>
                                </tr>
                                @*  <tr>
                                <td class="fw-semibold text-truncate"><i class="text-secondary cp-logs me-1 fs-6"></i>Log Sequence</td>
                                <td class="text-truncate"><span id=""></span></td>
                                <td class="text-truncate"><span id=""></span></td>
                                </tr>  *@
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-configure-settings me-1 fs-6"></i>Log Archive Config</td>
                                    <td class="text-truncate"><span id="PR_Log_archive_config"></span></td>
                                    <td class="text-truncate"><span id="DR_Log_archive_config"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-left-right me-1 fs-6"></i>Force Logging</td>
                                    <td class="text-truncate"><span id="PR_Force_logging"></span></td>
                                    <td class="text-truncate"><span id="DR_Force_logging"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-fal-server me-1 fs-6"></i>FAL Server</td>
                                    <td class="text-truncate">@* <i class="text-primary cp-fal-server me-1 fs-6"></i> *@<span id="PR_Fal_server"></span></td>
                                    <td class="text-truncate">@* <i class="text-primary cp-fal-server me-1 fs-6"></i> *@<span id="DR_Fal_server"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-fal-client me-1 fs-6"></i>FAL Client</td>
                                    <td class="text-truncate">@* <i class="text-primary cp-fal-client me-1 fs-6"></i> *@<span id="PR_Fal_client"></span></td>
                                    <td class="text-truncate">@* <i class="text-primary cp-fal-client me-1 fs-6"></i> *@<span id="DR_Fal_client"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-standby-file me-1 fs-6"></i>Standby File Management</td>
                                    <td class="text-truncate"><span id="PR_Standby_file_management"></span></td>
                                    <td class="text-truncate"><span id="DR_Standby_file_management"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-standby-redo-logs me-1 fs-6"></i>Standby_Logfile_Count  </td>
                                    <td class="text-truncate"><span id="PR_Standby_log_FileCount"></span></td>
                                    <td class="text-truncate"><span id="DR_Standby_log_FileCount"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-standby-redo-logs me-1 fs-6"></i>Standby REDO Logs</td>
                                    <td class="text-truncate"><span id="PR_Standby_redo_logs"></span></td>
                                    <td class="text-truncate"><span id="DR_Standby_redo_logs"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-standby-redo-logs me-1 fs-6"></i>Transport Lag</td>
                                    <td class="text-truncate"><span id="PR_Transport_lag"></span></td>
                                    <td class="text-truncate"><span id="DR_Transport_lag"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-apply-lag me-1 fs-6"></i>Apply Lag</td>
                                    <td class="text-truncate"><span id="PR_Apply_lag"></span></td>
                                    <td class="text-truncate"><span id="DR_Apply_lag"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-time me-1 fs-6"></i>Apply Finish Time</td>
                                    <td class="text-truncate"><span id="PR_Apply_finish_time"></span></td>
                                    <td class="text-truncate"><span id="DR_Apply_finish_time"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-time me-1 fs-6"></i>Estimated Startup Time</td>
                                    <td class="text-truncate"><span id="PR_Estimated_startup_time"></span></td>
                                    <td class="text-truncate"><span id="DR_Estimated_startup_time"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-apply-lag me-1 fs-6"></i>DataLag (HH:MM )</td>
                                    <td class="text-truncate"> <span id="PR_Datalag"></span></td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="row g-2">
            <div class="col-12 d-grid">
                @* <div class="col-6 d-grid">*@
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title" style="font-size:15px" title="Multi Tenancy">Multi Tenancy</div>
                    <div class="card-body pt-0">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Multi Tenancy">Multi Tenancy</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                @* <tr>
                                <td class="fw-semibold text-truncate" title="Components(Primary/DR)"><i class="text-secondary cp-name me-1 fs-6"></i>Components(Primary/DR)</td>
                                <td class="text-truncate"><span><i class="text-danger cp-disable me-1 fs-6"></i>NA</span></td>
                                <td class="text-truncate"><span><i class="text-danger cp-disable me-1 fs-6"></i>NA</span></td>
                                </tr>  *@
                                <tr>
                                    <td class="fw-semibold text-truncate" title="CDB"><i class="text-secondary cp-name me-1 fs-6"></i>CDB</td>
                                    <td class="text-truncate"><span id="PR_CDB"></span></td>
                                    <td class="text-truncate"><span id="DR_CDB"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Containers"><i class="text-secondary cp-synbase-backup-server me-1 fs-6"></i>Containers</td>
                                    <td class="text-truncate"><span id="PR_Containers"></span></td>
                                    <td class="text-truncate"><span id="DR_Containers"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="PDBs"><i class="text-secondary cp-database me-1 fs-6"></i>PDBs</td>
                                    <td class="text-truncate"><span id="PR_Pdbs"></span></td>
                                    <td class="text-truncate"><span id="DR_Pdbs"></span></td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>



            </div>
            <div class="col-12 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title" title="ASM Details">ASM Details </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th title="ASM">ASM</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Services">Services</td>
                                    <td class="text-truncate">
                                        <table class="table mb-0" id="asmPrimaryData" style="table-layout:fixed">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th title="Name">Name</th>
                                                    <th title="State">State</th>
                                                    <th title="Type">Type</th>
                                                    <th title="Total MB">Total MB</th>
                                                    <th title="Fee MB">Free MB</th>
                                                    <th title="Used(%)">Used(%)</th>
                                                </tr>
                                            </thead>
                                            <tbody id="prASMData">
                                            </tbody>
                                        </table>
                                    </td>
                                    <td class="text-truncate">
                                        <table class="table mb-0" id="asmDRData" style="table-layout:fixed">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th title="Name">Name</th>
                                                    <th title="State">State</th>
                                                    <th title="Type">Type</th>
                                                    <th title="Total MB">Total MB</th>
                                                    <th title="Fee MB">Free MB</th>
                                                    <th title="Used(%)">Used(%)</th>
                                                </tr>
                                            </thead>
                                            <tbody id="drASMData">
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @*   <div class="col-6 d-grid">
            <div class="card Card_Design_None mb-0">
            <div class="card-header card-title" style="font-size:15px" title="ASM Details">ASM Details</div>
            <div class="card-body pt-0">
            <div class="NoData text-center p-2 d-grid justify-content-center">
            <img src="~/img/isomatric/nodatalag.svg" class="mx-auto" />
            <span class="text-danger">
            Asm details not available
            </span>
            </div>
            </div>
            </div>
            </div> *@

            <div class="col-12 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title" style="font-size:15px" title="Pluggable Databases">Pluggable Databases</div>

                    <div class="card-body pt-0" id="noPluggableimg">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Replication Details">Replication Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="PDB Name"><i class="text-secondary cp-replication-connect me-1 fs-6"></i>PDB Name</td>
                                    <td class="text-truncate" id="PR_PDB_Name"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td>
                                    <td class="text-truncate" id="DR_PDB_Name"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="CONNECTION ID"><i class="text-secondary cp-name me-1 fs-6"></i>CONNECTION ID</td>
                                    <td class="text-truncate" id="PR_CONNECTION_ID"><span><i class="text-success cp-circle-switch me-1 fs-6"></i></span></td>
                                    <td class="text-truncate" id="DR_CONNECTION_ID"><span><i class="text-success cp-circle-switch me-1 fs-6"></i></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="PDB ID"><i class="text-secondary cp-replication-connect me-1 fs-6"></i>PDB ID</td>
                                    <td class="text-truncate" id="PR_PDB_ID"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td>
                                    <td class="text-truncate" id="DR_PDB_ID"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="PDB MODE"><i class="text-secondary cp-replication-connect me-1 fs-6"></i>PDB MODE</td>
                                    <td class="text-truncate" id="PR_PDB_MODE"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td>
                                    <td class="text-truncate" id="DR_PDB_MODE"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="LOGGING"><i class="text-secondary cp-log-file-name me-1 fs-6"></i>LOGGING</td>
                                    <td class="text-truncate" id="PR_LOGGING"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td>
                                    <td class="text-truncate" id="DR_LOGGING"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="FORCE_LOGGING"><i class="text-secondary cp-log-file-name me-1 fs-6"></i>FORCE_LOGGING</td>
                                    <td class="text-truncate" id="PR_FORCE_LOGGING"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td>
                                    <td class="text-truncate" id="DR_FORCE_LOGGING"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="RECOVERY_STATUS"><i class="text-secondary cp-time me-1 fs-6"></i>RECOVERY_STATUS</td>
                                    <td class="text-truncate" id="PR_RECOVERY_STATUS"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td>
                                    <td class="text-truncate" id="DR_RECOVERY_STATUS"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="PDB SIZE"><i class="text-secondary cp-replication-connect me-1 fs-6"></i>PDB SIZE</td>
                                    <td class="text-truncate" id="PR_PDB_SIZE"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td>
                                    <td class="text-truncate" id="DR_PDB_SIZE"><span><i class="text-danger cp-disable me-1 fs-6"></i></span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-12 d-grid">
                <div class="card Card_Design_None mb-0" id="mssqlserver">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span title=" Services ">
                            Services
                        </span>

                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" id="tableCluster" style="table-layout:fixed">
                            <thead class="align-middle">
                                <tr>
                                    <th rowspan="2">Service / Process / Workflow Name</th>
                                    <th colspan="2" class="text-center">Server IP/HostName</th>
                                </tr>
                                <tr>
                                    <th id="prIp"></th>
                                    <th id="drIp"></th>
                                </tr>
                            </thead>
                            @* <thead>
                                <tr>
                                    <th title="Service / Process / Workflow Name">Service / Process / Workflow Name</th>
                                    <th class="text-primary" title="Server IP/HostName">Server IP/HostName</th>
                                    <th class="">Status</th>
                                </tr>
                            </thead> *@
                            <tbody id="mssqlserverbody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card Card_Design_None">
                    <div class="card-header card-title d-flex align-items-center justify-content-between mb-2">
                        <span class="text-truncate d-inline-block" title="Archive Log Generation Hourly Last 24Hrs(Count)">
                            Archive Log Generation
                            Hourly Last 24Hrs(Count)
                        </span>
                    </div>
                    <div class="card-body pt-0">
                        <div id="ArchiveLogHour" style="width:80%; height:250px;"></div>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card Card_Design_None">
                    <div class="card-header card-title d-flex align-items-center justify-content-between mb-2">
                        <span class="text-truncate d-inline-block" title="Archive Log Generation Last 24Hrs(Size)">
                            Archive Log Generation
                            Last 24Hrs(Size)
                        </span>

                    </div>
                    <div class="card-body pt-0">
                        <div id="ArchiveLogDay" style="width:80%; height:250px;"></div>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card Card_Design_None">
                    <div class="card-header card-title d-flex align-items-center justify-content-between mb-2">
                        <span class="text-truncate d-inline-block" title="Archive Log Generation Weekly (Size)">
                            Archive Log Generation Weekly (Size)
                        </span>
                    </div>
                    <div class="card-body pt-0">
                        <div id="ArchiveLogWeek" style="width:100%; height:250px;"></div>
                    </div>
                </div>
            </div>

        </div>

    </div>
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
@* <script src="~/js/monitoring/oracledatagaurd.js"></script> *@
<script src="~/js/Monitoring/ArchiveLog.js"></script>
<script src="~/js/Monitoring/MonitoringOracleDataguard.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>
