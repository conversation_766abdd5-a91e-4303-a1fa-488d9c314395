﻿using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;
using ContinuityPatrol.Shared.Infrastructure.Extensions;

namespace ContinuityPatrol.Infrastructure.Impl;

public class SmsService : ISmsService
{
    private readonly ILogger<SmsService> _logger;

    public SmsService(ILogger<SmsService> logger)
    {
        _logger = logger;
    }

    public async Task<bool> SendSmsAsync(SmsMessage sms)
    {
        var text = "<Root>\n   <ChnlId>" + sms.UserName + "</ChnlId>\n   <Key>" + sms.Password + "</Key>\n       <Row>\n           <RefId>" + sms.RecipientNo + "</RefId>\n           <MobNo>" + sms.ToMobileNo + "</MobNo>\n           <Msg>" + sms.Message + "</Msg>\n       </Row>\n   </Root>";

        try
        {
            _logger.LogInformation($"Sending SMS to: {sms.ToMobileNo}, RefId: {sms.RecipientNo}" );

            // ServicePointManager.ServerCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;

            var options = new RestClientOptions(sms.Url)
            {
                RemoteCertificateValidationCallback = (_, _, _, _) => true
            };

            var client = new RestClient(options);

            var request = new RestRequest()
                .AddStringBody(text, DataFormat.Xml);

            var response = await client.PostAsync(request);

            if (response is { IsSuccessful: true })
            {
                _logger.LogInformation($"SMS sent successfully to {sms.ToMobileNo}");

                return true;
            }

            _logger.LogWarning($"SMS failed for {sms.ToMobileNo}. Status: {response.StatusCode}, Content: {response.Content}");
        }
        catch (Exception ex)
        {
            _logger.Exception($"Exception occurred while sending SMS to {sms.ToMobileNo}", ex);

            return false;
        }

        return true;
    }
}