﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Form.Events.Delete;

public class FormDeletedEventHandler : INotificationHandler<FormDeletedEvent>
{
    private readonly ILogger<FormDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FormDeletedEventHandler(ILoggedInUserService userService, ILogger<FormDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(FormDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var result = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.Form}",
            Entity = Modules.Form.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"Form '{deletedEvent.FormName}' deleted successfully."
        };
        await _userActivityRepository.AddAsync(result);

        _logger.LogInformation($"Form '{deletedEvent.FormName}' deleted successfully.");
    }
}