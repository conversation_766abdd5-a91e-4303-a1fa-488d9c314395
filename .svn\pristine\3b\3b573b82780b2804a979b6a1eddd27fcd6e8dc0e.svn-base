﻿
function monitorTypeAlwaysOn(value, infraObjectName, moniterType, parsedData) {

    //let prWfName = [], drWfName = [];    
    //let prStatusArr = [], drStatusArr = [];
    //let prWfDisplay = '--', drWfDisplay = '--';
    //let prStatusDisplay = '--', drStatusDisplay = '--';
    //let iconWF = '', iconStatus = '';
    let monitor = value?.monitorServiceDetail
    let drStatus = value?.drServerStatus
    //if (monitor?.length) {
    //    monitor?.forEach(list => {
    //        let parsed = [];
    //        const isValidJson = list?.isServiceUpdate && list?.isServiceUpdate.trim().startsWith('[');

    //        if (isValidJson) {
    //            try {
    //                parsed = JSON?.parse(list?.isServiceUpdate);
    //            } catch (err) {
    //                console.warn('Invalid JSON in isServiceUpdate:', list?.isServiceUpdate);
    //                parsed = [];
    //            }
    //        }
    //        parsed?.forEach(entry => {
    //            entry?.Services?.forEach(service => {
    //                if (entry?.Type?.toLowerCase() === 'pr') {
    //                    if (service?.ServiceName && service?.ServiceName !== 'NA') {
    //                        prWfName.push(service?.ServiceName);
    //                    }
    //                    if (service?.Status && service?.Status !== 'NA') {
    //                        prStatusArr.push(service?.Status);
    //                    }
    //                } else if (entry?.Type?.toLowerCase() === 'dr') {
    //                    if (service?.ServiceName && service?.ServiceName !== 'NA') {
    //                        drWfName.push(service?.ServiceName);
    //                    }
    //                    if (service?.Status && service?.Status !== 'NA') {
    //                        drStatusArr.push(service?.Status);
    //                    }
    //                }
    //            });
    //        });
    //    });

    //    // Unique workflow names
    //    prWfDisplay = prWfName?.length > 0 ? [...new Set(prWfName)].join(', ') : '--';
    //    drWfDisplay = drWfName?.length > 0 ? [...new Set(drWfName)].join(', ') : '--';

    //    // Status summary
    //    function getStatusSummary(arr) {
    //        let countMap = {};
    //        arr?.forEach(status => {
    //            countMap[status] = (countMap[status] || 0) + 1;
    //        });
    //        let total = arr?.length;
    //        let statusSummary = Object.entries(countMap)
    //            .map(([status, count]) => `${count} ${status}`)
    //            .join(', ');
    //        return statusSummary ? `${statusSummary} / ${total}` : '--';
    //    }

    //    prStatusDisplay = getStatusSummary(prStatusArr);
    //    drStatusDisplay = getStatusSummary(drStatusArr);

    //    iconWF = (prWfDisplay !== '--' || drWfDisplay !== '--') ? '<i class="text-primary cp-monitoring-services me-1 fs-6"></i>' : '';

    //    iconStatus = (prStatusDisplay !== '--' || drStatusDisplay !== '--') ? '<i class="text-primary cp-Job-status me-1 fs-6"></i>' : '';

    //}
    const getDRDetails = (data, value,obj = null) => {

        let tdHtml = '';
        data?.forEach((item, i) => {
            let iconClass = getIconClass(value, item);

            let tableData = obj ? item?.MonitoringModel[obj][value] : item?.MonitoringModel[value];
            let displayData = tableData || 'NA';

            tdHtml += `<td class="text-truncate" title="${displayData}"><i class="${iconClass} me-1"></i>${tableData || 'NA'}</td>`
        })
        return tdHtml
    }

    const getIconClass = (value, monitoringData) => {
        let iconClass = '';

        if (value == 'Server_Edition') {
            iconClass = 'cp-stand-server text-primary'

        } else if (value === 'Server_IpAddress' || value === 'Server_HostName') {
            let text = drStatus?.toLowerCase()
            iconClass = text === 'down' ? "cp-down-linearrow me-1 text-danger" : text === 'pending' ? 'cp-pending me-1 text-warning' : text === 'up' ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";

        } else if (value === 'Server_HostName') {
            let text = monitoringData?.MonitoringModel?.Server_Status?.toLowerCase()
            iconClass = text === 'down' ? "cp-down-linearrow me-1 text-danger" : text === 'pending' ? 'cp-pending me-1 text-warning' : text === 'up' ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";

        } else if (value === 'DataBase_Synchroniztion_State') {
            iconClass =   monitoringData?.MonitoringModel?.DatabaseLevelMonitoring?.DataBase_Synchroniztion_State?.toLowerCase()?.substring(0, 6) === 'asynch' ? "cp-refresh me-1 text-danger" : monitoringData?.MonitoringModel?.DatabaseLevelMonitoring?.DataBase_Synchroniztion_State?.toLowerCase()?.substring(0, 6) === 'synchr' ? "cp-refresh me-1 text-success" : "cp-disable me-1 text-danger";

        } else if (value === 'DataBase_Synchroniztion_Health_Status') {
            iconClass = monitoringData?.MonitoringModel?.DatabaseLevelMonitoring?.DataBase_Synchroniztion_Health_Status?.toLowerCase() === "healthy" ? "cp-health-success me-1 text-success" : monitoringData?.MonitoringModel?.DatabaseLevelMonitoring?.DataBase_Synchroniztion_Health_Status?.toLowerCase() === "unhealthy" ? "cp-health-error me-1 text-danger" : "cp-disable me-1 text-danger";

        } else if (value === 'DataBase_State') {
            iconClass =  monitoringData?.MonitoringModel?.DatabaseLevelMonitoring?.DataBase_State?.toLowerCase() === "online" ? "cp-online me-1 text-success" : monitoringData?.MonitoringModel?.DatabaseLevelMonitoring?.DataBase_State?.toLowerCase() === "offline" ? "cp-offline me-1 text-danger" : "cp-disable me-1 text-danger";

        } else if (value === 'Database_Name') {
            iconClass = monitoringData?.MonitoringModel?.DatabaseLevelMonitoring?.Database_Name ? "cp-database me-1 text-primary" : "cp-disable me-1 text-danger";
        }
        else if (value === 'Availability_Group_Role') {
            iconClass = monitoringData?.MonitoringModel?.AvailabilityGroupMonitoring?.Availability_Group_Role?.toLowerCase() === "primary" ? "text-primary me-1 cp-list-prsite" : monitoringData?.MonitoringModel?.AvailabilityGroupMonitoring?.Availability_Group_Role?.toLowerCase() === "secondary" ? "text-info me-1 cp-dr" : "cp-disable me-1 text-danger";

        } else if (value === 'Availability_Mode') {
            
            iconClass = monitoringData?.MonitoringModel?.AvailabilityGroupMonitoring?.Availability_Mode?.toLowerCase()?.substring(0, 6) === 'asynch' ? "cp-refresh me-1 text-danger" : monitoringData?.MonitoringModel?.AvailabilityGroupMonitoring?.Availability_Mode?.toLowerCase()?.substring(0, 6) === 'synchr' ? "cp-refresh me-1 text-success" : "cp-disable me-1 text-danger";

        } else if (value === 'Availability_Group_Connected_State') {
            
            iconClass = monitoringData?.MonitoringModel?.AvailabilityGroupMonitoring?.Availability_Group_Connected_State?.toLowerCase() === "connected" ? "cp-connected me-1 text-success" : monitoringData?.AvailabilityGroupMonitoring?.Availability_Group_Connected_State?.toLowerCase() === "disconnected" ? "cp-disconnected me-1 text-danger" : "cp-disable me-1 text-danger";
        }

        return iconClass;
    }

    const getDynamicHeader = (AlwaysOnMonitoringModels) => {

        let dynamicHeader = '';

        AlwaysOnMonitoringModels?.length && AlwaysOnMonitoringModels?.map((data) => {
            dynamicHeader += `<th>${data?.Type}</th>`
        })

        return dynamicHeader;
    }

    if (moniterType === "MssqlAlwaysOn") {        
        let prStatus = value?.prServerStatus
        let ipOrHostName;
        let application = value?.monitorServiceDetails[0]?.isServiceUpdate?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.isServiceUpdate?.toLowerCase()?.includes("running") ? "text-success cp-reload cp-animate" : "text-danger cp-fail-back";
        let workflow = value?.monitorServiceDetails[0]?.workflowName?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.workflowName ? "cp-log-archive-config me-1 text-primary" : "cp-disable me-1 text-danger";
        let pripaddress = prStatus?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : prStatus?.toLowerCase() === "pending" ? "cp-pending me-1 text-warning" : prStatus?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";
    
        let prhealthstate = parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_Health_Status?.toLowerCase() === "healthy" ? "cp-health-success me-1 text-success" : parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_Health_Status?.toLowerCase() === "unhealthy" ? "cp-health-error me-1 text-danger" : "cp-disable me-1 text-danger";
       
        let pronlinestate = parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_State?.toLowerCase() === "online" ? "cp-online me-1 text-success" : parsedData?.DatabaseLevelMonitoring?.PR_DataBase_State?.toLowerCase() === "offline" ? "cp-offline me-1 text-danger" : "cp-disable me-1 text-danger";
  
        let prprimarystate = parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Group_Role?.toLowerCase() === "primary" ? "text-primary me-1 cp-list-prsite" : parsedData?.AvailabilityGroupMonitoring?.PR_Availability_Group_Role?.toLowerCase() === "secondary" ? "text-info me-1 cp-dr" : "cp-disable me-1 text-danger";
 
        let prconnectstate =  parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Group_Connnected_State?.toLowerCase() === "connected" ? "cp-connected me-1 text-success" : parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Group_Connnected_State?.toLowerCase() === "disconnected" ? "cp-disconnected me-1 text-danger" : "cp-disable me-1 text-danger";
 
        let prdatabasestate = parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_State?.toLowerCase()?.substring(0, 6) === 'asynch' ? "cp-refresh me-1 text-danger" : parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_State?.toLowerCase()?.substring(0, 6) === 'synchr' ? "cp-refresh me-1 text-success" : "cp-disable me-1 text-danger";
  
        let prreplicationstate = parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Mode?.toLowerCase()?.substring(0, 6) === 'asynch' ? "cp-refresh me-1 text-danger" : parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Mode?.toLowerCase()?.substring(0, 6) === 'synchr' ? "cp-refresh me-1 text-success" : "cp-disable me-1 text-danger";
        let ipprdata = parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.Pr_ConnectViaHostName?.toLowerCase() === "true" ? parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PR_Server_HostName : parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PR_Server_IpAddress
        let drdata = parsedData?.AlwaysOnMonitoringModels.map((ip) => ip?.MonitoringModel?.connectViaHostName);

        parsedData?.AlwaysOnMonitoringModels.forEach((ip, index) => {

            let isHostName = drdata[index]?.toLowerCase() === "true";
            value = isHostName ? 'Server_HostName' : 'Server_IpAddress';
            ipOrHostName = isHostName
                ? getDRDetails(parsedData?.AlwaysOnMonitoringModels, 'Server_HostName')
                : getDRDetails(parsedData?.AlwaysOnMonitoringModels, 'Server_IpAddress');
        });
        let infraobjectdata =
            '<div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
            '<thead ="dynamic_site_header" style="position: sticky;top: 0px;z-index: 1;">' +
            '<tr>' +
            '<th>Component Monitor</th>' +
            '<th>Production Server</th>' +
            `${getDynamicHeader(parsedData?.AlwaysOnMonitoringModels)}` +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td class="text-truncate">' + "MSSQL Server Edition" + '</td>' +
            '<td class="text-truncate" title="' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PR_Server_Edition || "NA") + '">' + '<i class="cp-stand-server me-1 text-primary"></i>' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PR_Server_Edition !== null && parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PR_Server_Edition !== "" ? parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PR_Server_Edition : 'NA') + '</td>' +
            `${getDRDetails(parsedData.AlwaysOnMonitoringModels, 'Server_Edition')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + 'IP Address' + '</td>' +
            '<td>' + '<i class="' + pripaddress + '"></i>' + (ipprdata || 'NA') + '</td>' +
            `${ipOrHostName}` +         
            '</tr>' +
            '<tr>' +
            '<td>' + 'Host Name' + '</td>' +
            '<td title="' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PR_Server_HostName || "NA") + '">' + '<i class="' + pripaddress + '"></i>' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PR_Server_HostName !== null && parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PR_Server_HostName !== "" ? parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PR_Server_HostName : 'NA') + '</td>' +          
            `${getDRDetails(parsedData?.AlwaysOnMonitoringModels, 'Server_HostName')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Database Synchronization State" + '</td>' +
            '<td title="' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_State || "NA") + '">' + '<i class="' + prdatabasestate + '"></i>' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_State !== null && parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_State !== "" ? parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_State : 'NA') + '</td>' +           
            `${getDRDetails(parsedData?.AlwaysOnMonitoringModels, 'DataBase_Synchroniztion_State', 'DatabaseLevelMonitoring')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Synchronization Health Status" + '</td>' +
            '<td title="' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_Health_Status || "NA") + '">' + '<i class="' + prhealthstate + '"></i>' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_Health_Status !== null && parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_Health_Status !== "" ? parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_State : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.AlwaysOnMonitoringModels, 'DataBase_Synchroniztion_Health_Status', 'DatabaseLevelMonitoring')}` +         
            '</tr>' +
            '<tr>' +
            '<td>' + " Database Status" + '</td>' +
            '<td title="' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_State || "NA") + '">' + '<i class="' + pronlinestate + '"></i>' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_State !== null && parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_State !== "" ? parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_DataBase_State : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.AlwaysOnMonitoringModels, 'DataBase_State', 'DatabaseLevelMonitoring')}` +
          
            '</tr>';        
        //if (Array.isArray(monitor) && monitor?.length > 0) {
        //    infraobjectdata +=
        //        '<tr id="prWorkflow">' +
        //        '<td>Monitoring workflow</td>' +
        //        '<td>' + iconWF + prWfDisplay + '</td>' +
        //        '<td>' + iconWF + drWfDisplay + '</td>' +
        //        '</tr>' +
        //        '<tr id="prStatus">' +
        //        '<td>Application Status</td>' +
        //        '<td>' + iconStatus + prStatusDisplay + '</td>' +
        //        '<td>' + iconStatus + drStatusDisplay + '</td>' +
        //        '</tr>';
        //}
        infraobjectdata += generateMonitorServiceDetailsRow(workflow, application, monitor);

        infraobjectdata += '</tbody>' +
            '</table>' +
            '</div>' +
            '<div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
            '<thead style="position: sticky;top: 0px;z-index: 1;">' +
            '<tr>' +
            '<th>Replication Monitor</th>' +
            '<th>Production Server</th>' +
            `${getDynamicHeader(parsedData?.AlwaysOnMonitoringModels)}` +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td>' + "Availability Group Name" + '</td>' +
            '<td title="' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Group_Name || "NA") + '">' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Group_Name !== null && parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Group_Name !== "" ? parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Group_Name : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.AlwaysOnMonitoringModels, 'Availability_Group_Name', 'AvailabilityGroupMonitoring')}` +
          
            '</tr>' +
            '<tr>' +
            '<td>' + "Database Name" + '</td>' +
            '<td title="' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Database_Name || "NA") + '">' + '<i class="cp-database me-1 text-primary"></i>' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_Database_Name !== null && parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_Database_Name !== "" ? parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring?.PR_Database_Name : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.AlwaysOnMonitoringModels, 'Database_Name', 'DatabaseLevelMonitoring')}` +
          
            '</tr>' +
            '<tr>' +
            '<td>' + "Availability Group Role" + '</td>' +
            '<td title="' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Group_Role || "NA") + '">' + '<i class="' + prprimarystate + '"></i>' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Group_Role !== null && parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Group_Role !== "" ? parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Group_Role : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.AlwaysOnMonitoringModels, 'Availability_Group_Role', 'AvailabilityGroupMonitoring')}` +
          
            '</tr>' +
            '<tr>' +
            '<td>' + "Replica Mode" + '</td>' +
            '<td class="text-truncate" title="' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Mode || "NA") + '">' + '<i class="' + prreplicationstate + '"></i>' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Mode !== null && parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Mode !== "" ? parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Mode : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.AlwaysOnMonitoringModels, 'Availability_Mode', 'AvailabilityGroupMonitoring')}` +
           
            '</tr>' +
            '<tr>' +
            '<td>' + "Availability Group Connected State" + '</td>' +
            '<td title="' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Group_Connnected_State || "NA") + '">' + '<i class="' + prconnectstate + '"></i>' + (parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Group_Connnected_State !== null && parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Group_Connnected_State !== "" ? parsedData?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring?.PR_Availability_Group_Connnected_State : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.AlwaysOnMonitoringModels, 'Availability_Group_Connected_State', 'AvailabilityGroupMonitoring')}` +
            
            '</tr>' +
            '</tbody style="">' +
            '</table>' +
            '</div>'



        setTimeout(() => {
            $("#infraobjectalldata").html(infraobjectdata);
        }, 200)


    }
}