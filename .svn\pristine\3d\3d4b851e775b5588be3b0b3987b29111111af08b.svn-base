﻿using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Events.BaseLicenseEvent.Update;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.CommonBaseLicenseUpdate;

public class CommonBaseLicenseUpdateCommandHandler
{
    private readonly ILicenseHistoryRepository _licenseHistoryRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ILogger<UpdateBaseLicenseCommandHandler> _logger;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CommonBaseLicenseUpdateCommandHandler(IMapper mapper, ILicenseManagerRepository licenseManagerRepository,
        IPublisher publisher, ILicenseHistoryRepository licenseHistoryRepository,
        ILoggedInUserService loggedInUserService, ILogger<UpdateBaseLicenseCommandHandler> logger)
    {
        _licenseManagerRepository = licenseManagerRepository;
        _mapper = mapper;
        _publisher = publisher;
        _licenseHistoryRepository = licenseHistoryRepository;
        _loggedInUserService = loggedInUserService;
        _logger = logger;
    }

    public async Task<string> Handle(CommonBaseLicenseUpdateCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _licenseManagerRepository.GetByReferenceIdAsync(request.Id);

        var existLicenseKey = SecurityHelper.Decrypt(eventToUpdate.LicenseKey);

        var updateLicenseKey = SecurityHelper.Decrypt(request.LicenseKey);

        var existLicenseKeyDetail = existLicenseKey.Split('*');

        var updateLicenseKeyDetail = updateLicenseKey.Split('*');

        var expiryDate = string.Empty;

        if (DateTime.Parse(existLicenseKeyDetail[7]).Equals(DateTime.Parse(updateLicenseKeyDetail[7])))
            expiryDate = SecurityHelper.Decrypt(eventToUpdate.ExpiryDate);

        var newRequest = new UpdateBaseLicenseCommand
        {
            Id = request.Id,
            PoNumber = SecurityHelper.Encrypt(updateLicenseKeyDetail[0]),
            CompanyId = _loggedInUserService.CompanyId,
            CompanyName = _loggedInUserService.CompanyName,
            HostName = SecurityHelper.Encrypt(updateLicenseKeyDetail[1]),
            IpAddress = SecurityHelper.Encrypt(updateLicenseKeyDetail[2]),
            MacAddress = SecurityHelper.Encrypt(updateLicenseKeyDetail[3]),
            Properties = SecurityHelper.Encrypt(request.Properties),
            Validity = SecurityHelper.Encrypt(updateLicenseKeyDetail[5]),
            ExpiryDate = SecurityHelper.Encrypt(expiryDate),
            ParentId = string.Empty,
            IsParent = _loggedInUserService.IsParent,
            LicenseKey = request.LicenseKey,
            ParentPoNumber = SecurityHelper.Encrypt("NA"),
            IsState = request.IsState
        };
        _mapper.Map(newRequest, eventToUpdate, typeof(UpdateBaseLicenseCommand),
            typeof(Domain.Entities.LicenseManager));

        await _licenseManagerRepository.UpdateAsync(eventToUpdate);

        await _licenseHistoryRepository.AddAsync(new Domain.Entities.LicenseHistory
        {
            LicenseId = newRequest.Id,
            PONumber = newRequest.PoNumber,
            CompanyName = newRequest.CompanyName,
            CompanyId = newRequest.CompanyId,
            CPHostName = newRequest.HostName,
            IPAddress = newRequest.IpAddress,
            MACAddress = newRequest.MacAddress,
            Properties = newRequest.Properties,
            Validity = newRequest.Validity,
            ExpiryDate = newRequest.ExpiryDate,
            ParentId = string.Empty,
            IsParent = _loggedInUserService.IsParent,
            LicenseKey = newRequest.LicenseKey,
            UpdaterId = _loggedInUserService.UserId,
            ParentPONumber = SecurityHelper.Encrypt("NA")
        });

        _logger.LogDebug($"Base License id '{newRequest.Id}' upgraded successfully.");

        var response = $"Base License '{updateLicenseKeyDetail[0]}' upgraded Successfully.";

        await _publisher.Publish(new BaseLicenseUpdatedEvent { PONumber = newRequest.PoNumber }, cancellationToken);

        return response;
    }
}