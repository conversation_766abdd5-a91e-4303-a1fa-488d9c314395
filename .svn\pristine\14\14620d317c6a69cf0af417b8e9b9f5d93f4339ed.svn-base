let timelineArray;
let checkService;
let executionURL = {
    "CheckWindowServiceurl": RootUrl + 'ITAutomation/WorkflowExecution/CheckWindowsService',
}
let executeTimeline = 0;
let executeParentIndex = 0;
let bulkImportObject = "";
let validatorResponse = "";
let serverIndex = "";
let databaseIndex = "";
let replicationIndex = "";
let infraObjectIndex = "";
let validationResponse = "";
let intervalSet = false;
let bulkImportSetIntervalOne;
let bulkImportSetIntervalTwo;
let dynamicCardParentIndex = 0;
let dynamicCardChildIndex = 0;
let bulkImportOperationStatus;
let allowInsertToDB = true;
const requiredKeysServer = ["name", "siteName", "businessServiceName", "serverRole", "serverType", "osType", "licenseKey", "osVersion", "formVersion"];
const requiredKeysDatabase = ["name", "databaseType", "businessServiceName", "databaseVersion", "serverName", "licenseKey", "exceptionMessage", "formVersion"];
const requiredKeysReplication = ["name", "siteName", "businessServiceName", "licenseKey", "replicationType", "formVersion"];

//Document ready
$(async function () {
    $("#insertBulkImport").removeClass('d-none');
    $("#CompleteBulkImport").addClass('d-none');
    let html = '';
    const dropzone = document.getElementById('dropzone');
    const fileInput = document.getElementById('file-input');

    dropzone.addEventListener('dragover', (e) => {
        e.preventDefault();
        e.stopPropagation();
    });

    dropzone.addEventListener('drop', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const file = e.dataTransfer.files[0];
        handleFiles(file);
    });

    if (fileInput) {
        fileInput.addEventListener('change', (e) => {
            $("#insertBulkImport").removeClass('d-none');
            $("#CompleteBulkImport").addClass('d-none');
            handleFiles(e.target.files[0]);
        });
    }

    if (performance.navigation.type != performance.navigation.TYPE_RELOAD) {
        localStorage.removeItem("checkMonitorService")
        await $.ajax({
            type: "POST",
            url: executionURL.CheckWindowServiceurl,
            data: { type: 'monitor', __RequestVerificationToken: gettoken() },
            dataType: "json",
            traditional: true,
            success: function (result) {
                checkService = result
                localStorage.setItem("checkMonitorService", JSON.stringify(result))

                if (result?.success) {
                    html = loadActiveInActive(result);
                    notificationAlert("success", html, 'execution')
                } else {
                    if (result?.message != "") {
                        errorNotification(result)
                    } else {
                        html = loadActiveInActive(result);
                        notificationAlert("warning", html, 'execution')
                    }
                }
            }
        })
    } else {
        let getLocalData = JSON.parse(localStorage.getItem("checkMonitorService"))
        checkService = getLocalData
    }

    setupValidation("#PRServerName", "Configuration/Server/IsServerNameExist", "#PRServerNameError", "Enter server name", "serverName");
    setupValidation("#DRServerName", "Configuration/Server/IsServerNameExist", "#DRServerNameError", "Enter server name", "serverName");
    setupValidation("#PRDatabaseName", "Configuration/Database/IsDatabaseNameExist", "#PRDatabaseNameError", "Enter Database name", "databaseName");
    setupValidation("#DRDatabaseName", "Configuration/Database/IsDatabaseNameExist", "#DRDatabaseNameError", "Enter Database name", "databaseName");
    setupValidation("#PRReplicationName", "Configuration/Replication/IsReplicationNameExist", "#PRReplicationNameError", "Enter Replication name", "replicationName");
    setupValidation("#DRReplicationName", "Configuration/Replication/IsReplicationNameExist", "#DRReplicationNameError", "Enter Replication name", "replicationName");
    setupValidation("#InfraObjectName", "Configuration/InfraObject/IsInfraObjectNameExist", "#InfraObjectNameError", "Enter InfraObject name", "infraObjectName");

    $("#completeAction").hide();
    runnigStatus();
    //disableDelete();

    $("#insertBulkImport").on("click", function () {
        if (allowInsertToDB) {
            $("#insertBulkImport").addClass('d-none');
            insertBulkImportToDB();
        }
    });

    $("#closeOffCanvas").on("click", function () {
        bulkImportObject = "";
        validationResponse = "";
        removeUploadedFile();
    });

    $("#closeInsertDBModal").on("click", function () {
        $('#closeOffCanvas').trigger('click');
        clearInterval(bulkImportSetIntervalOne);
        clearInterval(bulkImportSetIntervalTwo);
    });

    $("#insertToDB").on("click", function () {
        let value = $("#bulkimportDescription").val();
        let result = descriptionValidation(value);
        if (result) {
            $("#totalInfra").html(0);
            $("#successInfra").html(0);
            $("#errorInfra").html(0);
            $("#addDescription").modal("hide");
            $("#offcanvasTop").modal("hide");
            $("#InsetDBModal").modal("show");

            let value = $("#bulkimportDescription").val();
            bulkImportObject.description = value;
            const checkboxes = document.querySelectorAll('.checkBoxBulkImport');
            const selectedValues = [...checkboxes]
                .filter(checkbox => checkbox.checked)
                .map(checkbox => parseInt(checkbox.value, 10));
            $("#bulkimportDescription").val("");

            // Update the bulkImportOperationList to keep only selected values
            if (selectedValues.length > 0) {
                bulkImportObject.bulkImportOperationList = bulkImportObject.bulkImportOperationList.filter((_, index) =>
                    selectedValues.includes(index)
                );
            }

            let $ul = $('#bulkImportLists');
            $ul.empty();
            let $timeLine = $('#bulkImportTimeLine');
            $timeLine.empty();

            $("#totalInfra").html(bulkImportObject?.bulkImportOperationList?.length);

            bulkImportObject?.bulkImportOperationList?.forEach(function (data, index) {
                let htmlDesign = `<li class="list-group-item border-top d-flex justify-content-between align-items-center">
                     <div class="d-flex">
                         <i class="cp-idea Skipped_Paused me-2"></i>
                         <span class="fw-bold text-truncate" style="width:150px" title="${data?.infraObject?.name}">${data?.infraObject?.name}</span>
                     </div>
                     <div>
                     <span id="actionName" class="text-truncate d-inline-block" style="max-width:90%"></span>
                         <div class="d-flex align-items-center gap-1">
                             <i class="cp-thunder"></i>
                                 <div class="progress" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="height: 4px; width:180px;">
                                     <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%;"></div>
                                 </div>
                                 <span class="text-truncate d-inline-block" style="width:50px">Pending</span><span class="ms-2">0/9</span>
                         </div>
                     </div>
                     <div class="btn-group-sm">
                         <button type="button" disabled class='btn btn btn-outline-secondary border-0'><i class="cp-circle-playnext fs-6 me-2"></i><span class="align-middle">Next</span></button>
                         <button type="button" disabled class="btn btn btn-outline-secondary border-0"><i class="cp-reload fs-6 me-2"></i><span class="align-middle">Rollback</span></button>
                         <button type="button" disabled class="btn btn btn-outline-secondary border-0"><i class="cp-disable fs-6 me-2"></i><span class="align-middle">Abort</span></button>
                     </div>
                 </li>`
                $ul.append(htmlDesign);
            });

            let $litime = $('<li></li>');
            $litime.append(`<span style = "text-align: center;"> <img src="/img/isomatric/no_data_found.svg" style="width: auto; height: 200px;" alt="No Data"></span>`);
            $timeLine.append($litime);
        }
    });

    $('#saveServerList').on("click", function () {
        saveEditedServerData();
    });

    $('#saveDatabaseList').on("click", function () {
        saveEditedDatabaseData();
    });

    $('#saveReplicationList').on("click", function () {
        saveEditedReplicationData();
    });

    $('#saveInfraObjectList').on("click", function () {
        saveEditedInfraObjectData();
    });

    $("#flexCheckDefault").on("change", function () {
        let isChecked = $(this).prop("checked");
        $(".checkBoxBulkImport").prop("checked", isChecked);
    });

    $("#closeDescription").on("click", function () {
        $('#Description-Error').text("").removeClass('field-validation-error');
        $("#bulkimportDescription").val("");
    });

    $("#bulkimportDescription").on("keyup", async function () {
        let value = $(this).val();
        descriptionValidation(value);
    });

    $('#validationLists').on('click', '.delete-Row', function () {
        let infraName = $(this).data("infra-name");
        let rowIndex = $(this).data("row-id");
        $("#bulkImportIndex").val(rowIndex);
        $("#deleteData").text(infraName);
        $("#deleteData").attr("title", infraName);
    });

    $("#confirmBulkDeleteButton").on("click", function () {
        const checkboxes = document.querySelectorAll('.checkBoxBulkImport');
        const selectedValues = [...checkboxes]
            .filter(checkbox => checkbox.checked)
            .map(checkbox => parseInt(checkbox.value, 10))
            .sort((a, b) => b - a);
        if (selectedValues.length > 0 && selectedValues.length < bulkImportObject?.bulkImportOperationList?.length) {
            selectedValues.forEach(index => {
                bulkImportObject?.bulkImportOperationList?.splice(index, 1);
            });
        }
        //disableDelete();
        bulkImportValidation("validate");
    });

    $("#confirmDeleteButton").on('click', function () {
        let rowIndex = Number($("#bulkImportIndex").val());
        if (rowIndex >= 0 && rowIndex < bulkImportObject?.bulkImportOperationList?.length) {
            bulkImportObject?.bulkImportOperationList?.splice(rowIndex, 1); // Remove 1 item at the specified index
        }
        bulkImportValidation("validate");
    })

    $("#confirmSave").on("click", function () {
        $("#addDescription").modal("show");
        $("#offcanvasTop").modal("hide");
        setTimeout(() => {
            const now = new Date();
            const formattedDate = now.toISOString().split('T')[0].replace(/-/g, '_');
            let description = "Bulk_Import_" + formattedDate;
            $("#bulkimportDescription").val(description);
        },200)
    });    
});

function removeUploadedFile() {
    $("#fileName").html("File Name");
    const progressBar = document.getElementById('dynamicProgressBar');
    progressBar.style.width = '0%';
    $("#percentageProgressBar").html("0%");
    const fileInput = document.getElementById('file-input');
    fileInput.value = '';
}

//function disableDelete() {
//    $("#deleteRows").css({
//        "pointer-events": "none",
//        "opacity": "0.5"
//    });
//}

//function enableDelete() {
//    $("#deleteRows").removeAttr("style");
//}

//document.addEventListener('DOMContentLoaded', (event) => {
//    const form = document.getElementById('templateForm');
//    form.addEventListener('submit', function (event) {
//        event.preventDefault();
//    });
//});

function isXlsxFile(filename) {
    return filename.toLowerCase().endsWith('.xls');
}

function stringifyProperties(jsonData) {
    const traverse = (obj) => {
        for (const key in obj) {
            if (obj[key] && typeof obj[key] === 'object') {
                if (key === 'properties') {
                    obj[key] = JSON.stringify(obj[key]);
                } else {
                    traverse(obj[key]);
                }
            }
        }
    };
    traverse(jsonData);
    return jsonData;
}

async function getSiteID(name) {
    return new Promise((resolve, reject) => {
        $.ajax({
            type: "GET",
            url: RootUrl + 'Configuration/BulkImport/GetSiteByName',
            dataType: "json",
            data: { name: name },
            success: function (result) {
                if (result.success) {
                    resolve(result); // Resolve the promise with the result
                } else {
                    errorNotification(result);
                    resolve(null); // Resolve with null if there was an error
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                reject(errorThrown); // Reject the promise on error
            }
        });
    });
}

function excelToJson(updatedArray, i, arrayLength) {
    let keyMaps = {
        "isSwitchOver": "",
        "isFailOver": "",
        "isSwitchBack": "",
        "isFailBack": "",
        "prserverconfiguration": {
            "type": "PR", "properties": {}, "status": "Pending", "siteId": "string", "logo": "string", "businessServiceId": "string",
            "roleTypeId": "string", "serverTypeId": "string", "osTypeId": "string", "licenseId": "string",
        },
        "drserverconfiguration": {
            "type": "DR", "properties": {}, "status": "Pending", "siteId": "string", "logo": "string", "businessServiceId": "string",
            "roleTypeId": "string", "serverTypeId": "string", "osTypeId": "string", "licenseId": "string",
        },
        "prdatabaseconfiguration": {
            "type": "PR", "properties": {}, "databaseTypeId": "string", "logo": "string", "serverId": "string", "companyId": "string",
            "licenseId": "string", "businessServiceId": "string", "modeType": "Pending", "serverName": ""
        },
        "drdatabaseconfiguration": {
            "type": "DR", "properties": {}, "databaseTypeId": "string", "logo": "string", "serverId": "string", "companyId": "string",
            "licenseId": "string", "businessServiceId": "string", "modeType": "Pending", "serverName": ""
        },
        "prreplicationconfiguration": {
            "replType": "PR", "properties": {}, "companyId": "string", "logo": "string", "siteId": "string", "typeId": "string",
            "licenseId": "string", "businessServiceId": "string",
        },
        "drreplicationconfiguration": {
            "replType": "DR", "properties": {}, "companyId": "string", "logo": "string", "siteId": "string", "typeId": "string",
            "licenseId": "string", "businessServiceId": "string",
        },
        "infraobjectconfiguration": {
            "companyId": "string", "businessServiceId": "string", "businessFunctionId": "string", "prServerId": "string", "drServerId": "string",
            "nearDRServerId": "string", "prDatabaseId": "string", "drDatabaseId": "string", "nearDRDatabaseId": "string", "drReplicationId": "string",
            "prReplicationId": "string", "nearDRReplicationId": "string", "pairInfraObjectId": "string", "isAssociateInfraObjectId": "string",
            "replicationTypeId": "string", "replicationCategoryTypeId": "string", "subTypeId": "string", "prNodeId": "string", "drNodeId": "string",
            "type": 2,
            "drReady": true, "nearDR": false, "recoveryType": 0, "prServerName": "Mssql_PR_Server", "drServerName": "",
            "nearDRServerName": "", "prDatabaseName": "Mssql_PR_Database", "drDatabaseName": "", "nearDRDatabaseName": "",
            "prReplicationName": "Today_Replication", "drReplicationName": "", "nearDRReplicationName": "", "state": "Maintenance",
            "replicationStatus": 0, "drOperationStatus": 0, "pairInfraObjectName": "", "isAssociateInfraObjectName": "",
            "replicationTypeName": "", "replicationCategoryType": "",
            "prNodeName": "", "drNodeName": "", "nodeProperties": "{}", "reason": "",
            "serverProperties": JSON.stringify({
                "PR": { "id": "", "name": "", "type": "PRDBServer" },
                "DR": { "id": "", "name": "", "type": "DRDBServer" }
            }),
            "databaseProperties": JSON.stringify({
                "PR": { "id": "", "name": "", "type": "PRDB" },
                "DR": { "id": "", "name": "", "type": "DRDB" }
            }),
            "replicationProperties": JSON.stringify({
                "PR": { "id": "", "name": "" },
                "DR": { "id": "", "name": "", }
            }),
            "siteProperties": JSON.stringify([]),
        }
    };

    updatedArray[0].forEach((key, index) => {
        let sanitizedKey = key?.toLowerCase().replace(/\s+/g, '');
        let sanitizedValue = updatedArray[1][index]?.slice(0, 2).toLowerCase() + updatedArray[1][index]?.slice(2).replace(/\s+/g, '');
        let propertiesValue = updatedArray[1][index]?.replace(/\s+/g, '');
        let rowValue = updatedArray[i][index] // don't use this if dropdown value is false assigned "".  || "";
        let toLower = sanitizedValue.toLowerCase();
        if (toLower === "isswitchover" || toLower === "isfailover" || toLower === "isswitchback" || toLower === "isfailback") {
            rowValue = rowValue === "true" ? true : false;
            keyMaps[sanitizedValue] = rowValue;
        }
        if (sanitizedValue === "operationalServiceName" || sanitizedValue === "operationalService") {
            keyMaps["prserverconfiguration"]["businessServiceName"] = rowValue;
            keyMaps["prdatabaseconfiguration"]["businessServiceName"] = rowValue;
            keyMaps["prreplicationconfiguration"]["businessServiceName"] = rowValue;
            keyMaps["drserverconfiguration"]["businessServiceName"] = rowValue;
            keyMaps["drdatabaseconfiguration"]["businessServiceName"] = rowValue;
            keyMaps["drreplicationconfiguration"]["businessServiceName"] = rowValue;
            keyMaps["infraobjectconfiguration"]["businessServiceName"] = rowValue;
        }
        if (sanitizedValue === "licenseKey") {
            keyMaps["prserverconfiguration"]["licenseKey"] = rowValue;
            keyMaps["prdatabaseconfiguration"]["licenseKey"] = rowValue;
            keyMaps["prreplicationconfiguration"]["licenseKey"] = rowValue;
            keyMaps["drserverconfiguration"]["licenseKey"] = rowValue;
            keyMaps["drdatabaseconfiguration"]["licenseKey"] = rowValue;
            keyMaps["drreplicationconfiguration"]["licenseKey"] = rowValue;
        }
        if (sanitizedValue === "operationalFunction") {
            keyMaps["infraobjectconfiguration"]["businessFunctionName"] = rowValue;
        }
        if (keyMaps[sanitizedKey]) {
            if (sanitizedKey === "prserverconfiguration" || sanitizedKey === "drserverconfiguration") {
                if (!requiredKeysServer.includes(sanitizedValue)) {
                    rowValue = rowValue === "false" ? false : rowValue;
                    rowValue = rowValue === "true" ? true : rowValue;
                    keyMaps[sanitizedKey].properties[propertiesValue] = rowValue;
                    //if (propertiesValue.toLowerCase().includes('password')) {                   
                    //}                   
                } else {
                    sanitizedValue = sanitizedValue === "serverRole" ? "roleType" : sanitizedValue;
                    sanitizedValue = sanitizedValue === "osVersion" ? "version" : sanitizedValue;
                    rowValue = sanitizedValue === "version" ? rowValue.split(" ")[1] : rowValue;
                    if (sanitizedKey === "prserverconfiguration" && sanitizedValue === "name") {
                        keyMaps.prdatabaseconfiguration.serverName = rowValue;
                        keyMaps.infraobjectconfiguration.prServerName = rowValue;
                        let serverProperties = JSON.parse(keyMaps.infraobjectconfiguration.serverProperties);
                        serverProperties.PR.id = "@" + rowValue;
                        serverProperties.PR.name = rowValue;
                        keyMaps.infraobjectconfiguration.serverProperties = JSON.stringify(serverProperties);
                    }
                    if (sanitizedKey === "drserverconfiguration" && sanitizedValue === "name") {
                        keyMaps.drdatabaseconfiguration.serverName = rowValue;
                        keyMaps.infraobjectconfiguration.drServerName = rowValue;
                        let serverProperties = JSON.parse(keyMaps.infraobjectconfiguration.serverProperties);
                        serverProperties.DR.id = "@" + rowValue;
                        serverProperties.DR.name = rowValue;
                        keyMaps.infraobjectconfiguration.serverProperties = JSON.stringify(serverProperties);
                    }
                    keyMaps[sanitizedKey][sanitizedValue] = rowValue;
                }
            }
            if (sanitizedKey === "prdatabaseconfiguration" || sanitizedKey === "drdatabaseconfiguration") {
                if (!requiredKeysDatabase.includes(sanitizedValue)) {
                    rowValue = rowValue === "false" ? false : rowValue;
                    rowValue = rowValue === "true" ? true : rowValue;
                    keyMaps[sanitizedKey].properties[propertiesValue] = rowValue;
                } else {
                    sanitizedValue = sanitizedValue === "databaseVersion" ? "version" : sanitizedValue;
                    rowValue = sanitizedValue === "version" ? rowValue.split(" ")[1] : rowValue;
                    if (sanitizedKey === "prdatabaseconfiguration" && sanitizedValue === "name") {
                        keyMaps.infraobjectconfiguration.prDatabaseName = rowValue;
                        let databaseProperties = JSON.parse(keyMaps.infraobjectconfiguration.databaseProperties);
                        databaseProperties.PR.id = "@" + rowValue;
                        databaseProperties.PR.name = rowValue;
                        keyMaps.infraobjectconfiguration.databaseProperties = JSON.stringify(databaseProperties);
                    }
                    if (sanitizedKey === "drdatabaseconfiguration" && sanitizedValue === "name") {
                        keyMaps.infraobjectconfiguration.drDatabaseName = rowValue;
                        let databaseProperties = JSON.parse(keyMaps.infraobjectconfiguration.databaseProperties);
                        databaseProperties.DR.id = "@" + rowValue;
                        databaseProperties.DR.name = rowValue;
                        keyMaps.infraobjectconfiguration.databaseProperties = JSON.stringify(databaseProperties);
                    }
                    if (sanitizedValue !== "serverName") {
                        keyMaps[sanitizedKey][sanitizedValue] = rowValue;
                    }
                }
            }
            if (sanitizedKey === "prreplicationconfiguration" || sanitizedKey === "drreplicationconfiguration") {
                if (!requiredKeysReplication.includes(sanitizedValue)) {
                    rowValue = rowValue === "false" ? false : rowValue;
                    rowValue = rowValue === "true" ? true : rowValue;
                    keyMaps[sanitizedKey].properties[propertiesValue] = rowValue;
                } else {
                    sanitizedValue = sanitizedValue === "replicationType" ? "type" : sanitizedValue;
                    if (sanitizedKey === "prreplicationconfiguration" && sanitizedValue === "name") {
                        keyMaps.infraobjectconfiguration.prReplicationName = rowValue;
                        let replicationProperties = JSON.parse(keyMaps.infraobjectconfiguration.replicationProperties);
                        replicationProperties.PR.id = "@" + rowValue;
                        replicationProperties.PR.name = rowValue;
                        keyMaps.infraobjectconfiguration.replicationProperties = JSON.stringify(replicationProperties);
                    }
                    if (sanitizedKey === "drreplicationconfiguration" && sanitizedValue === "name") {
                        keyMaps.infraobjectconfiguration.drReplicationName = rowValue;
                        let replicationProperties = JSON.parse(keyMaps.infraobjectconfiguration.replicationProperties);
                        replicationProperties.DR.id = "@" + rowValue;
                        replicationProperties.DR.name = rowValue;
                        keyMaps.infraobjectconfiguration.replicationProperties = JSON.stringify(replicationProperties);
                    }
                    keyMaps[sanitizedKey][sanitizedValue] = rowValue;
                    if (sanitizedValue === "type") {
                        if (!rowValue.toLowerCase().includes("perpetuuiti")) {
                            keyMaps.prreplicationconfiguration.licenseId = "NA";
                            keyMaps.drreplicationconfiguration.licenseId = "NA";
                            keyMaps.prreplicationconfiguration.licenseKey = "NA";
                            keyMaps.drreplicationconfiguration.licenseKey = "NA";
                        }
                    }
                }
            }
            if (sanitizedKey === "infraobjectconfiguration") {
                if (sanitizedValue === "name" || sanitizedValue === "description" || sanitizedValue === "businessFunctionName"
                    || sanitizedValue === "businessServiceName" || sanitizedValue === "activityType"
                    || sanitizedValue === "databaseType" || sanitizedValue === "priority" || sanitizedValue === "isPair"
                    || sanitizedValue === "isAssociate" || sanitizedValue === "siteTypePR" || sanitizedValue === "siteTypeDR"
                    || sanitizedValue === "replicationCategoryType" || sanitizedValue === "replicationType") {
                    sanitizedValue = sanitizedValue === "activityType" ? "typeName" : sanitizedValue;
                    sanitizedValue = sanitizedValue === "databaseType" ? "subType" : sanitizedValue;
                    sanitizedValue = sanitizedValue === "replicationType" ? "replicationTypeName" : sanitizedValue;
                    rowValue = rowValue === "false" ? false : rowValue;
                    rowValue = rowValue === "true" ? true : rowValue;
                    if (sanitizedValue === "siteTypePR") {
                        async function getSiteIDByName() {
                            let siteID = await getSiteID(rowValue);
                            let siteTypePR = { "id": "", "name": "", "category": "Primary" };
                            let siteProperties = JSON.parse(keyMaps.infraobjectconfiguration.siteProperties);
                            siteTypePR.id = siteID?.data[0]?.id;
                            siteTypePR.name = rowValue;
                            siteProperties.push(siteTypePR);
                            keyMaps.infraobjectconfiguration.siteProperties = JSON.stringify(siteProperties);
                        }
                        setTimeout(async () => { await getSiteIDByName(); }, 100);
                    } else if (sanitizedValue === "siteTypeDR") {
                        async function getSiteIDByName() {
                            let siteID = await getSiteID(rowValue);
                            let siteTypeDR = { "id": "", "name": "", "category": "DR" };
                            let siteProperties = JSON.parse(keyMaps.infraobjectconfiguration.siteProperties);
                            siteTypeDR.id = siteID?.data[0]?.id;
                            siteTypeDR.name = rowValue;
                            siteProperties.push(siteTypeDR);
                            keyMaps.infraobjectconfiguration.siteProperties = JSON.stringify(siteProperties);
                        }
                        setTimeout(async () => { await getSiteIDByName(); }, 100);
                    } else {
                        if (rowValue !== 1 && rowValue !== 2 && rowValue !== 3 && rowValue !== false && rowValue !== true) {
                            if (rowValue?.toLowerCase() === "application") {
                                keyMaps[sanitizedKey]['type'] = 1;
                            }
                            if (rowValue?.toLowerCase() === "db") {
                                keyMaps[sanitizedKey]['type'] = 2;
                            }
                            if (rowValue?.toLowerCase() === "virtual") {
                                keyMaps[sanitizedKey]['type'] = 3;
                            }
                        }
                        keyMaps[sanitizedKey][sanitizedValue] = rowValue;
                    }
                }
            }
        }
    });

    let bulkImportRow = {
        serverList: [keyMaps["prserverconfiguration"], keyMaps["drserverconfiguration"]],
        databaseList: [keyMaps["prdatabaseconfiguration"], keyMaps["drdatabaseconfiguration"]],
        replicationList: [keyMaps["prreplicationconfiguration"], keyMaps["drreplicationconfiguration"]],
        infraObject: keyMaps["infraobjectconfiguration"],
        isSwitchOver: keyMaps["isSwitchOver"],
        isFailOver: keyMaps["isFailOver"],
        isSwitchBack: keyMaps["isSwitchBack"],
        isFailBack: keyMaps["isFailBack"],
    };
    const result = stringifyProperties(bulkImportRow);
    return result;
}

function bindTimelineData(parentindex, index, item) {
    let $timeLine = $('#bulkImportTimeLine');
    $timeLine.empty();
    if (item[parentindex]?.bulkImportOperationGroup[index]?.bulkImportActionResultListVms.length === 0) {
        let $litime = $('<li></li>');
        $litime.append(`<span style = "text-align: center;"> <img src="/img/isomatric/no_data_found.svg" style="width: auto; height: 200px;" alt="No Data"></span>`);
        $timeLine.append($litime);
        return
    }
    let rollBack = item[parentindex]?.bulkImportOperationGroup[index]?.status
    let actionStatus = [];
    item[parentindex]?.bulkImportOperationGroup[index]?.bulkImportActionResultListVms?.forEach(function (tmline) {
        actionStatus.push(tmline?.status);
        const entityType = tmline.entityType.toLowerCase();
        const icon = entityType === "server" ? 'cp-server'
            : entityType === "database" ? 'cp-database'
                : entityType === "replication" ? 'cp-replication-on'
                    : entityType === "infraobject" ? 'cp-infra-object'
                        : entityType.includes("workflow") ? 'cp-workflow-configuration'
                            : "";
        const iconColor = entityType === "server" ? '#2f0406'
            : entityType === "database" ? '#1015eb'
                : entityType === "replication" ? '#9d15b3'
                    : entityType === "infraobject" ? '#00183a'
                        : '';
        const date = new Date(tmline.startTime);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based
        const year = date.getFullYear();
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        const formattedDateTime = `${day}.${month}.${year} ${hours}:${minutes}:${seconds}`;
        let $litime = $('<li></li>');
        let $icontime = $(`<div class="item-icon" style="background:${(tmline?.status === "Error" || tmline?.status === "Next" || rollBack === "Rollback") ? '#ffd8da' : '#bde9de'}"><i class="${icon}" style="color: ${iconColor}"></i></div>`);
        let $textDivtime = $('<div class="item-text"></div>');
        let $titleDivtime = $('<div class="item-title"></div>');
        let $timestampDivtime = $('<div class="item-timestamp fs-8 ms-2 text-end" style="width:40%;"></div>').text(formattedDateTime);
        let time = $(' <div class="d-flex align-items-center"></div>');
        let $titleSpan = $(`<span class="ms-2"></span>`).text(tmline?.errorMessage); //${(tmline?.status === "Error" || tmline?.status === "Next" || rollBack === "Rollback") ? 'text-danger' : 'text-success'} tmline?.entityName + ": " +
        time.append($titleSpan);
        $titleDivtime.append(time)
        $textDivtime.append($titleDivtime).append($timestampDivtime);
        $litime.append($icontime).append($textDivtime);
        $timeLine.append($litime);
    });
}

function renderTimeline(parentindex, childindex) {
    // Deselect the currently active item
    const currentActiveItem = document.querySelector(`.Active-Card`);
    if (currentActiveItem) {
        dynamicCardParentIndex = parentindex;
        dynamicCardChildIndex = childindex;
        currentActiveItem.classList.remove('Active-Card');
        document.querySelector(`.dynamic-active-card${parentindex}${childindex}`).classList.add('Active-Card');
    }
    if (timelineArray && timelineArray.length > 0) {
        executeParentIndex = parentindex;
        executeTimeline = childindex;
        bindTimelineData(parentindex, childindex, timelineArray)
    }
}

async function UpdateBulkActionResult(updateBulkImportAction) {
    await $.ajax({
        type: "POST",
        url: RootUrl + "Configuration/BulkImport/UpdateBulkImportActionResult",
        dataType: "json",
        headers: { 'RequestVerificationToken': await gettoken() },
        data: { updateBulkImportActionResultCommand: updateBulkImportAction },
        success: function (reslt) {
            if (reslt.success) {

            } else {
                errorNotification(reslt);
            }
        }
    });
}

async function getBulkImportActionResult(response, status, conditional) {
    await $.ajax({
        type: "GET",
        url: RootUrl + "Configuration/BulkImport/GetBulkImportActionResult",
        dataType: "json",
        headers: { 'RequestVerificationToken': await gettoken() },
        data: { operationId: response?.data?.bulkImportOperationId, operationGroupId: response?.data?.id },
        success: function (res) {
            if (res.success) {
                res?.data?.forEach(function (data, index) {
                    if (data.status === "Error") {
                        let updateBulkImportAction = {
                            id: res?.data[index]?.id,
                            companyId: res?.data[index]?.companyId,
                            nodeId: res?.data[index]?.nodeId,
                            nodeName: res?.data[index]?.nodeName,
                            bulkImportOperationId: res?.data[index]?.bulkImportOperationId,
                            bulkImportOperationGroupId: res?.data[index]?.bulkImportOperationGroupId,
                            conditionalOperation: conditional,
                            entityId: res?.data[index]?.entityId,
                            entityName: res?.data[index]?.entityName,
                            entityType: res?.data[index]?.entityType,
                            status: status,
                            errorMessage: res?.data[index]?.errorMessage,
                            startTime: res?.data[index]?.startTime,
                            endTime: res?.data[index]?.endTime
                        };
                        UpdateBulkActionResult(updateBulkImportAction);
                    }
                });
            } else {
                errorNotification(res);
            }
        }
    });
}

async function UpdateBulkImportGroup(response, updateBulkFroup, status, conditional) {
    await $.ajax({
        type: "POST",
        url: RootUrl + "Configuration/BulkImport/UpdateBulkImportGroup",
        dataType: "json",
        headers: { 'RequestVerificationToken': await gettoken() },
        data: { updateBulkImportOperationGroupCommand: updateBulkFroup },
        success: function (result) {
            if (result.success) {
                if (status === "Next") {
                    getBulkImportActionResult(response, status, conditional);
                }
            } else {
                errorNotification(result);
            }
        }
    });
}

async function actionButton(GroupId, status, conditional) {
    await $.ajax({
        type: "GET",
        url: RootUrl + "Configuration/BulkImport/GetBulkImportOperationGroup",
        dataType: "json",
        headers: { 'RequestVerificationToken': await gettoken() },
        data: { id: GroupId },
        success: function (response) {
            if (response.success) {
                let updateBulkFroup = {
                    id: response?.data?.id,
                    bulkImportOperationId: response?.data?.bulkImportOperationId,
                    companyId: response?.data?.companyId,
                    properties: response?.data?.properties,
                    status: status,
                    ErrorMessage: response?.data?.errorMessage,
                    conditionalOperation: conditional,
                    nodeId: response?.data?.nodeId,
                    infraObjectName: response?.data?.infraObjectName,
                    progressStatus: response?.data?.progressStatus,
                };
                UpdateBulkImportGroup(response, updateBulkFroup, status, conditional);
            } else {
                errorNotification(response);
            }
        }
    });
}

async function rollBackButton(groupID, status) {
    await $.ajax({
        type: "GET",
        url: RootUrl + "Configuration/BulkImport/GetBulkImportOperationGroup",
        dataType: "json",
        headers: { 'RequestVerificationToken': await gettoken() },
        data: { id: groupID },
        success: function (response) {
            if (response.success) {
                let updateBulkFroup = {
                    id: response?.data?.id,
                    bulkImportOperationId: response?.data?.bulkImportOperationId,
                    companyId: response?.data?.companyId,
                    properties: response?.data?.properties,
                    status: status,
                    ErrorMessage: response?.data?.errorMessage,
                    conditionalOperation: response?.data?.conditionalOperation,
                    nodeId: response?.data?.nodeId,
                    infraObjectName: response?.data?.infraObjectName,
                    progressStatus: response?.data?.progressStatus,
                };
                UpdateBulkImportGroup(response, updateBulkFroup, status, response?.data?.conditionalOperation);
            } else {
                errorNotification(response);
            }
        }
    });

    await $.ajax({
        type: "POST",
        url: RootUrl + "Configuration/BulkImport/RollBackBulkImportAction",
        dataType: "json",
        headers: { 'RequestVerificationToken': await gettoken() },
        data: { rollBackBulkImportCommand: { groupId: groupID } },
        success: function (result) {
            if (result.success) {
                $(`.${groupID}`).attr("disabled", "disabled");
                notificationAlert("success", result?.data?.message);
            } else {
                errorNotification(result);
            }
        }
    });
}

function getResult() {
    $.ajax({
        type: "GET",
        url: RootUrl + 'Configuration/BulkImport/GetBulkImportOperationsrunningStatus',
        dataType: 'json',
        success: function (result) {
            if (result.success) {
                let $ul = $('#bulkImportLists');
                $ul.empty();
                if (result?.data?.length > 0) {
                    bulkImportOperationStatus = result?.data;
                    result?.data?.forEach(async function (item, parentindex) {
                        let actionSuccess = 0;
                        let actionError = 0;
                        $("#totalInfra").html(item?.bulkImportOperationGroup?.length);
                        let success = []; //multiple operationGroup
                        let percent = [];
                        item?.bulkImportOperationGroup?.forEach(function (data, childindex) {
                            let groupID = JSON.stringify(data?.id);
                            let strg = data?.progressStatus;
                            let percentage = eval(strg) * 100;
                            percent.push(percentage);
                            success.push(data?.status);
                            let infraName = data?.infraObjectName || "NA";
                            let errorClass = data?.status === "Error" ? "bg-error" : "bg-success";
                            let html = `<li onclick="renderTimeline(${parentindex}, ${childindex})" class="dynamic-active-card${parentindex}${childindex} list-group-item border-top ${(dynamicCardParentIndex === parentindex && dynamicCardChildIndex === childindex) ? 'Active-Card' : ''}  d-flex justify-content-between align-items-center">
                                            <div class="d-flex">
                                                <i class="cp-idea Skipped_Paused me-2"></i>
                                                <span class="fw-bold text-truncate" style="width:150px" title="${infraName}">${infraName}</span>
                                            </div>
                                            <div>        
                                            <span id="actionName${childindex}" class="text-truncate d-inline-block" style="max-width:90%"></span>                                                                                                 
                                                <div class="d-flex align-items-center gap-1 mt-1">
                                                    <i class="${data?.status.toLowerCase() === "running" ? "cp-thunder" : data?.status.toLowerCase() === "success" ? "cp-success" : data?.status.toLowerCase() === "error" ? "cp-error" : "cp-pause"}"></i>
                                                        <div class="progress" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="height: 4px; width:180px;">
                                                            <div class="progress-bar ${errorClass} progress-bar-striped progress-bar-animated" style="width: ${percentage}%;"></div>
                                                        </div>
                                                        <span class="text-truncate d-inline-block" title="${data?.status}" style="width:50px">${data?.status}</span><span class="ms-2">${data?.progressStatus}</span>
                                                </div>
                                            </div>
                                            <div class="btn-group-sm">
                                                <button type="button" ${(data?.status === "Error" || data?.status === "Next") && percentage !== 100 ? "" : "disabled"} onclick='actionButton(${groupID},"Next",${1})' class='btn btn btn-outline-secondary border-0'><i class="cp-circle-playnext fs-6 me-2"></i><span class="align-middle">Next</span></button>
                                                <button type="button" ${(data?.status === "Error" || data?.status === "Next") && percentage !== 100 ? "" : "disabled"} onclick='rollBackButton(${groupID},"Rollback")' class="btn btn btn-outline-secondary border-0"><i class="cp-reload fs-6 me-2"></i><span class="align-middle">Rollback</span></button>
                                                <button type="button" onclick='actionButton(${groupID},"Abort",${0})' class="btn btn btn-outline-secondary border-0"><i class="cp-disable fs-6 me-2"></i><span class="align-middle">Abort</span></button>
                                            </div>
                                        </li>`
                            $ul.append(html);
                            let actionStatus = [];
                            data?.bulkImportActionResultListVms?.map(function (action) {
                                $("#actionName" + childindex).text("");
                                $("#actionName" + childindex).text(action?.entityName ? action?.entityName : "NA");
                                $("#actionName" + childindex).attr("title", action?.entityName ? action?.entityName : "NA");
                                actionStatus.push(action?.status);
                            })
                            if (actionStatus.length > 0) {
                                let resultStatus = actionStatus?.every(value => value.toLowerCase() === "success");
                                if (resultStatus) {
                                    actionSuccess++
                                } else {
                                    actionError++
                                }
                                $("#successInfra").html(actionSuccess);
                                $("#errorInfra").html(actionError);
                            }
                            if (executeTimeline > 0 || executeParentIndex > 0) {
                                timelineArray = result?.data;
                                bindTimelineData(executeParentIndex, executeTimeline, result?.data);
                            } else if (parentindex === 0 && childindex === 0) {
                                timelineArray = result?.data;
                                bindTimelineData(parentindex, childindex, result?.data);
                            }
                        });
                        if (success.length > 0) {
                            let successed = success?.every(value => (value?.toLowerCase() === "success" || value?.toLowerCase() === "abort" || value?.toLowerCase() === "rollback" || value?.toLowerCase() === "error"));
                            let resultPercentage = percent?.every(value => (value === 100));
                            success.forEach(function (data, index) {
                                if (data?.toLowerCase() === "rollback" || data?.toLowerCase() === "abort") {
                                    resultPercentage = true
                                }
                            })
                            if (successed && resultPercentage) {
                                //$ul.append(`
                                //    <div class="text-end p-2">
                                //        <button type="button" onclick='completeAction(${JSON.stringify(item.id)},${parentindex})' class="btn btn-primary">Completed</button>
                                //    </div>
                                //`);
                                // $("#insertBulkImport").addClass('d-none');
                                $("#CompleteBulkImport").removeClass('d-none');
                                $("#CompleteBulkImport").attr("onclick", `completeAction(${JSON.stringify(item.id)}, ${parentindex})`);
                            }
                        }
                    });
                } else {
                    bindTimelineData(0, 0, []);
                    clearInterval(bulkImportSetIntervalOne);
                    clearInterval(bulkImportSetIntervalTwo);
                    $("#closeInsertDBModal").trigger("click");
                    removeUploadedFile();
                }
            } else {
                errorNotification(result);
            }
        }
    });
}

async function completeAction(GroupId, index) {
    let filteredOperationGroup = bulkImportOperationStatus[index];
    let success = false;
    if (filteredOperationGroup?.bulkImportOperationGroup.length > 0) {
        success = filteredOperationGroup?.bulkImportOperationGroup.every(value => (value.status.toLowerCase() === "success"));
    }
    try {
        $("#CompleteBulkImport").addClass('d-none');
        let operationStatus = success ? "Success" : "SuccessWithError";
        const operationResult = await $.ajax({
            type: "GET",
            url: `${RootUrl}Configuration/BulkImport/GetBulkImportOperation`,
            dataType: "json",
            headers: { 'RequestVerificationToken': await gettoken() },
            data: { id: GroupId },
        });
        if (!operationResult.success) {
            return errorNotification(operationResult);
        }
        const updateBulkOperation = {
            id: operationResult.data.id,
            companyId: operationResult.data.companyId,
            userName: operationResult.data.userName,
            description: operationResult.data.description,
            status: operationStatus,
            startTime: operationResult.data.startTime,
            endTime: operationResult.data.endTime,
        };
        const updateOperationResult = await $.ajax({
            type: "POST",
            url: `${RootUrl}Configuration/BulkImport/UpdateBulkImportOperation`,
            dataType: "json",
            headers: { 'RequestVerificationToken': await gettoken() },
            data: { updateBulkImportOperationCommand: updateBulkOperation },
        });
        if (!updateOperationResult.success) {
            return errorNotification(updateOperationResult);
        }
        const groupResponse = await $.ajax({
            type: "GET",
            url: `${RootUrl}Configuration/BulkImport/GetBulkImportOperationGroupByOperationId`,
            dataType: "json",
            headers: { 'RequestVerificationToken': await gettoken() },
            data: { id: GroupId },
        });
        if (!groupResponse.success) {
            return errorNotification(groupResponse);
        }
        const updateBulkImOperationGroup = groupResponse.data.map(groupdata => ({
            Id: groupdata.id,
            BulkImportOperationId: groupdata.bulkImportOperationId,
            CompanyId: groupdata.companyId,
            Properties: groupdata.properties,
            Status: groupdata?.status?.toLowerCase() === "success" ? "Completed" : groupdata?.status?.toLowerCase() === "error" ? "CompletedWithError" : "Aborted",
            ProgressStatus: groupdata.progressStatus,
            ErrorMessage: groupdata.errorMessage,
            ConditionalOperation: groupdata.conditionalOperation,
            NodeId: groupdata.nodeId,
            InfraObjectName: groupdata.infraObjectName,
        }));
        const updateGroupResult = await $.ajax({
            type: "POST",
            url: `${RootUrl}Configuration/BulkImport/UpdateBulkImportOperationGroup`,
            dataType: "json",
            headers: { 'RequestVerificationToken': await gettoken() },
            data: { updateBulkImportOperationGroupCommand: updateBulkImOperationGroup },
        });
        if (!updateGroupResult.success) {
            return errorNotification(updateGroupResult);
        } else {
            setTimeout(() => {
                $("#insertBulkImport").removeClass('d-none');
            },100)
        }
    } catch (error) {
        errorNotification({ success: false, message: error.message });
    }
}

function getTdAttributes(item, commandType, modalId, index) {
    const commandExists = item?.[commandType]?.length > 0;
    return {
        attributes: commandExists ? `data-bs-toggle="modal" data-bs-target="#${modalId}" role="button" onclick="edit${modalId}(${index})" ` : '',
        iconClass: commandExists ? 'cp-error text-danger' : 'cp-success text-success'
    };
}

function editServerModal(index) {    //getTdAttributes dynamically added editServerModal
    serverIndex = index;
    bulkImportObject?.bulkImportOperationList[index]?.serverList?.forEach(function (serverdata) {
        validationResponse.validatorResponse[index]?.serverCommand?.forEach(function (servercommand) {
            const serverType = serverdata?.type?.toLowerCase();
            const fields = {
                name: serverdata?.name,
                siteName: serverdata?.siteName,
                businessServiceName: serverdata?.businessServiceName,
                roleType: serverdata?.roleType,
                serverType: serverdata?.serverType,
                osType: serverdata?.osType,
                version: serverdata?.version,
                licenseKey: serverdata?.licenseKey
            };
            if (serverType === "pr") {
                $("#PRServerName").val(fields.name);
                $("#PRSiteName").val(fields.siteName);
                $("#PROperationalService").val(fields.businessServiceName);
                $("#PRServerRole").val(fields.roleType);
                $("#PRServerType").val(fields.serverType);
                $("#PROSType").val(fields.osType);
                $("#PRVersion").val(fields.version);
                $("#PRLicense").val(fields.licenseKey);
            } else if (serverType === "dr") {
                $("#DRServerName").val(fields.name);
                $("#DRSiteName").val(fields.siteName);
                $("#DROperationalService").val(fields.businessServiceName);
                $("#DRServerRole").val(fields.roleType);
                $("#DRServerType").val(fields.serverType);
                $("#DROSType").val(fields.osType);
                $("#DRVersion").val(fields.version);
                $("#DRLicense").val(fields.licenseKey);
            }
            Object.keys(fields).forEach(field => {
                if (fields[field]?.toLowerCase() === servercommand?.name?.toLowerCase()) {
                    const errorFieldId = serverType === "pr" ? `PRServer${servercommand?.propertyName}Error` : `DRServer${servercommand?.propertyName}Error`;
                    $(`#${errorFieldId}`).text(servercommand.exception).addClass('field-validation-error');
                }
            });
        });
    });
}

function saveEditedServerData() {
    const prName = $("#PRServerName").val();
    //const prSiteName = $("#PRSiteName").val();
    //const prBusinessServiceName = $("#PROperationalService").val();
    //const prRoleType = $("#PRServerRole").val();
    //const prServerType2 = $("#PRServerType").val();
    //const prOSType = $("#PROSType").val();
    //const prVersion = $("#PRVersion").val();
    //const prLicenseKey = $("#PRLicense").val();

    //DR
    const drName = $("#DRServerName").val();
    //const drSiteName = $("#DRSiteName").val();
    //const drBusinessServiceName = $("#DROperationalService").val();
    //const drRoleType = $("#DRServerRole").val();
    //const drServerType2 = $("#DRServerType").val();
    //const drOSType = $("#DROSType").val();
    //const drVersion = $("#DRVersion").val();
    //const drLicenseKey = $("#DRLicense").val();

    // Iterate over serverList to update the name
    bulkImportObject?.bulkImportOperationList[serverIndex]?.serverList?.forEach(function (serverdata) {
        const serverType = serverdata?.type?.toLowerCase();
        if (serverType === "pr") {
            serverdata.name = prName;
            //serverdata.siteName = prSiteName;
            //serverdata.businessServiceName = prBusinessServiceName;
            //serverdata.roleType = prRoleType;
            //serverdata.serverType = prServerType2;
            //serverdata.osType = prOSType;
            //serverdata.version = prVersion;
            //serverdata.licenseKey = prLicenseKey;
        }
        if (serverType === "dr") {
            serverdata.name = drName;
            //serverdata.siteName = drSiteName;
            //serverdata.businessServiceName = drBusinessServiceName;
            //serverdata.roleType = drRoleType;
            //serverdata.serverType = drServerType2;
            //serverdata.osType = drOSType;
            //serverdata.version = drVersion;
            //serverdata.licenseKey = drLicenseKey;
        }
    });
    $('#ServerModal').modal("hide");
    bulkImportValidation("server");
}

function editDatabaseModal(index) {   //getTdAttributes dynamically added editDatabaseModal
    databaseIndex = index;
    bulkImportObject?.bulkImportOperationList[index]?.databaseList.forEach(function (databasedata) {
        validationResponse.validatorResponse[index]?.databaseCommand.forEach(function (databasecommand) {
            const databaseType = databasedata.type.toLowerCase();
            const fields = {
                name: databasedata?.name,
                databaseType: databasedata?.databaseType,
                businessServiceName: databasedata?.businessServiceName,
                version: databasedata?.version,
                serverName: databasedata?.serverName,
                licenseKey: databasedata?.licenseKey,
            };
            if (databaseType === "pr") {
                $("#PRDatabaseName").val(fields?.name);
                $("#PRDatabaseType").val(fields?.databaseType);
                $("#PRDBOperational").val(fields?.businessServiceName);
                $("#PRDBVersion").val(fields?.version);
                $("#PRDBServer").val(fields?.serverName);
                $("#PRDBLicense").val(fields?.licenseKey);
            } else if (databaseType === "dr") {
                $("#DRDatabaseName").val(fields?.name);
                $("#DRDatabaseType").val(fields?.databaseType);
                $("#DRDBOperational").val(fields?.businessServiceName);
                $("#DRDBVersion").val(fields?.version);
                $("#DRDBServer").val(fields?.serverName);
                $("#DRDBLicense").val(fields?.licenseKey);
            }
            Object.keys(fields).forEach(field => {
                if (fields[field]?.toLowerCase() === databasecommand?.name?.toLowerCase()) {
                    const errorFieldId = databaseType === "pr" ? `PRDatabase${databasecommand.propertyName}Error` : `DRDatabase${databasecommand.propertyName}Error`;
                    $(`#${errorFieldId}`).text(databasecommand.exception).addClass('field-validation-error');
                }
            });
        });
    });
}

function saveEditedDatabaseData() {
    const PRDatabaseName = $("#PRDatabaseName").val();
    //const PRDatabaseType = $("#PRDatabaseType").val();
    //const PRDBOperational = $("#PRDBOperational").val();
    //const PRDBVersion = $("#PRDBVersion").val();
    //const PRDBServer = $("#PRDBServer").val();
    //const PRDBLicense = $("#PRDBLicense").val();

    //DR
    const DRDatabaseName = $("#DRDatabaseName").val();
    //const DRDatabaseType = $("#DRDatabaseType").val();
    //const DRDBOperational = $("#DRDBOperational").val();
    //const DRDBVersion = $("#DRDBVersion").val();
    //const DRDBServer = $("#DRDBServer").val();
    //const DRDBLicense = $("#DRDBLicense").val();

    // Iterate over serverList to update the name
    bulkImportObject?.bulkImportOperationList[databaseIndex]?.databaseList?.forEach(function (databasedata) {
        const databaseType = databasedata?.type?.toLowerCase();
        if (databaseType === "pr") {
            databasedata.name = PRDatabaseName;
            //databasedata.databaseType = PRDatabaseType;
            //databasedata.businessServiceName = PRDBOperational;
            //databasedata.version = PRDBVersion;
            //databasedata.serverName = PRDBServer;
            //databasedata.licenseKey = PRDBLicense;
        }
        if (databaseType === "dr") {
            databasedata.name = DRDatabaseName;
            //databasedata.databaseType = DRDatabaseType;
            //databasedata.businessServiceName = DRDBOperational;
            //databasedata.version = DRDBVersion;
            //databasedata.serverName = DRDBServer;
            //databasedata.licenseKey = DRDBLicense;
        }
    });
    $('#DatabaseModal').modal("hide");
    bulkImportValidation("database");
}

function editReplicationModal(index) {   //getTdAttributes dynamically added editReplicationModal
    replicationIndex = index;
    bulkImportObject?.bulkImportOperationList[index]?.replicationList?.forEach(function (replicationdata) {
        validationResponse.validatorResponse[index]?.replicationCommand?.forEach(function (replicationcommand) {
            const replicationType = replicationdata.replType.toLowerCase();
            const fields = {
                name: replicationdata?.name,
                siteName: replicationdata?.siteName,
                businessServiceName: replicationdata?.businessServiceName,
                type: replicationdata?.type,
                licenseKey: replicationdata?.licenseKey,
            };
            if (replicationType === "pr") {
                $("#PRReplicationName").val(fields?.name);
                $("#PRReplicationSite").val(fields?.siteName);
                $("#PRReplicationOperational").val(fields?.businessServiceName);
                $("#PRReplicationType").val(fields?.type);
                $("#PRReplicationLicense").val(fields?.licenseKey);
            } else if (replicationType === "dr") {
                $("#DRReplicationName").val(fields?.name);
                $("#DRReplicationSite").val(fields?.siteName);
                $("#DRReplicationOperational").val(fields?.businessServiceName);
                $("#DRReplicationType").val(fields?.type);
                $("#DRReplicationLicense").val(fields?.licenseKey);
            }
            Object.keys(fields).forEach(field => {
                if (fields[field]?.toLowerCase() === replicationcommand?.name?.toLowerCase()) {
                    const errorFieldId = replicationType === "pr" ? `PRReplication${replicationcommand.propertyName}Error` : `DRReplication${replicationcommand.propertyName}Error`;
                    $(`#${errorFieldId}`).text(replicationcommand.exception).addClass('field-validation-error');
                }
            });
        });
    });
}

function saveEditedReplicationData() {
    const PRReplicationName = $("#PRReplicationName").val();
    //const PRReplicationSite = $("#PRReplicationSite").val();
    //const PRReplicationOperational = $("#PRReplicationOperational").val();
    //const PRReplicationType = $("#PRReplicationType").val();
    //const PRReplicationLicense = $("#PRReplicationLicense").val();

    //DR
    const DRReplicationName = $("#DRReplicationName").val();
    //const DRReplicationSite = $("#DRReplicationSite").val();
    //const DRReplicationOperational = $("#DRReplicationOperational").val();
    //const DRReplicationType = $("#DRReplicationType").val();
    //const DRReplicationLicense = $("#DRReplicationLicense").val();

    // Iterate over serverList to update the name
    bulkImportObject?.bulkImportOperationList[replicationIndex]?.replicationList?.forEach(function (replicationdata) {
        const replicationType = replicationdata.replType.toLowerCase();
        if (replicationType === "pr") {
            replicationdata.name = PRReplicationName;
            //replicationdata.siteName = PRReplicationSite;
            //replicationdata.businessServiceName = PRReplicationOperational;
            //replicationdata.type = PRReplicationType;
            //replicationdata.licenseKey = PRReplicationLicense;
        }
        if (replicationType === "dr") {
            replicationdata.name = DRReplicationName;
            //replicationdata.siteName = DRReplicationSite;
            //replicationdata.businessServiceName = DRReplicationOperational;
            //replicationdata.type = DRReplicationType;
            //replicationdata.licenseKey = DRReplicationLicense;
        }
    });
    $('#ReplicationModal').modal("hide");
    bulkImportValidation("replication");
}

function editInfraObjectModal(index) {  //getTdAttributes dynamically added editInfraObjectModal
    infraObjectIndex = index;
    const fields = {
        name: bulkImportObject?.bulkImportOperationList[index]?.infraObject?.name,
        description: bulkImportObject?.bulkImportOperationList[index]?.infraObject?.description,
        businessServiceName: bulkImportObject?.bulkImportOperationList[index]?.infraObject?.businessServiceName,
        businessFunctionName: bulkImportObject?.bulkImportOperationList[index]?.infraObject?.businessFunctionName,
        replicationCategoryType: bulkImportObject?.bulkImportOperationList[index]?.infraObject?.replicationCategoryType,
        replicationTypeName: bulkImportObject?.bulkImportOperationList[index]?.infraObject?.replicationTypeName,
    };
    $("#InfraObjectName").val(fields?.name);
    $("#InfraObjectDescription").val(fields?.description);
    $("#InfraObjectOperationalService").val(fields?.businessServiceName);
    $("#InfraObjectOperationalFunction").val(fields?.businessFunctionName);
    $("#InfraObjectReplicationCategory").val(fields?.replicationCategoryType);
    $("#InfraObjectReplicationType").val(fields?.replicationTypeName);

    validationResponse.validatorResponse[index]?.infraObjectCommand?.forEach(function (infraobjectcommand) {
        Object.keys(fields).forEach(field => {
            if (fields[field]?.toLowerCase() === infraobjectcommand?.name?.toLowerCase()) {
                const errorFieldId = `InfraObject${infraobjectcommand.propertyName}Error`;
                $(`#${errorFieldId}`).text(infraobjectcommand.exception).addClass('field-validation-error');
            }
        });
    });
}

function saveEditedInfraObjectData() {
    const InfraObjectName = $("#InfraObjectName").val();
    bulkImportObject.bulkImportOperationList[infraObjectIndex].infraObject.name = InfraObjectName;
    $('#InfraObjectModal').modal("hide");
    bulkImportValidation("infraobject");
}

async function bulkImportValidation(value = null) {
    if (value === "server" || value === "database" || value === "replication" || value === "infraobject") {
        $('#validationModal').trigger('click');
    }
    await $.ajax({
        type: "POST",
        url: RootUrl + "Configuration/BulkImport/BulkImportValidation",
        dataType: "json",
        headers: {
            'RequestVerificationToken': await gettoken()
        },
        contentType: 'application/json',
        data: JSON.stringify(bulkImportObject),
        success: function (response) {
            const progressBar = document.getElementById('dynamicProgressBar');
            progressBar.style.width = '100%';
            $("#percentageProgressBar").html(`100%`);
            if (response.success) {
                validatorResponse = response?.data?.validatorResponse;
                if (response?.data?.validatorResponse?.length > 0) {
                    let workflowError = `<i class="cp-error text-danger"></i>`;
                    let workflowSuccess = `<i class="cp-success text-success"></i>`;
                    validationResponse = response?.data;
                    if (!value) {
                        $('#validationModal').trigger('click');
                    }
                    let validationLists = $("#validationLists");
                    validationLists.empty();
                    $('#totalInfraObject').text(response?.data?.validatorResponse.length);
                    let success = 0;
                    let failure = 0;
                    $("#failureInfraObject").text(failure);
                    $("#successInfraObject").text(success);

                    response?.data?.validatorResponse?.forEach(function (item, index) {
                        let infraError = "";
                        if (item?.databaseCommand?.length > 0 || item?.infraObjectCommand?.length > 0
                            || item?.replicationCommand?.length > 0 || item?.serverCommand?.length > 0) {
                            infraError = `<i class="cp-error text-danger fs-7"></i>`;
                            failure++
                            $("#failureInfraObject").text(failure);
                        } else {
                            infraError = `<i class="cp-success text-success fs-7"></i>`;
                            success++
                            $("#successInfraObject").text(success);
                            notificationAlert("success", "All the fields are correct")
                        }
                        let newRow = `
                        <tr>
                            <td class="text-center">
                                <input class="form-check-input mx-0 checkBoxBulkImport" id="firstCheckBoxBulkImport${index}" type="checkbox" value="${index}">
                            </td>
                            <td>${index + 1}</td>
                            <td>${infraError}<span class="ms-2">${item?.infraObjectName}</span></td>
                            <td
                                ${getTdAttributes(item, 'serverCommand', 'ServerModal', index).attributes}>
                                <i class="${getTdAttributes(item, 'serverCommand', 'ServerModal').iconClass}"></i>
                            </td>
                            <td
                                ${getTdAttributes(item, 'databaseCommand', 'DatabaseModal', index).attributes}>
                                <i class="${getTdAttributes(item, 'databaseCommand', 'DatabaseModal').iconClass}"></i>
                            </td>
                            <td
                                ${getTdAttributes(item, 'replicationCommand', 'ReplicationModal', index).attributes}>
                                <i class="${getTdAttributes(item, 'replicationCommand', 'ReplicationModal').iconClass}"></i>
                            </td>
                            <td                                    
                                ${getTdAttributes(item, 'infraObjectCommand', 'InfraObjectModal', index).attributes}>
                                <i class="${getTdAttributes(item, 'infraObjectCommand', 'InfraObjectModal').iconClass}"></i>
                            </td>
                            <td>
                                ${item.isSwitchOver === true ? workflowSuccess : workflowError}
                            </td>
                            <td>
                                ${item.isSwitchBack === true ? workflowSuccess : workflowError}
                            </td>
                            <td>
                                ${item.isFailOver === true ? workflowSuccess : workflowError}
                            </td>
                            <td>
                                ${item.isFailBack === true ? workflowSuccess : workflowError}
                            </td>
                            <td><i role="button" title="Delete" data-bs-toggle="modal" data-bs-target="#DeleteRow" data-infra-name="${item?.infraObjectName}" data-row-id="${index}" class="cp-Delete delete-Row text-danger"></i></td>                           
                        </tr>                       
                    `;
                        validationLists.append(newRow);
                    });
                    $(".checkBoxBulkImport").on("change", function () {
                        if ($(".checkBoxBulkImport:checked").length === $(".checkBoxBulkImport").length) {
                            $("#flexCheckDefault").prop("checked", true);
                            //enableDelete();
                        }
                        $(".checkBoxBulkImport").each(function () {
                            //if ($(".checkBoxBulkImport:checked").length < 2) {
                            //    disableDelete();
                            //}
                            //if ($(".checkBoxBulkImport:checked").length > 1) {
                            //    enableDelete();
                            //}
                            if (!$(this).is(":checked")) {
                                $("#flexCheckDefault").prop("checked", false);
                                //return false;
                            }
                        });
                    });
                } else { //if  bulkImportObject?.bulkImportOperationList is empty after delete all row in validation page
                    removeUploadedFile();
                    $("#closeOffCanvas").trigger("click");
                    notificationAlert("warning", "The configuration fields are missing in the uploaded document. Please re-upload the file with the complete or required information.");
                }
            } else {
                removeUploadedFile();
                notificationAlert("warning", "The configuration fields are missing in the uploaded document. Please re-upload the file with the complete or required information.");
            }
        }
    });
}

async function runnigStatus() {
    await $.ajax({
        type: "GET",
        url: RootUrl + "Configuration/BulkImport/GetBulkImportOperationsrunningStatus",
        dataType: "json",
        headers: {
            'RequestVerificationToken': await gettoken()
        },
        success: function (response) {
            if (response?.data?.length > 0) {
                if (!intervalSet) {
                    setTimeout(() => {
                        $("#InsetDBModal").modal("show");                       
                    }, 1500);
                    intervalSet = true; // Set the flag to true
                    bulkImportSetIntervalOne = setInterval(() => {
                        getResult();
                    }, 3000);
                }
            }
        }
    });
}

function replaceEmptyStrings(array) {
    let serviceLabel;
    for (let i = 0; i < array[0].length; i++) {
        let sub = array[0][i]
        if (sub !== undefined && sub !== null && sub.trim() !== "") {
            serviceLabel = sub.trim();
        } else {
            array[0][i] = serviceLabel;
        }
    }
    return array;
}

async function insertBulkImportToDB() {  
    allowInsertToDB = false;

    validatorResponse.forEach(function (data, index) {
        bulkImportObject.bulkImportOperationList.forEach(function (bulkdata, childindex) {
            bulkdata["failBackTemplate"] = data.failBackTemplate;
            bulkdata["failOverTemplate"] = data.failOverTemplate;
            bulkdata["switchBackTemplate"] = data.switchBackTemplate;
            bulkdata["switchOverTemplate"] = data.switchOverTemplate;
        });
    });

    bulkImportObject.bulkImportOperationList.forEach(function (bulkdata, childindex) {
        // Parse the switchBackTemplate
        const parsedTemplate = JSON.parse(bulkdata?.switchBackTemplate);
        parsedTemplate?.nodes?.forEach(function (nodeData) {
            if (nodeData.actionInfo) {
                let props = {};
                if (nodeData.actionInfo.properties) {
                    nodeData.actionInfo.formInput.forEach(function (formData, formindex) {
                        bulkdata.serverList.forEach(function (serverData, serverindex) {
                            if (serverData.type === "PR") {
                                if (formData.name.toLowerCase().includes('prserver')) {
                                    props[formData.name] = "@" + serverData.name;
                                }
                            }
                            if (serverData.type === "DR") {
                                if (formData.name.toLowerCase().includes('drserver')) {
                                    props[formData.name] = "@" + serverData.name;
                                }
                            }
                        });
                        bulkdata.databaseList.forEach(function (databaseData, databaseindex) {
                            if (databaseData.type === "PR") {
                                if (formData.name.toLowerCase().includes('prdb')) {
                                    props[formData.name] = "@" + databaseData.name;
                                }
                            }
                            if (databaseData.type === "DR") {
                                if (formData.name.toLowerCase().includes('drdb')) {
                                    props[formData.name] = "@" + databaseData.name;
                                }
                            }
                        });
                        bulkdata.replicationList.forEach(function (replicationData, replicationindex) {
                            if (replicationData.replType === "PR") {
                                if (formData.name.toLowerCase().includes('prreplication')) {
                                    props[formData.name] = "@" + replicationData.name;
                                }
                            }
                            if (replicationData.replType === "DR") {
                                if (formData.name.toLowerCase().includes('drreplication')) {
                                    props[formData.name] = "@" + replicationData.name;
                                }
                            }
                        });
                    });
                    nodeData.actionInfo.properties = props;
                }
            }
        });

        // Update the original bulkImportObject with the modified template
        bulkdata.switchBackTemplate = JSON.stringify(parsedTemplate);
    });

    async function createBulkImport() {
        await $.ajax({
            type: "POST",
            url: RootUrl + "Configuration/BulkImport/SaveBulkImport",
            dataType: "json",
            headers: {
                'RequestVerificationToken': await gettoken()
            },
            contentType: 'application/json',
            data: JSON.stringify(bulkImportObject),
            success: function (response) {
                if (response.success) {
                    bulkImportObject = '';
                    bulkImportSetIntervalTwo = setInterval(() => {
                        getResult();
                    }, 3000);
                } else {
                    allowInsertToDB = true;
                    removeUploadedFile();
                    errorNotification(response);
                    $("#insertBulkImport").removeClass('d-none');
                }
            }
        });
    }
    createBulkImport();
}

async function IsNameExist(url, data, errorFunc) {
    const names = ['serverName', 'databaseName', 'replicationName', 'infraObjectName'];
    for (const name of names) {
        if (data[name]) {
            const trimmedName = data[name]?.trim();
            if (!trimmedName) return true;
            if (await GetAsync(url, data, errorFunc)) return "A same name already exists";
        }
    }
    return true;
}

async function GetAsync(url, data, errorFunc) {
    return await $.get(url, data).fail(errorFunc);
}

async function commonNameValidation(value, nameExistURL, errorele, errortext, nameexistsdata) {
    if (!value) {
        errorele.text(errortext).addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        errorele.text('Special characters not allowed').addClass('field-validation-error');
        return false;
    }
    const url = RootUrl + nameExistURL;
    let data = nameexistsdata;
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsNameExist(url, data, OnError)
    ];
    return await CommonValidation(errorele, validationResults);
}

function setupValidation(selector, url, errorElement, errorText, nameKey) {
    $(selector).on("keyup", commonDebounce(async function () {
        let data = { id: null, [nameKey]: $(this).val() };
        commonNameValidation($(this).val(), url, $(errorElement), errorText, data);
    }));
}

function descriptionValidation(value) {
    if (!value) {
        $('#Description-Error').text("Enter description").addClass('field-validation-error');
        return false;
    }
    if (value.length < 3 || value.length > 250) {
        $('#Description-Error').text("Between 3 to 250 characters").addClass('field-validation-error');
        return false;
    }
    $('#Description-Error').text("").removeClass('field-validation-error');
    return true;
}

function handleFiles(file) {
    allowInsertToDB = true;
    bulkImportObject = "";
    validationResponse = "";
    const filename = file?.name;
    if (!isXlsxFile(filename)) {
        $("#fileInput").val("");
        $("#drop-zone").css({
            "font-weight": "bold"
        });
        setTimeout(() => {
            $("#drop-zone").css({ "font-weight": "normal" });
        }, 2000)
    }
    $("#fileName").html("File Name");
    if (!file || !isXlsxFile(filename)) return;
    $("#fileName").html(filename);

    const reader = new FileReader();
    reader.onload = async () => {
        try {
            const reader = new FileReader();
            reader.onload = function (e) {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                const isNonEmpty = (row) => row.some(cell => typeof cell === 'string' ? cell.trim() !== '' : cell !== null && cell !== undefined);
                const nonEmptyRows = rows.filter(isNonEmpty);
                //const filteredDatas = nonEmptyRows.slice(3);
                //const resultDatas = filteredDatas.slice(0, -1);
                let updatedArray = replaceEmptyStrings(nonEmptyRows);
                let lastIndexValue = updatedArray[0][updatedArray[0].length - 1];
                let difference = updatedArray[1].length - updatedArray[0].length;
                let additionalConfigs = new Array(difference).fill(lastIndexValue);
                let modifiedArray = updatedArray[0].concat(additionalConfigs);
                updatedArray[0] = modifiedArray;

                bulkImportObject = { description: "string", infraObjectName: "string", bulkImportOperationList: [] };
                let arrayLength = updatedArray.length;
                const promises = [];
                for (let i = 2; i < arrayLength; i++) {
                    let excelToJSON = excelToJson(updatedArray, i, arrayLength);
                    bulkImportObject.bulkImportOperationList.push(excelToJSON);
                    const percentage = ((i - 1) / (arrayLength - 2)) * 100;
                    let roundedNum = Math.round(percentage);
                    const progressBar = document.getElementById('dynamicProgressBar');
                    const promise = new Promise((resolve) => {
                        let currentWidth = 0;
                        const interval = setInterval(() => {
                            if (currentWidth < roundedNum - 5) { //to stop at 95%
                                currentWidth++;
                                progressBar.style.width = `${currentWidth}%`;
                                $("#percentageProgressBar").html(`${currentWidth}%`);
                            } else {
                                clearInterval(interval);
                                if (i === arrayLength - 1) {
                                    progressBar.style.width = '95%'; //100
                                    $("#percentageProgressBar").html(`95%`); //100
                                }
                                resolve(); // Resolve the promise when done
                            }
                        }, 50);
                    });
                    promises.push(promise);
                }
                Promise.all(promises).then(() => {
                    //console.log(bulkImportObject);
                    bulkImportValidation();
                });
            };
            reader.readAsArrayBuffer(file);
        } catch (error) {
            console.error('Error reading Excel file:', error);
        }
    };
    reader.onerror = () => {
        console.error('Error reading file');
    };
    reader.readAsArrayBuffer(file); // or readAsText(file) 
}

const loadActiveInActive = (result) => {
    let html = '';

    if (result?.activeNodes && Array.isArray(result?.activeNodes) && result?.activeNodes?.length) {
        for (let i = 0; i < result?.activeNodes?.length; i++) {
            html += `<div class='mb-1'><i class="cp-network fs-4 text-success" ></i> '${result.activeNodes[i]}'</div>`;
        }
    }

    if (result?.inActiveNodes && Array.isArray(result?.activeNodes) && result?.inActiveNodes?.length) {
        for (let i = 0; i < result?.inActiveNodes?.length; i++) {
            html += `<div class='mb-1'><i class="cp-network fs-4 text-danger" ></i> '${result.inActiveNodes[i]}'</div>`;
        }
    }

    return html;
}
