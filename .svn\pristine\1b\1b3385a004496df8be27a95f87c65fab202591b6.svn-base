﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IBusinessServiceRepository : IRepository<BusinessService>
{
    Task<List<BusinessService>> GetBusinessServiceNames();
    Task<bool> IsBusinessServiceNameExist(string name, string id);
    Task<bool> IsBusinessServiceNameUnique(string name);
    Task<IReadOnlyList<BusinessService>> GetBusinessServicesBySiteId(string siteId);
    Task<BusinessService> GetFilterByReferenceIdAsync(string id);
    Task<IReadOnlyList<BusinessService>> GetByReferenceIdsAsync(List<string> ids);
    Task<IReadOnlyList<BusinessService>> GetBySiteIds(List<string> siteIds);
}