﻿@using DevExpress.AspNetCore
@using ContinuityPatrol.Web.Areas.Report.ReportTemplate

<style>
    .form-group {
        margin-left: 550px;
        margin-top: 60px;
    }
</style>
@section Styles
    {
    <link href="~/css/viewer.part.bundle.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/thirdparty.bundle.css" rel="stylesheet" asp-append-version="true" />
    }
@section HeaderScripts
    {
    <script src="~/js/thirdparty.bundle.js" asp-append-version="true"></script>
    <script src="~/js/viewer.part.bundle.js" asp-append-version="true"></script>
    }

<div class="card card-custom gutter-b">
    <div class="card-body">
        <div id="reportContainer">
           @*  @Html.DevExpress().WebDocumentViewer("DocumentViewer").Height("1150px").Bind(new Alert()); *@
        </div>
    </div>
</div>