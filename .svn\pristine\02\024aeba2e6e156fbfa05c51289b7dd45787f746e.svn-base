﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class RiskMitigationRepositoryMocks
{
    public static Mock<IRiskMitigationRepository> CreateRiskMitigationRepository(List<RiskMitigation> riskMitigations)
    {
        var riskMitigationRepository = new Mock<IRiskMitigationRepository>();

        riskMitigationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(riskMitigations);

        riskMitigationRepository.Setup(repo => repo.AddAsync(It.IsAny<RiskMitigation>())).ReturnsAsync(
            (RiskMitigation riskMitigation) =>
            {
                riskMitigation.Id = new Fixture().Create<int>();

                riskMitigation.ReferenceId = new Fixture().Create<Guid>().ToString();

                riskMitigations.Add(riskMitigation);

                return riskMitigation;
            });

        return riskMitigationRepository;
    }

    public static Mock<IRiskMitigationRepository> UpdateRiskMitigationRepository(List<RiskMitigation> riskMitigations)
    {
        var riskMitigationRepository = new Mock<IRiskMitigationRepository>();

        riskMitigationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(riskMitigations);

        riskMitigationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => riskMitigations.SingleOrDefault(x => x.ReferenceId == i));

        riskMitigationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<RiskMitigation>())).ReturnsAsync((RiskMitigation riskMitigation) =>
        {
            var index = riskMitigations.FindIndex(item => item.ReferenceId == riskMitigation.ReferenceId);

            riskMitigations[index] = riskMitigation;

            return riskMitigation;

        });
        return riskMitigationRepository;
    }

    public static Mock<IRiskMitigationRepository> DeleteRiskMitigationRepository(List<RiskMitigation> riskMitigations)
    {
        var riskMitigationRepository = new Mock<IRiskMitigationRepository>();

        riskMitigationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(riskMitigations);

        riskMitigationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => riskMitigations.SingleOrDefault(x => x.ReferenceId == i));

        riskMitigationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<RiskMitigation>())).ReturnsAsync((RiskMitigation riskMitigation) =>
        {
            var index = riskMitigations.FindIndex(item => item.ReferenceId == riskMitigation.ReferenceId);

            riskMitigation.IsActive = false;

            riskMitigations[index] = riskMitigation;

            return riskMitigation;
        });

        return riskMitigationRepository;
    }

    public static Mock<IRiskMitigationRepository> GetRiskMitigationRepository(List<RiskMitigation> riskMitigations)
    {
        var riskMitigationRepository = new Mock<IRiskMitigationRepository>();

        riskMitigationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(riskMitigations);

        riskMitigationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => riskMitigations.SingleOrDefault(x => x.ReferenceId == i));

        return riskMitigationRepository;
    }

    public static Mock<IRiskMitigationRepository> GetRiskMitigationEmptyRepository()
    {
        var riskMitigationRepository = new Mock<IRiskMitigationRepository>();

        riskMitigationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<RiskMitigation>());

        return riskMitigationRepository;
    }

    public static Mock<IRiskMitigationRepository> GetPaginatedRiskMitigationRepository(List<RiskMitigation> riskMitigations)
    {
        var riskMitigationRepository = new Mock<IRiskMitigationRepository>();

        var queryableRiskMitigation = riskMitigations.BuildMock();

        riskMitigationRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableRiskMitigation);

        return riskMitigationRepository;
    }
}