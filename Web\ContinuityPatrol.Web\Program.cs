using ContinuityPatrol.Application.Extensions;
using ContinuityPatrol.Application.Hubs;
using ContinuityPatrol.Infrastructure.Extension;
using ContinuityPatrol.Persistence.Extensions;
using ContinuityPatrol.Services.Api.Extension;
using ContinuityPatrol.Services.Db.Extension;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Hubs;
using ContinuityPatrol.Shared.Infrastructure.Middlewares;
using ContinuityPatrol.Shared.Services.Extension;
using ContinuityPatrol.Web.Helper;
using ContinuityPatrol.Web.Middlewares;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Serilog.Debugging;

// Configuration and Logging Setup

var config = new ConfigurationBuilder()
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", true)
    .Build();

var level = config["Logging:LogLevel:Default"] ?? "information";

var levelSwitch = new LoggingLevelSwitch(GetMinimumLevel(level));

LogEventLevel GetMinimumLevel(string minimumLevel)
{
    minimumLevel = minimumLevel.ToLower();

    return minimumLevel switch
    {
        "debug" => LogEventLevel.Debug,
        "information" => LogEventLevel.Information,
        "error" => LogEventLevel.Error,
        _ => LogEventLevel.Information
    };
}

var selfLogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "serilog-selflog.txt");
Directory.CreateDirectory(Path.GetDirectoryName(selfLogPath)!);

SelfLog.Enable(msg =>
{
    File.AppendAllText(selfLogPath, msg + Environment.NewLine);
});

Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(config)
    .MinimumLevel.ControlledBy(levelSwitch)
    .Filter.ByExcluding(evt => evt.MessageTemplate.Text.Contains("AspNetCoreThreadingTimerStoragesCleaner.StartCore"))
    .Filter.ByExcluding("SourceContext = 'Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddleware'")
    .Filter.ByExcluding(Matching.FromSource("Microsoft.AspNetCore"))
    .MinimumLevel.Override("Microsoft", LogEventLevel.Error)
    .MinimumLevel.Override("Quartz", LogEventLevel.Error)
    //.Enrich.WithCorrelationId()
    //.Enrich.WithClientIp()
    //.Enrich.FromLogContext()
    .CreateLogger();

Log.Information("=================== Starting Continuity Patrol Web UI ===================");


try
{
    // HibernatingRhinos.Profiler.Appender.EntityFramework.EntityFrameworkProfiler.Initialize();

    var builder = WebApplication.CreateBuilder(args);

    builder.Logging.ClearProviders();
    builder.Logging.AddSerilog(Log.Logger);

    builder.Services.AddHttpContextAccessor();
    builder.Services.AddDistributedMemoryCache();

    // SignalR, Infrastructure, and Core Services
    builder.Services.AddSignalR();
    builder.Services.AddSharedWebInfrastructure(builder.Configuration);
    builder.Services.AddInfrastructure(builder.Configuration);
    builder.Services.AddCore();

    //builder.Services.Configure<IpRateLimitOptions>(builder.Configuration.GetSection("IpRateLimiting"));
    //builder.Services.Configure<IpRateLimitPolicies>(builder.Configuration.GetSection("IpRateLimitPolicies"));

    //// Register in-memory rate limiting services
    //builder.Services.AddInMemoryRateLimiting();

    //// Register rate limit counter store and processing strategy
    //builder.Services.AddSingleton<IRateLimitCounterStore, MemoryCacheRateLimitCounterStore>();
    //builder.Services.AddSingleton<IProcessingStrategy, AsyncKeyLockProcessingStrategy>();

    //// Register rate limit configuration
    //builder.Services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();

    // Persistence and Data Provider
    builder.Services.AddPersistence(builder.Configuration);
    builder.Services.AddHealthCheck(builder.Configuration);

    builder.Services.AddMiniProfiler().AddEntityFramework();

    DXFontRepository.Instance.AddFont(@"wwwroot\fonts\Poppins\Poppins-Medium.ttf");
    DXFontRepository.Instance.AddFont(@"wwwroot\fonts\Poppins\Poppins-Regular.ttf");
    DXFontRepository.Instance.AddFont(@"wwwroot\fonts\Poppins\Poppins-Light.ttf");
    DXFontRepository.Instance.AddFont(@"wwwroot\fonts\Poppins\Poppins-Thin.ttf");
    DXFontRepository.Instance.AddFont(@"wwwroot\fonts\San-Francisco-UI\SFUIText-Regular.ttf");
    DXFontRepository.Instance.AddFont(@"wwwroot\fonts\San-Francisco-UI\SFUIText-Bold.ttf");

    // Essential Services Configuration
    builder.Services.Configure<CookiePolicyOptions>(options =>
    {
        // This lambda determines whether user consent for non-essential cookies is needed for a given request.  
        options.CheckConsentNeeded = _ => true;
        options.MinimumSameSitePolicy = SameSiteMode.None;
    });

    var provider = config["DataProvider:Default"] ?? "db";
    if (provider.Equals("db", StringComparison.OrdinalIgnoreCase))
    {
        builder.Services.AddSharedDbServices(builder.Configuration);
    }
    else
    {
        builder.Services.AddSharedApiServices();
    }
    builder.Services.AddSharedServices();

    var mimeTypes = new HashSet<string>
    { "image/svg+xml", "application/pdf", "application/json", "application/javascript", "application/rtf", "application/xhtml+xml", "application/xml", "text/*", "model/gltf+json"
    };

    builder.Services.AddResponseCompression(options =>
    {
        options.Providers.Add<BrotliCompressionProvider>();
        options.EnableForHttps = true;
        options.MimeTypes = mimeTypes;
    });

    //// Response Compression and Minification
    //builder.Services.AddResponseCompression(options =>
    //{
    //    options.Providers.Add<BrotliCompressionProvider>();
    //    options.EnableForHttps = true;
    //    options.MimeTypes = new[] { "image/svg+xml", "application/pdf", "application/json", "application/javascript", "application/rtf", "application/xhtml+xml", "application/xml", "text/*", "model/gltf+json" }; // NOTE: add MIME-Types to compress here
    //});

    builder.Services.AddWebMarkupMin(options =>
    {
        options.AllowMinificationInDevelopmentEnvironment = true;
        options.AllowCompressionInDevelopmentEnvironment = true;
        options.DisablePoweredByHttpHeaders = true;
    })
    .AddHtmlMinification(options =>
    {
        options.MinificationSettings.RemoveRedundantAttributes = true; options.MinificationSettings.RemoveHttpProtocolFromAttributes = true;
        options.MinificationSettings.RemoveHttpsProtocolFromAttributes = true;
    })
    .AddHttpCompression(options =>
    {
        options.CompressorFactories = new List<ICompressorFactory>
        {
            new BuiltInBrotliCompressorFactory(new BuiltInBrotliCompressionSettings
            {
                Level = CompressionLevel.Optimal
            }),
            new DeflateCompressorFactory(new DeflateCompressionSettings
            {
                Level = CompressionLevel.Optimal
            }),
            new GZipCompressorFactory(new GZipCompressionSettings
            {
                Level = CompressionLevel.Optimal
            })
        };
    });
    var allowedOrigins = config.GetValue<string>("App:CorsOrigins")
        ?.Split(",", StringSplitOptions.RemoveEmptyEntries)
        .ToArray();
    // Security Headers, HTTPS Redirection, and CORS
    //builder.Services.AddCors(
    //    options => options.AddPolicy("CPCorsPolicy",
    //        b =>
    //        {
    //            if (allowedOrigins != null)
    //                b.WithOrigins(allowedOrigins)
    //                    .AllowAnyHeader()
    //                    .AllowAnyMethod()
    //                    .AllowCredentials();
    //        }));

    builder.Services.AddCors(options =>
    options.AddPolicy("CPCorsPolicy", b =>
    {
        if (allowedOrigins != null)
        {
            b.WithOrigins(allowedOrigins)
             .AllowAnyHeader()
             .AllowAnyMethod()
             .AllowCredentials()
             .SetIsOriginAllowedToAllowWildcardSubdomains()
             .WithExposedHeaders("Cross-Origin-Embedder-Policy", "Cross-Origin-Opener-Policy");
        }
    })
);

    builder.Services.AddHsts(options =>
    {
        options.Preload = true;
        options.IncludeSubDomains = true;
        options.MaxAge = TimeSpan.FromDays(365); // 1 Year
    });

    builder.Services.AddAntiforgery(options =>
    {
        options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
    });
    // Session and MVC
    builder.Services.AddSession(opts =>
    {
        opts.IdleTimeout = TimeSpan.FromDays(1);
        opts.Cookie.HttpOnly = true;
        opts.Cookie.IsEssential = true; // make the session cookie Essential
        opts.Cookie.SecurePolicy = CookieSecurePolicy.Always;
        opts.Cookie.SameSite = SameSiteMode.Strict;
        opts.Cookie.Path = "/";
        opts.Cookie.Name = "__Host-Session";
    });

    //DevExpressReport
    builder.Services.AddDevExpressControls();

    builder.Services.AddControllersWithViews();
    builder.Services.AddMvc()
        .AddRazorRuntimeCompilation()
        .AddSessionStateTempDataProvider();

    builder.Services.AddControllers();

    var app = builder.Build();

    app.UseSession();

    // Exception Handling and Security Headers
    if (!app.Environment.IsDevelopment())
    {
        app.UseExceptionHandler("/Error");
        app.UseStatusCodePagesWithReExecute("/Error/{0}");
        app.UseHsts();
    }
    app.UseMiddleware<ApiKeyMiddleware>();
    app.UseMiddleware<ErrorHandlingMiddleware>();

    app.UseHttpsRedirection();

    app.UseAntiXssMiddleware();

    //DevExpressReport
    app.UseDevExpressControls();

    // CORS and Routing
    app.UseCors("CPCorsPolicy");

    app.Use(async (context, next) =>
    {
        context.Response.Headers.Append("Cross-Origin-Embedder-Policy", "require-corp");
        context.Response.Headers.Append("Cross-Origin-Opener-Policy", "same-origin");
        context.Response.Headers.Append("Cross-Origin-Resource-Policy", "same-origin");
        await next();
    });

    app.UseStaticFiles(new StaticFileOptions()
    {
        HttpsCompression = Microsoft.AspNetCore.Http.Features.HttpsCompressionMode.Compress,
        OnPrepareResponse = (context) =>
        {
            var headers = context.Context.Response.GetTypedHeaders();
            headers.CacheControl = new CacheControlHeaderValue
            {
                Public = true,
                MaxAge = TimeSpan.FromDays(30)
            };
        }
    });



    app.UseSecurityHeaders(policies => policies
        .AddDefaultSecurityHeaders()
        .RemoveServerHeader()
        .RemoveCustomHeader("X-Powered-By")
        .RemoveCustomHeader("Server")
    );

    if (!app.Environment.EnvironmentName.Equals("Testing"))
    {
        app.UseRefererHeaderMiddleware();
    }

    app.UseRemoveReturnUrlMiddleware();

    app.UseHsts(options => { options.MaxAge(365).IncludeSubdomains().Preload(); });
    app.UseHttpsRedirection();

    app.UseXContentTypeOptions();
    app.UseXXssProtection(option => { option.EnabledWithBlockMode(); });
    app.UseXfo(option => { option.SameOrigin(); });
    app.UseReferrerPolicy(opts => opts.NoReferrer());
    app.UseNoCacheHttpHeaders();
    app.UseCsp(options =>
    {
        options.BlockAllMixedContent()
            .ScriptSources(s => s.Self())
            .StyleSources(s => s.Self())
            .StyleSources(s => s.UnsafeInline())
            .FontSources(s => s.Self())
            .FormActions(s => s.Self())
            .FrameAncestors(s => s.Self())
            .ImageSources(s => s.Self());
    });
    app.UseXDownloadOptions();
    app.UseXRobotsTag(options => options.NoIndex().NoFollow());

    app.UseRouting();

    // Response Compression and Static Files
    app.UseResponseCompression();
    app.UseStaticHttpContext();
    // Cookie Policy and Additional Middleware (e.g., Anti-XSS)
    app.UseCookiePolicy();
    app.UseAuthentication();
    app.UseAuthorization();

    if (app.Environment.EnvironmentName.Equals("Testing"))
    {
        app.UseMiniProfiler();
    }

    app.UseMiddleware<SerialogUIMiddleware>();

    app.UsePreventMultipleLogin();

    app.UseWebMarkupMin();

    app.MapHub<AlertHub>("/alerthub");

    app.MapHub<WorkflowHub>("/workflowhub");

    app.MapHub<WorkflowActionResultHub>("/workflowactionresulthub");

    app.MapHub<LogHub>("/loghub");

    app.MapHub<NotificationHub>("/notificationhub");

    app.MapHealthChecks("/health",new HealthCheckOptions
    {
        ResponseWriter = HealthCheckResponseWriter.WriteJsonResponse
    });

    // Endpoint Mapping
    app.UseEndpoints(endpoints =>
    {
        endpoints.MapControllerRoute(
            name: "areas",
            pattern: "{area:exists}/{controller=Account}/{action=Login}/{id?}");

        endpoints.MapControllerRoute(
            name: "default",
            pattern: "{controller=Account}/{action=Login}/{id?}");

        endpoints.MapRazorPages();

        endpoints.MapControllers();
    });

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex.GetMessage(), "Unhandled exception");
}
finally
{
    Log.Information("Shut down complete");
    Log.CloseAndFlush();
}

