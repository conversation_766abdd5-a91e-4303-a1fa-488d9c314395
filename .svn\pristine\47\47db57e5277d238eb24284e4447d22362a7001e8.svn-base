﻿namespace ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetDCMappingBySites;

public class GetDcMappingSitesQueryHandler : IRequestHandler<GetDcMappingSitesQuery, GetDcMappingSitesVm>
{
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IMapper _mapper;
    private readonly ISiteRepository _siteRepository;
    private readonly IInfraDashboardViewRepository _infraDashboardViewRepository;

    public GetDcMappingSitesQueryHandler(IMapper mapper, 
        IBusinessServiceRepository businessServiceRepository,
        ISiteRepository siteRepository, 
        IInfraDashboardViewRepository infraDashboardViewRepository)
    {
        _mapper = mapper;
        _businessServiceRepository = businessServiceRepository;
        _siteRepository = siteRepository;
        _infraDashboardViewRepository = infraDashboardViewRepository;
    }

    public async Task<GetDcMappingSitesVm> Handle(GetDcMappingSitesQuery request, CancellationToken cancellationToken)
    {
        var siteListVm = await _siteRepository.ListAllAsync();

        var siteList = _mapper.Map<GetDcMappingSitesVm>(siteListVm);

        siteList.DcMappingSites.AddRange(_mapper.Map<List<DcMappingSite>>(siteListVm));

        var siteIds = siteListVm
            .Select(x => x.ReferenceId)
            .ToList();

        var businessServiceVm = await _businessServiceRepository.GetBySiteIds(siteIds);

        var businessServiceDto = businessServiceVm.DistinctBy(x => x.ReferenceId).ToList();

        siteList.TotalAppCount += businessServiceDto.Count;

        var businessServiceIds = businessServiceDto.Select(item => item.ReferenceId).ToList();

        var infraDashboardView =
            await _infraDashboardViewRepository.GetBsSitePropertiesByBusinessServiceIds(businessServiceIds);

        var groupByBs = infraDashboardView
            .GroupBy(x => new { x.BusinessServiceId,x.BusinessServiceName,x.BsSiteProperties})
            .Select(x => new DcMappingBusinessService
            {
                BusinessServiceId = x.Key.BusinessServiceId,
                BusinessServiceName = x.Key.BusinessServiceName,
                BsSiteProperties = x.Key.BsSiteProperties,
                DcMappingInfra = x.Select(dcInfra=> new DcMappingInfra
                {
                    InfraObjectId = dcInfra.ReferenceId,
                    InfraObjectName = dcInfra.Name,
                    DrOperationStatus = dcInfra.DROperationStatus,
                    ReplicationStatus = dcInfra.ReplicationStatus
                }).ToList()
            }).ToList();

        siteList.DcMappingBusinessServices.AddRange(groupByBs);

        return siteList;


        //foreach (var bs in groupByBs)
        //{
        //    foreach (var infraObject in bs.InfraDashboardView)
        //    {
        //        if (infraObject.ReferenceId.IsNotNullOrWhiteSpace())
        //        {
        //            var json = JObject.Parse(infraObject.BsSiteProperties);

        //            var propertyNames = GetPropertyNames(json);


        //        }
        //    }
        //}







        //foreach (var infraDashboard in infraDashboardView)
        //{
        //    var json = JObject.Parse(infraDashboard.BsSiteProperties);

        //    var propertyNames = GetPropertyNames(json);

        //    var infraDto = infraDashboardView
        //        .Where(x => x.BusinessServiceId == infraDashboard.BusinessServiceId)
        //        .DistinctBy(x => x.ReferenceId)
        //        .ToList();


        //    foreach (var propertyName in propertyNames)
        //    {
        //        var siteId = json.SelectToken($"['{propertyName}'].Id")?.ToString() ?? "NA";
        //        var siteName = json.SelectToken($"['{propertyName}'].Name")?.ToString() ?? "NA";
        //        var siteLocation = json.SelectToken($"['{propertyName}'].Location")?.ToString() ?? "NA";
        //        var siteLng = json.SelectToken($"['{propertyName}'].Lng")?.ToString() ?? "NA";
        //        var siteLat = json.SelectToken($"['{propertyName}'].Lat")?.ToString() ?? "NA";
        //        var siteType = propertyName;

        //        var dcMappingSite = new DcMappingSite
        //        {
        //            Id = siteId,
        //            Name = siteName,
        //            Location = siteLocation,
        //            Lng = siteLng,
        //            Lat = siteLat,
        //            Type = siteType
        //        };






        //       // var result = await AddOrUpdateValue(sites, siteType, 1, siteLocation, dcMappingSite, infraDto);

        //       // sites.RemoveAll(x => x.Location.Equals(siteLocation));


        //       // sites.Add(result);
        //    }

        //}

        // siteList.DcMappingSites = sites;

        //return siteList;
    }

    //private static List<string> GetPropertyNames(JObject json)
    //{
    //    var propertyNames = new List<string>();
    //    foreach (var property in json.Properties()) propertyNames.Add(property.Name);
    //    return propertyNames;
    //}

    //private Task<DcMappingSite> AddOrUpdateValue(List<DcMappingSite> list, string key, int value, string location,
    //    DcMappingSite dCMappingSite, List<InfraDashboardView> infraDto)
    //{
    //    var dbList = list.FirstOrDefault(x => x.Location.Equals(location));

    //    if (dbList is not null)
    //    {
    //        var siteProp = JsonConvert.DeserializeObject<Dictionary<string, int>>(dbList.Properties);

    //        if (siteProp.ContainsKey(key))
    //        {
    //            siteProp[key] += value;

    //            dCMappingSite.Properties = JsonConvert.SerializeObject(siteProp);

    //            var replicationStatusValues = infraDto
    //                .Where(item => item?.ReplicationStatus != null)
    //                .Select(item => item.ReplicationStatus)
    //                .Distinct().ToList();

    //            dbList.ReplicationStatus.AddRange(replicationStatusValues);

    //            dCMappingSite.ReplicationStatus.AddRange(dbList.ReplicationStatus);

    //            var drOperationStatusValues = infraDto
    //                .Where(item => item?.DROperationStatus != null)
    //                .Select(item => item.DROperationStatus)
    //                .Distinct().ToList();

    //            dbList.DrOperationStatus.AddRange(drOperationStatusValues);

    //            dCMappingSite.DrOperationStatus.AddRange(dbList.DrOperationStatus);

    //            return Task.FromResult(dCMappingSite);
    //        }
    //        else
    //        {
    //            siteProp.Add(key, value);

    //            dCMappingSite.Properties = JsonConvert.SerializeObject(siteProp);

    //            var replicationStatusValues = infraDto
    //                .Where(item => item?.ReplicationStatus != null)
    //                .Select(item => item.ReplicationStatus)
    //                .Distinct().ToList();

    //            dbList.ReplicationStatus.AddRange(replicationStatusValues);

    //            dCMappingSite.ReplicationStatus.AddRange(dbList.ReplicationStatus);

    //            var drOperationStatusValues = infraDto
    //                .Where(item => item?.DROperationStatus != null)
    //                .Select(item => item.DROperationStatus)
    //                .Distinct().ToList();

    //            dbList.DrOperationStatus.AddRange(drOperationStatusValues);

    //            dCMappingSite.DrOperationStatus.AddRange(dbList.DrOperationStatus);

    //            return Task.FromResult(dCMappingSite);
    //        }
    //    }
    //    else
    //    {
    //        var siteProp = new Dictionary<string, int> { { key, value } };

    //        dCMappingSite.Properties = JsonConvert.SerializeObject(siteProp);

    //        var replicationStatusValues = infraDto
    //            .Where(item => item?.ReplicationStatus != null)
    //            .Select(item => item.ReplicationStatus)
    //            .Distinct().ToList();

    //        dCMappingSite.ReplicationStatus.AddRange(replicationStatusValues);

    //        var drOperationStatusValues = infraDto
    //            .Where(item => item?.DROperationStatus != null)
    //            .Select(item => item.DROperationStatus)
    //            .Distinct().ToList();

    //        dCMappingSite.DrOperationStatus.AddRange(drOperationStatusValues);

    //        return Task.FromResult(dCMappingSite);
    //    }
    //}
}