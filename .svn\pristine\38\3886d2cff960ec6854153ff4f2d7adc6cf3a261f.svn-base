﻿let mId = sessionStorage.getItem("monitorId");
let monitortype = 'MssqlAlwaysOnAvailabilityGroup';
let infraObjectId = sessionStorage.getItem("infraobjectId");
setTimeout(() => { AlwaysOnAvailabilityGroupmonitorStatus(mId, monitortype) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
setTimeout(() => { alwaysOnAGServer(infraObjectId) }, 250)

$('#mssqlserver').hide();
async function alwaysOnAGServer(id) {

    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
    let data = {}
    data.infraObjectId = id;
    let mssqlServerData = await getAysncWithHandler(url, data);

    if (mssqlServerData != null && mssqlServerData?.length > 0) {
        mssqlServerData?.forEach(data => {
            let value = data?.isServiceUpdate
            let parsed = []
            if (value && value !== 'NA') parsed = JSON?.parse(value)
            if (Array.isArray(parsed)) {
                parsed?.forEach(s => {
                    if (s?.Services?.length) {
                        $('#mssqlserver').show();
                        bindAGServer(mssqlServerData)
                    }
                })
            }
        })

    } else {
        $('#mssqlserver').hide();
    }

}
function bindAGServer(mssqlServerData) {

    let prType = { IpAddress: '--', Services: [] };
    let drType = { IpAddress: '--', Services: [] };

    // Loop through each item to find PR and DR entries
    mssqlServerData?.forEach(item => {
        let parsedServices = [];
        try {
            const value = item?.isServiceUpdate
            if (value && value !== 'NA') {
                parsedServices = JSON.parse(item?.isServiceUpdate)
            }
        } catch (e) {
            console.error("Invalid JSON in isServiceUpdate:", item?.isServiceUpdate);
        }

        parsedServices?.forEach(serviceGroup => {
            if (serviceGroup?.Type === 'PR') {
                prType.IpAddress = serviceGroup?.IpAddress || prType.IpAddress;
                prType.Services = [...prType.Services, ...(serviceGroup?.Services || [])];
            } else if (serviceGroup?.Type === 'DR') {
                drType.IpAddress = serviceGroup?.IpAddress || drType?.IpAddress;
                drType.Services = [...drType.Services, ...(serviceGroup?.Services || [])];
            }
        });
    });

    // Set header IPs
    $('#prIp').text('Primary (' + prType?.IpAddress + ')');
    $('#drIp').text('DR (' + drType?.IpAddress + ')');

    const prStatusSummary = getStatusSummary(prType.Services.map(s => s.Status).filter(Boolean));
    const drStatusSummary = getStatusSummary(drType.Services.map(s => s.Status).filter(Boolean));
    $('#prIp').html(`Primary (${prType.IpAddress}) <br ><span class="status-summary text-muted small">${prStatusSummary}</span>`);
    $('#drIp').html(`DR (${drType.IpAddress}) <br ><span class="status-summary text-muted small">${drStatusSummary}</span>`);

    // Unique list of all service names from both PR and DR
    let allServiceNames = [...new Set([
        ...prType?.Services?.map(s => s?.ServiceName),
        ...drType?.Services?.map(s => s?.ServiceName)
    ])];

    // Build table rows
    let tbody = $('#mssqlserverbody');
    tbody.empty();

    allServiceNames?.forEach(serviceName => {
        let prService = prType?.Services?.find(s => s?.ServiceName === serviceName);
        let drService = drType?.Services?.find(s => s?.ServiceName === serviceName);

        let prStatus = prService ? prService?.Status : '--';
        let drStatus = drService ? drService?.Status : '--';
        let prIcon = prStatus !== '--' ? `<i class="${getStatusIconClass(prStatus)} me-2 fs-6"></i>` : '';
        let drIcon = drStatus !== '--' ? `<i class="${getStatusIconClass(drStatus)} me-2 fs-6"></i>` : '';
        const iconService = serviceName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";

        let row = `
            <tr>
                <td><i class="${iconService} me-2 fs-6"></i><span>${serviceName}</span></td>
                <td>${prIcon}${prStatus}</td>
                <td>${drIcon}${drStatus}</td>
            </tr>
        `;
        tbody.append(row);
    });
}
function getStatusSummary(arr) {
    let countMap = {};
    arr?.forEach(status => {
        countMap[status] = (countMap[status] || 0) + 1;
    });
    let total = arr?.length;
    let statusSummary = Object.entries(countMap)
        .map(([status, count]) => `${count} ${status}`)
        .join(', ');
    return statusSummary ? `${statusSummary} out of ${total} services` : '--';
}
function getStatusIconClass(status) {
    if (!status) return "text-danger cp-disable";

    const lowerStatus = status.toLowerCase();
    if (lowerStatus === "running") {
        return "text-success cp-reload cp-animate";
    } else if (lowerStatus === "error" || lowerStatus === "stopped") {
        return "text-danger cp-fail-back";
    } else {
        return "text-danger cp-disable";
    }
}


$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})

async function AlwaysOnAvailabilityGroupmonitorStatus(id, type) {
    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
}
let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'

function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}
function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}
function propertiesData(value) {
    if (value === undefined || value === null || value === '') {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
    else {
        let data = JSON?.parse(value?.properties);
        availabilityGroupData(data);
        console.log(data, 'groupData');
        DatabaseDetails(data)
        replicationDetails(data)
    }
}
function availabilityGroupData(data) {
    let prDb = data?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.AvailabilityGroupMonitoring;
    let drDbList = data?.AlwaysOnAvailabilityGroupMonitoringDetails?.[0]?.AlwaysOnAvailabilityGroupMonitoringDetailsList || [];

    const tbody = $('#availabilityGroupTable tbody');
    const thead = $('#availabilityGroupTable thead');
    thead.find('tr.dynamic-header').remove();   
    tbody.empty();

    const fields = [
        { label: 'Instance Name (Database Name)', key: 'InstanceName', icon:'cp-instance-name' },
        { label: 'Availability Group Role', key: 'AvailabilityGroupRole', icon: 'cp-user-role' },
        { label: 'Availability Group Name', key: 'AvailabilityGroupName', icon: 'cp-user-role' },
        { label: 'Replica Mode', key: 'ReplicaMode', icon: 'cp-archive-mode' },
        { label: 'Failover Mode Setting', key: 'FailoverModeSetting', icon: 'cp-left-right' },
        { label: 'Role Allow connections', key: 'RoleAllowConnections', icon: 'cp-cluster-database' },
        { label: 'Availability Group Operational State', key: 'AvailabilityGroupOperationalState', icon: 'cp-online' },
        { label: 'Availability Group Connected State', key: 'AvailabilityGroupConnnectedState', icon: 'cp-connected' }
    ];

    let thRow = '<tr class="dynamic-header"><th>Server Name</th>'; // First empty header for row labels
    thRow += `<th>${data?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.ServerName || 'Primary'}</th>`; // PR
    drDbList.forEach(dr => {
        thRow += `<th>${dr?.ServerName || 'DR'}</th>`; // DRs
    });
    thRow += '</tr>';
    thead.append(thRow);

    const formatArray = (val) => Array.isArray(val) ? val : [val ?? ''];
    const prInstances = formatArray(prDb?.['InstanceName']);
    const drInstancesList = drDbList.map(dr =>
        formatArray(dr?.AvailabilityGroupMonitoring?.['InstanceName'])
    );
    const allInstanceLengths = [prInstances.length, ...drInstancesList.map(arr => arr.length)];
    const maxInstanceRows = Math.max(...allInstanceLengths);

    for (let i = 0; i < maxInstanceRows; i++) {
        let row = '<tr>';        
        if (i === 0) {
            row += `<td class="text-truncate" title="${fields[i].label}" rowspan="${maxInstanceRows}"><i class="text-secondary me-1 fs-6 ${fields[i].icon}"></i>Instance Name (Database Name)</td>`;
        }

        row += `<td title="${prInstances[i] || ''}">${prInstances[i] || ''}</td>`;

        for (let dr of drInstancesList) {
            row += `<td title="${dr[i] || ''}">${dr[i] || ''}</td>`;
        }

        row += '</tr>';
        tbody.append(row);
    }

    for (let i = 1; i < fields.length; i++) {
        const field = fields[i];

        const prVal = prDb?.[field.key] ?? '';
        const drVals = drDbList.map(dr => dr?.AvailabilityGroupMonitoring?.[field.key] ?? '');

        let row = `<tr>
        <td class="text-truncate" title="${field.label}"><i class="text-secondary me-1 fs-6 ${field.icon}"></i>${field.label}</td>
        <td title="${prVal}">${prVal}</td>`;

        // Add dynamic number of DR values
        drVals.forEach(val => {
            row += `<td title="${val}">${val}</td>`;
        });

        row += '</tr>';
        tbody.append(row);
    }
}
function DatabaseDetails(data) {
    const prDbLevel = data?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.DatabaseLevelMonitoring || {};
    const prDB = Array.isArray(prDbLevel.DatabaseDetails)
        ? prDbLevel.DatabaseDetails
        : prDbLevel.DatabaseDetails
            ? [prDbLevel.DatabaseDetails]
            : [];
    const prEndpointPort = prDbLevel.EndpointPortNumber ?? '-';

    const drList = data?.AlwaysOnAvailabilityGroupMonitoringDetails?.[0]?.AlwaysOnAvailabilityGroupMonitoringDetailsList || [];
    let drDbList = data?.AlwaysOnAvailabilityGroupMonitoringDetails?.[0]?.AlwaysOnAvailabilityGroupMonitoringDetailsList || [];

    const drDBs = drList.map(dr => {
        const dbLevel = dr?.DatabaseLevelMonitoring || {};
        return {
            dbList: Array.isArray(dbLevel.DatabaseDetails)
                ? dbLevel.DatabaseDetails
                : dbLevel.DatabaseDetails
                    ? [dbLevel.DatabaseDetails]
                    : [],
            endpointPort: dbLevel.EndpointPortNumber ?? '-'
        };
    });

    const tbody = document.querySelector('#databaseTable tbody');
    const thead = $('#databaseTable thead');
    thead.find('tr.dynamic-header').remove(); 
    if (!tbody) return;
    tbody.innerHTML = '';

    let thRow = '<tr class="dynamic-header"><th>Server Name</th>'; // First empty header for row labels
    thRow += `<th>${data?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.ServerName || 'Primary'}</th>`; // PR
    drDbList.forEach(dr => {
        thRow += `<th>${dr?.ServerName || 'DR'}</th>`; // DRs
    });
    thRow += '</tr>';
    thead.append(thRow);

    const rowCount = Math.max(prDB.length, ...drDBs.map(d => d.dbList.length), 1);

    const fields = [
        {
            key: 'DatabaseSynchroniztionState',
            label: 'Database Synchronization State',
            icon: 'cp-mysql-data'
        },
        {
            key: 'DatabaseSynchroniztionHealthStatus',
            label: 'Database Synchronization Health Status',
            icon: 'cp-mysql-data'
        },
        {
            key: 'DatabaseState',
            label: 'Database State',
            icon: 'cp-mysql-data'
        },
        {
            key: 'DataSyncStateAvailabilityDatabase',
            label: 'Data synchronization State on availability database, Reason if Suspended',
            icon: 'cp-mysql-data'
        }
    ];

    // Loop for other database fields (excluding EndpointPortNumber)
    fields.forEach(field => {
        for (let i = 0; i < rowCount; i++) {
            const tr = document.createElement('tr');

            if (i === 0) {
                const labelCell = document.createElement('td');
                labelCell.setAttribute('rowspan', rowCount);
                labelCell.innerHTML = `<i class="text-secondary me-1 fs-6 ${field.icon}"></i><span title="${field.label}">${field.label}</span>`;
                tr.appendChild(labelCell);
            }

            // PR value
            const val = prDB[i]?.[field.key] ?? '-';
            const dbName = prDB[i]?.DatabaseName ?? '-';
            const cell = document.createElement('td');
            cell.title = `${dbName} (${val})`;
            cell.textContent = `${dbName} (${val})`;
            tr.appendChild(cell);

            // DR values
            drDBs.forEach(({ dbList }) => {
                const dbItem = dbList[i] || {};
                const drVal = dbItem?.[field.key] ?? '-';
                const drName = dbItem?.DatabaseName ?? '-';
                const drCell = document.createElement('td');
                drCell.title = `${drName} (${drVal})`;
                drCell.textContent = `${drName} (${drVal})`;
                tr.appendChild(drCell);
            });

            tbody.appendChild(tr);
        }
    });

    // ✅ Add EndpointPortNumber as a single row (no db name, no loop)
    const tr = document.createElement('tr');
    const labelCell = document.createElement('td');
    labelCell.innerHTML = `<i class="text-secondary me-1 fs-6 cp-endpoint-port-number"></i>Endpoint Port Number`;
    tr.appendChild(labelCell);

    const prCell = document.createElement('td');
    prCell.title = prEndpointPort;
    prCell.textContent = prEndpointPort;
    tr.appendChild(prCell);

    drDBs.forEach(({ endpointPort }) => {
        const cell = document.createElement('td');
        cell.title = endpointPort;
        cell.textContent = endpointPort;
        tr.appendChild(cell);
    });

    tbody.appendChild(tr);
}
function replicationDetails(data) {
    const prDBRaw = data?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.ReplicationMonitoring || {};
    const prDB = Array.isArray(prDBRaw) ? prDBRaw : [prDBRaw];
    // Handle DR DatabaseDetails (can be object or array per DR)
    const drList = data?.AlwaysOnAvailabilityGroupMonitoringDetails?.[0]?.AlwaysOnAvailabilityGroupMonitoringDetailsList || [];
    const drDBs = drList.map(item => {
        const db = item?.ReplicationMonitoring || [];
        return Array.isArray(db) ? db : [db]; // ensure it's always an array
    });
    let drDbList = data?.AlwaysOnAvailabilityGroupMonitoringDetails?.[0]?.AlwaysOnAvailabilityGroupMonitoringDetailsList || [];

    const tbody = document.querySelector('#replicationTable tbody');
    if (!tbody) return;
    const thead = $('#replicationTable thead');
    thead.find('tr.dynamic-header').remove();
    tbody.innerHTML = '';

    let thRow = '<tr class="dynamic-header"><th>Server Name</th>'; // First empty header for row labels
    thRow += `<th>${data?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.ServerName || 'Primary'}</th>`; // PR
    drDbList.forEach(dr => {
        thRow += `<th>${dr?.ServerName || 'DR'}</th>`; // DRs
    });
    thRow += '</tr>';
    thead.append(thRow);

    const rowCount = Math.max(prDB.length, ...drDBs.map(db => db.length), 1); // Max of all DB lengths
    const rows =[
        {
            field: 'LastSentLSN',
            label: 'Last Sent LSN',
            icon: 'cp-last-copied-transaction'
        },
        {
            field: 'LastReceivedLSN',
            label: 'Last Received LSN',
            icon: 'cp-last-copied-transaction'
        },
        {
            field: 'LastRedoneLSN',
            label: 'Last Redone LSN',
            icon: 'cp-last-copied-transaction'
        },
        {
            field: 'LastCommitLSN',
            label: 'Last Commit LSN',
            icon: 'cp-last-copied-transaction'
        },
        {
            field: 'LastSentTime',
            label: 'Last Sent Time',
            icon: 'cp-table-clock'
        },
        {
            field: 'LastReceivedTime',
            label: 'Last Received Time',
            icon: 'cp-table-clock'
        },
        {
            field: 'LastRedoneTime',
            label: 'Last Redone Time',
            icon: 'cp-table-clock'
        },
        {
            field: 'LastCommitTime',
            label: 'Last Commit Time',
            icon: 'cp-table-clock'
        },
        {
            field: 'LogSendQueueSize',
            label: 'Log Send Queue Size (KB)',
            icon: 'cp-log-file-name'
        },
        {
            field: 'RedoQueueSize',
            label: 'Redo Queue Size (KB)',
            icon: 'cp-last-copied-transaction'
        },
        {
            field: 'Datalag',
            label: 'Datalag',
            icon: 'cp-time'
        },
       
    ];

    rows.forEach((rowType) => {
        for (let i = 0; i < rowCount; i++) {
            const tr = document.createElement('tr');

            // Add label only for the first row of each rowType
            if (i === 0) {
                const td = document.createElement('td');
                td.setAttribute('rowspan', rowCount);
                td.innerHTML = `<i class="text-secondary me-1 fs-6 ${rowType.icon}"></i><span title="${rowType.label}">${rowType.label}`;
                tr.appendChild(td);
            }

            // PR database value
            const prItem = prDB[i] || {};
            const prValue = prItem?.[rowType.field] ?? '-';
            const prName = prItem?.DatabaseName ?? '-';
            const prCell = document.createElement('td');
            prCell.title = `${prName} (${prValue})`;
            prCell.innerHTML = `${prName} (${prValue})`;
            tr.appendChild(prCell);

            // DR databases values
            drDBs.forEach(drDB => {
                const drItem = drDB[i] || {};
                const drValue = drItem?.[rowType.field] ?? '-';
                const drName = drItem?.DatabaseName ?? '-';
                const drCell = document.createElement('td');
                drCell.title = `${drName} (${drValue})`;
                drCell.innerHTML = `${drName} (${drValue})`;
                tr.appendChild(drCell);
            });

            tbody.appendChild(tr);
        }
    });

}
