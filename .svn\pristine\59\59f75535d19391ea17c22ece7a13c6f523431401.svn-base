﻿using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceHealthLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessServiceHealthLog.Queries;

public class GetBusinessServiceHealthLogPaginatedListQueryHandlerTests : IClassFixture<BusinessServiceHealthLogFixture>
{
    private readonly Mock<IBusinessServiceHealthLogRepository> _mockBusinessServiceHealthLogRepository;
    private readonly GetBusinessServiceHealthLogPaginatedListQueryHandler _handler;

    public GetBusinessServiceHealthLogPaginatedListQueryHandlerTests(BusinessServiceHealthLogFixture businessServiceHealthLogFixture)
    {
        _mockBusinessServiceHealthLogRepository = BusinessServiceHealthLogRepositoryMocks.GetPaginatedBusinessServiceHealthLogRepository(businessServiceHealthLogFixture.BusinessServiceHealthLogs);

        _handler = new GetBusinessServiceHealthLogPaginatedListQueryHandler(_mockBusinessServiceHealthLogRepository.Object, businessServiceHealthLogFixture.Mapper);

        businessServiceHealthLogFixture.BusinessServiceHealthLogs[0].ProblemState = "Testing";
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetBusinessServiceHealthLogPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<BusinessServiceHealthLogListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_PaginatedBusinessServiceHealthLogs_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetBusinessServiceHealthLogPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Testing" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<BusinessServiceHealthLogListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<BusinessServiceHealthLogListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].ProblemState.ShouldBe("Testing");
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetBusinessServiceHealthLogPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABC" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<BusinessServiceHealthLogListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetBusinessServiceHealthLogPaginatedListQuery(), CancellationToken.None);

        _mockBusinessServiceHealthLogRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}