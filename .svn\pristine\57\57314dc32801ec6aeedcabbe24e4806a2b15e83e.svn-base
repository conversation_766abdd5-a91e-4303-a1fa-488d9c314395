﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByBusinessService;

public class GetUserInfraObjectByBusinessServiceVm
{
    public bool IsAll { get; set; } = false;
    public List<AssignedBusinessServices> AssignedBusinessServices { get; set; }
}

public class AssignedBusinessFunctions
{
    public string Id { get; set; }
    public string Name { get; set; }

    [JsonIgnore] public string BusinessServiceId { get; set; }

    public bool IsAll { get; set; } = false;
    public bool IsPartial { get; set; } = false;
    public List<AssignedInfraObjects> AssignedInfraObjects { get; set; } = new();
}

public class AssignedBusinessServices
{
    public string Id { get; set; }
    public string Name { get; set; }
    public bool IsAll { get; set; } = false;
    public bool IsPartial { get; set; } = false;
    public List<AssignedBusinessFunctions> AssignedBusinessFunctions { get; set; } = new();
}

public class AssignedInfraObjects
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string State { get; set; }

    [JsonIgnore] public string BusinessFunctionId { get; set; }

    public bool IsSelected { get; set; } = false;
}