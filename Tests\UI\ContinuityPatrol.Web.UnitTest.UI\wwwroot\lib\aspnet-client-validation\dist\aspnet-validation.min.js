!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.aspnetValidation=t():e.aspnetValidation=t()}(self,(()=>(()=>{"use strict";var e={d:(t,n)=>{for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{MvcValidationProviders:()=>u,ValidationService:()=>d,isValidatable:()=>a});var n=function(e,t,n,r){return new(n||(n=Promise))((function(i,a){function s(e){try{l(r.next(e))}catch(e){a(e)}}function o(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,o)}l((r=r.apply(e,t||[])).next())}))},r=function(e,t){var n,r,i,a,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function o(o){return function(l){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,o[0]&&(s=0)),s;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,r=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!((i=(i=s.trys).length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}},i=new(function(){function e(){this.warn=globalThis.console.warn}return e.prototype.log=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n]},e}()),a=function(e){return e instanceof HTMLInputElement||e instanceof HTMLSelectElement||e instanceof HTMLTextAreaElement},s=["input","select","textarea"],o=function(e){return s.map((function(t){return"".concat(t).concat(e||"")})).join(",")};function l(e,t){var n=e.name,r=t.substring(2),i=n.lastIndexOf(".");if(i>-1){var s=n.substring(0,i)+"."+r,l=document.getElementsByName(s)[0];if(a(l))return l}return e.form.querySelector(o("[name=".concat(r,"]")))}var u=function(){this.required=function(e,t,n){var r=t.type.toLowerCase();if("checkbox"===r||"radio"===r){for(var i=0,a=Array.from(t.form.querySelectorAll(o("[name='".concat(t.name,"'][type='").concat(r,"']"))));i<a.length;i++){var s=a[i];if(s instanceof HTMLInputElement&&!0===s.checked)return!0}if("checkbox"===r){var l=t.form.querySelector("input[name='".concat(t.name,"'][type='hidden']"));if(l instanceof HTMLInputElement&&"false"===l.value)return!0}return!1}return Boolean(null==e?void 0:e.trim())},this.stringLength=function(e,t,n){if(!e)return!0;if(n.min){var r=parseInt(n.min);if(e.length<r)return!1}if(n.max){var i=parseInt(n.max);if(e.length>i)return!1}return!0},this.compare=function(e,t,n){if(!n.other)return!0;var r=l(t,n.other);return!r||r.value===e},this.range=function(e,t,n){if(!e)return!0;var r=parseFloat(e);return!(isNaN(r)||n.min&&r<parseFloat(n.min)||n.max&&r>parseFloat(n.max))},this.regex=function(e,t,n){return!e||!n.pattern||new RegExp(n.pattern).test(e)},this.email=function(e,t,n){return!e||/^([^\x00-\x20\x22\x28\x29\x2c\x2e\x3a-\x3c\x3e\x40\x5b-\x5d\x7f-\xff]+|\x22([^\x0d\x22\x5c\x80-\xff]|\x5c[\x00-\x7f])*\x22)(\x2e([^\x00-\x20\x22\x28\x29\x2c\x2e\x3a-\x3c\x3e\x40\x5b-\x5d\x7f-\xff]+|\x22([^\x0d\x22\x5c\x80-\xff]|\x5c[\x00-\x7f])*\x22))*\x40([^\x00-\x20\x22\x28\x29\x2c\x2e\x3a-\x3c\x3e\x40\x5b-\x5d\x7f-\xff]+|\x5b([^\x0d\x5b-\x5d\x80-\xff]|\x5c[\x00-\x7f])*\x5d)(\x2e([^\x00-\x20\x22\x28\x29\x2c\x2e\x3a-\x3c\x3e\x40\x5b-\x5d\x7f-\xff]+|\x5b([^\x0d\x5b-\x5d\x80-\xff]|\x5c[\x00-\x7f])*\x5d))*(\.\w{2,})+$/.test(e)},this.creditcard=function(e,t,n){if(!e)return!0;if(/[^0-9 \-]+/.test(e))return!1;var r,i,a=0,s=0,o=!1;if((e=e.replace(/\D/g,"")).length<13||e.length>19)return!1;for(r=e.length-1;r>=0;r--)i=e.charAt(r),s=parseInt(i,10),o&&(s*=2)>9&&(s-=9),a+=s,o=!o;return a%10==0},this.url=function(e,t,n){if(!e)return!0;var r=e.toLowerCase();return r.indexOf("http://")>-1||r.indexOf("https://")>-1||r.indexOf("ftp://")>-1},this.phone=function(e,t,n){return!e||!/[\+\-\s][\-\s]/g.test(e)&&/^\+?[0-9\-\s]+$/.test(e)},this.remote=function(e,t,n){if(!e)return!0;for(var r=n.additionalfields.split(","),i={},a=0,s=r;a<s.length;a++){var o=s[a],u=o.substr(2),d=l(t,o);Boolean(d&&d.value)&&(d instanceof HTMLInputElement&&("checkbox"===d.type||"radio"===d.type)?i[u]=d.checked?d.value:"":i[u]=d.value)}var c=n.url,f=[];for(var u in i){var m=encodeURIComponent(u)+"="+encodeURIComponent(i[u]);f.push(m)}var h=f.join("&");return new Promise((function(e,t){var r=new XMLHttpRequest;if(n.type&&"post"===n.type.toLowerCase()){var a=new FormData;for(var s in i)a.append(s,i[s]);r.open("post",c),r.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),r.send(h)}else r.open("get",c+"?"+h),r.send();r.onload=function(n){if(r.status>=200&&r.status<300){var i=JSON.parse(r.responseText);e(i)}else t({status:r.status,statusText:r.statusText,data:r.responseText})},r.onerror=function(e){t({status:r.status,statusText:r.statusText,data:r.responseText})}}))}},d=function(){function e(e){var t=this;this.providers={},this.messageFor={},this.elementUIDs=[],this.elementByUID={},this.formInputs={},this.validators={},this.formEvents={},this.inputEvents={},this.summary={},this.debounce=300,this.allowHiddenFields=!1,this.validateForm=function(e,i){return n(t,void 0,void 0,(function(){var t,n,a;return r(this,(function(r){switch(r.label){case 0:if(!(e instanceof HTMLFormElement))throw new Error("validateForm() can only be called on <form> elements");return t=this.getElementUID(e),n=this.formEvents[t],(a=!n)?[3,2]:[4,n(void 0,i)];case 1:a=r.sent(),r.label=2;case 2:return[2,a]}}))}))},this.validateField=function(e,i){return n(t,void 0,void 0,(function(){var t,n,a;return r(this,(function(r){switch(r.label){case 0:return t=this.getElementUID(e),n=this.inputEvents[t],(a=!n)?[3,2]:[4,n(void 0,i)];case 1:a=r.sent(),r.label=2;case 2:return[2,a]}}))}))},this.preValidate=function(e){e.preventDefault(),e.stopImmediatePropagation()},this.handleValidated=function(e,n,r){if(!(e instanceof HTMLFormElement))throw new Error("handleValidated() can only be called on <form> elements");n?r&&t.submitValidForm(e,r):t.focusFirstInvalid(e)},this.submitValidForm=function(e,t){if(!(e instanceof HTMLFormElement))throw new Error("submitValidForm() can only be called on <form> elements");var n=new SubmitEvent("submit",t);if(e.dispatchEvent(n)){var r=t.submitter,i=null,a=e.action;if(r){var s=r.getAttribute("name");s&&((i=document.createElement("input")).type="hidden",i.name=s,i.value=r.getAttribute("value"),e.appendChild(i));var o=r.getAttribute("formaction");o&&(e.action=o)}try{e.submit()}finally{i&&e.removeChild(i),e.action=a}}},this.focusFirstInvalid=function(e){if(!(e instanceof HTMLFormElement))throw new Error("focusFirstInvalid() can only be called on <form> elements");var n=t.getElementUID(e),r=t.formInputs[n],i=null==r?void 0:r.find((function(e){return t.summary[e]}));if(i){var a=t.elementByUID[i];a instanceof HTMLElement&&a.focus()}},this.isValid=function(e,n,r){if(void 0===n&&(n=!0),!(e instanceof HTMLFormElement))throw new Error("isValid() can only be called on <form> elements");n&&t.validateForm(e,r);var i=t.getElementUID(e),a=t.formInputs[i];return!(!0===(null==a?void 0:a.some((function(e){return t.summary[e]}))))},this.isFieldValid=function(e,n,r){void 0===n&&(n=!0),n&&t.validateField(e,r);var i=t.getElementUID(e);return void 0===t.summary[i]},this.options={root:document.body,watch:!1,addNoValidate:!0},this.ValidationInputCssClassName="input-validation-error",this.ValidationInputValidCssClassName="input-validation-valid",this.ValidationMessageCssClassName="field-validation-error",this.ValidationMessageValidCssClassName="field-validation-valid",this.ValidationSummaryCssClassName="validation-summary-errors",this.ValidationSummaryValidCssClassName="validation-summary-valid",this.logger=e||i}return e.prototype.addProvider=function(e,t){this.providers[e]||(this.logger.log("Registered provider: %s",e),this.providers[e]=t)},e.prototype.addMvcProviders=function(){var e=new u;this.addProvider("required",e.required),this.addProvider("length",e.stringLength),this.addProvider("maxlength",e.stringLength),this.addProvider("minlength",e.stringLength),this.addProvider("equalto",e.compare),this.addProvider("range",e.range),this.addProvider("regex",e.regex),this.addProvider("creditcard",e.creditcard),this.addProvider("email",e.email),this.addProvider("url",e.url),this.addProvider("phone",e.phone),this.addProvider("remote",e.remote)},e.prototype.scanMessages=function(e,t){for(var n=0,r=Array.from(e.querySelectorAll("span[form]"));n<r.length;n++){var i=r[n];(u=document.getElementById(i.getAttribute("form")))instanceof HTMLFormElement&&t.call(this,u,i)}var a=Array.from(e.querySelectorAll("form"));e instanceof HTMLFormElement&&a.push(e);var s=e instanceof Element?e.closest("form"):null;s&&a.push(s);for(var o=0,l=a;o<l.length;o++)for(var u=l[o],d=0,c=Array.from(u.querySelectorAll("[data-valmsg-for]"));d<c.length;d++)i=c[d],t.call(this,u,i)},e.prototype.pushValidationMessageSpan=function(e,t){var n,r,i,a=this.getElementUID(e),s=null!==(n=(i=this.messageFor)[a])&&void 0!==n?n:i[a]={},o=t.getAttribute("data-valmsg-for");if(o){var l=null!==(r=s[o])&&void 0!==r?r:s[o]=[];l.indexOf(t)<0?l.push(t):this.logger.log("Validation element for '%s' is already tracked",name,t)}},e.prototype.removeValidationMessageSpan=function(e,t){var n=this.getElementUID(e),r=this.messageFor[n];if(r){var i=t.getAttribute("data-valmsg-for");if(i){var a=r[i];if(a){var s=a.indexOf(t);s>=0?a.splice(s,1):this.logger.log("Validation element for '%s' was already removed",name,t)}}}},e.prototype.parseDirectives=function(e){for(var t={},n={},r=0;r<e.length;r++){var i=e[r];if(0===i.name.indexOf("data-val-")){var a=i.name.substr(9);n[a]=i.value}}var s=function(e){if(-1===e.indexOf("-")){for(var r=Object.keys(n).filter((function(t){return t!==e&&0===t.indexOf(e)})),i={error:n[e],params:{}},a=(e+"-").length,s=0;s<r.length;s++){var o=n[r[s]],l=r[s].substr(a);i.params[l]=o}t[e]=i}};for(var a in n)s(a);return t},e.prototype.guid4=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}))},e.prototype.getElementUID=function(e){var t=this.elementUIDs.filter((function(t){return t.node===e}))[0];if(t)return t.uid;var n=this.guid4();return this.elementUIDs.push({node:e,uid:n}),this.elementByUID[n]=e,n},e.prototype.getFormValidationTask=function(e){var t=this.formInputs[e];if(!t||0===t.length)return Promise.resolve(!0);for(var n=[],r=0,i=t;r<i.length;r++){var a=i[r],s=this.validators[a];s&&n.push(s)}var o=n.map((function(e){return e()}));return Promise.all(o).then((function(e){return e.every((function(e){return e}))}))},e.prototype.getMessageFor=function(e){var t;if(e.form){var n=this.getElementUID(e.form);return null===(t=this.messageFor[n])||void 0===t?void 0:t[e.name]}},e.prototype.shouldValidate=function(e){return!(e&&e.submitter&&e.submitter.formNoValidate)},e.prototype.trackFormInput=function(e,t){var i,a,s=this,o=this.getElementUID(e),l=null!==(i=(a=this.formInputs)[o])&&void 0!==i?i:a[o]=[];if(-1===l.indexOf(t)?(l.push(t),this.options.addNoValidate?(this.logger.log("Setting novalidate on form",e),e.setAttribute("novalidate","novalidate")):this.logger.log("Not setting novalidate on form",e)):this.logger.log("Form input for UID '%s' is already tracked",t),!this.formEvents[o]){var u=null,d=function(t,i){return u||(s.shouldValidate(t)?(u=s.getFormValidationTask(o),t&&s.preValidate(t),s.logger.log("Validating",e),u.then((function(a){return n(s,void 0,void 0,(function(){var n;return r(this,(function(r){switch(r.label){case 0:return this.logger.log("Validated (success = %s)",a,e),i?(i(a),[2,a]):(n=new CustomEvent("validation",{detail:{valid:a}}),e.dispatchEvent(n),[4,new Promise((function(e){return setTimeout(e,0)}))]);case 1:return r.sent(),this.handleValidated(e,a,t),[2,a]}}))}))})).catch((function(e){return s.logger.log("Validation error",e),!1})).finally((function(){u=null}))):Promise.resolve(!0))};e.addEventListener("submit",d);var c=function(e){for(var t=0,n=s.formInputs[o];t<n.length;t++){var r=n[t];s.resetField(r)}s.renderSummary()};e.addEventListener("reset",c),d.remove=function(){e.removeEventListener("submit",d),e.removeEventListener("reset",c)},this.formEvents[o]=d}},e.prototype.reset=function(e){this.isDisabled(e)?this.resetField(this.getElementUID(e)):this.scan(e)},e.prototype.resetField=function(e){var t=this.elementByUID[e];this.swapClasses(t,"",this.ValidationInputCssClassName),this.swapClasses(t,"",this.ValidationInputValidCssClassName);var n=a(t)&&this.getMessageFor(t);if(n)for(var r=0;r<n.length;r++)n[r].innerHTML="",this.swapClasses(n[r],"",this.ValidationMessageCssClassName),this.swapClasses(n[r],"",this.ValidationMessageValidCssClassName);delete this.summary[e]},e.prototype.untrackFormInput=function(e,t){var n,r=this.getElementUID(e),i=this.formInputs[r];if(i){var a=i.indexOf(t);a>=0?(i.splice(a,1),i.length||(null===(n=this.formEvents[r])||void 0===n||n.remove(),delete this.formEvents[r],delete this.formInputs[r],delete this.messageFor[r])):this.logger.log("Form input for UID '%s' was already removed",t)}},e.prototype.addInput=function(e){var t,i=this,a=this.getElementUID(e),s=this.parseDirectives(e.attributes);if(this.validators[a]=this.createValidator(e,s),e.form&&this.trackFormInput(e.form,a),!this.inputEvents[a]){var o=function(t,s){return n(i,void 0,void 0,(function(){var n,i,o;return r(this,(function(r){switch(r.label){case 0:if(!(n=this.validators[a]))return[2,!0];if(!e.dataset.valEvent&&t&&"input"===t.type&&!e.classList.contains(this.ValidationInputCssClassName))return[2,!0];this.logger.log("Validating",{event:t}),r.label=1;case 1:return r.trys.push([1,3,,4]),[4,n()];case 2:return i=r.sent(),s(i),[2,i];case 3:return o=r.sent(),this.logger.log("Validation error",o),[2,!1];case 4:return[2]}}))}))},l=null;o.debounced=function(e,t){null!==l&&clearTimeout(l),l=setTimeout((function(){o(e,t)}),i.debounce)};var u=e instanceof HTMLSelectElement?"change":"input change",d=(null!==(t=e.dataset.valEvent)&&void 0!==t?t:u).split(" ");d.forEach((function(t){e.addEventListener(t,o.debounced)})),o.remove=function(){d.forEach((function(t){e.removeEventListener(t,o.debounced)}))},this.inputEvents[a]=o}},e.prototype.removeInput=function(e){var t=this.getElementUID(e),n=this.inputEvents[t];(null==n?void 0:n.remove)&&(n.remove(),delete n.remove),delete this.summary[t],delete this.inputEvents[t],delete this.validators[t],e.form&&this.untrackFormInput(e.form,t)},e.prototype.scanInputs=function(e,t){var n=Array.from(e.querySelectorAll(o('[data-val="true"]')));a(e)&&"true"===e.getAttribute("data-val")&&n.push(e);for(var r=0;r<n.length;r++){var i=n[r];t.call(this,i)}},e.prototype.createSummaryDOM=function(){if(!Object.keys(this.summary).length)return null;var e=[],t=document.createElement("ul");for(var n in this.summary){var r=this.elementByUID[n];if(!(r instanceof HTMLInputElement&&("checkbox"===r.type||"radio"===r.type)&&r.className===this.ValidationInputValidCssClassName||e.indexOf(this.summary[n])>-1)){var i=document.createElement("li");i.innerHTML=this.summary[n],t.appendChild(i),e.push(this.summary[n])}}return t},e.prototype.renderSummary=function(){var e=document.querySelectorAll('[data-valmsg-summary="true"]');if(e.length){var t=JSON.stringify(this.summary,Object.keys(this.summary).sort());if(t!==this.renderedSummaryJSON){this.renderedSummaryJSON=t;for(var n=this.createSummaryDOM(),r=0;r<e.length;r++){for(var i=e[r],a=i.querySelectorAll("ul"),s=0;s<a.length;s++)a[s].remove();n&&n.hasChildNodes()?(this.swapClasses(i,this.ValidationSummaryCssClassName,this.ValidationSummaryValidCssClassName),i.appendChild(n.cloneNode(!0))):this.swapClasses(i,this.ValidationSummaryValidCssClassName,this.ValidationSummaryCssClassName)}}}},e.prototype.addError=function(e,t){var n=this.getMessageFor(e);if(n)for(var r=0;r<n.length;r++)n[r],n[r].innerHTML=t,this.swapClasses(n[r],this.ValidationMessageCssClassName,this.ValidationMessageValidCssClassName);if(this.swapClasses(e,this.ValidationInputCssClassName,this.ValidationInputValidCssClassName),e.form){var i=e.form.querySelectorAll(o('[name="'.concat(e.name,'"]')));for(r=0;r<i.length;r++){this.swapClasses(i[r],this.ValidationInputCssClassName,this.ValidationInputValidCssClassName);var a=this.getElementUID(i[r]);this.summary[a]=t}}this.renderSummary()},e.prototype.removeError=function(e){var t=this.getMessageFor(e);if(t)for(var n=0;n<t.length;n++)t[n].innerHTML="",this.swapClasses(t[n],this.ValidationMessageValidCssClassName,this.ValidationMessageCssClassName);if(this.swapClasses(e,this.ValidationInputValidCssClassName,this.ValidationInputCssClassName),e.form){var r=e.form.querySelectorAll(o('[name="'.concat(e.name,'"]')));for(n=0;n<r.length;n++){this.swapClasses(r[n],this.ValidationInputValidCssClassName,this.ValidationInputCssClassName);var i=this.getElementUID(r[n]);delete this.summary[i]}}this.renderSummary()},e.prototype.createValidator=function(e,t){var i=this;return function(){return n(i,void 0,void 0,(function(){var n,i,a,s,o,l,u,d,c,f,m;return r(this,(function(r){switch(r.label){case 0:if(this.isHidden(e)||this.isDisabled(e))return[3,7];for(a in i=[],n=t)i.push(a);s=0,r.label=1;case 1:return s<i.length?(a=i[s])in n?(l=t[o=a],(u=this.providers[o])?(this.logger.log("Running %s validator on element",o,e),d=u(e.value,e,l.params),c=!1,f=l.error,"boolean"!=typeof d?[3,2]:(c=d,[3,5])):(this.logger.log("aspnet-validation provider not implemented: %s",o),[3,6])):[3,6]:[3,7];case 2:return"string"!=typeof d?[3,3]:(c=!1,f=d,[3,5]);case 3:return[4,d];case 4:"boolean"==typeof(m=r.sent())?c=m:(c=!1,f=m),r.label=5;case 5:if(!c)return this.addError(e,f),[2,!1];r.label=6;case 6:return s++,[3,1];case 7:return this.removeError(e),[2,!0]}}))}))}},e.prototype.isHidden=function(e){return!(this.allowHiddenFields||e.offsetWidth||e.offsetHeight||e.getClientRects().length)},e.prototype.isDisabled=function(e){return e.disabled},e.prototype.swapClasses=function(e,t,n){!t||this.isDisabled(e)||e.classList.contains(t)||e.classList.add(t),e.classList.contains(n)&&e.classList.remove(n)},e.prototype.bootstrap=function(e){var t=this;Object.assign(this.options,e),this.addMvcProviders();var n=window.document,r=this.options.root,i=function(){t.scan(r),t.options.watch&&t.watch(r)};"complete"===n.readyState||"interactive"===n.readyState?i():n.addEventListener("DOMContentLoaded",i)},e.prototype.scan=function(e){null!=e||(e=this.options.root),this.logger.log("Scanning",e),this.scanMessages(e,this.pushValidationMessageSpan),this.scanInputs(e,this.addInput)},e.prototype.remove=function(e){null!=e||(e=this.options.root),this.logger.log("Removing",e),this.scanMessages(e,this.removeValidationMessageSpan),this.scanInputs(e,this.removeInput)},e.prototype.watch=function(e){var t=this;null!=e||(e=this.options.root),this.observer=new MutationObserver((function(e){e.forEach((function(e){t.observed(e)}))})),this.observer.observe(e,{attributes:!0,childList:!0,subtree:!0}),this.logger.log("Watching for mutations")},e.prototype.observed=function(e){var t,n,r;if("childList"===e.type){for(var i=0;i<e.addedNodes.length;i++){var a=e.addedNodes[i];this.logger.log("Added node",a),a instanceof HTMLElement&&this.scan(a)}for(i=0;i<e.removedNodes.length;i++)a=e.removedNodes[i],this.logger.log("Removed node",a),a instanceof HTMLElement&&this.remove(a)}else if("attributes"===e.type&&e.target instanceof HTMLElement)if("disabled"===e.attributeName){var s=e.target;this.reset(s)}else{var o=null!==(t=e.oldValue)&&void 0!==t?t:"",l=null!==(r=null===(n=e.target.attributes[e.attributeName])||void 0===n?void 0:n.value)&&void 0!==r?r:"";this.logger.log("Attribute '%s' changed from '%s' to '%s'",e.attributeName,o,l,e.target),o!==l&&this.scan(e.target)}},e}();return t})()));
//# sourceMappingURL=aspnet-validation.min.js.map