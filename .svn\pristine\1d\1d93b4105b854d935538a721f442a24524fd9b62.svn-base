﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class FormTypeRepositoryMocks 
{
    public static Mock<IFormTypeRepository> CreateFormTypeRepository(List<FormType> formTypes)
    {
        var mockFormTypeRepository = new Mock<IFormTypeRepository>();

        mockFormTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(formTypes);

        mockFormTypeRepository.Setup(repo => repo.AddAsync(It.IsAny<FormType>())).ReturnsAsync(
            (FormType formType) =>
            {
                formType.Id = new Fixture().Create<int>();

                formType.ReferenceId = new Fixture().Create<Guid>().ToString();

                formTypes.Add(formType);

                return formType;
            });

        return mockFormTypeRepository;
    }

    public static Mock<IFormTypeRepository> UpdateFormTypeRepository(List<FormType> formTypes)
    {
        var mockFormTypeRepository = new Mock<IFormTypeRepository>();

        mockFormTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(formTypes);

        mockFormTypeRepository.Setup(repo => repo.GetFormTypeById(It.IsAny<string>())).ReturnsAsync((string i) => formTypes.SingleOrDefault(x => x.ReferenceId == i));

        mockFormTypeRepository.Setup(repo => repo.UpdateAsync(It.IsAny<FormType>())).ReturnsAsync((FormType formType) =>
        {
            var index = formTypes.FindIndex(item => item.ReferenceId == formType.ReferenceId);

            formTypes[index] = formType;

            return formType;
        });

        return mockFormTypeRepository;
    }

    public static Mock<IFormTypeRepository> DeleteFormTypeRepository(List<FormType> formTypes)
    {
        var mockFormTypeRepository = new Mock<IFormTypeRepository>();

        mockFormTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(formTypes);

        mockFormTypeRepository.Setup(repo => repo.GetFormTypeById(It.IsAny<string>())).ReturnsAsync((string i) => formTypes.SingleOrDefault(x => x.ReferenceId == i));

        mockFormTypeRepository.Setup(repo => repo.UpdateAsync(It.IsAny<FormType>())).ReturnsAsync((FormType formType) =>
        {
            var index = formTypes.FindIndex(item => item.ReferenceId == formType.ReferenceId);

            formType.IsActive = false;

            formTypes[index] = formType;

            return formType;
        });

        return mockFormTypeRepository;
    }

    public static Mock<IFormTypeRepository> GetFormTypeRepository(List<FormType> formTypes)
    {
        var mockFormTypeRepository = new Mock<IFormTypeRepository>();

        mockFormTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(formTypes);

        mockFormTypeRepository.Setup(repo => repo.GetFormTypeById(It.IsAny<string>())).ReturnsAsync((string i) => formTypes.SingleOrDefault(x => x.ReferenceId == i));

        return mockFormTypeRepository;
    }

    public static Mock<IFormTypeRepository> GetFormTypeNamesRepository(List<FormType> formTypes)
    {
        var mockFormTypeRepository = new Mock<IFormTypeRepository>();

        mockFormTypeRepository.Setup(repo => repo.GetFormTypeNames()).ReturnsAsync(formTypes);

        return mockFormTypeRepository;
    }


    public static Mock<IFormTypeRepository> GetFormTypeNameUniqueRepository(List<FormType> formTypes)
    {
        var mockFormTypeRepository = new Mock<IFormTypeRepository>();

        mockFormTypeRepository.Setup(repo => repo.IsFormTypeNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => formTypes.Exists(x => x.FormTypeName == i && x.ReferenceId == j));

        return mockFormTypeRepository;
    }

    public static Mock<IFormTypeRepository> GetFormTypeEmptyRepository()
    {
        var mockFormTypeRepository = new Mock<IFormTypeRepository>();

        mockFormTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<FormType>());

        return mockFormTypeRepository;
    }

    public static Mock<IFormTypeRepository> GetPaginatedFormTypeRepository(List<FormType> formTypes)
    {
        var formTypeRepository = new Mock<IFormTypeRepository>();

        var queryableFormType = formTypes.BuildMock();

        formTypeRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableFormType);

        return formTypeRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateFormTypeEventRepository(List<UserActivity> userActivities)
    {
        var formTypeEventRepository = new Mock<IUserActivityRepository>();

        formTypeEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return formTypeEventRepository;
    }
}