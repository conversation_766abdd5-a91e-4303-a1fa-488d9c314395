﻿using ContinuityPatrol.Application.Features.VeritasCluster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.VeritasClusterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.VeritasCluster.Queries
{
    public class GetVeritasClusterPaginatedListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IVeritasClusterRepository> _mockVeritasClusterRepository;
        private readonly GetVeritasClusterPaginatedListQueryHandler _handler;

        public GetVeritasClusterPaginatedListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockVeritasClusterRepository = new Mock<IVeritasClusterRepository>();
            _handler = new GetVeritasClusterPaginatedListQueryHandler(_mockMapper.Object, _mockVeritasClusterRepository.Object);
        }

        [Fact]
        public async Task Handle_ReturnsPaginatedResults_WhenDataExists()
        {
            var query = new GetVeritasClusterPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = "Cluster"
            };

            var clusters = new List<Domain.Entities.VeritasCluster>
            {
                new() { Id = 1, ClusterName = "Cluster1", IsActive = true },
                new() { Id = 2, ClusterName = "Cluster2", IsActive = true }
            }.AsQueryable();

            var paginatedClusters = new PaginatedResult<Domain.Entities.VeritasCluster>();

            _mockVeritasClusterRepository.Setup(r => r.GetPaginatedQuery())
                .Returns(clusters);

            _mockMapper.Setup(m => m.Map<VeritasClusterListVm>(It.IsAny<Domain.Entities.VeritasCluster>()))
                .Returns((Domain.Entities.VeritasCluster src) => new VeritasClusterListVm { Id = Guid.NewGuid().ToString(), ClusterName = src.ClusterName });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Data.Count);
            Assert.Equal("Cluster1", result.Data.First().ClusterName);

            _mockVeritasClusterRepository.Verify(r => r.GetPaginatedQuery(), Times.Once);
        }

        [Fact]
        public async Task Handle_ReturnsEmptyPaginatedResults_WhenNoDataExists()
        {
            var query = new GetVeritasClusterPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = "NonExistent"
            };

            var clusters = new List<Domain.Entities.VeritasCluster>().AsQueryable();

            _mockVeritasClusterRepository.Setup(r => r.GetPaginatedQuery())
                .Returns(clusters);

            _mockMapper.Setup(m => m.Map<VeritasClusterListVm>(It.IsAny<Domain.Entities.VeritasCluster>()))
                .Returns((Domain.Entities.VeritasCluster src) => new VeritasClusterListVm { Id = Guid.NewGuid().ToString(), ClusterName = src.ClusterName });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);

            _mockVeritasClusterRepository.Verify(r => r.GetPaginatedQuery(), Times.Once);
        }
    }
}
