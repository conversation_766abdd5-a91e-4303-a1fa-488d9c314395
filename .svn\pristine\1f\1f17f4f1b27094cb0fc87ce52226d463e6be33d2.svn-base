﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title" title="Dell PowerMax">
            <i class="cp-monitoring"></i><span>PowerMax Detail Monitoring :<span id="infraName"></span></span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time :"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>
    </div>
    <div class="row g-2 mt-0">
        <div class="col-md-6 col-lg-6 col-xl-6 d-grid">
            <div class="card Card_Design_None h-100">
                <div class="card-header card-title" title="PowerMax Monitoring">PowerMax Monitoring</div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0 noDataimg" style="table-layout:fixed">
                        <thead>
                            <tr>
                                <th title="Replication Details">Component</th>
                                <th class="text-primary" title="Primary">Primary</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="fw-semibold text-truncate " title="Powermax IP Address">
                                    <i class="text-secondary cp-ip-address me-1 fs-6"></i>Powermax IP Address
                                </td>
                                <td class="text-truncate"><span id="IpAddress"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate " title="Version"><i class="text-secondary cp-product-version-icon me-1 fs-6"></i>Version</td>
                                <td class="text-truncate"><span id="Version"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate " title="Array(Symmetric ID)">
                                    <i class="text-secondary cp-folder-server me-1 fs-6"></i>Array(Symmetric ID)
                                </td>
                                <td class="text-truncate"><span id="Array"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate " title="Current WAL File Name">
                                    <i class="text-secondary cp-control-file-name me-1 fs-6"></i>Model Name(Array)
                                </td>
                                <td class="text-truncate"><span id="ModelName"></span></td>
                            </tr>
                           @*  <tr>
                                <td class="fw-semibold text-truncate " title="Current WAL Log Location">
                                    <i class="text-secondary cp-storage-name me-1 fs-6"></i>Storage Group
                                </td>
                                <td class="text-truncate"><span>Ptech-SG01</span></td>
                            </tr> *@
                        </tbody>

                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-6 col-xl-6 d-grid">
            <div class="card Card_Design_None mb-2 h-100">
                <div class="card-header card-title" title="Solution Diagram">Solution Diagram</div>
                <div class="card-body text-center p-0 d-flex align-items-center justify-content-center">
                    @* <img src="/img/charts_img/PageBuilder-Solution.svg" /> *@
                    <div id="Solution_Diagram" class="w-100"></div>
                </div>
            </div>
        </div>
        <div class="col-12 mt-0 pb-2">
            <div class="d-flex align-items-center justify-content-end gap-2" data-select2-id="select2-data-13-m66x">
                <div style="width:200px;background:#fff" data-select2-id="select2-data-12-bcuf">
                    <select class="form-select" data-placeholder="Select Storage Group Name" id="ddlStorage">
                        <option>All</option>

                    </select>
                </div>
                <div style="width:200px;background:#fff">
                    <select class="form-select">
                        <option value="All">All</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-12 d-grid">
            <div class="card Card_Design_None mb-2">
                <div class="card-header card-title d-flex align-items-center justify-content-between">
                    <span title="Storage Group Monitoring">Storage Group Monitoring</span>
                </div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0" style="table-layout:fixed">
                        <thead>
                            <tr>
                                <th>Storage Group Name</th>
                                <th>Compliance</th>
                                <th>SRP</th>
                                <th>Service level</th>
                                <th>Capacity(GB)</th>
                                <th>Emulation</th>
                                <th>SRDF Replication status</th>
                                <th>No of snapshot</th>
                            </tr>
                        </thead>
                        <tbody id="storageGroup">
                           @*  <td class="text-truncate"><i class="text-secondary cp-Storage-three me-1 fs-6"></i><span>Ptech-SG01</span></td>
                            <td class="text-truncate"><span>STABLE</span></td>
                            <td class="text-truncate"><span>SRP_1</span></td>
                            <td class="text-truncate"><span>Diamond</span></td>
                            <td class="text-truncate"><span>1</span></td>
                            <td class="text-truncate"><span>FAB</span></td>
                            <td class="text-truncate"><span>NA</span></td>
                            <td class="text-truncate"><span>5</span></td> *@
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @* <div class="col-12 mt-0 pb-2">
            <div class="d-flex align-items-center justify-content-end gap-2" data-select2-id="select2-data-13-m66x">
                <div style="width:200px;background:#fff" data-select2-id="select2-data-12-bcuf">
                    <select class="form-select" data-placeholder="Select Storage Group Name" id="ddlStorage">
                         <option>All</option>
                       
                    </select>
                </div>
                <div style="width:200px;background:#fff">
                    <select class="form-select">
                        <option value="All">All</option>
                    </select>
                </div>
            </div>
        </div> *@
        @* <div class="col-12 d-grid mt-0">
            <div class="card Card_Design_None mb-2">
                <div class="card-header card-title d-flex align-items-center justify-content-between">
                    <span title="Storage Group Monitoring">Snapshot Monitoring</span>
                </div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0 table-bordered" style="table-layout:fixed">
                        <tbody>
                            <tr>
                                <td rowspan="4" scope="rowgroup">Terrestrial planets</td>
                                <td>Select All</td>
                                <td>*</td>
                            </tr>
                            <tr>
                                <td>Append with Target Storage Name Automatically</th>
                                <td class="p-0">
                                    <table class="table mb-0">
                                        <tbody>
                                            <tr>
                                                <td>12</td>
                                            </tr>
                                            <tr>
                                                <td class="border-0">34</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    
                                </t>
                            </tr>

                            <tr>
                                <td>Select Target Storage Group Name</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>Type Snapshot Name</td>
                                <td></td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </div> *@

        <div class="col-12 d-grid mt-0">
            <div class="card Card_Design_None mb-2">
                <div class="card-header header">
                    <h6 class="card-title">Snapshot Monitoring</h6>
                    <div>
                        <div class="d-flex align-items-end gap-2">
                            <div class="d-flex align-items-center">
                                <div class="form-label me-2">Snapshot Type</div>
                                <div class="input-group me-2" style="width:300px">
                                    <span class="input-group-text"><i class="cp-snap-1"></i></span>
                                    <select data-placeholder="Select Snapshot Type" id="ddlsnapType"
                                                class="form-select" aria-label="Default select example" data-live-search="true">
                                                <option value="All">All</option>
                                    </select>
                                </div>
                            </div>
                            <div class="input-group me-2">
                                <input type="search" id="search-input" class="form-control" placeholder="Search" autocomplete="off">
                                <span class="input-group-text pe-0"><i class="cp-search"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0" style="table-layout:fixed">
                        <thead>
                            <tr>
                                <th>Snapshot Name</th>
                                <th>Snapshot ID</th>
                                <th>Creation Time</th>
                                <th>Linked</th>
                                <th>Restored</th>
                                <th>Expired</th>
                                <th>Expiry Time</th>
                                <th>secured</th>
                            </tr>
                        </thead>
                        <tbody id="snapDetails">
                           @*  <tr>
                                <td class="text-truncate"><i class="text-secondary cp-Storage-three me-1 fs-6"></i><span>Ptech-SG01</span></td>
                                <td class="text-truncate"><span>147859757825</span></td>
                                <td class="text-truncate"><span>Wed Jan 08 2025 05:41:07</span></td>
                                <td class="text-truncate"><span>-</span></td>
                                <td class="text-truncate"><span>-</span></td>
                                <td class="text-truncate"><span>-</span></td>
                                <td class="text-truncate"><span>Thu Jan 01 1970 01:00:00</span></td>
                                <td class="text-truncate"><span>-</span></td>
                            </tr> *@
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/js/monitoring/MonitoringSolutionDiagram.js"></script>
<script src="~/js/monitoring/PowerMaxMonitoring.js"></script>