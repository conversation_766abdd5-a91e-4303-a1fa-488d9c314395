﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.UserInfraObject.Commands.Create;

public record CreateUserInfraObjectCommand : IRequest<CreateUserInfraObjectResponse>
{
    [JsonIgnore] public string UserId { get; set; }

    public string Properties { get; set; }

    public int IsApplication { get; set; }

    [JsonIgnore] public string CreatedBy { get; set; }

    [JsonIgnore] public string LastModifiedBy { get; set; }
}