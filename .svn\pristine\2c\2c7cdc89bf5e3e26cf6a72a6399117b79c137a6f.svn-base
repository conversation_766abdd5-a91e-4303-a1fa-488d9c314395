﻿using AutoMapper;
using ContinuityPatrol.Domain.ViewModels.CyberSnapsModel;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Cloud.Controllers;
using ContinuityPatrol.Web.Areas.CyberResiliency.Controllers;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;

namespace ContinuityPatrol.Web.UnitTests.Areas.Cyber.Controllers
{
    public class CyberRecoveryControllerTests
    {
        private readonly Mock<ILogger<CyberResiliencyController>> _mockLogger = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly CloudConnectController _controller;
        public CyberRecoveryControllerTests()
        {
       
        }
        [Fact]
        public void List_ReturnsViewResult()
        {
            _mockDataProvider.Setup(x=>x.CyberSnaps.GetCyberSnapsList()).ReturnsAsync(It.IsAny<List<CyberSnapsListVm>>);
            var controller = new CyberResiliencyController(_mockDataProvider.Object, _mockLogger.Object, _mockPublisher.Object);

            var result = controller.GetCyberSnapsList();

            Assert.IsType<Microsoft.AspNetCore.Mvc.JsonResult>(result.Result);
        }
    }
}

