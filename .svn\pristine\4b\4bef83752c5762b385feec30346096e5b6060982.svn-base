﻿using ContinuityPatrol.Application.Features.Alert.Events.UserLoginEvents.Update;

namespace ContinuityPatrol.Application.Features.Alert.Commands.Update;

public class UpdateAlertCommandHandler : IRequestHandler<UpdateAlertCommand, UpdateAlertResponse>
{
    private readonly IAlertRepository _alertRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateAlertCommandHandler(IAlertRepository alertRepository, IMapper mapper, IPublisher publisher)
    {
        _alertRepository = alertRepository;
        _mapper = mapper;
        _publisher = publisher;
    }

    public async Task<UpdateAlertResponse> Handle(UpdateAlertCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _alertRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Alert), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateAlertCommand), typeof(Domain.Entities.Alert));

        await _alertRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateAlertResponse
        {
            Message = Message.Update(nameof(Domain.Entities.Alert), eventToUpdate.InfraObjectId),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(
            new UserLoginUpdatedEvent
                { InfraObjectId = eventToUpdate.InfraObjectId, AlertId = eventToUpdate.Id },
            cancellationToken);
        return response;
    }
}