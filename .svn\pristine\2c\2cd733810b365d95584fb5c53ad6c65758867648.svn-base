using ContinuityPatrol.Application.Features.ImpactActivity.Commands.Create;
using ContinuityPatrol.Application.Features.ImpactActivity.Commands.Delete;
using ContinuityPatrol.Application.Features.ImpactActivity.Commands.Update;
using ContinuityPatrol.Application.Features.ImpactActivity.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ImpactActivity.Queries.GetList;
//using ContinuityPatrol.Application.Features.ImpactActivity.Queries.GetNameUnique;
//using ContinuityPatrol.Application.Features.ImpactActivity.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ImpactActivityModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class ImpactActivitiesController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<ImpactActivityListVm>>> GetImpactActivities()
    {
        Logger.LogDebug("Get All ImpactActivities");

        return Ok(await Mediator.Send(new GetImpactActivityListQuery()));
    }

    [HttpGet("{id}", Name = "GetImpactActivity")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<ImpactActivityDetailVm>> GetImpactActivityById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ImpactActivity Id");

        Logger.LogDebug($"Get ImpactActivity Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetImpactActivityDetailQuery { Id = id }));
    }
    #region Paginated
    // [Route("paginated-list"), HttpGet]
    // [Authorize(Policy = Permissions.Dashboard.View)]
    // public async Task<ActionResult<PaginatedResult<ImpactActivityListVm>>> GetPaginatedImpactActivities([FromQuery] GetImpactActivityPaginatedListQuery query)
    // {
    //     Logger.LogDebug("Get Searching Details in ImpactActivity Paginated List");
    //
    //     return Ok(await Mediator.Send(query));
    // }
    #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Dashboard.Create)]
    public async Task<ActionResult<CreateImpactActivityResponse>> CreateImpactActivity([FromBody] CreateImpactActivityCommand createImpactActivityCommand)
    {
        Logger.LogDebug($"Create ImpactActivity '{createImpactActivityCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateImpactActivity), await Mediator.Send(createImpactActivityCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Dashboard.Edit)]
    public async Task<ActionResult<UpdateImpactActivityResponse>> UpdateImpactActivity([FromBody] UpdateImpactActivityCommand updateImpactActivityCommand)
    {
        Logger.LogDebug($"Update ImpactActivity '{updateImpactActivityCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateImpactActivityCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Dashboard.Delete)]
    public async Task<ActionResult<DeleteImpactActivityResponse>> DeleteImpactActivity(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ImpactActivity Id");

        Logger.LogDebug($"Delete ImpactActivity Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteImpactActivityCommand { Id = id }));
    }

    #region NameExist

    // [Route("name-exist"), HttpGet]
    // public async Task<ActionResult> IsImpactActivityNameExist(string impactActivityName, string? id)
    // {
    //     Guard.Against.NullOrWhiteSpace(ImpactActivityName, "ImpactActivity Name");
    //
    //     Logger.LogDebug($"Check Name Exists Detail by ImpactActivity Name '{impactActivityName}' and Id '{id}'");
    //
    //     return Ok(await Mediator.Send(new GetImpactActivityNameUniqueQuery { Name = ImpactActivityName, Id = id }));
    // }
    #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


