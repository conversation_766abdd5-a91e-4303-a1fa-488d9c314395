﻿namespace ContinuityPatrol.Application.Features.ServerLog.Commands.Create;

public class CreateServerLogCommandValidator : AbstractValidator<CreateServerLogCommand>
{
    private readonly IServerLogRepository _serverLogRepository;
    public CreateServerLogCommandValidator(IServerLogRepository serverLogRepository)
    {
        _serverLogRepository = serverLogRepository;
    }

    private async Task<bool> ServerLogNameUnique(CreateServerLogCommand e, CancellationToken token)
    {
        return !await _serverLogRepository.IsServerLogNameUnique(e.Name, e.Id);
    }
}
