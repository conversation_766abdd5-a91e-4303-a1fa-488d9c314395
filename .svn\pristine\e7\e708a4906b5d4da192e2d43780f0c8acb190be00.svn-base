using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetDetail;
//using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IBulkImportActionResultService
{
    Task<List<BulkImportActionResultListVm>> GetBulkImportActionResultList();
    Task<BaseResponse> CreateAsync(CreateBulkImportActionResultCommand createBulkImportActionResultCommand);
    Task<BaseResponse> UpdateAsync(UpdateBulkImportActionResultCommand updateBulkImportActionResultCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<BulkImportActionResultDetailVm> GetByReferenceId(string id);

    Task<List<BulkImportActionResultListVm>> GetByOperationIdAndOperationGroupId(string operationId,
        string operationGroupId);

    Task<List<BulkImportActionResultListVm>> GetBulkImportActionResultOperationGroupId(string operationGroupId);

    #region NameExist

    // Task<bool> IsBulkImportActionResultNameExist(string name, string? id);

    #endregion

    #region Paginated

    // Task<PaginatedResult<BulkImportActionResultListVm>> GetPaginatedBulkImportActionResults(GetBulkImportActionResultPaginatedListQuery query);

    #endregion
}
