using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportActionResult.Queries;

public class GetBulkImportActionResultDetailQueryTests : IClassFixture<BulkImportActionResultFixture>
{
    private readonly BulkImportActionResultFixture _bulkImportActionResultFixture;
    private readonly Mock<IBulkImportActionResultRepository> _mockBulkImportActionResultRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetBulkImportActionResultDetailsQueryHandler _handler;

    public GetBulkImportActionResultDetailQueryTests(BulkImportActionResultFixture bulkImportActionResultFixture)
    {
        _bulkImportActionResultFixture = bulkImportActionResultFixture;

        _mockBulkImportActionResultRepository = BulkImportActionResultRepositoryMocks.CreateQueryBulkImportActionResultRepository(_bulkImportActionResultFixture.BulkImportActionResults);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<BulkImportActionResultDetailVm>(It.IsAny<Domain.Entities.BulkImportActionResult>()))
            .Returns((Domain.Entities.BulkImportActionResult entity) => new BulkImportActionResultDetailVm
            {
              
                CompanyId = entity.CompanyId,
                NodeId = entity.NodeId,
                NodeName = entity.NodeName,
                BulkImportOperationId = entity.BulkImportOperationId,
                BulkImportOperationGroupId = entity.BulkImportOperationGroupId,
                EntityId = entity.EntityId,
                EntityName = entity.EntityName,
                EntityType = entity.EntityType,
                Status = entity.Status,
                StartTime = entity.StartTime,
                EndTime = entity.EndTime,
                ErrorMessage = entity.ErrorMessage
            });

        _handler = new GetBulkImportActionResultDetailsQueryHandler(
            _mockMapper.Object,
            _mockBulkImportActionResultRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_BulkImportActionResultDetailVm_When_BulkImportActionResultExists()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var query = new GetBulkImportActionResultDetailQuery { Id = existingResult.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(BulkImportActionResultDetailVm));
      
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var query = new GetBulkImportActionResultDetailQuery { Id = existingResult.ReferenceId };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportActionResultRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var query = new GetBulkImportActionResultDetailQuery { Id = existingResult.ReferenceId };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<BulkImportActionResultDetailVm>(It.IsAny<Domain.Entities.BulkImportActionResult>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportActionResultNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var query = new GetBulkImportActionResultDetailQuery { Id = nonExistentId };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportActionResult)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportActionResultIsInactive()
    {
        // Arrange
        var inactiveResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        inactiveResult.IsActive = false;
        var query = new GetBulkImportActionResultDetailQuery { Id = inactiveResult.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_MapEntityToViewModel_WithCorrectProperties()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        existingResult.CompanyId = "TestCompanyId";
        existingResult.NodeId = "Node001";
        existingResult.NodeName = "TestNode";
        existingResult.BulkImportOperationId = "TestOperationId";
        existingResult.BulkImportOperationGroupId = "TestGroupId";
        existingResult.EntityId = "TestEntityId";
        existingResult.EntityName = "TestEntity";
        existingResult.EntityType = "Server";
        existingResult.Status = "Pending";
        existingResult.ErrorMessage = "";
        existingResult.ConditionalOperation = 1;

        var query = new GetBulkImportActionResultDetailQuery { Id = existingResult.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
       // result.Id.ShouldBe(existingResult.ReferenceId);
        result.CompanyId.ShouldBe("TestCompanyId");
        result.NodeId.ShouldBe("Node001");
        result.NodeName.ShouldBe("TestNode");
        result.BulkImportOperationId.ShouldBe("TestOperationId");
        result.BulkImportOperationGroupId.ShouldBe("TestGroupId");
        result.EntityId.ShouldBe("TestEntityId");
        result.EntityName.ShouldBe("TestEntity");
        result.EntityType.ShouldBe("Server");
        result.Status.ShouldBe("Pending");
        result.ErrorMessage.ShouldBe("");
       
    }

    [Fact]
    public async Task Handle_CallRepositoryWithCorrectId_When_QueryExecuted()
    {
        // Arrange
        var testId = Guid.NewGuid().ToString();
        var query = new GetBulkImportActionResultDetailQuery { Id = testId };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByReferenceIdAsync(testId))
            .ReturnsAsync(_bulkImportActionResultFixture.BulkImportActionResults.First());

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportActionResultRepository.Verify(x => x.GetByReferenceIdAsync(testId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectViewModelType_When_MappingSuccessful()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var query = new GetBulkImportActionResultDetailQuery { Id = existingResult.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<BulkImportActionResultDetailVm>();
        result.GetType().ShouldBe(typeof(BulkImportActionResultDetailVm));
    }

   

    [Fact]
    public async Task Handle_MapEntityAndNodeInfo_WithCorrectValues()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        existingResult.EntityType = "Database";
        existingResult.EntityName = "ProductionDB";
        existingResult.NodeId = "Node123";
        existingResult.NodeName = "ProductionNode";

        var query = new GetBulkImportActionResultDetailQuery { Id = existingResult.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.EntityType.ShouldBe("Database");
        result.EntityName.ShouldBe("ProductionDB");
        result.NodeId.ShouldBe("Node123");
        result.NodeName.ShouldBe("ProductionNode");
    }

    [Fact]
    public async Task Handle_UseGuardAgainstNullOrDeactive_When_EntityValidation()
    {
        // Arrange
        var inactiveResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        inactiveResult.IsActive = false;
        var query = new GetBulkImportActionResultDetailQuery { Id = inactiveResult.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }
}
