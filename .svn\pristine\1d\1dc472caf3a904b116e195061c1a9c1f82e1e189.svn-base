﻿using ContinuityPatrol.Application.Features.DashboardView.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.DashboardView.Commands;

public class CreateDashboardViewTests : IClassFixture<DashboardViewFixture>
{
    private readonly DashboardViewFixture _dashboardViewFixture;

    private readonly Mock<IDashboardViewRepository> _dashboardViewRepositoryMock;

    private readonly CreateDashboardViewCommandHandler _handler;

    public CreateDashboardViewTests(DashboardViewFixture dashboardViewFixture)
    {
        _dashboardViewFixture = dashboardViewFixture;

        var mockPublisher = new Mock<IPublisher>();
    
        _dashboardViewRepositoryMock = DashboardViewRepositoryMocks.CreateDashboardViewRepository(_dashboardViewFixture.DashboardViews);
        
        _handler = new CreateDashboardViewCommandHandler(_dashboardViewRepositoryMock.Object, _dashboardViewFixture.Mapper, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_IncreaseDashboardViewCount_When_DashboardView_Created()
    {
        await _handler.Handle(_dashboardViewFixture.CreateDashboardViewCommand, CancellationToken.None);

        var result = await _dashboardViewRepositoryMock.Object.ListAllAsync();

        result.Count.ShouldBe(_dashboardViewFixture.DashboardViews.Count);
    }

    [Fact]
    public async Task Handle_DashboardViewResponse_When_DashboardView_Created()
    {
        var result = await _handler.Handle(_dashboardViewFixture.CreateDashboardViewCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateDashboardViewResponse));

        result.BusinessViewId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_dashboardViewFixture.CreateDashboardViewCommand, CancellationToken.None);

        _dashboardViewRepositoryMock.Verify(x=>x.AddAsync(It.IsAny<Domain.Entities.DashboardView>()), Times.Once);
    }
}