using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Events.Update;

public class
    DriftManagementMonitorStatusUpdatedEventHandler : INotificationHandler<DriftManagementMonitorStatusUpdatedEvent>
{
    private readonly ILogger<DriftManagementMonitorStatusUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DriftManagementMonitorStatusUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<DriftManagementMonitorStatusUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(DriftManagementMonitorStatusUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} DriftManagementMonitorStatus",
            Entity = "DriftManagementMonitorStatus",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"DriftManagementMonitorStatus '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"DriftManagementMonitorStatus '{updatedEvent.Name}' updated successfully.");
    }
}