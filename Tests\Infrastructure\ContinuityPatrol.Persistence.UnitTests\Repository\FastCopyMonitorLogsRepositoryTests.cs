using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.Extensions.Configuration;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class FastCopyMonitorLogsRepositoryTests : IClassFixture<FastCopyMonitorLogFixture>, IDisposable
{
    private readonly FastCopyMonitorLogFixture _fastCopyMonitorLogFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly FastCopyMonitorLogsRepository _repository;

    public FastCopyMonitorLogsRepositoryTests(FastCopyMonitorLogFixture fastCopyMonitorLogFixture)
    {
        _fastCopyMonitorLogFixture = fastCopyMonitorLogFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        
        var config = new ConfigurationBuilder().Build();
        _repository = new FastCopyMonitorLogsRepository(_dbContext, config);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region GetByInfraObjectId Tests


    [Fact]
    public async Task GetByInfraObjectId_ReturnsEmpty_WhenNoMatchingInfraObjectId()
    {
        // Arrange
        var infraObjectId = "NON_EXISTENT";
        var startDate = "2024-01-01";
        var endDate = "2024-01-31";

        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[0].InfraObjectId = "INFRA_001";
        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[0].CreatedDate = new DateTime(2024, 1, 15);

        await _dbContext.FastCopyMonitorLogs.AddRangeAsync(_fastCopyMonitorLogFixture.FastCopyMonitorLogList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ReturnsEmpty_WhenDateRangeDoesNotMatch()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var startDate = "2024-02-01";
        var endDate = "2024-02-28";

        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[0].InfraObjectId = infraObjectId;
        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[0].CreatedDate = new DateTime(2024, 1, 15);

        await _dbContext.FastCopyMonitorLogs.AddRangeAsync(_fastCopyMonitorLogFixture.FastCopyMonitorLogList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ExcludesInactiveLogs_WhenInactiveLogsExist()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var infraObjectId2 = "INFRA_0012";
        var startDate = DateTime.Now;
        var endDate = DateTime.Now;

        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[0].InfraObjectId = infraObjectId;
        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[0].CreatedDate =  DateTime.Now;
        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[0].IsActive = true;

        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[1].InfraObjectId = infraObjectId2;
        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[1].CreatedDate = DateTime.Now;
        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[1].IsActive = false;
        await _dbContext.FastCopyMonitorLogs.AddRangeAsync(_fastCopyMonitorLogFixture.FastCopyMonitorLogList);
        await _dbContext.SaveChangesAsync();

        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[1].IsActive = false;
        _dbContext.FastCopyMonitorLogs.UpdateRange(_fastCopyMonitorLogFixture.FastCopyMonitorLogList);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate.ToString(), endDate.ToString());

        // Assert
        Assert.Single(result);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetByInfraObjectId_HandlesDateBoundaries_WhenLogsOnBoundaryDates()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var startDate = DateTime.Now;
        var endDate = DateTime.Now;

        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[0].InfraObjectId = infraObjectId;
        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[0].CreatedDate = DateTime.Now;
        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[0].IsActive = true;

        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[1].InfraObjectId = infraObjectId;
        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[1].CreatedDate = DateTime.Now;
        _fastCopyMonitorLogFixture.FastCopyMonitorLogList[1].IsActive = true;

        await _dbContext.FastCopyMonitorLogs.AddRangeAsync(_fastCopyMonitorLogFixture.FastCopyMonitorLogList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate.ToString(), endDate.ToString());

        // Assert
       Assert.Equal(2, result.Count);
      
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddFastCopyMonitorLog_WhenValidLog()
    {
        // Arrange
        var log = _fastCopyMonitorLogFixture.FastCopyMonitorLogDto;
        log.InfraObjectId = "TEST_INFRA";

        // Act
        var result = await _repository.AddAsync(log);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(log.InfraObjectId, result.InfraObjectId);
        Assert.Single(_dbContext.FastCopyMonitorLogs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenLogIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsLog_WhenExists()
    {
        // Arrange
        _fastCopyMonitorLogFixture.FastCopyMonitorLogDto.Id = 1;
        await _dbContext.FastCopyMonitorLogs.AddAsync(_fastCopyMonitorLogFixture.FastCopyMonitorLogDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_fastCopyMonitorLogFixture.FastCopyMonitorLogDto.Id, result.Id);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsAllActiveLogs_WhenLogsExist()
    {
        // Arrange
        await _dbContext.FastCopyMonitorLogs.AddRangeAsync(_fastCopyMonitorLogFixture.FastCopyMonitorLogList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion
}
