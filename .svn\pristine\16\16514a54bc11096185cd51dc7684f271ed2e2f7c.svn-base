﻿using ContinuityPatrol.Application.Features.Replication.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Replication.Events;

public class CreateReplicationEventTests : IClassFixture<ReplicationFixture>, IClassFixture<UserActivityFixture>
{
    private readonly ReplicationFixture _replicationFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly ReplicationCreatedEventHandler _handler;

    public CreateReplicationEventTests(ReplicationFixture replicationFixture, UserActivityFixture userActivityFixture)
    {
        _replicationFixture = replicationFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockReplicationEventLogger = new Mock<ILogger<ReplicationCreatedEventHandler>>();

        _mockUserActivityRepository = ReplicationRepositoryMocks.CreateReplicationEventRepository(_userActivityFixture.UserActivities);

        _handler = new ReplicationCreatedEventHandler(mockLoggedInUserService.Object, mockReplicationEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreatedReplicationEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_replicationFixture.ReplicationCreatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_replicationFixture.ReplicationCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}