﻿using ContinuityPatrol.Application.Features.DRCalendar.Commands.Create;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Update;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDrCalendarDrillEvents;
using ContinuityPatrol.Domain.ViewModels.DRCalendar;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IDrCalendarService
{
    Task<List<DrCalendarActivityListVm>> GetDrCalenderList();
    Task<DrCalendarDetailVm> GetDrCalendarById(string id);
    Task<BaseResponse> CreateAsync(CreateDrCalendarCommand createDrCalenderCommand);
    Task<BaseResponse> UpdateAsync(UpdateDrCalendarCommand updateDrCalenderCommand);
    Task<BaseResponse> DeleteAsync(string calId);
    Task<PaginatedResult<DrCalendarActivityListVm>> GetPaginatedDrCalendar(GetDrCalendarPaginatedListQuery query);
    Task<GetUpcomingDrillCountVm> GetDrCalendarDrillEvents();
    Task<bool> IsDrCalendarNameExist(string activityName, string? id, DateTime scheduleStartTime);

}