using ContinuityPatrol.Application.Features.CyberSnaps.Commands.Create;
using ContinuityPatrol.Application.Features.CyberSnaps.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberSnaps.Commands.Update;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetByGroupNameAndLinkedStatus;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetPowerMax;
using ContinuityPatrol.Domain.ViewModels.CyberSnapsModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class CyberSnapsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<List<CyberSnapsListVm>>> GetCyberSnapss()
    {
        Logger.LogDebug("Get All CyberSnaps");

        return Ok(await Mediator.Send(new GetCyberSnapsListQuery()));
    }

    [HttpGet,Route("by/cybersnapsDtl")]
    [Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<List<CyberSnapsListVm>>> GetCyberSnapsByStorageGroupNameAndLinkedStatus(string? storageGroupName,string? linkedStatus,string? snapshotName)
    {
        Logger.LogDebug($"Get GetCyberSnaps By StorageGroupName {storageGroupName} and LinkedStatus {linkedStatus}.");

        return Ok(await Mediator.Send(new GetCyberSnapsByGroupNameAndLinkedStatusQuery { StorageGroupName = storageGroupName, LinkedStatus = linkedStatus, SnapshotName = snapshotName }));
    }

    [HttpGet("{id}", Name = "GetCyberSnaps")]
    [Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<CyberSnapsDetailVm>> GetCyberSnapsById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberSnaps Id");

        Logger.LogDebug($"Get CyberSnaps Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetCyberSnapsDetailQuery { Id = id }));
    }
    [HttpGet,Route("powerMax")]
    [Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<PowerMaxDetailVm>> GetPowerMaxMonitorStatus(string? name, bool isSnap)
    {
        Logger.LogDebug($"Get PowerMaxMonitorStatus Details by name '{name}'");

        return Ok(await Mediator.Send(new GetPowerMaxDetailsQuery { Name = name, IsSnap = isSnap }));
    }
    #region Paginated
    
    [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Cyber.View)]
 public async Task<ActionResult<PaginatedResult<CyberSnapsListVm>>> GetPaginatedCyberSnapss([FromQuery] GetCyberSnapsPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in CyberSnaps Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Cyber.Create)]
    public async Task<ActionResult<CreateCyberSnapsResponse>> CreateCyberSnaps([FromBody] CreateCyberSnapsCommand createCyberSnapsCommand)
    {
        Logger.LogDebug($"Create CyberSnaps '{createCyberSnapsCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateCyberSnaps), await Mediator.Send(createCyberSnapsCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Cyber.Edit)]
    public async Task<ActionResult<UpdateCyberSnapsResponse>> UpdateCyberSnaps([FromBody] UpdateCyberSnapsCommand updateCyberSnapsCommand)
    {
        Logger.LogDebug($"Update CyberSnaps '{updateCyberSnapsCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateCyberSnapsCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Cyber.Delete)]
    public async Task<ActionResult<DeleteCyberSnapsResponse>> DeleteCyberSnaps(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberSnaps Id");

        Logger.LogDebug($"Delete CyberSnaps Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteCyberSnapsCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsCyberSnapsNameExist(string cyberSnapsName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(cyberSnapsName, "CyberSnaps Name");

     Logger.LogDebug($"Check Name Exists Detail by CyberSnaps Name '{cyberSnapsName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetCyberSnapsNameUniqueQuery { Name = cyberSnapsName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


