using ContinuityPatrol.Application.Features.PageWidget.Commands.Create;
using ContinuityPatrol.Application.Features.PageWidget.Commands.Delete;
using ContinuityPatrol.Application.Features.PageWidget.Commands.Update;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetList;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PageWidgetModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class PageWidgetsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<PageWidgetListVm>>> GetPageWidgets()
    {
        Logger.LogDebug("Get All PageWidgets");

        return Ok(await Mediator.Send(new GetPageWidgetListQuery()));
    }

    [HttpGet("{id}", Name = "GetPageWidget")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<PageWidgetDetailVm>> GetPageWidgetById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "PageWidget Id");

        Logger.LogDebug($"Get PageWidget Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetPageWidgetDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Admin.View)]
 public async Task<ActionResult<PaginatedResult<PageWidgetListVm>>> GetPaginatedPageWidgets([FromQuery] GetPageWidgetPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in PageWidget Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<ActionResult<CreatePageWidgetResponse>> CreatePageWidget([FromBody] CreatePageWidgetCommand createPageWidgetCommand)
    {
        Logger.LogDebug($"Create PageWidget '{createPageWidgetCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreatePageWidget), await Mediator.Send(createPageWidgetCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<ActionResult<UpdatePageWidgetResponse>> UpdatePageWidget([FromBody] UpdatePageWidgetCommand updatePageWidgetCommand)
    {
        Logger.LogDebug($"Update PageWidget '{updatePageWidgetCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updatePageWidgetCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Admin.Delete)]
    public async Task<ActionResult<DeletePageWidgetResponse>> DeletePageWidget(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "PageWidget Id");

        Logger.LogDebug($"Delete PageWidget Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeletePageWidgetCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsPageWidgetNameExist(string pageWidgetName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(pageWidgetName, "PageWidget Name");

     Logger.LogDebug($"Check Name Exists Detail by PageWidget Name '{pageWidgetName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetPageWidgetNameUniqueQuery { Name = pageWidgetName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


