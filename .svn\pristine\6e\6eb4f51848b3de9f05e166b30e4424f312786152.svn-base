﻿using ContinuityPatrol.Application.Features.Workflow.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.Workflow.Queries;

public class GetWorkflowPaginatedListQueryHandlerTests : IClassFixture<WorkflowFixture>
{
    private readonly WorkflowFixture _workflowFixture;
    private readonly Mock<IWorkflowRepository> _mockWorkflowRepository;
    private readonly GetWorkflowPaginatedListQueryHandler _handler;

    public GetWorkflowPaginatedListQueryHandlerTests(WorkflowFixture workflowFixture)
    {
        _workflowFixture = workflowFixture;

        _workflowFixture.Workflows[0].Name = "Test_workflows";
        _workflowFixture.Workflows[0].Version = "8";
        _workflowFixture.Workflows[0].Properties = "Test";

        _workflowFixture.Workflows[1].Name = "Test_workflow";
        _workflowFixture.Workflows[1].Version = "84";
        _workflowFixture.Workflows[1].Properties = "Check";
        
        _mockWorkflowRepository = WorkflowRepositoryMocks.GetPaginatedWorkflowRepository(_workflowFixture.Workflows);
        _handler = new GetWorkflowPaginatedListQueryHandler(_workflowFixture.Mapper, _mockWorkflowRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetWorkflowPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_Workflow_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetWorkflowPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=Test;Xml=check;actionIds=Alpha" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Name.ShouldBe(_workflowFixture.Workflows[0].Name);
        result.Data[0].Properties.ShouldBe(_workflowFixture.Workflows[0].Properties);
        result.Data[0].Version.ShouldBe(_workflowFixture.Workflows[0].Version);

        result.Data[1].Name.ShouldBe(_workflowFixture.Workflows[1].Name);
        result.Data[1].Version.ShouldBe(_workflowFixture.Workflows[1].Version);
        result.Data[1].Properties.ShouldBe(_workflowFixture.Workflows[1].Properties);
    }

    [Fact]
    public async Task Handle_Return_PaginatedWorkflow_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetWorkflowPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "workf" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());
        result.Data[0].Name.ShouldBe(_workflowFixture.Workflows[0].Name);
        result.Data[0].Properties.ShouldBe(_workflowFixture.Workflows[0].Properties);
        result.Data[0].Version.ShouldBe(_workflowFixture.Workflows[0].Version);

        result.Data[1].Name.ShouldBe(_workflowFixture.Workflows[1].Name);
        result.Data[1].Version.ShouldBe(_workflowFixture.Workflows[1].Version);
        result.Data[1].Properties.ShouldBe(_workflowFixture.Workflows[1].Properties);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetWorkflowPaginatedListQuery() { PageNumber = 1, PageSize = 10, SearchString = "ABCD" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetWorkflowPaginatedListQuery(), CancellationToken.None);

        _mockWorkflowRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}