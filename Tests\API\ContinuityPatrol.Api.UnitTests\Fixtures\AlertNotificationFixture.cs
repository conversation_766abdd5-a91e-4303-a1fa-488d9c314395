using ContinuityPatrol.Application.Features.AlertNotification.Commands.Create;
using ContinuityPatrol.Application.Features.AlertNotification.Commands.Update;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.AlertNotificationModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class AlertNotificationFixture
{
    public List<AlertNotificationListVm> AlertNotificationListVm { get; }
    public AlertNotificationDetailVm AlertNotificationDetailVm { get; }
    public CreateAlertNotificationCommand CreateAlertNotificationCommand { get; }
    public UpdateAlertNotificationCommand UpdateAlertNotificationCommand { get; }

    public AlertNotificationFixture()
    {
        var fixture = new Fixture();

        // Create sample AlertNotification list data
        AlertNotificationListVm = new List<AlertNotificationListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                AlertCategoryId = 1,
                AlertCode = "CRIT_001",
                AlertType = "Critical",
                AlertSentCount = 5,
                InfraObjectId = Guid.NewGuid().ToString(),
                EntityId = Guid.NewGuid().ToString(),
                PositiveAlertCount = 3
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                AlertCategoryId = 2,
                AlertCode = "WARN_001",
                AlertType = "Warning",
                AlertSentCount = 10,
                InfraObjectId = Guid.NewGuid().ToString(),
                EntityId = Guid.NewGuid().ToString(),
                PositiveAlertCount = 7
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                AlertCategoryId = 3,
                AlertCode = "INFO_001",
                AlertType = "Information",
                AlertSentCount = 2,
                InfraObjectId = Guid.NewGuid().ToString(),
                EntityId = Guid.NewGuid().ToString(),
                PositiveAlertCount = 1
            }
        };

        // Create detailed AlertNotification data
        AlertNotificationDetailVm = new AlertNotificationDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            AlertCategoryId = 1,
            AlertCode = "CRIT_001",
            AlertType = "Critical",
            AlertSentCount = 5,
            InfraObjectId = Guid.NewGuid().ToString(),
            EntityId = Guid.NewGuid().ToString(),
            PositiveAlertCount = 3
        };

        // Create command for creating AlertNotification
        CreateAlertNotificationCommand = new CreateAlertNotificationCommand
        {
            AlertCategoryId = 4,
            AlertCode = "ERR_001",
            AlertType = "Error",
            AlertSentCount = 0,
            InfraObjectId = Guid.NewGuid().ToString(),
            EntityId = Guid.NewGuid().ToString(),
            PositiveAlertCount = 0
        };

        // Create command for updating AlertNotification
        UpdateAlertNotificationCommand = new UpdateAlertNotificationCommand
        {
            Id = Guid.NewGuid().ToString(),
            AlertCategoryId = 2,
            AlertCode = "WARN_002",
            AlertType = "Updated Warning",
            AlertSentCount = 15,
            InfraObjectId = Guid.NewGuid().ToString(),
            EntityId = Guid.NewGuid().ToString(),
            PositiveAlertCount = 12
        };
    }
}
