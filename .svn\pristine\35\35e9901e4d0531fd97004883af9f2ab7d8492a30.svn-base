﻿using ContinuityPatrol.Application.Features.LicenseInfo.Commands.Create;
using ContinuityPatrol.Application.Features.LicenseInfo.Commands.Delete;
using ContinuityPatrol.Application.Features.LicenseInfo.Commands.Update;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetAvailableCount;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetByEntity;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetDetailView;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetList;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.LicenseInfoModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class LicenseInfoController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<LicenseInfoListVm>>> GetLicenseInfo()
    {
        Logger.LogDebug("Get All License Info");

        return Ok(await Mediator.Send(new GetLicenseInfoListQuery()));
    }
    [HttpGet("DetailView")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<LicenseInfoDetailViewVm>>> GetLicenseInfoById(string licenseId)
    {
        Logger.LogDebug($"Get  LicenseInfo Detail view by LicenseId '{licenseId}'");

        return Ok(await Mediator.Send(new GetLicenseInfoDetailViewQuery { LicenseId = licenseId }));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<ActionResult<CreateLicenseInfoResponse>> CreateLicenseInfo([FromBody] CreateLicenseInfoCommand createLicenseInfoCommand)
    {
        Logger.LogDebug($"Create LicenseInfo '{createLicenseInfoCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateLicenseInfo), await Mediator.Send(createLicenseInfoCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<ActionResult<UpdateLicenseInfoResponse>> UpdateLicenseInfo([FromBody] UpdateLicenseInfoCommand updateLicenseInfoCommand)
    {
        Logger.LogDebug($"Update LicenseInfo '{updateLicenseInfoCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateLicenseInfoCommand));
    }

    [HttpDelete("{entityId}")]
    [Authorize(Policy = Permissions.Admin.Delete)]
    public async Task<ActionResult<DeleteLicenseInfoResponse>> DeleteLicenseInfo(string entityId)
    {
        Logger.LogDebug($"Delete LicenseInfo Details by  Entity Id '{entityId}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteLicenseInfoCommand { EntityId = entityId }));
    }

    [HttpGet("{licenseId}")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<LicenseInfoDetailVm>>> GetLicenseInfoByLicenseId(string licenseId)
    {
        Guard.Against.InvalidGuidOrEmpty(licenseId, "licenseId");

        Logger.LogDebug($"Get LicenseInfo Detail by License Id'{licenseId}'");

        return Ok(await Mediator.Send(new GetLicenseInfoDetailQuery { LicenseId = licenseId }));
    }

    [HttpGet("availablecountby/{licenseId}")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<AvailableCountVm>> GetAvailableCountByLicenseId(string licenseId)
    {
        Guard.Against.InvalidGuidOrEmpty(licenseId, "LicenseId");

        Logger.LogDebug($"Get available count Detail by License id'{licenseId}'");

        return Ok(await Mediator.Send(new GetAvailableCountQuery { LicenseId = licenseId }));
    }

    [HttpGet("businessserviceid")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<LicenseInfoByBusinessServiceIdListVm>> GetLicenseByBusinessServiceId(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "businessService Id");

        Logger.LogDebug($"Get available count Detail by BusinessService id'{businessServiceId}'");

        return Ok(await Mediator.Send(new GetLicenseInfoDetailByBusinessServiceIdListQuery { BusinessServiceId = businessServiceId }));
    }


    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<PaginatedResult<LicenseInfoListVm>>> GetPaginatedLicenseInfo([FromQuery] GetLicenseInfoPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in LicenseInfo Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpGet("entity")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<LicenseInfoByEntityListVm>>> GetLicenseInfoByEntity(string licenseId, string entity,string? entityType)
    {
        Logger.LogDebug($"Get All LicenseInfo by LicenseId: '{licenseId}' and Entity :'{entity}'");

        return Ok(await Mediator.Send(new GetLicenseInfoByEntityListQuery { LicenseId = licenseId, Entity = entity ,EntityType = entityType}));
    }

    [HttpGet("type")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<LicenseInfoTypeListVm>>> GetLicenseInfoByType(string licenseId, string type,string? entityType)
    {
        Logger.LogDebug($"Get All LicenseInfo by LicenseId '{licenseId}' and Types '{type}'");

        return Ok(await Mediator.Send(new GetLicenseInfoTypeListQuery { LicenseId = licenseId, Type = type,EntityType = entityType }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllLicenseInfoCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllLicenseInfoNamesCacheKey };

        ClearCache(cacheKeys);
    }
}
