using ContinuityPatrol.Application.Contexts;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Persistence.Persistence;

public partial class ApplicationDbContext : IAdminDbContext
{
    #region Context
	public DbSet<MenuBuilder> MenuBuilders { get; set; }
    public DbSet<DynamicDashboardWidget> DynamicDashboardWidgets { get; set; }
    public DbSet<DynamicDashboardMap> DynamicDashboardMaps { get; set; }
    public DbSet<DynamicSubDashboard> DynamicSubDashboards { get; set; }
    public DbSet<DynamicDashboard> DynamicDashboards { get; set; }
    public DbSet<BackUpLog> BackUpLogs { get; set; }
    public DbSet<InfraMaster> InfraMasters { get; set; }
    public DbSet<BackUp> BackUps { get; set; }
    public DbSet<Archive> Archives { get; set; }
    public DbSet<PageWidget> PageWidgets { get; set; }
    public DbSet<PageBuilder> PageBuilders { get; set; }
    public DbSet<PageSolutionMapping> PageSolutionMappings { get; set; }
    public DbSet<Form> Forms { get; set; }
    public DbSet<WorkflowCategory> WorkflowCategories { get; set; }
    public DbSet<WorkflowAction> WorkflowActions { get; set; }
    public DbSet<LicenseManager> LicenseManagers { get; set; }
    public DbSet<SolutionHistory> SolutionHistories { get; set; }
    public DbSet<FormHistory> FormHistories { get; set; }
    public DbSet<FormType> FormTypes { get; set; }
    public DbSet<PluginManager> PluginManagers { get; set; }
    public DbSet<PluginManagerHistory> PluginManagerHistories { get; set; }
    public DbSet<LicenseInfo> LicenseInfo { get; set; }
    public DbSet<LoadBalancer> NodeConfigurations { get; set; }
    public DbSet<LicenseHistory> LicenseHistories { get; set; }
    public DbSet<FormTypeCategory> FormTypeCategories { get; set; }
    public DbSet<GroupPolicy> GroupPolicies { get; set; }
    public DbSet<ComplianceHistory> ComplianceHistories { get; set; }
   public DbSet<WorkflowCategoryView> WorkflowCategoryViews { get; set; }  
    public DbSet<SolutionTypeTables> solutionTypeTables { get;set; }

    #endregion
}
