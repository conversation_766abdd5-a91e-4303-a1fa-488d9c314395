﻿using ContinuityPatrol.Application.Features.Server.Commands.Create;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAll;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Hubs;
using ContinuityPatrol.Shared.Services.Provider;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.API;

[Route("api/[controller]")]
[ApiController]
public class SaveAllController : ControllerBase
{
    private readonly IDataProvider _dataProvider;
    private readonly IHubContext<NotificationHub> _hubContext;
    private readonly ILogger<SaveAllController> _logger;


    public SaveAllController(IDataProvider dataProvider, IHubContext<NotificationHub> hubContext, ILogger<SaveAllController> logger)
    {
        _dataProvider = dataProvider;
        _hubContext = hubContext;
        _logger = logger;
    }

    [HttpPost("server")]
    [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<IActionResult> ServerSaveAll([FromBody] string componentId)
    {
        Guard.Against.InvalidGuidOrEmpty(componentId, "ComponentSaveAll Id");

        var eventToUpdate = await _dataProvider.ComponentSaveAll.GetComponentById(componentId);

        var serverCommand = JsonConvert.DeserializeObject<SaveAllServerCommand>(eventToUpdate.Properties);

        var eventToServer = await _dataProvider.Server.GetByReferenceId(serverCommand.ServerId);

        foreach (var server in serverCommand.ServerList)
        {
            try
            {
                var command = new CreateServerCommand
                {
                    Name = server.Name,
                    SiteId = server.SiteId,
                    SiteName = server.SiteName,
                    Properties = server.Properties,
                    ServerTypeId = server.ServerTypeId,
                    ServerType = server.ServerType,
                    LicenseId = server.LicenseId,
                    LicenseKey = server.LicenseKey,
                    Logo = server.Logo,

                    BusinessServiceId = eventToServer.BusinessServiceId,
                    BusinessServiceName = eventToServer.BusinessServiceName,
                    Status = "Pending",
                    RoleTypeId = eventToServer.RoleTypeId,
                    RoleType = eventToServer.RoleType,
                    OSType = eventToServer.OSType,
                    OSTypeId = eventToServer.OSTypeId,
                    Version = eventToServer.Version,
                    FormVersion = eventToServer.FormVersion
                };

                var response = await _dataProvider.Server.CreateAsync(command);

                if (response.Success)
                {
                    await SendMessage(response.Message, true);
                }
                else
                {
                    await SendMessage(response.Message ?? $"Server {server.Name} creation failed.", false);
                }

            }
            catch (Exception ex)
            {
                _logger.Exception($"Server {server.Name} creation failed.", ex);
                await SendMessage($"Server {server.Name} creation failed.", false);
            }

           
        }
        return Ok();
    }

    [NonAction]
    private async Task SendMessage(string message, bool success)
    {
        if (_hubContext != null)
        {
            await _hubContext.Clients.All.SendAsync("ServerSaveAllMessage", new
            {
                Success = success,
                Message = message
            });
        }
    }
}