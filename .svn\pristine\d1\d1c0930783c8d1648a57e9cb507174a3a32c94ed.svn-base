﻿using AutoFixture;
using ContinuityPatrol.Application.Features.BusinessService.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessService.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessService.Events.PaginatedView;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Site.Queries.GetSiteByCompanyId;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Domain.ViewModels.SiteTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class OperationalServiceControllerShould
    {
        private readonly Mock<ILogger<OperationalServiceController>> _mockLogger =new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IPublisher> _mockPublisher = new();
        private  OperationalServiceController _controller;

        public OperationalServiceControllerShould()
        {
            
            _controller = new OperationalServiceController(_mockLogger.Object, _mockPublisher.Object, _mockDataProvider.Object, _mockMapper.Object);
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_ReturnsViewResult_WithBusinessServiceViewModel()
        {
            // Arrange
            var siteTypes = new List<SiteTypeListVm>
            {
                new SiteTypeListVm { Id = "1", Type = "Primary" },
                new SiteTypeListVm { Id = "2", Type = "Secondary" }
            };

            // Setup publisher mock to handle the event publishing
            _mockPublisher.Setup(p => p.Publish(It.IsAny<BusinessServicePaginatedEvent>(), default))
                .Returns(Task.CompletedTask);

            // Setup only SiteType data provider since BusinessService call is commented out in production code
            _mockDataProvider.Setup(dp => dp.SiteType.GetSiteTypeList())
                .ReturnsAsync(siteTypes);

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            var model = Assert.IsType<BusinessServiceViewModel>(result.Model);

            // Only SiteTypes should be populated since BusinessServices assignment is commented out in production code
            Assert.Equal(siteTypes, model.SiteTypes);
            Assert.Null(model.BusinessServices); // This should be null since it's not set in production code

            // Verify that the publisher was called
            _mockPublisher.Verify(p => p.Publish(It.IsAny<BusinessServicePaginatedEvent>(), default), Times.Once);
        }

        [Fact]
        public async Task GetList_ReturnsJsonResult_WithBusinessServiceList()
        {
            // Arrange
            var businessServices = new List<BusinessServiceListVm>();
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceList())
                .ReturnsAsync(businessServices);

            // Act
            var result = await _controller.GetList() as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(businessServices, jsonResult.Value);
        }

        [Fact]
        public async Task CreateOrUpdate_CreatesBusinessService_WhenIdIsNull()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<BusinessServiceViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", ""); // Empty ID for create path (note: capital "I" and empty value)
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            _mockMapper.Setup(m => m.Map<CreateBusinessServiceCommand>(viewModel))
                .Returns(new CreateBusinessServiceCommand { Name = "TestService" });
            _mockDataProvider.Setup(dp => dp.BusinessService.CreateAsync(It.IsAny<CreateBusinessServiceCommand>()))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Created" });

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"Message\":\"Created\"", json);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesBusinessService_WhenIdIsNotNull()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<BusinessServiceViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "22"); // Non-empty ID for update path (note: capital "I")
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            _mockMapper.Setup(m => m.Map<UpdateBusinessServiceCommand>(viewModel))
                .Returns(new UpdateBusinessServiceCommand { Name = "UpdatedService" });
            _mockDataProvider.Setup(dp => dp.BusinessService.UpdateAsync(It.IsAny<UpdateBusinessServiceCommand>()))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Updated" });

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"Message\":\"Updated\"", json);
        }

        [Fact]
        public async Task Delete_ReturnsRedirectToActionResult()
        {
            // Arrange
            var id = "1";
            _mockDataProvider.Setup(dp => dp.BusinessService.DeleteAsync(id))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Deleted" });

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task Delete_HandlesException()
        {
            // Arrange
            var id = "1";
            _mockDataProvider.Setup(dp => dp.BusinessService.DeleteAsync(id))
                .ThrowsAsync(new Exception("Delete failed"));

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task IsBusinessServiceNameExist_ReturnsTrue()
        {
            // Arrange
            var name = "TestService";
            var id = "1";
            _mockDataProvider.Setup(dp => dp.BusinessService.IsBusinessServiceNameExist(name, id))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.IsBusinessServiceNameExist(name, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonResult_WithPaginatedList()
        {
            // Arrange
            var query = new GetBusinessServicePaginatedListQuery();
            var paginatedList = new PaginatedResult<BusinessServiceListVm>();
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServicePaginatedList(query))
                .ReturnsAsync(paginatedList);

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(paginatedList, jsonResult.Value);
        }

        [Fact]
        public async Task GetBusinessServiceNames_ReturnsJsonResult_WithNames()
        {
            // Arrange
            var names = new List<BusinessServiceNameVm> ();
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames())
                .ReturnsAsync(names);

            // Act
            var result = await _controller.GetBusinessServiceNames() as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetBusinessServiceNames_HandlesException()
        {

            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames())
                .ThrowsAsync(new Exception("Service error"));


            var result = await _controller.GetBusinessServiceNames() as JsonResult;


            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":false", json);
            Assert.NotNull(result);
        }

        // ===== MISSING TEST CASES FOR 100% COVERAGE =====

        [Fact]
        public async Task GetList_HandlesException_ReturnsEmptyJson()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceList())
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.GetList() as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        // This test case is now covered by CreateOrUpdate_CreatesBusinessService_WhenIdIsNull above

        [Fact]
        public async Task CreateOrUpdate_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<BusinessServiceViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", ""); // Empty ID for create path (note: capital "I")
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            _mockMapper.Setup(m => m.Map<CreateBusinessServiceCommand>(viewModel))
                .Throws(new Exception("Mapping error"));

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            Assert.NotNull(result);
            // The production code catches exceptions and returns ex.GetJsonException()
            // This should be a JsonResult with error information
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task GetSiteByTypeAndCompanyId_ReturnsJsonResult_WithSiteList()
        {
            // Arrange
            var companyId = "123";
            var siteList = new List<GetSiteByCompanyIdVm>
            {
                new GetSiteByCompanyIdVm { Id = "1", Name = "Site1" },
                new GetSiteByCompanyIdVm { Id = "2", Name = "Site2" }
            };
            _mockDataProvider.Setup(dp => dp.Site.GetSiteByCompanyId(companyId))
                .ReturnsAsync(siteList);

            // Act
            var result = await _controller.GetSiteByTypeAndCompanyId(companyId) as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(siteList, jsonResult.Value);
        }

        [Fact]
        public async Task GetSiteByTypeAndCompanyId_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var companyId = "123";
            _mockDataProvider.Setup(dp => dp.Site.GetSiteByCompanyId(companyId))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.GetSiteByTypeAndCompanyId(companyId);

            // Assert
            Assert.NotNull(result);
            // The result should be a JsonResult with error information
        }

        [Fact]
        public async Task IsBusinessServiceNameExist_HandlesException_ReturnsFalse()
        {
            // Arrange
            var name = "TestService";
            var id = "1";
            _mockDataProvider.Setup(dp => dp.BusinessService.IsBusinessServiceNameExist(name, id))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.IsBusinessServiceNameExist(name, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetPagination_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var query = new GetBusinessServicePaginatedListQuery();
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServicePaginatedList(query))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            Assert.NotNull(result);
            // The result should be a JsonResult with error information
        }

        // ===== CONSTRUCTOR TESTS =====

        [Fact]
        public void Constructor_ShouldAcceptNullLogger_WithoutThrowingException()
        {
            // Act & Assert - Production code doesn't validate null, so it should not throw
            var exception = Record.Exception(() => new OperationalServiceController(
                null!,
                _mockPublisher.Object,
                _mockDataProvider.Object,
                _mockMapper.Object
            ));
            Assert.Null(exception);
        }

        [Fact]
        public void Constructor_ShouldAcceptNullPublisher_WithoutThrowingException()
        {
            // Act & Assert - Production code doesn't validate null, so it should not throw
            var exception = Record.Exception(() => new OperationalServiceController(
                _mockLogger.Object,
                null!,
                _mockDataProvider.Object,
                _mockMapper.Object
            ));
            Assert.Null(exception);
        }

        [Fact]
        public void Constructor_ShouldAcceptNullDataProvider_WithoutThrowingException()
        {
            // Act & Assert - Production code doesn't validate null, so it should not throw
            var exception = Record.Exception(() => new OperationalServiceController(
                _mockLogger.Object,
                _mockPublisher.Object,
                null!,
                _mockMapper.Object
            ));
            Assert.Null(exception);
        }

        [Fact]
        public void Constructor_ShouldAcceptNullMapper_WithoutThrowingException()
        {
            // Act & Assert - Production code doesn't validate null, so it should not throw
            var exception = Record.Exception(() => new OperationalServiceController(
                _mockLogger.Object,
                _mockPublisher.Object,
                _mockDataProvider.Object,
                null!
            ));
            Assert.Null(exception);
        }

        // ===== ADDITIONAL COVERAGE TESTS =====

        [Fact]
        public async Task List_ShouldPublishBusinessServicePaginatedEvent()
        {
            // Arrange
            var siteTypes = new List<SiteTypeListVm>();
            _mockDataProvider.Setup(dp => dp.SiteType.GetSiteTypeList()).ReturnsAsync(siteTypes);

            // Act
            await _controller.List();

            // Assert
            _mockPublisher.Verify(p => p.Publish(It.IsAny<BusinessServicePaginatedEvent>(), default), Times.Once);
        }

        // This test case is now covered by CreateOrUpdate_UpdatesBusinessService_WhenIdIsNotNull above
    }
}
