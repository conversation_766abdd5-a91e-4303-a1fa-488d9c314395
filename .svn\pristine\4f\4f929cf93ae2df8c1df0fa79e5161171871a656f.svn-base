﻿using ContinuityPatrol.Application.Features.Company.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Company.Commands;

public class DeleteCompanyTests : IClassFixture<CompanyFixture>
{
    private readonly CompanyFixture _companyFixture;
    private readonly Mock<ICompanyRepository> _mockCompanyRepository;
    private readonly DeleteCompanyCommandHandler _handler;

    public DeleteCompanyTests(CompanyFixture companyFixture)
    {
        _companyFixture = companyFixture;

        var mockPublisher = new Mock<IPublisher>();
        var mockSiteRepository = new Mock<ISiteRepository>();
        mockSiteRepository.Setup(x => x.GetSiteByCompanyId(It.IsAny<string>())).ReturnsAsync(new List<Domain.Entities.Site>());

        var mockUserRepository = new Mock<IUserRepository>();
        mockUserRepository.Setup(x => x.GetUsersByCompanyId(It.IsAny<string>())).ReturnsAsync(new List<Domain.Entities.User>());

        _mockCompanyRepository = CompanyRepositoryMocks.DeleteCompanyRepository(_companyFixture.Companies);

        _handler = new DeleteCompanyCommandHandler(_mockCompanyRepository.Object, mockPublisher.Object,mockSiteRepository.Object,mockUserRepository.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_CompanyDeleted()
    {
        var validGuid = Guid.NewGuid();

        _companyFixture.Companies[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteCompanyCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteCompanyResponse_When_CompanyDeleted()
    {
        var validGuid = Guid.NewGuid();

        _companyFixture.Companies[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteCompanyCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteCompanyResponse));

        result.IsActive.ShouldBeFalse();

        result.Success.ShouldBeTrue();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_CompanyDeleted_ByReferenceId()
    {
        var validGuid = Guid.NewGuid();

        _companyFixture.Companies[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteCompanyCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var company = await _mockCompanyRepository.Object.GetByReferenceIdAsync(validGuid.ToString());

        company.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidCompanyId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteCompanyCommand { Id = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid();

        _companyFixture.Companies[0].ReferenceId = validGuid.ToString();

        //var company = _companyFixture.Companies[0];

        //var ValidGuid = _companyFixture.Companies[0].ReferenceId.ToString();

        _companyFixture.Companies[0].ReferenceId = Guid.NewGuid().ToString();

        await _handler.Handle(new DeleteCompanyCommand { Id = _companyFixture.Companies[0].ReferenceId }, CancellationToken.None);

        _mockCompanyRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockCompanyRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.Company>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowEntityAssociatedException_WhenSitesOrUsersExist()
    {
        var associatedSites = new List<Domain.Entities.Site> { new() { ReferenceId = Guid.NewGuid().ToString() } };
       // var associatedUsers = new List<Domain.Entities.User> { new() { ReferenceId = Guid.NewGuid().ToString() } };

        var mockPublisher = new Mock<IPublisher>();

        var mockSiteRepository = new Mock<ISiteRepository>();
        mockSiteRepository
            .Setup(x => x.GetSiteByCompanyId(It.IsAny<string>()))
            .ReturnsAsync(associatedSites);

        var mockUserRepository = new Mock<IUserRepository>();
        mockUserRepository
            .Setup(x => x.GetUsersByCompanyId(It.IsAny<string>()))
            .ReturnsAsync(new List<Domain.Entities.User>());


        var mockCompanyRepository = CompanyRepositoryMocks.DeleteCompanyRepository(_companyFixture.Companies);

        var handler = new DeleteCompanyCommandHandler(
            mockCompanyRepository.Object,
            mockPublisher.Object,
            mockSiteRepository.Object,
            mockUserRepository.Object
        );
        var companyId = Guid.NewGuid().ToString();

        var command = new DeleteCompanyCommand { Id = companyId };

        await Assert.ThrowsAsync<EntityAssociatedException>(() =>
            handler.Handle(command, CancellationToken.None));

    }
}