﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class FormRepositoryMocks
{
    public static Mock<IFormRepository> CreateFormRepository(List<Form> forms)
    {
        var formRepository = new Mock<IFormRepository>();

        formRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(forms);
        
        formRepository.Setup(repo => repo.AddAsync(It.IsAny<Form>())).ReturnsAsync(
            (Form form) =>
            {
                form.Id = new Fixture().Create<int>();
                
                form.ReferenceId = new Fixture().Create<Guid>().ToString();
                
                forms.Add(form);
                return form;
            });

        return formRepository;
    }

    public static Mock<IFormRepository> UpdateFormRepository(List<Form> forms)
    {
        var formRepository = new Mock<IFormRepository>();

        formRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(forms);

        formRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => forms.SingleOrDefault(x => x.ReferenceId == i));

        formRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Form>())).ReturnsAsync((Form form) =>
        {
            var index = forms.FindIndex(item => item.ReferenceId == form.ReferenceId);

            forms[index] = form;

            return form;
        });
        return formRepository;
    }

        public static Mock<IFormRepository> DeleteFormRepository(List<Form> forms)
        {
            var formRepository = new Mock<IFormRepository>();

            formRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(forms);

            formRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => forms.SingleOrDefault(x => x.ReferenceId == i));

            formRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Form>())).ReturnsAsync((Form form) =>
            {
                var index = forms.FindIndex(item => item.ReferenceId == form.ReferenceId);

                form.IsActive = false;
                
                forms[index] = form;

                return form;
            });

            return formRepository;
        }

    public static Mock<IFormRepository> GetFormRepository(List<Form> forms)
    {
        var formRepository = new Mock<IFormRepository>();

        formRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(forms);

        formRepository.Setup(repo => repo.GetFormType(It.IsAny<string>())).ReturnsAsync(forms);

        formRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => forms.SingleOrDefault(x => x.ReferenceId == i));
        //formRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(forms);

        return formRepository;
    }

    public static Mock<IFormRepository> GetFormEmptyRepository()
    {
        var formRepository = new Mock<IFormRepository>();

        formRepository.Setup(repo => repo.GetFormType(It.IsAny<string>())).ReturnsAsync(new List<Form>());

        formRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Form>());
        
        return formRepository;
    }

    public static Mock<IFormRepository> GetFormNamesRepository(List<Form> forms)
    {
        var formRepository = new Mock<IFormRepository>();
        
        formRepository.Setup(repo => repo.GetFormNames()).ReturnsAsync(forms);

        return formRepository;
    }

    public static Mock<IFormRepository> GetFormNameUniqueRepository(List<Form> forms)
    {
        var formRepository = new Mock<IFormRepository>();

        formRepository.Setup(repo => repo.IsFormNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) =>
        {
            return j == 0.ToString() ? forms.Exists(x => x.Name == i) : forms.Exists(x => x.Name == i && x.ReferenceId == j);
        });

        return formRepository;
    }

    public static Mock<IFormRepository> GetPaginatedFormRepository(List<Form> forms)
    {
        var formRepository = new Mock<IFormRepository>();

        var queryableForm = forms.BuildMock();

        formRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableForm);

        return formRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateFormEventRepository(List<UserActivity> userActivities)
    {
        var formEventRepository = new Mock<IUserActivityRepository>();

        formEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return formEventRepository;
    }
}