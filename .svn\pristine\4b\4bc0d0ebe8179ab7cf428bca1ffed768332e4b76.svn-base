﻿using ContinuityPatrol.Application.Features.User.Events.Create;
using ContinuityPatrol.Application.Features.User.Events.SendEmail;
using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Features.User.Commands.Create;

public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, CreateUserResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IUnitOfWork<DbContext> _unitOfWork;

    public CreateUserCommandHandler(IMapper mapper, IUnitOfWork<DbContext> unitOfWork, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _unitOfWork = unitOfWork;
    }

    public async Task<CreateUserResponse> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        var user = _mapper.Map<Domain.Entities.User>(request);

        // user.LoginPassword = SecurityHelper.Encrypt(request.LoginPassword);

        if (request.LoginPassword.Contains(request.LoginName, StringComparison.OrdinalIgnoreCase))
            throw new BadRequestException("Password contains Username");

        if (user is null) throw new ArgumentNullException(nameof(user));

        user.IsReset = !user.IsReset || request.LoginType.Trim().ToLower().Equals("ad") ? false : true;

        await _unitOfWork.Repository<Domain.Entities.User>().AddAsync(user);

        request.UserInfoCommand.UserId = user.ReferenceId;

        var userInfo = _mapper.Map<Domain.Entities.UserInfo>(request.UserInfoCommand);

        await _unitOfWork.Repository<Domain.Entities.UserInfo>().AddAsync(userInfo);

        if (userInfo is null) throw new ArgumentException(nameof(userInfo));

        if (request.UserInfraObjectCommand != null)
        {
            request.UserInfraObjectCommand.UserId = user.ReferenceId;

            var userInfraObject = _mapper.Map<Domain.Entities.UserInfraObject>(request.UserInfraObjectCommand);

            if (userInfraObject is null) throw new ArgumentException(nameof(userInfraObject));

            await _unitOfWork.Repository<Domain.Entities.UserInfraObject>().AddAsync(userInfraObject);
        }
        // await transaction.CommitAsync(cancellationToken);

        var response = new CreateUserResponse
        {
            Message = Message.Create(nameof(Domain.Entities.User), user.LoginName),
            UserId = user.ReferenceId
        };

        await _publisher.Publish(new UserCreatedEvent { UserName = user.LoginName }, cancellationToken);

        await _publisher.Publish(
            new CreateSendEmailEvent
            {
                UserId = user.ReferenceId,
                IsPreferredMode = request.UserInfoCommand.IsPreferredMode,
                EncryptPassword = request.EncryptPassword,
                Email = request.UserInfoCommand.Email,
                LoginName = request.LoginName,
                UserName = request.UserInfoCommand.UserName,
                CompanyName = request.CompanyName,
                LoginType = request.LoginType
            }, cancellationToken);

        return response;
    }
}