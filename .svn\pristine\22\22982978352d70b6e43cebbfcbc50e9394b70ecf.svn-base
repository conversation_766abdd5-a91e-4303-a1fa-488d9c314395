﻿namespace ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetWorkflowActionByNodeId;

public class
    GetWorkflowActionByNodeIdQueryHandler : IRequestHandler<GetWorkflowActionByNodeIdQuery,
        List<GetWorkflowActionByNodeIdVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowActionRepository _workflowActionRepository;

    public GetWorkflowActionByNodeIdQueryHandler(IMapper mapper, IWorkflowActionRepository workflowActionRepository)
    {
        _mapper = mapper;
        _workflowActionRepository = workflowActionRepository;
    }

    public async Task<List<GetWorkflowActionByNodeIdVm>> Handle(GetWorkflowActionByNodeIdQuery request,
        CancellationToken cancellationToken)
    {
        var workflowActionList = await _workflowActionRepository.GetWorkflowActionDetailsByNodeId(request.Id);

        var workflowActionListDetailDto = _mapper.Map<List<GetWorkflowActionByNodeIdVm>>(workflowActionList);

        return workflowActionListDetailDto == null
            ? throw new NotFoundException(nameof(Domain.Entities.WorkflowAction), request.Id)
            : workflowActionListDetailDto;
    }
}