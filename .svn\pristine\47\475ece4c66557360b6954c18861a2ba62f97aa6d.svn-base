﻿using ContinuityPatrol.Application.Features.Database.Commands.Create;
using ContinuityPatrol.Application.Features.Database.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Database.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Database.Commands.TestConnection;
using ContinuityPatrol.Application.Features.Database.Commands.Update;
using ContinuityPatrol.Application.Features.Database.Commands.UpdateVersion;
using ContinuityPatrol.Application.Features.Database.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByType;
using ContinuityPatrol.Application.Features.Database.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAll;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using Newtonsoft.Json;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class DatabaseController : BaseController
{
    public static ILogger<DatabaseController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    private readonly IPublisher _publisher;
    public static string CompanyLogo { get; set; }

    public DatabaseController(ILogger<DatabaseController> logger, IPublisher publisher, IMapper mapper, IDataProvider dataProvider)
    {
        _logger = logger;
        _mapper = mapper;
        _publisher = publisher;
        _dataProvider = dataProvider;
    }

    [AntiXss]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in Database");
        await _publisher.Publish(new DatabasePaginatedEvent());
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(DatabaseViewModel databaseViewModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in Database");
        var databaseId = Request.Form["id"].ToString();
        try
        {
            databaseViewModel.Properties = databaseViewModel.Properties.IsNullOrWhiteSpace() ? databaseViewModel.Properties : SecurityHelper.Decrypt(databaseViewModel.Properties);
            if (databaseId.IsNullOrWhiteSpace())
            {
                var databaseCreateCommand = _mapper.Map<CreateDatabaseCommand>(databaseViewModel);
                _logger.LogDebug($"Creating Database '{databaseCreateCommand.Name}'");
                var response = await _dataProvider.Database.CreateAsync(databaseCreateCommand);
                _logger.LogDebug("Create operation completed successfully in Database, returning view.");
                return Json(new { success = true, data = response });
            }
            else
            {
                var databaseUpdateCommand = _mapper.Map<UpdateDatabaseCommand>(databaseViewModel);
                _logger.LogDebug($"Updating Database '{databaseViewModel.Name}'");
                var response = await _dataProvider.Database.UpdateAsync(databaseUpdateCommand);
                _logger.LogDebug("Update operation completed successfully in Database, returning view.");
                return Json(new { success = true, data = response });
            }
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on database page: {ex.ValidationErrors.FirstOrDefault()}");
            return ex.GetJsonException();
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on database page while processing the request for create or update.", ex);
            return ex.GetJsonException();
        }
    }

    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in Database");
        try
        {
            var response = await _dataProvider.Database.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted record in database");
            return Json(new { success = true, data = response });
        }       
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on database.", ex);
            return ex.GetJsonException();
        }
    }

    public async Task<List<GetDatabaseByTypeVm>> GetDataBaseList(string type)
    {
        _logger.LogDebug("Entering GetDataBaseList method in Database");
        try
        {
            var databaseByType = await _dataProvider.Database.GetByType(type);
            _logger.LogDebug($"Successfully retrieved database list by type:{type} in database page");
            return databaseByType;
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on database page while retrieving the database list by type.", ex);
            return new List<GetDatabaseByTypeVm>();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetDatabaseNamesForSaveAs()
    {
        _logger.LogDebug("Entering GetDatabaseNamesForSaveAs method in Database");
        try
        {
            var databaseList = await _dataProvider.Database.GetDatabaseNames();
            _logger.LogDebug("Successfully retrieved database name lists on database page");
            return Json(new { success = true, data = databaseList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on database page while processing the database names request.", ex);
            return ex.GetJsonException();
        }
    }

    //Save All
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> SaveAllDatabase(SaveAllDatabaseCommand command)
    {
        _logger.LogDebug("Entering SaveAllDatabase method in Database");

        try
        {
            var result = await _dataProvider.Database.SaveAllDatabase(command);
            _logger.LogDebug($"Successfully inserted databases");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on Database page while inserting databases", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> SaveAsDatabase(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
        _logger.LogDebug("Entering SaveAsDatabase method in Database");

        try
        {
            var result = await _dataProvider.Database.SaveAsDatabase(saveAsDatabaseCommand);
            _logger.LogDebug($"Successfully inserted database");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on database page while inserting database", ex);
            return ex.GetJsonException();
        }
    }

    public async Task<JsonResult> GetDatabaseNames(string type)
    {
        _logger.LogDebug("Entering GetDatabaseNames method in Database");
        try
        {
            var databaseNames = await _dataProvider.Database.GetByDatabaseType(type);
            _logger.LogDebug("Successfully retrieved database names in Database");
            return Json(new { success = true, data = databaseNames });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on database page while retrieving database names", ex);
            return ex.GetJsonException();
        }
    }

    //public async Task<JsonResult> GetdbNames()
    //{
    //    var databaseNames = await _dataProvider.Database.GetDatabaseNames();
    //    return Json(databaseNames);
    //}

    [HttpGet]
    public async Task<bool> IsDatabaseNameExist(string databaseName, string id)
    {
        _logger.LogDebug("Entering IsDatabaseNameExist method in Database");
        try
        {
            _logger.LogDebug("Returning result for IsDatabaseNameExist on database");
            return await _dataProvider.Database.IsDatabaseNameExist(databaseName, id);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on database while checking if database name exists for : {databaseName}.", ex);
            return false;
        }
    }

    [HttpPost]
    [AntiXss]
    [ValidateAntiForgeryToken]
    public async Task<JsonResult> UpdateDatabaseFormVersion(UpdateDatabaseVersionCommand updateDatabaseVersionCommand)
    {
        _logger.LogDebug("Entering UpdateDatabaseFormVersion method in Database");
        try
        {
            var updateDatabaseFormVersion = await _dataProvider.Database.UpdateDatabaseFormVersion(updateDatabaseVersionCommand);
            _logger.LogDebug("Updating database form version in database page");
            return Json(new { success = true, data = updateDatabaseFormVersion });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on database page while updating database form version", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetPagination(GetDatabasePaginatedListQuery databaseQuery)
    {
        _logger.LogDebug("Entering GetPagination method in Database");
        try
        {
            var databaseList = await _dataProvider.Database.GetDatabasePaginatedList(databaseQuery);
            _logger.LogDebug("Successfully retrieved database paginated list on database page");
            return Json(new { success = true, data = databaseList });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on database page while processing the pagination request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [AntiXss]
    [ValidateAntiForgeryToken]
    public async Task<JsonResult> DatabaseTestConnection(DatabaseTestConnectionCommand command)
    {
        _logger.LogDebug("Entering DatabaseTestConnection method in Database");
        try
        {
            var databaseTestConnection = await _dataProvider.Database.DatabaseTestConnection(command);
            _logger.LogDebug($"Successfully tested connection for database : {command.Id}");
            if (databaseTestConnection.Success)
            {
                return Json(new { success = true, data = databaseTestConnection });
            }
            else
            {
                //if license expired
                return Json(new { success = false, message = databaseTestConnection.Message });
            }           
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on database page while processing the test connection request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetByReferenceId(string id)
    {
        _logger.LogDebug("Entering GetByReferenceId method in Database");
        try
        {
            var getByReferenceId = await _dataProvider.Database.GetByReferenceId(id);
            _logger.LogDebug($"Successfully retrieved database detail by id : {getByReferenceId.Name}");
            return Json(new { success = true, data = getByReferenceId });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on database page while retrieving the database detail by id. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }
    [HttpGet]
    [AllowAnonymous]
    [SupportedOSPlatform("windows")]
    public async Task<IActionResult> LoadReport(string type, string selectedTypeId, string searchString)
    {
        var reportsDirectory = "";
        try
        {
            CompanyLogo = string.Empty;
            var companyId = WebHelper.UserSession.CompanyId;
            var companyDetails = await _dataProvider.Company.GetCompanyById(WebHelper.UserSession.CompanyId);
            if (!string.IsNullOrEmpty(companyDetails.CompanyLogo) && companyDetails.CompanyLogo != "NA") { CompanyLogo = companyDetails.CompanyLogo.ToString(); }
            GetDatabasePaginatedListQuery getDatabasePaginatedListQuery = new GetDatabasePaginatedListQuery();
            getDatabasePaginatedListQuery.DatabaseTypeId = selectedTypeId;
            getDatabasePaginatedListQuery.SearchString = searchString;
            var dbList = await _dataProvider.Database.GetDatabasePaginatedList(getDatabasePaginatedListQuery);
            // var dbList = await _dataProvider.Database.GetDatabaseList();
            var reportValue = JsonConvert.SerializeObject(dbList.Data);
            XtraReport report = new Report.ReportTemplate.DatabaseComponentReport(reportValue);
            var filenameSuffix = DateTime.Now.ToString("MMddyyyyhhmmsstt");
            if (type.Equals("pdf"))
            {
                var fileName = "DatabaseComponentReport_" + filenameSuffix + ".pdf";
                reportsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName);
                await report.ExportToPdfAsync(reportsDirectory);
                byte[] fileBytes = System.IO.File.ReadAllBytes(reportsDirectory);
                return File(fileBytes, "application/pdf", fileName);
            }
            else
            {
                var fileName = "DatabaseComponentReport_" + filenameSuffix + ".xls";
                reportsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName);
                await report.ExportToXlsAsync(reportsDirectory);
                byte[] fileBytes = System.IO.File.ReadAllBytes(reportsDirectory);
                return File(fileBytes, "application/vnd.ms-excel", fileName);
            }

        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred: {ex.Message}");
            return Content("An error occurred while generating the report.");
        }
        finally
        {
            if (System.IO.File.Exists(reportsDirectory))
            {
                System.IO.File.Delete(reportsDirectory);
            }
        }
    }
}
