QUnit.module("Company.js Integration & Validation Tests", hooks => {
    let $fixture;


    hooks.beforeEach(() => {
        $fixture = $("#qunit-fixture");
        $fixture.empty();
    });

    QUnit.test("QUnit is running", function (assert) {
        assert.ok(true, "QUnit test framework is working.");
    });

    QUnit.test("company.js loads and does not throw", function (assert) {
        assert.ok(true, "company.js script loaded (if this test runs, script did not throw on load).");
    });

    QUnit.test("companyTable DataTable is initialized if present", function (assert) {
        $fixture.append('<table id="companyTable"></table>');
        assert.ok($("#companyTable").length, "companyTable exists in DOM.");
    });

    QUnit.test("companyCreateBtn click shows modal (if Bootstrap/jQuery modal is present)", function (assert) {
        $fixture.append('<button id="companyCreateBtn"></button><div id="companyCreateModal"></div>');
        $("#companyCreateBtn").trigger("click");
        assert.ok($("#companyCreateModal").length, "companyCreateModal exists in DOM.");
    });

    QUnit.test("Internal validation functions are not accessible", function (assert) {
        assert.strictEqual(typeof window.validateName, "undefined", "validateName is not globally accessible.");
        assert.strictEqual(typeof window.validateDisplayName, "undefined", "validateDisplayName is not globally accessible.");
        assert.strictEqual(typeof window.validateWebAddress, "undefined", "validateWebAddress is not globally accessible.");
        assert.strictEqual(typeof window.companyFileFormatValidate, "undefined", "companyFileFormatValidate is not globally accessible.");
    });

    QUnit.test("populateCompanyFields sets input fields correctly", function (assert) {
        const sampleData = {
            id: 1, name: "Test Co", displayName: "Test Company", webAddress: "www.test.com",
            logoName: "logo.png", isParent: false, parentId: 0, parentName: "Parent Co"
        };

        $fixture.append(`
            <input id="companyName" />
            <input id="companyDisplayName" />
            <input id="companyWebAddress" />
            <input id="companyLogoFile" />
            <input id="parentCompanyName" />
            <span id="companyNameError"></span>
            <span id="companyDisplayNameError"></span>
            <span id="companyWebAddressError"></span>
            <span id="companyLogoError"></span>
            <button id="btnRemoveCompanyLogo"></button>
        `);

        window.populateCompanyFields(sampleData);

        assert.strictEqual($("#companyName").val(), "Test Co", "Company name populated");
        assert.strictEqual($("#companyDisplayName").val(), "Test Company", "Display name populated");
        assert.strictEqual($("#companyWebAddress").val(), "www.test.com", "Web address populated");
    });

    QUnit.test("Shows validation error when company name is empty and save is clicked", function (assert) {
        $fixture.append(`
        <input id="companyName" value="" />
        <span id="companyNameError"></span>
        <button id="companySaveBtn"></button>
    `);
        $("#companySaveBtn").trigger("click");
        assert.ok(
            $("#companyNameError").text().toLowerCase().includes("enter company name"),
            "Validation error message shown for empty company name"
        );
    });

    QUnit.test("Typing in search input triggers debounce and ajax reload", function (assert) {
        const done = assert.async();
        $fixture.append('<input id="companySearchInp" />');

        let called = false;

        window.commonDebounce = fn => {
            return function () {
                called = true;
                fn();
            };
        };

        $("#companySearchInp").val("Test").trigger("input");

        setTimeout(() => {
            assert.ok(called, "Debounced search function was called.");
            done();
        }, 600);
    });

    QUnit.test("FileValidation returns false for wrong file extension", async function (assert) {
        const done = assert.async();

        const fileInput = $('<input type="file" id="companyLogoFile" />')[0];
        const errorSpan = $('<span id="companyLogoError"></span>');
        $("#qunit-fixture").append(fileInput).append(errorSpan);
        const invalidFile = new File(["dummy content"], "test.jpg", { type: "image/jpeg" });
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(invalidFile);
        fileInput.files = dataTransfer.files;
        const result = await window.FileValidation(fileInput);
        assert.strictEqual(result, false, "File validation should fail for non-png file");
        assert.ok($("#companyLogoError").text().includes("Only png"), "Correct error message displayed");
        done();
    });
    QUnit.test("Check if FileValidation is exposed", function (assert) {
        assert.ok(typeof window.FileValidation === "function", "FileValidation is correctly exposed to test.");
    });

    QUnit.test("clearCompanyFormFields clears all fields and errors", function (assert) {
        $fixture.append(`
            <input id="companyName" value="SomeName" />
            <input id="companyDisplayName" value="DisplayName" />
            <input id="companyWebAddress" value="web.com" />
            <input id="companyLogoFile" value="somefile.png" />
            <input id="parentCompanyName" />
            <span id="companyNameError" class="field-validation-error">Error</span>
            <span id="companyDisplayNameError" class="field-validation-error">Error</span>
            <span id="companyWebAddressError" class="field-validation-error">Error</span>
            <span id="companyLogoError" class="field-validation-error">Error</span>
            <button id="btnRemoveCompanyLogo" style="display:inline;"></button>
        `);
        window.clearCompanyFormFields();

        assert.strictEqual($("#companyName").val(), "", "Company name field cleared");
        assert.strictEqual($("#companyDisplayName").val(), "", "Display name field cleared");
        assert.strictEqual($("#companyWebAddress").val(), "", "Web address field cleared");
        assert.strictEqual($("#companyLogoFile").val(), "", "Logo input cleared");
        assert.strictEqual($("#btnRemoveCompanyLogo").css("display"), "none", "Remove logo button hidden");
        assert.strictEqual($("#companyNameError").text(), "", "Company name error cleared");
    });

    QUnit.test("Shows validation error when company name is empty and save is clicked", function (assert) {
        $fixture.append(`
            <input id="companyName" value="" />
            <span id="companyNameError"></span>
            <button id="companySaveBtn"></button>
        `);
        $("#companySaveBtn").trigger("click");
        assert.ok(
            $("#companyNameError").text().toLowerCase().includes("enter company name"),
            "Validation error message shown for empty company name"
        );
    });

    QUnit.test("Shows success message after save (simulate notification)", function (assert) {
        $fixture.append('<div class="notification-success" style="display:none"></div>');
        $(".notification-success").text("Company saved successfully!").show();
        assert.ok($(".notification-success").is(":visible"), "Success message is visible");
        assert.ok($(".notification-success").text().toLowerCase().includes("saved"), "Success message contains 'saved'");
    });
});
