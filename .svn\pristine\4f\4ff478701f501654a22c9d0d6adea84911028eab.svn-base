﻿namespace ContinuityPatrol.Domain.Entities;

public class ApprovalMatrix : AuditableEntity
{
    public string Name { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string Description { get; set; }
    [Column(TypeName = "NCLOB")] public string Properties { get; set; }
    // public string ActionType { get; set; }

    //public string WorkflowProfileIds { get; set; }
    //[Column(TypeName = "NCLOB")] public string WorkflowNames { get; set; }
    //[Column(TypeName = "NCLOB")] public string WorkflowIds { get; set; }
    //public string WorkflowProfileNames { get; set; }
    //public string TemplateName { get; set; }
    //public string Status { get; set; }
    //public DateTime StartDate { get; set; }
    //public DateTime EndDate { get; set; }
    //public string ApprovalFlag { get; set; }
    //public string RejectedFlag { get; set; }
    //public string ApprovedBy { get; set; }
}