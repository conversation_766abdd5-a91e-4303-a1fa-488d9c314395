﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.MSSQLDBMirroingStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Queries.GetPaginatedList;

public class GetMSSQLDbMonitorStatusPaginatedListQueryHandler : IRequestHandler<
    GetMSSQLDbMonitorStatusPaginatedListQuery, PaginatedResult<MSSQLDBMirroingStatuslistVM>>
{
    private readonly IMsSqlDbMirroringStatusRepository _dbmirroringMonitorStatusRepository;

    private readonly IMapper _mapper;

    public GetMSSQLDbMonitorStatusPaginatedListQueryHandler(IMapper mapper,
        IMsSqlDbMirroringStatusRepository dbmirroringMonitorStatusRepository)
    {
        _mapper = mapper;
        _dbmirroringMonitorStatusRepository = dbmirroringMonitorStatusRepository;
    }

    public async Task<PaginatedResult<MSSQLDBMirroingStatuslistVM>> Handle(
        GetMSSQLDbMonitorStatusPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _dbmirroringMonitorStatusRepository.GetPaginatedQuery();

        var productFilterSpec = new MsSqlDbMirroingMonitorStatusFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<MSSQLDBMirroingStatuslistVM>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}