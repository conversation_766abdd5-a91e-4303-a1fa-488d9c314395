using ContinuityPatrol.Services.Api.Impl.Admin;
using ContinuityPatrol.Services.Api.Impl.Alert;
using ContinuityPatrol.Services.Api.Impl.Configuration;
using ContinuityPatrol.Services.Api.Impl.Cyber;
using ContinuityPatrol.Services.Api.Impl.Dashboard;
using ContinuityPatrol.Services.Api.Impl.Drift;
using ContinuityPatrol.Services.Api.Impl.FiaBia;
using ContinuityPatrol.Services.Api.Impl.Manage;
using ContinuityPatrol.Services.Api.Impl.Orchestration;
using ContinuityPatrol.Services.Api.Impl.Report;
using ContinuityPatrol.Services.Api.Provider;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Identity;
using ContinuityPatrol.Shared.Services.Provider;

namespace ContinuityPatrol.Services.Api.Extension;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddSharedApiServices(this IServiceCollection services)
    {


        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));
        #region ApiServices
     services.AddTransient<IMenuBuilderService, MenuBuilderService>();


        services.AddTransient<IGlobalVariableService, GlobalVariableService>();
        services.AddTransient<ICyberJobWorkflowSchedulerService, CyberJobWorkflowSchedulerService>();
        services.AddTransient<IFiaCostService, FiaCostService>();
        services.AddTransient<ITwoStepAuthenticationService, TwoStepAuthenticationService>();
        services.AddTransient<ICGExecutionReportService, CGExecutionReportService>();

        services.AddTransient<IApprovalMatrixUsersService, ApprovalMatrixUsersService>();

        services.AddTransient<IComponentSaveAllService, ComponentSaveAllService>();
        services.AddTransient<IDataSyncJobService, DataSyncJobService>();
        services.AddTransient<IWorkflowDrCalenderService, WorkflowDrCalenderService>();

        services.AddTransient<IAdPasswordJobService, AdPasswordJobService>();

        services.AddTransient<IAdPasswordExpireService, AdPasswordExpireService>();

        services.AddTransient<IFiaIntervalService, FiaIntervalService>();

        services.AddTransient<IFiaImpactTypeService, FiaImpactTypeService>();

        services.AddTransient<IFiaImpactCategoryService, FiaImpactCategoryService>();

        services.AddTransient<ICyberAirGapService, CyberAirGapService>();

        services.AddTransient<ICyberSnapsService, CyberSnapsService>();

        services.AddTransient<ICyberComponentMappingService, CyberComponentMappingService>();
        services.AddTransient<ICyberAlertService, CyberAlertService>();

        services.AddTransient<ICyberAirGapLogService, CyberAirGapLogService>();

        services.AddTransient<ICyberAirGapStatusService, CyberAirGapStatusService>();

        services.AddTransient<IDriftEventService, DriftEventService>();

        services.AddTransient<ICyberJobManagementService, CyberJobManagementService>();

        services.AddTransient<ICyberComponentGroupService, CyberComponentGroupService>();

        services.AddTransient<ICyberComponentService, CyberComponentService>();



        services.AddTransient<IDriftResourceSummaryService, DriftResourceSummaryService>();

        services.AddTransient<IDriftManagementMonitorStatusService, DriftManagementMonitorStatusService>();

        services.AddTransient<IDriftJobService, DriftJobService>();

        services.AddTransient<IApprovalMatrixApprovalService, ApprovalMatrixApprovalService>();

        services.AddTransient<IApprovalMatrixRequestService, ApprovalMatrixRequestService>();

        services.AddTransient<IDriftProfileService, DriftProfileService>();

        services.AddTransient<IDriftImpactTypeMasterService, DriftImpactTypeMasterService>();

        services.AddTransient<IDriftCategoryMasterService, DriftCategoryMasterService>();

        services.AddTransient<IDriftParameterService, DriftParameterService>();

        services.AddTransient<IEmployeeService, EmployeeService>();

        services.AddTransient<IBulkImportServices, BulkImportService>();
        services.AddTransient<IBulkImportActionResultService, BulkImportActionResultService>();

        services.AddTransient<IBulkImportOperationGroupService, BulkImportOperationGroupService>();

        services.AddTransient<IBulkImportOperationService, BulkImportOperationService>();

        services.AddTransient<IDynamicDashboardWidgetService, DynamicDashboardWidgetService>();

        services.AddTransient<IDynamicDashboardMapService, DynamicDashboardMapService>();

        services.AddTransient<IDynamicSubDashboardService, DynamicSubDashboardService>();

        services.AddTransient<IDynamicDashboardService, DynamicDashboardService>();


        services.AddTransient<IFormHistoryService, FormHistoryService>();
        services.AddTransient<IRoboCopyJobService, RoboCopyJobService>();
        services.AddTransient<IRsyncJobService, RsyncJobService>();
        services.AddTransient<IVeritasClusterService, VeritasClusterService>();

        services.AddTransient<IHacmpClusterService, HacmpClusterService>();

        services.AddTransient<IFiaTemplateService, FiaTemplateService>();

        services.AddTransient<IBiaRulesService, BiaRulesService>();

        services.AddTransient<IBackUpLogService, BackUpLogService>();

        services.AddTransient<IInfraMasterService, InfraMasterService>();

        services.AddTransient<IBackUpService, BackUpService>();

        services.AddTransient<IArchiveService, ArchiveService>();

        services.AddTransient<IReplicationJobService, ReplicationJobService>();

        services.AddTransient<IPageWidgetService, PageWidgetService>();

        services.AddTransient<IRsyncOptionService, RsyncOptionService>();

        services.AddTransient<IRoboCopyService, RoboCopyService>();

        services.AddTransient<IDataSyncOptionsService, DataSyncOptionsService>();

        services.AddTransient<IImpactActivityService, ImpactActivityService>();

        services.AddTransient<IPageBuilderService, PageBuilderService>();

        services.AddTransient<ISiteLocationService, SiteLocationService>();



        services.AddTransient<IIncidentManagementSummaryService, IncidentManagementSummaryService>();


        services.AddTransient<IIncidentManagementService, IncidentManagementService>();

        services.AddTransient<IUserLoginService, UserLoginService>();


        services.AddTransient<IWorkflowActionFieldMasterService, WorkflowActionFieldMasterService>();

        services.AddTransient<ICompanyService, CompanyService>();
        services.AddTransient<ISolutionHistoryService, SolutionHistoryService>();
        services.AddTransient<IUserService, UserService>();
        services.AddTransient<ILoginService, LoginService>();
        services.AddTransient<IAccountService, AccountService>();
        services.AddTransient<IAccessManagerService, AccessManagerService>();
        services.AddTransient<ISiteService, SiteService>();
        services.AddTransient<ISiteTypeService, SiteTypeService>();
        services.AddTransient<IBusinessFunctionService, BusinessFunctionService>();
        services.AddTransient<IBusinessServiceService, BusinessServiceService>();
        services.AddTransient<IUserRoleService, UserRoleService>();
        services.AddTransient<IInfraObjectService, InfraObjectService>();
        services.AddTransient<ILoadBalancerService, LoadBalancerService>();
        services.AddTransient<IDataSetService, DataSetService>();
        services.AddTransient<ISettingService, SettingService>();
        services.AddTransient<ITableAccessService, TableAccessService>();
        services.AddTransient<IServerService, ServerService>();
        services.AddTransient<IServerTypeService, ServerTypeService>();
        services.AddTransient<IDatabaseService, DatabaseService>();
        services.AddTransient<IReplicationService, ReplicationService>();
        services.AddTransient<INodeService, NodeService>();
        services.AddTransient<IMonitorService, MonitorService>();
        services.AddTransient<ICredentialProfileService, CredentialProfileService>();
        services.AddTransient<IWorkflowInfraObjectService, WorkflowInfraObjectService>();
        services.AddTransient<IWorkflowProfileService, WorkflowProfileService>();
        services.AddTransient<IWorkflowProfileInfoService, WorkflowProfileInfoService>();
        services.AddTransient<ITemplateService, TemplateService>();
        services.AddTransient<IFormService, FormService>();
        services.AddTransient<IFormTypeService, FormTypeService>();
        services.AddTransient<IWorkflowCategoryService, WorkflowCategoryService>();
        services.AddTransient<IWorkflowActionTypeService, WorkflowActionTypeService>();
        services.AddTransient<IWorkflowActionService, WorkflowActionService>();
        services.AddTransient<IWorkflowHistoryService, WorkflowHistoryService>();
        services.AddTransient<ILicenseManagerService, LicenseManagerService>();
        services.AddTransient<IComponentTypeService, ComponentTypeService>();
        services.AddTransient<ISingleSignOnService, SingleSignOnService>();
        services.AddTransient<ISmtpConfigurationService, SmtpConfigurationService>();
        services.AddTransient<IUserActivityService, UserActivityService>();
        services.AddTransient<IAccessManagerService, AccessManagerService>();
        services.AddTransient<ICredentialProfileService, CredentialProfileService>();
        services.AddTransient<IAlertService, AlertService>();
        services.AddTransient<IAlertInformationService, AlertInformationService>();
        services.AddTransient<IAlertMasterService, AlertMasterService>();
        services.AddTransient<IWorkflowPermissionService, WorkflowPermissionService>();
        services.AddTransient<IUserGroupService, UserGroupService>();
        services.AddTransient<IMssqlAlwaysOnMonitorLogsService, MssqlAlwaysOnMonitorLogsService>();
        services.AddTransient<IOracleMonitorLogsService, OracleMonitorLogsService>();
        services.AddTransient<IWorkflowOperationGroupService, WorkflowOperationGroupService>();
        services.AddTransient<IMssqlAlwaysOnMonitorStatusService, MssqlAlwaysOnMonitorStatusService>();
        services.AddTransient<IAlertNotificationService, AlertNotificationService>();
        services.AddTransient<IMssqlMonitorLogsService, MssqlMonitorLogsService>();
        services.AddTransient<IOracleRACMonitorLogsService, OracleRACMonitorLogsService>();
        services.AddTransient<IMssqlMonitorStatusService, MssqlMonitorStatusService>();
        services.AddTransient<IMysqlMonitorLogsService, MysqlMonitorLogsService>();
        services.AddTransient<IOracleMonitorStatusService, OracleMonitorStatusService>();
        services.AddTransient<IOracleRACMonitorStatusService, OracleRACMonitorStatusService>();
        services.AddTransient<IPostgresMonitorLogsService, PostgresMonitorLogsService>();
        services.AddTransient<IPostgresMonitorStatusService, PostgresMonitorStatusService>();
        services.AddTransient<IMysqlMonitorStatusService, MysqlMonitorStatusService>();
        services.AddTransient<IPluginManagerService, PluginManagerService>();
        //services.AddTransient<IWorkflowOperationService, WorkflowOperationService>();
        services.AddTransient<IJobService, JobService>();
        services.AddTransient<IDrReadyService, DrReadyService>();
        services.AddTransient<IAlertReceiverService, AlertReceiverService>();

        services.AddTransient<IWorkflowService, WorkflowService>();
        services.AddTransient<IReplicationMasterService, ReplicationMasterService>();
        services.AddTransient<IDataSetColumnsService, DataSetColumnsService>();
        services.AddTransient<IInfraReplicationMappingService, InfraReplicationMappingService>();
        services.AddTransient<IAboutCpService, AboutCpService>();
        services.AddTransient<IFormMappingService, FormMappingService>();
        services.AddTransient<IMonitorServicesService, MonitorServicesService>();
        services.AddTransient<IWorkflowActionResultService, WorkflowActionResultService>();
        services.AddTransient<IRiskMitigationService, RiskMitigationService>();
        services.AddTransient<IUserInfraObjectService, UserInfraObjectService>();
        services.AddTransient<ISmsConfigurationService, SmsConfigurationService>();
        services.AddTransient<IBusinessServiceAvailabilityService, BusinessServiceAvailabilityService>();
        services.AddTransient<IBusinessServiceEvaluationService, BusinessServiceEvaluationService>();
        services.AddTransient<ILicenseInfoService, LicenseInfoService>();
        services.AddTransient<IBusinessServiceHealthStatusService, BusinessServiceHealthStatusService>();
        services.AddTransient<IHeatMapStatusService, HeatMapStatusService>();
        services.AddTransient<IDrReadyStatusService, DrReadyStatusService>();
        services.AddTransient<IInfraObjectInfoService, InfraObjectInfoService>();
        services.AddTransient<IInfraSummaryService, InfraSummaryService>();
        services.AddTransient<IImpactAvailabilityService, ImpactAvailabilityService>();
        services.AddTransient<IDashboardViewService, DashboardViewService>();
        services.AddTransient<IWorkflowExecutionTempService, WorkflowExecutionTempService>();
        services.AddTransient<IDashboardViewLogService, DashboardViewLogService>();
        services.AddTransient<IGroupPolicyService, GroupPolicyService>();
        //services.AddTransient<INodeConfigurationService, Impl.Admin.LoadBalancerService>();
        services.AddTransient<IDrCalendarService, DRCalendarService>();

        services.AddTransient<IRpoSlaDeviationReportService, RpoSlaDeviationReportService>();
        services.AddTransient<IReportService, ReportService>();
        services.AddTransient<IReportScheduleService, ReportScheduleService>();
        services.AddTransient<IDb2HaDrMonitorStatusService, Db2HaDrMonitorStatusService>();
        services.AddTransient<IMsSqlNativeLogShippingMonitorStatusService, MsSqlNativeLogShippingMonitorStatusService>();
        services.AddTransient<IMongoDbMonitorStatusService, MongoDbMonitorStatusService>();
        services.AddTransient<ISvcMsSqlMonitorStatusService, SvcMsSqlMonitorStatusService>();
        services.AddTransient<IWorkflowPredictionService, WorkflowPredictionService>();
        services.AddTransient<IDb2HaDrMonitorLogService, Db2HaDrMonitorLogService>();
        services.AddTransient<IGlobalSettingService, GlobalSettingService>();
        services.AddTransient<IServerSubTypeService, ServerSubTypeService>();
        services.AddTransient<IApprovalMatrixService, ApprovalMatrixService>();
        services.AddTransient<ITeamMasterService, TeamMasterService>();
        services.AddTransient<ITeamResourceService, TeamResourceService>();
        services.AddScoped<IDataProvider, ApiProvider>();
        services.AddTransient<IEscalationMatrixService, EscalationMatrixService>();
        services.AddTransient<IEscalationMatrixLevelService, EscalationMatrixLevelService>();
        services.AddTransient<IMSSQLDbMirroringMonitorStatusService, MssqlDBMirroringMonitorStatusService>();
        services.AddTransient<IMSSQLDbMirroringMonitorLogsService, MssqlDBMirroringMonitorLogService>();
        services.AddTransient<IPageSolutionMappingService, PageSolutionMappingService>();
        services.AddTransient<IReplicationJobService, ReplicationJobService>();
        services.AddTransient<IRpForVmMonitorStatusService, RpForVmMonitorStatusService>();
        services.AddTransient<IUserInfoService, UserInfoService>();
        services.AddTransient<IRpForVmCGMonitorLogsService, RpForVmCGMonitorLogsServices>();
        services.AddTransient<IRpForVmCGMonitorStatusService, RpForVmCGMonitorStatusService>();
        services.AddTransient<IFastCopyMonitorService, FastCopyMonitorService>();
        #endregion

        return services;
    }
}
