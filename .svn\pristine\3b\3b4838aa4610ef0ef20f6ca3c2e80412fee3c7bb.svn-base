using ContinuityPatrol.Application.Features.HacmpCluster.Events.Update;

namespace ContinuityPatrol.Application.Features.HacmpCluster.Commands.Update;

public class UpdateHacmpClusterCommandHandler : IRequestHandler<UpdateHacmpClusterCommand, UpdateHacmpClusterResponse>
{
    private readonly IHacmpClusterRepository _hacmpClusterRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateHacmpClusterCommandHandler(IMapper mapper, IHacmpClusterRepository hacmpClusterRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _hacmpClusterRepository = hacmpClusterRepository;
        _publisher = publisher;
    }

    public async Task<UpdateHacmpClusterResponse> Handle(UpdateHacmpClusterCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _hacmpClusterRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.HacmpCluster), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateHacmpClusterCommand), typeof(Domain.Entities.HacmpCluster));

        await _hacmpClusterRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateHacmpClusterResponse
        {
            Message = Message.Update("HACMP Cluster", eventToUpdate.Name),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new HacmpClusterUpdatedEvent { Name = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}