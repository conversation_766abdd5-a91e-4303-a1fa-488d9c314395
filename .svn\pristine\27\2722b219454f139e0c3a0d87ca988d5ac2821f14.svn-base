﻿namespace ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetType;

public class GetLoadBalancerTypeQueryHandler : IRequestHandler<GetLoadBalancerTypeQuery, List<LoadBalancerTypeVm>>
{
    private readonly IMapper _mapper;
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;

    public GetLoadBalancerTypeQueryHandler(IMapper mapper, ILoadBalancerRepository nodeConfigurationRepository)
    {
        _mapper = mapper;
        _nodeConfigurationRepository = nodeConfigurationRepository;
    }

    public async Task<List<LoadBalancerTypeVm>> Handle(GetLoadBalancerTypeQuery request,
        CancellationToken cancellationToken)
    {
        var groupPolicy = request.Type != null
            ? (await _nodeConfigurationRepository.GetLoadBalancerType(request.Type)).ToList()
            : await _nodeConfigurationRepository.ListAllAsync();

        return groupPolicy.Count <= 0
            ? new List<LoadBalancerTypeVm>()
            : _mapper.Map<List<LoadBalancerTypeVm>>(groupPolicy);
    }
}