using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftCategoryList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftOperationSummary;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftResourceSummary;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetByInfraObjectId;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetConflictList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetDriftTreeView;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetResourceStatus;
using ContinuityPatrol.Domain.ViewModels.DriftManagementMonitorStatusModel;
using ContinuityPatrol.Domain.ViewModels.DriftResourceSummaryModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DriftManagementMonitorStatusControllerTests : IClassFixture<DriftManagementMonitorStatusFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DriftManagementMonitorStatussController _controller;
    private readonly DriftManagementMonitorStatusFixture _driftManagementMonitorStatusFixture;

    public DriftManagementMonitorStatusControllerTests(DriftManagementMonitorStatusFixture driftManagementMonitorStatusFixture)
    {
        _driftManagementMonitorStatusFixture = driftManagementMonitorStatusFixture;

        var testBuilder = new ControllerTestBuilder<DriftManagementMonitorStatussController>();
        _controller = testBuilder.CreateController(
            _ => new DriftManagementMonitorStatussController(),
            out _mediatorMock);
    }

    #region GetDriftManagementMonitorStatuses Tests

    [Fact]
    public async Task GetDriftManagementMonitorStatuses_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedList = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftManagementMonitorStatusListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDriftManagementMonitorStatuss();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftManagementMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item =>
        {
            Assert.NotNull(item.InfraObjectName);
            Assert.NotNull(item.Id);
            Assert.NotNull(item.Status);
        });
    }

    [Fact]
    public async Task GetDriftManagementMonitorStatuses_WithEmptyResult_ReturnsOkWithEmptyList()
    {
        // Arrange
        var emptyList = new List<DriftManagementMonitorStatusListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftManagementMonitorStatusListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDriftManagementMonitorStatuss();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftManagementMonitorStatusListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDriftManagementMonitorStatuses_WithException_ThrowsException()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftManagementMonitorStatusListQuery>(), default))
            .ThrowsAsync(new Exception("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _controller.GetDriftManagementMonitorStatuss());
    }

    #endregion

    #region CreateDriftManagementMonitorStatus Tests

    [Fact]
    public async Task CreateDriftManagementMonitorStatus_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftManagementMonitorStatusFixture.CreateDriftManagementMonitorStatusCommand;
        var expectedResponse = _driftManagementMonitorStatusFixture.CreateDriftManagementMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftManagementMonitorStatus(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftManagementMonitorStatusResponse>(createdResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
       
    }

    [Fact]
    public async Task CreateDriftManagementMonitorStatus_WithNullCommand_HandlesGracefully()
    {
        // Arrange
        CreateDriftManagementMonitorStatusCommand nullCommand = null;

        var successResponse = new CreateDriftManagementMonitorStatusResponse
        {
            Success = true,
            Message = "DriftManagementMonitorStatus created successfully",
            Id = Guid.NewGuid().ToString()
        };

        _mediatorMock
            .Setup(m => m.Send(nullCommand, default))
            .ReturnsAsync(successResponse);

        // Act
        var result = await _controller.CreateDriftManagementMonitorStatus(nullCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftManagementMonitorStatusResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task CreateDriftManagementMonitorStatus_WithInvalidInfraObjectId_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftManagementMonitorStatusFixture.CreateDriftManagementMonitorStatusCommand;
        command.InfraObjectId = "invalid-guid"; // Invalid GUID

        var failureResponse = new CreateDriftManagementMonitorStatusResponse
        {
            Success = false,
            Message = "Invalid InfraObjectId format"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.CreateDriftManagementMonitorStatus(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftManagementMonitorStatusResponse>(createdResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("Invalid", returnedResponse.Message);
    }

    #endregion

    #region UpdateDriftManagementMonitorStatus Tests

    [Fact]
    public async Task UpdateDriftManagementMonitorStatus_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _driftManagementMonitorStatusFixture.UpdateDriftManagementMonitorStatusCommand;
        var expectedResponse = _driftManagementMonitorStatusFixture.UpdateDriftManagementMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftManagementMonitorStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftManagementMonitorStatusResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
        // Check if ID is a valid GUID (handle AutoFixture generated IDs that may have prefixes)
        var idToCheck = returnedResponse.Id.StartsWith("Id") ? returnedResponse.Id.Substring(2) : returnedResponse.Id;
        Assert.True(Guid.TryParse(idToCheck, out _), $"Expected valid GUID but got: {returnedResponse.Id}");
       
    }

    [Fact]
    public async Task UpdateDriftManagementMonitorStatus_WithNullCommand_HandlesGracefully()
    {
        // Arrange
        UpdateDriftManagementMonitorStatusCommand nullCommand = null;

        var successResponse = new UpdateDriftManagementMonitorStatusResponse
        {
            Success = true,
            Message = "DriftManagementMonitorStatus updated successfully",
            Id = Guid.NewGuid().ToString()
        };

        _mediatorMock
            .Setup(m => m.Send(nullCommand, default))
            .ReturnsAsync(successResponse);

        // Act
        var result = await _controller.UpdateDriftManagementMonitorStatus(nullCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftManagementMonitorStatusResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDriftManagementMonitorStatus_WithInvalidId_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftManagementMonitorStatusFixture.UpdateDriftManagementMonitorStatusCommand;
        command.Id = "invalid-guid";

        var failureResponse = new UpdateDriftManagementMonitorStatusResponse
        {
            Success = false,
            Message = "Invalid ID format"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.UpdateDriftManagementMonitorStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftManagementMonitorStatusResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("Invalid", returnedResponse.Message);
    }

    #endregion

    #region GetDriftManagementMonitorStatusById Tests

    [Fact]
    public async Task GetDriftManagementMonitorStatusById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusDetailVm;
        expectedDetail.Id = id;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftManagementMonitorStatusDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftManagementMonitorStatusById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftManagementMonitorStatusDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.Id);
        Assert.NotNull(returnedDetail.InfraObjectName);
        Assert.NotNull(returnedDetail.Status);
    }

    [Fact]
    public async Task GetDriftManagementMonitorStatusById_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDriftManagementMonitorStatusById(invalidId));
    }

    [Fact]
    public async Task GetDriftManagementMonitorStatusById_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetDriftManagementMonitorStatusById(nullId));
    }

    #endregion

    #region DeleteDriftManagementMonitorStatus Tests

    [Fact]
    public async Task DeleteDriftManagementMonitorStatus_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftManagementMonitorStatusFixture.DeleteDriftManagementMonitorStatusResponse;
        expectedResponse.IsActive=false;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDriftManagementMonitorStatusCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftManagementMonitorStatus(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftManagementMonitorStatusResponse>(okResult.Value);
        Assert.Equal(false, returnedResponse.IsActive);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Message);
    }

    [Fact]
    public async Task DeleteDriftManagementMonitorStatus_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDriftManagementMonitorStatus(invalidId));
    }

    [Fact]
    public async Task DeleteDriftManagementMonitorStatus_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteDriftManagementMonitorStatus(nullId));
    }

    #endregion

    #region GetPaginatedDriftManagementMonitorStatuses Tests

    [Fact]
    public async Task GetPaginatedDriftManagementMonitorStatuses_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _driftManagementMonitorStatusFixture.GetDriftManagementMonitorStatusPaginatedListQuery;
        var expectedResult = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftManagementMonitorStatuss(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftManagementMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.True(returnedResult.Succeeded);
        Assert.NotEmpty(returnedResult.Data);
    }

    [Fact]
    public async Task GetPaginatedDriftManagementMonitorStatuses_WithNullQuery_HandlesGracefully()
    {
        // Arrange
        GetDriftManagementMonitorStatusPaginatedListQuery nullQuery = null;

        var emptyResult = new PaginatedResult<DriftManagementMonitorStatusListVm>
        {
            Data = new List<DriftManagementMonitorStatusListVm>(),
            TotalCount = 0,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(nullQuery, default))
            .ReturnsAsync(emptyResult);

        // Act
        var result = await _controller.GetPaginatedDriftManagementMonitorStatuss(nullQuery);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftManagementMonitorStatusListVm>>(okResult.Value);
        Assert.True(returnedResult.Succeeded);
        Assert.Empty(returnedResult.Data);
    }

    [Fact]
    public async Task GetPaginatedDriftManagementMonitorStatuses_WithInvalidPageSize_ReturnsEmptyResult()
    {
        // Arrange
        var query = _driftManagementMonitorStatusFixture.GetDriftManagementMonitorStatusPaginatedListQuery;
        query.PageSize = 0; // Invalid page size

        var emptyResult = new PaginatedResult<DriftManagementMonitorStatusListVm>
        {
            Data = new List<DriftManagementMonitorStatusListVm>(),
            TotalCount = 0,
            Succeeded = false
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(emptyResult);

        // Act
        var result = await _controller.GetPaginatedDriftManagementMonitorStatuss(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftManagementMonitorStatusListVm>>(okResult.Value);
        Assert.False(returnedResult.Succeeded);
        Assert.Empty(returnedResult.Data);
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public void ClearDataCache_WithValidCall_ExecutesSuccessfully()
    {
        // Arrange & Act
        _controller.ClearDataCache();

        // Assert
        // No exception should be thrown and method should complete successfully
        Assert.True(true); // Test passes if no exception is thrown
    }

    #endregion

    #region Additional Comprehensive Test Cases

    [Fact]
    public async Task CreateDriftManagementMonitorStatus_WithCriticalStatus_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftManagementMonitorStatusFixture.CreateDriftManagementMonitorStatusCommand;
        command.Status = "Critical";
        command.Properties = @"{""alertLevel"": 10, ""severity"": ""high""}";
        var expectedResponse = _driftManagementMonitorStatusFixture.CreateDriftManagementMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftManagementMonitorStatusCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftManagementMonitorStatus(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftManagementMonitorStatusResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Critical", command.Status);
        Assert.Contains("alertLevel", command.Properties);
    }

    [Fact]
    public async Task CreateDriftManagementMonitorStatus_WithHealthyStatus_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftManagementMonitorStatusFixture.CreateDriftManagementMonitorStatusCommand;
        command.Status = "Healthy";
        command.Properties = @"{""alertLevel"": 1, ""lastCheckTime"": """ + DateTime.UtcNow.ToString() + @"""}";
        var expectedResponse = _driftManagementMonitorStatusFixture.CreateDriftManagementMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftManagementMonitorStatusCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftManagementMonitorStatus(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftManagementMonitorStatusResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Healthy", command.Status);
        Assert.Contains("alertLevel", command.Properties);
    }

    [Fact]
    public async Task UpdateDriftManagementMonitorStatus_WithStatusTransition_ReturnsOkResult()
    {
        // Arrange
        var command = _driftManagementMonitorStatusFixture.UpdateDriftManagementMonitorStatusCommand;
        command.Status = "Warning";
        command.Properties = @"{""previousStatus"": ""Healthy"", ""statusChangeTime"": """ + DateTime.UtcNow.ToString() + @"""}";
        var expectedResponse = _driftManagementMonitorStatusFixture.UpdateDriftManagementMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftManagementMonitorStatusCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftManagementMonitorStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftManagementMonitorStatusResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Warning", command.Status);
        Assert.Contains("previousStatus", command.Properties);
    }

    [Fact]
    public async Task CreateDriftManagementMonitorStatus_WithDetailedMetrics_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftManagementMonitorStatusFixture.CreateDriftManagementMonitorStatusCommand;
        command.Properties = "{\"cpu\": 85.5, \"memory\": 70.2, \"disk\": 45.8}";
        command.ConflictProperties = "{\"cpu_max\": 90, \"memory_max\": 80, \"disk_max\": 85}";
        var expectedResponse = _driftManagementMonitorStatusFixture.CreateDriftManagementMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftManagementMonitorStatusCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftManagementMonitorStatus(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftManagementMonitorStatusResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("cpu", command.Properties);
        Assert.Contains("cpu_max", command.ConflictProperties);
    }

    [Fact]
    public async Task GetPaginatedDriftManagementMonitorStatuss_WithStatusFilter_ReturnsOkResult()
    {
        // Arrange
        var query = _driftManagementMonitorStatusFixture.GetDriftManagementMonitorStatusPaginatedListQuery;
        query.SearchString = "Critical";
        var expectedResult = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftManagementMonitorStatusPaginatedListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftManagementMonitorStatuss(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftManagementMonitorStatusListVm>>(okResult.Value);
        Assert.True(returnedResult.Succeeded);
        Assert.Equal("Critical", query.SearchString);
    }

    [Fact]
    public async Task UpdateDriftManagementMonitorStatus_WithAlertEscalation_ReturnsOkResult()
    {
        // Arrange
        var command = _driftManagementMonitorStatusFixture.UpdateDriftManagementMonitorStatusCommand;
        command.Properties = @"{""alertLevel"": 9, ""escalationRequired"": true, ""escalationTime"": """ + DateTime.UtcNow.AddMinutes(15).ToString() + @"""}";
        var expectedResponse = _driftManagementMonitorStatusFixture.UpdateDriftManagementMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftManagementMonitorStatusCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftManagementMonitorStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftManagementMonitorStatusResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("alertLevel", command.Properties);
        Assert.Contains("escalationRequired", command.Properties);
    }

    [Fact]
    public async Task GetDriftManagementMonitorStatusById_WithHistoricalData_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusDetailVm;
        expectedDetail.Properties = @"{""statusHistory"": ""Healthy -> Warning -> Critical"", ""totalDowntime"": ""2.5 hours""}";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftManagementMonitorStatusDetailQuery>(), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftManagementMonitorStatusById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftManagementMonitorStatusDetailVm>(okResult.Value);
        Assert.Contains("Critical", returnedDetail.Properties);
        Assert.Contains("totalDowntime", returnedDetail.Properties);
    }

    [Fact]
    public async Task DeleteDriftManagementMonitorStatus_WithActiveAlerts_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftManagementMonitorStatusFixture.DeleteDriftManagementMonitorStatusResponse;
        expectedResponse.Message = "Monitor status and active alerts cleared successfully";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteDriftManagementMonitorStatusCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftManagementMonitorStatus(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftManagementMonitorStatusResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Message);
    }

    [Fact]
    public async Task GetDriftManagementMonitorStatuss_WithRecentStatusChanges_ReturnsOkResult()
    {
        // Arrange
        var recentList = new List<DriftManagementMonitorStatusListVm>
        {
            new DriftManagementMonitorStatusListVm
            {
                Id = Guid.NewGuid().ToString(),
                InfraObjectName = "Enterprise Critical Monitor",
                Status = "Critical",
                InfraObjectId = Guid.NewGuid().ToString()
            },
            new DriftManagementMonitorStatusListVm
            {
                Id = Guid.NewGuid().ToString(),
                InfraObjectName = "Enterprise Warning Monitor",
                Status = "Warning",
                InfraObjectId = Guid.NewGuid().ToString()
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftManagementMonitorStatusListQuery>(), default))
            .ReturnsAsync(recentList);

        // Act
        var result = await _controller.GetDriftManagementMonitorStatuss();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftManagementMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(2, returnedList.Count);
        Assert.All(returnedList, item => Assert.NotNull(item.InfraObjectId));
    }

    [Fact]
    public async Task CreateDriftManagementMonitorStatus_WithAutomatedResponse_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftManagementMonitorStatusFixture.CreateDriftManagementMonitorStatusCommand;
        command.CategoryProperties = @"{""automatedResponseEnabled"": true, ""responseActions"": ""restart_service,send_notification,escalate_to_admin""}";
        var expectedResponse = _driftManagementMonitorStatusFixture.CreateDriftManagementMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftManagementMonitorStatusCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftManagementMonitorStatus(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftManagementMonitorStatusResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("automatedResponseEnabled", command.CategoryProperties);
        Assert.Contains("restart_service", command.CategoryProperties);
    }

    #endregion

    #region GetDriftManagementStatusByInfraObjectId Tests

    [Fact]
    public async Task GetDriftManagementStatusByInfraObjectId_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var query = _driftManagementMonitorStatusFixture.GetByInfraObjectIdQuery;
        query.InfraObjectId = infraObjectId;
        var expectedResult = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetByInfraObjectIdQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftManagementStatusByInfraObjectId(infraObjectId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<DriftManagementMonitorStatusListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.True(returnedResult.Count > 0);
    }

    [Fact]
    public async Task GetDriftManagementStatusByInfraObjectId_WithSpecificInfraObject_ReturnsFilteredResults()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var expectedResult = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetByInfraObjectIdQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftManagementStatusByInfraObjectId(infraObjectId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<DriftManagementMonitorStatusListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.All(returnedResult, item => Assert.NotNull(item.InfraObjectId));
    }

    [Fact]
    public async Task GetDriftManagementStatusByInfraObjectId_WithEnterpriseInfraObject_ReturnsOkResult()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var expectedResult = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusListVm;
        expectedResult.ForEach(x => x.InfraObjectId = infraObjectId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetByInfraObjectIdQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftManagementStatusByInfraObjectId(infraObjectId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<DriftManagementMonitorStatusListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.All(returnedResult, item => Assert.Equal(infraObjectId, item.InfraObjectId));
    }

    #endregion

    #region GetDriftTreeList Tests

    [Fact]
    public async Task GetDriftTreeList_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.GetDriftTreeListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftTreeListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftTreeList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<GetDriftTreeListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.True(returnedResult.Count > 0);
        Assert.All(returnedResult, item => Assert.Contains("Enterprise", item.BusinessServiceName));
    }

    [Fact]
    public async Task GetDriftTreeList_WithHierarchicalData_ReturnsStructuredResult()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.GetDriftTreeListVm;
        expectedResult.ForEach(x => x.BusinessServiceName = "Enterprise Infrastructure Node");

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftTreeListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftTreeList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<GetDriftTreeListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.All(returnedResult, item => Assert.Contains("Infrastructure", item.BusinessServiceName));
    }

    [Fact]
    public async Task GetDriftTreeList_WithMultipleNodes_ReturnsCompleteTree()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.GetDriftTreeListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftTreeListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftTreeList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<GetDriftTreeListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.True(returnedResult.Count >= 1);
        Assert.All(returnedResult, item => Assert.NotNull(item.BusinessServiceId));
    }

    #endregion

    #region GetDriftOperationSummary Tests

    [Fact]
    public async Task GetDriftOperationSummary_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.DriftOperationSummaryVm;
        expectedResult.TotalBusinessServiceCount = 150;
        expectedResult.TotalConflictCount = 25;
        expectedResult.TotalNonConflictCount = 125;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DriftOperationSummaryQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftOperationSummary();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<DriftOperationSummaryVm>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.Equal(150, returnedResult.TotalBusinessServiceCount);
        Assert.Equal(25, returnedResult.TotalConflictCount);
        Assert.Equal(125, returnedResult.TotalNonConflictCount);
    }

    [Fact]
    public async Task GetDriftOperationSummary_WithHighVolumeOperations_ReturnsCorrectSummary()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.DriftOperationSummaryVm;
        expectedResult.TotalBusinessServiceCount = 1000;
        expectedResult.TotalConflictCount = 100;
        expectedResult.TotalNonConflictCount = 900;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DriftOperationSummaryQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftOperationSummary();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<DriftOperationSummaryVm>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.Equal(1000, returnedResult.TotalBusinessServiceCount);
        Assert.True(returnedResult.TotalNonConflictCount > returnedResult.TotalConflictCount);
    }

    [Fact]
    public async Task GetDriftOperationSummary_WithZeroOperations_ReturnsEmptySummary()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.DriftOperationSummaryVm;
        expectedResult.TotalBusinessServiceCount = 0;
        expectedResult.TotalConflictCount = 0;
        expectedResult.TotalNonConflictCount = 0;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DriftOperationSummaryQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftOperationSummary();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<DriftOperationSummaryVm>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.Equal(0, returnedResult.TotalBusinessServiceCount);
        Assert.Equal(0, returnedResult.TotalConflictCount);
    }

    #endregion

    #region GetDriftResourceSummary Tests

    [Fact]
    public async Task GetDriftResourceSummary_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.DriftResourceSummaryListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftResourceSummaryQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftResourceSummary();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<DriftResourceSummaryListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.True(returnedResult.Count > 0);
        Assert.All(returnedResult, item => Assert.NotNull(item.InfraObjectId));
    }

    [Fact]
    public async Task GetDriftResourceSummary_WithConflictData_ReturnsConflictSummary()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.DriftResourceSummaryListVm;
        expectedResult.ForEach(x =>
        {
            x.ConflictCount = 10;
            x.NonConflictCount = 40;
            x.EntityName = "Database";
        });

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftResourceSummaryQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftResourceSummary();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<DriftResourceSummaryListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.All(returnedResult, item =>
        {
            Assert.Equal(10, item.ConflictCount);
            Assert.Equal(40, item.NonConflictCount);
            Assert.Equal("Database", item.EntityName);
        });
    }

    [Fact]
    public async Task GetDriftResourceSummary_WithMultipleEntityTypes_ReturnsVariedSummary()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.DriftResourceSummaryListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftResourceSummaryQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftResourceSummary();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<DriftResourceSummaryListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.All(returnedResult, item => Assert.NotNull(item.InfraObjectId));
    }

    #endregion

    #region GetDriftCategory Tests

    [Fact]
    public async Task GetDriftCategory_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.DriftCategoryListVm;
        expectedResult.CountMismatch = 25;
        expectedResult.VersionMismatch = 15;
        expectedResult.ConfigMismatch = 10;
        expectedResult.PolicyMismatch = 8;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DriftCategoryListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftCategory();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<DriftCategoryListVm>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.Equal(25, returnedResult.CountMismatch);
        Assert.Equal(15, returnedResult.VersionMismatch);
        Assert.Equal(10, returnedResult.ConfigMismatch);
        Assert.Equal(8, returnedResult.PolicyMismatch);
    }

    [Fact]
    public async Task GetDriftCategory_WithHighConfigurationDrift_ReturnsCorrectCounts()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.DriftCategoryListVm;
        expectedResult.CountMismatch = 100;
        expectedResult.VersionMismatch = 25;
        expectedResult.ConfigMismatch = 15;
        expectedResult.PolicyMismatch = 10;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DriftCategoryListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftCategory();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<DriftCategoryListVm>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.True(returnedResult.CountMismatch > returnedResult.VersionMismatch);
        Assert.True(returnedResult.VersionMismatch > returnedResult.ConfigMismatch);
    }

    [Fact]
    public async Task GetDriftCategory_WithZeroDrifts_ReturnsEmptyCategories()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.DriftCategoryListVm;
        expectedResult.CountMismatch = 0;
        expectedResult.VersionMismatch = 0;
        expectedResult.ConfigMismatch = 0;
        expectedResult.PolicyMismatch = 0;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DriftCategoryListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftCategory();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<DriftCategoryListVm>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.Equal(0, returnedResult.CountMismatch);
        Assert.Equal(0, returnedResult.VersionMismatch);
        Assert.Equal(0, returnedResult.ConfigMismatch);
        Assert.Equal(0, returnedResult.PolicyMismatch);
    }

    #endregion

    #region GetConflictOverView Tests

    [Fact]
    public async Task GetConflictOverView_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.ConflictListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetConflictListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetConflictOverView();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<ConflictListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.True(returnedResult.Count > 0);
        Assert.All(returnedResult, item => Assert.NotNull(item.InfraObjectId));
    }

    [Fact]
    public async Task GetConflictOverView_WithHighSeverityConflicts_ReturnsFilteredResults()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.ConflictListVm;
        expectedResult.ForEach(x =>
        {
            x.Status = "High";
            x.InfraObjectName = "Enterprise Production Database";
        });

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetConflictListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetConflictOverView();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<ConflictListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.All(returnedResult, item =>
        {
            Assert.Equal("High", item.Status);
            Assert.Contains("Enterprise", item.InfraObjectName);
        });
    }

    [Fact]
    public async Task GetConflictOverView_WithMultipleConflictTypes_ReturnsVariedResults()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.ConflictListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetConflictListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetConflictOverView();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<ConflictListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.All(returnedResult, item => Assert.NotNull(item.Id));
    }

    #endregion

    #region GetDriftDashboardResourceStatus Tests

    [Fact]
    public async Task GetDriftDashboardResourceStatus_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.DriftDashboardResourceStatusVm;
        expectedResult.TotalResourceCount = 200;
        expectedResult.DriftEnabledCount = 150;
        expectedResult.ConflictedCount = 25;
        expectedResult.NonConflictedCount = 125;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DriftDashboardResourceStatusQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftDashboardResourceStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<DriftDashboardResourceStatusVm>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.Equal(200, returnedResult.TotalResourceCount);
        Assert.Equal(150, returnedResult.DriftEnabledCount);
        Assert.Equal(25, returnedResult.ConflictedCount);
        Assert.Equal(125, returnedResult.NonConflictedCount);
    }

    [Fact]
    public async Task GetDriftDashboardResourceStatus_WithHighConflictCount_ReturnsCorrectStatus()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.DriftDashboardResourceStatusVm;
        expectedResult.TotalResourceCount = 500;
        expectedResult.DriftEnabledCount = 400;
        expectedResult.ConflictedCount = 100;
        expectedResult.NonConflictedCount = 300;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DriftDashboardResourceStatusQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftDashboardResourceStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<DriftDashboardResourceStatusVm>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.True(returnedResult.NonConflictedCount > returnedResult.ConflictedCount);
        Assert.Equal(400, returnedResult.DriftEnabledCount);
    }

    [Fact]
    public async Task GetDriftDashboardResourceStatus_WithZeroResources_ReturnsEmptyStatus()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.DriftDashboardResourceStatusVm;
        expectedResult.TotalResourceCount = 0;
        expectedResult.DriftEnabledCount = 0;
        expectedResult.ConflictedCount = 0;
        expectedResult.NonConflictedCount = 0;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DriftDashboardResourceStatusQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftDashboardResourceStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<DriftDashboardResourceStatusVm>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.Equal(0, returnedResult.TotalResourceCount);
        Assert.Equal(0, returnedResult.DriftEnabledCount);
    }

    #endregion

    #region IsDriftManagementMonitorStatusNameExist Tests

    [Fact]
    public async Task IsDriftManagementMonitorStatusNameExist_WithExistingName_ReturnsTrue()
    {
        // Arrange
        var driftManagementMonitorStatusName = "Enterprise Drift Management Monitor Status";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftManagementMonitorStatusNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDriftManagementMonitorStatusNameExist(driftManagementMonitorStatusName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedValue);
    }

    [Fact]
    public async Task IsDriftManagementMonitorStatusNameExist_WithNonExistingName_ReturnsFalse()
    {
        // Arrange
        var driftManagementMonitorStatusName = "Non-Existing Drift Management Monitor Status";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftManagementMonitorStatusNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDriftManagementMonitorStatusNameExist(driftManagementMonitorStatusName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    [Fact]
    public async Task IsDriftManagementMonitorStatusNameExist_WithNullId_ReturnsCorrectResult()
    {
        // Arrange
        var driftManagementMonitorStatusName = "Enterprise Production Drift Monitor";
        string? id = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftManagementMonitorStatusNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDriftManagementMonitorStatusNameExist(driftManagementMonitorStatusName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    [Fact]
    public async Task IsDriftManagementMonitorStatusNameExist_WithSpecialCharactersInName_ReturnsCorrectResult()
    {
        // Arrange
        var driftManagementMonitorStatusName = "Enterprise-Drift_Monitor@2024!";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftManagementMonitorStatusNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDriftManagementMonitorStatusNameExist(driftManagementMonitorStatusName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedValue);
    }

    [Fact]
    public async Task IsDriftManagementMonitorStatusNameExist_WithLongName_ReturnsCorrectResult()
    {
        // Arrange
        var driftManagementMonitorStatusName = "Enterprise Drift Management Monitor Status for Critical Production Infrastructure Management System";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftManagementMonitorStatusNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDriftManagementMonitorStatusNameExist(driftManagementMonitorStatusName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    #endregion

    #region Additional Comprehensive Test Cases

    [Fact]
    public async Task CreateDriftManagementMonitorStatus_WithComplexProperties_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftManagementMonitorStatusFixture.CreateDriftManagementMonitorStatusCommand;
        command.Properties = "{\"servers\": [{\"name\": \"srv1\", \"cpu\": 85}, {\"name\": \"srv2\", \"cpu\": 92}]}";
        command.ConflictProperties = "{\"threshold\": {\"cpu\": 90, \"memory\": 80}}";
        var expectedResponse = _driftManagementMonitorStatusFixture.CreateDriftManagementMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftManagementMonitorStatusCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftManagementMonitorStatus(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftManagementMonitorStatusResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
        Assert.Contains("servers", command.Properties);
        Assert.Contains("threshold", command.ConflictProperties);
    }

    [Fact]
    public async Task UpdateDriftManagementMonitorStatus_WithStatusChange_ReturnsOkResult()
    {
        // Arrange
        var command = _driftManagementMonitorStatusFixture.UpdateDriftManagementMonitorStatusCommand;
        command.Status = "Critical";
        command.ConflictProperties = "{\"alert_level\": \"high\", \"notification\": true}";
        var expectedResponse = _driftManagementMonitorStatusFixture.UpdateDriftManagementMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftManagementMonitorStatusCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftManagementMonitorStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftManagementMonitorStatusResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Critical", command.Status);
        Assert.Contains("alert_level", command.ConflictProperties);
    }

    [Fact]
    public async Task GetDriftManagementMonitorStatusById_WithDetailedProperties_ReturnsCompleteData()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusDetailVm;
        expectedDetail.Id = id; // Set the ID to match the test parameter
        expectedDetail.Properties = "{\"monitoring\": {\"enabled\": true, \"interval\": 300}}";
        expectedDetail.ConflictProperties = "{\"conflicts\": [{\"type\": \"config\", \"severity\": \"medium\"}]}";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftManagementMonitorStatusDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftManagementMonitorStatusById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftManagementMonitorStatusDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.Id);
        Assert.Contains("monitoring", returnedDetail.Properties);
        Assert.Contains("conflicts", returnedDetail.ConflictProperties);
    }

    [Fact]
    public async Task GetPaginatedDriftManagementMonitorStatuss_WithFilteringAndSorting_ReturnsFilteredResults()
    {
        // Arrange
        var query = _driftManagementMonitorStatusFixture.GetDriftManagementMonitorStatusPaginatedListQuery;
        query.PageNumber = 2;
        query.PageSize = 10;
        query.SearchString = "Enterprise";
        var expectedResult = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusPaginatedResult;
        expectedResult.CurrentPage = 2;
        expectedResult.PageSize = 10;
        expectedResult.TotalCount = 25;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftManagementMonitorStatuss(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftManagementMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(2, returnedResult.CurrentPage);
        Assert.Equal(10, returnedResult.PageSize);
        Assert.Equal(25, returnedResult.TotalCount);
        Assert.True(returnedResult.Data.Count <= 10);
    }

    [Fact]
    public async Task GetDriftOperationSummary_WithPerformanceMetrics_ReturnsDetailedSummary()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.DriftOperationSummaryVm;
        expectedResult.TotalBusinessServiceCount = 500;
        expectedResult.TotalConflictCount = 75;
        expectedResult.TotalNonConflictCount = 425;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DriftOperationSummaryQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftOperationSummary();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<DriftOperationSummaryVm>(okResult.Value);
        Assert.Equal(500, returnedResult.TotalBusinessServiceCount);
        Assert.Equal(75, returnedResult.TotalConflictCount);
        Assert.Equal(425, returnedResult.TotalNonConflictCount);
        Assert.True(returnedResult.TotalNonConflictCount > returnedResult.TotalConflictCount);
    }

    [Fact]
    public async Task GetDriftTreeList_WithHierarchicalStructure_ReturnsTreeStructure()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.GetDriftTreeListVm;
        expectedResult.ForEach(x => x.BusinessServiceName = "Enterprise Business Service Hierarchy");

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftTreeListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftTreeList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<GetDriftTreeListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.All(returnedResult, item => Assert.Contains("Hierarchy", item.BusinessServiceName));
        Assert.All(returnedResult, item => Assert.NotNull(item.BusinessServiceId));
    }

    [Fact]
    public async Task GetConflictOverView_WithCriticalConflicts_ReturnsHighPriorityConflicts()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.ConflictListVm;
        expectedResult.ForEach(x =>
        {
            x.Status = "Critical";
            x.InfraObjectName = "Critical Enterprise Infrastructure";
        });

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetConflictListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetConflictOverView();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<ConflictListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.All(returnedResult, item => Assert.Equal("Critical", item.Status));
        Assert.All(returnedResult, item => Assert.Contains("Critical", item.InfraObjectName));
    }

    [Fact]
    public async Task GetDriftCategory_WithMultipleCategoryTypes_ReturnsComprehensiveCategorization()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.DriftCategoryListVm;
        expectedResult.CountMismatch = 50;
        expectedResult.VersionMismatch = 30;
        expectedResult.ConfigMismatch = 20;
        expectedResult.PolicyMismatch = 15;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DriftCategoryListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftCategory();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<DriftCategoryListVm>(okResult.Value);
        Assert.Equal(50, returnedResult.CountMismatch);
        Assert.Equal(30, returnedResult.VersionMismatch);
        Assert.Equal(20, returnedResult.ConfigMismatch);
        Assert.Equal(15, returnedResult.PolicyMismatch);
        Assert.True(returnedResult.CountMismatch > returnedResult.VersionMismatch);
    }

    [Fact]
    public async Task IsDriftManagementMonitorStatusNameExist_WithExistingNameDifferentId_ReturnsTrue()
    {
        // Arrange
        var existingName = "Enterprise Drift Monitor Status";
        var differentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftManagementMonitorStatusNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDriftManagementMonitorStatusNameExist(existingName, differentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedValue);
    }

    [Fact]
    public async Task GetDriftDashboardResourceStatus_WithComplexResourceMetrics_ReturnsDetailedMetrics()
    {
        // Arrange
        var expectedResult = _driftManagementMonitorStatusFixture.DriftDashboardResourceStatusVm;
        expectedResult.TotalResourceCount = 1000;
        expectedResult.DriftEnabledCount = 800;
        expectedResult.ConflictedCount = 120;
        expectedResult.NonConflictedCount = 680;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DriftDashboardResourceStatusQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDriftDashboardResourceStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<DriftDashboardResourceStatusVm>(okResult.Value);
        Assert.Equal(1000, returnedResult.TotalResourceCount);
        Assert.Equal(800, returnedResult.DriftEnabledCount);
        Assert.Equal(120, returnedResult.ConflictedCount);
        Assert.Equal(680, returnedResult.NonConflictedCount);

        // Verify resource distribution logic
        var enabledPercentage = (double)returnedResult.DriftEnabledCount / returnedResult.TotalResourceCount * 100;
        Assert.True(enabledPercentage == 80.0);
    }

    #endregion
}
