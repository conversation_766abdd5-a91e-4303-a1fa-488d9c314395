﻿
const siteURL = {
    SiteExistUrl : 'Configuration/Site/IsSiteNameExist',
    SitePaginatedUrl: "/Configuration/Site/GetPagination"

}

let createPermission = $("#configurationCreate").data("create-permission")?.toLowerCase();
let deletePermission = $("#configurationDelete").data("delete-permission")?.toLowerCase();

$(function () {
  
    if (createPermission == 'false') {
        $("#create").removeClass('#create').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }
    let selectedValues = [];

    let dataTable = $('#tblSite').DataTable(

        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }
                , infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": siteURL.SitePaginatedUrl,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "type" : sortIndex === 3 ? "companyName" :
                        sortIndex === 4 ? "location" : sortIndex === 5 ? "platformType" : sortIndex === 6 ? "status" : "";
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                   
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json.data.length === 0) {
                        $(".pagination-column").addClass("disabled")
                   

                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                       
                    }
                    return json?.data;
                }
            },
            "columnDefs": [
                {
                    "targets": [1,2,3,4],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "type", "name": "Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        let icon;
                        switch (data?.toLowerCase()) {
                            case "prsite":
                                icon = "cp-prsites";
                                break;
                            case "drsite":
                                icon = "cp-physical-drsite";
                                break;
                            case "neardrsite":
                                icon = "cp-physical-neardrsite";
                                break;                            
                            default:
                                icon = "cp-custom-server-4"
                                break;
                        }                 
                        if (icon) {   
                            if (type === 'display' && data) {
                                return `<span ><i class="${icon} me-1"></i>${data}</span>`;
                            }
                        }

                        if (type === 'display') {
                            return '<span >' + data + '</span>';
                        }
                        return data;
                    }
                },  
                {
                    "data": "companyName", "name": "Display Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span>' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "location", "name": "Location", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span>' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "platformType", "name": "Platform", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span>' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row)
                    {
                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class=" edit-button" data-site='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="delete-button" data-site-id="${row.id}" data-site-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                  
                        </div>`;
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class=" edit-button" data-site='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="icon-disabled">
                                                <i class="cp-Delete"></i>
                                            </span>
                                  
                        </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="delete-button" data-site-id="${row.id}" data-site-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                  
                        </div>`;
                        }
                        else {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="icon-disabled">
                                    <i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }

                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1; 
                $('td:eq(0)', row).html(counter); 
            },
            initComplete: function () {             
                $('.paginate_button.page-item.previous').attr('title', 'Previous');                
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });
   
    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const NameCheckbox = $("#Name");
        const TypeCheckbox = $("#Type");
        const CompanyNameCheckbox1 = $("#CompanyName");
        const LocationCheckbox1 = $("#Location");
        const PlatformTypeCheckbox1 = $("#PlatformType");
        const inputValue = $('#search-inp').val();
        if (NameCheckbox.is(':checked')) {
            selectedValues.push(NameCheckbox.val() + inputValue);
        }
        if (TypeCheckbox.is(':checked')) {
            selectedValues.push(TypeCheckbox.val() + inputValue);
        }
        if (CompanyNameCheckbox1.is(':checked')) {
            selectedValues.push(CompanyNameCheckbox1.val() + inputValue);
        }
        if (LocationCheckbox1.is(':checked')) {
            selectedValues.push(LocationCheckbox1.val() + inputValue);
        }
        if (PlatformTypeCheckbox1.is(':checked')) {
            selectedValues.push(PlatformTypeCheckbox1.val() + inputValue);
        }
        dataTable.ajax.reload(function (json) {

            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    },500))

    $(".form-select-modal").select2({
        tags: true,
        dropdownParent: $("#CreateModal"),
    });

    $('#tblSite').on('click', '.edit-button', function () {

        let siteData = $(this).data('site');
        populateModalFields(siteData);
        $('#SaveFunction').text('Update');
        $('#CreateModal').modal('show');
    });

    //Delete
    $('#tblSite').on('click', '.delete-button', function () {
        let siteId = $(this).data('site-id');
        let siteName = $(this).data('site-name');
        $('#deleteData').attr('title', siteName).text(siteName);
        $('#textDeleteId').val(siteId);
    });


    //Create   
    $("#ddlCompanyName").on('change', function () {
        let companyId = $("#ddlCompanyName option:selected").attr('id');
        $('#textCompanyId').val(companyId);
     
    });
  
    //Name Validation
    $('#textName').on('keyup', commonDebounce(async function () {
        let siteId = $('#textSiteId').val();
        const value = $(this).val();
        let name = await sanitizeInput($(this).val());
        $(this).val(name);

        await validateName(value, siteId, siteURL.SiteExistUrl);
    }, 400));

    $('input[name="type"]').on('click', async function () {
        
        let value = this.value;
        let siteTypeId = $(this).attr('data-typeName');
        
        $('input[asp-for="Type"]').val(value);   
        if (siteTypeId == '1738f813-6090-40cc-8869-25741a156f73') {

            $('#drsitefeild').hide();
            $('#DRSiteType').val('').trigger('change');
            $('#DRSiteType-error') .text('').removeClass('field-validation-error');

        } else {
            $('#drsitefeild').show();
            $('#DRSiteType').val('').trigger('change');
            $('#DRSiteType-error').text('').removeClass('field-validation-error');
        }
        let errorElementType = $('#type-error');
        await ValidateRadioButton(errorElementType);
    });

    $('#ddlPlatform').on('change', function () {
        let platformType = $(this).val();
        let prIcon = $('#icon1');
        let drIcon = $('#icon2');
        let nearDrIcon = $('#icon3');

        prIcon.removeClass();
        drIcon.removeClass();
        nearDrIcon.removeClass();

        switch (platformType) {
            case 'Physical':
                prIcon.addClass('cp-prsites fs-1');
                drIcon.addClass('cp-physical-drsite fs-1');
                nearDrIcon.addClass('cp-physical-neardrsite fs-1');
                break;
            case 'Virtual':
                prIcon.addClass('cp-virtual-prsite fs-1');
                drIcon.addClass('cp-virtual-drsite fs-1');
                nearDrIcon.addClass('cp-virtual-neardrsite fs-1');
                break;
            case 'HCL':
                prIcon.addClass('cp-hcl-prsite fs-1');
                drIcon.addClass('cp-hcl-drsite fs-1');
                nearDrIcon.addClass('cp-hcl-neardrsite fs-1');
                break;
            case 'Cloud':
                prIcon.addClass('cp-cloud-prsite fs-1');
                drIcon.addClass('cp-cloud-drsite fs-1');
                nearDrIcon.addClass('cp-cloud-neardrsite fs-1');
                break;
            default:
                resultText = 'You selected Option 1.';
                break;
        }
    });

    $("#ddlocation").on('change', function () {

        let locationlng = $("#ddlocation option:selected").attr('lng');
        let locationlat = $("#ddlocation option:selected").attr('lat');
        let locationId = $("#ddlocation option:selected").attr('id');
        $('#locationId').val(locationId);
        $('#txtlat').val(locationlat);
        $('#txtlng').val(locationlng);
    });

    $('#ddlocation').on('change', function () {
        const value = $(this).val();
        let errorElement = $('#Location-error');
        validateDropDown(value, 'Select Location', errorElement);
    });
    $('#ddlPlatform').on('change', function () {
        const value = $(this).val();
        let errorElement = $('#Platform-error');
        validateDropDown(value, 'Select platform type', errorElement);
    });

    $('#DRSiteType').on('change', function () {
        const value = $(this).val();
        let errorElement = $('#DRSiteType-error');
        validateDropDown(value, 'Select site type', errorElement);
    });

    $('#ddlCompanyName').on('change', function () {
        const value = $(this).val();
        let errorElement = $('#Company-error');
        validateDropDown(value, 'Select company name', errorElement);
    });


    $("#SaveFunction").on('click', async function () {
        let form = $("#CreateForm")
        let name = $("#textName").val();
        let location = $("#ddlocation").val();
        let platform = $("#ddlPlatform").val();
        let company = $("#ddlCompanyName").val();
        let siteId = $('#textSiteId').val();
        let DRSiteType = $('#DRSiteType').val();
        let errorElementplatform = $('#Platform-error');
        let errorElementLocation = $('#Location-error');
        let errorElementCompany = $('#Company-error');
        let errorElementType = $('#type-error');
        let errorDRSiteType = $('#DRSiteType-error');
        let IsType = await ValidateRadioButton(errorElementType);
        let IsName = await validateName(name, siteId, siteURL.SiteExistUrl);
        let IsDRSiteType = validateDropDown(DRSiteType, 'Select site type', errorDRSiteType);
        let IsPlatform = validateDropDown(platform, 'Select platform type', errorElementplatform);
        let IsCompany = validateDropDown(company, 'Select company name', errorElementCompany);
        let IsLocation = validateDropDown(location, 'Select location', errorElementLocation);
        let SiteSanitizeArray = ['textName', 'ddlocation', 'locationId', 'txtlng', 'txtlat', 'textSiteId', 'dataTemperature']
        sanitizeContainer(SiteSanitizeArray)
        setTimeout(() => {
            if (IsName && IsLocation && IsPlatform && IsCompany && IsType && ($("#drsitefeild").is(":visible") ? IsDRSiteType : true)) {
                form.trigger('submit');
            }
        }, 200)

    });

    document.getElementById('textName')?.addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            e.preventDefault();
        }
    });   
    $('.btn-check').on("click", async function (e) {       
        let errorElementType = $('#type-error');
        let typeName = $(this).data('typename');
        $('#typeId').val(typeName)
        await ValidateRadioButton(errorElementType);
         
    });

    async function validateName(value, id = null, Url) {
        const errorElement = $('#Name-error');
    
        if (!value) {
            errorElement.text('Enter site name').addClass('field-validation-error');
            return false;
        }
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }
        const validationResults = [
            await SpecialCharValidate(value),
            await ShouldNotBeginWithSpace(value),
            await ShouldNotBeginWithUnderScore(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
            await ShouldNotEndWithSpace(value),
            await ShouldNotAllowMultipleSpace(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsSiteNameExist(value, id, Url)
        ];

        return await CommonValidation(errorElement, validationResults);
    }


    const IsSiteNameExist = (value, id, url) => {
        if (!value.trim()) {
            return true;
        }
        return new Promise((resolve) => {
            $.ajax({
                type: "GET",
                url: RootUrl + url,

                data: {
                    'siteName': value,
                    'id': id
                },
                async: true,
                success: function (result) {
                    resolve(result ? "Name already exists" : true);
                }
            });
        });
    }

    function validateDropDown(value, errorMessage, errorElement) {
        if (!value) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }

    async function ValidateRadioButton(errorElement) {
        if ($("[name='type']:checked").length == 0) {
            errorElement.text("Select type").addClass('field-validation-error');;
            return false;
        }
        else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }  

    function populateModalFields(siteData) {

        $('#textSiteId').val(siteData.id);
        $('#textName').val(siteData.name);
        $('#ddlocation').val(siteData.location);
        $('#txtlat').val(siteData.lat);
        $('#txtlng').val(siteData.lng);
        $('#locationId').val(siteData.locationId);
        $('#DRSiteType').val(siteData.dataTemperature);
        $('#ddlCompanyName').val(siteData.companyName);
        $('#typeId').val(siteData.typeId)
        if (siteData.typeId == '1738f813-6090-40cc-8869-25741a156f73') {
            $('#drsitefeild').hide();
            $('#DRSiteType').val('');

        } else {
            $('#drsitefeild').show();
        }
        $('#textCompanyId').val(siteData.companyId);
        $('#ddlPlatform').val(siteData.platformType);
        $('.btn-check[value="' + siteData.type + '"]').prop('checked', true);
        let prIcon = $('#icon1');
        let drIcon = $('#icon2');
        let nearDrIcon = $('#icon3');
        prIcon.removeClass();
        drIcon.removeClass();
        nearDrIcon.removeClass();

        switch (siteData.platformType) {
            case 'Physical':
                prIcon.addClass('cp-prsites fs-1');
                drIcon.addClass('cp-physical-drsite fs-1');
                nearDrIcon.addClass('cp-physical-neardrsite fs-1');
                break;
            case 'Virtual':
                prIcon.addClass('cp-virtual-prsite fs-1');
                drIcon.addClass('cp-virtual-drsite fs-1');
                nearDrIcon.addClass('cp-virtual-neardrsite fs-1');
                break;
            case 'HCL':
                prIcon.addClass('cp-hcl-prsite fs-1');
                drIcon.addClass('cp-hcl-drsite fs-1');
                nearDrIcon.addClass('cp-hcl-neardrsite fs-1');
                break;
            case 'Cloud':
                prIcon.addClass('cp-cloud-prsite fs-1');
                drIcon.addClass('cp-cloud-drsite fs-1');
                nearDrIcon.addClass('cp-cloud-neardrsite fs-1');
                break;
            default:
                break;
        }
    }


    const errorElements = ['#Name-error', '#Platform-error', '#Location-error', '#Company-error', '#type-error','#DRSiteType-error'];
    $('#create').on('click', function () {     
        clearInputFields('CreateForm', errorElements);
        $('#drsitefeild').hide();
        $('#ddlocation option:first').prop('selected', 'selected');
        $('#ddlCompanyName option:first').prop('selected', 'selected');
        $('#ddlPlatform option:first').prop('selected', 'selected');
        $('#DRSiteType option:first').prop('selected', 'selected');
        let prIcon = $('#icon1');
        let drIcon = $('#icon2');
        let nearDrIcon = $('#icon3');
        prIcon.removeClass();
        drIcon.removeClass();
        nearDrIcon.removeClass(); 
        prIcon.addClass('cp-prsites fs-1');
        drIcon.addClass('cp-physical-drsite fs-1');
        nearDrIcon.addClass('cp-physical-neardrsite fs-1');                
        $('#SaveFunction').text('Save');
    });

    $('#tblSite').on('click', '.edit-button', function () {
        
        const errorElements = ['#Name-error', '#Platform-error', '#Location-error', '#Company-error', '#type-error','#DRSiteType-error'];
        $('#SaveFunction').text('Update');
        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });
    });
});


