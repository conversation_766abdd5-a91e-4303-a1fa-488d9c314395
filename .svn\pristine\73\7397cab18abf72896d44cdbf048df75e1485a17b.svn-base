﻿using ContinuityPatrol.Web.Areas.Report.Controllers;
using DevExpress.XtraCharts;
using Newtonsoft.Json;
using System.Data;
using System.Drawing;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate;

[SupportedOSPlatform("windows")]
public partial class BusinessServiceSummaryXlsReport : DevExpress.XtraReports.UI.XtraReport
{
    HttpContext httpContext { get; set; }

    public  Application.Features.Report.Queries.BusinessServiceSummaryReport.BusinessServiceSummaryReport businessSummaryReportXls = new Application.Features.Report.Queries.BusinessServiceSummaryReport.BusinessServiceSummaryReport();

    public string username;
    private readonly ILogger<PreBuildReportController> _logger;
    public BusinessServiceSummaryXlsReport(string data)
    {
        try
        {
            _logger = PreBuildReportController._logger;
            businessSummaryReportXls = JsonConvert.DeserializeObject<Application.Features.Report.Queries.BusinessServiceSummaryReport.BusinessServiceSummaryReport>(data);
            var report = businessSummaryReportXls.BusinessServiceSummaryReportVms;
            InitializeComponent();
            ClientCompanyLogo();
            this.DataSource = report;
            tableCell8.BeforePrint += tableCell_SerialNumber_BeforePrint;
            Int64 valueup = 0;
            Int64 valuedown = 0;
            Int64 valueMain = 0;
            this.DisplayName = "OperationalServiceSummaryReport_" + DateTime.Now.ToString("ddMMyyyy" + "_" + "hhmmsstt");


            foreach (var row in report)
            {
                xrLabel51.Text = report.Count.ToString();
                valueup = valueup + row.Up;
                valuedown = valuedown + row.Down;
                valueMain = valueMain + row.Maintenance;
            }


            xrLabel5.Text = report.Count.ToString();
            xrLabel9.Text = valueMain.ToString();
            xrLabel8.Text = valueup.ToString();
            xrLabel6.Text = valuedown.ToString();
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the operationalservice summary excel Report. The error message : " + ex.Message); throw; }
    }

    private void xrPageInfo1_BeforePrint(object sender, CancelEventArgs e)
    {


    }
    private int serialNumber = 1;

    private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
    {
        XRTableCell cell = (XRTableCell)sender;

        cell.Text = serialNumber.ToString();
        serialNumber++;
    }

    private void _userName_BeforePrint(object sender, CancelEventArgs e)
    {
        try
        {
            _username.Text = "Report Generated By: " + businessSummaryReportXls.ReportGeneratedBy.ToString();
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the operationalservice summary excel Report's User name. The error message : " + ex.Message); throw; }
    }
    private DataTable CreateChartData(Int64 rowup, Int64 rowdown, Int64 rowmain, Int64 NodataFound)
    {
        // Create an empty table.
        DataTable table = new DataTable("Table1");

        // Add two columns to the table.
        table.Columns.Add("Argument", typeof(string));
        table.Columns.Add("Value", typeof(Int64));

        // Add data rows to the table.
        Random rnd = new Random();

        table.Rows.Add("Health Up", rowup);
        table.Rows.Add("Health Down", rowdown);
        table.Rows.Add("Maintenance", rowmain);
        table.Rows.Add("No Data Found", NodataFound);


        return table;
    }

    private void xrChart2_BeforePrint(object sender, CancelEventArgs e)
    {
        try
        {
            var Reporter = businessSummaryReportXls.BusinessServiceSummaryReportVms;
            Int64 valueup = 0;
            Int64 valuedown = 0;
            Int64 valueMain = 0;
            Int64 NodataFound = 0;

            foreach (var row in Reporter)
            {
                valueup = valueup + row.Up;
                valuedown = valuedown + row.Down;
                valueMain = valueMain + row.Maintenance;
            }


            if (valueup == 0 && valuedown == 0 && valueMain == 0)
            {
                Series series1 = new Series("Series1", ViewType.Doughnut);
                xrChart2.Series.Add(series1);
                NodataFound = 1;
                series1.DataSource = CreateChartData(valueup, valuedown, valueMain, NodataFound);
                DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                doughnutSeriesView.MinAllowedSizePercentage = 75D;
                series1.View = doughnutSeriesView;
                series1.ArgumentScaleType = ScaleType.Auto;
                series1.ArgumentDataMember = "Argument";
                series1.ValueScaleType = ScaleType.Numerical;
                series1.ValueDataMembers.AddRange(new string[] { "Value" });
                series1.Label.TextPattern = "{A}";
                series1.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                xrChart2.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            }
            else
            {
                Series series = new Series("Series1", ViewType.Doughnut);
                xrChart2.Series.Add(series);

                series.DataSource = CreateChartData(valueup, valuedown, valueMain, NodataFound);
                DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                doughnutSeriesView.MinAllowedSizePercentage = 75D;
                series.View = doughnutSeriesView;
                series.ArgumentScaleType = ScaleType.Auto;
                series.ArgumentDataMember = "Argument";
                series.ValueScaleType = ScaleType.Numerical;
                series.ValueDataMembers.AddRange(new string[] { "Value" });
                series.Label.TextPattern = "{A}\n{V}";
                ((DoughnutSeriesLabel)series.Label).ResolveOverlappingMode = ResolveOverlappingMode.Default;
                series.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                xrChart2.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            }
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the operationalservice summary excel Report's chart data. The error message : " + ex.Message); throw; }

    }
    private void _version_BeforePrint(object sender, CancelEventArgs e)
    {
        try
        {
            var builder = new ConfigurationBuilder()
                 .SetBasePath(Directory.GetCurrentDirectory())
                 .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            IConfigurationRoot configuration = builder.Build();
            var version = configuration["CP:Version"];
            xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the operationalservice summary excel Report's CP Version. The error message : " + ex.Message); throw; }
    }
    private void ClientCompanyLogo()
    {
        try
        {
            string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
            if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
            {
                prperpetuuitiLogo.Visible = false;
                if (imgbase64String.Contains(","))
                {
                    imgbase64String = imgbase64String.Split(',')[1];
                }
                byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                using (MemoryStream ms = new MemoryStream(imageBytes))
                {
                    Image image = Image.FromStream(ms);
                    prClientLogo.Image = image;
                }
            }
            else
            {
                prClientLogo.Visible = false;
                prperpetuuitiLogo.Visible = true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError("Error occured while display the customer logo in operationalservice summary excel report" + ex.Message.ToString());
        }
    }
}
