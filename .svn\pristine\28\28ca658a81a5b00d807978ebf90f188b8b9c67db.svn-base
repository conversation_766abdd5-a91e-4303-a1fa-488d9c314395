﻿using ContinuityPatrol.Application.Features.TableAccess.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.TableAccessModel;

namespace ContinuityPatrol.Application.UnitTests.Features.TableAccess.Queries
{
    public class GetTableAccessPaginatedListQueryHandlerTests
    {
        private readonly Mock<ITableAccessRepository> _mockTableAccessRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetTableAccessPaginatedListQueryHandler _handler;

        public GetTableAccessPaginatedListQueryHandlerTests()
        {
            _mockTableAccessRepository = new Mock<ITableAccessRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new GetTableAccessPaginatedListQueryHandler(_mockMapper.Object, _mockTableAccessRepository.Object);
        }

        [Fact]
        public async Task Handle_ValidRequest_ReturnsPaginatedResult()
        {
            var request = new GetTableAccessPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 10,
                SearchString = "test"
            };

            var tableAccessList = new List<Domain.Entities.TableAccess>
            {
                new Domain.Entities.TableAccess { SchemaName = "schema1", TableName = "table1" },
                new Domain.Entities.TableAccess { SchemaName = "schema2", TableName = "table2" }
            };

            var mappedList = tableAccessList.Select(t => new TableAccessListVm
            {
                SchemaName = t.SchemaName,
                TableName = t.TableName
            }).ToList();

            _mockTableAccessRepository
                .Setup(repo => repo.PaginatedListAllAsync())
                .Returns(tableAccessList.AsQueryable());

            _mockMapper
                .Setup(mapper => mapper.Map<TableAccessListVm>(It.IsAny<Domain.Entities.TableAccess>()))
                .Returns((Domain.Entities.TableAccess source) => new TableAccessListVm
                {
                    SchemaName = source.SchemaName,
                    TableName = source.TableName
                });

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Data.Count);
            Assert.Equal("schema1", result.Data.First().SchemaName);
            Assert.Equal("table1", result.Data.First().TableName);
            Assert.Equal(1, result.TotalCount);
            Assert.Equal(10, result.PageSize);
        }

        [Fact]
        public async Task Handle_EmptyResult_ReturnsEmptyPaginatedList()
        {
            var request = new GetTableAccessPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 10,
                SearchString = "nonexistent"
            };

            _mockTableAccessRepository
                .Setup(repo => repo.PaginatedListAllAsync())
                .Returns(Enumerable.Empty<Domain.Entities.TableAccess>().AsQueryable());

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);
            Assert.Equal(1, result.TotalCount);
            Assert.Equal(10, result.PageSize);
        }

        [Fact]
        public async Task Handle_RepositoryThrowsException_ThrowsException()
        {
            var request = new GetTableAccessPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 10,
                SearchString = "test"
            };

            _mockTableAccessRepository
                .Setup(repo => repo.PaginatedListAllAsync()).ToString();

            var exception = await Assert.ThrowsAsync<System.Exception>(() => _handler.Handle(request, CancellationToken.None));
            Assert.Equal("Repository error", exception.Message);
        }

        [Fact]
        public async Task Handle_ValidRequestWithPagination_ReturnsCorrectPaginatedResult()
        {
            var request = new GetTableAccessPaginatedListQuery
            {
                PageNumber = 2,
                PageSize = 5,
                SearchString = "table"
            };

            var tableAccessList = new List<Domain.Entities.TableAccess>
            {
                new Domain.Entities.TableAccess { SchemaName = "schema1", TableName = "table1" },
                new Domain.Entities.TableAccess { SchemaName = "schema2", TableName = "table2" },
                new Domain.Entities.TableAccess { SchemaName = "schema3", TableName = "table3" },
                new Domain.Entities.TableAccess { SchemaName = "schema4", TableName = "table4" },
                new Domain.Entities.TableAccess { SchemaName = "schema5", TableName = "table5" },
                new Domain.Entities.TableAccess { SchemaName = "schema6", TableName = "table6" }
            };

            var mappedList = tableAccessList.Select(t => new TableAccessListVm
            {
                SchemaName = t.SchemaName,
                TableName = t.TableName
            }).ToList();

            _mockTableAccessRepository
                .Setup(repo => repo.PaginatedListAllAsync())
                .Returns(tableAccessList.AsQueryable());

            _mockMapper
                .Setup(mapper => mapper.Map<TableAccessListVm>(It.IsAny<Domain.Entities.TableAccess>()))
                .Returns((Domain.Entities.TableAccess source) => new TableAccessListVm
                {
                    SchemaName = source.SchemaName,
                    TableName = source.TableName
                });

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(5, result.Data.Count);
            Assert.Equal(2, result.TotalCount);
            Assert.Equal(5, result.PageSize);
            Assert.Equal("schema6", result.Data.Last().SchemaName);
        }
    }
}   
