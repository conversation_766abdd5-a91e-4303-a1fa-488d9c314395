﻿@model ContinuityPatrol.Domain.ViewModels.EscalationMatrix.EscalationMatrixViewModel

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/wizard.css" rel="stylesheet" />
<link href="~/lib/jquery.steps/jquery.steps.css" rel="stylesheet" />
<style>
    .select2-container--default.select2-container--disabled .select2-selection--multiple{
        background-color: white;
    }
</style>
<div class="page-content">
    <div class="card Card_Design_None">

        <div class="">
            <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                <div class="d-flex align-items-center">
                    <h6 class="page_title">
                        <i class="cp-escalation_matrix_header-icon-3"></i>
                        <span>Escalation Matrix List</span>
                    </h6>
                </div>
                <form class="d-flex">
                    <div class="input-group me-2 w-auto">
                        <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                        <div class="input-group-text">
                            <div class="dropdown">
                                <span data-bs-toggle="dropdown"  title="Filter">
                                    <i class="cp-filter"></i>
                                </span>
                                <ul class="dropdown-menu filter-dropdown">
                                    <li>
                                        <h6 class="dropdown-header">Filter Search</h6>
                                    </li>
                                    <li class="dropdown-item">
                                        <div>
                                            <input class="form-check-input" type="checkbox" value="" id="Name">
                                            <label class="form-check-label" for="Name">
                                                Name
                                            </label>
                                        </div>
                                    </li>

                                </ul>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" id="creatematrix"
                            data-bs-target="#CreateModal">
                        <i class="cp-add me-1"></i>Create
                    </button>
                </form>

            </div>
        </div>
        <div class="card-body pt-0">
            <table id="tablebody" class="table table-hover dataTable align-middle" style="width:100%">
                <thead>
                    <tr>
                        <th  style="width:50px">Sr.No</th>
                        <th>Matrix Code Name</th>
                        <th>Matrix Used</th>
                        <th>Matrix Create Update Details</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>

        </div>
        <div id="ConfigurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true"></div>
        <div id="ConfigurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true"></div>

    </div>
  
    <!--Modal Create-->
    <div class="modal fade" data-bs-backdrop="static" id="CreateModal"  tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title">
                        <i class="cp-escalation_matrix_header-icon-3"></i>
                        <span>Escalation Matrix Configuration</span>
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="CreateForm" asp-controller="EscalationMatrix" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data">

                        <div class="mb-3 form-group">
                            <div class="form-label">Escalation Matrix Name</div>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="cp-name"></i>
                                </span>
                                <input asp-for="EscMatName" id="EscNameId" type="text" class="form-control" placeholder="Escalation Matrix Name" autocomplete="off" />
                            </div>
                            <span asp-validation-for="EscMatName" id="escmat-error"></span>
                        </div>
                        <div class="mb-3 form-group">
                            <div class="form-label">Escalation Matrix Description</div>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="cp-description"></i>
                                </span>
                                <input asp-for="EscMatDesc" id="EscDesId" type="text" class="form-control"
                                       placeholder="Enter Escalation Matrix Description" />
                            </div>
                            <span asp-validation-for="EscMatDesc" id="des-error"></span>
                        </div>


                        <div class="form-group">
                            <label class="form-label custom-cursor-default-hover">Escalation Matrix Type</label>
                            <div class="input-group">
                                <span class="input-group-text" id="basic-addon1">
                                    <i class="cp-formtype"></i>
                                </span>
                                <select asp-for="EscMatType" id="EscTypeId" class="form-select-modal" data-placeholder="Select Escalation Matrix Type">
                                    <option></option>
                                    <option value="1">Alerts</option>

                                </select>

                            </div>
                            <span asp-validation-for="EscMatType" id="mtType-error"></span>
                        </div>

                        <div class="form-group">
                            <div class="form-label">Escalation Matrix Owner</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-user"></i></span>
                                <select asp-for="OwnerId" id="EscOwnId" class="form-select-modal"  data-placeholder="Escalation Matrix Owner">
                                    @if (Model.PaginatedUserList?.Data != null && Model.PaginatedUserList.Data.Any()){
                                    @foreach (var list in Model.PaginatedUserList.Data)
                                    {
                                        if (list != null)
                                        {
                                            <option id="@list.Id" value="@list.Id">@list.LoginName</option>
                                        }
                                    }
                                    }

                                </select>
                            </div>
                        </div>
                        <input asp-for="Id" type="hidden" id="textgroupId" class="form-control" />
                        <input asp-for="EscMatCode" type="hidden" id="matcodeId" class="form-control" />
                        <input asp-for="CompanyId" type="hidden" id="companyId" class="form-control" />
                        <input asp-for="CreatedDate" type="hidden" id="createdateId" class="form-control" />
                        <input asp-for="ApproverId" type="hidden" id="approverId" class="form-control" />
                    </form>
                    <div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary">
                        <i class="cp-note me-1"></i>Note: All fields are mandatory
                        except optional
                    </small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" id="btnClick">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!--Modal Create Level-->
    <div class="modal fade" id="levelModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-xl ">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title">
                        <i class="cp-escalation_matrix_header-icon-3"></i>
                        <span>Escalation Matrix Level Configuration</span>
                    </h6>
                    <button type="button" class="btn-close" data-bs-target="#levelModal" data-bs-toggle="modal" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    
                    <div class="mb-0 h-100 card Card_Design_None shadow-sm">
                        <div class="card-body p-0">
                            <div class="row">
                                <div class="col-6 d-grid">
                                    <div class="card">
                                        <div class="d-flex align-items-center justify-content-between flex-wrap card-header p-1">
                                            <div class="d-flex align-items-center">
                                                <h6 class="page_title" title="Configure Escalation Levels :">
                                                    <span class="fs-6">Configure Escalation Levels :</span>
                                                </h6>
                                            </div>
                                            <form class="d-flex align-items-center">

                                                <button id="EscConfiglevel" type="button" class="btn btn-primary rounded-circle d-flex align-items-center btn-sm p-1" data-bs-toggle="modal"
                                                        data-bs-target="#LevelWizradModal">
                                                    <i class="cp-add"></i>
                                                </button>
                                            </form>

                                        </div>
                                        <div class="card-body p-0">
                                            <table id="tblEsclevel" class="table show active">
                                                 <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th>Level Name</th>
                                                        <th>Time</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead> 
                                                <tbody>
                                                   @*  <tr>
                                                    <td>1</td>
                                                    <td>Level_3</td>
                                                    <td>3 Minute(s)</td>
                                                    <td>
                                                    <div class="d-flex align-items-center  gap-2">
                                                    <span role="button" title="Edit" class="edit-button">
                                                    <i class="cp-edit"></i>
                                                    </span>

                                                    <span role="button" title="Delete">
                                                    <i class="cp-Delete"></i>
                                                    </span>

                                                    </div>
                                                    </td>
                                                    </tr>
                                                    <tr>
                                                    <td>2</td>
                                                    <td>Level_2</td>
                                                    <td>3 Minute(s)</td>
                                                    <td>
                                                    <div class="d-flex align-items-center  gap-2">
                                                    <span role="button" title="Edit" class="edit-button">
                                                    <i class="cp-edit"></i>
                                                    </span>

                                                    <span role="button" title="Delete">
                                                    <i class="cp-Delete"></i>
                                                    </span>

                                                    </div>
                                                    </td>
                                                    </tr>
                                                    <tr>
                                                    <td>3</td>
                                                    <td>Level_1</td>
                                                    <td>3 Minute(s)</td>
                                                    <td>
                                                    <div class="d-flex align-items-center  gap-2">
                                                    <span role="button" title="Edit" class="edit-button">
                                                    <i class="cp-edit"></i>
                                                    </span>

                                                    <span role="button" title="Delete">
                                                    <i class="cp-Delete"></i>
                                                    </span>

                                                    </div>
                                                    </td>
                                                    </tr> *@
                                                </tbody>
                                            </table>
                                        </div>
                                        <div id="ConfigurationCreatelev" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true"></div>
                                        <div id="ConfigurationDeletelev" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true"></div>

                                    </div>
                                </div>
                                <div class="col-6 d-grid">
                                    <div class="card">
                                        <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                                            <div class="d-flex align-items-center ">
                                                <h6 class="page_title" title="Escalation Matrix">

                                                    <span class="fs-6">Escalation Matrix</span>
                                                </h6>
                                            </div>


                                        </div>
                                        <div class="card-body p-2">
                                            <div class="mx-auto mt-3" style="width: 30rem;">
                                                <button type="button" class="btn btn-primary btn-sm">End</button>
                                                <div class="Escalation_Timeline">
                                                    <ul class="ul" id="Escalation_Timeline_ul">
                                                       
                                                        @* <li class="li"><span> <button type="button" class="btn btn-primary rounded-circle d-flex align-items-center btn-sm p-1"><i class="cp-add" cursorshover="true"></i></button></span></li> *@
                                                        @* <li class="li"><div class="Escalation_Timeline_Card card"><div class="d-flex align-items-center"><span class="Timeline_Card_Level badge bg-primary"> Level 0 </span><div class="d-grid ms-3"><span class="mb-1 text-truncate" title="">Escalation Level is not Configured. </span></div></div></div></li> *@
                                                   
                                                    </ul>
                                                    <button type="button" class="btn btn-primary btn-sm">Start Escalation</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <form>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary">
                        <i class="cp-note me-1"></i>Note: All fields are mandatory
                        except Optional
                    </small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm">Save</button>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>


<!--Level Wizrad Modal-->
<div class="modal fade" id="LevelWizradModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="LevelWizradModal" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable modal-dialog-centered Organization_modal wizard-sticky-header">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title" title="Escalation Level Configuration"><i class="cp-escalation_matrix_header-icon-3"></i><span>Escalation Level Configuration</span></h6>
                @* <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button> *@
            </div>
            <div class="modal-body py-0">

                <div class="wizard-content">
                    <form id="CreateEscleveForm" asp-controller="EscalationMatrix" asp-action="CreateOrUpdateEscLevel" method="post" enctype="multipart/form-data" class="tab-wizard wizard-circle wizard clearfix mb-2 example-form">

                        <h6>
                            <span class="step">
                                <i class="cp-control-file-type"></i>
                            </span>
                            <span class="step_title" title=" Levels Details">
                                Level Details
                            </span>
                        </h6>
                        <section>
                            <div>
                                <div class="form-group">
                                    <div class="form-label" title="Level Name">Level Name</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-name"></i></span>

                                        <input asp-for="EscalationMatrixLevelViewModel.EscLevName"  maxlength="30" type="text" class="form-control" id="escalationLevelName"
                                               placeholder="Enter Level Name" autocomplete="off" onkeyup="addValidation()"/>
                                        
                                    </div>
                                    <span asp-validation-for="EscalationMatrixLevelViewModel.EscLevName" id="lvlName"></span>
                                </div>
                                <div class="form-group">
                                    <div class="form-label" title="Level Description">Level Description</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-description"></i></span>

                                        <input asp-for="EscalationMatrixLevelViewModel.EscLevDescription" maxlength="30" type="text" class="form-control" id="escalationLevelDescriptin"
                                               placeholder="Enter Level Description" onkeyup="addDescriptionValidation()" />
                                        
                                    </div>
                                    <span asp-validation-for="EscalationMatrixLevelViewModel.EscLevDescription" id="lvlDesc"></span>
                                </div>
                                <div class="form-group">
                                    <div class="form-label" title="Escalation Time">Escalation Time</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-time"></i></span>
                                        <input asp-for="EscalationMatrixLevelViewModel.EscalationTime" maxlength="30" type="number" class="form-control" id="escalationLevelTime"
                                               placeholder="Enter Escalation Time" onkeyup="addTimeValidation()" />
                                        <span class="input-group-text">
                                            <select asp-for="EscalationMatrixLevelViewModel.EscalationTimeUnit" aria-label="Default select example" id="escalationTimeZone"
                                                    data-live-search="true"
                                                    class="form-select-modal" required>

                                                <option value="Days">Day(s)</option>
                                                <option value="Hour">Hour(s)</option>
                                                <option value="Mins">Min(s)</option>
                                            </select>
                                        </span>

                                        
                                    </div>
                                    <span asp-validation-for="EscalationMatrixLevelViewModel.EscalationTime" id="lvltime"></span>
                                </div>
                            </div>




                        </section>
                        <h6>
                            <span class="step">
                                <i class="cp-team-member-list"></i>
                            </span>
                            <span class="step_title" title=" Members">
                                Members
                            </span>
                        </h6>
                        <section >
                            @* Members Section *@
                            <div>

                                <div>
                                <div class="form-group">
                                    <div class="form-label" title="Members In Level">Members In Level</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-team-member-list"></i></span>
                                        <select aria-label="Default select example"
                                                data-live-search="true"
                                                data-placeholder="Currently No Teams Selected In Level"
                                                class="form-select-modal" multiple id="EsxLevMem" disabled="true">

                                           @*  <option value=""></option> *@

                                        </select>
                                    </div>
                                        <span asp-validation-for="EscalationMatrixLevelViewModel.EscMatLevelResourceID" id="teammembers"></span>
                                </div>
                                <div class="form-group">

                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-search"></i></span>
                                        <input class="form-control EscalMatSearch" type="text"  placeholder="Search Members To Add" />
                                        @* <span class="input-group-text"><button class="btn btn-sm btn-primary" id="escserlevusr">Search Resources</button></span>  *@
                                    </div>
                                </div>
                                </div>

                                <div class="form-group" style="max-height:712px; overflow-y:auto;">
                                    <table id="tblesclevUser" class="table">
                                        <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>Select</th>
                                                <th>Resource Name</th>

                                            </tr>
                                        </thead>
                                        <tbody>
                                              @{
                                                int i = 1;
                                            }
                                           @if (Model.PaginatedUserList?.Data != null && Model.PaginatedUserList.Data.Any()){
                                            @foreach (var item in Model.PaginatedUserList.Data)
                                            {
                                                @if (@item.UserInfo!=null)
                                                {
                                                <tr>
                                                    <td>
                                                        <span>@i</span>
                                                    </td>
                                                 

                                                    <td>
                                                            <input type="checkbox" class="form-check-input activeCheck" checkBoxId="@item.UserInfo.UserId" id="@item.UserInfo.UserId" onChange="BindUserName(id)" value="@item.LoginName">
                                                    </td>

                                                 
                                                   
                                                        <td>
                                                            <span class="EM_UserName">@item.LoginName</span><br>
                                                           
                                                                @if (string.IsNullOrEmpty(@item.UserInfo.Email))
                                                                 {
                                                                <span>  "NA"  </span> <br>
                                                                }
                                                                else
                                                                {
                                                                <span>  @item.UserInfo.Email</span>

                                                                <br>
                                                                
                                                                }



                                                            @if (string.IsNullOrEmpty(@item.UserInfo.Mobile))
                                                            {
                                                                <span>  "NA"  </span>

                                                                <br>
                                                            }
                                                            else
                                                            {
                                                                <span>  @item.UserInfo.Mobile</span>

                                                                <br>

                                                            }
                                                         
                                                        </td>
                                                   
                                                </tr>
                                                }
                                                i++;
                                            }  
                                           }

                                        </tbody>
                                    </table>
                                </div>
                                @* <input asp-for="EscalationMatrixLevelViewModel.EscMatLevelResourceID" type="hidden" id="esclevres" name="esclevres" class="form-control" /> *@
                                @Html.HiddenFor(model => model.EscalationMatrixLevelViewModel.EscMatLevelResourceID, new { @class = "form-control col-sm-2",Id="esclevres" })
                            </div>
                        </section>
                        <h6>
                            <span class="step">
                                <i class="cp-team-list"></i>
                            </span>
                            <span class="step_title" title=" Teams">
                                Teams
                            </span>
                        </h6>
                        <section>
                            @* Teams Section *@
                            <div>
                                <div class="form-group">
                                    <div class="form-label" title="Teams In Level">Teams In Level</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-teams"></i></span>
                                        <select aria-label="Default select example"
                                                data-live-search="true"
                                                data-placeholder="Currently No Teams Selected In Level"
                                                class="form-select-modal" multiple id="EsxLevTeam" disabled="true">

                                           @*  <option value=""></option> *@

                                        </select>
                                    </div>
                                    <span asp-validation-for="EscalationMatrixLevelViewModel.EscMatLevelTeamID" id="teams"></span>
                                </div>
                                <div class="form-group">

                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-search"></i></span>
                                        <input class="form-control EscalTeamSearch" type="search" placeholder="Search Teams To Add" />
                                        @* <span class="input-group-text"><button class="btn btn-sm btn-primary">Search Teams</button></span> *@
                                    </div>
                                </div>

                                <div class="form-group" style="max-height:712px; overflow-y:auto;">
                                    <table id="tblesclevTeam" class="table">
                                        <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>Select</th>
                                                <th>Team Name</th>

                                            </tr>
                                        </thead>
                                        <tbody>

                                            @{
                                                int j = 1;
                                            }
                                            @if (Model.PaginatedResultMasterList?.Data != null && Model.PaginatedResultMasterList.Data.Any())
                                            {
                                            @foreach (var item in Model.PaginatedResultMasterList.Data)
                                            {
                                                <tr>
                                                    <td>
                                                        <span>@j</span>
                                                    </td>
                                               

                                                    <td>
                                                       @*  <input type="checkbox" class="form-check-input" id="exampleCheck1"> *@
                                                        <input type="checkbox" class="form-check-input activeCheckteam" checkBoxId="@item.Id" id="@item.Id" onChange="BindTeamName(id)" value="@item.GroupName">
                                                    </td>

                                               
                                                    <td>

                                                        <span class="EM_TeamName">@item.GroupName</span><br>

                                                    </td>
                                                </tr>
                                                j++;
                                            }
                                            }

                                        </tbody>
                                    </table>
                                </div>
                                
                                @Html.HiddenFor(model => model.EscalationMatrixLevelViewModel.EscMatLevelTeamID, new { @class = "form-control col-sm-2",Id="esclevteam" })
                            </div>
                        </section>
                        <h6>
                            <span class="step">
                                <i class="cp-summary"></i>
                            </span>
                            <span class="step_title" title="ummary">
                                Summary
                            </span>
                        </h6>
                        <section>
                            @* Summary *@
                            <div class="mt-3">
                                <table class="table" style="table-layout:fixed">

                                    <tbody>
                                        <tr>
                                            <td class="fw-semibold">Level Name</td>
                                            <td id="sumaryLevelName"></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold">Level Description</td>
                                            <td id="sumaryLevelDescription"></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold">Escalation Time</td>
                                            <td id="sumaryLevelTime" style="word-wrap: break-word; !important"></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold">Selected Resources</td>
                                            <td id="summaryResources" style="word-wrap: break-word; !important"></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold">Selected Teams</td>
                                            <td id="summaryTeams"> </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </section>
                        <input asp-for="EscalationMatrixLevelViewModel.Id" type="hidden" id="textgrouplelId" class="form-control" />
                        @Html.HiddenFor(model => model.EscalationMatrixLevelViewModel.EscMatrixID, new { @class = "form-control col-sm-2",Id="EscmattId" })
                       
                   
                    </form>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary">
                    <i class="cp-note me-1"></i>Note: All fields are mandatory
                    except optional
                </small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm btn-cancel" data-bs-target="#levelModal" data-bs-toggle="modal" title="Cancel">Cancel</button>
                    <a class="btn btn-primary prev_btn btn-sm" href="javascript:void(0)" role="menuitem" onclick="form.steps('previous')">Previous</a>
                    <a class="btn btn-primary next_btn btn-sm" id="nextfunction" href="javascript:void(0)" role="menuitem" >Next</a>
                    <a class="btn btn-primary finish_btn btn-sm" href="javascript:void(0)" role="menuitem" id="btkesclevesub" @* onclick="form.steps('finish')" *@>Save</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!--Modal Delete-->
<div class="modal fade" id="DeleteModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <form asp-controller="EscalationMatrix" asp-action="Delete" asp-route-id="txtDeleteId" enctype="multipart/form-data">
                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" />
                </div>
                <div class="modal-body text-center pt-0">
                    <h4>Are you sure?</h4>
                    <p>
                        You want to delete <span class="font-weight-bolder text-primary">Escalation Matrix</span>
                        data?
                    </p>
                    <input asp-for="Id" type="hidden" id="textDeleteId" name="id" class="form-control" />
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="confirmDeleteButton">Yes</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div id="ManageExCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.CreateAndEdit" aria-hidden="true"></div>
<div id="ManageExDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.Delete" aria-hidden="true"></div>



<div class="modal fade" id="DeleteModallevel" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <form asp-controller="EscalationMatrix" asp-action="Deletelevel" asp-route-id="txtDeletelevId" enctype="multipart/form-data">
                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" />
                </div>
                <div class="modal-body text-center pt-0">
                    <h4>Are you sure?</h4>
                    <p>
                        You want to delete <span class="font-weight-bolder text-primary">Escalation Matrix Level</span>
                        data?
                    </p>
                    <input asp-for="EscalationMatrixLevelViewModel.Id" type="hidden" id="txtDeletelevId" name="id" class="form-control" />
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="confirmDeletelevButton">Yes</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts
    {
    <partial name="_ValidationScriptsPartial" />
}

<script src="~/js/Escalationmatrix.js"></script>
<script src="~/js/Escalationmatrixlevel.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.js"></script>
<script src="~/js/wizard.js"></script>

