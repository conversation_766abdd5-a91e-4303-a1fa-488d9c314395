﻿namespace ContinuityPatrol.Application.Features.MssqlNativeLogShippingMonitorLog.Commands.Create;

public class CreateMssqlNativeLogShippingMonitorLogCommandHandler : IRequestHandler<
    CreateMssqlNativeLogShippingMonitorLogCommand, CreateMssqlNativeLogShippingMonitorLogResponse>
{
    private readonly IMapper _mapper;
    private readonly IMssqlNativeLogShippingMonitorLogRepository _mssqlNativeLogShippingMonitorLogRepository;

    public CreateMssqlNativeLogShippingMonitorLogCommandHandler(IMapper mapper,
        IMssqlNativeLogShippingMonitorLogRepository mssqlNativeLogShippingMonitorLogRepository)
    {
        _mapper = mapper;
        _mssqlNativeLogShippingMonitorLogRepository = mssqlNativeLogShippingMonitorLogRepository;
    }

    public async Task<CreateMssqlNativeLogShippingMonitorLogResponse> Handle(
        CreateMssqlNativeLogShippingMonitorLogCommand request, CancellationToken cancellationToken)
    {
        var mssqlNativeLogShippingMonitorLog = _mapper.Map<Domain.Entities.MssqlNativeLogShippingMonitorLog>(request);

        mssqlNativeLogShippingMonitorLog =
            await _mssqlNativeLogShippingMonitorLogRepository.AddAsync(mssqlNativeLogShippingMonitorLog);

        var response = new CreateMssqlNativeLogShippingMonitorLogResponse
        {
            Message = Message.Create(nameof(Domain.Entities.MssqlNativeLogShippingMonitorLog),
                mssqlNativeLogShippingMonitorLog.ReferenceId),
            Id = mssqlNativeLogShippingMonitorLog.ReferenceId
        };

        return response;
    }
}