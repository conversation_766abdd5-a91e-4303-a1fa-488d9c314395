using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Delete;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetByOperationId;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationGroupModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class BulkImportOperationGroupControllerTests : IClassFixture<BulkImportOperationGroupFixture>
{
    private readonly BulkImportOperationGroupFixture _bulkImportOperationGroupFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly BulkImportOperationGroupsController _controller;

    public BulkImportOperationGroupControllerTests(BulkImportOperationGroupFixture bulkImportOperationGroupFixture)
    {
        _bulkImportOperationGroupFixture = bulkImportOperationGroupFixture;

        var testBuilder = new ControllerTestBuilder<BulkImportOperationGroupsController>();
        _controller = testBuilder.CreateController(
            _ => new BulkImportOperationGroupsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetBulkImportOperationGroups_ReturnsExpectedList()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
       // _controller.Cache.Remove(ApplicationConstants.Cache.AllBulkImportOperationGroupCacheKey + companyId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBulkImportOperationGroupListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_bulkImportOperationGroupFixture.BulkImportOperationGroupListVm);

        // Act
        var result = await _controller.GetBulkImportOperationGroups();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var operationGroups = Assert.IsAssignableFrom<List<BulkImportOperationGroupListVm>>(okResult.Value);
        Assert.Equal(3, operationGroups.Count);
    }

    [Fact]
    public async Task GetBulkImportOperationGroups_ReturnsEmptyList_WhenNoGroupsExist()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        //_controller.Cache.Remove(ApplicationConstants.Cache.AllBulkImportOperationGroupCacheKey + companyId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBulkImportOperationGroupListQuery>(), default))
            .ReturnsAsync(new List<BulkImportOperationGroupListVm>());

        // Act
        var result = await _controller.GetBulkImportOperationGroups();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var operationGroups = Assert.IsAssignableFrom<List<BulkImportOperationGroupListVm>>(okResult.Value);
        Assert.Empty(operationGroups);
    }

    [Fact]
    public async Task GetBulkImportOperationGroupById_ReturnsGroup_WhenIdIsValid()
    {
        // Arrange
        var groupId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBulkImportOperationGroupDetailQuery>(q => q.Id == groupId), default))
            .ReturnsAsync(_bulkImportOperationGroupFixture.BulkImportOperationGroupDetailVm);

        // Act
        var result = await _controller.GetBulkImportOperationGroupById(groupId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var operationGroup = Assert.IsType<BulkImportOperationGroupDetailVm>(okResult.Value);
        Assert.NotNull(operationGroup);
    }

    [Fact]
    public async Task GetBulkImportOperationGroupById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetBulkImportOperationGroupById("invalid-guid"));
    }

    [Fact]
    public async Task CreateBulkImportOperationGroup_Returns201Created()
    {
        // Arrange
        var command = _bulkImportOperationGroupFixture.CreateBulkImportOperationGroupCommand;
        var expectedMessage = $"BulkImportOperationGroup '{command.InfraObjectName}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBulkImportOperationGroupResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBulkImportOperationGroup(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBulkImportOperationGroupResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBulkImportOperationGroup_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"BulkImportOperationGroup '{_bulkImportOperationGroupFixture.UpdateBulkImportOperationGroupCommand.InfraObjectName}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateBulkImportOperationGroupCommand>(), default))
            .ReturnsAsync(new UpdateBulkImportOperationGroupResponse
            {
                Message = expectedMessage,
                Id = _bulkImportOperationGroupFixture.UpdateBulkImportOperationGroupCommand.Id
            });

        // Act
        var result = await _controller.UpdateBulkImportOperationGroup(_bulkImportOperationGroupFixture.UpdateBulkImportOperationGroupCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBulkImportOperationGroupResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteBulkImportOperationGroup_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "BulkImportOperationGroup 'Test Group' has been deleted successfully!.";
        var groupId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBulkImportOperationGroupCommand>(c => c.Id == groupId), default))
            .ReturnsAsync(new DeleteBulkImportOperationGroupResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteBulkImportOperationGroup(groupId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBulkImportOperationGroupResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetBulkImportOperationGroupByOperationId_ReturnsGroup_WhenOperationIdIsValid()
    {
        // Arrange
        var operationId = Guid.NewGuid().ToString();
        var expectedGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroupListVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByOperationIdQuery>(q => q.BulkImportOperationId == operationId), default))
            .ReturnsAsync(expectedGroup);

        // Act
        var result = await _controller.GetBulkImportOperationGroupByOperationId(operationId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var operationGroup = Assert.IsType<List<BulkImportOperationGroupListVm>>(okResult.Value);
        Assert.NotNull(operationGroup);
    }

    [Fact]
    public async Task CreateBulkImportOperationGroup_ValidatesInfraObjectName()
    {
        // Arrange
        var command = new CreateBulkImportOperationGroupCommand
        {
            InfraObjectName = "", // Empty name should cause validation error
            Status = "Initiated",
            ConditionalOperation = 1
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("InfraObjectName is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateBulkImportOperationGroup(command));
    }

   


    [Fact]
    public async Task CreateBulkImportOperationGroup_HandlesComplexProperties()
    {
        // Arrange
        var command = new CreateBulkImportOperationGroupCommand
        {
            BulkImportOperationId = Guid.NewGuid().ToString(),
            CompanyId = Guid.NewGuid().ToString(),
            Properties = "{\"groupType\":\"enterprise\",\"priority\":\"critical\",\"batchSize\":500,\"validation\":\"strict\",\"rollback\":\"enabled\",\"dependencies\":[\"network\",\"storage\",\"compute\"]}",
            Status = "Initiated",
            ProgressStatus = "0%",
            ConditionalOperation = 1,
            NodeId = Guid.NewGuid().ToString(),
            InfraObjectName = "Enterprise Critical Infrastructure Group"
        };

        var expectedMessage = $"BulkImportOperationGroup '{command.InfraObjectName}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBulkImportOperationGroupResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBulkImportOperationGroup(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBulkImportOperationGroupResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task CreateBulkImportOperationGroup_HandlesComplexGroupProperties()
    {
        // Arrange
        var command = new CreateBulkImportOperationGroupCommand
        {
            BulkImportOperationId = Guid.NewGuid().ToString(),
            CompanyId = Guid.NewGuid().ToString(),
            ProgressStatus = "running",
            InfraObjectName = "Enterprise-Critical-Infrastructure-Group",
            Properties = "{\"groupType\":\"enterprise\",\"priority\":\"critical\",\"batchSize\":1000,\"validation\":\"strict\",\"dependencies\":[\"server-group\",\"database-group\",\"network-group\"],\"notifications\":[\"<EMAIL>\",\"<EMAIL>\"],\"rollbackStrategy\":\"automatic\",\"timeoutMinutes\":480}",
            Status = "Initiated",
           ErrorMessage = "",
           ConditionalOperation = 5,
           NodeId = Guid.NewGuid().ToString()
        };

        var expectedMessage = $"BulkImportOperationGroup '{command.InfraObjectName}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBulkImportOperationGroupResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBulkImportOperationGroup(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBulkImportOperationGroupResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBulkImportOperationGroup_HandlesProgressTracking()
    {
        // Arrange
        var command = new UpdateBulkImportOperationGroupCommand
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = Guid.NewGuid().ToString(),
           ProgressStatus = "pending",
            InfraObjectName = "Updated Infrastructure Group",
            Properties = "{\"progress\":75,\"completedOperations\":15,\"totalOperations\":20,\"currentPhase\":\"database-import\",\"estimatedCompletion\":\"2024-01-15T18:00:00Z\"}",
            Status = "In Progress",
            ErrorMessage = "",
            ConditionalOperation = 5,
            NodeId = Guid.NewGuid().ToString()

        };

        var expectedMessage = $"BulkImportOperationGroup '{command.InfraObjectName}' has been updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateBulkImportOperationGroupResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateBulkImportOperationGroup(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBulkImportOperationGroupResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

   
    [Fact]
    public async Task CreateBulkImportOperationGroup_ValidatesPropertiesFormat()
    {
        // Arrange
        var command = new CreateBulkImportOperationGroupCommand
        {
            CompanyId = Guid.NewGuid().ToString(),
            InfraObjectName = "Test Group",
            Properties = "invalid-json-format", // Invalid JSON should cause validation error
            Status = "Initiated"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("Properties must be valid JSON format"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateBulkImportOperationGroup(command));
    }

    [Fact]
    public async Task UpdateBulkImportOperationGroup_ValidatesGroupExists()
    {
        // Arrange
        var command = new UpdateBulkImportOperationGroupCommand
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = Guid.NewGuid().ToString(),
            InfraObjectName = "Non-existent Group"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("BulkImportOperationGroup not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateBulkImportOperationGroup(command));
    }

    [Fact]
    public async Task GetBulkImportOperationGroupByOperationId_HandlesOperationNotFound()
    {
        // Arrange
        var operationId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByOperationIdQuery>(q => q.BulkImportOperationId == operationId), default))
            .ReturnsAsync(new List<BulkImportOperationGroupListVm>());

        // Act
        var result = await _controller.GetBulkImportOperationGroupByOperationId(operationId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var operationGroups = Assert.IsType<List<BulkImportOperationGroupListVm>>(okResult.Value);
        Assert.Empty(operationGroups);
    }

    [Fact]
    public async Task DeleteBulkImportOperationGroup_VerifiesGroupIsDeactivated()
    {
        // Arrange
        var groupId = Guid.NewGuid().ToString();
        var expectedMessage = "BulkImportOperationGroup 'Test Group' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBulkImportOperationGroupCommand>(c => c.Id == groupId), default))
            .ReturnsAsync(new DeleteBulkImportOperationGroupResponse
            {
                IsActive = false, // Verify it's deactivated
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteBulkImportOperationGroup(groupId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBulkImportOperationGroupResponse>(okResult.Value);
        Assert.False(response.IsActive);
        Assert.Equal(expectedMessage, response.Message);
    }
}
