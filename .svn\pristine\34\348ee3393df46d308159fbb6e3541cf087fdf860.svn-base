﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class MonitorServiceFilterSpecification : Specification<MonitorService>
{
    public MonitorServiceFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.BusinessServiceName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("infraobjectname=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.InfraObjectName.Contains(stringItem.Replace("infraobjectname=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("businessservicename=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.BusinessServiceName.Contains(stringItem.Replace("businessservicename=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("workflowname=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.WorkflowName.Contains(stringItem.Replace("workflowname=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("servername=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.ServerName.Contains(stringItem.Replace("servername=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("type=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Type.Contains(stringItem.Replace("type=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.InfraObjectName.Contains(searchString) || p.BusinessServiceName.Contains(searchString) ||
                    p.WorkflowName.Contains(searchString) ||
                    p.Type.Contains(searchString) || p.ServerName.Contains(searchString);
            }
        }
    }
}