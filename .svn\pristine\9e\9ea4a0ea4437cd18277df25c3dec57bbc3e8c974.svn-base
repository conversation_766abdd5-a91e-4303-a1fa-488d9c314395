﻿let LPassword = "";
let blurPassword = "";
let blurcurrentPassword = "";

async function onfocusPassword(encryptedPassword) {
    if (encryptedPassword && encryptedPassword.length > 30) {
        try {
            return await DecryptPassword(encryptedPassword);
        } catch (error) {
            console.error("Error decrypting password on focus:", error);
            return null;
        }
    }
    return null;
}

$(".cp-password-hide").on('mouseover', function () {
    $(this).attr("title", "Hide Password");
});

$(".cp-password-visible").on('mouseover', function () {
    $(this).attr("title", "Show Password");
});

function showPassword(input, icon) {
    input.attr("type", "text");
    icon.removeClass("cp-password-visible").addClass("cp-password-hide")
    var icon = $(".cp-password-hide");
    icon.attr("title", "Hide Password");
    $(".cp-password-hide").on('mouseover', function () {
        $(this).attr("title", "Hide Password");
    });
}
function hidePassword(input, icon) {
    input.attr("type", "password");
    icon.removeClass("cp-password-hide").addClass("cp-password-visible");
    var icon = $(".cp-password-visible");
    icon.attr("title", "Show Password");
    $(".cp-password-visible").on('mouseover', function () {
        $(this).attr("title", "Show Password");
    });
}

async function inputpassword(id, value) {
    const actions = {
        userPassword: () => validatePassword(value, 'User'),
        workflowPassword: () => validatePassword(value, 'Workflow'),
        changeNewPassword: () => validatePassword(value, 'change'),
        Password: () => validatePassword(value, 'Workflowpassword'),
        CurrentPassword: () => validateCurrentPassword(value)
    };

    if (actions[id]) await actions[id]();
}

async function validateCurrentPassword(value) {
    let currentPassword1 = $("#CurrentPassword").val();
    let newpass = $("#Password").val();
    let SamePassword1 = await onfocusPassword(currentPassword1);
    const errorElement = $('#OldPassword-error');
    let Passwordvalid1 = $("#changeNewPassword").val();
    let Passwordsame = await onfocusPassword(Passwordvalid1);
    if (!value) {
        errorElement.text('Enter current password').addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
    }
    if (SamePassword1 === value || (Passwordsame === value)) {
        $('#NewPassword-error').text('Same password already exists').addClass('field-validation-error');
        return false;
    }
    //else if (!newpass)
    //{
    //    $('#NewPassword-error').text('Enter new password').addClass('field-validation-error');
    //    return false;
    //}
    else {
        $('#NewPassword-error').text('').removeClass('field-validation-error');
        return true;
    }
    return true;
}

async function inputConfirmpassword(id, value) {
    if (LPassword == "") {
        return false;
    }
    const confirmPassword = value;
    const errorElement =
        (id === 'userConfirmPassword' || id === 'changeConfirmPassword' || id === 'ConfirmPassword')
            ? $('#ConfirmPassword-error')
            : $('#Confirmpassword-error');

    validateConfirmPassword(confirmPassword, errorElement);
}

async function blurpassword(id, password) {

    if (password !== "" && password.length > 0 && password.length < 64) {
        try {
            if (id === 'userPassword' || id === 'userConfirmPassword' || id === 'CurrentPassword' || id === 'changeConfirmPassword') {

                let loginName = $("#textLoginName").val()?.toLowerCase();
                if (!loginName) {
                    loginName = $("#LoginName").data("loginnames")?.toLowerCase();
                }
                blurPassword = await EncryptPassword((loginName || "") + password, "#" + id);
                /*blurPassword = await EncryptPassword(loginName + password, "#" + this.id); */
                let tempPassword = await EncryptPassword(password, "#encriptedPassword");
                $('#encriptedPassword').val(tempPassword);
            }
            else if (id == "changeNewPassword") {
                let userId = $("#loginId").data("loginid");
                let loginName = $("#LoginName").data("loginnames")?.toLowerCase();
                let newPassword = $("#changeNewPassword").val();
                await $.ajax({
                    url: RootUrl + "Admin/User/IsNewPasswordInLastFive",
                    type: "POST",
                    dataType: "json",
                    data: {
                        UserId: userId,
                        NewPassword: loginName.toLowerCase() + newPassword,
                    },
                    success: function (response) {
                        if (!response) {
                            $('#NewPassword-error').text('Please try with a different password, Last five password can not be accepted.').addClass('field-validation-error');
                            return false;
                        }
                    },
                    error: function (response) {
                        errorNotification(response)
                    }
                });
                blurPassword = await EncryptPassword(loginName + password, "#" + id);
            }
            else {
                blurPassword = await EncryptPassword(password, "#" + id);
            }
            $(`#${id}`).val(blurPassword);
        } catch (error) {
            console.error("Error hashing password on blur: " + error);
        }
    } else {
        $('#encriptedPassword').val("");
    }

    $(`#${id}`).attr('type', 'password');
    $('.toggle-password i').removeClass('fs-6');
    $(`#${id}`).siblings('.toggle-password').find('i').addClass('cp-password-visible fs-6');
};
async function focuspassword(id) {

    if (id === 'userPassword') {
        const loginName = $('#textLoginName').val();
        let encryptedPassword = $(`#${id}`).val();
        if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
            let afterLoginName = (await onfocusPassword(encryptedPassword))?.substring(loginName.length);
            $(`#${id}`).val(afterLoginName);
        }
    }
    if (id === 'changeNewPassword' || id === 'CurrentPassword') {
        const loginName = $("#LoginName").data("loginnames");
        let encryptedPassword = $(`#${id}`).val();
        if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
            let afterLoginName = (await onfocusPassword(encryptedPassword))?.substring(loginName.length);
            $(`#${id}`).val(afterLoginName);
        }
    }
    if (id === 'workflowPassword' || id === 'Password') {
        let encryptedPassword = $(`#${id}`).val();
        if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
            let afterLoginName = await onfocusPassword(encryptedPassword);
            $(`#${id}`).val(afterLoginName);
        }
    }
}

async function focusconfirmpassword(id) {

    if (id === 'userConfirmPassword') {
        const loginPassword = $('#userPassword').val();
        if (!loginPassword) {
            LPassword = "";
            return false;
        }
        if (loginPassword?.length < 60) {
            LPassword = loginPassword;
        } else {
            LPassword = await DecryptPassword(loginPassword);
        }
        const loginName = $('#textLoginName').val();
        let encryptedPassword = $(`#${id}`).val();
        if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
            let afterLoginName = (await onfocusPassword(encryptedPassword))?.substring(loginName.length);
            $(`#${id}`).val(afterLoginName);
        }
    }
    else if (id === 'workflowConfirmPassword') {
        const loginPassword = $('#workflowPassword').val();
        if (!loginPassword) {
            LPassword = "";
            return false;
        }
        if (loginPassword?.length < 60) {
            LPassword = loginPassword;
        } else {
            LPassword = await DecryptPassword(loginPassword);
        }
        let encryptedPassword = $(`#${id}`).val();
        if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
            let afterLoginName = await onfocusPassword(encryptedPassword);
            $(`#${id}`).val(afterLoginName);
        }

    }
    else if (id === 'ConfirmPassword') {
        const loginPassword = $('#Password').val();
        if (!loginPassword) {
            LPassword = "";
            return false;
        }
        if (loginPassword?.length < 60) {
            LPassword = loginPassword;
        } else {
            LPassword = await DecryptPassword(loginPassword);
        }
        let encryptedPassword = $(`#${id}`).val();
        if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
            let afterLoginName = await onfocusPassword(encryptedPassword);
            $(`#${id}`).val(afterLoginName);
        }
    } else {
        if (id === 'changeConfirmPassword') {
            const loginPassword = $('#changeNewPassword').val();
            if (!loginPassword) {
                LPassword = "";
                return false;
            }
            if (loginPassword?.length < 60) {
                LPassword = loginPassword;
            } else {
                LPassword = await DecryptPassword(loginPassword);
            }
        }
        let loginName = $("#LoginName").data("loginnames");
        let encryptedPassword = $(`#${id}`).val();
        if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
            let afterLoginName = (await onfocusPassword(encryptedPassword))?.substring(loginName.length);
            $(`#${id}`).val(afterLoginName);
        }

    }

};

async function validateConfirmPassword(value, errorElement) {

    if (!value) {
        errorElement.text('Enter confirm password')
            .addClass('field-validation-error');
        return false;
    }
    const passwordsMatch = [
        await confirmationPassword(value, errorElement),
    ];
    if (!passwordsMatch[0]) {
        errorElement.text('Password does not match').addClass('field-validation-error');
        return false;
    }
    return await CommonValidation(errorElement, passwordsMatch);
}

async function confirmationPassword(confirmPassword, errorElement) {
    if (confirmPassword?.length < 60) {
        // let Password = errorElement.is($('#ConfirmPassword-error')) ? ($('#userPassword').val() || $('#changeNewPassword').val()) : $("#workflowPassword").val();
        let Password = errorElement.is($('#ConfirmPassword-error')) ? ($('#userPassword').val() || $('#changeNewPassword').val() || $('#Password').val()) : $("#workflowPassword").val();

        let loginName = $("#textLoginName").val()?.toLowerCase();
        if (!loginName) {
            loginName = $("#LoginName").data("loginnames")?.toLowerCase();
        }

        let decryptedPassword = await DecryptPassword(Password);
        if (errorElement.is($('#ConfirmPassword-error'))) {
            passwordsMatch = decryptedPassword === (loginName || "") + confirmPassword;
        } else {
            passwordsMatch = decryptedPassword === confirmPassword;
        }

        if (!passwordsMatch) {
            errorElement.text('Password does not match').addClass('field-validation-error');
        } else {
            errorElement.text('').removeClass('field-validation-error');
        }
        return passwordsMatch;
    } else {
        let userLoginPassword = errorElement.is($('#ConfirmPassword-error')) ? ($('#userPassword').val() || $('#changeNewPassword').val()) : $("#workflowPassword").val();
        let userConfirmLoginPassword = errorElement.is($('#ConfirmPassword-error')) ? ($('#userConfirmPassword').val() || $('#changeConfirmPassword').val()) : $("#workflowConfirmPassword").val();
        //if ($('#ConfirmPassword').val() != "") {
        //    return false;
        //}
        let decryptPwd = await DecryptPassword(userLoginPassword);
        let decryptConformPwd2 = await DecryptPassword(userConfirmLoginPassword);
        let res = decryptPwd == decryptConformPwd2;
        if (!res) {
            errorElement.text('Password does not match').addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
}

async function validateLoginPassword(value, errorElement) {
    if (!value) {
        errorElement.text('Enter password')
            .addClass('field-validation-error');
        return false;
    }
    if (value.length > 60) {
        value = await DecryptPassword(value)
    }
    return await PasswordPolicy(value, errorElement);
}

async function validatePassword(value, type) {
    const errorElement =
        type === 'User' ? $('#LoginPassword-error') : type === 'Workflow' ? $('#Password-error') : type === 'Workflowpassword' ? $('#NewPassword-error') : $('#NewPassword-error');
    // const errorElement = type === 'Login' ? $('#LoginPassword-error') : $('#Password-error');
    let disableSave = type === 'Workflow' || 'change'; // Only disable save for new passwords

    if (!value) {
        errorElement.text(type === 'User' ? 'Enter password' : 'Enter new password')
            .addClass('field-validation-error');
        if (disableSave) $("#SaveFunction").prop('disabled', true);
        return false;
    }

    if (type === 'User' && value.length > 60) {
        value = await DecryptPassword(value);
    }

    if (type === 'Workflow' && value === $("#CurrentPassword").val()) {
        errorElement.text('Same password already exists').addClass('field-validation-error');
        return false;
    }

    try {
        const isValid = await PasswordPolicy(value, errorElement);
        //errorElement.text(isValid ? '' : 'Password does not meet policy requirements')
        //    .toggleClass('field-validation-error', !isValid);

        if (disableSave) $("#SaveFunction").prop('disabled', !isValid);
        return isValid;
    } catch (error) {
        if (disableSave) $("#SaveFunction").prop('disabled', true);
        return false;
    }
}

////Passwword Policy
async function PasswordPolicy(value, errorElement) {
    const settingList = "Admin/Settings/GetList";
    let defaultSkey = "Password Policy";
    try {
        const response = await $.ajax({
            type: "GET",
            url: RootUrl + settingList,
            async: true
        });
        if (response && response.length > 0) {
            const passwordPolicy = response.find(pwdplcy => pwdplcy.sKey === defaultSkey);
            if (passwordPolicy) {
                let passwordRules = JSON.parse(passwordPolicy.sValue);
                let minSValue = passwordRules.minSValue;
                let maxSValue = passwordRules.maxSValue;
                let minUpSValue = passwordRules.minUpSValue;
                let minNumSValue = passwordRules.minNumSValue;
                let minLowSValue = passwordRules.minLowSValue;
                let minSpclSValue = passwordRules.minSpclSValue;
                if (value == "Password_strength") {

                    return { minSValue, minUpSValue, minNumSValue, minLowSValue, minSpclSValue, maxSValue };
                }
                return validatePolicyPassword(value, errorElement, minSValue, minUpSValue, minNumSValue, minLowSValue, minSpclSValue, maxSValue);
            }
            else {
                return settingPassword(value)
            }
        }
        else {
            return settingPassword(value)
        }
    } catch (error) {
        console.error("Error fetching password policy: " + error);
        return "Error fetching password policy";
    }

}
function validatePolicyPassword(value, errorElement, minSValue, minUpSValue, minNumSValue, minLowSValue, minSpclSValue, maxSValue) {

    const uppercaseCount = (value.match(/[A-Z]/g) || []).length.toString();
    const numericCount = (value.match(/[0-9]/g) || []).length.toString();
    const lowercaseCount = (value.match(/[a-z]/g) || []).length.toString();
    const specialCount = (value.match(/[^a-zA-Z0-9]/g) || []).length.toString();

    if (value.length < parseInt(minSValue)) {
        errorElement.text('').removeClass('field-validation-error');
        errorElement.text("Password should contain at least " + minSValue + " characters").addClass('field-validation-error');
        return false;
    }

    if (uppercaseCount < parseInt(minUpSValue)) {
        errorElement.text('').removeClass('field-validation-error');
        errorElement.text("Password should contain at least " + minUpSValue + " uppercase characters").addClass('field-validation-error');
        return false;
    }

    if (numericCount < parseInt(minNumSValue)) {
        errorElement.text('').removeClass('field-validation-error');
        errorElement.text("Password should contain at least " + minNumSValue + " numeric characters").addClass('field-validation-error');
        return false;
    }

    if (lowercaseCount < parseInt(minLowSValue)) {
        errorElement.text('').removeClass('field-validation-error');
        errorElement.text("Password should contain at least " + minLowSValue + " lowercase characters").addClass('field-validation-error');
        return false;
    }

    if (specialCount < parseInt(minSpclSValue)) {
        errorElement.text('').removeClass('field-validation-error');
        errorElement.text("Password should contain at least " + minSpclSValue + " special characters").addClass('field-validation-error');
        return false;
    }

    if (value.length > parseInt(maxSValue)) {
        errorElement.text('').removeClass('field-validation-error');
        errorElement.text("Password must be " + maxSValue + " characters").addClass('field-validation-error');
        return false;
    }
    errorElement.text('').removeClass('field-validation-error');

    return true;

}
function settingPassword(value) {
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[#$@!%&*?])[A-Za-z\d#$@!%&*?]{8,15}$/;
    const errorElement = $('#LoginPassword-error');
    const errorMessages = {
        invalid: "Invalid password",
        length: "Password must be at least 8 characters",
        number: "Password must contain at least one number",
        symbol: "Password must contain at least one symbol",
        uppercase: "Password must contain at least one uppercase letter",
        lowercase: "Password must contain at least one lowercase letter",
    };

    if (value.length < 8) {
        errorElement.text(errorMessages.length).addClass('field-validation-error');
        return false;
    } else if (!/\d/.test(value)) {
        errorElement.text(errorMessages.number).addClass('field-validation-error');
        return false;
    } else if (!/[!@#$%^&*]/.test(value)) {
        errorElement.text(errorMessages.symbol).addClass('field-validation-error');
        return false;
    } else if (!/[A-Z]/.test(value)) {
        errorElement.text(errorMessages.uppercase).addClass('field-validation-error');
        return false;
    } else if (!/[a-z]/.test(value)) {
        errorElement.text(errorMessages.lowercase).addClass('field-validation-error');
        return false;
    }
    else if (!passwordRegex.test(value)) {
        errorElement.text(errorMessages.invalid).addClass('field-validation-error');
        return false;
    }
    errorElement.text('').removeClass('field-validation-error');

    return true;
}
function change_pwd_strength_debounce(func, delay = 300) {
    let timer;
    return function () {
        const context = this;
        const args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
            func.apply(context, args);
        }, delay);
    };
}

$(function () {
    $("input#changeNewPassword").on("keyup", change_pwd_strength_debounce(async function () {
        const value = $(this).val();
        try {
            const policyValues = await PasswordPolicy("Password_strength");
            if (policyValues.minSValue === 0 || policyValues.maxSValue === 0 || policyValues.minLowSValue === 0 || policyValues.minUpSValue === 0
                || policyValues.minSpclSValue === 0) {
                $("input#changeNewPassword").on("focus keyup", function () {
                    let score = 0;
                    let a = $(this).val();
                    let desc = new Array();


                    desc[0] = "Too short";
                    desc[1] = "Weak";
                    desc[2] = "Good";
                    desc[3] = "Strong";
                    desc[4] = "Best";

                    $("#length").text("At least 8 characters");
                    $("#pnum").text("At least 1 number");
                    $("#capital").text("At least 1 lowercase & 1 uppercase letter");
                    $("#spchar").text("At least 1 special character");
                    $("#mycPass_strength_wrap").fadeIn(400);


                    if (a.length >= 8) {
                        $("#length").removeClass("invalid").addClass("valid");

                        score++;
                    } else {
                        $("#length").removeClass("valid").addClass("invalid");
                    }

                    if (a.match(/\d/)) {
                        $("#pnum").removeClass("invalid").addClass("valid");

                        score++;
                    } else {
                        $("#pnum").removeClass("valid").addClass("invalid");
                    }


                    if (a.match(/[A-Z]/) && a.match(/[a-z]/)) {
                        $("#capital").removeClass("invalid").addClass("valid");

                        score++;
                    } else {
                        $("#capital").removeClass("valid").addClass("invalid");
                    }

                    if (a.match(/.[!,@,#,$,%,^,&,*,?,_,~,-,(,)]/)) {
                        $("#spchar").removeClass("invalid").addClass("valid");

                        score++;
                    } else {
                        $("#spchar").removeClass("valid").addClass("invalid");
                    }

                    if (a.length > 0) {
                        $("#passwordDescription").text(desc[score]);
                        $("#passwordStrength").removeClass().addClass("strength" + score);
                    } else {
                        $("#passwordDescription").text("Password not entered");
                        $("#passwordStrength").removeClass().addClass("strength" + score);
                    }
                });
                $("input#changeNewPassword").on('blur', function () {
                    $("#mycPass_strength_wrap").fadeOut(400);
                    let value = this.value.replace(/\s+/g, '');
                    blurpassword(this.id, value);

                });
                $("input#changeConfirmPassword").on('focus keyup', function () {
                    $("#mycPass_strength_wrap").fadeOut(400);
                    focusconfirmpassword(this.id);
                });
            }
            else {
                $("#length").text("Between " + policyValues.minSValue + " to " + policyValues.maxSValue + " characters");

                $("#pnum").text("At least " + policyValues.minNumSValue + " number(s)");
                $("#capital").text("At least " + policyValues.minLowSValue + " lowercase & " + policyValues.minUpSValue + " uppercase letter(s)");
                $("#spchar").text("At least " + policyValues.minSpclSValue + " special character(s)");
                const minSValue = policyValues.minSValue;
                const minUpSValue = policyValues.minUpSValue;
                const minNumSValue = policyValues.minNumSValue;
                const minLowSValue = policyValues.minLowSValue;
                const minSpclSValue = policyValues.minSpclSValue;
                const maxSValue = policyValues.maxSValue;
                const desc = ["Too short", "Weak", "Good", "Strong", "Best"];
                let score = 0;
                const uppercaseCount = (value.match(/[A-Z]/g) || []).length;
                const numericCount = (value.match(/[0-9]/g) || []).length;
                const lowercaseCount = (value.match(/[a-z]/g) || []).length;
                const specialCount = (value.match(/[^a-zA-Z0-9]/g) || []).length;

                $("#mycPass_strength_wrap").fadeIn(400);
                if (value.length >= minSValue) {
                    $("#length").removeClass("invalid").addClass("valid");
                    score++
                } else {
                    $("#length").removeClass("valid").addClass("invalid");
                }
                if (numericCount >= minNumSValue) {
                    $("#pnum").removeClass("invalid").addClass("valid");
                    score++

                } else {
                    $("#pnum").removeClass("valid").addClass("invalid");
                }
                if (uppercaseCount >= minUpSValue && lowercaseCount >= minLowSValue) {

                    $("#capital").removeClass("invalid").addClass("valid");
                    score++

                } else {
                    $("#capital").removeClass("valid").addClass("invalid");
                }

                if (specialCount >= minSpclSValue) {
                    $("#spchar").removeClass("invalid").addClass("valid");
                    score++

                } else {
                    $("#spchar").removeClass("valid").addClass("invalid");
                }

                $("#passwordDescription").text("Password Strength: " + (score < desc.length ? desc[score] : "Best"));
                $("#passwordStrength").removeClass().addClass("strength" + score);
                $("input#changeNewPassword").on('blur', function () {
                    $("#mycPass_strength_wrap").fadeOut(400);
                    let value = this.value.replace(/\s+/g, '');
                    blurpassword(this.id, value);

                });
                $("input#changeConfirmPassword").on('focus keyup', function () {
                    $("#mycPass_strength_wrap").fadeOut(400);
                    focusconfirmpassword(this.id);
                });
            }
        } catch (error) {

        }
    }, 500));
});