﻿using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using Microsoft.AspNetCore.Mvc;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessFunction.Validators;

public class UpdateBusinessFunctionValidatorTests
{
    private readonly Mock<IBusinessFunctionRepository> _mockBusinessFunctionRepository;

    public UpdateBusinessFunctionValidatorTests()
    {
        var businessFunction = new Fixture().Create<List<Domain.Entities.BusinessFunction>>();

        _mockBusinessFunctionRepository = BusinessFunctionRepositoryMocks.UpdateBusinessFunctionRepository(businessFunction);
    }

    //Name

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Name_WithEmpty(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Name = "";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Name_IsNull(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Name = null;
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNotNullRequired, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Name_MiniMumRange(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Name = "AR";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameRangeRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Name_MaxiMumRange(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Name = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionDescriptionContains, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Name_Valid(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Name = "   PTS  ";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Valid_Name_With_DoubleSpace_InBetween(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Name = "PTS  Technology";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }
    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Valid_Name_With_TripleSpace_InBetween(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Name = "PTS Technology   India  ";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }
    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Valid_Name_With_UnderScore_InFront(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Name = "_PTSTechnology";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Valid_Name_With_UnderScore_InFront_AndBack(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Name = "_PTSTechnology_";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Valid_Name_With_Numbers_InFront(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Name = "123PTSTechnology";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Valid_Name_With_UnderScore_InFront_AndBack_With_Numbers_InFront(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Name = "_123PTSTechnology_";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Valid_Name_With_UnderScore_InFront_With_Numbers_InBack(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Name = "_PTSTechnology123";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }


    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Valid_Name_Valid_With_NumbersOnly(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Name = "0123456789";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Valid_Name_Valid_With_SpecialCharacters_Only(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Name = "!@#$%^&*<>:{}";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }


    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Valid_Name_Valid_With_SpecialCharacters_InFront(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Name = "!@PRT";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    //Description

    //[Theory]
    // [AutoBusinessFunctionData]
    // public async Task Verify_Update_Description_InBusinessFunction_WithEmpty(UpdateBusinessFunctionCommand businessFunctionCommand)
    // {
    //     var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

    //     businessFunctionCommand.Description = "";

    //     var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
    //     Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionDescriptionRequired, validateResult.Errors[1].ErrorMessage);
    // }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Description_IsNull(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Description = null;
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_Description_MaxiMumRange(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.Description = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionDescriptionMaximum, validateResult.Errors[1].ErrorMessage);
    }

    //CriticalityLevel

    //[Theory]
    //[AutoBusinessFunctionData]
    //public async Task Verify_Update_CriticalityLevel_InBusinessFunction_WithEmpty(UpdateBusinessFunctionCommand businessFunctionCommand)
    //{
    //    var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

    //    businessFunctionCommand.CriticalityLevel = "";
    //    businessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";

    //    var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionCriticalityLevelRequired, validateResult.Errors[1].ErrorMessage);
    //}

    //[Theory]
    //[AutoBusinessFunctionData]
    //public async Task Verify_Update_CriticalityLevel_InBusinessFunction_IsNull(UpdateBusinessFunctionCommand businessFunctionCommand)
    //{
    //    var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

    //    businessFunctionCommand.CriticalityLevel = null;
    //    businessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";

    //    var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionCriticalityLevelNotNullRequired, validateResult.Errors[2].ErrorMessage);
    //}

    //[Theory]
    //[AutoBusinessFunctionData]
    //public async Task Verify_Update_CriticalityLevel_InBusinessFunction_AlphabetsOnly(UpdateBusinessFunctionCommand businessFunctionCommand)
    //{
    //    var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

    //    businessFunctionCommand.CriticalityLevel = "AW123";
    //    businessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";

    //    var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionCriticalityLevelAlphabetsOnlyRequired, validateResult.Errors[1].ErrorMessage);
    //}

    //BusinessServiceName

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_BusinessServiceName_WithEmpty(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.BusinessServiceName = "";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionDescriptionContains, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_BusinessServiceName_IsNull(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.BusinessServiceName = null;
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionBusinessServiceNameRequired, validateResult.Errors[2].ErrorMessage);
    }

    //ConfiguredRTO

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_ConfiguredRTO_WithEmpty(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.ConfiguredRTO = "";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredRtoRequired, validateResult.Errors[2].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_ConfiguredRTO_WithNumberOnly(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.ConfiguredRTO = "ABCD";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredRtoNumbersOnlyRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_ConfiguredRTO_IsNull(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.ConfiguredRTO = null;
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredRtoNotNullRequired, validateResult.Errors[3].ErrorMessage);
    }

    //ConfiguredRPO

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_ConfiguredRPO_WithEmpty(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.ConfiguredRPO = "";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRTO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        await Assert.ThrowsAsync<FormatException>(async () => 
             await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None)
          );
        //var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        //Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredRpoNumbersOnlyRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_ConfiguredRPO_WithNumberOnly(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.ConfiguredRPO = "ABCD";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRTO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        await Assert.ThrowsAsync<FormatException>(async () =>
            await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None)
         );

        //var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        //Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredRpoNumbersOnlyRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_ConfiguredRPO_IsNull(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.ConfiguredRPO = null;
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRTO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        await Assert.ThrowsAsync<FormatException>(async () =>
            await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None)
         );

        //var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        //Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredRpoNotNullRequired, validateResult.Errors[4].ErrorMessage);
    }

    //ConfiguredMAO

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_ConfiguredMAO_WithEmpty(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.ConfiguredMAO = "";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredMaoRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_ConfiguredMAO_WithNumberOnly(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.ConfiguredMAO = "ABCD";
        businessFunctionCommand.BusinessServiceId = Guid.NewGuid().ToString();
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredRpoNumbersOnlyRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_UpdateBusinessFunctionCommandValidator_ConfiguredMAO_IsNull(UpdateBusinessFunctionCommand businessFunctionCommand)
    {
        var validator = new UpdateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        businessFunctionCommand.ConfiguredMAO = null;
        businessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        businessFunctionCommand.Id = Guid.NewGuid().ToString();
        businessFunctionCommand.ConfiguredRPO = "100";
        businessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(businessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredMaoNotNullRequired, validateResult.Errors[4].ErrorMessage);
    }
}