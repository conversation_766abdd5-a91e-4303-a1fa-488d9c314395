
QUnit.module("TableAccess.js Full Unit Tests", hooks => {
    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <div id="AdminCreate" data-create-permission="true"></div>
            <div id="tableaccess_body"></div>
            <input type="text" id="search-inp" />
            <div id="data_notfound" style="display:none;">No data</div>
            <input type="checkbox" id="flexSwitchCheckChecked" />
            <button id="tableAccessSaveButton"></button>
            <button id="tableAccessCancelButton"></button>
        `);

        // Set dummy AJAX root URL
        window.RootUrl = "/";
    });

    QUnit.test("Initial permissions and DOM state", assert => {
        let createPermission = $('#AdminCreate').data('create-permission');
        assert.strictEqual(createPermission, true, "okey");
    });

    QUnit.test("Search input keypress prevention", assert => {
        const e = $.Event('keypress', { key: '=' });
        let prevented = false;
        $('#search-inp').on('keypress', function (e) {
            if (e.key === '=' || e.key === 'Enter') {
                prevented = true;
                e.preventDefault();
            }
        });
        $('#search-inp').trigger(e);
        assert.ok(prevented, "'=' keypress was prevented");
    });

    QUnit.test("Toggle checkboxes and sync master checkbox", assert => {
        $('#qunit-fixture').append(`
            <input type="checkbox" class="form-check-input isTableChecked" id="row1" data-tablename="Test1" data-schemaname="Schema1" data-configured="false">
            <input type="checkbox" class="form-check-input isTableChecked" id="row2" data-tablename="Test2" data-schemaname="Schema2" data-configured="false">
        `);

        $('#row1').prop('checked', true).trigger('change');
        $('#row2').prop('checked', true).trigger('change');

        let allChecked = $('.isTableChecked').length === $('.isTableChecked:checked').length;
        $('#flexSwitchCheckChecked').prop('checked', allChecked);

        assert.ok(allChecked, "All checkboxes are checked and master checkbox synced");
    });

    QUnit.test("Save and cancel buttons enabled on change", assert => {
        $('#qunit-fixture').append(`
            <input type="checkbox" class="form-check-input isTableChecked" id="row3" data-tablename="T3" data-schemaname="S3" data-configured="false">
        `);
        $('#row3').prop('checked', true).trigger('change');
        assert.notOk($('#tableAccessSaveButton').hasClass('disabled'), "Save button is enabled");
        assert.notOk($('#tableAccessCancelButton').hasClass('disabled'), "Cancel button is enabled");
    });
});
