{"Version": 1, "WorkspaceRootPath": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\heatmapstatusviewrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\heatmapstatusviewrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\infraobjectfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\infraobjectfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\databasefixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\databasefixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D68A3FFB-172E-4E39-A101-B47235C31F8C}|Tests\\Shared\\ContinuityPatrol.Shared.Tests\\ContinuityPatrol.Shared.Tests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\shared\\continuitypatrol.shared.tests\\infrastructure\\dbcontextfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D68A3FFB-172E-4E39-A101-B47235C31F8C}|Tests\\Shared\\ContinuityPatrol.Shared.Tests\\ContinuityPatrol.Shared.Tests.csproj|solutionrelative:tests\\shared\\continuitypatrol.shared.tests\\infrastructure\\dbcontextfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\infradashboardviewrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\infradashboardviewrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\infrastructure\\continuitypatrol.persistence\\repositories\\infradashboardviewrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|solutionrelative:infrastructure\\continuitypatrol.persistence\\repositories\\infradashboardviewrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\infrastructure\\continuitypatrol.persistence\\repositories\\baserepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|solutionrelative:infrastructure\\continuitypatrol.persistence\\repositories\\baserepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\core\\continuitypatrol.application\\helper\\getjsonproperties.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\helper\\getjsonproperties.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\impactavailabilityrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\impactavailabilityrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\driftprofilerepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\driftprofilerepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\db2hadrmonitorlogrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\db2hadrmonitorlogrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\datasyncmonitorlogsrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\datasyncmonitorlogsrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\databaseviewrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\databaseviewrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowprofilerepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowprofilerepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowprofileinforepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowprofileinforepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowprofileinfoviewrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowprofileinfoviewrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\workflowprofileinfofixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\workflowprofileinfofixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowpredictionrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowpredictionrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\workflowoperationfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\workflowoperationfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowpermissionrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowpermissionrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowoperationrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowoperationrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\infrastructure\\continuitypatrol.persistence\\repositories\\workflowoperationrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|solutionrelative:infrastructure\\continuitypatrol.persistence\\repositories\\workflowoperationrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowoperationgrouprepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowoperationgrouprepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\databaserepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\databaserepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowhistoryrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowhistoryrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowinfraobjectrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowinfraobjectrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\workflowinfraobjectfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\workflowinfraobjectfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\workflowhistoryfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\workflowhistoryfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowactiontyperepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowactiontyperepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\robocopyrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\robocopyrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowexecutiontemprepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowexecutiontemprepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowactionrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowactionrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowdrcalenderrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowdrcalenderrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowcategoryviewrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowcategoryviewrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\workflowcategoryviewfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\workflowcategoryviewfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\workflowexecutioneventlogfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\workflowexecutioneventlogfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowexecutioneventlogrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowexecutioneventlogrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowcategoryrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowcategoryrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowactionresultrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowactionresultrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\workflowactionresultfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\fixtures\\workflowactionresultfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\rpforvmmonitorstatusrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\rpforvmmonitorstatusrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\menubuilderrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\menubuilderrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\infraobjectrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\infraobjectrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowactionfieldmasterrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\workflowactionfieldmasterrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\approvalmatrixapprovalrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\approvalmatrixapprovalrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\alertinformationrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\alertinformationrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\rsyncmonitorstatusrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\rsyncmonitorstatusrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\aboutcprepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\aboutcprepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\mssqlmonitorlogsrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\mssqlmonitorlogsrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\infrastructure\\continuitypatrol.persistence\\repositories\\mssqlmonitorlogsrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|solutionrelative:infrastructure\\continuitypatrol.persistence\\repositories\\mssqlmonitorlogsrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\activedirectorymonitorlogrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\activedirectorymonitorlogrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\infrastructure\\continuitypatrol.persistence\\repositories\\infraobjectrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|solutionrelative:infrastructure\\continuitypatrol.persistence\\repositories\\infraobjectrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\cyberjobmanagementlogsrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC}|Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\ContinuityPatrol.Persistence.UnitTests.csproj|solutionrelative:tests\\infrastructure\\continuitypatrol.persistence.unittests\\repository\\cyberjobmanagementlogsrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\core\\continuitypatrol.application\\helper\\bulkimportjob.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\helper\\bulkimportjob.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\infrastructure\\continuitypatrol.persistence\\persistence\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|solutionrelative:infrastructure\\continuitypatrol.persistence\\persistence\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\core\\continuitypatrol.application\\specifications\\backupfilterspecification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\specifications\\backupfilterspecification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\core\\continuitypatrol.application\\contexts\\iconfigurationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\contexts\\iconfigurationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\83ca34cb13bbd5a50ea5bb752efe71db81c206cb5782a71e948bca9281f88b83\\ExceptionDispatchInfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\infrastructure\\continuitypatrol.persistence\\persistence\\configurationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|solutionrelative:infrastructure\\continuitypatrol.persistence\\persistence\\configurationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D68A3FFB-172E-4E39-A101-B47235C31F8C}|Tests\\Shared\\ContinuityPatrol.Shared.Tests\\ContinuityPatrol.Shared.Tests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\shared\\continuitypatrol.shared.tests\\mocks\\licensemanagermodelrepositorymocks.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D68A3FFB-172E-4E39-A101-B47235C31F8C}|Tests\\Shared\\ContinuityPatrol.Shared.Tests\\ContinuityPatrol.Shared.Tests.csproj|solutionrelative:tests\\shared\\continuitypatrol.shared.tests\\mocks\\licensemanagermodelrepositorymocks.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\tests\\core\\continuitypatrol.application.unittests\\mocks\\databaseviewrepositorymocks.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24AEB7A6-D6D7-4AFC-B550-C458954858C7}|Tests\\Core\\ContinuityPatrol.Application.UnitTests\\ContinuityPatrol.Application.UnitTests.csproj|solutionrelative:tests\\core\\continuitypatrol.application.unittests\\mocks\\databaseviewrepositorymocks.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E9CE10A8-B35D-497D-ABBD-F42D30BEBE69}|Shared\\ContinuityPatrol.Shared.Infrastructure\\ContinuityPatrol.Shared.Infrastructure.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\shared\\continuitypatrol.shared.infrastructure\\extensions\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E9CE10A8-B35D-497D-ABBD-F42D30BEBE69}|Shared\\ContinuityPatrol.Shared.Infrastructure\\ContinuityPatrol.Shared.Infrastructure.csproj|solutionrelative:shared\\continuitypatrol.shared.infrastructure\\extensions\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\infrastructure\\continuitypatrol.persistence\\extensions\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|solutionrelative:infrastructure\\continuitypatrol.persistence\\extensions\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2950A847-AFC7-4861-9515-F23C46CCE376}|Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\shared\\continuitypatrol.shared.core\\contracts\\persistence\\irepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2950A847-AFC7-4861-9515-F23C46CCE376}|Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj|solutionrelative:shared\\continuitypatrol.shared.core\\contracts\\persistence\\irepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\core\\continuitypatrol.application\\extensions\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\extensions\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\infrastructure\\continuitypatrol.persistence\\repositories\\alertreceiverrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|solutionrelative:infrastructure\\continuitypatrol.persistence\\repositories\\alertreceiverrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\infrastructure\\continuitypatrol.persistence\\persistence\\admindbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{742621A6-BB10-4DD2-9696-AFDBF99ABDBE}|Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj|solutionrelative:infrastructure\\continuitypatrol.persistence\\persistence\\admindbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\dc2a890e90ac087b42e9e56a38447974463bbcdae8d976c13656a86d10043618\\ServiceProviderServiceExtensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\core\\continuitypatrol.application\\features\\bulkimport\\commands\\create\\createbulkimportcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\bulkimport\\commands\\create\\createbulkimportcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|e:\\cp6 root copy\\cp6-.net 9 root\\ui\\core\\continuitypatrol.application\\features\\bulkimport\\commands\\next\\nextbulkimportcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\bulkimport\\commands\\next\\nextbulkimportcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 6, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "DatabaseFixture.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\DatabaseFixture.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\DatabaseFixture.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\DatabaseFixture.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\DatabaseFixture.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAwwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T13:53:21.734Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "DbContextFactory.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Infrastructure\\DbContextFactory.cs", "RelativeDocumentMoniker": "Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Infrastructure\\DbContextFactory.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Infrastructure\\DbContextFactory.cs", "RelativeToolTip": "Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Infrastructure\\DbContextFactory.cs", "ViewState": "AgIAAGMAAAAAAAAAAAAawGkAAABhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T12:25:23.573Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "InfraObjectFixture.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\InfraObjectFixture.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\InfraObjectFixture.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\InfraObjectFixture.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\InfraObjectFixture.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAWwAoAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T12:23:24.911Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "InfraDashboardViewRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\InfraDashboardViewRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\InfraDashboardViewRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\InfraDashboardViewRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\InfraDashboardViewRepositoryTests.cs", "ViewState": "AgIAAE8AAAAAAAAAAAAWwFIAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T12:04:55.823Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "InfraDashboardViewRepository.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\InfraDashboardViewRepository.cs", "RelativeDocumentMoniker": "Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\InfraDashboardViewRepository.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\InfraDashboardViewRepository.cs", "RelativeToolTip": "Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\InfraDashboardViewRepository.cs", "ViewState": "AgIAACcAAAAAAAAAAAAgwCcAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T12:03:50.328Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "GetJsonProperties.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Core\\ContinuityPatrol.Application\\Helper\\GetJsonProperties.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Helper\\GetJsonProperties.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Core\\ContinuityPatrol.Application\\Helper\\GetJsonProperties.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Helper\\GetJsonProperties.cs", "ViewState": "AgIAAP0BAAAAAAAAAAAIwAUCAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T12:01:11.759Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "HeatMapStatusViewRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\HeatMapStatusViewRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\HeatMapStatusViewRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\HeatMapStatusViewRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\HeatMapStatusViewRepositoryTests.cs", "ViewState": "AgIAAFUDAAAAAAAAAAAAAFsDAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T11:00:00.152Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "BaseRepository.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\BaseRepository.cs", "RelativeDocumentMoniker": "Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\BaseRepository.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\BaseRepository.cs", "RelativeToolTip": "Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\BaseRepository.cs", "ViewState": "AgIAACkAAAAAAAAAAAAuwC8AAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T07:22:43.537Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "WorkflowProfileRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowProfileRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowProfileRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowProfileRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowProfileRepositoryTests.cs", "ViewState": "AgIAAGIAAAAAAAAAAAAowGUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T10:34:06.398Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "WorkflowProfileInfoRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowProfileInfoRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowProfileInfoRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowProfileInfoRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowProfileInfoRepositoryTests.cs", "ViewState": "AgIAAJUAAAAAAAAAAAAowKgAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T09:40:32.592Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "WorkflowPredictionRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowPredictionRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowPredictionRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowPredictionRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowPredictionRepositoryTests.cs", "ViewState": "AgIAACcAAAAAAAAAAAAmwDEAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T09:39:44.484Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "ImpactAvailabilityRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\ImpactAvailabilityRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\ImpactAvailabilityRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\ImpactAvailabilityRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\ImpactAvailabilityRepositoryTests.cs", "ViewState": "AgIAAAIBAAAAAAAAAADwv/kAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T05:32:37.07Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "DataSyncMonitorLogsRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DataSyncMonitorLogsRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DataSyncMonitorLogsRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DataSyncMonitorLogsRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DataSyncMonitorLogsRepositoryTests.cs", "ViewState": "AgIAALQAAAAAAAAAAADwvy8BAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T10:52:40.319Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "DriftProfileRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DriftProfileRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DriftProfileRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DriftProfileRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DriftProfileRepositoryTests.cs", "ViewState": "AgIAAOYAAAAAAAAAAAAawOkAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T10:54:05.66Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "WorkflowOperationFixture.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowOperationFixture.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowOperationFixture.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowOperationFixture.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowOperationFixture.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAawA0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:50:36.218Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "DB2HADRMonitorLogRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DB2HADRMonitorLogRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DB2HADRMonitorLogRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DB2HADRMonitorLogRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DB2HADRMonitorLogRepositoryTests.cs", "ViewState": "AgIAADwBAAAAAAAAAAAlwEIBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T05:14:17.192Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "WorkflowProfileInfoViewRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowProfileInfoViewRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowProfileInfoViewRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowProfileInfoViewRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowProfileInfoViewRepositoryTests.cs", "ViewState": "AgIAAF0AAAAAAAAAAAAQwF8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T09:40:48.598Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "DatabaseViewRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DatabaseViewRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DatabaseViewRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DatabaseViewRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DatabaseViewRepositoryTests.cs", "ViewState": "AgIAAEMAAAAAAAAAAAAawEkAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T07:26:17.446Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "WorkflowProfileInfoFixture.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowProfileInfoFixture.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowProfileInfoFixture.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowProfileInfoFixture.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowProfileInfoFixture.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T09:48:07.878Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "WorkflowPermissionRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowPermissionRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowPermissionRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowPermissionRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowPermissionRepositoryTests.cs", "ViewState": "AgIAABgAAAAAAAAAAAA5wBUAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:46:37.12Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "WorkflowOperationRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowOperationRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowOperationRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowOperationRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowOperationRepositoryTests.cs", "ViewState": "AgIAAK0AAAAAAAAAAAAawLIAAABXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:46:12.249Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "WorkflowOperationGroupRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowOperationGroupRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowOperationGroupRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowOperationGroupRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowOperationGroupRepositoryTests.cs", "ViewState": "AgIAAB4BAAAAAAAAAADwvyUBAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:45:43.605Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "WorkflowOperationRepository.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\WorkflowOperationRepository.cs", "RelativeDocumentMoniker": "Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\WorkflowOperationRepository.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\WorkflowOperationRepository.cs", "RelativeToolTip": "Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\WorkflowOperationRepository.cs", "ViewState": "AgIAABMBAAAAAAAAAAAEwBgBAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T05:14:15.52Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "WorkflowHistoryRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowHistoryRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowHistoryRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowHistoryRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowHistoryRepositoryTests.cs", "ViewState": "AgIAAOMAAAAAAAAAAAAUwOYAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:04:29.49Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "DatabaseRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DatabaseRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DatabaseRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DatabaseRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\DatabaseRepositoryTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAWwAwAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T05:14:17.145Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "WorkflowHistoryFixture.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowHistoryFixture.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowHistoryFixture.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowHistoryFixture.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowHistoryFixture.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAwwAwAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:09:20.385Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "WorkflowInfraObjectFixture.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowInfraObjectFixture.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowInfraObjectFixture.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowInfraObjectFixture.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowInfraObjectFixture.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAowBwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:11:35.749Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "WorkflowInfraObjectRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowInfraObjectRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowInfraObjectRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowInfraObjectRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowInfraObjectRepositoryTests.cs", "ViewState": "AgIAABsBAAAAAAAAAAAUwCMBAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:04:55.739Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 28, "Title": "WorkflowActionTypeRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionTypeRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionTypeRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionTypeRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionTypeRepositoryTests.cs", "ViewState": "AgIAAGIAAAAAAAAAAAAAwGgAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T06:14:07.787Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "RoboCopyRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\RoboCopyRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\RoboCopyRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\RoboCopyRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\RoboCopyRepositoryTests.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:09:45.123Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "WorkflowExecutionTempRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowExecutionTempRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowExecutionTempRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowExecutionTempRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowExecutionTempRepositoryTests.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAQwBEAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:04:09.052Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "WorkflowExecutionEventLogRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowExecutionEventLogRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowExecutionEventLogRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowExecutionEventLogRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowExecutionEventLogRepositoryTests.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAtwBMAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T06:25:37.819Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "WorkflowDrCalenderRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowDrCalenderRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowDrCalenderRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowDrCalenderRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowDrCalenderRepositoryTests.cs", "ViewState": "AgIAAC4AAAAAAAAAAAAIwC8AAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T06:25:13.139Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 31, "Title": "WorkflowActionRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionRepositoryTests.cs", "ViewState": "AgIAAHMAAAAAAAAAAAAlwHkAAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T05:30:20.57Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 35, "Title": "WorkflowExecutionEventLogFixture.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowExecutionEventLogFixture.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowExecutionEventLogFixture.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowExecutionEventLogFixture.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowExecutionEventLogFixture.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T06:27:54.604Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "WorkflowCategoryViewFixture.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowCategoryViewFixture.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowCategoryViewFixture.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowCategoryViewFixture.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowCategoryViewFixture.cs", "ViewState": "AgIAABYAAAAAAAAAAAAAABYAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T06:46:13.539Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "WorkflowCategoryViewRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowCategoryViewRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowCategoryViewRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowCategoryViewRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowCategoryViewRepositoryTests.cs", "ViewState": "AgIAACAAAAAAAAAAAAAIwCEAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T06:24:48.516Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "WorkflowCategoryRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowCategoryRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowCategoryRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowCategoryRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowCategoryRepositoryTests.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAIwAsAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T06:14:25.872Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "WorkflowActionResultFixture.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowActionResultFixture.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowActionResultFixture.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowActionResultFixture.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Fixtures\\WorkflowActionResultFixture.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAwwBEAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T05:39:46.342Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "RpForVmMonitorStatusRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\RpForVmMonitorStatusRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\RpForVmMonitorStatusRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\RpForVmMonitorStatusRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\RpForVmMonitorStatusRepositoryTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T05:32:33.024Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "WorkflowActionResultRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionResultRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionResultRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionResultRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionResultRepositoryTests.cs", "ViewState": "AgIAAI0AAAAAAAAAAAAIwI4AAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T05:30:42.868Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "MenuBuilderRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\MenuBuilderRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\MenuBuilderRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\MenuBuilderRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\MenuBuilderRepositoryTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T05:32:41.499Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "InfraObjectRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\InfraObjectRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\InfraObjectRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\InfraObjectRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\InfraObjectRepositoryTests.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAxwIcHAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T05:14:17.426Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 44, "Title": "ApprovalMatrixApprovalRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\ApprovalMatrixApprovalRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\ApprovalMatrixApprovalRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\ApprovalMatrixApprovalRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\ApprovalMatrixApprovalRepositoryTests.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAABcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T05:13:56.868Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "AlertInformationRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\AlertInformationRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\AlertInformationRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\AlertInformationRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\AlertInformationRepositoryTests.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAABUAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T05:13:44.823Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "WorkflowActionFieldMasterRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionFieldMasterRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionFieldMasterRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionFieldMasterRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\WorkflowActionFieldMasterRepositoryTests.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAcAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T05:11:18.798Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "RsyncMonitorStatusRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\RsyncMonitorStatusRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\RsyncMonitorStatusRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\RsyncMonitorStatusRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\RsyncMonitorStatusRepositoryTests.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T05:11:58.897Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "MSSQLMonitorLogsRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\MSSQLMonitorLogsRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\MSSQLMonitorLogsRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\MSSQLMonitorLogsRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\MSSQLMonitorLogsRepositoryTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T13:09:47.205Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 50, "Title": "ActiveDirectoryMonitorLogRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\ActiveDirectoryMonitorLogRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\ActiveDirectoryMonitorLogRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\ActiveDirectoryMonitorLogRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\ActiveDirectoryMonitorLogRepositoryTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T05:14:16.692Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 49, "Title": "MSSQLMonitorLogsRepository.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\MSSQLMonitorLogsRepository.cs", "RelativeDocumentMoniker": "Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\MSSQLMonitorLogsRepository.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\MSSQLMonitorLogsRepository.cs", "RelativeToolTip": "Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\MSSQLMonitorLogsRepository.cs", "ViewState": "AgIAAEUAAAAAAAAAAAAqwFEAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T12:10:40.585Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 47, "Title": "AboutCPRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\AboutCPRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\AboutCPRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\AboutCPRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\AboutCPRepositoryTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T05:14:16.629Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 52, "Title": "CyberJobManagementLogsRepositoryTests.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\CyberJobManagementLogsRepositoryTests.cs", "RelativeDocumentMoniker": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\CyberJobManagementLogsRepositoryTests.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\CyberJobManagementLogsRepositoryTests.cs", "RelativeToolTip": "Tests\\Infrastructure\\ContinuityPatrol.Persistence.UnitTests\\Repository\\CyberJobManagementLogsRepositoryTests.cs", "ViewState": "AgIAAE8AAAAAAAAAAAAAAE4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:20:14.647Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "InfraObjectRepository.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\InfraObjectRepository.cs", "RelativeDocumentMoniker": "Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\InfraObjectRepository.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\InfraObjectRepository.cs", "RelativeToolTip": "Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\InfraObjectRepository.cs", "ViewState": "AgIAAEYBAAAAAAAAAAAAAEABAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T05:14:17.457Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 54, "Title": "ApplicationDbContext.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Persistence\\ApplicationDbContext.cs", "RelativeDocumentMoniker": "Infrastructure\\ContinuityPatrol.Persistence\\Persistence\\ApplicationDbContext.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Persistence\\ApplicationDbContext.cs", "RelativeToolTip": "Infrastructure\\ContinuityPatrol.Persistence\\Persistence\\ApplicationDbContext.cs", "ViewState": "AgIAAPQBAAAAAAAAAAAiwP0BAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T09:19:24.87Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "BulkImportJob.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Core\\ContinuityPatrol.Application\\Helper\\BulkImportJob.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Helper\\BulkImportJob.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Core\\ContinuityPatrol.Application\\Helper\\BulkImportJob.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Helper\\BulkImportJob.cs", "ViewState": "AgIAAD8DAAAAAAAAAAA1wCkDAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T05:18:49.797Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 55, "Title": "BackUpFilterSpecification.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Core\\ContinuityPatrol.Application\\Specifications\\BackUpFilterSpecification.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Specifications\\BackUpFilterSpecification.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Core\\ContinuityPatrol.Application\\Specifications\\BackUpFilterSpecification.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Specifications\\BackUpFilterSpecification.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAqwAwAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T10:56:09.375Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "ExceptionDispatchInfo.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\83ca34cb13bbd5a50ea5bb752efe71db81c206cb5782a71e948bca9281f88b83\\ExceptionDispatchInfo.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\83ca34cb13bbd5a50ea5bb752efe71db81c206cb5782a71e948bca9281f88b83\\ExceptionDispatchInfo.cs [Read Only]", "ViewState": "AgIAAA4AAAAAAAAAAADwvx4AAAACAAAAAQAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T12:38:00.751Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 56, "Title": "IConfigurationDbContext.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Core\\ContinuityPatrol.Application\\Contexts\\IConfigurationDbContext.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Contexts\\IConfigurationDbContext.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Core\\ContinuityPatrol.Application\\Contexts\\IConfigurationDbContext.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Contexts\\IConfigurationDbContext.cs", "ViewState": "AgIAACUAAAAAAAAAAAAAACcAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T11:43:32.951Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "ConfigurationDbContext.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Persistence\\ConfigurationDbContext.cs", "RelativeDocumentMoniker": "Infrastructure\\ContinuityPatrol.Persistence\\Persistence\\ConfigurationDbContext.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Persistence\\ConfigurationDbContext.cs", "RelativeToolTip": "Infrastructure\\ContinuityPatrol.Persistence\\Persistence\\ConfigurationDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T09:24:56.114Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "LicenseManagerModelRepositoryMocks.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Mocks\\LicenseManagerModelRepositoryMocks.cs", "RelativeDocumentMoniker": "Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Mocks\\LicenseManagerModelRepositoryMocks.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Mocks\\LicenseManagerModelRepositoryMocks.cs", "RelativeToolTip": "Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Mocks\\LicenseManagerModelRepositoryMocks.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAAwAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T07:44:43.188Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "DatabaseViewRepositoryMocks.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Mocks\\DatabaseViewRepositoryMocks.cs", "RelativeDocumentMoniker": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Mocks\\DatabaseViewRepositoryMocks.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Mocks\\DatabaseViewRepositoryMocks.cs", "RelativeToolTip": "Tests\\Core\\ContinuityPatrol.Application.UnitTests\\Mocks\\DatabaseViewRepositoryMocks.cs", "ViewState": "AgIAABUAAAAAAAAAAAAowBkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T07:46:36.704Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "ServiceCollectionExtensions.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Shared\\ContinuityPatrol.Shared.Infrastructure\\Extensions\\ServiceCollectionExtensions.cs", "RelativeDocumentMoniker": "Shared\\ContinuityPatrol.Shared.Infrastructure\\Extensions\\ServiceCollectionExtensions.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Shared\\ContinuityPatrol.Shared.Infrastructure\\Extensions\\ServiceCollectionExtensions.cs", "RelativeToolTip": "Shared\\ContinuityPatrol.Shared.Infrastructure\\Extensions\\ServiceCollectionExtensions.cs", "ViewState": "AgIAAKgAAAAAAAAAAAAAALEAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T11:34:40.233Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "ServiceCollectionExtensions.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Extensions\\ServiceCollectionExtensions.cs", "RelativeDocumentMoniker": "Infrastructure\\ContinuityPatrol.Persistence\\Extensions\\ServiceCollectionExtensions.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Extensions\\ServiceCollectionExtensions.cs", "RelativeToolTip": "Infrastructure\\ContinuityPatrol.Persistence\\Extensions\\ServiceCollectionExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T11:01:36.464Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "IRepository.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Shared\\ContinuityPatrol.Shared.Core\\Contracts\\Persistence\\IRepository.cs", "RelativeDocumentMoniker": "Shared\\ContinuityPatrol.Shared.Core\\Contracts\\Persistence\\IRepository.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Shared\\ContinuityPatrol.Shared.Core\\Contracts\\Persistence\\IRepository.cs", "RelativeToolTip": "Shared\\ContinuityPatrol.Shared.Core\\Contracts\\Persistence\\IRepository.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAvwBIAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T10:37:22.382Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "ServiceCollectionExtensions.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Core\\ContinuityPatrol.Application\\Extensions\\ServiceCollectionExtensions.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Extensions\\ServiceCollectionExtensions.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Core\\ContinuityPatrol.Application\\Extensions\\ServiceCollectionExtensions.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Extensions\\ServiceCollectionExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T11:33:44.449Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "AlertReceiverRepository.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\AlertReceiverRepository.cs", "RelativeDocumentMoniker": "Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\AlertReceiverRepository.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\AlertReceiverRepository.cs", "RelativeToolTip": "Infrastructure\\ContinuityPatrol.Persistence\\Repositories\\AlertReceiverRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAABeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T11:21:00.297Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "AdminDbContext.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Persistence\\AdminDbContext.cs", "RelativeDocumentMoniker": "Infrastructure\\ContinuityPatrol.Persistence\\Persistence\\AdminDbContext.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Infrastructure\\ContinuityPatrol.Persistence\\Persistence\\AdminDbContext.cs", "RelativeToolTip": "Infrastructure\\ContinuityPatrol.Persistence\\Persistence\\AdminDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T11:19:41.486Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "ServiceProviderServiceExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\dc2a890e90ac087b42e9e56a38447974463bbcdae8d976c13656a86d10043618\\ServiceProviderServiceExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\dc2a890e90ac087b42e9e56a38447974463bbcdae8d976c13656a86d10043618\\ServiceProviderServiceExtensions.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAIwBAAAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T09:40:34.731Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "CreateBulkImportCommandHandler.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Core\\ContinuityPatrol.Application\\Features\\BulkImport\\Commands\\Create\\CreateBulkImportCommandHandler.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\BulkImport\\Commands\\Create\\CreateBulkImportCommandHandler.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Core\\ContinuityPatrol.Application\\Features\\BulkImport\\Commands\\Create\\CreateBulkImportCommandHandler.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\BulkImport\\Commands\\Create\\CreateBulkImportCommandHandler.cs", "ViewState": "AgIAADMAAAAAAAAAAAAowD0AAABSAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T05:18:21.282Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "NextBulkImportCommandHandler.cs", "DocumentMoniker": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Core\\ContinuityPatrol.Application\\Features\\BulkImport\\Commands\\Next\\NextBulkImportCommandHandler.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\BulkImport\\Commands\\Next\\NextBulkImportCommandHandler.cs", "ToolTip": "E:\\CP6 Root Copy\\CP6-.net 9 Root\\UI\\Core\\ContinuityPatrol.Application\\Features\\BulkImport\\Commands\\Next\\NextBulkImportCommandHandler.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\BulkImport\\Commands\\Next\\NextBulkImportCommandHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAABdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T05:18:16.339Z"}]}]}]}