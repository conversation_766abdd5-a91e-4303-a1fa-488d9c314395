﻿using ContinuityPatrol.Application.Features.Report.Event.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Report.Events;

public class ReportDeletedEventHandlerTests : IClassFixture<ReportFixture>
{
    private readonly ReportFixture _reportFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly ReportDeletedEventHandler _handler;

    public ReportDeletedEventHandlerTests(ReportFixture reportFixture)
    {
        _reportFixture = reportFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockReportEventLogger = new Mock<ILogger<ReportDeletedEventHandler>>();

        _mockUserActivityRepository = ReportRepositoryMocks.CreateReportEventRepository(_reportFixture.UserActivities);

        _handler = new ReportDeletedEventHandler(mockLoggedInUserService.Object, mockReportEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteReportEventDeleted()
    {
        _reportFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_reportFixture.ReportDeletedEvent, CancellationToken.None);

        result.Equals(_reportFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_reportFixture.ReportDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_DeleteReportEventDeleted()
    {
        _reportFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_reportFixture.ReportDeletedEvent, CancellationToken.None);

        result.Equals(_reportFixture.UserActivities[0].Id);

        result.Equals(_reportFixture.ReportDeletedEvent.ReportName);

        await Task.CompletedTask;
    }
}