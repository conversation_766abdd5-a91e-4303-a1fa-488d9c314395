﻿let infraRoleReverseDetails = [];
let noDataImage = "<img src='../../img/isomatric/no_data_found.svg' class='Card_NoData_Img' style='width: 150px;margin-top:100px'>"

const disableSaveAsFields = () => {
    $("#findContainer, #replaceContainer, #positionContainer, #textContainer").hide();
    $("#saveAsparentCheck, #saveAsFindReplaceCheck").addClass('d-none');
}
disableSaveAsFields()

disableWorkflowTools(true)

const disableSortable = () => {
    $("#workflowActions").sortable({
        disabled: GlobalIsLock
    });
}

$("#saveAstype").on('change', function (e) {
    $('.newComponentHeader').addClass('d-none');
    $('.saveAsRestoreCopy').empty();
    if (e.target.value === "components") {
        $("#saveAsparentCheck, #saveAsFindReplaceCheck").removeClass('d-none').find('input').prop('checked', false)
        $("#findContainer, #replaceContainer, #positionContainer, #textContainer").hide();
       
        $(".saveAsRestoreComponents form").removeAttr("data-select2-id")
        $(".saveAsRestoreComponents").children().remove();
        $('.roleReverseContainer').empty();
        $('.newComponentHeader').removeClass('d-none');

        infraRoleReverseDetails = [];
        getUniqueFormInput(workFlowData, 'saveAsRestoreComponents')
    } else if (e.target.value === "rolereverse") {
        if ($('#workflowAttach').text() !== 'Attach') {        
            getUniqueFormInput(workFlowData, 'roleReverseContainer', 'roleReverse')   
            $("#saveAsComponents").empty();

            $("#saveAsparentCheck, #saveAsFindReplaceCheck").removeClass('d-none').find('input').prop('checked', false)
            $("#findContainer, #replaceContainer, #positionContainer, #textContainer").hide();
        } else {
            notificationAlert('warning', 'Attach infraobject for role reverse workflow')
            $("#saveAstype").val('duplicate')
            $("#saveAsComponents").empty();
            $("#findContainer, #replaceContainer, #saveAsAppendCheckBox, #saveAsAppendLabel").hide();
        }
    } else {
        $("#saveAsparentCheck, #saveAsFindReplaceCheck").addClass('d-none');
        $("#findContainer, #replaceContainer, #positionContainer, #textContainer").hide();

        restoreFormInput = []
        restoreFieldObj = []
        $("#saveAsComponents").empty();
    }
    $('#SaveAsWorkflowListModal').find('.modal-body').scrollTop(0);
});

//$('#SaveAsWorkflowListModal').on('shown.bs.modal', function () {
//    $(this).find('.modal-body').scrollTop(0); // Reset scroll position if necessary
//});

$("#saveAsAppendCheckBox, #generateAppendCheckBox").on('change', function (e) {
    const isSaveAs = e.target.id === 'saveAsAppendCheckBox';
    const containers = isSaveAs
        ? "#positionContainer, #textContainer"
        : "#positionGenerateContainer, #textGenerateContainer";

    $('#appendGenerateText-error, #appendText-error').text('').removeClass('field-validation-error')

    if (e.target.checked) {
        $(containers).removeClass('d-none').removeAttr('style');
    } else {
        $(containers).addClass('d-none').removeAttr('style')
    }
    
});

$('#saveAsFindReplaceCheckBox, #generateFindReplaceCheckBox').on('change', function (e) {

    const isSaveAs = e.target.id === 'saveAsFindReplaceCheckBox';
    const containers = isSaveAs
        ? "#findContainer, #replaceContainer"
        : "#generateFindAndReplaceContainer";

    $('#findValue-error, #replaceValue-error, #findGenerateValue-error, #replaceGenerateValue-error').text('').removeClass('field-validation-error')

    if (e.target.checked) {
        $(containers).removeClass('d-none').removeAttr('style');
    } else {
        $(containers).addClass('d-none').removeAttr('style')
    }
})



const SaveAsRoleReverse = async () => {
    let data = {}
    data.workflowId = GlobalWorkflowId
    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.GetInfraDetails,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                let infraDetails = result.data
                if (infraDetails[0].hasOwnProperty('infraObjectId')) {
                    getInfraObjectData(infraDetails[0])
                }
            } else {
                errorNotification(result)
            }
        }
    })
}

const getInfraObjectData = async (value) => {
    let infraData = {}
    infraData.infraObjectId = value.infraObjectId

    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.GetInfraObjectDetailsById,
        data: infraData,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                infraRoleReverseDetails.push(result.data)            
                roleReverseWorkflow()
                $('#SaveAsWorkflowListModal').modal('hide')
                $('#roleReverseConfirmationModal .roleReverseContainer .restoreTemplate').parent().parent().find('.field-validation-error').text('').removeClass('field-validation-error')
                $('#roleReverseConfirmationModal').modal('show')              
            } else {
                errorNotification(result)
            }
        }
    })
}

const roleReverseWorkflow = () => {
    let componentData = infraRoleReverseDetails[0]
    if (restoreFieldObj.length > 0) {
        restoreFieldObj.forEach((item) => {
            if (item?.optionRoleType !== "application") {
                if (item?.optionType === "PRDBServer") {
                    let drdbData = JSON.parse(componentData?.serverProperties);
                    if (drdbData?.DR?.id?.includes(',')) {
                        let splitData = drdbData?.DR?.id?.split(',')
                        $("#" + item?.id)[0]?.selectize?.setValue(splitData[0]);
                    } else {
                        $("#" + item?.id)[0]?.selectize?.setValue(drdbData?.DR?.id);

                    }                  
                } else if (item?.optionType === "DRDBServer") {
                    let prdbData = JSON.parse(componentData?.serverProperties);
                    if (prdbData?.PR?.id?.includes(',')) {
                        let splitData = prdbData?.PR?.id?.split(',')
                        $("#" + item?.id)[0]?.selectize?.setValue(splitData[0]);
                    } else {
                        $("#" + item?.id)[0]?.selectize?.setValue(prdbData?.PR?.id);
                    }
                } else if (item?.optionType === "PRDB") {
                    let drdbData = JSON.parse(componentData?.databaseProperties);
                    if (drdbData?.DR?.id?.includes(',')) {
                        let splitData = drdbData?.DR?.id?.split(',')
                        $("#" + item?.id)[0]?.selectize?.setValue(splitData[0]);
                    } else {
                        $("#" + item?.id)[0]?.selectize?.setValue(drdbData?.DR?.id);
                    }
                } else if (item?.optionType === "DRDB") {
                    let prdbData = JSON.parse(componentData?.databaseProperties);
                    if (prdbData?.PR?.id?.includes(',')) {
                        let splitData = prdbData?.PR?.id?.split(',')
                        $("#" + item?.id)[0]?.selectize?.setValue(splitData[0]);
                    } else {
                        $("#" + item?.id)[0]?.selectize?.setValue(prdbData?.PR?.id);
                    }
                }
            }
        })
    }   
}

async function validateAppendCheckBox(isSaveAs, findTextSelector, ReplaceTextSelector, appendTextSelector, findErrorSelector, replaceErrorSelector, appendErrorSelector) {
    let isValid = true;
    let appendTextVal = $(`#${appendTextSelector}`).val();
    let findValue = $(`#${findTextSelector}`).val();
    let replaceValue = $(`#${ReplaceTextSelector}`).val();
    let isAppendChecked = $(`#${isSaveAs ? 'saveAsAppendCheckBox' : 'generateAppendCheckBox'}`).prop("checked");
    let isFindReplaceChecked = $(`#${isSaveAs ? 'saveAsFindReplaceCheckBox' : 'generateFindReplaceCheckBox'}`).prop("checked");

    if (isAppendChecked) {
        if (appendTextVal == '') {
            actionSaveDataValidation($(`#${appendTextSelector}`).val(), appendErrorSelector, 'Enter text');
            isValid = false
        } else {
            isValid = partialValidation($(`#${appendTextSelector}`).val(), '', replaceErrorSelector, 'Enter replace value');
        }
        //if(findValue === '' && replaceValue === ''){
        //    [findErrorSelector, replaceErrorSelector].forEach(selector => {
        //        $(`#${selector}`).text('').removeClass('field-validation-error');
        //    });
        //}
    } 

    if (isFindReplaceChecked) {
        if (findValue === '' && replaceValue === '') {
            partialValidation($(`#${findTextSelector}`).val(), '', findErrorSelector, 'Enter find value');
            partialValidation($(`#${ReplaceTextSelector}`).val(), '', replaceErrorSelector, 'Enter replace value');
            isValid = false
        } else if (findValue !== '' && replaceValue === '') {
            partialValidation($(`#${ReplaceTextSelector}`).val(), '', replaceErrorSelector, 'Enter replace value');
            isValid = false;
        } else if (findValue === '' && replaceValue !== '') {
            partialValidation($(`#${findTextSelector}`).val(), '', findErrorSelector, 'Enter find value');
            isValid = false;
        } else if (findValue && replaceValue) {
            let frValidation = [];

            frValidation.push(partialValidation($(`#${findTextSelector}`).val(), '', findErrorSelector));
            frValidation.push(partialValidation($(`#${ReplaceTextSelector}`).val(), '', replaceErrorSelector));

            if (frValidation.includes(false)) isValid = false;
        } 
    }


    //let findAndReplaceChecked = $('#saveAsFindReplaceCheckBox').prop('checked')
    //if (findAndReplaceChecked && isSaveAs) {
    //    if (findValue === '' && replaceValue === '' && !isChecked) {
    //        actionSaveDataValidation($(`#${findErrorSelector.replace('-error', '')}`).val(), findErrorSelector, 'Enter find value');
    //        actionSaveDataValidation($(`#${replaceErrorSelector.replace('-error', '')}`).val(), replaceErrorSelector, 'Enter replace value');
    //        $(`#${appendErrorSelector}`).text('').removeClass('field-validation-error');
    //        isValid = false
    //    } else if ( findValue !== '' && replaceValue === '') {
    //        actionSaveDataValidation($(`#${replaceErrorSelector.replace('-error', '')}`).val(), replaceErrorSelector, 'Enter replace value');
    //        isValid = false;
    //    } else if (findValue === '' && replaceValue !== '') {
    //        actionSaveDataValidation($(`#${findErrorSelector.replace('-error', '')}`).val(), findErrorSelector, 'Enter find value');
    //        isValid = false;
    //    }
    //}
     
    return isValid;
}

async function saveAsInputValidation() {
    let formIsValid = true;
    const workflowSaveAsName = $('#saveAsWorkflowName').val();
    //const findValue = $('#findValue').val();
    //const replaceValue = $('#replaceValue').val();
    const saveAsType = $('#saveAstype').val();

    if (saveAsType === 'duplicate') {
        let saveInputValid = await WorkflowNameValidate(workflowSaveAsName, '', 'saveAsWorkflowName-error')
        formIsValid = saveInputValid ? true : false
    } else if (saveAsType === 'components' || saveAsType === 'rolereverse') {
        const saveInputValid = await WorkflowNameValidate(workflowSaveAsName, '', 'saveAsWorkflowName-error');
        formIsValid = saveInputValid ? true : false;

        if (!await validateAppendCheckBox(true, 'findValue', 'replaceValue', 'appendText', 'findValue-error', 'replaceValue-error', 'appendText-error')) {
            formIsValid = false;
        }
    }
    
    return formIsValid;
}

//async function generateSubmit() {

//    let formIsValid = true;
//    const findValue = $('#findGenerateValue').val();
//    const replaceValue = $('#replaceGenerateValue').val();

//    if (!await validateAppendCheckBox(false, findValue, replaceValue, 'appendGenerateText', 'findGenerateValue-error', 'replaceGenerateValue-error', 'appendGenerateText-error')) {
//        formIsValid = false;
//    }

//    return formIsValid;
//}

$("#btnSaveAsWorkflow").on('click', async function () {
    if ($('#saveAstype').val() === 'rolereverse' && await saveAsInputValidation()) {           
       await SaveAsRoleReverse()       
    } else {
       await saveAsConfirmation()
    }    
})

const replaceGenerateWorkflow = () => {

    let isNotMatched = false
    let isAppendChecked = $("#generateAppendCheckBox").prop("checked")
    let ConvertText = $('#findGenerateValue').val()

    if (ConvertText) { 
        templateNodes?.length && templateNodes.forEach(function (Details) {
            
            if (Details.hasOwnProperty('children')) {
                Details.children.forEach((obj) => {
                    if (!obj?.actionInfo?.actionName?.includes(ConvertText)) {
                        isNotMatched = true;
                        return false;
                    }
                })
            } else if (Details.hasOwnProperty('groupName')) {
                Details.groupActions.forEach((obj) => {
                    if (obj.hasOwnProperty('children')) {
                        obj.children.forEach((childObj) => {
                            if (!childObj?.actionInfo?.actionName?.includes(ConvertText)) {
                                isNotMatched = true
                                return false;
                            }
                         })
                    } else {
                        if (!obj?.actionInfo?.actionName?.includes(ConvertText)) {
                            isNotMatched = true
                            return false;
                        }                      
                    }
                })
            } else if (!Details.actionInfo.hasOwnProperty('IsGroup')) {
                if (!Details?.actionInfo?.actionName?.includes(ConvertText)) {
                    isNotMatched = true
                    return false;
                }              
            }
        })
    }

    if (isAppendChecked) {
        templateNodes?.length && templateNodes.forEach(function (Details) {
            if ($("#TextGenerateposition").val() === "prefix") {

                if (Details.hasOwnProperty('children')) {
                    Details.children.forEach((obj) => {
                        obj.actionInfo.actionName = $('#appendGenerateText').val() + obj.actionInfo.actionName
                    })
                } else if (Details.hasOwnProperty('groupName')) {
                    Details.groupActions.forEach((obj) => {
                        if (obj.hasOwnProperty('children')) {
                            obj.children.forEach((childObj) => {
                                childObj.actionInfo.actionName = $('#appendGenerateText').val() + childObj.actionInfo.actionName
                            })
                        } else {
                            obj.actionInfo.actionName = $('#appendGenerateText').val() + obj.actionInfo.actionName
                        }
                    })
                } else if (!Details.actionInfo.hasOwnProperty('IsGroup')) {
                    Details.actionInfo.actionName = $('#appendGenerateText').val() + Details.actionInfo.actionName
                }
            } else {
                if (Details.hasOwnProperty('children')) {
                    Details.children.forEach((obj) => {
                        obj.actionInfo.actionName = obj.actionInfo.actionName + $('#appendGenerateText').val()
                    })
                } else if (Details.hasOwnProperty('groupName')) {


                    Details.groupActions.forEach((obj) => {

                        if (obj.hasOwnProperty('children')) {
                            obj.children.forEach((childObj) => {
                                childObj.actionInfo.actionName = childObj.actionInfo.actionName + $('#appendGenerateText').val()
                            })

                        } else {
                            obj.actionInfo.actionName = obj.actionInfo.actionName + $('#appendGenerateText').val()
                        }


                    })
                } else if (!Details.actionInfo.hasOwnProperty('IsGroup')) {
                    Details.actionInfo.actionName = Details.actionInfo.actionName + $('#appendGenerateText').val()
                }
            }

        })
    } 

    if (isNotMatched && ConvertText) {
        $('#RestoreModal').modal('hide')
        $('#templateFindConfirmationModal').modal('show')
    } else {

        let restore_check = $('.restore-check:checked').val();

        $('#RestoreModal').modal('hide')
        $('#attachedInfraObjectName, #attachedInfraObjectType, #attachedProfileName').text('').parent().hide();

        if (restore_check == 'custom') {
            $('#RestoreAnimationModal').modal('show')

            let interval = setInterval(() => {
                let progressBarValue = +$('.restoreProgressBar').attr('aria-valuenow')
                progressBarStatus(progressBarValue, interval, restore_check)
            }, 1700)
        } else if (restore_check == 'infraobject') {       
            $('#RestoreAnimationModal').modal('show')
            let infraId = $('#WFInfraNameSelectList :selected').val()
            let filteredInfra = replicationByInfra?.length && replicationByInfra.find(infra => infra.id == infraId)
            let interval = setInterval(() => {
                let progressBarValue = +$('.restoreProgressBar').attr('aria-valuenow')
                progressBarStatus(progressBarValue, interval)
            }, 1700)
            setTimeout(() => {
                replaceAIWorkflow(templateNodes, filteredInfra, 'infrarestore')
            }, 8000)
        }
    }

}

$('#confirmationtemplateFindDiscard').on('click', function () {
    $('#templateFindConfirmationModal').modal('hide')
    $('#RestoreModal').modal('show')
})
$("#confirmationRRDiscard").on('click', function () {
    $('#roleReverseConfirmationModal .roleReverseContainer .restoreTemplate').parent().parent().find('.field-validation-error').text('').removeClass('field-validation-error')
    $('#SaveAsWorkflowListModal').modal('show')
    $('#roleReverseConfirmationModal').modal('hide')

})

$("#confirmationRRSave").on('click', function () {
    getRestoreObj('roleReverseContainer');   
    if (actionRestoreValidation('roleReverseContainer', 'restoreTemplate')) {       
        replaceObjValues(restoreUniqueObj, 'saveas', workFlowData)
        setTimeout(() => {
            saveAsConfirmation()
        }, 500)
    } else {
        return false;
    }
})

const saveAsConfirmation = async () => {
        if ($('#saveAstype').val() === 'components') {
            getRestoreObj('saveAsRestoreComponents');
            if (saveAsInputValidation() && actionRestoreValidation('saveAsRestoreComponents', 'restoreTemplate')) {
                replaceObjValues(restoreUniqueObj, 'saveas', workFlowData)
            } else {
                return false;
            }
        }

        if ($('#saveAstype').val() === 'components' || $('#saveAstype').val() === 'rolereverse') {
            if ($("#saveAsAppendCheckBox").prop("checked")) {
                workFlowData.forEach(function (Details) {
                    if ($("#Textposition").val() === "prefix") {

                        if (Details.hasOwnProperty('children')) {
                            Details.children.forEach((obj) => {
                                obj.actionInfo.actionName = $('#appendText').val() + obj.actionInfo.actionName
                            })
                        } else if (Details.hasOwnProperty('groupName')) {
                            Details.groupActions.forEach((obj) => {
                                if (obj.hasOwnProperty('children')) {
                                    obj.children.forEach((childObj) => {
                                        childObj.actionInfo.actionName = $('#appendText').val() + childObj.actionInfo.actionName
                                    })
                                } else {
                                    obj.actionInfo.actionName = $('#appendText').val() + obj.actionInfo.actionName
                                }
                            })
                        } else if (!Details.actionInfo.hasOwnProperty('IsGroup')) {
                            Details.actionInfo.actionName = $('#appendText').val() + Details.actionInfo.actionName
                        }
                    } else {
                        if (Details.hasOwnProperty('children')) {
                            Details.children.forEach((obj) => {
                                obj.actionInfo.actionName = obj.actionInfo.actionName + $('#appendText').val()
                            })
                        } else if (Details.hasOwnProperty('groupName')) {


                            Details.groupActions.forEach((obj) => {

                                if (obj.hasOwnProperty('children')) {
                                    obj.children.forEach((childObj) => {
                                        childObj.actionInfo.actionName = childObj.actionInfo.actionName + $('#appendText').val()
                                    })

                                } else {
                                    obj.actionInfo.actionName = obj.actionInfo.actionName + $('#appendText').val()
                                }


                            })
                        } else if (!Details.actionInfo.hasOwnProperty('IsGroup')) {
                            Details.actionInfo.actionName = Details.actionInfo.actionName + $('#appendText').val()
                        }
                    }

                })
            }

            workFlowData.forEach(function (Details) {
                let ConvertText = $('#findValue').val()
                if (Details.hasOwnProperty('children')) {
                    Details.children.forEach((obj) => {
                        obj.actionInfo.actionName = obj.actionInfo.actionName.replace(ConvertText, $('#replaceValue').val());
                    })
                } else if (Details.hasOwnProperty('groupName')) {
                    Details.groupActions.forEach((obj) => {
                        if (obj.hasOwnProperty('children')) {
                            obj.children.forEach((childObj) => {
                                childObj.actionInfo.actionName = childObj.actionInfo.actionName.replace(ConvertText, $('#replaceValue').val());
                            })
                        } else {
                            obj.actionInfo.actionName = obj.actionInfo.actionName.replace(ConvertText, $('#replaceValue').val());
                        }
                    })
                } else if (!Details.actionInfo.hasOwnProperty('IsGroup')) {
                    Details.actionInfo.actionName = Details.actionInfo.actionName.replace(ConvertText, $('#replaceValue').val());
                }
            })
        }

    if ($('#saveAstype').val() === 'duplicate') {
        if (await saveAsInputValidation() && $('#saveAsWorkflowName').val()) {
            saveAsWorkflow()
        } else {
            return false
        }
    } else if ($('#saveAstype').val() === 'components' || $('#saveAstype').val() === 'rolereverse') {
        if (await saveAsInputValidation() ) {
            saveAsWorkflow()
        } else {
            return false;
        }
    }
 }

const saveAsWorkflow = async () => {
    let saveAsData = '';
    if (workFlowData.length > 0) {
        saveAsData = JSON.stringify({ "nodes": workFlowData })
    } else {
        errorNotification('warning', 'Error occured while save workflow')
        return false;
    }

    let data = {
        "WorkflowId": GlobalWorkflowId,
        "name": $("#saveAsWorkflowName").val(),
        "properties": saveAsData,
        "Version": "0",
        __RequestVerificationToken: gettoken()
    }
    
    $.ajax({
        type: "POST",
        url: RootUrl + Urls.workflowSaveAs,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                notificationAlert("success", result.data.message)
                setTimeout(() => {
                    dataDisplay.empty()
                    workFlowData = [];
                    loadWorkflowDiagram(result.data.id)
                    GlobalWorkflowId = result.data.id
                    $('#SaveAsWorkflowListModal').modal('hide')
                    $('#roleReverseConfirmationModal').modal('hide')
                    
                },300)
    
            } else {
                errorNotification(result)
           }
        },
    })
}

// Search Workflow //

let searchTerm
let currentPlace = 0;
var currentIndex = -1;
var changeCurrentIndex = -1
let totalHighlighted = 0
var currentIndexInHighlights = 0

$('#highlight_arrow').hide()

$('#searchWorkFlow').on('input', function () {

    searchTerm = $(this).val().toLowerCase();
    $('.workflowActions').each(function () {
        const actionSpan = $(this).find('.actionSpan');
        const actionName = actionSpan.text().toLowerCase();

        if (actionName.includes(searchTerm) && searchTerm.length) {
            highlightMatchingLetters(actionSpan, searchTerm);
            totalHighlighted = $('.workflowActions .actionSpan').find('.highlight').length;
            currentIndex = -1
            changeCurrentIndex = -1
            currentIndexInHighlights = 0
            currentPlace = 0
            $('#highlightCount').text(`0 / ${totalHighlighted}`);
        } else {
            removeHighlighting(actionSpan);
        }
    });
});

$("#up-arrow-icon").on('click', function () {

    var $results = $('.actionSpan');
    let $currentResult = $results.eq(changeCurrentIndex);
    let $changeHighlight = $currentResult.find('.highlight')

    if (currentIndexInHighlights <= $changeHighlight.length - 1) {

        if (changeCurrentIndex > 0) {
            if ($changeHighlight.index($currentResult.find('.scroll-highlight')) !== 0) {
                currentIndex = changeCurrentIndex + 1

            } else {
                currentIndex = changeCurrentIndex
            }
        } else {
            if ($changeHighlight.index($currentResult.find('.scroll-highlight')) !== 0) {
                currentIndex = changeCurrentIndex + 1

            } else {
                currentIndex = changeCurrentIndex
            }
        }
    }
    else {
        currentIndex = changeCurrentIndex + 1
    }
   // if ($('.highlight.scroll-highlight').length > 0) $('.highlight.scroll-highlight')[0].scrollIntoView()
   
    updateHighlight(-1, 'up');
});

$("#down-arrow-icon").on('click', function () {
    var $results = $('.actionSpan');
    let $currentResult = $results.eq(changeCurrentIndex);
    let $changeHighlight = $currentResult.find('.highlight')

    if (changeCurrentIndex === 0) {
        if ($changeHighlight.index($currentResult.find('.scroll-highlight')) == $changeHighlight.length - 1) {
            currentIndex = changeCurrentIndex
            currentIndexInHighlights = 0
        } else {

            currentIndex = changeCurrentIndex - 1

        }
    } else if (changeCurrentIndex == $results.length - 1) {

        if ($changeHighlight.index($currentResult.find('.scroll-highlight')) == $changeHighlight.length - 1) {
            currentIndex = -1
            currentIndexInHighlights = 0
        } else {
            currentIndex = changeCurrentIndex - 1
        }

    } else if (changeCurrentIndex > 0) {

        if ($changeHighlight.index($currentResult.find('.scroll-highlight')) == $changeHighlight.length - 1) {

            currentIndex = changeCurrentIndex
            currentIndexInHighlights = 0
        }
    }
    //if ($('.highlight.scroll-highlight').length > 0) $('.highlight.scroll-highlight')[0].scrollIntoView()
    updateHighlight(1, 'down');
});
function updateHighlight(direction, mode) {

    var $results = $('.actionSpan');
    var $highlighted = $results.eq(currentIndex);
    var oldIndexHighlighted = $results.eq(currentIndex)
    var $highlights = $highlighted.find('.highlight');

    var originalIndex = currentIndex;

    do {

        currentIndex = (currentIndex + direction + $results.length) % $results.length;
        if (currentIndexInHighlights == 0) changeCurrentIndex = currentIndex
        $highlighted = $results.eq(currentIndex);
        $highlights = $highlighted.find('.highlight');

        if ($highlights.length > 1) {
            currentIndex = originalIndex;
            break;
        }
    } while ($highlights.length === 0);

    if ($highlights.length > 1) {

        if (mode == 'down') {

            currentIndexInHighlights = $highlights.index($highlighted.find('.scroll-highlight'));
            currentIndexInHighlights = (currentIndexInHighlights + direction + $highlights.length) % $highlights.length;
            if (oldIndexHighlighted.length > 0) {

                oldIndexHighlighted.find('.highlight').removeClass('scroll-highlight');
            }
            $highlighted.find('.highlight').removeClass('scroll-highlight');
            $highlighted.find('.highlight').eq(currentIndexInHighlights).addClass('scroll-highlight');

            if ($highlighted.length > 0) {
                //var highlightTop = $highlighted.position().top;
                //var containerHeight = $(".workflow_body_scroll").height();
                //var scrollTop = $(".workflow_body_scroll").scrollTop();

                //if (highlightTop < 0) {
                //    $(".workflow_body_scroll").scrollTop(scrollTop + highlightTop);
                //} else if (highlightTop + scrollTop > containerHeight) {
                //    $(".workflow_body_scroll").scrollTop(scrollTop + highlightTop - containerHeight + $highlighted.outerHeight());
                //}

                $('.highlight.scroll-highlight')[0].scrollIntoView()

                if (currentIndexInHighlights === $highlights.length - 1) {
                    currentIndex = (currentIndex + direction + $results.length) % $results.length;
                    $highlighted = $results.eq(currentIndex);
                    currentIndexInHighlights = 0
                }
            }
        } else if (mode == 'up') {

            currentIndexInHighlights = $highlights.index($highlighted.find('.scroll-highlight'));
            if ($highlights.index($highlighted.find('.scroll-highlight')) == -1) {
                currentIndexInHighlights = $highlights.length
            }
            currentIndexInHighlights = (currentIndexInHighlights + direction + $highlights.length) % $highlights.length;
            if (oldIndexHighlighted.length > 0) {
                oldIndexHighlighted.find('.highlight').removeClass('scroll-highlight');
            }
            $highlighted.find('.highlight').removeClass('scroll-highlight');
            $highlighted.find('.highlight').eq(currentIndexInHighlights).addClass('scroll-highlight');

            if ($highlighted.length > 0) {
                //var highlightTop = $highlighted.position().top;
                //var containerHeight = $(".workflow_body_scroll").height();
                //var scrollTop = $(".workflow_body_scroll").scrollTop();

                //if (highlightTop < 0) {
                //    $(".workflow_body_scroll").scrollTop(scrollTop + highlightTop);
                //} else if (highlightTop + scrollTop > containerHeight) {
                //    $(".workflow_body_scroll").scrollTop(scrollTop + highlightTop - containerHeight + $highlighted.outerHeight());
                //}

                $('.highlight.scroll-highlight')[0].scrollIntoView()
            }
        }

    } else {
        if (oldIndexHighlighted.length > 0) {
            oldIndexHighlighted.find('.highlight').removeClass('scroll-highlight');
        }
        $highlights.addClass('scroll-highlight');

        //var highlightTop = $highlighted.position().top;
        //var containerHeight = $(".workflow_body_scroll").height();
        //var scrollTop = $(".workflow_body_scroll").scrollTop();

        //if (highlightTop < 0) {
        //    $(".workflow_body_scroll").scrollTop(scrollTop + highlightTop);
        //} else if (highlightTop + scrollTop > containerHeight) {
        //    $(".workflow_body_scroll").scrollTop(scrollTop + highlightTop - containerHeight + $highlighted.outerHeight());
        //}

        $('.highlight.scroll-highlight')[0].scrollIntoView()
    }
    if (mode == 'down') {
        updateDownCurrentIndex();
    } else {
        updateUpCurrentIndex();
    }
}

function updateDownCurrentIndex() {

    totalHighlighted = $('.workflowActions .actionSpan').find('.highlight').length;
    if (currentPlace == totalHighlighted) {
        currentPlace = 0 + 1;
    } else if ($('.workflowActions').length > currentIndex) {
        currentPlace = currentPlace + 1;
    }
    $('#highlightCount').text(`${currentPlace} / ${totalHighlighted}`);
}

function updateUpCurrentIndex() {

    totalHighlighted = $('.workflowActions .actionSpan').find('.highlight').length;
    if (currentPlace == 1 || currentPlace == 0) {
        currentPlace = totalHighlighted;
    } else if ($('.workflowActions').length >= currentIndex) {

        currentPlace = currentPlace - 1;
    }
    $('#highlightCount').text(`${currentPlace} / ${totalHighlighted}`);
}

function highlightMatchingLetters($element, searchTerm) {
    const text = $element.text();
    const highlightedText = text.replace(new RegExp(searchTerm, 'gi'), (match) => `<span class="highlight">${match}</span>`);
    $element.html(highlightedText);
    $('#highlight_arrow').show()
}

function removeHighlighting($element) {
    $element.find('.highlight').each(function () {
        $(this).replaceWith($(this).text());
    });

    totalHighlighted = $('.workflowActions .actionSpan').find('.highlight').length;
    if (totalHighlighted === 0) {
        $('#highlight_arrow').hide()
    }
}

$('#search_category').on('input', function () {
    let filter = $(this).val().toLowerCase();

    $(".Workflow-Tree .categorysummary").each(function () {
        let $this = $(this);
        let parentDetails = $this.closest(".Workflow-Tree details");

        if (filter === '') {
            parentDetails.show().removeAttr('open');

            if ($this.hasClass('actiontype')) {
                $this.parent().show();
            } else {
                $this.closest(".Workflow-Tree details").show();
            }
        }
    });

    if (filter === '') {
        $("#workflowTreeNoData").hide().html("");
    }
});


$("#btn_search").on('click', btnDebounce(function () {
    let filter = $('#search_category').val().toLowerCase();
    let categoryFlagStatus = true;
    $(".Workflow-Tree .categorysummary").each(function () {
        let splitText = $(this)?.text()?.trim()?.split(" ")
        let $i = 0;

        if (splitText[0].search(new RegExp(filter, "i")) >= 0) {
            $i++;
        }

        if (filter !== '') {

            if ($i > 0 && $(this).hasClass('secondChild') && $(this)?.parent()?.parent().css('display') == 'none') {
                $(this).parent().parent().css('display', 'block').attr('open', 'open')

            } else if ($(this).hasClass('secondChild') && $(this)?.parent()?.parent().css('display') != 'none') {

                let firstData = $(this)?.parent()?.parent()?.text()?.trim()?.split(" ")

                if (firstData[0]?.toLowerCase().includes(filter)) $i++
                else $i = 0

            }


            if ($(this).hasClass('thirdChild') && $(this)?.parent()?.parent().css('display') != 'none') {

                let firstData = $(this)?.parent()?.parent()?.parent()?.text()?.trim()?.split(" ")
                let secondData = $(this)?.parent()?.parent()?.text()?.trim()?.split(" ")

                if (firstData[0]?.toLowerCase().includes(filter) || secondData[0]?.toLowerCase().includes(filter)) $i++
                else $i = 0

            } else if ($i > 0 && $(this).hasClass('thirdChild') && $(this)?.parent()?.parent().css('display') == 'none') {
                $(this).parent().parent().css('display', 'block').attr('open', 'open')
                $(this).parent().parent().parent().css('display', 'block').attr('open', 'open')
            }


            if ($(this).hasClass('actiontype') && $(this)?.parent()?.parent().css('display') != 'none') {
                let firstData = $(this)?.parent()?.parent()?.parent()?.parent()?.text()?.trim()?.split(" ")
                let secondData = $(this)?.parent()?.parent()?.parent()?.text()?.trim()?.split(" ")
                let thirdData = $(this)?.parent()?.parent()?.text()?.trim()?.split(" ")
                let currentText = $(this)?.text()?.trim()?.split(" ")

                if (firstData[0]?.toLowerCase().includes(filter) || secondData[0]?.toLowerCase().includes(filter) || thirdData[0]?.toLowerCase().includes(filter)) {
                    $i++
                } else if (currentText[0]?.toLowerCase().includes(filter)) {
                    $i++
                } else {
                    $i = 0
                }
                //$i++

            } else if ($i > 0 && $(this).hasClass('actiontype') && $(this)?.parent()?.parent().css('display') == 'none') {
                $(this)?.parent()?.parent().css('display', 'block').attr('open', 'open')
                $(this)?.parent()?.parent()?.parent().css('display', 'block').attr('open', 'open')
                $(this)?.parent()?.parent()?.parent()?.parent().css('display', 'block').attr('open', 'open')
            }
        }

        if (filter == '') {
            categoryFlagStatus = false
            $(this).closest(".Workflow-Tree details").show();

            if ($(this).hasClass('actiontype')) {
                $(this)?.parent()[0]?.style.setProperty('display', 'block', 'important');
            } 

            $(this).closest(".Workflow-Tree details").removeAttr('open');

        } else if ($i > 0) {
            categoryFlagStatus = false

            if ($(this).hasClass('actiontype')) {
                $(this)?.parent()[0]?.style.setProperty('display', 'block', 'important');
            } else {
                $(this).closest(".Workflow-Tree details").show();
            }
           
            $(this).closest(".Workflow-Tree details").attr('open', 'open');

        } else {
            //$(this).closest(".Workflow-Tree details").hide();

            if ($(this).hasClass('actiontype')) {
                $(this)?.parent()[0]?.style.setProperty('display', 'none', 'important');
            } else {
                $(this).closest(".Workflow-Tree details").hide();
            }
       }

    });

    if (categoryFlagStatus) {
        $("#workflowTreeNoData").show().css({ 'text-align': 'center' }).html(noDataImage);
    }
    else {
        $("#workflowTreeNoData").hide().html("")
    }

}, 800));

function updateFilterProperties(newActionObj, groupData = {}) {
    const tempArr = [];
    let elements = $(".filterTitle");

    $filterProperty.find('li input[type="checkbox"]').each(function () {
        tempArr.push($(this).val());
    });


    //if (Object.keys(groupData).length) {
    //    if (!tempArr.includes(groupData?.groupId)) {
    //        tempArr.push(groupData?.groupId);

    //        let newList = $("<li class='dropdown-item'><div><input class='form-check-input' type='checkbox' data-type='" + 'group' + "' value='" + groupData?.groupId + "' /><label class='form-check-label'>" + groupData?.groupName + "</label></div></li>");
    //        newList.find('input[type="checkbox"]').on('change', filterActions);
    //        $("#filter_property ul").append(newList);
    //    }
    //}

    for (let i = 0; i < elements.length; i++) {
        let element = $(elements[i]);

        if (element.attr('id') == newActionObj.actionInfo.parentActionId) {
            let optionValue = newActionObj.actionInfo.parentActionId;
            let optionText = element.text().trim();
            var newLi = ''

            if (!tempArr.includes(optionValue)) {
                var newLi = $("<li class='dropdown-item'><div><input class='form-check-input' type='checkbox' value='" + optionValue + "' /><label class='form-check-label'>" + optionText + "</label></div></li>");
                newLi.find('input[type="checkbox"]').on('change', filterActions);
                $("#filter_property ul").append(newLi);
            }

            break; 
        }

    }

}

const filterActions = () => {
    let mainDiv = $('#filter_property');
    let isGroupFound = false;
    let selectedCheckboxes = mainDiv.find("input[type=checkbox]:checked").map(function () {
        ($(this).data('type') === 'group') ? isGroupFound = true : isGroupFound = false;
        return this.value;
    }).get();
    $('.workflowActions').each(function () {
        const actionId = $(this).attr('parentId')
        const isParallel = $(this).closest('.parallelCont').length > 0;
        const isGroup = $(this).closest('.parentGroup').length > 0;

        if (!selectedCheckboxes.includes('all')) {

            if (!selectedCheckboxes.length || selectedCheckboxes.includes(actionId)) {
                checkAll($(this), isParallel, isGroup)
            } else if (!selectedCheckboxes.length || isGroupFound) {
                checkAll(selectedCheckboxes, isParallel, isGroup, isGroupFound)
            } else {
                unCheckAll($(this), isParallel, isGroup)
            }
        } else {

            checkAll($(this), isParallel, isGroup)
        }
    });
}

function checkAll($element, isParallel, isGroup, isGroupFound = false) {

    if (isGroupFound) {
        $(`#${$element[0]}`).show()
        $(`#${$element[0]}`).closest('.ui-sortable-handle').show();

    }else if (isParallel) {
        $element.show();
        $element.closest('.parallelCont').parent().show();

    } else if (isGroup) {
        $element.show();
        $element.closest('.parentGroup').parent().show();
    } else {
        $element.parent().show();
    }
}
function unCheckAll($element, isParallel, isGroup) {
    if (isParallel) {
        $element.hide();
        const siblingActions = $element.closest('.parallelCont').find('.workflowActions:visible');

        if (siblingActions.length === 0) {
            $element.closest('.parallelCont').parent().hide();
        }
    } else if (isGroup) {
        $element.hide();

        const siblingActions = $element.closest('.parentGroup').find('.workflowActions:visible');

        if (siblingActions.length === 0) {
            $element.closest('.parentGroup').parent().hide();
        }
    } else {
        $element.parent().hide();
    }
}

const expandId = document.getElementById('expandAction')
let expandChangeId = $('#expandAction')
expandId.addEventListener('click', function (e) {
    
    const details = document.querySelectorAll('details');

    details.forEach((e) => {
        (e.hasAttribute('open')) ?
            e.removeAttribute('open') : e.setAttribute('open', true);
    })

    if (e.currentTarget.classList.contains('cp-circle-rightarrow')) {
        expandChangeId.removeClass('cp-circle-rightarrow')
        expandChangeId.addClass('cp-circle-downarrow')
    } else {
        expandChangeId.removeClass('cp-circle-downarrow')
        expandChangeId.addClass('cp-circle-rightarrow')
    }
})





