﻿using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByBusinessServiceId;
using ContinuityPatrol.Application.Features.User.Queries.GetDetail;
using ContinuityPatrol.Application.Features.UserInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetWorkflowInfraObjectByInfraObjectId;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixModel;
using ContinuityPatrol.Domain.ViewModels.UserGroupModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileModel;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Mocks;
using ContinuityPatrol.Web.Areas.Manage.Controllers;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;


namespace ContinuityPatrol.Web.UnitTests.Areas.Manage.Controllers;
public class ApprovalMatrixControllerTests
{
    private readonly Mock<IPublisher> _mockPublisher = new();
    private readonly Mock<IMapper> _mockMapper = new();
    private readonly Mock<ILogger<ApprovalMatrixController>> _mockLogger = new();
    private readonly Mock<IDataProvider> _mockDataProvider = new();
    private readonly Mock<IEmailService> _mockEmailService = new();
    private  ApprovalMatrixController _controller;

    public ApprovalMatrixControllerTests()
    {
        Initialize();
    }
    public void Initialize()
    { 
        _controller = new ApprovalMatrixController(
            _mockPublisher.Object,
            _mockMapper.Object,
            _mockLogger.Object,
            _mockDataProvider.Object,
            _mockEmailService.Object
        );
        _controller.ControllerContext.HttpContext = new DefaultHttpContext();
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");

    }

    [Fact]
    public void CreateOrUpdate_ReturnsRedirectOnSuccess()
    {
        var fixture = new Fixture();

        fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
            .ForEach(b => fixture.Behaviors.Remove(b));
        fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        var viewModel = fixture.Create<ApprovalMatrixModel>();

        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues> { { "id", "22" } };
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;

        var approvalMatrixModel = new ApprovalMatrixModel
        {
           // FourEyeModel = new FourEyeModel
            
            Name = "user1",
            BusinessServiceName = "group1"
            
            //ApprovalMatrixTemplate = new ApprovalMatrixTemplate()
        };

        var mockUser = new UserDetailVm
        {
            Id = "123",
            LoginName = "user1",
            UserInfo = new UserInfoDetailVm { Email = "<EMAIL>" }
        };

        var mockGroupUser = new UserGroupListVm
        {
            UserNames = "groupUser",
            UserId = "456"
        };

        var mockResult = new BaseResponse
        {
            Success = true,
            Message = "Success"
        };

        _mockDataProvider.Setup(x => x.User.GetByReferenceId(It.IsAny<string>()))
            .ReturnsAsync(mockUser);

        _mockDataProvider.Setup(x => x.UserGroup.GetByReferenceId(It.IsAny<string>()))
            .ReturnsAsync(mockGroupUser);

        //_mockMapper.Setup(x => x.Map<CreateApprovalMatrixTemplateCommand>(It.IsAny<FourEyeModel>()))
        //    .Returns(new CreateApprovalMatrixTemplateCommand());

        //_mockDataProvider.Setup(x => x.ApprovalMatrixTemplateService.CreateAsync(It.IsAny<CreateApprovalMatrixTemplateCommand>()))
        //    .ReturnsAsync(mockResult);

       // var result =  _controller.CreateOrUpdate(approvalMatrixModel);

        _mockDataProvider.Verify(x => x.User.GetByReferenceId(It.IsAny<string>()), Times.Once);
     
    }
    [Fact]
    public void  CreateOrUpdate_ReturnsBadRequestOnFailure()
    {
        //var command = new CreateApprovalMatrixTemplateCommand();

        //var approvalMatrixModel = new ApprovalMatrixModel
        //{
        //    FourEyeModel = new FourEyeModel
        //    {
        //        UserNames = "user1",
        //        GroupNames = "group1"
        //    },
        //    ApprovalMatrixTemplate = new ApprovalMatrixTemplate()
        //};

        //var mockUser = new UserDetailVm
        //{
        //    Id = "123",
        //    LoginName = "user1",
        //    UserInfo = new UserInfoDetailVm { Email = "<EMAIL>" }
        //};

        //var mockGroupUser = new UserGroupListVm
        //{
        //    UserNames = "groupUser",
        //    UserId = "456"
        //};

        //var mockResult = new BaseResponse
        //{
        //    Success = false,
        //    Message = "Failure"
        //};

        //_mockDataProvider.Setup(x => x.User.GetByReferenceId(It.IsAny<string>()))
        //    .ReturnsAsync(mockUser);

        //_mockDataProvider.Setup(x => x.UserGroup.GetByReferenceId(It.IsAny<string>()))
        //    .ReturnsAsync(mockGroupUser);

        //_mockMapper.Setup(x => x.Map<CreateApprovalMatrixTemplateCommand>(It.IsAny<FourEyeModel>()))
        //    .Returns(new CreateApprovalMatrixTemplateCommand());

        //_mockDataProvider.Setup(x => x.ApprovalMatrixTemplateService.CreateAsync(It.IsAny<CreateApprovalMatrixTemplateCommand>()))
        //    .ReturnsAsync(mockResult);

        //var result =  _controller.CreateOrUpdate(approvalMatrixModel);

        //_mockDataProvider.Verify(x => x.User.GetByReferenceId(It.IsAny<string>()), Times.Once);
    }
    [Theory]
    [InlineData("approve", "Approved", "Approval Request is approved for : TestName")]
    [InlineData("reject", "Rejected", "Approval Request is rejected for : TestName")]
    public void  ApproveOrRejectMatrix_HandlesBothApprovalAndRejection(string action, string expectedStatus, string expectedMessage)
    {
        string matrixId = "TestId";
        string matrixName = "TestName";
        _mockDataProvider.Setup(d => d.approvalMatrixService.ApproveApprovalRequest(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                         .ReturnsAsync(expectedMessage);
        var result =  _controller.ApproveMatrix(matrixId, matrixName);
          
        Assert.NotNull (result);
        
    }

    [Fact]
    public void  CreateOrUpdate_Process_ShouldCreateNewApprovalMatrix()
    {
        //var approvalMatrixModel = new ApprovalMatrixModel
        //{
        //    ApprovalMatrixTemplate = new ApprovalMatrixTemplate { BusinessServiceName = "TestService" }
        //};

        _mockDataProvider.Setup(d => d.BusinessService.GetByBusinessServiceName(It.IsAny<string>()))
                         .ReturnsAsync(new BusinessServiceDetailVm { Id = "ServiceId" });

        _mockDataProvider.Setup(d => d.approvalMatrixService.CreateAsync(It.IsAny<CreateApprovalMatrixCommand>()))
                         .ReturnsAsync(new BaseResponse { Message = "Success" });

        //var result =  _controller.CreateOrUpdate_Process(approvalMatrixModel);

        // Assert.NotNull(result);
       
    }

    [Fact]
    public async Task GetWorkflowByBusinessService_ShouldReturnWorkflowNames()
    {
        string businessServiceName = "TestService";
        var infraObjects = new List<GetInfraObjectByBusinessServiceIdVm> { new GetInfraObjectByBusinessServiceIdVm { Id = "InfraId" } };
        var workflows = new List<WorkflowInfraObjectByInfraObjectIdVm> { new WorkflowInfraObjectByInfraObjectIdVm { WorkflowId = "WorkflowId", WorkflowName = "WorkflowName" } };

        _mockDataProvider.Setup(d => d.InfraObject.GetInfraObjectByBusinessServiceName(It.IsAny<string>()))
                         .ReturnsAsync(infraObjects);

        _mockDataProvider.Setup(d => d.WorkflowInfraObject.GetWorkflowByInfraObjectId(It.IsAny<string>()))
                         .ReturnsAsync(workflows);

        var result = await _controller.GetWorkflowByBusinessService(businessServiceName);

        var jsonResult = Assert.IsType<JsonResult>(result);
        var workflowNameVms = Assert.IsAssignableFrom<List<WorkflowNameVm>>(jsonResult.Value);
        Assert.Single(workflowNameVms);
        Assert.Equal("WorkflowId", workflowNameVms.First().Id);
    }
        [Theory]
        [InlineData("Template", true)]
        [InlineData("Matrix", false)]
        public async Task TemplateAndMatrixNameExist_ChecksExistence(string nameType, bool expectedExistence)
        {
            if (nameType == "Template")
            {
                //_mockDataProvider.Setup(d => d.ApprovalMatrixTemplateService.IsTemplateNameExist(It.IsAny<string>()))
                //    .ReturnsAsync(expectedExistence);

                var result =  _controller.IsTemplateNameExist("TestTemplate");

                Assert.Equal(expectedExistence, result);
            }
            else
            {
                _mockDataProvider.Setup(d => d.approvalMatrixService.IsMatrixNameExist(It.IsAny<string>(), It.IsAny<string>()))
                    .ReturnsAsync(expectedExistence);
       
                var result = await _controller.IsMatrixNameExist("TestMatrix", "Id");
                
                Assert.Equal(expectedExistence, result);
            }
        }

        [Theory]
        [InlineData("ApprovalMatrixId$TestMatrix", "Success")]
        [InlineData("TemplateId$TestTemplate", "Attached")]
        public void WithdrawApprovalMatrix_HandlesWithdrawal(string approvalId, string expectedResult)
        {
            if (expectedResult == "Attached")
            {
                _mockDataProvider.Setup(d => d.approvalMatrixService.IsTemplateAttached(It.IsAny<string>()))
                                 .ReturnsAsync(true);
                var result =  _controller.WithdrawApprovalMatrixTemplate(approvalId);
  
                
                Assert.Equal(expectedResult, result.Result.Value);
            }
            else
            {
                _mockDataProvider.Setup(d => d.approvalMatrixService.WithdrawApprovalMatrix(It.IsAny<string>()))
                                .ReturnsAsync(expectedResult);
             
                var result =  _controller.WithdrawApprovalMatrix(approvalId);
                var json = JsonConvert.SerializeObject(result);
                Assert.Contains(expectedResult, json);
            }
        }
 

        [Fact]
        public async Task GetWorkflowProfilesByBusinessService_ReturnsProfiles()
        {
            string businessServiceName = "TestService";
            var infraObjects = new List<GetInfraObjectByBusinessServiceIdVm> { new GetInfraObjectByBusinessServiceIdVm { Id = "InfraId" } };
            var profiles = new List<WorkflowProfileInfoNameVm> { new WorkflowProfileInfoNameVm { ProfileId = "ProfileId", ProfileName = "ProfileName" } };

            _mockDataProvider.Setup(d => d.InfraObject.GetInfraObjectByBusinessServiceName(It.IsAny<string>()))
                             .ReturnsAsync(infraObjects);
            _mockDataProvider.Setup(d => d.WorkflowProfileInfo.GetWorkflowProfileInfoByInfraObjectId(It.IsAny<string>()))
                             .ReturnsAsync(profiles);

            var result = await _controller.GetWorkflowProfilesByBusinessService(businessServiceName);

            var jsonResult = Assert.IsType<JsonResult>(result);
            var workflowProfiles = Assert.IsAssignableFrom<List<WorkflowProfileNameVm>>(jsonResult.Value);
            Assert.Single(workflowProfiles);
            Assert.Equal("ProfileId", workflowProfiles.First().Id);
        }

    [Fact]
    public void GetLoginName_ReturnsLoggedUserName()
    {
        string loggedUserName = "TestUser";

        var mockUserSession = new UserSession { LoginName = loggedUserName };
        WebHelper.UserSession = mockUserSession;
        _controller.ControllerContext = new ControllerContextMocks().Default();
        var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
        WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
        var result = _controller.GetLoginName();

        Assert.NotNull(result);
        //Assert.Equal(loggedUserName, jsonResult.Value);
    }

}


