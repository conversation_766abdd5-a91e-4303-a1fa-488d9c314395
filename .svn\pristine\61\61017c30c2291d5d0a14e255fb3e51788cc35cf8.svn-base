﻿using AutoFixture;
using ContinuityPatrol.Application.Features.PageBuilder.Commands.Create;
using ContinuityPatrol.Application.Features.PageBuilder.Commands.Update;
using ContinuityPatrol.Application.Features.PageBuilder.Events.PaginatedView;
using ContinuityPatrol.Application.Features.PageBuilder.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;
using ContinuityPatrol.Domain.ViewModels.PageBuilderModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class ConfigurePageControllerShould
    {
        private readonly Mock<IPublisher> _publisherMock = new();
        private readonly Mock<IPageBuilderService> _pageBuilderServiceMock = new();
        private readonly Mock<IDataProvider> _dataProviderMock = new();
        private readonly Mock<ILogger<ConfigureWidgetController>> _loggerMock = new();
        private readonly Mock<IMapper> _mapperMock = new();
        private readonly Fixture _fixture = new();
        private ConfigurePageController _controller;

        public ConfigurePageControllerShould()
        {
            Initialize();
        }
        internal void Initialize()
        {
            _controller = new ConfigurePageController(
                _publisherMock.Object,
                _dataProviderMock.Object,
                _loggerMock.Object,
                _mapperMock.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_Returns_View()
        {
            var result = await _controller.List() as ViewResult;

            Assert.NotNull(result);
        }

        [Fact]
        public async Task List_ThrowsException_ReturnsView()
        {
            // Arrange
            _publisherMock.Setup(p => p.Publish(It.IsAny<PageBuilderPaginatedEvent>(), It.IsAny<CancellationToken>()))
                         .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
        }


        [Fact]
        public async Task GetPageBuilderList_Returns_Json_With_PageBuilders()
        {

            var pageBuilders = _fixture.Create<List<PageBuilderListVm>>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            _dataProviderMock.Setup(dp => dp.PageBuilder.GetPageBuilderList())
                .ReturnsAsync(pageBuilders);

            
            var result = await _controller.GetPageBuilderList() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
            
        }

        [Fact]
        public async Task GetPageBuilderList_Handles_Exception()
        {
            
            _dataProviderMock.Setup(dp => dp.PageBuilder.GetPageBuilderList())
                .ThrowsAsync(new Exception("Error"));

            
            var result = await _controller.GetPageBuilderList() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
            
        }

        [Fact]
        public async Task CreateOrUpdate_Creates_PageBuilder()
        {

            var viewModel = _fixture.Create<PageBuilderViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreatePageBuilderCommand {
                Name= viewModel.Name,
                Properties=viewModel.Properties,
                Type= viewModel.Type,
                IsLock=false,
                IsPublish=true

            };
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mapperMock.Setup(m => m.Map<CreatePageBuilderCommand>(viewModel))
                .Returns(command);
            _dataProviderMock.Setup(service => service.PageBuilder.CreateAsync(command))
                .ReturnsAsync(response);

            
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            
            
        }

        [Fact]
        public async Task CreateOrUpdate_Updates_PageBuilder()
        {

            var viewModel = _fixture.Create<PageBuilderViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdatePageBuilderCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mapperMock.Setup(m => m.Map<UpdatePageBuilderCommand>(viewModel))
                .Returns(command);
            _dataProviderMock.Setup(service => service.PageBuilder.UpdateAsync(command))
                 .ReturnsAsync(response);

            
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":true", json);
            
        }

        [Fact]
        public async Task CreateOrUpdate_Handles_Exception()
        {

            var viewModel = _fixture.Create<PageBuilderViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdatePageBuilderCommand();

            _mapperMock.Setup(m => m.Map<UpdatePageBuilderCommand>(viewModel))
                .Returns(command);
            _dataProviderMock.Setup(service => service.PageBuilder.UpdateAsync(command))
                .ThrowsAsync(new Exception("Error"));


            var result = await _controller.CreateOrUpdate(viewModel) as RedirectToActionResult;

            Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_Handles_ValidationException()
        {
            // Arrange
            var viewModel = _fixture.Create<PageBuilderViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreatePageBuilderCommand();

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Name", "Name is required"));
            var validationException = new ValidationException(validationResult);

            _mapperMock.Setup(m => m.Map<CreatePageBuilderCommand>(viewModel))
                .Returns(command);
            _dataProviderMock.Setup(service => service.PageBuilder.CreateAsync(command))
                .ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as RedirectToActionResult;

            // Assert
            Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task Delete_Calls_Delete_Method()
        {

            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };

            _dataProviderMock.Setup(dp => dp.PageBuilder.DeleteAsync(id))
                .ReturnsAsync(response);


            var result = await _controller.Delete(id) as RedirectToActionResult;


            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task Delete_Handles_Exception()
        {
            // Arrange
            var id = "1";
            _dataProviderMock.Setup(dp => dp.PageBuilder.DeleteAsync(id))
                .ThrowsAsync(new Exception("Delete error"));

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task GetByReferenceId_Returns_Json_With_PageBuilderList()
        {
            // Arrange
            var id = "1";
            var pageBuilderDetail = _fixture.Create<PageBuilderDetailVm>();
            _dataProviderMock.Setup(M => M.PageBuilder.GetByReferenceId(id)).ReturnsAsync(pageBuilderDetail);

            // Act
            var result = await _controller.GetByReferenceId(id) as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);

            // Assert
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetByReferenceId_Handles_Exception()
        {
            
            var id = "1";

            _dataProviderMock.Setup(dp => dp.PageBuilder.GetByReferenceId(id))
                .ThrowsAsync(new Exception("Error"));

            
            var result = await _controller.GetByReferenceId(id) as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":false", json);
            
        }

        [Fact]
        public async Task GetMonitorTypeByInfraObject_Returns_Json_With_DashboardNames()
        {
            // Arrange
            var dashboardNames = _fixture.Create<List<DashboardViewNameVm>>();
            _dataProviderMock.Setup(M => M.DashboardView.GetDashboardNames()).ReturnsAsync(dashboardNames);

            // Act
            var result = await _controller.GetMonitorTypeByInfraObject() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);

            // Assert
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetMonitorTypeByInfraObject_Handles_Exception()
        {

            _dataProviderMock.Setup(dp => dp.DashboardView.GetDashboardNames())
                .ThrowsAsync(new Exception("Error"));


            var result = await _controller.GetMonitorTypeByInfraObject() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":false", json);

        }

        // ===== CONTROLLER ATTRIBUTE TESTS =====

        [Fact]
        public void Controller_ShouldHaveAreaAttribute()
        {
            // Arrange & Act
            var areaAttribute = _controller.GetType().GetCustomAttributes(typeof(AreaAttribute), false).FirstOrDefault() as AreaAttribute;

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void List_ShouldHaveAntiXssAttribute()
        {
            // Arrange & Act
            var method = _controller.GetType().GetMethod("List");
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void GetPageBuilderList_ShouldHaveAntiXssAttribute()
        {
            // Arrange & Act
            var method = _controller.GetType().GetMethod("GetPageBuilderList");
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void CreateOrUpdate_ShouldHaveValidateAntiForgeryTokenAndAntiXssAttributes()
        {
            // Arrange & Act
            var method = _controller.GetType().GetMethod("CreateOrUpdate");
            var validateAntiForgeryTokenAttribute = method.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void Delete_ShouldHaveValidateAntiForgeryTokenAndAntiXssAttributes()
        {
            // Arrange & Act
            var method = _controller.GetType().GetMethod("Delete");
            var validateAntiForgeryTokenAttribute = method.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void GetByReferenceId_ShouldHaveAntiXssAttribute()
        {
            // Arrange & Act
            var method = _controller.GetType().GetMethod("GetByReferenceId");
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void GetMonitorTypeByInfraObject_ShouldHaveHttpGetAttribute()
        {
            // Arrange & Act
            var method = _controller.GetType().GetMethod("GetMonitorTypeByInfraObject");
            var httpGetAttribute = method.GetCustomAttributes(typeof(HttpGetAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        // ===== CONSTRUCTOR TESTS =====

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new ConfigurePageController(
                _publisherMock.Object,
                _dataProviderMock.Object,
                _loggerMock.Object,
                _mapperMock.Object
            );

            // Assert
            Assert.NotNull(controller);
        }
    }
}
