using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class TableAccessFixture : IDisposable
{
    public List<TableAccess> TableAccessPaginationList { get; set; }
    public List<TableAccess> TableAccessList { get; set; }
    public TableAccess TableAccessDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public TableAccessFixture()
    {
        var fixture = new Fixture();

        TableAccessList = fixture.Create<List<TableAccess>>();

        TableAccessPaginationList = fixture.CreateMany<TableAccess>(20).ToList();

        TableAccessPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        TableAccessPaginationList.ForEach(x => x.IsActive = true);

        TableAccessList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        TableAccessList.ForEach(x => x.IsActive = true);

        TableAccessDto = fixture.Create<TableAccess>();
        TableAccessDto.ReferenceId = Guid.NewGuid().ToString();
        TableAccessDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}