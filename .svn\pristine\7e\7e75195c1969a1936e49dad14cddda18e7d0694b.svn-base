﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class AlertNotificationRepository : BaseRepository<AlertNotification>, IAlertNotificationRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public AlertNotificationRepository(ApplicationDbContext dbContext,ILoggedInUserService loggedInUserService) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<AlertNotification>> GetAlertNotificationByInfraObjectIdAndAlertCode(string infraObjectId,
        string alertCode)
    {
        return await _dbContext.AlertNotifications
            .AsNoTracking()
            .Where(x =>
                x.IsActive &&
                x.InfraObjectId == infraObjectId &&
                x.AlertCode == alertCode
            )
            .ToListAsync();
    }
}