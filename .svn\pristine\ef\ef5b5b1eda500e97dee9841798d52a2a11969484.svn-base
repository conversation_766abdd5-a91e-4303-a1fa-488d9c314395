﻿@model ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels.UserViewModel 
<div class="modal-dialog modal-sm modal-dialog-centered">
    <div class="modal-content">
        <form id="resetForm" asp-controller="User" asp-action="Unlock" method="post" enctype="multipart/form-data">
            <div class="modal-header p-0">
                <img class="delete-img" src="/img/isomatric/reset-password.svg" />
            </div>
            <div class="modal-body text-center pt-0">
                <h5 class="fw-bold">Are you sure?</h5>
                <p> You want to unlock <span class="font-weight-bolder text-primary" id="resetData"></span> user? </p>
                <input asp-for="UserUnLockModel.UserId" type="hidden" id="resetId" />
                <input asp-for="UserUnLockModel.IsLock" type="hidden" id="resetName" value="" />
            @* <input asp-for="userResetViewModal.Email" type="hidden" id="resetEmail" value=""/>
            <input asp-for="userResetViewModal.Password" type="hidden" id="password" value=""/>
                <input asp-for="userResetViewModal.NewPassword" type="hidden" id="newPassword" value=""/> *@
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="submit" id="userReset" class="btn btn-primary btn-sm px-4">Yes</button>
            </div>
            
        </form>
    </div>
</div>