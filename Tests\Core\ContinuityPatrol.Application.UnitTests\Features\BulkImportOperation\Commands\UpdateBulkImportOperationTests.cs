using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperation.Commands;

public class UpdateBulkImportOperationTests : IClassFixture<BulkImportOperationFixture>
{
    private readonly BulkImportOperationFixture _bulkImportOperationFixture;
    private readonly Mock<IBulkImportOperationRepository> _mockBulkImportOperationRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IPublisher> _publisher;
    private readonly UpdateBulkImportOperationCommandHandler _handler;

    public UpdateBulkImportOperationTests(BulkImportOperationFixture bulkImportOperationFixture)
    {
        _bulkImportOperationFixture = bulkImportOperationFixture;

        _mockBulkImportOperationRepository = BulkImportOperationRepositoryMocks.CreateUpdateBulkImportOperationRepository(_bulkImportOperationFixture.BulkImportOperations);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map(It.IsAny<object>(), It.IsAny<object>(), It.IsAny<Type>(), It.IsAny<Type>()))
            .Callback<object, object, Type, Type>((source, destination, srcType, destType) =>
            {
                if (source is UpdateBulkImportOperationCommand cmd && destination is Domain.Entities.BulkImportOperation entity)
                {
                    entity.CompanyId = cmd.CompanyId;
                    entity.UserName = cmd.UserName;
                    entity.Description = cmd.Description;
                    entity.Status = cmd.Status;
                    entity.StartTime = cmd.StartTime;
                    entity.EndTime = cmd.EndTime;
                }
            });

        _publisher = new Mock<IPublisher>();
        _handler = new UpdateBulkImportOperationCommandHandler(
            _mockMapper.Object,
            _mockBulkImportOperationRepository.Object, _publisher.Object);
    }

    [Fact]
    public async Task Handle_Return_UpdateBulkImportOperationResponse_When_BulkImportOperationUpdated()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var command = new UpdateBulkImportOperationCommand 
        { 
            Id = existingOperation.ReferenceId,
            Description = "Updated description"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(UpdateBulkImportOperationResponse));
        result.Id.ShouldBe(existingOperation.ReferenceId);
        result.Message.ShouldContain("BulkImportOperation");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var command = new UpdateBulkImportOperationCommand { Id = existingOperation.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_UpdateAsync_OnlyOnce()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var command = new UpdateBulkImportOperationCommand { Id = existingOperation.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperation>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var command = new UpdateBulkImportOperationCommand { Id = existingOperation.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map(It.IsAny<object>(), It.IsAny<object>(),
            It.IsAny<Type>(), It.IsAny<Type>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportOperationNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new UpdateBulkImportOperationCommand { Id = nonExistentId };

        _mockBulkImportOperationRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportOperation)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_UpdateEntityProperties_When_CommandMapped()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var command = new UpdateBulkImportOperationCommand 
        { 
            Id = existingOperation.ReferenceId,
            CompanyId = "UpdatedCompanyId",
            UserName = "UpdatedUser",
            Description = "Updated description",
            Status = "Completed",
            StartTime = DateTime.Now.AddDays(-1),
            EndTime = DateTime.Now
        };

        Domain.Entities.BulkImportOperation capturedEntity = null;

        _mockBulkImportOperationRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperation>()))
      .Callback<Domain.Entities.BulkImportOperation>(operation => capturedEntity = operation)
      .ReturnsAsync((Domain.Entities.BulkImportOperation operation) => operation);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEntity.ShouldNotBeNull();
        capturedEntity.CompanyId.ShouldBe("UpdatedCompanyId");
        capturedEntity.UserName.ShouldBe("UpdatedUser");
        capturedEntity.Description.ShouldBe("Updated description");
        capturedEntity.Status.ShouldBe("Completed");
    }

    [Fact]
    public async Task Handle_CreateResponseWithCorrectMessage_When_BulkImportOperationUpdated()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        existingOperation.UserName = "TestUser";
        var command = new UpdateBulkImportOperationCommand 
        { 
            Id = existingOperation.ReferenceId,
            UserName = "TestUser"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldContain("BulkImportOperation");
        result.Message.ShouldContain("TestUser");
        result.Id.ShouldBe(existingOperation.ReferenceId);
    }

    [Fact]
    public async Task Handle_CallRepositoryWithCorrectId_When_QueryExecuted()
    {
        // Arrange
        var testId = Guid.NewGuid().ToString();
        var command = new UpdateBulkImportOperationCommand { Id = testId };

        _mockBulkImportOperationRepository.Setup(x => x.GetByReferenceIdAsync(testId))
            .ReturnsAsync(_bulkImportOperationFixture.BulkImportOperations.First());

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.GetByReferenceIdAsync(testId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_UpdateSuccessful()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var command = new UpdateBulkImportOperationCommand { Id = existingOperation.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<UpdateBulkImportOperationResponse>();
        result.GetType().ShouldBe(typeof(UpdateBulkImportOperationResponse));
    }

    [Fact]
    public async Task Handle_UpdateStatusAndTimes_When_CommandProvided()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var startTime = DateTime.Now.AddHours(-2);
        var endTime = DateTime.Now;
        var command = new UpdateBulkImportOperationCommand 
        { 
            Id = existingOperation.ReferenceId,
            Status = "Running",
            StartTime = startTime,
            EndTime = endTime
        };

        Domain.Entities.BulkImportOperation capturedEntity = null;
        
        _mockBulkImportOperationRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperation>()))
      .Callback<Domain.Entities.BulkImportOperation>(operation => capturedEntity = operation)
      .ReturnsAsync((Domain.Entities.BulkImportOperation operation) => operation);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEntity.Status.ShouldBe("Running");
        capturedEntity.StartTime.ShouldBe(startTime);
        capturedEntity.EndTime.ShouldBe(endTime);
    }

    [Fact]
    public async Task Handle_UpdateDescription_When_CommandProvided()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var newDescription = "Updated bulk import operation description";
        var command = new UpdateBulkImportOperationCommand 
        { 
            Id = existingOperation.ReferenceId,
            Description = newDescription
        };

        Domain.Entities.BulkImportOperation capturedEntity = null;
        
        _mockBulkImportOperationRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperation>()))
      .Callback<Domain.Entities.BulkImportOperation>(operation => capturedEntity = operation)
      .ReturnsAsync((Domain.Entities.BulkImportOperation operation) => operation);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEntity.Description.ShouldBe(newDescription);
    }
}
