﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.SiteLocationModel.SiteLocationViewModel

<div class="modal-dialog modal-sm modal-dialog-centered">
    <div class="modal-content">
        <form  asp-action="Delete" asp-route-id="textDeleteId" enctype="multipart/form-data">
            <div class="modal-header p-0">
                <img class="delete-img" src="~/img/isomatric/delete.png" alt="Delete Img" />
            </div>
            <div class="modal-body text-center pt-0">
                <h5 class="fw-semibold">Are you sure?</h5>
                <p class="d-flex gap-1 align-items-center justify-content-center">You want to delete the <span class="font-weight-bolder text-primary text-truncate d-inline-block" style="max-width: 110px;" id="deleteData" ></span> data?</p>
                <input  type="hidden" id="textDeleteId" name="id" class="form-control" />
            </div>
            <div class="modal-footer gap-1 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="confirmDeleteButton">Yes</button>
            </div>
        </form>
    </div>
</div> 