﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Template.Events.Delete;

public class TemplateDeletedEventHandler : INotificationHandler<TemplateDeletedEvent>
{
    private readonly ILogger<TemplateDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public TemplateDeletedEventHandler(IUserActivityRepository userActivityRepository,
        ILogger<TemplateDeletedEventHandler> logger, ILoggedInUserService userService)
    {
        _userActivityRepository = userActivityRepository;
        _logger = logger;
        _userService = userService;
    }

    public async Task Handle(TemplateDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var result = await _userActivityRepository.AddAsync(
            new Domain.Entities.UserActivity
            {
                UserId = _userService.UserId,
                LoginName = _userService.LoginName,
                RequestUrl = _userService.RequestedUrl,
                CompanyId = _userService.CompanyId,
                HostAddress = _userService.IpAddress,
                Action = $"{ActivityType.Delete} {Modules.Template}",
                Entity = Modules.Template.ToString(),
                ActivityType = ActivityType.Delete.ToString(),
                ActivityDetails = $"Template '{deletedEvent.TemplateName}' Deleted successfully."
            });

        _logger.LogInformation($"Template '{deletedEvent.TemplateName}' Deleted successfully.");
    }
}