using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class BackUpLogFixture : IDisposable
{
    public List<BackUpLog> BackUpLogPaginationList { get; set; }
    public List<BackUpLog> BackUpLogList { get; set; }
    public BackUpLog BackUpLogDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public BackUpLogFixture()
    {
        var fixture = new Fixture();

        BackUpLogList = fixture.Create<List<BackUpLog>>();

        BackUpLogPaginationList = fixture.CreateMany<BackUpLog>(20).ToList();

        BackUpLogDto = fixture.Create<BackUpLog>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
