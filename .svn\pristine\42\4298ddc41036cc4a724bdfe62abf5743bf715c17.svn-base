using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ApprovalMatrixApprovalFixture : IDisposable
{
    public List<ApprovalMatrixApproval> ApprovalMatrixApprovalPaginationList { get; set; }
    public List<ApprovalMatrixApproval> ApprovalMatrixApprovalList { get; set; }
    public ApprovalMatrixApproval ApprovalMatrixApprovalDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public ApprovalMatrixApprovalFixture()
    {
        var fixture = new Fixture();

        ApprovalMatrixApprovalList = fixture.Create<List<ApprovalMatrixApproval>>();

        ApprovalMatrixApprovalPaginationList = fixture.CreateMany<ApprovalMatrixApproval>(20).ToList();

        ApprovalMatrixApprovalDto = fixture.Create<ApprovalMatrixApproval>();


        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
