﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowOperationGroupRepositoryMocks
{
    public static Mock<IWorkflowOperationGroupRepository> CreateWorkflowOperationGroupRepository(List<WorkflowOperationGroup> workflowOperationGroups)
    {
        var mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();

        mockWorkflowOperationGroupRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowOperationGroups);

        mockWorkflowOperationGroupRepository.Setup(repo => repo.AddRange((IEnumerable<WorkflowOperationGroup>)It.IsAny<WorkflowOperationGroup>()));

        mockWorkflowOperationGroupRepository.Setup(repo => repo.AddAsync(It.IsAny<WorkflowOperationGroup>())).ReturnsAsync(
            (WorkflowOperationGroup workflowOperationGroup) =>
            {
                workflowOperationGroup.Id = new Fixture().Create<int>();

                workflowOperationGroup.ReferenceId = new Fixture().Create<Guid>().ToString();

                workflowOperationGroups.Add(workflowOperationGroup);

                return workflowOperationGroup;
            });

        return mockWorkflowOperationGroupRepository;
    }

    public static Mock<IWorkflowOperationGroupRepository> DeleteWorkflowOperationGroupRepository(List<WorkflowOperationGroup> workflowOperationGroups)
    {
        var mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();

        mockWorkflowOperationGroupRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowOperationGroups);

        mockWorkflowOperationGroupRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowOperationGroups.SingleOrDefault(x => x.ReferenceId == i));

        mockWorkflowOperationGroupRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowOperationGroup>())).ReturnsAsync((WorkflowOperationGroup workflowOperationGroup) =>
        {
            var index = workflowOperationGroups.FindIndex(item => item.ReferenceId == workflowOperationGroup.ReferenceId);
            workflowOperationGroup.IsActive = false;
            workflowOperationGroups[index] = workflowOperationGroup;

            return workflowOperationGroup;
        });

        return mockWorkflowOperationGroupRepository;
    }

    public static Mock<IWorkflowOperationGroupRepository> UpdateWorkflowOperationGroupRepository(List<WorkflowOperationGroup> workflowOperationGroups)
    {
        var mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();

        mockWorkflowOperationGroupRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowOperationGroups);

        mockWorkflowOperationGroupRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowOperationGroups.SingleOrDefault(x => x.ReferenceId == i));

        mockWorkflowOperationGroupRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowOperationGroup>())).ReturnsAsync((WorkflowOperationGroup workflowOperationGroup) =>
        {
            var index = workflowOperationGroups.FindIndex(item => item.Id == workflowOperationGroup.Id);

            workflowOperationGroups[index] = workflowOperationGroup;

            return workflowOperationGroup;
        });

        return mockWorkflowOperationGroupRepository;
    }

    public static Mock<IWorkflowOperationGroupRepository> GetWorkflowOperationGroupRepository(List<WorkflowOperationGroup> workflowOperationGroups)
    {
        var mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();

        mockWorkflowOperationGroupRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowOperationGroups);

        mockWorkflowOperationGroupRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowOperationGroups.FirstOrDefault(x => x.ReferenceId == i));

        return mockWorkflowOperationGroupRepository;
    }

    public static Mock<IWorkflowOperationGroupRepository> GetWorkflowOperationGroupEmptyRepository()
    {
        var mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();

        mockWorkflowOperationGroupRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowOperationGroup>());

        return mockWorkflowOperationGroupRepository;
    }

    public static Mock<IWorkflowOperationGroupRepository> GetWorkflowOperationGroupNamesRepository(List<WorkflowOperationGroup> workflowOperationGroups)
    {
        var mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();

        mockWorkflowOperationGroupRepository.Setup(repo => repo.GetWorkflowOperationGroupNames()).ReturnsAsync(workflowOperationGroups);

        return mockWorkflowOperationGroupRepository;
    }

    public static Mock<IWorkflowOperationGroupRepository> GetWorkflowOperationGroupNameUniqueRepository(List<WorkflowOperationGroup> workflowOperationGroups)
    {
        var mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();

        mockWorkflowOperationGroupRepository.Setup(repo => repo.IsWorkflowOperationGroupNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => workflowOperationGroups.Exists(x => x.WorkflowName == i && x.ReferenceId == j));

        return mockWorkflowOperationGroupRepository;
    }

    public static Mock<IWorkflowOperationGroupRepository> GetPaginatedWorkflowOperationGroupRepository(List<WorkflowOperationGroup> workflowOperationGroups)
    {
        var mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();

        var queryableWorkflowOperationGroup = workflowOperationGroups.BuildMock();

        mockWorkflowOperationGroupRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableWorkflowOperationGroup);

        return mockWorkflowOperationGroupRepository;
    }

    public static Mock<IWorkflowOperationGroupRepository> GetByWorkflowOperationIdRepository(List<WorkflowOperationGroup> workflowOperationGroups)
    {
        var mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();

        mockWorkflowOperationGroupRepository.Setup(repo => repo.GetWorkflowOperationGroupByWorkflowOperationId(It.IsAny<string>())).ReturnsAsync((string i) => workflowOperationGroups.Where(x => x.WorkflowOperationId == i && x.IsActive).ToList());

        return mockWorkflowOperationGroupRepository;
    }

    public static Mock<IWorkflowOperationGroupRepository> GetWorkflowOperationGroupByWorkflowOperationId(List<WorkflowOperationGroup> workflowOperationGroups)
    {
        var mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();

        mockWorkflowOperationGroupRepository.Setup(repo => repo.GetWorkflowOperationGroupByWorkflowOperationId(It.IsAny<string>())).ReturnsAsync((string i) => workflowOperationGroups.Where(x => x.WorkflowOperationId == i && x.IsActive).ToList());

        return mockWorkflowOperationGroupRepository;
    }

    public static Mock<IWorkflowOperationGroupRepository> GetWorkflowOperationGroupByInfraObjectIdRepository(List<WorkflowOperationGroup> workflowOperationGroups)
    {
        var mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();

        mockWorkflowOperationGroupRepository.Setup(repo => repo.GetWorkflowOperationGroupByInfraObjectId(It.IsAny<string>())).ReturnsAsync((string i) => workflowOperationGroups.Where(x => x.WorkflowOperationId == i && x.IsActive).ToList());

        return mockWorkflowOperationGroupRepository;
    }

    public static Mock<IWorkflowOperationGroupRepository> GetWorkflowOperationGroupByNodeIdRepository(List<WorkflowOperationGroup> workflowOperationGroups)
    {
        var mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();

        mockWorkflowOperationGroupRepository.Setup(repo => repo.GetWorkflowOperationGroupByWorkflowOperationIdAndNodeId(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => workflowOperationGroups.Where(x => x.WorkflowOperationId == i && x.NodeId == j).ToList());

        return mockWorkflowOperationGroupRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateWorkflowOperationGroupEventRepository(List<UserActivity> userActivities)
    {
        var workflowOperationGroupEventRepository = new Mock<IUserActivityRepository>();

        workflowOperationGroupEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return workflowOperationGroupEventRepository;
    }
}