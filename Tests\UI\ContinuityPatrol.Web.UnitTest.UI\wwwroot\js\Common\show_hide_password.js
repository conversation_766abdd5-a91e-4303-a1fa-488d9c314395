﻿$(document).ready(function () {
    //$(".toggle-password").click(function () {
    //    let input = $(this).prev();
    //    let icon = $(this).find("i");
    //    if (input.attr("type") === "password") {
    //        input.attr("type", "text");
    //        icon.removeClass("cp-password-visible").addClass("cp-password-hide");
    //    } else {
    //        input.attr("type", "password");
    //        icon.removeClass("cp-password-hide").addClass("cp-password-visible");
    //    }
    //});

    var message = $(this).hasClass('fs-6 cp-password-visible') ? 'Hide Password' : 'Show Password';
    $('.toggle-password').attr('title', message);
    $('.toggle-password').on("click", function () {

        var message = $(this).hasClass('fs-6 cp-password-visible') ? 'Hide Password' : 'Show Password';
                    $(this).attr('title', message);
        if ($(this).hasClass('fs-6 cp-password-visible')) {

            $(this).removeClass('fs-6 cp-password-visible');

            $(this).addClass('fs-6 cp-password-hide');   

                    $('#txtPassword').attr('type', 'text');

                } else {
            $(this).removeClass("fs-6 cp-password-hide");

            $(this).addClass('fs-6 cp-password-visible');

                    $('#txtPassword').attr('type', 'password');
                }
            });

    $('.toggle-password1').on("click",function () {
        if ($(this).hasClass('cp-password-visible')) {

            $(this).removeClass('cp-password-visible');

            $(this).addClass('cp-password-hide');

            $('.txtPassword1').attr('type', 'text');

        } else {

            $(this).removeClass("cp-password-hide");

            $(this).addClass('cp-password-visible');

            $('.txtPassword1').attr('type', 'password');
        }
    });
    $('.toggle-password2').on("click",function () {
        if ($(this).hasClass('cp-password-visible')) {

            $(this).removeClass('cp-password-visible');

            $(this).addClass('cp-password-hide');

            $('.txtPassword2').attr('type', 'text');

        } else {

            $(this).removeClass("cp-password-hide");

            $(this).addClass('cp-password-visible');

            $('.txtPassword2').attr('type', 'password');
        }
    });

    //$('.toggle-password3').on("click",function () {
    //    if ($(this).hasClass('cp-password-visible')) {

    //        $(this).removeClass('cp-password-visible');

    //        $(this).addClass('cp-password-hide');

    //        $('.txtPassword3').attr('type', 'text');

    //    } else {

    //        $(this).removeClass("cp-password-hide");

    //        $(this).addClass('cp-password-visible');

    //        $('.txtPassword3').attr('type', 'password');
    //    }
    //});
    //$('.toggle-password4').on("click",function () {
    //    if ($(this).hasClass('cp-password-visible')) {

    //        $(this).removeClass('cp-password-visible');

    //        $(this).addClass('cp-password-hide');

    //        $('.txtPassword4').attr('type', 'text');

    //    } else {

    //        $(this).removeClass("cp-password-hide");

    //        $(this).addClass('cp-password-visible');

    //        $('.txtPassword4').attr('type', 'password');
    //    }
    //});
    $("#showPass").on("click",function () {
        if ($("#mycPass").attr("type") == "password") {
            $("#mycPass").attr("type", "text");
        } else {
            $("#mycPass").attr("type", "password");
        }
    });
    $("#showPass").on("click",function () {
        $("#showPass i").toggle();
    });

    $("#showPass1").on("click",function () {
        if ($("#myPass").attr("type") == "password") {
            $("#myPass").attr("type", "text");
        } else {
            $("#myPass").attr("type", "password");
        }
    });
    $("#showPass1").on("click",function () {
        $("#showPass1 i").toggle();
    });
});


