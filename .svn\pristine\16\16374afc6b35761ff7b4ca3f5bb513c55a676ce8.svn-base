﻿using ContinuityPatrol.Application.Features.Database.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Tests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Database.Commands;

public class CreateDatabaseTests : IClassFixture<DatabaseFixture>, IClassFixture<ServerFixture>, IClassFixture<LicenseManagerFixture>,
    IClassFixture<SiteFixture>, IClassFixture<SiteTypeFixture>
{
    private readonly DatabaseFixture _databaseFixture;

    private LicenseManagerFixture _licenseManagerFixture;

    private readonly ServerFixture _serverFixture;

    private readonly SiteFixture _siteFixture;

    private readonly SiteTypeFixture _siteTypeFixture;

    private readonly Mock<IDatabaseRepository> _mockDatabaseRepository;

    public readonly Mock<ILicenseManagerRepository> _mockLicenseManagerServiceRepository;

    public readonly Mock<IServerRepository> _mockServerRepository;

    private readonly Mock<ISiteRepository> _mockSiteRepository;

    private readonly Mock<ISiteTypeRepository> _mockSiteTypeRepository;


    private readonly CreateDatabaseCommandHandler _handler;

    public CreateDatabaseTests(DatabaseFixture databaseFixture, ServerFixture serverFixture, LicenseManagerFixture licenseManagerFixture, 
        SiteFixture siteFixture, SiteTypeFixture siteTypeFixture)
    {
        _databaseFixture = databaseFixture;

        _serverFixture = serverFixture;
        _licenseManagerFixture = licenseManagerFixture;
        _siteFixture = siteFixture;
        _siteTypeFixture = siteTypeFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockSiteTypeRepository = SiteTypeRepositoryMocks.GetSiteTypeRepository(_siteTypeFixture.SiteTypes);

        _mockSiteRepository = SiteRepositoryMocks.GetSiteRepository(_siteFixture.Sites);

        var mockLoggedInUserService = LoggedInUserServiceRepositoryMocks.GetLoggedInUserServiceRepository();

        _mockDatabaseRepository = DatabaseRepositoryMocks.CreateDatabaseRepository(_databaseFixture.Databases);

        _mockLicenseManagerServiceRepository =
            LicenseManagerRepositoryMocks.GetLicenseManagerRepository(_licenseManagerFixture.LicenseManagers);

        _mockServerRepository = ServerRepositoryMocks.GetServerRepository(_serverFixture.Servers);

        _handler = new CreateDatabaseCommandHandler(_databaseFixture.Mapper, _mockDatabaseRepository.Object, mockPublisher.Object, 
            mockLoggedInUserService.Object, _mockLicenseManagerServiceRepository.Object, _mockServerRepository.Object, _mockSiteRepository.Object, 
            _mockSiteTypeRepository.Object);

        //_databaseFixture.LicenseManagers[0].PONumber = "PO-3355";
        //_databaseFixture.CreateDatabaseCommand.LicenseKey = _databaseFixture.LicenseManagers[0].PONumber;

        _databaseFixture.Server.ReferenceId = "45390667-2fb7-4bfd-8712-71af5e7dd548";
        _databaseFixture.CreateDatabaseCommand.ServerId = "45390667-2fb7-4bfd-8712-71af5e7dd548";

        _databaseFixture.CreateDatabaseCommand.LicenseId = _licenseManagerFixture.LicenseManagers[0].ReferenceId;
        _databaseFixture.CreateDatabaseCommand.ServerId = _serverFixture.Servers[0].ReferenceId;

        _serverFixture.Servers[0].IsAttached = true;

        _serverFixture.Servers[0].Properties =
            "{\"ThisisaPartofCluster\":false,\"VirtualIPAddress\":\"\",\"IpAddress\":\"************\",\"HostName\":\"TestSQL1\",\"ConnectViaHostName\":false,\"Port\":\"22\",\"VirtualGuestOS\":false,\"AuthenticationType\":\"SshPassword\",\"SSOEnabled\":false,\"@@singlesignon_name\":\"\",\"@@singlesignon_nameID\":\"\",\"signonprofile\":\"Select Profile\",\"signonprofileID\":\"\",\"SSHUser\":\"Administrator\",\"SSHPassword\":\"PwU1qkHaBLnZz68p6vQm5KhgnAnDAB046qRWyzwjFLc=$6KqUMEqfIdCPtbfTm4oQsgIe7y9cZv0CQPJa3mNfFTHZQihzbg==\",\"SSHKeyUser\":\"\",\"SSHKeyPath\":\"\",\"SSHKeyPassword\":\"\",\"WMIUser\":\"\",\"WMIPassword\":\"\",\"PowerShellUser\":\"\",\"PowerShellPassword\":\"\",\"WinRMPort\":\"\",\"ProxyAccessType\":\"\",\"ShellPrompt\":\"$\",\"isASMGrid\":false,\"ASMInstance\":\"\",\"icon\":\"cp-windows\"}";

        _siteFixture.Sites[0].ReferenceId = _serverFixture.Servers[0].SiteId;

        _siteTypeFixture.SiteTypes[0].ReferenceId = _siteFixture.Sites[0].TypeId;

        _siteTypeFixture.SiteTypes[0].Category = "primary";
    }

    [Fact]
    public async Task Handle_IncreaseDatabaseCount_When_DatabaseCreated()
    {
        await _handler.Handle(_databaseFixture.CreateDatabaseCommand, CancellationToken.None);

        var allCategories = await _mockDatabaseRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_databaseFixture.Databases.Count);
    }

    [Fact]
    public async Task Handle_Return_CreateDatabaseResponse_When_DatabaseCreated()
    {
        var result = await _handler.Handle(_databaseFixture.CreateDatabaseCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateDatabaseResponse));

        result.DatabaseId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_databaseFixture.CreateDatabaseCommand, CancellationToken.None);

        _mockDatabaseRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.Database>()), Times.Once);
    }
}