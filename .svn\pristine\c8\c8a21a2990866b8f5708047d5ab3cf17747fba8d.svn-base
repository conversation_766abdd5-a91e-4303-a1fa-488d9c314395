﻿@using ContinuityPatrol.Shared.Core.Enums;
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@model ContinuityPatrol.Domain.ViewModels.DRCalendar.DrCalendarActivityViewModel
<link href="~/lib/dr-calendar/fullcalendar/fullcalendar.min.css" rel="stylesheet" />
<style>
    #recipient-drop-down > .select2-container {
        max-width: 355px;
        overflow: hidden;
    }
</style>
<div class="page-content">
    <div class="card Card_Design_None mb-0">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-dr-calendar"></i><span>DR Calendar</span></h6>
            <div>
                <button class="btn btn-sm btn-primary" id="create_btn">Create</button>
            </div>
        </div>
        <div class="card-body pt-0">
            <div class="row">
                <div class="col-xl-3">
                    <div class="dr-calender">
                        <h6 class="page_title mb-2">Upcoming Drill Activity</h6>
                        <div>
                            <div id='external-events-list' style="height: calc(100vh - 175px); overflow-y: scroll;">
                                <div id="tilesContainer" class="pe-2">
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-2">
                                <div>
                                    <span class="badge text-bg-primary me-1">
                                        <span style="visibility: hidden;"></span>
                                    </span>Upcoming Activity
                                </div>
                                <div>
                                    <span class="badge  text-bg-success me-1">
                                        <span style="visibility: hidden;"></span>
                                    </span>Today Activity
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-9">
                    <div style="height: calc(100vh - 152px); overflow-y: scroll;">
                        <div id="calender" class="fc fc-unthemed fc-ltr">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal" tabindex="-1" id="myModal">
        <div class="modal-dialog">
            <div class="modal-content" id="custom-modal">
                <div class="modal-header">
                    <h6 class="page_title" title="DR Calendar"><i class="cp-dr-calendar"></i><span id="eventTitle"></span></h6>
                    <button type="button" id="modal-footer-close-btn" class="btn-close" data-bs-dismiss="modal" aria-label="Close" onclick="$('#myModal').modal('hide');"></button>
                </div>
                <div class="modal-body " id="modal-body">
                    <p id="pDetails"></p>
                    <table class="table " style="table-layout: fixed;">
                        <tbody>
                            <tr>
                                <td class="text-light">Start Date</td>
                                <td class="list-title" id="startDate"></td>
                            </tr>
                            <tr>
                                <td class="text-light">End Date</td>
                                <td class="list-title" id="endDate"></td>
                            </tr>
                            <tr>
                                <td class="text-light">Description</td>
                                <td class="list-title" id="descriptionText"></td>
                            </tr>
                            <tr>
                                <td class="text-light">Profile Name</td>
                                <td class="list-title" id="profileName"></td>
                            </tr>
                        </tbody>
                    </table>
                    <p hidden style="width:100%; padding: 0rem 0.5rem;">
                        <b class="text-light">File Name:</b>
                        <span id="fileName"></span>
                        <i class="cp-download" id="downloadFile-btn" style="cursor:pointer; margin-left: 8px;"></i>
                    </p>
                </div>
                <div class="modal-footer">
                    <button id="btnDelete" class="btn btn-sm btn-secondary t">
                        <span class="glyphicon glyphicon-remove"></span> Delete
                    </button>
                    <button id="btnEdit" class="btn btn-primary btn-sm pull-right" style="margin-right:5px;">
                        <span class="glyphicon glyphicon-pencil"></span> Edit
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!--Modal Create-->
    <div class="modal fade" data-bs-backdrop="static" id="myModalSave" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title" title="DR Calendar Configuration"><i class="cp-dr-calendar"></i><span>DR Calendar Configuration</span></h6>
                    <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createDRCalendar" asp-area="Configuration" autocomplete="off" asp-controller="DRCalendar" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-xl-6">
                                <div class="mb-3 form-group">
                                    <div class="form-label">
                                        Activity Name
                                    </div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-name"></i></span>
                                        <input type="text" asp-for="ActivityName" id="drActivityName" required class="form-control" maxlength="100" placeholder="Enter Activity Name">
                                        <input asp-for="Id" type="hidden" id="textrefId" />
                                    </div>
                                    <span asp-validation-for="ActivityName" id="drActivityNameError"></span>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="mb-3 ">
                                    <div class="mb-3 form-group">
                                        <div class="form-label">Description<small class="text-secondary">( Optional )</small></div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-description"></i></span>
                                            <input type="text" asp-for="Description" id="drDescription" class="form-control" data-placeholder="Enetr Description" maxlength="250" placeholder="Description" />
                                        </div>
                                        <span asp-validation-for="Description" id="drDescriptionError"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="mb-3 form-group">
                                    <div class="form-label">Operational Service</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-bag"></i></span>
                                        <select asp-for="BusinessServiceId" id="infraBusinessServiceId" class="form-select-modal" data-placeholder="Select Operational Service" aria-label="Default select example" required>
                                        </select>
                                    </div>
                                    <span asp-validation-for="BusinessServiceId" id="BusinessServiceId-error"></span>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="mb-3">
                                    <div class="mb-3 form-group">
                                        <div class="form-label">Workflow Profile </div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-user-profile"></i></span>
                                            <select id="drWorkFlowsProfile" class="form-select-modal" data-placeholder="Select Workflow Profile" required multiple>
                                            </select>
                                        </div>
                                        <input asp-for="WorkflowProfiles" type="hidden" id="ProfileData" />
                                        <span asp-validation-for="WorkflowProfiles" id="profilesError"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="mb-3 form-group">
                                    <div class="form-label">Activity Type</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-activity-type"></i></span>
                                        <select asp-for="ActivityType" id="drActivityType" class="form-select-modal" data-placeholder="Select Activity Type" class="form-control" required>
                                            <option value="" disabled selected>Select Activity Type</option>
                                            <option value="Audits">Audits</option>
                                            <option value="BIA">BIA</option>
                                            <option value="Budget">Budget</option>
                                            <option value="component Resources"> component Resources</option>
                                            <option value="Drill">Drill</option>
                                            <option value="External Learning And Sharing">External Learning And Sharing</option>
                                            <option value="Exercise">Exercise</option>
                                            <option value="Incidents">Incidents</option>
                                            <option value="Improvements Made In BCMS">Improvements Made In BCMS</option>
                                            <option value="ISO 22301 Certification Audit">ISO 22301 Certification Audit</option>
                                            <option value="Management Reviews">Management Reviews</option>
                                            <option value="Meeting">Meeting</option>
                                            <option value="Meetings With Authorities">Meetings With Authorities</option>
                                            <option value="Program Plan">Program Plan</option>
                                            <option value="Policy">Policy</option>
                                            <option value="RA">RA</option>
                                            <option value="Regulatory OR Compliance Notices">Regulatory OR Compliance Notices</option>
                                            <option value="Trainings">Trainings</option>
                                            <option value="Third Party Assessments">Third Party Assessments</option>
                                        </select>
                                    </div>
                                    <span asp-validation-for="ActivityType" id="drActivityTypeError"></span>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="mb-3 form-group">
                                    <div class="form-label">Activity Status</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-activity-type"></i></span>
                                        <select class="form-select-modal" asp-for="ActivityStatus" class="form-select-modal" id="drActivityStatus" data-placeholder="Select Activity Status" required>
                                            <option value="" disabled selected>Select ActivityStatus </option>
                                            <option value="immediate">immediate</option>
                                            <option value="Scheduled">Scheduled</option>
                                        </select>
                                    </div>
                                    <span asp-validation-for="ActivityStatus" id="drActivityStatusError"></span>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="mb-3">
                                    <div class="mb-3 form-group">
                                        <div class="form-label">Recipient</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-user"></i></span>
                                            <select id="drUserName" class="form-select-modal" data-placeholder="Select Recipient" required multiple>
                                            </select>
                                        </div>
                                        <input asp-for="Responsibility" type="hidden" id="drResponsibilityData" />
                                        <span asp-validation-for="Responsibility" id="drResponsibilityError"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label class="form-label">Recipient<small class="text-secondary">( Optional )</small></label>
                                    <div class="input-group" id="recipient-drop-down">
                                        <i class="cp-user"></i><select id="drUserNameRecipient" aria-label="Default select example" class="form-select-modal" data-placeholder="Select Recipients" multiple required style="background-color:pink; max-width:350px;">
                                        </select>
                                    </div>
                                    <input asp-for="RecipientTwo" type="hidden" id="RecipientData" />
                                    <span asp-validation-for="RecipientTwo" id="Recipient2Error"></span>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="mb-3 form-group">
                                    <div class="form-label">
                                        Start Date
                                    </div>
                                    <div class="input-group date" id='dtp1'>
                                        <i class="cp-calendar"></i>
                                        <input id="drScheduledStartDate" class="form-control" type="datetime-local" name="sdate" placeholder="Enter Start Date" required />
                                    </div>
                                    <input asp-for="ScheduledStartDate" type="hidden" id="drStartDate" class="form-control" />
                                    <span asp-validation-for="ScheduledStartDate" id="drScheduledStartDateError"></span>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="mb-3 form-group">
                                    <div class="form-label">
                                        End Date
                                    </div>
                                    <div class="input-group date" id='dtp2'>
                                        <i class="cp-calendar"></i>
                                        <input id="drScheduledEndDate" class="form-control" placeholder="Enter End Date" name="edate" type="datetime-local" cursorshover="true" required step="1800" />
                                    </div>
                                    <input asp-for="ScheduledEndDate" type="hidden" id="drEndDate" class="form-control" />
                                    <span asp-validation-for="ScheduledEndDate" id="drScheduledEndDateError"></span>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="mb-3">
                                    <div class="mb-3 form-group">
                                        <div class="form-label">
                                            Set Reminder
                                        </div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-apply-finish-time"></i></span>
                                            <select class="form-select-modal" asp-for="SetReminders" id="drSetReminders" data-placeholder="Select Reminder" required>
                                                <option value="" disabled selected>Select  Set Reminders </option>
                                                <option value="Never">Never</option>
                                                <option value="15 minutes before">15 minutes before</option>
                                                <option value="30 minutes before">30 minutes before</option>
                                                <option value="45 minutes before">45 minutes before</option>
                                                <option value="1 hours before">1 hours before</option>
                                                <option value="2 hours before">2 hours before</option>
                                                <option value="3 hours before">3 hours before</option>
                                                <option value="1 day before">1 day before</option>
                                            </select>
                                        </div>
                                        <span asp-validation-for="SetReminders" id="setRemindersError"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="mb-3 ">
                                    <div class="mb-3 form-group">
                                        <div class="form-label">Location</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-location"></i></span>
                                            <input type="text" asp-for='Location' id="location" class="form-control" data-placeholder="Enetr Location" maxlength="500" placeholder="Enter Location" />

                                        </div>
                                        <span asp-for='Location' id="locationError"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="mb-3 ">
                                    <div class="mb-3 form-group">
                                        <div class="form-label">
                                            Upload File <small class="text-secondary">( Optional )</small>
                                        </div>
                                        <div class="input-group ">

                                            <input type="file" asp-for="File" class="form-control-sm w-100" id="textCompanyLogo" />
                                            <button type="button" class="btn btn-sm btn-danger rounded-1" id="removeImgss">Remove</button>
                                        </div>
                                        <span asp-validation-for="File" id="drUploadFileError"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary">
                        <i class="cp-note me-1"></i>Note: All fields are mandatory
                        except optional
                    </small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" onclick="$('#myModalSave').modal('hide');">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" id="btnCreateDRCalendar" >Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="configurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<div id="configurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<script src="~/lib/dr-calendar/multiselect/bootstrap-multiselect.js"></script>
<script src="~/js/Configuration/DR Calendar/scripts/moment.min.js"></script>
<script src="~/lib/dr-calendar/fullcalendar/fullcalendar.min.js"></script>
<script src="~/js/Configuration/DR Calendar/DRCal.js"></script>
<script>
    $(document).ready(function () {
        $('#btn_createDRCalender').on('click', async function () {
            if ($("varActivityName").value == "") {
                ("Please fill out the form before submitting!");
            } else {
            }
        });
    });
</script>
<style>
    #arrayPos - 0 {
        color: green
    }
    #icardbody iv {
        opacity: 1;
        transition: all 0.2s linear
    }
    #icardbody div:hover {
        background: #eee
    }
    #icardbody div.del {
        opacity: 0
    }
</style>
<script type="text/javascript">
</script>
@section Scripts {
    <script>
    </script>
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}
<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>
<div class="modal fade" tabindex="-1" id="EditModal" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content" id="custom-modal">
            <div class="modal-header">
                <h6 class="page_title" title="DR Calendar">
                    <i class="cp-dr-calendar"></i>
                    <span id="eventTitle"></span> Drill Activity
                </h6>
                <button type="button" id="modal-footer-close-btn" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="modal-body" style="min-height: calc(100vh - 120px);">
                <div id="Activitydata"></div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

