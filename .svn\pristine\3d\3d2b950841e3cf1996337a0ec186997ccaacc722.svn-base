﻿const isNameExits = "Configuration/FiaTemplates/IsTemplateNameExist"
const isimpactcatagoryNameExits = "Configuration/FiaTemplates/ImpactMasterNameExist"
const ImpactTypeMasterNameExist = "Configuration/FiaTemplates/ImpactTypeMasterNameExist"
let createPermission = $("#ConfigurationCreate").data("create-permission").toLowerCase();
let deletePermission = $("#ConfigurationDelete").data("delete-permission").toLowerCase();
let selectedValues = [], globalId = "", globalDeletelistid = '', globalDeleteId = '', globalTimeIntervalId = '', globalimpactmasterId = '', globalimpactmasterDeleteId = '', globalimpacttypeId = '', globalimpacttypeDeleteId = '', arr = [], Create_deleteid, dataTable, impactdata, impactTypedata, timeIntervaldata, propertydata, intervaldata
function Fiadebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}
if (createPermission == 'false') {
    $("#createmodal").removeClass('#createmodal').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
}
$( function () {
    dataTable = $('#overallTable_List').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }, infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/Configuration/FiaTemplates/GetPaginatedFiaTemplatesList",
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "userName" : sortIndex === 5 ? "createdDate" : ""
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                      
                        json.recordsTotal = json.data.totalPages;
                        json.recordsFiltered = json.data.totalCount;
                        Create_deleteid = json.data.data
                        propertydata = json.data.data
                        if (json.data.data.length === 0) {
                            $(".pagination-column").addClass("disabled")                        
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")                    
                        }
                        return json.data.data;
                    } else {
                        errorNotification(json)
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [1, 4, 5, 3],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {

                            var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
                            return (page * meta.settings._iDisplayLength) + meta.row + 1;
                        }
                        return data;
                    }, "orderable": false,

                },
                {
                    "data": "name", "name": "Template Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title="${data == null ? "NA" : data}"> ${data == null ? "NA" : data}</span></td>`
                    }
                },
                {
                    "data": "userName",
                    "name": "Username",
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span> ${data == null ? "NA" : data}</span></td>`
                    }
                },
                {
                    "data": null,
                    "name": "Template In Used",
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><i class="cp-time me-1"></i>No</td>`;
                    }
                },
                {
                    "data": null, "name": "Template Used By", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td>NA</td>`;
                    }
                },
                {
                    "data": "createdDate", "name": "Created Date", "autoWidth": true,
                    "render": function (data, type, row) {
                        let datemod = data.split("T").join(" ").split(".")[0].split(" ")
                        let fia_date = datemod[0].split("-")
                        return `<td><div class="d-flex align-items-center  gap-2">${fia_date[2] + "-" + fia_date[1] + "-" + fia_date
                        [0] + " " + datemod[1]}</div></td>`;
                    }
                },
                {
                    "render": function (data, type, row) {
                        if (createPermission === 'true' && deletePermission === "true") {

                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="edit_businessService-button" name="overallupdate" updateId="${row.id}" temp_name='${row.name}'  onclick="overall_Edit(this)" data-bs-toggle="modal" data-bs-target="#CreateModal">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="delete-bservice-button" deletename='${row.name}' deleteId='${row.id}' onclick="overalldeleteBtn(this)" data-bs-toggle="modal" data-bs-target="#overall_delete">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>
                        </td>`;

                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `<td>
                       <div class="d-flex align-items-center  gap-2">
                          <span role="button" title="Edit" class="edit_businessService-button" name="overallupdate" updateId="${row.id}" temp_name='${row.name}'  onclick="overall_Edit(this)" data-bs-toggle="modal" data-bs-target="#CreateModal">
                                    <i class="cp-edit"></i>
                                </span>                                                                                      
                                            <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>                                                
                                </div>
                                </td>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                               <span role="button" title="Delete" class="delete-bservice-button" deletename='${row.templateName}' deleteId='${row.id}' onclick="overalldeleteBtn(this)" data-bs-toggle="modal" data-bs-target="#overall_delete">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div></td>`;
                        }
                        else {
                            return `
                             <td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                </span>
                            </div>
                             </td>`;
                        }
                    },
                    "orderable": false,
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        }
    )
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });
})
$('#search-inp').on('keydown input', Fiadebounce(function (e) {
    if (e.key === '=' || e.key === 'Enter') {
        e.preventDefault();
        return false;
    }
    const nameCheckbox = $("#Name");
    const inputValue = $('#search-inp').val();
    if (nameCheckbox.is(':checked')) {
        selectedValues.push(nameCheckbox.val() + inputValue);
    }
    let currentPage = dataTable.page.info().page + 1;
    if (!isNaN(currentPage)) {
        dataTable.ajax.reload(function (json) {
            if ($('#search-inp').val().length === 0) {
                if (json?.data?.data?.length === 0) {
                    $('.dataTables_empty').text('No Data Found');
                }
            } else if (json?.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        }, false)
    }
}));
function validateDropDown(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    }else{
        errorElement.text("").removeClass('field-validation-error');
        return true;
    }
}
 async function validatenameDropDown(value, errorMsg, errorElement,url) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else if (value.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    } else {
        var url = RootUrl + url;
        var data = {};
        data.id = null;
        data.name = value
        const validationResults = [
            SpecialCharValidate(value),
            value.charAt(0) == "_" ? ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? ShouldNotBeginWithSpace(value) :
                OnlyNumericsValidate(value),
            minMaxlength(value),
            ShouldNotBeginWithSpace(value),
            ShouldNotBeginWithNumber(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            secondChar(value),
            await IsSameNameExist(url, data)
        ];
        const failedValidations = validationResults.filter(result => result !== true);
        if (failedValidations?.length > 0) {
            errorElement.text(failedValidations[0]).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
}
async function IsSameNameExist(url, inputValue) { 
    return !inputValue.name.trim() || $("#fia_template_save").text() == "Update" ? true : (await GetAsync(url, inputValue, OnError)) ? " Name already exists" : true;
}
$(".fia_template_cancel").on("click", function () {
    $('#fia_template_save').text("Save");
    $("#fia_template_name").val("")
    $("#fia_impact_category,#fia_impact_type,#fia_interval").val("").trigger("change")
    ClearFiaTemplateErrorElements()
})
function ClearFiaTemplateErrorElements() {
    $("#fia_template_name_error,#fia_impact_category_error,#fia_impact_type_error,#fia_interval_error").text('').removeClass('field-validation-error');
}
$('#fia_template_name').on('input',Fiadebounce(async function () {
    let value = await sanitizeInput($("#fia_template_name").val());
    $("#fia_template_name").val(value);
    validatenameDropDown(value, "Enter template name", $('#fia_template_name_error'),isNameExits);
}))
let impactcatagoryarr=[],impacttypearr = [],impactinterval=[]
$('#fia_impact_category').on('change', function () {
    var selectimpactcatagory = $(this).find('option:selected');
    impactcatagoryarr = []
    selectimpactcatagory.each(function () {
        let option = $(this);
        let obj = { ImpactCatagoryId: option.val(), ImpactCatagoryName: option.text() };
        impactcatagoryarr.push(obj)
    });
    overall_Edit("change_value")
    validateDropDown($(this).val(), "Select impact category", $('#fia_impact_category_error'));
})

$('#fia_impact_type').on('change', function () {
    var selectimpacttype = $(this).find('option:selected');
    impacttypearr = []
    selectimpacttype.each(function () {
        let obj = { ImpactTypeId: $(this).val(), ImpactTypeName: $(this).text(), catagoryid: $(this).attr("catagoryid"), catagoryname: $(this).attr("catagoryname") };
        impacttypearr.push(obj)
    });
    validateDropDown($(this).val(), "Select impact type", $('#fia_impact_type_error'));
})
$('#fia_interval').on('change', function () {
    var selectimpactinterval = $(this).find('option:selected');
    impactinterval = []
    selectimpactinterval.each(function () {
        let option = $(this);
        let obj = { IntervalId: option.val(), IntervalName: option.text() };
        impactinterval.push(obj)
    });
    
    validateDropDown($(this).val(), "Select interval", $('#fia_interval_error'));
})
$("#createmodal").on("click", function () { 
    overall_Edit()
})
$("#fia_template_save").on("click", async function () {
    
    let property = {
        Fiacatagory: impactcatagoryarr,
        FiaType: impacttypearr,
        FiaInterval: impactinterval
    }

    let form = $("#CreateModal");

    let istemplatename = await validatenameDropDown($("#fia_template_name").val(), "Enter template name", $('#fia_template_name_error'), isNameExits);

    let isimpactcategory = validateDropDown($("#fia_impact_category").val(), "Select impact category", $('#fia_impact_category_error'));

    let isimpact_type = validateDropDown($("#fia_impact_type").val(), "Select impact type", $('#fia_impact_type_error'));

    let isinterval = validateDropDown($("#fia_interval").val(), "Select interval", $('#fia_interval_error'));

    if (istemplatename & isimpactcategory & isimpact_type & isinterval) {
        form.trigger("submit")
        let data = {
            "Name": $("#fia_template_name").val(),
            "Description": "",
            "Properties": JSON.stringify(property),
            "UserName":"",
            "TemplateUsedBy": "",
            "TemplateInUsed": "",
            __RequestVerificationToken: gettoken()
        }
        $('#fia_template_save').text() === "Update" ? data["id"] = globalId : null
        await $.ajax({
            type: "POST",
            url: RootUrl + "Configuration/FiaTemplates/FiaTemplateCreateOrUpdate",
            dataType: "json",
            data: data,
            success: function (result) {
                let data = result?.data
                if (result?.success) {
                    $('#CreateModal').modal('hide');
                    notificationAlert("success", data?.message)
                    dataTable.ajax.reload()
                } else {
                    errorNotification(result)
                }
            },
        })
        $('#fia_template_save').text("Save");
        $("#fia_template_name").val("")
        $("#fia_impact_category,#fia_impact_type,#fia_interval").val("").trigger("change")
        ClearFiaTemplateErrorElements()
    }
})
function overall_Edit(data) {   
    $.ajax({
        type: "GET",
        url: RootUrl + "Configuration/FiaTemplates/GetList",
        dataType: "json",
        success: function (result) {          
            impactTypedata = result?.data?.impact
            impactdata = result?.data?.impactType
             timeIntervaldata = result?.data?.timeInterval
            if (result?.success) {
                if ($("#fia_template_save").text() != "Update") {
                    $('#fia_impact_type').empty().append('<option value=""></option>')
                    let dataVal = $('#fia_impact_category').val()
                    if (dataVal.length) {
                        dataVal?.forEach((x) => {
                            impactTypedata?.forEach(function (item) {
                                
                                if (item?.fiaImpactCategoryId === x) {
                                    $('#fia_impact_type').append('<option catagoryname="' + item.fiaImpactCategoryName +'" catagoryid="' + item.fiaImpactCategoryId +'" value="' + item?.id + '">' + item?.name + '</option>');
                                } 
                            });
                        })
                    }  
                    $('#fia_interval').empty().append('<option value=""></option>')
                    if (data != "change_value") {
                        $('#fia_impact_category').empty()
                        $('#fia_impact_category').append('<option value=""></option>')
                    }
                    $('#Impact_catagory').empty().append('<option value=""></option>')
                    impactdata?.forEach(function (item, i) {
                        $('#fia_impact_category').append('<option value="' + item?.id + '">' + item?.name + '</option>')
                        $('#Impact_catagory').append('<option value="' + item?.id + '">' + item?.name + '</option>')
                    })
                    timeIntervaldata?.forEach(function (item, i) {
                        if (item?.minTimeUnit == 1 && item?.maxTimeUnit == 2) {
                            let one = item?.minTime + " " + "Hours" + " " + "To" + " " + item?.maxTime + ' Days'
                            $("#fia_interval").append('<option value="' + item?.id + '">' + one + '</option>')
                        } else if (item?.minTimeUnit == 2 && item?.maxTimeUnit == 2) {
                            let two = item?.minTime + " " + "To" + " " + item?.maxTime + ' Days '
                            $("#fia_interval").append('<option value="' + item?.id + '">' + two + '</option>')
                        }
                        else {
                            let three = item?.minTime + " " + "To" + " " + item?.maxTime + ' Hours'
                            $("#fia_interval").append('<option value="' + item?.id + '">' + three + '</option>')
                        }
                    })
                } else {
                    timeIntervaldata.forEach(function (item, i) {
                        if (item?.minTimeUnit == 1 && item?.maxTimeUnit == 2) {
                            let one = item?.minTime + " " + "Hours" + " " + "To" + " " + item?.maxTime + ' Days'
                            $("#fia_interval").append('<option value="' + item?.id + '">' + one + '</option>')
                        } else if (item.minTimeUnit == 2 && item.maxTimeUnit == 2) {
                            let two = item?.minTime + " " + "To" + " " + item?.maxTime + ' Days '
                            $("#fia_interval").append('<option value="' + item?.id + '">' + two + '</option>')
                        }
                        else {
                            let three = item?.minTime + " " + "To" + " " + item?.maxTime + ' Hours'
                            $("#fia_interval").append('<option value="' + item?.id + '">' + three + '</option>')
                        }
                    })
                    impactdata?.forEach(function (item, i) {
                        $('#fia_impact_category').append('<option value="' + item?.id + '">' + item?.name + '</option>')
                        $('#Impact_catagory').append('<option value="' + item?.id + '">' + item?.name + '</option>')
                    }) 
                    impactTypedata?.forEach(function (item, i) {
                        $('#fia_impact_type').append('<option value="' + item?.id + '">' + item?.name + '</option>')
                    })
                }
                $("#Impact_catagory option,#fia_impact_category option,#fia_impact_type option,#fia_interval option").each(function () {
                    $(this).siblings('[value="' + this.value + '"]').remove()
                })
            } else {
                errorNotification(result)
            }
        },
    })
    if ($(data).attr("name") == "overallupdate") {
        let catagoryArr = [], typeArr = [], intervalArr = []
        $('#fia_template_save').text("Update");
        globalId = $(data).attr('updateId');
        $("#fia_template_name").val($(data).attr('temp_name'))
        propertydata?.forEach((x) => {
            if (x?.id == $(data).attr("updateId")) {
                if (x?.properties != null) {
                    let dat = JSON.parse(x?.properties)
                    dat?.Fiacatagory?.forEach((x) => {
                        catagoryArr.push(x.ImpactCatagoryId)
                    })
                    dat?.FiaType?.forEach((x) => {
                        typeArr.push(x.ImpactTypeId)
                    })
                    dat?.FiaInterval?.forEach((x) => {
                        intervalArr.push(x.IntervalId)
                    })
                        setTimeout(() => {
                            $('#fia_impact_category').val(catagoryArr).trigger('change')
                            $('#fia_impact_type').val(typeArr).trigger('change')
                            $('#fia_interval').val(intervalArr).trigger('change')
                        }, 500) 
                }
            }
        })
    }
}
function overalldeleteBtn(data) {
    globalDeletelistid = $(data).attr('deleteId')
    $("#overall_deleted_id").text($(data).attr('deletename')).attr("title", $(data).attr('deletename'));
}
$("#overall_confirmDeleteButton").on("click", async function () {
    await $.ajax({
        type: "POST",
        url: RootUrl + "Configuration/FiaTemplates/FiaTemplateDelete",
        dataType: "json",
        data: {
            id: globalDeletelistid,
            __RequestVerificationToken: gettoken() },
        success: function (result) { 
            let data = result?.data
            if (result?.success) {
                $('#overall_delete').modal('hide');
                notificationAlert("success", data?.message)
                dataTable.ajax.reload()
            } else {
                errorNotification(result)
            }
        },
    })
})
const getTimeInterval = () => {
    $("#timeintevaltable tbody").empty()
    $.ajax({
        type: "GET",
        url: RootUrl + "Configuration/FiaTemplates/GetTimeIntervalMasterList",
        dataType: "json",
        success: function (result) {
            let data = result?.data
            if (result?.success) {
                intervaldata=data
                data?.forEach(function (item, i) {
                    if (item?.minTimeUnit == 1 && item?.maxTimeUnit == 2) {
                        $("#timeintevaltable tbody ").append('<tr>' +
                            '<td  class="text-truncate" title="' + item?.minTime + " " + "Hours" + " " + "To" + " " + item?.maxTime +'">' + item?.minTime + " " + "Hours" + " " + "To" + " " + item?.maxTime + ' Days </td>' +
                            '<td><div class="d-flex align-items-center  justify-content-end   gap-2"><span role="button" onclick="TimeintervalupdateBtn(this)" maxTimeUnit=' + item.maxTimeUnit + ' minTimeUnit=' + item?.minTimeUnit + ' maxTime=' + item?.maxTime + ' minTime=' + item?.minTime + ' updateId=' + item?.id + '  title="Edit" class="edit_businessService-button"> <i class="cp-edit"></i></span><span role="button" title="Delete" class="delete-bservice-button" data-bs-toggle="modal"  onclick="TimeintervaldeleteBtn(this)" deletename="' + item?.minTime + " " + "Hours" + " " + "To" + " " + item?.maxTime + ' Days '+'" deleteId=' + item?.id + ' data-bs-target="#DeleteModal"> <i class="cp-Delete"></i></span></div></td>' +
                            '</tr>')
                    } else if (item?.minTimeUnit == 2 && item?.maxTimeUnit == 2) {
                        $("#timeintevaltable tbody ").append('<tr>' +
                            '<td  class="text-truncate  d-inline-block" title="' + item?.minTime + " " + "To" + " " + item?.maxTime + ' Days ">' + item?.minTime + " " + "To" + " " + item?.maxTime + ' Days </td>' +
                            '<td><div class="d-flex align-items-center  justify-content-end   gap-2"><span role="button" onclick="TimeintervalupdateBtn(this)" maxTimeUnit=' + item?.maxTimeUnit + ' minTimeUnit=' + item?.minTimeUnit + ' maxTime=' + item?.maxTime + ' minTime=' + item?.minTime + ' updateId=' + item?.id + '  title="Edit" class="edit_businessService-button"> <i class="cp-edit"></i></span><span role="button" title="Delete" class="delete-bservice-button" data-bs-toggle="modal"  onclick="TimeintervaldeleteBtn(this)" deletename="' + item?.minTime + " " + "To" + " " + item?.maxTime + ' Days'+'" deleteId=' + item?.id + ' data-bs-target="#DeleteModal"> <i class="cp-Delete"></i></span></div></td>' +
                            '</tr>')
                    }
                    else {
                        $("#timeintevaltable tbody ").append('<tr>' +
                            '<td  class="text-truncate" title="' + item?.minTime + " " + "To" + " " + item?.maxTime + ' Hours">' + item?.minTime + " " + "To" + " " + item?.maxTime + ' Hours </td>' +
                            '<td ><div class="d-flex align-items-center  justify-content-end   gap-2"><span role="button" onclick="TimeintervalupdateBtn(this)" maxTimeUnit=' + item.maxTimeUnit + ' minTimeUnit=' + item?.minTimeUnit + ' maxTime=' + item?.maxTime + ' minTime=' + item?.minTime + ' updateId=' + item?.id + '  title="Edit" class="edit_businessService-button"> <i class="cp-edit"></i></span><span role="button" title="Delete" class="delete-bservice-button" data-bs-toggle="modal"  onclick="TimeintervaldeleteBtn(this)" deletename="' + item?.minTime + " " + "To" + " " + item?.maxTime + ' Hours'+'" deleteId=' + item?.id + ' data-bs-target="#DeleteModal"> <i class="cp-Delete"></i></span></div></td>' +
                            '</tr>')
                    }
                })
            } else {
                errorNotification(result)
            }
        },
    })
}
function validateminmax(val1, val2) {
    val1 = Number(val1)
    val2 = Number(val2)
    if (!val1 && !val2) {
        $("#MinTime-error").text("Enter start time interval").addClass('field-validation-error');
        $("#MaxTime-error").text("Enter end time interval").addClass('field-validation-error');
        return false;
    } else if (val1 > val2 && val2 <= val1) {
        $('#MinTime-error').text("Enter value lesser than end time interval").addClass('field-validation-error');
        $('#MaxTime-error').text("Enter value greater than start time interval").addClass('field-validation-error');
        return false;
    } else if (val1.length >= 3) {
        $('#MinTime-error').text("Enter value 2 digit").addClass('field-validation-error');
        return false;
    } else if (val2.length >= 3) {
        $('#MaxTime-error').text("Enter value 2 digit").addClass('field-validation-error');
        return false;
    } else if (val1 == 0 && val2 == 0) {
        $("#MaxTime-error").text("Invalid schedule").addClass('field-validation-error');
        return false;
    } else if (intervaldata[0]?.maxTime >= Number(val1)) {
        if (intervaldata[0]?.maxTime <= Number(val1)){
            interval = true
        }
        if (interval == true) {
            $("#MinTime-error,#MaxTime-error").text('').removeClass('field-validation-error');
            return true;
        } else {
            $('#MinTime-error').text("Already configured with this interval").addClass('field-validation-error');
            return false;
        }
      
    } else {
        $("#MinTime-error,#MaxTime-error").text('').removeClass('field-validation-error');
        return true;
    }
}
function validatehoursDays(val1, val2) {
    if (val1.length == 0 || val2.length == 0) {
        $("#MinTimeUnit-error,#MaxTimeUnit-error").text("Enter hours or days").addClass('field-validation-error');
        return false;
    } else if (val1 == "Days" && val2 == "Hours") {
        $('#MinTimeUnit-error').text("Invalid schedule").addClass('field-validation-error');
        return false;
    } else {
        $("#MinTimeUnit-error,#MaxTimeUnit-error").text('').removeClass('field-validation-error');
        return true;
    }
}
$(".overall_cancel").on("click", function () {
    overall_Edit()
})
$("#TimeintervalcancelBtn").on("click", function () {
    $("#MinTime,#MaxTime").val("")
    $("#MinTimeUnit,#MaxTimeUnit").val("").trigger("change")
    Modalerror()
})
function Modalerror() {
    $("#MaxTimeUnit-error,#MinTimeUnit-error,#MaxTime-error,#MinTime-error").text('').removeClass('field-validation-error');
}
function setModalvalue() {
    let minimum = document.getElementById("MinTime"), maximum = document.getElementById("MaxTime"), minTimeUnit = document.getElementById("MinTimeUnit").value, maxTimeUnit = document.getElementById("MaxTimeUnit").value
    if (minTimeUnit == "Hours") {
        minimum.setAttribute("min", 0)
        minimum.setAttribute("max", 24)
    } else if (minTimeUnit == "Days") {
        minimum.setAttribute("min", 1)
        minimum.setAttribute("max", 31)
    } else if (maxTimeUnit == "Hours") {
        maximum.setAttribute("min", 0)
        maximum.setAttribute("max", 24)
    } else if (maxTimeUnit == "Days") {
        maximum.setAttribute("min", 1)
        maximum.setAttribute("max", 31)
    }
}
$('#MinTimeUnit,#MaxTimeUnit').on('change keyup keydown', function () {
    setModalvalue()
    let  minUnit = $("#MinTimeUnit").val(), maxUnit = $("#MaxTimeUnit").val()
    validatehoursDays(minUnit, maxUnit) 
})
$('#MinTime,#MaxTime').on('input', function () {
    interval=false
    let min = $("#MinTime").val(), max = $("#MaxTime").val()
    validateminmax(min, max)
})
$('#MinTime,#MaxTime').on('keypress', function (event) {
    ["e", "E", "+", "-", "."].includes(event.key) && event.preventDefault()
})
$('#TimeintervalsaveBtn').on('click', async function () {
    //setModalvalue()
    let form = $("#TimeIntervalModalToggle1")
    let min = $("#MinTime").val(), max = $("#MaxTime").val(), minUnit = $("#MinTimeUnit").val(), maxUnit = $("#MaxTimeUnit").val(), Time = validateminmax(min, max), Timeunit = validatehoursDays(minUnit, maxUnit)  
    if (Time & Timeunit) {
        form.trigger("submit")
        let data = {
            "MinTime": $("#MinTime").val(),
            "MaxTime": $("#MaxTime").val(),
            "MinTimeUnit": $("#MinTimeUnit").val() == "Hours" ? 1 : 2,
            "MaxTimeUnit": $("#MaxTimeUnit").val() == "Hours" ? 1 : 2,
            __RequestVerificationToken: gettoken()
        }
        $('#TimeintervalsaveBtn').text() === "Update" ? data["id"] = globalTimeIntervalId : null
        await $.ajax({
            type: "POST",
            url: RootUrl + "Configuration/FiaTemplates/TimeIntervalMasterCreateOrUpdate",
            dataType: "json",
            data: data,
            success: function (result) {
                let data = result?.data
                if (result?.success) {
                    notificationAlert("success", data?.message)
                    getTimeInterval()
                } else {
                    errorNotification(result)
                }
            },
        })
        $("#MinTime,#MaxTime").val("")
        $("#MinTimeUnit,#MaxTimeUnit").val("").change()
        $('#TimeintervalsaveBtn').text("Save") 
        Modalerror()
    }
})

$("#TimeIntervalModal").on("click", async function () {
    $('#TimeintervalsaveBtn').text("Save");
    await getTimeInterval()
})
function TimeintervaldeleteBtn(data) {
    arr =[]
    globalDeleteId = $(data).attr('deleteId')
    $("#deleted_id").text($(data).attr('deletename'))
    $("#deleted_id").attr("title", $(data).attr('deletename'));
    Create_deleteid?.forEach((item, i) => {
        if (item?.intervalId == globalDeleteId) {
            arr.push(item?.intervalId)
        }
    })
}
$("#confirmDeleteButton").on("click", async function () {
    if (arr.length == 0) {
        await $.ajax({
            type: "POST",
            url: RootUrl + "Configuration/FiaTemplates/TimeIntervalMasterDelete",
            dataType: "json",
            data: {
                id: globalDeleteId,
                __RequestVerificationToken: gettoken() },
            success: function (result) {
                let data = result?.data
                if (result?.success) {
                    notificationAlert("success", data.message)
                } else {
                    errorNotification(result)
                }
            },
        })
        getTimeInterval()
    } else {
        notificationAlert("warning", `The Interval '${$("#deleted_id").text()}'  is currently in use`)
    }
})
let interval =false
function TimeintervalupdateBtn(data) {
    $('#TimeintervalsaveBtn').text("Update");
    globalTimeIntervalId = $(data).attr('updateId');
    if ($(data).attr('updateId')) {
        interval =true
    }
    let MinTime = $(data).attr('minTime'), MaxTime = $(data).attr('maxTime'), MinTimeUnit = $(data).attr('minTimeUnit'), MaxTimeUnit = $(data).attr('maxTimeUnit')
    $("#MinTime").val(MinTime);
    $("#MaxTime").val(MaxTime);
    $("#MinTimeUnit").val(MinTimeUnit == 1 ? "Hours" : "Days").trigger("change") 
    $("#MaxTimeUnit").val(MaxTimeUnit == 1 ? "Hours" : "Days").trigger("change") 
}
//ImpactMaster
$("#ImpactMasterModal").on("click", async function () {
    $('#ImpactMastersaveBtn').text("Save");
    $("#Impact_catagory,#Impact_type,#Impact_description").val("")
    Impact_catagoryModalerror()
    await getImpactMaster()
})
const getImpactMaster = () => {
    $("#TimeImpactcatagorytable tbody ").empty()
    $.ajax({
        type: "GET",
        url: RootUrl + "Configuration/FiaTemplates/GetImpactTypeMasterList",
        dataType: "json",
        success: function (result) {
            let data = result?.data
            if (result?.success) {
                data?.forEach(function (item, i) {
                    let sno = i + 1
                    $("#TimeImpactcatagorytable tbody ").append('<tr>' +
                        '<td>' + sno + '</td>' +
                        '<td  class="text-truncate" title="' + item?.name + '">' + item?.name + '</td>' +
                        '<td>' + item?.description + '</td>' +
                        '<td class="text-truncate" title="' + item?.fiaImpactCategoryName + '">' + item?.fiaImpactCategoryName + '</td>' +
                        '<td><div class="d-flex align-items-center  gap-2"><span role="button" title ="Edit" onclick="impactmasteredit(this)" updateId=' + item?.id + ' impactcatagoryTypeId=' + item?.name + ' impactcatagoryDescription=' + item?.description + ' impactcatagoryName=' + item?.fiaImpactCategoryId + ' class= "edit_businessService-button"><i class="cp-edit"></i></span><span role="button" title="Delete" deleteId="' + item?.id + '" deleted_name="' + item?.name + '" onclick="impactmasterdeleteBtn(this)" class="delete-bservice-button" data-bs-toggle="modal" data-bs-target="#impactmasterDeleteModal"><i class="cp-Delete"></i></span></div></td>' +
                        '</tr>')
                })
            } else {
                errorNotification(result)
            }
        },
    })
}
function Impact_catagoryModalerror() {
    $("#Impact_catagory_error,#Impact_type_error,#Impact_description_error").text('').removeClass('field-validation-error');
}
function validateimpactmaster(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
async function IsSameimpacttypeExist(url, inputValue) {
    return !inputValue.name.trim() || $("#ImpactMastersaveBtn").text() == "Update" ? true : (await GetAsync(url, inputValue, OnError)) ? " Name already exists" : true;
}
async function validateimpacttypenamemaster(value, errorMsg, errorElement, url) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else if (value.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    } else {
        var url = RootUrl + url;
        var data = {};
        data.id = null;
        data.name = value
        const validationResults = [
            SpecialCharValidate(value),
            value.charAt(0) == "_" ? ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? ShouldNotBeginWithSpace(value) :
                OnlyNumericsValidate(value),
            minMaxlength(value),
            ShouldNotBeginWithSpace(value),
            ShouldNotBeginWithNumber(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            secondChar(value),
            await IsSameimpacttypeExist(url, data)
        ];
        const failedValidations = validationResults.filter(result => result !== true);
        if (failedValidations.length > 0) {
            errorElement.text(failedValidations[0]).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
}
$("#Impact_catagory").on("change", function () {
      validateimpactmaster($(this).val(), "Select impact catagory", $('#Impact_catagory_error'));
})
$("#Impact_type").on("input",Fiadebounce(async function () {
    let value = await sanitizeInput($("#Impact_type").val());
    $("#Impact_type").val(value);
    validateimpacttypenamemaster(value, "Enter impact type", $('#Impact_type_error'), ImpactTypeMasterNameExist);
}))
$('#ImpactMastersaveBtn').on('click', async function () {
    //setModalvalue()
    let form = $("#ManageImpactCategoryModalToggle")
    let impactcatagory = $("#Impact_catagory option:selected").text(), impactcatagoryid = $("#Impact_catagory ").val(), impacttype = $("#Impact_type").val(), impactdescription = $("#Impact_description").val()

    let impactcatagoryvalidation = validateimpactmaster($("#Impact_catagory").val(), "Select impact catagory", $('#Impact_catagory_error'))
    let impacttypevalidation = await validateimpacttypenamemaster($("#Impact_type").val(), "Enter impact type", $('#Impact_type_error'), ImpactTypeMasterNameExist)
    if (impactcatagoryvalidation && impacttypevalidation ) {
        form.trigger("submit")
        let data = {
            "FiaImpactCategoryName": impactcatagory,
            "FiaImpactCategoryId": impactcatagoryid,
            "Name": impacttype,
            "Description": impactdescription == "" ? "NA" : impactdescription,
            __RequestVerificationToken: gettoken()
        }
        $('#ImpactMastersaveBtn').text() === "Update" ? data["id"] = globalimpactmasterId : null
        await $.ajax({
            type: "POST",
            url: RootUrl + "Configuration/FiaTemplates/ImpactTypeMasterCreateOrUpdate",
            dataType: "json",
            data: data,
            success: function (result) {           
                let data = result.data
                if (result.success) {
                    notificationAlert("success", data.message)
                    getImpactMaster()
                } else {
                    errorNotification(result)
                }
            },
        })
        $("#Impact_type,#Impact_description").val("")
        $("#Impact_catagory").val("").change()
        $('#ImpactMastersaveBtn').text("Save") 
        Impact_catagoryModalerror()
    }
})
function impactmasteredit(data) {
    $('#ImpactMastersaveBtn').text("Update");
    globalimpactmasterId = $(data).attr('updateId');
    let catagory_id = $(data).attr('impactcatagoryName')
    $("#Impact_catagory ").val(catagory_id).trigger("change")
    $("#Impact_type").val($(data).attr('impactcatagoryTypeId'));
    $("#Impact_description").val($(data).attr('impactcatagoryDescription'));
}
function impactmasterdeleteBtn(data) {
    arr =[]
    globalimpactmasterDeleteId = $(data).attr('deleteId')
    $("#impactmaster_deleted_id").text($(data).attr('deleted_name')).attr("title", $(data).attr('deleted_name'));
    Create_deleteid?.forEach((item, i) => {
        if (item?.impactTypeId == globalimpactmasterDeleteId) {
            arr.push(item?.impactTypeId)
        }
    })
}
$("#impactmaster_confirmDeleteButton").on("click", async function () {
    if (arr?.length == 0) {
        await $.ajax({
            type: "POST",
            url: RootUrl + "Configuration/FiaTemplates/ImpactTypeMasterDelete",
            dataType: "json",
            data: {
                id: globalimpactmasterDeleteId,
                __RequestVerificationToken: gettoken()
            },
            success: function (result) {
                let data = result?.data
                if (result?.success) {
                    notificationAlert("success", data?.message)
                } else {
                    errorNotification(result)
                }
            },
        })
        getImpactMaster()
    } else {
        notificationAlert("warning", `The Impact Type '${$("#impactmaster_deleted_id").text()}'  is currently in use`)
    }

})
//impacttype
function Impact_typeModalerror() {
    $("#impactcatagory_error,#impactdescription_error").text('').removeClass('field-validation-error');
}
function validateimpacttype(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        const validationResults = [
            SpecialCharValidate(value),
            value.charAt(0) == "_" ? ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? ShouldNotBeginWithSpace(value) :
                OnlyNumericsValidate(value),
            minMaxlength(value),
            ShouldNotBeginWithSpace(value),
            ShouldNotBeginWithNumber(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            secondChar(value),
        ];
        return CommonValidation(errorElement, validationResults);
    }
}
$("#ImpactTypeModal").on("click", async function () {
    $('#impacttype_btnsave').text("Save");
    $("#impactcatagory,#impactDescription").val("");
    Impact_typeModalerror()
    await getImpactType()
})
const getImpactType = () => {
    $("#TimeImpacttypetable tbody ").empty()
    $.ajax({
        type: "GET",
        url: RootUrl + "Configuration/FiaTemplates/GetImpactMasterList",
        dataType: "json",
        success: function (result) {
            let data = result?.data
            if (result?.success) {
                data.forEach(function (item, i) {
                    let sno =i+1
                    $("#TimeImpacttypetable tbody ").append('<tr>' +
                        '<td>' + sno + '</td>' +
                        '<td class="text-truncate" title="' + item?.name+'">' + item?.name + '</td>' +
                        '<td>' + item.description + '</td>' +
                        '<td><div class="d-flex align-items-center  gap-2"><span role="button" title ="Edit" onclick="impacttypedit(this)" updateId=' + item?.id + ' impactTypeDescription=' + item?.description + ' impactTypeName=' + item?.name + ' class= "edit_businessService-button"><i class="cp-edit"></i></span><span role="button" title="Delete" deleteId="' + item?.id + '" deleted_name="' + item?.name +'" onclick="impacttypedeleteBtn(this)"class="delete-bservice-button" data-bs-toggle="modal" data-bs-target="#impacttypeDeleteModal"><i class="cp-Delete"></i></span></div></td>' +
                        '</tr>')
                })
            } else {
                errorNotification(result)
            }
        },
    })
}
function impacttypedit(data) {
    $('#impacttype_btnsave').text("Update");
    globalimpacttypeId = $(data).attr('updateId');
    $("#impactcatagory").val($(data).attr('impactTypeName'));
    $("#impactDescription").val($(data).attr('impactTypeDescription'));
}
async function validateimpacttypemaster(value, errorMsg, errorElement, url) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text("").removeClass('field-validation-error');
        return true;
    }
}
async function IsSameimpactcatagoryExist(url, inputValue) {
    return !inputValue.name.trim() || $("#impacttype_btnsave").text() == "Update" ? true : (await GetAsync(url, inputValue, OnError)) ? " Name already exists" : true;
}
async function validateimpactcatagorymaster(value, errorMsg, errorElement, url) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else if (value.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    } else {
        var url = RootUrl + url;
        var data = {};
        data.id = null;
        data.name = value
        const validationResults = [
            SpecialCharValidate(value),
            value.charAt(0) == "_" ? ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? ShouldNotBeginWithSpace(value) :
                OnlyNumericsValidate(value),
            minMaxlength(value),
            ShouldNotBeginWithSpace(value),
            ShouldNotBeginWithNumber(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            secondChar(value),
            await IsSameimpactcatagoryExist(url, data)
        ];
        const failedValidations = validationResults.filter(result => result !== true);

        if (failedValidations?.length > 0) {
            errorElement.text(failedValidations[0]).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
}
$("#impactcatagory").on("input",Fiadebounce(async function () {
    let value = await sanitizeInput($("#impactcatagory").val());
    $("#impactcatagory").val(value);
    validateimpactcatagorymaster(value, "Enter impact catagory", $('#impactcatagory_error'), isimpactcatagoryNameExits);
}))
$('#impacttype_btnsave').on('click', async function () {
    //setModalvalue()
    let form = $("#ImpactCategoryModalToggle")
    let impactcatagory = $("#impactcatagory").val(), impactdescription = $("#impactDescription").val()

    let impactcatagoryvalidation = await validateimpactcatagorymaster($("#impactcatagory").val(), "Enter impact catagory", $('#impactcatagory_error'), isimpactcatagoryNameExits)
    if (impactcatagoryvalidation ) {
        form.trigger("submit")
        let data = {
            "Name": impactcatagory,
            "Description": impactdescription == "" ? "NA" : impactdescription,
            __RequestVerificationToken: gettoken()
        }
            $('#impacttype_btnsave').text() === "Update" ? data["id"] = globalimpacttypeId : null

        await $.ajax({
            type: "POST",
            url: RootUrl + "Configuration/FiaTemplates/ImpactMasterCreateOrUpdate",
            dataType: "json",
            data: data,
            success: function (result) {
                let data = result?.data
                if (result.success) {
                    notificationAlert("success", data?.message)
                    getImpactType()
                    overall_Edit()
                } else {
                    errorNotification(result)
                }
            },
        })
        $("#impactcatagory,#impactDescription").val("")
        $('#impacttype_btnsave').text("Save") 
        Impact_typeModalerror()
    }
})
function impacttypedeleteBtn(data) {
    arr = []
    globalimpacttypeDeleteId = $(data).attr('deleteId')
    $("#impacttype_deleted_id").text($(data).attr('deleted_name')).attr("title", $(data).attr('deleted_name'));

    Create_deleteid.forEach((item, i) => {
        if (item.impactCategoryId == globalimpacttypeDeleteId) {
            arr.push(item?.impactCategoryId)
        } 
    })       
}
$("#impacttype_confirmDeleteButton").on("click", async function () {
    if (arr.length  == 0) { 
    await $.ajax({
            type: "POST",
            url: RootUrl + "Configuration/FiaTemplates/ImpactMasterDelete",
            dataType: "json",
            data: {
             id: globalimpacttypeDeleteId,
             __RequestVerificationToken: gettoken() },
            success: function (result) {
                let data = result?.data
                if (result?.success) {
                    notificationAlert("success", data?.message)
                } else {
                    errorNotification(result)
                }
            },
        })
        getImpactType()
    } else {
        notificationAlert("warning", `The Impact Category '${$("#impacttype_deleted_id").text()}'  is currently in use`)
    }
    
})