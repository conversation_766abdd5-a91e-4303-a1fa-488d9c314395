﻿using ContinuityPatrol.Application.Features.UserRole.Commands.Create;
using ContinuityPatrol.Application.Features.UserRole.Commands.Update;
using ContinuityPatrol.Application.Features.UserRole.Queries.GetDetail;
using ContinuityPatrol.Application.Features.UserRole.Queries.GetNames;
using ContinuityPatrol.Application.Features.UserRole.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.UserRoleModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IUserRoleService
{
    Task<BaseResponse> CreateAsync(CreateUserRoleCommand createUserRoleCommand);
    Task<BaseResponse> UpdateAsync(UpdateUserRoleCommand updateUserRoleCommand);
    Task<BaseResponse> DeleteAsync(string userRoleId);
    Task<UserRoleDetailVm> GetByReferenceId(string userRoleId);
    Task<PaginatedResult<UserRoleListVm>> GetUserRolePaginatedList(GetUserRolePaginatedListQuery query);
    Task<bool> IsUserRoleNameExist(string userRoleName, string id);
    Task<List<UserRoleListVm>> GetUserRoles();
    Task<List<UserRoleNamesVm>> GetUserRoleNames();
}