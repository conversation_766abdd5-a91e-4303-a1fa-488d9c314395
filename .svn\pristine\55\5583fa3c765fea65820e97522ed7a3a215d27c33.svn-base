﻿using ContinuityPatrol.Application.Helper;

namespace ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetAvailableCount;

public class GetAvailableCountQueryHandler : IRequestHandler<GetAvailableCountQuery, AvailableCountVm>
{
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;

    public GetAvailableCountQueryHandler(ILicenseInfoRepository licenseInfoRepository,
        ILicenseManagerRepository licenseManagerRepository)
    {
        _licenseInfoRepository = licenseInfoRepository;
        _licenseManagerRepository = licenseManagerRepository;
    }

    public async Task<AvailableCountVm> Handle(GetAvailableCountQuery request, CancellationToken cancellationToken)
    {
        //var databaseCount = _licenseInfoRepository.GetAvailableCountByLicenseId(request.LicenseId, Modules.Database.ToString());
        //var serverCount = _licenseInfoRepository.GetAvailableCountByLicenseId(request.LicenseId, Modules.Server.ToString());
        //var replicationCount = _licenseInfoRepository.GetAvailableCountByLicenseId(request.LicenseId, Modules.Replication.ToString());

        var licenseInfo = await _licenseInfoRepository.GetLicenseInfoDetailByLicenseId(request.LicenseId);

        var licenseDtl = await _licenseManagerRepository.GetLicenseDetailByIdAsync(request.LicenseId);

        Guard.Against.NullOrDeactive(licenseDtl, nameof(Domain.Entities.LicenseInfo),
            new NotFoundException(nameof(Domain.Entities.LicenseInfo), request.LicenseId));


        var jj = GetJsonProperties.SubtractLicenseCount(licenseDtl, licenseInfo);


        //var availableServerCount = int.Parse(licenseDtl.ServerCount) - serverCount;
        //var availableDatabaseCount = int.Parse(licenseDtl.DatabaseCount) - databaseCount;
        //var availableReplicationCount = int.Parse(licenseDtl.ReplicationCount) - replicationCount;
        var response = new AvailableCountVm
        {
            Properties = jj

            //ServerAvailableCount = availableServerCount,
            //DatabaseAvailableCount = availableDatabaseCount,
            //ReplicationAvailableCount = availableReplicationCount,
        };
        return response;
    }
}