﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfileInfo.Commands;

public class UpdateWorkflowProfileInfoTests : IClassFixture<WorkflowProfileInfoFixture>
{
    private readonly WorkflowProfileInfoFixture _workflowProfileInfoFixture;

    private readonly Mock<IWorkflowProfileInfoRepository> _mockWorkflowProfileInfoRepository;

    private readonly UpdateWorkflowProfileInfoCommandHandler _handler;

    public UpdateWorkflowProfileInfoTests(WorkflowProfileInfoFixture workflowProfileInfoFixture)
    {
        _workflowProfileInfoFixture = workflowProfileInfoFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockWorkflowProfileInfoRepository = WorkflowProfileInfoRepositoryMocks.UpdateWorkflowProfileInfoRepository(_workflowProfileInfoFixture.WorkflowProfileInfos);

        _handler = new UpdateWorkflowProfileInfoCommandHandler(_workflowProfileInfoFixture.Mapper, _mockWorkflowProfileInfoRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidWorkflowProfileInfo_UpdatedTo_WorkflowProfileInfoRepo()
    {
        _workflowProfileInfoFixture.UpdateWorkflowProfileInfoCommand.Id = _workflowProfileInfoFixture.WorkflowProfileInfos[0].ReferenceId;

        var result = await _handler.Handle(_workflowProfileInfoFixture.UpdateWorkflowProfileInfoCommand, CancellationToken.None);

        var workflowProfileInfo = await _mockWorkflowProfileInfoRepository.Object.GetByReferenceIdAsync(result.WorkflowProfileId);

        Assert.Equal(_workflowProfileInfoFixture.UpdateWorkflowProfileInfoCommand.WorkflowName, workflowProfileInfo.WorkflowName);
    }

    [Fact]
    public async Task Handle_Return_Valid_WorkflowProfileInfoResponse()
    {
        _workflowProfileInfoFixture.UpdateWorkflowProfileInfoCommand.Id = _workflowProfileInfoFixture.WorkflowProfileInfos[0].ReferenceId;

        var result = await _handler.Handle(_workflowProfileInfoFixture.UpdateWorkflowProfileInfoCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateWorkflowProfileInfoResponse));

        result.WorkflowProfileId.ShouldBe(_workflowProfileInfoFixture.UpdateWorkflowProfileInfoCommand.Id);

        var expectedMessage = $"Workflow Profile info {_workflowProfileInfoFixture.WorkflowProfileInfos[0].WorkflowName} Updated in {_workflowProfileInfoFixture.WorkflowProfileInfos[0].ProfileName} successfully.";

        result.Message.ShouldBe(expectedMessage);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_Invalid_WorkflowProfileInfoId()
    {
        _workflowProfileInfoFixture.UpdateWorkflowProfileInfoCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_workflowProfileInfoFixture.UpdateWorkflowProfileInfoCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _workflowProfileInfoFixture.UpdateWorkflowProfileInfoCommand.Id = _workflowProfileInfoFixture.WorkflowProfileInfos[0].ReferenceId;

        await _handler.Handle(_workflowProfileInfoFixture.UpdateWorkflowProfileInfoCommand, CancellationToken.None);

        _mockWorkflowProfileInfoRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockWorkflowProfileInfoRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.WorkflowProfileInfo>()), Times.Once);
    }
}