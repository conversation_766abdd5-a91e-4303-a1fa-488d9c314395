﻿using ContinuityPatrol.Web.Helper;

namespace ContinuityPatrol.Web.UnitTests.Helper;

public class HashHelperTests
{
    [Fact]
    public void ComputeSha256Hash_WithNormalString_ReturnsExpectedHash()
    {
        // Arrange
        var input = "HelloWorld";
         var expected = "872e4e50ce9990d8b041330c47c9ddd11bec6b503ae9386a99da8584e9bb12c4";

        // Act
        var result = HashHelper.ComputeSha256Hash(input);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void ComputeSha256Hash_SameInput_ReturnsSameHash()
    {
        // Arrange
        var input = "RepeatableHash";

        // Act
        var hash1 = HashHelper.ComputeSha256Hash(input);
        var hash2 = HashHelper.ComputeSha256Hash(input);

        // Assert
        hash1.Should().Be(hash2);
    }

    [Fact]
    public void ComputeSha256Hash_DifferentInput_ReturnsDifferentHash()
    {
        // Arrange
        var input1 = "InputOne";
        var input2 = "InputTwo";

        // Act
        var hash1 = HashHelper.ComputeSha256Hash(input1);
        var hash2 = HashHelper.ComputeSha256Hash(input2);

        // Assert
        hash1.Should().NotBe(hash2);
    }

    [Theory]
    [InlineData("  space  ", "space")]
    [InlineData("Space", "space")]
    [InlineData("Space", " SPACE ")]
    public void ComputeSha256Hash_SimilarInputs_ReturnDifferentHashes(string input1, string input2)
    {
        var hash1 = HashHelper.ComputeSha256Hash(input1);
        var hash2 = HashHelper.ComputeSha256Hash(input2);

        hash1.Should().NotBe(hash2);
    }

    [Fact]
    public void ComputeSha256Hash_LargeInput_ShouldReturnValidHash()
    {
        // Arrange
        var builder = new StringBuilder();
        for (int i = 0; i < 100000; i++)
        {
            builder.Append("TestData123");
        }

        var largeInput = builder.ToString();

        // Act
        var hash = HashHelper.ComputeSha256Hash(largeInput);

        // Assert
        hash.Should().NotBeNullOrWhiteSpace();
        hash.Length.Should().Be(64); // SHA256 hex string length
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void ComputeSha256Hash_NullOrEmptyInput_ThrowsArgumentException(string input)
    {
        // Act
        Action act = () => HashHelper.ComputeSha256Hash(input);

        // Assert
        act.Should().Throw<ArgumentException>()
            .WithMessage("Input cannot be null or empty.*");
    }

}