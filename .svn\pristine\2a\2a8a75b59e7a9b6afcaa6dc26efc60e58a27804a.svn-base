﻿using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;

namespace ContinuityPatrol.Application.Features.DashboardView.Queries.GetDcMappingList;

public class GetDcMappingListQueryHandler : IRequestHandler<GetDcMappingListQuery, List<GetDcMappingListVm>>
{
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IDashboardViewRepository _dashboardViewRepository;
    private readonly IBusinessFunctionRepository _functionRepository;
    private readonly IInfraObjectViewRepository _infraObjectViewRepository;
    private readonly IMapper _mapper;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;

    public GetDcMappingListQueryHandler(IMapper mapper, IInfraObjectViewRepository infraObjectViewRepository,
        IBusinessServiceRepository businessServiceRepository, IBusinessFunctionRepository businessFunctionRepository,
        IDashboardViewRepository dashboardViewRepository,
        IWorkflowOperationGroupRepository workflowOperationGroupRepository)
    {
        _mapper = mapper;
        _infraObjectViewRepository = infraObjectViewRepository;
        _businessServiceRepository = businessServiceRepository;
        _dashboardViewRepository = dashboardViewRepository;
        _functionRepository = businessFunctionRepository;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
    }
    //Not Used
    public async Task<List<GetDcMappingListVm>> Handle(GetDcMappingListQuery request,
        CancellationToken cancellationToken)
    {
        var businessService = await (request.SiteId.IsNotNullOrWhiteSpace()
            ? _businessServiceRepository.GetBusinessServicesBySiteId(request.SiteId)
            : _businessServiceRepository.ListAllAsync());

        var businessServiceVm = _mapper.Map<List<GetDcMappingListVm>>(businessService);
        foreach(var bs in businessServiceVm)
        
        {
            var availableCount = 0;
            var majorImpactCount = 0;
            var notAvailableCount = 0;

            var businessFunction =await _functionRepository.GetBusinessFunctionListByBusinessServiceId(bs.BusinessServiceId);
                

            bs.GetDcMappingBusinessFunctionListVms =
                _mapper.Map<List<GetDcMappingBusinessFunctionListVm>>(businessFunction);

           foreach(var bf in bs.GetDcMappingBusinessFunctionListVms)
            {
                var infraObjects = await _infraObjectViewRepository.GetInfraObjectByBusinessFunctionId(bf.BusinessFunctionId);
                    
                var infraObjectVm = _mapper.Map<List<GetDcMappingInfraObjectListVm>>(infraObjects);

                foreach(var infra in infraObjectVm)
                {
                    var drillDate = await _workflowOperationGroupRepository
                        .GetWorkflowOperationGroupByInfraObjectId(infra.InfraObjectId);

                    var dashboard = await _dashboardViewRepository.GetBusinessViewByInfraObjectId(infra.InfraObjectId);

                    infra.DataLagValue = dashboard?.DataLagValue ?? "NA";
                    infra.Status = dashboard?.Status ?? "NA";
                    infra.LastDrillDate = drillDate?.LastOrDefault()?.LastModifiedDate.ToString() ?? "NA";
                };
                bf.GetDcMappingInfraObjectListVms = infraObjectVm;

                availableCount = infraObjectVm.Count(x => x.Status?.Trim().ToLower() == "available");
                majorImpactCount = infraObjectVm.Count(x => x.Status?.Trim().ToLower() == "major impact");
                notAvailableCount = infraObjectVm.Count(x =>
                    x.Status?.Trim().ToLower() == "notavailable" || x.Status.IsNullOrWhiteSpace());
            };

            bs.Status = availableCount > majorImpactCount && availableCount > notAvailableCount ? "Available" :
                majorImpactCount > availableCount && majorImpactCount > notAvailableCount ? "Major Impact" :
                "Not Available";
        };


        return businessServiceVm;
    }
}