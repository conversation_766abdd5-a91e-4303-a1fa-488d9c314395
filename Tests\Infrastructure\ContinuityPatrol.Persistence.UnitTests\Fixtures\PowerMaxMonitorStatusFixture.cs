using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetPowerMax;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Newtonsoft.Json;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class PowerMaxMonitorStatusFixture : IDisposable
{
    public List<PowerMaxMonitorStatus> PowerMaxMonitorStatusPaginationList { get; set; }
    public List<PowerMaxMonitorStatus> PowerMaxMonitorStatusList { get; set; }
    public PowerMaxMonitorStatus PowerMaxMonitorStatusDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public PowerMaxMonitorStatusFixture()
    {
        var fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        fixture.Customize<PowerMaxMonitorStatus>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .With(x => x.LastModifiedDate, DateTime.UtcNow)
            .With(x => x.CreatedBy, "TestUser")
            .With(x => x.LastModifiedBy, "TestUser")
            .With(x => x.Type, () => 
            {
                var type = fixture.Create<string>();
                return $"Type_{(type.Length > 8 ? type.Substring(0, 8) : type)}";
            })
            .With(x => x.InfraObjectId, () => Guid.NewGuid().ToString())
            .With(x => x.InfraObjectName, () => 
            {
                var name = fixture.Create<string>();
                return $"InfraObject_{(name.Length > 10 ? name.Substring(0, 10) : name)}";
            })
            .With(x => x.WorkflowId, () => Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, () => 
            {
                var name = fixture.Create<string>();
                return $"Workflow_{(name.Length > 10 ? name.Substring(0, 10) : name)}";
            })
            .With(x => x.Properties, () => CreateSamplePowerMaxProperties())
            .With(x => x.ConfiguredRPO, () => 
            {
                var rpo = fixture.Create<string>();
                return rpo.Length > 20 ? rpo.Substring(0, 20) : rpo;
            })
            .With(x => x.DataLagValue, () => 
            {
                var lag = fixture.Create<string>();
                return lag.Length > 20 ? lag.Substring(0, 20) : lag;
            })
            .With(x => x.Threshold, () => 
            {
                var threshold = fixture.Create<string>();
                return threshold.Length > 20 ? threshold.Substring(0, 20) : threshold;
            }));

        PowerMaxMonitorStatusList = fixture.CreateMany<PowerMaxMonitorStatus>(5).ToList();
        PowerMaxMonitorStatusPaginationList = fixture.CreateMany<PowerMaxMonitorStatus>(20).ToList();
        PowerMaxMonitorStatusDto = fixture.Create<PowerMaxMonitorStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public PowerMaxMonitorStatus CreatePowerMaxMonitorStatusWithProperties(
        string type = null,
        string infraObjectId = null,
        string infraObjectName = null,
        string workflowId = null,
        string workflowName = null,
        string properties = null,
        bool isActive = true)
    {
        var fixture = new Fixture();
        var typeStr = fixture.Create<string>();
        var infraNameStr = fixture.Create<string>();
        var workflowNameStr = fixture.Create<string>();
        var rpoStr = fixture.Create<string>();
        var lagStr = fixture.Create<string>();
        var thresholdStr = fixture.Create<string>();
        
        return new PowerMaxMonitorStatus
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = type ?? $"Type_{(typeStr.Length > 8 ? typeStr.Substring(0, 8) : typeStr)}",
            InfraObjectId = infraObjectId ?? Guid.NewGuid().ToString(),
            InfraObjectName = infraObjectName ?? $"InfraObject_{(infraNameStr.Length > 10 ? infraNameStr.Substring(0, 10) : infraNameStr)}",
            WorkflowId = workflowId ?? Guid.NewGuid().ToString(),
            WorkflowName = workflowName ?? $"Workflow_{(workflowNameStr.Length > 10 ? workflowNameStr.Substring(0, 10) : workflowNameStr)}",
            Properties = properties ?? CreateSamplePowerMaxProperties(),
            ConfiguredRPO = rpoStr.Length > 20 ? rpoStr.Substring(0, 20) : rpoStr,
            DataLagValue = lagStr.Length > 20 ? lagStr.Substring(0, 20) : lagStr,
            Threshold = thresholdStr.Length > 20 ? thresholdStr.Substring(0, 20) : thresholdStr,
            IsActive = isActive,
            CreatedDate = DateTime.UtcNow,
            LastModifiedDate = DateTime.UtcNow,
            CreatedBy = "TestUser",
            LastModifiedBy = "TestUser"
        };
    }

    public PowerMaxMonitorStatus CreatePowerMaxMonitorStatusWithSpecificType(string type)
    {
        return CreatePowerMaxMonitorStatusWithProperties(type: type);
    }

    public List<PowerMaxMonitorStatus> CreatePowerMaxMonitorStatusesWithSameType(string type, int count)
    {
        var statuses = new List<PowerMaxMonitorStatus>();
        for (int i = 0; i < count; i++)
        {
            statuses.Add(CreatePowerMaxMonitorStatusWithProperties(type: type));
        }
        return statuses;
    }

    public List<PowerMaxMonitorStatus> CreatePowerMaxMonitorStatusesWithSameInfraObjectId(string infraObjectId, int count)
    {
        var statuses = new List<PowerMaxMonitorStatus>();
        for (int i = 0; i < count; i++)
        {
            statuses.Add(CreatePowerMaxMonitorStatusWithProperties(infraObjectId: infraObjectId));
        }
        return statuses;
    }

    public PowerMaxMonitorStatus CreatePowerMaxMonitorStatusWithCustomProperties(string storageGroupName, string snapshotName = null)
    {
        var properties = CreateCustomPowerMaxProperties(storageGroupName, snapshotName);
        return CreatePowerMaxMonitorStatusWithProperties(properties: properties);
    }

    private string CreateSamplePowerMaxProperties()
    {
        var powerMaxDetail = new PowerMaxDetailVm
        {
            IpAddress = "*************",
            Version = "********",
            Array = "000197800123",
            ModelName = "PowerMax 8000",
            StorageGroupMonitoring = new List<StorageGroupMonitoring>
            {
                new StorageGroupMonitoring
                {
                    StorageGroupName = "SG_Test_01",
                    Compliance = "Green",
                    SRP = "SRP_1",
                    ServiceLevel = "Diamond",
                    Capacity = "1000GB",
                    Emulation = "FBA",
                    SRDFReplicationstatus = "Synchronized",
                    SnapshotCount = "5",
                    SnapshotDetails = new List<SnapshotDetail>
                    {
                        new SnapshotDetail
                        {
                            SnapshotId = "12345",
                            SnapshotName = "Snap_Test_01",
                            CreationTime = DateTime.UtcNow.ToString(),
                            LinkedStatus = "Linked",
                            Restored = "No",
                            Expired = "No",
                            ExpiryTime = DateTime.UtcNow.AddDays(7).ToString(),
                            Secured = "Yes"
                        }
                    }
                }
            }
        };

        return JsonConvert.SerializeObject(powerMaxDetail);
    }

    private string CreateCustomPowerMaxProperties(string storageGroupName, string snapshotName = null)
    {
        var powerMaxDetail = new PowerMaxDetailVm
        {
            IpAddress = "*************",
            Version = "********",
            Array = "000197800123",
            ModelName = "PowerMax 8000",
            StorageGroupMonitoring = new List<StorageGroupMonitoring>
            {
                new StorageGroupMonitoring
                {
                    StorageGroupName = storageGroupName,
                    Compliance = "Green",
                    SRP = "SRP_1",
                    ServiceLevel = "Diamond",
                    Capacity = "1000GB",
                    Emulation = "FBA",
                    SRDFReplicationstatus = "Synchronized",
                    SnapshotCount = "1",
                    SnapshotDetails = new List<SnapshotDetail>
                    {
                        new SnapshotDetail
                        {
                            SnapshotId = "12345",
                            SnapshotName = snapshotName ?? "Default_Snapshot",
                            CreationTime = DateTime.UtcNow.ToString(),
                            LinkedStatus = "Linked",
                            Restored = "No",
                            Expired = "No",
                            ExpiryTime = DateTime.UtcNow.AddDays(7).ToString(),
                            Secured = "Yes"
                        }
                    }
                }
            }
        };

        return JsonConvert.SerializeObject(powerMaxDetail);
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
