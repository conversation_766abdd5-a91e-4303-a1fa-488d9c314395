﻿using ContinuityPatrol.Application.Features.RpForVmCGMonitorStatus.Queries.GetPagination;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using Microsoft.Extensions.Logging;
using System.Runtime.Versioning;
using System.Text.Json;
using Tavis.UriTemplates;

namespace ContinuityPatrol.Web.Areas.Monitor.Controllers;

[Area("Monitor")]
public class RPForVMController : Controller
{
    public static ILogger<RPForVMController> _logger;
    private readonly IDataProvider _provider;
    private readonly IPublisher _publisher;
    public static string CompanyLogo { get; set; }
    public RPForVMController(ILogger<RPForVMController> logger, IDataProvider provider)
    {
        _logger = logger;
        _provider = provider;
    }
    [AntiXss]
    public IActionResult List()
    {
        return View();
    }

    [SupportedOSPlatform("windows")]
    public async Task<IActionResult> DownloadReport([FromBody] ReportRequest reportData)
    {
        var reportsDirectory = "";
        try
        {
            if (reportData.ReportData is not null && reportData.ReportData.Count > 0)
            {
                _logger.LogInformation("RPforVM report data not null");
                await GetCompanyLogo();
                string reportGeneratedName = WebHelper.UserSession.LoginName.ToString();
                XtraReport report = new Report.ReportTemplate.RP4VMCGMonitoringReport(reportData.ReportData, reportGeneratedName);
                var infraobject = await _provider.InfraObject.GetInfraObjectById(reportData.ReportData.FirstOrDefault().InfraObjectId);
                var filenameSuffix = DateTime.Now.ToString("MMddyyyyhhmmsstt");
                var fileName = "RP4VM_CG_Monitoring_" + filenameSuffix + ".pdf";
                reportsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName);
                await report.ExportToPdfAsync(reportsDirectory);
                byte[] fileBytes = System.IO.File.ReadAllBytes(reportsDirectory);
                _logger.LogInformation("RPforVM report loaded successfully");
                return File(fileBytes, "application/pdf", fileName);
            }
            else
            {
                _logger.LogError(" RPforVM report No data found. Report data count : " + reportData.ReportData.Count());
                return Json(new { success = false, message = "No data found" });
            }
            
        }
        catch (Exception ex)
        {
            _logger.LogError("Error occured while download the RPforVM Report : " + ex.Message.ToString());
            return Json(new { success = false, message = ex.Message });
        }
        finally
        {

            if (System.IO.File.Exists(reportsDirectory))
            {
                System.IO.File.Delete(reportsDirectory);
            }
        }
    }
    public class ReportRequest
    {
        public List<RpForVmCGMonitorStatusListVm> ReportData { get; set; }
    }
    public async Task GetCompanyLogo()
    {
        CompanyLogo = string.Empty;
        var companyDetails = await _provider.Company.GetCompanyById(WebHelper.UserSession.CompanyId);
        if (!string.IsNullOrEmpty(companyDetails.CompanyLogo) && companyDetails.CompanyLogo != "NA") { CompanyLogo = companyDetails.CompanyLogo.ToString(); }
    }
}

