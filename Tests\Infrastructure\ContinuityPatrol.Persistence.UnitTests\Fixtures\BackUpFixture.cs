using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class BackUpFixture : IDisposable
{
    public List<BackUp> BackUpPaginationList { get; set; }
    public List<BackUp> BackUpList { get; set; }
    public BackUp BackUpDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public BackUpFixture()
    {
        var fixture = new Fixture();

        BackUpList = fixture.Create<List<BackUp>>();

        BackUpPaginationList = fixture.CreateMany<BackUp>(20).ToList();

        BackUpPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BackUpPaginationList.ForEach(x => x.IsActive = true);


        BackUpList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BackUpList.ForEach(x => x.IsActive = true);

        BackUpDto = fixture.Create<BackUp>();

        BackUpDto.ReferenceId = Guid.NewGuid().ToString();
        BackUpDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
