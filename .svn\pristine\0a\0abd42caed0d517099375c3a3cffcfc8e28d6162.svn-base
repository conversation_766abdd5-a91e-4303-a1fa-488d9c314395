﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class ServerRepositoryMocks
{
    public static Mock<IServerRepository> CreateServerRepository(List<Server> servers)
    {
        var serverRepository = new Mock<IServerRepository>();

        serverRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(servers);

        serverRepository.Setup(repo => repo.AddAsync(It.IsAny<Server>())).ReturnsAsync(
            (Server server) =>
            {
                server.Id = new Fixture().Create<int>();

                server.ReferenceId = new Fixture().Create<Guid>().ToString();

                servers.Add(server);

                return server;
            });

        return serverRepository;
    }

    public static Mock<IServerRepository> UpdateServerRepository(List<Server> servers)
    {
        var serverRepository = new Mock<IServerRepository>();

        serverRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(servers);

        serverRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => servers.SingleOrDefault(x => x.ReferenceId == i));

        serverRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Server>())).ReturnsAsync((Server server) =>
        {
            var index = servers.FindIndex(item => item.ReferenceId == server.ReferenceId);

            servers[index] = server;

            return server;
        });

        return serverRepository;
    }

    public static Mock<IServerRepository> DeleteServerRepository(List<Server> servers)
    {
        var serverRepository = new Mock<IServerRepository>();

        serverRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(servers);

        serverRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => servers.SingleOrDefault(x => x.ReferenceId == i));

       // serverRepository.Setup(repo => repo.GetServerByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(servers);

        serverRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Server>())).ReturnsAsync((Server server) =>
        {
            var index = servers.FindIndex(item => item.ReferenceId == server.ReferenceId);

            server.IsActive = false;

            servers[index] = server;

            return server;
        });

        return serverRepository;
    }

    public static Mock<IServerRepository> GetServerRepository(List<Server> servers)
    {
        var serverRepository = new Mock<IServerRepository>();

        serverRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(servers);

        serverRepository.Setup(repo => repo.GetType(It.IsAny<string>())).ReturnsAsync(servers);

        serverRepository.Setup(repo => repo.GetRoleType(It.IsAny<string>())).ReturnsAsync(servers);

        serverRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => servers.SingleOrDefault(x => x.ReferenceId == i));

        return serverRepository;
    }

    public static Mock<IServerRepository> GetServerByServerNameRepository(List<Server> servers)
    {
        var serverRepository = new Mock<IServerRepository>();

        serverRepository.Setup(repo => repo.GetServerByServerName(It.IsAny<string>())).ReturnsAsync((string i) => servers.SingleOrDefault(x => x.Name == i));

        return serverRepository;
    }

    public static Mock<IServerRepository> GetServerEmptyRepository()
    {
        var serverRepository = new Mock<IServerRepository>();

        serverRepository.Setup(repo => repo.GetType(It.IsAny<string>())).ReturnsAsync(new List<Server>());

        serverRepository.Setup(repo => repo.GetRoleType(It.IsAny<string>())).ReturnsAsync(new List<Server>());

        serverRepository.Setup(repo => repo.GetServerNames()).ReturnsAsync(new List<Server>());

        serverRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Server>());

        serverRepository.Setup(repo => repo.GetServerByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(new List<Server>());

        return serverRepository;
    }

    public static Mock<IServerRepository> GetServerNamesRepository(List<Server> servers)
    {
        var serverRepository = new Mock<IServerRepository>();

        serverRepository.Setup(repo => repo.GetServerNames()).ReturnsAsync(servers);

        return serverRepository;
    }

    public static Mock<IServerRepository> GetServerByLicenseKey(List<Server> servers)
    {
        var serverRepository = new Mock<IServerRepository>();

        serverRepository.Setup(repo => repo.GetServerByLicenseKey(It.IsAny<string>())).ReturnsAsync(servers);

        return serverRepository;
    }

    public static Mock<IServerRepository> GetServerNameUniqueRepository(List<Server> servers)
    {
        var serverRepository = new Mock<IServerRepository>();

        serverRepository.Setup(repo => repo.IsServerNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) =>
        {
            return j == 0.ToString() ? servers.Exists(x => x.Name == i) : servers.Exists(x => x.Name == i && x.ReferenceId == j);
        });

        return serverRepository;
    }

    public static Mock<IServerRepository> GetPaginatedServerRepository(List<Server> servers)
    {
        var serverRepository = new Mock<IServerRepository>();

        var queryableServer = servers.BuildMock();

        serverRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableServer);

        return serverRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateServerEventRepository(List<UserActivity> userActivities)
    {
        var serverEventRepository = new Mock<IUserActivityRepository>();

        serverEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        serverEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return serverEventRepository;
    }
}