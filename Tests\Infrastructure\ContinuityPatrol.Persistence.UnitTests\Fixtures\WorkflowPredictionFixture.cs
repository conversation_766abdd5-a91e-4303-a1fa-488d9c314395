using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowPredictionFixture : IDisposable
{
    public List<WorkflowPrediction> WorkflowPredictionPaginationList { get; set; }
    public List<WorkflowPrediction> WorkflowPredictionList { get; set; }
    public WorkflowPrediction WorkflowPredictionDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowPredictionFixture()
    {
        var fixture = new Fixture();

        WorkflowPredictionList = fixture.Create<List<WorkflowPrediction>>();

        WorkflowPredictionPaginationList = fixture.CreateMany<WorkflowPrediction>(20).ToList();

  

        WorkflowPredictionDto = fixture.Create<WorkflowPrediction>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
