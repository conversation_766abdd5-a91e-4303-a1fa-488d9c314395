﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ContinuityPatrol.Persistence.Repositories
{

    public class FastCopyMonitorLogsRepository : BaseRepository<FastCopyMonitorLog>, IFastCopyMonitorLogsRepository
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IConfiguration _config;
        public FastCopyMonitorLogsRepository(ApplicationDbContext dbContext, IConfiguration config) : base(dbContext)
        {
            _dbContext = dbContext;
            _config = config;
        }

        public async Task<List<FastCopyMonitorLog>> GetByInfraObjectId(string infraObjectId, string startDate, string endDate)
        {
            return await _dbContext.FastCopyMonitorLogs.Active()
                .Where(x => x.InfraObjectId.Equals(infraObjectId) &&
                            x.CreatedDate.Date >= startDate.ToDateTime().Date &&
                            x.CreatedDate.Date <= endDate.ToDateTime().Date)
                .ToListAsync();
        }
    }
}
