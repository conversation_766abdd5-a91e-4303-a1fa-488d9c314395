﻿namespace ContinuityPatrol.Application.Features.NodeWorkflowExecution.Commands.Create;

public class CreateNodeWorkflowExecutionCommandHandler : IRequestHandler<CreateNodeWorkflowExecutionCommand,
    CreateNodeWorkflowExecutionResponse>
{
    private readonly IMapper _mapper;
    private readonly INodeWorkflowExecutionRepository _nodeWorkflowExecutionRepository;

    public CreateNodeWorkflowExecutionCommandHandler(IMapper mapper,
        INodeWorkflowExecutionRepository nodeWorkflowExecutionRepository)
    {
        _mapper = mapper;
        _nodeWorkflowExecutionRepository = nodeWorkflowExecutionRepository;
    }

    public async Task<CreateNodeWorkflowExecutionResponse> Handle(CreateNodeWorkflowExecutionCommand request,
        CancellationToken cancellationToken)
    {
        var nodeWorkflowExecution = _mapper.Map<Domain.Entities.NodeWorkflowExecution>(request);

        nodeWorkflowExecution = await _nodeWorkflowExecutionRepository.AddAsync(nodeWorkflowExecution);

        var response = new CreateNodeWorkflowExecutionResponse
        {
            Message = Message.Create(nameof(Domain.Entities.NodeWorkflowExecution), request.NodeName),
            Id = nodeWorkflowExecution.ReferenceId
        };

        return response;
    }
}