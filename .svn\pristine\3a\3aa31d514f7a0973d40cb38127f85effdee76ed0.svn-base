using ContinuityPatrol.Application.Features.WorkflowApprovalMapping.Events.Delete;

namespace ContinuityPatrol.Application.Features.WorkflowApprovalMapping.Commands.Delete;

public class DeleteWorkflowApprovalMappingCommandHandler : IRequestHandler<DeleteWorkflowApprovalMappingCommand, DeleteWorkflowApprovalMappingResponse>
{
    private readonly IWorkflowApprovalMappingRepository _workflowApprovalMappingRepository;
    private readonly IPublisher _publisher;

    public DeleteWorkflowApprovalMappingCommandHandler(IWorkflowApprovalMappingRepository workflowApprovalMappingRepository, IPublisher publisher)
    {
        _workflowApprovalMappingRepository = workflowApprovalMappingRepository;

        _publisher = publisher;
    }

    public async Task<DeleteWorkflowApprovalMappingResponse> Handle(DeleteWorkflowApprovalMappingCommand request, CancellationToken cancellationToken)
    {
        var eventToDelete = await _workflowApprovalMappingRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.WorkflowApprovalMapping),
            new NotFoundException(nameof(Domain.Entities.WorkflowApprovalMapping), request.Id));

        eventToDelete.IsActive = false;

        await _workflowApprovalMappingRepository.UpdateAsync(eventToDelete);

        var response = new DeleteWorkflowApprovalMappingResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.WorkflowApprovalMapping), eventToDelete.WorkflowName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new WorkflowApprovalMappingDeletedEvent { Name = eventToDelete.WorkflowName }, cancellationToken);

        return response;
    }
}
