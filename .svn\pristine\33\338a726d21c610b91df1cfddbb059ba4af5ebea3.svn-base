﻿using ContinuityPatrol.Application.Features.Replication.Events.PaginatedView;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.Replication.Events
{
    public class PaginatedReplicationEventTests
    {
        private readonly Mock<ILogger<ReplicationPaginatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly ReplicationPaginatedEventHandler _handler;

        public PaginatedReplicationEventTests()
        {
            _mockLogger = new Mock<ILogger<ReplicationPaginatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _handler = new ReplicationPaginatedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldLogAndSaveUserActivity_WhenEventIsHandled()
        {
            var paginatedEvent = new ReplicationPaginatedEvent();

            _mockUserService.Setup(us => us.UserId).Returns("TestUser123");
            _mockUserService.Setup(us => us.LoginName).Returns("TestUser");
            _mockUserService.Setup(us => us.CompanyId).Returns("TestCompany");
            _mockUserService.Setup(us => us.RequestedUrl).Returns("http://example.com/replications");
            _mockUserService.Setup(us => us.IpAddress).Returns("127.0.0.1");

            var userActivity = new Domain.Entities.UserActivity
            {
                UserId = "TestUser123",
                LoginName = "TestUser",
                RequestUrl = "http://example.com/replications",
                CompanyId = "TestCompany",
                HostAddress = "127.0.0.1",
                Entity = Modules.Replication.ToString(),
                Action = $"{ActivityType.View} {Modules.Replication}",
                ActivityType = ActivityType.View.ToString(),
                ActivityDetails = "Replication viewed"
            };

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Returns(ToString);

            await _handler.Handle(paginatedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(
                repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                    ua.UserId == userActivity.UserId &&
                    ua.LoginName == userActivity.LoginName &&
                    ua.RequestUrl == userActivity.RequestUrl &&
                    ua.CompanyId == userActivity.CompanyId &&
                    ua.HostAddress == userActivity.HostAddress &&
                    ua.Entity == userActivity.Entity &&
                    ua.Action == userActivity.Action &&
                    ua.ActivityType == userActivity.ActivityType &&
                    ua.ActivityDetails == userActivity.ActivityDetails)),
                Times.Once
            );

            _mockLogger.Verify(
                logger => logger.LogInformation("Replication viewed"),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ShouldNotThrowException_WhenDependenciesAreMocked()
        {
            var paginatedEvent = new ReplicationPaginatedEvent();

            _mockUserService.Setup(us => us.UserId).Returns("AnotherUser");
            _mockUserService.Setup(us => us.LoginName).Returns("AnotherLoginName");
            _mockUserService.Setup(us => us.CompanyId).Returns("AnotherCompany");
            _mockUserService.Setup(us => us.RequestedUrl).Returns("http://anotherexample.com");
            _mockUserService.Setup(us => us.IpAddress).Returns("***********");

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Returns(ToString);

            var exception = await Record.ExceptionAsync(() =>
                _handler.Handle(paginatedEvent, CancellationToken.None));

            Assert.Null(exception);
        }
    }
}
