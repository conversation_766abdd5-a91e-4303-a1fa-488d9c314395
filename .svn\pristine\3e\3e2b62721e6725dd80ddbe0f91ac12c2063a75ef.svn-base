﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Site.Events.PaginatedView;

public class SitePaginatedEventHandler : INotificationHandler<SitePaginatedEvent>
{
    private readonly ILogger<SitePaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public SitePaginatedEventHandler(ILoggedInUserService userService, ILogger<SitePaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(SitePaginatedEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            CompanyId = _userService.CompanyId,
            Entity = Modules.Site.ToString(),
            Action = $"{ActivityType.View} {Modules.Site}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = " Site viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Site viewed");
    }
}