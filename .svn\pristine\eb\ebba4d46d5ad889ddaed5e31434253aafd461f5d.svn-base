﻿using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Shared.Tests.Infrastructure;


namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class ImpactActivityRepositoryTests
    {
        private readonly ApplicationDbContext _dbContext;
        //private readonly ImpactActivityRepository _repository;

        public ImpactActivityRepositoryTests()
        {
            _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
            //_repository = new ImpactActivityRepository(_dbContext, DbContextFactory.GetMockUserService());
        }
        public void Dispose()
        {
            _dbContext?.Dispose();
        }
        [Fact]
        public void Constructor_ShouldInitializeRepository()
        {
            // Arrange

            // Act
            var repository = new ImpactActivityRepository(_dbContext, DbContextFactory.GetMockUserService());

            // Assert
            Assert.NotNull(repository);
        }
    
    }
}
