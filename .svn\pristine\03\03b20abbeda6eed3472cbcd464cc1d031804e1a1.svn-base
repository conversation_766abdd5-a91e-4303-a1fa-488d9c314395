﻿using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetChildLicenseByParentId;
using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Runtime.Versioning;
using System.Text.Json;

namespace ContinuityPatrol.Web.Areas.Report.Controllers;

[Area("Report")]
public class PreBuildReportController : BaseController
{
    public static ILogger<PreBuildReportController> _logger;
    public static List<ChildLicenseDetailByParentIdVm> getChildPO = new List<ChildLicenseDetailByParentIdVm>();
    private readonly IDataProvider _provider;
    private readonly IPublisher _publisher;
    public static string CompanyLogo { get; set; }

    public PreBuildReportController(ILogger<PreBuildReportController> logger, IDataProvider provider, IPublisher publisher)
    {
        _logger = logger;
        _provider = provider;
        _publisher = publisher;
    }

    public IActionResult List()
    {
        return View();
    }

    [SupportedOSPlatform("windows")]
    public async Task<IActionResult> LicenseUtilizationReport(string Licensepoid, string businessServiceId, string Derivedpoid, string Type)
    {
        try
        {
            await GetCompanyLogo();
            if (Licensepoid != null)
            {
                Licensepoid = (Licensepoid == "All") ? "" : Licensepoid;
                var GetLicenseReportById = await _provider.Report.GetLicenseReportById(Licensepoid);
                var LicenseReportvalue = "";
                GetLicenseReportById.ChildLicensePOIds = Derivedpoid ?? "All";
                GetLicenseReportById.LicenseType = "PONumber";
                // var filteredItems = GetLicenseReportById.LicenseReportVms.Where(x => x.LicenseReportDetail.Any(y => y.PONumber != null)).ToList();
                if (GetLicenseReportById != null && GetLicenseReportById.LicenseReportVms.Count > 0)
                {
                   // GetLicenseReportById.LicenseReportVms = filteredItems;
                    LicenseReportvalue = JsonConvert.SerializeObject(GetLicenseReportById);

                    if (Type == "Dashboard")
                    {
                        var reportsDirectory = "";
                        XtraReport report = new Report.ReportTemplate.LicenseUtlizationReport(LicenseReportvalue);
                        var filenameSuffix = DateTime.Now.ToString("ddMMyyyy_hhmmsstt");
                        var fileName = "LicenseUtilizationReport_PONumber_" + filenameSuffix + ".pdf";
                        reportsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName);
                        report.ExportToPdf(reportsDirectory);
                        _logger.LogInformation(fileName + " was downloaded successfully.");
                        byte[] fileBytes = System.IO.File.ReadAllBytes(reportsDirectory);
                        if (System.IO.File.Exists(reportsDirectory))
                        {
                            System.IO.File.Delete(reportsDirectory);
                        }
                        return File(fileBytes, "application/pdf", fileName);
                    }
                    return Json(new { success = true, data = LicenseReportvalue });
                }
                return Json(new { success =false , data= GetLicenseReportById });
            }
            if (businessServiceId != "")
            {
                businessServiceId = (businessServiceId == "All") ? "" : businessServiceId;
                var getLicensereporbybusinessid = await _provider.Report.GetLicenseUtilizationReportByBusinessServiceId(businessServiceId);

                if(getLicensereporbybusinessid is not null && getLicensereporbybusinessid.LicenseReportByBusinessServiceVms.Any(x => x.BusinessFunctionLicenseDto.Any()))
                {                
                    var report= getLicensereporbybusinessid.LicenseReportByBusinessServiceVms.Where(x => x.BusinessFunctionLicenseDto.Any(y => y.BusinessServiceName != null)).ToList();
                    getLicensereporbybusinessid.LicenseReportByBusinessServiceVms=report;
                    getLicensereporbybusinessid.LicenseType = "BusinessService";
                    string reportvalue = JsonConvert.SerializeObject(getLicensereporbybusinessid);
                    return Json(new { success = true,data= reportvalue });
                }              
            }
            return Json(new { success = false });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }

    public async Task<IActionResult> GetInfraobject()
    {
        try
        {
            var list = await _provider.DashboardView.GetDashboardNames();
            list = list.Where(x => x.InfraObjectId != "" && x.InfraObjectId != null).ToList();
            //return Json(list);
            //var list = await _provider.InfraObject.GetInfraObjectNames();
            //return Json(list);
            return Json(new { success = true, data = list });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page  while getting the InfraObject list.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetDatalagStatusReport()
    {
        try
        {
            var datalag = await _provider.DashboardViewLog.GetDatalagStatusReport();
            await GetCompanyLogo();
            if (datalag != null && datalag.DataLagStatusReportVms.Count != 0)
            {
                string report = JsonConvert.SerializeObject(datalag);
                return Json(new { success = true, data = report });
            }
            return Json(new { success = false, data = datalag });

      }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetInfraObjectList(string infraId, string infraObjectType)
    {
        try
        {
            if (!string.IsNullOrEmpty(infraId))
            {
                var infraReport = await _provider.Report.GetInfraObjectConfigurationReport(infraId);
                await GetCompanyLogo();
                if (infraReport != null && infraReport.InfraObjectConfigurationReportVm != null)
                {
                    string report = JsonConvert.SerializeObject(infraReport);
                    return Json(new { success = true, data = report });
                }
                return Json(new { success = false, data = infraReport });
            }
            else
            {
                _logger.LogError($"InfraObject Id or Type can not be empty!");
                return Json(new { success = false });
            }
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetUserNames()
    {
        try
        {
            var userNames = await _provider.User.GetUserNames();
            return Json(new { success = true, data = userNames });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }

    }
    public async Task<IActionResult> GetUserActivityDetails(string userId, string startDate, string endDate)
    {
        try
        {
            if (userId == "All" || userId == "All Users")
            { userId = ""; }
            var activityReport = await _provider.Report.GetUserActivityReport(userId, startDate, endDate); 
            await GetCompanyLogo();
            if (activityReport != null && activityReport.UserActivityReportVms.Count != 0)
            {
                activityReport.UserName = userId == "" ? "All Users" : activityReport?.UserActivityReportVms[0]?.LoginName;
                activityReport.ActiveStartDate = startDate;
                activityReport.ActiveEndDate = endDate;
                string report = JsonConvert.SerializeObject(activityReport);
                return Json(new { success = true, data = report });
            }
            return Json(new { success = false, data = activityReport });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetRtoReport(string workflowId, string workflowName, string type, string startDate, string endDate)
    {
        try
        {
            var reports = await _provider.Report.GetRtoReportByWorkflowOperationId(workflowId);
            await GetCompanyLogo();
            if (reports != null)
            {
                reports.ActiveStartDate = startDate;
                reports.ActiveEndDate = endDate;
                var rtoreport = JsonConvert.SerializeObject(reports);
                return Json(new { success = true, data = rtoreport });
            }          
            return Json(new { success = false, data = reports});
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }

    //LicenseReport
    public async Task<IActionResult> GetLicensePONumber()
    {
        try
        {
            var GetAllPONumber = await _provider.LicenseManager.GetAllPoNumbers();

            var Licensepo = GetAllPONumber.Where(x => x.IsParent);

            return Json(new { success = true, data = GetAllPONumber });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> ChildLicensePONumber()
    {
        try
        {
            var allPoNumbers = await _provider.LicenseManager.GetAllPoNumbers();
            getChildPO.Clear();
            var childpo = allPoNumbers.Where(x => !x.IsParent);
            foreach (var lic in childpo)
            {
                var childLicense = await _provider.LicenseManager.GetLicenseByParentIdAndParentPoNumber(lic.CompanyId, lic.PoNumber);
                getChildPO.AddRange((IEnumerable<ChildLicenseDetailByParentIdVm>)childLicense);
            }

            return Json(new { success = true, data = getChildPO });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }

    public async Task<IActionResult> GetChildPONumberByParentId(string parentPoNumber)
    {
        try
        {
            getChildPO.Clear();
            if (parentPoNumber != null)
            {
                var parentIds = parentPoNumber.Split(",");
                var allPoNumbers = await _provider.LicenseManager.GetAllPoNumbers();


                if (parentPoNumber != "All" && !string.IsNullOrEmpty(parentPoNumber))
                {
                    allPoNumbers = allPoNumbers.Where(x => parentIds.Any(id => x.Id.Contains(id))).ToList();
                }
                foreach (var lic in allPoNumbers)
                {
                    var childLicense = await _provider.LicenseManager.GetLicenseByParentIdAndParentPoNumber(lic.CompanyId, lic.PoNumber);
                    getChildPO.AddRange((IEnumerable<ChildLicenseDetailByParentIdVm>)childLicense);
                }
            }

            return Json(new { success = true, data = getChildPO });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetbusinessserviceName()
    {
        try
        {
            var businessservicename = await _provider.BusinessService.GetBusinessServiceNames();
            return Json(new { success = true, data = businessservicename });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    ////Resilience Readiness Report Operational Service
    public async Task<IActionResult> GetDRReadyBusinessServiceName()
    {
        try
        {
            List<InfraObjectListVm> serviceName = new List<InfraObjectListVm>();
            var drReadyView = await _provider.DrReady.GetDrReadyPaginatedList(new GetInfraObjectSchedulerPaginatedListQuery());
            var list = await _provider.InfraObject.GetInfraObjectList();
            foreach (var dr in drReadyView.Data)
            {
                foreach (var item in list)
                {
                    if (dr.InfraObjectId == item.Id)
                    {
                        serviceName.Add(item);
                    }
                }
            }
            List<InfraObjectListVm> distinctServiceNames = serviceName
                .GroupBy(x => x.BusinessServiceName)
                .Select(g => g.First())
                .ToList();
            return Json(new { success = true, data = distinctServiceNames });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    //Drdrill
    public async Task<IActionResult> GetWorkflow(string startDate, string endDate, string runMode)
    {
        try
        {
            var Workflow = await _provider.WorkflowOperation.GetDescriptionByStartTimeAndEndTime(startDate, endDate, runMode);
            return Json(new { success = true, data = Workflow });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetAllWorkflow(string workflowId, string type, string runMode, bool isCustom)
    {
        try
        {
            var drDrillReport = await _provider.Report.GetDrDrillReportByWorkflowOperationId(workflowId, runMode, isCustom);
            await GetCompanyLogo();
            drDrillReport.IsCustom = isCustom;
            if (drDrillReport.WorkflowOperationDrDrillReportVm != null)
            {
                string report = JsonConvert.SerializeObject(drDrillReport);
                return Json(new { success = true, data = report });
            }
            return Json(new { success = true, data = drDrillReport.WorkflowOperationDrDrillReportVm });

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetDRReadyStatus(string businessServiceId)
    {
        try
        {
            await GetCompanyLogo();
            businessServiceId = businessServiceId == "All" ? "" : businessServiceId;
            var readyReport = await _provider.Report.GetDrReadyStatusReportByBusinessServiceId(businessServiceId);
            var drReadyView = (await _provider.DrReady.GetDrReadyPaginatedList(new GetInfraObjectSchedulerPaginatedListQuery())).Data.ToDictionary(dataItem => dataItem.InfraObjectId);
            if (readyReport.DrReadyStatusForDrReadyReportVms.Count > 0)
            {
                //BusinessServiceName = readyReport.BusinessServiceName;
                foreach (var item in readyReport.DrReadyStatusForDrReadyReportVms)
                {
                    if (drReadyView.TryGetValue(item.InfraObjectId, out var dataItem))
                    {
                        item.AfterSwitchOverWorkflowId = dataItem.AfterSwitchOverWorkflowId;
                        item.AfterSwitchOverWorkflowName = dataItem.AfterSwitchOverWorkflowName;
                        item.BeforeSwitchOverWorkflowId = dataItem.BeforeSwitchOverWorkflowId;
                        item.BeforeSwitchOverWorkflowName = dataItem.BeforeSwitchOverWorkflowName;
                    }
                }
                string resiliencyReport = JsonConvert.SerializeObject(readyReport);
                return Json(new { success = true, data = resiliencyReport });
            }
            return Json(new { success = false, data = readyReport.DrReadyStatusForDrReadyReportVms });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }

    }
    public async Task<IActionResult> GetDrReadyExecution(string startDate, string endDate, string businessServiceId)
    {
        try
        {
            businessServiceId = businessServiceId == "All" ? "" : businessServiceId;
            var executionReport = await _provider.Report.GetDrReadyExecutionLogReport(startDate, endDate, businessServiceId);
            executionReport.DRReadyExecutionReportDataName = businessServiceId == "" ? "All Operational Service" : (await _provider.BusinessService.GetByReferenceId(businessServiceId))?.Name;
            executionReport.ActiveStartDate = startDate;
            executionReport.ActiveEndDate = endDate;
            await GetCompanyLogo();
            if (executionReport != null && executionReport.DRReadyExecutionReportVm.Count > 0)
            {
                var Executionlog = JsonConvert.SerializeObject(executionReport);
                return Json(new { success = true, data = Executionlog });
            }
            return Json(new { success = false, data = executionReport });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetInfraObjectSummary(string businessServiceId, string type)
    {
        try
        {            
            businessServiceId = businessServiceId == "All" ? "" : businessServiceId;
            var infra = await _provider.Report.GetInfraObjectSummaryReport(businessServiceId);
            await GetCompanyLogo();
            infra.InfraSummaryReportDataName = businessServiceId == "" ? "All Operational Service" : (await _provider.BusinessService.GetByReferenceId(businessServiceId))?.Name;
            if (infra != null && infra.InfraObjectSummaryReportVms.Count != 0)
            {
                string report = JsonConvert.SerializeObject(infra);
                return Json(new { success = true, data = report });
            }
            return Json(new { success = false, data = infra });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetRpoSlaDeviationReport(string businessServiceId, string infraObjectId, string startDate, string endDate)
    {
        try
        {
            var deviationReport = await _provider.Report.GetRpoSlaDeviationReportByStartTimeEndTimeAndBusinessServiceIdAndInfraObjectId(businessServiceId, infraObjectId, startDate, endDate);
            await GetCompanyLogo();
            deviationReport.REPOSLADeviationReportDataName = businessServiceId == "" ? "All Operational Service" : (await _provider.BusinessService.GetByReferenceId(businessServiceId))?.Name;
            deviationReport.ActiveStartDate = startDate;
            deviationReport.ActiveEndDate = endDate;
            if (deviationReport != null && deviationReport.RpoSlaDeviationReportByStartTimeAndEndTimeVms.Count != 0)
            {
                string report = JsonConvert.SerializeObject(deviationReport);
                return Json(new { success = true, data = report });
            }
            return Json(new { success = false, data = deviationReport });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetInfraObjectByBusinessServiceId(string businessServiceId)
    {
        try
        {
            var deviationReport = await _provider.InfraObject.GetInfraObjectByBusinessServiceId(businessServiceId);
            return Json(new { success = true, data = deviationReport });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetRpoSLAReport(string infraId, string type, string startDate, string endDate, string dateOption, string infraName)
    {
        try
        {          
            await GetCompanyLogo();
            var slaReport = await _provider.Report.GetRpoSlaReportByInfraObjectId(infraId, type, startDate, endDate, dateOption);
            string report = JsonConvert.SerializeObject(slaReport);
            return Json(new { success = true, data = report });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> UserReportGeneratedCount(string reportName)
    {
        try
        {
            await _publisher.Publish(new ReportViewedEvent { ReportName = reportName.Replace("\n", ""), ActivityType = ActivityType.Generate.ToString() }, CancellationToken.None);
            return Json(new { success = true });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetBusinessServiceSummaryReport()
    {
        try
        {
            var value = await _provider.Report.GetBusinessServiceSummaryReport();
            await GetCompanyLogo();
            if (value != null && value.BusinessServiceSummaryReportVms.Count > 0)
            {
                    string report = JsonConvert.SerializeObject(value);
                    return Json(new { success = true, data = report });
            }
            return Json(new { success = false, data = value });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }

    [HttpPost]
    [AllowAnonymous]
    [RequestSizeLimit(*********)]
    public ActionResult LoadReport([FromBody] JsonElement request)
    {
        try
        {
            string reportName = request.GetProperty("reportName").GetString();
            string responseData = request.GetProperty("responseData").GetString();
            ViewData[reportName + "Data"] = responseData;
            return PartialView(reportName, responseData);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return PartialView("EmptyAlert");
        }
    }

    [SupportedOSPlatform("windows")]
    public async Task<IActionResult> AllReportDownload([FromBody] JsonElement request)
    {
        string reportName = request.GetProperty("reportName").GetString();
        string type = request.GetProperty("type").GetString();
        string data = request.GetProperty("responseData").GetString();
        string LicenseType = string.Empty;
        string downloadReportName = request.GetProperty("downloadReportName").GetString();
        string RPOSLAReportType=string.Empty;
        if (reportName == "RPOSLAReport")
        {
            var jsonObject = JObject.Parse(data);
            RPOSLAReportType = jsonObject["InfraObjectType"].ToString();
        }

        if (reportName == "LicenseUtilizationReport")
        {
            var jsonObject = JObject.Parse(data);
            LicenseType = jsonObject["LicenseType"].ToString();
        } 
        bool IsCustom = false;
        if (reportName == "DRDrillReport")
        {
            var jsonObject = JObject.Parse(data);
            IsCustom = jsonObject["IsCustom"].Value<bool>();
        }
        var reportPath = "";    
        var reportsDirectory = "";
        var fileName = "";
        try
        {
            var ReportPathList = _provider.Setting.GetSettingBySKey("Report").Result;
            reportPath = ReportPathList?.SValue?.ToString();
            string extension, contentType;
            XtraReport report = new XtraReport();


            (extension, contentType) = type switch
            {
                "Excel" => ("xls", "application/vnd.ms-excel"),
                "PDF" => ("pdf", "application/pdf"),
                "Image" => ("png", "image/png"),
                _ => throw new ArgumentException("Invalid report type.")
            };

            report = reportName switch
            {
                "BusinessServiceSummaryReport" => type == "Excel" ?
                    new Report.ReportTemplate.BusinessServiceSummaryXlsReport(data) :
                    new Report.ReportTemplate.BusinessServiceSummaryReport(data),
                "DataLagStatusReport" => new Report.ReportTemplate.DataLagStatusReport(data),
                "SnapReport" => new Report.ReportTemplate.CyberSnapReport(data),
                "DriftReport" => type == "Excel" ?
                    new Report.ReportTemplate.DriftReportXLS(data) :
                    new Report.ReportTemplate.DriftReport(data),
                "BulkImportReport" => new Report.ReportTemplate.BulkImportReport(data),
                "DRDrillReport" => IsCustom switch
                {
                    false => type == "Excel" ?
                        new Report.ReportTemplate.DRDrillSummaryXLSReport(data) :
                        new Report.ReportTemplate.DRDrillSummaryReport(data),
                    true => new Report.ReportTemplate.WorkflowActionResult(data),
                },
                "InfraObjectConfigurationReport" => new Report.ReportTemplate.InfraObjectConfigurationReport(data),
                "InfraObjectSummaryReport" => type == "Excel" ?
                    new Report.ReportTemplate.InfraObjectSummaryExcel(data) :
                    new Report.ReportTemplate.InfraObjectSummary(data),
                "ResiliencyReadinessSchedulerLogReport" => new Report.ReportTemplate.ResilencyReadinessSchedulerLogReport(data),
                "CyberResiliencyScheduleLogReport" => new Report.ReportTemplate.CyberResiliencyScheduleLogReport(data),
                "LicenseUtilizationReport" => LicenseType switch
                {
                    "BusinessService" => type == "Excel" ?
                    new Report.ReportTemplate.LicenseUtilizationOperationalServiceXlsReport(data) :
                    new Report.ReportTemplate.LicenseUtilizationOperationalServiceReport(data),
                    "PONumber" => type == "Excel" ?
                      new Report.ReportTemplate.LicenseUtlizationXlsReport(data) :
                    new Report.ReportTemplate.LicenseUtlizationReport(data),
                    _ => throw new ArgumentException("Invalid License report type.")
                },

                "DRReadyReport" => new Report.ReportTemplate.DRReadyReport(data),
                "DRReadinessLog" => new Report.ReportTemplate.DRReadinessLog(data),
                "RPOSLADeviationReport" => new Report.ReportTemplate.RPOSLADeviationReport(data),
                "RTOReport" => type == "Excel" ?
                    new Report.ReportTemplate.RTOXlsReport(data) :
                    new Report.ReportTemplate.RTOReport(data),
                "UserActivityReport" => new Report.ReportTemplate.UserActivityReport(data),
                "AirGapReport" => new Report.ReportTemplate.AirGapReport(data),
                "RPOSLAReport" => RPOSLAReportType.ToLower() switch
                {
                    "oracle" => new Report.ReportTemplate.RPOSLAODG(data),
                    "mssqlalwayson" => new Report.ReportTemplate.RPOSLAMssqlAlwaysOn(data),
                    "oraclerac" => new Report.ReportTemplate.RPOSLAOracleRac(data),
                    "postgres" => new Report.ReportTemplate.RPOSLAPostgresSQL(data),
                    "mysql" => new Report.ReportTemplate.RPOSLAMySQLReport(data),
                    "mongodb" => new Report.ReportTemplate.RPOSLAMongoDBReport(data),
                    "db2hadr" => new Report.ReportTemplate.RPOSLADB2HADRReport(data),
                    "mssqldbmirroring" => new Report.ReportTemplate.RPOSLAMssqlDBMirroringReport(data),
                    "mssqlnls" => new Report.ReportTemplate.RPOSLAMssql2k19(data),
                    "robocopy" => new Report.ReportTemplate.RPOSLARoboCopyReport(data),
                    "rsyncappreplication" => new Report.ReportTemplate.RPOSLARSyncReport(data),
                    "rsyncdbreplication" => new Report.ReportTemplate.RPOSLARSyncReport(data),
                    "srm" => new Report.ReportTemplate.RPOSLASRMReport(data),
                    "azurestorageaccount" => new Report.ReportTemplate.RPOSLAAzureStorageAccountReport(data),
                    "activedirectory" => new Report.ReportTemplate.RPOSLAActiveDirectory(data),
                    "windowsactivedirectory" => new Report.ReportTemplate.RPOSLAActiveDirectory(data),
                    "datasyncappreplication" => new Report.ReportTemplate.RPOSLADataSyncReport(data),
                    "zertovpg" => new Report.ReportTemplate.RPOSLAZertoVPGReport(data),
                    "sybasershadr" => new Report.ReportTemplate.RPOSLASybaseRSHADRReport(data),
                    _ => throw new ArgumentException("Invalid RPOSLA report type.")
                },
                _ => throw new ArgumentException("Invalid report name.")
            };

            var filenameSuffix = DateTime.Now.ToString("ddMMyyyy_hhmmsstt");
            fileName = reportName switch
            {
                "BusinessServiceSummaryReport" => $"OperationalServiceSummaryReport_{filenameSuffix}.{extension}",
                "DataLagStatusReport" => $"DataLagStatusReport_{filenameSuffix}.{extension}",
                "DriftReport" => $"DriftReport_{filenameSuffix}.{extension}",
                "BulkImportReport" => $"{downloadReportName}_BulkImportReport_{filenameSuffix}.{extension}",
               // "WorkflowActionReport" => $"{WorkflowName}_WorkflowActionReport_{filenameSuffix}.{extension}",
                "SnapReport" => $"{downloadReportName}_SnapReport_{filenameSuffix}.{extension}",
               // "DRDrillReport" => $"{WorkflowName}_DRDrillSummaryReport_{filenameSuffix}.{extension}",
                "InfraObjectConfigurationReport" => $"{downloadReportName}_InfraObjectConfigurationReport_{filenameSuffix}.{extension}",
                "InfraObjectSummaryReport" => $"{downloadReportName}_InfraObjectSummaryReport_{filenameSuffix}.{extension}",
                "ResiliencyReadinessSchedulerLogReport" => $"ResiliencyReadinessSchedulerLogReport_{filenameSuffix}.{extension}",
                "CyberResiliencyScheduleLogReport" => $"CyberResiliencyScheduleLogReport{filenameSuffix}.{extension}",
                "AirGapReport" => $"{downloadReportName}_AirGapReport_{filenameSuffix}.{extension}",
                "LicenseUtilizationReport" => LicenseType switch
                {
                    "BusinessService" => $"LicenseUtilization_OperationalService_Report_{filenameSuffix}.{extension}",
                    "PONumber" => $"LicenseUtilization_PONumber_Report_{filenameSuffix}.{extension}",
                    _ => throw new ArgumentException("Invalid License report name.")
                },
                "DRDrillReport" => IsCustom switch
                {
                    false => $"{downloadReportName}_DRDrillSummaryReport_{filenameSuffix}.{extension}",
                    true => $"WorkflowActionReport_{filenameSuffix}.{extension}",                 
                },
                "DRReadyReport" => $"{downloadReportName}_Resiliency Readiness Report_{filenameSuffix}.{extension}",
                "DRReadinessLog" => $"ResiliencyReadinessExecutionLogReport_{filenameSuffix}.{extension}",
                "RPOSLADeviationReport" => $"{downloadReportName}_RPOSLADeviationReport_{filenameSuffix}.{extension}",
                "RTOReport" => $"{downloadReportName}_RTOReport_{filenameSuffix}.{extension}",
                "UserActivityReport" => $"{downloadReportName.Replace("\\", "_")}_User Audit Report_{filenameSuffix}.{extension}",
                "RPOSLAReport" => $"RPOSLAReport_{downloadReportName}_{filenameSuffix}.{extension}",
                _ => throw new ArgumentException("Invalid report name.")
            };
            string clientIp = HttpContext.Connection.RemoteIpAddress?.ToString();
            string serverIp = GetServerIpAddress();
            if (clientIp == serverIp || clientIp == "::1" || clientIp == "127.0.0.1")
            {
                reportsDirectory = reportPath.IsNullOrEmpty()
               ? Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName)
               : Path.Combine(reportPath, fileName);
            }
            else
            {
                reportsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName);
            }

            switch (type)
            {
                case "Excel":
                    await report.ExportToXlsAsync(reportsDirectory);
                    break;
                case "PDF":
                    await report.ExportToPdfAsync(reportsDirectory);
                    break;
                case "Image":
                    await report.ExportToImageAsync(reportsDirectory);
                    break;
            }

            _logger.LogInformation($"{fileName} was downloaded successfully.");

            if (reportsDirectory == Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName))
            {
                byte[] fileBytes = System.IO.File.ReadAllBytes(reportsDirectory);
                return File(fileBytes, contentType, fileName);
            }

            return Content($"{fileName}_downloaded successfully: {reportPath}");
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on preBuildReport page while downloading {reportName}.", ex);
            return Content($"An error occurred while generating the report. {ex.Message}");
        }
        finally
        {
            if (System.IO.File.Exists(reportsDirectory) && reportsDirectory == Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName))
            {
                System.IO.File.Delete(reportsDirectory);
            }
        }
    }
    private string GetServerIpAddress()
    {
        var hostName = System.Net.Dns.GetHostName();
        var serverIp = System.Net.Dns.GetHostAddresses(hostName)
            .FirstOrDefault(ip => ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork);

        return serverIp?.ToString() ?? "127.0.0.1";
    }
    public async Task GetCompanyLogo()
    {
        CompanyLogo = string.Empty;
        var companyDetails = await _provider.Company.GetCompanyById(WebHelper.UserSession.CompanyId);
        if (!string.IsNullOrEmpty(companyDetails.CompanyLogo) && companyDetails.CompanyLogo != "NA") { CompanyLogo = companyDetails.CompanyLogo.ToString(); }
    }
    public async Task<IActionResult> GetAirGapReport(string startDate,string endDate,string airGapId)
    {
        _logger.LogInformation("Entered the  GetAirGapReport Method");
        try
        {
            
            await GetCompanyLogo();
            var cyberAirGap = await _provider.Report.GetAirGapReport(startDate, endDate,airGapId);
            if (cyberAirGap.CyberAirGapLogLists.Count > 0)
            {
                var list = cyberAirGap.CyberAirGapLogLists;               
                string report= JsonConvert.SerializeObject(cyberAirGap);    
                _logger.LogInformation("Get airGaplog Report values Successfully");
                return Json(new { success = true, data = report });
            }
            else
            {
                return Json(new { success = false,data=cyberAirGap });
            }
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetAirGapList(string startDate, string endDate)
    {
        try
        {
            var airGapList = await _provider.Report.GetAirGapList(startDate,endDate);
            return Json(new { success = true, data = airGapList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetDriftReport(string startDate, string endDate, string InfraId,string DriftStatusId)
    {
        try
        {         
            await GetCompanyLogo();
            var driftReport = await _provider.Report.GetDriftReport(startDate,endDate,InfraId,DriftStatusId);
            if (driftReport.DriftEventReportVm.Count > 0)
            {
                var driftreport = JsonConvert.SerializeObject(driftReport);
                return Json(new { success = true, data = driftreport });
            }
            return Json(new { success = false, data= driftReport });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetDriftReportInfraName(string startDate,string endDate)
    {
        try
        {
            var getInfraObjectId = await _provider.Report.GetDriftReportInfraId(startDate, endDate);
            getInfraObjectId = getInfraObjectId.Where(x => x.InfraObjectId != null).ToList();

            return Json(new { success = true, data = getInfraObjectId });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page  while getting the InfraObject list.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetDriftStatus(string startDate, string endDate,string InfraId)
    {
        try
        {
            var getDriftStatus = await _provider.Report.GetDriftReportStatus(startDate,endDate,InfraId);

            return Json(new { success = true, data = getDriftStatus });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page  while getting the InfraObject list.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetInfraObjectIdBulkImport(string startDate, string endDate)
    {
        try
        {
            var bulks = await _provider.BulkImportOperation.GetDescriptionBulkImportStartAndEndTime(startDate, endDate);
            return Json(new { success = true, data = bulks });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> Download_BulkImport_Report(string operationId, string operationName)
    {
        try
        {
            var bulkImportReportDetails = await _provider.Report.GetBulkImportReport(operationId);
            if (bulkImportReportDetails != null && bulkImportReportDetails.BulkImportOperationGroupListVms.Count != 0)
            {
                string report = JsonConvert.SerializeObject(bulkImportReportDetails);
                return Json(new { success = true, data = report });
            }
            return Json(new { success = false, data = bulkImportReportDetails });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while generating BulkImportReport.", ex);
            return Content("An error occurred while generating BulkImportReport.");
        }
    }
    public async Task<IActionResult> GetCyberSnapsList(string snapStartDate, string snapEndDate)
    {
        try
        {
            var getCyberSnapsList = await _provider.Report.GetCyberSnapsList(snapStartDate, snapEndDate);
            if (getCyberSnapsList.Count > 0)
            {
                _logger.LogInformation($"Get all cybersnaps list values successfully.");
            }
            else
            {
                _logger.LogError($"An error occured in cybersnaps lists has no values.");
            }
            return Json(new { success = true, data = getCyberSnapsList });
        }
        catch(Exception ex)
        {
            _logger.Exception("An error occurred on GetCyberSnapsList method while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetCyberSnapsBySnapId(string cyberSnapTagName, string snapStartDate, string snapEndDate)
    {
        try
        {
            await GetCompanyLogo();
            var getCyberSnapsBySnapId = await _provider.Report.GetCyberSnapsBySnapTagName(cyberSnapTagName, snapStartDate, snapEndDate);
            cyberSnapTagName = cyberSnapTagName == "all" ? "" : cyberSnapTagName;
            getCyberSnapsBySnapId.CyberSnapReportDataName = cyberSnapTagName == "" ? "All_SnapTag" : cyberSnapTagName;
            if (getCyberSnapsBySnapId != null && getCyberSnapsBySnapId.CyberSnapsReportVm.Count != 0)
            {
                string report = JsonConvert.SerializeObject(getCyberSnapsBySnapId);
                return Json(new { success = true, data = report });
            }
            return Json(new { success = false, data = getCyberSnapsBySnapId });
        }
        catch(Exception ex)
        {
            _logger.Exception("An error occurred on GetCyberSnapsBySnapId method while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetAirgaplists()
    {
        try
        {
            var airGapList = await _provider.CyberAirGapStatus.GetCyberAirGapStatusList();
            return Json(new { success = true, data = airGapList });
        }
        catch(Exception ex)
        {
            _logger.Exception("An error occurred on preBuildReport page while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetResiliencyReadinessSchedulerLogReportList(string startDate, string endDate)
    {
        try
        {
            await GetCompanyLogo();
            var InfraObjectSchedulerLogReport = await _provider.Report.GetInfraObjectSchedulerLogList(startDate, endDate);
            if (InfraObjectSchedulerLogReport != null && InfraObjectSchedulerLogReport.InfraObjectSchedulerLogsReportList.Count != 0)
            {
                InfraObjectSchedulerLogReport.InfraObjectSchedulerLogsReportList = InfraObjectSchedulerLogReport
                   .InfraObjectSchedulerLogsReportList
                   .Where(x => x.WorkflowType != "Cyber Resiliency")
                   .ToList();
                if (InfraObjectSchedulerLogReport != null && InfraObjectSchedulerLogReport.InfraObjectSchedulerLogsReportList.Count != 0)
                {
                    string report = JsonConvert.SerializeObject(InfraObjectSchedulerLogReport);
                    return Json(new { success = true, data = report });
                }
            }
            return Json(new { success = false, data = InfraObjectSchedulerLogReport, message = "No data found" });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuild Report GetInfraObjectSchedulerReportList method while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public async Task<IActionResult> GetCyberResiliencyScheduleLogReportList(string startDate, string endDate)
    {
        try
        {
            await GetCompanyLogo();
            var CyberResiliencyScheduleLogReport = await _provider.Report.GetInfraObjectSchedulerLogList(startDate, endDate);
            if (CyberResiliencyScheduleLogReport != null && CyberResiliencyScheduleLogReport.InfraObjectSchedulerLogsReportList.Count != 0)
            {
                CyberResiliencyScheduleLogReport.InfraObjectSchedulerLogsReportList = CyberResiliencyScheduleLogReport
                    .InfraObjectSchedulerLogsReportList
                    .Where(x => x.WorkflowType == "Cyber Resiliency")
                    .ToList();
                if (CyberResiliencyScheduleLogReport != null && CyberResiliencyScheduleLogReport.InfraObjectSchedulerLogsReportList.Count != 0)
                {
                    string report = JsonConvert.SerializeObject(CyberResiliencyScheduleLogReport);
                    return Json(new { success = true, data = report });
                }
            }
            return Json(new { success = false, data = CyberResiliencyScheduleLogReport, message ="No data found"});
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on preBuild Report GetCyberResiliencyScheduleReportList method while processing the request.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
}