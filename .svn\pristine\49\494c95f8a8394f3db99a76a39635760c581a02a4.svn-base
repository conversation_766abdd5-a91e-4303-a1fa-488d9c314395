﻿using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Create;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Update;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Create;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class NodeConfigurationFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<LoadBalancer> NodeConfigurations { get; set; }

    public CreateLoadBalancerCommand CreateNodeConfigurationCommand { get; set; }
    public UpdateLoadBalancerCommand UpdateNodeConfigurationCommand { get; set; }

    //public NodeConfigurationCreatedEvent NodeConfigurationCreatedEvent { get; set; }
    //public NodeConfigurationDeletedEvent NodeConfigurationDeletedEvent { get; set; }
    //public NodeConfigurationUpdatedEvent NodeConfigurationUpdatedEvent { get; set; }


    public NodeConfigurationFixture()
    {
        NodeConfigurations = AutoNodeConfigurationFixture.Create<List<LoadBalancer>>();

        CreateNodeConfigurationCommand = AutoNodeConfigurationFixture.Create<CreateLoadBalancerCommand>();

        UpdateNodeConfigurationCommand = AutoNodeConfigurationFixture.Create<UpdateLoadBalancerCommand>();

        //NodeConfigurationCreatedEvent = AutoNodeConfigurationFixture.Create<NodeConfigurationCreatedEvent>();

        //NodeConfigurationDeletedEvent = AutoNodeConfigurationFixture.Create<NodeConfigurationDeletedEvent>();

        //NodeConfigurationUpdatedEvent = AutoNodeConfigurationFixture.Create<NodeConfigurationUpdatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<LoadBalancerProfile>();
        });

        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoNodeConfigurationFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateLoadBalancerCommand>(p => p.Name, 10));
            fixture.Customize<CreateLoadBalancerCommand>(c => c.With(b => b.Name, 0.ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateLoadBalancerCommand>(p => p.Name, 10));
            fixture.Customize<UpdateLoadBalancerCommand>(c => c.With(b => b.Id, 0.ToString));
            fixture.Customize<LoadBalancer>(c => c.With(b => b.IsActive, true));

            //fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<NodeConfigurationCreatedEvent>(p => p.SiteName, 10));

            //fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<NodeConfigurationDeletedEvent>(p => p.SiteName, 10));

            //fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<NodeConfigurationUpdatedEvent>(p => p.SiteName, 10));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}