﻿using ContinuityPatrol.Application.Features.Company.Queries.GetDetail;
using ContinuityPatrol.Application.Features.User.Commands.Create;
using ContinuityPatrol.Application.Features.User.Commands.ResetPassword;
using ContinuityPatrol.Application.Features.User.Commands.Update;
using ContinuityPatrol.Application.Features.User.Commands.UpdatePassword;
using ContinuityPatrol.Application.Features.User.Commands.UserLock;
using ContinuityPatrol.Application.Features.User.Commands.UserUnLock;
using ContinuityPatrol.Application.Features.User.Events.PaginatedView;
using ContinuityPatrol.Application.Features.User.Queries.GetLoginName;
using ContinuityPatrol.Application.Features.User.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.User.Queries.GetUserProfile;
using ContinuityPatrol.Application.Features.UserInfo.Commands.Update;
using ContinuityPatrol.Application.Features.UserInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByBusinessService;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Domain.ViewModels.UserActivityModel;
using ContinuityPatrol.Domain.ViewModels.UserModel.ChangePasswordModels;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels;
using ContinuityPatrol.Domain.ViewModels.UserRoleModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Helper;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;
using FluentValidation.Results;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Routing;
using System.Reflection;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class UserControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IUserService> _mockUserService = new();
        private readonly Mock<ILogger<UserController>> _mockLogger = new();
        private readonly Mock<IUrlHelper> _mockUrlHelper = new();
        private UserController _controller;

        public UserControllerShould()
        {
            Initialize();
        }
        internal void Initialize()
        {
            var httpContext = new DefaultHttpContext();
            httpContext.Session = new TestSession();

            // Setup WebHelper.UserSession for tests that need it BEFORE creating controller
            var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            mockHttpContextAccessor.Setup(x => x.HttpContext).Returns(httpContext);
            WebHelper.Configure(mockHttpContextAccessor.Object);

            WebHelper.UserSession = new UserSession
            {
                LoginName = "testuser",
                AdUserName = "testaduser",
                AuthenticationType = "Local",
                IsUserAuthenticated = true
            };

            // Pre-populate the session with UnlockAttempt to avoid null reference
            // The BuildFullKey method adds "Web.UI." prefix, so we need to set the full key
            httpContext.Session.SetString("Web.UI.UnlockAttempt", "0");

            _controller = new UserController(
                _mockPublisher.Object,
                _mockDataProvider.Object,
                _mockMapper.Object,
                //_mockUserService.Object,
                _mockLogger.Object
            );

            _controller.ControllerContext.HttpContext = httpContext;
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");

            // Setup URL helper mock - only set if needed for specific tests
            // _mockUrlHelper.Setup(x => x.Action(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<object>()))
            //     .Returns("/test-url");
            // _controller.Url = _mockUrlHelper.Object;
        }

        [Fact]
        public void TestControllerInstantiation()
        {
            // Test that the controller can be instantiated without throwing exception
            try
            {
                var controller = new UserController(
                    _mockPublisher.Object,
                    _mockDataProvider.Object,
                    _mockMapper.Object,
                    _mockLogger.Object
                );
                Assert.True(true, "Controller instantiated successfully");
            }
            catch (Exception ex)
            {
                Assert.True(false, $"Controller instantiation failed: {ex.GetType().Name}: {ex.Message}\nStack Trace: {ex.StackTrace}");
            }
        }

        [Fact]
        public void TestWebHelperSetup()
        {
            // Test that WebHelper.CurrentSession.Get works without throwing exception
            try
            {
                var testValue = WebHelper.CurrentSession.Get<string>("UnlockAttempt");
                Assert.True(true, $"WebHelper.CurrentSession.Get worked. Returned: {testValue ?? "null"}");
            }
            catch (Exception ex)
            {
                Assert.True(false, $"WebHelper.CurrentSession.Get failed: {ex.GetType().Name}: {ex.Message}\nStack Trace: {ex.StackTrace}");
            }
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesUser_WhenIdIsNotNull()
        {
            var userViewModel = new AutoFixture.Fixture().Create<UserViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
            {
                { "id", "22" }
            };
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateUserCommand = new UpdateUserCommand();
            var response = new BaseResponse { Success = true, Message = "User updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateUserCommand>(userViewModel)).Returns(updateUserCommand);
            _mockDataProvider.Setup(dp => dp.User.UpdateAsync(updateUserCommand)).Returns(Task.FromResult(response));

            var result = await _controller.CreateOrUpdate(userViewModel) as JsonResult;

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"Message\":", json);
        }

        [Fact]
        public async Task ChangePassword_UpdatesPassword_WhenModelIsValid()
        {
            // Arrange
            var changePasswordViewModel = new AutoFixture.Fixture().Create<ChangePasswordViewModel>();
            var updatePasswordCommand = new UpdatePasswordCommand();
            var response = new BaseResponse { Success = true, Message = "Password changed successfully", ErrorCode = 104 };

            _mockMapper.Setup(m => m.Map<UpdatePasswordCommand>(changePasswordViewModel)).Returns(updatePasswordCommand);
            _mockDataProvider.Setup(us => us.User.UpdateUserPassword(updatePasswordCommand)).Returns(Task.FromResult(response));

            // Act
            var result = await _controller.ChangePassword(changePasswordViewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var expectedValue = new { Success = true, data = response };
            Assert.Equal(JsonConvert.SerializeObject(expectedValue), JsonConvert.SerializeObject(result.Value));
        }

        [Fact]
        public async Task GetDomains_ReturnsDomains_WhenSuccessful()
        {
            // Arrange
            var domains = new List<string>();
            _mockDataProvider.Setup(dp => dp.User.GetDomainList()).Returns(Task.FromResult(domains));

            // Act
            var result = await _controller.GetDomains() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonResponse = result.Value as dynamic;
            var json = JsonConvert.SerializeObject(jsonResponse);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetCompanyById_ReturnsCompany_WhenIdIsValid()
        {
            // Arrange
            var company = new CompanyDetailVm();
            _mockDataProvider.Setup(dp => dp.Company.GetCompanyById("1")).Returns(Task.FromResult(company));

            // Act
            var result = await _controller.GetCompanyById("1") as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonResponse = result.Value as dynamic;
            Assert.Equal(company, jsonResponse);
        }

        [Fact]
        public async Task ResetPassword_ResetsPassword_WhenModelIsValid()
        {
            // Arrange
            var resetUser = new AutoFixture.Fixture().Create <ResetPasswordCommand>(); 
            var resetPassword = new UserViewModel();
            _mockMapper.Setup(m => m.Map<UserViewModel>(resetUser)).Returns(resetPassword);
            // Act
            var result = await _controller.ResetPassword(resetPassword) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
            
        }

        [Fact]
        public async Task Unlock_UnLocksUser_WhenModelIsValid()
        {
            // Arrange
            var unlockUser = new AutoFixture.Fixture().Create<UserViewModel> ();
            var response = new BaseResponse { Success = true, Message = "User unlocked successfully" };

            _mockMapper.Setup(m => m.Map<UserUnLockCommand>(unlockUser.UserUnLockModel)).Returns(new UserUnLockCommand());
            _mockDataProvider.Setup(dp => dp.User.UserUnLock(It.IsAny<UserUnLockCommand>())).Returns(Task.FromResult(response));

            // Act
            var result = await _controller.Unlock(unlockUser) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            //Assert.Equal("User", result.ControllerName);
        }

        // ===== MISSING TEST CASES FOR 100% COVERAGE =====

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new UserController(
                _mockPublisher.Object,
                _mockDataProvider.Object,
                _mockMapper.Object,
                _mockLogger.Object);

            // Assert
            Assert.NotNull(controller);
        }

        [Fact]
        public async Task List_ShouldReturnViewWithUserViewModel()
        {
            // Arrange
            var companyNames = new List<CompanyNameVm>();
            _mockDataProvider.Setup(dp => dp.Company.GetCompanyNamesOnLogin())
                .Returns(Task.FromResult(companyNames));

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.IsType<UserViewModel>(result.Model);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<UserPaginatedEvent>(), default), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleValidationException()
        {
            // Arrange
            var userViewModel = new AutoFixture.Fixture().Create<UserViewModel>();
            userViewModel.Id = "";
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("LoginName", "LoginName is required"));
            var validationException = new ValidationException(validationResult);

            var command = new CreateUserCommand();
            _mockMapper.Setup(m => m.Map<CreateUserCommand>(userViewModel))
                .Returns(command);
            _mockDataProvider.Setup(s => s.User.CreateAsync(command))
                .ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(userViewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleGeneralException()
        {
            // Arrange
            var userViewModel = new AutoFixture.Fixture().Create<UserViewModel>();
            userViewModel.Id = "";
            var exception = new Exception("General error");

            var command = new CreateUserCommand();
            _mockMapper.Setup(m => m.Map<CreateUserCommand>(userViewModel))
                .Returns(command);
            _mockDataProvider.Setup(s => s.User.CreateAsync(command))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.CreateOrUpdate(userViewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task IsLoginNameExist_ShouldReturnTrue_WhenNameExists()
        {
            // Arrange
            var loginName = "testuser";
            var id = "1";
            _mockDataProvider.Setup(dp => dp.User.IsLoginNameExist(loginName, id))
                .Returns(Task.FromResult(true));

            // Act
            var result = await _controller.IsLoginNameExist(loginName, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task IsLoginNameExist_ShouldReturnFalse_WhenExceptionOccurs()
        {
            // Arrange
            var loginName = "testuser";
            var id = "1";
            _mockDataProvider.Setup(dp => dp.User.IsLoginNameExist(loginName, id))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.IsLoginNameExist(loginName, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetDomains_ShouldReturnFailure_WhenExceptionOccurs()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.User.GetDomainList())
                .ThrowsAsync(new Exception("Domain error"));

            // Act
            var result = await _controller.GetDomains() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetDomainUsers_ShouldReturnUsers_WhenSuccessful()
        {
            // Arrange
            var domainName = "testdomain";
            var domainUserName = "testuser";
            var domainUsers = new List<string> { "user1", "user2" };
            _mockDataProvider.Setup(dp => dp.User.GetDomainUsers(domainName, domainUserName))
                .Returns(Task.FromResult(domainUsers));

            // Act
            var result = await _controller.GetDomainUsers(domainName, domainUserName);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
        }

        [Fact]
        public async Task GetDomainUsers_ShouldReturnEmptyList_WhenExceptionOccurs()
        {
            // Arrange
            var domainName = "testdomain";
            var domainUserName = "testuser";
            _mockDataProvider.Setup(dp => dp.User.GetDomainUsers(domainName, domainUserName))
                .ThrowsAsync(new Exception("Domain error"));

            // Act
            var result = await _controller.GetDomainUsers(domainName, domainUserName);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetDomainGroups_ShouldReturnGroups_WhenSuccessful()
        {
            // Arrange
            var domainName = "testdomain";
            var domainUserName = "testuser";
            var domainGroups = new List<string> { "group1", "group2" };
            _mockDataProvider.Setup(dp => dp.User.GetDomainGroups(domainName, domainUserName))
                .Returns(Task.FromResult(domainGroups));

            // Act
            var result = await _controller.GetDomainGroups(domainName, domainUserName);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
        }

        [Fact]
        public async Task GetDomainGroups_ShouldReturnEmptyList_WhenExceptionOccurs()
        {
            // Arrange
            var domainName = "testdomain";
            var domainUserName = "testuser";
            _mockDataProvider.Setup(dp => dp.User.GetDomainGroups(domainName, domainUserName))
                .ThrowsAsync(new Exception("Domain error"));

            // Act
            var result = await _controller.GetDomainGroups(domainName, domainUserName);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetAllInfraObjectList_ShouldReturnInfraObjects_WhenSuccessful()
        {
            // Arrange
            var companyId = "1";
            var infraObjects = new GetUserInfraObjectByBusinessServiceVm();
            _mockDataProvider.Setup(dp => dp.UserInfraObject.GetUserInfraObjects(companyId))
                .Returns(Task.FromResult(infraObjects));

            // Act
            var result = await _controller.GetAllInfraObjectList(companyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(infraObjects, result);
        }

        [Fact]
        public async Task GetAllInfraObjectList_ShouldReturnEmptyObject_WhenExceptionOccurs()
        {
            // Arrange
            var companyId = "1";
            _mockDataProvider.Setup(dp => dp.UserInfraObject.GetUserInfraObjects(companyId))
                .ThrowsAsync(new Exception("Infra error"));

            // Act
            var result = await _controller.GetAllInfraObjectList(companyId);

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task Delete_ShouldReturnRedirectToList_WhenSuccessful()
        {
            // Arrange
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "User deleted successfully" };
            _mockDataProvider.Setup(dp => dp.User.DeleteAsync(id))
                .Returns(Task.FromResult(response));

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task Delete_ShouldReturnRedirectToList_WhenExceptionOccurs()
        {
            // Arrange
            var id = "1";
            _mockDataProvider.Setup(dp => dp.User.DeleteAsync(id))
                .ThrowsAsync(new Exception("Delete error"));

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public void Lock_ShouldReturnView_WhenSuccessful()
        {
            // Act
            var result = _controller.Lock() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.IsType<UserLockCommand>(result.Model);
        }

        [Fact]
        public async Task LockLogin_ShouldReturnSuccess_WhenAuthenticationSucceeds()
        {
            // Arrange
            var userLock = new UserLockCommand
            {
                UserName = "testuser",
                Password = "password",
                AuthenticationType = "Local"
            };
            var response = new BaseResponse { Success = true, Message = "Authentication successful" };
            _mockDataProvider.Setup(dp => dp.User.UsersAuthentication(userLock))
                .Returns(Task.FromResult(response));

            // Act
            var result = await _controller.LockLogin(userLock) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task LockLogin_ShouldReturnFailure_WhenAuthenticationFails()
        {
            // Arrange
            var userLock = new UserLockCommand
            {
                UserName = "testuser",
                Password = "wrongpassword",
                AuthenticationType = "Local"
            };
            var response = new BaseResponse { Success = false, Message = "Authentication failed" };
            _mockDataProvider.Setup(dp => dp.User.UsersAuthentication(userLock))
                .Returns(Task.FromResult(response));

            // Act
            var result = await _controller.LockLogin(userLock) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":false", json);
        }

        [Fact]
        public async Task LockLogin_ShouldReturnFailure_WhenModelStateIsInvalid()
        {
            // Arrange
            var userLock = new UserLockCommand();
            _controller.ModelState.AddModelError("UserName", "UserName is required");

            // Act
            var result = await _controller.LockLogin(userLock) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":false", json);
            Assert.Contains("\"validCount\":2", json);
        }

        [Fact]
        public async Task LockLogin_ShouldReturnFailure_WhenExceptionOccurs()
        {
            // Arrange
            var userLock = new UserLockCommand
            {
                UserName = "testuser",
                Password = "password",
                AuthenticationType = "Local"
            };
            _mockDataProvider.Setup(dp => dp.User.UsersAuthentication(userLock))
                .ThrowsAsync(new Exception("Authentication error"));

            // Setup URL helper for this specific test that needs it
            var mockUrlHelper = new Mock<IUrlHelper>();
            mockUrlHelper.Setup(x => x.Action(It.IsAny<UrlActionContext>())).Returns("/test-url");
            _controller.Url = mockUrlHelper.Object;

            // Act
            var result = await _controller.LockLogin(userLock) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":false", json);
        }

        [Fact]
        public async Task UserActivityProfile_ShouldReturnJsonResult_WhenSuccessful()
        {
            // Arrange
            var startDate = "2023-01-01";
            var endDate = "2023-01-31";
            var userActivity = new List<UserActivityListVm>();
            _mockDataProvider.Setup(dp => dp.UserActivity.GetStartTimeEndTimeByUser(It.IsAny<string>(), startDate, endDate))
                .Returns(Task.FromResult(userActivity));

            // Act
            var result = await _controller.UserActivityProfile(startDate, endDate) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task UserActivityProfile_ShouldReturnJsonException_WhenExceptionOccurs()
        {
            // Arrange
            var startDate = "2023-01-01";
            var endDate = "2023-01-31";
            _mockDataProvider.Setup(dp => dp.UserActivity.GetStartTimeEndTimeByUser(It.IsAny<string>(), startDate, endDate))
                .ThrowsAsync(new Exception("Activity error"));

            // Act
            var result = await _controller.UserActivityProfile(startDate, endDate) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetUserLoginName_ShouldReturnSuccess_WhenSuccessful()
        {
            // Arrange
            var loginName = "testuser";
            var user = new UserLoginNameVm();
            _mockDataProvider.Setup(dp => dp.User.GetUserByLoginName(loginName))
                .Returns(Task.FromResult(user));

            // Act
            var result = await _controller.GetUserLoginName(loginName) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetUserLoginName_ShouldReturnFailure_WhenExceptionOccurs()
        {
            // Arrange
            var loginName = "testuser";
            _mockDataProvider.Setup(dp => dp.User.GetUserByLoginName(loginName))
                .ThrowsAsync(new Exception("User error"));

            // Act
            var result = await _controller.GetUserLoginName(loginName) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public void SiteAdminLanding_ShouldReturnView()
        {
            // Act
            var result = _controller.SiteAdminLanding() as ViewResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task UserProfiles_ShouldReturnJson_WhenSuccessful()
        {
            // Arrange
            var userId = "1";
            var userProfile = new UserProfileDetailVm();
            _mockDataProvider.Setup(dp => dp.User.GetUserProfile(userId))
                .Returns(Task.FromResult(userProfile));

            // Act
            var result = await _controller.UserProfiles(userId) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(userProfile, result.Value);
        }

        [Fact]
        public async Task UserProfiles_ShouldReturnJsonException_WhenExceptionOccurs()
        {
            // Arrange
            var userId = "1";
            _mockDataProvider.Setup(dp => dp.User.GetUserProfile(userId))
                .ThrowsAsync(new Exception("Profile error"));

            // Act
            var result = await _controller.UserProfiles(userId) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task UpdateUserProfileImage_ShouldReturnFailure_WhenUserIdIsInvalid()
        {
            // Arrange
            var command = new UserInfoCommand { UserId = "" };

            // Act
            var result = await _controller.UpdateUserProfileImage(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("UserId is not valid format", json);
        }

        [Fact]
        public async Task UpdateUserProfileImage_ShouldReturnSuccess_WhenSuccessful()
        {
            // Arrange
            var command = new UserInfoCommand { UserId = "1", LogoName = "logo.png" };
            var userProfile = new UserInfoDetailVm { UserName = "testuser" };
            var updateCommand = new UpdateUserInfoCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockDataProvider.Setup(dp => dp.User.GetUserProfileById(command.UserId))
                .Returns(Task.FromResult(userProfile));
            _mockMapper.Setup(m => m.Map<UpdateUserInfoCommand>(userProfile))
                .Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.User.UpdateUserProfileImage(updateCommand))
                .Returns(Task.FromResult(response));

            // Act
            var result = await _controller.UpdateUserProfileImage(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(response, result.Value);
        }

        [Fact]
        public async Task UpdateUserProfileImage_ShouldReturnJsonException_WhenExceptionOccurs()
        {
            // Arrange
            var command = new UserInfoCommand { UserId = "1", LogoName = "logo.png" };
            _mockDataProvider.Setup(dp => dp.User.GetUserProfileById(command.UserId))
                .ThrowsAsync(new Exception("Profile error"));

            // Act
            var result = await _controller.UpdateUserProfileImage(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task UserProfileDelete_ShouldReturnFailure_WhenUserIdIsInvalid()
        {
            // Arrange
            var command = new UserInfoCommand { UserId = "" };

            // Act
            var result = await _controller.UserProfileDelete(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("UserId is not valid format", json);
        }

        [Fact]
        public async Task UserProfileDelete_ShouldReturnSuccess_WhenSuccessful()
        {
            // Arrange
            var command = new UserInfoCommand { UserId = "1", LogoName = "logo.png" };
            var userProfile = new UserInfoDetailVm { UserName = "testuser" };
            var updateCommand = new UpdateUserInfoCommand();
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };

            _mockDataProvider.Setup(dp => dp.User.GetUserProfileById(command.UserId))
                .Returns(Task.FromResult(userProfile));
            _mockMapper.Setup(m => m.Map<UpdateUserInfoCommand>(userProfile))
                .Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.User.UpdateUserProfileImage(updateCommand))
                .Returns(Task.FromResult(response));

            // Act
            var result = await _controller.UserProfileDelete(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("UserProfile 'testuser' image Delete successfully", json);
        }

        [Fact]
        public async Task UserProfileDelete_ShouldReturnJsonException_WhenExceptionOccurs()
        {
            // Arrange
            var command = new UserInfoCommand { UserId = "1", LogoName = "logo.png" };
            _mockDataProvider.Setup(dp => dp.User.GetUserProfileById(command.UserId))
                .ThrowsAsync(new Exception("Profile error"));

            // Act
            var result = await _controller.UserProfileDelete(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public void ChangePassword_Get_ShouldReturnView()
        {
            // Act
            var result = _controller.ChangePassword() as ViewResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task ChangePassword_ShouldReturnJsonException_WhenExceptionOccurs()
        {
            // Arrange
            var changePasswordViewModel = new AutoFixture.Fixture().Create<ChangePasswordViewModel>();
            _mockMapper.Setup(m => m.Map<UpdatePasswordCommand>(changePasswordViewModel))
                .Throws(new Exception("Mapping error"));

            // Act
            var result = await _controller.ChangePassword(changePasswordViewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public void HashPassword_ShouldReturnEncryptedPassword_WhenLoginNameIsProvided()
        {
            // Arrange
            var loginName = "testuser";
            var password = "password123";

            // Act
            var result = _controller.HashPassword(loginName, password) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("encrypt", json);
        }

        [Fact]
        public void HashPassword_ShouldReturnEncryptedPassword_WhenLoginNameIsEmpty()
        {
            // Arrange
            var loginName = "";
            var password = "password123";

            // Act
            var result = _controller.HashPassword(loginName, password) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("encrypt", json);
        }

        [Fact]
        public void HashPassword_ShouldReturnEncryptedValue_WhenPasswordIsNull()
        {
            // Arrange
            var loginName = "testuser";
            string password = null; // This will be processed as empty

            // Act
            var result = _controller.HashPassword(loginName, password) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("encrypt", json);
        }

        [Fact]
        public void HashPasswordForEmail_ShouldReturnEncryptedPassword_WhenPasswordIsProvided()
        {
            // Arrange
            var password = "password123";

            // Act
            var result = _controller.HashPasswordForEmail(password) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("encrypt", json);
        }

        [Fact]
        public void HashPasswordForEmail_ShouldReturnEmptyEncrypt_WhenPasswordIsEmpty()
        {
            // Arrange
            var password = "";

            // Act
            var result = _controller.HashPasswordForEmail(password) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"encrypt\":\"\"", json);
        }

        [Fact]
        public void HashPasswordForEmail_ShouldReturnEmptyEncrypt_WhenPasswordIsNull()
        {
            // Arrange
            string password = null; // This will return empty encrypt

            // Act
            var result = _controller.HashPasswordForEmail(password) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"encrypt\":\"\"", json);
        }

        [Fact]
        public void DecryptPassword_ShouldReturnDecryptedPassword_WhenPasswordIsProvided()
        {
            // Arrange
            var password = "encryptedpassword";

            // Act
            var result = _controller.DecryptPassword(password) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("decrypt", json);
        }

        [Fact]
        public void DecryptPassword_ShouldReturnEmptyDecrypt_WhenPasswordIsEmpty()
        {
            // Arrange
            var password = "";

            // Act
            var result = _controller.DecryptPassword(password) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"decrypt\":\"\"", json);
        }

        [Fact]
        public void DecryptPassword_ShouldReturnEmptyDecrypt_WhenPasswordIsNull()
        {
            // Arrange
            string password = null; // This will return empty decrypt

            // Act
            var result = _controller.DecryptPassword(password) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"decrypt\":\"\"", json);
        }

        [Fact]
        public async Task GetPagination_ShouldReturnJson_WhenSuccessful()
        {
            // Arrange
            var query = new GetUserPaginatedListQuery();
            var paginatedResult = new PaginatedResult<UserViewListVm>();
            _mockDataProvider.Setup(dp => dp.User.GetUserPaginatedList(query))
                .Returns(Task.FromResult(paginatedResult));

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(paginatedResult, result.Value);
        }

        [Fact]
        public async Task GetPagination_ShouldReturnEmptyJson_WhenExceptionOccurs()
        {
            // Arrange
            var query = new GetUserPaginatedListQuery();
            _mockDataProvider.Setup(dp => dp.User.GetUserPaginatedList(query))
                .ThrowsAsync(new Exception("Pagination error"));

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("", result.Value);
        }

        [Fact]
        public async Task ResetPassword_ShouldReturnRedirectToList_WhenExceptionOccurs()
        {
            // Arrange
            var resetUser = new UserViewModel
            {
                userResetViewModal = new UserResetViewModal { LoginName = "testuser" }
            };
            _mockMapper.Setup(m => m.Map<ResetPasswordCommand>(resetUser.userResetViewModal))
                .Throws(new Exception("Reset error"));

            // Act
            var result = await _controller.ResetPassword(resetUser) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task Unlock_ShouldReturnRedirectToList_WhenExceptionOccurs()
        {
            // Arrange
            var resetUser = new UserViewModel
            {
                UserUnLockModel = new UserUnLockModel { UserId = "1", IsLock = false }
            };
            _mockMapper.Setup(m => m.Map<UserUnLockCommand>(resetUser.UserUnLockModel))
                .Throws(new Exception("Unlock error"));

            // Act
            var result = await _controller.Unlock(resetUser) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task GetCompanyById_ShouldReturnEmptyJson_WhenExceptionOccurs()
        {
            // Arrange
            var id = "1";
            _mockDataProvider.Setup(dp => dp.Company.GetCompanyById(id))
                .ThrowsAsync(new Exception("Company error"));

            // Act
            var result = await _controller.GetCompanyById(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("", result.Value);
        }

        [Fact]
        public async Task GetUserRoleList_ShouldReturnSuccess_WhenSuccessful()
        {
            // Arrange
            var userRoles = new List<UserRoleListVm>();
            _mockDataProvider.Setup(dp => dp.UserRole.GetUserRoles())
                .Returns(Task.FromResult(userRoles));

            // Act
            var result = await _controller.GetUserRoleList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task GetUserRoleList_ShouldReturnJsonException_WhenExceptionOccurs()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.UserRole.GetUserRoles())
                .ThrowsAsync(new Exception("Role error"));

            // Act
            var result = await _controller.GetUserRoleList() as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task IsNewPasswordInLastFive_ShouldReturnTrue_WhenPasswordIsNew()
        {
            // Arrange
            var userId = "1";
            var newPassword = "newpassword";
            _mockDataProvider.Setup(dp => dp.User.IsNewPasswordInLastFive(userId, newPassword))
                .Returns(Task.FromResult(true));

            // Act
            var result = await _controller.IsNewPasswordInLastFive(userId, newPassword);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task IsNewPasswordInLastFive_ShouldReturnFalse_WhenExceptionOccurs()
        {
            // Arrange
            var userId = "1";
            var newPassword = "newpassword";
            _mockDataProvider.Setup(dp => dp.User.IsNewPasswordInLastFive(userId, newPassword))
                .ThrowsAsync(new Exception("Password check error"));

            // Act
            var result = await _controller.IsNewPasswordInLastFive(userId, newPassword);

            // Assert
            Assert.False(result);
        }

        // ===== ATTRIBUTE TESTING =====

        [Fact]
        public void Controller_ShouldHaveAreaAttribute()
        {
            // Arrange
            var controllerType = typeof(UserController);

            // Act
            var areaAttribute = controllerType.GetCustomAttribute<AreaAttribute>();

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void CreateOrUpdate_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("CreateOrUpdate");

            // Act
            var httpPostAttribute = method.GetCustomAttribute<HttpPostAttribute>();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();
            var antiXssAttribute = method.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void Delete_ShouldHaveAuthorizeAttribute()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("Delete");

            // Act
            var authorizeAttribute = method.GetCustomAttribute<AuthorizeAttribute>();

            // Assert
            Assert.NotNull(authorizeAttribute);
        }

        [Fact]
        public void IsLoginNameExist_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("IsLoginNameExist");

            // Act
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void GetDomains_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("GetDomains");

            // Act
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void GetDomainUsers_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("GetDomainUsers");

            // Act
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void GetDomainGroups_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("GetDomainGroups");

            // Act
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void GetAllInfraObjectList_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("GetAllInfraObjectList");

            // Act
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void LockLogin_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("LockLogin");

            // Act
            var httpPostAttribute = method.GetCustomAttribute<HttpPostAttribute>();
            var allowAnonymousAttribute = method.GetCustomAttribute<AllowAnonymousAttribute>();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(allowAnonymousAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
        }

        [Fact]
        public void UserActivityProfile_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("UserActivityProfile");

            // Act
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void GetUserLoginName_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("GetUserLoginName");

            // Act
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();
            var allowAnonymousAttribute = method.GetCustomAttribute<AllowAnonymousAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
            Assert.NotNull(allowAnonymousAttribute);
        }

        [Fact]
        public void UserProfiles_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("UserProfiles");

            // Act
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void UpdateUserProfileImage_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("UpdateUserProfileImage");

            // Act
            var httpPostAttribute = method.GetCustomAttribute<HttpPostAttribute>();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();
            var antiXssAttribute = method.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void UserProfileDelete_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("UserProfileDelete");

            // Act
            var validateAntiForgeryTokenAttribute = method.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();
            var antiXssAttribute = method.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void ChangePassword_Post_ShouldHaveValidateAntiForgeryTokenAttribute()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("ChangePassword", new[] { typeof(ChangePasswordViewModel) });

            // Act
            var httpPostAttribute = method.GetCustomAttribute<HttpPostAttribute>();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
        }

        [Fact]
        public void HashPassword_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("HashPassword");

            // Act
            var allowAnonymousAttribute = method.GetCustomAttribute<AllowAnonymousAttribute>();
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(allowAnonymousAttribute);
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void HashPasswordForEmail_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("HashPasswordForEmail");

            // Act
            var allowAnonymousAttribute = method.GetCustomAttribute<AllowAnonymousAttribute>();
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(allowAnonymousAttribute);
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void DecryptPassword_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("DecryptPassword");

            // Act
            var allowAnonymousAttribute = method.GetCustomAttribute<AllowAnonymousAttribute>();
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(allowAnonymousAttribute);
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void GetPagination_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("GetPagination");

            // Act
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void ResetPassword_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("ResetPassword");

            // Act
            var httpPostAttribute = method.GetCustomAttribute<HttpPostAttribute>();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
        }

        [Fact]
        public void Unlock_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("Unlock");

            // Act
            var httpPostAttribute = method.GetCustomAttribute<HttpPostAttribute>();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
        }

        [Fact]
        public void GetCompanyById_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("GetCompanyById");

            // Act
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void GetUserRoleList_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(UserController).GetMethod("GetUserRoleList");

            // Act
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }
    }
}
