﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Site.Events.Create;

public class SiteCreatedEventHandler : INotificationHandler<SiteCreatedEvent>
{
    private readonly ILogger<SiteCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public SiteCreatedEventHandler(ILoggedInUserService userService, ILogger<SiteCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(SiteCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            CompanyId = _userService.CompanyId,
            Entity = Modules.Site.ToString(),
            Action = $"{ActivityType.Create} {Modules.Site}",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $" Site '{createdEvent.SiteName}' created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Site '{createdEvent.SiteName}' created successfully.");
    }
}