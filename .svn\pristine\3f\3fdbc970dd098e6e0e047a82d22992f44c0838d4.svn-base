﻿using ContinuityPatrol.Application.Features.Setting.Commands.Create;
using ContinuityPatrol.Application.Features.Setting.Commands.Update;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoSettingDataAttribute : AutoDataAttribute
{
    public AutoSettingDataAttribute()
        : base(() =>
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(
                new StringPropertyTruncateSpecimenBuilder<CreateSettingCommand>(p => p.<PERSON><PERSON>, 10));
            fixture.Customize<CreateSettingCommand>(c => c.With(b => b.<PERSON><PERSON>ser<PERSON>d, 0.ToString));

            fixture.Customizations.Add(
                new StringPropertyTruncateSpecimenBuilder<UpdateSettingCommand>(p => p.SValue, 10));
            fixture.Customize<UpdateSettingCommand>(c => c.With(b => b.Id, 0.ToString));
            fixture.Customize<Setting>(c => c.With(b => b.IsActive, true));

            return fixture;
        })
    {

    }
}