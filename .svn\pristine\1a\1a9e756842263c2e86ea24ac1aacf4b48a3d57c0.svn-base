namespace ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Update;

public class UpdateAdPasswordJobCommand : IRequest<UpdateAdPasswordJobResponse>
{
    public string Id { get; set; }
    public string DomainServerId { get; set; }
    public string DomainServer { get; set; }
    public string State { get; set; }
	public int IsSchedule { get; set; }
	public int ScheduleType { get; set; }
	public string CronExpression { get; set; }
	public string ScheduleTime { get; set; }
	public string NodeId { get; set; }
	public string NodeName { get; set; }
	public string ExceptionMessage { get; set; }
	
}
