﻿namespace ContinuityPatrol.Shared.Core.Responses;

public class AuthenticationServiceResponse
{
    public List<string> Permissions = new();
    public bool IsAuthorized { get; set; }
    public string LoginName { get; set; }
    public string CompanyId { get; set; }
    public string CompanyName { get; set; }
    public string Role { get; set; }
    public string RoleName { get; set; }
    public bool IsAllInfra { get; set; }
    public string AssignedInfras { get; set; }
    public string UserId { get; set; }
    public bool IsParent { get; set; }
    public int SessionTimeout { get; set; }
    public string TwoFactorAuthentication { get; set; }
    public bool IsReset { get; set; }
    public string AuthenticationType { get; set; }
    public bool IsLicenseValidity { get; set; }
    public bool LicenseEmpty { get; set; }
    public string ParentCompanyId { get; set; }
    public bool IsDefaultDashboard { get; set; }
    public string Url { get; set; }
    public DateTime? LastPasswordChanged { get; set; }
}