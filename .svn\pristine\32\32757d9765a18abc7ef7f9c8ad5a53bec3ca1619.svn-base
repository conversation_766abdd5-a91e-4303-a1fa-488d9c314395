﻿using ContinuityPatrol.Application.Features.CGExecutionReport.Events.UpdateConditionAction;

namespace ContinuityPatrol.Application.Features.CGExecutionReport.Commands.UpdateConditionActionId;

public class ResiliencyReadinessWorkflowScheduleLogCommandHandler : IRequestHandler<ResiliencyReadinessWorkflowScheduleLogCommand, ResiliencyReadyWorkflowScheduleLogResponse>
{
    private readonly IResiliencyReadyWorkflowScheduleLogRepository _workflowScheduleLogRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public ResiliencyReadinessWorkflowScheduleLogCommandHandler(IResiliencyReadyWorkflowScheduleLogRepository workflowScheduleLogRepository, IMapper mapper, IPublisher publisher)
    {
        _workflowScheduleLogRepository = workflowScheduleLogRepository;
        _mapper = mapper;
        _publisher = publisher;
    }
    public async Task<ResiliencyReadyWorkflowScheduleLogResponse> Handle(ResiliencyReadinessWorkflowScheduleLogCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _workflowScheduleLogRepository.GetByReferenceIdAsync(request.WorkflowOperationId);
       
        if (eventToUpdate == null) throw new NotFoundException("CG Execution Report", request.WorkflowOperationId);

       // _mapper.Map(request, eventToUpdate, typeof(ResiliencyReadinessWorkflowScheduleLogCommand), typeof(ResiliencyReadyWorkflowScheduleLog));
       
        eventToUpdate.ConditionActionId = request.ConditionActionId;
        
        await _workflowScheduleLogRepository.UpdateAsync(eventToUpdate);

        var response = new ResiliencyReadyWorkflowScheduleLogResponse
        {
            Message = Message.Update("CG Execution Report", eventToUpdate.WorkflowName),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new CGExecutionConditionActionIdUpdatedEvent { WorkflowName = eventToUpdate.WorkflowName }, cancellationToken);

        return response;

    }
}
