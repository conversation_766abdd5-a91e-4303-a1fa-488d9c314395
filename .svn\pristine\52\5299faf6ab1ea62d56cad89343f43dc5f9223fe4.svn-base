//let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'
//let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">'
//let asmNoDataimg = '<img src="/img/isomatric/nodatalag.svg" class="mx-auto"> <br><span class="text-danger">No data available</span>'
//let noData = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
//let noPluggable = '<div class="NoData text-center p-2 d-grid justify-content-center">' +
//    '<img src="/img/isomatric/nodatalag.svg" class="mx-auto" />' +
//    '<span class="text-danger">' +
//    'Pluggable Databases not configured.' +
//    '</span>' +
//    '</div>';

PageBuilderData()




//$.ajax({
//    url: '@Url.Action("List", "CustomDashboard")', // Update with your controller name
//    type: 'GET',
//    success: function (result) {
//        debugger
//        $('#subDashboardContainer').html(result);
//    },
//    error: function (xhr, status, error) {
//        debugger
//        alert("An error occurred: " + status + " " + error);
//    }
//});

$("#customDashboard").val()
function PageBuilderData() {
    let PageId;
    if ($("#customDashboard").attr("data-dashboard")) {
         PageId = $("#customDashboard").attr("data-dashboard")
    }
    else {
        let params = new URLSearchParams(location.search);
         PageId = params.get('dashboardSubId')
    }

    $.ajax({
        type: "GET",
        url: RootUrl + 'Dashboard/CustomDashboard/DynamicDashboardList',
        dataType: "json",
        data: { dashboardSubId: PageId },
        traditional: true,
        success: function (result) {
            
            $("#ITViewBuilderList").empty()
            $("#monitoringTitle").text(result.name)
            let propertices = JSON.parse(result.properties)
            $("#ITViewBuilderList").append(propertices.pageDetails)

            $(".pageBuilderSetDesign").removeClass("border-dashed")
            $(".iconcollection").remove()
            $(".tablerowcolumn").removeAttr("contenteditable")
            
            let infraId = $(".pageBuilderInfraId").attr("infraobject")
            let datainfra = {}
            datainfra.infraId = infraId

            debugger
            $(".pageDiagram").each(function () {


                let datasetId = this.getAttribute("datasetId")
                let datasetName = this.getAttribute("datasetName")
                let datasetQuery = this.getAttribute("datasetQuery")
                let datasetType = this.getAttribute("datasetType")
                let chartType = this.getAttribute("chartType")
                let xAxisValue = this.getAttribute("xView")
                let yAxisValue = this.getAttribute("yview")
                let title = this.getAttribute("title")
                let id = this.getAttribute("id")
                if (datasetQuery) {
                    DatasetDetails(datasetId, datasetName, datasetQuery, datasetType, chartType, xAxisValue, yAxisValue, id, title)


                }

            })
           

            //$.ajax({
            //    type: "GET",
            //    url: RootUrl + 'Monitor/ITViewBuilder/GetDashboardViewListByInfraObjectId',
            //    dataType: "json",
            //    data: datainfra,
            //    traditional: true,
            //    success: function (result) {


            //        if (result.success) {
            //            let data = {}
            //            data.monitorId = result.data.entityId
            //            data.type = result.data.monitorType;
            //            $.ajax({
            //                type: "GET",
            //                url: RootUrl + 'Monitor/Monitoring/GetMonitorServiceStatusByIdAndType',
            //                dataType: "json",
            //                data: data,
            //                traditional: true,
            //                success: function (result) {
            //                    if (result.success) {
            //                        $("#infraName").text(result.data.infraObjectName)
            //                        $("#modifiedTime").text(result.data.rpoGeneratedDate)
            //                        let properties = JSON.parse(result.data.properties)
            //                        let infraobjectId = $(".solutionDiagramData").attr("infraobject")

            //                        debugger

            //                        if (infraobjectId) {
            //                            monitoringSolution(infraobjectId, data.type)
            //                            msSQLNLSServer(infraobjectId)
            //                        }
            //                        for (var key in properties) {
            //                            if (properties.hasOwnProperty(key))

            //                                if (typeof (properties[key]) != "object") {
            //                                    $("." + key).empty()

            //                                    if (properties[key] == null || properties[key] == undefined || properties[key] == "") {
            //                                        $("." + key).text("NA")
            //                                        $("." + key).prev().addClass("text-danger")
            //                                    }
            //                                    else {
            //                                        $("." + key).prev().removeClass("text-danger")
            //                                        $("." + key).text(properties[key])
            //                                        let iconClass = getIconClass(properties[key])
            //                                        if (iconClass != "") {
            //                                            $("." + key).prev().addClass(iconClass)
            //                                        }

            //                                    }
            //                                }
            //                                else {
            //                                    let subPropertics = properties[key]
            //                                    for (let subpropertieskey in subPropertics) {
            //                                        $("." + subpropertieskey).empty()

            //                                        if (subPropertics[subpropertieskey] == null || subPropertics[subpropertieskey] == undefined || subPropertics[subpropertieskey] == "") {
            //                                            $("." + subpropertieskey).text("NA")
            //                                            $("." + subpropertieskey).prev().addClass("text-danger")
            //                                        }
            //                                        else {
            //                                            $("." + subpropertieskey).text(subPropertics[subpropertieskey])
            //                                            $("." + subpropertieskey).prev().removeClass("text-danger")
            //                                            let iconClass = getIconClass(subPropertics[subpropertieskey])
            //                                            if (iconClass != "") {
            //                                                $("." + subpropertieskey).prev().addClass(iconClass)
            //                                            }
            //                                        }

            //                                    }

            //                                }

            //                        }

            //                        //let pluggableData = [];

            //                        //if (properties?.PR_CDB?.toLowerCase() === "yes" && properties?.DR_CDB?.toLowerCase() === "yes") {

            //                        //    pluggableData = [
            //                        //        "PR_PDB_Name", "PR_CONNECTION_ID", "PR_PDB_ID", "PR_PDB_MODE", "PR_LOGGING", "PR_FORCE_LOGGING",
            //                        //        "PR_RECOVERY_STATUS", "PR_PDB_SIZE",
            //                        //        "DR_PDB_Name", "DR_CONNECTION_ID", "DR_PDB_ID", "DR_PDB_MODE", "DR_LOGGING", "DR_FORCE_LOGGING",
            //                        //        "DR_RECOVERY_STATUS", "DR_PDB_SIZE"
            //                        //    ];
            //                        //    setPropData(properties, pluggableData)

            //                        //} else {
            //                        //    $("#noPluggableimg").css('text-align', 'center').html(noPluggable);

            //                        //}
            //                        //if (properties?.PR_Asm_Details != undefined) {
            //                        //    displayASM(properties?.PR_Asm_Details, '#prASMData', '#asmPrimaryData');
            //                        //    displayASM(properties?.DR_Asm_Details, '#drASMData', '#asmDRData');
            //                        //}

            //                    }
            //                }


            //            })
            //        }
            //    }


            //})



        }
    })


}

function setPropData(data, propSets) {
    propSets.forEach(properties => {
        bindProperties(data, properties);
    });
}
function DatasetDetails(datasetId, datasetName, datasetQuery, datasetType, chartType, xAxisValue, yAxisValue, id, title) {
    debugger
     $.ajax({
        type: "GET",
        url: RootUrl + 'Manage/TileConfiguration/DatasetDetails',
        dataType: "json",
         data: { data: datasetQuery },
         success: async function (result) {

             if (chartType == "pie" || chartType == "bar") {

              
                let x = []
                 let y = []
                 let propertice = JSON.parse(result.message.tableValue)
                 if (propertice) {
                     propertice.forEach((data) => {
                        x.push(data[xAxisValue])
                        y.push(data[yAxisValue])

                    })
                    let xy = {
                        x: x,
                        y: y
                    }
                     await ChartFunction(chartType, xy, "#" + id, title)
                }
                
            }
             else if (datasetType == "customTable") {
                debugger

                let indexNo = 1;
                $("#widgettable").empty();

                 let propertice = JSON.parse(result.message.tableValue)
                debugger
                 propertice.forEach((data, index) => {

                    let Html = ""
                    Html += '<tr class="dragtr">'
                    for (let tabledata in data) {
                        $("#tableCreation thead th").each(function (column) {
                            if (this.textContent == tabledata) {
                                Html += '<td class="text-truncate fw-semibold"><span class="d-flex align-items-center"><span contenteditable="true" class="tablerowcolumn" tableclass="tablerowcolumn" id="row_1">' + data[tabledata] + '</span></span><div class="collapse mt-2" id="IconsCollapse"><div class="d-flex flex-wrap align-items-center"><div class="border p-2" role="button"><i class="cp-server"></i></div><div class="border p-2" role="button"><i class="cp-database"></i></div><div class="border p-2" role="button"><i class="cp-cloud"></i></div><div class="border p-2" role="button"><i class="cp-server-role"></i></div><div class="border p-2" role="button"><i class="cp-dataguard-status"></i></div><div class="border p-2" role="button"><i class="cp-virtualization"></i></div><div class="border p-2" role="button"><i class="cp-business-function"></i></div><div class="border p-2" role="button"><i class="cp-business-service"></i></div><div class="border p-2" role="button"><i class="cp-database-success"></i></div></div></div></td>'
                                // Html += '<td class="text-truncate fw-semibold " ><i class="cp-images me-1 cpimages_310827" icon="cp-images" color="select" name="cpimages_310827" onclick="iconBondle(this)"></i><span tableclass="tablerowcolumn" id="rowcolum_310827">' + data.BusinessServiceName + '</span></td > <td class="text-truncate fw-semibold "><i class="cp-images me-1 cpimages_407571" icon="cp-images" color="select" name="cpimages_407571" onclick="iconBondle(this)"></i><span tableclass="tablerowcolumn" id="rowcolum_407571">' + data.BusinessFunctionName + '</span></td>'
                                // Html += '<td class="text-truncate fw-semibold " ><i class="cp-images me-1 cpimages_310827" icon="cp-images" color="select" name="cpimages_310827" onclick="iconBondle(this)"></i><span tableclass="tablerowcolumn" id="rowcolum_310827">' + data.Name + '</span></td > <td class="text-truncate fw-semibold "><i class="cp-images me-1 cpimages_407571" icon="cp-images" color="select" name="cpimages_407571" onclick="iconBondle(this)"></i><span tableclass="tablerowcolumn" id="rowcolum_407571">' + data.State + '</span></td>'
                            }
                        })
                    }
                    Html += '</tr>'
                    indexNo = indexNo + 1
                    $("#widgettable").append(Html)

                })


            }
           
        }
     })
  
    
}

function bindProperties(data, properties) {
    properties.forEach(property => {
        const value = data[property];
        const displayedValue = (value !== undefined || value !== '') ? checkAndReplace(value) : 'NA';

        let iconClass = getIconClass(displayedValue);

        // Displayed value with icon
        if (property !== 'PR_InstanceStartUpTime' && property !== 'DR_InstanceStartUpTime') {
            const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
            const mergeValue = `${iconHtml}${displayedValue}`;
            $(`#${property}`).html(mergeValue).attr('title', displayedValue);
        }
        if (property === 'PR_InstanceStartUpTime' || property === 'DR_InstanceStartUpTime') {
            if (displayedValue !== "NA") {
                const [date, time] = displayedValue.split(' ');
                const formattedValue = `${date}<br>${time}`;
                const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
                const mergeValue = `${iconHtml}${formattedValue}`;
                $(`#${property}`).html(mergeValue).attr('title', `${date} ${time}`);
            }
            else {
                const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
                const mergeValue = `${iconHtml}${displayedValue}`;
                $(`#${property}`).html(mergeValue).attr('title', displayedValue);
            }

        }
        //if (property === 'PR_Recovery_Status' || property === 'DR_Recovery_Status') {
        //    if (displayedValue !== "NA") {
        //        const part = displayedValue?.split('\t');
        //        const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
        //        const mergeValue = `${iconHtml}${part[1]}`;
        //        $(`#${property}`).html(mergeValue).attr('title', part[1]);
        //    }
        //}
    });
}



async function msSQLNLSServer(id) {

    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
    let data = {}
    data.infraObjectId = id;
    let mssqlServerData = await getAysncWithHandler(url, data);

    if (mssqlServerData != null && mssqlServerData?.length > 0) {
        $('#mssqlserver').show();
        $('#mssqlserverbody').empty()
        bindMSSQLServer(mssqlServerData)
    } else {
        $('#mssqlserver').hide();
    }

}

function bindMSSQLServer(mssqlServerData) {
    debugger
    const rowsValue = mssqlServerData?.map((list, i) => {
        const serverName = checkAndReplace(list?.servicePath === null ? list.workflowName : list.workflowName === null ? list.servicePath : "NA");
        const ipAddress = checkAndReplace(list?.ipAddress);
        const status = checkAndReplace(list?.isServiceUpdate);
        const iconServer = serverName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";
        const iconIp = ipAddress === "NA" ? "text-danger cp-disable" : "text-secondary cp-ip-address";
        const iconStatus = status?.toLowerCase() === "running" ? "text-success cp-reload cp-animate" : "error" ? "text-danger cp-fail-back" : "text-danger cp-disable";

        return `<tr><td><i class="${iconServer} me-1 fs-6"></i><span>${serverName}</span></td><td><i class="${iconIp} me-1 fs-6"></i>${ipAddress}</td><td><i class="${iconStatus} me-1 fs-6"></i>${status}</td></tr>`;
    }).join('');

    $('#mssqlserverbody').append(rowsValue);

}

function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}


function getIconClass(displayedValue) {

    switch (displayedValue.toLowerCase()) {
        case 'na':
        case 'no':
        case 'not allowed':
        case 'disabled':
        case 'disable': return 'text-danger cp-disable';
        case 'manual': return 'text-warning cp-settings';
        case 'healthy': return 'text-success cp-health-success';
        case 'nothealthy':
        case 'not_healthy':
        case 'unhealthy': return 'text-danger cp-health-error';
        case 'online': return 'text-success cp-online';
        case 'offline': return 'text-danger cp-offline';
        case 'primary': return 'text-primary cp-list-prsite';
        case 'secondary': return 'text-info cp-dr';
        case 'physical standby': return 'text-info cp-physical-drsite';
        case 'connected':
        case 'connect': return 'text-success cp-connected';
        case 'disconnected':
        case 'disconnect': return 'text-danger cp-disconnected';
        case 'synchronous_commit':
        case 'synchronized':
        case 'synchronizing':
        case 'sync': return 'text-success cp-refresh';
        case 'asynchronous_commit':
        case 'notsynchronizing ':
        case 'notsynchronized':
        case 'not':
        case 'not synchronized':
        case 'not synchronizing':
        case 'asynchronizing':
        case 'asynchronized':
        case 'async': return 'text-danger cp-refresh';
        case 'pending': return 'text-warning cp-pending';
        case 'running':
        case 'run': return 'text-success cp-reload cp-animate';
        case 'error': return 'text-danger cp-fail-back';
        case 'stopped':
        case 'stop': return 'text-danger cp-Stopped';
        case 'standby':
        case 'to standby':
        case 'mounted': return 'text-warning cp-control-file-type';
        case 'enabled':
        case 'enable': return 'text-success cp-end';
        case 'true':
        case 'yes':
        case 'valid': return 'text-success cp-success';
        case 'false':
        case 'defer':
        case 'deferred': return 'text-danger cp-error';
        case 'pause':
        case 'paused': return 'text-warning cp-circle-pause';
        case 'required':
        case 'require': return 'text-warning cp-warning';
        case 'on': return 'text-success cp-end';
        case 'off': return 'text-danger cp-end';
        case 'current':
        case 'read write':
            return 'text-success cp-file-edits';
        case displayedValue.includes('running'):

            if (displayedValue.includes('running')) {
                return iconClass = 'text-success cp-reload cp-animate';
            } else if (displayedValue.includes('production') || displayedValue.includes('archive recovery')) {
                return iconClass = 'text-warning cp-log-archive-config';
            }
        default:
            return '';
    }
}


function displayASM(val, target, element) {
    try {
        if (!val) {
            $(element)
                .css('text-align', 'center')
                .html(asmNoDataimg);
        } else {
            let data = val.replace(/,(?=\s*[\]}])/, '');
            let asmVal = JSON?.parse(data);
            if (asmVal?.length > 0) {
                const asmRows = asmVal.map((list, i) => `<tr><td>${i + 1}</td><td>${list.NAME}</td><td>${list.STATE}</td><td>${list.TYPE}</td><td>${list.TOTAL_MB}</td><td>${list.FREE_MB}</td><td>${list['USED(%)']}</td></tr>`).join('');
                $(target).append(asmRows);
            }
            else {
                $(element)
                    .css('text-align', 'center')
                    .html(asmNoDataimg);
            }
        }
    } catch (error) {
        $(element)
            .css('text-align', 'center')
            .html(asmNoDataimg);
        //notificationAlert("warning", "Invalid JSON Format");
        //setTimeout(() => {
        //    window.location.assign('/Dashboard/ITResiliencyView/List');
        //}, 3000)
    }
}


