﻿using ContinuityPatrol.Domain.ViewModels.IncidentModel;

namespace ContinuityPatrol.Application.Features.Incident.Queries.GetList;

public class GetIncidentListQueryHandler : IRequestHandler<GetIncidentListQuery, List<IncidentListVm>>
{
    private readonly IIncidentRepository _incidentRepository;
    private readonly IMapper _mapper;

    public GetIncidentListQueryHandler(IIncidentRepository incidentRepository, IMapper mapper)
    {
        _mapper = mapper;
        _incidentRepository = incidentRepository;
    }

    public async Task<List<IncidentListVm>> Handle(GetIncidentListQuery request, CancellationToken cancellationToken)
    {
        var list = await _incidentRepository.ListAllAsync();

        return _mapper.Map<List<IncidentListVm>>(list);
    }
}