﻿using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;

namespace ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetWorkflowprofileByInfraobjectId;

public class GetWorkflowprofileByInfraobjectIdQueryHandler : IRequestHandler<GetWorkflowprofileByInfraobjectIdQuery,
    List<WorkflowProfileInfoNameVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;

    public GetWorkflowprofileByInfraobjectIdQueryHandler(
        IWorkflowProfileInfoRepository workflowProfileInfoRepository, IMapper mapper)
    {
        _workflowProfileInfoRepository = workflowProfileInfoRepository;
        _mapper = mapper;
    }

    public async Task<List<WorkflowProfileInfoNameVm>> Handle(GetWorkflowprofileByInfraobjectIdQuery request,
        CancellationToken cancellationToken)
    {
        var workflowProfile = await _workflowProfileInfoRepository.GetWorkflowProfileByInfraId(request.infraobjectId);

        var workflowProfileInfoName = _mapper.Map<List<WorkflowProfileInfoNameVm>>(workflowProfile);


        return workflowProfileInfoName;
    }
}