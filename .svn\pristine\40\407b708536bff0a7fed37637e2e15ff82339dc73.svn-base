﻿async function getGroupByDomain(value, value2) {

    var url = RootUrl + userManageURL.domainGroups;
    var data = {};
    data.domainName = value;
    data.domainUserName = value2
    var result = await GetAsync(url, data, OnError);
    $('#userDomainGroup').empty();
    $('#userDomainGroup').append('<option value="">Select Group</option>');
    if (result.length > 0) {
        for (var index = 0; index <= result.length; index++) {
            if (result[index] != undefined) {
                $('#userDomainGroup').append('<option value="' + result[index] + '">' + result[index] + '</option>');
            }

        }
    }

}
function roledata(data, isParent) {
    const $roleDropdown = $('#userRoleName');
    const isSiteAdmin = userRoleValue.toLowerCase() === 'siteadmin';
    const isSuperAdmin = userRoleValue.toLowerCase() === 'superadmin';
    const userRoleName = userData?.roleName?.toLowerCase();
    const selectedRole = userData?.role;

    $roleDropdown.empty().val(null);
    $roleDropdown.append($('<option>', { value: '', text: 'Select Role' }));

    const shouldInclude = (item) => {
        if (isSuperAdmin) {
            return isParent || item.role !== 'SuperAdmin';
        }
        return true; // default: allow all
    };

    $.each(data, (index, item) => {
        if (shouldInclude(item)) {
            $roleDropdown.append($('<option>', {
                "data-roleId": item?.id,
                value: item?.id,
                text: item?.role
            }));
        }
    });

    if (Object.keys(userData).length) {
        if (isSiteAdmin && userRoleName === 'siteadmin') {
            $roleDropdown.append($('<option>', { value: 'siteadmin', text: 'SiteAdmin' }));
            $roleDropdown.prop('disabled', true).val('siteadmin').trigger('change');
        } else {
            $roleDropdown.prop('disabled', false).val(selectedRole).trigger('change');
        }
    } else {
        $roleDropdown.prop('disabled', false);
    }
}
function SelectAllTreeViewExpended(open) {
    $('#treeview details').attr('open', open);
}
function infraObjectData(selectedCount, bf) {
    const total = bf.assignedInfraObjects.length;
    bf.isAll = selectedCount === total;
    bf.isPartial = selectedCount > 0 && selectedCount < total;
}
function businessFunctionCondition(selectedFCount, selectedPartialCount, d) {
    const total = d.assignedBusinessFunctions.length;
    if (selectedFCount === total && selectedPartialCount === 0) {
        d.isAll = true;
        d.isPartial = false;
    } else if (selectedFCount === 0 && selectedPartialCount === 0) {
        d.isAll = false;
        d.isPartial = false;
    } else {
        d.isAll = false;
        d.isPartial = true;
    }
}
function SelectAllTreeView(check) {
    $('#userWorkflowAll').prop('checked', check);

    if (Array.isArray(jsonData.assignedBusinessServices) && jsonData.assignedBusinessServices.length > 0) {
        jsonData.assignedBusinessServices.forEach(service => {
            service.isAll = check;
            service.isPartial = check;

            service.assignedBusinessFunctions?.forEach(func => {
                func.isAll = check;
                func.isPartial = check;

                func.assignedInfraObjects?.forEach(infra => {
                    infra.isSelected = check;
                });
            });
        });

        jsonData.isAll = check;

        $('#userProperties').val(JSON.stringify(jsonData));
        $('#treeview').empty();
        createTreeView($('#treeview'), jsonData);

    }
}
function areAllValuesFalse(data) {
    if (userRoleValue === 'SiteAdmin') return true;

    const checkData = JSON.parse(data);
    const services = checkData?.assignedBusinessServices;

    // Skip detailed check if `isAll` is true
    if (checkData.isAll === true || !Array.isArray(services) || services.length === 0) {
        $('#userProperties').val(data);
        return true;
    }

    const hasSelected = services.some(service =>
        service.assignedBusinessFunctions?.some(func =>
            func.assignedInfraObjects?.some(infra => infra.isSelected === true)
        )
    );

    $('#userProperties').val(data);
    return hasSelected ? true : false;
}
function updateTreeView(userData) {
    const $treeview = $("#treeview");
    $treeview.empty();

    const userInfraObject = userData?.properties && JSON.parse(userData.properties);
    jsonData = userInfraObject;

    if (userRoleValue !== 'SiteAdmin') {
        createTreeView($treeview, userInfraObject);
    }

    const isAllChecked = userInfraObject?.isAll === true;
    $("#userWorkflowAll").prop('checked', isAllChecked);

    // Ensure 'Select All' reflects partial/unselected states
    const hasUnselected = jsonData?.assignedBusinessServices?.some(service => !service.isAll && !service.isPartial);
    if (hasUnselected) {
        $("#userWorkflowAll").prop('checked', false);
    }
}
function populateTreeView(jsonData) {
    const $container = $("#treeview").empty();
    const $treeElement = document.querySelector('.Workflow-Tree');
    const existingNoDataNode = document.querySelector('.new-node');

    if (existingNoDataNode) {
        existingNoDataNode.remove();
    }

    if (jsonData?.assignedBusinessServices?.length > 0) {
        $("#userWorkflowAll").css("background-color", "");
        createTreeView($container, jsonData);
        $treeElement?.classList.remove("d-none");
    } else {
        $treeElement?.classList.add("d-none");

        const noDataHTML = `
            <div class="new-node">
                <li class="list-group-item text-center" id="noDataFoundMessage">
                    <img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">
                </li>
            </div>`;

        $treeElement?.insertAdjacentHTML('afterend', noDataHTML);
    }
}
function createTreeView(container, userInfraObject, serviceId = '') {
    const $container = $("#treeview").empty();

    // Sync selection states from `userInfraObject` to `JSONDataForClickEditButton`
    if (JSONDataForClickEditButton?.assignedBusinessServices?.length > 0) {
        JSONDataForClickEditButton.assignedBusinessServices.forEach(serviceRef => {
            userInfraObject?.assignedBusinessServices?.forEach(serviceLive => {
                if (serviceRef.id === serviceLive.id) {
                    serviceRef.isAll = serviceLive.isAll;
                    serviceRef.isPartial = serviceLive.isPartial;

                    serviceRef.assignedBusinessFunctions?.forEach(funcRef => {
                        serviceLive.assignedBusinessFunctions?.forEach(funcLive => {
                            if (funcRef.id === funcLive.id) {
                                funcRef.isAll = funcLive.isAll;
                                funcRef.isPartial = funcLive.isPartial;

                                funcRef.assignedInfraObjects?.forEach(infraRef => {
                                    funcLive.assignedInfraObjects?.forEach(infraLive => {
                                        if (infraRef.id === infraLive.id) {
                                            infraRef.isSelected = infraLive.isSelected;
                                        }
                                    });
                                });
                            }
                        });
                    });
                }
            });
        });

        // Reassign updated data
        userInfraObject = JSONDataForClickEditButton;
        jsonData = JSONDataForClickEditButton;
    }

    // Set 'Select All' checkbox state
    $("#userWorkflowAll").prop('checked', !!userInfraObject?.isAll);

    // Render Tree
    if (userInfraObject?.assignedBusinessServices?.length > 0) {
        userInfraObject.assignedBusinessServices.forEach(service => {
            const $details = $(`<details id="${service.id}" ${serviceId === service.id ? 'open' : ''}></details>`);
            const $summary = $('<summary></summary>');
            const $checkbox = $(`<input type="checkbox" class="form-check-input selecttree" onchange="selectTree(this)">`);

            $checkbox.attr('businessId', service.id);
            $checkbox.prop('checked', service.isAll);

            // Add indeterminate state styling if partially selected
            if (!service.isAll && service.isPartial) {
                $checkbox.css('background-color', 'grey');
            }

            $summary.append($checkbox).append(` ${service.name}`);
            $details.append($summary);

            if (service.assignedBusinessFunctions?.length > 0) {
                const $functionList = $('<ul class="tree"></ul>');
                createFunctions($functionList, service.assignedBusinessFunctions, service.id);
                $details.append($functionList);
            }

            $container.append($details);
        });
    }
}
function selectTree(checkbox) {
    let check = checkbox.checked;
    var businessId = $(checkbox).attr("businessid");
    var functionid = $(checkbox).attr("functionid");
    var infraid = $(checkbox).attr("infraid");
    JsonTreeView(check, businessId, functionid, infraid);
}
function createFunctions(container, functions, businessId) {
    functions.forEach(func => {
        const $details = $('<details open></details>'); // Expanded by default
        const $summary = $('<summary></summary>');
        const $checkbox = $('<input>', {
            type: 'checkbox',
            class: 'form-check-input selecttree',
            onchange: 'selectTree(this)',
            checked: func.isAll,
            style: !func.isAll && func.isPartial ? 'background-color:grey' : ''
        });
        $checkbox.attr('businessId', businessId);
        $checkbox.attr('functionId', func.id);
        $summary.append($checkbox).append(` ${func.name}`);
        $details.append($summary);
        if (Array.isArray(func.assignedInfraObjects) && func.assignedInfraObjects.length > 0) {
            const $objectList = $('<ul class="tree"></ul>');
            createObjects($objectList, func.assignedInfraObjects, businessId, func.id);
            $details.append($objectList);
        }
        container.append($details);
    });
}
function createObjects(container, objects, parentId, functionId) {
    objects.forEach((obj) => {
        const objectDetails = $('<ul open></ul>'); // Add the "open" attribute
        const objectSummary =
            $('<summary style="list-style-type: none; padding : 1px 22px"></summary>'); // Add inline style to hide the arrow
        const objectCheckbox = $('<input type="checkbox" class="form-check-input selecttree" onchange="selectTree(this)">');
        objectCheckbox.prop('checked', obj.isSelected);
        objectCheckbox.attr('businessId', parentId);
        objectCheckbox.attr('functionId', functionId);
        objectCheckbox.attr('infraId', obj.id);
        objectSummary.append(objectCheckbox);
        objectSummary.append(' ' + obj.name);

        objectDetails.append(objectSummary);
        container.append(objectDetails[0]);
    });
}

async function updateParentCheckState($checkbox, childListPrefix) {
    const $list = $checkbox.closest(`ul[id^=${childListPrefix}]`);
    const listId = $list.attr('id');
    if (!listId) return;

    const $allCheckboxes = $(`#${listId} > li input[type=checkbox]`);
    const $checkedCheckboxes = $allCheckboxes.filter(':checked');

    const allChecked = $allCheckboxes.length > 0 && $checkedCheckboxes.length === $allCheckboxes.length;
    $(`#${listId}`).siblings("input[type=checkbox]").prop('checked', allChecked);

    check_fst_lvl(listId);
}

async function validateMobilePre(value) {
    const errorElement = $('#MobilePre-error');

    if (!value) {
        errorElement.text('Select country code').addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true
    }
}

async function validateMobile(value) {
    const errorElement = $('#Mobile-error');
    const minLength = 7;

    if (!value) {
        errorElement.text('Enter mobile number')
            .addClass('field-validation-error');
        return false;
    }

    if (value.length < minLength) {
        errorElement.text('Must be at least 7 characters')
            .addClass('field-validation-error');
        return false;
    }

    errorElement.text('')
        .removeClass('field-validation-error');
    return true;
}

async function validateLoginName(value, id = null, url) {
    let type = $("#userLoginType").val();
    const errorElement = $('#LoginName-error');

    if (!value) {
        errorElement.text('Enter login name')
            .addClass('field-validation-error');
        return false;
    }
    if (value.includes("<")) {
        $('#LoginName-error').text('Special characters not allowed').addClass('field-validation-error');
        return false;
    }
    var url = RootUrl + url;
    var data = {};
    data.loginName = value;
    data.id = id;
    const validationResults = [];

    validationResults.push(
        await ShouldNotBeginWithUnderScore(value),
        // await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumberForUser(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await secondChar(value),
        await IsNameExist(url, data, OnError)
    );

    if (type !== 'AD') {
        validationResults.push(await LoginSpecialCharValidate(value));
        validationResults.push(await ShouldNotBeginWithSpace(value));
        validationResults.push(await minMaxlength(value));
    }

    return await CommonValidation(errorElement, validationResults);
}

const ShouldNotBeginWithNumberForUser = (value) => { return (RegExp(/^\d+[a-zA-Z]/).test(value)) ? "Should not begin with number" : true; }

const LoginSpecialCharValidate = (value) => {
    const regex = /^[a-zA-Z0-9_\s.]*$/;

    if (!regex.test(value)) {
        return "Special characters not allowed";
    }

    if (/^\./.test(value)) {
        return "Special characters not allowed";
    }

    if (/\.$/.test(value)) {
        return "Special characters not allowed";
    }

    return true;
}

async function validateDropDown(value, errorMessage, errorElement) {
    if (!value) {
        errorElement.text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}

async function validateUserName(value, id = null) {
    const errorElement = $('#UserName-error');
    if (!value) {
        errorElement.text('Enter full name')
            .addClass('field-validation-error');
        return false;
    }
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        //await IsNameExist(value, id)
    ];
    return await CommonValidation(errorElement, validationResults);
}

async function validateEmail(value, id = null) {
    const errorElement = $('#Email-error');
    let format = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/; // /^[a-zA-Z0-9._]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
    const domainPart = value?.split('@')[1]; // Get everything after the '@' symbol
    const domainParts = domainPart?.split('.');

    // If there are more than two domain parts, it's invalid

    if (!value) {
        errorElement.text('Enter email address')
            .addClass('field-validation-error');
        return false;
    } else if (value.length >= 321) {
        errorElement.text('Enter the value less than 320 characters')
            .addClass('field-validation-error');
        return false;
    } else if (value.length) {
        if (format.test(value) == false) {
            errorElement.text('Invalid email')
                .addClass('field-validation-error');
            return false;
        } else if (value.charAt(0) == "." || value.charAt(0) == "_") {
            errorElement.text('Invalid email')
                .addClass('field-validation-error');
            return false;
        } else if (domainParts.length > 2) {

            errorElement.text('Email cannot have more than one top-level domain')
                .addClass('field-validation-error');
            return false;

        }
        else {
            errorElement.text('')
                .removeClass('field-validation-error');
            return true;
        }
    }
    else {
        errorElement.text('')
            .removeClass('field-validation-error');
        return true;
    }

    const validationResults = [await emailRegex(value)];
    return await CommonValidation(errorElement, validationResults);

    //const errorElement = $('#Email-error');
    //if (!value) {
    //    errorElement.text('Enter email address')
    //        .addClass('field-validation-error');
    //    return false;
    //}
    //const validationResults = [
    //    //await emailRegex(value),
    //    await validEmail(value),
    //];
    //return await CommonValidation(errorElement, validationResults);
}

const validEmail = (value) => { return (!(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,63}$/).test(value)) ? "Invalid email" : true; }

async function validateSessionTimeout(value, id = null) {
    const errorElement = $('#SessionTimeout-error');
    if (!value || value == 0) {
        errorElement.text('Enter session timeout')
            .addClass('field-validation-error');
        return false;
    }
    if (parseInt(value, 10) < 5) {
        errorElement.text('A minimum of 5 minutes is required') //Minimum 5 minutes required
            .addClass('field-validation-error');
        return false;
    }
    const validationResults = [
        await OnlyNum(value)
    ];
    return await CommonValidation(errorElement, validationResults);
}

async function IsNameExist(url, data, errorFunc) {
    return !data.loginName.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}

async function ValidateLoginType(value) {
    if (value == "AD") {
        $('#ad_hide').css('display', 'block');
        $('.inhouse_div').css('display', 'none');
    } else {
        $('#ad_hide').css('display', 'none');
        $('.inhouse_div').css('display', 'block');
    }
}

async function SetDomainUser(value, value2) {

    var url = RootUrl + userManageURL.domainUsers;
    var data = {};
    data.domainName = value;
    data.domainUserName = value2
    var result = await GetAsync(url, data, OnError);
    $('#userDomainUser').empty();
    $('#userDomainUser').append('<option value="">Select User</option>');
    if (result.length > 0) {
        for (var index = 0; index <= result.length; index++) {
            if (result[index] != undefined) {
                $('#userDomainUser').append('<option value="' + result[index] + '">' + result[index] + '</option>');
            }

        }
    }

}

async function GetUserInfraObjectList(user) {
    var url = RootUrl + "Admin/User/GetUserInfraObjects";
    var data = {};
    data.Id = user.Id;
    var result = await GetAsync(url, data, OnError);
    jsonData = result;
}

async function SetDomain() {
    var url = RootUrl + userManageURL.domains;
    var data = {};
    var result = await GetAsync(url, data, OnError);
    $('#userDomain').empty();
    if (result.success == true) {
        $('#userDomain').append('<option value=""> Select Domain</option>');
        for (var index = 0; index < result.message.length; index++) {
            $('#userDomain').append('<option value="' + result.message[index] + '">' + result.message[index] + '</option>');
        }

    }
    else {
        notificationAlert("warning", result.message);
    }
}
function filterJSONData(jsonData) {
    const filteredData = {
        isAll: jsonData.isAll,
        assignedBusinessServices: []
    };

    $.each(jsonData.assignedBusinessServices || [], function (_, service) {
        const filteredService = {
            id: service.id,
            name: service.name,
            isAll: service.isAll,
            isPartial: service.isPartial,
            assignedBusinessFunctions: []
        };

        $.each(service.assignedBusinessFunctions || [], function (_, func) {
            const filteredFunction = {
                id: func.id,
                name: func.name,
                isAll: func.isAll,
                isPartial: func.isPartial,
                assignedInfraObjects: []
            };

            $.each(func.assignedInfraObjects || [], function (_, infraObject) {
                if (infraObject.isSelected) {
                    filteredFunction.assignedInfraObjects.push({
                        id: infraObject.id,
                        name: infraObject.name,
                        isSelected: true
                    });
                }
            });

            if (filteredFunction.assignedInfraObjects.length) {
                filteredService.assignedBusinessFunctions.push(filteredFunction);
            }
        });

        if (filteredService.assignedBusinessFunctions.length) {
            filteredData.assignedBusinessServices.push(filteredService);
        }
    });

    return filteredData;
}
function check_fst_lvl(dd) {
    var ss = $('#' + dd).parent().closest("ul").attr("id");
    if ($('#' + ss + ' > li input[type=checkbox]:checked').length == $('#' + ss + ' > li input[type=checkbox]').length) {
        $('#' + ss).siblings("input[type=checkbox]").prop('checked', true);
    }
    else {
        $('#' + ss).siblings("input[type=checkbox]").prop('checked', false);
    }
}
function pageLoad() {
    $(".plus").on('click', function () {
        $(this).toggleClass("minus").siblings("ul").toggle();
    })
}