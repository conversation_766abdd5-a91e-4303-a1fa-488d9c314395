using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Events.Create;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Create;

public class CreateApprovalMatrixApprovalCommandHandler : IRequestHandler<CreateApprovalMatrixApprovalCommand,
    CreateApprovalMatrixApprovalResponse>
{
    private readonly IApprovalMatrixApprovalRepository _approvalMatrixApprovalRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateApprovalMatrixApprovalCommandHandler(IMapper mapper,
        IApprovalMatrixApprovalRepository approvalMatrixApprovalRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _approvalMatrixApprovalRepository = approvalMatrixApprovalRepository;
    }

    public async Task<CreateApprovalMatrixApprovalResponse> Handle(CreateApprovalMatrixApprovalCommand request,
        CancellationToken cancellationToken)
    {
        var approvalMatrixApproval = _mapper.Map<Domain.Entities.ApprovalMatrixApproval>(request);

        approvalMatrixApproval = await _approvalMatrixApprovalRepository.AddAsync(approvalMatrixApproval);

        var response = new CreateApprovalMatrixApprovalResponse
        {
            Message =
                Message.Create(nameof(Domain.Entities.ApprovalMatrixApproval), approvalMatrixApproval.ProcessName),

            Id = approvalMatrixApproval.ReferenceId
        };

        await _publisher.Publish(new ApprovalMatrixApprovalCreatedEvent { Name = approvalMatrixApproval.ProcessName },
            cancellationToken);

        return response;
    }
}