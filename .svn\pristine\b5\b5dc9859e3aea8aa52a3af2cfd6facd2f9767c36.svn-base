﻿using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyReport;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using DevExpress.XtraCharts;
using Newtonsoft.Json;
using System.Data;
using System.Drawing;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate;

[SupportedOSPlatform("windows")]
public partial class DRReadyReport : DevExpress.XtraReports.UI.XtraReport
{
    private readonly ILogger<PreBuildReportController> _logger;
    public DrReadyStatusReport drReadyStatusReport = new DrReadyStatusReport();

    Int32 upvalue = 0;
    Int32 downvalue = 0;
    int totalvalue = 0;

    public DRReadyReport(string data)
    {
        try
        {
            _logger = PreBuildReportController._logger;
            drReadyStatusReport = JsonConvert.DeserializeObject<DrReadyStatusReport>(data);
            var DRreadyReport = drReadyStatusReport.DrReadyStatusForDrReadyReportVms;

            #region Table Count
            //for (int i = 0; i < 29; i++)
            //{
            //    DRreadyReport.Add(new DrReadyStatusForDrReadyReportVm
            //    {
            //        Id = $"Id{i}",
            //        BusinessServiceId = $"BusinessServiceId{i}",
            //        BusinessServiceName = $"BusinessServiceName{i}",
            //        BusinessFunctionId = $"BusinessFunctionId{i}",
            //        BusinessFunctionName = $"BusinessFunctionName{i}",
            //        InfraObjectId = $"InfraObjectId{i}",
            //        InfraObjectName = $"InfraObjectName{i}",
            //        ErrorMessage = (i % 2 == 0) ? "Affected" : "Not Affected",
            //        DRReady = (i % 2 == 0) ? "1" : "0",
            //        InfraObjectCountLists = new List<InfraObjectCountList>
            //{
            //    new InfraObjectCountList
            //    {
            //        BusinessServiceId = $"BusinessServiceId{i}",
            //        BusinessFunctionId = $"BusinessFunctionId{i}",
            //        BusinessFunctionName = $"BusinessFunctionName{i}",
            //        InfraObjectId = $"InfraObjectId{i}",
            //        TotalCount = i * 2,
            //        UpCount = i * 2 + 1,
            //        DownCount = i * 2 + 1
            //    },
            //}
            //    });
            //}
            //foreach (var GrReport in DRreadyReport)
            //{
            //    if (GrReport.InfraObjectCountLists != null)
            //    {
            //        foreach (var Infraobjectcounts in GrReport.InfraObjectCountLists)
            //        {
            //            Infralist.Add(Infraobjectcounts);
            //        }
            //    }
            //}
            #endregion
            DRreadyReport.ForEach(x => x.ErrorMessage = string.IsNullOrEmpty(x.ErrorMessage) || string.IsNullOrWhiteSpace(x.ErrorMessage) ? x.ErrorMessage = "" : x.ErrorMessage);
            DRreadyReport.ForEach(x => x.ErrorMessage = x.DRReady == "1" ? "Success" : x.ErrorMessage);
            InitializeComponent();
            ClientCompanyLogo();
            this.DetailReport1.DataSource = drReadyStatusReport.InfraCountList;
            this.DetailReport.DataSource = DRreadyReport;
            this.DisplayName = drReadyStatusReport.BusinessServiceName.ToString() + "_Resilience Readiness Report_" + DateTime.Now.ToString("ddMMyyyy" + "_" + "hhmmsstt");
            xrTableCell2.BeforePrint += tableCell_SerialNumber_BeforePrint;
            Int64 Infracountlist = 0;
            Int64 drready = 0;
            Int64 drready1 = 0;
            Int64 infra = 0;
            foreach (var value in DRreadyReport)
            {
                int Count = (int)Convert.ToInt64(value.DRReady.ToString() == "1");
                int Counts = (int)Convert.ToInt64(value.DRReady.ToString() == "0");
                drready = drready + Count;
                drready1 = drready1 + Counts;
                infra = value.InfraObjectName.Count();
                Infracountlist = InfraObjectCountList(value.InfraObjectCountLists);
                xrLabel20.Text = drReadyStatusReport.BusinessServiceName.ToString();
                xrLabel8.Text = drready.ToString();
                xrLabel3.Text = drready1.ToString();
            }
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the Resiliency Readiness Report. The error message : " + ex.Message); throw; }
    }
    private int InfraObjectCountList(List<InfraObjectCountList> infraObjectCountLists)
    {
        if (infraObjectCountLists != null)
        {
            foreach (var val in infraObjectCountLists)
            {
                upvalue = upvalue + val.UpCount;
                downvalue = val.DownCount;
                totalvalue = val.TotalCount;
            }
        }
        return upvalue + downvalue + totalvalue;
    }
    private int serialNumber = 1;
    private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
    {
        XRTableCell cell = (XRTableCell)sender;
        cell.Text = serialNumber.ToString();
        serialNumber++;
    }
    private void _userName_BeforePrint(object sender, CancelEventArgs e)
    {
        try
        {
            _username.Text = "Report Generated By: " + drReadyStatusReport.ReportGeneratedBy.ToString();
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the Resiliency Readiness Report's User name. The error message : " + ex.Message); throw; }
    }
    private DataTable CreateChartData(List<InfraObjectCountList> infralist)
    {
        DataTable table = new DataTable("Table1");
        table.Columns.Add("Argument", typeof(string));
        table.Columns.Add("ValueUp", typeof(Int64));
        table.Columns.Add("ValueDown", typeof(Int64));
        infralist.Reverse();

        List<string> uniques = new List<string>();
        foreach (var item in infralist)
        {
            if (!uniques.Contains(item.BusinessFunctionName))
            {
                uniques.Add(item.BusinessFunctionName);
            }
            else
            {
                item.BusinessFunctionName = " " + item.BusinessFunctionName;
            }
        }
        foreach (var infra in infralist)
        {
            table.Rows.Add(infra.BusinessFunctionName, infra.UpCount, infra.DownCount);
        }
        return table;
    }
    private void xrChart1_BeforePrint(object sender, CancelEventArgs e)
    {
        try
        {
            //Infralist = PreBuildReportController.Infralists;
            #region Chart 
            int calculatedHeight = 0;
            double dynamicBarWidth = 0;
            int dataPointCount = drReadyStatusReport.InfraCountList.Count;
            if (dataPointCount < 4)
            {
                int minHeight = 265;

                dynamicBarWidth = 0.1;

                calculatedHeight = (int)Math.Max(minHeight, dataPointCount * 20 * dynamicBarWidth);
            }
            else
            {
                int minHeight = Math.Min(dataPointCount * 50, 750);

                dynamicBarWidth = 0.5;

                calculatedHeight = (int)Math.Max(minHeight, dataPointCount * 20 * dynamicBarWidth);
            }
            // Set the calculated height to the chart
            xrChart1.HeightF = calculatedHeight;
            #endregion

            Series series = new Series("Series1", ViewType.Bar);
            xrChart1.Series.Add(series);
            series.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;
            series.DataSource = CreateChartData(drReadyStatusReport.InfraCountList);
            series.ArgumentScaleType = ScaleType.Auto;
            series.ArgumentDataMember = "Argument";
            series.ValueScaleType = ScaleType.Numerical;
            series.ValueDataMembers.AddRange(new string[] { "ValueUp" });

            SideBySideBarSeriesView view = series.View as SideBySideBarSeriesView;
            view.BarWidth = dynamicBarWidth;
            view.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
            view.Pane.BorderVisible = false;

            Series series2 = new Series("Series2", ViewType.Bar);
            xrChart1.Series.Add(series2);
            series2.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;
            series2.DataSource = CreateChartData(drReadyStatusReport.InfraCountList);
            series2.ArgumentScaleType = ScaleType.Auto;
            series2.ArgumentDataMember = "Argument";
            series2.ValueScaleType = ScaleType.Numerical;
            series2.ValueDataMembers.AddRange(new string[] { "ValueDown" });
            SideBySideBarSeriesView view1 = series2.View as SideBySideBarSeriesView;
            view1.BarWidth = dynamicBarWidth;
            view1.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
            view1.Pane.BorderVisible = false;

            xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            ((XYDiagram)xrChart1.Diagram).Rotated = true;
            ((XYDiagram)xrChart1.Diagram).AxisY.GridLines.Visible = false;
            int red = 138;
            int green = 144;
            int blue = 154;

            // Create a Color object
            Color customColor = Color.FromArgb(red, green, blue);

            // Set the X-axis label text color
            ((XYDiagram)xrChart1.Diagram).AxisX.Label.TextColor = customColor;
            //Only integers allow in chart
            NumericScaleOptions numericScaleOptions = ((XYDiagram)xrChart1.Diagram).AxisY.NumericScaleOptions;
            numericScaleOptions.GridOffset = 0;
            numericScaleOptions.AutoGrid = false;
            numericScaleOptions.GridAlignment = NumericGridAlignment.Ones;
            numericScaleOptions.GridSpacing = 1;

            xrChart1.Series.AddRange(new Series[] { series, series2 });
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the Resiliency Readiness Report's Chart. The error message : " + ex.Message); throw; }
    }
    private void _version_BeforePrint(object sender, CancelEventArgs e)
    {
        try
        {
            var builder = new ConfigurationBuilder()
                 .SetBasePath(Directory.GetCurrentDirectory())
                 .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            IConfigurationRoot configuration = builder.Build();
            var version = configuration["CP:Version"];
            xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the Resiliency Readiness Report's CP Version. The error message : " + ex.Message); throw; }
    }
    public void ClientCompanyLogo()
    {
        try
        {
            string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
            if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
            {
                prperpetuuitiLogo.Visible = false;
                if (imgbase64String.Contains(","))
                {
                    imgbase64String = imgbase64String.Split(',')[1];
                }
                byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                using (MemoryStream ms = new MemoryStream(imageBytes))
                {
                    Image image = Image.FromStream(ms);
                    prClientLogo.Image = image;
                }
            }
            else
            {
                prClientLogo.Visible = false;
                prperpetuuitiLogo.Visible = true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError("Error occured while display the customer logo in Resiliency Readiness Report " + ex.Message.ToString());
        }
    }
}