﻿using ContinuityPatrol.Application.Features.BusinessService.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessService.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessService.Events.Create;
using ContinuityPatrol.Application.Features.BusinessService.Events.Delete;
using ContinuityPatrol.Application.Features.BusinessService.Events.PaginatedView;
using ContinuityPatrol.Application.Features.BusinessService.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class BusinessServiceFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<BusinessService> BusinessServices { get; set; }

    public CreateBusinessServiceCommand CreateBusinessServiceCommand { get; set; }

    public UpdateBusinessServiceCommand UpdateBusinessServiceCommand { get; set; }

    public BusinessServiceCreatedEvent BusinessServiceCreatedEvent { get; set; }

    public BusinessServiceDeletedEvent BusinessServiceDeletedEvent { get; set; }

    public BusinessServiceUpdatedEvent BusinessServiceUpdatedEvent { get; set; }

    public BusinessServicePaginatedEvent BusinessServicePaginatedEvent { get; set; }


    public BusinessServiceFixture()
    {
        BusinessServices = AutoBusinessServiceFixture.Create<List<BusinessService>>();

        CreateBusinessServiceCommand = AutoBusinessServiceFixture.Create<CreateBusinessServiceCommand>();

        UpdateBusinessServiceCommand = AutoBusinessServiceFixture.Create<UpdateBusinessServiceCommand>();

        BusinessServiceCreatedEvent = AutoBusinessServiceFixture.Create<BusinessServiceCreatedEvent>();

        BusinessServiceDeletedEvent = AutoBusinessServiceFixture.Create<BusinessServiceDeletedEvent>();

        BusinessServiceUpdatedEvent = AutoBusinessServiceFixture.Create<BusinessServiceUpdatedEvent>();

        BusinessServicePaginatedEvent = AutoBusinessServiceFixture.Create<BusinessServicePaginatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<BusinessServiceProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoBusinessServiceFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateBusinessServiceCommand>(p => p.Name, 10));
            fixture.Customize<CreateBusinessServiceCommand>(c => c.With(b => b.CompanyId, 0.ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateBusinessServiceCommand>(p => p.Name, 10));
            fixture.Customize<UpdateBusinessServiceCommand>(c => c.With(b => b.Id, 0.ToString));
            fixture.Customize<BusinessService>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<BusinessServiceCreatedEvent>(p => p.BusinessServiceName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<BusinessServiceDeletedEvent>(p => p.BusinessServiceName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<BusinessServiceUpdatedEvent>(p => p.BusinessServiceName, 10));

            //fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<BusinessServicePaginatedEvent>(p => p.BusinessServiceName, 10));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}