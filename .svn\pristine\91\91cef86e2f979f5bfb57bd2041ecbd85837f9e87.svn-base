using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DynamicDashboard.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicDashboard.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicDashboard.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetList;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DynamicDashboardControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DynamicDashboardsController _controller;
    private readonly DynamicDashboardFixture _dynamicDashboardFixture;

    public DynamicDashboardControllerTests()
    {
        _dynamicDashboardFixture = new DynamicDashboardFixture();

        var testBuilder = new ControllerTestBuilder<DynamicDashboardsController>();
        _controller = testBuilder.CreateController(
            _ => new DynamicDashboardsController(),
            out _mediatorMock);
    }

    #region GetDynamicDashboards Tests

    [Fact]
    public async Task GetDynamicDashboards_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedList = _dynamicDashboardFixture.DynamicDashboardListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDynamicDashboards();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicDashboardListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item => Assert.NotNull(item.Id));
    }

    [Fact]
    public async Task GetDynamicDashboards_WithEmptyList_ReturnsEmptyOkResult()
    {
        // Arrange
        var emptyList = new List<DynamicDashboardListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDynamicDashboards();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicDashboardListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDynamicDashboards_WithException_ThrowsException()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardListQuery>(), default))
            .ThrowsAsync(new Exception("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _controller.GetDynamicDashboards());
    }

    #endregion

    #region GetDynamicDashboardById Tests

    [Fact]
    public async Task GetDynamicDashboardById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _dynamicDashboardFixture.DynamicDashboardDetailVm;
        expectedDetail.ReferenceId = id;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDynamicDashboardById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DynamicDashboardDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.ReferenceId);
        Assert.NotNull(returnedDetail.Name);
    }

    [Fact]
    public async Task GetDynamicDashboardById_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDynamicDashboardById(invalidId));
    }

    [Fact]
    public async Task GetDynamicDashboardById_WithNullId_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetDynamicDashboardById(null));
    }

    #endregion

    #region GetPaginatedDynamicDashboards Tests

    [Fact]
    public async Task GetPaginatedDynamicDashboards_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _dynamicDashboardFixture.GetDynamicDashboardPaginatedListQuery;
        var expectedResult = _dynamicDashboardFixture.DynamicDashboardPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicDashboards(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DynamicDashboardListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.Equal(expectedResult.CurrentPage, returnedResult.CurrentPage);
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboards_WithNullQuery_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetPaginatedDynamicDashboards(null));
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboards_WithLargePageSize_ReturnsValidResult()
    {
        // Arrange
        var query = _dynamicDashboardFixture.GetDynamicDashboardPaginatedListQuery;
        query.PageSize = 100;
        var expectedResult = _dynamicDashboardFixture.DynamicDashboardPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicDashboards(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DynamicDashboardListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
    }

    #endregion

    #region CreateDynamicDashboard Tests

    [Fact]
    public async Task CreateDynamicDashboard_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _dynamicDashboardFixture.CreateDynamicDashboardCommand;
        var expectedResponse = _dynamicDashboardFixture.CreateDynamicDashboardResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDynamicDashboard(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDynamicDashboardResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task CreateDynamicDashboard_WithNullCommand_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.CreateDynamicDashboard(null));
    }

    [Fact]
    public async Task CreateDynamicDashboard_WithInvalidCommand_ReturnsFailureResponse()
    {
        // Arrange
        var command = _dynamicDashboardFixture.CreateDynamicDashboardCommand;
        var failureResponse = new CreateDynamicDashboardResponse
        {
            Success = false,
            Message = "Validation failed"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.CreateDynamicDashboard(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDynamicDashboardResponse>(createdResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Equal("Validation failed", returnedResponse.Message);
    }

    #endregion

    #region UpdateDynamicDashboard Tests

    [Fact]
    public async Task UpdateDynamicDashboard_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _dynamicDashboardFixture.UpdateDynamicDashboardCommand;
        var expectedResponse = _dynamicDashboardFixture.UpdateDynamicDashboardResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDynamicDashboard(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDynamicDashboardResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDynamicDashboard_WithNullCommand_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.UpdateDynamicDashboard(null));
    }

    [Fact]
    public async Task UpdateDynamicDashboard_WithInvalidCommand_ReturnsFailureResponse()
    {
        // Arrange
        var command = _dynamicDashboardFixture.UpdateDynamicDashboardCommand;
        var failureResponse = new UpdateDynamicDashboardResponse
        {
            Success = false,
            Message = "Update validation failed"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.UpdateDynamicDashboard(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDynamicDashboardResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Equal("Update validation failed", returnedResponse.Message);
    }

    #endregion

    #region DeleteDynamicDashboard Tests

    [Fact]
    public async Task DeleteDynamicDashboard_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _dynamicDashboardFixture.DeleteDynamicDashboardResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDynamicDashboardCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDynamicDashboard(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDynamicDashboardResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("deleted successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task DeleteDynamicDashboard_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDynamicDashboard(invalidId));
    }

    [Fact]
    public async Task DeleteDynamicDashboard_WithNullId_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteDynamicDashboard(null));
    }

    #endregion

    #region IsDynamicDashboardNameExist Tests

    [Fact]
    public async Task IsDynamicDashboardNameExist_WithExistingName_ReturnsTrue()
    {
        // Arrange
        var dashboardName = "Enterprise Dashboard";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDynamicDashboardNameExist(dashboardName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedValue);
    }

    [Fact]
    public async Task IsDynamicDashboardNameExist_WithNonExistingName_ReturnsFalse()
    {
        // Arrange
        var dashboardName = "Non-Existing Dashboard";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDynamicDashboardNameExist(dashboardName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    [Fact]
    public async Task IsDynamicDashboardNameExist_WithNullName_ThrowsArgumentNullException()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.IsDynamicDashboardNameExist(null, id));
    }

    #endregion

    #region Additional Comprehensive Test Cases

    [Fact]
    public async Task CreateDynamicDashboard_WithComplexConfiguration_ReturnsCreatedResult()
    {
        // Arrange
        var command = _dynamicDashboardFixture.CreateDynamicDashboardCommand;
        command.Name = "Enterprise Multi-Tenant Dashboard";
        command.Url = "/dashboard/enterprise-multi-tenant";
        var expectedResponse = _dynamicDashboardFixture.CreateDynamicDashboardResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDynamicDashboard(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDynamicDashboardResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Multi-Tenant Dashboard", command.Name);
        Assert.Contains("multi-tenant", command.Url);
    }

    [Fact]
    public async Task UpdateDynamicDashboard_WithStatusChange_ReturnsOkResult()
    {
        // Arrange
        var command = _dynamicDashboardFixture.UpdateDynamicDashboardCommand;
        command.Name = "Updated Enterprise Dashboard";
        command.Url = "/dashboard/enterprise-updated";
        var expectedResponse = _dynamicDashboardFixture.UpdateDynamicDashboardResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDynamicDashboard(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDynamicDashboardResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Updated Enterprise Dashboard", command.Name);
        Assert.Contains("updated", command.Url);
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboards_WithSearchFilter_ReturnsFilteredResults()
    {
        // Arrange
        var query = _dynamicDashboardFixture.GetDynamicDashboardPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 10;
        query.SearchString = "Enterprise";
        var expectedResult = _dynamicDashboardFixture.DynamicDashboardPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicDashboards(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DynamicDashboardListVm>>(okResult.Value);
        Assert.Equal("Enterprise", query.SearchString);
        Assert.Equal(1, returnedResult.CurrentPage);
        Assert.Equal(10, query.PageSize);
    }

    #endregion

    #region Additional Test Cases Following CompanyControllerTests and BusinessServiceControllerTests Patterns

    [Fact]
    public async Task GetDynamicDashboards_ReturnsEmptyList_WhenNoDashboardsExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardListQuery>(), default))
            .ReturnsAsync(new List<DynamicDashboardListVm>());

        // Act
        var result = await _controller.GetDynamicDashboards();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicDashboardListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDynamicDashboardById_Throws_WhenDashboardNotFound()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardDetailQuery>(q => q.Id == id), default))
            .ThrowsAsync(new NotFoundException("DynamicDashboard", id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetDynamicDashboardById(id));
    }

    [Fact]
    public async Task CreateDynamicDashboard_Throws_WhenNameExists()
    {
        // Arrange
        var command = _dynamicDashboardFixture.CreateDynamicDashboardCommand;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDynamicDashboardCommand>(), default))
            .ThrowsAsync(new InvalidOperationException("Dashboard name already exists"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.CreateDynamicDashboard(command));
    }

    [Fact]
    public async Task UpdateDynamicDashboard_Throws_WhenDashboardNotFound()
    {
        // Arrange
        var command = _dynamicDashboardFixture.UpdateDynamicDashboardCommand;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDynamicDashboardCommand>(), default))
            .ThrowsAsync(new NotFoundException("DynamicDashboard", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.UpdateDynamicDashboard(command));
    }

    [Fact]
    public async Task DeleteDynamicDashboard_Throws_WhenDashboardNotFound()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDynamicDashboardCommand>(c => c.Id == id), default))
            .ThrowsAsync(new NotFoundException("DynamicDashboard", id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.DeleteDynamicDashboard(id));
    }

    [Fact]
    public async Task IsDynamicDashboardNameExist_IncludesIdInQuery_WhenProvided()
    {
        // Arrange
        var dashboardName = "Test Dashboard";
        var dashboardId = Guid.NewGuid().ToString();
        string? capturedId = null;
        string? capturedName = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardNameUniqueQuery>(), default))
            .Callback<IRequest<bool>, CancellationToken>((request, _) =>
            {
                if (request is GetDynamicDashboardNameUniqueQuery query)
                {
                    capturedId = query.Id;
                    capturedName = query.Name;
                }
            })
            .ReturnsAsync(false);

        // Act
        await _controller.IsDynamicDashboardNameExist(dashboardName, dashboardId);

        // Assert
        Assert.Equal(dashboardId, capturedId);
        Assert.Equal(dashboardName, capturedName);
    }

    [Fact]
    public async Task IsDynamicDashboardNameExist_ExcludesIdFromQuery_WhenNotProvided()
    {
        // Arrange
        var dashboardName = "Test Dashboard";
        string? capturedId = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardNameUniqueQuery>(), default))
            .Callback<IRequest<bool>, CancellationToken>((request, _) =>
            {
                if (request is GetDynamicDashboardNameUniqueQuery query)
                {
                    capturedId = query.Id;
                }
            })
            .ReturnsAsync(false);

        // Act
        await _controller.IsDynamicDashboardNameExist(dashboardName, null);

        // Assert
        Assert.Null(capturedId);
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboards_HandlesSearchString_Correctly()
    {
        // Arrange
        var query = _dynamicDashboardFixture.GetDynamicDashboardPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 10;
        query.SearchString = "Enterprise";

        var expectedData = _dynamicDashboardFixture.DynamicDashboardListVm.Take(1).ToList();
        var expectedResult = PaginatedResult<DynamicDashboardListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardPaginatedListQuery>(q => q.SearchString == "Enterprise"), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicDashboards(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<DynamicDashboardListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);
        Assert.Equal(1, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboards_HandlesDifferentPageSizes()
    {
        // Arrange
        var query = _dynamicDashboardFixture.GetDynamicDashboardPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 5;

        var expectedData = _dynamicDashboardFixture.DynamicDashboardListVm.Take(5).ToList();
        var expectedResult = PaginatedResult<DynamicDashboardListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardPaginatedListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicDashboards(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<DynamicDashboardListVm>>(okResult.Value);
        Assert.Equal(5, paginatedResult.PageSize);
        Assert.Equal(1, paginatedResult.CurrentPage);
    }

    [Fact]
    public async Task CreateDynamicDashboard_Returns201Created_WithSuccessMessage()
    {
        // Arrange
        var command = _dynamicDashboardFixture.CreateDynamicDashboardCommand;
        var expectedMessage = $"DynamicDashboard '{command.Name}' has been created successfully!";
        var expectedResponse = new CreateDynamicDashboardResponse
        {
            Id = Guid.NewGuid().ToString(),
            Success = true,
            Message = expectedMessage
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDynamicDashboard(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateDynamicDashboardResponse>(createdResult.Value);
        Assert.True(response.Success);
        Assert.Contains(command.Name, response.Message);
    }

    [Fact]
    public async Task UpdateDynamicDashboard_ReturnsOkResult_WithSuccessMessage()
    {
        // Arrange
        var command = _dynamicDashboardFixture.UpdateDynamicDashboardCommand;
        var expectedMessage = $"DynamicDashboard '{command.Name}' has been updated successfully!";
        var expectedResponse = new UpdateDynamicDashboardResponse
        {
            Id = command.Id,
            Success = true,
            Message = expectedMessage
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDynamicDashboard(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateDynamicDashboardResponse>(okResult.Value);
        Assert.True(response.Success);
        Assert.Contains(command.Name, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task GetDynamicDashboardById_WithValidId_ReturnsCorrectDashboardType()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _dynamicDashboardFixture.DynamicDashboardDetailVm;
        expectedDetail.ReferenceId = id;
        expectedDetail.Name = "Analytics Dashboard";
        expectedDetail.Url = "/dashboard/analytics";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicDashboardDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDynamicDashboardById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DynamicDashboardDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.ReferenceId);
        Assert.Equal("Analytics Dashboard", returnedDetail.Name);
        Assert.Equal("/dashboard/analytics", returnedDetail.Url);
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboards_WithEmptySearchString_ReturnsAllResults()
    {
        // Arrange
        var query = _dynamicDashboardFixture.GetDynamicDashboardPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 10;
        query.SearchString = "";

        var expectedData = _dynamicDashboardFixture.DynamicDashboardListVm;
        var expectedResult = PaginatedResult<DynamicDashboardListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardPaginatedListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicDashboards(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<DynamicDashboardListVm>>(okResult.Value);
        Assert.Equal(expectedData.Count, paginatedResult.Data.Count);
        Assert.Equal(expectedData.Count, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task IsDynamicDashboardNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var dashboardName = "Existing Dashboard";
        var dashboardId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDynamicDashboardNameExist(dashboardName, dashboardId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsDynamicDashboardNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var dashboardName = "New Dashboard";
        var dashboardId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicDashboardNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDynamicDashboardNameExist(dashboardName, dashboardId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task DeleteDynamicDashboard_ReturnsOkResult_WithSuccessMessage()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = new DeleteDynamicDashboardResponse
        {
            Success = true,
            Message = "DynamicDashboard has been deleted successfully!"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDynamicDashboardCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDynamicDashboard(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteDynamicDashboardResponse>(okResult.Value);
        Assert.True(response.Success);
        Assert.Contains("deleted successfully", response.Message);
    }

    #endregion
}
