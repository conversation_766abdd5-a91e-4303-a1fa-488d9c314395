﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<style>
    .table-bordered thead tr th {
        background-color: #eef1f45c !important;
    }

    .user-profile-timeline-container ul.tl li {
        padding: 0 0 20px 20px;
    }

    ul.tl li .item-text {
        background-color: #f4f4f4;
        padding: 5px 5px;
        border-radius: 5px;
    }

    .user-profile-timeline-container ul.tl li .item-icon {
        top: 0px;
        left: -13px;
        height: 25px;
        width: 25px;
        border: none;
    }

    .item-icon i {
        color: #4d4d4d;
        padding: 5px;
        line-height: 1.9;
        font-size: 14px;
    }
</style>
@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title" title="Bulk Import"><i class="cp-bulk-import"></i><span>Bulk Import</span></h6>
            <div class="d-flex gap-2">
                <div class="dropdown">
                    <button id="downloadBulk" type="button" class="btn btn-sm btn-primary" data-bs-toggle="dropdown" aria-label="Download" data-bs-auto-close="false" aria-expanded="false">
                        <i class="cp-download" title="Download"></i>
                    </button>
                    <form id="templateForm" class="dropdown-menu p-3" style="width: 15rem;">
                        <div class="form-group">
                            <label class="form-label" for="SolutionType">
                                Solution Type
                            </label>
                            <div class="input-group">
                                <select class="form-select" id="SolutionType" data-placeholder="Select Solution Type">
                                    <option value="">Select</option>
                                    <option value="Mysql">Mysql - Native Log Shipping</option>
                                    <option value="MssqlNLS">MSSQL - NLS</option>
                                    <option value="MSSQLMirroring">MSSQL - DB Mirroring</option>
                                    <option value="MSSQLAlWaysOn">MSSQL - AlwaysOn</option>
                                    <option value="Oracle">Oracle DataGuard</option>
                                    <option value="OracleRac">Oracle Rac</option>
                                    <option value="Mongo">MongoDB</option>
                                    <option value="RoboCopy">RoboCopy</option>
                                    <option value="Postgres">Postgres</option>
                                    <option value="SRM">SRM</option>
                                </select>
                            </div>
                            <span id="SolutionType_Error"></span>
                        </div>
                        <span id="DownloadTemplate" type="button" class="btn btn-sm btn-primary">Download Template</span>
                    </form>
                </div>
                <button type="button" class="btn btn-primary btn-sm d-none" data-bs-toggle="modal" data-bs-target="#offcanvasTop" id="validationModal">
                    Bulk Page
                </button>
            </div>
        </div>
        <div class="pt-1 card-body" style="height: calc(100vh - 141px);">
            <div class="p-3 h-100  d-grid" style="border: 2px dotted rgb(114, 127, 136); border-radius: 10px;">
                <div role="presentation" id="dropzone" class="dropzone text-center d-grid col">
                    <input multiple="" type="file" tabindex="-1" style="display: none;">
                    <p class="form-title mb-0 align-self-end">Drag &amp; Drop file to Upload</p>
                    <button type="button" class="ps-1 border-0 d-block w-100 bg-transparent  mb-1" ria-label="Upload File" style="cursor:default">
                        @*  title="Upload File" *@
                        <span>
                            <svg width="110" height="95" viewBox="0 0 110 95" fill="none">
                                <g clip-path="url()">
                                    <path d="M40.0002 94.7498H67.0303V71.5828H59.3102C58.8025 71.5837 58.2996 71.4845 57.8303 71.2909C57.3609 71.0973 56.9343 70.8131 56.5749 70.4545C56.2155 70.0959 55.9302 69.67 55.7355 69.201C55.5408 68.7321 55.4405 68.2295 55.4402 67.7217V59.9998H40.0002V94.7498Z"
                                          fill="#007BFF"></path>
                                    <path d="M30.1112 43.6368C27.1985 42.3539 24.049 41.6963 20.8663 41.7067C-4.28875 43.2597 -4.28875 75.3238 20.8663 76.8778H80.4662C87.5516 76.9844 94.4593 74.6576 100.036 70.2858C117.716 56.7728 108.256 29.6058 84.9862 27.0168C76.5962 -17.1002 3.8772 -0.339272 21.1312 41.7067"
                                          fill="#007BFF"></path>
                                    <path d="M45.79 78.556C45.3758 78.556 45.04 78.8918 45.04 79.306C45.04 79.7202 45.3758 80.056 45.79 80.056V78.556ZM57.37 80.056C57.7842 80.056 58.12 79.7202 58.12 79.306C58.12 78.8918 57.7842 78.556 57.37 78.556V80.056ZM45.79 86.278C45.3758 86.278 45.04 86.6138 45.04 87.028C45.04 87.4422 45.3758 87.778 45.79 87.778V86.278ZM61.24 87.778C61.6542 87.778 61.99 87.4422 61.99 87.028C61.99 86.6138 61.6542 86.278 61.24 86.278V87.778ZM56.2 60C56.2 59.5858 55.8642 59.25 55.45 59.25C55.0358 59.25 54.7 59.5858 54.7 60H56.2ZM55.45 67.722H54.7C54.7 67.7389 54.7006 67.7558 54.7017 67.7727L55.45 67.722ZM59.31 71.583L59.2591 72.3313C59.2761 72.3324 59.293 72.333 59.31 72.333V71.583ZM67.03 72.333C67.4442 72.333 67.78 71.9972 67.78 71.583C67.78 71.1688 67.4442 70.833 67.03 70.833V72.333ZM67.03 94.75H67.78C67.78 94.3358 67.4442 94 67.03 94V94.75ZM40 94.75H39.25C39.25 95.1642 39.5858 95.5 40 95.5V94.75ZM40 60V59.25C39.5858 59.25 39.25 59.5858 39.25 60H40ZM57.38 60L57.9104 59.4698C57.7698 59.329 57.579 59.25 57.38 59.25V60ZM67.03 69.653H67.78C67.78 69.4541 67.701 69.2634 67.5604 69.1228L67.03 69.653ZM66.28 94.753C66.28 95.1672 66.6158 95.503 67.03 95.503C67.4442 95.503 67.78 95.1672 67.78 94.753H66.28ZM7.57647 43.949C7.23462 44.1829 7.14712 44.6497 7.38103 44.9915C7.61494 45.3334 8.08168 45.4209 8.42353 45.187L7.57647 43.949ZM33.6196 45.6454C33.3862 45.3032 32.9196 45.215 32.5774 45.4484C32.2352 45.6818 32.147 46.1485 32.3804 46.4906L33.6196 45.6454ZM52.07 34L52.7576 33.7004C52.6385 33.4271 52.3689 33.2503 52.0708 33.25C51.7727 33.2497 51.5027 33.4259 51.3831 33.699L52.07 34ZM51.32 53.33C51.32 53.7442 51.6558 54.08 52.07 54.08C52.4842 54.08 52.82 53.7442 52.82 53.33H51.32ZM60.1689 43.503C60.5406 43.6858 60.9901 43.5328 61.173 43.1611C61.3558 42.7895 61.2028 42.3399 60.8311 42.1571L60.1689 43.503ZM42.7175 42.1353C42.3338 42.2913 42.1492 42.7288 42.3052 43.1125C42.4613 43.4962 42.8988 43.6808 43.2825 43.5248L42.7175 42.1353ZM45.79 80.056H57.37V78.556H45.79V80.056ZM45.79 87.778H61.24V86.278H45.79V87.778ZM54.7 60V67.722H56.2V60H54.7ZM54.7017 67.7727C54.7818 68.9551 55.2876 70.0685 56.1255 70.9067L57.1863 69.8461C56.6049 69.2645 56.2539 68.4919 56.1983 67.6713L54.7017 67.7727ZM56.1255 70.9067C56.9635 71.7448 58.0767 72.2509 59.2591 72.3313L59.3609 70.8347C58.5403 70.779 57.7678 70.4278 57.1863 69.8461L56.1255 70.9067ZM59.31 72.333H67.03V70.833H59.31V72.333ZM67.03 94H40V95.5H67.03V94ZM40.75 94.75V60H39.25V94.75H40.75ZM40 60.75H57.38V59.25H40V60.75ZM56.8496 60.5302L66.4996 70.1833L67.5604 69.1228L57.9104 59.4698L56.8496 60.5302ZM66.28 69.653V94.753H67.78V69.653H66.28ZM67.78 94.753V94.75H66.28V94.753H67.78ZM8.42353 45.187C9.94083 44.1488 12.8071 42.6484 16.1611 41.8511C19.515 41.0538 23.2769 40.977 26.6646 42.6708L27.3354 41.3292C23.5231 39.423 19.3685 39.5468 15.8142 40.3917C12.2599 41.2367 9.22617 42.8202 7.57647 43.949L8.42353 45.187ZM26.6646 42.6708C30.1935 44.4353 32.1158 46.1323 32.8954 46.9885C33.0929 47.2053 33.2022 47.352 33.2458 47.4206C33.2822 47.4779 33.2406 47.4292 33.2152 47.3225C33.2072 47.2887 33.1916 47.2125 33.1986 47.1129C33.2055 47.015 33.2404 46.8229 33.4044 46.6537C33.5805 46.472 33.7953 46.4287 33.9359 46.4291C34.0628 46.4295 34.1566 46.463 34.1961 46.4787C34.3199 46.528 34.3745 46.5949 34.3193 46.5431C34.2933 46.5187 34.2471 46.4711 34.1801 46.3934C34.0475 46.2393 33.8583 45.9953 33.6196 45.6454L32.3804 46.4906C32.6417 46.8737 32.865 47.1648 33.0433 47.372C33.1318 47.4748 33.2157 47.5644 33.2926 47.6366C33.3403 47.6814 33.4707 47.8045 33.6414 47.8724C33.6926 47.8928 33.7964 47.9287 33.9313 47.9291C34.08 47.9295 34.3011 47.8839 34.4815 47.6977C34.6499 47.524 34.6873 47.3251 34.6949 47.2182C34.7025 47.1096 34.6857 47.0222 34.6744 46.9751C34.6363 46.8151 34.5514 46.6785 34.512 46.6165C34.4041 46.4465 34.2321 46.2284 34.0046 45.9786C33.0842 44.9678 31.0065 43.1647 27.3354 41.3292L26.6646 42.6708ZM51.32 34V53.33H52.82V34H51.32ZM51.3824 34.2996C53.13 38.3104 56.2433 41.5715 60.1689 43.503L60.8311 42.1571C57.2241 40.3823 54.3633 37.3858 52.7576 33.7004L51.3824 34.2996ZM51.3831 33.699C49.7032 37.5321 46.5943 40.5588 42.7175 42.1353L43.2825 43.5248C47.5212 41.8011 50.9203 38.492 52.7569 34.301L51.3831 33.699Z"
                                          fill="white"></path>
                                </g>
                                <defs>
                                    <clipPath id="clip0_3414_6888">
                                        <rect width="110" height="95" fill="white"></rect>
                                    </clipPath>
                                </defs>
                            </svg>
                        </span>
                    </button>
                    <div class="Bulkfile-input">
                        <input type="file" name="file-input" id="file-input" class="file-input__input" accept=".xls">
                        <label class="file-input__label" for="file-input">
                            <i class="cp-upload"></i>&nbsp; <span class="custom-cursor-on-hover">Upload file</span>
                        </label>
                        <p class="mt-2 text-muted ">Supported File Type: XLS Only</p>
                    </div>
                    <div class="row">
                        <div class="col">
                            <div class="text-start ps-1 mb-1">Upload Status</div>
                            <div class="p-2 ps-3 pe-3 mb-0 rounded-4 border  border-secondary-subtle card">
                                <div class="d-flex">
                                    <div class="p-2 w-100 text-start text-muted">
                                        <span id="fileName">File Name</span>
                                    </div>
                                </div>
                                <div>
                                    <div class="d-flex">
                                        <div class="p-2 pb-1 w-100">
                                            <div class="progress" style="height: 4px;">
                                                <div role="progressbar" class="progress-bar" id="dynamicProgressBar"
                                                     aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"
                                                     style="width: 0%;" aria-label="File upload progress">
                                                    <span class="visually-hidden">60%</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="p-2 pt-0 pb-1 flex-shrink-1" id="percentageProgressBar">0%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- offcanvas -->
    <div class="modal fade" tabindex="-1" id="offcanvasTop" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="body-header-text" id="myLargeModalLabel">
                        <i class="cp-bulk-import me-1"></i> <span>Bulk Import - Validation</span>
                    </h6>
                    <div class="d-flex gap-2 align-items-center">
                        <div class="list-group list-group-horizontal">
                            <div class="border-0 fw-bold pt-0 pb-0 ps-2 pe-2 list-group-item">
                                <span class="me-2">Total InfraObject</span><span id="totalInfraObject" class="align-middle badge bg-primary">0</span>
                            </div>
                            <div class="border-0 fw-bold pt-0 pb-0 ps-2 pe-2 list-group-item">
                                <span class="me-2">Success</span><span id="successInfraObject" class="align-middle bg-success-subtle text-success badge">0</span>
                            </div>
                            <div class="border-0 fw-bold pt-0 pb-0 ps-2 pe-2 list-group-item">
                                <span class="me-2">Failure</span><span id="failureInfraObject" class="align-middle bg-danger-subtle text-danger badge">0</span>
                            </div>
                        </div>
                        <button type="button" id="closeOffCanvas" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
                    </div>
                </div>
                <div class="modal-body">
                    <div style="height: calc(100vh - 165px); overflow: auto;">
                        <table class="table table-bordered text-start">
                            <thead class="align-middle">
                                <tr>
                                    <th rowspan="3" class="text-center">
                                        <input class="form-check-input mx-0" type="checkbox" value="" id="flexCheckDefault">
                                    </th>
                                    <th rowspan="3">Sr.No</th>
                                    <th rowspan="3">InfraObject&nbsp;Name</th>
                                    <th rowspan="3">Server</th>
                                    <th rowspan="3">Database</th>
                                    <th rowspan="3">Replication</th>
                                    <th rowspan="3">InfraObject</th>
                                </tr>
                                <tr>
                                    <th colspan="4">Workflow</th>
                                    <th rowspan="3">Action</th>
                                </tr>
                                <tr>
                                    <th>SO</th>
                                    <th>SB</th>
                                    <th>FO </th>
                                    <th>FB </th>
                                </tr>
                            </thead>
                            <tbody id="validationLists">
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="confirmSave" type="button" class="btn btn-primary btn-sm">
                        Save
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- offcanvas -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="" aria-labelledby="offcanvasTopLabel"
         style="width: 100%;">
        <div class="offcanvas-header">
            <div class="">
                <h6 class="body-header-text" id="myLargeModalLabel" title="Bulk Import">
                    <i class="cp-bulk-import"></i>
                    <span>Bulk Import</span>
                </h6>
            </div>
            <div>
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="list-group list-group-horizontal">
                            <div class="border-0 fw-bold pt-0 pb-0 ps-2 pe-2 list-group-item">
                                All&nbsp;&nbsp;<span class="align-middle badge rounded-pill bg-primary">12</span>
                            </div>
                            <div class="border-0 fw-bold pt-0 pb-0 ps-2 pe-2 list-group-item">
                                Success&nbsp;&nbsp;<span class="align-middle bg-success-subtle text-success badge rounded-pill bg-primary">0</span>
                            </div>
                            <div class="border-0 fw-bold pt-0 pb-0 ps-2 pe-2 list-group-item">
                                Failure&nbsp;&nbsp;<span class="align-middle bg-warning-subtle text-warning badge rounded-pill bg-primary">12</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="">
                <div class="d-flex gap-2 align-items-center">
                    <div>
                        <button type="button" title="Delete" class="me-2 btn btn-danger btn-size">
                            Delete
                        </button>
                        <button type="button" title="Save" class="btn btn-primary btn-size">
                            Save
                        </button>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close" title="Close"></button>
                </div>
            </div>
        </div>
        <div class="offcanvas-body">
            <div style="height: calc(100vh - 100px); overflow: scroll;">
                <table class="table">
                    <thead class="table-info position-sticky top-0">
                        <tr>
                            <th>
                                <label class="customcheckbox ms-3">
                                    <input type="checkbox" id="mainCheckbox"><span class="checkmark"></span>
                                </label>
                            </th>
                            <th>Name</th>
                            <th>Type</th>
                            <th>SID</th>
                            <th>Version</th>
                            <th>Database Type</th>
                            <th>Database Type</th>
                            <th>Database Type</th>
                            <th>Database Type</th>
                            <th>Database Type</th>
                            <th>Database Type</th>
                        </tr>
                    </thead>
                    <tbody id="tablebody">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!--Modal Error-->
    <div class="modal fade" id="ErrorModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-md modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-error-message"></i><span>Error Message</span></h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="list-group list-group-flush Profile-Select">
                        <div class="d-grid">
                            <span>
                                Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
                            </span>
                            <div class="text-center">
                                <span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Server Modal -->
    <div class="modal fade" id="ServerModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-server"></i><span>Server Details</span></h6>
                    <button type="button" class="btn-close" data-bs-toggle="modal" data-bs-target="#offcanvasTop" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="card Card_Design_None border">
                                <div class="card-header card-title">
                                    PR
                                </div>
                                <div class="card-body pt-0">
                                    <div class="form-group">
                                        <div class="form-label">Name</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-name"></i></span>
                                            <input type="text" id="PRServerName" class="form-control" placeholder="Enter Server Name">
                                        </div>
                                        <span id="PRServerNameError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Site Name</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-web"></i></span>
                                            <input type="text" disabled id="PRSiteName" class="form-control" placeholder="Enter Site Name">
                                        </div>
                                        <span id="PRServerSiteNameError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Operational Service</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-business-service"></i></span>
                                            <input type="text" disabled id="PROperationalService" class="form-control" placeholder="Enter Operational Service Name">
                                        </div>
                                        <span id="PRServerOperationalServiceError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Server Role</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-server-role"></i></span>
                                            <input type="text" disabled id="PRServerRole" class="form-control" placeholder="Enter Server Role">
                                        </div>
                                        <span id="PRServerServerRoleError"></span>
                                    </div>
                                    <div class="form-group ">
                                        <div class="form-label">Server Type</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-production-server-name"></i></span>
                                            <input type="text" disabled id="PRServerType" class="form-control" placeholder="Enter Server Type">
                                        </div>
                                        <span id="PRServerServerTypeError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">OS Type</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-os-type" id="osTypeIcon"></i></span>
                                            <input type="text" disabled id="PROSType" class="form-control" placeholder="Enter OS Type">
                                        </div>
                                        <span id="PRServerOSTypeError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Version</div>
                                        <div class="input-group">
                                            <span class=" input-group-text"><i class="cp-version"></i></span>
                                            <input type="text" disabled id="PRVersion" class="form-control" placeholder="Enter Version">
                                        </div>
                                        <span id="PRServerVersionError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label"> License Key</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-license-key"></i></span>
                                            <input type="text" disabled id="PRLicense" class="form-control" placeholder="Enter License Key">
                                        </div>
                                        <span id="PRServerLicenseKeyError"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card Card_Design_None border">
                                <div class="card-header card-title">
                                    DR
                                </div>
                                <div class="card-body pt-0">
                                    <div class="form-group">
                                        <div class="form-label">Name</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-name"></i></span>
                                            <input type="text" id="DRServerName" class="form-control" placeholder="Enter Server Name">
                                        </div>
                                        <span id="DRServerNameError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Site Name</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-web"></i></span>
                                            <input type="text" disabled id="DRSiteName" class="form-control" placeholder="Enter Site Name">
                                        </div>
                                        <span id="DRServerSiteNameError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Operational Service</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-business-service"></i></span>
                                            <input type="text" disabled id="DROperationalService" class="form-control" placeholder="Enter Operational Service Name">
                                        </div>
                                        <span id="DRServerOperationalServiceError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Server Role</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-server-role"></i></span>
                                            <input type="text" disabled id="DRServerRole" class="form-control" placeholder="Enter Server Role">
                                        </div>
                                        <span id="DRServerServerRoleError"></span>
                                    </div>
                                    <div class="form-group ">
                                        <div class="form-label">Server Type</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-production-server-name"></i></span>
                                            <input type="text" disabled id="DRServerType" class="form-control" placeholder="Enter Server Type">
                                        </div>
                                        <span id="DRServerServerTypeError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">OS Type</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-os-type" id="osTypeIcon"></i></span>
                                            <input type="text" disabled id="DROSType" class="form-control" placeholder="Enter OS Type">
                                        </div>
                                        <span id="DRServerOSTypeError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Version</div>
                                        <div class="input-group">
                                            <span class=" input-group-text"><i class="cp-version"></i></span>
                                            <input type="text" disabled id="DRVersion" class="form-control" placeholder="Enter Version">
                                        </div>
                                        <span id="DRServerVersionError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label"> License Key</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-license-key"></i></span>
                                            <input type="text" disabled id="DRLicense" class="form-control" placeholder="Enter License Key">
                                        </div>
                                        <span id="DRServerLicenseKeyError"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" data-bs-toggle="modal" data-bs-target="#offcanvasTop" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveServerList">Save</button>
                </div>
            </div>
        </div>
    </div>
    <!-- Database Modal -->
    <div class="modal fade" id="DatabaseModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-server"></i><span>Database Details</span></h6>
                    <button type="button" data-bs-toggle="modal" data-bs-target="#offcanvasTop" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="card Card_Design_None border">
                                <div class="card-header card-title">
                                    PR
                                </div>
                                <div class="card-body pt-0">
                                    <div class="form-group">
                                        <div class="form-label">Name</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-name"></i></span>
                                            <input type="text" id="PRDatabaseName" class="form-control" placeholder="Enter Database Name">
                                        </div>
                                        <span id="PRDatabaseNameError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Database Type</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-database-type" id="databaseTypeIcon"></i></span>
                                            <input type="text" disabled id="PRDatabaseType" class="form-control" placeholder="Enter Database Type">
                                        </div>
                                        <span id="PRDatabaseTypeError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Operational Service</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-business-service"></i></span>
                                            <input type="text" disabled id="PRDBOperational" class="form-control" placeholder="Enter Operational Service">
                                        </div>
                                        <span id="PRDatabaseOperationalServiceError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Version</div>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="cp-version"></i>
                                            </span>
                                            <input type="text" disabled id="PRDBVersion" class="form-control" placeholder="Enter Version">
                                        </div>
                                        <span id="PRDatabaseVersionError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Server</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-server"></i></span>
                                            <input type="text" disabled id="PRDBServer" class="form-control" placeholder="Enter Server Name">
                                        </div>
                                        <span id="PRDatabaseServerError"></span>
                                    </div>
                                    <div class="form-group ">
                                        <div class="form-label"> License Key</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-license-key"></i></span>
                                            <input type="text" disabled id="PRDBLicense" class="form-control" placeholder="Enter License Key">
                                        </div>
                                        <span id="PRDatabaseLicenseKeyError"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card Card_Design_None border">
                                <div class="card-header card-title">
                                    DR
                                </div>
                                <div class="card-body pt-0">
                                    <div class="form-group">
                                        <div class="form-label">Name</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-name"></i></span>
                                            <input type="text" id="DRDatabaseName" class="form-control" placeholder="Enter Database Name">
                                        </div>
                                        <span id="DRDatabaseNameError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Database Type</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-database-type" id="databaseTypeIcon"></i></span>
                                            <input type="text" disabled id="DRDatabaseType" class="form-control" placeholder="Enter Database Type">
                                        </div>
                                        <span id="DRDatabaseTypeError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Operational Service</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-business-service"></i></span>
                                            <input type="text" disabled id="DRDBOperational" class="form-control" placeholder="Enter Operational Service">
                                        </div>
                                        <span id="DRDatabaseOperationalServiceError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Version</div>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="cp-version"></i>
                                            </span>
                                            <input type="text" disabled id="DRDBVersion" class="form-control" placeholder="Enter Version">
                                        </div>
                                        <span id="DRDatabaseVersionError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Server</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-server"></i></span>
                                            <input type="text" disabled id="DRDBServer" class="form-control" placeholder="Enter Server Name">
                                        </div>
                                        <span id="DRDatabaseServerError"></span>
                                    </div>
                                    <div class="form-group ">
                                        <div class="form-label"> License Key</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-license-key"></i></span>
                                            <input type="text" disabled id="DRDBLicense" class="form-control" placeholder="Enter License Key">
                                        </div>
                                        <span id="DRDatabaseLicenseKeyError"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" data-bs-toggle="modal" data-bs-target="#offcanvasTop" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveDatabaseList">Save</button>
                </div>
            </div>
        </div>
    </div>
    <!-- Replication Modal -->
    <div class="modal fade" id="ReplicationModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-server"></i><span>Replication Details</span></h6>
                    <button type="button" data-bs-toggle="modal" data-bs-target="#offcanvasTop" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="card Card_Design_None border">
                                <div class="card-header card-title">
                                    PR
                                </div>
                                <div class="card-body pt-0">
                                    <div class="form-group">
                                        <div class="form-label">Name</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-name"></i></span>
                                            <input id="PRReplicationName" type="text" class="form-control" placeholder="Enter Replication Name">
                                        </div>
                                        <span id="PRReplicationNameError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Site Name</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-web"></i></span>
                                            <input disabled id="PRReplicationSite" type="text" class="form-control" placeholder="Enter Site Name">
                                        </div>
                                        <span id="PRReplicationSiteNameError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Operational Service</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-business-service"></i></span>
                                            <input disabled id="PRReplicationOperational" type="text" class="form-control" placeholder="Enter Operational Service">
                                        </div>
                                        <span id="PRReplicationOperationalServiceError"></span>
                                    </div>

                                    <div class="form-group">
                                        <div class="form-label">Replication Type</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-replication-type" id="replicationTypeIcon"></i></span>
                                            <input disabled id="PRReplicationType" type="text" class="form-control" placeholder="Enter Replication Type">
                                        </div>
                                        <span id="PRReplicationReplicationTypeError"></span>
                                    </div>

                                    <div class="form-group ">
                                        <div class="form-label"> License Key</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-license-key"></i></span>
                                            <input disabled id="PRReplicationLicense" type="text" class="form-control" placeholder="Enter License Key">
                                        </div>
                                        <span id="PRReplicationLicenseKeyError"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card Card_Design_None border">
                                <div class="card-header card-title">
                                    DR
                                </div>
                                <div class="card-body pt-0">
                                    <div class="form-group">
                                        <div class="form-label">Name</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-name"></i></span>
                                            <input id="DRReplicationName" type="text" class="form-control" placeholder="Enter Replication Name">
                                        </div>
                                        <span id="DRReplicationNameError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Site Name</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-web"></i></span>
                                            <input disabled id="DRReplicationSite" type="text" class="form-control" placeholder="Enter Site Name">
                                        </div>
                                        <span id="DRReplicationSiteNameError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Operational Service</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-business-service"></i></span>
                                            <input disabled id="DRReplicationOperational" type="text" class="form-control" placeholder="Enter Operational Service">
                                        </div>
                                        <span id="DRReplicationOperationalServiceError"></span>
                                    </div>

                                    <div class="form-group">
                                        <div class="form-label">Replication Type</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-replication-type" id="replicationTypeIcon"></i></span>
                                            <input disabled id="DRReplicationType" type="text" class="form-control" placeholder="Enter Replication Type">
                                        </div>
                                        <span id="DRReplicationReplicationTypeError"></span>
                                    </div>

                                    <div class="form-group ">
                                        <div class="form-label"> License Key</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-license-key"></i></span>
                                            <input disabled id="DRReplicationLicense" type="text" class="form-control" placeholder="Enter License Key">
                                        </div>
                                        <span id="DRReplicationLicenseKeyError"></span>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" data-bs-toggle="modal" data-bs-target="#offcanvasTop" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveReplicationList">Save</button>
                </div>
            </div>
        </div>
    </div>
    <!-- InfraObject Modal -->
    <div class="modal fade" id="InfraObjectModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-server"></i><span>InfraObject Details</span></h6>
                    <button type="button" data-bs-toggle="modal" data-bs-target="#offcanvasTop" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="card Card_Design_None border">
                                <div class="card-body pt-0">
                                    <div class="form-group">
                                        <div class="form-label">Name</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-name"></i></span>
                                            <input id="InfraObjectName" type="text" class="form-control" placeholder="Enter InfraObject Name">
                                        </div>
                                        <span id="InfraObjectNameError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Description</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-description"></i></span>
                                            <input disabled id="InfraObjectDescription" type="text" class="form-control" placeholder="Enter Description">
                                        </div>
                                        <span id="InfraObjectDescriptionError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Operational Service</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-business-service"></i></span>
                                            <input disabled id="InfraObjectOperationalService" type="text" class="form-control" placeholder="Enter Operational Service">
                                        </div>
                                        <span id="InfraObjectOperationalServiceError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Operational Function</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-business-function"></i></span>
                                            <input disabled id="InfraObjectOperationalFunction" type="text" class="form-control" placeholder="Enter Operational Function">
                                        </div>
                                        <span id="InfraObjectOperationalFunctionError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Replication Category</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-replication-on"></i></span>
                                            <input disabled id="InfraObjectReplicationCategory" type="text" class="form-control" placeholder="Enter Replication Category">
                                        </div>
                                        <span id="InfraObjectReplicationCategoryError"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-label">Replication Type</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-replication-rotate"></i></span>
                                            <input disabled id="InfraObjectReplicationType" type="text" class="form-control" placeholder="Enter Replication Type">
                                        </div>
                                        <span id="InfraObjectReplicationTypeError"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" data-bs-toggle="modal" data-bs-target="#offcanvasTop" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveInfraObjectList">Save</button>
                </div>
            </div>
        </div>
    </div>
    <!--Row Delete-->
    <div class="modal fade" id="DeleteRow" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <partial name="Delete" />
    </div>
    <!-- InsetDB Modal -->
    <div class="modal fade" id="InsetDBModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title" style="width: 25.5%;"><i class="cp-bulk-import"></i><span>Bulk Import - Save Components</span></h6>
                    <button type="button" id="closeInsertDBModal" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-8">
                            <div class="card Card_Design_None border">
                                <div class="card-header d-flex  align-items-center">
                                    <span class="card-title" style="width: 29.5%;">InfraObject Name</span>
                                    <span class="card-title" style="width: 29.5%;">Status</span>
                                    <span class="card-title" style="width: 17.5%;">Total InfraObject : <span id="totalInfra">0</span></span>
                                    <span class="card-title" style="width: 13.5%;">Success : <span id="successInfra">0</span></span>
                                    <span class="card-title">Error : <span id="errorInfra">0</span></span>
                                </div>
                                <div style="height: calc(100vh - 225px);overflow:auto;">
                                    <ul class="list-group list-group-flush Profile-Select" id="bulkImportLists">
                                    </ul>                                    
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="card Card_Design_None border">
                                <div class="card-header card-title">Time Line</div>
                                <div class="card-body p-2" style="height: calc(100vh - 225px);overflow:auto;">
                                    <div class="User_Profile_Timeline">
                                        <div class="user-profile-timeline-container">
                                            <ul class="tl m-0 ms-2" id="bulkImportTimeLine">
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="min-height: 50px;">
                    <div style="display:flex;justify-content:flex-end;padding:1px">
                        <button type="button" id="insertBulkImport" class="me-3 btn btn-primary btn-sm">
                            Start
                        </button>
                        <button type="button" id="CompleteBulkImport" class="me-3 btn btn-primary btn-sm d-none">
                            Completed
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="DeleteBulkRow" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="~/img/isomatric/delete.png" alt="Delete Img" />
            </div>
            <div class="modal-body text-center pt-0">
                <h5 class="fw-bold">Are you sure?</h5>
                <p class="d-flex align-items-center justify-content-center gap-1">You want to delete the selected data?</p>
            </div>
            <input type="hidden" id="bulkImportIndex" />
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" data-bs-dismiss="modal" id="confirmBulkDeleteButton">Yes</button>
            </div>
        </div>
    </div>
</div>
@* Add Description *@
<div class="modal fade" id="addDescription" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header pt-3">
                <h6 class="page_title">Add Description</h6>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <div class="form-label">Description</div>
                    <div class="input-group">
                        <span class=" input-group-text"><i class="cp-description"></i></span>
                        <input id="bulkimportDescription" autocomplete="off" maxlength="250" type="text" class="form-control" placeholder="Enter Description">
                    </div>
                    <span id="Description-Error"></span>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-end">
                <button type="button" class="btn btn-secondary btn-sm btn-cancel me-2" id="closeDescription" data-bs-toggle="modal" data-bs-target="#offcanvasTop" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary finish_btn btn-sm" id="insertToDB">Save</button>
            </div>
        </div>
    </div>
</div>
<script src="~/js/configuration/bulk import/bulkimport.js"></script>
<script src="~/lib/exceltojson/exceltojson.js"></script>
<script src="~/js/Configuration/Bulk Import/bulkimportexceltojson.js"></script>