﻿using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.MSSQLAlwaysOnMonitorLogs.Queries;

public class GetMssqlAlwaysOnMonitorLogsDetailQueryHandlerTests : IClassFixture<MssqlAlwaysOnMonitorLogsFixture>
{
    private readonly MssqlAlwaysOnMonitorLogsFixture _mssqlAlwaysOnMonitorLogsFixture;

    private readonly Mock<IMssqlAlwaysOnMonitorLogsRepository> _mockMssqlAlwaysOnMonitorLogsRepository;

    private readonly GetMSSQLAlwaysOnMonitorLogsDetailQueryHandler _handler;

    public GetMssqlAlwaysOnMonitorLogsDetailQueryHandlerTests(
        MssqlAlwaysOnMonitorLogsFixture mssqlAlwaysOnMonitorLogsFixture)
    {
        _mssqlAlwaysOnMonitorLogsFixture = mssqlAlwaysOnMonitorLogsFixture;

        _mockMssqlAlwaysOnMonitorLogsRepository =
            MssqlAlwaysOnMonitorLogsRepositoryMocks.GetMssqlAlwaysOnMonitorLogsRepository(
                _mssqlAlwaysOnMonitorLogsFixture.MssqlAlwaysOnMonitorLogs);

        _handler = new GetMSSQLAlwaysOnMonitorLogsDetailQueryHandler(_mockMssqlAlwaysOnMonitorLogsRepository.Object,
            _mssqlAlwaysOnMonitorLogsFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_InfraObjectInfoDetails_When_ValidInfraObjectInfoId()
    {
        var result =
            await _handler.Handle(new GetMSSQLAlwaysOnMonitorLogsDetailQuery { Id = _mssqlAlwaysOnMonitorLogsFixture.MssqlAlwaysOnMonitorLogs[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<MSSQLAlwaysOnMonitorLogsDetailVm>();

        result.Id.ShouldBe(_mssqlAlwaysOnMonitorLogsFixture.MssqlAlwaysOnMonitorLogs[0].ReferenceId);
        result.Type.ShouldBe(_mssqlAlwaysOnMonitorLogsFixture.MssqlAlwaysOnMonitorLogs[0].Type);
        result.InfraObjectId.ShouldBe(_mssqlAlwaysOnMonitorLogsFixture.MssqlAlwaysOnMonitorLogs[0].InfraObjectId);
        result.InfraObjectName.ShouldBe(
            _mssqlAlwaysOnMonitorLogsFixture.MssqlAlwaysOnMonitorLogs[0].InfraObjectName);
        result.WorkflowId.ShouldBe(_mssqlAlwaysOnMonitorLogsFixture.MssqlAlwaysOnMonitorLogs[0].WorkflowId);
        result.WorkflowName.ShouldBe(_mssqlAlwaysOnMonitorLogsFixture.MssqlAlwaysOnMonitorLogs[0].WorkflowName);
        result.Properties.ShouldBe(_mssqlAlwaysOnMonitorLogsFixture.MssqlAlwaysOnMonitorLogs[0].Properties);
        result.ConfiguredRPO.ShouldBe(_mssqlAlwaysOnMonitorLogsFixture.MssqlAlwaysOnMonitorLogs[0].ConfiguredRPO);
        result.DataLagValue.ShouldBe(_mssqlAlwaysOnMonitorLogsFixture.MssqlAlwaysOnMonitorLogs[0].DataLagValue);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidInfraObjectInfoId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetMSSQLAlwaysOnMonitorLogsDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetMSSQLAlwaysOnMonitorLogsDetailQuery { Id = _mssqlAlwaysOnMonitorLogsFixture.MssqlAlwaysOnMonitorLogs[0].ReferenceId }, CancellationToken.None);

        _mockMssqlAlwaysOnMonitorLogsRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}