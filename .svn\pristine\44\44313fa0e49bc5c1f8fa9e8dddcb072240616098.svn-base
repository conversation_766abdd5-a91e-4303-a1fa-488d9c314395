namespace ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetDetail;

public class CyberSnapsDetailVm
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string Tag { get; set; }
    public string Type { get; set; }
    public string Location { get; set; }
    public int Age { get; set; }
    public int Size { get; set; }
    public string SID { get; set; }
    public string TimeStamp { get; set; }
    public string StorageGroupName { get; set; }
    public string SnapshotName { get; set; }
    public string LinkSG { get; set; }
    public string LinkedSGTime { get; set; }
    public string UnlinkSGTime { get; set; }
    public string Remark { get; set; }
    public string LinkedStatus { get; set; }
    public string AvailableStatus { get; set; }
    public string SecureStatus { get; set; }
    public string Gen { get; set; }


}