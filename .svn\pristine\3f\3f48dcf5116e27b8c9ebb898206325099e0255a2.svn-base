using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.CyberSnapsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetPaginatedList;

public class
    GetCyberSnapsPaginatedListQueryHandler : IRequestHandler<GetCyberSnapsPaginatedListQuery,
        PaginatedResult<CyberSnapsListVm>>
{
    private readonly ICyberSnapsRepository _cyberSnapsRepository;
    private readonly IMapper _mapper;

    public GetCyberSnapsPaginatedListQueryHandler(IMapper mapper, ICyberSnapsRepository cyberSnapsRepository)
    {
        _mapper = mapper;
        _cyberSnapsRepository = cyberSnapsRepository;
    }

    public async Task<PaginatedResult<CyberSnapsListVm>> Handle(GetCyberSnapsPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new CyberSnapsFilterSpecification(request.SearchString);
       
        if (request.StartDate != DateTime.MinValue && request.EndDate != DateTime.MinValue)
        {
            var queryable =
             await _cyberSnapsRepository.GetCyberSnapsByDateTime(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder, request.StartDate, request.EndDate);
            return queryable;
        }
        else
        {
            var query = await _cyberSnapsRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

            var cyberSnapsList = _mapper.Map<PaginatedResult<CyberSnapsListVm>>(query);

            return cyberSnapsList;
        }
        //var queryable = _cyberSnapsRepository.GetPaginatedQuery();

        //var productFilterSpec = new CyberSnapsFilterSpecification(request.SearchString);

        //var cyberSnapsList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<CyberSnapsListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return cyberSnapsList;
    }
}