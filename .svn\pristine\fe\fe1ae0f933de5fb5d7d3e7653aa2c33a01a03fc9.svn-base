using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperationGroup.Events;

public class CreateBulkImportOperationGroupEventTests : IClassFixture<BulkImportOperationGroupFixture>, IClassFixture<UserActivityFixture>
{
    private readonly BulkImportOperationGroupFixture _bulkImportOperationGroupFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly BulkImportOperationGroupCreatedEventHandler _handler;
    
    public CreateBulkImportOperationGroupEventTests(BulkImportOperationGroupFixture bulkImportOperationGroupFixture, UserActivityFixture userActivityFixture)
    {
        _bulkImportOperationGroupFixture = bulkImportOperationGroupFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");
        mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/bulkimportoperationgroup");
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        var mockBulkImportOperationGroupEventLogger = new Mock<ILogger<BulkImportOperationGroupCreatedEventHandler>>();

        _mockUserActivityRepository = BulkImportOperationGroupRepositoryMocks.CreateBulkImportOperationGroupEventRepository(_userActivityFixture.UserActivities);

        _handler = new BulkImportOperationGroupCreatedEventHandler(
            mockLoggedInUserService.Object, 
            mockBulkImportOperationGroupEventLogger.Object, 
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateBulkImportOperationGroupEventCreated()
    {
        // Arrange
        _userActivityFixture.UserActivities[0].LoginName = "Tester";
        var bulkImportOperationGroupCreatedEvent = new BulkImportOperationGroupCreatedEvent { Name = "TestInfraObject" };

        // Act
        var result = _handler.Handle(bulkImportOperationGroupCreatedEvent, CancellationToken.None);

        // Assert
        Assert.True(result.IsCompleted);
        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var bulkImportOperationGroupCreatedEvent = new BulkImportOperationGroupCreatedEvent { Name = "TestInfraObject" };

        // Act
        await _handler.Handle(bulkImportOperationGroupCreatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_WithCorrectProperties()
    {
        // Arrange
        var bulkImportOperationGroupCreatedEvent = new BulkImportOperationGroupCreatedEvent { Name = "TestInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationGroupCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.LoginName.ShouldBe("Tester");
        capturedUserActivity.Action.ShouldContain("Create BulkImportOperationGroup");
        capturedUserActivity.Entity.ShouldBe("BulkImportOperationGroup");
        capturedUserActivity.ActivityType.ShouldBe("Create");
        capturedUserActivity.ActivityDetails.ShouldContain("TestInfraObject");
        capturedUserActivity.ActivityDetails.ShouldContain("created successfully");
    }

    [Fact]
    public async Task Handle_LogCorrectInformation_When_BulkImportOperationGroupCreated()
    {
        // Arrange
        var bulkImportOperationGroupCreatedEvent = new BulkImportOperationGroupCreatedEvent { Name = "TestInfraObject" };
        var mockLogger = new Mock<ILogger<BulkImportOperationGroupCreatedEventHandler>>();

        var handler = new BulkImportOperationGroupCreatedEventHandler(
            new Mock<ILoggedInUserService>().Object,
            mockLogger.Object,
            _mockUserActivityRepository.Object);

        // Act
        await handler.Handle(bulkImportOperationGroupCreatedEvent, CancellationToken.None);

        // Assert
        //mockLogger.Verify(
        //    x => x.Log(
        //        LogLevel.Information,
        //        It.IsAny<EventId>(),
        //        It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("TestInfraObject") && v.ToString().Contains("created successfully")),
        //        It.IsAny<Exception>(),
        //        It.IsAny<Func<It.IsAnyType, Exception, string>>()),
        //    Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectUserActivityProperties_When_UserServiceHasData()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var testUrl = "/api/test";
        var testIpAddress = "***********";
        var testLoginName = "TestUser";

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);
        mockLoggedInUserService.Setup(x => x.LoginName).Returns(testLoginName);
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(testUrl);
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns(testIpAddress);

        var handler = new BulkImportOperationGroupCreatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationGroupCreatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationGroupCreatedEvent = new BulkImportOperationGroupCreatedEvent { Name = "TestInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationGroupCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.UserId.ShouldBe(testUserId);
        capturedUserActivity.LoginName.ShouldBe(testLoginName);
        capturedUserActivity.RequestUrl.ShouldBe(testUrl);
        capturedUserActivity.HostAddress.ShouldBe(testIpAddress);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_EventHandled()
    {
        // Arrange
        var bulkImportOperationGroupCreatedEvent = new BulkImportOperationGroupCreatedEvent { Name = "ProductionInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationGroupCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.Action.ShouldBe("Create BulkImportOperationGroup");
        capturedUserActivity.Entity.ShouldBe("BulkImportOperationGroup");
        capturedUserActivity.ActivityType.ShouldBe("Create");
        capturedUserActivity.ActivityDetails.ShouldBe("BulkImportOperationGroup 'ProductionInfraObject' created successfully.");
    }

    [Fact]
    public async Task Handle_SetCreatedByAndLastModifiedBy_When_UserIdExists()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);

        var handler = new BulkImportOperationGroupCreatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationGroupCreatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationGroupCreatedEvent = new BulkImportOperationGroupCreatedEvent { Name = "TestInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationGroupCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.CreatedBy.ShouldBe(testUserId);
        capturedUserActivity.LastModifiedBy.ShouldBe(testUserId);
    }

    [Fact]
    public async Task Handle_SetDefaultGuidForCreatedBy_When_UserIdIsEmpty()
    {
        // Arrange
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns("");

        var handler = new BulkImportOperationGroupCreatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationGroupCreatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationGroupCreatedEvent = new BulkImportOperationGroupCreatedEvent { Name = "TestInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationGroupCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.CreatedBy.ShouldNotBeNullOrEmpty();
        capturedUserActivity.LastModifiedBy.ShouldNotBeNullOrEmpty();
        Guid.TryParse(capturedUserActivity.CreatedBy, out _).ShouldBeTrue();
        Guid.TryParse(capturedUserActivity.LastModifiedBy, out _).ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_EventProcessed()
    {
        // Arrange
        var bulkImportOperationGroupCreatedEvent = new BulkImportOperationGroupCreatedEvent { Name = null };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationGroupCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.ActivityDetails.ShouldContain("created successfully");
    }
}
