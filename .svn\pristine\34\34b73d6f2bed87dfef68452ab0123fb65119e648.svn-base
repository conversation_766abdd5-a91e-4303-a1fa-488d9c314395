﻿using ContinuityPatrol.Application.Features.WorkflowCategory.Queries.GetWorkflowCategoryViewList;
using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowCategory.Queries
{
    public class GetWorkflowCategoryViewQueryHandlerTests
    {
        private readonly Mock<IWorkflowCategoryViewRepository> _mockWorkflowCategoryViewRepository;

        private readonly WorkflowCategoryViewQueryHandler _handler;

        public GetWorkflowCategoryViewQueryHandlerTests()
        {
            _mockWorkflowCategoryViewRepository = new Mock<IWorkflowCategoryViewRepository>();
            _handler = new WorkflowCategoryViewQueryHandler(_mockWorkflowCategoryViewRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnHierarchicalWorkflowCategoryViewList()
        {
            var rootNodeId = Guid.NewGuid().ToString();
            var child1NodeId = Guid.NewGuid().ToString();
            var child2NodeId = Guid.NewGuid().ToString();
            var subChild1NodeId = Guid.NewGuid().ToString();
            var actionId = Guid.NewGuid().ToString();

            var workflowCategories = new List<WorkflowCategoryView>
            {
                //new WorkflowCategoryView { NodeId = rootNodeId, ReferenceId = Guid.NewGuid().ToString(), Name = "Root1", NodeName = "Root1", Level = 1, Color = "Red", Icon = "icon1" },
                //new WorkflowCategoryView { NodeId = child1NodeId, ReferenceId = Guid.NewGuid().ToString(), Name = "Child1", NodeName = "Child1", ParentTitle = "Root1", Level = 2, Color = "Blue", Icon = "icon2" },
                //new WorkflowCategoryView { NodeId = child2NodeId, ReferenceId = Guid.NewGuid().ToString(), Name = "Child2", NodeName = "Child2", ParentTitle = "Root1", Level = 2, Color = "Green", Icon = "icon3" },
                //new WorkflowCategoryView { NodeId = subChild1NodeId, ReferenceId = Guid.NewGuid().ToString(), Name = "SubChild1", NodeName = "SubChild1", ParentTitle = "Child1", Level = 3, Color = "Yellow", Icon = "icon4", ActionName = "Action1", ActionId = actionId }
            };

            _mockWorkflowCategoryViewRepository.Setup(repo => repo.GetAllWorkflowCategoriesAsync()).ReturnsAsync(workflowCategories);

            var query = new GetWorkflowCategoryViewQuery();
            var cancellationToken = CancellationToken.None;

            var result = await _handler.Handle(query, cancellationToken);

            Assert.NotNull(result);
            Assert.Equal(1, result.Count);

            var root = result.First();
            Assert.Equal("Root1", root.CategoryName);
            Assert.Equal(rootNodeId, root.NodeId);

            Assert.NotNull(root.WorkflowCategoryBaseChildViewListVms);
            Assert.Equal(2, root.WorkflowCategoryBaseChildViewListVms.Count);

            var child1 = root.WorkflowCategoryBaseChildViewListVms.First(x => x.Name == "Child1");
            Assert.NotNull(child1.WorkflowCategoryChildViewListVms);
            Assert.Single(child1.WorkflowCategoryChildViewListVms);

            var subChild1 = child1.WorkflowCategoryChildViewListVms.First();
            Assert.Equal("SubChild1", subChild1.Name);
            Assert.Single(subChild1.ActionLists);

            Assert.Equal("Action1", subChild1.ActionLists.First().ActionName);
            Assert.Equal(actionId, subChild1.ActionLists.First().ActionId);

            _mockWorkflowCategoryViewRepository.Verify(repo => repo.GetAllWorkflowCategoriesAsync(), Times.Once);
        }
    }
}
