using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class CyberJobManagementRepository : BaseRepository<CyberJobManagement>, ICyberJobManagementRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public CyberJobManagementRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public override async Task<IReadOnlyList<CyberJobManagement>> ListAllAsync()
    {
        var cyberJob = base.ListAllAsync(x => x.IsActive);

        var jobs = MapCyberJobManagement(cyberJob);

        return await jobs.ToListAsync();
    }
    public override Task<CyberJobManagement> GetByReferenceIdAsync(string id)
    {
        var cyberJob = base.GetByReferenceIdAsync(id, x =>
                  x.ReferenceId.Equals(id));

        var jobs = MapCyberJobManagement(cyberJob);

        return jobs.FirstOrDefaultAsync();
    }
    public override async Task<PaginatedResult<CyberJobManagement>> PaginatedListAllAsync(int pageNumber,int pageSize,Specification<CyberJobManagement> specification, string sortColumn, string sortOrder)
    {
        var cyberJobs = await MapCyberJobManagement(Entities.Specify(specification).DescOrderById()).ToSortedPaginatedListAsync(pageNumber,pageSize,sortColumn,sortOrder);
         
        return cyberJobs;
    }
    public override IQueryable<CyberJobManagement> PaginatedListAllAsync()
    {
        var cyberJob = base.ListAllAsync(x => x.IsActive);

        var jobs = MapCyberJobManagement(cyberJob);

        return jobs.AsNoTracking().OrderByDescending(x => x.Id);
    }
    public async Task<List<CyberJobManagement>> GetCyberJobByAirGapId(string id)
    {
        var cyberJob = base.FilterBy(x => x.AirgapId.Equals(id));

        var jobs = MapCyberJobManagement(cyberJob);

        return await jobs.ToListAsync();
    }
    public async Task<List<CyberJobManagement>> GetCyberJobByWorkflowId(string id)
    {
        var cyberJob = base.FilterBy(x => x.WorkflowId.Equals(id));

        var jobs = MapCyberJobManagement(cyberJob);

        return await jobs.ToListAsync();
    }
    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.Name.Equals(name))
            : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }
    private IQueryable<CyberJobManagement> MapCyberJobManagement(IQueryable<CyberJobManagement> cyberJobs)
    {
        return cyberJobs.Select(x => new
        {
            AirGap = _dbContext.CyberAirGaps.FirstOrDefault(a => a.ReferenceId.Equals(x.AirgapId)),
            Workflow = _dbContext.Workflows.FirstOrDefault(w => w.ReferenceId.Equals(x.WorkflowId)),
            CyberJob = x
        })
        .Select(res => new CyberJobManagement
        {
            Id = res.CyberJob.Id,
            ReferenceId = res.CyberJob.ReferenceId,
            Name = res.CyberJob.Name,
            AirgapId = res.AirGap.ReferenceId ?? res.CyberJob.AirgapId,
            AirgapName = res.AirGap.Name ?? res.CyberJob.AirgapName,
            WorkflowId = res.Workflow.ReferenceId ?? res.CyberJob.WorkflowId,
            WorkflowName = res.Workflow.Name ?? res.CyberJob.WorkflowName,
            SolutionId = res.CyberJob.SolutionId,
            SolutionName = res.CyberJob.SolutionName,
            IsSchedule = res.CyberJob.IsSchedule,
            ScheduleType = res.CyberJob.ScheduleType,
            ScheduleTime = res.CyberJob.ScheduleTime,
            CronExpression = res.CyberJob.CronExpression,
            Status = res.CyberJob.Status,
            State = res.CyberJob.State,
            //Mode=res.CyberJob.Mode,
            NodeId = res.CyberJob.NodeId,
            NodeName = res.CyberJob.NodeName,
            ExceptionMessage = res.CyberJob.ExceptionMessage,
            IsActive = res.CyberJob.IsActive,
            CreatedBy = res.CyberJob.CreatedBy,
            CreatedDate = res.CyberJob.CreatedDate,
            LastModifiedBy = res.CyberJob.LastModifiedBy,
            LastModifiedDate = res.CyberJob.LastModifiedDate,
            LastExecutedTime = res.CyberJob.LastExecutedTime
        });

    }

}
