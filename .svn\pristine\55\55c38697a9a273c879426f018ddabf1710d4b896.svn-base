//GetSolutionMappingList()
GetPageBuilderList();

GetDynamicDashboardList()


async function GetDynamicDashboardList() {

    await $.ajax({
        type: "GET",
        url: RootUrl + "Dashboard/ServiceAvailability/GetDynamicDashboardList",
        dataType: "json",
        success: function (result) {
            if (result.success) {
                let Html = ""
                result.message.reverse()
                result.message.forEach((data) => {

                    if (data.name == "Custom Dashboard") {
                        sessionStorage.removeItem("CustomDashboardDetails")
                        sessionStorage.setItem("CustomDashboardDetails", JSON.stringify(data))

                    }

                })

            }
            //else {
            //    //$("#dashboard-dropdown").empty();
            //    errorNotification(result)
            //}
        },

    })

}

var createPermission = $("#ConfigurationCreate").data("create-permission").toLowerCase();
var deletePermission = $("#ConfigurationDelete").data("delete-permission").toLowerCase();
if (createPermission == 'false') {
    $(".createbutton").removeClass('.createbutton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
}

let selectedValues = [];
$(document).ready(function () {
    let dataTable = $('#tblSolutionMapping').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }
            },
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            Sortable: true,
            "processing": true,
            "serverSide": false,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/Manage/UserMapping/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                        json.recordsTotal = json?.totalPages;
                        json.recordsFiltered = json?.totalCount;
                        if (json.data.data.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        return json?.data?.data;
                    }
                    else {
                        return ""
                    }

                },
            },
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        return meta.row + 1;
                        return data;
                    },
                },
                {
                    data: "dashBoardSubName",
                    name: 'Page Builder',
                    autoWidth: true,
                    render: function (data, type, row) {
                        return data ? data : "NA";
                    }
                },
                {
                    data: "userName",
                    name: 'Page Builder',
                    autoWidth: true,
                    render: function (data, type, row) {
                        return data ? data : "NA";
                    }
                },
                {
                    data: "roleName",
                    name: 'Page Builder',
                    autoWidth: true,
                    render: function (data, type, row) {
                        return data ? data : "NA";
                    }
                },
                {
                    "render": function (data, type, row) {

                     //   return `<div class="d-flex align-items-center  gap-2">
                     //           <span role="button" title="Edit"  class="edit-button" data-map-id="${row.id}" data-map='${JSON.stringify(row)}'>
                     //               <i class="cp-edit"></i>
                     //           </span>

                     //               <span role="button" title="Delete" class="delete-button" data-map-id="${row.id}" data-map-name="${row.dashBoardSubName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                     //                   <i class="cp-Delete"></i>
                     //               </span>
                     //       </div>
                        //`

                        if (createPermission === 'true' && deletePermission === "true") {
                            const isParent = row.isParent;
                            return `<div class="d-flex align-items-center  gap-2">                                       
                                <span role="button" title="Edit"  class="edit-button"  data-map-id="${row.id}" data-map='${JSON.stringify(row)}'>
                                    <i class="cp-edit"></i>
                                </span>
                                ${isParent ? `
                                    <span title="Delete" style="cursor:not-allowed; opacity:0.50;" class="delete-button ">
                                        <i class="cp-Delete"></i>
                                    </span>` :
                                    `
                                    <span role="button" title="Delete" class="delete-button" data-map-id="${row.id}" data-map-name="${row.dashBoardSubName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                        <i class="cp-Delete"></i>
                                    </span>`
                                }
                            </div>
                     `;
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                       <div class="d-flex align-items-center  gap-2">
                           <span role="button" title="Edit" class="edit-button"  data-map-id="${row.id}" data-map='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>                                                                                       
                                            <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>                                  
                                            
                                </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="delete-button"data-map-id="${row.id}" data-map-name="${row.dashBoardSubName}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }
                        else {
                            return `
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }
                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            },

            "drawCallback": function () {
                const randomColor = () => {
                    return "#" + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0').toUpperCase();
                }

                const namelist = document.querySelectorAll("#companyname");
                namelist.forEach((name) => {
                    name.style.backgroundColor = randomColor();
                });
            }

        });

    $('#search-inp').on('keydown input', function (e) {


        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        } else {
            const inputValue = $('#search-inp').val();
            selectedValues.push(inputValue);
            dataTable.ajax.reload(function (json) {
                $('.dataTables_empty').empty()
                if (json.data.data.length === 0) {
                    $('.dataTables_empty').append(`<tr> <td style="text-align: center;"  >No matching records Found</td><td style="text-align: center;"  ></td></tr>`);
                }
            })
        }


    })

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('.form-select-sm').select2({
        minimumResultsForSearch: Infinity
    });

    $(".createbutton").on('click', function () {
        $('.btn_save').text('Save');
        $('#userMappingType').val("");
        $('#mapUserRole').val("");
        $('#mapUserRole').empty();
        $('#dashboardBuilderDropdown').val("");
        clearSolutionMapping();
    })

    $("#userMappingType").on("change", function () {
        let userMappingType = $("#userMappingType option:selected").val()
        userandroleData(userMappingType, null)
    })
})
function userandroleData(userMappingType, userrole, roleId = null) {
    let url;
    if (userMappingType == "user") {
        url = "Manage/UserMapping/GetUsers"
    }
    else {
        url = "Manage/UserMapping/GetRoles"
    }
    $.ajax({
        url: RootUrl + url,
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (response) {
            $("#mapUserRole").empty()
            if (userMappingType == "user") {
                $("#mapUserRole").append('<option value="">Select Map By Role</option>')
                response.data.forEach((data) => {
                    if (data?.loginName != null) {
                        $("#mapUserRole").append('<option value=' + data.id + '>' + data.loginName + '</option>')
                    }
                })
            }
            else {
                $("#mapUserRole").append('<option value="">Select Map By Role</option>')
                response.data.forEach((data) => {
                    $("#mapUserRole").append('<option value=' + data.id + '>' + data.role + '</option>')
                })
            }
            if (userrole) {
                $("#mapUserRole option[value=" + userrole + "]").attr('selected', 'selected');
            }
            if (roleId) {
                $("#mapUserRole option[value=" + roleId + "]").attr('selected', 'selected');
            }
        },
        error: function (error) {
            console.log(error);
        }
    });
}

function clearSolutionMapping() {
    $('#dashboardBuilderDropdown-error').removeClass("field-validation-error")
    $('#dashboardBuilderDropdown-error').text("")
    $('#userMappingType-error').removeClass("field-validation-error")
    $('#userMappingType-error').text("")
    $('#mapUserRole-error').removeClass("field-validation-error")
    $('#mapUserRole-error').text("")
}

$('#tblSolutionMapping').on('click', '.edit-button', async function () {
    clearSolutionMapping();
    let mapData = $(this).data('map');
    $('.btn_save').text('Update')
    $("#userMappingType").val(mapData.type)
    //$("#userMappingType option[value=" + mapData.type + "]").attr('selected', 'selected');
    GetPageBuilderList(mapData.dashBoardSubId);
    $("#defaultSet").prop("checked", mapData.isDefault)
    $("#viewCheck").prop("checked", mapData.isView)
    userandroleData(mapData.type, mapData.userId, mapData.roleId)
    $(".btn_save").attr("mapDataId", mapData.id)
    $('.btn_save').attr('title', 'Update')
    $('#CreateModal').modal('show');
});

//delete
$('#tblSolutionMapping').on('click', '.delete-button', function () {
    let solutionId = $(this).data('map-id');
    let solutionName = $(this).data('map-name');
    $('#textDeleteId').val(solutionId);
    $('#deleteData').text(solutionName);
});

$('#solutionName').on('keydown keyup', async function () {
    const value = $(this).val();
    let sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validateName(value);
});

$("#pageBuilderDropdown").on("change", () => {
    $("#pageBuilderName-error").removeClass("field-validation-error")
    $("#pageBuilderName-error").text("")
})

$("#Activetype").on("change", () => {
    $("#activityName-error").removeClass("field-validation-error")
    $("#activityName-error").text("")
    let selectedActivity = $("#Activetype option:selected").val()
    if (selectedActivity == "2") {
        $(".databaseType").removeClass("d-none")
        GetDatabaseLisList()
    }
    else {
        $(".databaseType").addClass("d-none")
    }
    GetReplicationList($("#Activetype option:selected").text())
})

$("#activityDatabaseType").on("change", () => {
    $("#activityDatabaseName-error").removeClass("field-validation-error")
    $("#activityDatabaseName-error").text("")
})

$("#replicationNmae").on("change", () => {
    $("#replicationName-error").removeClass("field-validation-error")
    $("#replicationName-error").text("")
    let selectedreplication = $("#replicationNmae option:selected").val()
    let activeType = $("#Activetype option:selected").text()
    GetReplicationType(selectedreplication, activeType)
})

$("#replicationType").on("change", () => {
    $("#replicationType-error").removeClass("field-validation-error")
    $("#replicationType-error").text("")
})

$("#dashboardBuilderDropdown").on("change", () => {
    $("#dashboardBuilderDropdown-error").removeClass("field-validation-error")
    $("#dashboardBuilderDropdown-error").text("")
})

$("#userMappingType").on("change", () => {
    $("#userMappingType-error").removeClass("field-validation-error")
    $("#userMappingType-error").text("")
})

$("#mapUserRole").on("change", () => {
    $("#mapUserRole-error").removeClass("field-validation-error")
    $("#mapUserRole-error").text("")
})

$(".btn_save").on('click', async function (e) {
    let dashboardBuilderId = $("#dashboardBuilderDropdown").val();
    let dashboardBuilderName = $("#dashboardBuilderDropdown option:selected").text();
    let userMappingTypeId = $("#userMappingType option:selected").val();
    let userMappingTypeName = $("#userMappingType option:selected").text();
    let mapUserRoleId = $("#mapUserRole option:selected").val();
    let mapUserRoleName = $("#mapUserRole option:selected").text();
    let defaultSet = $("#defaultSet").prop("checked")
    let viewCheck = $("#viewCheck").prop("checked")
    //let isName = await validateName(name);
    if (userMappingTypeName == "") {
        $("#userMappingType-error").addClass("field-validation-error")
        $("#userMappingType-error").text("Enter mappingtype name")
    }
    else {
        $("#userMappingType-error").removeClass("field-validation-error")
        $("#userMappingType-error").text("")
    }
    if (mapUserRoleName == "") {
        $("#mapUserRole-error").addClass("field-validation-error")
        $("#mapUserRole-error").text("Enter mapuser role")
    }
    else {
        $("#mapUserRole-error").removeClass("field-validation-error")
        $("#mapUserRole-error").text("")
    }
    if (dashboardBuilderName == "") {
        $("#dashboardBuilderDropdown-error").addClass("field-validation-error")
        $("#dashboardBuilderDropdown-error").text("Enter Database Type")
    }
    else {
        $("#dashboardBuilderDropdown-error").removeClass("field-validation-error")
        $("#dashboardBuilderDropdown-error").text("")
    }
    //if (replicationNmae == "") {
    //    $("#replicationName-error").addClass("field-validation-error")
    //    $("#replicationName-error").text("Enter replication name")
    //}
    //else {
    //    $("#replicationName-error").removeClass("field-validation-error")
    //    $("#replicationName-error").text("")
    //}
    //if (replicationTypeName == "") {
    //    $("#replicationType-error").addClass("field-validation-error")
    //    $("#replicationType-error").text("Enter replication type")
    //}
    //else {
    //    $("#replicationType-error").removeClass("field-validation-error")
    //    $("#replicationType-error").text("")
    //}
    //let randomNum = Math.floor(Math.random() * 90000) + 10000;
    if (userMappingTypeName == "" || mapUserRoleName == "" || dashboardBuilderName == "") {
        return false
    }
    let data = {}
    if (e.target.textContent == 'Update') {
        data.id = e.target.getAttribute('mapDataId')
    }
    data.DashBoardSubId = dashboardBuilderId
    data.DashBoardSubName = dashboardBuilderName
    if (userMappingTypeId == "user") {
        data.UserId = mapUserRoleId
        data.UserName = mapUserRoleName
        data.RoleId = null
        data.RoleName = null
    }
    else {
        data.UserId = null
        data.UserName = null
        data.RoleId = mapUserRoleId
        data.RoleName = mapUserRoleName
    }
    data.__RequestVerificationToken=gettoken()
    data.IsDefault = defaultSet
    data.IsView = viewCheck
    data.Type = userMappingTypeId
    data.url = "Dashboard/CustomDashboard/List"
    //data.Properties = JSON.stringify({
    //    type: e.target.getAttribute('type'),
    //    widgetDetails: pageHtml.outerHTML
    //})
    $.ajax({
        type: "POST",
        url: RootUrl + 'Manage/UserMapping/CreateOrUpdate',
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                $("#CreateModal").modal("hide")
                $("#dashboardBuilderDropdown option:selected").val("")
                $("#userMappingType option:selected").val("user")
                $("#mapUserRole option:selected").val("")
                $('#alertClass').removeClass("info-toast")
                $('#alertClass').addClass("success-toast")
                $('#message').text(result.message)
                $('#mytoastrdata').toast({ delay: 3000 });
                $('#mytoastrdata').toast('show');
                $(".iconClass").removeClass("cp-exclamation")
                $(".iconClass").addClass("cp-check");
                setTimeout(() => {
                    window.location.reload();
                },2000)
            }
        }
    })
})

async function validateName(value) {
    const errorElement = $('#Name-error');
    if (!value) {
        errorElement.text('Enter solution name')
            .addClass('field-validation-error');
        return false;
    }
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxCompanylength(value),
        await secondChar(value),
    ];
    return await CommonValidation(errorElement, validationResults);
}

function GetSolutionMappingList() {
    $.ajax({
        url: RootUrl + "Manage/UserMapping/GetPageWidgetList",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (response) {
            //     console.log("response", response);
        },
        error: function (error) {
            console.log(error);
        }
    });
}

function GetDatabaseLisList(databaseId) {
    $.ajax({
        url: RootUrl + "Configuration/InfraObject/GetDatabaseListByName",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (result) {
            $("#activityDatabaseType").empty()
            if (result.success) {
                $("#activityDatabaseType").append('<option value=""></option>')
                if (result?.data && Array.isArray(result.data) && result.data.length) {
                    result.data.forEach((s) => {
                        let optionValues = JSON.parse(s.properties)
                        $('#activityDatabaseType').append('<option value="' + s.id + '">' + optionValues['name'] + '</option>');
                        $("#activityDatabaseType option[value=" + databaseId + "]").attr('selected', 'selected');
                    })
                }
            }
        },
        error: function (error) {
            console.log(error);
        }
    });
}

function GetReplicationList(type, replicationId) {
    var data = {};
    data.infraMasterName = type == 'Database' ? 'DB' : type;
    $.ajax({
        url: RootUrl + "Configuration/InfraObject/GetReplicationMasterByInfraMasterName",
        dataType: "json",
        traditional: true,
        type: 'GET',
        data: data,
        success: function (response) {
            $("#replicationNmae").empty()
            if (response.success) {
                $("#replicationNmae").append('<option value=""></option>')
                response.data.forEach((data) => {
                    $("#replicationNmae").append("<option value='" + data.id + "'>" + data.name + "</option>")
                    $("#replicationNmae option[value=" + replicationId + "]").attr('selected', 'selected');
                })
            }
        },
        error: function (error) {
            console.log(error);
        }
    });
}

function GetReplicationType(replicationType, activeType, categorytype) {
    var data = {};
    data.databaseid = activeType === "DB" ? $('#activityDatabaseType option:selected').val() : ''
    data.replicationmasterid = replicationType
    data.type = activeType == 'DB' ? 'Database' : activeType
    $.ajax({
        url: RootUrl + "Admin/SolutionMapping/GetTypeByDatabaseIdAndReplicationMasterId",
        dataType: "json",
        traditional: true,
        type: 'GET',
        data: data,
        success: function (result) {
            $("#replicationType").empty()
            if (result.success) {
                if (result?.data && Array.isArray(result.data) && result.data.length) {
                    let uniqueIds = new Set();
                    $('#replicationType').append('<option value=""></option>');
                    for (let index = 0; index < result.data.length; index++) {
                        let properties = JSON.parse(result.data[index].properties)
                        if (properties.length > 0) {
                            for (let j = 0; j < properties.length; j++) {
                                if (!uniqueIds.has(properties[j].id)) {
                                    $('#replicationType').append('<option value="' + properties[j].id + '">' + properties[j].label + '</option>');
                                    $("#replicationType option[value=" + categorytype + "]").attr('selected', 'selected');
                                }
                            }
                        }
                    }
                }
            }
        },
        error: function (error) {
            console.log(error);
        }
    });
}

function GetPageBuilderList(dashBoardSubId = null) {
    $.ajax({
        url: RootUrl + "Manage/UserMapping/GetPageBuilderList",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (response) {
            $("#dashboardBuilderDropdown").empty()
            if (response.success) {
                $("#dashboardBuilderDropdown").append('<option value=""></option>')
                response.message.forEach((data) => {
                    $("#dashboardBuilderDropdown").append("<option value='" + data.id + "'>" + data.name + "</option>")
                })
            }
            if (dashBoardSubId) {
                $("#dashboardBuilderDropdown option[value=" + dashBoardSubId + "]").attr('selected', 'selected');
            }

        },
        error: function (error) {
            console.log(error);
        }
    });
}
