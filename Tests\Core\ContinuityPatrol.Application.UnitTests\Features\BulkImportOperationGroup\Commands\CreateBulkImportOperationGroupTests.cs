using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperationGroup.Commands;

public class CreateBulkImportOperationGroupTests : IClassFixture<BulkImportOperationGroupFixture>
{
    private readonly BulkImportOperationGroupFixture _bulkImportOperationGroupFixture;
    private readonly Mock<IBulkImportOperationGroupRepository> _mockBulkImportOperationGroupRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IPublisher> _publisher;
    private readonly CreateBulkImportOperationGroupCommandHandler _handler;

    public CreateBulkImportOperationGroupTests(BulkImportOperationGroupFixture bulkImportOperationGroupFixture)
    {
        _bulkImportOperationGroupFixture = bulkImportOperationGroupFixture;

        _mockBulkImportOperationGroupRepository = BulkImportOperationGroupRepositoryMocks.CreateBulkImportOperationGroupRepository(_bulkImportOperationGroupFixture.BulkImportOperationGroups);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<Domain.Entities.BulkImportOperationGroup>(It.IsAny<CreateBulkImportOperationGroupCommand>()))
            .Returns((CreateBulkImportOperationGroupCommand cmd) => new Domain.Entities.BulkImportOperationGroup
            {
                BulkImportOperationId = cmd.BulkImportOperationId,
                CompanyId = cmd.CompanyId,
                Properties = cmd.Properties,
                Status = cmd.Status,
                ProgressStatus = cmd.ProgressStatus,
                ErrorMessage = cmd.ErrorMessage,
                ConditionalOperation = cmd.ConditionalOperation,
                NodeId = cmd.NodeId,
                InfraObjectName = cmd.InfraObjectName
            });

        //_handler = new CreateBulkImportOperationGroupCommandHandler(
        //    _mockMapper.Object,
        //    _mockBulkImportOperationGroupRepository.Object, _publisher!.Object);
        _mockMapper = new Mock<IMapper>();
        _mockBulkImportOperationGroupRepository = new Mock<IBulkImportOperationGroupRepository>();
        _publisher = new Mock<IPublisher>(); 

        _handler = new CreateBulkImportOperationGroupCommandHandler(
            _mockMapper.Object,
            _mockBulkImportOperationGroupRepository.Object,
            _publisher.Object);
    }

    [Fact]
    public async Task Handle_Return_CreateBulkImportOperationGroupResponse_When_BulkImportOperationGroupCreated()
    {
        var command = _bulkImportOperationGroupFixture.CreateBulkImportOperationGroupCommand;

        var expectedEntity = new Domain.Entities.BulkImportOperationGroup
        {
            ReferenceId = Guid.NewGuid().ToString()
        };

        _mockBulkImportOperationGroupRepository
            .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
            .ReturnsAsync(expectedEntity);

        var result = await _handler.Handle(command, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateBulkImportOperationGroupResponse));
        result.Id.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var command = _bulkImportOperationGroupFixture.CreateBulkImportOperationGroupCommand;

        var expectedEntity = new Domain.Entities.BulkImportOperationGroup
        {
            ReferenceId = Guid.NewGuid().ToString()
        };

        _mockBulkImportOperationGroupRepository
            .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
            .ReturnsAsync(expectedEntity);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var command = _bulkImportOperationGroupFixture.CreateBulkImportOperationGroupCommand;

        var expectedEntity = new Domain.Entities.BulkImportOperationGroup
        {
            ReferenceId = Guid.NewGuid().ToString()
        };

        _mockBulkImportOperationGroupRepository
            .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
            .ReturnsAsync(expectedEntity);

        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<Domain.Entities.BulkImportOperationGroup>(It.IsAny<CreateBulkImportOperationGroupCommand>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateResponseWithCorrectProperties_When_BulkImportOperationGroupCreated()
    {
        // Arrange
        var command = _bulkImportOperationGroupFixture.CreateBulkImportOperationGroupCommand;
        command.InfraObjectName = "TestInfraObject";

        var expectedEntity = new Domain.Entities.BulkImportOperationGroup
        {
            ReferenceId = Guid.NewGuid().ToString()
        };

        _mockBulkImportOperationGroupRepository
            .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
            .ReturnsAsync(expectedEntity);

        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_MapCommandToEntity_WithCorrectProperties()
    {
        // Arrange
        var command = new CreateBulkImportOperationGroupCommand
        {
            BulkImportOperationId = Guid.NewGuid().ToString(),
            CompanyId = Guid.NewGuid().ToString(),
            Properties = "{\"test\":\"value\"}",
            Status = "Pending",
            ProgressStatus = "0/5",
            ErrorMessage = "",
            ConditionalOperation = 1,
            NodeId = "Node001",
            InfraObjectName = "TestInfraObject"
        };

        Domain.Entities.BulkImportOperationGroup capturedEntity = null;
        _mockBulkImportOperationGroupRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
            .Callback<Domain.Entities.BulkImportOperationGroup>(entity => capturedEntity = entity)
            .ReturnsAsync((Domain.Entities.BulkImportOperationGroup entity) => 
            {
                entity.ReferenceId = Guid.NewGuid().ToString();
                return entity;
            });
        var expectedEntity = new Domain.Entities.BulkImportOperationGroup
        {
            ReferenceId = Guid.NewGuid().ToString()
        };

        _mockBulkImportOperationGroupRepository
            .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
            .ReturnsAsync(expectedEntity);
       
        await _handler.Handle(command, CancellationToken.None);
    }

    [Fact]
    public async Task Handle_SetCorrectEntityProperties_When_CommandMapped()
    {
        // Arrange
        var command = _bulkImportOperationGroupFixture.CreateBulkImportOperationGroupCommand;
        command.BulkImportOperationId = "TestOperationId";
        command.CompanyId = "TestCompanyId";
        command.Properties = "{\"key\":\"value\"}";
        command.Status = "Active";
        command.ProgressStatus = "2/10";
        command.ConditionalOperation = 2;
        command.NodeId = "TestNode";
        command.InfraObjectName = "TestInfra";

        Domain.Entities.BulkImportOperationGroup capturedEntity = null;
        _mockBulkImportOperationGroupRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
            .Callback<Domain.Entities.BulkImportOperationGroup>(entity => capturedEntity = entity)
            .ReturnsAsync((Domain.Entities.BulkImportOperationGroup entity) => entity);
        var expectedEntity = new Domain.Entities.BulkImportOperationGroup
        {
            ReferenceId = Guid.NewGuid().ToString()
        };

        _mockBulkImportOperationGroupRepository
            .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
            .ReturnsAsync(expectedEntity);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_OperationSuccessful()
    {
        // Arrange
        var command = _bulkImportOperationGroupFixture.CreateBulkImportOperationGroupCommand;

        var expectedEntity = new Domain.Entities.BulkImportOperationGroup
        {
            ReferenceId = Guid.NewGuid().ToString()
        };

        _mockBulkImportOperationGroupRepository
            .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
            .ReturnsAsync(expectedEntity);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<CreateBulkImportOperationGroupResponse>();
        result.GetType().ShouldBe(typeof(CreateBulkImportOperationGroupResponse));
    }

    [Fact]
    public async Task Handle_SetReferenceIdInResponse_When_EntityCreated()
    {
        // Arrange
        var command = _bulkImportOperationGroupFixture.CreateBulkImportOperationGroupCommand;
        var expectedReferenceId = Guid.NewGuid().ToString();

        _mockBulkImportOperationGroupRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
            .ReturnsAsync((Domain.Entities.BulkImportOperationGroup entity) => 
            {
                entity.ReferenceId = "bcffa872-3552-4250-af92-8b6ccb132d86";
                return entity;
            });
        var expectedEntity = new Domain.Entities.BulkImportOperationGroup
        {
            ReferenceId = Guid.NewGuid().ToString()
        };

        _mockBulkImportOperationGroupRepository
            .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
            .ReturnsAsync(expectedEntity);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

       
    }
}
