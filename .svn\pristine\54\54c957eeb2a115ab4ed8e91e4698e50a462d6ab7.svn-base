﻿using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Helper;

public static class UrlHelper
{
    #region Workflow

    public static string GeneratePauseResumeUrl(string baseUrl, string groupId)
    {
        return $"{baseUrl}/LoadBalancer/workflow/workflowOperationGroupId?workflowOperationGroupId={groupId}";
    }

    public static string GenerateFailedActionUrl(string typeCategory, string baseUrl, string operationId)
    {
        return typeCategory == ServiceType.LoadBalancer.ToString()
            ? $"{baseUrl}/LoadBalancer/{operationId}"
            : $"{baseUrl}/api/workflow/execute/{operationId}";
    }

    public static string GenerateExecutionUrl(string typeCategory, string baseUrl, string operationId)
    {
        return typeCategory == ServiceType.LoadBalancer.ToString()
            ? $"{baseUrl}/LoadBalancer/{operationId}"
            : $"{baseUrl}/api/workflow/execute/{operationId}";
    }

    public static string GenerateCheckWindowsServiceUrl(string typeCategory, string baseUrl, string type)
    {
        return typeCategory == ServiceType.LoadBalancer.ToString()
            ? type.Equals("monitor") ? $"{baseUrl}/LoadBalancer/monitorCheck" : $"{baseUrl}/LoadBalancer"
            : $"{baseUrl}/api/workflow/greet";
    }

    #endregion

    #region Monitor
    public static string GenerateSaveAllUrl(string typeCategory, string baseUrl)
    {
        return typeCategory == ServiceType.LoadBalancer.ToString()
            ? $"{baseUrl}/LoadBalancer/ComponentSaveAll/"
            : $"{baseUrl}/api/monitor/ComponentSaveAll/";
    }
    public static string GenerateServerTestConnectionUrl(string typeCategory, string baseUrl)
    {
       return typeCategory == ServiceType.LoadBalancer.ToString()
            ? $"{baseUrl}/LoadBalancer/ServerTestConnection"
            : $"{baseUrl}/api/monitor/ServerTest";
    }

    public static string GenerateDatabaseTestConnectionUrl(string typeCategory, string baseUrl)
    {
       return typeCategory == ServiceType.LoadBalancer.ToString()
            ? $"{baseUrl}/LoadBalancer/DatabaseTestConnection"
            : $"{baseUrl}/api/monitor/DatabaseTest";
    }

    public static string GenerateMonitorCheckUrl(string typeCategory, string baseUrl)
    {
        return typeCategory == ServiceType.LoadBalancer.ToString()
            ? $"{baseUrl}/LoadBalancer/monitorCheck"
            : $"{baseUrl}/api/monitor/greet";
    }

    public static string GenerateScheduleReportUrl(string baseUrl, string reportScheduleId)
    {
        return $"{baseUrl}/LoadBalancer/ScheduleReport/{reportScheduleId}";
    }

    public static string GenerateReplicationJobUrl(string typeCategory, string baseUrl, string jobId)
    {
        return typeCategory == ServiceType.LoadBalancer.ToString()
            ? $"{baseUrl}/LoadBalancer/monitor/jobId?jobId={jobId}"
            : $"{baseUrl}/api/monitor/execute/{jobId}";
    }

    public static string GenerateMonitorServiceUrl(string baseUrl, string monitorId)
    {
        return $"{baseUrl}/LoadBalancer/MonitoringService/{monitorId}";
    }

    public static string GenerateJobUrl(string typeCategory, string baseUrl, string jobId)
    {
        return typeCategory == ServiceType.LoadBalancer.ToString()
            ? $"{baseUrl}/LoadBalancer/monitor/jobId?jobId={jobId}"
            : $"{baseUrl}/api/monitor/execute/{jobId}";
    }

    public static string GenerateRescheduleJobUrl(string baseUrl, string infraObjectId)
    {
        return $"{baseUrl}/LoadBalancer/monitor/infraObjectId?infraObjectId={infraObjectId}";
    }

    public static string GenerateInfraObjectSchedulerUrl(string typeCategory, string baseUrl, string infraObjectSchedulerId)
    {
        return typeCategory == ServiceType.LoadBalancer.ToString()
            ? $"{baseUrl}/LoadBalancer/drReady/infraObjectSchedulerId?infraObjectSchedulerId={infraObjectSchedulerId}"
            : $"{baseUrl}/api/monitor/execute/{infraObjectSchedulerId}";
    }

    public static string GenerateInfraObjectUrl(string baseUrl, string infraObjectId)
    {
       return $"{baseUrl}/LoadBalancer/monitor-InfraObject/infraObjectId?infraObjectId={infraObjectId}";
    }

    public static string GenerateBulkImportUrl(string typeCategory, string baseUrl, string bulkImportOperationId)
    {
        return typeCategory == ServiceType.LoadBalancer.ToString()
            ? $"{baseUrl}/LoadBalancer/bulkimport/id?bulkImportId={bulkImportOperationId}"
            : $"{baseUrl}/api/monitor/bulkimport/id?bulkImportId={bulkImportOperationId}";
    }

    public static string GenerateDriftJobUrl(string baseUrl, string driftJobId)
    {
        return $"{baseUrl}/LoadBalancer/DriftManagement/{driftJobId}";
        
    }

    public static string GenerateAirGapMonitorJobUrl(string typeCategory, string baseUrl, string airGabJobId)
    {
        return typeCategory == ServiceType.LoadBalancer.ToString()
            ? $"{baseUrl}/LoadBalancer/drReady/InitializeAirGapMonitor?cyberJobId={airGabJobId}"
            : $"{baseUrl}/api/monitor/execute/airGapJob/{airGabJobId}";
    }

    public static string GenerateAirGapMonitorStatusUrl(string typeCategory, string baseUrl, string airGabId)
    {
        return typeCategory == ServiceType.LoadBalancer.ToString()
            ? $"{baseUrl}/LoadBalancer/monitor/jobId?jobId={airGabId}"
            : $"{baseUrl}/api/monitor/execute/{airGabId}";
    }

    public static string GenerateAdPasswordJobUrl(string baseUrl, string adPasswordJobId)
    {
        return $"{baseUrl}/LoadBalancer/AdPasswordExpiry/{adPasswordJobId}";
    }

    public static string GenerateBackUptUrl(string typeCategory, string baseUrl, string backUpLogId)
    {
        return typeCategory == ServiceType.LoadBalancer.ToString()
            ? $"{baseUrl}/LoadBalancer/Backup/{backUpLogId}"
            : $"{baseUrl}/api/monitor/InitiateBackUpService/{backUpLogId}";
    }

    public static string GenerateDetailMonitorCheckUrl(string typeCategory, string baseUrl,string id)
    {
        return typeCategory == ServiceType.LoadBalancer.ToString()
            ? $"{baseUrl}/LoadBalancer/detail-monitor/infraObjectId?infraObjectId={id}"
            : "";
    }

    #endregion
}