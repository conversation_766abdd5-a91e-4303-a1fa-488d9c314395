﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class UserRepositoryMocks
{
    public static Mock<IUnitOfWork<DbContext>> CreateUserRepository(List<User> users, List<UserInfo> userInfos, List<UserInfraObject> userInfraObjects)
    {
        var userRepository = new Mock<IUnitOfWork<DbContext>>();

        userRepository.Setup(x => x.Repository<User>().ListAllAsync()).ReturnsAsync(users);

        userRepository.Setup(x => x.Repository<UserInfraObject>().AddRangeAsync((IEnumerable<UserInfraObject>)It.IsAny<UserInfraObject>()));

        userRepository.Setup(x => x.Repository<User>().AddAsync(It.IsAny<User>())).ReturnsAsync(
        (User user) =>
        {
            user.Id = new Fixture().Create<int>();

            user.ReferenceId = new Fixture().Create<Guid>().ToString();

            return user;
        });

        userRepository.Setup(x => x.Repository<UserInfo>().AddAsync(It.IsAny<UserInfo>())).ReturnsAsync(
        (UserInfo userInfo) =>
        {
            userInfo.Id = new Fixture().Create<int>();

            userInfo.ReferenceId = new Fixture().Create<Guid>().ToString();

            return userInfo;
        });

        return userRepository;
    }

    public static Mock<IUserRepository> CreateUserValidateRepository(List<User> users)
    {
        var userValidateRepository = new Mock<IUserRepository>();

        userValidateRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(users);

        userValidateRepository.Setup(repo => repo.AddAsync(It.IsAny<User>())).ReturnsAsync(
            (User user) =>
            {
                user.Id = new Fixture().Create<int>();

                user.ReferenceId = new Fixture().Create<Guid>().ToString();

                users.Add(user);

                return user;
            });

        return userValidateRepository;
    }

    public static Mock<IUserRepository> UpdateUserRepository(List<User> users)
    {
        var mockUserRepository = new Mock<IUserRepository>();

        mockUserRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(users);

        mockUserRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => users.SingleOrDefault(x => x.ReferenceId == i));

        mockUserRepository.Setup(repo => repo.UpdateAsync(It.IsAny<User>())).ReturnsAsync((User user) =>
        {
            var index = users.FindIndex(item => item.ReferenceId == user.ReferenceId);

            users[index] = user;

            return user;
        });

        return mockUserRepository;
    }

    public static Mock<IUserRepository> UpdatePasswordRepository(List<User> users)
    {
        var updatePasswordRepository = new Mock<IUserRepository>();

        updatePasswordRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(users);

        updatePasswordRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => users.SingleOrDefault(x => x.ReferenceId == i));

        updatePasswordRepository.Setup(repo => repo.FindByLoginNameAsync(It.IsAny<string>())).ReturnsAsync((string i) => users.SingleOrDefault(x => x.LoginName == i));

        updatePasswordRepository.Setup(repo => repo.UpdateAsync(It.IsAny<User>())).ReturnsAsync((User user) =>
        {
            var index = users.FindIndex(item => item.ReferenceId == user.ReferenceId);

            users[index] = user;

            return user;
        });

        return updatePasswordRepository;
    }

    public static Mock<IUserRepository> DeleteUserRepository(List<User> users)
    {
        var mockUserRepository = new Mock<IUserRepository>();

        mockUserRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(users);

        mockUserRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => users.SingleOrDefault(x => x.ReferenceId == i));

        mockUserRepository.Setup(repo => repo.UpdateAsync(It.IsAny<User>())).ReturnsAsync((User user) =>
        {
            var index = users.FindIndex(item => item.ReferenceId == user.ReferenceId);

            user.IsActive = false;

            users[index] = user;

            return user;
        });

        return mockUserRepository;
    }

    public static Mock<IUserRepository> GetUserRepository(List<User> users)
    {
        var mockUserRepository = new Mock<IUserRepository>();

        mockUserRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(users);

        mockUserRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => users.SingleOrDefault(x => x.ReferenceId == i));

        return mockUserRepository;
    }

    public static Mock<IUserRepository> GetUserNamesRepository(List<User> users)
    {
        var userNamesRepository = new Mock<IUserRepository>();

        userNamesRepository.Setup(repo => repo.GetUserNames()).ReturnsAsync(users);

        return userNamesRepository;
    }

    public static Mock<IUserRepository> GetUserNameUniqueRepository(List<User> users)
    {
        var userNameUniqueRepository = new Mock<IUserRepository>();

        userNameUniqueRepository.Setup(repo => repo.IsUserNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => users.Exists(x => x.LoginName == i && x.ReferenceId == j));

        return userNameUniqueRepository;
    }

    public static Mock<IUserRepository> GetUserEmptyRepository()
    {
        var mockUserRepository = new Mock<IUserRepository>();

        mockUserRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<User>());

        return mockUserRepository;
    }

    public static Mock<IUserRepository> GetUserLoginName(List<User> users)
    {
        var userLoginNameRepository = new Mock<IUserRepository>();

        userLoginNameRepository.Setup(repo => repo.FindByLoginNameAsync(It.IsAny<string>())).ReturnsAsync((string i) => users.SingleOrDefault(x => x.LoginName == i));

        return userLoginNameRepository;
    }

    public static Mock<IUserRepository> GetPaginatedUserRepository(List<User> users)
    {
        var userRepository = new Mock<IUserRepository>();

        var queryableUsers = users.BuildMock();

        userRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableUsers);

        return userRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateUserEventRepository(List<UserActivity> userActivities)
    {
        var userEventRepository = new Mock<IUserActivityRepository>();

        userEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        userEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return userEventRepository;
    }
}