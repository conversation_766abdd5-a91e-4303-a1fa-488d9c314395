﻿using ContinuityPatrol.Application.Features.User.Events.Update;
using ContinuityPatrol.Application.Features.User.Events.UpdateSendEmail;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.User.Commands.Update;

public class UpdateUserCommandHandler : IRequestHandler<UpdateUserCommand, UpdateUserResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IUserInfoRepository _userInfoRepository;
    private readonly IUserInfraObjectRepository _userInfraObjectRepository;
    private readonly IUserRepository _userRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IUserRoleRepository _userRoleRepository;   
    public UpdateUserCommandHandler(IMapper mapper, IUserRepository userRepository,
        IUserInfoRepository userInfoRepository, IPublisher publisher,
        IUserInfraObjectRepository userInfraObjectRepository,ILoggedInUserService loggedInUserService,IUserRoleRepository userRoleRepository)
    {
        _mapper = mapper;
        _userRepository = userRepository;
        _userInfoRepository = userInfoRepository;
        _publisher = publisher;
        _userInfraObjectRepository = userInfraObjectRepository;
        _loggedInUserService = loggedInUserService;
        _userRoleRepository = userRoleRepository;
    }

    public async Task<UpdateUserResponse> Handle(UpdateUserCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdateUser = await _userRepository.GetByReferenceIdAsync(request.Id);

        string previouseRole=eventToUpdateUser.RoleName;

        request.UserInfoCommand.UserId = request.Id;

        var eventToUpdateUserInfo = await _userInfoRepository.GetUserInfoByUserIdAsync(request.Id);

        var eventToUpdateUserInfraObject = await _userInfraObjectRepository.GetUserInfraObjectByUserIdAsync(request.Id);

        if (eventToUpdateUser == null && eventToUpdateUserInfo == null) 
            throw new NotFoundException(nameof(Domain.Entities.User), request.Id);

        _mapper.Map(request, eventToUpdateUser, typeof(UpdateUserCommand), typeof(Domain.Entities.User));

        _mapper.Map(request.UserInfoCommand, eventToUpdateUserInfo, typeof(UpdateUserCommand),
            typeof(Domain.Entities.UserInfo));
        await _userRepository.UpdateAsync(eventToUpdateUser);

        await _userInfoRepository.UpdateAsync(eventToUpdateUserInfo);

        if (request.UserInfraObjectCommand != null)
        {
            _mapper.Map(request.UserInfraObjectCommand, eventToUpdateUserInfraObject);

            await _userInfraObjectRepository.UpdateAsync(eventToUpdateUserInfraObject);
        }

        var response = new UpdateUserResponse
        {
            Message = Message.Update(nameof(Domain.Entities.User), eventToUpdateUser!.LoginName),

            UserId = eventToUpdateUser.ReferenceId
        };

        await _publisher.Publish(new UserUpdatedEvent { UserName = eventToUpdateUser.LoginName }, cancellationToken);


        if (eventToUpdateUser.RoleName.ToLower()!=previouseRole.ToLower())
        {
            await _publisher.Publish(
                new UpdateSendEmailEvent
                {
                    UserId = eventToUpdateUser.ReferenceId,
                    IsPreferredMode = request.UserInfoCommand.IsPreferredMode,
                    Email = request.UserInfoCommand.Email,
                    LoginName = request.LoginName,
                    UserName = request.UserInfoCommand.UserName,
                    CompanyName = request.CompanyName,
                    LoginType = request.LoginType,
                    PreviouseRole= previouseRole,
                    CurrentRole = request.RoleName,
                    ChangedByUserRole = _userRoleRepository.GetByReferenceIdAsync(_loggedInUserService?.Role)?.Result?.Role,
                    ChangedByUserName= _loggedInUserService.LoginName
                }, cancellationToken);
        }
       


        return response;
    }

    private string GetUserPassword(string userName, string userPassword, string loginType)
    {
        if (loginType.Trim().ToLower().Equals("ad")) return SecurityHelper.Encrypt("Use you AD password");

        var pas = SecurityHelper.Decrypt(userPassword);

        var splitIndex = userName.Length;

        if (splitIndex != -1)
        {
            var password = pas.Substring(splitIndex);

            return SecurityHelper.Encrypt(password);
        }

        return string.Empty;
    }
}