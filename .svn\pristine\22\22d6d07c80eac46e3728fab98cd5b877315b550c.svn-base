﻿namespace ContinuityPatrol.Application.Features.LicenseHistory.Commands.Delete;

public class
    DeleteLicenseHistoryCommandHandler : IRequestHandler<DeleteLicenseHistoryCommand, DeleteLicenseHistoryResponse>
{
    private readonly ILicenseHistoryRepository _licenseHistoryRepository;

    public DeleteLicenseHistoryCommandHandler(ILicenseHistoryRepository licenseHistoryRepository)
    {
        _licenseHistoryRepository = licenseHistoryRepository;
    }

    public async Task<DeleteLicenseHistoryResponse> Handle(DeleteLicenseHistoryCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _licenseHistoryRepository.GetLicenseHistoryByLicenseId(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.LicenseHistory),
            new NotFoundException(nameof(Domain.Entities.LicenseHistory), request.Id));

        eventToDelete.IsActive = false;

        await _licenseHistoryRepository.UpdateAsync(eventToDelete);

        var response = new DeleteLicenseHistoryResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.LicenseHistory), eventToDelete.ReferenceId),

            IsActive = eventToDelete.IsActive
        };
        return response;
    }
}