﻿const serverLogURL = {
    ServerLogNameExistUrl: 'Configuration/ServerLog/IsServerLogNameExist',
    ServerLogPaginatedUrl: "/Configuration/ServerLog/GetPagination"

}
let createPermission = $("#configurationCreate").data("create-permission")?.toLowerCase();
let deletePermission = $("#configurationDelete").data("delete-permission")?.toLowerCase();
let serverLogs = '';
let exceptThisSymbol = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "_", "+", "=", "{", "}", "[", "]", "|", ":", ";", "'", "\"", "<", ">", "?", "/", "\\"];
let regex = new RegExp('[' + exceptThisSymbol.join('\\') + ']|\\.(?=.*\\.)');
let selectedValues = [];
if (createPermission == 'false') {
    $("#create").removeClass('#create').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
}
$(function () {
    // btnCrudEnable('confirmDeleteButton');


    let dataTable = $('#serverLogTable').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": serverLogURL.ServerLogPaginatedUrl,
                "dataType": "json",
                "data": function (d) {
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                        json.recordsTotal = json?.data?.totalPages;
                        json.recordsFiltered = json?.data?.totalCount;
                        if (json?.data?.data?.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        return json?.data?.data;
                    }
                    else {
                        if (json.message.includes("Invalid object name 'server_logs'")) {
                            $("#create").addClass("disabled");
                            $("#serverLogTable_paginate").hide();
                        }
                        errorNotification(json);
                        return false;
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [1, 2],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    orderable: false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    orderable: false
                },

                {
                    "data": "name", "name": "ServerLog Name", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<td><span title="${row.Name}" > ${row.name || 'NA'}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "ipaddress", "name": "IPAddress", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<td><span title="${row.ipAddress}"> ${row.ipAddress || "NA"}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "userName", "name": "User Name", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<td><span  > ${row.userName || 'NA'}</span></td>`
                        }
                        return data;
                    }
                }, {
                    "data": "folderpath", "name": "Folder Path", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<td><span  > ${row.folderPath || 'NA'}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {

                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                 <span role="button" title="Edit" class="${row.isDelete === false ? 'icon-disabled' : 'edit-button'}" ${row.isDelete !== false ? `data-serverLog='${JSON.stringify(row)}'` : ''}>
                                <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="${row.isDelete === false ? 'icon-disabled' : 'delete-button'}" ${row.isDelete !== false ? `data-serverLog-id="${row.id}" data-serverLog-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"` : ''}>
                                <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                 <span role="button" title="Edit" class="${row.isDelete === false ? 'icon-disabled' : 'edit-button'}" ${row.isDelete !== false ? `data-serverLog='${JSON.stringify(row)}'` : ''}>
                                <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="icon-disabled ">
                                <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                 <span role="button" title="Edit" class="icon-disabled">
                                <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="${row.isDelete === false ? 'icon-disabled' : 'delete-button'}" ${row.isDelete !== false ? `data-serverLog-id="${row.id}" data-serverLog-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"` : ''}>
                                <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                        }
                        else {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                 <span role="button" title="Edit" class="icon-disabled">
                                <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="icon-disabled">
                                <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                        }
                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });
    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }

        const serverLogNameCheckbox = $("#ServerLogNames");
        const userNameCheckbox = $("#UserNames");

        const inputValue = $('#search-inp').val();
        if (serverLogNameCheckbox.is(':checked')) {
            selectedValues.push(serverLogNameCheckbox.val() + inputValue);
        }
        if (userNameCheckbox.is(':checked')) {
            selectedValues.push(userNameCheckbox.val() + inputValue);
        }

        dataTable.ajax.reload(function (json) {
            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    }, 500));
});

//Update
$('#serverLogTable').on('click', '.edit-button', function () {
    clearValidations();
    serverLogs = $(this).data("serverlog");
    $('#CreateModal').modal('show');
    serverLogEdit(serverLogs);
    $('#SaveFunction').text("Update");
});

//  Delete
$('#serverLogTable').on('click', '.delete-button', function () {
    const serverLogId = $(this).data("serverlogId");
    const serverLogName = $(this).data("serverlogName")
    //  $("#deleteData").attr("title", serverLogName);
    $('#textDeleteId').val(serverLogId);
    $('#deleteData').text(serverLogName);
});

//clear Error Message

function clearValidations() {
    let errorElements = ['#ServerLogName-error', '#IPAddress-error', '#UserName-error', '#Password-error', '#Path-error'];
    clearInputFields('CreateForm', errorElements);
}
//Clear data
$('#create').on('click', function () {
    clearValidations();
    $('#SaveFunction').text('Save');
});

function serverLogEdit(serverLogs) {
    $('#serverLogName').val(serverLogs.name);
    $('#serverNameId').val(serverLogs.id);
    $('#ipaddress').val(serverLogs.ipAddress);
    $('#userName').val(serverLogs.userName);
    $('#passwordID').val(serverLogs.password);
    $('#pathId').val(serverLogs.folderPath);
}
$('#serverLogName').on('input', function (event) {
    var value = $(this).val();
    var id = $("#serverNameId").val();
    var filterLogName = validateLog(value, "#ServerLogName-error", id, serverLogURL.ServerLogNameExistUrl);
    // Remove non-alphanumeric characters (keeping only letters, numbers, underscores, and hyphens)
    //   var cleanedValue = value.replace(/[^a-zA-Z0-9_-]/g, '');
    if (!value) {
        showError("#ServerLogName-error", "Enter Server Log Name"); return;
    } else if (value.length <= 2) { showError("#ServerLogName-error", "Between 3 to 100 characters") }
    else { hideError("#ServerLogName-error"); }
});
$('#ipaddress').on('input', function (event) {
    var valueIP = $(this).val();
    var filterIP = IpaddressReg(valueIP);
    if (valueIP.length == 0) {
        showError("#IPAddress-error", "Enter IP Address");
    } else if (filterIP != true) {
        showError("#IPAddress-error", "Invalid IP Address");
    } else { hideError("#IPAddress-error"); }
});
$('#userName').on('input', function (event) {
    var valueUserName = $(this).val();
    var filterUserName = UserNameValidate(valueUserName);

    // Validate User Name
    if (!valueUserName) { showError("#UserName-error", "Enter User Name"); }
    // Check if the input contains more than 3 consecutive repeated characters
    else if (/^(?!.*(.)\1{3}).*$/.test(valueUserName) === false) {
        showError("#UserName-error", "Repeated letter allowed minimum 3 times");
    }
    else if (valueUserName.length <= 2) {
        showError("#UserName-error", "Between 3 to 100 characters");
    } else if (filterUserName != true) {
        showError("#UserName-error", "Invalid User Name");
    } else { hideError("#UserName-error"); }
});
$('#passwordID').on('input', function (event) {
    var valuePassword = $(this).val();
    var filterPassword = systemPasswordRegex(valuePassword);

    // Validate Password
    if (!valuePassword) {
        showError("#Password-error", "Enter Password");
    } else if (filterPassword != true) {
        showError("#Password-error", "Invalid Password");
    } else { hideError("#Password-error"); }

});
$('#pathId').on('input', function (event) {
    var valuePath = $(this).val();
    var filterPath = folderPathValidate(valuePath);

    // Validate Path
    if (!valuePath) {
        showError("#Path-error", "Enter Folder Path");
    } else if (filterPath != true) {
        showError("#Path-error", "Invalid Folder Path");
    } else { hideError("#Path-error"); }

});
$("#SaveFunction").on("click", async function () {
    let form = $("#CreateForm");
    let fields = {
        serverLogName: $("#serverLogName"),
        serverLogId: $("#serverNameId"),
        ipAddress: $("#ipaddress"),
        userName: $("#userName"),
        password: $("#passwordID"),
        path: $("#pathId"),
    };

    let errorMessages = {
        serverLogName: "Enter Server Log Name",
        invalidServerLogName: "Invalid Server Log Name",
        ipAddress: "Enter IP Address",
        invalidIpAddress: "Invalid IP Address",
        userName: "Enter User Name",
        invalidUserName: "Invalid User Name",
        password: "Enter Password",
        invalidPassword: "Invalid Password",
        path: "Enter Folder Path",
        invalidPath: "Invalid Folder Path",
    };

    // Declare validation variables
    let serverLogNameError = true,
        ipError = true,
        userNameError = true,
        passwordError = true,
        pathError = true;

    // Validate Server Log Name
    if (!fields.serverLogName.val()) {
        serverLogNameError = showError("#ServerLogName-error", errorMessages.serverLogName);
    } else if ((await validateLog(fields.serverLogName.val(), "#ServerLogName-error", fields.serverLogId.val(), serverLogURL.ServerLogNameExistUrl)) != true) {
        serverLogNameError = false//showError("#ServerLogName-error", errorMessages.invalidServerLogName);
    }
    else { hideError("#ServerLogName-error"); }

    // Validate IP Address
    if (!fields.ipAddress.val()) {
        ipError = showError("#IPAddress-error", errorMessages.ipAddress);
    } else if ((await IpaddressReg(fields.ipAddress.val())) != true) {
        ipError = showError("#IPAddress-error", errorMessages.invalidIpAddress);
    } else { hideError("#IPAddress-error"); }

    // Validate User Name
    if (!fields.userName.val()) {
        userNameError = showError("#UserName-error", errorMessages.userName);
    } else if ((await UserNameValidate(fields.userName.val())) != true) {
        userNameError = showError("#UserName-error", errorMessages.invalidUserName);
    } else { hideError("#UserName-error"); }

    // Validate Password
    if (!fields.password.val()) {
        passwordError = showError("#Password-error", errorMessages.password);
    } else if ((await passwordRegex(fields.password.val())) != true) {
        passwordError = showError("#Password-error", errorMessages.invalidPassword);
    } else { hideError("#Password-error"); }

    // Validate Path
    if (!fields.path.val()) {
        pathError = showError("#Path-error", errorMessages.path);
    } else if ((await folderPathValidate(fields.path.val())) != true) {
        pathError = showError("#Path-error", errorMessages.invalidPath);
    }
    //else if (typeof fields.path === 'string' && fields.path.includes("//")) {
    //    pathError = showError("Path-error", "'//' is not allowed. Please use '\\' instead.")
    //}
    else { hideError("#Path-error"); }

    // If all validations pass, submit the form
    if (serverLogNameError == true && ipError == true && userNameError == true && passwordError == true && pathError == true) {
        sanitizeContainer(Object.keys(fields));
        setTimeout(() => form.trigger("submit"), 200);
    } else { hideError(""); }

});

// Function to display error messages
function showError(selector, message) {
    $(selector).text(message).addClass("field-validation-error");
    return false;
}
// Function to clear error message
function hideError(selector) {
    $(selector).text('').removeClass("field-validation-error");
}

async function validateLog(serverLogName, element, id = null, url) {

    const errorElement = $(element);

    //if (serverLogName == "") {
    //    errorElement.text('Enter ServerLog name')
    //        .addClass('field-validation-error');
    //    return false;
    //}
    if (serverLogName.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    if (serverLogName.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    const validationResults = [
        await IsServerLogNameExist(serverLogName, id, url),
        await ShouldNotBeginWithSpace(serverLogName),
        await SpecialCharValidate(serverLogName),
        await ShouldNotBeginWithUnderScore(serverLogName),
        await OnlyNumericsValidate(serverLogName),
        await ShouldNotBeginWithNumber(serverLogName),
        await ShouldNotEndWithSpace(serverLogName),
        await ShouldNotAllowMultipleSpace(serverLogName),
        await SpaceWithUnderScore(serverLogName),
        await ShouldNotEndWithUnderScore(serverLogName),
        await MultiUnderScoreRegex(serverLogName),
        await SpaceAndUnderScoreRegex(serverLogName),
        await minMaxlength(serverLogName),
        await secondChar(serverLogName)
    ];
    //return validationResults;
    return await CommonValidation(errorElement, validationResults);
}
const UserNameValidate = (value) => {
    const regex = /^(?:(?:[a-zA-Z0-9.-]+)\\)?(?!.*[\\/:*?"<>|])(?!^[ .])(?!.*[ .]$)[a-zA-Z0-9._\- ]{1,20}$/;  // Allows letters, numbers, underscores, dots, space (only between) and hyphens, but not parentheses, and other special characters
    return !regex.test(value) ? "Special characters not allowed" : !(/^[^<]*$/).test(value) ? "Special characters not allowed" : true;
}
const folderPathValidate = (value) => {
    return (!RegExp(/^(?:[a-zA-Z]:\\|\\\\|\/)(?:[\w\s().\-\$\&\+#,@%]+(\\|\/)?)+$/).test(value)) ? "Invalid target path" : true;
};
const systemPasswordRegex = (value) => {
    return (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$%^&+=!]).{8,15}$/.test(value))
        ? "Invalid password"
        : true;
}

const IsServerLogNameExist = (value, id, url) => {
    if (!value.trim()) {
        return true;
    }
    return new Promise((resolve) => {
        $.ajax({
            type: "GET",
            url: RootUrl + url,

            data: {
                'name': value,
                'id': id
            },
            async: true,
            success: function (result) {
                resolve(result ? "Name already exists" : true);
            }
        });
    });
}