using ContinuityPatrol.Application.Features.DriftJob.Commands.Create;
using ContinuityPatrol.Application.Features.DriftJob.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftJob.Commands.RescheduleJob;
using ContinuityPatrol.Application.Features.DriftJob.Commands.Update;
using ContinuityPatrol.Application.Features.DriftJob.Commands.UpdateState;
using ContinuityPatrol.Application.Features.DriftJob.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.DriftJob.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftJob.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftJob.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftJobModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DriftJobFixture : IDisposable
{
    public CreateDriftJobCommand CreateDriftJobCommand { get; }
    public CreateDriftJobResponse CreateDriftJobResponse { get; }
    public UpdateDriftJobCommand UpdateDriftJobCommand { get; }
    public UpdateDriftJobResponse UpdateDriftJobResponse { get; }
    public DeleteDriftJobCommand DeleteDriftJobCommand { get; }
    public DeleteDriftJobResponse DeleteDriftJobResponse { get; }
    public DriftJobDetailVm DriftJobDetailVm { get; }
    public List<DriftJobListVm> DriftJobListVm { get; }
    public GetDriftJobPaginatedListQuery GetDriftJobPaginatedListQuery { get; }
    public PaginatedResult<DriftJobListVm> DriftJobPaginatedResult { get; }
    public GetDriftJobNameUniqueQuery GetDriftJobNameUniqueQuery { get; }
    public RescheduleDriftJobCommand RescheduleDriftJobCommand { get; }
    public RescheduleDriftJobResponse RescheduleDriftJobResponse { get; }
    public UpdateDriftJobStateCommand UpdateDriftJobStateCommand { get; }
    public BaseResponse UpdateDriftJobStateResponse { get; }
    public UpdateDriftJobStatusCommand UpdateDriftJobStatusCommand { get; }
    public UpdateDriftJobStatusResponse UpdateDriftJobStatusResponse { get; }

    public DriftJobFixture()
    {
        var fixture = new Fixture();

        // Configure fixture for enterprise drift job scenarios
        fixture.Customize<CreateDriftJobCommand>(c => c
            .With(x => x.Name, "Enterprise Configuration Drift Detection Job")
            .With(x => x.Properties, @"{""scanInterval"": ""24h"", ""alertThreshold"": ""medium"", ""autoRemediation"": true}")
            .With(x => x.SolutionTypeId, Guid.NewGuid().ToString())
            .With(x => x.SolutionTypeName, "Enterprise Drift Detection Solution")
            .With(x => x.NodeId, Guid.NewGuid().ToString())
            .With(x => x.NodeName, "Enterprise Primary Node")
            .With(x => x.Status, "Active")
            .With(x => x.State, "Running")
            .With(x => x.CronExpression, "0 0 2 * * ?")
            .With(x => x.IsSchedule, 1)
            .With(x => x.ScheduleType, 1)
            .With(x => x.ScheduleTime, "02:00:00")
            .With(x => x.ExceptionMessage, "")
            .With(x => x.LastExecutionTime, DateTime.UtcNow.AddHours(-2).ToString()));

        fixture.Customize<CreateDriftJobResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise Drift Job created successfully!")
            .With(x => x.Success, true));

        fixture.Customize<UpdateDriftJobCommand>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Name, "Enterprise Updated Configuration Drift Detection Job")
            .With(x => x.Properties, @"{""scanInterval"": ""12h"", ""alertThreshold"": ""high"", ""autoRemediation"": true}")
            .With(x => x.SolutionTypeId, Guid.NewGuid().ToString())
            .With(x => x.SolutionTypeName, "Enterprise Updated Drift Detection Solution")
            .With(x => x.NodeId, Guid.NewGuid().ToString())
            .With(x => x.NodeName, "Enterprise Updated Primary Node")
            .With(x => x.Status, "Active")
            .With(x => x.State, "Running")
            .With(x => x.CronExpression, "0 0 */12 * * ?")
            .With(x => x.IsSchedule, 1)
            .With(x => x.ScheduleType, 2)
            .With(x => x.ScheduleTime, "02:00:00,14:00:00")
            .With(x => x.ExceptionMessage, "")
            .With(x => x.LastExecutionTime, DateTime.UtcNow.AddHours(-1).ToString()));

        fixture.Customize<UpdateDriftJobResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise Drift Job updated successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DeleteDriftJobResponse>(c => c
            .With(x => x.IsActive, false)
            .With(x => x.Message, "Enterprise Drift Job deleted successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DriftJobDetailVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Name, "Enterprise Detail Configuration Drift Detection Job")
            .With(x => x.Properties, @"{""scanInterval"": ""6h"", ""alertThreshold"": ""critical"", ""autoRemediation"": false}")
            .With(x => x.SolutionTypeId, Guid.NewGuid().ToString())
            .With(x => x.SolutionTypeName, "Enterprise Detail Drift Detection Solution")
            .With(x => x.NodeId, Guid.NewGuid().ToString())
            .With(x => x.NodeName, "Enterprise Detail Primary Node")
            .With(x => x.Status, "Active")
            .With(x => x.State, "Running")
            .With(x => x.CronExpression, "0 0 */6 * * ?")
            .With(x => x.IsSchedule, 1)
            .With(x => x.ScheduleType, 3)
            .With(x => x.ScheduleTime, "02:00:00,08:00:00,14:00:00,20:00:00")
            .With(x => x.ExceptionMessage, "")
            .With(x => x.LastExecutionTime, DateTime.UtcNow.AddMinutes(-30).ToString()));

        fixture.Customize<DriftJobListVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Name, "Enterprise List Configuration Drift Detection Job")
            .With(x => x.SolutionTypeName, "Enterprise List Drift Detection Solution")
            .With(x => x.NodeName, "Enterprise List Primary Node")
            .With(x => x.Status, "Active")
            .With(x => x.State, "Running"));

        // Reschedule Command and Response
        fixture.Customize<RescheduleDriftJobCommand>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Name, "Enterprise Configuration Drift Detection Job")
            .With(x => x.Status, "Pending"));

        fixture.Customize<RescheduleDriftJobResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Drift Job Enterprise Configuration Drift Detection Job Rescheduled successfully!")
            .With(x => x.Success, true));

        // Update State Command and Response
        fixture.Customize<UpdateDriftJobStateCommand>(c => c
            .With(x => x.State, "Paused")
            .With(x => x.Reason, "Maintenance window scheduled")
            .With(x => x.UpdateDriftJobState, new List<UpdateDriftJobState>
            {
                new() { Id = Guid.NewGuid().ToString(), Name = "Enterprise Configuration Drift Detection Job" },
                new() { Id = Guid.NewGuid().ToString(), Name = "Enterprise Secondary Drift Detection Job" }
            }));

        fixture.Customize<BaseResponse>(c => c
            .With(x => x.Message, "Drift Job state updated successfully!")
            .With(x => x.Success, true));

        // Update Status Command and Response
        fixture.Customize<UpdateDriftJobStatusCommand>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Name, "Enterprise Configuration Drift Detection Job")
            .With(x => x.Status, "Completed"));

        fixture.Customize<UpdateDriftJobStatusResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Drift Job status updated successfully!")
            .With(x => x.Success, true));

        // Initialize properties
        CreateDriftJobCommand = fixture.Create<CreateDriftJobCommand>();
        CreateDriftJobResponse = fixture.Create<CreateDriftJobResponse>();
        UpdateDriftJobCommand = fixture.Create<UpdateDriftJobCommand>();
        UpdateDriftJobResponse = fixture.Create<UpdateDriftJobResponse>();
        DeleteDriftJobCommand = new DeleteDriftJobCommand { Id = Guid.NewGuid().ToString() };
        DeleteDriftJobResponse = fixture.Create<DeleteDriftJobResponse>();
        DriftJobDetailVm = fixture.Create<DriftJobDetailVm>();
        DriftJobListVm = fixture.CreateMany<DriftJobListVm>(8).ToList();
        
        GetDriftJobPaginatedListQuery = fixture.Create<GetDriftJobPaginatedListQuery>();
        DriftJobPaginatedResult = new PaginatedResult<DriftJobListVm>
        {
            Data = fixture.CreateMany<DriftJobListVm>(15).ToList(),
            TotalCount = 15,
            PageSize = 15,
            Succeeded = true
        };
        
        GetDriftJobNameUniqueQuery = new GetDriftJobNameUniqueQuery { Name = "Enterprise Configuration Drift Detection Job" };

        // Initialize missing command and response objects
        RescheduleDriftJobCommand = fixture.Create<RescheduleDriftJobCommand>();
        RescheduleDriftJobResponse = fixture.Create<RescheduleDriftJobResponse>();
        UpdateDriftJobStateCommand = fixture.Create<UpdateDriftJobStateCommand>();
        UpdateDriftJobStateResponse = fixture.Create<BaseResponse>();
        UpdateDriftJobStatusCommand = fixture.Create<UpdateDriftJobStatusCommand>();
        UpdateDriftJobStatusResponse = fixture.Create<UpdateDriftJobStatusResponse>();
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
