﻿using ContinuityPatrol.Application.Features.User.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.User.Queries;

public class GetUserNameUniqueQueryHandlerTests : IClassFixture<UserFixture>
{
    private readonly UserFixture _userFixture;
    private Mock<IUserRepository> _mockUserRepository;
    private readonly GetUserNameUniqueQueryHandler _handler;

    public GetUserNameUniqueQueryHandlerTests(UserFixture userFixture)
    {
        _userFixture = userFixture;
        _mockUserRepository = UserRepositoryMocks.GetUserNameUniqueRepository(_userFixture.Users);
        _handler = new GetUserNameUniqueQueryHandler(_userFixture.Mapper, _mockUserRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_UserName_Exist()
    {
        _userFixture.Users[0].LoginName = "Admin";

        var result = await _handler.Handle(new GetUserNameUniqueQuery { LoginName = _userFixture.Users[0].LoginName, UserId = _userFixture.Users[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_UserNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetUserNameUniqueQuery { LoginName = "Operator", UserId = _userFixture.Users[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_False_UserName_NotMatch()
    {
        var result = await _handler.Handle(new GetUserNameUniqueQuery { LoginName = "Manager", UserId = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockUserRepository = UserRepositoryMocks.GetUserEmptyRepository();

        var result = await _handler.Handle(new GetUserNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }

    [Fact]
    public async Task Handle_Call_IsUserNameExist_OneTime()
    {
        await _handler.Handle(new GetUserNameUniqueQuery(), CancellationToken.None);

        _mockUserRepository.Verify(x => x.IsUserNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }
}