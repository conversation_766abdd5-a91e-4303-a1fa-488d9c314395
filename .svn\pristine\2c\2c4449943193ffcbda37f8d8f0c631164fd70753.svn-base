using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixUsersModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IApprovalMatrixUsersService
{
    Task<List<ApprovalMatrixUsersListVm>> GetApprovalMatrixUsersList();
    Task<BaseResponse> CreateAsync(CreateApprovalMatrixUsersCommand createApprovalMatrixUsersCommand);
    Task<BaseResponse> UpdateAsync(UpdateApprovalMatrixUsersCommand updateApprovalMatrixUsersCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<ApprovalMatrixUsersDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsApprovalMatrixUsersNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<ApprovalMatrixUsersListVm>> GetPaginatedApprovalMatrixUsers(GetApprovalMatrixUsersPaginatedListQuery query);
    #endregion
}
