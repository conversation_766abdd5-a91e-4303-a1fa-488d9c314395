﻿const componentURL = {
    createOrUpdate: "Admin/ComponentType/CreateOrUpdate",
    delete: "Admin/ComponentType/Delete",
    getFormTypeNames: "Admin/FormType/GetFormTypeNames",
    getPagination: "/Admin/ComponentType/GetPagination",
    nameExist: "Admin/ComponentType/ComponentTypeNameExist"
};
let selectedValues = [];
let versions = [];
let btnDisableComponentType = false;
let dataTable = "";
var RootUrl = "/";

//$(async function () {
preventSpecialKeys('#searchInputCT, #componentTypeName');
$('#componentTypelogo').hide();
dataTable = $('#componentTypeTable').DataTable(
    {
        language: {
            decimal: ",",
            paginate: {
                next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next" ></i>',
                previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous" ></i>'
            },
            infoFiltered: ""
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        processing: true,
        serverSide: true,
        filter: true,
        Sortable: true,
        order: [],
        fixedColumns: { left: 1, right: 1 },
        "ajax": {
            "type": "GET",
            "url": componentURL.getPagination,
            "dataType": "json",
            "data": function (d) {
                const sortIndex = d?.order[0]?.column ?? '';
                d.PageNumber = Math.ceil(d.start / d.length) + 1;
                d.pageSize = d.length;
                d.searchString = selectedValues.length ? selectedValues.join(';') : $('#searchInputCT').val();
                d.sortColumn = ["", "formTypeName", "componentName", "version"][sortIndex] ?? "";;
                d.SortOrder = d?.order[0]?.dir || 'asc';
                let selectedVal = $("#cmptType").val();
                d.formTypeId = selectedVal === "All" ? "" : selectedVal;
                selectedValues.length = 0;
            },
            "dataSrc": function (json) {
                if (json?.success) {
                    let compTypeDatas = json?.data;
                    compTypeDatas?.data.forEach(function (value, index) {
                        $('#cmptType').append('<option title="' + value.formTypeId + '" value="' + value.formTypeId + '">' + value.formTypeName + '</option>')
                    });
                    $("#cmptType option").each(function () {
                        $(this).siblings('[value="' + this.value + '"]').remove()
                    })
                    const data = compTypeDatas;
                    json.recordsTotal = data?.totalPages || 0;
                    json.recordsFiltered = data?.totalCount || 0;
                    data?.data?.forEach(d => d.properties = JSON.parse(d.properties));
                    $(".pagination-column").toggleClass("disabled", !data?.data?.length);
                    return data?.data;
                }
                else {
                    errorNotification(json);
                }
            },
        },
        "columnDefs": [
            { "targets": [1, 2, 3], "className": "truncate" }
        ],
        "columns": [
            {
                "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                "render": function (data, type, row, meta) {
                    return type === 'display' ? meta?.row + 1 : data;
                }
            },
            {
                "data": "formTypeName", "name": "Type", "autoWidth": true,
                "render": function (data, type, row) {
                    return type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data;
                }
            },
            {
                "data": null, name: 'Name', autowidth: true,
                "render": function (data, type, row) {
                    const { name = 'NA', icon = 'NA' } = row?.properties || {};
                    return type === 'display' ? `<span title="${name}"><i class="${icon} me-1"></i>${name}</span>` : data;
                }
            },
            {
                "data": 'properties', name: 'version', autowidth: true,
                "render": function (data, type) {
                    const version = data?.version?.replace(/[[\]"\s]/g, "") || 'NA';
                    return type === 'display' ? `<span title="${version}">${version}</span>` : data;
                }
            },
            {
                "orderable": false,
                "render": function (data, type, row) {
                    if (row?.isMapped) {
                        return `
<div class="d-flex align-items-center gap-2">
<span role="button" title="Edit" class="form-delete-disable">
<i class="cp-edit"></i>
</span>
<span role="button" title="Delete" class="form-delete-disable">
<i class="cp-Delete "></i>
</span>
</div>`;
                    } else {
                        return `
<div class="d-flex align-items-center gap-2">
<span role="button" title="Edit" class="editButtonCT" data-servertype='${JSON.stringify(row)}'>
<i class="cp-edit"></i>
</span>
<span role="button" title="Delete" class="deleteButtonCT" data-servertype-id="${row?.id}"
data-name="${row?.properties?.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
<i class="cp-Delete "></i>
</span>
</div>`;
                    }
                }
            }
        ],
        "rowCallback": function (row, data, index) {
            var api = this.api();
            var startIndex = api?.context[0]?._iDisplayStart;
            var counter = startIndex + index + 1;
            $('td:eq(0)', row).html(counter);
        },
        initComplete: function () {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
        },
    });

$('#searchInputCT').on('input', commonDebounce(async function () {
    let val = $(this).val().trim().replace(/\s+/g, ' ');
    $(this).val(val);
    selectedValues = ['#compType', '#compName', '#compVersion']
        .map(id => $(id)).filter(chk => chk.is(':checked'))
        .map(chk => chk.val() + val);
    dataTable.ajax.reload(json => {
        $('.dataTables_empty').text(!json?.recordsFiltered ? (val ? 'No matching records found' : 'No Data Found') : '');
    });
}));

$("#cmptType").on("change", function () {
    dataTable.ajax.reload()
})
$('#componentTypeName').on('keyup', commonDebounce(function () {
    const val = $(this).val().replace(/\s{2,}/g, ' ');
    $(this).val(val);
    $('#componentName').val(val);
    moduleNameValidation('componenttype', val, componentURL.nameExist, $("#componentNameError"), "Enter name", {
        serverTypeId: $('#textServerTypeId').val(),
        serverTypeName: val
    });
}));

$("#saveButtonCT").on("click", async function (event) {
    const buttons = document.querySelectorAll(".remove-button");
    const componentVersion = buttons.length ? JSON.stringify([...buttons].map(b => b.textContent.replace("X", "").trim())) : "";
    let componentType = $("#selectComponentType").val();
    let data = {
        serverTypeId: $('#textServerTypeId').val(),
        serverTypeName: $('#componentTypeName').val()
    };
    let compNameValidation = await moduleNameValidation('componenttype', $('#componentTypeName').val(), componentURL.nameExist, $("#componentNameError"), "Enter name", data);
    let versionValidation = componentVersionValidation(componentVersion, " Add version", "componentTypeVersionError");
    let compTypeValidation = componentTypeValidation(componentType, " Select type", "componentTypeError");
    if (compNameValidation && versionValidation && compTypeValidation) {
        if (componentType === 'Replication') {
            enableInfraComponent('isServerChecked', '#enableInfraServer');
            enableInfraComponent('isDatabaseChecked', '#enableInfraDatabase');
            enableInfraComponent('isReplicationChecked', '#enableInfrareplication');
            enableInfraComponent('isClusterChecked', '#enableInfraIsCluster');
            updateProperties()
        } else {
            $('#enableInfraServer, #enableInfraDatabase, #enableInfrareplication, #enableInfraIsCluster').val(false);
        }
        $("#versionDatas").val(componentVersion);
        let iconValue = $("#componentTypeIcon").val() || document.getElementById('componentTypelogo').className;
        $("#iconName").val(iconValue);
        let propertiesObject = {
            name: $("#componentTypeName").val(),
            version: componentVersion,
            icon: iconValue
        };
        if (!btnDisableComponentType) {
            btnDisableComponentType = true;
            let encryption = await propertyEncryption(propertiesObject);
            $("#propertiesData").val(encryption)
            const form = $('#createComponentForm')[0];
            const formData = new FormData(form);
            let response = await createOrUpdate(RootUrl + componentURL.createOrUpdate, formData);
            $('#createModal').modal('hide');
            if (response?.success) {
                notificationAlert("success", response?.data?.message);
                setTimeout(() => {
                    dataTableCreateAndUpdate($("#saveButtonCT"), dataTable);
                }, 2000)
            } else {
                errorNotification(response);
            }
            btnDisableComponentType = false;
        }
    }
});

$("#createModalForm").on("click", function () {
    $('#saveButtonCT').text('Save');
    $('#textServerTypeId').val('');
    $(".hideForReplication").show();
    $(".hideForSelectVersion, .hideInfraComponent, #componentTypelogo").hide();
    $("#selectedVersionDetails").empty();
    clearServerTypeErrorMessage();
    versions = [];
    let myCollapse = new bootstrap.Collapse(document.getElementById('collapseExample'), {
        toggle: false
    });
    myCollapse.hide();
});
$(".disable-button").css({
    "pointer-events": "none",
    "color": "gray",
    "cursor": "not-allowed"
});
$('#componentTypeTable')
    .on('click', '.deleteButtonCT', function () {
        $("#deleteData").attr("title", $(this).data('name')).text($(this).data('name'));
        $('#textDeleteId').val($(this).data('servertype-id'));
    })
    .on('click', '.editButtonCT', function () {
        clearServerTypeErrorMessage();
        populateDatabaseModalFields($(this).data("servertype"));
        $('#saveButtonCT').text('Update');
        $('#createModal').modal('show');
    });
$("#confirmDeleteButton").on("click", async function () {
    const form = $('#deleteComponentType')[0];
    const formData = new FormData(form);
    if (!btnDisableComponentType) {
        btnDisableComponentType = true;
        let response = await deleteData(RootUrl + componentURL.delete, formData);
        $("#DeleteModal").modal("hide");
        if (response?.success) {
            notificationAlert("success", response?.data?.message);
            setTimeout(() => {
                dataTableDelete(dataTable);
            }, 2000)
        } else {
            errorNotification(response);
        }
        btnDisableComponentType = false;
    }
});
$('#Server_icons, #Database_icons').hide();
$('#selectComponentType').on('change', function () {
    let component = $('#selectComponentType').val();
    $("#formTypeId").val($("#selectComponentType :selected").attr("id"));
    $('#Server_icons').toggle(component === 'Server');
    $('#Database_icons').toggle(component === 'Database');
    $('#collapseExample').removeClass('show');
    validateComponentType();
});
formTypeNames();
//});

$(document).on('click', '#iconchange td', function () {
    const className = this.firstElementChild?.className?.split(' ')[0];
    $('#componentTypelogo').removeClass().addClass(className);
    $('#componentTypeIcon').val(className);
    $('#collapseExample').removeClass('show')
});
function updateProperties() {
    properties = [{
        Server: $('#isServerChecked').prop('checked'),
        Database: $('#isDatabaseChecked').prop('checked'),
        Replication: $('#isReplicationChecked').prop('checked'),
        isCluster: $('#isClusterChecked').prop('checked'),
        isRAC: $('#isRacChecked').prop('checked'),
        MultiPR: $('#isMultiPRChecked').prop('checked'),
        MultiDR: $('#isMultiDRChecked').prop('checked')
    }];
    let propertiesVal = (JSON.stringify(properties));
    $('#componentProperties').val(propertiesVal)
}
async function formTypeNames(value = null) {
    let result = await getRequest(RootUrl + componentURL.getFormTypeNames);
    if (result?.length) {
        const options = [];
        let componentType = $('#selectComponentType');
        componentType.empty().append($('<option>').val("").text("Select Type"));
        result?.forEach(function (item) {
            const formTypeName = item?.formTypeName || "";
            const modifiedType = formTypeName?.toLowerCase()?.replace(/\s+/g, "");
            const displayText = modifiedType === "singlesignon" ? "Single Sign-On" : formTypeName;
            options.push($('<option>').val(formTypeName).text(displayText).attr('id', item?.id)
            );
        });
        componentType.append(options);
        if (value) {
            $('#selectComponentType').val(value).trigger('change');
        }
    }
}
async function validateComponentType() {
    const serverTableBody =
        `<tbody id="serverIcon">
<tr>
<td><i title="Server" style="cursor:pointer" class="cp-server custom-cursor-on-hover"></i></td>
<td><i title="IBM-AIX" style="cursor:pointer" class="cp-IBM-AIX custom-cursor-on-hover"></i></td>
<td><i title="HP" style="cursor:pointer" class="cp-hp custom-cursor-on-hover"></i></td>
<td><i title="Linux" style="cursor:pointer" class="cp-linux" cursorshover="true"></i></td>
<td><i title="Solaris" style="cursor:pointer" class="cp-solaris custom-cursor-on-hover" cursorshover="true"></i></td>
<td><i title="MSSQL" style="cursor:pointer" class="cp-mssql custom-cursor-on-hover"></i></td>
<td><i title="Windows" style="cursor:pointer" class="cp-windows custom-cursor-on-hover" cursorshover="true"></i></td>
</tr>
<tr>
<td><i title="AIX" class="cp-aix custom-cursor-on-hover" cursorshover="true"></i></td>
</tr>
</tbody>`;
    const databaseTableBody =
        `<tbody id="databaseIcon">
<tr>
<td><i title="Database" style="cursor:pointer" class="cp-database custom-cursor-on-hover"></i></td>
<td><i title="Oracle" style="cursor:pointer" class="cp-oracle custom-cursor-on-hover"></i></td>
<td><i title="Postgres" style="cursor:pointer" class="cp-postgres custom-cursor-on-hover" cursorshover="true"></i></td>
<td><i title="MYSQL" style="cursor:pointer" class="cp-mysql custom-cursor-on-hover" cursorshover="true"></i></td>
<td><i title="MSSQL" style="cursor:pointer" class="cp-mssql custom-cursor-on-hover"></i></td>
<td><i title="Mongo DB" style="cursor:pointer" class="cp-mongo-db"></i></td>
<td><i title="IBM-AIX" style="cursor:pointer" class="cp-IBM-AIX custom-cursor-on-hover"></i></td>
</tr>
<tr>
<td><i title="Mailing System" style="cursor:pointer" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
<td><i title="Nutanix" style="cursor:pointer" class="cp-nutanix custom-cursor-on-hover"></i></td>
<td><i title="NetApp" style="cursor:pointer" class="cp-power-cli" cursorshover="true"></i></td>
<td><i title="VMware" style="cursor:pointer" class="cp-vmware custom-cursor-on-hover"></i></td>
<td><i title="Rsync" style="cursor:pointer" class="cp-rsync custom-cursor-on-hover"></i></td>
<td><i title="EMC" style="cursor:pointer" class="cp-EMC custom-cursor-on-hover"></i></td>
<td><i title="Oracle Ops Center" style="cursor:pointer" class="cp-microsoft custom-cursor-on-hover"></i></td>
</tr>
<tr>
<td><i title="Veritas Cluster" class="cp-veritas-cluster custom-cursor-on-hover"></i></td>
</tr>
</tbody>`;
    const replicationTableBody =
        `<tbody id="replicationIcon">
<div>
<tr>
<td><i title="Replication Type" style="cursor:pointer" class="cp-replication-type custom-cursor-on-hover" cursorshover="true"></i></td>
<td><i title="Oracle" style="cursor:pointer" class="cp-oracle custom-cursor-on-hover"></i></td>
<td><i title="Postgres" style="cursor:pointer" class="cp-postgres custom-cursor-on-hover" cursorshover="true"></i></td>
<td><i title="MYSQL" style="cursor:pointer" class="cp-mysql custom-cursor-on-hover" cursorshover="true"></i></td>
<td><i title="MSSQL" style="cursor:pointer" class="cp-mssql custom-cursor-on-hover"></i></td>
<td><i title="Mongo DB" style="cursor:pointer" class="cp-mongo-db"></i></td>
<td><i title="IBM-AIX" style="cursor:pointer" class="cp-IBM-AIX custom-cursor-on-hover"></i></td>
</tr>
<tr>
<td><i title="Mailing System" style="cursor:pointer" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
<td><i title="EMC" style="cursor:pointer" class="cp-EMC custom-cursor-on-hover"></i></td>
<td><i title="Nutanix" style="cursor:pointer" class="cp-nutanix custom-cursor-on-hover"></i></td>
<td><i title="NetApp" style="cursor:pointer" class="cp-power-cli" cursorshover="true"></i></td>
<td><i title="VMware" style="cursor:pointer" class="cp-vmware custom-cursor-on-hover"></i></td>
<td><i title="Rsync" style="cursor:pointer" class="cp-rsync-options custom-cursor-on-hover" cursorshover="true"></i></td>
<td><i title="RoboCopy" style="cursor:pointer" class="cp-robocopy custom-cursor-on-hover"></i></td>
</tr>
<tr>
<td><i title="Oracle Ops Center" style="cursor:pointer" class="cp-microsoft custom-cursor-on-hover"></i></td>
</tr>
</div>
</tbody>`;
    const singleSignOnTableBody =
        `<tbody id="singleSignonIcon">
<tr>
<td><i title="Single Sign-On" style="cursor:pointer" class="cp-single-sign_on custom-cursor-on-hover" cursorshover="true"></i></td>
</tr>
</tbody>`;
    const nodeTableBody =
        `<tbody id="nodeIcon">
<tr>
<td><i title="Node" style="cursor:pointer" class="cp-network custom-cursor-on-hover" cursorshover="true"></i></td>
</tr>
</tbody>`;
    const OtherTypeTableBody =
        `<tbody id="otherType">
<tr>
<td><i title="Mapping" style="cursor:pointer" class="cp-mapping custom-cursor-on-hover" cursorshover="true"></i></td>
</tr>
</tbody>`;
    const componentTableBodies = {
        "Server": serverTableBody,
        "Database": databaseTableBody,
        "Replication": replicationTableBody,
        "Single SignOn": singleSignOnTableBody,
        "Node": nodeTableBody
    };
    const componentIcons = {
        "Server": "cp-server",
        "Database": "cp-database",
        "Replication": "cp-replication-rotate",
        "Single SignOn": "cp-single-sign_on",
        "Node": "cp-network",
    };
    let componentType = $("#selectComponentType").val();
    $('#componentTypelogo').show();
    let table = document.getElementById('iconchange');
    while (table.firstChild) {
        table.removeChild(table.firstChild);
    }
    const selectedTableBody = componentTableBodies[componentType];
    table.insertAdjacentHTML('beforeend', selectedTableBody ? selectedTableBody : OtherTypeTableBody);
    const icon = componentIcons[componentType] || "cp-mapping";
    $("#componentTypelogo").attr("class", icon);
    $('#componentTypeIcon').val(icon);
    $('#collapseExample').addClass('hide');
    $("#componentNameError, #componentTypeVersionError").text("").removeClass('field-validation-error');
    componentTypeValidation(componentType, " Select type", "componentTypeError");
};
function componentTypeValidation(value, errorMessage, errorElement) {
    if (value === "Server" || value === "Database" || value == "") {
        $(".hideForReplication").show();
    } else {
        $(".hideForReplication, .hideForSelectVersion").hide();
        $("#selectedVersionDetails").empty();
    }
    $(".hideInfraComponent").toggle(value === "Replication");
    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
}
function validateComponentVersion() {
    let version = $("#selectedVersionDetails").val() || $("#componentTypeVersion").val();
    let inputField = document.getElementById('componentTypeVersion');
    inputField.addEventListener('keydown', function (event) {
        if (event.key === ' ') {
            const value = this.value;
            const selectionStart = this.selectionStart;
            const len = value.trim().length;
            if (len < selectionStart) {
                $('#componentTypeVersionError').text("Should not end with space").addClass('field-validation-error');
                event.preventDefault();
            }
        }
    });
    componentVersionValidation(version, " Add version", "componentTypeVersionError");
};
function componentVersionValidation(value, errorMessage, errorElement) {
    let componentType = $("#selectComponentType").val();
    if (componentType == "Server" || componentType == "Database" || componentType === "") {
        if (!value) {
            $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            $('#' + errorElement).text('').removeClass('field-validation-error');
            return true;
        }
    } else {
        $(".hideForReplication").hide();
        return true;
    }
}
function addVersion(version) {
    let $componentTypeVersionError = $("#componentTypeVersionError");
    $componentTypeVersionError.text("").removeClass('field-validation-error');
    if (version?.includes(',')) {
        versions?.push(...version?.split(','));
    } else if (versions?.includes(version)) {
        $componentTypeVersionError.text("Version already exists").addClass('field-validation-error');
    } else {
        $componentTypeVersionError.text("").removeClass('field-validation-error');
        versions?.push(version);
    }
    updateSelectedVersions();
}
function updateSelectedVersions() {
    $("#selectedVersionDetails").empty();
    if (versions?.length === 0) {
        $(".hideForSelectVersion").hide();
    }
    versions?.forEach((version, index) => {
        const versionElement = document.createElement("span");
        versionElement.style.marginRight = "10px";
        const addValue = document.createElement("button");
        addValue.innerHTML = `${version}<span >&nbsp; X</span>`;
        addValue.className = `remove-button btn btn-secondary rounded-pill btn-sm shadow mt-2`;
        addValue.title = "Remove";
        addValue.addEventListener("click", function () {
            removeVersion(index);
        });
        versionElement.appendChild(addValue);
        $("#selectedVersionDetails").append(versionElement);
    });
}
function removeVersion(index) {
    versions.splice(index, 1);
    updateSelectedVersions();
}
function addVersionData() {
    const data = $("#componentTypeVersion").val().trim();
    data ? (addVersion(data), $(".hideForSelectVersion").show()) : validateComponentVersion();
    $("#componentTypeVersion").val("");
}
function handleKeyPress(event) {
    if (event.keyCode === 13 && event.target.id === 'componentTypeVersion') {
        event.preventDefault();
        document.getElementById("btnSaveProfile").click();
    }
}
function enableInfraComponent(infraid, valueid) {
    $(valueid).val(document.getElementById(infraid).checked);
}
function clearServerTypeErrorMessage() {
    const errorElements = ['#componentTypeError', '#componentNameError', '#componentTypeVersionError'];
    clearInputFields('createComponentForm', errorElements);
}
async function populateDatabaseModalFields(serverTypeData) {
    const formTypeId = $("#selectComponentType :selected").attr("id");
    $("#formTypeId").val(formTypeId);
    $('#textServerTypeId').val(serverTypeData?.id);
    const ComponentCheck = JSON.parse(serverTypeData.componentProperties);
    if (Array.isArray(ComponentCheck) && ComponentCheck?.length) {
        $('#isDatabaseChecked').prop('checked', ComponentCheck[0]?.Database);
        $('#isReplicationChecked').prop('checked', ComponentCheck[0]?.Replication);
        $('#isClusterChecked').prop('checked', ComponentCheck[0]?.isCluster);
        $('#isRacChecked').prop('checked', ComponentCheck[0]?.isRAC);
        $('#isMultiPRChecked').prop('checked', ComponentCheck[0]?.MultiPR);
        $('#isMultiDRChecked').prop('checked', ComponentCheck[0]?.MultiDR);
    }
    $("#componentTypeName").val(serverTypeData?.properties?.name);
    $("#componentName").val(serverTypeData?.properties?.name);
    $("#selectedVersionDetails").empty();
    versions = [];
    const className = serverTypeData?.properties?.icon;
    let formTypeName = serverTypeData?.formTypeName;
    const isServerOrDatabase = formTypeName === "Server" || formTypeName === "Database" || formTypeName === "";
    $(".hideForReplication, .hideForSelectVersion").toggle(isServerOrDatabase);
    await formTypeNames(serverTypeData?.formTypeName).then(() => {
        $("#componentTypelogo").attr("class", className);
        $('#componentTypeIcon').val(className);
    })
    if (serverTypeData?.properties?.version) {
        addVersion(serverTypeData?.properties?.version?.replace(/[\[\]"\s]/g, ""));
    }
}