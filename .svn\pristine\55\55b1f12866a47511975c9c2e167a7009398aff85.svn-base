﻿using ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.DRReadyStatus.Commands;

public class DeleteDrReadyStatusTests : IClassFixture<DrReadyStatusFixture>
{
    private readonly DrReadyStatusFixture _drReadyStatusFixture;

    private readonly Mock<IDrReadyStatusRepository> _mockDrReadyStatusRepository;

    private readonly DeleteDRReadyStatusCommandHandler _handler;

    public DeleteDrReadyStatusTests(DrReadyStatusFixture drReadyStatusFixture)
    {
        _drReadyStatusFixture = drReadyStatusFixture;

        _mockDrReadyStatusRepository = DrReadyStatusRepositoryMocks.DeleteDrReadyStatusRepository(_drReadyStatusFixture.DrReadyStatuses);

        _handler = new DeleteDRReadyStatusCommandHandler(_mockDrReadyStatusRepository.Object);
    }

    [Fact]
    public async Task Handle_UpdateReferenceIdAsyncIsActiveFalse_When_DRReadyStatusDeleted()
    {
        var result = await _handler.Handle(new DeleteDRReadyStatusCommand { Id = _drReadyStatusFixture.DrReadyStatuses[0].ReferenceId }, CancellationToken.None);

        Assert.True(result.Success);

        var drReadyStatus = await _mockDrReadyStatusRepository.Object.GetByReferenceIdAsync(_drReadyStatusFixture.DrReadyStatuses[0].ReferenceId);
        Assert.False(drReadyStatus.IsActive);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulDRReadyStatusResponse_When_DRReadyStatusDeleted()
    {
        var result = await _handler.Handle(new DeleteDRReadyStatusCommand { Id = _drReadyStatusFixture.DrReadyStatuses[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteDRReadyStatusResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Return_IsActive_False_When_DeleteReferenceIdAsync_DRReadyStatus()
    {
        await _handler.Handle(new DeleteDRReadyStatusCommand { Id = _drReadyStatusFixture.DrReadyStatuses[0].ReferenceId }, CancellationToken.None);

        var drReadyStatus = await _mockDrReadyStatusRepository.Object.GetByReferenceIdAsync(_drReadyStatusFixture.DrReadyStatuses[0].ReferenceId);

        drReadyStatus.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteDRReadyStatusCommand { Id = _drReadyStatusFixture.DrReadyStatuses[0].ReferenceId }, CancellationToken.None);

        _mockDrReadyStatusRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockDrReadyStatusRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.DRReadyStatus>()), Times.Once);
    }
}