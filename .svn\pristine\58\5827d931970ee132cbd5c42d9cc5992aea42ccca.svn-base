﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.EscalationMatrixLevel.Events.Delete;

public class EscalationMatrixLevelDeletedEventHandler : INotificationHandler<EscalationMatrixLevelDeletedEvent>
{
    private readonly ILogger<EscalationMatrixLevelDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;

    private readonly ILoggedInUserService _userService;

    public EscalationMatrixLevelDeletedEventHandler(ILoggedInUserService userService,
        IUserActivityRepository userActivityRepository, ILogger<EscalationMatrixLevelDeletedEventHandler> logger)
    {
        _userService = userService;
        _userActivityRepository = userActivityRepository;
        _logger = logger;
    }


    public async Task Handle(EscalationMatrixLevelDeletedEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.EscalationMatrix.ToString(),
            Action = $"{ActivityType.Delete} {Modules.EscalationMatrixLevel}",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $" Escalation Matrix Level '{notification.EscLevName}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Escalation Matrix Level '{notification.EscLevName}' deleted successfully.");
    }
}