﻿using ContinuityPatrol.Domain.ViewModels.TemplateModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Application.Features.Template.Commands.Create;
using ContinuityPatrol.Application.Features.Template.Commands.Update;
using ContinuityPatrol.Application.Features.Template.Events.PaginatedView;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Web.Areas.ITAutomation.Controllers;

[Area("ITAutomation")]
public class WorkflowTemplateController : Controller
{
    private readonly IPublisher _publisher;
    private readonly ILogger<WorkflowTemplateController> _logger;
    private readonly IDataProvider _provider;
    private readonly IMapper _mapper;
    
    public WorkflowTemplateController(IPublisher publisher,ILogger<WorkflowTemplateController> logger, IDataProvider provider, IMapper mapper)
    {
        _publisher = publisher;
        _logger = logger;
        _provider = provider;
        _mapper = mapper;
    }
   
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in WorkflowTemplate");

        await _publisher.Publish(new TemplatePaginatedEvent());

        return View();
    }

    public async Task<JsonResult> GetTemplateList()
    {
        _logger.LogDebug("Entering GetTemplateList method in WorkflowTemplate");

        try
        {
            var templateList = await _provider.Template.GetTemplateList();

            _logger.LogDebug("Successfully retrieved WorkflowTemplate list in WorkflowTemplate");

            return Json(new { Success = true, data = templateList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on WorkflowTemplate page while retrieving WorkflowTemplate list.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public string TemplateDataEncrypt(string data)
    {
        try
        {
            _logger.LogDebug("Entering TemplateDataEncrypt method in WorkflowTemplate");

            var templateEncryptData = SecurityHelper.Encrypt(data);

            _logger.LogDebug("Successfully retrieved WorkflowTemplate Data Encrypt in WorkflowTemplate");

            return templateEncryptData;
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on WorkflowTemplate page while retrieving WorkflowTemplate Data Encrypt.", ex);

            return "";
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public string TemplateDataDecrypt(string data)
    {
        _logger.LogDebug("Entering TemplateDataDecrypt method in WorkflowTemplate");

        try
        {
            var templateDecryptData = SecurityHelper.Decrypt(data);

            _logger.LogDebug("Successfully retrieved WorkflowTemplate Data Decrypt in WorkflowTemplate");

            return templateDecryptData;

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on WorkflowTemplate page while retrieving WorkflowTemplate Data Decrypt.", ex);

            return "";
        }
    }

    public async Task<IActionResult> GetTemplateById(string id)
    {
        _logger.LogDebug("Entering Get Template By Id method in WorkflowTemplate");


        if (id.IsNullOrWhiteSpace())
        {
            return Json(new { Success = false, message = "TemplateId is not valid format" });
        }

        try
        {
            var result = await _provider.Template.GetByReferenceId(id);

            _logger.LogDebug($"Successfully retrieved Template details for Id :{id} in WorkflowTemplate page");

            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on WorkflowTemplate page while retrieving the WorkflowTemplate details by id.", ex);

            return ex.GetJsonException();
        }
    }

    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in WorkflowTemplate");

        try
        {
            _logger.LogDebug($"Deleting WorkflowTemplate Details by Id '{id}'");

            var template = await _provider.Template.DeleteAsync(id);

            TempData.NotifySuccess(template.Message);

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred while deleting record on WorkflowTemplate.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }     
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateTemplate(TemplateViewModel template)
    {
        _logger.LogDebug("Entering CreateTemplate method in WorkflowTemplate");

        var templateId = Request.Form["id"].ToString();

        try
        {
            if (templateId.IsNullOrWhiteSpace())
            {
                _logger.LogDebug($"Creating WorkflowTemplate '{template.Name}'");

                var workflow = _mapper.Map<CreateTemplateCommand>(template);

                var result = await _provider.Template.CreateAsync(workflow);

                return Json(new { Success = true, data = result });
            }
            else
            {
                _logger.LogDebug($"Updating WorkflowTemplate '{template.Name}'");

                var workflow = _mapper.Map<UpdateTemplateCommand>(template);

                var result = await _provider.Template.UpdateAsync(workflow);

                return Json(new { Success = true, data = result });

            }
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on WorkflowTemplate page while processing the request for Create Template.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<bool> TemplateNameExist(string name, string id)
    {
        _logger.LogDebug("Entering TemplateNameExist method in WorkflowTemplate");

        try
        {
            var nameExist = await _provider.Template.IsTemplateNameExist(name, id);

            _logger.LogDebug("Returning result for TemplateNameExist on WorkflowTemplate");

            return nameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on WorkflowTemplate while checking if WorkflowTemplate name exists for : {name}.", ex);

            return false;
        }
    }
}