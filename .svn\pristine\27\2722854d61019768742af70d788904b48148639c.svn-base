﻿namespace ContinuityPatrol.Application.Features.ReplicationMaster.Commands.Create;

public class
    CreateReplicationMasterCommandHandler : IRequestHandler<CreateReplicationMasterCommand,
        CreateReplicationMasterResponse>
{
    private readonly IMapper _mapper;
    private readonly IReplicationMasterRepository _replicationMasterRepository;

    public CreateReplicationMasterCommandHandler(IReplicationMasterRepository replicationMasterRepository,
        IMapper mapper)
    {
        _replicationMasterRepository = replicationMasterRepository;
        _mapper = mapper;
    }

    public async Task<CreateReplicationMasterResponse> Handle(CreateReplicationMasterCommand request,
        CancellationToken cancellationToken)
    {
        var replicationMaster = _mapper.Map<Domain.Entities.ReplicationMaster>(request);

        replicationMaster = await _replicationMasterRepository.AddAsync(replicationMaster);

        var response = new CreateReplicationMasterResponse
        {
            Message = Message.Create(nameof(Domain.Entities.ReplicationMaster), replicationMaster.Name),

            ReplicationMasterId = replicationMaster.ReferenceId
        };

        return response;
    }
}