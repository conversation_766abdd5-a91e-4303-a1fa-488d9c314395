using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ImpactAvailabilityFixture : IDisposable
{
    public List<ImpactAvailability> ImpactAvailabilityPaginationList { get; set; }
    public List<ImpactAvailability> ImpactAvailabilityList { get; set; }
    public ImpactAvailability ImpactAvailabilityDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public ImpactAvailabilityFixture()
    {
        var fixture = new Fixture();

        ImpactAvailabilityList = fixture.Create<List<ImpactAvailability>>();

        ImpactAvailabilityPaginationList = fixture.CreateMany<ImpactAvailability>(20).ToList();

        ImpactAvailabilityDto = fixture.Create<ImpactAvailability>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
