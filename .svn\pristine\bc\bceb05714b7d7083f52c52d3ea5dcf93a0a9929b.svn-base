using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class FiaCostFixture : IDisposable
{
    public List<FiaCost> FiaCostPaginationList { get; set; }
    public List<FiaCost> FiaCostList { get; set; }
    public FiaCost FiaCostDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public FiaCostFixture()
    {
        var fixture = new Fixture();

        FiaCostList = fixture.Create<List<FiaCost>>();

        FiaCostPaginationList = fixture.CreateMany<FiaCost>(20).ToList();

        FiaCostPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FiaCostPaginationList.ForEach(x => x.IsActive = true);

        FiaCostList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FiaCostList.ForEach(x => x.IsActive = true);

        FiaCostDto = fixture.Create<FiaCost>();
        FiaCostDto.ReferenceId = Guid.NewGuid().ToString();
        FiaCostDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
