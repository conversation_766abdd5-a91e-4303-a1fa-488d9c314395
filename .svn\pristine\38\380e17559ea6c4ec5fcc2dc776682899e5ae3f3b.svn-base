﻿namespace ContinuityPatrol.Application.Features.Site.Queries.GetNameUnique;

public class GetSiteNameUniqueQueryHandler : IRequestHandler<GetSiteNameUniqueQuery, bool>
{
    private readonly ISiteRepository _siteRepository;

    public GetSiteNameUniqueQueryHandler(ISiteRepository siteRepository)
    {
        _siteRepository = siteRepository;
    }

    public async Task<bool> Handle(GetSiteNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _siteRepository.IsSiteNameExist(request.SiteName, request.SiteId);
    }
}