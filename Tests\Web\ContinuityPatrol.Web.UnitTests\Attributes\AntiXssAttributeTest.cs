﻿using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Web.Attributes;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Routing;

namespace ContinuityPatrol.Web.UnitTests.Attributes;

public class AntiXssAttributeTest
{
    private ActionExecutingContext CreateContext(Dictionary<string, object> actionArguments)
    {
        var actionContext = new ActionContext
        {
            RouteData = new RouteData(),
            ActionDescriptor = new ControllerActionDescriptor(),
            HttpContext = new DefaultHttpContext()
        };

        return new ActionExecutingContext(
            actionContext,
            new List<IFilterMetadata>(),
            actionArguments!,
            controller: null!);
    }


    [Fact]
    public void OnActionExecuting_StringParameterWithScript_ThrowsAntiXssException()
    {
        // Arrange
        var attribute = new AntiXssAttribute();
        var args = new Dictionary<string, object>
        {
            { "input", "<script>alert('xss')</script>" }
        };
        var context = CreateContext(args);

        // Act & Assert
        var ex = Assert.Throws<AntiXssException>(() => attribute.OnActionExecuting(context));
        Assert.Equal("A Potentially dangerous request was detected.", ex.Message);
        Assert.Equal(5005, ex.ErrorCode);
    }
    [Fact]
    public void OnActionExecuting_StringParameterWithoutScript_DoesNotThrow()
    {
        var attribute = new AntiXssAttribute();
        var args = new Dictionary<string, object>
        {
            { "input", "Hello World" }
        };
        var context = CreateContext(args);

        var exception = Record.Exception(() => attribute.OnActionExecuting(context));

        Assert.Null(exception);
    }

    public class DummyRequest
    {
        public string? Name { get; set; }
    }

    [Fact]
    public void OnActionExecuting_ObjectPropertyWithScript_ThrowsAntiXssException()
    {
        var attribute = new AntiXssAttribute();
        var obj = new DummyRequest { Name = "<script>alert('xss')</script>" };
        var args = new Dictionary<string, object>
        {
            { "model", obj }
        };
        var context = CreateContext(args);

        var ex = Assert.Throws<AntiXssException>(() => attribute.OnActionExecuting(context));
        Assert.Equal(5005, ex.ErrorCode);
    }

    [Fact]
    public void OnActionExecuting_ObjectWithSafeProperty_DoesNotThrow()
    {
        var attribute = new AntiXssAttribute();
        var obj = new DummyRequest { Name = "Safe Value" };
        var args = new Dictionary<string, object>
        {
            { "model", obj }
        };
        var context = CreateContext(args);

        var ex = Record.Exception(() => attribute.OnActionExecuting(context));

        Assert.Null(ex);
    }
    [Fact]
    public void OnActionExecuting_ObjectPropertyIsNull_DoesNotThrow()
    {
        var attribute = new AntiXssAttribute();
        var args = new Dictionary<string, object>
        {
            { "model", new DummyRequest { Name = null } }
        };

        var context = CreateContext(args);

        var ex = Record.Exception(() => attribute.OnActionExecuting(context));
        Assert.Null(ex);
    }

    [Fact]
    public void OnActionExecuting_NullParameter_SkipsCheck()
    {
        var attribute = new AntiXssAttribute();
        var args = new Dictionary<string, object>
        {
            { "input", null! }
        };
        var context = CreateContext(args);

        var ex = Record.Exception(() => attribute.OnActionExecuting(context));
        Assert.Null(ex);
    }

    [Fact]
    public void OnActionExecuting_EmptyStringArgument_DoesNotThrow()
    {
        var attribute = new AntiXssAttribute();
        var args = new Dictionary<string, object>
        {
            { "input", "" }
        };
        var context = CreateContext(args);

        var ex = Record.Exception(() => attribute.OnActionExecuting(context));
        Assert.Null(ex);
    }

    [Fact]
    public void OnActionExecuting_IntegerArgument_DoesNotThrow()
    {
        var attribute = new AntiXssAttribute();
        var args = new Dictionary<string, object>
        {
            { "number", 42 }
        };
        var context = CreateContext(args);

        var ex = Record.Exception(() => attribute.OnActionExecuting(context));
        Assert.Null(ex);
    }
    [Fact]
    public void OnActionExecuted_DoesNotThrow()
    {
        // Arrange
        var attribute = new AntiXssAttribute();

        var actionContext = new ActionContext
        {
            HttpContext = new DefaultHttpContext(),
            RouteData = new RouteData(),
            ActionDescriptor = new ControllerActionDescriptor()
        };

        var executedContext = new ActionExecutedContext(
            actionContext,
            new List<IFilterMetadata>(),
            controller: null!
        );

        // Act
        var exception = Record.Exception(() => attribute.OnActionExecuted(executedContext));

        // Assert
        Assert.Null(exception); // Passes if no exception is thrown
    }

}