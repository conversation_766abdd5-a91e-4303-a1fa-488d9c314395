﻿namespace ContinuityPatrol.Application.Features.TeamResource.Queries.GetDetail;

public class GetTeamResourceDetailQueryHandler : IRequestHandler<GetTeamResourceDetailQuery, TeamResourceDetailVm>
{
    private readonly IMapper _mapper;
    private readonly ITeamResourceRepository _teamResourceRepository;

    public GetTeamResourceDetailQueryHandler(IMapper mapper, ITeamResourceRepository teamResourceRepository)
    {
        _mapper = mapper;
        _teamResourceRepository = teamResourceRepository;
    }

    public async Task<TeamResourceDetailVm> Handle(GetTeamResourceDetailQuery request,
        CancellationToken cancellationToken)
    {
        var teamResource = await _teamResourceRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(teamResource, nameof(Domain.Entities.TeamResource),
            new NotFoundException(nameof(Domain.Entities.TeamResource), request.Id));

        var teamResourceDetailDto = _mapper.Map<TeamResourceDetailVm>(teamResource);

        return teamResourceDetailDto == null
            ? throw new NotFoundException(nameof(Domain.Entities.TeamResource), request.Id)
            : teamResourceDetailDto;
    }
}