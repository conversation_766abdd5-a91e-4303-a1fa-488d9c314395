﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.BiaRules.Events.PaginatedView;

public class BiaRulesPaginatedEventHandler : INotificationHandler<BiaRulesPaginatedEvent>
{
    private readonly ILogger<BiaRulesPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public BiaRulesPaginatedEventHandler(ILoggedInUserService userService,
        ILogger<BiaRulesPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(BiaRulesPaginatedEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.BiaRules.ToString(),
            Action = $"{ActivityType.View} {Modules.BiaRules}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "BIA Rules viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("BIA Rules viewed");
    }
}