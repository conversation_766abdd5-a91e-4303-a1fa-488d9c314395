namespace ContinuityPatrol.Application.Features.FiaTemplate.Commands.Create;

public class CreateFiaTemplateCommandValidator : AbstractValidator<CreateFiaTemplateCommand>
{
    private readonly IFiaTemplateRepository _fiaTemplateRepository;

    public CreateFiaTemplateCommandValidator(IFiaTemplateRepository fiaTemplateRepository)
    {
        _fiaTemplateRepository = fiaTemplateRepository;
        //RuleFor(p => p)
        //    .Must(IsValidGUID).WithMessage("Invalid id");

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.Description)
            .MaximumLength(250).WithMessage("{PropertyName} Maximum 250 characters.")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("{PropertyName} contains invalid characters.")
            .When(p => p.Description.IsNotNullOrWhiteSpace());

        RuleFor(p => p)
            .MustAsync(IsTemplateNameUnique).WithMessage("A same Name already exists.");
    }


    private async Task<bool> IsTemplateNameUnique(CreateFiaTemplateCommand createFiaTemplateCommand,
        CancellationToken token)
    {
        return !await _fiaTemplateRepository.IsNameExist(createFiaTemplateCommand.Name, string.Empty);
    }
}