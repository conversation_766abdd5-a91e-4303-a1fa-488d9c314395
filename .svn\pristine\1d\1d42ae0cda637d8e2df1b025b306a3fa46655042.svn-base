﻿using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.SingleSignOn.Commands;

public class DeleteSingleSignOnTests : IClassFixture<SingleSignOnFixture>
{
    private readonly SingleSignOnFixture _singleSignOnFixture;
    private readonly Mock<ISingleSignOnRepository> _mockSingleSignOnRepository;
    private readonly DeleteSingleSignOnCommandHandler _handler;

    public DeleteSingleSignOnTests(SingleSignOnFixture singleSignOnFixture)
    {
        _singleSignOnFixture = singleSignOnFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockSingleSignOnRepository = SingleSignOnRepositoryMocks.DeleteSingleSignOnRepository(_singleSignOnFixture.SingleSignOns);

        _handler = new DeleteSingleSignOnCommandHandler(_mockSingleSignOnRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_Success_When_Delete_SingleSignOn()
    {
        var validGuid = Guid.NewGuid();

        _singleSignOnFixture.SingleSignOns[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteSingleSignOnCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteSingleSignOnResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Return_IsActive_False_When_Delete_SingleSignOn()
    {
       
        var validGuid = Guid.NewGuid();

        _singleSignOnFixture.SingleSignOns[0].ReferenceId = validGuid.ToString();


        await _handler.Handle(new DeleteSingleSignOnCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var singleSignOn = await _mockSingleSignOnRepository.Object.GetByReferenceIdAsync(_singleSignOnFixture.SingleSignOns[0].ReferenceId);

        singleSignOn.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_NotFoundException_When_InvalidSingleSignOnId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteSingleSignOnCommand { Id = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {

        await _handler.Handle(new DeleteSingleSignOnCommand { Id = _singleSignOnFixture.SingleSignOns[0].ReferenceId }, CancellationToken.None);

        _mockSingleSignOnRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockSingleSignOnRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.SingleSignOn>()), Times.Once);
    }
}