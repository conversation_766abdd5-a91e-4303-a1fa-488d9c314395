using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class NodeWorkflowExecutionFixture : IDisposable
{
    public List<NodeWorkflowExecution> NodeWorkflowExecutionPaginationList { get; set; }
    public List<NodeWorkflowExecution> NodeWorkflowExecutionList { get; set; }
    public NodeWorkflowExecution NodeWorkflowExecutionDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public NodeWorkflowExecutionFixture()
    {
        var fixture = new Fixture();

        NodeWorkflowExecutionList = fixture.Create<List<NodeWorkflowExecution>>();

        NodeWorkflowExecutionPaginationList = fixture.CreateMany<NodeWorkflowExecution>(20).ToList();

        NodeWorkflowExecutionDto = fixture.Create<NodeWorkflowExecution>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
