using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.CyberJobManagementModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetPaginatedList;

public class GetCyberJobManagementPaginatedListQueryHandler : IRequestHandler<GetCyberJobManagementPaginatedListQuery,
    PaginatedResult<CyberJobManagementListVm>>
{
    private readonly ICyberJobManagementRepository _cyberJobManagementRepository;
    private readonly IMapper _mapper;

    public GetCyberJobManagementPaginatedListQueryHandler(IMapper mapper,
        ICyberJobManagementRepository cyberJobManagementRepository)
    {
        _mapper = mapper;
        _cyberJobManagementRepository = cyberJobManagementRepository;
    }

    public async Task<PaginatedResult<CyberJobManagementListVm>> Handle(GetCyberJobManagementPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new CyberJobManagementFilterSpecification(request.SearchString);

        var queryable= await _cyberJobManagementRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var cyberJobManagementList = _mapper.Map<PaginatedResult<CyberJobManagementListVm>>(queryable);

        return cyberJobManagementList;
        //var queryable = _cyberJobManagementRepository.GetPaginatedQuery();

        //var productFilterSpec = new CyberJobManagementFilterSpecification(request.SearchString);

        //var cyberJobManagementList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<CyberJobManagementListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return cyberJobManagementList;
    }
}