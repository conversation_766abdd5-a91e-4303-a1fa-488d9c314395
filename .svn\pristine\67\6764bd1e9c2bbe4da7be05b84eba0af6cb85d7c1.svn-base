﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Repositories;

public class SolutionHistoryRepository : BaseRepository<SolutionHistory>, ISolutionHistoryRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public SolutionHistoryRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public Task<List<SolutionHistory>> GetSolutionHistoryByActionId(string actionId)
    {
        Guard.Against.InvalidGuidOrEmpty(actionId, "ActionId", "ActionId cannot be invalid");

        if (_loggedInUserService.IsParent)
            return Task.FromResult(_dbContext.SolutionHistories
                .Where(solutionHistory => solutionHistory.ActionId.Equals(actionId) && solutionHistory.IsActive)
                .OrderByDescending(e => e.Id).ToList());

        return Task.FromResult(_dbContext.SolutionHistories
            .Where(solutionHistory => solutionHistory.ActionId.Equals(actionId) && solutionHistory.IsActive &&
                                      solutionHistory.CompanyId.Equals(_loggedInUserService.CompanyId))
            .OrderByDescending(e => e.Id).ToList());
    }

    public override Task<IReadOnlyList<SolutionHistory>> ListAllAsync()
    {
        return _loggedInUserService.IsParent
            ? base.ListAllAsync()
            : FindByFilterAsync(solutionHistory => solutionHistory.CompanyId.Equals(_loggedInUserService.CompanyId));
    }

    public override Task<SolutionHistory> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilterAsync(solutionHistory =>
                solutionHistory.ReferenceId.Equals(id) &&
                solutionHistory.CompanyId.Equals(_loggedInUserService.CompanyId)).Result.SingleOrDefault());
    }
}