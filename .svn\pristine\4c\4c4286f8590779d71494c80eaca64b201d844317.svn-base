﻿namespace ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetActionId;

public class WorkflowPredictionListByActionIdVm
{
    public string Id { get; set; }
    public string ActionId { get; set; }
    public string ActionName { get; set; }
    public int Count { get; set; }
    public string NextPossibleId { get; set; }
    public string NodeId { get; set; }
    public string NextPossibleActionName { get; set; }
    public string LastModifiedDate { get; set; }
}

public class WorkflowPredictionResult
{
    public bool Status { get; set; }

    public List<WorkflowPredictionListByActionIdVm> WorkflowPredictionListByActionIdVms { get; set; }

    public List<WorkflowPredictionListByActionIdVm> MatchedActions { get; set; }
}