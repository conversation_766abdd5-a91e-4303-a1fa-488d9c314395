﻿using ContinuityPatrol.Application.Features.UserInfo.Commands.Create;
using ContinuityPatrol.Application.Features.UserInfo.Commands.Update;
using ContinuityPatrol.Application.Features.UserInfo.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IUserInfoService
{
    Task<BaseResponse> CreateAsync(CreateUserInfoCommand createUserInfoCommand);
    Task<BaseResponse> UpdateAsync(UpdateUserInfoCommand  updateUserInfoCommand);
    Task<BaseResponse> DeleteAsync(string userId);
    Task<UserInfoDetailVm> GetByReferenceId(string id);
}
