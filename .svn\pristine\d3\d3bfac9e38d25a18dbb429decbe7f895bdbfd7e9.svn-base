﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Helper;


namespace ContinuityPatrol.Persistence.Repositories;

public class DashboardViewRepository : BaseRepository<DashboardView>, IDashboardViewRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public DashboardViewRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<DashboardView>> ListAllAsync()
    {
        var businessServices = base.QueryAll(businessService =>
            businessService.CompanyId.Equals(_loggedInUserService.CompanyId));

        var businessServicesDto = MapDashBoardView(businessServices);

        return _loggedInUserService.IsAllInfra
            ? await businessServicesDto.ToListAsync()
            : AssignedBusinessServices(businessServicesDto);
    }

    public async Task<List<DashboardView>> GetDashboardNames()
    {
        var businessServices = base.QueryAll(businessService =>
            businessService.CompanyId.Equals(_loggedInUserService.CompanyId));

        var businessServicesDto = MapDashBoardView(businessServices);

        return _loggedInUserService.IsAllInfra
            ? await businessServicesDto.Select(x => new DashboardView
            {
                InfraObjectId = x.InfraObjectId,
                InfraObjectName = x.InfraObjectName,
                MonitorType = x.MonitorType,
                EntityId = x.EntityId
            })
                .ToListAsync()
            : AssignedInfraObjects(businessServicesDto).Select(x => new DashboardView
            {
                InfraObjectId = x.InfraObjectId,
                InfraObjectName = x.InfraObjectName,
                MonitorType = x.MonitorType,
                EntityId = x.EntityId
            })
                .ToList();
    }

    public override async Task<DashboardView> GetByReferenceIdAsync(string id)
    {
        var businessService = base.GetByReferenceId(id,
           businessService => businessService.CompanyId.Equals(_loggedInUserService.CompanyId) &&
           businessService.ReferenceId.Equals(id));

        var businessServicesDto = MapDashBoardView(businessService);

        return _loggedInUserService.IsAllInfra
            ? await businessServicesDto.FirstOrDefaultAsync()
            : AssignedBusinessServices(businessServicesDto).FirstOrDefault();
    }

    public override IQueryable<DashboardView> GetPaginatedQuery()
    {
        var businessServices = SelectDashboardView(base.QueryAll(businessService =>
            businessService.CompanyId.Equals(_loggedInUserService.CompanyId)));

        var businessServicesDto = MapDashBoardView(businessServices);

        return _loggedInUserService.IsAllInfra
            ? businessServicesDto.AsNoTracking().OrderByDescending(x => x.Id)
            : PaginatedAssignedBusinessServices(businessServicesDto).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public async Task<DashboardView> GetByEntityIdAndType(string entityId, string type)
    {
        var dashboardView = SelectDashboardView(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.EntityId.Equals(entityId) && x.MonitorType.Trim().ToLower().Equals(type.Trim().ToLower()))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.EntityId.Equals(entityId) 
            && x.MonitorType.Trim().ToLower().Equals(type.Trim().ToLower())));

        var dashboardViewDto = MapDashBoardView(dashboardView);

        return _loggedInUserService.IsAllInfra      
            ? await dashboardViewDto.FirstOrDefaultAsync()
            : AssignedBusinessServices(dashboardViewDto).FirstOrDefault();
    }

    public async Task<DashboardView> GetBusinessViewByInfraObjectId(string infraObjectId)
    {
        var infraObject = _loggedInUserService.IsParent 
            ? await Entities.AsNoTracking().Active().FirstOrDefaultAsync(x => x.InfraObjectId==infraObjectId)
            : await Entities.AsNoTracking().Active().FirstOrDefaultAsync(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.InfraObjectId.Equals(infraObjectId));

        return _loggedInUserService.IsAllInfra
            ? infraObject
            : GetByInfraObjectId(infraObject);
    }

    public async Task<DashboardView> GetSitePropertiesByBusinessServiceId(string businessServiceId)
    {
        var businessService = SelectDashboardView(GetBusinessServiceByReferenceId(businessServiceId,
            businessService => businessService.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                               businessService.BusinessServiceId.Equals(businessServiceId)));

        var businessServicesDto = MapDashBoardView(businessService);

        return _loggedInUserService.IsAllInfra
            ? await businessServicesDto.FirstOrDefaultAsync()
            : GetByBusinessServiceId(businessServicesDto.FirstOrDefault());
    }
    public async Task<List<DashboardView>> GetBusinessViewListByBusinessServiceIds(List<string> businessServiceId)
    {
        var businessServices = SelectDashboardView(_loggedInUserService.IsParent
           ? base.FilterBy(x => businessServiceId.Contains(x.BusinessServiceId))
           : base.FilterBy(x =>
               x.CompanyId.Equals(_loggedInUserService.CompanyId) && businessServiceId.Contains(x.BusinessServiceId)));

        var businessServicesDto = MapDashBoardView(businessServices);

        return _loggedInUserService.IsAllInfra
            ? await businessServicesDto.ToListAsync()
            : AssignedBusinessServices(businessServicesDto).ToList();
    }
    public async Task<List<DashboardView>> GetBusinessViewListByBusinessServiceId(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "BusinessServiceId", "BusinessServiceId cannot be invalid");

        var businessServices = SelectDashboardView(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.BusinessServiceId.Equals(businessServiceId)));

        var businessServicesDto = MapDashBoardView(businessServices);

        return _loggedInUserService.IsAllInfra
            ? await businessServicesDto.ToListAsync()
            : AssignedBusinessServices(businessServicesDto).ToList();
    }

    public async Task<List<DashboardView>> GetBusinessViewListByBusinessServiceIdDatalag(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "BusinessServiceId", "BusinessServiceId cannot be invalid");

        var businessServices = SelectDashboardView(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.BusinessServiceId.Equals(businessServiceId)));

        var businessServicesDto = MapDashBoardView(businessServices);

        return _loggedInUserService.IsAllInfra
            ? await businessServicesDto.ToListAsync()
            : AssignedInfraObjects(businessServicesDto).ToList();
    }
    public async Task<List<DashboardView>> GetBusinessViewListByBusinessFunctionId(string businessFunctionId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessFunctionId, "BusinessFunctionId",
            "BusinessFunctionId cannot be invalid");

        var businessFunctions = SelectDashboardView(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.BusinessFunctionId.Equals(businessFunctionId))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.BusinessFunctionId.Equals(businessFunctionId)));

        var businessServicesDto = MapDashBoardView(businessFunctions);

        return _loggedInUserService.IsAllInfra
            ? await businessServicesDto.ToListAsync()
            : AssignedBusinessFunctions(businessServicesDto).ToList();
    }

    public Task<List<DashboardView>> GetBusinessViewByLast7Days()
    {
        var matches = _dbContext.DashboardViews.Active()
            .Where(x => x.LastModifiedDate.Date >= DateTime.Now.Date.AddDays(-7))
            .ToListAsync();

        return matches;
    }

    public async Task<List<DashboardView>> GetInfraObjectSummaryReport()
    {
        var infraObjects = SelectDashboardView(base.QueryAll(infra => infra.CompanyId.Equals(_loggedInUserService.CompanyId)));

        var infraObjectDto = MapDashBoardView(infraObjects);

        return (List<DashboardView>)(_loggedInUserService.IsAllInfra
            ? await infraObjectDto.ToListAsync()
            : AssignedInfraObjects(infraObjectDto));
    }

    //Filters
    public IReadOnlyList<DashboardView> AssignedBusinessServices(IQueryable<DashboardView> businessServices)
    {
        var services = new List<DashboardView>();

        foreach (var businessService in businessServices)
            if (AssignedEntity.AssignedBusinessServices.Count > 0)
                services.AddRange(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
                                  where businessService.BusinessServiceId == assignedBusinessService.Id
                                  select businessService);
        return services;
    }

    public IReadOnlyList<DashboardView> AssignedBusinessFunctions(IQueryable<DashboardView> businessFunctions)
    {
        var functions = new List<DashboardView>();

        var assignedBusinessFunctions = new List<AssignedBusinessFunctions>();

        if (AssignedEntity.AssignedBusinessServices.Count > 0)
            foreach (var assignedBusinessServices in AssignedEntity.AssignedBusinessServices)
                assignedBusinessFunctions.AddRange(assignedBusinessServices.AssignedBusinessFunctions);

        foreach (var businessFunction in businessFunctions)
            if (assignedBusinessFunctions.Count > 0)
                functions.AddRange(from assignedBusinessFunction in assignedBusinessFunctions
                                   where businessFunction.BusinessFunctionId == assignedBusinessFunction.Id
                                   select businessFunction);
        return functions;
    }

    public IReadOnlyList<DashboardView> AssignedInfraObjects(IQueryable<DashboardView> infraObjects)
    {
        var infraObjectList = new List<DashboardView>();

        var assignedBusinessFunctions = new List<AssignedBusinessFunctions>();

        if (AssignedEntity.AssignedBusinessServices.Count > 0)
            foreach (var assignedBusinessServices in AssignedEntity.AssignedBusinessServices)
                assignedBusinessFunctions.AddRange(assignedBusinessServices.AssignedBusinessFunctions);

        var assignedBusinessInfraObjects = new List<AssignedInfraObjects>();

        if (assignedBusinessFunctions.Count > 0)
            foreach (var assignedBusinessFunction in assignedBusinessFunctions)
                assignedBusinessInfraObjects.AddRange(assignedBusinessFunction.AssignedInfraObjects);

        foreach (var infraObject in infraObjects)
            if (assignedBusinessInfraObjects.Count > 0)
                infraObjectList.AddRange(from assignedInfraObject in assignedBusinessInfraObjects
                                         where infraObject.InfraObjectId == assignedInfraObject.Id
                                         select infraObject);
        return infraObjectList;
    }

    public IQueryable<DashboardView> PaginatedAssignedBusinessServices(IQueryable<DashboardView> businessServices)
    {
        var assignedServiceIds = AssignedEntity.AssignedBusinessServices.Select(s => s.Id);

        return businessServices.Where(s => assignedServiceIds.Contains(s.BusinessServiceId));
    }

    public IQueryable<DashboardView> GetByInfraObjectIdAsync(string id,
        Expression<Func<DashboardView, bool>> expression = null)
    {
        return _loggedInUserService.IsParent
            ? Entities.Where(x => x.InfraObjectId.Equals(id))
            : FilterBy(expression);
    }

    public IQueryable<DashboardView> GetBusinessServiceByReferenceId(string id,
        Expression<Func<DashboardView, bool>> expression = null)
    {
        return _loggedInUserService.IsParent
            ? Entities.Where(x => x.IsActive && x.BusinessServiceId.Equals(id))
                .AsNoTracking()
                .OrderByDescending(x => x.Id)
            : FilterBy(expression);
    }

    public DashboardView GetByInfraObjectId(DashboardView infraObject)
    {
        var services = AssignedEntity.AssignedBusinessServices
            .SelectMany(assignedBusinessService => assignedBusinessService.AssignedBusinessFunctions)
            .SelectMany(assignedBusinessFunction => assignedBusinessFunction.AssignedInfraObjects)
            .Where(assignedInfraObjects => infraObject?.InfraObjectId == assignedInfraObjects.Id)
            .Select(_ => infraObject).SingleOrDefault();

        return services;
    }
    public DashboardView GetByBusinessServiceId(DashboardView businessService)
    {
        var services = AssignedEntity.AssignedBusinessServices
            .Where(x => businessService?.BusinessServiceId == x.Id)
            .Select(_ => businessService).SingleOrDefault();

        return services;
    }
    private IQueryable<DashboardView> MapDashBoardView(IQueryable<DashboardView> dashboard)
    {
        var mapDashBoard = dashboard.Select(data => new
        {
            DashBoard = data,
            BusinessService = _dbContext.BusinessServices.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.BusinessServiceId)),
            BusinessFunction = _dbContext.BusinessFunctions.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.BusinessFunctionId)),
            InfraObject = _dbContext.InfraObjects.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.InfraObjectId)),
        });


       var mapDashBoardQuery = mapDashBoard.Select(result => new DashboardView
            {
                Id = result.DashBoard.Id,
                ReferenceId = result.DashBoard.ReferenceId,
                CompanyId = result.DashBoard.CompanyId,
                BusinessServiceId = result!.BusinessService!.ReferenceId ?? string.Empty,
                BusinessServiceName = result!.BusinessService!.Name ?? string.Empty,
                BusinessFunctionId = result!.BusinessFunction!.ReferenceId ?? string.Empty,
                BusinessFunctionName = result!.BusinessFunction!.Name ?? string.Empty,
                InfraObjectId = result!.InfraObject!.ReferenceId ?? string.Empty,
                InfraObjectName = result!.InfraObject!.Name ?? string.Empty,
                Priority = result!.BusinessService == null ? 0 : result!.BusinessService!.Priority,
                Description = result!.BusinessService!.Description ?? string.Empty,
                 EntityId = result!.DashBoard!.EntityId ?? string.Empty,
                MonitorType = result!.DashBoard!.MonitorType ?? string.Empty,
                Type = result!.DashBoard!.Type,
                DataLagValue = result!.DashBoard!.DataLagValue ?? string.Empty,
                Status = result!.DashBoard!.Status ?? string.Empty,
                Properties = result!.DashBoard!.Properties,
                ReplicationStatus = result!.DashBoard!.ReplicationStatus,
                DROperationStatus = result!.DashBoard!.DROperationStatus,
                ConfiguredRPO = result!.BusinessFunction!.ConfiguredRPO ?? string.Empty,
                ConfiguredRTO = result!.BusinessFunction!.ConfiguredRTO ?? string.Empty,
                RPOThreshold = result!.BusinessFunction!.RPOThreshold ?? string.Empty,
                SiteProperties = result!.BusinessService!.SiteProperties,
                CurrentRPO = result!.DashBoard!.CurrentRPO ?? string.Empty,
                CurrentRTO = result!.DashBoard!.CurrentRTO ?? string.Empty,
                RPOGeneratedDate = result!.DashBoard!.RPOGeneratedDate ?? string.Empty,
                RTOGeneratedDate = result!.DashBoard!.RTOGeneratedDate ?? string.Empty,
                EstimatedRTO = result!.DashBoard!.EstimatedRTO ?? string.Empty,
                IsDRReady = result!.DashBoard!.IsDRReady,
                State = result!.DashBoard!.State ?? string.Empty,
                ErrorMessage = result!.DashBoard!.ErrorMessage,
                IsActive = result.DashBoard.IsActive,
                CreatedBy = result.DashBoard.CreatedBy,
                CreatedDate = result.DashBoard.CreatedDate,
                LastModifiedBy = result.DashBoard.LastModifiedBy,
                LastModifiedDate = result.DashBoard.LastModifiedDate,
            });


            return mapDashBoardQuery;


      

        //return mapDashBoard.Select(x => x.DashBoard).AsQueryable();
        //var mapDashBoardQuery = mapDashBoard.Select(result => new DashboardView
        //{
        //    Id = result.DashBoard.Id,
        //    ReferenceId = result.DashBoard.ReferenceId,
        //    CompanyId = result.DashBoard.CompanyId,
        //    BusinessServiceId = result!.BusinessService!.ReferenceId ?? string.Empty,
        //    BusinessServiceName = result!.BusinessService!.Name ?? string.Empty,
        //    BusinessFunctionId = result!.BusinessFunction!.ReferenceId ?? string.Empty,
        //    BusinessFunctionName = result!.BusinessFunction!.Name ?? string.Empty,
        //    InfraObjectId = result!.InfraObject!.ReferenceId ?? string.Empty,
        //    InfraObjectName = result!.InfraObject!.Name ?? string.Empty,
        //    Priority = result!.BusinessService == null ? 0 : result!.BusinessService!.Priority,
        //    Description = result!.BusinessService!.Description ?? string.Empty,
        //    EntityId = result!.DashBoard!.EntityId ?? string.Empty,
        //    MonitorType = result!.DashBoard!.MonitorType ?? string.Empty,
        //    Type = result!.DashBoard!.Type,
        //    DataLagValue = result!.DashBoard!.DataLagValue ?? string.Empty,
        //    Status = result!.DashBoard!.Status ?? string.Empty,
        //    Properties = result!.DashBoard!.Properties ?? string.Empty,
        //    ReplicationStatus = result!.DashBoard!.ReplicationStatus,
        //    DROperationStatus = result!.DashBoard!.DROperationStatus,
        //    ConfiguredRPO = result!.BusinessFunction!.ConfiguredRPO ?? string.Empty,
        //    ConfiguredRTO = result!.BusinessFunction!.ConfiguredRTO ?? string.Empty,
        //    RPOThreshold = result!.BusinessFunction!.RPOThreshold ?? string.Empty,
        //    SiteProperties = result!.BusinessService!.SiteProperties ?? string.Empty,
        //    CurrentRPO = result!.DashBoard!.CurrentRPO ?? string.Empty,
        //    CurrentRTO = result!.DashBoard!.CurrentRTO ?? string.Empty,
        //    RPOGeneratedDate = result!.DashBoard!.RPOGeneratedDate ?? string.Empty,
        //    RTOGeneratedDate = result!.DashBoard!.RTOGeneratedDate ?? string.Empty,
        //    EstimatedRTO = result!.DashBoard!.EstimatedRTO ?? string.Empty,
        //    IsDRReady = result!.DashBoard!.IsDRReady,
        //    State = result!.DashBoard!.State ?? string.Empty,
        //    ErrorMessage = result!.DashBoard!.ErrorMessage ?? string.Empty,
        //    IsActive = result.DashBoard.IsActive,
        //    CreatedBy = result.DashBoard.CreatedBy,
        //    CreatedDate = result.DashBoard.CreatedDate,
        //    LastModifiedBy = result.DashBoard.LastModifiedBy,
        //    LastModifiedDate = result.DashBoard.LastModifiedDate,
        //});

        // return mapDashBoardQuery;
    }
    public IQueryable<DashboardView> SelectDashboardView(IQueryable<DashboardView> query)
    {
        return query.Select(x => new DashboardView
        {
            Id=x.Id,
            ReferenceId=x.ReferenceId,
            BusinessServiceId=x.BusinessServiceId,
            BusinessServiceName=x.BusinessServiceName,
            BusinessFunctionId=x.BusinessFunctionId,
            BusinessFunctionName=x.BusinessFunctionName,
            InfraObjectId=x.InfraObjectId,
            InfraObjectName=x.InfraObjectName,
            Priority=x.Priority,
            CompanyId=x.CompanyId,
            Description=x.Description,
            EntityId=x.EntityId,
            Type=x.Type,
            MonitorType=x.MonitorType,
            DataLagValue=x.DataLagValue,
            Status=x.Status,
            Properties=x.Properties,
            ReplicationStatus=x.ReplicationStatus,
            DROperationStatus=x.DROperationStatus,
            ConfiguredRPO=x.ConfiguredRPO,
            ConfiguredRTO=x.ConfiguredRTO,
            RPOThreshold=x.RPOThreshold,
            RPOGeneratedDate=x.RPOGeneratedDate,
            RTOGeneratedDate=x.RTOGeneratedDate,
            SiteProperties=x.SiteProperties,
            CurrentRPO=x.CurrentRPO,
            CurrentRTO=x.CurrentRTO,
            State=x.State,
            ErrorMessage=x.ErrorMessage,
            EstimatedRTO=x.EstimatedRTO,
            IsDRReady=x.IsDRReady
        });
    }
}