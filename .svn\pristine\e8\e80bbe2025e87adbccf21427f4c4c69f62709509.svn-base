﻿using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RsyncOptionModel;

namespace ContinuityPatrol.Application.UnitTests.Features.RsyncOption.Queries
{
    public class GetRsyncOptionPaginatedListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IRsyncOptionRepository> _mockRsyncOptionRepository;
        private readonly GetRsyncOptionPaginatedListQueryHandler _handler;

        public GetRsyncOptionPaginatedListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockRsyncOptionRepository = new Mock<IRsyncOptionRepository>();
            _handler = new GetRsyncOptionPaginatedListQueryHandler(_mockMapper.Object, _mockRsyncOptionRepository.Object);
        }

        [Fact]
        public async Task Handle_ReturnsPaginatedList_WhenDataExists()
        {
            var query = new GetRsyncOptionPaginatedListQuery
            {
                SearchString = "Test",
                PageNumber = 1,
                PageSize = 2
            };

            var rsyncOptions = new List<Domain.Entities.RsyncOption>
            {
                new Domain.Entities.RsyncOption { Id = 1, Name = "Option1" },
                new Domain.Entities.RsyncOption { Id = 2, Name = "Option2" }
            }.AsQueryable();

            var mappedList = new List<RsyncOptionListVm>
            {
                new RsyncOptionListVm { Id = Guid.NewGuid().ToString(), Name = "Option1" },
                new RsyncOptionListVm { Id = Guid.NewGuid().ToString(), Name = "Option2" }
            };

            _mockRsyncOptionRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(rsyncOptions);

            _mockMapper
                .Setup(mapper => mapper.Map<RsyncOptionListVm>(It.IsAny<Domain.Entities.RsyncOption>()))
                .Returns((Domain.Entities.RsyncOption option) =>
                    new RsyncOptionListVm { Id = Guid.NewGuid().ToString(), Name = option.Name });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Data.Count);
            Assert.Equal("Option1", result.Data[0].Name);
            Assert.Equal("Option2", result.Data[1].Name);
            _mockRsyncOptionRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }

        [Fact]
        public async Task Handle_ReturnsEmptyList_WhenNoDataExists()
        {
            var query = new GetRsyncOptionPaginatedListQuery
            {
                SearchString = "NonExistent",
                PageNumber = 1,
                PageSize = 2
            };

            var emptyQueryable = new List<Domain.Entities.RsyncOption>().AsQueryable();

            _mockRsyncOptionRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(emptyQueryable);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);
            _mockRsyncOptionRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsException_WhenRepositoryThrowsException()
        {
            var query = new GetRsyncOptionPaginatedListQuery
            {
                SearchString = "Test",
                PageNumber = 1,
                PageSize = 2
            };

            _mockRsyncOptionRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(ToString);

            var exception = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(query, CancellationToken.None));
            Assert.Equal("Repository error", exception.Message);
        }
    }
}
