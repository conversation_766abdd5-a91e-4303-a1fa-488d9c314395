using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DynamicDashboardFixture : IDisposable
{
    public List<DynamicDashboard> DynamicDashboardPaginationList { get; set; }
    public List<DynamicDashboard> DynamicDashboardList { get; set; }
    public DynamicDashboard DynamicDashboardDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string DashboardName = "TestDashboard";
    public const string Description = "Test Dashboard Description";
    public const string DashboardType = "Operational";
    public const string Layout = "Grid";

    public ApplicationDbContext DbContext { get; private set; }

    public DynamicDashboardFixture()
    {
        var fixture = new Fixture();

        DynamicDashboardList = fixture.Create<List<DynamicDashboard>>();

        DynamicDashboardPaginationList = fixture.CreateMany<DynamicDashboard>(20).ToList();

        DynamicDashboardPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DynamicDashboardPaginationList.ForEach(x => x.IsActive = true);

        DynamicDashboardDto = fixture.Create<DynamicDashboard>();
        DynamicDashboardDto.IsActive = true;
      
        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
