using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class RoboCopyFixture : IDisposable
{
    public List<RoboCopy> RoboCopyPaginationList { get; set; }
    public List<RoboCopy> RoboCopyList { get; set; }
    public RoboCopy RoboCopyDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public RoboCopyFixture()
    {
        var fixture = new Fixture();

        RoboCopyList = fixture.Create<List<RoboCopy>>();

        RoboCopyPaginationList = fixture.CreateMany<RoboCopy>(20).ToList();

        RoboCopyDto = fixture.Create<RoboCopy>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
