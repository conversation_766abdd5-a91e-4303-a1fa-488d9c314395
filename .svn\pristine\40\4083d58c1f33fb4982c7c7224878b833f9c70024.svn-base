using ContinuityPatrol.Application.Features.SiteLocation.Events.Delete;

namespace ContinuityPatrol.Application.Features.SiteLocation.Commands.Delete;

public class DeleteSiteLocationCommandHandler : IRequestHandler<DeleteSiteLocationCommand, DeleteSiteLocationResponse>
{
    private readonly IPublisher _publisher;
    private readonly ISiteLocationRepository _siteLocationRepository;
    private readonly ISiteRepository _siteRepository;

    public DeleteSiteLocationCommandHandler(ISiteLocationRepository siteLocationRepository, IPublisher publisher,
        ISiteRepository siteRepository)
    {
        _siteLocationRepository = siteLocationRepository;
        _siteRepository = siteRepository;
        _publisher = publisher;
    }

    public async Task<DeleteSiteLocationResponse> Handle(DeleteSiteLocationCommand request,
        CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, "SiteLocation Id");

        var eventToDelete = await _siteLocationRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.SiteLocation),
            new NotFoundException(nameof(Domain.Entities.SiteLocation), request.Id));

        var site = await _siteRepository.GetSiteBySiteLocation(eventToDelete.City);

        if (site.Count > 0) throw new InvalidException("The site location is currently in use");

        eventToDelete.IsActive = false;

        await _siteLocationRepository.UpdateAsync(eventToDelete);

        var response = new DeleteSiteLocationResponse
        {
            Message = Message.Delete("Site Location", eventToDelete.City),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new SiteLocationDeletedEvent { Name = eventToDelete.City }, cancellationToken);

        return response;
    }
}