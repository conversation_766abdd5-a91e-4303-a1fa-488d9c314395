﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.SolutionHistory.Events.Delete;

public class SolutionHistoryDeletedEventHandler : INotificationHandler<SolutionHistoryDeletedEvent>
{
    private readonly ILogger<SolutionHistoryDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public SolutionHistoryDeletedEventHandler(ILoggedInUserService userService,
        ILogger<SolutionHistoryDeletedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(SolutionHistoryDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.SolutionHistory}",
            Entity = Modules.SolutionHistory.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"SolutionHistory '{deletedEvent.ActionName}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"SolutionHistory '{deletedEvent.ActionName}' deleted successfully.");
    }
}