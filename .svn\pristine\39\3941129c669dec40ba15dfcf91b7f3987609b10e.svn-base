﻿using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Infrastructure.Extensions;

namespace ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Update;

public class
    UpdateWorkflowOperationCommandHandler : IRequestHandler<UpdateWorkflowOperationCommand,
        UpdateWorkflowOperationResponse>
{
    private readonly IMapper _mapper;
   // private readonly IPublisher _publisher;
    private readonly IWorkflowOperationRepository _workflowOperationRepository;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;
    private readonly IJobScheduler _client;
    private readonly IWorkflowRunningActionRepository _workflowRunningActionRepository;
    private readonly ILogger<UpdateWorkflowOperationCommandHandler> _logger;

    public UpdateWorkflowOperationCommandHandler(IMapper mapper,
        IWorkflowOperationRepository workflowOperationRepository,
        IWorkflowOperationGroupRepository workflowOperationGroupRepository, 
        IJobScheduler client,
        IWorkflowRunningActionRepository workflowRunningActionRepository, 
        ILogger<UpdateWorkflowOperationCommandHandler> logger)
    {
        _mapper = mapper;
        _workflowOperationRepository = workflowOperationRepository;
       // _publisher = publisher;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
        _client = client;
       // _seqService = seqService;
        _workflowRunningActionRepository = workflowRunningActionRepository;
        _logger = logger;
    }

    public async Task<UpdateWorkflowOperationResponse> Handle(UpdateWorkflowOperationCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _workflowOperationRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Workflow), request.Id);
        _mapper.Map(request, eventToUpdate, typeof(UpdateWorkflowOperationCommand),
            typeof(Domain.Entities.WorkflowOperation));

        eventToUpdate.EndTime = DateTime.Now;

        await _workflowOperationRepository.UpdateAsync(eventToUpdate);

        await DeleteJobAndSeqLog(eventToUpdate);

        var response = new UpdateWorkflowOperationResponse
        {
            Message = Message.Update(nameof(Domain.Entities.WorkflowOperation), eventToUpdate.ProfileId),

            Id = eventToUpdate.ReferenceId
        };

        //await _publisher.Publish(new WorkflowOperationUpdatedEvent { Description = eventToUpdate.Description }, cancellationToken);

        return response;
    }

    private async Task DeleteJobAndSeqLog(Domain.Entities.WorkflowOperation eventToUpdate)
    {
        try
        {
            _logger.LogDebug("Starting DeleteJobAndSeqLog for event with ProfileName: {ProfileName}", eventToUpdate.ProfileName);

            var status = eventToUpdate.Status.Trim().ToLower();

            _logger.LogDebug("Event status: {Status}", status);

            if (status.Equals("successwitherror") || status.Equals("success"))
            {
                _logger.LogDebug("Event status is valid for processing. ReferenceId: {ReferenceId}", eventToUpdate.ReferenceId);

                var workflowOperationGroups = await _workflowOperationGroupRepository.GetWorkflowOperationGroupByWorkflowOperationId(eventToUpdate.ReferenceId);

                _logger.LogDebug("Retrieved {Count} workflow operation groups.", workflowOperationGroups.Count);

                var operationIdDictionary = workflowOperationGroups.ToDictionary(x => x.WorkflowName, x => x.ReferenceId);

                _logger.LogDebug("OperationIdDictionary created with {Count} entries.", operationIdDictionary.Count);

                var groupIds = operationIdDictionary.Values
                    .Select(x => $"{x}-SeqServiceTrigger")
                    .ToList();

                _logger.LogDebug("GroupIds generated: {GroupIds}", string.Join(", ", groupIds));

                var dynamicData = new Dictionary<string, object>
                {
                    ["operationIdDictionary"] = operationIdDictionary,
                    ["groupIds"] = groupIds,
                    ["client"] = _client
                };

                _logger.LogDebug("Dynamic data prepared for scheduling.");

                _logger.LogDebug("Scheduling deletion of jobs and logs.");

                await _client.ScheduleDeleteJobAndSeqLog(eventToUpdate.ReferenceId, dynamicData);

                _logger.LogDebug("Deleting workflow running actions by group IDs.");
                await _workflowRunningActionRepository.DeleteWorkflowRunningActionsByGroupIds(operationIdDictionary.Values.ToList());

                _logger.LogDebug("DeleteJobAndSeqLog completed successfully for ReferenceId: {ReferenceId}", eventToUpdate.ReferenceId);

                //await _client.DeleteSeqJobs(groupIds);
                //await _seqService.DeleteSeqLog(operationIdDictionary);
                // await _seqService.LogoutAsync(); 

                //foreach (var id in operationIdDictionary.Values)
                //{
                //    var workflowRunning = await _workflowRunningActionRepository.GetWorkflowRunningActionsByOperationGroupId(id);

                //    await _workflowRunningActionRepository.RemoveRange(workflowRunning);
                //}
            }
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while processing DeleteJobAndSeqLog",ex);
        }
    }
}