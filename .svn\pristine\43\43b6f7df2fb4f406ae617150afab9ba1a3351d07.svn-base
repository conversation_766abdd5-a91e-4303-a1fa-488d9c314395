namespace ContinuityPatrol.Application.Features.DriftResourceSummary.Commands.Update;

public class UpdateDriftResourceSummaryCommand : IRequest<UpdateDriftResourceSummaryResponse>
{
    public string Id { get; set; }
    public string CompanyId { get; set; }
    public string ParameterName { get; set; }
    public string InfraObjectId { get; set; }
    public string EntityName { get; set; }
    public string Type { get; set; }
    public string TypeId { get; set; }
    public int TotalCount { get; set; }
    public int ConflictCount { get; set; }
    public int NonConflictCount { get; set; }
    public bool IsConflict { get; set; }
    public string Logo { get; set; }
}