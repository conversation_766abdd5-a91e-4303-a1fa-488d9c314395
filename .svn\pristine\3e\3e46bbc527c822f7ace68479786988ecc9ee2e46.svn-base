﻿namespace ContinuityPatrol.Application.Features.Database.Commands.UpdateBulkPassword;

public class UpdateDatabasePasswordCommandValidator : AbstractValidator<UpdateDatabasePasswordCommand>
{
    public UpdateDatabasePasswordCommandValidator()
    {
        RuleFor(p => p.Password)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull();

        RuleFor(p => p.PasswordList)
            .NotEmpty().WithMessage("PasswordList cannot be empty.")
            .Must(list => list.Count > 0).WithMessage("{PropertyName} must have count.");

        RuleForEach(x => x.PasswordList).SetValidator(new UpdateDatabasePasswordListValidator());
    }
}

public class UpdateDatabasePasswordListValidator : AbstractValidator<UpdateDatabasePasswordList>
{
    public UpdateDatabasePasswordListValidator()
    {
        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull();
        RuleFor(p => p)
            .MustAsync(IsValidGUID).WithMessage("Invalid id.");
    }

    private Task<bool> IsValidGUID(UpdateDatabasePasswordList p, CancellationToken token)
    {
        Guard.Against.InvalidGuidOrEmpty(p.Id, "Id");
        return Task.FromResult(true);
    }
}