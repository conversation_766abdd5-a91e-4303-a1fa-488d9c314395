using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class FiaTemplateRepository : BaseRepository<FiaTemplate>, IFiaTemplateRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public FiaTemplateRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public override async Task<IReadOnlyList<FiaTemplate>> ListAllAsync()
    {
        var fiaTemplates = base.QueryAll(x => x.IsActive);

        return await fiaTemplates.ToListAsync();
    }
    public override Task<FiaTemplate> GetByReferenceIdAsync(string id)
    {
        var fiaTemplates = base.GetByReferenceId(id, x =>
                  x.ReferenceId.Equals(id));

        return fiaTemplates.FirstOrDefaultAsync();
    }
    public override async Task<PaginatedResult<FiaTemplate>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<FiaTemplate> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await Entities.Specify(productFilterSpec).DescOrderById().ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);

    }
    public override IQueryable<FiaTemplate> GetPaginatedQuery()
    {
        var fiaTemplates = base.QueryAll(x => x.IsActive);

        return fiaTemplates.AsNoTracking().OrderByDescending(x => x.Id);
    }
    public async Task<List<FiaTemplate>> GetFiaTemplateByImpactCategoryId(string impactCategoryId)
    {
        var fiaTemplates = base.FilterBy(x => x.Properties.Contains(impactCategoryId));

        return await fiaTemplates.ToListAsync();
    }
    public async Task<List<FiaTemplate>> GetFiaTemplateByImpactTypeId(string impactTypeId)
    {
        var fiaTemplates = base.FilterBy(x => x.Properties.Contains(impactTypeId));

        return await fiaTemplates.ToListAsync();
    }
    public async Task<List<FiaTemplate>> GetFiaTemplateByIntervalId(string intervalId)
    {
        var fiaTemplates = base.FilterBy(x => x.Properties.Contains(intervalId));

        return await fiaTemplates.ToListAsync();
    }
    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.Name.Equals(name))
            : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }
    //private IQueryable<FiaTemplate> MapFiaTemplate(IQueryable<FiaTemplate> fiaTemplates)
    //{
    //    return fiaTemplates.Select(x => new
    //    {
    //        FiaTemplate = x
    //    })
    //    .Select(res => new FiaTemplate
    //    {
    //        Id = res.FiaTemplate.Id,
    //        ReferenceId = res.FiaTemplate.ReferenceId,
    //        Name = res.FiaTemplate.Name,
    //        Description = res.FiaTemplate.Description,
    //       Properties=res.FiaTemplate.Properties,
    //        UserName = res.FiaTemplate.UserName,
    //        TemplateInUsed = res.FiaTemplate.TemplateInUsed,
    //        TemplateUsedBy = res.FiaTemplate.TemplateUsedBy,
    //        IsActive = res.FiaTemplate.IsActive,
    //        CreatedBy = res.FiaTemplate.CreatedBy,
    //        CreatedDate = res.FiaTemplate.CreatedDate,
    //        LastModifiedBy = res.FiaTemplate.LastModifiedBy,
    //        LastModifiedDate = res.FiaTemplate.LastModifiedDate
    //    });
    //}


}
