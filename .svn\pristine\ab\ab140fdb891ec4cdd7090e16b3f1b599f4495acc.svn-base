﻿using System.Drawing;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSchedulerLogs.Models;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    public partial class ResilencyReadinessSchedulerLogReport : DevExpress.XtraReports.UI.XtraReport
    {
        public  GetResiliencyReadinessSchedulerLogReportVm schedulerLogReportVm = new GetResiliencyReadinessSchedulerLogReportVm();

        private readonly ILogger<PreBuildReportController> _logger;
        public string[] InputDateFormats = { "dd/MM/yyyy", "MM/dd/yyyy", "yyyy-MM-dd", "M/d/yyyy", "d/M/yyyy", "MM-dd-yyyy", "dd-MM-yyyy", "dd-MM-yyyy HH:mm:ss", "dd-MM-yyyy hh:mm:ss tt", "MM/dd/yyyy HH:mm:ss", "MM-dd-yyyy HH:mm:ss" };
        public ResilencyReadinessSchedulerLogReport(string data)
        {
            try
            {
                schedulerLogReportVm = JsonConvert.DeserializeObject<GetResiliencyReadinessSchedulerLogReportVm>(data);
                _logger = PreBuildReportController._logger;
                InitializeComponent();
                ClientCompanyLogo();
                foreach(var infraSchedule in schedulerLogReportVm.InfraObjectSchedulerLogsReportList)
                {
                    var logExecution = DateTime.ParseExact(infraSchedule.LastExecutionTime, InputDateFormats, null);
                    infraSchedule.LastExecutionTime = logExecution.ToString("dd-MM-yyyy HH:mm:ss");
                }
                this.DataSource= schedulerLogReportVm.InfraObjectSchedulerLogsReportList;
                _userName.Text = "Report Generated By: " + schedulerLogReportVm.ReportGeneratedBy;
                _serialNumber.BeforePrint += _serialNumberBeforePrint;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the InfraObject scheduler log Report. The error message : " + ex.Message); 
                throw;
            }

        }
        private int serialNumber = 1;
        private void _serialNumberBeforePrint(object sender, CancelEventArgs e)
        {
            XRTableCell cell = (XRTableCell)sender;

            cell.Text = serialNumber.ToString();
            serialNumber++;
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RPOSLAMssqlDBMirroring Report's CP Version. The error message : " + ex.Message); throw; }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the RPOSLAMssqlDBMirroring Report's customer logo" + ex.Message.ToString());
            }
        }
    }
}
