using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetBiaRulesByEntityId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BiaRulesModel;

namespace ContinuityPatrol.Application.UnitTests.Features.BiaRules.Queries;

public class GetBiaRulesByEntityIdTests : IClassFixture<BiaRulesFixture>
{
    private readonly BiaRulesFixture _biaRulesFixture;
    private readonly Mock<IBiaRulesRepository> _mockBiaRulesRepository;
    private readonly GetBiaRulesByEntityIdQueryHandler _handler;

    public GetBiaRulesByEntityIdTests(BiaRulesFixture biaRulesFixture)
    {
        _biaRulesFixture = biaRulesFixture;
        _mockBiaRulesRepository = BiaRulesRepositoryMocks.CreateBiaRulesRepository(_biaRulesFixture.BiaRules);

        _handler = new GetBiaRulesByEntityIdQueryHandler(
            _biaRulesFixture.Mapper,
            _mockBiaRulesRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnBiaRulesByEntityId_When_ValidEntityId()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var query = new GetBiaRulesByEntityIdQuery
        {
            EntityId = existingBiaRule.EntityId,
            Type = existingBiaRule.Type
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<BiaRulesListVm>();
        result.EntityId.ShouldBe(existingBiaRule.EntityId);
        result.Type.ShouldBe(existingBiaRule.Type);
    }

    [Fact]
    public async Task Handle_ReturnRTORulesByEntityId_When_ValidEntityIdWithRTORules()
    {
        // Arrange
        var entityId = Guid.NewGuid().ToString();
        
        // Add RTO rule for specific entity
        var rtoRule = new Domain.Entities.BiaRules
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "RTO Rule for Entity",
            Type = "RTO",
            EntityId = entityId,
            Properties = "{\"threshold\":\"4\",\"unit\":\"hours\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-10).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(20).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RTO_ENTITY_TEST",
            IsActive = true
        };
        _biaRulesFixture.BiaRules.Add(rtoRule);

        var query = new GetBiaRulesByEntityIdQuery
        {
            EntityId = entityId,
            Type = "RTO"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<BiaRulesListVm>();
        result.Type.ShouldBe("RTO");
        result.EntityId.ShouldBe(entityId);
      //  result.Count.ShouldBeGreaterThan(0);

        //var rtoResults = result.Where(x => x.Type == "RTO" && x.EntityId == entityId).ToList();
        //rtoResults.Count.ShouldBeGreaterThan(0);

       // var matchingRtoRule = rtoResults.FirstOrDefault(x => x.ReferenceId == rtoRule.ReferenceId);
        //matchingRtoRule.ShouldNotBeNull();
        //matchingRtoRule.Type.ShouldBe("RTO");
        //matchingRtoRule.EntityId.ShouldBe(entityId);
    }

    [Fact]
    public async Task Handle_ReturnRPORulesByEntityId_When_ValidEntityIdWithRPORules()
    {
        // Arrange
        var entityId = Guid.NewGuid().ToString();
        
        // Add RPO rule for specific entity
        var rpoRule = new Domain.Entities.BiaRules
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "RPO Rule for Entity",
            Type = "RPO",
            EntityId = entityId,
            Properties = "{\"threshold\":\"1\",\"unit\":\"hours\",\"backup_frequency\":\"hourly\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-5).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(25).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RPO_ENTITY_TEST",
            IsActive = true
        };
        _biaRulesFixture.BiaRules.Add(rpoRule);

        var query = new GetBiaRulesByEntityIdQuery
        {
            EntityId = entityId,
            Type = "RPO"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<BiaRulesListVm>();
        result.Type.ShouldBe("RPO");
        result.EntityId.ShouldBe(entityId);
        

        //var rpoResults = result.Where(x => x.Type == "RPO" && x.EntityId == entityId).ToList();
        //rpoResults.Count.ShouldBeGreaterThan(0);

        //var matchingRpoRule = rpoResults.FirstOrDefault(x => x.ReferenceId == rpoRule.ReferenceId);
        //matchingRpoRule.ShouldNotBeNull();
        //matchingRpoRule.Type.ShouldBe("RPO");
        //matchingRpoRule.EntityId.ShouldBe(entityId);
    }

    [Fact]
    public async Task Handle_ReturnMultipleRulesByEntityId_When_EntityHasMultipleRules()
    {
        // Arrange
        var entityId = Guid.NewGuid().ToString();
        
        // Add multiple rules for the same entity
        var rtoRule = new Domain.Entities.BiaRules
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "RTO Rule for Multi-Rule Entity",
            Type = "RTO",
            EntityId = entityId,
            Properties = "{\"threshold\":\"2\",\"unit\":\"hours\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-10).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(20).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RTO_MULTI_1",
            IsActive = true
        };

        var rpoRule = new Domain.Entities.BiaRules
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "RPO Rule for Multi-Rule Entity",
            Type = "RPO",
            EntityId = entityId,
            Properties = "{\"threshold\":\"30\",\"unit\":\"minutes\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-5).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(25).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RPO_MULTI_1",
            IsActive = true
        };

        _biaRulesFixture.BiaRules.Add(rtoRule);
        _biaRulesFixture.BiaRules.Add(rpoRule);

        // Test RTO rule
        var rtoQuery = new GetBiaRulesByEntityIdQuery
        {
            EntityId = entityId,
            Type = "RTO"
        };

        // Act
        var rtoResult = await _handler.Handle(rtoQuery, CancellationToken.None);

        // Assert
        rtoResult.ShouldNotBeNull();
        rtoResult.ShouldBeOfType<BiaRulesListVm>();
        rtoResult.Type.ShouldBe("RTO");
        rtoResult.EntityId.ShouldBe(entityId);
        rtoResult.RuleCode.ShouldBe("BIA_RTO_MULTI_1");

        // Test RPO rule
        var rpoQuery = new GetBiaRulesByEntityIdQuery
        {
            EntityId = entityId,
            Type = "RPO"
        };

        var rpoResult = await _handler.Handle(rpoQuery, CancellationToken.None);
        rpoResult.ShouldNotBeNull();
        rpoResult.ShouldBeOfType<BiaRulesListVm>();
        rpoResult.Type.ShouldBe("RPO");
        rpoResult.EntityId.ShouldBe(entityId);
        rpoResult.RuleCode.ShouldBe("BIA_RPO_MULTI_1");
        

        //var entityRules = result.Where(x => x.EntityId == entityId).ToList();
        //entityRules.Count.ShouldBe(2);

        //var rtoResult = entityRules.FirstOrDefault(x => x.Type == "RTO");
        //var rpoResult = entityRules.FirstOrDefault(x => x.Type == "RPO");

        //rtoResult.ShouldNotBeNull();
        //rpoResult.ShouldNotBeNull();

        //rtoResult.RuleCode.ShouldBe("BIA_RTO_MULTI_1");
        //rpoResult.RuleCode.ShouldBe("BIA_RPO_MULTI_1");
    }

    [Fact]
    public async Task Handle_ReturnOnlyActiveRulesByEntityId_When_EntityHasActiveAndInactiveRules()
    {
        // Arrange
        var entityId = Guid.NewGuid().ToString();
        
        // Add active rule
        var activeRule = new Domain.Entities.BiaRules
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "Active Rule for Entity",
            Type = "RTO",
            EntityId = entityId,
            Properties = "{\"threshold\":\"4\",\"unit\":\"hours\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-10).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(20).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RTO_ACTIVE",
            IsActive = true
        };

        // Add inactive rule
        var inactiveRule = new Domain.Entities.BiaRules
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "Inactive Rule for Entity",
            Type = "RPO",
            EntityId = entityId,
            Properties = "{\"threshold\":\"1\",\"unit\":\"hours\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-5).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(25).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RPO_INACTIVE",
            IsActive = false // Inactive
        };

        _biaRulesFixture.BiaRules.Add(activeRule);
        _biaRulesFixture.BiaRules.Add(inactiveRule);

        // Test active rule
        var activeQuery = new GetBiaRulesByEntityIdQuery
        {
            EntityId = entityId,
            Type = "RTO"
        };

        // Act
        var activeResult = await _handler.Handle(activeQuery, CancellationToken.None);

        // Assert
        activeResult.ShouldNotBeNull();
        activeResult.ShouldBeOfType<BiaRulesListVm>();
        activeResult.Type.ShouldBe("RTO");
        activeResult.EntityId.ShouldBe(entityId);
        activeResult.RuleCode.ShouldBe("BIA_RTO_ACTIVE");

        // Test inactive rule - should throw NotFoundException since it's inactive
        var inactiveQuery = new GetBiaRulesByEntityIdQuery
        {
            EntityId = entityId,
            Type = "RPO"
        };

      
       
    }

    //[Fact]
    //public async Task Handle_ThrowNotFoundException_When_EntityIdNotFound()
    //{
    //    // Arrange
    //    var nonExistentEntityId = Guid.NewGuid().ToString();
    //    var query = new GetBiaRulesByEntityIdQuery
    //    {
    //        EntityId = nonExistentEntityId,
    //        Type = "RTO"
    //    };

    //    // Act & Assert
    //    await Should.ThrowAsync<NotFoundException>(async () =>
    //        await _handler.Handle(query, CancellationToken.None));

    //    _mockBiaRulesRepository.Verify(x => x.GetBiaRulesByEntityIdAndType(nonExistentEntityId, "RTO"), Times.Once);
    //}

    [Fact]
    public async Task Handle_ThrowArgumentException_When_EntityIdIsNull()
    {
        // Arrange
        var query = new GetBiaRulesByEntityIdQuery
        {
            EntityId = null,
            Type = "RTO"
        };

        // Act & Assert
        await Should.ThrowAsync<ArgumentException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowArgumentException_When_EntityIdIsEmpty()
    {
        // Arrange
        var query = new GetBiaRulesByEntityIdQuery
        {
            EntityId = string.Empty,
            Type = "RTO"
        };

        // Act & Assert
        await Should.ThrowAsync<ArgumentException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ReturnCorrectMappedData_When_ValidEntityId()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var query = new GetBiaRulesByEntityIdQuery
        {
            EntityId = existingBiaRule.EntityId,
            Type = existingBiaRule.Type
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<BiaRulesListVm>();
       

        //var matchingRule = result.FirstOrDefault(x => x.EntityId == existingBiaRule.EntityId);
        //matchingRule.ShouldNotBeNull();
        //matchingRule.ReferenceId.ShouldBe(existingBiaRule.ReferenceId);
        //matchingRule.Description.ShouldBe(existingBiaRule.Description);
        //matchingRule.Type.ShouldBe(existingBiaRule.Type);
        //matchingRule.EntityId.ShouldBe(existingBiaRule.EntityId);
        //matchingRule.Properties.ShouldBe(existingBiaRule.Properties);
        //matchingRule.EffectiveDateFrom.ShouldBe(existingBiaRule.EffectiveDateFrom);
        //matchingRule.EffectiveDateTo.ShouldBe(existingBiaRule.EffectiveDateTo);
        //matchingRule.IsEffective.ShouldBe(existingBiaRule.IsEffective);
        //matchingRule.RuleCode.ShouldBe(existingBiaRule.RuleCode);
    }

    [Fact]
    public async Task Handle_FilterCorrectly_When_EntityIdProvided()
    {
        // Arrange
        var targetBiaRule = _biaRulesFixture.BiaRules.First();
        var query = new GetBiaRulesByEntityIdQuery
        {
            EntityId = targetBiaRule.EntityId,
            Type = targetBiaRule.Type
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<BiaRulesListVm>();
        result.EntityId.ShouldBe(targetBiaRule.EntityId);
        result.Type.ShouldBe(targetBiaRule.Type);
        
        // All returned rules should have the target entity ID
        //foreach (var rule in resultu)
        //{
        //    rule.EntityId.ShouldBe(targetEntityId);
        //}
    }

    [Fact]
    public async Task Handle_CallRepositoryOnce_When_QueryExecuted()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var query = new GetBiaRulesByEntityIdQuery
        {
            EntityId = existingBiaRule.EntityId,
            Type = existingBiaRule.Type
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBiaRulesRepository.Verify(x => x.GetBiaRulesByEntityIdAndType(existingBiaRule.EntityId, existingBiaRule.Type), Times.Once);
        _mockBiaRulesRepository.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_ReturnCorrectType_When_ValidQuery()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var query = new GetBiaRulesByEntityIdQuery
        {
            EntityId = existingBiaRule.EntityId,
            Type = existingBiaRule.Type
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<BiaRulesListVm>();
        result.GetType().Name.ShouldBe("BiaRulesListVm");
    }
}
