﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class FormTypeCategoryRepository : BaseRepository<FormTypeCategory>, IFormTypeCategoryRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public FormTypeCategoryRepository(ApplicationDbContext dbContext,ILoggedInUserService loggedInUserService) : base(dbContext,loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
       
    }

    public override async Task<IReadOnlyList<FormTypeCategory>> ListAllAsync()
    {
        var formTypeCategory = SelectFormTypeCategory(base.QueryAll(form =>form.IsActive));

        var mapFormTypeCategory = MapFormTypeCategory(formTypeCategory);

        return await mapFormTypeCategory.ToListAsync();
    }

    public override async Task<PaginatedResult<FormTypeCategory>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<FormTypeCategory> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await SelectFormTypeCategory(MapFormTypeCategory(Entities.Specify(productFilterSpec).DescOrderById())).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public override IQueryable<FormTypeCategory> GetPaginatedQuery()
    {
        var formTypeCategory = base.GetPaginatedQuery();

        var mapFormTypeCategory = MapFormTypeCategory(formTypeCategory);

        return mapFormTypeCategory.AsNoTracking().OrderByDescending(x => x.Id);
    }
    public async Task<FormTypeCategory> GetFormTypeCategoryByFormTypeId(string formTypeId)
    {
        var formTypeCategory = SelectFormTypeCategory( base.FilterBy(x => x.FormTypeId.Equals(formTypeId)));

        var mapFormTypeCategory = MapFormTypeCategory(formTypeCategory);

        return await mapFormTypeCategory.FirstOrDefaultAsync();

        //return await _dbContext.FormTypeCategories.Active()
        //    .Where(x => x.FormTypeId.Equals(formTypeId))
        //    .FirstOrDefaultAsync();
    }

    public async Task<List<FormTypeCategory>> GetFormTypeCategoryByName(string name)
    {
        var formTypeCategory = SelectFormTypeCategory(_dbContext.FormTypeCategories.Active().AsNoTracking().
            Where(x => x.Name.ToLower().Equals(name.ToLower())));
            //base.FilterBy(x => 
           // x.Name.Trim().ToLower().Replace(" ","").Equals(name.Trim().ToLower().Replace(" ","")));

        var mapFormTypeCategory = MapFormTypeCategory(formTypeCategory);

        return await mapFormTypeCategory.ToListAsync();
    }
    public async Task<FormTypeCategory> GetFormTypeCategoryByFormTypeIdAndVersion(string formTypeId, string version)
    {
        var formTypeCategory = SelectFormTypeCategory(base.FilterBy(x => x.FormTypeId.Equals(formTypeId) && x.Version.Contains(version)));

        var mapFormTypeCategory = MapFormTypeCategory(formTypeCategory);

        return await mapFormTypeCategory.FirstOrDefaultAsync();

        //return await _dbContext.FormTypeCategories.Active()
        //    .Where(x => x.FormTypeId.Equals(formTypeId) && x.Version.Contains(version))
        //    .FirstOrDefaultAsync();
    }

    public Task<bool> IsFormTypeCategoryNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.FormTypeName.Equals(name))
            : Entities.Where(e => e.FormTypeName.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsFormTypeCategoryNameUnique(string name)
    {
        var match = _dbContext.FormTypeCategories.Any(x => x.Name.Equals(name));
        return Task.FromResult(match);
    }

    public async Task<List<string>> IsFormNamesUnique(List<string> name)
    {
        var matches = await _dbContext.FormTypeCategories
            .AsNoTracking()
           .Where(e => name.Contains(e.Name))
            .Select(e => e.Name)
            .ToListAsync();

        return matches;
    }

    public async Task<List<FormTypeCategory>> GetFormTypeCategoryByIds(List<string> ids)
    {
        return await _dbContext.FormTypeCategories
            .AsNoTracking()
            .Where(e => ids.Contains(e.ReferenceId))
            .ToListAsync();
    }

    public async Task<List<FormTypeCategory>> GetFormTypeCategoryNames()
    {
        return await _dbContext.FormTypeCategories.Where(x => x.IsActive)
            .Select(x => new FormTypeCategory { ReferenceId = x.ReferenceId, Name = x.Name }).ToListAsync();
    }

    public async Task<List<FormTypeCategory>> GetFormTypeCategoryByFormId(string formId)
    {
        var formTypeCategory = SelectFormTypeCategory(base.FilterBy(x => x.FormId.Equals(formId)));

        var mapFormTypeCategory = MapFormTypeCategory(formTypeCategory);

        return await mapFormTypeCategory.ToListAsync();
        //return await _dbContext.FormTypeCategories.Active().Where(x => x.FormId.Equals(formId)).ToListAsync();
    }

    public async Task<List<FormTypeCategory>> GetFormTypeCategoriesByFormIds(List<string> formIds)
    {
        var formTypeCategory =await SelectFormTypeCategory(MapFormTypeCategory(base.FilterBy(x => formIds.Contains(x.FormId)))).ToListAsync();

        return formTypeCategory;

    }
    public async Task<List<FormTypeCategory>> GetFormTypeCategoriesByFormTypeIds(List<string> formTypeIds)
    {
        var formTypeCategory =await SelectFormTypeCategory(MapFormTypeCategory(base.FilterBy(x => formTypeIds.Contains(x.FormTypeId)))).ToListAsync();

        return formTypeCategory;
    }

    private IQueryable<FormTypeCategory> MapFormTypeCategory(IQueryable<FormTypeCategory> formTypeCategories)
    {
        return formTypeCategories.Select(x => new
        {
            Form = _dbContext.Forms.FirstOrDefault(f => f.ReferenceId.Equals(x.FormId)),
            FormType = _dbContext.ComponentTypes.FirstOrDefault(c => c.ReferenceId.Equals(x.FormTypeId)),
            FormTypeCategory = x
        })
            .Select(result => new FormTypeCategory
            {
                Id = result.FormTypeCategory.Id,
                ReferenceId = result.FormTypeCategory.ReferenceId,
                Name = result.FormTypeCategory.Name,
                FormTypeId = result.FormType.ReferenceId,
                FormTypeName = result.FormType.ComponentName,
                Logo = result.FormTypeCategory.Logo,
                FormId = result.Form.ReferenceId,
                FormName = result.Form.Name,
                FormVersion = result.Form.Version,
                Properties = result.FormTypeCategory.Properties,
                Version = result.FormTypeCategory.Version,
                IsActive = result.FormTypeCategory.IsActive,
                CreatedBy = result.FormTypeCategory.CreatedBy,
                CreatedDate = result.FormTypeCategory.CreatedDate,
                LastModifiedBy = result.FormTypeCategory.LastModifiedBy,
                LastModifiedDate = result.FormTypeCategory.LastModifiedDate,
            });
    }
    private IQueryable<FormTypeCategory> SelectFormTypeCategory(IQueryable<FormTypeCategory> query)
    {
        return query.Select(x => new FormTypeCategory
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            Name = x.Name,
            FormId = x.FormId,
            FormName = x.FormName,
            FormTypeId = x.FormTypeId,
            FormTypeName = x.FormTypeName,
            Logo = x.Logo,
            Version = x.Version,
            Properties = x.Properties,
            FormVersion = x.FormVersion
        });
    }
}