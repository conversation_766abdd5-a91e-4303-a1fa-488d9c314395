using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.CyberComponentMappingModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetPaginatedList;

public class GetCyberComponentMappingPaginatedListQueryHandler : IRequestHandler<
    GetCyberComponentMappingPaginatedListQuery, PaginatedResult<CyberComponentMappingListVm>>
{
    private readonly ICyberComponentMappingRepository _cyberComponentMappingRepository;
    private readonly IMapper _mapper;

    public GetCyberComponentMappingPaginatedListQueryHandler(IMapper mapper,
        ICyberComponentMappingRepository cyberComponentMappingRepository)
    {
        _mapper = mapper;
        _cyberComponentMappingRepository = cyberComponentMappingRepository;
    }

    public async Task<PaginatedResult<CyberComponentMappingListVm>> Handle(
        GetCyberComponentMappingPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new CyberComponentMappingFilterSpecification(request.SearchString);

        var queryable =await  _cyberComponentMappingRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var cyberComponentMappingList = _mapper.Map<PaginatedResult<CyberComponentMappingListVm>>(queryable);

        return cyberComponentMappingList;
        //var queryable = _cyberComponentMappingRepository.GetPaginatedQuery();

        //var productFilterSpec = new CyberComponentMappingFilterSpecification(request.SearchString);

        //var cyberComponentMappingList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<CyberComponentMappingListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return cyberComponentMappingList;
    }
}