﻿using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByEntityId;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller
{
    public class OracleDataguardControllerShould
    {
        private readonly Mock<IDashboardViewService> _mockDashboardViewService;
        private readonly Mock<ILogger<OracleDataguardController>> _mockLogger;
        private readonly OracleDataguardController _controller;

        public OracleDataguardControllerShould()
        {
            _mockDashboardViewService = new Mock<IDashboardViewService>();
            _mockLogger = new Mock<ILogger<OracleDataguardController>>();
            _controller = new OracleDataguardController(_mockDashboardViewService.Object, _mockLogger.Object);
        }

        [Fact]
        public void List_Returns_ViewResult()
        {
            
            var result = _controller.List();

            
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
        }

        [Fact]
        public async Task GetMonitorServiceStatusByIdAndType_Returns_GetByEntityIdVm()
        {
            
            var monitorId = "testMonitorId";
            var type = "testType";
            var expectedResult = new GetByEntityIdVm();
            _mockDashboardViewService
                .Setup(service => service.GetMonitorServiceStatusByIdAndType(monitorId, type))
                .ReturnsAsync(expectedResult);

            
            var result = await _controller.GetMonitorServiceStatusByIdAndType(monitorId, type);

            
            Assert.Equal(expectedResult, result);
            _mockDashboardViewService.Verify(service => service.GetMonitorServiceStatusByIdAndType(monitorId, type), Times.Once);
        }

        [Fact]
        public async Task GetMonitorServiceStatusByIdAndType_Logs_Error_On_Exception()
        {
            
            var monitorId = "testMonitorId";
            var type = "testType";
            var exception = new Exception("Test exception");
            _mockDashboardViewService
                .Setup(service => service.GetMonitorServiceStatusByIdAndType(monitorId, type))
                .ThrowsAsync(exception);

            
            var result = await _controller.GetMonitorServiceStatusByIdAndType(monitorId, type);

            
            Assert.Null(result);
            
        }
    }
}
