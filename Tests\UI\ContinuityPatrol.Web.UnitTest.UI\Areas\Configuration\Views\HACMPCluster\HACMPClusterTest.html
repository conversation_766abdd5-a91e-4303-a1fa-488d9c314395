<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>QUnit Test Runner for HACMP Cluster</title>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.20.1.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">

    <!-- jQuery and dependencies -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <!-- QUnit and Sinon for testing -->
    <script src="https://code.jquery.com/qunit/qunit-2.20.1.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sinon.js/15.2.0/sinon.min.js"></script>

    <!-- Application and test scripts -->
    <script src="/js/Common/common.js"></script>
    <script src="/js/Configuration/Infra Components/HACMPCluster/HACMPCluster.js"></script>
    <script src="/js/Configuration/Infra Components/HACMPCluster/HACMPClusterTest.js"></script>
</head>
<body>
    <h1>HACMPCluster QUnit Test Runner</h1>
    <div id="qunit"></div>
    <!-- Test Fixture: Pre-populated with required elements -->
    <div id="qunit-fixture">
        <!-- Permission Data -->
        <div id="hacmpConfigCreate" data-create-permission="TRUE"></div>
        <div id="hacmpConfigDelete" data-delete-permission="TRUE"></div>

        <!-- Create Button -->
        <button id="hacmpCreateBtn" class="btn">Create</button>

        <!-- Search Input -->
        <input id="hacmpSearchInp" type="text" placeholder="Search..." />

        <!-- Cluster Table -->
        <table id="hacmpClusterTable">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Server</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <!-- DataTable will populate rows here -->
            </tbody>
        </table>

        <!-- Pagination Controls -->
        <div class="pagination-column"></div>

        <!-- DataTables Empty Message -->
        <span class="dataTables_empty"></span>

        <!-- Save and Delete Buttons (for modals/dialogs) -->
        <button id="SaveFunction">Save</button>
        <button id="hacmpConfirmDeleteBtn">Delete</button>
    </div>
    <script type="text/javascript">
        // Set RootUrl for test environment
        var RootUrl = '/';
    </script>
</body>
</html>
