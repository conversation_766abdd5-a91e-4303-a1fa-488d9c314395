﻿using ContinuityPatrol.Application.Features.Workflow.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Workflow.Events;

public class WorkflowUpdatedEventHandlerTests : IClassFixture<WorkflowFixture>
{
    private readonly WorkflowFixture _workflowFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly WorkflowUpdatedEventHandler _handler;

    public WorkflowUpdatedEventHandlerTests(WorkflowFixture workflowFixture)
    {
        _workflowFixture = workflowFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockWorkflowEventLogger = new Mock<ILogger<WorkflowUpdatedEventHandler>>();

        _mockUserActivityRepository = WorkflowRepositoryMocks.CreateWorkflowEventRepository(_workflowFixture.UserActivities);

        _handler = new WorkflowUpdatedEventHandler(mockLoggedInUserService.Object, mockWorkflowEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateWorkflowEventUpdated()
    {
        _workflowFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowFixture.WorkflowUpdatedEvent, CancellationToken.None);

        result.Equals(_workflowFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowFixture.WorkflowUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_UpdateWorkflowEventUpdated()
    {
        _workflowFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowFixture.WorkflowUpdatedEvent, CancellationToken.None);

        result.Equals(_workflowFixture.UserActivities[0].Id);

        result.Equals(_workflowFixture.WorkflowUpdatedEvent.WorkflowName);

        await Task.CompletedTask;
    }
}
