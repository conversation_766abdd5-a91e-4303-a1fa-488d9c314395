﻿using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Domain.ViewModels.DriftJobModel;
using ContinuityPatrol.Application.Features.DriftJob.Commands.Create;
using ContinuityPatrol.Application.Features.DriftJob.Commands.Update;
using ContinuityPatrol.Application.Features.DriftJob.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Application.Features.DriftJob.Events.Paginated;
using ContinuityPatrol.Application.Features.DriftJob.Commands.UpdateState;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.CyberJobManagementModel;
using ContinuityPatrol.Application.Features.DriftJob.Commands.RescheduleJob;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.UpdateStatus;


namespace ContinuityPatrol.Web.Areas.Drift.Controllers;

[Area("Drift")]
public class DriftManagementController : BaseController
{
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;
    private readonly ILogger<DriftManagementController> _logger;
    private readonly IPublisher _publisher;
    public DriftManagementController(ILogger<DriftManagementController> logger, IDataProvider dataProvider, IMapper mapper, IPublisher publisher)
    {
        _dataProvider = dataProvider;
        _mapper = mapper;
        _publisher = publisher;
        _logger = logger;
    }
    public async Task<IActionResult> List()
    {
        await _publisher.Publish(new DriftJobPaginatedEvent());
        _logger.LogDebug("Entering List method in DriftManagement");

        return View();
    }

    public async Task<JsonResult> GetSolutionDetails()
    {
        _logger.LogDebug("Entering GetSolutionDetails method in DriftManagement");

        try
        {
            var replicationTypeList = await _dataProvider.ComponentType.GetComponentTypeListByName("Replication");
            _logger.LogDebug($"Successfully retrieved componentType by name 'Replication'");
            return Json(new { Success = true, data = replicationTypeList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on drift management page while retrieving solution type.", ex);
            return ex.GetJsonException();
        }
    }
    public async Task<IActionResult> GetPagination(GetDriftJobPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in DriftManagement");
        try
        {
            var list = await _dataProvider.DriftJob.GetPaginatedDriftJobs(query);
            _logger.LogDebug("Successfully retrieved drift management paginated list on DriftManagement page");
            return Json(new { Success = true, data = list });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on drift management page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }
    }
    public async Task<IActionResult> GetDriftProfileList()
    {
        _logger.LogDebug("Entering GetDriftProfileList method in DriftManagement");
        try
        {
            var list = await _dataProvider.DriftProfile.GetDriftProfileList();
            _logger.LogDebug("Successfully retrieved drift profile list on DriftManagement page");
            return Json(new { Success = true, data = list });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on drift management page while retrieving drift profile list.", ex);
            return ex.GetJsonException();
        }

    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(DriftJobListVm parameterListVm)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in DriftManagement");
        var templateId = Request.Form["id"].ToString();
        try
        {
            BaseResponse result;
            if (templateId.IsNullOrWhiteSpace())
            {
                var createDriftCommand = _mapper.Map<CreateDriftJobCommand>(parameterListVm);
                result = await _dataProvider.DriftJob.CreateAsync(createDriftCommand);
                _logger.LogDebug($"Creating DriftManagement '{createDriftCommand.Name}'");

            }
            else
            {
                var updateDriftCommand = _mapper.Map<UpdateDriftJobCommand>(parameterListVm);
                result = await _dataProvider.DriftJob.UpdateAsync(updateDriftCommand);
                _logger.LogDebug($"Updating DriftManagement '{updateDriftCommand.Name}'");
            }
            _logger.LogDebug("CreateOrUpdate operation completed successfully in DriftManagement, returning view.");
            return Json(new { Success = true, data = result });
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on drift management page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on drift management page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }
    [HttpPut]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> UpdateJobState(UpdateDriftJobStateCommand updateDriftJobStateCommand)
    {
        _logger.LogDebug("Entering UpdateJobState method in DriftManagement.");
        try
        {
            var getState = await _dataProvider.DriftJob.UpdateDriftJobState(updateDriftJobStateCommand);

            _logger.LogDebug("Successfully updated job state in DriftManagement.");

            return Json(new { Success = true, data = getState });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on DriftManagement page while updating job state.", ex);

            return ex.GetJsonException();
        }

    }

   
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> ResetDrift(RescheduleDriftJobCommand rescheduleDriftJobCommand)
    {
        _logger.LogDebug("Entering DriftManagementStatus method in MDriftManagement");
        try
        {
            rescheduleDriftJobCommand.Status = "Pending";
            var drCommand = _mapper.Map<RescheduleDriftJobCommand>(rescheduleDriftJobCommand);
            var result = await _dataProvider.DriftJob.RescheduleDriftJob(drCommand);
            _logger.LogDebug($"Successfully reset status as '{drCommand.Status}' in DriftManagement");
            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred DriftManagement page while reset the status.", ex);
            return ex.GetJsonException();
        }
    }
    public async Task<bool> IsNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsNameExist method in DriftManagement");

        try
        {
            var isNameExits = await _dataProvider.DriftJob.IsDriftJobNameExist(name, id);
            _logger.LogDebug("Returning result for IsNameExist on DriftManagement");
            return isNameExits;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on drift management page while checking if drift management name exists for : {name}.", ex);
            return false;
        }
    }

    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in DriftManagement");
        try
        {
            var delete = await _dataProvider.DriftJob.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted record in DriftManagement");
            return Json(new { Success = true, data = delete });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on drift management.", ex);
            return ex.GetJsonException();
        }

    }
}
