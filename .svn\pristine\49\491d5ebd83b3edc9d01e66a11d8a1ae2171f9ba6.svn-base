﻿@model ContinuityPatrol.Domain.ViewModels.AlertReceiverModel.AlertReceiverViewModal
@* <style>
  
    .container {
        position: relative;
    }

    .suggestions {
        position: absolute;
        overflow-y: scroll !important;
        height:100px !important;
        top: 110px;
        left: 0;
        border: 1px solid #ccc;
        background: black;
        display: none;
        z-index: 10;
    }

    .suggestion-item {
        padding: 5px;
        cursor: pointer;
    }

        .suggestion-item:hover {
            background-color: #f0f0f0;
        }
</style> *@

<div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-notification-manager"></i><span>Notification Manager Configuration</span></h6>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
        </div>
        <div class="modal-body">
            <form id="CreateForm" asp-controller="NotificationManager" asp-action="CreateOrUpdate" method="post" class="tab-wizard wizard-circle wizard clearfix">
                @Html.AntiForgeryToken()
                <div class="row row-cols-2 mt-2">
                    <div class="col">
                        <div class="mb-3 form-group">
                            <div class="form-label">Name</div>
                            <div class="input-group" id="container">
                                <span class="input-group-text"><i class="cp-name"></i></span>
                                <input asp-for="Name" id="notifName" type="text" class="form-control" placeholder="Enter Notification Name" maxlength="100" autocomplete="off" />
                            </div>
                            <span asp-validation-for="Name" id="notifNameError"></span>
                            @* <span id="suggestions"></span> *@

                        </div>
                    </div>
                    <div class="col">
                        <div class="mb-3 form-group">
                            <div class="form-label">Email Address</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-email"></i></span>
                                <input asp-for="EmailAddress" id="notifEmail" type="text" maxlength="320" class="form-control" placeholder="Enter Email Address" autocomplete="off" />
                            </div>
                            <span asp-validation-for="EmailAddress" id="notifEmailError"></span>
                        </div>
                    </div>
                </div>
                <div class="col-xl-12">
                    <div class="d-flex">
                        <div class="custom-control custom-checkbox flex-fill">
                            <input asp-for="IsMail" type="checkbox"
                                   id="notifIsMobile" name="isMail"
                                   class="form-check-input custom-cursor-default-hover"
                                   cursorshover="true"><label class="custom-control-label form-label custom-cursor-default-hover"
                                                              for="isMail">Mobile</label>
                        </div>
                    </div>
                    <div id="notifMob">
                        <div class="d-flex gap-2">
                            <div style="width:155px">
                                <div class="form-group">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-mobile-icon"></i></span>
                                        <select id="notifMobilePre" class="form-select-modal" autocomplete="off" data-placeholder="Select Country Code">
                                            <option></option>
                                        </select>
                                    </div>
                                    <span asp-validation-for="MobileNumber" id="notifMobilePreError" class="text-start"></span>
                                </div>
                            </div>
                            <div class="w-100">
                                <div class="form-group">
                                    <div class="input-group">
                                        <span class="input-group-text"></span>
                                        <input id="notifMobileNum" type="text" class="form-control" placeholder="Enter Mobile Number" autocomplete="off" maxlength="15" />
                                    </div>
                                    <span asp-validation-for="MobileNumber" id="notifMobileError"></span>
                                </div>
                            </div>
                            <input asp-for="MobileNumber" type="hidden" id="comMobile" />
                        </div>
                    </div>
                    @*          <div>
                    <div class="form-group" id="notifMobPre" style="width:10%">
                    <div class="form-label" >Mobile Number</div>
                    <div class="form-label" >Country Code</div>
                    <div class="input-group">
                    <span class="input-group-text"><i class="cp-mobile-icon"></i></span>
                    <input id="mobilepre" type="text" class="form-control" placeholder="+91" autocomplete="off" maxlength="5" />
                    </div>
                    <span asp-validation-for="MobileNumber" id="MobilePre-error" style="font-size:8px"></span>
                    </div>
                    <div class="form-group" id="mob">
                    <div class="form-label" >Mobile Number</div>
                    <div class="input-group">
                    <span class="input-group-text"><i class="cp-mobile-icon"></i></span>

                    <span class="input-group-text pb-0" style="width: 60px;"><input id="mobilepre" type="text" class="form-control" placeholder="+91" autocomplete="off" maxlength="5" /></span>
                    <input id="mobilenum" type="text" class="form-control" placeholder="Enter Mobile Number" autocomplete="off" maxlength="15" />
                    </div>
                    <div class="d-flex align-item-center justify-content-between">
                    <span asp-validation-for="MobileNumber" id="MobilePre-error" class="w-25 text-start"></span>

                    <span asp-validation-for="MobileNumber" id="Mobile-error"></span>
                    </div>

                    </div>
                    <input asp-for="MobileNumber" type="hidden" id="comMobile" />
                    </div>
                    </div> *@
                    <div class="d-flex gap-2">
                        <div class="mb-3 pr-3">
                            <div class="form-check"><input asp-for="IsActiveUser" type="checkbox" id="notifActiveUser" name="isActiveUser" class="form-check-input custom-cursor-default-hover" cursorshover="true"><label for="formBasicCheckbox" class="form-check-label custom-cursor-default-hover">Active User</label></div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check"><input asp-for="IsSendReport" type="checkbox" id="notifSendReport" name="isSendReport" class="form-check-input custom-cursor-default-hover"><label for="formBasicCheckbox1" class="form-check-label custom-cursor-default-hover">Send Report</label></div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        Assigned InfraObject(s)
                        <span asp-validation-for="Properties" id="notifTreeError"></span>
                    </div>
                   
                    <div class="Workflow-Tree" style="height: calc(100vh - 400px);overflow:auto">
                        <div>
                         
                            <details class="ms-0" id="notifExpand">
                                <summary>
                                 
                                    <input class="form-check-input selectAll " type="checkbox" id="notifSelectAll">All <!-- Add "Select All" checkbox -->
                                    <ul class="tree" id="notifyTreeView">
                                        <!-- JSON data will be dynamically generated here -->
                                    </ul>
                                </summary>
                            </details>
                        </div>
                       
                        <input asp-for="Properties" type="hidden" id="textProperties" />
                        <input asp-for="Id" type="hidden" id="notifyId" class="form-control" />
                    </div>

            </form>
        </div>
        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary">
                <i class="cp-note me-1"></i>Note: All fields are mandatory
                except optional
            </small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm" id="notifyBtnCancel"  data-bs-dismiss="modal">Cancel</button>
                <button type="button" id="notifSave" class="btn btn-primary btn-sm">Save</button>
            </div>
        </div>
    </div>
</div>

@section Scripts
{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}