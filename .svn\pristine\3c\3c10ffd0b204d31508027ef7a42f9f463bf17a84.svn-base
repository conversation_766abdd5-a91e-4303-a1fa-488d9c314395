﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class DrCalenderRepository : BaseRepository<DrCalenderActivity>, IDrCalenderRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public DrCalenderRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override Task<IReadOnlyList<DrCalenderActivity>> ListAllAsync()
    {
        return _loggedInUserService.IsParent
            ? base.ListAllAsync()
            : FindByFilterAsync(drCalender => drCalender.ReferenceId.Equals(_loggedInUserService.CompanyId));
    }

    public Task<bool> IsDrCalenderNameUnique(string name)
    {
        var matches = _dbContext.DrCalenderActivity.Any(e => e.ActivityName.Equals(name));

        return Task.FromResult(matches);
    }

    public Task<IReadOnlyList<DrCalenderActivity>> DrCalendarDrillEventList()
    {
        return _loggedInUserService.IsParent
            ? FindByFilterAsync(dr => dr.ScheduledStartDate >= DateTime.Today)
            : FindByFilterAsync(dr => dr.ScheduledStartDate >= DateTime.Today && _loggedInUserService.CompanyId.Equals(dr.CompanyId));
      
    }
    public Task<bool> IsActivityNameExist(string name, string id,DateTime scheduleStartDate)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.DrCalenderActivity.Any(x => x.ActivityName.Equals(name) && x.ScheduledStartDate.Equals(scheduleStartDate)))
            : Task.FromResult(_dbContext.DrCalenderActivity.Where(x => x.ActivityName.Equals(name)&& x.ScheduledStartDate.Equals(scheduleStartDate)).ToList()
                .Unique(id));
    }

    public Task<IReadOnlyList<DrCalenderActivity>> UpComingDrillList()
    {
        var tenDaysAfter = DateTime.Now.Date.AddDays(10);
        
        return _loggedInUserService.IsParent
            ? FindByFilterAsync(dr => dr.ScheduledStartDate >= DateTime.Now && dr.ScheduledStartDate.Date <= tenDaysAfter)
            : FindByFilterAsync(dr => dr.ScheduledStartDate >= DateTime.Now &&
                                 dr.ScheduledStartDate.Date <= tenDaysAfter &&
                                 _loggedInUserService.CompanyId.Equals(dr.CompanyId));
    }

    public async  Task<List<DrCalenderActivity>> GetByWorkflowProfileId(string workflowProfileId)
    {
        var result =await (_loggedInUserService.IsParent
            ?Entities.DescOrderById().Where(dr => dr.WorkflowProfiles.Contains(workflowProfileId)&& dr.ScheduledStartDate<DateTime.Now)
            : Entities.DescOrderById().Where(dr=>dr.WorkflowProfiles.Contains(workflowProfileId) && dr.CompanyId.Equals(_loggedInUserService.CompanyId) && dr.ScheduledStartDate < DateTime.Now))
            .Select( x=>new DrCalenderActivity
            {
                Id = x.Id,
                ReferenceId=x.ReferenceId,
                ActivityName = x.ActivityName,
                ActivityType = x.ActivityType,
                ScheduledStartDate = x.ScheduledStartDate,
                ScheduledEndDate = x.ScheduledEndDate,
                WorkflowProfiles = x.WorkflowProfiles,
                CompanyId = x.CompanyId,
                Responsibility=x.Responsibility

            }).ToListAsync();

        return result.ToList();
    }
}