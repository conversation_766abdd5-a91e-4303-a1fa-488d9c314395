﻿using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceHealthLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class BusinessServiceHealthLogProfile : Profile
{
    public BusinessServiceHealthLogProfile()
    {
        CreateMap<BusinessServiceHealthLog, CreateBusinessServiceHealthLogCommand>().ReverseMap();
        CreateMap<UpdateBusinessServiceHealthLogCommand, BusinessServiceHealthLog>()
            .ForMember(x => x.Id, y => y.Ignore());

        CreateMap<BusinessServiceHealthLog, BusinessServiceHealthLogListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<BusinessServiceHealthLog, BusinessServiceHealthLogDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<BusinessServiceHealthLog, BusinessServiceHealthLogListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<PaginatedResult<BusinessServiceHealthLog>, PaginatedResult<BusinessServiceHealthLogListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}