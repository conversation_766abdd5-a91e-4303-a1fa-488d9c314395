﻿using System.Text.Json;
using ContinuityPatrol.Shared.Core.Extensions;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace ContinuityPatrol.Web.Helper;

public class HealthCheckResponseWriter
{
    public static Task WriteJsonResponse(HttpContext context, HealthReport report)
    {
        context.Response.ContentType = "application/json";

        var response = new
        {
            status = report.Status.ToString(),
            entries = report.Entries.ToDictionary(
                e => e.Key,
                e => new
                {
                    status = e.Value.Status.ToString(),
                    description = e.Value.Description.IsNullOrWhiteSpace() ? GetDescriptionFor(e.Key) : e.Value.Description,
                }),

            totalDuration = report.TotalDuration.TotalMilliseconds
        };

        var options = new JsonSerializerOptions { WriteIndented = true };

        return context.Response.WriteAsync(JsonSerializer.Serialize(response, options));
    }


    private static string GetDescriptionFor(string name) =>
        name switch
        {
            "MySql Health Check" => "Checks MySQL database connectivity",
            "MSSQL Health Check" => "Checks SQL Server database connectivity",
            "Oracle Health Check" => "Checks Oracle database connectivity",
            "Postgres Health Check" => "Checks PostgreSQL database connectivity",
            "Application" => "The application is running.",
            "Seq Health Check" => "The seq service is running.",
            _ => null
        };

}