﻿using ContinuityPatrol.Application.Features.WorkflowHistory.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowHistory.Commands;

public class CreateWorkflowHistoryTests : IClassFixture<WorkflowHistoryFixture>
{
    private readonly WorkflowHistoryFixture _workflowHistoryFixture;
    private readonly Mock<IWorkflowHistoryRepository> _mockWorkflowHistoryRepository;
    private readonly CreateWorkflowHistoryCommandHandler _handler;

    public CreateWorkflowHistoryTests(WorkflowHistoryFixture workflowHistoryFixture)
    {
        var mockPublisher = new Mock<IPublisher>();
        _workflowHistoryFixture = workflowHistoryFixture;
        _mockWorkflowHistoryRepository = WorkflowHistoryRepositoryMocks.CreateWorkflowHistoryRepository(_workflowHistoryFixture.WorkflowHistories);
        _handler = new CreateWorkflowHistoryCommandHandler(_workflowHistoryFixture.Mapper, _mockWorkflowHistoryRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Should_Increase_Count_When_AddValid_WorkflowHistory()
    {
        await _handler.Handle(_workflowHistoryFixture.CreateWorkflowHistoryCommand, CancellationToken.None);

        var allCategories = await _mockWorkflowHistoryRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_workflowHistoryFixture.WorkflowHistories.Count);
    }

    [Fact]
    public async Task Handle_Return_CreateWorkflowHistoryResponse_When_AddValidWorkflowHistory()
    {
        var result = await _handler.Handle(_workflowHistoryFixture.CreateWorkflowHistoryCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateWorkflowHistoryResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowHistoryFixture.CreateWorkflowHistoryCommand, CancellationToken.None);

        _mockWorkflowHistoryRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.WorkflowHistory>()), Times.Once);
    }
}