﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class BusinessServiceHealthStatusRepositoryMocks
{
    public static Mock<IBusinessServiceHealthStatusRepository> CreateBusinessServiceHealthStatusRepository(List<BusinessServiceHealthStatus> businessServiceHealthStatusList)
    {
        var createBusinessServiceHealthStatusRepository = new Mock<IBusinessServiceHealthStatusRepository>();

        createBusinessServiceHealthStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServiceHealthStatusList);

        createBusinessServiceHealthStatusRepository.Setup(repo => repo.AddAsync(It.IsAny<BusinessServiceHealthStatus>())).ReturnsAsync((BusinessServiceHealthStatus businessServiceHealthStatus) =>
            {
                businessServiceHealthStatus.Id = new Fixture().Create<int>();

                businessServiceHealthStatus.ReferenceId = new Fixture().Create<Guid>().ToString();

                businessServiceHealthStatusList.Add(businessServiceHealthStatus);

                return businessServiceHealthStatus;
            });

        return createBusinessServiceHealthStatusRepository;
    }

    public static Mock<IBusinessServiceHealthStatusRepository> UpdateBusinessServiceHealthStatusRepository(List<BusinessServiceHealthStatus> businessServiceHealthStatusList)
    {
        var updateBusinessServiceHealthStatusRepository = new Mock<IBusinessServiceHealthStatusRepository>();

        updateBusinessServiceHealthStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServiceHealthStatusList);

        updateBusinessServiceHealthStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessServiceHealthStatusList.SingleOrDefault(x => x.ReferenceId == i));

        updateBusinessServiceHealthStatusRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BusinessServiceHealthStatus>())).ReturnsAsync((BusinessServiceHealthStatus businessServiceHealthStatus) =>
            {
                var index = businessServiceHealthStatusList.FindIndex(item => item.ReferenceId == businessServiceHealthStatus.ReferenceId);

                businessServiceHealthStatusList[index] = businessServiceHealthStatus;

                return businessServiceHealthStatus;
            });
        return updateBusinessServiceHealthStatusRepository;
    }

    public static Mock<IBusinessServiceHealthStatusRepository> DeleteBusinessServiceHealthStatusRepository(List<BusinessServiceHealthStatus> businessServiceHealthStatusList)
    {
        var deleteBusinessServiceHealthStatusRepository = new Mock<IBusinessServiceHealthStatusRepository>();

        deleteBusinessServiceHealthStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServiceHealthStatusList);

        deleteBusinessServiceHealthStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessServiceHealthStatusList.SingleOrDefault(x => x.ReferenceId == i));

        deleteBusinessServiceHealthStatusRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BusinessServiceHealthStatus>())).ReturnsAsync((BusinessServiceHealthStatus businessServiceHealthStatus) =>
                {
                    var index = businessServiceHealthStatusList.FindIndex(item => item.ReferenceId == businessServiceHealthStatus.ReferenceId);

                    businessServiceHealthStatus.IsActive = false;

                    businessServiceHealthStatusList[index] = businessServiceHealthStatus;

                    return businessServiceHealthStatus;
                });

        return deleteBusinessServiceHealthStatusRepository;
    }

    public static Mock<IBusinessServiceHealthStatusRepository> GetBusinessServiceHealthStatusRepository(List<BusinessServiceHealthStatus> businessServiceHealthStatusList)
    {
        var businessServiceHealthStatusRepository = new Mock<IBusinessServiceHealthStatusRepository>();

        businessServiceHealthStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessServiceHealthStatusList.SingleOrDefault(x => x.ReferenceId == i));

        businessServiceHealthStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServiceHealthStatusList);

        return businessServiceHealthStatusRepository;
    }

    public static Mock<IBusinessServiceHealthStatusRepository> GetBusinessServiceHealthStatusEmptyRepository()
    {
        var businessServiceHealthStatusRepository = new Mock<IBusinessServiceHealthStatusRepository>();

        businessServiceHealthStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<BusinessServiceHealthStatus>());

        return businessServiceHealthStatusRepository;
    }

    public static Mock<IBusinessServiceHealthStatusRepository> GetPaginatedBusinessServiceHealthStatusRepository(List<BusinessServiceHealthStatus> businessServiceHealthStatusList)
    {
        var getPaginatedBusinessServiceHealthStatusRepository = new Mock<IBusinessServiceHealthStatusRepository>();

        var queryableBusinessServiceHealthStatus = businessServiceHealthStatusList.BuildMock();

        getPaginatedBusinessServiceHealthStatusRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableBusinessServiceHealthStatus);

        return getPaginatedBusinessServiceHealthStatusRepository;
    }
}