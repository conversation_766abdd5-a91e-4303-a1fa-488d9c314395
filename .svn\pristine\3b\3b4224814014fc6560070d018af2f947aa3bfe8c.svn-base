using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class FormRepositoryTests : IClassFixture<FormFixture>, IDisposable
{
    private readonly FormFixture _formFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly FormRepository _repository;

    public FormRepositoryTests(FormFixture formFixture)
    {
        _formFixture = formFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _repository = new FormRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region GetFormNames Tests

    [Fact]
    public async Task PaginatedListAllAsync_WithNullSearchString_ReturnsAllData()
    {
        // Arrange
        var listValue = _formFixture.FormPaginationList;
        foreach (var val in listValue)
            val.IsActive = true;
        await _dbContext.Forms.AddRangeAsync(listValue);
        await _dbContext.SaveChangesAsync();

        string? searchString = null;
        var spec = new FormFilterSpecification(searchString);

        // Act
        var result = await _repository.PaginatedListAllAsync(
            pageNumber: 1,
            pageSize: 20,
            productFilterSpec: spec,
            sortColumn: "Id",
            sortOrder: "asc"
        );
    }
        [Fact]
    public async Task GetFormNames_ReturnsActiveForms_WhenActiveFormsExist()
    {
        // Arrange
        var activeForms = new[]
        {
            new Domain.Entities.Form
            {
                Name = "Survey Form",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = true,
                CompanyId = FormFixture.CompanyId
            },
            new Domain.Entities.Form
            {
                Name = "Assessment Form",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = false,
                CompanyId = FormFixture.CompanyId
            }
        };

        var inactiveForm = new Domain.Entities.Form
        {
            Name = "Inactive Form",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = false,
            IsPublish = true,
            CompanyId = FormFixture.CompanyId
        };

        await _dbContext.Forms.AddRangeAsync(activeForms);
        await _dbContext.Forms.AddAsync(inactiveForm);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormNames();

        // Assert
        Assert.True(result.Count >= 2); // At least the 2 we added
        Assert.Contains(result, x => x.Name == "Assessment Form");
        Assert.Contains(result, x => x.Name == "Survey Form");
        Assert.True(result.First().Name.CompareTo(result.Last().Name) <= 0); // Ordered by name
        // Note: IsActive is not set in the projection, but .Active() filter ensures only active forms are returned
    }

    [Fact]
    public async Task GetFormNames_ReturnsFormsOrderedByName_WhenFormsExist()
    {
        // Arrange
        var form1 = new Domain.Entities.Form
        {
            Name = "Z Form",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true,
            CompanyId = FormFixture.CompanyId
        };

        var form2 = new Domain.Entities.Form
        {
            Name = "A Form",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true,
            CompanyId = FormFixture.CompanyId
        };

        await _dbContext.Forms.AddRangeAsync(new[] { form1, form2 });
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormNames();

        // Assert
        Assert.Contains(result, x => x.Name == "A Form");
        Assert.Contains(result, x => x.Name == "Z Form");

        // Verify ordering - find the positions of our test forms
        var aFormIndex = result.FindIndex(x => x.Name == "A Form");
        var zFormIndex = result.FindIndex(x => x.Name == "Z Form");
        Assert.True(aFormIndex < zFormIndex, "Forms should be ordered by name");
    }

    #endregion

    #region IsFormNameExist Tests

    [Fact]
    public async Task IsFormNameExist_ReturnsTrue_WhenNameExists_WithoutId()
    {
        // Arrange
        var formName = "Existing Form";
        _formFixture.FormDto.Name = formName;

        await _dbContext.Forms.AddAsync(_formFixture.FormDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsFormNameExist(formName, null);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsFormNameExist_ReturnsFalse_WhenNameDoesNotExist_WithoutId()
    {
        // Arrange
        var nonExistentName = "Non Existent Form";

        // Act
        var result = await _repository.IsFormNameExist(nonExistentName, null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsFormNameExist_ReturnsFalse_WhenNameExists_WithSameId()
    {
        // Arrange
        var existingId = Guid.NewGuid().ToString();
        var formName = "Test Form";

        _formFixture.FormDto.ReferenceId = existingId;
        _formFixture.FormDto.Name = formName;

        await _dbContext.Forms.AddAsync(_formFixture.FormDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsFormNameExist(formName, existingId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsFormNameExist_ReturnsTrue_WhenNameExists_WithDifferentId()
    {
        // Arrange
        var differentId = Guid.NewGuid().ToString();
        var formName = "Duplicate Form";

        _formFixture.FormDto.Name = formName;

        await _dbContext.Forms.AddAsync(_formFixture.FormDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsFormNameExist(formName, differentId);

        // Assert
        Assert.True(result);
    }

    #endregion

    #region IsFormNameUnique Tests

    [Fact]
    public async Task IsFormNameUnique_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var formName = "Unique Form";
        _formFixture.FormDto.Name = formName;

        await _dbContext.Forms.AddAsync(_formFixture.FormDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsFormNameUnique(formName);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsFormNameUnique_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var nonExistentName = "Non Existent Form";

        // Act
        var result = await _repository.IsFormNameUnique(nonExistentName);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsFormNamesUnique Tests

    [Fact]
    public async Task IsFormNamesUnique_ReturnsMatchingNames_WhenSomeNamesExist()
    {
        // Arrange
        var forms = new[]
        {
            new Domain.Entities.Form { Name = "Form A", ReferenceId = Guid.NewGuid().ToString(), CompanyId = FormFixture.CompanyId },
            new Domain.Entities.Form { Name = "Form B", ReferenceId = Guid.NewGuid().ToString(), CompanyId = FormFixture.CompanyId },
            new Domain.Entities.Form { Name = "Form C", ReferenceId = Guid.NewGuid().ToString(), CompanyId = FormFixture.CompanyId }
        };

        await _dbContext.Forms.AddRangeAsync(forms);
        await _dbContext.SaveChangesAsync();

        var namesToCheck = new List<string> { "Form A", "Form B", "Form D", "Form E" };

        // Act
        var result = await _repository.IsFormNamesUnique(namesToCheck);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains("Form A", result);
        Assert.Contains("Form B", result);
        Assert.DoesNotContain("Form C", result);
        Assert.DoesNotContain("Form D", result);
    }

    [Fact]
    public async Task IsFormNamesUnique_ReturnsEmpty_WhenNoNamesExist()
    {
        // Arrange
        var namesToCheck = new List<string> { "Form X", "Form Y", "Form Z" };

        // Act
        var result = await _repository.IsFormNamesUnique(namesToCheck);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetFormsByIds Tests

    [Fact]
    public async Task GetFormsByIds_ReturnsForms_WhenIdsExist()
    {
        // Arrange
        var form1Id = Guid.NewGuid().ToString();
        var form2Id = Guid.NewGuid().ToString();
        var form3Id = Guid.NewGuid().ToString();

        var forms = new[]
        {
            new Domain.Entities.Form { Name = "Form 1", ReferenceId = form1Id, CompanyId = FormFixture.CompanyId },
            new Domain.Entities.Form { Name = "Form 2", ReferenceId = form2Id, CompanyId = FormFixture.CompanyId },
            new Domain.Entities.Form { Name = "Form 3", ReferenceId = form3Id, CompanyId = FormFixture.CompanyId }
        };

        await _dbContext.Forms.AddRangeAsync(forms);
        await _dbContext.SaveChangesAsync();

        var idsToGet = new List<string> { form1Id, form2Id };

        // Act
        var result = await _repository.GetFormsByIds(idsToGet);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.ReferenceId == form1Id);
        Assert.Contains(result, x => x.ReferenceId == form2Id);
        Assert.DoesNotContain(result, x => x.ReferenceId == form3Id);
    }

    [Fact]
    public async Task GetFormsByIds_ReturnsEmpty_WhenNoIdsExist()
    {
        // Arrange
        var nonExistentIds = new List<string> { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() };

        // Act
        var result = await _repository.GetFormsByIds(nonExistentIds);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetFormType Tests

    [Fact]
    public async Task GetFormType_ReturnsPublishedForms_WhenTypeMatches()
    {
        // Arrange
        var formType = "survey";
        var forms = new[]
        {
            new Domain.Entities.Form
            {
                Name = "Survey Form 1",
                Type = "survey",
                IsActive = true,
                IsPublish = true,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = FormFixture.CompanyId
            },
            new Domain.Entities.Form
            {
                Name = "Survey Form 2",
                Type = "Survey", // Different case
                IsActive = true,
                IsPublish = true,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = FormFixture.CompanyId
            },
            new Domain.Entities.Form
            {
                Name = "Assessment Form",
                Type = "assessment",
                IsActive = true,
                IsPublish = true,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = FormFixture.CompanyId
            },
            new Domain.Entities.Form
            {
                Name = "Unpublished Survey",
                Type = "survey",
                IsActive = true,
                IsPublish = false,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = FormFixture.CompanyId
            }
        };

        await _dbContext.Forms.AddRangeAsync(forms);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormType(formType);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.IsPublish));
        Assert.All(result, x => Assert.Equal("survey", x.Type.ToLower()));
    }

    [Fact]
    public async Task GetFormType_ReturnsEmpty_WhenNoMatchingTypeExists()
    {
        // Arrange
        var formType = "nonexistent";

        _formFixture.FormDto.Type = "survey";
        _formFixture.FormDto.IsPublish = true;
        await _dbContext.Forms.AddAsync(_formFixture.FormDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormType(formType);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public void GetPaginatedQuery_ReturnsActiveFormsOrderedById()
    {
        // Arrange
        var forms = new[]
        {
            new Domain.Entities.Form
            {
                Id = 1,
                Name = "Form 1",
                IsActive = true,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = FormFixture.CompanyId
            },
            new Domain.Entities.Form
            {
                Id = 2,
                Name = "Form 2",
                IsActive = true,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = FormFixture.CompanyId
            },
            new Domain.Entities.Form
            {
                Id = 3,
                Name = "Inactive Form",
                IsActive = false,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = FormFixture.CompanyId
            }
        };

        _dbContext.Forms.AddRange(forms);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedQuery().ToList();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
        Assert.Equal(2, result[0].Id); // Should be ordered by Id descending
        Assert.Equal(1, result[1].Id);
    }

    #endregion

    #region GetFormByTypeQueryable Tests

    [Fact]
    public async Task GetFormByTypeQueryable_ReturnsPaginatedFormsByType_WhenTypeExists()
    {
        // Arrange
        var targetType = "Assessment";
        var otherType = "Survey";

        var assessmentForms = new[]
        {
            new Domain.Entities.Form
            {
                Name = "Assessment Form 1",
                Type = targetType,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CompanyId = FormFixture.CompanyId
            },
            new Domain.Entities.Form
            {
                Name = "Assessment Form 2",
                Type = targetType,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CompanyId = FormFixture.CompanyId
            }
        };

        var surveyForm = new Domain.Entities.Form
        {
            Name = "Survey Form",
            Type = otherType,
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true,
            CompanyId = FormFixture.CompanyId
        };

        await _dbContext.Forms.AddRangeAsync(assessmentForms.Concat(new[] { surveyForm }));
        await _dbContext.SaveChangesAsync();
       string searchString = null;
        var specification  = new FormFilterSpecification(searchString);

        // Act
        var result = await _repository.GetFormByTypeQueryable(targetType, 1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(2, result.Data.Count);
        Assert.All(result.Data, x => Assert.Equal(targetType, x.Type));
        Assert.Contains(result.Data, x => x.Name == "Assessment Form 1");
        Assert.Contains(result.Data, x => x.Name == "Assessment Form 2");
        Assert.DoesNotContain(result.Data, x => x.Name == "Survey Form");
    }

    [Fact]
    public async Task GetFormByTypeQueryable_ReturnsEmptyPaginatedResult_WhenTypeDoesNotExist()
    {
        // Arrange
        var nonExistentType = "NonExistentType";

        var form = new Domain.Entities.Form
        {
            Name = "Test Form",
            Type = "DifferentType",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true,
            CompanyId = FormFixture.CompanyId
        };

        await _dbContext.Forms.AddAsync(form);
        await _dbContext.SaveChangesAsync();

        string searchString = null;
        var specification = new FormFilterSpecification(searchString);
        // Act
        var result = await _repository.GetFormByTypeQueryable(nonExistentType, 1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Empty(result.Data);
        Assert.Equal(0, result.TotalCount);
    }

    [Fact]
    public async Task GetFormByTypeQueryable_RespectsPaginationParameters_WhenMultipleFormsExist()
    {
        // Arrange
        var targetType = "TestType";
        var forms = new List<Domain.Entities.Form>();

        for (int i = 1; i <= 5; i++)
        {
            forms.Add(new Domain.Entities.Form
            {
                Name = $"Form {i:D2}",
                Type = targetType,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                CompanyId = FormFixture.CompanyId
            });
        }

        await _dbContext.Forms.AddRangeAsync(forms);
        await _dbContext.SaveChangesAsync();

        string searchString = null;
        var specification = new FormFilterSpecification(searchString);
        // Act - Get first page with 2 items
        var result = await _repository.GetFormByTypeQueryable(targetType, 1, 2, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(2, result.Data.Count);
        Assert.Equal(5, result.TotalCount);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(2, result.PageSize);
        Assert.True(result.TotalPages >= 3); // 5 items with page size 2 = 3 pages
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddForm_WhenValidForm()
    {
        // Arrange
        var form = _formFixture.FormDto;
        form.Name = "Test Form";
        form.Type = "Survey";
        form.Properties = "{\"fields\":[{\"name\":\"field1\",\"type\":\"text\"}]}";
        form.Version = "1.0";
        form.IsPublish = false;
        form.IsLock = false;

        // Act
        var result = await _repository.AddAsync(form);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(form.Name, result.Name);
        Assert.Equal(form.Type, result.Type);
        Assert.Equal(form.Properties, result.Properties);
        Assert.Equal(form.Version, result.Version);
        Assert.Single(_dbContext.Forms);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenFormIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsForm_WhenExists()
    {
        // Arrange
        _formFixture.FormDto.Id = 1;
        await _dbContext.Forms.AddAsync(_formFixture.FormDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_formFixture.FormDto.Id, result.Id);
        Assert.Equal(_formFixture.FormDto.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsForm_WhenExists()
    {
        // Arrange
        var referenceId = Guid.NewGuid().ToString();
        _formFixture.FormDto.ReferenceId = referenceId;

        await _dbContext.Forms.AddAsync(_formFixture.FormDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateForm_WhenValidForm()
    {
        // Arrange
        _dbContext.Forms.Add(_formFixture.FormDto);
        await _dbContext.SaveChangesAsync();

        _formFixture.FormDto.Name = "Updated Form Name";
        _formFixture.FormDto.Type = "Updated Type";
        _formFixture.FormDto.IsPublish = true;
        _formFixture.FormDto.IsLock = true;

        // Act
        var result = await _repository.UpdateAsync(_formFixture.FormDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Form Name", result.Name);
        Assert.Equal("Updated Type", result.Type);
        Assert.True(result.IsPublish);
        Assert.True(result.IsLock);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenFormIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task ListAllAsync_ReturnsAllActiveForms_WhenFormsExist()
    {
        // Arrange
        await _dbContext.Forms.AddRangeAsync(_formFixture.FormList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion
}
