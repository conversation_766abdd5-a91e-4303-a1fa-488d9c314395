﻿using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceHealthStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessServiceHealthStatus.Queries;

public class GetBusinessServiceHealthStatusPaginatedListQueryHandlerTests : IClassFixture<BusinessServiceHealthStatusFixture>
{
    private readonly Mock<IBusinessServiceHealthStatusRepository> _businessServiceHealthStatusRepositoryMock;

    private readonly GetBusinessServiceHealthStatusPaginatedListQueryHandler _handler;

    public GetBusinessServiceHealthStatusPaginatedListQueryHandlerTests(BusinessServiceHealthStatusFixture businessServiceHealthStatusFixture)
    {
        _businessServiceHealthStatusRepositoryMock = BusinessServiceHealthStatusRepositoryMocks.GetPaginatedBusinessServiceHealthStatusRepository(businessServiceHealthStatusFixture.BusinessServiceHealthStatusList);
        
        _handler = new GetBusinessServiceHealthStatusPaginatedListQueryHandler(_businessServiceHealthStatusRepositoryMock.Object, businessServiceHealthStatusFixture.Mapper);

        businessServiceHealthStatusFixture.BusinessServiceHealthStatusList[0].ProblemState = "Testing";
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetBusinessServiceHealthStatusPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<BusinessServiceHealthStatusListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_PaginatedBusinessServiceHealthStatus_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetBusinessServiceHealthStatusPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Testing" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<BusinessServiceHealthStatusListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<BusinessServiceHealthStatusListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].ProblemState.ShouldBe("Testing");
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetBusinessServiceHealthStatusPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABC" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<BusinessServiceHealthStatusListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetBusinessServiceHealthStatusPaginatedListQuery(), CancellationToken.None);

        _businessServiceHealthStatusRepositoryMock.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}