using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class SolutionHistoryRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly SolutionHistoryRepository _repository;
    private readonly SolutionHistoryFixture _fixture;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public SolutionHistoryRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = databaseFixture.DbContext;
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        
        _repository = new SolutionHistoryRepository(_dbContext, _mockLoggedInUserService.Object);
        _fixture = new SolutionHistoryFixture();

        // Setup default mock behavior
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
    }

    #region GetSolutionHistoryByActionId Tests

    [Fact]
    public async Task GetSolutionHistoryByActionId_ShouldReturnHistoriesForAction_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var history1 = _fixture.CreateSolutionHistory(
            companyId: "COMPANY_123",
            actionId: "abea4151-6ee0-4f6b-9fc0-2fa5c961d956",
            actionName: "Test Action 1",
            version: "1.0"
        );
        var history2 = _fixture.CreateSolutionHistory(
            companyId: "COMPANY_456",
            actionId: "abea4151-6ee0-4f6b-9fc0-2fa5c961d956",
            actionName: "Test Action 2",
            version: "1.1"
        );
        var history3 = _fixture.CreateSolutionHistory(
            companyId: "COMPANY_123",
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            actionName: "Different Action",
            version: "1.0"
        );
        await _dbContext.SolutionHistories.AddRangeAsync(history1, history2, history3);
        _dbContext.SaveChanges();

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetSolutionHistoryByActionId("abea4151-6ee0-4f6b-9fc0-2fa5c961d956");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);

        
  
    }

    [Fact]
    public async Task GetSolutionHistoryByActionId_ShouldReturnHistoriesForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var history1 = _fixture.CreateSolutionHistory(
            companyId: "COMPANY_123",
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            actionName: "Test Action 1"
        );
        var history2 = _fixture.CreateSolutionHistory(
            companyId: "COMPANY_456",
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            actionName: "Test Action 2"
        );
        var history3 = _fixture.CreateSolutionHistory(
            companyId: "COMPANY_123",
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            actionName: "Test Action 3"
        );

        await _repository.AddAsync(history1);
        await _repository.AddAsync(history2);
        await _repository.AddAsync(history3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetSolutionHistoryByActionId("8f6aaef6-2b08-45d1-8915-c194439f4c27");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, h => Assert.Equal("COMPANY_123", h.CompanyId));

    }

    [Fact]
    public async Task GetSolutionHistoryByActionId_ShouldReturnEmpty_WhenActionIdNotFound()
    {
        // Arrange
        await ClearDatabase();

        var history = _fixture.CreateSolutionHistory(actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27");
        await _repository.AddAsync(history);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetSolutionHistoryByActionId("abea4151-6ee0-4f6b-9fc0-2fa5c961d956");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    //[Fact]
    //public async Task GetSolutionHistoryByActionId_ShouldThrowException_WhenActionIdIsInvalid()
    //{
    //    // Arrange
    //    await ClearDatabase();

    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentException>(() => _repository.GetSolutionHistoryByActionId("invalid-guid"));
    //}

    [Fact]
    public async Task GetSolutionHistoryByActionId_ShouldOnlyReturnActiveHistories()
    {
        // Arrange
        await ClearDatabase();

        var activeHistory = _fixture.CreateSolutionHistory(
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            companyId: "COMPANY_123",
            isActive: true
        );
        var inactiveHistory = _fixture.CreateSolutionHistory(
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            companyId: "COMPANY_123",
            isActive: false
        );

        await _dbContext.SolutionHistories.AddRangeAsync(activeHistory, inactiveHistory);
        _dbContext.SaveChanges();
      

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetSolutionHistoryByActionId("8f6aaef6-2b08-45d1-8915-c194439f4c27");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result[0].IsActive);
    }

    //[Fact]
    //public async Task GetSolutionHistoryByActionId_ShouldThrowException_WhenActionIdIsEmpty()
    //{
    //    // Arrange
    //    await ClearDatabase();

    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentException>(() => _repository.GetSolutionHistoryByActionId(""));
    //}

    //[Fact]
    //public async Task GetSolutionHistoryByActionId_ShouldThrowException_WhenActionIdIsNull()
    //{
    //    // Arrange
    //    await ClearDatabase();

    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentException>(() => _repository.GetSolutionHistoryByActionId(null));
    //}

    [Fact]
    public async Task GetSolutionHistoryByActionId_ShouldReturnOrderedResults_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var history1 = _fixture.CreateSolutionHistory(
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            companyId: "COMPANY_123",
            actionName: "First Action",
            version: "1.0"
        );
        var history2 = _fixture.CreateSolutionHistory(
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            companyId: "COMPANY_456",
            actionName: "Second Action",
            version: "2.0"
        );
        var history3 = _fixture.CreateSolutionHistory(
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            companyId: "COMPANY_789",
            actionName: "Third Action",
            version: "3.0"
        );

        await _repository.AddAsync(history1);
        await _repository.AddAsync(history2);
        await _repository.AddAsync(history3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetSolutionHistoryByActionId("8f6aaef6-2b08-45d1-8915-c194439f4c27");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        // Should be ordered by Id descending
        for (int i = 0; i < result.Count - 1; i++)
        {
            Assert.True(result[i].Id >= result[i + 1].Id);
        }
    }

    [Fact]
    public async Task GetSolutionHistoryByActionId_ShouldReturnOrderedResults_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var history1 = _fixture.CreateSolutionHistory(
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            companyId: "COMPANY_123",
            actionName: "First Action",
            version: "1.0"
        );
        var history2 = _fixture.CreateSolutionHistory(
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            companyId: "COMPANY_123",
            actionName: "Second Action",
            version: "2.0"
        );
        var history3 = _fixture.CreateSolutionHistory(
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            companyId: "COMPANY_123",
            actionName: "Third Action",
            version: "3.0"
        );

        await _repository.AddAsync(history1);
        await _repository.AddAsync(history2);
        await _repository.AddAsync(history3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetSolutionHistoryByActionId("8f6aaef6-2b08-45d1-8915-c194439f4c27");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        // Should be ordered by Id descending
        for (int i = 0; i < result.Count - 1; i++)
        {
            Assert.True(result[i].Id >= result[i + 1].Id);
        }
        // All should belong to the same company
        Assert.All(result, h => Assert.Equal("COMPANY_123", h.CompanyId));
    }

    [Fact]
    public async Task GetSolutionHistoryByActionId_ShouldReturnEmpty_WhenNoActiveHistoriesForCompany()
    {
        // Arrange
        await ClearDatabase();

        var activeHistoryDifferentCompany = _fixture.CreateSolutionHistory(
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            companyId: "COMPANY_456",
            isActive: true
        );
        var inactiveHistorySameCompany = _fixture.CreateSolutionHistory(
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            companyId: "COMPANY_123",
            isActive: false
        );
        await _dbContext.SolutionHistories.AddRangeAsync(activeHistoryDifferentCompany, inactiveHistorySameCompany);
    _dbContext.SaveChanges();

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetSolutionHistoryByActionId("8f6aaef6-2b08-45d1-8915-c194439f4c27");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetSolutionHistoryByActionId_ShouldReturnAllProperties_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var history = _fixture.CreateSolutionHistory(
            companyId: "COMPANY_TEST",
            loginName: "<EMAIL>",
            nodeId: "NODE_001",
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            actionName: "Complete Test Action",
            properties: "{\"type\": \"solution\", \"status\": \"completed\", \"details\": {\"steps\": 5}}",
            version: "2.1.0",
            updaterId: "UPDATER_001",
            description: "Complete test description",
            comments: "Complete test comments",
            isActive: true
        );

        await _repository.AddAsync(history);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetSolutionHistoryByActionId("8f6aaef6-2b08-45d1-8915-c194439f4c27");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);


    }

    [Fact]
    public async Task GetSolutionHistoryByActionId_ShouldReturnAllProperties_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var history = _fixture.CreateSolutionHistory(
            companyId: "COMPANY_123",
            loginName: "<EMAIL>",
            nodeId: "NODE_002",
            actionId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            actionName: "Non-Parent Test Action",
            properties: "{\"type\": \"solution\", \"status\": \"in_progress\"}",
            version: "1.5.2",
            updaterId: "UPDATER_002",
            description: "Non-parent test description",
            comments: "Non-parent test comments",
            isActive: true
        );

        await _repository.AddAsync(history);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetSolutionHistoryByActionId("8f6aaef6-2b08-45d1-8915-c194439f4c27");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
       

    }

    [Fact]
    public async Task GetSolutionHistoryByActionId_ShouldHandleComplexProperties()
    {
        // Arrange
        await ClearDatabase();

        var complexProperties = new Dictionary<string, object>
        {
            {"type", "solution"},
            {"status", "completed"},
            {"metadata", new Dictionary<string, object>
                {
                    {"executionTime", "00:05:30"},
                    {"resourcesUsed", new List<string> {"CPU", "Memory", "Disk"}},
                    {"performance", new Dictionary<string, object>
                        {
                            {"throughput", "1000 ops/sec"},
                            {"latency", "50ms"}
                        }
                    }
                }
            },
            {"results", new List<Dictionary<string, object>>
                {
                    new Dictionary<string, object> {{"step", 1}, {"status", "success"}},
                    new Dictionary<string, object> {{"step", 2}, {"status", "success"}},
                    new Dictionary<string, object> {{"step", 3}, {"status", "success"}}
                }
            }
        };

        var history = _fixture.CreateSolutionHistoryWithProperties(complexProperties);
        history.ActionId = "8f6aaef6-2b08-45d1-8915-c194439f4c27";

        await _repository.AddAsync(history);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetSolutionHistoryByActionId("8f6aaef6-2b08-45d1-8915-c194439f4c27");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.NotNull(result[0].Properties);
        Assert.Contains("solution", result[0].Properties);
        Assert.Contains("completed", result[0].Properties);
        Assert.Contains("metadata", result[0].Properties);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllHistories_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var history1 = _fixture.CreateSolutionHistory(companyId: "COMPANY_123", actionName: "Action 1");
        var history2 = _fixture.CreateSolutionHistory(companyId: "COMPANY_456", actionName: "Action 2");
        var history3 = _fixture.CreateSolutionHistory(companyId: "COMPANY_789", actionName: "Action 3");

        await _repository.AddAsync(history1);
        await _repository.AddAsync(history2);
        await _repository.AddAsync(history3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.Contains(result, h => h.CompanyId == "COMPANY_123");
        Assert.Contains(result, h => h.CompanyId == "COMPANY_456");
        Assert.Contains(result, h => h.CompanyId == "COMPANY_789");
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnHistoriesForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var history1 = _fixture.CreateSolutionHistory(companyId: "COMPANY_123", actionName: "Action 1");
        var history2 = _fixture.CreateSolutionHistory(companyId: "COMPANY_456", actionName: "Action 2");
        var history3 = _fixture.CreateSolutionHistory(companyId: "COMPANY_123", actionName: "Action 3");

        await _repository.AddAsync(history1);
        await _repository.AddAsync(history2);
        await _repository.AddAsync(history3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, h => Assert.Equal("COMPANY_123", h.CompanyId));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmpty_WhenNoHistoriesForCompany()
    {
        // Arrange
        await ClearDatabase();

        var history = _fixture.CreateSolutionHistory(companyId: "COMPANY_456");
        await _repository.AddAsync(history);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnHistory_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var history = _fixture.CreateSolutionHistory(
            companyId: "COMPANY_456",
            actionName: "Test Action",
            version: "1.0",
            description: "Test Description"
        );
        await _repository.AddAsync(history);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetByReferenceIdAsync(history.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(history.ReferenceId, result.ReferenceId);
        Assert.Equal("COMPANY_456", result.CompanyId);
        Assert.Equal("Test Action", result.ActionName);
        Assert.Equal("1.0", result.Version);
        Assert.Equal("Test Description", result.Description);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnHistory_WhenUserIsNotParentAndSameCompany()
    {
        // Arrange
        await ClearDatabase();

        var history = _fixture.CreateSolutionHistory(
            companyId: "COMPANY_123",
            actionName: "Test Action"
        );
        await _repository.AddAsync(history);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetByReferenceIdAsync(history.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(history.ReferenceId, result.ReferenceId);
        Assert.Equal("COMPANY_123", result.CompanyId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenUserIsNotParentAndDifferentCompany()
    {
        // Arrange
        await ClearDatabase();

        var history = _fixture.CreateSolutionHistory(companyId: "COMPANY_456");
        await _repository.AddAsync(history);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetByReferenceIdAsync(history.ReferenceId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.SolutionHistories.RemoveRange(_dbContext.SolutionHistories);
        await _dbContext.SaveChangesAsync();
    }
}
