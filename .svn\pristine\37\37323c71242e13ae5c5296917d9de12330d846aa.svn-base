﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@using ContinuityPatrol.Domain.ViewModels.ArchiveModel;
@using ContinuityPatrol.Shared.Services.Helper
@model ContinuityPatrol.Domain.ViewModels.ArchiveModel.ArchiveViewModel
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title">
                <i class="cp-archive"></i>
                <span>Archive </span>
            </h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown" title="Filter">
                            <span data-bs-toggle="dropdown"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="archiveProfileName=" id="ProfileName">
                                        <label class="form-check-label" for="ProfileName">
                                            Profile Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="tablename=" id="TableName">
                                        <label class="form-check-label" for="TableName">
                                            Table Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="type=" id="Type">
                                        <label class="form-check-label" for="Type">
                                            Type
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="backup=" id="BackupType">
                                        <label class="form-check-label" for="backup">
                                            Count/BackupType
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal" id='btnCreate'><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body  pt-0">
            <table id="archiveTable" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Profile Name</th>
                        <th>Table Name</th>
                        <th>Type</th>
                        <th>Schedule Time</th>
                        <th>Table Count/Backup Type</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>
<div class="modal fade" id="CreateModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title">
                    <i class="cp-archive"></i><span>
                        Archive Configuration
                    </span>
                </h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="ArchiveForm" asp-controller="Archive" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data" class="tab-wizard wizard-circle wizard clearfix example-form">

                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="form-label">Archive Profile Name</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-name"></i></span>
                                    <input asp-for="ArchiveProfileName" type="text" maxlength="100" id="archiveName" class="form-control" placeholder="Enter Archive Profile Name" autocomplete="off" />
                                </div>
                                <span asp-validation-for="ArchiveProfileName" id="profileName-error"></span>
                            </div>
                            <div class="form-group">
                                <div class="form-label">Table Name</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-table"></i></span>
                                    <select class="form-select-modal"  id="archiveTableName" data-placeholder="Select Table Name" multiple>
                                    </select>
                                </div>
                                <input asp-for="TableNameProperties" id="TableNameProperties" type="hidden" class="form-control" />
                                <span asp-validation-for="TableNameProperties" id="tableName-error"></span>
                                <input asp-for="CronExpression" id="archiveCronExpression" type="hidden" class="form-control" />
                                <input asp-for="CompanyId" id="CompanyId" type="hidden" value="@WebHelper.UserSession.CompanyId" />
                                <input asp-for="Id" id="ArchiveId" type="hidden" class="form-control" />
                                <input asp-for="ScheduleType" id="textScheduleType" type="hidden" class="form-control" />
                                <input asp-for="ScheduleTime" id="archiveCronViewList" type="hidden" class="form-control" />
                                <input asp-for="BackUpType" id="ChkValue" type="hidden" class="form-control" />
                                <input asp-for="Type" id="radioVal" type="hidden" class="form-control" />
                                <input asp-for="ClearBackup" id="ClearBackupId" type="hidden" class="form-control" />
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row mt-2 w-100 year">
                                <div>
                                    <div class="mb-3">
                                        <div class="form-label">Scheduler</div>
                                        <div>
                                            <nav>
                                                <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                                    <button class="nav-link active" id="nav-Minutes-tab" data-bs-toggle="tab" name="Scheduler"
                                                            data-bs-target="#nav-Minutes" type="button" role="tab"
                                                            aria-controls="nav-Minutes" aria-selected="true">
                                                        Minutes
                                                    </button>
                                                    <button class="nav-link" id="nav-Hourly-tab" data-bs-toggle="tab" name="Scheduler"
                                                            data-bs-target="#nav-Hourly" type="button" role="tab"
                                                            aria-controls="nav-Hourly" aria-selected="false">
                                                        Hourly
                                                    </button>
                                                    <button class="nav-link" id="nav-Daily-tab" data-bs-toggle="tab" name="Scheduler"
                                                            data-bs-target="#nav-Daily" type="button" role="tab"
                                                            aria-controls="nav-Daily" aria-selected="false">
                                                        Daily
                                                    </button>
                                                    <button class="nav-link" id="nav-Weekly-tab" data-bs-toggle="tab" name="Scheduler"
                                                            data-bs-target="#nav-Weekly" type="button" role="tab"
                                                            aria-controls="nav-Weekly" aria-selected="false">
                                                        Weekly
                                                    </button>
                                                    <button class="nav-link" id="nav-Monthly-tab" data-bs-toggle="tab" name="Scheduler"
                                                            data-bs-target="#nav-Monthly" type="button" role="tab"
                                                            aria-controls="nav-Monthly" aria-selected="false">
                                                        Monthly
                                                    </button>
                                                </div>
                                            </nav>
                                            <div class="tab-content" id="nav-tabContent">
                                               @*  <div class="tab-pane fade active show" id="nav-Minutes" role="tabpanel" aria-labelledby="nav-home-tab" tabindex="0">
                                                    <div class="row">
                                                        <div class="col-6">
                                                            <div class="form-group">
                                                                <div class="form-label">Every</div>
                                                                <div class="form-label">Minutes</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text" id="EveryMint">
                                                                        <i class="cp-apply-finish-time"></i>
                                                                    </span>
                                                                    @Html.TextBox("txtMins", null, new { id = "txtMins", type = "number", min = "1", max = "60", @class = "form-control", @placeholder = "Minute(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                    <span class="input-group-text form-label mb-0 text-secondary">
                                                                        mins
                                                                    </span>
                                                                </div>
                                                                <span asp-validation-for="ScheduleTime" id="CronMin-error"></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div> *@
                                                <div class="tab-pane fade show active" id="nav-Minutes" role="tabpanel" aria-labelledby="nav-home-tab" tabindex="0">
                                                    <div class="row mt-2 align-items-end">
                                                        <div class="col-3">
                                                            <div class="form-group">
                                                                <div class="form-label">Minites</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text pe-0"><i class="cp-calendar"></i></span>
                                                                    @Html.TextBox("txtMins", null, new { id = "txtMins", type = "number", maxlength = "2", min = "0", max = "59", pattern = "d{2}", @class = "form-control", @placeholder = "Enter Mins", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                    <span class="input-group-text small text-secondary">mins</span>
                                                                </div>
                                                                <span id="CronMin-error"></span>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                                <div class="tab-pane fade" id="nav-Hourly" role="tabpanel" aria-labelledby="nav-Hourly-tab" tabindex="0">
                                                    <div class="row mt-2">
                                                        <div class="col-4">
                                                            <div class="form-group">
                                                                <div class="form-label">Hours</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text">
                                                                        <i class="cp-calendar"></i>
                                                                    </span>
                                                                    @Html.TextBox("txtHours", null, new { id = "txtHours", type = "number", min = "0", max = "23", @class = "form-control", @placeholder = "Enter Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                    <span class="input-group-text fs-8 ms-1 text-secondary">
                                                                        hrs
                                                                    </span>
                                                                </div>
                                                                <span id="CronHourly-error"></span>
                                                            </div>
                                                        </div>
                                                        <div class="col-xl-6">
                                                            <div class="form-group">
                                                                <div class="form-label">Minutes</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text">
                                                                        <i class="cp-calendar"></i>
                                                                    </span>
                                                                    @Html.TextBox("txtMinutes", null, new { id = "txtMinutes", type = "number", min = "0", max = "59", @class = "form-control", @placeholder = "Enter Min(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                    <span class="input-group-text form-label mb-0 text-secondary">mins</span>
                                                                </div>
                                                                <span id="CronHourMin-error"></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tab-pane fade" id="nav-Daily" role="tabpanel" aria-labelledby="nav-Daily-tab" tabindex="0">
                                                    <div class="row mt-2 align-items-center">
                                                        <div class="col-4">
                                                            <div class="form-group flex-fill ">
                                                                <label class="animation-label form-label">
                                                                    Select Day Type
                                                                </label>
                                                                <div class="">
                                                                    <span class="input-group-text"></span>
                                                                    <div class="form-check form-check-inline">
                                                                        <input name="daysevery" aria-label="Every Day" type="radio" id="defaultCheck-everyday" class="form-check-input custom-cursor-default-hover" value="everyday" cursorshover="true">
                                                                        <label for="defaultCheck-everyday" class="form-check-label custom-cursor-default-hover" cursorshover="true">Every Day</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline">
                                                                        <input name="daysevery" aria-label="Every Week Day" type="radio" id="defaultCheck-MON-FRI" class="form-check-input custom-cursor-default-hover" value="MON-FRI">
                                                                        <label for="defaultCheck-MON-FRI" class="form-check-label custom-cursor-default-hover" cursorshover="true">Every Week Day</label>
                                                                    </div>
                                                                </div>
                                                                <span id="Crondaysevery-error"></span>
                                                            </div>
                                                        </div>
                                                        <div class="col-4">
                                                            <div class="form-group">
                                                                <div class="form-label">Starts at</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text"><i class="cp-calendar"></i></span>
                                                                    @Html.TextBox("everyHours", null, new { id = "everyHours", type = "time", min = "0", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                </div>
                                                                <span id="CroneveryHour-error"></span>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                                <div class="tab-pane fade" id="nav-Weekly" role="tabpanel" aria-labelledby="nav-Weekly-tab" tabindex="0">
                                                <div class="row row-cols-2 mt-2">
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <label class="form-label custom-cursor-default-hover">Select Day</label>
                                                            <div class="bg-transparent input-group">
                                                                <div class="form-check form-check-inline">
                                                                    <input name="weekDays" aria-label="Monday" type="checkbox" id="defaultCheck-1" class="form-check-input" value="MON"><label for="defaultCheck-1" class="form-check-label custom-cursor-default-hover">Monday</label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input name="weekDays" aria-label="Tuesday" type="checkbox" id="defaultCheck-2" class="form-check-input" value="TUE"><label for="defaultCheck-2" class="form-check-label">Tuesday</label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input name="weekDays" aria-label="Wednesday" type="checkbox" id="defaultCheck-3" class="form-check-input" value="WED"><label for="defaultCheck-3" class="form-check-label" cursorshover="true">Wednesday</label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input name="weekDays" aria-label="Thursday" type="checkbox" id="defaultCheck-4" class="form-check-input" value="THU"><label for="defaultCheck-4" class="form-check-label custom-cursor-default-hover" cursorshover="true">Thursday</label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input name="weekDays" aria-label="Friday" type="checkbox" id="defaultCheck-5" class="form-check-input" value="FRI" cursorshover="true"><label for="defaultCheck-5" class="form-check-label">Friday</label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input name="weekDays" aria-label="Saturday" type="checkbox" id="defaultCheck-6" class="form-check-input" value="SAT"><labelfor ="defaultCheck-6" class="form-check-label">Saturday</label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input name="weekDays" aria-label="Sunday" type="checkbox" id="defaultCheck-0" class="form-check-input" value="SUN"><label for="defaultCheck-0" class="form-check-label">Sunday</label>
                                                                </div>
                                                            </div>
                                                            <span id="CronDay-error"></span>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="form-group">
                                                            <div class="form-label">Starts at</div>
                                                            <div class="input-group">
                                                                <span class="input-group-text"><i class="cp-calendar"></i></span>
                                                                @Html.TextBox("ddlHours", null, new { id = "ddlHours", type = "time", min = "0", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                            </div>
                                                            <span id="CronddlHour-error"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                                <div class="tab-pane fade" id="nav-Monthly" role="tabpanel" aria-labelledby="nav-Monthly-tab" tabindex="0">
                                                    <div class="row row-cols-2 mt-2">
                                                        <div class="col-4">
                                                            <div class="mb-3 form-group">
                                                                <div class="form-label">Select Month And Year</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text">
                                                                        <i class="cp-calendar"></i>
                                                                    </span>
                                                                    <input name="month" autocomplete="off" type="month"
                                                                           id="lblMonth"
                                                                           class="form-control custom-cursor-default-hover"
                                                                           cursorshover="true" />
                                                                </div>
                                                                <span id="CronMonthly-error"></span>
                                                            </div>
                                                        </div>
                                                        <div class="col-12">
                                                            <div class="mb-3 form-group text-justify " style="display: inline-table;">
                                                                <div class="form-label mb-2">Select Date</div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox1" value="1">
                                                                    <label class="form-check-label checklabel">1</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox2" value="2">
                                                                    <label class="form-check-label checklabel">2</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox3" value="3">
                                                                    <label class="form-check-label checklabel">3</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox4" value="4">
                                                                    <label class="form-check-label checklabel">4</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox5" value="5">
                                                                    <label class="form-check-label checklabel">5</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox6" value="6">
                                                                    <label class="form-check-label checklabel">6</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox7" value="7">
                                                                    <label class="form-check-label checklabel">7</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox8" value="8">
                                                                    <label class="form-check-label checklabel">8</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox9" value="9">
                                                                    <label class="form-check-label checklabel">9</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox10" value="10">
                                                                    <label class="form-check-label checklabel">10</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox11" value="11">
                                                                    <label class="form-check-label checklabel">11</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox12" value="12">
                                                                    <label class="form-check-label checklabel">12</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox13" value="13">
                                                                    <label class="form-check-label checklabel">13</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox14" value="14">
                                                                    <label class="form-check-label checklabel">14</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox15" value="15">
                                                                    <label class="form-check-label checklabel">15</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox16" value="16">
                                                                    <label class="form-check-label checklabel">16</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox17" value="17">
                                                                    <label class="form-check-label checklabel">17</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox18" value="18">
                                                                    <label class="form-check-label checklabel">18</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox19" value="19">
                                                                    <label class="form-check-label checklabel">19</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox20" value="20">
                                                                    <label class="form-check-label checklabel">20</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox21" value="21">
                                                                    <label class="form-check-label checklabel">21</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox22" value="22">
                                                                    <label class="form-check-label checklabel">22</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox23" value="23">
                                                                    <label class="form-check-label checklabel">23</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox24" value="24">
                                                                    <label class="form-check-label checklabel">24</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox25" value="25">
                                                                    <label class="form-check-label checklabel">25</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox26" value="26">
                                                                    <label class="form-check-label checklabel">26</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox27" value="27">
                                                                    <label class="form-check-label checklabel">27</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox28" value="28">
                                                                    <label class="form-check-label checklabel">28</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox29" value="29">
                                                                    <label class="form-check-label checklabel">29</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox30" value="30">
                                                                    <label class="form-check-label checklabel">30</label>
                                                                </div>
                                                                <div class="form-check form-check-inline" style="width: 30px;">
                                                                    <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox31" value="31">
                                                                    <label class="form-check-label checklabel">31</label>
                                                                </div>
                                                                <div class="form-group">
                                                                    <span id="CronMon-error"></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-4">
                                                            <div class="form-group">
                                                                <div class="form-label">Starts at</div>
                                                                <div class="input-group">
                                                                    <span class="input-group-text">
                                                                        <i class="cp-calendar"></i>
                                                                    </span>
                                                                    @Html.TextBox("MonthlyHours", null, new { id = "MonthlyHours", type = "time", min = "0", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                </div>
                                                                <span id="MonthlyHours-error"></span>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group mb-1" id="Radio">
                                    <div class="form-check form-check-inline" id="user_list">
                                        <input class="form-check-input" type="radio" name="inlineRadioOptions" id="Countclick" checked value="Count">
                                        <label class="form-check-label" for="Count">Count</label>
                                    </div>
                                    <div class="form-check form-check-inline" id="user_role">
                                        <input class="form-check-input" type="radio" name="inlineRadioOptions" id="Periodclick" value="Period">
                                        <label class="form-check-label" for="Period">Period</label>
                                    </div>
                                    <span id="permission-radio-error"></span>
                                </div>

                                <div class="form-group" id="countClm">
                                    <div class="form-label">Backup Data Count</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-backup_data"></i></span>
                                        <input asp-for="Count" class="form-control" maxlength="6" type="text" placeholder="Enter Backup Data Count" id="archiveBackupDataCount" />
                                    </div>
                                    <span asp-validation-for="Count" id="TableMax-error"></span>
                                </div>
                                <div class="form-group " id="BackupTablesClm">
                                    <div class="form-label" title="Select Period">Select Period</div>
                                    <div class="mt-2">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="oneweek" id="oneweek" value="One Week" checked>
                                            <label class="form-check-label" for="oneweek">One Week</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="oneweek" id="onemonth" value="One Month">
                                            <label class="form-check-label" for="onemonth">One Month</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="oneweek" id="threemonth" value="Three Month">
                                            <label class="form-check-label" for="threemonth">Three Month</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="oneweek" id="sixmonth" value="Six Month">
                                            <label class="form-check-label" for="sixmonth">Six Month</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="oneweek" id="oneyear" value="One Year">
                                            <label class="form-check-label" for="oneyear">One Year</label>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="form-group " id="BackupTablesClm">
                                        <div class="form-label">Clear Data From Backup Tables</div>
                                        <div class="mt-2">
                                            <div class="form-check form-check-inline" id="week">
                                                <input class="form-check-input" type="radio" name="options" id="option1" value="One Week" checked>
                                                <label class="form-check-label" for="option1">One Week</label>
                                            </div>
                                            <div class="form-check form-check-inline" id="month">
                                                <input class="form-check-input" type="radio" name="options" id="option2" value="One Month">
                                                <label class="form-check-label" for="option2">One Month</label>
                                            </div>
                                            <div class="form-check form-check-inline" id="month2">
                                                <input class="form-check-input" type="radio" name="options" id="option3" value="Three Month">
                                                <label class="form-check-label" for="option3">Three Month</label>
                                            </div>
                                            <div class="form-check form-check-inline" id="month6">
                                                <input class="form-check-input" type="radio" name="options" id="option4" value="Six Month">
                                                <label class="form-check-label" for="option4">Six Month</label>
                                            </div>
                                            <div class="form-check form-check-inline" id="Year">
                                                <input class="form-check-input" type="radio" name="options" id="option5" value="One Year">
                                                <label class="form-check-label" for="option5">One Year</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnArchiveCancel" class="btn btn-secondary me-2" data-bs-dismiss="modal" role="button">
                    <span class="btn-text">Cancel</span>
                </button>
                <button type="button" id="btnArchiveSave" class="btn btn-primary" role="button">
                    <span class="btn-text">Save</span>
                </button>
            </div>
        </div>
    </div>
</div>
<div id="AdminArCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.CreateAndEdit" aria-hidden="true"></div>
<div id="AdminArDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.Delete" aria-hidden="true"></div>
<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>
<!--View Modal Delete-->
<div class="modal fade" id="ViewModal" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title" title="Archive Database Details - oracle_monitor_status">
                    <i class="cp-archive"></i><span>
                        Archive Database Details - oracle_monitor_status
                    </span>
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Sr.No</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Records</th>
                            <th>Result</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>2022-07-09 6:27:38 PM</td>
                            <td>2022-07-09 6:27:38 PM</td>
                            <td>200</td>
                            <td><span class="badge text-bg-success fw-normal">Successful</span></td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>2022-07-09 6:27:38 PM</td>
                            <td>2022-07-09 6:27:38 PM</td>
                            <td>150</td>
                            <td><span class="badge text-bg-danger fw-normal">Failure</span></td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>2022-07-09 6:27:38 PM</td>
                            <td>2022-07-09 6:27:38 PM</td>
                            <td>100</td>
                            <td><span class="badge text-bg-success fw-normal">Successful</span></td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td>2022-07-09 6:27:38 PM</td>
                            <td>2022-07-09 6:27:38 PM</td>
                            <td>300</td>
                            <td><span class="badge text-bg-danger fw-normal">Failure</span></td>
                        </tr>
                        <tr>
                            <td>5</td>
                            <td>2022-07-09 6:27:38 PM</td>
                            <td>2022-07-09 6:27:38 PM</td>
                            <td>250</td>
                            <td><span class="badge text-bg-success fw-normal">Successful</span></td>
                        </tr>
                        <tr>
                            <td>6</td>
                            <td>2022-07-09 6:27:38 PM</td>
                            <td>2022-07-09 6:27:38 PM</td>
                            <td>450</td>
                            <td><span class="badge text-bg-danger fw-normal">Failure</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<script>
    $('select').on('select2:closing', () => {
        $('.select2-selection').width('auto');
        var $choice = $('.select2-selection__choice');
        $choice.first().show();
        $choice.slice(6).hide();
        $choice.eq(6).after(`<li class='select2-selection__choice select2-selection__choice_more'>...</li>`);
    });
</script>
<script src="~/js/common/commoncronexpressionjs.js"></script>
<script src="~/js/Admin/Archive/Archive.js"></script>