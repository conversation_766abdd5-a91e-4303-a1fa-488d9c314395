﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/dashboard.css" rel="stylesheet" />
@Html.AntiForgeryToken()

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">

    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title">
            <i class="cp-monitoring"></i>
            <span>
                MSSQL DB Mirror Detail Monitoring :
                <span id="infraName"></span>
            </span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time :"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>

    </div>
    <div class="monitor_pages">
        <div class="row mb-2 g-2 mt-0" id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row g-2  mt-0">
            <div class="col-7 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                        Database Component Monitoring
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th title="Component">Component</th>
                                    <th title="Primary" class="text-primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-ip-address me-1"></i>Server Name</td>
                                    <td class="text-truncate"><span id="PRServerName"></span></td>
                                    <td class="text-truncate"><span id="DR_ServerName"></span></td>

                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-ip-address me-1"></i>IP Address/Hostname</td>
                                    <td class="text-truncate"><span id="PR_Server_IpAddress"></span></td>
                                    <td class="text-truncate"><span id="DR_Server_IpAddress"></span></td>

                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-mysql-data me-1"></i>Database Name</td>
                                    <td class="text-truncate"><span id="PR_Database"></span></td>
                                    <td class="text-truncate"><span id="DR_Database"></span></td>
                                </tr>

                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-mysql-data me-1"></i>Server Network</td>
                                    <td class="text-truncate"><span id="PR_Server_NetworkAddress"></span></td>
                                    <td class="text-truncate"><span id="DR_Server_NetworkAddress"></span></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-mysql-data me-1"></i>Operation Mode</td>
                                    <td class="text-truncate"><span id="PROpreationMode"></span></td>
                                    <td class="text-truncate"><span id="DR_OpreationMode"></span></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-mysql-data me-1"></i>Role Of DB</td>
                                    <td class="text-truncate"><span id="PRDBRole"></span></td>
                                    <td class="text-truncate"><span id="DR_DBRole"></span></td>
                                </tr>

                            </tbody>

                        </table>
                    </div>
                </div>
                <div class="card Card_Design_None">
                    <div class="card-header card-title">Replication Level Monitoring</div>
                    <div class="card-body pt-0 p-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th title="Component">Component</th>
                                    <th title="Primary" class="text-primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                   <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-replication-rotate me-1"></i>Replication Type</td>
                                    <td class="text-truncate"><span id="replica"></span></td>
                                    <td class="text-truncate"><span id="DR_replica"></span></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-control-file-name me-1"></i>Monitoring State</td>
                                    <td class="text-truncate"><span id="PRMirroringState"></span></td>
                                    <td class="text-truncate"><span id="DR_MirroringState"></span></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-file-edits me-1 fs-6"></i>Log Generate Rate(Kbytes/Sec)</td>
                                    <td class="text-truncate"><span id="PRLogGenerateRate"></span></td>
                                    <td class="text-truncate"><span id="DR_LogGenerateRate"></span></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-apply-finish-time me-1"></i>UnSent Log(Kbytes)</td>
                                    <td class="text-truncate"><span id="PRUnsentLog"></span></td>
                                    <td class="text-truncate"><span id="DR_UnsentLog"></span></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-file-edits me-1 fs-6"></i>Log Sent Rate(Kb/Sec)</td>
                                    <td class="text-truncate"><span id="PRSentRate"></span></td>
                                    <td class="text-truncate"><span id="DR_SentRate"></span></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-file-edits me-1 fs-6"></i>Unrestored Queue Log Value(Kbytes)</td>
                                    <td class="text-truncate"><span id="PRUnrestoredLog"></span></td>
                                    <td class="text-truncate"><span id="DR_UnrestoredLog"></span></td>

                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-file-edits me-1 fs-6"></i>Log Recovery Rate(Kbytes/Sec)</td>
                                    <td class="text-truncate"><span id="PRRecoveryRate"></span></td>
                                    <td class="text-truncate"><span id="DR_RecoveryRate"></span></td>

                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-file-edits me-1 fs-6"></i>Transaction Delay(millisec)</td>
                                    <td class="text-truncate"><span id="PRTransactionDelay"></span></td>
                                    <td class="text-truncate"><span id="DR_TransactionDelay"></span></td>

                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-file-edits me-1 fs-6"></i>Transaction Per Second(Trans/Sec)</td>
                                    <td class="text-truncate"><span id="PRTransactionPerSecond"></span></td>
                                    <td class="text-truncate"><span id="DR_TransactionPerSecond"></span></td>

                                </tr>

                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-data-lag me-1"></i>Datalag</td>
                                    <td class="text-truncate"> <span id="DR_PR_Datalag"></span></td>
                                    @* <td class="text-truncate"><span  id="PR_Datalag"></span></td> *@
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-5 d-grid">
                <div class="card Card_Design_None">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center">
                        <div id="Solution_Diagram" class="w-100 h-100"></div>
                    </div>
                </div>
                <div class="card Card_Design_None">
                    <div class="card-header card-title" style="font-size:15px" title="Database Size">Database Size</div>
                    <div class="card-body d-flex pt-0 align-items-center gap-4 justify-content-center">
                        <div>
                            <i class="cp-database-sizes text-light" style="font-size: 5.9rem;"></i>
                        </div>
                        <div class="d-grid  border-start border-3">
                            <div class="text-primary ms-2 fw-semibold" title="Primary">Primary</div>
                            <span class="text-secondary mb-1 ms-2" title="Database Size">Database Size</span>
                            <h6 class="mb-0 fw-bold ms-2" id="PR_Dbsize"></h6>
                        </div>
                        @*<div class="w-50" id="DatabaseSize"></div>*@
                        <div>
                            <div class="d-grid">
                                <div class="ms-2 fw-semibold dynamicSite-header" title="DR">DR</div>
                                <span class="text-secondary mb-1 ms-2" title="Database Size">Database Size</span>
                                <h6 class="mb-0 fw-bold ms-2" id="DR_Dbsize"></h6>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="col-xl-6 d-grid">
                <div class="card Card_Design_None mb-0" id="mssqlserver">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span title=" Services ">
                            Services
                        </span>

                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" id="tableCluster" style="table-layout:fixed">
                            <thead class="align-middle">
                                <tr>
                                    <th rowspan="2">Service / Process / Workflow Name</th>
                                    <th colspan="2" class="text-center">Server IP/HostName</th>
                                </tr>
                                <tr>
                                    <th id="prIp"></th>
                                    <th id="drIp"></th>
                                </tr>
                            </thead>
                            <tbody id="mssqlserverbody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>


<script src="~/js/monitoring/monitoringsqldbmirroring.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>
@* <script src="~/js/monitoring/solutiondiagramLinux.js"></script>
<script src="~/js/monitoring/solutiondigrammongodb.js"></script> *@
