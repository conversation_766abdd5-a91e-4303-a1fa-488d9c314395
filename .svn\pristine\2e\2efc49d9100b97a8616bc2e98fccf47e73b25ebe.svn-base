using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Approval;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Delete;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class ApprovalMatrixApprovalFixture : IDisposable
{
    public List<ApprovalMatrixApproval> ApprovalMatrixApprovals { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public List<ApprovalMatrix> ApprovalMatrices { get; set; }
    public List<Workflow> Workflows { get; set; }
    public List<WorkflowTemp> WorkflowTemps { get; set; }
    public CreateApprovalMatrixApprovalCommand CreateApprovalMatrixApprovalCommand { get; set; }
    public UpdateApprovalMatrixApprovalCommand UpdateApprovalMatrixApprovalCommand { get; set; }
    public DeleteApprovalMatrixApprovalCommand DeleteApprovalMatrixApprovalCommand { get; set; }
    public ApprovalMatrixApprovalCommand ApprovalMatrixApprovalCommand { get; set; }
    public IMapper Mapper { get; set; }

    public ApprovalMatrixApprovalFixture()
    {
        // Initialize with manual data first
        ApprovalMatrixApprovals = new List<ApprovalMatrixApproval>
        {
            new ApprovalMatrixApproval
            {
                ReferenceId = Guid.NewGuid().ToString(),
                RequestId = Guid.NewGuid().ToString(),
                ApprovalMatrixId = Guid.NewGuid().ToString(),
                ProcessName = "TestProcess1",
                Description = "Test approval description",
                UserId = Guid.NewGuid().ToString(),
                UserName = "TestUser1",
                Status = "Pending",
                Message = "Approval request submitted",
                IsApproval = true,
                ApproverId = Guid.NewGuid().ToString(),
                ApproverName = "TestApprover1",
                StartDateTime = DateTime.Now,
                EndDateTime = DateTime.Now.AddDays(7),
                IsActive = true
            }
        };

        // Create additional entities using AutoFixture
        try
        {
            var additionalApprovals = AutoApprovalMatrixApprovalFixture.CreateMany<ApprovalMatrixApproval>(2).ToList();
            ApprovalMatrixApprovals.AddRange(additionalApprovals);

            UserActivities = AutoApprovalMatrixApprovalFixture.CreateMany<UserActivity>(3).ToList();
            ApprovalMatrices = AutoApprovalMatrixApprovalFixture.CreateMany<ApprovalMatrix>(2).ToList();
            Workflows = AutoApprovalMatrixApprovalFixture.CreateMany<Workflow>(2).ToList();
            WorkflowTemps = AutoApprovalMatrixApprovalFixture.CreateMany<WorkflowTemp>(2).ToList();

            CreateApprovalMatrixApprovalCommand = AutoApprovalMatrixApprovalFixture.Create<CreateApprovalMatrixApprovalCommand>();
            UpdateApprovalMatrixApprovalCommand = AutoApprovalMatrixApprovalFixture.Create<UpdateApprovalMatrixApprovalCommand>();
            DeleteApprovalMatrixApprovalCommand = AutoApprovalMatrixApprovalFixture.Create<DeleteApprovalMatrixApprovalCommand>();
            ApprovalMatrixApprovalCommand = AutoApprovalMatrixApprovalFixture.Create<ApprovalMatrixApprovalCommand>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            UserActivities = new List<UserActivity>();
            ApprovalMatrices = new List<ApprovalMatrix>();
            Workflows = new List<Workflow>();
            WorkflowTemps = new List<WorkflowTemp>();

            CreateApprovalMatrixApprovalCommand = new CreateApprovalMatrixApprovalCommand();
            UpdateApprovalMatrixApprovalCommand = new UpdateApprovalMatrixApprovalCommand();
            DeleteApprovalMatrixApprovalCommand = new DeleteApprovalMatrixApprovalCommand();
            ApprovalMatrixApprovalCommand = new ApprovalMatrixApprovalCommand();
        }

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ApprovalMatrixApprovalProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoApprovalMatrixApprovalFixture
    {
        get
        {
            var fixture = new Fixture();

            // Configure fixture to handle circular references
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
                .ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            // String customizations
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateApprovalMatrixApprovalCommand>(p => p.ProcessName, 100));
            fixture.Customize<CreateApprovalMatrixApprovalCommand>(c => c
                .With(a => a.ProcessName, () => $"TestProcess{fixture.Create<int>()}")
                .With(a => a.Description, () => $"Test approval description {fixture.Create<int>()}")
                .With(a => a.UserName, () => $"TestUser{fixture.Create<int>()}")
                .With(a => a.Status, "Pending")
                .With(a => a.Approver, () => $"TestApprover{fixture.Create<int>()}")
                .With(a => a.StartDateTime, DateTime.Now)
                .With(a => a.EndDateTime, DateTime.Now.AddDays(7)));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateApprovalMatrixApprovalCommand>(p => p.ProcessName, 100));
            fixture.Customize<UpdateApprovalMatrixApprovalCommand>(c => c
                .With(a => a.Id, () => Guid.NewGuid().ToString())
                .With(a => a.ProcessName, () => $"UpdatedProcess{fixture.Create<int>()}")
                .With(a => a.Description, () => $"Updated approval description {fixture.Create<int>()}")
                .With(a => a.UserName, () => $"UpdatedUser{fixture.Create<int>()}")
                .With(a => a.Status, "InProgress")
                .With(a => a.Approver, () => $"UpdatedApprover{fixture.Create<int>()}")
                .With(a => a.StartDateTime, DateTime.Now)
                .With(a => a.EndDateTime, DateTime.Now.AddDays(14)));

            fixture.Customize<DeleteApprovalMatrixApprovalCommand>(c => c
                .With(a => a.Id, () => Guid.NewGuid().ToString()));

            fixture.Customize<ApprovalMatrixApprovalCommand>(c => c
                .With(a => a.Id, () => Guid.NewGuid().ToString())
                .With(a => a.ProcessName, () => $"ApprovalProcess{fixture.Create<int>()}")
                .With(a => a.Status, "Approved"));

            //fixture.Customize<ApprovalMatrixApproval>(c => c
            //    .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
            //    .With(a => a.RequestId, () => Guid.NewGuid().ToString())
            //    .With(a => a.ApprovalMatrixId, () => Guid.NewGuid().ToString())
            //    .With(a => a.IsActive, true)
            //    .With(a => a.ProcessName, () => $"TestProcess{fixture.Create<int>()}")
            //    .With(a => a.Description, () => $"Test approval description {fixture.Create<int>()}")
            //    .With(a => a.UserId, () => Guid.NewGuid().ToString())
            //    .With(a => a.UserName, () => $"TestUser{fixture.Create<int>()}")
            //    .With(a => a.Status, "Pending")
            //    .With(a => a.Message, () => $"Approval request submitted {fixture.Create<int>()}")
            //    .With(a => a.IsApproval, true)
            //    .With(a => a.ApproverId, () => Guid.NewGuid().ToString())
            //    .With(a => a.ApproverName, () => $"TestApprover{fixture.Create<int>()}")
            //    .With(a => a.StartDateTime, DateTime.Now)
            //    .With(a => a.EndDateTime, DateTime.Now.AddDays(7))
            //    .Without(a => a.Comments)); // Exclude potentially problematic properties

            fixture.Customize<ApprovalMatrix>(c => c
                .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
                .With(a => a.Name, () => $"TestMatrix{fixture.Create<int>()}")
                .With(a => a.BusinessServiceId, () => Guid.NewGuid().ToString())
                .With(a => a.BusinessServiceName, () => $"TestService{fixture.Create<int>()}")
                .With(a => a.BusinessFunctionId, () => Guid.NewGuid().ToString())
                .With(a => a.BusinessFunctionName, () => $"TestFunction{fixture.Create<int>()}")
                .With(a => a.Description, () => $"Test matrix description {fixture.Create<int>()}")
                .With(a => a.Properties, "{\"approvalLevel\":\"Manager\",\"threshold\":10000}")
                .With(a => a.IsActive, true));

            //fixture.Customize<Workflow>(c => c
            //    .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
            //    .With(a => a.RequestId, () => Guid.NewGuid().ToString())
            //    .With(a => a.IsActive, true));

            fixture.Customize<WorkflowTemp>(c => c
                .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
                .With(a => a.RequestId, () => Guid.NewGuid().ToString())
                .With(a => a.IsActive, true));

            // Add UserActivity customization
            fixture.Customize<UserActivity>(c => c
                .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
                .With(a => a.UserId, () => Guid.NewGuid().ToString())
                .With(a => a.LoginName, () => $"TestUser{fixture.Create<int>()}")
                .With(a => a.Entity, "ApprovalMatrixApproval")
                .With(a => a.Action, "Create")
                .With(a => a.ActivityType, "Create")
                .With(a => a.ActivityDetails, () => $"Test activity {fixture.Create<int>()}")
                .With(a => a.RequestUrl, "/api/test")
                .With(a => a.HostAddress, "127.0.0.1")
                .With(a => a.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}
