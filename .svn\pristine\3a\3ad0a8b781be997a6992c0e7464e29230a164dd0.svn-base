﻿using ContinuityPatrol.Application.Features.Server.Events.Create;
using ContinuityPatrol.Application.Features.Server.Events.LicenseInfoEvents.Create;
using ContinuityPatrol.Application.Features.Server.Events.SaveAll;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using System.ComponentModel;

namespace ContinuityPatrol.Application.Features.Server.Commands.SaveAll;

public class SaveAllServerCommandHandler : IRequestHandler<SaveAllServerCommand, SaveAllServerResponse>
{
    private readonly IServerRepository _serverRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ISiteRepository _siteRepository;
    private readonly ISiteTypeRepository _siteTypeRepository;
    private readonly ILogger<SaveAllServerCommandHandler> _logger;
    private readonly IPublisher _publisher;

    public SaveAllServerCommandHandler(IServerRepository serverRepository, ILoggedInUserService loggedInUserService,ISiteRepository siteRepository,
        ISiteTypeRepository siteTypeRepository,ILogger<SaveAllServerCommandHandler> logger,IPublisher publisher)
    {
        _serverRepository = serverRepository;
        _loggedInUserService = loggedInUserService;
        _siteRepository = siteRepository;
        _siteTypeRepository = siteTypeRepository;
        _logger = logger;
        _publisher = publisher;
   
    }

    public async Task<SaveAllServerResponse> Handle(SaveAllServerCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _serverRepository.GetByReferenceIdAsync(request.ServerId);

        Guard.Against.NullOrDeactive(eventToUpdate, nameof(Domain.Entities.Server),
            new NotFoundException(nameof(Domain.Entities.Server), request.ServerName));

        var serverList = request.ServerList.Select(server => new Domain.Entities.Server
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = server.Name,
            SiteId = server.SiteId,
            SiteName = server.SiteName,
            ServerTypeId = server.ServerTypeId,
            ServerType = server.ServerType,
            Properties = server.Properties,
            LicenseId = server.LicenseId,
            LicenseKey = server.LicenseKey,
            CompanyId = eventToUpdate.CompanyId,
            BusinessServiceId = eventToUpdate.BusinessServiceId,
            BusinessServiceName = eventToUpdate.BusinessServiceName,
            OSTypeId = eventToUpdate.OSTypeId,
            OSType = eventToUpdate.OSType,
            IsActive = true,
            CreatedBy = _loggedInUserService.UserId,
            CreatedDate = DateTime.Now,
            LastModifiedBy = _loggedInUserService.UserId,
            LastModifiedDate = DateTime.Now,
            RoleType = eventToUpdate?.RoleType,
            RoleTypeId = eventToUpdate?.RoleTypeId,
            Status = eventToUpdate?.Status,
            Version = eventToUpdate?.Version,

        }).ToList();

        //await _serverRepository.AddRange(serverList);

        var serverNames = new List<string>();
        foreach (var server in serverList)
        {
            try
            {
                var poNumber = server.LicenseKey;

                server.LicenseKey = SecurityHelper.Encrypt(poNumber);

                var serverAdded = await _serverRepository.AddAsync(server);

                if (!serverAdded.RoleType.Trim().ToLower().Equals("database"))
                {
                    var ipAddress = GetJsonProperties.GetIpAddressFromProperties(serverAdded.Properties);

                    var hostName = GetJsonProperties.GetHostNameFromProperties(serverAdded.Properties);
                    var logo = GetJsonProperties.GetJsonValue(serverAdded.Properties, "icon");

                    var site = await _siteRepository.GetByReferenceIdAsync(server.SiteId);

                    var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site.TypeId);

                    if (siteType.Category.ToLower().Contains("primary"))
                        await _publisher.Publish(new ServerLicenseInfoCreatedEvent
                        {
                            EntityName = serverAdded.Name,
                            LicenseId = serverAdded.LicenseId,
                            PONumber = poNumber,
                            EntityId = serverAdded.ReferenceId,
                            EntityType = serverAdded.RoleType,
                            Type = serverAdded.OSType,
                            IpAddress = $"{ipAddress},{hostName}",
                            BusinessServiceId = serverAdded.BusinessServiceId,
                            BusinessServiceName = serverAdded.BusinessServiceName,
                            Category = serverAdded.ServerType,
                            Logo = logo
                        }, cancellationToken);
                }
                serverNames.Add(serverAdded.Name);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"Error while process save-as for {server.Name} in Server {ex.Message}");
                continue;
            }
        }
        await _publisher.Publish(new SaveAllServerEvent { ServerOrginal = eventToUpdate.Name, ServerNames = serverNames }, cancellationToken);

        return new SaveAllServerResponse
        {
            Message = "Server save-as Completed!."
        };

    }
}