﻿using ContinuityPatrol.Application.Features.ServerSubType.Commands.Create;
using ContinuityPatrol.Application.Features.ServerSubType.Commands.Update;
using ContinuityPatrol.Application.Features.ServerSubType.Events.Create;
using ContinuityPatrol.Application.Features.ServerSubType.Events.Delete;
using ContinuityPatrol.Application.Features.ServerSubType.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;


public class ServerSubTypeFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<ServerSubType> ServerSubTypes { get; set; }
    public CreateServerSubTypeCommand CreateServerSubTypeCommand { get; set; }
    public UpdateServerSubTypeCommand UpdateServerSubTypeCommand { get; set; }
    public ServerSubTypeCreatedEvent ServerSubTypeCreatedEvent { get; set; }
    public ServerSubTypeDeletedEvent ServerSubTypeDeletedEvent { get; set; }
    public ServerSubTypeUpdatedEvent ServerSubTypeUpdatedEvent { get; set; }

    public ServerSubTypeFixture()
    {
        ServerSubTypes = AutoServerSubTypeFixture.Create<List<ServerSubType>>();

        CreateServerSubTypeCommand = AutoServerSubTypeFixture.Create<CreateServerSubTypeCommand>();

        UpdateServerSubTypeCommand = AutoServerSubTypeFixture.Create<UpdateServerSubTypeCommand>();

        ServerSubTypeCreatedEvent = AutoServerSubTypeFixture.Create<ServerSubTypeCreatedEvent>();

        ServerSubTypeDeletedEvent = AutoServerSubTypeFixture.Create<ServerSubTypeDeletedEvent>();

        ServerSubTypeUpdatedEvent = AutoServerSubTypeFixture.Create<ServerSubTypeUpdatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ServerSubTypeProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoServerSubTypeFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateServerSubTypeCommand>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateServerSubTypeCommand>(p => p.Name, 10));
            fixture.Customize<UpdateServerSubTypeCommand>(c => c.With(b => b.Id, 0.ToString));
            fixture.Customize<ServerSubType>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ServerSubTypeCreatedEvent>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ServerSubTypeDeletedEvent>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ServerSubTypeUpdatedEvent>(p => p.Name, 10));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}
