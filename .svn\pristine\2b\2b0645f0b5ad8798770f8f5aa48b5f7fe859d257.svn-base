﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class DrCalendarFilterSpecification : Specification<DrCalenderActivity>
{
    public DrCalendarFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.ActivityName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("responsibility=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Responsibility.Contains(stringItem.Replace("responsibility=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("activitystatus=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ActivityStatus.Contains(stringItem.Replace("activitystatus=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("recipienttwo=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.RecipientTwo.Contains(stringItem.Replace("recipienttwo=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("workflowprofiles=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.WorkflowProfiles.Contains(stringItem.Replace("workflowprofiles=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.Responsibility.Contains(searchString) || p.ActivityStatus.Contains(searchString) ||
                    p.RecipientTwo.Contains(searchString) || p.WorkflowProfiles.Contains(searchString);
            }
        }
    }
}