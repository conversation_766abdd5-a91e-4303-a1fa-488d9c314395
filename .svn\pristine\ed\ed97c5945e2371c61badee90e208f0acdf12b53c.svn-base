using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ResiliencyReadyWorkflowScheduleLogFixture : IDisposable
{
    public List<ResiliencyReadyWorkflowScheduleLog> ResiliencyReadyWorkflowScheduleLogPaginationList { get; set; }
    public List<ResiliencyReadyWorkflowScheduleLog> ResiliencyReadyWorkflowScheduleLogList { get; set; }
    public ResiliencyReadyWorkflowScheduleLog ResiliencyReadyWorkflowScheduleLogDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ResiliencyReadyWorkflowScheduleLogFixture()
    {
        var fixture = new Fixture();

        ResiliencyReadyWorkflowScheduleLogList = fixture.Create<List<ResiliencyReadyWorkflowScheduleLog>>();

        ResiliencyReadyWorkflowScheduleLogPaginationList = fixture.CreateMany<ResiliencyReadyWorkflowScheduleLog>(20).ToList();

        ResiliencyReadyWorkflowScheduleLogPaginationList.ForEach(x => x.CompanyId = CompanyId);

        ResiliencyReadyWorkflowScheduleLogList.ForEach(x => x.CompanyId = CompanyId);

        ResiliencyReadyWorkflowScheduleLogDto = fixture.Create<ResiliencyReadyWorkflowScheduleLog>();

        ResiliencyReadyWorkflowScheduleLogDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
