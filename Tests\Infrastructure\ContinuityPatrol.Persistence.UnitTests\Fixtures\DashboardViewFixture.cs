using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DashboardViewFixture : IDisposable
{
    public List<DashboardView> DashboardViewPaginationList { get; set; }
    public List<DashboardView> DashboardViewList { get; set; }
    public DashboardView DashboardViewDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
    public const string BusinessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";
    public const string InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c";


    public ApplicationDbContext DbContext { get; private set; }

    public DashboardViewFixture()
    {
        var fixture = new Fixture();

        DashboardViewList = fixture.Create<List<DashboardView>>();

        DashboardViewPaginationList = fixture.CreateMany<DashboardView>(20).ToList();

        DashboardViewPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DashboardViewPaginationList.ForEach(x => x.IsActive = true);
        DashboardViewPaginationList.ForEach(x => x.CompanyId = CompanyId);
        DashboardViewPaginationList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        DashboardViewPaginationList.ForEach(x => x.BusinessFunctionId = BusinessFunctionId);
    


        DashboardViewList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DashboardViewList.ForEach(x => x.IsActive = true);
        DashboardViewList.ForEach(x => x.CompanyId = CompanyId);
        DashboardViewList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        DashboardViewList.ForEach(x => x.BusinessFunctionId = BusinessFunctionId);
      

        DashboardViewDto = fixture.Create<DashboardView>();
        DashboardViewDto.ReferenceId = Guid.NewGuid().ToString();
        DashboardViewDto.IsActive = true;
        DashboardViewDto.CompanyId = CompanyId;
        DashboardViewDto.BusinessServiceId = BusinessServiceId;
        DashboardViewDto.BusinessFunctionId = BusinessFunctionId;
        DashboardViewDto.InfraObjectId = InfraObjectId;
  

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
