﻿using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetByEntity;
using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Create;
using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Delete;
using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Command.Decommission;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Create;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Delete;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Command.Replace;
using ContinuityPatrol.Application.Features.LicenseManager.Command.UpdateState;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetAMCExpiredList;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetByPoNumber;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetChildLicenseByParentId;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetDecommissionByPoNumber;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetDetailView;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseByCompanyId;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseByPONumber;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseCount;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseExpireList;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetList;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetNames;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetPoNumber;
using ContinuityPatrol.Domain.ViewModels.LicenseInfoModel;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class LicenseManagerService : BaseService, ILicenseManagerService
{
    public LicenseManagerService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<LicenseInfoByEntityListVm>> GetLicenseInfoByEntity(string licenseId, string entity)
    {
        Logger.LogDebug($"Get All LicenseInfo by LicenseId: '{licenseId}' and Entity :'{entity}'");

        return await Mediator.Send(new GetLicenseInfoByEntityListQuery { LicenseId = licenseId, Entity = entity });
    }

    public async Task<List<LicenseManagerDetailViewVm>> LicenseManagerDetailView()
    {
        Logger.LogDebug("Get License Managers Detail View");

        return await Mediator.Send(new GetLicenseManagerDetailViewQuery());
    }

    public async Task<List<LicenseManagerListVm>> GetLicenseManagerList()
    {
        Logger.LogDebug("Get All License Managers");

        return await Mediator.Send(new GetLicenseManagerListQuery());
    }
    public async  Task<List<LicenseExpireListVm>> GetLicenseAMCExpiresByCompanyId(string companyId)
    {
        Logger.LogDebug($"Get license Amc Plan expires by company Id {companyId}");

        return await Mediator.Send(new GetLicenseManagerAMCExpiryListQuery { CompanyId = companyId });
    }

    public async Task<BaseResponse> CreateBaseLicense(CreateBaseLicenseCommand createBaseLicenseCommand)
    {
        Logger.LogDebug($"Create BaseLicense '{createBaseLicenseCommand}'");

        return await Mediator.Send(createBaseLicenseCommand);
    }

    public async Task<BaseResponse> UpdateBaseLicense(UpdateBaseLicenseCommand updateBaseLicenseCommand)
    {
        Logger.LogDebug($"Update BaseLicense '{updateBaseLicenseCommand}'");

        return await Mediator.Send(updateBaseLicenseCommand);
    }

    public async Task<BaseResponse> CreateDerivedLicense(CreateDerivedLicenseCommand createDerivedLicenseCommand)
    {
        Logger.LogDebug($"Create DerivedLicense '{createDerivedLicenseCommand}'");

        return await Mediator.Send(createDerivedLicenseCommand);
    }

    public async Task<BaseResponse> UpdateDerivedLicense(UpdateDerivedLicenseCommand updateDerivedLicenseCommand)
    {
        Logger.LogDebug($"Update DerivedLicense '{updateDerivedLicenseCommand}'");

        return await Mediator.Send(updateDerivedLicenseCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "License Id");

        Logger.LogDebug($"Delete BaseLicense Details by Id '{id}'");

        return await Mediator.Send(new DeleteBaseLicenseCommand { Id = id });
    }

    public async Task<LicenseManagerDetailVm> GetLicenseManagerById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "License Id");

        Logger.LogDebug($"Get LicenseManager Detail by Id'{id}'");

        return await Mediator.Send(new GetLicenseManagerDetailQuery { Id = id });
    }

    public async Task<BaseResponse> DeleteDerivedLicense(string id)
    {
        Logger.LogDebug($"Delete Derived License Details by Id '{id}'");

        return await Mediator.Send(new DeleteDerivedLicenseCommand { Id = id });
    }

    public async Task<List<LicenseManagerNameVm>> GetAllPoNumbers()
    {
        Logger.LogDebug("Get All License Manager PONumbers");

        return await Mediator.Send(new GetLicenseManagerNameQuery());
    }

    public async Task<List<ChildLicenseDetailByParentIdVm>> GetLicenseByParentIdAndParentPoNumber(string parentId,
        string parentPoNumber)
    {
        Guard.Against.InvalidGuidOrEmpty(parentId, "Parent Id");

        Logger.LogDebug($"Get Child License Detail by  Parent Id'{parentId}' and parentPO'{parentPoNumber}'");

        return await Mediator.Send(new GetChildLicenseDetailByParentIdQuery
            { ParentId = parentId, ParentPO = parentPoNumber });
    }
    public async Task<GetLicenseByPONumberVm> GetLicenseDetailsByPoNumber(string poNumber)
    {
        Logger.LogDebug($"Get LicenseManager Detail by poNumber'{poNumber}'");

        return await Mediator.Send(new GetLicenseByPONumberQuery { PONumber = poNumber });
    }
    public async Task<LicenseManagerByPoNumberVm> GetLicenseManagerByPoNumber(string poNumber)
    {
        Logger.LogDebug($"Get LicenseManager Detail by poNumber'{poNumber}'");

        return await Mediator.Send(new GetLicenseManagerByPoNumberQuery { PoNumber = poNumber });
    }

    public async Task<object> GetDecommissionByIdAndEntityId(string licenseId, string entityId, string entityType)
    {
        Logger.LogDebug(
            $"Get Decommission by entityId'{entityId}' and licenseId '{licenseId}' and entityType '{entityType}'");

        return await Mediator.Send(new DecommissionDetailQuery
            { EntityId = entityId, LicenseId = licenseId, EntityType = entityType });
    }

    public async Task<BaseResponse> DeleteDecommissionByEntityId(string entityId, string licenseId, string entityType,string entityName)
    {
        Logger.LogDebug(
            $"Delete Decommission license details by entityId '{entityId}' and licenseId '{licenseId}' and entityType '{entityType}' and entityName {entityName}");

        return await Mediator.Send(new LicenseDecommissionCommand
            { EntityId = entityId, LicenseId = licenseId, EntityType = entityType,EntityName=entityName });
    }

    public async Task<LicenseCountVm> GetLicenseManagerCount()
    {
        Logger.LogDebug("Get LicenseManager Count");

        return await Mediator.Send(new GetLicenseCountQuery());
    }

    public async Task<BaseResponse> ReplaceLicenseDetails(LicenseReplaceCommand command)
    {
        Logger.LogDebug($"Replace License '{command.EntityName}'");

        return await Mediator.Send(command);
    }

    public async Task<List<GetLicenseByCompanyIdVm>> GetLicenseByCompanyId(string companyId)
    {
        Logger.LogDebug($"Get LicenseManager Detail by companyId'{companyId}'");

        return await Mediator.Send(new GetLicenseByCompanyIdQuery { CompanyId = companyId });
    }
    public async Task<List<LicenseExpireListVm>> GetLicenseExpiresByCompanyId(string companyId)
    {
        Logger.LogDebug($"Get license expires by company Id {companyId}");

        return await Mediator.Send(new GetLicenseExpireListQuery { CompanyId = companyId });
    }
    public async Task<BaseResponse> UpdateLicenseState(UpdateLicenseStateCommand updateLicenseStateCommand)
    {
        Logger.LogDebug("Update License State");

        return await Mediator.Send(updateLicenseStateCommand);
    }


    public async Task<List<GetPoNumberListVm>> GetPoNumber(string type, string? roleType, string? siteId, string? serverId, string? replicationType,string? databaseTypeId)
    {
        Logger.LogDebug("Get LicenseManager PoNumbers.");

        return await Mediator.Send(new GetPoNumberListQuery { Type = type, RoleType = roleType, SiteId = siteId, ServerId = serverId, ReplicationType = replicationType,DatabaseTypeId = databaseTypeId });
    }
}