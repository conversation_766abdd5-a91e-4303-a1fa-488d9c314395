﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Shared.Infrastructure.Identity;

public class LoggedInUserService : ILoggedInUserService
{
    public LoggedInUserService(IHttpContextAccessor httpContextAccessor)
    {
        UserId = httpContextAccessor.HttpContext?.User.Claims.FirstOrDefault(c => c.Type == "uid")?.Value;

        CompanyId = httpContextAccessor.HttpContext?.User.Claims.FirstOrDefault(c => c.Type == "companyId")?.Value;

        CompanyName = httpContextAccessor.HttpContext?.User.Claims.FirstOrDefault(c => c.Type == "companyName")?.Value;

        Role = httpContextAccessor.HttpContext?.User.FindFirstValue(ClaimTypes.Role) ?? "Undefined";

        LoginName = httpContextAccessor.HttpContext?.User.Identity?.Name ?? "Anonymous";

        var parent = httpContextAccessor.HttpContext?.User.Claims.FirstOrDefault(c => c.Type == "isParent")?.Value;

        IsParent = !string.IsNullOrWhiteSpace(parent) && Convert.ToBoolean(parent);

        IsAuthenticated = httpContextAccessor.HttpContext?.User.Identity?.IsAuthenticated ?? false;

        IsAllInfra = (httpContextAccessor.HttpContext?.User.Claims.FirstOrDefault(c => c.Type == "isAllInfra")?.Value).ToBoolean();

        AssignedInfras = httpContextAccessor.HttpContext?.User.Claims.FirstOrDefault(c => c.Type == "AssignedInfras")
            ?.Value;

        if (httpContextAccessor.HttpContext?.Connection.RemoteIpAddress != null)
            IpAddress = httpContextAccessor.HttpContext?.Connection.RemoteIpAddress.ToString();

        RequestedUrl = httpContextAccessor.HttpContext?.Request.GetDisplayUrl();

        var permissions = httpContextAccessor.HttpContext?.User.Claims.FirstOrDefault(c => c.Type == "permissions")
            ?.Value;

        Permissions = new List<string>();

        if (string.IsNullOrWhiteSpace(permissions)) return;

        Permissions = JsonConvert.DeserializeObject<List<string>>(permissions);
    }

    public string UserId { get; }
    public string CompanyId { get; }
    public string CompanyName { get; }
    public bool IsParent { get; }
    public string Role { get; }
    public string RequestedUrl { get; }
    public List<string> Permissions { get; }
    public string LoginName { get; }
    public bool IsAuthenticated { get; }
    public string IpAddress { get; }
    public string AssignedInfras { get; }
    public bool IsSuperAdmin => Role == UserRole.SuperAdmin.GetDescription();
    public bool IsAdministrator => Role == UserRole.Administrator.GetDescription();
    public bool IsSiteAdmin => Role == UserRole.SiteAdmin.GetDescription();
    public bool IsOperator => Role == UserRole.Operator.GetDescription();
    public bool IsManager => Role == UserRole.Manager.GetDescription();
    public bool IsAllInfra { get; }
}