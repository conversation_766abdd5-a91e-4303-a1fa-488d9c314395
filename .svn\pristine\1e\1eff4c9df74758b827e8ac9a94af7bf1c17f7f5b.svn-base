﻿using ContinuityPatrol.Application.Features.Setting.Commands.Create;
using ContinuityPatrol.Application.Features.Setting.Commands.Update;
using ContinuityPatrol.Application.Features.Setting.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Setting.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SettingModel;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class SettingsControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<ILogger<SettingsController>> _mockLogger = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private  SettingsController _controller;

        public SettingsControllerShould()
        {

            Initialize();
        }
        internal void Initialize()
        {
            _controller = new SettingsController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockLoggedInUserService.Object,
                _mockMapper.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_ReturnsView()
        {
            var result = await _controller.List() as ViewResult;

            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetList_ReturnsJsonResultWithSettings()
        {
            var _getSettingPaginatedListQuery = new AutoFixture.Fixture().Create<GetSettingPaginatedListQuery>();
            _mockDataProvider.Setup(p=>p.Setting.GetSettingPaginatedList(_getSettingPaginatedListQuery)).ReturnsAsync(It.IsAny<PaginatedResult<SettingListVm>>);
            
            // Act
            var result = await _controller.GetList() as JsonResult;
            var data = result?.Value as List<SettingModel>;

            // Assert
            Assert.IsType<JsonResult>(result);
            
        }

        [Fact]
        public async Task CreateOrUpdate_CreatesSettingSuccessfully()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<SettingModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateSettingCommand ();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockLoggedInUserService.Setup(s => s.UserId).Returns("user-id");
            _mockMapper.Setup(m => m.Map<CreateSettingCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.Setting.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result.ActionName);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesSettingSuccessfully()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<SettingModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateSettingCommand ();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockLoggedInUserService.Setup(s => s.UserId).Returns("user-id");
            _mockMapper.Setup(m => m.Map<UpdateSettingCommand>(model)).Returns(updateCommand);
            _mockDataProvider.Setup(p => p.Setting.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result.ActionName);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesGeneralException()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<SettingModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            _mockLoggedInUserService.Setup(s => s.UserId).Returns("user-id");
            _mockMapper.Setup(m => m.Map<CreateSettingCommand>(model)).Returns(new CreateSettingCommand());
            _mockDataProvider.Setup(p => p.Setting.CreateAsync(It.IsAny<CreateSettingCommand>()))
                .ThrowsAsync(new Exception("General error"));

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result.ActionName);
            Assert.Equal("List", result.ActionName);
           
        }

        [Fact]
        public void SmtpConfiguration_ReturnsView()
        {
            // Act
            var result = _controller.SmtpConfiguration() as ViewResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== ADDITIONAL COMPREHENSIVE TESTS FOR 100% COVERAGE =====

        [Fact]
        public async Task List_PublishesSettingPaginatedEvent()
        {
            // Act
            await _controller.List();

            // Assert
            _mockPublisher.Verify(p => p.Publish(It.IsAny<SettingPaginatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task List_LogsDebugMessage()
        {
            // Act
            await _controller.List();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering List method in Settings")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetList_ReturnsJsonWithSettingsList_WhenSuccessful()
        {
            // Arrange
            var expectedSettings = new List<SettingListVm>();
            _mockDataProvider.Setup(p => p.Setting.GetSettingsList())
                .ReturnsAsync(expectedSettings);

            // Act
            var result = await _controller.GetList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedSettings, result.Value);
        }

        [Fact]
        public async Task GetList_ReturnsEmptyJson_WhenExceptionOccurs()
        {
            // Arrange
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(p => p.Setting.GetSettingsList())
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.GetList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("", result.Value);
        }

        [Fact]
        public async Task GetList_LogsDebugAndExceptionMessages()
        {
            // Arrange
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(p => p.Setting.GetSettingsList())
                .ThrowsAsync(exception);

            // Act
            await _controller.GetList();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering GetList method in Settings")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_CreatesSettingWithFailureResponse()
        {
            // Arrange
            var model = new SettingModel { Id = "" };
            var createCommand = new CreateSettingCommand();
            var response = new BaseResponse { Success = false, Message = "Creation failed" };

            _mockLoggedInUserService.Setup(s => s.UserId).Returns("user-id");
            _mockMapper.Setup(m => m.Map<CreateSettingCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.Setting.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesSettingWithFailureResponse()
        {
            // Arrange
            var model = new SettingModel { Id = "123" };
            var updateCommand = new UpdateSettingCommand();
            var response = new BaseResponse { Success = false, Message = "Update failed" };

            _mockLoggedInUserService.Setup(s => s.UserId).Returns("user-id");
            _mockMapper.Setup(m => m.Map<UpdateSettingCommand>(model)).Returns(updateCommand);
            _mockDataProvider.Setup(p => p.Setting.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesValidationException()
        {
            // Arrange
            var model = new SettingModel { Id = "" };
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Name", "Name is required"));
            var validationException = new ValidationException(validationResult);

            _mockLoggedInUserService.Setup(s => s.UserId).Returns("user-id");
            _mockMapper.Setup(m => m.Map<CreateSettingCommand>(model)).Returns(new CreateSettingCommand());
            _mockDataProvider.Setup(p => p.Setting.CreateAsync(It.IsAny<CreateSettingCommand>()))
                .ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_SetsLoginUserId()
        {
            // Arrange
            var model = new SettingModel { Id = "" };
            var userId = "test-user-id";
            var createCommand = new CreateSettingCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockLoggedInUserService.Setup(s => s.UserId).Returns(userId);
            _mockMapper.Setup(m => m.Map<CreateSettingCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.Setting.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            Assert.Equal(userId, model.LoginUserId);
        }

        [Fact]
        public async Task CreateOrUpdate_LogsDebugMessages()
        {
            // Arrange
            var model = new SettingModel { Id = "" };
            var createCommand = new CreateSettingCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockLoggedInUserService.Setup(s => s.UserId).Returns("user-id");
            _mockMapper.Setup(m => m.Map<CreateSettingCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.Setting.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering CreateOrUpdate method in Settings")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Creating Settings")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        // ===== CONSTRUCTOR AND SETUP TESTS =====

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new SettingsController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockLoggedInUserService.Object,
                _mockMapper.Object
            );

            // Assert
            Assert.NotNull(controller);
        }

        [Fact]
        public void Controller_ShouldHaveAreaAttribute()
        {
            // Arrange
            var controllerType = typeof(SettingsController);

            // Act
            var areaAttribute = controllerType.GetCustomAttributes(typeof(AreaAttribute), false)
                .Cast<AreaAttribute>()
                .FirstOrDefault();

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void Controller_ShouldImplementControllerBase()
        {
            // Act
            var controller = new SettingsController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockLoggedInUserService.Object,
                _mockMapper.Object
            );

            // Assert
            Assert.IsAssignableFrom<Microsoft.AspNetCore.Mvc.Controller>(controller);
        }

        // ===== METHOD ATTRIBUTE TESTS =====

        [Fact]
        public void CreateOrUpdate_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var controllerType = typeof(SettingsController);
            var method = controllerType.GetMethod("CreateOrUpdate");

            // Act
            var httpPostAttribute = method.GetCustomAttributes(typeof(HttpPostAttribute), false).FirstOrDefault();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        // ===== EDGE CASE AND ERROR HANDLING TESTS =====

        [Fact]
        public async Task List_ShouldNotThrowException()
        {
            // Act & Assert
            var exception = await Record.ExceptionAsync(() => _controller.List());
            Assert.Null(exception);
        }

        [Fact]
        public async Task GetList_ShouldNotThrowException()
        {
            // Act & Assert
            var exception = await Record.ExceptionAsync(() => _controller.GetList());
            Assert.Null(exception);
        }

        [Fact]
        public void SmtpConfiguration_ShouldNotThrowException()
        {
            // Act & Assert
            var exception = Record.Exception(() => _controller.SmtpConfiguration());
            Assert.Null(exception);
        }

        [Fact]
        public async Task CreateOrUpdate_WithNullModel_ThrowsNullReferenceException()
        {
            // Arrange
            SettingModel model = null;

            // Act & Assert
            var exception = await Record.ExceptionAsync(() => _controller.CreateOrUpdate(model));
            Assert.NotNull(exception);
            Assert.IsType<NullReferenceException>(exception);
        }

        [Fact]
        public async Task CreateOrUpdate_WithEmptyId_UsesCreatePath()
        {
            // Arrange
            var model = new SettingModel { Id = "" };
            var createCommand = new CreateSettingCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockLoggedInUserService.Setup(s => s.UserId).Returns("user-id");
            _mockMapper.Setup(m => m.Map<CreateSettingCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.Setting.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockDataProvider.Verify(p => p.Setting.CreateAsync(createCommand), Times.Once);
            _mockDataProvider.Verify(p => p.Setting.UpdateAsync(It.IsAny<UpdateSettingCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_WithNonEmptyId_UsesUpdatePath()
        {
            // Arrange
            var model = new SettingModel { Id = "123" };
            var updateCommand = new UpdateSettingCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockLoggedInUserService.Setup(s => s.UserId).Returns("user-id");
            _mockMapper.Setup(m => m.Map<UpdateSettingCommand>(model)).Returns(updateCommand);
            _mockDataProvider.Setup(p => p.Setting.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockDataProvider.Verify(p => p.Setting.UpdateAsync(updateCommand), Times.Once);
            _mockDataProvider.Verify(p => p.Setting.CreateAsync(It.IsAny<CreateSettingCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_LogsUpdateDebugMessage()
        {
            // Arrange
            var model = new SettingModel { Id = "123" };
            var updateCommand = new UpdateSettingCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockLoggedInUserService.Setup(s => s.UserId).Returns("user-id");
            _mockMapper.Setup(m => m.Map<UpdateSettingCommand>(model)).Returns(updateCommand);
            _mockDataProvider.Setup(p => p.Setting.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Updating Settings")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_LogsCompletionMessage()
        {
            // Arrange
            var model = new SettingModel { Id = "" };
            var createCommand = new CreateSettingCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockLoggedInUserService.Setup(s => s.UserId).Returns("user-id");
            _mockMapper.Setup(m => m.Map<CreateSettingCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.Setting.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("CreateOrUpdate operation completed successfully in Settings, returning view.")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }
    }
}
