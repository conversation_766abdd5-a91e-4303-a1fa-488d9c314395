﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.InfraObjectScheduler.Events.Delete;

public class InfraObjectSchedulerDeletedEventHandler : INotificationHandler<InfraObjectSchedulerDeletedEvent>
{
    private readonly ILogger<InfraObjectSchedulerDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public InfraObjectSchedulerDeletedEventHandler(ILoggedInUserService userService,
        ILogger<InfraObjectSchedulerDeletedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(InfraObjectSchedulerDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = "Manage Resilience Readiness",
            Action = $"{ActivityType.Delete} Manage Resilience Readiness",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"Manage Resilience Readiness '{deletedEvent.InfraObjectName}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Manage Resilience Readiness '{deletedEvent.InfraObjectName}' deleted successfully.");
    }
}