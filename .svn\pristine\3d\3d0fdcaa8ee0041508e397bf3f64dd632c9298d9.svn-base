using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using System.Threading.Tasks;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DatabaseViewRepositoryTests : IClassFixture<DatabaseViewFixture>, IClassFixture<InfraObjectFixture>
{
    private readonly DatabaseViewFixture _databaseViewFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DatabaseViewRepository _repository;
    private readonly DatabaseViewRepository _repositoryNotParent;
    private readonly InfraObjectRepository _infraObjectRepository;


    public DatabaseViewRepositoryTests(DatabaseViewFixture databaseViewFixture)
    {

        _databaseViewFixture = databaseViewFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _infraObjectRepository = new InfraObjectRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
        _repository = new DatabaseViewRepository(_dbContext, DbContextFactory.GetMockUserService(), _infraObjectRepository);

        _repositoryNotParent = new DatabaseViewRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent(), _infraObjectRepository);
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var databaseView = _databaseViewFixture.DatabaseViewDto;

        // Act
        var result = await _repository.AddAsync(databaseView);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(databaseView.Name, result.Name);
        Assert.Equal(databaseView.CompanyId, result.CompanyId);
        Assert.Single(_dbContext.DatabaseViews);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(databaseViews.Count, result.Count);
        Assert.All(result, x => Assert.Equal(DatabaseViewFixture.CompanyId, x.CompanyId));
    }

   
    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repositoryNotParent.AddRangeAsync(databaseViews);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
        Assert.All(result, x => Assert.Equal(DatabaseViewFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task ListAllAsync_ShouldThrowException_WhenLoggedInUserServiceIsNull()
    {
        // Arrange
        var faultyRepo = new DatabaseViewRepository(_dbContext, null, _infraObjectRepository);

        // Act & Assert
        await Assert.ThrowsAsync<NullReferenceException>(() => faultyRepo.ListAllAsync());
    }
    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenDatabaseViewIsEmpty()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }


    #endregion

    #region DatabaseCountAsync Tests

    [Fact]
    public async Task DatabaseCountAsync_ShouldReturnCorrectCount_WhenIsAllInfraTrue()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.DatabaseCountAsync();

        // Assert
        Assert.Equal(databaseViews.Count, result);
    }

    [Fact]
    public async Task DatabaseCountAsync_ShouldReturnFilteredCount_WhenIsAllInfraFalse()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repositoryNotParent.AddRangeAsync(databaseViews);

        // Act
        var result = await _repositoryNotParent.DatabaseCountAsync();

        // Assert
        Assert.True(result >= 0); // Should return filtered count
    }

    #endregion

    #region GetAllByDatabaseIdsAsync Tests

    [Fact]
    public async Task GetAllByDatabaseIdsAsync_ShouldReturnEntitiesWithMatchingIds_WhenIsParent()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);
        var ids = databaseViews.Take(2).Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repository.GetAllByDatabaseIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains(x.ReferenceId, ids));
    }

    [Fact]
    public async Task GetAllByDatabaseIdsAsync_ShouldReturnFilteredEntities_WhenIsNotParent()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repositoryNotParent.AddRangeAsync(databaseViews);
        var ids = databaseViews.Take(2).Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repositoryNotParent.GetAllByDatabaseIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Contains(x.ReferenceId, ids));
        Assert.All(result, x => Assert.Equal(DatabaseViewFixture.CompanyId, x.CompanyId));
    }

    #endregion

    #region GetByUserName Tests

    [Fact]
    public async Task GetByUserName_ShouldReturnEntitiesWithMatchingUserName_WhenIsAllInfraTrue()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetByUserName(DatabaseViewFixture.UserName);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DatabaseViewFixture.UserName, x.UserName));
    }

    [Fact]
    public async Task GetByUserName_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repositoryNotParent.AddRangeAsync(databaseViews);

        // Act
        var result = await _repositoryNotParent.GetByUserName(DatabaseViewFixture.UserName);

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region GetDatabaseByServerId Tests

    [Fact]
    public async Task GetDatabaseByServerId_ShouldReturnEntitiesWithMatchingServerId_WhenIsAllInfraTrue()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseByServerId(DatabaseViewFixture.ServerId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DatabaseViewFixture.ServerId, x.ServerId));
    }

    [Fact]
    public async Task GetDatabaseByServerId_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repositoryNotParent.AddRangeAsync(databaseViews);

        // Act
        var result = await _repositoryNotParent.GetDatabaseByServerId(DatabaseViewFixture.ServerId);

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    [Fact]
    public async Task GetDatabaseByServerId_ShouldHandleCommaSeparatedServerIds()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var serverId1 = "SERVER_001";
        var serverId2 = "SERVER_002";
        databaseViews.First().ServerId = serverId1;
        databaseViews.Skip(1).First().ServerId = serverId2;

        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseByServerId($"{serverId1},{serverId2}");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.ServerId == serverId1);
        Assert.Contains(result, x => x.ServerId == serverId2);
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public async Task GetPaginatedQuery_ShouldReturnQueryableOrderedByIdDescending_WhenIsAllInfraTrue()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Any());

        // Verify ordering by checking if the first item has a higher ID than the last
        var resultList = result.ToList();
        if (resultList.Count > 1)
        {
            Assert.True(resultList.First().Id >= resultList.Last().Id);
        }
    }

    [Fact]
    public async Task GetPaginatedQuery_ShouldReturnFilteredQueryable_WhenIsAllInfraFalse()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        await _repositoryNotParent.AddRangeAsync(databaseViews);

        // Act
        var result = _repositoryNotParent.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region GetDatabaseByDatabaseTypeIds Tests

    [Fact]
    public async Task GetDatabaseByDatabaseTypeIds_ShouldReturnEntitiesWithMatchingDatabaseTypeIds()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var databaseTypeIds = new List<string> { DatabaseViewFixture.DatabaseTypeId };
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseByDatabaseTypeIds(databaseTypeIds);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DatabaseViewFixture.DatabaseTypeId, x.DatabaseTypeId));
    }

    [Fact]
    public async Task GetDatabaseByDatabaseTypeIds_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList;
        var databaseTypeIds = new List<string> { "NON_EXISTENT_TYPE" };
        await _repository.AddRangeAsync(databaseViews);

        // Act
        var result = await _repository.GetDatabaseByDatabaseTypeIds(databaseTypeIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var databaseViews = _databaseViewFixture.DatabaseViewList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(databaseViews);
        var initialCount = databaseViews.Count;

        var toUpdate = databaseViews.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedDatabaseViewName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = databaseViews.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Name == "UpdatedDatabaseViewName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
