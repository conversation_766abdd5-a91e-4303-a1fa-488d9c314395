﻿using ContinuityPatrol.Application.Features.PluginManager.Commands.Create;
using ContinuityPatrol.Application.Features.PluginManager.Commands.Delete;
using ContinuityPatrol.Application.Features.PluginManager.Commands.Update;
using ContinuityPatrol.Application.Features.PluginManager.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PluginManager.Queries.GetList;
using ContinuityPatrol.Application.Features.PluginManager.Queries.GetNames;
using ContinuityPatrol.Application.Features.PluginManager.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.PluginManager.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PluginManagerModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class PluginManagerService : BaseService, IPluginManagerService
{
    public PluginManagerService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<PluginManagerListVm>> GetPluginManagerList()
    {
        Logger.LogDebug("Get All PluginManagers");

        return await Mediator.Send(new GetPluginManagerListQuery());
    }

    public async Task<PluginManagerDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "PluginManager Id");

        Logger.LogDebug($"Get PluginManager Detail by Id '{id}'");

        return await Mediator.Send(new GetPluginManagerDetailQuery { Id = id });
    }

    public async Task<PaginatedResult<PluginManagerListVm>> GetPluginManagerPaginatedList(
        GetPluginManagerPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in PluginManager Paginated List");

        return await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllPluginManagerCacheKey,
            () => Mediator.Send(query));
    }

    public async Task<BaseResponse> CreateAsync(CreatePluginManagerCommand createPluginManagerCommand)
    {
        Logger.LogDebug($"Create PluginManager '{createPluginManagerCommand}'");

        return await Mediator.Send(createPluginManagerCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdatePluginManagerCommand updatePluginManagerCommand)
    {
        Logger.LogDebug($"Update PluginManager '{updatePluginManagerCommand}'");

        return await Mediator.Send(updatePluginManagerCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "PluginManager Id");

        Logger.LogDebug($"Delete PluginManager Details by Id '{id}'");

        return await Mediator.Send(new DeletePluginManagerCommand { Id = id });
    }

    public async Task<List<PluginManagerNameVm>> GetPluginManagerNames()
    {
        Logger.LogDebug("Get All PluginManager Names");

        return await Mediator.Send(new GetPluginManagerNameQuery());
    }

    public async Task<bool> IsPluginManagerNameExist(string pluginName, string id)
    {
        Guard.Against.NullOrWhiteSpace(pluginName, "PluginManager Name");

        Logger.LogDebug($"Check Name Exists Detail by PluginManager Name '{pluginName}'and Id '{id}'");

        return await Mediator.Send(new GetPluginManagerNameUniqueQuery { Name = pluginName, PluginId = id });
    }
}