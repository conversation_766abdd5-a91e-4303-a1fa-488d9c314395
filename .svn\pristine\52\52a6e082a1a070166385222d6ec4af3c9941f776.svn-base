﻿
namespace ContinuityPatrol.Application.Features.Database.Commands.UpdateVersion;

public class
    UpdateDatabaseVersionCommandHandler : IRequestHandler<UpdateDatabaseVersionCommand, UpdateDatabaseVersionResponse>
{
    private readonly IDatabaseViewRepository _databaseViewRepository;
    private readonly IDatabaseRepository _databaseRepository;
    private readonly IMapper _mapper;

    public UpdateDatabaseVersionCommandHandler(IDatabaseViewRepository databaseViewRepository, IDatabaseRepository databaseRepository, IMapper mapper)
    {
        _databaseRepository = databaseRepository;
        _databaseViewRepository = databaseViewRepository;
        _mapper = mapper;
    }

    public async Task<UpdateDatabaseVersionResponse> Handle(UpdateDatabaseVersionCommand request,
        CancellationToken cancellationToken)
    {
        if (request.IsUpdateAll)
        {
            var eventToUpdateType =
                await _databaseViewRepository.GetByDatabaseTypeIdAndFormVersion(request.DatabaseTypeId,
                    request.OldFormVersion);

            eventToUpdateType.ForEach(x => x.FormVersion = request.NewFormVersion);

            var databaseDto = _mapper.Map<IEnumerable<Domain.Entities.Database>>(eventToUpdateType);

            await _databaseRepository.UpdateRange(databaseDto);

            return new UpdateDatabaseVersionResponse
            {
                Message = "All Database version updated successfully!"
            };
        }

        var eventToUpdateDatabase = await _databaseRepository.GetByReferenceIdAsync(request.Id);

        eventToUpdateDatabase.FormVersion = request.NewFormVersion;

        await _databaseRepository.UpdateAsync(eventToUpdateDatabase);

        var response = new UpdateDatabaseVersionResponse
        {
            Message = "Database version updated successfully!"
        };

        return response;
    }
}