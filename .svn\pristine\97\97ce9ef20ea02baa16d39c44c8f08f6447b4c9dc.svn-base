using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ServerFixture : IDisposable
{
    public List<Server> ServerPaginationList { get; set; }
    public List<Server> ServerList { get; set; }
    public Server ServerDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ServerFixture()
    {
        var fixture = new Fixture();

        ServerList = fixture.Create<List<Server>>();

        ServerPaginationList = fixture.CreateMany<Server>(20).ToList();

        ServerPaginationList.ForEach(x => x.CompanyId = CompanyId);

        ServerList.ForEach(x => x.CompanyId = CompanyId);

        ServerDto = fixture.Create<Server>();

        ServerDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
