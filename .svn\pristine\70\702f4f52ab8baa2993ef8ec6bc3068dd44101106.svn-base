﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetBreachDetail;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class InfraDashboardViewRepository : BaseRepository<InfraDashboardView>, IInfraDashboardViewRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public InfraDashboardViewRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService = null) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public override async Task<IReadOnlyList<InfraDashboardView>> ListAllAsync()
    {
        var infraDashboardList = SelectInfraDashboardView(
            base.QueryAll(infraObject => infraObject.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await infraDashboardList.ToListAsync()
            : await AssignedInfraObjects(infraDashboardList);
    }
    public async Task<List<InfraDashboardView>> GetBusinessServiceViewDetails()
    {
        var infraDashboardList = (
            base.QueryAll(infraObject => infraObject.CompanyId.Equals(_loggedInUserService.CompanyId)))
            .Select(x => new InfraDashboardView { BusinessServiceId=x.BusinessServiceId,BusinessServiceName=x.BusinessServiceName,Status=x.Status});

        return _loggedInUserService.IsAllInfra
            ? await infraDashboardList.ToListAsync()
            :  AssignedBusinessServices(infraDashboardList).ToList();
    }

    public async Task<List<InfraDashboardView>> GetDrReadyInfraObjectList()
    {
        var infraDashboardList = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.DRReady)
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.DRReady);

        return _loggedInUserService.IsAllInfra
            ? await infraDashboardList.ToListAsync()
            : AssignedBusinessServices(infraDashboardList).ToList();
    }

    public async Task<List<InfraDashboardView>> GetBusinessServiceList()
    {
        var businessServicesQuery = base
       .QueryAll(x => x.CompanyId == _loggedInUserService.CompanyId)
       .Select(x => new
       {
           x.BusinessServiceId,
           x.BusinessServiceName,
           x.Priority,
           x.Status,
           x.IsDRReady,
           x.ConfiguredRPO,
           x.ConfiguredRTO,
           x.RPOThreshold,
           x.IsAffectedReplication,
           x.MaxCurrentRPO,
           x.MaxCurrentRTO,
           x.IsPartial,
           x.DRReady,
           x.WorkflowIsRunning
       });

        var groupedQuery = businessServicesQuery
            .GroupBy(bs => new { bs.BusinessServiceId, bs.BusinessServiceName, bs.Priority })
            .Select(group => new InfraDashboardView
            {
                BusinessServiceId = group.Key.BusinessServiceId,
                BusinessServiceName = group.Key.BusinessServiceName,
                Priority = group.Key.Priority,
                Status = group.Any(x => x.Status == "Not Available")
                    ? "Not Available"
                    : group.Any(x => x.Status == "Major Impact")
                        ? "Major Impact"
                        : "Available",
                IsDRReady = group.Where(x => x.DRReady).All(x => x.IsDRReady),
                ConfiguredRPO = group.Max(x => x.ConfiguredRPO),
                ConfiguredRTO = group.Max(x => x.ConfiguredRTO),
                RPOThreshold = group.Max(x => x.RPOThreshold),
                IsAffectedReplication = group.Any(x => x.IsAffectedReplication),
                MaxCurrentRPO = group
                    .Where(x => !string.IsNullOrEmpty(x.MaxCurrentRPO))
                    .Select(x => x.MaxCurrentRPO)
                    .FirstOrDefault() ?? "N/A",
                MaxCurrentRTO = group
                    .Where(x => !string.IsNullOrEmpty(x.MaxCurrentRTO))
                    .Select(x => x.MaxCurrentRTO)
                    .FirstOrDefault() ?? "N/A",
                IsPartial = group.Any(x => x.IsPartial),
                WorkflowIsRunning = group.Any(x => x.WorkflowIsRunning)
            });

        return _loggedInUserService.IsAllInfra
           ? await groupedQuery.ToListAsync()
           : AssignedBusinessServices(groupedQuery).ToList();
    }


    public async Task<List<InfraDashboardView>> GetBsSitePropertiesByBusinessServiceIds(List<string> businessServiceId)
    {
        var businessServices = _loggedInUserService.IsParent
            ? base.FilterBy(x => businessServiceId.Contains(x.BusinessServiceId))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && businessServiceId.Contains(x.BusinessServiceId));

        return _loggedInUserService.IsAllInfra
            ? await businessServices.ToListAsync()
            : AssignedBusinessServices(businessServices).ToList();
    }


    public async Task<BreachDetailVm> GetBreachDetails()
    {
        var infraDashboard =  
            _loggedInUserService.IsParent
                ? base.FilterBy(x => x.ReferenceId != null)
                : base.FilterBy(x=>x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.ReferenceId != null);


        var infraObject = _loggedInUserService.IsAllInfra
            ? await infraDashboard.ToListAsync()
            : await AssignedInfraObjects(infraDashboard);


        var rtoAchievedCount = infraObject.Count(x => x.CurrentRTO.IsNotNullOrWhiteSpace() && GetJsonProperties.IsRtoAchieved(x.CurrentRTO, x.ConfiguredRTO));
        var rtoExceededCount = infraObject.Count(x => x.CurrentRTO.IsNotNullOrWhiteSpace() && GetJsonProperties.IsRtoExceeded(x.CurrentRTO, x.ConfiguredRTO));
        var rpoAchievedCount = infraObject.Count(x => x.CurrentRPO.IsNotNullOrWhiteSpace() && GetJsonProperties.IsRpoAchieved(x.CurrentRPO,x.ConfiguredRPO));
        var rpoExceededCount = infraObject.Count(x => x.CurrentRPO.IsNotNullOrWhiteSpace() && GetJsonProperties.IsRpoExceeded(x.CurrentRPO,x.ConfiguredRPO));

        return new BreachDetailVm
        {
            RtoAchievedCount = rtoAchievedCount,
            RtoExceededCount = rtoExceededCount,
            RpoAchievedCount = rpoAchievedCount,
            RpoExceededCount = rpoExceededCount
        };
    }
    public async Task<Dictionary<string, int>>GetConfiguredRtoByBusinessServiceId(string businessServiceId)
    {
        var filteredQuery = await (_loggedInUserService.IsParent
            ? base.FilterBy(x =>
                x.BusinessServiceId.Equals(businessServiceId) && !string.IsNullOrWhiteSpace(x.ReferenceId))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                x.BusinessServiceId.Equals(businessServiceId) &&
                !string.IsNullOrWhiteSpace(x.ReferenceId))).ToDictionaryAsync(x => x.ReferenceId, x =>Convert.ToInt32(x.ConfiguredRTO)); 

        return filteredQuery;
    }
    public async Task<(string BusinessServiceId, int RtoAchieved, int RtoExceeded)> GetCurrentRtoByBusinessServiceId(string businessServiceId,List<string> woGroupInfraObjectIds)
    {
        var filteredQuery = _loggedInUserService.IsParent
            ? base.FilterBy(x =>
                x.BusinessServiceId.Equals(businessServiceId) && !string.IsNullOrWhiteSpace(x.ReferenceId))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                x.BusinessServiceId.Equals(businessServiceId) &&
                !string.IsNullOrWhiteSpace(x.ReferenceId));

        var result = await filteredQuery.Where(idv=> woGroupInfraObjectIds.Contains(idv.ReferenceId))
            .Select(x => new
            {
                x.BusinessServiceId,
                x.CurrentRTO,
                x.ConfiguredRTO
            })
            .ToListAsync();

        var rtoAchieved = result.Count(x =>
            !string.IsNullOrEmpty(x.CurrentRTO) &&
            Convert.ToInt32(x.ConfiguredRTO) > GetJsonProperties.ConvertToMinutes(x.CurrentRTO));

        var rtoTotal = result.Count(x => !string.IsNullOrEmpty(x.CurrentRTO));
        var rtoExceeded = rtoTotal - rtoAchieved;

        return (businessServiceId, rtoAchieved, rtoExceeded);
    }
    public async Task<InfraDashboardView> GetIsAffectedDetailsById(string infraObjectId)
    {
        var infra = (_loggedInUserService.IsParent
            ? base.FilterBy(x => x.ReferenceId.Equals(infraObjectId))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.ReferenceId.Equals(infraObjectId))).
                Select(x => new InfraDashboardView { Name = x.Name, HeatMapCount = x.HeatMapCount, AffectedCount = x.AffectedCount });

        return _loggedInUserService.IsAllInfra
            ? await infra.FirstOrDefaultAsync()
            : GetInfraObjectByReferenceId(infra.FirstOrDefault());
    }
    public async Task<List<InfraDashboardView>> GetInfraObjectViewByBusinessFunctionId(List<string> businessFunctionId)
    {
        var dashboardView = (_loggedInUserService.IsParent
            ? base.FilterBy(x => businessFunctionId.Contains(x.BusinessFunctionId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) 
                                && businessFunctionId.Contains(x.BusinessFunctionId)))
                .Select(x => new InfraDashboardView
                {
                    BusinessFunctionId = x.BusinessFunctionId,
                    BusinessFunctionName = x.BusinessFunctionName,
                    Status = x.Status,
                    ReferenceId = x.ReferenceId,
                    Name = x.Name,
                    DROperationStatus = x.DROperationStatus,
                    ReplicationStatus = x.ReplicationStatus,
                    MonitorType = x.MonitorType,
                    EntityId = x.EntityId,
                    DataLagValue = x.DataLagValue,
                    ConfiguredRPO = x.ConfiguredRPO,
                    Properties = x.Properties,
                    State = x.State,
                    ReplicationCategoryType = x.ReplicationCategoryType,
                    ReplicationTypeName = x.ReplicationTypeName,
                    RPOGeneratedDate=x.RPOGeneratedDate,
                    RTOGeneratedDate=x.RTOGeneratedDate,
                    TypeName = x.TypeName
                });

        return _loggedInUserService.IsAllInfra
            ? await dashboardView.ToListAsync()
            : await AssignedInfraObjects(dashboardView);
    }
    public async Task<InfraDashboardView> GetInfraObjectDetailsById(string infraObjectId)
    {
        var infraDashboard = (_loggedInUserService.IsParent
            ? base.FilterBy(x => x.ReferenceId.Equals(infraObjectId))
            : base.FilterBy(x => x.ReferenceId.Equals(infraObjectId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)))
            .Select(x => new InfraDashboardView
            {
                ReferenceId=x.ReferenceId,
                DROperationStatus=x.DROperationStatus,
                ReplicationStatus=x.ReplicationStatus,
                ReplicationTypeName =x.ReplicationTypeName,
                ReplicationCategoryType=x.ReplicationCategoryType,
                Properties =x.Properties,
                ServerProperties = x.ServerProperties,
                DatabaseProperties = x.DatabaseProperties,
                ReplicationProperties = x.ReplicationProperties,
                DataLagValue=x.DataLagValue,
                ConfiguredRTO=x.ConfiguredRTO,
                ConfiguredRPO=x.ConfiguredRPO,
                CurrentRPO=x.CurrentRPO,
                CurrentRTO=x.CurrentRTO,
                RPOThreshold=x.RPOThreshold,
                RPOGeneratedDate=x.RPOGeneratedDate,
                RTOGeneratedDate=x.RTOGeneratedDate,
                LastModifiedDate=x.LastModifiedDate,               
                EstimatedRTO=x.EstimatedRTO
            });

        return _loggedInUserService.IsAllInfra
            ? await infraDashboard.FirstOrDefaultAsync()
           : GetInfraObjectByReferenceId(infraDashboard.FirstOrDefault());
    }

    public async Task<List<InfraDashboardView>> GetInfraObjectViewByInfraObjectIds(List<string> infraIds)
    {
        var dashboardView = (_loggedInUserService.IsParent
                ? base.FilterBy(x => !string.IsNullOrWhiteSpace(x.ReferenceId) && infraIds.Contains(x.ReferenceId))
                : base.FilterBy(x =>
                    x.CompanyId.Equals(_loggedInUserService.CompanyId) && !string.IsNullOrWhiteSpace(x.ReferenceId) && infraIds.Contains(x.ReferenceId)))
            .Select(x => new InfraDashboardView
            {
                ReferenceId = x.ReferenceId,
                Status = x.Status,
                Name = x.Name,
                ConfiguredRTO = x.ConfiguredRTO,
                ServerProperties = x.ServerProperties,
                DatabaseProperties = x.DatabaseProperties,
                IsAffected=x.IsAffected
            });

        return _loggedInUserService.IsAllInfra
            ? await dashboardView.ToListAsync()
            : await AssignedInfraObjects(dashboardView);
    }
    public async Task<List<(string BusinessServiceId, List<InfraDashboardView> Views)>> GroupByBusinessServiceIdListAsync()
    {
        var businessServices = base.QueryAll(x => x.CompanyId == _loggedInUserService.CompanyId)
            .Select(x => new InfraDashboardView
            {
                ReferenceId = x.ReferenceId,
                Name = x.Name,
                ConfiguredRTO = x.ConfiguredRTO,
                ServerProperties = x.ServerProperties,
                DatabaseProperties = x.DatabaseProperties,
                BusinessServiceId = x.BusinessServiceId,
                BusinessServiceName = x.BusinessServiceName,
                DROperationStatus = x.DROperationStatus,
                IsAffected = x.IsAffected,
                Status = x.Status
            });

        var serviceList = _loggedInUserService.IsAllInfra
            ? await businessServices.ToListAsync()
            :  AssignedBusinessServices(businessServices);

        return serviceList
            .GroupBy(x => x.BusinessServiceId)
            .Select(group => (
                BusinessServiceId: group.Key,
                Views: group.Select(item => new InfraDashboardView
                {
                    BusinessServiceId = item.BusinessServiceId,
                    BusinessServiceName = item.BusinessServiceName,
                    ReferenceId = item.ReferenceId,
                    Name = item.Name,
                    DROperationStatus = item.DROperationStatus,
                    IsAffected = item.IsAffected,
                    Status = item.Status
                }).ToList()
            ))
            .ToList();
    }


    public async Task<List<InfraDashboardView>> SeviceAvailabilityByInfraObjectIdListAsync()
    {
        var infraDashboardsView = base.QueryAll(x => x.CompanyId == _loggedInUserService.CompanyId)
            .Where(x => x.ReferenceId != null)
            .Select(x => new InfraDashboardView
            {
                ReferenceId = x.ReferenceId,
                Name = x.Name,
                BusinessServiceId = x.BusinessServiceId,
                BusinessServiceName = x.BusinessServiceName,
                BsSiteProperties = x.BsSiteProperties,
                BusinessFunctionId = x.BusinessFunctionId,
                BusinessFunctionName = x.BusinessFunctionName,
                DROperationStatus = x.DROperationStatus,
                IsAffected = x.IsAffected,
                Status = x.Status
            });

        var serviceList = _loggedInUserService.IsAllInfra
            ? await infraDashboardsView.ToListAsync()
            : await AssignedInfraObjects(infraDashboardsView);

        return serviceList;

    }


    public async Task<List<InfraDashboardView>> GetBsSitePropertiesByBusinessServiceId(string businessServiceId)
    {
        var infraDashboard =  (_loggedInUserService.IsParent
              ? base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId))
              : base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)))
              .Select(x => new InfraDashboardView
              {
                  ReferenceId = x.ReferenceId,
                  Name = x.Name,
                  CompanyId = x.CompanyId,
                  BusinessServiceId = x.BusinessServiceId,
                  BusinessServiceName = x.BusinessServiceName,
                  BsSiteProperties = x.BsSiteProperties,
                  DROperationStatus = x.DROperationStatus,
                  ReplicationStatus = x.ReplicationStatus
              });

        return _loggedInUserService.IsAllInfra
            ? await infraDashboard.ToListAsync()
            : AssignedBusinessServices(infraDashboard).ToList();
    }

    //private  async Task<List<InfraDashboardView>> AssignedBusinessFunctions(IQueryable<InfraDashboardView> businessFunctions)
    //{
    //    var assignedBusinessFunctionIds = AssignedEntity.AssignedBusinessServices
    //    .SelectMany(service => service.AssignedBusinessFunctions)
    //    .Select(assignedFunction => assignedFunction.Id)
    //    .ToList();

    //    return await businessFunctions
    //         .Where(businessFunction => assignedBusinessFunctionIds.Contains(businessFunction.BusinessFunctionId))
    //         .ToListAsync();
    //}
    private async Task<List<InfraDashboardView>> AssignedInfraObjects(IQueryable<InfraDashboardView> infraObjects)
    {
        var assignedInfraObjectIds = AssignedEntity.AssignedBusinessServices
        .SelectMany(service => service.AssignedBusinessFunctions)
        .SelectMany(function => function.AssignedInfraObjects)
        .Select(infraObject => infraObject.Id)
        .ToList();

        return await infraObjects
            .Where(infraObject => assignedInfraObjectIds.Contains(infraObject.ReferenceId))
            .ToListAsync();
    }
    public IQueryable<InfraDashboardView> SelectInfraDashboardView(IQueryable<InfraDashboardView> query)
    {
        return query.Select(x => new InfraDashboardView
        {
            ReferenceId = x.ReferenceId,
            Name = x.Name,
            CompanyId = x.CompanyId,
            BusinessServiceId = x.BusinessServiceId,
            BusinessServiceName = x.BusinessServiceName,
            BsSiteProperties = x.BsSiteProperties,
            Priority = x.Priority,
            BusinessFunctionId = x.BusinessFunctionId,
            BusinessFunctionName = x.BusinessFunctionName,
            ConfiguredRPO = x.ConfiguredRPO,
            ConfiguredRTO = x.ConfiguredRTO,
            RPOThreshold = x.RPOThreshold,
            ReplicationCategoryTypeId = x.ReplicationCategoryTypeId,
            ReplicationCategoryType = x.ReplicationCategoryType,
            Type = x.Type,
            TypeName = x.TypeName,
            Status = x.Status,
            EntityId = x.EntityId,
            MonitorType = x.MonitorType,
            DataLagValue = x.DataLagValue,
            Properties = x.Properties,
            CurrentRPO = x.CurrentRPO,
            CurrentRTO = x.CurrentRTO,
            RPOGeneratedDate = x.RPOGeneratedDate,
            RTOGeneratedDate = x.RTOGeneratedDate,
            EstimatedRTO = x.EstimatedRTO,
            IsDRReady = x.IsDRReady,
            ErrorMessage = x.ErrorMessage,
            IsDrift = x.IsDrift,
            State = x.State,
            ReplicationStatus = x.ReplicationStatus,
            DROperationStatus = x.DROperationStatus,
            ServerProperties = x.ServerProperties,
            DatabaseProperties = x.DatabaseProperties,
            ReplicationProperties = x.ReplicationProperties,
            Reason = x.Reason,
            SiteProperties = x.SiteProperties,
            ReplicationTypeId = x.ReplicationTypeId,
            ReplicationTypeName = x.ReplicationTypeName,
            SubTypeId = x.SubTypeId,
            SubType = x.SubType,
            NodeProperties = x.NodeProperties,
            IsAffected = x.IsAffected,
            IsAffectedReplication = x.IsAffectedReplication,
            AffectedCount = x.AffectedCount,
            MaxCurrentRPO = x.MaxCurrentRPO,
            MaxCurrentRTO = x.MaxCurrentRTO
        });
    }

    private IReadOnlyList<InfraDashboardView> AssignedBusinessServices(IQueryable<InfraDashboardView> businessServices)
    {
        var services = new List<InfraDashboardView>();

        foreach (var businessService in businessServices)
            if (AssignedEntity.AssignedBusinessServices.Count > 0)
                services.AddRange(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
                                  where businessService.BusinessServiceId == assignedBusinessService.Id
                                  select businessService);
        return services;
    }
}