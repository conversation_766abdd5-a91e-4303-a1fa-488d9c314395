﻿using ContinuityPatrol.Application.Features.ServerSubType.Commands.Create;
using ContinuityPatrol.Application.Features.ServerSubType.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;


public class AutoServerSubTypeDataAttribute : AutoDataAttribute
{
    public AutoServerSubTypeDataAttribute()
        : base(() =>
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateServerSubTypeCommand>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateServerSubTypeCommand>(p => p.Name, 10));
            fixture.Customize<UpdateServerSubTypeCommand>(c => c.With(b => b.Id, 0.ToString));

            return fixture;
        })
    {

    }
}
