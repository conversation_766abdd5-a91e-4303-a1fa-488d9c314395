﻿using ContinuityPatrol.Application.Features.RpForVmCGMonitorStatus.Commands.Discover;
using ContinuityPatrol.Application.Features.RpForVmCGMonitorStatus.Queries.GetCGHealthStatus;
using ContinuityPatrol.Application.Features.RpForVmCGMonitorStatus.Queries.GetPagination;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IRpForVmCGMonitorStatusService
{
    Task<CGHealthStatusVm> CalculateCgEnableStatus();
    Task<DiscoverRpForResponse> Discover(DiscoverRpForCommand command);

    Task<(PaginatedResult<RpForVmCGMonitorStatusListVm>, Dictionary<string, int>)> GetPagination(GetRpForVmCGMonitorStatusPaginatedQuery request);
        
}
