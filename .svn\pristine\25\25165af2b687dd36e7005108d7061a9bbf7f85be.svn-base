//Formbuilder validation for Server, Database, Single Sign-On, Node, Replication.
let pattern = /^[^!#$%^*()+={}\[\]:;,<>?\/\\~`|"' ]+$/; ///^[a-zA-Z0-9]+$/;
let abbrevationLettersForValidation = /\b(os|ip|sql|sid|pr|dr|asm|ssh|sso|wmi|id|wnn|lss|oauth|arcos|api|url|cmd|vm|esxi|dscli|hmc|vg)\b/ig; //By adding \b before and after each word in the regular expression, it ensures that the matched substrings are whole words and not parts of larger words.
const rsyncRobocopyPathForm = /^(\\\\[^\\\/]+\\[^\\\/]+(\\[^\\\/]*)*|[A-Za-z]:\\[^\\\/]+(\\[^\\\/]*)*|\\[^\\\/]+(\\[^\\\/]*)*|\/[^\/]+(\/[^\/]*)*|~\/[^\/]+(\/[^\/]*)*|https?:\/\/[^\/]+(\/[^\/]*)*|\\\\\.[\\\/][^\\\/]+|\\\\\?[\\\/][^\\\/]+)$/
const pathForServer = /^(\\(\\[^\\]+)+|([A-Za-z]:(\\)?|[A-Za-z]:(\\[^\\]+)+))(\\)?$/  //file and win network path.
const postgresSQLDataBinDirectory = /^(?:(?:[a-zA-Z]:\\Program Files\\PostgreSQL\\\d+\\(?:data|bin)\\?)|(?:\/(?:var\/lib\/(?:postgresql|pgsql)\/\d+\/(?:main|data)|usr\/(?:lib|local)\/postgresql\/\d+\/bin|usr\/local\/pgsql\/bin)\/?))$/;
const regexURL = /^(https?:\/\/)?([\w-]+\.)+[\w-]{2,}(:\d+)?(\/[\w-._~:/?#[\]@!$&'()*+,;=%]*)?$/;  //supports port 
const ipaddressRegx = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

//Node.js source, target path
const regex = /^((?!.*\/\/.*)(?!.*\/ .*)\/{1}([^\\(){}:\*\?<>\|'])+.)$|[a-zA-Z]:(\\\w+)*([\\]|[.])?$/; //in CP-4.5

//Replication
const networkPath = /^\\\\([^\\\/:*?"<>|]+)\\([^\\\/:*?"<>|]+(?:\\[^\\\/:*?"<>|]+)*)$/;
const localPath = /^(\/([a-zA-Z0-9_-]+(\/[a-zA-Z0-9_-]+)*))|(([a-zA-Z]:\\)([a-zA-Z0-9_-]+(\\[a-zA-Z0-9_-]+)*))$/;
const cmdPath = /^[A-Za-z]:\\(?:[^\\\n]*\\)*[^\\\n]+$/;

//common validation message
const filedValidationError = (result) => {
    return "<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> " + result + "</span></div>";
}

async function formBuilderRequiredPathValidation($this, modulename = null) {
    let placeholder = await commonPlaceholder($this);
    let inputValue = $this?.val()?.trim() ?? '';
    $this.next(".dynamic-input-field").remove();

    if (!inputValue) {
        $this.after(filedValidationError(placeholder));
        return false;
    }

    let sanitizedValue = inputValue.replace(/\s{2,}/g, ' '); //Restrict Multiple spaces      
    $this?.val(sanitizedValue);

    if (modulename === "singlesignon") { // don't add database because text also will come.
        let pathValidationSSO = await InvalidPathRegex(sanitizedValue);
        if (pathValidationSSO === true) return true;
        $this.after(filedValidationError(pathValidationSSO));
        return false;
    }

    if (modulename === "server") {
        if (pathForServer.test(sanitizedValue)) return true;
        $this.after(filedValidationError("Invalid path"));
        return false;
    }
    //don't delete
    //if (modulename === "database") {
    //    let validate = placeholder.replaceAll(' ', '').toLowerCase();
    //    if (validate.toLowerCase().includes('datadirectory') || validate.toLowerCase().includes('bindirectory')) {
    //        if (postgresSQLDataBinDirectory.test(sanitizedValue)) {
    //            return true;
    //        }
    //        $this.after(filedValidationError("Invalid path"));
    //        return false;
    //    }
    //    if (validate.toLowerCase().includes('databasebinpath')) {
    //        if (RegExp(/^(?:[a-zA-Z]:)?[\\/][\w\s!@#$%^&*()\-+=,.{}\[\]~`'";]+(?:[\\/][\w\s!@#$%^&*()\-+=,.{}\[\]~`'";]+)*[\\/]?$/i).test(sanitizedValue)) {
    //            return true;
    //        }
    //        $this.after(filedValidationError("Invalid path"));
    //        return false;
    //    }
    //}
    //if (modulename === "replication") {
    //    const resultTrim = placeholder.replace(/Enter/g, "").toLowerCase().replace(/\s+/g, "");
    //    if (resultTrim.toLowerCase().includes("networkpath")) {
    //        if (networkPath.test(sanitizedValue)) {
    //            return true;
    //        }
    //        $this.after(filedValidationError("Invalid path"));
    //        return false;
    //    }
    //    if (resultTrim.toLowerCase().includes("localpath")) {
    //        if (localPath.test(sanitizedValue)) {
    //            return true;
    //        }
    //        $this.after(filedValidationError("Invalid path"));
    //        return false;
    //    }
    //    if (resultTrim.toLowerCase().includes("cmdpath")) {
    //        if (cmdPath.test(sanitizedValue)) {
    //            return true;
    //        }
    //        $this.after(filedValidationError("Invalid path"));
    //        return false;
    //    }
    //}
    return true;
}

function validationRestrictRequiredInputs(restrictRequiredInputs) {
    restrictRequiredInputs?.each(function () {
        let $this = $(this);
        document.getElementById($(this)?.attr("id")).addEventListener("keydown", function () {

            if ($this.is(':visible')) {
                $this.next(".dynamic-input").remove();
                let inputValue = $this?.val()?.trim() ?? '';

                if (inputValue && !pattern.test(inputValue)) {
                    $this.after(`<div class='dynamic-input field-validation-error'>
                                      <span class='required-field-msg'>
                                            Only & _ @ . - special characters allowed
                                      </span>
                                 </div>`);
                    restrictSpecialChars = false;
                }
            }
        });
    });
}

function commonPlaceholder($this) {
    let placeholderValue = $($this)?.attr('placeholder');
    const customCasing = { esxi: 'ESXi' };

    if (placeholderValue) {
        return placeholderValue.charAt(0).toUpperCase() + placeholderValue.slice(1).toLowerCase()
            .replace(abbrevationLettersForValidation, match =>
                match.toLowerCase() === "oauth" ? "OAuth" : customCasing[match.toLowerCase()] || match.toUpperCase()
            );
    }
    return $('label[for="' + $this.attr('id') + '"]').text().replace(/\*/g, '').toLowerCase();
}

const formBuilderRestrictSpaceatStart = (value) => {
    return /^[^\s].*$/.test(value) ? true : "Should not begin with space";
}

const formBuilderRestrictSpaceatEnd = (value) => {
    return /^(?!.*\s$).+$/.test(value) ? true : "Should not end with space";
}

async function formBuilderTextValidation($this, modulename = null) {
    let result = await commonPlaceholder($this);
    let inputValue = $this?.val()?.trim() ?? '';
    $this.next(".dynamic-input-field").remove();

    if (!inputValue) {
        $this.after(filedValidationError(result));
        return false;
    }
    if (inputValue.includes('<')) {
        $this.after(filedValidationError("Special characters not allowed"));
        return false;
    }
    $this.after(`<div class='dynamic-input-field' id='element${$this.attr('id')}'></div>`);
    const errorElement = $(`#element${$this.attr('id')}`);
    let validationResults = [];

    if ($this.attr('type') == 'text') {
        let sanitizedValue = $this?.val().replace(/\s{2,}/g, ' '); //Restrict Multiple spaces
        $this?.val(sanitizedValue);

        //Don't add for server, DB, Repl because for some input need special char.
        //$this.attr('name').toLowerCase().trim(" ").includes("username") || 
        //don't add for username because it affect server page. special char will come.
        if (modulename === "singlesignon" && ($this.attr('name').toLowerCase().trim(" ").includes("appkey") ||
            $this.attr('name').toLowerCase().trim(" ").includes("apiuser") ||
            $this.attr('name').toLowerCase().trim(" ").includes("accountname") ||
            $this.attr('name').toLowerCase().trim(" ").includes("requestreason") ||
            $this.attr('name').toLowerCase().trim(" ").includes("sharedkey") ||
            $this.attr('name').toLowerCase().trim(" ").includes("servicetype") ||
            $this.attr('name').toLowerCase().trim(" ").includes("username"))) {
            validationResults = [
                await SpecialCharValidateCustom(value), //SpecialCharValidate(inputValue),
                await ShouldNotBeginWithUnderScore(inputValue),
                await ShouldNotBeginWithSpace(inputValue),
                await OnlyNumericsValidate(inputValue),
                await ShouldNotBeginWithNumber(inputValue),
                await SpaceWithUnderScore(inputValue),
                await ShouldNotEndWithUnderScore(inputValue),
                await ShouldNotEndWithSpace(inputValue),
                await MultiUnderScoreRegex(inputValue),
                await SpaceAndUnderScoreRegex(inputValue),
            ];
        } else {
            validationResults = [
                await ShouldNotBeginWithUnderScore(inputValue),
                await ShouldNotEndWithUnderScore(inputValue),
                formBuilderRestrictSpaceatStart(inputValue),
                formBuilderRestrictSpaceatEnd(inputValue),
            ];
        }
    } else if ($this.attr('type') == 'number') {
        validationResults = [
            await OnlyNum(inputValue),
        ];
    }
    let res = await CommonValidation(errorElement, validationResults);
    return res === true ? true : false;
}

async function formBuilderIPAddressValidation($this) {
    let result = await commonPlaceholder($this);
    let inputValue = $this?.val()?.trim() ?? '';
    $this.next(".dynamic-input-field").remove();

    if (!inputValue) {
        $this.after(filedValidationError(result));
        return false;
    }
    let sanitizedValue = $this?.val().replace(/\s{2,}/g, ' '); //Restrict Multiple spaces
    let cleanedIp = sanitizedValue.replace(/\.+/g, '.');
    $this?.val(cleanedIp);

    if (cleanedIp.includes(" ")) {
        $this.after(filedValidationError("Enter IP address without space"));
        return false;
    }
    else if (ipaddressRegx.test(cleanedIp)) {
        return true;
    }
    $this.after(filedValidationError("Enter valid IP address"));
    return false;
}

async function formBuilderRequiredURLValidation($this) {
    let result = await commonPlaceholder($this);
    let inputValue = $this?.val()?.trim() ?? '';
    $this.next(".dynamic-input-field").remove();

    if (!inputValue) {
        $this.after(filedValidationError(result));
        return false;
    }
    let sanitizedValue = inputValue.replace(/\s{2,}/g, ' '); //Restrict Multiple spaces      
    $this?.val(sanitizedValue);

    if (regexURL.test(sanitizedValue)) {
        return true;
    }
    let resultString = result.replace("Enter", "Invalid").trim();
    $this.after(filedValidationError(resultString));
    return false;
}

async function formBuilderSelectFieldValidation($this, modulename) {
    let result = await commonPlaceholder($this);
    let inputValue = $this?.val()?.trim() ?? '';
    let inputValue2 = inputValue?.replace(/\*/g, '')?.replace(/\s+/g, '');

    if (modulename === 'replication') {
        if (inputValue2 && result.toLowerCase().includes('server')) {
            $.ajax({
                method: 'GET',
                url: RootUrl + 'Configuration/Replication/GetByReferenceId',
                data: { id: inputValue2 },
                datatype: "json",
                success: function (result) {
                    if (result.success) {
                        let data = result?.data;
                        let props = JSON.parse(data?.properties);
                        let IPAddress = props?.IpAddress ? props?.IpAddress : "NA";
                        let user = props?.SSHUser || props?.SSHKeyUser || props?.PowerShellUser || "NA";
                        let password = props?.SSHPassword || props?.SSHKeyPassword || props?.PowerShellPassword || "NA";

                        if (data?.siteName?.toLowerCase().includes('pr')) {
                            let PRIpAddress = $(".formeo-render .f-field-group input[type='text']");
                            PRIpAddress?.each(async function () {
                                let $this = $(this);

                                if ($this.is(':visible')) {
                                    let name = $this?.attr('name')?.toLowerCase();

                                    if (name.includes('pripaddress') || name.includes("prmanagementconsoleip")) {
                                        $this.val(IPAddress);
                                        $this.attr('disabled', 'disabled');
                                        $this.css("background", "#D3D3D3");
                                        $this.next(".dynamic-input-field").remove();
                                    }
                                }
                            });

                            let PRUserName = $(".formeo-render .f-field-group input[type='text']");
                            PRUserName?.each(async function () {
                                let $this = $(this);

                                if ($this.is(':visible')) {
                                    let name = $this?.attr('name')?.toLowerCase();

                                    if (name.includes('prusername')) {
                                        $this.val(user);
                                        $this.attr('disabled', 'disabled');
                                        $this.css("background", "#D3D3D3");
                                        $this.next(".dynamic-input-field").remove();
                                    }
                                }
                            });

                            let PRPassword = $(".formeo-render .f-field-group input[type='password']");
                            PRPassword?.each(async function () {
                                let $this = $(this);

                                if ($this.is(':visible')) {
                                    let name = $this?.attr('name')?.toLowerCase();

                                    if (name.includes('prpassword')) {
                                        $this.val(password);
                                        $this.attr('disabled', 'disabled');
                                        $this.css("background", "#D3D3D3");
                                        $this.next(".dynamic-input-field").remove();
                                    }
                                }
                            });
                        }
                        if (data?.siteName?.toLowerCase().includes('dr')) {
                            let DRIpAddress = $(".formeo-render .f-field-group input[type='text']");
                            DRIpAddress?.each(async function () {
                                let $this = $(this);

                                if ($this.is(':visible')) {
                                    let name = $this?.attr('name')?.toLowerCase();

                                    if (name.includes('dripaddress') || name.includes("drmanagementconsoleip")) {
                                        $this.val(IPAddress);
                                        $this.attr('disabled', 'disabled');
                                        $this.css("background", "#D3D3D3");
                                        $this.next(".dynamic-input-field").remove();
                                    }
                                }
                            });

                            let DRUserName = $(".formeo-render .f-field-group input[type='text']");
                            DRUserName?.each(async function () {
                                let $this = $(this);

                                if ($this.is(':visible')) {
                                    let name = $this?.attr('name')?.toLowerCase();

                                    if (name.includes('drusername')) {
                                        $this.val(user);
                                        $this.attr('disabled', 'disabled');
                                        $this.css("background", "#D3D3D3");
                                        $this.next(".dynamic-input-field").remove();
                                    }
                                }
                            });

                            let DRPassword = $(".formeo-render .f-field-group input[type='password']");
                            DRPassword?.each(async function () {
                                let $this = $(this);

                                if ($this.is(':visible')) {
                                    let name = $this?.attr('name')?.toLowerCase();

                                    if (name.includes('drpassword')) {
                                        $this.val(password);
                                        $this.attr('disabled', 'disabled');
                                        $this.css("background", "#D3D3D3");
                                        $this.next(".dynamic-input-field").remove();
                                    }
                                }
                            });
                        }
                    }
                    else {
                        errorNotification(result);
                    }
                }
            })
        }
    }
    let value = "";

    if (Array.isArray(inputValue2) && inputValue2?.length) {
        value = inputValue2.filter(item => item !== '')[0];
    } else if (typeof inputValue2 === 'string') {
        value = inputValue2;
        if (value && value?.toLowerCase()?.includes('select')) value = ""
    }

    $this.next(".dynamic-select-tag").remove();
    $this.next(".dynamic-input-field").remove();

    if (!value.trim()) {
        $this.after(`<div class='dynamic-select-tag field-validation-error-selecttag'>
                        <span class='required-field-msg'>${result}</span>
                    </div>`);
        return false;
    } else {
        return true;
    }
}

async function inputRequired(modulename) { // [type='text']
    let validationResults = [];

    let requiredInputs = $(".formeo-render .f-field-group input[required]")
        .filter("[input-type='text'], [type='number']")
        .not("[minlength], [restrict]");

    if (requiredInputs?.length > 0) {
        await Promise.all(requiredInputs.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                validationResults.push(await formBuilderTextValidation($this, modulename));
            }
        }));
    } else {
        validationResults.push(true);
    }

    let restrictInputs = $(".formeo-render .f-field-group input[restrict]")
        .not("[required], [minlength]");

    if (restrictInputs?.length > 0) {
        await Promise.all(restrictInputs.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                let inputValue = $this.val();
                $this.next(".dynamic-input-field").remove();

                if (inputValue && !pattern.test(inputValue)) {
                    $this.next(".dynamic-input-field").remove();
                    $this.after(filedValidationError("Only & _ @ . - special characters allowed"));
                    validationResults.push(false);
                } else {
                    validationResults.push(true);
                }
            }
        }));
    } else {
        validationResults.push(true);
    }

    let minlengthMaxlength = $(".formeo-render .f-field-group input[minlength][maxlength]")
        .not("[required], [restrict]");

    if (minlengthMaxlength?.length > 0) {
        await Promise.all(minlengthMaxlength.map(async function () {
            let $this = $(this);
            let minLength = $(this).attr("minLength");
            let maxLength = $(this).attr("maxLength");

            if ($this.is(':visible')) {
                let inputVal = this.value;
                $this.next(".dynamic-input-field").remove();

                if (inputVal.includes('<')) {
                    $this.after(filedValidationError('Special characters not allowed'));
                    validationResults.push(false);
                    return;
                }
                if (inputVal.length > 0) {
                    $this.next(".dynamic-input-field").remove();

                    if (!minLength && !maxLength || minLength > 0 && maxLength > 0) {
                        if (inputVal.length < minLength || inputVal.length > maxLength) {
                            $this.after(filedValidationError(`Between ${minLength} to ${maxLength} characters`));
                            validationResults.push(false);
                        } else {
                            $this.next(".dynamic-input-field").remove();
                            validationResults.push(true);
                        }
                    }
                } else {
                    $this.next(".dynamic-input-field").remove();
                    validationResults.push(true); //because it's not req it's minlength, maxlength only.
                }
            }
        }));
    } else {
        validationResults.push(true);
    }

    let restrictRequiredNotMinlengthInputs = $(".formeo-render .f-field-group input[restrict][required]")
        .not("[minlength]");

    if (restrictRequiredNotMinlengthInputs?.length > 0) {
        await Promise.all(restrictRequiredNotMinlengthInputs.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                let inputVal = $this?.val()?.trim() ?? '';

                if (inputVal) {
                    if (!pattern.test(inputVal)) {
                        $this.next(".dynamic-input-field").remove();
                        $this.after(filedValidationError("Only & _ @ . - special characters allowed"));
                        validationResults.push(false);
                    } else {
                        validationResults.push(await formBuilderTextValidation($this, modulename));
                    }
                } else {
                    $this.next(".dynamic-input-field").remove();
                    validationResults.push(false);
                }

            }
        }));
    } else {
        validationResults.push(true);
    }

    let restrictMinLengthNotRequiredInputs = $(".formeo-render .f-field-group input[restrict][minlength][maxlength]")
        .not("[required]");

    if (restrictMinLengthNotRequiredInputs?.length > 0) {
        await Promise.all(restrictMinLengthNotRequiredInputs.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                $this.next(".dynamic-input-field").remove();
                let inputVal = this.value;
                let minLength = $(this)?.attr("minLength");
                let maxLength = $(this)?.attr("maxLength");

                if (inputVal) {
                    if (!pattern.test(inputVal)) {
                        $this.after(filedValidationError("Only & _ @ . - special characters allowed"));
                        validationResults.push(false);
                    }
                    else {
                        if (inputVal.length > 0) {
                            if (!minLength && !maxLength || minLength > 0 && maxLength > 0) {
                                if (inputVal.length < minLength || inputVal.length > maxLength) {
                                    $this.after(filedValidationError(`Between ${minLength} to ${maxLength} characters`));
                                    validationResults.push(false);
                                } else {
                                    $this.next(".dynamic-input-field").remove();
                                    validationResults.push(true);
                                }
                            }
                        }
                    }
                } else {
                    validationResults.push(true); //because it's not req it's restrict, minlength, maxlength only.
                }
            }
        }));
    } else {
        validationResults.push(true);
    }

    let requiredPath = $(".formeo-render .f-field-group input[required][input-type='path']");

    if (requiredPath?.length > 0) {
        await Promise.all(requiredPath.map(async function () {
            let $this = $(this);
            let minLength = $this?.attr("minLength");
            let maxLength = $this?.attr("maxLength");
            let inputVal = $this?.val()?.trim() ?? '';

            if ($this.is(':visible')) {
                let fieldValidationResult = await formBuilderRequiredPathValidation($this, modulename);
                validationResults.push(fieldValidationResult);

                if (fieldValidationResult) {
                    if (inputVal?.length > 0) {
                        if (!minLength && !maxLength || minLength > 0 && maxLength > 0) {
                            if (inputVal.length < minLength || inputVal.length > maxLength) {
                                $this.after(filedValidationError(`Between ${minLength} to ${maxLength} characters`));
                                validationResults.push(false);
                            } else {
                                $this.next(".dynamic-input-field").remove();
                                validationResults.push(true);
                            }
                        }
                    }
                }
            }
        }));
    } else {
        validationResults.push(true);
    }

    let requiredMinLengthNotRestrict = $(".formeo-render .f-field-group input[required][minlength][maxlength]")
        .not("[restrict], [input-type='path']");

    if (requiredMinLengthNotRestrict?.length > 0) {
        await Promise.all(requiredMinLengthNotRestrict.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                let inputVal = this.value;
                let minLength = $this?.attr("minLength");
                let maxLength = $this?.attr("maxLength");
                $this.next(".dynamic-input-field").remove();

                if (!inputVal) {
                    validationResults.push(await formBuilderTextValidation($this, modulename));
                    return;
                }

                if (inputVal.includes('<')) {
                    $this.after(filedValidationError('Special characters not allowed'));
                    validationResults.push(false);
                    return;
                }

                if (!minLength && !maxLength || minLength > 0 && maxLength > 0) {
                    if (inputVal.length < minLength || inputVal.length > maxLength) {
                        $this.after(filedValidationError(`Between ${minLength} to ${maxLength} characters`));
                        validationResults.push(false);
                    } else {
                        validationResults.push(true);
                    }
                }
            }
        }));
    } else {
        validationResults.push(true);
    }

    let requiredMinLengthRestrict = $(".formeo-render .f-field-group input[required][minlength][restrict][maxlength]");

    if (requiredMinLengthRestrict?.length > 0) {
        await Promise.all(requiredMinLengthRestrict.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                let inputVal = this.value;
                let minLength = $(this)?.attr("minLength");
                let maxLength = $(this)?.attr("maxLength");
                $this.next(".dynamic-input-field").remove();

                if (!inputVal) {
                    validationResults.push(await formBuilderTextValidation($this, modulename));
                    return;
                }

                if (!pattern.test(inputVal)) {
                    $this.after(filedValidationError("Only & _ @ . - special characters allowed"));
                    validationResults.push(false);
                } else if (!minLength && !maxLength || minLength > 0 && maxLength > 0) {

                    if (inputVal.length < minLength || inputVal.length > maxLength) {
                        $this.after(filedValidationError(`Between ${minLength} to ${maxLength} characters`));
                        validationResults.push(false);
                    } else {
                        $this.next(".dynamic-input-field").remove();
                        validationResults.push(true);
                    }
                }
            }
        }));
    } else {
        validationResults.push(true);
    }

    let requiredURLInput = $(".formeo-render .f-field-group input[required][attrid='URLField']");

    if (requiredURLInput?.length > 0) {
        await Promise.all(requiredURLInput.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                validationResults.push(await formBuilderRequiredURLValidation($this));
            }
        }));
    } else {
        validationResults.push(true);
    }

    let requiredpassword = $(".formeo-render .f-field-group input[required][type='password']");

    if (requiredpassword?.length > 0) {
        await Promise.all(requiredpassword.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                validationResults.push(PasswordValidation($($this)?.attr('placeholder'), $this, 'onsave'));
            }
        }));
    } else {
        validationResults.push(true);
    }

    let restrictReqInputs = $(".formeo-render .f-field-group input[required][type='date']");

    if (restrictReqInputs?.length > 0) {
        await Promise.all(restrictReqInputs.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                validationResults.push(dateFieldValidation(this.value, $this, 'onsave'));
            }
        }));
    } else {
        validationResults.push(true);
    }

    let requiredInputsTextArea = $(".formeo-render .f-field-group textarea[required]");

    if (requiredInputsTextArea?.length > 0) {
        await Promise.all(requiredInputsTextArea.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                validationResults.push(textAreaFieldValidation($this, 'onsave'));
            }
        }));
    } else {
        validationResults.push(true);
    }

    let requiredInputsIpAddress = $(".formeo-render .f-field-group input[required][attrid='ipAddressField']");

    if (requiredInputsIpAddress?.length > 0) {
        await Promise.all(requiredInputsIpAddress.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                validationResults.push(await formBuilderIPAddressValidation($this));
            }
        }));
    } else {
        validationResults.push(true);
    }

    let selectInput = $(".formeo-render .f-field-group select[required]")
        .not("[multiple]");

    if (selectInput?.length > 0) {
        await Promise.all(selectInput.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                validationResults.push(await formBuilderSelectFieldValidation($this, modulename));
            }
        }));
    } else {
        validationResults.push(true);
    }

    let selectInput2 = $(".formeo-render .f-field-group select[multiple][required]");

    if (selectInput2?.length > 0) {
        await Promise.all(selectInput2.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                let placeholder = $($this)?.attr('placeholder');
                let result;

                if (placeholder) {
                    result = placeholder?.charAt(0)?.toUpperCase() + placeholder?.slice(1)?.toLowerCase();
                } else {
                    let labelText = "nodes";
                    result = labelText?.toLowerCase();
                }
                let inputValue = $this?.val()?.filter(item => item !== '');
                let value;

                if (Array?.isArray(inputValue)) {
                    if (inputValue?.length > 0) {
                        value = "Not empty";
                    } else {
                        value = "";
                    }
                }
                if (typeof inputValue === 'string') {
                    value = $this?.val();
                    if (value && value?.toLowerCase()?.replace(/\*/g, '')?.includes('select')) value = ""
                }
                $this.next(".dynamic-select-tag").remove();

                if (value?.length === 0 || value === "") {
                    $this.after(`<div class='dynamic-select-tag field-validation-error-selecttag2'>
                                    <span class='required-field-msg'>${result}</span>
                                  </div>`);
                    validationResults.push(false);
                } else {
                    validationResults.push(true);
                }
            }
        }));
    } else {
        validationResults.push(true);
    }
    return validationResults.every(result => result === true);
};


function PasswordValidation(placeholderValue, $this, validation) {
    const customCasing = { esxi: 'ESXi' };
    let result;

    if (placeholderValue) {
        let firstLetterCapital = placeholderValue?.charAt(0)?.toUpperCase() + placeholderValue?.slice(1)?.toLowerCase();
        result = firstLetterCapital?.replace(abbrevationLettersForValidation, function (match) {
            return customCasing[match.toLowerCase()] || match.toUpperCase();
        });
    } else {
        let associatedLabel = $('label[for="' + $this.attr('id') + '"]');
        let labelText = associatedLabel?.text()?.replace(/\*/g, '');
        result = labelText?.toLowerCase();
    }
    let passwordValue = $this?.val();
    $this.next(".dynamic-input-field").remove();

    if (passwordValue === "") {
        $this.after(filedValidationError(result));

        if (validation === 'onsave') {
            return false;
        }
    } else {
        $this.next(".dynamic-input-field").remove();

        if (validation === 'onsave') {
            return true;
        }
    }
}

function dateFieldValidation(value, $this, validation) {
    let inputValue = value;
    $this.next(".dynamic-input-field").remove();

    if (!inputValue) {
        $this.after(filedValidationError("Select date"));

        if (validation === 'onsave') {
            return false;
        }
    } else {
        $this.next(".dynamic-input-field").remove();

        if (validation === 'onsave') {
            return true;
        }
    }
}

function textAreaFieldValidation($this, validation) {
    let inputValue = $this?.val()?.trim() ?? '';
    $this.next(".dynamic-input-field").remove();

    if (!inputValue) {
        $this.after(`<div style="margin-top: -5px" class="dynamic-input-field field-validation-error">
                        <span class="required-field-msg">Enter text area</span>
                      </div>`);
        if (validation === 'onsave') {
            return false;
        }
    } else {
        $this.next(".dynamic-input-field").remove();

        if (validation === 'onsave') {
            return true;
        }
    }
}

async function requiredSudoSuTable() {
    let requiredSudoSuTable = $("table[required]");

    if (requiredSudoSuTable?.length > 0) {
        let validationResults = [];
        await Promise.all(requiredSudoSuTable.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                var name = $(this).attr("name");

                if (name.toLowerCase().includes("sudo")) {
                    let type = await SubstituteAuthenticationType();
                    let user = await SubstituteAuthenticationUser();

                    if (type && user) {
                        validationResults.push(true);
                    } else {
                        validationResults.push(false);
                    }
                } else {
                    validationResults.push(true);
                }
            }
        }));
        return validationResults.every(result => result === true);
    } else {
        return true;
    }
}

async function SubstituteAuthenticationType() {
    let selectInput = $("select[name='SubstituteAuthenticationType']");

    if (selectInput?.length > 0) {
        const validationResults = await Promise.all(selectInput.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                let inputValue = $this?.val()?.replace(/\s/g, "");
                let placeholder = $this?.attr('data-placeholder')?.charAt(0)?.toUpperCase() + $this?.attr('data-placeholder')?.slice(1)?.toLowerCase();
                $this.next(".dynamic-input-field").remove();

                if (!inputValue) {
                    $this.after(`<div class="dynamic-input-field table-select-field-validation-error">
                                    <span class="required-field-msg">${placeholder}</span>
                                  </div>`);
                    return false;
                }
                return true;
            }
            return true;
        }));
        return validationResults.every(result => result);
    }
    return true;
}

async function tableFieldValidation(requiredInputs) {
    const validationResults = await Promise.all(requiredInputs.map(async function () {
        let $this = $(this);

        if ($this.is(':visible')) {
            let inputValue = $this?.val()?.trim() ?? '';
            let placeholder = $this?.attr('placeholder')?.charAt(0)?.toUpperCase() + $this?.attr('placeholder')?.slice(1)?.toLowerCase();
            $this.next(".dynamic-input-field").remove();

            if (!inputValue) {
                $this.after(`<div class="dynamic-input-field table-field-validation-error">
                                <span class="required-field-msg">${placeholder}</span>
                              </div>`);
                return false;
            }
            return true;
        }
        return true;

    }));
    return validationResults.every(result => result);
}

async function SubstituteAuthenticationUser() {
    let requiredInputs = $("input[name='SubstituteAuthenticationUser']");

    if (requiredInputs?.length > 0) {
        return await tableFieldValidation(requiredInputs);
    }
    return true;
}

async function zfsTableValidation() {
    let requiredTableField = $("input[requiredTableTextField]");

    if (requiredTableField?.length > 0) {
        let validationResults = [];
        await Promise.all(requiredTableField.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                let secondDiv = $this.closest('.input-field-error');
                validationResults.push(await zfstableFieldValidation($this, secondDiv));
            }
        }));
        return validationResults.every(result => result === true);
    }
    return true;
}

async function zfstableFieldValidation(requiredInputs, selectDiv) {
    const validationResults = await Promise.all(requiredInputs.map(async function () {
        let $this = $(this);

        if ($this.is(':visible')) {
            let placeholder = $this?.attr('placeholder')?.charAt(0)?.toUpperCase() + $this?.attr('placeholder')?.slice(1)?.toLowerCase();
            let inputValue = $this?.val()?.trim() ?? '';
            selectDiv.next(".field-validation-error").remove();

            if (!inputValue) {
                selectDiv.after("<span class='field-validation-error'> " + placeholder + "</span>");
                return false;
            }
            return true;
        }
        return true;
    }));
    return validationResults.every(result => result);
}

async function zfsTableSelectValidation() {
    let requiredTableField = $("select[requiredtableselectfield]");

    if (requiredTableField?.length > 0) {
        let validationResults = [];
        await Promise.all(requiredTableField.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                let secondDiv = $this.closest('.select-field-error');
                validationResults.push(await zfstableFieldValidation($this, secondDiv));
            }
        }));
        return validationResults.every(result => result === true);
    }
    return true;
}

async function sourceDestinationValidation(className, error, replType) {
    let requiredTextField = $(className);

    if (requiredTextField?.length > 0) {
        let validateSourceDir = [];
        await Promise.all(requiredTextField.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                let value = $this?.val()?.trim() ?? '';
                let $sourceFieldErrorSpan = $this.closest('.form-group').find(error);
                let placeHolder = $this.attr('placeholder').charAt(0).toUpperCase() + $this.attr('placeholder').slice(1).toLowerCase();
                $sourceFieldErrorSpan.text("").removeClass("field-validation-error");

                if (value) {
                    if (replType.trim().toLowerCase().includes("rsync") ||
                        replType.trim().toLowerCase().includes("robocopy")) {

                        if (rsyncRobocopyPathForm.test(value)) {
                            validateSourceDir.push(true);
                        } else {
                            $sourceFieldErrorSpan.text("Enter valid path").addClass("field-validation-error");
                            validateSourceDir.push(false);
                        }
                    } else {
                        validateSourceDir.push(true);
                    }

                } else {
                    validateSourceDir.push(false);
                    $sourceFieldErrorSpan.text(placeHolder).addClass("field-validation-error");
                }
            }
        }));
        return validateSourceDir.every(result => result === true);
    }
    return true;
}

async function propertiesSelectTag(propertyValue, error) {
    let requiredSelectField = $(propertyValue);

    if (requiredSelectField?.length > 0) {
        let validateProperty = [];
        await Promise.all(requiredSelectField.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                let value = $this?.val()?.trim() ?? '';
                let $propertyFieldErrorSpan = $this.closest('.form-group').find(error);
                let placeHolder = $this.attr('placeholder').charAt(0).toUpperCase() + $this.attr('placeholder').slice(1).toLowerCase();
                $propertyFieldErrorSpan.text("").removeClass("field-validation-error");

                if (value) {
                    validateProperty.push(true);
                } else {
                    validateProperty.push(false);
                    $propertyFieldErrorSpan.text(placeHolder).addClass("field-validation-error");
                }
            }
        }));
        return validateProperty.every(result => result === true);
    }
    return true;
}

async function lunsTableValidation() {
    let requiredLunsTableTextField = $("input[requiredLunsTableTextField]");

    if (requiredLunsTableTextField?.length > 0) {
        let validationResults = [];
        await Promise.all(requiredLunsTableTextField.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                let secondDiv = $this.closest('.input-field-error');
                validationResults.push(await lunsTableFieldValidation(requiredLunsTableTextField, secondDiv));
            }
        }));
        return validationResults.every(result => result === true);;
    }
    return true;
}

async function lunsTableFieldValidation(requiredInputs) {
    const validationResults = await Promise.all(requiredInputs.map(async function () {
        let $this = $(this);
        let secondDiv = $this.closest('.input-field-error');

        if ($this.is(':visible')) {
            let placeHolder = $this?.attr('placeholder')?.charAt(0)?.toUpperCase() + $this?.attr('placeholder')?.slice(1)?.toLowerCase();
            let inputValue = $this?.val()?.trim() ?? '';
            secondDiv?.next(".field-validation-error").remove();

            if (!inputValue) {
                secondDiv?.after("<span class='field-validation-error'> " + placeHolder + "</span>");
                return false;
            }

            if (/^[A-F0-9]+$/.test(inputValue)) {
                return true;
            } else {
                secondDiv?.after("<span class='field-validation-error'> " + "A-F & 0-9" + "</span>");
                return false;
            }
        }
        return true;
    }));
    return validationResults.every(result => result);
}

//For Node.js source, target archieve log path
async function requiredsourcetargetPath() {
    let requiredsourcetargetPath = $(".formeo-render .f-field-group input[input-type='path']").not("[required]");

    if (requiredsourcetargetPath?.length > 0) {
        let validationResults = [];
        await Promise.all(requiredsourcetargetPath.map(async function () {
            let $this = $(this);

            if ($this.is(':visible')) {
                let name = $this.attr("name").toLowerCase();

                if (name.toLowerCase().includes("sourcearchivelogpath") || name.toLowerCase().includes("targetarchivelogpath")) {
                    let value = $this?.val()?.trim() ?? '';

                    if (value && !regex.test(value)) {
                        $this.after(filedValidationError("Enter valid path"));
                        validationResults.push(false);
                    } else {
                        $this.next(".dynamic-input-field").remove();
                        validationResults.push(true); // when optional
                    }
                }
            }
        }));
        return validationResults.every(result => result === true);
    }
    return true;
}

async function inputFormValidation(modulename, replType) {
    let inputReq = await inputRequired(modulename);
    //let sudoSuTable = true;
    let validateZfsTable = true;
    let selectValidationZfsTable = true;
    let validatelunsTable = true;
    let validateSourceTargetPath = true;
    let validateRsyncSource = true;
    let validateRsyncDestination = true;
    let validateRsyncProprty = true;

    if (modulename === 'replication') {
        validateZfsTable = await zfsTableValidation();
        selectValidationZfsTable = await zfsTableSelectValidation();
        validatelunsTable = await lunsTableValidation();

        if (replType === "rsync" || replType === "robocopy") {
            validateRsyncSource = await sourceDestinationValidation("input.sourceDirectory", '.sourceFieldError', replType);
            validateRsyncDestination = await sourceDestinationValidation("input.destinationDirectory", '.destinationFieldError', replType);
            validateRsyncProprty = await propertiesSelectTag('select[class^="replicationProperties"]', '.propertyError');
        }
    }
    //don't delete important.
    //if (modulename === 'server') {
    //    sudoSuTable = await requiredSudoSuTable(); 
    //}
    if (modulename === "node") {
        validateSourceTargetPath = await requiredsourcetargetPath();
    }
    if (inputReq && validateZfsTable && selectValidationZfsTable && validatelunsTable &&
        validateSourceTargetPath && validateRsyncSource && validateRsyncDestination && validateRsyncProprty) {
        return true;
    } else {
        return false;
    }
}
