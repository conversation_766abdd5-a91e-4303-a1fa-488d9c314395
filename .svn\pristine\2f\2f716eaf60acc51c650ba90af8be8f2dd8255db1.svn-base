﻿using ContinuityPatrol.Application.Features.User.Events.Delete;

namespace ContinuityPatrol.Application.Features.User.Commands.Delete;

public class DeleteUserCommandHandler : IRequestHandler<DeleteUserCommand, DeleteUserResponse>
{
    private readonly IPublisher _publisher;
    private readonly IUserInfoRepository _userInfoRepository;
    private readonly IUserInfraObjectRepository _userInfraObjectRepository;
    private readonly IUserRepository _userRepository;
    private readonly IReportScheduleRepository _reportScheduleRepository; 
    private readonly IWorkflowPermissionRepository _workflowPermissionRepository;
    private readonly IUserViewRepository _userViewRepository;
    private readonly ILogger<DeleteUserCommandHandler> _logger;
    private readonly IUserGroupRepository _userGroupRepository;

    public DeleteUserCommandHandler(IUserRepository userRepository, IUserInfoRepository userInfoRepository,IUserGroupRepository userGroupRepository,
        IUserInfraObjectRepository userInfraObjectRepository, IPublisher publisher,IReportScheduleRepository reportScheduleRepository,
        IWorkflowPermissionRepository workflowPermissionRepository,ILogger<DeleteUserCommandHandler> logger, IUserViewRepository userViewRepository)
    {
        _publisher = publisher;
        _userRepository = userRepository;
        _userInfoRepository = userInfoRepository;
        _userInfraObjectRepository = userInfraObjectRepository;
        _reportScheduleRepository= reportScheduleRepository;
        _workflowPermissionRepository = workflowPermissionRepository;
        _logger = logger;
        _userViewRepository = userViewRepository;
        _userGroupRepository = userGroupRepository;
    }

    public async Task<DeleteUserResponse> Handle(DeleteUserCommand request, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, "User Id");

        var eventToDeleteUser = await _userRepository.GetByReferenceIdAsync(request.Id);

        var isLoggedIn = await _userViewRepository.IsUserLoggedInAsync(request.Id);

        if (isLoggedIn)
        {
            _logger.LogInformation($"The user '{eventToDeleteUser.LoginName}' is currently logged in.");

            throw new InvalidException($"The User '{eventToDeleteUser.LoginName}' is currently logged in.");
        }

        var reportScheduler = await _reportScheduleRepository.GetReportSchedulerByUserGroupId(request.Id);

        var workflowPermission = await _workflowPermissionRepository.GetWorkflowPermissionByUserIdAsync(request.Id);
        var userGroup = await _userGroupRepository.GetUserGroupByUserId(request.Id);
        if (reportScheduler.Count > 0 || workflowPermission.Count>0 || userGroup.Count >0)
        {
            var loggerDtl = reportScheduler.Count > 0 && workflowPermission.Count > 0 ? "Scheduler report and Workflow user privileges " 
                :reportScheduler.Count > 0 ? "Scheduler report" 
                : "Workflow user privileges";

            _logger.LogInformation($"The user '{eventToDeleteUser.LoginName}' is currently being used in {loggerDtl}.");

            throw new InvalidException($"The User '{eventToDeleteUser.LoginName}' is currently in use.");
        }

        var eventToDeleteUserInfo = await _userInfoRepository.GetUserInfoByUserIdAsync(request.Id);

        var eventToUpdateUserInfraObject = await _userInfraObjectRepository.GetUserInfraObjectByUserIdAsync(request.Id);

        if (eventToDeleteUser == null && eventToDeleteUserInfo == null)
            throw new NotFoundException(nameof(Domain.Entities.User), request.Id);

        if (eventToDeleteUserInfo != null)
            eventToDeleteUserInfo.IsActive = false;
        await _userInfoRepository.UpdateAsync(eventToDeleteUserInfo);

        if (eventToDeleteUser != null)
            eventToDeleteUser.IsActive = false;
        await _userRepository.UpdateAsync(eventToDeleteUser);

        if (eventToUpdateUserInfraObject != null)
            eventToUpdateUserInfraObject.IsActive = false;
        await _userInfraObjectRepository.UpdateAsync(eventToUpdateUserInfraObject);

        var response = new DeleteUserResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.User), eventToDeleteUser?.LoginName),
            IsActive = eventToDeleteUser?.IsActive ?? false
        };

        await _publisher.Publish(new UserDeletedEvent { UserName = eventToDeleteUser?.LoginName }, cancellationToken);

        return response;
    }
}