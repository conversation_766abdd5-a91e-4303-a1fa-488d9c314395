﻿namespace ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetDetail;

public class
    GetDashboardViewLogDetailQueryHandler : IRequestHandler<GetDashboardViewLogDetailQuery, DashboardViewLogDetailVm>
{
    private readonly IDashboardViewLogRepository _dashboardViewLogRepository;
    private readonly IMapper _mapper;

    public GetDashboardViewLogDetailQueryHandler(IMapper mapper, IDashboardViewLogRepository dashboardViewLogRepository)
    {
        _mapper = mapper;
        _dashboardViewLogRepository = dashboardViewLogRepository;
    }

    public async Task<DashboardViewLogDetailVm> Handle(GetDashboardViewLogDetailQuery request,
        CancellationToken cancellationToken)
    {
        var dataLagLog = await _dashboardViewLogRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(dataLagLog, nameof(Domain.Entities.DashboardViewLog),
            new NotFoundException(nameof(Domain.Entities.DashboardViewLog), request.Id));

        var dataLagLogDetailDto = _mapper.Map<DashboardViewLogDetailVm>(dataLagLog);

        return dataLagLogDetailDto ?? throw new NotFoundException(nameof(Domain.Entities.DashboardViewLog), request.Id);
    }
}