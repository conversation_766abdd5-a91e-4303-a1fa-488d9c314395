﻿namespace ContinuityPatrol.Application.Features.DRReadyLog.Commands.Create;

public class CreateDRReadyLogCommand : IRequest<CreateDRReadyLogResponse>
{
    public string UserId { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string IsProtected { get; set; }
    public string AffectedInfra { get; set; }
    public string ActiveInfra { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string WorkflowStatus { get; set; }
    public string FailedActionName { get; set; }
    public string FailedActionId { get; set; }
    public string ActiveBusinessFunction { get; set; }
    public string AffectedBusinessFunction { get; set; }
    public string DRReady { get; set; }
    public string NotReady { get; set; }
    public string WorkflowAttach { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string ComponentName { get; set; }
    public string Type { get; set; }
    public string ErrorMessage { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }

    public override string ToString()
    {
        return $"Business Service Name: {BusinessServiceName};";
    }
}