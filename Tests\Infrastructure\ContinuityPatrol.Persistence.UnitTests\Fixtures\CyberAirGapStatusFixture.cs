using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class CyberAirGapStatusFixture : IDisposable
{
    public const string CompanyId = "550e8400-e29b-41d4-a716-446655440000";
    public const string ParentCompanyId = "550e8400-e29b-41d4-a716-446655440001";
    public const string AirGapId = "AIRGAP_STATUS_001";
    public const string ParentAirGapId = "PARENT_AIRGAP_STATUS_001";

    public List<CyberAirGapStatus> CyberAirGapStatusPaginationList { get; set; }
    public List<CyberAirGapStatus> CyberAirGapStatusList { get; set; }
    public CyberAirGapStatus CyberAirGapStatusDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public CyberAirGapStatusFixture()
    {
        var fixture = new Fixture();

        CyberAirGapStatusList = fixture.Create<List<CyberAirGapStatus>>();

        CyberAirGapStatusPaginationList = fixture.CreateMany<CyberAirGapStatus>(20).ToList();

    
        CyberAirGapStatusDto = fixture.Create<CyberAirGapStatus>();

        CyberAirGapStatusDto.ReferenceId = Guid.NewGuid().ToString();
        CyberAirGapStatusDto.IsActive = true;
        CyberAirGapStatusDto.AirGapId = AirGapId;
        CyberAirGapStatusDto.AirGapName = "TestAirGapStatus";
        CyberAirGapStatusDto.Status = "Online";
        CyberAirGapStatusDto.CreatedDate = DateTime.Now;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
