using ContinuityPatrol.Application.Features.BiaRules.Commands.Create;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Update;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BiaRulesModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class BiaRulesFixture
{
    public List<BiaRulesListVm> BiaRulesListVm { get; }
    public BiaRulesDetailVm BiaRulesDetailVm { get; }
    public CreateBiaRulesCommand CreateBiaRulesCommand { get; }
    public UpdateBiaRulesCommand UpdateBiaRulesCommand { get; }

    public BiaRulesFixture()
    {
        var fixture = new Fixture();

        // Create sample BiaRules list data
        BiaRulesListVm = new List<BiaRulesListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Description = "Critical Business Process Recovery Rule",
                EntityId = Guid.NewGuid().ToString(),
                Type = "Recovery",
                Properties = "{\"priority\":\"critical\",\"rto\":\"4hours\",\"rpo\":\"1hour\"}",
                EffectiveDateFrom = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd"),
                EffectiveDateTo = DateTime.Now.AddDays(335).ToString("yyyy-MM-dd"),
                IsEffective = true,
                RuleCode = "BIA-CRIT-001"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Description = "Financial System Continuity Rule",
                EntityId = Guid.NewGuid().ToString(),
                Type = "Continuity",
                Properties = "{\"priority\":\"high\",\"rto\":\"2hours\",\"rpo\":\"30minutes\"}",
                EffectiveDateFrom = DateTime.Now.AddDays(-60).ToString("yyyy-MM-dd"),
                EffectiveDateTo = DateTime.Now.AddDays(305).ToString("yyyy-MM-dd"),
                IsEffective = true,
                RuleCode = "BIA-FIN-002"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Description = "IT Infrastructure Backup Rule",
                EntityId = Guid.NewGuid().ToString(),
                Type = "Backup",
                Properties = "{\"priority\":\"medium\",\"rto\":\"8hours\",\"rpo\":\"4hours\"}",
                EffectiveDateFrom = DateTime.Now.AddDays(-15).ToString("yyyy-MM-dd"),
                EffectiveDateTo = DateTime.Now.AddDays(350).ToString("yyyy-MM-dd"),
                IsEffective = false,
                RuleCode = "BIA-IT-003"
            }
        };

        // Create detailed BiaRules data
        BiaRulesDetailVm = new BiaRulesDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            Description = "Enterprise-wide Business Impact Analysis Rule for Mission Critical Operations",
            EntityId = Guid.NewGuid().ToString(),
            Type = "Enterprise",
            Properties = "{\"priority\":\"mission_critical\",\"rto\":\"1hour\",\"rpo\":\"15minutes\",\"dependencies\":[\"power\",\"network\",\"database\"],\"escalation\":\"immediate\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-90).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(275).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA-ENT-001"
        };

        // Create command for creating BiaRules
        CreateBiaRulesCommand = new CreateBiaRulesCommand
        {
            Description = "New Business Impact Analysis Rule for Customer Service Operations",
            Type = "Service",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"priority\":\"high\",\"rto\":\"6hours\",\"rpo\":\"2hours\",\"department\":\"customer_service\"}",
            EffectiveDateFrom = DateTime.Now.ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(365).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA-CS-004"
        };

        // Create command for updating BiaRules
        UpdateBiaRulesCommand = new UpdateBiaRulesCommand
        {
            Id = Guid.NewGuid().ToString(),
            Description = "Updated Business Impact Analysis Rule for Supply Chain Management",
            EntityId = Guid.NewGuid().ToString(),
            Type = "Supply Chain",
            Properties = "{\"priority\":\"medium\",\"rto\":\"12hours\",\"rpo\":\"6hours\",\"suppliers\":[\"primary\",\"secondary\"]}",
            EffectiveDateFrom = DateTime.Now.AddDays(-10).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(355).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA-SC-005"
        };
    }
}
