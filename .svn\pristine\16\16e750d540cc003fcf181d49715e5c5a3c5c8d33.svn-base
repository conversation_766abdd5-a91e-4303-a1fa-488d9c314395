﻿using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;

namespace ContinuityPatrol.Shared.Tests.Infrastructure;

public static class DbContextFactory
{
    public static ApplicationDbContext CreateInMemoryDbContext(string dbName = null)
    {

        dbName ??= Guid.NewGuid().ToString();

        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: dbName)
            .Options;

        return new ApplicationDbContext(options, GetMockUserService());
    }


    public static ILoggedInUserService GetMockUserService()
    {
        var mock = new Mock<ILoggedInUserService>();
        mock.Setup(x => x.CompanyId).Returns("COMPANY_123");
        mock.Setup(x => x.UserId).Returns("USER_456");
        mock.Setup(x => x.IsParent).Returns(true);
        mock.Setup(x => x.IsAllInfra).Returns(true);
        mock.Setup(x => x.IsAuthenticated).Returns(true);

        return mock.Object;
    }
    public static ILoggedInUserService GetMockLoggedInUserIsNotParent()
    {
        var mock = new Mock<ILoggedInUserService>();

        var assigndinfra = System.Text.Json.JsonSerializer.Serialize(GetAssignedEntity());
        mock.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mock.Setup(x => x.UserId).Returns("USER_Child1245");
        mock.Setup(x => x.IsParent).Returns(false);
        mock.Setup(x => x.AssignedInfras).Returns(assigndinfra);
        mock.Setup(x => x.IsAuthenticated).Returns(true);
        return mock.Object;
    }

    public static AssignedEntity GetAssignedEntity()
    {
        return new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
        {
            new AssignedBusinessServices
            {
                Id = "BS1",
                Name = "Service 1",
                IsAll = false,
                IsPartial = false,
                AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                {
                    new AssignedBusinessFunctions
                    {
                        Id = "BF1",
                        Name = "Function 1",
                        IsAll = false,
                        IsPartial = false,
                        AssignedInfraObjects = new List<AssignedInfraObjects>
                        {
                            new AssignedInfraObjects { Id = "INFRA_1", Name = "Infra 1", IsSelected = true },
                            new AssignedInfraObjects { Id = "INFRA_2", Name = "Infra 2", IsSelected = true }
                        }
                    }
                }
            }
        }
        };
    }


}