﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;


namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowProfileInfoViewRepository : BaseRepository<WorkflowProfileInfoView>, IWorkflowProfileInfoViewRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IWorkflowRepository _workflowRepository;
    public WorkflowProfileInfoViewRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService, IWorkflowRepository workflowRepository) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
        _workflowRepository = workflowRepository;
    }

    public async Task<List<WorkflowProfileInfoView>> GetWorkflowProfileInfoByProfileIds(List<string> profileIds)
    {
        var workflowProfiles = SelectToWorkflowProfileInfo(_loggedInUserService.IsParent
            ? base.FilterBy(workflow => profileIds.Contains(workflow.ProfileId))
            : base.FilterBy(workflow => profileIds.Contains(workflow.ProfileId) && workflow.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await workflowProfiles.ToListAsync()
            : AssignedInfraObjects(workflowProfiles).ToList();
    }

    public async Task<(bool IsRunning,string ProgressBar)> IsRunningWorkflow(string workflowId)
    {
        var workflowProfile = _loggedInUserService.IsParent
            ? await base.FilterBy(workflow => workflow.WorkflowId.Equals(workflowId) && workflow.IsRunning)
                .Select(x => new { x.IsRunning, x.ProgressBar })
                .FirstOrDefaultAsync()
            : await base.FilterBy(workflow => workflow.WorkflowId.Equals(workflowId) && workflow.IsRunning && workflow.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new { x.IsRunning, x.ProgressBar })
                .FirstOrDefaultAsync();

        // Return the values, ensuring null safety
        return workflowProfile != null
            ? (workflowProfile.IsRunning, workflowProfile.ProgressBar)
            : (false, null);
    }

    public async Task<List<WorkflowProfileInfoView>> GetRunningProfileByProfileIds(List<string> profileIds)
    {
        var workflowProfile = _loggedInUserService.IsParent
            ? await base.FilterBy(workflow => profileIds.Contains(workflow.ProfileId) && workflow.IsRunning)
                .Select(x => new WorkflowProfileInfoView  { IsRunning = x.IsRunning, ProgressBar = x.ProgressBar,ProfileId = x.ProfileId })
                .ToListAsync()
            : await base.FilterBy(workflow => profileIds.Contains(workflow.ProfileId) && workflow.IsRunning && workflow.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new WorkflowProfileInfoView { IsRunning = x.IsRunning, ProgressBar = x.ProgressBar, ProfileId = x.ProfileId })
                .ToListAsync();

        // Return the values, ensuring null safety
        return workflowProfile;
    }



    public async Task<List<WorkflowProfileInfoView>> GetWorkflowProfileInfoViewByProfileIdandWorkflowIds(string profileId,List<string> workflowIds)
    {
        var workflowProfiles = SelectToWorkflowProfileInfo(_loggedInUserService.IsParent
            ? base.FilterBy(workflow => profileId.Equals(workflow.ProfileId) && workflowIds.Contains(workflow.WorkflowId))
            : base.FilterBy(workflow => profileId.Equals(workflow.ProfileId) && workflowIds.Contains(workflow.WorkflowId) && workflow.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await workflowProfiles.ToListAsync()
            : AssignedInfraObjects(workflowProfiles).ToList();
    }

    private IQueryable<WorkflowProfileInfoView> SelectToWorkflowProfileInfo(IQueryable<WorkflowProfileInfoView> query)
    {
        return query.Select(x => new WorkflowProfileInfoView
        {
            ReferenceId = x.ReferenceId,
            CompanyId = x.CompanyId,
            ProfileId = x.ProfileId,
            ProfileName = x.ProfileName,
            Status = x.Status,
            InfraObjectId = x.InfraObjectId,
            InfraObjectName = x.InfraObjectName,
            BusinessFunctionId = x.BusinessFunctionId,
            BusinessFunctionName = x.BusinessFunctionName,
            BusinessServiceId = x.BusinessServiceId,
            BusinessServiceName = x.BusinessServiceName,
            WorkflowId = x.WorkflowId,
            WorkflowName = x.WorkflowName,
            CurrentActionId = x.CurrentActionId,
            CurrentActionName = x.CurrentActionName,
            Message = x.Message,
            ProgressStatus = x.ProgressStatus,
            ConditionalOperation = x.ConditionalOperation,
            WorkflowType = x.WorkflowType,
            ActionMode = x.ActionMode,
            IsLock = x.IsLock,
            IsPublish = x.IsPublish,
            IsRunning = x.IsRunning,
            IsParallel = x.IsParallel,
            TotalCount = x.TotalCount,
            State = x.State,
            WorkflowVersion = x.WorkflowVersion
        });
    }


    private IReadOnlyList<WorkflowProfileInfoView> AssignedInfraObjects(IQueryable<WorkflowProfileInfoView> infraObjects)
    {
        var infraObjectList = new List<WorkflowProfileInfoView>();

        var assignedBusinessFunctions = new List<AssignedBusinessFunctions>();

        if (AssignedEntity.AssignedBusinessServices.Count > 0)
            foreach (var assignedBusinessServices in AssignedEntity.AssignedBusinessServices)
                assignedBusinessFunctions.AddRange(assignedBusinessServices.AssignedBusinessFunctions);

        var assignedBusinessInfraObjects = new List<AssignedInfraObjects>();

        if (assignedBusinessFunctions.Count > 0)
            foreach (var assignedBusinessFunction in assignedBusinessFunctions)
                assignedBusinessInfraObjects.AddRange(assignedBusinessFunction.AssignedInfraObjects);

        foreach (var infraObject in infraObjects)
            if (assignedBusinessInfraObjects.Count > 0)
                infraObjectList.AddRange(from assignedInfraObject in assignedBusinessInfraObjects
                    where infraObject.InfraObjectId == assignedInfraObject.Id
                    select infraObject);
        return infraObjectList;
    }

    public async Task<List<WorkflowProfileInfoView>> GetWorkflowProfileInfoNames()
    {
        var workflowPermission = await _workflowRepository.GetWorkflowPermissions("profile");
        var profileInfos = _loggedInUserService.IsAllInfra
             ? base.QueryAll(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
              .Select(x => new WorkflowProfileInfoView
              {
                  ReferenceId = x.ReferenceId,
                  ProfileName = x.ProfileName,
                  ProfileId = x.ProfileId,
                  BusinessServiceId = x.BusinessServiceId
              }).ToList()
              : AssignedInfraObjects(base.QueryAll(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)))
              .Select(x => new WorkflowProfileInfoView
              {
                  ReferenceId = x.ReferenceId,
                  ProfileName = x.ProfileName,
                  ProfileId = x.ProfileId,
                  BusinessServiceId = x.BusinessServiceId
              }).ToList();
        return workflowPermission.Count > 0
        ? profileInfos.Concat(await base.FindByFilterAsync(x => workflowPermission.Contains(x.ReferenceId))).DistinctBy(x => x.ReferenceId).ToList()
        : profileInfos;
    }
}
