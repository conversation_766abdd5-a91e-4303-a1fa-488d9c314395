using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.Database.Commands.Create;
using ContinuityPatrol.Application.Features.Database.Commands.Delete;
using ContinuityPatrol.Application.Features.Database.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Database.Commands.TestConnection;
using ContinuityPatrol.Application.Features.Database.Commands.Update;
using ContinuityPatrol.Application.Features.Database.Commands.UpdateBulkPassword;
using ContinuityPatrol.Application.Features.Database.Commands.UpdateVersion;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByLicenseKey;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByServerId;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByType;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByUserName;
using ContinuityPatrol.Application.Features.Database.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Database.Queries.GetList;
using ContinuityPatrol.Application.Features.Database.Queries.GetNames;
using ContinuityPatrol.Application.Features.Database.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Database.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Database.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DatabaseControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DatabasesController _controller;
    private readonly DatabaseFixture _databaseFixture;

    public DatabaseControllerTests()
    {
        _databaseFixture = new DatabaseFixture();

        var testBuilder = new ControllerTestBuilder<DatabasesController>();
        _controller = testBuilder.CreateController(
            _ => new DatabasesController(),
            out _mediatorMock);
    }

    #region Core CRUD Operations

    [Fact]
    public async Task CreateDatabase_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _databaseFixture.CreateDatabaseCommand;
        var expectedResponse = _databaseFixture.CreateDatabaseResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDatabase(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDatabaseResponse>(createdResult.Value);
        Assert.Equal("Enterprise Production Database created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.DatabaseId);
    }

    [Fact]
    public async Task UpdateDatabase_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _databaseFixture.UpdateDatabaseCommand;
        var expectedResponse = _databaseFixture.UpdateDatabaseResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDatabase(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDatabaseResponse>(okResult.Value);
        Assert.Equal("Enterprise Database updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.DatabaseId);
    }

    [Fact]
    public async Task DeleteDatabase_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var databaseId = Guid.NewGuid().ToString();
        var expectedResponse = _databaseFixture.DeleteDatabaseResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDatabaseCommand>(c => c.Id == databaseId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDatabase(databaseId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDatabaseResponse>(okResult.Value);
        Assert.Equal("Enterprise Database deleted successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.False(returnedResponse.IsActive);
    }

    [Fact]
    public async Task GetDatabases_ReturnsOkResult()
    {
        // Arrange
        var databaseList = new List<DatabaseListVm> { _databaseFixture.DatabaseListVm };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDatabaseListQuery>(), default))
            .ReturnsAsync(databaseList);

        // Act
        var result = await _controller.GetDatabases();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DatabaseListVm>>(okResult.Value);
        Assert.Single(returnedList);
        Assert.Equal("Enterprise Database List Item", returnedList.First().Name);
    }

    [Fact]
    public async Task GetDatabaseDetail_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var databaseId = Guid.NewGuid().ToString();
        var expectedDetail = _databaseFixture.DatabaseDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDatabaseDetailQuery>(q => q.Id == databaseId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDatabaseById(databaseId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DatabaseDetailVm>(okResult.Value);
        Assert.Equal("Enterprise Production Database Detail", returnedDetail.Name);
        Assert.Equal("SQL Server", returnedDetail.DatabaseType);
    }

    #endregion

    #region Database Operations

    [Fact]
    public async Task SaveAsDatabase_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _databaseFixture.SaveAsDatabaseCommand;
        var expectedResponse = _databaseFixture.SaveAsDatabaseResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.SaveAsDatabase(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<SaveAsDatabaseResponse>(createdResult.Value);
        Assert.Equal("Database saved as copy successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task DatabaseTestConnection_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _databaseFixture.DatabaseTestConnectionCommand;
        var expectedResponse = _databaseFixture.DatabaseTestConnectionResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DatabaseTestConnection(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<DatabaseTestConnectionResponse>(createdResult.Value);
        Assert.Equal("Database connection test successful!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDatabasePassword_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _databaseFixture.UpdateDatabasePasswordCommand;
        var expectedResponse = _databaseFixture.UpdateDatabasePasswordResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDatabasePassword(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDatabasePasswordResponse>(okResult.Value);
        Assert.Equal("Database passwords updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
    }

    [Fact]
    public async Task UpdateDatabaseVersion_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _databaseFixture.UpdateDatabaseVersionCommand;
        var expectedResponse = _databaseFixture.UpdateDatabaseVersionResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDatabaseFormVersion(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDatabaseVersionResponse>(okResult.Value);
        Assert.Equal("Database versions updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
    }

    #endregion

    #region Query Operations

    [Fact]
    public async Task GetDatabaseByType_WithValidTypeId_ReturnsOkResult()
    {
        // Arrange
        var databaseTypeId = Guid.NewGuid().ToString();
        var databaseTypes = new List<DatabaseTypeVm> { _databaseFixture.DatabaseTypeVm };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDatabaseTypeQuery>(q => q.TypeId == databaseTypeId), default))
            .ReturnsAsync(databaseTypes);

        // Act
        var result = await _controller.GetDatabaseByType(databaseTypeId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedTypes = Assert.IsType<List<DatabaseTypeVm>>(okResult.Value);
        Assert.Single(returnedTypes);
        Assert.Equal("SQL Server Enterprise", returnedTypes.First().Name);
    }

    [Fact]
    public async Task GetDatabasesByType_WithValidType_ReturnsOkResult()
    {
        // Arrange
        var databaseType = "SQL Server";
        var databasesByType = new List<GetDatabaseByTypeVm> { _databaseFixture.GetDatabaseByTypeVm };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDatabaseByTypeQuery>(q => q.Type == databaseType), default))
            .ReturnsAsync(databasesByType);

        // Act
        var result = await _controller.GetDatabasesByType(databaseType);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDatabases = Assert.IsType<List<GetDatabaseByTypeVm>>(okResult.Value);
        Assert.Single(returnedDatabases);
        Assert.Equal("Enterprise Database by Type", returnedDatabases.First().Name);
    }

    [Fact]
    public async Task GetDatabaseNames_ReturnsOkResult()
    {
        // Arrange
        var databaseNames = new List<DatabaseNameVm> { _databaseFixture.DatabaseNameVm };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDatabaseNameQuery>(), default))
            .ReturnsAsync(databaseNames);

        // Act
        var result = await _controller.GetDatabaseNames();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedNames = Assert.IsType<List<DatabaseNameVm>>(okResult.Value);
        Assert.Single(returnedNames);
        Assert.Equal("Enterprise Database Name", returnedNames.First().Name);
    }

    [Fact]
    public async Task GetPaginatedDatabases_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _databaseFixture.GetDatabasePaginatedListQuery;
        var paginatedResult = new PaginatedResult<DatabaseListVm>
        {
            Data = new List<DatabaseListVm> { _databaseFixture.DatabaseListVm },
            TotalCount = 1,
            PageSize = 10,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedDatabases(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DatabaseListVm>>(okResult.Value);
        Assert.Single(returnedResult.Data);
        Assert.Equal("Enterprise Database List Item", returnedResult.Data.First().Name);
        Assert.True(returnedResult.Succeeded);
    }

    [Fact]
    public async Task GetDatabaseByServerId_WithValidServerId_ReturnsOkResult()
    {
        // Arrange
        var serverId = Guid.NewGuid().ToString();
        var databasesByServer = new List<GetDatabaseByServerIdVm> { _databaseFixture.GetDatabaseByServerIdVm };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDatabaseByServerIdQuery>(q => q.ServerId == serverId), default))
            .ReturnsAsync(databasesByServer);

        // Act
        var result = await _controller.GetDatabaseByServerId(serverId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<GetDatabaseByServerIdVm>>(okResult.Value);
        Assert.Single(returnedList);
        Assert.Equal("Database by Server", returnedList.First().Name);
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public async Task CreateDatabase_CallsClearDataCache()
    {
        // Arrange
        var command = _databaseFixture.CreateDatabaseCommand;
        var expectedResponse = _databaseFixture.CreateDatabaseResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.CreateDatabase(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task UpdateDatabase_CallsClearDataCache()
    {
        // Arrange
        var command = _databaseFixture.UpdateDatabaseCommand;
        var expectedResponse = _databaseFixture.UpdateDatabaseResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.UpdateDatabase(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    #endregion

   

    [Fact]
    public async Task CreateDatabase_HandlesEnterpriseScaleDatabase()
    {
        // Arrange
        var enterpriseCommand = new CreateDatabaseCommand
        {
            Name = "Enterprise Global Data Warehouse",
            DatabaseTypeId = Guid.NewGuid().ToString(),
            DatabaseType = "SQL Server Enterprise",
            Type = "Production",
            ServerId = Guid.NewGuid().ToString(),
            ServerName = "GLOBAL-SQL-CLUSTER-01",
            CompanyId = Guid.NewGuid().ToString(),
            Properties = "{\"connectionString\":\"Server=GLOBAL-SQL-CLUSTER-01;Database=GlobalDataWarehouse;Integrated Security=true;MultipleActiveResultSets=true;Connection Timeout=300;\",\"maxPoolSize\":1000,\"commandTimeout\":600}",
            Logo = "sql-server-enterprise-logo.png",
            ModeType = "Production",
            LicenseId = Guid.NewGuid().ToString(),
            LicenseKey = "ENTERPRISE-GLOBAL-LICENSE-KEY-2024",
            Version = "2022 Enterprise",
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Global Enterprise Data Services",
            ExceptionMessage = "",
            FormVersion = "2.0"
        };

        var expectedResponse = new CreateDatabaseResponse
        {
            DatabaseId = Guid.NewGuid().ToString(),
            Message = "Enterprise Global Data Warehouse created successfully!",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(enterpriseCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDatabase(enterpriseCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDatabaseResponse>(createdResult.Value);

        Assert.Equal("Enterprise Global Data Warehouse created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);

        // Validate enterprise scale properties
        Assert.Equal("Enterprise Global Data Warehouse", enterpriseCommand.Name);
        Assert.Equal("SQL Server Enterprise", enterpriseCommand.DatabaseType);
        Assert.Equal("GLOBAL-SQL-CLUSTER-01", enterpriseCommand.ServerName);
        Assert.Contains("maxPoolSize", enterpriseCommand.Properties);
        Assert.Contains("commandTimeout", enterpriseCommand.Properties);
    }

    [Fact]
    public async Task UpdateDatabase_HandlesHighAvailabilityConfiguration()
    {
        // Arrange
        var haCommand = new UpdateDatabaseCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Critical HA Database Cluster",
            DatabaseTypeId = Guid.NewGuid().ToString(),
            DatabaseType = "SQL Server Always On",
            Type = "Production",
            ServerId = Guid.NewGuid().ToString(),
            ServerName = "HA-SQL-CLUSTER-PRIMARY",
            Properties = "{\"connectionString\":\"Server=HA-SQL-CLUSTER-PRIMARY,HA-SQL-CLUSTER-SECONDARY;Database=CriticalDB;Integrated Security=true;ApplicationIntent=ReadWrite;MultiSubnetFailover=true;\",\"alwaysOnAvailabilityGroup\":\"CriticalAG\",\"failoverPartner\":\"HA-SQL-CLUSTER-SECONDARY\"}",
            Logo = "sql-server-ha-logo.png",
            ModeType = "Production",
            LicenseId = Guid.NewGuid().ToString(),
            LicenseKey = "HA-ENTERPRISE-LICENSE-2024",
            Version = "2022 Enterprise",
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Critical High Availability Services",
            ExceptionMessage = "",
            FormVersion = "2.1"
        };

        var expectedResponse = new UpdateDatabaseResponse
        {
            DatabaseId = haCommand.Id,
            Message = "Critical HA Database Cluster updated successfully!",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(haCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDatabase(haCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDatabaseResponse>(okResult.Value);

        Assert.Equal("Critical HA Database Cluster updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);

        // Validate high availability configuration
        Assert.Equal("SQL Server Always On", haCommand.DatabaseType);
        Assert.Contains("MultiSubnetFailover", haCommand.Properties);
        Assert.Contains("alwaysOnAvailabilityGroup", haCommand.Properties);
        Assert.Contains("failoverPartner", haCommand.Properties);
    }

    [Fact]
    public async Task GetDatabaseByLicenseKey_HandlesMultipleLicenseTypes()
    {
        // Arrange
        var licenseId = Guid.NewGuid().ToString();
        var databasesByLicense = new List<GetDatabaseByLicenseKeyListVm>
        {
            new GetDatabaseByLicenseKeyListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Production DB",
                LicenseId = licenseId,
                DatabaseType = "SQL Server Enterprise"
            },
            new GetDatabaseByLicenseKeyListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Analytics DB",
                LicenseId = licenseId,
                DatabaseType = "SQL Server Enterprise"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDatabaseByLicenseKeyQuery>(q => q.LicenseId == licenseId), default))
            .ReturnsAsync(databasesByLicense);

        // Act
        var result = await _controller.GetDatabaseByLicenseKey(licenseId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<GetDatabaseByLicenseKeyListVm>>(okResult.Value);

        Assert.Equal(2, returnedList.Count);
        Assert.All(returnedList, db => Assert.Equal(licenseId, db.LicenseId));
        Assert.All(returnedList, db => Assert.Equal("SQL Server Enterprise", db.DatabaseType));
    }

    [Fact]
    public async Task GetDatabaseByUserName_HandlesUserAccessControl()
    {
        // Arrange
        var userName = "enterprise_dba_admin";
        var databaseType = "SQL Server";
        var databasesByUser = new List<GetDatabaseByUserNameVm>
        {
            new GetDatabaseByUserNameVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "User Accessible Production DB",
                UserName = userName,
                DatabaseType = "SQL Server Enterprise"
            },
            new GetDatabaseByUserNameVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "User Accessible Analytics DB",
                UserName = userName,
                DatabaseType = "SQL Server Enterprise"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDatabaseByUserNameQuery>(q => q.UserName == userName && q.DatabaseTypeId == databaseType), default))
            .ReturnsAsync(databasesByUser);

        // Act
        var result = await _controller.GetDatabaseByUserName(userName, databaseType);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<GetDatabaseByUserNameVm>>(okResult.Value);

        Assert.Equal(2, returnedList.Count);
        Assert.All(returnedList, db => Assert.Equal(userName, db.UserName));
        Assert.All(returnedList, db => Assert.Equal("SQL Server Enterprise", db.DatabaseType));
    }

    [Fact]
    public async Task IsDatabaseNameExist_ValidatesUniqueness()
    {
        // Arrange
        var databaseName = "Unique Enterprise Database";
        var databaseId = Guid.NewGuid().ToString();
        var existsResult = false; // false means name is unique

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDatabaseNameUniqueQuery>(q => q.DatabaseName == databaseName && q.DatabaseId == databaseId), default))
            .ReturnsAsync(existsResult);

        // Act
        var result = await _controller.IsDatabaseNameExist(databaseName, databaseId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedResult = Assert.IsType<bool>(okResult.Value);

        Assert.False(returnedResult); // false means name is unique
    }

    [Fact]
    public async Task DatabaseTestConnection_HandlesMultipleDatabaseConnections()
    {
        // Arrange
        var multiDbCommand = new DatabaseTestConnectionCommand
        {
            Id = new List<string>
            {
                Guid.NewGuid().ToString(),
                Guid.NewGuid().ToString(),
                Guid.NewGuid().ToString()
            }
        };

        var expectedResponse = new DatabaseTestConnectionResponse
        {
            Id = string.Join(",", multiDbCommand.Id),
            Message = "Multiple database connections tested successfully!",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(multiDbCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DatabaseTestConnection(multiDbCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<DatabaseTestConnectionResponse>(createdResult.Value);

        Assert.Equal("Multiple database connections tested successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.Equal(3, multiDbCommand.Id.Count);
    }

    [Fact]
    public async Task UpdateDatabasePassword_HandlesBulkPasswordUpdate()
    {
        // Arrange
        var bulkPasswordCommand = new UpdateDatabasePasswordCommand
        {
            Password = "NewComplexPassword123!@#",
            PasswordList = new List<UpdateDatabasePasswordList>
            {
                new UpdateDatabasePasswordList
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Enterprise Database 1",
                    UserName = "enterprise_service_account"
                },
                new UpdateDatabasePasswordList
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Enterprise Database 2",
                    UserName = "enterprise_service_account"
                },
                new UpdateDatabasePasswordList
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Enterprise Database 3",
                    UserName = "enterprise_service_account"
                },
                new UpdateDatabasePasswordList
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Enterprise Database 4",
                    UserName = "enterprise_service_account"
                }
            }
        };

        var expectedResponse = new UpdateDatabasePasswordResponse
        {
            Message = "Bulk database password update completed successfully!",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(bulkPasswordCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDatabasePassword(bulkPasswordCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDatabasePasswordResponse>(okResult.Value);

        Assert.Equal("Bulk database password update completed successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.Equal(4, bulkPasswordCommand.PasswordList.Count);
        Assert.All(bulkPasswordCommand.PasswordList, item => Assert.Equal("enterprise_service_account", item.UserName));
    }

    [Fact]
    public async Task UpdateDatabaseVersion_HandlesEnterpriseVersionUpgrade()
    {
        // Arrange
        var versionUpgradeCommand = new UpdateDatabaseVersionCommand
        {
            Id = Guid.NewGuid().ToString(),
            DatabaseTypeId = Guid.NewGuid().ToString(),
            OldFormVersion = "1.0",
            NewFormVersion = "2022 Enterprise SP1 CU5",
            IsUpdateAll = false
        };

        var expectedResponse = new UpdateDatabaseVersionResponse
        {
            Message = "Enterprise database version upgrade completed successfully!",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(versionUpgradeCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDatabaseFormVersion(versionUpgradeCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDatabaseVersionResponse>(okResult.Value);

        Assert.Equal("Enterprise database version upgrade completed successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.Equal("2022 Enterprise SP1 CU5", versionUpgradeCommand.NewFormVersion);
        Assert.Equal("1.0", versionUpgradeCommand.OldFormVersion);
        Assert.False(versionUpgradeCommand.IsUpdateAll);
    }

    [Fact]
    public async Task GetPaginatedDatabases_HandlesComplexFiltering()
    {
        // Arrange
        var complexQuery = new GetDatabasePaginatedListQuery
        {
            DatabaseTypeId = Guid.NewGuid().ToString(),
            SearchString = "Enterprise Production",
            PageNumber = 1,
            PageSize = 10,
            SortColumn = "Name",
            SortOrder = "ASC"
        };

        var filteredDatabases = new PaginatedResult<DatabaseListVm>
        {
            Data = new List<DatabaseListVm>
            {
                new DatabaseListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Enterprise Production Database 1",
                    DatabaseType = "SQL Server Enterprise",
                    Type = "Production",
                    ServerName = "PROD-SQL-01"
                },
                new DatabaseListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Enterprise Production Database 2",
                    DatabaseType = "SQL Server Enterprise",
                    Type = "Production",
                    ServerName = "PROD-SQL-02"
                }
            },
            TotalCount = 2,
            PageSize = 10,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(complexQuery, default))
            .ReturnsAsync(filteredDatabases);

        // Act
        var result = await _controller.GetPaginatedDatabases(complexQuery);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DatabaseListVm>>(okResult.Value);

        Assert.Equal(2, returnedResult.Data.Count);
        Assert.All(returnedResult.Data, db => Assert.Contains("Enterprise Production", db.Name));
        Assert.All(returnedResult.Data, db => Assert.Equal("SQL Server Enterprise", db.DatabaseType));
        Assert.All(returnedResult.Data, db => Assert.Equal("Production", db.Type));
        Assert.True(returnedResult.Succeeded);
    }

   

  

    [Fact]
    public async Task CreateDatabase_HandlesHighAvailabilityConfiguration()
    {
        // Arrange
        var haCommand = _databaseFixture.CreateDatabaseCommand;
        haCommand.Name = "Enterprise High Availability Database";
        haCommand.DatabaseTypeId = "SQL Server Always On";
        haCommand.Properties = @"{
            ""availabilityGroup"": ""AG-Enterprise-Primary"",
            ""replicaMode"": ""SYNCHRONOUS_COMMIT"",
            ""failoverMode"": ""AUTOMATIC"",
            ""backupPreference"": ""SECONDARY_ONLY"",
            ""readOnlyRouting"": true,
            ""connectionTimeout"": 30
        }";

        var expectedResponse = _databaseFixture.CreateDatabaseResponse;

        _mediatorMock
            .Setup(m => m.Send(haCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDatabase(haCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDatabaseResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise High Availability Database", haCommand.Name);
        Assert.Equal("SQL Server Always On", haCommand.DatabaseTypeId);
        Assert.Contains("availabilityGroup", haCommand.Properties);
        Assert.Contains("SYNCHRONOUS_COMMIT", haCommand.Properties);
        Assert.Contains("AUTOMATIC", haCommand.Properties);
    }

    [Fact]
    public async Task UpdateDatabase_HandlesPerformanceTuningConfiguration()
    {
        // Arrange
        var performanceCommand = _databaseFixture.UpdateDatabaseCommand;
        performanceCommand.Name = "Enterprise Performance Optimized Database";
        performanceCommand.Properties = @"{
            ""maxDegreeOfParallelism"": 8,
            ""costThresholdForParallelism"": 50,
            ""maxServerMemory"": 16384,
            ""optimizeForAdHocWorkloads"": true,
            ""pageVerify"": ""CHECKSUM"",
            ""autoUpdateStatistics"": true,
            ""autoCreateStatistics"": true,
            ""indexFillFactor"": 90
        }";

        var expectedResponse = _databaseFixture.UpdateDatabaseResponse;

        _mediatorMock
            .Setup(m => m.Send(performanceCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDatabase(performanceCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDatabaseResponse>(okResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Performance Optimized Database", performanceCommand.Name);
        Assert.Contains("maxDegreeOfParallelism", performanceCommand.Properties);
        Assert.Contains("optimizeForAdHocWorkloads", performanceCommand.Properties);
        Assert.Contains("CHECKSUM", performanceCommand.Properties);
    }

    [Fact]
    public async Task SaveAsDatabase_HandlesTemplateBasedCreation()
    {
        // Arrange
        var templateCommand = _databaseFixture.SaveAsDatabaseCommand;
        templateCommand.Name = "Enterprise Template Based Database";
        templateCommand.DatabaseId = Guid.NewGuid().ToString();

        var expectedResponse = _databaseFixture.SaveAsDatabaseResponse;

        _mediatorMock
            .Setup(m => m.Send(templateCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.SaveAsDatabase(templateCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<SaveAsDatabaseResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Template Based Database", templateCommand.Name);
        Assert.NotNull(templateCommand.DatabaseId);
        Assert.Contains("successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task DatabaseTestConnection_HandlesMultipleDatabaseIds()
    {
        // Arrange
        var multiConnectionCommand = _databaseFixture.DatabaseTestConnectionCommand;
        multiConnectionCommand.Id = new List<string>
        {
            Guid.NewGuid().ToString(),
            Guid.NewGuid().ToString(),
            Guid.NewGuid().ToString()
        };

        var expectedResponse = _databaseFixture.DatabaseTestConnectionResponse;

        _mediatorMock
            .Setup(m => m.Send(multiConnectionCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DatabaseTestConnection(multiConnectionCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<DatabaseTestConnectionResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal(3, multiConnectionCommand.Id.Count);
        Assert.All(multiConnectionCommand.Id, id =>
            Assert.True(Guid.TryParse(id, out _)));
        Assert.Contains("successful", returnedResponse.Message);
    }

    [Fact]
    public async Task UpdateDatabasePassword_HandlesEnterprisePasswordPolicy()
    {
        // Arrange
        var passwordCommand = _databaseFixture.UpdateDatabasePasswordCommand;
        passwordCommand.Password = "NewEnterpriseComplexPassword123!@#";
        passwordCommand.PasswordList = new List<UpdateDatabasePasswordList>
        {
            new UpdateDatabasePasswordList
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Admin Database",
                UserName = "enterprise_admin"
            },
            new UpdateDatabasePasswordList
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise ReadOnly Database",
                UserName = "enterprise_readonly"
            }
        };

        var expectedResponse = _databaseFixture.UpdateDatabasePasswordResponse;

        _mediatorMock
            .Setup(m => m.Send(passwordCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDatabasePassword(passwordCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDatabasePasswordResponse>(okResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal(2, passwordCommand.PasswordList.Count);
        Assert.Contains(passwordCommand.PasswordList, p => p.UserName == "enterprise_admin");
        Assert.Contains(passwordCommand.PasswordList, p => p.UserName == "enterprise_readonly");
        Assert.Contains(passwordCommand.PasswordList, p => p.Name == "Enterprise Admin Database");
        Assert.Contains(passwordCommand.PasswordList, p => p.Name == "Enterprise ReadOnly Database");
        Assert.Equal("NewEnterpriseComplexPassword123!@#", passwordCommand.Password);
    }

   

    [Fact]
    public async Task GetDatabaseByServerId_HandlesMultipleServerDatabases()
    {
        // Arrange
        var serverId = Guid.NewGuid().ToString();
        var primaryDatabase = _databaseFixture.GetDatabaseByServerIdVm;
        primaryDatabase.ServerId = serverId;
        primaryDatabase.ServerName = "PROD-SQL-01";

        var serverDatabases = new List<GetDatabaseByServerIdVm>
        {
            primaryDatabase,
            new GetDatabaseByServerIdVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Secondary Database",
                DatabaseType = "SQL Server Enterprise",
                ServerId = serverId,
                ServerName = "PROD-SQL-01"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDatabaseByServerIdQuery>(q => q.ServerId == serverId), default))
            .ReturnsAsync(serverDatabases);

        // Act
        var result = await _controller.GetDatabaseByServerId(serverId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<GetDatabaseByServerIdVm>>(okResult.Value);

        Assert.Equal(2, returnedList.Count);
        Assert.All(returnedList, db => Assert.Equal(serverId, db.ServerId));
        Assert.All(returnedList, db => Assert.Equal("PROD-SQL-01", db.ServerName));
        Assert.Contains(returnedList, db => db.Name.Contains("Database by Server"));
        Assert.Contains(returnedList, db => db.Name.Contains("Secondary"));
    }

    [Fact]
    public async Task GetDatabaseByLicenseKey_HandlesEnterpriseLicensing()
    {
        // Arrange
        var licenseId = Guid.NewGuid().ToString();
        var primaryDatabase = _databaseFixture.GetDatabaseByLicenseKeyListVm;
        primaryDatabase.LicenseId = licenseId;
        primaryDatabase.DatabaseType = "SQL Server Enterprise";
        primaryDatabase.Name = "Enterprise Licensed Database 1";

        var licensedDatabases = new List<GetDatabaseByLicenseKeyListVm>
        {
            primaryDatabase,
            new GetDatabaseByLicenseKeyListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Licensed Database 2",
                LicenseId = licenseId,
                DatabaseType = "SQL Server Enterprise"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDatabaseByLicenseKeyQuery>(q => q.LicenseId == licenseId), default))
            .ReturnsAsync(licensedDatabases);

        // Act
        var result = await _controller.GetDatabaseByLicenseKey(licenseId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<GetDatabaseByLicenseKeyListVm>>(okResult.Value);

        Assert.Equal(2, returnedList.Count);
        Assert.All(returnedList, db => Assert.Equal(licenseId, db.LicenseId));
        Assert.All(returnedList, db => Assert.Equal("SQL Server Enterprise", db.DatabaseType));
        Assert.All(returnedList, db => Assert.Contains("Enterprise", db.Name));
    }

    [Fact]
    public async Task GetDatabases_HandlesLargeEnterpriseEnvironment()
    {
        // Arrange
        var largeDatabaseList = new List<DatabaseListVm>();
        for (int i = 1; i <= 50; i++)
        {
            largeDatabaseList.Add(new DatabaseListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = $"Enterprise Database {i:D2}",
                DatabaseType = i % 2 == 0 ? "SQL Server Enterprise" : "Oracle Enterprise",
                ServerId = Guid.NewGuid().ToString(),
                ServerName = $"PROD-DB-{i:D2}",
               
            });
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDatabaseListQuery>(), default))
            .ReturnsAsync(largeDatabaseList);

        // Act
        var result = await _controller.GetDatabases();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DatabaseListVm>>(okResult.Value);

        Assert.Equal(50, returnedList.Count);
        Assert.Equal(25, returnedList.Count(db => db.DatabaseType == "SQL Server Enterprise"));
        Assert.Equal(25, returnedList.Count(db => db.DatabaseType == "Oracle Enterprise"));
   
        Assert.All(returnedList, db => Assert.Contains("Enterprise Database", db.Name));
    }

    [Fact]
    public async Task GetDatabaseDetail_HandlesComplexEnterpriseConfiguration()
    {
        // Arrange
        var databaseId = Guid.NewGuid().ToString();
        var complexDetail = _databaseFixture.DatabaseDetailVm;
        complexDetail.Name = "Complex Enterprise Multi-Tier Database";
        complexDetail.Properties = @"{
            ""tier"": ""enterprise"",
            ""environment"": ""production"",
            ""highAvailability"": {
                ""enabled"": true,
                ""replicationMode"": ""synchronous"",
                ""backupStrategy"": ""full_differential_log""
            },
            ""performance"": {
                ""maxConnections"": 1000,
                ""queryTimeout"": 300,
                ""indexOptimization"": true
            },
            ""security"": {
                ""encryptionAtRest"": true,
                ""encryptionInTransit"": true,
                ""auditingEnabled"": true,
                ""complianceLevel"": ""SOX""
            }
        }";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDatabaseDetailQuery>(q => q.Id == databaseId), default))
            .ReturnsAsync(complexDetail);

        // Act
        var result = await _controller.GetDatabaseById(databaseId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DatabaseDetailVm>(okResult.Value);

        Assert.Equal("Complex Enterprise Multi-Tier Database", returnedDetail.Name);
        Assert.Contains("enterprise", returnedDetail.Properties);
        Assert.Contains("highAvailability", returnedDetail.Properties);
        Assert.Contains("synchronous", returnedDetail.Properties);
        Assert.Contains("encryptionAtRest", returnedDetail.Properties);
        Assert.Contains("SOX", returnedDetail.Properties);
    }

    #region Additional Enterprise Test Cases

    [Fact]
    public async Task CreateDatabase_HandlesCloudDatabaseConfiguration()
    {
        // Arrange
        var cloudCommand = _databaseFixture.CreateDatabaseCommand;
        cloudCommand.Name = "Enterprise Azure SQL Database";
        cloudCommand.DatabaseType = "Azure SQL Database";
        cloudCommand.Properties = @"{
            ""cloudProvider"": ""Azure"",
            ""serviceTier"": ""Premium"",
            ""computeSize"": ""P4"",
            ""maxSizeGB"": 1024,
            ""elasticPool"": ""Enterprise-Pool-01"",
            ""geoReplication"": true,
            ""transparentDataEncryption"": true,
            ""advancedThreatProtection"": true
        }";

        var expectedResponse = _databaseFixture.CreateDatabaseResponse;

        _mediatorMock
            .Setup(m => m.Send(cloudCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDatabase(cloudCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDatabaseResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Azure SQL Database", cloudCommand.Name);
        Assert.Equal("Azure SQL Database", cloudCommand.DatabaseType);
        Assert.Contains("Premium", cloudCommand.Properties);
        Assert.Contains("geoReplication", cloudCommand.Properties);
        Assert.Contains("transparentDataEncryption", cloudCommand.Properties);
    }

    [Fact]
    public async Task UpdateDatabase_HandlesDisasterRecoveryConfiguration()
    {
        // Arrange
        var drCommand = _databaseFixture.UpdateDatabaseCommand;
        drCommand.Name = "Enterprise DR Database";
        drCommand.Properties = @"{
            ""disasterRecovery"": {
                ""enabled"": true,
                ""rpoMinutes"": 15,
                ""rtoMinutes"": 60,
                ""backupFrequency"": ""every_4_hours"",
                ""offSiteBackup"": true,
                ""crossRegionReplication"": true
            },
            ""monitoring"": {
                ""alertOnFailure"": true,
                ""healthChecks"": ""continuous"",
                ""performanceBaseline"": true
            }
        }";

        var expectedResponse = _databaseFixture.UpdateDatabaseResponse;

        _mediatorMock
            .Setup(m => m.Send(drCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDatabase(drCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDatabaseResponse>(okResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise DR Database", drCommand.Name);
        Assert.Contains("disasterRecovery", drCommand.Properties);
        Assert.Contains("crossRegionReplication", drCommand.Properties);
        Assert.Contains("alertOnFailure", drCommand.Properties);
    }

    [Fact]
    public async Task SaveAsDatabase_HandlesDataMaskingConfiguration()
    {
        // Arrange
        var maskingCommand = _databaseFixture.SaveAsDatabaseCommand;
        maskingCommand.Name = "Enterprise Data Masked Database";
        maskingCommand.DatabaseId = Guid.NewGuid().ToString();

        var expectedResponse = _databaseFixture.SaveAsDatabaseResponse;

        _mediatorMock
            .Setup(m => m.Send(maskingCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.SaveAsDatabase(maskingCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<SaveAsDatabaseResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Data Masked Database", maskingCommand.Name);
        Assert.NotNull( maskingCommand.DatabaseId);
       
    }

    [Fact]
    public async Task UpdateDatabaseVersion_HandlesRollbackScenario()
    {
        // Arrange
        var rollbackCommand = _databaseFixture.UpdateDatabaseVersionCommand;
        rollbackCommand.OldFormVersion = "2022 Enterprise";
        rollbackCommand.NewFormVersion = "2019 Enterprise SP3";
        rollbackCommand.IsUpdateAll = true;

        var expectedResponse = _databaseFixture.UpdateDatabaseVersionResponse;

        _mediatorMock
            .Setup(m => m.Send(rollbackCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDatabaseFormVersion(rollbackCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDatabaseVersionResponse>(okResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("2022 Enterprise", rollbackCommand.OldFormVersion);
        Assert.Equal("2019 Enterprise SP3", rollbackCommand.NewFormVersion);
        Assert.True(rollbackCommand.IsUpdateAll);
    }

    [Fact]
    public async Task GetDatabasesByType_HandlesNoSQLDatabases()
    {
        // Arrange
        var databaseType = "MongoDB";
        var noSqlDatabases = new List<GetDatabaseByTypeVm>
        {
            new GetDatabaseByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise MongoDB Cluster",
                DatabaseType = "MongoDB",
                Type = "Production",
                ServerId = Guid.NewGuid().ToString(),
                ServerName = "MONGO-CLUSTER-01"
            },
            new GetDatabaseByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise MongoDB Analytics",
                DatabaseType = "MongoDB",
                Type = "Analytics",
                ServerId = Guid.NewGuid().ToString(),
                ServerName = "MONGO-ANALYTICS-01"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDatabaseByTypeQuery>(q => q.Type == databaseType), default))
            .ReturnsAsync(noSqlDatabases);

        // Act
        var result = await _controller.GetDatabasesByType(databaseType);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDatabases = Assert.IsType<List<GetDatabaseByTypeVm>>(okResult.Value);

        Assert.Equal(2, returnedDatabases.Count);
        Assert.All(returnedDatabases, db => Assert.Equal("MongoDB", db.DatabaseType));
        Assert.Contains(returnedDatabases, db => db.Type == "Production");
        Assert.Contains(returnedDatabases, db => db.Type == "Analytics");
    }

    [Fact]
    public async Task DatabaseTestConnection_HandlesConnectionPooling()
    {
        // Arrange
        var poolingCommand = _databaseFixture.DatabaseTestConnectionCommand;
        poolingCommand.Id = new List<string>
        {
            Guid.NewGuid().ToString(),
            Guid.NewGuid().ToString()
        };

        var expectedResponse = _databaseFixture.DatabaseTestConnectionResponse;
        expectedResponse.Message = "Connection pooling test completed successfully!";

        _mediatorMock
            .Setup(m => m.Send(poolingCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DatabaseTestConnection(poolingCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<DatabaseTestConnectionResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal(2, poolingCommand.Id.Count);
        Assert.Contains("pooling", returnedResponse.Message);
    }

    [Fact]
    public async Task GetDatabaseNames_HandlesFilteredResults()
    {
        // Arrange
        var filteredNames = new List<DatabaseNameVm>
        {
            new DatabaseNameVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Production Database"
            },
            new DatabaseNameVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Analytics Database"
            },
            new DatabaseNameVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Reporting Database"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDatabaseNameQuery>(), default))
            .ReturnsAsync(filteredNames);

        // Act
        var result = await _controller.GetDatabaseNames();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedNames = Assert.IsType<List<DatabaseNameVm>>(okResult.Value);

        Assert.Equal(3, returnedNames.Count);
        Assert.All(returnedNames, name => Assert.StartsWith("Enterprise", name.Name));
        Assert.Contains(returnedNames, name => name.Name.Contains("Production"));
        Assert.Contains(returnedNames, name => name.Name.Contains("Analytics"));
        Assert.Contains(returnedNames, name => name.Name.Contains("Reporting"));
    }

    [Fact]
    public async Task IsDatabaseNameExist_HandlesNameConflict()
    {
        // Arrange
        var conflictingName = "Existing Enterprise Database";
        var databaseId = Guid.NewGuid().ToString();
        var nameExistsResult = true; // true means name already exists

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDatabaseNameUniqueQuery>(q =>
                q.DatabaseName == conflictingName && q.DatabaseId == databaseId), default))
            .ReturnsAsync(nameExistsResult);

        // Act
        var result = await _controller.IsDatabaseNameExist(conflictingName, databaseId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedResult = Assert.IsType<bool>(okResult.Value);

        Assert.True(returnedResult); // true means name conflict exists
    }

    [Fact]
    public async Task GetDatabaseByUserName_HandlesMultipleUserAccess()
    {
        // Arrange
        var userName = "enterprise_multi_access_user";
        var databaseType = "SQL Server";
        var primaryDatabase = _databaseFixture.GetDatabaseByUserNameVm;
        primaryDatabase.UserName = userName;
        primaryDatabase.Name = "Enterprise Multi-Access Database Primary";

        var userAccessDatabases = new List<GetDatabaseByUserNameVm>
        {
            primaryDatabase,
            new GetDatabaseByUserNameVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Multi-Access Database 1",
                UserName = userName,
                DatabaseType = "SQL Server Enterprise"
            },
            new GetDatabaseByUserNameVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Multi-Access Database 2",
                UserName = userName,
                DatabaseType = "SQL Server Enterprise"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDatabaseByUserNameQuery>(q =>
                q.UserName == userName && q.DatabaseTypeId == databaseType), default))
            .ReturnsAsync(userAccessDatabases);

        // Act
        var result = await _controller.GetDatabaseByUserName(userName, databaseType);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<GetDatabaseByUserNameVm>>(okResult.Value);

        Assert.Equal(3, returnedList.Count);
        Assert.All(returnedList, db => Assert.Equal(userName, db.UserName));
        Assert.All(returnedList, db => Assert.Contains("Enterprise", db.Name));
    }

    [Fact]
    public async Task UpdateDatabasePassword_HandlesServiceAccountRotation()
    {
        // Arrange
        var serviceAccountCommand = _databaseFixture.UpdateDatabasePasswordCommand;
        serviceAccountCommand.Password = "RotatedServiceAccountPassword2024!";
        serviceAccountCommand.PasswordList = new List<UpdateDatabasePasswordList>
        {
            new UpdateDatabasePasswordList
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Service Database 1",
                UserName = "svc_enterprise_app"
            },
            new UpdateDatabasePasswordList
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Service Database 2",
                UserName = "svc_enterprise_app"
            },
            new UpdateDatabasePasswordList
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Service Database 3",
                UserName = "svc_enterprise_app"
            }
        };

        var expectedResponse = _databaseFixture.UpdateDatabasePasswordResponse;

        _mediatorMock
            .Setup(m => m.Send(serviceAccountCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDatabasePassword(serviceAccountCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDatabasePasswordResponse>(okResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal(3, serviceAccountCommand.PasswordList.Count);
        Assert.All(serviceAccountCommand.PasswordList, item =>
            Assert.Equal("svc_enterprise_app", item.UserName));
        Assert.Equal("RotatedServiceAccountPassword2024!", serviceAccountCommand.Password);
    }

    #endregion
}
