using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberComponentMappingModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Cyber;

public class CyberComponentMappingService : BaseClient, ICyberComponentMappingService
{
    public CyberComponentMappingService(IConfiguration config, IAppCache cache, ILogger<CyberComponentMappingService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<CyberComponentMappingListVm>> GetCyberComponentMappingList()
    {
        var request = new RestRequest("api/v6/cybercomponentmappings");

        return await GetFromCache<List<CyberComponentMappingListVm>>(request, "GetCyberComponentMappingList");
    }

    public async Task<BaseResponse> CreateAsync(CreateCyberComponentMappingCommand createCyberComponentMappingCommand)
    {
        var request = new RestRequest("api/v6/cybercomponentmappings", Method.Post);

        request.AddJsonBody(createCyberComponentMappingCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateCyberComponentMappingCommand updateCyberComponentMappingCommand)
    {
        var request = new RestRequest("api/v6/cybercomponentmappings", Method.Put);

        request.AddJsonBody(updateCyberComponentMappingCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/cybercomponentmappings/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<CyberComponentMappingDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/cybercomponentmappings/{id}");

        return await Get<CyberComponentMappingDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsCyberComponentMappingNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/cybercomponentmappings/name-exist?cybercomponentmappingName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<CyberComponentMappingListVm>> GetPaginatedCyberComponentMappings(GetCyberComponentMappingPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/cybercomponentmappings/paginated-list");

      return await Get<PaginatedResult<CyberComponentMappingListVm>>(request);
  }
   #endregion
}
