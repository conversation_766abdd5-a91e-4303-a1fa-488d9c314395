﻿namespace ContinuityPatrol.Application.Features.WorkflowProfile.Commands.WorkflowProfileAuthentication;

public class WorkflowProfileAuthenticationCommandHandler : IRequestHandler<WorkflowProfileAuthenticationCommand,
    WorkflowProfileAuthenticationResponse>
{
    private readonly IUserRepository _userRepository;

    public WorkflowProfileAuthenticationCommandHandler(IUserRepository userRepository)
    {
        _userRepository = userRepository;
    }

    public async Task<WorkflowProfileAuthenticationResponse> Handle(WorkflowProfileAuthenticationCommand request,
        CancellationToken cancellationToken)
    {
        var user = await _userRepository.FindByLoginNameAsync(request.LoginName);

        Guard.Against.Null(user, nameof(Domain.Entities.User), new InvalidException("Invalid User Credential."));

        var isValid = request.AuthenticationType.Trim().ToLower() == "ad"
            ? await _userRepository.IsValidActiveDirectoryUser(request.LoginName,
                SecurityHelper.Decrypt(request.Password))
            : SecurityHelper.Decrypt(user.LoginPassword).Equals(SecurityHelper.Decrypt(request.Password));

        var response = new WorkflowProfileAuthenticationResponse
        {
            Message = "Authenticated Successfully",
            Success = true
        };
        if (!isValid)
        {
            response.Success = false;
            response.Message = "Invalid Credential";
        }

        return response;
    }
}