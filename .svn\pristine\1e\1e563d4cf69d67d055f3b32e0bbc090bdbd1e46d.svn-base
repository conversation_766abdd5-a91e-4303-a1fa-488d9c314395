﻿@model ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels.UserViewModel
<div class="modal-dialog modal-lg  modal-dialog-scrollabel modal-dialog-centered Organization_modal wizard-sticky-header">
    <div class="modal-content">
        <div class="modal-header" id="step1">
            <h6 class="page_title">
                <i class="cp-user"></i><span>
                    Users
                    Configuration
                </span>
            </h6>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
        </div>
        <div class="modal-body py-0">
            <div class="wizard-content" id="userModalWizard">
                <form id="example-form" asp-area="Admin" asp-controller="User" asp-action="CreateOrUpdate" method="post" class="tab-wizard wizard-circle wizard clearfix  example-form">
                    @Html.AntiForgeryToken()

                    <h6 id="stopnavigate">
                        <span class="step">
                            <i class="cp-user"></i>
                        </span>
                        <span class="step_title">
                            User Configuration
                        </span>
                    </h6>
                    <section>
                        <div class="form-group">
                            <div class="form-label">Authentication Type</div>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="cp-user_role"></i>
                                </span>
                                <select id="userLoginType" class="form-select-modal" data-live-search="true" data-placeholder="Select Authentication Type">
                                    <option></option>
                                    <option value="In House">In House</option>
                                    <option value="AD">AD</option>
                                </select>
                            </div>
                            <input asp-for="LoginType" type="hidden" id="loginType" />
                            <span asp-validation-for="LoginType" id="LoginType-error"></span>
                        </div>                      
                        <div class="" id="ad_hide">
                            <div class="form-group">
                            
                                <div class="row row-cols-4">
                                    <div class="col-auto col">
                                        <div class="form-check">
                                            <input name="state" type="radio" id="ADIndividual"
                                                   class="form-check-input" value="Individual"><label for="ADIndividual"
                                                                                                      class="form-check-label" cursorshover="true">Individual</label>
                                        </div>
                                    </div>
                                    <div class="col-auto col">
                                        <div class="form-check">
                                            <input name="state" type="radio" id="ADGroup"
                                                   class="form-check-input" value="group" cursorshover="true"><label for="ADGroup" class="form-check-label"
                                                                                                                     cursorshover="true">Group</label>
                                        </div>
                                    </div>
                                </div>
                                <span id="AdUsertype"></span>
                            </div>
                            <div class="form-group ">
                                <label class="form-label" for="ddlCompanyId">Select Domain</label>
                                <div class="input-group w-auto">
                                    <span class="input-group-text"><i class="cp-web"></i></span>
                                    <select id="userDomain" class="form-select-modal form-select-sm" aria-label="Default select example"
                                            data-placeholder="Select Domain" name="ddlDomain">
                                    </select>
                                    <span class="input-group-text p-0">
                                        <button type="button" class="btn btn-primary btn-sm rounded-1" id="btnGetDomainExe">Get Domains</button>
                                    </span>
                                </div>
                                <span id="AdDomain-error"></span>
                            </div>

                            <div class="" id="ad_individual">
                                <div class="form-group " id="searchStringDomain">
                                    <label class="form-label" for="ddlCompanyId">Search User</label>
                                    <div class="input-group me-2 w-auto">
                                        <span class="input-group-text"><i class="cp-user"></i></span>
                                        <input id="SearchUserDomain" required="" autocomplete="off" placeholder="Search User" type="text" class="form-control">
                                        <span class="input-group-text p-0">
                                            <button type="button" class="btn btn-primary btn-sm rounded-1" id="btnSearchUserDomain">Search</button>
                                        </span>
                                    </div>
                                    <span id="AdUserSearchString-error"></span>
                                </div>
                                <div class="form-group" id="usernameDomain">
                                    <label class="form-label" for="ddlCompanyId">Select User</label>
                                    <div class="input-group w-auto">
                                        <span class="input-group-text"><i class="cp-user"></i></span>
                                        <select id="userDomainUser" class="form-select-modal form-select-sm" aria-label="Default select example"
                                                data-placeholder="Select User">
                                        </select>
                                    </div>
                                    <span id="AdUser-error"></span>
                                </div>                          
                            </div>
                            <div class="form-group" id="ADGroupName">
                            <div class="form-group " id="searchDomainGroup">
                                <label class="form-label" for="ddlCompanyId">Search Group</label>
                                <div class="input-group me-2 w-auto">
                                    <span class="input-group-text"><i class="cp-user"></i></span>
                                    <input id="SearchDomainGroup" required="" autocomplete="off" placeholder="Search Group" type="text" class="form-control">
                                    <span class="input-group-text p-0">
                                        <button type="button" class="btn btn-primary btn-sm rounded-1" id="btnSearchGroupDomain">Search</button>
                                    </span>
                                </div>
                                <span id="AdSearchGroupString-error"></span>
                            </div>
                            <div class="form-group" id="groupDomain">
                                <label class="form-label" for="ddlCompanyId">Select Group</label>
                                <div class="input-group w-auto">
                                    <span class="input-group-text"><i class="cp-user"></i></span>
                                    <select id="userDomainGroup" class="form-select-modal form-select-sm" aria-label="Default select example"
                                            data-placeholder="Select Group">
                                    </select>
                                </div>
                                <span id="AdGroup-error"></span>
                            </div>
                          </div>                             
                        </div>
                        <div class="form-group" id="ADLoginName">
                            <div class="form-label">Login Name</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-name"></i></span>
                                <input type="text" class="form-control" asp-for="LoginName" id="textLoginName" placeholder="Enter Login Name" maxlength="100" />
                            </div>
                            <input asp-for="Id" type="hidden" id="textLoginId" />
                            <span asp-validation-for="LoginName" id="LoginName-error"></span>
                        </div>
                        <div class="inhouse_div " id="login_hide">
                            <div class="row" id="password-group">
                                <div class="col">
                                    <div class="form-group">
                                        <label class="animation-label form-label" id="lblpassword">Password</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="cp-lock"></i>
                                            </span>
                                            <input required="" name="loginPassword" foreye="password" asp-for="LoginPassword" id="userPassword"
                                                   autocomplete="off" placeholder="Enter Password" maxlength="30"
                                                   type="password" class="form-control">
                                            <span class="input-group-text eye-change " role="button"><i class="cp-password-visible fs-6" title="Show Password"></i></span>
                                            @* <span class="input-group-text toggle-password" role="button" id="hf"><i class="cp-password-visible fs-6" title="show password"></i></span> *@
                                           @*  <span role="button" title="password" class="input-group-text toggle-password"></span> *@
                                        </div>
                                        <div class="progress"
                                             style="height: 0px; background-color: rgb(255, 255, 255);">
                                            <div class="progress-bar"
                                                 style="width: 0%; background: rgb(130, 130, 130); height: 7px;">
                                            </div>
                                        </div>
                                        <span asp-validation-for="LoginPassword" id="LoginPassword-error"></span>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <label class="animation-label form-label" id="lblpassword">
                                            Confirm
                                            Password
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="cp-lock"></i>
                                            </span>
                                            <input required="" name="ConfirmPassword" foreye="password" asp-for="ConfirmPassword" id="userConfirmPassword"
                                                   autocomplete="off" placeholder="Enter Confirm Password"
                                                   maxlength="30" type="password" class="form-control">
                                        </div> 

                                        <div class="progress"
                                             style="height: 0px; background-color: rgb(255, 255, 255);">
                                            <div class="progress-bar"
                                                 style="width: 0%; background: rgb(130, 130, 130); height: 7px;">
                                            </div>
                                        </div>
                                        <span asp-validation-for="ConfirmPassword" id="ConfirmPassword-error"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group text-end mt-2 mb-0">
                                <div class="row">
                                    <div id="mycPass_strength_wrap" class="mt-4">
                                        <div id="passwordDescription">Password not entered</div>
                                        <div id="passwordStrength" class="strength0"></div>
                                        <div id="pswd_info">
                                            <strong>Strong Password Tips:</strong>
                                            <ul>
                                                <li class="invalid" id="length"></li>
                                                <li class="invalid" id="pnum"></li>
                                                <li class="invalid" id="capital"></li>
                                                <li class="invalid" id="spchar"></li>
                                            </ul>
                                        </div>
                                    </div>
                                   
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <div class="form-label">Company</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-company"></i></span>
                                    <select asp-for="CompanyName" id="userCompanyName" class="form-select-modal" aria-label="Default select example"
                                            data-live-search="true" data-placeholder="Select Company Name">
                                        @foreach (var company in Model.Companies)
                                        {
                                            <option data-isParent="@company.IsParent" id="@company.Id" value="@company.DisplayName">@company.DisplayName</option>
                                        }
                                    </select>
                                </div>
                                <span asp-validation-for="CompanyName" id="Company-error"></span>
                                <input asp-for="CompanyId" name="companyId" type="hidden" id="userCompanyId" class="form-control" placeholder="Select Company Name" />
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-label">Role</div>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="cp-user-role"></i>
                                </span>
                                <select asp-for="RoleName" id="userRoleName" class="form-select-modal" aria-label="Default select example"
                                        data-live-search="true" data-placeholder="Select Role">
                                </select>
                            </div>
                            <span asp-validation-for="RoleName" id="Role-error"></span>
                            <input asp-for="Role" name="role" type="hidden" id="userRoleId" class="form-control" />
                        </div>
                    </section>
                    <h6>
                        <span class="step">
                            <i class="cp-user-profile"></i>
                        </span>
                        <span class="step_title">
                            User Details
                        </span>
                    </h6>
                    <section>
                        <div class="form-group">
                            <div class="form-label">Full Name</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-user-hcard"></i></span>
                                <input type="text" class="form-control" placeholder="Enter Full Name" id="textUserName" asp-for="UserInfoCommand.UserName"
                                       maxlength="100" />
                            </div>
                            <span asp-validation-for="UserInfoCommand.UserName" id="UserName-error"></span>
                        </div>
                        <div class="d-flex gap-3">
                            <div class="mb-3 form-group w-50">
                                <div class="form-label">Email</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-email"></i>
                                    </span>
                                    <input type="email" class="form-control" placeholder="Enter Email" id="textEmail" asp-for="UserInfoCommand.Email" maxlength="254" />
                                </div>
                                <span asp-validation-for="UserInfoCommand.Email" id="Email-error"></span>
                            </div>
                            <div class="form-group w-50">
                                <div class="form-label">Session Timeout (min)</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-timer-meter"></i></span>
                                    <input type="number" class="form-control" asp-for="SessionTimeout" id="usersSessionTimeout"
                                           placeholder="Enter Session Timeout" maxlength="5" min="1" value="20" />
                                </div>
                                <span asp-validation-for="SessionTimeout" id="SessionTimeout-error"></span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <div class="d-flex form-group">
                                    <label class="animation-label form-label custom-cursor-default-hover"
                                           cursorshover="true">Alert Mode :</label>

                                    <div class="mx-3 form-check">
                                        <input name="alertMode" required="" disabled="true" asp-for="UserInfoCommand.AlertMode"
                                               type="checkbox" id="chk-AD" class="form-check-input"
                                               checked="true"><label for="chk-AD" class="form-check-label custom-cursor-default-hover"
                                                                     cursorshover="true">Email</label>
                                    </div>
                                    <div class="form-check">
                                        <input name="alertMode" required="" type="checkbox" asp-for="UserInfoCommand.AlertMode"
                                               id="chk-LDAP" class="form-check-input custom-cursor-default-hover"
                                               cursorshover="true"><label for="chk-LDAP"
                                                                          class="form-check-label custom-cursor-default-hover"
                                                                          cursorshover="true">Mobile</label>
                                    </div>
                                    <div class="mx-3 form-check">
                                        <input required=""
                                               type="checkbox" id="dashboardMode" class="form-check-input"><label for="dashboardMode" class="form-check-label custom-cursor-default-hover"
                                                                                                                  cursorshover="true">Dashboard</label>
                                        <input type="hidden" id="dashboardModeValue" asp-for="IsDefaultDashboard" value=false />
                                    </div>
                                </div>
                                <div id="mobileInputContainer" style="display: none;">
                                    <div class="d-flex gap-2">
                                        <div style="width:155px">
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="cp-mobile-icon"></i></span>
                                                    <select id="userCountryCode" class="form-select-modal" autocomplete="off" data-placeholder="Select Country Code">
                                                        <option></option>
                                                    </select>
                                                </div>
                                                <span asp-validation-for="UserInfoCommand.Mobile" id="MobilePre-error" class="text-start"></span>
                                            </div>
                                        </div>
                                        <div class="w-100">
                                            <div class="form-group">
                                                <div class="input-group">
                                                    <span class="input-group-text"></span>
                                                    <input id="userMobileNum" type="text" class="form-control" placeholder="Enter Mobile Number" autocomplete="off" maxlength="15" />
                                                </div>
                                                <span asp-validation-for="UserInfoCommand.Mobile" id="Mobile-error"></span>
                                            </div>
                                        </div>
                                        <input asp-for=" UserInfoCommand.Mobile" type="hidden" id="comMobile" />
                                    </div>
                                </div>
                                <div id="dashboardInputContainer" class="d-none">
                                    <div class="form-group">
                                        <div class="form-label">Dashboard</div>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="cp-dashboard"></i>
                                            </span>
                                            <select asp-for="Url" id="dashboardList" class="form-select-modal" aria-label="Default select example"
                                                    data-live-search="true" data-placeholder="Select Dashboard">
                                                <option value=""></option>
                                                <option value="Dashboard/ServiceAvailability/List">Service Availability</option>
                                                <option value="Dashboard/ITResiliencyView/List">IT Resiliency View</option>
                                                <option value="Dashboard/Analytics/Index">Operational Analytics</option>
                                                <option value="Dashboard/ResiliencyMapping/ResiliencyMapping">One View</option>
                                                <option value="Drift/DriftDashboard/List">Drift Dashboard</option>
                                                <option value="ResiliencyReadiness/ResiliencyDashboard/List">Resiliency Dashboard</option>
                                                <option value="CyberResiliency/CyberResiliency/Dashboard">Cyber Resiliency Dashboard</option>
                                            </select>
                                        </div>
                                        <span id="dashboard-error"></span>
                                    </div>
                                </div>
                                <div class="form-group " id="confirmation_mail">
                                    <div class="form-check">
                                        <input name="UserInfo.isPreferredMode" asp-for="UserInfoCommand.IsPreferredMode"
                                               type="checkbox" id="chk-workflowtype" checked="true"
                                               class="form-check-input">
                                        <label for="chk-workflowtype"
                                               class="form-check-label custom-cursor-default-hover"
                                               cursorshover="true">
                                            Send confirmation mail to user
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <h6>
                        <span class="step">
                            <i class="cp-infra-object"></i>
                        </span>
                        <span class="step_title">
                            Assign Infra Object
                        </span>
                    </h6>
                    <section>
                        <div class="Workflow-Tree" style="height: calc(100vh - 400px);">
                            <details>
                                <summary>
                                    <input class="form-check-input " type="checkbox" id="userWorkflowAll">All <!-- Add "Select All" checkbox -->
                                    <ul class="tree" id="treeview">
                                        <!-- JSON data will be dynamically generated here -->
                                    </ul>
                                </summary>
                            </details>
                            <span id="treeList-error" style="width: 97%"></span>
                            <input asp-for="InfraObjectAllFlag" type="hidden" id="txtinfraObjectAllFlag" />
                            <input asp-for="UserInfraObjectCommand.Properties" type="hidden" id="userProperties" />
                            <input asp-for="UserInfraObjectCommand.UserId" type="hidden" id="textUserId" />
                            <input asp-for="UserInfoCommand.IsPreferredMode" type="hidden" id="isPreferedChk" />
                            <input asp-for="EncryptPassword" type="hidden" id="encriptedPassword" />
                        </div>
                    </section>
                </form>
            </div>

        </div>
        <div class="modal-footer">
            <form asp-area="Admin" asp-controller="User" asp-action="CreateOrUpdate" class="d-flex justify-content-between align-items-center w-100">
                <small class="text-secondary">
                    <i class="cp-note me-1"></i>Note: All fields are mandatory
                    except optional
                </small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm" id="btnCancle" data-bs-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary prev_btn btn-sm" href="javascript:void(0)" role="menuitem" id="previousFunction"
                       onclick="form.steps('previous')">Previous</a>
                    <a class="btn btn-primary next_btn btn-sm" href="javascript:void(0)" role="menuitem" id="NextFunction">Next</a>
                    <button type="button" class="btn btn-primary finish_btn btn-sm" href="javascript:void(0)" role="menuitem" id="SaveFunction">Save</button>
                  
                </div>
            </form>
        </div>
    </div>
</div>
@* </div> *@
<script>
    $(document).ready(function () {
        $("#stopnavigate").css({ "pointer-events": "none" });
    })
</script>
@section Scripts
{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}
