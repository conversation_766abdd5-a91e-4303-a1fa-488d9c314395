﻿using ContinuityPatrol.Application.Features.Job.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Job.Queries;

public class GetJobNameQueryHandlerTests : IClassFixture<JobFixture>
{
    private readonly JobFixture _jobFixture;

    private Mock<IJobRepository> _mockJobRepository;

    private readonly GetJobNameQueryHandler _handler;

    public GetJobNameQueryHandlerTests(JobFixture jobFixture)
    {
        _jobFixture = jobFixture;

        _mockJobRepository = JobRepositoryMocks.GetJobNamesRepository(_jobFixture.Jobs);

        _handler = new GetJobNameQueryHandler(_mockJobRepository.Object, _jobFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_Active_Jobs_Name()
    {
        var result = await _handler.Handle(new GetJobNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<JobNameVm>>();

        result[0].Id.ShouldBe(_jobFixture.Jobs[0].ReferenceId);
        result[0].Name.ShouldBe(_jobFixture.Jobs[0].Name);
    }

    [Fact]
    public async Task Handle_Return_Active_InfraObjectNamesCount()
    {
        var result = await _handler.Handle(new GetJobNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<JobNameVm>>();

        result.Count.ShouldBe(_jobFixture.Jobs.Count);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockJobRepository = JobRepositoryMocks.GetJobEmptyRepository();

        var handler = new GetJobNameQueryHandler(_mockJobRepository.Object, _jobFixture.Mapper);

        var result = await handler.Handle(new GetJobNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetInfraObjectNamesMethod_OneTime()
    {
        await _handler.Handle(new GetJobNameQuery(), CancellationToken.None);

        _mockJobRepository.Verify(x => x.GetJobNames(), Times.Once);
    }
}