﻿using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface ITableAccessRepository : IRepository<TableAccess>
{
    Task<bool> IsTableAccessNameExist(string name, string id);

    Task<bool> IsTableAccessNameUnique(string name);

    Task<List<TableAccess>> GetTableAccessNames();

    Task<List<TableAccess>> GetSchemaNameList();

    Task<List<TableAccess>> GetTableNameListBySchema(string schemaName);

    Task<List<TableInformation>> GetSchemas(string schema, string provider);

    Task<TableAccess> GetTableAccessByTableName(string tableName);

    Task<List<TableAccessListVm>> GetTableAccessListAsync();
    Task<List<TableAccess>> GetTableAccessByTableNames(List<string> tableNames);
    Task<List<TableAccess>> GetUnUsedTableAccessByTableNames(List<string> tableNames);
}