using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class CyberAirGapRepository : BaseRepository<CyberAirGap>, ICyberAirGapRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public CyberAirGapRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public override async Task<IReadOnlyList<CyberAirGap>> ListAllAsync()
    {
        var cyberAirGaps = base.ListAllAsync(x=>x.IsActive);

        var airGap = MapCyberAirGap(cyberAirGaps);

        return await airGap.ToListAsync();
    }
    public override Task<CyberAirGap> GetByReferenceIdAsync(string id)
    {
        var cyberAirGaps = base.GetByReferenceIdAsync(id,x=>         
                  x.ReferenceId.Equals(id));

        var airGap = MapCyberAirGap(cyberAirGaps);

        return airGap.FirstOrDefaultAsync();
    }
    public override async Task<PaginatedResult<CyberAirGap>>PaginatedListAllAsync(int pageNumber,int pageSize,Specification<CyberAirGap> specification, string sortColumn, string sortOrder)
    { 
       return await MapCyberAirGap(Entities.Specify(specification)).DescOrderById().ToSortedPaginatedListAsync(pageNumber,pageSize,sortColumn,sortOrder);
    }
    public override IQueryable<CyberAirGap> PaginatedListAllAsync()
    {
        var cyberAirGaps = base.ListAllAsync(x=>x.IsActive);

        var airGap = MapCyberAirGap(cyberAirGaps);

        return airGap.AsNoTracking().OrderByDescending(x => x.Id);
    }
    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.Name.Equals(name))
            : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public async Task<List<CyberAirGap>> GetAirGapBySiteId(string id)
    {
        var cyberAirGaps = base.FilterBy(x => x.SourceSiteId.Equals(id) || x.TargetSiteId.Equals(id));

        var airGap = MapCyberAirGap(cyberAirGaps);

        return await airGap.ToListAsync();
    }
    public async Task<List<CyberAirGap>> GetAirGapByServerId(string id)
    {
        var cyberAirGaps = base.FilterBy(x => x.Source.Equals(id) || x.Target.Equals(id));

        var airGap = MapCyberAirGap(cyberAirGaps);

        return await airGap.ToListAsync();
    }
    public async Task<List<CyberAirGap>> GetAirGapByComponentId(string id)
    {
        var cyberAirGaps = base.FilterBy(x => x.SourceComponentId.Equals(id) || x.TargetComponentId.Equals(id));

        var airGap = MapCyberAirGap(cyberAirGaps);

        return await airGap.ToListAsync();
    }
    private IQueryable<CyberAirGap> MapCyberAirGap(IQueryable<CyberAirGap> cyberAirGaps)
    {
        return cyberAirGaps.Select(x => new
        {
            SourceSite = _dbContext.Sites.FirstOrDefault(s => s.ReferenceId.Equals(x.SourceSiteId)),
            TargetSite = _dbContext.Sites.FirstOrDefault(s => s.ReferenceId.Equals(x.TargetSiteId)),
            SourceComponent = _dbContext.CyberComponents.FirstOrDefault(c => c.ReferenceId.Equals(x.SourceComponentId)),
            TargetComponent = _dbContext.CyberComponents.FirstOrDefault(c => c.ReferenceId.Equals(x.TargetComponentId)),
            AirGap = x
        })
        .Select(res => new CyberAirGap
        {
            Id= res.AirGap.Id,
            ReferenceId=res.AirGap.ReferenceId,
            Name = res.AirGap.Name,
            SourceSiteId=res.SourceSite.ReferenceId ?? res.AirGap.SourceSiteId,
            SourceSiteName=res.SourceSite.Name ?? res.AirGap.SourceSiteName,
            TargetSiteId=res.TargetSite.ReferenceId ?? res.AirGap.TargetSiteId,
            TargetSiteName = res.TargetSite.Name?? res.AirGap.TargetSiteName,
            Port=res.AirGap.Port,
            Description=res.AirGap.Description,
            Source = res.AirGap.Source,
            SourceComponentId=res.SourceComponent.ReferenceId ?? res.AirGap.SourceComponentId,
            SourceComponentName=res.SourceComponent.Name ?? res.AirGap.SourceComponentName,
            Target=res.AirGap.Target,
            TargetComponentId=res.TargetComponent.ReferenceId ?? res.AirGap.TargetComponentId,
            TargetComponentName=res.TargetComponent.Name ?? res.AirGap.TargetComponentName,
            EnableWorkflowId=res.AirGap.EnableWorkflowId,
            DisableWorkflowId=res.AirGap.DisableWorkflowId,
            WorkflowStatus=res.AirGap.WorkflowStatus,
            StartTime=res.AirGap.StartTime,
            EndTime=res.AirGap.EndTime,
            RPO=res.AirGap.RPO,
            IsAttached=res.AirGap.IsAttached,
            Status= res.AirGap.Status,
            ErrorMessage=res.AirGap.ErrorMessage,
            IsActive=res.AirGap.IsActive,
            CreatedBy=res.AirGap.CreatedBy,
            CreatedDate=res.AirGap.CreatedDate,
            LastModifiedBy=res.AirGap.LastModifiedBy,
            LastModifiedDate=res.AirGap.LastModifiedDate

        });
    }

  
}
