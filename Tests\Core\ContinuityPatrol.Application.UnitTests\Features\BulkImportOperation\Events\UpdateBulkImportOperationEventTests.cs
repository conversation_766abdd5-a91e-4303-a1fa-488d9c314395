using ContinuityPatrol.Application.Features.BulkImportOperation.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperation.Events;

public class UpdateBulkImportOperationEventTests : IClassFixture<BulkImportOperationFixture>, IClassFixture<UserActivityFixture>
{
    private readonly BulkImportOperationFixture _bulkImportOperationFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly BulkImportOperationUpdatedEventHandler _handler;

    public UpdateBulkImportOperationEventTests(BulkImportOperationFixture bulkImportOperationFixture, UserActivityFixture userActivityFixture)
    {
        _bulkImportOperationFixture = bulkImportOperationFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");
        mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/bulkimportoperation");
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        var mockBulkImportOperationEventLogger = new Mock<ILogger<BulkImportOperationUpdatedEventHandler>>();

        _mockUserActivityRepository = BulkImportOperationRepositoryMocks.CreateBulkImportOperationEventRepository(_userActivityFixture.UserActivities);

        _handler = new BulkImportOperationUpdatedEventHandler(
            mockLoggedInUserService.Object, 
            mockBulkImportOperationEventLogger.Object, 
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateBulkImportOperationEventCreated()
    {
        // Arrange
        _userActivityFixture.UserActivities[0].LoginName = "Tester";
        var bulkImportOperationUpdatedEvent = new BulkImportOperationUpdatedEvent { Name = "TestUser" };

        // Act
        var result = _handler.Handle(bulkImportOperationUpdatedEvent, CancellationToken.None);

        // Assert
        Assert.True(result.IsCompleted);
        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var bulkImportOperationUpdatedEvent = new BulkImportOperationUpdatedEvent { Name = "TestUser" };

        // Act
        await _handler.Handle(bulkImportOperationUpdatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_WithCorrectProperties()
    {
        // Arrange
        var bulkImportOperationUpdatedEvent = new BulkImportOperationUpdatedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.LoginName.ShouldBe("Tester");
        capturedUserActivity.Action.ShouldContain("Update BulkImportOperation");
        capturedUserActivity.Entity.ShouldBe("BulkImportOperation");
        capturedUserActivity.ActivityType.ShouldBe("Update");
        capturedUserActivity.ActivityDetails.ShouldContain("TestUser");
        capturedUserActivity.ActivityDetails.ShouldContain("updated successfully");
    }

    [Fact]
    public async Task Handle_LogCorrectInformation_When_BulkImportOperationUpdated()
    {
        // Arrange
        var bulkImportOperationUpdatedEvent = new BulkImportOperationUpdatedEvent { Name = "TestUser" };
        var mockLogger = new Mock<ILogger<BulkImportOperationUpdatedEventHandler>>();

        var handler = new BulkImportOperationUpdatedEventHandler(
            new Mock<ILoggedInUserService>().Object,
            mockLogger.Object,
            _mockUserActivityRepository.Object);

        // Act
        await handler.Handle(bulkImportOperationUpdatedEvent, CancellationToken.None);

        // Assert
        //mockLogger.Verify(
        //    x => x.Log(
        //        LogLevel.Information,
        //        It.IsAny<EventId>(),
        //        It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("TestUser") && v.ToString().Contains("updated successfully")),
        //        It.IsAny<Exception>(),
        //        It.IsAny<Func<It.IsAnyType, Exception, string>>()),
        //    Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectUserActivityProperties_When_UserServiceHasData()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var testUrl = "/api/test";
        var testIpAddress = "***********";
        var testLoginName = "TestUser";

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);
        mockLoggedInUserService.Setup(x => x.LoginName).Returns(testLoginName);
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(testUrl);
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns(testIpAddress);

        var handler = new BulkImportOperationUpdatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationUpdatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationUpdatedEvent = new BulkImportOperationUpdatedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.UserId.ShouldBe(testUserId);
        capturedUserActivity.LoginName.ShouldBe(testLoginName);
        capturedUserActivity.RequestUrl.ShouldBe(testUrl);
        capturedUserActivity.HostAddress.ShouldBe(testIpAddress);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_EventHandled()
    {
        // Arrange
        var bulkImportOperationUpdatedEvent = new BulkImportOperationUpdatedEvent { Name = "ProductionUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.Action.ShouldBe("Update BulkImportOperation");
        capturedUserActivity.Entity.ShouldBe("BulkImportOperation");
        capturedUserActivity.ActivityType.ShouldBe("Update");
        capturedUserActivity.ActivityDetails.ShouldBe("BulkImportOperation 'ProductionUser' updated successfully.");
    }

    [Fact]
    public async Task Handle_NotSetCreatedByAndLastModifiedBy_When_UpdateEvent()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);

        var handler = new BulkImportOperationUpdatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationUpdatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationUpdatedEvent = new BulkImportOperationUpdatedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        // Update events don't set CreatedBy and LastModifiedBy unlike Create events
        capturedUserActivity.CreatedBy.ShouldBeNull();
        capturedUserActivity.LastModifiedBy.ShouldBeNull();
    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_EventProcessed()
    {
        // Arrange
        var bulkImportOperationUpdatedEvent = new BulkImportOperationUpdatedEvent { Name = null };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.ActivityDetails.ShouldContain("updated successfully");
    }

    [Fact]
    public async Task Handle_SetCorrectRequestUrl_When_UserServiceProvided()
    {
        // Arrange
        var testRequestUrl = "/api/bulkimportoperation/update";
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(testRequestUrl);

        var handler = new BulkImportOperationUpdatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationUpdatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationUpdatedEvent = new BulkImportOperationUpdatedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.RequestUrl.ShouldBe(testRequestUrl);
    }

    [Fact]
    public async Task Handle_SetCorrectHostAddress_When_UserServiceProvided()
    {
        // Arrange
        var testHostAddress = "********";
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns(testHostAddress);

        var handler = new BulkImportOperationUpdatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationUpdatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationUpdatedEvent = new BulkImportOperationUpdatedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.HostAddress.ShouldBe(testHostAddress);
    }

    [Fact]
    public async Task Handle_UseUserNameAsEventName_When_BulkImportOperationUpdated()
    {
        // Arrange
        var bulkImportOperationUpdatedEvent = new BulkImportOperationUpdatedEvent { Name = "AdminUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.ActivityDetails.ShouldContain("AdminUser");
        capturedUserActivity.ActivityDetails.ShouldBe("BulkImportOperation 'AdminUser' updated successfully.");
    }
}
