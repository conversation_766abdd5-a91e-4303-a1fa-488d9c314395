using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class AdPasswordExpireRepository : BaseRepository<AdPasswordExpire>, IAdPasswordExpireRepository
{
    public AdPasswordExpireRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
    }
    public async Task<bool> IsNameExist(string name, string id)
    {
        if (!id.IsValidGuid())
        {
            return await Entities.AnyAsync(e => e.UserName == name);
        }

        var matchedList = await Entities.Where(e => e.UserName == name).ToListAsync();

        return matchedList.Unique(id);
    }
}
