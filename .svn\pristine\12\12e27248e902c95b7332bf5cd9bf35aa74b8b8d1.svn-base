using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class ApprovalMatrixRequestRepository : BaseRepository<ApprovalMatrixRequest>, IApprovalMatrixRequestRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public ApprovalMatrixRequestRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.ProcessName.Equals(name))
            : Entities.Where(e => e.ProcessName.Equals(name)).ToList().Unique(id));
    }


    public async Task<bool> IsValidWithdrawUser(string id)
    {
        return await _dbContext.ApprovalMatrixRequests
            .AsNoTracking()
            .AnyAsync(x => x.ReferenceId == id && x.CreatedBy == _loggedInUserService.UserId);
    }


}
