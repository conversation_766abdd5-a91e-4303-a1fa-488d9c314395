﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ComponentType.Events.Create;

public class ComponentTypeCreatedEventHandler : INotificationHandler<ComponentTypeCreatedEvent>
{
    private readonly ILogger<ComponentTypeCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ComponentTypeCreatedEventHandler(ILoggedInUserService userService,
        ILogger<ComponentTypeCreatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ComponentTypeCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.ComponentType.ToString(),
            Action = $"{ActivityType.Create} {Modules.ComponentType}",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"ComponentType '{createdEvent.Name}' created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"ComponentType '{createdEvent.Name}' created successfully.");
    }
}