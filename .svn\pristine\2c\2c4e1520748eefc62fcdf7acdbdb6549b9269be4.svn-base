﻿namespace ContinuityPatrol.Application.Features.GlobalSetting.Commands.Create;

public class
    CreateGlobalSettingCommandHandler : IRequestHandler<CreateGlobalSettingCommand, CreateGlobalSettingResponse>
{
    private readonly IGlobalSettingRepository _globalSettingRepository;
    private readonly IMapper _mapper;

    public CreateGlobalSettingCommandHandler(IMapper mapper, IGlobalSettingRepository globalSettingRepository)
    {
        _mapper = mapper;
        _globalSettingRepository = globalSettingRepository;
    }

    public async Task<CreateGlobalSettingResponse> Handle(CreateGlobalSettingCommand request,
        CancellationToken cancellationToken)
    {
        var globalSetting = _mapper.Map<Domain.Entities.GlobalSetting>(request);

        globalSetting = await _globalSettingRepository.AddAsync(globalSetting);

        var response = new CreateGlobalSettingResponse
        {

            Message = globalSetting.GlobalSettingValue.Equals("true")
            ? $"Global Settings '{globalSetting.GlobalSettingKey}' Enabled Successfully."
            : $"Global Settings '{globalSetting.GlobalSettingKey}' Disabled Successfully.",


            Id = globalSetting.ReferenceId
        };

        return response;
    }
}