﻿let selectedValues = [];
let createPermission = $("#orchestrationCreate").data("create-permission").toLowerCase();
let deletePermission = $("#orchestrationDelete").data("delete-permission").toLowerCase();
let globalUserName = $('#userLoginName').text().replace(/ /g, '')
const workflowList = {
    getPagination: "/ITAutomation/WorkflowList/GetPagination",
    workflowConfiguration: '/ITAutomation/WorkflowConfiguration/List',
    getReport: "ITAutomation/WorkflowConfiguration/GetRunBookReport"
}
$(function () {
    let dataTable = $('#WorkFlowList').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
            }
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        processing: true,
        serverSide: false,
        filter: true,
        order: [],
        ajax: {
            type: "GET",
            url: workflowList.getPagination,
            dataType: "json",
            data(d) {
                d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                d.pageSize = d?.length;
                d.searchString = selectedValues?.length ? selectedValues.join(';') : $('#wfListSearch').val();
                selectedValues.length = 0;
            },
            dataSrc(json) {
                json.recordsTotal = json?.totalPages;
                json.recordsFiltered = json?.totalCount;
                $(".pagination-column").toggleClass("disabled", json.data.length === 0);
                return json?.data;
            }
        },
        "columns": [
            {
                "data": null,
                "name": "Sr. No.",
                "autoWidth": true,
                "orderable": false,
                "render": function (data, type, row, meta) {
                    if (type === 'display') {
                        return meta.row + 1;
                    }
                    return data;
                },
            },
            {
                data: "workflowName",
                name: "Workflow Name",
                autoWidth: true,
                render: function (data, type) {
                    const value = data || 'NA';
                    return type === 'display' ? `<span title="${value}">${value}</span>` : value;
                }
            },
            {
                data: "businessServiceName",
                name: "Business Servic",
                autoWidth: true,
                render: function (data, type) {
                    const value = data || 'NA';
                    return type === 'display' ? `<span title="${value}">${value}</span>` : value;
                }
            },
            {
                data: "profileName",
                name: "Profile Name",
                autoWidth: true,
                render: function (data, type) {
                    const value = data || 'NA';
                    return type === 'display' ? `<span title="${value}">${value}</span>` : value;
                }
            },
            {
                data: "infraObjectName",
                name: "InfraObject Name",
                autoWidth: true,
                render: function (data, type) {
                    const value = data || 'NA';
                    return type === 'display' ? `<span title="${value}">${value}</span>` : value;
                }
            },
            {
                data: "workflowType",
                name: "Type",
                autoWidth: true,
                render: function (data, type) {
                    const value = data || 'NA';
                    return type === 'display' ? `<span title="${value}">${value}</span>` : value;
                }
            },
            {
                data: "lastExecutionDate",
                name: "LastExecution Date",
                autoWidth: true,
                render: function (data, type) {
                    const value = data || 'NA';
                    return type === 'display' ? `<span title="${value}">${value}</span>` : value;
                }
            },
            {
                data: "createdName",
                name: "createdBy",
                autoWidth: true,
                render: function (data, type) {
                    const value = data || 'NA';
                    return type === 'display' ? `<span title="${value}">${value}</span>` : value;
                }
            },
            {
                "data": null,
                "name": "Status",
                "autoWidth": true,
                render: function (data, type, row) {
                    if (createPermission == 'true') {
                        if (row) {
                            let lockStatus = `<span><i class="${row.isLock ? "cp-lock text-danger me-1" : "cp-open-lock text-success me-1"}" title="${row.isLock ? "Lock" : "Unlock"}"data-Workflow-id="${row.workflowId}" data-workflowName="${row.workflowName}" data-Workflow-isLock="${row.isLock}"></i></span>`;
                            let publishStatus = `<span><i class="${row.isPublish ? "cp-publish text-warning" : "cp-non-publish text-info"}" title="${row.isPublish ? "Publish" : "Unpublish"}" data-workflowName="${row.workflowName}"data-Workflow-id="${row.workflowId}" data-Workflow-isPublish="${row.isPublish}"></i></span>`;
                            return `
                                        <span >
                                          ${lockStatus}  ${publishStatus}
                                        </span>`;
                        }
                        return data;
                    }
                    else if (createPermission == 'false') {

                        if (row) {
                            let lockStatus = `<span><i class="${row.isLock ? "cp-lock text-danger me-1 icon-disabled" : "cp-open-lock text-success me-1 icon-disabled"}"title="${row.isLock ? "Lock" : "Unlock"}"data-Workflow-id="${row.workflowId}" data-workflowName="${row.workflowName}"data-Workflow-isLock="${row.isLock}"></i></span>`;
                            let publishStatus = `<span><i class="${row.isPublish ? "cp-publish text-warning me-1 icon-disabled" : "cp-non-publish text-info me-1 icon-disabled"}"title="${row.isPublish ? "Publish" : "Unpublish"}"data-workflowName="${row.workflowName}"data-Workflow-id="${row.workflowId}"data-Workflow-isPublish="${row.isPublish}"></i></span>`;

                            return `
                                        <span >
                                          ${lockStatus}  ${publishStatus}
                                        </span>`;
                        }
                        return data;
                    }
                }
            },
            {
                "data": null,
                "name": "Report",
                "autoWidth": true,
                render: function (data, type, row) {
                    return `
                                <span role='button' class="downloadReportButton" title="Report" data-workflowId='${row?.id}' data-workflowName='${row?.workflowName}'>
                                   <i class="cp-custom-reports" type="button"></i>
                                </span>
                                `;
                },
            },
            {
                "data": null,
                "name": "View",
                "autoWidth": true,
                render: function (data, type, row) {
                    if (createPermission == 'true') {
                        return `<span role='button'><i class="cp-password-visible viewModeUnique text-primary${row.isPublish || ((row.createdName.includes('\\') ? row.createdName.split('\\')[1] : row.createdName) === $('#userLoginName').text().replace(/ /g, '')) ? '' : 'btn-disabled'}" type="button" title="View" data-Workflow-id="${row.id}" style="${row.isPublish || ((row.createdName.includes('\\') ? row.createdName.split('\\')[1] : row.createdName) === $('#userLoginName').text().replace(/ /g, '')) ? '' : 'pointer-events:none;'}"></i></span>`;
                    }
                    else if (createPermission == 'false') {
                        return `
                                <span role='button'>
                                   <i class="cp-workflow icon-disabled" type="button"></i>
                                </span>
                                `;
                    }
                },
            }
        ],

        rowCallback(row, data, index) {
            const startIndex = this.api().context[0]._iDisplayStart;
            $('td:eq(0)', row).html(startIndex + index + 1);
        },
        initComplete() {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
        },
        columnDefs: [
            { orderable: false, targets: [-2, -1] },
            { targets: [1, 2, 3, 4, 5, 6], className: "truncate" }
        ]
    });

    $('#wfListSearch').on('keyup input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        selectedValues = [];
        var inputValue = $('#wfListSearch').val();
        let searchArray = [{ key: 'name', value: 'workflow' }, { key: 'infraname', value: 'infraobject' }, { key: 'profilename', value: 'profile' },
        { key: 'bsName', value: 'businessservice' }, { key: 'bfName', value: 'businessfunction' }, { key: 'wfType', value: 'workflowtype' }]
        searchArray.forEach((x) => {
            if ($(`#${x.key}`).is(':checked')) {
                selectedValues.push(x.value + '=' + inputValue)
            }
        })
        dataTable.ajax.reload(function (json) {
            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })

    }, 200))
    $(document).on('click', '.viewModeUnique', function () {
        let id = $(this).data("workflowId");
        sessionStorage.setItem('WorkflowId', id)
        setTimeout(() => {
            window.location.assign(workflowList.workflowConfiguration)
        }, 200)
    })

    // Report Download
    $('#WorkFlowList').on('click', '.downloadReportButton', async function () {
        let workflowId = $(this).data("workflowid");
        let workflowName = $(this).data("workflowname");
        try {
            const url = `${RootUrl + workflowList.getReport}?WorkflowId=${workflowId}`;
            const method = 'POST';
            const fetchResponse = await fetch(url, {
                method: method,
                headers: {
                    'RequestVerificationToken': await gettoken(),
                },
            });
            if (fetchResponse?.ok) {
                const response = await fetchResponse?.blob();
                if (response?.type == "application/pdf") {
                    const DateTime = new Date().toLocaleString('en-US', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        fractionalSecondDigits: 3,
                        hour12: false,
                    })?.replace(/[^0-9]/g, '');
                    const formattedDateTime = DateTime?.replace(/(\d{2})(\d{2})(\d{4})(\d{2})(\d{2})(\d{2})(\d{3})/, '$1$2$3_$4$5$6');
                    await downloadFileXls(response, workflowName + "_RunBook_" + formattedDateTime + ".xls", "application/xls");
                    message = workflowName + " RunBook downloaded successfully";
                    notificationAlert("success", message);
                }
                else {
                    message = workflowName + " RunBook downloaded failed!";
                    notificationAlert("error", message);
                }
            } else {
                message = workflowName + " RunBook downloaded failed!";
                notificationAlert("error", message);
            }
        } catch (error) {
            message = workflowName + " RunBook downloaded failed!";
            notificationAlert("error", message);
        }
    });

    async function downloadFileXls(blob, fileName, contentType) {
        try {
            const link = document.createElement("a");
            link.download = fileName;
            link.href = window.URL.createObjectURL(blob);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error) {
            console.error("Error downloading file: " + error?.message);
        }
    }
})