﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Workflow.Events.Publish;

public class WorkflowPublishEventHandler : INotificationHandler<WorkflowPublishEvent>
{
    private readonly ILogger<WorkflowPublishEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowPublishEventHandler(ILogger<WorkflowPublishEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(WorkflowPublishEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{(notification.IsPublished ? ActivityType.Publish : ActivityType.UnPublish)} {Modules.Workflow}",
            Entity = notification.IsPublished ? ActivityType.Publish.ToString() : ActivityType.UnPublish.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails =
                $"Workflow '{notification.WorkflowName}' {(notification.IsPublished ? "published" : "unpublished")} updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation(
            $"Workflow '{notification.WorkflowName}' {(notification.IsPublished ? "published" : "unpublished")} updated successfully.");
    }
}