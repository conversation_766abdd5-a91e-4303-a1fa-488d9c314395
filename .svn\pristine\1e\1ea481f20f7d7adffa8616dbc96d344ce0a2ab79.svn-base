﻿namespace ContinuityPatrol.Web.Middlewares;

public class RemoveReturnUrlMiddleware
{
    private readonly RequestDelegate _next;

    public RemoveReturnUrlMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task Invoke(HttpContext context)
    {
        // Check if the request contains a 'returnUrl' query parameter
        if (context.Request.Query.ContainsKey("returnUrl"))
        {
            context.Request.QueryString = new QueryString(string.Empty);

            var path = context.Request.Path;

            if (path.ToString().Contains("Account/Login"))
            {
                path = path.ToString().Replace("Account/Login", string.Empty);
            }

            context.Response.Redirect(path);

            return;
        }

        // Call the next middleware in the pipeline
        await _next(context);
    }
}

public static class RemoveReturnUrlMiddlewareExtensions
{
    public static IApplicationBuilder UseRemoveReturnUrlMiddleware(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<RemoveReturnUrlMiddleware>();
    }
}