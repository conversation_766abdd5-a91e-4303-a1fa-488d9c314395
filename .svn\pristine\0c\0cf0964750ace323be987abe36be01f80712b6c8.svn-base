﻿using ContinuityPatrol.Application.Features.Alert.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.AlertModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.Alert.Queries;

public class GetAlertPaginatedListQueryHandlerTests : IClassFixture<AlertFixture>
{
    private readonly AlertFixture _alertFixture;
    private readonly Mock<IAlertRepository> _mockAlertRepository;
    private readonly GetAlertPaginatedListQueryHandler _handler;

    public GetAlertPaginatedListQueryHandlerTests(AlertFixture alertFixture)
    {
        _alertFixture = alertFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var userLogin = new Mock<IUserLoginRepository>();
        _mockAlertRepository = AlertRepositoryMocks.GetPaginatedAlertRepository(_alertFixture.Alerts);
        _handler = new GetAlertPaginatedListQueryHandler(_mockAlertRepository.Object, _alertFixture.Mapper, mockLoggedInUserService.Object, userLogin.Object);

        _alertFixture.Alerts[0].JobName = "ODG_RAC_Job1";
        _alertFixture.Alerts[0].Type = "Common_Unhandled_Exception1";
        _alertFixture.Alerts[0].Severity = "Critical1";
        _alertFixture.Alerts[0].InfraObjectName = "ODG_RAC_InfraObject1";

        _alertFixture.Alerts[1].JobName = "ODG_RAC_Job2";
        _alertFixture.Alerts[1].Type = "Common_Unhandled_Exception2";
        _alertFixture.Alerts[1].Severity = "Critical2";
        _alertFixture.Alerts[1].InfraObjectName = "ODG_RAC_InfraObject2";
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetAlertPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertListVm>>();

        result.Item1.TotalCount.ShouldBe(3);

        result.Item1.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_Alerts_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetAlertPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "jobname=ODG_RAC_Job1;type=Common_Unhandled_Exception1;severity=Critical1;infraobjectname=ODG_RAC_InfraObject1" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertListVm>>();

        result.Item1.TotalCount.ShouldBe(1);

        result.Item1.Data[0].JobName.ShouldBe("ODG_RAC_Job1");

        result.Item1.Data[0].Type.ShouldBe("Common_Unhandled_Exception1");

        result.Item1.Data[0].Severity.ShouldBe("Critical1");

        result.Item1.Data[0].InfraObjectName.ShouldBe("ODG_RAC_InfraObject1");
    }

    [Fact]
    public async Task Handle_Return_PaginatedAlerts_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetAlertPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Critical1" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertListVm>>();
        result.Item1.TotalCount.ShouldBe(1);

        result.Item1.Data[0].ShouldBeOfType<AlertListVm>();

        result.Item1.Data[0].Id.ShouldBeGreaterThan(0.ToString());
        result.Item1.Data[0].Type.ShouldBe(_alertFixture.Alerts[0].Type);
        result.Item1.Data[0].Severity.ShouldBe("Critical1");
        result.Item1.Data[0].SystemMessage.ShouldBe(_alertFixture.Alerts[0].SystemMessage);
        result.Item1.Data[0].UserMessage.ShouldNotBeEmpty(_alertFixture.Alerts[0].UserMessage);
        result.Item1.Data[0].JobName.ShouldNotBeEmpty(_alertFixture.Alerts[0].JobName);
        result.Item1.Data[0].InfraObjectId.ShouldNotBeEmpty(_alertFixture.Alerts[0].InfraObjectId);
        result.Item1.Data[0].InfraObjectName.ShouldNotBeEmpty(_alertFixture.Alerts[0].InfraObjectName);
        result.Item1.Data[0].ClientAlertId.ShouldNotBeEmpty(_alertFixture.Alerts[0].ClientAlertId);
        result.Item1.Data[0].IsResolve.ShouldBe(_alertFixture.Alerts[0].IsResolve);
        result.Item1.Data[0].IsAcknowledgement.ShouldBe(_alertFixture.Alerts[0].IsAcknowledgement);
        result.Item1.Data[0].EntityId.ShouldBe(_alertFixture.Alerts[0].EntityId);
        result.Item1.Data[0].EntityType.ShouldBe(_alertFixture.Alerts[0].EntityType);
        result.Item1.Data[0].AlertCategoryId.ShouldBe(_alertFixture.Alerts[0].AlertCategoryId);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetAlertPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "abc" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertListVm>>();

        result.Item1.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetAlertPaginatedListQuery(), CancellationToken.None);

        _mockAlertRepository.Verify(x => x.PaginatedListAllAsync(), Times.Once);
    }

}