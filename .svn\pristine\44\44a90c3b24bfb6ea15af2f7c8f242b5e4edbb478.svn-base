﻿using ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Commands.Delete;
using ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Commands.Update;
using ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Queries.GetList;
using ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerWorkflowDetailModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class InfraObjectSchedulerWorkflowDetailController : CommonBaseController
{
    [HttpPost]
    [Authorize(Policy = Permissions.Orchestration.Create)]
    public async Task<ActionResult<CreateInfraObjectSchedulerWorkflowDetailResponse>> CreateInfraObjectSchedulerWorkflowDetail([FromBody] CreateInfraObjectSchedulerWorkflowDetailCommand createInfraObjectSchedulerWorkflowDetailCommand)
    {
        Logger.LogInformation($"Create InfraObjectSchedulerWorkflowDetail '{createInfraObjectSchedulerWorkflowDetailCommand.InfraObjectName}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateInfraObjectSchedulerWorkflowDetail), await Mediator.Send(createInfraObjectSchedulerWorkflowDetailCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Orchestration.Edit)]
    public async Task<ActionResult<UpdateInfraObjectSchedulerWorkflowDetailResponse>> UpdateInfraObjectSchedulerWorkflowDetail([FromBody] UpdateInfraObjectSchedulerWorkflowDetailCommand updateInfraObjectSchedulerWorkflowDetailCommand)
    {
        Logger.LogInformation(
            $"Update InfraObjectSchedulerWorkflowDetail '{updateInfraObjectSchedulerWorkflowDetailCommand.InfraObjectName}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateInfraObjectSchedulerWorkflowDetailCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Orchestration.Delete)]
    public async Task<ActionResult<DeleteInfraObjectSchedulerWorkflowDetailResponse>> DeleteInfraObjectSchedulerWorkflowDetail(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "InfraObjectSchedulerWorkflowDetail Id");

        Logger.LogInformation($"Delete InfraObjectSchedulerWorkflowDetails by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteInfraObjectSchedulerWorkflowDetailCommand { Id = id }));
    }

    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<InfraObjectSchedulerWorkflowDetailListVm>>> GetInfraObjectSchedulerWorkflowDetailList()
    {
        Logger.LogInformation("Get All InfraObjectSchedulerWorkflowDetails");

        return Ok(await Cache.GetOrAddAsync(
            ApplicationConstants.Cache.AllInfraObjectSchedulerWorkflowDetailCacheKey + LoggedInUserService.CompanyId,
            () => Mediator.Send(new GetInfraObjectSchedulerWorkflowDetailListQuery()), CacheExpiry));
    }

    [HttpGet("{id}", Name = "GetInfraObjectSchedulerWorkflowDetail")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<InfraObjectSchedulerWorkflowDetailDetailVm>> GetInfraObjectSchedulerWorkflowDetailById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "InfraObjectSchedulerWorkflowDetail Id");

        Logger.LogInformation($"Get InfraObjectSchedulerWorkflowDetail by Id '{id}'");

        return Ok(await Mediator.Send(new GetInfraObjectSchedulerWorkflowDetailQuery { Id = id }));
    }

    [Route("paginated-list")]
    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<InfraObjectSchedulerWorkflowDetailListVm>>> GetPaginatedInfraObjectSchedulerWorkflowDetailList([FromQuery] GetInfraObjectSchedulerWorkflowDetailPaginatedListQuery query)
    {
        Logger.LogInformation("Get Searching Details in InfraObjectSchedulerWorkflowDetail Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys =
        {
            ApplicationConstants.Cache.AllInfraObjectSchedulerWorkflowDetailCacheKey + LoggedInUserService.CompanyId
        };

        ClearCache(cacheKeys);
    }
}