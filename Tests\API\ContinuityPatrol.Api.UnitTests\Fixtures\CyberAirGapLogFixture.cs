using AutoFixture;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapLogModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class CyberAirGapLogFixture
{
    public CreateCyberAirGapLogCommand CreateCyberAirGapLogCommand { get; }
    public UpdateCyberAirGapLogCommand UpdateCyberAirGapLogCommand { get; }
    public DeleteCyberAirGapLogCommand DeleteCyberAirGapLogCommand { get; }
    public CyberAirGapLogListVm CyberAirGapLogListVm { get; }
    public CyberAirGapLogDetailVm CyberAirGapLogDetailVm { get; }

    public CyberAirGapLogFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CreateCyberAirGapLogCommand>(c => c
            .With(b => b.AirGapId, Guid.NewGuid().ToString)
            .With(b => b.AirGapName, "Enterprise Air Gap Log Entry")
            .With(b => b.SourceSiteId, Guid.NewGuid().ToString)
            .With(b => b.SourceSiteName, "Primary Data Center")
            .With(b => b.TargetSiteId, Guid.NewGuid().ToString)
            .With(b => b.TargetSiteName, "DR Site")
            .With(b => b.Port, 8443)
            .With(b => b.Description, "Air gap replication log entry for monitoring")
            .With(b => b.Source, "{\"logLevel\":\"INFO\",\"timestamp\":\"2024-01-15T10:30:00Z\",\"message\":\"Replication started successfully\"}")
            .With(b => b.Target, "{\"logLevel\":\"INFO\",\"timestamp\":\"2024-01-15T10:30:05Z\",\"message\":\"Replication received successfully\"}")
            .With(b => b.SourceComponentId, Guid.NewGuid().ToString)
            .With(b => b.SourceComponentName, "Primary Database Logger")
            .With(b => b.TargetComponentId, Guid.NewGuid().ToString)
            .With(b => b.TargetComponentName, "DR Database Logger")
         
            .With(b => b.WorkflowStatus, "Completed")
            .With(b => b.StartTime, DateTime.Now.AddMinutes(-30))
            .With(b => b.EndTime, DateTime.Now.AddMinutes(-25))
            .With(b => b.RPO, "5 minutes")
            .With(b => b.Status, "Success")
            .With(b => b.IsFileTransfered, true));

        fixture.Customize<UpdateCyberAirGapLogCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.AirGapId, Guid.NewGuid().ToString)
            .With(b => b.AirGapName, "Updated Enterprise Air Gap Log Entry")
            .With(b => b.SourceSiteId, Guid.NewGuid().ToString)
            .With(b => b.SourceSiteName, "Updated Primary Data Center")
            .With(b => b.TargetSiteId, Guid.NewGuid().ToString)
            .With(b => b.TargetSiteName, "Updated DR Site")
            .With(b => b.Port, 9443)
            .With(b => b.Description, "Updated air gap replication log entry")
            .With(b => b.Source, "{\"logLevel\":\"WARN\",\"timestamp\":\"2024-01-15T11:30:00Z\",\"message\":\"Replication completed with warnings\"}")
            .With(b => b.Target, "{\"logLevel\":\"WARN\",\"timestamp\":\"2024-01-15T11:30:10Z\",\"message\":\"Replication received with warnings\"}")
            .With(b => b.SourceComponentId, Guid.NewGuid().ToString)
            .With(b => b.SourceComponentName, "Updated Primary Database Logger")
            .With(b => b.TargetComponentId, Guid.NewGuid().ToString)
            .With(b => b.TargetComponentName, "Updated DR Database Logger")
           
            .With(b => b.WorkflowStatus, "Completed with Warnings")
            .With(b => b.StartTime, DateTime.Now.AddMinutes(-60))
            .With(b => b.EndTime, DateTime.Now.AddMinutes(-50))
            .With(b => b.RPO, "10 minutes")
            .With(b => b.Status, "Warning")
            .With(b => b.IsFileTransfered, false));

        fixture.Customize<DeleteCyberAirGapLogCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString));

        fixture.Customize<CyberAirGapLogListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.AirGapId, Guid.NewGuid().ToString)
            .With(b => b.AirGapName, () => $"AirGapLog-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.SourceSiteId, Guid.NewGuid().ToString)
            .With(b => b.SourceSiteName, () => $"Source-Site-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.TargetSiteId, Guid.NewGuid().ToString)
            .With(b => b.TargetSiteName, () => $"Target-Site-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.Port, () => fixture.Create<int>() % 9000 + 1000)
            .With(b => b.Description, () => $"Log entry - {fixture.Create<string>().Substring(0, 10)}")
            .With(b => b.Source, () => $"{{\"logLevel\":\"INFO\",\"message\":\"Log-{fixture.Create<string>().Substring(0, 8)}\"}}")
            .With(b => b.Target, () => $"{{\"logLevel\":\"INFO\",\"message\":\"Log-{fixture.Create<string>().Substring(0, 8)}\"}}")
            .With(b => b.SourceComponentId, Guid.NewGuid().ToString)
            .With(b => b.SourceComponentName, () => $"Source-Logger-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.TargetComponentId, Guid.NewGuid().ToString)
            .With(b => b.TargetComponentName, () => $"Target-Logger-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.EnableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.DisableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.ErrorMessage, () => fixture.Create<bool>() ? null : $"Error-{fixture.Create<string>().Substring(0, 10)}")
            .With(b => b.WorkflowStatus, () => fixture.Create<bool>() ? "Completed" : "Failed")
            .With(b => b.StartTime, () => DateTime.Now.AddMinutes(-(fixture.Create<int>() % 120)))
            .With(b => b.EndTime, () => DateTime.Now.AddMinutes(-(fixture.Create<int>() % 60)))
            .With(b => b.RPO, () => $"{fixture.Create<int>() % 30 + 1} minutes")
            .With(b => b.Status, () => fixture.Create<bool>() ? "Success" : "Failed")
            .With(b => b.IsFileTransfered, () => fixture.Create<bool>()));

        fixture.Customize<CyberAirGapLogDetailVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.AirGapId, Guid.NewGuid().ToString)
            .With(b => b.AirGapName, "Enterprise Air Gap Detailed Log")
            .With(b => b.SourceSiteId, Guid.NewGuid().ToString)
            .With(b => b.SourceSiteName, "Production Data Center")
            .With(b => b.TargetSiteId, Guid.NewGuid().ToString)
            .With(b => b.TargetSiteName, "Disaster Recovery Center")
            .With(b => b.Port, 8443)
            .With(b => b.Description, "Detailed log entry for enterprise air gap replication monitoring and auditing")
            .With(b => b.Source, "{\"logDetails\":{\"level\":\"INFO\",\"timestamp\":\"2024-01-15T14:30:00Z\",\"operation\":\"REPLICATION_START\",\"sourceFiles\":[\"database_backup_20240115.bak\",\"transaction_log_20240115.trn\"],\"checksums\":{\"database_backup\":\"sha256:a1b2c3d4e5f6\",\"transaction_log\":\"sha256:f6e5d4c3b2a1\"},\"compression\":\"gzip\",\"encryption\":\"AES-256\"}}")
            .With(b => b.Target, "{\"logDetails\":{\"level\":\"INFO\",\"timestamp\":\"2024-01-15T14:35:00Z\",\"operation\":\"REPLICATION_COMPLETE\",\"receivedFiles\":[\"database_backup_20240115.bak\",\"transaction_log_20240115.trn\"],\"verifiedChecksums\":{\"database_backup\":\"sha256:a1b2c3d4e5f6\",\"transaction_log\":\"sha256:f6e5d4c3b2a1\"},\"decompression\":\"gzip\",\"decryption\":\"AES-256\",\"integrity\":\"VERIFIED\"}}")
            .With(b => b.SourceComponentId, Guid.NewGuid().ToString)
            .With(b => b.SourceComponentName, "Production Database Replication Agent")
            .With(b => b.TargetComponentId, Guid.NewGuid().ToString)
            .With(b => b.TargetComponentName, "DR Database Replication Agent")
            .With(b => b.EnableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.DisableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.ErrorMessage, (string)null)
            .With(b => b.WorkflowStatus, "Successfully Completed")
            .With(b => b.StartTime, DateTime.Now.AddMinutes(-45))
            .With(b => b.EndTime, DateTime.Now.AddMinutes(-40))
            .With(b => b.RPO, "2 minutes")
            .With(b => b.Status, "Success")
            .With(b => b.IsFileTransfered, true));

        CreateCyberAirGapLogCommand = fixture.Create<CreateCyberAirGapLogCommand>();
        UpdateCyberAirGapLogCommand = fixture.Create<UpdateCyberAirGapLogCommand>();
        DeleteCyberAirGapLogCommand = fixture.Create<DeleteCyberAirGapLogCommand>();
        CyberAirGapLogListVm = fixture.Create<CyberAirGapLogListVm>();
        CyberAirGapLogDetailVm = fixture.Create<CyberAirGapLogDetailVm>();
    }
}
