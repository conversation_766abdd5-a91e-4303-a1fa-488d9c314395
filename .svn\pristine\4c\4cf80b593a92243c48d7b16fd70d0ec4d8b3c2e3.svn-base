﻿using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MongoDbMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Manage;

public class MongoDbMonitorStatusService : BaseService, IMongoDbMonitorStatusService
{
    public MongoDbMonitorStatusService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateMongoDbMonitorStatusCommand createMongoDbMonitorStatusCommand)
    {
        Logger.LogDebug($"Create MongoDbMonitorStatus '{createMongoDbMonitorStatusCommand.InfraObjectName}'");

        return await Mediator.Send(createMongoDbMonitorStatusCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateMongoDbMonitorStatusCommand updateMongoDbMonitorStatusCommand)
    {
        Logger.LogDebug($"Update MongoDbMonitorStatus '{updateMongoDbMonitorStatusCommand.InfraObjectName}'");

        return await Mediator.Send(updateMongoDbMonitorStatusCommand);
    }

    public async Task<List<MongoDbMonitorStatusListVm>> GetAllMongoDbMonitorStatus()
    {
        Logger.LogDebug("Get All MongoDbMonitorStatus");

        return await Mediator.Send(new MongoDbMonitorStatusListQuery());
    }

    public async Task<MongoDbMonitorStatusDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "MongoDbMonitorStatusById");

        Logger.LogDebug($"Get MongoDbMonitorStatus Detail By Id '{id}' ");

        return await Mediator.Send(new MongoDbMonitorStatusDetailQuery { Id = id });
    }

    public async Task<List<MongoDbMonitorStatusDetailByTypeVm>> GetMongoDbMonitorStatusByType(string type)
    {
        Guard.Against.InvalidGuidOrEmpty(type, "MongoDbMonitorStatus Type");

        Logger.LogDebug($"Get MongoDbMonitorStatus Detail by Type '{type}'");

        return await Mediator.Send(new MongoDbMonitorStatusDetailByTypeQuery { Type = type });
    }

    public async Task<PaginatedResult<MongoDbMonitorStatusListVm>> GetPaginatedMongoDbMonitorStatus(
        GetMongoDbMonitorStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in MongoDbMonitorStatus Paginated List");

        return await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllMongoDbMonitorStatusCacheKey,
            () => Mediator.Send(query));
    }
}