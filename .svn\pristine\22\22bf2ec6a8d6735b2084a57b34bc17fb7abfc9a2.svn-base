using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class RiskMitigationRepositoryTests : IClassFixture<RiskMitigationFixture>
{
    private readonly RiskMitigationRepository _repository;
    private readonly RiskMitigationFixture _fixture;

    public RiskMitigationRepositoryTests(RiskMitigationFixture fixture)
    {
        _fixture = fixture;
        var loggedInUserService = DbContextFactory.GetMockUserService();
        _repository = new RiskMitigationRepository(_fixture.DbContext, loggedInUserService);
    }

    private async Task ClearDatabase()
    {
        _fixture.DbContext.RiskMitigations.RemoveRange(_fixture.DbContext.RiskMitigations);
        await _fixture.DbContext.SaveChangesAsync();
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveRiskMitigations_WhenUserIsAllInfra()
    {
        // Arrange
        await ClearDatabase();

        var riskMitigations = new List<RiskMitigation>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_001",
                BusinessServiceName = "Service 1",
                InfraObjectId = "INFRA_001",
                InfraObjectName = "Infrastructure 1",
                UnderControlRTO = true,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_002",
                BusinessServiceName = "Service 2",
                InfraObjectId = "INFRA_002",
                InfraObjectName = "Infrastructure 2",
                ExceedRTO = true,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_003",
                BusinessServiceName = "Service 3",
                InfraObjectId = "INFRA_003",
                InfraObjectName = "Infrastructure 3",
                MaintenanceRTO = true,
                IsActive = false
            }
        };

        foreach (var riskMitigation in riskMitigations)
        {
            await _repository.AddAsync(riskMitigation);
        }

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, result.Count); // Only active ones
        Assert.All(result, r => Assert.True(r.IsActive));
        Assert.Contains(result, r => r.BusinessServiceId == "BS_001");
        Assert.Contains(result, r => r.BusinessServiceId == "BS_002");
        Assert.DoesNotContain(result, r => r.BusinessServiceId == "BS_003");
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnAssignedBusinessServices_WhenUserIsNotAllInfra()
    {
        // Arrange
        await ClearDatabase();

        var riskMitigations = new List<RiskMitigation>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                BusinessServiceId = "BS_001",
                BusinessServiceName = "Assigned Service 1",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                BusinessServiceId = "BS_002",
                BusinessServiceName = "Assigned Service 2",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                BusinessServiceId = "BS_003",
                BusinessServiceName = "Unassigned Service",
                IsActive = true 
            }
        };

        foreach (var riskMitigation in riskMitigations)
        {
            await _repository.AddAsync(riskMitigation);
        }

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, r => r.BusinessServiceId == "BS_001");
        Assert.Contains(result, r => r.BusinessServiceId == "BS_002");
        Assert.DoesNotContain(result, r => r.BusinessServiceId == "BS_003");
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoActiveRiskMitigations()
    {
        // Arrange
        await ClearDatabase();

        var inactiveRiskMitigation = new RiskMitigation
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = "BS_001",
            BusinessServiceName = "Inactive Service",
            IsActive = false
        };

        await _repository.AddAsync(inactiveRiskMitigation);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnOnlyActiveRiskMitigations()
    {
        // Arrange
        await ClearDatabase();

        var riskMitigations = new List<RiskMitigation>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_001",
                BusinessServiceName = "Active Service 1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_002",
                BusinessServiceName = "Active Service 2",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_003",
                BusinessServiceName = "Inactive Service",
                IsActive = false
            }
        };

        foreach (var riskMitigation in riskMitigations)
        {
            await _repository.AddAsync(riskMitigation);
        }

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.True(r.IsActive));
        Assert.Contains(result, r => r.BusinessServiceId == "BS_001");
        Assert.Contains(result, r => r.BusinessServiceId == "BS_002");
        Assert.DoesNotContain(result, r => r.BusinessServiceId == "BS_003");
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnRiskMitigation_WhenExists()
    {
        // Arrange
        await ClearDatabase();

        var referenceId = Guid.NewGuid().ToString();
        var riskMitigation = new RiskMitigation
        {
            ReferenceId = referenceId,
            BusinessServiceId = "BS_001",
            BusinessServiceName = "Test Service",
            InfraObjectId = "INFRA_001",
            InfraObjectName = "Test Infrastructure",
            UnderControlRTO = true,
            ExceedRPO = false,
            RTODescription = "Test RTO Description",
            RPODescription = "Test RPO Description",
            IsAffected = true,
            ErrorMessage = "Test Error Message",
            IsActive = true
        };

        await _repository.AddAsync(riskMitigation);

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal("BS_001", result.BusinessServiceId);
        Assert.Equal("Test Service", result.BusinessServiceName);
        Assert.Equal("INFRA_001", result.InfraObjectId);
        Assert.Equal("Test Infrastructure", result.InfraObjectName);
        Assert.True(result.UnderControlRTO);
        Assert.False(result.ExceedRPO);
        Assert.Equal("Test RTO Description", result.RTODescription);
        Assert.Equal("Test RPO Description", result.RPODescription);
        Assert.True(result.IsAffected);
        Assert.Equal("Test Error Message", result.ErrorMessage);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenReferenceIdNotFound()
    {
        // Arrange
        await ClearDatabase();

        var nonExistentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnRiskMitigation_WhenValidReferenceId()
    {
        // Arrange
        await ClearDatabase();

        var referenceId = Guid.NewGuid().ToString();
        var riskMitigation = new RiskMitigation
        {
            ReferenceId = referenceId,
            BusinessServiceId = "BS_001",
            BusinessServiceName = "Test Service",
            InfraObjectId = "INFRA_001",
            InfraObjectName = "Test Infrastructure",
            UnderControlRTO = false,
            ExceedRTO = true,
            MaintenanceRTO = false,
            UnderControlRPO = true,
            ExceedRPO = false,
            MaintenanceRPO = false,
            RTODescription = "RTO Test Description",
            RPODescription = "RPO Test Description",
            IsAffected = false,
            ErrorMessage = "No errors",
            IsActive = true
        };

        await _repository.AddAsync(riskMitigation);

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal("BS_001", result.BusinessServiceId);
        Assert.Equal("Test Service", result.BusinessServiceName);
        Assert.Equal("INFRA_001", result.InfraObjectId);
        Assert.Equal("Test Infrastructure", result.InfraObjectName);
        Assert.False(result.UnderControlRTO);
        Assert.True(result.ExceedRTO);
        Assert.False(result.MaintenanceRTO);
        Assert.True(result.UnderControlRPO);
        Assert.False(result.ExceedRPO);
        Assert.False(result.MaintenanceRPO);
        Assert.Equal("RTO Test Description", result.RTODescription);
        Assert.Equal("RPO Test Description", result.RPODescription);
        Assert.False(result.IsAffected);
        Assert.Equal("No errors", result.ErrorMessage);
        Assert.True(result.IsActive);
    }

    #endregion

    #region AssignedBusinessServices Tests

    [Fact]
    public void AssignedBusinessServices_ShouldReturnMatchingServices_WhenAssignedBusinessServicesExist()
    {
        // Arrange
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.CompanyId).Returns("TEST_COMPANY");
        mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);

        // Create AssignedEntity with specific business services
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new() { Id = "BS_001", Name = "Service 1" },
                new() { Id = "BS_003", Name = "Service 3" }
            }
        };

        var assignedInfrasJson = JsonConvert.SerializeObject(assignedEntity);
        mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);

        var repository = new RiskMitigationRepository(_fixture.DbContext, mockLoggedInUserService.Object);

        var businessServices = new List<RiskMitigation>
        {
            new() { BusinessServiceId = "BS_001", BusinessServiceName = "Service 1" },
            new() { BusinessServiceId = "BS_002", BusinessServiceName = "Service 2" },
            new() { BusinessServiceId = "BS_003", BusinessServiceName = "Service 3" },
            new() { BusinessServiceId = "BS_004", BusinessServiceName = "Service 4" }
        }.AsQueryable();

        // Act
        var result = repository.AssignedBusinessServices(businessServices);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, r => r.BusinessServiceId == "BS_001");
        Assert.Contains(result, r => r.BusinessServiceId == "BS_003");
        Assert.DoesNotContain(result, r => r.BusinessServiceId == "BS_002");
        Assert.DoesNotContain(result, r => r.BusinessServiceId == "BS_004");
    }

    [Fact]
    public void AssignedBusinessServices_ShouldReturnEmpty_WhenNoAssignedBusinessServices()
    {
        // Arrange
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.CompanyId).Returns("TEST_COMPANY");
        mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);

        // Create AssignedEntity with empty business services
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>()
        };

        var assignedInfrasJson = JsonConvert.SerializeObject(assignedEntity);
        mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);

        var repository = new RiskMitigationRepository(_fixture.DbContext, mockLoggedInUserService.Object);

        var businessServices = new List<RiskMitigation>
        {
            new() { BusinessServiceId = "BS_001", BusinessServiceName = "Service 1" },
            new() { BusinessServiceId = "BS_002", BusinessServiceName = "Service 2" }
        }.AsQueryable();

        // Act
        var result = repository.AssignedBusinessServices(businessServices);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public void AssignedBusinessServices_ShouldReturnEmpty_WhenInputBusinessServicesIsEmpty()
    {
        // Arrange
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.CompanyId).Returns("TEST_COMPANY");
        mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);

        // Create AssignedEntity with business services
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new() { Id = "BS_001", Name = "Service 1" }
            }
        };

        var assignedInfrasJson = JsonConvert.SerializeObject(assignedEntity);
        mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);

        var repository = new RiskMitigationRepository(_fixture.DbContext, mockLoggedInUserService.Object);

        var businessServices = new List<RiskMitigation>().AsQueryable();

        // Act
        var result = repository.AssignedBusinessServices(businessServices);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public void AssignedBusinessServices_ShouldReturnEmpty_WhenNoMatchingBusinessServices()
    {
        // Arrange
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.CompanyId).Returns("TEST_COMPANY");
        mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);

        // Create AssignedEntity with different business services
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new() { Id = "BS_999", Name = "Service 999" },
                new() { Id = "BS_888", Name = "Service 888" }
            }
        };

        var assignedInfrasJson = JsonConvert.SerializeObject(assignedEntity);
        mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);

        var repository = new RiskMitigationRepository(_fixture.DbContext, mockLoggedInUserService.Object);

        var businessServices = new List<RiskMitigation>
        {
            new() { BusinessServiceId = "BS_001", BusinessServiceName = "Service 1" },
            new() { BusinessServiceId = "BS_002", BusinessServiceName = "Service 2" }
        }.AsQueryable();

        // Act
        var result = repository.AssignedBusinessServices(businessServices);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public void AssignedBusinessServices_ShouldReturnPartialMatch_WhenSomeServicesMatch()
    {
        // Arrange
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.CompanyId).Returns("TEST_COMPANY");
        mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);

        // Create AssignedEntity with some matching business services
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new() { Id = "BS_002", Name = "Service 2" },
                new() { Id = "BS_004", Name = "Service 4" },
                new() { Id = "BS_999", Name = "Service 999" } // This won't match
            }
        };

        var assignedInfrasJson = JsonConvert.SerializeObject(assignedEntity);
        mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);

        var repository = new RiskMitigationRepository(_fixture.DbContext, mockLoggedInUserService.Object);

        var businessServices = new List<RiskMitigation>
        {
            new() { BusinessServiceId = "BS_001", BusinessServiceName = "Service 1" },
            new() { BusinessServiceId = "BS_002", BusinessServiceName = "Service 2" },
            new() { BusinessServiceId = "BS_003", BusinessServiceName = "Service 3" },
            new() { BusinessServiceId = "BS_004", BusinessServiceName = "Service 4" }
        }.AsQueryable();

        // Act
        var result = repository.AssignedBusinessServices(businessServices);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, r => r.BusinessServiceId == "BS_002");
        Assert.Contains(result, r => r.BusinessServiceId == "BS_004");
        Assert.DoesNotContain(result, r => r.BusinessServiceId == "BS_001");
        Assert.DoesNotContain(result, r => r.BusinessServiceId == "BS_003");
    }

    [Fact]
    public void AssignedBusinessServices_ShouldPreserveOriginalOrder_WhenReturningMatches()
    {
        // Arrange
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.CompanyId).Returns("TEST_COMPANY");
        mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);

        // Create AssignedEntity with business services
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new() { Id = "BS_003", Name = "Service 3" },
                new() { Id = "BS_001", Name = "Service 1" },
                new() { Id = "BS_002", Name = "Service 2" }
            }
        };

        var assignedInfrasJson = JsonConvert.SerializeObject(assignedEntity);
        mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);

        var repository = new RiskMitigationRepository(_fixture.DbContext, mockLoggedInUserService.Object);

        // Input services in specific order
        var businessServices = new List<RiskMitigation>
        {
            new() { BusinessServiceId = "BS_001", BusinessServiceName = "Service 1" },
            new() { BusinessServiceId = "BS_002", BusinessServiceName = "Service 2" },
            new() { BusinessServiceId = "BS_003", BusinessServiceName = "Service 3" }
        }.AsQueryable();

        // Act
        var result = repository.AssignedBusinessServices(businessServices);

        // Assert
        Assert.Equal(3, result.Count);
        // Verify the order is preserved from the input businessServices, not from AssignedBusinessServices
        Assert.Equal("BS_001", result[0].BusinessServiceId);
        Assert.Equal("BS_002", result[1].BusinessServiceId);
        Assert.Equal("BS_003", result[2].BusinessServiceId);
    }

    [Fact]
    public void AssignedBusinessServices_ShouldHandleDuplicateBusinessServiceIds()
    {
        // Arrange
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.CompanyId).Returns("TEST_COMPANY");
        mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);

        // Create AssignedEntity with business services
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new() { Id = "BS_001", Name = "Service 1" },
                new() { Id = "BS_002", Name = "Service 2" }
            }
        };

        var assignedInfrasJson = JsonConvert.SerializeObject(assignedEntity);
        mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);

        var repository = new RiskMitigationRepository(_fixture.DbContext, mockLoggedInUserService.Object);

        // Input services with duplicates
        var businessServices = new List<RiskMitigation>
        {
            new() { BusinessServiceId = "BS_001", BusinessServiceName = "Service 1 - First" },
            new() { BusinessServiceId = "BS_001", BusinessServiceName = "Service 1 - Duplicate" },
            new() { BusinessServiceId = "BS_002", BusinessServiceName = "Service 2" }
        }.AsQueryable();

        // Act
        var result = repository.AssignedBusinessServices(businessServices);

        // Assert
        Assert.Equal(3, result.Count); // All matching services should be returned, including duplicates
        Assert.Equal(2, result.Count(r => r.BusinessServiceId == "BS_001"));
        Assert.Single(result.Where(r => r.BusinessServiceId == "BS_002"));
    }

    #endregion
}
