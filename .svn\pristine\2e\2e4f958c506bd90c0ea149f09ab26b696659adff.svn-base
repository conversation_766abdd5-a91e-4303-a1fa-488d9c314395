﻿using ContinuityPatrol.Application.Features.ReplicationMaster.Commands.Create;
using ContinuityPatrol.Application.Features.ReplicationMaster.Commands.Update;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetReplicationMasterByInfraMasterName;
using ContinuityPatrol.Domain.ViewModels.ReplicationMasterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class ReplicationMasterProfile : Profile
{
    public ReplicationMasterProfile()
    {
        CreateMap<CreateReplicationMasterCommand, ReplicationMasterViewModel>().ReverseMap();
        CreateMap<UpdateReplicationMasterCommand, ReplicationMasterViewModel>().ReverseMap();

        CreateMap<ReplicationMaster, CreateReplicationMasterCommand>().ReverseMap();
        CreateMap<UpdateReplicationMasterCommand, ReplicationMaster>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<ReplicationMaster, ReplicationMasterDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<ReplicationMaster, ReplicationMasterListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<ReplicationMaster, ReplicationMasterNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<ReplicationMaster, GetByInfraMasterNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<PaginatedResult<ReplicationMaster>,PaginatedResult<ReplicationMasterListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}