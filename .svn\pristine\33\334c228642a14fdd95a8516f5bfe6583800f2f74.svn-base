﻿using ContinuityPatrol.Application.Features.Server.Events.Delete;
using ContinuityPatrol.Application.Features.Server.Events.LicenseInfoEvents.Delete;

namespace ContinuityPatrol.Application.Features.Server.Commands.Delete;

public class DeleteServerCommandHandler : IRequestHandler<DeleteServerCommand, DeleteServerResponse>
{
    private readonly ICyberAirGapRepository _cyberAirGapRepository;
    private readonly IDatabaseRepository _databaseRepository;
    private readonly INodeRepository _nodeRepository;
    private readonly IPublisher _publisher;
    private readonly IServerRepository _serverRepository;
    private readonly IWorkflowRepository _workflowRepository;

    public DeleteServerCommandHandler(IServerRepository serverRepository, IDatabaseRepository databaseRepository,
        IPublisher publisher, IWorkflowRepository workflowRepository, INodeRepository nodeRepository,
        ICyberAirGapRepository cyberAirGapRepository)
    {
        _serverRepository = serverRepository;
        _databaseRepository = databaseRepository;
        _publisher = publisher;
        _workflowRepository = workflowRepository;
        _nodeRepository = nodeRepository;
        _cyberAirGapRepository = cyberAirGapRepository;
    }

    public async Task<DeleteServerResponse> Handle(DeleteServerCommand request, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, "Server Id");

        var databases = await _databaseRepository.GetDatabaseByServerId(request.Id);

        var workflow = await _workflowRepository.GetWorkflowPropertiesByServerId(request.Id);

        var nodes = await _nodeRepository.GetNodeByServerId(request.Id);

        var cyberAirGap = await _cyberAirGapRepository.GetAirGapByServerId(request.Id);

        var eventToDelete = await _serverRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.Server),
           new NotFoundException(nameof(Domain.Entities.Server), request.Id));

        if ((databases?.Count ?? 0) > 0 || (workflow?.Count ?? 0) > 0 || (nodes?.Count ?? 0) > 0 ||
            (cyberAirGap?.Count ?? 0) > 0)
            throw new InvalidException($"The server '{eventToDelete?.Name}' is currently in use");

        eventToDelete.IsActive = false;

        eventToDelete.LicenseKey = SecurityHelper.Encrypt(eventToDelete.LicenseKey);

        await _serverRepository.UpdateAsync(eventToDelete);

        var response = new DeleteServerResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.Server), eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(
            new ServerDeletedEvent { ServerId = eventToDelete.ReferenceId, ServerName = eventToDelete.Name },
            cancellationToken);

        if (!eventToDelete.RoleType.Trim().ToLower().Equals("database"))
            await _publisher.Publish(
                new ServerLicenseInfoDeletedEvent
                    { EntityName = eventToDelete.Name, EntityId = eventToDelete.ReferenceId },
                cancellationToken);

        return response;
    }
}