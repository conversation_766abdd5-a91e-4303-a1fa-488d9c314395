using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class HacmpClusterRepository : BaseRepository<HacmpCluster>, IHacmpClusterRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public HacmpClusterRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
   
    public async Task<bool> IsNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? await Entities.AnyAsync(e => e.Name.Equals(name))
            : (await Entities.Where(e => e.Name.Equals(name)).ToListAsync()).Unique(id);
    }

    public override async Task<PaginatedResult<HacmpCluster>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<HacmpCluster> specification, string sortColumn, string sortOrder)
    {
        return await Entities.Specify(specification).DescOrderById()
            .Select(x => new HacmpCluster
            {
                Id=x.Id,
                ReferenceId=x.ReferenceId,
                Name=x.Name,
                ServerId=x.ServerId,
                ServerName=x.ServerName,
                CLRGInfoPath=x.CLRGInfoPath,
                LSSRCPath=x.LSSRCPath,
                ResourceGroupName=x.ResourceGroupName
            }).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
}
