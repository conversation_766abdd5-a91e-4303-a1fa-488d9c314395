﻿using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.AlertNotificationModel;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertNotification.Queries;

public class GetAlertNotificationListQueryHandlerTests : IClassFixture<AlertNotificationFixture>
{
    private readonly AlertNotificationFixture _alertNotificationFixture;

    private Mock<IAlertNotificationRepository> _mockAlertNotificationRepository;

    private readonly GetAlertNotificationListQueryHandler _handler;

    public GetAlertNotificationListQueryHandlerTests(AlertNotificationFixture alertNotificationFixture)
    {
        _alertNotificationFixture = alertNotificationFixture;

        _mockAlertNotificationRepository = AlertNotificationRepositoryMocks.GetAlertNotificationRepository(_alertNotificationFixture.AlertNotifications);

        _handler = new GetAlertNotificationListQueryHandler(_alertNotificationFixture.Mapper, _mockAlertNotificationRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_AlertNotificationsCount()
    {
        var result = await _handler.Handle(new GetAlertNotificationListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<AlertNotificationListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_Valid_AlertNotificationsList()
    {
        var result = await _handler.Handle(new GetAlertNotificationListQuery(), CancellationToken.None);
        result.ShouldBeOfType<List<AlertNotificationListVm>>();

        result[0].Id.ShouldBe(_alertNotificationFixture.AlertNotifications[0].ReferenceId);

        result[0].AlertType.ShouldBe(_alertNotificationFixture.AlertNotifications[0].AlertType);

        result[0].AlertCategoryId.ShouldBe(_alertNotificationFixture.AlertNotifications[0].AlertCategoryId);

        result[0].AlertCode.ShouldBe(_alertNotificationFixture.AlertNotifications[0].AlertCode);

        result[0].AlertSentCount.ShouldBe(_alertNotificationFixture.AlertNotifications[0].AlertSentCount);

        result[0].InfraObjectId.ShouldBe(_alertNotificationFixture.AlertNotifications[0].InfraObjectId);

        result[0].EntityId.ShouldBe(_alertNotificationFixture.AlertNotifications[0].EntityId);

        result[0].PositiveAlertCount.ShouldBe(_alertNotificationFixture.AlertNotifications[0].PositiveAlertCount);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockAlertNotificationRepository = AlertNotificationRepositoryMocks.GetAlertNotificationEmptyRepository();

        var handler = new GetAlertNotificationListQueryHandler(_alertNotificationFixture.Mapper, _mockAlertNotificationRepository.Object);

        var result = await handler.Handle(new GetAlertNotificationListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetAlertNotificationListQuery(), CancellationToken.None);

        _mockAlertNotificationRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}