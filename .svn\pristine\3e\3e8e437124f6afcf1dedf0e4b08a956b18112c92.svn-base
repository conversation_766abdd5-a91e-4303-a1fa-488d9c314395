using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Delete;
using ContinuityPatrol.Application.Features.BiaRules.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Exceptions;
using System.ComponentModel.DataAnnotations.Schema;

namespace ContinuityPatrol.Application.UnitTests.Features.BiaRules.Commands;

public class DeleteBiaRulesTests : IClassFixture<BiaRulesFixture>
{
    private readonly BiaRulesFixture _biaRulesFixture;
    private readonly Mock<IBiaRulesRepository> _mockBiaRulesRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly DeleteBiaRulesCommandHandler _handler;

    public DeleteBiaRulesTests(BiaRulesFixture biaRulesFixture)
    {
        _biaRulesFixture = biaRulesFixture;
        _mockBiaRulesRepository = BiaRulesRepositoryMocks.CreateBiaRulesRepository(_biaRulesFixture.BiaRules);
        _mockPublisher = new Mock<IPublisher>();

        _handler = new DeleteBiaRulesCommandHandler(
            _mockBiaRulesRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_DeleteBiaRules_When_ValidCommand()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var command = new DeleteBiaRulesCommand { Id = existingBiaRule.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules deleted successfully");
        result.IsActive.ShouldBeFalse();

        _mockBiaRulesRepository.Verify(x => x.GetByReferenceIdAsync(existingBiaRule.ReferenceId), Times.Once);
        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BiaRules>(b => b.IsActive == false)), Times.Once);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<BiaRulesDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteActiveBiaRule_When_ValidCommand()
    {
        // Arrange
        var activeBiaRule = _biaRulesFixture.BiaRules.First(x => x.IsActive);
        var command = new DeleteBiaRulesCommand { Id = activeBiaRule.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules deleted successfully");
        result.IsActive.ShouldBeFalse();

        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BiaRules>(b =>
            b.ReferenceId == activeBiaRule.ReferenceId &&
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteRTORule_When_ValidCommand()
    {
        // Arrange
        var rtoRule = _biaRulesFixture.BiaRules.First(x => x.Type == "RTO");
        var command = new DeleteBiaRulesCommand { Id = rtoRule.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules deleted successfully");
        result.IsActive.ShouldBeFalse();

        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BiaRules>(b =>
            b.Type == "RTO" &&
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteRPORule_When_ValidCommand()
    {
        // Arrange
        // Add an RPO rule to the fixture
        var rpoRule = new Domain.Entities.BiaRules
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "RPO Rule for Testing",
            Type = "RPO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"threshold\":\"1\",\"unit\":\"hours\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-10).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(20).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RPO_DELETE_TEST",
            IsActive = true
        };
        _biaRulesFixture.BiaRules.Add(rpoRule);

        var command = new DeleteBiaRulesCommand { Id = rpoRule.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules deleted successfully");
        result.IsActive.ShouldBeFalse();

        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BiaRules>(b =>
            b.Type == "RPO" &&
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteEffectiveRule_When_ValidCommand()
    {
        // Arrange
        var effectiveRule = _biaRulesFixture.BiaRules.First(x => x.IsEffective);
        var command = new DeleteBiaRulesCommand { Id = effectiveRule.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules deleted successfully");
        result.IsActive.ShouldBeFalse();

        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BiaRules>(b =>
            b.IsEffective == true &&
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteInactiveRule_When_ValidCommand()
    {
        // Arrange
        // Add an inactive rule to the fixture
        var inactiveRule = new Domain.Entities.BiaRules
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "Inactive Rule for Testing",
            Type = "RTO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"threshold\":\"8\",\"unit\":\"hours\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-5).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(5).ToString("yyyy-MM-dd"),
            IsEffective = false,
            RuleCode = "BIA_RTO_INACTIVE_DELETE",
            IsActive = true
        };
        _biaRulesFixture.BiaRules.Add(inactiveRule);

        var command = new DeleteBiaRulesCommand { Id = inactiveRule.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules deleted successfully");
        result.IsActive.ShouldBeFalse();

        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BiaRules>(b =>
            b.IsEffective == false &&
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BiaRuleNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new DeleteBiaRulesCommand { Id = nonExistentId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));

        _mockBiaRulesRepository.Verify(x => x.GetByReferenceIdAsync(nonExistentId), Times.Once);
        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BiaRules>()), Times.Never);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<BiaRulesDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BiaRuleIsNull()
    {
        // Arrange
        var nullId = Guid.NewGuid().ToString();
        _mockBiaRulesRepository.Setup(x => x.GetByReferenceIdAsync(nullId))
            .ReturnsAsync((Domain.Entities.BiaRules)null);

        var command = new DeleteBiaRulesCommand { Id = nullId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));

        _mockBiaRulesRepository.Verify(x => x.GetByReferenceIdAsync(nullId), Times.Once);
        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BiaRules>()), Times.Never);
    }

    [Fact]
    public async Task Handle_PublishEvent_When_BiaRulesDeleted()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var command = new DeleteBiaRulesCommand { Id = existingBiaRule.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockPublisher.Verify(x => x.Publish(
            It.Is<BiaRulesDeletedEvent>(e => e.Name == existingBiaRule.Type),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_SoftDelete_When_BiaRulesDeleted()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var originalIsActive = existingBiaRule.IsActive;
        var command = new DeleteBiaRulesCommand { Id = existingBiaRule.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();
        originalIsActive.ShouldBeFalse(); // Verify it was originally active

    }

    [Fact]
    public async Task Handle_PreserveOtherProperties_When_BiaRulesDeleted()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var originalDescription = existingBiaRule.Description;
        var originalType = existingBiaRule.Type;
        var originalEntityId = existingBiaRule.EntityId;
        var originalProperties = existingBiaRule.Properties;
        var originalRuleCode = existingBiaRule.RuleCode;

        var command = new DeleteBiaRulesCommand { Id = existingBiaRule.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BiaRules>(b =>
            b.ReferenceId == existingBiaRule.ReferenceId &&
            b.Description == originalDescription &&
            b.Type == originalType &&
            b.EntityId == originalEntityId &&
            b.Properties == originalProperties &&
            b.RuleCode == originalRuleCode &&
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponse_When_BiaRulesDeleted()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var command = new DeleteBiaRulesCommand { Id = existingBiaRule.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<DeleteBiaRulesResponse>();
        result.Message.ShouldBe("BIA Rules deleted successfully");
        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_DeleteRuleWithComplexProperties_When_ValidCommand()
    {
        // Arrange
        var complexRule = new Domain.Entities.BiaRules
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "Complex Rule with Multiple Properties",
            Type = "RTO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"threshold\":\"2\",\"unit\":\"hours\",\"escalation\":{\"level1\":\"1h\",\"level2\":\"2h\"},\"notifications\":[\"email\",\"sms\"],\"business_impact\":\"critical\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-7).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(14).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RTO_COMPLEX_DELETE",
            IsActive = true
        };
        _biaRulesFixture.BiaRules.Add(complexRule);

        var command = new DeleteBiaRulesCommand { Id = complexRule.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules deleted successfully");
        result.IsActive.ShouldBeFalse();

        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(
    It.Is<Domain.Entities.BiaRules>(b => b.ReferenceId == complexRule.ReferenceId)
));
    }
    
}