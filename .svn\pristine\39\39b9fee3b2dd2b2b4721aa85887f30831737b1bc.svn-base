﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Node.Events.PaginatedView;

public class NodePaginatedEventHandler : INotificationHandler<NodePaginatedEvent>
{
    private readonly ILogger<NodePaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public NodePaginatedEventHandler(ILoggedInUserService userService, ILogger<NodePaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(NodePaginatedEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.Node.ToString(),
            Action = $"{ActivityType.View} {Modules.Node}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Node viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Node viewed");
    }
}