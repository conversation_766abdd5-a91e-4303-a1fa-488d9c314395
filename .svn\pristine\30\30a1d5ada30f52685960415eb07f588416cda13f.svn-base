﻿using ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertReceiver.Queries;

public class GetAlertReceiverDetailQueryHandlerTests : IClassFixture<AlertReceiverFixture>
{
    private readonly AlertReceiverFixture _alertReceiverFixture;

    private readonly Mock<IAlertReceiverRepository> _mockAlertReceiverRepository;

    private readonly GetAlertReceiverDetailQueryHandler _handler;

    public GetAlertReceiverDetailQueryHandlerTests(AlertReceiverFixture alertReceiverFixture)
    {
        _alertReceiverFixture = alertReceiverFixture;

        _mockAlertReceiverRepository = AlertReceiverRepositoryMocks.GetAlertReceiverRepository(_alertReceiverFixture.AlertReceivers);

        _handler = new GetAlertReceiverDetailQueryHandler(_alertReceiverFixture.Mapper, _mockAlertReceiverRepository.Object);

        _alertReceiverFixture.AlertReceivers[0].IsActive = true;
    }

    [Fact]
    public async Task Handle_Return_AlertReceiverDetails_When_ValidAlertReceiverId()
    {
        var result = await _handler.Handle(new GetAlertReceiverDetailQuery { Id = _alertReceiverFixture.AlertReceivers[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<AlertReceiverDetailVm>();

        result.Id.ShouldBe(_alertReceiverFixture.AlertReceivers[0].ReferenceId);
        result.Name.ShouldBe(_alertReceiverFixture.AlertReceivers[0].Name);
        result.EmailAddress.ShouldBe(_alertReceiverFixture.AlertReceivers[0].EmailAddress);
        result.MobileNumber.ShouldBe(_alertReceiverFixture.AlertReceivers[0].MobileNumber);
        result.Properties.ShouldBe(_alertReceiverFixture.AlertReceivers[0].Properties);
        result.IsMail.ShouldBe(_alertReceiverFixture.AlertReceivers[0].IsMail);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidAlertReceiverId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetAlertReceiverDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetAlertReceiverDetailQuery { Id = _alertReceiverFixture.AlertReceivers[0].ReferenceId }, CancellationToken.None);

        _mockAlertReceiverRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}