﻿using ContinuityPatrol.Application.Features.BusinessService.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Create;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Update;
using ContinuityPatrol.Application.Features.DRCalendar.Events.PaginatedView;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DRCalender;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserListModels;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using Microsoft.Extensions.FileProviders;


namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class DRCalendarController : BaseController
{
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;
    private readonly ILogger<DRCalendarController> _logger;
    private readonly IPublisher _publisher;
   

    public DRCalendarController(IPublisher publisher, IMapper mapper, ILogger<DRCalendarController> logger, IDataProvider dataProvider)
    {
        _dataProvider = dataProvider;
        _logger = logger;
        _mapper = mapper;
        _publisher = publisher;
    }


    public async Task<IActionResult> List()
    {
        await _publisher.Publish(new DrCalendarPaginatedEvent());

        var businessServiceView = await _dataProvider.BusinessService.GetBusinessServiceList();

        var userList = await _dataProvider.User.GetUserNames();

        var profileList = await _dataProvider.WorkflowProfile.GetWorkflowProfileList();
        // Profile_list = _dataProvider.WorkflowProfileInfo.GetPaginatedWorkflowProfileInfos(new GetWorkflowProfileInfoPaginatedListQuery()).Result.Data;
        // var Unique_Profile = Profile_list.GroupBy(p => p.ProfileId).Select(a => a.First()).ToList();

            var newList = userList.Select(item => new DRCalendarUserList
            {
                
                Id = item.Id, LoginName = item.LoginName, IsChecked = false
            })
            .ToList();

        var paginatedUserList = new PaginatedResult<DRCalendarUserList>
        {
            Data = newList
        };




        var drCalender = new DrCalenderActivityViewModel
        {
            BusinessServices = businessServiceView,
            PaginatedUserList = paginatedUserList,
            PaginatedUserList2 = paginatedUserList,
           // PaginatedProfileList = profileList
        };
        return View(drCalender);
    }


    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(DrCalenderActivityViewModel drCalenderActivityViewModel)

    {
        var databaseId = Request.Form["id"].ToString();
       
        try
        {
            drCalenderActivityViewModel.CompanyId = WebHelper.UserSession.CompanyId;
            if (drCalenderActivityViewModel.File is not null)
            {
                var fileName = drCalenderActivityViewModel.File.FileName;
                drCalenderActivityViewModel.FileName = fileName;
            }

            if (string.IsNullOrWhiteSpace(databaseId))
            {
                //if (drCalenderActivityViewModel.FileName != "")
                //{
                //    var filepath = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "DRCalenderActivityDoc")).Root + $@"\{drCalenderActivityViewModel.FileName}";
                //   // System.Net.Mail.Attachment attachment;
                //   // attachment = new System.Net.Mail.Attachment(filepath);
                //   // msg.Attachments.Add(attachment);
                //}
               
                var drCalenderCreateCommand = _mapper.Map<CreateDrCalenderCommand>(drCalenderActivityViewModel);
                var response = await _dataProvider.DRCalenderService.CreateAsync(drCalenderCreateCommand);
                TempData.NotifySuccess(response.Message);
            }
            else
            {
                var drCalenderUpdateCommand = _mapper.Map<UpdateDrCalenderCommand>(drCalenderActivityViewModel);
                var response = await _dataProvider.DRCalenderService.UpdateAsync(drCalenderUpdateCommand);
                TempData.NotifySuccess(response.Message);
            }
            return RedirectToAction("List");
        }
        catch (ValidationException ex)
        {
            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> Delete(string id)
    {
        try
        {
          
            var deleteResponse = await _dataProvider.DRCalenderService.DeleteAsync(id);

            TempData.NotifySuccess(deleteResponse.Message);

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }
    [HttpGet]
    public async Task<JsonResult> DrCalendarPaginationList(GetDrCalendarPaginatedListQuery query)
    {
        try
        {
            var drCalendar = await _dataProvider.DRCalenderService.GetPaginatedDrCalendar(query);
            return Json(new { Success = true, data = drCalendar });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }
    // login name
    [HttpGet]
    public async Task<JsonResult> GetUserList()
    {
        try
        {
            var userName = await _dataProvider.User.GetUserNames();
            return Json(new { Success = true, data = userName });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public ContentResult DownloadFile(string fileName)
    {
        //Set the File Folder Path.

        var filepath = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "DRCalenderActivityDoc")).Root + $@"\{fileName}";

        //Read the File as Byte Array.
        byte[] bytes = System.IO.File.ReadAllBytes(filepath);

        //Convert File to Base64 string and send to Client.
        string base64 = Convert.ToBase64String(bytes, 0, bytes.Length);

        return Content(base64);
    }
   

    [HttpGet]
    public async Task<bool> IsActivityNameExist(string activityName, string id, DateTime scheduleStartTime)
    {
        try
        {
            var RES= await _dataProvider.DRCalenderService.IsDrCalendarNameExist(activityName, id, scheduleStartTime);
            return RES;
        }
        catch (Exception ex)
        {
            _logger.LogInformation("Facing issue while get IsActivityNameExist" + ex.Message);
            return false;
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetBusinessService(GetBusinessServicePaginatedListQuery query)
    {
        try
        {
            var businessServices = await _dataProvider.BusinessService.GetBusinessServiceList();
            return Json(new { Success = true, data = businessServices });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetProfileNamesByBusinessServiceId(string businessServiceId)
    {
        try
        {
            var workflowProfileInfo = await _dataProvider.WorkflowProfileInfo.WorkflowProfileInfoNames();

            var filterBusinessService = workflowProfileInfo.Where(x =>x.BusinessServiceId.IsNotNullOrWhiteSpace()&& x.BusinessServiceId.Equals(businessServiceId)).ToList();

            return Json(new { Success = true, data = filterBusinessService });
        }
        catch (Exception ex)
        {
            _logger.LogError($" Get Profile Names By BusinessServiceId {ex.GetMessage()}");

            return Json(new { Success = true, data = ex.GetMessage() });
        }
    }


    //[HttpPost]
    //[ValidateAntiForgeryToken]
    //public async Task<IActionResult> DRCalendarCreate(DrCalenderActivityListVm drCalendarActivityVm)
    //{


    //    _logger.LogInformation("Call DRCalendarCreate() ");
    //    if (drCalendarActivityVm.UploadFile == null)
    //    {
    //        //if(drCalendarActivityVm.FileName!=null)
    //        //{

    //        //}
    //        //else
    //        //{
    //        //    ModelState.AddModelError("UploadFile", "Please Upload File");
    //        //    return RedirectToAction("DRCalendar");
    //        //}
    //    }

    //    if (ModelState.IsValid)
    //    {
    //        BaseResponse result;
    //        if (drCalendarActivityVm.Id == "0")
    //        {
    //            _logger.LogInformation("Save DRCalendar Activity ");
    //            drCalendarActivityVm.Id = "";
    //            string ifilename = "";
    //            if (drCalendarActivityVm.UploadFile != null)
    //            {
    //                var fileName = Path.GetFileNameWithoutExtension(drCalendarActivityVm.UploadFile.FileName);

    //                var fileExtension = Path.GetExtension(drCalendarActivityVm.UploadFile.FileName);

    //                fileName = fileName.Trim() + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + fileExtension;

    //                var filePath = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "DRCalenderActivityDoc")).Root + $@"\{fileName}";

    //                await using (var fs = System.IO.File.Create(filePath))
    //                {
    //                    await drCalendarActivityVm.UploadFile.CopyToAsync(fs);
    //                    fs.Flush();
    //                }
    //                ifilename = fileName;
    //                drCalendarActivityVm.FileName = fileName;
    //            }
    //            drCalendarActivityVm.CompanyId = LoggedInUserCompanyId;

    //            string userIds = drCalendarActivityVm.UserIds[0];//.Aggregate(string.Empty, (current, item) => current + (item + ","));
    //            drCalendarActivityVm.Recipient2 = userIds.TrimEnd(',');
    //            string[] usres = drCalendarActivityVm.Recipient2.Split(",");

    //            string profiles = drCalendarActivityVm.wfprofiles[0];
    //            drCalendarActivityVm.Profiles = profiles.TrimEnd(',');

    //            string usermailcc = string.Empty;
    //            for (int i = 0; i < usres.Length - 1; i++)
    //            {
    //                //GetByReferenceId
    //                UserDetailVm userinfo = await _dataProvider.User.GetByReferenceId(usres[i].ToString().Trim());
    //                usermailcc += userinfo.UserInfo.Email.Trim() + ';';
    //            }
    //            drCalendarActivityVm.ActivityName = drCalendarActivityVm.ActivityName;
    //            var calenderCommand = _mapper.Map<CreateDrCalenderCommand>(drCalendarActivityVm);
    //            result = await _dataProvider.DRCalenderService.CreateAsync(calenderCommand);
    //            //Send  Mail 

    //            _logger.LogInformation("Call Send Mail SendEmailAsync() ... ");
    //            await SendEmailAsync(drCalendarActivityVm.ScheduledStartDate, drCalendarActivityVm.ScheduledEndDate, drCalendarActivityVm.ActivityName, drCalendarActivityVm.BusinessServiceId, drCalendarActivityVm.Profiles, usermailcc, ifilename);
    //        }
    //        else
    //        {
    //            _logger.LogInformation("Udpate DRCalendar Activity... ");
    //            if (drCalendarActivityVm.UploadFile != null)
    //            {
    //                var fileName = Path.GetFileNameWithoutExtension(drCalendarActivityVm.UploadFile.FileName);

    //                var fileExtension = Path.GetExtension(drCalendarActivityVm.UploadFile.FileName);

    //                fileName = fileName.Trim() + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + fileExtension;
    //                var filepath = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "DRCalenderActivityDoc")).Root + $@"\{fileName}";
    //                await using (var fs = System.IO.File.Create(filepath))
    //                {
    //                    await drCalendarActivityVm.UploadFile.CopyToAsync(fs);
    //                    fs.Flush();
    //                }
    //                drCalendarActivityVm.FileName = fileName;
    //            }
    //            else
    //            {
    //                drCalendarActivityVm.FileName = "";
    //            }

    //            drCalendarActivityVm.CompanyId = LoggedInUserCompanyId;

    //            if (drCalendarActivityVm.UserIds[0] != null)
    //            {
    //                string userIds = drCalendarActivityVm.UserIds[0];
    //                drCalendarActivityVm.Recipient2 = userIds.TrimEnd(',');
    //            }
    //            if (drCalendarActivityVm.wfprofiles[0] != null)
    //            {
    //                string wfprofile = drCalendarActivityVm.wfprofiles[0];
    //                drCalendarActivityVm.Profiles = wfprofile.TrimEnd(',');
    //            }


    //            var drCalendarUpCommand = _mapper.Map<UpdateDrCalenderCommand>(drCalendarActivityVm);

    //            result = await _dataProvider.DRCalenderService.UpdateAsync(drCalendarUpCommand);
    //        }

    //        return RouteToPostView(result);
    //    }
    //    // SendEmailAsync(drCalendarActivityVm.ScheduledStartDate, drCalendarActivityVm.ScheduledEndDate, drCalendarActivityVm.ActivityName, drCalendarActivityVm.BusinessServiceId, drCalendarActivityVm.Profiles, usermailcc);
    //    //ModelState.AddModelError("ActivityName", "Name is required");
    //    return RedirectToAction("DRCalendar");
    //}


    ////[HttpGet]
    ////public async Task<JsonResult> GetPaginationList()
    ////{
    ////    var result = await _dataProvider.DRCalenderService.GetPaginatedDrCalendar();
    ////    return result != null ? Json(result.ToArray()) : new JsonResult("");
    ////}


    //public async Task<Task> SendEmailAsync(DateTime startdate, DateTime enddate, string activityname, string bsname, string profilename, string mailcc, string file)
    //{
    //    try
    //    {

    //        SmtpConfigurationListVm smtpConfigurationListVms = await _dataProvider.SmtpConfiguration.GetSmtpConfigurationList();

    //        // string _from = "<EMAIL>";
    //        string from = smtpConfigurationListVms.UserName;
    //        string subj = "DR Activity [" + activityname + "] ";// + DateTime.Now.ToString("G");
    //        string body = "Hi Team ,<br/><br/>DR Activity For Bussiness Service:" + bsname + " <br/><br/>Profile Name: " + profilename + " <br/><br/>Schedule Start Date :-" + startdate + " And Schedule  End Date:-" + enddate + "<br/><br/>"
    //                + "Regards<br/>Continuity Patrol";
    //        body = CheckWellFormedHtml(body);

    //        MailMessage msg = new MailMessage();
    //        msg.From = new MailAddress(from);
    //        mailcc = mailcc.TrimEnd(';');
    //        string[] ccId = mailcc.Split(';');
    //        foreach (string ccEmail in ccId)
    //        {
    //            msg.To.Add(ccEmail);
    //            //msg.CC.Add(new MailAddress(CCEmail)); //Adding Multiple CC email Id  
    //        }

    //        msg.Subject = subj;

    //        AlternateView avBody = AlternateView.CreateAlternateViewFromString(body, Encoding.UTF8, MediaTypeNames.Text.Html);
    //        msg.AlternateViews.Add(avBody);
    //        //string startTime1 = Convert.ToDateTime(startdate).ToString("yyyyMMddTHHmmssZ");
    //        //string endTime1 = Convert.ToDateTime(enddate).ToString("yyyyMMddTHHmmssZ");
    //        // Generate Calendar Invite ---------------------------------------------------  
    //        StringBuilder str = new StringBuilder();
    //        str.AppendLine("BEGIN:VCALENDAR");
    //        str.AppendLine("PRODID:-//Schedule a Meeting");
    //        str.AppendLine("VERSION:2.0");
    //        str.AppendLine("METHOD:REQUEST");
    //        str.AppendLine("BEGIN:VTIMEZONE");

    //        str.AppendLine("TZID:Asia/Kolkata");
    //        str.AppendLine("TZNAME:IST");
    //        str.AppendLine("BEGIN:STANDARD");
    //        str.AppendLine("TZOFFSETFROM:+0530");
    //        str.AppendLine("TZOFFSETTO:+0530");
    //        str.AppendLine("END:STANDARD");
    //        str.AppendLine("END:VTIMEZONE");
    //        str.AppendLine("BEGIN:VEVENT");
    //        str.AppendLine(string.Format("DTSTART:{0:yyyyMMddTHHmmssZ}", startdate.ToUniversalTime()));
    //        str.AppendLine(string.Format("DTSTAMP:{0:yyyyMMddTHHmmss}", DateTime.Now));
    //        str.AppendLine(string.Format("DTEND:{0:yyyyMMddTHHmmssZ}", enddate.ToUniversalTime()));
    //        str.AppendLine("LOCATION: " + "Pune");
    //        str.AppendLine(string.Format("UID:{0}", Guid.NewGuid()));
    //        str.AppendLine(string.Format("DESCRIPTION:{0}", "Metting Schedule From Date:-" + startdate + " To Date:-" + enddate));
    //        str.AppendLine(string.Format("X-ALT-DESC;FMTTYPE=text/html:{0}", msg.Body));
    //        str.AppendLine(string.Format("SUMMARY:{0}", msg.Subject));
    //        str.AppendLine(string.Format("ORGANIZER:MAILTO:{0}", msg.From.Address));
    //        //str.AppendLine(string.Format("RRULE:FREQ=WEEKLY;UNTIL={0:yyyyMMddTHHmmssZ}", utcEtime));
    //        str.AppendLine(string.Format("ATTENDEE;CN=\"{0}\";RSVP=TRUE:mailto:{1}", msg.To[0].DisplayName, msg.To[0].Address));
    //        str.AppendLine("X-MICROSOFT-CDO-BUSYSTATUS:BUSY");
    //        str.AppendLine("BEGIN:VALARM");
    //        str.AppendLine("TRIGGER:-PT15M");
    //        str.AppendLine("ACTION:DISPLAY");
    //        str.AppendLine("DESCRIPTION:Reminder");
    //        str.AppendLine("END:VALARM");
    //        str.AppendLine("END:VEVENT");
    //        str.AppendLine("END:VCALENDAR");


    //        // Attach Calendar Invite ------------------------------------------------------  
    //        byte[] byteArray = Encoding.ASCII.GetBytes(str.ToString());
    //        MemoryStream stream = new MemoryStream(byteArray);
    //        Attachment attach = new Attachment(stream, "invite.ics");
    //        attach.TransferEncoding = TransferEncoding.QuotedPrintable;
    //        msg.Attachments.Add(attach);

    //        ContentType contype = new ContentType("text/calendar");
    //        contype.CharSet = "UTF-8";
    //        contype.Parameters.Add("method", "REQUEST");
    //        contype.Parameters.Add("name", "invite.ics");

    //        AlternateView avCal = AlternateView.CreateAlternateViewFromString(str.ToString(), contype);
    //        avCal.TransferEncoding = TransferEncoding.QuotedPrintable;
    //        msg.AlternateViews.Add(avCal);


    //        //// File Attachment --------------------------------------------------------------  
    //        if (file != "")
    //        {
    //            var filepath = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "DRCalenderActivityDoc")).Root + $@"\{file}";
    //            System.Net.Mail.Attachment attachment;
    //            attachment = new System.Net.Mail.Attachment(filepath);
    //            msg.Attachments.Add(attachment);
    //        }

    //        //Now sending a mail with attachment ICS file. ----------------------------------  
    //        SmtpClient smtpclient = new SmtpClient();
    //        smtpclient.Host = smtpConfigurationListVms.SmtpHost.Trim();// "smtp.logix.in"; //-------this has to given the Mailserver IP  
    //        smtpclient.EnableSsl = true;
    //        smtpclient.Port = Convert.ToInt32(smtpConfigurationListVms.Port);
    //        smtpclient.Credentials = new System.Net.NetworkCredential(from, SecurityHelper.Decrypt(smtpConfigurationListVms.Password));

    //        smtpclient.Send(msg);


    //    }
    //    catch (Exception ex)
    //    {
    //        _logger.LogInformation("Facing Issue on DRCalender Page while Sending Mail  " + ex.Message);
    //    }

    //    return Task.CompletedTask;
    //}

    //public static string CheckWellFormedHtml(string txt)
    //{
    //    if (txt == null)
    //        return "";
    //    else
    //    {
    //        StringBuilder htmlTop = new StringBuilder();
    //        StringBuilder htmlBottom = new StringBuilder();

    //        if (!txt.ToUpper().Contains("<!DOCTYPE"))
    //        {
    //            if (!txt.ToUpper().Contains("<HTML>") & !txt.ToUpper().Contains("< HTML>") & !txt.ToUpper().Contains("<HTML >") & !txt.ToUpper().Contains("< HTML >"))
    //                htmlTop.Append("<html>");
    //            if (!txt.ToUpper().Contains("<HEAD>") & !txt.ToUpper().Contains("< HEAD>") & !txt.ToUpper().Contains("<HEAD >") & !txt.ToUpper().Contains("< HEAD >"))
    //                htmlTop.Append("<head>");
    //            if (!txt.ToUpper().Contains("<TITLE>") & !txt.ToUpper().Contains("< TITLE>") & !txt.ToUpper().Contains("<TITLE >") & !txt.ToUpper().Contains("< TITLE >"))
    //                htmlTop.Append("<title>Untitled Document</title>");
    //            if (!txt.ToUpper().Contains("</HEAD>") & !txt.ToUpper().Contains("</ HEAD>") & !txt.ToUpper().Contains("</HEAD >") & !txt.ToUpper().Contains("</ HEAD >"))
    //                htmlTop.Append("</head>");
    //            if (!txt.ToUpper().Contains("<BODY>") & !txt.ToUpper().Contains("< BODY>") & !txt.ToUpper().Contains("<BODY >") & !txt.ToUpper().Contains("< BODY >"))
    //                htmlTop.Append("<body>");

    //            // Html at the bottom of the email  
    //            if (!txt.ToUpper().Contains("</BODY>") & !txt.ToUpper().Contains("</ BODY>") & !txt.ToUpper().Contains("</BODY >") & !txt.ToUpper().Contains("</ BODY >"))
    //                htmlBottom.Append("</body>");
    //            if (!txt.ToUpper().Contains("</HTML>") & !txt.ToUpper().Contains("</ HTML>") & !txt.ToUpper().Contains("</HTML >") & !txt.ToUpper().Contains("</ HTML >"))
    //                htmlBottom.Append("</html>");

    //            txt = htmlTop.ToString() + txt + htmlBottom.ToString();
    //        }

    //        return txt;
    //    }
    //}


    //[HttpGet]
    //public async Task<JsonResult> CurrentactivityPaginationList()
    //{
    //    DateTime dt = DateTime.Now;
    //    int date = dt.Day;
    //    int month = dt.Month;
    //    int dayval = 31;
    //    if (month==2)
    //        dayval=29;
    //    _logger.LogInformation("CurrentactivityPaginationList"+dt.ToDateTime());
    //    var result = (await _dataProvider.DRCalenderService.GetDrCalActivityPaginatedList()).Where(x => x.ScheduledStartDate >= new DateTime(DateTime.Now.Year, DateTime.Now.Month, date) &&  x.ScheduledStartDate <= new DateTime(DateTime.Now.Year, DateTime.Now.Month, dayval)).OrderBy(x => x.ScheduledStartDate);

    //    return result != null ? Json(result.ToArray()) : new JsonResult("");
    //}

    //[HttpPost]
    //[ValidateAntiForgeryToken]
    //[AntiXss]
    //public ContentResult DownloadFile(string fileName)
    //{
    //    //Set the File Folder Path.

    //    var filepath = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "DRCalenderActivityDoc")).Root + $@"\{fileName}";

    //    //Read the File as Byte Array.
    //    byte[] bytes = System.IO.File.ReadAllBytes(filepath);

    //    //Convert File to Base64 string and send to Client.
    //    string base64 = Convert.ToBase64String(bytes, 0, bytes.Length);

    //    return Content(base64);
    //}
    //private IActionResult RouteToPostView(BaseResponse result)
    //{

    //    if (result.Success)
    //    {
    //        TempData.Set(new NotificationMessage(NotificationType.Success, result.Message));

    //        return RedirectToAction("DRCalendar");
    //    }
    //    TempData.Set(result.Success
    //        ? new NotificationMessage(NotificationType.Success, result.Message)
    //        : new NotificationMessage(NotificationType.Error, result.Message)
    //    );

    //    return RedirectToAction("DRCalendar", "DRCalendar", new { Area = "Configuration" });
    //}



    //public async Task<IActionResult> DRCalendarNew()
    //{
    //    var businessServiceView = await _businessService.GetBusinessServicePaginatedList(new GetBusinessServicePaginatedListQuery());
    //    var userList = await _userPaginatedList.GetUserPaginatedList(new GetUserPaginatedListQuery());
    //    var profileList = await _profileList.GetWorkflowProfileList();

    //    List<DRCalendarUserList> newList = new List<DRCalendarUserList>();

    //    foreach (UserListVm item in userList.Data)
    //    {
    //        DRCalendarUserList u = new DRCalendarUserList();
    //        // _mapper.Map(item, u);
    //        u.Id = item.Id;
    //        u.LoginName = item.LoginName;
    //        u.IsChecked = false;

    //        newList.Add(u);
    //    }

    //    var paginatedResult = new PaginatedResult<DRCalendarUserList>
    //    {
    //        Data = newList
    //    };


    //    var drCalender = new DrCalenderActivityListVm
    //    {
    //        PaginatedBusinessService = businessServiceView,
    //        PaginatedUserList = paginatedResult,
    //        PaginatedProfileList = profileList
    //    };
    //    return View(drCalender);
    //}

    //[HttpGet]
    //public async Task<bool> IsActivityNameExist(string activityName, string id)
    //{
    //    try
    //    {
    //        return await _dataProvider.DRCalenderService.IsActivityNameExist(activityName, id);
    //    }
    //    catch (Exception ex) 
    //    {
    //        _logger.LogInformation("Facing issue while get IsActivityNameExist"+ex.Message);
    //        return false;
    //    }
    //}



    //[HttpGet]
    //public Task<IActionResult>  GetProfilesName(string businessService)
    //{
    //    try
    //    {


    //        List<WorkflowProfileInfoListVm> profileList = new List<WorkflowProfileInfoListVm>();
    //        profileList = _dataProvider.WorkflowProfileInfo.GetPaginatedWorkflowProfileInfos(new GetWorkflowProfileInfoPaginatedListQuery()).Result.Data;
    //        var uniqueProfile = profileList.GroupBy(p => p.ProfileId).Select(a => a.First()).ToList();
    //        var uniqueProfile1 = from p in uniqueProfile where p.BusinessServiceName== businessService select new { p.ProfileId, p.ProfileName }; ;
    //        //return await _dataProvider.DRCalenderService.IsActivityNameExist(ActivityName, id);
    //        return Task.FromResult<IActionResult>(Json(uniqueProfile1));
    //    }
    //    catch (Exception ex ) 
    //    {
    //        _logger.LogInformation(" BusinessService:"+businessService+" Profile Not found ");
    //        _logger.LogInformation("Facing issue while get Profile name"+ex.Message);
    //        return Task.FromResult<IActionResult>(null);
    //    }
    //}


}