﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.CyberComponentGroupModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetPaginatedList;

public class GetCyberComponentGroupPaginatedListQueryHandler : IRequestHandler<GetCyberComponentGroupPaginatedListQuery,
    PaginatedResult<CyberComponentGroupListVm>>
{
    private readonly ICyberComponentGroupRepository _cyberComponentGroupRepository;
    private readonly IMapper _mapper;

    public GetCyberComponentGroupPaginatedListQueryHandler(ICyberComponentGroupRepository cyberComponentGroupRepository,
        IMapper mapper)
    {
        _cyberComponentGroupRepository = cyberComponentGroupRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<CyberComponentGroupListVm>> Handle(
        GetCyberComponentGroupPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var productFilterSpec = new CyberComponentGroupFilterSpecification(request.SearchString);

        var queryable = await _cyberComponentGroupRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var cyberComponentGroupList = _mapper.Map<PaginatedResult<CyberComponentGroupListVm>>(queryable);

        return cyberComponentGroupList;
        //var queryable = _cyberComponentGroupRepository.GetPaginatedQuery();

        //var productFilterSpec = new CyberComponentGroupFilterSpecification(request.SearchString);

        //var cyberComponentGroupList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<CyberComponentGroupListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return cyberComponentGroupList;
    }
}