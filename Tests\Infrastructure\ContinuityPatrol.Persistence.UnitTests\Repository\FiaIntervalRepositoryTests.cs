using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class FiaIntervalRepositoryTests : IClassFixture<FiaIntervalFixture>, IDisposable
{
    private readonly FiaIntervalFixture _fiaIntervalFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly FiaIntervalRepository _repository;

    public FiaIntervalRepositoryTests(FiaIntervalFixture fiaIntervalFixture)
    {
        _fiaIntervalFixture = fiaIntervalFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _repository = new FiaIntervalRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddFiaInterval_WhenValidInterval()
    {
        // Arrange
        var interval = _fiaIntervalFixture.FiaIntervalDto;
        interval.MinTime = 1;
        interval.MaxTime = 24;
        interval.MinTimeUnit = 1; // Hours
        interval.MaxTimeUnit = 1; // Hours

        // Act
        var result = await _repository.AddAsync(interval);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(interval.MinTime, result.MinTime);
        Assert.Equal(interval.MaxTime, result.MaxTime);
        Assert.Equal(interval.MinTimeUnit, result.MinTimeUnit);
        Assert.Equal(interval.MaxTimeUnit, result.MaxTimeUnit);
        Assert.Single(_dbContext.FiaIntervals);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenIntervalIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsInterval_WhenExists()
    {
        // Arrange
        _fiaIntervalFixture.FiaIntervalDto.Id = 1;
        _fiaIntervalFixture.FiaIntervalDto.MinTime = 2;
        _fiaIntervalFixture.FiaIntervalDto.MaxTime = 48;

        await _dbContext.FiaIntervals.AddAsync(_fiaIntervalFixture.FiaIntervalDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_fiaIntervalFixture.FiaIntervalDto.Id, result.Id);
        Assert.Equal(_fiaIntervalFixture.FiaIntervalDto.MinTime, result.MinTime);
        Assert.Equal(_fiaIntervalFixture.FiaIntervalDto.MaxTime, result.MaxTime);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsInterval_WhenExists()
    {
        // Arrange
        var referenceId = Guid.NewGuid().ToString();
        _fiaIntervalFixture.FiaIntervalDto.ReferenceId = referenceId;
        _fiaIntervalFixture.FiaIntervalDto.MinTime = 4;
        _fiaIntervalFixture.FiaIntervalDto.MaxTime = 72;

        await _dbContext.FiaIntervals.AddAsync(_fiaIntervalFixture.FiaIntervalDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal(_fiaIntervalFixture.FiaIntervalDto.MinTime, result.MinTime);
        Assert.Equal(_fiaIntervalFixture.FiaIntervalDto.MaxTime, result.MaxTime);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        var nonExistentReferenceId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentReferenceId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateInterval_WhenValidInterval()
    {
        // Arrange
        _fiaIntervalFixture.FiaIntervalDto.MinTime = 1;
        _fiaIntervalFixture.FiaIntervalDto.MaxTime = 12;
        _fiaIntervalFixture.FiaIntervalDto.MinTimeUnit = 1;
        _fiaIntervalFixture.FiaIntervalDto.MaxTimeUnit = 1;

        _dbContext.FiaIntervals.Add(_fiaIntervalFixture.FiaIntervalDto);
        await _dbContext.SaveChangesAsync();

        // Update the interval
        _fiaIntervalFixture.FiaIntervalDto.MinTime = 2;
        _fiaIntervalFixture.FiaIntervalDto.MaxTime = 24;
        _fiaIntervalFixture.FiaIntervalDto.MinTimeUnit = 2;
        _fiaIntervalFixture.FiaIntervalDto.MaxTimeUnit = 2;

        // Act
        var result = await _repository.UpdateAsync(_fiaIntervalFixture.FiaIntervalDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.MinTime);
        Assert.Equal(24, result.MaxTime);
        Assert.Equal(2, result.MinTimeUnit);
        Assert.Equal(2, result.MaxTimeUnit);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenIntervalIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity_WhenIntervalExists()
    {
        // Arrange
        _dbContext.FiaIntervals.Add(_fiaIntervalFixture.FiaIntervalDto);
        await _dbContext.SaveChangesAsync();
        var initialCount = _dbContext.FiaIntervals.Count();

        // Act
        var result = await _repository.DeleteAsync(_fiaIntervalFixture.FiaIntervalDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(initialCount - 1, _dbContext.FiaIntervals.Count());
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenIntervalIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    [Fact]
    public async Task ListAllAsync_ReturnsAllActiveIntervals_WhenIntervalsExist()
    {
        // Arrange
        _fiaIntervalFixture.FiaIntervalList[0].MinTime = 1;
        _fiaIntervalFixture.FiaIntervalList[0].MaxTime = 8;
        _fiaIntervalFixture.FiaIntervalList[1].MinTime = 8;
        _fiaIntervalFixture.FiaIntervalList[1].MaxTime = 24;
        _fiaIntervalFixture.FiaIntervalList[2].MinTime = 24;
        _fiaIntervalFixture.FiaIntervalList[2].MaxTime = 72;

        await _dbContext.FiaIntervals.AddRangeAsync(_fiaIntervalFixture.FiaIntervalList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoIntervalsExist()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task FindByFilterAsync_ReturnsMatchingIntervals_WhenFilterMatches()
    {
        // Arrange
        _fiaIntervalFixture.FiaIntervalList[0].MinTime = 1;
        _fiaIntervalFixture.FiaIntervalList[0].MaxTime = 4;
        _fiaIntervalFixture.FiaIntervalList[1].MinTime = 4;
        _fiaIntervalFixture.FiaIntervalList[1].MaxTime = 8;
        _fiaIntervalFixture.FiaIntervalList[2].MinTime = 8;
        _fiaIntervalFixture.FiaIntervalList[2].MaxTime = 24;

        await _dbContext.FiaIntervals.AddRangeAsync(_fiaIntervalFixture.FiaIntervalList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.FindByFilterAsync(i => i.MaxTime <= 8);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.MaxTime <= 8));
    }

    [Fact]
    public async Task FindByFilterAsync_ReturnsEmpty_WhenNoMatches()
    {
        // Arrange
        await _dbContext.FiaIntervals.AddRangeAsync(_fiaIntervalFixture.FiaIntervalList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.FindByFilterAsync(i => i.MinTime > 1000);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public void QueryAll_ReturnsQueryable_WhenCalled()
    {
        // Act
        var result = _repository.QueryAll(i => i.IsActive);

        // Assert
        Assert.NotNull(result);
        Assert.IsAssignableFrom<IQueryable<Domain.Entities.FiaInterval>>(result);
    }


    [Fact]
    public async Task FilterBy_ReturnsFilteredQueryable_WhenCalled()
    {
        // Arrange
        _fiaIntervalFixture.FiaIntervalList[0].MinTimeUnit = 1; // Hours
        _fiaIntervalFixture.FiaIntervalList[1].MinTimeUnit = 2; // Days
        _fiaIntervalFixture.FiaIntervalList[2].MinTimeUnit = 1; // Hours

        await _dbContext.FiaIntervals.AddRangeAsync(_fiaIntervalFixture.FiaIntervalList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = _repository.FilterBy(i => i.MinTimeUnit == 1).ToList();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(1, x.MinTimeUnit));
    }

    #endregion
}
