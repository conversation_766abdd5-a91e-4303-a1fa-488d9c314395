﻿let createBiaPermission = $("#ConfigurationBIACreate").data("create-permission").toLowerCase();
let deleteBiaPermission = $("#ConfigurationBIADelete").data("delete-permission").toLowerCase();
if (createBiaPermission == 'false') {
    $("#createBiaModal").removeClass('#createBiaModal').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('icon-disabled', createBiaPermission == 'false');
}
if (createBiaPermission == 'false') {
    $("#createfirstmodal").removeClass('#createfirstmodal').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('icon-disabled', createBiaPermission == 'false');
}
if (createBiaPermission == 'false') {
    $("#biaCreateSecondModal").removeClass('#biaCreateSecondModal').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('icon-disabled', createBiaPermission == 'false');
}
let biaUrls = {
    GetBiaImpactList: "Configuration/BiaRules/GetBiaImpactList",
    GetBiaRulesByentityIdAndType: "Configuration/BiaRules/GetBiaRulesByentityIdAndType",
    GetBusinessFunctionList: "Configuration/BiaRules/GetBusinessFunctionList",
    GetBusinessServiceList: "Configuration/BiaRules/GetBusinessServiceList",
    GetInfraObjectByBusinessServiceId: "Configuration/BiaRules/GetInfraObjectByBusinessServiceId",
    GetInfraObjectById: "Configuration/BiaRules/GetInfraObjectById",
    CreateOrUpdate: "Configuration/BiaRules/CreateOrUpdate",
    Delete: "Configuration/BiaRules/Delete",
    GetBIABusinessServiceTreeViewListByBusinessServiceId: "Configuration/BiaRules/GetBIABusinessServiceTreeViewListByBusinessServiceId",
}
let globalInfraBfId = '', globalInfratoBfDeleteId = '', globalBftoBfDeleteId = '', globalBfBfId = '', globalBSBSId = '', globalBstoBsDeleteId = '', affectBs = null, affectBf = null, treeBf = "", treeBs = "", arrInfraToBf = [], arrBfToBf = [], arrBsToBs = [], infraToBfTree = "", impactInfraBfStatus = "", impactInfraBfId = [], image = "<img src='../../img/isomatric/Infrastructure_View_No_Data_Found.svg' class='img-fluid mx-auto' >", treeInfraBf = "", arrayData = [], assignData = "", assignType = ""
function clearVal() {
    $("#biaInfraBFDescription,#biaBFBFDescription,#biaBSBSDescription").val("")
    $("#biaInfraBs,#biaInfraInfraObject,#biaInfraInfraComponent,#biaInfraImpact,#biaInfraBusinessFunction,#bia-biaInfraBs1,#biaInfraImpact1,#biaInfraDate,#biaBfBusinessFunction,#biaBfImpact,#biaBfBusinessFunction1,#biaBfImpact1,#biaBfDate,#biaBsBusinessService,#biaBsImpact,#biaBsInfraComponent,#biaBsDate").val("").trigger('change')
}
function clearBiaruleErrorElements() {
    $("#biaInfraBsError,#biaInfraInfraObjectError,#biaInfraInfraComponentError,#biaInfraBusinessFunctionError,#biaInfraImpactError,#biaInfraBs1Error,#biaInfraImpact1Error,#biaInfraDateError,#biaBfBusinessFunctionError,#biaBfImpactError,#biaBfBusinessFunction1Error,#biaBfImpact1Error,#biaBfDateError,#biaBsBusinessServiceError,#biaBsImpactError,#biaBsInfraComponentError,#biaBsDateError").text('').removeClass('field-validation-error');
}
$(".biaBtnCancel").on("click", function () {
    clearVal()
    clearBiaruleErrorElements()
    $("#biaInfraBfSave,#biaBfBfSave,#biaBsBsSave").text("Save")
})
const getBia = async (scheDule) => {
    await $.ajax({
        type: "GET",
        url: RootUrl + biaUrls?.GetBiaImpactList,
        dataType: "json",
        success: function (result) {
            let data = result?.data
            if (result?.success) {
                BindList(data, scheDule)
            } else {
                errorNotification(result)
            }
        },
    })
}
function BindList(data, scheDule) {
    arrInfraToBf = []
    arrBfToBf = []
    arrBsToBs = []
    infraToBfTree = data
    $("#biaInfraToBfTable tbody,#biaBfToBfTable tbody,#biaBsBsTable tbody").empty()
    let datas = "", datas1 = "", datas2 = "", d = "", sno = 1, sno1 = 1, sno2 = 1
    $('#biaTreeBS').empty().append('<option value=""></option>')
    data?.forEach(function (item, i) {
        if (item?.type == 1) {
            let jsonstring = JSON.parse(item?.properties)
            jsonstring?.forEach(function (parse, i) {
                d = parse
            })
            arrInfraToBf.push(item)
            let addclass, addclass1
            if (d?.IsNotAvailableThen?.BusinessFunctionImpact == "Partial") {
                addclass = "text-bg-warning"
            } else if (d?.IsNotAvailableThen?.BusinessFunctionImpact == "Total") {
                addclass = "text-bg-danger"
            } else {
                addclass = `background:#E76802`
            }
            if (d?.then?.BusinessServiceImpact == "Partial") {
                addclass1 = "text-bg-warning"
            } else if (d?.then?.BusinessServiceImpact == "Total") {
                addclass1 = "text-bg-danger"
            } else {
                addclass1 = `background:#E76802`
            }
            impactInfraBfStatus = d?.ImpactStatus
            impactInfraBfId.push(d?.InfraObjectId)
            if (d?.BusinessServiceName) {
                $('#biaTreeBS').append('<option title="' + d?.BusinessServiceName + '" value="' + d?.BusinessServiceId + '">' + d?.BusinessServiceName + '</option>')
                $("#biaTreeBS")?.each(function () {
                    $(this).siblings('[value="' + this.value + '"]').remove()
                })
            } else {
                $('#biaTreeBS').append('<option value=""></option>')
            }
            datas += '<tr>'
            datas += '<td class="text-truncate" title="">' + sno + '</td>'
            datas += '<td class="text-truncate" title="' + d?.InfraObjectName + '">' + d?.InfraObjectName + '</td>'
            datas += '<td class="text-truncate" title="">NA</td>'
            datas += '<td class="text-truncate" title="">NA</td>'
            datas += '<td class="text-truncate" title=""><span class="badge text-bg-danger me-1 text-white">' + d?.InfraComponentName + '</span></td>'
            datas += '<td class="text-truncate" title="Datapower will be">' + d?.IsNotAvailableThen?.BusinessFunctionName + '</td>'
            datas += '<td class="text-truncate" title="" ><span style="' + addclass + '" class="badge ' + addclass + '  me-1 text-white">' + d?.IsNotAvailableThen?.BusinessFunctionImpact + ' Impacted</span> and</td>'
            datas += '<td class="text-truncate" title="">' + d?.BusinessServiceName + '</td>'
            datas += '<td class="text-truncate" title=""><span  style="' + addclass1 + '" class="badge ' + addclass1 + '  me-1 text-white">' + d?.then?.BusinessServiceImpact + ' Impacted</span></td>'
            datas += '<td class="text-truncate" title="">'
            datas += '<div class="d-flex align-items-center gap-2">'
            if (createBiaPermission === 'true' && deleteBiaPermission === "true") {
                datas += '<span role="button" title="Edit" class="database-edit-button" data-bs-toggle="modal" data-bs-target="#biaCreateModal"  name="Infraupdatename" date=' + item?.effectiveDateTo + ' infraimpact1=' + d?.then?.BusinessServiceImpact + ' infrabs=' + d?.then?.BusinessServiceId + ' infraimpact=' + d?.IsNotAvailableThen?.BusinessFunctionImpact + ' infrabf=' + d?.IsNotAvailableThen?.BusinessFunctionId + '  infracomponent="' + d?.InfraComponentId + '" infra=' + d?.InfraObjectId + ' bs=' + d?.BusinessServiceId + ' description=' + item?.description + ' updateId="' + item?.id + '" onclick="infraBfEdit(this)">'
                datas += '<i class="cp-edit"></i>'
                datas += '</span>'
                datas += '<span role="button" title="Delete" class="database-delete-button" onclick="infraToBsdelete(this)"  deleteId="' + item?.id + '" delete_name="' + d?.InfraObjectName + '" data-bs-toggle="modal" data-bs-target="#biaDeleteModal">'
                datas += '<i class="cp-Delete"></i>'
                datas += '</span>'
            }
            else if (createBiaPermission === 'true' && deleteBiaPermission === "false") {
                datas += '<span role="button" title="Edit" class="database-edit-button" data-bs-toggle="modal" data-bs-target="#biaCreateModal" name="Infraupdatename" date=' + item?.effectiveDateTo + ' infraimpact1=' + d?.then?.BusinessServiceImpact + ' infrabs=' + d?.then?.BusinessServiceId + ' infraimpact=' + d?.IsNotAvailableThen?.BusinessFunctionImpact + ' infrabf=' + d?.IsNotAvailableThen?.BusinessFunctionId + '  infracomponent="' + d?.InfraComponentId + '" infra=' + d?.InfraObjectId + ' bs=' + d?.BusinessServiceId + ' description=' + item?.description + ' updateId="' + item?.id + '" onclick="infraBfEdit(this)">'
                datas += '<i class="cp-edit"></i>'
                datas += '</span>'
                datas += '<span role="button" title="Delete" class="icon-disabled">'
                datas += '<i class="cp-Delete"></i>'
                datas += '</span>'
            }
            else if (createBiaPermission === 'false' && deleteBiaPermission === "true") {
                datas += '<span role="button" title="Edit" class="icon-disabled">'
                datas += '<i class="cp-edit"></i>'
                datas += '</span>'
                datas += '<span role="button" title="Delete" class="database-delete-button" onclick="infraToBsdelete(this)"  deleteId="' + item?.id + '" delete_name="' + d?.InfraObjectname + '" data-bs-toggle="modal" data-bs-target="#biaDeleteModal">'
                datas += '<i class="cp-Delete"></i>'
                datas += '</span>'
            }
            else {
                datas += '<span role="button" title="Edit" class="icon-disabled">'
                datas += '<i class="cp-edit"></i>'
                datas += '</span>'
                datas += '<span role="button" title="Delete" class="icon-disabled" >'
                datas += '<i class="cp-Delete"></i>'
                datas += '</span>'
            }
            datas += '<span role="button" title="View"  type="' + item?.type + '" entityId="' + item?.entityId + '" onclick="viewTree(this)" class="database-edit-button">'
            datas += '<i class="cp-file-edits text-primary"></i>'
            datas += '</span>'
            datas += '</div>'
            datas += '</td>'
            datas += '</tr>'
            sno++
        }
        if (item?.type == 2) {
            let jsonstring = JSON.parse(item?.properties)
            jsonstring?.forEach(function (parse, i) {
                d = parse
            })
            arrBfToBf.push(item)
            let addclass, addclass1
            if (d?.if?.BusinessFunctionImpact == "Partial") {
                addclass = "text-bg-warning"
            } else if (d?.if?.BusinessFunctionImpact == "Total") {
                addclass = "text-bg-danger"
            } else {
                addclass = `background:#E76802`
            }
            if (d?.then?.BusinessFunctionImpact == "Partial") {
                addclass1 = "text-bg-warning"
            } else if (d?.then?.BusinessFunctionImpact == "Total") {
                addclass1 = "text-bg-danger"
            } else {
                addclass1 = `background:#E76802`
            }
            let datemod = item?.effectiveDateTo.split("T"), bf_date = datemod[0].split("-")
            datas1 += '<tr>'
            datas1 += '<td class="text-truncate" title="">' + sno1 + '</td>'
            //datas1 += '<td class="text-truncate" title="">BFtoBF2016-2526</td>'
            datas1 += '<td class="text-truncate" title="' + d?.if?.BusinessFunctionName + ' " >' + d?.if?.BusinessFunctionName + ' is</td>'
            datas1 += '<td class="text-truncate" title=""><span style="' + addclass + '" class="badge ' + addclass + ' me-1 text-white">' + d?.if?.BusinessFunctionImpact + ' Impacted</span> Then</td>'
            datas1 += '<td class="text-truncate" title="">' + d?.then?.BusinessFunctionName + ' will be</td>'
            datas1 += '<td class="text-truncate" title=""><span style="' + addclass1 + '" class="badge ' + addclass1 + ' me-1 text-white">' + d?.then?.BusinessFunctionImpact + ' Impacted</span> and</td>'
            datas1 += '<td class="text-truncate" title="">' + bf_date[2] + "-" + bf_date[1] + "-" + bf_date[0] + " " + datemod[1] + '</td>'
            datas1 += '<td class="text-truncate" title="">'
            datas1 += '<div class="d-flex align-items-center gap-2">'
            if (createBiaPermission === 'true' && deleteBiaPermission === "true") {
                datas1 += '<span role="button" title="Edit" class="database-edit-button" name="bfupdatename" description=' + item?.description + ' date="' + item?.effectiveDateTo + '" bf_impact1="' + d?.then?.BusinessFunctionImpact + '" Bf_Bf1="' + d?.then?.BusinessFunctionId + '" bf_impact="' + d?.if?.BusinessFunctionImpact + '" Bf-Bf="' + d?.if?.BusinessFunctionId + '" updateId=' + item?.id + ' onclick="bfBfedit(this)" data-bs-toggle="modal" data-bs-target="#biaBusinessFunctionModal">'
                datas1 += '<i class="cp-edit"></i>'
                datas1 += '</span>'
                datas1 += '<span role="button" title="Delete" class="database-delete-button" deleteId="' + item?.id + '" delete_name="' + d?.if?.BusinessFunctionName + '" onclick="bfToBsdelete(this)" data-bs-toggle="modal" data-bs-target="#biaDeleteModal1">'
                datas1 += '<i class="cp-Delete"></i>'
                datas1 += '</span>'
            }
            else if (createBiaPermission === 'true' && deleteBiaPermission === "false") {
                datas1 += '<span role="button" title="Edit" class="database-edit-button" name="bfupdatename" description=' + item?.description + ' date="' + item?.effectiveDateTo + '" bf_impact1="' + d?.then?.BusinessFunctionImpact + '" Bf_Bf1="' + d?.then?.BusinessFunctionId + '" bf_impact="' + d?.if?.BusinessFunctionImpact + '" Bf-Bf="' + d?.if?.BusinessFunctionId + '" updateId=' + item?.id + ' onclick="bfBfedit(this)" data-bs-toggle="modal" data-bs-target="#biaBusinessFunctionModal">'
                datas1 += '<i class="cp-edit"></i>'
                datas1 += '</span>'
                datas1 += '<span role="button" title="Delete" class="icon-disabled">'
                datas1 += '<i class="cp-Delete"></i>'
                datas1 += '</span>'
            }
            else if (createBiaPermission === 'false' && deleteBiaPermission === "true") {
                datas1 += '<span role="button" title="Edit" class="icon-disabled" description=' + item?.description + ' >'
                datas1 += '<i class="cp-edit"></i>'
                datas1 += '</span>'
                datas1 += '<span role="button" title="Delete" class="database-delete-button" deleteId="' + item?.id + '" delete_name="' + d?.if?.BusinessFunctionName + '" onclick="bfToBsdelete(this)" data-bs-toggle="modal" data-bs-target="#biaDeleteModal1">'
                datas1 += '<i class="cp-Delete"></i>'
                datas1 += '</span>'
            }
            else {
                datas1 += '<span role="button" title="Edit" class="icon-disabled" >'
                datas1 += '<i class="cp-edit"></i>'
                datas1 += '</span>'
                datas1 += '<span role="button" title="Delete" class="icon-disabled">'
                datas1 += '<i class="cp-Delete"></i>'
                datas1 += '</span>'
            }
            datas1 += '<span role="button" title="View" class="database-edit-button" typename="Businessfunc" type="' + item?.type + '" entityId="' + item?.entityId + '" onclick="viewTree(this)">'
            datas1 += '<i class="cp-file-edits text-primary"></i>'
            datas1 += '</span>'
            datas1 += '</div>'
            datas1 += '</td>'
            datas1 += '</tr>'
            sno1++
        }
        if (item?.type == 3) {
            let jsonstring = JSON.parse(item?.properties)
            jsonstring?.forEach(function (parse, i) {
                d = parse
            })
            arrBsToBs.push(item)
            let valuesArray = d?.Is?.ByBusinessserviceProperties.map(item => item.Val);
            let commaSeparatedValues = valuesArray.join(", ");
            let Id_Data = d?.Is?.ByBusinessserviceProperties.map(item => item.Id).join(",")
            let addclass
            if (d?.Is?.Impact == "Partial") {
                addclass = "text-bg-warning"
            } else if (d?.Is?.Impact == "Total") {
                addclass = "text-bg-danger"
            } else {
                addclass = `background:#E76802`
            }
            let datemod = item?.effectiveDateTo.split("T")
            let bs_date = datemod[0].split("-")
            datas2 += '<tr>'
            datas2 += ' <td class="text-truncate" title="">' + sno2 + '</td>'
            datas2 += '<td class="text-truncate" title="' + d?.BusinessServiceName + '">' + d?.BusinessServiceName + '</td>'
            datas2 += '<td class="text-truncate" title=""><span style="' + addclass + '" class="badge ' + addclass + '  me-1 text-white">' + d?.Impact + ' Impacted</span> By</td>'

            datas2 += '<td class="text-truncate" title="' + commaSeparatedValues + '">' + commaSeparatedValues + '</td>'
            datas2 += '<td class="text-truncate" title="">' + bs_date[2] + "-" + bs_date[1] + "-" + bs_date[0] + " " + datemod[1] + '</td>'
            datas2 += '<td class="text-truncate" title="">'
            datas2 += '<div class="d-flex align-items-center gap-2">'
            if (createBiaPermission === 'true' && deleteBiaPermission === "true") {
                datas2 += '<span role="button" title="Edit" class="database-edit-button" description=' + item?.description + ' name="updatename" date="' + item?.effectiveDateTo + '" Bs_Bs1="' + Id_Data + '" bs_impact="' + d?.Impact + '" Bs-Bs="' + d?.BusinessServiceId + '" description="' + item?.description + '" onclick="bSBSBinddata(this)" updateId="' + item?.id + '" data-bs-toggle="modal" data-bs-target="#biaBusinessServiceModal" >'
                datas2 += '<i class="cp-edit"></i>'
                datas2 += '</span>'
                datas2 += '<span role="button" title="Delete" onclick="bsToBsdelete(this)" delete_name="' + d?.BusinessServiceName + '"  deleteId="' + item?.id + '" class="database-delete-button" data-bs-toggle="modal" data-bs-target="#biaDeleteModal2">'
                datas2 += '<i class="cp-Delete"></i>'
                datas2 += '</span>'
            }
            else if (createBiaPermission === 'true' && deleteBiaPermission === "false") {
                datas2 += '<span role="button" title="Edit" class="database-edit-button" description=' + item?.description + ' name="updatename" date="' + item?.effectiveDateTo + '" Bs_Bs1="' + Id_Data + '" bs_impact="' + d?.Impact + '" Bs-Bs="' + d?.BusinessServiceId + '" description="' + item?.description + '" onclick="bSBSBinddata(this)" updateId="' + item?.id + '" data-bs-toggle="modal" data-bs-target="#biaBusinessServiceModal" >'
                datas2 += '<i class="cp-edit"></i>'
                datas2 += '</span>'
                datas2 += '<span role="button" title="Delete" class="icon-disabled">'
                datas2 += '<i class="cp-Delete"></i>'
                datas2 += '</span>'
            }
            else if (createBiaPermission === 'false' && deleteBiaPermission === "true") {
                datas2 += '<span role="button" title="Edit" class="icon-disabled">'
                datas2 += '<i class="cp-edit"></i>'
                datas2 += '</span>'
                datas2 += '<span role="button" title="Delete" onclick="bsToBsdelete(this)" delete_name="' + d?.BusinessServiceName + '" deleteId="' + item?.id + '" class="database-delete-button" data-bs-toggle="modal" data-bs-target="#biaDeleteModal2">'
                datas2 += '<i class="cp-Delete"></i>'
                datas2 += '</span>'
            }
            else {
                datas2 += '<span role="button" title="Edit" class="icon-disabled">'
                datas2 += '<i class="cp-edit"></i>'
                datas2 += '</span>'
                datas2 += '<span role="button" title="Delete" class="icon-disabled">'
                datas2 += '<i class="cp-Delete"></i>'
                datas2 += '</span>'
            }
            datas2 += '<span role="button" title="View" class="database-edit-button" typename="BusinessServ" type="' + item?.type + '" entityId="' + item?.entityId + '" onclick="viewTree(this)">'
            datas2 += '<i class="cp-file-edits text-primary"></i>'
            datas2 += '</span>'
            datas2 += '</div>'
            datas2 += '</td>'
            datas2 += '</tr>'
            sno2++
        }
    })

    if (scheDule == undefined || scheDule == 1) {
        if (arrInfraToBf?.length) {
            if (arrInfraToBf?.length == 1) {
                arrInfraToBf?.forEach((x) => {
                    newJsonCreate(JSON.parse(x?.properties), "newTree", x?.type)
                })
            } else {
                newJsonCreate(JSON.parse(arrInfraToBf[0]?.properties), "newTree", arrInfraToBf[0]?.type)
            }
        }
    } else if (scheDule == 2) {
        if (arrBfToBf?.length) {
            if (arrBfToBf?.length == 1) {
                arrBfToBf?.forEach((x) => {
                    newJsonCreate(JSON.parse(x?.properties), "newTree", x?.type, treeBf)
                })
            } else {
                newJsonCreate(JSON.parse(arrBfToBf[0]?.properties), "newTree", arrBfToBf[0]?.type, treeBf)
            }
        }
    } else {
        if (arrBsToBs?.length) {
            if (arrBsToBs?.length == 1) {
                arrBsToBs?.forEach((x) => {
                    newJsonCreate(JSON.parse(x?.properties), "newTree", x?.type, treeBs)
                })
            } else {
                newJsonCreate(JSON.parse(arrBsToBs[0]?.properties), "newTree", arrBsToBs[0]?.type, treeBs)
            }
        }
    }

    $("#biaInfraToBfTable tbody").append(datas)
    $("#biaBfToBfTable tbody").append(datas1)
    $("#biaBsBsTable tbody").append(datas2)
}
getBia()
async function fetchAndCreateTree(url, data, typeName, type) {
    try {
        const result = await $.ajax({
            type: "GET",
            url: RootUrl + url,
            dataType: "json",
        });
        const datas = result.data
        if (result?.success) {
            if (type == "bfGet") {
                affectBf = datas;
                treeBf = datas;
            } else if (type == "bsGet") {
                treeBs = datas;
                datas?.forEach((item) => {
                    $('#biaInfraBs').append(
                        `<option title="${item.name}" value="${item.id}">${item.name}</option>`
                    );
                });
                $("#biaInfraBs,#biaTreeBS,#biaInfraBs1").each(function () {
                    $(this).siblings(`[value="${this.value}"]`).remove();
                });
            }
            else {
                const parsedData = JSON.parse(data?.properties);
                newJsonCreate(parsedData, "newTree", typeName, result?.data);
            }
        } else {
            errorNotification(result);
        }
    } catch (err) {
        errorNotification({ message: `Error loading ${typeName}` });
    }
}

async function viewTree(d) {
    await $.ajax({
        type: "POST",
        url: RootUrl + biaUrls?.GetBiaRulesByentityIdAndType,
        dataType: "json",
        data: {
            entityId: $(d).attr("entityId"),
            type: $(d).attr("type"),
            __RequestVerificationToken: gettoken()
        },
        success: async function (result) {
            let data = result?.data
            if (result?.success) {
                if ($(d).attr("typename") === "Businessfunc") {
                    await fetchAndCreateTree(biaUrls?.GetBusinessFunctionList, data, $(d).attr("type"), "Business Function");
                } else if ($(d).attr("typename") === "BusinessServ") {
                    await fetchAndCreateTree(biaUrls?.GetBusinessServiceList, data, $(d).attr("type"), "Business Service");
                } else {
                    newJsonCreate(JSON.parse(data?.properties), "newTree", $(d).attr("type"));
                }
            } else {
                errorNotification(result)
            }
        },
    })
}
async function infraBfEdit(datas) {
    $("#biaInfraBusinessFunction,#biaInfraBs,#biaInfraBs1,#biaTreeBS").empty().append('<option title="" value=""></option>')
    await fetchAndCreateTree(
        biaUrls?.GetBusinessFunctionList,
        "",
        "Business Function",
        "bfGet"
    );
    await fetchAndCreateTree(
        biaUrls?.GetBusinessServiceList,
        "",
        "Business Service",
        "bsGet"
    );
    if ($(datas).attr('name') == "Infraupdatename") {
        setTimeout(() => {
            $('#biaInfraBfSave').text("Update");
            globalInfraBfId = $(datas).attr('updateId');
            infraToBfTree?.forEach(function (item, i) {
                if (item?.id == globalInfraBfId) {
                    let jsonstring = JSON.parse(item?.properties)
                    jsonstring?.forEach(function (parse, i) {
                        d = parse?.propertytreedata?.businessServiceId
                    })
                }
            })
            $("#biaInfraBFDescription").val($(datas).attr('description') == "NA" ? "" : $(datas).attr('description'))
            $("#biaInfraBs").val($(datas).attr('bs')).trigger('change')
            setTimeout(() => {
                $("#biaInfraInfraObject").val($(datas).attr('infra')).trigger('change')
                setTimeout(() => {
                    $("#biaInfraBusinessFunction").val($(datas).attr('infrabf')).trigger('change')
                    $("#biaInfraBs1").val($(datas).attr('infrabs')).trigger('change')
                    $("#biaInfraInfraComponent").val($(datas).attr('infracomponent')).trigger('change')
                }, 500)
            }, 500)
            $("#biaInfraImpact").val($(datas).attr('infraimpact')).trigger("change")
            $("#biaInfraImpact1").val($(datas).attr('infraimpact1')).trigger('change')
            $("#biaInfraDate").val($(datas).attr('date')).trigger('change')
        }, 500)
    }
}
infraBfEdit()
