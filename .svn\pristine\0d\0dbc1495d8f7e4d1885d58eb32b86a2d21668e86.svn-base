﻿using ContinuityPatrol.Application.Features.Server.Events.Update;
using ContinuityPatrol.Application.Features.ServerLog.Events.Update;

namespace ContinuityPatrol.Application.Features.ServerLog.Commands.Update;

public class UpdateServerLogCommandHandler : IRequestHandler<UpdateServerLogCommand, UpdateServerLogResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IServerLogRepository _serverLogRepository;

    public UpdateServerLogCommandHandler(IMapper mapper, IServerLogRepository serverLogRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _serverLogRepository = serverLogRepository;
    }

    public async Task<UpdateServerLogResponse> Handle(UpdateServerLogCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _serverLogRepository.GetByReferenceIdAsync(request.Id);
        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.ServerLog), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateServerLogCommand), typeof(Domain.Entities.ServerLog));
        await _serverLogRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateServerLogResponse
        {
            Message = Message.Update(nameof(Domain.Entities.ServerLog), eventToUpdate.Name),

            Id = eventToUpdate.ReferenceId
        };
        await _publisher.Publish(new ServerLogUpdatedEvent { Name = eventToUpdate.Name }, cancellationToken);
        return response;
    }
}
