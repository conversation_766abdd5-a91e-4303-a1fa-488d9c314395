﻿function monitorTypeIBMSVCGM(commondata,infraObjectName, moniterType, parsedData ) {
    if (moniterType === "SVC") {
        let pripaddress = commondata?.prServerStatus?.includes("NA") ? "cp-down-linearrow me-1 text-danger" :
            (commondata?.prServerStatus)?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-down-linearrow me-1 text-danger";

        let dripaddress = commondata?.drServerStatus?.includes("NA") ? "cp-down-linearrow me-1 text-danger" :
            (commondata?.drServerStatus)?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-down-linearrow me-1 text-danger";

        let prhostname = parsedData?.PR_Server_HostName?.includes("NA" || "N/A") ? "cp-disable" : "cp-host-name text-success"

        let drhostname = parsedData?.DR_Server_HostName?.includes("NA" || "N/A") ? "cp-disable" : "cp-host-name text-success"

        let infraobjectdata = 
            '<div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm">' +
            '<thead style="position: sticky;top: 0px;">' +
            '<tr>' +
            '<th>Component Monitor</th>' +
            '<th>Production Server</th>' +
            '<th>DR Server</th>' +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td>' + 'IP Address' + '</td>' +
            '<td  title="' + (parsedData?.PR_Server_IpAddress || "NA") + '">' + '<i class="' + pripaddress + '"></i>' + (parsedData?.PR_Server_IpAddress !== null && parsedData?.PR_Server_IpAddress !== "" ? parsedData?.PR_Server_IpAddress : 'NA') + '</td>' +
            '<td title="' + (parsedData?.DR_Server_IpAddress || "NA") + '">' + '<i class="' + dripaddress + '"></i>' + (parsedData?.DR_Server_IpAddress !== null && parsedData?.DR_Server_IpAddress !== "" ? parsedData?.DR_Server_IpAddress : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + 'Host Name' + '</td>' +
            '<td title="' + (parsedData?.PR_Server_HostName || "NA") + '">' + '<i class="' + prhostname + '"></i>' + (parsedData?.PR_Server_HostName !== null && parsedData?.PR_Server_HostName !== "" ? parsedData?.PR_Server_HostName : 'NA') + '</td>' +
            '<td title="' + (parsedData?.DR_Server_HostName || "NA") + '">' + '<i class="' + drhostname + '"></i>' + (parsedData?.DR_Server_HostName !== null && parsedData?.DR_Server_HostName !== "" ? parsedData?.DR_Server_HostName : 'NA') + '</td>' +
            '</tr>' +
            '</tbody>' +
            '</table>' +
            '</div>' +
            '<div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm">' +
            '<thead style="position: sticky;top: 0px;z-index: 1;">' +
            '<tr>' +
            '<th>Replication Monitor</th>' +
            '<th>Production Server</th>' +
            '<th>DR Server</th>' +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td>' + "RelationShip Name" + '</td>' +
            '<td title="' + (parsedData?.SVCGMReplicationM?.PRRelationshipName || "NA") + '">' + (parsedData?.SVCGMReplicationM?.PRRelationshipName !== null && parsedData?.SVCGMReplicationM?.PRRelationshipName !== "" ? parsedData?.SVCGMReplicationM?.PRRelationshipName : 'NA') + '</td>' +
            '<td>' + (parsedData?.SVCGMReplicationM?.DRRelationshipName !== null && parsedData?.SVCGMReplicationM?.DRRelationshipName !== "" ? parsedData?.SVCGMReplicationM?.DRRelationshipName : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + "Group Name" + '</td>' +
            '<td title="' + (parsedData?.SVCGMReplicationM?.PRConsistencyGroupName || "NA") + '">' + (parsedData?.SVCGMReplicationM?.PRConsistencyGroupName !== null && parsedData?.SVCGMReplicationM?.PRConsistencyGroupName !== "" ? parsedData?.SVCGMReplicationM?.PRConsistencyGroupName : 'NA') + '</td>' +
            '<td title="' + (parsedData?.SVCGMReplicationM?.DRConsistencyGroupName || "NA") + '">' + (parsedData?.SVCGMReplicationM?.DRConsistencyGroupName !== null && parsedData?.SVCGMReplicationM?.DRConsistencyGroupName !== "" ? parsedData?.SVCGMReplicationM?.DRConsistencyGroupName : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + "Relationship Primary Value" + '</td>' +
            '<td title="' + (parsedData?.SVCGMReplicationM?.PRRelationshipPrimaryValue || "NA") + '">' + (parsedData?.SVCGMReplicationM?.PRRelationshipPrimaryValue !== null && parsedData?.SVCGMReplicationM?.PRRelationshipPrimaryValue !== "" ? parsedData?.SVCGMReplicationM?.PRRelationshipPrimaryValue : 'NA') + '</td>' +
            '<td title="' + (parsedData?.SVCGMReplicationM?.DRRelationshipPrimaryValue || "NA") + '">' + (parsedData?.SVCGMReplicationM?.DRRelationshipPrimaryValue !== null && parsedData?.SVCGMReplicationM?.DRRelationshipPrimaryValue !== "" ? parsedData?.SVCGMReplicationM?.DRRelationshipPrimaryValue : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + "Master Change Volume Name" + '</td>' +
            '<td title="' + (parsedData?.SVCGMReplicationM?.PRMasterVolumeName || "NA") + '">' + (parsedData?.SVCGMReplicationM?.PRMasterVolumeName !== null && parsedData?.SVCGMReplicationM?.PRMasterVolumeName !== "" ? parsedData?.SVCGMReplicationM?.PRMasterVolumeName : 'NA') + '</td>' +
            '<td title="' + (parsedData?.SVCGMReplicationM?.DRMasterVolumeName || "NA") + '">' + (parsedData?.SVCGMReplicationM?.DRMasterVolumeName !== null && parsedData?.SVCGMReplicationM?.DRMasterVolumeName !== "" ? parsedData?.SVCGMReplicationM?.DRMasterVolumeName : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + "Auxiliary Change Volume Name" + '</td>' +
            '<td title="' + (parsedData?.SVCGMReplicationM?.PRAuxiliaryVolumeName || "NA") + '">' + (parsedData?.SVCGMReplicationM?.PRAuxiliaryVolumeName !== null && parsedData?.SVCGMReplicationM?.PRAuxiliaryVolumeName !== "" ? parsedData?.SVCGMReplicationM?.PRAuxiliaryVolumeName : 'NA') + '</td>' +
            '<td title="' + (parsedData?.SVCGMReplicationM?.DRAuxiliaryVolumeName || "NA") + '">' + (parsedData?.SVCGMReplicationM?.DRAuxiliaryVolumeName !== null && parsedData?.SVCGMReplicationM?.DRAuxiliaryVolumeName !== "" ? parsedData?.SVCGMReplicationM?.DRAuxiliaryVolumeName : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + "Relationship State" + '</td>' +
            '<td title="' + (parsedData?.SVCGMReplicationM?.PRRCRelationshipState || "NA") + '">' + '<i class="' + checkValueAndReturnClass(parsedData?.SVCGMReplicationM?.PRRCRelationshipState) + '"></i>' + (parsedData?.SVCGMReplicationM?.PRRCRelationshipState !== null && parsedData?.SVCGMReplicationM?.PRRCRelationshipState !== "" ? parsedData?.SVCGMReplicationM?.PRRCRelationshipState : 'NA') + '</td>' +
            '<td title="' + (parsedData?.SVCGMReplicationM?.PRRCRelationshipState || "NA") + '">' + '<i class="' + checkValueAndReturnClass(parsedData?.SVCGMReplicationM?.PRRCRelationshipState) + '"></i>' + (parsedData?.SVCGMReplicationM?.DRRCRelationshipState !== null && parsedData?.SVCGMReplicationM?.DRRCRelationshipState !== "" ? parsedData?.SVCGMReplicationM?.DRRCRelationshipState : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + "Relationship Progress" + '</td>' +
            '<td title="' + (parsedData?.SVCGMReplicationM?.PRRCRelationshipProgress || "NA") + '">' + (parsedData?.SVCGMReplicationM?.PRRCRelationshipProgress !== null && parsedData?.SVCGMReplicationM?.PRRCRelationshipProgress !== "" ? parsedData?.SVCGMReplicationM?.PRRCRelationshipProgress : 'NA') + '</td>' +
            '<tdtitle="' + (parsedData?.SVCGMReplicationM?.DRRCRelationshipProgress || "NA") + '">' + (parsedData?.SVCGMReplicationM?.DRRCRelationshipProgress !== null && parsedData?.SVCGMReplicationM?.DRRCRelationshipProgress !== "" ? parsedData?.SVCGMReplicationM?.DRRCRelationshipProgress : 'NA') + '</td>' +
            '</tr>' +
            '</tbody style="">' +
            '</table>' +
            '</div>' 
            

        setTimeout(() => {
            $("#infraobjectalldata").html(infraobjectdata);
        }, 200);
    }

}

function checkValueAndReturnClass(value) {
    let classvalue = "";
    if (value === null || value === '' || value === 'undefined' || value === 'NA' || value === 'N/A')
        return classvalue = " ";

    if (typeof value === 'string') {
        switch (value?.toLowerCase()) {
            case "consistent_copying":
                classvalue = "cp-copy text-success";
                break;
            case "inconsistent_stopped":
                classvalue = "cp-Stopped text-danger";
                break;
            case "inconsistent_copying":
                classvalue = "cp-copy text-success";
                break;
            case "consistent_stopped":
                classvalue = "cp-Stopped text-danger";
                break;
            case "consistent_synchronized":
                classvalue = "cp-refresh text-success";
                break;
            case "idling":
                classvalue = "cp-pending";
                break;
            case "idling_disconnected":
                classvalue = "cp-idling-disconnected text-danger";
                break;
            case "inconsistent_disconnected":
                classvalue = "cp-disconnecteds text-danger";
                break;
            case "consistent_disconnected":
                classvalue = "cp-disconnecteds text-danger";
                break;
            case "online":
                classvalue = "cp-online text-success";
                break;
            case "primary_offline":
                classvalue = "cp-offline";
                break;
            case "secondary_offline":
                classvalue = "cp-offline";
                break;
            case "io_channel_offline":
                classvalue = "cp-offline";
                break;
            case "in_sync":
                classvalue = "cp-refresh";
                break;
            case "out_of_sync":
                classvalue = "cp-unSync text-danger";
                break;
            default:
                classvalue = " ";

        }
    }
    return classvalue;
}