﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Form.Events.PaginatedView;

public class FormPaginatedViewEventHandler : INotificationHandler<FormPaginatedViewEvent>
{
    private readonly ILogger<FormPaginatedViewEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FormPaginatedViewEventHandler(ILogger<FormPaginatedViewEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(FormPaginatedViewEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.Form.ToString(),
            Action = $"{ActivityType.View} {Modules.Form}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = " Form Builder viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Form Builder viewed");
    }
}