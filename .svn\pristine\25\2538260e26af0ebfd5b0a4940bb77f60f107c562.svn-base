﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@using ContinuityPatrol.Domain.Entities
@model ContinuityPatrol.Domain.ViewModels.JobModel.JobViewModel
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/switch_slide_toggle.css" rel="stylesheet" />

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-job-management"></i><span>Monitoring Job Management</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" autocomplete="off" id="search-inp" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown"  title="Filter">
                                <i class="cp-filter"></i>
                            </span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li>
                                    <h6 class="dropdown-header">Filter Search</h6>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="name=" id="JobName">
                                        <label class="form-check-label" t for="Job Name">
                                            Job Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="templatename=" id="Templatename">
                                        <label class="form-check-label" for="Template name">
                                            Template Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="solutiontype=" id="Solutiontype">
                                        <label class="form-check-label" for="Solution type">
                                            Solution Type
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="state=" id="State">
                                        <label class="form-check-label" for="State">
                                            State
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" id="Activebtn" class="btn btn-transparent border border-success-subtle me-2 d-flex align-items-center text-primary-emphasis"
                        data-bs-toggle="modal"
                        data-bs-target="" style="background-color:#fafff8">
                    <i class="cp-active-inactive text-success" title="Active"></i> &nbsp; Active
                </button>
                <button type="button" id="Inactivebtn" class="btn btn-transparent border border-danger-subtle me-2 d-flex align-items-center" data-bs-toggle="collapse"
                        href="#collapseExample" data-clicked="false" role="button" aria-expanded="false" 
                        aria-controls="collapseExample" style="background-color:#fff0f2">
                    <i class="cp-active-inactive text-danger" title="InActive"></i> &nbsp; InActive
                </button>
                <button type="button" id="CreteButton" class="btn btn-primary btn-sm rounded-2 me-2" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">
            <div id="collapetable">
                <table class="datatable table table-hover  no-footer" id="tblJobManagement" style="width:100%">
                    <thead>
                        <tr>
                            <th class="SrNo_th">Sr. No.</th>
                            <th>
                                <div class="">
                                    <input name="checkboxAll" type="checkbox" id="flexCheckDefault"
                                           class="form-check-input custom-cursor-default-hover">
                                </div>
                            </th>
                            <th>Job Name</th>
                            <th>Template Name</th>
                            <th>Node Name</th>
                            <th>Solution Type</th>
                            <th>Scheduled Time</th>
                            <th>Last Monitoring Time</th>
                            <th class="">Status</th>
                            <th class="">State</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>

        </div>
    </div>

    <div id="ConfigurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.CreateAndEdit" aria-hidden="true"></div>
    <div id="ConfigurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.Delete" aria-hidden="true"></div>

    <!--Modal Create-->
    <div class="modal fade" id="CreateModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <form class="modal-content" id="CreateForm" asp-controller="MonitoringJob" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-job-management"></i><span>Monitoring Job Configuration</span></h6>
                    <button type="button" id="buttonclose" class="btn-close monitorbtn-cancel" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
                </div>
                <div class="modal-body" style="min-height: calc(100vh - 112px);">
                    <div class="container ">
                        <div class="row row-cols-2">
                            <div class="col">
                                <div class="mb-3 form-group">
                                    <div class="form-label">Job Name</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-name"></i></span>
                                        <input asp-for="Name" id="textJobName" autocomplete="off" type="text" class="form-control" placeholder="Enter Job Name" maxlength="100" />
                                    </div>
                                    <span asp-validation-for="Name" id="Name-error"></span>
                                </div>
                            </div>
                            <div class="col">
                                <div class="mb-3 form-group">
                                    <div class="form-label">Execution Policy</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-group-policy"></i></span>

                                        <select asp-for="ExecutionPolicy" class="form-select-modal" id="selectExecutionPolicy" data-live-search="true" data-placeholder="Select Execution Policy">
                                            <option value=""></option>
                                            <option value="2">Distribution Policy</option>
                                            <option value="1">Group Node Policy</option>

                                            @* <option value="3">Resource Policy</option> *@
                                        </select>
                                    </div>
                                    <span asp-validation-for="ExecutionPolicy" id="ExecutionPolicy-error"></span>
                                </div>
                            </div>
                            <div class="col" id="groupPolicyDiv">
                                <div class="mb-3 form-group" id="groupPolicy">
                                    <div class="form-label">Group Node Policy</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-standby-file"></i></span>
                                        <select asp-for="GroupPolicyName" id="selectGroupPolicy" class="form-select-modal" data-placeholder="Select Group Node Policy">
                                            <option id="selectgrp" value=""></option>
                                            @*    @foreach (var policy in Model.GroupPolicies)
                                            {
                                                @if (policy.Type.ToLower() == "Monitor Service" || policy.Type.ToLower() == "monitorservice")
                                                {
                                                    <option id="@policy.Id" value="@policy.GroupName">@policy.GroupName</option>
                                               *@  }
                                            }
                                        </select>
                                    </div>
                                    <span asp-validation-for="GroupPolicyName" id="GroupPolicy-error"></span>
                                    <input type="hidden" asp-for="GroupPolicyId" id="policyid" />
                                </div>
                            </div>

                            <div class="col">
                                <div class="mb-3 form-group">
                                    <div class="form-label">Solution Type</div>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="cp-solution"></i>
                                        </span>
                                      @*   <div id="SolutionType" class="w-100">

                                        </div> *@
                                        <select asp-for="SolutionType" id="selectSolutionType" class="form-select-modal" aria-label="Default select example" data-placeholder="Select Solution Type">
                                            @* <option value=""></option>   *@

                                          @*     @foreach (var replicationType in Model.ReplicationTypes)
                                            {
                                            <option value="@replicationType.Key" id="@replicationType.Value">@replicationType.Key</option>
                                            } *@
                                        </select> 
                              
                                    </div>
                                    <span asp-validation-for="SolutionType" id="SolutionType-error"></span>
                                </div>
                            </div>
                            <div class="col">
                                <div class="mb-3 form-group">
                                    <div class="form-label">Workflow Templates</div>
                                    <div class="input-group">
                                        <i class="cp-template-store"></i>
                                        <select  id="selectTemplateName" class="form-select-modal" aria-label="Default select example" data-placeholder="Select Workflow Templates">
                                            @* <option value=""></option> *@
                                        </select>
                                  
                                    </div>
                                    <input type="hidden" asp-for="SolutionTypeId" id="solutionTypeId" />
                                    <input type="hidden" asp-for="SolutionType" id="solutionType" />
                                    <span asp-validation-for="TemplateName" id="TemplateName-error"></span>
                                    <input asp-for="TemplateId" id="textTemplateId" type="hidden" class="form-control" />
                                    <input asp-for="TemplateName" id="textTemplateName" type="hidden" class="form-control" />
                                    <input asp-for="Id" id="textJobId" type="hidden" class="form-control" />
                                    @*<input asp-for="InfraObjectId" id="textInfraObjectId" type="hidden" class="form-control" />*@
                                    @*<input asp-for="InfraObjectName" id="textinfraname" type="hidden" class="form-control" />*@
                                    <input asp-for="InfraObjectProperties" id="textInfraObjectProperties" type="hidden" class="form-control" />
                                    <input asp-for="NodeId" id="textNodeId" type="hidden" class="form-control" />
                                    <input asp-for="NodeName" id="textNodeName" type="hidden" class="form-control" />
                                    <input asp-for="CronExpression" id="textCronExpression" type="hidden" class="form-control" />
                                    <input asp-for="LastExecutionTime" id="txtlastexecutetime" type="hidden" class="form-control" />
                                    <input asp-for="Status" id="textStatus" type="hidden" class="form-control" />
                                    <input asp-for="IsSchedule" id="textIsSchedule" type="hidden" class="form-control" />
                                    <input asp-for="ScheduleType" id="textScheduleType" type="hidden" class="form-control" />
                                    <input asp-for="ScheduleTime" id="textScheduleTime" type="hidden" class="form-control" />
                                    <input asp-for="Type" id="cronexpresstype" type="hidden" class="form-control" />

                                </div>
                            </div>
                            <div class="col" id="InfraObject_Name">
                                <div class="form-group">
                                    <div class="form-label">InfraObject Name</div>
                                    <div class="input-group" >
                                        <span class="input-group-text"><i class="cp-infra-object"></i></span>
                                        
                                        <select id="selectInfraObjectName" class="form-select-modal" aria-label="Default select example" data-placeholder="Select InfraObject Name" multiple>
                                           @* <option value=""></option> *@

                                        </select>
                                      
                                    </div>
                                    <span asp-validation-for="InfraObjectProperties" id="InfraObjectName-error"></span> 

                                    @* <input asp-for="InfraObjectId" id="textInfraObjectId" type="hidden" class="form-control">  *@
                                </div>
                            </div>
                           
                            <div class="col d-none" id="cron">
                                <div class="form-group">
                                    <div class="form-label">Cron Expressions (Optional) </div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-time"></i></span>
                                        <input class="form-control datetimeCron" id="datetimeCronlist" placeholder=" Cron Expressions" type="text" autocomplete="off" />
                                    </div>

                                </div>
                            </div>
                         
                        </div>
                        <div class="row">
                            <div class="col">
                                <div class="switches-container mb-3">
                                    <input type="radio" id="switchMonthly" name="switchPlan" value="Cycle" checked="checked" />
                                    <input type="radio" id="switchYearly" name="switchPlan" value="Once" />
                                    <label for="switchMonthly" class="nav-link">Cycle</label>
                                    <label for="switchYearly" class="nav-link">Once</label>
                                    <div class="switch-wrapper">
                                        <div class="switch">
                                            <div>Cycle</div>
                                            <div>Once</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="month" id="yeargroup">
                                    <div class="mb-3">
                                        <div class="form-group ">
                                            <div class="form-label">Schedule Time</div>
                                            <div class="input-group">
                                                <span class="input-group-text">  <i class="cp-time"></i></span>

                                                <input id="datetimeCron" class="form-control datetimeCron" name="datetime_currentdate" placeholder="Schedule Time" type="datetime-local" />
                                            </div>
                                            <span asp-validation-for="CronExpression" id="CronExpression-error"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 pe-0">
                                <div class="row mt-2 w-100 ">
                                    <div class="year" id="monthgroup">
                                        <div class="mb-3">
                                            <div class="form-label">Scheduler</div>
                                            <div>
                                                <nav>
                                                    <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                                        <button class="nav-link active" id="nav-Minutes-tab" data-bs-toggle="tab" name="Scheduler"
                                                                data-bs-target="#nav-Minutes" type="button" role="tab"
                                                                aria-controls="nav-Minutes" aria-selected="true">
                                                            Minutes
                                                        </button>
                                                        <button class="nav-link" id="nav-Hourly-tab" data-bs-toggle="tab" name="Scheduler"
                                                                data-bs-target="#nav-Hourly" type="button" role="tab"
                                                                aria-controls="nav-Hourly" aria-selected="false">
                                                            Hourly
                                                        </button>
                                                        <button class="nav-link" id="nav-Daily-tab" data-bs-toggle="tab" name="Scheduler"
                                                                data-bs-target="#nav-Daily" type="button" role="tab"
                                                                aria-controls="nav-Daily" aria-selected="false">
                                                            Daily
                                                        </button>
                                                        <button class="nav-link" id="nav-Weekly-tab" data-bs-toggle="tab" name="Scheduler"
                                                                data-bs-target="#nav-Weekly" type="button" role="tab"
                                                                aria-controls="nav-Weekly" aria-selected="false">
                                                            Weekly
                                                        </button>
                                                        <button class="nav-link" id="nav-Monthly-tab" data-bs-toggle="tab" name="Scheduler"
                                                                data-bs-target="#nav-Monthly" type="button" role="tab"
                                                                aria-controls="nav-Monthly" aria-selected="false">
                                                            Monthly
                                                        </button>
                                                    </div>
                                                </nav>
                                                <div class="tab-content" id="nav-tabContent">
                                                    <div class="tab-pane fade show active" id="nav-Minutes" role="tabpanel" aria-labelledby="nav-home-tab" tabindex="0">
                                                        <div class="row mt-2 align-items-end">
                                                            <div class="col-3">
                                                                <div class="form-group">
                                                                    <div class="form-label">Minutes</div>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text pe-0"><i class="cp-calendar"></i></span>
                                                                        @Html.TextBox("txtMins", null, new { id = "txtMins", type = "number", maxlength = "2", min = "0", max = "59", pattern = "d{2}", @class = "form-control", @placeholder = "Enter Mins", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                        <span class="input-group-text small text-secondary">mins</span>
                                                                    </div>
                                                                    <span asp-validation-for="CronExpression" id="CronMin-error"></span>
                                                                </div>
                                                            </div>
                                                            <div class="col-3 d-none">
                                                                <div class="form-group">
                                                                    <div class="form-label"></div>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text pe-0"><i class="cp-calendar"></i></span>
                                                                        <input type="number" class="form-control" placeholder="Select Minute" />
                                                                        <span class="input-group-text small pe-0 text-secondary">Minute</span>
                                                                    </div>
                                                                    <span asp-validation-for="CronExpression" id="CronMin-error"></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane fade" id="nav-Hourly" role="tabpanel" aria-labelledby="nav-Hourly-tab" tabindex="0">
                                                        <div class="row mt-2">
                                                            <div class="col-4">
                                                                <div class="form-group">
                                                                    <div class="form-label">Hours</div>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text">
                                                                            <i class="cp-calendar"></i>
                                                                        </span>
                                                                        @Html.TextBox("txtHours", null, new { id = "txtHours", type = "number", min = "0", max = "23", @class = "form-control", @placeholder = "Enter Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                        <span class="input-group-text fs-8 ms-1 text-secondary">
                                                                            hrs
                                                                        </span>
                                                                    </div>
                                                                    <span asp-validation-for="CronExpression" id="CronHourly-error"></span>
                                                                </div>
                                                            </div>
                                                            @*    <div class="col-4 d-none">
                                                            <div class="form-group">
                                                            <div class="form-label invisible">Mins</div>
                                                            <div class="input-group">
                                                            <span class="input-group-text">
                                                            <i class="cp-calendar"></i>
                                                            </span>
                                                            <input autocomplete="off" class="form-control" max="60" min="0" placeholder="Min(s)" type="number">
                                                            <span class="input-group-text fs-8 ms-1">
                                                            Mins
                                                            </span>
                                                            </div>

                                                            </div>
                                                            </div> *@
                                                            <div class="col-xl-6">
                                                                <div class="form-group">
                                                                    <div class="form-label">Minutes</div>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text">
                                                                            <i class="cp-calendar"></i>
                                                                        </span>
                                                                        @Html.TextBox("txtMinutes", null, new { id = "txtMinutes", type = "number", min = "0", max = "59", @class = "form-control", @placeholder = "Enter Min(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                        <span class="input-group-text form-label mb-0 text-secondary">mins</span>
                                                                    </div>
                                                                    <span asp-validation-for="CronExpression" id="CronHourMin-error"></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane fade" id="nav-Daily" role="tabpanel" aria-labelledby="nav-Daily-tab" tabindex="0">
                                                        <div class="row mt-2 align-items-center">
                                                            <div class="col-4">
                                                                <div class="form-group flex-fill ">
                                                                    <label class="animation-label form-label">
                                                                        Select Day Type
                                                                    </label>
                                                                    <div class="">
                                                                        <span class="input-group-text"></span>
                                                                        <div class="form-check form-check-inline">
                                                                            <input name="daysevery" aria-label="Every Day" type="radio" id="defaultCheck-everyday" class="form-check-input custom-cursor-default-hover" value="everyday" cursorshover="true">
                                                                            <label for="defaultCheck-everyday" class="form-check-label custom-cursor-default-hover" cursorshover="true">Every Day</label>
                                                                        </div>
                                                                        <div class="form-check form-check-inline">
                                                                            <input name="daysevery" aria-label="Every Week Day" type="radio" id="defaultCheck-MON-FRI" class="form-check-input custom-cursor-default-hover" value="MON-FRI">
                                                                            <label for="defaultCheck-MON-FRI" class="form-check-label custom-cursor-default-hover" cursorshover="true">Every Week Day</label>
                                                                        </div>
                                                                    </div>
                                                                    <span asp-validation-for="CronExpression" id="Crondaysevery-error"></span>
                                                                </div>
                                                            </div>
                                                            <div class="col-4">
                                                                <div class="form-group">
                                                                    <div class="form-label">Starts at</div>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text"><i class="cp-calendar"></i></span>
                                                                        @Html.TextBox("everyHours", null, new { id = "everyHours", type = "time", min = "0", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                    </div>
                                                                    <span asp-validation-for="CronExpression" id="CroneveryHour-error"></span>
                                                                </div>
                                                            </div>
                                                            @* <div class="col">
                                                            <div class="mb-3 form-group">
                                                            <div class="form-label" title="Minutes">Minutes</div>
                                                            <div class="input-group">
                                                            <span class="input-group-text">
                                                            <i class="cp-calendar"></i>
                                                            </span>
                                                            @Html.TextBox("everyMinutes", null, new { id = "everyMinutes", type = "number", min = "0", max = "59", @class = "form-control", @placeholder = "Min(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                            <span class="input-group-text form-label mb-0 text-secondary">Mins</span>
                                                            </div>
                                                            <span asp-validation-for="CronExpression" id="CroneveryMin-error"></span>
                                                            </div>
                                                            </div> *@
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane fade" id="nav-Weekly" role="tabpanel" aria-labelledby="nav-Weekly-tab" tabindex="0">
                                                        <div class="row row-cols-2 mt-2">
                                                            <div class="col-12">
                                                                <div class="form-group">
                                                                    <label class="form-label custom-cursor-default-hover">Select Day(s)</label>
                                                                    <div class="bg-transparent input-group">
                                                                        <div class="form-check form-check-inline">
                                                                            <input name="weekDays" aria-label="Monday" type="checkbox" id="defaultCheck-1" class="form-check-input" value="MON"><label for="defaultCheck-1" class="form-check-label custom-cursor-default-hover">Monday</label>
                                                                        </div>
                                                                        <div class="form-check form-check-inline">
                                                                            <input name="weekDays" aria-label="Tuesday" type="checkbox" id="defaultCheck-2" class="form-check-input" value="TUE"><label for="defaultCheck-2" class="form-check-label">Tuesday</label>
                                                                        </div>
                                                                        <div class="form-check form-check-inline">
                                                                            <input name="weekDays" aria-label="Wednesday" type="checkbox" id="defaultCheck-3" class="form-check-input" value="WED"><label for="defaultCheck-3" class="form-check-label" cursorshover="true">Wednesday</label>
                                                                        </div>
                                                                        <div class="form-check form-check-inline">
                                                                            <input name="weekDays" aria-label="Thursday" type="checkbox" id="defaultCheck-4" class="form-check-input" value="THU"><label for="defaultCheck-4" class="form-check-label custom-cursor-default-hover" cursorshover="true">Thursday</label>
                                                                        </div>
                                                                        <div class="form-check form-check-inline">
                                                                            <input name="weekDays" aria-label="Friday" type="checkbox" id="defaultCheck-5" class="form-check-input" value="FRI" cursorshover="true"><label for="defaultCheck-5" class="form-check-label">Friday</label>
                                                                        </div>
                                                                        <div class="form-check form-check-inline">
                                                                            <input name="weekDays" aria-label="Saturday" type="checkbox" id="defaultCheck-6" class="form-check-input" value="SAT"><labelfor ="defaultCheck-6" class="form-check-label">Saturday</label>
                                                                        </div>
                                                                        <div class="form-check form-check-inline">
                                                                            <input name="weekDays" aria-label="Sunday" type="checkbox" id="defaultCheck-0" class="form-check-input" value="SUN"><label for="defaultCheck-0" class="form-check-label">Sunday</label>
                                                                        </div>
                                                                    </div>
                                                                    <span asp-validation-for="CronExpression" id="CronDay-error"></span>
                                                                </div>
                                                            </div>
                                                            <div class="col-4">
                                                                <div class="form-group">
                                                                    <div class="form-label">Starts at</div>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text"><i class="cp-calendar"></i></span>
                                                                        @Html.TextBox("ddlHours", null, new { id = "ddlHours", type = "time", min = "0", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                    </div>
                                                                    <span asp-validation-for="CronExpression" id="CronddlHour-error"></span>
                                                                </div>
                                                            </div>
                                                            @* <div class="col">
                                                            <div class="mb-3 form-group">
                                                            <div class="form-label" title="Minutes">Minutes</div>
                                                            <div class="input-group">
                                                            <span class="input-group-text">
                                                            <i class="cp-calendar"></i>
                                                            </span>
                                                            @Html.TextBox("ddlMinutes", null, new { id = "ddlMinutes", type = "number", min = "0", max = "59", @class = "form-control", @placeholder = "Min(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                            <span class="input-group-text form-label mb-0 text-secondary">Mins</span>
                                                            </div>
                                                            <span asp-validation-for="CronExpression" id="CronddlMin-error"></span>
                                                            </div>
                                                            </div> *@
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane fade" id="nav-Monthly" role="tabpanel" aria-labelledby="nav-Monthly-tab" tabindex="0">
                                                        <div class="row row-cols-2 mt-2">
                                                            <div class="col-4">
                                                                <div class="mb-3 form-group">
                                                                    <div class="form-label">Select Month And Year</div>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text">
                                                                            <i class="cp-calendar"></i>
                                                                        </span>
                                                                        <input name="month" autocomplete="off" type="month"
                                                                               id="lblMonth"
                                                                               class="form-control custom-cursor-default-hover"
                                                                               cursorshover="true" />
                                                                    </div>
                                                                    <span asp-validation-for="CronExpression" id="CronMonthly-error"></span>
                                                                </div>
                                                            </div>
                                                            <div class="col-12 pe-0">
                                                                <div class="mb-3 form-group text-justify " style="display: inline-table;">
                                                                    <div class="form-label mb-2">Select Date(s)</div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox1" value="1">
                                                                        <label class="form-check-label checklabel">1</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox2" value="2">
                                                                        <label class="form-check-label checklabel">2</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox3" value="3">
                                                                        <label class="form-check-label checklabel">3</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox4" value="4">
                                                                        <label class="form-check-label checklabel">4</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox5" value="5">
                                                                        <label class="form-check-label checklabel">5</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox6" value="6">
                                                                        <label class="form-check-label checklabel">6</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox7" value="7">
                                                                        <label class="form-check-label checklabel">7</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox8" value="8">
                                                                        <label class="form-check-label checklabel">8</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox9" value="9">
                                                                        <label class="form-check-label checklabel">9</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox10" value="10">
                                                                        <label class="form-check-label checklabel">10</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox11" value="11">
                                                                        <label class="form-check-label checklabel">11</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox12" value="12">
                                                                        <label class="form-check-label checklabel">12</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox13" value="13">
                                                                        <label class="form-check-label checklabel">13</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox14" value="14">
                                                                        <label class="form-check-label checklabel">14</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox15" value="15">
                                                                        <label class="form-check-label checklabel">15</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox16" value="16">
                                                                        <label class="form-check-label checklabel">16</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox17" value="17">
                                                                        <label class="form-check-label checklabel">17</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox18" value="18">
                                                                        <label class="form-check-label checklabel">18</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox19" value="19">
                                                                        <label class="form-check-label checklabel">19</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox20" value="20">
                                                                        <label class="form-check-label checklabel">20</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox21" value="21">
                                                                        <label class="form-check-label checklabel">21</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox22" value="22">
                                                                        <label class="form-check-label checklabel">22</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox23" value="23">
                                                                        <label class="form-check-label checklabel">23</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox24" value="24">
                                                                        <label class="form-check-label checklabel">24</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox25" value="25">
                                                                        <label class="form-check-label checklabel">25</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox26" value="26">
                                                                        <label class="form-check-label checklabel">26</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox27" value="27">
                                                                        <label class="form-check-label checklabel">27</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox28" value="28">
                                                                        <label class="form-check-label checklabel">28</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox29" value="29">
                                                                        <label class="form-check-label checklabel">29</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox30" value="30">
                                                                        <label class="form-check-label checklabel">30</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline" style="width: 30px;">
                                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox31" value="31">
                                                                        <label class="form-check-label checklabel">31</label>
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <span asp-validation-for="CronExpression" id="CronMon-error"></span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-4">
                                                                <div class="form-group">
                                                                    <div class="form-label">Starts at</div>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text">
                                                                            <i class="cp-calendar"></i>
                                                                        </span>
                                                                        @Html.TextBox("MonthlyHours", null, new { id = "MonthlyHours", type = "time", min = "0", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                    </div>
                                                                    <span asp-validation-for="CronExpression" id="MonthlyHours-error"></span>
                                                                </div>
                                                            </div>
                                                            @* <div class="col">
                                                            <div class="form-group">
                                                            <div class="form-label" title="Minutes">Minutes</div>
                                                            <div class="input-group">
                                                            <span class="input-group-text">
                                                            <i class="cp-calendar"></i>
                                                            </span>
                                                            @Html.TextBox("MonthlyMins", null, new { id = "MonthlyMins", type = "number", min = "0", max = "59", @class = "form-control", @placeholder = "Min(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                            <span class="input-group-text form-label mb-0 text-secondary">Mins</span>
                                                            </div>
                                                            <span asp-validation-for="CronExpression" id="MonthlyMins-error"></span>
                                                            </div>
                                                            </div> *@
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                       
                        </div>

                        <div class="form-group">
                            <label class="form-label custom-cursor-default-hover" cursorshover="true">State</label>
                            <div class="row row-cols-4">
                                <div class="col-auto col">
                                    <div class="form-check">
                                        <input asp-for="State" name="state" type="radio" id="textStateActive"
                                               class="form-check-input" value="Active"><label for="textStateActive"
                                                                                              class="form-check-label" cursorshover="true">Active</label>
                                    </div>
                                </div>
                                <div class="col-auto col">
                                    <div class="form-check">
                                        <input asp-for="State" name="state" type="radio" id="textStateInactive"
                                               class="form-check-input" value="InActive" cursorshover="true"><label for="textStateInactive" class="form-check-label"
                                                                                                                    cursorshover="true">InActive</label>
                                    </div>
                                </div>
                            </div>
                            <span asp-validation-for="State" id="state-error"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary">
                        <i class="cp-note me-1"></i>Note: All fields are mandatory
                        except optional
                    </small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm monitorbtn-cancel"  data-bs-dismiss="modal">Cancel</button>
                        <button id="SaveFunction" type="button" class="btn btn-primary btn-sm">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>


    <!--Modal Active-->
    <div class="modal fade" id="ActiveModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" />
                </div>
                <div class="modal-body text-center pt-0">
                    <h4>Are you sure?</h4>
                    <p class="my-3">Switch to <span class="font-weight-bolder text-primary">active</span> mode ?</p>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="button" class="btn btn-primary btn-sm">Yes</button>
                </div>
            </div>
        </div>
    </div>
    <!--Modal Delete-->
    <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <form asp-controller="MonitoringJob" asp-action="Delete" asp-route-id="textDeleteId" method="post" enctype="multipart/form-data" class="w-100">
                <div class="modal-content">
                    <div class="modal-header p-0">
                        <img class="delete-img" src="/img/isomatric/delete.png" />
                    </div>
                    <div class="modal-body text-center pt-0">
                        <h4>Are you sure?</h4>
                        <p class="d-flex align-items-center justify-content-center gap-1">You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="deleteData"></span> data?</p>
                        <input asp-for="Id" type="hidden" id="textDeleteId" name="id" class="form-control" />
                    </div>
                    <div class="modal-footer gap-2 justify-content-center">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                        <button type="submit" class="btn btn-primary btn-sm" id="confirmDeleteButton">Yes</button>
                    </div>
                </div>
            </form>
        </div>
    </div>


    <!--Modal Error-->

    <div class="modal fade" id="ErrorModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-md modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-error-message"></i><span>Error Message</span></h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
                </div>
                <div class="modal-body" >
                    <div class="list-group list-group-flush Profile-Select">
                        <div class="d-grid text-start">
                           <p id="error_message"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/common/slide_toggle.js"></script>
<script src="~/js/Manage/Job Management/Monitoring Job/jobManagement.js"></script>
@* <script src="~/js/common.js"></script> *@