﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.IncidentModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.Incident.Queries.GetPaginatedList;

public class
    GetIncidentPaginatedListQueryHandler : IRequestHandler<GetIncidentPaginatedListQuery,
        PaginatedResult<IncidentListVm>>
{
    private readonly IIncidentRepository _incidentRepository;
    private readonly IMapper _mapper;

    public GetIncidentPaginatedListQueryHandler(IIncidentRepository incidentRepository, IMapper mapper)
    {
        _incidentRepository = incidentRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<IncidentListVm>> Handle(GetIncidentPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var specification = new IncidentFilterSpecification(request.SearchString);
       
        var queryable =await _incidentRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, specification, request.SortColumn, request.SortOrder);

        var incidentList = _mapper.Map<PaginatedResult<IncidentListVm>>(queryable);
        
        return incidentList;
    }
}