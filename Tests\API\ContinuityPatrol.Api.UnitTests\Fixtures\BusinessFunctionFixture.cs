﻿using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class BusinessFunctionFixture : IDisposable
{
    public List<BusinessFunctionListVm> BusinessFunctionListVm { get; set; }
    public BusinessFunctionDetailVm BusinessFunctionDetailVm { get; set; }
    public CreateBusinessFunctionCommand CreateBusinessFunctionCommand { get; set; }
    public UpdateBusinessFunctionCommand UpdateBusinessFunctionCommand { get; set; }
    public List<BusinessFunctionNameVm> BusinessFunctionNameVm { get; set; }

    public BusinessFunctionFixture()
    {
        var fixture = new Fixture();

        BusinessFunctionListVm = fixture.Create<List<BusinessFunctionListVm>>();
        BusinessFunctionDetailVm = fixture.Create<BusinessFunctionDetailVm>();
        CreateBusinessFunctionCommand = fixture.Create<CreateBusinessFunctionCommand>();
        UpdateBusinessFunctionCommand = fixture.Create<UpdateBusinessFunctionCommand>();
        BusinessFunctionNameVm = fixture.Create<List<BusinessFunctionNameVm>>();
    }

    public void Dispose()
    {
        
    }
}