﻿using Microsoft.AspNetCore.Http;

namespace ContinuityPatrol.Application.Features.DRCalendar.Events.SendEmail;

public class DrCalendarSendEmailEvent : INotification
{
    public string Id { get; set; }
    public string BusinessServiceId { get; set; }
    public string Description { get; set; }
    public string ActivityName { get; set; }
    public int InvitationNo { get; set; }
    public DateTime ScheduledStartDate { get; set; }
    public DateTime ScheduledEndDate { get; set; }
    public string Responsibility { get; set; }
    public string RecipientTwo { get; set; }
    public string WorkflowProfiles { get; set; }
    public IFormFile File { get; set; }
    public string Location { get; set; }
    public string FileName { get; set; }
    public string MailSentActivity { get; set; }

    public string SetReminder { get; set; }
}