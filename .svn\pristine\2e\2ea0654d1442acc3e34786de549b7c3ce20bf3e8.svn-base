﻿namespace ContinuityPatrol.Application.Features.Replication.Commands.Create;

public class CreateReplicationCommand : IRequest<CreateReplicationResponse>
{
    public string Name { get; set; }

    public string Type { get; set; }

    public string TypeId { get; set; }

    public string CompanyId { get; set; }

    public string SiteId { get; set; }

    public string SiteName { get; set; }

    public string Logo { get; set; }

    public string Properties { get; set; }

    public string LicenseId { get; set; }

    public string LicenseKey { get; set; }

    public string BusinessServiceId { get; set; }

    public string BusinessServiceName { get; set; }

    public string FormVersion { get; set; }

    public override string ToString()
    {
        return $"Name: {Name};";
    }
}