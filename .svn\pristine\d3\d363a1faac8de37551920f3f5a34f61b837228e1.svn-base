using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetPaginatedList;

public class GetCyberAirGapStatusPaginatedListQueryHandler : IRequestHandler<GetCyberAirGapStatusPaginatedListQuery,
    PaginatedResult<CyberAirGapStatusListVm>>
{
    private readonly ICyberAirGapStatusRepository _cyberAirGapStatusRepository;
    private readonly IMapper _mapper;

    public GetCyberAirGapStatusPaginatedListQueryHandler(IMapper mapper,
        ICyberAirGapStatusRepository cyberAirGapStatusRepository)
    {
        _mapper = mapper;
        _cyberAirGapStatusRepository = cyberAirGapStatusRepository;
    }

    public async Task<PaginatedResult<CyberAirGapStatusListVm>> Handle(GetCyberAirGapStatusPaginatedListQuery request,
        CancellationToken cancellationToken)
    {  
        var productFilterSpec = new CyberAirGapStatusFilterSpecification(request.SearchString);

        var queryable =await _cyberAirGapStatusRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var cyberAirGapStatusList = _mapper.Map<PaginatedResult<CyberAirGapStatusListVm>>(queryable);
           
        return cyberAirGapStatusList;

        //var queryable = _cyberAirGapStatusRepository.GetPaginatedQuery();

        //var productFilterSpec = new CyberAirGapStatusFilterSpecification(request.SearchString);

        //var cyberAirGapStatusList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<CyberAirGapStatusListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return cyberAirGapStatusList;
    }
}