﻿@model ContinuityPatrol.Domain.ViewModels.LoadBalancerModel.LoadBalancerViewModel

<div class="modal-dialog modal-lg modal-dialog-scrollabel modal-dialog-centered Organization_modal">
    <div class="modal-content">
        <div class="modal-header">
            <h6 class="page_title">
                <i class="cp-load-balancer me-1"></i>  Load Balancer Configuration
            </h6>
            <button type="button" class="btn-close" title="Close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="wizard-content">
                <form id="loadBalancerConfigureForm" class="tab-wizard wizard-circle wizard clearfix">
                    @Html.AntiForgeryToken()
                    <section>
                        <div class="mb-3 ">
                            <div class="d-flex gap-2">
                                <div>Configuration Type</div>
                            </div>
                            <div class="form-group mt-2">
                                <div class="d-flex mt-4 gap-2">

                                    <div>
                                        <input asp-for="TypeCategory" type="radio" class="btn-check pair" name="typecategory" id="prIcon" autocomplete="off" value="LoadBalancer" checked>
                                        <label class="site_type btn border-secondary-subtle" for="prIcon"><i class="cp-load-balancer fs-1" id="prVal"></i> </label>
                                        <div class="text-center mt-2 d-block text-truncate" style="max-width:80px">Load Balancer </div>
                                    </div>
                                    <div>
                                        <input type="radio" asp-for="TypeCategory" class="btn-check pair" name="typecategory" id="drIcon" autocomplete="off" value="CPNode">
                                        <label class="site_type btn border-secondary-subtle" for="drIcon"> <i class="cp-network fs-1" id="drVal"></i></label>
                                        <div class="text-center mt-2 d-block text-truncate" style="max-width:80px"> CP Node </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3 mb-3 d-none">
                            <div class="form-group">
                                <div class="form-label">URL</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-url"></i></span>
                                    <input type="text" id="textURL" class="form-control" placeholder="Enter URL" maxlength="30" autocomplete="off" autofocus />
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-group">
                                <div class="form-label">Name</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-name"></i></span>
                                    <input asp-for="Name" type="text" id="nameLB" type="text" class="form-control" placeholder="Enter Name" maxlength="100" autocomplete="off" autofocus />
                                </div>
                                <span asp-validation-for="Name" id="loadBalNameError"></span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-group">
                                <div class="form-label">IP Address</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-ip-address"></i>
                                    </span>
                                    <input asp-for="IPAddress" type="text" id="IpAddressLB" type="text" class="form-control" placeholder="Enter IP Address" maxlength="15" autocomplete="off" />
                                </div>
                                <span asp-validation-for="IPAddress" id="loadBalIPAddressError"></span>
                            </div>
                        </div>
                        <div class="col-12" id="defaultConnection">

                            @* <div class="form-check form-check-inline">
                                <input class="form-check-input Checkboxs" asp-for="IsDefault" type="checkbox" id="default" value="options1" checked>
                                <label class="form-check-label" for="default">Is Default</label>
                            </div> *@

                            <div class="form-check form-check-inline" id="testDiv">
                                <input class="form-check-input Checkboxs" asp-for="IsConnection" type="checkbox" id="chkLBTestConnection" value="options2" checked>
                                <label class="form-check-label" for="TestDisconection">Is Testconnection</label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-group">
                                <div class="row row-cols-3">
                                    <label class="animation-label col-3 form-label custom-cursor-default-hover"
                                           id="lblconnection">Connection Type</label>
                                    <div class="col-auto col d-none" id="node-http">
                                        <div class="form-check">
                                            <input asp-for="ConnectionType" name="connectionType" type="radio"
                                                   id="http" class="form-check-input" value="http" checked><label title="" for="http" class="form-check-label"
                                                                                                                  cursorshover="true">http</label>
                                        </div>
                                    </div>
                                    <div class="col-auto col">
                                        <div class="form-check">
                                            <input asp-for="ConnectionType" name="connectionType" type="radio"
                                                   id="https" class="form-check-input" value="https"><label title="" for="https" class="form-check-label">https</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input asp-for="IsNodeStatus" id="nodeStatus" value="true" type="checkbox" hidden />
                        @*                                 <input asp-for="ConnectionType" value="https" type="text" hidden />
                        *@
                        <div class="mb-3">
                            <div class="form-group">
                                <div class="row row-cols-4">
                                    <label class="animation-label col-3 form-label custom-cursor-default-hover"
                                           id="lblType">Service Type</label>

                                    <div class="col-auto col" id="typeAll">
                                        <div class="form-check">
                                            <input asp-for="Type" name="type" type="radio"
                                                   id="all" class="form-check-input"
                                                   value="All" checked><label title=""
                                                                              for="all" class="form-check-label">
                                                All
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-auto col">
                                        <div class="form-check">
                                            <input asp-for="Type" name="type" type="radio"
                                                   id="monitorService" class="form-check-input"
                                                   value="MonitorService"><label title=""
                                                                                  for="monitorService" class="form-check-label">
                                                Monitor
                                                Service
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-auto col">
                                        <div class="form-check">
                                            <input asp-for="Type" name="type" type="radio"
                                                   id="workflowService" class="form-check-input"
                                                   value="WorkflowService"><label title="" for="workflowService"
                                                                                   class="form-check-label">
                                                Workflow
                                                Service
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-auto col">
                                        <div class="form-check">
                                            <input asp-for="Type" name="type" type="radio" id="bothService"
                                                   class="form-check-input" value="ResiliencyReadyService"><label title=""
                                                                                                                    for="bothService" class="form-check-label">
                                                Resiliency Ready Service
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-group">
                                <div class="form-label">Host Name</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-host-name"></i></span>
                                    <input asp-for="HostName" id="hostNameLB" type="text" class="form-control" placeholder="Enter Host Name" maxlength="30" autocomplete="off" />
                                </div>
                                <span asp-validation-for="HostName" id="loadBalHostNameError"></span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-group">
                                <div class="form-label">Port</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-port"></i>
                                    </span>
                                    <input asp-for="Port" id="portLB" type="text" class="form-control" placeholder="Enter Port" autocomplete="off" maxlength="5" />
                                </div>
                                <span asp-validation-for="Port" id="loadBalPortError"></span>
                            </div>
                        </div>
                    </section>
                    <input asp-for="Id" type="hidden" id="loadBalancerId" class="form-control" />
                    <input asp-for="HealthStatus" type="hidden" id="status" class="form-control" />
                    <input asp-for="IsDefault" type="hidden" value="false" id="default" value="options1" checked>
                </form>
            </div>
        </div>


        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary">
                <i class="cp-note me-1"></i>Note: All fields are mandatory
                except optional
            </small>
            <div class="gap-2 d-flex">
                <button id="cancel" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button id="save" class="btn btn-primary btn-sm">Save</button>
            </div>
        </div>
    </div>
</div>

@section Scripts
  {
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}