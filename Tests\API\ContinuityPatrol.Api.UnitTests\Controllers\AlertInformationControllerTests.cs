using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.AlertInformation.Commands.Create;
using ContinuityPatrol.Application.Features.AlertInformation.Commands.Delete;
using ContinuityPatrol.Application.Features.AlertInformation.Commands.Update;
using ContinuityPatrol.Application.Features.AlertInformation.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AlertInformation.Queries.GetDetailByCode;
using ContinuityPatrol.Application.Features.AlertInformation.Queries.GetList;
using ContinuityPatrol.Application.Features.AlertInformation.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AlertInformationModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class AlertInformationControllerTests : IClassFixture<AlertInformationFixture>
{
    private readonly AlertInformationFixture _alertInformationFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly AlertInformationController _controller;

    public AlertInformationControllerTests(AlertInformationFixture alertInformationFixture)
    {
        _alertInformationFixture = alertInformationFixture;

        var testBuilder = new ControllerTestBuilder<AlertInformationController>();
        _controller = testBuilder.CreateController(
            _ => new AlertInformationController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAlertInformation_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertInformationListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_alertInformationFixture.AlertInformationListVm);

        // Act
        var result = await _controller.GetAlertInformation();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertInformation = Assert.IsAssignableFrom<List<AlertInformationListVm>>(okResult.Value);
        Assert.Equal(3, alertInformation.Count);
    }

    [Fact]
    public async Task GetAlertInformation_ReturnsEmptyList_WhenNoAlertInformationExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertInformationListQuery>(), default))
            .ReturnsAsync(new List<AlertInformationListVm>());

        // Act
        var result = await _controller.GetAlertInformation();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertInformation = Assert.IsAssignableFrom<List<AlertInformationListVm>>(okResult.Value);
        Assert.Empty(alertInformation);
    }

    [Fact]
    public async Task GetAlertInformationById_ReturnsAlertInformation_WhenIdIsValid()
    {
        // Arrange
        var alertInformationId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertInformationDetailQuery>(q => q.Id == alertInformationId), default))
            .ReturnsAsync(_alertInformationFixture.AlertInformationDetailVm);

        // Act
        var result = await _controller.GetAlertInformationById(alertInformationId);

        // Assert
        Assert.IsType<OkObjectResult>(result.Result);
    }

    [Fact]
    public async Task GetAlertInformationById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetAlertInformationById("invalid-guid"));
    }

    [Fact]
    public async Task CreateAlertInformation_Returns201Created()
    {
        // Arrange
        var command = _alertInformationFixture.CreateAlertInformationCommand;
        var expectedMessage = $"AlertInformation '{command.Type}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAlertInformationResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateAlertInformation(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateAlertInformationResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task CreateAlertInformation_Throws_WhenTypeExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateAlertInformationCommand>(), default))
            .ThrowsAsync(new InvalidOperationException("Type exists"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.CreateAlertInformation(_alertInformationFixture.CreateAlertInformationCommand));
    }

    [Fact]
    public async Task UpdateAlertInformation_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"AlertInformation '{_alertInformationFixture.UpdateAlertInformationCommand.Type}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateAlertInformationCommand>(), default))
            .ReturnsAsync(new UpdateAlertInformationResponse
            {
                Message = expectedMessage,
                Id = _alertInformationFixture.UpdateAlertInformationCommand.Id
            });

        // Act
        var result = await _controller.UpdateAlertInformation(_alertInformationFixture.UpdateAlertInformationCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateAlertInformationResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAlertInformation_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "AlertInformation 'Critical' has been deleted successfully!.";
        var alertInformationId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAlertInformationCommand>(c => c.Id == alertInformationId), default))
            .ReturnsAsync(new DeleteAlertInformationResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteAlertInformation(alertInformationId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteAlertInformationResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAlertInformation_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteAlertInformation("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedAlertInformation_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetAlertInformationPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _alertInformationFixture.AlertInformationListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertInformationPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<AlertInformationListVm>.Success(
                data: expectedData,
                count: expectedData.Count,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        // Act
        var result = await _controller.GetPaginatedAlertInformation(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<AlertInformationListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<AlertInformationListVm>>(okResult.Value);

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(3, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task GetAlertInformationByCode_ReturnsAlertInformation_WhenCodeIsValid()
    {
        // Arrange
        var code = "CRIT_001";
        var expectedResult = new List<AlertInformationDetailByCodeVm>
        {
            new() { Id = Guid.NewGuid().ToString(), Code = code, Type = "Critical" }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertInformationDetailByCodeQuery>(q => q.Code == code), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAlertInformationByCode(code);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertInformation = Assert.IsAssignableFrom<List<AlertInformationDetailByCodeVm>>(okResult.Value);
        Assert.Single(alertInformation);
        Assert.Equal(code, alertInformation.First().Code);
    }

    [Fact]
    public async Task GetAlertInformation_CallsCorrectQuery()
    {
        // Arrange
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertInformationListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<AlertInformationListVm>());

        // Act
        await _controller.GetAlertInformation();

        // Assert
        Assert.True(queryExecuted);
    }

    [Fact]
    public async Task CreateAlertInformation_ClearsCacheAfterCreation()
    {
        // Arrange
        var command = _alertInformationFixture.CreateAlertInformationCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAlertInformationResponse
            {
                Message = "Created successfully",
                Id = Guid.NewGuid().ToString()
            });

        // Act
        await _controller.CreateAlertInformation(command);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public async Task UpdateAlertInformation_ClearsCacheAfterUpdate()
    {
        // Arrange
        var command = _alertInformationFixture.UpdateAlertInformationCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateAlertInformationResponse
            {
                Message = "Updated successfully",
                Id = command.Id
            });

        // Act
        await _controller.UpdateAlertInformation(command);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public async Task DeleteAlertInformation_ClearsCacheAfterDeletion()
    {
        // Arrange
        var alertInformationId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAlertInformationCommand>(c => c.Id == alertInformationId), default))
            .ReturnsAsync(new DeleteAlertInformationResponse
            {
                IsActive = false,
                Message = "Deleted successfully"
            });

        // Act
        await _controller.DeleteAlertInformation(alertInformationId);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task CreateAlertInformation_ValidatesAlertFrequency()
    {
        // Arrange
        var command = new CreateAlertInformationCommand
        {
            Type = "Test Alert",
            Severity = "High",
            Code = "TEST_001",
            AlertFrequency = 0 // Invalid frequency
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("AlertFrequency must be greater than 0"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateAlertInformation(command));
    }

    [Fact]
    public async Task UpdateAlertInformation_ValidatesInformationExists()
    {
        // Arrange
        var command = new UpdateAlertInformationCommand
        {
            Id = Guid.NewGuid().ToString(),
            Type = "Updated Alert",
            Severity = "Medium",
            Code = "UPDATED_001",
            AlertFrequency = 15
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("AlertInformation not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateAlertInformation(command));
    }

    [Fact]
    public async Task GetAlertInformationByCode_HandlesComplexCodes()
    {
        // Arrange
        var complexCode = "COMPLEX_ALERT_CODE_WITH_UNDERSCORES_123";
        var expectedResult = new List<AlertInformationDetailByCodeVm>
        {
            new() { Id = Guid.NewGuid().ToString(), Code = complexCode, Type = "Complex Alert Type" }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertInformationDetailByCodeQuery>(q => q.Code == complexCode), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAlertInformationByCode(complexCode);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertInformation = Assert.IsAssignableFrom<List<AlertInformationDetailByCodeVm>>(okResult.Value);
        Assert.Single(alertInformation);
        Assert.Equal(complexCode, alertInformation.First().Code);
    }

    [Fact]
    public async Task GetPaginatedAlertInformation_HandlesFilteringBySeverity()
    {
        // Arrange
        var query = new GetAlertInformationPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var highSeverityAlerts = new List<AlertInformationListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Critical System Alert",
                Severity = "High",
                Code = "HIGH_001",
                AlertFrequency = 1
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Critical Database Alert",
                Severity = "High",
                Code = "HIGH_002",
                AlertFrequency = 2
            }
        };

        var expectedPaginatedResult = PaginatedResult<AlertInformationListVm>.Success(
            data: highSeverityAlerts,
            count: highSeverityAlerts.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertInformationPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedAlertInformation(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<AlertInformationListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<AlertInformationListVm>>(okResult.Value);

        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, alert => Assert.Equal("High", alert.Severity));
    }

    [Fact]
    public async Task CreateAlertInformation_HandlesVeryHighFrequency()
    {
        // Arrange
        var command = new CreateAlertInformationCommand
        {
            Type = "High Frequency Alert",
            Severity = "Medium",
            Code = "FREQ_001",
            AlertFrequency = 86400 // Every second for a day
        };

        var expectedMessage = $"AlertInformation '{command.Type}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAlertInformationResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateAlertInformation(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateAlertInformationResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAlertInformation_VerifiesInformationIsDeactivated()
    {
        // Arrange
        var alertInformationId = Guid.NewGuid().ToString();
        var expectedMessage = "AlertInformation 'Test Information' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAlertInformationCommand>(c => c.Id == alertInformationId), default))
            .ReturnsAsync(new DeleteAlertInformationResponse
            {
                IsActive = false, // Verify it's deactivated
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteAlertInformation(alertInformationId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteAlertInformationResponse>(okResult.Value);
        Assert.False(response.IsActive);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetAlertInformation_HandlesEmptyDatabase()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertInformationListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<AlertInformationListVm>());

        // Act
        var result = await _controller.GetAlertInformation();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertInformation = Assert.IsAssignableFrom<List<AlertInformationListVm>>(okResult.Value);
        Assert.Empty(alertInformation);
    }

    [Fact]
    public async Task GetAlertInformationByCode_HandlesNonExistentCode()
    {
        // Arrange
        var nonExistentCode = "NONEXISTENT_999";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertInformationDetailByCodeQuery>(q => q.Code == nonExistentCode), default))
            .ReturnsAsync(new List<AlertInformationDetailByCodeVm>());

        // Act
        var result = await _controller.GetAlertInformationByCode(nonExistentCode);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertInformation = Assert.IsAssignableFrom<List<AlertInformationDetailByCodeVm>>(okResult.Value);
        Assert.Empty(alertInformation);
    }
}
