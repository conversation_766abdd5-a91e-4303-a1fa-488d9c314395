﻿using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Contracts.Version;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowAction.Commands;

public class CreateWorkflowActionTests : IClassFixture<WorkflowActionFixture>, IClassFixture<SolutionHistoryFixture>
{
    private readonly WorkflowActionFixture _workflowActionFixture;

    private readonly SolutionHistoryFixture _solutionHistoryFixture;

    private readonly Mock<IWorkflowActionRepository> _mockWorkflowActionRepository;

    private readonly Mock<ISolutionHistoryRepository> _mockSolutionHistoryRepository;

    private readonly CreateWorkflowActionCommandHandler _handler;

    public CreateWorkflowActionTests(WorkflowActionFixture workflowActionFixture, SolutionHistoryFixture solutionHistoryFixture)
    {
        _workflowActionFixture = workflowActionFixture;

        _solutionHistoryFixture = solutionHistoryFixture;

        _workflowActionFixture.WorkflowActions[0].Version = "6.0.0";

        _solutionHistoryFixture.SolutionHistories[0].Version = _workflowActionFixture.WorkflowActions[0].Version;

        var mockPublisher = new Mock<IPublisher>();

        var mockVersionManager = new Mock<IVersionManager>();

        var configuration = new Mock<IConfiguration>();

        var configSection = new Mock<IConfigurationSection>();

        configuration.Setup(x => x.GetSection("UpdateApiVersion:Version")).Returns(configSection.Object);

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        _mockWorkflowActionRepository = WorkflowActionRepositoryMocks.CreateWorkflowActionRepository(_workflowActionFixture.WorkflowActions);

        _mockSolutionHistoryRepository = SolutionHistoryRepositoryMocks.CreateSolutionHistoryRepository(_solutionHistoryFixture.SolutionHistories);

        _handler = new CreateWorkflowActionCommandHandler(_workflowActionFixture.Mapper, _mockWorkflowActionRepository.Object, mockPublisher.Object, mockLoggedInUserService.Object, _mockSolutionHistoryRepository.Object, mockVersionManager.Object);
    }

    [Fact]
    public async Task Handle_Should_IncreaseWorkflowActionCount_When_AddValidWorkflowAction()
    {
        //_workflowActionFixture.WorkflowActions[0].Version = "6.0.0";
        _workflowActionFixture.CreateWorkflowActionCommand.Version = " ";

        // var version =  ConfigurationBinder.GetValue<string>(Version);
        //var configuration = new Mock<IConfiguration>();
        //configuration.SetupGet(x => x[It.IsAny<string>()]).Returns();

        await _handler.Handle(_workflowActionFixture.CreateWorkflowActionCommand, CancellationToken.None);

        var allActions = await _mockWorkflowActionRepository.Object.ListAllAsync();

        allActions.Count.ShouldBe(_workflowActionFixture.WorkflowActions.Count);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulWorkflowActionResponse_When_AddValidWorkflowAction()
    {
        var result = await _handler.Handle(_workflowActionFixture.CreateWorkflowActionCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateWorkflowActionResponse));

        result.WorkflowActionId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowActionFixture.CreateWorkflowActionCommand, CancellationToken.None);

        _mockWorkflowActionRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.WorkflowAction>()), Times.Once);
    }
}
