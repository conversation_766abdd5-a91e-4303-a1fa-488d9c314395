﻿using ContinuityPatrol.Application.Features.UserLogin.Commands.ClearSession;
using ContinuityPatrol.Application.Features.UserLogin.Commands.CreateSession;
using ContinuityPatrol.Application.Features.UserLogin.Commands.Update;
using ContinuityPatrol.Application.Features.UserLogin.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IUserLoginService
{
    Task<UserLoginDetailVm> GetUserInfoByUserId(string userId);
    Task<BaseResponse> UpdateAsync(UpdateUserLoginCommand updateUserLogin);
    Task<ClearSessionUserLoginResponse> ClearDatabaseSession(string userId);
    Task<CreateSessionUserLoginResponse> CreateDatabaseSession(string userId, string sessionId);
}
