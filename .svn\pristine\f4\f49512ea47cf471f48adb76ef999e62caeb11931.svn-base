﻿using AutoFixture;
using ContinuityPatrol.Application.Features.Site.Commands.Create;
using ContinuityPatrol.Application.Features.Site.Commands.Update;
using ContinuityPatrol.Application.Features.Site.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Site.Queries.GetByType;
using ContinuityPatrol.Application.Features.Site.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Site.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Domain.ViewModels.ReplicationModel;
using ContinuityPatrol.Domain.ViewModels.ServerModel;
using ContinuityPatrol.Domain.ViewModels.SiteLocationModel;
using ContinuityPatrol.Domain.ViewModels.SiteModel;
using ContinuityPatrol.Domain.ViewModels.SiteTypeModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller;

public class SiteControllerShould
{
    private readonly Mock<IPublisher> _mockPublisher = new();
    private readonly Mock<IMapper> _mockMapper = new();
    private readonly Mock<IDataProvider> _mockProvider = new();
    private readonly Mock<ILogger<SiteController>> _mockLogger = new();
    private SiteController _controller;

    public SiteControllerShould()
    {
            
        _controller = new SiteController(
            _mockPublisher.Object,
            _mockProvider.Object,
            _mockMapper.Object,
            _mockLogger.Object
        );
        _controller.ControllerContext.HttpContext = new DefaultHttpContext();
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
    }

    [Fact]
    public async Task List_ReturnsViewResult_WithSiteViewModel()
    {
        // Arrange
        var companyNames = new List<CompanyNameVm>();
        var siteTypes = new List<SiteTypeListVm>();
        var siteLocationList = new List<SiteLocationListVm>();
        var companies = new List<SelectListItem>();
        var siteLocations = new List<SiteLocationListVm>();

        _mockProvider.Setup(p => p.Company.GetCompanyNamesOnLogin()).ReturnsAsync(companyNames);
        _mockProvider.Setup(p => p.SiteType.GetSiteTypeList()).ReturnsAsync(siteTypes);
        _mockProvider.Setup(p => p.SiteLocation.GetSiteLocationList()).ReturnsAsync(siteLocationList);
        _mockMapper.Setup(m => m.Map<List<SelectListItem>>(companyNames)).Returns(companies);
        _mockMapper.Setup(m => m.Map<List<SiteLocationListVm>>(siteLocationList)).Returns(siteLocations);

        // Act
        var result = await _controller.List();

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        var model = Assert.IsType<SiteViewModel>(viewResult.Model);
        Assert.Equal(companies, model.Companies);
        Assert.Equal(siteLocations, model.SiteLocations);
        Assert.Equal(siteTypes, model.SiteTypes);

        // Verify event was published
        _mockPublisher.Verify(p => p.Publish(It.IsAny<SitePaginatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task SaveOrUpdate_CreatesSite_WhenIdIsEmpty()
    {
        // Arrange
        var siteViewModel = new AutoFixture.Fixture().Create<SiteViewModel>();
        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        dic.Add("id", "");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;
        var command = new CreateSiteCommand();
        var response = new BaseResponse { Message = "Created" };

        _mockMapper.Setup(m => m.Map<CreateSiteCommand>(siteViewModel)).Returns(command);
        _mockProvider.Setup(p => p.Site.CreateAsync(command)).ReturnsAsync(response);

        // Act
        var result = await _controller.SaveOrUpdate(siteViewModel);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.Contains("\"data\":", json);
    }

    [Fact]
    public async Task SaveOrUpdate_UpdatesSite_WhenIdIsNotEmpty()
    {
        // Arrange
        var siteViewModel = new AutoFixture.Fixture().Create<SiteViewModel>();
        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        dic.Add("id", "22");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;
        var command = new UpdateSiteCommand();
        var response = new BaseResponse { Message = "Updated" };

        _mockMapper.Setup(m => m.Map<UpdateSiteCommand>(siteViewModel)).Returns(command);
        _mockProvider.Setup(p => p.Site.UpdateAsync(command)).ReturnsAsync(response);

        // Act
        var result = await _controller.SaveOrUpdate(siteViewModel);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.Contains("\"data\":", json);
    }

    [Fact]
    public async Task SaveOrUpdate_HandlesValidationException()
    {
        // Arrange
        var siteViewModel = new SiteViewModel();
        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        dic.Add("id", "");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;

        var validationResult = new FluentValidation.Results.ValidationResult();
        validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Name", "Site name is required"));
        var validationException = new ValidationException(validationResult);

        _mockMapper.Setup(m => m.Map<CreateSiteCommand>(siteViewModel)).Throws(validationException);

        // Act
        var result = await _controller.SaveOrUpdate(siteViewModel);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":false", json);
        Assert.Contains("\"errors\":", json);
    }

    [Fact]
    public async Task SaveOrUpdate_HandlesGeneralException()
    {
        // Arrange
        var siteViewModel = new SiteViewModel();
        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        dic.Add("id", "");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;

        var exception = new Exception("An error occurred");
        _mockMapper.Setup(m => m.Map<CreateSiteCommand>(siteViewModel)).Throws(exception);

        // Act
        var result = await _controller.SaveOrUpdate(siteViewModel);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);
    }

    [Fact]
    public async Task Delete_ReturnsJsonResult_WhenSuccessful()
    {
        // Arrange
        var id = "1";
        var response = new BaseResponse { Message = "Deleted" };

        _mockProvider.Setup(p => p.Site.DeleteAsync(id)).ReturnsAsync(response);

        // Act
        var result = await _controller.Delete(id);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.Contains("\"data\":", json);
    }
        
    [Fact]
    public async Task Delete_HandlesException()
    {
        // Arrange
        var id = "1";
        var exception = new Exception("An error occurred");
        _mockProvider.Setup(p => p.Site.DeleteAsync(id)).ThrowsAsync(exception);

        // Act
        var result = await _controller.Delete(id);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);
    }

    [Fact]
    public async Task IsSiteNameExist_ReturnsJsonResult_WhenNameExists()
    {
        // Arrange
        var siteName = "Site1";
        var id = "1";
        _mockProvider.Setup(p => p.Site.IsSiteNameExist(siteName, id)).ReturnsAsync(true);

        // Act
        var result = await _controller.IsSiteNameExist(siteName, id);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.Contains("\"data\":true", json);
    }

    [Fact]
    public async Task IsSiteNameExist_ReturnsJsonResult_WhenNameDoesNotExist()
    {
        // Arrange
        var siteName = "Site1";
        var id = "1";
        _mockProvider.Setup(p => p.Site.IsSiteNameExist(siteName, id)).ReturnsAsync(false);

        // Act
        var result = await _controller.IsSiteNameExist(siteName, id);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.Contains("\"data\":false", json);
    }

    [Fact]
    public async Task IsSiteNameExist_HandlesException_ReturnsJsonException()
    {
        // Arrange
        var siteName = "Site1";
        var id = "1";
        var exception = new Exception("Database error");
        _mockProvider.Setup(p => p.Site.IsSiteNameExist(siteName, id)).ThrowsAsync(exception);

        // Act
        var result = await _controller.IsSiteNameExist(siteName, id);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);
    }

    [Fact]
    public async Task GetSiteById_ReturnsJsonResult()
    {
        // Arrange
        var siteId = "1";
        var siteDto = new SiteDetailVm();
        _mockProvider.Setup(p => p.Site.GetSiteById(siteId)).ReturnsAsync(siteDto);

        // Act
        var result = await _controller.GetSiteById(siteId);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.Contains("\"data\":", json);
    }

    [Fact]
    public async Task GetSiteById_HandlesException_ReturnsJsonException()
    {
        // Arrange
        var siteId = "1";
        var exception = new Exception("Database error");
        _mockProvider.Setup(p => p.Site.GetSiteById(siteId)).ThrowsAsync(exception);

        // Act
        var result = await _controller.GetSiteById(siteId);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);
    }

    [Fact]
    public async Task GetSiteByTypeAndCompanyId_ReturnsJsonResult()
    {
        // Arrange
        var companyId = "Company1";
        var siteType = "Type1";
        var siteList = new List<SiteBySiteTypeVm>();
        _mockProvider.Setup(p => p.Site.GetSiteByTypeAndCompanyId(companyId, siteType)).ReturnsAsync(siteList);

        // Act
        var result = await _controller.GetSiteByTypeAndCompanyId(companyId, siteType);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.Contains("\"data\":", json);
    }

    [Fact]
    public async Task GetSiteByTypeAndCompanyId_HandlesException_ReturnsJsonException()
    {
        // Arrange
        var companyId = "Company1";
        var siteType = "Type1";
        var exception = new Exception("Database error");
        _mockProvider.Setup(p => p.Site.GetSiteByTypeAndCompanyId(companyId, siteType)).ThrowsAsync(exception);

        // Act
        var result = await _controller.GetSiteByTypeAndCompanyId(companyId, siteType);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);
    }

    [Fact]
    public async Task GetPagination_ReturnsJsonResult()
    {
        // Arrange
        var query = new GetSitePaginatedListQuery();
        var siteList = new PaginatedResult<SiteListVm>();
        _mockProvider.Setup(p => p.Site.GetSitePaginatedList(query)).ReturnsAsync(siteList);

        // Act
        var result = await _controller.GetPagination(query);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.Contains("\"data\":", json);
    }

    [Fact]
    public async Task GetPagination_HandlesException_ReturnsJsonException()
    {
        // Arrange
        var query = new GetSitePaginatedListQuery();
        var exception = new Exception("Database error");
        _mockProvider.Setup(p => p.Site.GetSitePaginatedList(query)).ThrowsAsync(exception);

        // Act
        var result = await _controller.GetPagination(query);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);
    }

    [Fact]
    public async Task GetComponentDetails_ReturnsJsonResult()
    {
        // Arrange
        var businessServices = new List<BusinessServiceNameVm> ();
        var sites = new List<SiteNameVm> ();
        var operationalFunctions = new List<BusinessFunctionNameVm> ();
        var servers = new List<ServerNameVm>
        {
            new ServerNameVm { ServerType = "PRDBServer" },
            new ServerNameVm { ServerType = "DRDBServer" },
            new ServerNameVm { ServerType = "OtherServer" }
        };
        var databases = new List<DatabaseNameVm> ();
        var replications = new List<ReplicationNameVm> ();
        var infraObjects = new List<GetInfraObjectNameVm> ();

        _mockProvider.Setup(p => p.BusinessService.GetBusinessServiceNames()).ReturnsAsync(businessServices);
        _mockProvider.Setup(p => p.Site.GetSiteNames()).ReturnsAsync(sites);
        _mockProvider.Setup(p => p.BusinessFunction.GetBusinessFunctionNames()).ReturnsAsync(operationalFunctions);
        _mockProvider.Setup(p => p.Server.GetServerNames()).ReturnsAsync(servers);
        _mockProvider.Setup(p => p.Database.GetDatabaseNames()).ReturnsAsync(databases);
        _mockProvider.Setup(p => p.Replication.GetReplicationNames()).ReturnsAsync(replications);
        _mockProvider.Setup(p => p.InfraObject.GetInfraObjectNames()).ReturnsAsync(infraObjects);

        // Act
        var result = await _controller.GetComponentDetails();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":true", json);
        Assert.Contains("filteredPrServers", json);
        Assert.Contains("filteredDrServers", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetComponentDetails_HandlesException_ReturnsJsonException()
    {
        // Arrange
        var exception = new Exception("Database error");
        _mockProvider.Setup(p => p.BusinessService.GetBusinessServiceNames()).ThrowsAsync(exception);

        // Act
        var result = await _controller.GetComponentDetails();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);
    }
}