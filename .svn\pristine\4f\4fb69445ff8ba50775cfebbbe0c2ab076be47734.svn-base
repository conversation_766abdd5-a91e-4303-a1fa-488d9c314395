﻿let globalId = "", globaldeletedid = "", dataTable, selectedValues = [], IsSchedulevalue = "", IsScheduletype = "", strArrayData = '', isNameExits = "Drift/DriftManagement/IsNameExist"
let createPermission = $("#DriftCreate").data("create-permission").toLowerCase();
let deletePermission = $("#DriftDelete").data("delete-permission").toLowerCase();
if (createPermission == 'false') {
    $("#create_management").removeClass('#create_management').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
}
function validateProfileDropDown(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
async function validateProfileName(value, errorMsg, errorElement, url) {
    if (!value) {
        errorElement.text('Enter job name').addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        errorElement.text('Special characters not allowed').addClass('field-validation-error');
        return false;
    }
    var url = RootUrl + url;
    var data = {};
    data.id = null;
    data.name = value
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsSameNameExist(url, data)
    ];
    const failedValidations = validationResults.filter(result => result !== true);
    if (failedValidations.length > 0) {
        errorElement.text(failedValidations[0]).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
async function IsSameNameExist(url, inputValue) {
    return !inputValue.name.trim() || $("#drift_management_save").text() == "Update" ? true : (await GetAsync(url, inputValue, OnError)) ? " Name already exists" : true;
}
$(function () {

    dataTable = $('#drift_management').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }, infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/Drift/DriftManagement/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "properties" : sortIndex === 3 ? "nodename" :
                        sortIndex === 4 ? "scheduleTime" : sortIndex === 5 ? "lastExecutionTime" : sortIndex === 6 ? "status" : ""
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                        json.recordsTotal = json.data.totalPages;
                        json.recordsFiltered = json.data.totalCount;
                        if (json.data.data.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        update_properties = []
                        update_properties.push(json.data.data)
                        return json.data.data;
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [0, 1, 2, 3, 4, 5],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {

                            var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
                            return (page * meta.settings._iDisplayLength) + meta.row + 1;
                        }
                        return data;
                    }, "orderable": false,

                },
                {
                    "data": null, "name": "CheckboxAll", "autoWidth": true, "orderable": false,
                    "render": function (data, type, full, meta) {

                        return '<input type="checkbox" name="rowCheckbox" statename="' + data.name + '" class="' + data.name + ' form-check-input custom-cursor-default-hover" title=' + data.id + ' id="' + data.name + '">';

                    }
                },
                {
                    "data": "name", "name": "Job Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title="${data == null ? "NA" : data}"> ${data == null ? "NA" : data}</span></td>`
                    }
                },
                {
                    "data": "properties", "name": "Drift Profile", "autoWidth": true,
                    "render": function (data, type, row) {
                        let property = []
                        JSON.parse(data).forEach((x, i) => {
                            property.push(x.value)
                        })
                        let properties = property.join(", ")
                        return `<td><span title='${properties}'>${properties}</span></td>`;
                    }
                },
                {
                    "data": "nodeName", "name": "Node Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title='${data == null ? "NA" : data}'>${data == null ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "data": "scheduleTime", "name": "Scheduled Time", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title='${data == null ? "NA" : data}'>${data == null ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "data": "lastExecutionTime", "name": "LastExecuted Time", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title='${data || "NA"}'>${data || "NA"}</span></td>`;
                    }
                },
                {
                    "data": "status", "name": "Status", "autoWidth": true,
                    "render": function (data, type, row) {
                        let iconClass = '';
                        if (data == "Pending") {
                            iconClass = "cp-pending text-warning me-1";
                        } else if (data == "Running") {
                            iconClass = "text-primary cp-reload cp-animate me-1";
                        } else if (data == "Success") {
                            iconClass = "cp-success text-success me-1";
                        } else {
                            iconClass = "cp-error text-danger me-1";
                        }
                        return `<td><i class="${iconClass}"></i></td>
                              <td><span title="${data == null ? "NA" : data}"> ${data == null ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "data": "state", "name": "State", "autoWidth": true,
                    "render": function (data, type, row) {

                        var iconClass = '';
                        if (data == "Active") {
                            iconClass = "cp-active-inactive text-success me-1";
                        } else if (data === 'InActive') {
                            iconClass = "cp-active-inactive text-danger me-1";
                        } else if (data === null) {
                            iconClass = "cp-active-inactive text-danger me-1";
                        }


                        return `<td><i class="${iconClass}" id="icon" title="${data}" ></i></td>
                              <td><span id="jobmanagestate"> ${data == null ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "render": function (data, type, row) {
                        if (createPermission == "true" && deletePermission == "true") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="edit-button" name="overallupdate" data-job='${JSON.stringify(row)}'>
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="delete-button" delete_id="${row.id}" deletename="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                    <i class="cp-Delete"></i>
                                </span>
                                 </span>
                                                    <span  title="Reset" id="reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       >
                                                        <i class="cp-job-reset"></i>                                    
                                                 </span>        
                            </div>
                        </td>`;
                        }
                        if (createPermission == "false" && deletePermission == "true") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                            <i class="cp-edit"></i>
                                        </span>
                                <span role="button" title="Delete" class="delete-button" delete_id="${row.id}" deletename="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                    <i class="cp-Delete"></i>
                                </span>
                                 <span  title="Reset" id="reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       >
                                                        <i class="cp-job-reset"></i>                                    
                                                 </span>   
                            </div>
                        </td>`;
                        }
                        if (createPermission == "true" && deletePermission == "false") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="edit-button" name="overallupdate" data-job='${JSON.stringify(row)}'>
                                    <i class="cp-edit"></i>
                                </span>
                             </span>
                                <span role="button" title="Delete" class="icon-disabled">
                        <i class="cp-Delete"></i>

                    </span>
                     <span  title="Reset" id="reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       >
                                                        <i class="cp-job-reset"></i>                                    
                                                 </span> 
                            </div>
                        </td>`;
                        }
                        if (createPermission == "false" && deletePermission == "false") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                              <span role="button" title="Edit" class="icon-disabled">
                                <i class="cp-edit"></i>
                            </span>
                                <span role="button" title="Delete" class="icon-disabled">
                        <i class="cp-Delete"></i>
                    </span>
                      <span  title="Reset" id="reset" class="icon-disabled">
                                                        <i class="cp-job-reset"></i>
                                                 </span>
                            </div>
                        </td>`;
                        }
                    },
                    "orderable": false,
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        }
    )
})
$(document).on('click', '.delete-button', function () {
    globaldeletedid = $(this).attr("delete_id")
    $("#overall_deleted_id").text($(this).attr("deletename")).attr("title", $(this).attr("deletename"))
})
$("#overall_confirmDeleteButton").on("click", async function () {
    await $.ajax({
        type: "POST",
        url: RootUrl + "Drift/DriftManagement/Delete",
        dataType: "json",
        data: {
            id: globaldeletedid,
            __RequestVerificationToken: gettoken()
        },
        success: function (result) {
            let data = result.data
            if (result.success) {
                $('#DeleteModal').modal('hide');
                $('#drift_management_save').text("Save");
                notificationAlert("success", data.message)
                dataTable.ajax.reload()
            } else {
                errorNotification(result)
            }
        },
    })
})

$(async function () {

    await DriftJobService()
})

const DriftJobService = async () => {
    await $.ajax({
        type: "POST",
        url: RootUrl + 'ITAutomation/WorkflowExecution/CheckWindowsService',
        data: { type: 'monitor', __RequestVerificationToken: gettoken() },
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                if (result && result.success) {
                    let html = DriftJobMessage(result)
                    notificationAlert("success", html, 'execution')
                } else {
                    notificationAlert("warning", response.message);
                }

            } else {
                errorNotification(result)
            }
        }
    })
}

const DriftJobMessage = (result) => {
    let html = ''
    if (result?.activeNodes && Array.isArray(result?.activeNodes) && result?.activeNodes?.length) {
        for (let i = 0; i < result?.activeNodes?.length; i++) {
            html += `<div class='mb-1'><i class="cp-network fs-5 text-success" ></i> '${result.activeNodes[i]}'</div>`;
        }
    }
    if (result?.inActiveNodes && Array.isArray(result?.activeNodes) && result?.inActiveNodes?.length) {
        for (let i = 0; i < result?.inActiveNodes?.length; i++) {
            html += `<div class='mb-1'><i class="cp-network fs-5 text-danger" ></i> '${result.inActiveNodes[i]}'</div>`;
        }
    }
    return html;
}

getSolutionDetails()
function getSolutionDetails() {
    $.ajax({
        type: "GET",
        url: RootUrl + "Drift/DriftManagement/GetSolutionDetails",
        dataType: "json",
        success: function (result) {
            $("#selectSolutionType").empty().append("<option></option>")
            if (result.data.length != 0) {
                result.data.forEach((datavalue) => {
                    $("#selectSolutionType").append("<option value=" + datavalue.id + " >" + datavalue.componentName + "</option>")
                })
            }
        },
    })
}
function driftmanagementdebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}
$('#search-inp').on('keydown input', driftmanagementdebounce(function (e) {
    if (e.key === '=' || e.key === 'Enter') {
        e.preventDefault();
        return false;
    }
    const profilename = $("#Jobname");
    const profileCheckbox = $("#driftprofile");
    const statusCheckbox = $("#status");
    const inputValue = $('#search-inp').val();
    if (profileCheckbox.is(':checked')) {
        selectedValues.push(profileCheckbox.val() + inputValue);
    }
    if (statusCheckbox.is(':checked')) {
        selectedValues.push(statusCheckbox.val() + inputValue);
    }
    if (profilename.is(':checked')) {
        selectedValues.push(profilename.val() + inputValue);
    }
    var currentPage = dataTable.page.info().page + 1;
    if (!isNaN(currentPage)) {
        dataTable.ajax.reload(function (json) {
            if (e.target.value && json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        }, false)
    }
}, 500))

$('#drift_management').on('click', '#reset', function () {
    var jobData = $(this).data('job');
    
    jobData.__RequestVerificationToken = gettoken()
    $.ajax({
        url: "/Drift/DriftManagement/ResetDrift",
        type: 'POST',
        data: jobData,
        success: function (result) {
            if (result.success) {
                notificationAlert("success", result?.data.message);
                setTimeout(() => {
                    dataTable.ajax.reload();
                }, 2000)
            } else {
                // errorNotification(result)
            }
        }
    });
});


$('#Activebtn').on('click', function () {
    
    let checkFind = document.querySelectorAll('input[name="rowCheckbox"]')
    let datas = []
    checkFind.forEach((obj, idx) => {
        
        if (obj.checked && obj.id != "Active") {
            datas.push({
                "id": obj.title,
                "name": obj.getAttribute('statename')
            })
        }
    })
    if (datas.length) {
        
        $.ajax({
            url: "/Drift/DriftManagement/UpdateJobState",
            type: 'PUT',
            data: {
                "State":"Active",
                "UpdateDriftJobState": datas,
                __RequestVerificationToken: gettoken()
            },
            success: function (result) {
                if (result.success) {
                    var data = result?.data
                    $('input[name="rowCheckbox"]').prop("checked", false)
                    $('input[name="checkboxAll"]').prop("checked", false)
                    notificationAlert("success", data.message)
                    setTimeout(() => {
                        location.reload();
                    }, 2000)
                } else {
                    errorNotification(result)
                }
            },
        });
    } else {
        if ($('input[name="rowCheckbox"]').prop("checked")) {
            notificationAlert("warning", "Jobs state has already updated to 'Active' state ")
            setTimeout(() => {
                location.reload();
            }, 2000)
        }
    }
})
$('#Inactivebtn').on('click', function () {
    let checkFind = document.querySelectorAll('input[name="rowCheckbox"]')
    let datas = []
    checkFind.forEach((obj, idx) => {
        if (obj.checked && obj.id != "InActive") {
            datas.push({
                "id": obj.title,
                "name": obj.getAttribute('statename')
            })
        }
    })
    if (datas.length) {
        $.ajax({
            url: "/Drift/DriftManagement/UpdateJobState",
            type: 'PUT',
            data: {
                "State": "InActive",
                "UpdateDriftJobState": datas,
                __RequestVerificationToken: gettoken()
            },
            success: function (result) {
                if (result.success) {
                    var data = result?.data
                    $('input[name="rowCheckbox"]').prop("checked", false)
                    $('input[name="checkboxAll"]').prop("checked", false)
                    notificationAlert("success", data.message)
                    setTimeout(() => {
                        location.reload();
                    }, 2000)
                } else {
                    errorNotification(result)
                }
            },
        });
    } else {
        if ($('input[name="rowCheckbox"]').prop("checked")) {
            notificationAlert("warning", "Jobs state has already updated to 'InActive' state ")
            setTimeout(() => {
                location.reload();
            }, 2000)
        }
    }
})
$("#flexCheckDefault").on('change', function (e) {
    setTimeout(() => {
        if (e.target.checked) {
            $('input[name="rowCheckbox"]').prop("checked", true);

        } else {
            $('input[name="rowCheckbox"]').prop("checked", false)
        }
    }, 100)
})
$("#tblJobManagement").on('change', 'input[name="rowCheckbox"]', function (e) {
    $('input[name="checkboxAll"]').prop("checked", false)
})

$('#drift_management').on('click', '.edit-button', function () {
    var jobData = $(this).data("job");
    populateModalFields(jobData);
    Tab_selection(jobData);
    Tab_schedule_type(jobData);
    $('#drift_management_save').text("Update");
    ClearJobErrorElements();
    $('#Create_Modal').modal('show');
});
function populateModalFields(data) {
    globalId = data.id
    $("#profile_name").val(data.name)
    let Arr = [];
    JSON.parse(data?.properties).forEach((d) => {
        Arr.push(d.Id)
    })
    $("#selectSolutionType").val(data.solutionTypeId).trigger("change")
    setTimeout(() => {
        $('#Drift_profile').val(Arr).trigger('change')
    }, 1000)
    var scheduleTime = data.scheduleTime.split(" ")
    if (data.state == "Active") {
        $("#textStateActive").prop("checked", true);
    }
    else {
        $("#textStateInactive").prop("checked", true);
    }
    setTimeout(() => {
        if (scheduleTime.length == 3) {
            $("#txtMins").val(scheduleTime[1])
        }
        if (data.scheduleTime.includes("Every day") == true) {
            $("#defaultCheck-everyday").prop("checked", true)
            $("#everyHours").val(scheduleTime[4] + ":" + scheduleTime[6]).trigger("change")
        }
        if (data.scheduleTime.includes("MON-FRI") == true) {
            $("#defaultCheck-MON-FRI").prop("checked", true)
            $("#everyHours").val(scheduleTime[3] + ":" + scheduleTime[5]).trigger("change")
        }
        if (scheduleTime.length == 7) {
            $("#txtMinutes").val(scheduleTime[5])
            $("#txtHours").val(scheduleTime[1])
        }
        if ($("#defaultCheck-MON-FRI").prop("checked") != true) {
            if (data.scheduleTime.includes("MON") == true) {
                $("#defaultCheck-1").prop("checked", true)
            }
            if (data.scheduleTime.includes("TUE") == true) {
                $("#defaultCheck-2").prop("checked", true)
            }
            if (data.scheduleTime.includes("WED") == true) {
                $("#defaultCheck-3").prop("checked", true)
            }
            if (data.scheduleTime.includes("THU") == true) {
                $("#defaultCheck-4").prop("checked", true)
            }
            if (data.scheduleTime.includes("FRI") == true) {
                $("#defaultCheck-5").prop("checked", true)
            }
            if (data.scheduleTime.includes("SAT") == true) {
                $("#defaultCheck-6").prop("checked", true)
            }
            if (data.scheduleTime.includes("SUN") == true) {
                $("#defaultCheck-0").prop("checked", true)
            }
            $("#ddlHours").val(scheduleTime[2] + ":" + scheduleTime[4]).trigger("change")
        }
        if (scheduleTime.length >= 12) {
            let year = parseInt(scheduleTime[12])
            let month = parseInt(scheduleTime[8] == "JAN" ? "01" : scheduleTime[8] == "FEB" ? "02" : scheduleTime[8] == "MAR" ? "03" : scheduleTime[8] == "APR" ? "04" :
                scheduleTime[8] == "MAY" ? "05" : scheduleTime[8] == "JUN" ? "06" : scheduleTime[8] == "JUL" ? "07" : scheduleTime[8] == "AUG" ? "08" : scheduleTime[8] == "SEP" ? "09" :
                    scheduleTime[8] == "OCT" ? "10" : scheduleTime[8] == "NOV" ? "11" : scheduleTime[8] == "DEC" ? "12" : "")
            if (month <= 9 && month > 0) {
                month = "0" + month;
            }
            else if (month == 0) {
                month = "12";
                year = year - 1;
            }
            var newdate = year + "-" + month;
            $("#lblMonth").val(newdate).trigger("change")
            scheduleTime[5]?.split(",").forEach(function (i) {
                if (i) {
                    $("#inlineCheckbox" + i).prop("checked", true)
                } else {
                    $("#inlineCheckbox" + i).prop("checked", false)
                }
            })
            $("#MonthlyHours").val(scheduleTime[0] + ":" + scheduleTime[2]).trigger("change")
        }
    }, 500)
}
$(".profile_cancel").on("click", function () {
    jobOnce()
    ClearCroneElements()
    $("#profile_name,#Drift_profile").val("")
    $("#profile_name_error,#Drift_profile_error").text('').removeClass('field-validation-error');
    $("#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab").removeClass("active");
    $("#nav-Hourly,#nav-Daily,#nav-Weekly,#nav-Monthly").removeClass("show active");
    $("#nav-Minutes").addClass("show active");
    $("#nav-Minutes-tab").addClass("active");
})
$("#create_management").on("click", async function () {
    $("#textStateActive").prop("checked", true);
    jobOnce()
    $('#SaveFunction').text("Save");
    clearJobFields();
    profile_data()
})
async function profile_data() {
    $("#Drift_profile").empty()
    await $.ajax({
        type: "GET",
        url: RootUrl + "Drift/DriftManagement/GetDriftProfileList",
        dataType: "json",
        success: function (result) {
            let data = result.data
            if (result.success) {
                data.forEach((x, i) => {
                    $("#Drift_profile").append(`<option value=""></option>`)
                    $("#Drift_profile").append(`<option value="${x.id}">${x.name}</option>`)
                })
            } else {
                errorNotification(result)
            }
        },
    })
}
profile_data()
const clearJobFields = () => {
    $('#drift_management_save').text("Save");
    $("#profile_name,#textNodeId,#textNodeName,#textCronExpression,#textStatus,#textIsSchedule,#textScheduleType").val("")
    $("#Drift_profile,#selectSolutionType").val("").trigger("change")
    $("#textstateinactive").prop("checked", false)
    ClearJobErrorElements();
    ClearCroneElements();
}
function ClearJobErrorElements() {
    $("#profile_name_error,#SolutionType-error,#Drift_profile_error,#CronMin-error,#CronHourly-error,#CroneveryHour-error,#ExecutionPolicy-error, #CronddlMin-error,#CronddlHour-error,#CronDay-error,#CronExpression-error,#Crondaysevery-error,#InfraObjectName-error,#MonthlyHours-error,#CronMon-error,#CronMonthly-error,#CronExpression-error").text('').removeClass('field-validation-error');
}
$('#profile_name').on('input', driftmanagementdebounce(async function () {
    let value = await sanitizeInput($('#profile_name').val());
    $("#profile_name").val(value);
    await validateProfileName(value, "Enter job name", $("#profile_name_error"), isNameExits);
}, 400));
$('#Drift_profile').on('change', async function () {
    await validateProfileDropDown($(this).val(), "Select drift profile", $("#Drift_profile_error"));
    let selectName = $(this).find('option:selected');
    Arraydata = []
    selectName.each(function () {
        let option = $(this);
        let val = option.text();
        let id = option.val()
        let obj = {
            Id: id,
            value: val
        };
        Arraydata.push(obj)
    });
    strArrayData = JSON.stringify(Arraydata)
});
$("#selectSolutionType").on("change", async function () {
    await validateProfileDropDown($(this).val(), "Select solution type", $("#SolutionType-error"));
})
$('#txtMins').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
        event.preventDefault();
        $('#txtMins').val('');
    }
    if ($(this).val() == 0 || $(this).val() > 59) {
        $('#txtMins').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateMinJobNumber($(this).val(), "Enter minutes", $('#CronMin-error'));
});
$('#txtHours').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key) || $(this).val() > 23) {
        event.preventDefault();
        $('#txtHours').val('');
    }
    if ($(this).val() == 0) {
        $('#txtHours').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateHourJobNumber($(this).val(), "Enter hours", $('#CronHourly-error'));
});

$('#txtMinutes').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key) || $(this).val() > 59) {
        event.preventDefault();
        $('#txtMinutes').val('');
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateMiniteJobNumber($(this).val(), "Enter minutes", $('#CronHourMin-error'));
});
$("#txtMinutes,#txtHours").on("input", function () {
    if ($("#txtMinutes").val() == "00" && $("#txtHours").val() == "00" || $("#txtMinutes").val() == "0" && $("#txtHours").val() == "0") {
        $("#txtMinutes").val("")
        setTimeout(() => {
            $('#CronHourMin-error').text("Enter the proper hours and minites")
        }, 200)
    }
})
$('#everyHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
        event.preventDefault();
        $('#everyHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#CroneveryHour-error'));
});
$('#everyMinutes').on('click', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69) {
        event.preventDefault();
        $('#everyMinutes').val('');
    }
    validateMinJobNumber($(this).val(), "Select minutes", $('#CroneveryMin-error'));
});
$('#ddlHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
        $('#ddlHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#CronddlHour-error'));
});

$('#ddlMinutes').on('click', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69) {
        event.preventDefault();
        $('#ddlMinutes').val('');
    }
    validateMinJobNumber($(this).val(), "Select hours", $('#CronddlMin-error'));
});
$('#MonthlyHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
        event.preventDefault();
        $('#MonthlyHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#MonthlyHours-error'));
});
function srvTime() {
    try {
        //FF, Opera, Safari, Chrome
        xmlHttp = new XMLHttpRequest();
    }
    catch (err1) {
        //IE
        try {
            xmlHttp = new ActiveXObject('Msxml2.XMLHTTP');
        }
        catch (err2) {
            try {
                xmlHttp = new ActiveXObject('Microsoft.XMLHTTP');
            }
            catch (eerr3) {
                //AJAX not supported, use CPU time.
                alert("AJAX not supported");
            }
        }
    }
    xmlHttp.open('HEAD', window.location.href.toString(), false);
    xmlHttp.setRequestHeader("Content-Type", "text/html");
    xmlHttp.send('');
    return xmlHttp.getResponseHeader("Date");
}
$('.datetimeCron').on('change', function () {
    validateDayNumber($(this).val(), "Select schedule time", $('#CronExpression-error'));
    let selectdate = new Date($(this).val()), currentdate = new Date(srvTime())
    if (selectdate > currentdate) {
        $('#CronExpression-error').text('').removeClass('field-validation-error');
        return true;
    } else if (selectdate < currentdate) {
        $('#CronExpression-error').text("Select schedule date and time should be greater than current date and time").addClass('field-validation-error');
        return false;
    }
});
$('#lblMonth').on("change", function () {
    $('input[name="Monthyday"]').prop("checked", false)
    validateDayNumber($(this).val(), "Select month and year", $('#CronMonthly-error'));
    let selectedDate = new Date($(this).val()), currentDate = new Date()
    const getDays = (year, month) => {
        return new Date(year, month, 0).getDate();
    };
    const daysInmonth = getDays(selectedDate.getFullYear(), selectedDate.getMonth() + 1)
    for (let i = 0; i < daysInmonth; i++) {
        let data = ""
        data = i + 1
        $('input[name="Monthyday"]').each(function () {
            let checkboxValue = parseInt($(this).val());
            if (checkboxValue > data) {
                $(this).css("display", "none")
            } else {
                $(this).css("display", "block")
            }
        })
        $(".checklabel").each(function () {
            let checkboxValue = parseInt($(this).text());
            if (checkboxValue > data) {
                $(this).css("display", "none")
            } else {
                $(this).css("display", "block")
            }
        })
    }
    if ($(this).val() == "") {
        $('input[name="Monthyday"]').prop('disabled', true);
        $('input[name="Monthyday"]').prop('checked', false);
    } else {
        $('input[name="Monthyday"]').each(function () {
            let checkboxValue = parseInt($(this).val());
            if ((selectedDate.getMonth() === currentDate.getMonth() && selectedDate.getFullYear() === currentDate.getFullYear() && checkboxValue < currentDate.getDate()) ||
                (selectedDate.getMonth() < currentDate.getMonth() && selectedDate.getFullYear() <= currentDate.getFullYear())) {
                $(this).prop('disabled', true);
            } else {
                $(this).prop('disabled', false);
            }
        })
    }
});
$('input[name=weekDays]').on('click', function () {
    let checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    let Dayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    validateDayNumber(Dayvalue, "Select day", $('#CronDay-error'));
});
$('input[name=Monthyday]').on('click', function () {
    let checkedCheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    let MonthDayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    validateDayNumber(MonthDayvalue, "Select date", $('#CronMon-error'));
});
$('.nav-link').on("click", function () {
    ClearCroneElements();
});
$('input[name = "switchPlan"]').on('click', function () {
    ClearCroneElements();
});
$('input[name=daysevery]').on('click', function () {
    ValidateCronRadioButton($('#Crondaysevery-error'));
});
$('#textStateActive').on('click', function () {
    const errorElement = $('#state-error');
    ValidateRadioButton(errorElement);
});
$('#textStateInactive').on('click', function () {
    const errorElement = $('#state-error');
    ValidateRadioButton(errorElement);
});

function ValidateRadioButton(errorElement) {

    if ($('input[name=state]:checked').length > 0) {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
    else {
        errorElement.text("Select state").addClass('field-validation-error');;
        return false;
    }
}
function jobOnce() {
    Drready_SM2 = document.getElementById("switchMonthly");
    Drready_SM2.checked = true;
    let elementToHide11 = document.getElementById("monthgroup");
    elementToHide11.style.display = "block";
    let elementToHide22 = document.getElementById("yeargroup");
    elementToHide22.style.display = "none";
}
$(".nav-link,#nav-Minutes-tab,#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab,#Next_profile_data").on("click", function () {
    $("#CronMin-error,#CronHourly-error,#CronHourMin-error,#Crondaysevery-error,#CroneveryHour-error,#CronDay-error,#CronddlHour-error, #CronMonthly-error,#CronMon-error,#MonthlyHours-error,#CronExpression-error").text('').removeClass('field-validation-error');
})
$("#nav-Monthly-tab").on("click", function () {
    if ($("#drift_management_save").text() == "Save") {
        $('input[name=Monthyday]').attr('disabled', 'disabled');
    }
})
function ClearCroneElements() {
    $("#txtMins,#txtHours,#txtMinutes,#ddlHours,#everyHours,#lblMonth,#MonthlyHours,#datetimeCron").val('');
    $("input[name=weekDays],input[name=daysevery],input[name=Monthyday],input[name=Monthyday]").prop("checked", false);
}
$("#drift_management_save").on("click", async function () {
    let form = $("#Create_Modal")
    GetIsSchedule();
    Get_ScheduleTypes();
    errorElement = $('#state-error');
    var isStateActive = ValidateRadioButton(errorElement);

    const profile_name = await validateProfileName($('#profile_name').val(), "Enter job name", $("#profile_name_error"), isNameExits);
    const drift_profile = validateProfileDropDown($('#Drift_profile').val(), "Select drift profile", $("#Drift_profile_error"));
    const drift_solutiontype = validateProfileDropDown($('#selectSolutionType').val(), "Select solution type", $("#SolutionType-error"));
    let isScheduler = CronValidation();
    var { CronExpression, listcron } = JobCronExpression();
    if (profile_name && drift_profile && drift_solutiontype && isScheduler && isStateActive) {
        form.trigger("submit")
        let data = {
            "Name": $('#profile_name').val(),
            "Properties": strArrayData,
            "SolutionTypeId": $('#selectSolutionType option:selected').val(),
            "SolutionTypeName": $('#selectSolutionType option:selected').text(),
            "NodeId": "",
            "State": $("input[name='state']:checked").val(),
            "NodeName": "",
            "Status": "Pending",
            "CronExpression": CronExpression,
            "IsSchedule": IsSchedulevalue,
            "ScheduleType": IsScheduletype,
            "ScheduleTime": listcron,
            "ExceptionMessage": "",
            __RequestVerificationToken: gettoken()
        }
        $('#drift_management_save').text() === "Update" ? data["id"] = globalId : null
        await $.ajax({
            type: "POST",
            url: RootUrl + "Drift/DriftManagement/CreateOrUpdate",
            dataType: "json",
            data: data,
            success: function (result) {
                let data = result.data
                if (result.success) {
                    $('#Create_Modal').modal('hide');
                    notificationAlert("success", data.message)
                    dataTable.ajax.reload()
                } else {
                    errorNotification(result)
                }
            },
        })
        clearJobFields()
    }
})
function JobCronExpression() {
    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    var Monthlycheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    var txtmonthday = Array.from(Monthlycheckboxes).map(checkbox => checkbox.value);
    var monthlymonth = $('#lblMonth').val();
    var CronExpression = "";
    var Minutes = $('#txtMins').val();
    var txtHours = $('#txtHours').val() == "00" ? "0" : $('#txtHours').val() == "01" ? "1" : $('#txtHours').val() == "03" ? "3" : $('#txtHours').val() == "04" ? "4" : $('#txtHours').val() == "05" ? "5" : $('#txtHours').val() == "06" ? "6" : $('#txtHours').val() == "07" ? "7" : $('#txtHours').val() == "08" ? "8" : $('#txtHours').val() == "09" ? "9" : $('#txtHours').val()
    var txtHourMinutes = $('#txtMinutes').val() == "00" ? "0" : $('#txtMinutes').val() == "01" ? "1" : $('#txtMinutes').val() == "03" ? "3" : $('#txtMinutes').val() == "04" ? "4" : $('#txtMinutes').val() == "05" ? "5" : $('#txtMinutes').val() == "06" ? "6" : $('#txtMinutes').val() == "07" ? "7" : $('#txtMinutes').val() == "08" ? "8" : $('#txtMinutes').val() == "09" ? "9" : $('#txtMinutes').val()
    var day = $('#ddlHours').val().split(":")
    var ddlHours = day[0]
    var ddlMinutes = day[1] //$('#ddlMinutes').val();
    var Daily = $('#everyHours').val().split(":")
    var everyHours = Daily[0]
    var everyMinutes = Daily[1]
    var month = $('#MonthlyHours').val().split(":")
    var MonthlyHours = month[0]
    var MonthlyMins = month[1] //$('#MonthlyMins').val()
    var weekDay = $('#defaultCheck-MON-FRI').val();
    var datetime = $('#datetimeCron').val()

    let schedule_model = document.querySelector('input[name="daysevery"]:checked');

    var listcron = '';
    if (datetime != '') {
        var { CronExpression, listCron } = DateTimeCronBuilder(datetime)
        CronExpression = CronExpression
        listcron = listCron;
    }
    else {
        if (Minutes != '') {
            CronExpression = "0" + " 0/" + Minutes + " * * * ?";
            listcron = "Every " + Minutes + " minutes"
        }
        else if (txtHours != '') {
            CronExpression = "0 " + " 0/" + txtHourMinutes + " 0/" + txtHours + " * * ?"
            listcron = "Every " + txtHours + " hours, " + " every " + txtHourMinutes + " minutes";
        }
        else if (txtDay != '') {
            CronExpression = "0 " + ddlMinutes + " " + ddlHours + " ? * " + txtDay + " *"
            listcron = txtDay + " at " + ddlHours + " hours " + ddlMinutes + " minutes";
        }
        else if (txtmonthday != '') {
            if (monthlymonth != '') {
                monthlymonth = monthlymonth.split('-');
                var txtmonth = monthlymonth[1] == "01" ? "JAN" : monthlymonth[1] == "02" ? "FEB" : monthlymonth[1] == "03" ? "MAR" : monthlymonth[1] == "04" ? "APR" :
                    monthlymonth[1] == "05" ? "MAY" : monthlymonth[1] == "06" ? "JUN" : monthlymonth[1] == "07" ? "JUL" : monthlymonth[1] == "08" ? "AUG" : monthlymonth[1] == "09" ? "SEP" :
                        monthlymonth[1] == "10" ? "OCT" : monthlymonth[1] == "11" ? "NOV" : monthlymonth[1] == "12" ? "DEC" : ""

                var txtyear = monthlymonth[0];
            }
            CronExpression = "0 " + MonthlyMins + " 0/" + MonthlyHours + " " + txtmonthday + " " + txtmonth + " ? " + txtyear
            listcron = MonthlyHours + " hours " + MonthlyMins + " minutes for " + txtmonthday + " day(s) on " + txtmonth + " in the year " + txtyear;
        }
        else if (schedule_model != null) {
            if (schedule_model.value == "everyday") {
                CronExpression = "0 " + everyMinutes + " " + everyHours + " * * ?"
                listcron = " Every day at " + everyHours + " hours " + everyMinutes + " minutes ";
            }
            else if (schedule_model.value == "MON-FRI") {
                CronExpression = "0 " + everyMinutes + " " + everyHours + " ? * " + weekDay + " * ";
                listcron = " MON-FRI at " + everyHours + " hours " + everyMinutes + " minutes ";
            }
        }
    }
    return { CronExpression, listcron };
}
function DateTimeCronBuilder(datetime) {
    var splitDate = datetime.split("T");
    var cronDate = splitDate[0].split("-");
    var cronTime = splitDate[1].split(":");

    var cronYear = cronDate[0];
    var cronMonth = cronDate[1]
    var cronDay = cronDate[2];
    var cronHours = cronTime[0];
    var cronMin = cronTime[1];
    var cronmonthexp = cronDate[1] == "01" ? "JAN" : cronDate[1] == "02" ? "FEB" : cronDate[1] == "03" ? "MAR" : cronDate[1] == "04" ? "APR" :
        cronDate[1] == "05" ? "MAY" : cronDate[1] == "06" ? "JUN" : cronDate[1] == "07" ? "JUL" : cronDate[1] == "08" ? "AUG" : cronDate[1] == "09" ? "SEP" :
            cronDate[1] == "10" ? "OCT" : cronDate[1] == "11" ? "NOV" : cronDate[1] == "12" ? "DEC" : ""
    CronExpression = "0 " + cronMin + " " + cronHours + " " + cronDay + " " + cronMonth + " ? " + cronYear;
    // monthname
    listCron = "At " + cronHours + ":" + cronMin + ", on day  " + cronDay + " of the month, only in " + cronmonthexp + ", only in " + cronYear;
    //At 12: 51 PM, on day 14 of the month, only in February, only in 2024
    return { CronExpression, listCron }
}
function DateTimeCronConventor(cron) {
    var splitcron = cron?.split(" ");
    var cronYear = splitcron[6];
    var cronMonth = splitcron[4];
    var cronDay = splitcron[3];
    var cronHours = splitcron[2];
    var cronMin = splitcron[1];
    var cronDate = cronYear + "-" + cronMonth + "-" + cronDay + "T" + cronHours + ":" + cronMin
    //At 06:05:00am, on the 8th day, in May, in 2023
    return cronDate
}
function CronValidation() {
    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    var Monthlycheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    var txtmonthday = Array.from(Monthlycheckboxes).map(checkbox => checkbox.value);
    var monthlymonth = $('#lblMonth').val();
    var Minutes = $('#txtMins').val();
    var txtHours = $('#txtHours').val();
    var txtHourMinutes = $('#txtMinutes').val();
    var everyHours = $('#everyHours').val();
    var datetime = $('#datetimeCron').val();
    var MonthlyHours = $('#MonthlyHours').val();
    var isScheduler = '';

    if (document.getElementById('switchMonthly').checked == true) {

        $('#datetimeCron').val('');
        var Scheduler_types = $('.nav-tabs .active').text().trim();
        switch (Scheduler_types) {
            case "Minutes":
                isScheduler = validateMinJobNumber(Minutes, "Enter minutes", $('#CronMin-error'));
                break;
            case "Hourly":
                isScheduler = validateHourJobNumber(txtHours, "Enter hours", $('#CronHourly-error'));
                isScheduler = validateMiniteJobNumber(txtHourMinutes, "Enter minutes", $('#CronHourMin-error'));
                break;
            case "Daily":
                isSchedulerHour = validateHourJobNumber(everyHours, "Select start time", $('#CroneveryHour-error'));
                isSchedulerDay = ValidateCronRadioButton($('#Crondaysevery-error'));
                if (isSchedulerHour && isSchedulerDay) {
                    isScheduler = true;
                }
                break;
            case "Weekly":
                isSchedulerHour = validateHourJobNumber($('#ddlHours').val(), "Select start time", $('#CronddlHour-error'));
                isSchedulerDay = validateDayNumber(txtDay, "Select day", $('#CronDay-error'));
                if (isSchedulerHour && isSchedulerDay) {
                    isScheduler = true;
                }
                break;
            case "Monthly":
                isSchedulerHour = validateHourJobNumber(MonthlyHours, "Select start time", $('#MonthlyHours-error'));
                isSchedulerDay = validateDayNumber(txtmonthday, "Select date", $('#CronMon-error'));
                isSchedulerMonth = validateDayNumber(monthlymonth, "Select month and year", $('#CronMonthly-error'));
                if (isSchedulerHour && isSchedulerDay && isSchedulerMonth) {
                    isScheduler = true;
                }
                break;
        }
    }
    else {
        isScheduler = validateDayNumber(datetime, "Select schedule time", $('#CronExpression-error')) && validateprevNumber(datetime, "", $('#CronExpression-error'));
    }
    return isScheduler;
}
function validateJobDropDown(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
function ValidateRadioButton(errorElement) {
    if ($('input[name=state]:checked').length > 0) {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
    else {
        errorElement.text("Select state").addClass('field-validation-error');;
        return false;
    }
}
function ValidateCronRadioButton(errorElement) {
    if ($('input[name=daysevery]:checked').length > 0) {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
    else {
        errorElement.text("Select day type").addClass('field-validation-error');;
        return false;
    }
}
function GetIsSchedule() {
    var schedule_type = document.querySelector('input[name = "switchPlan"]:checked');
    if (schedule_type.value === "Once") {
        IsSchedulevalue = 1
        $('#textIsSchedule').val(1);
    } else {
        IsSchedulevalue = 2
        IsSchedule = $('#textIsSchedule').val(2);
    }
}
function Get_ScheduleTypes() {
    var Scheduler_types = $('.nav-tabs .active').text().trim();
    switch (Scheduler_types) {
        case "Minutes":
            IsScheduletype = 1
            $('#textScheduleType').val(1);
            break;
        case "Hourly":
            IsScheduletype = 2
            $('#textScheduleType').val(2);
            break;
        case "Daily":
            IsScheduletype = 3
            $('#textScheduleType').val(3);
            break;
        case "Weekly":
            IsScheduletype = 4
            $('#textScheduleType').val(4);
            break;
        case "Monthly":
            IsScheduletype = 5
            $('#textScheduleType').val(5);
            break;
    }
}
function Tab_selection(jobData) {
    if (jobData.isSchedule == 2) {
        Drready_SM1 = document.getElementById("switchMonthly");
        Drready_SM1.checked = true;
        var elementToHide1 = document.getElementById("monthgroup");
        elementToHide1.style.display = "block";
        var elementToHide22 = document.getElementById("yeargroup");
        elementToHide22.style.display = "none";
    } else {
        Drready_SM2 = document.getElementById("switchYearly");
        Drready_SM2.checked = true;
        var elementToHide11 = document.getElementById("monthgroup");
        elementToHide11.style.display = "none";
        var elementToHide22 = document.getElementById("yeargroup");
        elementToHide22.style.display = "block";
    }
}
function Tab_schedule_type(jobData) {
    let types = jobData.scheduleType, clickedLink = "", linkId = "";
    if (jobData.isSchedule == 1) {
        var datetime = DateTimeCronConventor(jobData.cronExpression)
        $('#datetimeCron').val(datetime)
    }
    else {
        switch (types) {
            case 1:
                linkId = "nav-Minutes-tab";
                setTimeout(() => {
                    clickedLink = document.getElementById(linkId);
                    clickedLink.click();
                    const { minutes } = parseMinCronExpression(jobData.cronExpression);
                    document.getElementById("txtMins").value = minutes;
                }, 150)
                break;
            case 2:
                linkId = "nav-Hourly-tab";
                setTimeout(() => {
                    clickedLink = document.getElementById(linkId);
                    clickedLink.click();
                    const { hours, minutes } = parseHoursCronExpression(jobData.cronExpression);
                    document.getElementById("txtHours").value = hours;
                    document.getElementById("txtMinutes").value = minutes;
                }, 150)
                break;
            case 3:
                linkId = "nav-Daily-tab";
                setTimeout(() => {
                    clickedLink = document.getElementById(linkId);
                    clickedLink.click();
                    const { hours, day } = parseDailyCronExpression(jobData.cronExpression);
                    document.getElementById("everyHours").value = hours;
                    if (day == "?") {
                        $("#defaultCheck-everyday").prop("checked", true);
                    }
                    else {
                        $("#defaultCheck-MON-FRI").prop("checked", true);
                    }
                }, 150)
                break;
            case 4:
                linkId = "nav-Weekly-tab";
                setTimeout(() => {
                    clickedLink = document.getElementById(linkId);
                    clickedLink.click();
                    const { hours, day } = parseWeeklyCronExpression(jobData.cronExpression);
                    document.getElementById("ddlHours").value = hours;
                    dayconventor(day);
                }, 150)
                break;
            case 5:
                linkId = "nav-Monthly-tab";
                setTimeout(() => {
                    clickedLink = document.getElementById(linkId);
                    clickedLink.click();
                    const { hours, month, days } = parseCronMonthExpression(jobData.cronExpression);
                    document.getElementById("MonthlyHours").value = hours;
                    document.getElementById("lblMonth").value = month;
                    monthDayconventor(days);
                }, 150)
                break;
        }
    }
}
function dayconventor(day) {
    const daysMap = {
        MON: 1,
        TUE: 2,
        WED: 3,
        THU: 4,
        FRI: 5,
        SAT: 6,
        SUN: 0
    };
    const days = day.split(',');
    days.forEach(day => {
        const checkboxId = `#defaultCheck-${daysMap[day]}`;
        $(checkboxId).prop("checked", true);
    });
}
function parseMinCronExpression(expression) {
    const parts = expression.split(' '), minutes = parseInt(parts[1].substring(2)), hours = parseInt(parts[2].substring(2)), day = parseInt(parts[3].substring(2))
    return { hours, minutes, day };
}
function parseHoursCronExpression(expression) {
    const parts = expression.split(' '), minutes = parseInt(parts[1]), hours = parseInt(parts[2].substring(2)), day = parts[5]
    return { hours, minutes, day };
}
function parseDailyCronExpression(expression) {
    const parts = expression.split(' '), minutes = parseInt(parts[1]), hours = parseInt(parts[2].substring(2)), day = parts[5]
    return { hours, minutes, day };
}
function parseWeeklyCronExpression(expression) {
    const parts = expression.split(' '), minutes = parseInt(parts[1]), hours = parseInt(parts[2].substring(2)), day = parts[5]
    return { hours, minutes, day };
}
function parseCronMonthExpression(expression) {
    const parts = expression.split(' '), minutes = parseInt(parts[1]), hours = parseInt(parts[2].substring(2)), month = parts[6] + "-" + parts[4], days = parts[3]
    return { minutes, hours, month, days };
};
function monthDayconventor(days) {
    const day = days.split(" ")
    let checkboxes = document.querySelectorAll('input[name="Monthyday"]');
    checkboxes.forEach(function (checkbox) {
        if (day.includes(checkbox.value)) {
            checkbox.checked = true;
        }
    });
};
function validateDayNumber(value, errorMsg, errorElement) {
    if (!value || value.length == 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
function validateprevNumber(value, errorMsg, errorElement) {
    let selectdate = new Date(value), currentdate = new Date()
    if (selectdate > currentdate) {
        $('#CronExpression-error').text('').removeClass('field-validation-error');
        return true;
    } else if (selectdate < currentdate) {
        $('#CronExpression-error').text("Select schedule date and time should be greater than current date and time").addClass('field-validation-error');
        return false;
    }
}
function validateMiniteJobNumber(value, errorMsg, errorElement) {
    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error')
        return false;
    }
    else if ((Number(value) < 0) || (Number(value) >= 60)) {
        errorElement.text("Enter value between 0 to 59").addClass('field-validation-error')
        return false;
    }
    else {
        errorElement.text('').removeClass('field-validation-error')
        return true;
    }
}
function validateMinJobNumber(value, errorMsg, errorElement) {
    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error')
        return false;
    }
    else if ((Number(value) < 0) || (Number(value) > 59)) {
        errorElement.text("Enter value between 1 to 59").addClass('field-validation-error')
        return false;
    } else if (Number(value) == "0") {
        errorElement.text("Enter the value more than 0").addClass('field-validation-error')
        return false;
    }
    else {
        errorElement.text('').removeClass('field-validation-error')
        return true;
    }
}
function validateHourJobNumber(value, errorMsg, errorElement) {
    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error')
        return false;
    }
    else if ((Number(value) == 0)) {
        errorElement.text("Enter value greater than zero").addClass('field-validation-error')
        return false;
    }
    else if ((Number(value) < 1) || (Number(value) >= 24)) {
        errorElement.text("Enter value between 1 to 23").addClass('field-validation-error')
        return false;
    }
    else {
        errorElement.text('').removeClass('field-validation-error')
        return true;
    }
}
let monthInput = document.getElementById("lblMonth");
let today = new Date();
let currentYear = today.getFullYear();
let currentMonth = today.getMonth() + 1;
let minMonth = currentYear + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
let maxMonth = (currentYear + 77) + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
monthInput.setAttribute("min", minMonth);
monthInput.setAttribute("max", maxMonth)

const now = new Date();
const year = now.getFullYear();
const month = String(now.getMonth() + 1).padStart(2, '0');
const day = String(now.getDate()).padStart(2, '0');
const hours = String(now.getHours()).padStart(2, '0');
const minutes = String(now.getMinutes()).padStart(2, '0');
const minformattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;
const maxformattedDate = `${year + 77}-${month}-${day}T${hours}:${minutes}`;
const datetimeInput = document.getElementById('datetimeCron');
datetimeInput.min = minformattedDate;
datetimeInput.max = maxformattedDate;

