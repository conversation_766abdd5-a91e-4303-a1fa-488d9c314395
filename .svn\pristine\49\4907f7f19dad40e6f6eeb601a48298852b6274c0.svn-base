﻿let mId = sessionStorage.getItem("monitorId")
let monitortype = 'Oracle';
let infraObjectId = sessionStorage.getItem("infraobjectId");

setTimeout(() => { oracledataguardmonitorstatus(mId, monitortype) }, 250)
//setTimeout(() => { odgServer(infraObjectId) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
/*$('#mssqlserver').hide();*/
//async function odgServer(id) {
    
//    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
//    let data = {}
//    data.infraObjectId = id;
//    let mssqlServerData = await getAysncWithHandler(url, data);
   
//    if (mssqlServerData != null && mssqlServerData?.length > 0) {
//        mssqlServerData?.forEach(data => {
//            let value = data?.isServiceUpdate
//            let parsed = []
//            if (value && value !== 'NA') parsed = JSON?.parse(value)
//            if (Array.isArray(parsed)) {
//                parsed?.forEach(s => {
//                    if (s?.Services?.length) {
//                        $('#mssqlserver').show();
//                        bindODGServer(mssqlServerData)
//                    }
//                })
//            }
//        })
       
//    } else {
//        $('#mssqlserver').hide();
//    }

//}
$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})
//function bindODGServer(mssqlServerData) {

//  let prType = { IpAddress: '--', Services: [] };
//    let drType = { IpAddress: '--', Services: [] };

//    // Loop through each item to find PR and DR entries
//    mssqlServerData?.forEach(item => {
//        let parsedServices = []
//        try {
//            const value = item?.isServiceUpdate
//            if (value && value !== 'NA') {
//                parsedServices = JSON?.parse(item?.isServiceUpdate)
//            }
            
//        } catch (e) {
//            console.error("Invalid JSON in isServiceUpdate:", item?.isServiceUpdate);
//        }
       
//            parsedServices?.forEach(serviceGroup => {
//                if (serviceGroup.Type === 'PR') {
                  
//                    prType.IpAddress = serviceGroup?.IpAddress || prType.IpAddress;
//                    prType.Services = [...prType.Services, ...(serviceGroup?.Services || [])];

//                } else if (serviceGroup.Type === 'DR') {
                  
//                    drType.IpAddress = serviceGroup?.IpAddress || drType.IpAddress;
//                    drType.Services = [...drType.Services, ...(serviceGroup?.Services || [])];

//                }
//            });       
//    });
    
//    // Set header IPs
//    $('#prIp').text('Primary (' + prType.IpAddress + ')');
//    $('#drIp').text('DR (' + drType.IpAddress + ')');
    
//    const prStatusSummary = getStatusSummary(prType.Services.map(s => s.Status).filter(Boolean));
//    const drStatusSummary = getStatusSummary(drType.Services.map(s => s.Status).filter(Boolean));
//    // Append IPs with status summary
   
//    $('#prIp').html(`Primary (${prType.IpAddress}) <br ><span class="status-summary text-muted small">${prStatusSummary}</span>`);
//    $('#drIp').html(`DR (${drType.IpAddress}) <br ><span class="status-summary text-muted small">${drStatusSummary}</span>`);


//    // Unique list of all service names from both PR and DR
//    let allServiceNames = [...new Set([
//        ...prType.Services.map(s => s.ServiceName),
//        ...drType.Services.map(s => s.ServiceName)
//    ])];

//    // Build table rows
//    let tbody = $('#mssqlserverbody');
//    tbody.empty();

//    allServiceNames.forEach(serviceName => {
//        let prService = prType.Services.find(s => s.ServiceName === serviceName);
//        let drService = drType.Services.find(s => s.ServiceName === serviceName);

//        let prStatus = prService ? prService.Status : '--';
//        let drStatus = drService ? drService.Status : '--';
//        let prIcon = prStatus !== '--' ? `<i class="${getStatusIconClass(prStatus)} me-2 fs-6"></i>` : '';
//        let drIcon = drStatus !== '--' ? `<i class="${getStatusIconClass(drStatus)} me-2 fs-6"></i>` : '';
//        const iconService = serviceName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";

//        let row = `
//            <tr>
//                <td><i class="${iconService} me-2 fs-6"></i><span>${serviceName}</span></td>
//                <td>${prIcon}${prStatus}</td>
//                <td>${drIcon}${drStatus}</td>
//            </tr>
//        `;
//        tbody.append(row);
//    });
//}
//function getStatusSummary(arr) {
//    let countMap = {};
//    arr?.forEach(status => {
//        countMap[status] = (countMap[status] || 0) + 1;
//    });
//    let total = arr?.length;
//    let statusSummary = Object.entries(countMap)
//        .map(([status, count]) => `${count} ${status}`)
//        .join(', ');
//    return statusSummary ? `${statusSummary} out of ${total} services` : '--';
//}
//function getStatusIconClass(status) {
//    if (!status) return "text-danger cp-disable";

//    const lowerStatus = status.toLowerCase();
//    if (lowerStatus === "running") {
//        return "text-success cp-reload cp-animate";
//    } else if (lowerStatus === "error" || lowerStatus === "stopped") {
//        return "text-danger cp-fail-back";
//    } else {
//        return "text-danger cp-disable";
//    }
//}

async function oracledataguardmonitorstatus(id, type) {

    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noData);
    }
}
let asmNoDataimg = '<img src="/img/isomatric/nodatalag.svg" class="mx-auto"> <br><span class="text-danger">No data available</span>'
let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'
let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">'
let noData = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
let noPluggable = '<div class="NoData text-center p-2 d-grid justify-content-center">' +
    '<img src="/img/isomatric/nodatalag.svg" class="mx-auto" />' +
    '<span class="text-danger">' +
    'Pluggable Databases not configured.' +
    '</span>' +
    '</div>';

function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}

function propertiesData(value) {
    if (value !== undefined && value !== null && value !== '') {
        
        try {
            let jsonString = value?.properties;
            let data = JSON?.parse(jsonString);
            let customSite = data?.OracleDataGuardModels?.length > 1;
            if (customSite) {
                $("#Sitediv").show();
            } else {
                $("#Sitediv").hide();
            }


            $(".siteContainer").empty();


            data?.OracleDataGuardModels?.forEach((a, index) => {
                let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
                $(".siteContainer").append(selectTab);
            });


            if (data?.OracleDataGuardModels?.length > 0) {
                $("#siteName0 .nav-link").addClass("active");
                displaySiteData(data?.OracleDataGuardModels[0]);
            }



            let defaultSite = data?.OracleDataGuardModels?.find(d => d?.Type === 'DR') || data?.OracleDataGuardModels[0];                       
            //Datalag
            const datalag = checkAndReplace(defaultSite?.MonitoringModel?.PR_Datalag);
           
            $('#PR_Datalag').text(datalag)
            let dataLagValue = (datalag !== undefined && datalag !== "" && datalag !== null && datalag !== "0" && datalag !== "NA")
                ? `${datalag}`
                : 'NA';

            var result = "";
            let iconClass = "text-danger cp-disable";

            if (dataLagValue !== "NA") {
                if (dataLagValue.includes(".")) {
                    const values = dataLagValue.split(".");
                    const hours = values[0] * 24;
                    const minutes = values[1]?.split(':')?.slice(0, 2)?.join(':');
                    const min = minutes?.split(':');
                    const firstValue = parseInt(min[0]) + parseInt(hours);
                    result = firstValue + ":" + min[1];
                }
                else if (dataLagValue.includes("+")) {
                    const value = dataLagValue.split(" ");
                    result = value[1]?.split(':')?.slice(0, 2)?.join(':');
                }
                else {
                    result = dataLagValue.split(':')?.slice(0, 2)?.join(':');
                }

                const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
                const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);

                if (minute > value?.configuredRPO) {
                    $('#PR_Datalag').text(result).attr('title', result).css('color', 'red');
                } else {
                    $('#PR_Datalag').text(result).attr('title', result).css('color', '');
                }

                iconClass = "text-primary cp-time";
            } else {
                dataLagValue !== 'NA' ? $('#PR_Datalag').text(dataLagValue).attr('title', dataLagValue).css('color', 'red') : $('#PR_Datalag').text(dataLagValue).attr('title', dataLagValue).css('color', '')
            }

            $('#PR_Datalag').prepend(`<i class="${iconClass} me-1 fs-6"></i>`);


            if (defaultSite) {
                displaySiteData(defaultSite);
            }

            $(document).on('click', '.siteListChange', function () {
                $(".siteListChange .nav-link").removeClass("active");
                $(this).find(".nav-link").addClass("active");
                let siteId = $(this)[0]?.id
                let getSiteName = $(`#${siteId} .siteName`).text()

                let MonitoringModel = data?.OracleDataGuardModels?.find(d => d?.Type === getSiteName);
                if (MonitoringModel) {
                    displaySiteData(MonitoringModel, getSiteName);
                }
                bindMonitoringServices(globalMSSQLServerData, getSiteName);
            });
            function displaySiteData(siteData) {

                let obj = {};
                $('.dynamicSite-header').text(siteData?.Type).attr('title', siteData?.Type);

                for (let key in siteData?.MonitoringModel) {
                    obj[`DR_` + key] = siteData?.MonitoringModel[key];
                }
                if (obj['DR_Dbsize']) {
                    obj['DR_Dbsize'] += " MB";
                }


                let MonitoringModelOracle = [
                    "DR_Server_Name", "DR_Database_Sid", "DR_Unique_Name", "DR_Database_createdtime", "DR_Database_version", "DR_Database_role", "DR_Openmode",
                    "DR_Reset_logsmode", "DR_Archive_mode", "DR_InstanceName", "DR_InstanceId", "DR_InstanceId", "DR_InstanceStartUpTime", "DR_OpenMode", "DR_Archiver",
                    "DR_Archive_Dest_Location", "DR_Archivelog_compression", "DR_Log_sequence", "DR_Currentscn", "DR_TNSServiceName", "DR_Control_filetype",
                    "DR_Control_filename", "DR_IsClusterDatabase", "DR_DB_Reset_logschange", "DR_ParameterFile", "DR_Platform_name", "DR_Database_incarnation",
                    "DR_Db_create_file_dest", "DR_Db_create_online_log_dest1", "DR_Db_recovery_file_dest", "DR_Db_recovery_file_dest_size", "DR_Db_file_name_convert",
                    "DR_Log_file_name_convert", "DR_Flashback_on", "DR_Db_flashback_retention_target", "DR_Active_DG_Enabled", "DR_Affirm", "DR_Remote_login_passwordfile",
                    "DR_Protection_mode", "DR_Transmit_mode", "DR_Recovery_mode", "DR_Recovery_Status", "DR_Delay_mins", "DR_Dg_broker_status", "DR_Dataguard_status"
                    , "DR_Switchover_status", "DR_Log_archive_config", "DR_Force_logging", "DR_Fal_server", "DR_Fal_client", "DR_Standby_file_management", "DR_Standby_log_FileCount"
                    , "DR_Standby_redo_logs", "DR_Transport_lag", "DR_Apply_lag", "DR_Apply_finish_time", "DR_Estimated_startup_time", "DR_CDB", "DR_Containers", "DR_Pdbs", "DR_PDB_Name"
                    , "DR_CONNECTION_ID", "DR_PDB_ID", "DR_PDB_MODE", "DR_LOGGING", "DR_FORCE_LOGGING", "DR_RECOVERY_STATUS", "DR_PDB_SIZE"
                    , "DR_Dbsize",];

                if (Object.keys(obj).length > 0) {
                    bindProperties(obj, MonitoringModelOracle, value);
                }
            }
            // oracleSolutionDiagram(data);
            //Archieve \
            let dbDetail = data?.PrOracleDataGuardModel?.PrMonitoringModel;            
            const hourly = dbDetail?.Archieve_Log_Genearion_Hourly && dbDetail?.Archieve_Log_Genearion_Hourly !== "NA" ? JSON.parse(dbDetail?.Archieve_Log_Genearion_Hourly) : {};
            const daily = dbDetail?.Archieve_Log_Genearion_Day && dbDetail?.Archieve_Log_Genearion_Day !== "NA" ? JSON.parse(dbDetail?.Archieve_Log_Genearion_Day) : {};
            const weekly = dbDetail?.Archieve_Log_Genearion_Weekly && dbDetail?.Archieve_Log_Genearion_Weekly !== "NA" ? JSON.parse(dbDetail?.Archieve_Log_Genearion_Weekly) : {};


            archiveLogDaily(daily);
            archiveLogHour(hourly);
            archiveLogWeek(weekly);

            //Database details
            const dbDetailsProp = [
                "PR_Server_Name", "PR_Unique_Name", "PR_Database_Sid",
                "PR_Database_version", "PR_Database_createdtime", "PR_Database_role", "PR_Openmode", "PR_Reset_logsmode",
                "PR_Archive_mode", "PR_InstanceName", "PR_InstanceId", "PR_InstanceStartUpTime", "PR_OpenMode", "PR_Archiver", "PR_Archive_Dest_Location", "PR_Archivelog_compression", "PR_Currentscn", "PR_TNSServiceName", "PR_Control_filetype",
                "PR_IsClusterDatabase", "PR_Control_filename", "PR_DB_Reset_logschange", "PR_ParameterFile", "PR_Platform_name", "PR_Database_incarnation", "PR_Db_create_file_dest", "PR_Db_create_online_log_dest1", "PR_Db_recovery_file_dest", "PR_Db_recovery_file_dest_size",
                "PR_Db_file_name_convert", "PR_Log_sequence", "PR_Log_file_name_convert", "PR_Flashback_on", "PR_Db_flashback_retention_target", "PR_Active_DG_Enabled", "PR_Affirm", "PR_Remote_login_passwordfile", "PR_Protection_mode",
                "PR_Transmit_mode", "PR_Recovery_mode", "PR_Recovery_Status", "PR_Delay_mins", "PR_Dg_broker_status", "PR_Dataguard_status", "PR_Switchover_status", "PR_Log_archive_config", "PR_Force_logging", "PR_Fal_server"
                , "PR_Fal_client", "PR_Standby_file_management", "PR_Standby_log_FileCount", "PR_Standby_redo_logs", "PR_Transport_lag", "PR_Apply_lag", "PR_Apply_finish_time", "PR_Estimated_startup_time", "PR_CDB", "PR_Containers", "PR_Pdbs", "PR_PDB_Name",
                "PR_CONNECTION_ID", "PR_PDB_ID", "PR_PDB_MODE", "PR_LOGGING", "PR_FORCE_LOGGING", "PR_RECOVERY_STATUS", "PR_PDB_SIZE"]



            var prDbsizeValue = checkAndReplace(dbDetail?.PR_Dbsize);
            //var drDbsizeValue = checkAndReplace(data?.DR_Dbsize);

            if (prDbsizeValue === 'NA') {
                $('#PR_Dbsize').text(prDbsizeValue).attr('title', prDbsizeValue);
            } else if (prDbsizeValue?.includes("MB")) {
                $('#PR_Dbsize').text(prDbsizeValue).attr('title', prDbsizeValue);
            }
            else {
                $('#PR_Dbsize').text(prDbsizeValue + " MB").attr('title', prDbsizeValue + " MB");
            }

            displayASM(data?.PR_Asm_Details, '#prASMData', '#asmPrimaryData');
            displayASM(data?.DR_Asm_Details, '#drASMData', '#asmDRData');

            bindProperties(dbDetail, dbDetailsProp);

        }      
        catch (error) {

            //notificationAlert("warning", "Invalid JSON Format");
            //setTimeout(() => {
            //    window.location.assign('/Dashboard/ITResiliencyView/List');
            //}, 3000)
        }
    }
    else {
        $("#noDataimg").css('text-align', 'center').html(noData);
    }
}
function displayASM(val, target, element) {
    try {
        if (!val) {
            $(element)
                .css('text-align', 'center')
                .html(asmNoDataimg);
        } else {
            let data = val.replace(/,(?=\s*[\]}])/, '');
            let asmVal = JSON?.parse(data);
            if (asmVal?.length > 0) {
                const asmRows = asmVal.map((list, i) => `<tr><td>${i + 1}</td><td>${list.NAME}</td><td>${list.STATE}</td><td>${list.TYPE}</td><td>${list.TOTAL_MB}</td><td>${list.FREE_MB}</td><td>${list['USED(%)']}</td></tr>`).join('');
                $(target).append(asmRows);
            }
            else {
                $(element)
                    .css('text-align', 'center')
                    .html(asmNoDataimg);
            }
        }
    } catch (error) {
        $(element)
            .css('text-align', 'center')
            .html(asmNoDataimg);
        //notificationAlert("warning", "Invalid JSON Format");
        //setTimeout(() => {
        //    window.location.assign('/Dashboard/ITResiliencyView/List');
        //}, 3000)
    }
}
function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined || value === 'NA') ? 'NA' : value;
}

function setPropData(data, propSets) {
    propSets?.forEach(properties => {
        bindProperties(data, properties);
    });
}
let iconClass
function bindProperties(data, properties,value) {

    properties?.forEach(property => {
        const values = data[property];
        const displayedValue = (value !== undefined || value !=='')? checkAndReplace(values) : 'NA';
        const iconHtml = getIconClass(displayedValue, property, data, value);
        const mergeValue = `${iconHtml}${displayedValue}`;
        $(`#${property}`).html(mergeValue).attr('title', displayedValue);
        //iconClass = getIconClass(displayedValue);

        //// Displayed value with icon
        //if (property !== 'PR_InstanceStartUpTime' && property !== 'DR_InstanceStartUpTime') {
        //    const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
        //    const mergeValue = `${iconHtml}${displayedValue}`;
        //    $(`#${property}`).html(mergeValue).attr('title', displayedValue);
        //}
        if (property === 'PR_InstanceStartUpTime' || property === 'DR_InstanceStartUpTime') {
            if (displayedValue !== "NA") {
                const [date, time] = displayedValue.split(' ');
                const formattedValue = `${date}<br>${time}`;
                const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
                const mergeValue = `${iconHtml}${formattedValue}`;
                $(`#${property}`).html(mergeValue).attr('title', `${date} ${time}`);
            }
            else {
                const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
                const mergeValue = `${iconHtml}${displayedValue}`;
                $(`#${property}`).html(mergeValue).attr('title', displayedValue);
            }

        }
        //if (property === 'PR_Recovery_Status' || property === 'DR_Recovery_Status') {
        //    if (displayedValue !== "NA") {
        //        const part = displayedValue?.split('\t');
        //        const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
        //        const mergeValue = `${iconHtml}${part[1]}`;
        //        $(`#${property}`).html(mergeValue).attr('title', part[1]);
        //    }
        //}
    });
}

function getIconClass(displayedValue, property, data) {
      
    let prdbFile = data?.PR_Db_file_name_convert ? "text-primary cp-generate me-1 fs-6" : "text-danger cp-disable"
    let drdbFile = data?.DR_Db_file_name_convert ? "text-primary cp-generate me-1 fs-6" : "text-danger cp-disable"
    let prdestSize = data?.PR_Db_recovery_file_dest_size ? "text-primary cp-roate-settings me-1 fs-6" : "text-danger cp-disable"
    let drdestSize = data?.DR_Db_recovery_file_dest_size ? "text-primary cp-roate-settings me-1 fs-6" : "text-danger cp-disable"
    let prlog = data?.PR_Log_file_name_convert ? "text-primary cp-log-file-name me-1 fs-6" : "text-danger cp-disable"
    let drlog = data?.DR_Log_file_name_convert ? "text-primary cp-log-file-name me-1 fs-6" : "text-danger cp-disable"
    let prFal = data?.PR_Fal_server ? "text-primary cp-fal-server me-1 me-1 fs-6" : "text-danger cp-disable"
    let drFal = data?.DR_Fal_server ? "text-primary cp-fal-server me-1 me-1 fs-6" : "text-danger cp-disable"
    let prFalClient = data?.PR_Fal_client ? "text-primary cp-fal-client me-1 me-1 fs-6" : "text-danger cp-disable"
    let drFalClient = data?.DR_Fal_client ? "text-primary cp-fal-client me-1 me-1 fs-6" : "text-danger cp-disable"
    let prunique = data?.PR_Unique_Name ? "text-primary cp-database-unique-name me-1 fs-6" : "text-danger cp-disable"
    let drunique = data?.DR_Unique_Name ? "text-primary cp-database-unique-name me-1 fs-6" : "text-danger cp-disable"
    let prTime = data?.PR_Database_createdtime ? "text-primary cp-time me-1 fs-6" : "text-danger cp-disable"
    let drTime = data?.DR_Database_createdtime ? "text-primary cp-time me-1 fs-6" : "text-danger cp-disable"
    let prdbVersion = data?.PR_Database_version ? "text-primary cp-version me-1 fs-6" : "text-danger cp-disable"
    let drdbVersion = data?.DR_Database_version ? "text-primary cp-version me-1 fs-6" : "text-danger cp-disable"
    let prArchieve = data?.PR_Archive_mode ? "text-success cp-archive-mode me-1 fs-6" : "text-danger cp-disable"
    let drArchieve = data?.DR_Archive_mode ? "text-success cp-archive-mode me-1 fs-6" : "text-danger cp-disable"
    let prInstance = data?.PR_InstanceName ? "text-primary cp-instance-name me-1 fs-6" : "text-danger cp-disable"
    let drInstance = data?.DR_InstanceName ? "text-primary cp-instance-name me-1 fs-6" : "text-danger cp-disable"
    let prInstanceId = data?.PR_InstanceId ? "text-primary cp-instance-id me-1 fs-6" : "text-danger cp-disable"
    let drInstanceId = data?.DR_InstanceId ? "text-primary cp-instance-id me-1 fs-6" : "text-danger cp-disable"
    let prInstancestart = data?.PR_InstanceStartUpTime ? "text-primary cp-timer-meter me-1 fs-6" : "text-danger cp-disable"
    let drInstancestart = data?.DR_InstanceStartUpTime ? "text-primary cp-timer-meter me-1 fs-6" : "text-danger cp-disable"
    let prDBlog = data?.PR_DB_Reset_logschange ? "text-primary cp-reset-log-change me-1 fs-6" : "text-danger cp-disable"
    let drDBlog = data?.DR_DB_Reset_logschange ? "text-primary cp-reset-log-change me-1 fs-6" : "text-danger cp-disable"
    let prFile = data?.PR_ParameterFile ? "text-primary cp-parameter-file me-1 fs-6" : "text-danger cp-disable"
    let drFile = data?.DR_ParameterFile ? "text-primary cp-parameter-file me-1 fs-6" : "text-danger cp-disable"
    let prPlatform = data?.PR_Platform_name ? "text-primary cp-platform-name me-1 fs-6" : "text-danger cp-disable"
    let drPlatform = data?.DR_Platform_name ? "text-primary cp-platform-name me-1 fs-6" : "text-danger cp-disable"
    
    const iconMapping = {
        'PR_Db_file_name_convert': prdbFile,
        'DR_Db_file_name_convert': drdbFile,
        'PR_Db_recovery_file_dest_size': prdestSize,
        'DR_Db_recovery_file_dest_size': drdestSize,
        'PR_Log_file_name_convert': prlog,
        'DR_Log_file_name_convert': drlog,
        'PR_Fal_server': prFal,
        'DR_Fal_server': drFal,
        'PR_Fal_client': prFalClient,
        'DR_Fal_client': drFalClient,
        'PR_Unique_Name': prunique,
        'DR_Unique_Name': drunique,
        'PR_Database_createdtime': prTime,
        'DR_Database_createdtime': drTime,
        'PR_Database_version': prdbVersion,
        'DR_Database_version': drdbVersion,
        'PR_Archive_mode': prArchieve,
        'DR_Archive_mode': drArchieve,
        'PR_InstanceName': prInstance,
        'DR_InstanceName': drInstance,
        'PR_InstanceId': prInstanceId,
        'DR_InstanceId': drInstanceId,
        'PR_InstanceStartUpTime': prInstancestart,
        'DR_InstanceStartUpTime': drInstancestart,
        'PR_DB_Reset_logschange': prDBlog,
        'DR_DB_Reset_logschange': drDBlog,
        'PR_ParameterFile': prFile,
        'DR_ParameterFile': drFile,
        'PR_Platform_name': prPlatform,
        'DR_Platform_name': drPlatform,
    }
    iconClass = iconMapping[property] || ''; 
    switch (displayedValue?.toLowerCase()) {
        case 'not allowed':
        case 'no':
        case 'na':
            iconClass = 'text-danger cp-disable';
            break;
        case 'manual':
            iconClass = 'text-warning cp-settings';
            break;
        case 'healthy':
            iconClass = 'text-success cp-health-success';
            break;
        case 'nothealthy':
        case 'not_healthy':
        case 'unhealthy':
            iconClass = 'text-danger cp-health-error';
            break;
        case 'online':
            iconClass = 'text-success cp-online';
            break;
        case 'offline':
            iconClass = 'text-danger cp-offline';
            break;
        case 'primary':
            iconClass = 'text-primary cp-list-prsite';
            break;
        case 'secondary':
            iconClass = 'text-info cp-dr';
            break;
        case 'physical standby':
            iconClass = 'text-info cp-physical-drsite';
            break;
        case 'connected':
        case 'connect':
            iconClass = 'text-success cp-connected';
            break;
        case 'disconnected':
        case 'disconnect':
            iconClass = 'text-danger cp-disconnected';
            break;
        case 'synchronous_commit':
        case 'synchronized':
        case 'synchronizing':
        case 'sync':
            iconClass = 'text-success cp-refresh';
            break;
        case 'asynchronous_commit':
        case 'notsynchronizing ':
        case 'notsynchronized':
        case 'not':
        case 'not synchronized':
        case 'not synchronizing':
        case 'asynchronizing':
        case 'asynchronized':
        case 'async':
            iconClass = 'text-danger cp-refresh';
            break;
        case 'pending':
            iconClass = 'text-warning cp-pending';
            break;
        case 'running':
        case 'run':
            iconClass = 'text-success cp-reload cp-animate';
            break;
        case 'error':
            iconClass = 'text-danger cp-fail-back';
            break;
        case 'stopped':
        case 'stop':
            iconClass = 'text-danger cp-Stopped';
            break;
        case 'standby':
        case 'to standby':
        case 'mounted':
            iconClass = 'text-warning cp-control-file-type';
            break;
        case 'enabled':
        case 'enable':
            iconClass = 'text-success cp-enables';
            break;
        case 'disabled':
        case 'disable':
            iconClass = 'text-danger cp-disables';
            break;
        case 'true':
        case 'yes':
            iconClass = 'text-success cp-agree';
            break;
        case 'valid':
            iconClass = 'text-success cp-success';
            break;
        case 'false':
        case 'defer':
        case 'deferred':
            iconClass = 'text-danger cp-error';
            break;
        case 'pause':
        case 'paused':
            iconClass = 'text-warning cp-circle-pause';
            break;
        case 'required':
        case 'require':
            iconClass = 'text-warning cp-warning';
            break;
        case 'on':
            iconClass = 'text-success cp-end';
            break;
        case 'off':
            iconClass = 'text-danger cp-end';
            break;
        case 'current':
        case 'read write':
            iconClass = 'text-success cp-file-edits';
            break;
        default:
            break;
    }
    return iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
}