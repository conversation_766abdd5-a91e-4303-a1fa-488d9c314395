﻿using ContinuityPatrol.Application.Features.Report.Queries.AirGapReport;
using ContinuityPatrol.Application.Features.Report.Queries.BulkImportReport;
using ContinuityPatrol.Application.Features.Report.Queries.BusinessServiceSummaryReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDrDrillReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyExecutionReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetInfraObjectConfigurationReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReportByBusinessService;
using ContinuityPatrol.Application.Features.Report.Queries.GetRTOReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRunBookReport;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSummaryReport;
using ContinuityPatrol.Application.Features.Report.Queries.UserActivityReport;
using ContinuityPatrol.Domain.ViewModels.RpoSlaDeviationReportModel;
using ContinuityPatrol.Application.Features.Report.Queries.DriftReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetCyberSnapList;
using ContinuityPatrol.Application.Features.Report.Queries.CyberSnapsReport;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSchedulerLogs.Models;
using ContinuityPatrol.Application.Features.Report.Queries.GetSchedulerWorkflowActionResultsReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetCGExecutionReport;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IReportService
{
    Task<object> GetRpoSlaReportByInfraObjectId(string infraObjectId, string type, string reportStartDate, string reportEndDate,string dateOption);
    Task<RTOReports> GetRtoReportByWorkflowOperationId(string workflowOperationId);
    Task<LicenseReport> GetLicenseReportById(string licenseId);
    Task<DrReadyStatusReport> GetDrReadyStatusReportByBusinessServiceId(string businessServiceId);
    Task<DRReadyExecutionReport> GetDrReadyExecutionLogReport(string startTime, string endTime, string businessserviceid);
    Task<BusinessServiceSummaryReport> GetBusinessServiceSummaryReport();
    Task<InfraObjectSummaryReport> GetInfraObjectSummaryReport(string businessServiceId);
    //Task<GetRpoSlaDeviationReportVm> GetRpoSlaDeviationReportByStartTimeEndTimeAndBusinessServiceId(string businessServiceId, string createdDate,
    //   string lastModifiedDate);
    Task<GetRpoSlaDeviationReportVm> GetRpoSlaDeviationReportByStartTimeEndTimeAndBusinessServiceIdAndInfraObjectId(string businessServiceId,string infraObjectId, string createdDate,
      string lastModifiedDate);
    
    Task<InfraReport> GetInfraObjectConfigurationReport(string infraObjectId);
    Task<UserActivityReport> GetUserActivityReport(string userId, string createDate, string lastModifiedDate);
    Task<DrDrillReport> GetDrDrillReportByWorkflowOperationId(string workflowOperationId,string runMode, bool isCustom);

    Task<LicenseReportByBusinessServiceReport> GetLicenseUtilizationReportByBusinessServiceId(string businessServiceId);
    Task<GetRunBookReportVm> GetRunbookReportByWorkflowId(string workflowId);
    Task<AirGapLogReportVm> GetAirGapReport(string startDate, string endDate, string airGapId);
    Task<List<GetAirGapListVm>> GetAirGapList(string startDate, string endDate);
    Task<GetBulkImportReportVm> GetBulkImportReport(string operationId);
    Task<DriftReportVm> GetDriftReport(string startDate, string endDate, string InfraId, string DriftStatusId);
    Task<List<DriftEventReportVm>> GetDriftReportInfraId(string startDate, string endDate);
    Task<List<DriftEventReportVm>> GetDriftReportStatus(string startDate, string endDate,string InfraId);
    Task<List<GetCyberSnapsListVm>> GetCyberSnapsList(string startDate, string endDate);
    Task<GetCyberSnapsReportVm> GetCyberSnapsBySnapTagName(string cyberSnapId, string startDate, string endDate);

    Task<GetResiliencyReadinessSchedulerLogReportVm> GetInfraObjectSchedulerLogList(string startDate, string endDate);
    Task<SchedulerWorkflowActionResultsVm> GetScheduleWorkflowActionResultReport(string workflowId, string infraReferenceId);
    Task<CGExecutionReportResultVm> GetCGExecutionReport(string workflowOperationId);
}
