﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels.UserViewModel
@Html.AntiForgeryToken()
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@using ContinuityPatrol.Shared.Services.Helper;
<link href="~/css/password_stregnth_meter.css" rel="stylesheet" />
<link href="~/lib/jquery.steps/jquery.steps.css" rel="stylesheet" />
<link href="~/css/wizard.css" rel="stylesheet" />
<link rel="stylesheet" type="text/css" href="~/css/Icon.css">
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title">
                <i class="cp-user"></i><span>Users</span>
            </h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="userSearch" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input filterSearch" type="checkbox" value="loginname=" id="Name">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input filterSearch" type="checkbox" value="companyname=" id="CompanyName">
                                        <label class="form-check-label" for="CompanyName">
                                            Company Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input filterSearch" type="checkbox" value="logintype=" id="Logintype">
                                        <label class="form-check-label" for="Logintype">
                                            Authentication Type
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm btn-User-Create" id="btnCreate" data-bs-toggle="modal"
                        data-bs-target="#userCreateModal">
                    <i class="cp-add me-1"></i>Create
                </button>
            </form>
        </div>
        <div class="card-body pt-0 ">
            <table id="userTable" class="table table-hover dataTable no-footer" style="width:100%" ;>
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Name</th>
                        <th>Company Name</th>
                        <th>Authentication Type</th>
                        <th>Role</th>
                        <th>Email</th>
                        <th>Last Login Time</th>
                       @*  <th>Communication</th> *@
                        <th>State</th>
                        <th class="Action-th">Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <div>
                <span class="align-middle" id="userRole" style="display:none;">  @WebHelper.UserSession.RoleName</span>
                <span class="align-middle" id="userRoleValue" style="display:none;">  @WebHelper.UserSession.RoleName</span>
                <span class="align-middle" id="loggedInUserId" style="display:none;">  @WebHelper.UserSession.LoggedUserId</span>
            </div>
        </div>
    </div>
    <!--Reset Modal-->
    <div class="modal fade" id="ResetModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <partial name="Reset" />
    </div>
</div>
<div id="userCreate" data-create-permission="@WebHelper.CurrentSession.Permissions.Admin.CreateAndEdit" aria-hidden="true"></div>
<div id="userDelete" data-delete-permission="@WebHelper.CurrentSession.Permissions.Admin.Delete" aria-hidden="true"></div>
<div class="modal fade" id="userCreateModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Configuration" />
</div>
<div class="modal fade" id="userDeleteModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<script src="~/js/Admin/Usermanagement/Users/<USER>"></script>
<script src="~/js/Admin/UserManagement/Users/<USER>"></script>
<script src="~/js/Admin/UserManagement/Users/<USER>"></script>
<script src="~/js/Admin/UserManagement/Users/<USER>"></script>
<script src="~/js/Admin/UserManagement/Users/<USER>"></script> 
<script src="~/js/common/wizard.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.js"></script>
<script src="~/js/common/show_hide_password.js"></script>