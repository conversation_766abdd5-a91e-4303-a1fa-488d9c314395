using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Delete;
using ContinuityPatrol.Application.Features.AdPasswordJob.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordJob.Commands;

public class DeleteAdPasswordJobTests : IClassFixture<AdPasswordJobFixture>
{
    private readonly AdPasswordJobFixture _adPasswordJobFixture;
    private readonly Mock<IAdPasswordJobRepository> _mockAdPasswordJobRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly DeleteAdPasswordJobCommandHandler _handler;

    public DeleteAdPasswordJobTests(AdPasswordJobFixture adPasswordJobFixture)
    {
        _adPasswordJobFixture = adPasswordJobFixture;

        _mockAdPasswordJobRepository = AdPasswordJobRepositoryMocks.CreateDeleteAdPasswordJobRepository(_adPasswordJobFixture.AdPasswordJobs);
        _mockPublisher = new Mock<IPublisher>();

        _handler = new DeleteAdPasswordJobCommandHandler(
            _mockAdPasswordJobRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_DeleteAdPasswordJobResponse_When_AdPasswordJobDeleted()
    {
        var validGuid = _adPasswordJobFixture.AdPasswordJobs[0].ReferenceId;
        _adPasswordJobFixture.AdPasswordJobs[0].ReferenceId = validGuid;

        var result = await _handler.Handle(new DeleteAdPasswordJobCommand { Id = validGuid }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteAdPasswordJobResponse));
        result.IsActive.ShouldBeFalse();
        result.Message.ShouldContain("AdPasswordJob");
      
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var validGuid = _adPasswordJobFixture.AdPasswordJobs[0].ReferenceId;
        _adPasswordJobFixture.AdPasswordJobs[0].ReferenceId = validGuid;

        var result = await _handler.Handle(new DeleteAdPasswordJobCommand { Id = validGuid }, CancellationToken.None);

        // Assert
        _mockAdPasswordJobRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_UpdateAsync_OnlyOnce()
    {
        // Arrange
        var validGuid = _adPasswordJobFixture.AdPasswordJobs[0].ReferenceId;
        _adPasswordJobFixture.AdPasswordJobs[0].ReferenceId = validGuid;

        var result = await _handler.Handle(new DeleteAdPasswordJobCommand { Id = validGuid }, CancellationToken.None);

        // Assert
        _mockAdPasswordJobRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.AdPasswordJob>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_PublishEvent_OnlyOnce()
    {
        var validGuid = _adPasswordJobFixture.AdPasswordJobs[0].ReferenceId;
        _adPasswordJobFixture.AdPasswordJobs[0].ReferenceId = validGuid;

        var result = await _handler.Handle(new DeleteAdPasswordJobCommand { Id = validGuid }, CancellationToken.None);

        // Assert
        _mockPublisher.Verify(x => x.Publish(It.IsAny<AdPasswordJobDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_SetIsActiveToFalse_When_AdPasswordJobDeleted()
    {
        var validGuid = _adPasswordJobFixture.AdPasswordJobs[0].ReferenceId;
        _adPasswordJobFixture.AdPasswordJobs[0].ReferenceId = validGuid;

        _mockAdPasswordJobRepository
            .Setup(repo => repo.GetByReferenceIdAsync(validGuid))
            .ReturnsAsync(_adPasswordJobFixture.AdPasswordJobs[0]);
       
        var result = await _handler.Handle(new DeleteAdPasswordJobCommand { Id = validGuid }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AdPasswordJobNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new DeleteAdPasswordJobCommand { Id = nonExistentId };

        _mockAdPasswordJobRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.AdPasswordJob)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_PublishEventWithCorrectName_When_AdPasswordJobDeleted()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        var command = new DeleteAdPasswordJobCommand { Id = existingJob.ReferenceId };

        AdPasswordJobDeletedEvent capturedEvent = null;
        _mockPublisher.Setup(x => x.Publish(It.IsAny<AdPasswordJobDeletedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<AdPasswordJobDeletedEvent, CancellationToken>((evt, ct) => capturedEvent = evt)
            .Returns(Task.CompletedTask);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEvent.ShouldNotBeNull();
        capturedEvent.Name.ShouldBe(existingJob.DomainServer);
    }
}
