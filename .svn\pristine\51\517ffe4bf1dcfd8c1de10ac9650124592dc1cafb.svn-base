﻿let globalbusinessFunId = '';

const businessFunURL = {
    businessFunExistUrl: "Configuration/OperationalFunction/IsBusinessFunctionNameExist",
    businessFunPaginatedUrl: "/Configuration/OperationalFunction/GetPaginationList",
     saveOrUpdate: "Configuration/OperationalFunction/CreateOrUpdate"
}

const errorElements = ['#Name-error', '#textDescription-error', '#ConfiguredRPO-error', '#ConfiguredRTO-error', '#ConfiguredMAO-error', '#RPOThreshold-error', '#selectBusinessService-error', "#selectCriticalityLevel-error"];
const exceptThisSymbol = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "_", "+", "=", "{", "}", "[", "]", "|", ":", ";", "'", "\"", "<", ">", "?", "/", "\\"];
$(function () {
    let createPermission = $("#configurationCreate").data("create-permission").toLowerCase();
    let deletePermission = $("#configurationDelete").data("delete-permission").toLowerCase();

    if (createPermission == 'false') {
        $("#createButton").removeClass('#createButton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }
    let selectedValues = [];
    let dataTable = $('#businessFunction').DataTable(

        {
            language: {
                paginate: {             
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow"></i>',

                }
                , infoFiltered: ""

            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": businessFunURL.businessFunPaginatedUrl ,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "businessServiceName" : sortIndex === 3 ? "criticalityLevel" :
                        sortIndex === 4 ? "configuredRPO" : sortIndex === 5 ? "configuredRTO" : sortIndex === 6 ? "configuredMAO" : sortIndex === 7 ? "status" : "";
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {

                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json.data.length === 0) {
                        $(".pagination-column").addClass("disabled")
                        $('.page-link').attr('title', 'Previous')
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data;
                },
            },
            "columnDefs": [
                {
                    "targets": [1,2,3],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "businessServiceName", "name": "Business Service", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "criticalityLevel",
                    "name": "Criticality Level",
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display' && (data == 1 || data == 2 || data == 3)) {
                            const iconClass = data == 1 ? "cp-up-doublearrow text-danger me-1" :
                                data == 2 ? "cp-equal text-warning  me-1" :
                                    "cp-down-doublearrow text-info  me-1";
                            const tooltip = data == 1 ? "High" : data == 2 ? "Medium" : "Low";
                            return ` <span > <i class="${iconClass}"></i> ${tooltip}
                            </span>`;
                        }
                        return data;

                    }

                },

                {
                    "data": "configuredRPO", "name": "Config RPO", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span >' + data + ' min</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "configuredRTO", "name": "Config RTO", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span >' + data + ' min</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "configuredMAO", "name": "Config MAO", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span >' + data + ' min</span>';
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class=" edit-button" data-businessfunction='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="delete-button" data-businessfunction-id="${row.id}" data-businessfunction-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                  
                        </div>`;
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class=" edit-button" data-businessfunction='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="icon-disabled">
                                                <i class="cp-Delete"></i>
                                            </span>
                                  
                        </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="delete-button" data-businessfunction-id="${row.id}" data-businessfunction-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                  
                        </div>`;
                        }
                        else {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="icon-disabled">
                                    <i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }

                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },

            initComplete: function () {             
                $('.paginate_button.page-item.previous').attr('title', 'Previous');                
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });
   

    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        let BusinessServiceCheckbox = $("#BusinessServiceName");
        let NameCheckbox = $("#Name");
        let inputValue = $('#search-inp').val();
        if (BusinessServiceCheckbox.is(':checked')) {
            selectedValues.push(BusinessServiceCheckbox.val() + inputValue);
        }
        if (NameCheckbox.is(':checked')) {
            selectedValues.push(NameCheckbox.val() + inputValue);
        }
        dataTable.ajax.reload(function (json) {

            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    },500))

    $('.select2-icon').select2({
        templateSelection: formatText,
        templateResult: formatText
    });

    function formatText(icon) {
        return $('<span><i class="bi ' + $(icon.element).data('icon') + '"></i> ' + icon.text + '</span>');
    };

 
    $(function () {
        $("#create").on("click",function (e) {
            $('#SaveFunction').text("Save");
     });

      
        //Create
        $("#selectBusinessService").on('change', function () {
            let selectBusinessService = $("#selectBusinessService option:selected").attr('id');
            $('#textBusinessServiceId').val(selectBusinessService);
            let value = $(this).val();
            let errorElement = $('#selectBusinessService-error');
            validateDropDown(value, "Select business service", errorElement);
        });


        //Delete
        $("#businessFunction").on('click', '.delete-button', function () {
            let businessFunctionId = $(this).data('businessfunction-id');
            let businessFunctionName = $(this).data('businessfunction-name');
            $("#deleteData").attr("title", businessFunctionName);
            $('#deleteData').text(businessFunctionName);
            $('#textDeleteId').val(businessFunctionId);
        });
     
        //Update
        $('#businessFunction').on('click', '.edit-button', function () {
            let businessFunctionData = $(this).data('businessfunction');
            populateModalFields(businessFunctionData);
            $('#SaveFunction').text("Update");
            ClearErrorElements(errorElements);
            $('#CreateModal').modal('show');
        });

        // Name Validations
        $('#textName').on('keyup', commonDebounce(async function () {
            let elementValue = $(this).val();
            let errorElement = $('#Name-error');
            let sanitizedValue = elementValue.replace(/\s{2,}/g, ' ');
            $(this).val(sanitizedValue);
            await validateName(sanitizedValue, "Enter operational function name", errorElement);
        },400));

        // Description
        $('#textDescription').on('input', async function (event) {
            let value = $(this).val();
            let sanitizedValue = value.replace(/^\s+/, '').replace(/\s{2,}/g, ' ');
            $(this).val(sanitizedValue);
            if (exceptThisSymbol.includes(event.key)) {
                event.preventDefault();
            }
            let errorElement = $('#textDescription-error');
            await validateDescription(sanitizedValue, "Should not allow more than 250 characters", errorElement);
        });

        // RPO
        $('#textConfiguredRPO').on('input keypress', function (event) {
            let elementValue = $(this).val();
            let errorElement = $('#ConfiguredRPO-error');
            let thresholdErrorElement = $('#RPOThreshold-error');
            let rtoErrorElement = $('#ConfiguredRTO-error');
            let numericValue = parseInt($(this).val(), 10);
            if (isNaN(numericValue) || numericValue < 1) {
                $(this).val('');
            }
            validateNumber(elementValue, "Enter RPO", errorElement);
            
            if (elementValue.length >= 5) {
                event.preventDefault();
                return;
            }
            
            if (event.type === 'keypress') {
                if (event.which < 48 || event.which > 57) {
                    event.preventDefault();
                }
            }
            let rtoElementValue = $('#textConfiguredRTO').val();
            let thresholdElementValue = $('#textRPOThreshold').val();
           
            if (parseInt(elementValue) >= parseInt(thresholdElementValue)) {
                thresholdErrorElement.text('').removeClass('field-validation-error');
            } else if (parseInt(elementValue) < parseInt(thresholdElementValue)) {
                ShowValidation("Should be less than RPO", thresholdErrorElement);
            }
            
            if (!elementValue) {
                $('#ConfiguredRPO-error').text("Enter RPO").addClass('field-validation-error');
            } else
                if (parseInt(elementValue) >= parseInt(rtoElementValue)) {
                ShowValidation("Should be more than RPO", rtoErrorElement)
            }             
        });

        // RTO
        $('#textConfiguredRTO').on('input keypress', function (event) {
            let elementValue = $(this).val();
            let errorElement = $('#ConfiguredRTO-error');
            validateNumber(elementValue, "Enter RTO", errorElement);          
            let rpoElementValue = $('#textConfiguredRPO').val();
            let errorElementmao = $('#ConfiguredMAO-error');
            let maoElementValue = $('#textConfiguredMAO').val();
            let numericValue = parseInt(elementValue, 10);
            let valRPO = $('#textConfiguredRPO').val()
         
            if (isNaN(numericValue) || numericValue < 1) {
                $(this).val('');
            }
            if (event.type === 'keypress') {
                if (event.which < 48 || event.which > 57) {
                    event.preventDefault();
                }
                if (valRPO === '') {
                    $('#ConfiguredRPO-error').text("Enter RPO").addClass('field-validation-error');
                    return false;
                }
            }

            if (elementValue.length >= 5) {
                event.preventDefault();
            }          
            if (numericValue <= parseInt(rpoElementValue)) {
                ShowValidation("Should be more than RPO", errorElement);
            }
            if (!elementValue) {
                $('#ConfiguredRTO-error').text("Enter RTO").addClass('field-validation-error');
            } else if (parseInt(elementValue) >= parseInt(maoElementValue)) {
                ShowValidation("Should be more than RTO", errorElementmao)
            }                        
        });

        // MAO
        $('#textConfiguredMAO').on('input keypress', function (event) {
            let elementValue = $(this).val();
            let errorElement = $('#ConfiguredMAO-error');
            validateNumber(elementValue, "Enter MAO", errorElement);
            let valRTO = $('#textConfiguredRTO').val();
            let valRPO = $('#textConfiguredRPO').val();        
            let rtoElementValue = $('#textConfiguredRTO').val();
            let numericValue = parseInt($(this).val(), 10);
            if (isNaN(numericValue) || numericValue < 1) {
                $(this).val('');
            }
            if (event.type === 'keypress') {

                if (event.which < 48 || event.which > 57) {
                    event.preventDefault();
                }
                if (valRTO === '' || valRPO === '') {
                    if (valRTO === '') $('#ConfiguredRTO-error').text("Enter RTO").addClass('field-validation-error');

                    if (valRPO === '') $('#ConfiguredRPO-error').text("Enter RPO").addClass('field-validation-error');
                    return false;
                }
            }       
            if (elementValue.length >= 5) {
                event.preventDefault();
            }
            if (parseInt(elementValue) <= parseInt(rtoElementValue)) {
                ShowValidation("Should be more than RTO", errorElement);
            }
               
        });

        // RPOThreshold
        $('#textRPOThreshold').on('input keypress', function (event) {         
            let valRTO = $('#textConfiguredRTO').val();
            let valRPO = $('#textConfiguredRPO').val()
            let valMAO = $('#textConfiguredMAO').val()
            let elementValue = $(this).val();
            let errorElement = $('#RPOThreshold-error');
            validateNumber(elementValue, "Enter threshold RPO", errorElement);
            let rpoElementValue = $('#textConfiguredRPO').val();
            let numericValue = parseInt($(this).val(), 10);
            if (isNaN(numericValue) || numericValue < 1) {
                $(this).val('');
            }
            if (event.type === 'keypress') {
                if (event.which < 48 || event.which > 57) {
                    event.preventDefault();
                }
                if (valRTO === '' || valRPO === '' || valMAO === '') {
                    if (valRTO === '') $('#ConfiguredRTO-error').text("Enter RTO").addClass('field-validation-error');
                    if (valMAO === '') $('#ConfiguredMAO-error').text("Enter MAO").addClass('field-validation-error');
                    if (valRPO === '') $('#ConfiguredRPO-error').text("Enter RPO").addClass('field-validation-error');
                    return false;
                }
            }
            if (elementValue.length >= 5) {
                event.preventDefault();
            }
            if (rpoElementValue.length > 0 && elementValue.length > 0) {
                if (parseInt(rpoElementValue) <= parseInt(elementValue)) {
                    ShowValidation("Should be less than RPO", errorElement);
                } else {
                    errorElement.text('').removeClass('field-validation-error');           
                    return true;
                }
            }   
        });

        $('#selectBusinessService').on('change', function (event) {
            let value = $(this).val();
            let errorElement = $('#selectBusinessService-error');
            validateDropDown(value, "Select operational service", errorElement);
        });


        $(document).on('change', '.priorityBtn', function () {
            $('.priorityBtn').prop('checked', false)
            let value = $(this).val()
            $(this).prop('checked', true)
            $("#btnradio").val(value)
            validateCritical(value, "select critical level");

        })

        //Save      
        $("#SaveFunction").on('click', commonDebounce(async function () {
            let form = $("#example-form");
            elementName = $("#textName").val();
            errorElement = $('#Name-error');
            let isName = await validateName(elementName, "Enter operational function name", errorElement);
            elementDescription = $("#textDescription").val()
            errorElement = $('#textDescription-error');
            let isDescription = await validateDescription(elementDescription, "Should not allow more than 250 characters", errorElement);
            elementBusinessService = $('#selectBusinessService').val();
            errorElement = $('#selectBusinessService-error');
            let isSelectValue = await validateDropDown(elementBusinessService, "Select operational service", errorElement);
            elementCritical = $(".priorityBtn:checked").val();
            let isCriticalValue = await validateCritical(elementCritical, "Select critical level");
            elementRPO = $('#textConfiguredRPO').val();
            errorElement = $('#ConfiguredRPO-error');
            let isNumberRPO = await validateNumber(elementRPO, "Enter RPO", errorElement);
            elementRTO = $('#textConfiguredRTO').val();
            errorElement = $('#ConfiguredRTO-error');
            let isRTOGreaterThanRPO = parseInt(elementRTO) <= parseInt(elementRPO)
            let isNumberRTO = isRTOGreaterThanRPO ? await ShowValidation("Should be more than RPO", errorElement) : await validateNumber(elementRTO, "Enter RTO", errorElement);
            elementMAO = $('#textConfiguredMAO').val();
            errorElement = $('#ConfiguredMAO-error');
            let isMAOGreaterThanRTO = parseInt(elementMAO) <= parseInt(elementRTO)
            let isNumberMAO = isMAOGreaterThanRTO ? await ShowValidation("Should be more than RTO", errorElement) : await validateNumber(elementMAO, "Enter MAO", errorElement);
            elementThreshold = $('#textRPOThreshold').val();
            errorElement = $('#RPOThreshold-error');
            let isThresholdLessThanRPO = parseInt(elementThreshold) >= parseInt(elementRPO);
            let isNumberThreshold = isThresholdLessThanRPO ? await ShowValidation("Should be less than RPO", errorElement) : await validateNumber(elementThreshold, "Enter threshold RPO", errorElement);           
            let businessFunsanitizeArray = ['textName', 'textDescription', 'selectBusinessService', 'textBusinessServiceId', 'btnradio', 'textConfiguredRPO', 'textConfiguredRTO', 'textConfiguredMAO','textRPOThreshold']
            sanitizeContainer(businessFunsanitizeArray)
            setTimeout(() => {
                if (isName && isNumberRPO && isNumberRTO && isSelectValue && isCriticalValue && isNumberMAO && isNumberThreshold && isDescription) {
                    /*   form.trigger('submit');*/
                    savePermission()
                }
            }, 200)      
        }, 800));

        const savePermission = async () => {
            
            let data = {
                "Id": $('#textBusinessFunctionId').val(),
                "BusinessServiceId": $('#textBusinessServiceId').val(),
                "BusinessServiceName": $('#selectBusinessService').val(),
                "Name": $("#textName").val(),
                "Description": $("#textDescription").val(),
                "CriticalityLevel": $("#btnradio").val(),
                "ConfiguredRPO": $('#textConfiguredRPO').val(),
                "ConfiguredRTO": $('#textConfiguredRTO').val(),
                "ConfiguredMAO": $('#textConfiguredMAO').val(),
                "RPOThreshold": $('#textRPOThreshold').val(),
                
                __RequestVerificationToken: gettoken()
            }
            $('#SaveFunction').text() === "Update" ? data["id"] = globalbusinessFunId : null

            await $.ajax({
                type: "POST",
                url: RootUrl + businessFunURL.saveOrUpdate,
                dataType: "json",
                data: data,
                success: function (result) {
                    let data = result.data
                    if (result.success) {
                        notificationAlert("success", data.message)
                        $('#CreateModal').modal('hide');
                        setTimeout(() => {
                            dataTable.ajax.reload()
                        }, 2000)
                    } else {
                        errorNotification(result)
                    }
                },
                error: function (response) {
                    errorNotification(response)
                }
            })
        }


        //Clear data
        $('#createButton').on('click', function () {
            const errorElements = ['#Name-error', '#textDescription-error', '#selectBusinessService-error', '#selectCriticalityLevel-error', '#ConfiguredRPO-error', '#ConfiguredRTO-error', '#ConfiguredMAO-error', '#RPOThreshold-error'];
            $(".priorityBtn").prop('checked', false);
            clearInputFields('example-form', errorElements);
            $('#SaveFunction').text('Save');
            $('#selectBusinessService option:first').prop('selected', 'selected');

        });
    });

    function validateNumber(value, errorMsg, errorElement) {
        
        if (!value) {
            errorElement.text(errorMsg).addClass('field-validation-error');
            return false;
        } else if (value.length > 5) {
            errorElement.text("Enter the less than 5 digits").addClass('field-validation-error');
            return false;
        }
         else if (value > 20000) {
            errorElement.text("Value must be less than or equal to 20000").addClass('field-validation-error');
            return false;
        }
        else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
    async function validateDescription(value, errorMessage) {
        const errorElement = $('#textDescription-error');
        if (!value) {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        } else if (value.length < 0) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        } else if (value.length > 250) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        }
        else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
    async function validateName(value, errorMessage, errorElement) {
        if (!value) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        }
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }
        let url = RootUrl + businessFunURL.businessFunExistUrl;
        let data = {};
        data.businessFunctionName = value;
        data.id = $('#textBusinessFunctionId').val();

        let validationResults = [
            await SpecialCharValidateCustom(value),
            await ShouldNotBeginWithSpace(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithDotAndHyphen(value),
            await ShouldNotConsecutiveDotAndHyphen(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
            await ShouldNotEndWithSpace(value),
            await ShouldNotAllowMultipleSpace(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsNameExist(url, data, OnError)
        ];

        let failedValidations = validationResults.filter(result => result !== true);

        if (failedValidations.length > 0) {
            errorElement.text(failedValidations[0]).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }

    async function IsNameExist(url, data, errorFunc) {
        return !data.businessFunctionName.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
    }

    function validateDropDown(value, errorMsg, errorElement) {
        if (!value) {
            errorElement.text(errorMsg).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
    function validateCritical(value, errorMessage) {
        let errorElement = $('#selectCriticalityLevel-error');
        if (!value) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        }
        else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
 
   function ClearErrorElements(errorElements) {
        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });
    }

    async function ShowValidation(errorMessage, errorElement) {
        errorElement.text(errorMessage).addClass('field-validation-error');
        return false;
    }
    function populateModalFields(businessFunctionData) {
        $('#textName').val(businessFunctionData.name);
        $('#textDescription').val(businessFunctionData.description);
        $('#selectBusinessService').val(businessFunctionData.businessServiceName);
        $('#textConfiguredRPO').val(businessFunctionData.configuredRPO);
        $('#textConfiguredRTO').val(businessFunctionData.configuredRTO);
        $('#textConfiguredMAO').val(businessFunctionData.configuredMAO);
        $('#textRPOThreshold').val(businessFunctionData.rpoThreshold);
        $('#textBusinessServiceId').val(businessFunctionData.businessServiceId);
        $('#textBusinessFunctionId').val(businessFunctionData.id);
        selectBusinessService = businessFunctionData.businessServiceId;
        $('.priorityBtn').prop('checked', false)
        let criticalLevel = businessFunctionData.criticalityLevel;
        $(this).prop('checked', true)
        $('.priorityBtn[value="' + criticalLevel + '"]').prop("checked", true);
        $("#btnradio").val(criticalLevel)
    }
})