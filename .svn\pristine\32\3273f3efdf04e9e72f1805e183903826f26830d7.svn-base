﻿using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;

namespace ContinuityPatrol.Application.Features.DashboardView.Queries.GetNames;

public class GetDashboardNameQueryHandler : IRequestHandler<GetDashboardNameQuery, List<DashboardViewNameVm>>
{
    private readonly IDashboardViewRepository _dashboardViewRepository;
    private readonly IMapper _mapper;

    public GetDashboardNameQueryHandler(IMapper mapper, IDashboardViewRepository dashboardViewRepository)
    {
        _dashboardViewRepository = dashboardViewRepository;
        _mapper = mapper;
    }

    public async Task<List<DashboardViewNameVm>> Handle(GetDashboardNameQuery request,
        CancellationToken cancellationToken)
    {
        var dashboard = await _dashboardViewRepository.GetDashboardNames();

        return dashboard.Count == 0
            ? new List<DashboardViewNameVm>()
            : _mapper.Map<List<DashboardViewNameVm>>(dashboard);
    }
}