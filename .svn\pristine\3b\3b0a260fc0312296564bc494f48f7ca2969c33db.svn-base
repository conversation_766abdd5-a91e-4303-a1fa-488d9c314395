﻿using ContinuityPatrol.Application.Features.FormType.Commands.Create;
using ContinuityPatrol.Application.Features.FormType.Commands.Update;
using ContinuityPatrol.Application.Features.FormType.Queries.GetNames;
using ContinuityPatrol.Application.Features.FormType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FormTypeModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class FormTypeControllerTests
    {
        private FormTypeController _controller;
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<IFormTypeService> _mockFormTypeService = new();
        private readonly Mock<ILogger<FormTypeController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();

        public FormTypeControllerTests()
        {
            Initialize();
        }

        public void Initialize()
        {
            _controller = new FormTypeController(
                _mockPublisher.Object,
               // _mockFormTypeService.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_ShouldReturnViewResult()
        {
            // Act
            var result = await _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldReturnJsonResult_ForCreate()
        {
            // Arrange
            var formTypeViewModel = new FormTypeViewModel { FormTypeName = "New Form Type" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty id for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new CreateFormTypeCommand();
            _mockMapper.Setup(m => m.Map<CreateFormTypeCommand>(formTypeViewModel)).Returns(command);
            _mockDataProvider.Setup(dp => dp.FormType.CreateAsync(command))
                             .ReturnsAsync(new BaseResponse { Success = true, Message = "Created" });

            // Act
            var result = await _controller.CreateOrUpdate(formTypeViewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldReturnJsonResult_ForUpdate()
        {
            // Arrange
            var formTypeViewModel = new FormTypeViewModel { FormTypeName = "Updated Form Type" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "123"); // Non-empty id for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new UpdateFormTypeCommand();
            _mockMapper.Setup(m => m.Map<UpdateFormTypeCommand>(formTypeViewModel)).Returns(command);
            _mockDataProvider.Setup(dp => dp.FormType.UpdateAsync(command))
                             .ReturnsAsync(new BaseResponse { Success = true, Message = "Updated" });

            // Act
            var result = await _controller.CreateOrUpdate(formTypeViewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task Delete_ShouldReturnJsonResult_WithSuccess()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.FormType.DeleteAsync(It.IsAny<string>()))
                             .ReturnsAsync(new BaseResponse { Success = true, Message = "Deleted" });

            // Act
            var result = await _controller.Delete("1");

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            _mockDataProvider.Verify(dp => dp.FormType.DeleteAsync("1"), Times.Once);
        }

        [Fact]
        public async Task FormTypeNameExist_ShouldReturnTrueIfNameExists()
        {
            
            _mockDataProvider.Setup(dp => dp.FormType.IsFormTypeNameExist("Test Form Type", "1"))
                             .ReturnsAsync(true);

        
            var result = await _controller.FormTypeNameExist("Test Form Type", "1");

          
            Assert.True(result);
        }

        [Fact]
        public async Task GetPagination_ShouldReturnJsonResult_WithFormTypeList()
        {
            var res = new PaginatedResult<FormTypeListVm>();
            var query = new GetFormTypePaginatedListQuery();
            var formTypeList = new { Id = "1", Name = "Test Form Type" };
            _mockDataProvider.Setup(dp => dp.FormType.GetPaginatedFormTypes(query)).ReturnsAsync(res);

            var result = await _controller.GetPagination(query);          
        
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.True((bool)jsonResult.Value.GetType().GetProperty("success").GetValue(jsonResult.Value, null));
          
        }

        [Fact]
        public async Task GetFormTypeNames_ShouldReturnJsonResult_WithFormTypeNames()
        {
            // Arrange
            var form = new List<FormTypeNameVm>();
            _mockDataProvider.Setup(dp => dp.FormType.GetFormTypeNames()).ReturnsAsync(form);

            // Act
            var result = await _controller.GetFormTypeNames();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
        }

        // Exception handling tests
        [Fact]
        public async Task CreateOrUpdate_Returns_Json_With_Error_When_ValidationException_Occurs_Create()
        {
            // Arrange
            var formTypeViewModel = new FormTypeViewModel { FormTypeName = "Test Form" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty id for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new CreateFormTypeCommand();
            _mockMapper.Setup(m => m.Map<CreateFormTypeCommand>(formTypeViewModel)).Returns(command);

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Field", "Validation error occurred"));
            var validationException = new ValidationException(validationResult);
            _mockDataProvider.Setup(dp => dp.FormType.CreateAsync(command)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(formTypeViewModel);

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task CreateOrUpdate_Returns_Json_With_Error_When_Exception_Occurs_Create()
        {
            // Arrange
            var formTypeViewModel = new FormTypeViewModel { FormTypeName = "Test Form" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty id for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new CreateFormTypeCommand();
            _mockMapper.Setup(m => m.Map<CreateFormTypeCommand>(formTypeViewModel)).Returns(command);
            _mockDataProvider.Setup(dp => dp.FormType.CreateAsync(command)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(formTypeViewModel);

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task CreateOrUpdate_Returns_Json_With_Error_When_ValidationException_Occurs_Update()
        {
            // Arrange
            var formTypeViewModel = new FormTypeViewModel { FormTypeName = "Test Form" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "123"); // Non-empty id for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new UpdateFormTypeCommand();
            _mockMapper.Setup(m => m.Map<UpdateFormTypeCommand>(formTypeViewModel)).Returns(command);

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Field", "Validation error occurred"));
            var validationException = new ValidationException(validationResult);
            _mockDataProvider.Setup(dp => dp.FormType.UpdateAsync(command)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(formTypeViewModel);

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task CreateOrUpdate_Returns_Json_With_Error_When_Exception_Occurs_Update()
        {
            // Arrange
            var formTypeViewModel = new FormTypeViewModel { FormTypeName = "Test Form" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "123"); // Non-empty id for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new UpdateFormTypeCommand();
            _mockMapper.Setup(m => m.Map<UpdateFormTypeCommand>(formTypeViewModel)).Returns(command);
            _mockDataProvider.Setup(dp => dp.FormType.UpdateAsync(command)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(formTypeViewModel);

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task Delete_Returns_Json_With_Error_When_Exception_Occurs()
        {
            // Arrange
            var id = "testId";
            _mockDataProvider.Setup(dp => dp.FormType.DeleteAsync(id)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Delete(id);

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task FormTypeNameExist_Returns_False_When_Exception_Occurs()
        {
            // Arrange
            var formTypeName = "testName";
            var id = "testId";
            _mockDataProvider.Setup(dp => dp.FormType.IsFormTypeNameExist(formTypeName, id)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.FormTypeNameExist(formTypeName, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetPagination_Returns_Json_With_Error_When_Exception_Occurs()
        {
            // Arrange
            var query = new GetFormTypePaginatedListQuery();
            _mockDataProvider.Setup(dp => dp.FormType.GetPaginatedFormTypes(query)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetFormTypeNames_Returns_Json_With_Error_When_Exception_Occurs()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.FormType.GetFormTypeNames()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetFormTypeNames();

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }
    }
}

