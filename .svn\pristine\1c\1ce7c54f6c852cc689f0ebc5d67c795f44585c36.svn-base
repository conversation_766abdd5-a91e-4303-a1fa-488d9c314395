﻿//using ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Create;
//using ContinuityPatrol.Application.UnitTests.Fixtures;
//using ContinuityPatrol.Application.UnitTests.Mocks;

//namespace ContinuityPatrol.Application.UnitTests.Features.HeatMapStatus.Commands;

//public class CreateHeatMapStatusTests : IClassFixture<HeatMapStatusFixture>
//{
//    private readonly HeatMapStatusFixture _heatMapStatusFixture;

//    private readonly Mock<IHeatMapStatusRepository> _mockHeatMapStatusRepository;

//    private readonly CreateHeatMapStatusCommandHandler _handler;

//    public CreateHeatMapStatusTests(HeatMapStatusFixture heatMapStatusFixture)
//    {
//        _heatMapStatusFixture = heatMapStatusFixture;
    
//        _mockHeatMapStatusRepository = HeatMapStatusRepositoryMocks.CreateHeatMapStatusRepository(_heatMapStatusFixture.HeatMapStatusList);
        
//        _handler = new CreateHeatMapStatusCommandHandler(_heatMapStatusFixture.Mapper, _mockHeatMapStatusRepository.Object);
//    }

//    [Fact]
//    public async Task Handle_IncreaseHeatMapStatusCount_When_HeatMapStatusCreated()
//    {
//        await _handler.Handle(_heatMapStatusFixture.CreateHeatMapStatusCommand, CancellationToken.None);

//        var result = await _mockHeatMapStatusRepository.Object.ListAllAsync();

//        result.Count.ShouldBe(_heatMapStatusFixture.HeatMapStatusList.Count);
//    }

//    [Fact]
//    public async Task Handle_Return_CreateHeatMapStatusResponse_When_HeatMapStatusCreated()
//    {
//        var result = await _handler.Handle(_heatMapStatusFixture.CreateHeatMapStatusCommand, CancellationToken.None);

//        result.ShouldBeOfType(typeof(CreateHeatMapStatusResponse));

//        result.Id.ShouldBeGreaterThan(0.ToString());

//        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
//    }

//    [Fact]
//    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
//    {
//        await _handler.Handle(_heatMapStatusFixture.CreateHeatMapStatusCommand, CancellationToken.None);

//        _mockHeatMapStatusRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.HeatMapStatus>()), Times.Once);
//    }
//}