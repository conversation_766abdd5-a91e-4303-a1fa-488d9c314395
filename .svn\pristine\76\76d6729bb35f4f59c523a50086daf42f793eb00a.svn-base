﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class TemplateRepository : BaseRepository<Template>, ITemplateRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public TemplateRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<Template>> ListAllAsync()
    {
        var templateList = await ToFilterTemplate(MapTemplate(base.QueryAll(temp =>
      temp.CompanyId.Equals(_loggedInUserService.CompanyId)))).ToListAsync();

        return templateList;

    }

    //public override Task<Template> GetByReferenceIdAsync(string id)
    //{
    //    return _loggedInUserService.IsParent
    //        ? base.GetByReferenceIdAsync(id)
    //        : Task.FromResult(FindByFilterAsync(template => template.ReferenceId.Equals(id) && template.CompanyId.Equals(_loggedInUserService.CompanyId)).Result.SingleOrDefault());
    //}
    public override async Task<PaginatedResult<Template>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<Template> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await (_loggedInUserService.IsParent
            ? ToFilterTemplate( Entities.Specify(productFilterSpec).DescOrderById())
            : ToFilterTemplate(Entities.Specify(productFilterSpec).Where(x=>x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .DescOrderById())).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public override IQueryable<Template> GetPaginatedQuery()
    {
        var templates = base.QueryAll(temp =>
            temp.CompanyId.Equals(_loggedInUserService.CompanyId));

        var templateList = MapTemplate(templates);

        return templateList;
    }

    public Task<List<Template>> GetTemplateNames()
    {
        if (!_loggedInUserService.IsParent)
            return _dbContext.Templates.Active()
                .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new Template { ReferenceId = x.ReferenceId, Name = x.Name })
                .OrderBy(x => x.Name)
                .ToListAsync();
        return _dbContext.Templates
            .Active()
            .Select(x => new Template { ReferenceId = x.ReferenceId, Name = x.Name })
            .OrderBy(x => x.Name)
            .ToListAsync();
    }

    public Task<bool> IsTemplateNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.Templates.Any(e => e.Name.Equals(name)))
            : Task.FromResult(_dbContext.Templates.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsTemplateNameUnique(string name)
    {
        var matches = _dbContext.Templates.Any(e => e.Name.Equals(name));

        return Task.FromResult(matches);
    }

    public async Task<Template> GetTemplateByReplicationTypeIdAndActionType(string replicationTypeId, string actionType)
    {
        var template = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.ReplicationTypeId.Equals(replicationTypeId) && x.ActionType.Trim().ToLower().Equals(actionType.Trim().ToLower()))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.ReplicationTypeId.Equals(replicationTypeId) &&
                x.ActionType.Trim().ToLower().Equals(actionType.Trim().ToLower()));

        var templateList = MapTemplate(template);

        return await templateList.FirstOrDefaultAsync();
    }

    public async Task<List<Template>> GetTemplateByReplicationTypeId(string replicationTypeId)
    {
      return await ToFilterTemplate(MapTemplate(_loggedInUserService.IsParent
          ? base.FilterBy(x => x.ReplicationTypeId.Equals(replicationTypeId))
          : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.ReplicationTypeId.Equals(replicationTypeId)))).OrderByDescending(x => x.Id).ToListAsync();

    }

    public Task<bool> IsTemplateByReplicationTypeIdAndActionTypeNameUnique(string replicationTypeId, string actionType)
    {
        var matches = _dbContext.Templates.Any(e =>
            e.ActionType.Equals(actionType) && e.ReplicationTypeId.Equals(replicationTypeId));

        return Task.FromResult(matches);
    }

    private IQueryable<Template> MapTemplate(IQueryable<Template> templates)
    {
        return templates.Select(x => new
        {
            Template = x,
            ComponentType = _dbContext.ComponentTypes.Active().AsNoTracking().Select(comp=>new ComponentType { ReferenceId=comp.ReferenceId,ComponentName=comp.ComponentName}).FirstOrDefault(y => y.ReferenceId.Equals(x.ReplicationTypeId)),
        })
        .Select(res => new Template
        {
            Id = res.Template.Id,
            ReferenceId = res.Template.ReferenceId,
            Name = res.Template.Name,
            Description = res.Template.Description,
            CompanyId = res.Template.CompanyId,
            Type = res.Template.Type,
            ActionType = res.Template.ActionType,
            Version = res.Template.Version,
            SubTypeId = res.Template.SubTypeId,
            SubTypeName = res.Template.SubTypeName,
            ReplicationCategoryTypeId =  res.Template.ReplicationCategoryTypeId,
            ReplicationCategoryTypeName = res.Template.ReplicationCategoryTypeName,
            Icon = res.Template.Icon,
            Properties = res.Template.Properties,
            ReplicationTypeId = res.ComponentType.ReferenceId ?? res.Template.ReplicationTypeId,
            ReplicationTypeName = res.ComponentType.ComponentName ?? res.Template.ReplicationTypeName,
            IsActive = res.Template.IsActive,
            CreatedBy = res.Template.CreatedBy,
            CreatedDate = res.Template.CreatedDate,
            LastModifiedBy = res.Template.LastModifiedBy,
            LastModifiedDate = res.Template.LastModifiedDate
        });
    }

    private IQueryable<Template> ToFilterTemplate(IQueryable<Template> query)
    {
        return query.Select(t => new Template
        {
            Id = t.Id,
            ReferenceId = t.ReferenceId,
            Name = t.Name,
            Description = t.Description,
            CompanyId = t.CompanyId,
            Type = t.Type,
            ActionType = t.ActionType,
            Version = t.Version,
            SubTypeId = t.SubTypeId,
            SubTypeName = t.SubTypeName,
            ReplicationCategoryTypeId = t.ReplicationCategoryTypeId,
            ReplicationCategoryTypeName = t.ReplicationCategoryTypeName,
            Icon = t.Icon,
            Properties = t.Properties,
            ReplicationTypeId = t.ReplicationTypeId,
            ReplicationTypeName = t.ReplicationTypeName
        });
    }
}