﻿/*let siteId = '';*/
let SiteValue = [], globalIsDelete = true;

const siteTypeURL = {
    SiteTypePaginatedUrl: "/Configuration/SiteType/GetPagination",
    SiteTypeExistUrl : 'Configuration/SiteType/IsSiteTypeExist'
}

const errorElements = ['#type-error', '#Site-error', '#Icon-error'];

$(function () {
    let selectedValues = [];
    let createPermission = $("#ConfigurationCreate").data("create-permission").toLowerCase();
    let deletePermission = $("#ConfigurationDelete").data("delete-permission").toLowerCase();
    if (createPermission == 'false') {
        $("#SiteType-CreateButton").removeClass('#SiteType-CreateButton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }
    let dataTable = $('#tblSiteType').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }
                , infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": siteTypeURL.SiteTypePaginatedUrl,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "type" : sortIndex === 2 ? "category"  :
                        sortIndex === 3 ? "status" : "";
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                                       
                         
                    SiteValue.push(json?.data); 

                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data;
                   
                }
            },
            "columnDefs": [
                {
                    "targets": [1],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false
                },
                {
                    "data": "type",
                    "name": "name",
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            let id = row.id;
                            let icon = row.icon.split(' ');

                            let iconMap = {
                                "1738f813-6090-40cc-8869-25741a156f73": "cp-prsites",
                                "2914c9b2-91d3-4e03-baed-77824f16327c": "cp-physical-drsite",
                                "0e3ecd95-ca88-42be-b6f3-01b68147c346": "cp-physical-neardrsite"
                            };

                            let iconClass = iconMap[id] || icon[0];

                            return `<span title="${data}"><i class="${iconClass}"></i>${data}</span>`;
                        }
                        return data;
                    }
                }, {
                    "data": "category", "name": "type", "autoWidth": true,

                    "render": function (data, type, row) {
                 
                        if (type === 'display') {
                            return `<td><span > ${row.category || 'NA'}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        
                        if (createPermission === 'true' && deletePermission === "true") {
                            if (row.isDelete) {
                                if (row.category === 'Primary' || row.category === 'DR') {
                                   
                                    return `
                                    <div class="d-flex align-items-center gap-2">
                                       <span role="button" title="Edit" class="edit-button" data-site-type='${JSON.stringify(row)}'>
                                          <i class="cp-edit"></i>
                                       </span>
                                            <span role="button" title="Delete" class="icon-disabled">
                                              <i class="cp-Delete"></i>
                                            </span>
                                    </div>`;
                                } else {
                                   
                                    return `
                                     <div class="d-flex align-items-center gap-2">
                                       <span role="button" title="Edit" class="edit-button" data-site-type='${JSON.stringify(row)}'>
                                           <i class="cp-edit"></i>
                                       </span>
                                       <span role="button" title="Delete" class="delete-button" data-bs-toggle="modal" data-bs-target="#DeleteModal" data-site-type-id="${row.id}" data-site-type-name="${row.type}">
                                           <i class="cp-Delete"></i>
                                       </span>
                                     </div>`;
                                }
                            }
                            else {
                                return `<div class="d-flex align-items-center gap-2">
                                  
                                             <span role="button" title="Edit" class="edit-button" data-site-type='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                     
                                            <span role="button" title="Delete"  class="delete-button icon-enabled">
                                                    <i class="cp-Delete"  data-bs-toggle="modal" data-bs-target="#DeleteModal" data-site-type-id="${row.id}" data-site-type-name="${row.type}"></i>
                                            </span>
                                     
                                </div>`;
                            }
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                                <div class="d-flex align-items-center gap-2">
                              
                                            <span role="button" title="Edit" class="edit-button" data-site-type='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                      
                                            <span role="button" title="Delete" class="icon-disabled">
                                                <i class="cp-Delete"></i>
                                            </span>
                                    
                                    </ul>
                                </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                                <div class="d-flex align-items-center gap-2">
                              
                                             <span role="button" title="Edit" class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                      
                                            <span role="button" title="Delete" class="delete-button" data-bs-toggle="modal" data-bs-target="#DeleteModal" data-site-type-id="${row.id}" data-site-type-name="${row.type}">
                                                <i class="cp-Delete"></i>
                                            </span>
                                    
                                    </ul>
                                </div>`;
                        }
                        else {
                            return `
                                <div class="d-flex align-items-center gap-2">
                              
                                             <span role="button" title="Edit" class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                              <span role="button" title="Delete" class="icon-disabled">
                                                <i class="cp-Delete"></i>
                                            </span>
                                    </ul>
                                </div>`;
                        }
                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });
    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const NameCheckbox = $("#Name");
        const TypeCheckbox = $("#Type");
        const inputValue = $('#search-inp').val();
        if (NameCheckbox.is(':checked')) {
            selectedValues.push(NameCheckbox.val() + inputValue);
        }
        if (TypeCheckbox.is(':checked')) {
            selectedValues.push(TypeCheckbox.val() + inputValue);
        }
        dataTable.ajax.reload(function (json) {

            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    },500))


    // Edit
    $('#tblSiteType').on('click', '.edit-button', function () {
        const siteTypeData = $(this).data('site-type');
        updateSiteType(siteTypeData);
        $('#SaveFunction').text('Update');
        $('#staticBackdrop').modal('show');
    });

    //Delete
    $('#tblSiteType').on('click', '.delete-button', function () {
        const siteTypeId = $(this).data('site-type-id');
        const siteTypeName = $(this).data('site-type-name');
        $('#deleteData').attr('title', siteTypeName).text(siteTypeName);
        $('#textDeleteId').val(siteTypeId);
    });

    $('#textSiteType').on('keyup', commonDebounce(async function () {
        let siteId = $('#textSiteTypeId').val();
        const value = $(this).val();
        const sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        $('#hidnIcon').val('cp-custom-server-4');
        await validateType(sanitizedValue, siteId, IsNameExist);
    },400));


    $('#Site').on('change',function () {       
        const value = $(this).val();
        validateDropDown(value, ' Select type', 'Site-error');
    })

 

    $("#CreateForm").on('keypress', function (e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
    });

    $("#SaveFunction").on('click', async function () {
        $('#deleteSiteStatus').val(globalIsDelete)
        let form = $("#CreateForm");
        let name = $("#textSiteType").val();
        let site = $("#Site").val();
        let siteId = $('#textSiteTypeId').val();
        let issite = validateDropDown(site, 'Select type', 'Site-error')
        let IsType = await validateType(name, siteId);
        let SiteTypeSanitizeArray = ['textSiteType', 'Site', 'deleteSiteStatus', 'textSiteTypeId', 'hidnIcon']
        sanitizeContainer(SiteTypeSanitizeArray)
        setTimeout(() => {
            if (IsType && issite) {
                $('#Site').prop('disabled', false)
                form.trigger('submit');
            }
        }, 200) 
        
    });

    function updateSiteType(siteTypeData) {
     
        globalIsDelete = siteTypeData.isDelete
      /*  siteId = siteTypeData.id*/
        $('#textSiteTypeId').val(siteTypeData.id);
        $('#textSiteType').val(siteTypeData.type);
        $('#Site').val(siteTypeData.category);
        if (siteTypeData.category?.toLowerCase() === 'primary') {
            $('#Site').prop('disabled', true)
        } else if (siteTypeData.category?.toLowerCase() === 'dr') {
            $('#Site').prop('disabled', true)
        }
        else {
            $('#Site').prop('disabled', false)
            $('#Site option[value="Primary"], #Site option[value="DR"]').prop('disabled', true);
        }
        $('#hidnIcon').val(siteTypeData.icon);
        let errorElement = ['#type-error', '#Site-error']
        errorElement.forEach(element => {
            $(element).text('').removeClass('field-validation-error')
        });
    }

    $('#SiteType-CreateButton').on('click', function () {
        $('#textSiteTypeId').val('');
        if (Array.isArray(SiteValue)) {
            SiteValue.forEach(innerArray => {

                if (Array.isArray(innerArray)) {
                    innerArray.forEach(item => {

                        if (item.category === 'Primary') {

                            $('#Site option[value="Primary"]').prop('disabled', true);

                            // $('#Site option[value="Custom DR"]').prop('disabled', true);
                        } else if (item.category === 'DR') {
                            $('#Site option[value="DR"]').prop('disabled', true);
                        } else {
                            $('#Site option[value="Custom DR"]').prop('disabled', false);
                        }

                    });
                }
            });
        }
        else {
            $$('#Site option[value="Primary"], #Site option[value="DR"], #Site option[value="Custom DR"]').prop('disabled', false);
        }
        clearInputFields();
        $("#prIcon").prop("checked", true);
    });

    const clearInputFields = () => {
        $('#textSiteType, #Site').val('');
        $('#SaveFunction').text('Save');
        $('.btn-check').prop('checked', false);
        $('#Site').prop('disabled', false)
      /*  siteId = ''*/
        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });
    };
});

async function validateType(value, id = null) {

    const errorElement = $('#type-error');

    if (!value) {
        errorElement.text('Enter site type name')
            .addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    let url = RootUrl + siteTypeURL.SiteTypeExistUrl;
    let data = {};
    data.type = value;
    data.id = id;

    const validationResults = [
         SpecialCharValidate(value),
         ShouldNotBeginWithSpace(value),
         ShouldNotBeginWithUnderScore(value),
         OnlyNumericsValidate(value),
         ShouldNotBeginWithNumber(value),
         ShouldNotEndWithSpace(value),
         ShouldNotAllowMultipleSpace(value),
         SpaceWithUnderScore(value),
         ShouldNotEndWithUnderScore(value),
         MultiUnderScoreRegex(value),
         SpaceAndUnderScoreRegex(value),
         minMaxlength(value),
         secondChar(value),
         await IsNameExist(url, data, OnError)
    ];

    return  CommonValidation(errorElement, validationResults);
}

async function ValidateRadioButton(errorElement) {
    if ($("[name='siteTypeIcon']:checked").length == 0) {
        errorElement.text("Select icon").addClass('field-validation-error');;
        return false;
    }
    else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}

function validateDropDown(value, errorMessage, errorElement) {

    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
}

async function IsNameExist(url, data, errorFunc) {
    return !data.type.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}