﻿using ContinuityPatrol.Application.Features.Server.Commands.Create;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Server.Commands.TestConnection;
using ContinuityPatrol.Application.Features.Server.Commands.Update;
using ContinuityPatrol.Application.Features.Server.Commands.UpdateVersion;
using ContinuityPatrol.Application.Features.Server.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Server.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Server.Queries.GetRoleType;
using ContinuityPatrol.Domain.ViewModels.ServerModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using Newtonsoft.Json;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class ServerController : BaseController
{
    private readonly IPublisher _publisher;
    public static ILogger<ServerController> _logger;
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;
    public static string CompanyLogo { get; set; }
    public ServerController(IPublisher publisher, ILogger<ServerController> logger, IDataProvider dataProvider, IMapper mapper)
    {
        _publisher = publisher;
        _logger = logger;
        _dataProvider = dataProvider;
        _mapper = mapper;
    }

    [AntiXss]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in Server");
        await _publisher.Publish(new ServerPaginatedEvent());
        return View();
    }

    public async Task<bool> IsServerNameExist(string serverName, string id)
    {
        _logger.LogDebug("Entering IsServerNameExist method in Server");
        try
        {
            _logger.LogDebug("Returning result for IsServerNameExist on server");
            return await _dataProvider.Server.IsServerNameExist(serverName, id);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on server page while checking if server name exists for : {serverName}.", ex);
            return false;
        }
    }

    public async Task<IActionResult> GetServerListData()
    {
        _logger.LogDebug("Entering GetServerListData method in Server");
        try
        {
            var serverList = await _dataProvider.Server.GetByServerOsType("");
            _logger.LogDebug("Successfully retrieved server list in Server");
            return Json(new { success = true, data = serverList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while retrieving server list.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetServerRole()
    {
        _logger.LogDebug("Entering GetServerRole method in Server");
        try
        {
            var serverRole = await _dataProvider.ServerType.GetServerTypeList();
            _logger.LogDebug("Successfully retrieved server role in Server");
            return Json(new { success = true, data = serverRole });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while retrieving server role.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetServerType(string id)
    {
        _logger.LogDebug("Entering GetServerType method in Server");
        try
        {
            var serverType = await _dataProvider.ServerSubType.GetServerSubTypeByTypeId(id);
            _logger.LogDebug("Successfully retrieved server subtype in Server");
            return Json(new { success = true, data = serverType });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while retrieving the server subtype.", ex);
            return ex.GetJsonException();
        }
    }

    //For Database.js
    public async Task<JsonResult> DatabaseServerNameList()
    {
        _logger.LogDebug("Entering DatabaseServerNameList method in Server");
        try
        {
            var nameList = await _dataProvider.Server.GetServerNames();
            var serverNameList = nameList.Where(x => x?.RoleType?.ToLower() == "database").ToList();
            _logger.LogDebug("Successfully retrieved database server names in Server");
            return Json(new { success = true, data = serverNameList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while retrieving database server names.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetSiteNames()
    {
        _logger.LogDebug("Entering GetSiteNames method in Server");
        try
        {
            var serverNames = await _dataProvider.Site.GetSiteNames();
            _logger.LogDebug("Successfully retrieved site names in Server");
            return Json(new { success = true, data = serverNames });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while retrieving site names.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Configuration.CreateAndEdit)]
    public async Task<IActionResult> CreateOrUpdate(ServerViewModel server)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in Server");
        var serverId = Request.Form["id"].ToString();
        try
        {
            server.Properties = server.Properties.IsNullOrWhiteSpace() ? server.Properties : SecurityHelper.Decrypt(server.Properties);
            if (serverId.IsNullOrWhiteSpace())
            {
                var formModel = _mapper.Map<CreateServerCommand>(server);
                _logger.LogDebug($"Creating Server '{formModel.Name}'");
                var response = await _dataProvider.Server.CreateAsync(formModel);
                _logger.LogDebug("Create operation completed successfully in Server, returning view.");
                return Json(new { success = true, data = response });
            }
            else
            {
                var formModel = _mapper.Map<UpdateServerCommand>(server);
                _logger.LogDebug($"Updating Server '{formModel.Name}'");
                var response = await _dataProvider.Server.UpdateAsync(formModel);
                _logger.LogDebug("Update operation completed successfully in Server, returning view.");
                return Json(new { success = true, data = response });
            }
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on server page: {ex.ValidationErrors.FirstOrDefault()}");
            return ex.GetJsonException();
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while processing the request for create or update.", ex);
            return ex.GetJsonException();
        }
    }

    //[HttpGet]
    //public async Task<JsonResult> GetOSType(string type)
    //{
    //    var osType = await _dataProvider.Server.GetByType(type);
    //    return Json(osType);
    //}    

    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in Server");
        try
        {
            var response = await _dataProvider.Server.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted record in server");
            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on server.", ex);
            return ex.GetJsonException();
        }
    }

    public async Task<List<ServerRoleTypeVm>> GetServerList(string roleType, string serverType)
    {
        _logger.LogDebug("Entering GetServerList method in Server");
        try
        {
            var serverByRoleAndServer = await _dataProvider.Server.GetByRoleTypeAndServerType(roleType, serverType);
            _logger.LogDebug($"Successfully retrieved server list by roleType '{roleType}' and serverType '{serverType}' in Server page");
            return serverByRoleAndServer;
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while retrieving server list by roleType and serverType", ex);
            return new List<ServerRoleTypeVm>();
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetServerNames(string roleType, string serverType)
    {
        _logger.LogDebug("Entering GetServerNames method in Server");
        try
        {
            var serverByRoleAndServer = await _dataProvider.Server.GetByRoleTypeAndServerType(roleType, serverType);
            _logger.LogDebug($"Successfully retrieved server names by roleType '{roleType}' and serverType '{serverType}' in Server page");
            return Json(new { success = true, data = serverByRoleAndServer });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while retrieving server names by roleType and serverType", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> UpdateServerFormVersion(UpdateServerVersionCommand updateServerVersionCommand)
    {
        _logger.LogDebug("Entering UpdateServerFormVersion method in Server");
        try
        {
            var updateVersion = await _dataProvider.Server.UpdateServerFormVersion(updateServerVersionCommand);
            return Json(new { success = true, data = updateVersion });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while updating the server form version.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public String ServerDataEncrypt(string data)
    {
        _logger.LogDebug("Entering ServerDataEncrypt method in Server");
        try
        {
            var serverDataEncrypt = SecurityHelper.Encrypt(data);
            _logger.LogDebug("Successfully encrypted server data in Server");
            return serverDataEncrypt;
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while encrypting server data", ex);
            return null;
        }
    }

    public String ServerDataDecrypt(string data)
    {
        _logger.LogDebug("Entering ServerDataDecrypt method in Server");
        try
        {
            var serverDataDecrypt = SecurityHelper.Decrypt(data);
            _logger.LogDebug("Successfully decrypted server data in Server");
            return serverDataDecrypt;
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while decrypting server data", ex);
            return null;
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> ServerTestConnection(ServerTestConnectionCommand command)
    {
        _logger.LogDebug("Entering ServerTestConnection method in Server");
        try
        {
            var serverTestConnection = await _dataProvider.Server.ServerTestConnection(command);
            _logger.LogDebug($"Successfully tested server test connection for serverId '{command.Id}' in Server page");
            if (serverTestConnection.Success)
            {
                return Json(new { success = true, data = serverTestConnection });
            }
            else
            {
                //if license expired
                return Json(new { success = false, message = serverTestConnection.Message });
            }

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while processing the server test connection request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetPagination(GetServerPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in Server");
        try
        {
            var serverList = await _dataProvider.Server.GetPaginatedServers(query);
            _logger.LogDebug("Successfully retrieved server paginated list on server page");
            return Json(new { success = true, data = serverList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }
    }

    [AllowAnonymous]
    [HttpGet]
    public JsonResult HashPassword(string password)
    {
        _logger.LogDebug("Entering HashPassword method in Server");
        try
        {
            var encryptString = SecurityHelper.Encrypt($"{password}");
            _logger.LogDebug("Successfully encrypted password in Server page");
            return Json(new { encrypt = encryptString });
        }
        catch (Exception ex)
        {
            _logger.Exception("An occurred on server page while processing encrypting the password", ex);
            return Json("");
        }
    }

    [AllowAnonymous]
    [HttpGet]
    public JsonResult HashPasswordDecrypt(string password)
    {
        _logger.LogDebug("Entering HashPasswordDecrypt method in Server");
        try
        {
            var decryptString = SecurityHelper.Decrypt($"{password}");
            _logger.LogDebug("Successfully decrypted password in Server page");
            return Json(new { decrypt = decryptString });
        }
        catch (Exception ex)
        {
            _logger.Exception("An occurred on server page while processing decrypting the password", ex);
            return Json("");
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetByReferenceId(string id)
    {
        _logger.LogDebug("Entering GetByReferenceId method in Server");
        try
        {
            var getByReferenceId = await _dataProvider.Server.GetByReferenceId(id);
            _logger.LogDebug($"Successfully retrieved server details by id '{id}' in Server");
            return Json(new { success = true, data = getByReferenceId });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while retrieving server detail by id.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetServerNamesForSaveAs()
    {
        _logger.LogDebug("Entering GetServerNamesForSaveAs method in Server");
        try
        {
            var serverList = await _dataProvider.Server.GetServerNames();
            _logger.LogDebug("Successfully retrieved server name lists on server page");
            return Json(new { success = true, data = serverList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while processing the server names request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> SaveAllServer(SaveAllServerCommand command)
    {
        _logger.LogDebug("Entering SaveAllServer method in Server");
        try
        {
            var result = await _dataProvider.Server.SaveAllServer(command);
            _logger.LogDebug($"Successfully inserted servers");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while inserting servers", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> SaveAsServer(SaveAsServerCommand saveAsServerCommand)
    {
        _logger.LogDebug("Entering SaveAsServer method in Server");
        try
        {
            var result = await _dataProvider.Server.SaveAsServer(saveAsServerCommand);
            _logger.LogDebug($"Successfully inserted server");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on server page while inserting server", ex);
            return ex.GetJsonException();
        }
    }

    [SupportedOSPlatform("windows")]
    public async Task<IActionResult> LoadReport(string type, string selectedTypeId, string searchString)
    {
        var reportsDirectory = "";
        try
        {
            CompanyLogo = string.Empty;
            selectedTypeId = selectedTypeId.Equals("All") ? null : selectedTypeId;
            var companyId = WebHelper.UserSession.CompanyId;
            var companyDetails = await _dataProvider.Company.GetCompanyById(WebHelper.UserSession.CompanyId);
            if (!string.IsNullOrEmpty(companyDetails.CompanyLogo) && companyDetails.CompanyLogo != "NA") { CompanyLogo = companyDetails.CompanyLogo.ToString(); }
            GetServerPaginatedListQuery getServerPaginatedList = new GetServerPaginatedListQuery();
            getServerPaginatedList.OSTypeId = selectedTypeId;
            getServerPaginatedList.SearchString = searchString;
            var serverList = await _dataProvider.Server.GetPaginatedServers(getServerPaginatedList);
            //  var serverList = await _dataProvider.Server.GetServerList();
            var reportValue = JsonConvert.SerializeObject(serverList.Data);
            XtraReport report = new Report.ReportTemplate.ServerComponentReport(reportValue);
            var filenameSuffix = DateTime.Now.ToString("MMddyyyyhhmmsstt");
            if (type.Equals("pdf"))
            {
                var fileName = "ServerComponentReport_" + filenameSuffix + ".pdf";
                reportsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName);
                await report.ExportToPdfAsync(reportsDirectory);
                byte[] fileBytes = System.IO.File.ReadAllBytes(reportsDirectory);
                return File(fileBytes, "application/pdf", fileName);
            }
            else
            {
                var fileName = "ServerComponentReport_" + filenameSuffix + ".xls";
                reportsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName);
                await report.ExportToXlsAsync(reportsDirectory);
                byte[] fileBytes = System.IO.File.ReadAllBytes(reportsDirectory);
                return File(fileBytes, "application/vnd.ms-excel", fileName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred: {ex.Message}");
            return Content("An error occurred while generating the report.");
        }
        finally
        {
            if (System.IO.File.Exists(reportsDirectory))
            {
                System.IO.File.Delete(reportsDirectory);
            }
        }
    }
}
