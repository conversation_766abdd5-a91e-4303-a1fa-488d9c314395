﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ServerLog.Events.Update;

public class ServerLogUpdatedEventHandler : INotificationHandler<ServerLogUpdatedEvent>
{
    private readonly ILogger<ServerLogUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ServerLogUpdatedEventHandler(ILogger<ServerLogUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(ServerLogUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {

        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            CompanyId = _userService.CompanyId,
            Entity = Modules.ServerLog.ToString(),
            Action = $"{ActivityType.Update} {Modules.ServerLog}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $" ServerLog '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"ServerLog '{updatedEvent.Name}' updated successfully.");

    }
}