﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class NodeConfigurationRepositoryMocks 
{
    public static Mock<ILoadBalancerRepository> CreateNodeConfigurationRepository(List<LoadBalancer> nodeConfigurations)
    {
        var mockNodeConfigurationRepository = new Mock<ILoadBalancerRepository>();

        mockNodeConfigurationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(nodeConfigurations);

        mockNodeConfigurationRepository.Setup(repo => repo.AddAsync(It.IsAny<LoadBalancer>())).ReturnsAsync(
            (LoadBalancer nodeConfiguration) =>
            {
                nodeConfiguration.Id = new Fixture().Create<int>();

                nodeConfiguration.ReferenceId = new Fixture().Create<Guid>().ToString();

                nodeConfigurations.Add(nodeConfiguration);

                return nodeConfiguration;
            });

        return mockNodeConfigurationRepository;
    }

    public static Mock<ILoadBalancerRepository> UpdateNodeConfigurationRepository(List<LoadBalancer> nodeConfigurations)
    {
        var mockNodeConfigurationRepository = new Mock<ILoadBalancerRepository>();

        mockNodeConfigurationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(nodeConfigurations);

        mockNodeConfigurationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => nodeConfigurations.SingleOrDefault(x => x.ReferenceId == i));

        mockNodeConfigurationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<LoadBalancer>())).ReturnsAsync((LoadBalancer nodeConfiguration) =>
        {
            var index = nodeConfigurations.FindIndex(item => item.Id == nodeConfiguration.Id);

            nodeConfigurations[index] = nodeConfiguration;

            return nodeConfiguration;
        });

        return mockNodeConfigurationRepository;
    }

    public static Mock<ILoadBalancerRepository> DeleteNodeConfigurationRepository(List<LoadBalancer> nodeConfigurations)
    {
        var mockNodeConfigurationRepository = new Mock<ILoadBalancerRepository>();

        mockNodeConfigurationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(nodeConfigurations);

        mockNodeConfigurationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => nodeConfigurations.SingleOrDefault(x => x.ReferenceId == i));

        mockNodeConfigurationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<LoadBalancer>())).ReturnsAsync((LoadBalancer nodeConfiguration) =>
        {
            var index = nodeConfigurations.FindIndex(item => item.Id == nodeConfiguration.Id);

            nodeConfiguration.IsActive = false;

            nodeConfigurations[index] = nodeConfiguration;

            return nodeConfiguration;
        });

        return mockNodeConfigurationRepository;
    }

    public static Mock<ILoadBalancerRepository> GetNodeConfigurationRepository(List<LoadBalancer> nodeConfigurations)
    {
        var mockNodeConfigurationRepository = new Mock<ILoadBalancerRepository>();

        mockNodeConfigurationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(nodeConfigurations);

        mockNodeConfigurationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => nodeConfigurations.SingleOrDefault(x => x.ReferenceId == i));

        return mockNodeConfigurationRepository;
    }

    public static Mock<ILoadBalancerRepository> GetNodeConfigurationEmptyRepository()
    {
        var mockNodeConfigurationRepository = new Mock<ILoadBalancerRepository>();

        mockNodeConfigurationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<LoadBalancer>());

        return mockNodeConfigurationRepository;
    }

    public static Mock<ILoadBalancerRepository> GetNodeConfigurationNameUniqueRepository(List<LoadBalancer> nodeConfigurations)
    {
        var mockNodeConfigurationRepository = new Mock<ILoadBalancerRepository>();

        mockNodeConfigurationRepository.Setup(repo => repo.IsNodeConfigurationNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) =>
        {
            return j == 0.ToString() ? nodeConfigurations.Exists(x => x.Name == i) : nodeConfigurations.Exists(x => x.Name == i && x.ReferenceId == j);
        });

        return mockNodeConfigurationRepository;
    }

    public static Mock<ILoadBalancerRepository> GetPaginatedNodeConfigurationRepository(List<LoadBalancer> nodeConfigurations)
    {
        var mockNodeConfigurationRepository = new Mock<ILoadBalancerRepository>();

        var queryableNodeConfiguration = nodeConfigurations.BuildMock();

        mockNodeConfigurationRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableNodeConfiguration);

        return mockNodeConfigurationRepository;
    }
}