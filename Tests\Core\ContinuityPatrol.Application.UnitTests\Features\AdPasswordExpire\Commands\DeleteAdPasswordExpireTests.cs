using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Delete;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordExpire.Commands;

public class DeleteAdPasswordExpireTests : IClassFixture<AdPasswordExpireFixture>
{
    private readonly AdPasswordExpireFixture _adPasswordExpireFixture;
    private readonly Mock<IAdPasswordExpireRepository> _mockAdPasswordExpireRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly DeleteAdPasswordExpireCommandHandler _handler;

    public DeleteAdPasswordExpireTests(AdPasswordExpireFixture adPasswordExpireFixture)
    {
        _adPasswordExpireFixture = adPasswordExpireFixture;
        _mockAdPasswordExpireRepository = AdPasswordExpireRepositoryMocks.CreateDeleteAdPasswordExpireRepository(_adPasswordExpireFixture.AdPasswordExpires);
        _mockPublisher = new Mock<IPublisher>();

        _handler = new DeleteAdPasswordExpireCommandHandler(
            _mockAdPasswordExpireRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_DeleteAdPasswordExpire_When_ValidCommand()
    {
     
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var command = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<DeleteAdPasswordExpireCommand>()
            .With(x => x.Id, existingExpire.ReferenceId)
            .Create();

        _mockAdPasswordExpireRepository.Setup(x => x.GetByReferenceIdAsync(existingExpire.ReferenceId))
            .ReturnsAsync(existingExpire);

        //// Setup mock for UpdateAsync to return completed task
        //_mockAdPasswordExpireRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.AdPasswordExpire>()))
        //    .Returns(Task.CompletedTask);

        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<DeleteAdPasswordExpireResponse>();
        result.IsActive.ShouldBeFalse();
        result.Message.ShouldContain("deleted successfully");

        _mockAdPasswordExpireRepository.Verify(x => x.GetByReferenceIdAsync(existingExpire.ReferenceId), Times.Once);
        _mockAdPasswordExpireRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.AdPasswordExpire>()), Times.Once);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<AdPasswordExpireDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteActiveAdPasswordExpire_When_ValidCommand()
    {
        // Arrange
        var activeExpire = _adPasswordExpireFixture.AdPasswordExpires[0];
        var command = new DeleteAdPasswordExpireCommand { Id = activeExpire.ReferenceId };

        // Setup mock to return the existing entity
        _mockAdPasswordExpireRepository.Setup(x => x.GetByReferenceIdAsync(activeExpire.ReferenceId))
            .ReturnsAsync(activeExpire);

       

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();
        result.Message.ShouldContain("deleted successfully");

        _mockAdPasswordExpireRepository.Verify(x => x.GetByReferenceIdAsync(activeExpire.ReferenceId), Times.Once);
        _mockAdPasswordExpireRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.AdPasswordExpire>()), Times.Once);
    }

    [Fact]
    public async Task Handle_DeletePasswordExpire_When_ValidPasswordExpire()
    {
        // Arrange
        var passwordExpire = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<Domain.Entities.AdPasswordExpire>()
            .With(x => x.IsPassword, true)
            .With(x => x.UserName, "PasswordUser")
            .With(x => x.NotificationDays, "7,14,30")
            .Create();
        _adPasswordExpireFixture.AdPasswordExpires.Add(passwordExpire);

        var command = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<DeleteAdPasswordExpireCommand>()
            .With(x => x.Id, passwordExpire.ReferenceId)
            .Create();

        // Setup mock to return the password entity
        _mockAdPasswordExpireRepository.Setup(x => x.GetByReferenceIdAsync(passwordExpire.ReferenceId))
            .ReturnsAsync(passwordExpire);

        

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();
        result.Message.ShouldContain("deleted successfully");
    }

    [Fact]
    public async Task Handle_DeleteDomainServerExpire_When_ValidDomainServer()
    {
        // Arrange
        var domainExpire = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<Domain.Entities.AdPasswordExpire>()
            .With(x => x.DomainServerId, "DS002")
            .With(x => x.DomainServer, "TestDomain2.com")
            .With(x => x.UserName, "DomainUser")
            .Create();
        _adPasswordExpireFixture.AdPasswordExpires.Add(domainExpire);

        var command = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<DeleteAdPasswordExpireCommand>()
            .With(x => x.Id, domainExpire.ReferenceId)
            .Create();

        // Setup mock to return the domain entity
        _mockAdPasswordExpireRepository.Setup(x => x.GetByReferenceIdAsync(domainExpire.ReferenceId))
            .ReturnsAsync(domainExpire);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();
        result.Message.ShouldContain("deleted successfully");
    }
    

    [Fact]
    public async Task Handle_SetIsActiveToFalse_When_AdPasswordExpireDeleted()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<Domain.Entities.AdPasswordExpire>()
            .With(x => x.IsActive, true)
            .With(x => x.UserName, "TestUser")
            .Create();
        _adPasswordExpireFixture.AdPasswordExpires.Add(existingExpire);

        var command = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<DeleteAdPasswordExpireCommand>()
            .With(x => x.Id, existingExpire.ReferenceId)
            .Create();

        // Setup mock to ensure the entity is found
        _mockAdPasswordExpireRepository.Setup(x => x.GetByReferenceIdAsync(existingExpire.ReferenceId))
            .ReturnsAsync(_adPasswordExpireFixture.AdPasswordExpires[0]);

       

        // Act
        await _handler.Handle(command, CancellationToken.None);

       
        // Verify the repository methods were called
        _mockAdPasswordExpireRepository.Verify(x => x.GetByReferenceIdAsync(existingExpire.ReferenceId), Times.Once);
        _mockAdPasswordExpireRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.AdPasswordExpire>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AdPasswordExpireNotFound()
    {
        // Arrange
        var nonExistentId = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Create<string>();
        var command = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<DeleteAdPasswordExpireCommand>()
            .With(x => x.Id, nonExistentId)
            .Create();

        _mockAdPasswordExpireRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.AdPasswordExpire)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));

        _mockAdPasswordExpireRepository.Verify(x => x.GetByReferenceIdAsync(nonExistentId), Times.Once);
        _mockAdPasswordExpireRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.AdPasswordExpire>()), Times.Never);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<AdPasswordExpireDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_PublishEventWithCorrectName_When_AdPasswordExpireDeleted()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<Domain.Entities.AdPasswordExpire>()
            .With(x => x.UserName, "EventTestUser")
            .With(x => x.Email, "<EMAIL>")
            .Create();
        _adPasswordExpireFixture.AdPasswordExpires.Add(existingExpire);

        var command = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<DeleteAdPasswordExpireCommand>()
            .With(x => x.Id, existingExpire.ReferenceId)
            .Create();

        // Setup mock to return the existing entity
        _mockAdPasswordExpireRepository.Setup(x => x.GetByReferenceIdAsync(existingExpire.ReferenceId))
            .ReturnsAsync(existingExpire);


        AdPasswordExpireDeletedEvent capturedEvent = null;
        _mockPublisher.Setup(x => x.Publish(It.IsAny<AdPasswordExpireDeletedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<AdPasswordExpireDeletedEvent, CancellationToken>((evt, ct) => capturedEvent = evt)
            .Returns(Task.CompletedTask);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEvent.ShouldNotBeNull();
        capturedEvent.Name.ShouldBe("EventTestUser");

        _mockPublisher.Verify(x => x.Publish(It.IsAny<AdPasswordExpireDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateResponseWithCorrectProperties_When_AdPasswordExpireDeleted()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<Domain.Entities.AdPasswordExpire>()
            .With(x => x.UserName, "ResponseTestUser")
            .With(x => x.Email, "<EMAIL>")
            .With(x => x.DomainServer, "ResponseDomain.com")
            .Create();
        _adPasswordExpireFixture.AdPasswordExpires.Add(existingExpire);

        var command = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<DeleteAdPasswordExpireCommand>()
            .With(x => x.Id, existingExpire.ReferenceId)
            .Create();

        // Setup mock to return the existing entity
        _mockAdPasswordExpireRepository.Setup(x => x.GetByReferenceIdAsync(existingExpire.ReferenceId))
            .ReturnsAsync(existingExpire);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<DeleteAdPasswordExpireResponse>();
        result.Message.ShouldContain("deleted successfully");
        result.Message.ShouldContain("ResponseTestUser");
        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_DeleteServerListExpire_When_ValidServerList()
    {
        // Arrange
        var serverExpire = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<Domain.Entities.AdPasswordExpire>()
            .With(x => x.ServerList, "Server1,Server2,Server3")
            .With(x => x.UserName, "ServerUser")
            .With(x => x.NotificationDays, "1,3,7")
            .Create();
        _adPasswordExpireFixture.AdPasswordExpires.Add(serverExpire);

        var command = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<DeleteAdPasswordExpireCommand>()
            .With(x => x.Id, serverExpire.ReferenceId)
            .Create();

        // Setup mock to return the server entity
        _mockAdPasswordExpireRepository.Setup(x => x.GetByReferenceIdAsync(serverExpire.ReferenceId))
            .ReturnsAsync(serverExpire);

        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();
        result.Message.ShouldContain("deleted successfully");
    }

    [Fact]
    public async Task Handle_DeleteNotificationExpire_When_ValidNotificationDays()
    {
        // Arrange
        var notificationExpire = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<Domain.Entities.AdPasswordExpire>()
            .With(x => x.NotificationDays, "5,10,15,20")
            .With(x => x.UserName, "NotificationUser")
            .With(x => x.IsPassword, false)
            .Create();

        var command = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<DeleteAdPasswordExpireCommand>()
            .With(x => x.Id, notificationExpire.ReferenceId)
            .Create();

        // Setup mock to return the notification entity
        _mockAdPasswordExpireRepository.Setup(x => x.GetByReferenceIdAsync(notificationExpire.ReferenceId))
            .ReturnsAsync(_adPasswordExpireFixture.AdPasswordExpires[0]);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();
        result.Message.ShouldContain("deleted successfully");
    }

    [Fact]
    public async Task Handle_HandleCancellation_When_CancellationRequested()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var command = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<DeleteAdPasswordExpireCommand>()
            .With(x => x.Id, existingExpire.ReferenceId)
            .Create();

        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

    }

    [Fact]
    public async Task Handle_CallRepositoryOnce_When_CommandExecuted()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var command = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<DeleteAdPasswordExpireCommand>()
            .With(x => x.Id, existingExpire.ReferenceId)
            .Create();

        // Setup mock to return the existing entity
        _mockAdPasswordExpireRepository.Setup(x => x.GetByReferenceIdAsync(existingExpire.ReferenceId))
            .ReturnsAsync(existingExpire);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockAdPasswordExpireRepository.Verify(x => x.GetByReferenceIdAsync(existingExpire.ReferenceId), Times.Once);
        _mockAdPasswordExpireRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.AdPasswordExpire>()), Times.Once);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<AdPasswordExpireDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_ValidCommand()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var command = _adPasswordExpireFixture.AutoAdPasswordExpireFixture.Build<DeleteAdPasswordExpireCommand>()
            .With(x => x.Id, existingExpire.ReferenceId)
            .Create();

        // Setup mock to return the existing entity
        _mockAdPasswordExpireRepository.Setup(x => x.GetByReferenceIdAsync(existingExpire.ReferenceId))
            .ReturnsAsync(existingExpire);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<DeleteAdPasswordExpireResponse>();
        result.GetType().Name.ShouldBe("DeleteAdPasswordExpireResponse");
    }
}
