using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DynamicDashboard.Events.Update;

public class DynamicDashboardUpdatedEventHandler : INotificationHandler<DynamicDashboardUpdatedEvent>
{
    private readonly ILogger<DynamicDashboardUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DynamicDashboardUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<DynamicDashboardUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(DynamicDashboardUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} DynamicDashboard",
            Entity = "DynamicDashboard",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"DynamicDashboard '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"DynamicDashboard '{updatedEvent.Name}' updated successfully.");
    }
}