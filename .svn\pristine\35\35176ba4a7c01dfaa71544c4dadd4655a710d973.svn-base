﻿namespace ContinuityPatrol.Application.Features.Job.Queries.GetDetail;

public class GetJobDetailQueryHandler : IRequestHandler<GetJobDetailQuery, JobDetailVm>
{
    private readonly IJobRepository _jobRepository;
    private readonly IMapper _mapper;

    public GetJobDetailQueryHandler(IMapper mapper, IJobRepository jobRepository)
    {
        _mapper = mapper;
        _jobRepository = jobRepository;
    }

    public async Task<JobDetailVm> Handle(GetJobDetailQuery request, CancellationToken cancellationToken)
    {
        var job = await _jobRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(job, nameof(Domain.Entities.Job),
            new NotFoundException(nameof(Domain.Entities.Job), request.Id));

        var jobDetailDto = _mapper.Map<JobDetailVm>(job);

        return jobDetailDto;
    }
}