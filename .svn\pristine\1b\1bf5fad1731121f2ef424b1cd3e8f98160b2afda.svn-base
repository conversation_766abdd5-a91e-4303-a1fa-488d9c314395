using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Core.Domain;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class InfraObjectSchedulerFixture : IDisposable
{
    public List<InfraObjectScheduler> InfraObjectSchedulerPaginationList { get; set; }
    public List<InfraObjectScheduler> InfraObjectSchedulerList { get; set; }
    public InfraObjectScheduler InfraObjectSchedulerDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string InfraObjectId = "INFRA_123";
    public const string WorkflowId = "WORKFLOW_123";

    public ApplicationDbContext DbContext { get; private set; }
    public Mock<ILoggedInUserService> MockLoggedInUserService { get; private set; }
    public AssignedEntity AssignedEntityForFiltering { get; private set; }
    public AssignedEntity AssignedEntityForAllAccess { get; private set; }

    public InfraObjectSchedulerFixture()
    {
        var fixture = new Fixture();

        InfraObjectSchedulerList = fixture.Create<List<InfraObjectScheduler>>();

        InfraObjectSchedulerPaginationList = fixture.CreateMany<InfraObjectScheduler>(20).ToList();

        // Setup proper test data for InfraObjectSchedulerPaginationList
        InfraObjectSchedulerPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraObjectSchedulerPaginationList.ForEach(x => x.IsActive = true);
        InfraObjectSchedulerPaginationList.ForEach(x => x.CompanyId = CompanyId);
        InfraObjectSchedulerPaginationList.ForEach(x => x.InfraObjectId = InfraObjectId);

        // Setup proper test data for InfraObjectSchedulerList
        InfraObjectSchedulerList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraObjectSchedulerList.ForEach(x => x.IsActive = true);
        InfraObjectSchedulerList.ForEach(x => x.CompanyId = CompanyId);
        InfraObjectSchedulerList.ForEach(x => x.InfraObjectId = InfraObjectId);

        InfraObjectSchedulerDto = fixture.Create<InfraObjectScheduler>();
        InfraObjectSchedulerDto.ReferenceId = Guid.NewGuid().ToString();
        InfraObjectSchedulerDto.IsActive = true;
        InfraObjectSchedulerDto.CompanyId = CompanyId;
        InfraObjectSchedulerDto.InfraObjectId = InfraObjectId;
        InfraObjectSchedulerDto.InfraObjectName = "Test Infrastructure Object";
        InfraObjectSchedulerDto.WorkflowTypeId = "WT_123";
        InfraObjectSchedulerDto.WorkflowType = "Disaster Recovery";
        InfraObjectSchedulerDto.BeforeSwitchOverWorkflowId = WorkflowId;
        InfraObjectSchedulerDto.BeforeSwitchOverWorkflowName = "Before Switchover Workflow";
        InfraObjectSchedulerDto.AfterSwitchOverWorkflowId = "WORKFLOW_456";
        InfraObjectSchedulerDto.AfterSwitchOverWorkflowName = "After Switchover Workflow";
        InfraObjectSchedulerDto.ScheduleType = 1;
        InfraObjectSchedulerDto.CronExpression = "0 0 12 * * ?";
        InfraObjectSchedulerDto.ScheduleTime = "12:00:00";
        InfraObjectSchedulerDto.Status = "Active";
        InfraObjectSchedulerDto.NodeId = "NODE_123";
        InfraObjectSchedulerDto.NodeName = "Test Node";
        InfraObjectSchedulerDto.State = "Running";
        InfraObjectSchedulerDto.IsSchedule = 1;
        InfraObjectSchedulerDto.WorkflowVersion = "1.0";
        InfraObjectSchedulerDto.GroupPolicyId = "GP_123";
        InfraObjectSchedulerDto.GroupPolicyName = "Test Group Policy";
        InfraObjectSchedulerDto.ExecutionPolicy = "Sequential";
        InfraObjectSchedulerDto.IsEnable = true;
        InfraObjectSchedulerDto.LastExecutionTime = DateTime.Now.AddHours(-1).ToString();
        InfraObjectSchedulerDto.ExceptionMessage = null;

        DbContext = DbContextFactory.CreateInMemoryDbContext();

        // Setup AssignedEntity for filtering scenarios
        SetupAssignedEntities();

        // Setup Mock LoggedInUserService
        SetupMockLoggedInUserService();
    }

    private void SetupAssignedEntities()
    {
        // AssignedEntity for filtering (IsAll = false)
        AssignedEntityForFiltering = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    IsAll = false,
                    IsPartial = false,
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_001",
                            Name = "Business Function 1",
                            IsAll = false,
                            IsPartial = false,
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = InfraObjectId, Name = "Test Infrastructure Object", IsSelected = true },
                                new AssignedInfraObjects { Id = "INFRA_456", Name = "Another Infrastructure Object", IsSelected = true }
                            }
                        },
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_002",
                            Name = "Business Function 2",
                            IsAll = false,
                            IsPartial = false,
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = "INFRA_789", Name = "Third Infrastructure Object", IsSelected = true }
                            }
                        }
                    }
                },
                new AssignedBusinessServices
                {
                    Id = "BS_002",
                    Name = "Business Service 2",
                    IsAll = false,
                    IsPartial = false,
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_003",
                            Name = "Business Function 3",
                            IsAll = false,
                            IsPartial = false,
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = "INFRA_999", Name = "Fourth Infrastructure Object", IsSelected = true }
                            }
                        }
                    }
                }
            }
        };

        // AssignedEntity for all access (IsAll = true)
        AssignedEntityForAllAccess = new AssignedEntity
        {
            IsAll = true,
            AssignedBusinessServices = new List<AssignedBusinessServices>()
        };
    }

    private void SetupMockLoggedInUserService()
    {
        MockLoggedInUserService = new Mock<ILoggedInUserService>();
        MockLoggedInUserService.Setup(x => x.CompanyId).Returns(CompanyId);
        MockLoggedInUserService.Setup(x => x.UserId).Returns("USER_456");
        MockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        MockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
    }

    /// <summary>
    /// Configure the mock for scenarios where IsAllInfra = true (no filtering)
    /// </summary>
    public void ConfigureMockForAllInfraAccess()
    {
        var assignedInfrasJson = System.Text.Json.JsonSerializer.Serialize(AssignedEntityForAllAccess);
        MockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);
        MockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);
    }

    /// <summary>
    /// Configure the mock for scenarios where IsAllInfra = false (filtering required)
    /// </summary>
    public void ConfigureMockForFilteredInfraAccess()
    {
        var assignedInfrasJson = System.Text.Json.JsonSerializer.Serialize(AssignedEntityForFiltering);
        MockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        MockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);
    }

    /// <summary>
    /// Configure the mock for scenarios where IsAllInfra = false but no assigned infras (empty result)
    /// </summary>
    public void ConfigureMockForEmptyInfraAccess()
    {
        var emptyAssignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>()
        };
        var assignedInfrasJson = System.Text.Json.JsonSerializer.Serialize(emptyAssignedEntity);
        MockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        MockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);
    }

    /// <summary>
    /// Configure the mock for scenarios where AssignedBusinessServices is null
    /// </summary>
    public void ConfigureMockForNullAssignedBusinessServices()
    {
        MockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        MockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{\"IsAll\":false,\"AssignedBusinessServices\":null}");
    }

    /// <summary>
    /// Configure the mock for complex nested scenarios with multiple business services and functions
    /// </summary>
    public void ConfigureMockForComplexNestedStructure()
    {
        var complexAssignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_001",
                            Name = "Business Function 1",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = InfraObjectId, Name = "Infrastructure 1", IsSelected = true },
                                new AssignedInfraObjects { Id = "INFRA_456", Name = "Infrastructure 2", IsSelected = true }
                            }
                        },
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_002",
                            Name = "Business Function 2",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = "INFRA_789", Name = "Infrastructure 3", IsSelected = true }
                            }
                        }
                    }
                },
                new AssignedBusinessServices
                {
                    Id = "BS_002",
                    Name = "Business Service 2",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_003",
                            Name = "Business Function 3",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = "INFRA_999", Name = "Infrastructure 4", IsSelected = true }
                            }
                        }
                    }
                }
            }
        };

        var assignedInfrasJson = System.Text.Json.JsonSerializer.Serialize(complexAssignedEntity);
        MockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        MockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);
    }

    /// <summary>
    /// Create a custom mock with specific assigned entity configuration
    /// </summary>
    public Mock<ILoggedInUserService> CreateCustomMock(AssignedEntity assignedEntity, bool isAllInfra = false, bool isParent = true)
    {
        var customMock = new Mock<ILoggedInUserService>();
        customMock.Setup(x => x.CompanyId).Returns(CompanyId);
        customMock.Setup(x => x.UserId).Returns("USER_456");
        customMock.Setup(x => x.IsAuthenticated).Returns(true);
        customMock.Setup(x => x.IsParent).Returns(isParent);
        customMock.Setup(x => x.IsAllInfra).Returns(isAllInfra);

        var assignedInfrasJson = System.Text.Json.JsonSerializer.Serialize(assignedEntity);
        customMock.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);

        return customMock;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
