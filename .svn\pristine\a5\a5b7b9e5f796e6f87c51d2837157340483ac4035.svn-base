﻿using ContinuityPatrol.Application.Features.Database.Events.LicenseInfoEvents.Create;
using ContinuityPatrol.Application.Features.Database.Events.SaveAs;
using ContinuityPatrol.Application.Helper;

namespace ContinuityPatrol.Application.Features.Database.Commands.SaveAs;

public class SaveAsDatabaseCommandHandler : IRequestHandler<SaveAsDatabaseCommand, SaveAsDatabaseResponse>
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly ISiteTypeRepository _siteTypeRepository;
    private readonly ISiteRepository _siteRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly IServerRepository _serverRepository;

    public SaveAsDatabaseCommandHandler(IPublisher publisher, IMapper mapper, IDatabaseRepository databaseRepository,
        ISiteTypeRepository siteTypeRepository, ISiteRepository siteRepository, ILicenseManagerRepository licenseManagerRepository, IServerRepository serverRepository)
    {
        _publisher = publisher;
        _mapper = mapper;
        _databaseRepository = databaseRepository;
        _siteTypeRepository = siteTypeRepository;
        _siteRepository = siteRepository;
        _licenseManagerRepository = licenseManagerRepository;
        _serverRepository = serverRepository;
    }

    public async Task<SaveAsDatabaseResponse> Handle(SaveAsDatabaseCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _databaseRepository.GetByReferenceIdAsync(request.DatabaseId);

        eventToUpdate.Id = 0;

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Database), request.DatabaseId);

        eventToUpdate.ReferenceId = "";

        eventToUpdate.ModeType = "Pending";

        _mapper.Map(request, eventToUpdate, typeof(SaveAsDatabaseCommand), typeof(Domain.Entities.Database));

        var database= await _databaseRepository.AddAsync(eventToUpdate);

        var serverDto = await _serverRepository.GetByReferenceIdAsync(database?.ServerId);

        serverDto.LicenseKey = SecurityHelper.Encrypt(serverDto.LicenseKey);

        serverDto.IsAttached = true;

        await _serverRepository.UpdateAsync(serverDto);

        var ipAddress = GetJsonProperties.GetIpAddressFromProperties(serverDto.Properties);

        var sid = GetJsonProperties.GetJsonDatabaseSidValue(eventToUpdate.Properties);

        var hostName = GetJsonProperties.GetHostNameFromProperties(serverDto.Properties);

        var logo = GetJsonProperties.GetJsonValue(database!.Properties, "icon");

        var poNumber = eventToUpdate.LicenseKey.IsNotNullOrWhiteSpace() ?
          SecurityHelper.Decrypt(eventToUpdate.LicenseKey) : 
         (await _licenseManagerRepository.GetLicenseDetailByIdAsync(eventToUpdate.LicenseId)).PoNumber;

        var site = await _siteRepository.GetByReferenceIdAsync(serverDto.SiteId);

        var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site.TypeId);

        if (siteType.Category.ToLower().Equals("primary"))
            await _publisher.Publish(new DatabaseLicenseInfoCreatedEvent
            {
                EntityName = database.ReferenceId,
                PONumber = poNumber,
                EntityId = database.ReferenceId,
                EntityField = $"{ipAddress},{hostName},{sid}",
                Type = database.DatabaseType,
                BusinessServiceId = database.BusinessServiceId,
                BusinessServiceName = database.BusinessServiceName,
                Category = database.Type,
                Logo = logo
            }, cancellationToken);
        var response = new SaveAsDatabaseResponse
        {
            Id = eventToUpdate.ReferenceId,
            Message = Message.Create(nameof(Domain.Entities.Database), eventToUpdate.Name)
        };

        await _publisher.Publish(new SaveAsDatabaseEvent { Name = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}