using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowActionTypeRepositoryTests : IClassFixture<WorkflowActionTypeFixture>
    {
        private readonly WorkflowActionTypeFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowActionTypeRepository _repository;

        public WorkflowActionTypeRepositoryTests(WorkflowActionTypeFixture fixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _repository = new WorkflowActionTypeRepository(_dbContext, DbContextFactory.GetMockUserService());
        }

        [Fact]
        public async Task ListAllAsync_ReturnsActiveActionTypes()
        {
            await _dbContext.WorkflowActionTypes.AddRangeAsync(_fixture.WorkflowActionTypeList);
            await _dbContext.SaveChangesAsync();

            var result = await _repository.ListAllAsync();

            Assert.Equal(_fixture.WorkflowActionTypeList.Count(x => x.IsActive), result.Count);
        }

        [Fact]
        public async Task GetWorkflowActionTypeById_ReturnsEntity_WhenExistsAndIsDeleteTrue()
        {
            var entity = _fixture.WorkflowActionTypeDto;
            entity.IsDelete = true;
            await _dbContext.WorkflowActionTypes.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repository.GetWorkflowActionTypeById(entity.ReferenceId);

            Assert.NotNull(result);
            Assert.Equal(entity.ReferenceId, result.ReferenceId);
        }

        [Fact]
        public async Task GetWorkflowActionTypeById_ReturnsNull_WhenNotExists()
        {
            var result = await _repository.GetWorkflowActionTypeById("non-existent-id");

            Assert.Null(result);
        }

        [Fact]
        public async Task IsWorkflowActionTypeExist_ReturnsTrue_WhenNameExists_AndIdIsInvalidGuid()
        {
            var entity = _fixture.WorkflowActionTypeDto;
            entity.ActionType = "TestType";
            await _dbContext.WorkflowActionTypes.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repository.IsWorkflowActionTypeExist("TestType", "invalid-guid");

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowActionTypeExist_ReturnsFalse_WhenNameDoesNotExist_AndIdIsInvalidGuid()
        {
            var result = await _repository.IsWorkflowActionTypeExist("NonExistent", "invalid-guid");

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowActionTypeExist_ReturnsExpected_WhenIdIsValidGuid()
        {
            var id = Guid.NewGuid().ToString();
            var entity = _fixture.WorkflowActionTypeDto;
            entity.ReferenceId = id;
            entity.ActionType = "UniqueType";
            await _dbContext.WorkflowActionTypes.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repository.IsWorkflowActionTypeExist("UniqueType", id);

            Assert.False(result);
        }

        [Fact]
        public async Task PaginatedListAllAsync_ReturnsPaginatedResult()
        {
            await _dbContext.WorkflowActionTypes.AddRangeAsync(_fixture.WorkflowActionTypeList);
            await _dbContext.SaveChangesAsync();

            var result = await _repository.PaginatedListAllAsync(1, 2, null, "ActionType", "desc");

            Assert.NotNull(result);
            Assert.True(result.Data.Count <= 2);
        }

        [Fact]
        public async Task IsWorkflowActionTypeUnique_ReturnsTrue_WhenTypeExists()
        {
            var entity = _fixture.WorkflowActionTypeDto;
            entity.ActionType = "UniqueType";
            await _dbContext.WorkflowActionTypes.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repository.IsWorkflowActionTypeUnique("UniqueType");

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowActionTypeUnique_ReturnsFalse_WhenTypeDoesNotExist()
        {
            var result = await _repository.IsWorkflowActionTypeUnique("NonExistent");

            Assert.False(result);
        }
    }
}