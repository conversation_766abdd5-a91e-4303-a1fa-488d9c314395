﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller;

public class HitachiOracleFullDBRacControllerShould
{
    private readonly HitachiOracleFullDBRacController _controller;

    public HitachiOracleFullDBRacControllerShould()
    {
            
        _controller = new HitachiOracleFullDBRacController();
    }

    [Fact]
    public void List_ReturnsViewResult()
    {
            
        var result = _controller.List();

            
        var viewResult = Assert.IsType<ViewResult>(result);
            
    }
}