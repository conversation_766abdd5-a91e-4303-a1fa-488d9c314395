﻿using ContinuityPatrol.Application.Features.SVCGMMonitoringStatus.Queries.GetByInfraObjectId;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.SVCGMMonitoringStatus.Queries
{
    public class SVCGMMonitorStatusByInfraObjectIdQueryHandlerTests
    {
        private readonly Mock<ISVCGMMonitorStatusRepository> _mockSVCGMMonitorStatusRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly SVCGMMonitorStatusByInfraObjectIdQueryHandler _handler;

        public SVCGMMonitorStatusByInfraObjectIdQueryHandlerTests()
        {
            _mockSVCGMMonitorStatusRepository = new Mock<ISVCGMMonitorStatusRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new SVCGMMonitorStatusByInfraObjectIdQueryHandler(_mockSVCGMMonitorStatusRepository.Object, _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnReferenceId_WhenSVCGMMonitorStatusExists()
        {
            var infraObjectId = Guid.NewGuid().ToString();
            var expectedReferenceId = "12345";
            var mockStatus = new SVCGMMonitorStatus { ReferenceId = expectedReferenceId };

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.GetSVCGMMonitorStatusByInfraObjectId(infraObjectId))
                .ReturnsAsync(mockStatus);

            var query = new SVCGMMonitorStatusByInfraObjectIdQuery { InfraObjectId = infraObjectId };

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(expectedReferenceId, result);

            _mockSVCGMMonitorStatusRepository.Verify(repo => repo.GetSVCGMMonitorStatusByInfraObjectId(infraObjectId), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenSVCGMMonitorStatusNotFound()
        {
            var infraObjectId = Guid.NewGuid().ToString();

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.GetSVCGMMonitorStatusByInfraObjectId(infraObjectId))
                .ReturnsAsync((SVCGMMonitorStatus)null);

            var query = new SVCGMMonitorStatusByInfraObjectIdQuery { InfraObjectId = infraObjectId };

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, CancellationToken.None));

            _mockSVCGMMonitorStatusRepository.Verify(repo => repo.GetSVCGMMonitorStatusByInfraObjectId(infraObjectId), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldCallRepositoryOnce()
        {
            var infraObjectId = Guid.NewGuid().ToString();
            var mockStatus = new SVCGMMonitorStatus { ReferenceId = "12345" };

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.GetSVCGMMonitorStatusByInfraObjectId(infraObjectId))
                .ReturnsAsync(mockStatus);

            var query = new SVCGMMonitorStatusByInfraObjectIdQuery { InfraObjectId = infraObjectId };

            await _handler.Handle(query, CancellationToken.None);

            _mockSVCGMMonitorStatusRepository.Verify(repo => repo.GetSVCGMMonitorStatusByInfraObjectId(infraObjectId), Times.Once);
        }
    }
}
