﻿using AutoFixture;
using ContinuityPatrol.Application.Features.Replication.Commands.Create;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Replication.Commands.Update;
using ContinuityPatrol.Application.Features.Replication.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Replication.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Replication.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Server.Queries.GetDetail;

using ContinuityPatrol.Domain.ViewModels.DataSyncJobModel;
using ContinuityPatrol.Domain.ViewModels.DataSyncOptionsModel;
using ContinuityPatrol.Domain.ViewModels.ReplicationModel;
using ContinuityPatrol.Domain.ViewModels.RoboCopyJobModel;
using ContinuityPatrol.Domain.ViewModels.RoboCopyModel;
using ContinuityPatrol.Domain.ViewModels.RsyncJobModel;
using ContinuityPatrol.Domain.ViewModels.RsyncOptionModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class ReplicationControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<ILogger<ReplicationController>> _mockLogger = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private  ReplicationController _controller;

        public ReplicationControllerShould()
        {
            
            _controller = new ReplicationController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockMapper.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_Returns_ViewResult_And_PublishesEvent()
        {
            // Arrange
            _mockPublisher.Setup(p => p.Publish(It.IsAny<ReplicationPaginatedEvent>(), default))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
            Assert.Null(result.Model); // Production code returns View() with no model

            // Verify event was published
            _mockPublisher.Verify(p => p.Publish(It.IsAny<ReplicationPaginatedEvent>(), default), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_Creates_New_Replication_WhenIdIsEmpty()
        {
            // Arrange
            var replicationViewModel = new AutoFixture.Fixture().Create<ReplicationViewModel>();
            replicationViewModel.Id = ""; // Empty ID for create path
            replicationViewModel.Type = "standard"; // Non-subtype for simplicity
            replicationViewModel.Properties = "test-properties";

            var createCommand = new CreateReplicationCommand { Name = "TestReplication" };
            var createResult = new CreateReplicationResponse { Success = true, Message = "Created", ReplicationId = "new-id" };

            _mockMapper.Setup(m => m.Map<CreateReplicationCommand>(It.IsAny<ReplicationViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.Replication.CreateAsync(createCommand))
                .ReturnsAsync(createResult);

            // Act
            var result = await _controller.CreateOrUpdate(replicationViewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task GetReplicationList_Returns_JsonResult_With_ReplicationViewModel()
        {
            
            var replicationList = new List<ReplicationListVm>(); // Fill with test data
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationList())
                .ReturnsAsync(replicationList);

            
            var result = await _controller.GetReplicationList() as JsonResult;
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetByReferenceId_Returns_JsonResult_With_ServerDto()
        {
            
            var serverDto = new ServerDetailVm(); // Fill with test data
            _mockDataProvider.Setup(dp => dp.Server.GetByReferenceId(It.IsAny<string>()))
                .ReturnsAsync(serverDto);

            
            var result = await _controller.GetByReferenceId("someId") as JsonResult;
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task Delete_Removes_Replication_And_Returns_JsonResult()
        {
            // Arrange
            var id = "someId";
            var response = new BaseResponse { Success = true, Message = "Deleted" };
            _mockDataProvider.Setup(dp => dp.Replication.DeleteAsync(id))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task CreateOrUpdate_Updates_Existing_Replication_WhenIdIsNotEmpty()
        {
            // Arrange
            var replicationViewModel = new AutoFixture.Fixture().Create<ReplicationViewModel>();
            replicationViewModel.Id = "existing-id"; // Non-empty ID for update path
            replicationViewModel.Type = "standard"; // Non-subtype for simplicity
            replicationViewModel.Properties = "test-properties";

            var updateCommand = new UpdateReplicationCommand { Name = "UpdatedReplication" };
            var updateResult = new UpdateReplicationResponse { Success = true, Message = "Updated", ReplicationId = "existing-id" };

            _mockMapper.Setup(m => m.Map<UpdateReplicationCommand>(It.IsAny<ReplicationViewModel>()))
                .Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.Replication.UpdateAsync(updateCommand))
                .ReturnsAsync(updateResult);

            // Act
            var result = await _controller.CreateOrUpdate(replicationViewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesValidationException_ReturnsJsonException()
        {
            // Arrange
            var replicationViewModel = new AutoFixture.Fixture().Create<ReplicationViewModel>();
            replicationViewModel.Id = "";
            replicationViewModel.Properties = "test-properties";

            var validationResult = new FluentValidation.Results.ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Property", "Validation error"));
            _mockMapper.Setup(m => m.Map<CreateReplicationCommand>(It.IsAny<ReplicationViewModel>()))
                .Throws(new ValidationException(validationResult));

            // Act
            var result = await _controller.CreateOrUpdate(replicationViewModel);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesGeneralException_ReturnsJsonException()
        {
            // Arrange
            var replicationViewModel = new AutoFixture.Fixture().Create<ReplicationViewModel>();
            replicationViewModel.Id = "";
            replicationViewModel.Properties = "test-properties";

            _mockMapper.Setup(m => m.Map<CreateReplicationCommand>(It.IsAny<ReplicationViewModel>()))
                .Throws(new Exception("General error"));

            // Act
            var result = await _controller.CreateOrUpdate(replicationViewModel);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task Delete_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var id = "someId";
            _mockDataProvider.Setup(dp => dp.Replication.DeleteAsync(id))
                .Throws(new Exception("Delete error"));

            // Act
            var result = await _controller.Delete(id);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task GetByReplicationReferenceId_Returns_JsonResult_With_ReplicationData()
        {
            // Arrange
            var id = "test-id";
            var replicationData = new ReplicationDetailVm { Id = id, Name = "Test Replication" };
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationById(id))
                .ReturnsAsync(replicationData);

            // Act
            var result = await _controller.GetByReplicationReferenceId(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task GetByReplicationReferenceId_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var id = "test-id";
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationById(id))
                .Throws(new Exception("Get error"));

            // Act
            var result = await _controller.GetByReplicationReferenceId(id);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task GetReplicationById_Returns_JsonResult_With_ReplicationData_NoSubType()
        {
            // Arrange
            var id = "test-id";
            var replicationData = new ReplicationDetailVm { Id = id, Name = "Test Replication", Type = "standard" };
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationById(id))
                .ReturnsAsync(replicationData);

            // Act
            var result = await _controller.GetReplicationById(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"response1\":\"\"", json); // No subtype data
            Assert.Contains("\"response2\":", json);
        }

        [Fact]
        public async Task GetPagination_Returns_JsonResult_With_PaginatedData()
        {
            // Arrange
            var query = new GetReplicationPaginatedListQuery { PageNumber = 1, PageSize = 10 };
            var paginatedResult = new PaginatedResult<ReplicationListVm>();
            _mockDataProvider.Setup(dp => dp.Replication.GetPaginatedReplications(query))
                .ReturnsAsync(paginatedResult);

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(paginatedResult, result.Value);
        }

        [Fact]
        public async Task GetPagination_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var query = new GetReplicationPaginatedListQuery { PageNumber = 1, PageSize = 10 };
            _mockDataProvider.Setup(dp => dp.Replication.GetPaginatedReplications(query))
                .Throws(new Exception("Pagination error"));

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task IsReplicationNameExist_Returns_True_WhenNameExists()
        {
            // Arrange
            var replicationName = "TestReplication";
            var id = "test-id";
            _mockDataProvider.Setup(dp => dp.Replication.IsReplicationNameExist(replicationName, id))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.IsReplicationNameExist(replicationName, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task IsReplicationNameExist_Returns_False_WhenExceptionOccurs()
        {
            // Arrange
            var replicationName = "TestReplication";
            var id = "test-id";
            _mockDataProvider.Setup(dp => dp.Replication.IsReplicationNameExist(replicationName, id))
                .Throws(new Exception("Check error"));

            // Act
            var result = await _controller.IsReplicationNameExist(replicationName, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetReplicationNames_Returns_JsonResult_With_ReplicationNames()
        {
            // Arrange
            var replicationNames = new List<ReplicationNameVm>
            {
                new ReplicationNameVm { Id = "1", Name = "Replication1" },
                new ReplicationNameVm { Id = "2", Name = "Replication2" }
            };
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationNames())
                .ReturnsAsync(replicationNames);

            // Act
            var result = await _controller.GetReplicationNames() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task GetReplicationNames_HandlesException_ReturnsJsonException()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationNames())
                .Throws(new Exception("Get names error"));

            // Act
            var result = await _controller.GetReplicationNames();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task SaveAsReplication_Returns_JsonResult_With_Success()
        {
            // Arrange
            var saveAsCommand = new SaveAsReplicationCommand { Name = "ClonedReplication" };
            var response = new BaseResponse { Success = true, Message = "Cloned successfully" };
            _mockDataProvider.Setup(dp => dp.Replication.SaveAsReplication(saveAsCommand))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.SaveAsReplication(saveAsCommand) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task SaveAsReplication_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var saveAsCommand = new SaveAsReplicationCommand { Name = "ClonedReplication" };
            _mockDataProvider.Setup(dp => dp.Replication.SaveAsReplication(saveAsCommand))
                .Throws(new Exception("Save as error"));

            // Act
            var result = await _controller.SaveAsReplication(saveAsCommand);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task SaveAllReplication_Returns_JsonResult_With_Success()
        {
            // Arrange
            var saveAllCommand = new SaveAllReplicationCommand();
            var response = new BaseResponse { Success = true, Message = "All saved successfully" };
            _mockDataProvider.Setup(dp => dp.Replication.SaveAllReplication(saveAllCommand))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.SaveAllReplication(saveAllCommand) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task SaveAllReplication_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var saveAllCommand = new SaveAllReplicationCommand();
            _mockDataProvider.Setup(dp => dp.Replication.SaveAllReplication(saveAllCommand))
                .Throws(new Exception("Save all error"));

            // Act
            var result = await _controller.SaveAllReplication(saveAllCommand);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task ReplicationDataSyncProperties_Returns_JsonResult_With_DataSyncOptions()
        {
            // Arrange
            var dataSyncOptions = new List<DataSyncOptionsListVm>
            {
                new DataSyncOptionsListVm { Id = "1", Name = "DataSync1" },
                new DataSyncOptionsListVm { Id = "2", Name = "DataSync2" }
            };
            _mockDataProvider.Setup(dp => dp.DataSync.GetDataSyncList())
                .ReturnsAsync(dataSyncOptions);

            // Act
            var result = await _controller.ReplicationDataSyncProperties() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task ReplicationDataSyncProperties_HandlesException_ReturnsJsonException()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.DataSync.GetDataSyncList())
                .Throws(new Exception("DataSync error"));

            // Act
            var result = await _controller.ReplicationDataSyncProperties();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task ReplicationRSyncOptions_Returns_JsonResult_With_RSyncOptions()
        {
            // Arrange
            var rSyncOptions = new List<RsyncOptionListVm>
            {
                new RsyncOptionListVm { Id = "1", Name = "RSync1" },
                new RsyncOptionListVm { Id = "2", Name = "RSync2" }
            };
            _mockDataProvider.Setup(dp => dp.RsyncOption.GetRsyncOptionList())
                .ReturnsAsync(rSyncOptions);

            // Act
            var result = await _controller.ReplicationRSyncOptions() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task ReplicationRSyncOptions_HandlesException_ReturnsJsonException()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.RsyncOption.GetRsyncOptionList())
                .Throws(new Exception("RSync error"));

            // Act
            var result = await _controller.ReplicationRSyncOptions();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task ReplicationRoboCopyOptions_Returns_JsonResult_With_RoboCopyOptions()
        {
            // Arrange
            var roboCopyOptions = new List<RoboCopyListVm>
            {
                new RoboCopyListVm { Id = "1", Name = "RoboCopy1" },
                new RoboCopyListVm { Id = "2", Name = "RoboCopy2" }
            };
            _mockDataProvider.Setup(dp => dp.RoboCopy.GetRoboCopyList())
                .ReturnsAsync(roboCopyOptions);

            // Act
            var result = await _controller.ReplicationRoboCopyOptions() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task ReplicationRoboCopyOptions_HandlesException_ReturnsJsonException()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.RoboCopy.GetRoboCopyList())
                .Throws(new Exception("RoboCopy error"));

            // Act
            var result = await _controller.ReplicationRoboCopyOptions();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task GetReplicationById_Returns_JsonResult_With_RsyncData_WhenTypeIsRsync()
        {
            // Arrange
            var id = "test-id";
            var replicationData = new ReplicationDetailVm { Id = id, Name = "Test Replication", Type = "rsync" };
            var rsyncJobs = new List<RsyncJobListVm>
            {
                new RsyncJobListVm { Id = "1", ReplicationId = id, ReplicationName = "RSync1" }
            };

            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationById(id))
                .ReturnsAsync(replicationData);
            _mockDataProvider.Setup(dp => dp.RsyncJob.GetRsyncJobs())
                .ReturnsAsync(rsyncJobs);

            // Act
            var result = await _controller.GetReplicationById(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"response1\":", json);
            Assert.Contains("\"response2\":", json);
        }

        [Fact]
        public async Task GetReplicationById_Returns_JsonResult_With_RoboCopyData_WhenTypeIsRoboCopy()
        {
            // Arrange
            var id = "test-id";
            var replicationData = new ReplicationDetailVm { Id = id, Name = "Test Replication", Type = "robocopy" };
            var roboCopyJobs = new List<RoboCopyJobListVm>
            {
                new RoboCopyJobListVm { Id = "1", ReplicationId = id, ReplicationName = "RoboCopy1" }
            };

            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationById(id))
                .ReturnsAsync(replicationData);
            _mockDataProvider.Setup(dp => dp.RoboCopyJob.GetRoboCopyJobs())
                .ReturnsAsync(roboCopyJobs);

            // Act
            var result = await _controller.GetReplicationById(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"response1\":", json);
            Assert.Contains("\"response2\":", json);
        }

        [Fact]
        public async Task GetReplicationById_Returns_JsonResult_With_DataSyncData_WhenTypeIsDataSync()
        {
            // Arrange
            var id = "test-id";
            var replicationData = new ReplicationDetailVm { Id = id, Name = "Test Replication", Type = "datasync" };
            var dataSyncJobs = new List<DataSyncJobListVm>
            {
                new DataSyncJobListVm { Id = "1", ReplicationId = id, ReplicationName = "DataSync1" }
            };

            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationById(id))
                .ReturnsAsync(replicationData);
            _mockDataProvider.Setup(dp => dp.DataSyncJob.GetDataSyncJobs())
                .ReturnsAsync(dataSyncJobs);

            // Act
            var result = await _controller.GetReplicationById(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"response1\":", json);
            Assert.Contains("\"response2\":", json);
        }

        [Fact]
        public async Task GetReplicationById_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var id = "test-id";
            _mockDataProvider.Setup(dp => dp.Replication.GetReplicationById(id))
                .Throws(new Exception("Get by ID error"));

            // Act
            var result = await _controller.GetReplicationById(id);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task DeleteSubTypes_Deletes_DataSyncJob_WhenTypeIsDataSync()
        {
            // Arrange
            var type = "datasync";
            var optionId = "option-1";
            var sourcePath = "/source";
            var targetPath = "/target";

            var dataSyncJobs = new List<DataSyncJobListVm>
            {
                new DataSyncJobListVm
                {
                    Id = "job-1",
                    DataSyncOptionId = optionId,
                    SourceDirectory = sourcePath,
                    DestinationDirectory = targetPath
                }
            };
            var response = new BaseResponse { Success = true, Message = "Deleted" };

            _mockDataProvider.Setup(dp => dp.DataSyncJob.GetDataSyncJobs())
                .ReturnsAsync(dataSyncJobs);
            _mockDataProvider.Setup(dp => dp.DataSyncJob.DeleteDataSyncJob("job-1"))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.DeleteSubTypes(type, optionId, sourcePath, targetPath) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task DeleteSubTypes_Deletes_RsyncJob_WhenTypeIsRsync()
        {
            // Arrange
            var type = "rsync";
            var optionId = "option-1";
            var sourcePath = "/source";
            var targetPath = "/target";

            var rsyncJobs = new List<RsyncJobListVm>
            {
                new RsyncJobListVm
                {
                    Id = "job-1",
                    RsyncOptionId = optionId,
                    SourceDirectory = sourcePath,
                    DestinationDirectory = targetPath
                }
            };
            var response = new BaseResponse { Success = true, Message = "Deleted" };

            _mockDataProvider.Setup(dp => dp.RsyncJob.GetRsyncJobs())
                .ReturnsAsync(rsyncJobs);
            _mockDataProvider.Setup(dp => dp.RsyncJob.DeleteRsyncJob("job-1"))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.DeleteSubTypes(type, optionId, sourcePath, targetPath) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task DeleteSubTypes_Deletes_RoboCopyJob_WhenTypeIsRoboCopy()
        {
            // Arrange
            var type = "robocopy";
            var optionId = "option-1";
            var sourcePath = "/source";
            var targetPath = "/target";

            var roboCopyJobs = new List<RoboCopyJobListVm>
            {
                new RoboCopyJobListVm
                {
                    Id = "job-1",
                    RoboCopyOptionsId = optionId,
                    SourceDirectory = sourcePath,
                    DestinationDirectory = targetPath
                }
            };
            var response = new BaseResponse { Success = true, Message = "Deleted" };

            _mockDataProvider.Setup(dp => dp.RoboCopyJob.GetRoboCopyJobs())
                .ReturnsAsync(roboCopyJobs);
            _mockDataProvider.Setup(dp => dp.RoboCopyJob.DeleteRoboCopyJob("job-1"))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.DeleteSubTypes(type, optionId, sourcePath, targetPath) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task DeleteSubTypes_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var type = "datasync";
            var optionId = "option-1";
            var sourcePath = "/source";
            var targetPath = "/target";

            _mockDataProvider.Setup(dp => dp.DataSyncJob.GetDataSyncJobs())
                .Throws(new Exception("Delete subtypes error"));

            // Act
            var result = await _controller.DeleteSubTypes(type, optionId, sourcePath, targetPath);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }









    }
}
