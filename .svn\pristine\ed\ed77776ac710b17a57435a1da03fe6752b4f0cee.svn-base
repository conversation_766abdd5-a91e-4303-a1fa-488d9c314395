using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SiteFixture : IDisposable
{
    public List<Site> SitePaginationList { get; set; }
    public List<Site> SiteList { get; set; }
    public Site SiteDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SiteFixture()
    {
        var fixture = new Fixture();

        SiteList = fixture.Create<List<Site>>();

        SitePaginationList = fixture.CreateMany<Site>(20).ToList();

        SitePaginationList.ForEach(x => x.CompanyId = CompanyId);

        SiteList.ForEach(x => x.CompanyId = CompanyId);

        SiteDto = fixture.Create<Site>();

        SiteDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
