using ContinuityPatrol.Application.Features.BulkImportActionResult.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportActionResult.Events;

public class CreateBulkImportActionResultEventTests : IClassFixture<BulkImportActionResultFixture>, IClassFixture<UserActivityFixture>
{
    private readonly BulkImportActionResultFixture _bulkImportActionResultFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly BulkImportActionResultCreatedEventHandler _handler;

    public CreateBulkImportActionResultEventTests(BulkImportActionResultFixture bulkImportActionResultFixture, UserActivityFixture userActivityFixture)
    {
        _bulkImportActionResultFixture = bulkImportActionResultFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");
        mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/bulkimportactionresult");
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        var mockBulkImportActionResultEventLogger = new Mock<ILogger<BulkImportActionResultCreatedEventHandler>>();

        _mockUserActivityRepository = BulkImportActionResultRepositoryMocks.CreateBulkImportActionResultEventRepository(_userActivityFixture.UserActivities);

        _handler = new BulkImportActionResultCreatedEventHandler(
            mockLoggedInUserService.Object, 
            mockBulkImportActionResultEventLogger.Object, 
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateBulkImportActionResultEventCreated()
    {
        // Arrange
        _userActivityFixture.UserActivities[0].LoginName = "Tester";
        var bulkImportActionResultCreatedEvent = new BulkImportActionResultCreatedEvent { Name = "TestEntity" };

        // Act
        var result = _handler.Handle(bulkImportActionResultCreatedEvent, CancellationToken.None);

        // Assert
        Assert.True(result.IsCompleted);
        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var bulkImportActionResultCreatedEvent = new BulkImportActionResultCreatedEvent { Name = "TestEntity" };

        // Act
        await _handler.Handle(bulkImportActionResultCreatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_WithCorrectProperties()
    {
        // Arrange
        var bulkImportActionResultCreatedEvent = new BulkImportActionResultCreatedEvent { Name = "TestEntity" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportActionResultCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.LoginName.ShouldBe("Tester");
        capturedUserActivity.Action.ShouldContain("Create BulkImportActionResult");
        capturedUserActivity.Entity.ShouldBe("BulkImportActionResult");
        capturedUserActivity.ActivityType.ShouldBe("Create");
        capturedUserActivity.ActivityDetails.ShouldContain("TestEntity");
        capturedUserActivity.ActivityDetails.ShouldContain("created successfully");
    }

    [Fact]
    public async Task Handle_LogCorrectInformation_When_BulkImportActionResultCreated()
    {
        // Arrange
        var bulkImportActionResultCreatedEvent = new BulkImportActionResultCreatedEvent { Name = "TestEntity" };
        var mockLogger = new Mock<ILogger<BulkImportActionResultCreatedEventHandler>>();

        var handler = new BulkImportActionResultCreatedEventHandler(
            new Mock<ILoggedInUserService>().Object,
            mockLogger.Object,
            _mockUserActivityRepository.Object);

        // Act
        await handler.Handle(bulkImportActionResultCreatedEvent, CancellationToken.None);

        // Assert
        //mockLogger.Verify(
        //    x => x.Log(
        //        LogLevel.Information,
        //        It.IsAny<EventId>(),
        //        It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("TestEntity") && v.ToString().Contains("created successfully")),
        //        It.IsAny<Exception>(),
        //        It.IsAny<Func<It.IsAnyType, Exception, string>>()),
        //    Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectUserActivityProperties_When_UserServiceHasData()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var testUrl = "/api/test";
        var testIpAddress = "***********";
        var testLoginName = "TestUser";

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);
        mockLoggedInUserService.Setup(x => x.LoginName).Returns(testLoginName);
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(testUrl);
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns(testIpAddress);

        var handler = new BulkImportActionResultCreatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportActionResultCreatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportActionResultCreatedEvent = new BulkImportActionResultCreatedEvent { Name = "TestEntity" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportActionResultCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.UserId.ShouldBe(testUserId);
        capturedUserActivity.LoginName.ShouldBe(testLoginName);
        capturedUserActivity.RequestUrl.ShouldBe(testUrl);
        capturedUserActivity.HostAddress.ShouldBe(testIpAddress);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_EventHandled()
    {
        // Arrange
        var bulkImportActionResultCreatedEvent = new BulkImportActionResultCreatedEvent { Name = "ProductionEntity" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportActionResultCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.Action.ShouldBe("Create BulkImportActionResult");
        capturedUserActivity.Entity.ShouldBe("BulkImportActionResult");
        capturedUserActivity.ActivityType.ShouldBe("Create");
        capturedUserActivity.ActivityDetails.ShouldBe("BulkImportActionResult 'ProductionEntity' created successfully.");
    }

    [Fact]
    public async Task Handle_SetCreatedByAndLastModifiedBy_When_UserIdExists()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);

        var handler = new BulkImportActionResultCreatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportActionResultCreatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportActionResultCreatedEvent = new BulkImportActionResultCreatedEvent { Name = "TestEntity" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportActionResultCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.CreatedBy.ShouldBe(testUserId);
        capturedUserActivity.LastModifiedBy.ShouldBe(testUserId);
    }

    [Fact]
    public async Task Handle_SetDefaultGuidForCreatedBy_When_UserIdIsEmpty()
    {
        // Arrange
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns("");

        var handler = new BulkImportActionResultCreatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportActionResultCreatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportActionResultCreatedEvent = new BulkImportActionResultCreatedEvent { Name = "TestEntity" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportActionResultCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.CreatedBy.ShouldNotBeNullOrEmpty();
        capturedUserActivity.LastModifiedBy.ShouldNotBeNullOrEmpty();
        Guid.TryParse(capturedUserActivity.CreatedBy, out _).ShouldBeTrue();
        Guid.TryParse(capturedUserActivity.LastModifiedBy, out _).ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_EventProcessed()
    {
        // Arrange
        var bulkImportActionResultCreatedEvent = new BulkImportActionResultCreatedEvent { Name = null };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportActionResultCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.ActivityDetails.ShouldContain("created successfully");
    }

    [Fact]
    public async Task Handle_UseEntityNameAsEventName_When_BulkImportActionResultCreated()
    {
        // Arrange
        var bulkImportActionResultCreatedEvent = new BulkImportActionResultCreatedEvent { Name = "ServerEntity" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportActionResultCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.ActivityDetails.ShouldContain("ServerEntity");
        capturedUserActivity.ActivityDetails.ShouldBe("BulkImportActionResult 'ServerEntity' created successfully.");
    }
}
