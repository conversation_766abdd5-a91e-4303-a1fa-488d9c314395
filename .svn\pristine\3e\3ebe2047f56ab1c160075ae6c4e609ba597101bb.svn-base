using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.SiteLocation.Events.Delete;

public class SiteLocationDeletedEventHandler : INotificationHandler<SiteLocationDeletedEvent>
{
    private readonly ILogger<SiteLocationDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public SiteLocationDeletedEventHandler(ILoggedInUserService userService,
        ILogger<SiteLocationDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(SiteLocationDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            CompanyId = _userService.CompanyId,
            Action = $"{ActivityType.Delete} SiteLocation",
            Entity = "SiteLocation",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"SiteLocation '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"SiteLocation '{deletedEvent.Name}' deleted successfully.");
    }
}