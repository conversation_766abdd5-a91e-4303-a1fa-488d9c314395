using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ServerSubTypeFixture : IDisposable
{
    public List<ServerSubType> ServerSubTypePaginationList { get; set; }
    public List<ServerSubType> ServerSubTypeList { get; set; }
    public ServerSubType ServerSubTypeDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ServerSubTypeFixture()
    {
        var fixture = new Fixture();

        ServerSubTypeList = fixture.Create<List<ServerSubType>>();

        ServerSubTypePaginationList = fixture.CreateMany<ServerSubType>(20).ToList();

        ServerSubTypeDto = fixture.Create<ServerSubType>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
