﻿using ContinuityPatrol.Application.Features.WorkflowActionType.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionType.Commands;

public class UpdateWorkflowActionTypeTests : IClassFixture<WorkflowActionTypeFixture>
{
    private readonly WorkflowActionTypeFixture _workflowActionTypeFixture;

    private readonly Mock<IWorkflowActionTypeRepository> _mockWorkflowActionTypeRepository;

    private readonly UpdateWorkflowActionTypeCommandHandler _handler;

    public UpdateWorkflowActionTypeTests(WorkflowActionTypeFixture workflowActionTypeFixture)
    {
        _workflowActionTypeFixture = workflowActionTypeFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockWorkflowActionTypeRepository = WorkflowActionTypeRepositoryMocks.UpdateWorkflowActionTypeRepository(_workflowActionTypeFixture.WorkflowActionTypes);

        _handler = new UpdateWorkflowActionTypeCommandHandler(_workflowActionTypeFixture.Mapper, _mockWorkflowActionTypeRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidWorkflowActionType_UpdateReferenceIdAsync_ToWorkflowActionTypesRepo()
    {
        _workflowActionTypeFixture.UpdateWorkflowActionTypeCommand.Id = _workflowActionTypeFixture.WorkflowActionTypes[0].ReferenceId;

        var result = await _handler.Handle(_workflowActionTypeFixture.UpdateWorkflowActionTypeCommand, CancellationToken.None);

        var workflowActionType = await _mockWorkflowActionTypeRepository.Object.GetWorkflowActionTypeById(result.Id);

        Assert.Equal(_workflowActionTypeFixture.UpdateWorkflowActionTypeCommand.ActionType, workflowActionType.ActionType);
    }

    [Fact]
    public async Task Handle_Return_ValidWorkflowActionTypeResponse_WhenUpdate_WorkflowActionType()
    {
        _workflowActionTypeFixture.UpdateWorkflowActionTypeCommand.Id = _workflowActionTypeFixture.WorkflowActionTypes[0].ReferenceId;

        var result = await _handler.Handle(_workflowActionTypeFixture.UpdateWorkflowActionTypeCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateWorkflowActionTypeResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_workflowActionTypeFixture.UpdateWorkflowActionTypeCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidWorkflowActionTypeId()
    {
        _workflowActionTypeFixture.UpdateWorkflowActionTypeCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_workflowActionTypeFixture.UpdateWorkflowActionTypeCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        _workflowActionTypeFixture.UpdateWorkflowActionTypeCommand.Id = _workflowActionTypeFixture.WorkflowActionTypes[0].ReferenceId;

        await _handler.Handle(_workflowActionTypeFixture.UpdateWorkflowActionTypeCommand, CancellationToken.None);

        _mockWorkflowActionTypeRepository.Verify(x => x.GetWorkflowActionTypeById(It.IsAny<string>()), Times.Once);

        _mockWorkflowActionTypeRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.WorkflowActionType>()), Times.Once);
    }
}
