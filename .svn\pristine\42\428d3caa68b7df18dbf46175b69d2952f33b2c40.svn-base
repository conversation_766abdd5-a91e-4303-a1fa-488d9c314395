﻿using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Events.PaginatedView;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixUsersModel;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Events.PaginatedView;
using ContinuityPatrol.Shared.Core.Permissions;
using static ContinuityPatrol.Application.Constants.ErrorMessage;


namespace ContinuityPatrol.Web.Areas.Manage.Controllers;
[Area("Manage")]

public class ApprovalController : Controller
{
    private readonly IPublisher _publisher;
    private readonly ILogger<ApprovalController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;

    public ApprovalController(IPublisher publisher, ILogger<ApprovalController> logger, IMapper mapper, IDataProvider dataProvider)
    {
        _publisher = publisher;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }

    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in  ApprovalMatrix Users");

        await _publisher.Publish(new ApprovalMatrixUsersPaginatedEvent());

        var UserName = await _dataProvider.User.GetUserNames();
        var superAdminUsers = UserName.Where(u => u.RoleName == "SuperAdmin").ToList();
        var approvalUserNames = new ApprovalMatrixUsersViewModel
        {
            UserNames = superAdminUsers
        };
        return View(approvalUserNames);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> Create(CreateApprovalMatrixUsersCommand approval)
    {
        _logger.LogDebug("Entering Create method in  ApprovalMatrix Users");
        var userId = Request.Form["id"].ToString();
        approval.ApprovalMatrixUsers.ForEach(x => x.AcceptType = "NA");

        try
        {
            var usersString = string.Join(", ", approval.ApprovalMatrixUsers.Select(x => x.UserName));
            _logger.LogDebug($"Creating  ApprovalMatrix Users '{usersString}'");
            var createCommand = _mapper.Map<CreateApprovalMatrixUsersCommand>(approval);
            var response = await _dataProvider.ApprovalMatrixUsers.CreateAsync(createCommand);
            return Json(new { success = true, data = response });

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on  ApprovalMatrix Users page while processing the request for create.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> Update(UpdateApprovalMatrixUsersCommand approval)
    {
        _logger.LogDebug("Entering Update method in  ApprovalMatrix Users");
        var userId = Request.Form["id"].ToString();
        approval.AcceptType = "NA";

        try
        {
            _logger.LogDebug($"Updating  ApprovalMatrix Users '{approval.UserName}'");
            var updateCommand = _mapper.Map<UpdateApprovalMatrixUsersCommand>(approval);
            var response = await _dataProvider.ApprovalMatrixUsers.UpdateAsync(updateCommand);
            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on  ApprovalMatrix Users page while processing the request for update.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> CreateCPUser(ApprovalMatrixUsersViewModel approval)
    {
        _logger.LogDebug("Entering CreateCPUser method in  ApprovalMatrix Users");
        var userId = Request.Form["id"].ToString();
        approval.AcceptType = "Approver";

        try
        {
            _logger.LogDebug($"Creating  ApprovalMatrix Users '{approval.UserName}'");

            var createCommand = _mapper.Map<CreateApprovalMatrixUsersCommand>(approval);
            var response = await _dataProvider.ApprovalMatrixUsers.CreateAsync(createCommand);
            _logger.LogDebug("CreateCPUser operation  ApprovalMatrix Users successfully in  ApprovalMatrix Users.");
            return Json(new { success = true, data = createCommand });
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on  ApprovalMatrix Users page: {ex.ValidationErrors.FirstOrDefault()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetPaginatedlist(GetApprovalMatrixUsersPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPaginated method in ApprovalMatrix Users");
        try
        {
            _logger.LogDebug("Successfully retrieved paginated list for ApprovalMatrix Users");

            return Json(await _dataProvider.ApprovalMatrixUsers.GetPaginatedApprovalMatrixUsers(query));

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on ApprovalMatrix Users page while processing the pagination request.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetBusinessServiceList()
    {
        _logger.LogDebug("Entering ApprovalMatrix Users method in ApprovalMatrix Users");

        try
        {
            var result = await _dataProvider.BusinessService.GetBusinessServiceNames();

            _logger.LogDebug("Successfully retrieved ApprovalMatrix Usernames in ApprovalMatrix Users");

            return Json(result);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on ApprovalMatrix Users page while retrieving ApprovalMatrix Users List.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<bool> IsApprovalMatrixUsersNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsApprovalMatrixUsersName method in ApprovalMatrix Users.");
        try
        {
            var isJobnameExits = await _dataProvider.ApprovalMatrixUsers.IsApprovalMatrixUsersNameExist(name, id);

            _logger.LogDebug($"Successfully retrieved name exist detail in ApprovalMatrix Users.");
            return isJobnameExits;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on ApprovalMatrix Users page while retrieving the name exist detail.", ex);
            return false;
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Manage.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in ApprovalMatrix Users");

        try
        {
            _logger.LogDebug($"Deleting ApprovalMatrix Users Details by Id '{id}'");

            var response = await _dataProvider.ApprovalMatrixUsers.DeleteAsync(id);

            TempData.NotifySuccess(response.Message);

            return RedirectToAction("List");

        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred while deleting record on ApprovalMatrix Users.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetUserByID(string id)
    {
        _logger.LogDebug("Entering GetUserByID method in ApprovalMatrix Users");

        try
        {
            var result = await _dataProvider.User.GetByReferenceId(id);

            _logger.LogDebug("Successfully retrieved UserInfo Users in ApprovalMatrix Users");

            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on ApprovalMatrix Users page while retrieving UserInfo Users.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetUserDetails()
    {
        try
        {
            var userProfile = await _dataProvider.User.GetUserNames();
            var superAdmin = userProfile.Where(x => x?.RoleName.ToLower() == "superadmin").ToList();
            _logger.LogDebug("Successfully retrieved UserInfo Users in Users");
            return Json(new { success = true, data = superAdmin });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on Approval page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetApprovalMatrixUsersList()
    {
        try
        {
            var userProfile = await _dataProvider.ApprovalMatrixUsers.GetApprovalMatrixUsersList();
            _logger.LogDebug("Successfully retrieved UserInfo Users in Approval Matrix Users");

            var userNames = await _dataProvider.User.GetUserNames();
            _logger.LogDebug("Successfully retrieved UserInfo Users in Users");

            var userProfileNames = new HashSet<string>(userProfile.Select(u => u?.UserName));
            var superAdmin = userNames.Where(x => x?.RoleName?.ToLower() == "superadmin" && !userProfileNames.Contains(x?.LoginName)).ToList();
            return Json(new { success = true, data = superAdmin });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on ApprovalMatrixUsers page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }
}

