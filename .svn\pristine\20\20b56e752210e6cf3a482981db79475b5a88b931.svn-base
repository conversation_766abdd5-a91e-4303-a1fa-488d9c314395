﻿using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessServiceHealthLog.Commands;

public class DeleteBusinessServiceHealthLogTests : IClassFixture<BusinessServiceHealthLogFixture>
{
    private readonly BusinessServiceHealthLogFixture _businessServiceHealthLogFixture;
    private readonly Mock<IBusinessServiceHealthLogRepository> _mockBusinessServiceHealthLogRepository;
    private readonly DeleteBusinessServiceHealthLogCommandHandler _handler;

    public DeleteBusinessServiceHealthLogTests(BusinessServiceHealthLogFixture businessServiceHealthLogFixture)
    {
        _businessServiceHealthLogFixture = businessServiceHealthLogFixture;

        _mockBusinessServiceHealthLogRepository = BusinessServiceHealthLogRepositoryMocks.DeleteBusinessServiceHealthLogRepository(_businessServiceHealthLogFixture.BusinessServiceHealthLogs);

        _handler = new DeleteBusinessServiceHealthLogCommandHandler(_mockBusinessServiceHealthLogRepository.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_BusinessServiceHealthLogDeleted()
    {
        var result = await _handler.Handle(new DeleteBusinessServiceHealthLogCommand { Id = _businessServiceHealthLogFixture.BusinessServiceHealthLogs[0].ReferenceId }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteBusinessServiceHealthLogResponse_When_BusinessServiceHealthLogDeleted()
    {
        var result = await _handler.Handle(new DeleteBusinessServiceHealthLogCommand { Id = _businessServiceHealthLogFixture.BusinessServiceHealthLogs[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteBusinessServiceHealthLogResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_BusinessServiceHealthLogDeleted()
    {
        await _handler.Handle(new DeleteBusinessServiceHealthLogCommand { Id = _businessServiceHealthLogFixture.BusinessServiceHealthLogs[0].ReferenceId }, CancellationToken.None);

        var businessServiceHealthLog = await _mockBusinessServiceHealthLogRepository.Object.GetByReferenceIdAsync(_businessServiceHealthLogFixture.BusinessServiceHealthLogs[0].ReferenceId);

        businessServiceHealthLog.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidBusinessServiceHealthLogId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteBusinessServiceHealthLogCommand { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteBusinessServiceHealthLogCommand { Id = _businessServiceHealthLogFixture.BusinessServiceHealthLogs[0].ReferenceId }, CancellationToken.None);

        _mockBusinessServiceHealthLogRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockBusinessServiceHealthLogRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BusinessServiceHealthLog>()), Times.Once);
    }
}