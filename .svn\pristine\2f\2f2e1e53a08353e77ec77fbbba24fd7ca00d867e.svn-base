﻿@model ContinuityPatrol.Application.Features.Database.Commands.UpdateBulkPassword.UpdateDatabasePasswordCommand;
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None archiveisometric" style="height:calc(100vh - 105px);background-image:url(/img/isomatric/bulk_credential.svg)">
        <div class="">
            <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                <div class="d-flex align-items-center">
                    <h6 class="page_title">
                        <i class="cp-scheduled-report"></i>
                        <span>Bulk Database Credential</span>
                    </h6>
                </div>
            </div>
        </div>
        <div class="card-body">
            <form id="bulkDatabase">
                <div class="row row-cols-6">
                    <div class="col-6">
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">
                                        Username
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-name"></i></span>
                                        <input class="form-control" autocomplete="off" id="userName" type="text" maxlength="100" placeholder="Enter Username" />
                                    </div>
                                    <span id="userName-error"></span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">
                                        Database Type
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-database-type"></i></span>
                                        <select class="form-select" aria-label="Default select example" id="DBtype" data-live-search="true" data-placeholder="Select Database Type">
                                        </select>
                                    </div>
                                    <span id="DBtype-error"></span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">
                                        Database List
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-database-page"></i></span>
                                        <select class="form-select" data-live-search="true" id="DBlist" multiple data-placeholder="Select Database List" required>
                                        </select>
                                    </div>
                                    <span id="DBlist-error"></span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">
                                        Password
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-lock"></i></span>
                                        <input class="form-control" type="password" id="Password" placeholder="Enter Password" autocomplete="Password" maxlength="30" />
                                        <span class="input-group-text toggle-password" role="button"><i class="cp-password-visible fs-6" title="show password"></i></span>
                                    </div>
                                    <span id="passwordError"></span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">
                                        Confirm Password
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-lock"></i></span>
                                        <input class="form-control" type="password" id="ConfirmPassword" placeholder="Enter Confirm Password" autocomplete="ConfirmPassword" maxlength="30" />
                                    </div>
                                    <span id="ConfirmPassword-error"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gap-2 d-flex align-items-center ">
                    <button type="button" class="btn btn-secondary btn-sm" id='cancel' style="min-width:80px"  cursorshover="true">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm btn-save" id="SaveFunction" style="min-width:80px">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div id="configurationbulkCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true"></div>
@* Duplicate Actions *@
<div class="modal fade" id="DuplicateActionsModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" area-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <img class="w-100" src="~/img/isomatric/confirmation.svg" alt="Duplicate Actions" />
            </div>
            <div class="modal-body text-center pt-5">
                <h5 class="fw-bold">Confirmation</h5>
                <h6 class="fw-bold">Are you sure?</h6>Do you want to change your credentials? <p>Once the credential was updated, couldn't retrieve the password.</p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-target="#CreateModal" data-bs-toggle="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="duplicateConfirmation">Yes</button>
            </div>
        </div>
    </div>
</div>
@* Duplicate Actions *@
<div class="modal" id="DuplicateActionsModal" tabindex="-1" area-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <img class="w-100" src="~/img/isomatric/confirmation.svg" alt="Duplicate Actions" />
            </div>
            <div class="modal-body text-center pt-5">
                <h5 class="fw-bold">Confirmation</h5>
                <h6 class="fw-bold">Are you sure?</h6>Do you want to change credential. <p>Once credential was updated couldn't retrive password</p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-target="#CreateModal" data-bs-toggle="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="duplicateConfirmation">Yes</button>
            </div>
        </div>
    </div>
</div>
<script src="~/js/Configuration/Bulk Database Credential/BulkDatabase.js"></script>