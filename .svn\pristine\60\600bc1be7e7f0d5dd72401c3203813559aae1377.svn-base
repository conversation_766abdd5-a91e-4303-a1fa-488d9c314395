﻿using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Application.UnitTests.Mocks
{
    public class DatabaseViewRepositoryMocks
    {
        public static Mock<IDatabaseViewRepository> GetPaginatedDatabaseViewRepository(List<DatabaseView> databaseViews)
        {
            var databaseViewRepository = new Mock<IDatabaseViewRepository>();

            var queryableDatabaseView = databaseViews.BuildMock();

            databaseViewRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableDatabaseView);

            return databaseViewRepository;
        }

        public static Mock<IDatabaseViewRepository> GetDatabaseViewTypeRepository(List<DatabaseView> databaseViews)
        {
            var databaseViewRepository = new Mock<IDatabaseViewRepository>();

            var queryableDatabaseView = databaseViews.BuildMock();

            databaseViewRepository.Setup(repo => repo.GetDatabaseByType(It.IsAny<string>())).Returns(queryableDatabaseView);

            return databaseViewRepository;
        }
    }
}
