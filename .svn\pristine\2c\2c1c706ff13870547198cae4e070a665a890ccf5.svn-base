﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.Database.Queries.GetPaginatedList;

public class
    GetDatabasePaginatedListQueryHandler : IRequestHandler<GetDatabasePaginatedListQuery,
        PaginatedResult<DatabaseListVm>>
{
    private readonly IDatabaseViewRepository _databaseViewRepository;
    private readonly IMapper _mapper;

    public GetDatabasePaginatedListQueryHandler(IMapper mapper, IDatabaseViewRepository databaseViewRepository)
    {
        _mapper = mapper;
        _databaseViewRepository = databaseViewRepository;
    }

    public async Task<PaginatedResult<DatabaseListVm>> Handle(GetDatabasePaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new DatabaseFilterSpecification(request.SearchString);

        var databases = request.DatabaseTypeId != null
           ?await _databaseViewRepository.GetDatabaseByType(request.PageNumber,request.PageSize,productFilterSpec ,request.DatabaseTypeId, request.SortColumn, request.SortOrder)
           :await  _databaseViewRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var databaseList = _mapper.Map<PaginatedResult<DatabaseListVm>>(databases);
      
        return databaseList;
        //var databases = request.DatabaseTypeId != null
        //    ? _databaseViewRepository.GetDatabaseByType(request.DatabaseTypeId)
        //    : _databaseViewRepository.GetPaginatedQuery();

        //var productFilterSpec = new DatabaseFilterSpecification(request.SearchString);

        //var databaseList = await databases
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<DatabaseListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //// databaseList.Data.ForEach(x => { x.LicenseKey = SecurityHelper.Decrypt(x.LicenseKey); });
        ////await _publisher.Publish(new DatabasePaginatedEvent(), cancellationToken);

        //return databaseList;
    }
}