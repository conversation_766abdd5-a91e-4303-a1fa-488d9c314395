﻿using ContinuityPatrol.Application.Features.RsyncJob.Queries.GetPaginated;
using ContinuityPatrol.Domain.ViewModels.RsyncJobModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.RsyncJob.Queries
{
    public class GetRsyncJobPaginatedQueryHandlerTests
    {
        private readonly Mock<IRsyncJobRepository> _mockRsyncJobRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetRsyncJobPaginatedQueryHandler _handler;

        public GetRsyncJobPaginatedQueryHandlerTests()
        {
            _mockRsyncJobRepository = new Mock<IRsyncJobRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new GetRsyncJobPaginatedQueryHandler(_mockRsyncJobRepository.Object, _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ReturnsPaginatedResult_WhenDataExists()
        {
            var rsyncJobs = new List<Domain.Entities.RsyncJob>
            {
                new Domain.Entities.RsyncJob { Id = 1, ReplicationName = "Job 1" },
                new Domain.Entities.RsyncJob { Id = 2, ReplicationName = "Job 2" },
                new Domain.Entities.RsyncJob { Id = 3, ReplicationName = "Job 3" }
            };

            var paginatedJobs = new PaginatedResult<Domain.Entities.RsyncJob>();

            var rsyncJobVms = rsyncJobs.Select(job => new RsyncJobListVm { Id = Guid.NewGuid().ToString(), ReplicationName = job.ReplicationName }).ToList();

            _mockRsyncJobRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(ToString);

            _mockMapper
                .Setup(mapper => mapper.Map<RsyncJobListVm>(It.IsAny<Domain.Entities.RsyncJob>()))
                .Returns((Domain.Entities.RsyncJob job) => rsyncJobVms.FirstOrDefault(vm => vm.Equals == job.Equals));

            var query = new GetRsyncJobPaginatedQuery
            {
                PageNumber = 1,
                PageSize = 3,
                SearchString = "Job"
            };

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(3, result.Data.Count);
            Assert.Equal("Job 1", result.Data[0].ReplicationName);
            Assert.Equal("Job 2", result.Data[1].ReplicationName);
            Assert.Equal("Job 3", result.Data[2].ReplicationName);

            _mockRsyncJobRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<RsyncJobListVm>(It.IsAny<Domain.Entities.RsyncJob>()), Times.Exactly(3));
        }

        [Fact]
        public async Task Handle_ReturnsEmptyPaginatedResult_WhenNoDataExists()
        {
            var paginatedJobs = new PaginatedResult<Domain.Entities.RsyncJob>();

            _mockRsyncJobRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(ToString);

            var query = new GetRsyncJobPaginatedQuery
            {
                PageNumber = 1,
                PageSize = 3,
                SearchString = "NonExistentJob"
            };

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);

            _mockRsyncJobRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<RsyncJobListVm>(It.IsAny<Domain.Entities.RsyncJob>()), Times.Never);
        }
    }
}
