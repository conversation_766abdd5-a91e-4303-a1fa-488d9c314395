﻿using ContinuityPatrol.Application.Features.Server.Commands.TestConnection;
using ContinuityPatrol.Application.Features.Server.Events.TestConnection;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Server.Commands
{
    public class ServerTestConnectionTests 
    {
        private readonly Mock<ILoadBalancerRepository> _mockNodeConfigurationRepository;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly Mock<IServerRepository> _mockServerRepository;
        private readonly Mock<IWindowsService> _mockWindowsService;
        private readonly Mock<IServerViewRepository> _mockServerViewRepository;
        private readonly Mock<ILicenseManagerRepository> _mockLicenseManagerRepository;
        private readonly Mock<ILicenseValidationService> _mockLicenseValidationService;
        private readonly Mock<ILogger<ServerTestConnectionCommandHandler>> _mockLogger;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IJobScheduler> _mockJobScheduler;
        private readonly ServerTestConnectionCommandHandler _handler;

        public ServerTestConnectionTests()
        {
            _mockNodeConfigurationRepository = new Mock<ILoadBalancerRepository>();
            _mockPublisher = new Mock<IPublisher>();
            _mockServerRepository = new Mock<IServerRepository>();
            _mockWindowsService = new Mock<IWindowsService>();
            _mockJobScheduler = new Mock<IJobScheduler>();

            _handler = new ServerTestConnectionCommandHandler(
                _mockServerRepository.Object,
                _mockServerViewRepository.Object,
                _mockMapper.Object,
                _mockNodeConfigurationRepository.Object,
                _mockWindowsService.Object,
                _mockPublisher.Object,
                _mockJobScheduler.Object,_mockLicenseManagerRepository.Object,_mockLicenseValidationService.Object,_mockLogger.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnResponse_WhenTestConnectionIsSuccessful()
        {
            var request = new ServerTestConnectionCommand
            {
                Id = new List<string> { "{\"id\": [\"123\", \"456\"]}" }
            };

            var nodeConfig = new Domain.Entities.LoadBalancer
            {
                ConnectionType = "http",
                IPAddress = "127.0.0.1",
                Port = 8080,
                TypeCategory = "Load Balancer"
            };

            var serverEntities = new List<Domain.Entities.Server>
            {
                new Domain.Entities.Server { Name = "Server1", LicenseKey = "Key1" },
                new Domain.Entities.Server { Name = "Server2", LicenseKey = "Key2" }
            };

            _mockNodeConfigurationRepository.Setup(repo => repo.GetNodeConfigurationByTypeAndTypeCategory("ALL", "Load Balancer"))
                .ReturnsAsync(nodeConfig);
            _mockWindowsService.Setup(service => service.CheckWindowsService(It.IsAny<string>()))
                .ReturnsAsync(new ServiceResponse { Success = true });
            _mockServerRepository.Setup(repo => repo.GetByServerIdsAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(serverEntities);
            _mockServerRepository.Setup(repo => repo.UpdateRangeAsync(It.IsAny<List<Domain.Entities.Server>>()))
                .ReturnsAsync(serverEntities);
            _mockPublisher.Setup(publisher => publisher.Publish(It.IsAny<ServerTestConnectionEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);
            _mockJobScheduler.Setup(client => client.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
                .Returns(Task.CompletedTask);

            var response = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(response);
            Assert.Contains("Server 'Server1, Server2' test connection request sent successfully.", response.Message);

            _mockNodeConfigurationRepository.Verify(repo => repo.GetNodeConfigurationByTypeAndTypeCategory("ALL", "Load Balancer"), Times.Once);
            _mockWindowsService.Verify(service => service.CheckWindowsService(It.IsAny<string>()), Times.Once);
            _mockServerRepository.Verify(repo => repo.UpdateRangeAsync(It.IsAny<List<Domain.Entities.Server>>()), Times.Once);
            _mockPublisher.Verify(publisher => publisher.Publish(It.IsAny<ServerTestConnectionEvent>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockJobScheduler.Verify(client => client.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowInvalidException_WhenLoadBalancerNotConfigured()
        {
            var request = new ServerTestConnectionCommand
            {
                Id = new List<string> { "{\"id\": [\"123\"]}" }
            };

            _mockNodeConfigurationRepository.Setup(repo => repo.GetNodeConfigurationByTypeAndTypeCategory("ALL", "Load Balancer"))
                .ReturnsAsync((Domain.Entities.LoadBalancer)null);

            await Assert.ThrowsAsync<InvalidException>(() => _handler.Handle(request, CancellationToken.None));

            _mockWindowsService.Verify(service => service.CheckWindowsService(It.IsAny<string>()), Times.Never);
            _mockServerRepository.Verify(repo => repo.UpdateRangeAsync(It.IsAny<List<Domain.Entities.Server>>()), Times.Never);
            _mockPublisher.Verify(publisher => publisher.Publish(It.IsAny<ServerTestConnectionEvent>(), It.IsAny<CancellationToken>()), Times.Never);
            _mockJobScheduler.Verify(client => client.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldThrowWindowServiceException_WhenMonitorCheckFails()
        {
            var request = new ServerTestConnectionCommand
            {
                Id = new List<string> { "{\"id\": [\"123\"]}" }
            };

            var nodeConfig = new Domain.Entities.LoadBalancer
            {
                ConnectionType = "http",
                IPAddress = "127.0.0.1",
                Port = 8080,
                TypeCategory = "Load Balancer"
            };

            _mockNodeConfigurationRepository.Setup(repo => repo.GetNodeConfigurationByTypeAndTypeCategory("ALL", "Load Balancer"))
                .ReturnsAsync(nodeConfig);
            _mockWindowsService.Setup(service => service.CheckWindowsService(It.IsAny<string>()))
                .ReturnsAsync(new ServiceResponse { Success = false, Message = "Service not running" });

            await Assert.ThrowsAsync<WindowServiceException>(() => _handler.Handle(request, CancellationToken.None));

            _mockServerRepository.Verify(repo => repo.UpdateRangeAsync(It.IsAny<List<Domain.Entities.Server>>()), Times.Never);
            _mockPublisher.Verify(publisher => publisher.Publish(It.IsAny<ServerTestConnectionEvent>(), It.IsAny<CancellationToken>()), Times.Never);
            _mockJobScheduler.Verify(client => client.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Never);
        }
    }
}
