﻿const monitoringServiceDetails = async () => {
    await $.ajax({
        type: "POST",
        url: RootUrl + monitorURL.checkWindowServiceurl,
        data: { type: 'monitor', __RequestVerificationToken: gettoken() },
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.success) {
                if (result && result.success) {
                    let html = monitoringMessage(result)
                    notificationAlert("success", html, 'execution')
                } else {
                    notificationAlert("warning", response.message);
                }

            } else {
                errorNotification(result)
            }
        }
    })
}

const monitoringMessage = (result) => {
    let html = ''
    if (result?.activeNodes && Array.isArray(result?.activeNodes) && result?.activeNodes?.length) {
        for (let i = 0; i < result?.activeNodes?.length; i++) {
            html += `<div class='mb-1'><i class="cp-network fs-5 text-success" ></i> '${result.activeNodes[i]}'</div>`;
        }
    }
    if (result?.inActiveNodes && Array.isArray(result?.activeNodes) && result?.inActiveNodes?.length) {
        for (let i = 0; i < result?.inActiveNodes?.length; i++) {
            html += `<div class='mb-1'><i class="cp-network fs-5 text-danger" ></i> '${result.inActiveNodes[i]}'</div>`;
        }
    }
    return html;
}

async function populateMSFields(monitorData) {
    $('#msBusinessService').val(monitorData.businessServiceName);
    await getInfraObjectsByBusinessServiceId(monitorData?.businessServiceId).then(async () => {
        $('#msInfraObject').val(monitorData.infraObjectName).trigger('change');
        await infraChange(monitorData.infraObjectId).then(async () => {
            if (monitorData.monitoringType) {
                $('#msMonitoringType').show();
                const radio = document.querySelector(`.serverMSRadio[value="${monitorData.monitoringType}"]`);
                if (radio) {
                    radio.checked = true;
                } else {
                    monitorData.monitoringType = 'PR'
                    $('#msMonitoringType').hide();
                }
                await getServersByInfraObjectId(monitorData.infraObjectId, monitorData.monitoringType).then(async () => {
                    $('#serverMS').val(monitorData.serverName);
                });
                await getServerData(monitorData.serverId).then(async () => {
                    $('#authenticationTypeMS').val(monitorData.type).trigger('change');
                })
            }
        })
    })
    const properties = JSON.parse(monitorData.properties);
    msId = monitorData.id;

    $('.msradio[value="' + properties.SubType + '"]').prop("checked", true);
    const WorkflowType = properties.details?.[0]?.type || '';
    $('#msWorkflowType').val(WorkflowType);
    properties.details.forEach(item => {
        addPaths(item.name, item.id, item.type);
    });
    $('#serverMS').attr({
        ServiceStatus: monitorData.isServiceUpdate ?? '',
        Status: monitorData.status ?? ''
    });

    let isWorkflow = monitorData.type === "Use Workflow";
    $('#workflowType, #workflow').toggle(isWorkflow);
    $('#command').toggle(!isWorkflow);
    if (isWorkflow) {
        getWorkflowsByType(monitorData.infraObjectId, WorkflowType, monitorData.workflowId, monitorData.workflowName);
    } else {
        let isService = $('.msradio:checked').val() === "Service";
        $('#msServiceDiv').toggle(isService);
        $('#msProcessDiv').toggle(!isService);
        $(isService ? '#msServiceName' : '#msProcessName').val(monitorData.servicePath);
    }
}

async function getInfraObjectsByBusinessServiceId(businessServiceId) {
    let infraDropdown = $('#msInfraObject').empty();
    let url = `${RootUrl}${monitorURL.getInfraObjectsByBusinessServiceId}`;
    let data = { businessServiceId };
    let result = await getAysncWithHandler(url, data, OnError);
    infraDropdown.append('<option value="">Select InfraObject</option>');
    result && result?.forEach(({ id, name }) => {
        if (id && name) {
            infraDropdown.append(`<option data-infraid="${id}" value="${name}">${name}</option>`);
        }
    });
};

async function getServers(infraObjectId) {
    let url = `${RootUrl}${monitorURL.getServersByInfraObjectId}`;
    let data = { infraObjectId };
    return result = await getAysncWithHandler(url, data, OnError);
}

async function getServersByInfraObjectId(infraObjectId, ServerType) {
    let serverDropdown = $('#serverMS').empty();
    $('#authenticationTypeMS').empty();
    let result = await getServers(infraObjectId);
    serverDropdown.append('<option value="">Select Server</option>');
    let servers = JSON?.parse(result?.serverProperties || '{}');
    if (servers && typeof servers === 'object' && Object.keys(servers).length > 0) {
        let typesToBind = [];
        if (ServerType === 'All') {
            typesToBind = Object.keys(servers);
        } else if (servers[ServerType]) {
            typesToBind = [ServerType];
        }
        typesToBind.forEach(type => {
            let { id, name } = servers[type];
            let ids = id?.split(',');
            let names = name?.split(',');
            ids.forEach((serverId, i) => {
                let serverName = names[i] || names[names.length - 1];
                //allServerIds.push({ serverId: serverId, serverName: serverName }); deadcode
                serverDropdown.append(
                    `<option data-serverid="${serverId}" value="${serverName}">${serverName}</option>`
                );
            });
        });

        let totalOptions = serverDropdown.find('option').length;
        if (totalOptions <= 2) {
            serverDropdown.prop('selectedIndex', 1);
            serverDropdown.prop('disabled', true);
            serverDropdown.trigger('change');
        } else {
            serverDropdown.prop('disabled', false);
        }
    }
    if (ServerType === "All") {
        serverDropdown.prop('selectedIndex', 1);
        serverDropdown.prop('disabled', true);
        serverDropdown.trigger('change');
        $('#MSServerDiv').hide();
    }
}

async function msCreateOrUpdate(commonData) {
    await $.ajax({
        url: RootUrl + monitorURL.createOrUpdate,
        type: "POST",
        dataType: "json",
        data: commonData,
        success: function (result) {
            if (result && typeof result === 'object' && result?.success) {
                notificationAlert("success", result?.data);
                $('#createMSModal').modal('hide');
                setTimeout(() => dataTable.ajax.reload(), 1500);
            } else {
                errorNotification(result);
            }
        }
    });
};

async function getServerData(id) {
    let authDropdown = $('#authenticationTypeMS').empty();
    let serverId = id?.includes(',') ? id.split(',')[0] : id;
    let data = { serverId };
    let url = RootUrl + monitorURL.getServerDataByServerId;
    await $.ajax({
        type: "GET",
        url: url,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result && typeof result === 'object' && Object.hasOwn(result, 'osType')) {
                let authOptions = {
                    Linux: ["Use ps -ef", "Use Workflow"],
                    Windows: ["Use WMI", "Use Workflow", "Use SSH", "Use PowerShell"],
                    AS400: ["Use WMI", "Use Workflow", "Use SSH", "Use PowerShell", "Use Telnet"],
                    default: ["Use ps -ef", "Use Workflow"]
                };
                let options = authOptions[result?.osType] || authOptions.default;
                authDropdown.append(new Option("Select Server Authentication Type", ""));
                options.forEach(option => authDropdown.append(new Option(option, option)));

            } else {
                authDropdown.append(new Option("Authentication type Not Found", ""));
            }
        }
    })
};

async function getWorkflowsByType(infraId, actionType, selectedId = '', selectedName = '') {
    let url = RootUrl + monitorURL.getWorkflowByType;
    let data = { infraId, actionType };
    let result = await getAysncWithHandler(url, data, OnError);
    let Workflow = $('#workflowMS').empty().append('<option value="">Select Workflow Name</option>');
    result?.forEach(({ workflowId, workflowName }) => {
        if (workflowId && workflowName) {
            Workflow.append(`<option value="${workflowId}">${workflowName}</option>`);
        }
    });
    if (selectedId?.length > 1) {
        Workflow.val(selectedId);
    }
}

async function addPaths(data, Id, Type) {
    if (data) {
        const trimmedData = data.trim();
        const alreadyExists = addedname.some(item => item.name === trimmedData);
        if (!alreadyExists) {
            addedname.push({ name: trimmedData, id: Id, type: Type });
            $("#msAddedName").show();
            updateSelectedPaths();
        }
    }
}

async function updateSelectedPaths() {
    $("#selectedPathDetails").empty();
    if (addedname?.length === 0) {
        $("#msAddedName").hide();
    }
    addedname && addedname?.forEach((versionObj, index) => {
        const versionElement = document.createElement("span");
        versionElement.style.marginRight = "10px";

        const addValue = document.createElement("button");
        addValue.innerHTML = `${versionObj.name}<span >&nbsp; X</span>`;
        addValue.className = `remove-button btn rounded-pill btn-sm shadow mt-2`;
        addValue.title = "Remove";
        addValue.setAttribute("data-id", versionObj.id);
        addValue.style.backgroundColor = "#95c9ff";
        addValue.setAttribute("data-type", versionObj.type);
        addValue.addEventListener("click", function () {
            removePaths(index);
        });
        versionElement.appendChild(addValue);
        $("#selectedPathDetails").append(versionElement);
    });
}

async function getSelectedPathDetails() {
    const values = [];
    document.querySelectorAll('#selectedPathDetails button').forEach(button => {
        let clone = button.cloneNode(true);
        [...clone.children].forEach(child => clone.removeChild(child));
        let name = clone.textContent.trim();
        values.push({ name });
    });
    return values;
}

async function getSelectedWorkflowDetails() {
    const values = [];
    document.querySelectorAll('#selectedPathDetails button').forEach(button => {
        const clone = button.cloneNode(true);
        [...clone.children].forEach(child => clone.removeChild(child));
        const name = clone.textContent.trim();
        const id = button.dataset.id;
        const type = button.dataset.type;
        values.push({ id, name, type });
    });
    return values;
}

async function removePaths(index) {
    addedname.splice(index, 1);
    updateSelectedPaths();
}

async function infraChange(infraID) {
    $('#serverMS,#authenticationTypeMS').empty();
    $('#workflowType, #workflow, #command,#msAddedName').hide();
    $('#msServiceName, #msWorkflowType, #workflowMS').val('');
    $('.serverMSRadio').prop('checked', false);
    $('#serverMS').prop('disabled', false);
    const servers = await getServers(infraID);
    const infraServers = JSON?.parse(servers?.serverProperties || '{}');
    let serverCount = 0;
    const keys = Object.keys(infraServers);
    keys.forEach(key => {
        const ids = infraServers[key].id?.split(',') || [];
        serverCount += ids.length;
    });
    const msMonitoringType = $('#msMonitoringType .row').empty(); // clear existing radios

    if (keys.length > 1) {
        keys.forEach((key, idx) => {
            const radio = `
                <div class="col-auto col">
                    <div class="form-check">
                        <input name="MonitoringType" type="radio" class="form-check-input serverMSRadio" value="${key}" id="radio-${key}">
                        <label class="form-check-label" for="radio-${key}">${key}</label>
                    </div>
                </div>
            `;
            msMonitoringType.append(radio);
        });
        msMonitoringType.append(`
            <div class="col-auto col">
                <div class="form-check">
                    <input name="MonitoringType" type="radio" class="form-check-input serverMSRadio" value="All" id="radio-All">
                    <label class="form-check-label" for="radio-All">All</label>
                </div>
            </div>
        `);

        $('#msMonitoringType').show();
    } else {
        $('#msMonitoringType').hide();
        const serverKey = keys[0] || '';
        await getServersByInfraObjectId(infraID, serverKey);
    }
}

//Validations-functions

async function isNameExist(url, inputValue, fieldName) {
    if (!inputValue || !inputValue[fieldName] || !inputValue[fieldName].trim()) {
        return true;
    }
    return (await getAysncWithHandler(url, inputValue, OnError)) ? " Name already exists" : true;
}

async function pathNameValidate(value, id = null, infraObjectId, type, serverId, threadType, urlPath, errorMsg, errorElement) {
    let isValid = !!value;
    errorElement.text(isValid ? '' : errorMsg).toggleClass('field-validation-error', !isValid);
    return isValid;
}

async function monitoringServiceValidate(value, errorMessage, errorElement) {
    let isValid = !!value;
    errorElement.text(isValid ? '' : errorMessage).toggleClass('field-validation-error', !isValid);
    return isValid;
}

async function workflowNameValidate(value, id = null, infraId, type, serverId, workflowType, workflowId, urlPath, errorMessage, errorElement) {
    let url = RootUrl + urlPath;
    let data = { workflowId: value, id, infraObjectId: infraId, type, serverId, workflowType, workflowName: workflowId };
    if (!value) {
        errorElement.text(errorMessage).addClass('field-validation-error');
        return false;
    }
    let validationResult = await isNameExist(url, data, "workflowId", OnError);
    return CommonValidation(errorElement, [validationResult]);
}

async function monitorServiceValidateFields() {
    let errorElements = ['#BusinessServiceId-error', '#InfraObjectId-error', '#ServerId-error', '#Type-error', '#WorkflowType-error', '#WorkflowId-error', '#ServicePath-error', '#ThreadType-error', '#ProcessPath-error', '#MonitoringType-error'];
    clearInputFields('CreateForm', errorElements);
}
