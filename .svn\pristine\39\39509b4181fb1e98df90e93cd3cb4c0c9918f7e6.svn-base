﻿using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;

namespace ContinuityPatrol.Application.Features.DashboardView.Queries.GetDatalagByBusinessServiceId;

public class GetDataLagDetailByBusinessServiceIdQueryHandler:IRequestHandler<GetDataLagDetailByBusinessServiceIdQuery,DatalagByBusinessServiceIdVm>
{
    private readonly IDatalagImpactAvailabilityViewRepository _datalagImpactAvailabilityView;
    public GetDataLagDetailByBusinessServiceIdQueryHandler(IDatalagImpactAvailabilityViewRepository datalagImpactAvailabilityView)
    {
        _datalagImpactAvailabilityView = datalagImpactAvailabilityView;
    }

    public async Task<DatalagByBusinessServiceIdVm> Handle(GetDataLagDetailByBusinessServiceIdQuery request, CancellationToken cancellationToken)
    {
        var datalagDtl = await _datalagImpactAvailabilityView.GetByBusinessServiceId(request.BusinessServiceId);

        Guard.Against.NullOrDeactive(datalagDtl, nameof(Domain.Views.DatalagImpactAvailabilityView),
            new NotFoundException(nameof(Domain.Views.DatalagImpactAvailabilityView), request.BusinessServiceId));

        var dataLagDetailDto = new DatalagByBusinessServiceIdVm
        {
            Id= datalagDtl.ReferenceId,
            BusinessServiceId= datalagDtl.ReferenceId,
            BusinessServiceName= datalagDtl.BusinessServiceName,
            TotalBusinessFunctionCount= datalagDtl.TotalBusinessFunctionCount,
            BusinessFunctionNotAvailable= datalagDtl.BusinessFunctionNotAvailable,
            BusinessFunctionRPOExceededCount= datalagDtl.BusinessFunctionImpactCount,
            BusinessFunctionThresholdExceededCount= datalagDtl.BusinessFunctionPartialImpactCount,
            BusinessFunctionUnderRPOCount=datalagDtl.BusinessFunctionAvailableCount,

            TotalInfraObjectCount=datalagDtl.TotalInfraObjectCount,
            InfraObjecNotAvailableCount=datalagDtl.InfraNotAvailableCount,
            InfraRPOExceededCount= datalagDtl.InfraMajorImpactCount,
            InfraThresholdExceededCount= datalagDtl.InfraPartialImpactCount,
            InfraUnderRPOCount= datalagDtl.InfraAvailableCount
        };
        
        return dataLagDetailDto;

       
    }
}
