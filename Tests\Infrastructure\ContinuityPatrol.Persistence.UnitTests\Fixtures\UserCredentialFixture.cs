using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class UserCredentialFixture : IDisposable
{
    public List<UserCredential> UserCredentialPaginationList { get; set; }
    public List<UserCredential> UserCredentialList { get; set; }
    public UserCredential UserCredentialDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public UserCredentialFixture()
    {
        var fixture = new Fixture();

        UserCredentialList = fixture.Create<List<UserCredential>>();

        UserCredentialPaginationList = fixture.CreateMany<UserCredential>(20).ToList();

        UserCredentialDto = fixture.Create<UserCredential>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
