using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class OracleMonitorLogsFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "Oracle";

    public List<OracleMonitorLogs> OracleMonitorLogsPaginationList { get; set; }
    public List<OracleMonitorLogs> OracleMonitorLogsList { get; set; }
    public OracleMonitorLogs OracleMonitorLogsDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public OracleMonitorLogsFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<OracleMonitorLogs>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow));

        OracleMonitorLogsPaginationList = _fixture.CreateMany<OracleMonitorLogs>(20).ToList();
        OracleMonitorLogsList = _fixture.CreateMany<OracleMonitorLogs>(5).ToList();
        OracleMonitorLogsDto = _fixture.Create<OracleMonitorLogs>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public OracleMonitorLogs CreateOracleMonitorLogsWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<OracleMonitorLogs>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public OracleMonitorLogs CreateOracleMonitorLogsWithWhitespace()
    {
        return CreateOracleMonitorLogsWithProperties(type: "  Oracle  ");
    }

    public List<OracleMonitorLogs> CreateOracleMonitorLogsWithDateRange(DateTime startDate, DateTime endDate, string infraObjectId = null)
    {
        var logs = new List<OracleMonitorLogs>();
        var currentDate = startDate;

        while (currentDate <= endDate)
        {
            logs.Add(CreateOracleMonitorLogsWithProperties(
                infraObjectId: infraObjectId ?? InfraObjectId,
                createdDate: currentDate,
                isActive: true));
            currentDate = currentDate.AddDays(1);
        }

        return logs;
    }

    public List<OracleMonitorLogs> CreateMultipleOracleMonitorLogsWithSameType(string type, int count)
    {
        var logs = new List<OracleMonitorLogs>();
        for (int i = 0; i < count; i++)
        {
            logs.Add(CreateOracleMonitorLogsWithProperties(type: type, isActive: true));
        }
        return logs;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
