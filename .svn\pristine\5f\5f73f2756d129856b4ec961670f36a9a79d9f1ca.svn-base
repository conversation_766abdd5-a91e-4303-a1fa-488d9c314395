﻿using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.ApprovalMatrix.Validators;

public class UpdateApprovalMatrixValidatorTests
{
    private readonly Mock<IApprovalMatrixRepository> _mockApprovalMatrixRepository;

    public UpdateApprovalMatrixValidatorTests()
    {
        var approvalMatrices = new Fixture().Create<List<Domain.Entities.ApprovalMatrix>>();

        _mockApprovalMatrixRepository = ApprovalMatrixRepositoryMocks.UpdateApprovalMatrixRepository(approvalMatrices);
    }

     //Name

     [Theory]
     [AutoApprovalMatrixData]
     public async Task Verify_Update_Name_InApprovalMatrix_WithEmpty(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
     {
       var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

      updateApprovalMatrixCommand.Name = "";

      var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
      Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameRequired, (string)validateResult.Errors[0].ErrorMessage);
      }

    [Theory]
    [AutoApprovalMatrixData]
     public async Task Verify_Update_Name_InApprovalMatrix_IsNull(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
      {
       var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

       updateApprovalMatrixCommand.Name = null;

       var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameNotNullRequired, (string)validateResult.Errors[1].ErrorMessage);
      }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Name_InApprovalMatrix_MinimumRange(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "PT";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameRangeRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Name_InApprovalMatrix_MaximumRange(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIHGFEDCBA";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameRangeRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Valid_Name_InApprovalMatrix(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = " PTS ";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Valid_Name_InApprovalMatrix_With_DoubleSpace_InFront(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "  PTS";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Valid_Name_InApprovalMatrix_With_DoubleSpace_InBack(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "PTS  ";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Valid_Name_InApprovalMatrix_With_TripleSpace_InBetween(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "PTS   Test";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Valid_Name_InApprovalMatrix_With_SpecialCharacters_InFront(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "&^^%PTS Test";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Valid_Name_InApprovalMatrix_With_SpecialCharacters_InBack(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "PTS Test*&^%";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Valid_Name_InApprovalMatrix_With_SpecialCharacters_InBetween(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "PTS*&&^%Test";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Valid_Name_InApprovalMatrix_With_SpecialCharacters_Only(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "*&%*&&^%";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Valid_Name_InApprovalMatrix_With_UnderScore_InFront(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "_PTS Test";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Valid_Name_InApprovalMatrix_With_UnderScore_InBack(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "PTS Test_";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Valid_Name_InApprovalMatrix_With_UnderScore_InFront_AndBack(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "_PTS Test_";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Valid_Name_InApprovalMatrix_With_Numbers_InFront(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "764PTS Test";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Valid_Name_InApprovalMatrix_With_UnderScore_InFront_AndNumbers_InBack(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "_PTS Test546";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Valid_Name_InApprovalMatrix_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "_436PTS Test_";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Valid_Name_InApprovalMatrix_With_Numbers_Only(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = "89871323236";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    //Description

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Description_InApprovalMatrix_WithEmpty(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

      //  updateApprovalMatrixCommand.UserName = null;

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixDescriptionContainsRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_Description_InApprovalMatrix_MaximumRange(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Description = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixDescriptionRangeRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    //UserName

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_UserName_InApprovalMatrix_WithEmpty(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

      //  updateApprovalMatrixCommand.UserName = "";

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixDescriptionContainsRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_UserName_InApprovalMatrix_IsNull(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.Name = null;
        updateApprovalMatrixCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameNotNullRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    //BusinessServiceName

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_BusinessServiceName_InApprovalMatrix_WithEmpty(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        // updateApprovalMatrixCommand.UserName = "";
        updateApprovalMatrixCommand.BusinessServiceName = "";
        updateApprovalMatrixCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixBusinessServiceNameRequired, (string)validateResult.Errors[2].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Update_BusinessServiceName_InApprovalMatrix_IsNull(UpdateApprovalMatrixCommand updateApprovalMatrixCommand)
    {
        var validator = new UpdateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        updateApprovalMatrixCommand.BusinessServiceName = null;
        updateApprovalMatrixCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixBusinessServiceNameNotNullRequired, (string)validateResult.Errors[3].ErrorMessage);
    }
}