﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.MonitorService.Event.UpdateStatus;

public class MonitorServiceStatusUpdatedEventHandler : INotificationHandler<MonitorServiceStatusUpdatedEvent>
{
    private readonly ILogger<MonitorServiceStatusUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public MonitorServiceStatusUpdatedEventHandler(ILogger<MonitorServiceStatusUpdatedEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(MonitorServiceStatusUpdatedEvent statusUpdatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.MonitorService.ToString(),
            Action = $"{ActivityType.Update} {Modules.MonitorService}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails =
                $"Monitoring Service '{statusUpdatedEvent.ServerName}' Status '{statusUpdatedEvent.Status}' successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation(
            $"Monitoring Service '{statusUpdatedEvent.ServerName}' Status '{statusUpdatedEvent.Status}' successfully.");
    }
}