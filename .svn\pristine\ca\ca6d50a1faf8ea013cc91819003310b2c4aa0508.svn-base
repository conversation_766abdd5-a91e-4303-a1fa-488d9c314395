using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class BusinessServiceAvailabilityFixture : IDisposable
{
    public List<BusinessServiceAvailability> BusinessServiceAvailabilityPaginationList { get; set; }
    public List<BusinessServiceAvailability> BusinessServiceAvailabilityList { get; set; }
    public BusinessServiceAvailability BusinessServiceAvailabilityDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public BusinessServiceAvailabilityFixture()
    {
        var fixture = new Fixture();

        BusinessServiceAvailabilityList = fixture.Create<List<BusinessServiceAvailability>>();

        BusinessServiceAvailabilityPaginationList = fixture.CreateMany<BusinessServiceAvailability>(20).ToList();

        BusinessServiceAvailabilityPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BusinessServiceAvailabilityPaginationList.ForEach(x => x.IsActive = true);

        BusinessServiceAvailabilityList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BusinessServiceAvailabilityList.ForEach(x => x.IsActive = true);

        BusinessServiceAvailabilityDto = fixture.Create<BusinessServiceAvailability>();
        BusinessServiceAvailabilityDto.ReferenceId = Guid.NewGuid().ToString();
        BusinessServiceAvailabilityDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
