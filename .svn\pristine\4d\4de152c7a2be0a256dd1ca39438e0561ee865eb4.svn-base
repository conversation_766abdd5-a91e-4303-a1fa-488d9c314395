﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.UserInfraObject.Commands.Update;

public class UpdateUserInfraObjectCommand : IRequest<UpdateUserInfraObjectResponse>
{
    public string UserId { get; set; }

    public string Properties { get; set; }

    public int IsApplication { get; set; }

    [JsonIgnore] public int CreatedBy { get; set; }

    [JsonIgnore] public int LastModifiedBy { get; set; }
}