using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class UserLoginFixture : IDisposable
{
    public List<UserLogin> UserLoginPaginationList { get; set; }
    public List<UserLogin> UserLoginList { get; set; }
    public UserLogin UserLoginDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public UserLoginFixture()
    {
        var fixture = new Fixture();

        UserLoginList = fixture.Create<List<UserLogin>>();

        UserLoginPaginationList = fixture.CreateMany<UserLogin>(20).ToList();

        UserLoginDto = fixture.Create<UserLogin>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
