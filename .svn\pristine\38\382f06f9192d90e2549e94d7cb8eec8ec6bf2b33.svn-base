﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Repositories;

public class LicenseManagerRepository : BaseRepository<LicenseManager>, ILicenseManagerRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public LicenseManagerRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public Task<List<string>> GetMacAddress()
    {
        var macAddress = (
            from nic in NetworkInterface.GetAllNetworkInterfaces()
            where nic.OperationalStatus == OperationalStatus.Up
            select nic.GetPhysicalAddress().ToString()).ToList();

        return Task.FromResult(macAddress);
    }

    public async Task<List<LicenseManager>> GetLicensePoNumber()
    {
        return await _dbContext.LicenseManagers
            .Where(e => e.IsActive && e.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new LicenseManager
            { ReferenceId = x.ReferenceId, PoNumber = SecurityHelper.Decrypt(x.PoNumber), IsAmc = x.IsAmc, IsParent = x.IsParent, CompanyId = x.CompanyId, ExpiryDate = SecurityHelper.Decrypt(x.ExpiryDate), Validity = SecurityHelper.Decrypt(x.Validity), IsPrimary = x.IsPrimary, Properties = SecurityHelper.Decrypt(x.Properties) })
            .ToListAsync();
    }

    public Task<string> GetHostName()
    {
        var hostname = Dns.GetHostName();
        return Task.FromResult(hostname);
    }

    public Task<string> GetDateByExpireTime(string expireTime, DateTime createDate, string endDateStr)
    {
        var expiry = expireTime.Split("-");
        var date = expiry[1].Split(" ");
        var expiryDate = date[0] == "Unlimited"
            ? DateTime.MaxValue.ToString("dd MMMM yyyy")
            : date[1] switch
            {
                "Month" => endDateStr.IsNullOrWhiteSpace() ? createDate.AddMonths(int.Parse(date[0])).ToString("dd MMMM yyyy") : GetEndDate(createDate, endDateStr),
                "Days" => endDateStr.IsNullOrWhiteSpace() ? createDate.AddDays(int.Parse(date[0])).ToString("dd MMMM yyyy") : GetEndDate(createDate, endDateStr),
                "Year" => endDateStr.IsNullOrWhiteSpace() ? createDate.AddYears(int.Parse(date[0])).ToString("dd MMMM yyyy") : GetEndDate(createDate, endDateStr),
                _ => throw new Exception("LicenseKey Type not match.")
            };

        return Task.FromResult(expiryDate);
    }

    private string GetEndDate(DateTime createDate, string endDateStr)
    {
        var licenseEndDate = DateTime.Now;

        if (endDateStr.IsNotNullOrWhiteSpace() &&
            DateTime.TryParse(endDateStr,
                CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedEndDate))
        {
            licenseEndDate = parsedEndDate;
        }


        var currentYear = createDate.Year;
        var currentMonth = createDate.Month;
        var currentDay = createDate.Day;
        var baseDate = new DateTime(currentYear, currentMonth, currentDay);

        var daysToAdd = (licenseEndDate - createDate).Days;

        var newDate = baseDate.AddDays(daysToAdd);

        return newDate.ToString("dd MMMM yyyy");
    }


    public async Task<LicenseManager> GetLicenseDetailByIdAsync(string id)
    {
        var detail = _loggedInUserService.IsParent
            ? await Entities.Active().AsNoTracking().Where(x => x.ReferenceId.Equals(id)).FirstOrDefaultAsync()
            : await Entities.Active().AsNoTracking()
                .Where(x => x.ReferenceId.Equals(id) && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .FirstOrDefaultAsync();

        return detail == null
            ? null
            : new LicenseManager
            {
                Id = detail.Id,
                ReferenceId = detail.ReferenceId,
                PoNumber = SecurityHelper.Decrypt(detail.PoNumber),
                CompanyId = detail.CompanyId,
                CompanyName = detail.CompanyName,
                HostName = SecurityHelper.Decrypt(detail.HostName),
                Properties = SecurityHelper.Decrypt(detail.Properties),
                IpAddress = SecurityHelper.Decrypt(detail.IpAddress),
                MacAddress = SecurityHelper.Decrypt(detail.MacAddress),
                LicenseKey = detail.LicenseKey,
                ParentId = detail.ParentId,
                IsParent = detail.IsParent,
                IsActive = detail.IsActive,
                Validity = SecurityHelper.Decrypt(detail.Validity),
                ExpiryDate = SecurityHelper.Decrypt(detail.ExpiryDate),
                ParentPoNumber = SecurityHelper.Decrypt(detail.ParentPoNumber),
                IsState = detail.IsState,
                IsAmc = detail.IsAmc,
                AmcPlan = SecurityHelper.Decrypt(detail.AmcPlan),
                AmcStartDate = detail.AmcStartDate,
                AmcEndDate = detail.AmcEndDate,
                IsExpired = detail.IsExpired
            };
    }

    public async Task<List<LicenseManager>> ListAllLicense()
    {
        var licenseList = _loggedInUserService.IsParent
            ? await _dbContext.LicenseManagers.Where(x => x.IsActive).ToListAsync()
            : await _dbContext.LicenseManagers
                .Where(x => x.IsActive && x.CompanyId.Equals(_loggedInUserService.CompanyId)).ToListAsync();
        return licenseList.Select(x => new LicenseManager
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            CompanyId = x.CompanyId,
            CompanyName = x.CompanyName,
            PoNumber = SecurityHelper.Decrypt(x.PoNumber),
            HostName = SecurityHelper.Decrypt(x.HostName),
            Properties = SecurityHelper.Decrypt(x.Properties),
            IpAddress = SecurityHelper.Decrypt(x.IpAddress),
            MacAddress = SecurityHelper.Decrypt(x.MacAddress),
            ParentId = x.ParentId,
            IsActive = x.IsActive,
            IsParent = x.IsParent,
            Validity = SecurityHelper.Decrypt(x.Validity),
            ExpiryDate = SecurityHelper.Decrypt(x.ExpiryDate),
            LicenseKey = x.LicenseKey,
            IsState = x.IsState,
            IsAmc = x.IsAmc,
            AmcPlan = SecurityHelper.Decrypt(x.AmcPlan),
            AmcStartDate = x.AmcStartDate,
            AmcEndDate = x.AmcEndDate,
            ParentPoNumber = SecurityHelper.Decrypt(x.ParentPoNumber),
            CreatedDate = x.CreatedDate,
            CreatedBy = x.CreatedBy,
            LastModifiedBy = x.LastModifiedBy,
            LastModifiedDate = x.LastModifiedDate,
            IsExpired = x.IsExpired
        }).OrderByDescending(x => x.ReferenceId).ToList();
    }

    public override IQueryable<LicenseManager> GetPaginatedQuery()
    {
        var paginatedList = _loggedInUserService.IsParent
            ? _dbContext.LicenseManagers.Where(x => x.IsActive).AsNoTracking()
                .OrderByDescending(x => x.ReferenceId).AsQueryable()
            : _dbContext.LicenseManagers.Where(x => x.IsActive && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .AsNoTracking()
                .OrderByDescending(x => x.ReferenceId).AsQueryable();

        return paginatedList
            .Select(x => new LicenseManager
            {
                ReferenceId = x.ReferenceId,
                PoNumber = SecurityHelper.Decrypt(x.PoNumber),
                CompanyId = x.CompanyId,
                CompanyName = x.CompanyName,
                HostName = SecurityHelper.Decrypt(x.HostName),
                Properties = SecurityHelper.Decrypt(x.Properties),
                IpAddress = SecurityHelper.Decrypt(x.IpAddress),
                IsState = x.IsState,
                IsAmc = x.IsAmc,
                AmcPlan = SecurityHelper.Decrypt(x.AmcPlan),
                AmcStartDate = x.AmcStartDate,
                AmcEndDate = x.AmcEndDate,
            })
            .AsNoTracking()
            .OrderByDescending(x => x.ReferenceId).AsQueryable();
    }

    public async Task<List<string>> GetIpAddress()
    {
        var hostEntry = await Dns.GetHostEntryAsync(Dns.GetHostName());
        var ipAddresses = hostEntry.AddressList
            .Where(ip => ip.AddressFamily == AddressFamily.InterNetwork) // Filter only IPv4 addresses if needed
            .Select(ip => ip.ToString())
            .ToList();
        return ipAddresses;
    }

    public Task<bool> IsLicenseKeyUnique(string licenseKey)
    {
        var licenseList = _dbContext.LicenseManagers
            .Select(x => new LicenseManager { LicenseKey = SecurityHelper.Decrypt(x.LicenseKey) }).ToList();

        var match = licenseList.Any(x => x.LicenseKey.Equals(SecurityHelper.Decrypt(licenseKey)));
        return Task.FromResult(match);
    }


    public async Task<bool> IsLicenseKeyUnique(string id, string licenseKey)
    {

        var licenses = await _dbContext.LicenseManagers
            .AsNoTracking()
            .Active()
            .Where(x => x.ReferenceId == id)
            .ToListAsync();

        var match = licenses.Any(x => SecurityHelper.Decrypt(x.LicenseKey) == licenseKey);

        return !match;
    }

    public Task<bool> IsLicenseKeyExist(string licenseKey, string id)
    {
        var licenseList = _dbContext.LicenseManagers
            .Select(x => new LicenseManager
            { ReferenceId = x.ReferenceId, LicenseKey = SecurityHelper.Decrypt(x.LicenseKey) }).ToList();
        return Task.FromResult(!id.IsValidGuid()
            ? licenseList.Any(x => x.LicenseKey.Equals(licenseKey))
            : licenseList.Where(x => x.LicenseKey.Equals(licenseKey)).ToList().Unique(id));
    }


    public Task<bool> IsLicenseKeyPoNumberUnique(string poNumber)
    {
        var poList = _dbContext.LicenseManagers
            .Select(x => new LicenseManager { PoNumber = SecurityHelper.Decrypt(x.PoNumber) }).ToList();

        var match = poList.Any(x => x.PoNumber.Equals(poNumber));

        return Task.FromResult(match);
    }

    public Task<bool> IsLicenseKeyPoNumberExist(string poNumber, string id)
    {
        var poList = _dbContext.LicenseManagers
            .Select(x => new LicenseManager
            { ReferenceId = x.ReferenceId, PoNumber = SecurityHelper.Decrypt(x.PoNumber) }).ToList();

        return Task.FromResult(!id.IsValidGuid()
            ? poList.Any(x => x.PoNumber.Equals(poNumber))
            : poList.Where(x => x.PoNumber.Equals(poNumber)).ToList().Unique(id));
    }


    public async Task<LicenseManager> GetLicenseDetailByPoNumber(string poNumber)
    {
        var list = await ListAllLicense();

        var detail = list.FirstOrDefault(x => x.PoNumber.Equals(poNumber));

        //Guard.Against.NullOrDeactive(detail, nameof(LicenseManager),
        //    new NotFoundException(nameof(LicenseManager), poNumber));

        return detail;
    }
    public async Task<LicenseManager> GetBaseLicenseDetailByDerivedLicenseDetailAsync(string parentId, string parentPo)
    {
        var list = await ListAllLicense();
        var detail = list.FirstOrDefault(x =>
            x.CompanyId.Equals(parentId) && x.IsParent.Equals(true) && x.PoNumber.Equals(parentPo));

        return detail == null
            ? throw new NotFoundException(nameof(LicenseManager), parentId)
            : detail;
    }

    public async Task<List<LicenseManager>> GetDerivedLicenseDetailByBaseLicenseDetailAsync(string companyId,
        string poNumber)
    {
        var licenseList = await ListAllLicense();
        return licenseList.Where(x =>
       (string.IsNullOrEmpty(companyId) || x.ParentId == companyId) &&
       (string.IsNullOrEmpty(poNumber) || x.ParentPoNumber == poNumber) &&
       x.ParentId != null && x.ParentPoNumber != null).ToList();
    }

    public async Task<List<LicenseManager>> GetDerivedLicenseByCompanyIdAsync(List<string> companyIds, List<string> poNumbers)
    {
        var licenseList = await ListAllLicense();

        return licenseList
            .Where(x => x.ParentId.IsNotNullOrWhiteSpace() && x.ParentPoNumber.IsNotNullOrWhiteSpace() && companyIds.Contains(x.ParentId) && poNumbers.Contains(x.ParentPoNumber))
            .ToList();
    }

    public async Task<List<LicenseManager>> GetLicenseExpiryDateByCompanyId(string companyId)
    {
        return await _dbContext.LicenseManagers
            .AsNoTracking()
            .Active()
            .Where(x => x.CompanyId.Equals(companyId))
            .Select(x => new LicenseManager
            {
                Id = x.Id,
                ReferenceId = x.ReferenceId,
                ExpiryDate = SecurityHelper.Decrypt(x.ExpiryDate),
                MacAddress = SecurityHelper.Decrypt(x.MacAddress)
            }).ToListAsync();
    }

    public async Task<List<LicenseManager>> GetLicenseExpiryDateByIds(List<string> ids)
    {
        return await _dbContext.LicenseManagers
            .AsNoTracking()
            .Active()
            .Where(x => ids.Contains(x.ReferenceId))
            .Select(x => new LicenseManager
            {
                Id = x.Id,
                ReferenceId = x.ReferenceId,
                PoNumber = SecurityHelper.Decrypt(x.PoNumber),
                ExpiryDate = SecurityHelper.Decrypt(x.ExpiryDate),
                IsState = x.IsState,
                IsExpired = x.IsExpired
            }).ToListAsync();
    }



    public async Task<List<LicenseManager>> GetLicenseDetailByCompanyId(string companyId)
    {
        return await _dbContext.LicenseManagers.AsNoTracking().Active().Where(x => x.CompanyId.Equals(companyId)).Select(x =>
            new LicenseManager
            {
                Id = x.Id,
                ReferenceId = x.ReferenceId,
                CompanyId = x.CompanyId,
                PoNumber = SecurityHelper.Decrypt(x.PoNumber),
                CompanyName = x.CompanyName,
                //CPHostName = CryptographyHelper.Decrypt(x.CPHostName),
                Properties = SecurityHelper.Decrypt(x.Properties),
                IpAddress = SecurityHelper.Decrypt(x.IpAddress),
                MacAddress = SecurityHelper.Decrypt(x.MacAddress),
                ParentId = x.ParentId,
                IsParent = x.IsParent,
                Validity = SecurityHelper.Decrypt(x.Validity),
                ExpiryDate = SecurityHelper.Decrypt(x.ExpiryDate),
                LicenseKey = x.LicenseKey,
                ParentPoNumber = SecurityHelper.Decrypt(x.ParentPoNumber),
                IsState = x.IsState,
                IsAmc = x.IsAmc,
                AmcPlan = SecurityHelper.Decrypt(x.AmcPlan),
                AmcStartDate = x.AmcStartDate,
                AmcEndDate = x.AmcEndDate,
                CreatedDate = x.CreatedDate,
                IsExpired = x.IsExpired
            }).ToListAsync();
    }

    public Task<bool> IsDerivedLicenseCompanyIdUnique(string companyId)
    {
        var match = _dbContext.LicenseManagers.Active().Any(x => x.CompanyId.Equals(companyId));
        return Task.FromResult(match);
    }

    public Task<LicenseManager> GetBaseLicenseByIdAsync(string id)
    {
        return _dbContext.LicenseManagers.AsNoTracking().Active().FirstOrDefaultAsync(x => x.ReferenceId.Equals(id) && x.IsParent);
    }

    public Task<LicenseManager> GetDerivedLicenseByIdAsync(string id)
    {
        return _dbContext.LicenseManagers.AsNoTracking().Active().FirstOrDefaultAsync(x => x.ReferenceId.Equals(id) && !x.IsParent);
    }

    public async Task<List<LicenseManager>> ListAllBaseLicense()
    {
        var licenseList = _loggedInUserService.IsParent
            ? await _dbContext.LicenseManagers.AsNoTracking().Active().Where(x => x.IsParent).ToListAsync()
            : await _dbContext.LicenseManagers.AsNoTracking().Active().Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .ToListAsync();

        return licenseList.Select(x => new LicenseManager
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            CompanyId = x.CompanyId,
            PoNumber = SecurityHelper.Decrypt(x.PoNumber),
            CompanyName = x.CompanyName,
            //CPHostName = CryptographyHelper.Decrypt(x.CPHostName),
            Properties = SecurityHelper.Decrypt(x.Properties),
            IpAddress = SecurityHelper.Decrypt(x.IpAddress),
            MacAddress = SecurityHelper.Decrypt(x.MacAddress),
            ParentId = x.ParentId,
            IsParent = x.IsParent,
            IsActive = x.IsActive,
            Validity = SecurityHelper.Decrypt(x.Validity),
            ExpiryDate = SecurityHelper.Decrypt(x.ExpiryDate),
            LicenseKey = x.LicenseKey,
            ParentPoNumber = SecurityHelper.Decrypt(x.ParentPoNumber),
            IsState = x.IsState,
            IsAmc = x.IsAmc,
            AmcPlan = SecurityHelper.Decrypt(x.AmcPlan),
            AmcStartDate = x.AmcStartDate,
            AmcEndDate = x.AmcEndDate,
            CreatedDate = x.CreatedDate,
            LastModifiedDate = x.LastModifiedDate,
            IsExpired = x.IsExpired
        }).OrderByDescending(x => x.Id).ToList();
    }

    public async Task<List<LicenseManager>> GetLicenseDetailByParentId(string parentId)
    {
        var licenses = await _dbContext.LicenseManagers.Active().Where(x => x.ParentId.Equals(parentId)).ToListAsync();
        return licenses.Select(x => new LicenseManager
        {
            ReferenceId = x.ReferenceId,
            CompanyId = x.CompanyId,
            PoNumber = SecurityHelper.Decrypt(x.PoNumber),
            CompanyName = x.CompanyName,
            //CPHostName = CryptographyHelper.Decrypt(x.CPHostName),
            Properties = SecurityHelper.Decrypt(x.Properties),
            IpAddress = SecurityHelper.Decrypt(x.IpAddress),
            MacAddress = SecurityHelper.Decrypt(x.MacAddress),
            ParentId = x.ParentId,
            IsParent = x.IsParent,
            Validity = SecurityHelper.Decrypt(x.Validity),
            ExpiryDate = SecurityHelper.Decrypt(x.ExpiryDate),
            LicenseKey = x.LicenseKey,
            ParentPoNumber = SecurityHelper.Decrypt(x.ParentPoNumber),
            IsState = x.IsState,
            IsAmc = x.IsAmc,
            AmcPlan = SecurityHelper.Decrypt(x.AmcPlan),
            AmcStartDate = x.AmcStartDate,
            AmcEndDate = x.AmcEndDate,
            IsExpired = x.IsExpired
        }).ToList();
    }
}