//Functions

function textBoxChange(errorID) {
    $(errorID).text("").removeClass('field-validation-error');
    document.getElementById('portTextBox').addEventListener('input', function (event) {

        if (event.data === '.' && event.data === '+') {
            event.preventDefault();
        } else {
            this.value = this.value.replace(/\D/g, '').slice(0, 5);
        }
    });
}

function customFieldChange() {
    let customValue = $('#customFieldTextarea');
    let sanitizedValue = customValue.val().replace(/\s{2,}/g, ' '); //Restrict Multiple spaces
    customValue.val(sanitizedValue);
    customValueValidation(sanitizedValue, 'Enter custom command', 'customCommandError')
}

function customValueValidation(value, errorMessage, errorElement) {
    let $errorEl = $('#' + errorElement);

    if (!value) {
        $errorEl.text(errorMessage).addClass('field-validation-error');
        return false;
    }

    if (/^\s/.test(value)) {
        $errorEl.text("Should not begin with space").addClass('field-validation-error');
        return false;
    }
    $errorEl.text('').removeClass('field-validation-error');
    return true;
};

function optionsSelectionChange() {
    // Determine which radio button is checked
    let isInlineRadio1Checked = $("#inlineRadio1").is(':checked');
    let isInlineRadio2Checked = $("#inlineRadio2").is(':checked');

    // Show or hide elements based on the checked radio button
    $('#showHideGeneralAdvancedOptions').toggle(isInlineRadio1Checked);
    $('#showHideCustom').toggle(isInlineRadio2Checked);

    // Clear validation error if either radio button is checked 
    $("#optionSelectionError").text("").removeClass('field-validation-error');
    $("#generalOptionsError").text("").removeClass('field-validation-error');
    $("#customCommandError").text("").removeClass('field-validation-error');
}

function generalOptionsChange() {
    let $generalOptionsError = $("#generalOptionsError")
    let isAnyChecked = generalOptionsCheckboxes.some(checkbox => checkbox.is(':checked'));

    if (isAnyChecked) {
        $generalOptionsError.text("").removeClass('field-validation-error');
    } else {
        $generalOptionsError.text("Select atleast one checkbox").addClass('field-validation-error');
    }
}

function enableTextBox(checkbox, textBox, textBoxError) {

    if ($(checkbox).is(':checked')) {

        if ($(textBox).val()) {
            $(textBox).removeAttr('disabled');
        } else {
            $(textBox).removeAttr('disabled').val('');
        }
    } else {
        $(textBox).prop('disabled', 'disabled').val('');
        $(textBoxError).text("").removeClass('field-validation-error');
    }
}

function validateReplicationType() {
    let type = $('#replicationType :selected').val();
    replicationTypeValidation(type, 'Select replication type', 'replicationTypeError')
}

function replicationTypeValidation(value, errorMessage, errorElement) {
    let $errorEl = $('#' + errorElement);

    if (!value) {
        $errorEl.text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $errorEl.text('').removeClass('field-validation-error');
        return true;
    }
};

function clearServerTypeErrorMessage() {
    const errorElements = ['#nameError', '#replicationTypeError', '#optionSelectionError', '#generalOptionsError',
        '#logFileTextBoxError', '#excludeTextBoxError', '#excludeFromTextBoxError', '#portTextBoxError', '#includeTextBoxError',
        '#includeFromTextBoxError', '#additionalTextBoxError', '#customCommandError'];
    clearInputFields('createFormRSync', errorElements);
}

const populateModalFields = (data) => {
    clearServerTypeErrorMessage();
    $('#statsCheckbox').prop("checked", true);
    let parsedProps = JSON.parse(data?.properties);

    if (parsedProps?.optionsSelection === "userSelection") {
        $("#inlineRadio1").prop("checked", true).trigger('change');
    } else if (parsedProps?.optionsSelection === "custom") {
        $("#inlineRadio2").prop("checked", true).trigger('change');
    }

    if (parsedProps?.optionsSelection === "userSelection") {
        let generalOptionsArray = parsedProps?.generalOptions?.split(',');
        generalOptionsCheckboxes.forEach(function (checkbox) {
            generalOptionsArray.forEach(function (generalOptions) {
                let check = generalOptions.split("")
                check.forEach(function (i) {

                    if (checkbox.val() === i) {
                        checkbox.prop("checked", true);
                    }
                })

            })
        });

        let advancedOptionsArray = parsedProps.advancedOptions.split(',');
        advancedOptionsCheckboxes.forEach(function (checkbox) {
            advancedOptionsArray.forEach(function (advancedOptions) {

                if (checkbox.val() === advancedOptions) {
                    checkbox.prop("checked", true);
                }
            })
        });

        let advancedOptionsTextFieldsArray = parsedProps.advancedOptionsTextFields;
        advancedOptionsTextBoxCheckboxes.forEach(function (checkbox, index) {
            advancedOptionsTextFieldsArray.forEach(function (advancedOptions) {

                if (checkbox.val() === advancedOptions.key) {
                    advancedOptionsTextBoxes[index].val(advancedOptions.value);
                    checkbox.prop("checked", true).trigger('change');
                }
            })
        });
    }

    if (parsedProps?.optionsSelection === "custom") {
        $('#customFieldTextarea').val(parsedProps?.customCommand);
    }
    $('#rsyncOptionName').val(data?.name);
    $('#replicationType').val(data?.replicationType).trigger('change');
    $('#rsyncID').val(data?.id);
}