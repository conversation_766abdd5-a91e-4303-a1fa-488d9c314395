using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportActionResult.Commands;

public class CreateBulkImportActionResultTests : IClassFixture<BulkImportActionResultFixture>
{
    private readonly BulkImportActionResultFixture _bulkImportActionResultFixture;
    private readonly Mock<IBulkImportActionResultRepository> _mockBulkImportActionResultRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly CreateBulkImportActionResultCommandHandler _handler;

    public CreateBulkImportActionResultTests(BulkImportActionResultFixture bulkImportActionResultFixture)
    {
        _bulkImportActionResultFixture = bulkImportActionResultFixture;

        _mockBulkImportActionResultRepository = BulkImportActionResultRepositoryMocks.CreateBulkImportActionResultRepository(_bulkImportActionResultFixture.BulkImportActionResults);
        _mockMapper = new Mock<IMapper>();
        _mockPublisher = new Mock<IPublisher>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<Domain.Entities.BulkImportActionResult>(It.IsAny<CreateBulkImportActionResultCommand>()))
            .Returns((CreateBulkImportActionResultCommand cmd) => new Domain.Entities.BulkImportActionResult
            {
                CompanyId = cmd.CompanyId,
                NodeId = cmd.NodeId,
                NodeName = cmd.NodeName,
                BulkImportOperationId = cmd.BulkImportOperationId,
                BulkImportOperationGroupId = cmd.BulkImportOperationGroupId,
                EntityId = cmd.EntityId,
                EntityName = cmd.EntityName,
                EntityType = cmd.EntityType,
                Status = cmd.Status,
                StartTime = cmd.StartTime,
                EndTime = cmd.EndTime,
                ErrorMessage = cmd.ErrorMessage
            });

        _handler = new CreateBulkImportActionResultCommandHandler(
            _mockMapper.Object,
            _mockBulkImportActionResultRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_CreateBulkImportActionResultResponse_When_BulkImportActionResultCreated()
    {
        // Arrange
        var command = _bulkImportActionResultFixture.CreateBulkImportActionResultCommand;

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(CreateBulkImportActionResultResponse));
        result.Id.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var command = _bulkImportActionResultFixture.CreateBulkImportActionResultCommand;

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportActionResultRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.BulkImportActionResult>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var command = _bulkImportActionResultFixture.CreateBulkImportActionResultCommand;

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<Domain.Entities.BulkImportActionResult>(It.IsAny<CreateBulkImportActionResultCommand>()), Times.Once);
    }

    [Fact]
    public async Task Handle_MapCommandToEntity_WithCorrectProperties()
    {
        // Arrange
        var command = new CreateBulkImportActionResultCommand
        {
            CompanyId = "TestCompanyId",
            NodeId = "Node001",
            NodeName = "TestNode",
            BulkImportOperationId = "TestOperationId",
            BulkImportOperationGroupId = "TestGroupId",
            EntityId = "TestEntityId",
            EntityName = "TestEntity",
            EntityType = "Server",
            Status = "Pending",
            StartTime = DateTime.Now,
            EndTime = DateTime.Now.AddHours(1),
            ErrorMessage = ""
        };

        Domain.Entities.BulkImportActionResult capturedEntity = null;
        _mockBulkImportActionResultRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.BulkImportActionResult>()))
            .Callback<Domain.Entities.BulkImportActionResult>(entity => capturedEntity = entity)
            .ReturnsAsync((Domain.Entities.BulkImportActionResult entity) => 
            {
                entity.ReferenceId = Guid.NewGuid().ToString();
                return entity;
            });

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEntity.ShouldNotBeNull();
        capturedEntity.CompanyId.ShouldBe("TestCompanyId");
        capturedEntity.NodeId.ShouldBe("Node001");
        capturedEntity.NodeName.ShouldBe("TestNode");
        capturedEntity.BulkImportOperationId.ShouldBe("TestOperationId");
        capturedEntity.BulkImportOperationGroupId.ShouldBe("TestGroupId");
        capturedEntity.EntityId.ShouldBe("TestEntityId");
        capturedEntity.EntityName.ShouldBe("TestEntity");
        capturedEntity.EntityType.ShouldBe("Server");
        capturedEntity.Status.ShouldBe("Pending");
        capturedEntity.ErrorMessage.ShouldBe("");
    }

    [Fact]
    public async Task Handle_CreateResponseWithCorrectProperties_When_BulkImportActionResultCreated()
    {
        // Arrange
        var command = _bulkImportActionResultFixture.CreateBulkImportActionResultCommand;
        command.EntityName = "TestEntity";

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_OperationSuccessful()
    {
        // Arrange
        var command = _bulkImportActionResultFixture.CreateBulkImportActionResultCommand;

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<CreateBulkImportActionResultResponse>();
        result.GetType().ShouldBe(typeof(CreateBulkImportActionResultResponse));
    }

    [Fact]
    public async Task Handle_SetReferenceIdInResponse_When_EntityCreated()
    {
        // Arrange
        var command = _bulkImportActionResultFixture.CreateBulkImportActionResultCommand;
        var expectedReferenceId = Guid.NewGuid().ToString();

        _mockBulkImportActionResultRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.BulkImportActionResult>()))
            .ReturnsAsync((Domain.Entities.BulkImportActionResult entity) => 
            {
                entity.ReferenceId = expectedReferenceId;
                return entity;
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Id.ShouldBe(expectedReferenceId);
    }

    [Fact]
    public async Task Handle_SetEntityTypeAndStatus_When_CommandMapped()
    {
        // Arrange
        var command = _bulkImportActionResultFixture.CreateBulkImportActionResultCommand;
        command.EntityType = "Database";
        command.Status = "Running";

        Domain.Entities.BulkImportActionResult capturedEntity = null;
        _mockBulkImportActionResultRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.BulkImportActionResult>()))
            .Callback<Domain.Entities.BulkImportActionResult>(entity => capturedEntity = entity)
            .ReturnsAsync((Domain.Entities.BulkImportActionResult entity) => entity);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEntity.EntityType.ShouldBe("Database");
        capturedEntity.Status.ShouldBe("Running");
    }

    [Fact]
    public async Task Handle_SetTimeProperties_When_CommandMapped()
    {
        // Arrange
        var startTime = DateTime.Now.AddHours(-1);
        var endTime = DateTime.Now;
        var command = _bulkImportActionResultFixture.CreateBulkImportActionResultCommand;
        command.StartTime = startTime;
        command.EndTime = endTime;

        Domain.Entities.BulkImportActionResult capturedEntity = null;
        _mockBulkImportActionResultRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.BulkImportActionResult>()))
            .Callback<Domain.Entities.BulkImportActionResult>(entity => capturedEntity = entity)
            .ReturnsAsync((Domain.Entities.BulkImportActionResult entity) => entity);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEntity.StartTime.ShouldBe(startTime);
        capturedEntity.EndTime.ShouldBe(endTime);
    }

    [Fact]
    public async Task Handle_SetOperationIds_When_CommandMapped()
    {
        // Arrange
        var command = _bulkImportActionResultFixture.CreateBulkImportActionResultCommand;
        command.BulkImportOperationId = "SpecificOperationId";
        command.BulkImportOperationGroupId = "SpecificGroupId";

        Domain.Entities.BulkImportActionResult capturedEntity = null;
        _mockBulkImportActionResultRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.BulkImportActionResult>()))
            .Callback<Domain.Entities.BulkImportActionResult>(entity => capturedEntity = entity)
            .ReturnsAsync((Domain.Entities.BulkImportActionResult entity) => entity);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEntity.BulkImportOperationId.ShouldBe("SpecificOperationId");
        capturedEntity.BulkImportOperationGroupId.ShouldBe("SpecificGroupId");
    }
}
