﻿using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Persistence;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Infrastructure.Persistence;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;


namespace ContinuityPatrol.Persistence.Repositories;

public class BaseRepository<T> : IRepository<T> where T : BaseEntity
{
    private readonly ILoggedInUserService _loggedInUserService;
    protected readonly ModuleDbContext DbContext;
    private AssignedEntity _assignedEntity;
    public DbSet<T> Entities;
        
    public BaseRepository(ModuleDbContext dbContext, ILoggedInUserService loggedInUserService = null)
    {
        DbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        _loggedInUserService = loggedInUserService;
        Entities = DbContext.Set<T>();
    }

    public AssignedEntity AssignedEntity => _assignedEntity ??= LoadAssignedEntity();

    private AssignedEntity LoadAssignedEntity()
    {
        if (_loggedInUserService == null || !_loggedInUserService.IsAuthenticated)
            throw new SessionExpiredException("Session logged out, no action taken too long");

        if (IsAllInfra) return new AssignedEntity();

        return _loggedInUserService.AssignedInfras.IsNotNullOrEmpty()
            ? JsonConvert.DeserializeObject<AssignedEntity>(_loggedInUserService.AssignedInfras)
            : new AssignedEntity();
    }

    public bool IsParent => _loggedInUserService.IsParent;
    public bool IsAllInfra => _loggedInUserService.IsAllInfra;

    public virtual async Task<T> GetByReferenceIdAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Id", "Id cannot be invalid");
        return await Entities.AsNoTracking().Where(x => x.ReferenceId.Equals(id)).FirstOrDefaultAsync();
    }

    public IQueryable<T> GetByReferenceId(string id, Expression<Func<T, bool>> expression = null)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Id", "Id cannot be invalid");
        return IsParent
            ? Entities.AsNoTracking().Where(x => x.ReferenceId.Equals(id))
            : FilterBy(expression ?? (x => x.ReferenceId == id));
    }

    public T GetBusinessServiceByReferenceId(T businessService)
    {
        return AssignedEntity.AssignedBusinessServices
            .Where(assignedBusinessService => businessService?.ReferenceId == assignedBusinessService.Id)
            .Select(_ => businessService).SingleOrDefault();
    }

    public T GetBusinessFunctionByReferenceId(T businessFunction)
    {
        return AssignedEntity.AssignedBusinessServices
            .SelectMany(assignedBusinessService => assignedBusinessService.AssignedBusinessFunctions)
            .Where(assignedBusinessFunction => businessFunction?.ReferenceId == assignedBusinessFunction.Id)
            .Select(_ => businessFunction).SingleOrDefault();
    }

    public T GetInfraObjectByReferenceId(T infraObject)
    {
        return AssignedEntity.AssignedBusinessServices
            .SelectMany(assignedBusinessService => assignedBusinessService.AssignedBusinessFunctions)
            .SelectMany(assignedBusinessFunction => assignedBusinessFunction.AssignedInfraObjects)
            .Where(assignedInfraObjects => infraObject?.ReferenceId == assignedInfraObjects.Id)
            .Select(_ => infraObject).SingleOrDefault();
    }


    public virtual async Task<IReadOnlyList<T>> ListAllAsync()
    {
        return await Entities.AsNoTracking().DescOrderById().ToListAsync();
    }

    public virtual IQueryable<T> QueryAll(Expression<Func<T, bool>> expression)
    {
        return IsParent ? Entities.AsNoTracking().DescOrderById() : FilterBy(expression);
    }

    public virtual IQueryable<T> FilterBy(Expression<Func<T, bool>> expression)
    {
        return Entities.AsNoTracking().Where(expression).DescOrderById();
    }

    public virtual async Task<IReadOnlyList<T>> FindByFilter(Expression<Func<T, bool>> expression)
    {
        return await Entities.AsNoTracking().Where(expression).DescOrderById().ToListAsync();
    }

    public virtual IQueryable<T> GetPaginatedQuery()
    {
        return Entities.AsNoTracking().DescOrderById();
    }

    public virtual async Task<PaginatedResult<T>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<T> productFilterSpec,string sortColumn,string sortOrder)
    {
        return await Entities
            .Specify(productFilterSpec)
            .DescOrderById()
            .ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);
    }

    public virtual async Task<T> AddAsync(T entity)
    {
        Guard.Against.Null(entity, nameof(entity));

        return await ExecuteWithRetryAsync(async () =>
        {
            await Entities.AddAsync(entity);
            await DbContext.SaveChangesAsync();
            return entity;
        });
    }

    public virtual async Task<T> UpdateAsync(T entity)
    {
        Guard.Against.Null(entity, nameof(entity));

        return await ExecuteWithRetryAsync(async () =>
        {
            DbContext.ChangeTracker.Clear();
            Entities.Attach(entity);
            DbContext.Entry(entity).State = EntityState.Modified;
            await DbContext.SaveChangesAsync();
            return entity;
        });
    }

    public virtual async Task<IEnumerable<T>> UpdateRangeAsync(IEnumerable<T> entities)
    {
        var entityArray = entities?.ToArray() ?? throw new ArgumentNullException(nameof(entities));
        if (!entityArray.Any()) return Enumerable.Empty<T>();

        DbContext.ChangeTracker.Clear();
        DbContext.Set<T>().UpdateRange(entityArray);
        await DbContext.SaveChangesAsync();
        return entityArray;
    }

    public virtual async Task<T> DeleteAsync(T entity)
    {
        Guard.Against.Null(entity, nameof(entity));

        Entities.Remove(entity);
        DbContext.Entry(entity).State = EntityState.Deleted;
        await DbContext.SaveChangesAsync();
        return entity;
    }

    public virtual async Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities)
    {
        var entityArray = entities?.ToArray() ?? throw new ArgumentNullException(nameof(entities));
        if (!entityArray.Any()) return Enumerable.Empty<T>();

        await DbContext.Set<T>().AddRangeAsync(entityArray);
        await DbContext.SaveChangesAsync();
        return entityArray;

    }
    public virtual async Task<IEnumerable<T>> RemoveRangeAsync(IEnumerable<T> entities)
    {
        var entityArray = entities?.ToArray() ?? throw new ArgumentNullException(nameof(entities));
        if (!entityArray.Any()) return Enumerable.Empty<T>();

        DbContext.Set<T>().RemoveRange(entityArray);
        await DbContext.SaveChangesAsync();
        return entityArray;
    }
    public IReadOnlyList<T> GetAssignedBusinessServices(IQueryable<T> businessServices)
    {
        var assignedServiceIds = AssignedEntity.AssignedBusinessServices
            .Select(s => s.Id)
            .ToHashSet();

        if (!assignedServiceIds.Any())
            return Array.Empty<T>();

        return businessServices
            .Where(bs => assignedServiceIds.Contains(bs.ReferenceId))
            .ToList();
    }

    public IReadOnlyList<T> GetAssignedBusinessFunctions(IQueryable<T> businessFunctions)
    {
        var functions = new List<T>();
        var assignedBusinessFunctions = AssignedEntity.AssignedBusinessServices
            .SelectMany(assignedBusinessService => assignedBusinessService.AssignedBusinessFunctions)
            .ToList();

        foreach (var businessFunction in businessFunctions)
        {
            if (assignedBusinessFunctions.Any())
            {
                functions.AddRange(assignedBusinessFunctions
                    .Where(assignedBusinessFunction => businessFunction?.ReferenceId == assignedBusinessFunction.Id)
                    .Select(_ => businessFunction));
            }
        }
        return functions;
    }

    public IReadOnlyList<T> GetAssignedInfraObjects(IQueryable<T> infraObjects)
    {
        var infraObjectList = new List<T>();

        var assignedBusinessInfraObjects = AssignedEntity?.AssignedBusinessServices
            .SelectMany(assignedBusinessService => assignedBusinessService?.AssignedBusinessFunctions)
            .SelectMany(assignedBusinessFunction => assignedBusinessFunction?.AssignedInfraObjects)
            .ToList();

        if (assignedBusinessInfraObjects is null) return infraObjectList;

        foreach (var infraObject in infraObjects)
        {
            if (assignedBusinessInfraObjects!.Any())
            {
                infraObjectList.AddRange(assignedBusinessInfraObjects
                    .Where(assignedInfraObject => infraObject?.ReferenceId == assignedInfraObject.Id)
                    .Select(_ => infraObject));

            }
        }
        return infraObjectList;
    }

    public IQueryable<T> GetPaginatedAssignedBusinessServices(IQueryable<T> businessServices)
    {
        var assignedServiceIds = AssignedEntity.AssignedBusinessServices.Select(s => s.Id).ToHashSet();
        return businessServices.Where(s => assignedServiceIds.Contains(s.ReferenceId));
    }

    public IQueryable<T> GetPaginatedBusinessFunctions(IQueryable<T> businessFunctions)
    {
        var assignedFunctionIds = AssignedEntity.AssignedBusinessServices
            .SelectMany(b => b.AssignedBusinessFunctions)
            .Select(f => f.Id)
            .ToHashSet();
        return businessFunctions.Where(f => assignedFunctionIds.Contains(f.ReferenceId));
    }

    public IQueryable<T> GetPaginatedInfraObjects(IQueryable<T> infraObjects)
    {
        var assignedInfraObjectIds = AssignedEntity.AssignedBusinessServices
            .SelectMany(b => b.AssignedBusinessFunctions)
            .SelectMany(f => f.AssignedInfraObjects)
            .Select(i => i.Id)
            .ToHashSet();

        return infraObjects.Where(i => assignedInfraObjectIds.Contains(i.ReferenceId));
    }

    public virtual async Task<T> GetByIdAsync(int id)
    {
        return await Entities.FindAsync(id);
    }

    private async Task<TResult> ExecuteWithRetryAsync<TResult>(Func<Task<TResult>> operation)
    {
        const int maxRetries = 5;
        var retryDelay = 1000;
        var retries = 0;

        while (true)
        {
            using var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            try
            {
                var result = await operation();
                scope.Complete();
                return result;
            }
            catch (DbUpdateException ex) when (ex.InnerException is SqlException sqlEx && sqlEx.Number == 1205)
            {
                retries++;
                if (retries >= maxRetries) throw;
                await Task.Delay(retryDelay);
                retryDelay *= 2;
            }
        }
    }
}