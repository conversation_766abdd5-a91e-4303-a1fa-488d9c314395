using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Approval;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixApprovalModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IApprovalMatrixApprovalService
{
    Task<ApprovalMatrixApprovalResponse> UpdateApprovalMatrixStatus(ApprovalMatrixApprovalCommand command);
    Task<List<ApprovalMatrixApprovalListVm>> GetApprovalMatrixApprovalList();
    Task<BaseResponse> CreateAsync(CreateApprovalMatrixApprovalCommand createApprovalMatrixApprovalCommand);
    Task<BaseResponse> UpdateAsync(UpdateApprovalMatrixApprovalCommand updateApprovalMatrixApprovalCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<ApprovalMatrixApprovalDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsApprovalMatrixApprovalNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<ApprovalMatrixApprovalListVm>> GetPaginatedApprovalMatrixApprovals(GetApprovalMatrixApprovalPaginatedListQuery query);
    #endregion
}
