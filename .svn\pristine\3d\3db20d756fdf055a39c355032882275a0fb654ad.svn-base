﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;
using Microsoft.EntityFrameworkCore;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IPageSolutionMappingRepository : IRepository<PageSolutionMapping>
{
    Task<bool> IsNameExist(string name, string id);
    Task<PageSolutionMapping> GetByReplicationCategoryTypeId(string replicationCategoryTypeId);
    Task<PageSolutionMapping> GetByReplicationTypeId(string replicationTypeId);
   
}