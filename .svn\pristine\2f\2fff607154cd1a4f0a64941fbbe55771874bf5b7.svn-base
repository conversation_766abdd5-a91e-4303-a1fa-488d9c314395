﻿<link href="~/css/WorkflowConfiguration.css" rel="stylesheet" />

<div class="page-content">
    <div class="row g-3">
        <div class="col">
            <div class="card Card_Design_None">
                <div class="card-header header">
                    <h6 class="page_title"><i class="cp-parameter-name"></i><span>Prerequisite Scanner</span></h6>
                    <form class="d-flex gap-2 w-75 justify-content-end align-items-end">
                        <div class="w-25 gap-2 d-flex align-items-center">
                            <div class="form-label">Profile&nbsp;Name</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-wf-profile"></i></span>
                                <select class="form-select">
                                    <option value="1">PTS</option>
                                    <option value="2">TCS</option>
                                    <option value="1">PTS1</option>
                                </select>
                            </div>
                        </div>
                        <div class="w-25 gap-2 d-flex align-items-center">
                            <label class="form-label mb-0">InfraObject&nbsp;Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-infra-object"></i></span>
                                <select class="form-select" multiple="multiple">
                                    <option value="1">PTS</option>
                                    <option value="2">TCS</option>
                                </select>
                            </div>
                        </div>
                        <div class="w-25 gap-2 d-flex align-items-center">
                            <label class="form-label mb-0">Node</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-network"></i></span>
                                <select class="form-select">
                                    <option value="1">PTS</option>
                                    <option value="2">TCS</option>
                                </select>
                            </div>
                        </div>
                        <button class="btn btn-primary btn-sm rounded-1"><i class="cp-report" style="line-height: 1.3;"></i></button>
                    </form>
                </div>
                <div class="card-body pt-0 Workflow-Execution">
                    <h6 class="text-primary">Profile_1 Server</h6>
                    <div class="accordion accordion-flush" id="accordionFlushExample">
                        <div class="accordion-item">
                            <div class="accordion-header">
                                <div class="accordion-button collapsed d-flex gap-3 justify-content-between" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                                    <div class="d-flex align-items-center" role="button">
                                        <div class="me-2">
                                            <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                        </div>
                                        <i class="cp-network me-2"></i>Node_1
                                    </div>
                                </div>
                            </div>
                            <div id="flush-collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionFlushExample">
                                <div class="accordion-body">
                                    <ul class="list-group list-group-flush Profile-Select">
                                        <li class="list-group-item border-top list-group-item-action d-flex justify-content-between align-items-center Active-Card">
                                            <div class="d-flex">
                                                <div class="mt-1">
                                                    <i class="cp-infra-object Success_Running me-2"></i>
                                                </div>
                                                <div>
                                                    <span class="fw-bold">InfraObject</span><br />
                                                    <div class="text-secondary mt-1">Infra_MYSQL_NLS</div>
                                                </div>
                                            </div>
                                            <div class="d-flex w-25">
                                                <div class="text-secondary flex-fill">
                                                    <div class="d-flex align-items-center gap-3 mt-1">
                                                        <span><i class="cp-scan me-2"></i>Scanning</span>
                                                        <div class="progress w-25" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="height: 4px">
                                                            <div class="progress-bar bg-warning progress-bar-striped progress-bar-animated" style="width: 75%;"></div>
                                                        </div>
                                                        <span class="ms-2">05/08</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="mt-1">
                                                    <i class="cp-parameter-name Success_Running me-2"></i>
                                                </div>
                                                <div>
                                                    <span class="fw-bold">Parameter</span><br />
                                                    <div class="text-secondary mt-1">Port Verification</div>
                                                </div>
                                            </div>
                                        </li>
                                        <li class="list-group-item border-top list-group-item-action d-flex justify-content-between align-items-center">
                                            <div class="d-flex">
                                                <div class="mt-1">
                                                    <i class="cp-infra-object Success_Running me-2"></i>
                                                </div>
                                                <div>
                                                    <span class="fw-bold">InfraObject</span><br />
                                                    <div class="text-secondary mt-1">Infra_MYSQL_NLS</div>
                                                </div>
                                            </div>
                                            <div class="d-flex w-25">
                                                <div class="text-secondary flex-fill">
                                                    <div class="d-flex align-items-center gap-3 mt-1">
                                                        <span><i class="cp-scan me-2"></i>Scanning</span>
                                                        <div class="progress w-25" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="height: 4px">
                                                            <div class="progress-bar bg-success progress-bar-striped progress-bar-animated" style="width: 75%;"></div>
                                                        </div>
                                                        <span class="ms-2">05/08</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="mt-1">
                                                    <i class="cp-parameter-name Success_Running me-2"></i>
                                                </div>
                                                <div>
                                                    <span class="fw-bold">Parameter</span><br />
                                                    <div class="text-secondary mt-1">Port Verification</div>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <div class="accordion-header">
                                <div class="accordion-button collapsed d-flex gap-3 justify-content-between" data-bs-toggle="collapse" data-bs-target="#flush-collapse1" aria-expanded="false" aria-controls="flush-collapseOne">
                                    <div class="d-flex align-items-center" role="button">
                                        <div class="me-2">
                                            <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                                        </div>
                                        <i class="cp-network me-2"></i>Node_1
                                    </div>
                                </div>

                            </div>
                            <div id="flush-collapse1" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                <div class="accordion-body">
                                    <div class="accordion-body">
                                        <ul class="list-group list-group-flush Profile-Select">
                                            <li class="list-group-item border-top list-group-item-action d-flex justify-content-between align-items-center">
                                                <div class="d-flex">
                                                    <div class="mt-1">
                                                        <i class="cp-infra-object Success_Running me-2"></i>
                                                    </div>
                                                    <div>
                                                        <span class="fw-bold">InfraObject</span><br />
                                                        <div class="text-secondary mt-1">Infra_MYSQL_NLS</div>
                                                    </div>
                                                </div>
                                                <div class="d-flex w-25">
                                                    <div class="text-secondary flex-fill">
                                                        <div class="d-flex align-items-center gap-3 mt-1">
                                                            <span><i class="cp-scan me-2"></i>Scanning</span>
                                                            <div class="progress w-25" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="height: 4px">
                                                                <div class="progress-bar bg-success progress-bar-striped progress-bar-animated" style="width: 75%;"></div>
                                                            </div>
                                                            <span class="ms-2">05/08</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="d-flex">
                                                    <div class="mt-1">
                                                        <i class="cp-parameter-name Success_Running me-2"></i>
                                                    </div>
                                                    <div>
                                                        <span class="fw-bold">Parameter</span><br />
                                                        <div class="text-secondary mt-1">Port Verification</div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="list-group-item border-top list-group-item-action d-flex justify-content-between align-items-center">
                                                <div class="d-flex">
                                                    <div class="mt-1">
                                                        <i class="cp-infra-object Success_Running me-2"></i>
                                                    </div>
                                                    <div>
                                                        <span class="fw-bold">InfraObject</span><br />
                                                        <div class="text-secondary mt-1">Infra_MYSQL_NLS</div>
                                                    </div>
                                                </div>
                                                <div class="d-flex w-25">
                                                    <div class="text-secondary flex-fill">
                                                        <div class="d-flex align-items-center gap-3 mt-1">
                                                            <span><i class="cp-scan me-2"></i>Scanning</span>
                                                            <div class="progress w-25" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="height: 4px">
                                                                <div class="progress-bar bg-success progress-bar-striped progress-bar-animated" style="width: 75%;"></div>
                                                            </div>
                                                            <span class="ms-2">05/08</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="d-flex">
                                                    <div class="mt-1">
                                                        <i class="cp-parameter-name Success_Running me-2"></i>
                                                    </div>
                                                    <div>
                                                        <span class="fw-bold">Parameter</span><br />
                                                        <div class="text-secondary mt-1">Port Verification</div>
                                                    </div>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-3 d-grid">
            <div class="card Card_Design_None mb-3">
                <div class="card-header fs-6">Checking Requirements</div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush Profile-Select">
                        <a href="#" class="list-group-item list-group-item-action" aria-current="true">
                            <div class=" d-flex justify-content-between gap-2">
                                <div>
                                    <i class="cp-database-2 text-primary fs-5"></i>
                                </div>
                                <div class="flex-fill">
                                    <span class="fw-bold">Windows Server 2019</span><br />
                                    @* <div class="d-flex align-items-center gap-3 mt-1 text-secondary">
                                        <small>Feb 22 - 23, 2023</small><small class="ms-2">11:50:10 - 12:05:06</small>
                                    </div> *@
                                </div>
                                <div><i class="cp-success text-success fs-5"></i></div>
                            </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" aria-current="true">
                            <div class=" d-flex justify-content-between gap-2">
                                <div>
                                    <i class="cp-database-2 text-primary fs-5"></i>
                                </div>
                                <div class="flex-fill">
                                    <span class="fw-bold">.Net Core Runtime 6</span><br />
                                    @* <div class="d-flex align-items-center gap-3 mt-1 text-secondary">
                                        <small>Feb 22 - 23, 2023</small><small class="ms-2">11:50:10 - 12:05:06</small>
                                    </div> *@
                                </div>
                                <div><i class="cp-error text-danger fs-5"></i></div>
                            </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" aria-current="true">
                            <div class=" d-flex justify-content-between gap-2">
                                <div>
                                    <i class="cp-database-2 text-primary fs-5"></i>
                                </div>
                                <div class="flex-fill">
                                    <span class="fw-bold">Java 8 </span><small class="text-secondary">( JRE 1.8 and JDK 1.8 )</small><br />
                                    @* <div class="d-flex align-items-center gap-3 mt-1 text-secondary">
                                        <small>Feb 22 - 23, 2023</small><small class="ms-2">11:50:10 - 12:05:06</small>
                                    </div> *@
                                </div>
                                <div><i class="cp-success text-success fs-5"></i></div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>