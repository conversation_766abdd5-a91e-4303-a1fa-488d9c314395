﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Alert.Queries.GetLastAlertId;

public class GetLastAlertDetailQueryHandler : IRequestHandler<GetLastAlertDetailQuery, GetLastAlertDetailVm>
{
    private readonly IAlertRepository _alertRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IUserLoginRepository _userLoginRepository;

    public GetLastAlertDetailQueryHandler(IAlertRepository alertRepository, IUserLoginRepository userLoginRepository,
        ILoggedInUserService loggedInUserService)
    {
        _alertRepository = alertRepository;
        _userLoginRepository = userLoginRepository;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<GetLastAlertDetailVm> Handle(GetLastAlertDetailQuery request, CancellationToken cancellationToken)
    {
        var userLogin = await _userLoginRepository.GetUserLoginByUserId(_loggedInUserService.UserId);

        var alertList = new GetLastAlertDetailVm();

        var alert = await _alertRepository.GetByIdAsync(userLogin.LastAlertId);

        if (alert is null || userLogin?.LastAlertId is null || userLogin?.LastAlertId == 0)
            return new GetLastAlertDetailVm
            {
                CompanyId=_loggedInUserService.CompanyId,
                UserId = _loggedInUserService.UserId,
                UserName = _loggedInUserService.LoginName,
                AlertId = userLogin.LastAlertId,
                AlertCount = 0
            };

        if (_loggedInUserService.IsAllInfra)
        {
            var alertCount = await _alertRepository.GetAlertByUserLastAlertId(alert.Id);

            alertList.CompanyId = _loggedInUserService.CompanyId;
            alertList.UserId = _loggedInUserService.UserId;
            alertList.UserName = _loggedInUserService.LoginName;
            alertList.AlertId = alert.Id == 0 ? 0 : alert.Id;
            alertList.AlertCount = alertCount.Count;
        }
        else
        {
            var deserializeValue = JsonConvert.DeserializeObject<AssignedEntity>(_loggedInUserService.AssignedInfras);

            var assignedInfraAlert = _alertRepository.GetAlertByUserLastAlertIdAndDate(alert.Id);

            var assignedInfra = deserializeValue?.AssignedBusinessServices
                .SelectMany(x => x?.AssignedBusinessFunctions
                    .SelectMany(infra => infra?.AssignedInfraObjects))
                .Select(infraObject => infraObject?.Id);

            var alertListVmQuery =
                assignedInfraAlert.Where(infraObject => assignedInfra.Contains(infraObject.InfraObjectId));

            var alertDto = new List<Domain.Entities.Alert>();

            //var deserializeValue = JsonConvert.DeserializeObject<AssignedEntity>(_loggedInUserService.AssignedInfras);

            //var assignedInfra = deserializeValue.AssignedBusinessServices
            //    .SelectMany(x => x.AssignedBusinessFunctions
            //        .SelectMany(infra => infra.AssignedInfraObjects))
            //    .ToList();

            //var assignedInfraObjects = assignedInfra.Where(y => y.Id.Equals(alert.InfraObjectId)).ToList();

            //var alertDto = new List<Domain.Entities.Alert>();

            //foreach(var assignInfra in assignedInfraObjects)
            //{
            //    var listOfAlertCount =await _alertRepository
            //        .GetAlertByUserLastInfraObjectId(assignInfra.Id, alert.CreatedDate);

            //    alertDto.AddRangeAsync(listOfAlertCount);
            //};
            alertList.CompanyId = _loggedInUserService.CompanyId;
            alertList.UserId = _loggedInUserService.UserId;
            alertList.UserName = _loggedInUserService.LoginName;
            alertList.AlertId = alert.Id == 0 ? 0 : alert.Id;
            alertList.AlertCount = alertListVmQuery.Count();
        }

        return alertList;
    }
}