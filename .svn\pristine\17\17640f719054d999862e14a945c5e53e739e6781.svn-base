﻿using ContinuityPatrol.Application.Features.DataSet.Commands.Create;
using ContinuityPatrol.Application.Features.DataSet.Commands.Update;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetNames;
using ContinuityPatrol.Domain.ViewModels.DataSetModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class DataSetProfile : Profile
{
    public DataSetProfile()
    {
        CreateMap<CreateDataSetCommand, DataSetModel>().ReverseMap();
        CreateMap<UpdateDataSetCommand, DataSetModel>().ReverseMap();

        CreateMap<DataSet, DataSetDetailVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DataSet, DataSetListVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DataSet, DataSetNameVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<DataSet, CreateDataSetCommand>().ReverseMap();
        CreateMap<UpdateDataSetCommand, DataSet>().ForMember(x => x.Id, y => y.Ignore());
        
        CreateMap<PaginatedResult<DataSet>,PaginatedResult<DataSetListVm>>().ForMember(x => x.Data, y => y.MapFrom(src=>src.Data));
    }

}