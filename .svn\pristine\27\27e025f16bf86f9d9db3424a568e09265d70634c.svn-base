using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AdPasswordExpireModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class AdPasswordExpireService : BaseClient, IAdPasswordExpireService
{
    public AdPasswordExpireService(IConfiguration config, IAppCache cache, ILogger<AdPasswordExpireService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<AdPasswordExpireListVm>> GetAdPasswordExpireList()
    {
        var request = new RestRequest("api/v6/adpasswordexpires");

        return await GetFromCache<List<AdPasswordExpireListVm>>(request, "GetAdPasswordExpireList");
    }

    public async Task<BaseResponse> CreateAsync(CreateAdPasswordExpireCommand createAdPasswordExpireCommand)
    {
        var request = new RestRequest("api/v6/adpasswordexpires", Method.Post);

        request.AddJsonBody(createAdPasswordExpireCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateAdPasswordExpireCommand updateAdPasswordExpireCommand)
    {
        var request = new RestRequest("api/v6/adpasswordexpires", Method.Put);

        request.AddJsonBody(updateAdPasswordExpireCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/adpasswordexpires/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<AdPasswordExpireDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/adpasswordexpires/{id}");

        return await Get<AdPasswordExpireDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsAdPasswordExpireNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/adpasswordexpires/name-exist?adpasswordexpireName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<AdPasswordExpireListVm>> GetPaginatedAdPasswordExpires(GetAdPasswordExpirePaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/adpasswordexpires/paginated-list");

      return await Get<PaginatedResult<AdPasswordExpireListVm>>(request);
  }
   #endregion
}
