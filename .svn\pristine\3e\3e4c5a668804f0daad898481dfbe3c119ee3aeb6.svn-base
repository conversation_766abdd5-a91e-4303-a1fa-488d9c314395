﻿let InfraObjectDetails = [];
let workflowOperationdetails = [];
let customExecutionArray = [];
let versionDetails = [];
let globalWorkflowRunBookArray = []
$('#attachedInfraObjectName, #attachedInfraObjectType, #attachedProfileName').parent().hide();

$('#workflowAttach').on('click', async function () {
    if (!$(".checkSaveWorkflow").is(':visible')) {
        btnDisableWhileClick()
        if ($('#workflowAttach').text() === "Attach") {
            if ($('#btnWorkflowPublish').text() === 'Unpublish') {
                $('#actionTypeAttach').val('').trigger('change');
                $('#infraObjectList').val('').trigger('change');
                $('#infraAttachModal').modal('show');
            }
            else {
                notificationAlert("warning", 'Publish the workflow before attaching an infraObject')
            }
        } else {
            await attachInfraObject(GlobalWorkflowId, 'de-attach')
        }
    } else {
        notificationAlert('info', 'Unsaved changes in workflow')
    }
   
});


const attachInfraObject = async(Id, mode = 'default') => {
    let data = {}
    data.workflowId = Id ? Id : GlobalWorkflowId

    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.GetInfraDetails,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                if (Array.isArray(result.data) && result.data.length > 0) {
                    $('#workflowDeleteData').text(result?.data[0]?.workflowName).attr('title', result?.data[0]?.workflowName)
                    $('#infraDeleteData').text(result?.data[0]?.infraObjectName).attr('title', result?.data[0]?.infraObjectName)
                    $('#attachedInfraObjectName').text(result?.data[0]?.infraObjectName).attr('title', result?.data[0]?.infraObjectName)             
                    $('#attachedInfraObjectType').text(result?.data[0]?.actionType).attr('title', result?.data[0]?.actionType)             
                    InfraObjectDetails = result?.data
                    if (mode === 'de-attach') $('#detachWorkflow').modal('show')    
                    $('#attachedInfraObjectName, #attachedInfraObjectType').parent().show();
                }
            } else {
                errorNotification(result)
            }
        }
    })
}

$('#btnAttachWorkFlow').on('click', async function () {
    infraObjectListId = $('#infraObjectList').val();
    actionTypeAttachId = $('#actionTypeAttach').val();
    let infraName = $('#infraObjectList option:selected').text()
    let attachedActionType = $('#actionTypeAttach option:selected').text();
    let isOperationType = actionType(actionTypeAttachId)
    let isInfraObject = infraObjectList(infraObjectListId)

    if (isOperationType && isInfraObject) {
        workflowData = {
            actionType: attachedActionType,
            infraObjectId: $('#infraObjectList').val(),
            infraObjectName: infraName,
            isAttach: true,
            totalRTO: "0",
            workflowId: GlobalWorkflowId,
            workflowName: $('#workflowNameForAttach').val(),
            workflowVersion: "0",
            __RequestVerificationToken: gettoken()
        }
        $.ajax({
            type: "POST",
            url: RootUrl + Urls.attachInfra,
            data: workflowData,
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result.success) {
                    $('#infraAttachModal').modal('hide')
                    $('#attachedInfraObjectName').text(infraName).attr('title', infraName)
                    $('#attachedInfraObjectType').text(attachedActionType).attr('title', attachedActionType)            
                    $('#attachedInfraObjectName, #attachedInfraObjectType').parent().show();
                    $('#workflowAttach').html("<i class='cp-url me-1'></i>Detach")
                    notificationAlert("success", result.data.message)
                } else {
                    errorNotification(result)
                }        
            },
        })
        Arr = [];
    }
})


$('#btnMarkAction').on('click', function () {
    if ($('#btnMarkAction').text() === "Mark Action") {
        $('.actionCheckBox').show();
        $('.groupCheckBox').show();
        $(".workflowActions").removeClass("selectedWfContainer");
        $('.parallelCont').removeClass("selectedWfContainer");
        $(".groupClass").removeClass("selectedWfContainer");
        $('#btnMarkAction').html('<i class="cp-mark-action me-1"></i>Unmark Action')
        $('.markAllCheck').prop('checked', false)
        $('#markAllContainer').removeClass('d-none')
        $('#markAllContainer').addClass('d-flex')
        
    } else {
        $('.actionCheckBox').hide();
        $('.groupCheckBox').hide();
        $('input[name="actionCheck"]').prop('checked', false)
        $('input[name="groupCheck"]').prop('checked', false)
        $('#btnMarkAction').html('<i class="cp-mark-action me-1"></i>Mark Action')
        $('#markAllContainer').removeClass('d-flex')
        $('#markAllContainer').addClass('d-none')
        
    }
    
})

$(document).on('change','.markAllCheck', function (e) {
    if (e.target.checked) {
        $('input[name="actionCheck"]').prop('checked', true)
        $('input[name="groupCheck"]').prop('checked', true)
        $('.markAllLabel').text('Unmark All Actions')
    } else {
        $('input[name="actionCheck"]').prop('checked', false)
        $('input[name="groupCheck"]').prop('checked', false)
        $('.markAllLabel').text('Mark All Actions')
    }
})

$(document).on('change', '.actionCheckBox', function (e) {   
    e.stopPropagation();
    clickCount = 0;   
    setTimeout(() => {
        $("#" + e.target.parentNode.id).removeClass('selectedWfContainer')
    }, 200)
})

$(document).on('change', '.groupCheckBox', function (e) {
    e.preventDefault();
    let getParentId = $(this).attr('parentId')
    let getGroupId = $('#' + getParentId).parents('.parentGroup')[0].id
    if (e.target.checked) {
        $('#' + getGroupId).find('.actionCheckBox').prop('checked', true)
        setTimeout(() => {
            $("#" + getGroupId + ' .groupClass').removeClass('selectedWfContainer')
        }, 100)
    } else {
        $('#' + getGroupId).find('.actionCheckBox').prop('checked', false)
        setTimeout(() => {
            $("#" + getGroupId + ' .groupClass').removeClass('selectedWfContainer')
        }, 100)
    }
})


const getCustomExecutionData = () => {
    customExecutionArray = [];
    $('#workflowActions').children().each(function (idx, obj) {
        let id = obj.children[1].id;
        let details;
        if (id.includes('node')) {
            let decodedDetails = atob($('#' + id).attr('details'))
            details = JSON.parse(decodedDetails)
            if ($('#' + id + ' .actionCheckBox').prop('checked')) {
                details['isCustom'] = true;
            } else {
                details['isCustom'] = false;
            }

            if (details.actionInfo.hasOwnProperty('IsGroup')) {
                delete details.actionInfo['IsGroup']
            }

            customExecutionArray.push(details)
        } else if (id.includes('parallel')) {
            let parallelArray = []
            $('#' + id).children().each(function (idx, child) {
                let childId = child.id;
                details = atob($('#' + childId).attr('details'))
                let parsedDetails = JSON.parse(details)
                parsedDetails.actionInfo['IsParallel'] = true;
                if ($('#' + childId + ' .actionCheckBox').prop('checked')) {
                    parsedDetails['isCustom'] = true;
                } else {
                    parsedDetails['isCustom'] = false;
                }
                if (parsedDetails.actionInfo.hasOwnProperty('IsGroup')) {
                    delete parsedDetails.actionInfo['IsGroup']
                }
                parallelArray.push(parsedDetails)
            })
            customExecutionArray.push({ 'actionInfo': { 'IsParallel': true }, 'children': parallelArray })

        } else if (id.includes('group')) {
            let groupName = obj.children[1].getAttribute('groupName')
            let groupColor = obj.children[1].getAttribute('groupColor')
            let groupArray = [];
            $('#' + id + ' .accordion-body').children().each(function (idx, child) {

                let groupChild
                if (idx !== 0) {
                    groupChild = child.children[1].id;
                } else {
                    groupChild = child.children[0].id;
                }
                if (groupChild.includes('node')) {
                    details = atob($('#' + groupChild).attr('details'))
                    let parsedDetails = JSON.parse(details)

                    if ($('#' + groupChild + ' .actionCheckBox').prop('checked')) {
                        parsedDetails['isCustom'] = true;
                    } else {
                        parsedDetails['isCustom'] = false;
                    }

                    parsedDetails.actionInfo['IsGroup'] = true;

                    groupArray.push(parsedDetails)
                } else if (groupChild.includes('parallel')) {
                    let groupParallelArray = []
                    $('#' + groupChild).children().each(function (idx, child) {
                        let childId = child.id;
                        details = atob($('#' + childId).attr('details'))
                        let parsedDetails = JSON.parse(details)
                        parsedDetails.actionInfo['IsParallel'] = true;
                        parsedDetails.actionInfo['IsGroup'] = true;

                        if ($('#' + childId + ' .actionCheckBox').prop('checked')) {
                            parsedDetails['isCustom'] = true;
                        } else {
                            parsedDetails['isCustom'] = false;
                        }
                        groupParallelArray.push(parsedDetails)
                    })
                    groupArray.push({ 'actionInfo': { 'IsParallel': true }, 'children': groupParallelArray })
                }
            });
            customExecutionArray.push({ 'groupName': groupName, 'groupColor': groupColor, 'groupActions': groupArray }, ...groupArray)
        }
    })
    return customExecutionArray;
}


$("#btnDetachWorkflow").on('click', function () {
    workflowData = {
        actionType: InfraObjectDetails[0].actionType,
        infraObjectId: InfraObjectDetails[0].infraObjectId,
        isAttach: false,
        totalRTO: "0",
        workflowId: InfraObjectDetails[0].workflowId,
        workflowVersion: "0",
    }

    $.ajax({
        type: "DELETE",
        url: RootUrl + Urls.DetachInfraObject,
        data: workflowData,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {              
                $('#workflowAttach').html("<i class='cp-url me-1'></i>Attach")
                notificationAlert("success", result.data.message)
                $('#attachedInfraObjectName, #attachedInfraObjectType').parent().hide();
            } else {
                errorNotification(result)
            }    
        },     
    })
    setTimeout(() => {
        $('#detachWorkflow').modal('hide');
    },1000)
  
})

//<-----  workflow Lock  ----->
$('#btnWorkflowLock').on('click', function () {
    if (!$(".checkSaveWorkflow").is(':visible')) {
        btnDisableWhileClick()
        $('#workflowLockData').text(GlobalWorkflowName).attr('title', GlobalWorkflowName)
        GlobalIsLock ? $('.updateLockText').text('unlock') : $('.updateLockText').text('lock')
        $('#lockWorkFlow').modal('show')
    } else {
        notificationAlert('info', 'Unsaved changes in workflow')
    }  
})

$('#btnSaveLockWorkflow').on('click', async function () {
    GlobalIsLock ? GlobalIsLock = false : GlobalIsLock = true
    let lockData = {
        id: GlobalWorkflowId,
        isLock: GlobalIsLock
    }
    await $.ajax({
        type: "PUT",
        url: RootUrl + Urls.workflowLock,
        data: lockData,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                $('#lockWorkFlow').modal('hide')
                notificationAlert("success", result.data.message)
                GlobalIsLock ? $('#btnWorkflowLock').html("<i class='cp-open-lock'></i>Unlock") : $('#btnWorkflowLock').html("<i class='cp-lock'></i>Lock")
                disableSortable()
                GlobalIsLock ? $('#workflowLockStatus').show() : $('#workflowLockStatus').hide()
                $('#btnPaste').hide();
                selectedActions = [];
                localStorage.removeItem('copyValue')

            } else {
                errorNotification(result)
                $('#lockWorkFlow').modal('hide')
            } 
        },
    })
})

//<---- Workflow Verify ---->
$('#btnWorkflowVerify').on('click', function () {
    if (!$(".checkSaveWorkflow").is(':visible')) {
        btnDisableWhileClick();
        $('#verifyWorkflowText').text(GlobalWorkflowName).attr('title', GlobalWorkflowName);
        GlobalIsVerify ? $('.updateVerifyText').text('unverify') : $('.updateVerifyText').text('verify');
        $('#verifyWorkFlowModal').modal('show');
    } else {
        notificationAlert('info', 'Unsaved changes in workflow')
    }
   
})

$('#btnVerifyWorkflow').on('click', async function () {
    let lockData = {
        id: GlobalWorkflowId,
        name: GlobalWorkflowName,
        isFreeze: !GlobalIsVerify
    }
    await $.ajax({
        type: "PUT",
        url: RootUrl + Urls.UpdateWorkflowVerify,
        data: lockData,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                GlobalIsVerify = !GlobalIsVerify
                $('#verifyWorkFlowModal').modal('hide')
                notificationAlert("success", result.data.message)
                GlobalIsVerify ? $('#btnWorkflowVerify').html("<i class='cp-workflow-unverify me-1'></i>Unverify") : $('#btnWorkflowVerify').html("<i class='cp-workflow-verify me-1'></i>Verify")
                GlobalIsVerify ? $('#workflowVerifyStatus').show() : $('#workflowVerifyStatus').hide()
                //GlobalIsLock ? $('#workflowLockStatus').show() : $('#workflowLockStatus').hide()
               // $('#btnPaste').hide();
               // selectedActions = [];
               // localStorage.removeItem('copyValue')

            } else {
                errorNotification(result)
                $('#verifyWorkFlowModal').modal('hide')
            }
        },
    })
})

//<---- workflow Publish ---->
$('#btnWorkflowPublish').on('click', function () {
    if (!$(".checkSaveWorkflow").is(':visible')) {
        btnDisableWhileClick()
        if ($('#workflowAttach').text() === 'Detach') {
            notificationAlert("warning", 'Detach an infraObject before unpublishing')
            return false;
        }
        $('#workflowPublishData').text(GlobalWorkflowName).attr('title', GlobalWorkflowName)
        $('.updatePublishText').text($('#btnWorkflowPublish').text().toLowerCase())
        $('#publishWorkFlow').modal('show')
    } else {
        notificationAlert('info', 'Unsaved changes in workflow')
    }
   
})

$('#btnSavePublishWorkflow').on('click',async function () {
    GlobalIsPublish ? GlobalIsPublish = false : GlobalIsPublish = true
    let lockData = {
        id: GlobalWorkflowId,
        isPublish: GlobalIsPublish
    }
    await $.ajax({
        type: "PUT",
        url: RootUrl + Urls.workflowPublish,
        data: lockData,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                $('#publishWorkFlow').modal('hide')
                notificationAlert("success", result.data.message)
                GlobalIsPublish ? $('#btnWorkflowPublish').html("<i class='cp-Unpublish me-1'></i>Unpublish") : $('#btnWorkflowPublish').html("<i class='cp-publish me-1'></i>Publish")
            } else {
                errorNotification(result)
                $('#publishWorkFlow').modal('hide')
            }
        },
    })

})
   


//----- Context Menu shortCut Keys ----//
$(document).on("keydown", function (event) {
    let selectedActions = $('.selectedWfContainer')

    if (event.key === 'Enter' && event.target.id === 'search_category') {
        if ($('#search_category').val().length) {
            $('#btn_search').trigger('click')
        }
    }

    if (event.target.tagName !== "INPUT" && event.target.tagName !== "TEXTAREA" && event.target.type !== "text") {
        if ((event.ctrlKey || event.metaKey) && event.key === "a") {
            event.preventDefault();
            if (!$('.actionCheckBox').is(':visible')) {
                selectAllActions();
            }
        }
        if ((event.ctrlKey || event.metaKey) && event.key === "d") {
            event.preventDefault();
            document.querySelector('#btnWorkFlowReport').click();
        }
        if ((event.ctrlKey || event.metaKey) && event.key === "x") {
            event.preventDefault();
            if (selectedActions.length > 0 && $('#workflowActions').children().length > $('#workflowActions .selectedWfContainer').length) {
                cutWorkflowActions();
            }
        } else if ((event.ctrlKey || event.metaKey) && event.key === "s") {
            event.preventDefault();
            if (!$('#btnSaveModalOpen').prop('disabled')) {
                openSaveModal();
            }
        } else if ((event.ctrlKey || event.metaKey) && event.key === "e") {
            event.preventDefault();
            event.stopPropagation();

            if (selectedActions.length == 1) {
                let selectedId = selectedActions.attr('id');

                actionEditData(selectedId);
                $('#filter_property').dropdown('hide')
            }
        }
        else if ((event.ctrlKey || event.metaKey) && event.key === "c") {

            event.preventDefault();
            if (selectedActions.length > 0) {
                copyWorkflowActions(event);
            }
        } else if ((event.ctrlKey || event.metaKey) && event.key === "v") {
            event.preventDefault();
            btnDebounce(BtnPasteWorkFlow(),500);
        } else if (event.key === "Delete") {
            if ($('.workflowActions.selectedWfContainer').length || $('.parallelCont.selectedWfContainer').length || $('.groupClass.selectedWfContainer').length) {
                event.preventDefault();
                actionDeletion()
            }
        } 
        else if ((event.ctrlKey || event.metaKey) && event.key === "p") {
            event.preventDefault();
            let checkParallel = [];
            selectedActions.each((idx, obj) => {
                if ($('#' + obj.id).hasClass('workflowActions') && $('#' + obj.id).parents('.parallelCont').length === 0) {
                    checkParallel.push(true)
                } else if ($('#' + obj.id).hasClass('parallelCont') || $('#' + obj.id).parents('.parallelCont').length > 0) {
                    checkParallel.push(false)
                } else if ($('#' + obj.id).hasClass('groupClass') || $('#' + obj.id).parents('.parallelCont').length > 0) {
                    checkParallel.push(false)
                }
            })
            if (!checkParallel.includes(false)) {
                if (selectedActions.length > 1) {
                    if (!$('.selectedWfContainer').filter('[isindependent=true]').length) {
                        ParallelActions();
                    } else {
                        removeSelection();
                        notificationAlert('warning', 'Independent actions cannot be permitted to parallel operations.')
                    }
                    
                }
            } else {
                notificationAlert("warning", "Cannot parallel with these selections")
            }
        } else if ((event.ctrlKey || event.metaKey) && event.key === "g") {
            event.preventDefault();
            let checkGroup = [];
            selectedActions.each((idx, obj) => {
                if ($('#' + obj.id).hasClass('workflowActions')) {
                    checkGroup.push(true)
                } else if ($('#' + obj.id).hasClass('parallelCont')) {
                    checkGroup.push(true)
                } else if ($('#' + obj.id).hasClass('groupClass')) {
                    checkGroup.push(false)
                }
            })
            if (!checkGroup.includes(false)) {
                if (selectedActions.length > 1) {
                    createGroupActions();
                }
            } else {
                notificationAlert("warning", "Cannot group with these selections")
            }
        }
    }
});


$(document).on('mouseenter', '.workflowIcon', function (e) {

    let id = $(this).parents('.workflowActions').attr('id');
    let decodedDetails = atob($('#' + id).attr('details'));
    let details = JSON.parse(decodedDetails);


    let tableData = `<div class="card shadow-lg mb-0 bg-white"><div class="card-header fs-7 fw-semibold px-2" style="margin-right: 110px;"><i class="cp-configure-settings me-1"></i>Action Properties</div><div class="card-body p-0"><table class='table mb-0'>
        <tbody>
            <tr>
                <td><i class="cp-action-name me-2 text-primary"></i>Action Name</td>
                <td>:</td>
                <td class='fw-medium text-wrap'>${details.actionInfo.actionName}</td>
            </tr
             <tr>
                <td><i class="cp-description me-2 text-primary"></i>Description</td>
                <td>:</td>
                <td class='fw-medium'>${details.actionInfo.description || 'NA'}</td>
            </tr>
            <tr>
                <td><i class="cp-RTO me-2 text-primary"></i>RTO</td>
                <td>:</td>
                <td class='fw-medium'>${details.actionInfo.rto}</td>
            </tr>
              <tr>
                <td><i class="cp-activity-type me-2 text-primary"></i>Action Category</td>
                <td>:</td>
                <td class='fw-medium'>${details?.actionInfo?.propertyData?.actionType}</td>
            </tr>`
    if (details.actionInfo.propertyData?.propertiesInfo.length) {
        details.actionInfo.propertyData?.propertiesInfo.forEach((x) => {
            if (x?.label && x?.value && !x?.value?.includes('encryptedcpl')) {
                let labelText = x.label.toLowerCase()
                let icon = labelText.includes('server') ? 'cp-server' : labelText.includes('database') || labelText.includes('db') ? 'cp-database' : labelText.includes('replication') ? 'cp-replication-on' : 'cp-text'           
                tableData += `<tr>
                <td><i class="${icon} me-2 text-primary"></i>${x.label}</td>
                <td>:</td>
                <td class='fw-medium'>${x.value}</td>
            </tr>`
            }
            
        })
    }
    tableData += `</tbody></table></div></div>`

    let topPosition = $(this).parents('.workflowActions').offset().top - 50 + 'px';
    let leftPosition = ($(this).parents('.workflowActions').offset().left + $('.workflowActions').outerWidth() + 10) + 'px';

    $('<div class="workflowTableTooltip"></div>')
        .html(tableData)
        .appendTo('#parentAction')
        .fadeIn('slow');

    if ($(window).height() < $(this).parents('.workflowActions').offset().top + $('.workflowTableTooltip').outerHeight()) {
        topPosition = $(window).height() - $('.workflowTableTooltip').outerHeight() - 100 + 'px';
    }

    if ($('#parentAction').width() < $(this).parents('.workflowActions').offset().left + 20 + $('.workflowTableTooltip').outerWidth()) {
        leftPosition = $(this).parents('.workflowActions').offset().left - ($('.workflowTableTooltip').outerWidth() + 20) + 'px';
    }

    $('.workflowTableTooltip').css({
            top: topPosition,
        left: leftPosition,
           "z-index":"99999"
        })
})

$(document).on('mouseleave', '.workflowIcon', function () {
    $('.workflowTableTooltip').remove();
})


//Load JSON from runbook

$('#btnUploadRunbook').on('click', function () {
    $('#uploadWorkflow').val('')
    $('#uploadRunbookWorkflow').attr('disabled', false)
    $('#WFUploadRunbookLoader').addClass('d-none')
    $('#uploadWorkflow-error').text('').removeClass('field-validation-error');
    $('#UploadRunbookWorkflowModal').modal('show')
})

$('#uploadWorkflow').on('change', function (e) {
    var file = e.target.files[0];
    if (file) {
        var reader = new FileReader();
        reader.onload = function (e) {
            var data = e.target.result;
            var workbook = XLSX.read(data, { type: 'binary' });
            var firstSheet = workbook.Sheets[workbook.SheetNames[0]];

            // Convert sheet data to JSON
            var jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
            convertJsonToWorkflow(jsonData)

        }
        reader.readAsBinaryString(file);
    }
    const value = $(this).val();
    importExportValidation(value, 'importWorkflow-error', 'Choose workflow');
})

const getServerByIP = async (ipAddress) => {
    let resutData = []

    let data = {}
    data.IPAddress = ipAddress
  
    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.GetServerByServerIPAddress,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            resutData = result
        }
    })
    return resutData;
}

const getActionByType = async (actionName) => {
    let resultData = []
    let data = {}
    data.actionName = actionName

    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.GetActionByActionType,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            resultData = result
        }
    })
    return resultData;
}

const convertJsonToWorkflow = (json) => {
    $('#uploadRunbookWorkflow').attr('disabled', true)
    $('#WFUploadRunbookLoader').removeClass('d-none')
    
    let modifiedArray = []
    let jsonLength = json.length
    if (jsonLength) {
        for (let i = 0; i < jsonLength; i++) {
            if (i !== 0) {
                let filterdData = json[i]?.filter((d) => d?.toString().trim().length !== 0)
                let obj = {}
                filterdData.forEach((d, i) => {
                    obj[json[0][i]] = d
                })
                if (Object.keys(obj).length) {
                    modifiedArray.push(obj)
                }
            }
        }
    }
    getDetailsFromDB(modifiedArray)
    return modifiedArray;
}

const getDetailsFromDB = async (modifiedArray) => {
    
    let WorkflowRunBookArray = [];
    globalWorkflowRunBookArray = []
    let parallelRunbookArray = []
    if (modifiedArray?.length) {
        for (let i = 0; i < modifiedArray?.length; i++) {
            let wfObj = {
                actionInfo: {
                    properties: {}, propertyData: { propertiesInfo: [] }, formInput: []
                }
            }

            let splitParameter = modifiedArray[i]?.Parameters?.split('|')
            let splitData = []
            splitParameter?.length && splitParameter.forEach((h) => {
                let convertData = h?.split(':')
                splitData.push({ key: convertData[0], value: convertData[1]})
            })

            let getServer = await getServerByIP(modifiedArray[i]?.ServerName)
            let getActionName = await getActionByType(modifiedArray[i]['Action Category'])

            wfObj['stepId'] = generateStepId();
            wfObj.actionInfo.uniqueId = getRandomId('node')


            if (getActionName?.success) {
                let parsedProp = JSON.parse(getActionName?.data?.properties)
                Object.values(parsedProp.formInput.fields).forEach((d) => {
                    if (!d?.attrs?.name?.toLowerCase().includes('servername')) {

                        splitData?.length && splitData.forEach(async (p) => {
                            if (d.attrs.name.toLowerCase().includes(p.key.trim().toLowerCase())) {
                                if (d.meta.id === "command" || d.meta.id === "textarea" || d.attrs.name.toLowerCase().includes('command')) {
                                    let value = p.value.trim();
                                    const encryptedValue = await $.ajax({
                                        type: "POST",
                                        url: RootUrl + 'ITAutomation/WorkflowConfiguration/WorkFlowDataEncrypt',
                                        data: { data: value, __RequestVerificationToken: gettoken() },
                                        dataType: 'text'
                                    });

                                    let encryptedData = JSON.parse(encryptedValue);
                                    inputValue = encryptedData?.data && encryptedData?.data + '_encryptedcpl';

                                    //newActionObj.actionInfo.properties[inputName] = inputValue;
                                    wfObj.actionInfo.properties[d.attrs.name] = inputValue
                                } else if (d.attrs.type === 'select') {

                                    let inputVal = p.value.trim() === 'true' ? "True" : p.value.trim() === 'false' ? 'False' : p.value
                                    let obj = {
                                        "label": d.config.label,
                                        "type": "select",
                                        "name": d.attrs.name,
                                        "optionType": "",
                                        "optionRoleType": "",
                                        "id": inputVal,
                                    }

                                    let propObj = {
                                        "id": inputVal,
                                        "label": d.config.label,
                                        "value": inputVal,
                                    }

                                    wfObj.actionInfo.propertyData.propertiesInfo.push(propObj)
                                    wfObj.actionInfo.formInput.push(obj)
                                    wfObj.actionInfo.properties[d.attrs.name] = inputVal

                                }
                                else {

                                    let propObj = {
                                        "id": p.value,
                                        "label": d.config.label,
                                        "value": p.value,
                                    }

                                    wfObj.actionInfo.properties[d.attrs.name] = p.value.toString().toLowerCase().trim()
                                    wfObj.actionInfo.propertyData.propertiesInfo.push(propObj)
                                    wfObj.actionInfo.properties[d.attrs.name] = p.value.toLowerCase().trim()
                                }

                            }
                        })
                    } else {
                        let obj = {
                            "label": d.config.label,
                            "type": "server",
                            "name": d.attrs.name,
                            "ServerRole": "",
                            "ServerType": "",
                            "optionType": getServer?.data[0]?.serverType,
                            "optionRoleType": getServer?.data[0]?.roleType,
                            "id": getServer?.data[0]?.id,
                        }
                        let propObj = {
                            "id": getServer?.data[0]?.id,
                            "label": d.config.label,
                            "value": getServer?.data[0]?.name,
                        }

                        wfObj.actionInfo.properties[d.attrs.name] = getServer?.data[0]?.id
                        wfObj.actionInfo.propertyData.propertiesInfo.push(propObj)
                        wfObj.actionInfo.formInput.push(obj)
                    }
                })
            } else {
                $('#uploadWorkflow-error').text('').removeClass('field-validation-error');
            }

            let actionName = modifiedArray[i]['Action Name'];

            wfObj.actionInfo.actionType = getActionName?.data?.id
            wfObj.actionInfo.actionName = actionName
            wfObj.actionInfo.description = actionName
            wfObj.actionInfo.rto = 3
            wfObj.actionInfo.email = false
            wfObj.actionInfo.isConditional = false
            wfObj.actionInfo.sms = false
            wfObj.actionInfo.nodeId = getActionName?.data?.nodeId

            //wfObj.actionInfo.isParallel = modifiedArray[i]['IsParallel']

            wfObj.actionInfo.propertyData['actionType'] = modifiedArray[i]['Action Category']

            let ColorData = $('.nodeSummaryData[nodeId=' + getActionName?.data?.nodeId + ']').attr('parentColor')
            let iconData = `${$('.nodeSummaryData[nodeId=' + getActionName?.data?.nodeId + ']').attr('parentIcon') || 'cp-flow'}`

            wfObj.actionInfo.icon = iconData
            wfObj.actionInfo.color = ColorData
            //"nodeId": "26930d7a-1b11-4eb0-af8d-55224909c988",
            //"parentActionId": "8e084897-13e9-4dc6-bc04-df859c05503f",

            if (!modifiedArray[i]['IsParallel']) {
                if (!parallelRunbookArray.length) {
                    WorkflowRunBookArray.push(wfObj)
                } else if (parallelRunbookArray.length === 1) {
                    WorkflowRunBookArray = WorkflowRunBookArray.concat(parallelRunbookArray)
                    WorkflowRunBookArray.push(wfObj)
                    parallelRunbookArray = []
                } else if (parallelRunbookArray.length > 1) {
                    let parallelobj = { 'actionInfo': { 'IsParallel': true }, 'children': parallelRunbookArray }
                    WorkflowRunBookArray.push(parallelobj)
                    WorkflowRunBookArray.push(wfObj)
                    parallelRunbookArray = []
                }
            } else {
                parallelRunbookArray.push(wfObj)
                if (i === modifiedArray.length - 1) {
                    let parallelobj = { 'actionInfo': { 'IsParallel': true }, 'children': parallelRunbookArray }
                    WorkflowRunBookArray.push(parallelobj)
                    parallelRunbookArray = []
                }
            }
            
        }
    }
    //let ParellelArray
    //WorkflowRunBookArray.forEach((d, i) => {

    //    if (d.actionInfo.isParallel) {
    //        if (WorkflowRunBookArray[i + 1]?.actionInfo.IsParallel || i === WorkflowRunBookArray.length - 1) {

    //        }
    //    }
    //})

    globalWorkflowRunBookArray = WorkflowRunBookArray
    $('#WFUploadRunbookLoader').addClass('d-none')
    $('#uploadRunbookWorkflow').attr('disabled', false)
    //loadWorkFlow(WorkflowRunBookArray)
   // return WorkflowRunBookArray;
}

$('#uploadRunbookWorkflow').on('click', function (e) {
    e.preventDefault();
    const fileInput = $('#uploadWorkflow')[0];
    const file = fileInput.files[0];
    let exportValidation = importExportValidation(file, 'uploadWorkflow-error', 'Upload runbook')
    if (exportValidation) {
        if (file.type === 'application/json') {
            $('#UploadRunbookWorkflowModal').modal('hide')
            workflowContainer.empty()
            loadWorkFlow(globalWorkflowRunBookArray)
            GlobalWorkflowId = '';
            $('#btnSaveModalOpen').prop('disabled', false);
            $('#versionText, #workflowVersion').text('')
            $('#workflowTitle').text('Untitled Workflow');
            workflowSaveMode = "Save";
            isTemplateWorkFlow = false
            $(".checkSaveWorkflow").show();
        } else {
            $('#uploadWorkflow-error').text('Only XLS and XLSX formats are accepted').addClass('field-validation-error');
        }
    } 
})









