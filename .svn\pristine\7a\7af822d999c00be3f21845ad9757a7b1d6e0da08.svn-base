﻿using AutoFixture;
using ContinuityPatrol.Application.Features.VeritasCluster.Commands.Create;
using ContinuityPatrol.Application.Features.VeritasCluster.Commands.Update;
using ContinuityPatrol.Application.Features.VeritasCluster.Events.PaginatedView;
using ContinuityPatrol.Application.Features.VeritasCluster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ServerModel;
using ContinuityPatrol.Domain.ViewModels.VeritasClusterModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class VeritasClusterShould
    {
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<ILogger<VeritasClusterController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private VeritasClusterController _controller;

        public VeritasClusterShould()
        {
           
            _controller = new VeritasClusterController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_ReturnsViewResult()
        {
            // Act
            var result = await _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<VeritasClusterPaginatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetPaginated_ReturnsJsonResult()
        {
            // Arrange
            var query = new GetVeritasClusterPaginatedListQuery();
            var paginatedList = new PaginatedResult<VeritasClusterListVm>();
            var mockVeritasClusterService = new Mock<IVeritasClusterService>();
            mockVeritasClusterService.Setup(s => s.GetPaginatedVeritasClusters(It.IsAny<GetVeritasClusterPaginatedListQuery>()))
                .ReturnsAsync(paginatedList);
            _mockDataProvider.Setup(dp => dp.VeritasCluster).Returns(mockVeritasClusterService.Object);

            // Act
            var result = await _controller.GetPaginated(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(paginatedList, jsonResult.Value);
            mockVeritasClusterService.Verify(s => s.GetPaginatedVeritasClusters(query), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_CreateVeritasCluster_ReturnsRedirectToActionResult()
        {
            // Arrange
            var veritasClusterModel = new AutoFixture.Fixture().Create<VeritasClusterViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateVeritasClusterCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            var mockVeritasClusterService = new Mock<IVeritasClusterService>();
            mockVeritasClusterService.Setup(s => s.CreateAsync(It.IsAny<CreateVeritasClusterCommand>()))
                .ReturnsAsync(response);
            _mockDataProvider.Setup(dp => dp.VeritasCluster).Returns(mockVeritasClusterService.Object);

            _mockMapper.Setup(m => m.Map<CreateVeritasClusterCommand>(It.IsAny<VeritasClusterViewModel>()))
                .Returns(command);

            // Act
            var result = await _controller.CreateOrUpdate(veritasClusterModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            mockVeritasClusterService.Verify(s => s.CreateAsync(It.IsAny<CreateVeritasClusterCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdateVeritasCluster_ReturnsRedirectToActionResult()
        {
            // Arrange
            var veritasClusterModel = new AutoFixture.Fixture().Create<VeritasClusterViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id","22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateVeritasClusterCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            var mockVeritasClusterService = new Mock<IVeritasClusterService>();
            mockVeritasClusterService.Setup(s => s.UpdateAsync(It.IsAny<UpdateVeritasClusterCommand>()))
                .ReturnsAsync(response);
            _mockDataProvider.Setup(dp => dp.VeritasCluster).Returns(mockVeritasClusterService.Object);

            _mockMapper.Setup(m => m.Map<UpdateVeritasClusterCommand>(It.IsAny<VeritasClusterViewModel>()))
                .Returns(command);

            // Act
            var result = await _controller.CreateOrUpdate(veritasClusterModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            mockVeritasClusterService.Verify(s => s.UpdateAsync(It.IsAny<UpdateVeritasClusterCommand>()), Times.Once);
        }

        [Fact]
        public async Task GetServerList_ReturnsJsonResult()
        {
            // Arrange
            var serverNames = new List<ServerNameVm>();
            var mockServerService = new Mock<IServerService>();
            mockServerService.Setup(s => s.GetServerNames())
                .ReturnsAsync(serverNames);
            _mockDataProvider.Setup(dp => dp.Server).Returns(mockServerService.Object);

            // Act
            var result = await _controller.GetServerList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(serverNames, jsonResult.Value);
            mockServerService.Verify(s => s.GetServerNames(), Times.Once);
        }

        [Fact]
        public async Task Delete_ReturnsRedirectToActionResult()
        {
            // Arrange
            var id = "123";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            var mockVeritasClusterService = new Mock<IVeritasClusterService>();
            mockVeritasClusterService.Setup(s => s.DeleteAsync(It.IsAny<string>()))
                .ReturnsAsync(response);
            _mockDataProvider.Setup(dp => dp.VeritasCluster).Returns(mockVeritasClusterService.Object);

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            mockVeritasClusterService.Verify(s => s.DeleteAsync(id), Times.Once);
        }

        [Fact]
        public async Task IsVeritasClusterNameExist_ReturnsBoolean()
        {
            // Arrange
            var name = "Cluster1";
            var id = "123";
            var mockVeritasClusterService = new Mock<IVeritasClusterService>();
            mockVeritasClusterService.Setup(s => s.IsVeritasClusterNameExist(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(true);
            _mockDataProvider.Setup(dp => dp.VeritasCluster).Returns(mockVeritasClusterService.Object);

            // Act
            var result = await _controller.IsVeritasClusterNameExist(name, id);

            // Assert
            Assert.True(result);
            mockVeritasClusterService.Verify(s => s.IsVeritasClusterNameExist(name, id), Times.Once);
        }

        [Fact]
        public async Task GetPaginated_WithException_ReturnsJsonError()
        {
            // Arrange
            var query = new GetVeritasClusterPaginatedListQuery();
            var mockVeritasClusterService = new Mock<IVeritasClusterService>();
            mockVeritasClusterService.Setup(s => s.GetPaginatedVeritasClusters(It.IsAny<GetVeritasClusterPaginatedListQuery>()))
                .ThrowsAsync(new Exception("Database connection failed"));
            _mockDataProvider.Setup(dp => dp.VeritasCluster).Returns(mockVeritasClusterService.Object);

            // Act
            var result = await _controller.GetPaginated(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            var response = jsonResult.Value as BaseResponse;
            Assert.NotNull(response);
            Assert.False(response.Success);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidationException_ReturnsRedirectToList()
        {
            // Arrange
            var veritasClusterModel = new AutoFixture.Fixture().Create<VeritasClusterViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateVeritasClusterCommand();

            var mockVeritasClusterService = new Mock<IVeritasClusterService>();
            mockVeritasClusterService.Setup(s => s.CreateAsync(It.IsAny<CreateVeritasClusterCommand>()))
                .ThrowsAsync(new ValidationException(new FluentValidation.Results.ValidationResult()));
            _mockDataProvider.Setup(dp => dp.VeritasCluster).Returns(mockVeritasClusterService.Object);

            _mockMapper.Setup(m => m.Map<CreateVeritasClusterCommand>(It.IsAny<VeritasClusterViewModel>()))
                .Returns(command);

            // Act
            var result = await _controller.CreateOrUpdate(veritasClusterModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_WithGeneralException_ReturnsRedirectToList()
        {
            // Arrange
            var veritasClusterModel = new AutoFixture.Fixture().Create<VeritasClusterViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "123");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateVeritasClusterCommand();

            var mockVeritasClusterService = new Mock<IVeritasClusterService>();
            mockVeritasClusterService.Setup(s => s.UpdateAsync(It.IsAny<UpdateVeritasClusterCommand>()))
                .ThrowsAsync(new Exception("Database error"));
            _mockDataProvider.Setup(dp => dp.VeritasCluster).Returns(mockVeritasClusterService.Object);

            _mockMapper.Setup(m => m.Map<UpdateVeritasClusterCommand>(It.IsAny<VeritasClusterViewModel>()))
                .Returns(command);

            // Act
            var result = await _controller.CreateOrUpdate(veritasClusterModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task GetServerList_WithException_ReturnsEmptyString()
        {
            // Arrange
            var mockServerService = new Mock<IServerService>();
            mockServerService.Setup(s => s.GetServerNames())
                .ThrowsAsync(new Exception("Server connection failed"));
            _mockDataProvider.Setup(dp => dp.Server).Returns(mockServerService.Object);

            // Act
            var result = await _controller.GetServerList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        [Fact]
        public async Task Delete_WithException_ReturnsRedirectToList()
        {
            // Arrange
            var id = "123";
            var mockVeritasClusterService = new Mock<IVeritasClusterService>();
            mockVeritasClusterService.Setup(s => s.DeleteAsync(It.IsAny<string>()))
                .ThrowsAsync(new Exception("Delete operation failed"));
            _mockDataProvider.Setup(dp => dp.VeritasCluster).Returns(mockVeritasClusterService.Object);

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task IsVeritasClusterNameExist_WithException_ReturnsFalse()
        {
            // Arrange
            var name = "Cluster1";
            var id = "123";
            var mockVeritasClusterService = new Mock<IVeritasClusterService>();
            mockVeritasClusterService.Setup(s => s.IsVeritasClusterNameExist(It.IsAny<string>(), It.IsAny<string>()))
                .ThrowsAsync(new Exception("Service unavailable"));
            _mockDataProvider.Setup(dp => dp.VeritasCluster).Returns(mockVeritasClusterService.Object);

            // Act
            var result = await _controller.IsVeritasClusterNameExist(name, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task CreateOrUpdate_WithNullId_CallsCreatePath()
        {
            // Arrange
            var veritasClusterModel = new AutoFixture.Fixture().Create<VeritasClusterViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", (string)null); // Null id
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateVeritasClusterCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            var mockVeritasClusterService = new Mock<IVeritasClusterService>();
            mockVeritasClusterService.Setup(s => s.CreateAsync(It.IsAny<CreateVeritasClusterCommand>()))
                .ReturnsAsync(response);
            _mockDataProvider.Setup(dp => dp.VeritasCluster).Returns(mockVeritasClusterService.Object);

            _mockMapper.Setup(m => m.Map<CreateVeritasClusterCommand>(It.IsAny<VeritasClusterViewModel>()))
                .Returns(command);

            // Act
            var result = await _controller.CreateOrUpdate(veritasClusterModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            mockVeritasClusterService.Verify(s => s.CreateAsync(It.IsAny<CreateVeritasClusterCommand>()), Times.Once);
            mockVeritasClusterService.Verify(s => s.UpdateAsync(It.IsAny<UpdateVeritasClusterCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_WithWhitespaceId_CallsCreatePath()
        {
            // Arrange
            var veritasClusterModel = new AutoFixture.Fixture().Create<VeritasClusterViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "   "); // Whitespace id
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateVeritasClusterCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            var mockVeritasClusterService = new Mock<IVeritasClusterService>();
            mockVeritasClusterService.Setup(s => s.CreateAsync(It.IsAny<CreateVeritasClusterCommand>()))
                .ReturnsAsync(response);
            _mockDataProvider.Setup(dp => dp.VeritasCluster).Returns(mockVeritasClusterService.Object);

            _mockMapper.Setup(m => m.Map<CreateVeritasClusterCommand>(It.IsAny<VeritasClusterViewModel>()))
                .Returns(command);

            // Act
            var result = await _controller.CreateOrUpdate(veritasClusterModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            mockVeritasClusterService.Verify(s => s.CreateAsync(It.IsAny<CreateVeritasClusterCommand>()), Times.Once);
            mockVeritasClusterService.Verify(s => s.UpdateAsync(It.IsAny<UpdateVeritasClusterCommand>()), Times.Never);
        }
    }
}
