using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class PageWidgetFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string PageWidgetName => "TestPageWidget";

    public List<PageWidget> PageWidgetPaginationList { get; set; }
    public List<PageWidget> PageWidgetList { get; set; }
    public PageWidget PageWidgetDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public PageWidgetFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<PageWidget>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Name, () => PageWidgetName + "_" + _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow));

        PageWidgetPaginationList = _fixture.CreateMany<PageWidget>(20).ToList();
        PageWidgetList = _fixture.CreateMany<PageWidget>(5).ToList();
        PageWidgetDto = _fixture.Create<PageWidget>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public PageWidget CreatePageWidgetWithProperties(
        string name = null,
        string properties = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<PageWidget>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Name, name ?? PageWidgetName + "_" + _fixture.Create<string>())
            .With(x => x.Properties, properties ?? _fixture.Create<string>())
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public PageWidget CreatePageWidgetWithSpecificName(string name)
    {
        return CreatePageWidgetWithProperties(name: name);
    }

    public List<PageWidget> CreateMultiplePageWidgetsWithSameName(string name, int count)
    {
        var widgets = new List<PageWidget>();
        for (int i = 0; i < count; i++)
        {
            widgets.Add(CreatePageWidgetWithProperties(name: name));
        }
        return widgets;
    }

    public PageWidget CreateInactivePageWidget()
    {
        return CreatePageWidgetWithProperties(isActive: false);
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
