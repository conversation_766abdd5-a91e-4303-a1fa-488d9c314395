namespace ContinuityPatrol.Application.Features.DriftResourceSummary.Commands.Update;

public class
    UpdateDriftResourceSummaryCommandHandler : IRequestHandler<UpdateDriftResourceSummaryCommand,
        UpdateDriftResourceSummaryResponse>
{
    private readonly IDriftResourceSummaryRepository _driftResourceSummaryRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateDriftResourceSummaryCommandHandler(IMapper mapper,
        IDriftResourceSummaryRepository driftResourceSummaryRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _driftResourceSummaryRepository = driftResourceSummaryRepository;
        _publisher = publisher;
    }

    public async Task<UpdateDriftResourceSummaryResponse> Handle(UpdateDriftResourceSummaryCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _driftResourceSummaryRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.DriftResourceSummary), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateDriftResourceSummaryCommand),
            typeof(Domain.Entities.DriftResourceSummary));

        await _driftResourceSummaryRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateDriftResourceSummaryResponse
        {
            // Message = Message.Update(nameof(Domain.Entities.DriftResourceSummary), eventToUpdate.Name),

            Id = eventToUpdate.ReferenceId
        };

        //await _publisher.Publish(new DriftResourceSummaryUpdatedEvent { Name = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}