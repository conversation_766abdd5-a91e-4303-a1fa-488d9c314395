﻿using ContinuityPatrol.Application.Features.BulkImport.Commands.Create.Helper;
using ContinuityPatrol.Application.Features.Workflow.Commands.Create;
using Newtonsoft.Json;
using System.Diagnostics.CodeAnalysis;

namespace ContinuityPatrol.Application.Features.BulkImport.Commands.Create;

public class BulkImportCreateServerValidationCommand : AbstractValidator<CreateBulkDataServerListCommand>
{
    public static string ParameterName = string.Empty;
    private readonly IServerRepository _serverRepository;

    public BulkImportCreateServerValidationCommand(IServerRepository serverRepository)
    {
        _serverRepository = serverRepository;

        RuleFor(p => p.<PERSON>)
            .NotEmpty().WithMessage("Select the {PropertyName}.");

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.SiteName)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull();

        RuleFor(p => p.ServerType)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}");

        RuleFor(p => p.OSType)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull()
            .Matches(@"^[a-zA-Z\d]+([_\s\-\.][a-zA-Z\d]+)*$")
            .WithMessage("Please Enter Valid {PropertyName}");

        RuleFor(p => p.RoleType)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}");

        RuleFor(p => p.Properties)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull();

        RuleFor(p => p)
            .MustAsync(ServerNameUnique)
            .WithMessage("A same name already exists.");

        RuleFor(y => y)
            .NotNull()
            .MustAsync(VerifyGuid)
            .WithMessage(_=>$"Input '{ParameterName ?? "guidString"}' is not valid format.");
    }

    private async Task<bool> VerifyGuid(CreateBulkDataServerListCommand p, CancellationToken cancellationToken)
    {
        var guidProperties = new (string Value, string Name)[]
        {
            (p.SiteId, "Site Id"),
            (p.RoleTypeId, "RoleType Id"),
            (p.ServerTypeId, "ServerType Id"),
            (p.BusinessServiceId, "Operational service Id"),
            (p.OSTypeId, "OsType Id"),
            (p.LicenseId, "License Id")
        };

        foreach (var (value, name) in guidProperties)
            if (!await InvalidGuidOrEmpty(value))
            {
                ParameterName = name;
                return false;
            }

        return true;
    }

    private async Task<bool> ServerNameUnique(CreateBulkDataServerListCommand p, CancellationToken cancel)
    {
        return !await _serverRepository.IsServerNameUnique(p.Name);
    }

    public Task<bool> InvalidGuidOrEmpty([NotNull] string input)
    {
        return Task.FromResult(!string.IsNullOrWhiteSpace(input) && Guid.TryParse(input, out var guid) &&
                               guid != Guid.Empty);
    }
}

public class BulkImportCreateDatabaseCommandValidator : AbstractValidator<CreateBulkDataDataBaseListCommand>
{
    public static string ParameterName = string.Empty;
    private readonly IDatabaseRepository _databaseRepository;

    public BulkImportCreateDatabaseCommandValidator(IDatabaseRepository databaseRepository)
    {
        _databaseRepository = databaseRepository;

        RuleFor(p => p)
            .MustAsync(VerifyGuid)
            .WithMessage(_=>$"Input '{ParameterName ?? "guidString"}' is not valid format.");

        RuleFor(p => p.LicenseKey)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull();

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.DatabaseType)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .Matches(@"^[a-zA-Z\d]+([_\s\-\.][a-zA-Z\d]+)*$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull();

        RuleFor(p => p.BusinessServiceName)
            .NotEmpty().WithMessage("Select Operational Service.")
            .Matches(@"^[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]$").WithMessage("Please Enter Valid Operational Service")
            .NotNull()
            .Length(3, 100).WithMessage("Operational Service should contain between 3 to 100 characters.");

        RuleFor(p => p.ServerName)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.Properties)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull();

        RuleFor(p => p.ModeType)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull();

        RuleFor(p => p)
            .MustAsync(DatabaseNameUnique)
            .WithMessage("A same Name already exist.");

        RuleFor(p => p.Type)
            .NotEmpty().WithMessage("Select {PropertyName}")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull();
    }

    private async Task<bool> DatabaseNameUnique(CreateBulkDataDataBaseListCommand p,
        CancellationToken cancellationToken)
    {
        return !await _databaseRepository.IsDatabaseNameUnique(p.Name);
    }

    private async Task<bool> VerifyGuid(CreateBulkDataDataBaseListCommand p, CancellationToken cancellationToken)
    {
        var guidProperties = new (string Value, string Name)[]
        {
            (p.LicenseId, "License Id"),
            (p.BusinessServiceId, "Operational service Id"),
            (p.ServerId, "Server Id"),
            (p.DatabaseTypeId, "Database type Id")
        };

        foreach (var (value, name) in guidProperties)
            if (!await InvalidGuidOrEmpty(value))
            {
                ParameterName = name;
                return false;
            }

        return true;
    }

    public Task<bool> InvalidGuidOrEmpty([NotNull] string input)
    {
        return Task.FromResult(!string.IsNullOrWhiteSpace(input) && Guid.TryParse(input, out var guid) &&
                               guid != Guid.Empty);
    }
}

public class BulkImportCreateReplicationCommandValidator : AbstractValidator<CreateBulkDataReplicationListCommand>
{
    private readonly IReplicationRepository _replicationRepository;
    public static string ParameterName = string.Empty;
    public BulkImportCreateReplicationCommandValidator(IReplicationRepository replicationRepository)
    {
        _replicationRepository = replicationRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        //RuleFor(e => e)
        //    .MustAsync(IsValidReplicationTypeAsync)
        //    .WithMessage("Replication Type is invalid.");

        RuleFor(p => p.SiteName)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}");

        RuleFor(p => p)
           .MustAsync(IsValidGuidId)
           .WithMessage(_ => $"Input '{ParameterName ?? "guidString"}' is not valid format.");

        //RuleFor(e => e)
        //    .MustAsync(IsValidSiteAsync)
        //    .WithMessage("Site is invalid.");

        RuleFor(p => p.LicenseKey)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .When(p => p.LicenseId != "NA");

        RuleFor(p => p.Type)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull();

        RuleFor(p => p.BusinessServiceName)
            .NotEmpty().WithMessage("Select Operational Service.")
            .Matches(@"^[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]$").WithMessage("Please Enter Valid Operational Service")
            .NotNull()
            .Length(3, 100).WithMessage("Operational Service should contain between 3 to 100 characters.");

        RuleFor(p => p.Properties)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull();

        RuleFor(e => e)
            .MustAsync(ReplicationNameUnique)
            .WithMessage("A same name already exists.");
    }

    public Task<bool> InvalidGuidOrEmpty([NotNull] string input)
    {
        return Task.FromResult(!string.IsNullOrWhiteSpace(input) && Guid.TryParse(input, out var guid) &&
                               guid != Guid.Empty);
    }

    private async Task<bool> IsValidGuidId(CreateBulkDataReplicationListCommand p, CancellationToken cancellationToken)
    {
        var guidProperties = new (string Value, string Name)[]
        {
            (p.SiteId, "Site Id"),
            (p.TypeId, "Replication type Id")
        };

        foreach (var (value, name) in guidProperties)
            if (!await InvalidGuidOrEmpty(value))
            {
                ParameterName = name;
                return false;
            }

        return true;
    }

    private async Task<bool> ReplicationNameUnique(CreateBulkDataReplicationListCommand e,
        CancellationToken cancellationToken)
    {
        return !await _replicationRepository.IsReplicationNameUnique(e.Name);
    }
}

public class BulkImportCreateInfraObjectCommandValidator : AbstractValidator<CreateBulkDataInfraObjectListCommand>
{
    public static string ParameterName = string.Empty;
    private readonly List<string> _allowedPriority = new() { "high", "medium", "low" };
    private readonly IInfraObjectRepository _infraObjectRepository;

    public BulkImportCreateInfraObjectCommandValidator(IInfraObjectRepository infraObjectRepository)
    {
        _infraObjectRepository = infraObjectRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p)
            .MustAsync(IsValidGuidId)
            .WithMessage(_ => $"Input '{ParameterName ?? "guidString"}' is not valid format.");


        RuleFor(e => e)
            .MustAsync(InfraObjectNameUnique)
            .WithMessage("A same name already exists.");

        RuleFor(p => p.Description)
            .MaximumLength(250).WithMessage("InfraObject {PropertyName} Maximum 250 characters.")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("InfraObject {PropertyName} contains invalid characters.")
            .When(p => p.Description.IsNotNullOrWhiteSpace());


        RuleFor(p => p.BusinessFunctionName)
            .NotEmpty().WithMessage("Select Operational Function.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid Operational Function.")
            .Length(3, 100).WithMessage("Operational Function should contain between 3 to 100 characters.");

        RuleFor(p => p.BusinessServiceName)
            .NotEmpty().WithMessage("Select Operational Service.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid Operational Service.")
            .Length(3, 100).WithMessage("Operational Service should contain between 3 to 100 characters.");

        RuleFor(p => p.Type)
            .NotEmpty().WithMessage("Select Activity {PropertyName}.")
            .NotNull()
            .InclusiveBetween(1, 3)
            .WithMessage("Please enter number only.");

        RuleFor(p => p.Priority)
            .NotEmpty().WithMessage("Select {PropertyName}")
            .Matches(@"^[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Must(value => value != null && _allowedPriority.Any(mode =>
                string.Equals(mode.Replace(" ", "").ToLower(), value.Replace(" ", "").ToLower())))
            .WithMessage("{PropertyName} is invalid.");
    }

    private async Task<bool> InfraObjectNameUnique(CreateBulkDataInfraObjectListCommand e, CancellationToken token)
    {
        return !await _infraObjectRepository.IsInfraObjectNameUnique(e.Name);
    }

    public Task<bool> InvalidGuidOrEmpty([NotNull] string input)
    {
        return Task.FromResult(!string.IsNullOrWhiteSpace(input) && Guid.TryParse(input, out var guid) &&
                               guid != Guid.Empty);
    }


    private async Task<bool> IsValidGuidId(CreateBulkDataInfraObjectListCommand p, CancellationToken cancellationToken)
    {
        var guidProperties = new (string Value, string Name)[]
        {
            (p.ReplicationCategoryTypeId, "ReplicationCategory Id"),
            (p.BusinessServiceId, "Operational service Id"),
            (p.BusinessFunctionId, "Operational function Id"),
            (p.ReplicationTypeId, "ReplicationType type Id")
        };

        foreach (var (value, name) in guidProperties)
            if (!await InvalidGuidOrEmpty(value))
            {
                ParameterName = name;
                return false;
            }

        return true;
    }
}