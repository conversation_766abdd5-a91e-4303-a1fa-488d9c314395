using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.FiaTemplateModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.FiaTemplate.Queries.GetPaginatedList;

public class
    GetFiaTemplatePaginatedListQueryHandler : IRequestHandler<GetFiaTemplatePaginatedListQuery,
        PaginatedResult<FiaTemplateListVm>>
{
    private readonly IFiaTemplateRepository _fiaTemplateRepository;
    private readonly IMapper _mapper;

    public GetFiaTemplatePaginatedListQueryHandler(IMapper mapper, IFiaTemplateRepository fiaTemplateRepository)
    {
        _mapper = mapper;
        _fiaTemplateRepository = fiaTemplateRepository;
    }

    public async Task<PaginatedResult<FiaTemplateListVm>> Handle(GetFiaTemplatePaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new FiaTemplateFilterSpecification(request.SearchString);
        
        var queryable = await _fiaTemplateRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var fiaTemplateList = _mapper.Map<PaginatedResult<FiaTemplateListVm>>(queryable);
        //var queryable = _fiaTemplateRepository.PaginatedListAllAsync();

        //var productFilterSpec = new FiaTemplateFilterSpecification(request.SearchString);

        //var fiaTemplateList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<FiaTemplateListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return fiaTemplateList;
    }
}