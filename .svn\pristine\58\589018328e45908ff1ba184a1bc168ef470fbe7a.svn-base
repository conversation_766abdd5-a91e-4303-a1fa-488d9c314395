﻿using ContinuityPatrol.Application.Features.BusinessService.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessService.Queries;

public class GetBusinessServiceDetailQueryHandlerTests : IClassFixture<BusinessServiceFixture>
{
    private readonly BusinessServiceFixture _businessServiceFixture;
    private readonly Mock<IBusinessServiceRepository> _mockBusinessServiceRepository;
    private readonly GetBusinessServiceDetailQueryHandler _handler;

    public GetBusinessServiceDetailQueryHandlerTests(BusinessServiceFixture businessServiceFixture)
    {
        _businessServiceFixture = businessServiceFixture;

        _mockBusinessServiceRepository = BusinessServiceRepositoryMocks.GetBusinessServiceRepository(_businessServiceFixture.BusinessServices);

        _handler = new GetBusinessServiceDetailQueryHandler(_businessServiceFixture.Mapper, _mockBusinessServiceRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_BusinessServiceDetails_When_ValidId()
    {
        var result = await _handler.Handle(new GetBusinessServiceDetailQuery { Id = _businessServiceFixture.BusinessServices[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<BusinessServiceDetailVm>();
        result.Id.ShouldBe(_businessServiceFixture.BusinessServices[0].ReferenceId);
        result.Name.ShouldBe(_businessServiceFixture.BusinessServices[0].Name);
        result.Description.ShouldBe(_businessServiceFixture.BusinessServices[0].Description);
        result.Priority.ShouldBe(_businessServiceFixture.BusinessServices[0].Priority);
        result.CompanyId.ShouldBe(_businessServiceFixture.BusinessServices[0].CompanyId);
        result.CompanyName.ShouldBe(_businessServiceFixture.BusinessServices[0].CompanyName);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidBusinessServiceId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetBusinessServiceDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));
        
        exceptionDetails.Message.ShouldContain("Not Found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetBusinessServiceDetailQuery { Id = _businessServiceFixture.BusinessServices[0].ReferenceId }, CancellationToken.None);

        _mockBusinessServiceRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}