using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class BusinessServiceAvailabilityRepositoryTests : IClassFixture<BusinessServiceAvailabilityFixture>
{
    private readonly BusinessServiceAvailabilityFixture _businessServiceAvailabilityFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly BusinessServiceAvailabilityRepository _repository;

    public BusinessServiceAvailabilityRepositoryTests(BusinessServiceAvailabilityFixture businessServiceAvailabilityFixture)
    {
        _businessServiceAvailabilityFixture = businessServiceAvailabilityFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new BusinessServiceAvailabilityRepository(_dbContext);
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var businessServiceAvailability = _businessServiceAvailabilityFixture.BusinessServiceAvailabilityDto;

        // Act
        var result = await _repository.AddAsync(businessServiceAvailability);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServiceAvailability.TotalBusinessService, result.TotalBusinessService);
        Assert.Equal(businessServiceAvailability.BusinessFunctionDown, result.BusinessFunctionDown);
        Assert.Single(_dbContext.BusinessServiceAvailabilities);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var businessServiceAvailability = _businessServiceAvailabilityFixture.BusinessServiceAvailabilityDto;
        await _repository.AddAsync(businessServiceAvailability);

        businessServiceAvailability.BusinessFunctionDown = 10;
        businessServiceAvailability.BusinessFunctionUp = 12;
        businessServiceAvailability.AvailabilityUp = 1;

        // Act
        var result = await _repository.UpdateAsync(businessServiceAvailability);

        // Assert
        Assert.Equal(10, result.BusinessFunctionDown);
        Assert.Equal(12, result.BusinessFunctionUp);
        Assert.Equal(1, result.AvailabilityUp);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.NullReferenceException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var businessServiceAvailability = _businessServiceAvailabilityFixture.BusinessServiceAvailabilityDto;
        await _repository.AddAsync(businessServiceAvailability);

        // Act
        var result = await _repository.DeleteAsync(businessServiceAvailability);

        // Assert
        Assert.Equal(businessServiceAvailability.ReferenceId, result.ReferenceId);
        Assert.Empty(_dbContext.BusinessServiceAvailabilities);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var businessServiceAvailability = _businessServiceAvailabilityFixture.BusinessServiceAvailabilityDto;
        var addedEntity = await _repository.AddAsync(businessServiceAvailability);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.IncidentUp, result.IncidentUp);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var businessServiceAvailability = _businessServiceAvailabilityFixture.BusinessServiceAvailabilityDto;
        await _repository.AddAsync(businessServiceAvailability);

        // Act
        var result = await _repository.GetByReferenceIdAsync(businessServiceAvailability.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServiceAvailability.ReferenceId, result.ReferenceId);
        Assert.Equal(businessServiceAvailability.TotalAlert, result.TotalAlert);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var businessServiceAvailabilities = _businessServiceAvailabilityFixture.BusinessServiceAvailabilityList;
        await _repository.AddRange(businessServiceAvailabilities);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServiceAvailabilities.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var businessServiceAvailabilities = _businessServiceAvailabilityFixture.BusinessServiceAvailabilityList;

        // Act
        var result = await _repository.AddRange(businessServiceAvailabilities);

        // Assert
        Assert.Equal(businessServiceAvailabilities.Count, result.Count());
        Assert.Equal(businessServiceAvailabilities.Count, _dbContext.BusinessServiceAvailabilities.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRange(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var businessServiceAvailabilities = _businessServiceAvailabilityFixture.BusinessServiceAvailabilityList;
        await _repository.AddRange(businessServiceAvailabilities);

        // Act
        var result = await _repository.RemoveRange(businessServiceAvailabilities);

        // Assert
        Assert.Equal(businessServiceAvailabilities.Count, result.Count());
        Assert.Empty(_dbContext.BusinessServiceAvailabilities);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRange(null));
    }

    #endregion

    #region FindByFilter Tests

    [Fact]
    public async Task FindByFilter_ShouldReturnFilteredEntities()
    {
        // Arrange
        var businessServiceAvailabilities = _businessServiceAvailabilityFixture.BusinessServiceAvailabilityList;
        var targetReferenceId = "e7efe010-687a-4a71-85c1-7a47e29367f1";
        businessServiceAvailabilities.First().ReferenceId = targetReferenceId;
        await _repository.AddRange(businessServiceAvailabilities);

        // Act
        var result = await _repository.FindByFilter(x => x.ReferenceId == targetReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(targetReferenceId, result.First().ReferenceId);
    }

    [Fact]
    public async Task FindByFilter_ShouldReturnEmptyList_WhenNoMatch()
    {
        // Arrange
        var businessServiceAvailabilities = _businessServiceAvailabilityFixture.BusinessServiceAvailabilityList;
        await _repository.AddRange(businessServiceAvailabilities);

        // Act
        var result = await _repository.FindByFilter(x => x.ReferenceId == "NON_EXISTENT_SERVICE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var availabilitys = _businessServiceAvailabilityFixture.BusinessServiceAvailabilityList;

        var availability1 = availabilitys[0];
        var availability2 = availabilitys[1];
        // Act
        var task1 = _repository.AddAsync(availability1);
        var task2 = _repository.AddAsync(availability2);

        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.BusinessServiceAvailabilities.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var availabilities = _businessServiceAvailabilityFixture.BusinessServiceAvailabilityList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRange(availabilities);
        var initialCount = availabilities.Count;

        var toUpdate = availabilities.Take(2).ToList();
        toUpdate.ForEach(x => x.AvailabilityUp = 45);
        await _repository.UpdateRange(toUpdate);

        var toDelete = availabilities.Skip(2).Take(1).ToList();
        await _repository.RemoveRange(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.AvailabilityUp == 45).ToList();
        Assert.Equal(2, updated.Count);
    }

   
    #endregion
}
