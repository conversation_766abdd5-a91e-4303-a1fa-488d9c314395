using ContinuityPatrol.Application.Features.CyberJobWorkflowScheduler.Commands.UpdateConditionActionId;
using ContinuityPatrol.Application.Features.CyberJobWorkflowScheduler.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberJobWorkflowSchedulerModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class CyberJobWorkflowSchedulerFixture
{
    public CyberJobWorkflowSchedulerCommand CyberJobWorkflowSchedulerCommand { get; }
    public GetCyberJobWorkflowSchedulerPaginatedListQuery GetCyberJobWorkflowSchedulerPaginatedListQuery { get; }
    public CyberJobWorkflowSchedulerListVm CyberJobWorkflowSchedulerListVm { get; }
    public CyberJobWorkflowSchedulerViewModel CyberJobWorkflowSchedulerViewModel { get; }

    public CyberJobWorkflowSchedulerFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CyberJobWorkflowSchedulerCommand>(c => c
            .With(b => b.JobId, Guid.NewGuid().ToString())
            .With(b => b.ConditionActionId, 1));

        fixture.Customize<GetCyberJobWorkflowSchedulerPaginatedListQuery>(c => c
            .With(b => b.PageNumber, 1)
            .With(b => b.PageSize, 10)
            .With(b => b.StartDate, DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd"))
            .With(b => b.EndDate, DateTime.Now.ToString("yyyy-MM-dd")));

        fixture.Customize<CyberJobWorkflowSchedulerListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.JobId, Guid.NewGuid().ToString())
            .With(b => b.Name, () => $"WorkflowScheduler-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.WorkflowId, Guid.NewGuid().ToString())
            .With(b => b.WorkflowName, () => $"Workflow-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.CurrentActionId, Guid.NewGuid().ToString())
            .With(b => b.CurrentActionName, () => $"Action-{fixture.Create<string>().Substring(0, 10)}")
            .With(b => b.IsSchedule, () => fixture.Create<int>() % 2)
            .With(b => b.ScheduleType, () => fixture.Create<int>() % 3 + 1)
            .With(b => b.ScheduleTime, () => $"{fixture.Create<int>() % 24:D2}:{fixture.Create<int>() % 60:D2}:00")
            .With(b => b.CronExpression, () => $"0 {fixture.Create<int>() % 60} {fixture.Create<int>() % 24} * * ?")
            .With(b => b.StartTime, () => DateTime.Now.AddHours(-(fixture.Create<int>() % 24)))
            .With(b => b.EndTime, () => DateTime.Now.AddHours(fixture.Create<int>() % 24))
            .With(b => b.Status, () => fixture.Create<bool>() ? "Active" : "Inactive")
            .With(b => b.ConditionActionId, () => fixture.Create<int>() % 10 + 1)
            .With(b => b.State, () => fixture.Create<bool>() ? "Running" : "Scheduled")
            .With(b => b.Mode, () => fixture.Create<bool>() ? "Automatic" : "Manual")
            .With(b => b.NodeId, Guid.NewGuid().ToString()));

        fixture.Customize<CyberJobWorkflowSchedulerViewModel>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.JobId, Guid.NewGuid().ToString())
            .With(b => b.Name, "Enterprise Critical Workflow Scheduler")
            .With(b => b.WorkflowId, Guid.NewGuid().ToString())
            .With(b => b.WorkflowName, "Mission-Critical Data Protection Workflow")
            .With(b => b.CurrentActionId, Guid.NewGuid().ToString())
            .With(b => b.CurrentActionName, "Database Backup and Verification Action")
            .With(b => b.IsSchedule, 1)
            .With(b => b.ScheduleType, 2)
            .With(b => b.ScheduleTime, "02:00:00")
            .With(b => b.CronExpression, "0 0 2 * * ? *")
            .With(b => b.StartTime, DateTime.Now.AddHours(-1))
            .With(b => b.EndTime, DateTime.Now.AddHours(23))
            .With(b => b.Status, "Active")
            .With(b => b.ConditionActionId, 1)
            .With(b => b.State, "Scheduled")
            .With(b => b.Mode, "Automatic")
            .With(b => b.NodeId, Guid.NewGuid().ToString()));

        CyberJobWorkflowSchedulerCommand = fixture.Create<CyberJobWorkflowSchedulerCommand>();
        GetCyberJobWorkflowSchedulerPaginatedListQuery = fixture.Create<GetCyberJobWorkflowSchedulerPaginatedListQuery>();
        CyberJobWorkflowSchedulerListVm = fixture.Create<CyberJobWorkflowSchedulerListVm>();
        CyberJobWorkflowSchedulerViewModel = fixture.Create<CyberJobWorkflowSchedulerViewModel>();
    }
}
