using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class CyberJobManagementLogsRepositoryTests : IClassFixture<CyberJobManagementLogsFixture>
{
    private readonly CyberJobManagementLogsFixture _cyberJobManagementLogsFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CyberJobManagementLogsRepository _repository;

    public CyberJobManagementLogsRepositoryTests(CyberJobManagementLogsFixture cyberJobManagementLogsFixture)
    {
        _cyberJobManagementLogsFixture = cyberJobManagementLogsFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CyberJobManagementLogsRepository(_dbContext);
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var jobLog = _cyberJobManagementLogsFixture.CyberJobManagementLogsDto;

        // Act
        await _dbContext.CyberJobManagementLogs.AddAsync(jobLog);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.GetByReferenceIdAsync(jobLog.ReferenceId);
       

        // Assert
        Assert.NotNull(result);
        Assert.Equal(jobLog.JobId, result.JobId);
        Assert.Equal(jobLog.WorkflowId, result.WorkflowId);
        Assert.Equal(jobLog.Status, result.Status);

        Assert.Single(_dbContext.CyberJobManagementLogs);
    }

    #endregion

   
    #region Infrastructure Assignment Tests

    [Fact]
    public async Task Repository_ShouldHandleJobAndWorkflowAssignments()
    {
        // Arrange
        var jobLogs = _cyberJobManagementLogsFixture.CyberJobManagementLogsList;
        jobLogs[0].JobId = "3ea5aeba-505b-49a0-bd05-87765044bc33";
        jobLogs[0].WorkflowId = "3ea5aeba-505b-49a0-bd05-87765044bcw1";
        jobLogs[0].Status = "Error";
        jobLogs[0].State = "Active";
        await _repository.AddRangeAsync(jobLogs);

        // Act
        var jobs = await _repository.FindByFilterAsync(x => x.JobId.Contains("3ea5aeba-505b-49a0-bd05-87765044bc33"));
        var workflows = await _repository.FindByFilterAsync(x => x.WorkflowId.Contains("3ea5aeba-505b-49a0-bd05-87765044bcw1"));
   

        // Assert
        Assert.Single(jobs);
        Assert.Single(workflows);
        Assert.Contains("3ea5aeba-505b-49a0-bd05-87765044bc33", jobs.First().JobId);
        Assert.Contains("3ea5aeba-505b-49a0-bd05-87765044bcw1", workflows.First().WorkflowId);
        Assert.Contains("Error", jobs.First().Status);
        Assert.Equal("Error", workflows.First().Status);

        Assert.Equal("Active", workflows.First().State);
       
    }

    #endregion
}
