﻿namespace ContinuityPatrol.Application.Features.NodeWorkflowExecution.Commands.Update;

public class UpdateNodeWorkflowExecutionCommand : IRequest<UpdateNodeWorkflowExecutionResponse>
{
    public string Id { get; set; }
    public string NodeId { get; set; }
    public string NodeName { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string ProfileId { get; set; }
    public string ProfileName { get; set; }
    public string JobId { get; set; }
    public string JobName { get; set; }
    public string InfraObjectSchedulerId { get; set; }
    public string InfraObjectSchedulerName { get; set; }
    public string WorkflowOperationId { get; set; }
    public string Status { get; set; }
    public string Type { get; set; }

    public override string ToString()
    {
        return $"Workflow Name: {WorkflowName}; Id:{Id};";
    }
}