using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowActionFixture : IDisposable
{
    public List<WorkflowAction> WorkflowActionPaginationList { get; set; }
    public List<WorkflowAction> WorkflowActionList { get; set; }
    public WorkflowAction WorkflowActionDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowActionFixture()
    {
        var fixture = new Fixture();

        WorkflowActionList = fixture.Create<List<WorkflowAction>>();

        WorkflowActionPaginationList = fixture.CreateMany<WorkflowAction>(20).ToList();

        WorkflowActionPaginationList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowActionList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowActionDto = fixture.Create<WorkflowAction>();

        WorkflowActionDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
