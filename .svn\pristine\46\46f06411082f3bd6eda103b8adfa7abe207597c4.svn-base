const JobManagementExistUrl = 'CyberResiliency/JobManagement/IsJobManagementExist';
const CheckWindowServiceurl= 'ITAutomation/WorkflowExecution/CheckWindowsService';



$(function () {
    GetWorkFlowList()
   // GetReplicationList()
    GetairgabList()
    let createPermission = $("#CyberCreate").data("create-permission").toLowerCase();
    let deletePermission = $("#CyberDelete").data("delete-permission").toLowerCase();

    if (createPermission == 'false') {
        $("#create").removeClass('#create').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }
    let selectedValues = [];

    let dataTable = $('#tblJobManage').DataTable(

        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }
                , infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/CyberResiliency/JobManagement/GetPaginated",
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 2 ? "name" : sortIndex === 3 ? "airgapName" : sortIndex === 4 ? "workflowName" :
                         sortIndex === 5 ? "status" : "";
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                    d.pageSize = d?.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")


                    }
                    else {
                        $(".pagination-column").removeClass("disabled")

                    }
                    return json?.data;
                }
            },
            "columnDefs": [
                {
                    "targets": [1, 2, 3, 4,5],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false
                },
                {
                    "data": null, "name": "CheckboxAll", "autoWidth": true, "orderable": false,
                    "render": function (data, type, full, meta) {

                        return '<input type="checkbox" name="rowCheckbox" statename="' + data.state + '" class="' + data.state + ' form-check-input custom-cursor-default-hover" title=' + data.id + ' id="' + data.state + '">';

                    }
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                
                {
                    "data": "airgapName", "name": "Airgap Name", "autoWidth": true,
                    "render": function (data, type, row) {
                      
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "workflowName", "name": "Workflow Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                //{
                //    "data": "solutionName", "name": "Solution Type", "autoWidth": true,
                //    "render": function (data, type, row) {
                //        if (type === 'display') {
                //            return '<span>' + data + '</span>';
                //        }
                //        return data;
                //    }
                //},
                {
                    "data": "scheduleTime", "name": "Scheduled Time", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "lastExecutedTime",
                    "name": "Last Executed Time",
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            // Check if data is null or empty, and return "NA" in that case
                            if (data === null || data === '') {
                                return '<span>NA</span>';
                            }
                            return '<span>' + data + '</span>';
                        }
                        // For other types (like 'filter' or 'sort'), return the original data
                        return data;
                    }
                },
                {
                    "data": "status", "name": "Status", "autoWidth": true,
                    "render": function (data, type, row) {
                        var iconClass = '';
                        if (data == "Pending") {
                            iconClass = "cp-pending text-warning me-1";
                        } else if (data == "Running") {
                            iconClass = "text-primary cp-reload cp-animate me-1";
                        } else if (data == "Success") {
                            iconClass = "cp-success text-success me-1";
                        } else {
                            iconClass = "cp-error text-danger me-1";
                        }

                        return `<td><i class="${iconClass}"></i></td>
                              <td><span title="${data == null ? "NA" : data}"> ${data == null ? "NA" : data}</span></td>`;
                    }

                },
                {
                    "data": "state", "name": "State", "autoWidth": true,
                    "render": function (data, type, row) {

                        var iconClass = '';
                        if (data == "Active") {
                            iconClass = "cp-active-inactive text-success me-1";
                        } else if (data === 'InActive') {
                            iconClass = "cp-active-inactive text-danger me-1";
                        } else if (data === null) {
                            iconClass = "cp-active-inactive text-danger me-1";
                        }


                        return `<td><i class="${iconClass}" id="icon" title="${data}" ></i></td>
                              <td><span id="jobmanagestate"> ${data == null ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "render": function (data, type, row) {
                        if (createPermission == "true" && deletePermission == "true") {
                            return `
                         <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="edit-button ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}"  data-job='${btoa(JSON.stringify(row))}'>
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete" class="delete-button ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}" data-job-id="${row.id}" data-job-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span  title="Reset" id="reset" class="reset-button" data-job='${JSON.stringify(row)}'' role="button" data-bs-toggle="modal"
                                                       >
                                                        <i class="cp-job-reset"></i>                                    
                                                 </span>                                                                                                                                                   
                                </div>`;
                        }
                        if (createPermission == "false" && deletePermission == "true") {
                            return `
                         <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete" class="delete-button ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}" data-job-id="${row.id}" data-job-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span  title="Reset" id="reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       >
                                                        <i class="cp-job-reset"></i>                                    
                                                 </span>                                                                                                                                                   
                                </div>`;
                        }
                        if (createPermission == "true" && deletePermission == "false") {
                            return `
                         <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="edit-button ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}"  data-job='${btoa(JSON.stringify(row))}'>
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span  title="Reset" id="reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       >
                                                        <i class="cp-job-reset"></i>                                    
                                                 </span>                                                                                                                                                   
                                </div>`;
                        }
                        if (createPermission == "false" && deletePermission == "false") {
                            return `
                         <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span  title="Reset" id="reset" class="icon-disabled">
                                                        <i class="cp-job-reset"></i>                                    
                                                 </span>                                                                                                                                                   
                                </div>`;
                        }
                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#search-inp').on('keydown input', commonDebounce(function (e) {
       
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const NameCheckbox = $("#Name");
        const solutionCheckbox = $("#airgapname");
        const workflowCheckbox1 = $("#workflowname");
       // const solutionCheckbox1 = $("#solutiontype");
   
        const inputValue = $('#search-inp').val();
        if (NameCheckbox.is(':checked')) {
            selectedValues.push(NameCheckbox.val() + inputValue);
        }
        if (solutionCheckbox.is(':checked')) {
            selectedValues.push(solutionCheckbox.val() + inputValue);
        }
        if (workflowCheckbox1.is(':checked')) {
            selectedValues.push(workflowCheckbox1.val() + inputValue);
        }
        //if (solutionCheckbox1.is(':checked')) {
        //    selectedValues.push(solutionCheckbox1.val() + inputValue);
        //}
        
        dataTable.ajax.reload(function (json) {
            
            if (json.recordsFiltered === 0) {
                
                $('.dataTables_empty').text('No matching records found');
            }
        })
    }, 500))

    $(".form-select-modal").select2({
        tags: true,
        dropdownParent: $("#CreateModal"),
    });
  
    $(async function () {
       
        await CyberJobService()
    })

    const CyberJobService = async () => {
        await $.ajax({
            type: "POST",
            url: RootUrl + CheckWindowServiceurl,
            data: { type: 'ResiliencyReadyService', __RequestVerificationToken: gettoken() },
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result.success) {
                    if (result && result.success) {
                        let html = CyberJobMessage(result)
                        notificationAlert("success", html, 'execution')
                    } else {
                        notificationAlert("warning", response.message);
                    }

                } else {
                    errorNotification(result)
                }
            }
        })
    }

    const CyberJobMessage = (result) => {
        let html = ''
        if (result?.activeNodes && Array.isArray(result?.activeNodes) && result?.activeNodes?.length) {
            for (let i = 0; i < result?.activeNodes?.length; i++) {
                html += `<div class='mb-1'><i class="cp-network fs-5 text-success" ></i> '${result.activeNodes[i]}'</div>`;
            }
        }
        if (result?.inActiveNodes && Array.isArray(result?.activeNodes) && result?.inActiveNodes?.length) {
            for (let i = 0; i < result?.inActiveNodes?.length; i++) {
                html += `<div class='mb-1'><i class="cp-network fs-5 text-danger" ></i> '${result.inActiveNodes[i]}'</div>`;
            }
        }
        return html;
    }

  

    //Create   
    $("#ddlCompanyName").on('change', function () {
        let companyId = $("#ddlCompanyName option:selected").attr('id');
        $('#textCompanyId').val(companyId);

    });
    $('#tblJobManage').on('click', '#reset', function () {
        var jobData = $(this).data('job');
        jobData.__RequestVerificationToken = gettoken()
        $.ajax({
            url: "/CyberResiliency/JobManagement/ResetCyberJob",
            type: 'POST',
            data: jobData,
            success: function (result) {
                if (result.success) {
                    notificationAlert("success", result?.data.message);
                    setTimeout(() => {
                        dataTable.ajax.reload();
                    }, 2000)
                } else {
                    // errorNotification(result)
                }
            }
        });
    });


    $('#Activebtn').on('click', function () {
        let checkFind = document.querySelectorAll('input[name="rowCheckbox"]')
        let datas = []
        checkFind.forEach((obj, idx) => {
            if (obj.checked && obj.id != "Active") {
                datas.push({
                    "id": obj.title,
                    "state": "Active"
                })
            }
        })
        if (datas.length) {
            $.ajax({
                url: "/CyberResiliency/JobManagement/UpdateJobState",
                type: 'PUT',
                data: {
                    "updateCyberJobStates": datas,
                    __RequestVerificationToken: gettoken()
                },
                success: function (result) {
                    if (result.success) {
                        var data = result?.data
                        $('input[name="rowCheckbox"]').prop("checked", false)
                        $('input[name="checkboxAll"]').prop("checked", false)
                        notificationAlert("success", data.message)
                        setTimeout(() => {
                            location.reload();
                        }, 2000)
                    } else {
                        errorNotification(result)
                    }
                },
            });
        } else {
            if ($('input[name="rowCheckbox"]').prop("checked")) {
                notificationAlert("warning", "Jobs state has already updated to 'Active' state ")
                setTimeout(() => {
                    location.reload();
                }, 2000)
            }
        }
    })
    $('#Inactivebtn').on('click', function () {
        let checkFind = document.querySelectorAll('input[name="rowCheckbox"]')
        let datas = []
        checkFind.forEach((obj, idx) => {
            if (obj.checked && obj.id != "InActive") {
                datas.push({
                    "id": obj.title,
                    "state": "InActive"
                })
            }
        })
        if (datas.length) {
            $.ajax({
                url: "/CyberResiliency/JobManagement/UpdateJobState",
                type: 'PUT',
                data: {
                    "updateCyberJobStates": datas,
                    __RequestVerificationToken: gettoken()
                },
                success: function (result) {
                    if (result.success) {
                        var data = result?.data
                        $('input[name="rowCheckbox"]').prop("checked", false)
                        $('input[name="checkboxAll"]').prop("checked", false)
                        notificationAlert("success", data.message)
                        setTimeout(() => {
                            location.reload();
                        }, 2000)
                    } else {
                        errorNotification(result)
                    }
                },
            });
        } else {
            if ($('input[name="rowCheckbox"]').prop("checked")) {
                notificationAlert("warning", "Jobs state has already updated to 'InActive' state ")
                setTimeout(() => {
                    location.reload();
                }, 2000)
            }
        }
    })
    $("#flexCheckDefault").on('change', function (e) {
        setTimeout(() => {
            if (e.target.checked) {
                $('input[name="rowCheckbox"]').prop("checked", true);

            } else {
                $('input[name="rowCheckbox"]').prop("checked", false)
            }
        }, 100)
    })
    $("#tblJobManagement").on('change', 'input[name="rowCheckbox"]', function (e) {
        $('input[name="checkboxAll"]').prop("checked", false)
    })
    async function GetWorkFlowList() {

        //$("#UserName_Recipient").empty()
        await $.ajax({
            type: "GET",
            url: RootUrl + "CyberResiliency/JobManagement/GetWorkflowNames",
            data: {},
            dataType: "json",
            traditional: true,
            success: function (result) {
                
                if (result.success) {

                    if (result?.data && result?.data.length > 0) {

                        $('#workflowName').append('<option value=""></option>');
                        result?.data.forEach(item => {
                            $('#workflowName').append('<option id="' + item.id + '" value="' + item.name + '">' + item.name + '</option>');
                        });
                    }

                } else {

                    errorNotification(result);
                }
            }
        })

        //if (Object.keys(ReplicationMap)) {
        //    $('#recipient').val(ReplicationMap.databaseName).trigger('change');
        //}
    }

    async function GetairgabList() {

        //$("#UserName_Recipient").empty()
        await $.ajax({
            type: "GET",
            url: RootUrl + "CyberResiliency/JobManagement/GetAirgapList",
            data: {},
            dataType: "json",
            traditional: true,
            success: function (result) {

                if (result.success) {

                    if (result?.data && result?.data.length > 0) {

                        $('#airgapName').append('<option value=""></option>');
                        result?.data.forEach(item => {
                            $('#airgapName').append('<option id="' + item.id + '" value="' + item.name + '">' + item.name + '</option>');
                        });
                    }

                } else {

                    errorNotification(result);
                }
            }
        })

        //if (Object.keys(ReplicationMap)) {
        //    $('#recipient').val(ReplicationMap.databaseName).trigger('change');
        //}
    }

    //async function GetReplicationList() {

    //    //$("#UserName_Recipient").empty()
    //    await $.ajax({
    //        type: "GET",
    //        url: RootUrl + "CyberResiliency/JobManagement/GetReplicationNames",
    //        data: {},
    //        dataType: "json",
    //        traditional: true,
    //        success: function (result) {
                
    //            if (result.success) {

    //                if (result.data && result.data.length > 0) {

    //                    $('#solutionName').append('<option value=""></option>');
    //                    result.data.forEach(item => {
    //                        $('#solutionName').append('<option id="' + item.id + '" value="' + item.name + '">' + item.name + '</option>');
    //                    });
    //                }

    //            } else {

    //                errorNotification(result);
    //            }
    //        }
    //    })

    //    //if (Object.keys(ReplicationMap)) {
    //    //    $('#recipient').val(ReplicationMap.databaseName).trigger('change');
    //    //}
    //}

    //Delete
    $('#tblJobManage').on('click', '.delete-button', function () {
        
        let jobId = $(this).data('job-id');
        let jobName = $(this).data('job-name');
        $("#deleteData").attr("title", jobName);
        $('#deleteData').text(jobName);
        $('#textDeleteId').val(jobId);
    });

    // Edit
    $('#tblJobManage').on('click', '.edit-button', function () {
        let jobData = $(this).data('job');
        let CyberjobDatas = atob(jobData);
        let CyberDatas = JSON.parse(CyberjobDatas)   
        Tab_selection(CyberDatas);
        Tab_schedule_type(CyberDatas); 
        populateModalFields(jobData);
        $('#SaveFunction').text('Update');
        $('#CreateModal').modal('show');
    });

    //$('input[name="mode"]').on('change', function () {
    //    const selectedMode = $('input[name="mode"]:checked').val();
    //    $("#ModeId").val(selectedMode)

    //});

    $('#textStateActive').on('click', function () {
        const errorElement = $('#state-error');
        ValidateRadioButton(errorElement);
    });
    $('#textStateInactive').on('click', function () {
        const errorElement = $('#state-error');
        ValidateRadioButton(errorElement);
    });

    //Name Validation
    $('#jobName').on('keyup', commonDebounce(async function () {
        
        let siteId = $('#textSiteId').val();
        const value = $(this).val();      
        let name = await sanitizeInput($(this).val());
        $(this).val(name);
        await validateNames(value, siteId, JobManagementExistUrl);
    }, 400));


    $('#workflowName').on('change', function () {
        const value = $(this).val();
        let id = $(this).children(":selected").attr("id");
        $('#workflowId').val(id);
        validateDropDown(value, 'Select workflow name', 'WorkflowName-error');
    });

    $('#airgapName').on('change',function () {
        const value = $(this).val();
        let id = $(this).children(":selected").attr("id");
        $('#airgapId').val(id);
        validateDropDown(value, 'Enter airgap name', 'AirgapName-error');
    });

    document.getElementById('textName')?.addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            e.preventDefault();
        }
    });

    $('#txtMins').on('input keypress', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
            event.preventDefault();
            $('#txtMins').val('');
        }
        const value = $(this).val();
        if (value == 0 || value > 59) {
            $('#txtMins').val("")
        }
        if (value.length >= 2) {
            event.preventDefault()
        }
        errorElement = $('#CronMin-error');
        validateMinJobNumber(value, "Enter minutes", errorElement);
    });

    $('#txtHours').on('input keypress', function (event) {
        const value = $(this).val();
        if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key) || value > 23) {
            event.preventDefault();
            $('#txtHours').val('');
        }
        if (value == 0) {
            $('#txtHours').val("")
        }

        if (value.length >= 2) {
            event.preventDefault()
        }
        errorElement = $('#CronHourly-error');
        validateHourJobNumber(value, "Enter hours", errorElement);
    });

    $('#txtMinutes').on('input keypress', function (event) {
        const value = $(this).val();
        if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key) || value > 59) {
            event.preventDefault();
            $('#txtMinutes').val('');
        }
        if (value.length >= 2) {
            event.preventDefault()
        }
        errorElement = $('#CronHourMin-error');
        validateMiniteJobNumber(value, "Enter minutes", errorElement);
    });

    $("#txtMinutes,#txtHours").on("input", function () {

        let txtMinutes = $("#txtMinutes").val()
        let txtHours = $("#txtHours").val()
        if (txtMinutes == "00" && txtHours == "00" || txtMinutes == "0" && txtHours == "0") {
            $("#txtMinutes").val("")
            setTimeout(() => {
                $('#CronHourMin-error').text("Enter the proper hours and minites")
            }, 200)

        }
    })
    $('#everyHours').on('input', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
            event.preventDefault();
            $('#everyHours').val('');
        }
        const value = $(this).val();
        errorElement = $('#CroneveryHour-error');
        validateHourJobNumber(value, "Select hours", errorElement);
    });

    $('#everyMinutes').on('click', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69) {
            event.preventDefault();
            $('#everyMinutes').val('');
        }
        const value = $(this).val();
        errorElement = $('#CroneveryMin-error');
        validateMinJobNumber(value, "Select minutes", errorElement);
    });
    $('#ddlHours').on('input', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
            //event.preventDefault();
            $('#ddlHours').val('');
        }
        const value = $(this).val();
        errorElement = $('#CronddlHour-error');
        validateHourJobNumber(value, "Select hours", errorElement);
    });

    $('#ddlMinutes').on('click', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69) {
            event.preventDefault();
            $('#ddlMinutes').val('');
        }
        const value = $(this).val();
        errorElement = $('#CronddlMin-error');
        validateMinJobNumber(value, "Select hours", errorElement);
    });

    $('#MonthlyHours').on('input', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
            event.preventDefault();
            $('#MonthlyHours').val('');
        }
        const value = $(this).val()
        errorElement = $('#MonthlyHours-error');
        validateHourJobNumber(value, "Select hours", errorElement);
    });

    $('.datetimeCron').on('change', function () {
        const value = $(this).val();
        errorElement = $('#CronExpression-error');
        validateDayNumber(value, "Select schedule time", errorElement);
        let selectdate = new Date(value)

        let currentdate = new Date(srvTime())


        if (selectdate > currentdate) {
            $('#CronExpression-error').text('');
            $('#CronExpression-error').removeClass('field-validation-error');
            return true;
        } else if (selectdate < currentdate) {
            $('#CronExpression-error').text("Select schedule date and time should be greater than current date and time");
            $('#CronExpression-error').addClass('field-validation-error');
            return false;
        }
    });


    //$('#lblMonth').on('change', function () {
    //    const value = $(this).val();
    //    errorElement = $('#CronMonth-error');
    //    validateDayNumber(value, "Select month and year", errorElement);
    //    if (value) {
    //        $('input[name=Days]').prop("checked", false);
    //        const [year, month] = value.split("-");
    //        const lastDayOfMonth = new Date(year, month, 0).getDate();
    //        const today = new Date();
    //        let currentdate = today.getDate();
    //        let currentMonth = today.getFullYear() + "-" + (today.getMonth() + 1);
    //        for (let i = 1; i <= 31; i++) {
    //            const checkboxElement = document.getElementById(`inlineCheckbox${i}`);

    //            currentMonth == value && currentdate > i ? checkboxElement.disabled = true : checkboxElement.disabled = !(i <= lastDayOfMonth);
    //        }
    //    }
    //});
    //$('#lblMonth').on('keypress', function (e) {
    //    // Prevent any keypresses (including numbers and characters)
    //    e.preventDefault();
    //});

    //$('#lblMonth').on("change", function () {
    //    debugger
    //    $('input[name="Monthyday"]').prop("checked", false)
    //    const value = $(this).val();
    //    errorElement = $('#CronMonthly-error');
    //    validateDayNumber(value, "Select month and year", errorElement);

    //    var selectedDate = new Date($(this).val());
    //    var currentDate = new Date();
    //    const getDays = (year, month) => {
    //        return new Date(year, month, 0).getDate();
    //    };
    //    const daysInmonth = getDays(selectedDate.getFullYear(), selectedDate.getMonth() + 1)
    //    for (let i = 0; i < daysInmonth; i++) {
    //        var data = ""
    //        data = i + 1
    //        $('input[name="Monthyday"]').each(function () {
    //            var checkboxValue = parseInt($(this).val());
    //            if (checkboxValue > data) {
    //                $(this).css("display", "none")
    //            } else {
    //                $(this).css("display", "block")
    //            }
    //        })
    //        $(".checklabel").each(function () {
    //            var checkboxValue = parseInt($(this).text());
    //            if (checkboxValue > data) {
    //                $(this).css("display", "none")
    //            } else {
    //                $(this).css("display", "block")
    //            }
    //        })
    //    }
    //    if ($(this).val() === "") {
    //        $('input[name="Monthyday"]').prop("checked", false)
    //    }

    //    $('input[name="Monthyday"]').each(function () {
    //        var checkboxValue = parseInt($(this).val());

    //        if ((selectedDate.getMonth() === currentDate.getMonth() && selectedDate.getFullYear() === currentDate.getFullYear() && checkboxValue < currentDate.getDate()) ||
    //            (selectedDate.getMonth() < currentDate.getMonth() && selectedDate.getFullYear() <= currentDate.getFullYear())) {
    //            $(this).prop('disabled', true);
    //        } else {
    //            $(this).prop('disabled', false);
    //        }
    //    })
    //});

    $('#lblMonth').on("change", function () {
        $('input[name="Monthyday"]').prop("checked", false)
        validateDayNumber($(this).val(), "Select month and year", $('#CronMonthly-error'));
        var selectedDate = new Date($(this).val());
        var currentDate = new Date();
        const getDays = (year, month) => {
            return new Date(year, month, 0).getDate();
        };
        const daysInmonth = getDays(selectedDate.getFullYear(), selectedDate.getMonth() + 1)
        for (let i = 0; i < daysInmonth; i++) {
            var data = ""
            data = i + 1
            $('input[name="Monthyday"]').each(function () {
                var checkboxValue = parseInt($(this).val());
                if (checkboxValue > data) {
                    $(this).css("display", "none")
                } else {
                    $(this).css("display", "block")
                }
            })
            $(".checklabel").each(function () {
                var checkboxValue = parseInt($(this).text());
                if (checkboxValue > data) {
                    $(this).css("display", "none")
                } else {
                    $(this).css("display", "block")
                }
            })
        }
        if ($(this).val() == "") {
            $('input[name="Monthyday"]').prop('disabled', true);
            $('input[name="Monthyday"]').prop('checked', false);
            $("#CronMon-error").text("").removeClass("field-validation-error")
        } else {
            $('input[name="Monthyday"]').each(function () {
                var checkboxValue = parseInt($(this).val());
                if ((selectedDate.getMonth() === currentDate.getMonth() && selectedDate.getFullYear() === currentDate.getFullYear() && checkboxValue < currentDate.getDate()) ||
                    (selectedDate.getMonth() < currentDate.getMonth() && selectedDate.getFullYear() <= currentDate.getFullYear())) {
                    $(this).prop('disabled', true);
                } else {
                    $(this).prop('disabled', false);

                }
            })
        }
    });
    $('input[name=weekDays]').on('click', function (event) {
        var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
        var Dayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
        errorElement = $('#CronDay-error');
        validateDayNumber(Dayvalue, "Select day", errorElement);
    });

    $('input[name=Monthyday]').on('click', function () {
        var checkedCheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
        var MonthDayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
        validateDayNumber(MonthDayvalue, "Select date(s)", $('#CronMon-error'));
    });

    $('.nav-link').on("click", function () {
        ClearCroneElements();
    });
    $('input[name = "switchPlan"]').on('click', function () {
        ClearCroneElements();
    });
    $('input[name=daysevery]').on('click', function () {
        errorElement = $('#Crondaysevery-error');
        ValidateCronRadioButton(errorElement);
    });
    $("#nav-Monthly-tab").on("click", function () {
        if ($("#SaveFunction").text() == "Save") {
            $('input[name=Monthyday]').attr('disabled', 'disabled');
        }
    })

    function ClearCroneElements() {
        $('#txtMins').val('');
        $('#txtHours').val('');
        $('#txtMinutes').val('');
        $('#ddlHours').val('');
        $('#everyHours').val('');
        $('#lblMonth').val('');     
        $('#MonthlyHours').val('');
        $('input[name=weekDays]').prop("checked", false);
        $('input[name=daysevery]').prop("checked", false);
        $('input[name=Monthyday]').prop("checked", false);
        $('input[name="Monthyday"]').prop('disabled', false);
        //$("#textStateActive").prop("checked", false)
        $("#textStateInactive").prop("checked", false)
        $('#datetimeCron').val('');
    }
    //function JobCronExpression() {
    //    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    //    var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    //    var Monthlycheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    //    var txtmonthday = Array.from(Monthlycheckboxes).map(checkbox => checkbox.value);
    //    var monthlymonth = $('#lblMonth').val();
    //    var CronExpression = "";
    //    var Minutes = $('#txtMins').val();
    //    var txtHours = $('#txtHours').val() == "00" ? "0" : $('#txtHours').val() == "01" ? "1" : $('#txtHours').val() == "03" ? "3" : $('#txtHours').val() == "04" ? "4" : $('#txtHours').val() == "05" ? "5" : $('#txtHours').val() == "06" ? "6" : $('#txtHours').val() == "07" ? "7" : $('#txtHours').val() == "08" ? "8" : $('#txtHours').val() == "09" ? "9" : $('#txtHours').val()
    //    var txtHourMinutes = $('#txtMinutes').val() == "00" ? "0" : $('#txtMinutes').val() == "01" ? "1" : $('#txtMinutes').val() == "03" ? "3" : $('#txtMinutes').val() == "04" ? "4" : $('#txtMinutes').val() == "05" ? "5" : $('#txtMinutes').val() == "06" ? "6" : $('#txtMinutes').val() == "07" ? "7" : $('#txtMinutes').val() == "08" ? "8" : $('#txtMinutes').val() == "09" ? "9" : $('#txtMinutes').val()

    //    var day = $('#ddlHours').val().split(":")
    //    var ddlHours = day[0]
    //    var ddlMinutes = day[1] 
    //    var Daily = $('#everyHours').val().split(":")
    //    var everyHours = Daily[0]
    //    var everyMinutes = Daily[1]
    //    var month = $('#MonthlyHours').val().split(":")
    //    var MonthlyHours = month[0]
    //    var MonthlyMins = month[1] 
    //    var weekDay = $('#defaultCheck-MON-FRI').val();
    //    var datetime = $('#datetimeCron').val()


    //    var schedule_model = document.querySelector('input[name="daysevery"]:checked');

    //    var listcron = '';

    //    if (datetime != '') {

    //        var { CronExpression, listCron } = DateTimeCronBuilder(datetime)
    //        CronExpression = CronExpression
    //        listcron = listCron;
    //    }

    //    else {
    //        if (Minutes != '') {
    //            CronExpression = "0" + " 0/" + Minutes + " * * * ?";
    //            listcron = "Every " + Minutes + " minutes"
    //        }

    //        else if (txtHours != '') {

    //            CronExpression = "0 " + txtHourMinutes + " 0/" + txtHours + " * * ?"
    //            listcron = "Every " + txtHours + " hours " + txtHourMinutes + " minutes";
    //        }
    //        else if (txtDay != '') {

    //            CronExpression = "0 " + ddlMinutes + " " + ddlHours + " ? * " + txtDay + " *"
    //            listcron = txtDay + " at " + ddlHours + " hours " + ddlMinutes + " minutes";
    //        }         
    //        else if (txtmonthday != '') {
    //            if (monthlymonth != '') {
    //                monthlymonth = monthlymonth.split('-');
    //                var txtmonth = monthlymonth[1] == "01" ? "JAN" : monthlymonth[1] == "02" ? "FEB" : monthlymonth[1] == "03" ? "MAR" : monthlymonth[1] == "04" ? "APR" :
    //                    monthlymonth[1] == "05" ? "MAY" : monthlymonth[1] == "06" ? "JUN" : monthlymonth[1] == "07" ? "JUL" : monthlymonth[1] == "08" ? "AUG" : monthlymonth[1] == "09" ? "SEP" :
    //                        monthlymonth[1] == "10" ? "OCT" : monthlymonth[1] == "11" ? "NOV" : monthlymonth[1] == "12" ? "DEC" : ""
    //                var txtyear = monthlymonth[0];
    //            }

    //            CronExpression = "0 " + MonthlyMins + " " + MonthlyHours + " " + txtmonthday + " " + txtmonth + " ? " + txtyear
    //            listcron = MonthlyHours + " hours " + MonthlyMins + " minutes for " + txtmonthday + " day(s) on " + txtmonth + " in the year " + txtyear;

    //        }
    //        else if (schedule_model != null) {
    //            if (schedule_model.value == "everyday") {
    //                CronExpression = "0 " + everyMinutes + " " + everyHours + " * * ?"
    //                listcron = " Every day at " + everyHours + " hours " + everyMinutes + " minutes ";
    //            }
    //            else if (schedule_model.value == "MON-FRI") {
    //                CronExpression = "0 " + everyMinutes + " " + everyHours + " ? * " + weekDay + " * ";
    //                listcron = " MON-FRI at " + everyHours + " hours " + everyMinutes + " minutes ";
    //            }
    //        }
    //    }
    //    return { CronExpression, listcron };
    //}

    //function DateTimeCronBuilder(datetime) {

    //    var splitDate = datetime.split("T");
    //    var cronDate = splitDate[0].split("-");
    //    var cronTime = splitDate[1].split(":");

    //    var cronYear = cronDate[0];
    //    var cronMonth = cronDate[1]
    //    var cronDay = cronDate[2];
    //    var cronHours = cronTime[0];
    //    var cronMin = cronTime[1];
    //    CronExpression = "0 " + cronMin + " " + cronHours + " " + cronDay + " " + cronMonth + " ? " + cronYear;
    //    // monthname
    //    //var month = datetime.split("T");
    //    //var datemonth = month[0].split("-");
    //    //var cronmonth_listcron = datemonth[1]

    //    //var cronname = ["January", "February", "March", "April", "May", "June", "July",
    //    //    "August", "September", "October", "November", "December"];
    //    //var cronmonthname = cronname[cronmonth_listcron - 1] 
    //    listCron = "At " + cronHours + ":" + cronMin + ", on day  " + cronDay + " of the month, only in " + cronMonth + ", only in " + cronYear;

    //    //At 12: 51 PM, on day 14 of the month, only in February, only in 2024

    //    return { CronExpression, listCron }

    //}
    //function DateTimeCronConventor(cron) {

    //    var splitcron = cron.split(" ");
    //    var cronYear = splitcron[6];
    //    var cronMonth = splitcron[4];
    //    var cronDay = splitcron[3];
    //    var cronHours = splitcron[2];
    //    var cronMin = splitcron[1];
    //    var cronDate = cronYear + "-" + cronMonth + "-" + cronDay + "T" + cronHours + ":" + cronMin

    //    //At 06:05:00am, on the 8th day, in May, in 2023
    //    return cronDate

    //}

    function CronValidation() {

        var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
        var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
        var Monthlycheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
        var txtmonthday = Array.from(Monthlycheckboxes).map(checkbox => checkbox.value);
        var monthlymonth = $('#lblMonth').val();
        var Minutes = $('#txtMins').val();
        var txtHours = $('#txtHours').val();
        var txtHourMinutes = $('#txtMinutes').val();
        //var ddlHours = $('#ddlHours').val();
        var ddlMinutes = $('#ddlMinutes').val();
        var everyHours = $('#everyHours').val();
        var everyMinutes = $('#everyMinutes').val();
        var datetime = $('#datetimeCron').val();
        var MonthlyHours = $('#MonthlyHours').val();
        var MonthlyMins = $('#MonthlyMins').val()
        var isScheduler = '';



        if (document.getElementById('switchMonthly').checked == true) {

            $('#datetimeCron').val('');
            var Scheduler_types = $('.nav-tabs .active').text().trim();

            switch (Scheduler_types) {

                case "Minutes":
                    errorElement = $('#CronMin-error');
                    isScheduler = validateMinJobNumber(Minutes, "Enter minutes", errorElement);
                    break;

                case "Hourly":
                    errorElement = $('#CronHourly-error');
                    isScheduler = validateHourJobNumber(txtHours, "Enter hours", errorElement);
                    errorElement = $('#CronHourMin-error');
                    isScheduler = validateMiniteJobNumber(txtHourMinutes, "Enter minutes", errorElement);
                    break;

                case "Daily":
                    errorElement = $('#CroneveryHour-error');
                    isSchedulerHour = validateHourJobNumber(everyHours, "Select hours", errorElement);
                    //errorElement = $('#CroneveryMin-error');
                    //isSchedulerMin = validateMinJobNumber(everyMinutes, "Select the minutes", errorElement);&& isSchedulerMin 
                    errorElement = $('#Crondaysevery-error');
                    isSchedulerDay = ValidateCronRadioButton(errorElement);
                    if (isSchedulerHour && isSchedulerDay) {
                        isScheduler = true;
                    }
                    break;

                case "Weekly":
                    //errorElement = $('#CronddlMin-error');
                    // isSchedulerMin = validateMinJobNumber(ddlMinutes, "Select the minutes", errorElement);&& isSchedulerMin
                    errorElement = $('#CronddlHour-error');
                    isSchedulerHour = validateHourJobNumber($('#ddlHours').val(), "Select hours", errorElement);
                    isSchedulerDay = validateDayNumber(txtDay, "Select day", $('#CronDay-error'));
                    if (isSchedulerHour && isSchedulerDay) {
                        isScheduler = true;
                    }
                    break;

                case "Monthly":
                    errorElement = $('#MonthlyHours-error');
                    isSchedulerHour = validateHourJobNumber(MonthlyHours, "Select hours", errorElement);
                    //errorElement = $('#MonthlyMins-error');
                    //isSchedulerMin = validateMinJobNumber(MonthlyMins, "Select the minutes", errorElement);&& isSchedulerMin
                    errorElement = $('#CronMon-error');
                    let mt = $("#lblMonth").val()
                    if (mt) {
                        // If mt has a value, then trigger the validation function
                        isSchedulerDay = validateDayNumber(txtmonthday, "Select date(s)", errorElement);
                    } 
                  //  isSchedulerDay = validateDayNumber(txtmonthday, "Select date", errorElement);
                    errorElement = $('#CronMonthly-error');
                    isSchedulerMonth = validateDayNumber(monthlymonth, "Select month and year", errorElement);
                    if (isSchedulerHour && isSchedulerDay && isSchedulerMonth) {
                        isScheduler = true;
                    }
                    break;
            }
        }
        else {
            errorElement = $('#CronExpression-error');
            isScheduler = validateDayNumber(datetime, "Select schedule time", errorElement) && validateprevNumber(datetime, "", errorElement);
        }
        return isScheduler;
    }

    function ValidateCronRadioButton(errorElement) {

        if ($('input[name=daysevery]:checked').length > 0) {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
        else {
            errorElement.text("Select day type").addClass('field-validation-error');;
            return false;
        }
    }
    //function validateMiniteJobNumber(value, errorMsg, errorElement) {

    //    if (!value) {
    //        errorElement.text(errorMsg);
    //        errorElement.addClass('field-validation-error');
    //        return false;
    //    }
    //    //else if (value.length > 2) {
    //    //    errorElement.text("Value should be 2 digit");
    //    //    errorElement.addClass('field-validation-error');
    //    //    return false;
    //    //}
    //    else if ((Number(value) < 0) || (Number(value) >= 60)) {
    //        errorElement.text("Enter value between 0 to 59");
    //        errorElement.addClass('field-validation-error');
    //        return false;
    //    }
    //    else {
    //        errorElement.text('');
    //        errorElement.removeClass('field-validation-error');
    //        return true;
    //    }
    //}
    //function validateMinJobNumber(value, errorMsg, errorElement) {

    //    if (!value) {
    //        errorElement.text(errorMsg);
    //        errorElement.addClass('field-validation-error');
    //        return false;
    //    }
    //    //else if (Number(value).length > 2) {
    //    //    errorElement.text("Value should be 2 digits");
    //    //    errorElement.addClass('field-validation-error');
    //    //    return false;
    //    //}
    //    else if ((Number(value) < 0) || (Number(value) > 59)) {
    //        errorElement.text("Enter value between 1 to 59");
    //        errorElement.addClass('field-validation-error');
    //        return false;
    //    } else if (Number(value) == "0") {
    //        errorElement.text("Enter the value more than 0");
    //        errorElement.addClass('field-validation-error');
    //        return false;
    //    }
    //    else {
    //        errorElement.text('');
    //        errorElement.removeClass('field-validation-error');
    //        return true;
    //    }
    //}
    //function validateHourJobNumber(value, errorMsg, errorElement) {
    //    if (!value) {
    //        errorElement.text(errorMsg);
    //        errorElement.addClass('field-validation-error');
    //        return false;
    //    }
    //    else if ((Number(value) == 0)) {
    //        errorElement.text("Enter value greater than zero");
    //        errorElement.addClass('field-validation-error');
    //        return false;
    //    }
    //    else if ((Number(value) < 1) || (Number(value) >= 24)) {
    //        errorElement.text("Enter value between 1 to 23");
    //        errorElement.addClass('field-validation-error');
    //        return false;
    //    }
    //    else {
    //        errorElement.text('');
    //        errorElement.removeClass('field-validation-error');
    //        return true;
    //    }      
    //}
    //function validateprevNumber(value, errorMsg, errorElement) {
    //    let selectdate = new Date(value)

    //    let currentdate = new Date()

    //    if (selectdate > currentdate) {
    //        $('#CronExpression-error').text('');
    //        $('#CronExpression-error').removeClass('field-validation-error');
    //        return true;
    //    } else if (selectdate < currentdate) {
    //        $('#CronExpression-error').text("Select schedule date and time should be greater than current date and time");
    //        $('#CronExpression-error').addClass('field-validation-error');
    //        return false;
    //    }
    //}
    //function validateDayNumber(value, errorMsg, errorElement) {

    //    if (!value || value.length == 0) {
    //        errorElement.text(errorMsg);
    //        errorElement.addClass('field-validation-error');
    //        return false;
    //    } else {
    //        errorElement.text('');
    //        errorElement.removeClass('field-validation-error');
    //        return true;
    //    }
    //}

    const clearJobFields = () => {
        $('#NameId').val('');
        $('#jobName').val('');
        $('#airgapName').val('');
        $('#airgapId').val('');    
        $('#workflowName').val('');
        $('#workflowId').val('');  
        $('#textCronExpression').val('');       
        $('#textIsSchedule').val('');
        $('#textScheduleType').val('');
        $('#SaveFunction').text("Save");
        //$('#SaveFunction').attr('title', 'Save');
        ClearJobErrorElements(errorElements);
        ClearCroneElements();
    }
    function ClearJobErrorElements() {
        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });
    }
    let error = ["#CronMin-error", "#CronHourly-error", "#CronHourMin-error", "#Crondaysevery-error", "#CroneveryHour-error", "#CronDay-error", "#CronddlHour-error", "#CronMonthly-error", "#CronMon-error", "#MonthlyHours-error", "#CronExpression-error"]
    $(".nav-link,#nav-Minutes-tab,#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab").on("click", function () {
        error.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });

    })

    function GetIsSchedule() {
        var schedule_type = document.querySelector('input[name = "switchPlan"]:checked');
        if (schedule_type.value === "Once") {
            $('#textIsSchedule').val(1);
        } else {
            $('#textIsSchedule').val(2);
        }
    }

    //function Get_ScheduleTypes() {

    //    var Scheduler_types = $('.nav-tabs .active').text().trim();

    //    switch (Scheduler_types) {
    //        case "Minutes":
    //            $('#textScheduleType').val(1);
    //            break;
    //        case "Hourly":
    //            $('#textScheduleType').val(2);
    //            break;
    //        case "Daily":
    //            $('#textScheduleType').val(3);
    //            break;
    //        case "Weekly":
    //            $('#textScheduleType').val(4);
    //            break;
    //        case "Monthly":
    //            $('#textScheduleType').val(5);
    //            break;
    //    }
    //}

    //function Tab_selection(jobData) {

    //    if (jobData.isSchedule == 2) {
    //        Drready_SM1 = document.getElementById("switchMonthly");
    //        Drready_SM1.checked = true;
    //        var elementToHide1 = document.getElementById("monthgroup");
    //        elementToHide1.style.display = "Block";
    //        var elementToHide22 = document.getElementById("yeargroup");
    //        elementToHide22.style.display = "none";

    //    } else {
    //        Drready_SM2 = document.getElementById("switchYearly");
    //        Drready_SM2.checked = true;
    //        var elementToHide11 = document.getElementById("monthgroup");
    //        elementToHide11.style.display = "none";
    //        var elementToHide22 = document.getElementById("yeargroup");
    //        elementToHide22.style.display = "block";
    //    }
    //}

    $(".monitorbtn-cancel").on("click", function () {
        //debugger
        $("#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab").removeClass("active");
        $("#nav-Hourly,#nav-Daily,#nav-Weekly,#nav-Monthly").removeClass("show active");
        $("#nav-Minutes").addClass("show active");
        $("#nav-Minutes-tab").addClass("active");
    })

    //function Tab_schedule_type(jobData) {
    //    var types = jobData.scheduleType;
    //    var clickedLink = "";
    //    var linkId = "";
 
    //    if (jobData.isSchedule == 1) {
    //        var datetime = DateTimeCronConventor(jobData.cronExpression)
    //        $('#datetimeCron').val(datetime)
    //    }
    //    else {
    //        switch (types) {
    //            case 1:
    //                linkId = "nav-Minutes-tab";
    //                setTimeout(() => {
    //                    clickedLink = document.getElementById(linkId);
    //                    clickedLink.click();
    //                    const { minutes } = parseMinCronExpression(jobData.cronExpression);
    //                    document.getElementById("txtMins").value = minutes;
    //                }, 150)
    //                break;
    //            case 2:
    //                linkId = "nav-Hourly-tab";
    //                setTimeout(() => {
    //                    clickedLink = document.getElementById(linkId);
    //                    clickedLink.click();
    //                    const { hours, minutes } = parseHoursCronExpression(jobData.cronExpression);
    //                    document.getElementById("txtHours").value = hours;
    //                    document.getElementById("txtMinutes").value = minutes;
    //                }, 150)
    //                break;
    //            case 3:
    //                linkId = "nav-Daily-tab";
    //                setTimeout(() => {
    //                    clickedLink = document.getElementById(linkId);
    //                    clickedLink.click();
    //                    const { hours, day } = parseDailyCronExpression(jobData.cronExpression);
    //                    document.getElementById("everyHours").value = hours;                      
    //                    if (day == "?") {
    //                        $("#defaultCheck-everyday").prop("checked", true);
    //                    }
    //                    else {
    //                        $("#defaultCheck-MON-FRI").prop("checked", true);
    //                    }
    //                }, 150)
    //                break;
    //            case 4:
    //                linkId = "nav-Weekly-tab";
    //                setTimeout(() => {
    //                    clickedLink = document.getElementById(linkId);
    //                    clickedLink.click();
    //                    const { hours, day } = parseWeeklyCronExpression(jobData.cronExpression);
    //                    document.getElementById("ddlHours").value = hours;                     
    //                    dayconventor(day);
    //                }, 150)
    //                break;
    //            case 5:
    //                linkId = "nav-Monthly-tab";
    //                setTimeout(() => {
    //                    clickedLink = document.getElementById(linkId);
    //                    clickedLink.click();
    //                    const { hours, month, days } = parseCronMonthExpression(jobData.cronExpression);
    //                    document.getElementById("MonthlyHours").value = hours;                       
    //                    document.getElementById("lblMonth").value = month;
    //                    monthDayconventor(days);
    //                }, 150)
    //                break;
    //        }
    //    }
    //}

    //function monthDayconventor(days) {
    //    const day = days.split(" ")
    //    var checkboxes = document.querySelectorAll('input[name="Monthyday"]');
    //    checkboxes.forEach(function (checkbox) {
    //        if (day.includes(checkbox.value)) {
    //            checkbox.checked = true;
    //        }
    //    });
    //};

    //function parseCronMonthExpression(expression) {
    //    const parts = expression.split(' ');
    //    const minutes = parseInt(parts[1]);
    //    const hours = parseInt(parts[2].substring(2));
    //    const month = parts[6] + "-" + parts[4];
    //    const days = parts[3];
    //    return { minutes, hours, month, days };
    //};

    //function parseMinCronExpression(expression) {
    //    const parts = expression.split(' ');
    //    const minutes = parseInt(parts[1].substring(2));
    //    const hours = parseInt(parts[2].substring(2));
    //    const day = parseInt(parts[3].substring(2));
    //    return { hours, minutes, day };
    //}
    //function parseHoursCronExpression(expression) {
    //    const parts = expression.split(' ')
    //    const minutes = parseInt(parts[1]);
    //    const hours = parseInt(parts[2].substring(2));
    //    const day = parts[5];
    //    return { hours, minutes, day };
    //}
    //function parseDailyCronExpression(expression) {
    //    const parts = expression.split(' ')
    //    const minutes = parseInt(parts[1]);
    //    const hours = parseInt(parts[2].substring(2));
    //    const day = parts[5];
    //    return { hours, minutes, day };
    //}
    //function parseWeeklyCronExpression(expression) {
    //    const parts = expression.split(' ')
    //    const minutes = parseInt(parts[1]);
    //    const hours = parseInt(parts[2].substring(2));
    //    const day = parts[5];
    //    return { hours, minutes, day };
    //}
    //function dayconventor(day) {
    //    const daysMap = {
    //        MON: 1,
    //        TUE: 2,
    //        WED: 3,
    //        THU: 4,
    //        FRI: 5,
    //        SAT: 6,
    //        SUN: 0
    //    };
    //    const days = day.split(',');
    //    days.forEach(day => {
    //        const checkboxId = `#defaultCheck-${daysMap[day]}`;
    //        $(checkboxId).prop("checked", true);
    //    });
    //}
    $(".monitorbtn-cancel").on("click", function () {
        $('#CronExpression-error').text('');
        $('#CronExpression-error').removeClass('field-validation-error');
        $("#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab").removeClass("active");
        $("#nav-Hourly,#nav-Daily,#nav-Weekly,#nav-Monthly").removeClass("show active");
        $("#nav-Minutes").addClass("show active");
        $("#nav-Minutes-tab").addClass("active");
    })
    function jobOnce() {
        Drready_SM2 = document.getElementById("switchMonthly");
        Drready_SM2.checked = true;
        var elementToHide11 = document.getElementById("monthgroup");
        elementToHide11.style.display = "block";
        var elementToHide22 = document.getElementById("yeargroup");
        elementToHide22.style.display = "none";
    }

    //var monthInput = document.getElementById("lblMonth");
    //var today = new Date();
    //var currentYear = today.getFullYear();
    //var currentMonth = today.getMonth() + 1;
    //var minMonth = currentYear + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
    //var maxMonth = (currentYear + 77) + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
    //monthInput.setAttribute("min", minMonth);
    //monthInput.setAttribute("max", maxMonth);


    //const now = new Date();
    //const year = now.getFullYear();
    //const month = String(now.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    //const day = String(now.getDate()).padStart(2, '0');
    //const hours = String(now.getHours()).padStart(2, '0');
    //const minutes = String(now.getMinutes()).padStart(2, '0');
    //const minformattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;
    //const maxformattedDate = `${year + 77}-${month}-${day}T${hours}:${minutes}`;

    //const datetimeInput = document.getElementById('datetimeCron');
    //datetimeInput.min = minformattedDate;
    //datetimeInput.max = maxformattedDate;

    const exceptThisSymbols = ["e", "E", "+", "-", "."];
    function srvTime() {
        try {
            //FF, Opera, Safari, Chrome
            xmlHttp = new XMLHttpRequest();
        }
        catch (err1) {
            //IE
            try {
                xmlHttp = new ActiveXObject('Msxml2.XMLHTTP');
            }
            catch (err2) {
                try {
                    xmlHttp = new ActiveXObject('Microsoft.XMLHTTP');
                }
                catch (eerr3) {
                    //AJAX not supported, use CPU time.
                    alert("AJAX not supported");
                }
            }
        }
        xmlHttp.open('HEAD', window.location.href.toString(), false);
        xmlHttp.setRequestHeader("Content-Type", "text/html");
        xmlHttp.send('');
        return xmlHttp.getResponseHeader("Date");
    }
    function ValidateRadioButton(errorElement) {

        if ($('input[name=state]:checked').length > 0) {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
        else {
            errorElement.text("Select state").addClass('field-validation-error');;
            return false;
        }
    }
    $("#SaveFunction").on('click', async function () {
        GetIsSchedule();
        Get_ScheduleTypes();
        let form = $("#CreateForm")
        let name = $("#jobName").val();
        let airgap = $("#airgapName").val();
        let workflow = $("#workflowName").val();
        let jobId = $('#NameId').val();
        let IsName = await validateNames(name, jobId, JobManagementExistUrl);
        let Isairgap = validateDropDown(airgap, 'Enter airgap name', 'AirgapName-error');
        let Isworkflow = validateDropDown(workflow, 'Select workflow name', 'WorkflowName-error');
        let SiteSanitizeArray = ['jobName', 'airgapName', 'workflowName', 'solutionName', 'ddlHours']

        errorElement = $('#state-error');
        var isStateActive = ValidateRadioButton(errorElement);
        var isScheduler = CronValidation();
        var { CronExpression, listcron } = JobCronExpression();
        document.getElementById("textCronExpression").value = CronExpression;
        document.getElementById("textScheduleTime").value = listcron;
        
        sanitizeContainer(SiteSanitizeArray)
        setTimeout(() => {
            if (IsName && Isairgap && Isworkflow && isScheduler && isStateActive) {
                form.trigger('submit');
            }
        }, 200)
       
    });


    async function validateNames(value, id = null) {
        
        const errorElement = $('#Name-error');
        if (!value) {
            errorElement.text('Enter job name ')
                .addClass('field-validation-error');
            return false;
        }
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }
        var url = RootUrl + JobManagementExistUrl;
        var data = {};
        data.name = value;
        data.id = id;

        const validationResults = [
            await SpecialCharValidate(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithSpace(value),
            await SpaceWithUnderScore(value),
            await ShouldNotBeginWithNumber(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsGroupNameExist(url, data, OnError)
        ];

        return await CommonValidation(errorElement, validationResults);
    }

    async function IsGroupNameExist(url, data, errorFunc) {

        return !data.name.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
    }


   
    function validateDropDown(value, errorMessage, errorElement) {

        if (!value) {
            $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            $('#' + errorElement).text('').removeClass('field-validation-error');
            return true;
        }
    }

    const errorElements = ['#Name-error', '#CronDay-error', '#CronddlHour-error','#state-error', '#AirgapName-error', '#WorkflowName-error'];
    $('#create').on('click', function () {
        clearInputFields('CreateForm', errorElements);
        clearJobFields();
        jobOnce();
        $('#SaveFunction').text('Save');
        $("#textStateActive").prop("checked", true);
    });
    
    function populateModalFields(jobData) {       
        let CyberjobData = atob(jobData);
        let CyberData = JSON.parse(CyberjobData)    
        $('#textCronExpression').val(CyberData.cronExpression);
        let scheduleTime = CyberData.scheduleTime.split(" "); 
        $("#ddlHours").val(scheduleTime[0]);       
        $('#NameId').val(CyberData.id);
        $('#jobName').val(CyberData.name);
        $('#airgapName').val(CyberData.airgapName);
        $('#airgapId').val(CyberData.airgapId);       
        $('#workflowName').val(CyberData.workflowName);
        $('#workflowId').val(CyberData.workflowId);
      //  $('input[name="mode"][value="' + CyberjonData.mode + '"]').prop('checked', true);        
        if (CyberData.state == "Active") {
            $("#textStateActive").prop("checked", true);
        }
        else {
            $("#textStateInactive").prop("checked", true);
        }
        setTimeout(() => {

            if (CyberData.scheduleTime.includes("Every day") == true) {
                $("#defaultCheck-everyday").prop("checked", true)
                $("#everyHours").val(scheduleTime[4] + ":" + scheduleTime[6]).trigger("change")
            }
            if (CyberData.scheduleTime.includes("MON-FRI") == true) {
                $("#defaultCheck-MON-FRI").prop("checked", true)
                $("#everyHours").val(scheduleTime[3] + ":" + scheduleTime[5]).trigger("change")
            }

            if (scheduleTime.length == 7) {
                $("#txtMinutes").val(scheduleTime[5])
                $("#txtHours").val(scheduleTime[1])
            }

            if ($("#defaultCheck-MON-FRI").prop("checked") != true) {
                if (CyberData.scheduleTime.includes("MON") == true) {
                    $("#defaultCheck-1").prop("checked", true)
                }
                if (CyberData.scheduleTime.includes("TUE") == true) {
                    $("#defaultCheck-2").prop("checked", true)
                }
                if (CyberData.scheduleTime.includes("WED") == true) {
                    $("#defaultCheck-3").prop("checked", true)
                }
                if (CyberData.scheduleTime.includes("THU") == true) {
                    $("#defaultCheck-4").prop("checked", true)
                }
                if (CyberData.scheduleTime.includes("FRI") == true) {
                    $("#defaultCheck-5").prop("checked", true)
                }
                if (CyberData.scheduleTime.includes("SAT") == true) {
                    $("#defaultCheck-6").prop("checked", true)
                }
                if (CyberData.scheduleTime.includes("SUN") == true) {
                    $("#defaultCheck-0").prop("checked", true)
                }
                $("#ddlHours").val(scheduleTime[2] + ":" + scheduleTime[4]).trigger("change")
            }


            if (scheduleTime.length >= 12) {
                //bugger
                var year = parseInt(scheduleTime[12])
                var month = parseInt(scheduleTime[8] == "JAN" ? "01" : scheduleTime[8] == "FEB" ? "02" : scheduleTime[8] == "MAR" ? "03" : scheduleTime[8] == "APR" ? "04" :
                    scheduleTime[8] == "MAY" ? "05" : scheduleTime[8] == "JUN" ? "06" : scheduleTime[8] == "JUL" ? "07" : scheduleTime[8] == "AUG" ? "08" : scheduleTime[8] == "SEP" ? "09" :
                        scheduleTime[8] == "OCT" ? "10" : scheduleTime[8] == "NOV" ? "11" : scheduleTime[8] == "DEC" ? "12" : "")
                if (month <= 9 && month > 0) {
                    month = "0" + month;
                }
                else if (month == 0) {
                    month = "12";
                    year = year - 1;
                }
                var newdate = year + "-" + month;

                $("#lblMonth").val(newdate).trigger('change')
                scheduleTime[5]?.split(",").forEach(function (i) {
                    if (i) {
                        $("#inlineCheckbox" + i).prop("checked", true)
                    } else {
                        $("#inlineCheckbox" + i).prop("checked", false)
                    }
                })
                $("#MonthlyHours").val(scheduleTime[0] + ":" + scheduleTime[2]).trigger("change")
            }
        }, 500)


        const errorElements = ['#Name-error', '#CronDay-error', '#CronddlHour-error','#state-error', '#AirgapName-error', '#WorkflowName-error'];
        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error')
        });
        }
    
});

