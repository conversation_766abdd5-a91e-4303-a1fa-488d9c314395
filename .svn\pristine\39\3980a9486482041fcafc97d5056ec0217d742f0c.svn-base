using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DriftManagementMonitorStatusFixture : IDisposable
{
    public List<DriftManagementMonitorStatus> DriftManagementMonitorStatusPaginationList { get; set; }
    public List<DriftManagementMonitorStatus> DriftManagementMonitorStatusList { get; set; }
    public DriftManagementMonitorStatus DriftManagementMonitorStatusDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string InfraObjectId = "INFRA_123";
    public const string InfraObjectName = "TestInfraObject";
    public const string Type = "TestType";
    public const string Status = "Active";

    public ApplicationDbContext DbContext { get; private set; }

    public DriftManagementMonitorStatusFixture()
    {
        var fixture = new Fixture();

        DriftManagementMonitorStatusList = fixture.Create<List<DriftManagementMonitorStatus>>();

        DriftManagementMonitorStatusPaginationList = fixture.CreateMany<DriftManagementMonitorStatus>(20).ToList();

        DriftManagementMonitorStatusPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftManagementMonitorStatusPaginationList.ForEach(x => x.IsActive = true);
      
        DriftManagementMonitorStatusList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftManagementMonitorStatusList.ForEach(x => x.IsActive = true);
      
        DriftManagementMonitorStatusDto = fixture.Create<DriftManagementMonitorStatus>();
        DriftManagementMonitorStatusDto.ReferenceId = Guid.NewGuid().ToString();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
