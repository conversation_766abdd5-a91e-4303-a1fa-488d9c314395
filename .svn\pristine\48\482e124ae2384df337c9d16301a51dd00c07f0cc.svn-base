﻿using ContinuityPatrol.Domain.ViewModels.MYSQLMonitorStatusModel;

namespace ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetList;

public class
    GetMYSQLMonitorStatusListQueryHandler : IRequestHandler<GetMYSQLMonitorStatusListQuery,
        List<MYSQLMonitorStatusListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMysqlMonitorStatusRepository _mysqlMonitorStatusRepository;

    public GetMYSQLMonitorStatusListQueryHandler(IMysqlMonitorStatusRepository mysqlMonitorStatusRepository,
        IMapper mapper)
    {
        _mysqlMonitorStatusRepository = mysqlMonitorStatusRepository;
        _mapper = mapper;
    }

    public async Task<List<MYSQLMonitorStatusListVm>> Handle(GetMYSQLMonitorStatusListQuery request,
        CancellationToken cancellationToken)
    {
        var mysqlMonitorStatus = await _mysqlMonitorStatusRepository.ListAllAsync();

        return mysqlMonitorStatus.Count <= 0
            ? new List<MYSQLMonitorStatusListVm>()
            : _mapper.Map<List<MYSQLMonitorStatusListVm>>(mysqlMonitorStatus);
    }
}