﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IDashboardViewRepository : IRepository<DashboardView>
{
    Task<List<DashboardView>> GetInfraObjectSummaryReport();
    Task<List<DashboardView>> GetBusinessViewByLast7Days();
    Task<List<DashboardView>> GetBusinessViewListByBusinessFunctionId(string businessFunctionId);
    Task<List<DashboardView>> GetBusinessViewListByBusinessServiceId(string businessServiceId);
    Task<List<DashboardView>> GetBusinessViewListByBusinessServiceIds(List<string> businessServiceId);
    Task<DashboardView> GetBusinessViewByInfraObjectId(string infraObjectId);
    Task<DashboardView> GetSitePropertiesByBusinessServiceId(string businessServiceId);
    Task<List<DashboardView>> GetDashboardNames();
    Task<DashboardView> GetByEntityIdAndType(string entityId, string type);
    Task<List<DashboardView>> GetBusinessViewListByBusinessServiceIdDatalag(string businessServiceId);
    
}