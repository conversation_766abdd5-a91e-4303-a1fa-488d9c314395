using ContinuityPatrol.Application.Features.VeritasCluster.Events.Create;

namespace ContinuityPatrol.Application.Features.VeritasCluster.Commands.Create;

public class
    CreateVeritasClusterCommandHandler : IRequestHandler<CreateVeritasClusterCommand, CreateVeritasClusterResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IVeritasClusterRepository _veritasClusterRepository;

    public CreateVeritasClusterCommandHandler(IMapper mapper, IVeritasClusterRepository veritasClusterRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _veritasClusterRepository = veritasClusterRepository;
    }

    public async Task<CreateVeritasClusterResponse> Handle(CreateVeritasClusterCommand request,
        CancellationToken cancellationToken)
    {
        var veritasCluster = _mapper.Map<Domain.Entities.VeritasCluster>(request);

        veritasCluster = await _veritasClusterRepository.AddAsync(veritasCluster);

        var response = new CreateVeritasClusterResponse
        {
            Message = Message.Create("Veritas Cluster", veritasCluster.ClusterName),

            Id = veritasCluster.ReferenceId
        };

        await _publisher.Publish(new VeritasClusterCreatedEvent { Name = veritasCluster.ClusterName },
            cancellationToken);

        return response;
    }
}