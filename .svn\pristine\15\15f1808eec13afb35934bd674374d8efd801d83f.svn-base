﻿using ContinuityPatrol.Application.Features.Server.Events.SaveAs;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.Server.Events
{
    public class ServerSaveAsEventTests
    {
        private readonly Mock<ILogger<ServerSaveAsEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly ServerSaveAsEventHandler _handler;

        public ServerSaveAsEventTests()
        {
            _mockLogger = new Mock<ILogger<ServerSaveAsEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _handler = new ServerSaveAsEventHandler(
                _mockLogger.Object,
                _mockUserActivityRepository.Object,
                _mockUserService.Object);
        }

        [Fact]
        public async Task Handle_ValidServerSaveAsEvent_ShouldLogAndSaveUserActivity()
        {
            var notification = new ServerSaveAsEvent { ServerName = "TestServer" };
            _mockUserService.Setup(s => s.UserId).Returns("test-user-id");
            _mockUserService.Setup(s => s.LoginName).Returns("test-login-name");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://test-url.com");
            _mockUserService.Setup(s => s.CompanyId).Returns("test-company-id");
            _mockUserService.Setup(s => s.IpAddress).Returns("127.0.0.1");

            await _handler.Handle(notification, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "test-user-id" &&
                activity.LoginName == "test-login-name" &&
                activity.RequestUrl == "http://test-url.com" &&
                activity.CompanyId == "test-company-id" &&
                activity.HostAddress == "127.0.0.1" &&
                activity.Entity == Modules.Server.ToString() &&
                activity.Action == $"{ActivityType.SaveAs} {Modules.Server}" &&
                activity.ActivityType == ActivityType.SaveAs.ToString() &&
                activity.ActivityDetails == "Server 'TestServer' save-as successfully."
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation("Server 'TestServer' save-as successfully."), Times.Once);
        }
    }
}
